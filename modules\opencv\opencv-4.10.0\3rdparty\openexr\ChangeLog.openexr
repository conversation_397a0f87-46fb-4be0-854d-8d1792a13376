Version 2.0.1
	* Temporarily turning off optimisation code path
	  (<PERSON><PERSON><PERSON>)
	* Added additional tests for future optimisation refactoring
	  (<PERSON><PERSON><PERSON> / <PERSON>)
	* Fixes for StringVectors
	  (<PERSON>)
	* Additional checks for type mismatches
	  (<PERSON>)
	* Fix for Composite Deep Scanline
	  (<PERSON>)

Version 2.0.0
	* Updated Documentation
	   (<PERSON>)
	* Updated Namespacing mechanism
	   (<PERSON><PERSON><PERSON>)
	* Fixes for succd & predd
	   (<PERSON>)
	* Fixes for FPE control registers
	   (<PERSON><PERSON><PERSON>)
	* Additional checks and tests on DeepImages, scanlines and tiles
	   (<PERSON>)
	* Folded in Autodesk read optimisations for RGB(A) files
	  (<PERSON>, <PERSON>)
	* Updated the bootstrap scripts to use libtoolize if glibtoolize isn't available on darwin. 
	  (<PERSON>)
	* Numerous minor fixes, missing includes etc

Version 2.0.0.beta.1:
* Please read the separate file for v2 additions and changes.
	* Added git specific files 
	  (<PERSON><PERSON><PERSON>)
	* Updated the so verison to 20
	  (<PERSON><PERSON><PERSON>)
	* Initial use of the CMake build system 
	  (<PERSON>)

Version 1.7.1:
	* Updated the .so verison to 7.
	  (<PERSON><PERSON><PERSON>)     

Version 1.7.0:
	* Added support for targetting builds on 64bit Windows and minimising 
	  number of compiler warnings on Windows. Thanks to <PERSON><PERSON>bbelt for his
	  contributions to CreateDLL.
	  (Ji Hun Yu)
	* Added new atttribute types:
	  M33dAttribute   3x3 double-precision matrix
	  M44dAttribute   4x4 double-precision matrix
	  V2d             2D double-precision vector
	  V3d             3D double-precision vector
	  (Florian Kainz)
	* Bug fix: crash when reading a damaged image file (found
	  by Apple).  An exception thrown inside the PIZ Huffman
	  decoder bypasses initialization of an array of pointers.
	  The uninitialized pointers are later passed to operator
	  delete.
	  (Florian Kainz)
	* Bug fix: crash when reading a damaged image file (found by
	  Apple).  Computing the size of input certain buffers may
	  overflow and wrap around to a small number, later causing
	  writes beyond the end of the buffer.
	  (Florian Kainz)
	* In the "Technical Introduction" document, added
	  Premultiplied vs. Un-Premulitiplied Color section:
	  states explicitly that pixels with zero alpha and non-zero
	  RGB are allowed, points out that preserving such a pixel can
	  be a problem in application programs with un-premultiplied
	  internal image representations.
	  (Florian Kainz)
	* exrenvmap improvements:
	  - New command line flags set the type of the input image to
	    latitude-longitude map or cube-face map, overriding the
	    envmap attribute in the input file header.
	  - Cube-face maps can now be assembled from or split into six
	    square sub-images.
	  - Converting a cube-face map into a new cube-face map with
	    the same face size copies the image instead of resampling
	    it.  This avoids blurring when a cube-face map is assembled
	    from or split into sub-images.
	  (Florian Kainz)
	* Updated standard chromaticities in ImfAcesFile.cpp to match
	  final ACES (Academy Color Encoding Specification) document.
	  (Florian Kainz)
	* Added worldToCamera and worldToNDC matrices to
	  ImfStandardAttributes.h (Florian Kainz)
	* Increased the maximum length of attribute and channel names
	  from 31 to 255 characters.  For files that do contain names
	  longer than 31 characters, a new LONG_NAMES_FLAG in the fil
	  version number is set.  This flag causes older versions of
	  the IlmImf library (1.6.1 and earlier) to reject files with
	  long names.  Without the flag, older library versions would
	  mis-interpret files with long names as broken.
	  (Florian Kainz)
	* Reading luminance/chroma-encoded files via the RGBA
	  interface is faster: buffer padding avoids cache thrashing
	  for certain image sizes, redundant calls to saturation()
	  have been eliminated.
	  (Mike Wall)
	* Added "hemispherical blur" option to exrenvmap.
	  (Florian Kainz)
	* Added experimental version of I/O classes for ACES file
	  format (restricted OpenEXR format with special primaries
	  and white point); added exr2aces file converter.
	  (Florian Kainz)
	* Added new constructors to classes Imf::RgbaInputFile and
	  Imf::TiledRgbaInputFile.  The new constructors have a
	  layerName parameter, which allows the caller to specify
	  which layer of a multi-layer or multi-view image will
	  be read.
	  (Florian Kainz)
	* A number of member functions in classes Imf::Header,
	  Imf::ChannelList and Imf::FrameBuffer have parameters
	  of type "const char *".  Added equivalent functions that
	  take "const std::string &" parameters.
	  (Florian Kainz)
	* Added library support for Weta Digital multi-view images:
	  StringVector attribute type, multiView standard attribute
	  of type StringVector, utility functions related to grouping
	  channels into separate views.
	  (Peter Hillman, Florian Kainz)

Version 1.6.1:
	* Removed Windows .suo files from distribution.
	  (Eric Wimmer)
	* Bug fix: crashes, memory leaks and file descriptor leaks
	  when reading damaged image files (some reported by Apple,
	  others found by running IlmImfFuzzTest).
	  (Florian Kainz)
	* Added new IlmImfFuzzTest program to test how resilient the
	  IlmImf library is with respect broken input files: the program
	  first damages OpenEXR files by partially overwriting them with
	  random data; then it tries to read the damaged files.  If all
	  goes well, the program doesn't crash.
	  (Florian Kainz)

Version 1.6.0:
	* Bumped DSO version number to 6.0
	  (Florian Kainz)
	* Added new standard attributes related to color rendering with
	  CTL (Color Transformation Language): renderingTransform,
	  lookModTransform and adoptedNeutral.
	  (Florian Kainz)
	* Bug fix: for pixels with luminance near HALF_MIN, conversion
	  from RGB to luminance/chroma produces NaNs and infinities
	  (Florian Kainz)
	* Bug fix: excessive desaturation of small details with certain
	  colors after repeatedly loading and saving luminance/chroma
	  encoded images with B44 compression.
	  (Florian Kainz)
	* Added B44A compression, a minor variation of B44: in most cases,
	  the compression ratio is 2.28:1, the same as with B44, but in
	  uniform image areas where all pixels have the same value, the
	  compression ratio increases to 10.66:1.  Uniform areas occur, for
	  example, in an image's alpha channel, which typically contains
	  large patches that are solid black or white, or in computer-
	  generated images with a black background.
	  (Florian Kainz)
	* Added flag to configure.ac to enable or disable use of large
	  auto arrays in the IlmImf library.  Default is "enable" for
	  Linux, "disable" for everything else.
	  (Darby Johnston, Florian Kainz)
	* corrected version number on dso's (libtool) - now 5.0
	* Separated ILMBASE_LDFLAGS and ILMBASE_LIBS so that test programs
	  can link with static libraries properly
	* eliminated some warning messages during install
	  (Andrew Kunz)
	
Version 1.5.0:
	* reorganized packaging of OpenEXR libraries to facilitate
	  integration with CTL.  Now this library depends on the library
	  IlmBase.  Some functionality has been moved into OpenEXR_Viewers,
	  which depends on two other libraries, CTL and OpenEXR_CTL.
	  Note: previously there were separate releases of
	  OpenEXR-related plugins for Renderman, Shake and Photoshop.
	  OpenEXR is supported natively by Rendermand and Photoshop, so
	  these plugins will not be supported for this or future
	  versions of OpenEXR.
	  (Andrew Kunz)
	* New build scripts for Linux/Unix
	  (Andrew Kunz)
	* New Windows project files and build scripts
	  (Kimball Thurston)
	* float-to-half conversion now preserves the sign of float zeroes
	  and of floats that are so small that they become half zeroes.
	  (Florian Kainz)
	* Bug fix: Imath::Frustum<T>::planes() returns incorrect planes
	  if the frustum is orthogonal.
	  (Philip Hubbard)
	* added new framesPerSecond optional standard attribute
	  (Florian Kainz)
	* Imath cleanup:
	  - Rewrote function Imath::Quat<T>::setRotation() to make it
	    numerically more accurate, added confidence tests
	  - Rewrote function Imath::Quat<T>::slerp() using Don Hatch's
	    method, which is numerically more accurate, added confidence
	    tests.
	  - Rewrote functions Imath::closestPoints(), Imath::intersect(),
	    added confidence tests.
	  - Removed broken function Imath::nearestPointOnTriangle().
	  - Rewrote Imath::drand48(), Imath::lrand48(), etc. to make
	    them functionally identical with the Unix/Linux versions
	    of drand48(), lrand48() and friends.
	  - Replaced redundant definitions of Int64 in Imath and IlmImf
	    with a single definition in ImathInt64.h.
	  (Florian Kainz)
	* exrdisplay: if the file's and the display's RGB chromaticities
	  differ, the pixels RGB values are transformed from the file's
	  to the display's RGB space.
	  (Florian Kainz)
	* Added new lossy B44 compression method.  HALF channels are
	  compressed with a fixed ratio of 2.28:1.  UINT and FLOAT
	  channels are stored verbatim, without compression.
	  (Florian Kainz)

Version 1.4.0a:
	* Fixed the ReleaseDLL targets for Visual Studio 2003.
	  (Barnaby Robson)
	
Version 1.4.0:	
	* Production release.
	* Bug Fix: calling setFrameBuffer() for every scan line
	  while reading a tiled file through the scan line API
	  returns bad pixel data. (Paul Schneider, Florian Kainz)

Version 1.3.1:
	* Fixed the ReleaseDLL targets for Visual Studio 2005.
	  (Nick Porcino, Drew Hess)
	* Fixes/enhancements for createDLL.
	  (Nick Porcino)
	
Version 1.3.0:
	* Removed openexr.spec file, it's out of date and broken to
	  boot.
	  (Drew Hess)
	* Support for Visual Studio 2005.
	  (Drew Hess, Nick Porcino)
	* When compiling against OpenEXR headers on Windows, you
	  no longer need to define any HAVE_* or PLATFORM_* 
	  macros in your projects.  If you are using any OpenEXR
	  DLLs, however, you must define OPENEXR_DLL in your
	  project's preprocessor directives.
	  (Drew Hess)
	* Many fixes to the Windows VC7 build system.
	  (Drew Hess, Nick Porcino)
	* Support for building universal binaries on OS X 10.4.
	  (Drew Hess, Paul Schneider)
	* Minor configure.ac fix to accomodate OS X's automake.
	  (Drew Hess)
	* Removed CPU-specific optimizations from configure.ac,
	  autoconf's guess at the CPU type isn't very useful,
	  anyway.  Closes #13429.
	  (Drew Hess)
	* Fixed quoting for tests in configure.ac.  Closes #13428.
	  (Drew Hess)
	* Use host specification instead of target in configure.ac.
	  Closes #13427.
	  (Drew Hess)
	* Fix use of AC_ARG_ENABLE in configure.ac.  Closes
	  #13426.
	  (Drew Hess)
	* Removed workaround for OS X istream::read bug.
	  (Drew Hess)
	* Added pthread support to OpenEXR pkg-config file.
	  (Drew Hess)
	* Added -no-undefined to LDFLAGS and required libs to LIBADD
	  for library projects with other library dependencies, per
	  Rex Dieter's patch.
	  (Drew Hess)
	* HAVE_* macros are now defined in the OpenEXRConfig.h header
	  file instead of via compiler flags.  There are a handful of
	  public headers which rely on the value of these macros,
	  and projects including these headers have previously needed
	  to define the same macros and values as used by OpenEXR's
	  'configure', which is bad form.  Now 'configure' writes these
	  values to the OpenEXRConfig.h header file, which is included
	  by any OpenEXR source files that need these macros.  This
	  method of specifying HAVE_* macros guarantees that projects
	  will get the proper settings without needing to add compile-
	  time flags to accomodate OpenEXR.  Note that this isn't
	  implemented properly for Windows yet.
	  (Drew Hess)
	* Platform cleanups:
	  - No more support for IRIX or OSF1.
	  - No more explicit support for SunOS, because we have no way to
	    verify that it's working.  I suspect that newish versions of 
	    SunOS will just work out of the box, but let me know if not.
	  - No more PLATFORM_* macros (vestiges of the ILM internal build 
	    system).  PLATFORM_DARWIN_PPC is replaced by HAVE_DARWIN.
	    PLATFORM_REDHAT_IA32 (which was only used in IlmImfTest) is
	    replaced by HAVE_LINUX_PROCFS.
	  - OS X 10.4, which is the minimum version we're going to support
	    with this version, appears to have support for nrand48 and friends,
	    so no need to use the Imath-supplied version of them anymore.
	  (Drew Hess)
	* No more PLATFORM_WINDOWS or PLATFORM_WIN32, replace with 
	  proper standard Windows macros.  (Drew Hess)
	* Remove support for gcc 2.95, no longer supported.  (Drew Hess)
	* Eliminate HAVE_IOS_BASE macro, OpenEXR now requires support for
	  ios_base.  (Drew Hess)
	* Eliminate HAVE_STL_LIMITS macro, OpenEXR now requires the ISO C++
	  <limits> header.  (Drew Hess)
	* Use double quote-style include dirctives for OpenEXR
	  includes.  (Drew Hess)
	* Added a document that gives an overview of the on-disk
	  layout of OpenEXR files (Florian Kainz)
	* Added sections on layers and on memory-mapped file input
	  to the documentation.  (Florian Kainz)
	* Bug fix: reading an incomplete file causes a deadlock while
	  waiting on a semaphore.  (Florian Kainz)
	* Updated documentation (ReadingAndWritingImageFiles.sxw) and
	  sample code (IlmImfExamples):
	  Added a section about multi-threading, updated section on
	  thread-safety, changed documentation and sample code to use
	  readTiles()/writeTiles() instead of readTile()/writeTile()
	  where possible, mentioned that environment maps contain
	  redundant pixels, updated section on testing if a file is
	  an OpenEXR file.
	  (Florian Kainz)
	* Multi-threading bug fixes (exceptions could be thrown
	  multiple times, some operations were not thread safe),
	  updated some comments, added comments, more multithreaded
	  testing.
	  (Florian Kainz)
	* Added multi-threading support: multiple threads
	  cooperate to read or write a single OpenEXR file.
	  (Wojciech Jarosz)
	* Added operator== and operator!= to Imath::Frustum.
	  (Andre Mazzone)
	* Bug fix: Reading a PIZ-compressed file with an invalid
	  Huffman code table caused crashes by indexing off the
	  end of an array.
	  (Florian Kainz)

Version 1.2.2:
	* Updated README to remove option for building with Visual C++ 6.0.
	  (Drew Hess)
	* Some older versions of gcc don't support a full iomanip
	  implemenation; check for this during configuration. 
	  (Drew Hess)
	* Install PDF versions of documentation, remove old/out-of-date
	  HTML documentation.  (Florian Kainz)
	* Removed vc/vc6 directory; Visual C++ 6.0 is no longer
	  supported.  (Drew Hess)
	* Updated README.win32 with details of new build system.
	  (Florian Kainz, Drew Hess)
	* New build system for Windows / Visual C++ 7 builds both
	  static libraries and DLLs.
	  (Nick Porcino)
	* Removed Imath::TMatrix<T> and related classes, which are not
	  used anywhere in OpenEXR.
	  (Florian Kainz)
	* Added minimal support for "image layers" to class Imf::ChannelList
	  (Florian Kainz)
	* Added new isComplete() method to InputFile, TiledInputFile
	  etc., that checks if a file is complete or if any pixels
	  are missing (for example, because writing the file was
	  aborted prematurely).
	  (Florian Kainz)
	* Exposed staticInitialize() function in ImfHeader.h in order
	  to allow thread-safe library initialization in multithreaded
	  programs.
	  (Florian Kainz)
	* Added a new "time code" attribute
	  (Florian Kainz)
	* exrmaketiled: when a MIPMAP_LEVELS or RIPMAP_LEVELS image
	  is produced, low-pass filtering takes samples outside the
	  image's data window.  This requires extrapolating the image.
	  The user can now specify how the image is extrapolated
	  horizontally and vertically (image is surrounded by black /
	  outermost row of pixels repeats / entire image repeats /
	  entire image repeats, every other copy is a mirror image).
	  exrdisplay: added option to swap the top and botton half,
	  and the left and right half of an image, so that the image's
	  four corners end up in the center.  This is useful for checking
	  the seams of wrap-around texture map images.
	  IlmImf library: Added new "wrapmodes" standard attribute
	  to indicate the extrapolation mode for MIPMAP_LEVELS and
	  RIPMAP_LEVELS images.
	  (Florian Kainz)
	* Added a new "key code" attribute to identify motion picture
	  film frames.
	  (Florian Kainz)
	* Removed #include <Iex.h> from ImfAttribute.h, ImfHeader.h
	  and ImfXdr.h so that including header files such as
	  ImfInputFile.h no longer defines ASSERT and THROW macros,
	  which may conflict with similar macros defined by
	  application programs.
	  (Florian Kainz)
	* Converted HTML documentation to OpenOffice format to
	  make maintaining the documents easier:
	      api.html -> ReadingAndWritingImageFiles.sxw
	      details.html -> TechnicalIntroduction.sxw
	  (Florian Kainz)

Version 1.2.1:
	* exrenvmap and exrmaketiled use slightly less memory
	  (Florian Kainz)
	* Added functions to IlmImf for quickly testing if a file
	  is an OpenEXR file, and whether the file is scan-line
	  based or tiled. (Florian Kainz)
	* Added preview image examples to IlmImfExamples.  Added
	  description of preview images and environment maps to
	  docs/api.html (Florian Kainz)
	* Bug fix: PXR24 compression did not work properly for channels
	  with ySampling != 1.
	  (Florian Kainz)
        * Made template <class T> become  template <class S, class T> for 
          the transform(ObjectS, ObjectT) methods. This was done to allow
          for differing templated objects to be passed in e.g.  say a 
          Box<Vec3<S>> and a Matrix44<T>, where S=float and T=double.
          (Jeff Yost, Arkell Rasiah)
        * New method Matrix44::setTheMatrix(). Used for assigning a 
          M44f to a M44d. (Jeff Yost, Arkell Rasiah)
        * Added convenience Color typedefs for half versions of Color3
          and Color4. Note the Makefile.am for both Imath and ImathTest
          have been updated with -I and/or -L pathing to Half.
          (Max Chen, Arkell Rasiah)
        * Methods equalWithAbsError() and equalWithRelError() are now
          declared as const. (Colette Mullenhoff, Arkell Rasiah)
        * Fixes for gcc34. Mainly typename/template/using/this syntax
          correctness changes. (Nick Ramussen, Arkell Rasiah)
	* Added Custom low-level file I/O examples to IlmImfExamples
	  and to the docs/api.html document.  (Florian Kainz)
	* Eliminated most warnings messages when OpenEXR is compiled
	  with Visual C++.  The OpenEXR code uses lots of (intentional
	  and unintended) implicit type conversions.  By default, Visual
	  C++ warns about almost all of them.  Most implicit conversions
	  have been removed from the .h files, so that including them
	  should not generate warnings even at warning level 3.  Most
	  .cpp files are now compiled with warning level 1.
	  (Florian Kainz)

Version 1.2.0:
	* Production-ready release.
	* Disable long double warnings on OS X.  (Drew Hess)
	* Add new source files to VC7 IlmImfDll target.  (Drew Hess)
	* Iex: change the way that APPEND_EXC and REPLACE_EXC modify
	  their what() string to work around an issue with Visual C++
	  7.1.  (Florian Kainz, Nick Porcino)
	* Bumped OpenEXR version to 1.2 and .so versions to 2.0.0 in
	  preparation for the release.  (Drew Hess)
	* Imath: fixed ImathTMatrix.h to work with gcc 3.4.  (Drew Hess)
	* Another quoting fix in openexr.m4.  (Drew Hess)
	* Quoting fix in acinclude.m4 for automake 1.8.  (Brad Hards)
	* Imath: put inline at beginning of declaration in ImathMatrix.h
	  to fix a warning.  (Ken McGaugh)
	* Imath: made Vec equalWith*Error () methods const.
	* Cleaned up compile-time Win32 support.  (Florian Kainz)
	* Bug fix: Reading a particular broken PIZ-compressed file
	  caused crashes by indexing off the end of an array.
	  (Florian Kainz)

Version 1.1.1:
	* Half: operator= and variants now return by reference rather
	  than by value.  This brings half into conformance with
	  built-in types.  (Drew Hess)
	* Half: remove copy constructor, let compiler supply its
	  own.  This improves performance up to 25% on some
	  expressions using half.  (Drew Hess)
	* configure: don't try to be fancy with CXXFLAGS, just use
	  what the user supplies or let configure choose a sensible
	  default if CXXFLAGS is not defined.
        * IlmImf: fixed a bug in reading scanline files on big-endian
          architectures.  (Drew Hess)
	* exrmaketiled: Added an option to select compression type.
	  (Florian Kainz)
	* exrenvmap: Added an option to select compression type.
	  (Florian Kainz)
	* exrdisplay: Added some new command-line options.  (Florian Kainz)
	* IlmImf: Added Pixar's new "slightly lossy" image compression
	  method.  The new method, named PXR24, preserves HALF and
	  UINT data without loss, but FLOAT pixels are converted to
	  a 24-bit representation.  PXR24 appears to compress
	  FLOAT depth buffers very well without losing much accuracy.
	  (Loren Carpenter, Florian Kainz)
	* Changed top-level LICENSE file to allow for other copyright
	  holders for individual files.
	* IlmImf: TILED FILE FORMAT CHANGE.  TiledOutputFile was
	  incorrectly interleaving channels and scanlines before
	  passing pixel data to a compressor.  The lossless compressors
	  still work, but lossy compressors do not.  Fix the bug by
	  interleaving channels and scanlines in tiled files in the
	  same way as ScanLineOutputFile does.  Programs compiled with
	  the new version of IlmImf cannot read tiled images produced
	  with version 1.1.0.  (Florian Kainz)
	* IlmImf: ImfXdr.h fix for 64-bit architectures.  (Florian Kainz)
	* IlmImf: OpenEXR now supports YCA (luminance/chroma/alpha)
	  images with subsampled chroma channels.  When an image
	  is written with the RGBA convenience interface, selecting
	  WRITE_YCA instead of WRITE_RGBA causes the library to
	  convert the pixels to YCA format.  If WRITE_Y is selected,
	  only luminance is stored in the file (for black and white
	  images).  When an image file is read with the RGBA convenience
	  interface, YCA data are automatically converted back to RGBA.
	  (Florian Kainz)
	* IlmImf: speed up reading tiled files as scan lines.
	  (Florian Kainz)
	* Half:  Fixed subtle bug in Half where signaling float NaNs
	  were being converted to inf in half.  (Florian Kainz)
	* gcc 3.3 compiler warning cleanups.  (various)
	* Imath: ImathEuler.h fixes for gcc 3.4.  (Garrick Meeker)
	
Version 1.1.0:
	* Added new targets to Visual C++ .NET 2003 project
	  for exrmaketiled, exrenvmap, exrmakepreview, and exrstdattr.
	  (Drew Hess)
	* A few assorted Win32 fixes for Imath.  (Drew Hess)
	* GNU autoconf builds now produce versioned libraries.
	  This release is 1:0:0.  (Drew Hess)
	* Fixes for Visual C++ .NET 2003.  (Paul Schneider)
	* Updated Visual C++ zlib project file to zlib 1.2.1.
	  (Drew Hess)
        * exrdisplay: Fixed fragment shader version.  (Drew Hess)
	* *Test: Fixed some compiler issues.  (Drew Hess)
	* Imath: Handle "restrict" keyword properly.  (Drew Hess)
	* IlmImfExamples: Updated to latest versions of example
	  source code, includes tiling and multi-res images.
	  (Florian Kainz)
	* exrmakepreview: A new utility to create preview images.
	  (Florian Kainz)
	* exrenvmap: A new utility to create OpenEXR environment
	  maps.  (Florian Kainz)
	* exrstdattr: A new utility to modify standard 
	  attributes.  (Florian Kainz)
	* Updated exrheader to print level rounding mode and
	  preview image size.  (Florian Kainz)
	* Updated exrmaketiled to use level rounding mode.
	  (Florian Kainz)
	* IlmImf: Changed the orientation of lat-long envmaps to
	  match typical panoramic camera setups.  (Florian Kainz)
	* IlmImf: Fixed a bug where partially-completed files with
	  DECREASING_Y could not be read.  (Florian Kainz)
	* IlmImf: Added support for selectable rounding mode (up/down)
	  when generating multiresolution files.  (Florian Kainz)
	* exrdisplay: Support for tiled images, mip/ripmaps, preview
	  images, and display windows.  (Florian Kainz, Drew Hess)
	* exrmaketiled: A new utility which generates tiled
	  versions of OpenEXR images.  (Florian Kainz)
	* IlmImf: Changed Imf::VERSION to Imf::EXR_VERSION to
	  work around problems with autoconf VERSION macro
	  conflict.  (Drew Hess)
	* exrheader: Support for tiles, mipmaps, environment
	  maps.  (Florian Kainz)
	* IlmImf: Environment map support.  (Florian Kainz)
	* IlmImf: Abstracted stream I/O support.  (Florian Kainz)
	* IlmImf: Support for tiled and mip/ripmapped files;
	  requires new file format.  (Wojciech Jarosz, Florian Kainz)
	* Imath: TMatrix*, generic 2D matricies and algorithms.
	  (Francesco Callari)
	* Imath: major quaternions cleanup.  (Cary Phillips)
	* Imath: added GLBegin, GLPushAttrib, GLPushMatrix objects
	  for automatic cleanup on exceptions.  (Cary Phillips)
	* Imath: removed implicit scalar->vector promotions and vector
	  comparisons.  (Nick Rasmussen)
	
Version 1.0.7:
	* Fixed a typo in one of the IlmImfTest tests. (Paul Schneider)
	* Fixed a bug in exrdisplay that causes the image to display
	  as all black if there's a NaN or infinity in an OpenEXR
	  image. (Florian Kainz)
	* Updated exrheader per recent changes to IlmImf library.
	  (Florian Kainz)
	* Changed an errant float to a T in ImathFrame.h nextFrame().
	  (Cary Phillips)
	* Support for new "optional standard" attributes
	  (chromaticities, luminance, comments, etc.).
	  (Florian Kainz, Greg Ward, Joseph Goldstone)
	* Fixed a buffer overrun in ImfOpaqueAttribute. (Paul Schneider)
	* Added new function, isImfMagic (). (Florian Kainz)
	
Version 1.0.6:
	* Added README.win32 to disted files.
	* Fixed OpenEXR.pc.in pkg-config file, OpenEXR now works
	  with pkg-config.
	* Random fixes to readme files for new release.
	* Fixed openexr.m4, now looks in /usr by default.
	* Added Visual Studio .NET 2003 "solution."
	* Fixes for Visual Studio .NET 2003 w/ Microsoft C++ compiler.
	  (Various)
	* Random Imath fixes and enhancements.  Note that 
	  extractSHRT now takes an additional optional
          argument, see ImathMatrixAlgo.h for details.  (Various)
	* Added Wojciech Jarosz to AUTHORS file.
	* Added test cases for uncompressed case, preview images,
	  frame buffer type conversion.  (Wojciech Jarosz,
	  Florian Kainz)
	* Fix a bug in IlmImf where uncompressed data doesn't get
	  read/written correctly.  (Wojciech Jarosz)
	* Added support for preview images and preview image
	  attributes (thumbnail images) in IlmImf.  (Florian Kainz)
	* Added support for automatic frame buffer type conversion
	  in IlmImf.  (Florian Kainz)
	* Cleaned up some compile-time checks.
	* Added HalfTest unit tests.
	* [exrdisplay] Download half framebuffer to texture memory 
	  instead of converting to float first.  Requires latest
	  Nvidia drivers.

Version 1.0.5:
        * Fixed IlmImf.dll to use static runtime libs (Andreas).
	* Added exrheader project to Visual Studio 6.0 workspace.
	* Added some example code showing how to use the IlmImf library.
	  (Florian)
	* Use DLL runtime libs for Win32 libraries rather than static
	  runtime libs.
	* Add an exrdisplay_fragshader project to the Visual Studio 6.0
	  workspace to enable fragment shaders in Win32.
	* Add an IlmImfDll project to the Visual Studio 6.0 workspace.
	* In Win32, export the ImfCRgbaFile C interface via a DLL so
	  that Visual C++ 6.0 users can link against an Intel-compiled
	  IlmImf.  (Andreas Kahler)
	* Use auto_ptr in ImfAutoArray on Win32, it doesn't like large 
	  automatic stacks.
	* Performance improvements in PIZ decoding, between
	  20 and 60% speedup on Athlon and Pentium 4 systems.
          (Florian)
	* Updated the README with various information, made
	  some cosmetic changes for readability.
	* Added fragment shader support to exrdisplay.
        * Bumped the version to 1.0.5 in prep for release.
	* Updated README and README.OSX to talk about CodeWarrior 
          project files.
	* Incorporated Rodrigo Damazio's patch for an openexr.m4
	  macro file and an openexr.spec file for building RPMs.
	* Small change in ImfAttribute.h to make IlmImf compile with gcc 2.95.
	* Updated ImfDoubleAttribute.h for Codewarrior on MacOS.
	* Added exrheader utility.
	* Update to AUTHORS file.
	* Added a README.win32 file.
	* Added project files for Visual Studio 6.0.
	* Initial Win32 port.  Requires Visual Studio 6.0 and Intel C++
	  compiler version 7.0.
	* Added new intersectT method in ImathSphere.h
	* Fixed some bugs in ImathQuat.h
	* Proper use of fltk-config to get platform-specific FLTK
	  compile- and link-time flags.
	* exrdisplay uses Imath::Math<T>::pow instead of powf now.
	  powf is not availble on all platforms.
	* Roll OS X "hack" into the source until Apple fixes their
	  istream implementation.
	
Version 1.0.4:
        * OpenEXR is now covered by a modified BSD license.  See LICENSE
	  for the new terms.

Version 1.0.3:

	* OpenEXR is now in sf.net CVS.
	* Imf::Xdr namespace cleanups.
	* Some IlmImfTest cleanups for OS X.
	* Use .cpp extension in exrdisplay sources.
	* Iex cleanups.
	* Make IlmImf compile with Metrowerks Codewarrior.
	* Change large automatic stacks in ImfHuf.C to auto_ptrs allocated
	  off the heap.  MacOS X default stack size isn't large enough.
	* std::ios fix for MacOS X in ImfInputFile.C.
	* Added new FP predecessor/successor functions to Imath, added
	  tests to ImathTest
	* Fixed a bug in Imath::extractSHRT for 3x3 matricies when
	  exactly one of the original scaling factors is negative, updated
	  ImathTest to check this case.
	* Install include files when 'make install' is run.
	* exrdisplay requires fltk 1.1+ now in an effort to support
	  a MacOS X display program (fltk 1.1 runs on OS X), though this
	  is untested.
	* renamed configure.in to configure.ac
	* Removed some tests from IexTest that are no longer used.
	* Removed ImfHalfXdr.h, it's not used anymore.
	* Revamped the autoconf system, added some compile-time 
          optimizations, a pkgconfig target, and some maintainer-specific
          stuff.

Version 1.0.2:

        * More OS X fixes in Imath, IlmImf and IlmImfTest.
        * Imath updates.
        * Fixed a rotation bug in Imath

Version 1.0.1:

	* Used autoconf 2.53 and automake 1.6 to generate build environment.
	* Makefile.am cleanups.
	* OS X fixes.
        * removed images directory (now distributed separately).

Version 1.0:

        * first official release.
        * added some high-level documentation, removed the old OpenEXR.html
          documentation.
        * fixed a few nagging build problems.
	* bumped IMV_VERSION_NUMBER to 2

Version 0.9:

	* added exrdisplay viewer application.
	* cleanup _data in Imf::InputFile and Imf::OutputFile constructors.
	* removed old ILM copyright notices.

Version 0.8:

	* Initial release.
