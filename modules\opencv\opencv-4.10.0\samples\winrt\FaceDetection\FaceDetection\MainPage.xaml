﻿<Page x:Class="FaceDetection.MainPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="using:FaceDetection"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      mc:Ignorable="d">

    <Grid Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
        <Button x:Name="InitBtn"
                Width="218"
                Height="67"
                Margin="69,81,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Click="InitBtn_Click"
                Content="Initialize" />
        <Button x:Name="detectBtn"
                Width="218"
                Height="67"
                Margin="69,168,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Click="detectBtn_Click"
                Content="Detect Faces" />
        <StackPanel x:Name="cvContainer"
                    Width="883"
                    Height="446"
                    Margin="354,84,0,0"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Top" />

    </Grid>
</Page>
