﻿// ============================================================================
// 鼠标控制线程类实现文件
// 功能：智能瞄准、目标预测、鼠标移动控制、自动射击
// 支持多种硬件设备：Arduino、KMBox、罗技G HUB、Windows API
// ============================================================================

#define WIN32_LEAN_AND_MEAN
#define _WINSOCKAPI_
#include <winsock2.h>
#include <Windows.h>

#include <cmath>
#include <algorithm>
#include <chrono>
#include <mutex>
#include <atomic>
#include <vector>

#include "mouse.h"
#include "capture.h"
#include "SerialConnection.h"
#include "sunone_aimbot_cpp.h"
#include "ghub.h"

// ============================================================================
// 构造函数：初始化鼠标控制线程的所有参数和设备连接
// ============================================================================
MouseThread::MouseThread(
    int resolution,                    // 屏幕分辨率（正方形）
    int fovX,                         // 水平视野角度
    int fovY,                         // 垂直视野角度
    double minSpeedMultiplier,        // 最小移动速度倍数
    double maxSpeedMultiplier,        // 最大移动速度倍数
    double predictionInterval,        // 目标位置预测时间间隔
    bool auto_shoot,                  // 是否启用自动射击
    float bScope_multiplier,          // 瞄准镜倍数修正系数
    SerialConnection* serialConnection,      // Arduino串口设备连接
    GhubMouse* gHubMouse,                   // 罗技G HUB鼠标连接
    Kmbox_b_Connection* kmboxConnection,     // KMBox USB设备连接
    KmboxNetConnection* Kmbox_Net_Connection) // KMBox网络设备连接
    : screen_width(resolution),        // 屏幕宽度
    screen_height(resolution),         // 屏幕高度
    prediction_interval(predictionInterval),  // 预测时间间隔
    fov_x(fovX),                      // 水平视野角度
    fov_y(fovY),                      // 垂直视野角度
    max_distance(std::hypot(resolution, resolution) / 2.0),  // 最大移动距离（屏幕对角线的一半）
    min_speed_multiplier(minSpeedMultiplier),  // 最小速度倍数
    max_speed_multiplier(maxSpeedMultiplier),  // 最大速度倍数
    center_x(resolution / 2.0),       // 屏幕中心X坐标
    center_y(resolution / 2.0),       // 屏幕中心Y坐标
    auto_shoot(auto_shoot),           // 自动射击开关
    bScope_multiplier(bScope_multiplier),  // 瞄准镜倍数系数
    serial(serialConnection),         // Arduino串口连接指针
    kmbox(kmboxConnection),           // KMBox USB连接指针
    kmbox_net(Kmbox_Net_Connection),  // KMBox网络连接指针
    gHub(gHubMouse),                  // 罗技G HUB连接指针
    prev_velocity_x(0.0),             // 上一帧目标X方向速度
    prev_velocity_y(0.0),             // 上一帧目标Y方向速度
    prev_x(0.0),                      // 上一帧目标X坐标
    prev_y(0.0)                       // 上一帧目标Y坐标
{
    // 初始化时间点为空（表示还没有历史数据）
    prev_time = std::chrono::steady_clock::time_point();
    // 设置最后检测到目标的时间为当前时间
    last_target_time = std::chrono::steady_clock::now();

    // 从全局配置加载Wind Mouse算法参数
    wind_mouse_enabled = config.wind_mouse_enabled;  // 是否启用Wind Mouse自然移动
    wind_G = config.wind_G;  // 重力参数（朝向目标的吸引力）
    wind_W = config.wind_W;  // 风力参数（随机扰动强度）
    wind_M = config.wind_M;  // 质量参数（影响加速度）
    wind_D = config.wind_D;  // 阻力参数（速度衰减系数）

    // 启动异步鼠标移动工作线程
    moveWorker = std::thread(&MouseThread::moveWorkerLoop, this);
}

// ============================================================================
// 运行时配置更新函数：允许在程序运行过程中动态修改参数
// ============================================================================
void MouseThread::updateConfig(
    int resolution,                   // 新的屏幕分辨率
    int fovX,                        // 新的水平视野角度
    int fovY,                        // 新的垂直视野角度
    double minSpeedMultiplier,       // 新的最小速度倍数
    double maxSpeedMultiplier,       // 新的最大速度倍数
    double predictionInterval,       // 新的预测时间间隔
    bool auto_shoot,                 // 新的自动射击设置
    float bScope_multiplier          // 新的瞄准镜倍数系数
)
{
    // 更新屏幕相关参数
    screen_width = screen_height = resolution;
    fov_x = fovX;  fov_y = fovY;
    
    // 更新速度控制参数
    min_speed_multiplier = minSpeedMultiplier;
    max_speed_multiplier = maxSpeedMultiplier;
    
    // 更新预测和射击参数
    prediction_interval = predictionInterval;
    this->auto_shoot = auto_shoot;
    this->bScope_multiplier = bScope_multiplier;

    // 重新计算屏幕中心点和最大移动距离
    center_x = center_y = resolution / 2.0;
    max_distance = std::hypot(resolution, resolution) / 2.0;

    // 重新加载Wind Mouse算法参数
    wind_mouse_enabled = config.wind_mouse_enabled;
    wind_G = config.wind_G; wind_W = config.wind_W;
    wind_M = config.wind_M; wind_D = config.wind_D;
}

// ============================================================================
// 析构函数：清理资源，安全停止工作线程
// ============================================================================
MouseThread::~MouseThread()
{
    workerStop = true;           // 设置停止标志，通知工作线程退出
    queueCv.notify_all();        // 唤醒所有等待的线程
    if (moveWorker.joinable()) moveWorker.join();  // 等待工作线程安全结束
}

// ============================================================================
// 鼠标移动命令入队函数：将移动命令添加到异步处理队列
// ============================================================================
void MouseThread::queueMove(int dx, int dy)
{
    std::lock_guard lg(queueMtx);  // 锁定队列互斥锁
    
    // 如果队列已满，移除最旧的命令防止积压
    if (moveQueue.size() >= queueLimit) moveQueue.pop();
    
    // 添加新的移动命令到队列
    moveQueue.push({ dx,dy });
    
    // 通知工作线程有新任务
    queueCv.notify_one();
}

// ============================================================================
// 鼠标移动工作线程主循环：异步处理移动命令队列
// ============================================================================
void MouseThread::moveWorkerLoop()
{
    while (!workerStop)  // 持续运行直到收到停止信号
    {
        std::unique_lock ul(queueMtx);  // 获取队列锁
        
        // 等待队列有数据或收到停止信号
        queueCv.wait(ul, [&] { return workerStop || !moveQueue.empty(); });

        // 批量处理队列中的所有移动命令
        while (!moveQueue.empty())
        {
            Move m = moveQueue.front();  // 获取队首命令
            moveQueue.pop();             // 移除已处理的命令
            ul.unlock();                 // 临时释放锁
            
            // 发送移动命令到硬件驱动
            sendMovementToDriver(m.dx, m.dy);
            
            ul.lock();                   // 重新获取锁继续处理
        }
    }
}

// ============================================================================
// Wind Mouse算法实现：生成自然的人类鼠标移动轨迹
// 模拟物理特性：重力、风力、惯性、阻力等，避免被反作弊系统检测
// ============================================================================
void MouseThread::windMouseMoveRelative(int dx, int dy)
{
    // 如果没有移动需求，直接返回
    if (dx == 0 && dy == 0) return;

    // 数学常数定义
    constexpr double SQRT3 = 1.7320508075688772;  // 根号3
    constexpr double SQRT5 = 2.23606797749979;    // 根号5

    // 算法状态变量
    double sx = 0, sy = 0;           // 当前累积位置
    double dxF = static_cast<double>(dx);  // 目标X位置
    double dyF = static_cast<double>(dy);  // 目标Y位置
    double vx = 0, vy = 0, wX = 0, wY = 0;  // 速度和风力向量
    int    cx = 0, cy = 0;           // 已发送的累积位置

    // Wind Mouse物理模拟主循环：持续移动直到接近目标
    while (std::hypot(dxF - sx, dyF - sy) >= 1.0)
    {
        double dist = std::hypot(dxF - sx, dyF - sy);  // 计算到目标的距离
        double wMag = std::min(wind_W, dist);          // 风力强度随距离调整

        // 根据距离调整风力影响
        if (dist >= wind_D)
        {
            // 更新风力向量（带有随机性和衰减）
            wX = wX / SQRT3 + ((double)rand() / RAND_MAX * 2.0 - 1.0) * wMag / SQRT5;
            wY = wY / SQRT3 + ((double)rand() / RAND_MAX * 2.0 - 1.0) * wMag / SQRT5;
        }
        else
        {
            // 接近目标时减少风力影响
            wX /= SQRT3;  wY /= SQRT3;
            // 动态调整质量参数
            wind_M = wind_M < 3.0 ? ((double)rand() / RAND_MAX) * 3.0 + 3.0 : wind_M / SQRT5;
        }

        // 更新速度：风力 + 重力（朝向目标的吸引力）
        vx += wX + wind_G * (dxF - sx) / dist;  // X方向加速度
        vy += wY + wind_G * (dyF - sy) / dist;  // Y方向加速度

        // 速度限制：防止移动过快
        double vMag = std::hypot(vx, vy);  // 计算速度大小
        if (vMag > wind_M)
        {
            // 随机化速度裁剪值，增加自然性
            double vClip = wind_M / 2.0 + ((double)rand() / RAND_MAX) * wind_M / 2.0;
            vx = (vx / vMag) * vClip;  // 按比例缩放X速度
            vy = (vy / vMag) * vClip;  // 按比例缩放Y速度
        }

        // 更新位置：当前位置 + 速度
        sx += vx;  sy += vy;
        
        // 计算实际需要移动的整数像素
        int rx = static_cast<int>(std::round(sx));
        int ry = static_cast<int>(std::round(sy));
        int step_x = rx - cx;  // 相对于上次发送位置的增量
        int step_y = ry - cy;
        
        // 如果有实际移动量，加入移动队列
        if (step_x || step_y)
        {
            queueMove(step_x, step_y);
            cx = rx; cy = ry;  // 更新已发送位置
        }
    }
}

// ============================================================================
// 目标位置预测算法：基于历史轨迹预测目标未来位置
// 用于补偿网络延迟和处理延迟，提高命中率
// ============================================================================
std::pair<double, double> MouseThread::predict_target_position(double target_x, double target_y)
{
    auto current_time = std::chrono::steady_clock::now();

    // 首次检测或目标重新出现：初始化预测数据
    if (prev_time.time_since_epoch().count() == 0 || !target_detected.load())
    {
        prev_time = current_time;
        prev_x = target_x;
        prev_y = target_y;
        prev_velocity_x = 0.0;
        prev_velocity_y = 0.0;
        return { target_x, target_y };  // 无历史数据时返回当前位置
    }

    // 计算时间间隔（帧间时间）
    double dt = std::chrono::duration<double>(current_time - prev_time).count();
    if (dt < 1e-8) dt = 1e-8;  // 防止除零错误

    // 计算目标移动速度（像素/秒）
    double vx = (target_x - prev_x) / dt;
    double vy = (target_y - prev_y) / dt;

    // 速度限制：防止异常数据导致的极端预测
    vx = std::clamp(vx, -20000.0, 20000.0);
    vy = std::clamp(vy, -20000.0, 20000.0);

    // 更新历史数据
    prev_time = current_time;
    prev_x = target_x;
    prev_y = target_y;
    prev_velocity_x = vx;
    prev_velocity_y = vy;

    // 基于当前速度预测未来位置
    double predictedX = target_x + vx * prediction_interval;
    double predictedY = target_y + vy * prediction_interval;

    // 检测延迟补偿：根据AI推理时间进行额外预测
    double detectionDelay = 0.01;  // 默认50ms延迟
    if (config.backend == "DML")
    {
        // 使用DirectML后端的实际推理时间
        detectionDelay = dml_detector->lastInferenceTimeDML.count();
    }
#ifdef USE_CUDA
    else
    {
        // 使用CUDA后端的实际推理时间
        detectionDelay = trt_detector.lastInferenceTime.count();
    }
#endif
    // 应用检测延迟补偿
    predictedX += vx * detectionDelay;
    predictedY += vy * detectionDelay;

    return { predictedX, predictedY };
}

// ============================================================================
// 硬件驱动发送函数：将移动命令发送到各种硬件设备
// 支持多种输入设备，根据配置自动选择
// ============================================================================
void MouseThread::sendMovementToDriver(int dx, int dy)
{
    std::lock_guard<std::mutex> lock(input_method_mutex);  // 确保线程安全

    // 根据配置的输入方法选择对应的硬件驱动
    if (kmbox)
    {
        // KMBox USB设备：硬件级鼠标模拟
        kmbox->move(dx, dy);
    }
    else if (kmbox_net)
    {
        // KMBox网络设备：通过网络控制的硬件鼠标
        kmbox_net->move(dx, dy);
    }
    else if (serial)
    {
        // Arduino串口设备：自制硬件鼠标模拟器
        serial->move(dx, dy);
    }
    else if (gHub)
    {
        // 罗技G HUB：利用官方驱动的鼠标控制
        gHub->mouse_xy(dx, dy);
    }
    else
    {
        // 默认方法：Windows API软件模拟
        INPUT in{ 0 };
        in.type = INPUT_MOUSE;
        in.mi.dx = dx;  in.mi.dy = dy;
        in.mi.dwFlags = MOUSEEVENTF_MOVE | MOUSEEVENTF_VIRTUALDESK;
        SendInput(1, &in, sizeof(INPUT));
    }
}

// ============================================================================
// 移动量计算函数：将屏幕坐标转换为鼠标移动增量
// 考虑视野角度、速度曲线、帧率补偿等因素
// ============================================================================
std::pair<double, double> MouseThread::calc_movement(double tx, double ty)
{
    // 计算目标相对于屏幕中心的偏移量
    double offx = tx - center_x;
    double offy = ty - center_y;
    double dist = std::hypot(offx, offy);  // 计算到中心的距离
    
    // 根据距离计算速度倍数（距离越远速度越快）
    double speed = calculate_speed_multiplier(dist);

    // 像素到角度转换：根据视野角度计算每像素对应的角度
    double degPerPxX = fov_x / screen_width;   // 水平方向每像素角度
    double degPerPxY = fov_y / screen_height;  // 垂直方向每像素角度
    double mmx = offx * degPerPxX;  // 水平角度偏移
    double mmy = offy * degPerPxY;  // 垂直角度偏移

    // 帧率补偿：高帧率时减少单帧移动量，保持总体速度一致
    double corr = 1.0;
    double fps = static_cast<double>(captureFps.load());
    if (fps > 60.0) corr = 60.0 / fps;  // 以30fps为基准进行补偿

    // 角度到鼠标计数转换：将角度转换为硬件鼠标的计数值
    auto counts_pair = config.degToCounts(mmx, mmy, fov_x);
    double move_x = counts_pair.first * speed * corr;   // 最终X方向移动量
    double move_y = counts_pair.second * speed * corr;  // 最终Y方向移动量

    return { move_x, move_y };
}

// ============================================================================
// 速度倍数计算函数：根据距离计算移动速度倍数
// 实现智能速度曲线：近距离慢速精确，远距离快速移动
// ============================================================================
double MouseThread::calculate_speed_multiplier(double distance)
{
    // 极近距离：使用最小速度 + 快速瞄准加成
    if (distance < config.snapRadius)
        return min_speed_multiplier * config.snapBoostFactor;

    // 近距离：使用平滑的速度曲线过渡
    if (distance < config.nearRadius)
    {
        double t = distance / config.nearRadius;  // 归一化距离 [0,1]
        // 应用指数曲线，实现平滑加速
        double curve = 1.0 - std::pow(1.0 - t, config.speedCurveExponent);
        return min_speed_multiplier +
            (max_speed_multiplier - min_speed_multiplier) * curve;
    }

    // 远距离：线性插值到最大速度
    double norm = std::clamp(distance / max_distance, 0.0, 1.0);
    return min_speed_multiplier +
        (max_speed_multiplier - min_speed_multiplier) * norm;
}

// ============================================================================
// 目标范围检查函数：判断准星是否在目标的有效射击区域内
// 用于自动射击的触发条件判断
// ============================================================================
bool MouseThread::check_target_in_scope(double target_x, double target_y, double target_w, double target_h, double reduction_factor)
{
    // 计算目标框的中心点
    double center_target_x = target_x + target_w / 2.0;
    double center_target_y = target_y + target_h / 2.0;

    // 根据缩减因子计算有效射击区域（通常小于目标框）
    double reduced_w = target_w * (reduction_factor / 2.0);
    double reduced_h = target_h * (reduction_factor / 2.0);

    // 计算有效射击区域的边界
    double x1 = center_target_x - reduced_w;
    double x2 = center_target_x + reduced_w;
    double y1 = center_target_y - reduced_h;
    double y2 = center_target_y + reduced_h;

    // 检查屏幕中心（准星位置）是否在有效区域内
    return (center_x > x1 && center_x < x2 && center_y > y1 && center_y < y2);
}

// ============================================================================
// 标准移动函数：移动鼠标到目标中心（带预测）
// 主要用于传统的目标框中心瞄准
// ============================================================================
void MouseThread::moveMouse(const AimbotTarget& target)
{
    std::lock_guard lg(input_method_mutex);

    // 预测目标框中心点的未来位置
    auto predicted = predict_target_position(
        target.x + target.w / 2.0,  // 目标框中心X坐标
        target.y + target.h / 2.0); // 目标框中心Y坐标

    // 计算移动量并加入异步处理队列
    auto mv = calc_movement(predicted.first, predicted.second);
    queueMove(static_cast<int>(mv.first), static_cast<int>(mv.second));
}

// ============================================================================
// 枢轴点移动函数：基于智能枢轴点的高级移动算法
// 这是主要使用的移动方式，提供更精确的瞄准控制
// ============================================================================
void MouseThread::moveMousePivot(double pivotX, double pivotY)
{
    std::lock_guard lg(input_method_mutex);

    auto current_time = std::chrono::steady_clock::now();

    // 首次检测到目标或目标重新出现的处理
    if (prev_time.time_since_epoch().count() == 0 || !target_detected.load())
    {
        // 初始化预测数据
        prev_time = current_time;
        prev_x = pivotX; prev_y = pivotY;
        prev_velocity_x = prev_velocity_y = 0.0;

        // 直接移动到目标位置（无预测）
        auto m0 = calc_movement(pivotX, pivotY);
        queueMove(static_cast<int>(m0.first), static_cast<int>(m0.second));
        return;
    }

    // 计算时间间隔和速度
    double dt = std::chrono::duration<double>(current_time - prev_time).count();
    prev_time = current_time;
    dt = std::max(dt, 1e-8);  // 防止除零

    // 计算当前速度并限制范围
    double vx = std::clamp((pivotX - prev_x) / dt, -20000.0, 20000.0);
    double vy = std::clamp((pivotY - prev_y) / dt, -20000.0, 20000.0);
    prev_x = pivotX; prev_y = pivotY;
    prev_velocity_x = vx;  prev_velocity_y = vy;

    // 预测未来位置：基础预测 + 额外延迟补偿
    double predX = pivotX + vx * prediction_interval + vx * 0.002;
    double predY = pivotY + vy * prediction_interval + vy * 0.002;

    // 计算移动量
    auto mv = calc_movement(predX, predY);
    int mx = static_cast<int>(mv.first);
    int my = static_cast<int>(mv.second);

    // 根据配置选择移动方式
    if (wind_mouse_enabled)  windMouseMoveRelative(mx, my);  // 自然轨迹移动
    else                     queueMove(mx, my);              // 直线移动
}

// ============================================================================
// 自动射击控制函数：智能判断射击时机并控制鼠标按键
// 根据目标是否在瞄准范围内自动按下或释放鼠标左键
// ============================================================================
void MouseThread::pressMouse(const AimbotTarget& target)
{
    std::lock_guard<std::mutex> lock(input_method_mutex);

    // 检查目标是否在有效射击范围内（考虑瞄准镜倍数）
    bool bScope = check_target_in_scope(target.x, target.y, target.w, target.h, bScope_multiplier);
    
    // 目标在范围内且当前未按下鼠标 -> 开始射击
    if (bScope && !mouse_pressed)
    {
        if (kmbox)
        {
            kmbox->press(0);  // KMBox USB设备按键
        }
        else if (kmbox_net)
        {
            kmbox_net->keyDown(0);  // KMBox网络设备按键
        }
        else if (serial)
        {
            serial->press();  // Arduino设备按键
        }
        else if (gHub)
        {
            gHub->mouse_down();  // 罗技G HUB按键
        }
        else
        {
            // Windows API软件按键
            INPUT input = { 0 };
            input.type = INPUT_MOUSE;
            input.mi.dwFlags = MOUSEEVENTF_LEFTDOWN;
            SendInput(1, &input, sizeof(INPUT));
        }
        mouse_pressed = true;  // 标记鼠标已按下
    }
    // 目标不在范围内但鼠标仍按下 -> 停止射击
    else if (!bScope && mouse_pressed)
    {
        if (kmbox)
        {
            kmbox->release(0);  // KMBox USB设备释放
        }
        else if (kmbox_net)
        {
            kmbox_net->keyUp(0);  // KMBox网络设备释放
        }
        else if (serial)
        {
            serial->release();  // Arduino设备释放
        }
        else if (gHub)
        {
            gHub->mouse_up();  // 罗技G HUB释放
        }
        else
        {
            // Windows API软件释放
            INPUT input = { 0 };
            input.type = INPUT_MOUSE;
            input.mi.dwFlags = MOUSEEVENTF_LEFTUP;
            SendInput(1, &input, sizeof(INPUT));
        }
        mouse_pressed = false;  // 标记鼠标已释放
    }
}

// ============================================================================
// 强制释放鼠标函数：确保鼠标左键被释放
// 用于紧急情况或程序退出时的清理
// ============================================================================
void MouseThread::releaseMouse()
{
    std::lock_guard<std::mutex> lock(input_method_mutex);

    // 只有在鼠标当前被按下时才执行释放操作
    if (mouse_pressed)
    {
        if (kmbox)
        {
            kmbox->release(0);
        }
        else if (kmbox_net)
        {
            kmbox_net->keyUp(0);
        }
        else if (serial)
        {
            serial->release();
        }
        else if (gHub)
        {
            gHub->mouse_up();
        }
        else
        {
            INPUT input = { 0 };
            input.type = INPUT_MOUSE;
            input.mi.dwFlags = MOUSEEVENTF_LEFTUP;
            SendInput(1, &input, sizeof(INPUT));
        }
        mouse_pressed = false;  // 更新状态标志
    }
}

// ============================================================================
// 预测数据重置函数：清除所有历史预测信息
// 用于目标丢失或重新开始跟踪时的状态重置
// ============================================================================
void MouseThread::resetPrediction()
{
    prev_time = std::chrono::steady_clock::time_point();  // 重置时间点
    prev_x = 0;                    // 重置历史X坐标
    prev_y = 0;                    // 重置历史Y坐标
    prev_velocity_x = 0;           // 重置X方向速度
    prev_velocity_y = 0;           // 重置Y方向速度
    target_detected.store(false);  // 标记目标未检测到
}

// ============================================================================
// 预测状态检查函数：监控目标丢失时间，自动重置过期的预测数据
// 防止长时间目标丢失后的错误预测
// ============================================================================
void MouseThread::checkAndResetPredictions()
{
    auto current_time = std::chrono::steady_clock::now();
    // 计算自上次检测到目标以来的时间
    double elapsed = std::chrono::duration<double>(current_time - last_target_time).count();

    // 如果超过0.5秒没有检测到目标，重置预测数据
    if (elapsed > 0.5 && target_detected.load())
    {
        resetPrediction();
    }
}

// ============================================================================
// 未来位置序列预测函数：预测目标在未来多帧的位置轨迹
// 主要用于可视化显示和轨迹分析
// ============================================================================
std::vector<std::pair<double, double>> MouseThread::predictFuturePositions(double pivotX, double pivotY, int frames)
{
    std::vector<std::pair<double, double>> result;
    result.reserve(frames);  // 预分配内存提高性能

    const double fixedFps = 30.0;           // 固定帧率用于预测计算
    double frame_time = 1.0 / fixedFps;     // 每帧时间间隔

    auto current_time = std::chrono::steady_clock::now();
    double dt = std::chrono::duration<double>(current_time - prev_time).count();

    // 如果没有有效的历史数据或时间间隔过长，返回空结果
    if (prev_time.time_since_epoch().count() == 0 || dt > 0.5)
    {
        return result;
    }

    // 使用当前计算的速度进行预测
    double vx = prev_velocity_x;
    double vy = prev_velocity_y;
    
    // 预测未来指定帧数的位置
    for (int i = 1; i <= frames; i++)
    {
        double t = frame_time * i;  // 未来第i帧的时间

        // 线性预测：当前位置 + 速度 × 时间
        double px = pivotX + vx * t;
        double py = pivotY + vy * t;

        result.push_back({ px, py });  // 添加预测位置到结果
    }

    return result;
}

// ============================================================================
// 未来位置数据管理函数：线程安全的数据存储和访问
// 用于在多线程环境下安全地共享预测轨迹数据
// ============================================================================

// 存储预测位置数据
void MouseThread::storeFuturePositions(const std::vector<std::pair<double, double>>& positions)
{
    std::lock_guard<std::mutex> lock(futurePositionsMutex);
    futurePositions = positions;
}

// 清空预测位置数据
void MouseThread::clearFuturePositions()
{
    std::lock_guard<std::mutex> lock(futurePositionsMutex);
    futurePositions.clear();
}

// 获取预测位置数据的副本
std::vector<std::pair<double, double>> MouseThread::getFuturePositions()
{
    std::lock_guard<std::mutex> lock(futurePositionsMutex);
    return futurePositions;  // 返回副本确保线程安全
}

// ============================================================================
// 设备连接管理函数：线程安全的硬件设备切换
// 允许在运行时动态切换不同的输入设备
// ============================================================================

// 设置Arduino串口连接
void MouseThread::setSerialConnection(SerialConnection* newSerial)
{
    std::lock_guard<std::mutex> lock(input_method_mutex);
    serial = newSerial;
}

// 设置KMBox USB连接
void MouseThread::setKmboxConnection(Kmbox_b_Connection* newKmbox)
{
    std::lock_guard<std::mutex> lock(input_method_mutex);
    kmbox = newKmbox;
}

// 设置KMBox网络连接
void MouseThread::setKmboxNetConnection(KmboxNetConnection* newKmbox_net)
{
    std::lock_guard<std::mutex> lock(input_method_mutex);
    kmbox_net = newKmbox_net;
}

// 设置罗技G HUB连接
void MouseThread::setGHubMouse(GhubMouse* newGHub)
{
    std::lock_guard<std::mutex> lock(input_method_mutex);
    gHub = newGHub;
}

