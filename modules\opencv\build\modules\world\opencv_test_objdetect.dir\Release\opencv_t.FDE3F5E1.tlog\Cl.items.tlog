D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\opencl\test_hogdetector.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_objdetect.dir\Release\test_hogdetector.obj
D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_aruco_tutorial.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_objdetect.dir\Release\test_aruco_tutorial.obj
D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_aruco_utils.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_objdetect.dir\Release\test_aruco_utils.obj
D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_arucodetection.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_objdetect.dir\Release\test_arucodetection.obj
D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_barcode.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_objdetect.dir\Release\test_barcode.obj
D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_boarddetection.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_objdetect.dir\Release\test_boarddetection.obj
D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_cascadeandhog.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_objdetect.dir\Release\test_cascadeandhog.obj
D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_charucodetection.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_objdetect.dir\Release\test_charucodetection.obj
D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_face.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_objdetect.dir\Release\test_face.obj
D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_main.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_objdetect.dir\Release\test_main.obj
D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_qrcode.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_objdetect.dir\Release\test_qrcode.obj
D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_qrcode_encode.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_objdetect.dir\Release\test_qrcode_encode.obj
