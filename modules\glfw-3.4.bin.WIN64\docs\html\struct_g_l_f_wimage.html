<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: GLFWimage Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">GLFWimage Struct Reference<div class="ingroups"><a class="el" href="group__window.html">Window reference</a></div></div></div>
</div><!--header-->
<div class="contents">

<p>Image data.  
 <a href="struct_g_l_f_wimage.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:af6a71cc999fe6d3aea31dd7e9687d835" id="r_af6a71cc999fe6d3aea31dd7e9687d835"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wimage.html#af6a71cc999fe6d3aea31dd7e9687d835">width</a></td></tr>
<tr class="separator:af6a71cc999fe6d3aea31dd7e9687d835"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b7d95368f0c80d5e5c9875057c7dbec" id="r_a0b7d95368f0c80d5e5c9875057c7dbec"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wimage.html#a0b7d95368f0c80d5e5c9875057c7dbec">height</a></td></tr>
<tr class="separator:a0b7d95368f0c80d5e5c9875057c7dbec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0c532a5c2bb715555279b7817daba0fb" id="r_a0c532a5c2bb715555279b7817daba0fb"><td class="memItemLeft" align="right" valign="top">unsigned char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wimage.html#a0c532a5c2bb715555279b7817daba0fb">pixels</a></td></tr>
<tr class="separator:a0c532a5c2bb715555279b7817daba0fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>This describes a single 2D image. See the documentation for each related function what the expected pixel format is.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#cursor_custom">Custom cursor creation</a> </dd>
<dd>
<a class="el" href="window_guide.html#window_icon">Window icon</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 2.1. <b>GLFW 3:</b> Removed format and bytes-per-pixel members. </dd></dl>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="af6a71cc999fe6d3aea31dd7e9687d835" name="af6a71cc999fe6d3aea31dd7e9687d835"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af6a71cc999fe6d3aea31dd7e9687d835">&#9670;&#160;</a></span>width</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int GLFWimage::width</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The width, in pixels, of this image. </p>

</div>
</div>
<a id="a0b7d95368f0c80d5e5c9875057c7dbec" name="a0b7d95368f0c80d5e5c9875057c7dbec"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0b7d95368f0c80d5e5c9875057c7dbec">&#9670;&#160;</a></span>height</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int GLFWimage::height</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The height, in pixels, of this image. </p>

</div>
</div>
<a id="a0c532a5c2bb715555279b7817daba0fb" name="a0c532a5c2bb715555279b7817daba0fb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0c532a5c2bb715555279b7817daba0fb">&#9670;&#160;</a></span>pixels</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">unsigned char* GLFWimage::pixels</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The pixel data of this image, arranged left-to-right, top-to-bottom. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="glfw3_8h_source.html">glfw3.h</a></li>
</ul>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
