D:/AI/opencv/cudabuild/install/etc/licenses/ippicv-readme.htm
D:/AI/opencv/cudabuild/install/etc/licenses/ippicv-EULA.rtf
D:/AI/opencv/cudabuild/install/etc/licenses/ippicv-third-party-programs.txt
D:/AI/opencv/cudabuild/install/etc/licenses/ippiw-support.txt
D:/AI/opencv/cudabuild/install/etc/licenses/ippiw-third-party-programs.txt
D:/AI/opencv/cudabuild/install/etc/licenses/ippiw-EULA.rtf
D:/AI/opencv/cudabuild/install/etc/licenses/flatbuffers-LICENSE.txt
D:/AI/opencv/cudabuild/install/etc/licenses/opencl-headers-LICENSE.txt
D:/AI/opencv/cudabuild/install/etc/licenses/ade-LICENSE
D:/AI/opencv/cudabuild/install/etc/licenses/ffmpeg-license.txt
D:/AI/opencv/cudabuild/install/etc/licenses/ffmpeg-readme.txt
D:/AI/opencv/cudabuild/install/include/opencv2/cvconfig.h
D:/AI/opencv/cudabuild/install/include/opencv2/opencv_modules.hpp
D:/AI/opencv/cudabuild/install/x64/vc17/lib/OpenCVModules.cmake
D:/AI/opencv/cudabuild/install/x64/vc17/lib/OpenCVModules-release.cmake
D:/AI/opencv/cudabuild/install/x64/vc17/lib/OpenCVConfig-version.cmake
D:/AI/opencv/cudabuild/install/x64/vc17/lib/OpenCVConfig.cmake
D:/AI/opencv/cudabuild/install/./OpenCVConfig-version.cmake
D:/AI/opencv/cudabuild/install/./OpenCVConfig.cmake
D:/AI/opencv/cudabuild/install/./LICENSE
D:/AI/opencv/cudabuild/install/./setup_vars_opencv4.cmd
D:/AI/opencv/cudabuild/install/etc/licenses/zlib-LICENSE
D:/AI/opencv/cudabuild/install/etc/licenses/libjpeg-turbo-README.md
D:/AI/opencv/cudabuild/install/etc/licenses/libjpeg-turbo-LICENSE.md
D:/AI/opencv/cudabuild/install/etc/licenses/libjpeg-turbo-README.ijg
D:/AI/opencv/cudabuild/install/etc/licenses/libtiff-COPYRIGHT
D:/AI/opencv/cudabuild/install/etc/licenses/libopenjp2-README.md
D:/AI/opencv/cudabuild/install/etc/licenses/libopenjp2-LICENSE
D:/AI/opencv/cudabuild/install/etc/licenses/libpng-LICENSE
D:/AI/opencv/cudabuild/install/etc/licenses/libpng-README
D:/AI/opencv/cudabuild/install/etc/licenses/openexr-LICENSE
D:/AI/opencv/cudabuild/install/etc/licenses/openexr-AUTHORS.ilmbase
D:/AI/opencv/cudabuild/install/etc/licenses/openexr-AUTHORS.openexr
D:/AI/opencv/cudabuild/install/etc/licenses/protobuf-LICENSE
D:/AI/opencv/cudabuild/install/etc/licenses/protobuf-README.md
D:/AI/opencv/cudabuild/install/etc/licenses/ittnotify-LICENSE.BSD
D:/AI/opencv/cudabuild/install/etc/licenses/ittnotify-LICENSE.GPL
D:/AI/opencv/cudabuild/install/include/opencv2/opencv.hpp
D:/AI/opencv/cudabuild/install/etc/licenses/SoftFloat-COPYING.txt
D:/AI/opencv/cudabuild/install/etc/licenses/mscr-chi_table_LICENSE.txt
D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_videoio_ffmpeg4100_64.dll
D:/AI/opencv/cudabuild/install/etc/licenses/vasot-LICENSE.txt
D:/AI/opencv/cudabuild/install/x64/vc17/lib/opencv_world4100.lib
D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_world4100.dll
D:/AI/opencv/cudabuild/install/include/opencv2/calib3d.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/calib3d/calib3d.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/calib3d/calib3d_c.h
D:/AI/opencv/cudabuild/install/include/opencv2/core.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/affine.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/async.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/base.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/bindings_utils.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/bufferpool.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/check.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/core.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/core_c.h
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda.inl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/block.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/border_interpolate.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/color.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/common.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/datamov_utils.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/detail/color_detail.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/detail/reduce.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/detail/reduce_key_val.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/detail/transform_detail.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/detail/type_traits_detail.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/detail/vec_distance_detail.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/dynamic_smem.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/emulation.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/filters.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/funcattrib.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/functional.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/limits.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/reduce.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/saturate_cast.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/scan.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/simd_functions.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/transform.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/type_traits.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/utility.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/vec_distance.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/vec_math.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/vec_traits.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/warp.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/warp_reduce.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/warp_shuffle.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda_stream_accessor.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda_types.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cv_cpu_dispatch.h
D:/AI/opencv/cudabuild/install/include/opencv2/core/cv_cpu_helper.h
D:/AI/opencv/cudabuild/install/include/opencv2/core/cvdef.h
D:/AI/opencv/cudabuild/install/include/opencv2/core/cvstd.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cvstd.inl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/cvstd_wrapper.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/detail/async_promise.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/detail/dispatch_helper.impl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/detail/exception_ptr.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/directx.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/dualquaternion.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/dualquaternion.inl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/eigen.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/fast_math.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/hal.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/interface.h
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_avx.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_avx512.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_cpp.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_forward.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_lasx.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_lsx.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_msa.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_neon.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_rvv.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_rvv071.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_rvv_010_compat_non-policy.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_rvv_010_compat_overloaded-non-policy.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_rvv_011_compat.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_rvv_compat_overloaded.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_rvv_scalable.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_sse.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_sse_em.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_vsx.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_wasm.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/msa_macros.h
D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/simd_utils.impl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/mat.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/mat.inl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/matx.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/matx.inl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/neon_utils.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/ocl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/ocl_genbase.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/ocl_defs.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/opencl_info.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/opencl_svm.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_clblas.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_clfft.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_core.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_core_wrappers.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_gl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_gl_wrappers.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_clblas.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_clfft.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_core.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_core_wrappers.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_gl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_gl_wrappers.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_svm_20.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_svm_definitions.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_svm_hsa_extension.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/opengl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/operations.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/optim.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/ovx.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/parallel/backend/parallel_for.openmp.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/parallel/backend/parallel_for.tbb.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/parallel/parallel_backend.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/persistence.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/quaternion.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/quaternion.inl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/saturate.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/simd_intrinsics.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/softfloat.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/sse_utils.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/traits.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/types.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/types_c.h
D:/AI/opencv/cudabuild/install/include/opencv2/core/utility.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/allocator_stats.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/allocator_stats.impl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/filesystem.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/fp_control_utils.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/instrumentation.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/logger.defines.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/logger.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/logtag.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/tls.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/trace.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/va_intel.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/version.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core/vsx_utils.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/dnn.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/dnn/all_layers.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/dnn/dict.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/dnn/dnn.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/dnn/dnn.inl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/dnn/layer.details.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/dnn/layer.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/dnn/shape_utils.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/dnn/utils/debug_utils.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/dnn/utils/inference_engine.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/dnn/version.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/features2d.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/features2d/features2d.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/features2d/hal/interface.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/flann/all_indices.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/allocator.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/any.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/autotuned_index.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/composite_index.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/config.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/defines.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/dist.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/dummy.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/dynamic_bitset.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/flann.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/flann/flann_base.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/flann/general.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/ground_truth.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/hdf5.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/heap.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/hierarchical_clustering_index.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/index_testing.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/kdtree_index.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/kdtree_single_index.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/kmeans_index.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/linear_index.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/logger.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/lsh_index.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/lsh_table.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/matrix.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/miniflann.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/flann/nn_index.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/object_factory.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/params.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/random.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/result_set.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/sampling.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/saving.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/simplex_downhill.h
D:/AI/opencv/cudabuild/install/include/opencv2/flann/timer.h
D:/AI/opencv/cudabuild/install/include/opencv2/gapi.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/core.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/cpu/core.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/cpu/gcpukernel.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/cpu/imgproc.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/cpu/ot.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/cpu/stereo.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/cpu/video.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/fluid/core.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/fluid/gfluidbuffer.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/fluid/gfluidkernel.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/fluid/imgproc.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/garg.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/garray.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gasync_context.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gcall.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gcommon.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gcompiled.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gcompiled_async.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gcompoundkernel.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gcomputation.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gcomputation_async.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gframe.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gkernel.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gmat.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gmetaarg.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gopaque.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gproto.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gpu/core.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gpu/ggpukernel.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gpu/imgproc.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gscalar.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gstreaming.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gtransform.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gtype_traits.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gtyped.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/imgproc.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/infer.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/infer/bindings_ie.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/infer/bindings_onnx.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/infer/bindings_ov.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/infer/ie.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/infer/onnx.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/infer/ov.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/infer/parsers.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/media.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/oak/infer.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/oak/oak.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/ocl/core.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/ocl/goclkernel.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/ocl/imgproc.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/opencv_includes.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/operators.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/ot.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/own/assert.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/own/convert.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/own/cvdefs.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/own/exports.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/own/mat.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/own/saturate.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/own/scalar.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/own/types.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/plaidml/core.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/plaidml/gplaidmlkernel.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/plaidml/plaidml.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/python/python.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/render.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/render/render.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/render/render_types.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/rmat.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/s11n.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/s11n/base.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/stereo.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/cap.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/desync.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/format.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/gstreamer/gstreamerpipeline.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/gstreamer/gstreamersource.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/meta.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/onevpl/accel_types.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/onevpl/cfg_params.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/onevpl/data_provider_interface.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/onevpl/default.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/onevpl/device_selector_interface.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/onevpl/source.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/queue_source.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/source.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/sync.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/util/any.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/util/compiler_hints.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/util/copy_through_move.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/util/optional.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/util/throw.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/util/type_traits.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/util/util.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/util/variant.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/gapi/video.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/highgui.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/highgui/highgui.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/highgui/highgui_c.h
D:/AI/opencv/cudabuild/install/include/opencv2/imgcodecs.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/imgcodecs/imgcodecs.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/imgcodecs/imgcodecs_c.h
D:/AI/opencv/cudabuild/install/include/opencv2/imgcodecs/ios.h
D:/AI/opencv/cudabuild/install/include/opencv2/imgcodecs/legacy/constants_c.h
D:/AI/opencv/cudabuild/install/include/opencv2/imgcodecs/macosx.h
D:/AI/opencv/cudabuild/install/include/opencv2/imgproc.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/bindings.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/detail/gcgraph.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/detail/legacy.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/hal/hal.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/hal/interface.h
D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/imgproc.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/imgproc_c.h
D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/segmentation.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/types_c.h
D:/AI/opencv/cudabuild/install/include/opencv2/ml.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ml/ml.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ml/ml.inl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/objdetect.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/aruco_board.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/aruco_detector.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/aruco_dictionary.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/barcode.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/charuco_detector.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/detection_based_tracker.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/face.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/graphical_code_detector.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/objdetect.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/photo.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/photo/cuda.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/photo/legacy/constants_c.h
D:/AI/opencv/cudabuild/install/include/opencv2/photo/photo.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stitching.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/autocalib.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/blenders.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/camera.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/exposure_compensate.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/matchers.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/motion_estimators.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/seam_finders.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/timelapsers.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/util.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/util_inl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/warpers.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/warpers_inl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stitching/warpers.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/video.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/video/background_segm.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/video/detail/tracking.detail.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/video/legacy/constants_c.h
D:/AI/opencv/cudabuild/install/include/opencv2/video/tracking.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/video/video.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videoio.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videoio/cap_ios.h
D:/AI/opencv/cudabuild/install/include/opencv2/videoio/legacy/constants_c.h
D:/AI/opencv/cudabuild/install/include/opencv2/videoio/registry.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videoio/videoio.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videoio/videoio_c.h
D:/AI/opencv/cudabuild/install/include/opencv2/world.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/aruco.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/aruco/aruco_calib.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/aruco/charuco.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/bgsegm.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/bioinspired.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/bioinspired/bioinspired.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/bioinspired/retina.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/bioinspired/retinafasttonemapping.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/bioinspired/transientareassegmentationmodule.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ccalib.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ccalib/multicalib.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ccalib/omnidir.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ccalib/randpattern.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudaarithm.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudabgsegm.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudacodec.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudafeatures2d.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudafilters.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudaimgproc.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudalegacy.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudalegacy/NCV.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudalegacy/NCVBroxOpticalFlow.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudalegacy/NCVHaarObjectDetection.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudalegacy/NCVPyramid.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudalegacy/NPP_staging.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudaobjdetect.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudaoptflow.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudastereo.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudawarping.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/block/block.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/block/detail/reduce.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/block/detail/reduce_key_val.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/block/dynamic_smem.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/block/reduce.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/block/scan.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/block/vec_distance.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/common.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/common.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/binary_func.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/binary_op.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/color.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/deriv.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/expr.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/per_element_func.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/reduction.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/unary_func.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/unary_op.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/warping.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/functional/color_cvt.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/functional/detail/color_cvt.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/functional/functional.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/functional/tuple_adapter.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/copy.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/copy.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/histogram.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/integral.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/minmaxloc.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/pyr_down.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/pyr_up.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/reduce.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/reduce_to_column.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/reduce_to_row.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/split_merge.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/transform.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/transpose.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/histogram.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/integral.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/pyramids.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/reduce.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/reduce_to_vec.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/split_merge.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/transform.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/transpose.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/constant.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/deriv.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/detail/gpumat.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/extrapolation.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/glob.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/gpumat.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/interpolation.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/lut.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/mask.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/remap.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/resize.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/texture.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/traits.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/transform.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/warping.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/zip.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/atomic.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/detail/tuple.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/detail/type_traits.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/limits.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/saturate_cast.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/simd_functions.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/tuple.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/type_traits.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/vec_math.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/vec_traits.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/warp/detail/reduce.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/warp/detail/reduce_key_val.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/warp/reduce.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/warp/scan.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/warp/shuffle.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/cudev/warp/warp.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/ar_hmdb.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/ar_sports.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/dataset.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/fr_adience.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/fr_lfw.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/gr_chalearn.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/gr_skig.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/hpe_humaneva.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/hpe_parse.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/ir_affine.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/ir_robot.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/is_bsds.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/is_weizmann.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/msm_epfl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/msm_middlebury.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/or_imagenet.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/or_mnist.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/or_pascal.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/or_sun.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/pd_caltech.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/pd_inria.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/slam_kitti.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/slam_tumindoor.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/sr_bsds.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/sr_div2k.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/sr_general100.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/tr_chars.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/tr_icdar.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/tr_svt.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/track_alov.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/track_vot.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/datasets/util.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/core_detect.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/dnn_superres.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/dpm.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/face.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/face/bif.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/face/face_alignment.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/face/facemark.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/face/facemarkAAM.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/face/facemarkLBF.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/face/facemark_train.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/face/facerec.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/face/mace.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/face/predict_collector.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/fuzzy.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/fuzzy/fuzzy_F0_math.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/fuzzy/fuzzy_F1_math.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/fuzzy/fuzzy_image.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/fuzzy/types.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/hfs.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/intensity_transform.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/line_descriptor.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/line_descriptor/descriptor.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/mcc.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/mcc/ccm.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/mcc/checker_detector.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/mcc/checker_model.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/optflow.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/optflow/motempl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/optflow/pcaflow.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/optflow/rlofflow.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/optflow/sparse_matching_gpc.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/phase_unwrapping.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/phase_unwrapping/histogramphaseunwrapping.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/phase_unwrapping/phase_unwrapping.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/plot.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/quality.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/quality/quality_utils.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/quality/qualitybase.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/quality/qualitybrisque.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/quality/qualitygmsd.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/quality/qualitymse.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/quality/qualitypsnr.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/quality/qualityssim.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/rapid.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/reg/map.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/reg/mapaffine.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/reg/mapper.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/reg/mappergradaffine.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/reg/mappergradeuclid.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/reg/mappergradproj.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/reg/mappergradshift.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/reg/mappergradsimilar.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/reg/mapperpyramid.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/reg/mapprojec.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/reg/mapshift.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/rgbd.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/colored_kinfu.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/depth.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/detail/pose_graph.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/dynafu.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/intrinsics.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/kinfu.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/large_kinfu.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/linemod.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/volume.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/saliency.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/saliency/saliencyBaseClasses.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/saliency/saliencySpecializedClasses.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/shape.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/shape/emdL1.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/shape/hist_cost.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/shape/shape.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/shape/shape_distance.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/shape/shape_transformer.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/signal.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/signal/signal_resample.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stereo.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stereo/descriptor.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stereo/quasi_dense_stereo.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/stereo/stereo.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/structured_light.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/structured_light/graycodepattern.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/structured_light/sinusoidalpattern.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/structured_light/structured_light.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/superres.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/superres/optical_flow.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/surface_matching.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/surface_matching/icp.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/surface_matching/pose_3d.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/surface_matching/ppf_helpers.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/surface_matching/ppf_match_3d.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/surface_matching/t_hash_int.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/text.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/text/erfilter.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/text/ocr.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/text/swt_text_detection.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/text/textDetector.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/tracking.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/tracking/feature.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/tracking/kalman_filters.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/tracking/onlineBoosting.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/tracking/tldDataset.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/tracking/tracking.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/tracking/tracking_by_matching.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/tracking/tracking_internals.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/tracking/tracking_legacy.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/tracking/twist.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videostab.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videostab/deblurring.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videostab/fast_marching.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videostab/fast_marching_inl.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videostab/frame_source.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videostab/global_motion.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videostab/inpainting.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videostab/log.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videostab/motion_core.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videostab/motion_stabilizing.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videostab/optical_flow.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videostab/outlier_rejection.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videostab/ring_buffer.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videostab/stabilizer.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/videostab/wobble_suppression.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/wechat_qrcode.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/xfeatures2d.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/xfeatures2d/cuda.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/xfeatures2d/nonfree.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/brightedges.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/color_match.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/deriche_filter.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/disparity_filter.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/edge_drawing.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/edge_filter.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/edgeboxes.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/edgepreserving_filter.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/estimated_covariance.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/fast_hough_transform.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/fast_line_detector.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/find_ellipses.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/fourier_descriptors.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/lsc.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/paillou_filter.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/peilin.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/radon_transform.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/ridgefilter.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/run_length_morphology.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/scansegment.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/seeds.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/segmentation.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/slic.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/sparse_match_interpolator.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/structured_edge_detection.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/weighted_median_filter.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/xobjdetect.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/xphoto.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/xphoto/bm3d_image_denoising.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/xphoto/dct_image_denoising.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/xphoto/inpainting.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/xphoto/oilpainting.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/xphoto/tonemap.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/xphoto/white_balance.hpp
D:/AI/opencv/cudabuild/install/bin/opencv_waldboost_detector.exe
D:/AI/opencv/cudabuild/install/x64/vc17/lib/opencv_img_hash4100.lib
D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_img_hash4100.dll
D:/AI/opencv/cudabuild/install/include/opencv2/img_hash.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/img_hash/average_hash.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/img_hash/block_mean_hash.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/img_hash/color_moment_hash.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/img_hash/img_hash_base.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/img_hash/marr_hildreth_hash.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/img_hash/phash.hpp
D:/AI/opencv/cudabuild/install/include/opencv2/img_hash/radial_variance_hash.hpp
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/__init__.py
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/load_config_py2.py
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/load_config_py3.py
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/config.py
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/misc/__init__.py
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/misc/version.py
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/mat_wrapper/__init__.py
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/utils/__init__.py
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/__init__.py
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/aruco/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/barcode/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/bgsegm/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/bioinspired/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ccm/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/colored_kinfu/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/cuda/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/cudacodec/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/datasets/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/detail/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/dnn/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/dnn_superres/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/dpm/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/dynafu/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/Error/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/face/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/fisheye/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/flann/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ft/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/core/cpu/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/core/fluid/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/core/ocl/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/core/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/ie/detail/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/ie/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/imgproc/fluid/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/imgproc/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/oak/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/onnx/ep/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/onnx/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/ot/cpu/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/ot/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/ov/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/own/detail/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/own/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/render/ocv/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/render/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/streaming/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/video/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/wip/draw/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/wip/gst/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/wip/onevpl/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/wip/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/hfs/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/img_hash/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/intensity_transform/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ipp/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/kinfu/detail/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/kinfu/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/large_kinfu/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/legacy/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/linemod/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/line_descriptor/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/mcc/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ml/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/motempl/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/multicalib/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ocl/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ogl/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/omnidir/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/optflow/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/parallel/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/phase_unwrapping/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/plot/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ppf_match_3d/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/py.typed
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/quality/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/rapid/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/reg/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/rgbd/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/saliency/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/samples/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/segmentation/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/signal/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/stereo/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/structured_light/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/text/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/typing/__init__.py
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/utils/fs/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/utils/nested/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/utils/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/videoio_registry/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/videostab/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/wechat_qrcode/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/xfeatures2d/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ximgproc/segmentation/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ximgproc/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/xphoto/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/__init__.pyi
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/python-3.11/cv2.cp311-win_amd64.pyd
C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/config-3.11.py
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_eye.xml
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_eye_tree_eyeglasses.xml
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_frontalcatface.xml
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_frontalcatface_extended.xml
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_frontalface_alt.xml
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_frontalface_alt2.xml
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_frontalface_alt_tree.xml
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_frontalface_default.xml
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_fullbody.xml
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_lefteye_2splits.xml
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_license_plate_rus_16stages.xml
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_lowerbody.xml
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_profileface.xml
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_righteye_2splits.xml
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_russian_plate_number.xml
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_smile.xml
D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_upperbody.xml
D:/AI/opencv/cudabuild/install/etc/lbpcascades/lbpcascade_frontalcatface.xml
D:/AI/opencv/cudabuild/install/etc/lbpcascades/lbpcascade_frontalface.xml
D:/AI/opencv/cudabuild/install/etc/lbpcascades/lbpcascade_frontalface_improved.xml
D:/AI/opencv/cudabuild/install/etc/lbpcascades/lbpcascade_profileface.xml
D:/AI/opencv/cudabuild/install/etc/lbpcascades/lbpcascade_silverware.xml
D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_annotation.exe
D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_visualisation.exe
D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_interactive-calibration.exe
D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_version.exe
D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_version_win32.exe
D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_model_diagnostics.exe