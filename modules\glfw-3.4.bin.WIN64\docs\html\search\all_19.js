var searchData=
[
  ['safety_0',['Thread safety',['../intro_guide.html#thread_safety',1,'']]],
  ['scale_1',['scale',['../monitor_guide.html#monitor_scale',1,'Content scale'],['../window_guide.html#window_scale',1,'Window content scale']]],
  ['scaling_2',['Window hint for framebuffer scaling',['../news.html#scale_framebuffer_hint',1,'']]],
  ['screen_20windows_3',['screen windows',['../window_guide.html#window_windowed_full_screen',1,'&quot;Windowed full screen&quot; windows'],['../window_guide.html#window_full_screen',1,'Full screen windows']]],
  ['scroll_20input_4',['Scroll input',['../input_guide.html#scrolling',1,'']]],
  ['scroll_20offsets_5',['Wheel position replaced by scroll offsets',['../moving_guide.html#moving_wheel',1,'']]],
  ['selection_6',['selection',['../moving_guide.html#moving_monitor',1,'Explicit monitor selection'],['../news.html#runtime_platform_selection',1,'Runtime platform selection'],['../intro_guide.html#platform',1,'Runtime platform selection']]],
  ['separation_20of_20window_20and_20framebuffer_20sizes_7',['Separation of window and framebuffer sizes',['../moving_guide.html#moving_hidpi',1,'']]],
  ['server_8',['X11 empty events no longer round-trip to server',['../news.html#x11_emptyevent_caveat',1,'']]],
  ['sets_20of_20native_20access_20functions_9',['Multiple sets of native access functions',['../news.html#multiplatform_caveat',1,'']]],
  ['setting_10',['Cursor setting',['../input_guide.html#cursor_set',1,'']]],
  ['setting_20an_20error_20callback_11',['Setting an error callback',['../quick_guide.html#quick_capture_error',1,'']]],
  ['shapes_12',['shapes',['../news.html#more_cursor_shapes',1,'More standard cursor shapes'],['../group__shapes.html',1,'Standard cursor shapes']]],
  ['shared_20cmake_20options_13',['Shared CMake options',['../compile_guide.html#compile_options_shared',1,'']]],
  ['shared_20init_20hints_14',['Shared init hints',['../intro_guide.html#init_hints_shared',1,'']]],
  ['sharing_15',['Context object sharing',['../context_guide.html#context_sharing',1,'']]],
  ['show_20command_20hint_16',['Windows STARTUPINFO show command hint',['../news.html#win32_showdefault_hint',1,'']]],
  ['size_17',['size',['../window_guide.html#window_fbsize',1,'Framebuffer size'],['../monitor_guide.html#monitor_size',1,'Physical size'],['../struct_g_l_f_wgammaramp.html#ad620e1cffbff9a32c51bca46301b59a5',1,'GLFWgammaramp::size'],['../window_guide.html#window_size',1,'Window size']]],
  ['size_20limits_18',['Window size limits',['../window_guide.html#window_sizelimits',1,'']]],
  ['sizes_19',['Separation of window and framebuffer sizes',['../moving_guide.html#moving_hidpi',1,'']]],
  ['soft_20constraints_20',['Hard and soft constraints',['../window_guide.html#window_hints_hard',1,'']]],
  ['source_21',['With CMake and GLFW source',['../build_guide.html#build_link_cmake_source',1,'']]],
  ['specific_20cmake_20options_22',['specific cmake options',['../compile_guide.html#compile_options_macos',1,'macOS specific CMake options'],['../compile_guide.html#compile_options_unix',1,'Unix-like system specific CMake options'],['../compile_guide.html#compile_options_win32',1,'Win32 specific CMake options']]],
  ['specific_20hints_23',['specific hints',['../window_guide.html#window_hints_osx',1,'macOS specific hints'],['../window_guide.html#window_hints_win32',1,'Win32 specific hints']]],
  ['specific_20init_20hints_24',['specific init hints',['../intro_guide.html#init_hints_osx',1,'macOS specific init hints'],['../intro_guide.html#init_hints_wayland',1,'Wayland specific init hints'],['../intro_guide.html#init_hints_x11',1,'X11 specific init hints']]],
  ['specific_20window_20hints_25',['specific window hints',['../window_guide.html#window_hints_wayland',1,'Wayland specific window hints'],['../window_guide.html#window_hints_x11',1,'X11 specific window hints']]],
  ['standard_20cursor_20creation_26',['Standard cursor creation',['../input_guide.html#cursor_standard',1,'']]],
  ['standard_20cursor_20shapes_27',['standard cursor shapes',['../news.html#more_cursor_shapes',1,'More standard cursor shapes'],['../group__shapes.html',1,'Standard cursor shapes']]],
  ['standards_28',['standards',['../compat_guide.html#compat_wayland',1,'Wayland protocols and IPC standards'],['../compat_guide.html#compat_x11',1,'X11 extensions, protocols and IPC standards']]],
  ['standards_20conformance_29',['Standards conformance',['../compat_guide.html',1,'']]],
  ['started_30',['Getting started',['../quick_guide.html',1,'']]],
  ['startupinfo_20show_20command_20hint_31',['Windows STARTUPINFO show command hint',['../news.html#win32_showdefault_hint',1,'']]],
  ['states_32',['states',['../input_guide.html#joystick_axis',1,'Joystick axis states'],['../input_guide.html#joystick_button',1,'Joystick button states'],['../input_guide.html#joystick_hat',1,'Joystick hat states'],['../group__hat__state.html',1,'Joystick hat states']]],
  ['static_20functions_33',['Static functions',['../internals_guide.html#internals_static',1,'']]],
  ['step_34',['Step by step',['../quick_guide.html#quick_steps',1,'']]],
  ['step_20by_20step_35',['Step by step',['../quick_guide.html#quick_steps',1,'']]],
  ['string_36',['Version string',['../intro_guide.html#intro_version_string',1,'']]],
  ['string_20format_20has_20been_20changed_37',['Version string format has been changed',['../news.html#version_string_caveat',1,'']]],
  ['structure_38',['Internal structure',['../internals_guide.html',1,'']]],
  ['subproject_39',['Tests and examples are disabled when built as a subproject',['../news.html#standalone_caveat',1,'']]],
  ['support_40',['support',['../vulkan_guide.html#vulkan_present',1,'Querying for Vulkan presentation support'],['../vulkan_guide.html#vulkan_support',1,'Querying for Vulkan support'],['../moving_guide.html#moving_mbcs',1,'Win32 MBCS support']]],
  ['support_20for_20custom_20heap_20memory_20allocator_41',['Support for custom heap memory allocator',['../news.html#custom_heap_allocator',1,'']]],
  ['support_20for_20versions_20of_20windows_20older_20than_20xp_42',['Support for versions of Windows older than XP',['../moving_guide.html#moving_windows',1,'']]],
  ['support_20has_20been_20removed_43',['wl_shell protocol support has been removed',['../news.html#wl_shell_removed',1,'']]],
  ['support_20is_20deprecated_44',['support is deprecated',['../news.html#mingw_deprecated',1,'Original MinGW support is deprecated'],['../news.html#yosemite_deprecated',1,'OS X Yosemite support is deprecated'],['../news.html#winxp_deprecated',1,'Windows XP and Vista support is deprecated']]],
  ['support_20is_20initialized_20on_20demand_45',['Joystick support is initialized on demand',['../news.html#joystick_init_caveat',1,'']]],
  ['support_20reference_46',['Vulkan support reference',['../group__vulkan.html',1,'']]],
  ['supported_20and_20default_20values_47',['supported and default values',['../window_guide.html#window_hints_values',1,'Supported and default values'],['../intro_guide.html#init_hints_values',1,'Supported and default values']]],
  ['surface_48',['Creating a Vulkan window surface',['../vulkan_guide.html#vulkan_surface',1,'']]],
  ['surface_20app_5fid_20hint_49',['Wayland surface app_id hint',['../news.html#wayland_app_id_hint',1,'']]],
  ['surface_20hint_50',['X11 Vulkan window surface hint',['../news.html#x11_xcb_vulkan_surface',1,'']]],
  ['swapping_51',['swapping',['../context_guide.html#context_swap',1,'Buffer swapping'],['../window_guide.html#buffer_swap',1,'Buffer swapping']]],
  ['swapping_20buffers_52',['Swapping buffers',['../quick_guide.html#quick_swap_buffers',1,'']]],
  ['symbols_53',['New symbols',['../news.html#new_symbols',1,'']]],
  ['system_20specific_20cmake_20options_54',['Unix-like system specific CMake options',['../compile_guide.html#compile_options_unix',1,'']]],
  ['system_20wide_20hotkeys_55',['Capture of system-wide hotkeys',['../moving_guide.html#moving_syskeys',1,'']]],
  ['systems_56',['systems',['../intro_guide.html#coordinate_systems',1,'Coordinate systems'],['../news.html#wayland_alpha_caveat',1,'Wayland framebuffer may lack alpha channel on older systems']]]
];
