var searchData=
[
  ['macos_0',['macos',['../compat_guide.html#compat_osx',1,'OpenGL on macOS'],['../build_guide.html#build_link_osx',1,'With command-line or makefile on macOS'],['../build_guide.html#build_link_xcode',1,'With Xcode on macOS']]],
  ['macos_20corevideo_20dependency_20has_20been_20removed_1',['macOS CoreVideo dependency has been removed',['../news.html#corevideo_caveat',1,'']]],
  ['macos_20main_20menu_20now_20created_20at_20initialization_2',['macOS main menu now created at initialization',['../news.html#macos_menu_caveat',1,'']]],
  ['macos_20specific_20cmake_20options_3',['macOS specific CMake options',['../compile_guide.html#compile_options_macos',1,'']]],
  ['macos_20specific_20hints_4',['macOS specific hints',['../window_guide.html#window_hints_osx',1,'']]],
  ['macos_20specific_20init_20hints_5',['macOS specific init hints',['../intro_guide.html#init_hints_osx',1,'']]],
  ['macro_6',['Removal of GLFWCALL macro',['../moving_guide.html#moving_stdcall',1,'']]],
  ['macros_7',['macros',['../internals_guide.html#internals_config',1,'Configuration macros'],['../build_guide.html#build_macros',1,'GLFW header option macros']]],
  ['main_20menu_20now_20created_20at_20initialization_8',['macOS main menu now created at initialization',['../news.html#macos_menu_caveat',1,'']]],
  ['main_2emd_9',['main.md',['../main_8md.html',1,'']]],
  ['makefile_20on_20macos_10',['With command-line or makefile on macOS',['../build_guide.html#build_link_osx',1,'']]],
  ['making_20the_20opengl_20context_20current_11',['Making the OpenGL context current',['../quick_guide.html#quick_context_current',1,'']]],
  ['management_12',['management',['../moving_guide.html#moving_context',1,'Explicit context management'],['../intro_guide.html#intro_version',1,'Version management']]],
  ['manually_13',['manually',['../compile_guide.html#compile_manual',1,'Compiling GLFW manually'],['../context_guide.html#context_glext_manual',1,'Loading extensions manually']]],
  ['mappings_14',['Gamepad mappings',['../input_guide.html#gamepad_mapping',1,'']]],
  ['maximization_15',['Window maximization',['../window_guide.html#window_maximize',1,'']]],
  ['may_20lack_20alpha_20channel_20on_20older_20systems_16',['Wayland framebuffer may lack alpha channel on older systems',['../news.html#wayland_alpha_caveat',1,'']]],
  ['mbcs_20support_17',['Win32 MBCS support',['../moving_guide.html#moving_mbcs',1,'']]],
  ['memory_20allocator_18',['memory allocator',['../intro_guide.html#init_allocator',1,'Custom heap memory allocator'],['../news.html#custom_heap_allocator',1,'Support for custom heap memory allocator']]],
  ['menu_20keyboard_20access_20hint_19',['Windows window menu keyboard access hint',['../news.html#win32_keymenu_hint',1,'']]],
  ['menu_20now_20created_20at_20initialization_20',['macOS main menu now created at initialization',['../news.html#macos_menu_caveat',1,'']]],
  ['mingw_21',['Cross-compilation with CMake and MinGW',['../compile_guide.html#compile_mingw_cross',1,'']]],
  ['mingw_20support_20is_20deprecated_22',['Original MinGW support is deprecated',['../news.html#mingw_deprecated',1,'']]],
  ['mingw_20w64_20and_20glfw_20binaries_23',['With MinGW-w64 and GLFW binaries',['../build_guide.html#build_link_mingw',1,'']]],
  ['mode_24',['mode',['../news.html#captured_cursor_mode',1,'Captured cursor mode'],['../input_guide.html#cursor_mode',1,'Cursor mode']]],
  ['mode_20enumeration_25',['Video mode enumeration',['../moving_guide.html#moving_video_modes',1,'']]],
  ['modes_26',['Video modes',['../monitor_guide.html#monitor_modes',1,'']]],
  ['modifier_20key_20flags_27',['Modifier key flags',['../group__mods.html',1,'']]],
  ['monitor_28',['Window monitor',['../window_guide.html#window_monitor',1,'']]],
  ['monitor_20configuration_20changes_29',['Monitor configuration changes',['../monitor_guide.html#monitor_event',1,'']]],
  ['monitor_20guide_30',['Monitor guide',['../monitor_guide.html',1,'']]],
  ['monitor_20objects_31',['Monitor objects',['../monitor_guide.html#monitor_object',1,'']]],
  ['monitor_20properties_32',['Monitor properties',['../monitor_guide.html#monitor_properties',1,'']]],
  ['monitor_20reference_33',['Monitor reference',['../group__monitor.html',1,'']]],
  ['monitor_20related_20hints_34',['Monitor related hints',['../window_guide.html#window_hints_mtr',1,'']]],
  ['monitor_20selection_35',['Explicit monitor selection',['../moving_guide.html#moving_monitor',1,'']]],
  ['monitor_2emd_36',['monitor.md',['../monitor_8md.html',1,'']]],
  ['monitors_37',['Retrieving monitors',['../monitor_guide.html#monitor_monitors',1,'']]],
  ['more_20standard_20cursor_20shapes_38',['More standard cursor shapes',['../news.html#more_cursor_shapes',1,'']]],
  ['motion_39',['Raw mouse motion',['../input_guide.html#raw_mouse_motion',1,'']]],
  ['mouse_20button_20input_40',['Mouse button input',['../input_guide.html#input_mouse_button',1,'']]],
  ['mouse_20buttons_41',['Mouse buttons',['../group__buttons.html',1,'']]],
  ['mouse_20event_20passthrough_42',['Mouse event passthrough',['../news.html#mouse_input_passthrough',1,'']]],
  ['mouse_20input_43',['Mouse input',['../input_guide.html#input_mouse',1,'']]],
  ['mouse_20motion_44',['Raw mouse motion',['../input_guide.html#raw_mouse_motion',1,'']]],
  ['moving_20from_20glfw_202_20to_203_45',['Moving from GLFW 2 to 3',['../moving_guide.html',1,'']]],
  ['moving_2emd_46',['moving.md',['../moving_8md.html',1,'']]],
  ['multiple_20sets_20of_20native_20access_20functions_47',['Multiple sets of native access functions',['../news.html#multiplatform_caveat',1,'']]]
];
