#!/bin/bash

# Usage info

usage()
{
cat << EOF
usage: $0 [options]

This script runs the OpenCV tests on linux device.

OPTIONS:
   -h           Show this message
   -c           Color output
EOF
}

# Parse options

COLOR_OUTPUT=0
while getopts "hc" OPTION
do
    case $OPTION in
        h)
            usage
            exit 0
            ;;
        c)
            COLOR_OUTPUT=1
            ;;
        ?)
            usage
            exit 1
            ;;
    esac
done

# Text style

if [ $COLOR_OUTPUT -eq 1 ]; then
    TEXT_RED="$(tput setaf 1)"
    TEXT_GREEN="$(tput setaf 2)"
    TEXT_CYAN="$(tput setaf 6)"
    TEXT_RESET="$(tput sgr0)"
else
    TEXT_RED=""
    TEXT_GREEN=""
    TEXT_CYAN=""
    TEXT_RESET=""
fi

# Test binaries and data paths

OPENCV_TEST_PATH=@CMAKE_INSTALL_PREFIX@/@OPENCV_TEST_INSTALL_PATH@
OPENCV_PYTHON_TESTS=@OPENCV_PYTHON_TESTS_LIST@
export OPENCV_TEST_DATA_PATH=@CMAKE_INSTALL_PREFIX@/share/OpenCV/testdata

CUR_DIR=`pwd`
if [ -d "$CUR_DIR" -a -w "$CUR_DIR" ]; then
    echo "${TEXT_CYAN}CUR_DIR : $CUR_DIR${TEXT_RESET}"
else
    echo "${TEXT_RED}Error: Do not have permissions to write to $CUR_DIR${TEXT_RESET}"
    echo "${TEXT_RED}Please run the script from directory with write access${TEXT_RESET}"
    exit 1
fi

# Run tests

SUMMARY_STATUS=0
FAILED_TESTS=""
PASSED_TESTS=""

for t in "$OPENCV_TEST_PATH/"opencv_test_* "$OPENCV_TEST_PATH/"opencv_perf_*;
do
    test_name=`basename "$t"`

    cmd="$t --perf_min_samples=1 --perf_force_samples=1 --gtest_output=xml:$test_name.xml"

    seg_reg="s/^/${TEXT_CYAN}[$test_name]${TEXT_RESET} /"                     # append test name
    if [ $COLOR_OUTPUT -eq 1 ]; then
        seg_reg="${seg_reg};s/\[==========\]/${TEXT_GREEN}&${TEXT_RESET}/g"   # green for [==========]
        seg_reg="${seg_reg};s/\[----------\]/${TEXT_GREEN}&${TEXT_RESET}/g"   # green for [----------]
        seg_reg="${seg_reg};s/\[ RUN      \]/${TEXT_GREEN}&${TEXT_RESET}/g"   # green for [ RUN      ]
        seg_reg="${seg_reg};s/\[       OK \]/${TEXT_GREEN}&${TEXT_RESET}/g"   # green for [       OK ]
        seg_reg="${seg_reg};s/\[  FAILED  \]/${TEXT_RED}&${TEXT_RESET}/g"     # red   for [  FAILED  ]
        seg_reg="${seg_reg};s/\[  PASSED  \]/${TEXT_GREEN}&${TEXT_RESET}/g"   # green for [  PASSED  ]
    fi

    echo "${TEXT_CYAN}[$test_name]${TEXT_RESET} RUN : $cmd"
    eval "$cmd" | tee "$test_name.log" | sed -r "$seg_reg"
    ret=${PIPESTATUS[0]}
    echo "${TEXT_CYAN}[$test_name]${TEXT_RESET} RETURN_CODE : $ret"

    if [ $ret -ne 0 ]; then
        echo "${TEXT_CYAN}[$test_name]${TEXT_RESET} ${TEXT_RED}FAILED${TEXT_RESET}"
        SUMMARY_STATUS=1
        FAILED_TESTS="$FAILED_TESTS $test_name"
    else
        echo "${TEXT_CYAN}[$test_name]${TEXT_RESET} ${TEXT_GREEN}OK${TEXT_RESET}"
        PASSED_TESTS="$PASSED_TESTS $test_name"
    fi

    echo ""
done

for t in $OPENCV_PYTHON_TESTS;
do
    test_name=`basename "$t"`

    cmd="py.test --junitxml $test_name.xml \"$OPENCV_TEST_PATH\"/$t"

    seg_reg="s/^/${TEXT_CYAN}[$test_name]${TEXT_RESET} /"                 # append test name

    echo "${TEXT_CYAN}[$test_name]${TEXT_RESET} RUN : $cmd"
    eval "$cmd" | tee "$test_name.log" | sed -r "$seg_reg"

    ret=${PIPESTATUS[0]}
    echo "${TEXT_CYAN}[$test_name]${TEXT_RESET} RETURN_CODE : $ret"

    if [ $ret -ne 0 ]; then
        echo "${TEXT_CYAN}[$test_name]${TEXT_RESET} ${TEXT_RED}FAILED${TEXT_RESET}"
        SUMMARY_STATUS=1
        FAILED_TESTS="$FAILED_TESTS $test_name"
    else
        echo "${TEXT_CYAN}[$test_name]${TEXT_RESET} ${TEXT_GREEN}OK${TEXT_RESET}"
        PASSED_TESTS="$PASSED_TESTS $test_name"
    fi

    echo ""
done

# Remove temporary test files

rm -f /tmp/__opencv_temp.*

# Report final status

echo "${TEXT_CYAN}===============================================================${TEXT_RESET}"
echo "${TEXT_CYAN}PASSED TESTS : $PASSED_TESTS${TEXT_RESET}"
echo "${TEXT_CYAN}FAILED TESTS : $FAILED_TESTS${TEXT_RESET}"
if [ $SUMMARY_STATUS -eq 0 ]; then
    echo "${TEXT_GREEN}STATUS : OK${TEXT_RESET}"
    echo "${TEXT_GREEN}STATUS : All OpenCV tests finished successfully${TEXT_RESET}"
else
    echo "${TEXT_RED}STATUS : FAIL${TEXT_RESET}"
    echo "${TEXT_RED}STATUS : OpenCV tests finished with status $SUMMARY_STATUS${TEXT_RESET}"
fi

exit $SUMMARY_STATUS
