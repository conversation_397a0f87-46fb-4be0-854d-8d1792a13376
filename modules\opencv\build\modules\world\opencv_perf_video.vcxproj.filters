﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\perf\opencl\perf_bgfg_knn.cpp">
      <Filter>opencv_video\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\perf\opencl\perf_bgfg_mog2.cpp">
      <Filter>opencv_video\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\perf\opencl\perf_dis_optflow.cpp">
      <Filter>opencv_video\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\perf\opencl\perf_motempl.cpp">
      <Filter>opencv_video\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\perf\opencl\perf_optflow_farneback.cpp">
      <Filter>opencv_video\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\perf\opencl\perf_optflow_pyrlk.cpp">
      <Filter>opencv_video\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\perf\perf_bgfg_knn.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\perf\perf_bgfg_mog2.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\perf\perf_disflow.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\perf\perf_ecc.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\perf\perf_main.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\perf\perf_optflowpyrlk.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\perf\perf_trackers.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\perf\perf_variational_refinement.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\video\perf\perf_bgfg_utils.hpp">
      <Filter>opencv_video\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\video\perf\perf_precomp.hpp">
      <Filter>opencv_video\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_video">
      <UniqueIdentifier>{AF88D1C3-D1DD-3F7C-A3F4-F14A50A9C567}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_video\Include">
      <UniqueIdentifier>{EF67B89B-773A-39AE-93E5-ED0FB73D1C0D}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_video\Src">
      <UniqueIdentifier>{8AC6AE3F-77AB-3B3F-B33F-DBBB50684B8A}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_video\Src\opencl">
      <UniqueIdentifier>{272D687D-A757-3601-BC50-BCDE046F45EF}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
