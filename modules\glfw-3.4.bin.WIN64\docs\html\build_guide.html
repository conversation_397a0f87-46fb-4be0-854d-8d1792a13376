<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Building applications</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div><div class="header">
  <div class="headertitle"><div class="title">Building applications</div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#build_include">Including the GLFW header file</a><ul><li class="level2"><a href="#build_macros">GLFW header option macros</a></li>
</ul>
</li>
<li class="level1"><a href="#build_link">Link with the right libraries</a><ul><li class="level2"><a href="#build_link_win32">With Visual C++ and GLFW binaries</a></li>
<li class="level2"><a href="#build_link_mingw">With MinGW-w64 and GLFW binaries</a></li>
<li class="level2"><a href="#build_link_cmake_source">With CMake and GLFW source</a></li>
<li class="level2"><a href="#build_link_cmake_package">With CMake and installed GLFW binaries</a></li>
<li class="level2"><a href="#build_link_pkgconfig">With pkg-config and GLFW binaries on Unix</a></li>
<li class="level2"><a href="#build_link_xcode">With Xcode on macOS</a></li>
<li class="level2"><a href="#build_link_osx">With command-line or makefile on macOS</a></li>
</ul>
</li>
</ul>
</div>
<div class="textblock"><p>This is about compiling and linking applications that use GLFW. For information on how to write such applications, start with the <a class="el" href="quick_guide.html">introductory tutorial</a>. For information on how to compile the GLFW library itself, see <a class="el" href="compile_guide.html">Compiling GLFW</a>.</p>
<p>This is not a tutorial on compilation or linking. It assumes basic understanding of how to compile and link a C program as well as how to use the specific compiler of your chosen development environment. The compilation and linking process should be explained in your C programming material and in the documentation for your development environment.</p>
<h1><a class="anchor" id="build_include"></a>
Including the GLFW header file</h1>
<p>You should include the GLFW header in the source files where you use OpenGL or GLFW.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3_8h.html">GLFW/glfw3.h</a>&gt;</span></div>
<div class="ttc" id="aglfw3_8h_html"><div class="ttname"><a href="glfw3_8h.html">glfw3.h</a></div><div class="ttdoc">The header of the GLFW 3 API.</div></div>
</div><!-- fragment --><p>This header defines all the constants and declares all the types and function prototypes of the GLFW API. By default, it also includes the OpenGL header from your development environment. See <a class="el" href="build_guide.html#build_macros">option macros</a> below for how to select OpenGL ES headers and more.</p>
<p>The GLFW header also defines any platform-specific macros needed by your OpenGL header, so that it can be included without needing any window system headers.</p>
<p>It does this only when needed, so if window system headers are included, the GLFW header does not try to redefine those symbols. The reverse is not true, i.e. <code>windows.h</code> cannot cope if any Win32 symbols have already been defined.</p>
<p>In other words:</p>
<ul>
<li>Use the GLFW header to include OpenGL or OpenGL ES headers portably</li>
<li>Do not include window system headers unless you will use those APIs directly</li>
<li>If you do need such headers, include them before the GLFW header</li>
</ul>
<p>If you are using an OpenGL extension loading library such as <a href="https://github.com/Dav1dde/glad">glad</a>, the extension loader header should be included before the GLFW one. GLFW attempts to detect any OpenGL or OpenGL ES header or extension loader header included before it and will then disable the inclusion of the default OpenGL header. Most extension loaders also define macros that disable similar headers below it.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#include &lt;glad/gl.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3_8h.html">GLFW/glfw3.h</a>&gt;</span></div>
</div><!-- fragment --><p>Both of these mechanisms depend on the extension loader header defining a known macro. If yours doesn't or you don't know which one your users will pick, the <a class="el" href="build_guide.html#GLFW_INCLUDE_NONE">GLFW_INCLUDE_NONE</a> macro will explicitly prevent the GLFW header from including the OpenGL header. This will also allow you to include the two headers in any order.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#define GLFW_INCLUDE_NONE</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3_8h.html">GLFW/glfw3.h</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;glad/gl.h&gt;</span></div>
</div><!-- fragment --><h2><a class="anchor" id="build_macros"></a>
GLFW header option macros</h2>
<p>These macros may be defined before the inclusion of the GLFW header and affect its behavior.</p>
<p><a class="anchor" id="GLFW_DLL"></a><b>GLFW_DLL</b> is required on Windows when using the GLFW DLL, to tell the compiler that the GLFW functions are defined in a DLL.</p>
<p>The following macros control which OpenGL or OpenGL ES API header is included. Only one of these may be defined at a time.</p>
<dl class="section note"><dt>Note</dt><dd>GLFW does not provide any of the API headers mentioned below. They are provided by your development environment or your OpenGL, OpenGL ES or Vulkan SDK, and most of them can be downloaded from the <a href="https://www.khronos.org/registry/">Khronos Registry</a>.</dd></dl>
<p><a class="anchor" id="GLFW_INCLUDE_GLCOREARB"></a><b>GLFW_INCLUDE_GLCOREARB</b> makes the GLFW header include the modern <code>GL/glcorearb.h</code> header (<code>OpenGL/gl3.h</code> on macOS) instead of the regular OpenGL header.</p>
<p><a class="anchor" id="GLFW_INCLUDE_ES1"></a><b>GLFW_INCLUDE_ES1</b> makes the GLFW header include the OpenGL ES 1.x <code>GLES/gl.h</code> header instead of the regular OpenGL header.</p>
<p><a class="anchor" id="GLFW_INCLUDE_ES2"></a><b>GLFW_INCLUDE_ES2</b> makes the GLFW header include the OpenGL ES 2.0 <code>GLES2/gl2.h</code> header instead of the regular OpenGL header.</p>
<p><a class="anchor" id="GLFW_INCLUDE_ES3"></a><b>GLFW_INCLUDE_ES3</b> makes the GLFW header include the OpenGL ES 3.0 <code>GLES3/gl3.h</code> header instead of the regular OpenGL header.</p>
<p><a class="anchor" id="GLFW_INCLUDE_ES31"></a><b>GLFW_INCLUDE_ES31</b> makes the GLFW header include the OpenGL ES 3.1 <code>GLES3/gl31.h</code> header instead of the regular OpenGL header.</p>
<p><a class="anchor" id="GLFW_INCLUDE_ES32"></a><b>GLFW_INCLUDE_ES32</b> makes the GLFW header include the OpenGL ES 3.2 <code>GLES3/gl32.h</code> header instead of the regular OpenGL header.</p>
<p><a class="anchor" id="GLFW_INCLUDE_NONE"></a><b>GLFW_INCLUDE_NONE</b> makes the GLFW header not include any OpenGL or OpenGL ES API header. This is useful in combination with an extension loading library.</p>
<p>If none of the above inclusion macros are defined, the standard OpenGL <code>GL/gl.h</code> header (<code>OpenGL/gl.h</code> on macOS) is included, unless GLFW detects the inclusion guards of any OpenGL, OpenGL ES or extension loader header it knows about.</p>
<p>The following macros control the inclusion of additional API headers. Any number of these may be defined simultaneously, and/or together with one of the above macros.</p>
<p><a class="anchor" id="GLFW_INCLUDE_VULKAN"></a><b>GLFW_INCLUDE_VULKAN</b> makes the GLFW header include the Vulkan <code>vulkan/vulkan.h</code> header in addition to any selected OpenGL or OpenGL ES header.</p>
<p><a class="anchor" id="GLFW_INCLUDE_GLEXT"></a><b>GLFW_INCLUDE_GLEXT</b> makes the GLFW header include the appropriate extension header for the OpenGL or OpenGL ES header selected above after and in addition to that header.</p>
<p><a class="anchor" id="GLFW_INCLUDE_GLU"></a><b>GLFW_INCLUDE_GLU</b> makes the header include the GLU header in addition to the header selected above. This should only be used with the standard OpenGL header and only for compatibility with legacy code. GLU has been deprecated and should not be used in new code.</p>
<dl class="section note"><dt>Note</dt><dd>None of these macros may be defined during the compilation of GLFW itself. If your build includes GLFW and you define any these in your build files, make sure they are not applied to the GLFW sources.</dd></dl>
<h1><a class="anchor" id="build_link"></a>
Link with the right libraries</h1>
<p>GLFW is essentially a wrapper of various platform-specific APIs and therefore needs to link against many different system libraries. If you are using GLFW as a shared library / dynamic library / DLL then it takes care of these links. However, if you are using GLFW as a static library then your executable will need to link against these libraries.</p>
<p>On Windows and macOS, the list of system libraries is static and can be hard-coded into your build environment. See the section for your development environment below. On Linux and other Unix-like operating systems, the list varies but can be retrieved in various ways as described below.</p>
<p>A good general introduction to linking is <a href="https://www.lurklurk.org/linkers/linkers.html">Beginner's Guide to Linkers</a> by David Drysdale.</p>
<h2><a class="anchor" id="build_link_win32"></a>
With Visual C++ and GLFW binaries</h2>
<p>If you are using a downloaded <a href="https://www.glfw.org/download.html">binary archive</a>, first make sure you have the archive matching the architecture you are building for (32-bit or 64-bit), or you will get link errors. Also make sure you are using the binaries for your version of Visual C++ or you may get other link errors.</p>
<p>There are two version of the static GLFW library in the binary archive, because it needs to use the same base run-time library variant as the rest of your executable.</p>
<p>One is named <code>glfw3.lib</code> and is for projects with the <em>Runtime Library</em> project option set to <em>Multi-threaded DLL</em> or <em>Multi-threaded Debug DLL</em>. The other is named <code>glfw3_mt.lib</code> and is for projects with <em>Runtime Library</em> set to <em>Multi-threaded</em> or <em>Multi-threaded Debug</em>. To use the static GLFW library you will need to add <code>path/to/glfw3.lib</code> or <code>path/to/glfw3_mt.lib</code> to the <em>Additional Dependencies</em> project option.</p>
<p>If you compiled a GLFW static library yourself then there will only be one, named <code>glfw3.lib</code>, and you have to make sure the run-time library variant matches.</p>
<p>The DLL version of the GLFW library is named <code>glfw3.dll</code>, but you will be linking against the <code>glfw3dll.lib</code> link library. To use the DLL you will need to add <code>path/to/glfw3dll.lib</code> to the <em>Additional Dependencies</em> project option. All of its dependencies are already listed there by default, but when building with the DLL version of GLFW, you also need to define the <a class="el" href="build_guide.html#GLFW_DLL">GLFW_DLL</a>. This can be done either in the <em>Preprocessor Definitions</em> project option or by defining it in your source code before including the GLFW header.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#define GLFW_DLL</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3_8h.html">GLFW/glfw3.h</a>&gt;</span></div>
</div><!-- fragment --><p>All link-time dependencies for GLFW are already listed in the <em>Additional Dependencies</em> option by default.</p>
<h2><a class="anchor" id="build_link_mingw"></a>
With MinGW-w64 and GLFW binaries</h2>
<p>This is intended for building a program from the command-line or by writing a makefile, on Windows with <a href="https://www.mingw-w64.org/">MinGW-w64</a> and GLFW binaries. These can be from a downloaded and extracted <a href="https://www.glfw.org/download.html">binary archive</a> or by compiling GLFW yourself. The paths below assume a binary archive is used.</p>
<p>If you are using a downloaded binary archive, first make sure you have the archive matching the architecture you are building for (32-bit or 64-bit) or you will get link errors.</p>
<p>Note that the order of source files and libraries matter for GCC. Dependencies must be listed after the files that depend on them. Any source files that depend on GLFW must be listed before the GLFW library. GLFW in turn depends on <code>gdi32</code> and must be listed before it.</p>
<p>If you are using the static version of the GLFW library, which is named <code>libglfw3.a</code>, do:</p>
<div class="fragment"><div class="line">gcc -o myprog myprog.c -I path/to/glfw/include path/to/glfw/lib-mingw-w64/libglfw3.a -lgdi32</div>
</div><!-- fragment --><p>If you are using the DLL version of the GLFW library, which is named <code>glfw3.dll</code>, you will need to use the <code>libglfw3dll.a</code> link library.</p>
<div class="fragment"><div class="line">gcc -o myprog myprog.c -I path/to/glfw/include path/to/glfw/lib-mingw-w64/libglfw3dll.a -lgdi32</div>
</div><!-- fragment --><p>The resulting executable will need to find <code>glfw3.dll</code> to run, typically by keeping both files in the same directory.</p>
<p>When you are building with the DLL version of GLFW, you will also need to define the <a class="el" href="build_guide.html#GLFW_DLL">GLFW_DLL</a> macro. This can be done in your source files, as long as it done before including the GLFW header:</p>
<div class="fragment"><div class="line"><span class="preprocessor">#define GLFW_DLL</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3_8h.html">GLFW/glfw3.h</a>&gt;</span></div>
</div><!-- fragment --><p>It can also be done on the command-line:</p>
<div class="fragment"><div class="line">gcc -o myprog myprog.c -D GLFW_DLL -I path/to/glfw/include path/to/glfw/lib-mingw-w64/libglfw3dll.a -lgdi32</div>
</div><!-- fragment --><h2><a class="anchor" id="build_link_cmake_source"></a>
With CMake and GLFW source</h2>
<p>This section is about using CMake to compile and link GLFW along with your application. If you want to use an installed binary instead, see <a class="el" href="build_guide.html#build_link_cmake_package">With CMake and installed GLFW binaries</a>.</p>
<p>With a few changes to your <code>CMakeLists.txt</code> you can have the GLFW source tree built along with your application.</p>
<p>Add the root directory of the GLFW source tree to your project. This will add the <code>glfw</code> target to your project.</p>
<div class="fragment"><div class="line">add_subdirectory(path/to/glfw)</div>
</div><!-- fragment --><p>Once GLFW has been added, link your application against the <code>glfw</code> target. This adds the GLFW library and its link-time dependencies as it is currently configured, the include directory for the GLFW header and, when applicable, the <a class="el" href="build_guide.html#GLFW_DLL">GLFW_DLL</a> macro.</p>
<div class="fragment"><div class="line">target_link_libraries(myapp glfw)</div>
</div><!-- fragment --><p>Note that the <code>glfw</code> target does not depend on OpenGL, as GLFW loads any OpenGL, OpenGL ES or Vulkan libraries it needs at runtime. If your application calls OpenGL directly, instead of using a modern <a class="el" href="context_guide.html#context_glext_auto">extension loader library</a>, use the OpenGL CMake package.</p>
<div class="fragment"><div class="line">find_package(OpenGL REQUIRED)</div>
</div><!-- fragment --><p>If OpenGL is found, the <code>OpenGL::GL</code> target is added to your project, containing library and include directory paths. Link against this like any other library.</p>
<div class="fragment"><div class="line">target_link_libraries(myapp OpenGL::GL)</div>
</div><!-- fragment --><p>For a minimal example of a program and GLFW sources built with CMake, see the <a href="https://github.com/juliettef/GLFW-CMake-starter">GLFW CMake Starter</a> on GitHub.</p>
<h2><a class="anchor" id="build_link_cmake_package"></a>
With CMake and installed GLFW binaries</h2>
<p>This section is about using CMake to link GLFW after it has been built and installed. If you want to build it along with your application instead, see <a class="el" href="build_guide.html#build_link_cmake_source">With CMake and GLFW source</a>.</p>
<p>With a few changes to your <code>CMakeLists.txt</code> you can locate the package and target files generated when GLFW is installed.</p>
<div class="fragment"><div class="line">find_package(glfw3 3.4 REQUIRED)</div>
</div><!-- fragment --><p>Once GLFW has been added to the project, link against it with the <code>glfw</code> target. This adds the GLFW library and its link-time dependencies, the include directory for the GLFW header and, when applicable, the <a class="el" href="build_guide.html#GLFW_DLL">GLFW_DLL</a> macro.</p>
<div class="fragment"><div class="line">target_link_libraries(myapp glfw)</div>
</div><!-- fragment --><p>Note that the <code>glfw</code> target does not depend on OpenGL, as GLFW loads any OpenGL, OpenGL ES or Vulkan libraries it needs at runtime. If your application calls OpenGL directly, instead of using a modern <a class="el" href="context_guide.html#context_glext_auto">extension loader library</a>, use the OpenGL CMake package.</p>
<div class="fragment"><div class="line">find_package(OpenGL REQUIRED)</div>
</div><!-- fragment --><p>If OpenGL is found, the <code>OpenGL::GL</code> target is added to your project, containing library and include directory paths. Link against this like any other library.</p>
<div class="fragment"><div class="line">target_link_libraries(myapp OpenGL::GL)</div>
</div><!-- fragment --><h2><a class="anchor" id="build_link_pkgconfig"></a>
With pkg-config and GLFW binaries on Unix</h2>
<p>This is intended for building a program from the command-line or by writing a makefile, on macOS or any Unix-like system like Linux, FreeBSD and Cygwin.</p>
<p>GLFW supports <a href="https://www.freedesktop.org/wiki/Software/pkg-config/">pkg-config</a>, and the <code>glfw3.pc</code> pkg-config file is generated when the GLFW library is built and is installed along with it. A pkg-config file describes all necessary compile-time and link-time flags and dependencies needed to use a library. When they are updated or if they differ between systems, you will get the correct ones automatically.</p>
<p>A typical compile and link command-line when using the static version of the GLFW library may look like this:</p>
<div class="fragment"><div class="line">cc $(pkg-config --cflags glfw3) -o myprog myprog.c $(pkg-config --static --libs glfw3)</div>
</div><!-- fragment --><p>If you are using the shared version of the GLFW library, omit the <code>--static</code> flag.</p>
<div class="fragment"><div class="line">cc $(pkg-config --cflags glfw3) -o myprog myprog.c $(pkg-config --libs glfw3)</div>
</div><!-- fragment --><p>You can also use the <code>glfw3.pc</code> file without installing it first, by using the <code>PKG_CONFIG_PATH</code> environment variable.</p>
<div class="fragment"><div class="line">env PKG_CONFIG_PATH=path/to/glfw/src cc $(pkg-config --cflags glfw3) -o myprog myprog.c $(pkg-config --libs glfw3)</div>
</div><!-- fragment --><p>The dependencies do not include OpenGL, as GLFW loads any OpenGL, OpenGL ES or Vulkan libraries it needs at runtime. If your application calls OpenGL directly, instead of using a modern <a class="el" href="context_guide.html#context_glext_auto">extension loader library</a>, you should add the <code>gl</code> pkg-config package.</p>
<div class="fragment"><div class="line">cc $(pkg-config --cflags glfw3 gl) -o myprog myprog.c $(pkg-config --libs glfw3 gl)</div>
</div><!-- fragment --><h2><a class="anchor" id="build_link_xcode"></a>
With Xcode on macOS</h2>
<p>If you are using the dynamic library version of GLFW, add it to the project dependencies.</p>
<p>If you are using the static library version of GLFW, add it and the Cocoa, OpenGL and IOKit frameworks to the project as dependencies. They can all be found in <code>/System/Library/Frameworks</code>.</p>
<h2><a class="anchor" id="build_link_osx"></a>
With command-line or makefile on macOS</h2>
<p>It is recommended that you use <a class="el" href="build_guide.html#build_link_pkgconfig">pkg-config</a> when using installed GLFW binaries from the command line on macOS. That way you will get any new dependencies added automatically. If you still wish to build manually, you need to add the required frameworks and libraries to your command-line yourself using the <code>-l</code> and <code>-framework</code> switches.</p>
<p>If you are using the dynamic GLFW library, which is named <code>libglfw.3.dylib</code>, do:</p>
<div class="fragment"><div class="line">cc -o myprog myprog.c -lglfw -framework Cocoa -framework OpenGL -framework IOKit</div>
</div><!-- fragment --><p>If you are using the static library, named <code>libglfw3.a</code>, substitute <code>-lglfw3</code> for <code>-lglfw</code>.</p>
<p>Note that you do not add the <code>.framework</code> extension to a framework when linking against it from the command-line.</p>
<dl class="section note"><dt>Note</dt><dd>Your machine may have <code>libGL.*.dylib</code> style OpenGL library, but that is for the X Window System and will not work with the macOS native version of GLFW. </dd></dl>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
