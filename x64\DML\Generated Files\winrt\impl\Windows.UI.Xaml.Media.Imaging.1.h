// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Media_Imaging_1_H
#define WINRT_Windows_UI_Xaml_Media_Imaging_1_H
#include "winrt/impl/Windows.UI.Xaml.Media.Imaging.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Media::Imaging
{
    struct WINRT_IMPL_EMPTY_BASES IBitmapImage :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapImage>
    {
        IBitmapImage(std::nullptr_t = nullptr) noexcept {}
        IBitmapImage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBitmapImage2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapImage2>
    {
        IBitmapImage2(std::nullptr_t = nullptr) noexcept {}
        IBitmapImage2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBitmapImage3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapImage3>
    {
        IBitmapImage3(std::nullptr_t = nullptr) noexcept {}
        IBitmapImage3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBitmapImageFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapImageFactory>
    {
        IBitmapImageFactory(std::nullptr_t = nullptr) noexcept {}
        IBitmapImageFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBitmapImageStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapImageStatics>
    {
        IBitmapImageStatics(std::nullptr_t = nullptr) noexcept {}
        IBitmapImageStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBitmapImageStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapImageStatics2>
    {
        IBitmapImageStatics2(std::nullptr_t = nullptr) noexcept {}
        IBitmapImageStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBitmapImageStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapImageStatics3>
    {
        IBitmapImageStatics3(std::nullptr_t = nullptr) noexcept {}
        IBitmapImageStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBitmapSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapSource>
    {
        IBitmapSource(std::nullptr_t = nullptr) noexcept {}
        IBitmapSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBitmapSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapSourceFactory>
    {
        IBitmapSourceFactory(std::nullptr_t = nullptr) noexcept {}
        IBitmapSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBitmapSourceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapSourceStatics>
    {
        IBitmapSourceStatics(std::nullptr_t = nullptr) noexcept {}
        IBitmapSourceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDownloadProgressEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDownloadProgressEventArgs>
    {
        IDownloadProgressEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDownloadProgressEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRenderTargetBitmap :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRenderTargetBitmap>
    {
        IRenderTargetBitmap(std::nullptr_t = nullptr) noexcept {}
        IRenderTargetBitmap(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRenderTargetBitmapStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRenderTargetBitmapStatics>
    {
        IRenderTargetBitmapStatics(std::nullptr_t = nullptr) noexcept {}
        IRenderTargetBitmapStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISoftwareBitmapSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISoftwareBitmapSource>
    {
        ISoftwareBitmapSource(std::nullptr_t = nullptr) noexcept {}
        ISoftwareBitmapSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISurfaceImageSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISurfaceImageSource>
    {
        ISurfaceImageSource(std::nullptr_t = nullptr) noexcept {}
        ISurfaceImageSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISurfaceImageSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISurfaceImageSourceFactory>
    {
        ISurfaceImageSourceFactory(std::nullptr_t = nullptr) noexcept {}
        ISurfaceImageSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISvgImageSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISvgImageSource>
    {
        ISvgImageSource(std::nullptr_t = nullptr) noexcept {}
        ISvgImageSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISvgImageSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISvgImageSourceFactory>
    {
        ISvgImageSourceFactory(std::nullptr_t = nullptr) noexcept {}
        ISvgImageSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISvgImageSourceFailedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISvgImageSourceFailedEventArgs>
    {
        ISvgImageSourceFailedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISvgImageSourceFailedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISvgImageSourceOpenedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISvgImageSourceOpenedEventArgs>
    {
        ISvgImageSourceOpenedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISvgImageSourceOpenedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISvgImageSourceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISvgImageSourceStatics>
    {
        ISvgImageSourceStatics(std::nullptr_t = nullptr) noexcept {}
        ISvgImageSourceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVirtualSurfaceImageSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVirtualSurfaceImageSource>
    {
        IVirtualSurfaceImageSource(std::nullptr_t = nullptr) noexcept {}
        IVirtualSurfaceImageSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVirtualSurfaceImageSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVirtualSurfaceImageSourceFactory>
    {
        IVirtualSurfaceImageSourceFactory(std::nullptr_t = nullptr) noexcept {}
        IVirtualSurfaceImageSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWriteableBitmap :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWriteableBitmap>
    {
        IWriteableBitmap(std::nullptr_t = nullptr) noexcept {}
        IWriteableBitmap(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWriteableBitmapFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWriteableBitmapFactory>
    {
        IWriteableBitmapFactory(std::nullptr_t = nullptr) noexcept {}
        IWriteableBitmapFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlRenderingBackgroundTask :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlRenderingBackgroundTask>
    {
        IXamlRenderingBackgroundTask(std::nullptr_t = nullptr) noexcept {}
        IXamlRenderingBackgroundTask(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlRenderingBackgroundTaskFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlRenderingBackgroundTaskFactory>
    {
        IXamlRenderingBackgroundTaskFactory(std::nullptr_t = nullptr) noexcept {}
        IXamlRenderingBackgroundTaskFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlRenderingBackgroundTaskOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlRenderingBackgroundTaskOverrides>
    {
        IXamlRenderingBackgroundTaskOverrides(std::nullptr_t = nullptr) noexcept {}
        IXamlRenderingBackgroundTaskOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
