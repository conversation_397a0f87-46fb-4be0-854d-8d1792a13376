﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\mathfuncs_core.avx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\src\corner.avx.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\accum.avx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\layers\layers_common.avx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\layers\cpu_kernels\conv_block.avx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\layers\cpu_kernels\conv_depthwise.avx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\layers\cpu_kernels\conv_winograd_f63.avx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\layers\cpu_kernels\fast_gemm_kernels.avx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{2A318434-2169-3E81-A653-C1C0490B5893}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_imgproc">
      <UniqueIdentifier>{FA23004A-22DB-30E6-B8FD-718F497FB629}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_imgproc\Src">
      <UniqueIdentifier>{7A7ACE0E-D197-310E-909D-212CE8311A24}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
