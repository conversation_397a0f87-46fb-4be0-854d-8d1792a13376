<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Gamepad buttons</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle"><div class="title">Gamepad buttons<div class="ingroups"><a class="el" href="group__input.html">Input reference</a></div></div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>See <a class="el" href="input_guide.html#gamepad">Gamepad input</a> for how these are used. </p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:gae055a12fbf4b48b5954c8e1cd129b810" id="r_gae055a12fbf4b48b5954c8e1cd129b810"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gae055a12fbf4b48b5954c8e1cd129b810">GLFW_GAMEPAD_BUTTON_A</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:gae055a12fbf4b48b5954c8e1cd129b810"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2228a6512fd5950cdb51ba07846546fa" id="r_ga2228a6512fd5950cdb51ba07846546fa"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga2228a6512fd5950cdb51ba07846546fa">GLFW_GAMEPAD_BUTTON_B</a>&#160;&#160;&#160;1</td></tr>
<tr class="separator:ga2228a6512fd5950cdb51ba07846546fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga52cc94785cf3fe9a12e246539259887c" id="r_ga52cc94785cf3fe9a12e246539259887c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga52cc94785cf3fe9a12e246539259887c">GLFW_GAMEPAD_BUTTON_X</a>&#160;&#160;&#160;2</td></tr>
<tr class="separator:ga52cc94785cf3fe9a12e246539259887c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafc931248bda494b530cbe057f386a5ed" id="r_gafc931248bda494b530cbe057f386a5ed"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gafc931248bda494b530cbe057f386a5ed">GLFW_GAMEPAD_BUTTON_Y</a>&#160;&#160;&#160;3</td></tr>
<tr class="separator:gafc931248bda494b530cbe057f386a5ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga17d67b4f39a39d6b813bd1567a3507c3" id="r_ga17d67b4f39a39d6b813bd1567a3507c3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga17d67b4f39a39d6b813bd1567a3507c3">GLFW_GAMEPAD_BUTTON_LEFT_BUMPER</a>&#160;&#160;&#160;4</td></tr>
<tr class="separator:ga17d67b4f39a39d6b813bd1567a3507c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadfbc9ea9bf3aae896b79fa49fdc85c7f" id="r_gadfbc9ea9bf3aae896b79fa49fdc85c7f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gadfbc9ea9bf3aae896b79fa49fdc85c7f">GLFW_GAMEPAD_BUTTON_RIGHT_BUMPER</a>&#160;&#160;&#160;5</td></tr>
<tr class="separator:gadfbc9ea9bf3aae896b79fa49fdc85c7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabc7c0264ce778835b516a472b47f6caf" id="r_gabc7c0264ce778835b516a472b47f6caf"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gabc7c0264ce778835b516a472b47f6caf">GLFW_GAMEPAD_BUTTON_BACK</a>&#160;&#160;&#160;6</td></tr>
<tr class="separator:gabc7c0264ce778835b516a472b47f6caf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga04606949dd9139434b8a1bedf4ac1021" id="r_ga04606949dd9139434b8a1bedf4ac1021"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga04606949dd9139434b8a1bedf4ac1021">GLFW_GAMEPAD_BUTTON_START</a>&#160;&#160;&#160;7</td></tr>
<tr class="separator:ga04606949dd9139434b8a1bedf4ac1021"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7fa48c32e5b2f5db2f080aa0b8b573dc" id="r_ga7fa48c32e5b2f5db2f080aa0b8b573dc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga7fa48c32e5b2f5db2f080aa0b8b573dc">GLFW_GAMEPAD_BUTTON_GUIDE</a>&#160;&#160;&#160;8</td></tr>
<tr class="separator:ga7fa48c32e5b2f5db2f080aa0b8b573dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3e089787327454f7bfca7364d6ca206a" id="r_ga3e089787327454f7bfca7364d6ca206a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga3e089787327454f7bfca7364d6ca206a">GLFW_GAMEPAD_BUTTON_LEFT_THUMB</a>&#160;&#160;&#160;9</td></tr>
<tr class="separator:ga3e089787327454f7bfca7364d6ca206a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1c003f52b5aebb45272475b48953b21a" id="r_ga1c003f52b5aebb45272475b48953b21a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga1c003f52b5aebb45272475b48953b21a">GLFW_GAMEPAD_BUTTON_RIGHT_THUMB</a>&#160;&#160;&#160;10</td></tr>
<tr class="separator:ga1c003f52b5aebb45272475b48953b21a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4f1ed6f974a47bc8930d4874a283476a" id="r_ga4f1ed6f974a47bc8930d4874a283476a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga4f1ed6f974a47bc8930d4874a283476a">GLFW_GAMEPAD_BUTTON_DPAD_UP</a>&#160;&#160;&#160;11</td></tr>
<tr class="separator:ga4f1ed6f974a47bc8930d4874a283476a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae2a780d2a8c79e0b77c0b7b601ca57c6" id="r_gae2a780d2a8c79e0b77c0b7b601ca57c6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gae2a780d2a8c79e0b77c0b7b601ca57c6">GLFW_GAMEPAD_BUTTON_DPAD_RIGHT</a>&#160;&#160;&#160;12</td></tr>
<tr class="separator:gae2a780d2a8c79e0b77c0b7b601ca57c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8f2b731b97d80f90f11967a83207665c" id="r_ga8f2b731b97d80f90f11967a83207665c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga8f2b731b97d80f90f11967a83207665c">GLFW_GAMEPAD_BUTTON_DPAD_DOWN</a>&#160;&#160;&#160;13</td></tr>
<tr class="separator:ga8f2b731b97d80f90f11967a83207665c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf0697e0e8607b2ebe1c93b0c6befe301" id="r_gaf0697e0e8607b2ebe1c93b0c6befe301"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gaf0697e0e8607b2ebe1c93b0c6befe301">GLFW_GAMEPAD_BUTTON_DPAD_LEFT</a>&#160;&#160;&#160;14</td></tr>
<tr class="separator:gaf0697e0e8607b2ebe1c93b0c6befe301"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5cc98882f4f81dacf761639a567f61eb" id="r_ga5cc98882f4f81dacf761639a567f61eb"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga5cc98882f4f81dacf761639a567f61eb">GLFW_GAMEPAD_BUTTON_LAST</a>&#160;&#160;&#160;<a class="el" href="group__gamepad__buttons.html#gaf0697e0e8607b2ebe1c93b0c6befe301">GLFW_GAMEPAD_BUTTON_DPAD_LEFT</a></td></tr>
<tr class="separator:ga5cc98882f4f81dacf761639a567f61eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf08d0df26527c9305253422bd98ed63a" id="r_gaf08d0df26527c9305253422bd98ed63a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gaf08d0df26527c9305253422bd98ed63a">GLFW_GAMEPAD_BUTTON_CROSS</a>&#160;&#160;&#160;<a class="el" href="group__gamepad__buttons.html#gae055a12fbf4b48b5954c8e1cd129b810">GLFW_GAMEPAD_BUTTON_A</a></td></tr>
<tr class="separator:gaf08d0df26527c9305253422bd98ed63a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaef094b3dacbf15f272b274516839b82" id="r_gaaef094b3dacbf15f272b274516839b82"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gaaef094b3dacbf15f272b274516839b82">GLFW_GAMEPAD_BUTTON_CIRCLE</a>&#160;&#160;&#160;<a class="el" href="group__gamepad__buttons.html#ga2228a6512fd5950cdb51ba07846546fa">GLFW_GAMEPAD_BUTTON_B</a></td></tr>
<tr class="separator:gaaef094b3dacbf15f272b274516839b82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafc7821e87d77d41ed2cd3e1f726ec35f" id="r_gafc7821e87d77d41ed2cd3e1f726ec35f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gafc7821e87d77d41ed2cd3e1f726ec35f">GLFW_GAMEPAD_BUTTON_SQUARE</a>&#160;&#160;&#160;<a class="el" href="group__gamepad__buttons.html#ga52cc94785cf3fe9a12e246539259887c">GLFW_GAMEPAD_BUTTON_X</a></td></tr>
<tr class="separator:gafc7821e87d77d41ed2cd3e1f726ec35f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3a7ef6bcb768a08cd3bf142f7f09f802" id="r_ga3a7ef6bcb768a08cd3bf142f7f09f802"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga3a7ef6bcb768a08cd3bf142f7f09f802">GLFW_GAMEPAD_BUTTON_TRIANGLE</a>&#160;&#160;&#160;<a class="el" href="group__gamepad__buttons.html#gafc931248bda494b530cbe057f386a5ed">GLFW_GAMEPAD_BUTTON_Y</a></td></tr>
<tr class="separator:ga3a7ef6bcb768a08cd3bf142f7f09f802"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="gae055a12fbf4b48b5954c8e1cd129b810" name="gae055a12fbf4b48b5954c8e1cd129b810"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae055a12fbf4b48b5954c8e1cd129b810">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_A</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_A&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga2228a6512fd5950cdb51ba07846546fa" name="ga2228a6512fd5950cdb51ba07846546fa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2228a6512fd5950cdb51ba07846546fa">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_B</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_B&#160;&#160;&#160;1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga52cc94785cf3fe9a12e246539259887c" name="ga52cc94785cf3fe9a12e246539259887c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga52cc94785cf3fe9a12e246539259887c">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_X</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_X&#160;&#160;&#160;2</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gafc931248bda494b530cbe057f386a5ed" name="gafc931248bda494b530cbe057f386a5ed"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafc931248bda494b530cbe057f386a5ed">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_Y</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_Y&#160;&#160;&#160;3</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga17d67b4f39a39d6b813bd1567a3507c3" name="ga17d67b4f39a39d6b813bd1567a3507c3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga17d67b4f39a39d6b813bd1567a3507c3">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_LEFT_BUMPER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_LEFT_BUMPER&#160;&#160;&#160;4</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gadfbc9ea9bf3aae896b79fa49fdc85c7f" name="gadfbc9ea9bf3aae896b79fa49fdc85c7f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadfbc9ea9bf3aae896b79fa49fdc85c7f">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_RIGHT_BUMPER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_RIGHT_BUMPER&#160;&#160;&#160;5</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gabc7c0264ce778835b516a472b47f6caf" name="gabc7c0264ce778835b516a472b47f6caf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gabc7c0264ce778835b516a472b47f6caf">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_BACK</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_BACK&#160;&#160;&#160;6</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga04606949dd9139434b8a1bedf4ac1021" name="ga04606949dd9139434b8a1bedf4ac1021"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga04606949dd9139434b8a1bedf4ac1021">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_START</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_START&#160;&#160;&#160;7</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga7fa48c32e5b2f5db2f080aa0b8b573dc" name="ga7fa48c32e5b2f5db2f080aa0b8b573dc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7fa48c32e5b2f5db2f080aa0b8b573dc">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_GUIDE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_GUIDE&#160;&#160;&#160;8</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga3e089787327454f7bfca7364d6ca206a" name="ga3e089787327454f7bfca7364d6ca206a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3e089787327454f7bfca7364d6ca206a">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_LEFT_THUMB</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_LEFT_THUMB&#160;&#160;&#160;9</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga1c003f52b5aebb45272475b48953b21a" name="ga1c003f52b5aebb45272475b48953b21a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1c003f52b5aebb45272475b48953b21a">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_RIGHT_THUMB</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_RIGHT_THUMB&#160;&#160;&#160;10</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga4f1ed6f974a47bc8930d4874a283476a" name="ga4f1ed6f974a47bc8930d4874a283476a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4f1ed6f974a47bc8930d4874a283476a">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_DPAD_UP</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_DPAD_UP&#160;&#160;&#160;11</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gae2a780d2a8c79e0b77c0b7b601ca57c6" name="gae2a780d2a8c79e0b77c0b7b601ca57c6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae2a780d2a8c79e0b77c0b7b601ca57c6">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_DPAD_RIGHT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_DPAD_RIGHT&#160;&#160;&#160;12</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga8f2b731b97d80f90f11967a83207665c" name="ga8f2b731b97d80f90f11967a83207665c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8f2b731b97d80f90f11967a83207665c">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_DPAD_DOWN</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_DPAD_DOWN&#160;&#160;&#160;13</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaf0697e0e8607b2ebe1c93b0c6befe301" name="gaf0697e0e8607b2ebe1c93b0c6befe301"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf0697e0e8607b2ebe1c93b0c6befe301">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_DPAD_LEFT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_DPAD_LEFT&#160;&#160;&#160;14</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga5cc98882f4f81dacf761639a567f61eb" name="ga5cc98882f4f81dacf761639a567f61eb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5cc98882f4f81dacf761639a567f61eb">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_LAST</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_LAST&#160;&#160;&#160;<a class="el" href="group__gamepad__buttons.html#gaf0697e0e8607b2ebe1c93b0c6befe301">GLFW_GAMEPAD_BUTTON_DPAD_LEFT</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaf08d0df26527c9305253422bd98ed63a" name="gaf08d0df26527c9305253422bd98ed63a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf08d0df26527c9305253422bd98ed63a">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_CROSS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_CROSS&#160;&#160;&#160;<a class="el" href="group__gamepad__buttons.html#gae055a12fbf4b48b5954c8e1cd129b810">GLFW_GAMEPAD_BUTTON_A</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaaef094b3dacbf15f272b274516839b82" name="gaaef094b3dacbf15f272b274516839b82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaaef094b3dacbf15f272b274516839b82">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_CIRCLE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_CIRCLE&#160;&#160;&#160;<a class="el" href="group__gamepad__buttons.html#ga2228a6512fd5950cdb51ba07846546fa">GLFW_GAMEPAD_BUTTON_B</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gafc7821e87d77d41ed2cd3e1f726ec35f" name="gafc7821e87d77d41ed2cd3e1f726ec35f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafc7821e87d77d41ed2cd3e1f726ec35f">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_SQUARE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_SQUARE&#160;&#160;&#160;<a class="el" href="group__gamepad__buttons.html#ga52cc94785cf3fe9a12e246539259887c">GLFW_GAMEPAD_BUTTON_X</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga3a7ef6bcb768a08cd3bf142f7f09f802" name="ga3a7ef6bcb768a08cd3bf142f7f09f802"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3a7ef6bcb768a08cd3bf142f7f09f802">&#9670;&#160;</a></span>GLFW_GAMEPAD_BUTTON_TRIANGLE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_BUTTON_TRIANGLE&#160;&#160;&#160;<a class="el" href="group__gamepad__buttons.html#gafc931248bda494b530cbe057f386a5ed">GLFW_GAMEPAD_BUTTON_Y</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
