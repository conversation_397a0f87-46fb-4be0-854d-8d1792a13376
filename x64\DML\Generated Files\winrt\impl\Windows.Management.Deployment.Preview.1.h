// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Management_Deployment_Preview_1_H
#define WINRT_Windows_Management_Deployment_Preview_1_H
#include "winrt/impl/Windows.Management.Deployment.Preview.0.h"
WINRT_EXPORT namespace winrt::Windows::Management::Deployment::Preview
{
    struct WINRT_IMPL_EMPTY_BASES IClassicAppManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IClassicAppManagerStatics>
    {
        IClassicAppManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IClassicAppManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInstalledClassicAppInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInstalledClassicAppInfo>
    {
        IInstalledClassicAppInfo(std::nullptr_t = nullptr) noexcept {}
        IInstalledClassicAppInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
