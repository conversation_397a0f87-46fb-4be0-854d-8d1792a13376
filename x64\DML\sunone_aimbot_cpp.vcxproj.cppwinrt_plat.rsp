-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.Actions.ActionsContract\*******\Windows.AI.Actions.ActionsContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.MachineLearning.MachineLearningContract\*******\Windows.AI.MachineLearning.MachineLearningContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract\*******\Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.ModelContextProtocol.ModelContextProtocolContract\*******\Windows.AI.ModelContextProtocol.ModelContextProtocolContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ActivatedEventsContract\*******\Windows.ApplicationModel.Activation.ActivatedEventsContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ActivationCameraSettingsContract\*******\Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ContactActivatedEventsContract\*******\Windows.ApplicationModel.Activation.ContactActivatedEventsContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract\*******\Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract\*******\Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.Background.CallsBackgroundContract\*******\Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.CallsPhoneContract\*******\Windows.ApplicationModel.Calls.CallsPhoneContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.CallsVoipContract\*******\Windows.ApplicationModel.Calls.CallsVoipContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.LockScreenCallContract\*******\Windows.ApplicationModel.Calls.LockScreenCallContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract\*******\Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.FullTrustAppContract\*******\Windows.ApplicationModel.FullTrustAppContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract\*******\Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Preview.Notes.PreviewNotesContract\*******\Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Resources.Management.ResourceIndexerContract\*******\Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Search.Core.SearchCoreContract\*******\Windows.ApplicationModel.Search.Core.SearchCoreContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Search.SearchContract\*******\Windows.ApplicationModel.Search.SearchContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.SocialInfo.SocialInfoContract\*******\Windows.ApplicationModel.SocialInfo.SocialInfoContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.StartupTaskContract\3.0.0.0\Windows.ApplicationModel.StartupTaskContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Wallet.WalletContract\*******\Windows.ApplicationModel.Wallet.WalletContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Custom.CustomDeviceContract\*******\Windows.Devices.Custom.CustomDeviceContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.DevicesLowLevelContract\3.0.0.0\Windows.Devices.DevicesLowLevelContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Portable.PortableDeviceContract\*******\Windows.Devices.Portable.PortableDeviceContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Power.PowerGridApiContract\*******\Windows.Devices.Power.PowerGridApiContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Printers.Extensions.ExtensionsContract\*******\Windows.Devices.Printers.Extensions.ExtensionsContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Printers.PrintersContract\*******\Windows.Devices.Printers.PrintersContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Scanners.ScannerDeviceContract\*******\Windows.Devices.Scanners.ScannerDeviceContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract\3.0.0.0\Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.SmartCards.SmartCardEmulatorContract\6.0.0.0\Windows.Devices.SmartCards.SmartCardEmulatorContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Sms.LegacySmsApiContract\*******\Windows.Devices.Sms.LegacySmsApiContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Foundation.FoundationContract\*******\Windows.Foundation.FoundationContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Foundation.UniversalApiContract\19.0.0.0\Windows.Foundation.UniversalApiContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.Input.GamingInputPreviewContract\*******\Windows.Gaming.Input.GamingInputPreviewContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.Preview.GamesEnumerationContract\*******\Windows.Gaming.Preview.GamesEnumerationContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.UI.GameChatOverlayContract\*******\Windows.Gaming.UI.GameChatOverlayContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.UI.GamingUIProviderContract\*******\Windows.Gaming.UI.GamingUIProviderContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.XboxLive.StorageApiContract\*******\Windows.Gaming.XboxLive.StorageApiContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract\*******\Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Graphics.Printing3D.Printing3DContract\*******\Windows.Graphics.Printing3D.Printing3DContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Deployment.Preview.DeploymentPreviewContract\*******\Windows.Management.Deployment.Preview.DeploymentPreviewContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Deployment.SharedPackageContainerContract\*******\Windows.Management.Deployment.SharedPackageContainerContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Update.WindowsUpdateContract\*******\Windows.Management.Update.WindowsUpdateContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Workplace.WorkplaceSettingsContract\*******\Windows.Management.Workplace.WorkplaceSettingsContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.AppBroadcasting.AppBroadcastingContract\*******\Windows.Media.AppBroadcasting.AppBroadcastingContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.AppRecording.AppRecordingContract\*******\Windows.Media.AppRecording.AppRecordingContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppBroadcastContract\*******\Windows.Media.Capture.AppBroadcastContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppCaptureContract\*******\Windows.Media.Capture.AppCaptureContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppCaptureMetadataContract\*******\Windows.Media.Capture.AppCaptureMetadataContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.CameraCaptureUIContract\*******\Windows.Media.Capture.CameraCaptureUIContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.GameBarContract\*******\Windows.Media.Capture.GameBarContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Devices.CallControlContract\*******\Windows.Media.Devices.CallControlContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.MediaControlContract\*******\Windows.Media.MediaControlContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Playlists.PlaylistsContract\*******\Windows.Media.Playlists.PlaylistsContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Protection.ProtectionRenewalContract\*******\Windows.Media.Protection.ProtectionRenewalContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.Connectivity.WwanContract\3.0.0.0\Windows.Networking.Connectivity.WwanContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract\*******\Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.Sockets.ControlChannelTriggerContract\3.0.0.0\Windows.Networking.Sockets.ControlChannelTriggerContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.XboxLive.XboxLiveSecureSocketsContract\*******\Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Perception.Automation.Core.PerceptionAutomationCoreContract\*******\Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.EnterpriseData.EnterpriseDataContract\*******\Windows.Security.EnterpriseData.EnterpriseDataContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.ExchangeActiveSyncProvisioning.EasContract\*******\Windows.Security.ExchangeActiveSyncProvisioning.EasContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.Isolation.Isolatedwindowsenvironmentcontract\*******\Windows.Security.Isolation.Isolatedwindowsenvironmentcontract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Maps.GuidanceContract\3.0.0.0\Windows.Services.Maps.GuidanceContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Maps.LocalSearchContract\*******\Windows.Services.Maps.LocalSearchContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Store.StoreContract\*******\Windows.Services.Store.StoreContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.TargetedContent.TargetedContentContract\*******\Windows.Services.TargetedContent.TargetedContentContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Storage.Provider.CloudFilesContract\*******\Windows.Storage.Provider.CloudFilesContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\windows.system.profile.platformautomaticappsignincontract\*******\windows.system.profile.platformautomaticappsignincontract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileHardwareTokenContract\*******\Windows.System.Profile.ProfileHardwareTokenContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileRetailInfoContract\*******\Windows.System.Profile.ProfileRetailInfoContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileSharedModeContract\*******\Windows.System.Profile.ProfileSharedModeContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.SystemManufacturers.SystemManufacturersContract\3.0.0.0\Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.SystemManagementContract\*******\Windows.System.SystemManagementContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.UserProfile.UserProfileContract\*******\Windows.System.UserProfile.UserProfileContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.UserProfile.UserProfileLockScreenContract\*******\Windows.System.UserProfile.UserProfileLockScreenContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.ApplicationSettings.ApplicationsSettingsContract\*******\Windows.UI.ApplicationSettings.ApplicationsSettingsContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Core.AnimationMetrics.AnimationMetricsContract\*******\Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Core.CoreWindowDialogsContract\*******\Windows.UI.Core.CoreWindowDialogsContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Shell.SecurityAppManagerContract\*******\Windows.UI.Shell.SecurityAppManagerContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Shell.WindowTabManagerContract\*******\Windows.UI.Shell.WindowTabManagerContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.UIAutomation.UIAutomationContract\*******\Windows.UI.UIAutomation.UIAutomationContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.ViewManagement.ViewManagementViewScalingContract\*******\Windows.UI.ViewManagement.ViewManagementViewScalingContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Xaml.Core.Direct.XamlDirectContract\*******\Windows.UI.Xaml.Core.Direct.XamlDirectContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Xaml.Hosting.HostingContract\*******\Windows.UI.Xaml.Hosting.HostingContract.winmd"
-in "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Web.Http.Diagnostics.HttpDiagnosticsContract\*******\Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.winmd" -out "x64\DML\Generated Files\."
