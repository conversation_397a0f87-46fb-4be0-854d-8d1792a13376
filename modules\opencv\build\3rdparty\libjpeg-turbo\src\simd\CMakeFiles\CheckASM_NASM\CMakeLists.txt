cmake_minimum_required(VERSION 4.1.0-rc3)
set(CMAKE_MODULE_PATH "")
set(_CMAKE_CHECK_ENABLED_LANGUAGES "C;CXX;RC")
set(CMAKE_C_COMPILER "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe")
set(CMAKE_C_COMPILER_ID "MSVC")
set(CMAKE_C_COMPILER_LOADED 1)
set(CMAKE_C_COMPILER_VERSION "19.44.35213.0")
set(CMAKE_CXX_COMPILER "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe")
set(CMAKE_CXX_COMPILER_ID "MSVC")
set(CMAKE_CXX_COMPILER_LOADED 1)
set(CMAKE_CXX_COMPILER_VERSION "19.44.35213.0")
set(CMAKE_RC_COMPILER "rc")
set(CMAKE_RC_COMPILER_ID "")
set(CMAKE_RC_COMPILER_LOADED 1)
set(CMAKE_RC_COMPILER_VERSION "")

project(CheckASM_NASM LANGUAGES ASM_NASM)
file(WRITE "${CMAKE_CURRENT_BINARY_DIR}/result.cmake" "set(CMAKE_ASM_NASM_COMPILER \"${CMAKE_ASM_NASM_COMPILER}\")
")