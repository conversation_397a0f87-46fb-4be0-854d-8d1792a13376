//clblasCaxpy
//clblasCcopy
//clblasCdotc
//clblasCdotu
//clblasCgbmv
clblasCgemm
//clblasCgemv
//clblasCgerc
//clblasCgeru
//clblasChbmv
//clblasChemm
//clblasChemv
//clblasCher
//clblasCher2
//clblasCher2k
//clblasCherk
//clblasChpmv
//clblasChpr
//clblasChpr2
//clblasCrotg
//clblasCscal
//clblasCsrot
//clblasCsscal
//clblasCswap
//clblasCsymm
//clblasCsyr2k
//clblasCsyrk
//clblasCtbmv
//clblasCtbsv
//clblasCtpmv
//clblasCtpsv
//clblasCtrmm
//clblasCtrmv
//clblasCtrsm
//clblasCtrsv
//clblasDasum
//clblasDaxpy
//clblasDcopy
//clblasDdot
//clblasDgbmv
clblasDgemm
//clblasDgemv
//clblasDger
//clblasDnrm2
//clblasDrot
//clblasDrotg
//clblasDrotm
//clblasDrotmg
//clblasDsbmv
//clblasDscal
//clblasDspmv
//clblasDspr
//clblasDspr2
//clblasDswap
//clblasDsymm
//clblasDsymv
//clblasDsyr
//clblasDsyr2
//clblasDsyr2k
//clblasDsyrk
//clblasDtbmv
//clblasDtbsv
//clblasDtpmv
//clblasDtpsv
//clblasDtrmm
//clblasDtrmv
//clblasDtrsm
//clblasDtrsv
//clblasDzasum
//clblasDznrm2
//clblasGetVersion
//clblasSasum
//clblasSaxpy
//clblasScasum
//clblasScnrm2
//clblasScopy
//clblasSdot
clblasSetup
//clblasSgbmv
clblasSgemm
//clblasSgemv
//clblasSger
//clblasSnrm2
//clblasSrot
//clblasSrotg
//clblasSrotm
//clblasSrotmg
//clblasSsbmv
//clblasSscal
//clblasSspmv
//clblasSspr
//clblasSspr2
//clblasSswap
//clblasSsymm
//clblasSsymv
//clblasSsyr
//clblasSsyr2
//clblasSsyr2k
//clblasSsyrk
//clblasStbmv
//clblasStbsv
//clblasStpmv
//clblasStpsv
//clblasStrmm
//clblasStrmv
//clblasStrsm
//clblasStrsv
clblasTeardown
//clblasZaxpy
//clblasZcopy
//clblasZdotc
//clblasZdotu
//clblasZdrot
//clblasZdscal
//clblasZgbmv
clblasZgemm
//clblasZgemv
//clblasZgerc
//clblasZgeru
//clblasZhbmv
//clblasZhemm
//clblasZhemv
//clblasZher
//clblasZher2
//clblasZher2k
//clblasZherk
//clblasZhpmv
//clblasZhpr
//clblasZhpr2
//clblasZrotg
//clblasZscal
//clblasZswap
//clblasZsymm
//clblasZsyr2k
//clblasZsyrk
//clblasZtbmv
//clblasZtbsv
//clblasZtpmv
//clblasZtpsv
//clblasZtrmm
//clblasZtrmv
//clblasZtrsm
//clblasZtrsv
//clblasiCamax
//clblasiDamax
//clblasiSamax
//clblasiZamax
#total 147
