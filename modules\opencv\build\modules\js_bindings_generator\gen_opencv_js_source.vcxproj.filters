﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="D:\AI\opencv\cudabuild\CMakeFiles\3e01e28ea25ca1305f9a03ec4c8891c5\bindings.cpp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\AI\opencv\cudabuild\CMakeFiles\77872ca115049222fae6cfb40204c4c2\gen_opencv_js_source.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\AI\opencv\opencv-4.10.0\modules\js\src\core_bindings.cpp">
      <Filter>Source Files</Filter>
    </None>
    <None Include="D:\AI\opencv\opencv-4.10.0\modules\js\generator\embindgen.py" />
    <None Include="D:\AI\opencv\opencv-4.10.0\modules\js\generator\templates.py" />
    <None Include="D:\AI\opencv\cudabuild\modules\js_bindings_generator\CMakeFiles\gen_opencv_js_source" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{D15E165F-1928-31B6-B5AD-589C9FB01FC7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{2A318434-2169-3E81-A653-C1C0490B5893}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
