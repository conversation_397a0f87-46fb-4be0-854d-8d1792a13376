framework module opencv2 {
  umbrella header "opencv2.h"
  header "ArrayUtil.h"
  header "ByteVector.h"
  header "Converters.h"
  header "CVObjcUtil.h"
  header "CvType.h"
  header "DMatch.h"
  header "Double2.h"
  header "Double3.h"
  header "DoubleVector.h"
  header "Float4.h"
  header "Float6.h"
  header "FloatVector.h"
  header "Int4.h"
  header "IntVector.h"
  header "KeyPoint.h"
  header "Mat.h"
  header "MatOfByte.h"
  header "MatOfDMatch.h"
  header "MatOfDouble.h"
  header "MatOfFloat.h"
  header "MatOfFloat4.h"
  header "MatOfFloat6.h"
  header "MatOfInt.h"
  header "MatOfInt4.h"
  header "MatOfKeyPoint.h"
  header "MatOfPoint2f.h"
  header "MatOfPoint2i.h"
  header "MatOfPoint3.h"
  header "MatOfPoint3f.h"
  header "MatOfRect2d.h"
  header "MatOfRect2i.h"
  header "MatOfRotatedRect.h"
  header "MinMaxLocResult.h"
  header "Point2d.h"
  header "Point2f.h"
  header "Point2i.h"
  header "Point3d.h"
  header "Point3f.h"
  header "Point3i.h"
  header "Range.h"
  header "Rect2d.h"
  header "Rect2f.h"
  header "Rect2i.h"
  header "RotatedRect.h"
  header "Scalar.h"
  header "Size2d.h"
  header "Size2f.h"
  header "Size2i.h"
  header "TermCriteria.h"
  header "Core.h"
  header "Algorithm.h"
  header "TickMeter.h"
  header "Moments.h"
  header "Imgproc.h"
  header "CLAHE.h"
  header "GeneralizedHough.h"
  header "GeneralizedHoughBallard.h"
  header "GeneralizedHoughGuil.h"
  header "LineSegmentDetector.h"
  header "Subdiv2D.h"
  header "IntelligentScissorsMB.h"
  header "Ml.h"
  header "ANN_MLP.h"
  header "Boost.h"
  header "DTrees.h"
  header "EM.h"
  header "KNearest.h"
  header "LogisticRegression.h"
  header "NormalBayesClassifier.h"
  header "ParamGrid.h"
  header "RTrees.h"
  header "SVM.h"
  header "SVMSGD.h"
  header "StatModel.h"
  header "TrainData.h"
  header "Phase_unwrapping.h"
  header "HistogramPhaseUnwrapping.h"
  header "HistogramPhaseUnwrappingParams.h"
  header "PhaseUnwrapping.h"
  header "Plot.h"
  header "Plot2d.h"
  header "Dnn.h"
  header "ClassificationModel.h"
  header "DetectionModel.h"
  header "DictValue.h"
  header "Image2BlobParams.h"
  header "KeypointsModel.h"
  header "Layer.h"
  header "Model.h"
  header "Net.h"
  header "SegmentationModel.h"
  header "TextDetectionModel.h"
  header "TextDetectionModel_DB.h"
  header "TextDetectionModel_EAST.h"
  header "TextRecognitionModel.h"
  header "Features2d.h"
  header "AKAZE.h"
  header "AffineFeature.h"
  header "AgastFeatureDetector.h"
  header "BFMatcher.h"
  header "BOWImgDescriptorExtractor.h"
  header "BOWKMeansTrainer.h"
  header "BOWTrainer.h"
  header "BRISK.h"
  header "DescriptorMatcher.h"
  header "FastFeatureDetector.h"
  header "Feature2D.h"
  header "FlannBasedMatcher.h"
  header "GFTTDetector.h"
  header "KAZE.h"
  header "MSER.h"
  header "ORB.h"
  header "SIFT.h"
  header "SimpleBlobDetector.h"
  header "SimpleBlobDetectorParams.h"
  header "MatConverters.h"
  header "MatQuickLook.h"
  header "Imgcodecs.h"
  header "Photo.h"
  header "AlignExposures.h"
  header "AlignMTB.h"
  header "CalibrateCRF.h"
  header "CalibrateDebevec.h"
  header "CalibrateRobertson.h"
  header "MergeDebevec.h"
  header "MergeExposures.h"
  header "MergeMertens.h"
  header "MergeRobertson.h"
  header "Tonemap.h"
  header "TonemapDrago.h"
  header "TonemapMantiuk.h"
  header "TonemapReinhard.h"
  header "Text.h"
  header "BaseOCR.h"
  header "ERFilter.h"
  header "ERFilterCallback.h"
  header "OCRBeamSearchDecoder.h"
  header "OCRBeamSearchDecoderClassifierCallback.h"
  header "OCRHMMDecoder.h"
  header "OCRHMMDecoderClassifierCallback.h"
  header "OCRTesseract.h"
  header "TextDetector.h"
  header "TextDetectorCNN.h"
  header "Videoio.h"
  header "VideoCapture.h"
  header "VideoWriter.h"
  header "Xphoto.h"
  header "GrayworldWB.h"
  header "LearningBasedWB.h"
  header "SimpleWB.h"
  header "TonemapDurand.h"
  header "WhiteBalancer.h"
  header "Calib3d.h"
  header "CirclesGridFinderParameters.h"
  header "StereoBM.h"
  header "StereoMatcher.h"
  header "StereoSGBM.h"
  header "UsacParams.h"
  header "Objdetect.h"
  header "BaseCascadeClassifier.h"
  header "CascadeClassifier.h"
  header "FaceDetectorYN.h"
  header "FaceRecognizerSF.h"
  header "GraphicalCodeDetector.h"
  header "HOGDescriptor.h"
  header "QRCodeDetector.h"
  header "QRCodeDetectorAruco.h"
  header "QRCodeDetectorArucoParams.h"
  header "QRCodeEncoder.h"
  header "QRCodeEncoderParams.h"
  header "ArucoDetector.h"
  header "Board.h"
  header "CharucoBoard.h"
  header "CharucoDetector.h"
  header "CharucoParameters.h"
  header "DetectorParameters.h"
  header "Dictionary.h"
  header "GridBoard.h"
  header "RefineParameters.h"
  header "BarcodeDetector.h"
  header "Structured_light.h"
  header "GrayCodePattern.h"
  header "SinusoidalPattern.h"
  header "SinusoidalPatternParams.h"
  header "StructuredLightPattern.h"
  header "Video.h"
  header "BackgroundSubtractor.h"
  header "BackgroundSubtractorKNN.h"
  header "BackgroundSubtractorMOG2.h"
  header "DISOpticalFlow.h"
  header "DenseOpticalFlow.h"
  header "FarnebackOpticalFlow.h"
  header "KalmanFilter.h"
  header "SparseOpticalFlow.h"
  header "SparsePyrLKOpticalFlow.h"
  header "Tracker.h"
  header "TrackerDaSiamRPN.h"
  header "TrackerDaSiamRPNParams.h"
  header "TrackerGOTURN.h"
  header "TrackerGOTURNParams.h"
  header "TrackerMIL.h"
  header "TrackerMILParams.h"
  header "TrackerNano.h"
  header "TrackerNanoParams.h"
  header "TrackerVit.h"
  header "TrackerVitParams.h"
  header "VariationalRefinement.h"
  header "Wechat_qrcode.h"
  header "WeChatQRCode.h"
  header "Xfeatures2d.h"
  header "AffineFeature2D.h"
  header "BEBLID.h"
  header "BoostDesc.h"
  header "BriefDescriptorExtractor.h"
  header "DAISY.h"
  header "FREAK.h"
  header "HarrisLaplaceFeatureDetector.h"
  header "LATCH.h"
  header "LUCID.h"
  header "MSDDetector.h"
  header "PCTSignatures.h"
  header "PCTSignaturesSQFD.h"
  header "SURF.h"
  header "StarDetector.h"
  header "TBMR.h"
  header "TEBLID.h"
  header "VGG.h"
  header "Ximgproc.h"
  header "AdaptiveManifoldFilter.h"
  header "ContourFitting.h"
  header "DTFilter.h"
  header "DisparityFilter.h"
  header "DisparityWLSFilter.h"
  header "EdgeAwareInterpolator.h"
  header "EdgeBoxes.h"
  header "EdgeDrawing.h"
  header "EdgeDrawingParams.h"
  header "FastBilateralSolverFilter.h"
  header "FastGlobalSmootherFilter.h"
  header "FastLineDetector.h"
  header "GuidedFilter.h"
  header "RFFeatureGetter.h"
  header "RICInterpolator.h"
  header "RidgeDetectionFilter.h"
  header "ScanSegment.h"
  header "SparseMatchInterpolator.h"
  header "StructuredEdgeDetection.h"
  header "SuperpixelLSC.h"
  header "SuperpixelSEEDS.h"
  header "SuperpixelSLIC.h"
  header "GraphSegmentation.h"
  header "SelectiveSearchSegmentation.h"
  header "SelectiveSearchSegmentationStrategy.h"
  header "SelectiveSearchSegmentationStrategyColor.h"
  header "SelectiveSearchSegmentationStrategyFill.h"
  header "SelectiveSearchSegmentationStrategyMultiple.h"
  header "SelectiveSearchSegmentationStrategySize.h"
  header "SelectiveSearchSegmentationStrategyTexture.h"
  header "Aruco.h"
  header "EstimateParameters.h"
  header "Bgsegm.h"
  header "BackgroundSubtractorCNT.h"
  header "BackgroundSubtractorGMG.h"
  header "BackgroundSubtractorGSOC.h"
  header "BackgroundSubtractorLSBP.h"
  header "BackgroundSubtractorLSBPDesc.h"
  header "BackgroundSubtractorMOG.h"
  header "SyntheticSequenceGenerator.h"
  header "Bioinspired.h"
  header "Retina.h"
  header "RetinaFastToneMapping.h"
  header "TransientAreasSegmentationModule.h"
  header "Face.h"
  header "BIF.h"
  header "BasicFaceRecognizer.h"
  header "EigenFaceRecognizer.h"
  header "FaceRecognizer.h"
  header "Facemark.h"
  header "FacemarkAAM.h"
  header "FacemarkKazemi.h"
  header "FacemarkLBF.h"
  header "FacemarkTrain.h"
  header "FisherFaceRecognizer.h"
  header "LBPHFaceRecognizer.h"
  header "MACE.h"
  header "PredictCollector.h"
  header "StandardCollector.h"
  header "Tracking.h"
  header "TrackerCSRT.h"
  header "TrackerCSRTParams.h"
  header "TrackerKCF.h"
  header "TrackerKCFParams.h"
  header "Img_hash.h"
  header "AverageHash.h"
  header "BlockMeanHash.h"
  header "ColorMomentHash.h"
  header "ImgHashBase.h"
  header "MarrHildrethHash.h"
  header "PHash.h"
  header "RadialVarianceHash.h"
  export *
  module * {export *}
}
