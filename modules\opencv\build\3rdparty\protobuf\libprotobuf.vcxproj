﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{BD9B388D-FD9D-3F8B-8911-0C2681FF376A}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>libprotobuf</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\cudabuild\3rdparty\lib\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">libprotobuf.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">libprotobufd</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\cudabuild\3rdparty\lib\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">libprotobuf.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">libprotobuf</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/AI/opencv/opencv-4.10.0/3rdparty/protobuf/src" /external:I "D:/AI/opencv/cudabuild" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4127;4251;4324;4275;4512;4589;4819;4244;4267;4018;4355;4800;4251;4996;4146;4305;4127;4100;4512;4125;4389;4510;4610;4702;4456;4457;4065;4310;4661;4506;4701;4703;4505</DisableSpecificWarnings>
      <ExceptionHandling>Async</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;_CRT_SECURE_NO_WARNINGS=1;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ProgramDataBaseFileName>D:\AI\opencv\cudabuild\3rdparty\lib\Debug\libprotobufd.pdb</ProgramDataBaseFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;_CRT_SECURE_NO_WARNINGS=1;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4221</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/AI/opencv/opencv-4.10.0/3rdparty/protobuf/src" /external:I "D:/AI/opencv/cudabuild" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4127;4251;4324;4275;4512;4589;4819;4244;4267;4018;4355;4800;4251;4996;4146;4305;4127;4100;4512;4125;4389;4510;4610;4702;4456;4457;4065;4310;4661;4506;4701;4703;4505</DisableSpecificWarnings>
      <ExceptionHandling>Async</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;NDEBUG;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;_CRT_SECURE_NO_WARNINGS=1;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ProgramDataBaseFileName>D:\AI\opencv\cudabuild\3rdparty\lib\Release\libprotobuf.pdb</ProgramDataBaseFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;NDEBUG;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;_CRT_SECURE_NO_WARNINGS=1;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4221</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\any_lite.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\arena.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\arenastring.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\extension_set.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\generated_message_util.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\implicit_weak_message.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\io\coded_stream.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\io\io_win32.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\io\strtod.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\io\zero_copy_stream.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\io\zero_copy_stream_impl.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\io\zero_copy_stream_impl_lite.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\map.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\message_lite.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\parse_context.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\repeated_field.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\repeated_ptr_field.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\bytestream.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\common.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\int128.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\status.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\stringpiece.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\stringprintf.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\structurally_valid.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\strutil.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\wire_format_lite.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\any.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\descriptor.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\descriptor.pb.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\descriptor_database.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\dynamic_message.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\extension_set_heavy.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\generated_message_reflection.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\io\tokenizer.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\map_field.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\message.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\reflection_ops.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\substitute.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\text_format.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\unknown_field_set.cc" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\wire_format.cc" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>