/*
 *
 * Header exported from binary.
 * [./export-boostdesc.py BINBOOST binboost.bin]
 *
 */

// dimensionality of learner
static const int nDim = 64;

// orientations
static const int orientQuant = 8;

// patch size
static const int patchSize = 32;

// gradient assignment type
static const int iGradAssignType = ASSIGN_SOFT;

// number of weak learners
static const int nWLs = 32;

// threshold array (64 x 32)
static const unsigned int thresh[] =
{
 0x3dce789e, 0x3dc2a1eb, 0x3e2b2570, 0x3dd417ca, 0x3ded4ed3, 0x3dee9bbf, 0x3e31eaff, 0x3e2d056c,
 0x3dd4f052, 0x3de0168b, 0x3db00ff0, 0x3dcf965b, 0x3de7b1c0, 0x3e464d3c, 0x3def8056, 0x3dcf7db7,
 0x3e2ab25b, 0x3d6be81b, 0x3da45c79, 0x3e17f3cf, 0x3e419a41, 0x3dd5c7ce, 0x3df25786, 0x3e3eba28,
 0x3e1fa6df, 0x3dfe6ff8, 0x3ddbf056, 0x3de63d81, 0x3e03c686, 0x3e3d7d7c, 0x3e0b877b, 0x3dd0635a,
 0x3e31c715, 0x3ddba3ca, 0x3e3119ce, 0x3df24074, 0x3e3affb0, 0x3dd09071, 0x3dc77ac1, 0x3df77c89,
 0x3df17508, 0x3dd847f5, 0x3e4b9453, 0x3dc1aa24, 0x3e4c42a6, 0x3e2055fc, 0x3d5afa8d, 0x3e2adafd,
 0x3e412fd3, 0x3dfd145e, 0x3dd8d4fe, 0x3e044a62, 0x3dcd759f, 0x3dd6659d, 0x3d8b8979, 0x3df11d7a,
 0x3dd17721, 0x3de88377, 0x3e04c448, 0x3dc63d16, 0x3e211e00, 0x3de5cb36, 0x3da7f90e, 0x3e21a3f4,
 0x3dcb2e09, 0x3e3433d7, 0x3de56a38, 0x3e4ef912, 0x3dc8d442, 0x3dd64f12, 0x3df70bd0, 0x3dda493d,
 0x3de6f933, 0x3df13059, 0x3df18223, 0x3e4ba55d, 0x3dd67a10, 0x3dcf459e, 0x3dd7e34c, 0x3e404ee8,
 0x3e21d7dc, 0x3dc6937d, 0x3dd14120, 0x3df9fa98, 0x3e03b0c4, 0x3da202da, 0x3e325636, 0x3dc110c9,
 0x3da54ecc, 0x3daf55f9, 0x3de86f8b, 0x3e2c3090, 0x3e3230ba, 0x3d9c972a, 0x3df2fcad, 0x3dd4538f,
 0x3dcbf734, 0x3de04966, 0x3df1f4f5, 0x3dd5e5b0, 0x3dfaf3a1, 0x3e3b3073, 0x3dd1337f, 0x3e3a3f46,
 0x3d9902de, 0x3e2c89b1, 0x3dd3f39d, 0x3def2f98, 0x3ddffcdb, 0x3dc2e283, 0x3dc823bb, 0x3df4994a,
 0x3df5e743, 0x3de7ded7, 0x3e3e8188, 0x3db3ad4f, 0x3e25c076, 0x3e0c1df3, 0x3de37ac4, 0x3dcae544,
 0x3e25daf0, 0x3dc003e1, 0x3db1e372, 0x3de9667b, 0x3e084d77, 0x3da1537a, 0x3d962b0d, 0x3dfa2728,
 0x3de35a42, 0x3de7607c, 0x3e20a3d7, 0x3e22e771, 0x3dec0054, 0x3def56eb, 0x3e230447, 0x3de19221,
 0x3dd1ab08, 0x3dc8557c, 0x3de465a5, 0x3dfb7846, 0x3dcddf01, 0x3dd7d06c, 0x3dcc2d5d, 0x3e3f30e8,
 0x3df434a0, 0x3dd67b1c, 0x3e232d2c, 0x3ded6dc2, 0x3e50663c, 0x3df94b7b, 0x3df74928, 0x3de43c3a,
 0x3dce5040, 0x3dc18cbb, 0x3dda1123, 0x3dfbcec8, 0x3e4cbd12, 0x3daa0a2a, 0x3def8b59, 0x3ddf4c2b,
 0x3df03b82, 0x3e1e6fb5, 0x3dea4b99, 0x3dd16b12, 0x3df0df9c, 0x3e1fb4c4, 0x3db6deef, 0x3e24217d,
 0x3dfa77e6, 0x3dd538ac, 0x3df65669, 0x3e1bc48f, 0x3de3ec03, 0x3df01755, 0x3e1ffef4, 0x3e520ddc,
 0x3e574c0a, 0x3e07d34e, 0x3dec4b4d, 0x3df043e5, 0x3dc0c9b2, 0x3da78705, 0x3dcb76a6, 0x3dc1db87,
 0x3dee171a, 0x3de73cd5, 0x3decc78f, 0x3db1cfd6, 0x3e02d341, 0x3de27d89, 0x3dd159c5, 0x3db58073,
 0x3dbf7dfa, 0x3e4bf445, 0x3deca926, 0x3dd35dab, 0x3d9aa682, 0x3dc833c6, 0x3e36dacb, 0x3e0b4f1e,
 0x3e2976bc, 0x3dfddb12, 0x3dd9b457, 0x3e316c61, 0x3e13f5b6, 0x3e20902e, 0x3dd5fe54, 0x3df4434e,
 0x3dd5b3e1, 0x3deced4e, 0x3da08462, 0x3d927e0f, 0x3df22186, 0x3dfc45cc, 0x3dc902c3, 0x3d95b5aa,
 0x3deb3893, 0x3de1871e, 0x3db5035a, 0x3decd749, 0x3dd329c3, 0x3e059d99, 0x3e450b0f, 0x3deeb570,
 0x3e2f227d, 0x3dd0bfa1, 0x3d46a3f4, 0x3e219ce0, 0x3e342891, 0x3dca2644, 0x3de89870, 0x3de1b64e,
 0x3e22cb64, 0x3e5c21e2, 0x3dcf2c73, 0x3df9945b, 0x3ddf47fa, 0x3dc978c8, 0x3dd32053, 0x3dd401c5,
 0x3dc6a3b0, 0x3d42730c, 0x3dd21038, 0x3db2e2e1, 0x3e020104, 0x3e223748, 0x3da2b679, 0x3df682f9,
 0x3deeb357, 0x3e1f969e, 0x3df8eeaf, 0x3df9207d, 0x3dee3bcd, 0x3defeb4a, 0x3ddf223a, 0x3dcd117b,
 0x3dfa1ec5, 0x3e3af102, 0x3df244a6, 0x3e2fae79, 0x3dc92395, 0x3df3bafe, 0x3d96ce1b, 0x3dbf3875,
 0x3ddcf13d, 0x3e23bafe, 0x3ddd6f11, 0x3dd6ae7d, 0x3e2eb52d, 0x3e2340d5, 0x3cb4c27f, 0x3de7da1f,
 0x3df39fba, 0x3de80842, 0x3df83a54, 0x3e13b7d8, 0x3dc76de8, 0x3d9bd747, 0x3e2f5771, 0x3e5636b1,
 0x3df743e9, 0x3df578a3, 0x3e2943e1, 0x3e240b35, 0x3dfa73b4, 0x3e174de0, 0x3df53f7d, 0x3dd757d6,
 0x3e19e668, 0x3ded2136, 0x3dc38378, 0x3d89f087, 0x3e2b4203, 0x3dfa6b51, 0x3deb228e, 0x3dc6dd42,
 0x3dd28848, 0x3e2a0b5f, 0x3db28f6a, 0x3df2ebe6, 0x3dfbb9d0, 0x3e0152b1, 0x3dbd38f6, 0x3e1da1a9,
 0x3e172da1, 0x3dfc9928, 0x3dfe8a2f, 0x3dc7d0e5, 0x3df5885d, 0x3df5464e, 0x3db689a2, 0x3dce0978,
 0x3dd19371, 0x3db943e1, 0x3d9f53c5, 0x3e009bfa, 0x3dc2eb45, 0x3e004103, 0x3e0dfdf0, 0x3deb228e,
 0x3dcaa6fb, 0x3e3cca71, 0x3dd03105, 0x3e2f0a5f, 0x3dfd933e, 0x3df8433d, 0x3e242e99, 0x3de2046c,
 0x3dffd827, 0x3d8e4855, 0x3de9f948, 0x3df79852, 0x3dd2fb1a, 0x3e04bb5e, 0x3df2ba9d, 0x3e1b9a5b,
 0x3e219eb6, 0x3db3b9e4, 0x3e00fe48, 0x3dd741d1, 0x3ded373b, 0x3dfbc5df, 0x3df26634, 0x3e048a16,
 0x3dd5df65, 0x3dd73322, 0x3deef998, 0x3dd9f7f9, 0x3e27a701, 0x3dfdc161, 0x3ded7f95, 0x3de7136a,
 0x3e38fa7b, 0x3e1ab192, 0x3e505682, 0x3dc3e2fe, 0x3e20ded3, 0x3e4271bd, 0x3e00b84a, 0x3ddf6a94,
 0x3dfd5800, 0x3e37f456, 0x3dd57a35, 0x3df06f69, 0x3dcd80a1, 0x3dec2b52, 0x3de09dd0, 0x3e066384,
 0x3e0358f3, 0x3d840bfe, 0x3ddaa92e, 0x3e343244, 0x3e1a5376, 0x3d86b419, 0x3df04ddb, 0x3d9b5328,
 0x3e020e63, 0x3defc611, 0x3dfc7043, 0x3de7cd8a, 0x3dde1c9b, 0x3df71194, 0x3e078034, 0x3e0ea4a9,
 0x3dc17f92, 0x3daf18d7, 0x3dbda47e, 0x3e24317b, 0x3e45b35b, 0x3dd15a4b, 0x3df3397e, 0x3e190f73,
 0x3dfb78cd, 0x3df013a9, 0x3e13f35a, 0x3e63bbc7, 0x3e462670, 0x3da1a90a, 0x3dc76f2a, 0x3df477bc,
 0x3de347e9, 0x3dd4213a, 0x3df1db44, 0x3dfbb0e6, 0x3df7214f, 0x3dd81e8a, 0x3df8e970, 0x3db93327,
 0x3df2e515, 0x3da2c146, 0x3df224ab, 0x3dd1bd62, 0x3dedf612, 0x3dc30596, 0x3e12fa0d, 0x3dd7a5f4,
 0x3e593965, 0x3e380885, 0x3deef1bb, 0x3de4180d, 0x3dc3fb1c, 0x3e35b079, 0x3dd004fb, 0x3e503750,
 0x3dd4b2fb, 0x3df39757, 0x3df9bb28, 0x3de67ad9, 0x3da950ac, 0x3e206e1a, 0x3deb51bd, 0x3e2e69f0,
 0x3e2c6e2b, 0x3dcc8af3, 0x3df5d24a, 0x3d8e18c7, 0x3e03cab8, 0x3e2c3c9f, 0x3ddf6c27, 0x3e43454a,
 0x3e11f6cb, 0x3e43fd0d, 0x3dda5daf, 0x3df61cbd, 0x3de979e1, 0x3e0c52e7, 0x3e1472c1, 0x3e1a57eb,
 0x3e456ead, 0x3dec8cd6, 0x3dc77278, 0x3dc86006, 0x3dc4519e, 0x3e2366d8, 0x3df85511, 0x3de8b008,
 0x3e55043e, 0x3df3636f, 0x3dc44773, 0x3dd9dece, 0x3e02fb1a, 0x3dd49e88, 0x3db90d0a, 0x3dc5eff7,
 0x3df4e2b0, 0x3e53f812, 0x3dd7d73d, 0x3dd5d80e, 0x3e0c0c63, 0x3e265b64, 0x3e2bcf0b, 0x3df1ed9e,
 0x3df4141f, 0x3d93793f, 0x3dc0f0c1, 0x3e04cb5c, 0x3df36f7e, 0x3e2716d3, 0x3ddd9f4d, 0x3de976bc,
 0x3ddb65ed, 0x3db3344d, 0x3d9460d3, 0x3dd415b1, 0x3e5b2a28, 0x3dea009f, 0x3dfc011d, 0x3dae6ce0,
 0x3df2d773, 0x3e1f9a07, 0x3df21ee6, 0x3e64d595, 0x3de85016, 0x3dde78e2, 0x3dc2ade0, 0x3dfb6673,
 0x3e20574b, 0x3dcfd32c, 0x3dcb6b03, 0x3dd06fef, 0x3e00a287, 0x3dd8bbd4, 0x3d8cb619, 0x3e235372,
 0x3db9f10d, 0x3df9abf3, 0x3e006855, 0x3df7ab32, 0x3e510625, 0x3e21e215, 0x3df27807, 0x3d910873,
 0x3deaa088, 0x3d85989e, 0x3e2cfb76, 0x3dcb485b, 0x3db9ed39, 0x3e49d1f6, 0x3e10bbb2, 0x3e3cd035,
 0x3db07f8f, 0x3db6370f, 0x3dd197a2, 0x3dadbc16, 0x3dcedbb6, 0x3dd4e337, 0x3df56324, 0x3dcf8c65,
 0x3d89b025, 0x3e1c5048, 0x3e3dd2af, 0x3dfc7150, 0x3dfe9292, 0x3dfd76ee, 0x3e16b0d9, 0x3dfda2f9,
 0x3db90dee, 0x3e4a0c6b, 0x3e43bd5a, 0x3ddbde83, 0x3ddfcb92, 0x3dfac3eb, 0x3df83622, 0x3df70d63,
 0x3dbcb6d5, 0x3d8364a4, 0x3dcb9537, 0x3d90a493, 0x3e584fd3, 0x3e3393ab, 0x3db54e38, 0x3e26c871,
 0x3df59ff5, 0x3db7bf97, 0x3df09396, 0x3dfd7a9a, 0x3de9ccb8, 0x3db5ee21, 0x3e23c03c, 0x3e4c90c5,
 0x3df264a1, 0x3df8665e, 0x3dd09aed, 0x3dfcea6c, 0x3e06b8b7, 0x3dd70479, 0x3e04e5d6, 0x3dfb1855,
 0x3dbba875, 0x3e38d0cc, 0x3e035504, 0x3db6eeed, 0x3e1b7d41, 0x3e12aa19, 0x3dd16095, 0x3dd9ed7c,
 0x3df68be3, 0x3ddbc4d2, 0x3d7b0eca, 0x3df481b2, 0x3da81f89, 0x3dc492d7, 0x3e46b010, 0x3de66773,
 0x3dde08af, 0x3e1f15e8, 0x3df4c900, 0x3d7fc90e, 0x3dc127b3, 0x3db379fb, 0x3dda97e1, 0x3d874836,
 0x3dd5b5fa, 0x3e27db2b, 0x3de2efd4, 0x3de24edf, 0x3e326b2f, 0x3e04e486, 0x3defa7a8, 0x3dce9ab3,
 0x3e022d95, 0x3df1c21a, 0x3defa58f, 0x3dcbe03e, 0x3e476534, 0x3df1819d, 0x3dd7f173, 0x3defe692,
 0x3defab54, 0x3df6a37b, 0x3dd8ef35, 0x3dac43ce, 0x3df3165d, 0x3df1b36c, 0x3de691ea, 0x3e16c094,
 0x3dd44d44, 0x3dfd6627, 0x3db471cf, 0x3db2ba32, 0x3e3bb5e1, 0x3dc104ad, 0x3e27b5f2, 0x3df0307f,
 0x3db1dc0e, 0x3d2a7067, 0x3def74cd, 0x3e0e86c6, 0x3df0fe8b, 0x3e3c48f1, 0x3e100b03, 0x3dff7b5b,
 0x3e378b37, 0x3df19503, 0x3e45aaf8, 0x3ded7cf6, 0x3ddeb5b3, 0x3df3c4f4, 0x3de37bd0, 0x3db5f03a,
 0x3e476d11, 0x3db0bdb0, 0x3e4627bf, 0x3debfb9c, 0x3dd2a9d6, 0x3daa9103, 0x3d267ffc, 0x3e4fd439,
 0x3dceba28, 0x3dcab7dc, 0x3dc97204, 0x3dfb9e8c, 0x3e28f10b, 0x3e03b9f1, 0x3d8f75a4, 0x3df22292,
 0x3dd02eec, 0x3e0e95fb, 0x3e0cb1cd, 0x3dce429e, 0x3d75a477, 0x3e055b8a, 0x3dbec7c9, 0x3e1c6584,
 0x3da86021, 0x3dd757d6, 0x3e004253, 0x3df4d50f, 0x3df753a4, 0x3de0e842, 0x3e34c09c, 0x3dfbcd36,
 0x3dd8ac9f, 0x3dc7c02b, 0x3e307464, 0x3e53d35f, 0x3dc0677f, 0x3db5dcab, 0x3dde18f0, 0x3ddf78bc,
 0x3dc329de, 0x3e43d46b, 0x3de55e29, 0x3dfa4df4, 0x3dec710d, 0x3df1d580, 0x3e06d19e, 0x3df8e970,
 0x3df15b57, 0x3df2fcad, 0x3d8669cf, 0x3e0bacf7, 0x3e08754f, 0x3df4f616, 0x3e23c85c, 0x3e050acc,
 0x3dc52b25, 0x3e031df7, 0x3e382667, 0x3e3edd05, 0x3d946321, 0x3df1e4b4, 0x3dcf965b, 0x3dfd4739,
 0x3de05037, 0x3e4ad213, 0x3dd45953, 0x3d89dd64, 0x3df0bae9, 0x3e125786, 0x3e04245f, 0x3de3d817,
 0x3e4da273, 0x3dc25b5a, 0x3dc50085, 0x3def1d3f, 0x3ded5e07, 0x3dcb66d1, 0x3de5796c, 0x3df5fb2f,
 0x3e02680a, 0x3dbf9c63, 0x3d9c72ba, 0x3de40182, 0x3e154691, 0x3df9378f, 0x3e03ae25, 0x3dfa8c58,
 0x3dfccc8a, 0x3de12f90, 0x3e05de9c, 0x3df5c5b5, 0x3e327957, 0x3e1d2f5e, 0x3e0b9c73, 0x3dc05d88,
 0x3e5b3f21, 0x3e1d4068, 0x3dfb0ab3, 0x3e3a57a8, 0x3db18e32, 0x3e220ff5, 0x3dd3dc05, 0x3dcc6a72,
 0x3e32ac75, 0x3dd3fa6e, 0x3e410a57, 0x3da59fcd, 0x3e0a3444, 0x3dfaaef3, 0x3dfff045, 0x3e4a9b8d,
 0x3e4db403, 0x3e4bdd33, 0x3dcfd545, 0x3deeb91b, 0x3de3fdd6, 0x3df2a38c, 0x3ddeeb2d, 0x3df34df0,
 0x3de4d877, 0x3d8bc2b9, 0x3e403600, 0x3dfa57eb, 0x3ded5a5c, 0x3dd4a44c, 0x3e2f9a4a, 0x3d928eae,
 0x3dd97aab, 0x3de338b4, 0x3dc7e084, 0x3e2e7211, 0x3df63d3e, 0x3d8ea8cd, 0x3e289ce5, 0x3e3b239a,
 0x3dfee45c, 0x3dfbe6e6, 0x3e52a561, 0x3df2d3c8, 0x3e0b0c89, 0x3e000325, 0x3e5ead4f, 0x3ddb1e19,
 0x3deffd1e, 0x3e0ec13c, 0x3d97fce1, 0x3ded013b, 0x3df85a4f, 0x3dde0c5b, 0x3def09d9, 0x3e0579f2,
 0x3dd53a3f, 0x3de27aea, 0x3df17efe, 0x3de174c5, 0x3e21ee67, 0x3dfb0bbf, 0x3e06c0d7, 0x3ddac259,
 0x3dd57796, 0x3e5129cc, 0x3e29c348, 0x3d846cb9, 0x3dfa1cac, 0x3e1faa04, 0x3df6fe2e, 0x3e3af252,
 0x3ddf3e03, 0x3dc21d7c, 0x3ddfa440, 0x3dd0c1b9, 0x3de58c4c, 0x3e2111f1, 0x3defba02, 0x3df5d0b7,
 0x3dee5e68, 0x3de7fb26, 0x3e211234, 0x3e4cf744, 0x3df9c62a, 0x3dfb773a, 0x3df4dbe0, 0x3e00fd3b,
 0x3df3300e, 0x3db3ad69, 0x3df55b46, 0x3df2567a, 0x3dea3c64, 0x3e28316a, 0x3e009b74, 0x3e2adafd,
 0x3e3d7668, 0x3de2ba5a, 0x3df75ea6, 0x3d57f29b, 0x3dd4db59, 0x3dfe7b81, 0x3e28fabe, 0x3e4487fd,
 0x3e023359, 0x3d85cf90, 0x3df3311a, 0x3e21c21a, 0x3ddf8f47, 0x3ddb688c, 0x3de0da1b, 0x3df9389b,
 0x3e137931, 0x3d92f314, 0x3e696c83, 0x3d905db1, 0x3d92cc20, 0x3e0f48c3, 0x3e5d3997, 0x3dcd184c,
 0x3de809d5, 0x3d809a17, 0x3dd4fce7, 0x3e08e608, 0x3d7898b3, 0x3ddc7bcc, 0x3dd24c83, 0x3d8852dd,
 0x3db1a346, 0x3e367be5, 0x3df37436, 0x3dd6d86f, 0x3dc8bd59, 0x3d8456b4, 0x3dfb71fc, 0x3df0318c,
 0x3e007b35, 0x3e233615, 0x3deb07d1, 0x3dc66285, 0x3c9add9c, 0x3d43254e, 0x3e15521a, 0x3dc03b67,
 0x3e2b62c7, 0x3dd4cb9f, 0x3de365cb, 0x3dd4677b, 0x3df0a2cb, 0x3def2bed, 0x3dfc304d, 0x3d8639ae,
 0x3df8b47c, 0x3e114c66, 0x3dee45c3, 0x3df9ffd6, 0x3de152b1, 0x3de7967d, 0x3e0b08dd, 0x3dea93f3,
 0x3dbe755e, 0x3dd87d70, 0x3daea8f5, 0x3df3b081, 0x3db650f5, 0x3dd6d19e, 0x3e4b5e0f, 0x3dc400fc,
 0x3e29ca9f, 0x3dcee286, 0x3de49a99, 0x3de36c9c, 0x3de21a72, 0x3dc9310f, 0x3e3de508, 0x3e22274a,
 0x3d8a1e67, 0x3db19ded, 0x3d337f39, 0x3db54958, 0x3e0a9cdc, 0x3ddcee18, 0x3df0d5a6, 0x3dfd840a,
 0x3df94749, 0x3e116b55, 0x3e0aed14, 0x3de90710, 0x3df9e9d1, 0x3dd3964a, 0x3e03ab43, 0x3deae85c,
 0x3e309918, 0x3df2dc2b, 0x3e265dc0, 0x3dc5a675, 0x3e4df86e, 0x3e01c582, 0x3d8f965b, 0x3e038477,
 0x3dd126e9, 0x3df2fe3f, 0x3e034b51, 0x3e01dc94, 0x3dfa06a7, 0x3e20f6f1, 0x3dc5f8ab, 0x3da0b07a,
 0x3dd3b752, 0x3d8837ea, 0x3df6d4c3, 0x3e58711d, 0x3db73e76, 0x3de9f05f, 0x3db6fe57, 0x3e393144,
 0x3dfff1d8, 0x3e0292c5, 0x3db161e5, 0x3dfdcc64, 0x3dd973da, 0x3ded80a1, 0x3defa93b, 0x3dcdce3a,
 0x3e04cd31, 0x3d801422, 0x3d7be985, 0x3df87f88, 0x3d933d0e, 0x3dcd141a, 0x3df08030, 0x3dd76ee7,
 0x3e60370d, 0x3dc0e5d9, 0x3dc6015f, 0x3de8e608, 0x3e0321e6, 0x3e1fce74, 0x3e39a0ae, 0x3de19c9d,
 0x3e022ea1, 0x3df61f5c, 0x3dfb645a, 0x3b81a55c, 0x3de81909, 0x3d8d80ca, 0x3df1a6d7, 0x3dc99fc9,
 0x3e01904b, 0x3e03910c, 0x3e100b89, 0x3dc25113, 0x3e03bc0a, 0x3da85b84, 0x3df24181, 0x3dca31f5,
 0x3dfe236c, 0x3debd2b7, 0x3df5579b, 0x3df35222, 0x3dfc1c61, 0x3df6c7a8, 0x3dfe3b8a, 0x3dd5d0b7,
 0x3d6abca2, 0x3e289b95, 0x3dbfd1a7, 0x3df4506a, 0x3e27429a, 0x3dcadfb5, 0x3e2fa2f0, 0x3db910eb,
 0x3dea9824, 0x3e286b9c, 0x3dd92e1f, 0x3e03e187, 0x3de580c3, 0x3dbbcd86, 0x3dc7cf1c, 0x3e4d2418,
 0x3da37a8e, 0x3e09a34d, 0x3e0a832c, 0x3de719b5, 0x3dd91eeb, 0x3df10f52, 0x3e004bc2, 0x3e154b06,
 0x3e3c6328, 0x3d9a24cc, 0x3dbfffaf, 0x3dd06f69, 0x3defcefb, 0x3e01cb8a, 0x3e4ce63a, 0x3e454e6e,
 0x3dfd3aa3, 0x3dcec6bd, 0x3be91604, 0x3e0d2242, 0x3e0b4f61, 0x3dfc8755, 0x3daa5bd9, 0x3e168273,
 0x3d85d837, 0x3e36ddf0, 0x3d8b5576, 0x3e11c9f7, 0x3e0616b5, 0x3e0c60cc, 0x3dc65626, 0x3dfc2870,
 0x3e32ebe6, 0x3dc4875c, 0x3ddab3ab, 0x3de896dd, 0x3df4a01b, 0x3df88ca4, 0x3df58b82, 0x3d9a1ce2,
 0x3e00f51b, 0x3df159c5, 0x3df3b107, 0x3db81d99, 0x3db61383, 0x3dccc964, 0x3df30510, 0x3e2fe048,
 0x3e395a29, 0x3d997e3b, 0x3d381c8c, 0x3dd5f139, 0x3de9b49a, 0x3d9eb48c, 0x3d8d56b0, 0x3df1a976,
 0x3dbbcc51, 0x3e554d62, 0x3e131833, 0x3de99d02, 0x3dfe0ce1, 0x3df67a96, 0x3e03d641, 0x3dedbf05,
 0x3e083ec9, 0x3d453fc0, 0x3e013986, 0x3dd818c6, 0x3e070a81, 0x3dd90108, 0x3dceb13e, 0x3defb118,
 0x3ddf212d, 0x3def1e4b, 0x3dd5fb2f, 0x3e4f0026, 0x3dfa7f3d, 0x3e04cdb8, 0x3dc8a2c4, 0x3ddb6fe3,
 0x3d935273, 0x3d81d771, 0x3df27781, 0x3de05ee5, 0x3df0d845, 0x3de3f78c, 0x3e11975f, 0x3de128bf,
 0x3da6fc59, 0x3e3dc11e, 0x3d96c68e, 0x3dfb64e0, 0x3e500f34, 0x3dadf952, 0x3dfc98a2, 0x3df40896,
 0x3db24c40, 0x3dfbe7f3, 0x3de3f1c7, 0x3de3e6c5, 0x3e030e80, 0x3deb178b, 0x3dfb1184, 0x3def038e,
 0x3dc1263b, 0x3df640ea, 0x3dd13f07, 0x3d9f3733, 0x3e094a6f, 0x3e4192ea, 0x3de352eb, 0x3dd0856e,
 0x3de83c29, 0x3e253198, 0x3d7070fc, 0x3e1a732e, 0x3d8d9bca, 0x3df8b8ae, 0x3de4f00f, 0x3e071045,
 0x3e2aa25e, 0x3e0965b2, 0x3dce3bcd, 0x3dfed84d, 0x3e04d2f6, 0x3dc2125f, 0x3dfd96ea, 0x3e08f96e,
 0x3dedd723, 0x3e139cd8, 0x3e065e89, 0x3e1ad7d8, 0x3df72474, 0x3e310e88, 0x3dc2c8ed, 0x3d8400d3,
 0x3df5ee9a, 0x3e471972, 0x3e00ad47, 0x3dc4b9d9, 0x3db9bf67, 0x3dfdfeb9, 0x3db1ae2e, 0x3def5e42,
 0x3e0da76e, 0x3df92d99, 0x3de8bb90, 0x3e18f57f, 0x3ddb5bf7, 0x3daa5a47, 0x3e03b214, 0x3da738a4,
 0x3dd9f905, 0x3d2960b7, 0x3df7df1a, 0x3e035dee, 0x3e0f9f45, 0x3ddca31e, 0x3dc25d4a, 0x3df7a5f4,
 0x3e043138, 0x3e10ba1f, 0x3de57d9e, 0x3dc28325, 0x3df70c56, 0x3d86ddf0, 0x3d8b917e, 0x3df4855e,
 0x3dd90eaa, 0x3df5bab2, 0x3da4cd5a, 0x3e08e68e, 0x3da69bd3, 0x3e014446, 0x3dd336df, 0x3e40fc72,
 0x3df424e6, 0x3db43fe6, 0x3dc076b3, 0x3db66ac0, 0x3df22d0e, 0x3e5966be, 0x3dcd3cff, 0x3d8ceba1,
 0x3e3d40ab, 0x3dc26a8e, 0x3ddee4e2, 0x3df9c843, 0x3dfbaecd, 0x3e0e77d5, 0x3e00da1b, 0x3e28df7a,
 0x3df98288, 0x3d80eb17, 0x3e4afa72, 0x3e07db6e, 0x3d8d7577, 0x3dfb9f99, 0x3db22ec9, 0x3dedd1e5,
 0x3dc97f55, 0x3df2aae3, 0x3e11b047, 0x3dbd12d8, 0x3e06e47e, 0x3e03ab43, 0x3dbca743, 0x3e29fe87,
 0x3dd4face, 0x3df3d428, 0x3d935eed, 0x3ddeab36, 0x3e01a049, 0x3debd231, 0x3d848832, 0x3dfa7c18,
 0x3dfd3133, 0x3e1e2da5, 0x3d9abe92, 0x3dff7404, 0x3e0a31e8, 0x3d926e11, 0x3df94291, 0x3e0cfaf0,
 0x3e4b2af1, 0x3dcb5d6e, 0x3e1041cc, 0x3e003d9b, 0x3e173e68, 0x3dbc54bd, 0x3dfa5b10, 0x3dd9780c,
 0x3df265ae, 0x3e05f783, 0x3da78606, 0x3d9a9de9, 0x3d93c37c, 0x3d4f3bdd, 0x3dcdba4d, 0x3dd9af19,
 0x3d6c12ae, 0x3e053437, 0x3db3ca32, 0x3dbb07b6, 0x3e230d30, 0x3defb97c, 0x3e051e75, 0x3dd42785,
 0x3de1e710, 0x3e09b2c4, 0x3e0a511a, 0x3dd7b310, 0x3e044d87, 0x3e4dc550, 0x3dd9f234, 0x3d9c28b3,
 0x3dfbd167, 0x3e066f93, 0x3e0bf940, 0x3d9f6888, 0x3e00e0a8, 0x3ddcf030, 0x3e10ac3b, 0x3de23e18,
 0x3e0a3012, 0x3d9da0fb, 0x3de28fe2, 0x3df9bcba, 0x3db222c8, 0x3e3ec56d, 0x3dc1bb49, 0x3e21a650,
 0x3df53ac5, 0x3dd2dc2b, 0x3d90be94, 0x3e007421, 0x3dde4de4, 0x3e2f8a4c, 0x3de09aaa, 0x3d97b777,
 0x3e04fa8b, 0x3e06ae7d, 0x3d93c212, 0x3dacc4f0, 0x3da1d522, 0x3d894848, 0x3de3fc44, 0x3e09b0ee,
 0x3e62ae08, 0x3e016228, 0x3d3914bf, 0x3de11a11, 0x3df03a75, 0x3e33d70a, 0x3e074ff8, 0x3dd31013,
 0x3e042e12, 0x3dcbfdea, 0x3da43dcd, 0x3dfa1c26, 0x3e00168b, 0x3dea686e, 0x3d80fc4a, 0x3dd63993,
 0x3dac6038, 0x3df78dd6, 0x3de1bfbe, 0x3e11001d, 0x3df3b4b3, 0x3de33c60, 0x3e0f5cf2, 0x3de2834d,
 0x3dc6e72a, 0x3da318c7, 0x3e0753a4, 0x3dd3e250, 0x3e549907, 0x3e05ac8a, 0x3e0d4cba, 0x3e055ac0,
 0x3dbb3c8f, 0x3e09335d, 0x3e1ac53b, 0x3dd86877, 0x3d85d661, 0x3e26f47b, 0x3df918a0, 0x3deafa2f,
 0x3e0ad8a1, 0x3d994192, 0x3dfeec39, 0x3dd07faa, 0x3d87f116, 0x3dec6cdb, 0x3e0a12b6, 0x3e00ff97,
 0x3de8943e, 0x3d6fd022, 0x3e0224ee, 0x3df5ef20, 0x3daf6e3f, 0x3d9e77ad, 0x3e0de3fc, 0x3dfd1e54,
 0x3dd538ac, 0x3e41d25b, 0x3e0072d2, 0x3de831ad, 0x3db2f4c2, 0x3dfa6e76, 0x3d920f47, 0x3e05479d,
 0x3dc089c8, 0x3da881f2, 0x3de9eb20, 0x3df363f5, 0x3987752a, 0x3de8eb46, 0x3e0e5e24, 0x3d99ffa0,
 0x3dfef0f1, 0x3e07d6b6, 0x3e04c33b, 0x3dedb057, 0x3dfc4c9d, 0x3df9e0e7, 0x3e00ac7e, 0x3df4c661,
 0x3e03da73, 0x3d81370f, 0x3dfa543f, 0x3dd8bb4d, 0x3e447b67, 0x3e4dece5, 0x3dfa92a3, 0x3e09dc2f,
 0x3de979e1, 0x3de7a5b1, 0x3debfca8, 0x3dc5ec8e, 0x3daef3ee, 0x3d9ef284, 0x3df317f0, 0x3e053112,
 0x3dc2f888, 0x3e146174, 0x3df7cbb4, 0x3de60f5e, 0x3dcda986, 0x3de84838, 0x3df72043, 0x3d9e2534,
 0x3dd74470, 0x3de71bce, 0x3df5a188, 0x3dd09396, 0x3d6ead27, 0x3e0013ec, 0x3df36e72, 0x3de5c033,
 0x3dea8199, 0x3dff16b1, 0x3d82c2cb, 0x3de007dd, 0x3dd19696, 0x3e0735c2, 0x3db05377, 0x3dfa5ca3,
 0x3df5af29, 0x3e02fcad, 0x3e0351df, 0x3da0c2ee, 0x3e013b9f, 0x3dfdd65a, 0x3e0729b3, 0x3df594f2,
 0x3da1c68f, 0x3dcc45d9, 0x3e062457, 0x3e05ebfb, 0x3e02263e, 0x3dc547ee, 0x3de6e43b, 0x3db4babd,
 0x3e0b5632, 0x3de775fb, 0x3de0ff54, 0x3de4af0c, 0x3dbbb25e, 0x3e064064, 0x3e036695, 0x3df53a3f,
 0x3df9e40d, 0x3d9da702, 0x3db1b300, 0x3de52157, 0x3e4c83a9, 0x3e0885d3, 0x3e0e9468, 0x3c9a45fc,
 0x3dfec13c, 0x3e33ae25, 0x3df31b15, 0x3dee7e63, 0x3dd9aae7, 0x3ded6ece, 0x3dc88f43, 0x3dbe3b6f,
 0x3e042b73, 0x3e299cbf, 0x3db99da3, 0x3e06f08d, 0x3e0c7608, 0x3def69cb, 0x3dfb0ff1, 0x3dfd7b20,
 0x3d7da547, 0x3e045671, 0x3dce3bcd, 0x3de5accd, 0x3dd93f6c, 0x3e00114d, 0x3e4f5233, 0x3deebb34,
 0x3dd2a6b1, 0x3e088e37, 0x3df98fa3, 0x3e037d63, 0x3e0ea5f8, 0x3e00e9d5, 0x3dd8f57f, 0x3ddf10ed,
 0x3e041cc5, 0x3df82b1f, 0x3e1c7d5f, 0x3d8ac00a, 0x3e0ecb75, 0x3dea12f9, 0x3e3bd3c3, 0x3dff9e7c,
 0x3d987981, 0x3dea8d22, 0x3df6fe2e, 0x3de2699c, 0x3d8800a8, 0x3de83ec9, 0x3e008bfc, 0x3df2e408,
 0x3dfce209, 0x3dd47c74, 0x3dd367a1, 0x3df43e96, 0x3e0040c0, 0x3e07ddca, 0x3e021d54, 0x3dfbdbe4,
 0x3dbaf01e, 0x3e652a84, 0x3dfc5e70, 0x3e09b49a, 0x3debc83b, 0x3dffaf42, 0x3d99b43c, 0x3e1122fb,
 0x3e0eed46, 0x3e069729, 0x3dc32fd8, 0x3e0b313c, 0x3dffc6da, 0x3df4fa48, 0x3de30caa, 0x3e04e5d6,
 0x3d9b9438, 0x3dabc6d0, 0x3e1027d9, 0x3dcdaa93, 0x3df54d1f, 0x3dd5aa71, 0x3de6c6de, 0x3dd0507a,
 0x3ddde2ef, 0x3dfc219f, 0x3dd230ba, 0x3d8e93fd, 0x3de6c00e, 0x3dcd6dc2, 0x3e5c98a2, 0x3e5a17f4,
 0x3e001bca, 0x3e13dd98, 0x3dde8816, 0x3df4ff86, 0x3e520e1f, 0x3e10a783, 0x3dc0e2a6, 0x3e0d4c77,
 0x3de9ba5e, 0x3dfb3b75, 0x3e0074a7, 0x3dd5653d, 0x3dd86fce, 0x3e0e642c, 0x3e0c7d5f, 0x3dcf52b9,
 0x3e137eb3, 0x3d0ad348, 0x3e226070, 0x3da9a06a, 0x3df22c02, 0x3dfc0db2, 0x3e003eea, 0x3e02f33d,
 0x3e00fb22, 0x3dee67d7, 0x3dd234ec, 0x3ddb3637, 0x3dccbef5, 0x3e0b3d4b, 0x3da83f9f, 0x3d9a9bb5,
 0x3e545fe1, 0x3dc4613e, 0x3df1e860, 0x3ddd5c31, 0x3de8f8e8, 0x3e07ccc0, 0x3dd28284, 0x3e054089,
 0x3ddd48cb, 0x3e0eb570, 0x3e064f98, 0x3e02de01, 0x3dea73f7, 0x3d7e69e3, 0x3e03cf2d, 0x3e007cc8,
 0x3d997fb3, 0x3e0498c4, 0x3da84655, 0x3dc599fb, 0x3e013123, 0x3d982c2c, 0x3dcc3cc7, 0x3df2e59b,
 0x3d860d53, 0x3df553ef, 0x3de22ee4, 0x3dc29960, 0x3dabf460, 0x3dadb5f3, 0x3dd65fd9, 0x3df5d788,
 0x3df0fe8b, 0x3d8d84ee, 0x3e17caea, 0x3e043f60, 0x3da01770, 0x3dfc4d23, 0x3e05f45e, 0x3dfecbb8,
 0x3e08a97a, 0x3dd927d4, 0x3db25205, 0x3e08eef2, 0x3e063e4b, 0x3e0be61d, 0x3de9fe87, 0x3e02dba5,
 0x3df5757d, 0x3dcfd545, 0x3ddb5c7d, 0x3dbd9176, 0x3e063e08, 0x3db4c009, 0x3dd77100, 0x3de1e2df,
 0x3e26d699, 0x3dc9bc34, 0x3e14d120, 0x3e0bc515, 0x3d88699e, 0x3d843e53, 0x3e001d9f, 0x3da6ec83,
 0x3df88ca4, 0x3e006573, 0x3e060392, 0x3e00318c, 0x3e039eae, 0x3de09fe8, 0x3e12b303, 0x3e01f42c,
 0x3dfdd5d4, 0x3de0639d, 0x3de1b435, 0x3e04e875, 0x3de2749f, 0x3e13111f, 0x3e060914, 0x3dbbc0d6,
 0x3deb29e5, 0x3e02d01c, 0x3dfba1b2, 0x3e021f2a, 0x3dda9e2c, 0x3d1607fa, 0x3e03547e, 0x3e03a9f4,
 0x3e0dedf2, 0x3de65626, 0x3dc41538, 0x3de3dddb, 0x3e056ffc, 0x3e085be2, 0x3e0f75da, 0x3e526959,
 0x3de4d445, 0x3de5a038, 0x3df9a390, 0x3e0b938a, 0x3dc39e50, 0x3dea1dfc, 0x3e0d2b2c, 0x3dfbd599,
 0x3defe47a, 0x3e000c0f, 0x3dc0073c, 0x3e2b4bb6, 0x3dd70e6f, 0x3e0662bb, 0x3dccd291, 0x3e040c84,
 0x3df2ba17, 0x3e10c38f, 0x3dd22d95, 0x3e03c750, 0x3dd1afc0, 0x3e06106b, 0x3e08e087, 0x3e086c22,
 0x3de78c00, 0x3e030b5b, 0x3da0eaac, 0x3d83496e, 0x3de6a11f, 0x3e48ccde, 0x3dba59f6, 0x3dd56862,
 0x3e4fc7a4, 0x3e032379, 0x3ddda943, 0x3de8a698, 0x3e08c2e7, 0x3df22fad, 0x3db3ce8c, 0x3d919b76,
 0x3d9e2ac3, 0x3dae22f3, 0x3ddd3ed5, 0x3e05e0f8, 0x3dfca6ca, 0x3e01322f, 0x3e493251, 0x3e03c750,
 0x3e0739b0, 0x3de6687f, 0x3dfd48cb, 0x3da09198, 0x3e0402d1, 0x3dd7d06c, 0x3e20ab2e, 0x3ddcd680,
 0x3e04a665, 0x3deb6178, 0x3da11b61, 0x3df6abde, 0x3de5668c, 0x3dcce9a3, 0x3e4f75da, 0x3e03e0bd,
 0x3d13d5a0, 0x3e129d41, 0x3def35e3, 0x3e0b7f9d, 0x3deb5c3a, 0x3e52f406, 0x3e016723, 0x3e0c93a7,
 0x3ddb1d0d, 0x3e021c8a, 0x3e10e23b, 0x3da788f6, 0x3dbeb789, 0x3df8ff76, 0x3e01083e, 0x3df75968,
 0x3dadfb94, 0x3dfd4739, 0x3da9142b, 0x3d177d7a, 0x3dd7c88e, 0x3ded2e51, 0x3d686057, 0x3debb55b,
 0x3dccd20b, 0x3d80dc26, 0x3e05d5b2, 0x3e0a47ed, 0x3ddc89f4, 0x3de1b329, 0x3dd23b36, 0x3e012d77,
 0x3dfbff8b, 0x3e0970b5, 0x3e01016d, 0x3db24fc4, 0x3e0593a3, 0x3dfe0829, 0x3df62ae5, 0x3e0b3dd1,
 0x3dd57bc8, 0x3daf33d8, 0x3deabde4, 0x3df1e109, 0x3e0355ce, 0x3dc13de0, 0x3dc960aa, 0x3e00abf7,
 0x3dbe3a70, 0x3cd380b1, 0x3e2f062d, 0x3dd97a25, 0x3e063843, 0x3d91e499, 0x3decdb7b, 0x3e0d0a24,
 0x3e077964, 0x3db0b524, 0x3de1bd1f, 0x3e058dde, 0x3df2d4d4, 0x3ddd4f9c, 0x3dee6299, 0x3dfb5526,
 0x3d95d3dd, 0x3df45e92, 0x3dc76592, 0x3dd91cd2, 0x3de6b50b, 0x3dfe0158, 0x3dc413a6, 0x3e0a8da8,
 0x3df88db0, 0x3dd015c2, 0x3df1348b, 0x3e02a8ca, 0x3dd62ae5, 0x3e0abe27, 0x3e046c76, 0x3dd69ec3,
 0x3dbd36ea, 0x3de8eb46, 0x3ded39da, 0x3dbb853a, 0x3dcec850, 0x3e05bc02, 0x3d9a99d2, 0x3e066a12,
 0x3dc264ff, 0x3e1f737e, 0x3df81388, 0x3dfc054f, 0x3df4445b, 0x3dfc08fa, 0x3de68e3f, 0x3e0faca3,
 0x3e13dea4, 0x3def7447, 0x3e0ab7dc, 0x3dff6018, 0x3dc3cb16, 0x3de0d5e9, 0x3de1d649, 0x3dfe58e6,
 0x3ddf034b, 0x3df615ec, 0x3df1fd59, 0x3e04eb14, 0x3e12888b, 0x3dfb1c86, 0x3df99cbf, 0x3d800a62,
 0x3dc4b414, 0x3defba88, 0x3dec4b4d, 0x3e0a0b1c, 0x3d9f465a, 0x3df6ce79, 0x3cecd4e0, 0x3e063ed1,
 0x3ddfea81, 0x3de1c0ca, 0x3de9a77e, 0x3dc03600, 0x3df479d5, 0x3dbe3f6b, 0x3db3ea2d, 0x3dd90fb6,
 0x3ddd9989, 0x3deccff2, 0x3de3675e, 0x3e19fc2b, 0x3dec3548, 0x3dab3a9e, 0x3dfd68c7, 0x3df5dc40,
 0x3dd5feda, 0x3ddbad3a, 0x3d965d11, 0x3d72f901, 0x3dfbc2b9, 0x3db92161, 0x3e238759, 0x3d192e1f,
 0x3db983ca, 0x3e0812be, 0x3da10518, 0x3df9799e, 0x3db2d68f, 0x3e02ebe6, 0x3e0855da, 0x3dd7585c,
 0x3dcbb92f, 0x3d97bdb4, 0x3e4c11e4, 0x3dfd0397, 0x3e0bcf0b, 0x3de7aa69, 0x3dc10533, 0x3d76ec76,
 0x3dfc9eed, 0x3e09c172, 0x3e10ae10, 0x3de90258, 0x3dea7b4e, 0x3e081345, 0x3df56755, 0x3e4d872f,
 0x3dab8edf, 0x3e00acc1, 0x3ddf42bb, 0x3e05cb79, 0x3e17baaa, 0x3dfa0be5, 0x3da0178a, 0x3df91e64,
 0x3da1084b, 0x3ddfb3fa, 0x3e002928, 0x3d96fc16, 0x3dfd46b2, 0x3e0a17f4, 0x3de5521a, 0x3df023ea,
 0x3dffd695, 0x3dfe5109, 0x3e0075b4, 0x3dce12e8, 0x3de73040, 0x3df30596, 0x3d9185f7, 0x3d0763f2,
 0x3de86df8, 0x3e129c78, 0x3e017df2, 0x3dfa5658, 0x3e0a1987, 0x3db04253, 0x3dc67d93, 0x3e183f92,
 0x3da0edb6, 0x3df595ff, 0x3ddcbee8, 0x3db4a7f8, 0x3d82883b, 0x3df76146, 0x3d9ae7d5, 0x3dfa9d1f,
 0x3df6fa83, 0x3e05f06f, 0x3daf7a41, 0x3e520685, 0x3dfc579f, 0x3ddd8d7a, 0x3d881883, 0x3dd6b767,
 0x3e131ceb, 0x3db98004, 0x3e078b7a, 0x3e00cae6, 0x3de3bb41, 0x3e0a1123, 0x3d944765, 0x3df79182,
 0x3e25e13b, 0x3dcd7b63, 0x3e3f91a3, 0x3e04aa97, 0x3e50092d, 0x3dc281c8, 0x3db7c7d3, 0x3defe9b8,
 0x3de9dae0, 0x3e0c7086, 0x3ded6c2f, 0x3de47a9e, 0x3dfed95a, 0x3e20dc33, 0x3df2a51e, 0x3ded2e51,
 0x3da21526, 0x3df4e1a4, 0x3e24534c, 0x3ddfea81, 0x3e105c46, 0x3e09e0e7, 0x3df7c0b1, 0x3dc4a622,
 0x3ddeed46, 0x3df8348f, 0x3dce379b, 0x3dfb688c, 0x3e066a12, 0x3dda4d6e, 0x3dee8d11, 0x3e488616,
 0x3db50cf2, 0x3df56191, 0x3dfe2bd0, 0x3e596356, 0x3e0c8430, 0x3e0b7e91, 0x3dade340, 0x3e4b5914,
 0x3e02220c, 0x3deb0963, 0x3d9979fc, 0x3ddec0b5, 0x3dc6b02b, 0x3deb838c, 0x3dc054e2, 0x3de3454a,
 0x3debd662, 0x3d9859bb, 0x3df1e18f, 0x3e0eb745, 0x3e39e668, 0x3dfaab47, 0x3dec2e77, 0x3df9b2c4,
 0x3dec3116, 0x3defc8b0, 0x3e06ffc1, 0x3dfbc1ad, 0x3dd2d4d4, 0x3dcdf71f, 0x3dfa921d, 0x3e0679cc,
 0x3e420b3d, 0x3deac42f, 0x3e0523f6, 0x3df69015, 0x3d8015a7, 0x3dd717df, 0x3d86494d, 0x3db87394,
 0x3df9f87f, 0x3dfb0b39, 0x3de31f04, 0x3dd71002, 0x3de2bf98, 0x3d209303, 0x3dbe51c5, 0x3df6512b,
 0x3d0ce506, 0x3de6b160, 0x3dcb8e08, 0x3e06b0d9, 0x3decc250, 0x3d97e2ee, 0x3e0dedf2, 0x3e180c31
};

// orientation array (64 x 32)
static const int orient[] =
{
 0x02, 0x02, 0x00, 0x04, 0x03, 0x01, 0x00, 0x07,
 0x02, 0x05, 0x03, 0x05, 0x03, 0x00, 0x03, 0x02,
 0x07, 0x04, 0x05, 0x07, 0x07, 0x03, 0x01, 0x07,
 0x00, 0x06, 0x02, 0x04, 0x01, 0x00, 0x00, 0x02,
 0x00, 0x03, 0x00, 0x03, 0x00, 0x04, 0x04, 0x01,
 0x04, 0x05, 0x07, 0x02, 0x07, 0x07, 0x03, 0x07,
 0x07, 0x01, 0x03, 0x01, 0x02, 0x02, 0x04, 0x01,
 0x02, 0x06, 0x01, 0x05, 0x00, 0x04, 0x02, 0x07,
 0x05, 0x00, 0x04, 0x00, 0x05, 0x03, 0x01, 0x05,
 0x01, 0x03, 0x03, 0x07, 0x03, 0x02, 0x02, 0x00,
 0x00, 0x05, 0x02, 0x06, 0x06, 0x03, 0x00, 0x05,
 0x03, 0x04, 0x04, 0x07, 0x00, 0x03, 0x06, 0x04,
 0x04, 0x04, 0x03, 0x02, 0x01, 0x00, 0x05, 0x07,
 0x04, 0x00, 0x02, 0x06, 0x03, 0x05, 0x05, 0x03,
 0x01, 0x06, 0x00, 0x04, 0x00, 0x07, 0x04, 0x05,
 0x07, 0x02, 0x05, 0x04, 0x04, 0x02, 0x02, 0x01,
 0x03, 0x04, 0x00, 0x00, 0x04, 0x03, 0x07, 0x03,
 0x02, 0x05, 0x03, 0x06, 0x05, 0x04, 0x02, 0x00,
 0x01, 0x02, 0x07, 0x01, 0x00, 0x03, 0x06, 0x02,
 0x03, 0x05, 0x05, 0x01, 0x00, 0x03, 0x06, 0x05,
 0x01, 0x07, 0x03, 0x02, 0x03, 0x00, 0x05, 0x00,
 0x06, 0x05, 0x06, 0x00, 0x04, 0x04, 0x07, 0x07,
 0x00, 0x01, 0x01, 0x01, 0x02, 0x04, 0x05, 0x02,
 0x06, 0x03, 0x01, 0x05, 0x03, 0x05, 0x02, 0x05,
 0x03, 0x07, 0x03, 0x05, 0x04, 0x02, 0x07, 0x00,
 0x00, 0x03, 0x04, 0x07, 0x00, 0x00, 0x05, 0x06,
 0x04, 0x01, 0x02, 0x03, 0x01, 0x03, 0x03, 0x02,
 0x05, 0x02, 0x02, 0x01, 0x05, 0x06, 0x07, 0x05,
 0x07, 0x03, 0x05, 0x00, 0x00, 0x05, 0x03, 0x05,
 0x07, 0x00, 0x02, 0x04, 0x04, 0x04, 0x03, 0x05,
 0x06, 0x04, 0x02, 0x04, 0x01, 0x00, 0x04, 0x01,
 0x06, 0x07, 0x04, 0x04, 0x06, 0x06, 0x02, 0x05,
 0x01, 0x00, 0x01, 0x00, 0x05, 0x03, 0x03, 0x02,
 0x04, 0x07, 0x05, 0x04, 0x07, 0x07, 0x03, 0x03,
 0x04, 0x01, 0x06, 0x00, 0x02, 0x04, 0x00, 0x07,
 0x01, 0x04, 0x07, 0x00, 0x06, 0x00, 0x06, 0x05,
 0x07, 0x03, 0x05, 0x03, 0x00, 0x06, 0x04, 0x04,
 0x02, 0x07, 0x05, 0x03, 0x03, 0x06, 0x05, 0x00,
 0x07, 0x04, 0x01, 0x05, 0x06, 0x01, 0x04, 0x02,
 0x05, 0x03, 0x04, 0x01, 0x02, 0x06, 0x07, 0x01,
 0x05, 0x00, 0x05, 0x07, 0x03, 0x04, 0x07, 0x04,
 0x03, 0x03, 0x04, 0x01, 0x04, 0x06, 0x03, 0x07,
 0x00, 0x05, 0x01, 0x02, 0x01, 0x06, 0x06, 0x06,
 0x05, 0x05, 0x06, 0x02, 0x00, 0x06, 0x03, 0x05,
 0x07, 0x07, 0x00, 0x04, 0x07, 0x00, 0x01, 0x05,
 0x04, 0x00, 0x02, 0x03, 0x04, 0x06, 0x03, 0x00,
 0x04, 0x03, 0x05, 0x00, 0x00, 0x03, 0x06, 0x05,
 0x01, 0x06, 0x01, 0x02, 0x02, 0x01, 0x01, 0x00,
 0x04, 0x04, 0x03, 0x00, 0x07, 0x02, 0x03, 0x07,
 0x06, 0x04, 0x00, 0x07, 0x07, 0x02, 0x05, 0x06,
 0x05, 0x05, 0x01, 0x06, 0x01, 0x02, 0x06, 0x04,
 0x06, 0x05, 0x01, 0x02, 0x04, 0x05, 0x07, 0x05,
 0x00, 0x00, 0x04, 0x02, 0x04, 0x00, 0x05, 0x07,
 0x05, 0x06, 0x06, 0x03, 0x04, 0x00, 0x06, 0x07,
 0x07, 0x02, 0x03, 0x05, 0x06, 0x07, 0x02, 0x00,
 0x07, 0x00, 0x05, 0x04, 0x03, 0x01, 0x07, 0x00,
 0x00, 0x01, 0x03, 0x05, 0x05, 0x00, 0x06, 0x04,
 0x00, 0x06, 0x05, 0x05, 0x01, 0x02, 0x04, 0x02,
 0x01, 0x07, 0x02, 0x04, 0x00, 0x00, 0x00, 0x01,
 0x03, 0x05, 0x02, 0x06, 0x03, 0x00, 0x06, 0x01,
 0x02, 0x02, 0x03, 0x05, 0x00, 0x04, 0x03, 0x02,
 0x06, 0x07, 0x06, 0x00, 0x05, 0x03, 0x05, 0x06,
 0x00, 0x02, 0x05, 0x05, 0x04, 0x02, 0x03, 0x07,
 0x04, 0x06, 0x01, 0x05, 0x07, 0x00, 0x01, 0x05,
 0x03, 0x04, 0x07, 0x02, 0x05, 0x07, 0x00, 0x00,
 0x04, 0x05, 0x02, 0x02, 0x02, 0x02, 0x06, 0x05,
 0x04, 0x07, 0x07, 0x01, 0x06, 0x01, 0x00, 0x06,
 0x05, 0x00, 0x07, 0x04, 0x04, 0x06, 0x06, 0x01,
 0x04, 0x03, 0x05, 0x04, 0x00, 0x07, 0x04, 0x07,
 0x06, 0x02, 0x01, 0x06, 0x04, 0x02, 0x00, 0x07,
 0x06, 0x06, 0x04, 0x03, 0x01, 0x02, 0x06, 0x01,
 0x05, 0x00, 0x01, 0x05, 0x07, 0x00, 0x05, 0x05,
 0x06, 0x03, 0x04, 0x01, 0x02, 0x02, 0x00, 0x04,
 0x03, 0x07, 0x06, 0x03, 0x05, 0x04, 0x02, 0x05,
 0x04, 0x00, 0x05, 0x02, 0x00, 0x01, 0x01, 0x04,
 0x01, 0x06, 0x06, 0x05, 0x07, 0x06, 0x05, 0x01,
 0x06, 0x01, 0x03, 0x02, 0x06, 0x04, 0x06, 0x07,
 0x02, 0x01, 0x05, 0x05, 0x07, 0x02, 0x00, 0x01,
 0x04, 0x03, 0x06, 0x07, 0x06, 0x00, 0x00, 0x06,
 0x07, 0x04, 0x00, 0x03, 0x04, 0x01, 0x02, 0x02,
 0x07, 0x05, 0x00, 0x01, 0x02, 0x05, 0x03, 0x00,
 0x03, 0x02, 0x02, 0x01, 0x07, 0x06, 0x04, 0x06,
 0x05, 0x07, 0x01, 0x05, 0x05, 0x04, 0x02, 0x00,
 0x03, 0x05, 0x01, 0x03, 0x01, 0x01, 0x00, 0x01,
 0x03, 0x05, 0x07, 0x07, 0x05, 0x04, 0x03, 0x05,
 0x02, 0x00, 0x04, 0x04, 0x02, 0x05, 0x06, 0x06,
 0x02, 0x01, 0x04, 0x00, 0x00, 0x01, 0x07, 0x06,
 0x04, 0x01, 0x00, 0x07, 0x03, 0x06, 0x02, 0x06,
 0x06, 0x00, 0x05, 0x03, 0x06, 0x07, 0x00, 0x04,
 0x07, 0x05, 0x05, 0x04, 0x06, 0x02, 0x03, 0x03,
 0x04, 0x05, 0x04, 0x02, 0x00, 0x01, 0x06, 0x01,
 0x06, 0x06, 0x01, 0x04, 0x07, 0x07, 0x00, 0x02,
 0x07, 0x07, 0x06, 0x07, 0x03, 0x00, 0x05, 0x05,
 0x00, 0x02, 0x00, 0x04, 0x01, 0x06, 0x01, 0x00,
 0x00, 0x00, 0x03, 0x01, 0x04, 0x06, 0x02, 0x01,
 0x05, 0x05, 0x00, 0x06, 0x01, 0x05, 0x00, 0x03,
 0x05, 0x03, 0x05, 0x00, 0x03, 0x04, 0x07, 0x07,
 0x06, 0x06, 0x07, 0x01, 0x00, 0x06, 0x07, 0x02,
 0x04, 0x00, 0x05, 0x01, 0x06, 0x01, 0x06, 0x06,
 0x02, 0x06, 0x01, 0x01, 0x07, 0x05, 0x01, 0x05,
 0x04, 0x00, 0x00, 0x04, 0x06, 0x07, 0x06, 0x00,
 0x03, 0x05, 0x03, 0x02, 0x04, 0x07, 0x01, 0x06,
 0x04, 0x02, 0x07, 0x00, 0x00, 0x01, 0x05, 0x01,
 0x06, 0x02, 0x03, 0x06, 0x06, 0x00, 0x06, 0x00,
 0x00, 0x04, 0x03, 0x04, 0x02, 0x06, 0x00, 0x07,
 0x06, 0x04, 0x06, 0x07, 0x06, 0x04, 0x01, 0x03,
 0x07, 0x05, 0x07, 0x05, 0x02, 0x00, 0x00, 0x05,
 0x01, 0x02, 0x04, 0x07, 0x04, 0x02, 0x02, 0x05,
 0x02, 0x07, 0x06, 0x04, 0x05, 0x04, 0x06, 0x03,
 0x06, 0x07, 0x06, 0x05, 0x03, 0x04, 0x00, 0x05,
 0x00, 0x03, 0x04, 0x02, 0x05, 0x01, 0x06, 0x04,
 0x01, 0x00, 0x01, 0x06, 0x04, 0x02, 0x00, 0x06,
 0x02, 0x04, 0x02, 0x01, 0x03, 0x05, 0x00, 0x02,
 0x07, 0x05, 0x06, 0x04, 0x01, 0x05, 0x00, 0x07,
 0x04, 0x04, 0x03, 0x05, 0x00, 0x05, 0x06, 0x01,
 0x01, 0x06, 0x06, 0x01, 0x01, 0x02, 0x00, 0x06,
 0x07, 0x04, 0x07, 0x05, 0x00, 0x01, 0x04, 0x04,
 0x02, 0x01, 0x04, 0x00, 0x01, 0x07, 0x02, 0x03,
 0x05, 0x04, 0x01, 0x00, 0x02, 0x04, 0x02, 0x07,
 0x06, 0x01, 0x05, 0x06, 0x06, 0x03, 0x04, 0x02,
 0x00, 0x04, 0x04, 0x01, 0x02, 0x04, 0x06, 0x05,
 0x00, 0x02, 0x05, 0x04, 0x06, 0x00, 0x00, 0x05,
 0x06, 0x01, 0x06, 0x05, 0x04, 0x02, 0x01, 0x03,
 0x00, 0x01, 0x06, 0x06, 0x00, 0x03, 0x01, 0x02,
 0x06, 0x04, 0x03, 0x01, 0x06, 0x06, 0x01, 0x05,
 0x05, 0x07, 0x02, 0x01, 0x07, 0x05, 0x07, 0x04,
 0x01, 0x00, 0x06, 0x04, 0x03, 0x02, 0x02, 0x07,
 0x05, 0x00, 0x01, 0x06, 0x03, 0x06, 0x00, 0x00,
 0x07, 0x03, 0x04, 0x02, 0x01, 0x03, 0x07, 0x07,
 0x01, 0x05, 0x05, 0x03, 0x07, 0x06, 0x02, 0x07,
 0x02, 0x07, 0x03, 0x00, 0x07, 0x01, 0x02, 0x06,
 0x00, 0x05, 0x05, 0x07, 0x06, 0x01, 0x07, 0x05,
 0x01, 0x04, 0x06, 0x04, 0x05, 0x04, 0x01, 0x00,
 0x07, 0x05, 0x05, 0x02, 0x04, 0x04, 0x02, 0x06,
 0x01, 0x07, 0x07, 0x07, 0x01, 0x01, 0x07, 0x00,
 0x07, 0x05, 0x06, 0x01, 0x06, 0x02, 0x05, 0x06,
 0x03, 0x06, 0x04, 0x00, 0x03, 0x07, 0x05, 0x04,
 0x05, 0x02, 0x01, 0x01, 0x01, 0x04, 0x07, 0x06,
 0x03, 0x07, 0x02, 0x06, 0x07, 0x03, 0x01, 0x00,
 0x05, 0x01, 0x01, 0x02, 0x06, 0x05, 0x01, 0x06,
 0x04, 0x04, 0x05, 0x03, 0x06, 0x07, 0x04, 0x02,
 0x01, 0x00, 0x04, 0x00, 0x03, 0x06, 0x01, 0x01,
 0x07, 0x01, 0x04, 0x00, 0x00, 0x02, 0x06, 0x06,
 0x01, 0x07, 0x04, 0x00, 0x06, 0x07, 0x05, 0x03,
 0x01, 0x07, 0x01, 0x05, 0x04, 0x04, 0x04, 0x06,
 0x07, 0x01, 0x04, 0x07, 0x05, 0x05, 0x01, 0x05,
 0x02, 0x04, 0x07, 0x06, 0x01, 0x04, 0x02, 0x03,
 0x00, 0x00, 0x01, 0x05, 0x06, 0x04, 0x02, 0x06,
 0x02, 0x04, 0x02, 0x06, 0x04, 0x06, 0x05, 0x07,
 0x06, 0x02, 0x02, 0x05, 0x01, 0x00, 0x05, 0x03,
 0x00, 0x04, 0x01, 0x01, 0x01, 0x03, 0x06, 0x07,
 0x06, 0x05, 0x07, 0x07, 0x04, 0x01, 0x03, 0x05,
 0x05, 0x01, 0x00, 0x02, 0x03, 0x06, 0x02, 0x07,
 0x02, 0x01, 0x02, 0x05, 0x01, 0x01, 0x04, 0x01,
 0x01, 0x07, 0x04, 0x06, 0x00, 0x02, 0x01, 0x07,
 0x07, 0x05, 0x00, 0x06, 0x07, 0x05, 0x02, 0x03,
 0x03, 0x00, 0x03, 0x04, 0x02, 0x03, 0x05, 0x04,
 0x03, 0x06, 0x05, 0x05, 0x07, 0x01, 0x07, 0x04,
 0x04, 0x04, 0x01, 0x01, 0x06, 0x07, 0x02, 0x02,
 0x06, 0x06, 0x06, 0x05, 0x01, 0x02, 0x07, 0x03,
 0x06, 0x05, 0x04, 0x07, 0x02, 0x07, 0x02, 0x00,
 0x01, 0x05, 0x03, 0x01, 0x01, 0x00, 0x06, 0x02,
 0x06, 0x06, 0x04, 0x02, 0x04, 0x02, 0x07, 0x07,
 0x07, 0x00, 0x03, 0x04, 0x01, 0x00, 0x01, 0x05,
 0x00, 0x05, 0x04, 0x06, 0x01, 0x01, 0x05, 0x06,
 0x04, 0x06, 0x01, 0x01, 0x06, 0x05, 0x00, 0x01,
 0x05, 0x04, 0x06, 0x03, 0x07, 0x01, 0x04, 0x06,
 0x05, 0x01, 0x01, 0x04, 0x03, 0x00, 0x06, 0x02,
 0x01, 0x04, 0x06, 0x04, 0x02, 0x04, 0x06, 0x01,
 0x04, 0x04, 0x01, 0x01, 0x03, 0x04, 0x01, 0x01,
 0x02, 0x07, 0x06, 0x03, 0x05, 0x01, 0x05, 0x01,
 0x03, 0x02, 0x00, 0x06, 0x04, 0x06, 0x01, 0x03,
 0x06, 0x06, 0x06, 0x03, 0x01, 0x06, 0x06, 0x06,
 0x01, 0x03, 0x06, 0x05, 0x00, 0x00, 0x01, 0x06,
 0x01, 0x04, 0x00, 0x05, 0x02, 0x02, 0x06, 0x06,
 0x02, 0x00, 0x06, 0x01, 0x01, 0x06, 0x01, 0x05,
 0x04, 0x04, 0x06, 0x05, 0x04, 0x03, 0x06, 0x06,
 0x05, 0x07, 0x03, 0x04, 0x05, 0x01, 0x05, 0x06,
 0x04, 0x04, 0x07, 0x03, 0x06, 0x01, 0x01, 0x01,
 0x04, 0x05, 0x06, 0x01, 0x00, 0x02, 0x00, 0x04,
 0x00, 0x05, 0x04, 0x05, 0x04, 0x01, 0x06, 0x06,
 0x06, 0x04, 0x04, 0x00, 0x07, 0x01, 0x06, 0x03,
 0x06, 0x07, 0x01, 0x01, 0x02, 0x00, 0x05, 0x02,
 0x01, 0x07, 0x02, 0x03, 0x06, 0x01, 0x06, 0x01,
 0x03, 0x01, 0x05, 0x03, 0x05, 0x06, 0x00, 0x01,
 0x05, 0x01, 0x01, 0x00, 0x01, 0x04, 0x01, 0x01,
 0x07, 0x06, 0x07, 0x04, 0x06, 0x03, 0x07, 0x04,
 0x02, 0x05, 0x00, 0x06, 0x02, 0x01, 0x01, 0x07,
 0x06, 0x05, 0x03, 0x04, 0x06, 0x00, 0x00, 0x01,
 0x05, 0x00, 0x06, 0x01, 0x07, 0x01, 0x04, 0x06,
 0x03, 0x07, 0x05, 0x07, 0x06, 0x01, 0x01, 0x00,
 0x04, 0x02, 0x01, 0x05, 0x07, 0x06, 0x01, 0x02,
 0x05, 0x06, 0x05, 0x03, 0x04, 0x05, 0x00, 0x00,
 0x01, 0x07, 0x01, 0x06, 0x00, 0x00, 0x02, 0x06,
 0x01, 0x04, 0x06, 0x02, 0x05, 0x00, 0x06, 0x05,
 0x00, 0x04, 0x07, 0x05, 0x06, 0x01, 0x01, 0x01,
 0x01, 0x03, 0x02, 0x05, 0x05, 0x01, 0x02, 0x04,
 0x00, 0x05, 0x01, 0x01, 0x06, 0x01, 0x02, 0x00,
 0x06, 0x07, 0x01, 0x01, 0x05, 0x04, 0x01, 0x01,
 0x04, 0x04, 0x02, 0x05, 0x04, 0x04, 0x03, 0x06,
 0x03, 0x01, 0x00, 0x05, 0x04, 0x05, 0x05, 0x01,
 0x04, 0x02, 0x00, 0x01, 0x05, 0x01, 0x00, 0x06,
 0x01, 0x01, 0x05, 0x01, 0x01, 0x06, 0x06, 0x04,
 0x06, 0x05, 0x02, 0x01, 0x01, 0x02, 0x01, 0x07,
 0x00, 0x05, 0x07, 0x00, 0x05, 0x04, 0x07, 0x03,
 0x01, 0x06, 0x06, 0x03, 0x01, 0x05, 0x00, 0x01,
 0x06, 0x06, 0x04, 0x00, 0x01, 0x00, 0x01, 0x05,
 0x06, 0x06, 0x01, 0x01, 0x01, 0x04, 0x01, 0x01,
 0x01, 0x01, 0x04, 0x04, 0x06, 0x01, 0x07, 0x00,
 0x01, 0x05, 0x04, 0x06, 0x05, 0x06, 0x07, 0x06,
 0x01, 0x03, 0x04, 0x07, 0x01, 0x06, 0x04, 0x00,
 0x06, 0x00, 0x05, 0x06, 0x01, 0x07, 0x06, 0x01,
 0x01, 0x03, 0x03, 0x03, 0x06, 0x00, 0x04, 0x05,
 0x07, 0x00, 0x05, 0x03, 0x07, 0x05, 0x02, 0x04,
 0x05, 0x05, 0x06, 0x06, 0x06, 0x01, 0x07, 0x01,
 0x01, 0x01, 0x06, 0x02, 0x01, 0x05, 0x00, 0x01,
 0x01, 0x01, 0x04, 0x06, 0x01, 0x05, 0x07, 0x00,
 0x03, 0x00, 0x01, 0x07, 0x01, 0x00, 0x06, 0x00,
 0x05, 0x01, 0x01, 0x04, 0x05, 0x03, 0x06, 0x01,
 0x05, 0x01, 0x05, 0x04, 0x00, 0x03, 0x02, 0x05,
 0x02, 0x03, 0x07, 0x01, 0x04, 0x05, 0x06, 0x04,
 0x01, 0x00, 0x01, 0x05, 0x06, 0x06, 0x01, 0x01,
 0x05, 0x04, 0x01, 0x00, 0x06, 0x04, 0x05, 0x01,
 0x02, 0x05, 0x00, 0x04, 0x06, 0x03, 0x06, 0x01,
 0x01, 0x05, 0x01, 0x04, 0x06, 0x05, 0x06, 0x06,
 0x02, 0x01, 0x05, 0x01, 0x01, 0x01, 0x05, 0x07,
 0x06, 0x06, 0x01, 0x00, 0x02, 0x07, 0x07, 0x01,
 0x04, 0x06, 0x02, 0x01, 0x02, 0x07, 0x03, 0x01,
 0x05, 0x07, 0x05, 0x06, 0x01, 0x04, 0x06, 0x07,
 0x00, 0x01, 0x01, 0x04, 0x02, 0x05, 0x06, 0x01,
 0x05, 0x01, 0x06, 0x01, 0x06, 0x01, 0x07, 0x04,
 0x01, 0x01, 0x01, 0x00, 0x05, 0x01, 0x04, 0x07,
 0x05, 0x06, 0x05, 0x05, 0x01, 0x02, 0x05, 0x04,
 0x03, 0x01, 0x01, 0x07, 0x04, 0x03, 0x07, 0x01,
 0x04, 0x01, 0x05, 0x02, 0x00, 0x02, 0x00, 0x02,
 0x05, 0x06, 0x03, 0x00, 0x04, 0x01, 0x01, 0x01,
 0x02, 0x03, 0x00, 0x06, 0x03, 0x07, 0x03, 0x02,
 0x03, 0x06, 0x01, 0x06, 0x00, 0x06, 0x01, 0x00,
 0x02, 0x03, 0x01, 0x06, 0x00, 0x00, 0x05, 0x05,
 0x05, 0x05, 0x06, 0x04, 0x01, 0x07, 0x06, 0x04,
 0x01, 0x01, 0x01, 0x05, 0x07, 0x07, 0x02, 0x03,
 0x05, 0x01, 0x03, 0x01, 0x06, 0x02, 0x06, 0x07,
 0x04, 0x01, 0x06, 0x04, 0x05, 0x00, 0x03, 0x01,
 0x01, 0x07, 0x05, 0x07, 0x01, 0x07, 0x05, 0x01,
 0x07, 0x06, 0x01, 0x06, 0x00, 0x01, 0x05, 0x06,
 0x00, 0x01, 0x00, 0x06, 0x00, 0x05, 0x04, 0x06,
 0x00, 0x00, 0x05, 0x04, 0x01, 0x07, 0x01, 0x00,
 0x03, 0x01, 0x07, 0x01, 0x00, 0x01, 0x00, 0x01,
 0x01, 0x01, 0x05, 0x03, 0x06, 0x01, 0x07, 0x07,
 0x04, 0x01, 0x01, 0x00, 0x00, 0x01, 0x05, 0x00,
 0x06, 0x00, 0x04, 0x06, 0x01, 0x07, 0x04, 0x06,
 0x01, 0x04, 0x06, 0x01, 0x00, 0x01, 0x04, 0x00,
 0x05, 0x05, 0x00, 0x01, 0x05, 0x05, 0x04, 0x00,
 0x00, 0x07, 0x01, 0x06, 0x04, 0x01, 0x03, 0x05,
 0x07, 0x00, 0x06, 0x06, 0x00, 0x03, 0x05, 0x01,
 0x02, 0x03, 0x05, 0x01, 0x01, 0x02, 0x07, 0x07
};

// Y min array (64 x 32)
static const int y_min[] =
{
 0x05, 0x0d, 0x0e, 0x0c, 0x04, 0x04, 0x09, 0x06,
 0x01, 0x07, 0x0d, 0x0c, 0x10, 0x0f, 0x02, 0x02,
 0x06, 0x0f, 0x0c, 0x03, 0x0d, 0x0b, 0x02, 0x0c,
 0x01, 0x01, 0x02, 0x05, 0x0b, 0x0d, 0x14, 0x07,
 0x0d, 0x09, 0x08, 0x02, 0x0f, 0x0d, 0x09, 0x03,
 0x02, 0x07, 0x0c, 0x09, 0x0e, 0x05, 0x10, 0x0f,
 0x0d, 0x06, 0x07, 0x0e, 0x10, 0x00, 0x0d, 0x03,
 0x0b, 0x03, 0x0a, 0x0e, 0x07, 0x07, 0x0c, 0x0d,
 0x05, 0x0a, 0x02, 0x0d, 0x07, 0x07, 0x01, 0x02,
 0x01, 0x04, 0x03, 0x0d, 0x0b, 0x04, 0x07, 0x0a,
 0x06, 0x09, 0x00, 0x05, 0x0d, 0x0e, 0x0f, 0x08,
 0x10, 0x0c, 0x0f, 0x08, 0x11, 0x0c, 0x03, 0x07,
 0x0c, 0x0f, 0x04, 0x05, 0x02, 0x0a, 0x02, 0x0d,
 0x0c, 0x0f, 0x04, 0x01, 0x08, 0x0a, 0x11, 0x02,
 0x03, 0x03, 0x0d, 0x13, 0x08, 0x01, 0x13, 0x07,
 0x06, 0x0f, 0x0d, 0x0f, 0x01, 0x10, 0x0c, 0x0b,
 0x04, 0x03, 0x06, 0x07, 0x03, 0x03, 0x0c, 0x04,
 0x05, 0x07, 0x09, 0x05, 0x03, 0x05, 0x06, 0x0b,
 0x10, 0x08, 0x11, 0x00, 0x10, 0x00, 0x01, 0x07,
 0x09, 0x0f, 0x01, 0x0c, 0x10, 0x0c, 0x03, 0x05,
 0x04, 0x03, 0x07, 0x0a, 0x03, 0x07, 0x0a, 0x0f,
 0x01, 0x06, 0x05, 0x05, 0x0e, 0x01, 0x08, 0x0f,
 0x0e, 0x0c, 0x09, 0x00, 0x0a, 0x0e, 0x07, 0x10,
 0x08, 0x07, 0x03, 0x0e, 0x07, 0x02, 0x07, 0x0a,
 0x0c, 0x0d, 0x02, 0x07, 0x0e, 0x06, 0x0a, 0x02,
 0x09, 0x02, 0x0c, 0x0e, 0x11, 0x07, 0x04, 0x07,
 0x0c, 0x02, 0x0e, 0x0c, 0x06, 0x04, 0x09, 0x0e,
 0x01, 0x03, 0x0f, 0x05, 0x11, 0x0b, 0x10, 0x05,
 0x06, 0x07, 0x0f, 0x0b, 0x09, 0x07, 0x00, 0x01,
 0x0c, 0x0d, 0x07, 0x04, 0x06, 0x0c, 0x0c, 0x10,
 0x0f, 0x0f, 0x06, 0x0e, 0x0d, 0x07, 0x0d, 0x0e,
 0x01, 0x07, 0x09, 0x07, 0x01, 0x04, 0x12, 0x03,
 0x08, 0x09, 0x02, 0x08, 0x06, 0x05, 0x0d, 0x0c,
 0x04, 0x0c, 0x06, 0x0f, 0x0d, 0x08, 0x0d, 0x01,
 0x02, 0x04, 0x00, 0x05, 0x08, 0x0c, 0x0a, 0x10,
 0x0c, 0x01, 0x09, 0x0f, 0x08, 0x12, 0x05, 0x0c,
 0x02, 0x04, 0x06, 0x0c, 0x0e, 0x0a, 0x09, 0x06,
 0x07, 0x09, 0x0c, 0x00, 0x12, 0x00, 0x09, 0x06,
 0x03, 0x00, 0x0f, 0x0c, 0x03, 0x00, 0x10, 0x0b,
 0x09, 0x09, 0x0b, 0x09, 0x0a, 0x0d, 0x01, 0x0b,
 0x0d, 0x0c, 0x06, 0x0e, 0x04, 0x13, 0x07, 0x0c,
 0x13, 0x0c, 0x04, 0x08, 0x08, 0x0c, 0x01, 0x01,
 0x0b, 0x0c, 0x07, 0x06, 0x06, 0x00, 0x01, 0x0d,
 0x03, 0x0e, 0x07, 0x04, 0x11, 0x05, 0x02, 0x13,
 0x0f, 0x04, 0x0d, 0x0c, 0x10, 0x0b, 0x05, 0x0e,
 0x04, 0x0a, 0x06, 0x06, 0x08, 0x03, 0x04, 0x04,
 0x15, 0x10, 0x01, 0x0f, 0x05, 0x0c, 0x01, 0x0c,
 0x07, 0x03, 0x0c, 0x01, 0x13, 0x0a, 0x0b, 0x13,
 0x09, 0x10, 0x0a, 0x05, 0x0c, 0x08, 0x01, 0x02,
 0x06, 0x04, 0x07, 0x0e, 0x11, 0x0e, 0x06, 0x01,
 0x04, 0x09, 0x03, 0x09, 0x07, 0x02, 0x0b, 0x0a,
 0x00, 0x0d, 0x03, 0x0f, 0x13, 0x0d, 0x09, 0x12,
 0x0f, 0x0a, 0x00, 0x00, 0x0b, 0x0d, 0x05, 0x0d,
 0x11, 0x02, 0x02, 0x01, 0x0d, 0x08, 0x07, 0x06,
 0x12, 0x12, 0x13, 0x0e, 0x0e, 0x0d, 0x00, 0x0f,
 0x01, 0x0b, 0x12, 0x14, 0x08, 0x0f, 0x12, 0x02,
 0x0b, 0x0d, 0x07, 0x0a, 0x09, 0x0e, 0x04, 0x02,
 0x0e, 0x0d, 0x0e, 0x02, 0x07, 0x05, 0x09, 0x0a,
 0x06, 0x0d, 0x01, 0x0b, 0x01, 0x0c, 0x07, 0x04,
 0x14, 0x0f, 0x10, 0x0e, 0x07, 0x07, 0x01, 0x04,
 0x02, 0x0d, 0x0d, 0x02, 0x0c, 0x07, 0x02, 0x0d,
 0x00, 0x10, 0x07, 0x10, 0x06, 0x06, 0x09, 0x02,
 0x10, 0x03, 0x10, 0x0f, 0x14, 0x13, 0x0e, 0x05,
 0x0b, 0x0a, 0x0c, 0x05, 0x10, 0x06, 0x06, 0x10,
 0x02, 0x0d, 0x0d, 0x07, 0x0c, 0x0d, 0x05, 0x10,
 0x0d, 0x0b, 0x0b, 0x0b, 0x05, 0x01, 0x07, 0x05,
 0x0e, 0x12, 0x0c, 0x09, 0x08, 0x05, 0x02, 0x09,
 0x0a, 0x10, 0x0d, 0x12, 0x0f, 0x07, 0x02, 0x0a,
 0x0a, 0x0e, 0x0d, 0x0e, 0x0f, 0x0b, 0x0b, 0x0c,
 0x05, 0x0a, 0x01, 0x0b, 0x00, 0x0a, 0x08, 0x0d,
 0x00, 0x0b, 0x05, 0x06, 0x0f, 0x04, 0x0f, 0x0c,
 0x0b, 0x0d, 0x0e, 0x0e, 0x12, 0x01, 0x08, 0x10,
 0x03, 0x07, 0x0d, 0x04, 0x0d, 0x0c, 0x0d, 0x04,
 0x0d, 0x07, 0x0b, 0x0f, 0x08, 0x0f, 0x07, 0x0f,
 0x0b, 0x08, 0x0f, 0x00, 0x0a, 0x08, 0x00, 0x08,
 0x0b, 0x0b, 0x03, 0x06, 0x0f, 0x0f, 0x12, 0x06,
 0x04, 0x02, 0x05, 0x0f, 0x02, 0x13, 0x0e, 0x00,
 0x07, 0x08, 0x0a, 0x0f, 0x11, 0x0f, 0x0c, 0x01,
 0x0d, 0x0e, 0x09, 0x03, 0x03, 0x0c, 0x12, 0x09,
 0x0b, 0x11, 0x10, 0x03, 0x07, 0x02, 0x14, 0x11,
 0x0d, 0x0c, 0x0d, 0x07, 0x10, 0x0b, 0x10, 0x0e,
 0x06, 0x0b, 0x05, 0x08, 0x08, 0x0d, 0x0e, 0x01,
 0x0a, 0x03, 0x0d, 0x08, 0x0f, 0x01, 0x0e, 0x04,
 0x0d, 0x11, 0x10, 0x00, 0x07, 0x06, 0x0a, 0x09,
 0x0b, 0x05, 0x09, 0x0f, 0x0b, 0x0d, 0x0c, 0x02,
 0x06, 0x0d, 0x01, 0x03, 0x05, 0x02, 0x03, 0x04,
 0x04, 0x0b, 0x0e, 0x13, 0x03, 0x03, 0x07, 0x0c,
 0x14, 0x0c, 0x0a, 0x10, 0x0d, 0x00, 0x13, 0x11,
 0x05, 0x0c, 0x02, 0x0f, 0x0f, 0x01, 0x14, 0x12,
 0x0e, 0x0a, 0x07, 0x04, 0x02, 0x08, 0x05, 0x06,
 0x02, 0x0a, 0x0d, 0x14, 0x10, 0x07, 0x04, 0x01,
 0x0c, 0x00, 0x0a, 0x14, 0x0a, 0x02, 0x14, 0x11,
 0x0f, 0x04, 0x0d, 0x0d, 0x0b, 0x06, 0x09, 0x06,
 0x08, 0x06, 0x0c, 0x0d, 0x10, 0x05, 0x0a, 0x0f,
 0x10, 0x0e, 0x09, 0x12, 0x02, 0x05, 0x0f, 0x03,
 0x12, 0x0b, 0x0b, 0x07, 0x05, 0x06, 0x10, 0x0d,
 0x05, 0x05, 0x0a, 0x0b, 0x02, 0x0e, 0x0a, 0x0b,
 0x0d, 0x01, 0x0f, 0x0b, 0x13, 0x0e, 0x10, 0x12,
 0x03, 0x09, 0x0d, 0x04, 0x06, 0x06, 0x07, 0x0c,
 0x05, 0x05, 0x0f, 0x08, 0x12, 0x00, 0x0c, 0x06,
 0x11, 0x0f, 0x06, 0x0d, 0x06, 0x12, 0x03, 0x09,
 0x0f, 0x09, 0x05, 0x11, 0x13, 0x0d, 0x03, 0x0a,
 0x02, 0x04, 0x06, 0x0f, 0x15, 0x0e, 0x06, 0x0b,
 0x03, 0x09, 0x13, 0x01, 0x02, 0x08, 0x0d, 0x08,
 0x0d, 0x0f, 0x05, 0x0f, 0x11, 0x09, 0x09, 0x0e,
 0x0a, 0x0d, 0x09, 0x0c, 0x00, 0x0b, 0x04, 0x01,
 0x13, 0x0d, 0x0d, 0x0f, 0x0f, 0x12, 0x0d, 0x06,
 0x02, 0x10, 0x12, 0x14, 0x0d, 0x10, 0x08, 0x0e,
 0x0f, 0x10, 0x00, 0x06, 0x08, 0x0d, 0x01, 0x01,
 0x0c, 0x0d, 0x03, 0x09, 0x0e, 0x10, 0x13, 0x09,
 0x0e, 0x08, 0x03, 0x11, 0x03, 0x10, 0x0c, 0x0c,
 0x09, 0x00, 0x07, 0x00, 0x0b, 0x02, 0x13, 0x08,
 0x0d, 0x0a, 0x0d, 0x04, 0x0a, 0x04, 0x10, 0x0e,
 0x0a, 0x0f, 0x08, 0x0a, 0x04, 0x0d, 0x0c, 0x04,
 0x0c, 0x0d, 0x11, 0x0a, 0x15, 0x10, 0x03, 0x0c,
 0x02, 0x10, 0x0c, 0x01, 0x0c, 0x0f, 0x14, 0x01,
 0x08, 0x03, 0x11, 0x0b, 0x0d, 0x08, 0x0e, 0x03,
 0x06, 0x07, 0x02, 0x13, 0x0b, 0x02, 0x10, 0x0d,
 0x09, 0x0e, 0x07, 0x0c, 0x09, 0x01, 0x09, 0x0a,
 0x0b, 0x0c, 0x10, 0x0e, 0x01, 0x04, 0x11, 0x11,
 0x14, 0x10, 0x0e, 0x0a, 0x10, 0x0f, 0x03, 0x02,
 0x0d, 0x0f, 0x10, 0x01, 0x08, 0x0b, 0x0a, 0x00,
 0x10, 0x02, 0x09, 0x0f, 0x12, 0x0d, 0x07, 0x0b,
 0x15, 0x0f, 0x0b, 0x0e, 0x14, 0x0a, 0x02, 0x11,
 0x09, 0x04, 0x00, 0x03, 0x02, 0x0a, 0x0c, 0x03,
 0x0e, 0x0f, 0x09, 0x08, 0x06, 0x0f, 0x11, 0x0b,
 0x09, 0x12, 0x0a, 0x01, 0x01, 0x0c, 0x0a, 0x0c,
 0x10, 0x13, 0x0e, 0x04, 0x09, 0x0f, 0x01, 0x08,
 0x0c, 0x0d, 0x0e, 0x10, 0x00, 0x02, 0x10, 0x0e,
 0x09, 0x06, 0x0f, 0x01, 0x00, 0x05, 0x0d, 0x14,
 0x0f, 0x11, 0x0c, 0x13, 0x15, 0x0b, 0x05, 0x05,
 0x0e, 0x0e, 0x14, 0x04, 0x08, 0x05, 0x03, 0x0e,
 0x03, 0x12, 0x06, 0x0e, 0x0c, 0x06, 0x0a, 0x11,
 0x0c, 0x0e, 0x0e, 0x0f, 0x01, 0x0c, 0x0e, 0x02,
 0x0f, 0x0d, 0x01, 0x01, 0x12, 0x11, 0x16, 0x03,
 0x00, 0x0e, 0x0e, 0x04, 0x09, 0x15, 0x02, 0x0b,
 0x0c, 0x06, 0x0e, 0x0e, 0x01, 0x04, 0x11, 0x0d,
 0x10, 0x0f, 0x09, 0x09, 0x00, 0x03, 0x13, 0x04,
 0x0a, 0x0d, 0x0c, 0x04, 0x0f, 0x0b, 0x0b, 0x14,
 0x08, 0x06, 0x01, 0x02, 0x0a, 0x14, 0x0d, 0x05,
 0x09, 0x02, 0x09, 0x0c, 0x0f, 0x0d, 0x11, 0x04,
 0x03, 0x0d, 0x0f, 0x01, 0x0e, 0x03, 0x03, 0x0b,
 0x08, 0x0e, 0x0e, 0x00, 0x14, 0x0f, 0x0c, 0x0c,
 0x01, 0x13, 0x16, 0x13, 0x09, 0x0a, 0x05, 0x0e,
 0x03, 0x0e, 0x0a, 0x11, 0x09, 0x00, 0x0a, 0x06,
 0x02, 0x06, 0x04, 0x10, 0x0e, 0x11, 0x09, 0x0d,
 0x14, 0x0f, 0x00, 0x09, 0x0c, 0x0a, 0x08, 0x00,
 0x13, 0x12, 0x0b, 0x09, 0x00, 0x11, 0x0d, 0x07,
 0x01, 0x12, 0x0c, 0x0b, 0x0b, 0x0e, 0x12, 0x0c,
 0x04, 0x0d, 0x07, 0x0d, 0x0a, 0x0e, 0x09, 0x0f,
 0x0d, 0x11, 0x03, 0x0d, 0x08, 0x00, 0x0c, 0x0e,
 0x05, 0x0d, 0x0e, 0x14, 0x0b, 0x11, 0x0b, 0x01,
 0x0a, 0x05, 0x12, 0x0a, 0x02, 0x0e, 0x0b, 0x10,
 0x06, 0x0b, 0x0e, 0x02, 0x0e, 0x09, 0x0d, 0x0e,
 0x0b, 0x04, 0x0c, 0x0c, 0x05, 0x10, 0x0d, 0x14,
 0x0e, 0x0e, 0x13, 0x04, 0x13, 0x0a, 0x01, 0x03,
 0x06, 0x14, 0x0c, 0x0c, 0x0e, 0x0e, 0x09, 0x10,
 0x0c, 0x0c, 0x0f, 0x09, 0x11, 0x0c, 0x01, 0x0c,
 0x11, 0x01, 0x0e, 0x10, 0x09, 0x0d, 0x00, 0x0f,
 0x0a, 0x0b, 0x0e, 0x0d, 0x0f, 0x01, 0x05, 0x0b,
 0x0e, 0x10, 0x11, 0x14, 0x11, 0x0b, 0x05, 0x07,
 0x09, 0x03, 0x0e, 0x0c, 0x02, 0x0b, 0x04, 0x11,
 0x0f, 0x0d, 0x0e, 0x0d, 0x0d, 0x0c, 0x18, 0x14,
 0x10, 0x15, 0x0e, 0x13, 0x11, 0x10, 0x08, 0x06,
 0x02, 0x06, 0x0f, 0x01, 0x09, 0x0f, 0x0f, 0x07,
 0x0b, 0x00, 0x19, 0x0d, 0x02, 0x02, 0x12, 0x10,
 0x0d, 0x0e, 0x0c, 0x0e, 0x0f, 0x0c, 0x04, 0x0b,
 0x0a, 0x0a, 0x0f, 0x08, 0x11, 0x10, 0x13, 0x02,
 0x0c, 0x0c, 0x07, 0x12, 0x0e, 0x11, 0x0d, 0x0f,
 0x12, 0x0d, 0x0b, 0x0a, 0x0a, 0x0d, 0x10, 0x09,
 0x08, 0x0e, 0x0d, 0x03, 0x0d, 0x0b, 0x10, 0x08,
 0x0a, 0x0f, 0x15, 0x0d, 0x0e, 0x0b, 0x0d, 0x10,
 0x00, 0x0d, 0x0b, 0x04, 0x0b, 0x13, 0x04, 0x0b,
 0x04, 0x0d, 0x08, 0x11, 0x0c, 0x0e, 0x0f, 0x09,
 0x0c, 0x12, 0x02, 0x0e, 0x0e, 0x0b, 0x11, 0x10,
 0x06, 0x13, 0x11, 0x16, 0x06, 0x08, 0x0f, 0x10,
 0x13, 0x02, 0x0d, 0x01, 0x0f, 0x06, 0x0a, 0x07,
 0x07, 0x15, 0x0f, 0x0f, 0x07, 0x0b, 0x10, 0x06,
 0x14, 0x04, 0x01, 0x11, 0x0e, 0x07, 0x09, 0x0f,
 0x0c, 0x03, 0x04, 0x0b, 0x14, 0x08, 0x01, 0x0c,
 0x13, 0x13, 0x0f, 0x00, 0x0c, 0x04, 0x0a, 0x0d,
 0x08, 0x0e, 0x09, 0x15, 0x0e, 0x10, 0x0c, 0x0f,
 0x0e, 0x0d, 0x0a, 0x07, 0x11, 0x14, 0x08, 0x02,
 0x0c, 0x11, 0x0f, 0x16, 0x0e, 0x08, 0x09, 0x0e,
 0x10, 0x0b, 0x06, 0x06, 0x06, 0x0c, 0x0d, 0x00,
 0x11, 0x0a, 0x0e, 0x02, 0x0f, 0x00, 0x04, 0x06,
 0x04, 0x03, 0x11, 0x0e, 0x09, 0x08, 0x0f, 0x16,
 0x10, 0x05, 0x02, 0x04, 0x0c, 0x0c, 0x0e, 0x16,
 0x06, 0x01, 0x05, 0x15, 0x0a, 0x13, 0x14, 0x0b,
 0x0b, 0x0e, 0x00, 0x0d, 0x17, 0x0d, 0x0f, 0x0d,
 0x00, 0x14, 0x10, 0x13, 0x0a, 0x11, 0x10, 0x02,
 0x0d, 0x0a, 0x10, 0x03, 0x15, 0x07, 0x09, 0x09,
 0x02, 0x0b, 0x01, 0x0b, 0x04, 0x05, 0x0e, 0x0e,
 0x07, 0x02, 0x05, 0x08, 0x11, 0x01, 0x0c, 0x0c,
 0x0d, 0x12, 0x0f, 0x09, 0x13, 0x13, 0x0b, 0x12,
 0x13, 0x10, 0x0a, 0x0d, 0x0c, 0x0a, 0x0d, 0x0a,
 0x09, 0x03, 0x09, 0x02, 0x0d, 0x0e, 0x0d, 0x0e,
 0x0d, 0x09, 0x12, 0x01, 0x07, 0x0e, 0x17, 0x14,
 0x08, 0x14, 0x0f, 0x0a, 0x01, 0x0d, 0x0e, 0x0e,
 0x0e, 0x00, 0x0a, 0x14, 0x16, 0x0d, 0x09, 0x0a,
 0x0d, 0x0d, 0x05, 0x0a, 0x0a, 0x0c, 0x0f, 0x0d,
 0x02, 0x0e, 0x04, 0x0d, 0x0e, 0x0d, 0x01, 0x06,
 0x0b, 0x07, 0x0d, 0x0e, 0x10, 0x0c, 0x07, 0x15,
 0x03, 0x0f, 0x04, 0x15, 0x0e, 0x0c, 0x13, 0x15,
 0x09, 0x11, 0x00, 0x13, 0x10, 0x0d, 0x02, 0x0b,
 0x12, 0x0d, 0x0e, 0x02, 0x0b, 0x03, 0x01, 0x05,
 0x0c, 0x09, 0x09, 0x05, 0x08, 0x14, 0x0a, 0x12,
 0x07, 0x0e, 0x09, 0x0e, 0x07, 0x11, 0x0e, 0x0b,
 0x0e, 0x0b, 0x0b, 0x04, 0x0b, 0x0c, 0x13, 0x0e,
 0x14, 0x01, 0x01, 0x0f, 0x10, 0x0c, 0x00, 0x0b,
 0x07, 0x01, 0x0f, 0x12, 0x11, 0x0a, 0x09, 0x13,
 0x05, 0x02, 0x12, 0x0b, 0x14, 0x13, 0x0a, 0x0d,
 0x06, 0x02, 0x10, 0x0d, 0x05, 0x0e, 0x08, 0x07,
 0x0e, 0x14, 0x03, 0x10, 0x14, 0x02, 0x08, 0x0e,
 0x10, 0x0c, 0x08, 0x0c, 0x0d, 0x0c, 0x10, 0x0e,
 0x0d, 0x0b, 0x08, 0x0f, 0x0e, 0x01, 0x0d, 0x00,
 0x0b, 0x02, 0x0d, 0x07, 0x11, 0x0f, 0x0f, 0x14,
 0x0e, 0x06, 0x01, 0x14, 0x09, 0x0d, 0x0d, 0x14,
 0x02, 0x0c, 0x0d, 0x0d, 0x0e, 0x00, 0x10, 0x10,
 0x0e, 0x0c, 0x0e, 0x0e, 0x05, 0x12, 0x0e, 0x03,
 0x05, 0x0e, 0x14, 0x0a, 0x07, 0x01, 0x05, 0x03,
 0x08, 0x13, 0x0d, 0x09, 0x0c, 0x0b, 0x0c, 0x0f,
 0x03, 0x0b, 0x11, 0x04, 0x0f, 0x0e, 0x08, 0x05,
 0x0f, 0x0d, 0x12, 0x12, 0x0c, 0x0f, 0x17, 0x0f,
 0x0c, 0x0d, 0x0b, 0x01, 0x07, 0x02, 0x08, 0x0b,
 0x0d, 0x06, 0x06, 0x12, 0x03, 0x08, 0x12, 0x14,
 0x01, 0x08, 0x06, 0x14, 0x02, 0x00, 0x15, 0x02,
 0x09, 0x0a, 0x06, 0x03, 0x10, 0x15, 0x0e, 0x0e,
 0x0c, 0x14, 0x05, 0x08, 0x10, 0x02, 0x0e, 0x01,
 0x13, 0x09, 0x0f, 0x01, 0x0b, 0x13, 0x09, 0x0e,
 0x13, 0x0a, 0x08, 0x10, 0x0f, 0x09, 0x16, 0x0d,
 0x19, 0x0b, 0x0a, 0x01, 0x08, 0x0f, 0x0e, 0x13,
 0x04, 0x06, 0x01, 0x12, 0x09, 0x0a, 0x0b, 0x07,
 0x08, 0x07, 0x09, 0x13, 0x02, 0x0b, 0x00, 0x0e,
 0x0f, 0x08, 0x0c, 0x0d, 0x14, 0x13, 0x08, 0x13,
 0x07, 0x05, 0x0e, 0x19, 0x0a, 0x09, 0x0d, 0x1a,
 0x06, 0x0a, 0x0e, 0x09, 0x03, 0x01, 0x09, 0x0e,
 0x17, 0x0e, 0x0c, 0x0a, 0x14, 0x0e, 0x08, 0x0d,
 0x0c, 0x01, 0x08, 0x0d, 0x12, 0x15, 0x08, 0x03,
 0x0b, 0x17, 0x0c, 0x0d, 0x10, 0x14, 0x02, 0x02,
 0x12, 0x0c, 0x09, 0x07, 0x17, 0x02, 0x0b, 0x0e,
 0x01, 0x0c, 0x00, 0x11, 0x0d, 0x0c, 0x11, 0x14,
 0x0e, 0x0f, 0x0b, 0x0a, 0x0d, 0x02, 0x0d, 0x04,
 0x04, 0x13, 0x12, 0x10, 0x0e, 0x18, 0x0d, 0x12,
 0x07, 0x07, 0x0b, 0x08, 0x16, 0x0c, 0x0d, 0x0a,
 0x12, 0x06, 0x0d, 0x0c, 0x0d, 0x07, 0x10, 0x0d,
 0x00, 0x12, 0x03, 0x08, 0x0b, 0x13, 0x0f, 0x17,
 0x0a, 0x0d, 0x05, 0x07, 0x13, 0x0f, 0x14, 0x13,
 0x08, 0x09, 0x0d, 0x00, 0x0f, 0x07, 0x01, 0x0f,
 0x0d, 0x04, 0x10, 0x0f, 0x14, 0x12, 0x08, 0x0e,
 0x0c, 0x15, 0x0e, 0x03, 0x15, 0x17, 0x09, 0x06,
 0x13, 0x0e, 0x07, 0x0d, 0x12, 0x08, 0x17, 0x17,
 0x02, 0x03, 0x14, 0x0b, 0x00, 0x03, 0x00, 0x14,
 0x10, 0x14, 0x0f, 0x02, 0x0b, 0x01, 0x0e, 0x0b,
 0x01, 0x15, 0x11, 0x0c, 0x04, 0x12, 0x0d, 0x09,
 0x0f, 0x03, 0x13, 0x0c, 0x0d, 0x0e, 0x13, 0x08
};

// Y max array (64 x 32)
static const int y_max[] =
{
 0x17, 0x18, 0x1b, 0x1f, 0x11, 0x1b, 0x12, 0x15,
 0x16, 0x0e, 0x14, 0x1d, 0x1f, 0x11, 0x12, 0x19,
 0x10, 0x12, 0x11, 0x0f, 0x0e, 0x1a, 0x10, 0x13,
 0x15, 0x0f, 0x10, 0x0f, 0x17, 0x0e, 0x1e, 0x1b,
 0x1c, 0x1b, 0x13, 0x12, 0x17, 0x1d, 0x15, 0x18,
 0x19, 0x1b, 0x13, 0x0f, 0x13, 0x12, 0x13, 0x18,
 0x0f, 0x1b, 0x14, 0x16, 0x19, 0x17, 0x10, 0x18,
 0x1f, 0x16, 0x15, 0x1a, 0x0f, 0x0e, 0x10, 0x1d,
 0x12, 0x14, 0x16, 0x0f, 0x16, 0x17, 0x1a, 0x1b,
 0x1d, 0x1a, 0x10, 0x12, 0x1a, 0x1b, 0x13, 0x13,
 0x0f, 0x17, 0x1a, 0x10, 0x12, 0x12, 0x18, 0x16,
 0x15, 0x12, 0x1e, 0x12, 0x16, 0x11, 0x10, 0x10,
 0x1f, 0x1e, 0x1a, 0x1a, 0x16, 0x14, 0x19, 0x15,
 0x11, 0x1a, 0x16, 0x0e, 0x1c, 0x16, 0x1c, 0x11,
 0x19, 0x17, 0x0e, 0x16, 0x11, 0x0e, 0x19, 0x0d,
 0x12, 0x1c, 0x13, 0x1f, 0x0c, 0x15, 0x15, 0x1c,
 0x11, 0x13, 0x11, 0x19, 0x1d, 0x1f, 0x1d, 0x14,
 0x15, 0x1b, 0x1f, 0x15, 0x13, 0x12, 0x1a, 0x13,
 0x1e, 0x10, 0x19, 0x10, 0x13, 0x13, 0x1d, 0x0d,
 0x0f, 0x17, 0x12, 0x17, 0x14, 0x13, 0x1e, 0x10,
 0x1b, 0x19, 0x1c, 0x1c, 0x14, 0x1b, 0x19, 0x18,
 0x13, 0x19, 0x1f, 0x0f, 0x1f, 0x15, 0x10, 0x12,
 0x12, 0x13, 0x12, 0x12, 0x10, 0x15, 0x19, 0x1a,
 0x1c, 0x1b, 0x1f, 0x10, 0x0b, 0x10, 0x1f, 0x12,
 0x15, 0x10, 0x19, 0x1d, 0x12, 0x13, 0x12, 0x0d,
 0x10, 0x10, 0x1b, 0x1a, 0x1f, 0x0f, 0x1b, 0x1c,
 0x1d, 0x1b, 0x13, 0x10, 0x0f, 0x0d, 0x12, 0x10,
 0x10, 0x0f, 0x18, 0x11, 0x1a, 0x13, 0x12, 0x0c,
 0x15, 0x13, 0x10, 0x1d, 0x15, 0x17, 0x18, 0x10,
 0x1e, 0x10, 0x18, 0x0e, 0x1a, 0x19, 0x19, 0x1b,
 0x10, 0x11, 0x15, 0x14, 0x1e, 0x1a, 0x15, 0x1a,
 0x1e, 0x10, 0x0c, 0x0f, 0x19, 0x19, 0x1a, 0x10,
 0x1e, 0x11, 0x14, 0x15, 0x19, 0x1f, 0x13, 0x1c,
 0x12, 0x1c, 0x0f, 0x1f, 0x18, 0x10, 0x0f, 0x14,
 0x18, 0x0b, 0x15, 0x0f, 0x14, 0x11, 0x16, 0x12,
 0x1c, 0x1e, 0x0f, 0x1d, 0x19, 0x1e, 0x1e, 0x1e,
 0x10, 0x12, 0x18, 0x12, 0x18, 0x1b, 0x1c, 0x14,
 0x1c, 0x0f, 0x12, 0x13, 0x1d, 0x12, 0x15, 0x1e,
 0x0f, 0x1f, 0x1d, 0x18, 0x1f, 0x1c, 0x15, 0x1b,
 0x0e, 0x13, 0x11, 0x12, 0x0e, 0x17, 0x0e, 0x1e,
 0x1a, 0x11, 0x1a, 0x17, 0x0d, 0x1d, 0x10, 0x1f,
 0x1e, 0x12, 0x0f, 0x1f, 0x12, 0x14, 0x11, 0x1b,
 0x1e, 0x0f, 0x18, 0x17, 0x16, 0x11, 0x1b, 0x1a,
 0x1a, 0x1b, 0x13, 0x1b, 0x17, 0x1a, 0x19, 0x1d,
 0x18, 0x15, 0x12, 0x18, 0x1c, 0x10, 0x15, 0x1f,
 0x0e, 0x17, 0x0f, 0x1f, 0x12, 0x1e, 0x16, 0x0c,
 0x1e, 0x13, 0x12, 0x15, 0x0f, 0x10, 0x1d, 0x14,
 0x14, 0x16, 0x1e, 0x11, 0x1d, 0x1e, 0x11, 0x1f,
 0x15, 0x16, 0x14, 0x12, 0x11, 0x1a, 0x1a, 0x14,
 0x19, 0x1b, 0x0d, 0x0f, 0x12, 0x15, 0x18, 0x11,
 0x0f, 0x1f, 0x16, 0x1b, 0x15, 0x18, 0x15, 0x0f,
 0x11, 0x10, 0x17, 0x1e, 0x1d, 0x18, 0x0c, 0x1a,
 0x11, 0x12, 0x1b, 0x14, 0x19, 0x17, 0x11, 0x0f,
 0x17, 0x17, 0x10, 0x18, 0x13, 0x11, 0x12, 0x13,
 0x18, 0x1d, 0x1e, 0x0f, 0x0f, 0x0f, 0x1a, 0x12,
 0x10, 0x11, 0x1c, 0x1f, 0x0e, 0x11, 0x1c, 0x10,
 0x0f, 0x0f, 0x13, 0x1a, 0x17, 0x1d, 0x18, 0x12,
 0x11, 0x1a, 0x19, 0x12, 0x15, 0x1f, 0x12, 0x1d,
 0x10, 0x10, 0x17, 0x1e, 0x0e, 0x1d, 0x11, 0x11,
 0x1a, 0x12, 0x1a, 0x13, 0x0e, 0x10, 0x0c, 0x17,
 0x19, 0x11, 0x11, 0x19, 0x10, 0x1d, 0x12, 0x14,
 0x1d, 0x1f, 0x1d, 0x11, 0x11, 0x11, 0x18, 0x14,
 0x1e, 0x18, 0x1b, 0x1b, 0x1d, 0x19, 0x11, 0x15,
 0x11, 0x17, 0x1c, 0x0d, 0x15, 0x0f, 0x1b, 0x13,
 0x12, 0x12, 0x19, 0x18, 0x16, 0x10, 0x0e, 0x14,
 0x16, 0x12, 0x1f, 0x0f, 0x11, 0x18, 0x1a, 0x14,
 0x12, 0x19, 0x11, 0x19, 0x18, 0x11, 0x11, 0x1f,
 0x16, 0x11, 0x10, 0x19, 0x1e, 0x17, 0x1f, 0x13,
 0x13, 0x12, 0x1b, 0x14, 0x12, 0x16, 0x13, 0x1a,
 0x1f, 0x16, 0x17, 0x12, 0x16, 0x15, 0x0f, 0x10,
 0x11, 0x18, 0x15, 0x0d, 0x16, 0x1b, 0x12, 0x1a,
 0x0f, 0x17, 0x14, 0x15, 0x19, 0x0f, 0x0f, 0x19,
 0x1c, 0x10, 0x10, 0x1a, 0x14, 0x1c, 0x11, 0x12,
 0x1d, 0x0f, 0x1d, 0x13, 0x14, 0x16, 0x13, 0x11,
 0x1b, 0x19, 0x1d, 0x0c, 0x10, 0x12, 0x16, 0x10,
 0x18, 0x1e, 0x10, 0x1b, 0x11, 0x1a, 0x1e, 0x0b,
 0x1b, 0x0f, 0x18, 0x15, 0x1e, 0x1d, 0x1c, 0x1b,
 0x12, 0x19, 0x0f, 0x17, 0x16, 0x17, 0x1d, 0x1c,
 0x11, 0x10, 0x16, 0x0e, 0x1d, 0x11, 0x1f, 0x1a,
 0x0f, 0x1f, 0x13, 0x10, 0x10, 0x16, 0x1e, 0x16,
 0x0e, 0x13, 0x11, 0x18, 0x1c, 0x10, 0x12, 0x0f,
 0x13, 0x1d, 0x18, 0x1d, 0x10, 0x10, 0x12, 0x11,
 0x11, 0x0e, 0x13, 0x1b, 0x11, 0x0e, 0x1b, 0x11,
 0x16, 0x1c, 0x1f, 0x1f, 0x15, 0x11, 0x0f, 0x10,
 0x1a, 0x13, 0x10, 0x12, 0x17, 0x15, 0x1f, 0x1f,
 0x16, 0x0f, 0x13, 0x1d, 0x0f, 0x0d, 0x0f, 0x1a,
 0x0e, 0x11, 0x10, 0x1a, 0x0d, 0x19, 0x0f, 0x1c,
 0x18, 0x15, 0x11, 0x15, 0x14, 0x14, 0x1d, 0x1d,
 0x10, 0x10, 0x16, 0x14, 0x1a, 0x11, 0x19, 0x1e,
 0x11, 0x17, 0x0d, 0x1e, 0x13, 0x1c, 0x11, 0x0f,
 0x0e, 0x10, 0x15, 0x1e, 0x1f, 0x1e, 0x15, 0x11,
 0x1c, 0x0d, 0x13, 0x1b, 0x12, 0x15, 0x1a, 0x1a,
 0x13, 0x11, 0x1a, 0x18, 0x15, 0x10, 0x15, 0x12,
 0x10, 0x1a, 0x15, 0x11, 0x12, 0x19, 0x14, 0x12,
 0x11, 0x0f, 0x0e, 0x1e, 0x13, 0x1e, 0x1f, 0x0d,
 0x1e, 0x11, 0x0f, 0x16, 0x19, 0x11, 0x11, 0x13,
 0x18, 0x10, 0x1a, 0x1a, 0x1f, 0x12, 0x19, 0x13,
 0x12, 0x1c, 0x12, 0x1b, 0x1e, 0x0f, 0x12, 0x1e,
 0x11, 0x0c, 0x10, 0x0e, 0x1f, 0x0c, 0x14, 0x14,
 0x13, 0x0d, 0x19, 0x0d, 0x17, 0x0c, 0x15, 0x0f,
 0x1a, 0x13, 0x13, 0x12, 0x1d, 0x1a, 0x18, 0x12,
 0x1b, 0x1a, 0x15, 0x1d, 0x18, 0x1d, 0x14, 0x10,
 0x11, 0x12, 0x1a, 0x10, 0x1c, 0x19, 0x0d, 0x1d,
 0x18, 0x10, 0x1f, 0x0a, 0x11, 0x1a, 0x15, 0x12,
 0x12, 0x1f, 0x0e, 0x11, 0x1e, 0x15, 0x12, 0x12,
 0x1c, 0x13, 0x18, 0x1f, 0x0d, 0x1f, 0x13, 0x11,
 0x1e, 0x10, 0x0f, 0x11, 0x12, 0x1f, 0x0e, 0x1c,
 0x0c, 0x12, 0x19, 0x1e, 0x10, 0x1d, 0x13, 0x10,
 0x14, 0x16, 0x11, 0x13, 0x1d, 0x14, 0x1c, 0x15,
 0x10, 0x1a, 0x1e, 0x19, 0x0f, 0x11, 0x18, 0x0d,
 0x18, 0x10, 0x14, 0x1e, 0x0d, 0x19, 0x19, 0x0f,
 0x14, 0x0f, 0x1c, 0x18, 0x1d, 0x0b, 0x1b, 0x11,
 0x1a, 0x1d, 0x17, 0x1e, 0x13, 0x17, 0x11, 0x1a,
 0x15, 0x1c, 0x11, 0x1f, 0x12, 0x18, 0x0f, 0x17,
 0x12, 0x15, 0x12, 0x15, 0x1c, 0x19, 0x12, 0x18,
 0x1f, 0x14, 0x12, 0x12, 0x11, 0x1d, 0x1b, 0x0f,
 0x14, 0x1c, 0x1a, 0x1b, 0x12, 0x13, 0x11, 0x0d,
 0x0e, 0x1c, 0x0f, 0x1e, 0x1a, 0x14, 0x16, 0x13,
 0x1c, 0x10, 0x1b, 0x10, 0x1a, 0x18, 0x15, 0x0f,
 0x1f, 0x1e, 0x13, 0x15, 0x11, 0x0f, 0x1a, 0x18,
 0x1e, 0x12, 0x11, 0x1b, 0x13, 0x1b, 0x11, 0x1a,
 0x10, 0x1a, 0x18, 0x13, 0x13, 0x1e, 0x11, 0x15,
 0x13, 0x1e, 0x12, 0x10, 0x1f, 0x12, 0x1b, 0x18,
 0x1f, 0x14, 0x0f, 0x0f, 0x1d, 0x0f, 0x0b, 0x1e,
 0x1c, 0x11, 0x0f, 0x1e, 0x12, 0x11, 0x1a, 0x1f,
 0x12, 0x1d, 0x14, 0x13, 0x13, 0x1b, 0x17, 0x12,
 0x11, 0x17, 0x11, 0x10, 0x15, 0x0e, 0x1a, 0x0e,
 0x11, 0x18, 0x11, 0x0f, 0x17, 0x15, 0x0b, 0x0c,
 0x12, 0x11, 0x17, 0x1c, 0x1a, 0x0d, 0x13, 0x0f,
 0x13, 0x1e, 0x10, 0x0b, 0x0e, 0x17, 0x13, 0x18,
 0x11, 0x12, 0x11, 0x19, 0x1f, 0x0f, 0x16, 0x10,
 0x16, 0x18, 0x1f, 0x0b, 0x14, 0x16, 0x0b, 0x10,
 0x16, 0x1e, 0x1f, 0x15, 0x18, 0x14, 0x17, 0x17,
 0x0f, 0x10, 0x0f, 0x1c, 0x11, 0x11, 0x12, 0x15,
 0x11, 0x11, 0x0f, 0x08, 0x19, 0x15, 0x1e, 0x08,
 0x0d, 0x10, 0x12, 0x0f, 0x16, 0x1f, 0x17, 0x12,
 0x1f, 0x18, 0x1b, 0x11, 0x15, 0x0c, 0x1b, 0x1c,
 0x13, 0x13, 0x11, 0x0c, 0x10, 0x13, 0x1e, 0x13,
 0x13, 0x10, 0x12, 0x0f, 0x12, 0x13, 0x19, 0x1d,
 0x11, 0x17, 0x12, 0x10, 0x15, 0x1b, 0x19, 0x17,
 0x11, 0x10, 0x1d, 0x10, 0x10, 0x11, 0x1e, 0x0e,
 0x16, 0x1d, 0x12, 0x18, 0x12, 0x0e, 0x0d, 0x16,
 0x11, 0x15, 0x19, 0x0b, 0x1b, 0x1a, 0x1d, 0x13,
 0x19, 0x16, 0x1f, 0x19, 0x1b, 0x0e, 0x0f, 0x10,
 0x15, 0x11, 0x19, 0x16, 0x0f, 0x0f, 0x11, 0x1c,
 0x10, 0x14, 0x13, 0x1f, 0x1e, 0x16, 0x17, 0x11,
 0x1d, 0x10, 0x0b, 0x15, 0x12, 0x1b, 0x19, 0x15,
 0x1b, 0x1d, 0x0f, 0x0d, 0x0c, 0x14, 0x13, 0x12,
 0x14, 0x1d, 0x0f, 0x13, 0x11, 0x1a, 0x18, 0x11,
 0x16, 0x0f, 0x14, 0x15, 0x12, 0x12, 0x19, 0x13,
 0x0f, 0x17, 0x0e, 0x16, 0x15, 0x08, 0x13, 0x1c,
 0x18, 0x0f, 0x11, 0x1f, 0x10, 0x16, 0x13, 0x0c,
 0x1c, 0x13, 0x1f, 0x12, 0x0e, 0x19, 0x1a, 0x19,
 0x12, 0x12, 0x0f, 0x1e, 0x16, 0x12, 0x0f, 0x11,
 0x19, 0x16, 0x11, 0x1b, 0x0c, 0x14, 0x17, 0x1b,
 0x12, 0x1e, 0x1d, 0x11, 0x1a, 0x11, 0x09, 0x16,
 0x0e, 0x19, 0x11, 0x11, 0x12, 0x0f, 0x18, 0x1f,
 0x10, 0x14, 0x13, 0x17, 0x1a, 0x1e, 0x0a, 0x1f,
 0x1a, 0x0e, 0x11, 0x12, 0x16, 0x12, 0x13, 0x12,
 0x12, 0x11, 0x12, 0x14, 0x12, 0x0d, 0x0d, 0x1f,
 0x14, 0x17, 0x1d, 0x1a, 0x15, 0x0f, 0x15, 0x12,
 0x1f, 0x1d, 0x13, 0x17, 0x15, 0x10, 0x13, 0x14,
 0x12, 0x16, 0x14, 0x15, 0x13, 0x0f, 0x1f, 0x1f,
 0x13, 0x1f, 0x10, 0x1b, 0x16, 0x12, 0x14, 0x0f,
 0x0d, 0x15, 0x16, 0x1a, 0x18, 0x11, 0x11, 0x0d,
 0x12, 0x19, 0x1f, 0x11, 0x10, 0x15, 0x1c, 0x1e,
 0x1b, 0x13, 0x14, 0x1a, 0x11, 0x15, 0x0a, 0x12,
 0x10, 0x11, 0x10, 0x10, 0x13, 0x11, 0x1a, 0x0f,
 0x0f, 0x13, 0x11, 0x18, 0x0f, 0x1e, 0x16, 0x11,
 0x1f, 0x10, 0x16, 0x19, 0x13, 0x12, 0x11, 0x17,
 0x0c, 0x11, 0x13, 0x13, 0x14, 0x14, 0x12, 0x0f,
 0x0e, 0x15, 0x1d, 0x17, 0x0f, 0x1a, 0x12, 0x14,
 0x18, 0x15, 0x1c, 0x11, 0x1f, 0x19, 0x10, 0x13,
 0x14, 0x12, 0x16, 0x1b, 0x10, 0x11, 0x1a, 0x11,
 0x17, 0x1f, 0x0a, 0x1b, 0x18, 0x13, 0x1d, 0x11,
 0x0c, 0x1a, 0x15, 0x1f, 0x0b, 0x11, 0x16, 0x15,
 0x18, 0x13, 0x1f, 0x19, 0x12, 0x0e, 0x1d, 0x0c,
 0x0d, 0x1f, 0x12, 0x1e, 0x1f, 0x13, 0x11, 0x0f,
 0x1e, 0x0d, 0x0c, 0x15, 0x12, 0x17, 0x12, 0x19,
 0x11, 0x16, 0x11, 0x15, 0x1d, 0x18, 0x07, 0x14,
 0x1d, 0x1e, 0x1c, 0x17, 0x14, 0x10, 0x16, 0x15,
 0x11, 0x13, 0x14, 0x1d, 0x11, 0x14, 0x11, 0x11,
 0x1e, 0x0e, 0x14, 0x12, 0x1f, 0x1e, 0x18, 0x0e,
 0x11, 0x17, 0x1b, 0x1f, 0x10, 0x11, 0x1d, 0x17,
 0x13, 0x14, 0x1b, 0x12, 0x12, 0x1b, 0x0f, 0x1c,
 0x1b, 0x14, 0x19, 0x0b, 0x11, 0x0f, 0x10, 0x11,
 0x0c, 0x1a, 0x19, 0x13, 0x12, 0x0c, 0x16, 0x1e,
 0x14, 0x0f, 0x07, 0x13, 0x0e, 0x1e, 0x17, 0x1e,
 0x12, 0x15, 0x12, 0x1e, 0x1c, 0x1b, 0x1d, 0x1a,
 0x17, 0x0f, 0x1e, 0x14, 0x1f, 0x18, 0x15, 0x10,
 0x0b, 0x18, 0x18, 0x1a, 0x12, 0x1c, 0x1c, 0x0b,
 0x13, 0x0f, 0x13, 0x0c, 0x18, 0x0d, 0x11, 0x0d,
 0x13, 0x1c, 0x1b, 0x11, 0x17, 0x19, 0x0f, 0x12,
 0x16, 0x10, 0x0d, 0x11, 0x12, 0x0f, 0x18, 0x12,
 0x0f, 0x1d, 0x1e, 0x10, 0x1b, 0x1f, 0x11, 0x18,
 0x1b, 0x11, 0x0e, 0x11, 0x1b, 0x1b, 0x13, 0x11,
 0x16, 0x10, 0x0f, 0x0e, 0x1d, 0x11, 0x12, 0x13,
 0x10, 0x18, 0x18, 0x0c, 0x12, 0x16, 0x1e, 0x17,
 0x10, 0x1a, 0x14, 0x16, 0x14, 0x12, 0x13, 0x14,
 0x14, 0x0d, 0x10, 0x19, 0x1e, 0x12, 0x0d, 0x11,
 0x10, 0x1f, 0x0a, 0x10, 0x12, 0x13, 0x1d, 0x15,
 0x11, 0x13, 0x1d, 0x13, 0x12, 0x1a, 0x0d, 0x11,
 0x12, 0x11, 0x11, 0x14, 0x14, 0x13, 0x0f, 0x1e,
 0x17, 0x1b, 0x0b, 0x1b, 0x13, 0x18, 0x19, 0x1e,
 0x0e, 0x19, 0x14, 0x1e, 0x11, 0x10, 0x0b, 0x15,
 0x1a, 0x1f, 0x13, 0x0d, 0x13, 0x14, 0x10, 0x12,
 0x11, 0x12, 0x0c, 0x0b, 0x10, 0x17, 0x10, 0x17,
 0x1e, 0x15, 0x18, 0x12, 0x0d, 0x12, 0x11, 0x16,
 0x12, 0x19, 0x17, 0x10, 0x12, 0x15, 0x1b, 0x12,
 0x1f, 0x0c, 0x11, 0x15, 0x18, 0x11, 0x0d, 0x1a,
 0x1b, 0x10, 0x19, 0x15, 0x1d, 0x16, 0x0f, 0x1e,
 0x1e, 0x0f, 0x19, 0x15, 0x1f, 0x17, 0x10, 0x0f,
 0x10, 0x0e, 0x15, 0x11, 0x1a, 0x13, 0x12, 0x0f,
 0x0f, 0x19, 0x0b, 0x1f, 0x18, 0x0d, 0x0e, 0x12,
 0x11, 0x16, 0x0d, 0x13, 0x11, 0x18, 0x12, 0x13,
 0x12, 0x10, 0x1a, 0x13, 0x10, 0x0c, 0x1f, 0x0d,
 0x12, 0x15, 0x12, 0x13, 0x16, 0x19, 0x12, 0x1e,
 0x0f, 0x0d, 0x0e, 0x1e, 0x11, 0x11, 0x15, 0x1e,
 0x11, 0x17, 0x10, 0x15, 0x1b, 0x1e, 0x11, 0x1e,
 0x15, 0x12, 0x16, 0x0f, 0x09, 0x1f, 0x10, 0x0d,
 0x1d, 0x11, 0x1b, 0x12, 0x18, 0x1f, 0x0c, 0x0d,
 0x1c, 0x1a, 0x15, 0x13, 0x11, 0x13, 0x17, 0x11,
 0x1d, 0x11, 0x18, 0x08, 0x12, 0x16, 0x1c, 0x16,
 0x17, 0x0f, 0x17, 0x1a, 0x13, 0x13, 0x1f, 0x12,
 0x13, 0x13, 0x0e, 0x0e, 0x1f, 0x1e, 0x10, 0x0f,
 0x11, 0x18, 0x1d, 0x1d, 0x14, 0x14, 0x1d, 0x1b,
 0x0f, 0x0e, 0x15, 0x1f, 0x17, 0x0d, 0x1e, 0x0a,
 0x11, 0x11, 0x0e, 0x0b, 0x17, 0x1e, 0x13, 0x12,
 0x1d, 0x17, 0x0d, 0x1a, 0x18, 0x1f, 0x0f, 0x0e,
 0x1f, 0x0e, 0x14, 0x0f, 0x0d, 0x19, 0x0e, 0x12,
 0x18, 0x17, 0x11, 0x14, 0x12, 0x15, 0x1e, 0x13,
 0x1e, 0x1d, 0x0b, 0x0d, 0x0f, 0x18, 0x10, 0x1d,
 0x0e, 0x12, 0x0d, 0x1b, 0x1b, 0x10, 0x12, 0x11,
 0x0c, 0x13, 0x0e, 0x18, 0x14, 0x11, 0x0c, 0x1c,
 0x1d, 0x0c, 0x10, 0x10, 0x19, 0x15, 0x1b, 0x14,
 0x0f, 0x12, 0x13, 0x1f, 0x11, 0x18, 0x14, 0x1e,
 0x11, 0x0f, 0x10, 0x1b, 0x0d, 0x09, 0x12, 0x12,
 0x1e, 0x0f, 0x11, 0x10, 0x1c, 0x10, 0x16, 0x11,
 0x15, 0x10, 0x0d, 0x13, 0x1d, 0x1c, 0x14, 0x0c,
 0x15, 0x1f, 0x13, 0x11, 0x1f, 0x19, 0x0b, 0x13,
 0x17, 0x1a, 0x16, 0x1d, 0x1d, 0x0a, 0x12, 0x10,
 0x0f, 0x12, 0x0e, 0x19, 0x13, 0x15, 0x12, 0x18,
 0x12, 0x1f, 0x0e, 0x13, 0x12, 0x0a, 0x13, 0x14,
 0x11, 0x19, 0x18, 0x11, 0x17, 0x1d, 0x10, 0x19,
 0x0d, 0x0c, 0x11, 0x14, 0x1d, 0x12, 0x0f, 0x12,
 0x17, 0x0f, 0x13, 0x17, 0x10, 0x0c, 0x15, 0x11,
 0x08, 0x1f, 0x0d, 0x0d, 0x1b, 0x16, 0x1a, 0x1d,
 0x14, 0x19, 0x10, 0x0d, 0x1e, 0x13, 0x1a, 0x1d,
 0x0e, 0x1d, 0x1b, 0x10, 0x10, 0x11, 0x0a, 0x10,
 0x11, 0x1a, 0x18, 0x12, 0x19, 0x13, 0x10, 0x0f,
 0x13, 0x1d, 0x12, 0x0d, 0x19, 0x1f, 0x10, 0x15,
 0x17, 0x11, 0x13, 0x10, 0x14, 0x10, 0x1c, 0x1e,
 0x0d, 0x0e, 0x1c, 0x1a, 0x11, 0x18, 0x11, 0x1b,
 0x11, 0x1a, 0x11, 0x17, 0x11, 0x0f, 0x11, 0x13,
 0x0c, 0x1d, 0x15, 0x10, 0x08, 0x13, 0x0f, 0x1a,
 0x10, 0x13, 0x17, 0x11, 0x10, 0x10, 0x18, 0x0c
};

// X min array (64 x 32)
static const int x_min[] =
{
 0x04, 0x00, 0x02, 0x07, 0x08, 0x02, 0x09, 0x0a,
 0x02, 0x05, 0x01, 0x0c, 0x01, 0x0f, 0x06, 0x0a,
 0x04, 0x0c, 0x06, 0x06, 0x0e, 0x02, 0x07, 0x03,
 0x02, 0x00, 0x06, 0x06, 0x11, 0x0b, 0x0d, 0x01,
 0x0b, 0x06, 0x04, 0x06, 0x0c, 0x06, 0x09, 0x0c,
 0x05, 0x03, 0x0c, 0x02, 0x07, 0x06, 0x0b, 0x0d,
 0x09, 0x06, 0x00, 0x04, 0x05, 0x11, 0x0d, 0x01,
 0x02, 0x0f, 0x12, 0x0c, 0x08, 0x02, 0x0e, 0x04,
 0x09, 0x08, 0x04, 0x0e, 0x00, 0x06, 0x08, 0x02,
 0x01, 0x02, 0x08, 0x0e, 0x02, 0x00, 0x11, 0x0a,
 0x08, 0x03, 0x0c, 0x03, 0x0d, 0x0b, 0x07, 0x10,
 0x0d, 0x0a, 0x05, 0x10, 0x0f, 0x0d, 0x08, 0x05,
 0x07, 0x04, 0x04, 0x01, 0x0c, 0x0a, 0x0b, 0x08,
 0x09, 0x05, 0x0e, 0x02, 0x01, 0x03, 0x0c, 0x0e,
 0x02, 0x10, 0x09, 0x0c, 0x03, 0x05, 0x0f, 0x0a,
 0x07, 0x0a, 0x04, 0x07, 0x05, 0x0e, 0x0f, 0x09,
 0x08, 0x0b, 0x03, 0x04, 0x0f, 0x0d, 0x01, 0x0d,
 0x02, 0x0a, 0x01, 0x03, 0x07, 0x03, 0x01, 0x08,
 0x07, 0x10, 0x07, 0x06, 0x0d, 0x08, 0x06, 0x0e,
 0x0b, 0x0a, 0x03, 0x06, 0x0c, 0x03, 0x00, 0x02,
 0x02, 0x03, 0x01, 0x01, 0x07, 0x00, 0x0d, 0x07,
 0x07, 0x03, 0x08, 0x04, 0x01, 0x03, 0x0c, 0x0d,
 0x0f, 0x12, 0x06, 0x08, 0x10, 0x0b, 0x00, 0x04,
 0x12, 0x04, 0x11, 0x09, 0x06, 0x03, 0x11, 0x0b,
 0x06, 0x0d, 0x05, 0x05, 0x0d, 0x0f, 0x0f, 0x05,
 0x01, 0x02, 0x08, 0x05, 0x08, 0x06, 0x0a, 0x13,
 0x0a, 0x05, 0x0d, 0x0b, 0x03, 0x09, 0x03, 0x0e,
 0x00, 0x08, 0x04, 0x01, 0x00, 0x10, 0x10, 0x08,
 0x0a, 0x01, 0x10, 0x08, 0x04, 0x08, 0x04, 0x02,
 0x03, 0x0f, 0x11, 0x06, 0x0c, 0x11, 0x02, 0x0d,
 0x0e, 0x10, 0x0b, 0x06, 0x02, 0x03, 0x08, 0x05,
 0x04, 0x0c, 0x0a, 0x05, 0x00, 0x14, 0x03, 0x09,
 0x07, 0x0c, 0x00, 0x07, 0x01, 0x05, 0x0c, 0x08,
 0x0d, 0x07, 0x08, 0x0b, 0x01, 0x12, 0x10, 0x0c,
 0x01, 0x03, 0x04, 0x01, 0x0d, 0x0d, 0x12, 0x0d,
 0x02, 0x09, 0x05, 0x0a, 0x11, 0x08, 0x05, 0x13,
 0x01, 0x06, 0x0b, 0x0c, 0x09, 0x02, 0x00, 0x09,
 0x07, 0x08, 0x00, 0x02, 0x01, 0x02, 0x09, 0x00,
 0x05, 0x02, 0x00, 0x15, 0x00, 0x0d, 0x0f, 0x01,
 0x03, 0x0c, 0x0c, 0x02, 0x11, 0x03, 0x05, 0x14,
 0x01, 0x01, 0x01, 0x09, 0x04, 0x05, 0x04, 0x06,
 0x02, 0x0d, 0x08, 0x02, 0x05, 0x01, 0x09, 0x00,
 0x0b, 0x0b, 0x13, 0x0f, 0x02, 0x00, 0x08, 0x10,
 0x08, 0x0a, 0x10, 0x08, 0x05, 0x00, 0x04, 0x0a,
 0x0a, 0x10, 0x0d, 0x01, 0x04, 0x09, 0x0f, 0x02,
 0x07, 0x11, 0x06, 0x0f, 0x02, 0x06, 0x08, 0x00,
 0x08, 0x0d, 0x04, 0x0c, 0x07, 0x0a, 0x05, 0x0e,
 0x12, 0x0c, 0x00, 0x0f, 0x02, 0x10, 0x0d, 0x02,
 0x11, 0x08, 0x0a, 0x01, 0x09, 0x01, 0x07, 0x0d,
 0x0a, 0x02, 0x08, 0x0a, 0x0c, 0x0a, 0x0e, 0x00,
 0x00, 0x02, 0x09, 0x02, 0x00, 0x0b, 0x13, 0x0d,
 0x04, 0x0e, 0x0e, 0x04, 0x0b, 0x11, 0x0c, 0x02,
 0x0f, 0x0b, 0x08, 0x0e, 0x08, 0x0a, 0x08, 0x11,
 0x0d, 0x00, 0x02, 0x09, 0x0b, 0x02, 0x10, 0x09,
 0x0c, 0x03, 0x00, 0x0c, 0x10, 0x0e, 0x10, 0x07,
 0x0f, 0x08, 0x0c, 0x0a, 0x06, 0x0c, 0x06, 0x06,
 0x0d, 0x0d, 0x05, 0x09, 0x06, 0x0c, 0x03, 0x05,
 0x0e, 0x03, 0x04, 0x01, 0x11, 0x00, 0x0d, 0x05,
 0x03, 0x09, 0x0d, 0x0c, 0x0d, 0x03, 0x08, 0x05,
 0x0b, 0x0c, 0x0a, 0x13, 0x0f, 0x0a, 0x00, 0x01,
 0x0a, 0x0b, 0x0a, 0x01, 0x0d, 0x03, 0x00, 0x0c,
 0x01, 0x0b, 0x01, 0x0b, 0x03, 0x08, 0x0d, 0x02,
 0x05, 0x07, 0x0d, 0x0e, 0x01, 0x05, 0x0a, 0x11,
 0x0a, 0x11, 0x01, 0x07, 0x0e, 0x08, 0x05, 0x0e,
 0x05, 0x09, 0x05, 0x11, 0x0c, 0x0b, 0x08, 0x08,
 0x09, 0x08, 0x03, 0x03, 0x10, 0x0f, 0x11, 0x0b,
 0x0f, 0x09, 0x0f, 0x03, 0x02, 0x0e, 0x03, 0x03,
 0x14, 0x0e, 0x0e, 0x08, 0x0a, 0x10, 0x04, 0x07,
 0x04, 0x0d, 0x14, 0x0d, 0x0c, 0x02, 0x03, 0x01,
 0x00, 0x0e, 0x02, 0x10, 0x05, 0x02, 0x0b, 0x0e,
 0x01, 0x12, 0x0d, 0x0f, 0x02, 0x0f, 0x0d, 0x11,
 0x0a, 0x09, 0x02, 0x0f, 0x05, 0x06, 0x05, 0x0d,
 0x0b, 0x03, 0x0e, 0x11, 0x02, 0x01, 0x0b, 0x05,
 0x00, 0x05, 0x03, 0x0b, 0x04, 0x0c, 0x0f, 0x10,
 0x11, 0x05, 0x10, 0x06, 0x03, 0x03, 0x00, 0x05,
 0x04, 0x0f, 0x09, 0x00, 0x0d, 0x06, 0x13, 0x07,
 0x03, 0x08, 0x05, 0x03, 0x05, 0x09, 0x04, 0x08,
 0x0c, 0x12, 0x0c, 0x0d, 0x0d, 0x01, 0x0d, 0x02,
 0x08, 0x0e, 0x12, 0x07, 0x16, 0x06, 0x01, 0x07,
 0x0d, 0x09, 0x0f, 0x06, 0x09, 0x00, 0x01, 0x0c,
 0x0e, 0x08, 0x04, 0x01, 0x06, 0x0b, 0x0d, 0x0c,
 0x06, 0x02, 0x0d, 0x13, 0x05, 0x10, 0x09, 0x05,
 0x00, 0x10, 0x10, 0x0b, 0x10, 0x0d, 0x10, 0x00,
 0x06, 0x09, 0x01, 0x00, 0x04, 0x04, 0x08, 0x08,
 0x02, 0x0c, 0x00, 0x0b, 0x0d, 0x07, 0x00, 0x02,
 0x03, 0x0d, 0x00, 0x05, 0x14, 0x05, 0x02, 0x01,
 0x0e, 0x02, 0x0b, 0x0a, 0x05, 0x0c, 0x0b, 0x13,
 0x0c, 0x0f, 0x0c, 0x08, 0x0c, 0x10, 0x04, 0x11,
 0x10, 0x0c, 0x04, 0x0d, 0x01, 0x08, 0x0d, 0x01,
 0x0e, 0x0e, 0x0e, 0x0b, 0x11, 0x13, 0x0f, 0x0c,
 0x00, 0x05, 0x0c, 0x04, 0x02, 0x0e, 0x01, 0x09,
 0x01, 0x09, 0x11, 0x06, 0x11, 0x10, 0x09, 0x04,
 0x0c, 0x0b, 0x05, 0x0a, 0x06, 0x0a, 0x03, 0x03,
 0x0a, 0x07, 0x07, 0x0b, 0x10, 0x01, 0x09, 0x0e,
 0x10, 0x0b, 0x08, 0x06, 0x0c, 0x0b, 0x03, 0x07,
 0x0f, 0x11, 0x0a, 0x0f, 0x05, 0x03, 0x0e, 0x0e,
 0x05, 0x03, 0x10, 0x03, 0x01, 0x05, 0x06, 0x01,
 0x0d, 0x00, 0x0f, 0x11, 0x09, 0x10, 0x0c, 0x03,
 0x09, 0x0a, 0x0d, 0x07, 0x05, 0x08, 0x14, 0x0f,
 0x10, 0x09, 0x10, 0x05, 0x05, 0x03, 0x05, 0x09,
 0x0a, 0x0c, 0x08, 0x0e, 0x05, 0x09, 0x04, 0x09,
 0x00, 0x11, 0x0e, 0x06, 0x01, 0x05, 0x01, 0x0d,
 0x01, 0x0f, 0x01, 0x0b, 0x07, 0x01, 0x01, 0x0a,
 0x14, 0x0c, 0x02, 0x07, 0x0c, 0x07, 0x13, 0x00,
 0x0c, 0x0a, 0x02, 0x0d, 0x00, 0x12, 0x01, 0x0e,
 0x05, 0x0c, 0x12, 0x02, 0x04, 0x10, 0x03, 0x10,
 0x00, 0x0a, 0x0a, 0x0f, 0x0f, 0x03, 0x0d, 0x04,
 0x0d, 0x0f, 0x0d, 0x04, 0x0d, 0x03, 0x10, 0x0c,
 0x06, 0x06, 0x03, 0x0a, 0x10, 0x0f, 0x04, 0x10,
 0x0f, 0x04, 0x0f, 0x0f, 0x0f, 0x0e, 0x0d, 0x0d,
 0x01, 0x0a, 0x10, 0x03, 0x02, 0x0f, 0x13, 0x0c,
 0x05, 0x05, 0x05, 0x01, 0x13, 0x10, 0x0d, 0x11,
 0x02, 0x09, 0x01, 0x02, 0x03, 0x01, 0x0f, 0x08,
 0x10, 0x08, 0x11, 0x02, 0x01, 0x07, 0x0d, 0x00,
 0x10, 0x07, 0x0c, 0x0a, 0x08, 0x0f, 0x0b, 0x0f,
 0x15, 0x12, 0x07, 0x02, 0x0b, 0x02, 0x06, 0x06,
 0x00, 0x05, 0x0d, 0x10, 0x08, 0x04, 0x0a, 0x07,
 0x0e, 0x02, 0x02, 0x06, 0x12, 0x00, 0x05, 0x0c,
 0x07, 0x0e, 0x0f, 0x0b, 0x03, 0x0d, 0x00, 0x0a,
 0x01, 0x06, 0x10, 0x14, 0x14, 0x07, 0x14, 0x09,
 0x0d, 0x0f, 0x0a, 0x12, 0x0d, 0x0d, 0x0a, 0x09,
 0x0e, 0x0d, 0x0d, 0x00, 0x04, 0x0f, 0x09, 0x01,
 0x0d, 0x01, 0x0f, 0x10, 0x05, 0x0b, 0x08, 0x00,
 0x09, 0x10, 0x08, 0x11, 0x0e, 0x0c, 0x0e, 0x01,
 0x03, 0x0c, 0x01, 0x03, 0x07, 0x0f, 0x0f, 0x13,
 0x0e, 0x06, 0x0f, 0x00, 0x09, 0x0c, 0x04, 0x02,
 0x04, 0x0b, 0x13, 0x04, 0x07, 0x0d, 0x08, 0x0c,
 0x0a, 0x0e, 0x10, 0x0b, 0x00, 0x0c, 0x03, 0x0b,
 0x07, 0x08, 0x08, 0x00, 0x03, 0x05, 0x0e, 0x0c,
 0x13, 0x12, 0x10, 0x06, 0x00, 0x05, 0x13, 0x0d,
 0x0f, 0x0f, 0x0f, 0x01, 0x0a, 0x10, 0x03, 0x08,
 0x03, 0x0c, 0x07, 0x11, 0x13, 0x0f, 0x0e, 0x0c,
 0x01, 0x03, 0x01, 0x0d, 0x11, 0x11, 0x01, 0x0c,
 0x0e, 0x09, 0x0d, 0x02, 0x11, 0x08, 0x10, 0x0a,
 0x10, 0x10, 0x0c, 0x04, 0x04, 0x11, 0x07, 0x07,
 0x03, 0x10, 0x0f, 0x00, 0x09, 0x0b, 0x14, 0x15,
 0x09, 0x0f, 0x0f, 0x0b, 0x0a, 0x0c, 0x0a, 0x0e,
 0x0d, 0x0d, 0x07, 0x02, 0x03, 0x0d, 0x0d, 0x13,
 0x08, 0x11, 0x09, 0x00, 0x0e, 0x09, 0x0f, 0x0e,
 0x0e, 0x00, 0x00, 0x15, 0x15, 0x0d, 0x0e, 0x10,
 0x0a, 0x01, 0x0e, 0x0a, 0x10, 0x0d, 0x00, 0x01,
 0x02, 0x00, 0x0d, 0x00, 0x0c, 0x05, 0x0c, 0x0e,
 0x12, 0x10, 0x0a, 0x00, 0x09, 0x06, 0x00, 0x08,
 0x00, 0x0a, 0x0e, 0x0a, 0x03, 0x09, 0x15, 0x0e,
 0x10, 0x0e, 0x0b, 0x0b, 0x0e, 0x0d, 0x10, 0x01,
 0x10, 0x02, 0x02, 0x09, 0x10, 0x0c, 0x0f, 0x0a,
 0x00, 0x0f, 0x0c, 0x03, 0x11, 0x11, 0x11, 0x0a,
 0x0e, 0x10, 0x07, 0x0a, 0x05, 0x10, 0x0d, 0x0b,
 0x0d, 0x0e, 0x0d, 0x09, 0x04, 0x0f, 0x13, 0x10,
 0x11, 0x0b, 0x0f, 0x13, 0x09, 0x0f, 0x04, 0x0b,
 0x07, 0x0a, 0x05, 0x10, 0x08, 0x05, 0x0f, 0x07,
 0x0a, 0x0e, 0x0f, 0x08, 0x11, 0x09, 0x03, 0x03,
 0x13, 0x04, 0x08, 0x0e, 0x10, 0x0f, 0x0d, 0x0f,
 0x11, 0x01, 0x0d, 0x07, 0x11, 0x08, 0x0e, 0x0b,
 0x11, 0x0b, 0x11, 0x04, 0x08, 0x0c, 0x11, 0x08,
 0x0d, 0x0e, 0x08, 0x09, 0x0f, 0x07, 0x10, 0x08,
 0x09, 0x0b, 0x0c, 0x08, 0x0d, 0x0c, 0x15, 0x0a,
 0x0d, 0x11, 0x0e, 0x0e, 0x07, 0x11, 0x01, 0x02,
 0x08, 0x02, 0x10, 0x0d, 0x11, 0x0f, 0x13, 0x0f,
 0x09, 0x06, 0x08, 0x0c, 0x0e, 0x00, 0x10, 0x04,
 0x10, 0x0d, 0x05, 0x0b, 0x0e, 0x0e, 0x0e, 0x04,
 0x0e, 0x07, 0x0c, 0x10, 0x03, 0x0a, 0x12, 0x0d,
 0x11, 0x12, 0x0c, 0x0a, 0x0f, 0x05, 0x08, 0x07,
 0x0e, 0x0c, 0x0c, 0x13, 0x0e, 0x0e, 0x13, 0x06,
 0x03, 0x06, 0x0f, 0x05, 0x0a, 0x0d, 0x10, 0x0d,
 0x07, 0x05, 0x03, 0x10, 0x00, 0x01, 0x11, 0x11,
 0x0c, 0x08, 0x0f, 0x0b, 0x0d, 0x03, 0x03, 0x09,
 0x05, 0x10, 0x10, 0x04, 0x0b, 0x0f, 0x0d, 0x15,
 0x0e, 0x0f, 0x06, 0x0c, 0x0f, 0x09, 0x10, 0x0f,
 0x03, 0x0f, 0x05, 0x13, 0x07, 0x07, 0x0f, 0x08,
 0x0e, 0x11, 0x0f, 0x15, 0x0d, 0x01, 0x0e, 0x0f,
 0x09, 0x11, 0x0d, 0x09, 0x0e, 0x06, 0x0d, 0x0f,
 0x01, 0x12, 0x05, 0x04, 0x02, 0x11, 0x07, 0x11,
 0x10, 0x0f, 0x10, 0x0d, 0x09, 0x10, 0x01, 0x03,
 0x13, 0x11, 0x06, 0x01, 0x0b, 0x0c, 0x06, 0x0c,
 0x07, 0x0b, 0x0b, 0x0f, 0x02, 0x0e, 0x11, 0x10,
 0x0d, 0x0c, 0x02, 0x0f, 0x10, 0x10, 0x12, 0x0c,
 0x09, 0x08, 0x08, 0x06, 0x11, 0x0c, 0x10, 0x09,
 0x04, 0x00, 0x01, 0x09, 0x0d, 0x05, 0x10, 0x0f,
 0x10, 0x11, 0x07, 0x0f, 0x07, 0x16, 0x03, 0x06,
 0x00, 0x01, 0x0c, 0x00, 0x12, 0x10, 0x07, 0x11,
 0x11, 0x12, 0x0a, 0x0c, 0x0c, 0x0a, 0x0c, 0x12,
 0x03, 0x10, 0x09, 0x03, 0x03, 0x0f, 0x12, 0x06,
 0x0b, 0x0d, 0x13, 0x03, 0x06, 0x02, 0x03, 0x0e,
 0x0b, 0x11, 0x04, 0x05, 0x02, 0x06, 0x0c, 0x0f,
 0x0f, 0x12, 0x0c, 0x00, 0x0e, 0x04, 0x03, 0x02,
 0x0c, 0x05, 0x07, 0x08, 0x0c, 0x0e, 0x08, 0x08,
 0x0b, 0x04, 0x03, 0x12, 0x0d, 0x12, 0x0f, 0x04,
 0x02, 0x0c, 0x01, 0x01, 0x0c, 0x06, 0x10, 0x01,
 0x16, 0x0a, 0x00, 0x12, 0x04, 0x11, 0x10, 0x0d,
 0x07, 0x06, 0x10, 0x08, 0x0d, 0x01, 0x0d, 0x0d,
 0x0f, 0x0e, 0x08, 0x12, 0x0a, 0x10, 0x08, 0x0d,
 0x06, 0x11, 0x07, 0x0d, 0x02, 0x06, 0x0b, 0x0e,
 0x10, 0x0c, 0x00, 0x0d, 0x0f, 0x00, 0x01, 0x0a,
 0x10, 0x0d, 0x0f, 0x13, 0x0d, 0x01, 0x05, 0x09,
 0x0b, 0x0d, 0x13, 0x11, 0x04, 0x12, 0x05, 0x02,
 0x16, 0x01, 0x12, 0x0c, 0x15, 0x11, 0x0e, 0x04,
 0x0f, 0x10, 0x0b, 0x00, 0x0d, 0x01, 0x0d, 0x0d,
 0x13, 0x0d, 0x03, 0x12, 0x00, 0x0f, 0x0f, 0x13,
 0x13, 0x09, 0x11, 0x06, 0x03, 0x0c, 0x11, 0x13,
 0x0b, 0x0d, 0x0a, 0x06, 0x0c, 0x15, 0x09, 0x13,
 0x09, 0x0a, 0x01, 0x0b, 0x0d, 0x05, 0x09, 0x00,
 0x0f, 0x07, 0x09, 0x06, 0x0a, 0x0b, 0x0d, 0x08,
 0x12, 0x11, 0x01, 0x12, 0x0c, 0x0f, 0x0d, 0x0c,
 0x0c, 0x13, 0x12, 0x05, 0x0f, 0x0f, 0x07, 0x0c,
 0x02, 0x0d, 0x13, 0x04, 0x07, 0x03, 0x00, 0x11,
 0x10, 0x13, 0x08, 0x07, 0x0c, 0x04, 0x13, 0x10,
 0x05, 0x0f, 0x13, 0x0c, 0x09, 0x10, 0x07, 0x11,
 0x10, 0x13, 0x0c, 0x0f, 0x10, 0x10, 0x06, 0x0b,
 0x07, 0x00, 0x07, 0x10, 0x0c, 0x12, 0x00, 0x12,
 0x03, 0x09, 0x07, 0x0a, 0x10, 0x09, 0x0f, 0x12,
 0x03, 0x02, 0x0e, 0x11, 0x0b, 0x09, 0x0b, 0x0e,
 0x0b, 0x08, 0x0c, 0x0c, 0x14, 0x11, 0x0d, 0x07,
 0x0a, 0x0a, 0x0c, 0x09, 0x0c, 0x03, 0x0d, 0x0e,
 0x0f, 0x0d, 0x0d, 0x0d, 0x14, 0x11, 0x0e, 0x0e,
 0x13, 0x08, 0x07, 0x0d, 0x0f, 0x10, 0x10, 0x00,
 0x15, 0x02, 0x0a, 0x0e, 0x11, 0x15, 0x12, 0x09,
 0x0d, 0x08, 0x10, 0x0c, 0x06, 0x0d, 0x14, 0x01,
 0x00, 0x12, 0x10, 0x0a, 0x01, 0x17, 0x0f, 0x03,
 0x08, 0x0a, 0x12, 0x0b, 0x09, 0x01, 0x0a, 0x01,
 0x04, 0x0c, 0x08, 0x12, 0x0c, 0x03, 0x0e, 0x04,
 0x05, 0x0d, 0x08, 0x12, 0x0d, 0x12, 0x0f, 0x03,
 0x09, 0x0c, 0x0e, 0x0a, 0x0f, 0x06, 0x18, 0x10,
 0x00, 0x10, 0x0a, 0x04, 0x0a, 0x0f, 0x0b, 0x01,
 0x10, 0x11, 0x08, 0x00, 0x03, 0x00, 0x0c, 0x10,
 0x0f, 0x0f, 0x11, 0x11, 0x00, 0x16, 0x06, 0x0d,
 0x07, 0x12, 0x04, 0x0b, 0x14, 0x0a, 0x04, 0x05,
 0x13, 0x13, 0x10, 0x03, 0x05, 0x10, 0x09, 0x06,
 0x12, 0x0e, 0x01, 0x13, 0x0d, 0x09, 0x0d, 0x12,
 0x02, 0x04, 0x0b, 0x07, 0x0e, 0x0f, 0x0f, 0x10,
 0x12, 0x04, 0x0b, 0x0c, 0x0c, 0x12, 0x0e, 0x0e,
 0x0b, 0x0e, 0x0b, 0x02, 0x13, 0x0e, 0x12, 0x09,
 0x0b, 0x13, 0x03, 0x0e, 0x04, 0x10, 0x0a, 0x08,
 0x0c, 0x02, 0x06, 0x0e, 0x0a, 0x13, 0x09, 0x04,
 0x08, 0x04, 0x0d, 0x0b, 0x0d, 0x0d, 0x00, 0x0f,
 0x11, 0x08, 0x0a, 0x03, 0x12, 0x11, 0x0d, 0x04,
 0x03, 0x0e, 0x0c, 0x0a, 0x0f, 0x0f, 0x0f, 0x0d,
 0x09, 0x0a, 0x0f, 0x11, 0x0e, 0x0a, 0x13, 0x0a,
 0x11, 0x19, 0x0c, 0x11, 0x03, 0x0b, 0x10, 0x05,
 0x10, 0x0a, 0x12, 0x0a, 0x01, 0x09, 0x06, 0x03,
 0x03, 0x0e, 0x11, 0x0a, 0x0e, 0x09, 0x0e, 0x10,
 0x00, 0x11, 0x0a, 0x07, 0x10, 0x00, 0x0e, 0x08,
 0x08, 0x04, 0x13, 0x13, 0x0f, 0x0d, 0x0d, 0x0b,
 0x10, 0x0a, 0x0c, 0x0c, 0x14, 0x06, 0x11, 0x0f,
 0x10, 0x0f, 0x05, 0x0b, 0x0a, 0x0f, 0x0a, 0x10,
 0x0f, 0x04, 0x09, 0x0f, 0x0f, 0x0f, 0x10, 0x10,
 0x08, 0x0e, 0x03, 0x10, 0x01, 0x0a, 0x0d, 0x0f,
 0x0a, 0x10, 0x03, 0x0c, 0x00, 0x08, 0x13, 0x15,
 0x03, 0x0d, 0x10, 0x0b, 0x0f, 0x04, 0x0a, 0x11,
 0x06, 0x10, 0x0e, 0x0e, 0x07, 0x0b, 0x16, 0x0d,
 0x09, 0x0d, 0x0a, 0x0c, 0x10, 0x0a, 0x0c, 0x16,
 0x0e, 0x0e, 0x12, 0x10, 0x0b, 0x0f, 0x0c, 0x13,
 0x02, 0x06, 0x0c, 0x04, 0x0a, 0x03, 0x05, 0x0b,
 0x11, 0x08, 0x10, 0x05, 0x11, 0x09, 0x0e, 0x15,
 0x0f, 0x0e, 0x09, 0x15, 0x03, 0x10, 0x07, 0x12,
 0x10, 0x09, 0x0f, 0x10, 0x01, 0x0b, 0x09, 0x08
};

// X max array (64 x 32)
static const int x_max[] =
{
 0x15, 0x11, 0x16, 0x1e, 0x1c, 0x1f, 0x19, 0x16,
 0x1e, 0x10, 0x1c, 0x1c, 0x1e, 0x10, 0x1f, 0x1c,
 0x13, 0x16, 0x16, 0x19, 0x12, 0x17, 0x1a, 0x13,
 0x16, 0x0f, 0x1e, 0x1a, 0x1b, 0x16, 0x16, 0x12,
 0x1c, 0x15, 0x19, 0x19, 0x17, 0x1f, 0x1c, 0x1c,
 0x1f, 0x11, 0x15, 0x13, 0x14, 0x1f, 0x12, 0x1e,
 0x18, 0x1e, 0x1e, 0x0f, 0x18, 0x1d, 0x16, 0x12,
 0x12, 0x1d, 0x1d, 0x1d, 0x1a, 0x0d, 0x13, 0x17,
 0x1c, 0x1b, 0x1d, 0x12, 0x19, 0x1a, 0x1e, 0x1b,
 0x10, 0x1c, 0x1f, 0x12, 0x17, 0x10, 0x1d, 0x16,
 0x18, 0x1d, 0x1f, 0x1c, 0x0f, 0x16, 0x17, 0x1c,
 0x0f, 0x17, 0x1e, 0x19, 0x19, 0x12, 0x13, 0x18,
 0x1e, 0x1b, 0x18, 0x1f, 0x1c, 0x19, 0x1e, 0x15,
 0x18, 0x1d, 0x19, 0x1b, 0x1c, 0x13, 0x19, 0x19,
 0x1c, 0x1f, 0x12, 0x15, 0x12, 0x18, 0x18, 0x15,
 0x1e, 0x1c, 0x1a, 0x1b, 0x19, 0x16, 0x12, 0x1f,
 0x1c, 0x1d, 0x14, 0x1c, 0x1e, 0x1b, 0x17, 0x1d,
 0x19, 0x1f, 0x14, 0x17, 0x18, 0x19, 0x1f, 0x17,
 0x1c, 0x15, 0x1d, 0x19, 0x14, 0x1e, 0x19, 0x18,
 0x18, 0x1e, 0x16, 0x0f, 0x19, 0x0e, 0x18, 0x11,
 0x1f, 0x1b, 0x1d, 0x13, 0x19, 0x1f, 0x1a, 0x1f,
 0x12, 0x17, 0x1f, 0x12, 0x16, 0x18, 0x1d, 0x10,
 0x13, 0x18, 0x0d, 0x18, 0x16, 0x16, 0x1c, 0x10,
 0x18, 0x1c, 0x1e, 0x10, 0x1c, 0x18, 0x1d, 0x1f,
 0x1c, 0x11, 0x19, 0x1d, 0x11, 0x17, 0x19, 0x15,
 0x17, 0x18, 0x17, 0x11, 0x1f, 0x18, 0x18, 0x1e,
 0x17, 0x14, 0x13, 0x17, 0x1c, 0x13, 0x1e, 0x14,
 0x10, 0x1a, 0x15, 0x15, 0x18, 0x1a, 0x12, 0x14,
 0x16, 0x1b, 0x12, 0x1c, 0x12, 0x18, 0x1c, 0x15,
 0x1e, 0x13, 0x1c, 0x15, 0x1d, 0x1c, 0x19, 0x1e,
 0x0f, 0x13, 0x1f, 0x1a, 0x18, 0x17, 0x16, 0x1d,
 0x14, 0x1b, 0x12, 0x0c, 0x1f, 0x1e, 0x12, 0x14,
 0x1d, 0x12, 0x15, 0x1a, 0x1c, 0x1f, 0x12, 0x15,
 0x1c, 0x1a, 0x12, 0x1c, 0x13, 0x16, 0x11, 0x16,
 0x1f, 0x1f, 0x15, 0x12, 0x16, 0x13, 0x19, 0x12,
 0x17, 0x1b, 0x18, 0x19, 0x19, 0x14, 0x1d, 0x1d,
 0x1c, 0x1d, 0x1b, 0x12, 0x1d, 0x1a, 0x15, 0x12,
 0x18, 0x1b, 0x19, 0x15, 0x1c, 0x14, 0x10, 0x1c,
 0x19, 0x18, 0x14, 0x1f, 0x15, 0x1a, 0x1a, 0x18,
 0x1d, 0x14, 0x14, 0x18, 0x18, 0x0e, 0x1d, 0x1f,
 0x1d, 0x14, 0x17, 0x1a, 0x16, 0x1b, 0x1b, 0x1a,
 0x1e, 0x11, 0x1e, 0x1d, 0x1a, 0x12, 0x13, 0x1e,
 0x1b, 0x13, 0x1f, 0x1e, 0x0c, 0x13, 0x1f, 0x1c,
 0x1f, 0x1b, 0x19, 0x1e, 0x18, 0x13, 0x1d, 0x1a,
 0x17, 0x1e, 0x13, 0x1c, 0x19, 0x15, 0x1d, 0x1b,
 0x1d, 0x18, 0x1f, 0x1f, 0x1c, 0x10, 0x12, 0x1f,
 0x1e, 0x11, 0x15, 0x19, 0x16, 0x12, 0x1b, 0x15,
 0x17, 0x1c, 0x10, 0x1f, 0x13, 0x1d, 0x0e, 0x1d,
 0x1b, 0x18, 0x1c, 0x13, 0x1a, 0x11, 0x1c, 0x1c,
 0x1b, 0x1f, 0x18, 0x13, 0x12, 0x14, 0x1e, 0x14,
 0x17, 0x0f, 0x10, 0x16, 0x13, 0x18, 0x1b, 0x1a,
 0x18, 0x18, 0x1d, 0x1d, 0x1c, 0x1e, 0x15, 0x1c,
 0x10, 0x18, 0x1f, 0x1e, 0x1d, 0x19, 0x11, 0x12,
 0x1a, 0x13, 0x1d, 0x14, 0x1a, 0x0f, 0x1b, 0x13,
 0x14, 0x1d, 0x1e, 0x0f, 0x15, 0x10, 0x1b, 0x13,
 0x17, 0x18, 0x1b, 0x1d, 0x1a, 0x0f, 0x1d, 0x17,
 0x17, 0x0f, 0x1d, 0x1d, 0x13, 0x1f, 0x18, 0x16,
 0x12, 0x0e, 0x1c, 0x1c, 0x1b, 0x0e, 0x17, 0x10,
 0x15, 0x12, 0x1c, 0x1e, 0x15, 0x1f, 0x14, 0x18,
 0x10, 0x13, 0x1a, 0x1d, 0x19, 0x10, 0x1b, 0x0e,
 0x1c, 0x19, 0x19, 0x15, 0x13, 0x14, 0x1e, 0x14,
 0x18, 0x1c, 0x0b, 0x13, 0x0f, 0x1a, 0x19, 0x1d,
 0x1d, 0x1f, 0x1d, 0x1e, 0x1d, 0x13, 0x13, 0x1d,
 0x1d, 0x1c, 0x1d, 0x11, 0x10, 0x14, 0x19, 0x11,
 0x19, 0x13, 0x11, 0x1b, 0x1d, 0x15, 0x15, 0x1b,
 0x16, 0x12, 0x12, 0x17, 0x1a, 0x1d, 0x1f, 0x17,
 0x19, 0x1c, 0x16, 0x1b, 0x0c, 0x15, 0x10, 0x12,
 0x1f, 0x13, 0x11, 0x19, 0x15, 0x16, 0x0f, 0x0c,
 0x14, 0x11, 0x1e, 0x11, 0x13, 0x1a, 0x16, 0x18,
 0x13, 0x1e, 0x17, 0x15, 0x1b, 0x16, 0x12, 0x16,
 0x19, 0x1d, 0x17, 0x1d, 0x0d, 0x1c, 0x10, 0x1e,
 0x14, 0x14, 0x10, 0x18, 0x15, 0x18, 0x0e, 0x19,
 0x1b, 0x17, 0x0f, 0x1d, 0x15, 0x14, 0x11, 0x19,
 0x16, 0x1d, 0x12, 0x12, 0x19, 0x18, 0x1c, 0x12,
 0x1f, 0x0b, 0x1c, 0x1a, 0x1d, 0x1d, 0x0e, 0x1a,
 0x0d, 0x19, 0x1d, 0x1e, 0x0f, 0x10, 0x1f, 0x19,
 0x0f, 0x1e, 0x19, 0x0d, 0x18, 0x18, 0x0f, 0x19,
 0x1e, 0x1d, 0x15, 0x16, 0x14, 0x15, 0x1e, 0x14,
 0x15, 0x12, 0x1b, 0x1c, 0x1e, 0x10, 0x17, 0x15,
 0x12, 0x1f, 0x14, 0x15, 0x17, 0x11, 0x15, 0x17,
 0x12, 0x1d, 0x16, 0x11, 0x1e, 0x15, 0x10, 0x0f,
 0x13, 0x15, 0x19, 0x1e, 0x1b, 0x13, 0x13, 0x1a,
 0x0e, 0x1e, 0x18, 0x1f, 0x14, 0x19, 0x18, 0x11,
 0x13, 0x1a, 0x17, 0x1c, 0x18, 0x0e, 0x14, 0x10,
 0x17, 0x1c, 0x1c, 0x14, 0x1e, 0x13, 0x1f, 0x13,
 0x14, 0x19, 0x1d, 0x18, 0x1e, 0x18, 0x11, 0x18,
 0x1f, 0x0b, 0x12, 0x15, 0x17, 0x18, 0x16, 0x19,
 0x10, 0x18, 0x16, 0x16, 0x12, 0x18, 0x17, 0x1e,
 0x1c, 0x10, 0x17, 0x0f, 0x12, 0x1f, 0x19, 0x1a,
 0x14, 0x18, 0x1a, 0x1c, 0x1b, 0x1c, 0x1d, 0x1c,
 0x1c, 0x1b, 0x11, 0x1f, 0x1a, 0x1d, 0x15, 0x1e,
 0x12, 0x1f, 0x18, 0x1b, 0x1a, 0x1a, 0x1f, 0x0f,
 0x13, 0x1f, 0x19, 0x17, 0x12, 0x1e, 0x0f, 0x15,
 0x13, 0x1f, 0x14, 0x1e, 0x14, 0x11, 0x10, 0x16,
 0x13, 0x14, 0x18, 0x1c, 0x1a, 0x1c, 0x1b, 0x1f,
 0x1b, 0x12, 0x14, 0x1b, 0x11, 0x0e, 0x10, 0x11,
 0x0f, 0x16, 0x1a, 0x11, 0x1f, 0x16, 0x1c, 0x18,
 0x0f, 0x1e, 0x17, 0x19, 0x14, 0x18, 0x13, 0x17,
 0x1c, 0x0f, 0x16, 0x1d, 0x17, 0x14, 0x1f, 0x1b,
 0x1b, 0x1f, 0x16, 0x12, 0x14, 0x12, 0x10, 0x17,
 0x13, 0x11, 0x14, 0x13, 0x0f, 0x1b, 0x1e, 0x15,
 0x10, 0x1e, 0x1e, 0x1c, 0x1b, 0x18, 0x16, 0x1b,
 0x1c, 0x1c, 0x0a, 0x14, 0x19, 0x0b, 0x14, 0x1d,
 0x1d, 0x15, 0x0e, 0x12, 0x19, 0x0f, 0x1a, 0x14,
 0x1e, 0x16, 0x1c, 0x11, 0x1e, 0x1d, 0x16, 0x10,
 0x10, 0x13, 0x1f, 0x16, 0x1b, 0x1c, 0x12, 0x1f,
 0x15, 0x0e, 0x0f, 0x12, 0x16, 0x1b, 0x14, 0x10,
 0x17, 0x16, 0x1b, 0x1a, 0x14, 0x0f, 0x1a, 0x0f,
 0x0f, 0x16, 0x16, 0x16, 0x1f, 0x14, 0x0e, 0x1a,
 0x12, 0x18, 0x1b, 0x18, 0x12, 0x15, 0x1e, 0x16,
 0x17, 0x1a, 0x1f, 0x0e, 0x11, 0x1a, 0x1d, 0x12,
 0x0d, 0x1b, 0x0d, 0x1f, 0x1e, 0x19, 0x15, 0x1e,
 0x1a, 0x1f, 0x12, 0x16, 0x16, 0x12, 0x14, 0x13,
 0x1c, 0x18, 0x1f, 0x1a, 0x0c, 0x1e, 0x15, 0x08,
 0x12, 0x16, 0x14, 0x0f, 0x17, 0x1d, 0x18, 0x1a,
 0x1f, 0x1b, 0x1b, 0x0c, 0x0f, 0x0f, 0x15, 0x18,
 0x1a, 0x13, 0x1a, 0x1e, 0x10, 0x16, 0x19, 0x1c,
 0x15, 0x16, 0x13, 0x1c, 0x1c, 0x1c, 0x1b, 0x0f,
 0x11, 0x13, 0x1a, 0x13, 0x0c, 0x1b, 0x0d, 0x11,
 0x1a, 0x0d, 0x14, 0x1e, 0x1d, 0x1d, 0x1b, 0x15,
 0x16, 0x16, 0x16, 0x1e, 0x10, 0x1c, 0x19, 0x1f,
 0x14, 0x18, 0x1a, 0x1c, 0x11, 0x1a, 0x12, 0x14,
 0x0f, 0x13, 0x1b, 0x11, 0x1c, 0x10, 0x10, 0x11,
 0x17, 0x18, 0x0f, 0x13, 0x17, 0x11, 0x1e, 0x0e,
 0x11, 0x1c, 0x1c, 0x19, 0x17, 0x17, 0x1a, 0x1e,
 0x0f, 0x15, 0x18, 0x0d, 0x18, 0x1b, 0x0f, 0x14,
 0x0e, 0x17, 0x18, 0x0f, 0x10, 0x18, 0x1c, 0x11,
 0x18, 0x15, 0x19, 0x1d, 0x10, 0x0f, 0x1b, 0x17,
 0x12, 0x1a, 0x1f, 0x1e, 0x11, 0x13, 0x0f, 0x17,
 0x1d, 0x1e, 0x11, 0x1f, 0x14, 0x18, 0x1c, 0x19,
 0x13, 0x11, 0x11, 0x16, 0x1c, 0x17, 0x13, 0x19,
 0x12, 0x1f, 0x13, 0x18, 0x1a, 0x17, 0x19, 0x0f,
 0x1a, 0x16, 0x1e, 0x1c, 0x19, 0x17, 0x0c, 0x17,
 0x15, 0x0f, 0x10, 0x0d, 0x1a, 0x16, 0x13, 0x1d,
 0x11, 0x12, 0x1b, 0x14, 0x14, 0x19, 0x1e, 0x15,
 0x1a, 0x12, 0x18, 0x11, 0x10, 0x18, 0x1f, 0x1f,
 0x1d, 0x1e, 0x1d, 0x13, 0x1f, 0x1b, 0x1c, 0x1b,
 0x11, 0x0e, 0x14, 0x11, 0x1d, 0x1b, 0x18, 0x17,
 0x10, 0x12, 0x0d, 0x15, 0x10, 0x11, 0x1f, 0x1b,
 0x1a, 0x10, 0x0d, 0x1c, 0x1a, 0x1e, 0x16, 0x17,
 0x0f, 0x17, 0x1f, 0x15, 0x15, 0x13, 0x18, 0x18,
 0x12, 0x1a, 0x10, 0x0c, 0x13, 0x1a, 0x16, 0x18,
 0x18, 0x19, 0x1d, 0x0d, 0x19, 0x0f, 0x15, 0x0d,
 0x0c, 0x1a, 0x1c, 0x15, 0x0e, 0x11, 0x1e, 0x10,
 0x17, 0x11, 0x1c, 0x1c, 0x14, 0x1f, 0x15, 0x14,
 0x1c, 0x16, 0x1e, 0x1f, 0x1d, 0x13, 0x19, 0x10,
 0x11, 0x10, 0x17, 0x0e, 0x13, 0x1c, 0x19, 0x1e,
 0x1e, 0x1f, 0x0e, 0x16, 0x12, 0x13, 0x11, 0x1a,
 0x1c, 0x1f, 0x13, 0x11, 0x15, 0x1a, 0x1a, 0x12,
 0x19, 0x17, 0x17, 0x1e, 0x0c, 0x12, 0x13, 0x12,
 0x10, 0x1e, 0x11, 0x1f, 0x0d, 0x18, 0x18, 0x1c,
 0x10, 0x15, 0x10, 0x17, 0x16, 0x16, 0x10, 0x19,
 0x1f, 0x13, 0x1a, 0x17, 0x1e, 0x1c, 0x15, 0x1c,
 0x19, 0x0d, 0x11, 0x1e, 0x1d, 0x0c, 0x16, 0x10,
 0x1c, 0x1b, 0x1b, 0x0d, 0x17, 0x10, 0x16, 0x13,
 0x13, 0x1f, 0x18, 0x13, 0x17, 0x13, 0x19, 0x11,
 0x1b, 0x19, 0x17, 0x13, 0x11, 0x13, 0x1e, 0x17,
 0x0e, 0x1b, 0x18, 0x1e, 0x1d, 0x1d, 0x1c, 0x18,
 0x1d, 0x0f, 0x13, 0x0f, 0x1a, 0x12, 0x1c, 0x14,
 0x0f, 0x0e, 0x1f, 0x13, 0x12, 0x16, 0x13, 0x18,
 0x19, 0x13, 0x17, 0x14, 0x19, 0x14, 0x14, 0x0e,
 0x19, 0x1f, 0x14, 0x1b, 0x0b, 0x0d, 0x1a, 0x14,
 0x14, 0x19, 0x0e, 0x0e, 0x1d, 0x0e, 0x19, 0x19,
 0x11, 0x18, 0x10, 0x1e, 0x18, 0x18, 0x1c, 0x0e,
 0x0f, 0x0f, 0x15, 0x10, 0x18, 0x10, 0x14, 0x1e,
 0x15, 0x0d, 0x1f, 0x17, 0x16, 0x0c, 0x1f, 0x1f,
 0x1f, 0x18, 0x19, 0x10, 0x10, 0x12, 0x1a, 0x0e,
 0x13, 0x1e, 0x16, 0x0e, 0x10, 0x10, 0x1d, 0x1f,
 0x12, 0x11, 0x1b, 0x1b, 0x13, 0x1d, 0x19, 0x11,
 0x17, 0x10, 0x17, 0x1f, 0x11, 0x11, 0x16, 0x11,
 0x15, 0x16, 0x12, 0x1c, 0x19, 0x0d, 0x12, 0x16,
 0x12, 0x17, 0x12, 0x10, 0x10, 0x0e, 0x12, 0x10,
 0x16, 0x1d, 0x1e, 0x1f, 0x0b, 0x19, 0x13, 0x19,
 0x1e, 0x12, 0x19, 0x17, 0x10, 0x11, 0x17, 0x10,
 0x17, 0x1f, 0x17, 0x0e, 0x18, 0x10, 0x14, 0x10,
 0x10, 0x1e, 0x12, 0x1f, 0x10, 0x1f, 0x1b, 0x12,
 0x1a, 0x1c, 0x16, 0x19, 0x14, 0x1a, 0x1c, 0x15,
 0x0f, 0x14, 0x14, 0x16, 0x1e, 0x1c, 0x14, 0x17,
 0x18, 0x1d, 0x1b, 0x13, 0x12, 0x0e, 0x14, 0x1d,
 0x1c, 0x1d, 0x0f, 0x12, 0x12, 0x1f, 0x13, 0x15,
 0x1b, 0x1d, 0x19, 0x0c, 0x1b, 0x17, 0x10, 0x1f,
 0x1b, 0x16, 0x11, 0x18, 0x11, 0x0e, 0x13, 0x14,
 0x1e, 0x11, 0x0f, 0x17, 0x0e, 0x19, 0x1e, 0x0e,
 0x10, 0x1b, 0x1c, 0x1a, 0x11, 0x0e, 0x10, 0x1d,
 0x12, 0x15, 0x14, 0x1b, 0x11, 0x0e, 0x11, 0x19,
 0x1b, 0x1a, 0x1e, 0x1f, 0x14, 0x17, 0x0e, 0x10,
 0x15, 0x14, 0x1f, 0x0e, 0x0e, 0x16, 0x13, 0x1d,
 0x12, 0x0e, 0x1d, 0x1d, 0x11, 0x19, 0x15, 0x17,
 0x1d, 0x1e, 0x18, 0x14, 0x18, 0x18, 0x1c, 0x0f,
 0x1f, 0x0f, 0x1d, 0x16, 0x10, 0x17, 0x12, 0x0e,
 0x1b, 0x14, 0x18, 0x16, 0x12, 0x0b, 0x1f, 0x1e,
 0x15, 0x12, 0x12, 0x1e, 0x12, 0x19, 0x0d, 0x17,
 0x0f, 0x1e, 0x1e, 0x10, 0x1a, 0x11, 0x15, 0x14,
 0x16, 0x1f, 0x17, 0x10, 0x11, 0x10, 0x0c, 0x10,
 0x11, 0x1b, 0x18, 0x1b, 0x19, 0x1d, 0x10, 0x18,
 0x12, 0x10, 0x19, 0x1e, 0x10, 0x1a, 0x0c, 0x16,
 0x1f, 0x1c, 0x18, 0x1a, 0x1e, 0x12, 0x13, 0x17,
 0x10, 0x18, 0x18, 0x17, 0x1b, 0x0f, 0x1c, 0x14,
 0x18, 0x18, 0x0f, 0x1d, 0x0b, 0x15, 0x18, 0x17,
 0x17, 0x1b, 0x15, 0x14, 0x19, 0x11, 0x19, 0x1a,
 0x13, 0x18, 0x10, 0x12, 0x12, 0x1e, 0x1d, 0x1c,
 0x14, 0x0e, 0x0c, 0x0e, 0x11, 0x12, 0x15, 0x1c,
 0x16, 0x0e, 0x0f, 0x13, 0x11, 0x0f, 0x19, 0x1b,
 0x1b, 0x1d, 0x10, 0x1c, 0x0f, 0x17, 0x15, 0x11,
 0x12, 0x18, 0x1e, 0x16, 0x15, 0x19, 0x17, 0x0f,
 0x18, 0x1b, 0x1a, 0x12, 0x15, 0x0f, 0x17, 0x1f,
 0x17, 0x19, 0x10, 0x15, 0x0f, 0x15, 0x18, 0x19,
 0x0c, 0x1a, 0x17, 0x10, 0x11, 0x12, 0x0f, 0x1e,
 0x13, 0x1d, 0x1d, 0x1f, 0x11, 0x15, 0x1c, 0x12,
 0x1c, 0x1f, 0x16, 0x17, 0x1a, 0x17, 0x11, 0x19,
 0x11, 0x17, 0x0f, 0x14, 0x18, 0x10, 0x13, 0x1d,
 0x11, 0x0c, 0x1c, 0x15, 0x1e, 0x11, 0x15, 0x10,
 0x12, 0x15, 0x0f, 0x0f, 0x1b, 0x16, 0x11, 0x11,
 0x14, 0x14, 0x1a, 0x17, 0x14, 0x1a, 0x12, 0x14,
 0x12, 0x17, 0x18, 0x10, 0x19, 0x1d, 0x10, 0x0f,
 0x17, 0x0e, 0x11, 0x11, 0x11, 0x18, 0x1c, 0x13,
 0x1a, 0x11, 0x19, 0x14, 0x18, 0x1f, 0x14, 0x13,
 0x12, 0x18, 0x1a, 0x1a, 0x0c, 0x0f, 0x19, 0x0e,
 0x16, 0x18, 0x13, 0x0f, 0x10, 0x1c, 0x13, 0x0c,
 0x13, 0x10, 0x18, 0x10, 0x14, 0x08, 0x0e, 0x18,
 0x12, 0x10, 0x11, 0x19, 0x19, 0x0f, 0x1b, 0x1a,
 0x10, 0x1e, 0x0d, 0x19, 0x17, 0x19, 0x1e, 0x0e,
 0x1a, 0x1a, 0x16, 0x17, 0x11, 0x1e, 0x1f, 0x15,
 0x1c, 0x11, 0x10, 0x0e, 0x0d, 0x1a, 0x1d, 0x13,
 0x13, 0x19, 0x0d, 0x19, 0x1b, 0x12, 0x1e, 0x16,
 0x13, 0x17, 0x1f, 0x1f, 0x12, 0x1d, 0x15, 0x1d,
 0x10, 0x19, 0x0c, 0x1d, 0x1e, 0x1e, 0x18, 0x18,
 0x17, 0x18, 0x1d, 0x0b, 0x0e, 0x1e, 0x14, 0x0b,
 0x1e, 0x16, 0x0d, 0x1b, 0x18, 0x1d, 0x0f, 0x1e,
 0x15, 0x13, 0x12, 0x12, 0x13, 0x17, 0x1d, 0x11,
 0x1c, 0x0c, 0x1f, 0x10, 0x0e, 0x18, 0x1a, 0x12,
 0x1b, 0x1f, 0x15, 0x0d, 0x1c, 0x1a, 0x14, 0x17,
 0x13, 0x1f, 0x17, 0x15, 0x0e, 0x17, 0x0d, 0x0f,
 0x11, 0x10, 0x13, 0x17, 0x17, 0x19, 0x1a, 0x0c,
 0x1b, 0x11, 0x10, 0x12, 0x16, 0x15, 0x08, 0x11,
 0x19, 0x0f, 0x17, 0x1c, 0x18, 0x19, 0x1a, 0x1f,
 0x1d, 0x11, 0x0f, 0x17, 0x1c, 0x19, 0x15, 0x0e,
 0x11, 0x10, 0x14, 0x16, 0x15, 0x0f, 0x17, 0x16,
 0x18, 0x1f, 0x12, 0x18, 0x1a, 0x14, 0x12, 0x0c,
 0x12, 0x1b, 0x19, 0x11, 0x11, 0x13, 0x1f, 0x0e,
 0x10, 0x18, 0x1c, 0x14, 0x17, 0x15, 0x0f, 0x12,
 0x0c, 0x15, 0x1d, 0x17, 0x16, 0x18, 0x10, 0x19,
 0x18, 0x0d, 0x19, 0x18, 0x13, 0x1a, 0x11, 0x12,
 0x15, 0x12, 0x17, 0x10, 0x1c, 0x0e, 0x12, 0x1a,
 0x16, 0x1a, 0x17, 0x11, 0x13, 0x14, 0x0f, 0x19,
 0x17, 0x0d, 0x19, 0x12, 0x14, 0x15, 0x14, 0x13,
 0x13, 0x1e, 0x12, 0x15, 0x0c, 0x13, 0x1a, 0x18,
 0x0e, 0x1a, 0x07, 0x13, 0x09, 0x13, 0x1e, 0x1a,
 0x0b, 0x1a, 0x1a, 0x1c, 0x14, 0x0c, 0x18, 0x13,
 0x1c, 0x1a, 0x12, 0x14, 0x19, 0x11, 0x1c, 0x13,
 0x0b, 0x15, 0x15, 0x1c, 0x1b, 0x11, 0x13, 0x1e,
 0x18, 0x11, 0x19, 0x15, 0x15, 0x12, 0x14, 0x1d,
 0x16, 0x11, 0x17, 0x10, 0x1f, 0x0e, 0x1f, 0x15,
 0x12, 0x0c, 0x15, 0x10, 0x12, 0x11, 0x10, 0x1d,
 0x17, 0x1c, 0x0e, 0x18, 0x12, 0x15, 0x13, 0x18,
 0x11, 0x1c, 0x1c, 0x14, 0x0c, 0x11, 0x11, 0x11
};

// alpha array (64 x 32)
static const unsigned int alpha[] =
{
 0x3cef7503, 0x3cfd2ba5, 0x3cf05aa6, 0x3cefcc41, 0x3cf38cf5, 0x3cee9758, 0x3ceb7fab, 0x3ce21a72,
 0x3ce314a2, 0x3cd8e905, 0x3cd7099c, 0x3cd63fa8, 0x3cd746d9, 0x3cd5926e, 0x3ccf1bfd, 0x3cc94f34,
 0x3cc3fd50, 0x3cc22ccb, 0x3cbef467, 0x3cbb2e75, 0x3cd8b60f, 0x3cb7036d, 0x3cb49ea3, 0x3cb4dd22,
 0x3caabf5c, 0x3cab61e3, 0x3ca8cfcd, 0x3cb089a0, 0x3ca72b88, 0x3cabd49a, 0x3ca47489, 0x3c9e6b5b,
 0x3ce56f91, 0x3cf2ac90, 0x3cea7d82, 0x3ce75ddd, 0x3ce70959, 0x3ce42b8e, 0x3cdf7795, 0x3cde1991,
 0x3cd31261, 0x3cd29a36, 0x3cd5138d, 0x3cca20b6, 0x3cc635da, 0x3cc6be96, 0x3cc4885b, 0x3cbead85,
 0x3cc1de27, 0x3cbcd757, 0x3cbba8ee, 0x3cba83bf, 0x3cb83962, 0x3cb9d401, 0x3cb9d727, 0x3cb13fde,
 0x3cae0f72, 0x3cad2964, 0x3ca61ee3, 0x3ca680d3, 0x3ca92df7, 0x3c9f746f, 0x3ca31437, 0x3c99db3e,
 0x3cea02d3, 0x3cf1baf9, 0x3cf26253, 0x3cf99c6e, 0x3cf41180, 0x3ce89c00, 0x3cec3182, 0x3ce1be61,
 0x3cd77bb2, 0x3cd8c3b1, 0x3cd763ca, 0x3cd03537, 0x3cd17f4f, 0x3cc8ed44, 0x3cc556b7, 0x3cc3abbc,
 0x3cbdaff9, 0x3cc50706, 0x3cba8f48, 0x3cba7b26, 0x3cbc5f97, 0x3cb68888, 0x3cb1d34c, 0x3cad5e3d,
 0x3cb2cb2e, 0x3caa16bf, 0x3cb1857e, 0x3ca6f38a, 0x3ca16865, 0x3ca3a6ce, 0x3ca1b882, 0x3ca2958c,
 0x3ce9c165, 0x3cf6ce93, 0x3cf33806, 0x3cf35929, 0x3ce572b6, 0x3ce96f65, 0x3ce0a70a, 0x3ce1e34a,
 0x3cd67705, 0x3cd3d16e, 0x3cd1753e, 0x3ccb6376, 0x3cc8ff18, 0x3cca361a, 0x3cc0dc69, 0x3cc2c435,
 0x3cc11f4f, 0x3cc0c232, 0x3cb8f86f, 0x3cb22c6d, 0x3cb5acc0, 0x3cb99151, 0x3cad121c, 0x3cacf4f6,
 0x3ca97686, 0x3cae9574, 0x3cab5ef4, 0x3ca380d9, 0x3ca690f9, 0x3ca1ee32, 0x3c985b41, 0x3c9d183f,
 0x3ce845cf, 0x3cec6e53, 0x3cedaf9b, 0x3ceb792b, 0x3ce22051, 0x3cde9948, 0x3cdbc416, 0x3cd5cfab,
 0x3cdb37ff, 0x3cccdaa4, 0x3cd96a34, 0x3ccb0d45, 0x3cc55f86, 0x3cc4ad0e, 0x3cc230c7, 0x3cbe0afe,
 0x3cbb242e, 0x3cbcd64a, 0x3cbc30d3, 0x3cb449b4, 0x3cba860e, 0x3cacdd43, 0x3cacac66, 0x3caa916e,
 0x3ca81df7, 0x3ca589fd, 0x3ca7e480, 0x3ca30f2e, 0x3ca3821b, 0x3c9ff7b7, 0x3c9cdd86, 0x3c9b54f0,
 0x3ce3d01f, 0x3ce5f0c0, 0x3ce59734, 0x3cdf7e80, 0x3cd7419b, 0x3cd8c1ce, 0x3ccf4913, 0x3cc99e7a,
 0x3cca515d, 0x3cc411dd, 0x3cc3f812, 0x3cc15d98, 0x3cc1bff4, 0x3cbbc0d6, 0x3cbcbdc1, 0x3cceae19,
 0x3cb5e636, 0x3cb25b31, 0x3cb46c4e, 0x3cb698e4, 0x3cb2f4ea, 0x3caf0938, 0x3ca70959, 0x3ca70486,
 0x3ca1d898, 0x3c9f6124, 0x3c9edee8, 0x3c9e4d28, 0x3c9a6b1b, 0x3c9a3098, 0x3c9a9725, 0x3c945309,
 0x3cda0cbc, 0x3cef6dac, 0x3ce41695, 0x3ce290b9, 0x3ce3d633, 0x3cdd0df8, 0x3cd74ed1, 0x3cccf180,
 0x3cce72f5, 0x3cceddb4, 0x3cca89c7, 0x3cbe3814, 0x3cbd4b50, 0x3cbf20a7, 0x3cb6611b, 0x3cb6541a,
 0x3cb3ee44, 0x3cb36d4b, 0x3cbbf21f, 0x3cb24024, 0x3cad952f, 0x3caa8728, 0x3ca9043b, 0x3ca7ce7b,
 0x3ca779c2, 0x3ca1e852, 0x3c9db080, 0x3c9bfd8d, 0x3c9b95f3, 0x3c9cc63f, 0x3ca0a237, 0x3c955d2a,
 0x3cdaed57, 0x3ce3f82d, 0x3ceb7168, 0x3cdea649, 0x3cdd6719, 0x3cd70ea5, 0x3cd18d91, 0x3cd00bb1,
 0x3cccdbb1, 0x3cd3a23f, 0x3cc45092, 0x3cc757ad, 0x3cbe7297, 0x3cb6db29, 0x3cbb3e9a, 0x3cb9d32b,
 0x3cd6bef4, 0x3caea59a, 0x3cb88730, 0x3cab8e59, 0x3cab97ff, 0x3ca552a0, 0x3ca6e257, 0x3ca57bbb,
 0x3ca3fcca, 0x3ca50e0c, 0x3c9f6307, 0x3c97e564, 0x3ca8ab85, 0x3c9b6cd8, 0x3c9e08ca, 0x3c931ec1,
 0x3cce6b32, 0x3ceb74c3, 0x3ce7dd29, 0x3ce51fe0, 0x3cd6967a, 0x3cd52ed1, 0x3cd7844b, 0x3cc9ff93,
 0x3ccc07e1, 0x3cc7e465, 0x3cc7c2a2, 0x3cc8eb2b, 0x3cbd7e46, 0x3cb754cb, 0x3cbbbe88, 0x3cb5dd67,
 0x3cba8496, 0x3cadf033, 0x3cb5092c, 0x3cab8950, 0x3cad835b, 0x3cac8f75, 0x3ca12f5a, 0x3cb9b090,
 0x3ca3765c, 0x3c9fe005, 0x3c9dd59e, 0x3c9c91d1, 0x3c99a954, 0x3c9a610a, 0x3c97c980, 0x3c9571b7,
 0x3ccd2044, 0x3ceb03ef, 0x3cdd1af9, 0x3ce30553, 0x3cdd6429, 0x3cda5368, 0x3cd2e657, 0x3ccd6e7e,
 0x3ccebd32, 0x3cce728a, 0x3cc56565, 0x3cc58dde, 0x3cbd9cae, 0x3cbaf6e1, 0x3cb99f28, 0x3cbab06b,
 0x3cb9dcd0, 0x3cb834fb, 0x3cb3ca9d, 0x3cb3db2f, 0x3caf5ce5, 0x3caa7fd1, 0x3ca7cff3, 0x3ca819c5,
 0x3ca4c86c, 0x3ca48b2f, 0x3ca6fa40, 0x3ca2f8f4, 0x3c9bcb38, 0x3c9a5e50, 0x3c9c574f, 0x3c9556a9,
 0x3ccd2fc9, 0x3cd83c6d, 0x3cdbeaad, 0x3cd8102d, 0x3cd68258, 0x3cd04959, 0x3ccb054c, 0x3cc857f3,
 0x3cbf7ed1, 0x3cc4ed3a, 0x3cbac2a9, 0x3cc398a6, 0x3cb6d8da, 0x3cb49782, 0x3cb332fe, 0x3cb1787e,
 0x3cad1684, 0x3cb199d6, 0x3cb113d4, 0x3cabf2cd, 0x3ca830d6, 0x3ca45abe, 0x3ca4ba5f, 0x3c9e7bec,
 0x3c9f5c1c, 0x3c98da21, 0x3ca052f1, 0x3c9b0d01, 0x3c94082a, 0x3c938a56, 0x3c8e7d71, 0x3c8a44fd,
 0x3cccb3d8, 0x3cd6436e, 0x3ce10a06, 0x3cd25969, 0x3cd0ca1d, 0x3ccaea3f, 0x3ccc43db, 0x3cc51838,
 0x3cc97f05, 0x3cbb81ec, 0x3cb9954d, 0x3cc078cc, 0x3cb9da4c, 0x3cb42d64, 0x3cb6357c, 0x3caf67cd,
 0x3caf7ae2, 0x3cb3845c, 0x3cad2e01, 0x3cae613c, 0x3caaa84a, 0x3ca586d8, 0x3ca534d8, 0x3c9fb3fa,
 0x3ca2af57, 0x3c9b8ad6, 0x3ca08f57, 0x3c9aa4fd, 0x3c95c19e, 0x3c9ce945, 0x3c91cd52, 0x3c91c98c,
 0x3cbe9f0d, 0x3ccf0ec6, 0x3cd61b96, 0x3cd486a0, 0x3ccc334a, 0x3cc628d9, 0x3cd082cf, 0x3cc11e79,
 0x3cbfabb2, 0x3cbdd65a, 0x3cba87f1, 0x3cbd298c, 0x3cb7b03b, 0x3cb4da9d, 0x3cb2e317, 0x3cb469ca,
 0x3cae66b0, 0x3cb14ec2, 0x3ca7041b, 0x3ca7cbf7, 0x3ca5aaea, 0x3c9dd2af, 0x3c9dbca9, 0x3c9cb868,
 0x3ca074eb, 0x3c98b27e, 0x3c9a191b, 0x3c983a1e, 0x3c93db14, 0x3c949873, 0x3c8d9d77, 0x3c8e0074,
 0x3cd5025b, 0x3cd57a1a, 0x3cd42f2c, 0x3ccbbc2c, 0x3cc82101, 0x3cca488e, 0x3cc57c76, 0x3ccd03bf,
 0x3cba2dc4, 0x3cbadb68, 0x3cb83bb1, 0x3cb12ab0, 0x3cb4f575, 0x3cb4ee8a, 0x3cb1d2ab, 0x3cac0288,
 0x3cab6bf4, 0x3ca82441, 0x3ca459b1, 0x3cb976e4, 0x3c9ddcf5, 0x3caaa98c, 0x3c9d2e44, 0x3c9b0758,
 0x3c9c1250, 0x3c97d432, 0x3c9852a7, 0x3c9275b9, 0x3c93179f, 0x3c9e33c8, 0x3c9001a0, 0x3c8768e0,
 0x3cc6b9c3, 0x3cd7ef40, 0x3ccc78b4, 0x3cd1390d, 0x3cc5ef99, 0x3cc56854, 0x3cc2d3ba, 0x3cc05b3a,
 0x3cc07896, 0x3cb9280a, 0x3cb8d4fe, 0x3cb48a81, 0x3cb568cd, 0x3cb16d2b, 0x3cb3c9fc, 0x3ca87d2c,
 0x3cacfb76, 0x3cadd8b6, 0x3ca7eacb, 0x3ca14f05, 0x3c9d5ff8, 0x3ca0c862, 0x3c9b99ef, 0x3c970098,
 0x3c98ac34, 0x3c950947, 0x3c9333b9, 0x3c9588fe, 0x3c8d0170, 0x3c8e7b58, 0x3c916477, 0x3c942f98,
 0x3cc50e5d, 0x3cd87ab6, 0x3cdb7a44, 0x3cd3bb33, 0x3ccc99d7, 0x3ccbe7ca, 0x3ccb2be3, 0x3cc3745e,
 0x3cc89e34, 0x3cc23a6d, 0x3cb7a919, 0x3cbef757, 0x3cb5b482, 0x3cb4ccfc, 0x3cb3c84f, 0x3cb280f1,
 0x3ca91502, 0x3cacc3e3, 0x3caf709b, 0x3c9fb862, 0x3ca36205, 0x3ca12514, 0x3ca00fa0, 0x3c9923f3,
 0x3ca04a8e, 0x3c9b3403, 0x3c9e21f4, 0x3c96b79d, 0x3c91acd1, 0x3c90092d, 0x3c93aa01, 0x3c8d05a2,
 0x3cbe34b9, 0x3cd2f068, 0x3cd207d5, 0x3cd34efd, 0x3cc3d46b, 0x3cc3a67e, 0x3cc0280e, 0x3cbe0d82,
 0x3cbd0cd1, 0x3cbbe122, 0x3cb05f79, 0x3cb7f900, 0x3caebdee, 0x3cb30c9d, 0x3cab986a, 0x3ca64e48,
 0x3ca57499, 0x3ca60c6f, 0x3c9ed6f0, 0x3ca01472, 0x3ca0d710, 0x3c9b7897, 0x3c9d27c4, 0x3c9dcccf,
 0x3c9223b9, 0x3caded79, 0x3ca21717, 0x3c97833f, 0x3c8ff3c9, 0x3c8bfcc3, 0x3c916583, 0x3c9368c8,
 0x3cbcb197, 0x3cdba4d7, 0x3cccb22b, 0x3ccc2614, 0x3cc91695, 0x3cc468b0, 0x3cc0a329, 0x3cbe73d9,
 0x3cbd482a, 0x3cb5a16d, 0x3cb58553, 0x3cb76f38, 0x3cb6413a, 0x3cac0cce, 0x3cae93fd, 0x3ca8a9d8,
 0x3ca51d91, 0x3ca21a72, 0x3c9f5c87, 0x3c9e518f, 0x3c9e405d, 0x3c9c1326, 0x3c9a1a5d, 0x3c9b72b8,
 0x3c9ccbe9, 0x3c95ebc5, 0x3c91a61b, 0x3c92502f, 0x3c98b2ea, 0x3c9011c6, 0x3c8d6eb3, 0x3c8ad942,
 0x3cb574c1, 0x3ccad221, 0x3cc98f2b, 0x3cc3e99a, 0x3cc43e1d, 0x3cba78a2, 0x3cc26d2d, 0x3cbadb68,
 0x3cb540bf, 0x3cb49427, 0x3cafe6ad, 0x3cb2b488, 0x3cac0a4a, 0x3ca886d2, 0x3ca7feed, 0x3cb67430,
 0x3ca3b221, 0x3c9fa97e, 0x3ca11340, 0x3c9deefe, 0x3c987684, 0x3c986b9c, 0x3c9d9fee, 0x3c958a0b,
 0x3c95be43, 0x3c90ff2c, 0x3c8ebf15, 0x3c8e3e87, 0x3c9a18e6, 0x3c8b7e18, 0x3c87d5b7, 0x3c858bc6,
 0x3cb78f18, 0x3cc4a7d0, 0x3cc80c74, 0x3cbfdf13, 0x3cc6876e, 0x3cb816fd, 0x3cbde0a1, 0x3cb51952,
 0x3cb7963a, 0x3caaf725, 0x3caf2694, 0x3cb10419, 0x3cad4af2, 0x3ca86bfa, 0x3ca52d81, 0x3ca40f74,
 0x3ca6882a, 0x3caeb04c, 0x3ca32676, 0x3ca0ec3f, 0x3c8e185c, 0x3c972515, 0x3c94cf65, 0x3c9a554c,
 0x3c9c2d5d, 0x3c8edef6, 0x3c9290fc, 0x3c8cb627, 0x3c8722d4, 0x3c8a5e5e, 0x3c89dbb6, 0x3c8698d6,
 0x3cc9586e, 0x3cc70c99, 0x3cc9f4ab, 0x3cc57845, 0x3cbd30e3, 0x3cba2bab, 0x3cbb0849, 0x3cc5857b,
 0x3cb1e18f, 0x3cadbeb5, 0x3cad6777, 0x3ca8c11f, 0x3cadfe40, 0x3cb86a0a, 0x3ca90979, 0x3ca68b4f,
 0x3ca030c2, 0x3ca43d62, 0x3ca19404, 0x3ca0e304, 0x3ca0d2a9, 0x3c96a452, 0x3c94d940, 0x3c91d54a,
 0x3c8dd3c8, 0x3c9271bd, 0x3c8999a7, 0x3c8afb71, 0x3c8688b0, 0x3c98636e, 0x3c87d912, 0x3c8715d4,
 0x3cb37a81, 0x3cc248e5, 0x3cbd7a7f, 0x3cbc2408, 0x3cb2ed5d, 0x3cb38aa7, 0x3cb797e7, 0x3cb58ac7,
 0x3cb0ce34, 0x3cac530f, 0x3cb071ee, 0x3ca3f141, 0x3ca761a4, 0x3ca0a7e1, 0x3ca5fdc1, 0x3c9f0abd,
 0x3c9aa7b7, 0x3ca17a03, 0x3ca04b65, 0x3c9b188a, 0x3c96fda8, 0x3c92af9b, 0x3c92150b, 0x3c8b295f,
 0x3c89c4db, 0x3c8d6d3b, 0x3c8abf0b, 0x3c886c4b, 0x3c8f2e06, 0x3c84000a, 0x3c835c0b, 0x3c831ceb,
 0x3ca52701, 0x3cc65981, 0x3cc2a928, 0x3cbb8f22, 0x3cb36446, 0x3cbd7a49, 0x3cb04684, 0x3cb3ca9d,
 0x3cb1bd47, 0x3caec398, 0x3ca55483, 0x3ca907cc, 0x3ca96afe, 0x3c9f4ff2, 0x3ca8bdc4, 0x3c9fd7d7,
 0x3ca11ec9, 0x3c95c81e, 0x3c989e27, 0x3c959d56, 0x3c92a305, 0x3c94ff6c, 0x3c91485d, 0x3c924512,
 0x3c8b58fa, 0x3c8e2f38, 0x3c8ed9b8, 0x3c824afe, 0x3c868372, 0x3c80e67a, 0x3c842529, 0x3c8380f4,
 0x3cad3c0e, 0x3cbe81e6, 0x3cb75968, 0x3cb75679, 0x3cb97064, 0x3cb6a005, 0x3cb6e13e, 0x3cac4a76,
 0x3cafd8a0, 0x3ca6e0e0, 0x3ca2f52d, 0x3ca2afc3, 0x3cab4451, 0x3ca28f41, 0x3c9f15a5, 0x3ca1225a,
 0x3cae2867, 0x3ca92776, 0x3c9cf1a8, 0x3c964f98, 0x3c914d2f, 0x3c9123df, 0x3c93d3f2, 0x3c8f4fff,
 0x3c9074c2, 0x3c91a4d9, 0x3c8d6ddc, 0x3c8ff10f, 0x3c89037f, 0x3c8873d7, 0x3c993b3a, 0x3c8a1710,
 0x3ca7bf97, 0x3cbebd0a, 0x3cb92ec0, 0x3cb2173f, 0x3cb35f73, 0x3cae5e17, 0x3caca728, 0x3ca6b4d5,
 0x3cb1c0a2, 0x3ca845cf, 0x3ca088a1, 0x3c9fed71, 0x3ca0a486, 0x3c9ee31a, 0x3ca3864d, 0x3c9b48fc,
 0x3c9b8d5a, 0x3c9df472, 0x3c9bdcd5, 0x3c963d59, 0x3c8d8051, 0x3c91fe65, 0x3c93c2f6, 0x3c8fb4df,
 0x3c8fa5fb, 0x3c873182, 0x3c8392c7, 0x3c845275, 0x3c87541d, 0x3c8248b0, 0x3c87bc22, 0x3c7c957d,
 0x3ca2a5b2, 0x3cbf444e, 0x3cbe61d0, 0x3cb5dfec, 0x3cad5c24, 0x3cb5f0e8, 0x3cac62ca, 0x3ca8bced,
 0x3cad2dcb, 0x3ca045bb, 0x3c9ffa06, 0x3c9cf7f3, 0x3c9e9659, 0x3c9d8369, 0x3c9e2768, 0x3c985887,
 0x3c9b9ac6, 0x3c9322f2, 0x3c901953, 0x3c94d113, 0x3c91e42e, 0x3c89c5e7, 0x3c8b9311, 0x3c8c0fa3,
 0x3c87c881, 0x3c89e3af, 0x3c86214d, 0x3c88d5fd, 0x3c825403, 0x3c8047ef, 0x3c81cdcb, 0x3c7ac95f,
 0x3ca004ee, 0x3cb98a9b, 0x3cb4a193, 0x3cbc96f5, 0x3cad301a, 0x3ca96554, 0x3ca893d3, 0x3cabb590,
 0x3ca90547, 0x3ca36f71, 0x3ca0a3af, 0x3ca3eb62, 0x3c9e8526, 0x3c950e85, 0x3c989f9f, 0x3c96c9a6,
 0x3c950b2a, 0x3c92aa5c, 0x3c93726e, 0x3c971b70, 0x3c995e76, 0x3c8e1ea7, 0x3c959e2d, 0x3c88893b,
 0x3c8c4fcf, 0x3c87e506, 0x3c8504b7, 0x3c878fe2, 0x3c841d66, 0x3c85875e, 0x3c795f67, 0x3c83c7d6,
 0x3ca17962, 0x3cb442c8, 0x3cb62124, 0x3cb4fdd9, 0x3cabd1ab, 0x3ca4d1a6, 0x3ca67763, 0x3ca6c318,
 0x3ca2368c, 0x3ca215d4, 0x3c9f6307, 0x3c9cb75b, 0x3ca8df52, 0x3ca34abe, 0x3c9a86ff, 0x3c95ad10,
 0x3c92490e, 0x3c9a133c, 0x3c900e00, 0x3c912125, 0x3c8fe627, 0x3c8f71f8, 0x3c8a951a, 0x3c90c14e,
 0x3c90ead4, 0x3c834f40, 0x3c86c5b7, 0x3c82a1d1, 0x3c7d665d, 0x3c78982d, 0x3c7bf7c8, 0x3c7e018e,
 0x3c9eae77, 0x3cb50be6, 0x3cb5ab48, 0x3cb5b340, 0x3ca969bc, 0x3caef72f, 0x3caf979d, 0x3ca27bc0,
 0x3ca05bc0, 0x3c9f7a84, 0x3ca15550, 0x3c9cd8e9, 0x3c9e488b, 0x3c94eaa8, 0x3c9a92f4, 0x3c95b905,
 0x3c99369d, 0x3c92391d, 0x3c8ea408, 0x3c8f9a07, 0x3c8d056c, 0x3c878672, 0x3c8be115, 0x3c8e55ce,
 0x3c86f07f, 0x3c82ad59, 0x3c862259, 0x3c847d08, 0x3c8d690a, 0x3c7adb68, 0x3c7d35b6, 0x3c7ab5a9,
 0x3c9473c0, 0x3caf170f, 0x3cade72e, 0x3cb011e1, 0x3cb9eea4, 0x3cacaa83, 0x3caaa26b, 0x3ca619a5,
 0x3ca47885, 0x3c9d79f9, 0x3c9f34e4, 0x3c977d60, 0x3c97aa0b, 0x3c95d986, 0x3c97e05c, 0x3c96b9b6,
 0x3c8e9630, 0x3c940c26, 0x3c8e9b39, 0x3c8e3293, 0x3c8ef1a0, 0x3c87d25c, 0x3c8a93a2, 0x3c80df8e,
 0x3c8a9e1e, 0x3c841cfb, 0x3c84cc83, 0x3c83067a, 0x3c7eed46, 0x3c8333fd, 0x3c7535a2, 0x3c710f37,
 0x3c98affa, 0x3cb75d64, 0x3cb0e073, 0x3caf8777, 0x3cad9092, 0x3ca5e496, 0x3ca8c5f2, 0x3ca54e6e,
 0x3cab8dee, 0x3ca1c1bc, 0x3c9c8755, 0x3ca0d23d, 0x3c9769df, 0x3c9567dc, 0x3c994b96, 0x3c8f8544,
 0x3c908b9e, 0x3c935d0a, 0x3c8d760a, 0x3caab657, 0x3c8a460a, 0x3c8bf71a, 0x3c85ac47, 0x3c83cf98,
 0x3c881832, 0x3c84e9df, 0x3c858eeb, 0x3c836b90, 0x3c753968, 0x3c860f43, 0x3c8092cd, 0x3c747299,
 0x3c9b1382, 0x3cb036ca, 0x3cacad07, 0x3ca92d8b, 0x3cae3e6c, 0x3cae1d14, 0x3c9ff1a2, 0x3c9f6988,
 0x3cb3c66c, 0x3c9a176e, 0x3c991bc5, 0x3c9996aa, 0x3c949441, 0x3c993c47, 0x3c8d9abe, 0x3c925d65,
 0x3c9172b9, 0x3c8c377c, 0x3c916c39, 0x3c8c366f, 0x3c8ac307, 0x3c8941c8, 0x3c8310c1, 0x3c842345,
 0x3c8080fa, 0x3c794d5e, 0x3c7fe451, 0x3c82ef9f, 0x3c7cd390, 0x3c83b023, 0x3c80e0d1, 0x3c7b8257,
 0x3c9363c0, 0x3cae8ca6, 0x3caa48a9, 0x3ca3d01f, 0x3c9d0636, 0x3c9dc4a2, 0x3cab1caf, 0x3c990cac,
 0x3c980ff7, 0x3c97957e, 0x3cb327e0, 0x3c94db8f, 0x3c8f5bbe, 0x3c942e8b, 0x3c90525e, 0x3c8e1933,
 0x3c92ac0a, 0x3c942ec1, 0x3c8ca743, 0x3c87820a, 0x3c85128e, 0x3c89f150, 0x3c83d5ad, 0x3c864163,
 0x3c810e1d, 0x3c7be1f9, 0x3c7a7bc7, 0x3c7fd827, 0x3c804a08, 0x3c7dca9c, 0x3c77b7c8, 0x3c7ffb63,
 0x3c92bd07, 0x3ca789e7, 0x3ca9bbf1, 0x3c9fefbf, 0x3c9e40fe, 0x3ca020d2, 0x3c9e9440, 0x3c9c9cef,
 0x3c9e1a68, 0x3c971f00, 0x3ca252a6, 0x3c964693, 0x3c96de69, 0x3c8fcc91, 0x3c8d279b, 0x3c89b7a4,
 0x3caa3197, 0x3c8df698, 0x3c878b0f, 0x3c81d0f0, 0x3c83e319, 0x3c810152, 0x3c815edb, 0x3c80d837,
 0x3c7ea019, 0x3c8c6859, 0x3c7c83df, 0x3c7558dd, 0x3c7bf254, 0x3c6c8f0a, 0x3c7be04b, 0x3c6ded43,
 0x3c92443b, 0x3ca595f1, 0x3ca3dc48, 0x3ca7cfbd, 0x3c9e7104, 0x3c9c867e, 0x3c9b8f3d, 0x3c93bd4c,
 0x3c923847, 0x3c98fe33, 0x3c93264d, 0x3c991e14, 0x3c89d495, 0x3c8c1b97, 0x3c87c4bb, 0x3c8955ea,
 0x3c8e7f54, 0x3c94face, 0x3c898301, 0x3c844d6d, 0x3c8fef2c, 0x3c802f65, 0x3c811178, 0x3c7e4d78,
 0x3c7190d1, 0x3c754379, 0x3c8106fc, 0x3c799d0f, 0x3c73a9b0, 0x3c6b93cd, 0x3c6ee4ba, 0x3c6a3238,
 0x3c92b50e, 0x3ca8a109, 0x3ca3a414, 0x3c996c18, 0x3ca18988, 0x3c9bc375, 0x3c9b9552, 0x3c98820d,
 0x3c9a7d24, 0x3c92882d, 0x3c95212f, 0x3c90932b, 0x3c9553ba, 0x3c8f16f4, 0x3c890994, 0x3c911963,
 0x3c90c6f8, 0x3c835082, 0x3c840075, 0x3c7fc2c3, 0x3c7e5789, 0x3c83013c, 0x3c771e45, 0x3c821e53,
 0x3c797fb3, 0x3c70039e, 0x3c6a6d5c, 0x3c713272, 0x3c784373, 0x3c71f007, 0x3c67e0ba, 0x3c78d5d5,
 0x3c8fa707, 0x3ca27ff2, 0x3c9b07f9, 0x3c9de7a7, 0x3ca3f141, 0x3c962896, 0x3c96b3d6, 0x3c91ecc7,
 0x3c98c5ca, 0x3c9085f5, 0x3c8d54e8, 0x3c9078f4, 0x3c90173a, 0x3c883dbc, 0x3c8b6f6a, 0x3c8e8b13,
 0x3c8665aa, 0x3c860ed8, 0x3c837967, 0x3c7f51f0, 0x3c8a13eb, 0x3c7bc721, 0x3c7906e7, 0x3c79d61a,
 0x3c7314cb, 0x3c73032d, 0x3c7f8a24, 0x3c6fa24f, 0x3c6f4100, 0x3c66531b, 0x3c6e83d7, 0x3c73dc05,
 0x3c944567, 0x3c9c3555, 0x3c9e378e, 0x3c9659a9, 0x3c9bf198, 0x3c93db7f, 0x3c955242, 0x3c9a14b4,
 0x3c950e85, 0x3c8d760a, 0x3c8f19ae, 0x3c90dcfd, 0x3c906c29, 0x3c93d387, 0x3c874dd2, 0x3c8cb301,
 0x3c862ca0, 0x3c84831c, 0x3c883375, 0x3c848edb, 0x3c7c2580, 0x3c7c6251, 0x3c76fcb7, 0x3c786933,
 0x3c6b93cd, 0x3c833ee4, 0x3c7c059f, 0x3c735562, 0x3c6cd4aa, 0x3c62a074, 0x3c725deb, 0x3c675c9b,
 0x3c8defe2, 0x3c9b12ab, 0x3ca2d00f, 0x3ca454de, 0x3c992535, 0x3c94a000, 0x3c8eb108, 0x3c9029ae,
 0x3c968fc4, 0x3c933d29, 0x3c946222, 0x3c8b62d5, 0x3c8ad9ae, 0x3c8901d2, 0x3c882492, 0x3c93caee,
 0x3c80fba9, 0x3c810010, 0x3c83d394, 0x3c801baf, 0x3c77065c, 0x3c7d18c5, 0x3c722186, 0x3c82359a,
 0x3c8099b9, 0x3c72cf96, 0x3c6a7702, 0x3c75cbca, 0x3c69dec1, 0x3c6f1928, 0x3c5c1fbc, 0x3c5d305d,
 0x3c8a5086, 0x3c9dd4fd, 0x3c9eeea3, 0x3c9cd127, 0x3c970494, 0x3c9a39d3, 0x3c944b7c, 0x3c97d4d3,
 0x3c8b5fb0, 0x3c8e7a4c, 0x3c8bd704, 0x3c8b15de, 0x3c84e110, 0x3c86dfee, 0x3c8328a9, 0x3c871aa6,
 0x3c807aaf, 0x3c79de7e, 0x3c8ef4c5, 0x3c86b73f, 0x3c7a871a, 0x3c7baa9b, 0x3c7a5028, 0x3c8149ac,
 0x3c68d039, 0x3c72e2e1, 0x3c6fc951, 0x3c669dc4, 0x3c5fb217, 0x3c6db4a4, 0x3c595506, 0x3c625b3f,
 0x3c8e19d4, 0x3c9fade6, 0x3c955384, 0x3c9c4ff8, 0x3c9318e1, 0x3c961805, 0x3c8cf978, 0x3c940612,
 0x3c91598f, 0x3c9127a5, 0x3c909361, 0x3c8ef5d2, 0x3c87bbb6, 0x3c84d18b, 0x3c824844, 0x3c89949f,
 0x3c830aac, 0x3c8c7c45, 0x3c7a4c62, 0x3c7a25cb, 0x3c7fc90e, 0x3c793f1c, 0x3c80460b, 0x3c74411a,
 0x3c852172, 0x3c6ecda9, 0x3c77ede3, 0x3c63d592, 0x3c6d6b3d, 0x3c5c11e4, 0x3c69ddea, 0x3c629d84,
 0x3c879520, 0x3c9e646f, 0x3c9de44c, 0x3ca4e238, 0x3c962033, 0x3c970718, 0x3c97689d, 0x3c8ce083,
 0x3c95bab2, 0x3c914644, 0x3c8b9d8d, 0x3c8ce44a, 0x3c8cceb0, 0x3c8a4aa7, 0x3c838c7c, 0x3c83c18b,
 0x3c88e8dd, 0x3c8ced84, 0x3c7bbfff, 0x3c78f839, 0x3c916b98, 0x3c7a6811, 0x3c73eda3, 0x3c736c74,
 0x3c754e61, 0x3c700fc8, 0x3c727bb3, 0x3c6ddb3a, 0x3c617c87, 0x3c8541be, 0x3c5ae163, 0x3c6089ae,
 0x3c8585b1, 0x3c99d2a4, 0x3c93d4ff, 0x3c8d9d42, 0x3c96a70b, 0x3c95f387, 0x3c8e5273, 0x3ca07a29,
 0x3c86173c, 0x3c9434a0, 0x3c84728b, 0x3c83855b, 0x3c8bdcad, 0x3c7edc7f, 0x3c7e68bc, 0x3c82dc53,
 0x3c8384ba, 0x3c78fc6b, 0x3c8849e6, 0x3c6dffb8, 0x3c775968, 0x3c7349a4, 0x3c79dcd0, 0x3c62bae0,
 0x3c668357, 0x3c6513eb, 0x3c6a2aac, 0x3c650324, 0x3c718d76, 0x3c5d0310, 0x3c54e1da, 0x3c5967e6,
 0x3c84c2dd, 0x3c98f748, 0x3c8e7727, 0x3c99f7c3, 0x3c8b9b3f, 0x3c88bd08, 0x3c8c5eb3, 0x3c8b7cd6,
 0x3c88fb1c, 0x3c8e60b6, 0x3c84717f, 0x3c85c1ab, 0x3c82447e, 0x3c8b7473, 0x3c800c60, 0x3c7807e4,
 0x3c7d3621, 0x3c6ebce2, 0x3c78c50e, 0x3c666fa1, 0x3c6736db, 0x3c66e0e0, 0x3c701d34, 0x3c8c38be,
 0x3c5ec8e3, 0x3c5aff2a, 0x3c6cd3d3, 0x3c5b390c, 0x3c5f12b5, 0x3c5885d3, 0x3c5d9fee, 0x3c54f2a1,
 0x3c7fd9d5, 0x3c91697f, 0x3c9538e2, 0x3c87efee, 0x3c8fc5db, 0x3c86fac6, 0x3c885dd2, 0x3c81882b,
 0x3c836e4a, 0x3c86b195, 0x3c86efa9, 0x3c813f9b, 0x3c82a4c0, 0x3c7fa4fc, 0x3c858eeb, 0x3c707040,
 0x3c7387b7, 0x3c6f529e, 0x3c6e6751, 0x3c6839a5, 0x3c6ab515, 0x3c680b82, 0x3c667367, 0x3c6165e1,
 0x3c5c16ed, 0x3c56a4bd, 0x3c67ec78, 0x3c5a4dbf, 0x3c56d4f9, 0x3c522bb1, 0x3c56e123, 0x3c4d3466,
 0x3c82c0a5, 0x3c8bb4d5, 0x3c8b5c8a, 0x3c8b4e7d, 0x3c8da753, 0x3c848b15, 0x3c87a1b5, 0x3c829e76,
 0x3c891e8d, 0x3c8424bd, 0x3c84de8c, 0x3c83844f, 0x3c7faf78, 0x3c71e307, 0x3c793b55, 0x3c6f8777,
 0x3c6d923f, 0x3c8b5137, 0x3c729b28, 0x3c7371e8, 0x3c6daec5, 0x3c6729db, 0x3c68a71e, 0x3c70ccf2,
 0x3c70bd6d, 0x3c6825ef, 0x3c7064ed, 0x3c548b73, 0x3c5bfe99, 0x3c5f1108, 0x3c65cfd3, 0x3c4e732b,
 0x3c86b4f0, 0x3c92f505, 0x3c8eff42, 0x3c922bb1, 0x3c8cb479, 0x3c895f5a, 0x3c98ad40, 0x3c884bff,
 0x3c85d5cd, 0x3c812cf1, 0x3c87093e, 0x3c82ac83, 0x3c7e87c5, 0x3c7e3880, 0x3c760c61, 0x3c7dd9b5,
 0x3c83f2d4, 0x3c694904, 0x3c6a5000, 0x3c663ef9, 0x3c6a8544, 0x3c6da87a, 0x3c56d422, 0x3c6c06b9,
 0x3c73ea48, 0x3c60814a, 0x3c5d33b8, 0x3c56e986, 0x3c6604ac, 0x3c59e2ca, 0x3c536f13, 0x3c5e3bf5,
 0x3c721d54, 0x3c914f48, 0x3c8c7d87, 0x3c89c2f7, 0x3c86f700, 0x3c869ddf, 0x3c8887f9, 0x3c81bff4,
 0x3c8728b3, 0x3c87e71f, 0x3c815a08, 0x3c7c002c, 0x3c751fd2, 0x3c6e57cc, 0x3c77fd68, 0x3c822000,
 0x3c758cdf, 0x3c75e6a2, 0x3c6a3ece, 0x3c681883, 0x3c65c704, 0x3c62f167, 0x3c61353a, 0x3c60f005,
 0x3c68335b, 0x3c7549c4, 0x3c5e0feb, 0x3c5b99ef, 0x3c54bbaf, 0x3c532bf7, 0x3c56fc66, 0x3c5a54e0,
 0x3c7bd19d, 0x3c8651f4, 0x3c86d5a7, 0x3c8bfd9a, 0x3c81dc43, 0x3c80cce4, 0x3c8fd1cf, 0x3c867c51,
 0x3c8540b2, 0x3c7e31ca, 0x3c7e817b, 0x3c734069, 0x3c970429, 0x3c6e6d9c, 0x3c6c82e0, 0x3c76010e,
 0x3c6e6249, 0x3c6f1059, 0x3c6459b1, 0x3c623c35, 0x3c6be313, 0x3c67e267, 0x3c70a07c, 0x3c5c2b0f,
 0x3c560e95, 0x3c660ebd, 0x3c52d1ca, 0x3c58b1dd, 0x3c56f9e2, 0x3c512811, 0x3c4cac16, 0x3c45acb2,
 0x3c6e8de8, 0x3c8aa89b, 0x3c86ef3d, 0x3c85d5cd, 0x3c886021, 0x3c9ed726, 0x3c84915f, 0x3c81ebc8,
 0x3c8c1c6e, 0x3c7cf158, 0x3c83a42f, 0x3c71ed83, 0x3c71e307, 0x3c7c0d98, 0x3c6a15b3, 0x3c756fef,
 0x3c6f8fdb, 0x3c6ab0e3, 0x3c6fddde, 0x3c62888b, 0x3c62e9da, 0x3c6d626f, 0x3c61df18, 0x3c5ada41,
 0x3c53798f, 0x3c55ba47, 0x3c55446b, 0x3c53b3dc, 0x3c5bc15c, 0x3c61b669, 0x3c45dcef, 0x3c5a286b,
 0x3c8343b7, 0x3c896b84, 0x3c863ea9, 0x3c85465b, 0x3c8aaf51, 0x3c813d82, 0x3c77cb7e, 0x3c82388a,
 0x3c738be9, 0x3c7b2e75, 0x3c71a204, 0x3c705060, 0x3c7cdd36, 0x3c6a5f19, 0x3c718365, 0x3c6b412c,
 0x3c65b34e, 0x3c6be597, 0x3c6d3bd8, 0x3c6b0e00, 0x3c60a98e, 0x3c607018, 0x3c5d2c96, 0x3c648917,
 0x3c53bded, 0x3c4d7c8b, 0x3c4a6eb9, 0x3c57914c, 0x3c60cc5e, 0x3c5153b0, 0x3c4d76ab, 0x3c4ce018,
 0x3c82ff59, 0x3c8445e0, 0x3c8944ed, 0x3c826adf, 0x3c856be5, 0x3c804535, 0x3c7b593d, 0x3c7a15db,
 0x3c6f44c7, 0x3c6b04c6, 0x3c7c76df, 0x3c611acd, 0x3c67197f, 0x3c6966cc, 0x3c5d9fee, 0x3c63596c,
 0x3c788766, 0x3c53c6bc, 0x3c5f2742, 0x3c5a81c1, 0x3c72cf96, 0x3c502d3f, 0x3c634eef, 0x3c50c43e,
 0x3c4d6f1f, 0x3c4d9183, 0x3c524689, 0x3c5c6343, 0x3c55a767, 0x3c4d46db, 0x3c5c7765, 0x3c4646a1,
 0x3c855758, 0x3c80a6b9, 0x3c8487ba, 0x3c829bbc, 0x3c8dec52, 0x3c846a93, 0x3c7d1138, 0x3c767324,
 0x3c756217, 0x3c77f85f, 0x3c6aa4ba, 0x3c70eb90, 0x3c7467b1, 0x3c6d78a9, 0x3c7057ec, 0x3c60f868,
 0x3c657fb7, 0x3c5c42f7, 0x3c56b6c6, 0x3c60dcba, 0x3c53ea63, 0x3c5e0788, 0x3c6248ca, 0x3c4f71c3,
 0x3c564b66, 0x3c4cd8f7, 0x3c46470c, 0x3c51d4df, 0x3c4613e1, 0x3c44885b, 0x3c45dbac, 0x3c651dfc,
 0x3c650177, 0x3c753826, 0x3c7cc8a8, 0x3c7f7602, 0x3c7ab904, 0x3c735b41, 0x3c76e829, 0x3c7bd711,
 0x3c7e8b20, 0x3c6abe4f, 0x3c7318fc, 0x3c5ff88e, 0x3c6205af, 0x3c70797a, 0x3c606cbd, 0x3c6a8255,
 0x3c72bb08, 0x3c5c329b, 0x3c60a98e, 0x3c67323e, 0x3c502daa, 0x3c512f9d, 0x3c648cdd, 0x3c5d194b,
 0x3c51e109, 0x3c539904, 0x3c472dbc, 0x3c4bc0c9, 0x3c607952, 0x3c4666ed, 0x3c3e751b, 0x3c413db8,
 0x3c575a5a, 0x3c7b58d1, 0x3c81adeb, 0x3c788ef2, 0x3c79b8be, 0x3c76a0dc, 0x3c6a1cd4, 0x3c787dc0,
 0x3c7f653b, 0x3c6fc230, 0x3c62e9da, 0x3c6a35ff, 0x3c629b00, 0x3c74751d, 0x3c65427a, 0x3c5b6f92,
 0x3c64c0df, 0x3c5c5495, 0x3c70a94b, 0x3c50a966, 0x3c579af2, 0x3c4da67c, 0x3c549d7c, 0x3c481400,
 0x3c45890c, 0x3c4e36c5, 0x3c44f715, 0x3c55ca37, 0x3c4b223d, 0x3c401747, 0x3c409ec1, 0x3c4472f7,
 0x3c5fe17d, 0x3c8ab8f6, 0x3c77e366, 0x3c7dc37a, 0x3c80771e, 0x3c6dfb86, 0x3c6bc68d, 0x3c6cedd5,
 0x3c6a7d4c, 0x3c67b6c8, 0x3c71418c, 0x3c6acf82, 0x3c5b078e, 0x3c551fed, 0x3c5970b5, 0x3c5c1ee5,
 0x3c51a1b3, 0x3c5d2d6d, 0x3c543790, 0x3c55ed72, 0x3c65796c, 0x3c4ed550, 0x3c4853c1, 0x3c45366b,
 0x3c454de8, 0x3c58127b, 0x3c4b64ee, 0x3c41c1d7, 0x3c5bc1c8, 0x3c3e55a6, 0x3c39d5e5, 0x3c434565,
 0x3c66be10, 0x3c849922, 0x3c8101f3, 0x3c7f935e, 0x3c6f1562, 0x3c684209, 0x3c6f6223, 0x3c75b6d1,
 0x3c76a070, 0x3c6f2e8c, 0x3c6894a9, 0x3c5d179e, 0x3c68d1e6, 0x3c4e2669, 0x3c5d65a1, 0x3c5bd9b0,
 0x3c5ae4be, 0x3c5ea7c1, 0x3c5c53be, 0x3c5487ac, 0x3c573f17, 0x3c517cca, 0x3c4d071a, 0x3c49c398,
 0x3c4be2c2, 0x3c4cd8f7, 0x3c466ccc, 0x3c37a65f, 0x3c3ff914, 0x3c4139f2, 0x3c43a2ed, 0x3c3e42c6,
 0x3c5b2fd1, 0x3c78ec10, 0x3c81f213, 0x3c6f4674, 0x3c76485c, 0x3c71857e, 0x3c7877e1, 0x3c65fd1f,
 0x3c6717d2, 0x3c695b0d, 0x3c6be52b, 0x3c625c16, 0x3c55bf4f, 0x3c6134ce, 0x3c57bb3d, 0x3c85d024,
 0x3c5d70f4, 0x3c522b46, 0x3c57a71b, 0x3c54b77d, 0x3c585f3d, 0x3c4b06fa, 0x3c5c7765, 0x3c537dc1,
 0x3c446c41, 0x3c41e3d0, 0x3c3748d7, 0x3c450706, 0x3c3f5d79, 0x3c3d01e9, 0x3c3a577f, 0x3c351e5a,
 0x3c658454, 0x3c6d05bd, 0x3c66f644, 0x3c62e898, 0x3c6b2f23, 0x3c63681a, 0x3c6af7c6, 0x3c5effd5,
 0x3c58e509, 0x3c5e254f, 0x3c5a4d53, 0x3c59301d, 0x3c4fe22b, 0x3c5459f4, 0x3c56e844, 0x3c516fca,
 0x3c48e4e1, 0x3c48c42a, 0x3c49b04d, 0x3c4f1364, 0x3c53528d, 0x3c4e003e, 0x3c3e38b5, 0x3c3c6dda,
 0x3c42b303, 0x3c3af244, 0x3c3ed9c5, 0x3c328b6e, 0x3c489f41, 0x3c3ca750, 0x3c371bf6, 0x3c2c6ef4,
 0x3c5b5c47, 0x3c7569a4, 0x3c83f1fd, 0x3c6378e1, 0x3c623eb9, 0x3c6298e7, 0x3c6646f1, 0x3c674c3f,
 0x3c584323, 0x3c734217, 0x3c710526, 0x3c61c369, 0x3c598d3a, 0x3c6afb8c, 0x3c5eb456, 0x3c5a0961,
 0x3c5a3f7c, 0x3c4f14a6, 0x3c4a7bba, 0x3c6a63b7, 0x3c421ef4, 0x3c4b7407, 0x3c52e8db, 0x3c4c1ebd,
 0x3c401091, 0x3c3a82b3, 0x3c417b60, 0x3c4e29c4, 0x3c41813f, 0x3c42cd04, 0x3c40ad6f, 0x3c3ae32b,
 0x3c528573, 0x3c7a545a, 0x3c72027c, 0x3c647560, 0x3c68504b, 0x3c6deb2b, 0x3c6ab580, 0x3c763795,
 0x3c6871d9, 0x3c57a27e, 0x3c58d9b6, 0x3c5bf55f, 0x3c608008, 0x3c4d9a52, 0x3c4f3e2c, 0x3c5a865e,
 0x3c555f43, 0x3c4c1af6, 0x3c53f4df, 0x3c525bed, 0x3c55fc8c, 0x3c3b3f06, 0x3c48a58b, 0x3c3dff3f,
 0x3c3edb73, 0x3c43fdf1, 0x3c35dedf, 0x3c5c84d1, 0x3c3a4a13, 0x3c44899d, 0x3c3db6af, 0x3c37dba4,
 0x3c5fa79b, 0x3c6499de, 0x3c64b5f8, 0x3c65f527, 0x3c62386f, 0x3c68bc82, 0x3c6dcadf, 0x3c58523c,
 0x3c60440d, 0x3c4e1750, 0x3c4a3832, 0x3c56c432, 0x3c501190, 0x3c5bcf34, 0x3c4f7737, 0x3c674ded,
 0x3c51c55a, 0x3c53cd72, 0x3c509833, 0x3c4df480, 0x3c48981f, 0x3c3ec38a, 0x3c3dd624, 0x3c411ff0,
 0x3c39b599, 0x3c3c932e, 0x3c30cbe5, 0x3c3645a2, 0x3c2ff4ba, 0x3c387379, 0x3c2cfd24, 0x3c2f1023,
 0x3c55ef20, 0x3c6cfb40, 0x3c60eafc, 0x3c694fba, 0x3c60758c, 0x3c5ec9ba, 0x3c625c81, 0x3c5becfb,
 0x3c55b82e, 0x3c545d4f, 0x3c5eba35, 0x3c505441, 0x3c4ba733, 0x3c4ca41e, 0x3c4a29f0, 0x3c649038,
 0x3c5b2413, 0x3c564ec1, 0x3c4a1131, 0x3c4ce87b, 0x3c43efae, 0x3c3fa8f8, 0x3c524bfd, 0x3c3b4e1f,
 0x3c3cda11, 0x3c4ec850, 0x3c4101bd, 0x3c3fd2e9, 0x3c3742f8, 0x3c41fc24, 0x3c362703, 0x3c2bd571,
 0x3c5ee569, 0x3c83126f, 0x3c674158, 0x3c65738d, 0x3c5e5017, 0x3c6452fb, 0x3c648627, 0x3c552927,
 0x3c7578be, 0x3c5bc233, 0x3c68e531, 0x3c4f9061, 0x3c5f3e54, 0x3c4accad, 0x3c59b0e1, 0x3c53a9cb,
 0x3c5131b6, 0x3c4a5cb0, 0x3c48486e, 0x3c506fef, 0x3c433b54, 0x3c42a4c0, 0x3c41362b, 0x3c43a0d4,
 0x3c4ff434, 0x3c36f778, 0x3c438e60, 0x3c3b86bf, 0x3c3c0e39, 0x3c3c9c69, 0x3c3f0132, 0x3c2ee4f0
};

// beta array (64 x 32)
static const unsigned int beta[] =
{
 0x3f800000, 0x3b291538, 0x397ba882, 0x3b1bf9c6, 0x3a08509c, 0x3927c5ac, 0x3b2e5365, 0x3a08509c,
 0xbb1d4952, 0xbb54562e, 0xbacc78ea, 0xb9c73abd, 0x3b6a9e6f, 0xbb0461fa, 0x3a83126f, 0x3b79096c,
 0x3b1aaa3b, 0x3b30f27c, 0x3a9aaa3b, 0x3b2fa2f0, 0xba83126f, 0x39f12c28, 0xba473abd, 0x3a712c28,
 0x3a1d4952, 0x3a22877f, 0x3966afcd, 0x3a956c0d, 0x3a1d4952, 0x3a22877f, 0xb8d1b717, 0xba0d8ec9,
 0x3f80624e, 0x3aee8d11, 0x3acc78ea, 0xbbc49ba6, 0x3b195aaf, 0x3b7a58f7, 0x3ae6afcd, 0xba51b717,
 0x3a6bedfa, 0x3b05b185, 0x39fba882, 0x3b5ed289, 0x3b05b185, 0xb7a7c5ac, 0x3b434c1b, 0xbb9374bc,
 0x3a807358, 0x3b102de0, 0x39c73abd, 0x3b656042, 0xba766a55, 0xba7ba882, 0x3acf1801, 0xbb0c3f3e,
 0x3b922531, 0x3ac73abd, 0x3ab24207, 0xb9d1b717, 0xb8fba882, 0xbb1bf9c6, 0x3b1fe868, 0x3a473abd,
 0x3f7ab368, 0x39c73abd, 0x3b05b185, 0x3ba1dfb9, 0x3b0d8ec9, 0xbb09a027, 0x3b50678c, 0xbb88509c,
 0x00000000, 0x3b03126f, 0xbb2e5365, 0xbb0d8ec9, 0xbb180b24, 0x3b41fc8f, 0x3afba882, 0xbb8c3f3e,
 0x39e6afcd, 0x3a27c5ac, 0xba22877f, 0x3b1e98dd, 0xbb7ba882, 0xbabf5d79, 0xba08509c, 0xbaaa64c3,
 0x3b73cb3e, 0xbb34e11e, 0xbb27c5ac, 0xbac1fc8f, 0xba378034, 0xba980b24, 0xba12ccf7, 0x3b49d9d3,
 0x3f80cb29, 0x3a83126f, 0x3966afcd, 0x3a5c3372, 0x3af9096c, 0xbae410b6, 0x3a27c5ac, 0x00000000,
 0xbb0461fa, 0xbaf3cb3e, 0x3ad6f545, 0xb827c5ac, 0xbb102de0, 0x3af3cb3e, 0xbb8aefb3, 0x3a8aefb3,
 0x3b3cbe62, 0xbaf12c28, 0x3a980b24, 0x39fba882, 0x3aafa2f0, 0x399d4952, 0x3b2a64c3, 0x3b4dc875,
 0xb9b24207, 0xbb156c0d, 0x3b1d4952, 0xba92ccf7, 0xbab24207, 0x3b656042, 0xb992ccf7, 0xba180b24,
 0x3f7d4fdf, 0xbb0d8ec9, 0x3b0aefb3, 0xba66afcd, 0xbb2a64c3, 0xb927c5ac, 0x3aa2877f, 0x39f12c28,
 0xb9d1b717, 0xbb2a64c3, 0x3b102de0, 0x3ae6afcd, 0x3b34e11e, 0xbb3f5d79, 0xbb01c2e3, 0x3b339192,
 0xbac73abd, 0xbac1fc8f, 0xba2d03da, 0xb992ccf7, 0x3b30f27c, 0xb827c5ac, 0x3ac49ba6, 0x3966afcd,
 0xbafba882, 0xbb5306a3, 0xb8fba882, 0x38fba882, 0xba4c78ea, 0xb912ccf7, 0x39e6afcd, 0xbb267621,
 0x3f815810, 0xbac1fc8f, 0x3992ccf7, 0x3b7cf80e, 0x3ab24207, 0x3a9aaa3b, 0xb927c5ac, 0xbb712c28,
 0xbafba882, 0x3ad6f545, 0xbb9aaa3b, 0xba8aefb3, 0x3a3cbe62, 0x3a56f545, 0xbaa52696, 0xbb7ba882,
 0xba8aefb3, 0x3a9fe868, 0x3b05b185, 0xbb6fdc9c, 0x3aee8d11, 0x3ab4e11e, 0x3b7f9724, 0x3a956c0d,
 0x3b267621, 0x3b0c3f3e, 0xbaee8d11, 0xbacf1801, 0x3acf1801, 0xb99d4952, 0xba6bedfa, 0x3b73cb3e,
 0x3f811687, 0x3a4c78ea, 0xbb826aa9, 0xb912ccf7, 0x3ab24207, 0xba902de0, 0x3b6bedfa, 0xb99d4952,
 0xbb59945b, 0xb99d4952, 0x3a85b185, 0x3a66afcd, 0xb912ccf7, 0xbae94ee4, 0xbb16bb99, 0x3a378034,
 0x3aa52696, 0xb9a7c5ac, 0x3acf1801, 0xbb08509c, 0xbabcbe62, 0xbb1aaa3b, 0x3a9aaa3b, 0x39a7c5ac,
 0xba180b24, 0x3ac1fc8f, 0xbb378034, 0x3ad4562e, 0xbaba1f4b, 0xbb1d4952, 0xbb117d6b, 0x3b3f5d79,
 0x3f818fc5, 0x3ae6afcd, 0x3b0d8ec9, 0x3827c5ac, 0xbaf12c28, 0x3b49d9d3, 0xba766a55, 0x3b1bf9c6,
 0xbb09a027, 0xbb870111, 0x3a3cbe62, 0x3b1d4952, 0xbb61719f, 0xba92ccf7, 0x3a83126f, 0xbb449ba6,
 0xbb1fe868, 0xbb66afcd, 0xba85b185, 0x3b180b24, 0xbb05b185, 0x3aee8d11, 0xbb0461fa, 0xbb473abd,
 0xbab78034, 0xbb2a64c3, 0xb99d4952, 0x3b0d8ec9, 0xbb195aaf, 0x39bcbe62, 0xbb88f862, 0xba85b185,
 0x3f810ff9, 0x3a180b24, 0xb992ccf7, 0x3a8aefb3, 0xba9d4952, 0x3b30f27c, 0x3b1aaa3b, 0x3b3a1f4b,
 0xbaafa2f0, 0x3b30f27c, 0xb9fba882, 0xb9bcbe62, 0xbb16bb99, 0xbb01c2e3, 0x3b117d6b, 0x3b55a5b9,
 0xb8fba882, 0xbabf5d79, 0xbb2137f4, 0x3a83126f, 0xbaf66a55, 0x3a66afcd, 0xbab78034, 0xbb291538,
 0xba27c5ac, 0xbb38cfc0, 0x3b8509c0, 0xbabf5d79, 0xbaa2877f, 0xba2d03da, 0xbb8b9778, 0xbb40ad04,
 0x3f7c1bda, 0xbb2a64c3, 0x39c73abd, 0x3b007358, 0x3abcbe62, 0x3b12ccf7, 0x3b03126f, 0xb927c5ac,
 0x3bb4e11e, 0x3b180b24, 0x3a9aaa3b, 0x3b1bf9c6, 0x3ac73abd, 0xbabcbe62, 0x3a980b24, 0x3b0d8ec9,
 0xbb1bf9c6, 0xba66afcd, 0x3b89a027, 0xba766a55, 0x3b1bf9c6, 0x3af12c28, 0xba92ccf7, 0x3a7ba882,
 0x3b41fc8f, 0x3a6bedfa, 0xb827c5ac, 0x3ad9945b, 0xbaf66a55, 0xb87ba882, 0xba7ba882, 0x3ab78034,
 0x3f7dfa44, 0x39f12c28, 0x3b2bb44e, 0xbabcbe62, 0xbaa52696, 0xba22877f, 0xba27c5ac, 0x39dc3372,
 0x3ad4562e, 0xbaaa64c3, 0x3a180b24, 0x3a12ccf7, 0x3a66afcd, 0x39dc3372, 0x387ba882, 0xbaaa64c3,
 0xbabf5d79, 0xb9b24207, 0xbab4e11e, 0x3ad4562e, 0x3b03126f, 0x3b007358, 0x3a88509c, 0xba3cbe62,
 0x3a324207, 0xbb0ede55, 0xba8aefb3, 0x3912ccf7, 0xba378034, 0xba0d8ec9, 0x3b54562e, 0x39d1b717,
 0x3f807c85, 0x3a956c0d, 0xbadc3372, 0xbb751aca, 0x3b2a64c3, 0x3b23d70a, 0x3b117d6b, 0x3b070111,
 0xbb77b9e0, 0xbb4f1801, 0xbac9d9d3, 0xbb62c12b, 0xba12ccf7, 0x3a51b717, 0x3af66a55, 0xba9aaa3b,
 0xbb8b9778, 0x3a4c78ea, 0x3aee8d11, 0x3a980b24, 0x3bc154ca, 0x37a7c5ac, 0x39b24207, 0x3a4c78ea,
 0x3afe4799, 0xbb0ede55, 0x3a324207, 0x3a03126f, 0x3af3cb3e, 0x3b12ccf7, 0xbb0461fa, 0xbbdc3372,
 0x3f7ef9db, 0xb927c5ac, 0x3aafa2f0, 0xbb1d4952, 0x3aa2877f, 0x3b30f27c, 0x3b0ede55, 0x3b3630a9,
 0xb9d1b717, 0xb9fba882, 0x3ae410b6, 0xbabcbe62, 0x3a3cbe62, 0xba92ccf7, 0xba8aefb3, 0xb951b717,
 0xb93cbe62, 0x3b0461fa, 0x3b0d8ec9, 0x3a56f545, 0xbafba882, 0x3ae410b6, 0xb8a7c5ac, 0xbab4e11e,
 0xb9fba882, 0xb8fba882, 0x3b2e5365, 0x3b27c5ac, 0x3a03126f, 0x3b5306a3, 0xbae94ee4, 0x3aba1f4b,
 0x3f7f06f7, 0xbacf1801, 0x39b24207, 0x39a7c5ac, 0x3a956c0d, 0xba324207, 0xb9d1b717, 0xbb08509c,
 0x3a7ba882, 0x387ba882, 0x3aded289, 0xba9d4952, 0xba7ba882, 0xbb4f1801, 0x3b117d6b, 0x393cbe62,
 0xba807358, 0xbb9bf9c6, 0x387ba882, 0xbb324207, 0xbb449ba6, 0xbaafa2f0, 0x3b0c3f3e, 0xb9dc3372,
 0x3988509c, 0xba0d8ec9, 0xba41fc8f, 0xb951b717, 0xb912ccf7, 0xbb449ba6, 0x3b9aaa3b, 0x3aebedfa,
 0x3f7d07c8, 0xb9b24207, 0x3abf5d79, 0xb8a7c5ac, 0xbac49ba6, 0xb992ccf7, 0x3a712c28, 0x3b0aefb3,
 0x3966afcd, 0xba92ccf7, 0xb827c5ac, 0xba83126f, 0x397ba882, 0xbb03126f, 0x3aba1f4b, 0x3aded289,
 0x37a7c5ac, 0x3b0c3f3e, 0xbaa7c5ac, 0xbbc9d9d3, 0xb9fba882, 0xba92ccf7, 0xb99d4952, 0x3b51b717,
 0xba4c78ea, 0x39fba882, 0xbb30f27c, 0x3a83126f, 0x3a766a55, 0x3a92ccf7, 0xba807358, 0x3988509c,
 0x3f7c49ba, 0xbaa2877f, 0x39f12c28, 0xbb473abd, 0xbaf66a55, 0xbb656042, 0xb97ba882, 0x00000000,
 0xbb79096c, 0xba3cbe62, 0x3ab24207, 0x3b08509c, 0xba03126f, 0x3acc78ea, 0x3a766a55, 0xbb83ba34,
 0x3a41fc8f, 0xbae410b6, 0xbad6f545, 0xba12ccf7, 0x3a1d4952, 0x3b870111, 0xb966afcd, 0x3a956c0d,
 0xba324207, 0x3b070111, 0xba6bedfa, 0x3b291538, 0xb966afcd, 0x38d1b717, 0xbaa7c5ac, 0xbb34e11e,
 0x3f8185f0, 0xba712c28, 0x3b252696, 0x00000000, 0xba2d03da, 0x3a83126f, 0x3ad6f545, 0xba5c3372,
 0xb99d4952, 0x3b378034, 0x3b83ba34, 0xbb9aaa3b, 0xba378034, 0x3aded289, 0x3a378034, 0xb988509c,
 0xbaba1f4b, 0x3a956c0d, 0xbb378034, 0x3a712c28, 0x3a9aaa3b, 0xba980b24, 0x3a807358, 0xbaf12c28,
 0x3a12ccf7, 0x3a473abd, 0xbb252696, 0x3ac73abd, 0xb87ba882, 0xbb8aefb3, 0x00000000, 0x3a7ba882,
 0x3f7ce704, 0xbb5ae3e7, 0x3af12c28, 0xba980b24, 0xb951b717, 0xba9fe868, 0xba324207, 0xbb41fc8f,
 0x3b807358, 0xba8d8ec9, 0x3b0ede55, 0x3b41fc8f, 0xb827c5ac, 0xba92ccf7, 0xba7ba882, 0xbb8e368f,
 0x3b05b185, 0xba3cbe62, 0x3a0d8ec9, 0x3b49d9d3, 0x39fba882, 0xb827c5ac, 0x3a5c3372, 0x3a22877f,
 0x3a7ba882, 0x3b0461fa, 0xba61719f, 0xbb378034, 0xba956c0d, 0x3b102de0, 0x3aad03da, 0xbb141c82,
 0x3f807fcc, 0x3a83126f, 0xbab78034, 0x3a902de0, 0x39c73abd, 0xbb811b1e, 0x3951b717, 0xbb50678c,
 0x3b30f27c, 0x3a03126f, 0xbb34e11e, 0xba56f545, 0xbb2d03da, 0xbb980b24, 0xba2d03da, 0x3b3b6ed6,
 0x3b4c78ea, 0x39b24207, 0x3b902de0, 0x3a0d8ec9, 0xba92ccf7, 0xbaad03da, 0x3ad1b717, 0xbb291538,
 0x3ac73abd, 0x3b102de0, 0x3ab78034, 0x3912ccf7, 0xb912ccf7, 0xb912ccf7, 0xb9c73abd, 0xba12ccf7,
 0x3f8130be, 0xbb102de0, 0x3992ccf7, 0x38fba882, 0xb992ccf7, 0xbaebedfa, 0x38fba882, 0x3a88509c,
 0x3a4c78ea, 0xba5c3372, 0x3b252696, 0x37a7c5ac, 0xbbaefb2b, 0xbad1b717, 0xba473abd, 0xba6bedfa,
 0x3a61719f, 0x3abf5d79, 0xbb007358, 0xbb870111, 0x3a180b24, 0xbbbc169c, 0xbaaa64c3, 0x399d4952,
 0xba9d4952, 0xb9c73abd, 0x39fba882, 0xbb03126f, 0xbb3a1f4b, 0x3afe4799, 0x39e6afcd, 0x3ac9d9d3,
 0x3f7e353f, 0xbb55a5b9, 0xba3cbe62, 0x3b2bb44e, 0xbb180b24, 0xbb2a64c3, 0xb966afcd, 0xbb7e4799,
 0xb7a7c5ac, 0x3b007358, 0x399d4952, 0x3b2a64c3, 0xbae94ee4, 0xba61719f, 0xba56f545, 0xb951b717,
 0xba66afcd, 0x3aaa64c3, 0xb8d1b717, 0x3b88f862, 0x3ab78034, 0xba180b24, 0xbb4c78ea, 0x3af66a55,
 0x39d1b717, 0x3a180b24, 0xbabcbe62, 0xbad6f545, 0xbaf9096c, 0xb93cbe62, 0x39e6afcd, 0xba85b185,
 0x3f8068dc, 0x38fba882, 0x39dc3372, 0x3aafa2f0, 0xbb08509c, 0xbb40ad04, 0xbb81c2e3, 0xba807358,
 0x3a56f545, 0x3b01c2e3, 0x3a807358, 0x3b117d6b, 0xbb30f27c, 0x393cbe62, 0x3a9d4952, 0x3afe4799,
 0xbb4c78ea, 0x39f12c28, 0x3b5ae3e7, 0xbb070111, 0x3a61719f, 0x3b2137f4, 0x393cbe62, 0x3a51b717,
 0x3b8b9778, 0x3a88509c, 0xba92ccf7, 0x3a6bedfa, 0xb8a7c5ac, 0xbb3b6ed6, 0x3b22877f, 0xbaebedfa,
 0x3f7e0ded, 0xb9dc3372, 0x3a61719f, 0xbb7a58f7, 0x3a61719f, 0xbaebedfa, 0xbb902de0, 0xbbaefb2b,
 0x3992ccf7, 0xba56f545, 0x3a51b717, 0x3a9aaa3b, 0x3b141c82, 0x39c73abd, 0x3ae1719f, 0xbb79096c,
 0x3a712c28, 0xbaf3cb3e, 0xbb0d8ec9, 0x3ac9d9d3, 0x3a27c5ac, 0xbb324207, 0x3966afcd, 0xbb5844d0,
 0xbabf5d79, 0x3b070111, 0xbb156c0d, 0xbb49d9d3, 0xb827c5ac, 0xbb870111, 0x3992ccf7, 0x3a8aefb3,
 0x3f7fe5c9, 0x387ba882, 0x3b2d03da, 0x37a7c5ac, 0xbb488a48, 0xbae94ee4, 0x39d1b717, 0x39e6afcd,
 0xbb03126f, 0xbb5306a3, 0xbb2bb44e, 0xbb4dc875, 0xbb55a5b9, 0xbb73cb3e, 0x3b195aaf, 0xbb49d9d3,
 0xba51b717, 0x3a1d4952, 0xbab78034, 0xb9c73abd, 0xbacf1801, 0x3ad4562e, 0xb9dc3372, 0xbaf9096c,
 0x3b1bf9c6, 0xba473abd, 0x3a980b24, 0x3a3cbe62, 0xbb1e98dd, 0xba5c3372, 0x3b180b24, 0x3b1e98dd,
 0x3f806595, 0xba8d8ec9, 0xbab4e11e, 0xb9e6afcd, 0xb992ccf7, 0x3b81c2e3, 0xba6bedfa, 0xbaf9096c,
 0xba4c78ea, 0x3b2bb44e, 0xbb324207, 0xbad4562e, 0xb9f12c28, 0x3ae6afcd, 0x39a7c5ac, 0x3a378034,
 0xbb807358, 0x3b22877f, 0xba2d03da, 0xbaf12c28, 0x3b05b185, 0x3ab24207, 0xb9dc3372, 0x3ad9945b,
 0xbbaefb2b, 0x3b7cf80e, 0x3adc3372, 0xbadc3372, 0x3966afcd, 0x38fba882, 0x37a7c5ac, 0xbb180b24,
 0x3f7fb15b, 0xba56f545, 0xba83126f, 0x3b16bb99, 0xba83126f, 0x3aebedfa, 0xbb2a64c3, 0x3a8d8ec9,
 0xb9b24207, 0xbaf12c28, 0xba3cbe62, 0xbb85b185, 0xbb16bb99, 0xba66afcd, 0x3a27c5ac, 0x3b2a64c3,
 0xb9c73abd, 0x3a378034, 0x3b08509c, 0xbb83ba34, 0x3ad4562e, 0x3b324207, 0x3b267621, 0x393cbe62,
 0xbb751aca, 0x3a51b717, 0x3a88509c, 0xbb102de0, 0x3a88509c, 0x3966afcd, 0xba902de0, 0xba51b717,
 0x3f813a93, 0x39fba882, 0x3b62c12b, 0x3a8aefb3, 0xbb1e98dd, 0xb9b24207, 0xbae1719f, 0xbb8b9778,
 0x3b3630a9, 0x3abcbe62, 0xb912ccf7, 0xbb434c1b, 0x3ae1719f, 0xb8a7c5ac, 0xb9f12c28, 0x3a766a55,
 0x39bcbe62, 0x397ba882, 0xbabf5d79, 0x3992ccf7, 0xba27c5ac, 0x3aa7c5ac, 0x399d4952, 0xba41fc8f,
 0xba1d4952, 0x3a8d8ec9, 0x3adc3372, 0x3a1d4952, 0xbb4c78ea, 0x3b102de0, 0x3aad03da, 0x37a7c5ac,
 0x3f800347, 0x39bcbe62, 0xba83126f, 0xbabf5d79, 0x3a473abd, 0xba9aaa3b, 0xbaee8d11, 0xbacc78ea,
 0x3af12c28, 0x3ab24207, 0xba956c0d, 0x3a378034, 0xbaaa64c3, 0x3a5c3372, 0x3b1bf9c6, 0xbb9a0275,
 0xba92ccf7, 0xba83126f, 0xb9f12c28, 0x39d1b717, 0x3ae94ee4, 0xbb51b717, 0x3b0aefb3, 0x3a473abd,
 0x3a8aefb3, 0xb9bcbe62, 0xba378034, 0xbb070111, 0x3a902de0, 0xbaf3cb3e, 0x3b22877f, 0xba7ba882,
 0x3f7f41f2, 0xbb727bb3, 0x3b05b185, 0xb9a7c5ac, 0x3927c5ac, 0x39a7c5ac, 0xbad6f545, 0x3988509c,
 0x3ab4e11e, 0x3ad1b717, 0x3b6410b6, 0xba08509c, 0xb966afcd, 0xbad1b717, 0xbb0461fa, 0xbb5ed289,
 0x3b8e368f, 0x3b1d4952, 0x3acf1801, 0x3aaa64c3, 0xb9f12c28, 0xbae6afcd, 0xba324207, 0x3b252696,
 0xba66afcd, 0x3b94c448, 0xbacf1801, 0x3b195aaf, 0xbae1719f, 0x3a41fc8f, 0xbb1e98dd, 0xbba91538,
 0x3f7cbfb1, 0xba08509c, 0xbadc3372, 0x3a22877f, 0x38a7c5ac, 0xbae1719f, 0x3b378034, 0x3ab4e11e,
 0x397ba882, 0xba85b185, 0x3ab78034, 0x3ac9d9d3, 0x3992ccf7, 0x3b89a027, 0xbb0ede55, 0x3b5d82fd,
 0xba980b24, 0x3abf5d79, 0x3ae6afcd, 0x3ae94ee4, 0x3abcbe62, 0x3b2137f4, 0x3b62c12b, 0x397ba882,
 0xbb0ede55, 0x3a2d03da, 0x3b195aaf, 0x3a92ccf7, 0xbb8ce704, 0xb9e6afcd, 0x3b378034, 0x3a180b24,
 0x3f7bedfa, 0xb912ccf7, 0xbb09a027, 0x3a1d4952, 0xbb54562e, 0xbb070111, 0xb9e6afcd, 0xbaa52696,
 0x3acc78ea, 0x3b0461fa, 0xbb180b24, 0x3827c5ac, 0xbabf5d79, 0xba56f545, 0x3a712c28, 0xba4c78ea,
 0x39f12c28, 0x3b656042, 0x3a8aefb3, 0xbaf3cb3e, 0xbac1fc8f, 0x3b5c3372, 0x3b6d3d86, 0x3966afcd,
 0xbb291538, 0x3a9aaa3b, 0x3b1aaa3b, 0xbb117d6b, 0x397ba882, 0x3b01c2e3, 0xbb8ede55, 0xbaa52696,
 0x3f77e282, 0x3b007358, 0x3afe4799, 0xba0d8ec9, 0xb992ccf7, 0x3aebedfa, 0x3a41fc8f, 0xb9dc3372,
 0xba2d03da, 0xbb070111, 0x3988509c, 0xb93cbe62, 0xb7a7c5ac, 0xbb1e98dd, 0x3b49d9d3, 0x3a61719f,
 0xbacc78ea, 0xbacc78ea, 0xbb1d4952, 0x3b4b295f, 0x3a56f545, 0x3b1fe868, 0x3b62c12b, 0xbaf66a55,
 0x3aba1f4b, 0xbb007358, 0x39c73abd, 0x39e6afcd, 0xbb0461fa, 0xbb0461fa, 0xbb3b6ed6, 0x3ab24207,
 0x3f7e0ded, 0xbb3f5d79, 0x3b339192, 0xb992ccf7, 0x3a12ccf7, 0x3b59945b, 0x3a88509c, 0xbb08509c,
 0xbb2137f4, 0xbb378034, 0x3aba1f4b, 0xb8d1b717, 0x3ae410b6, 0x3b4c78ea, 0x3b1aaa3b, 0xba980b24,
 0xbb2e5365, 0xb988509c, 0xb9dc3372, 0x3ae6afcd, 0x3a9fe868, 0xbb2a64c3, 0x3b3630a9, 0x3827c5ac,
 0x3b267621, 0x3a12ccf7, 0xbafe4799, 0xbab78034, 0x3a4c78ea, 0xbb66afcd, 0x3a22877f, 0x3afba882,
 0x3f7c7e28, 0xbac73abd, 0x3aebedfa, 0xb966afcd, 0xba378034, 0xbb007358, 0xba08509c, 0xba0d8ec9,
 0x3988509c, 0xbb141c82, 0xbb070111, 0x3afba882, 0x3b03126f, 0x3b712c28, 0x3abf5d79, 0xb9f12c28,
 0x3b5306a3, 0xbb7cf80e, 0x3ad4562e, 0x3b9b5200, 0x3a8aefb3, 0x3ac1fc8f, 0x3a4c78ea, 0xb93cbe62,
 0x3b0d8ec9, 0x3a08509c, 0xbb180b24, 0xbb1bf9c6, 0x3a41fc8f, 0xb992ccf7, 0xba0d8ec9, 0xb9f12c28,
 0x3f80b439, 0xbb22877f, 0x39fba882, 0x3aaa64c3, 0xb9d1b717, 0x3a56f545, 0x3988509c, 0xbb3cbe62,
 0x3a9d4952, 0x3ac9d9d3, 0xba7ba882, 0x3acf1801, 0x3a03126f, 0x39d1b717, 0x39dc3372, 0x3992ccf7,
 0x3a956c0d, 0xbb08509c, 0xbb01c2e3, 0xba378034, 0x3a980b24, 0xba1d4952, 0xbaf3cb3e, 0xbb01c2e3,
 0xba0d8ec9, 0xbb0ede55, 0xbb1fe868, 0xba766a55, 0xbaf9096c, 0x3a980b24, 0x39c73abd, 0x3966afcd,
 0x3f806c22, 0x3a9d4952, 0x3abcbe62, 0x39d1b717, 0x3a9aaa3b, 0xbaf66a55, 0xbae410b6, 0x3aaa64c3,
 0xba4c78ea, 0xbac73abd, 0xb8fba882, 0xb9f12c28, 0xba56f545, 0x3a08509c, 0xbb5ae3e7, 0x3ad1b717,
 0xb912ccf7, 0xbb007358, 0xbaf9096c, 0xba473abd, 0xbb34e11e, 0xba5c3372, 0x3ad6f545, 0xb97ba882,
 0xba41fc8f, 0xbabcbe62, 0xba51b717, 0xbb291538, 0xb8fba882, 0xb827c5ac, 0x3a473abd, 0xba22877f,
 0x3f7e69ad, 0xb992ccf7, 0x3b51b717, 0xb87ba882, 0xbaaa64c3, 0xbb180b24, 0x3ae94ee4, 0x3b49d9d3,
 0xb9d1b717, 0x3b55a5b9, 0xba956c0d, 0xbb8ede55, 0xbb2137f4, 0xbb03126f, 0xbac73abd, 0x3a5c3372,
 0x3b0c3f3e, 0xbb01c2e3, 0xbb488a48, 0xbb23d70a, 0xb8a7c5ac, 0xbb0461fa, 0xbadc3372, 0xbb1e98dd,
 0x3b4b295f, 0x3abf5d79, 0xba378034, 0x38a7c5ac, 0x3a8aefb3, 0xb9b24207, 0x3a83126f, 0x3b09a027,
 0x3f803e42, 0x3af9096c, 0x3b0aefb3, 0x39b24207, 0x3b3b6ed6, 0x39f12c28, 0x3b694ee4, 0x3a9aaa3b,
 0x3a66afcd, 0x3ba67621, 0xba7ba882, 0x3b473abd, 0xbaf66a55, 0x3adc3372, 0x3aad03da, 0x3b23d70a,
 0xba83126f, 0xb827c5ac, 0x3b45eb31, 0x3aba1f4b, 0xbb38cfc0, 0xbb86594b, 0xb9c73abd, 0xbb117d6b,
 0x3a378034, 0x3b12ccf7, 0xbaebedfa, 0x3aa52696, 0x3ac9d9d3, 0x3b267621, 0x3a766a55, 0x3ae6afcd,
 0x3f7f5c29, 0xba5c3372, 0xbac9d9d3, 0x39fba882, 0x3ac73abd, 0x3aad03da, 0xbabf5d79, 0x3b61719f,
 0x3827c5ac, 0xba180b24, 0xbb01c2e3, 0x3b3b6ed6, 0x39dc3372, 0xbb5c3372, 0xbaf9096c, 0x3ad9945b,
 0x3a807358, 0x3a807358, 0xb87ba882, 0x3af66a55, 0xba8d8ec9, 0xbab4e11e, 0x38d1b717, 0x37a7c5ac,
 0xbb5ed289, 0x3ad6f545, 0xbb656042, 0xbacf1801, 0x3b156c0d, 0x3af12c28, 0x3a56f545, 0x3adc3372,
 0x3f7f2e49, 0x3a0d8ec9, 0xba12ccf7, 0xbaded289, 0x3b0d8ec9, 0x3a712c28, 0x39c73abd, 0xb8fba882,
 0xbaa7c5ac, 0x393cbe62, 0xbb97635e, 0x3b79096c, 0x3a22877f, 0xbb0ede55, 0xbb66afcd, 0x399d4952,
 0xbb102de0, 0xbb267621, 0x3992ccf7, 0x3a85b185, 0x3a180b24, 0x37a7c5ac, 0xbb1fe868, 0xb827c5ac,
 0xbb83ba34, 0xbaee8d11, 0x39a7c5ac, 0xba61719f, 0x397ba882, 0xb8a7c5ac, 0x3a0d8ec9, 0x3b195aaf,
 0x3f7ed917, 0x3a4c78ea, 0x3aebedfa, 0x3aa2877f, 0xbae1719f, 0xb992ccf7, 0xbaad03da, 0x3a88509c,
 0xbae1719f, 0xba66afcd, 0x3a66afcd, 0xbb1e98dd, 0xbb54562e, 0x3a9fe868, 0x3a12ccf7, 0xbb3b6ed6,
 0x3b180b24, 0x3a378034, 0xbb0461fa, 0x3b070111, 0xba324207, 0xbac1fc8f, 0xba6bedfa, 0x3b195aaf,
 0xb7a7c5ac, 0x3a88509c, 0xbb73cb3e, 0x3ac49ba6, 0x3b38cfc0, 0x3afe4799, 0xba980b24, 0x3b5d82fd,
 0x3f7c985f, 0xb8a7c5ac, 0x3b291538, 0x39e6afcd, 0x3b291538, 0x3b2a64c3, 0xbb0aefb3, 0xba27c5ac,
 0x3b694ee4, 0x3b30f27c, 0x3a12ccf7, 0xba4c78ea, 0x38d1b717, 0x3b27c5ac, 0x00000000, 0xba9aaa3b,
 0xbb01c2e3, 0xbaee8d11, 0x3b102de0, 0x3ab24207, 0x3b8509c0, 0xba56f545, 0x3a83126f, 0x39dc3372,
 0x3b1d4952, 0x3a473abd, 0xbac9d9d3, 0xbb4dc875, 0x3b5306a3, 0x3a27c5ac, 0xbaf3cb3e, 0xba0d8ec9,
 0x3f7b9f56, 0x3a766a55, 0x3b03126f, 0x39bcbe62, 0x39dc3372, 0xba902de0, 0x38a7c5ac, 0x3b8e368f,
 0xbb141c82, 0xb93cbe62, 0xba6bedfa, 0x39d1b717, 0xb8a7c5ac, 0x3a56f545, 0x3a956c0d, 0xb7a7c5ac,
 0x3b267621, 0xbba32f45, 0x3b473abd, 0x3b62c12b, 0xb9f12c28, 0x3b73cb3e, 0x3b40ad04, 0x3afba882,
 0x3af3cb3e, 0xb827c5ac, 0x3b291538, 0xba807358, 0x3ae94ee4, 0xba324207, 0xbb05b185, 0xba3cbe62,
 0x3f7e2196, 0x3a66afcd, 0x38fba882, 0xba902de0, 0x3af66a55, 0x3b1d4952, 0xb9f12c28, 0xb9d1b717,
 0xbb03126f, 0x3a8aefb3, 0x399d4952, 0xbb1bf9c6, 0xbb38cfc0, 0x3aee8d11, 0xba66afcd, 0x3b6fdc9c,
 0xb99d4952, 0xba956c0d, 0x3ae6afcd, 0x393cbe62, 0xb8fba882, 0x3aa7c5ac, 0xb8fba882, 0x3aba1f4b,
 0x3b27c5ac, 0xba03126f, 0xb87ba882, 0x3ab4e11e, 0x3adc3372, 0xba8aefb3, 0x3b1aaa3b, 0x3b0461fa,
 0x3f8089a0, 0x3b007358, 0x3afe4799, 0x3b3a1f4b, 0x39dc3372, 0x3b070111, 0xb966afcd, 0x38a7c5ac,
 0x3b007358, 0xbaa2877f, 0x3a03126f, 0x3ae1719f, 0x3b09a027, 0xbac9d9d3, 0x3a1d4952, 0xba83126f,
 0xbab24207, 0x3a12ccf7, 0xba9fe868, 0xb992ccf7, 0x3abf5d79, 0x3ac49ba6, 0x3a902de0, 0x3b8f861a,
 0x3a9aaa3b, 0xbad6f545, 0xbaa7c5ac, 0xbad6f545, 0xbbab0c89, 0xb8d1b717, 0x3af3cb3e, 0x39d1b717,
 0x3f7df3b6, 0x38a7c5ac, 0x3b05b185, 0x3a9aaa3b, 0x3b2bb44e, 0xbb1aaa3b, 0xbb007358, 0x3a2d03da,
 0xbab78034, 0x3a378034, 0xbad1b717, 0xb8a7c5ac, 0x3a4c78ea, 0xbb27c5ac, 0xbae1719f, 0xbb3e0ded,
 0xbabcbe62, 0xbac73abd, 0x3ad9945b, 0x39e6afcd, 0xbabcbe62, 0xbad1b717, 0x3b55a5b9, 0xb9b24207,
 0x38fba882, 0x39bcbe62, 0x3acf1801, 0x3b807358, 0x3b88509c, 0xba0d8ec9, 0xbaaa64c3, 0xbb6bedfa,
 0x3f821965, 0x3b22877f, 0x3a41fc8f, 0x3a56f545, 0x3951b717, 0x39fba882, 0xba9aaa3b, 0xb99d4952,
 0xbb59945b, 0xbb1e98dd, 0x3ad6f545, 0xbb473abd, 0x3ae94ee4, 0x37a7c5ac, 0x397ba882, 0x3b83126f,
 0x3aad03da, 0x3b0d8ec9, 0x3ad1b717, 0xb9c73abd, 0x3adc3372, 0xb9bcbe62, 0xb992ccf7, 0xbaf66a55,
 0x3ad6f545, 0x3ac49ba6, 0x3b90d5a6, 0xba7ba882, 0x3a4c78ea, 0x3b291538, 0xb99d4952, 0x3b79096c,
 0x3f80c155, 0x3b4dc875, 0x39a7c5ac, 0xba41fc8f, 0x3a61719f, 0xba41fc8f, 0xb988509c, 0xbaaa64c3,
 0x3b727bb3, 0x3b656042, 0xb8d1b717, 0xb9b24207, 0xb927c5ac, 0x3a956c0d, 0x3988509c, 0x37a7c5ac,
 0x3ac9d9d3, 0x3a4c78ea, 0xb988509c, 0xb9d1b717, 0xbb324207, 0xbb180b24, 0x39fba882, 0xbb5c3372,
 0xba3cbe62, 0xba56f545, 0x3bab0c89, 0x3a902de0, 0x3b40ad04, 0x3a8aefb3, 0xba08509c, 0xbb4dc875,
 0x3f8072b0, 0xb9dc3372, 0x3a1d4952, 0xb9f12c28, 0xb9a7c5ac, 0x3b27c5ac, 0x3a9fe868, 0x3a1d4952,
 0xb966afcd, 0x39d1b717, 0xbb16bb99, 0xbadc3372, 0x38fba882, 0xbb50678c, 0xbb5306a3, 0xbb712c28,
 0xbb9aaa3b, 0xbab78034, 0x38d1b717, 0x39d1b717, 0xb8fba882, 0xb9c73abd, 0xbb180b24, 0xbb09a027,
 0xbb7ba882, 0xb9d1b717, 0x3afba882, 0x39fba882, 0x3a712c28, 0xb87ba882, 0xbaaa64c3, 0xba766a55,
 0x3f816873, 0xbb324207, 0x3aa2877f, 0xba2d03da, 0xb988509c, 0xbb102de0, 0x3af3cb3e, 0xbb602214,
 0xbac9d9d3, 0x3a473abd, 0xba27c5ac, 0xbad4562e, 0x3a83126f, 0x3992ccf7, 0xbb61719f, 0xbb86594b,
 0xbb5844d0, 0xbb89a027, 0x3951b717, 0xbb007358, 0x3abf5d79, 0x3b56f545, 0x3ad1b717, 0xbb45eb31,
 0x3a712c28, 0xba03126f, 0xba180b24, 0xba61719f, 0xba88509c, 0xb9b24207, 0xbb03126f, 0x3a980b24,
 0x3f7d07c8, 0x3a766a55, 0xba980b24, 0x3b3f5d79, 0x3b378034, 0xbad1b717, 0xbb488a48, 0xba27c5ac,
 0xb93cbe62, 0x393cbe62, 0x3a8aefb3, 0xb93cbe62, 0xbaad03da, 0x3a378034, 0x3992ccf7, 0x3a8d8ec9,
 0xbb0ede55, 0x39b24207, 0x3b03126f, 0x39fba882, 0xbaded289, 0x387ba882, 0xba88509c, 0xbaebedfa,
 0xbae6afcd, 0xbb49d9d3, 0xbb3e0ded, 0xba712c28, 0x3ac73abd, 0x3a7ba882, 0xba03126f, 0x3a41fc8f,
 0x3f7e5604, 0x3a56f545, 0x3a712c28, 0xb992ccf7, 0xba9d4952, 0x39a7c5ac, 0x3ae94ee4, 0x38d1b717,
 0xb912ccf7, 0xba324207, 0xbaded289, 0xbb77b9e0, 0x3b102de0, 0xb966afcd, 0x3ac1fc8f, 0xba7ba882,
 0x3ad6f545, 0x3a61719f, 0xbb0aefb3, 0x38fba882, 0x3a5c3372, 0x3aaa64c3, 0x3a902de0, 0xbaf9096c,
 0xbab24207, 0xb8d1b717, 0xbba52696, 0xbb51b717, 0x3a902de0, 0x3a180b24, 0x39dc3372, 0x3a902de0,
 0x3f81205c, 0x39f12c28, 0xbaaa64c3, 0x3ad4562e, 0x3adc3372, 0xba473abd, 0xba1d4952, 0xba956c0d,
 0xbabf5d79, 0xb7a7c5ac, 0xbb5306a3, 0xbaa2877f, 0x3a03126f, 0x38d1b717, 0xbb2a64c3, 0x3b16bb99,
 0xbb488a48, 0x3951b717, 0xba5c3372, 0x3aded289, 0xbb08509c, 0xbb3b6ed6, 0x3b070111, 0x3ab4e11e,
 0x3a956c0d, 0xba712c28, 0x39d1b717, 0xbb38cfc0, 0xb912ccf7, 0xba5c3372, 0xbb0c3f3e, 0xba83126f,
 0x3f80d845, 0xbad4562e, 0xba5c3372, 0x3b01c2e3, 0xbb67ff58, 0x3aafa2f0, 0x3afe4799, 0x3b23d70a,
 0xb912ccf7, 0xbacc78ea, 0xbb1d4952, 0xba378034, 0x3a766a55, 0x3a378034, 0xb97ba882, 0xbb40ad04,
 0x3b1bf9c6, 0xbad9945b, 0x3a85b185, 0xbb03126f, 0x3abf5d79, 0xba51b717, 0x3a807358, 0xbb3e0ded,
 0x3a9d4952, 0xba4c78ea, 0x3abf5d79, 0xba712c28, 0x3b8b9778, 0x3aad03da, 0xbb34e11e, 0xb9bcbe62,
 0x3f7e2196, 0x3b56f545, 0x3abcbe62, 0x3a83126f, 0xbb7f9724, 0x3b2a64c3, 0xba92ccf7, 0xb951b717,
 0xbaf66a55, 0xb9d1b717, 0x3ad6f545, 0xba3cbe62, 0x3abcbe62, 0x3b5c3372, 0xbb656042, 0xbbafa2f0,
 0xba9aaa3b, 0xb9fba882, 0xbb0461fa, 0x3b03126f, 0xba9aaa3b, 0x3a473abd, 0x3a9aaa3b, 0xba66afcd,
 0x3b3a1f4b, 0x3b449ba6, 0x3b195aaf, 0x3a473abd, 0x3b339192, 0xb9dc3372, 0xbb38cfc0, 0xbb488a48,
 0x3f800d1b, 0xbb3cbe62, 0x3af3cb3e, 0xba12ccf7, 0x3a8d8ec9, 0x3a0d8ec9, 0x3aee8d11, 0x3a9fe868,
 0x3a324207, 0xbaba1f4b, 0xba85b185, 0xbb2a64c3, 0xba6bedfa, 0x3927c5ac, 0xb8fba882, 0x3b41fc8f,
 0xbae6afcd, 0xbb656042, 0xbb434c1b, 0xb9fba882, 0xbb1bf9c6, 0x39e6afcd, 0x3b54562e, 0x00000000,
 0xb8d1b717, 0xba7ba882, 0x3aaa64c3, 0x3b180b24, 0x37a7c5ac, 0x00000000, 0xbaa7c5ac, 0xbb55a5b9,
 0x3f7c6a7f, 0xb9d1b717, 0xba6bedfa, 0xba3cbe62, 0xbb3f5d79, 0xbb16bb99, 0xbafba882, 0x3af9096c,
 0x3a5c3372, 0x3a1d4952, 0xbae1719f, 0xb8a7c5ac, 0xba88509c, 0x3b980b24, 0x3b1fe868, 0x39b24207,
 0xbb6a9e6f, 0x3b08509c, 0x3b180b24, 0x3b05b185, 0x3ae94ee4, 0xbb0d8ec9, 0xbb8a47ed, 0xba807358,
 0xbaaa64c3, 0xbad6f545, 0x3acf1801, 0xbaaa64c3, 0xbb54562e, 0x3a0d8ec9, 0xb99d4952, 0xba7ba882,
 0x3f828588, 0xbafe4799, 0xba378034, 0xbb01c2e3, 0x3a8d8ec9, 0xbb488a48, 0xbb0461fa, 0xba08509c,
 0x3a5c3372, 0x3b324207, 0xbb2d03da, 0xba4c78ea, 0x3b2137f4, 0xbb77b9e0, 0xbaad03da, 0x3aebedfa,
 0xbaf66a55, 0xb9a7c5ac, 0xbb8e368f, 0xbb117d6b, 0xbb0d8ec9, 0xba473abd, 0x3a85b185, 0xbb8ce704,
 0xbb54562e, 0xbb09a027, 0xba0d8ec9, 0x3ae410b6, 0x3af9096c, 0xbafe4799, 0xbaaa64c3, 0xb827c5ac,
 0x3f802de0, 0xb9fba882, 0xbb602214, 0x39e6afcd, 0x3a12ccf7, 0x3a7ba882, 0xb827c5ac, 0x3b38cfc0,
 0x3a22877f, 0x3abf5d79, 0xbae94ee4, 0xbb61719f, 0xba66afcd, 0x39dc3372, 0xbb51b717, 0x3a1d4952,
 0x3a180b24, 0x3a12ccf7, 0xba8aefb3, 0x3a807358, 0x3b03126f, 0xbafe4799, 0xb988509c, 0xbaa7c5ac,
 0xb9dc3372, 0xba9d4952, 0xba180b24, 0x3ba7c5ac, 0xbab78034, 0x3a980b24, 0xb992ccf7, 0x3a08509c,
 0x3f7bac71, 0x3b5844d0, 0xb8d1b717, 0xba902de0, 0xbb807358, 0xbab4e11e, 0xbae6afcd, 0xbaf66a55,
 0x3a7ba882, 0x3ac9d9d3, 0x3a3cbe62, 0xb9fba882, 0x3b4dc875, 0x3a324207, 0xbb0d8ec9, 0x3b55a5b9,
 0xbb102de0, 0xba88509c, 0x3b956c0d, 0x37a7c5ac, 0x3aafa2f0, 0xb9a7c5ac, 0xbae410b6, 0x38a7c5ac,
 0x3b980b24, 0x3abf5d79, 0xba03126f, 0xba1d4952, 0x3b751aca, 0xbb180b24, 0x3b2fa2f0, 0x3966afcd,
 0x3f7db22d, 0xb8d1b717, 0x39c73abd, 0x3b8b9778, 0x3b7ba882, 0x3a0d8ec9, 0x3abcbe62, 0xba807358,
 0xbb12ccf7, 0xbb41fc8f, 0x38d1b717, 0x3b40ad04, 0x3827c5ac, 0x3ae94ee4, 0xbb694ee4, 0x3b49d9d3,
 0xbb117d6b, 0xba61719f, 0xbb3b6ed6, 0xbae6afcd, 0x38d1b717, 0x3951b717, 0x3b180b24, 0xbb0c3f3e,
 0x3abcbe62, 0x3abcbe62, 0x3b870111, 0xb8fba882, 0x3912ccf7, 0xbb16bb99, 0xbaf12c28, 0xba956c0d,
 0x3f81652c, 0xbb1bf9c6, 0xba3cbe62, 0x3b5306a3, 0x3b7f9724, 0x3b141c82, 0x3b3630a9, 0x38d1b717,
 0x3af12c28, 0xb8a7c5ac, 0x3a6bedfa, 0x3b59945b, 0xbaa2877f, 0x3aa7c5ac, 0xbb434c1b, 0x3b156c0d,
 0xb97ba882, 0xba4c78ea, 0x3a41fc8f, 0x3b40ad04, 0xbb2bb44e, 0x3b141c82, 0x3af12c28, 0xb9b24207,
 0xbb488a48, 0xbb85b185, 0xba980b24, 0x39fba882, 0xbac49ba6, 0xb9f12c28, 0x3adc3372, 0x3a766a55,
 0x3f7e3bcd, 0xbb34e11e, 0x3a08509c, 0x3a2d03da, 0x3aa7c5ac, 0xb8d1b717, 0xbae410b6, 0x399d4952,
 0x3b8aefb3, 0xbaa7c5ac, 0xbafe4799, 0xba980b24, 0x3b45eb31, 0x3a766a55, 0xba88509c, 0x3abcbe62,
 0xba22877f, 0xba27c5ac, 0x3951b717, 0xbb2a64c3, 0xba92ccf7, 0xba3cbe62, 0xbaa7c5ac, 0x3b81c2e3,
 0xbad9945b, 0x3992ccf7, 0xb9d1b717, 0xbb0461fa, 0x3a08509c, 0xbb7cf80e, 0xbb102de0, 0xb912ccf7,
 0x3f7f5c29, 0xb8fba882, 0x3af66a55, 0xbac49ba6, 0xbb3f5d79, 0x3a88509c, 0xb912ccf7, 0x3b3b6ed6,
 0x38a7c5ac, 0xbabf5d79, 0x3af12c28, 0x3be8a71e, 0x39bcbe62, 0xbb267621, 0x3a9fe868, 0x3a8d8ec9,
 0x3a180b24, 0xbb2e5365, 0x3b378034, 0x3b195aaf, 0x3a66afcd, 0x3b49d9d3, 0x3a712c28, 0xba902de0,
 0x3a51b717, 0x38fba882, 0x3acf1801, 0x3b16bb99, 0xbb83126f, 0xb9c73abd, 0x3b180b24, 0xbab4e11e
};
