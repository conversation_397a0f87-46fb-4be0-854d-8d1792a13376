<?xml version="1.0"?>
<package>
  <name>serial</name>
  <version>1.2.1</version>
  <description>
    Serial is a cross-platform, simple to use library for using serial ports on computers.
    This library provides a C++, object oriented interface for interacting with RS-232
    like devices on Linux and Windows.
  </description>

  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>MIT</license>

  <url type="website">http://wjwwood.github.com/serial/</url>
  <url type="repository">https://github.com/wjwwood/serial</url>
  <url type="bugtracker">https://github.com/wjwwood/serial/issues</url>

  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>catkin</buildtool_depend>

</package>
