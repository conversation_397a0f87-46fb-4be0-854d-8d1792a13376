#pragma once

// Font Awesome 6 Free Solid Icons
#define ICON_MIN_FA 0xe000
#define ICON_MAX_FA 0xf8ff

// 常用图标定义
#define ICON_FA_CAMERA "\xef\x80\xb3"
#define ICON_FA_CROSSHAIRS "\xef\x81\x9b"
#define ICON_FA_MOUSE "\xef\x89\x85"
#define ICON_FA_BRAIN "\xef\x92\x96"
#define ICON_FA_KEYBOARD "\xef\x84\x9c"
#define ICON_FA_SLIDERS_H "\xef\x87\x9e"
#define ICON_FA_CHART_LINE "\xef\x88\x81"
#define ICON_FA_BUG "\xef\x86\x88"
#define ICON_FA_HOME "\xef\x80\x95"
#define ICON_FA_USER "\xef\x80\x87"
#define ICON_FA_GEAR "\xef\x80\x93"
#define ICON_FA_BOLT "\xef\x83\xa7"
#define ICON_FA_CHART_BAR "\xef\x82\x80"
#define ICON_FA_DISPLAY "\xef\x84\x88"
#define ICON_FA_BULLSEYE "\xef\x85\x80"
#define ICON_FA_ROBOT "\xef\xa4\x84"
#define ICON_FA_GAMEPAD "\xef\x84\x9b"
#define ICON_FA_PALETTE "\xef\x94\xbf"
#define ICON_FA_INFO_CIRCLE "\xef\x81\x9a"
#define ICON_FA_MICROCHIP "\xef\x8b\x9b"
#define ICON_FA_GEAR "\xef\x80\x93"
#define ICON_FA_BOLT "\xef\x83\xa7"
#define ICON_FA_CROWN "\xef\x94\xa1"
#define ICON_FA_FIRE "\xef\x81\xad"
#define ICON_FA_STAR "\xef\x80\x85"



