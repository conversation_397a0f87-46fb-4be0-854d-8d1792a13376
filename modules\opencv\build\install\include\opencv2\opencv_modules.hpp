/*
 *      ** File generated automatically, do not modify **
 *
 * This file defines the list of modules available in current build configuration
 *
 *
*/

// This definition means that OpenCV is built with enabled non-free code.
// For example, patented algorithms for non-profit/non-commercial use only.
/* #undef OPENCV_ENABLE_NONFREE */

#define HAVE_OPENCV_ARUCO
#define HAVE_OPENCV_BGSEGM
#define HAVE_OPENCV_BIOINSPIRED
#define HAVE_OPENCV_CALIB3D
#define HAVE_OPENCV_CCALIB
#define HAVE_OPENCV_CORE
#define HAVE_OPENCV_CUDAARITHM
#define HAVE_OPENCV_CUDABGSEGM
#define HAVE_OPENCV_CUDACODEC
#define HAVE_OPENCV_CUDAFEATURES2D
#define HAVE_OPENCV_CUDAFILTERS
#define HAVE_OPENCV_CUDAIMGPROC
#define HAVE_OPENCV_CUDALEGACY
#define HAVE_OPENCV_CUDAOBJDETECT
#define HAVE_OPENCV_CUDAOPTFLOW
#define HAVE_OPENCV_CUDASTEREO
#define HAVE_OPENCV_CUDAWARPING
#define HAVE_OPENCV_CUDEV
#define HAVE_OPENCV_DATASETS
#define HAVE_OPENCV_DNN
#define HAVE_OPENCV_DNN_OBJDETECT
#define HAVE_OPENCV_DNN_SUPERRES
#define HAVE_OPENCV_DPM
#define HAVE_OPENCV_FACE
#define HAVE_OPENCV_FEATURES2D
#define HAVE_OPENCV_FLANN
#define HAVE_OPENCV_FUZZY
#define HAVE_OPENCV_GAPI
#define HAVE_OPENCV_HFS
#define HAVE_OPENCV_HIGHGUI
#define HAVE_OPENCV_IMG_HASH
#define HAVE_OPENCV_IMGCODECS
#define HAVE_OPENCV_IMGPROC
#define HAVE_OPENCV_INTENSITY_TRANSFORM
#define HAVE_OPENCV_LINE_DESCRIPTOR
#define HAVE_OPENCV_MCC
#define HAVE_OPENCV_ML
#define HAVE_OPENCV_OBJDETECT
#define HAVE_OPENCV_OPTFLOW
#define HAVE_OPENCV_PHASE_UNWRAPPING
#define HAVE_OPENCV_PHOTO
#define HAVE_OPENCV_PLOT
#define HAVE_OPENCV_QUALITY
#define HAVE_OPENCV_RAPID
#define HAVE_OPENCV_REG
#define HAVE_OPENCV_RGBD
#define HAVE_OPENCV_SALIENCY
#define HAVE_OPENCV_SHAPE
#define HAVE_OPENCV_SIGNAL
#define HAVE_OPENCV_STEREO
#define HAVE_OPENCV_STITCHING
#define HAVE_OPENCV_STRUCTURED_LIGHT
#define HAVE_OPENCV_SUPERRES
#define HAVE_OPENCV_SURFACE_MATCHING
#define HAVE_OPENCV_TEXT
#define HAVE_OPENCV_TRACKING
#define HAVE_OPENCV_VIDEO
#define HAVE_OPENCV_VIDEOIO
#define HAVE_OPENCV_VIDEOSTAB
#define HAVE_OPENCV_WECHAT_QRCODE
#define HAVE_OPENCV_WORLD
#define HAVE_OPENCV_XFEATURES2D
#define HAVE_OPENCV_XIMGPROC
#define HAVE_OPENCV_XOBJDETECT
#define HAVE_OPENCV_XPHOTO


