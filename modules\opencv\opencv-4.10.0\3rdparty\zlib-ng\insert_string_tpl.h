#ifndef INSERT_STRING_H_
#define INSERT_STRING_H_

/* insert_string.h -- Private insert_string functions shared with more than
 *                    one insert string implementation
 *
 * Copyright (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>
 *
 * Copyright (C) 2013 Intel Corporation. All rights reserved.
 * Authors: <AUTHORS>
 *  <PERSON>    <<EMAIL>>
 *  <PERSON><PERSON><PERSON>    <<EMAIL>>
 *  <PERSON><PERSON><PERSON>   <<EMAIL>>
 *  <PERSON>     <<EMAIL>>
 *
 * Portions are Copyright (C) 2016 12Sided Technology, LLC.
 * Author:
 *  <PERSON>     <<EMAIL>>
 *
 * For conditions of distribution and use, see copyright notice in zlib.h
 *
 */

#ifndef HASH_CALC_OFFSET
#  define HASH_CALC_OFFSET 0
#endif
#ifndef HASH_CALC_MASK
#  define HASH_CALC_MASK HASH_MASK
#endif
#ifndef HASH_CALC_READ
#  if BYTE_ORDER == LITTLE_ENDIAN
#    define HASH_CALC_READ \
        memcpy(&val, strstart, sizeof(val));
#  else
#    define HASH_CALC_READ \
        val  = ((uint32_t)(strstart[0])); \
        val |= ((uint32_t)(strstart[1]) << 8); \
        val |= ((uint32_t)(strstart[2]) << 16); \
        val |= ((uint32_t)(strstart[3]) << 24);
#  endif
#endif

/* ===========================================================================
 * Update a hash value with the given input byte
 * IN  assertion: all calls to UPDATE_HASH are made with consecutive
 *    input characters, so that a running hash key can be computed from the
 *    previous key instead of complete recalculation each time.
 */
Z_INTERNAL uint32_t UPDATE_HASH(deflate_state *const s, uint32_t h, uint32_t val) {
    (void)s;
    HASH_CALC(s, h, val);
    return h & HASH_CALC_MASK;
}

/* ===========================================================================
 * Quick insert string str in the dictionary and set match_head to the previous head
 * of the hash chain (the most recent string with same hash key). Return
 * the previous length of the hash chain.
 */
Z_INTERNAL Pos QUICK_INSERT_STRING(deflate_state *const s, uint32_t str) {
    Pos head;
    uint8_t *strstart = s->window + str + HASH_CALC_OFFSET;
    uint32_t val, hm;

    HASH_CALC_VAR_INIT;
    HASH_CALC_READ;
    HASH_CALC(s, HASH_CALC_VAR, val);
    HASH_CALC_VAR &= HASH_CALC_MASK;
    hm = HASH_CALC_VAR;

    head = s->head[hm];
    if (LIKELY(head != str)) {
        s->prev[str & s->w_mask] = head;
        s->head[hm] = (Pos)str;
    }
    return head;
}

/* ===========================================================================
 * Insert string str in the dictionary and set match_head to the previous head
 * of the hash chain (the most recent string with same hash key). Return
 * the previous length of the hash chain.
 * IN  assertion: all calls to INSERT_STRING are made with consecutive
 *    input characters and the first STD_MIN_MATCH bytes of str are valid
 *    (except for the last STD_MIN_MATCH-1 bytes of the input file).
 */
Z_INTERNAL void INSERT_STRING(deflate_state *const s, uint32_t str, uint32_t count) {
    uint8_t *strstart = s->window + str + HASH_CALC_OFFSET;
    uint8_t *strend = strstart + count;

    for (Pos idx = (Pos)str; strstart < strend; idx++, strstart++) {
        uint32_t val, hm;

        HASH_CALC_VAR_INIT;
        HASH_CALC_READ;
        HASH_CALC(s, HASH_CALC_VAR, val);
        HASH_CALC_VAR &= HASH_CALC_MASK;
        hm = HASH_CALC_VAR;

        Pos head = s->head[hm];
        if (LIKELY(head != idx)) {
            s->prev[idx & s->w_mask] = head;
            s->head[hm] = idx;
        }
    }
}
#endif
