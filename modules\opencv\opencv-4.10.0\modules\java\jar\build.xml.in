<!-- This is an Apache Ant build file. As part of the desktop java build -->
<!-- process, this config is used to package the autogenerated .java -->
<!-- interface files into OpenCV.jar -->
<project name="OpenCV">

    <tstamp>
      <format property="timestamp" pattern="EEE MMM d yyyy HH:mm:ss z"/>
    </tstamp>

  <target name="jar">
    <!-- This is to make a jar with a source attachment, for e.g. easy -->
    <!-- navigation in Eclipse. See this question: -->
    <!-- http://stackoverflow.com/questions/3584968/ant-how-to-compile-jar-that-includes-source-attachment -->
    <javac sourcepath="" srcdir="java" destdir="build/classes" debug="on" includeantruntime="false" @OPENCV_ANT_JAVAC_EXTRA_ATTRS@ >
      <include name="**/*.java"/>
      <compilerarg line="-encoding utf-8"/>
    </javac>
    <jar destfile="@OPENCV_JAR_FILE@">
      <fileset dir="java"/>
      <fileset dir="build/classes"/>
      <manifest>
        <attribute name="Specification-Title" value="OpenCV"/>
        <attribute name="Specification-Version" value="@OPENCV_VERSION@"/>
        <attribute name="Implementation-Title" value="OpenCV"/>
        <attribute name="Implementation-Version" value="@OPENCV_VCSVERSION@"/>
        <attribute name="Implementation-Date" value="${timestamp}"/>
      </manifest>
    </jar>
  </target>

  <target name="javadoc">
    <copy file="@OpenCV_SOURCE_DIR@/doc/mymath.js"
      todir="@OPENCV_JAVADOC_DESTINATION@" />

    <!-- synchronize with platforms\android\build_sdk.py -->
    <javadoc
      packagenames="org.opencv.*"
      sourcepath="java"
      destdir="@OPENCV_JAVADOC_DESTINATION@"
      Windowtitle="OpenCV @OPENCV_VERSION_PLAIN@ Java documentation"
      Doctitle="OpenCV Java documentation (@OPENCV_VERSION@)"
      bottom="Generated on ${timestamp} / OpenCV @OPENCV_VCSVERSION@"
      failonerror="true"
      encoding="UTF-8" charset="UTF-8" docencoding="UTF-8"
      @CMAKE_CONFIG_OPENCV_JAVADOC_LINK@
      additionalparam="--allow-script-in-comments"
      >
      <bottom>
         <![CDATA[
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.setAttribute("src", url);
              script.setAttribute("defer", "")
              document.head.appendChild(script);
              script = document.createElement('script');
              script.setAttribute("src", '@OPENCV_MATHJAX_RELPATH@/es5/tex-chtml.js');
              script.setAttribute("defer", "")
              document.head.appendChild(script);
            </script>
         ]]>
      </bottom>
    </javadoc>
  </target>

</project>
