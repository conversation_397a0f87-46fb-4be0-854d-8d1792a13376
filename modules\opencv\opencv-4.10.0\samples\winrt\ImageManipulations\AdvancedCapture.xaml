﻿<!--
//*********************************************************
//
// Copyright (c) Microsoft. All rights reserved.
// THIS CODE IS PROVIDED *AS IS* WITHOUT WARRANTY OF
// ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING ANY
// IMPLIED WARRANTIES OF FITNESS FOR A PARTICULAR
// PURPOSE, MERCHANTABILITY, OR NON-INFRINGEMENT.
//
//*********************************************************
-->

<common:LayoutAwarePage
    x:Class="SDKSample.MediaCapture.AdvancedCapture"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:$rootsnamespace$"
    xmlns:common="using:SDKSample.Common"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid x:Name="LayoutRoot" Background="White" HorizontalAlignment="Left" VerticalAlignment="Top">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Grid x:Name="Input" Grid.Row="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <TextBlock TextWrapping="Wrap" Grid.Row="0"  Text="This scenario shows how to enumerate cameras in the system. Choose a camera from the list to preview, record or take a photo from the chosen camera.  You can add the gray scale effect using the checkbox provided." Style="{StaticResource BasicTextStyle}" HorizontalAlignment="Left"/>
            <StackPanel Orientation="Horizontal" Grid.Row="1" Margin="0,10,0,0">
                <ListBox x:Name="EnumedDeviceList2" SelectionChanged="lstEnumedDevices_SelectionChanged" />
                <Button x:Name="btnStartDevice2" Click="btnStartDevice_Click" IsEnabled="true"  Margin="0,0,10,0" Content="StartDevice"/>
                <Button x:Name="btnStartPreview2" Click="btnStartPreview_Click" IsEnabled="true"  Margin="0,0,10,0" Content="StartPreview"/>
                <ComboBox x:Name="EffectTypeCombo" Width="120" SelectedIndex="0">
                    <ComboBoxItem Content="Preview"/>
                    <ComboBoxItem Content="Grayscale"/>
                    <ComboBoxItem Content="Canny"/>
                    <ComboBoxItem Content="Sobel"/>
                    <ComboBoxItem Content="Histogram"/>
                </ComboBox>
                <Button Content="Apply" HorizontalAlignment="Stretch" VerticalAlignment="Top" Click="Button_Click"/>
            </StackPanel>
            <StackPanel x:Name="EffectTypeCombo1" Orientation="Horizontal" Grid.Row="1" Margin="324,5,-324,7"/>
        </Grid>

        <Grid x:Name="Output" HorizontalAlignment="Left" VerticalAlignment="Top" Grid.Row="1">
            <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                <StackPanel>
                    <TextBlock Style="{StaticResource BasicTextStyle}"  HorizontalAlignment='Center'  VerticalAlignment='Center'  TextAlignment='Center'	Text='Preview' />
                    <Canvas x:Name="previewCanvas2" Background="Gray">
                        <CaptureElement x:Name="previewElement2" />
                    </Canvas>
                </StackPanel>
                <StackPanel/>
                <StackPanel/>
            </StackPanel>
        </Grid>

        <!-- Add Storyboards to the visual states below as necessary for supporting the various layouts -->
        <VisualStateManager.VisualStateGroups>
            <VisualStateGroup>
                <VisualState x:Name="FullScreenLandscape"/>
                <VisualState x:Name="Filled"/>
                <VisualState x:Name="FullScreenPortrait"/>
                <VisualState x:Name="Snapped"/>
            </VisualStateGroup>
        </VisualStateManager.VisualStateGroups>
    </Grid>

</common:LayoutAwarePage>
