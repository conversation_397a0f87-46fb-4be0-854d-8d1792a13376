<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: GLFWallocator Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">GLFWallocator Struct Reference<div class="ingroups"><a class="el" href="group__init.html">Initialization, version and error reference</a></div></div></div>
</div><!--header-->
<div class="contents">

<p>Custom heap memory allocator.  
 <a href="struct_g_l_f_wallocator.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a18a798136f17a9cb105be18312193bf7" id="r_a18a798136f17a9cb105be18312193bf7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__init.html#ga4306a564e9f60f4de8cc8f31731a3120">GLFWallocatefun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wallocator.html#a18a798136f17a9cb105be18312193bf7">allocate</a></td></tr>
<tr class="separator:a18a798136f17a9cb105be18312193bf7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af5a674af9e170095b968f467233437be" id="r_af5a674af9e170095b968f467233437be"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__init.html#ga3e88a829615d8efe8bec1746f7309c63">GLFWreallocatefun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wallocator.html#af5a674af9e170095b968f467233437be">reallocate</a></td></tr>
<tr class="separator:af5a674af9e170095b968f467233437be"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab74cf9a969e73e6eb65a6112a591a988" id="r_ab74cf9a969e73e6eb65a6112a591a988"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__init.html#ga7181615eda94c4b07bd72bdcee39fa28">GLFWdeallocatefun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wallocator.html#ab74cf9a969e73e6eb65a6112a591a988">deallocate</a></td></tr>
<tr class="separator:ab74cf9a969e73e6eb65a6112a591a988"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6153be74dbaf7f0a7e8bd3bfc039910" id="r_af6153be74dbaf7f0a7e8bd3bfc039910"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wallocator.html#af6153be74dbaf7f0a7e8bd3bfc039910">user</a></td></tr>
<tr class="separator:af6153be74dbaf7f0a7e8bd3bfc039910"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>This describes a custom heap memory allocator for GLFW. To set an allocator, pass it to <a class="el" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a> before initializing the library.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="intro_guide.html#init_allocator">Custom heap memory allocator</a> </dd>
<dd>
<a class="el" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.4. </dd></dl>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="a18a798136f17a9cb105be18312193bf7" name="a18a798136f17a9cb105be18312193bf7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a18a798136f17a9cb105be18312193bf7">&#9670;&#160;</a></span>allocate</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__init.html#ga4306a564e9f60f4de8cc8f31731a3120">GLFWallocatefun</a> GLFWallocator::allocate</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The memory allocation function. See <a class="el" href="group__init.html#ga4306a564e9f60f4de8cc8f31731a3120">GLFWallocatefun</a> for details about allocation function. </p>

</div>
</div>
<a id="af5a674af9e170095b968f467233437be" name="af5a674af9e170095b968f467233437be"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af5a674af9e170095b968f467233437be">&#9670;&#160;</a></span>reallocate</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__init.html#ga3e88a829615d8efe8bec1746f7309c63">GLFWreallocatefun</a> GLFWallocator::reallocate</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The memory reallocation function. See <a class="el" href="group__init.html#ga3e88a829615d8efe8bec1746f7309c63">GLFWreallocatefun</a> for details about reallocation function. </p>

</div>
</div>
<a id="ab74cf9a969e73e6eb65a6112a591a988" name="ab74cf9a969e73e6eb65a6112a591a988"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab74cf9a969e73e6eb65a6112a591a988">&#9670;&#160;</a></span>deallocate</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__init.html#ga7181615eda94c4b07bd72bdcee39fa28">GLFWdeallocatefun</a> GLFWallocator::deallocate</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The memory deallocation function. See <a class="el" href="group__init.html#ga7181615eda94c4b07bd72bdcee39fa28">GLFWdeallocatefun</a> for details about deallocation function. </p>

</div>
</div>
<a id="af6153be74dbaf7f0a7e8bd3bfc039910" name="af6153be74dbaf7f0a7e8bd3bfc039910"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af6153be74dbaf7f0a7e8bd3bfc039910">&#9670;&#160;</a></span>user</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* GLFWallocator::user</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The user pointer for this custom allocator. This value will be passed to the allocator functions. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="glfw3_8h_source.html">glfw3.h</a></li>
</ul>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
