{"module_imports": ["java.lang.String"], "module_j_code": "src/java/core+Core.jcode.in", "class_ignore_list": ["FileNode", "FileStorage", "KDTree", "KeyPoint", "DMatch"], "missing_consts": {"Core": {"private": [["CV_8U", 0], ["CV_8S", 1], ["CV_16U", 2], ["CV_16S", 3], ["CV_32S", 4], ["CV_32F", 5], ["CV_64F", 6], ["CV_USRTYPE1", 7]], "public": [["SVD_MODIFY_A", 1], ["SVD_NO_UV", 2], ["SVD_FULL_UV", 4], ["FILLED", -1], ["REDUCE_SUM", 0], ["REDUCE_AVG", 1], ["REDUCE_MAX", 2], ["REDUCE_MIN", 3]]}}, "ManualFuncs": {"Core": {"minMaxLoc": {"j_code": ["// manual port", "public static class MinMaxLocResult {", "    public double minVal;", "    public double maxVal;", "    public Point minLoc;", "    public Point maxLoc;", "\n", "    public MinMaxLocResult() {", "        minVal=0; maxVal=0;", "        minLoc=new Point();", "        maxLoc=new Point();", "    }", "}", "\n", "// C++: minMaxLoc(Mat src, double* minVal, double* maxVal=0, Point* minLoc=0, Point* maxLoc=0, InputArray mask=noArray())", "\n", "//javadoc: minMaxLoc(src, mask)", "public static MinMaxLocResult minMaxLoc(Mat src, <PERSON> mask) {", "    MinMaxLocResult res = new MinMaxLocResult();", "    long maskNativeObj=0;", "    if (mask != null) {", "        maskNativeObj=mask.nativeObj;", "    }", "    double resarr[] = n_minMaxLocManual(src.nativeObj, maskNativeObj);", "    res.minVal=resarr[0];", "    res.maxVal=resarr[1];", "    res.minLoc.x=resarr[2];", "    res.minLoc.y=resarr[3];", "    res.maxLoc.x=resarr[4];", "    res.maxLoc.y=resarr[5];", "    return res;", "}", "\n", "//javadoc: minMaxLoc(src)", "public static MinMaxLocResult minMaxLoc(Mat src) {", "    return minMaxLoc(src, null);", "}"], "jn_code": ["private static native double[] n_minMaxLocManual(long src_nativeObj, long mask_nativeObj);\n"], "cpp_code": ["// C++: minMaxLoc(Mat src, double* minVal, double* maxVal=0, Point* minLoc=0, Point* maxLoc=0, InputArray mask=noArray())", "JNIEXPORT jdoubleArray JNICALL Java_org_opencv_core_Core_n_1minMaxLocManual (JNIEnv*, jclass, jlong, jlong);", "\n", "JNIEXPORT jdoubleArray JNICALL Java_org_opencv_core_Core_n_1minMaxLocManual", "  (JNIEnv* env, jclass, jlong src_nativeObj, jlong mask_nativeObj)", "{", "    try {", "        LOGD(\"Core::n_1minMaxLoc()\");", "        jdoubleArray result;", "        result = env->NewDoubleArray(6);", "        if (result == NULL) {", "            return NULL; /* out of memory error thrown */", "        }", "\n", "        Mat& src = *((Mat*)src_nativeObj);", "\n", "        double minVal, maxVal;", "        Point minLoc, maxLoc;", "        if (mask_nativeObj != 0) {", "            Mat& mask = *((Mat*)mask_nativeObj);", "            minMaxLoc(src, &minVal, &maxVal, &minLoc, &maxLoc, mask);", "        } else {", "            minMaxLoc(src, &minVal, &maxVal, &minLoc, &maxLoc);", "        }", "\n", "        j<PERSON><PERSON><PERSON> fill[6];", "        fill[0]=minVal;", "        fill[1]=maxVal;", "        fill[2]=minLoc.x;", "        fill[3]=minLoc.y;", "        fill[4]=maxLoc.x;", "        fill[5]=maxLoc.y;", "\n", "        env->SetDoubleArrayRegion(result, 0, 6, fill);", "\n", "    return result;", "\n", "    } catch(const cv::Exception& e) {", "        LOGD(\"Core::n_1minMaxLoc() caught cv::Exception: %s\", e.what());", "        jclass je = env->FindClass(\"org/opencv/core/CvException\");", "        if(!je) je = env->FindClass(\"java/lang/Exception\");", "        env->ThrowNew(je, e.what());", "        return NULL;", "    } catch (...) {", "        LOGD(\"Core::n_1minMaxLoc() caught unknown exception (...)\");", "        jclass je = env->FindClass(\"java/lang/Exception\");", "        env->Throw<PERSON>ew(je, \"Unknown exception in JNI code {core::minMaxLoc()}\");", "        return NULL;", "    }", "}", "\n"]}}}, "func_arg_fix": {"randu": {"low": {"ctype": "double"}, "high": {"ctype": "double"}}, "randn": {"mean": {"ctype": "double"}, "stddev": {"ctype": "double"}}, "inRange": {"lowerb": {"ctype": "<PERSON><PERSON><PERSON>"}, "upperb": {"ctype": "<PERSON><PERSON><PERSON>"}}, "boundingRect": {"points": {"ctype": "vector_Point"}}, "hconcat": {"src": {"ctype": "vector_Mat"}}, "vconcat": {"src": {"ctype": "vector_Mat"}}, "checkRange": {"pos": {"ctype": "*"}}, "meanStdDev": {"mean": {"ctype": "vector_double"}, "stddev": {"ctype": "vector_double"}}, "mixChannels": {"dst": {"attrib": []}}}, "type_dict": {"Algorithm": {"j_type": "Feature2D", "jn_type": "long", "jni_type": "jlong", "jni_var": "Feature2D %(n)s", "suffix": "J", "j_import": "org.opencv.core.Algorithm"}, "CvSlice": {"j_type": "Range", "jn_args": [["int", ".start"], ["int", ".end"]], "jni_type": "jdoubleArray", "jni_var": "Range %(n)s(%(n)s_start, %(n)s_end)", "suffix": "II", "j_import": "org.opencv.core.Range"}, "CvTermCriteria": {"j_type": "TermCriteria", "jn_args": [["int", ".type"], ["int", ".maxCount"], ["double", ".epsilon"]], "jni_type": "jdoubleArray", "jni_var": "TermCriteria %(n)s(%(n)s_type, %(n)s_maxCount, %(n)s_epsilon)", "suffix": "IID", "j_import": "org.opencv.core.TermCriteria"}, "DMatch": {"j_type": "DMatch", "jn_args": [["int", "queryIdx"], ["int", "trainIdx"], ["int", "imgIdx"], ["float", "distance"]], "jni_type": "jdoubleArray", "jni_var": "DMatch %(n)s(%(n)s_queryIdx, %(n)s_trainIdx, %(n)s_imgIdx, %(n)s_distance)", "suffix": "IIIF", "j_import": "org.opencv.core.DMatch"}, "KeyPoint": {"j_type": "KeyPoint", "jn_args": [["float", ".x"], ["float", ".y"], ["float", ".size"], ["float", ".angle"], ["float", ".response"], ["int", ".octave"], ["int", ".class_id"]], "jni_type": "jdoubleArray", "jni_var": "KeyPoint %(n)s(%(n)s_x, %(n)s_y, %(n)s_size, %(n)s_angle, %(n)s_response, %(n)s_octave, %(n)s_class_id)", "suffix": "FFFFFII", "j_import": "org.opencv.core.KeyPoint"}, "Mat": {"j_type": "Mat", "jn_args": [["__int64", ".nativeObj"]], "jn_type": "long", "jni_type": "jlong", "jni_var": "Mat& %(n)s = *((Mat*)%(n)s_nativeObj)", "suffix": "J", "j_import": "org.opencv.core.Mat"}, "Moments": {"j_type": "Moments", "jn_args": [["double", ".m00"], ["double", ".m10"], ["double", ".m01"], ["double", ".m20"], ["double", ".m11"], ["double", ".m02"], ["double", ".m30"], ["double", ".m21"], ["double", ".m12"], ["double", ".m03"]], "jni_type": "jdoubleArray", "jni_var": "Moments %(n)s(%(n)s_m00, %(n)s_m10, %(n)s_m01, %(n)s_m20, %(n)s_m11, %(n)s_m02, %(n)s_m30, %(n)s_m21, %(n)s_m12, %(n)s_m03)", "suffix": "DDDDDDDDDD"}, "Point": {"j_type": "Point", "jn_args": [["double", ".x"], ["double", ".y"]], "jni_type": "jdoubleArray", "jni_var": "Point %(n)s((int)%(n)s_x, (int)%(n)s_y)", "suffix": "DD", "j_import": "org.opencv.core.Point"}, "Point2d": {"j_type": "Point", "jn_args": [["double", ".x"], ["double", ".y"]], "jni_type": "jdoubleArray", "jni_var": "Point2d %(n)s(%(n)s_x, %(n)s_y)", "suffix": "DD", "j_import": "org.opencv.core.Point"}, "Point2f": {"j_type": "Point", "jn_args": [["double", ".x"], ["double", ".y"]], "jni_type": "jdoubleArray", "jni_var": "Point2f %(n)s((float)%(n)s_x, (float)%(n)s_y)", "suffix": "DD", "j_import": "org.opencv.core.Point"}, "Point3d": {"j_type": "Point3", "jn_args": [["double", ".x"], ["double", ".y"], ["double", ".z"]], "jni_type": "jdoubleArray", "jni_var": "Point3d %(n)s(%(n)s_x, %(n)s_y, %(n)s_z)", "suffix": "DDD", "j_import": "org.opencv.core.Point3"}, "Point3f": {"j_type": "Point3", "jn_args": [["double", ".x"], ["double", ".y"], ["double", ".z"]], "jni_type": "jdoubleArray", "jni_var": "Point3f %(n)s((float)%(n)s_x, (float)%(n)s_y, (float)%(n)s_z)", "suffix": "DDD", "j_import": "org.opencv.core.Point3"}, "Point3i": {"j_type": "Point3", "jn_args": [["double", ".x"], ["double", ".y"], ["double", ".z"]], "jni_type": "jdoubleArray", "jni_var": "Point3i %(n)s((int)%(n)s_x, (int)%(n)s_y, (int)%(n)s_z)", "suffix": "DDD", "j_import": "org.opencv.core.Point3"}, "Range": {"j_type": "Range", "jn_args": [["int", ".start"], ["int", ".end"]], "jni_type": "jdoubleArray", "jni_var": "Range %(n)s(%(n)s_start, %(n)s_end)", "suffix": "II", "j_import": "org.opencv.core.Range"}, "Rect": {"j_type": "Rect", "jn_args": [["int", ".x"], ["int", ".y"], ["int", ".width"], ["int", ".height"]], "jni_type": "jdoubleArray", "jni_var": "Rect %(n)s(%(n)s_x, %(n)s_y, %(n)s_width, %(n)s_height)", "suffix": "IIII", "j_import": "org.opencv.core.Rect"}, "Rect2d": {"j_type": "Rect2d", "jn_args": [["double", ".x"], ["double", ".y"], ["double", ".width"], ["double", ".height"]], "jni_type": "jdoubleArray", "jni_var": "Rect %(n)s(%(n)s_x, %(n)s_y, %(n)s_width, %(n)s_height)", "suffix": "DDDD", "j_import": "org.opencv.core.Rect2d"}, "RotatedRect": {"j_type": "RotatedRect", "jn_args": [["double", ".center.x"], ["double", ".center.y"], ["double", ".size.width"], ["double", ".size.height"], ["double", ".angle"]], "jni_type": "jdoubleArray", "jni_var": "RotatedRect %(n)s(cv::Point2f(%(n)s_center_x, %(n)s_center_y), cv::Size2f(%(n)s_size_width, %(n)s_size_height), %(n)s_angle)", "suffix": "DDDDD", "j_import": "org.opencv.core.RotatedRect"}, "Scalar": {"j_type": "<PERSON><PERSON><PERSON>", "jn_args": [["double", ".val[0]"], ["double", ".val[1]"], ["double", ".val[2]"], ["double", ".val[3]"]], "jni_type": "jdoubleArray", "jni_var": "Scalar %(n)s(%(n)s_val0, %(n)s_val1, %(n)s_val2, %(n)s_val3)", "suffix": "DDDD", "j_import": "org.opencv.core.Scalar"}, "Size": {"j_type": "Size", "jn_args": [["double", ".width"], ["double", ".height"]], "jni_type": "jdoubleArray", "jni_var": "Size %(n)s((int)%(n)s_width, (int)%(n)s_height)", "suffix": "DD", "j_import": "org.opencv.core.Size"}, "Size2f": {"j_type": "Size", "jn_args": [["double", ".width"], ["double", ".height"]], "jni_type": "jdoubleArray", "jni_var": "Size2f %(n)s((float)%(n)s_width, (float)%(n)s_height)", "suffix": "DD", "j_import": "org.opencv.core.Size"}, "String": {"j_type": "String", "jn_type": "String", "jni_name": "n_%(n)s", "jni_type": "jstring", "jni_var": "const char* utf_%(n)s = env->GetStringUTFChars(%(n)s, 0); String n_%(n)s( utf_%(n)s ? utf_%(n)s : \"\" ); env->ReleaseStringUTFChars(%(n)s, utf_%(n)s)", "suffix": "<PERSON><PERSON><PERSON>_lang_String_2", "j_import": "java.lang.String"}, "TermCriteria": {"j_type": "TermCriteria", "jn_args": [["int", ".type"], ["int", ".maxCount"], ["double", ".epsilon"]], "jni_type": "jdoubleArray", "jni_var": "TermCriteria %(n)s(%(n)s_type, %(n)s_maxCount, %(n)s_epsilon)", "suffix": "IID", "j_import": "org.opencv.core.TermCriteria"}, "Vec2d": {"j_type": "double[]", "jn_args": [["double", ".val[0]"], ["double", ".val[1]"]], "jn_type": "double[]", "jni_type": "jdoubleArray", "jni_var": "Vec2d %(n)s(%(n)s_val0, %(n)s_val1)", "suffix": "DD"}, "Vec3d": {"j_type": "double[]", "jn_args": [["double", ".val[0]"], ["double", ".val[1]"], ["double", ".val[2]"]], "jn_type": "double[]", "jni_type": "jdoubleArray", "jni_var": "Vec3d %(n)s(%(n)s_val0, %(n)s_val1, %(n)s_val2)", "suffix": "DDD"}, "c_string": {"j_type": "String", "jn_type": "String", "jni_name": "n_%(n)s.c_str()", "jni_type": "jstring", "jni_var": "const char* utf_%(n)s = env->GetStringUTFChars(%(n)s, 0); String n_%(n)s( utf_%(n)s ? utf_%(n)s : \"\" ); env->ReleaseStringUTFChars(%(n)s, utf_%(n)s)", "suffix": "<PERSON><PERSON><PERSON>_lang_String_2", "j_import": "java.lang.String"}, "size_t": {"j_type": "long", "jn_type": "long", "jni_type": "jlong", "suffix": "J"}, "vector_DMatch": {"j_type": "MatOfDMatch", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<DMatch> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfDMatch"}, "vector_KeyPoint": {"j_type": "MatOfKeyPoint", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<KeyPoint> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfKeyPoint"}, "vector_Mat": {"j_type": "List<Mat>", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<Mat> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.Mat"}, "vector_Point": {"j_type": "MatOfPoint", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<Point> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfPoint"}, "vector_Point2f": {"j_type": "MatOfPoint2f", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<Point2f> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfPoint2f"}, "vector_Point3f": {"j_type": "MatOfPoint3f", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<Point3f> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfPoint3f"}, "vector_Point2d": {"j_type": "MatOfPoint2f", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<Point2f> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfPoint2f"}, "vector_Point3d": {"j_type": "MatOfPoint3f", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<Point3f> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfPoint3f"}, "vector_Point3i": {"j_type": "MatOfPoint3", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<Point3i> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfPoint3"}, "vector_Rect": {"j_type": "MatOfRect", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<Rect> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfRect"}, "vector_Rect2d": {"j_type": "MatOfRect2d", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<Rect2d> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfRect2d"}, "vector_RotatedRect": {"j_type": "MatOfRotatedRect", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector< RotatedRect > %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfRotatedRect"}, "vector_String": {"j_type": "List<String>", "jn_type": "List<String>", "jni_type": "jobject", "jni_var": "std::vector< String > %(n)s", "suffix": "Ljava_util_List", "v_type": "String", "j_import": "java.lang.String"}, "vector_Vec4f": {"j_type": "MatOfFloat4", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<Vec4f> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfFloat4"}, "vector_Vec4i": {"j_type": "MatOfInt4", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<Vec4i> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfInt4"}, "vector_Vec6f": {"j_type": "MatOfFloat6", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<Vec6f> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfFloat6"}, "vector_char": {"j_type": "MatOfByte", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<char> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfByte"}, "vector_double": {"j_type": "MatOfDouble", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<double> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfDouble"}, "vector_float": {"j_type": "MatOfFloat", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<float> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfFloat"}, "vector_int": {"j_type": "MatOfInt", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<int> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfInt"}, "vector_uchar": {"j_type": "MatOfByte", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector<uchar> %(n)s", "suffix": "J", "v_type": "Mat", "j_import": "org.opencv.core.MatOfByte"}, "vector_vector_DMatch": {"j_type": "List<MatOfDMatch>", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector< std::vector<DMatch> > %(n)s", "v_type": "vector_Mat", "j_import": "org.opencv.core.MatOfDMatch"}, "vector_vector_KeyPoint": {"j_type": "List<MatOfKeyPoint>", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector< std::vector<KeyPoint> > %(n)s", "v_type": "vector_Mat", "j_import": "org.opencv.core.MatOfKeyPoint"}, "vector_vector_Point": {"j_type": "List<MatOfPoint>", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector< std::vector<Point> > %(n)s", "v_type": "vector_Mat", "j_import": "org.opencv.core.MatOfPoint"}, "vector_vector_Point2f": {"j_type": "List<MatOfPoint2f>", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector< std::vector<Point2f> > %(n)s", "v_type": "vector_Mat", "j_import": "org.opencv.core.MatOfPoint2f"}, "vector_vector_Point3f": {"j_type": "List<MatOfPoint3f>", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector< std::vector<Point3f> > %(n)s", "v_type": "vector_Mat", "j_import": "org.opencv.core.MatOfPoint3f"}, "vector_vector_char": {"j_type": "List<MatOfByte>", "jn_type": "long", "jni_type": "jlong", "jni_var": "std::vector< std::vector<char> > %(n)s", "v_type": "vector_Mat", "j_import": "org.opencv.core.MatOfByte"}}}