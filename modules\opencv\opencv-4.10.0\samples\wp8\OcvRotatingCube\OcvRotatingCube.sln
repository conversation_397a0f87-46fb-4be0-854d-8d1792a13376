﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 2013
VisualStudioVersion = 12.0.31101.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PhoneXamlDirect3DApp1Comp", "PhoneXamlDirect3DApp1\PhoneXamlDirect3DApp1Comp\PhoneXamlDirect3DApp1Comp.vcxproj", "{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OcvRotatingCube", "PhoneXamlDirect3DApp1\PhoneXamlDirect3DApp1\OcvRotatingCube.csproj", "{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|ARM = Debug|ARM
		Debug|Mixed Platforms = Debug|Mixed Platforms
		Debug|Win32 = Debug|Win32
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|ARM = Release|ARM
		Release|Mixed Platforms = Release|Mixed Platforms
		Release|Win32 = Release|Win32
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Debug|Any CPU.ActiveCfg = Debug|Win32
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Debug|ARM.ActiveCfg = Debug|ARM
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Debug|ARM.Build.0 = Debug|ARM
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Debug|Mixed Platforms.ActiveCfg = Debug|Win32
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Debug|Mixed Platforms.Build.0 = Debug|Win32
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Debug|Win32.ActiveCfg = Debug|Win32
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Debug|Win32.Build.0 = Debug|Win32
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Debug|x86.ActiveCfg = Debug|Win32
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Debug|x86.Build.0 = Debug|Win32
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Release|Any CPU.ActiveCfg = Release|Win32
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Release|ARM.ActiveCfg = Release|ARM
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Release|ARM.Build.0 = Release|ARM
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Release|Mixed Platforms.ActiveCfg = Release|Win32
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Release|Mixed Platforms.Build.0 = Release|Win32
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Release|Win32.ActiveCfg = Release|Win32
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Release|Win32.Build.0 = Release|Win32
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Release|x86.ActiveCfg = Release|Win32
		{C0F94AFA-466F-4FC4-B5C1-6CD955F3FF88}.Release|x86.Build.0 = Release|Win32
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Debug|ARM.ActiveCfg = Debug|ARM
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Debug|ARM.Build.0 = Debug|ARM
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Debug|ARM.Deploy.0 = Debug|ARM
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Debug|Mixed Platforms.ActiveCfg = Debug|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Debug|Mixed Platforms.Build.0 = Debug|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Debug|Mixed Platforms.Deploy.0 = Debug|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Debug|Win32.ActiveCfg = Debug|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Debug|Win32.Build.0 = Debug|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Debug|Win32.Deploy.0 = Debug|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Debug|x86.ActiveCfg = Debug|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Debug|x86.Build.0 = Debug|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Debug|x86.Deploy.0 = Debug|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Release|Any CPU.Build.0 = Release|Any CPU
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Release|ARM.ActiveCfg = Release|ARM
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Release|ARM.Build.0 = Release|ARM
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Release|ARM.Deploy.0 = Release|ARM
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Release|Mixed Platforms.ActiveCfg = Release|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Release|Mixed Platforms.Build.0 = Release|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Release|Mixed Platforms.Deploy.0 = Release|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Release|Win32.ActiveCfg = Release|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Release|Win32.Build.0 = Release|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Release|Win32.Deploy.0 = Release|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Release|x86.ActiveCfg = Release|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Release|x86.Build.0 = Release|x86
		{CC734B3D-D8F2-4528-B223-0E7B8A4F6CC7}.Release|x86.Deploy.0 = Release|x86
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
