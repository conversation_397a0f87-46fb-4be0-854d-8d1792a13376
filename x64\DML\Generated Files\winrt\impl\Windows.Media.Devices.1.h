// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Media_Devices_1_H
#define WINRT_Windows_Media_Devices_1_H
#include "winrt/impl/Windows.Media.Devices.0.h"
WINRT_EXPORT namespace winrt::Windows::Media::Devices
{
    struct WINRT_IMPL_EMPTY_BASES IAdvancedPhotoCaptureSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedPhotoCaptureSettings>
    {
        IAdvancedPhotoCaptureSettings(std::nullptr_t = nullptr) noexcept {}
        IAdvancedPhotoCaptureSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAdvancedPhotoControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedPhotoControl>
    {
        IAdvancedPhotoControl(std::nullptr_t = nullptr) noexcept {}
        IAdvancedPhotoControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAdvancedVideoCaptureDeviceController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController>
    {
        IAdvancedVideoCaptureDeviceController(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAdvancedVideoCaptureDeviceController10 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController10>
    {
        IAdvancedVideoCaptureDeviceController10(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController10(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAdvancedVideoCaptureDeviceController11 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController11>
    {
        IAdvancedVideoCaptureDeviceController11(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController11(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAdvancedVideoCaptureDeviceController2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController2>
    {
        IAdvancedVideoCaptureDeviceController2(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAdvancedVideoCaptureDeviceController3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController3>
    {
        IAdvancedVideoCaptureDeviceController3(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAdvancedVideoCaptureDeviceController4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController4>
    {
        IAdvancedVideoCaptureDeviceController4(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAdvancedVideoCaptureDeviceController5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController5>
    {
        IAdvancedVideoCaptureDeviceController5(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAdvancedVideoCaptureDeviceController6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController6>
    {
        IAdvancedVideoCaptureDeviceController6(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAdvancedVideoCaptureDeviceController7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController7>
    {
        IAdvancedVideoCaptureDeviceController7(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAdvancedVideoCaptureDeviceController8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController8>
    {
        IAdvancedVideoCaptureDeviceController8(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAdvancedVideoCaptureDeviceController9 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController9>
    {
        IAdvancedVideoCaptureDeviceController9(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController9(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAudioDeviceController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAudioDeviceController>,
        impl::require<winrt::Windows::Media::Devices::IAudioDeviceController, winrt::Windows::Media::Devices::IMediaDeviceController>
    {
        IAudioDeviceController(std::nullptr_t = nullptr) noexcept {}
        IAudioDeviceController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAudioDeviceController2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAudioDeviceController2>
    {
        IAudioDeviceController2(std::nullptr_t = nullptr) noexcept {}
        IAudioDeviceController2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAudioDeviceModule :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAudioDeviceModule>
    {
        IAudioDeviceModule(std::nullptr_t = nullptr) noexcept {}
        IAudioDeviceModule(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAudioDeviceModuleNotificationEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAudioDeviceModuleNotificationEventArgs>
    {
        IAudioDeviceModuleNotificationEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAudioDeviceModuleNotificationEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAudioDeviceModulesManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAudioDeviceModulesManager>
    {
        IAudioDeviceModulesManager(std::nullptr_t = nullptr) noexcept {}
        IAudioDeviceModulesManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAudioDeviceModulesManagerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAudioDeviceModulesManagerFactory>
    {
        IAudioDeviceModulesManagerFactory(std::nullptr_t = nullptr) noexcept {}
        IAudioDeviceModulesManagerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICallControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICallControl>
    {
        ICallControl(std::nullptr_t = nullptr) noexcept {}
        ICallControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICallControlStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICallControlStatics>
    {
        ICallControlStatics(std::nullptr_t = nullptr) noexcept {}
        ICallControlStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICameraOcclusionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICameraOcclusionInfo>
    {
        ICameraOcclusionInfo(std::nullptr_t = nullptr) noexcept {}
        ICameraOcclusionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICameraOcclusionState :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICameraOcclusionState>
    {
        ICameraOcclusionState(std::nullptr_t = nullptr) noexcept {}
        ICameraOcclusionState(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICameraOcclusionStateChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICameraOcclusionStateChangedEventArgs>
    {
        ICameraOcclusionStateChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICameraOcclusionStateChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDefaultAudioDeviceChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDefaultAudioDeviceChangedEventArgs>
    {
        IDefaultAudioDeviceChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDefaultAudioDeviceChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDialRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDialRequestedEventArgs>
    {
        IDialRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDialRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDigitalWindowBounds :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDigitalWindowBounds>
    {
        IDigitalWindowBounds(std::nullptr_t = nullptr) noexcept {}
        IDigitalWindowBounds(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDigitalWindowCapability :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDigitalWindowCapability>
    {
        IDigitalWindowCapability(std::nullptr_t = nullptr) noexcept {}
        IDigitalWindowCapability(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDigitalWindowControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDigitalWindowControl>
    {
        IDigitalWindowControl(std::nullptr_t = nullptr) noexcept {}
        IDigitalWindowControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IExposureCompensationControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExposureCompensationControl>
    {
        IExposureCompensationControl(std::nullptr_t = nullptr) noexcept {}
        IExposureCompensationControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IExposureControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExposureControl>
    {
        IExposureControl(std::nullptr_t = nullptr) noexcept {}
        IExposureControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IExposurePriorityVideoControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExposurePriorityVideoControl>
    {
        IExposurePriorityVideoControl(std::nullptr_t = nullptr) noexcept {}
        IExposurePriorityVideoControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFlashControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlashControl>
    {
        IFlashControl(std::nullptr_t = nullptr) noexcept {}
        IFlashControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFlashControl2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlashControl2>
    {
        IFlashControl2(std::nullptr_t = nullptr) noexcept {}
        IFlashControl2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFocusControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusControl>
    {
        IFocusControl(std::nullptr_t = nullptr) noexcept {}
        IFocusControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFocusControl2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusControl2>
    {
        IFocusControl2(std::nullptr_t = nullptr) noexcept {}
        IFocusControl2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFocusSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusSettings>
    {
        IFocusSettings(std::nullptr_t = nullptr) noexcept {}
        IFocusSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHdrVideoControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHdrVideoControl>
    {
        IHdrVideoControl(std::nullptr_t = nullptr) noexcept {}
        IHdrVideoControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInfraredTorchControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInfraredTorchControl>
    {
        IInfraredTorchControl(std::nullptr_t = nullptr) noexcept {}
        IInfraredTorchControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsoSpeedControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsoSpeedControl>
    {
        IIsoSpeedControl(std::nullptr_t = nullptr) noexcept {}
        IIsoSpeedControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsoSpeedControl2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsoSpeedControl2>
    {
        IIsoSpeedControl2(std::nullptr_t = nullptr) noexcept {}
        IIsoSpeedControl2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeypadPressedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeypadPressedEventArgs>
    {
        IKeypadPressedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IKeypadPressedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILowLagPhotoControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILowLagPhotoControl>
    {
        ILowLagPhotoControl(std::nullptr_t = nullptr) noexcept {}
        ILowLagPhotoControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILowLagPhotoSequenceControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILowLagPhotoSequenceControl>
    {
        ILowLagPhotoSequenceControl(std::nullptr_t = nullptr) noexcept {}
        ILowLagPhotoSequenceControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMediaDeviceControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaDeviceControl>
    {
        IMediaDeviceControl(std::nullptr_t = nullptr) noexcept {}
        IMediaDeviceControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMediaDeviceControlCapabilities :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaDeviceControlCapabilities>
    {
        IMediaDeviceControlCapabilities(std::nullptr_t = nullptr) noexcept {}
        IMediaDeviceControlCapabilities(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMediaDeviceController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaDeviceController>
    {
        IMediaDeviceController(std::nullptr_t = nullptr) noexcept {}
        IMediaDeviceController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMediaDeviceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaDeviceStatics>
    {
        IMediaDeviceStatics(std::nullptr_t = nullptr) noexcept {}
        IMediaDeviceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IModuleCommandResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IModuleCommandResult>
    {
        IModuleCommandResult(std::nullptr_t = nullptr) noexcept {}
        IModuleCommandResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOpticalImageStabilizationControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOpticalImageStabilizationControl>
    {
        IOpticalImageStabilizationControl(std::nullptr_t = nullptr) noexcept {}
        IOpticalImageStabilizationControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPanelBasedOptimizationControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPanelBasedOptimizationControl>
    {
        IPanelBasedOptimizationControl(std::nullptr_t = nullptr) noexcept {}
        IPanelBasedOptimizationControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoConfirmationControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoConfirmationControl>
    {
        IPhotoConfirmationControl(std::nullptr_t = nullptr) noexcept {}
        IPhotoConfirmationControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRedialRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRedialRequestedEventArgs>
    {
        IRedialRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRedialRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRegionOfInterest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRegionOfInterest>
    {
        IRegionOfInterest(std::nullptr_t = nullptr) noexcept {}
        IRegionOfInterest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRegionOfInterest2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRegionOfInterest2>
    {
        IRegionOfInterest2(std::nullptr_t = nullptr) noexcept {}
        IRegionOfInterest2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRegionsOfInterestControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRegionsOfInterestControl>
    {
        IRegionsOfInterestControl(std::nullptr_t = nullptr) noexcept {}
        IRegionsOfInterestControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneModeControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneModeControl>
    {
        ISceneModeControl(std::nullptr_t = nullptr) noexcept {}
        ISceneModeControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITorchControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITorchControl>
    {
        ITorchControl(std::nullptr_t = nullptr) noexcept {}
        ITorchControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVideoDeviceController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVideoDeviceController>,
        impl::require<winrt::Windows::Media::Devices::IVideoDeviceController, winrt::Windows::Media::Devices::IMediaDeviceController>
    {
        IVideoDeviceController(std::nullptr_t = nullptr) noexcept {}
        IVideoDeviceController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVideoDeviceControllerGetDevicePropertyResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVideoDeviceControllerGetDevicePropertyResult>
    {
        IVideoDeviceControllerGetDevicePropertyResult(std::nullptr_t = nullptr) noexcept {}
        IVideoDeviceControllerGetDevicePropertyResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVideoTemporalDenoisingControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVideoTemporalDenoisingControl>
    {
        IVideoTemporalDenoisingControl(std::nullptr_t = nullptr) noexcept {}
        IVideoTemporalDenoisingControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWhiteBalanceControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWhiteBalanceControl>
    {
        IWhiteBalanceControl(std::nullptr_t = nullptr) noexcept {}
        IWhiteBalanceControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IZoomControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IZoomControl>
    {
        IZoomControl(std::nullptr_t = nullptr) noexcept {}
        IZoomControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IZoomControl2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IZoomControl2>
    {
        IZoomControl2(std::nullptr_t = nullptr) noexcept {}
        IZoomControl2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IZoomSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IZoomSettings>
    {
        IZoomSettings(std::nullptr_t = nullptr) noexcept {}
        IZoomSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
