
/*
// Copyright 2014 Intel Corporation All Rights Reserved.
//
//
// This software and the related documents are Intel copyrighted materials, and your use of them is governed by
// the express license under which they were provided to you ('License'). Unless the License provides otherwise,
// you may not use, modify, copy, publish, distribute, disclose or transmit this software or the related
// documents without Intel's prior written permission.
// This software and the related documents are provided as is, with no express or implied warranties, other than
// those that are expressly stated in the License.
//
*/

#if !defined( __IPPICV_REDEFS_H__ )
#define __IPPICV_REDEFS_H__

#if !defined( __NO_ICV_REDEF__ )

#define ippFree ippicvFree
#define ippGetCacheParams ippicvGetCacheParams
#define ippGetCpuFeatures ippicvGetCpuFeatures
#define ippGetEnabledCpuFeatures ippicvGetEnabledCpuFeatures
#define ippGetL2CacheSize ippicvGetL2CacheSize
#define ippGetLibVersion ippicvGetLibVersion
#define ippInit ippicvInit
#define ippMalloc_L ippicvMalloc_L
#define ippSetCpuFeatures ippicvSetCpuFeatures
#define ippccGetLibVersion ippicvccGetLibVersion
#define ippcvGetLibVersion ippicvcvGetLibVersion
#define ippiAbsDiff_16u_C1R ippicviAbsDiff_16u_C1R
#define ippiAbsDiff_32f_C1R ippicviAbsDiff_32f_C1R
#define ippiAbsDiff_8u_C1R ippicviAbsDiff_8u_C1R
#define ippiAddC_16s_C1IRSfs ippicviAddC_16s_C1IRSfs
#define ippiAddC_32f_C1IR ippicviAddC_32f_C1IR
#define ippiAddC_32f_C1R ippicviAddC_32f_C1R
#define ippiAddProduct_16u32f_C1IMR ippicviAddProduct_16u32f_C1IMR
#define ippiAddProduct_16u32f_C1IR ippicviAddProduct_16u32f_C1IR
#define ippiAddProduct_32f_C1IMR ippicviAddProduct_32f_C1IMR
#define ippiAddProduct_32f_C1IR ippicviAddProduct_32f_C1IR
#define ippiAddProduct_8u32f_C1IMR ippicviAddProduct_8u32f_C1IMR
#define ippiAddProduct_8u32f_C1IR ippicviAddProduct_8u32f_C1IR
#define ippiAddSquare_16u32f_C1IMR ippicviAddSquare_16u32f_C1IMR
#define ippiAddSquare_16u32f_C1IR ippicviAddSquare_16u32f_C1IR
#define ippiAddSquare_32f_C1IMR ippicviAddSquare_32f_C1IMR
#define ippiAddSquare_32f_C1IR ippicviAddSquare_32f_C1IR
#define ippiAddSquare_8u32f_C1IMR ippicviAddSquare_8u32f_C1IMR
#define ippiAddSquare_8u32f_C1IR ippicviAddSquare_8u32f_C1IR
#define ippiAddWeighted_16u32f_C1IMR ippicviAddWeighted_16u32f_C1IMR
#define ippiAddWeighted_16u32f_C1IR ippicviAddWeighted_16u32f_C1IR
#define ippiAddWeighted_32f_C1IMR ippicviAddWeighted_32f_C1IMR
#define ippiAddWeighted_32f_C1IR ippicviAddWeighted_32f_C1IR
#define ippiAddWeighted_8u32f_C1IMR ippicviAddWeighted_8u32f_C1IMR
#define ippiAddWeighted_8u32f_C1IR ippicviAddWeighted_8u32f_C1IR
#define ippiAdd_16s_C1RSfs ippicviAdd_16s_C1RSfs
#define ippiAdd_16u32f_C1IMR ippicviAdd_16u32f_C1IMR
#define ippiAdd_16u32f_C1IR ippicviAdd_16u32f_C1IR
#define ippiAdd_16u_C1RSfs ippicviAdd_16u_C1RSfs
#define ippiAdd_32f_C1IMR ippicviAdd_32f_C1IMR
#define ippiAdd_32f_C1IR ippicviAdd_32f_C1IR
#define ippiAdd_32f_C1R ippicviAdd_32f_C1R
#define ippiAdd_8u32f_C1IMR ippicviAdd_8u32f_C1IMR
#define ippiAdd_8u32f_C1IR ippicviAdd_8u32f_C1IR
#define ippiAdd_8u_C1RSfs ippicviAdd_8u_C1RSfs
#define ippiAlphaPremul_8u_AC4R ippicviAlphaPremul_8u_AC4R
#define ippiAnd_8u_C1R ippicviAnd_8u_C1R
#define ippiApplyHaarClassifier_32f_C1R ippicviApplyHaarClassifier_32f_C1R
#define ippiApplyMixedHaarClassifier_32f_C1R ippicviApplyMixedHaarClassifier_32f_C1R
#define ippiBGRToLab_8u_C3R ippicviBGRToLab_8u_C3R
#define ippiCannyBorderGetSize ippicviCannyBorderGetSize
#define ippiCannyBorder_8u_C1R ippicviCannyBorder_8u_C1R
#define ippiCannyGetSize ippicviCannyGetSize
#define ippiCannyGetSize_L ippicviCannyGetSize_L
#define ippiCanny_16s8u_C1R ippicviCanny_16s8u_C1R
#define ippiCanny_16s8u_C1R_L ippicviCanny_16s8u_C1R_L
#define ippiCanny_32f8u_C1R ippicviCanny_32f8u_C1R
#define ippiCanny_32f8u_C1R_L ippicviCanny_32f8u_C1R_L
#define ippiColorToGray_16s_AC4C1R ippicviColorToGray_16s_AC4C1R
#define ippiColorToGray_16s_C3C1R ippicviColorToGray_16s_C3C1R
#define ippiColorToGray_16u_AC4C1R ippicviColorToGray_16u_AC4C1R
#define ippiColorToGray_16u_C3C1R ippicviColorToGray_16u_C3C1R
#define ippiColorToGray_32f_AC4C1R ippicviColorToGray_32f_AC4C1R
#define ippiColorToGray_32f_C3C1R ippicviColorToGray_32f_C3C1R
#define ippiColorToGray_8u_AC4C1R ippicviColorToGray_8u_AC4C1R
#define ippiColorToGray_8u_C3C1R ippicviColorToGray_8u_C3C1R
#define ippiCompare_16s_C1R ippicviCompare_16s_C1R
#define ippiCompare_16u_C1R ippicviCompare_16u_C1R
#define ippiCompare_32f_C1R ippicviCompare_32f_C1R
#define ippiCompare_8u_C1R ippicviCompare_8u_C1R
#define ippiComputeThreshold_Otsu_8u_C1R ippicviComputeThreshold_Otsu_8u_C1R
#define ippiConvert_16s16u_C1Rs ippicviConvert_16s16u_C1Rs
#define ippiConvert_16s32f_C1R ippicviConvert_16s32f_C1R
#define ippiConvert_16s32s_C1R ippicviConvert_16s32s_C1R
#define ippiConvert_16s8s_C1RSfs ippicviConvert_16s8s_C1RSfs
#define ippiConvert_16s8u_C1R ippicviConvert_16s8u_C1R
#define ippiConvert_16u16s_C1RSfs ippicviConvert_16u16s_C1RSfs
#define ippiConvert_16u32f_C1R ippicviConvert_16u32f_C1R
#define ippiConvert_16u32s_C1R ippicviConvert_16u32s_C1R
#define ippiConvert_16u8s_C1RSfs ippicviConvert_16u8s_C1RSfs
#define ippiConvert_16u8u_C1R ippicviConvert_16u8u_C1R
#define ippiConvert_32f16s_C1R ippicviConvert_32f16s_C1R
#define ippiConvert_32f16u_C1R ippicviConvert_32f16u_C1R
#define ippiConvert_32f16u_C1RSfs ippicviConvert_32f16u_C1RSfs
#define ippiConvert_32f32s_C1RSfs ippicviConvert_32f32s_C1RSfs
#define ippiConvert_32f8s_C1R ippicviConvert_32f8s_C1R
#define ippiConvert_32f8s_C1RSfs ippicviConvert_32f8s_C1RSfs
#define ippiConvert_32f8u_C1R ippicviConvert_32f8u_C1R
#define ippiConvert_32f8u_C1RSfs ippicviConvert_32f8u_C1RSfs
#define ippiConvert_32s16s_C1RSfs ippicviConvert_32s16s_C1RSfs
#define ippiConvert_32s16u_C1RSfs ippicviConvert_32s16u_C1RSfs
#define ippiConvert_32s32f_C1R ippicviConvert_32s32f_C1R
#define ippiConvert_32s8s_C1R ippicviConvert_32s8s_C1R
#define ippiConvert_32s8u_C1R ippicviConvert_32s8u_C1R
#define ippiConvert_8s16s_C1R ippicviConvert_8s16s_C1R
#define ippiConvert_8s16u_C1Rs ippicviConvert_8s16u_C1Rs
#define ippiConvert_8s32f_C1R ippicviConvert_8s32f_C1R
#define ippiConvert_8s32s_C1R ippicviConvert_8s32s_C1R
#define ippiConvert_8s8u_C1Rs ippicviConvert_8s8u_C1Rs
#define ippiConvert_8u16s_C1R ippicviConvert_8u16s_C1R
#define ippiConvert_8u16u_C1R ippicviConvert_8u16u_C1R
#define ippiConvert_8u32f_C1R ippicviConvert_8u32f_C1R
#define ippiConvert_8u32s_C1R ippicviConvert_8u32s_C1R
#define ippiConvert_8u8s_C1RSfs ippicviConvert_8u8s_C1RSfs
#define ippiCopyConstBorder_16s_C1IR_L ippicviCopyConstBorder_16s_C1IR_L
#define ippiCopyConstBorder_16s_C1R_L ippicviCopyConstBorder_16s_C1R_L
#define ippiCopyConstBorder_16s_C3IR_L ippicviCopyConstBorder_16s_C3IR_L
#define ippiCopyConstBorder_16s_C3R_L ippicviCopyConstBorder_16s_C3R_L
#define ippiCopyConstBorder_16s_C4IR_L ippicviCopyConstBorder_16s_C4IR_L
#define ippiCopyConstBorder_16s_C4R_L ippicviCopyConstBorder_16s_C4R_L
#define ippiCopyConstBorder_16u_C1IR_L ippicviCopyConstBorder_16u_C1IR_L
#define ippiCopyConstBorder_16u_C1R_L ippicviCopyConstBorder_16u_C1R_L
#define ippiCopyConstBorder_16u_C3IR_L ippicviCopyConstBorder_16u_C3IR_L
#define ippiCopyConstBorder_16u_C3R_L ippicviCopyConstBorder_16u_C3R_L
#define ippiCopyConstBorder_16u_C4IR_L ippicviCopyConstBorder_16u_C4IR_L
#define ippiCopyConstBorder_16u_C4R_L ippicviCopyConstBorder_16u_C4R_L
#define ippiCopyConstBorder_32f_C1IR_L ippicviCopyConstBorder_32f_C1IR_L
#define ippiCopyConstBorder_32f_C1R_L ippicviCopyConstBorder_32f_C1R_L
#define ippiCopyConstBorder_32f_C3IR_L ippicviCopyConstBorder_32f_C3IR_L
#define ippiCopyConstBorder_32f_C3R_L ippicviCopyConstBorder_32f_C3R_L
#define ippiCopyConstBorder_32f_C4IR_L ippicviCopyConstBorder_32f_C4IR_L
#define ippiCopyConstBorder_32f_C4R_L ippicviCopyConstBorder_32f_C4R_L
#define ippiCopyConstBorder_32s_C1IR_L ippicviCopyConstBorder_32s_C1IR_L
#define ippiCopyConstBorder_32s_C1R_L ippicviCopyConstBorder_32s_C1R_L
#define ippiCopyConstBorder_32s_C3IR_L ippicviCopyConstBorder_32s_C3IR_L
#define ippiCopyConstBorder_32s_C3R_L ippicviCopyConstBorder_32s_C3R_L
#define ippiCopyConstBorder_32s_C4IR_L ippicviCopyConstBorder_32s_C4IR_L
#define ippiCopyConstBorder_32s_C4R_L ippicviCopyConstBorder_32s_C4R_L
#define ippiCopyConstBorder_8u_C1IR_L ippicviCopyConstBorder_8u_C1IR_L
#define ippiCopyConstBorder_8u_C1R_L ippicviCopyConstBorder_8u_C1R_L
#define ippiCopyConstBorder_8u_C3IR_L ippicviCopyConstBorder_8u_C3IR_L
#define ippiCopyConstBorder_8u_C3R_L ippicviCopyConstBorder_8u_C3R_L
#define ippiCopyConstBorder_8u_C4IR_L ippicviCopyConstBorder_8u_C4IR_L
#define ippiCopyConstBorder_8u_C4R_L ippicviCopyConstBorder_8u_C4R_L
#define ippiCopyMirrorBorder_16s_C1IR_L ippicviCopyMirrorBorder_16s_C1IR_L
#define ippiCopyMirrorBorder_16s_C1R_L ippicviCopyMirrorBorder_16s_C1R_L
#define ippiCopyMirrorBorder_16s_C3IR_L ippicviCopyMirrorBorder_16s_C3IR_L
#define ippiCopyMirrorBorder_16s_C3R_L ippicviCopyMirrorBorder_16s_C3R_L
#define ippiCopyMirrorBorder_16s_C4IR_L ippicviCopyMirrorBorder_16s_C4IR_L
#define ippiCopyMirrorBorder_16s_C4R_L ippicviCopyMirrorBorder_16s_C4R_L
#define ippiCopyMirrorBorder_16u_C1IR_L ippicviCopyMirrorBorder_16u_C1IR_L
#define ippiCopyMirrorBorder_16u_C1R_L ippicviCopyMirrorBorder_16u_C1R_L
#define ippiCopyMirrorBorder_16u_C3IR_L ippicviCopyMirrorBorder_16u_C3IR_L
#define ippiCopyMirrorBorder_16u_C3R_L ippicviCopyMirrorBorder_16u_C3R_L
#define ippiCopyMirrorBorder_16u_C4IR_L ippicviCopyMirrorBorder_16u_C4IR_L
#define ippiCopyMirrorBorder_16u_C4R_L ippicviCopyMirrorBorder_16u_C4R_L
#define ippiCopyMirrorBorder_32f_C1IR_L ippicviCopyMirrorBorder_32f_C1IR_L
#define ippiCopyMirrorBorder_32f_C1R_L ippicviCopyMirrorBorder_32f_C1R_L
#define ippiCopyMirrorBorder_32f_C3IR_L ippicviCopyMirrorBorder_32f_C3IR_L
#define ippiCopyMirrorBorder_32f_C3R_L ippicviCopyMirrorBorder_32f_C3R_L
#define ippiCopyMirrorBorder_32f_C4IR_L ippicviCopyMirrorBorder_32f_C4IR_L
#define ippiCopyMirrorBorder_32f_C4R_L ippicviCopyMirrorBorder_32f_C4R_L
#define ippiCopyMirrorBorder_32s_C1IR_L ippicviCopyMirrorBorder_32s_C1IR_L
#define ippiCopyMirrorBorder_32s_C1R_L ippicviCopyMirrorBorder_32s_C1R_L
#define ippiCopyMirrorBorder_32s_C3IR_L ippicviCopyMirrorBorder_32s_C3IR_L
#define ippiCopyMirrorBorder_32s_C3R_L ippicviCopyMirrorBorder_32s_C3R_L
#define ippiCopyMirrorBorder_32s_C4IR_L ippicviCopyMirrorBorder_32s_C4IR_L
#define ippiCopyMirrorBorder_32s_C4R_L ippicviCopyMirrorBorder_32s_C4R_L
#define ippiCopyMirrorBorder_8u_C1IR_L ippicviCopyMirrorBorder_8u_C1IR_L
#define ippiCopyMirrorBorder_8u_C1R_L ippicviCopyMirrorBorder_8u_C1R_L
#define ippiCopyMirrorBorder_8u_C3IR_L ippicviCopyMirrorBorder_8u_C3IR_L
#define ippiCopyMirrorBorder_8u_C3R_L ippicviCopyMirrorBorder_8u_C3R_L
#define ippiCopyMirrorBorder_8u_C4IR_L ippicviCopyMirrorBorder_8u_C4IR_L
#define ippiCopyMirrorBorder_8u_C4R_L ippicviCopyMirrorBorder_8u_C4R_L
#define ippiCopyReplicateBorder_16s_C1IR_L ippicviCopyReplicateBorder_16s_C1IR_L
#define ippiCopyReplicateBorder_16s_C1R_L ippicviCopyReplicateBorder_16s_C1R_L
#define ippiCopyReplicateBorder_16s_C3IR_L ippicviCopyReplicateBorder_16s_C3IR_L
#define ippiCopyReplicateBorder_16s_C3R_L ippicviCopyReplicateBorder_16s_C3R_L
#define ippiCopyReplicateBorder_16s_C4IR_L ippicviCopyReplicateBorder_16s_C4IR_L
#define ippiCopyReplicateBorder_16s_C4R_L ippicviCopyReplicateBorder_16s_C4R_L
#define ippiCopyReplicateBorder_16u_C1IR_L ippicviCopyReplicateBorder_16u_C1IR_L
#define ippiCopyReplicateBorder_16u_C1R_L ippicviCopyReplicateBorder_16u_C1R_L
#define ippiCopyReplicateBorder_16u_C3IR_L ippicviCopyReplicateBorder_16u_C3IR_L
#define ippiCopyReplicateBorder_16u_C3R_L ippicviCopyReplicateBorder_16u_C3R_L
#define ippiCopyReplicateBorder_16u_C4IR_L ippicviCopyReplicateBorder_16u_C4IR_L
#define ippiCopyReplicateBorder_16u_C4R_L ippicviCopyReplicateBorder_16u_C4R_L
#define ippiCopyReplicateBorder_32f_C1IR_L ippicviCopyReplicateBorder_32f_C1IR_L
#define ippiCopyReplicateBorder_32f_C1R_L ippicviCopyReplicateBorder_32f_C1R_L
#define ippiCopyReplicateBorder_32f_C3IR_L ippicviCopyReplicateBorder_32f_C3IR_L
#define ippiCopyReplicateBorder_32f_C3R_L ippicviCopyReplicateBorder_32f_C3R_L
#define ippiCopyReplicateBorder_32f_C4IR_L ippicviCopyReplicateBorder_32f_C4IR_L
#define ippiCopyReplicateBorder_32f_C4R_L ippicviCopyReplicateBorder_32f_C4R_L
#define ippiCopyReplicateBorder_32s_C1IR_L ippicviCopyReplicateBorder_32s_C1IR_L
#define ippiCopyReplicateBorder_32s_C1R_L ippicviCopyReplicateBorder_32s_C1R_L
#define ippiCopyReplicateBorder_32s_C3IR_L ippicviCopyReplicateBorder_32s_C3IR_L
#define ippiCopyReplicateBorder_32s_C3R_L ippicviCopyReplicateBorder_32s_C3R_L
#define ippiCopyReplicateBorder_32s_C4IR_L ippicviCopyReplicateBorder_32s_C4IR_L
#define ippiCopyReplicateBorder_32s_C4R_L ippicviCopyReplicateBorder_32s_C4R_L
#define ippiCopyReplicateBorder_8u_C1IR_L ippicviCopyReplicateBorder_8u_C1IR_L
#define ippiCopyReplicateBorder_8u_C1R_L ippicviCopyReplicateBorder_8u_C1R_L
#define ippiCopyReplicateBorder_8u_C3IR_L ippicviCopyReplicateBorder_8u_C3IR_L
#define ippiCopyReplicateBorder_8u_C3R_L ippicviCopyReplicateBorder_8u_C3R_L
#define ippiCopyReplicateBorder_8u_C4IR_L ippicviCopyReplicateBorder_8u_C4IR_L
#define ippiCopyReplicateBorder_8u_C4R_L ippicviCopyReplicateBorder_8u_C4R_L
#define ippiCopySubpixIntersect_32f_C1R ippicviCopySubpixIntersect_32f_C1R
#define ippiCopySubpixIntersect_8u32f_C1R ippicviCopySubpixIntersect_8u32f_C1R
#define ippiCopySubpixIntersect_8u_C1R ippicviCopySubpixIntersect_8u_C1R
#define ippiCopyWrapBorder_32f_C1IR_L ippicviCopyWrapBorder_32f_C1IR_L
#define ippiCopyWrapBorder_32f_C1R_L ippicviCopyWrapBorder_32f_C1R_L
#define ippiCopyWrapBorder_32s_C1IR_L ippicviCopyWrapBorder_32s_C1IR_L
#define ippiCopyWrapBorder_32s_C1R_L ippicviCopyWrapBorder_32s_C1R_L
#define ippiCopy_16s_C1C3R ippicviCopy_16s_C1C3R
#define ippiCopy_16s_C1C4R ippicviCopy_16s_C1C4R
#define ippiCopy_16s_C1MR ippicviCopy_16s_C1MR
#define ippiCopy_16s_C1R ippicviCopy_16s_C1R
#define ippiCopy_16s_C3C1R ippicviCopy_16s_C3C1R
#define ippiCopy_16s_C3CR ippicviCopy_16s_C3CR
#define ippiCopy_16s_C3MR ippicviCopy_16s_C3MR
#define ippiCopy_16s_C3P3R ippicviCopy_16s_C3P3R
#define ippiCopy_16s_C4C1R ippicviCopy_16s_C4C1R
#define ippiCopy_16s_C4CR ippicviCopy_16s_C4CR
#define ippiCopy_16s_C4MR ippicviCopy_16s_C4MR
#define ippiCopy_16s_C4P4R ippicviCopy_16s_C4P4R
#define ippiCopy_16s_P3C3R ippicviCopy_16s_P3C3R
#define ippiCopy_16s_P4C4R ippicviCopy_16s_P4C4R
#define ippiCopy_16u_AC4C3R ippicviCopy_16u_AC4C3R
#define ippiCopy_16u_C1C3R ippicviCopy_16u_C1C3R
#define ippiCopy_16u_C1C4R ippicviCopy_16u_C1C4R
#define ippiCopy_16u_C1MR ippicviCopy_16u_C1MR
#define ippiCopy_16u_C3AC4R ippicviCopy_16u_C3AC4R
#define ippiCopy_16u_C3C1R ippicviCopy_16u_C3C1R
#define ippiCopy_16u_C3CR ippicviCopy_16u_C3CR
#define ippiCopy_16u_C3MR ippicviCopy_16u_C3MR
#define ippiCopy_16u_C3P3R ippicviCopy_16u_C3P3R
#define ippiCopy_16u_C4C1R ippicviCopy_16u_C4C1R
#define ippiCopy_16u_C4CR ippicviCopy_16u_C4CR
#define ippiCopy_16u_C4MR ippicviCopy_16u_C4MR
#define ippiCopy_16u_C4P4R ippicviCopy_16u_C4P4R
#define ippiCopy_16u_P3C3R ippicviCopy_16u_P3C3R
#define ippiCopy_16u_P4C4R ippicviCopy_16u_P4C4R
#define ippiCopy_32f_AC4C3R ippicviCopy_32f_AC4C3R
#define ippiCopy_32f_C1C3R ippicviCopy_32f_C1C3R
#define ippiCopy_32f_C1C4R ippicviCopy_32f_C1C4R
#define ippiCopy_32f_C1MR ippicviCopy_32f_C1MR
#define ippiCopy_32f_C1R ippicviCopy_32f_C1R
#define ippiCopy_32f_C3AC4R ippicviCopy_32f_C3AC4R
#define ippiCopy_32f_C3C1R ippicviCopy_32f_C3C1R
#define ippiCopy_32f_C3CR ippicviCopy_32f_C3CR
#define ippiCopy_32f_C3MR ippicviCopy_32f_C3MR
#define ippiCopy_32f_C3P3R ippicviCopy_32f_C3P3R
#define ippiCopy_32f_C4C1R ippicviCopy_32f_C4C1R
#define ippiCopy_32f_C4CR ippicviCopy_32f_C4CR
#define ippiCopy_32f_C4MR ippicviCopy_32f_C4MR
#define ippiCopy_32f_C4P4R ippicviCopy_32f_C4P4R
#define ippiCopy_32f_P3C3R ippicviCopy_32f_P3C3R
#define ippiCopy_32f_P4C4R ippicviCopy_32f_P4C4R
#define ippiCopy_32s_C1C3R ippicviCopy_32s_C1C3R
#define ippiCopy_32s_C1C4R ippicviCopy_32s_C1C4R
#define ippiCopy_32s_C1MR ippicviCopy_32s_C1MR
#define ippiCopy_32s_C1R ippicviCopy_32s_C1R
#define ippiCopy_32s_C3C1R ippicviCopy_32s_C3C1R
#define ippiCopy_32s_C3CR ippicviCopy_32s_C3CR
#define ippiCopy_32s_C3MR ippicviCopy_32s_C3MR
#define ippiCopy_32s_C3P3R ippicviCopy_32s_C3P3R
#define ippiCopy_32s_C4C1R ippicviCopy_32s_C4C1R
#define ippiCopy_32s_C4CR ippicviCopy_32s_C4CR
#define ippiCopy_32s_C4MR ippicviCopy_32s_C4MR
#define ippiCopy_32s_C4P4R ippicviCopy_32s_C4P4R
#define ippiCopy_32s_P3C3R ippicviCopy_32s_P3C3R
#define ippiCopy_32s_P4C4R ippicviCopy_32s_P4C4R
#define ippiCopy_8u_AC4C3R ippicviCopy_8u_AC4C3R
#define ippiCopy_8u_C1C3R ippicviCopy_8u_C1C3R
#define ippiCopy_8u_C1C4R ippicviCopy_8u_C1C4R
#define ippiCopy_8u_C1MR ippicviCopy_8u_C1MR
#define ippiCopy_8u_C1R_L ippicviCopy_8u_C1R_L
#define ippiCopy_8u_C3AC4R ippicviCopy_8u_C3AC4R
#define ippiCopy_8u_C3C1R ippicviCopy_8u_C3C1R
#define ippiCopy_8u_C3CR ippicviCopy_8u_C3CR
#define ippiCopy_8u_C3MR ippicviCopy_8u_C3MR
#define ippiCopy_8u_C3P3R ippicviCopy_8u_C3P3R
#define ippiCopy_8u_C4C1R ippicviCopy_8u_C4C1R
#define ippiCopy_8u_C4CR ippicviCopy_8u_C4CR
#define ippiCopy_8u_C4MR ippicviCopy_8u_C4MR
#define ippiCopy_8u_C4P4R ippicviCopy_8u_C4P4R
#define ippiCopy_8u_P3C3R ippicviCopy_8u_P3C3R
#define ippiCopy_8u_P4C4R ippicviCopy_8u_P4C4R
#define ippiCountInRange_32f_C1R ippicviCountInRange_32f_C1R
#define ippiCountInRange_8u_C1R ippicviCountInRange_8u_C1R
#define ippiCrossCorrNormGetBufferSize ippicviCrossCorrNormGetBufferSize
#define ippiCrossCorrNorm_32f_C1R ippicviCrossCorrNorm_32f_C1R
#define ippiCrossCorrNorm_8u32f_C1R ippicviCrossCorrNorm_8u32f_C1R
#define ippiDCTFwdGetSize_32f ippicviDCTFwdGetSize_32f
#define ippiDCTFwdInit_32f ippicviDCTFwdInit_32f
#define ippiDCTFwd_32f_C1R ippicviDCTFwd_32f_C1R
#define ippiDCTInvGetSize_32f ippicviDCTInvGetSize_32f
#define ippiDCTInvInit_32f ippicviDCTInvInit_32f
#define ippiDCTInv_32f_C1R ippicviDCTInv_32f_C1R
#define ippiDFTFwd_CToC_32fc_C1R ippicviDFTFwd_CToC_32fc_C1R
#define ippiDFTFwd_RToPack_32f_C1R ippicviDFTFwd_RToPack_32f_C1R
#define ippiDFTGetSize_C_32fc ippicviDFTGetSize_C_32fc
#define ippiDFTGetSize_R_32f ippicviDFTGetSize_R_32f
#define ippiDFTInit_C_32fc ippicviDFTInit_C_32fc
#define ippiDFTInit_R_32f ippicviDFTInit_R_32f
#define ippiDFTInv_CToC_32fc_C1R ippicviDFTInv_CToC_32fc_C1R
#define ippiDFTInv_PackToR_32f_C1R ippicviDFTInv_PackToR_32f_C1R
#define ippiDilateGetBufferSize_L ippicviDilateGetBufferSize_L
#define ippiDilateGetSpecSize_L ippicviDilateGetSpecSize_L
#define ippiDilateInit_L ippicviDilateInit_L
#define ippiDilate_16s_C1R_L ippicviDilate_16s_C1R_L
#define ippiDilate_16u_C1R_L ippicviDilate_16u_C1R_L
#define ippiDilate_1u_C1R_L ippicviDilate_1u_C1R_L
#define ippiDilate_32f_C1R_L ippicviDilate_32f_C1R_L
#define ippiDilate_32f_C3R_L ippicviDilate_32f_C3R_L
#define ippiDilate_32f_C4R_L ippicviDilate_32f_C4R_L
#define ippiDilate_8u_C1R_L ippicviDilate_8u_C1R_L
#define ippiDilate_8u_C3R_L ippicviDilate_8u_C3R_L
#define ippiDilate_8u_C4R_L ippicviDilate_8u_C4R_L
#define ippiDistanceTransform_3x3_8u32f_C1R ippicviDistanceTransform_3x3_8u32f_C1R
#define ippiDistanceTransform_3x3_8u_C1R ippicviDistanceTransform_3x3_8u_C1R
#define ippiDistanceTransform_5x5_8u32f_C1R ippicviDistanceTransform_5x5_8u32f_C1R
#define ippiDotProd_16s64f_C1R ippicviDotProd_16s64f_C1R
#define ippiDotProd_16u64f_C1R ippicviDotProd_16u64f_C1R
#define ippiDotProd_32f64f_C1R ippicviDotProd_32f64f_C1R
#define ippiDotProd_32s64f_C1R ippicviDotProd_32s64f_C1R
#define ippiDotProd_8u64f_C1R ippicviDotProd_8u64f_C1R
#define ippiErodeGetBufferSize_L ippicviErodeGetBufferSize_L
#define ippiErodeGetSpecSize_L ippicviErodeGetSpecSize_L
#define ippiErodeInit_L ippicviErodeInit_L
#define ippiErode_16s_C1R_L ippicviErode_16s_C1R_L
#define ippiErode_16u_C1R_L ippicviErode_16u_C1R_L
#define ippiErode_1u_C1R_L ippicviErode_1u_C1R_L
#define ippiErode_32f_C1R_L ippicviErode_32f_C1R_L
#define ippiErode_32f_C3R_L ippicviErode_32f_C3R_L
#define ippiErode_32f_C4R_L ippicviErode_32f_C4R_L
#define ippiErode_8u_C1R_L ippicviErode_8u_C1R_L
#define ippiErode_8u_C3R_L ippicviErode_8u_C3R_L
#define ippiErode_8u_C4R_L ippicviErode_8u_C4R_L
#define ippiFilterBilateralBorderGetBufferSize ippicviFilterBilateralBorderGetBufferSize
#define ippiFilterBilateralBorderGetBufferSize_L ippicviFilterBilateralBorderGetBufferSize_L
#define ippiFilterBilateralBorderInit ippicviFilterBilateralBorderInit
#define ippiFilterBilateralBorderInit_L ippicviFilterBilateralBorderInit_L
#define ippiFilterBilateralBorder_32f_C1R ippicviFilterBilateralBorder_32f_C1R
#define ippiFilterBilateralBorder_32f_C3R ippicviFilterBilateralBorder_32f_C3R
#define ippiFilterBilateralBorder_8u_C1R ippicviFilterBilateralBorder_8u_C1R
#define ippiFilterBilateralBorder_8u_C1R_L ippicviFilterBilateralBorder_8u_C1R_L
#define ippiFilterBilateralBorder_8u_C3R ippicviFilterBilateralBorder_8u_C3R
#define ippiFilterBilateralBorder_8u_C3R_L ippicviFilterBilateralBorder_8u_C3R_L
#define ippiFilterBorderGetSize ippicviFilterBorderGetSize
#define ippiFilterBorderInit_16s ippicviFilterBorderInit_16s
#define ippiFilterBorderInit_32f ippicviFilterBorderInit_32f
#define ippiFilterBorderSetMode ippicviFilterBorderSetMode
#define ippiFilterBorder_16s_C1R ippicviFilterBorder_16s_C1R
#define ippiFilterBorder_16s_C3R ippicviFilterBorder_16s_C3R
#define ippiFilterBorder_16s_C4R ippicviFilterBorder_16s_C4R
#define ippiFilterBorder_16u_C1R ippicviFilterBorder_16u_C1R
#define ippiFilterBorder_16u_C3R ippicviFilterBorder_16u_C3R
#define ippiFilterBorder_16u_C4R ippicviFilterBorder_16u_C4R
#define ippiFilterBorder_32f_C1R ippicviFilterBorder_32f_C1R
#define ippiFilterBorder_32f_C3R ippicviFilterBorder_32f_C3R
#define ippiFilterBorder_32f_C4R ippicviFilterBorder_32f_C4R
#define ippiFilterBorder_8u_C1R ippicviFilterBorder_8u_C1R
#define ippiFilterBorder_8u_C3R ippicviFilterBorder_8u_C3R
#define ippiFilterBorder_8u_C4R ippicviFilterBorder_8u_C4R
#define ippiFilterBoxBorderGetBufferSize ippicviFilterBoxBorderGetBufferSize
#define ippiFilterBoxBorder_16s_C1R ippicviFilterBoxBorder_16s_C1R
#define ippiFilterBoxBorder_16s_C3R ippicviFilterBoxBorder_16s_C3R
#define ippiFilterBoxBorder_16s_C4R ippicviFilterBoxBorder_16s_C4R
#define ippiFilterBoxBorder_16u_C1R ippicviFilterBoxBorder_16u_C1R
#define ippiFilterBoxBorder_16u_C3R ippicviFilterBoxBorder_16u_C3R
#define ippiFilterBoxBorder_16u_C4R ippicviFilterBoxBorder_16u_C4R
#define ippiFilterBoxBorder_32f_C1R ippicviFilterBoxBorder_32f_C1R
#define ippiFilterBoxBorder_32f_C3R ippicviFilterBoxBorder_32f_C3R
#define ippiFilterBoxBorder_32f_C4R ippicviFilterBoxBorder_32f_C4R
#define ippiFilterBoxBorder_8u_C1R ippicviFilterBoxBorder_8u_C1R
#define ippiFilterBoxBorder_8u_C3R ippicviFilterBoxBorder_8u_C3R
#define ippiFilterBoxBorder_8u_C4R ippicviFilterBoxBorder_8u_C4R
#define ippiFilterGaussianGetBufferSize_L ippicviFilterGaussianGetBufferSize_L
#define ippiFilterGaussianGetSpecSize_L ippicviFilterGaussianGetSpecSize_L
#define ippiFilterGaussianInit_L ippicviFilterGaussianInit_L
#define ippiFilterGaussian_16s_C1R_L ippicviFilterGaussian_16s_C1R_L
#define ippiFilterGaussian_16s_C3R_L ippicviFilterGaussian_16s_C3R_L
#define ippiFilterGaussian_16u_C1R_L ippicviFilterGaussian_16u_C1R_L
#define ippiFilterGaussian_16u_C3R_L ippicviFilterGaussian_16u_C3R_L
#define ippiFilterGaussian_32f_C1R_L ippicviFilterGaussian_32f_C1R_L
#define ippiFilterGaussian_32f_C3R_L ippicviFilterGaussian_32f_C3R_L
#define ippiFilterGaussian_8u_C1R_L ippicviFilterGaussian_8u_C1R_L
#define ippiFilterGaussian_8u_C3R_L ippicviFilterGaussian_8u_C3R_L
#define ippiFilterLaplacianBorder_32f_C1R ippicviFilterLaplacianBorder_32f_C1R
#define ippiFilterLaplacianBorder_8u16s_C1R ippicviFilterLaplacianBorder_8u16s_C1R
#define ippiFilterLaplacianGetBufferSize_32f_C1R ippicviFilterLaplacianGetBufferSize_32f_C1R
#define ippiFilterLaplacianGetBufferSize_8u16s_C1R ippicviFilterLaplacianGetBufferSize_8u16s_C1R
#define ippiFilterMaxBorderGetBufferSize ippicviFilterMaxBorderGetBufferSize
#define ippiFilterMaxBorder_16s_C1R ippicviFilterMaxBorder_16s_C1R
#define ippiFilterMaxBorder_16s_C3R ippicviFilterMaxBorder_16s_C3R
#define ippiFilterMaxBorder_16s_C4R ippicviFilterMaxBorder_16s_C4R
#define ippiFilterMaxBorder_16u_C1R ippicviFilterMaxBorder_16u_C1R
#define ippiFilterMaxBorder_16u_C3R ippicviFilterMaxBorder_16u_C3R
#define ippiFilterMaxBorder_16u_C4R ippicviFilterMaxBorder_16u_C4R
#define ippiFilterMaxBorder_32f_C1R ippicviFilterMaxBorder_32f_C1R
#define ippiFilterMaxBorder_32f_C3R ippicviFilterMaxBorder_32f_C3R
#define ippiFilterMaxBorder_32f_C4R ippicviFilterMaxBorder_32f_C4R
#define ippiFilterMaxBorder_8u_C1R ippicviFilterMaxBorder_8u_C1R
#define ippiFilterMaxBorder_8u_C3R ippicviFilterMaxBorder_8u_C3R
#define ippiFilterMaxBorder_8u_C4R ippicviFilterMaxBorder_8u_C4R
#define ippiFilterMedianBorderGetBufferSize ippicviFilterMedianBorderGetBufferSize
#define ippiFilterMedianBorder_16s_C1R ippicviFilterMedianBorder_16s_C1R
#define ippiFilterMedianBorder_16s_C3R ippicviFilterMedianBorder_16s_C3R
#define ippiFilterMedianBorder_16s_C4R ippicviFilterMedianBorder_16s_C4R
#define ippiFilterMedianBorder_16u_C1R ippicviFilterMedianBorder_16u_C1R
#define ippiFilterMedianBorder_16u_C3R ippicviFilterMedianBorder_16u_C3R
#define ippiFilterMedianBorder_16u_C4R ippicviFilterMedianBorder_16u_C4R
#define ippiFilterMedianBorder_32f_C1R ippicviFilterMedianBorder_32f_C1R
#define ippiFilterMedianBorder_8u_C1R ippicviFilterMedianBorder_8u_C1R
#define ippiFilterMedianBorder_8u_C3R ippicviFilterMedianBorder_8u_C3R
#define ippiFilterMedianBorder_8u_C4R ippicviFilterMedianBorder_8u_C4R
#define ippiFilterMinBorderGetBufferSize ippicviFilterMinBorderGetBufferSize
#define ippiFilterMinBorder_16s_C1R ippicviFilterMinBorder_16s_C1R
#define ippiFilterMinBorder_16s_C3R ippicviFilterMinBorder_16s_C3R
#define ippiFilterMinBorder_16s_C4R ippicviFilterMinBorder_16s_C4R
#define ippiFilterMinBorder_16u_C1R ippicviFilterMinBorder_16u_C1R
#define ippiFilterMinBorder_16u_C3R ippicviFilterMinBorder_16u_C3R
#define ippiFilterMinBorder_16u_C4R ippicviFilterMinBorder_16u_C4R
#define ippiFilterMinBorder_32f_C1R ippicviFilterMinBorder_32f_C1R
#define ippiFilterMinBorder_32f_C3R ippicviFilterMinBorder_32f_C3R
#define ippiFilterMinBorder_32f_C4R ippicviFilterMinBorder_32f_C4R
#define ippiFilterMinBorder_8u_C1R ippicviFilterMinBorder_8u_C1R
#define ippiFilterMinBorder_8u_C3R ippicviFilterMinBorder_8u_C3R
#define ippiFilterMinBorder_8u_C4R ippicviFilterMinBorder_8u_C4R
#define ippiFilterRowBorderPipelineGetBufferSize_32f_C1R ippicviFilterRowBorderPipelineGetBufferSize_32f_C1R
#define ippiFilterRowBorderPipelineGetBufferSize_32f_C3R ippicviFilterRowBorderPipelineGetBufferSize_32f_C3R
#define ippiFilterRowBorderPipeline_32f_C1R ippicviFilterRowBorderPipeline_32f_C1R
#define ippiFilterRowBorderPipeline_32f_C3R ippicviFilterRowBorderPipeline_32f_C3R
#define ippiFilterScharrHorizMaskBorderGetBufferSize ippicviFilterScharrHorizMaskBorderGetBufferSize
#define ippiFilterScharrHorizMaskBorder_16s_C1R ippicviFilterScharrHorizMaskBorder_16s_C1R
#define ippiFilterScharrHorizMaskBorder_32f_C1R ippicviFilterScharrHorizMaskBorder_32f_C1R
#define ippiFilterScharrHorizMaskBorder_8u16s_C1R ippicviFilterScharrHorizMaskBorder_8u16s_C1R
#define ippiFilterScharrVertMaskBorderGetBufferSize ippicviFilterScharrVertMaskBorderGetBufferSize
#define ippiFilterScharrVertMaskBorder_16s_C1R ippicviFilterScharrVertMaskBorder_16s_C1R
#define ippiFilterScharrVertMaskBorder_32f_C1R ippicviFilterScharrVertMaskBorder_32f_C1R
#define ippiFilterScharrVertMaskBorder_8u16s_C1R ippicviFilterScharrVertMaskBorder_8u16s_C1R
#define ippiFilterSobelHorizBorderGetBufferSize ippicviFilterSobelHorizBorderGetBufferSize
#define ippiFilterSobelHorizBorder_16s_C1R ippicviFilterSobelHorizBorder_16s_C1R
#define ippiFilterSobelHorizBorder_32f_C1R ippicviFilterSobelHorizBorder_32f_C1R
#define ippiFilterSobelHorizBorder_8u16s_C1R ippicviFilterSobelHorizBorder_8u16s_C1R
#define ippiFilterSobelHorizSecondBorderGetBufferSize ippicviFilterSobelHorizSecondBorderGetBufferSize
#define ippiFilterSobelHorizSecondBorder_32f_C1R ippicviFilterSobelHorizSecondBorder_32f_C1R
#define ippiFilterSobelHorizSecondBorder_8u16s_C1R ippicviFilterSobelHorizSecondBorder_8u16s_C1R
#define ippiFilterSobelNegVertBorderGetBufferSize ippicviFilterSobelNegVertBorderGetBufferSize
#define ippiFilterSobelNegVertBorder_32f_C1R ippicviFilterSobelNegVertBorder_32f_C1R
#define ippiFilterSobelNegVertBorder_8u16s_C1R ippicviFilterSobelNegVertBorder_8u16s_C1R
#define ippiFilterSobelVertBorderGetBufferSize ippicviFilterSobelVertBorderGetBufferSize
#define ippiFilterSobelVertBorder_16s_C1R ippicviFilterSobelVertBorder_16s_C1R
#define ippiFilterSobelVertBorder_32f_C1R ippicviFilterSobelVertBorder_32f_C1R
#define ippiFilterSobelVertBorder_8u16s_C1R ippicviFilterSobelVertBorder_8u16s_C1R
#define ippiFilterSobelVertSecondBorderGetBufferSize ippicviFilterSobelVertSecondBorderGetBufferSize
#define ippiFilterSobelVertSecondBorder_32f_C1R ippicviFilterSobelVertSecondBorder_32f_C1R
#define ippiFilterSobelVertSecondBorder_8u16s_C1R ippicviFilterSobelVertSecondBorder_8u16s_C1R
#define ippiGetAffineBound ippicviGetAffineBound
#define ippiGetCentralMoment_64f ippicviGetCentralMoment_64f
#define ippiGetHaarClassifierSize_32f ippicviGetHaarClassifierSize_32f
#define ippiGetHuMoments_64f ippicviGetHuMoments_64f
#define ippiGetLibVersion ippicviGetLibVersion
#define ippiGetNormalizedCentralMoment_64f ippicviGetNormalizedCentralMoment_64f
#define ippiGetNormalizedSpatialMoment_64f ippicviGetNormalizedSpatialMoment_64f
#define ippiGetPyramidDownROI ippicviGetPyramidDownROI
#define ippiGetPyramidUpROI ippicviGetPyramidUpROI
#define ippiGetRotateTransform ippicviGetRotateTransform
#define ippiGetSpatialMoment_64f ippicviGetSpatialMoment_64f
#define ippiGrayToRGB_16u_C1C3R ippicviGrayToRGB_16u_C1C3R
#define ippiGrayToRGB_16u_C1C4R ippicviGrayToRGB_16u_C1C4R
#define ippiGrayToRGB_32f_C1C3R ippicviGrayToRGB_32f_C1C3R
#define ippiGrayToRGB_32f_C1C4R ippicviGrayToRGB_32f_C1C4R
#define ippiGrayToRGB_8u_C1C3R ippicviGrayToRGB_8u_C1C3R
#define ippiGrayToRGB_8u_C1C4R ippicviGrayToRGB_8u_C1C4R
#define ippiHLSToRGB_16u_C3R ippicviHLSToRGB_16u_C3R
#define ippiHLSToRGB_32f_C3R ippicviHLSToRGB_32f_C3R
#define ippiHLSToRGB_8u_C3R ippicviHLSToRGB_8u_C3R
#define ippiHSVToRGB_16u_C3R ippicviHSVToRGB_16u_C3R
#define ippiHSVToRGB_8u_C3R ippicviHSVToRGB_8u_C3R
#define ippiHaarClassifierGetSize ippicviHaarClassifierGetSize
#define ippiHaarClassifierInit_32f ippicviHaarClassifierInit_32f
#define ippiHarrisCornerGetBufferSize ippicviHarrisCornerGetBufferSize
#define ippiHarrisCorner_32f_C1R ippicviHarrisCorner_32f_C1R
#define ippiHarrisCorner_8u32f_C1R ippicviHarrisCorner_8u32f_C1R
#define ippiHistogramGetBufferSize ippicviHistogramGetBufferSize
#define ippiHistogramGetLevels ippicviHistogramGetLevels
#define ippiHistogramInit ippicviHistogramInit
#define ippiHistogramUniformInit ippicviHistogramUniformInit
#define ippiHistogram_16s_C1R ippicviHistogram_16s_C1R
#define ippiHistogram_16u_C1R ippicviHistogram_16u_C1R
#define ippiHistogram_32f_C1R ippicviHistogram_32f_C1R
#define ippiHistogram_8u_C1R ippicviHistogram_8u_C1R
#define ippiHoughLineGetSize_8u_C1R ippicviHoughLineGetSize_8u_C1R
#define ippiHoughLine_Region_8u32f_C1R ippicviHoughLine_Region_8u32f_C1R
#define ippiHoughProbLineGetSize_8u_C1R ippicviHoughProbLineGetSize_8u_C1R
#define ippiHoughProbLineInit_8u32f_C1R ippicviHoughProbLineInit_8u32f_C1R
#define ippiHoughProbLine_8u32f_C1R ippicviHoughProbLine_8u32f_C1R
#define ippiIntegral_32f_C1R ippicviIntegral_32f_C1R
#define ippiIntegral_8u32f_C1R ippicviIntegral_8u32f_C1R
#define ippiIntegral_8u32s_C1R ippicviIntegral_8u32s_C1R
#define ippiLUTPalette_8u32u_C1R ippicviLUTPalette_8u32u_C1R
#define ippiLUTPalette_8u_C1R ippicviLUTPalette_8u_C1R
#define ippiLUTPalette_8u_C3R ippicviLUTPalette_8u_C3R
#define ippiLUTPalette_8u_C4R ippicviLUTPalette_8u_C4R
#define ippiLUVToRGB_16s_C3R ippicviLUVToRGB_16s_C3R
#define ippiLUVToRGB_16u_C3R ippicviLUVToRGB_16u_C3R
#define ippiLUVToRGB_32f_C3R ippicviLUVToRGB_32f_C3R
#define ippiLUVToRGB_8u_C3R ippicviLUVToRGB_8u_C3R
#define ippiLabToBGR_8u_C3R ippicviLabToBGR_8u_C3R
#define ippiMarkSpecklesGetBufferSize ippicviMarkSpecklesGetBufferSize
#define ippiMarkSpeckles_16s_C1IR ippicviMarkSpeckles_16s_C1IR
#define ippiMarkSpeckles_8u_C1IR ippicviMarkSpeckles_8u_C1IR
#define ippiMaxIndx_16s_C1R ippicviMaxIndx_16s_C1R
#define ippiMaxIndx_16u_C1R ippicviMaxIndx_16u_C1R
#define ippiMaxIndx_32f_C1R ippicviMaxIndx_32f_C1R
#define ippiMaxIndx_8u_C1R ippicviMaxIndx_8u_C1R
#define ippiMax_16s_C1R ippicviMax_16s_C1R
#define ippiMax_16u_C1R ippicviMax_16u_C1R
#define ippiMax_32f_C1R ippicviMax_32f_C1R
#define ippiMax_8u_C1R ippicviMax_8u_C1R
#define ippiMean_16s_C1R ippicviMean_16s_C1R
#define ippiMean_16s_C3R ippicviMean_16s_C3R
#define ippiMean_16s_C4R ippicviMean_16s_C4R
#define ippiMean_16u_C1MR ippicviMean_16u_C1MR
#define ippiMean_16u_C1R ippicviMean_16u_C1R
#define ippiMean_16u_C3CMR ippicviMean_16u_C3CMR
#define ippiMean_16u_C3R ippicviMean_16u_C3R
#define ippiMean_16u_C4R ippicviMean_16u_C4R
#define ippiMean_32f_C1MR ippicviMean_32f_C1MR
#define ippiMean_32f_C1R ippicviMean_32f_C1R
#define ippiMean_32f_C3CMR ippicviMean_32f_C3CMR
#define ippiMean_32f_C3R ippicviMean_32f_C3R
#define ippiMean_32f_C4R ippicviMean_32f_C4R
#define ippiMean_8u_C1MR ippicviMean_8u_C1MR
#define ippiMean_8u_C1R ippicviMean_8u_C1R
#define ippiMean_8u_C3CMR ippicviMean_8u_C3CMR
#define ippiMean_8u_C3R ippicviMean_8u_C3R
#define ippiMean_8u_C4R ippicviMean_8u_C4R
#define ippiMean_StdDev_16u_C1MR ippicviMean_StdDev_16u_C1MR
#define ippiMean_StdDev_16u_C1R ippicviMean_StdDev_16u_C1R
#define ippiMean_StdDev_16u_C3CMR ippicviMean_StdDev_16u_C3CMR
#define ippiMean_StdDev_16u_C3CR ippicviMean_StdDev_16u_C3CR
#define ippiMean_StdDev_32f_C1MR ippicviMean_StdDev_32f_C1MR
#define ippiMean_StdDev_32f_C1R ippicviMean_StdDev_32f_C1R
#define ippiMean_StdDev_32f_C3CMR ippicviMean_StdDev_32f_C3CMR
#define ippiMean_StdDev_32f_C3CR ippicviMean_StdDev_32f_C3CR
#define ippiMean_StdDev_8u_C1MR ippicviMean_StdDev_8u_C1MR
#define ippiMean_StdDev_8u_C1R ippicviMean_StdDev_8u_C1R
#define ippiMean_StdDev_8u_C3CMR ippicviMean_StdDev_8u_C3CMR
#define ippiMean_StdDev_8u_C3CR ippicviMean_StdDev_8u_C3CR
#define ippiMinEigenValGetBufferSize_32f_C1R ippicviMinEigenValGetBufferSize_32f_C1R
#define ippiMinEigenValGetBufferSize_8u32f_C1R ippicviMinEigenValGetBufferSize_8u32f_C1R
#define ippiMinEigenVal_32f_C1R ippicviMinEigenVal_32f_C1R
#define ippiMinEigenVal_8u32f_C1R ippicviMinEigenVal_8u32f_C1R
#define ippiMinIndx_16s_C1R ippicviMinIndx_16s_C1R
#define ippiMinIndx_16u_C1R ippicviMinIndx_16u_C1R
#define ippiMinIndx_32f_C1R ippicviMinIndx_32f_C1R
#define ippiMinIndx_8u_C1R ippicviMinIndx_8u_C1R
#define ippiMinMaxIndx_16u_C1MR ippicviMinMaxIndx_16u_C1MR
#define ippiMinMaxIndx_16u_C1R ippicviMinMaxIndx_16u_C1R
#define ippiMinMaxIndx_32f_C1MR ippicviMinMaxIndx_32f_C1MR
#define ippiMinMaxIndx_32f_C1R ippicviMinMaxIndx_32f_C1R
#define ippiMinMaxIndx_8u_C1MR ippicviMinMaxIndx_8u_C1MR
#define ippiMinMaxIndx_8u_C1R ippicviMinMaxIndx_8u_C1R
#define ippiMinMax_16s_C1R ippicviMinMax_16s_C1R
#define ippiMinMax_16u_C1R ippicviMinMax_16u_C1R
#define ippiMinMax_32f_C1R ippicviMinMax_32f_C1R
#define ippiMinMax_8u_C1R ippicviMinMax_8u_C1R
#define ippiMin_16s_C1R ippicviMin_16s_C1R
#define ippiMin_16u_C1R ippicviMin_16u_C1R
#define ippiMin_32f_C1R ippicviMin_32f_C1R
#define ippiMin_8u_C1R ippicviMin_8u_C1R
#define ippiMirror_16s_C1IR ippicviMirror_16s_C1IR
#define ippiMirror_16s_C1R ippicviMirror_16s_C1R
#define ippiMirror_16s_C3IR ippicviMirror_16s_C3IR
#define ippiMirror_16s_C3R ippicviMirror_16s_C3R
#define ippiMirror_16s_C4IR ippicviMirror_16s_C4IR
#define ippiMirror_16s_C4R ippicviMirror_16s_C4R
#define ippiMirror_16u_C1IR ippicviMirror_16u_C1IR
#define ippiMirror_16u_C1R ippicviMirror_16u_C1R
#define ippiMirror_16u_C3IR ippicviMirror_16u_C3IR
#define ippiMirror_16u_C3R ippicviMirror_16u_C3R
#define ippiMirror_16u_C4IR ippicviMirror_16u_C4IR
#define ippiMirror_16u_C4R ippicviMirror_16u_C4R
#define ippiMirror_32f_C1IR ippicviMirror_32f_C1IR
#define ippiMirror_32f_C1R ippicviMirror_32f_C1R
#define ippiMirror_32f_C3IR ippicviMirror_32f_C3IR
#define ippiMirror_32f_C3R ippicviMirror_32f_C3R
#define ippiMirror_32f_C4IR ippicviMirror_32f_C4IR
#define ippiMirror_32f_C4R ippicviMirror_32f_C4R
#define ippiMirror_32s_C1IR ippicviMirror_32s_C1IR
#define ippiMirror_32s_C1R ippicviMirror_32s_C1R
#define ippiMirror_32s_C3IR ippicviMirror_32s_C3IR
#define ippiMirror_32s_C3R ippicviMirror_32s_C3R
#define ippiMirror_32s_C4IR ippicviMirror_32s_C4IR
#define ippiMirror_32s_C4R ippicviMirror_32s_C4R
#define ippiMirror_8u_C1IR ippicviMirror_8u_C1IR
#define ippiMirror_8u_C1R ippicviMirror_8u_C1R
#define ippiMirror_8u_C3IR ippicviMirror_8u_C3IR
#define ippiMirror_8u_C3R ippicviMirror_8u_C3R
#define ippiMirror_8u_C4IR ippicviMirror_8u_C4IR
#define ippiMirror_8u_C4R ippicviMirror_8u_C4R
#define ippiMomentGetStateSize_64f ippicviMomentGetStateSize_64f
#define ippiMomentInit_64f ippicviMomentInit_64f
#define ippiMoments64f_16u_AC4R ippicviMoments64f_16u_AC4R
#define ippiMoments64f_16u_C1R ippicviMoments64f_16u_C1R
#define ippiMoments64f_16u_C3R ippicviMoments64f_16u_C3R
#define ippiMoments64f_32f_AC4R ippicviMoments64f_32f_AC4R
#define ippiMoments64f_32f_C1R ippicviMoments64f_32f_C1R
#define ippiMoments64f_32f_C3R ippicviMoments64f_32f_C3R
#define ippiMoments64f_8u_AC4R ippicviMoments64f_8u_AC4R
#define ippiMoments64f_8u_C1R ippicviMoments64f_8u_C1R
#define ippiMoments64f_8u_C3R ippicviMoments64f_8u_C3R
#define ippiMorphBlackhat_16s_C1R_L ippicviMorphBlackhat_16s_C1R_L
#define ippiMorphBlackhat_16u_C1R_L ippicviMorphBlackhat_16u_C1R_L
#define ippiMorphBlackhat_1u_C1R_L ippicviMorphBlackhat_1u_C1R_L
#define ippiMorphBlackhat_32f_C1R_L ippicviMorphBlackhat_32f_C1R_L
#define ippiMorphBlackhat_32f_C3R_L ippicviMorphBlackhat_32f_C3R_L
#define ippiMorphBlackhat_32f_C4R_L ippicviMorphBlackhat_32f_C4R_L
#define ippiMorphBlackhat_8u_C1R_L ippicviMorphBlackhat_8u_C1R_L
#define ippiMorphBlackhat_8u_C3R_L ippicviMorphBlackhat_8u_C3R_L
#define ippiMorphBlackhat_8u_C4R_L ippicviMorphBlackhat_8u_C4R_L
#define ippiMorphClose_16s_C1R_L ippicviMorphClose_16s_C1R_L
#define ippiMorphClose_16u_C1R_L ippicviMorphClose_16u_C1R_L
#define ippiMorphClose_1u_C1R_L ippicviMorphClose_1u_C1R_L
#define ippiMorphClose_32f_C1R_L ippicviMorphClose_32f_C1R_L
#define ippiMorphClose_32f_C3R_L ippicviMorphClose_32f_C3R_L
#define ippiMorphClose_32f_C4R_L ippicviMorphClose_32f_C4R_L
#define ippiMorphClose_8u_C1R_L ippicviMorphClose_8u_C1R_L
#define ippiMorphClose_8u_C3R_L ippicviMorphClose_8u_C3R_L
#define ippiMorphClose_8u_C4R_L ippicviMorphClose_8u_C4R_L
#define ippiMorphGetBufferSize_L ippicviMorphGetBufferSize_L
#define ippiMorphGetSpecSize_L ippicviMorphGetSpecSize_L
#define ippiMorphGradient_16s_C1R_L ippicviMorphGradient_16s_C1R_L
#define ippiMorphGradient_16u_C1R_L ippicviMorphGradient_16u_C1R_L
#define ippiMorphGradient_1u_C1R_L ippicviMorphGradient_1u_C1R_L
#define ippiMorphGradient_32f_C1R_L ippicviMorphGradient_32f_C1R_L
#define ippiMorphGradient_32f_C3R_L ippicviMorphGradient_32f_C3R_L
#define ippiMorphGradient_32f_C4R_L ippicviMorphGradient_32f_C4R_L
#define ippiMorphGradient_8u_C1R_L ippicviMorphGradient_8u_C1R_L
#define ippiMorphGradient_8u_C3R_L ippicviMorphGradient_8u_C3R_L
#define ippiMorphGradient_8u_C4R_L ippicviMorphGradient_8u_C4R_L
#define ippiMorphInit_L ippicviMorphInit_L
#define ippiMorphOpen_16s_C1R_L ippicviMorphOpen_16s_C1R_L
#define ippiMorphOpen_16u_C1R_L ippicviMorphOpen_16u_C1R_L
#define ippiMorphOpen_1u_C1R_L ippicviMorphOpen_1u_C1R_L
#define ippiMorphOpen_32f_C1R_L ippicviMorphOpen_32f_C1R_L
#define ippiMorphOpen_32f_C3R_L ippicviMorphOpen_32f_C3R_L
#define ippiMorphOpen_32f_C4R_L ippicviMorphOpen_32f_C4R_L
#define ippiMorphOpen_8u_C1R_L ippicviMorphOpen_8u_C1R_L
#define ippiMorphOpen_8u_C3R_L ippicviMorphOpen_8u_C3R_L
#define ippiMorphOpen_8u_C4R_L ippicviMorphOpen_8u_C4R_L
#define ippiMorphSetMode_L ippicviMorphSetMode_L
#define ippiMorphTophat_16s_C1R_L ippicviMorphTophat_16s_C1R_L
#define ippiMorphTophat_16u_C1R_L ippicviMorphTophat_16u_C1R_L
#define ippiMorphTophat_1u_C1R_L ippicviMorphTophat_1u_C1R_L
#define ippiMorphTophat_32f_C1R_L ippicviMorphTophat_32f_C1R_L
#define ippiMorphTophat_32f_C3R_L ippicviMorphTophat_32f_C3R_L
#define ippiMorphTophat_32f_C4R_L ippicviMorphTophat_32f_C4R_L
#define ippiMorphTophat_8u_C1R_L ippicviMorphTophat_8u_C1R_L
#define ippiMorphTophat_8u_C3R_L ippicviMorphTophat_8u_C3R_L
#define ippiMorphTophat_8u_C4R_L ippicviMorphTophat_8u_C4R_L
#define ippiMulC_16s_C1IRSfs ippicviMulC_16s_C1IRSfs
#define ippiMulC_32f_C1IR ippicviMulC_32f_C1IR
#define ippiMulC_32f_C1R ippicviMulC_32f_C1R
#define ippiMul_16s_C1RSfs ippicviMul_16s_C1RSfs
#define ippiMul_16u_C1RSfs ippicviMul_16u_C1RSfs
#define ippiMul_32f_C1R ippicviMul_32f_C1R
#define ippiMul_8u_C1RSfs ippicviMul_8u_C1RSfs
#define ippiNormDiff_Inf_16s_C1R ippicviNormDiff_Inf_16s_C1R
#define ippiNormDiff_Inf_16s_C3R ippicviNormDiff_Inf_16s_C3R
#define ippiNormDiff_Inf_16s_C4R ippicviNormDiff_Inf_16s_C4R
#define ippiNormDiff_Inf_16u_C1MR ippicviNormDiff_Inf_16u_C1MR
#define ippiNormDiff_Inf_16u_C1R ippicviNormDiff_Inf_16u_C1R
#define ippiNormDiff_Inf_16u_C3CMR ippicviNormDiff_Inf_16u_C3CMR
#define ippiNormDiff_Inf_16u_C3R ippicviNormDiff_Inf_16u_C3R
#define ippiNormDiff_Inf_16u_C4R ippicviNormDiff_Inf_16u_C4R
#define ippiNormDiff_Inf_32f_C1MR ippicviNormDiff_Inf_32f_C1MR
#define ippiNormDiff_Inf_32f_C1R ippicviNormDiff_Inf_32f_C1R
#define ippiNormDiff_Inf_32f_C3CMR ippicviNormDiff_Inf_32f_C3CMR
#define ippiNormDiff_Inf_32f_C3R ippicviNormDiff_Inf_32f_C3R
#define ippiNormDiff_Inf_32f_C4R ippicviNormDiff_Inf_32f_C4R
#define ippiNormDiff_Inf_8u_C1MR ippicviNormDiff_Inf_8u_C1MR
#define ippiNormDiff_Inf_8u_C1R ippicviNormDiff_Inf_8u_C1R
#define ippiNormDiff_Inf_8u_C3CMR ippicviNormDiff_Inf_8u_C3CMR
#define ippiNormDiff_Inf_8u_C3R ippicviNormDiff_Inf_8u_C3R
#define ippiNormDiff_Inf_8u_C4R ippicviNormDiff_Inf_8u_C4R
#define ippiNormDiff_L1_16s_C1R ippicviNormDiff_L1_16s_C1R
#define ippiNormDiff_L1_16s_C3R ippicviNormDiff_L1_16s_C3R
#define ippiNormDiff_L1_16s_C4R ippicviNormDiff_L1_16s_C4R
#define ippiNormDiff_L1_16u_C1MR ippicviNormDiff_L1_16u_C1MR
#define ippiNormDiff_L1_16u_C1R ippicviNormDiff_L1_16u_C1R
#define ippiNormDiff_L1_16u_C3CMR ippicviNormDiff_L1_16u_C3CMR
#define ippiNormDiff_L1_16u_C3R ippicviNormDiff_L1_16u_C3R
#define ippiNormDiff_L1_16u_C4R ippicviNormDiff_L1_16u_C4R
#define ippiNormDiff_L1_32f_C1MR ippicviNormDiff_L1_32f_C1MR
#define ippiNormDiff_L1_32f_C1R ippicviNormDiff_L1_32f_C1R
#define ippiNormDiff_L1_32f_C3CMR ippicviNormDiff_L1_32f_C3CMR
#define ippiNormDiff_L1_32f_C3R ippicviNormDiff_L1_32f_C3R
#define ippiNormDiff_L1_32f_C4R ippicviNormDiff_L1_32f_C4R
#define ippiNormDiff_L1_8u_C1MR ippicviNormDiff_L1_8u_C1MR
#define ippiNormDiff_L1_8u_C1R ippicviNormDiff_L1_8u_C1R
#define ippiNormDiff_L1_8u_C3CMR ippicviNormDiff_L1_8u_C3CMR
#define ippiNormDiff_L1_8u_C3R ippicviNormDiff_L1_8u_C3R
#define ippiNormDiff_L1_8u_C4R ippicviNormDiff_L1_8u_C4R
#define ippiNormDiff_L2_16s_C1R ippicviNormDiff_L2_16s_C1R
#define ippiNormDiff_L2_16s_C3R ippicviNormDiff_L2_16s_C3R
#define ippiNormDiff_L2_16s_C4R ippicviNormDiff_L2_16s_C4R
#define ippiNormDiff_L2_16u_C1MR ippicviNormDiff_L2_16u_C1MR
#define ippiNormDiff_L2_16u_C1R ippicviNormDiff_L2_16u_C1R
#define ippiNormDiff_L2_16u_C3CMR ippicviNormDiff_L2_16u_C3CMR
#define ippiNormDiff_L2_16u_C3R ippicviNormDiff_L2_16u_C3R
#define ippiNormDiff_L2_16u_C4R ippicviNormDiff_L2_16u_C4R
#define ippiNormDiff_L2_32f_C1MR ippicviNormDiff_L2_32f_C1MR
#define ippiNormDiff_L2_32f_C1R ippicviNormDiff_L2_32f_C1R
#define ippiNormDiff_L2_32f_C3CMR ippicviNormDiff_L2_32f_C3CMR
#define ippiNormDiff_L2_32f_C3R ippicviNormDiff_L2_32f_C3R
#define ippiNormDiff_L2_32f_C4R ippicviNormDiff_L2_32f_C4R
#define ippiNormDiff_L2_8u_C1MR ippicviNormDiff_L2_8u_C1MR
#define ippiNormDiff_L2_8u_C1R ippicviNormDiff_L2_8u_C1R
#define ippiNormDiff_L2_8u_C3CMR ippicviNormDiff_L2_8u_C3CMR
#define ippiNormDiff_L2_8u_C3R ippicviNormDiff_L2_8u_C3R
#define ippiNormDiff_L2_8u_C4R ippicviNormDiff_L2_8u_C4R
#define ippiNormRel_Inf_16s_C1R ippicviNormRel_Inf_16s_C1R
#define ippiNormRel_Inf_16u_C1MR ippicviNormRel_Inf_16u_C1MR
#define ippiNormRel_Inf_16u_C1R ippicviNormRel_Inf_16u_C1R
#define ippiNormRel_Inf_32f_C1MR ippicviNormRel_Inf_32f_C1MR
#define ippiNormRel_Inf_32f_C1R ippicviNormRel_Inf_32f_C1R
#define ippiNormRel_Inf_8u_C1MR ippicviNormRel_Inf_8u_C1MR
#define ippiNormRel_Inf_8u_C1R ippicviNormRel_Inf_8u_C1R
#define ippiNormRel_L1_16s_C1R ippicviNormRel_L1_16s_C1R
#define ippiNormRel_L1_16u_C1MR ippicviNormRel_L1_16u_C1MR
#define ippiNormRel_L1_16u_C1R ippicviNormRel_L1_16u_C1R
#define ippiNormRel_L1_32f_C1MR ippicviNormRel_L1_32f_C1MR
#define ippiNormRel_L1_32f_C1R ippicviNormRel_L1_32f_C1R
#define ippiNormRel_L1_8u_C1MR ippicviNormRel_L1_8u_C1MR
#define ippiNormRel_L1_8u_C1R ippicviNormRel_L1_8u_C1R
#define ippiNormRel_L2_16s_C1R ippicviNormRel_L2_16s_C1R
#define ippiNormRel_L2_16u_C1MR ippicviNormRel_L2_16u_C1MR
#define ippiNormRel_L2_16u_C1R ippicviNormRel_L2_16u_C1R
#define ippiNormRel_L2_32f_C1MR ippicviNormRel_L2_32f_C1MR
#define ippiNormRel_L2_32f_C1R ippicviNormRel_L2_32f_C1R
#define ippiNormRel_L2_8u_C1MR ippicviNormRel_L2_8u_C1MR
#define ippiNormRel_L2_8u_C1R ippicviNormRel_L2_8u_C1R
#define ippiNorm_Inf_16s_C1R ippicviNorm_Inf_16s_C1R
#define ippiNorm_Inf_16s_C3R ippicviNorm_Inf_16s_C3R
#define ippiNorm_Inf_16s_C4R ippicviNorm_Inf_16s_C4R
#define ippiNorm_Inf_16u_C1MR ippicviNorm_Inf_16u_C1MR
#define ippiNorm_Inf_16u_C1R ippicviNorm_Inf_16u_C1R
#define ippiNorm_Inf_16u_C3CMR ippicviNorm_Inf_16u_C3CMR
#define ippiNorm_Inf_16u_C3R ippicviNorm_Inf_16u_C3R
#define ippiNorm_Inf_16u_C4R ippicviNorm_Inf_16u_C4R
#define ippiNorm_Inf_32f_C1MR ippicviNorm_Inf_32f_C1MR
#define ippiNorm_Inf_32f_C1R ippicviNorm_Inf_32f_C1R
#define ippiNorm_Inf_32f_C3CMR ippicviNorm_Inf_32f_C3CMR
#define ippiNorm_Inf_32f_C3R ippicviNorm_Inf_32f_C3R
#define ippiNorm_Inf_32f_C4R ippicviNorm_Inf_32f_C4R
#define ippiNorm_Inf_8u_C1MR ippicviNorm_Inf_8u_C1MR
#define ippiNorm_Inf_8u_C1R ippicviNorm_Inf_8u_C1R
#define ippiNorm_Inf_8u_C3CMR ippicviNorm_Inf_8u_C3CMR
#define ippiNorm_Inf_8u_C3R ippicviNorm_Inf_8u_C3R
#define ippiNorm_Inf_8u_C4R ippicviNorm_Inf_8u_C4R
#define ippiNorm_L1_16s_C1R ippicviNorm_L1_16s_C1R
#define ippiNorm_L1_16s_C3R ippicviNorm_L1_16s_C3R
#define ippiNorm_L1_16s_C4R ippicviNorm_L1_16s_C4R
#define ippiNorm_L1_16u_C1MR ippicviNorm_L1_16u_C1MR
#define ippiNorm_L1_16u_C1R ippicviNorm_L1_16u_C1R
#define ippiNorm_L1_16u_C3CMR ippicviNorm_L1_16u_C3CMR
#define ippiNorm_L1_16u_C3R ippicviNorm_L1_16u_C3R
#define ippiNorm_L1_16u_C4R ippicviNorm_L1_16u_C4R
#define ippiNorm_L1_32f_C1MR ippicviNorm_L1_32f_C1MR
#define ippiNorm_L1_32f_C1R ippicviNorm_L1_32f_C1R
#define ippiNorm_L1_32f_C3CMR ippicviNorm_L1_32f_C3CMR
#define ippiNorm_L1_32f_C3R ippicviNorm_L1_32f_C3R
#define ippiNorm_L1_32f_C4R ippicviNorm_L1_32f_C4R
#define ippiNorm_L1_8u_C1MR ippicviNorm_L1_8u_C1MR
#define ippiNorm_L1_8u_C1R ippicviNorm_L1_8u_C1R
#define ippiNorm_L1_8u_C3CMR ippicviNorm_L1_8u_C3CMR
#define ippiNorm_L1_8u_C3R ippicviNorm_L1_8u_C3R
#define ippiNorm_L1_8u_C4R ippicviNorm_L1_8u_C4R
#define ippiNorm_L2_16s_C1R ippicviNorm_L2_16s_C1R
#define ippiNorm_L2_16s_C3R ippicviNorm_L2_16s_C3R
#define ippiNorm_L2_16s_C4R ippicviNorm_L2_16s_C4R
#define ippiNorm_L2_16u_C1MR ippicviNorm_L2_16u_C1MR
#define ippiNorm_L2_16u_C1R ippicviNorm_L2_16u_C1R
#define ippiNorm_L2_16u_C3CMR ippicviNorm_L2_16u_C3CMR
#define ippiNorm_L2_16u_C3R ippicviNorm_L2_16u_C3R
#define ippiNorm_L2_16u_C4R ippicviNorm_L2_16u_C4R
#define ippiNorm_L2_32f_C1MR ippicviNorm_L2_32f_C1MR
#define ippiNorm_L2_32f_C1R ippicviNorm_L2_32f_C1R
#define ippiNorm_L2_32f_C3CMR ippicviNorm_L2_32f_C3CMR
#define ippiNorm_L2_32f_C3R ippicviNorm_L2_32f_C3R
#define ippiNorm_L2_32f_C4R ippicviNorm_L2_32f_C4R
#define ippiNorm_L2_8u_C1MR ippicviNorm_L2_8u_C1MR
#define ippiNorm_L2_8u_C1R ippicviNorm_L2_8u_C1R
#define ippiNorm_L2_8u_C3CMR ippicviNorm_L2_8u_C3CMR
#define ippiNorm_L2_8u_C3R ippicviNorm_L2_8u_C3R
#define ippiNorm_L2_8u_C4R ippicviNorm_L2_8u_C4R
#define ippiNot_8u_C1R ippicviNot_8u_C1R
#define ippiOr_8u_C1R ippicviOr_8u_C1R
#define ippiPyramidGetSize ippicviPyramidGetSize
#define ippiPyramidInit ippicviPyramidInit
#define ippiPyramidLayerDownGetSize_16u_C1R ippicviPyramidLayerDownGetSize_16u_C1R
#define ippiPyramidLayerDownGetSize_16u_C3R ippicviPyramidLayerDownGetSize_16u_C3R
#define ippiPyramidLayerDownGetSize_32f_C1R ippicviPyramidLayerDownGetSize_32f_C1R
#define ippiPyramidLayerDownGetSize_32f_C3R ippicviPyramidLayerDownGetSize_32f_C3R
#define ippiPyramidLayerDownGetSize_8u_C1R ippicviPyramidLayerDownGetSize_8u_C1R
#define ippiPyramidLayerDownGetSize_8u_C3R ippicviPyramidLayerDownGetSize_8u_C3R
#define ippiPyramidLayerDownInit_16u_C1R ippicviPyramidLayerDownInit_16u_C1R
#define ippiPyramidLayerDownInit_16u_C3R ippicviPyramidLayerDownInit_16u_C3R
#define ippiPyramidLayerDownInit_32f_C1R ippicviPyramidLayerDownInit_32f_C1R
#define ippiPyramidLayerDownInit_32f_C3R ippicviPyramidLayerDownInit_32f_C3R
#define ippiPyramidLayerDownInit_8u_C1R ippicviPyramidLayerDownInit_8u_C1R
#define ippiPyramidLayerDownInit_8u_C3R ippicviPyramidLayerDownInit_8u_C3R
#define ippiPyramidLayerDown_16u_C1R ippicviPyramidLayerDown_16u_C1R
#define ippiPyramidLayerDown_16u_C3R ippicviPyramidLayerDown_16u_C3R
#define ippiPyramidLayerDown_32f_C1R ippicviPyramidLayerDown_32f_C1R
#define ippiPyramidLayerDown_32f_C3R ippicviPyramidLayerDown_32f_C3R
#define ippiPyramidLayerDown_8u_C1R ippicviPyramidLayerDown_8u_C1R
#define ippiPyramidLayerDown_8u_C3R ippicviPyramidLayerDown_8u_C3R
#define ippiPyramidLayerUpGetSize_16u_C1R ippicviPyramidLayerUpGetSize_16u_C1R
#define ippiPyramidLayerUpGetSize_16u_C3R ippicviPyramidLayerUpGetSize_16u_C3R
#define ippiPyramidLayerUpGetSize_32f_C1R ippicviPyramidLayerUpGetSize_32f_C1R
#define ippiPyramidLayerUpGetSize_32f_C3R ippicviPyramidLayerUpGetSize_32f_C3R
#define ippiPyramidLayerUpGetSize_8u_C1R ippicviPyramidLayerUpGetSize_8u_C1R
#define ippiPyramidLayerUpGetSize_8u_C3R ippicviPyramidLayerUpGetSize_8u_C3R
#define ippiPyramidLayerUpInit_16u_C1R ippicviPyramidLayerUpInit_16u_C1R
#define ippiPyramidLayerUpInit_16u_C3R ippicviPyramidLayerUpInit_16u_C3R
#define ippiPyramidLayerUpInit_32f_C1R ippicviPyramidLayerUpInit_32f_C1R
#define ippiPyramidLayerUpInit_32f_C3R ippicviPyramidLayerUpInit_32f_C3R
#define ippiPyramidLayerUpInit_8u_C1R ippicviPyramidLayerUpInit_8u_C1R
#define ippiPyramidLayerUpInit_8u_C3R ippicviPyramidLayerUpInit_8u_C3R
#define ippiPyramidLayerUp_16u_C1R ippicviPyramidLayerUp_16u_C1R
#define ippiPyramidLayerUp_16u_C3R ippicviPyramidLayerUp_16u_C3R
#define ippiPyramidLayerUp_32f_C1R ippicviPyramidLayerUp_32f_C1R
#define ippiPyramidLayerUp_32f_C3R ippicviPyramidLayerUp_32f_C3R
#define ippiPyramidLayerUp_8u_C1R ippicviPyramidLayerUp_8u_C1R
#define ippiPyramidLayerUp_8u_C3R ippicviPyramidLayerUp_8u_C3R
#define ippiRGBToGray_16s_AC4C1R ippicviRGBToGray_16s_AC4C1R
#define ippiRGBToGray_16s_C3C1R ippicviRGBToGray_16s_C3C1R
#define ippiRGBToGray_16u_AC4C1R ippicviRGBToGray_16u_AC4C1R
#define ippiRGBToGray_16u_C3C1R ippicviRGBToGray_16u_C3C1R
#define ippiRGBToGray_32f_AC4C1R ippicviRGBToGray_32f_AC4C1R
#define ippiRGBToGray_32f_C3C1R ippicviRGBToGray_32f_C3C1R
#define ippiRGBToGray_8u_AC4C1R ippicviRGBToGray_8u_AC4C1R
#define ippiRGBToGray_8u_C3C1R ippicviRGBToGray_8u_C3C1R
#define ippiRGBToHLS_16u_C3R ippicviRGBToHLS_16u_C3R
#define ippiRGBToHLS_32f_C3R ippicviRGBToHLS_32f_C3R
#define ippiRGBToHLS_8u_C3R ippicviRGBToHLS_8u_C3R
#define ippiRGBToHSV_16u_C3R ippicviRGBToHSV_16u_C3R
#define ippiRGBToHSV_8u_C3R ippicviRGBToHSV_8u_C3R
#define ippiRGBToLUV_16s_C3R ippicviRGBToLUV_16s_C3R
#define ippiRGBToLUV_16u_C3R ippicviRGBToLUV_16u_C3R
#define ippiRGBToLUV_32f_C3R ippicviRGBToLUV_32f_C3R
#define ippiRGBToLUV_8u_C3R ippicviRGBToLUV_8u_C3R
#define ippiRGBToXYZ_16u_C3R ippicviRGBToXYZ_16u_C3R
#define ippiRGBToXYZ_32f_C3R ippicviRGBToXYZ_32f_C3R
#define ippiRGBToXYZ_8u_C3R ippicviRGBToXYZ_8u_C3R
#define ippiRGBToYUV_8u_C3R ippicviRGBToYUV_8u_C3R
#define ippiRectStdDev_32f_C1R ippicviRectStdDev_32f_C1R
#define ippiRemap_16s_C1R ippicviRemap_16s_C1R
#define ippiRemap_16s_C3R ippicviRemap_16s_C3R
#define ippiRemap_16s_C4R ippicviRemap_16s_C4R
#define ippiRemap_16u_C1R ippicviRemap_16u_C1R
#define ippiRemap_16u_C3R ippicviRemap_16u_C3R
#define ippiRemap_16u_C4R ippicviRemap_16u_C4R
#define ippiRemap_32f_C1R ippicviRemap_32f_C1R
#define ippiRemap_32f_C3R ippicviRemap_32f_C3R
#define ippiRemap_32f_C4R ippicviRemap_32f_C4R
#define ippiRemap_8u_C1R ippicviRemap_8u_C1R
#define ippiRemap_8u_C3R ippicviRemap_8u_C3R
#define ippiRemap_8u_C4R ippicviRemap_8u_C4R
#define ippiResizeAntialiasingCubicInit_L ippicviResizeAntialiasingCubicInit_L
#define ippiResizeAntialiasingLanczosInit_L ippicviResizeAntialiasingLanczosInit_L
#define ippiResizeAntialiasingLinearInit_L ippicviResizeAntialiasingLinearInit_L
#define ippiResizeAntialiasing_16s_C1R_L ippicviResizeAntialiasing_16s_C1R_L
#define ippiResizeAntialiasing_16s_C3R_L ippicviResizeAntialiasing_16s_C3R_L
#define ippiResizeAntialiasing_16s_C4R_L ippicviResizeAntialiasing_16s_C4R_L
#define ippiResizeAntialiasing_16u_C1R_L ippicviResizeAntialiasing_16u_C1R_L
#define ippiResizeAntialiasing_16u_C3R_L ippicviResizeAntialiasing_16u_C3R_L
#define ippiResizeAntialiasing_16u_C4R_L ippicviResizeAntialiasing_16u_C4R_L
#define ippiResizeAntialiasing_32f_C1R_L ippicviResizeAntialiasing_32f_C1R_L
#define ippiResizeAntialiasing_32f_C3R_L ippicviResizeAntialiasing_32f_C3R_L
#define ippiResizeAntialiasing_32f_C4R_L ippicviResizeAntialiasing_32f_C4R_L
#define ippiResizeAntialiasing_8u_C1R_L ippicviResizeAntialiasing_8u_C1R_L
#define ippiResizeAntialiasing_8u_C3R_L ippicviResizeAntialiasing_8u_C3R_L
#define ippiResizeAntialiasing_8u_C4R_L ippicviResizeAntialiasing_8u_C4R_L
#define ippiResizeCubicInit_L ippicviResizeCubicInit_L
#define ippiResizeCubic_16s_C1R_L ippicviResizeCubic_16s_C1R_L
#define ippiResizeCubic_16s_C3R_L ippicviResizeCubic_16s_C3R_L
#define ippiResizeCubic_16s_C4R_L ippicviResizeCubic_16s_C4R_L
#define ippiResizeCubic_16u_C1R_L ippicviResizeCubic_16u_C1R_L
#define ippiResizeCubic_16u_C3R_L ippicviResizeCubic_16u_C3R_L
#define ippiResizeCubic_16u_C4R_L ippicviResizeCubic_16u_C4R_L
#define ippiResizeCubic_32f_C1R_L ippicviResizeCubic_32f_C1R_L
#define ippiResizeCubic_32f_C3R_L ippicviResizeCubic_32f_C3R_L
#define ippiResizeCubic_32f_C4R_L ippicviResizeCubic_32f_C4R_L
#define ippiResizeCubic_8u_C1R_L ippicviResizeCubic_8u_C1R_L
#define ippiResizeCubic_8u_C3R_L ippicviResizeCubic_8u_C3R_L
#define ippiResizeCubic_8u_C4R_L ippicviResizeCubic_8u_C4R_L
#define ippiResizeGetBorderSize_L ippicviResizeGetBorderSize_L
#define ippiResizeGetBufferSize_L ippicviResizeGetBufferSize_L
#define ippiResizeGetSize_L ippicviResizeGetSize_L
#define ippiResizeGetSrcOffset_L ippicviResizeGetSrcOffset_L
#define ippiResizeGetSrcRoi_L ippicviResizeGetSrcRoi_L
#define ippiResizeLanczosInit_L ippicviResizeLanczosInit_L
#define ippiResizeLanczos_16s_C1R_L ippicviResizeLanczos_16s_C1R_L
#define ippiResizeLanczos_16s_C3R_L ippicviResizeLanczos_16s_C3R_L
#define ippiResizeLanczos_16s_C4R_L ippicviResizeLanczos_16s_C4R_L
#define ippiResizeLanczos_16u_C1R_L ippicviResizeLanczos_16u_C1R_L
#define ippiResizeLanczos_16u_C3R_L ippicviResizeLanczos_16u_C3R_L
#define ippiResizeLanczos_16u_C4R_L ippicviResizeLanczos_16u_C4R_L
#define ippiResizeLanczos_32f_C1R_L ippicviResizeLanczos_32f_C1R_L
#define ippiResizeLanczos_32f_C3R_L ippicviResizeLanczos_32f_C3R_L
#define ippiResizeLanczos_32f_C4R_L ippicviResizeLanczos_32f_C4R_L
#define ippiResizeLanczos_8u_C1R_L ippicviResizeLanczos_8u_C1R_L
#define ippiResizeLanczos_8u_C3R_L ippicviResizeLanczos_8u_C3R_L
#define ippiResizeLanczos_8u_C4R_L ippicviResizeLanczos_8u_C4R_L
#define ippiResizeLinearInit_L ippicviResizeLinearInit_L
#define ippiResizeLinear_16s_C1R_L ippicviResizeLinear_16s_C1R_L
#define ippiResizeLinear_16s_C3R_L ippicviResizeLinear_16s_C3R_L
#define ippiResizeLinear_16s_C4R_L ippicviResizeLinear_16s_C4R_L
#define ippiResizeLinear_16u_C1R_L ippicviResizeLinear_16u_C1R_L
#define ippiResizeLinear_16u_C3R_L ippicviResizeLinear_16u_C3R_L
#define ippiResizeLinear_16u_C4R_L ippicviResizeLinear_16u_C4R_L
#define ippiResizeLinear_32f_C1R_L ippicviResizeLinear_32f_C1R_L
#define ippiResizeLinear_32f_C3R_L ippicviResizeLinear_32f_C3R_L
#define ippiResizeLinear_32f_C4R_L ippicviResizeLinear_32f_C4R_L
#define ippiResizeLinear_64f_C1R_L ippicviResizeLinear_64f_C1R_L
#define ippiResizeLinear_64f_C3R_L ippicviResizeLinear_64f_C3R_L
#define ippiResizeLinear_64f_C4R_L ippicviResizeLinear_64f_C4R_L
#define ippiResizeLinear_8u_C1R_L ippicviResizeLinear_8u_C1R_L
#define ippiResizeLinear_8u_C3R_L ippicviResizeLinear_8u_C3R_L
#define ippiResizeLinear_8u_C4R_L ippicviResizeLinear_8u_C4R_L
#define ippiResizeNearestInit_L ippicviResizeNearestInit_L
#define ippiResizeNearest_16s_C1R_L ippicviResizeNearest_16s_C1R_L
#define ippiResizeNearest_16s_C3R_L ippicviResizeNearest_16s_C3R_L
#define ippiResizeNearest_16s_C4R_L ippicviResizeNearest_16s_C4R_L
#define ippiResizeNearest_16u_C1R_L ippicviResizeNearest_16u_C1R_L
#define ippiResizeNearest_16u_C3R_L ippicviResizeNearest_16u_C3R_L
#define ippiResizeNearest_16u_C4R_L ippicviResizeNearest_16u_C4R_L
#define ippiResizeNearest_32f_C1R_L ippicviResizeNearest_32f_C1R_L
#define ippiResizeNearest_32f_C3R_L ippicviResizeNearest_32f_C3R_L
#define ippiResizeNearest_32f_C4R_L ippicviResizeNearest_32f_C4R_L
#define ippiResizeNearest_8u_C1R_L ippicviResizeNearest_8u_C1R_L
#define ippiResizeNearest_8u_C3R_L ippicviResizeNearest_8u_C3R_L
#define ippiResizeNearest_8u_C4R_L ippicviResizeNearest_8u_C4R_L
#define ippiResizeSuperInit_L ippicviResizeSuperInit_L
#define ippiResizeSuper_16s_C1R_L ippicviResizeSuper_16s_C1R_L
#define ippiResizeSuper_16s_C3R_L ippicviResizeSuper_16s_C3R_L
#define ippiResizeSuper_16s_C4R_L ippicviResizeSuper_16s_C4R_L
#define ippiResizeSuper_16u_C1R_L ippicviResizeSuper_16u_C1R_L
#define ippiResizeSuper_16u_C3R_L ippicviResizeSuper_16u_C3R_L
#define ippiResizeSuper_16u_C4R_L ippicviResizeSuper_16u_C4R_L
#define ippiResizeSuper_32f_C1R_L ippicviResizeSuper_32f_C1R_L
#define ippiResizeSuper_32f_C3R_L ippicviResizeSuper_32f_C3R_L
#define ippiResizeSuper_32f_C4R_L ippicviResizeSuper_32f_C4R_L
#define ippiResizeSuper_8u_C1R_L ippicviResizeSuper_8u_C1R_L
#define ippiResizeSuper_8u_C3R_L ippicviResizeSuper_8u_C3R_L
#define ippiResizeSuper_8u_C4R_L ippicviResizeSuper_8u_C4R_L
#define ippiScaleC_16s16u_C1R ippicviScaleC_16s16u_C1R
#define ippiScaleC_16s32f_C1R ippicviScaleC_16s32f_C1R
#define ippiScaleC_16s32s_C1R ippicviScaleC_16s32s_C1R
#define ippiScaleC_16s64f_C1R ippicviScaleC_16s64f_C1R
#define ippiScaleC_16s8s_C1R ippicviScaleC_16s8s_C1R
#define ippiScaleC_16s8u_C1R ippicviScaleC_16s8u_C1R
#define ippiScaleC_16s_C1IR ippicviScaleC_16s_C1IR
#define ippiScaleC_16s_C1R ippicviScaleC_16s_C1R
#define ippiScaleC_16u16s_C1R ippicviScaleC_16u16s_C1R
#define ippiScaleC_16u32f_C1R ippicviScaleC_16u32f_C1R
#define ippiScaleC_16u32s_C1R ippicviScaleC_16u32s_C1R
#define ippiScaleC_16u64f_C1R ippicviScaleC_16u64f_C1R
#define ippiScaleC_16u8s_C1R ippicviScaleC_16u8s_C1R
#define ippiScaleC_16u8u_C1R ippicviScaleC_16u8u_C1R
#define ippiScaleC_16u_C1IR ippicviScaleC_16u_C1IR
#define ippiScaleC_16u_C1R ippicviScaleC_16u_C1R
#define ippiScaleC_32f16s_C1R ippicviScaleC_32f16s_C1R
#define ippiScaleC_32f16u_C1R ippicviScaleC_32f16u_C1R
#define ippiScaleC_32f32s_C1R ippicviScaleC_32f32s_C1R
#define ippiScaleC_32f64f_C1R ippicviScaleC_32f64f_C1R
#define ippiScaleC_32f8s_C1R ippicviScaleC_32f8s_C1R
#define ippiScaleC_32f8u_C1R ippicviScaleC_32f8u_C1R
#define ippiScaleC_32f_C1IR ippicviScaleC_32f_C1IR
#define ippiScaleC_32f_C1R ippicviScaleC_32f_C1R
#define ippiScaleC_32s16s_C1R ippicviScaleC_32s16s_C1R
#define ippiScaleC_32s16u_C1R ippicviScaleC_32s16u_C1R
#define ippiScaleC_32s32f_C1R ippicviScaleC_32s32f_C1R
#define ippiScaleC_32s64f_C1R ippicviScaleC_32s64f_C1R
#define ippiScaleC_32s8s_C1R ippicviScaleC_32s8s_C1R
#define ippiScaleC_32s8u_C1R ippicviScaleC_32s8u_C1R
#define ippiScaleC_32s_C1IR ippicviScaleC_32s_C1IR
#define ippiScaleC_32s_C1R ippicviScaleC_32s_C1R
#define ippiScaleC_64f16s_C1R ippicviScaleC_64f16s_C1R
#define ippiScaleC_64f16u_C1R ippicviScaleC_64f16u_C1R
#define ippiScaleC_64f32f_C1R ippicviScaleC_64f32f_C1R
#define ippiScaleC_64f32s_C1R ippicviScaleC_64f32s_C1R
#define ippiScaleC_64f8s_C1R ippicviScaleC_64f8s_C1R
#define ippiScaleC_64f8u_C1R ippicviScaleC_64f8u_C1R
#define ippiScaleC_64f_C1IR ippicviScaleC_64f_C1IR
#define ippiScaleC_64f_C1R ippicviScaleC_64f_C1R
#define ippiScaleC_8s16s_C1R ippicviScaleC_8s16s_C1R
#define ippiScaleC_8s16u_C1R ippicviScaleC_8s16u_C1R
#define ippiScaleC_8s32f_C1R ippicviScaleC_8s32f_C1R
#define ippiScaleC_8s32s_C1R ippicviScaleC_8s32s_C1R
#define ippiScaleC_8s64f_C1R ippicviScaleC_8s64f_C1R
#define ippiScaleC_8s8u_C1R ippicviScaleC_8s8u_C1R
#define ippiScaleC_8s_C1IR ippicviScaleC_8s_C1IR
#define ippiScaleC_8s_C1R ippicviScaleC_8s_C1R
#define ippiScaleC_8u16s_C1R ippicviScaleC_8u16s_C1R
#define ippiScaleC_8u16u_C1R ippicviScaleC_8u16u_C1R
#define ippiScaleC_8u32f_C1R ippicviScaleC_8u32f_C1R
#define ippiScaleC_8u32s_C1R ippicviScaleC_8u32s_C1R
#define ippiScaleC_8u64f_C1R ippicviScaleC_8u64f_C1R
#define ippiScaleC_8u8s_C1R ippicviScaleC_8u8s_C1R
#define ippiScaleC_8u_C1IR ippicviScaleC_8u_C1IR
#define ippiScaleC_8u_C1R ippicviScaleC_8u_C1R
#define ippiSet_16s_C1MR ippicviSet_16s_C1MR
#define ippiSet_16s_C1R ippicviSet_16s_C1R
#define ippiSet_16s_C3CR ippicviSet_16s_C3CR
#define ippiSet_16s_C3MR ippicviSet_16s_C3MR
#define ippiSet_16s_C3R ippicviSet_16s_C3R
#define ippiSet_16s_C4CR ippicviSet_16s_C4CR
#define ippiSet_16s_C4MR ippicviSet_16s_C4MR
#define ippiSet_16s_C4R ippicviSet_16s_C4R
#define ippiSet_16u_C1MR ippicviSet_16u_C1MR
#define ippiSet_16u_C1R ippicviSet_16u_C1R
#define ippiSet_16u_C3CR ippicviSet_16u_C3CR
#define ippiSet_16u_C3MR ippicviSet_16u_C3MR
#define ippiSet_16u_C3R ippicviSet_16u_C3R
#define ippiSet_16u_C4CR ippicviSet_16u_C4CR
#define ippiSet_16u_C4MR ippicviSet_16u_C4MR
#define ippiSet_16u_C4R ippicviSet_16u_C4R
#define ippiSet_32f_C1MR ippicviSet_32f_C1MR
#define ippiSet_32f_C1R ippicviSet_32f_C1R
#define ippiSet_32f_C3CR ippicviSet_32f_C3CR
#define ippiSet_32f_C3MR ippicviSet_32f_C3MR
#define ippiSet_32f_C3R ippicviSet_32f_C3R
#define ippiSet_32f_C4CR ippicviSet_32f_C4CR
#define ippiSet_32f_C4MR ippicviSet_32f_C4MR
#define ippiSet_32f_C4R ippicviSet_32f_C4R
#define ippiSet_32s_C1MR ippicviSet_32s_C1MR
#define ippiSet_32s_C1R ippicviSet_32s_C1R
#define ippiSet_32s_C3CR ippicviSet_32s_C3CR
#define ippiSet_32s_C3MR ippicviSet_32s_C3MR
#define ippiSet_32s_C3R ippicviSet_32s_C3R
#define ippiSet_32s_C4CR ippicviSet_32s_C4CR
#define ippiSet_32s_C4MR ippicviSet_32s_C4MR
#define ippiSet_32s_C4R ippicviSet_32s_C4R
#define ippiSet_8u_C1MR ippicviSet_8u_C1MR
#define ippiSet_8u_C1R ippicviSet_8u_C1R
#define ippiSet_8u_C3CR ippicviSet_8u_C3CR
#define ippiSet_8u_C3MR ippicviSet_8u_C3MR
#define ippiSet_8u_C3R ippicviSet_8u_C3R
#define ippiSet_8u_C4CR ippicviSet_8u_C4CR
#define ippiSet_8u_C4MR ippicviSet_8u_C4MR
#define ippiSet_8u_C4R ippicviSet_8u_C4R
#define ippiSqrDistanceNormGetBufferSize ippicviSqrDistanceNormGetBufferSize
#define ippiSqrDistanceNorm_32f_C1R ippicviSqrDistanceNorm_32f_C1R
#define ippiSqrDistanceNorm_8u32f_C1R ippicviSqrDistanceNorm_8u32f_C1R
#define ippiSqrIntegral_8u32f64f_C1R ippicviSqrIntegral_8u32f64f_C1R
#define ippiSqrIntegral_8u32s64f_C1R ippicviSqrIntegral_8u32s64f_C1R
#define ippiSqrIntegral_8u32s_C1R ippicviSqrIntegral_8u32s_C1R
#define ippiSqr_32f_C1R ippicviSqr_32f_C1R
#define ippiSub_16s_C1RSfs ippicviSub_16s_C1RSfs
#define ippiSub_16u_C1RSfs ippicviSub_16u_C1RSfs
#define ippiSub_32f_C1R ippicviSub_32f_C1R
#define ippiSub_8u_C1RSfs ippicviSub_8u_C1RSfs
#define ippiSum_16s_C1R ippicviSum_16s_C1R
#define ippiSum_16s_C3R ippicviSum_16s_C3R
#define ippiSum_16s_C4R ippicviSum_16s_C4R
#define ippiSum_16u_C1R ippicviSum_16u_C1R
#define ippiSum_16u_C3R ippicviSum_16u_C3R
#define ippiSum_16u_C4R ippicviSum_16u_C4R
#define ippiSum_32f_C1R ippicviSum_32f_C1R
#define ippiSum_32f_C3R ippicviSum_32f_C3R
#define ippiSum_32f_C4R ippicviSum_32f_C4R
#define ippiSum_8u_C1R ippicviSum_8u_C1R
#define ippiSum_8u_C3R ippicviSum_8u_C3R
#define ippiSum_8u_C4R ippicviSum_8u_C4R
#define ippiSwapChannels_16s_C3C4R ippicviSwapChannels_16s_C3C4R
#define ippiSwapChannels_16u_C3C4R ippicviSwapChannels_16u_C3C4R
#define ippiSwapChannels_16u_C3R ippicviSwapChannels_16u_C3R
#define ippiSwapChannels_16u_C4C3R ippicviSwapChannels_16u_C4C3R
#define ippiSwapChannels_16u_C4R ippicviSwapChannels_16u_C4R
#define ippiSwapChannels_32f_C3C4R ippicviSwapChannels_32f_C3C4R
#define ippiSwapChannels_32f_C3R ippicviSwapChannels_32f_C3R
#define ippiSwapChannels_32f_C4C3R ippicviSwapChannels_32f_C4C3R
#define ippiSwapChannels_32f_C4R ippicviSwapChannels_32f_C4R
#define ippiSwapChannels_32s_C3C4R ippicviSwapChannels_32s_C3C4R
#define ippiSwapChannels_8u_C3C4R ippicviSwapChannels_8u_C3C4R
#define ippiSwapChannels_8u_C3IR ippicviSwapChannels_8u_C3IR
#define ippiSwapChannels_8u_C3R ippicviSwapChannels_8u_C3R
#define ippiSwapChannels_8u_C4C3R ippicviSwapChannels_8u_C4C3R
#define ippiSwapChannels_8u_C4IR ippicviSwapChannels_8u_C4IR
#define ippiSwapChannels_8u_C4R ippicviSwapChannels_8u_C4R
#define ippiThreshold_GTVal_16s_C1IR ippicviThreshold_GTVal_16s_C1IR
#define ippiThreshold_GTVal_16s_C1R ippicviThreshold_GTVal_16s_C1R
#define ippiThreshold_GTVal_16s_C3IR ippicviThreshold_GTVal_16s_C3IR
#define ippiThreshold_GTVal_16s_C3R ippicviThreshold_GTVal_16s_C3R
#define ippiThreshold_GTVal_16s_C4IR ippicviThreshold_GTVal_16s_C4IR
#define ippiThreshold_GTVal_16s_C4R ippicviThreshold_GTVal_16s_C4R
#define ippiThreshold_GTVal_16u_C1IR ippicviThreshold_GTVal_16u_C1IR
#define ippiThreshold_GTVal_16u_C1R ippicviThreshold_GTVal_16u_C1R
#define ippiThreshold_GTVal_16u_C3IR ippicviThreshold_GTVal_16u_C3IR
#define ippiThreshold_GTVal_16u_C3R ippicviThreshold_GTVal_16u_C3R
#define ippiThreshold_GTVal_16u_C4IR ippicviThreshold_GTVal_16u_C4IR
#define ippiThreshold_GTVal_16u_C4R ippicviThreshold_GTVal_16u_C4R
#define ippiThreshold_GTVal_32f_C1IR ippicviThreshold_GTVal_32f_C1IR
#define ippiThreshold_GTVal_32f_C1R ippicviThreshold_GTVal_32f_C1R
#define ippiThreshold_GTVal_32f_C3IR ippicviThreshold_GTVal_32f_C3IR
#define ippiThreshold_GTVal_32f_C3R ippicviThreshold_GTVal_32f_C3R
#define ippiThreshold_GTVal_32f_C4IR ippicviThreshold_GTVal_32f_C4IR
#define ippiThreshold_GTVal_32f_C4R ippicviThreshold_GTVal_32f_C4R
#define ippiThreshold_GTVal_8u_C1IR ippicviThreshold_GTVal_8u_C1IR
#define ippiThreshold_GTVal_8u_C1R ippicviThreshold_GTVal_8u_C1R
#define ippiThreshold_GTVal_8u_C3IR ippicviThreshold_GTVal_8u_C3IR
#define ippiThreshold_GTVal_8u_C3R ippicviThreshold_GTVal_8u_C3R
#define ippiThreshold_GTVal_8u_C4IR ippicviThreshold_GTVal_8u_C4IR
#define ippiThreshold_GTVal_8u_C4R ippicviThreshold_GTVal_8u_C4R
#define ippiThreshold_GT_16s_C1IR ippicviThreshold_GT_16s_C1IR
#define ippiThreshold_GT_16s_C1R ippicviThreshold_GT_16s_C1R
#define ippiThreshold_GT_16s_C3IR ippicviThreshold_GT_16s_C3IR
#define ippiThreshold_GT_16s_C3R ippicviThreshold_GT_16s_C3R
#define ippiThreshold_GT_16u_C1IR ippicviThreshold_GT_16u_C1IR
#define ippiThreshold_GT_16u_C1R ippicviThreshold_GT_16u_C1R
#define ippiThreshold_GT_16u_C3IR ippicviThreshold_GT_16u_C3IR
#define ippiThreshold_GT_16u_C3R ippicviThreshold_GT_16u_C3R
#define ippiThreshold_GT_32f_C1IR ippicviThreshold_GT_32f_C1IR
#define ippiThreshold_GT_32f_C1R ippicviThreshold_GT_32f_C1R
#define ippiThreshold_GT_32f_C3IR ippicviThreshold_GT_32f_C3IR
#define ippiThreshold_GT_32f_C3R ippicviThreshold_GT_32f_C3R
#define ippiThreshold_GT_8u_C1IR ippicviThreshold_GT_8u_C1IR
#define ippiThreshold_GT_8u_C1R ippicviThreshold_GT_8u_C1R
#define ippiThreshold_GT_8u_C3IR ippicviThreshold_GT_8u_C3IR
#define ippiThreshold_GT_8u_C3R ippicviThreshold_GT_8u_C3R
#define ippiThreshold_LTVal_16s_C1IR ippicviThreshold_LTVal_16s_C1IR
#define ippiThreshold_LTVal_16s_C1R ippicviThreshold_LTVal_16s_C1R
#define ippiThreshold_LTVal_16s_C3IR ippicviThreshold_LTVal_16s_C3IR
#define ippiThreshold_LTVal_16s_C3R ippicviThreshold_LTVal_16s_C3R
#define ippiThreshold_LTVal_16s_C4IR ippicviThreshold_LTVal_16s_C4IR
#define ippiThreshold_LTVal_16s_C4R ippicviThreshold_LTVal_16s_C4R
#define ippiThreshold_LTVal_16u_C1IR ippicviThreshold_LTVal_16u_C1IR
#define ippiThreshold_LTVal_16u_C1R ippicviThreshold_LTVal_16u_C1R
#define ippiThreshold_LTVal_16u_C3IR ippicviThreshold_LTVal_16u_C3IR
#define ippiThreshold_LTVal_16u_C3R ippicviThreshold_LTVal_16u_C3R
#define ippiThreshold_LTVal_16u_C4IR ippicviThreshold_LTVal_16u_C4IR
#define ippiThreshold_LTVal_16u_C4R ippicviThreshold_LTVal_16u_C4R
#define ippiThreshold_LTVal_32f_C1IR ippicviThreshold_LTVal_32f_C1IR
#define ippiThreshold_LTVal_32f_C1R ippicviThreshold_LTVal_32f_C1R
#define ippiThreshold_LTVal_32f_C3IR ippicviThreshold_LTVal_32f_C3IR
#define ippiThreshold_LTVal_32f_C3R ippicviThreshold_LTVal_32f_C3R
#define ippiThreshold_LTVal_32f_C4IR ippicviThreshold_LTVal_32f_C4IR
#define ippiThreshold_LTVal_32f_C4R ippicviThreshold_LTVal_32f_C4R
#define ippiThreshold_LTVal_8u_C1IR ippicviThreshold_LTVal_8u_C1IR
#define ippiThreshold_LTVal_8u_C1R ippicviThreshold_LTVal_8u_C1R
#define ippiThreshold_LTVal_8u_C3IR ippicviThreshold_LTVal_8u_C3IR
#define ippiThreshold_LTVal_8u_C3R ippicviThreshold_LTVal_8u_C3R
#define ippiThreshold_LTVal_8u_C4IR ippicviThreshold_LTVal_8u_C4IR
#define ippiThreshold_LTVal_8u_C4R ippicviThreshold_LTVal_8u_C4R
#define ippiTiltHaarFeatures_32f ippicviTiltHaarFeatures_32f
#define ippiTiltedHaarClassifierInit_32f ippicviTiltedHaarClassifierInit_32f
#define ippiTiltedIntegral_8u32f_C1R ippicviTiltedIntegral_8u32f_C1R
#define ippiTiltedIntegral_8u32s_C1R ippicviTiltedIntegral_8u32s_C1R
#define ippiTiltedSqrIntegral_8u32f64f_C1R ippicviTiltedSqrIntegral_8u32f64f_C1R
#define ippiTiltedSqrIntegral_8u32s64f_C1R ippicviTiltedSqrIntegral_8u32s64f_C1R
#define ippiTiltedSqrIntegral_8u32s_C1R ippicviTiltedSqrIntegral_8u32s_C1R
#define ippiTranspose_16s_C1IR ippicviTranspose_16s_C1IR
#define ippiTranspose_16s_C1R ippicviTranspose_16s_C1R
#define ippiTranspose_16s_C3IR ippicviTranspose_16s_C3IR
#define ippiTranspose_16s_C3R ippicviTranspose_16s_C3R
#define ippiTranspose_16s_C4IR ippicviTranspose_16s_C4IR
#define ippiTranspose_16s_C4R ippicviTranspose_16s_C4R
#define ippiTranspose_16u_C1IR ippicviTranspose_16u_C1IR
#define ippiTranspose_16u_C1R ippicviTranspose_16u_C1R
#define ippiTranspose_16u_C3IR ippicviTranspose_16u_C3IR
#define ippiTranspose_16u_C3R ippicviTranspose_16u_C3R
#define ippiTranspose_16u_C4IR ippicviTranspose_16u_C4IR
#define ippiTranspose_16u_C4R ippicviTranspose_16u_C4R
#define ippiTranspose_32f_C1IR ippicviTranspose_32f_C1IR
#define ippiTranspose_32f_C1R ippicviTranspose_32f_C1R
#define ippiTranspose_32f_C3IR ippicviTranspose_32f_C3IR
#define ippiTranspose_32f_C3R ippicviTranspose_32f_C3R
#define ippiTranspose_32f_C4IR ippicviTranspose_32f_C4IR
#define ippiTranspose_32f_C4R ippicviTranspose_32f_C4R
#define ippiTranspose_32s_C1IR ippicviTranspose_32s_C1IR
#define ippiTranspose_32s_C1R ippicviTranspose_32s_C1R
#define ippiTranspose_32s_C3IR ippicviTranspose_32s_C3IR
#define ippiTranspose_32s_C3R ippicviTranspose_32s_C3R
#define ippiTranspose_32s_C4IR ippicviTranspose_32s_C4IR
#define ippiTranspose_32s_C4R ippicviTranspose_32s_C4R
#define ippiTranspose_8u_C1IR ippicviTranspose_8u_C1IR
#define ippiTranspose_8u_C1R ippicviTranspose_8u_C1R
#define ippiTranspose_8u_C3IR ippicviTranspose_8u_C3IR
#define ippiTranspose_8u_C3R ippicviTranspose_8u_C3R
#define ippiTranspose_8u_C4IR ippicviTranspose_8u_C4IR
#define ippiTranspose_8u_C4R ippicviTranspose_8u_C4R
#define ippiTrueDistanceTransformGetBufferSize_8u32f_C1R ippicviTrueDistanceTransformGetBufferSize_8u32f_C1R
#define ippiTrueDistanceTransform_8u32f_C1R ippicviTrueDistanceTransform_8u32f_C1R
#define ippiUpdateMotionHistory_8u32f_C1IR ippicviUpdateMotionHistory_8u32f_C1IR
#define ippiWarpAffineCubicInit ippicviWarpAffineCubicInit
#define ippiWarpAffineCubic_16s_C1R ippicviWarpAffineCubic_16s_C1R
#define ippiWarpAffineCubic_16s_C3R ippicviWarpAffineCubic_16s_C3R
#define ippiWarpAffineCubic_16s_C4R ippicviWarpAffineCubic_16s_C4R
#define ippiWarpAffineCubic_16u_C1R ippicviWarpAffineCubic_16u_C1R
#define ippiWarpAffineCubic_16u_C3R ippicviWarpAffineCubic_16u_C3R
#define ippiWarpAffineCubic_16u_C4R ippicviWarpAffineCubic_16u_C4R
#define ippiWarpAffineCubic_32f_C1R ippicviWarpAffineCubic_32f_C1R
#define ippiWarpAffineCubic_32f_C3R ippicviWarpAffineCubic_32f_C3R
#define ippiWarpAffineCubic_32f_C4R ippicviWarpAffineCubic_32f_C4R
#define ippiWarpAffineCubic_64f_C1R ippicviWarpAffineCubic_64f_C1R
#define ippiWarpAffineCubic_64f_C3R ippicviWarpAffineCubic_64f_C3R
#define ippiWarpAffineCubic_64f_C4R ippicviWarpAffineCubic_64f_C4R
#define ippiWarpAffineCubic_8u_C1R ippicviWarpAffineCubic_8u_C1R
#define ippiWarpAffineCubic_8u_C3R ippicviWarpAffineCubic_8u_C3R
#define ippiWarpAffineCubic_8u_C4R ippicviWarpAffineCubic_8u_C4R
#define ippiWarpAffineGetSize ippicviWarpAffineGetSize
#define ippiWarpAffineLinearInit ippicviWarpAffineLinearInit
#define ippiWarpAffineLinear_16s_C1R ippicviWarpAffineLinear_16s_C1R
#define ippiWarpAffineLinear_16s_C3R ippicviWarpAffineLinear_16s_C3R
#define ippiWarpAffineLinear_16s_C4R ippicviWarpAffineLinear_16s_C4R
#define ippiWarpAffineLinear_16u_C1R ippicviWarpAffineLinear_16u_C1R
#define ippiWarpAffineLinear_16u_C3R ippicviWarpAffineLinear_16u_C3R
#define ippiWarpAffineLinear_16u_C4R ippicviWarpAffineLinear_16u_C4R
#define ippiWarpAffineLinear_32f_C1R ippicviWarpAffineLinear_32f_C1R
#define ippiWarpAffineLinear_32f_C3R ippicviWarpAffineLinear_32f_C3R
#define ippiWarpAffineLinear_32f_C4R ippicviWarpAffineLinear_32f_C4R
#define ippiWarpAffineLinear_64f_C1R ippicviWarpAffineLinear_64f_C1R
#define ippiWarpAffineLinear_64f_C3R ippicviWarpAffineLinear_64f_C3R
#define ippiWarpAffineLinear_64f_C4R ippicviWarpAffineLinear_64f_C4R
#define ippiWarpAffineLinear_8u_C1R ippicviWarpAffineLinear_8u_C1R
#define ippiWarpAffineLinear_8u_C3R ippicviWarpAffineLinear_8u_C3R
#define ippiWarpAffineLinear_8u_C4R ippicviWarpAffineLinear_8u_C4R
#define ippiWarpAffineNearestInit ippicviWarpAffineNearestInit
#define ippiWarpAffineNearest_16s_C1R ippicviWarpAffineNearest_16s_C1R
#define ippiWarpAffineNearest_16s_C3R ippicviWarpAffineNearest_16s_C3R
#define ippiWarpAffineNearest_16s_C4R ippicviWarpAffineNearest_16s_C4R
#define ippiWarpAffineNearest_16u_C1R ippicviWarpAffineNearest_16u_C1R
#define ippiWarpAffineNearest_16u_C3R ippicviWarpAffineNearest_16u_C3R
#define ippiWarpAffineNearest_16u_C4R ippicviWarpAffineNearest_16u_C4R
#define ippiWarpAffineNearest_32f_C1R ippicviWarpAffineNearest_32f_C1R
#define ippiWarpAffineNearest_32f_C3R ippicviWarpAffineNearest_32f_C3R
#define ippiWarpAffineNearest_32f_C4R ippicviWarpAffineNearest_32f_C4R
#define ippiWarpAffineNearest_64f_C1R ippicviWarpAffineNearest_64f_C1R
#define ippiWarpAffineNearest_64f_C3R ippicviWarpAffineNearest_64f_C3R
#define ippiWarpAffineNearest_64f_C4R ippicviWarpAffineNearest_64f_C4R
#define ippiWarpAffineNearest_8u_C1R ippicviWarpAffineNearest_8u_C1R
#define ippiWarpAffineNearest_8u_C3R ippicviWarpAffineNearest_8u_C3R
#define ippiWarpAffineNearest_8u_C4R ippicviWarpAffineNearest_8u_C4R
#define ippiWarpGetBufferSize ippicviWarpGetBufferSize
#define ippiWarpGetRectInfinite ippicviWarpGetRectInfinite
#define ippiWarpPerspectiveCubicInit ippicviWarpPerspectiveCubicInit
#define ippiWarpPerspectiveCubic_16s_C1R ippicviWarpPerspectiveCubic_16s_C1R
#define ippiWarpPerspectiveCubic_16s_C3R ippicviWarpPerspectiveCubic_16s_C3R
#define ippiWarpPerspectiveCubic_16s_C4R ippicviWarpPerspectiveCubic_16s_C4R
#define ippiWarpPerspectiveCubic_16u_C1R ippicviWarpPerspectiveCubic_16u_C1R
#define ippiWarpPerspectiveCubic_16u_C3R ippicviWarpPerspectiveCubic_16u_C3R
#define ippiWarpPerspectiveCubic_16u_C4R ippicviWarpPerspectiveCubic_16u_C4R
#define ippiWarpPerspectiveCubic_32f_C1R ippicviWarpPerspectiveCubic_32f_C1R
#define ippiWarpPerspectiveCubic_32f_C3R ippicviWarpPerspectiveCubic_32f_C3R
#define ippiWarpPerspectiveCubic_32f_C4R ippicviWarpPerspectiveCubic_32f_C4R
#define ippiWarpPerspectiveCubic_8u_C1R ippicviWarpPerspectiveCubic_8u_C1R
#define ippiWarpPerspectiveCubic_8u_C3R ippicviWarpPerspectiveCubic_8u_C3R
#define ippiWarpPerspectiveCubic_8u_C4R ippicviWarpPerspectiveCubic_8u_C4R
#define ippiWarpPerspectiveGetSize ippicviWarpPerspectiveGetSize
#define ippiWarpPerspectiveLinearInit ippicviWarpPerspectiveLinearInit
#define ippiWarpPerspectiveLinear_16s_C1R ippicviWarpPerspectiveLinear_16s_C1R
#define ippiWarpPerspectiveLinear_16s_C3R ippicviWarpPerspectiveLinear_16s_C3R
#define ippiWarpPerspectiveLinear_16s_C4R ippicviWarpPerspectiveLinear_16s_C4R
#define ippiWarpPerspectiveLinear_16u_C1R ippicviWarpPerspectiveLinear_16u_C1R
#define ippiWarpPerspectiveLinear_16u_C3R ippicviWarpPerspectiveLinear_16u_C3R
#define ippiWarpPerspectiveLinear_16u_C4R ippicviWarpPerspectiveLinear_16u_C4R
#define ippiWarpPerspectiveLinear_32f_C1R ippicviWarpPerspectiveLinear_32f_C1R
#define ippiWarpPerspectiveLinear_32f_C3R ippicviWarpPerspectiveLinear_32f_C3R
#define ippiWarpPerspectiveLinear_32f_C4R ippicviWarpPerspectiveLinear_32f_C4R
#define ippiWarpPerspectiveLinear_8u_C1R ippicviWarpPerspectiveLinear_8u_C1R
#define ippiWarpPerspectiveLinear_8u_C3R ippicviWarpPerspectiveLinear_8u_C3R
#define ippiWarpPerspectiveLinear_8u_C4R ippicviWarpPerspectiveLinear_8u_C4R
#define ippiWarpPerspectiveNearestInit ippicviWarpPerspectiveNearestInit
#define ippiWarpPerspectiveNearest_16s_C1R ippicviWarpPerspectiveNearest_16s_C1R
#define ippiWarpPerspectiveNearest_16s_C3R ippicviWarpPerspectiveNearest_16s_C3R
#define ippiWarpPerspectiveNearest_16s_C4R ippicviWarpPerspectiveNearest_16s_C4R
#define ippiWarpPerspectiveNearest_16u_C1R ippicviWarpPerspectiveNearest_16u_C1R
#define ippiWarpPerspectiveNearest_16u_C3R ippicviWarpPerspectiveNearest_16u_C3R
#define ippiWarpPerspectiveNearest_16u_C4R ippicviWarpPerspectiveNearest_16u_C4R
#define ippiWarpPerspectiveNearest_32f_C1R ippicviWarpPerspectiveNearest_32f_C1R
#define ippiWarpPerspectiveNearest_32f_C3R ippicviWarpPerspectiveNearest_32f_C3R
#define ippiWarpPerspectiveNearest_32f_C4R ippicviWarpPerspectiveNearest_32f_C4R
#define ippiWarpPerspectiveNearest_8u_C1R ippicviWarpPerspectiveNearest_8u_C1R
#define ippiWarpPerspectiveNearest_8u_C3R ippicviWarpPerspectiveNearest_8u_C3R
#define ippiWarpPerspectiveNearest_8u_C4R ippicviWarpPerspectiveNearest_8u_C4R
#define ippiXYZToRGB_16u_C3R ippicviXYZToRGB_16u_C3R
#define ippiXYZToRGB_32f_C3R ippicviXYZToRGB_32f_C3R
#define ippiXYZToRGB_8u_C3R ippicviXYZToRGB_8u_C3R
#define ippiXor_8u_C1R ippicviXor_8u_C1R
#define ippiYUVToRGB_8u_C3R ippicviYUVToRGB_8u_C3R
#define ippsConvert_64f32f ippicvsConvert_64f32f
#define ippsCopy_64f ippicvsCopy_64f
#define ippsDFTFwd_CToC_32fc ippicvsDFTFwd_CToC_32fc
#define ippsDFTFwd_CToC_64fc ippicvsDFTFwd_CToC_64fc
#define ippsDFTFwd_RToPack_32f ippicvsDFTFwd_RToPack_32f
#define ippsDFTFwd_RToPack_64f ippicvsDFTFwd_RToPack_64f
#define ippsDFTGetSize_C_32fc ippicvsDFTGetSize_C_32fc
#define ippsDFTGetSize_C_64fc ippicvsDFTGetSize_C_64fc
#define ippsDFTGetSize_R_32f ippicvsDFTGetSize_R_32f
#define ippsDFTGetSize_R_64f ippicvsDFTGetSize_R_64f
#define ippsDFTInit_C_32fc ippicvsDFTInit_C_32fc
#define ippsDFTInit_C_64fc ippicvsDFTInit_C_64fc
#define ippsDFTInit_R_32f ippicvsDFTInit_R_32f
#define ippsDFTInit_R_64f ippicvsDFTInit_R_64f
#define ippsDFTInv_CToC_32fc ippicvsDFTInv_CToC_32fc
#define ippsDFTInv_CToC_64fc ippicvsDFTInv_CToC_64fc
#define ippsDFTInv_PackToR_32f ippicvsDFTInv_PackToR_32f
#define ippsDFTInv_PackToR_64f ippicvsDFTInv_PackToR_64f
#define ippsDotProd_32f64f ippicvsDotProd_32f64f
#define ippsDotProd_64f ippicvsDotProd_64f
#define ippsExp_32f_A21 ippicvsExp_32f_A21
#define ippsExp_64f_A50 ippicvsExp_64f_A50
#define ippsFlip_16u_I ippicvsFlip_16u_I
#define ippsFlip_32f_I ippicvsFlip_32f_I
#define ippsFlip_64f_I ippicvsFlip_64f_I
#define ippsFlip_8u_I ippicvsFlip_8u_I
#define ippsFree ippicvsFree
#define ippsGetLibVersion ippicvsGetLibVersion
#define ippsInvSqrt_32f_A21 ippicvsInvSqrt_32f_A21
#define ippsInvSqrt_64f_A50 ippicvsInvSqrt_64f_A50
#define ippsLn_32f_A21 ippicvsLn_32f_A21
#define ippsLn_64f_A50 ippicvsLn_64f_A50
#define ippsMagnitude_32f ippicvsMagnitude_32f
#define ippsMagnitude_64f ippicvsMagnitude_64f
#define ippsMalloc_8u_L ippicvsMalloc_8u_L
#define ippsMaxEvery_16u ippicvsMaxEvery_16u
#define ippsMaxEvery_32f ippicvsMaxEvery_32f
#define ippsMaxEvery_64f ippicvsMaxEvery_64f
#define ippsMaxEvery_8u ippicvsMaxEvery_8u
#define ippsMinEvery_16u ippicvsMinEvery_16u
#define ippsMinEvery_32f ippicvsMinEvery_32f
#define ippsMinEvery_64f ippicvsMinEvery_64f
#define ippsMinEvery_8u ippicvsMinEvery_8u
#define ippsPolarToCart_32f ippicvsPolarToCart_32f
#define ippsPolarToCart_64f ippicvsPolarToCart_64f
#define ippsPowx_32f_A21 ippicvsPowx_32f_A21
#define ippsPowx_64f_A50 ippicvsPowx_64f_A50
#define ippsRound_64f ippicvsRound_64f
#define ippsSet_16s ippicvsSet_16s
#define ippsSet_32f ippicvsSet_32f
#define ippsSet_32s ippicvsSet_32s
#define ippsSet_64f ippicvsSet_64f
#define ippsSet_8u ippicvsSet_8u
#define ippsSortRadixAscend_16s_I ippicvsSortRadixAscend_16s_I
#define ippsSortRadixAscend_16u_I ippicvsSortRadixAscend_16u_I
#define ippsSortRadixAscend_32f_I ippicvsSortRadixAscend_32f_I
#define ippsSortRadixAscend_32s_I ippicvsSortRadixAscend_32s_I
#define ippsSortRadixAscend_64f_I ippicvsSortRadixAscend_64f_I
#define ippsSortRadixAscend_8u_I ippicvsSortRadixAscend_8u_I
#define ippsSortRadixDescend_16s_I ippicvsSortRadixDescend_16s_I
#define ippsSortRadixDescend_16u_I ippicvsSortRadixDescend_16u_I
#define ippsSortRadixDescend_32f_I ippicvsSortRadixDescend_32f_I
#define ippsSortRadixDescend_32s_I ippicvsSortRadixDescend_32s_I
#define ippsSortRadixDescend_64f_I ippicvsSortRadixDescend_64f_I
#define ippsSortRadixDescend_8u_I ippicvsSortRadixDescend_8u_I
#define ippsSortRadixGetBufferSize ippicvsSortRadixGetBufferSize
#define ippsSortRadixIndexAscend_16s ippicvsSortRadixIndexAscend_16s
#define ippsSortRadixIndexAscend_16u ippicvsSortRadixIndexAscend_16u
#define ippsSortRadixIndexAscend_32f ippicvsSortRadixIndexAscend_32f
#define ippsSortRadixIndexAscend_32s ippicvsSortRadixIndexAscend_32s
#define ippsSortRadixIndexAscend_8u ippicvsSortRadixIndexAscend_8u
#define ippsSortRadixIndexDescend_16s ippicvsSortRadixIndexDescend_16s
#define ippsSortRadixIndexDescend_16u ippicvsSortRadixIndexDescend_16u
#define ippsSortRadixIndexDescend_32f ippicvsSortRadixIndexDescend_32f
#define ippsSortRadixIndexDescend_32s ippicvsSortRadixIndexDescend_32s
#define ippsSortRadixIndexDescend_8u ippicvsSortRadixIndexDescend_8u
#define ippsSortRadixIndexGetBufferSize ippicvsSortRadixIndexGetBufferSize
#define ippsSqrt_32f_A21 ippicvsSqrt_32f_A21
#define ippsSqrt_64f_A50 ippicvsSqrt_64f_A50
#define ippsZero_8u ippicvsZero_8u
#define ippvmGetLibVersion ippicvvmGetLibVersion

#endif /* __NO_ICV_REDEF__    */
#endif /* __IPPICV_REDEFS_H__ */

