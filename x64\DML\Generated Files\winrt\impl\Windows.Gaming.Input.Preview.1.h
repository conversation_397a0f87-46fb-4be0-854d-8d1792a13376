// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Gaming_Input_Preview_1_H
#define WINRT_Windows_Gaming_Input_Preview_1_H
#include "winrt/impl/Windows.Gaming.Input.Preview.0.h"
WINRT_EXPORT namespace winrt::Windows::Gaming::Input::Preview
{
    struct WINRT_IMPL_EMPTY_BASES IGameControllerProviderInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGameControllerProviderInfoStatics>
    {
        IGameControllerProviderInfoStatics(std::nullptr_t = nullptr) noexcept {}
        IGameControllerProviderInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILegacyGipGameControllerProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILegacyGipGameControllerProvider>
    {
        ILegacyGipGameControllerProvider(std::nullptr_t = nullptr) noexcept {}
        ILegacyGipGameControllerProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILegacyGipGameControllerProviderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILegacyGipGameControllerProviderStatics>
    {
        ILegacyGipGameControllerProviderStatics(std::nullptr_t = nullptr) noexcept {}
        ILegacyGipGameControllerProviderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
