/*
 * Software License Agreement (BSD License)
 *
 *  Copyright (c) 2009, Willow Garage, Inc.
 *  All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *   * Redistributions of source code must retain the above copyright
 *     notice, this list of conditions and the following disclaimer.
 *   * Redistributions in binary form must reproduce the above
 *     copyright notice, this list of conditions and the following
 *     disclaimer in the documentation and/or other materials provided
 *     with the distribution.
 *   * Neither the name of Willow Garage, Inc. nor the names of its
 *     contributors may be used to endorse or promote products derived
 *     from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 *  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 *  COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 *  INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIAL DAMAGES (INCLUDING,
 *  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 *  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 *  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 *  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
 *  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 *  POSSIBILITY OF SUCH DAMAGE.
 *
 */
/**
 * @file sphereview_3dobj_demo.cpp
 * @brief Generating training data for CNN with triplet loss.
 * <AUTHOR> Wang
 */
#include <iostream>
#include "opencv2/imgproc.hpp"
#include "opencv2/cnn_3dobj.hpp"
using namespace cv;
using namespace cv::cnn_3dobj;

int main(int argc, char** argv)
{
    const String keys = "{help | | this demo will have an analysis on the trained model, it will print information about whether the model is suit for set different classes apart and also discriminant on object pose at the same time.}"
"{caffemodel | ../../testdata/cv/3d_triplet_iter_30000.caffemodel | caffe model for feature exrtaction.}"
"{network_forIMG | ../../testdata/cv/3d_triplet_testIMG.prototxt | Network definition file used for extracting feature from a single image and making a classification}"
"{mean_file | no | The mean file generated by Caffe from all gallery images, this could be used for mean value substraction from all images. If you want to use the mean file, you can set this as ../data/images_mean/triplet_mean.binaryproto.}"
"{target_img | ../data/images_all/4_78.png | Path of image in reference.}"
"{ref_img1 | ../data/images_all/4_79.png | Path of closest image.}"
"{ref_img2 | ../data/images_all/4_87.png | Path of less closer image in the same class with reference image.}"
"{ref_img3 | ../data/images_all/3_78.png | Path of image with the same pose in another class.}"
"{feature_blob | feat | Name of layer which will represent as the feature, in this network, ip1 or feat is well.}"
"{device | CPU | device}"
"{dev_id | 0 | dev_id}";
    /* Get parameters from comand line. */
    cv::CommandLineParser parser(argc, argv, keys);
    parser.about("Demo for object data classification and pose estimation");
    if (parser.has("help"))
    {
        parser.printMessage();
        return 0;
    }
    String caffemodel = parser.get<String>("caffemodel");
    String network_forIMG = parser.get<String>("network_forIMG");
    String mean_file = parser.get<String>("mean_file");
    String target_img = parser.get<String>("target_img");
    String ref_img1 = parser.get<String>("ref_img1");
    String ref_img2 = parser.get<String>("ref_img2");
    String ref_img3 = parser.get<String>("ref_img3");
    String feature_blob = parser.get<String>("feature_blob");
    String device = parser.get<String>("device");
    int dev_id = parser.get<int>("dev_id");

    std::vector<String> ref_img;
    /* Sample which is most closest in pose to reference image
    *and also the same class.
    */
    ref_img.push_back(ref_img1);
    /* Sample which is less closest in pose to reference image
    *and also the same class.
    */
    ref_img.push_back(ref_img2);
    /* Sample which is very close in pose to reference image
    *but not the same class.
    */
    ref_img.push_back(ref_img3);

    /* Initialize a net work with Device. */
    cv::cnn_3dobj::descriptorExtractor descriptor(device, dev_id);
    /* Load net with the caffe trained net work parameter and structure. */
    if (strcmp(mean_file.c_str(), "no") == 0)
        descriptor.loadNet(network_forIMG, caffemodel);
    else
        descriptor.loadNet(network_forIMG, caffemodel, mean_file);

    cv::Mat img_base = cv::imread(target_img, -1);
    if (img_base.empty())
    {
        printf("could not read reference image %s\n, make sure the path of images are set properly.", target_img.c_str());
    }

    std::vector<cv::Mat> img;
    for (unsigned int i = 0; i < ref_img.size(); i++)
    {
        img.push_back(cv::imread(ref_img[i], -1));
        if (img[i].empty()) {
          printf("could not read reference image %s\n, make sure the path of images are set properly.", ref_img[i].c_str());
        }
    }
    cv::Mat feature_test;
    descriptor.extract(img_base, feature_test, feature_blob);
    if (feature_test.empty()) {
      printf("could not extract feature from test image which is read into cv::Mat.");
    }

    cv::Mat feature_reference;
    descriptor.extract(img, feature_reference, feature_blob);
    if (feature_reference.empty()) {
      printf("could not extract feature from reference images which is already stored in vector<cv::Mat>.");
    }

    std::vector<float> matches;
    for (int i = 0; i < feature_reference.rows; i++)
    {
        cv::Mat distance = feature_test-feature_reference.row(i);
        matches.push_back(cv::norm(distance));
    }
    bool pose_pass = false;
    bool class_pass = false;
    /* Have comparations on the distance between reference image and 3 other images
    *distance between closest sample and reference image should be smallest and
    *distance between sample in another class and reference image should be largest.
    */
    if (matches[0] < matches[1] && matches[0] < matches[2])
        pose_pass = true;
    if (matches[1] < matches[2])
        class_pass = true;
    if (!pose_pass)
    {
        printf("\n =========== Model %s ========== \nIs not trained properly that the similar pose could not be tell from a cluster of features.\n", caffemodel.c_str());
    }
    else if (!class_pass)
    {
        printf("\n =========== Model %s ========== \nIs not trained properly that feature from the same class is not discriminant from the one of another class with similar pose.\n", caffemodel.c_str());
    }
    else
    {
        printf("\n =========== Model %s ========== \nSuits for setting different classes apart and also discriminant on object pose at the same time.\n", caffemodel.c_str());
    }
    return 0;
}
