﻿<?xml version="1.0" encoding="utf-8"?>
<Package xmlns="http://schemas.microsoft.com/appx/2010/manifest" xmlns:m2="http://schemas.microsoft.com/appx/2013/manifest">
  <Identity Name="96635370-3751-48a8-84a6-afd4229cf435" Publisher="CN=dalestam" Version="1.1.0.1" />
  <Properties>
    <DisplayName>OcvImageProcessing</DisplayName>
    <PublisherDisplayName>asmorkalov</PublisherDisplayName>
    <Logo>Assets\StoreLogo.png</Logo>
  </Properties>
  <Prerequisites>
    <OSMinVersion>6.3</OSMinVersion>
    <OSMaxVersionTested>6.3</OSMaxVersionTested>
  </Prerequisites>
  <Resources>
    <Resource Language="x-generate" />
  </Resources>
  <Applications>
    <Application Id="App" Executable="$targetnametoken$.exe" EntryPoint="OcvImageProcessing.App">
      <m2:VisualElements DisplayName="OcvImageProcessing" Description="OcvImageProcessing" BackgroundColor="#464646" ForegroundText="light" Square150x150Logo="Assets\Logo.png" Square30x30Logo="Assets\SmallLogo.png">
        <m2:DefaultTile>
          <m2:ShowNameOnTiles>
            <m2:ShowOn Tile="square150x150Logo" />
          </m2:ShowNameOnTiles>
        </m2:DefaultTile>
        <m2:SplashScreen Image="Assets\SplashScreen.png" />
      </m2:VisualElements>
    </Application>
  </Applications>
</Package>