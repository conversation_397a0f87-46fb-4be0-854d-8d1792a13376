// This file is part of OpenCV project.
// It is subject to the license terms in the LICENSE file found in the top-level directory
// of this distribution and at http://opencv.org/license.html
#include "perf_precomp.hpp"

#include "perf_camera.impl.hpp"

namespace opencv_test
{
using namespace perf;

typedef perf::TestBaseWithParam<std::string> VideoCapture_Reading;

const string bunny_files[] = {
    "highgui/video/big_buck_bunny.avi",
    "highgui/video/big_buck_bunny.mov",
    "highgui/video/big_buck_bunny.mp4",
#ifndef HAVE_MSMF
    // MPEG2 is not supported by Media Foundation yet
    // http://social.msdn.microsoft.com/Forums/en-US/mediafoundationdevelopment/thread/39a36231-8c01-40af-9af5-3c105d684429
    "highgui/video/big_buck_bunny.mpg",
#endif
    "highgui/video/big_buck_bunny.wmv"
};

PERF_TEST_P(VideoCapture_Reading, ReadFile, testing::ValuesIn(bunny_files) )
{
  string filename = getDataPath(GetParam());

  VideoCapture cap;

  TEST_CYCLE() cap.open(filename);

  SANITY_CHECK_NOTHING();
}

} // namespace
