//
// This file is auto-generated. Please don't modify it!
//
#pragma once

#ifdef __cplusplus
//#import "opencv.hpp"
#import "opencv2/features2d.hpp"
#import "D:/AI/opencv/opencv-4.10.0\modules/features2d\include\opencv2\features2d.hpp"
#else
#define CV_EXPORTS
#endif

#import <Foundation/Foundation.h>
#import "Feature2D.h"




// C++: enum FastDetectorType (cv.FastFeatureDetector.DetectorType)
typedef NS_ENUM(int, FastDetectorType) {
    FastFeatureDetector_TYPE_5_8 NS_SWIFT_NAME(TYPE_5_8) = 0,
    FastFeatureDetector_TYPE_7_12 NS_SWIFT_NAME(TYPE_7_12) = 1,
    FastFeatureDetector_TYPE_9_16 NS_SWIFT_NAME(TYPE_9_16) = 2
};



NS_ASSUME_NONNULL_BEGIN

// C++: class FastFeatureDetector
/**
 * Wrapping class for feature detection using the FAST method. :
 *
 * Member of `Features2d`
 */
CV_EXPORTS @interface FastFeatureDetector : Feature2D


#ifdef __cplusplus
@property(readonly)cv::Ptr<cv::FastFeatureDetector> nativePtrFastFeatureDetector;
#endif

#ifdef __cplusplus
- (instancetype)initWithNativePtr:(cv::Ptr<cv::FastFeatureDetector>)nativePtr;
+ (instancetype)fromNative:(cv::Ptr<cv::FastFeatureDetector>)nativePtr;
#endif


#pragma mark - Class Constants


@property (class, readonly) int THRESHOLD NS_SWIFT_NAME(THRESHOLD);
@property (class, readonly) int NONMAX_SUPPRESSION NS_SWIFT_NAME(NONMAX_SUPPRESSION);
@property (class, readonly) int FAST_N NS_SWIFT_NAME(FAST_N);

#pragma mark - Methods


//
// static Ptr_FastFeatureDetector cv::FastFeatureDetector::create(int threshold = 10, bool nonmaxSuppression = true, FastFeatureDetector_DetectorType type = FastFeatureDetector::TYPE_9_16)
//
+ (FastFeatureDetector*)create:(int)threshold nonmaxSuppression:(BOOL)nonmaxSuppression type:(FastDetectorType)type NS_SWIFT_NAME(create(threshold:nonmaxSuppression:type:));

+ (FastFeatureDetector*)create:(int)threshold nonmaxSuppression:(BOOL)nonmaxSuppression NS_SWIFT_NAME(create(threshold:nonmaxSuppression:));

+ (FastFeatureDetector*)create:(int)threshold NS_SWIFT_NAME(create(threshold:));

+ (FastFeatureDetector*)create NS_SWIFT_NAME(create());


//
//  void cv::FastFeatureDetector::setThreshold(int threshold)
//
- (void)setThreshold:(int)threshold NS_SWIFT_NAME(setThreshold(threshold:));


//
//  int cv::FastFeatureDetector::getThreshold()
//
- (int)getThreshold NS_SWIFT_NAME(getThreshold());


//
//  void cv::FastFeatureDetector::setNonmaxSuppression(bool f)
//
- (void)setNonmaxSuppression:(BOOL)f NS_SWIFT_NAME(setNonmaxSuppression(f:));


//
//  bool cv::FastFeatureDetector::getNonmaxSuppression()
//
- (BOOL)getNonmaxSuppression NS_SWIFT_NAME(getNonmaxSuppression());


//
//  void cv::FastFeatureDetector::setType(FastFeatureDetector_DetectorType type)
//
- (void)setType:(FastDetectorType)type NS_SWIFT_NAME(setType(type:));


//
//  FastFeatureDetector_DetectorType cv::FastFeatureDetector::getType()
//
- (FastDetectorType)getType NS_SWIFT_NAME(getType());


//
//  String cv::FastFeatureDetector::getDefaultName()
//
- (NSString*)getDefaultName NS_SWIFT_NAME(getDefaultName());



@end

NS_ASSUME_NONNULL_END


