<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Vulkan guide</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div><div class="header">
  <div class="headertitle"><div class="title">Vulkan guide</div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#vulkan_loader">Finding the Vulkan loader</a></li>
<li class="level1"><a href="#vulkan_include">Including the Vulkan header file</a></li>
<li class="level1"><a href="#vulkan_support">Querying for Vulkan support</a><ul><li class="level2"><a href="#vulkan_proc">Querying Vulkan function pointers</a></li>
</ul>
</li>
<li class="level1"><a href="#vulkan_ext">Querying required Vulkan extensions</a></li>
<li class="level1"><a href="#vulkan_present">Querying for Vulkan presentation support</a></li>
<li class="level1"><a href="#vulkan_window">Creating the window</a></li>
<li class="level1"><a href="#vulkan_surface">Creating a Vulkan window surface</a></li>
</ul>
</div>
<div class="textblock"><p>This guide is intended to fill the gaps between the official <a href="https://www.khronos.org/vulkan/">Vulkan resources</a> and the rest of the GLFW documentation and is not a replacement for either. It assumes some familiarity with Vulkan concepts like loaders, devices, queues and surfaces and leaves it to the Vulkan documentation to explain the details of Vulkan functions.</p>
<p>To develop for Vulkan you should download the <a href="https://vulkan.lunarg.com/">LunarG Vulkan SDK</a> for your platform. Apart from headers and link libraries, they also provide the validation layers necessary for development.</p>
<p>The <a href="https://vulkan-tutorial.com/">Vulkan Tutorial</a> has more information on how to use GLFW and Vulkan. The <a href="https://github.com/KhronosGroup/Vulkan-Samples">Khronos Vulkan Samples</a> also use GLFW, although with a small framework in between.</p>
<p>For details on a specific Vulkan support function, see the <a class="el" href="group__vulkan.html">Vulkan support reference</a>. There are also guides for the other areas of the GLFW API.</p>
<ul>
<li><a class="el" href="intro_guide.html">Introduction to the API</a></li>
<li><a class="el" href="window_guide.html">Window guide</a></li>
<li><a class="el" href="context_guide.html">Context guide</a></li>
<li><a class="el" href="monitor_guide.html">Monitor guide</a></li>
<li><a class="el" href="input_guide.html">Input guide</a></li>
</ul>
<h1><a class="anchor" id="vulkan_loader"></a>
Finding the Vulkan loader</h1>
<p>GLFW itself does not ever need to be linked against the Vulkan loader.</p>
<p>By default, GLFW will load the Vulkan loader dynamically at runtime via its standard name: <code>vulkan-1.dll</code> on Windows, <code>libvulkan.so.1</code> on Linux and other Unix-like systems and <code>libvulkan.1.dylib</code> on macOS.</p>
<p><b>macOS:</b> GLFW will also look up and search the <code>Frameworks</code> subdirectory of your application bundle.</p>
<p>If your code is using a Vulkan loader with a different name or in a non-standard location you will need to direct GLFW to it. Pass your version of <code>vkGetInstanceProcAddr</code> to <a class="el" href="group__init.html#ga76af552d0307bb5f7791f245417d4752">glfwInitVulkanLoader</a> before initializing GLFW and it will use that function for all Vulkan entry point retrieval. This prevents GLFW from dynamically loading the Vulkan loader.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__init.html#ga76af552d0307bb5f7791f245417d4752">glfwInitVulkanLoader</a>(vkGetInstanceProcAddr);</div>
<div class="ttc" id="agroup__init_html_ga76af552d0307bb5f7791f245417d4752"><div class="ttname"><a href="group__init.html#ga76af552d0307bb5f7791f245417d4752">glfwInitVulkanLoader</a></div><div class="ttdeci">void glfwInitVulkanLoader(PFN_vkGetInstanceProcAddr loader)</div><div class="ttdoc">Sets the desired Vulkan vkGetInstanceProcAddr function.</div></div>
</div><!-- fragment --><p><b>macOS:</b> To make your application be redistributable you will need to set up the application bundle according to the LunarG SDK documentation. This is explained in more detail in the <a href="https://vulkan.lunarg.com/doc/sdk/latest/mac/getting_started.html">SDK documentation for macOS</a>.</p>
<h1><a class="anchor" id="vulkan_include"></a>
Including the Vulkan header file</h1>
<p>To have GLFW include the Vulkan header, define <a class="el" href="build_guide.html#GLFW_INCLUDE_VULKAN">GLFW_INCLUDE_VULKAN</a> before including the GLFW header.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#define GLFW_INCLUDE_VULKAN</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3_8h.html">GLFW/glfw3.h</a>&gt;</span></div>
<div class="ttc" id="aglfw3_8h_html"><div class="ttname"><a href="glfw3_8h.html">glfw3.h</a></div><div class="ttdoc">The header of the GLFW 3 API.</div></div>
</div><!-- fragment --><p>If you instead want to include the Vulkan header from a custom location or use your own custom Vulkan header then do this before the GLFW header.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#include &lt;path/to/vulkan.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3_8h.html">GLFW/glfw3.h</a>&gt;</span></div>
</div><!-- fragment --><p>Unless a Vulkan header is included, either by the GLFW header or above it, the following GLFW functions will not be declared, as depend on Vulkan types.</p>
<ul>
<li><a class="el" href="group__init.html#ga76af552d0307bb5f7791f245417d4752">glfwInitVulkanLoader</a></li>
<li><a class="el" href="group__vulkan.html#gadf228fac94c5fd8f12423ec9af9ff1e9">glfwGetInstanceProcAddress</a></li>
<li><a class="el" href="group__vulkan.html#gaff3823355cdd7e2f3f9f4d9ea9518d92">glfwGetPhysicalDevicePresentationSupport</a></li>
<li><a class="el" href="group__vulkan.html#ga1a24536bec3f80b08ead18e28e6ae965">glfwCreateWindowSurface</a></li>
</ul>
<p>The <code>VK_USE_PLATFORM_*_KHR</code> macros do not need to be defined for the Vulkan part of GLFW to work. Define them only if you are using these extensions directly.</p>
<h1><a class="anchor" id="vulkan_support"></a>
Querying for Vulkan support</h1>
<p>If you are linking directly against the Vulkan loader then you can skip this section. The canonical desktop loader library exports all Vulkan core and Khronos extension functions, allowing them to be called directly.</p>
<p>If you are loading the Vulkan loader dynamically instead of linking directly against it, you can check for the availability of a loader and ICD with <a class="el" href="group__vulkan.html#ga2e7f30931e02464b5bc8d0d4b6f9fe2b">glfwVulkanSupported</a>.</p>
<div class="fragment"><div class="line"><span class="keywordflow">if</span> (<a class="code hl_function" href="group__vulkan.html#ga2e7f30931e02464b5bc8d0d4b6f9fe2b">glfwVulkanSupported</a>())</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// Vulkan is available, at least for compute</span></div>
<div class="line">}</div>
<div class="ttc" id="agroup__vulkan_html_ga2e7f30931e02464b5bc8d0d4b6f9fe2b"><div class="ttname"><a href="group__vulkan.html#ga2e7f30931e02464b5bc8d0d4b6f9fe2b">glfwVulkanSupported</a></div><div class="ttdeci">int glfwVulkanSupported(void)</div><div class="ttdoc">Returns whether the Vulkan loader and an ICD have been found.</div></div>
</div><!-- fragment --><p>This function returns <code>GLFW_TRUE</code> if the Vulkan loader and any minimally functional ICD was found.</p>
<p>If one or both were not found, calling any other Vulkan related GLFW function will generate a <a class="el" href="group__errors.html#ga56882b290db23261cc6c053c40c2d08e">GLFW_API_UNAVAILABLE</a> error.</p>
<h2><a class="anchor" id="vulkan_proc"></a>
Querying Vulkan function pointers</h2>
<p>To load any Vulkan core or extension function from the found loader, call <a class="el" href="group__vulkan.html#gadf228fac94c5fd8f12423ec9af9ff1e9">glfwGetInstanceProcAddress</a>. To load functions needed for instance creation, pass <code>NULL</code> as the instance.</p>
<div class="fragment"><div class="line">PFN_vkCreateInstance pfnCreateInstance = (PFN_vkCreateInstance)</div>
<div class="line">    <a class="code hl_function" href="group__vulkan.html#gadf228fac94c5fd8f12423ec9af9ff1e9">glfwGetInstanceProcAddress</a>(NULL, <span class="stringliteral">&quot;vkCreateInstance&quot;</span>);</div>
<div class="ttc" id="agroup__vulkan_html_gadf228fac94c5fd8f12423ec9af9ff1e9"><div class="ttname"><a href="group__vulkan.html#gadf228fac94c5fd8f12423ec9af9ff1e9">glfwGetInstanceProcAddress</a></div><div class="ttdeci">GLFWvkproc glfwGetInstanceProcAddress(VkInstance instance, const char *procname)</div><div class="ttdoc">Returns the address of the specified Vulkan instance function.</div></div>
</div><!-- fragment --><p>Once you have created an instance, you can load from it all other Vulkan core functions and functions from any instance extensions you enabled.</p>
<div class="fragment"><div class="line">PFN_vkCreateDevice pfnCreateDevice = (PFN_vkCreateDevice)</div>
<div class="line">    <a class="code hl_function" href="group__vulkan.html#gadf228fac94c5fd8f12423ec9af9ff1e9">glfwGetInstanceProcAddress</a>(instance, <span class="stringliteral">&quot;vkCreateDevice&quot;</span>);</div>
</div><!-- fragment --><p>This function in turn calls <code>vkGetInstanceProcAddr</code>. If that fails, the function falls back to a platform-specific query of the Vulkan loader (i.e. <code>dlsym</code> or <code>GetProcAddress</code>). If that also fails, the function returns <code>NULL</code>. For more information about <code>vkGetInstanceProcAddr</code>, see the Vulkan documentation.</p>
<p>Vulkan also provides <code>vkGetDeviceProcAddr</code> for loading device-specific versions of Vulkan function. This function can be retrieved from an instance with <a class="el" href="group__vulkan.html#gadf228fac94c5fd8f12423ec9af9ff1e9">glfwGetInstanceProcAddress</a>.</p>
<div class="fragment"><div class="line">PFN_vkGetDeviceProcAddr pfnGetDeviceProcAddr = (PFN_vkGetDeviceProcAddr)</div>
<div class="line">    <a class="code hl_function" href="group__vulkan.html#gadf228fac94c5fd8f12423ec9af9ff1e9">glfwGetInstanceProcAddress</a>(instance, <span class="stringliteral">&quot;vkGetDeviceProcAddr&quot;</span>);</div>
</div><!-- fragment --><p>Device-specific functions may execute a little faster, due to not having to dispatch internally based on the device passed to them. For more information about <code>vkGetDeviceProcAddr</code>, see the Vulkan documentation.</p>
<h1><a class="anchor" id="vulkan_ext"></a>
Querying required Vulkan extensions</h1>
<p>To do anything useful with Vulkan you need to create an instance. If you want to use Vulkan to render to a window, you must enable the instance extensions GLFW requires to create Vulkan surfaces.</p>
<p>To query the instance extensions required, call <a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a>.</p>
<div class="fragment"><div class="line">uint32_t count;</div>
<div class="line"><span class="keyword">const</span> <span class="keywordtype">char</span>** extensions = <a class="code hl_function" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a>(&amp;count);</div>
<div class="ttc" id="agroup__vulkan_html_ga99ad342d82f4a3421e2864978cb6d1d6"><div class="ttname"><a href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a></div><div class="ttdeci">const char ** glfwGetRequiredInstanceExtensions(uint32_t *count)</div><div class="ttdoc">Returns the Vulkan instance extensions required by GLFW.</div></div>
</div><!-- fragment --><p>These extensions must all be enabled when creating instances that are going to be passed to <a class="el" href="group__vulkan.html#gaff3823355cdd7e2f3f9f4d9ea9518d92">glfwGetPhysicalDevicePresentationSupport</a> and <a class="el" href="group__vulkan.html#ga1a24536bec3f80b08ead18e28e6ae965">glfwCreateWindowSurface</a>. The set of extensions will vary depending on platform and may also vary depending on graphics drivers and other factors.</p>
<p>If it fails it will return <code>NULL</code> and GLFW will not be able to create Vulkan window surfaces. You can still use Vulkan for off-screen rendering and compute work.</p>
<p>If successful the returned array will always include <code>VK_KHR_surface</code>, so if you don't require any additional extensions you can pass this list directly to the <code>VkInstanceCreateInfo</code> struct.</p>
<div class="fragment"><div class="line">VkInstanceCreateInfo ici;</div>
<div class="line"> </div>
<div class="line">memset(&amp;ici, 0, <span class="keyword">sizeof</span>(ici));</div>
<div class="line">ici.enabledExtensionCount = count;</div>
<div class="line">ici.ppEnabledExtensionNames = extensions;</div>
<div class="line">...</div>
</div><!-- fragment --><p>Additional extensions may be required by future versions of GLFW. You should check whether any extensions you wish to enable are already in the returned array, as it is an error to specify an extension more than once in the <code>VkInstanceCreateInfo</code> struct.</p>
<p><b>macOS:</b> MoltenVK is (as of July 2022) not yet a fully conformant implementation of Vulkan. As of Vulkan SDK 1.3.216.0, this means you must also enable the <code>VK_KHR_portability_enumeration</code> instance extension and set the <code>VK_INSTANCE_CREATE_ENUMERATE_PORTABILITY_BIT_KHR</code> bit in the instance creation info flags for MoltenVK to show up in the list of physical devices. For more information, see the Vulkan and MoltenVK documentation.</p>
<h1><a class="anchor" id="vulkan_present"></a>
Querying for Vulkan presentation support</h1>
<p>Not every queue family of every Vulkan device can present images to surfaces. To check whether a specific queue family of a physical device supports image presentation without first having to create a window and surface, call <a class="el" href="group__vulkan.html#gaff3823355cdd7e2f3f9f4d9ea9518d92">glfwGetPhysicalDevicePresentationSupport</a>.</p>
<div class="fragment"><div class="line"><span class="keywordflow">if</span> (<a class="code hl_function" href="group__vulkan.html#gaff3823355cdd7e2f3f9f4d9ea9518d92">glfwGetPhysicalDevicePresentationSupport</a>(instance, physical_device, queue_family_index))</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// Queue family supports image presentation</span></div>
<div class="line">}</div>
<div class="ttc" id="agroup__vulkan_html_gaff3823355cdd7e2f3f9f4d9ea9518d92"><div class="ttname"><a href="group__vulkan.html#gaff3823355cdd7e2f3f9f4d9ea9518d92">glfwGetPhysicalDevicePresentationSupport</a></div><div class="ttdeci">int glfwGetPhysicalDevicePresentationSupport(VkInstance instance, VkPhysicalDevice device, uint32_t queuefamily)</div><div class="ttdoc">Returns whether the specified queue family can present images.</div></div>
</div><!-- fragment --><p>The <code>VK_KHR_surface</code> extension additionally provides the <code>vkGetPhysicalDeviceSurfaceSupportKHR</code> function, which performs the same test on an existing Vulkan surface.</p>
<h1><a class="anchor" id="vulkan_window"></a>
Creating the window</h1>
<p>Unless you will be using OpenGL or OpenGL ES with the same window as Vulkan, there is no need to create a context. You can disable context creation with the <a class="el" href="window_guide.html#GLFW_CLIENT_API_hint">GLFW_CLIENT_API</a> hint.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#ga649309cf72a3d3de5b1348ca7936c95b">GLFW_CLIENT_API</a>, <a class="code hl_define" href="glfw3_8h.html#a8f6dcdc968d214ff14779564f1389264">GLFW_NO_API</a>);</div>
<div class="line"><a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window = <a class="code hl_function" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>(640, 480, <span class="stringliteral">&quot;Window Title&quot;</span>, NULL, NULL);</div>
<div class="ttc" id="aglfw3_8h_html_a8f6dcdc968d214ff14779564f1389264"><div class="ttname"><a href="glfw3_8h.html#a8f6dcdc968d214ff14779564f1389264">GLFW_NO_API</a></div><div class="ttdeci">#define GLFW_NO_API</div><div class="ttdef"><b>Definition</b> glfw3.h:1140</div></div>
<div class="ttc" id="agroup__window_html_ga3555a418df92ad53f917597fe2f64aeb"><div class="ttname"><a href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a></div><div class="ttdeci">GLFWwindow * glfwCreateWindow(int width, int height, const char *title, GLFWmonitor *monitor, GLFWwindow *share)</div><div class="ttdoc">Creates a window and its associated context.</div></div>
<div class="ttc" id="agroup__window_html_ga3c96d80d363e67d13a41b5d1821f3242"><div class="ttname"><a href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a></div><div class="ttdeci">struct GLFWwindow GLFWwindow</div><div class="ttdoc">Opaque window object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1403</div></div>
<div class="ttc" id="agroup__window_html_ga649309cf72a3d3de5b1348ca7936c95b"><div class="ttname"><a href="group__window.html#ga649309cf72a3d3de5b1348ca7936c95b">GLFW_CLIENT_API</a></div><div class="ttdeci">#define GLFW_CLIENT_API</div><div class="ttdoc">Context client API hint and attribute.</div><div class="ttdef"><b>Definition</b> glfw3.h:1031</div></div>
<div class="ttc" id="agroup__window_html_ga7d9c8c62384b1e2821c4dc48952d2033"><div class="ttname"><a href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a></div><div class="ttdeci">void glfwWindowHint(int hint, int value)</div><div class="ttdoc">Sets the specified window hint to the desired value.</div></div>
</div><!-- fragment --><p>See <a class="el" href="context_guide.html#context_less">Windows without contexts</a> for more information.</p>
<h1><a class="anchor" id="vulkan_surface"></a>
Creating a Vulkan window surface</h1>
<p>You can create a Vulkan surface (as defined by the <code>VK_KHR_surface</code> extension) for a GLFW window with <a class="el" href="group__vulkan.html#ga1a24536bec3f80b08ead18e28e6ae965">glfwCreateWindowSurface</a>.</p>
<div class="fragment"><div class="line">VkSurfaceKHR surface;</div>
<div class="line">VkResult err = <a class="code hl_function" href="group__vulkan.html#ga1a24536bec3f80b08ead18e28e6ae965">glfwCreateWindowSurface</a>(instance, window, NULL, &amp;surface);</div>
<div class="line"><span class="keywordflow">if</span> (err)</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// Window surface creation failed</span></div>
<div class="line">}</div>
<div class="ttc" id="agroup__vulkan_html_ga1a24536bec3f80b08ead18e28e6ae965"><div class="ttname"><a href="group__vulkan.html#ga1a24536bec3f80b08ead18e28e6ae965">glfwCreateWindowSurface</a></div><div class="ttdeci">VkResult glfwCreateWindowSurface(VkInstance instance, GLFWwindow *window, const VkAllocationCallbacks *allocator, VkSurfaceKHR *surface)</div><div class="ttdoc">Creates a Vulkan surface for the specified window.</div></div>
</div><!-- fragment --><p>If an OpenGL or OpenGL ES context was created on the window, the context has ownership of the presentation on the window and a Vulkan surface cannot be created.</p>
<p>It is your responsibility to destroy the surface. GLFW does not destroy it for you. Call <code>vkDestroySurfaceKHR</code> function from the same extension to destroy it. </p>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
