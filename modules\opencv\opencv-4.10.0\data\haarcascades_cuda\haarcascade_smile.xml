<?xml version="1.0"?>
<!--
  Smile detector
  Contributed by <PERSON>
  More information can be found at http://visilab.etsii.uclm.es/personas/oscar/oscar.html

//////////////////////////////////////////////////////////////////////////
| Contributors License Agreement
| IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
|   By downloading, copying, installing or using the software you agree
|   to this license.
|   If you do not agree to this license, do not download, install,
|   copy or use the software.
|
| Copyright (c) 2011, Modesto Castrillon-Santana (IUSIANI, Universidad de
| Las Palmas de Gran Canaria, Spain).
|  All rights reserved.
|
| Redistribution and use in source and binary forms, with or without
| modification, are permitted provided that the following conditions are
| met:
|
|    * Redistributions of source code must retain the above copyright
|       notice, this list of conditions and the following disclaimer.
|    * Redistributions in binary form must reproduce the above
|      copyright notice, this list of conditions and the following
|      disclaimer in the documentation and/or other materials provided
|      with the distribution.
|    * The name of Contributor may not used to endorse or promote products
|      derived from this software without specific prior written permission.
|
| THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
| "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
| LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
| A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
| CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
| EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
| PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
| PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
| LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
| NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
| SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.  Back to
| Top
//////////////////////////////////////////////////////////////////////////

-->
<opencv_storage>
<!-- Automatically converted from data/classifier, window size = 36x18 -->
<SmileDetector type_id="opencv-haar-classifier">
  <size>
    36 18</size>
  <stages>
    <_>
      <!-- stage 0 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 2 4 -1.</_>
                <_>
                  0 2 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.8783610691316426e-004</threshold>
            <left_val>0.5921934843063355</left_val>
            <right_val>-0.4416360855102539</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 10 2 8 -1.</_>
                <_>
                  34 14 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.2209611274302006e-004</threshold>
            <left_val>0.3031865060329437</left_val>
            <right_val>-0.3291291892528534</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 10 2 8 -1.</_>
                <_>
                  0 14 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.9940118333324790e-004</threshold>
            <left_val>0.4856331050395966</left_val>
            <right_val>-0.4292306005954742</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 0 18 10 -1.</_>
                <_>
                  24 0 9 5 2.</_>
                <_>
                  15 5 9 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0372891984879971</threshold>
            <left_val>-0.2866730093955994</left_val>
            <right_val>0.5997999906539917</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 4 4 -1.</_>
                <_>
                  7 0 2 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.4334049774333835e-003</threshold>
            <left_val>-0.3489313125610352</left_val>
            <right_val>0.4048275053501129</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 5 6 4 -1.</_>
                <_>
                  15 6 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.7213020995259285e-003</threshold>
            <left_val>0.7571418881416321</left_val>
            <right_val>-0.1222594976425171</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 6 8 3 -1.</_>
                <_>
                  13 7 8 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.1067271530628204e-003</threshold>
            <left_val>-0.1665772050619125</left_val>
            <right_val>0.7509614825248718</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 6 8 4 -1.</_>
                <_>
                  14 7 8 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.7238711528480053e-003</threshold>
            <left_val>0.6266279220581055</left_val>
            <right_val>-0.1912745982408524</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 10 2 8 -1.</_>
                <_>
                  0 14 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.4225031160749495e-004</threshold>
            <left_val>-0.2394447028636932</left_val>
            <right_val>0.4484061896800995</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 0 2 16 -1.</_>
                <_>
                  35 0 1 8 2.</_>
                <_>
                  34 8 1 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6867710510268807e-003</threshold>
            <left_val>-0.1843906939029694</left_val>
            <right_val>0.0917824134230614</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 4 7 -1.</_>
                <_>
                  3 0 2 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0146256200969219</threshold>
            <left_val>0.1616805940866470</left_val>
            <right_val>-0.8150117993354797</right_val></_></_></trees>
      <stage_threshold>-1.2678639888763428</stage_threshold>
      <parent>-1</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 1 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 7 28 3 -1.</_>
                <_>
                  11 7 14 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0381411388516426</threshold>
            <left_val>-0.3327588140964508</left_val>
            <right_val>0.7783334255218506</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 0 2 2 -1.</_>
                <_>
                  34 1 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3136120105627924e-004</threshold>
            <left_val>0.3635309040546417</left_val>
            <right_val>-0.3204346895217896</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 4 6 -1.</_>
                <_>
                  0 15 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.8757019210606813e-003</threshold>
            <left_val>0.7135239243507385</left_val>
            <right_val>-0.3518598973751068</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 0 2 2 -1.</_>
                <_>
                  34 1 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.4266290236264467e-003</threshold>
            <left_val>0.0681008473038673</left_val>
            <right_val>-0.6172732710838318</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 2 2 -1.</_>
                <_>
                  0 1 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4605958606116474e-004</threshold>
            <left_val>0.5727149844169617</left_val>
            <right_val>-0.3786099851131439</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 5 9 12 -1.</_>
                <_>
                  20 5 3 12 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0318226404488087</threshold>
            <left_val>-0.6348456144332886</left_val>
            <right_val>0.1164183989167213</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 5 9 12 -1.</_>
                <_>
                  13 5 3 12 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0171309504657984</threshold>
            <left_val>-0.6279314756393433</left_val>
            <right_val>0.3247947096824646</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 32 1 -1.</_>
                <_>
                  4 0 16 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.3903783708810806e-003</threshold>
            <left_val>-0.2757895886898041</left_val>
            <right_val>0.2233072966337204</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 3 3 -1.</_>
                <_>
                  1 0 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.2802520543336868e-003</threshold>
            <left_val>0.1897764056921005</left_val>
            <right_val>-0.6881762146949768</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  32 7 4 7 -1.</_>
                <_>
                  33 8 2 7 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.6840099599212408e-003</threshold>
            <left_val>-0.2235050052404404</left_val>
            <right_val>0.1372579932212830</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 8 6 -1.</_>
                <_>
                  7 0 4 3 2.</_>
                <_>
                  11 3 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0106046395376325</threshold>
            <left_val>-0.2142623066902161</left_val>
            <right_val>0.5620787143707275</right_val></_></_></trees>
      <stage_threshold>-1.5844069719314575</stage_threshold>
      <parent>0</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 2 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 2 2 -1.</_>
                <_>
                  0 1 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.1677199876867235e-004</threshold>
            <left_val>0.4659548103809357</left_val>
            <right_val>-0.3742581903934479</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  27 1 8 9 -1.</_>
                <_>
                  29 3 4 9 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0551206283271313</threshold>
            <left_val>0.5417978763580322</left_val>
            <right_val>-0.2265765070915222</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 10 1 8 -1.</_>
                <_>
                  1 14 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.4742640824988484e-004</threshold>
            <left_val>0.3770307004451752</left_val>
            <right_val>-0.3348644077777863</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 6 30 9 -1.</_>
                <_>
                  13 9 10 3 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.3950783908367157</threshold>
            <left_val>-0.1814441978931427</left_val>
            <right_val>0.8132591843605042</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 5 8 6 -1.</_>
                <_>
                  12 7 8 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0405094102025032</threshold>
            <left_val>-0.0953694134950638</left_val>
            <right_val>0.8059561848640442</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 4 6 3 -1.</_>
                <_>
                  16 5 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.8735421150922775e-003</threshold>
            <left_val>-0.1402366012334824</left_val>
            <right_val>0.6164302825927734</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 2 18 -1.</_>
                <_>
                  0 0 1 9 2.</_>
                <_>
                  1 9 1 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0105780400335789</threshold>
            <left_val>0.1293267011642456</left_val>
            <right_val>-0.7482334971427918</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 2 2 14 -1.</_>
                <_>
                  35 2 1 7 2.</_>
                <_>
                  34 9 1 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.2986393719911575e-003</threshold>
            <left_val>0.0589406006038189</left_val>
            <right_val>-0.4410730004310608</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 2 2 14 -1.</_>
                <_>
                  0 2 1 7 2.</_>
                <_>
                  1 9 1 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.0301607698202133e-003</threshold>
            <left_val>-0.6630973219871521</left_val>
            <right_val>0.1810476928949356</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  35 0 1 4 -1.</_>
                <_>
                  35 2 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0947990085696802e-004</threshold>
            <left_val>0.2211259007453919</left_val>
            <right_val>-0.2730903923511505</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 24 18 -1.</_>
                <_>
                  5 0 12 9 2.</_>
                <_>
                  17 9 12 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1168550997972488</threshold>
            <left_val>-0.7720596790313721</left_val>
            <right_val>0.1248165965080261</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  35 16 1 2 -1.</_>
                <_>
                  35 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.3603649828583002e-005</threshold>
            <left_val>0.1367060989141464</left_val>
            <right_val>-0.1612793952226639</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 1 2 -1.</_>
                <_>
                  0 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5056360280141234e-004</threshold>
            <left_val>0.4486046135425568</left_val>
            <right_val>-0.2171128988265991</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 6 8 12 -1.</_>
                <_>
                  19 6 4 12 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0163946095854044</threshold>
            <left_val>-0.6582735180854797</left_val>
            <right_val>0.1674550026655197</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 5 8 13 -1.</_>
                <_>
                  13 5 4 13 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0144828604534268</threshold>
            <left_val>-0.6834514737129211</left_val>
            <right_val>0.1345615983009338</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  35 16 1 2 -1.</_>
                <_>
                  35 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.9269471017178148e-005</threshold>
            <left_val>-0.1499813944101334</left_val>
            <right_val>0.1601772010326386</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 9 12 3 -1.</_>
                <_>
                  10 10 12 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.4323131702840328e-003</threshold>
            <left_val>-0.1684845983982086</left_val>
            <right_val>0.5396398901939392</right_val></_></_></trees>
      <stage_threshold>-1.3820559978485107</stage_threshold>
      <parent>1</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 3 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 10 1 8 -1.</_>
                <_>
                  0 14 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.3472499237395823e-004</threshold>
            <left_val>0.4394924044609070</left_val>
            <right_val>-0.4224875867366791</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  20 0 10 10 -1.</_>
                <_>
                  25 0 5 5 2.</_>
                <_>
                  20 5 5 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0329953208565712</threshold>
            <left_val>-0.1979825049638748</left_val>
            <right_val>0.5953487157821655</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 1 4 -1.</_>
                <_>
                  0 2 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.1011828579939902e-004</threshold>
            <left_val>0.4440306127071381</left_val>
            <right_val>-0.3074846863746643</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 0 13 18 -1.</_>
                <_>
                  19 9 13 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0819697380065918</threshold>
            <left_val>-0.5333436727523804</left_val>
            <right_val>0.1671810001134872</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 14 6 -1.</_>
                <_>
                  4 0 7 3 2.</_>
                <_>
                  11 3 7 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0177787002176046</threshold>
            <left_val>-0.2045017927885056</left_val>
            <right_val>0.5144413113594055</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 5 6 6 -1.</_>
                <_>
                  16 7 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0228346996009350</threshold>
            <left_val>-0.1484607011079788</left_val>
            <right_val>0.5624278783798218</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 7 7 8 -1.</_>
                <_>
                  13 9 7 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0386043414473534</threshold>
            <left_val>-0.1273147016763687</left_val>
            <right_val>0.8149448037147522</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  33 0 3 1 -1.</_>
                <_>
                  34 0 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.3286908445879817e-004</threshold>
            <left_val>-0.3719344139099121</left_val>
            <right_val>0.0676164999604225</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 1 10 4 -1.</_>
                <_>
                  6 2 10 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0232290402054787</threshold>
            <left_val>0.7123206257820129</left_val>
            <right_val>-0.1158939003944397</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 2 6 16 -1.</_>
                <_>
                  18 2 3 8 2.</_>
                <_>
                  15 10 3 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0195753592997789</threshold>
            <left_val>-0.6899073123931885</left_val>
            <right_val>0.1399950981140137</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 10 1 8 -1.</_>
                <_>
                  0 14 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.1991271427832544e-004</threshold>
            <left_val>-0.1835464984178543</left_val>
            <right_val>0.4943555891513825</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  27 4 6 6 -1.</_>
                <_>
                  29 6 2 6 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0570897497236729</threshold>
            <left_val>0.6260784864425659</left_val>
            <right_val>-0.0785768479108810</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 5 8 8 -1.</_>
                <_>
                  16 5 4 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0256996992975473</threshold>
            <left_val>0.1155714020133019</left_val>
            <right_val>-0.8193519115447998</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  27 5 6 6 -1.</_>
                <_>
                  29 7 2 6 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0325796194374561</threshold>
            <left_val>-0.1176773980259895</left_val>
            <right_val>0.4277622103691101</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 5 6 6 -1.</_>
                <_>
                  7 7 6 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0205922499299049</threshold>
            <left_val>0.4868524074554443</left_val>
            <right_val>-0.2131853997707367</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 5 12 9 -1.</_>
                <_>
                  15 5 6 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0174852795898914</threshold>
            <left_val>-0.5228734016418457</left_val>
            <right_val>0.1339704990386963</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 3 1 -1.</_>
                <_>
                  1 0 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.9153228327631950e-004</threshold>
            <left_val>0.0963044911623001</left_val>
            <right_val>-0.6886307001113892</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 4 18 6 -1.</_>
                <_>
                  15 6 18 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0575339011847973</threshold>
            <left_val>-0.0870805233716965</left_val>
            <right_val>0.4048064947128296</right_val></_></_></trees>
      <stage_threshold>-1.3879380226135254</stage_threshold>
      <parent>2</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 4 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 10 1 6 -1.</_>
                <_>
                  0 13 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.6606198884546757e-004</threshold>
            <left_val>0.4277374148368835</left_val>
            <right_val>-0.3542076945304871</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 6 30 6 -1.</_>
                <_>
                  13 8 10 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.3055455982685089</threshold>
            <left_val>-0.1639281064271927</left_val>
            <right_val>0.8606523275375366</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 7 12 4 -1.</_>
                <_>
                  11 8 12 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0114494003355503</threshold>
            <left_val>0.5972732901573181</left_val>
            <right_val>-0.2323434054851532</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 8 9 3 -1.</_>
                <_>
                  14 9 9 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.3891541212797165e-003</threshold>
            <left_val>-0.1291541010141373</left_val>
            <right_val>0.6105204224586487</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 8 7 4 -1.</_>
                <_>
                  14 9 7 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.4334248676896095e-003</threshold>
            <left_val>0.4792853891849518</left_val>
            <right_val>-0.1900272965431213</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 7 18 6 -1.</_>
                <_>
                  12 9 18 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0538089312613010</threshold>
            <left_val>-0.1149377003312111</left_val>
            <right_val>0.5339453816413879</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 8 3 10 -1.</_>
                <_>
                  7 13 3 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.7580219688825309e-004</threshold>
            <left_val>-0.3459854125976563</left_val>
            <right_val>0.2548804879188538</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  35 10 1 6 -1.</_>
                <_>
                  35 13 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3450840197037905e-004</threshold>
            <left_val>0.2241459041833878</left_val>
            <right_val>-0.1955007016658783</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 10 1 6 -1.</_>
                <_>
                  0 13 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0016911700367928e-004</threshold>
            <left_val>-0.1972054988145828</left_val>
            <right_val>0.4967764019966126</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 13 9 5 -1.</_>
                <_>
                  21 13 3 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0150632699951530</threshold>
            <left_val>0.1063077002763748</left_val>
            <right_val>-0.4113821089267731</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 9 6 4 -1.</_>
                <_>
                  15 10 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.7588870190083981e-003</threshold>
            <left_val>-0.1537311971187592</left_val>
            <right_val>0.4893161952495575</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 4 18 8 -1.</_>
                <_>
                  16 6 18 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0454101189970970</threshold>
            <left_val>-0.0735593065619469</left_val>
            <right_val>0.2773792147636414</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 14 9 3 -1.</_>
                <_>
                  12 14 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0145996697247028</threshold>
            <left_val>-0.7096682786941528</left_val>
            <right_val>0.0975155606865883</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  32 0 4 6 -1.</_>
                <_>
                  32 0 2 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0172360707074404</threshold>
            <left_val>0.0168695393949747</left_val>
            <right_val>-0.5738832950592041</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 4 6 -1.</_>
                <_>
                  2 0 2 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0142307104542851</threshold>
            <left_val>0.0947145000100136</left_val>
            <right_val>-0.7839525938034058</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  27 0 6 7 -1.</_>
                <_>
                  29 2 2 7 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0437068603932858</threshold>
            <left_val>0.6097965240478516</left_val>
            <right_val>-0.1560188978910446</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 1 4 -1.</_>
                <_>
                  0 2 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.2343222089111805e-004</threshold>
            <left_val>0.3485119044780731</left_val>
            <right_val>-0.2170491069555283</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  27 8 6 4 -1.</_>
                <_>
                  29 10 2 4 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0192450508475304</threshold>
            <left_val>-0.1171097978949547</left_val>
            <right_val>0.3070116043090820</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 9 27 6 -1.</_>
                <_>
                  13 11 9 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2703577876091003</threshold>
            <left_val>-0.0900964364409447</left_val>
            <right_val>0.7665696144104004</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  31 14 2 3 -1.</_>
                <_>
                  31 14 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.5394480801187456e-004</threshold>
            <left_val>-0.2002478986978531</left_val>
            <right_val>0.1249336004257202</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 0 5 6 -1.</_>
                <_>
                  8 2 5 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0360139608383179</threshold>
            <left_val>0.6702855825424194</left_val>
            <right_val>-0.1057187989354134</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 7 11 3 -1.</_>
                <_>
                  14 8 11 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.2952791601419449e-003</threshold>
            <left_val>-0.1057471036911011</left_val>
            <right_val>0.4509387910366058</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 2 6 -1.</_>
                <_>
                  0 15 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.3304709359072149e-004</threshold>
            <left_val>0.2793382108211517</left_val>
            <right_val>-0.2457676976919174</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 13 2 4 -1.</_>
                <_>
                  34 15 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9147620807634667e-005</threshold>
            <left_val>0.0858138129115105</left_val>
            <right_val>-0.0954695865511894</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 13 2 4 -1.</_>
                <_>
                  0 15 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.4382669148035347e-004</threshold>
            <left_val>-0.2022008001804352</left_val>
            <right_val>0.5454357862472534</right_val></_></_></trees>
      <stage_threshold>-1.3538850545883179</stage_threshold>
      <parent>3</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 5 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 6 4 12 -1.</_>
                <_>
                  3 10 4 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.9610757529735565e-003</threshold>
            <left_val>-0.3672207891941071</left_val>
            <right_val>0.4315434992313385</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 0 22 12 -1.</_>
                <_>
                  25 0 11 6 2.</_>
                <_>
                  14 6 11 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0633948296308517</threshold>
            <left_val>-0.2073971033096314</left_val>
            <right_val>0.5742601752281189</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 1 7 6 -1.</_>
                <_>
                  6 3 7 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0531933493912220</threshold>
            <left_val>0.7255092263221741</left_val>
            <right_val>-0.1434202045202255</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 5 14 3 -1.</_>
                <_>
                  12 6 14 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0154607696458697</threshold>
            <left_val>-0.0960538163781166</left_val>
            <right_val>0.7578523755073547</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 6 7 4 -1.</_>
                <_>
                  6 7 7 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0176431406289339</threshold>
            <left_val>0.6681562066078186</left_val>
            <right_val>-0.1417672932147980</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 3 6 4 -1.</_>
                <_>
                  18 4 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.5065636560320854e-003</threshold>
            <left_val>-0.0962597429752350</left_val>
            <right_val>0.4699633121490479</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 5 5 6 -1.</_>
                <_>
                  4 7 5 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.0446049533784389e-003</threshold>
            <left_val>-0.1973251998424530</left_val>
            <right_val>0.4283801019191742</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  33 0 3 4 -1.</_>
                <_>
                  34 0 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.2312041148543358e-003</threshold>
            <left_val>0.1186169013381004</left_val>
            <right_val>-0.6103963255882263</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 0 6 18 -1.</_>
                <_>
                  9 9 6 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0401590503752232</threshold>
            <left_val>-0.4166434109210968</left_val>
            <right_val>0.2167232930660248</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 6 24 6 -1.</_>
                <_>
                  14 8 8 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2852425873279572</threshold>
            <left_val>-0.1043575033545494</left_val>
            <right_val>0.8573396801948547</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 8 4 4 -1.</_>
                <_>
                  16 9 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.9264221452176571e-003</threshold>
            <left_val>0.4706046879291534</left_val>
            <right_val>-0.1399745941162109</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 8 13 4 -1.</_>
                <_>
                  13 9 13 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0137817002832890</threshold>
            <left_val>-0.1271356940269470</left_val>
            <right_val>0.4461891949176788</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 2 2 -1.</_>
                <_>
                  0 17 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.9873598618432879e-004</threshold>
            <left_val>0.4702663123607636</left_val>
            <right_val>-0.1548373997211456</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  35 14 1 4 -1.</_>
                <_>
                  35 15 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5621389320585877e-004</threshold>
            <left_val>0.1885481029748917</left_val>
            <right_val>-0.0778397768735886</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 14 1 4 -1.</_>
                <_>
                  0 15 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.7597760092467070e-004</threshold>
            <left_val>0.5769770145416260</left_val>
            <right_val>-0.1335622072219849</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 6 9 7 -1.</_>
                <_>
                  18 6 3 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0106659103184938</threshold>
            <left_val>-0.4106529951095581</left_val>
            <right_val>0.1556212007999420</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 3 4 -1.</_>
                <_>
                  1 0 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.4135230816900730e-003</threshold>
            <left_val>-0.7636343240737915</left_val>
            <right_val>0.1020964980125427</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 16 2 2 -1.</_>
                <_>
                  35 16 1 1 2.</_>
                <_>
                  34 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.6471868447260931e-005</threshold>
            <left_val>-0.1644393056631088</left_val>
            <right_val>0.2290841937065125</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 2 2 -1.</_>
                <_>
                  0 16 1 1 2.</_>
                <_>
                  1 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.1611599368043244e-004</threshold>
            <left_val>-0.1629032939672470</left_val>
            <right_val>0.4575636088848114</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  22 0 10 4 -1.</_>
                <_>
                  22 0 5 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0108227198943496</threshold>
            <left_val>-0.2446253001689911</left_val>
            <right_val>0.1388894021511078</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 4 6 14 -1.</_>
                <_>
                  15 4 3 7 2.</_>
                <_>
                  18 11 3 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0150849102064967</threshold>
            <left_val>-0.5781347751617432</left_val>
            <right_val>0.1156411990523338</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 3 8 10 -1.</_>
                <_>
                  17 3 4 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0257159601897001</threshold>
            <left_val>0.0396311990916729</left_val>
            <right_val>-0.6527001261711121</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 2 5 -1.</_>
                <_>
                  1 0 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.6093570049852133e-003</threshold>
            <left_val>0.1142188981175423</left_val>
            <right_val>-0.5680108070373535</right_val></_></_></trees>
      <stage_threshold>-1.3707510232925415</stage_threshold>
      <parent>4</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 6 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 1 8 6 -1.</_>
                <_>
                  5 3 8 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0518619008362293</threshold>
            <left_val>0.7043117284774780</left_val>
            <right_val>-0.2214370071887970</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 0 11 18 -1.</_>
                <_>
                  19 9 11 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0503416284918785</threshold>
            <left_val>-0.4639782905578613</left_val>
            <right_val>0.2804746031761169</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 8 24 6 -1.</_>
                <_>
                  14 10 8 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2570973038673401</threshold>
            <left_val>-0.1312427967786789</left_val>
            <right_val>0.8239594101905823</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 6 10 3 -1.</_>
                <_>
                  14 7 10 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0110318996012211</threshold>
            <left_val>-0.1425814032554627</left_val>
            <right_val>0.6382390260696411</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 7 11 4 -1.</_>
                <_>
                  12 8 11 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0185650903731585</threshold>
            <left_val>-0.1512387990951538</left_val>
            <right_val>0.5988119244575501</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 0 16 6 -1.</_>
                <_>
                  26 0 8 3 2.</_>
                <_>
                  18 3 8 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0175023507326841</threshold>
            <left_val>-0.1261979937553406</left_val>
            <right_val>0.3817803859710693</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 3 7 3 -1.</_>
                <_>
                  4 4 7 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>7.2723729535937309e-003</threshold>
            <left_val>-0.1510328948497772</left_val>
            <right_val>0.5812842249870300</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 4 4 4 -1.</_>
                <_>
                  18 5 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.1504750996828079e-003</threshold>
            <left_val>-0.0654647573828697</left_val>
            <right_val>0.5639755129814148</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 3 10 4 -1.</_>
                <_>
                  4 4 10 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0185527391731739</threshold>
            <left_val>0.5315709710121155</left_val>
            <right_val>-0.1252657026052475</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 8 8 10 -1.</_>
                <_>
                  18 8 4 5 2.</_>
                <_>
                  14 13 4 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0231014806777239</threshold>
            <left_val>-0.6794939041137695</left_val>
            <right_val>0.1104625985026360</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 0 4 1 -1.</_>
                <_>
                  5 0 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.8539339362177998e-004</threshold>
            <left_val>0.3010003864765167</left_val>
            <right_val>-0.2120669931173325</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  20 0 10 8 -1.</_>
                <_>
                  25 0 5 4 2.</_>
                <_>
                  20 4 5 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0173191204667091</threshold>
            <left_val>-0.0937381312251091</left_val>
            <right_val>0.2100856006145477</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 0 10 8 -1.</_>
                <_>
                  13 0 5 4 2.</_>
                <_>
                  18 4 5 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0143056204542518</threshold>
            <left_val>0.1800594925880432</left_val>
            <right_val>-0.3977671861648560</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  21 5 6 13 -1.</_>
                <_>
                  23 5 2 13 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0257633402943611</threshold>
            <left_val>8.7056998163461685e-003</left_val>
            <right_val>-0.6289495229721069</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 5 6 13 -1.</_>
                <_>
                  11 5 2 13 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0153833404183388</threshold>
            <left_val>-0.5341547131538391</left_val>
            <right_val>0.1038073003292084</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  27 5 5 3 -1.</_>
                <_>
                  27 6 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0605469578877091e-003</threshold>
            <left_val>-0.0901285186409950</left_val>
            <right_val>0.1679212003946304</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 0 3 6 -1.</_>
                <_>
                  10 2 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.5230729263275862e-003</threshold>
            <left_val>-0.1711069047451019</left_val>
            <right_val>0.3259654045104981</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  26 6 3 6 -1.</_>
                <_>
                  26 8 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0107892798259854</threshold>
            <left_val>0.3610992133617401</left_val>
            <right_val>-0.0663391500711441</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 11 36 7 -1.</_>
                <_>
                  18 11 18 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2795093953609467</threshold>
            <left_val>-0.0746058970689774</left_val>
            <right_val>0.7336987853050232</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  27 5 5 3 -1.</_>
                <_>
                  27 6 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.8369540125131607e-003</threshold>
            <left_val>0.0448735393583775</left_val>
            <right_val>-0.1860270053148270</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 5 5 3 -1.</_>
                <_>
                  4 6 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.6195949865505099e-003</threshold>
            <left_val>-0.1392249017953873</left_val>
            <right_val>0.4343700110912323</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  28 6 4 4 -1.</_>
                <_>
                  29 7 2 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0116479499265552</threshold>
            <left_val>-0.0743575915694237</left_val>
            <right_val>0.5420144200325012</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 15 8 2 -1.</_>
                <_>
                  16 15 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.9066400863230228e-003</threshold>
            <left_val>-0.7055758833885193</left_val>
            <right_val>0.0864336192607880</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 5 30 6 -1.</_>
                <_>
                  13 7 10 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.3968684077262878</threshold>
            <left_val>-0.0748983696103096</left_val>
            <right_val>0.9406285881996155</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 7 16 6 -1.</_>
                <_>
                  6 9 16 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0576637797057629</threshold>
            <left_val>-0.0965584069490433</left_val>
            <right_val>0.5418242812156677</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 10 12 6 -1.</_>
                <_>
                  14 12 12 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0603195689618587</threshold>
            <left_val>-0.0665010735392571</left_val>
            <right_val>0.6402354836463928</right_val></_></_></trees>
      <stage_threshold>-1.3303329944610596</stage_threshold>
      <parent>5</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 7 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 12 10 -1.</_>
                <_>
                  6 0 6 5 2.</_>
                <_>
                  12 5 6 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0190502498298883</threshold>
            <left_val>-0.4443340897560120</left_val>
            <right_val>0.4394856989383698</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  25 2 7 16 -1.</_>
                <_>
                  25 10 7 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0201983004808426</threshold>
            <left_val>-0.3170621991157532</left_val>
            <right_val>0.1043293029069901</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 6 18 7 -1.</_>
                <_>
                  15 6 6 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0214780308306217</threshold>
            <left_val>-0.3502483963966370</left_val>
            <right_val>0.2635537087917328</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 26 18 -1.</_>
                <_>
                  18 0 13 9 2.</_>
                <_>
                  5 9 13 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1018775999546051</threshold>
            <left_val>-0.5988957881927490</left_val>
            <right_val>0.1768579930067062</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 6 10 3 -1.</_>
                <_>
                  10 7 10 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0109741603955626</threshold>
            <left_val>-0.1489523947238922</left_val>
            <right_val>0.6011521816253662</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 6 6 4 -1.</_>
                <_>
                  17 7 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0114767104387283</threshold>
            <left_val>0.4066570997238159</left_val>
            <right_val>-0.1240468993782997</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 6 6 7 -1.</_>
                <_>
                  18 6 3 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0234311502426863</threshold>
            <left_val>-0.7148783206939697</left_val>
            <right_val>0.1427811980247498</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  26 6 5 4 -1.</_>
                <_>
                  26 7 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.4963559806346893e-003</threshold>
            <left_val>-0.1704585999250412</left_val>
            <right_val>0.1719308048486710</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 1 6 -1.</_>
                <_>
                  0 15 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.4855772759765387e-004</threshold>
            <left_val>0.3155323863029480</left_val>
            <right_val>-0.2144445031881332</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 4 18 14 -1.</_>
                <_>
                  18 4 9 7 2.</_>
                <_>
                  9 11 9 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0749126300215721</threshold>
            <left_val>0.0912405624985695</left_val>
            <right_val>-0.6395121216773987</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 5 6 3 -1.</_>
                <_>
                  6 6 6 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>6.8816398270428181e-003</threshold>
            <left_val>-0.1490440964698792</left_val>
            <right_val>0.4795236885547638</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  27 5 6 3 -1.</_>
                <_>
                  29 7 2 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0382125787436962</threshold>
            <left_val>0.5288773775100708</left_val>
            <right_val>-0.0618947297334671</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 8 3 3 -1.</_>
                <_>
                  6 9 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.4051730073988438e-003</threshold>
            <left_val>-0.1193412989377976</left_val>
            <right_val>0.5061342120170593</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  28 5 6 5 -1.</_>
                <_>
                  30 7 2 5 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0239668991416693</threshold>
            <left_val>-0.0897205099463463</left_val>
            <right_val>0.3315277993679047</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 5 5 6 -1.</_>
                <_>
                  6 7 5 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0341629907488823</threshold>
            <left_val>0.5313478112220764</left_val>
            <right_val>-0.1466650068759918</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  31 0 4 1 -1.</_>
                <_>
                  31 0 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.9642219413071871e-003</threshold>
            <left_val>0.0907835885882378</left_val>
            <right_val>-0.4303255975246429</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 4 1 -1.</_>
                <_>
                  3 0 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.6757910796441138e-005</threshold>
            <left_val>0.2255253940820694</left_val>
            <right_val>-0.2822071015834808</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 11 4 3 -1.</_>
                <_>
                  17 12 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.2862399239093065e-003</threshold>
            <left_val>0.4051502048969269</left_val>
            <right_val>-0.1177619993686676</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 3 7 4 -1.</_>
                <_>
                  12 4 7 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0116883097216487</threshold>
            <left_val>-0.0918571278452873</left_val>
            <right_val>0.6283488869667053</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 9 9 3 -1.</_>
                <_>
                  14 10 9 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.0287420637905598e-003</threshold>
            <left_val>0.3926180899143219</left_val>
            <right_val>-0.1228715032339096</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 17 21 1 -1.</_>
                <_>
                  8 17 7 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0137213403359056</threshold>
            <left_val>-0.5529879927635193</left_val>
            <right_val>0.0910412818193436</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 9 20 4 -1.</_>
                <_>
                  12 9 10 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0756266415119171</threshold>
            <left_val>-0.0449295900762081</left_val>
            <right_val>0.1744275987148285</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 9 22 4 -1.</_>
                <_>
                  14 9 11 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0934344828128815</threshold>
            <left_val>-0.0845939517021179</left_val>
            <right_val>0.6013116240501404</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  25 0 3 3 -1.</_>
                <_>
                  26 1 1 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>5.8748829178512096e-003</threshold>
            <left_val>-0.0441314987838268</left_val>
            <right_val>0.3956570923328400</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 9 4 3 -1.</_>
                <_>
                  14 10 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.0064537897706032e-003</threshold>
            <left_val>-0.1141439974308014</left_val>
            <right_val>0.3792538046836853</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 4 9 3 -1.</_>
                <_>
                  22 4 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0229454599320889</threshold>
            <left_val>0.0246731899678707</left_val>
            <right_val>-0.4152199923992157</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 4 9 3 -1.</_>
                <_>
                  11 4 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0128104602918029</threshold>
            <left_val>-0.5155742764472961</left_val>
            <right_val>0.0913196131587029</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 15 36 3 -1.</_>
                <_>
                  12 16 12 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2042552977800369</threshold>
            <left_val>-0.0659275427460670</left_val>
            <right_val>0.7594249248504639</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 4 2 -1.</_>
                <_>
                  2 0 4 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.9796327948570251e-003</threshold>
            <left_val>0.1080627962946892</left_val>
            <right_val>-0.5001627206802368</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 9 2 9 -1.</_>
                <_>
                  19 12 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0283976309001446</threshold>
            <left_val>-0.0371529608964920</left_val>
            <right_val>0.5401064753532410</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 7 8 3 -1.</_>
                <_>
                  13 8 8 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.0867150314152241e-003</threshold>
            <left_val>-0.1197860985994339</left_val>
            <right_val>0.3569226861000061</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  30 4 2 2 -1.</_>
                <_>
                  31 4 1 1 2.</_>
                <_>
                  30 5 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.1456899412441999e-004</threshold>
            <left_val>0.1874015033245087</left_val>
            <right_val>-0.0884172022342682</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 4 2 2 -1.</_>
                <_>
                  4 4 1 1 2.</_>
                <_>
                  5 5 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.8941858909092844e-004</threshold>
            <left_val>-0.1259797960519791</left_val>
            <right_val>0.3998227119445801</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 7 4 3 -1.</_>
                <_>
                  18 8 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3047619722783566e-003</threshold>
            <left_val>0.1549997031688690</left_val>
            <right_val>-0.0753860473632813</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 0 1 8 -1.</_>
                <_>
                  9 0 1 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0129750100895762</threshold>
            <left_val>-0.5534411072731018</left_val>
            <right_val>0.0823542475700378</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  25 6 10 3 -1.</_>
                <_>
                  25 7 10 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.7442410401999950e-003</threshold>
            <left_val>0.0276998002082109</left_val>
            <right_val>-0.3483599126338959</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 6 10 3 -1.</_>
                <_>
                  1 7 10 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.4850629270076752e-003</threshold>
            <left_val>-0.1297612935304642</left_val>
            <right_val>0.3790883123874664</right_val></_></_></trees>
      <stage_threshold>-1.5300060510635376</stage_threshold>
      <parent>6</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 8 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 6 14 12 -1.</_>
                <_>
                  6 6 7 6 2.</_>
                <_>
                  13 12 7 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0403868816792965</threshold>
            <left_val>0.5960354804992676</left_val>
            <right_val>-0.3574176132678986</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  31 14 3 4 -1.</_>
                <_>
                  31 16 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.6068649175576866e-005</threshold>
            <left_val>0.4462898075580597</left_val>
            <right_val>-0.3595947027206421</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 12 2 4 -1.</_>
                <_>
                  1 14 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.7622239906340837e-003</threshold>
            <left_val>0.1794701963663101</left_val>
            <right_val>-0.7563151121139526</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 0 12 5 -1.</_>
                <_>
                  19 0 4 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0309677198529243</threshold>
            <left_val>-0.2884705066680908</left_val>
            <right_val>0.0768705308437347</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 0 8 14 -1.</_>
                <_>
                  12 0 4 14 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0305665601044893</threshold>
            <left_val>0.1400360018014908</left_val>
            <right_val>-0.7175536751747131</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  28 1 8 7 -1.</_>
                <_>
                  30 3 4 7 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>9.9054910242557526e-004</threshold>
            <left_val>0.0829155892133713</left_val>
            <right_val>-0.2919717133045197</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 14 20 4 -1.</_>
                <_>
                  8 14 10 2 2.</_>
                <_>
                  18 16 10 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0125777004286647</threshold>
            <left_val>0.1538071930408478</left_val>
            <right_val>-0.4688293039798737</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 11 24 3 -1.</_>
                <_>
                  14 12 8 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1239292025566101</threshold>
            <left_val>-0.0908238589763641</left_val>
            <right_val>0.7383757233619690</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 5 27 6 -1.</_>
                <_>
                  13 7 9 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.3773748874664307</threshold>
            <left_val>-0.0542329512536526</left_val>
            <right_val>0.9229121804237366</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 22 18 -1.</_>
                <_>
                  18 0 11 9 2.</_>
                <_>
                  7 9 11 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1099637001752853</threshold>
            <left_val>0.0915962681174278</left_val>
            <right_val>-0.6597716808319092</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 3 2 -1.</_>
                <_>
                  16 1 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2721329694613814e-003</threshold>
            <left_val>0.3347575068473816</left_val>
            <right_val>-0.1829068958759308</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 17 36 1 -1.</_>
                <_>
                  9 17 18 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0469062514603138</threshold>
            <left_val>-0.0839710533618927</left_val>
            <right_val>0.6984758973121643</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 5 12 1 -1.</_>
                <_>
                  5 5 6 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.2869930146262050e-004</threshold>
            <left_val>0.1879463046789169</left_val>
            <right_val>-0.2929005920886993</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 15 2 1 -1.</_>
                <_>
                  34 15 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.7333080177195370e-004</threshold>
            <left_val>-0.2696416079998016</left_val>
            <right_val>0.3494757115840912</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 8 16 4 -1.</_>
                <_>
                  7 9 16 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0198009591549635</threshold>
            <left_val>-0.1467922925949097</left_val>
            <right_val>0.4399561882019043</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  35 10 1 6 -1.</_>
                <_>
                  35 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.0056760695297271e-004</threshold>
            <left_val>-0.1372741013765335</left_val>
            <right_val>0.2221331000328064</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 8 3 4 -1.</_>
                <_>
                  13 9 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4923149719834328e-003</threshold>
            <left_val>0.3473525941371918</left_val>
            <right_val>-0.1594821065664291</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  35 10 1 6 -1.</_>
                <_>
                  35 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.2736999603221193e-005</threshold>
            <left_val>0.3152787089347839</left_val>
            <right_val>-0.2306694984436035</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 0 1 4 -1.</_>
                <_>
                  11 1 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>6.6625140607357025e-004</threshold>
            <left_val>-0.2013110071420670</left_val>
            <right_val>0.2869189083576202</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  35 10 1 6 -1.</_>
                <_>
                  35 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.3850460163666867e-005</threshold>
            <left_val>-0.2021923959255219</left_val>
            <right_val>0.2307330965995789</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 0 1 14 -1.</_>
                <_>
                  18 0 1 7 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0409726314246655</threshold>
            <left_val>0.0795431807637215</left_val>
            <right_val>-0.8079563975334168</right_val></_></_></trees>
      <stage_threshold>-1.4114329814910889</stage_threshold>
      <parent>7</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 9 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 6 16 12 -1.</_>
                <_>
                  5 6 8 6 2.</_>
                <_>
                  13 12 8 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0469829291105270</threshold>
            <left_val>0.7082253098487854</left_val>
            <right_val>-0.3703424036502838</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 1 7 8 -1.</_>
                <_>
                  16 3 7 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.5753079727292061e-004</threshold>
            <left_val>-0.1255030930042267</left_val>
            <right_val>0.1394442021846771</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 4 8 10 -1.</_>
                <_>
                  14 4 4 5 2.</_>
                <_>
                  18 9 4 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0153272999450564</threshold>
            <left_val>0.2161353975534439</left_val>
            <right_val>-0.5629395246505737</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  22 0 9 3 -1.</_>
                <_>
                  25 0 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0181470401585102</threshold>
            <left_val>-0.0320796482264996</left_val>
            <right_val>0.3234755992889404</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 10 26 8 -1.</_>
                <_>
                  0 10 13 4 2.</_>
                <_>
                  13 14 13 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0473471917212009</threshold>
            <left_val>-0.1738158017396927</left_val>
            <right_val>0.5758044719696045</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 10 16 8 -1.</_>
                <_>
                  23 10 8 4 2.</_>
                <_>
                  15 14 8 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0598379410803318</threshold>
            <left_val>0.4779787063598633</left_val>
            <right_val>-0.1026028022170067</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 24 18 -1.</_>
                <_>
                  6 0 12 9 2.</_>
                <_>
                  18 9 12 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0527967996895313</threshold>
            <left_val>-0.4798848927021027</left_val>
            <right_val>0.1878775954246521</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 0 9 6 -1.</_>
                <_>
                  21 0 3 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0243854299187660</threshold>
            <left_val>-0.3084166944026947</left_val>
            <right_val>8.7605630978941917e-003</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 0 9 6 -1.</_>
                <_>
                  12 0 3 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0252883005887270</threshold>
            <left_val>0.1391403973102570</left_val>
            <right_val>-0.7109494209289551</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  30 1 5 14 -1.</_>
                <_>
                  30 8 5 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0216124504804611</threshold>
            <left_val>-0.2328253984451294</left_val>
            <right_val>0.0809946805238724</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 1 5 14 -1.</_>
                <_>
                  1 8 5 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.4023479092866182e-003</threshold>
            <left_val>-0.2298990041017532</left_val>
            <right_val>0.3788951039314270</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 8 26 6 -1.</_>
                <_>
                  23 8 13 3 2.</_>
                <_>
                  10 11 13 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1127460002899170</threshold>
            <left_val>-0.0154747096821666</left_val>
            <right_val>0.5703054070472717</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 28 6 -1.</_>
                <_>
                  0 8 14 3 2.</_>
                <_>
                  14 11 14 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0345168709754944</threshold>
            <left_val>-0.1230008006095886</left_val>
            <right_val>0.5677536725997925</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 0 24 12 -1.</_>
                <_>
                  24 0 12 6 2.</_>
                <_>
                  12 6 12 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0789848119020462</threshold>
            <left_val>-0.1424216926097870</left_val>
            <right_val>0.4694185853004456</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 1 14 2 -1.</_>
                <_>
                  3 1 14 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0153778595849872</threshold>
            <left_val>0.6394686102867127</left_val>
            <right_val>-0.1123619005084038</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  33 16 3 2 -1.</_>
                <_>
                  33 17 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2373620595317334e-004</threshold>
            <left_val>0.5558329820632935</left_val>
            <right_val>-0.2724758088588715</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 0 9 14 -1.</_>
                <_>
                  15 0 3 14 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0247623901814222</threshold>
            <left_val>-0.5040485858917236</left_val>
            <right_val>0.1407779008150101</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  28 16 8 2 -1.</_>
                <_>
                  32 16 4 1 2.</_>
                <_>
                  28 17 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.4061157142277807e-005</threshold>
            <left_val>0.3719528019428253</left_val>
            <right_val>-0.2250299006700516</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 8 6 6 -1.</_>
                <_>
                  15 10 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0202563591301441</threshold>
            <left_val>0.5105100870132446</left_val>
            <right_val>-0.1429875940084457</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 6 22 6 -1.</_>
                <_>
                  24 6 11 3 2.</_>
                <_>
                  13 9 11 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0481228791177273</threshold>
            <left_val>-0.0669795125722885</left_val>
            <right_val>0.3662230968475342</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 10 26 4 -1.</_>
                <_>
                  0 10 13 2 2.</_>
                <_>
                  13 12 13 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0237878002226353</threshold>
            <left_val>0.5081325173377991</left_val>
            <right_val>-0.1290815025568008</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  24 16 4 2 -1.</_>
                <_>
                  24 17 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0520319920033216e-003</threshold>
            <left_val>-0.1560467034578323</left_val>
            <right_val>0.0662133172154427</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 16 3 2 -1.</_>
                <_>
                  9 17 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6640200521796942e-003</threshold>
            <left_val>-0.7254558205604553</left_val>
            <right_val>0.0823654532432556</right_val></_></_></trees>
      <stage_threshold>-1.3777890205383301</stage_threshold>
      <parent>8</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 10 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 7 18 8 -1.</_>
                <_>
                  3 7 9 4 2.</_>
                <_>
                  12 11 9 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0502246208488941</threshold>
            <left_val>0.7084565758705139</left_val>
            <right_val>-0.2558549940586090</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  23 0 8 4 -1.</_>
                <_>
                  23 0 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0140728699043393</threshold>
            <left_val>0.0630331784486771</left_val>
            <right_val>-0.0598385296761990</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 8 4 -1.</_>
                <_>
                  9 0 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0178040098398924</threshold>
            <left_val>0.1941471993923187</left_val>
            <right_val>-0.5844426751136780</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 10 24 3 -1.</_>
                <_>
                  14 11 8 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1304673999547958</threshold>
            <left_val>-0.1151698008179665</left_val>
            <right_val>0.8504030108451843</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 5 5 6 -1.</_>
                <_>
                  5 7 5 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0175068005919456</threshold>
            <left_val>-0.2071896940469742</left_val>
            <right_val>0.4643828868865967</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 16 26 2 -1.</_>
                <_>
                  18 16 13 1 2.</_>
                <_>
                  5 17 13 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.4240020476281643e-003</threshold>
            <left_val>-0.6656516790390015</left_val>
            <right_val>0.1403498947620392</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 7 24 4 -1.</_>
                <_>
                  0 7 12 2 2.</_>
                <_>
                  12 9 12 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0345711186528206</threshold>
            <left_val>0.6511297821998596</left_val>
            <right_val>-0.1490191966295242</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  23 14 13 4 -1.</_>
                <_>
                  23 15 13 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.2270249687135220e-003</threshold>
            <left_val>-1.6027219826355577e-003</left_val>
            <right_val>0.3895606100559235</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 10 18 8 -1.</_>
                <_>
                  2 10 9 4 2.</_>
                <_>
                  11 14 9 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0506620407104492</threshold>
            <left_val>0.5803576707839966</left_val>
            <right_val>-0.1514143943786621</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 10 6 4 -1.</_>
                <_>
                  15 11 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0715770125389099e-003</threshold>
            <left_val>0.5300896763801575</left_val>
            <right_val>-0.1449830979108810</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 6 24 2 -1.</_>
                <_>
                  0 6 12 1 2.</_>
                <_>
                  12 7 12 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0118635101243854</threshold>
            <left_val>0.6729742288589478</left_val>
            <right_val>-0.1106354966759682</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 0 18 18 -1.</_>
                <_>
                  17 9 18 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0605200305581093</threshold>
            <left_val>-0.3316448926925659</left_val>
            <right_val>0.2119556069374085</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 11 2 -1.</_>
                <_>
                  1 1 11 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.7340779826045036e-003</threshold>
            <left_val>-0.6941440105438232</left_val>
            <right_val>0.0727053135633469</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 6 8 12 -1.</_>
                <_>
                  19 6 4 6 2.</_>
                <_>
                  15 12 4 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0324861407279968</threshold>
            <left_val>-0.5185081958770752</left_val>
            <right_val>0.0592126213014126</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 1 32 12 -1.</_>
                <_>
                  2 1 16 6 2.</_>
                <_>
                  18 7 16 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0832797065377235</threshold>
            <left_val>0.1206794008612633</left_val>
            <right_val>-0.5309563279151917</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  29 10 7 8 -1.</_>
                <_>
                  29 12 7 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.8782817581668496e-004</threshold>
            <left_val>-0.2737655937671661</left_val>
            <right_val>0.2716251909732819</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 2 8 10 -1.</_>
                <_>
                  12 2 4 5 2.</_>
                <_>
                  16 7 4 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0175391808152199</threshold>
            <left_val>-0.5690230131149292</left_val>
            <right_val>0.1228737011551857</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 12 6 4 -1.</_>
                <_>
                  15 13 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.8226347900927067e-003</threshold>
            <left_val>0.4386585950851440</left_val>
            <right_val>-0.1493742018938065</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 8 6 -1.</_>
                <_>
                  0 14 8 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0100575601682067</threshold>
            <left_val>-0.6616886258125305</left_val>
            <right_val>0.1144542992115021</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 9 26 8 -1.</_>
                <_>
                  23 9 13 4 2.</_>
                <_>
                  10 13 13 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0903454273939133</threshold>
            <left_val>-0.0666652470827103</left_val>
            <right_val>0.2870647907257080</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 8 22 10 -1.</_>
                <_>
                  7 8 11 5 2.</_>
                <_>
                  18 13 11 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0675872936844826</threshold>
            <left_val>-0.5363761186599731</left_val>
            <right_val>0.1123751997947693</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 9 8 3 -1.</_>
                <_>
                  14 10 8 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.1747528165578842e-003</threshold>
            <left_val>0.4434241950511932</left_val>
            <right_val>-0.1297765970230103</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 3 4 9 -1.</_>
                <_>
                  11 6 4 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0115505503490567</threshold>
            <left_val>0.3273158073425293</left_val>
            <right_val>-0.1700761020183563</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  29 14 2 2 -1.</_>
                <_>
                  29 14 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.7406829283572733e-004</threshold>
            <left_val>0.1327867954969406</left_val>
            <right_val>-0.1081293970346451</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 13 8 3 -1.</_>
                <_>
                  14 14 8 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.6040047891438007e-003</threshold>
            <left_val>-0.1226582005620003</left_val>
            <right_val>0.4412580132484436</right_val></_></_></trees>
      <stage_threshold>-1.3266400098800659</stage_threshold>
      <parent>9</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 11 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 3 7 8 -1.</_>
                <_>
                  9 5 7 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0469432808458805</threshold>
            <left_val>0.6094344258308411</left_val>
            <right_val>-0.2637800872325897</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  28 13 1 4 -1.</_>
                <_>
                  28 13 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.6899159527383745e-004</threshold>
            <left_val>0.1665875017642975</left_val>
            <right_val>-0.1254196017980576</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 13 4 1 -1.</_>
                <_>
                  8 13 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.7983370237052441e-003</threshold>
            <left_val>0.1905744969844818</left_val>
            <right_val>-0.6568077206611633</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 9 4 3 -1.</_>
                <_>
                  16 10 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.0413960814476013e-003</threshold>
            <left_val>-0.1731746941804886</left_val>
            <right_val>0.6362075209617615</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 8 10 4 -1.</_>
                <_>
                  13 9 10 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.6033362895250320e-003</threshold>
            <left_val>0.6025841832160950</left_val>
            <right_val>-0.2316936999559403</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 8 8 3 -1.</_>
                <_>
                  14 9 8 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.8247945532202721e-003</threshold>
            <left_val>-0.1756583005189896</left_val>
            <right_val>0.7104166746139526</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 10 6 2 -1.</_>
                <_>
                  4 12 2 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-9.2786159366369247e-003</threshold>
            <left_val>-0.6890857219696045</left_val>
            <right_val>0.1789650022983551</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 10 6 3 -1.</_>
                <_>
                  16 11 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.0826768167316914e-003</threshold>
            <left_val>-0.1706372052431107</left_val>
            <right_val>0.5375748276710510</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 5 8 13 -1.</_>
                <_>
                  12 5 4 13 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0390073694288731</threshold>
            <left_val>-0.6834635734558106</left_val>
            <right_val>0.1441708058118820</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 36 8 -1.</_>
                <_>
                  18 0 18 4 2.</_>
                <_>
                  0 4 18 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0703379511833191</threshold>
            <left_val>-0.6508566737174988</left_val>
            <right_val>0.1008547991514206</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 5 8 12 -1.</_>
                <_>
                  1 5 4 6 2.</_>
                <_>
                  5 11 4 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0331666991114616</threshold>
            <left_val>-0.1932571977376938</left_val>
            <right_val>0.4779865145683289</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 8 18 10 -1.</_>
                <_>
                  27 8 9 5 2.</_>
                <_>
                  18 13 9 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0752889066934586</threshold>
            <left_val>-0.0695677325129509</left_val>
            <right_val>0.4125064909458160</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 18 10 -1.</_>
                <_>
                  0 8 9 5 2.</_>
                <_>
                  9 13 9 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0705017298460007</threshold>
            <left_val>0.7157300710678101</left_val>
            <right_val>-0.1022270023822784</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 5 14 3 -1.</_>
                <_>
                  11 6 14 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0122494902461767</threshold>
            <left_val>-0.1061242967844009</left_val>
            <right_val>0.6295958161354065</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 6 16 6 -1.</_>
                <_>
                  10 8 16 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0706446766853333</threshold>
            <left_val>-0.0973746329545975</left_val>
            <right_val>0.6762204170227051</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 2 24 16 -1.</_>
                <_>
                  19 2 12 8 2.</_>
                <_>
                  7 10 12 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1624888032674789</threshold>
            <left_val>0.0527133606374264</left_val>
            <right_val>-0.8494657278060913</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 18 15 -1.</_>
                <_>
                  6 6 6 5 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1380825042724609</threshold>
            <left_val>0.1406479030847549</left_val>
            <right_val>-0.4764721095561981</right_val></_></_></trees>
      <stage_threshold>-1.4497200250625610</stage_threshold>
      <parent>10</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 12 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 5 16 6 -1.</_>
                <_>
                  12 5 8 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0418823398649693</threshold>
            <left_val>-0.8077452778816223</left_val>
            <right_val>0.2640967071056366</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  29 0 6 11 -1.</_>
                <_>
                  31 2 2 11 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0536229908466339</threshold>
            <left_val>0.5580704212188721</left_val>
            <right_val>-0.2498968988656998</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 8 9 1 -1.</_>
                <_>
                  5 11 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>9.3709938228130341e-003</threshold>
            <left_val>0.2650170028209686</left_val>
            <right_val>-0.5990694761276245</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 6 17 3 -1.</_>
                <_>
                  10 7 17 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0139097301289439</threshold>
            <left_val>-0.1470918059349060</left_val>
            <right_val>0.7354667186737061</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 6 6 2 -1.</_>
                <_>
                  20 8 2 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0190035700798035</threshold>
            <left_val>-0.1887511014938355</left_val>
            <right_val>0.7487422227859497</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 11 12 3 -1.</_>
                <_>
                  13 12 12 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.9199850074946880e-003</threshold>
            <left_val>-0.1599563956260681</left_val>
            <right_val>0.5673577785491943</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 3 8 8 -1.</_>
                <_>
                  2 3 4 4 2.</_>
                <_>
                  6 7 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0247051399201155</threshold>
            <left_val>0.7556992173194885</left_val>
            <right_val>-0.1235088035464287</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 12 18 4 -1.</_>
                <_>
                  27 12 9 2 2.</_>
                <_>
                  18 14 9 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0160583592951298</threshold>
            <left_val>-0.1282460987567902</left_val>
            <right_val>0.5129454731941223</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 5 11 3 -1.</_>
                <_>
                  11 6 11 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.8288700208067894e-003</threshold>
            <left_val>-0.1686663925647736</left_val>
            <right_val>0.6152185201644898</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 7 14 4 -1.</_>
                <_>
                  14 8 14 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0175563395023346</threshold>
            <left_val>-0.1090169996023178</left_val>
            <right_val>0.5803176164627075</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 8 16 10 -1.</_>
                <_>
                  9 8 8 5 2.</_>
                <_>
                  17 13 8 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0421881191432476</threshold>
            <left_val>0.1486624032258987</left_val>
            <right_val>-0.6922233104705811</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 17 2 1 -1.</_>
                <_>
                  18 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0687207840383053e-004</threshold>
            <left_val>0.0315808691084385</left_val>
            <right_val>-0.3700995147228241</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 10 5 3 -1.</_>
                <_>
                  13 11 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.7651190757751465e-003</threshold>
            <left_val>-0.2133754044771195</left_val>
            <right_val>0.4704301059246063</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 17 2 1 -1.</_>
                <_>
                  18 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2231520377099514e-003</threshold>
            <left_val>-0.7818967103958130</left_val>
            <right_val>0.0209542606025934</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 5 8 3 -1.</_>
                <_>
                  6 6 8 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>8.5432287305593491e-003</threshold>
            <left_val>-0.1455352008342743</left_val>
            <right_val>0.6789504289627075</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 17 2 1 -1.</_>
                <_>
                  18 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.0657219283748418e-004</threshold>
            <left_val>0.2437624037265778</left_val>
            <right_val>-0.0675588026642799</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 5 5 3 -1.</_>
                <_>
                  10 6 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.6798270195722580e-003</threshold>
            <left_val>0.6684169769287109</left_val>
            <right_val>-0.1388788074254990</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 5 34 10 -1.</_>
                <_>
                  19 5 17 5 2.</_>
                <_>
                  2 10 17 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1220175996422768</threshold>
            <left_val>0.1102816015481949</left_val>
            <right_val>-0.7530742287635803</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 2 12 3 -1.</_>
                <_>
                  6 5 6 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0204043406993151</threshold>
            <left_val>0.1645383983850479</left_val>
            <right_val>-0.5223162174224854</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  35 6 1 6 -1.</_>
                <_>
                  35 8 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.0343370791524649e-004</threshold>
            <left_val>-0.1301285028457642</left_val>
            <right_val>0.2635852992534638</right_val></_></_></trees>
      <stage_threshold>-1.4622910022735596</stage_threshold>
      <parent>11</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 13 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 6 13 6 -1.</_>
                <_>
                  10 8 13 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0727917104959488</threshold>
            <left_val>-0.1372790038585663</left_val>
            <right_val>0.8291574716567993</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 5 6 4 -1.</_>
                <_>
                  15 6 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.5939209200441837e-003</threshold>
            <left_val>-0.1678012013435364</left_val>
            <right_val>0.5683972239494324</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 2 11 4 -1.</_>
                <_>
                  4 3 11 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0235623903572559</threshold>
            <left_val>0.6500560045242310</left_val>
            <right_val>-0.1424535065889359</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  26 6 10 6 -1.</_>
                <_>
                  31 6 5 3 2.</_>
                <_>
                  26 9 5 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0173929501324892</threshold>
            <left_val>-0.1529144942760468</left_val>
            <right_val>0.3425354063510895</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 7 11 8 -1.</_>
                <_>
                  10 9 11 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0718258023262024</threshold>
            <left_val>-0.0991311371326447</left_val>
            <right_val>0.8279678821563721</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  28 2 4 9 -1.</_>
                <_>
                  29 3 2 9 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0136738000437617</threshold>
            <left_val>-0.0417872704565525</left_val>
            <right_val>0.5078148245811462</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 2 10 4 -1.</_>
                <_>
                  7 3 10 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0285859592258930</threshold>
            <left_val>0.7011532187461853</left_val>
            <right_val>-0.1314471065998077</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  31 0 5 2 -1.</_>
                <_>
                  31 1 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.1845720261335373e-004</threshold>
            <left_val>0.2845467031002045</left_val>
            <right_val>-0.3123202919960022</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 6 16 12 -1.</_>
                <_>
                  10 10 16 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0520956814289093</threshold>
            <left_val>0.4181294143199921</left_val>
            <right_val>-0.1699313074350357</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 4 4 3 -1.</_>
                <_>
                  18 5 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.2256329432129860e-003</threshold>
            <left_val>-0.0904662087559700</left_val>
            <right_val>0.3008623123168945</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 10 6 6 -1.</_>
                <_>
                  11 12 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0347716398537159</threshold>
            <left_val>-0.0842167884111404</left_val>
            <right_val>0.7801663875579834</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  35 8 1 10 -1.</_>
                <_>
                  35 13 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3356630224734545e-003</threshold>
            <left_val>0.3316453099250794</left_val>
            <right_val>-0.1696092039346695</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 10 36 8 -1.</_>
                <_>
                  18 10 18 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2510198056697846</threshold>
            <left_val>-0.1392046958208084</left_val>
            <right_val>0.6633893251419067</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 7 6 8 -1.</_>
                <_>
                  19 7 3 4 2.</_>
                <_>
                  16 11 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.9689997732639313e-003</threshold>
            <left_val>-0.3713817000389099</left_val>
            <right_val>0.1290012001991272</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 6 8 4 -1.</_>
                <_>
                  7 6 4 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0143037298694253</threshold>
            <left_val>0.1572919934988022</left_val>
            <right_val>-0.5093821287155151</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  21 11 4 3 -1.</_>
                <_>
                  21 12 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0856059901416302e-003</threshold>
            <left_val>0.4656791090965271</left_val>
            <right_val>-0.0662708207964897</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 9 1 8 -1.</_>
                <_>
                  0 13 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.6260809176601470e-004</threshold>
            <left_val>0.2933731079101563</left_val>
            <right_val>-0.2333986014127731</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  27 7 6 4 -1.</_>
                <_>
                  29 9 2 4 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0344354808330536</threshold>
            <left_val>0.7002474069595337</left_val>
            <right_val>-0.1013351008296013</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 14 8 4 -1.</_>
                <_>
                  12 14 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.2570890188217163e-003</threshold>
            <left_val>-0.5628641247749329</left_val>
            <right_val>0.1314862072467804</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 17 2 1 -1.</_>
                <_>
                  18 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.8352940939366817e-004</threshold>
            <left_val>0.0262274891138077</left_val>
            <right_val>-0.2605080008506775</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 4 11 4 -1.</_>
                <_>
                  10 5 11 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0129999397322536</threshold>
            <left_val>0.5311700105667114</left_val>
            <right_val>-0.1202305033802986</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 12 2 4 -1.</_>
                <_>
                  17 13 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0009329998865724e-003</threshold>
            <left_val>0.3964129984378815</left_val>
            <right_val>-0.1599515974521637</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 4 5 3 -1.</_>
                <_>
                  13 5 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.1314200498163700e-003</threshold>
            <left_val>-0.1492992043495178</left_val>
            <right_val>0.4295912086963654</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 12 11 2 -1.</_>
                <_>
                  13 13 11 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.7364455685019493e-003</threshold>
            <left_val>-0.1127102002501488</left_val>
            <right_val>0.4945647120475769</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 16 2 2 -1.</_>
                <_>
                  1 16 1 1 2.</_>
                <_>
                  2 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.6352869463153183e-004</threshold>
            <left_val>-0.1212491989135742</left_val>
            <right_val>0.4943937957286835</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  27 7 6 4 -1.</_>
                <_>
                  29 9 2 4 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0538859590888023</threshold>
            <left_val>0.7035598754882813</left_val>
            <right_val>-0.0132305501028895</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 7 6 6 -1.</_>
                <_>
                  4 9 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.2885672301054001e-003</threshold>
            <left_val>-0.1754055023193359</left_val>
            <right_val>0.3567946851253510</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  30 6 4 5 -1.</_>
                <_>
                  31 7 2 5 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>7.9539399594068527e-003</threshold>
            <left_val>-0.0998840034008026</left_val>
            <right_val>0.3137167096138001</right_val></_></_></trees>
      <stage_threshold>-1.3885619640350342</stage_threshold>
      <parent>12</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 14 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 5 20 7 -1.</_>
                <_>
                  13 5 10 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0567523688077927</threshold>
            <left_val>-0.3257648050785065</left_val>
            <right_val>0.3737593889236450</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  30 2 3 12 -1.</_>
                <_>
                  30 8 3 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.0906039327383041e-003</threshold>
            <left_val>-0.1391862928867340</left_val>
            <right_val>0.1503984034061432</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 2 12 4 -1.</_>
                <_>
                  4 2 12 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0412988215684891</threshold>
            <left_val>0.4702607989311218</left_val>
            <right_val>-0.1617936044931412</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 36 6 -1.</_>
                <_>
                  12 10 12 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.4775018990039825</threshold>
            <left_val>-0.1006157994270325</left_val>
            <right_val>0.7635074257850647</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 5 30 6 -1.</_>
                <_>
                  13 7 10 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.4226649105548859</threshold>
            <left_val>-0.0351909101009369</left_val>
            <right_val>0.8303126096725464</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 4 12 9 -1.</_>
                <_>
                  18 4 4 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0330318994820118</threshold>
            <left_val>-0.3750554919242859</left_val>
            <right_val>0.0489026196300983</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 17 6 1 -1.</_>
                <_>
                  3 17 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1923770216526464e-004</threshold>
            <left_val>-0.2661466896533966</left_val>
            <right_val>0.2234652042388916</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 0 1 2 -1.</_>
                <_>
                  34 0 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.2101400904357433e-003</threshold>
            <left_val>8.7575968354940414e-003</left_val>
            <right_val>-0.5938351750373840</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 2 1 -1.</_>
                <_>
                  2 0 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.3337279455736279e-004</threshold>
            <left_val>-0.2122765928506851</left_val>
            <right_val>0.2473503947257996</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  31 3 3 8 -1.</_>
                <_>
                  32 4 1 8 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0117938900366426</threshold>
            <left_val>-0.0689979493618011</left_val>
            <right_val>0.5898082852363586</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 6 26 12 -1.</_>
                <_>
                  5 6 13 6 2.</_>
                <_>
                  18 12 13 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1143207997083664</threshold>
            <left_val>-0.7733368277549744</left_val>
            <right_val>0.0628622919321060</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 4 12 9 -1.</_>
                <_>
                  18 4 4 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0824010074138641</threshold>
            <left_val>0.0168252792209387</left_val>
            <right_val>-0.6170011758804321</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 7 10 10 -1.</_>
                <_>
                  13 7 5 5 2.</_>
                <_>
                  18 12 5 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0181261505931616</threshold>
            <left_val>0.0995334684848785</left_val>
            <right_val>-0.3830915987491608</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  30 5 4 6 -1.</_>
                <_>
                  31 6 2 6 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>8.9282449334859848e-003</threshold>
            <left_val>-0.1010973975062370</left_val>
            <right_val>0.2948305010795593</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 5 6 4 -1.</_>
                <_>
                  5 6 6 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0174371004104614</threshold>
            <left_val>0.4614987075328827</left_val>
            <right_val>-0.1050636023283005</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  29 5 4 5 -1.</_>
                <_>
                  30 6 2 5 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0112803103402257</threshold>
            <left_val>0.4561164975166321</left_val>
            <right_val>-0.1013116016983986</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 5 5 4 -1.</_>
                <_>
                  6 6 5 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>7.0190089754760265e-003</threshold>
            <left_val>-0.1368626952171326</left_val>
            <right_val>0.4173265993595123</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 36 1 -1.</_>
                <_>
                  12 0 12 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.2439709175378084e-003</threshold>
            <left_val>0.2321648001670837</left_val>
            <right_val>-0.1791536957025528</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 3 24 6 -1.</_>
                <_>
                  14 5 8 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.3561589121818543</threshold>
            <left_val>-0.0486268103122711</left_val>
            <right_val>0.9537345767021179</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 12 6 3 -1.</_>
                <_>
                  15 13 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.8440749049186707e-003</threshold>
            <left_val>-0.1028828024864197</left_val>
            <right_val>0.3671778142452240</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 1 9 17 -1.</_>
                <_>
                  14 1 3 17 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0609500296413898</threshold>
            <left_val>0.0561417415738106</left_val>
            <right_val>-0.6458569765090942</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 1 18 10 -1.</_>
                <_>
                  18 1 9 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1814922988414764</threshold>
            <left_val>0.0308063905686140</left_val>
            <right_val>-0.4604896008968353</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 18 10 -1.</_>
                <_>
                  9 1 9 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0923592597246170</threshold>
            <left_val>-0.4524821043014526</left_val>
            <right_val>0.0881522372364998</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  30 7 4 5 -1.</_>
                <_>
                  31 8 2 5 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>7.6072998344898224e-003</threshold>
            <left_val>-0.0971223264932632</left_val>
            <right_val>0.2155224978923798</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 10 1 3 -1.</_>
                <_>
                  0 11 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.6946710790507495e-004</threshold>
            <left_val>-0.4089371860027313</left_val>
            <right_val>0.0800421908497810</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  33 16 2 2 -1.</_>
                <_>
                  34 16 1 1 2.</_>
                <_>
                  33 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0301820293534547e-004</threshold>
            <left_val>-0.1153035983443260</left_val>
            <right_val>0.2795535027980804</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 16 2 2 -1.</_>
                <_>
                  1 16 1 1 2.</_>
                <_>
                  2 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.7936851256527007e-004</threshold>
            <left_val>-0.1139610037207604</left_val>
            <right_val>0.2931660115718842</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 36 3 -1.</_>
                <_>
                  12 9 12 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2467595934867859</threshold>
            <left_val>-0.0385956317186356</left_val>
            <right_val>0.8264998197555542</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 7 8 4 -1.</_>
                <_>
                  14 8 8 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.4232958033680916e-003</threshold>
            <left_val>0.3299596905708313</left_val>
            <right_val>-0.1164536997675896</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 9 5 3 -1.</_>
                <_>
                  17 10 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.2311567813158035e-003</threshold>
            <left_val>0.2714211940765381</left_val>
            <right_val>-0.1081148013472557</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 1 2 -1.</_>
                <_>
                  4 0 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.5653009759262204e-003</threshold>
            <left_val>0.0782537832856178</left_val>
            <right_val>-0.5209766030311585</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  31 0 3 2 -1.</_>
                <_>
                  31 0 3 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.0341398455202579e-003</threshold>
            <left_val>0.2948805987834930</left_val>
            <right_val>-0.0469605103135109</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 2 3 -1.</_>
                <_>
                  5 0 1 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.4283140189945698e-003</threshold>
            <left_val>-0.1379459947347641</left_val>
            <right_val>0.2432370930910111</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 13 36 5 -1.</_>
                <_>
                  0 13 18 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1903136968612671</threshold>
            <left_val>-0.0520935095846653</left_val>
            <right_val>0.6870803236961365</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 3 4 3 -1.</_>
                <_>
                  5 4 4 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>8.1368777900934219e-003</threshold>
            <left_val>-0.0533115193247795</left_val>
            <right_val>0.5827271938323975</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  28 7 6 3 -1.</_>
                <_>
                  30 9 2 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0467283688485622</threshold>
            <left_val>0.3552536070346832</left_val>
            <right_val>-0.0178062599152327</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 7 3 6 -1.</_>
                <_>
                  6 9 3 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0143171697854996</threshold>
            <left_val>-0.1262664049863815</left_val>
            <right_val>0.2696101069450378</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 5 18 10 -1.</_>
                <_>
                  23 5 9 5 2.</_>
                <_>
                  14 10 9 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0961097329854965</threshold>
            <left_val>0.3411748111248016</left_val>
            <right_val>-0.0392176099121571</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 5 18 10 -1.</_>
                <_>
                  4 5 9 5 2.</_>
                <_>
                  13 10 9 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0748788118362427</threshold>
            <left_val>-0.0648199021816254</left_val>
            <right_val>0.5671138167381287</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  32 17 3 1 -1.</_>
                <_>
                  33 17 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.1972299843328074e-005</threshold>
            <left_val>0.2874209880828857</left_val>
            <right_val>-0.1642889976501465</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 17 3 1 -1.</_>
                <_>
                  2 17 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.0099039829801768e-004</threshold>
            <left_val>0.2659021019935608</left_val>
            <right_val>-0.1299035996198654</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 26 2 -1.</_>
                <_>
                  18 0 13 1 2.</_>
                <_>
                  5 1 13 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0155834900215268</threshold>
            <left_val>0.0363226197659969</left_val>
            <right_val>-0.8874331712722778</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 3 27 9 -1.</_>
                <_>
                  9 6 9 3 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.7313341423869133e-003</threshold>
            <left_val>0.1628185957670212</left_val>
            <right_val>-0.1971620023250580</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 0 18 12 -1.</_>
                <_>
                  13 6 18 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0452514104545116</threshold>
            <left_val>-0.2031500935554504</left_val>
            <right_val>0.1573408991098404</right_val></_></_>
        <_>
          <!-- tree 44 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 17 4 1 -1.</_>
                <_>
                  1 17 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.8729529003612697e-004</threshold>
            <left_val>-0.1244959011673927</left_val>
            <right_val>0.2565822899341583</right_val></_></_>
        <_>
          <!-- tree 45 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  29 13 1 3 -1.</_>
                <_>
                  28 14 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-2.1028579212725163e-003</threshold>
            <left_val>-0.5088729262351990</left_val>
            <right_val>0.0340831801295280</right_val></_></_>
        <_>
          <!-- tree 46 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 8 6 -1.</_>
                <_>
                  0 14 8 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.9328099228441715e-003</threshold>
            <left_val>-0.3393375873565674</left_val>
            <right_val>0.0930555686354637</right_val></_></_>
        <_>
          <!-- tree 47 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  23 7 3 3 -1.</_>
                <_>
                  24 7 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.1205590348690748e-003</threshold>
            <left_val>-0.0227940604090691</left_val>
            <right_val>0.2379353046417236</right_val></_></_>
        <_>
          <!-- tree 48 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 1 12 6 -1.</_>
                <_>
                  11 3 12 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0780286788940430</threshold>
            <left_val>-0.0445036217570305</left_val>
            <right_val>0.6776394248008728</right_val></_></_>
        <_>
          <!-- tree 49 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 10 26 8 -1.</_>
                <_>
                  18 10 13 4 2.</_>
                <_>
                  5 14 13 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0424769781529903</threshold>
            <left_val>0.0925821065902710</left_val>
            <right_val>-0.3536301851272583</right_val></_></_>
        <_>
          <!-- tree 50 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 12 9 6 -1.</_>
                <_>
                  14 12 3 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0257683005183935</threshold>
            <left_val>-0.9091991186141968</left_val>
            <right_val>0.0266928393393755</right_val></_></_>
        <_>
          <!-- tree 51 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 12 12 3 -1.</_>
                <_>
                  18 13 4 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0614446699619293</threshold>
            <left_val>-0.0249543990939856</left_val>
            <right_val>0.7212049961090088</right_val></_></_>
        <_>
          <!-- tree 52 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 12 12 3 -1.</_>
                <_>
                  14 13 4 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.5776318982243538e-003</threshold>
            <left_val>0.1772899031639099</left_val>
            <right_val>-0.1972344964742661</right_val></_></_></trees>
      <stage_threshold>-1.2766569852828979</stage_threshold>
      <parent>13</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 15 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 6 27 6 -1.</_>
                <_>
                  13 8 9 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2858596146106720</threshold>
            <left_val>-0.1539604961872101</left_val>
            <right_val>0.6624677181243897</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 9 5 4 -1.</_>
                <_>
                  17 10 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.2271259054541588e-003</threshold>
            <left_val>-0.1074633970856667</left_val>
            <right_val>0.4311806857585907</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 16 2 -1.</_>
                <_>
                  0 0 8 1 2.</_>
                <_>
                  8 1 8 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.2924109362065792e-003</threshold>
            <left_val>-0.1983013004064560</left_val>
            <right_val>0.3842228949069977</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  22 0 8 8 -1.</_>
                <_>
                  26 0 4 4 2.</_>
                <_>
                  22 4 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0140045098960400</threshold>
            <left_val>-0.1924948990345001</left_val>
            <right_val>0.3442491888999939</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 32 12 -1.</_>
                <_>
                  1 0 16 6 2.</_>
                <_>
                  17 6 16 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0960232019424438</threshold>
            <left_val>0.1299059987068176</left_val>
            <right_val>-0.6065304875373840</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  28 7 6 10 -1.</_>
                <_>
                  31 7 3 5 2.</_>
                <_>
                  28 12 3 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.1803720891475677e-003</threshold>
            <left_val>-0.1904646009206772</left_val>
            <right_val>0.1891862004995346</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 7 6 10 -1.</_>
                <_>
                  2 7 3 5 2.</_>
                <_>
                  5 12 3 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.2172285765409470e-003</threshold>
            <left_val>-0.2518267929553986</left_val>
            <right_val>0.2664459049701691</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  20 10 3 3 -1.</_>
                <_>
                  20 11 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4542760327458382e-003</threshold>
            <left_val>0.2710269093513489</left_val>
            <right_val>-0.1204148977994919</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 10 3 3 -1.</_>
                <_>
                  13 11 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.0185449868440628e-003</threshold>
            <left_val>-0.1353860944509506</left_val>
            <right_val>0.4733603000640869</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 16 6 2 -1.</_>
                <_>
                  19 16 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.4214779734611511e-003</threshold>
            <left_val>-0.5049971938133240</left_val>
            <right_val>0.1042480990290642</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 11 7 3 -1.</_>
                <_>
                  13 12 7 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.5980763435363770e-003</threshold>
            <left_val>-0.1034729033708572</left_val>
            <right_val>0.5837283730506897</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  25 13 3 2 -1.</_>
                <_>
                  25 13 3 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.1849957779049873e-003</threshold>
            <left_val>0.0588967092335224</left_val>
            <right_val>-0.4623228907585144</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 10 4 4 -1.</_>
                <_>
                  13 11 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.6107750385999680e-003</threshold>
            <left_val>0.3783561885356903</left_val>
            <right_val>-0.1259022951126099</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 16 18 2 -1.</_>
                <_>
                  26 16 9 1 2.</_>
                <_>
                  17 17 9 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.8978679329156876e-003</threshold>
            <left_val>-0.1369954943656921</left_val>
            <right_val>0.2595148086547852</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 13 4 1 -1.</_>
                <_>
                  9 13 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.2606070637702942e-003</threshold>
            <left_val>0.0882339626550674</left_val>
            <right_val>-0.6390284895896912</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 1 2 1 -1.</_>
                <_>
                  34 1 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.2996238917112350e-003</threshold>
            <left_val>-0.7953972816467285</left_val>
            <right_val>0.0170935597270727</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 4 24 6 -1.</_>
                <_>
                  13 6 8 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.3542361855506897</threshold>
            <left_val>-0.0593450404703617</left_val>
            <right_val>0.8557919859886169</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  33 16 3 2 -1.</_>
                <_>
                  33 17 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0245838570408523e-004</threshold>
            <left_val>0.3147065043449402</left_val>
            <right_val>-0.1448609977960587</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 17 36 1 -1.</_>
                <_>
                  18 17 18 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0271694902330637</threshold>
            <left_val>-0.1249295026063919</left_val>
            <right_val>0.4280903935432434</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 1 2 1 -1.</_>
                <_>
                  34 1 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.4571529831737280e-003</threshold>
            <left_val>0.0397093296051025</left_val>
            <right_val>-0.7089157104492188</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 1 1 2 -1.</_>
                <_>
                  2 1 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.1742798853665590e-003</threshold>
            <left_val>0.0658724531531334</left_val>
            <right_val>-0.6949694156646729</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  22 0 8 10 -1.</_>
                <_>
                  24 2 4 10 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0252638105303049</threshold>
            <left_val>-0.1169395968317986</left_val>
            <right_val>0.1904976963996887</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 4 8 12 -1.</_>
                <_>
                  12 4 4 6 2.</_>
                <_>
                  16 10 4 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0247209891676903</threshold>
            <left_val>-0.4965795874595642</left_val>
            <right_val>0.1017538011074066</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  26 6 6 6 -1.</_>
                <_>
                  29 6 3 3 2.</_>
                <_>
                  26 9 3 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0103848800063133</threshold>
            <left_val>-0.1148673966526985</left_val>
            <right_val>0.3374153077602387</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 6 4 6 -1.</_>
                <_>
                  5 6 2 3 2.</_>
                <_>
                  7 9 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0045028328895569e-003</threshold>
            <left_val>-0.1096355020999908</left_val>
            <right_val>0.3925519883632660</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  29 5 2 4 -1.</_>
                <_>
                  29 5 1 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>7.1279620751738548e-003</threshold>
            <left_val>-0.0649081915616989</left_val>
            <right_val>0.4042040109634399</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 4 18 3 -1.</_>
                <_>
                  7 5 18 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0197004191577435</threshold>
            <left_val>-0.0793758779764175</left_val>
            <right_val>0.5308234095573425</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  29 13 2 3 -1.</_>
                <_>
                  28 14 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.2097331024706364e-003</threshold>
            <left_val>0.0407970212399960</left_val>
            <right_val>-0.6044098734855652</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 5 3 3 -1.</_>
                <_>
                  8 6 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.4459570199251175e-003</threshold>
            <left_val>-0.1038623005151749</left_val>
            <right_val>0.4093598127365112</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 16 22 2 -1.</_>
                <_>
                  18 16 11 1 2.</_>
                <_>
                  7 17 11 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.9610428288578987e-003</threshold>
            <left_val>-0.5291494727134705</left_val>
            <right_val>0.0805394500494003</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 2 1 3 -1.</_>
                <_>
                  0 3 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.7519221445545554e-004</threshold>
            <left_val>0.0638044029474258</left_val>
            <right_val>-0.5863661766052246</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 3 20 6 -1.</_>
                <_>
                  26 3 10 3 2.</_>
                <_>
                  16 6 10 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0605248510837555</threshold>
            <left_val>-0.0337128005921841</left_val>
            <right_val>0.2631115913391113</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 5 8 6 -1.</_>
                <_>
                  12 5 4 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0103538101539016</threshold>
            <left_val>-0.4792002141475678</left_val>
            <right_val>0.0800439566373825</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 8 34 8 -1.</_>
                <_>
                  18 8 17 4 2.</_>
                <_>
                  1 12 17 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0227775108069181</threshold>
            <left_val>-0.3116275072097778</left_val>
            <right_val>0.1189998015761375</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 9 8 8 -1.</_>
                <_>
                  14 9 4 4 2.</_>
                <_>
                  18 13 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0224688798189163</threshold>
            <left_val>-0.6608346104621887</left_val>
            <right_val>0.0522344894707203</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  35 0 1 3 -1.</_>
                <_>
                  35 1 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.8432162040844560e-004</threshold>
            <left_val>0.0546303391456604</left_val>
            <right_val>-0.4639565944671631</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 8 3 5 -1.</_>
                <_>
                  16 8 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.6177870351821184e-003</threshold>
            <left_val>0.6744704246520996</left_val>
            <right_val>-0.0587895289063454</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 0 10 1 -1.</_>
                <_>
                  19 0 5 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0300888605415821</threshold>
            <left_val>0.0331335216760635</left_val>
            <right_val>-0.4646137058734894</right_val></_></_></trees>
      <stage_threshold>-1.4061349630355835</stage_threshold>
      <parent>14</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 16 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 3 9 6 -1.</_>
                <_>
                  7 5 9 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0726009905338287</threshold>
            <left_val>0.6390709280967712</left_val>
            <right_val>-0.1512455046176910</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 6 24 6 -1.</_>
                <_>
                  14 8 8 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.3471255898475647</threshold>
            <left_val>-0.0790246576070786</left_val>
            <right_val>0.7955042123794556</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 8 27 6 -1.</_>
                <_>
                  13 10 9 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.3429723083972931</threshold>
            <left_val>-0.1230095997452736</left_val>
            <right_val>0.6572809815406799</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 4 27 6 -1.</_>
                <_>
                  14 6 9 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.3561694025993347</threshold>
            <left_val>-0.0537334382534027</left_val>
            <right_val>0.8285108208656311</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 6 5 6 -1.</_>
                <_>
                  5 8 5 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.0840700753033161e-003</threshold>
            <left_val>-0.1284721046686173</left_val>
            <right_val>0.3382267951965332</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  35 0 1 2 -1.</_>
                <_>
                  35 1 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6281309945043176e-004</threshold>
            <left_val>0.3035660982131958</left_val>
            <right_val>-0.2518202960491180</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 3 10 3 -1.</_>
                <_>
                  3 4 10 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0112819001078606</threshold>
            <left_val>-0.0839143469929695</left_val>
            <right_val>0.4347592890262604</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  29 5 2 4 -1.</_>
                <_>
                  29 5 1 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>7.4357059784233570e-003</threshold>
            <left_val>-0.0670880377292633</left_val>
            <right_val>0.3722797930240631</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 0 28 16 -1.</_>
                <_>
                  3 0 14 8 2.</_>
                <_>
                  17 8 14 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0905762165784836</threshold>
            <left_val>-0.5831961035728455</left_val>
            <right_val>0.0801467597484589</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  31 0 4 2 -1.</_>
                <_>
                  31 0 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>8.8247694075107574e-003</threshold>
            <left_val>0.1290193051099777</left_val>
            <right_val>-0.4760313034057617</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 9 3 9 -1.</_>
                <_>
                  4 12 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6147770695388317e-003</threshold>
            <left_val>-0.4000220894813538</left_val>
            <right_val>0.1124631017446518</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  32 16 4 2 -1.</_>
                <_>
                  32 17 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.5541300419718027e-004</threshold>
            <left_val>0.3238615989685059</left_val>
            <right_val>-0.2333187013864517</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 0 1 10 -1.</_>
                <_>
                  17 0 1 5 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0265476293861866</threshold>
            <left_val>0.0723338723182678</left_val>
            <right_val>-0.5837839841842651</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 4 14 8 -1.</_>
                <_>
                  17 4 7 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0513831414282322</threshold>
            <left_val>-0.2244618982076645</left_val>
            <right_val>0.0409497395157814</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 11 4 -1.</_>
                <_>
                  6 2 11 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.3701129723340273e-003</threshold>
            <left_val>-0.1671708971261978</left_val>
            <right_val>0.2552697062492371</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  35 0 1 2 -1.</_>
                <_>
                  35 1 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2581920493394136e-003</threshold>
            <left_val>-0.9207922816276550</left_val>
            <right_val>3.4371060319244862e-003</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 1 2 -1.</_>
                <_>
                  0 1 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3282749569043517e-004</threshold>
            <left_val>0.1857322007417679</left_val>
            <right_val>-0.2249896973371506</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  33 0 2 1 -1.</_>
                <_>
                  33 0 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-2.8032590635120869e-003</threshold>
            <left_val>-0.8589754104614258</left_val>
            <right_val>0.0463845208287239</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 0 1 2 -1.</_>
                <_>
                  3 0 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.3141379458829761e-003</threshold>
            <left_val>0.0796270668506622</left_val>
            <right_val>-0.4610596895217896</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 17 36 1 -1.</_>
                <_>
                  9 17 18 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0638845413923264</threshold>
            <left_val>-0.0534401498734951</left_val>
            <right_val>0.8104500174522400</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 13 3 1 -1.</_>
                <_>
                  8 14 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.9811019301414490e-003</threshold>
            <left_val>-0.6382514834403992</left_val>
            <right_val>0.0766435563564301</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 4 14 8 -1.</_>
                <_>
                  17 4 7 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0133598595857620</threshold>
            <left_val>-0.0950375497341156</left_val>
            <right_val>0.0625333487987518</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 4 2 -1.</_>
                <_>
                  0 17 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0935300088021904e-004</threshold>
            <left_val>0.1747954040765762</left_val>
            <right_val>-0.2287603020668030</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 12 10 3 -1.</_>
                <_>
                  13 13 10 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0119106303900480</threshold>
            <left_val>-0.0770419836044312</left_val>
            <right_val>0.5045837759971619</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 36 6 -1.</_>
                <_>
                  18 12 18 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2395170032978058</threshold>
            <left_val>-0.0651228874921799</left_val>
            <right_val>0.5042074918746948</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 3 27 6 -1.</_>
                <_>
                  14 5 9 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.3983140885829926</threshold>
            <left_val>-0.0299998205155134</left_val>
            <right_val>0.7968547940254211</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 5 5 3 -1.</_>
                <_>
                  8 6 5 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>6.1875800602138042e-003</threshold>
            <left_val>-0.0853391736745834</left_val>
            <right_val>0.3945176899433136</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 7 12 4 -1.</_>
                <_>
                  15 7 6 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.4047123566269875e-003</threshold>
            <left_val>-0.4344133138656616</left_val>
            <right_val>0.0826191008090973</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 5 8 4 -1.</_>
                <_>
                  15 5 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0117366304621100</threshold>
            <left_val>0.0694831609725952</left_val>
            <right_val>-0.4870649874210358</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 14 6 4 -1.</_>
                <_>
                  16 14 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0151767702773213</threshold>
            <left_val>-0.5854120850563049</left_val>
            <right_val>0.0328795611858368</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 10 5 3 -1.</_>
                <_>
                  14 11 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.0744259711354971e-003</threshold>
            <left_val>-0.1314608007669449</left_val>
            <right_val>0.2546674013137817</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  25 3 6 4 -1.</_>
                <_>
                  25 4 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.9391339048743248e-003</threshold>
            <left_val>-0.1086023002862930</left_val>
            <right_val>0.2783496081829071</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 6 6 8 -1.</_>
                <_>
                  3 8 6 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.1510310471057892e-003</threshold>
            <left_val>-0.1575057953596115</left_val>
            <right_val>0.2087786048650742</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  27 4 5 6 -1.</_>
                <_>
                  27 6 5 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.3775361739099026e-003</threshold>
            <left_val>-0.1320703029632568</left_val>
            <right_val>0.3767293989658356</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 1 6 9 -1.</_>
                <_>
                  4 4 6 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0221741795539856</threshold>
            <left_val>-0.0901802927255630</left_val>
            <right_val>0.4157527089118958</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  21 9 2 4 -1.</_>
                <_>
                  21 10 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.9948610570281744e-003</threshold>
            <left_val>0.2560858130455017</left_val>
            <right_val>-0.0990849286317825</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 10 34 4 -1.</_>
                <_>
                  1 10 17 2 2.</_>
                <_>
                  18 12 17 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0315575599670410</threshold>
            <left_val>0.0741889998316765</left_val>
            <right_val>-0.5494022965431213</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 15 2 3 -1.</_>
                <_>
                  34 16 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.3111158447572961e-005</threshold>
            <left_val>0.3032462894916534</left_val>
            <right_val>-0.1778181046247482</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 0 2 2 -1.</_>
                <_>
                  3 0 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-3.2675920519977808e-003</threshold>
            <left_val>-0.6721243262290955</left_val>
            <right_val>0.0591883286833763</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  33 0 1 2 -1.</_>
                <_>
                  33 0 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.2293380829505622e-004</threshold>
            <left_val>-0.1103409975767136</left_val>
            <right_val>0.1257317960262299</right_val></_></_></trees>
      <stage_threshold>-1.3384460210800171</stage_threshold>
      <parent>15</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 17 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 0 10 8 -1.</_>
                <_>
                  6 2 10 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0425620190799236</threshold>
            <left_val>0.3334665894508362</left_val>
            <right_val>-0.2986198067665100</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 6 30 6 -1.</_>
                <_>
                  13 8 10 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.4182719886302948</threshold>
            <left_val>-0.0951386988162994</left_val>
            <right_val>0.7570992112159729</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 7 10 4 -1.</_>
                <_>
                  13 8 10 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0202563796192408</threshold>
            <left_val>0.4778389036655426</left_val>
            <right_val>-0.1459210067987442</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 5 6 12 -1.</_>
                <_>
                  19 5 3 6 2.</_>
                <_>
                  16 11 3 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0189483091235161</threshold>
            <left_val>-0.3872750103473663</left_val>
            <right_val>0.0524798892438412</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 1 4 6 -1.</_>
                <_>
                  8 3 4 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0405505895614624</threshold>
            <left_val>0.5464624762535095</left_val>
            <right_val>-0.0813998579978943</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 7 33 6 -1.</_>
                <_>
                  13 9 11 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.5187274813652039</threshold>
            <left_val>-0.0279305391013622</left_val>
            <right_val>0.8458098173141480</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 6 30 3 -1.</_>
                <_>
                  13 7 10 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2071361988782883</threshold>
            <left_val>-0.0588508695363998</left_val>
            <right_val>0.7960156202316284</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 11 6 3 -1.</_>
                <_>
                  15 12 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.1972572952508926e-003</threshold>
            <left_val>-0.0999663695693016</left_val>
            <right_val>0.4983156025409699</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 5 6 12 -1.</_>
                <_>
                  14 5 3 6 2.</_>
                <_>
                  17 11 3 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0174453891813755</threshold>
            <left_val>0.0680409595370293</left_val>
            <right_val>-0.5669981837272644</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 12 26 6 -1.</_>
                <_>
                  18 12 13 3 2.</_>
                <_>
                  5 15 13 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0563102811574936</threshold>
            <left_val>-0.6862804293632507</left_val>
            <right_val>0.0742225572466850</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 12 27 3 -1.</_>
                <_>
                  13 13 9 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1809556037187576</threshold>
            <left_val>-0.0528081282973289</left_val>
            <right_val>0.8448318243026733</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 11 4 3 -1.</_>
                <_>
                  16 12 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.3450690787285566e-003</threshold>
            <left_val>0.2839694023132324</left_val>
            <right_val>-0.1112336963415146</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 12 4 2 -1.</_>
                <_>
                  6 13 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.8937770295888186e-003</threshold>
            <left_val>0.0654993131756783</left_val>
            <right_val>-0.5792096257209778</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 17 2 1 -1.</_>
                <_>
                  34 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.9383721741614863e-005</threshold>
            <left_val>-0.3093047142028809</left_val>
            <right_val>0.4223710894584656</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 1 12 -1.</_>
                <_>
                  16 0 1 6 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0338991582393646</threshold>
            <left_val>0.0307075399905443</left_val>
            <right_val>-0.7229980826377869</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 17 34 1 -1.</_>
                <_>
                  2 17 17 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0336443893611431</threshold>
            <left_val>0.4266444146633148</left_val>
            <right_val>-0.0720057785511017</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 3 18 4 -1.</_>
                <_>
                  5 4 18 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0388077609241009</threshold>
            <left_val>-0.0417135208845139</left_val>
            <right_val>0.6599556803703308</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 17 2 1 -1.</_>
                <_>
                  34 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.9149548683781177e-005</threshold>
            <left_val>0.4933550059795380</left_val>
            <right_val>-0.2426010966300964</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 2 2 -1.</_>
                <_>
                  0 1 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.7580570895224810e-004</threshold>
            <left_val>0.1791010946035385</left_val>
            <right_val>-0.2192519009113312</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 5 16 3 -1.</_>
                <_>
                  15 6 16 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0126366596668959</threshold>
            <left_val>-0.0712336227297783</left_val>
            <right_val>0.2534261941909790</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 9 3 3 -1.</_>
                <_>
                  13 10 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.3681739587336779e-003</threshold>
            <left_val>0.3310086131095886</left_val>
            <right_val>-0.1020777970552445</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  20 4 8 14 -1.</_>
                <_>
                  22 4 4 14 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0411845296621323</threshold>
            <left_val>-0.4787198901176453</left_val>
            <right_val>0.0274448096752167</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 5 20 6 -1.</_>
                <_>
                  12 5 10 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0172852799296379</threshold>
            <left_val>-0.2373382002115250</left_val>
            <right_val>0.1541430056095123</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  26 3 6 6 -1.</_>
                <_>
                  28 5 2 6 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0583733208477497</threshold>
            <left_val>0.3635525107383728</left_val>
            <right_val>-0.0629119277000427</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 3 6 6 -1.</_>
                <_>
                  8 5 6 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0252293199300766</threshold>
            <left_val>-0.0943458229303360</left_val>
            <right_val>0.4322442114353180</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 0 2 3 -1.</_>
                <_>
                  34 0 1 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.7925519756972790e-003</threshold>
            <left_val>0.0486642718315125</left_val>
            <right_val>-0.4704689085483551</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 2 2 -1.</_>
                <_>
                  0 17 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3549529830925167e-004</threshold>
            <left_val>0.1936188042163849</left_val>
            <right_val>-0.1933847069740295</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  30 6 4 8 -1.</_>
                <_>
                  31 7 2 8 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0179694108664989</threshold>
            <left_val>0.2900086045265198</left_val>
            <right_val>-0.0545452795922756</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 6 7 4 -1.</_>
                <_>
                  5 7 7 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0111410403624177</threshold>
            <left_val>-0.1080225035548210</left_val>
            <right_val>0.3332796096801758</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  20 4 8 14 -1.</_>
                <_>
                  22 4 4 14 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0397595092654228</threshold>
            <left_val>0.0192408692091703</left_val>
            <right_val>-0.4889996051788330</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 4 8 14 -1.</_>
                <_>
                  10 4 4 14 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0226527098566294</threshold>
            <left_val>-0.5036928057670593</left_val>
            <right_val>0.0807737335562706</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 17 6 1 -1.</_>
                <_>
                  19 17 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0915650054812431e-003</threshold>
            <left_val>0.0655540525913239</left_val>
            <right_val>-0.2444387972354889</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 20 6 -1.</_>
                <_>
                  10 0 10 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0687547475099564</threshold>
            <left_val>0.0891968086361885</left_val>
            <right_val>-0.3565390110015869</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 0 22 18 -1.</_>
                <_>
                  8 0 11 18 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.3307105898857117</threshold>
            <left_val>0.4649569988250732</left_val>
            <right_val>-0.0581836998462677</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 2 8 12 -1.</_>
                <_>
                  13 2 4 6 2.</_>
                <_>
                  17 8 4 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0193072296679020</threshold>
            <left_val>-0.4415718019008637</left_val>
            <right_val>0.0830501168966293</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 10 14 8 -1.</_>
                <_>
                  18 10 7 4 2.</_>
                <_>
                  11 14 7 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0348087586462498</threshold>
            <left_val>0.0534805804491043</left_val>
            <right_val>-0.5037739872932434</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 16 2 2 -1.</_>
                <_>
                  1 16 1 1 2.</_>
                <_>
                  2 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.8908151327632368e-004</threshold>
            <left_val>0.3427126109600067</left_val>
            <right_val>-0.0899231806397438</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 0 2 1 -1.</_>
                <_>
                  34 0 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-2.1421869751065969e-003</threshold>
            <left_val>-0.6064280271530151</left_val>
            <right_val>0.0555892400443554</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 3 24 4 -1.</_>
                <_>
                  12 3 12 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1101581007242203</threshold>
            <left_val>-0.0547747202217579</left_val>
            <right_val>0.6878091096878052</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 1 2 3 -1.</_>
                <_>
                  19 2 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.0875208904035389e-004</threshold>
            <left_val>-0.0558342188596725</left_val>
            <right_val>0.0931682363152504</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 1 2 -1.</_>
                <_>
                  2 0 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.1960400044918060e-003</threshold>
            <left_val>0.0539557486772537</left_val>
            <right_val>-0.6050305962562561</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 3 6 8 -1.</_>
                <_>
                  18 3 3 4 2.</_>
                <_>
                  15 7 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0126062501221895</threshold>
            <left_val>-0.4686402976512909</left_val>
            <right_val>0.0599438697099686</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 5 4 2 -1.</_>
                <_>
                  14 6 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.7497899718582630e-003</threshold>
            <left_val>0.2894253134727478</left_val>
            <right_val>-0.1129785031080246</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 7 30 9 -1.</_>
                <_>
                  13 10 10 3 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.6096264123916626</threshold>
            <left_val>-0.0478859916329384</left_val>
            <right_val>0.5946549177169800</right_val></_></_>
        <_>
          <!-- tree 44 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 8 12 9 -1.</_>
                <_>
                  12 8 6 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0450232513248920</threshold>
            <left_val>0.0638310685753822</left_val>
            <right_val>-0.5295680165290833</right_val></_></_></trees>
      <stage_threshold>-1.2722699642181396</stage_threshold>
      <parent>16</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 18 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 8 16 5 -1.</_>
                <_>
                  14 8 8 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0159072801470757</threshold>
            <left_val>-0.3819232881069183</left_val>
            <right_val>0.2941176891326904</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  30 1 4 10 -1.</_>
                <_>
                  31 2 2 10 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0304830092936754</threshold>
            <left_val>0.6401454806327820</left_val>
            <right_val>-0.1133823990821838</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 0 10 8 -1.</_>
                <_>
                  11 2 10 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0258412398397923</threshold>
            <left_val>-0.1765469014644623</left_val>
            <right_val>0.2556340098381043</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  32 2 2 14 -1.</_>
                <_>
                  32 2 1 14 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0121606197208166</threshold>
            <left_val>-0.0494619905948639</left_val>
            <right_val>0.3473398983478546</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 2 14 2 -1.</_>
                <_>
                  4 2 14 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0159101597964764</threshold>
            <left_val>0.4796676933765411</left_val>
            <right_val>-0.1300950944423676</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  30 14 6 4 -1.</_>
                <_>
                  30 14 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.5282061435282230e-004</threshold>
            <left_val>-0.3418492972850800</left_val>
            <right_val>0.2309112995862961</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 13 1 4 -1.</_>
                <_>
                  11 15 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.7633582511916757e-004</threshold>
            <left_val>-0.1543250977993012</left_val>
            <right_val>0.2668730020523071</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 0 14 18 -1.</_>
                <_>
                  18 0 7 9 2.</_>
                <_>
                  11 9 7 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0599361397325993</threshold>
            <left_val>-0.4880258142948151</left_val>
            <right_val>0.0933274477720261</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 20 9 -1.</_>
                <_>
                  10 1 10 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1134240999817848</threshold>
            <left_val>-0.6577144265174866</left_val>
            <right_val>0.0591668188571930</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  21 3 8 3 -1.</_>
                <_>
                  23 3 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.3361280113458633e-003</threshold>
            <left_val>-0.1593652069568634</left_val>
            <right_val>0.0502370409667492</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 9 2 4 -1.</_>
                <_>
                  13 10 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.8627740209922194e-003</threshold>
            <left_val>0.3073025941848755</left_val>
            <right_val>-0.1254066973924637</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 9 11 2 -1.</_>
                <_>
                  14 10 11 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0126530099660158</threshold>
            <left_val>-0.1004493013024330</left_val>
            <right_val>0.3749617934226990</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 2 36 9 -1.</_>
                <_>
                  12 5 12 3 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.6911857724189758</threshold>
            <left_val>-0.0471464097499847</left_val>
            <right_val>0.8321244120597839</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 12 2 6 -1.</_>
                <_>
                  34 15 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6093868655152619e-004</threshold>
            <left_val>0.3198773860931397</left_val>
            <right_val>-0.2718330919742584</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 4 14 6 -1.</_>
                <_>
                  11 6 14 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0763450562953949</threshold>
            <left_val>0.4309130012989044</left_val>
            <right_val>-0.0908882692456245</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  31 0 4 1 -1.</_>
                <_>
                  31 0 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.8098300099372864e-003</threshold>
            <left_val>0.0587311200797558</left_val>
            <right_val>-0.6199675202369690</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 4 1 -1.</_>
                <_>
                  3 0 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3322039740160108e-004</threshold>
            <left_val>0.2000005990266800</left_val>
            <right_val>-0.2012010961771011</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 14 6 4 -1.</_>
                <_>
                  21 14 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0137176299467683</threshold>
            <left_val>-0.7309545278549194</left_val>
            <right_val>0.0271785296499729</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 14 6 4 -1.</_>
                <_>
                  13 14 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.2303808517754078e-003</threshold>
            <left_val>-0.5478098988533020</left_val>
            <right_val>0.0687499493360519</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 14 36 1 -1.</_>
                <_>
                  9 14 18 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0499227195978165</threshold>
            <left_val>-0.0473043099045753</left_val>
            <right_val>0.8242310285568237</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 2 2 -1.</_>
                <_>
                  5 0 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.9126719562336802e-003</threshold>
            <left_val>-0.5394017100334168</left_val>
            <right_val>0.0774475932121277</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  26 3 5 3 -1.</_>
                <_>
                  26 4 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1384560493752360e-003</threshold>
            <left_val>-0.0965376868844032</left_val>
            <right_val>0.1548569053411484</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 8 1 3 -1.</_>
                <_>
                  15 9 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-2.4732090532779694e-003</threshold>
            <left_val>0.3559078872203827</left_val>
            <right_val>-0.0931698307394981</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  21 11 2 3 -1.</_>
                <_>
                  21 12 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.1464257780462503e-004</threshold>
            <left_val>0.1452019065618515</left_val>
            <right_val>-0.0741942077875137</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 5 6 4 -1.</_>
                <_>
                  8 6 6 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0204371493309736</threshold>
            <left_val>0.4416376948356628</left_val>
            <right_val>-0.0809424370527267</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  31 0 2 2 -1.</_>
                <_>
                  31 0 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.0483791381120682e-003</threshold>
            <left_val>-0.5999277830123901</left_val>
            <right_val>0.0330253802239895</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 4 3 9 -1.</_>
                <_>
                  6 7 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0111480504274368</threshold>
            <left_val>-0.1135832965373993</left_val>
            <right_val>0.3264499902725220</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 0 11 2 -1.</_>
                <_>
                  19 0 11 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>9.8842009902000427e-003</threshold>
            <left_val>0.0554044805467129</left_val>
            <right_val>-0.3273097872734070</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 2 2 -1.</_>
                <_>
                  5 0 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.1296359375119209e-003</threshold>
            <left_val>0.0774086564779282</left_val>
            <right_val>-0.4595307111740112</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  22 0 14 4 -1.</_>
                <_>
                  29 0 7 2 2.</_>
                <_>
                  22 2 7 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.9721839819103479e-003</threshold>
            <left_val>-0.1291726976633072</left_val>
            <right_val>0.1552311033010483</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 1 4 13 -1.</_>
                <_>
                  15 1 2 13 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0205544792115688</threshold>
            <left_val>0.0876004695892334</left_val>
            <right_val>-0.4577418863773346</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  21 3 8 4 -1.</_>
                <_>
                  23 3 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0230272803455591</threshold>
            <left_val>0.3548808991909027</left_val>
            <right_val>-0.0205669198185205</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 3 8 4 -1.</_>
                <_>
                  9 3 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.3903772756457329e-003</threshold>
            <left_val>-0.4324072897434235</left_val>
            <right_val>0.0920679792761803</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  32 14 2 2 -1.</_>
                <_>
                  33 14 1 1 2.</_>
                <_>
                  32 15 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.1431539896875620e-003</threshold>
            <left_val>0.3959133923053742</left_val>
            <right_val>-0.0231928899884224</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 14 2 2 -1.</_>
                <_>
                  2 14 1 1 2.</_>
                <_>
                  3 15 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.9133709399029613e-004</threshold>
            <left_val>0.4274964034557343</left_val>
            <right_val>-0.0855242162942886</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  35 5 1 12 -1.</_>
                <_>
                  35 9 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.1292928401380777e-004</threshold>
            <left_val>-0.1619673967361450</left_val>
            <right_val>0.1961497068405151</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 7 1 9 -1.</_>
                <_>
                  0 10 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.8478871360421181e-003</threshold>
            <left_val>-0.5911636948585510</left_val>
            <right_val>0.0624482408165932</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 2 15 6 -1.</_>
                <_>
                  12 4 15 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0941330492496490</threshold>
            <left_val>0.4770160913467407</left_val>
            <right_val>-0.0567101612687111</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 17 2 1 -1.</_>
                <_>
                  1 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0079269850393757e-004</threshold>
            <left_val>-0.1625709980726242</left_val>
            <right_val>0.2140229046344757</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 17 2 1 -1.</_>
                <_>
                  34 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.2930231100181118e-005</threshold>
            <left_val>-0.1859605014324188</left_val>
            <right_val>0.1964769065380096</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 17 2 1 -1.</_>
                <_>
                  1 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.1743210052372888e-004</threshold>
            <left_val>0.3182134926319122</left_val>
            <right_val>-0.1328738033771515</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 0 16 10 -1.</_>
                <_>
                  15 0 8 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1275181025266647</threshold>
            <left_val>0.0301400795578957</left_val>
            <right_val>-0.7411035895347595</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 10 24 8 -1.</_>
                <_>
                  5 10 12 4 2.</_>
                <_>
                  17 14 12 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0803262963891029</threshold>
            <left_val>0.0415550395846367</left_val>
            <right_val>-0.8263683915138245</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  27 4 3 3 -1.</_>
                <_>
                  27 5 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.6904190415516496e-003</threshold>
            <left_val>-0.1029061973094940</left_val>
            <right_val>0.2972418069839478</right_val></_></_></trees>
      <stage_threshold>-1.3022350072860718</stage_threshold>
      <parent>17</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 19 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 6 14 12 -1.</_>
                <_>
                  6 6 7 6 2.</_>
                <_>
                  13 12 7 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0461227893829346</threshold>
            <left_val>0.4425258934497833</left_val>
            <right_val>-0.2991319894790649</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 5 24 6 -1.</_>
                <_>
                  14 7 8 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.3672331869602203</threshold>
            <left_val>-0.0630117505788803</left_val>
            <right_val>0.7712538242340088</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 6 3 4 -1.</_>
                <_>
                  12 7 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0962929595261812e-003</threshold>
            <left_val>0.3514241874217987</left_val>
            <right_val>-0.1730643957853317</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  30 7 6 10 -1.</_>
                <_>
                  33 7 3 5 2.</_>
                <_>
                  30 12 3 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.2647131532430649e-003</threshold>
            <left_val>-0.1607280969619751</left_val>
            <right_val>0.1853290945291519</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 12 6 6 -1.</_>
                <_>
                  3 12 3 3 2.</_>
                <_>
                  6 15 3 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.1748649198561907e-003</threshold>
            <left_val>-0.1968899965286255</left_val>
            <right_val>0.2409728020429611</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  20 0 13 2 -1.</_>
                <_>
                  20 0 13 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>8.0439839512109756e-003</threshold>
            <left_val>0.0898629724979401</left_val>
            <right_val>-0.3655225932598114</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 10 24 6 -1.</_>
                <_>
                  14 12 8 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.3275249004364014</threshold>
            <left_val>-0.0568796806037426</left_val>
            <right_val>0.7749336957931519</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 4 8 8 -1.</_>
                <_>
                  19 4 4 4 2.</_>
                <_>
                  15 8 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0190744306892157</threshold>
            <left_val>-0.2895380854606628</left_val>
            <right_val>0.0622916705906391</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 4 8 8 -1.</_>
                <_>
                  13 4 4 4 2.</_>
                <_>
                  17 8 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0205017495900393</threshold>
            <left_val>-0.6262530088424683</left_val>
            <right_val>0.0682769715785980</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 16 2 2 -1.</_>
                <_>
                  34 16 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.3187010053079575e-005</threshold>
            <left_val>-0.2514955997467041</left_val>
            <right_val>0.2613196074962616</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 6 3 3 -1.</_>
                <_>
                  12 7 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.3275580499321222e-003</threshold>
            <left_val>-0.1199077963829041</left_val>
            <right_val>0.3651930093765259</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  21 7 4 4 -1.</_>
                <_>
                  21 8 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.8408430777490139e-003</threshold>
            <left_val>-0.0827485173940659</left_val>
            <right_val>0.2365082055330277</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 8 30 4 -1.</_>
                <_>
                  2 8 15 2 2.</_>
                <_>
                  17 10 15 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0464623309671879</threshold>
            <left_val>-0.6928564906120300</left_val>
            <right_val>0.0781976729631424</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  27 4 3 4 -1.</_>
                <_>
                  27 5 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.7785700988024473e-003</threshold>
            <left_val>0.3437257111072540</left_val>
            <right_val>-0.1027545034885407</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 4 3 4 -1.</_>
                <_>
                  5 5 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.6655459767207503e-003</threshold>
            <left_val>-0.1160527989268303</left_val>
            <right_val>0.3716202974319458</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  34 16 2 2 -1.</_>
                <_>
                  34 16 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.7107670727418736e-005</threshold>
            <left_val>0.4589366912841797</left_val>
            <right_val>-0.2123643010854721</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 34 2 -1.</_>
                <_>
                  0 16 17 1 2.</_>
                <_>
                  17 17 17 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.0066380798816681e-003</threshold>
            <left_val>-0.5953341126441956</left_val>
            <right_val>0.0808764025568962</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 5 15 12 -1.</_>
                <_>
                  12 9 15 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1378971040248871</threshold>
            <left_val>0.3957067131996155</left_val>
            <right_val>-0.0898853763937950</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 36 6 -1.</_>
                <_>
                  12 10 12 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.5759987235069275</threshold>
            <left_val>-0.0538108199834824</left_val>
            <right_val>0.8170394897460938</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  25 4 6 2 -1.</_>
                <_>
                  25 5 6 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.3918158840388060e-003</threshold>
            <left_val>0.1393374055624008</left_val>
            <right_val>-0.0421559289097786</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 17 2 1 -1.</_>
                <_>
                  1 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.4896071408875287e-004</threshold>
            <left_val>-0.1485866010189056</left_val>
            <right_val>0.2626332938671112</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 9 9 -1.</_>
                <_>
                  19 0 3 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0330624915659428</threshold>
            <left_val>0.0306599102914333</left_val>
            <right_val>-0.3231860101222992</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 0 9 9 -1.</_>
                <_>
                  14 0 3 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0443218797445297</threshold>
            <left_val>0.0478538200259209</left_val>
            <right_val>-0.7813590168952942</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  20 5 16 5 -1.</_>
                <_>
                  24 5 8 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0187181904911995</threshold>
            <left_val>0.1201262027025223</left_val>
            <right_val>-0.1121146976947784</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 3 16 9 -1.</_>
                <_>
                  4 3 8 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0923093706369400</threshold>
            <left_val>0.0424630790948868</left_val>
            <right_val>-0.8009700179100037</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 6 26 12 -1.</_>
                <_>
                  20 6 13 6 2.</_>
                <_>
                  7 12 13 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0906654372811317</threshold>
            <left_val>-0.0223045293241739</left_val>
            <right_val>0.1284797936677933</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 6 24 12 -1.</_>
                <_>
                  5 6 12 6 2.</_>
                <_>
                  17 12 12 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0582949295639992</threshold>
            <left_val>-0.3936854004859924</left_val>
            <right_val>0.0954821407794952</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 4 3 12 -1.</_>
                <_>
                  18 4 1 12 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.6649780124425888e-003</threshold>
            <left_val>-0.0656419470906258</left_val>
            <right_val>0.3640717864036560</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 11 6 1 -1.</_>
                <_>
                  3 13 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>5.2480432204902172e-003</threshold>
            <left_val>0.0687657818198204</left_val>
            <right_val>-0.5050830245018005</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  21 12 14 2 -1.</_>
                <_>
                  28 12 7 1 2.</_>
                <_>
                  21 13 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5315659586340189e-003</threshold>
            <left_val>-0.0933471694588661</left_val>
            <right_val>0.1649612933397293</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 13 2 3 -1.</_>
                <_>
                  2 13 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.4391160695813596e-004</threshold>
            <left_val>-0.1888543963432312</left_val>
            <right_val>0.1695670038461685</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  26 8 3 2 -1.</_>
                <_>
                  27 9 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.3037211075425148e-003</threshold>
            <left_val>0.3826352953910828</left_val>
            <right_val>-0.0590420998632908</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 8 2 3 -1.</_>
                <_>
                  9 9 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.2754059173166752e-003</threshold>
            <left_val>-0.1224882006645203</left_val>
            <right_val>0.2828365862369537</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 0 18 18 -1.</_>
                <_>
                  12 0 9 18 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.2769486904144287</threshold>
            <left_val>0.4851497113704681</left_val>
            <right_val>-0.0404825396835804</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 9 3 3 -1.</_>
                <_>
                  7 10 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>5.8051547966897488e-003</threshold>
            <left_val>-0.0835584178566933</left_val>
            <right_val>0.4215149879455566</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  28 5 5 6 -1.</_>
                <_>
                  28 7 5 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.4654529988765717e-003</threshold>
            <left_val>-0.1281685978174210</left_val>
            <right_val>0.2077662944793701</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 1 9 8 -1.</_>
                <_>
                  9 1 9 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>7.8863510861992836e-003</threshold>
            <left_val>-0.1719754040241242</left_val>
            <right_val>0.2079081982374191</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 36 2 -1.</_>
                <_>
                  18 0 18 1 2.</_>
                <_>
                  0 1 18 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0118171302601695</threshold>
            <left_val>-0.5788066983222961</left_val>
            <right_val>0.0589591413736343</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 26 6 -1.</_>
                <_>
                  5 0 13 3 2.</_>
                <_>
                  18 3 13 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0641399174928665</threshold>
            <left_val>-0.6368926167488098</left_val>
            <right_val>0.0417975001037121</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  28 3 3 3 -1.</_>
                <_>
                  28 4 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2179970508441329e-003</threshold>
            <left_val>0.2356870025396347</left_val>
            <right_val>-0.0805152580142021</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 3 5 3 -1.</_>
                <_>
                  5 4 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.8652620967477560e-003</threshold>
            <left_val>-0.0931371971964836</left_val>
            <right_val>0.3902595043182373</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 12 8 2 -1.</_>
                <_>
                  16 12 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.7746102102100849e-003</threshold>
            <left_val>-0.5753986835479736</left_val>
            <right_val>0.0596776902675629</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 0 9 14 -1.</_>
                <_>
                  16 0 3 14 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0653770864009857</threshold>
            <left_val>0.0341660715639591</left_val>
            <right_val>-0.7425342202186585</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  23 0 10 1 -1.</_>
                <_>
                  23 0 5 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0162657108157873</threshold>
            <left_val>0.0536542609333992</left_val>
            <right_val>-0.2365860939025879</right_val></_></_>
        <_>
          <!-- tree 44 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 14 2 2 -1.</_>
                <_>
                  8 14 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.2717609535902739e-003</threshold>
            <left_val>0.0533591099083424</left_val>
            <right_val>-0.5494074225425720</right_val></_></_>
        <_>
          <!-- tree 45 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 36 3 -1.</_>
                <_>
                  12 13 12 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2262602001428604</threshold>
            <left_val>-0.0420460589230061</left_val>
            <right_val>0.7791252136230469</right_val></_></_>
        <_>
          <!-- tree 46 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 13 34 4 -1.</_>
                <_>
                  0 13 17 2 2.</_>
                <_>
                  17 15 17 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0293774604797363</threshold>
            <left_val>-0.5947058796882629</left_val>
            <right_val>0.0548178702592850</right_val></_></_></trees>
      <stage_threshold>-1.1933319568634033</stage_threshold>
      <parent>18</parent>
      <next>-1</next></_></stages></SmileDetector>
</opencv_storage>
