var searchData=
[
  ['c_20and_20glfw_20binaries_0',['With Visual C++ and GLFW binaries',['../build_guide.html#build_link_win32',1,'']]],
  ['callback_1',['Setting an error callback',['../quick_guide.html#quick_capture_error',1,'']]],
  ['capture_20of_20system_20wide_20hotkeys_2',['Capture of system-wide hotkeys',['../moving_guide.html#moving_syskeys',1,'']]],
  ['captured_20cursor_20mode_3',['Captured cursor mode',['../news.html#captured_cursor_mode',1,'']]],
  ['caveats_4',['Caveats',['../news.html#caveats',1,'']]],
  ['change_20tables_5',['Name change tables',['../moving_guide.html#moving_tables',1,'']]],
  ['changed_6',['Version string format has been changed',['../news.html#version_string_caveat',1,'']]],
  ['changed_20and_20removed_20features_7',['Changed and removed features',['../moving_guide.html#moving_removed',1,'']]],
  ['changes_8',['changes',['../moving_guide.html#moving_cursorpos',1,'Cursor position changes'],['../input_guide.html#joystick_event',1,'Joystick configuration changes'],['../moving_guide.html#moving_joystick',1,'Joystick function changes'],['../monitor_guide.html#monitor_event',1,'Monitor configuration changes'],['../moving_guide.html#moving_window_close',1,'Window closing changes']]],
  ['channel_20on_20older_20systems_9',['Wayland framebuffer may lack alpha channel on older systems',['../news.html#wayland_alpha_caveat',1,'']]],
  ['character_20actions_10',['Removal of character actions',['../moving_guide.html#moving_char_up',1,'']]],
  ['checking_20for_20extensions_11',['Checking for extensions',['../context_guide.html#context_glext_string',1,'']]],
  ['checking_20the_20window_20close_20flag_12',['Checking the window close flag',['../quick_guide.html#quick_window_close',1,'']]],
  ['clipboard_20input_20and_20output_13',['Clipboard input and output',['../input_guide.html#clipboard',1,'']]],
  ['close_20flag_14',['close flag',['../quick_guide.html#quick_window_close',1,'Checking the window close flag'],['../window_guide.html#window_close',1,'Window closing and close flag']]],
  ['closing_20and_20close_20flag_15',['Window closing and close flag',['../window_guide.html#window_close',1,'']]],
  ['closing_20changes_16',['Window closing changes',['../moving_guide.html#moving_window_close',1,'']]],
  ['cmake_17',['cmake',['../compile_guide.html#compile_generate',1,'Generating build files with CMake'],['../compile_guide.html#compile_generate_cli',1,'Generating with command-line CMake'],['../compile_guide.html#compile_cmake',1,'Using CMake']]],
  ['cmake_20and_20glfw_20source_18',['With CMake and GLFW source',['../build_guide.html#build_link_cmake_source',1,'']]],
  ['cmake_20and_20installed_20glfw_20binaries_19',['With CMake and installed GLFW binaries',['../build_guide.html#build_link_cmake_package',1,'']]],
  ['cmake_20and_20mingw_20',['Cross-compilation with CMake and MinGW',['../compile_guide.html#compile_mingw_cross',1,'']]],
  ['cmake_20gui_21',['Generating with the CMake GUI',['../compile_guide.html#compile_generate_gui',1,'']]],
  ['cmake_20option_20has_20been_20removed_22',['cmake option has been removed',['../news.html#use_osmesa_removed',1,'GLFW_USE_OSMESA CMake option has been removed'],['../news.html#use_wayland_removed',1,'GLFW_USE_WAYLAND CMake option has been removed'],['../news.html#vulkan_static_removed',1,'GLFW_VULKAN_STATIC CMake option has been removed']]],
  ['cmake_20options_23',['cmake options',['../compile_guide.html#compile_options',1,'CMake options'],['../compile_guide.html#compile_options_macos',1,'macOS specific CMake options'],['../compile_guide.html#compile_options_shared',1,'Shared CMake options'],['../compile_guide.html#compile_options_unix',1,'Unix-like system specific CMake options'],['../compile_guide.html#compile_options_win32',1,'Win32 specific CMake options']]],
  ['cocoa_20nsview_20native_20access_20function_24',['Cocoa NSView native access function',['../news.html#cocoa_nsview_function',1,'']]],
  ['codes_25',['Error codes',['../group__errors.html',1,'']]],
  ['command_20hint_26',['Windows STARTUPINFO show command hint',['../news.html#win32_showdefault_hint',1,'']]],
  ['command_20line_20cmake_27',['Generating with command-line CMake',['../compile_guide.html#compile_generate_cli',1,'']]],
  ['command_20line_20or_20makefile_20on_20macos_28',['With command-line or makefile on macOS',['../build_guide.html#build_link_osx',1,'']]],
  ['compat_2emd_29',['compat.md',['../compat_8md.html',1,'']]],
  ['compatibility_30',['Version compatibility',['../intro_guide.html#compatibility',1,'']]],
  ['compilation_20with_20cmake_20and_20mingw_31',['Cross-compilation with CMake and MinGW',['../compile_guide.html#compile_mingw_cross',1,'']]],
  ['compile_20time_20version_32',['Compile-time version',['../intro_guide.html#intro_version_compile',1,'']]],
  ['compile_2emd_33',['compile.md',['../compile_8md.html',1,'']]],
  ['compiling_20glfw_34',['Compiling GLFW',['../compile_guide.html',1,'']]],
  ['compiling_20glfw_20manually_35',['Compiling GLFW manually',['../compile_guide.html#compile_manual',1,'']]],
  ['compiling_20the_20library_36',['Compiling the library',['../compile_guide.html#compile_compile',1,'']]],
  ['config_20and_20glfw_20binaries_20on_20unix_37',['With pkg-config and GLFW binaries on Unix',['../build_guide.html#build_link_pkgconfig',1,'']]],
  ['configuration_20changes_38',['configuration changes',['../input_guide.html#joystick_event',1,'Joystick configuration changes'],['../monitor_guide.html#monitor_event',1,'Monitor configuration changes']]],
  ['configuration_20header_20is_20no_20longer_20generated_39',['Configuration header is no longer generated',['../news.html#config_header_caveat',1,'']]],
  ['configuration_20macros_40',['Configuration macros',['../internals_guide.html#internals_config',1,'']]],
  ['conformance_41',['Standards conformance',['../compat_guide.html',1,'']]],
  ['constants_42',['New constants',['../news.html#new_constants',1,'']]],
  ['constraints_43',['Hard and soft constraints',['../window_guide.html#window_hints_hard',1,'']]],
  ['content_20scale_44',['content scale',['../monitor_guide.html#monitor_scale',1,'Content scale'],['../window_guide.html#window_scale',1,'Window content scale']]],
  ['context_45',['context',['../quick_guide.html#quick_create_window',1,'Creating a window and context'],['../context_guide.html#context_current',1,'Current context']]],
  ['context_20creation_20hints_46',['Context creation hints',['../context_guide.html#context_hints',1,'']]],
  ['context_20current_47',['Making the OpenGL context current',['../quick_guide.html#quick_context_current',1,'']]],
  ['context_20guide_48',['Context guide',['../context_guide.html',1,'']]],
  ['context_20management_49',['Explicit context management',['../moving_guide.html#moving_context',1,'']]],
  ['context_20object_20sharing_50',['Context object sharing',['../context_guide.html#context_sharing',1,'']]],
  ['context_20objects_51',['Context objects',['../context_guide.html#context_object',1,'']]],
  ['context_20reference_52',['Context reference',['../group__context.html',1,'']]],
  ['context_20related_20attributes_53',['Context related attributes',['../window_guide.html#window_attribs_ctx',1,'']]],
  ['context_20related_20hints_54',['Context related hints',['../window_guide.html#window_hints_ctx',1,'']]],
  ['context_2emd_55',['context.md',['../context_8md.html',1,'']]],
  ['contexts_56',['contexts',['../context_guide.html#context_offscreen',1,'Offscreen contexts'],['../context_guide.html#context_less',1,'Windows without contexts']]],
  ['coordinate_20systems_57',['Coordinate systems',['../intro_guide.html#coordinate_systems',1,'']]],
  ['corevideo_20dependency_20has_20been_20removed_58',['macOS CoreVideo dependency has been removed',['../news.html#corevideo_caveat',1,'']]],
  ['created_20at_20initialization_59',['macOS main menu now created at initialization',['../news.html#macos_menu_caveat',1,'']]],
  ['creating_20a_20vulkan_20window_20surface_60',['Creating a Vulkan window surface',['../vulkan_guide.html#vulkan_surface',1,'']]],
  ['creating_20a_20window_20and_20context_61',['Creating a window and context',['../quick_guide.html#quick_create_window',1,'']]],
  ['creating_20the_20window_62',['Creating the window',['../vulkan_guide.html#vulkan_window',1,'']]],
  ['creation_63',['creation',['../input_guide.html#cursor_custom',1,'Custom cursor creation'],['../input_guide.html#cursor_standard',1,'Standard cursor creation'],['../window_guide.html#window_creation',1,'Window creation']]],
  ['creation_20hints_64',['creation hints',['../context_guide.html#context_hints',1,'Context creation hints'],['../window_guide.html#window_hints',1,'Window creation hints']]],
  ['cross_20compilation_20with_20cmake_20and_20mingw_65',['Cross-compilation with CMake and MinGW',['../compile_guide.html#compile_mingw_cross',1,'']]],
  ['current_66',['Making the OpenGL context current',['../quick_guide.html#quick_context_current',1,'']]],
  ['current_20context_67',['Current context',['../context_guide.html#context_current',1,'']]],
  ['cursor_20creation_68',['cursor creation',['../input_guide.html#cursor_custom',1,'Custom cursor creation'],['../input_guide.html#cursor_standard',1,'Standard cursor creation']]],
  ['cursor_20destruction_69',['Cursor destruction',['../input_guide.html#cursor_destruction',1,'']]],
  ['cursor_20enter_20leave_20events_70',['Cursor enter/leave events',['../input_guide.html#cursor_enter',1,'']]],
  ['cursor_20mode_71',['cursor mode',['../news.html#captured_cursor_mode',1,'Captured cursor mode'],['../input_guide.html#cursor_mode',1,'Cursor mode']]],
  ['cursor_20objects_72',['Cursor objects',['../input_guide.html#cursor_object',1,'']]],
  ['cursor_20position_73',['Cursor position',['../input_guide.html#cursor_pos',1,'']]],
  ['cursor_20position_20changes_74',['Cursor position changes',['../moving_guide.html#moving_cursorpos',1,'']]],
  ['cursor_20setting_75',['Cursor setting',['../input_guide.html#cursor_set',1,'']]],
  ['cursor_20shapes_76',['cursor shapes',['../news.html#more_cursor_shapes',1,'More standard cursor shapes'],['../group__shapes.html',1,'Standard cursor shapes']]],
  ['custom_20cursor_20creation_77',['Custom cursor creation',['../input_guide.html#cursor_custom',1,'']]],
  ['custom_20heap_20memory_20allocator_78',['custom heap memory allocator',['../intro_guide.html#init_allocator',1,'Custom heap memory allocator'],['../news.html#custom_heap_allocator',1,'Support for custom heap memory allocator']]]
];
