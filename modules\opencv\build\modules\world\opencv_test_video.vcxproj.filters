﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\test\ocl\test_bgfg_mog2.cpp">
      <Filter>opencv_video\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\test\ocl\test_dis.cpp">
      <Filter>opencv_video\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\test\ocl\test_optflow_farneback.cpp">
      <Filter>opencv_video\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\test\ocl\test_optflowpyrlk.cpp">
      <Filter>opencv_video\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\test\test_OF_accuracy.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\test\test_OF_reproducibility.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\test\test_accum.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\test\test_camshift.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\test\test_ecc.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\test\test_estimaterigid.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\test\test_kalman.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\test\test_main.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\test\test_optflowpyrlk.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\video\test\test_trackers.cpp">
      <Filter>opencv_video\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\video\test\test_precomp.hpp">
      <Filter>opencv_video\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\video\test\test_trackers.impl.hpp">
      <Filter>opencv_video\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_video">
      <UniqueIdentifier>{AF88D1C3-D1DD-3F7C-A3F4-F14A50A9C567}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_video\Include">
      <UniqueIdentifier>{EF67B89B-773A-39AE-93E5-ED0FB73D1C0D}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_video\Src">
      <UniqueIdentifier>{8AC6AE3F-77AB-3B3F-B33F-DBBB50684B8A}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_video\Src\ocl">
      <UniqueIdentifier>{90040BC0-673B-3169-B9F2-2617F6CBA5FD}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
