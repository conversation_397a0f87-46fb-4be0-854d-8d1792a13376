﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\any_lite.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\arena.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\arenastring.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\extension_set.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\generated_message_util.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\implicit_weak_message.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\io\coded_stream.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\io\io_win32.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\io\strtod.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\io\zero_copy_stream.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\io\zero_copy_stream_impl.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\io\zero_copy_stream_impl_lite.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\map.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\message_lite.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\parse_context.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\repeated_field.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\repeated_ptr_field.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\bytestream.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\common.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\int128.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\status.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\stringpiece.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\stringprintf.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\structurally_valid.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\strutil.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\wire_format_lite.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\any.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\descriptor.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\descriptor.pb.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\descriptor_database.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\dynamic_message.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\extension_set_heavy.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\generated_message_reflection.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\io\tokenizer.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\map_field.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\message.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\reflection_ops.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\stubs\substitute.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\text_format.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\unknown_field_set.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src\google\protobuf\wire_format.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{2A318434-2169-3E81-A653-C1C0490B5893}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
