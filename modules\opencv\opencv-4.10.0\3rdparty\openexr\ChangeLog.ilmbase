Version 2.x.x
	* Bumped version to track OpenEXR
	  (<PERSON><PERSON><PERSON>)

Version 2.0.1
	* Bumped version to track OpenEXR
	  (<PERSON><PERSON><PERSON>)

Version 2.0.0
	* Bumped version to track OpenEXR
	  (<PERSON><PERSON><PERSON>)
	* Numerous minor fixes, missing includes etc

Version 1.1.0.beta.1
	* Added new module PyIlmBase : python bindings for IlmBase
	  (<PERSON>)
	* Added git specific files 
	  (<PERSON><PERSON><PERSON>)
	* Minor fixes for newer gcc versions and OS X.
	  (misc)
	* Preparation for OpenEXR v2 release { remove message for final release }
	  (<PERSON><PERSON><PERSON>)
        * Updated the so verison to 10
	  (<PERSON><PERSON><PERSON>)
	* Initial use of the CMake build system 
	  (<PERSON>)

Version 1.0.3
        * Added support for enabling/disabling large stack optimisations, used in
          halfFunction.h.
          (<PERSON><PERSON><PERSON>)
	* Added ImathNoise.(h/cpp) files. Initializes Perlin noise to match the
	  Renderman implmenetation.
	  (Pixar Contribution)
	* Fixed a number of missing includes to comply with stricter 
	  enforcement by gnu compilers.
	  (<PERSON><PERSON><PERSON>)
	* Depracated compiler flag: -Wno-long-double since it is no longer
	  supported under OS X.
	  (<PERSON><PERSON><PERSON>)
	* A minor API change to Imath::Frustum has been made:  the functions
	  'near' and 'far' have been renamed to 'nearPlane' and 'farPlane' due
	  to conflicts with certain windows headers.  The former alternate
	  accessor names for these values on windows ('hither' and 'yon')
	  remain, though should be considered deprecated.
	  (David Lenihan)
	* Added SVD, eigenvalue solver, and procrustes fit calculations
	  to ImathMatrixAlgo.
	  (Chris Twigg, Ji Hun Yu)
	* Added Imath::FrustumTest for frustum visibility testing.
	  (Eric Johnston)
	* Fixed a stack corruption in the matrix minorOf functions.
	  (Nick Rasmussen)
	* Visual studio 2008 project files have been added to the vc/vc9
	  directory, and several minor visual studio compile fixes have
	  been applied.
	  (Nick Rasmussen)
	* Updated the so verison to 7.
	  (Piotr Stanczyk)
	* Depracated the MacCode_Warrior and Shake submodules.
	  (Piotr Stanczyk)

Version 1.0.2
	* Added support for targetting builds on 64bit Windows and minimising 
	  number of compiler warnings on Windows. Thanks to Ger Hobbelt for his
	  contributions to CreateDLL.
	  (Ji Hun Yu)
	* Removed a spurious restrict qualifier in the matrix manipulation code
	  that was causing the 64-bit MS compiler to generate code in release 
	  mode that caused incorrect results. 
	  (Ji Hun Yu)
	* Added patches for improving universal binaries on OS X. Thanks to
	  Paul Schneider for the contribution
	  (Piotr Stanczyk)
	* Imath::Box optimization: remove loops from methods by partially 
	  specializing the class, for boxes of two and three dimensions.
	  (Piotr Stanczyk)
	* Added explicit copy constructors to Imath::Matrix33<T> and
	  ImathMatrix44<T> to make conversions between float and double
	  matrices more convenient.
	  (Florian Kainz)
	* Added slerpShortestArc() and euclideanInnerProduct() functions
	  to Imath::Quat<T>.
	  (Nick Porcino)
	* Added 4D vector class template Imath::Vec4<T>.
	  (Nick Porcino)
	* Copy constructors and assignment operators for Matrix33<T>
	  and Matrix44<T> are up to 25% faster.  Added matrix constructors
	  that do not initialize the matrix (this is faster in cases where
	  the initial value of the matrix is immediately overwritten anyway).
	  (Nick Porcino)
	* Rewrote function closestPointOnBox(point,box).  Shortened
	  the code, improved numerical accuracy, fixed a bug where
	  closestPointOnBox(box.center(),box) would return the center
	  of the +Z side of the box, even if the +/-X or +/-Y sides
	  were closer.
	  (Florian Kainz)
	* Rewrote function findEntryAndExitPoints() in ImathBoxAlgo.h.
	  Results are now consistent with those from intersect(), also
	  in ImathBoxAlgo.h.
	  (Florian Kainz)
	* Made Vec2<T>::length() and Vec3<T>::length() more accurate for
	  vectors whose length is less than sqrt(limits<T>::smallest());
	  (Florian Kainz)
	* Made Quat<T>::angle() more accurate for small angles.
	  (Don Hatch) 

Version 1.0.1:
	* Removed Windows .suo files from distribution.
	  (Eric Wimmer)

Version 1.0.0:
	* Bumped DSO version number to 6.0
	  (Florian Kainz)
	* Rounding during float-to-half conversion now implements
	  "round to nearest even" mode: if the original float value
	  is exactly in the middle between the two closest half values
	  then rounding chooses the half value whose least significant
	  bit is zero.
	  (Florian Kainz)
	* Installation Tuning:
	  - Corrected version number on dso's (libtool) - now 5.0
	  - Separated ILMBASE_LDFLAGS and ILMBASE_LIBS so that test programs
	    in configure scripts of packages dependent on IlmBase can link
	    with static libraries properly
	  - eliminated some warning messages during install
	  (Andrew Kunz)
	
Version 0.9.0:
	* Initial release of this code as a separate library.
	  Previously the libraries contained were part of
	  version 1.4.0 of OpenEXR
	* New build scripts for Linux/Unix
	  (Andrew Kunz)
	* New Windows project files and build scripts
	  (Kimball Thurston)
