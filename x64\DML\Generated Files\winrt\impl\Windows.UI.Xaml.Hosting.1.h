// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Hosting_1_H
#define WINRT_Windows_UI_Xaml_Hosting_1_H
#include "winrt/impl/Windows.UI.Xaml.Hosting.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Hosting
{
    struct WINRT_IMPL_EMPTY_BASES IDesignerAppExitedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDesignerAppExitedEventArgs>
    {
        IDesignerAppExitedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDesignerAppExitedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDesignerAppManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDesignerAppManager>
    {
        IDesignerAppManager(std::nullptr_t = nullptr) noexcept {}
        IDesignerAppManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDesignerAppManagerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDesignerAppManagerFactory>
    {
        IDesignerAppManagerFactory(std::nullptr_t = nullptr) noexcept {}
        IDesignerAppManagerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDesignerAppView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDesignerAppView>
    {
        IDesignerAppView(std::nullptr_t = nullptr) noexcept {}
        IDesignerAppView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDesktopWindowXamlSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDesktopWindowXamlSource>
    {
        IDesktopWindowXamlSource(std::nullptr_t = nullptr) noexcept {}
        IDesktopWindowXamlSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDesktopWindowXamlSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDesktopWindowXamlSourceFactory>
    {
        IDesktopWindowXamlSourceFactory(std::nullptr_t = nullptr) noexcept {}
        IDesktopWindowXamlSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDesktopWindowXamlSourceGotFocusEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDesktopWindowXamlSourceGotFocusEventArgs>
    {
        IDesktopWindowXamlSourceGotFocusEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDesktopWindowXamlSourceGotFocusEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDesktopWindowXamlSourceTakeFocusRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDesktopWindowXamlSourceTakeFocusRequestedEventArgs>
    {
        IDesktopWindowXamlSourceTakeFocusRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDesktopWindowXamlSourceTakeFocusRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IElementCompositionPreview :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElementCompositionPreview>
    {
        IElementCompositionPreview(std::nullptr_t = nullptr) noexcept {}
        IElementCompositionPreview(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IElementCompositionPreviewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElementCompositionPreviewStatics>
    {
        IElementCompositionPreviewStatics(std::nullptr_t = nullptr) noexcept {}
        IElementCompositionPreviewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IElementCompositionPreviewStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElementCompositionPreviewStatics2>
    {
        IElementCompositionPreviewStatics2(std::nullptr_t = nullptr) noexcept {}
        IElementCompositionPreviewStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IElementCompositionPreviewStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElementCompositionPreviewStatics3>
    {
        IElementCompositionPreviewStatics3(std::nullptr_t = nullptr) noexcept {}
        IElementCompositionPreviewStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsXamlManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsXamlManager>
    {
        IWindowsXamlManager(std::nullptr_t = nullptr) noexcept {}
        IWindowsXamlManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsXamlManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsXamlManagerStatics>
    {
        IWindowsXamlManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IWindowsXamlManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlSourceFocusNavigationRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlSourceFocusNavigationRequest>
    {
        IXamlSourceFocusNavigationRequest(std::nullptr_t = nullptr) noexcept {}
        IXamlSourceFocusNavigationRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlSourceFocusNavigationRequestFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlSourceFocusNavigationRequestFactory>
    {
        IXamlSourceFocusNavigationRequestFactory(std::nullptr_t = nullptr) noexcept {}
        IXamlSourceFocusNavigationRequestFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlSourceFocusNavigationResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlSourceFocusNavigationResult>
    {
        IXamlSourceFocusNavigationResult(std::nullptr_t = nullptr) noexcept {}
        IXamlSourceFocusNavigationResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlSourceFocusNavigationResultFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlSourceFocusNavigationResultFactory>
    {
        IXamlSourceFocusNavigationResultFactory(std::nullptr_t = nullptr) noexcept {}
        IXamlSourceFocusNavigationResultFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlUIPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlUIPresenter>
    {
        IXamlUIPresenter(std::nullptr_t = nullptr) noexcept {}
        IXamlUIPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlUIPresenterHost :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlUIPresenterHost>
    {
        IXamlUIPresenterHost(std::nullptr_t = nullptr) noexcept {}
        IXamlUIPresenterHost(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlUIPresenterHost2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlUIPresenterHost2>
    {
        IXamlUIPresenterHost2(std::nullptr_t = nullptr) noexcept {}
        IXamlUIPresenterHost2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlUIPresenterHost3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlUIPresenterHost3>
    {
        IXamlUIPresenterHost3(std::nullptr_t = nullptr) noexcept {}
        IXamlUIPresenterHost3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlUIPresenterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlUIPresenterStatics>
    {
        IXamlUIPresenterStatics(std::nullptr_t = nullptr) noexcept {}
        IXamlUIPresenterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlUIPresenterStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlUIPresenterStatics2>
    {
        IXamlUIPresenterStatics2(std::nullptr_t = nullptr) noexcept {}
        IXamlUIPresenterStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
