﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\perf\opencl\perf_stereobm.cpp">
      <Filter>opencv_calib3d\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\perf\perf_affine2d.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\perf\perf_cicrlesGrid.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\perf\perf_main.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\perf\perf_pnp.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\perf\perf_stereosgbm.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\perf\perf_undistort.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\perf\perf_precomp.hpp">
      <Filter>opencv_calib3d\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_calib3d">
      <UniqueIdentifier>{5793AB40-0443-396B-B27D-D8B999CC6F98}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_calib3d\Include">
      <UniqueIdentifier>{93EA1108-B0BB-3337-AD6C-DF6B8AA65C55}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_calib3d\Src">
      <UniqueIdentifier>{E6EEE02A-D60F-3044-B61D-DEAC04AF6489}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_calib3d\Src\opencl">
      <UniqueIdentifier>{54AD7CCA-4A11-3F74-A288-A57565481BFC}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
