{"const_ignore_list": ["CV_TM_.+", "CV_COLORCVT_MAX", "CV_.*<PERSON><PERSON>.*", "CV_YUV420(i|sp|p)2.+", "CV_L?(BGRA?|RGBA?|GRAY|XYZ|YCrCb|Luv|Lab|HLS|YUV|HSV)\\d*2L?(BGRA?|RGBA?|GRAY|XYZ|YCrCb|Luv|Lab|HLS|YUV|HSV).*", "CV_FLOODFILL_.+", "CV_ADAPTIVE_THRESH_.+"], "const_private_list": ["CV_MOP_.+", "CV_INTER_.+", "CV_THRESH_.+", "CV_INPAINT_.+", "CV_RETR_.+", "CV_CHAIN_APPROX_.+"], "missing_consts": {"Imgproc": {"private": [["IPL_BORDER_CONSTANT", 0], ["IPL_BORDER_REPLICATE", 1], ["IPL_BORDER_REFLECT", 2], ["IPL_BORDER_WRAP", 3], ["IPL_BORDER_REFLECT_101", 4], ["IPL_BORDER_TRANSPARENT", 5]]}}, "ManualFuncs": {"Imgproc": {"getTextSize": {"j_code": ["\n", "// C++: Size getTextSize(const String& text, int fontFace, double fontScale, int thickness, int* baseLine);", "//javadoc:getTextSize(text, fontFace, fontScale, thickness, baseLine)", "public static Size getTextSize(String text, int fontFace, double fontScale, int thickness, int[] baseLine) {", "    if(baseLine != null && baseLine.length != 1)", "        throw new java.lang.IllegalArgumentException(\"'baseLine' must be 'int[1]' or 'null'.\");", "    Size retVal = new Size(n_getTextSize(text, fontFace, fontScale, thickness, baseLine));", "    return retVal;", "}", "\n"], "jn_code": ["private static native double[] n_getTextSize(String text, int fontFace, double fontScale, int thickness, int[] baseLine);\n"], "cpp_code": ["\n", "    // C++: Size getTextSize(const String& text, int fontFace, double fontScale, int thickness, int* baseLine);", "    JNIEXPORT jdoubleArray JNICALL Java_org_opencv_imgproc_Imgproc_n_1getTextSize (JNIEnv*, jclass, jstring, jint, jdouble, jint, jint<PERSON>rray);", "\n", "    JNIEXPORT jdoubleArray JNICALL Java_org_opencv_imgproc_Imgproc_n_1getTextSize", "    (JNIEnv* env, jclass, jstring text, jint fontFace, jdouble fontScale, jint thickness, jintArray baseLine)", "    {", "    try {", "        LOGD(\"Core::n_1getTextSize()\");", "        jdoubleArray result;", "        result = env->NewDoubleArray(2);", "        if (result == NULL) {", "            return NULL; /* out of memory error thrown */", "        }", "\n", "        const char* utf_text = env->GetStringUTFChars(text, 0);", "        String n_text( utf_text ? utf_text : \"\" );", "        env->ReleaseStringUTFChars(text, utf_text);", "\n", "        int _baseLine;", "        int* pbaseLine = 0;", "\n", "        if (baseLine != NULL)", "            pbaseLine = &_baseLine;", "\n", "        cv::Size rsize = cv::getTextSize(n_text, (int)fontFace, (double)fontScale, (int)thickness, pbaseLine);", "\n", "        j<PERSON><PERSON>le fill[2];", "        fill[0]=rsize.width;", "        fill[1]=rsize.height;", "\n", "        env->SetDoubleArrayRegion(result, 0, 2, fill);", "\n", "        if (baseLine != NULL) {", "            jint jbaseLine = (jint)(*pbaseLine);", "            env->SetIntArrayRegion(baseLine, 0, 1, &jbaseLine);", "        }", "\n", "        return result;", "\n", "    } catch(const cv::Exception& e) {", "        LOGD(\"Imgproc::n_1getTextSize() caught cv::Exception: %s\", e.what());", "        jclass je = env->FindClass(\"org/opencv/core/CvException\");", "        if(!je) je = env->FindClass(\"java/lang/Exception\");", "        env->ThrowNew(je, e.what());", "        return NULL;", "    } catch (...) {", "        LOGD(\"Imgproc::n_1getTextSize() caught unknown exception (...)\");", "        jclass je = env->FindClass(\"java/lang/Exception\");", "        env->ThrowNew(je, \"Unknown exception in JNI code {core::getTextSize()}\");", "        return NULL;", "    }", "    }"]}}}, "func_arg_fix": {"goodFeaturesToTrack": {"corners": {"ctype": "vector_Point"}}, "minEnclosingCircle": {"points": {"ctype": "vector_Point2f"}}, "fitEllipse": {"points": {"ctype": "vector_Point2f"}}, "fillPoly": {"pts": {"ctype": "vector_vector_Point"}}, "polylines": {"pts": {"ctype": "vector_vector_Point"}}, "fillConvexPoly": {"points": {"ctype": "vector_Point"}}, "approxPolyDP": {"curve": {"ctype": "vector_Point2f"}, "approxCurve": {"ctype": "vector_Point2f"}}, "arcLength": {"curve": {"ctype": "vector_Point2f"}}, "pointPolygonTest": {"contour": {"ctype": "vector_Point2f"}}, "minAreaRect": {"points": {"ctype": "vector_Point2f"}}, "getAffineTransform": {"src": {"ctype": "vector_Point2f"}, "dst": {"ctype": "vector_Point2f"}}, "drawContours": {"contours": {"ctype": "vector_vector_Point"}}, "findContours": {"contours": {"ctype": "vector_vector_Point"}}, "convexityDefects": {"contour": {"ctype": "vector_Point"}, "convexhull": {"ctype": "vector_int"}, "convexityDefects": {"ctype": "vector_Vec4i"}}, "isContourConvex": {"contour": {"ctype": "vector_Point"}}, "convexHull": {"points": {"ctype": "vector_Point"}, "hull": {"ctype": "vector_int"}, "returnPoints": {"ctype": ""}}}}