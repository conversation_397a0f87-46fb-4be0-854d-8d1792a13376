﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_core.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_color_convert_all.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_color_convert_rgbs.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_bilateral.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_box.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_canny.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_gaussian.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_general.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_laplacian.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_morphology.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_scharr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_sobel.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_copy.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_copy_channel.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_copy_make_border.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_copy_merge.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_copy_split.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_scale.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_set.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_set_channel.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_swap_channels.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_transform_mirror.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_transform_resize.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_transform_rotate.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_transform_warpaffine.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_own.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw++\iw.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw++\iw_core.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw++\iw_image.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw++\iw_image_color.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw++\iw_image_filter.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw++\iw_image_op.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw++\iw_image_transform.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw_core.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw_image.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw_image_color.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw_image_filter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw_image_op.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw_image_transform.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw_ll.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw_version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw_config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw_own.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw_owni.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{792E8A66-904F-3E91-91DB-8CBB2E5E760B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{2A318434-2169-3E81-A653-C1C0490B5893}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
