// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_AI_ModelContextProtocol_1_H
#define WINRT_Windows_AI_ModelContextProtocol_1_H
#include "winrt/impl/Windows.Foundation.0.h"
#include "winrt/impl/Windows.AI.ModelContextProtocol.0.h"
WINRT_EXPORT namespace winrt::Windows::AI::ModelContextProtocol
{
    struct WINRT_IMPL_EMPTY_BASES IModelContextProtocolClientContext :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IModelContextProtocolClientContext>
    {
        IModelContextProtocolClientContext(std::nullptr_t = nullptr) noexcept {}
        IModelContextProtocolClientContext(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IModelContextProtocolClientContextFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IModelContextProtocolClientContextFactory>
    {
        IModelContextProtocolClientContextFactory(std::nullptr_t = nullptr) noexcept {}
        IModelContextProtocolClientContextFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IModelContextProtocolServer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IModelContextProtocolServer>,
        impl::require<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServer, winrt::Windows::Foundation::IClosable>
    {
        IModelContextProtocolServer(std::nullptr_t = nullptr) noexcept {}
        IModelContextProtocolServer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IModelContextProtocolServerCatalog :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IModelContextProtocolServerCatalog>
    {
        IModelContextProtocolServerCatalog(std::nullptr_t = nullptr) noexcept {}
        IModelContextProtocolServerCatalog(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IModelContextProtocolServerCatalogFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IModelContextProtocolServerCatalogFactory>
    {
        IModelContextProtocolServerCatalogFactory(std::nullptr_t = nullptr) noexcept {}
        IModelContextProtocolServerCatalogFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IModelContextProtocolServerInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IModelContextProtocolServerInfo>
    {
        IModelContextProtocolServerInfo(std::nullptr_t = nullptr) noexcept {}
        IModelContextProtocolServerInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IModelContextProtocolServerInfoFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IModelContextProtocolServerInfoFactory>
    {
        IModelContextProtocolServerInfoFactory(std::nullptr_t = nullptr) noexcept {}
        IModelContextProtocolServerInfoFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
