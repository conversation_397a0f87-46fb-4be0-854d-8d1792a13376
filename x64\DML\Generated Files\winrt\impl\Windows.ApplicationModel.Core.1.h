// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_ApplicationModel_Core_1_H
#define WINRT_Windows_ApplicationModel_Core_1_H
#include "winrt/impl/Windows.ApplicationModel.Core.0.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Core
{
    struct WINRT_IMPL_EMPTY_BASES IAppListEntry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppListEntry>
    {
        IAppListEntry(std::nullptr_t = nullptr) noexcept {}
        IAppListEntry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppListEntry2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppListEntry2>
    {
        IAppListEntry2(std::nullptr_t = nullptr) noexcept {}
        IAppListEntry2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppListEntry3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppListEntry3>
    {
        IAppListEntry3(std::nullptr_t = nullptr) noexcept {}
        IAppListEntry3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppListEntry4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppListEntry4>
    {
        IAppListEntry4(std::nullptr_t = nullptr) noexcept {}
        IAppListEntry4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreApplication :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplication>
    {
        ICoreApplication(std::nullptr_t = nullptr) noexcept {}
        ICoreApplication(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreApplication2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplication2>
    {
        ICoreApplication2(std::nullptr_t = nullptr) noexcept {}
        ICoreApplication2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreApplication3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplication3>
    {
        ICoreApplication3(std::nullptr_t = nullptr) noexcept {}
        ICoreApplication3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreApplicationExit :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationExit>
    {
        ICoreApplicationExit(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationExit(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreApplicationUnhandledError :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationUnhandledError>
    {
        ICoreApplicationUnhandledError(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationUnhandledError(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreApplicationUseCount :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationUseCount>
    {
        ICoreApplicationUseCount(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationUseCount(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreApplicationView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationView>
    {
        ICoreApplicationView(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreApplicationView2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationView2>
    {
        ICoreApplicationView2(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationView2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreApplicationView3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationView3>
    {
        ICoreApplicationView3(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationView3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreApplicationView5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationView5>
    {
        ICoreApplicationView5(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationView5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreApplicationView6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationView6>
    {
        ICoreApplicationView6(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationView6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreApplicationViewTitleBar :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreApplicationViewTitleBar>
    {
        ICoreApplicationViewTitleBar(std::nullptr_t = nullptr) noexcept {}
        ICoreApplicationViewTitleBar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreImmersiveApplication :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreImmersiveApplication>
    {
        ICoreImmersiveApplication(std::nullptr_t = nullptr) noexcept {}
        ICoreImmersiveApplication(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreImmersiveApplication2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreImmersiveApplication2>
    {
        ICoreImmersiveApplication2(std::nullptr_t = nullptr) noexcept {}
        ICoreImmersiveApplication2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreImmersiveApplication3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreImmersiveApplication3>
    {
        ICoreImmersiveApplication3(std::nullptr_t = nullptr) noexcept {}
        ICoreImmersiveApplication3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkView>
    {
        IFrameworkView(std::nullptr_t = nullptr) noexcept {}
        IFrameworkView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkViewSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkViewSource>
    {
        IFrameworkViewSource(std::nullptr_t = nullptr) noexcept {}
        IFrameworkViewSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHostedViewClosingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHostedViewClosingEventArgs>
    {
        IHostedViewClosingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IHostedViewClosingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUnhandledError :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUnhandledError>
    {
        IUnhandledError(std::nullptr_t = nullptr) noexcept {}
        IUnhandledError(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUnhandledErrorDetectedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUnhandledErrorDetectedEventArgs>
    {
        IUnhandledErrorDetectedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IUnhandledErrorDetectedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
