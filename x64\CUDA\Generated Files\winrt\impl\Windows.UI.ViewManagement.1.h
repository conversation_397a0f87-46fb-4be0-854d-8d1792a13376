// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_UI_ViewManagement_1_H
#define WINRT_Windows_UI_ViewManagement_1_H
#include "winrt/impl/Windows.UI.ViewManagement.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::ViewManagement
{
    struct WINRT_IMPL_EMPTY_BASES IAccessibilitySettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccessibilitySettings>
    {
        IAccessibilitySettings(std::nullptr_t = nullptr) noexcept {}
        IAccessibilitySettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActivationViewSwitcher :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActivationViewSwitcher>
    {
        IActivationViewSwitcher(std::nullptr_t = nullptr) noexcept {}
        IActivationViewSwitcher(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationView>
    {
        IApplicationView(std::nullptr_t = nullptr) noexcept {}
        IApplicationView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationView2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationView2>
    {
        IApplicationView2(std::nullptr_t = nullptr) noexcept {}
        IApplicationView2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationView3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationView3>
    {
        IApplicationView3(std::nullptr_t = nullptr) noexcept {}
        IApplicationView3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationView4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationView4>
    {
        IApplicationView4(std::nullptr_t = nullptr) noexcept {}
        IApplicationView4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationView7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationView7>
    {
        IApplicationView7(std::nullptr_t = nullptr) noexcept {}
        IApplicationView7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationView9 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationView9>
    {
        IApplicationView9(std::nullptr_t = nullptr) noexcept {}
        IApplicationView9(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewConsolidatedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewConsolidatedEventArgs>
    {
        IApplicationViewConsolidatedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewConsolidatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewConsolidatedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewConsolidatedEventArgs2>
    {
        IApplicationViewConsolidatedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewConsolidatedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewFullscreenStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewFullscreenStatics>
    {
        IApplicationViewFullscreenStatics(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewFullscreenStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewInteropStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewInteropStatics>
    {
        IApplicationViewInteropStatics(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewInteropStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewScaling :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewScaling>
    {
        IApplicationViewScaling(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewScaling(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewScalingStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewScalingStatics>
    {
        IApplicationViewScalingStatics(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewScalingStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewStatics>
    {
        IApplicationViewStatics(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewStatics2>
    {
        IApplicationViewStatics2(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewStatics3>
    {
        IApplicationViewStatics3(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewStatics4>
    {
        IApplicationViewStatics4(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewSwitcherStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewSwitcherStatics>
    {
        IApplicationViewSwitcherStatics(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewSwitcherStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewSwitcherStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewSwitcherStatics2>
    {
        IApplicationViewSwitcherStatics2(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewSwitcherStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewSwitcherStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewSwitcherStatics3>
    {
        IApplicationViewSwitcherStatics3(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewSwitcherStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewTitleBar :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewTitleBar>
    {
        IApplicationViewTitleBar(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewTitleBar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewTransferContext :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewTransferContext>
    {
        IApplicationViewTransferContext(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewTransferContext(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewTransferContextStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewTransferContextStatics>
    {
        IApplicationViewTransferContextStatics(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewTransferContextStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationViewWithContext :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationViewWithContext>
    {
        IApplicationViewWithContext(std::nullptr_t = nullptr) noexcept {}
        IApplicationViewWithContext(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInputPane :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInputPane>
    {
        IInputPane(std::nullptr_t = nullptr) noexcept {}
        IInputPane(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInputPane2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInputPane2>
    {
        IInputPane2(std::nullptr_t = nullptr) noexcept {}
        IInputPane2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInputPaneControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInputPaneControl>
    {
        IInputPaneControl(std::nullptr_t = nullptr) noexcept {}
        IInputPaneControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInputPaneStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInputPaneStatics>
    {
        IInputPaneStatics(std::nullptr_t = nullptr) noexcept {}
        IInputPaneStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInputPaneStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInputPaneStatics2>
    {
        IInputPaneStatics2(std::nullptr_t = nullptr) noexcept {}
        IInputPaneStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInputPaneVisibilityEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInputPaneVisibilityEventArgs>
    {
        IInputPaneVisibilityEventArgs(std::nullptr_t = nullptr) noexcept {}
        IInputPaneVisibilityEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProjectionManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProjectionManagerStatics>
    {
        IProjectionManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IProjectionManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProjectionManagerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProjectionManagerStatics2>
    {
        IProjectionManagerStatics2(std::nullptr_t = nullptr) noexcept {}
        IProjectionManagerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUISettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUISettings>
    {
        IUISettings(std::nullptr_t = nullptr) noexcept {}
        IUISettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUISettings2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUISettings2>
    {
        IUISettings2(std::nullptr_t = nullptr) noexcept {}
        IUISettings2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUISettings3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUISettings3>
    {
        IUISettings3(std::nullptr_t = nullptr) noexcept {}
        IUISettings3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUISettings4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUISettings4>
    {
        IUISettings4(std::nullptr_t = nullptr) noexcept {}
        IUISettings4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUISettings5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUISettings5>
    {
        IUISettings5(std::nullptr_t = nullptr) noexcept {}
        IUISettings5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUISettings6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUISettings6>
    {
        IUISettings6(std::nullptr_t = nullptr) noexcept {}
        IUISettings6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUISettingsAnimationsEnabledChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUISettingsAnimationsEnabledChangedEventArgs>
    {
        IUISettingsAnimationsEnabledChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IUISettingsAnimationsEnabledChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUISettingsAutoHideScrollBarsChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUISettingsAutoHideScrollBarsChangedEventArgs>
    {
        IUISettingsAutoHideScrollBarsChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IUISettingsAutoHideScrollBarsChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUISettingsMessageDurationChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUISettingsMessageDurationChangedEventArgs>
    {
        IUISettingsMessageDurationChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IUISettingsMessageDurationChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIViewSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIViewSettings>
    {
        IUIViewSettings(std::nullptr_t = nullptr) noexcept {}
        IUIViewSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIViewSettingsPreferredInteractionMode :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIViewSettingsPreferredInteractionMode>
    {
        IUIViewSettingsPreferredInteractionMode(std::nullptr_t = nullptr) noexcept {}
        IUIViewSettingsPreferredInteractionMode(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIViewSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIViewSettingsStatics>
    {
        IUIViewSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        IUIViewSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewModePreferences :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewModePreferences>
    {
        IViewModePreferences(std::nullptr_t = nullptr) noexcept {}
        IViewModePreferences(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewModePreferencesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewModePreferencesStatics>
    {
        IViewModePreferencesStatics(std::nullptr_t = nullptr) noexcept {}
        IViewModePreferencesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
