# Authors of OpenJPEG
See also [THANKS](https://github.com/uclouvain/openjpeg/blob/master/THANKS.md)

<PERSON> designed and implemented the first version of OpenJPEG.

<PERSON><PERSON> designed and implemented the first version of OpenJPIP.

<PERSON> implemented the alpha version of OpenJPEG 2.0.

<PERSON> added the JPWL functionalities.

<PERSON><PERSON><PERSON> implemented the final OpenJPEG 2.0 version based on a big merge between 1.5 version and alpha version of 2.0.

<PERSON><PERSON> participated to the OpenJPEG 2.0 version and improved the libraries and utilities. 

<PERSON><PERSON>, 
<PERSON><PERSON>, 
<PERSON><PERSON><PERSON><PERSON>, 
<PERSON><PERSON>
    improved the libraries and utilities.

