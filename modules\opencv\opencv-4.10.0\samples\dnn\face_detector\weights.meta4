<?xml version="1.0" encoding="UTF-8"?>
<metalink xmlns="urn:ietf:params:xml:ns:metalink">
    <file name="res10_300x300_ssd_iter_140000_fp16.caffemodel">
        <identity>opencv_face_detector_fp16</identity>
        <hash type="sha-1">31fc22bfdd907567a04bb45b7cfad29966caddc1</hash>
        <url>https://raw.githubusercontent.com/opencv/opencv_3rdparty/dnn_samples_face_detector_20180205_fp16/res10_300x300_ssd_iter_140000_fp16.caffemodel</url>
    </file>
    <file name="opencv_face_detector_uint8.pb">
        <identity>opencv_face_detector_uint8</identity>
        <hash type="sha-1">4f2fdf6f231d759d7bbdb94353c5a68690f3d2ae</hash>
        <url>https://raw.githubusercontent.com/opencv/opencv_3rdparty/dnn_samples_face_detector_20180220_uint8/opencv_face_detector_uint8.pb</url>
    </file>
</metalink>
