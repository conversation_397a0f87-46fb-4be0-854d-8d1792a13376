// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_UI_Input_Inking_Preview_1_H
#define WINRT_Windows_UI_Input_Inking_Preview_1_H
#include "winrt/impl/Windows.UI.Input.Inking.Preview.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Input::Inking::Preview
{
    struct WINRT_IMPL_EMPTY_BASES IPalmRejectionDelayZonePreview :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPalmRejectionDelayZonePreview>
    {
        IPalmRejectionDelayZonePreview(std::nullptr_t = nullptr) noexcept {}
        IPalmRejectionDelayZonePreview(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPalmRejectionDelayZonePreviewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPalmRejectionDelayZonePreviewStatics>
    {
        IPalmRejectionDelayZonePreviewStatics(std::nullptr_t = nullptr) noexcept {}
        IPalmRejectionDelayZonePreviewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
