if (typeof window === 'undefined') {
  var cv = require("../opencv");
  if (cv instanceof Promise) {
    loadOpenCV();
  } else {
    cv.onRuntimeInitialized = perf;
  }
}

let gCvSize;

function getCvSize() {
  if (gCvSize === undefined) {
    gCvSize = {
      szODD: new cv.<PERSON>ze(127, 61),
      szQVGA: new cv.<PERSON>ze(320, 240),
      szVGA: new cv.<PERSON>ze(640, 480),
      szSVGA: new cv.<PERSON>ze(800, 600),
      szqHD: new cv.<PERSON>ze(960, 540),
      szXGA: new cv.<PERSON>ze(1024, 768),
      sz720p: new cv.<PERSON>ze(1280, 720),
      szSXGA: new cv.<PERSON>ze(1280, 1024),
      sz1080p: new cv.<PERSON>ze(1920, 1080),
      sz130x60: new cv.<PERSON><PERSON>(130, 60),
      sz213x120: new cv.<PERSON>ze(120 * 1280 / 720, 120),
    };
  }

  return gCvSize;
}

async function loadOpenCV() {
  cv = await cv;
}

if (typeof window === 'undefined') {
  exports.getCvSize = getCvSize;
}