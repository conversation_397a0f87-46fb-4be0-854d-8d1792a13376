<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Keyboard key tokens</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle"><div class="title">Keyboard key tokens<div class="ingroups"><a class="el" href="group__input.html">Input reference</a></div></div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>See <a class="el" href="input_guide.html#input_key">key input</a> for how these are used.</p>
<p>These key codes are inspired by the <em>USB HID Usage Tables v1.12</em> (p. 53-60), but re-arranged to map to 7-bit ASCII for printable keys (function keys are put in the 256+ range).</p>
<p>The naming of the key codes follow these rules:</p><ul>
<li>The US keyboard layout is used</li>
<li>Names of printable alphanumeric characters are used (e.g. "A", "R", "3", etc.)</li>
<li>For non-alphanumeric characters, Unicode:ish names are used (e.g. "COMMA", "LEFT_SQUARE_BRACKET", etc.). Note that some names do not correspond to the Unicode standard (usually for brevity)</li>
<li>Keys that lack a clear US mapping are named "WORLD_x"</li>
<li>For non-printable keys, custom names are used (e.g. "F4", "BACKSPACE", etc.) </li>
</ul>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:gaddb2c23772b97fd7e26e8ee66f1ad014" id="r_gaddb2c23772b97fd7e26e8ee66f1ad014"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaddb2c23772b97fd7e26e8ee66f1ad014">GLFW_KEY_SPACE</a>&#160;&#160;&#160;32</td></tr>
<tr class="separator:gaddb2c23772b97fd7e26e8ee66f1ad014"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6059b0b048ba6980b6107fffbd3b4b24" id="r_ga6059b0b048ba6980b6107fffbd3b4b24"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga6059b0b048ba6980b6107fffbd3b4b24">GLFW_KEY_APOSTROPHE</a>&#160;&#160;&#160;39  /* ' */</td></tr>
<tr class="separator:ga6059b0b048ba6980b6107fffbd3b4b24"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab3d5d72e59d3055f494627b0a524926c" id="r_gab3d5d72e59d3055f494627b0a524926c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gab3d5d72e59d3055f494627b0a524926c">GLFW_KEY_COMMA</a>&#160;&#160;&#160;44  /* , */</td></tr>
<tr class="separator:gab3d5d72e59d3055f494627b0a524926c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac556b360f7f6fca4b70ba0aecf313fd4" id="r_gac556b360f7f6fca4b70ba0aecf313fd4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gac556b360f7f6fca4b70ba0aecf313fd4">GLFW_KEY_MINUS</a>&#160;&#160;&#160;45  /* - */</td></tr>
<tr class="separator:gac556b360f7f6fca4b70ba0aecf313fd4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga37e296b650eab419fc474ff69033d927" id="r_ga37e296b650eab419fc474ff69033d927"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga37e296b650eab419fc474ff69033d927">GLFW_KEY_PERIOD</a>&#160;&#160;&#160;46  /* . */</td></tr>
<tr class="separator:ga37e296b650eab419fc474ff69033d927"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadf3d753b2d479148d711de34b83fd0db" id="r_gadf3d753b2d479148d711de34b83fd0db"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gadf3d753b2d479148d711de34b83fd0db">GLFW_KEY_SLASH</a>&#160;&#160;&#160;47  /* / */</td></tr>
<tr class="separator:gadf3d753b2d479148d711de34b83fd0db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga50391730e9d7112ad4fd42d0bd1597c1" id="r_ga50391730e9d7112ad4fd42d0bd1597c1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga50391730e9d7112ad4fd42d0bd1597c1">GLFW_KEY_0</a>&#160;&#160;&#160;48</td></tr>
<tr class="separator:ga50391730e9d7112ad4fd42d0bd1597c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga05e4cae9ddb8d40cf6d82c8f11f2502f" id="r_ga05e4cae9ddb8d40cf6d82c8f11f2502f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga05e4cae9ddb8d40cf6d82c8f11f2502f">GLFW_KEY_1</a>&#160;&#160;&#160;49</td></tr>
<tr class="separator:ga05e4cae9ddb8d40cf6d82c8f11f2502f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadc8e66b3a4c4b5c39ad1305cf852863c" id="r_gadc8e66b3a4c4b5c39ad1305cf852863c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gadc8e66b3a4c4b5c39ad1305cf852863c">GLFW_KEY_2</a>&#160;&#160;&#160;50</td></tr>
<tr class="separator:gadc8e66b3a4c4b5c39ad1305cf852863c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga812f0273fe1a981e1fa002ae73e92271" id="r_ga812f0273fe1a981e1fa002ae73e92271"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga812f0273fe1a981e1fa002ae73e92271">GLFW_KEY_3</a>&#160;&#160;&#160;51</td></tr>
<tr class="separator:ga812f0273fe1a981e1fa002ae73e92271"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9e14b6975a9cc8f66cdd5cb3d3861356" id="r_ga9e14b6975a9cc8f66cdd5cb3d3861356"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga9e14b6975a9cc8f66cdd5cb3d3861356">GLFW_KEY_4</a>&#160;&#160;&#160;52</td></tr>
<tr class="separator:ga9e14b6975a9cc8f66cdd5cb3d3861356"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4d74ddaa5d4c609993b4d4a15736c924" id="r_ga4d74ddaa5d4c609993b4d4a15736c924"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga4d74ddaa5d4c609993b4d4a15736c924">GLFW_KEY_5</a>&#160;&#160;&#160;53</td></tr>
<tr class="separator:ga4d74ddaa5d4c609993b4d4a15736c924"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9ea4ab80c313a227b14d0a7c6f810b5d" id="r_ga9ea4ab80c313a227b14d0a7c6f810b5d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga9ea4ab80c313a227b14d0a7c6f810b5d">GLFW_KEY_6</a>&#160;&#160;&#160;54</td></tr>
<tr class="separator:ga9ea4ab80c313a227b14d0a7c6f810b5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab79b1cfae7bd630cfc4604c1f263c666" id="r_gab79b1cfae7bd630cfc4604c1f263c666"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gab79b1cfae7bd630cfc4604c1f263c666">GLFW_KEY_7</a>&#160;&#160;&#160;55</td></tr>
<tr class="separator:gab79b1cfae7bd630cfc4604c1f263c666"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadeaa109a0f9f5afc94fe4a108e686f6f" id="r_gadeaa109a0f9f5afc94fe4a108e686f6f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gadeaa109a0f9f5afc94fe4a108e686f6f">GLFW_KEY_8</a>&#160;&#160;&#160;56</td></tr>
<tr class="separator:gadeaa109a0f9f5afc94fe4a108e686f6f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2924cb5349ebbf97c8987f3521c44f39" id="r_ga2924cb5349ebbf97c8987f3521c44f39"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga2924cb5349ebbf97c8987f3521c44f39">GLFW_KEY_9</a>&#160;&#160;&#160;57</td></tr>
<tr class="separator:ga2924cb5349ebbf97c8987f3521c44f39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga84233de9ee5bb3e8788a5aa07d80af7d" id="r_ga84233de9ee5bb3e8788a5aa07d80af7d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga84233de9ee5bb3e8788a5aa07d80af7d">GLFW_KEY_SEMICOLON</a>&#160;&#160;&#160;59  /* ; */</td></tr>
<tr class="separator:ga84233de9ee5bb3e8788a5aa07d80af7d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae1a2de47240d6664423c204bdd91bd17" id="r_gae1a2de47240d6664423c204bdd91bd17"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gae1a2de47240d6664423c204bdd91bd17">GLFW_KEY_EQUAL</a>&#160;&#160;&#160;61  /* = */</td></tr>
<tr class="separator:gae1a2de47240d6664423c204bdd91bd17"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga03e842608e1ea323370889d33b8f70ff" id="r_ga03e842608e1ea323370889d33b8f70ff"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga03e842608e1ea323370889d33b8f70ff">GLFW_KEY_A</a>&#160;&#160;&#160;65</td></tr>
<tr class="separator:ga03e842608e1ea323370889d33b8f70ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8e3fb647ff3aca9e8dbf14fe66332941" id="r_ga8e3fb647ff3aca9e8dbf14fe66332941"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga8e3fb647ff3aca9e8dbf14fe66332941">GLFW_KEY_B</a>&#160;&#160;&#160;66</td></tr>
<tr class="separator:ga8e3fb647ff3aca9e8dbf14fe66332941"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga00ccf3475d9ee2e679480d540d554669" id="r_ga00ccf3475d9ee2e679480d540d554669"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga00ccf3475d9ee2e679480d540d554669">GLFW_KEY_C</a>&#160;&#160;&#160;67</td></tr>
<tr class="separator:ga00ccf3475d9ee2e679480d540d554669"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga011f7cdc9a654da984a2506479606933" id="r_ga011f7cdc9a654da984a2506479606933"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga011f7cdc9a654da984a2506479606933">GLFW_KEY_D</a>&#160;&#160;&#160;68</td></tr>
<tr class="separator:ga011f7cdc9a654da984a2506479606933"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabf48fcc3afbe69349df432b470c96ef2" id="r_gabf48fcc3afbe69349df432b470c96ef2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gabf48fcc3afbe69349df432b470c96ef2">GLFW_KEY_E</a>&#160;&#160;&#160;69</td></tr>
<tr class="separator:gabf48fcc3afbe69349df432b470c96ef2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5df402e02aca08444240058fd9b42a55" id="r_ga5df402e02aca08444240058fd9b42a55"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga5df402e02aca08444240058fd9b42a55">GLFW_KEY_F</a>&#160;&#160;&#160;70</td></tr>
<tr class="separator:ga5df402e02aca08444240058fd9b42a55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae74ecddf7cc96104ab23989b1cdab536" id="r_gae74ecddf7cc96104ab23989b1cdab536"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gae74ecddf7cc96104ab23989b1cdab536">GLFW_KEY_G</a>&#160;&#160;&#160;71</td></tr>
<tr class="separator:gae74ecddf7cc96104ab23989b1cdab536"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad4cc98fc8f35f015d9e2fb94bf136076" id="r_gad4cc98fc8f35f015d9e2fb94bf136076"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gad4cc98fc8f35f015d9e2fb94bf136076">GLFW_KEY_H</a>&#160;&#160;&#160;72</td></tr>
<tr class="separator:gad4cc98fc8f35f015d9e2fb94bf136076"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga274655c8bfe39742684ca393cf8ed093" id="r_ga274655c8bfe39742684ca393cf8ed093"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga274655c8bfe39742684ca393cf8ed093">GLFW_KEY_I</a>&#160;&#160;&#160;73</td></tr>
<tr class="separator:ga274655c8bfe39742684ca393cf8ed093"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga65ff2aedb129a3149ad9cb3e4159a75f" id="r_ga65ff2aedb129a3149ad9cb3e4159a75f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga65ff2aedb129a3149ad9cb3e4159a75f">GLFW_KEY_J</a>&#160;&#160;&#160;74</td></tr>
<tr class="separator:ga65ff2aedb129a3149ad9cb3e4159a75f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4ae8debadf6d2a691badae0b53ea3ba0" id="r_ga4ae8debadf6d2a691badae0b53ea3ba0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga4ae8debadf6d2a691badae0b53ea3ba0">GLFW_KEY_K</a>&#160;&#160;&#160;75</td></tr>
<tr class="separator:ga4ae8debadf6d2a691badae0b53ea3ba0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaa8b54a13f6b1eed85ac86f82d550db2" id="r_gaaa8b54a13f6b1eed85ac86f82d550db2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaaa8b54a13f6b1eed85ac86f82d550db2">GLFW_KEY_L</a>&#160;&#160;&#160;76</td></tr>
<tr class="separator:gaaa8b54a13f6b1eed85ac86f82d550db2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4d7f0260c82e4ea3d6ebc7a21d6e3716" id="r_ga4d7f0260c82e4ea3d6ebc7a21d6e3716"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga4d7f0260c82e4ea3d6ebc7a21d6e3716">GLFW_KEY_M</a>&#160;&#160;&#160;77</td></tr>
<tr class="separator:ga4d7f0260c82e4ea3d6ebc7a21d6e3716"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae00856dfeb5d13aafebf59d44de5cdda" id="r_gae00856dfeb5d13aafebf59d44de5cdda"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gae00856dfeb5d13aafebf59d44de5cdda">GLFW_KEY_N</a>&#160;&#160;&#160;78</td></tr>
<tr class="separator:gae00856dfeb5d13aafebf59d44de5cdda"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaecbbb79130df419d58dd7f09a169efe9" id="r_gaecbbb79130df419d58dd7f09a169efe9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaecbbb79130df419d58dd7f09a169efe9">GLFW_KEY_O</a>&#160;&#160;&#160;79</td></tr>
<tr class="separator:gaecbbb79130df419d58dd7f09a169efe9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8fc15819c1094fb2afa01d84546b33e1" id="r_ga8fc15819c1094fb2afa01d84546b33e1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga8fc15819c1094fb2afa01d84546b33e1">GLFW_KEY_P</a>&#160;&#160;&#160;80</td></tr>
<tr class="separator:ga8fc15819c1094fb2afa01d84546b33e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafdd01e38b120d67cf51e348bb47f3964" id="r_gafdd01e38b120d67cf51e348bb47f3964"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gafdd01e38b120d67cf51e348bb47f3964">GLFW_KEY_Q</a>&#160;&#160;&#160;81</td></tr>
<tr class="separator:gafdd01e38b120d67cf51e348bb47f3964"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4ce6c70a0c98c50b3fe4ab9a728d4d36" id="r_ga4ce6c70a0c98c50b3fe4ab9a728d4d36"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga4ce6c70a0c98c50b3fe4ab9a728d4d36">GLFW_KEY_R</a>&#160;&#160;&#160;82</td></tr>
<tr class="separator:ga4ce6c70a0c98c50b3fe4ab9a728d4d36"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1570e2ccaab036ea82bed66fc1dab2a9" id="r_ga1570e2ccaab036ea82bed66fc1dab2a9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga1570e2ccaab036ea82bed66fc1dab2a9">GLFW_KEY_S</a>&#160;&#160;&#160;83</td></tr>
<tr class="separator:ga1570e2ccaab036ea82bed66fc1dab2a9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga90e0560422ec7a30e7f3f375bc9f37f9" id="r_ga90e0560422ec7a30e7f3f375bc9f37f9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga90e0560422ec7a30e7f3f375bc9f37f9">GLFW_KEY_T</a>&#160;&#160;&#160;84</td></tr>
<tr class="separator:ga90e0560422ec7a30e7f3f375bc9f37f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacad52f3bf7d378fc0ffa72a76769256d" id="r_gacad52f3bf7d378fc0ffa72a76769256d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gacad52f3bf7d378fc0ffa72a76769256d">GLFW_KEY_U</a>&#160;&#160;&#160;85</td></tr>
<tr class="separator:gacad52f3bf7d378fc0ffa72a76769256d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga22c7763899ecf7788862e5f90eacce6b" id="r_ga22c7763899ecf7788862e5f90eacce6b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga22c7763899ecf7788862e5f90eacce6b">GLFW_KEY_V</a>&#160;&#160;&#160;86</td></tr>
<tr class="separator:ga22c7763899ecf7788862e5f90eacce6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa06a712e6202661fc03da5bdb7b6e545" id="r_gaa06a712e6202661fc03da5bdb7b6e545"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaa06a712e6202661fc03da5bdb7b6e545">GLFW_KEY_W</a>&#160;&#160;&#160;87</td></tr>
<tr class="separator:gaa06a712e6202661fc03da5bdb7b6e545"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac1c42c0bf4192cea713c55598b06b744" id="r_gac1c42c0bf4192cea713c55598b06b744"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gac1c42c0bf4192cea713c55598b06b744">GLFW_KEY_X</a>&#160;&#160;&#160;88</td></tr>
<tr class="separator:gac1c42c0bf4192cea713c55598b06b744"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafd9f115a549effdf8e372a787c360313" id="r_gafd9f115a549effdf8e372a787c360313"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gafd9f115a549effdf8e372a787c360313">GLFW_KEY_Y</a>&#160;&#160;&#160;89</td></tr>
<tr class="separator:gafd9f115a549effdf8e372a787c360313"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac489e208c26afda8d4938ed88718760a" id="r_gac489e208c26afda8d4938ed88718760a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gac489e208c26afda8d4938ed88718760a">GLFW_KEY_Z</a>&#160;&#160;&#160;90</td></tr>
<tr class="separator:gac489e208c26afda8d4938ed88718760a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad1c8d9adac53925276ecb1d592511d8a" id="r_gad1c8d9adac53925276ecb1d592511d8a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gad1c8d9adac53925276ecb1d592511d8a">GLFW_KEY_LEFT_BRACKET</a>&#160;&#160;&#160;91  /* [ */</td></tr>
<tr class="separator:gad1c8d9adac53925276ecb1d592511d8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab8155ea99d1ab27ff56f24f8dc73f8d1" id="r_gab8155ea99d1ab27ff56f24f8dc73f8d1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gab8155ea99d1ab27ff56f24f8dc73f8d1">GLFW_KEY_BACKSLASH</a>&#160;&#160;&#160;92  /* \ */</td></tr>
<tr class="separator:gab8155ea99d1ab27ff56f24f8dc73f8d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga86ef225fd6a66404caae71044cdd58d8" id="r_ga86ef225fd6a66404caae71044cdd58d8"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga86ef225fd6a66404caae71044cdd58d8">GLFW_KEY_RIGHT_BRACKET</a>&#160;&#160;&#160;93  /* ] */</td></tr>
<tr class="separator:ga86ef225fd6a66404caae71044cdd58d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7a3701fb4e2a0b136ff4b568c3c8d668" id="r_ga7a3701fb4e2a0b136ff4b568c3c8d668"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga7a3701fb4e2a0b136ff4b568c3c8d668">GLFW_KEY_GRAVE_ACCENT</a>&#160;&#160;&#160;96  /* ` */</td></tr>
<tr class="separator:ga7a3701fb4e2a0b136ff4b568c3c8d668"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadc78dad3dab76bcd4b5c20114052577a" id="r_gadc78dad3dab76bcd4b5c20114052577a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gadc78dad3dab76bcd4b5c20114052577a">GLFW_KEY_WORLD_1</a>&#160;&#160;&#160;161 /* non-US #1 */</td></tr>
<tr class="separator:gadc78dad3dab76bcd4b5c20114052577a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga20494bfebf0bb4fc9503afca18ab2c5e" id="r_ga20494bfebf0bb4fc9503afca18ab2c5e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga20494bfebf0bb4fc9503afca18ab2c5e">GLFW_KEY_WORLD_2</a>&#160;&#160;&#160;162 /* non-US #2 */</td></tr>
<tr class="separator:ga20494bfebf0bb4fc9503afca18ab2c5e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaac6596c350b635c245113b81c2123b93" id="r_gaac6596c350b635c245113b81c2123b93"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaac6596c350b635c245113b81c2123b93">GLFW_KEY_ESCAPE</a>&#160;&#160;&#160;256</td></tr>
<tr class="separator:gaac6596c350b635c245113b81c2123b93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9555a92ecbecdbc1f3435219c571d667" id="r_ga9555a92ecbecdbc1f3435219c571d667"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga9555a92ecbecdbc1f3435219c571d667">GLFW_KEY_ENTER</a>&#160;&#160;&#160;257</td></tr>
<tr class="separator:ga9555a92ecbecdbc1f3435219c571d667"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6908a4bda9950a3e2b73f794bbe985df" id="r_ga6908a4bda9950a3e2b73f794bbe985df"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga6908a4bda9950a3e2b73f794bbe985df">GLFW_KEY_TAB</a>&#160;&#160;&#160;258</td></tr>
<tr class="separator:ga6908a4bda9950a3e2b73f794bbe985df"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6c0df1fe2f156bbd5a98c66d76ff3635" id="r_ga6c0df1fe2f156bbd5a98c66d76ff3635"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga6c0df1fe2f156bbd5a98c66d76ff3635">GLFW_KEY_BACKSPACE</a>&#160;&#160;&#160;259</td></tr>
<tr class="separator:ga6c0df1fe2f156bbd5a98c66d76ff3635"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga373ac7365435d6b0eb1068f470e34f47" id="r_ga373ac7365435d6b0eb1068f470e34f47"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga373ac7365435d6b0eb1068f470e34f47">GLFW_KEY_INSERT</a>&#160;&#160;&#160;260</td></tr>
<tr class="separator:ga373ac7365435d6b0eb1068f470e34f47"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadb111e4df74b8a715f2c05dad58d2682" id="r_gadb111e4df74b8a715f2c05dad58d2682"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gadb111e4df74b8a715f2c05dad58d2682">GLFW_KEY_DELETE</a>&#160;&#160;&#160;261</td></tr>
<tr class="separator:gadb111e4df74b8a715f2c05dad58d2682"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga06ba07662e8c291a4a84535379ffc7ac" id="r_ga06ba07662e8c291a4a84535379ffc7ac"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga06ba07662e8c291a4a84535379ffc7ac">GLFW_KEY_RIGHT</a>&#160;&#160;&#160;262</td></tr>
<tr class="separator:ga06ba07662e8c291a4a84535379ffc7ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae12a010d33c309a67ab9460c51eb2462" id="r_gae12a010d33c309a67ab9460c51eb2462"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gae12a010d33c309a67ab9460c51eb2462">GLFW_KEY_LEFT</a>&#160;&#160;&#160;263</td></tr>
<tr class="separator:gae12a010d33c309a67ab9460c51eb2462"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae2e3958c71595607416aa7bf082be2f9" id="r_gae2e3958c71595607416aa7bf082be2f9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gae2e3958c71595607416aa7bf082be2f9">GLFW_KEY_DOWN</a>&#160;&#160;&#160;264</td></tr>
<tr class="separator:gae2e3958c71595607416aa7bf082be2f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2f3342b194020d3544c67e3506b6f144" id="r_ga2f3342b194020d3544c67e3506b6f144"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga2f3342b194020d3544c67e3506b6f144">GLFW_KEY_UP</a>&#160;&#160;&#160;265</td></tr>
<tr class="separator:ga2f3342b194020d3544c67e3506b6f144"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3ab731f9622f0db280178a5f3cc6d586" id="r_ga3ab731f9622f0db280178a5f3cc6d586"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga3ab731f9622f0db280178a5f3cc6d586">GLFW_KEY_PAGE_UP</a>&#160;&#160;&#160;266</td></tr>
<tr class="separator:ga3ab731f9622f0db280178a5f3cc6d586"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaee0a8fa442001cc2147812f84b59041c" id="r_gaee0a8fa442001cc2147812f84b59041c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaee0a8fa442001cc2147812f84b59041c">GLFW_KEY_PAGE_DOWN</a>&#160;&#160;&#160;267</td></tr>
<tr class="separator:gaee0a8fa442001cc2147812f84b59041c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga41452c7287195d481e43207318c126a7" id="r_ga41452c7287195d481e43207318c126a7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga41452c7287195d481e43207318c126a7">GLFW_KEY_HOME</a>&#160;&#160;&#160;268</td></tr>
<tr class="separator:ga41452c7287195d481e43207318c126a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga86587ea1df19a65978d3e3b8439bedd9" id="r_ga86587ea1df19a65978d3e3b8439bedd9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga86587ea1df19a65978d3e3b8439bedd9">GLFW_KEY_END</a>&#160;&#160;&#160;269</td></tr>
<tr class="separator:ga86587ea1df19a65978d3e3b8439bedd9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga92c1d2c9d63485f3d70f94f688d48672" id="r_ga92c1d2c9d63485f3d70f94f688d48672"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga92c1d2c9d63485f3d70f94f688d48672">GLFW_KEY_CAPS_LOCK</a>&#160;&#160;&#160;280</td></tr>
<tr class="separator:ga92c1d2c9d63485f3d70f94f688d48672"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf622b63b9537f7084c2ab649b8365630" id="r_gaf622b63b9537f7084c2ab649b8365630"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaf622b63b9537f7084c2ab649b8365630">GLFW_KEY_SCROLL_LOCK</a>&#160;&#160;&#160;281</td></tr>
<tr class="separator:gaf622b63b9537f7084c2ab649b8365630"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3946edc362aeff213b2be6304296cf43" id="r_ga3946edc362aeff213b2be6304296cf43"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga3946edc362aeff213b2be6304296cf43">GLFW_KEY_NUM_LOCK</a>&#160;&#160;&#160;282</td></tr>
<tr class="separator:ga3946edc362aeff213b2be6304296cf43"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf964c2e65e97d0cf785a5636ee8df642" id="r_gaf964c2e65e97d0cf785a5636ee8df642"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaf964c2e65e97d0cf785a5636ee8df642">GLFW_KEY_PRINT_SCREEN</a>&#160;&#160;&#160;283</td></tr>
<tr class="separator:gaf964c2e65e97d0cf785a5636ee8df642"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8116b9692d87382afb5849b6d8907f18" id="r_ga8116b9692d87382afb5849b6d8907f18"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga8116b9692d87382afb5849b6d8907f18">GLFW_KEY_PAUSE</a>&#160;&#160;&#160;284</td></tr>
<tr class="separator:ga8116b9692d87382afb5849b6d8907f18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafb8d66c573acf22e364049477dcbea30" id="r_gafb8d66c573acf22e364049477dcbea30"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gafb8d66c573acf22e364049477dcbea30">GLFW_KEY_F1</a>&#160;&#160;&#160;290</td></tr>
<tr class="separator:gafb8d66c573acf22e364049477dcbea30"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0900750aff94889b940f5e428c07daee" id="r_ga0900750aff94889b940f5e428c07daee"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga0900750aff94889b940f5e428c07daee">GLFW_KEY_F2</a>&#160;&#160;&#160;291</td></tr>
<tr class="separator:ga0900750aff94889b940f5e428c07daee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaed7cd729c0147a551bb8b7bb36c17015" id="r_gaed7cd729c0147a551bb8b7bb36c17015"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaed7cd729c0147a551bb8b7bb36c17015">GLFW_KEY_F3</a>&#160;&#160;&#160;292</td></tr>
<tr class="separator:gaed7cd729c0147a551bb8b7bb36c17015"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9b61ebd0c63b44b7332fda2c9763eaa6" id="r_ga9b61ebd0c63b44b7332fda2c9763eaa6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga9b61ebd0c63b44b7332fda2c9763eaa6">GLFW_KEY_F4</a>&#160;&#160;&#160;293</td></tr>
<tr class="separator:ga9b61ebd0c63b44b7332fda2c9763eaa6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf258dda9947daa428377938ed577c8c2" id="r_gaf258dda9947daa428377938ed577c8c2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaf258dda9947daa428377938ed577c8c2">GLFW_KEY_F5</a>&#160;&#160;&#160;294</td></tr>
<tr class="separator:gaf258dda9947daa428377938ed577c8c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6dc2d3f87b9d51ffbbbe2ef0299d8e1d" id="r_ga6dc2d3f87b9d51ffbbbe2ef0299d8e1d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga6dc2d3f87b9d51ffbbbe2ef0299d8e1d">GLFW_KEY_F6</a>&#160;&#160;&#160;295</td></tr>
<tr class="separator:ga6dc2d3f87b9d51ffbbbe2ef0299d8e1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacca6ef8a2162c52a0ac1d881e8d9c38a" id="r_gacca6ef8a2162c52a0ac1d881e8d9c38a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gacca6ef8a2162c52a0ac1d881e8d9c38a">GLFW_KEY_F7</a>&#160;&#160;&#160;296</td></tr>
<tr class="separator:gacca6ef8a2162c52a0ac1d881e8d9c38a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac9d39390336ae14e4a93e295de43c7e8" id="r_gac9d39390336ae14e4a93e295de43c7e8"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gac9d39390336ae14e4a93e295de43c7e8">GLFW_KEY_F8</a>&#160;&#160;&#160;297</td></tr>
<tr class="separator:gac9d39390336ae14e4a93e295de43c7e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae40de0de1c9f21cd26c9afa3d7050851" id="r_gae40de0de1c9f21cd26c9afa3d7050851"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gae40de0de1c9f21cd26c9afa3d7050851">GLFW_KEY_F9</a>&#160;&#160;&#160;298</td></tr>
<tr class="separator:gae40de0de1c9f21cd26c9afa3d7050851"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga718d11d2f7d57471a2f6a894235995b1" id="r_ga718d11d2f7d57471a2f6a894235995b1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga718d11d2f7d57471a2f6a894235995b1">GLFW_KEY_F10</a>&#160;&#160;&#160;299</td></tr>
<tr class="separator:ga718d11d2f7d57471a2f6a894235995b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0bc04b11627e7d69339151e7306b2832" id="r_ga0bc04b11627e7d69339151e7306b2832"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga0bc04b11627e7d69339151e7306b2832">GLFW_KEY_F11</a>&#160;&#160;&#160;300</td></tr>
<tr class="separator:ga0bc04b11627e7d69339151e7306b2832"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf5908fa9b0a906ae03fc2c61ac7aa3e2" id="r_gaf5908fa9b0a906ae03fc2c61ac7aa3e2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaf5908fa9b0a906ae03fc2c61ac7aa3e2">GLFW_KEY_F12</a>&#160;&#160;&#160;301</td></tr>
<tr class="separator:gaf5908fa9b0a906ae03fc2c61ac7aa3e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad637f4308655e1001bd6ad942bc0fd4b" id="r_gad637f4308655e1001bd6ad942bc0fd4b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gad637f4308655e1001bd6ad942bc0fd4b">GLFW_KEY_F13</a>&#160;&#160;&#160;302</td></tr>
<tr class="separator:gad637f4308655e1001bd6ad942bc0fd4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf14c66cff3396e5bd46e803c035e6c1f" id="r_gaf14c66cff3396e5bd46e803c035e6c1f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaf14c66cff3396e5bd46e803c035e6c1f">GLFW_KEY_F14</a>&#160;&#160;&#160;303</td></tr>
<tr class="separator:gaf14c66cff3396e5bd46e803c035e6c1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f70970db6e8be1794da8516a6d14058" id="r_ga7f70970db6e8be1794da8516a6d14058"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga7f70970db6e8be1794da8516a6d14058">GLFW_KEY_F15</a>&#160;&#160;&#160;304</td></tr>
<tr class="separator:ga7f70970db6e8be1794da8516a6d14058"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa582dbb1d2ba2050aa1dca0838095b27" id="r_gaa582dbb1d2ba2050aa1dca0838095b27"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaa582dbb1d2ba2050aa1dca0838095b27">GLFW_KEY_F16</a>&#160;&#160;&#160;305</td></tr>
<tr class="separator:gaa582dbb1d2ba2050aa1dca0838095b27"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga972ce5c365e2394b36104b0e3125c748" id="r_ga972ce5c365e2394b36104b0e3125c748"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga972ce5c365e2394b36104b0e3125c748">GLFW_KEY_F17</a>&#160;&#160;&#160;306</td></tr>
<tr class="separator:ga972ce5c365e2394b36104b0e3125c748"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaebf6391058d5566601e357edc5ea737c" id="r_gaebf6391058d5566601e357edc5ea737c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaebf6391058d5566601e357edc5ea737c">GLFW_KEY_F18</a>&#160;&#160;&#160;307</td></tr>
<tr class="separator:gaebf6391058d5566601e357edc5ea737c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaec011d9ba044058cb54529da710e9791" id="r_gaec011d9ba044058cb54529da710e9791"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaec011d9ba044058cb54529da710e9791">GLFW_KEY_F19</a>&#160;&#160;&#160;308</td></tr>
<tr class="separator:gaec011d9ba044058cb54529da710e9791"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga82b9c721ada04cd5ca8de767da38022f" id="r_ga82b9c721ada04cd5ca8de767da38022f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga82b9c721ada04cd5ca8de767da38022f">GLFW_KEY_F20</a>&#160;&#160;&#160;309</td></tr>
<tr class="separator:ga82b9c721ada04cd5ca8de767da38022f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga356afb14d3440ff2bb378f74f7ebc60f" id="r_ga356afb14d3440ff2bb378f74f7ebc60f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga356afb14d3440ff2bb378f74f7ebc60f">GLFW_KEY_F21</a>&#160;&#160;&#160;310</td></tr>
<tr class="separator:ga356afb14d3440ff2bb378f74f7ebc60f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga90960bd2a155f2b09675324d3dff1565" id="r_ga90960bd2a155f2b09675324d3dff1565"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga90960bd2a155f2b09675324d3dff1565">GLFW_KEY_F22</a>&#160;&#160;&#160;311</td></tr>
<tr class="separator:ga90960bd2a155f2b09675324d3dff1565"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga43c21099aac10952d1be909a8ddee4d5" id="r_ga43c21099aac10952d1be909a8ddee4d5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga43c21099aac10952d1be909a8ddee4d5">GLFW_KEY_F23</a>&#160;&#160;&#160;312</td></tr>
<tr class="separator:ga43c21099aac10952d1be909a8ddee4d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8150374677b5bed3043408732152dea2" id="r_ga8150374677b5bed3043408732152dea2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga8150374677b5bed3043408732152dea2">GLFW_KEY_F24</a>&#160;&#160;&#160;313</td></tr>
<tr class="separator:ga8150374677b5bed3043408732152dea2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa4bbd93ed73bb4c6ae7d83df880b7199" id="r_gaa4bbd93ed73bb4c6ae7d83df880b7199"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaa4bbd93ed73bb4c6ae7d83df880b7199">GLFW_KEY_F25</a>&#160;&#160;&#160;314</td></tr>
<tr class="separator:gaa4bbd93ed73bb4c6ae7d83df880b7199"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga10515dafc55b71e7683f5b4fedd1c70d" id="r_ga10515dafc55b71e7683f5b4fedd1c70d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga10515dafc55b71e7683f5b4fedd1c70d">GLFW_KEY_KP_0</a>&#160;&#160;&#160;320</td></tr>
<tr class="separator:ga10515dafc55b71e7683f5b4fedd1c70d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf3a29a334402c5eaf0b3439edf5587c3" id="r_gaf3a29a334402c5eaf0b3439edf5587c3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaf3a29a334402c5eaf0b3439edf5587c3">GLFW_KEY_KP_1</a>&#160;&#160;&#160;321</td></tr>
<tr class="separator:gaf3a29a334402c5eaf0b3439edf5587c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf82d5a802ab8213c72653d7480c16f13" id="r_gaf82d5a802ab8213c72653d7480c16f13"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaf82d5a802ab8213c72653d7480c16f13">GLFW_KEY_KP_2</a>&#160;&#160;&#160;322</td></tr>
<tr class="separator:gaf82d5a802ab8213c72653d7480c16f13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7e25ff30d56cd512828c1d4ae8d54ef2" id="r_ga7e25ff30d56cd512828c1d4ae8d54ef2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga7e25ff30d56cd512828c1d4ae8d54ef2">GLFW_KEY_KP_3</a>&#160;&#160;&#160;323</td></tr>
<tr class="separator:ga7e25ff30d56cd512828c1d4ae8d54ef2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gada7ec86778b85e0b4de0beea72234aea" id="r_gada7ec86778b85e0b4de0beea72234aea"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gada7ec86778b85e0b4de0beea72234aea">GLFW_KEY_KP_4</a>&#160;&#160;&#160;324</td></tr>
<tr class="separator:gada7ec86778b85e0b4de0beea72234aea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9a5be274434866c51738cafbb6d26b45" id="r_ga9a5be274434866c51738cafbb6d26b45"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga9a5be274434866c51738cafbb6d26b45">GLFW_KEY_KP_5</a>&#160;&#160;&#160;325</td></tr>
<tr class="separator:ga9a5be274434866c51738cafbb6d26b45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafc141b0f8450519084c01092a3157faa" id="r_gafc141b0f8450519084c01092a3157faa"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gafc141b0f8450519084c01092a3157faa">GLFW_KEY_KP_6</a>&#160;&#160;&#160;326</td></tr>
<tr class="separator:gafc141b0f8450519084c01092a3157faa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8882f411f05d04ec77a9563974bbfa53" id="r_ga8882f411f05d04ec77a9563974bbfa53"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga8882f411f05d04ec77a9563974bbfa53">GLFW_KEY_KP_7</a>&#160;&#160;&#160;327</td></tr>
<tr class="separator:ga8882f411f05d04ec77a9563974bbfa53"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab2ea2e6a12f89d315045af520ac78cec" id="r_gab2ea2e6a12f89d315045af520ac78cec"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gab2ea2e6a12f89d315045af520ac78cec">GLFW_KEY_KP_8</a>&#160;&#160;&#160;328</td></tr>
<tr class="separator:gab2ea2e6a12f89d315045af520ac78cec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafb21426b630ed4fcc084868699ba74c1" id="r_gafb21426b630ed4fcc084868699ba74c1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gafb21426b630ed4fcc084868699ba74c1">GLFW_KEY_KP_9</a>&#160;&#160;&#160;329</td></tr>
<tr class="separator:gafb21426b630ed4fcc084868699ba74c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4e231d968796331a9ea0dbfb98d4005b" id="r_ga4e231d968796331a9ea0dbfb98d4005b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga4e231d968796331a9ea0dbfb98d4005b">GLFW_KEY_KP_DECIMAL</a>&#160;&#160;&#160;330</td></tr>
<tr class="separator:ga4e231d968796331a9ea0dbfb98d4005b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabca1733780a273d549129ad0f250d1e5" id="r_gabca1733780a273d549129ad0f250d1e5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gabca1733780a273d549129ad0f250d1e5">GLFW_KEY_KP_DIVIDE</a>&#160;&#160;&#160;331</td></tr>
<tr class="separator:gabca1733780a273d549129ad0f250d1e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9ada267eb0e78ed2ada8701dd24a56ef" id="r_ga9ada267eb0e78ed2ada8701dd24a56ef"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga9ada267eb0e78ed2ada8701dd24a56ef">GLFW_KEY_KP_MULTIPLY</a>&#160;&#160;&#160;332</td></tr>
<tr class="separator:ga9ada267eb0e78ed2ada8701dd24a56ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa3dbd60782ff93d6082a124bce1fa236" id="r_gaa3dbd60782ff93d6082a124bce1fa236"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaa3dbd60782ff93d6082a124bce1fa236">GLFW_KEY_KP_SUBTRACT</a>&#160;&#160;&#160;333</td></tr>
<tr class="separator:gaa3dbd60782ff93d6082a124bce1fa236"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad09c7c98acc79e89aa6a0a91275becac" id="r_gad09c7c98acc79e89aa6a0a91275becac"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gad09c7c98acc79e89aa6a0a91275becac">GLFW_KEY_KP_ADD</a>&#160;&#160;&#160;334</td></tr>
<tr class="separator:gad09c7c98acc79e89aa6a0a91275becac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4f728f8738f2986bd63eedd3d412e8cf" id="r_ga4f728f8738f2986bd63eedd3d412e8cf"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga4f728f8738f2986bd63eedd3d412e8cf">GLFW_KEY_KP_ENTER</a>&#160;&#160;&#160;335</td></tr>
<tr class="separator:ga4f728f8738f2986bd63eedd3d412e8cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaebdc76d4a808191e6d21b7e4ad2acd97" id="r_gaebdc76d4a808191e6d21b7e4ad2acd97"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaebdc76d4a808191e6d21b7e4ad2acd97">GLFW_KEY_KP_EQUAL</a>&#160;&#160;&#160;336</td></tr>
<tr class="separator:gaebdc76d4a808191e6d21b7e4ad2acd97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8a530a28a65c44ab5d00b759b756d3f6" id="r_ga8a530a28a65c44ab5d00b759b756d3f6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga8a530a28a65c44ab5d00b759b756d3f6">GLFW_KEY_LEFT_SHIFT</a>&#160;&#160;&#160;340</td></tr>
<tr class="separator:ga8a530a28a65c44ab5d00b759b756d3f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9f97b743e81460ac4b2deddecd10a464" id="r_ga9f97b743e81460ac4b2deddecd10a464"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga9f97b743e81460ac4b2deddecd10a464">GLFW_KEY_LEFT_CONTROL</a>&#160;&#160;&#160;341</td></tr>
<tr class="separator:ga9f97b743e81460ac4b2deddecd10a464"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f27dabf63a7789daa31e1c96790219b" id="r_ga7f27dabf63a7789daa31e1c96790219b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga7f27dabf63a7789daa31e1c96790219b">GLFW_KEY_LEFT_ALT</a>&#160;&#160;&#160;342</td></tr>
<tr class="separator:ga7f27dabf63a7789daa31e1c96790219b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafb1207c91997fc295afd1835fbc5641a" id="r_gafb1207c91997fc295afd1835fbc5641a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gafb1207c91997fc295afd1835fbc5641a">GLFW_KEY_LEFT_SUPER</a>&#160;&#160;&#160;343</td></tr>
<tr class="separator:gafb1207c91997fc295afd1835fbc5641a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaffca36b99c9dce1a19cb9befbadce691" id="r_gaffca36b99c9dce1a19cb9befbadce691"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaffca36b99c9dce1a19cb9befbadce691">GLFW_KEY_RIGHT_SHIFT</a>&#160;&#160;&#160;344</td></tr>
<tr class="separator:gaffca36b99c9dce1a19cb9befbadce691"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad1ca2094b2694e7251d0ab1fd34f8519" id="r_gad1ca2094b2694e7251d0ab1fd34f8519"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gad1ca2094b2694e7251d0ab1fd34f8519">GLFW_KEY_RIGHT_CONTROL</a>&#160;&#160;&#160;345</td></tr>
<tr class="separator:gad1ca2094b2694e7251d0ab1fd34f8519"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga687b38009131cfdd07a8d05fff8fa446" id="r_ga687b38009131cfdd07a8d05fff8fa446"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga687b38009131cfdd07a8d05fff8fa446">GLFW_KEY_RIGHT_ALT</a>&#160;&#160;&#160;346</td></tr>
<tr class="separator:ga687b38009131cfdd07a8d05fff8fa446"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad4547a3e8e247594acb60423fe6502db" id="r_gad4547a3e8e247594acb60423fe6502db"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gad4547a3e8e247594acb60423fe6502db">GLFW_KEY_RIGHT_SUPER</a>&#160;&#160;&#160;347</td></tr>
<tr class="separator:gad4547a3e8e247594acb60423fe6502db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9845be48a745fc232045c9ec174d8820" id="r_ga9845be48a745fc232045c9ec174d8820"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga9845be48a745fc232045c9ec174d8820">GLFW_KEY_MENU</a>&#160;&#160;&#160;348</td></tr>
<tr class="separator:ga9845be48a745fc232045c9ec174d8820"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga442cbaef7bfb9a4ba13594dd7fbf2789" id="r_ga442cbaef7bfb9a4ba13594dd7fbf2789"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga442cbaef7bfb9a4ba13594dd7fbf2789">GLFW_KEY_LAST</a>&#160;&#160;&#160;<a class="el" href="group__keys.html#ga9845be48a745fc232045c9ec174d8820">GLFW_KEY_MENU</a></td></tr>
<tr class="separator:ga442cbaef7bfb9a4ba13594dd7fbf2789"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="gaddb2c23772b97fd7e26e8ee66f1ad014" name="gaddb2c23772b97fd7e26e8ee66f1ad014"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaddb2c23772b97fd7e26e8ee66f1ad014">&#9670;&#160;</a></span>GLFW_KEY_SPACE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_SPACE&#160;&#160;&#160;32</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga6059b0b048ba6980b6107fffbd3b4b24" name="ga6059b0b048ba6980b6107fffbd3b4b24"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6059b0b048ba6980b6107fffbd3b4b24">&#9670;&#160;</a></span>GLFW_KEY_APOSTROPHE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_APOSTROPHE&#160;&#160;&#160;39  /* ' */</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gab3d5d72e59d3055f494627b0a524926c" name="gab3d5d72e59d3055f494627b0a524926c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab3d5d72e59d3055f494627b0a524926c">&#9670;&#160;</a></span>GLFW_KEY_COMMA</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_COMMA&#160;&#160;&#160;44  /* , */</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gac556b360f7f6fca4b70ba0aecf313fd4" name="gac556b360f7f6fca4b70ba0aecf313fd4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac556b360f7f6fca4b70ba0aecf313fd4">&#9670;&#160;</a></span>GLFW_KEY_MINUS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_MINUS&#160;&#160;&#160;45  /* - */</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga37e296b650eab419fc474ff69033d927" name="ga37e296b650eab419fc474ff69033d927"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga37e296b650eab419fc474ff69033d927">&#9670;&#160;</a></span>GLFW_KEY_PERIOD</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_PERIOD&#160;&#160;&#160;46  /* . */</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gadf3d753b2d479148d711de34b83fd0db" name="gadf3d753b2d479148d711de34b83fd0db"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadf3d753b2d479148d711de34b83fd0db">&#9670;&#160;</a></span>GLFW_KEY_SLASH</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_SLASH&#160;&#160;&#160;47  /* / */</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga50391730e9d7112ad4fd42d0bd1597c1" name="ga50391730e9d7112ad4fd42d0bd1597c1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga50391730e9d7112ad4fd42d0bd1597c1">&#9670;&#160;</a></span>GLFW_KEY_0</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_0&#160;&#160;&#160;48</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga05e4cae9ddb8d40cf6d82c8f11f2502f" name="ga05e4cae9ddb8d40cf6d82c8f11f2502f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga05e4cae9ddb8d40cf6d82c8f11f2502f">&#9670;&#160;</a></span>GLFW_KEY_1</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_1&#160;&#160;&#160;49</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gadc8e66b3a4c4b5c39ad1305cf852863c" name="gadc8e66b3a4c4b5c39ad1305cf852863c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadc8e66b3a4c4b5c39ad1305cf852863c">&#9670;&#160;</a></span>GLFW_KEY_2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_2&#160;&#160;&#160;50</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga812f0273fe1a981e1fa002ae73e92271" name="ga812f0273fe1a981e1fa002ae73e92271"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga812f0273fe1a981e1fa002ae73e92271">&#9670;&#160;</a></span>GLFW_KEY_3</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_3&#160;&#160;&#160;51</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga9e14b6975a9cc8f66cdd5cb3d3861356" name="ga9e14b6975a9cc8f66cdd5cb3d3861356"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9e14b6975a9cc8f66cdd5cb3d3861356">&#9670;&#160;</a></span>GLFW_KEY_4</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_4&#160;&#160;&#160;52</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga4d74ddaa5d4c609993b4d4a15736c924" name="ga4d74ddaa5d4c609993b4d4a15736c924"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4d74ddaa5d4c609993b4d4a15736c924">&#9670;&#160;</a></span>GLFW_KEY_5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_5&#160;&#160;&#160;53</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga9ea4ab80c313a227b14d0a7c6f810b5d" name="ga9ea4ab80c313a227b14d0a7c6f810b5d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9ea4ab80c313a227b14d0a7c6f810b5d">&#9670;&#160;</a></span>GLFW_KEY_6</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_6&#160;&#160;&#160;54</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gab79b1cfae7bd630cfc4604c1f263c666" name="gab79b1cfae7bd630cfc4604c1f263c666"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab79b1cfae7bd630cfc4604c1f263c666">&#9670;&#160;</a></span>GLFW_KEY_7</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_7&#160;&#160;&#160;55</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gadeaa109a0f9f5afc94fe4a108e686f6f" name="gadeaa109a0f9f5afc94fe4a108e686f6f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadeaa109a0f9f5afc94fe4a108e686f6f">&#9670;&#160;</a></span>GLFW_KEY_8</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_8&#160;&#160;&#160;56</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga2924cb5349ebbf97c8987f3521c44f39" name="ga2924cb5349ebbf97c8987f3521c44f39"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2924cb5349ebbf97c8987f3521c44f39">&#9670;&#160;</a></span>GLFW_KEY_9</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_9&#160;&#160;&#160;57</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga84233de9ee5bb3e8788a5aa07d80af7d" name="ga84233de9ee5bb3e8788a5aa07d80af7d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga84233de9ee5bb3e8788a5aa07d80af7d">&#9670;&#160;</a></span>GLFW_KEY_SEMICOLON</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_SEMICOLON&#160;&#160;&#160;59  /* ; */</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gae1a2de47240d6664423c204bdd91bd17" name="gae1a2de47240d6664423c204bdd91bd17"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae1a2de47240d6664423c204bdd91bd17">&#9670;&#160;</a></span>GLFW_KEY_EQUAL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_EQUAL&#160;&#160;&#160;61  /* = */</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga03e842608e1ea323370889d33b8f70ff" name="ga03e842608e1ea323370889d33b8f70ff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga03e842608e1ea323370889d33b8f70ff">&#9670;&#160;</a></span>GLFW_KEY_A</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_A&#160;&#160;&#160;65</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga8e3fb647ff3aca9e8dbf14fe66332941" name="ga8e3fb647ff3aca9e8dbf14fe66332941"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8e3fb647ff3aca9e8dbf14fe66332941">&#9670;&#160;</a></span>GLFW_KEY_B</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_B&#160;&#160;&#160;66</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga00ccf3475d9ee2e679480d540d554669" name="ga00ccf3475d9ee2e679480d540d554669"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga00ccf3475d9ee2e679480d540d554669">&#9670;&#160;</a></span>GLFW_KEY_C</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_C&#160;&#160;&#160;67</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga011f7cdc9a654da984a2506479606933" name="ga011f7cdc9a654da984a2506479606933"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga011f7cdc9a654da984a2506479606933">&#9670;&#160;</a></span>GLFW_KEY_D</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_D&#160;&#160;&#160;68</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gabf48fcc3afbe69349df432b470c96ef2" name="gabf48fcc3afbe69349df432b470c96ef2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gabf48fcc3afbe69349df432b470c96ef2">&#9670;&#160;</a></span>GLFW_KEY_E</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_E&#160;&#160;&#160;69</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga5df402e02aca08444240058fd9b42a55" name="ga5df402e02aca08444240058fd9b42a55"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5df402e02aca08444240058fd9b42a55">&#9670;&#160;</a></span>GLFW_KEY_F</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F&#160;&#160;&#160;70</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gae74ecddf7cc96104ab23989b1cdab536" name="gae74ecddf7cc96104ab23989b1cdab536"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae74ecddf7cc96104ab23989b1cdab536">&#9670;&#160;</a></span>GLFW_KEY_G</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_G&#160;&#160;&#160;71</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gad4cc98fc8f35f015d9e2fb94bf136076" name="gad4cc98fc8f35f015d9e2fb94bf136076"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad4cc98fc8f35f015d9e2fb94bf136076">&#9670;&#160;</a></span>GLFW_KEY_H</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_H&#160;&#160;&#160;72</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga274655c8bfe39742684ca393cf8ed093" name="ga274655c8bfe39742684ca393cf8ed093"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga274655c8bfe39742684ca393cf8ed093">&#9670;&#160;</a></span>GLFW_KEY_I</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_I&#160;&#160;&#160;73</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga65ff2aedb129a3149ad9cb3e4159a75f" name="ga65ff2aedb129a3149ad9cb3e4159a75f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga65ff2aedb129a3149ad9cb3e4159a75f">&#9670;&#160;</a></span>GLFW_KEY_J</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_J&#160;&#160;&#160;74</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga4ae8debadf6d2a691badae0b53ea3ba0" name="ga4ae8debadf6d2a691badae0b53ea3ba0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4ae8debadf6d2a691badae0b53ea3ba0">&#9670;&#160;</a></span>GLFW_KEY_K</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_K&#160;&#160;&#160;75</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaaa8b54a13f6b1eed85ac86f82d550db2" name="gaaa8b54a13f6b1eed85ac86f82d550db2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaaa8b54a13f6b1eed85ac86f82d550db2">&#9670;&#160;</a></span>GLFW_KEY_L</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_L&#160;&#160;&#160;76</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga4d7f0260c82e4ea3d6ebc7a21d6e3716" name="ga4d7f0260c82e4ea3d6ebc7a21d6e3716"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4d7f0260c82e4ea3d6ebc7a21d6e3716">&#9670;&#160;</a></span>GLFW_KEY_M</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_M&#160;&#160;&#160;77</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gae00856dfeb5d13aafebf59d44de5cdda" name="gae00856dfeb5d13aafebf59d44de5cdda"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae00856dfeb5d13aafebf59d44de5cdda">&#9670;&#160;</a></span>GLFW_KEY_N</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_N&#160;&#160;&#160;78</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaecbbb79130df419d58dd7f09a169efe9" name="gaecbbb79130df419d58dd7f09a169efe9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaecbbb79130df419d58dd7f09a169efe9">&#9670;&#160;</a></span>GLFW_KEY_O</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_O&#160;&#160;&#160;79</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga8fc15819c1094fb2afa01d84546b33e1" name="ga8fc15819c1094fb2afa01d84546b33e1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8fc15819c1094fb2afa01d84546b33e1">&#9670;&#160;</a></span>GLFW_KEY_P</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_P&#160;&#160;&#160;80</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gafdd01e38b120d67cf51e348bb47f3964" name="gafdd01e38b120d67cf51e348bb47f3964"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafdd01e38b120d67cf51e348bb47f3964">&#9670;&#160;</a></span>GLFW_KEY_Q</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_Q&#160;&#160;&#160;81</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga4ce6c70a0c98c50b3fe4ab9a728d4d36" name="ga4ce6c70a0c98c50b3fe4ab9a728d4d36"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4ce6c70a0c98c50b3fe4ab9a728d4d36">&#9670;&#160;</a></span>GLFW_KEY_R</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_R&#160;&#160;&#160;82</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga1570e2ccaab036ea82bed66fc1dab2a9" name="ga1570e2ccaab036ea82bed66fc1dab2a9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1570e2ccaab036ea82bed66fc1dab2a9">&#9670;&#160;</a></span>GLFW_KEY_S</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_S&#160;&#160;&#160;83</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga90e0560422ec7a30e7f3f375bc9f37f9" name="ga90e0560422ec7a30e7f3f375bc9f37f9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga90e0560422ec7a30e7f3f375bc9f37f9">&#9670;&#160;</a></span>GLFW_KEY_T</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_T&#160;&#160;&#160;84</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gacad52f3bf7d378fc0ffa72a76769256d" name="gacad52f3bf7d378fc0ffa72a76769256d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gacad52f3bf7d378fc0ffa72a76769256d">&#9670;&#160;</a></span>GLFW_KEY_U</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_U&#160;&#160;&#160;85</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga22c7763899ecf7788862e5f90eacce6b" name="ga22c7763899ecf7788862e5f90eacce6b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga22c7763899ecf7788862e5f90eacce6b">&#9670;&#160;</a></span>GLFW_KEY_V</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_V&#160;&#160;&#160;86</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaa06a712e6202661fc03da5bdb7b6e545" name="gaa06a712e6202661fc03da5bdb7b6e545"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa06a712e6202661fc03da5bdb7b6e545">&#9670;&#160;</a></span>GLFW_KEY_W</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_W&#160;&#160;&#160;87</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gac1c42c0bf4192cea713c55598b06b744" name="gac1c42c0bf4192cea713c55598b06b744"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac1c42c0bf4192cea713c55598b06b744">&#9670;&#160;</a></span>GLFW_KEY_X</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_X&#160;&#160;&#160;88</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gafd9f115a549effdf8e372a787c360313" name="gafd9f115a549effdf8e372a787c360313"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafd9f115a549effdf8e372a787c360313">&#9670;&#160;</a></span>GLFW_KEY_Y</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_Y&#160;&#160;&#160;89</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gac489e208c26afda8d4938ed88718760a" name="gac489e208c26afda8d4938ed88718760a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac489e208c26afda8d4938ed88718760a">&#9670;&#160;</a></span>GLFW_KEY_Z</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_Z&#160;&#160;&#160;90</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gad1c8d9adac53925276ecb1d592511d8a" name="gad1c8d9adac53925276ecb1d592511d8a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad1c8d9adac53925276ecb1d592511d8a">&#9670;&#160;</a></span>GLFW_KEY_LEFT_BRACKET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_LEFT_BRACKET&#160;&#160;&#160;91  /* [ */</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gab8155ea99d1ab27ff56f24f8dc73f8d1" name="gab8155ea99d1ab27ff56f24f8dc73f8d1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab8155ea99d1ab27ff56f24f8dc73f8d1">&#9670;&#160;</a></span>GLFW_KEY_BACKSLASH</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_BACKSLASH&#160;&#160;&#160;92  /* \ */</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga86ef225fd6a66404caae71044cdd58d8" name="ga86ef225fd6a66404caae71044cdd58d8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga86ef225fd6a66404caae71044cdd58d8">&#9670;&#160;</a></span>GLFW_KEY_RIGHT_BRACKET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_RIGHT_BRACKET&#160;&#160;&#160;93  /* ] */</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga7a3701fb4e2a0b136ff4b568c3c8d668" name="ga7a3701fb4e2a0b136ff4b568c3c8d668"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7a3701fb4e2a0b136ff4b568c3c8d668">&#9670;&#160;</a></span>GLFW_KEY_GRAVE_ACCENT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_GRAVE_ACCENT&#160;&#160;&#160;96  /* ` */</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gadc78dad3dab76bcd4b5c20114052577a" name="gadc78dad3dab76bcd4b5c20114052577a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadc78dad3dab76bcd4b5c20114052577a">&#9670;&#160;</a></span>GLFW_KEY_WORLD_1</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_WORLD_1&#160;&#160;&#160;161 /* non-US #1 */</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga20494bfebf0bb4fc9503afca18ab2c5e" name="ga20494bfebf0bb4fc9503afca18ab2c5e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga20494bfebf0bb4fc9503afca18ab2c5e">&#9670;&#160;</a></span>GLFW_KEY_WORLD_2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_WORLD_2&#160;&#160;&#160;162 /* non-US #2 */</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaac6596c350b635c245113b81c2123b93" name="gaac6596c350b635c245113b81c2123b93"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaac6596c350b635c245113b81c2123b93">&#9670;&#160;</a></span>GLFW_KEY_ESCAPE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_ESCAPE&#160;&#160;&#160;256</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga9555a92ecbecdbc1f3435219c571d667" name="ga9555a92ecbecdbc1f3435219c571d667"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9555a92ecbecdbc1f3435219c571d667">&#9670;&#160;</a></span>GLFW_KEY_ENTER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_ENTER&#160;&#160;&#160;257</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga6908a4bda9950a3e2b73f794bbe985df" name="ga6908a4bda9950a3e2b73f794bbe985df"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6908a4bda9950a3e2b73f794bbe985df">&#9670;&#160;</a></span>GLFW_KEY_TAB</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_TAB&#160;&#160;&#160;258</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga6c0df1fe2f156bbd5a98c66d76ff3635" name="ga6c0df1fe2f156bbd5a98c66d76ff3635"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6c0df1fe2f156bbd5a98c66d76ff3635">&#9670;&#160;</a></span>GLFW_KEY_BACKSPACE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_BACKSPACE&#160;&#160;&#160;259</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga373ac7365435d6b0eb1068f470e34f47" name="ga373ac7365435d6b0eb1068f470e34f47"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga373ac7365435d6b0eb1068f470e34f47">&#9670;&#160;</a></span>GLFW_KEY_INSERT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_INSERT&#160;&#160;&#160;260</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gadb111e4df74b8a715f2c05dad58d2682" name="gadb111e4df74b8a715f2c05dad58d2682"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadb111e4df74b8a715f2c05dad58d2682">&#9670;&#160;</a></span>GLFW_KEY_DELETE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_DELETE&#160;&#160;&#160;261</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga06ba07662e8c291a4a84535379ffc7ac" name="ga06ba07662e8c291a4a84535379ffc7ac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga06ba07662e8c291a4a84535379ffc7ac">&#9670;&#160;</a></span>GLFW_KEY_RIGHT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_RIGHT&#160;&#160;&#160;262</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gae12a010d33c309a67ab9460c51eb2462" name="gae12a010d33c309a67ab9460c51eb2462"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae12a010d33c309a67ab9460c51eb2462">&#9670;&#160;</a></span>GLFW_KEY_LEFT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_LEFT&#160;&#160;&#160;263</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gae2e3958c71595607416aa7bf082be2f9" name="gae2e3958c71595607416aa7bf082be2f9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae2e3958c71595607416aa7bf082be2f9">&#9670;&#160;</a></span>GLFW_KEY_DOWN</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_DOWN&#160;&#160;&#160;264</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga2f3342b194020d3544c67e3506b6f144" name="ga2f3342b194020d3544c67e3506b6f144"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2f3342b194020d3544c67e3506b6f144">&#9670;&#160;</a></span>GLFW_KEY_UP</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_UP&#160;&#160;&#160;265</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga3ab731f9622f0db280178a5f3cc6d586" name="ga3ab731f9622f0db280178a5f3cc6d586"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3ab731f9622f0db280178a5f3cc6d586">&#9670;&#160;</a></span>GLFW_KEY_PAGE_UP</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_PAGE_UP&#160;&#160;&#160;266</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaee0a8fa442001cc2147812f84b59041c" name="gaee0a8fa442001cc2147812f84b59041c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaee0a8fa442001cc2147812f84b59041c">&#9670;&#160;</a></span>GLFW_KEY_PAGE_DOWN</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_PAGE_DOWN&#160;&#160;&#160;267</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga41452c7287195d481e43207318c126a7" name="ga41452c7287195d481e43207318c126a7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga41452c7287195d481e43207318c126a7">&#9670;&#160;</a></span>GLFW_KEY_HOME</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_HOME&#160;&#160;&#160;268</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga86587ea1df19a65978d3e3b8439bedd9" name="ga86587ea1df19a65978d3e3b8439bedd9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga86587ea1df19a65978d3e3b8439bedd9">&#9670;&#160;</a></span>GLFW_KEY_END</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_END&#160;&#160;&#160;269</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga92c1d2c9d63485f3d70f94f688d48672" name="ga92c1d2c9d63485f3d70f94f688d48672"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga92c1d2c9d63485f3d70f94f688d48672">&#9670;&#160;</a></span>GLFW_KEY_CAPS_LOCK</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_CAPS_LOCK&#160;&#160;&#160;280</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaf622b63b9537f7084c2ab649b8365630" name="gaf622b63b9537f7084c2ab649b8365630"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf622b63b9537f7084c2ab649b8365630">&#9670;&#160;</a></span>GLFW_KEY_SCROLL_LOCK</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_SCROLL_LOCK&#160;&#160;&#160;281</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga3946edc362aeff213b2be6304296cf43" name="ga3946edc362aeff213b2be6304296cf43"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3946edc362aeff213b2be6304296cf43">&#9670;&#160;</a></span>GLFW_KEY_NUM_LOCK</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_NUM_LOCK&#160;&#160;&#160;282</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaf964c2e65e97d0cf785a5636ee8df642" name="gaf964c2e65e97d0cf785a5636ee8df642"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf964c2e65e97d0cf785a5636ee8df642">&#9670;&#160;</a></span>GLFW_KEY_PRINT_SCREEN</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_PRINT_SCREEN&#160;&#160;&#160;283</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga8116b9692d87382afb5849b6d8907f18" name="ga8116b9692d87382afb5849b6d8907f18"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8116b9692d87382afb5849b6d8907f18">&#9670;&#160;</a></span>GLFW_KEY_PAUSE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_PAUSE&#160;&#160;&#160;284</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gafb8d66c573acf22e364049477dcbea30" name="gafb8d66c573acf22e364049477dcbea30"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafb8d66c573acf22e364049477dcbea30">&#9670;&#160;</a></span>GLFW_KEY_F1</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F1&#160;&#160;&#160;290</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga0900750aff94889b940f5e428c07daee" name="ga0900750aff94889b940f5e428c07daee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga0900750aff94889b940f5e428c07daee">&#9670;&#160;</a></span>GLFW_KEY_F2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F2&#160;&#160;&#160;291</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaed7cd729c0147a551bb8b7bb36c17015" name="gaed7cd729c0147a551bb8b7bb36c17015"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaed7cd729c0147a551bb8b7bb36c17015">&#9670;&#160;</a></span>GLFW_KEY_F3</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F3&#160;&#160;&#160;292</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga9b61ebd0c63b44b7332fda2c9763eaa6" name="ga9b61ebd0c63b44b7332fda2c9763eaa6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9b61ebd0c63b44b7332fda2c9763eaa6">&#9670;&#160;</a></span>GLFW_KEY_F4</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F4&#160;&#160;&#160;293</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaf258dda9947daa428377938ed577c8c2" name="gaf258dda9947daa428377938ed577c8c2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf258dda9947daa428377938ed577c8c2">&#9670;&#160;</a></span>GLFW_KEY_F5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F5&#160;&#160;&#160;294</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga6dc2d3f87b9d51ffbbbe2ef0299d8e1d" name="ga6dc2d3f87b9d51ffbbbe2ef0299d8e1d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6dc2d3f87b9d51ffbbbe2ef0299d8e1d">&#9670;&#160;</a></span>GLFW_KEY_F6</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F6&#160;&#160;&#160;295</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gacca6ef8a2162c52a0ac1d881e8d9c38a" name="gacca6ef8a2162c52a0ac1d881e8d9c38a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gacca6ef8a2162c52a0ac1d881e8d9c38a">&#9670;&#160;</a></span>GLFW_KEY_F7</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F7&#160;&#160;&#160;296</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gac9d39390336ae14e4a93e295de43c7e8" name="gac9d39390336ae14e4a93e295de43c7e8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac9d39390336ae14e4a93e295de43c7e8">&#9670;&#160;</a></span>GLFW_KEY_F8</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F8&#160;&#160;&#160;297</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gae40de0de1c9f21cd26c9afa3d7050851" name="gae40de0de1c9f21cd26c9afa3d7050851"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae40de0de1c9f21cd26c9afa3d7050851">&#9670;&#160;</a></span>GLFW_KEY_F9</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F9&#160;&#160;&#160;298</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga718d11d2f7d57471a2f6a894235995b1" name="ga718d11d2f7d57471a2f6a894235995b1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga718d11d2f7d57471a2f6a894235995b1">&#9670;&#160;</a></span>GLFW_KEY_F10</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F10&#160;&#160;&#160;299</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga0bc04b11627e7d69339151e7306b2832" name="ga0bc04b11627e7d69339151e7306b2832"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga0bc04b11627e7d69339151e7306b2832">&#9670;&#160;</a></span>GLFW_KEY_F11</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F11&#160;&#160;&#160;300</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaf5908fa9b0a906ae03fc2c61ac7aa3e2" name="gaf5908fa9b0a906ae03fc2c61ac7aa3e2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf5908fa9b0a906ae03fc2c61ac7aa3e2">&#9670;&#160;</a></span>GLFW_KEY_F12</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F12&#160;&#160;&#160;301</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gad637f4308655e1001bd6ad942bc0fd4b" name="gad637f4308655e1001bd6ad942bc0fd4b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad637f4308655e1001bd6ad942bc0fd4b">&#9670;&#160;</a></span>GLFW_KEY_F13</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F13&#160;&#160;&#160;302</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaf14c66cff3396e5bd46e803c035e6c1f" name="gaf14c66cff3396e5bd46e803c035e6c1f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf14c66cff3396e5bd46e803c035e6c1f">&#9670;&#160;</a></span>GLFW_KEY_F14</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F14&#160;&#160;&#160;303</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga7f70970db6e8be1794da8516a6d14058" name="ga7f70970db6e8be1794da8516a6d14058"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7f70970db6e8be1794da8516a6d14058">&#9670;&#160;</a></span>GLFW_KEY_F15</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F15&#160;&#160;&#160;304</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaa582dbb1d2ba2050aa1dca0838095b27" name="gaa582dbb1d2ba2050aa1dca0838095b27"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa582dbb1d2ba2050aa1dca0838095b27">&#9670;&#160;</a></span>GLFW_KEY_F16</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F16&#160;&#160;&#160;305</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga972ce5c365e2394b36104b0e3125c748" name="ga972ce5c365e2394b36104b0e3125c748"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga972ce5c365e2394b36104b0e3125c748">&#9670;&#160;</a></span>GLFW_KEY_F17</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F17&#160;&#160;&#160;306</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaebf6391058d5566601e357edc5ea737c" name="gaebf6391058d5566601e357edc5ea737c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaebf6391058d5566601e357edc5ea737c">&#9670;&#160;</a></span>GLFW_KEY_F18</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F18&#160;&#160;&#160;307</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaec011d9ba044058cb54529da710e9791" name="gaec011d9ba044058cb54529da710e9791"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaec011d9ba044058cb54529da710e9791">&#9670;&#160;</a></span>GLFW_KEY_F19</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F19&#160;&#160;&#160;308</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga82b9c721ada04cd5ca8de767da38022f" name="ga82b9c721ada04cd5ca8de767da38022f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga82b9c721ada04cd5ca8de767da38022f">&#9670;&#160;</a></span>GLFW_KEY_F20</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F20&#160;&#160;&#160;309</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga356afb14d3440ff2bb378f74f7ebc60f" name="ga356afb14d3440ff2bb378f74f7ebc60f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga356afb14d3440ff2bb378f74f7ebc60f">&#9670;&#160;</a></span>GLFW_KEY_F21</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F21&#160;&#160;&#160;310</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga90960bd2a155f2b09675324d3dff1565" name="ga90960bd2a155f2b09675324d3dff1565"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga90960bd2a155f2b09675324d3dff1565">&#9670;&#160;</a></span>GLFW_KEY_F22</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F22&#160;&#160;&#160;311</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga43c21099aac10952d1be909a8ddee4d5" name="ga43c21099aac10952d1be909a8ddee4d5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga43c21099aac10952d1be909a8ddee4d5">&#9670;&#160;</a></span>GLFW_KEY_F23</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F23&#160;&#160;&#160;312</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga8150374677b5bed3043408732152dea2" name="ga8150374677b5bed3043408732152dea2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8150374677b5bed3043408732152dea2">&#9670;&#160;</a></span>GLFW_KEY_F24</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F24&#160;&#160;&#160;313</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaa4bbd93ed73bb4c6ae7d83df880b7199" name="gaa4bbd93ed73bb4c6ae7d83df880b7199"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa4bbd93ed73bb4c6ae7d83df880b7199">&#9670;&#160;</a></span>GLFW_KEY_F25</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_F25&#160;&#160;&#160;314</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga10515dafc55b71e7683f5b4fedd1c70d" name="ga10515dafc55b71e7683f5b4fedd1c70d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga10515dafc55b71e7683f5b4fedd1c70d">&#9670;&#160;</a></span>GLFW_KEY_KP_0</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_0&#160;&#160;&#160;320</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaf3a29a334402c5eaf0b3439edf5587c3" name="gaf3a29a334402c5eaf0b3439edf5587c3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf3a29a334402c5eaf0b3439edf5587c3">&#9670;&#160;</a></span>GLFW_KEY_KP_1</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_1&#160;&#160;&#160;321</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaf82d5a802ab8213c72653d7480c16f13" name="gaf82d5a802ab8213c72653d7480c16f13"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf82d5a802ab8213c72653d7480c16f13">&#9670;&#160;</a></span>GLFW_KEY_KP_2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_2&#160;&#160;&#160;322</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga7e25ff30d56cd512828c1d4ae8d54ef2" name="ga7e25ff30d56cd512828c1d4ae8d54ef2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7e25ff30d56cd512828c1d4ae8d54ef2">&#9670;&#160;</a></span>GLFW_KEY_KP_3</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_3&#160;&#160;&#160;323</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gada7ec86778b85e0b4de0beea72234aea" name="gada7ec86778b85e0b4de0beea72234aea"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gada7ec86778b85e0b4de0beea72234aea">&#9670;&#160;</a></span>GLFW_KEY_KP_4</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_4&#160;&#160;&#160;324</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga9a5be274434866c51738cafbb6d26b45" name="ga9a5be274434866c51738cafbb6d26b45"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9a5be274434866c51738cafbb6d26b45">&#9670;&#160;</a></span>GLFW_KEY_KP_5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_5&#160;&#160;&#160;325</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gafc141b0f8450519084c01092a3157faa" name="gafc141b0f8450519084c01092a3157faa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafc141b0f8450519084c01092a3157faa">&#9670;&#160;</a></span>GLFW_KEY_KP_6</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_6&#160;&#160;&#160;326</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga8882f411f05d04ec77a9563974bbfa53" name="ga8882f411f05d04ec77a9563974bbfa53"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8882f411f05d04ec77a9563974bbfa53">&#9670;&#160;</a></span>GLFW_KEY_KP_7</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_7&#160;&#160;&#160;327</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gab2ea2e6a12f89d315045af520ac78cec" name="gab2ea2e6a12f89d315045af520ac78cec"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab2ea2e6a12f89d315045af520ac78cec">&#9670;&#160;</a></span>GLFW_KEY_KP_8</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_8&#160;&#160;&#160;328</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gafb21426b630ed4fcc084868699ba74c1" name="gafb21426b630ed4fcc084868699ba74c1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafb21426b630ed4fcc084868699ba74c1">&#9670;&#160;</a></span>GLFW_KEY_KP_9</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_9&#160;&#160;&#160;329</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga4e231d968796331a9ea0dbfb98d4005b" name="ga4e231d968796331a9ea0dbfb98d4005b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4e231d968796331a9ea0dbfb98d4005b">&#9670;&#160;</a></span>GLFW_KEY_KP_DECIMAL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_DECIMAL&#160;&#160;&#160;330</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gabca1733780a273d549129ad0f250d1e5" name="gabca1733780a273d549129ad0f250d1e5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gabca1733780a273d549129ad0f250d1e5">&#9670;&#160;</a></span>GLFW_KEY_KP_DIVIDE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_DIVIDE&#160;&#160;&#160;331</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga9ada267eb0e78ed2ada8701dd24a56ef" name="ga9ada267eb0e78ed2ada8701dd24a56ef"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9ada267eb0e78ed2ada8701dd24a56ef">&#9670;&#160;</a></span>GLFW_KEY_KP_MULTIPLY</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_MULTIPLY&#160;&#160;&#160;332</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaa3dbd60782ff93d6082a124bce1fa236" name="gaa3dbd60782ff93d6082a124bce1fa236"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa3dbd60782ff93d6082a124bce1fa236">&#9670;&#160;</a></span>GLFW_KEY_KP_SUBTRACT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_SUBTRACT&#160;&#160;&#160;333</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gad09c7c98acc79e89aa6a0a91275becac" name="gad09c7c98acc79e89aa6a0a91275becac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad09c7c98acc79e89aa6a0a91275becac">&#9670;&#160;</a></span>GLFW_KEY_KP_ADD</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_ADD&#160;&#160;&#160;334</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga4f728f8738f2986bd63eedd3d412e8cf" name="ga4f728f8738f2986bd63eedd3d412e8cf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4f728f8738f2986bd63eedd3d412e8cf">&#9670;&#160;</a></span>GLFW_KEY_KP_ENTER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_ENTER&#160;&#160;&#160;335</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaebdc76d4a808191e6d21b7e4ad2acd97" name="gaebdc76d4a808191e6d21b7e4ad2acd97"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaebdc76d4a808191e6d21b7e4ad2acd97">&#9670;&#160;</a></span>GLFW_KEY_KP_EQUAL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_KP_EQUAL&#160;&#160;&#160;336</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga8a530a28a65c44ab5d00b759b756d3f6" name="ga8a530a28a65c44ab5d00b759b756d3f6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8a530a28a65c44ab5d00b759b756d3f6">&#9670;&#160;</a></span>GLFW_KEY_LEFT_SHIFT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_LEFT_SHIFT&#160;&#160;&#160;340</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga9f97b743e81460ac4b2deddecd10a464" name="ga9f97b743e81460ac4b2deddecd10a464"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9f97b743e81460ac4b2deddecd10a464">&#9670;&#160;</a></span>GLFW_KEY_LEFT_CONTROL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_LEFT_CONTROL&#160;&#160;&#160;341</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga7f27dabf63a7789daa31e1c96790219b" name="ga7f27dabf63a7789daa31e1c96790219b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7f27dabf63a7789daa31e1c96790219b">&#9670;&#160;</a></span>GLFW_KEY_LEFT_ALT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_LEFT_ALT&#160;&#160;&#160;342</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gafb1207c91997fc295afd1835fbc5641a" name="gafb1207c91997fc295afd1835fbc5641a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafb1207c91997fc295afd1835fbc5641a">&#9670;&#160;</a></span>GLFW_KEY_LEFT_SUPER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_LEFT_SUPER&#160;&#160;&#160;343</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaffca36b99c9dce1a19cb9befbadce691" name="gaffca36b99c9dce1a19cb9befbadce691"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaffca36b99c9dce1a19cb9befbadce691">&#9670;&#160;</a></span>GLFW_KEY_RIGHT_SHIFT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_RIGHT_SHIFT&#160;&#160;&#160;344</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gad1ca2094b2694e7251d0ab1fd34f8519" name="gad1ca2094b2694e7251d0ab1fd34f8519"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad1ca2094b2694e7251d0ab1fd34f8519">&#9670;&#160;</a></span>GLFW_KEY_RIGHT_CONTROL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_RIGHT_CONTROL&#160;&#160;&#160;345</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga687b38009131cfdd07a8d05fff8fa446" name="ga687b38009131cfdd07a8d05fff8fa446"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga687b38009131cfdd07a8d05fff8fa446">&#9670;&#160;</a></span>GLFW_KEY_RIGHT_ALT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_RIGHT_ALT&#160;&#160;&#160;346</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gad4547a3e8e247594acb60423fe6502db" name="gad4547a3e8e247594acb60423fe6502db"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad4547a3e8e247594acb60423fe6502db">&#9670;&#160;</a></span>GLFW_KEY_RIGHT_SUPER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_RIGHT_SUPER&#160;&#160;&#160;347</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga9845be48a745fc232045c9ec174d8820" name="ga9845be48a745fc232045c9ec174d8820"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9845be48a745fc232045c9ec174d8820">&#9670;&#160;</a></span>GLFW_KEY_MENU</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_MENU&#160;&#160;&#160;348</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga442cbaef7bfb9a4ba13594dd7fbf2789" name="ga442cbaef7bfb9a4ba13594dd7fbf2789"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga442cbaef7bfb9a4ba13594dd7fbf2789">&#9670;&#160;</a></span>GLFW_KEY_LAST</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_LAST&#160;&#160;&#160;<a class="el" href="group__keys.html#ga9845be48a745fc232045c9ec174d8820">GLFW_KEY_MENU</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
