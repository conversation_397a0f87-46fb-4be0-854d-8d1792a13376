<?xml version="1.0"?>
<!--
    Tree-based 20x20 left eye detector.
    The detector is trained by 6665 positive samples from FERET, VALID and BioID face databases.
    Created by <PERSON><PERSON> (http://yushiqi.cn/research/eyedetection).

////////////////////////////////////////////////////////////////////////////////////////

  IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.

  By downloading, copying, installing or using the software you agree to this license.
  If you do not agree to this license, do not download, install,
  copy or use the software.


                        Intel License Agreement
                For Open Source Computer Vision Library

 Copyright (C) 2000, Intel Corporation, all rights reserved.
 Third party copyrights are property of their respective owners.

 Redistribution and use in source and binary forms, with or without modification,
 are permitted provided that the following conditions are met:

   * Redistribution's of source code must retain the above copyright notice,
     this list of conditions and the following disclaimer.

   * Redistribution's in binary form must reproduce the above copyright notice,
     this list of conditions and the following disclaimer in the documentation
     and/or other materials provided with the distribution.

   * The name of Intel Corporation may not be used to endorse or promote products
     derived from this software without specific prior written permission.

 This software is provided by the copyright holders and contributors "as is" and
 any express or implied warranties, including, but not limited to, the implied
 warranties of merchantability and fitness for a particular purpose are disclaimed.
 In no event shall the Intel Corporation or contributors be liable for any direct,
 indirect, incidental, special, exemplary, or consequential damages
 (including, but not limited to, procurement of substitute goods or services;
 loss of use, data, or profits; or business interruption) however caused
 and on any theory of liability, whether in contract, strict liability,
 or tort (including negligence or otherwise) arising in any way out of
 the use of this software, even if advised of the possibility of such damage.
-->
<opencv_storage>
<haarcascade_lefteye type_id="opencv-haar-classifier">
  <size>
    20 20</size>
  <stages>
    <_>
      <!-- stage 0 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 12 3 8 -1.</_>
                <_>
                  8 16 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0273259896785021</threshold>
            <left_val>-0.9060062170028687</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 11 8 9 -1.</_>
                <_>
                  7 11 4 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0568458177149296e-03</threshold>
            <left_val>0.9338570833206177</left_val>
            <right_val>-0.4585995972156525</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 7 11 12 -1.</_>
                <_>
                  8 11 11 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1253869980573654</threshold>
            <left_val>0.7246372103691101</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 0 7 8 -1.</_>
                <_>
                  1 4 7 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1148729994893074</threshold>
            <left_val>0.5303416848182678</left_val>
            <right_val>-0.8322122097015381</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 7 6 6 -1.</_>
                <_>
                  7 9 6 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0583099387586117</threshold>
            <left_val>0.6540889143943787</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 7 4 -1.</_>
                <_>
                  0 2 7 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0176843702793121</threshold>
            <left_val>0.2948287129402161</left_val>
            <right_val>-0.7480958104133606</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 13 4 4 -1.</_>
                <_>
                  18 13 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.5937170032411814e-03</threshold>
            <left_val>-0.5030391812324524</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 15 2 3 -1.</_>
                <_>
                  17 15 1 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.3436110457405448e-03</threshold>
            <left_val>0.6599534153938293</left_val>
            <right_val>-0.5574085712432861</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 13 6 2 -1.</_>
                <_>
                  2 13 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.1795940119773149e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4201635122299194</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 0 6 6 -1.</_>
                <_>
                  7 0 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0115148704499006</threshold>
            <left_val>0.5969433188438416</left_val>
            <right_val>-0.8050804734230042</right_val></_></_></trees>
      <stage_threshold>-2.3924100399017334</stage_threshold>
      <parent>-1</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 1 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 7 9 12 -1.</_>
                <_>
                  8 11 3 4 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.2248556017875671</threshold>
            <left_node>1</left_node>
            <right_val>-0.8136320114135742</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 6 4 10 -1.</_>
                <_>
                  5 6 2 5 2.</_>
                <_>
                  7 11 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.6008004620671272e-03</threshold>
            <left_val>0.9086313843727112</left_val>
            <right_val>-0.3220897018909454</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 12 11 8 -1.</_>
                <_>
                  8 16 11 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0742191672325134</threshold>
            <left_val>-0.7532945275306702</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 1 8 -1.</_>
                <_>
                  0 4 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.3165741264820099e-03</threshold>
            <left_val>0.8633949756622314</left_val>
            <right_val>-0.0334635712206364</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 6 6 -1.</_>
                <_>
                  3 0 3 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.1913449745625257e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5572034716606140</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 14 6 6 -1.</_>
                <_>
                  14 17 6 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0118009597063065</threshold>
            <left_val>-0.3235968053340912</left_val>
            <right_val>0.6416382193565369</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 13 9 7 -1.</_>
                <_>
                  8 13 3 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.6179709285497665e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5316786766052246</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 17 6 3 -1.</_>
                <_>
                  8 17 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.0587511658668518e-03</threshold>
            <left_val>-0.7361145019531250</left_val>
            <right_val>0.5566077232360840</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 4 4 -1.</_>
                <_>
                  0 2 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.9959779717028141e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4147691130638123</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 0 3 3 -1.</_>
                <_>
                  2 1 1 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.0803930759429932e-03</threshold>
            <left_val>0.5927835702896118</left_val>
            <right_val>-0.6738492250442505</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 18 6 2 -1.</_>
                <_>
                  3 19 6 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.9909010734409094e-03</threshold>
            <left_val>-0.4214592874050140</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 18 4 2 -1.</_>
                <_>
                  8 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.6845749923959374e-03</threshold>
            <left_val>0.5467922091484070</left_val>
            <right_val>-0.7509945034980774</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 10 12 2 -1.</_>
                <_>
                  6 11 12 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.0781872123479843e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3989954888820648</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 8 3 1 -1.</_>
                <_>
                  16 9 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.6645609177649021e-03</threshold>
            <left_val>0.5894060134887695</left_val>
            <right_val>-0.4677804112434387</right_val></_></_></trees>
      <stage_threshold>-2.6498730182647705</stage_threshold>
      <parent>0</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 2 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 7 9 12 -1.</_>
                <_>
                  8 11 3 4 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.2530143857002258</threshold>
            <left_node>1</left_node>
            <right_val>-0.7540258765220642</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 13 1 6 -1.</_>
                <_>
                  16 16 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.9663778841495514e-03</threshold>
            <left_val>-0.3527964949607849</left_val>
            <right_val>0.8799229860305786</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 7 5 6 -1.</_>
                <_>
                  7 9 5 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0471276491880417</threshold>
            <left_node>1</left_node>
            <right_val>-0.5223489999771118</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 12 4 6 -1.</_>
                <_>
                  18 12 2 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.9500750349834561e-03</threshold>
            <left_val>-0.3037990927696228</left_val>
            <right_val>0.7520437836647034</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 6 8 -1.</_>
                <_>
                  0 4 6 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0714810267090797</threshold>
            <left_val>0.6584190130233765</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 1 15 12 -1.</_>
                <_>
                  3 5 15 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2218973040580750</threshold>
            <left_val>-0.6090720295906067</left_val>
            <right_val>0.5684216022491455</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 12 9 8 -1.</_>
                <_>
                  11 16 9 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0338428206741810</threshold>
            <left_val>-0.6431164741516113</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 12 9 -1.</_>
                <_>
                  4 0 4 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.1714561413973570e-04</threshold>
            <left_val>0.5462036132812500</left_val>
            <right_val>-0.3998414874076843</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 6 4 -1.</_>
                <_>
                  2 12 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.4458211157470942e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4563683867454529</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 18 4 2 -1.</_>
                <_>
                  11 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.4395729415118694e-03</threshold>
            <left_val>0.4779818952083588</left_val>
            <right_val>-0.9124708771705627</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 2 3 3 -1.</_>
                <_>
                  6 2 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.1385070867836475e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.8361775875091553</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 18 3 2 -1.</_>
                <_>
                  13 18 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.8324409611523151e-03</threshold>
            <left_val>0.3346279859542847</left_val>
            <right_val>-0.7500854730606079</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 2 8 -1.</_>
                <_>
                  1 0 1 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1167610064148903e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6908379793167114</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 18 4 2 -1.</_>
                <_>
                  5 19 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.9106997367925942e-05</threshold>
            <left_val>-0.3456133008003235</left_val>
            <right_val>0.4118317961692810</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 11 6 6 -1.</_>
                <_>
                  17 11 3 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0154477702453732</threshold>
            <left_node>1</left_node>
            <right_val>0.3698019087314606</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 12 8 4 -1.</_>
                <_>
                  8 12 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0322449393570423</threshold>
            <left_val>0.6111283898353577</left_val>
            <right_val>-0.5568534135818481</right_val></_></_></trees>
      <stage_threshold>-2.3828399181365967</stage_threshold>
      <parent>1</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 3 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 6 4 9 -1.</_>
                <_>
                  9 9 4 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.1225112974643707</threshold>
            <left_node>1</left_node>
            <right_val>-0.6702662706375122</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 9 4 7 -1.</_>
                <_>
                  12 10 2 7 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0142306098714471</threshold>
            <left_val>0.8780239224433899</left_val>
            <right_val>-0.1878418028354645</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 8 4 8 -1.</_>
                <_>
                  5 8 2 4 2.</_>
                <_>
                  7 12 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.9833219274878502e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5812284946441650</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 12 11 8 -1.</_>
                <_>
                  8 16 11 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0770851373672485</threshold>
            <left_val>-0.5039535164833069</left_val>
            <right_val>0.6738736033439636</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 0 14 6 -1.</_>
                <_>
                  3 3 14 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1108618974685669</threshold>
            <left_val>0.6343203783035278</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 1 6 12 -1.</_>
                <_>
                  7 4 6 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0946047604084015</threshold>
            <left_val>-0.4972639083862305</left_val>
            <right_val>0.3878743946552277</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 18 7 2 -1.</_>
                <_>
                  0 19 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.7696130089461803e-04</threshold>
            <left_val>-0.6393880248069763</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 12 4 3 -1.</_>
                <_>
                  18 12 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.0120320841670036e-03</threshold>
            <left_val>-0.3531391024589539</left_val>
            <right_val>0.5153843760490417</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 4 8 -1.</_>
                <_>
                  2 0 2 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6102839726954699e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5191590189933777</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 0 4 1 -1.</_>
                <_>
                  5 0 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.6666069859638810e-03</threshold>
            <left_val>0.4047819077968597</left_val>
            <right_val>-0.6949635744094849</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 13 2 2 -1.</_>
                <_>
                  3 13 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.1480998303741217e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.4894518852233887</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 16 19 4 -1.</_>
                <_>
                  0 18 19 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.7647571191191673e-03</threshold>
            <left_val>-0.5003775954246521</left_val>
            <right_val>0.4079605937004089</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 13 8 2 -1.</_>
                <_>
                  11 13 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.8659597784280777e-03</threshold>
            <left_val>-0.3363642990589142</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 8 4 1 -1.</_>
                <_>
                  9 8 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2938310392200947e-03</threshold>
            <left_val>-0.6762138009071350</left_val>
            <right_val>0.4701024889945984</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 1 4 -1.</_>
                <_>
                  0 3 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.6533139063976705e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.4707160890102386</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 1 4 -1.</_>
                <_>
                  0 1 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.0565679296851158e-03</threshold>
            <left_val>0.4132341146469116</left_val>
            <right_val>-0.5552641749382019</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 15 5 2 -1.</_>
                <_>
                  15 16 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.8385717642959207e-05</threshold>
            <left_val>-0.5152115821838379</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 18 3 2 -1.</_>
                <_>
                  8 18 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.7511800397187471e-03</threshold>
            <left_val>0.3341724872589111</left_val>
            <right_val>-0.7955815792083740</right_val></_></_></trees>
      <stage_threshold>-2.1312201023101807</stage_threshold>
      <parent>2</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 4 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 7 3 8 -1.</_>
                <_>
                  11 9 3 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0646952390670776</threshold>
            <left_node>1</left_node>
            <right_val>-0.6132640242576599</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 12 2 8 -1.</_>
                <_>
                  15 16 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.5212170854210854e-03</threshold>
            <left_val>-0.5483155846595764</left_val>
            <right_val>0.7865244746208191</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 10 6 -1.</_>
                <_>
                  2 3 10 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0981097668409348</threshold>
            <left_val>0.6911330819129944</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 5 18 15 -1.</_>
                <_>
                  6 10 6 5 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.8593845963478088</threshold>
            <left_val>0.4536468088626862</left_val>
            <right_val>-0.5002614855766296</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 11 12 6 -1.</_>
                <_>
                  7 13 4 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0898361727595329</threshold>
            <left_node>1</left_node>
            <right_val>-0.5292878150939941</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 12 4 7 -1.</_>
                <_>
                  18 12 2 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.6945930439978838e-03</threshold>
            <left_val>-0.3819977939128876</left_val>
            <right_val>0.5782129764556885</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 18 4 2 -1.</_>
                <_>
                  9 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5973599404096603e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.9192836880683899</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 17 4 3 -1.</_>
                <_>
                  9 17 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0058110132813454e-03</threshold>
            <left_val>-0.8021379709243774</left_val>
            <right_val>0.2925927937030792</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 6 6 -1.</_>
                <_>
                  2 12 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.5496290549635887e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4367895126342773</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 16 4 4 -1.</_>
                <_>
                  5 16 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.7376728616654873e-03</threshold>
            <left_val>0.4101088047027588</left_val>
            <right_val>-0.7269281148910522</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 0 4 6 -1.</_>
                <_>
                  4 0 2 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.6190437860786915e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.8489515185356140</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 0 4 7 -1.</_>
                <_>
                  2 0 2 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.5377281494438648e-03</threshold>
            <left_val>0.3012467920780182</left_val>
            <right_val>-0.7030177116394043</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 8 3 -1.</_>
                <_>
                  6 0 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4952790699899197e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4678474962711334</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 3 4 6 -1.</_>
                <_>
                  9 3 2 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.1753767766058445e-03</threshold>
            <left_val>-0.7453035116195679</left_val>
            <right_val>0.4001182019710541</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 10 3 2 -1.</_>
                <_>
                  10 11 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.2049742080271244e-03</threshold>
            <left_val>0.4866926968097687</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 3 7 6 -1.</_>
                <_>
                  4 6 7 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0878920033574104</threshold>
            <left_val>0.8349394798278809</left_val>
            <right_val>-0.3382771909236908</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 18 10 2 -1.</_>
                <_>
                  15 18 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.9997250102460384e-03</threshold>
            <left_val>-0.2903988957405090</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 13 6 1 -1.</_>
                <_>
                  9 13 3 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-9.0990252792835236e-03</threshold>
            <left_val>0.6231582164764404</left_val>
            <right_val>-0.3542473018169403</right_val></_></_></trees>
      <stage_threshold>-2.0176210403442383</stage_threshold>
      <parent>3</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 5 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 8 4 6 -1.</_>
                <_>
                  8 10 4 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0557021014392376</threshold>
            <left_node>1</left_node>
            <right_val>-0.6984158158302307</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 12 6 8 -1.</_>
                <_>
                  14 16 6 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0340332910418510</threshold>
            <left_val>-0.3950918912887573</left_val>
            <right_val>0.8031312823295593</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 8 6 4 -1.</_>
                <_>
                  12 10 2 4 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0461990609765053</threshold>
            <left_node>1</left_node>
            <right_val>-0.4886038005352020</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 12 6 3 -1.</_>
                <_>
                  2 12 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.8061669804155827e-03</threshold>
            <left_val>0.8077561259269714</left_val>
            <right_val>-0.0744908228516579</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 11 2 6 -1.</_>
                <_>
                  19 11 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.8170489929616451e-03</threshold>
            <left_val>-0.3804352879524231</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 1 10 -1.</_>
                <_>
                  0 5 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.6162370815873146e-03</threshold>
            <left_val>0.6045172214508057</left_val>
            <right_val>-0.2258224040269852</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 4 8 12 -1.</_>
                <_>
                  7 4 4 12 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0157069507986307</threshold>
            <left_node>1</left_node>
            <right_val>-0.3757799863815308</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 3 9 8 -1.</_>
                <_>
                  4 3 3 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.3929950334131718e-03</threshold>
            <left_val>0.5421422123908997</left_val>
            <right_val>-0.3738824129104614</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 2 2 -1.</_>
                <_>
                  0 1 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0047219984699041e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.4743340909481049</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 8 6 12 -1.</_>
                <_>
                  14 12 2 4 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0864751189947128</threshold>
            <left_val>0.5018631815910339</left_val>
            <right_val>-0.2113623023033142</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 2 14 6 -1.</_>
                <_>
                  4 4 14 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0779607668519020</threshold>
            <left_val>0.5733734965324402</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 0 12 8 -1.</_>
                <_>
                  3 4 12 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0985612869262695</threshold>
            <left_val>-0.3251555860042572</left_val>
            <right_val>0.5303598046302795</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 17 20 -1.</_>
                <_>
                  0 5 17 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.5435916781425476</threshold>
            <left_val>0.5946429967880249</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 0 13 6 -1.</_>
                <_>
                  4 2 13 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0441776998341084</threshold>
            <left_val>0.2967107892036438</left_val>
            <right_val>-0.3847483098506927</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 10 3 6 -1.</_>
                <_>
                  3 10 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.8016409426927567e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.3200058937072754</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 14 6 4 -1.</_>
                <_>
                  4 14 3 2 2.</_>
                <_>
                  7 16 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.6359390467405319e-03</threshold>
            <left_val>-0.1758614033460617</left_val>
            <right_val>0.4836035072803497</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 1 6 8 -1.</_>
                <_>
                  10 1 2 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0142036899924278</threshold>
            <left_val>-0.7788208723068237</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 1 2 6 -1.</_>
                <_>
                  1 1 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.3902818257920444e-05</threshold>
            <left_val>0.3061941862106323</left_val>
            <right_val>-0.3319604992866516</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 12 1 3 -1.</_>
                <_>
                  7 13 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.6157240867614746e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.4968977868556976</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 4 8 4 -1.</_>
                <_>
                  5 4 8 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0111523102968931</threshold>
            <left_val>-0.5343589186668396</left_val>
            <right_val>0.0972294434905052</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 2 4 5 -1.</_>
                <_>
                  1 2 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.0547702014446259e-03</threshold>
            <left_val>-0.8381121754646301</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 12 3 2 -1.</_>
                <_>
                  6 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.1118740551173687e-03</threshold>
            <left_val>0.6361703276634216</left_val>
            <right_val>-0.0482991896569729</right_val></_></_></trees>
      <stage_threshold>-2.2212049961090088</stage_threshold>
      <parent>4</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 6 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 13 8 2 -1.</_>
                <_>
                  7 13 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0129568297415972</threshold>
            <left_node>1</left_node>
            <right_val>-0.6487473249435425</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 9 9 8 -1.</_>
                <_>
                  11 11 9 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0271410197019577</threshold>
            <left_val>0.7629305720329285</left_val>
            <right_val>-0.3394787013530731</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 12 4 3 -1.</_>
                <_>
                  18 12 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.5119998976588249e-03</threshold>
            <left_val>-0.5005983710289001</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 14 4 6 -1.</_>
                <_>
                  16 17 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0125166904181242</threshold>
            <left_val>-0.3687332868576050</left_val>
            <right_val>0.5988863110542297</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 6 3 -1.</_>
                <_>
                  2 12 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.0557941906154156e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3894093036651611</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 6 7 6 -1.</_>
                <_>
                  6 8 7 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0469237491488457</threshold>
            <left_val>0.6326891183853149</left_val>
            <right_val>-0.2627002894878387</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 1 6 -1.</_>
                <_>
                  0 3 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4018269032239914e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5051792860031128</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 2 15 5 -1.</_>
                <_>
                  5 2 5 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0159360896795988</threshold>
            <left_val>0.6552600264549255</left_val>
            <right_val>-0.1730810999870300</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 11 10 3 -1.</_>
                <_>
                  13 11 5 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0140002900734544</threshold>
            <left_val>-0.4165323078632355</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 11 2 8 -1.</_>
                <_>
                  8 15 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0132027799263597</threshold>
            <left_val>-0.4912196993827820</left_val>
            <right_val>0.3739793896675110</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 2 6 -1.</_>
                <_>
                  1 1 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.7658580802381039e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.4538286924362183</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 1 4 4 -1.</_>
                <_>
                  1 1 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.8634149134159088e-03</threshold>
            <left_val>-0.5979688167572021</left_val>
            <right_val>0.3121772110462189</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 16 3 1 -1.</_>
                <_>
                  6 17 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.7654920704662800e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7647656798362732</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 0 7 15 -1.</_>
                <_>
                  5 5 7 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2553476989269257</threshold>
            <left_val>-0.0342672206461430</left_val>
            <right_val>0.7078657746315002</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 0 3 2 -1.</_>
                <_>
                  18 1 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.6812961809337139e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7879086136817932</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 18 6 2 -1.</_>
                <_>
                  6 18 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.5162130631506443e-03</threshold>
            <left_val>0.1887757927179337</left_val>
            <right_val>-0.7913225889205933</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 1 4 5 -1.</_>
                <_>
                  7 1 2 5 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0573253296315670</threshold>
            <left_node>1</left_node>
            <right_val>0.6234918832778931</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 0 6 8 -1.</_>
                <_>
                  14 0 3 4 2.</_>
                <_>
                  17 4 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0127183301374316</threshold>
            <left_val>0.3086060881614685</left_val>
            <right_val>-0.3278433084487915</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 2 4 18 -1.</_>
                <_>
                  5 2 2 9 2.</_>
                <_>
                  7 11 2 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.7374261561781168e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.4545154869556427</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 18 6 2 -1.</_>
                <_>
                  9 18 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.6564649567008018e-03</threshold>
            <left_val>0.2743133902549744</left_val>
            <right_val>-0.7844793796539307</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 8 2 3 -1.</_>
                <_>
                  10 9 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.1134090386331081e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.3973877131938934</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 10 4 2 -1.</_>
                <_>
                  10 10 2 1 2.</_>
                <_>
                  12 11 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.4249779526144266e-03</threshold>
            <left_val>-0.3519827127456665</left_val>
            <right_val>0.3049009144306183</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 2 12 6 -1.</_>
                <_>
                  4 4 12 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0556414611637592</threshold>
            <left_val>0.4557549059391022</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 1 12 8 -1.</_>
                <_>
                  5 3 12 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0435481294989586</threshold>
            <left_val>-0.3337092995643616</left_val>
            <right_val>0.2950142920017242</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 18 4 2 -1.</_>
                <_>
                  2 19 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.0783379962667823e-04</threshold>
            <left_node>1</left_node>
            <right_val>0.2246004045009613</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 18 8 1 -1.</_>
                <_>
                  4 18 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.8713270546868443e-03</threshold>
            <left_val>-0.6604840755462646</left_val>
            <right_val>0.1503167003393173</right_val></_></_></trees>
      <stage_threshold>-2.1328830718994141</stage_threshold>
      <parent>5</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 7 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 7 12 12 -1.</_>
                <_>
                  8 11 4 4 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.4351662993431091</threshold>
            <left_node>1</left_node>
            <right_val>-0.4995929002761841</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 11 4 6 -1.</_>
                <_>
                  18 11 2 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.2595037743449211e-03</threshold>
            <left_val>-0.2363958954811096</left_val>
            <right_val>0.7997537851333618</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 13 6 7 -1.</_>
                <_>
                  8 13 2 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.6518150269985199e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5475280880928040</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 1 8 -1.</_>
                <_>
                  0 4 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.7092090137302876e-03</threshold>
            <left_val>0.6427332758903503</left_val>
            <right_val>-0.2151180952787399</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 14 5 6 -1.</_>
                <_>
                  15 17 5 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0194501802325249</threshold>
            <left_val>-0.5360500216484070</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 7 6 9 -1.</_>
                <_>
                  2 7 2 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.4476498626172543e-03</threshold>
            <left_val>0.5579450130462646</left_val>
            <right_val>-0.2147496044635773</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 11 4 1 -1.</_>
                <_>
                  16 12 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.6347589553333819e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.5596284270286560</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 11 8 2 -1.</_>
                <_>
                  15 11 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.1614650078117847e-03</threshold>
            <left_val>-0.1660436987876892</left_val>
            <right_val>0.4680525958538055</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 12 11 -1.</_>
                <_>
                  3 1 6 11 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0131451701745391</threshold>
            <left_node>1</left_node>
            <right_val>-0.4127990901470184</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 8 6 4 -1.</_>
                <_>
                  7 9 6 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0114368097856641</threshold>
            <left_val>0.3790180087089539</left_val>
            <right_val>-0.4179157912731171</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 17 6 3 -1.</_>
                <_>
                  8 17 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.2912001051008701e-03</threshold>
            <left_val>-0.7608966827392578</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 1 4 -1.</_>
                <_>
                  0 2 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.2170921117067337e-04</threshold>
            <left_val>0.3252761960029602</left_val>
            <right_val>-0.3011097013950348</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 1 1 3 -1.</_>
                <_>
                  2 2 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.3754010219126940e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7837396264076233</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 11 2 3 -1.</_>
                <_>
                  18 12 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5100160855799913e-03</threshold>
            <left_val>0.1852544993162155</left_val>
            <right_val>-0.5808495879173279</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 12 2 8 -1.</_>
                <_>
                  3 12 1 4 2.</_>
                <_>
                  4 16 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2884209863841534e-03</threshold>
            <left_val>0.2733950018882751</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 12 3 3 -1.</_>
                <_>
                  4 12 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.8726480193436146e-03</threshold>
            <left_val>0.1681987941265106</left_val>
            <right_val>-0.5198690295219421</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 18 4 2 -1.</_>
                <_>
                  12 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.4010189808905125e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.8296467065811157</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 10 3 3 -1.</_>
                <_>
                  17 11 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.8938081599771976e-03</threshold>
            <left_val>0.1679659932851791</left_val>
            <right_val>-0.6553087234497070</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 14 5 2 -1.</_>
                <_>
                  7 15 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.1223020050674677e-03</threshold>
            <left_val>-0.4352130889892578</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 0 4 5 -1.</_>
                <_>
                  6 0 2 5 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0503664910793304</threshold>
            <left_val>-5.8327801525592804e-03</left_val>
            <right_val>0.7087830901145935</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 1 5 8 -1.</_>
                <_>
                  6 5 5 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0361518003046513</threshold>
            <left_node>1</left_node>
            <right_val>0.4497916102409363</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 1 9 8 -1.</_>
                <_>
                  3 5 9 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1342658996582031</threshold>
            <left_val>0.3947243094444275</left_val>
            <right_val>-0.3758862912654877</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 14 15 6 -1.</_>
                <_>
                  7 14 5 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0277913697063923</threshold>
            <left_node>1</left_node>
            <right_val>-0.2948872148990631</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 3 6 5 -1.</_>
                <_>
                  14 3 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0127121703699231</threshold>
            <left_val>-0.7201173901557922</left_val>
            <right_val>0.3659502863883972</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 16 2 2 -1.</_>
                <_>
                  5 16 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-3.8276749546639621e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.4058133959770203</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 16 2 2 -1.</_>
                <_>
                  5 16 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.1330529861152172e-03</threshold>
            <left_val>-0.5272595882415771</left_val>
            <right_val>0.3604049980640411</right_val></_></_></trees>
      <stage_threshold>-1.9884539842605591</stage_threshold>
      <parent>6</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 8 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 8 6 4 -1.</_>
                <_>
                  11 10 2 4 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0477486699819565</threshold>
            <left_node>1</left_node>
            <right_val>-0.5990238785743713</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 11 3 4 -1.</_>
                <_>
                  4 13 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.6201851218938828e-03</threshold>
            <left_val>-0.2488749027252197</left_val>
            <right_val>0.6920158267021179</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 8 6 12 -1.</_>
                <_>
                  15 12 2 4 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0853534564375877</threshold>
            <left_node>1</left_node>
            <right_val>-0.5171583294868469</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 1 10 -1.</_>
                <_>
                  0 5 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0110969245433807e-03</threshold>
            <left_val>0.5695065259933472</left_val>
            <right_val>-0.2474942058324814</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 6 4 -1.</_>
                <_>
                  2 12 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.6567470096051693e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3731651902198792</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 5 8 6 -1.</_>
                <_>
                  5 7 8 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0359194912016392</threshold>
            <left_val>0.4943858087062836</left_val>
            <right_val>-0.3958668112754822</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 1 16 4 -1.</_>
                <_>
                  3 3 16 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0743266269564629</threshold>
            <left_val>0.5675597786903381</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 2 10 9 -1.</_>
                <_>
                  6 5 10 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0901185870170593</threshold>
            <left_val>-0.3892117142677307</left_val>
            <right_val>0.3107909858226776</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 10 6 10 -1.</_>
                <_>
                  17 10 3 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0167364608496428</threshold>
            <left_val>-0.3667413890361786</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 17 4 3 -1.</_>
                <_>
                  6 17 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.8592580454424024e-03</threshold>
            <left_val>0.3487572073936462</left_val>
            <right_val>-0.5748311281204224</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 12 3 2 -1.</_>
                <_>
                  6 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.5264140032231808e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.6787899136543274</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 12 3 2 -1.</_>
                <_>
                  6 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.5309391096234322e-03</threshold>
            <left_val>0.4861792027950287</left_val>
            <right_val>-0.2566064000129700</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 2 9 -1.</_>
                <_>
                  1 0 1 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.9510748795000836e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.4566124081611633</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 6 3 2 -1.</_>
                <_>
                  2 6 3 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.8923248909413815e-03</threshold>
            <left_val>-0.5713472962379456</left_val>
            <right_val>0.3292104899883270</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 16 6 3 -1.</_>
                <_>
                  9 16 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.1156069859862328e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7131536006927490</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 17 6 2 -1.</_>
                <_>
                  9 17 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.5014882236719131e-03</threshold>
            <left_val>-0.5913907885551453</left_val>
            <right_val>0.1980594992637634</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 3 9 6 -1.</_>
                <_>
                  4 5 9 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0423780605196953</threshold>
            <left_node>1</left_node>
            <right_val>-0.3823930025100708</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 15 3 2 -1.</_>
                <_>
                  7 16 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.2011259570717812e-03</threshold>
            <left_val>0.3345701098442078</left_val>
            <right_val>-0.4303233921527863</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 2 3 3 -1.</_>
                <_>
                  7 2 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.1217379253357649e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6831002235412598</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 1 6 4 -1.</_>
                <_>
                  4 1 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.4385468140244484e-03</threshold>
            <left_val>0.2047861069440842</left_val>
            <right_val>-0.6179394125938416</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 11 4 2 -1.</_>
                <_>
                  13 11 2 1 2.</_>
                <_>
                  15 12 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.1177410855889320e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.5113716125488281</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 10 2 2 -1.</_>
                <_>
                  14 10 1 1 2.</_>
                <_>
                  15 11 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.2230269173160195e-04</threshold>
            <left_val>-0.3644020855426788</left_val>
            <right_val>0.2107304930686951</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 7 3 3 -1.</_>
                <_>
                  18 8 1 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.5657291561365128e-03</threshold>
            <left_val>-0.6458150148391724</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 7 3 2 -1.</_>
                <_>
                  18 8 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.5686610024422407e-03</threshold>
            <left_val>0.2764356136322021</left_val>
            <right_val>-0.3419849872589111</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 3 1 2 -1.</_>
                <_>
                  0 4 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.2437567976303399e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.3175807893276215</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 1 2 5 -1.</_>
                <_>
                  11 1 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.6269261036068201e-03</threshold>
            <left_val>-0.8105195760726929</left_val>
            <right_val>0.2721863090991974</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 8 3 12 -1.</_>
                <_>
                  1 11 3 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.4638389479368925e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3951576948165894</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 10 8 2 -1.</_>
                <_>
                  2 10 4 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0749301910400391</threshold>
            <left_val>-0.5435386896133423</left_val>
            <right_val>0.2610611915588379</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 12 3 3 -1.</_>
                <_>
                  7 13 1 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.7247250378131866e-03</threshold>
            <left_val>0.4112487137317657</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 11 3 4 -1.</_>
                <_>
                  7 11 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.5450199395418167e-03</threshold>
            <left_val>-0.3157655000686646</left_val>
            <right_val>0.3904697000980377</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 17 4 2 -1.</_>
                <_>
                  6 17 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.7354240883141756e-03</threshold>
            <left_val>-0.7490674853324890</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 19 20 1 -1.</_>
                <_>
                  10 19 10 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0169694703072309</threshold>
            <left_val>-0.6243721842765808</left_val>
            <right_val>0.1838738024234772</right_val></_></_></trees>
      <stage_threshold>-2.0902318954467773</stage_threshold>
      <parent>7</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 9 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 11 8 5 -1.</_>
                <_>
                  7 11 4 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0249786991626024</threshold>
            <left_node>1</left_node>
            <right_val>-0.6069788932800293</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 8 8 9 -1.</_>
                <_>
                  10 11 8 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0580078698694706</threshold>
            <left_val>0.7147802114486694</left_val>
            <right_val>-0.2994323968887329</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 13 6 2 -1.</_>
                <_>
                  2 13 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.1753749139606953e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3529798984527588</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 14 2 1 -1.</_>
                <_>
                  18 14 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-8.9618662605062127e-04</threshold>
            <left_val>0.5441746115684509</left_val>
            <right_val>-0.3978995084762573</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 2 2 4 -1.</_>
                <_>
                  2 2 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.8718139219563454e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.4889818131923676</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 5 8 5 -1.</_>
                <_>
                  9 5 4 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.7620530240237713e-03</threshold>
            <left_val>-0.3114455938339233</left_val>
            <right_val>0.4678679108619690</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 13 5 4 -1.</_>
                <_>
                  7 15 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0197512805461884</threshold>
            <left_val>-0.4302048981189728</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 18 3 2 -1.</_>
                <_>
                  17 19 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2683609966188669e-03</threshold>
            <left_val>-0.5409085154533386</left_val>
            <right_val>0.3979752063751221</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 2 1 2 -1.</_>
                <_>
                  0 3 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.5749718992738053e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.4451893866062164</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 0 1 3 -1.</_>
                <_>
                  2 1 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.4090509396046400e-03</threshold>
            <left_val>0.2882230877876282</left_val>
            <right_val>-0.5451431274414062</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 11 3 4 -1.</_>
                <_>
                  11 11 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.5728669501841068e-03</threshold>
            <left_val>0.5503987073898315</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 11 4 8 -1.</_>
                <_>
                  16 11 2 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.9018214493989944e-03</threshold>
            <left_val>-0.4159888923168182</left_val>
            <right_val>0.1746889948844910</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 2 9 6 -1.</_>
                <_>
                  2 5 9 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1205644980072975</threshold>
            <left_val>0.6889057755470276</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 4 17 8 -1.</_>
                <_>
                  0 6 17 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0469199307262897</threshold>
            <left_val>-0.4226630926132202</left_val>
            <right_val>0.1701094061136246</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 17 5 3 -1.</_>
                <_>
                  15 18 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.2390259914100170e-03</threshold>
            <left_val>-0.6304534077644348</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 11 2 8 -1.</_>
                <_>
                  2 15 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.2174249645322561e-03</threshold>
            <left_val>-0.3609794974327087</left_val>
            <right_val>0.2493373006582260</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 12 3 3 -1.</_>
                <_>
                  4 12 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.5738790221512318e-04</threshold>
            <left_val>0.3099347949028015</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 12 9 7 -1.</_>
                <_>
                  6 12 3 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0184324495494366</threshold>
            <left_val>0.0977584496140480</left_val>
            <right_val>-0.5074235200881958</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 1 4 7 -1.</_>
                <_>
                  14 1 2 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.8692828752100468e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7455605864524841</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 16 2 2 -1.</_>
                <_>
                  3 16 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.8751699291169643e-03</threshold>
            <left_val>-0.6745839118957520</left_val>
            <right_val>0.1591881066560745</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 17 2 1 -1.</_>
                <_>
                  3 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.8542227381840348e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.4127942025661469</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 9 6 6 -1.</_>
                <_>
                  4 9 3 3 2.</_>
                <_>
                  7 12 3 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0106585798785090</threshold>
            <left_val>0.3700270950794220</left_val>
            <right_val>-0.2173172980546951</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 13 3 1 -1.</_>
                <_>
                  12 13 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.8811509944498539e-03</threshold>
            <left_val>0.5790283083915710</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 20 3 -1.</_>
                <_>
                  5 0 10 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0223091300576925</threshold>
            <left_val>0.1972568035125732</left_val>
            <right_val>-0.3247519135475159</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 1 2 -1.</_>
                <_>
                  0 1 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.5826578065752983e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.6063023805618286</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 0 3 1 -1.</_>
                <_>
                  18 1 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.0781588070094585e-03</threshold>
            <left_val>-0.7712330222129822</left_val>
            <right_val>0.1818612962961197</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 8 9 -1.</_>
                <_>
                  4 3 8 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0562150813639164</threshold>
            <left_node>1</left_node>
            <right_val>0.5056139826774597</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 0 6 4 -1.</_>
                <_>
                  6 2 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0377205908298492</threshold>
            <left_val>0.3605211079120636</left_val>
            <right_val>-0.3274376094341278</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 0 2 1 -1.</_>
                <_>
                  18 0 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.9480631239712238e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7578818202018738</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 2 6 1 -1.</_>
                <_>
                  17 2 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4269670248031616e-03</threshold>
            <left_val>0.5207610130310059</left_val>
            <right_val>-0.0610213615000248</right_val></_></_></trees>
      <stage_threshold>-1.9407310485839844</stage_threshold>
      <parent>8</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 10 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 13 8 2 -1.</_>
                <_>
                  7 13 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0169066991657019</threshold>
            <left_node>1</left_node>
            <right_val>-0.4750126898288727</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 12 3 8 -1.</_>
                <_>
                  15 16 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0253278408199549</threshold>
            <left_val>-0.4401676058769226</left_val>
            <right_val>0.6088535189628601</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 10 8 3 -1.</_>
                <_>
                  5 11 8 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0156633201986551</threshold>
            <left_val>0.5710005164146423</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 0 11 9 -1.</_>
                <_>
                  5 3 11 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1610189974308014</threshold>
            <left_val>0.4098914861679077</left_val>
            <right_val>-0.3814237117767334</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 14 2 2 -1.</_>
                <_>
                  19 14 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.6885380318854004e-04</threshold>
            <left_val>-0.4795849025249481</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 3 9 8 -1.</_>
                <_>
                  4 3 3 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0552360694855452e-03</threshold>
            <left_val>0.4285230040550232</left_val>
            <right_val>-0.2825263142585754</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 6 2 3 -1.</_>
                <_>
                  2 7 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.8042940907180309e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6865913867950439</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 6 2 3 -1.</_>
                <_>
                  2 7 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.0092511810362339e-03</threshold>
            <left_val>-0.5903354287147522</left_val>
            <right_val>0.1973250061273575</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 7 1 12 -1.</_>
                <_>
                  13 11 1 4 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0371195189654827</threshold>
            <left_node>1</left_node>
            <right_val>-0.4313096106052399</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 1 15 -1.</_>
                <_>
                  0 5 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.7857799325138330e-03</threshold>
            <left_val>0.3359619081020355</left_val>
            <right_val>-0.3740172088146210</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 9 6 3 -1.</_>
                <_>
                  6 10 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0108698504045606</threshold>
            <left_val>0.5484120845794678</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 18 3 2 -1.</_>
                <_>
                  3 19 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.0577541221864522e-04</threshold>
            <left_val>-0.5002269744873047</left_val>
            <right_val>0.0514238588511944</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 17 4 3 -1.</_>
                <_>
                  16 18 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0201490521430969e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5901622772216797</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 17 4 3 -1.</_>
                <_>
                  11 17 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5601210072636604e-03</threshold>
            <left_val>0.1946980059146881</left_val>
            <right_val>-0.6464836001396179</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 13 4 3 -1.</_>
                <_>
                  14 13 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2395749799907207e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.2776215970516205</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 15 3 2 -1.</_>
                <_>
                  5 16 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.1075750961899757e-03</threshold>
            <left_val>-0.6114916205406189</left_val>
            <right_val>0.3525038957595825</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 4 2 2 -1.</_>
                <_>
                  1 4 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.4853738876990974e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.3400886058807373</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 0 2 5 -1.</_>
                <_>
                  5 0 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.3282810579985380e-03</threshold>
            <left_val>0.2713474929332733</left_val>
            <right_val>-0.6691539883613586</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 9 3 8 -1.</_>
                <_>
                  1 11 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5571110416203737e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4114424884319305</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 8 1 3 -1.</_>
                <_>
                  4 9 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.3992219939827919e-03</threshold>
            <left_val>0.2593970000743866</left_val>
            <right_val>-0.4038029909133911</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 13 2 1 -1.</_>
                <_>
                  5 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.7784422319382429e-04</threshold>
            <left_node>1</left_node>
            <right_val>0.2952392101287842</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 11 4 9 -1.</_>
                <_>
                  11 11 2 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.2334199640899897e-03</threshold>
            <left_val>-0.5843685269355774</left_val>
            <right_val>-0.0179366394877434</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 1 2 -1.</_>
                <_>
                  0 2 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.6113858590833843e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.3502165079116821</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 1 3 -1.</_>
                <_>
                  0 1 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.9111000001430511e-03</threshold>
            <left_val>0.2631261050701141</left_val>
            <right_val>-0.6154934763908386</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 11 1 4 -1.</_>
                <_>
                  12 12 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.4321150742471218e-03</threshold>
            <left_val>0.3749330043792725</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 10 3 3 -1.</_>
                <_>
                  15 11 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0145419696345925</threshold>
            <left_val>0.4378893077373505</left_val>
            <right_val>-0.3013161122798920</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 12 1 6 -1.</_>
                <_>
                  18 12 1 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0250270701944828</threshold>
            <left_val>-0.5282974839210510</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 17 3 2 -1.</_>
                <_>
                  5 17 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.1183639075607061e-03</threshold>
            <left_val>-0.8133684992790222</left_val>
            <right_val>0.1792842000722885</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 7 3 2 -1.</_>
                <_>
                  18 8 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.9415208846330643e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4724305868148804</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 9 2 1 -1.</_>
                <_>
                  18 9 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-2.4807679001241922e-03</threshold>
            <left_val>-0.6005833148956299</left_val>
            <right_val>0.2149710953235626</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 11 4 5 -1.</_>
                <_>
                  9 12 2 5 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.2498838156461716e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3323060870170593</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 1 2 7 -1.</_>
                <_>
                  8 1 1 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.6959328725934029e-03</threshold>
            <left_val>0.2124706953763962</left_val>
            <right_val>-0.8196725249290466</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 4 14 6 -1.</_>
                <_>
                  4 6 14 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0614260397851467</threshold>
            <left_val>0.5220044851303101</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 2 11 6 -1.</_>
                <_>
                  2 5 11 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0531767904758453</threshold>
            <left_val>-0.2985176146030426</left_val>
            <right_val>0.2865419089794159</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 16 2 2 -1.</_>
                <_>
                  18 17 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5695779186207801e-05</threshold>
            <left_val>-0.3471929132938385</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 11 2 6 -1.</_>
                <_>
                  18 11 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.4311970919370651e-03</threshold>
            <left_val>-0.1213349029421806</left_val>
            <right_val>0.3896535038948059</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 0 3 3 -1.</_>
                <_>
                  18 1 1 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>5.6956289336085320e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6636403203010559</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 0 2 6 -1.</_>
                <_>
                  18 3 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.6630227956920862e-04</threshold>
            <left_val>0.2792190909385681</left_val>
            <right_val>-0.2162484973669052</right_val></_></_></trees>
      <stage_threshold>-2.1061589717864990</stage_threshold>
      <parent>9</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 11 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 7 6 8 -1.</_>
                <_>
                  4 7 3 4 2.</_>
                <_>
                  7 11 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0285095497965813</threshold>
            <left_node>1</left_node>
            <right_val>-0.5513324141502380</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 11 4 2 -1.</_>
                <_>
                  11 11 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0164291094988585</threshold>
            <left_val>0.6032876968383789</left_val>
            <right_val>-0.3000960052013397</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 6 7 -1.</_>
                <_>
                  3 0 3 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.8078952133655548e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4864051938056946</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 10 5 8 -1.</_>
                <_>
                  15 12 5 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0146703496575356</threshold>
            <left_val>0.4478665888309479</left_val>
            <right_val>-0.3544836044311523</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 10 3 8 -1.</_>
                <_>
                  3 10 1 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0694459779188037e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3859311938285828</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 7 6 6 -1.</_>
                <_>
                  7 9 6 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0506975390017033</threshold>
            <left_val>0.4386560022830963</left_val>
            <right_val>-0.3113405108451843</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 1 6 6 -1.</_>
                <_>
                  4 4 6 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0723180174827576</threshold>
            <left_val>0.5569549202919006</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 0 16 2 -1.</_>
                <_>
                  4 1 16 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0167407598346472</threshold>
            <left_val>0.3403693139553070</left_val>
            <right_val>-0.3771306872367859</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 8 6 6 -1.</_>
                <_>
                  14 8 3 3 2.</_>
                <_>
                  17 11 3 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0129232602193952</threshold>
            <left_node>1</left_node>
            <right_val>0.2698718011379242</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 12 2 8 -1.</_>
                <_>
                  4 12 1 4 2.</_>
                <_>
                  5 16 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.0832989830523729e-03</threshold>
            <left_val>0.0722172632813454</left_val>
            <right_val>-0.5061725974082947</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 18 7 2 -1.</_>
                <_>
                  0 19 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.9217539122328162e-04</threshold>
            <left_val>-0.4719946980476379</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 13 1 4 -1.</_>
                <_>
                  9 15 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.6477448195219040e-03</threshold>
            <left_val>-0.2023364007472992</left_val>
            <right_val>0.3668462038040161</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 10 2 8 -1.</_>
                <_>
                  19 10 1 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.6355320112779737e-03</threshold>
            <left_val>-0.3336915075778961</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 0 4 8 -1.</_>
                <_>
                  7 0 2 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.0143060982227325e-03</threshold>
            <left_val>0.2633537054061890</left_val>
            <right_val>-0.7531512975692749</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 2 6 6 -1.</_>
                <_>
                  3 2 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0197680406272411</threshold>
            <left_val>-0.7339664101600647</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 10 8 2 -1.</_>
                <_>
                  10 10 4 1 2.</_>
                <_>
                  14 11 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0995801575481892e-03</threshold>
            <left_val>-0.1062633022665977</left_val>
            <right_val>0.3787747919559479</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 9 2 3 -1.</_>
                <_>
                  2 10 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.1737320348620415e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4587362110614777</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 1 13 6 -1.</_>
                <_>
                  5 3 13 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0236210599541664</threshold>
            <left_val>-0.0373419895768166</left_val>
            <right_val>0.5031296014785767</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 4 13 6 -1.</_>
                <_>
                  4 6 13 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0470704399049282</threshold>
            <left_node>1</left_node>
            <right_val>0.3915967047214508</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 1 4 5 -1.</_>
                <_>
                  8 1 2 5 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0484291613101959</threshold>
            <left_val>-0.2750763893127441</left_val>
            <right_val>0.3692345023155212</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 7 2 1 -1.</_>
                <_>
                  8 7 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.1763257437851280e-05</threshold>
            <left_val>-0.2613370120525360</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 5 4 4 -1.</_>
                <_>
                  6 5 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.0031517855823040e-03</threshold>
            <left_val>-0.4611847996711731</left_val>
            <right_val>0.3410157859325409</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 12 4 2 -1.</_>
                <_>
                  14 12 2 1 2.</_>
                <_>
                  16 13 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5536299217492342e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.4423784911632538</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 11 4 2 -1.</_>
                <_>
                  13 11 2 1 2.</_>
                <_>
                  15 12 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.5720898993313313e-03</threshold>
            <left_val>0.4306653141975403</left_val>
            <right_val>-0.2836068868637085</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 10 4 3 -1.</_>
                <_>
                  16 11 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.7512210011482239e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7764763236045837</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 0 4 5 -1.</_>
                <_>
                  11 0 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.7346918620169163e-03</threshold>
            <left_val>0.1455115973949432</left_val>
            <right_val>-0.7507416009902954</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 11 1 3 -1.</_>
                <_>
                  7 12 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.6438838839530945e-03</threshold>
            <left_val>0.4035055041313171</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 12 3 2 -1.</_>
                <_>
                  7 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.4590701106935740e-03</threshold>
            <left_val>0.2876971960067749</left_val>
            <right_val>-0.2802160084247589</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 8 2 3 -1.</_>
                <_>
                  17 8 1 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>9.9742468446493149e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6067702174186707</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 0 6 5 -1.</_>
                <_>
                  13 0 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0132336597889662</threshold>
            <left_val>0.1547808051109314</left_val>
            <right_val>-0.7075914740562439</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 3 3 -1.</_>
                <_>
                  0 1 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.0271311774849892e-03</threshold>
            <left_val>-0.7389777898788452</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 0 1 2 -1.</_>
                <_>
                  2 1 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2092100223526359e-04</threshold>
            <left_val>0.2347300052642822</left_val>
            <right_val>-0.2440057992935181</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 11 7 2 -1.</_>
                <_>
                  13 12 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2881499715149403e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.2890166938304901</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 8 3 3 -1.</_>
                <_>
                  18 9 1 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>6.2854858115315437e-03</threshold>
            <left_val>0.2810086905956268</left_val>
            <right_val>-0.5693385004997253</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 15 1 3 -1.</_>
                <_>
                  14 16 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>5.6929360143840313e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7845693230628967</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 13 6 2 -1.</_>
                <_>
                  8 13 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.3880861960351467e-03</threshold>
            <left_val>0.2620132863521576</left_val>
            <right_val>-0.2223203033208847</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 10 3 4 -1.</_>
                <_>
                  9 10 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.8205819912254810e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.5679597258567810</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 0 12 19 -1.</_>
                <_>
                  13 0 6 19 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.3427918851375580</threshold>
            <left_val>-0.1831423044204712</left_val>
            <right_val>0.5410807132720947</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 16 8 4 -1.</_>
                <_>
                  12 18 8 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.1370919682085514e-03</threshold>
            <left_val>-0.3911676108837128</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 5 12 2 -1.</_>
                <_>
                  14 5 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.1285221278667450e-03</threshold>
            <left_val>0.5307633876800537</left_val>
            <right_val>-0.0300193093717098</right_val></_></_></trees>
      <stage_threshold>-2.0051579475402832</stage_threshold>
      <parent>10</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 12 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 8 6 4 -1.</_>
                <_>
                  12 10 2 4 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0513861291110516</threshold>
            <left_node>1</left_node>
            <right_val>-0.5314878225326538</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 11 3 4 -1.</_>
                <_>
                  4 13 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.1850839518010616e-03</threshold>
            <left_val>-0.2474454045295715</left_val>
            <right_val>0.6118162274360657</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 2 12 7 -1.</_>
                <_>
                  3 2 6 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0152594000101089</threshold>
            <left_node>1</left_node>
            <right_val>-0.4330362975597382</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 0 4 2 -1.</_>
                <_>
                  8 0 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0259951502084732</threshold>
            <left_val>0.0439799018204212</left_val>
            <right_val>0.7382913827896118</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 11 6 6 -1.</_>
                <_>
                  15 13 2 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0323123708367348</threshold>
            <left_node>1</left_node>
            <right_val>-0.3960975110530853</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 11 10 4 -1.</_>
                <_>
                  12 11 5 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0137007003650069</threshold>
            <left_val>-0.2764388024806976</left_val>
            <right_val>0.4253535866737366</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 11 4 5 -1.</_>
                <_>
                  2 11 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2647869773209095e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3200556933879852</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 14 4 2 -1.</_>
                <_>
                  3 15 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.8290620110929012e-03</threshold>
            <left_val>-0.5168297290802002</left_val>
            <right_val>0.3697570860385895</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 1 6 -1.</_>
                <_>
                  0 3 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2481549531221390e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3624435067176819</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 2 6 6 -1.</_>
                <_>
                  6 5 6 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0459445491433144</threshold>
            <left_val>-1.3187309959903359e-03</left_val>
            <right_val>0.6321768164634705</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 18 4 2 -1.</_>
                <_>
                  7 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.8755620112642646e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7140339016914368</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 18 4 2 -1.</_>
                <_>
                  7 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.9700559787452221e-03</threshold>
            <left_val>-0.5873066186904907</left_val>
            <right_val>0.1759281009435654</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 4 7 4 -1.</_>
                <_>
                  3 5 7 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.5721389837563038e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3634751141071320</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 8 8 12 -1.</_>
                <_>
                  7 8 4 12 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0117461802437901</threshold>
            <left_val>0.3144079148769379</left_val>
            <right_val>-0.4011111855506897</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 17 2 1 -1.</_>
                <_>
                  5 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.6494120063725859e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.3779259026050568</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 18 2 1 -1.</_>
                <_>
                  5 18 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.2169408667832613e-05</threshold>
            <left_val>0.5279111266136169</left_val>
            <right_val>-0.1079031974077225</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 16 7 2 -1.</_>
                <_>
                  13 17 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.9697639800142497e-04</threshold>
            <left_val>-0.4709764122962952</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 15 2 3 -1.</_>
                <_>
                  7 15 1 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0114235095679760</threshold>
            <left_val>-0.8520929217338562</left_val>
            <right_val>0.1766286939382553</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 2 4 5 -1.</_>
                <_>
                  10 2 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.5562228187918663e-03</threshold>
            <left_val>-0.8060116171836853</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 2 4 6 -1.</_>
                <_>
                  8 2 2 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.4720191508531570e-03</threshold>
            <left_val>-0.6150020956993103</left_val>
            <right_val>0.1290830969810486</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 12 3 3 -1.</_>
                <_>
                  4 12 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7765410011634231e-03</threshold>
            <left_val>0.3138259947299957</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 12 3 3 -1.</_>
                <_>
                  6 13 1 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.8799277544021606e-03</threshold>
            <left_val>0.3039462864398956</left_val>
            <right_val>-0.3720492124557495</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 12 3 2 -1.</_>
                <_>
                  5 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4284689677879214e-03</threshold>
            <left_val>0.5041303038597107</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 13 3 1 -1.</_>
                <_>
                  11 13 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.8939910223707557e-03</threshold>
            <left_val>0.3482376039028168</left_val>
            <right_val>-0.2367382049560547</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 5 4 3 -1.</_>
                <_>
                  12 5 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.1496640294790268e-03</threshold>
            <left_val>-0.6681237816810608</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 7 1 10 -1.</_>
                <_>
                  19 12 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0107161197811365</threshold>
            <left_val>-0.4851551949977875</left_val>
            <right_val>0.1903641968965530</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 8 2 3 -1.</_>
                <_>
                  3 9 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.8033537827432156e-03</threshold>
            <left_val>-0.5697926878929138</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 0 6 5 -1.</_>
                <_>
                  9 0 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0149023197591305</threshold>
            <left_val>0.1309825032949448</left_val>
            <right_val>-0.7144827246665955</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 6 2 -1.</_>
                <_>
                  5 0 3 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0341702289879322</threshold>
            <left_val>0.5057513117790222</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 0 13 9 -1.</_>
                <_>
                  5 3 13 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1477925032377243</threshold>
            <left_val>0.2823326885700226</left_val>
            <right_val>-0.2720532119274139</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 6 1 2 -1.</_>
                <_>
                  0 7 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.5842810979811475e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.2693673074245453</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 0 16 6 -1.</_>
                <_>
                  1 2 16 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0398850813508034</threshold>
            <left_val>5.6696129031479359e-03</left_val>
            <right_val>0.6397516131401062</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 0 2 4 -1.</_>
                <_>
                  18 0 1 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0124831302091479</threshold>
            <left_node>1</left_node>
            <right_val>-0.7453374266624451</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 13 2 2 -1.</_>
                <_>
                  4 13 1 1 2.</_>
                <_>
                  5 14 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.2864511013031006e-04</threshold>
            <left_val>0.3644962012767792</left_val>
            <right_val>-0.0964988172054291</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 3 4 1 -1.</_>
                <_>
                  2 3 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4710469986312091e-04</threshold>
            <left_val>0.1406044065952301</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 0 8 12 -1.</_>
                <_>
                  3 6 8 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.2781434059143066</threshold>
            <left_val>0.5700283050537109</left_val>
            <right_val>-0.4875547885894775</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 13 4 1 -1.</_>
                <_>
                  13 13 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3452640268951654e-03</threshold>
            <left_val>0.3925583064556122</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 12 2 2 -1.</_>
                <_>
                  12 12 1 1 2.</_>
                <_>
                  13 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.1500842245295644e-04</threshold>
            <left_val>-0.3021517097949982</left_val>
            <right_val>0.3669803142547607</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 16 3 1 -1.</_>
                <_>
                  6 17 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-3.4133149310946465e-03</threshold>
            <left_val>-0.6408581733703613</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 13 8 4 -1.</_>
                <_>
                  3 13 4 2 2.</_>
                <_>
                  7 15 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.1169008947908878e-03</threshold>
            <left_val>-0.2305258065462112</left_val>
            <right_val>0.2428591996431351</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 18 3 -1.</_>
                <_>
                  6 9 6 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0888466984033585</threshold>
            <left_node>1</left_node>
            <right_val>0.4538188874721527</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 4 6 5 -1.</_>
                <_>
                  11 4 3 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.1080828309059143e-03</threshold>
            <left_val>-0.3588008880615234</left_val>
            <right_val>0.1320938020944595</right_val></_></_></trees>
      <stage_threshold>-2.1121981143951416</stage_threshold>
      <parent>11</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 13 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 14 9 1 -1.</_>
                <_>
                  8 14 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0159300006926060</threshold>
            <left_node>1</left_node>
            <right_val>-0.3524534106254578</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 0 4 4 -1.</_>
                <_>
                  4 0 2 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0274074506014585</threshold>
            <left_val>-0.0602367892861366</left_val>
            <right_val>0.7271584868431091</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 9 12 8 -1.</_>
                <_>
                  7 11 12 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0850376784801483</threshold>
            <left_node>1</left_node>
            <right_val>-0.4357671141624451</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 15 2 1 -1.</_>
                <_>
                  18 15 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.1508919997140765e-03</threshold>
            <left_val>0.4647167921066284</left_val>
            <right_val>-0.3589689135551453</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 13 2 4 -1.</_>
                <_>
                  3 13 1 2 2.</_>
                <_>
                  4 15 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.4599298639222980e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.3137106001377106</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 7 3 3 -1.</_>
                <_>
                  3 8 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>5.5495807901024818e-03</threshold>
            <left_val>0.4122591912746429</left_val>
            <right_val>-0.4940044879913330</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 2 7 -1.</_>
                <_>
                  1 1 1 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.1472150217741728e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3919258117675781</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 0 3 9 -1.</_>
                <_>
                  5 0 1 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.4546810463070869e-03</threshold>
            <left_val>-0.6919782757759094</left_val>
            <right_val>0.2610394060611725</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 10 3 3 -1.</_>
                <_>
                  14 11 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0114142503589392</threshold>
            <left_val>0.3236142098903656</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 11 2 2 -1.</_>
                <_>
                  12 11 1 1 2.</_>
                <_>
                  13 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1582579463720322e-03</threshold>
            <left_val>-0.3830499947071075</left_val>
            <right_val>0.2801598012447357</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 1 4 -1.</_>
                <_>
                  0 2 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.1077292775735259e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.3747107982635498</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 18 8 2 -1.</_>
                <_>
                  12 19 8 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1812780285254121e-03</threshold>
            <left_val>-0.1768521964550018</left_val>
            <right_val>0.3549810945987701</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 9 2 2 -1.</_>
                <_>
                  17 9 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>7.9117231070995331e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6968191862106323</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 10 4 2 -1.</_>
                <_>
                  17 11 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-9.0904926764778793e-05</threshold>
            <left_val>0.2075673937797546</left_val>
            <right_val>-0.4428209066390991</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 13 10 1 -1.</_>
                <_>
                  12 13 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.8638960793614388e-03</threshold>
            <left_val>-0.4136478900909424</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 7 4 3 -1.</_>
                <_>
                  9 7 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.2769990134984255e-03</threshold>
            <left_val>-0.2115702033042908</left_val>
            <right_val>0.3191956877708435</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 18 6 2 -1.</_>
                <_>
                  11 18 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.5440858490765095e-03</threshold>
            <left_val>-0.7549569010734558</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 18 6 2 -1.</_>
                <_>
                  10 18 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.4467269219458103e-03</threshold>
            <left_val>0.1322987973690033</left_val>
            <right_val>-0.6769589185714722</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 9 3 1 -1.</_>
                <_>
                  18 10 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.3641830300912261e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4216814935207367</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 7 2 11 -1.</_>
                <_>
                  18 7 1 11 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0138107798993587</threshold>
            <left_val>0.1571936011314392</left_val>
            <right_val>-0.6796516776084900</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 2 4 4 -1.</_>
                <_>
                  8 2 2 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0502656400203705</threshold>
            <left_node>1</left_node>
            <right_val>0.7436913847923279</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 6 2 3 -1.</_>
                <_>
                  7 6 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.7765119234099984e-05</threshold>
            <left_val>-0.3810234963893890</left_val>
            <right_val>0.1060535013675690</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 9 5 -1.</_>
                <_>
                  10 3 3 5 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.1466668993234634</threshold>
            <left_node>1</left_node>
            <right_val>0.5340983271598816</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 0 15 9 -1.</_>
                <_>
                  6 3 5 3 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.3042683005332947</threshold>
            <left_val>0.3778361082077026</left_val>
            <right_val>-0.2153462022542953</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 12 4 3 -1.</_>
                <_>
                  3 12 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.2244708854705095e-03</threshold>
            <left_val>0.2827424108982086</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 12 4 5 -1.</_>
                <_>
                  1 12 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7187190242111683e-03</threshold>
            <left_val>0.1067710965871811</left_val>
            <right_val>-0.4420411884784698</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 2 2 3 -1.</_>
                <_>
                  2 3 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-8.4115704521536827e-03</threshold>
            <left_val>-0.8355705142021179</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 13 6 1 -1.</_>
                <_>
                  4 13 3 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0232209190726280</threshold>
            <left_val>-0.5193390846252441</left_val>
            <right_val>0.1318164020776749</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 4 6 -1.</_>
                <_>
                  6 0 2 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.3912221230566502e-03</threshold>
            <left_val>-0.6855232119560242</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 17 2 1 -1.</_>
                <_>
                  2 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-3.0661540222354233e-04</threshold>
            <left_val>0.2219285070896149</left_val>
            <right_val>-0.2394503057003021</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 9 1 3 -1.</_>
                <_>
                  3 10 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.8742750398814678e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4721843898296356</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 2 6 9 -1.</_>
                <_>
                  2 2 2 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0282995402812958</threshold>
            <left_val>-0.6818671822547913</left_val>
            <right_val>0.1592379063367844</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 7 2 2 -1.</_>
                <_>
                  16 7 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>7.9352483153343201e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7313578128814697</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 2 6 4 -1.</_>
                <_>
                  9 2 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.7599940598011017e-03</threshold>
            <left_val>-0.6001471877098083</left_val>
            <right_val>0.1035033017396927</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 18 6 2 -1.</_>
                <_>
                  9 18 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.5426149629056454e-03</threshold>
            <left_val>-0.5936040878295898</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 14 6 4 -1.</_>
                <_>
                  3 14 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.8066290067508817e-03</threshold>
            <left_val>0.2553352117538452</left_val>
            <right_val>-0.1703643947839737</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 8 7 3 -1.</_>
                <_>
                  5 9 7 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-8.3993803709745407e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.2395361065864563</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 12 4 1 -1.</_>
                <_>
                  15 13 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.9515500171110034e-03</threshold>
            <left_val>0.3725241124629974</left_val>
            <right_val>-0.1298290044069290</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 12 3 2 -1.</_>
                <_>
                  5 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2850139066576958e-03</threshold>
            <left_val>0.5022721290588379</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 12 3 3 -1.</_>
                <_>
                  6 12 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.1910818330943584e-03</threshold>
            <left_val>0.4455165863037109</left_val>
            <right_val>-0.1630778014659882</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 2 2 2 -1.</_>
                <_>
                  19 2 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1659320443868637e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.3480907976627350</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 0 6 1 -1.</_>
                <_>
                  17 0 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.1016779355704784e-03</threshold>
            <left_val>0.3153137862682343</left_val>
            <right_val>-0.3471026122570038</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 0 3 3 -1.</_>
                <_>
                  18 1 1 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-9.1615924611687660e-03</threshold>
            <left_val>-0.6862319707870483</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 4 6 8 -1.</_>
                <_>
                  13 4 2 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0200365409255028</threshold>
            <left_val>-0.6899188160896301</left_val>
            <right_val>0.1296222060918808</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 12 3 2 -1.</_>
                <_>
                  8 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.7148448862135410e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.4774574041366577</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 0 3 2 -1.</_>
                <_>
                  16 1 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.2834159899502993e-03</threshold>
            <left_val>-0.0133445700630546</left_val>
            <right_val>-0.6179587841033936</right_val></_></_></trees>
      <stage_threshold>-1.8701590299606323</stage_threshold>
      <parent>12</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 14 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 11 9 4 -1.</_>
                <_>
                  8 11 3 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0328384712338448</threshold>
            <left_node>1</left_node>
            <right_val>-0.5198407173156738</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 9 1 6 -1.</_>
                <_>
                  12 11 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.5696408748626709e-03</threshold>
            <left_val>0.6369025111198425</left_val>
            <right_val>-0.1156217008829117</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 4 4 -1.</_>
                <_>
                  4 0 2 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0541258715093136</threshold>
            <left_node>1</left_node>
            <right_val>0.5034024715423584</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 1 11 12 -1.</_>
                <_>
                  5 5 11 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2700459957122803</threshold>
            <left_val>-0.3464067876338959</left_val>
            <right_val>0.3765150904655457</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 12 4 8 -1.</_>
                <_>
                  18 12 2 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.0261410437524319e-03</threshold>
            <left_val>-0.4104644060134888</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 14 2 6 -1.</_>
                <_>
                  18 17 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.1245660502463579e-03</threshold>
            <left_val>-0.4138219058513641</left_val>
            <right_val>0.3755074143409729</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 12 4 4 -1.</_>
                <_>
                  2 12 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.8708549905568361e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3782733082771301</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 7 6 4 -1.</_>
                <_>
                  5 8 6 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0149690099060535</threshold>
            <left_val>0.3994168043136597</left_val>
            <right_val>-0.2225451022386551</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 15 3 2 -1.</_>
                <_>
                  6 16 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.4136420581489801e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5466756820678711</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 16 3 1 -1.</_>
                <_>
                  7 17 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.3454260081052780e-03</threshold>
            <left_val>0.1661884039640427</left_val>
            <right_val>-0.6320394277572632</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 14 1 2 -1.</_>
                <_>
                  10 14 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.1689099483191967e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4497218132019043</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 7 3 3 -1.</_>
                <_>
                  3 8 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.8206984326243401e-03</threshold>
            <left_val>-0.5716611742973328</left_val>
            <right_val>0.1859999001026154</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 6 8 -1.</_>
                <_>
                  4 0 2 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0263242591172457</threshold>
            <left_val>-0.7804111242294312</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 5 6 3 -1.</_>
                <_>
                  4 5 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.1647548833861947e-04</threshold>
            <left_val>0.2310009002685547</left_val>
            <right_val>-0.2122412025928497</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 11 3 6 -1.</_>
                <_>
                  4 11 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.3702960461378098e-03</threshold>
            <left_val>0.2730421125888824</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 11 2 3 -1.</_>
                <_>
                  14 12 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-9.2874821275472641e-03</threshold>
            <left_val>0.2320079952478409</left_val>
            <right_val>-0.3460255861282349</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 17 4 3 -1.</_>
                <_>
                  12 17 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.9221060685813427e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6997262835502625</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 11 2 2 -1.</_>
                <_>
                  13 11 1 1 2.</_>
                <_>
                  14 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4097889652475715e-03</threshold>
            <left_val>0.4801935851573944</left_val>
            <right_val>-0.0426502004265785</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 11 2 2 -1.</_>
                <_>
                  13 11 1 1 2.</_>
                <_>
                  14 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.3326548812910914e-04</threshold>
            <left_node>1</left_node>
            <right_val>0.3770847916603088</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 2 5 6 -1.</_>
                <_>
                  8 5 5 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0568373091518879</threshold>
            <left_val>0.4637516140937805</left_val>
            <right_val>-0.2044157981872559</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 1 2 -1.</_>
                <_>
                  0 1 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.1405760031193495e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.2944777011871338</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 8 10 4 -1.</_>
                <_>
                  0 10 10 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0111477700993419</threshold>
            <left_val>0.3657920062541962</left_val>
            <right_val>-0.1610623002052307</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 11 3 1 -1.</_>
                <_>
                  18 12 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>8.0759642878547311e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.3876996934413910</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 18 2 2 -1.</_>
                <_>
                  8 18 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.7215589759871364e-03</threshold>
            <left_val>0.1779005974531174</left_val>
            <right_val>-0.5967379212379456</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 6 18 4 -1.</_>
                <_>
                  9 6 9 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0143056400120258</threshold>
            <left_val>-0.2888791859149933</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 12 12 8 -1.</_>
                <_>
                  6 12 4 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0388850085437298</threshold>
            <left_val>0.3649722933769226</left_val>
            <right_val>-0.1376271992921829</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 14 1 -1.</_>
                <_>
                  8 0 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.4479280002415180e-03</threshold>
            <left_val>0.1811084002256393</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 0 12 19 -1.</_>
                <_>
                  14 0 6 19 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.3016817867755890</threshold>
            <left_val>-0.3542549014091492</left_val>
            <right_val>0.4295836091041565</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 12 3 2 -1.</_>
                <_>
                  8 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.8582389932125807e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.5295780897140503</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 11 3 5 -1.</_>
                <_>
                  9 11 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.4091320335865021e-03</threshold>
            <left_val>-0.2123443037271500</left_val>
            <right_val>0.3142850995063782</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 18 3 2 -1.</_>
                <_>
                  8 18 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6597079811617732e-03</threshold>
            <left_val>-0.6334841847419739</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 13 2 2 -1.</_>
                <_>
                  5 13 1 1 2.</_>
                <_>
                  6 14 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.7804382201284170e-04</threshold>
            <left_val>-0.0553153008222580</left_val>
            <right_val>0.3938995897769928</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 9 3 1 -1.</_>
                <_>
                  17 10 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.0211800001561642e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4712730944156647</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 0 2 3 -1.</_>
                <_>
                  18 0 1 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.8409871309995651e-03</threshold>
            <left_val>-0.6406552791595459</left_val>
            <right_val>0.1486144065856934</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 2 15 6 -1.</_>
                <_>
                  4 4 15 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0472007617354393</threshold>
            <left_node>1</left_node>
            <right_val>0.4121640920639038</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 0 10 4 -1.</_>
                <_>
                  10 0 5 2 2.</_>
                <_>
                  15 2 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.9684080295264721e-03</threshold>
            <left_val>-0.3240430057048798</left_val>
            <right_val>0.1575596034526825</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 12 6 -1.</_>
                <_>
                  5 2 12 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0375299118459225</threshold>
            <left_node>1</left_node>
            <right_val>0.4132845997810364</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 1 8 6 -1.</_>
                <_>
                  12 1 4 3 2.</_>
                <_>
                  16 4 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0116650899872184</threshold>
            <left_val>0.2546750009059906</left_val>
            <right_val>-0.3130356073379517</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 3 2 1 -1.</_>
                <_>
                  1 3 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.8298257247079164e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.2721207141876221</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 7 2 4 -1.</_>
                <_>
                  16 7 1 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0153254298493266</threshold>
            <left_val>0.2294660955667496</left_val>
            <right_val>-0.6734570860862732</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 17 5 3 -1.</_>
                <_>
                  15 18 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.5185896605253220e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7111467123031616</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 12 6 8 -1.</_>
                <_>
                  8 12 2 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6828479021787643e-03</threshold>
            <left_val>0.1551170051097870</left_val>
            <right_val>-0.3544489145278931</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 12 2 2 -1.</_>
                <_>
                  6 12 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.3791749952360988e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.3691627085208893</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 12 4 6 -1.</_>
                <_>
                  14 12 2 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.3968368370551616e-05</threshold>
            <left_val>0.0591509304940701</left_val>
            <right_val>-0.4600771963596344</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 0 3 4 -1.</_>
                <_>
                  18 1 1 4 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>5.8259358629584312e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5498669743537903</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 0 4 10 -1.</_>
                <_>
                  5 0 2 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.1688696518540382e-03</threshold>
            <left_val>-0.5056741237640381</left_val>
            <right_val>0.1518967002630234</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 12 3 3 -1.</_>
                <_>
                  6 12 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.3251199163496494e-03</threshold>
            <left_val>0.3490481078624725</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 12 3 3 -1.</_>
                <_>
                  12 12 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.8669208772480488e-03</threshold>
            <left_val>0.5313856005668640</left_val>
            <right_val>-0.2141346931457520</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 2 1 3 -1.</_>
                <_>
                  2 3 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.3380381539463997e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7824826240539551</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 1 8 1 -1.</_>
                <_>
                  4 1 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.4176679328083992e-03</threshold>
            <left_val>0.1246078982949257</left_val>
            <right_val>-0.5529775023460388</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 3 18 12 -1.</_>
                <_>
                  6 7 6 4 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.5530973076820374</threshold>
            <left_node>1</left_node>
            <right_val>0.4657307863235474</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 18 6 2 -1.</_>
                <_>
                  15 18 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.3636389523744583e-03</threshold>
            <left_val>-0.3330905139446259</left_val>
            <right_val>0.0943800508975983</right_val></_></_></trees>
      <stage_threshold>-1.9807859659194946</stage_threshold>
      <parent>13</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 15 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 9 4 7 -1.</_>
                <_>
                  12 10 2 7 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0229342803359032</threshold>
            <left_node>1</left_node>
            <right_val>-0.4471629858016968</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 8 3 12 -1.</_>
                <_>
                  16 12 1 4 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0426658503711224</threshold>
            <left_val>0.5408589839935303</left_val>
            <right_val>-0.3358927965164185</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 10 7 3 -1.</_>
                <_>
                  6 11 7 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.8418388515710831e-03</threshold>
            <left_val>0.3995800018310547</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 9 10 3 -1.</_>
                <_>
                  4 10 10 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0119323497638106</threshold>
            <left_val>0.3421911895275116</left_val>
            <right_val>-0.4241695106029510</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 15 7 -1.</_>
                <_>
                  5 1 5 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0244370102882385</threshold>
            <left_node>1</left_node>
            <right_val>-0.3733735978603363</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 1 18 -1.</_>
                <_>
                  0 6 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.9987169913947582e-03</threshold>
            <left_val>0.4035832881927490</left_val>
            <right_val>-0.3519937098026276</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 13 2 4 -1.</_>
                <_>
                  8 14 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.8582950579002500e-03</threshold>
            <left_val>-0.4415811896324158</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 16 4 4 -1.</_>
                <_>
                  16 18 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.7540219016373158e-03</threshold>
            <left_val>-0.2872293889522552</left_val>
            <right_val>0.3385724127292633</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 10 4 8 -1.</_>
                <_>
                  2 10 2 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.4452530089765787e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3182198107242584</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 15 3 2 -1.</_>
                <_>
                  3 16 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.9277489781379700e-03</threshold>
            <left_val>-0.6507351994514465</left_val>
            <right_val>0.2710922062397003</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 17 2 1 -1.</_>
                <_>
                  2 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.2391789641696960e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.3346720039844513</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 10 2 8 -1.</_>
                <_>
                  18 10 2 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0733271390199661</threshold>
            <left_val>-0.5964624881744385</left_val>
            <right_val>0.2286181002855301</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 11 18 3 -1.</_>
                <_>
                  6 12 6 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0839647501707077</threshold>
            <left_node>1</left_node>
            <right_val>-0.2252518981695175</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 10 4 2 -1.</_>
                <_>
                  16 11 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-8.1644707825034857e-04</threshold>
            <left_val>0.3821364939212799</left_val>
            <right_val>-0.3341045081615448</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 1 5 4 -1.</_>
                <_>
                  9 3 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0152077795937657</threshold>
            <left_val>0.3074269890785217</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 1 7 6 -1.</_>
                <_>
                  6 4 7 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0468947887420654</threshold>
            <left_val>-0.3883388936519623</left_val>
            <right_val>0.2317751944065094</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 3 8 6 -1.</_>
                <_>
                  3 6 8 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1039844006299973</threshold>
            <left_val>0.7132114171981812</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 1 4 2 -1.</_>
                <_>
                  18 1 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.9815339259803295e-03</threshold>
            <left_val>-0.2331019937992096</left_val>
            <right_val>0.2924784123897552</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 12 2 3 -1.</_>
                <_>
                  18 13 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5737080723047256e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5501734018325806</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 6 2 8 -1.</_>
                <_>
                  17 6 1 4 2.</_>
                <_>
                  18 10 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.1035291552543640e-04</threshold>
            <left_val>-0.1822893023490906</left_val>
            <right_val>0.2837032079696655</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 5 3 4 -1.</_>
                <_>
                  18 6 1 4 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>6.4211348071694374e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4858197867870331</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 9 4 8 -1.</_>
                <_>
                  0 11 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.8243819512426853e-03</threshold>
            <left_val>0.2460819035768509</left_val>
            <right_val>-0.2156502008438110</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 6 3 8 -1.</_>
                <_>
                  0 10 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0400436297059059</threshold>
            <left_val>-0.6388055086135864</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 11 2 2 -1.</_>
                <_>
                  14 11 1 1 2.</_>
                <_>
                  15 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.4683427121490240e-04</threshold>
            <left_val>-0.0604355894029140</left_val>
            <right_val>0.4371112883090973</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 11 3 3 -1.</_>
                <_>
                  14 12 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0129645802080631</threshold>
            <left_node>1</left_node>
            <right_val>0.5949506163597107</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 12 5 2 -1.</_>
                <_>
                  14 13 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2524749510921538e-04</threshold>
            <left_val>0.0868314728140831</left_val>
            <right_val>-0.3636232018470764</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 12 1 2 -1.</_>
                <_>
                  19 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7258729785680771e-03</threshold>
            <left_val>-0.6470772027969360</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 0 4 7 -1.</_>
                <_>
                  7 0 2 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.2289421223104000e-03</threshold>
            <left_val>-0.6877536773681641</left_val>
            <right_val>0.1383872032165527</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 12 3 2 -1.</_>
                <_>
                  12 13 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5079259648919106e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.3065930902957916</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 13 4 2 -1.</_>
                <_>
                  12 13 2 1 2.</_>
                <_>
                  14 14 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.9473560387268662e-03</threshold>
            <left_val>0.2296776026487350</left_val>
            <right_val>-0.3473764955997467</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 18 4 2 -1.</_>
                <_>
                  16 19 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.4747111648321152e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6519178748130798</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 18 1 2 -1.</_>
                <_>
                  14 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0328400094294921e-04</threshold>
            <left_val>-0.2072588950395584</left_val>
            <right_val>0.2240213006734848</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 3 2 -1.</_>
                <_>
                  17 1 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.8996885567903519e-03</threshold>
            <left_val>-0.7247917056083679</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 0 4 2 -1.</_>
                <_>
                  17 1 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.2833909392356873e-03</threshold>
            <left_val>0.1395497024059296</left_val>
            <right_val>-0.4308606088161469</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 13 2 2 -1.</_>
                <_>
                  12 13 1 1 2.</_>
                <_>
                  13 14 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.3452741596847773e-04</threshold>
            <left_node>1</left_node>
            <right_val>0.2979263961315155</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 10 4 2 -1.</_>
                <_>
                  7 10 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.4966621100902557e-03</threshold>
            <left_val>-0.5620539188385010</left_val>
            <right_val>-0.0296081192791462</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 3 1 3 -1.</_>
                <_>
                  2 4 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.1408690847456455e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6132214069366455</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 4 2 3 -1.</_>
                <_>
                  2 5 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.0443639047443867e-03</threshold>
            <left_val>-0.5306010246276855</left_val>
            <right_val>0.1250745952129364</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 0 16 6 -1.</_>
                <_>
                  3 2 16 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0459648706018925</threshold>
            <left_node>1</left_node>
            <right_val>0.3818871974945068</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 2 2 5 -1.</_>
                <_>
                  12 2 1 5 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.3749699145555496e-03</threshold>
            <left_val>0.1408901065587997</left_val>
            <right_val>-0.3553569018840790</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 1 3 -1.</_>
                <_>
                  3 1 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.9262059833854437e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6088665723800659</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 12 2 2 -1.</_>
                <_>
                  13 12 1 1 2.</_>
                <_>
                  14 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.2230368601158261e-04</threshold>
            <left_val>-0.0714415684342384</left_val>
            <right_val>0.3627525866031647</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 17 4 3 -1.</_>
                <_>
                  6 17 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.4181118719279766e-03</threshold>
            <left_val>-0.7645800709724426</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 13 3 3 -1.</_>
                <_>
                  17 14 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.3349149636924267e-03</threshold>
            <left_val>0.1124641001224518</left_val>
            <right_val>-0.5455384850502014</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 2 8 -1.</_>
                <_>
                  0 12 1 4 2.</_>
                <_>
                  1 16 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.6483018882572651e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.2354231029748917</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 16 1 3 -1.</_>
                <_>
                  3 17 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.0814110282808542e-03</threshold>
            <left_val>0.1442230045795441</left_val>
            <right_val>-0.3440195918083191</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 2 1 2 -1.</_>
                <_>
                  0 3 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.4296739108394831e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.2860746085643768</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 2 4 7 -1.</_>
                <_>
                  11 2 2 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.5393581278622150e-03</threshold>
            <left_val>0.1934528946876526</left_val>
            <right_val>-0.5054942965507507</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 1 6 9 -1.</_>
                <_>
                  2 4 6 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0337030999362469</threshold>
            <left_node>1</left_node>
            <right_val>0.3830255866050720</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 4 2 2 -1.</_>
                <_>
                  2 4 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2178930046502501e-04</threshold>
            <left_val>0.0664141774177551</left_val>
            <right_val>-0.4853005111217499</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 12 2 2 -1.</_>
                <_>
                  13 12 1 1 2.</_>
                <_>
                  14 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7803770024329424e-03</threshold>
            <left_val>0.4411354959011078</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 0 2 1 -1.</_>
                <_>
                  19 0 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.6019638577708974e-05</threshold>
            <left_val>0.1239674985408783</left_val>
            <right_val>-0.2629227042198181</right_val></_></_></trees>
      <stage_threshold>-1.9697020053863525</stage_threshold>
      <parent>14</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 16 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 13 3 1 -1.</_>
                <_>
                  5 13 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.1982790678739548e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.5420842170715332</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 13 4 1 -1.</_>
                <_>
                  7 13 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5240450156852603e-03</threshold>
            <left_val>0.0827848389744759</left_val>
            <right_val>-0.5016483068466187</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 10 6 3 -1.</_>
                <_>
                  6 11 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0122844297438860</threshold>
            <left_val>0.4417493939399719</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 9 4 3 -1.</_>
                <_>
                  7 10 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.3555448800325394e-03</threshold>
            <left_val>0.3586339950561523</left_val>
            <right_val>-0.3625485897064209</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 4 3 -1.</_>
                <_>
                  6 0 2 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0413578003644943</threshold>
            <left_node>1</left_node>
            <right_val>0.4785881042480469</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 15 5 2 -1.</_>
                <_>
                  15 16 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.2308749612420797e-03</threshold>
            <left_val>-0.6039034724235535</left_val>
            <right_val>-8.7199418339878321e-04</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 18 12 -1.</_>
                <_>
                  6 12 6 4 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.5416054129600525</threshold>
            <left_node>1</left_node>
            <right_val>-0.3253665864467621</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 6 14 4 -1.</_>
                <_>
                  8 6 7 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.9009458422660828e-03</threshold>
            <left_val>-0.3641510009765625</left_val>
            <right_val>0.4050160050392151</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 11 6 3 -1.</_>
                <_>
                  2 12 6 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-2.7236728928983212e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.2764418125152588</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 9 1 3 -1.</_>
                <_>
                  4 10 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.1041880827397108e-03</threshold>
            <left_val>0.3406811952590942</left_val>
            <right_val>-0.4192248880863190</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 10 3 3 -1.</_>
                <_>
                  18 11 1 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.2688159476965666e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5452076792716980</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 11 1 4 -1.</_>
                <_>
                  16 12 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.2881062254309654e-03</threshold>
            <left_val>0.3006008863449097</left_val>
            <right_val>-0.1523319035768509</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 12 9 -1.</_>
                <_>
                  4 0 6 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.8890449106693268e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3766582012176514</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 3 4 5 -1.</_>
                <_>
                  10 3 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0922110676765442e-03</threshold>
            <left_val>0.2180331945419312</left_val>
            <right_val>-0.5712652206420898</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 8 6 3 -1.</_>
                <_>
                  7 9 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0944731123745441e-03</threshold>
            <left_val>0.5192192196846008</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 1 9 6 -1.</_>
                <_>
                  7 3 9 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0254318900406361</threshold>
            <left_val>-0.2126024961471558</left_val>
            <right_val>0.3056620061397552</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 2 2 -1.</_>
                <_>
                  0 2 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.7461907747201622e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.3340674936771393</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 8 3 5 -1.</_>
                <_>
                  14 9 1 5 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-8.5350889712572098e-03</threshold>
            <left_val>0.3504346013069153</left_val>
            <right_val>-0.0903848335146904</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 16 3 1 -1.</_>
                <_>
                  4 17 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.1117807850241661e-03</threshold>
            <left_val>-0.6968370079994202</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 1 4 7 -1.</_>
                <_>
                  12 1 2 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.3964081928133965e-03</threshold>
            <left_val>0.1154263988137245</left_val>
            <right_val>-0.6664537191390991</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 13 2 2 -1.</_>
                <_>
                  11 13 1 1 2.</_>
                <_>
                  12 14 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.8322751000523567e-04</threshold>
            <left_node>1</left_node>
            <right_val>0.3569537997245789</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 14 3 1 -1.</_>
                <_>
                  13 14 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.5737968068569899e-04</threshold>
            <left_val>0.2308111041784286</left_val>
            <right_val>-0.2886263132095337</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 2 3 1 -1.</_>
                <_>
                  18 3 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.8798289131373167e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5992326736450195</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 2 6 6 -1.</_>
                <_>
                  14 2 3 3 2.</_>
                <_>
                  17 5 3 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.7164517715573311e-03</threshold>
            <left_val>0.3607490062713623</left_val>
            <right_val>-0.0818276181817055</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 16 8 4 -1.</_>
                <_>
                  12 18 8 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.7285129074007273e-03</threshold>
            <left_val>-0.3773201107978821</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 11 3 3 -1.</_>
                <_>
                  6 12 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0131611097604036</threshold>
            <left_val>0.6702303886413574</left_val>
            <right_val>0.0151145495474339</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 3 8 6 -1.</_>
                <_>
                  4 5 8 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0389661304652691</threshold>
            <left_node>1</left_node>
            <right_val>-0.3125221133232117</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 8 3 8 -1.</_>
                <_>
                  1 10 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.7413699105381966e-03</threshold>
            <left_val>0.3394747972488403</left_val>
            <right_val>-0.1601140946149826</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 8 6 -1.</_>
                <_>
                  9 2 4 6 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.1253833025693893</threshold>
            <left_node>1</left_node>
            <right_val>0.7372115254402161</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 2 7 6 -1.</_>
                <_>
                  5 5 7 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0972431227564812</threshold>
            <left_val>0.5028898119926453</left_val>
            <right_val>-0.1328437030315399</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 13 3 1 -1.</_>
                <_>
                  11 13 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.0128490868955851e-03</threshold>
            <left_val>0.4136789143085480</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 12 4 2 -1.</_>
                <_>
                  12 12 2 1 2.</_>
                <_>
                  14 13 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.5349070094525814e-03</threshold>
            <left_val>-0.1592327058315277</left_val>
            <right_val>0.4405657947063446</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 1 14 19 -1.</_>
                <_>
                  13 1 7 19 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.4484654068946838</threshold>
            <left_node>1</left_node>
            <right_val>0.5942366123199463</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 9 14 1 -1.</_>
                <_>
                  13 9 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0103877801448107</threshold>
            <left_val>0.3039911985397339</left_val>
            <right_val>-0.1828735023736954</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 0 2 1 -1.</_>
                <_>
                  18 0 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.4210389927029610e-03</threshold>
            <left_val>-0.4536106884479523</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 0 3 1 -1.</_>
                <_>
                  16 1 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.6446070298552513e-03</threshold>
            <left_val>0.1576682031154633</left_val>
            <right_val>-0.6260883808135986</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 7 2 3 -1.</_>
                <_>
                  4 8 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.2253630924969912e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4141024053096771</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 12 3 3 -1.</_>
                <_>
                  14 13 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>9.8893349058926105e-04</threshold>
            <left_val>-0.1075780019164085</left_val>
            <right_val>0.3115688860416412</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 17 4 2 -1.</_>
                <_>
                  11 17 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.7107829228043556e-03</threshold>
            <left_val>-0.7535281777381897</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 12 3 3 -1.</_>
                <_>
                  9 13 1 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.9264871999621391e-03</threshold>
            <left_val>0.2746442854404449</left_val>
            <right_val>-0.1172894984483719</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 1 7 6 -1.</_>
                <_>
                  4 3 7 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0379427708685398</threshold>
            <left_val>0.2693654894828796</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 0 6 6 -1.</_>
                <_>
                  11 2 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0134864598512650</threshold>
            <left_val>-0.3153286874294281</left_val>
            <right_val>0.2578544020652771</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 1 4 -1.</_>
                <_>
                  0 2 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.7866458985954523e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6843165755271912</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 5 4 4 -1.</_>
                <_>
                  8 5 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.2895719632506371e-03</threshold>
            <left_val>0.1294910013675690</left_val>
            <right_val>-0.4447514116764069</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 1 3 -1.</_>
                <_>
                  1 1 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.7910100286826491e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5623742938041687</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 3 4 2 -1.</_>
                <_>
                  9 4 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.3694170415401459e-03</threshold>
            <left_val>-0.0619367696344852</left_val>
            <right_val>0.3679428994655609</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 13 2 5 -1.</_>
                <_>
                  19 13 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.5897632157430053e-04</threshold>
            <left_val>-0.2770572006702423</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 11 3 6 -1.</_>
                <_>
                  3 11 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.2603838917566463e-05</threshold>
            <left_val>0.2742677927017212</left_val>
            <right_val>-0.2236953973770142</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 5 2 12 -1.</_>
                <_>
                  0 9 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0601757206022739</threshold>
            <left_val>-0.7417491078376770</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 10 8 5 -1.</_>
                <_>
                  15 10 4 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0212176106870174</threshold>
            <left_val>-0.4503475129604340</left_val>
            <right_val>0.1142600029706955</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 11 4 2 -1.</_>
                <_>
                  16 12 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-2.2632910404354334e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3053858876228333</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 8 4 2 -1.</_>
                <_>
                  16 9 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>6.0313078574836254e-03</threshold>
            <left_val>0.2056266069412231</left_val>
            <right_val>-0.4068979918956757</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 13 2 1 -1.</_>
                <_>
                  6 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.7578482665121555e-04</threshold>
            <left_node>1</left_node>
            <right_val>0.3509874939918518</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 13 2 2 -1.</_>
                <_>
                  13 13 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.3677162658423185e-04</threshold>
            <left_val>0.2161615937948227</left_val>
            <right_val>-0.2441577017307281</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 12 8 8 -1.</_>
                <_>
                  13 12 4 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0376265682280064</threshold>
            <left_val>-0.5911368131637573</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 0 6 10 -1.</_>
                <_>
                  5 0 2 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.4729812070727348e-03</threshold>
            <left_val>0.1579227000474930</left_val>
            <right_val>-0.3222627937793732</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 14 2 2 -1.</_>
                <_>
                  6 14 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.1853301487863064e-03</threshold>
            <left_val>-0.5951905250549316</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 5 19 4 -1.</_>
                <_>
                  0 7 19 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0405202284455299</threshold>
            <left_val>-0.0666884630918503</left_val>
            <right_val>0.3403024971485138</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 4 3 2 -1.</_>
                <_>
                  18 5 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.1968388035893440e-03</threshold>
            <left_val>-0.6728746294975281</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 3 3 4 -1.</_>
                <_>
                  18 4 1 4 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0103115299716592</threshold>
            <left_val>0.1068323999643326</left_val>
            <right_val>-0.5482596755027771</right_val></_></_></trees>
      <stage_threshold>-2.0330519676208496</stage_threshold>
      <parent>15</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 17 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 13 8 2 -1.</_>
                <_>
                  7 13 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0193205196410418</threshold>
            <left_node>1</left_node>
            <right_val>-0.3871257007122040</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 2 8 -1.</_>
                <_>
                  0 4 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0151264602318406</threshold>
            <left_val>0.6446818113327026</left_val>
            <right_val>-0.1272711008787155</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 9 15 6 -1.</_>
                <_>
                  0 11 15 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0601826906204224</threshold>
            <left_node>1</left_node>
            <right_val>-0.3081910908222198</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 14 2 1 -1.</_>
                <_>
                  18 14 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.3576049823313951e-03</threshold>
            <left_val>0.4802188873291016</left_val>
            <right_val>-0.3342868089675903</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 4 8 -1.</_>
                <_>
                  2 0 2 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.6930771097540855e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3316608071327209</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 13 6 2 -1.</_>
                <_>
                  2 13 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.0942036584019661e-03</threshold>
            <left_val>0.4751748144626617</left_val>
            <right_val>-0.0747615620493889</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 18 3 2 -1.</_>
                <_>
                  3 19 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.8413332337513566e-04</threshold>
            <left_val>-0.3574196994304657</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 11 15 6 -1.</_>
                <_>
                  7 13 5 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1152058988809586</threshold>
            <left_val>0.2610509097576141</left_val>
            <right_val>-0.3177380859851837</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 14 3 3 -1.</_>
                <_>
                  8 15 1 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-9.1124046593904495e-03</threshold>
            <left_val>-0.5854070782661438</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 8 2 2 -1.</_>
                <_>
                  8 8 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.4891068430151790e-05</threshold>
            <left_val>-0.2298189997673035</left_val>
            <right_val>0.2348290979862213</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 9 6 3 -1.</_>
                <_>
                  6 10 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.5622539520263672e-03</threshold>
            <left_val>0.3915528059005737</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 8 7 3 -1.</_>
                <_>
                  5 9 7 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.2032606005668640e-03</threshold>
            <left_val>0.4317995011806488</left_val>
            <right_val>-0.2317329049110413</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 9 3 1 -1.</_>
                <_>
                  18 10 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.0035760030150414e-03</threshold>
            <left_val>-0.5870047807693481</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 9 3 2 -1.</_>
                <_>
                  18 10 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.5406230706721544e-03</threshold>
            <left_val>0.1799003034830093</left_val>
            <right_val>-0.4168156981468201</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 9 1 3 -1.</_>
                <_>
                  11 10 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.9435470458120108e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.3034000992774963</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 11 2 2 -1.</_>
                <_>
                  12 11 1 1 2.</_>
                <_>
                  13 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.4362342022359371e-04</threshold>
            <left_val>-0.3066104054450989</left_val>
            <right_val>0.2364699989557266</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 6 4 5 -1.</_>
                <_>
                  4 6 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.3103519603610039e-03</threshold>
            <left_val>-0.5630481839179993</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 6 4 3 -1.</_>
                <_>
                  6 6 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.5526719875633717e-03</threshold>
            <left_val>-0.5569577217102051</left_val>
            <right_val>0.1502279043197632</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 3 1 6 -1.</_>
                <_>
                  0 5 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.1414401754736900e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6762663722038269</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 12 2 2 -1.</_>
                <_>
                  14 12 1 1 2.</_>
                <_>
                  15 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.1435860069468617e-03</threshold>
            <left_val>0.3787387907505035</left_val>
            <right_val>-0.0744428932666779</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 16 3 3 -1.</_>
                <_>
                  4 16 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.1177429482340813e-03</threshold>
            <left_val>-0.6256858706474304</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 1 14 4 -1.</_>
                <_>
                  3 3 14 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0774156227707863</threshold>
            <left_val>0.3983941078186035</left_val>
            <right_val>-0.0552623197436333</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 14 8 -1.</_>
                <_>
                  6 0 7 4 2.</_>
                <_>
                  13 4 7 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0392529889941216</threshold>
            <left_val>0.3409483134746552</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 0 4 8 -1.</_>
                <_>
                  4 2 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0220499709248543</threshold>
            <left_val>-0.2441371977329254</left_val>
            <right_val>0.4305087029933929</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 0 8 1 -1.</_>
                <_>
                  13 0 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2205871064215899e-03</threshold>
            <left_val>0.2830972075462341</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 1 6 1 -1.</_>
                <_>
                  17 1 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.8649640735238791e-03</threshold>
            <left_val>-0.3540188074111938</left_val>
            <right_val>0.2105457037687302</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 18 2 2 -1.</_>
                <_>
                  18 19 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.8806730521610007e-05</threshold>
            <left_val>-0.2701404094696045</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 16 2 2 -1.</_>
                <_>
                  5 16 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.6595021635293961e-03</threshold>
            <left_val>-0.5931348204612732</left_val>
            <right_val>0.2189286947250366</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 8 11 3 -1.</_>
                <_>
                  2 9 11 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0169316008687019</threshold>
            <left_val>-0.1127962023019791</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 8 2 3 -1.</_>
                <_>
                  1 9 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.7026639804244041e-03</threshold>
            <left_val>0.4921221137046814</left_val>
            <right_val>-0.3970288038253784</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 12 2 5 -1.</_>
                <_>
                  19 12 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.7478819936513901e-03</threshold>
            <left_val>-0.2233936935663223</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 16 1 3 -1.</_>
                <_>
                  18 17 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-2.0893230102956295e-03</threshold>
            <left_val>-0.4315781891345978</left_val>
            <right_val>0.2537313997745514</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 9 2 2 -1.</_>
                <_>
                  14 9 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0115348501130939</threshold>
            <left_node>1</left_node>
            <right_val>-0.7066854238510132</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 11 2 2 -1.</_>
                <_>
                  13 11 1 1 2.</_>
                <_>
                  14 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.7350117973983288e-04</threshold>
            <left_val>-0.0725091323256493</left_val>
            <right_val>0.3997502923011780</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 12 4 4 -1.</_>
                <_>
                  14 12 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.2836421895772219e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.2356764972209930</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 11 1 3 -1.</_>
                <_>
                  19 12 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.2666890397667885e-03</threshold>
            <left_val>0.2258238941431046</left_val>
            <right_val>-0.4231734871864319</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 1 4 -1.</_>
                <_>
                  0 3 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.4794021677225828e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.2830702960491180</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 20 20 -1.</_>
                <_>
                  0 0 10 10 2.</_>
                <_>
                  10 10 10 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.3621244132518768</threshold>
            <left_val>0.1672423928976059</left_val>
            <right_val>-0.7682694792747498</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 12 3 3 -1.</_>
                <_>
                  10 13 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.9437649752944708e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.2722941935062408</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 17 1 2 -1.</_>
                <_>
                  16 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.1159680113196373e-03</threshold>
            <left_val>-0.6421130895614624</left_val>
            <right_val>0.1881023049354553</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 10 4 2 -1.</_>
                <_>
                  13 10 2 1 2.</_>
                <_>
                  15 11 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.3254039697349072e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.2851688861846924</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 11 2 2 -1.</_>
                <_>
                  15 11 1 1 2.</_>
                <_>
                  16 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4815620379522443e-03</threshold>
            <left_val>0.4257420897483826</left_val>
            <right_val>-0.2111361026763916</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 10 3 6 -1.</_>
                <_>
                  3 10 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.6233296820428222e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.2820585072040558</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 6 9 -1.</_>
                <_>
                  2 0 2 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0337564311921597</threshold>
            <left_val>-0.8180304169654846</left_val>
            <right_val>0.1705366969108582</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 17 2 1 -1.</_>
                <_>
                  8 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-9.4350927975028753e-04</threshold>
            <left_val>0.1527314037084579</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 18 8 1 -1.</_>
                <_>
                  8 18 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0650219628587365e-03</threshold>
            <left_val>-0.4265049099922180</left_val>
            <right_val>0.1523593962192535</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 11 1 4 -1.</_>
                <_>
                  3 12 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.2905279872938991e-03</threshold>
            <left_val>0.1736539006233215</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 11 3 3 -1.</_>
                <_>
                  6 12 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>9.6549028530716896e-03</threshold>
            <left_val>-0.3972159922122955</left_val>
            <right_val>0.1795317977666855</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 18 4 1 -1.</_>
                <_>
                  10 18 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.3434770517051220e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6960932016372681</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 19 2 1 -1.</_>
                <_>
                  1 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.5220007197931409e-04</threshold>
            <left_val>-0.0722587704658508</left_val>
            <right_val>0.3449329137802124</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 6 3 5 -1.</_>
                <_>
                  12 6 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.5795350559055805e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4807066917419434</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 0 12 20 -1.</_>
                <_>
                  8 0 6 10 2.</_>
                <_>
                  14 10 6 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0105854999274015</threshold>
            <left_val>-0.3297558128833771</left_val>
            <right_val>0.1468691974878311</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 1 4 -1.</_>
                <_>
                  3 1 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.5636040847748518e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6141502261161804</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 14 16 4 -1.</_>
                <_>
                  8 14 8 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1029829010367393</threshold>
            <left_val>-0.7236648201942444</left_val>
            <right_val>0.0844470709562302</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 9 5 4 -1.</_>
                <_>
                  6 10 5 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0296057593077421</threshold>
            <left_val>0.4711360931396484</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 12 6 2 -1.</_>
                <_>
                  5 12 3 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0345805995166302</threshold>
            <left_val>-0.4312899112701416</left_val>
            <right_val>0.0246234703809023</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 14 4 1 -1.</_>
                <_>
                  1 14 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.7923368401825428e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4627079963684082</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 10 1 3 -1.</_>
                <_>
                  3 11 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.7058040248230100e-03</threshold>
            <left_val>0.1473857015371323</left_val>
            <right_val>-0.3781889081001282</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 10 3 9 -1.</_>
                <_>
                  4 10 1 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.3174119889736176e-03</threshold>
            <left_val>0.2792986035346985</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 11 3 4 -1.</_>
                <_>
                  5 11 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7022279789671302e-03</threshold>
            <left_val>0.2632699012756348</left_val>
            <right_val>-0.2512921094894409</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 12 3 2 -1.</_>
                <_>
                  6 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.1695342669263482e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.1285964995622635</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 12 3 2 -1.</_>
                <_>
                  8 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4184829778969288e-03</threshold>
            <left_val>0.5885540246963501</left_val>
            <right_val>-0.0500851683318615</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 2 12 6 -1.</_>
                <_>
                  5 2 4 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0104785999283195</threshold>
            <left_val>0.1473290026187897</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 0 8 3 -1.</_>
                <_>
                  11 2 4 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0319819115102291</threshold>
            <left_val>-0.4129954874515533</left_val>
            <right_val>0.3444204926490784</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 1 6 2 -1.</_>
                <_>
                  8 1 3 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0455438494682312</threshold>
            <left_node>1</left_node>
            <right_val>0.4884208142757416</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 4 15 9 -1.</_>
                <_>
                  4 7 15 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0235740095376968</threshold>
            <left_val>-0.4638321995735168</left_val>
            <right_val>0.0374437682330608</right_val></_></_></trees>
      <stage_threshold>-1.9516259431838989</stage_threshold>
      <parent>16</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 18 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 10 8 6 -1.</_>
                <_>
                  7 10 4 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0323471315205097</threshold>
            <left_node>1</left_node>
            <right_val>-0.4115316867828369</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 8 9 9 -1.</_>
                <_>
                  11 11 9 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0748554319143295</threshold>
            <left_val>0.5440948009490967</left_val>
            <right_val>-0.2104308009147644</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 6 4 -1.</_>
                <_>
                  9 2 2 4 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0591647997498512</threshold>
            <left_val>0.4694552123546600</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 11 6 3 -1.</_>
                <_>
                  2 12 6 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.0734709948301315e-03</threshold>
            <left_val>0.0809333473443985</left_val>
            <right_val>-0.4043686985969543</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 12 4 3 -1.</_>
                <_>
                  18 12 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.6304411739110947e-03</threshold>
            <left_val>-0.3194395005702972</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 10 2 10 -1.</_>
                <_>
                  10 15 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0228042807430029</threshold>
            <left_val>-0.3527761101722717</left_val>
            <right_val>0.3635815978050232</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 7 3 4 -1.</_>
                <_>
                  4 8 3 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.4148059785366058e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4213989973068237</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 9 6 1 -1.</_>
                <_>
                  3 11 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.0696629807353020e-03</threshold>
            <left_val>0.2819094061851501</left_val>
            <right_val>-0.2572798132896423</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 1 6 -1.</_>
                <_>
                  0 3 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.3271780703216791e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3338018059730530</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 10 10 2 -1.</_>
                <_>
                  8 10 5 1 2.</_>
                <_>
                  13 11 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0123812397941947</threshold>
            <left_val>0.0258311200886965</left_val>
            <right_val>0.5820063948631287</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 2 5 6 -1.</_>
                <_>
                  5 5 5 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0785619020462036</threshold>
            <left_val>0.5708081722259521</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 1 6 1 -1.</_>
                <_>
                  6 1 3 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.6863910071551800e-03</threshold>
            <left_val>0.1909738034009933</left_val>
            <right_val>-0.2474946975708008</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 3 1 12 -1.</_>
                <_>
                  0 7 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.9404830895364285e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3529588878154755</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 7 2 1 -1.</_>
                <_>
                  1 7 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0624810177832842e-05</threshold>
            <left_val>0.2843806147575378</left_val>
            <right_val>-0.1646942049264908</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 5 1 3 -1.</_>
                <_>
                  2 6 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-2.2568539716303349e-03</threshold>
            <left_val>-0.4618921875953674</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 12 2 3 -1.</_>
                <_>
                  10 13 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-3.5595949739217758e-03</threshold>
            <left_val>0.2452594041824341</left_val>
            <right_val>-0.1898497939109802</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 12 3 3 -1.</_>
                <_>
                  11 12 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0113100074231625e-03</threshold>
            <left_val>0.3059439063072205</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 11 3 3 -1.</_>
                <_>
                  10 12 1 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.2748990021646023e-03</threshold>
            <left_val>0.1471614986658096</left_val>
            <right_val>-0.3326522111892700</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 17 4 2 -1.</_>
                <_>
                  7 17 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5835279375314713e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7485389113426208</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 18 6 2 -1.</_>
                <_>
                  15 18 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.2576550729572773e-03</threshold>
            <left_val>-0.1494961977005005</left_val>
            <right_val>0.2629367113113403</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 17 2 1 -1.</_>
                <_>
                  3 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-2.6957978843711317e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.2946836054325104</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 15 4 1 -1.</_>
                <_>
                  2 16 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.4593680649995804e-03</threshold>
            <left_val>-0.4590528905391693</left_val>
            <right_val>0.2223538011312485</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 0 2 2 -1.</_>
                <_>
                  18 1 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.2841650061309338e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6381593942642212</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 0 1 3 -1.</_>
                <_>
                  19 1 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.7595718428492546e-04</threshold>
            <left_val>-0.3175694048404694</left_val>
            <right_val>0.1490307003259659</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 11 3 2 -1.</_>
                <_>
                  16 11 3 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>6.1428439803421497e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.2418702989816666</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 12 2 3 -1.</_>
                <_>
                  15 13 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.7392068877816200e-03</threshold>
            <left_val>-0.3148753941059113</left_val>
            <right_val>0.2358912974596024</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 0 8 1 -1.</_>
                <_>
                  16 0 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.0209311041980982e-03</threshold>
            <left_val>0.2538956105709076</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 1 9 6 -1.</_>
                <_>
                  2 4 9 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0268921405076981</threshold>
            <left_val>-0.3439103960990906</left_val>
            <right_val>0.2301076054573059</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 1 3 2 -1.</_>
                <_>
                  17 1 3 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0146710602566600</threshold>
            <left_node>1</left_node>
            <right_val>0.5951753854751587</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 5 6 4 -1.</_>
                <_>
                  7 6 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0124441199004650</threshold>
            <left_val>0.3733592927455902</left_val>
            <right_val>-0.1454063951969147</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 6 6 2 -1.</_>
                <_>
                  7 6 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.0527220331132412e-03</threshold>
            <left_val>-0.2113502025604248</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 4 6 6 -1.</_>
                <_>
                  13 4 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0170889906585217</threshold>
            <left_val>-0.7251623272895813</left_val>
            <right_val>0.2335873991250992</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 7 9 3 -1.</_>
                <_>
                  5 8 9 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.8585523664951324e-03</threshold>
            <left_val>0.4539042115211487</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 8 9 3 -1.</_>
                <_>
                  5 9 9 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0105411903932691</threshold>
            <left_val>0.3550005853176117</left_val>
            <right_val>-0.1711850017309189</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 4 3 -1.</_>
                <_>
                  2 0 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.0034228004515171e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7043396234512329</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 9 5 4 -1.</_>
                <_>
                  9 10 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0118891401216388</threshold>
            <left_val>0.4043655991554260</left_val>
            <right_val>-0.0462636202573776</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 6 7 -1.</_>
                <_>
                  3 0 2 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0206857006996870</threshold>
            <left_val>-0.6434760093688965</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 9 3 2 -1.</_>
                <_>
                  17 10 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.9243928194046021e-03</threshold>
            <left_val>-0.5363292098045349</left_val>
            <right_val>0.1100298985838890</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 12 2 2 -1.</_>
                <_>
                  14 12 1 1 2.</_>
                <_>
                  15 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.2431150535121560e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.4122002124786377</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 14 1 -1.</_>
                <_>
                  7 0 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.2312019504606724e-03</threshold>
            <left_val>0.0798876583576202</left_val>
            <right_val>-0.3092674016952515</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 11 2 2 -1.</_>
                <_>
                  15 11 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>9.8197339102625847e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6097676157951355</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 14 12 4 -1.</_>
                <_>
                  3 14 6 2 2.</_>
                <_>
                  9 16 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0454554110765457</threshold>
            <left_val>0.1062114015221596</left_val>
            <right_val>-0.6468737125396729</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 2 1 3 -1.</_>
                <_>
                  4 3 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.6892758905887604e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4912298917770386</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 12 3 2 -1.</_>
                <_>
                  9 13 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.5172710409387946e-03</threshold>
            <left_val>0.1757874935865402</left_val>
            <right_val>-0.2681894004344940</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 11 2 2 -1.</_>
                <_>
                  14 11 1 1 2.</_>
                <_>
                  15 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.2014168361201882e-04</threshold>
            <left_node>1</left_node>
            <right_val>0.2550072968006134</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 10 7 2 -1.</_>
                <_>
                  13 11 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.0233519899193197e-04</threshold>
            <left_val>7.2745857760310173e-03</left_val>
            <right_val>-0.5081527233123779</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 13 1 2 -1.</_>
                <_>
                  7 13 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.1760020647197962e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.4384926855564117</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 12 4 3 -1.</_>
                <_>
                  6 12 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2668699491769075e-03</threshold>
            <left_val>0.1634940057992935</left_val>
            <right_val>-0.2912816107273102</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 2 2 5 -1.</_>
                <_>
                  9 2 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.1056100055575371e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7500135898590088</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 17 4 2 -1.</_>
                <_>
                  3 17 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5026510227471590e-03</threshold>
            <left_val>0.2719883024692535</left_val>
            <right_val>-0.0994867980480194</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 17 4 3 -1.</_>
                <_>
                  13 17 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.6238620523363352e-03</threshold>
            <left_val>-0.6039624810218811</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 16 5 3 -1.</_>
                <_>
                  15 17 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.6577658765017986e-03</threshold>
            <left_val>0.1093837991356850</left_val>
            <right_val>-0.5300763845443726</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 16 4 3 -1.</_>
                <_>
                  15 17 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.1830249354243279e-03</threshold>
            <left_val>-0.4772489070892334</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 17 16 3 -1.</_>
                <_>
                  4 17 8 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0109313298016787</threshold>
            <left_val>-0.0430658198893070</left_val>
            <right_val>0.3894585967063904</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 14 2 2 -1.</_>
                <_>
                  0 14 1 1 2.</_>
                <_>
                  1 15 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0047679534181952e-03</threshold>
            <left_val>0.4155359864234924</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 2 6 6 -1.</_>
                <_>
                  7 4 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0466604307293892</threshold>
            <left_val>0.3015987873077393</left_val>
            <right_val>-0.1618438065052032</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 5 1 3 -1.</_>
                <_>
                  2 6 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.2002381049096584e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5462177991867065</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 7 2 2 -1.</_>
                <_>
                  2 7 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.7367519903928041e-03</threshold>
            <left_val>-0.2198777943849564</left_val>
            <right_val>0.1960642039775848</right_val></_></_></trees>
      <stage_threshold>-1.7628519535064697</stage_threshold>
      <parent>17</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 19 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 11 5 3 -1.</_>
                <_>
                  5 12 5 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0171605199575424</threshold>
            <left_val>-0.3227300941944122</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 14 4 6 -1.</_>
                <_>
                  16 17 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0145035600289702</threshold>
            <left_val>-0.3943862020969391</left_val>
            <right_val>0.5792297720909119</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 13 6 7 -1.</_>
                <_>
                  8 13 2 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.0323518961668015e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4153687059879303</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 1 12 11 -1.</_>
                <_>
                  3 1 6 11 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.9836131297051907e-03</threshold>
            <left_val>0.3551585972309113</left_val>
            <right_val>-0.3817715048789978</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 10 7 3 -1.</_>
                <_>
                  6 11 7 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0192209091037512</threshold>
            <left_val>0.4531590044498444</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 0 9 4 -1.</_>
                <_>
                  8 2 9 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0400871597230434</threshold>
            <left_val>0.1722837984561920</left_val>
            <right_val>-0.3111056089401245</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 14 10 2 -1.</_>
                <_>
                  10 15 10 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.6549701839685440e-03</threshold>
            <left_val>-0.4046160876750946</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 1 18 -1.</_>
                <_>
                  0 6 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0116112697869539</threshold>
            <left_val>0.2903423905372620</left_val>
            <right_val>-0.2207850962877274</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 13 2 2 -1.</_>
                <_>
                  4 13 1 1 2.</_>
                <_>
                  5 14 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0576159693300724e-03</threshold>
            <left_val>0.3585166931152344</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 11 3 6 -1.</_>
                <_>
                  9 12 1 6 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.3360800221562386e-03</threshold>
            <left_val>0.0159689001739025</left_val>
            <right_val>-0.4199010133743286</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 7 2 3 -1.</_>
                <_>
                  5 8 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>5.2302791737020016e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4966328144073486</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 8 3 3 -1.</_>
                <_>
                  5 8 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.7848479803651571e-03</threshold>
            <left_val>-0.5296021103858948</left_val>
            <right_val>0.1553544998168945</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 4 14 1 -1.</_>
                <_>
                  1 4 7 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0256541296839714</threshold>
            <left_val>-0.5930917859077454</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 13 8 3 -1.</_>
                <_>
                  14 13 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.8942131474614143e-03</threshold>
            <left_val>0.2431810945272446</left_val>
            <right_val>-0.1823194026947021</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 17 2 1 -1.</_>
                <_>
                  4 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.9622750743292272e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.3271628916263580</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 16 2 2 -1.</_>
                <_>
                  6 16 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.4154611900448799e-03</threshold>
            <left_val>-0.5082166790962219</left_val>
            <right_val>0.1954334974288940</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 17 4 2 -1.</_>
                <_>
                  4 17 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.7164386564400047e-05</threshold>
            <left_val>0.1860219985246658</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 7 20 2 -1.</_>
                <_>
                  5 7 10 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0224166903644800</threshold>
            <left_val>-0.3928199112415314</left_val>
            <right_val>0.1327912956476212</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 9 2 2 -1.</_>
                <_>
                  15 9 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>8.4287580102682114e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5544756054878235</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 12 2 2 -1.</_>
                <_>
                  3 12 1 1 2.</_>
                <_>
                  4 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.7357551092281938e-04</threshold>
            <left_val>0.4715873003005981</left_val>
            <right_val>-0.0384924784302711</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 5 2 1 -1.</_>
                <_>
                  1 5 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.7496971092186868e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.2519702911376953</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 0 3 2 -1.</_>
                <_>
                  18 1 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.5816078782081604e-03</threshold>
            <left_val>0.2025039941072464</left_val>
            <right_val>-0.6163808107376099</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 8 3 9 -1.</_>
                <_>
                  3 11 1 3 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0111751500517130</threshold>
            <left_node>1</left_node>
            <right_val>-0.2777119874954224</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 7 4 2 -1.</_>
                <_>
                  16 8 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.4238609522581100e-03</threshold>
            <left_val>-0.5010343790054321</left_val>
            <right_val>0.1931852996349335</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 16 3 3 -1.</_>
                <_>
                  5 16 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0201480258256197e-03</threshold>
            <left_val>-0.6590424776077271</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 14 6 1 -1.</_>
                <_>
                  10 14 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0343679245561361e-03</threshold>
            <left_val>0.3196248114109039</left_val>
            <right_val>-0.1051291003823280</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 0 6 6 -1.</_>
                <_>
                  14 0 3 3 2.</_>
                <_>
                  17 3 3 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0109712900593877</threshold>
            <left_val>0.3270700871944427</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 2 2 1 -1.</_>
                <_>
                  17 2 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.2000739661743864e-04</threshold>
            <left_val>-0.4167926907539368</left_val>
            <right_val>0.1164520010352135</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 19 20 1 -1.</_>
                <_>
                  10 19 10 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.1552699618041515e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.1538939028978348</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 19 6 1 -1.</_>
                <_>
                  3 19 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.5970800304785371e-03</threshold>
            <left_val>-0.4297927021980286</left_val>
            <right_val>0.1919295042753220</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 17 4 3 -1.</_>
                <_>
                  10 17 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.3590939603745937e-03</threshold>
            <left_val>-0.8661373853683472</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 11 3 3 -1.</_>
                <_>
                  5 12 1 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.5752048976719379e-03</threshold>
            <left_val>0.3529854118824005</left_val>
            <right_val>-0.0726247206330299</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 7 3 3 -1.</_>
                <_>
                  18 8 1 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.5486191045492887e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3614104092121124</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 1 1 4 -1.</_>
                <_>
                  18 2 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.7437560018151999e-03</threshold>
            <left_val>-0.0402509197592735</left_val>
            <right_val>0.4111959040164948</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 8 2 1 -1.</_>
                <_>
                  7 8 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.5892767452169210e-05</threshold>
            <left_node>1</left_node>
            <right_val>0.1552398949861526</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 4 4 4 -1.</_>
                <_>
                  6 5 2 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0122171696275473</threshold>
            <left_val>-0.3656722903251648</left_val>
            <right_val>0.2515968978404999</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 8 7 -1.</_>
                <_>
                  9 0 4 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0601993091404438</threshold>
            <left_node>1</left_node>
            <right_val>-0.6895959973335266</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 7 5 9 -1.</_>
                <_>
                  0 10 5 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0916843712329865</threshold>
            <left_val>-0.6631187200546265</left_val>
            <right_val>0.0948273614048958</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 10 2 2 -1.</_>
                <_>
                  14 10 1 1 2.</_>
                <_>
                  15 11 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.9392811059951782e-04</threshold>
            <left_node>1</left_node>
            <right_val>0.2873100936412811</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 11 2 2 -1.</_>
                <_>
                  15 11 1 1 2.</_>
                <_>
                  16 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.1146500473842025e-03</threshold>
            <left_val>0.3612706065177917</left_val>
            <right_val>-0.2405422925949097</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 2 6 4 -1.</_>
                <_>
                  11 2 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0110427802428603</threshold>
            <left_val>-0.7168669104576111</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 12 12 8 -1.</_>
                <_>
                  6 12 6 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0377693511545658</threshold>
            <left_val>0.1112534999847412</left_val>
            <right_val>-0.5632094740867615</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 6 2 -1.</_>
                <_>
                  3 0 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.5979429744184017e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5699890851974487</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 12 4 5 -1.</_>
                <_>
                  1 12 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.5462140329182148e-03</threshold>
            <left_val>0.2673457860946655</left_val>
            <right_val>-0.1052770018577576</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 12 4 4 -1.</_>
                <_>
                  3 12 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7929819878190756e-03</threshold>
            <left_val>0.1771212071180344</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 11 2 4 -1.</_>
                <_>
                  13 11 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.9686378487385809e-05</threshold>
            <left_val>0.1676241010427475</left_val>
            <right_val>-0.4133665859699249</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 1 4 -1.</_>
                <_>
                  2 2 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.8254990037530661e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.3132705092430115</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 1 4 9 -1.</_>
                <_>
                  7 1 2 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.0599349886178970e-03</threshold>
            <left_val>0.2031262964010239</left_val>
            <right_val>-0.4636094868183136</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 10 2 3 -1.</_>
                <_>
                  13 11 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.5843180008232594e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.2641308903694153</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 9 15 3 -1.</_>
                <_>
                  8 10 5 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0461016409099102</threshold>
            <left_val>0.2458764016628265</left_val>
            <right_val>-0.3115119934082031</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 10 3 1 -1.</_>
                <_>
                  16 11 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.5759950038045645e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3659397065639496</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 0 15 8 -1.</_>
                <_>
                  1 2 15 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0359046310186386</threshold>
            <left_val>-0.0133526204153895</left_val>
            <right_val>0.4950073957443237</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 3 15 6 -1.</_>
                <_>
                  2 6 15 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0192305296659470</threshold>
            <left_node>1</left_node>
            <right_val>0.1860356032848358</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 0 6 6 -1.</_>
                <_>
                  6 2 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0134618300944567</threshold>
            <left_val>-0.4270431101322174</left_val>
            <right_val>0.1475695073604584</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 9 4 3 -1.</_>
                <_>
                  16 10 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.3534970395267010e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5882459282875061</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 7 4 3 -1.</_>
                <_>
                  16 8 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.7998740337789059e-03</threshold>
            <left_val>0.1396612972021103</left_val>
            <right_val>-0.3694832026958466</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 10 2 2 -1.</_>
                <_>
                  15 10 1 1 2.</_>
                <_>
                  16 11 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.7894563805311918e-04</threshold>
            <left_val>0.4315659105777740</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 11 2 3 -1.</_>
                <_>
                  13 12 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.8534340197220445e-03</threshold>
            <left_val>-0.1905311048030853</left_val>
            <right_val>0.2686879932880402</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 16 2 2 -1.</_>
                <_>
                  2 16 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>5.5962381884455681e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.3054575026035309</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 0 4 7 -1.</_>
                <_>
                  4 0 2 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.1787789240479469e-03</threshold>
            <left_val>-0.7235335111618042</left_val>
            <right_val>0.1619776934385300</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 2 2 -1.</_>
                <_>
                  0 16 1 1 2.</_>
                <_>
                  1 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.4591833506710827e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.1612174957990646</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 0 18 3 -1.</_>
                <_>
                  8 0 6 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.2282380163669586e-03</threshold>
            <left_val>0.4244168102741241</left_val>
            <right_val>-0.1148820966482162</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 1 3 -1.</_>
                <_>
                  0 2 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.2379399053752422e-03</threshold>
            <left_val>-0.8281142711639404</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 6 4 4 -1.</_>
                <_>
                  10 7 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.7763898037374020e-03</threshold>
            <left_val>0.3915700912475586</left_val>
            <right_val>-0.0376774296164513</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 4 4 6 -1.</_>
                <_>
                  16 4 2 3 2.</_>
                <_>
                  18 7 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.1182728968560696e-03</threshold>
            <left_val>0.3020882904529572</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 12 4 2 -1.</_>
                <_>
                  11 12 2 1 2.</_>
                <_>
                  13 13 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.1565790995955467e-03</threshold>
            <left_val>-0.1904578953981400</left_val>
            <right_val>0.3021968901157379</right_val></_></_></trees>
      <stage_threshold>-1.8088439702987671</stage_threshold>
      <parent>18</parent>
      <next>-1</next></_></stages></haarcascade_lefteye>
</opencv_storage>
