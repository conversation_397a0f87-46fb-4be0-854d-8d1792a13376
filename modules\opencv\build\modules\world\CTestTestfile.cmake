# CMake generated Testfile for 
# Source directory: D:/AI/opencv/opencv-4.10.0/modules/world
# Build directory: D:/AI/opencv/cudabuild/modules/world
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_core "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_cored.exe" "--gtest_output=xml:opencv_test_core.xml")
  set_tests_properties(opencv_test_core PROPERTIES  LABELS "Main;opencv_core;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/core/CMakeLists.txt;197;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/core/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_core "D:/AI/opencv/cudabuild/bin/Release/opencv_test_core.exe" "--gtest_output=xml:opencv_test_core.xml")
  set_tests_properties(opencv_test_core PROPERTIES  LABELS "Main;opencv_core;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/core/CMakeLists.txt;197;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/core/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_core NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_core "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cored.exe" "--gtest_output=xml:opencv_perf_core.xml")
  set_tests_properties(opencv_perf_core PROPERTIES  LABELS "Main;opencv_core;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/core/CMakeLists.txt;198;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/core/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_core "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_core.exe" "--gtest_output=xml:opencv_perf_core.xml")
  set_tests_properties(opencv_perf_core PROPERTIES  LABELS "Main;opencv_core;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/core/CMakeLists.txt;198;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/core/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_core NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_core "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cored.exe" "--gtest_output=xml:opencv_perf_core.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_core PROPERTIES  LABELS "Main;opencv_core;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/core/CMakeLists.txt;198;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/core/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_core "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_core.exe" "--gtest_output=xml:opencv_perf_core.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_core PROPERTIES  LABELS "Main;opencv_core;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/core/CMakeLists.txt;198;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/core/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_core NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_cudaarithm "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_cudaarithmd.exe" "--gtest_output=xml:opencv_test_cudaarithm.xml")
  set_tests_properties(opencv_test_cudaarithm PROPERTIES  LABELS "Extra;opencv_cudaarithm;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaarithm/CMakeLists.txt;39;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaarithm/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_cudaarithm "D:/AI/opencv/cudabuild/bin/Release/opencv_test_cudaarithm.exe" "--gtest_output=xml:opencv_test_cudaarithm.xml")
  set_tests_properties(opencv_test_cudaarithm PROPERTIES  LABELS "Extra;opencv_cudaarithm;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaarithm/CMakeLists.txt;39;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaarithm/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_cudaarithm NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_cudaarithm "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudaarithmd.exe" "--gtest_output=xml:opencv_perf_cudaarithm.xml")
  set_tests_properties(opencv_perf_cudaarithm PROPERTIES  LABELS "Extra;opencv_cudaarithm;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaarithm/CMakeLists.txt;40;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaarithm/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_cudaarithm "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudaarithm.exe" "--gtest_output=xml:opencv_perf_cudaarithm.xml")
  set_tests_properties(opencv_perf_cudaarithm PROPERTIES  LABELS "Extra;opencv_cudaarithm;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaarithm/CMakeLists.txt;40;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaarithm/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_cudaarithm NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_cudaarithm "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudaarithmd.exe" "--gtest_output=xml:opencv_perf_cudaarithm.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudaarithm PROPERTIES  LABELS "Extra;opencv_cudaarithm;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaarithm/CMakeLists.txt;40;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaarithm/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_cudaarithm "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudaarithm.exe" "--gtest_output=xml:opencv_perf_cudaarithm.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudaarithm PROPERTIES  LABELS "Extra;opencv_cudaarithm;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaarithm/CMakeLists.txt;40;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaarithm/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_cudaarithm NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_flann "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_flannd.exe" "--gtest_output=xml:opencv_test_flann.xml")
  set_tests_properties(opencv_test_flann PROPERTIES  LABELS "Main;opencv_flann;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/flann/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/flann/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_flann "D:/AI/opencv/cudabuild/bin/Release/opencv_test_flann.exe" "--gtest_output=xml:opencv_test_flann.xml")
  set_tests_properties(opencv_test_flann PROPERTIES  LABELS "Main;opencv_flann;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/flann/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/flann/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_flann NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_imgproc "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_imgprocd.exe" "--gtest_output=xml:opencv_test_imgproc.xml")
  set_tests_properties(opencv_test_imgproc PROPERTIES  LABELS "Main;opencv_imgproc;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/imgproc/CMakeLists.txt;13;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/imgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_imgproc "D:/AI/opencv/cudabuild/bin/Release/opencv_test_imgproc.exe" "--gtest_output=xml:opencv_test_imgproc.xml")
  set_tests_properties(opencv_test_imgproc PROPERTIES  LABELS "Main;opencv_imgproc;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/imgproc/CMakeLists.txt;13;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/imgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_imgproc NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_imgproc "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_imgprocd.exe" "--gtest_output=xml:opencv_perf_imgproc.xml")
  set_tests_properties(opencv_perf_imgproc PROPERTIES  LABELS "Main;opencv_imgproc;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/imgproc/CMakeLists.txt;13;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/imgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_imgproc "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_imgproc.exe" "--gtest_output=xml:opencv_perf_imgproc.xml")
  set_tests_properties(opencv_perf_imgproc PROPERTIES  LABELS "Main;opencv_imgproc;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/imgproc/CMakeLists.txt;13;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/imgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_imgproc NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_imgproc "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_imgprocd.exe" "--gtest_output=xml:opencv_perf_imgproc.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_imgproc PROPERTIES  LABELS "Main;opencv_imgproc;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/imgproc/CMakeLists.txt;13;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/imgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_imgproc "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_imgproc.exe" "--gtest_output=xml:opencv_perf_imgproc.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_imgproc PROPERTIES  LABELS "Main;opencv_imgproc;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/imgproc/CMakeLists.txt;13;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/imgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_imgproc NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_intensity_transform "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_intensity_transformd.exe" "--gtest_output=xml:opencv_test_intensity_transform.xml")
  set_tests_properties(opencv_test_intensity_transform PROPERTIES  LABELS "Extra;opencv_intensity_transform;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/intensity_transform/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/intensity_transform/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_intensity_transform "D:/AI/opencv/cudabuild/bin/Release/opencv_test_intensity_transform.exe" "--gtest_output=xml:opencv_test_intensity_transform.xml")
  set_tests_properties(opencv_test_intensity_transform PROPERTIES  LABELS "Extra;opencv_intensity_transform;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/intensity_transform/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/intensity_transform/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_intensity_transform NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_ml "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_mld.exe" "--gtest_output=xml:opencv_test_ml.xml")
  set_tests_properties(opencv_test_ml PROPERTIES  LABELS "Main;opencv_ml;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/ml/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/ml/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_ml "D:/AI/opencv/cudabuild/bin/Release/opencv_test_ml.exe" "--gtest_output=xml:opencv_test_ml.xml")
  set_tests_properties(opencv_test_ml PROPERTIES  LABELS "Main;opencv_ml;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/ml/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/ml/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_ml NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_phase_unwrapping "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_phase_unwrappingd.exe" "--gtest_output=xml:opencv_test_phase_unwrapping.xml")
  set_tests_properties(opencv_test_phase_unwrapping PROPERTIES  LABELS "Extra;opencv_phase_unwrapping;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/phase_unwrapping/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/phase_unwrapping/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_phase_unwrapping "D:/AI/opencv/cudabuild/bin/Release/opencv_test_phase_unwrapping.exe" "--gtest_output=xml:opencv_test_phase_unwrapping.xml")
  set_tests_properties(opencv_test_phase_unwrapping PROPERTIES  LABELS "Extra;opencv_phase_unwrapping;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/phase_unwrapping/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/phase_unwrapping/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_phase_unwrapping NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_quality "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_qualityd.exe" "--gtest_output=xml:opencv_test_quality.xml")
  set_tests_properties(opencv_test_quality PROPERTIES  LABELS "Extra;opencv_quality;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/quality/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/quality/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_quality "D:/AI/opencv/cudabuild/bin/Release/opencv_test_quality.exe" "--gtest_output=xml:opencv_test_quality.xml")
  set_tests_properties(opencv_test_quality PROPERTIES  LABELS "Extra;opencv_quality;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/quality/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/quality/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_quality NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_reg "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_regd.exe" "--gtest_output=xml:opencv_test_reg.xml")
  set_tests_properties(opencv_test_reg PROPERTIES  LABELS "Extra;opencv_reg;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/reg/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/reg/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_reg "D:/AI/opencv/cudabuild/bin/Release/opencv_test_reg.exe" "--gtest_output=xml:opencv_test_reg.xml")
  set_tests_properties(opencv_test_reg PROPERTIES  LABELS "Extra;opencv_reg;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/reg/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/reg/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_reg NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_reg "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_regd.exe" "--gtest_output=xml:opencv_perf_reg.xml")
  set_tests_properties(opencv_perf_reg PROPERTIES  LABELS "Extra;opencv_reg;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/reg/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/reg/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_reg "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_reg.exe" "--gtest_output=xml:opencv_perf_reg.xml")
  set_tests_properties(opencv_perf_reg PROPERTIES  LABELS "Extra;opencv_reg;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/reg/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/reg/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_reg NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_reg "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_regd.exe" "--gtest_output=xml:opencv_perf_reg.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_reg PROPERTIES  LABELS "Extra;opencv_reg;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/reg/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/reg/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_reg "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_reg.exe" "--gtest_output=xml:opencv_perf_reg.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_reg PROPERTIES  LABELS "Extra;opencv_reg;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/reg/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/reg/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_reg NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_signal "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_signald.exe" "--gtest_output=xml:opencv_test_signal.xml")
  set_tests_properties(opencv_test_signal PROPERTIES  LABELS "Extra;opencv_signal;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/signal/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/signal/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_signal "D:/AI/opencv/cudabuild/bin/Release/opencv_test_signal.exe" "--gtest_output=xml:opencv_test_signal.xml")
  set_tests_properties(opencv_test_signal PROPERTIES  LABELS "Extra;opencv_signal;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/signal/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/signal/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_signal NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_signal "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_signald.exe" "--gtest_output=xml:opencv_perf_signal.xml")
  set_tests_properties(opencv_perf_signal PROPERTIES  LABELS "Extra;opencv_signal;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/signal/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/signal/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_signal "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_signal.exe" "--gtest_output=xml:opencv_perf_signal.xml")
  set_tests_properties(opencv_perf_signal PROPERTIES  LABELS "Extra;opencv_signal;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/signal/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/signal/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_signal NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_signal "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_signald.exe" "--gtest_output=xml:opencv_perf_signal.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_signal PROPERTIES  LABELS "Extra;opencv_signal;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/signal/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/signal/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_signal "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_signal.exe" "--gtest_output=xml:opencv_perf_signal.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_signal PROPERTIES  LABELS "Extra;opencv_signal;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/signal/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/signal/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_signal NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_cudafilters "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_cudafiltersd.exe" "--gtest_output=xml:opencv_test_cudafilters.xml")
  set_tests_properties(opencv_test_cudafilters PROPERTIES  LABELS "Extra;opencv_cudafilters;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafilters/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafilters/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_cudafilters "D:/AI/opencv/cudabuild/bin/Release/opencv_test_cudafilters.exe" "--gtest_output=xml:opencv_test_cudafilters.xml")
  set_tests_properties(opencv_test_cudafilters PROPERTIES  LABELS "Extra;opencv_cudafilters;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafilters/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafilters/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_cudafilters NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_cudafilters "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudafiltersd.exe" "--gtest_output=xml:opencv_perf_cudafilters.xml")
  set_tests_properties(opencv_perf_cudafilters PROPERTIES  LABELS "Extra;opencv_cudafilters;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafilters/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafilters/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_cudafilters "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudafilters.exe" "--gtest_output=xml:opencv_perf_cudafilters.xml")
  set_tests_properties(opencv_perf_cudafilters PROPERTIES  LABELS "Extra;opencv_cudafilters;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafilters/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafilters/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_cudafilters NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_cudafilters "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudafiltersd.exe" "--gtest_output=xml:opencv_perf_cudafilters.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudafilters PROPERTIES  LABELS "Extra;opencv_cudafilters;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafilters/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafilters/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_cudafilters "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudafilters.exe" "--gtest_output=xml:opencv_perf_cudafilters.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudafilters PROPERTIES  LABELS "Extra;opencv_cudafilters;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafilters/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafilters/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_cudafilters NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_cudaimgproc "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_cudaimgprocd.exe" "--gtest_output=xml:opencv_test_cudaimgproc.xml")
  set_tests_properties(opencv_test_cudaimgproc PROPERTIES  LABELS "Extra;opencv_cudaimgproc;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaimgproc/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaimgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_cudaimgproc "D:/AI/opencv/cudabuild/bin/Release/opencv_test_cudaimgproc.exe" "--gtest_output=xml:opencv_test_cudaimgproc.xml")
  set_tests_properties(opencv_test_cudaimgproc PROPERTIES  LABELS "Extra;opencv_cudaimgproc;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaimgproc/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaimgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_cudaimgproc NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_cudaimgproc "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudaimgprocd.exe" "--gtest_output=xml:opencv_perf_cudaimgproc.xml")
  set_tests_properties(opencv_perf_cudaimgproc PROPERTIES  LABELS "Extra;opencv_cudaimgproc;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaimgproc/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaimgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_cudaimgproc "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudaimgproc.exe" "--gtest_output=xml:opencv_perf_cudaimgproc.xml")
  set_tests_properties(opencv_perf_cudaimgproc PROPERTIES  LABELS "Extra;opencv_cudaimgproc;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaimgproc/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaimgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_cudaimgproc NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_cudaimgproc "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudaimgprocd.exe" "--gtest_output=xml:opencv_perf_cudaimgproc.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudaimgproc PROPERTIES  LABELS "Extra;opencv_cudaimgproc;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaimgproc/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaimgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_cudaimgproc "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudaimgproc.exe" "--gtest_output=xml:opencv_perf_cudaimgproc.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudaimgproc PROPERTIES  LABELS "Extra;opencv_cudaimgproc;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaimgproc/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaimgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_cudaimgproc NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_cudawarping "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_cudawarpingd.exe" "--gtest_output=xml:opencv_test_cudawarping.xml")
  set_tests_properties(opencv_test_cudawarping PROPERTIES  LABELS "Extra;opencv_cudawarping;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudawarping/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudawarping/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_cudawarping "D:/AI/opencv/cudabuild/bin/Release/opencv_test_cudawarping.exe" "--gtest_output=xml:opencv_test_cudawarping.xml")
  set_tests_properties(opencv_test_cudawarping PROPERTIES  LABELS "Extra;opencv_cudawarping;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudawarping/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudawarping/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_cudawarping NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_cudawarping "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudawarpingd.exe" "--gtest_output=xml:opencv_perf_cudawarping.xml")
  set_tests_properties(opencv_perf_cudawarping PROPERTIES  LABELS "Extra;opencv_cudawarping;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudawarping/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudawarping/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_cudawarping "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudawarping.exe" "--gtest_output=xml:opencv_perf_cudawarping.xml")
  set_tests_properties(opencv_perf_cudawarping PROPERTIES  LABELS "Extra;opencv_cudawarping;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudawarping/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudawarping/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_cudawarping NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_cudawarping "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudawarpingd.exe" "--gtest_output=xml:opencv_perf_cudawarping.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudawarping PROPERTIES  LABELS "Extra;opencv_cudawarping;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudawarping/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudawarping/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_cudawarping "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudawarping.exe" "--gtest_output=xml:opencv_perf_cudawarping.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudawarping PROPERTIES  LABELS "Extra;opencv_cudawarping;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudawarping/CMakeLists.txt;12;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudawarping/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_cudawarping NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_dnn "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_dnnd.exe" "--gtest_output=xml:opencv_test_dnn.xml")
  set_tests_properties(opencv_test_dnn PROPERTIES  LABELS "Main;opencv_dnn;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/dnn/CMakeLists.txt;247;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/dnn/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_dnn "D:/AI/opencv/cudabuild/bin/Release/opencv_test_dnn.exe" "--gtest_output=xml:opencv_test_dnn.xml")
  set_tests_properties(opencv_test_dnn PROPERTIES  LABELS "Main;opencv_dnn;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/dnn/CMakeLists.txt;247;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/dnn/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_dnn NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_dnn "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_dnnd.exe" "--gtest_output=xml:opencv_perf_dnn.xml")
  set_tests_properties(opencv_perf_dnn PROPERTIES  LABELS "Main;opencv_dnn;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/dnn/CMakeLists.txt;258;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/dnn/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_dnn "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_dnn.exe" "--gtest_output=xml:opencv_perf_dnn.xml")
  set_tests_properties(opencv_perf_dnn PROPERTIES  LABELS "Main;opencv_dnn;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/dnn/CMakeLists.txt;258;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/dnn/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_dnn NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_dnn "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_dnnd.exe" "--gtest_output=xml:opencv_perf_dnn.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_dnn PROPERTIES  LABELS "Main;opencv_dnn;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/dnn/CMakeLists.txt;258;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/dnn/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_dnn "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_dnn.exe" "--gtest_output=xml:opencv_perf_dnn.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_dnn PROPERTIES  LABELS "Main;opencv_dnn;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/dnn/CMakeLists.txt;258;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/dnn/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_dnn NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_dnn_superres "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_dnn_superresd.exe" "--gtest_output=xml:opencv_test_dnn_superres.xml")
  set_tests_properties(opencv_test_dnn_superres PROPERTIES  LABELS "Extra;opencv_dnn_superres;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/dnn_superres/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/dnn_superres/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_dnn_superres "D:/AI/opencv/cudabuild/bin/Release/opencv_test_dnn_superres.exe" "--gtest_output=xml:opencv_test_dnn_superres.xml")
  set_tests_properties(opencv_test_dnn_superres PROPERTIES  LABELS "Extra;opencv_dnn_superres;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/dnn_superres/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/dnn_superres/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_dnn_superres NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_dnn_superres "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_dnn_superresd.exe" "--gtest_output=xml:opencv_perf_dnn_superres.xml")
  set_tests_properties(opencv_perf_dnn_superres PROPERTIES  LABELS "Extra;opencv_dnn_superres;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/dnn_superres/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/dnn_superres/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_dnn_superres "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_dnn_superres.exe" "--gtest_output=xml:opencv_perf_dnn_superres.xml")
  set_tests_properties(opencv_perf_dnn_superres PROPERTIES  LABELS "Extra;opencv_dnn_superres;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/dnn_superres/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/dnn_superres/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_dnn_superres NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_dnn_superres "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_dnn_superresd.exe" "--gtest_output=xml:opencv_perf_dnn_superres.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_dnn_superres PROPERTIES  LABELS "Extra;opencv_dnn_superres;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/dnn_superres/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/dnn_superres/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_dnn_superres "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_dnn_superres.exe" "--gtest_output=xml:opencv_perf_dnn_superres.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_dnn_superres PROPERTIES  LABELS "Extra;opencv_dnn_superres;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/dnn_superres/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/dnn_superres/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_dnn_superres NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_features2d "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_features2dd.exe" "--gtest_output=xml:opencv_test_features2d.xml")
  set_tests_properties(opencv_test_features2d PROPERTIES  LABELS "Main;opencv_features2d;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/features2d/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/features2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_features2d "D:/AI/opencv/cudabuild/bin/Release/opencv_test_features2d.exe" "--gtest_output=xml:opencv_test_features2d.xml")
  set_tests_properties(opencv_test_features2d PROPERTIES  LABELS "Main;opencv_features2d;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/features2d/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/features2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_features2d NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_features2d "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_features2dd.exe" "--gtest_output=xml:opencv_perf_features2d.xml")
  set_tests_properties(opencv_perf_features2d PROPERTIES  LABELS "Main;opencv_features2d;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/features2d/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/features2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_features2d "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_features2d.exe" "--gtest_output=xml:opencv_perf_features2d.xml")
  set_tests_properties(opencv_perf_features2d PROPERTIES  LABELS "Main;opencv_features2d;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/features2d/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/features2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_features2d NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_features2d "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_features2dd.exe" "--gtest_output=xml:opencv_perf_features2d.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_features2d PROPERTIES  LABELS "Main;opencv_features2d;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/features2d/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/features2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_features2d "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_features2d.exe" "--gtest_output=xml:opencv_perf_features2d.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_features2d PROPERTIES  LABELS "Main;opencv_features2d;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/features2d/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/features2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_features2d NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_fuzzy "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_fuzzyd.exe" "--gtest_output=xml:opencv_test_fuzzy.xml")
  set_tests_properties(opencv_test_fuzzy PROPERTIES  LABELS "Extra;opencv_fuzzy;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/fuzzy/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/fuzzy/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_fuzzy "D:/AI/opencv/cudabuild/bin/Release/opencv_test_fuzzy.exe" "--gtest_output=xml:opencv_test_fuzzy.xml")
  set_tests_properties(opencv_test_fuzzy PROPERTIES  LABELS "Extra;opencv_fuzzy;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/fuzzy/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/fuzzy/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_fuzzy NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_imgcodecs "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_imgcodecsd.exe" "--gtest_output=xml:opencv_test_imgcodecs.xml")
  set_tests_properties(opencv_test_imgcodecs PROPERTIES  LABELS "Main;opencv_imgcodecs;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/imgcodecs/CMakeLists.txt;186;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/imgcodecs/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_imgcodecs "D:/AI/opencv/cudabuild/bin/Release/opencv_test_imgcodecs.exe" "--gtest_output=xml:opencv_test_imgcodecs.xml")
  set_tests_properties(opencv_test_imgcodecs PROPERTIES  LABELS "Main;opencv_imgcodecs;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/imgcodecs/CMakeLists.txt;186;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/imgcodecs/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_imgcodecs NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_imgcodecs "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_imgcodecsd.exe" "--gtest_output=xml:opencv_perf_imgcodecs.xml")
  set_tests_properties(opencv_perf_imgcodecs PROPERTIES  LABELS "Main;opencv_imgcodecs;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/imgcodecs/CMakeLists.txt;197;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/imgcodecs/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_imgcodecs "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_imgcodecs.exe" "--gtest_output=xml:opencv_perf_imgcodecs.xml")
  set_tests_properties(opencv_perf_imgcodecs PROPERTIES  LABELS "Main;opencv_imgcodecs;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/imgcodecs/CMakeLists.txt;197;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/imgcodecs/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_imgcodecs NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_imgcodecs "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_imgcodecsd.exe" "--gtest_output=xml:opencv_perf_imgcodecs.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_imgcodecs PROPERTIES  LABELS "Main;opencv_imgcodecs;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/imgcodecs/CMakeLists.txt;197;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/imgcodecs/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_imgcodecs "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_imgcodecs.exe" "--gtest_output=xml:opencv_perf_imgcodecs.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_imgcodecs PROPERTIES  LABELS "Main;opencv_imgcodecs;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/imgcodecs/CMakeLists.txt;197;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/imgcodecs/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_imgcodecs NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_line_descriptor "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_line_descriptord.exe" "--gtest_output=xml:opencv_test_line_descriptor.xml")
  set_tests_properties(opencv_test_line_descriptor PROPERTIES  LABELS "Extra;opencv_line_descriptor;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/line_descriptor/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/line_descriptor/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_line_descriptor "D:/AI/opencv/cudabuild/bin/Release/opencv_test_line_descriptor.exe" "--gtest_output=xml:opencv_test_line_descriptor.xml")
  set_tests_properties(opencv_test_line_descriptor PROPERTIES  LABELS "Extra;opencv_line_descriptor;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/line_descriptor/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/line_descriptor/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_line_descriptor NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_line_descriptor "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_line_descriptord.exe" "--gtest_output=xml:opencv_perf_line_descriptor.xml")
  set_tests_properties(opencv_perf_line_descriptor PROPERTIES  LABELS "Extra;opencv_line_descriptor;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/line_descriptor/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/line_descriptor/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_line_descriptor "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_line_descriptor.exe" "--gtest_output=xml:opencv_perf_line_descriptor.xml")
  set_tests_properties(opencv_perf_line_descriptor PROPERTIES  LABELS "Extra;opencv_line_descriptor;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/line_descriptor/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/line_descriptor/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_line_descriptor NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_line_descriptor "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_line_descriptord.exe" "--gtest_output=xml:opencv_perf_line_descriptor.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_line_descriptor PROPERTIES  LABELS "Extra;opencv_line_descriptor;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/line_descriptor/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/line_descriptor/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_line_descriptor "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_line_descriptor.exe" "--gtest_output=xml:opencv_perf_line_descriptor.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_line_descriptor PROPERTIES  LABELS "Extra;opencv_line_descriptor;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/line_descriptor/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/line_descriptor/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_line_descriptor NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_photo "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_photod.exe" "--gtest_output=xml:opencv_test_photo.xml")
  set_tests_properties(opencv_test_photo PROPERTIES  LABELS "Main;opencv_photo;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/photo/CMakeLists.txt;7;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/photo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_photo "D:/AI/opencv/cudabuild/bin/Release/opencv_test_photo.exe" "--gtest_output=xml:opencv_test_photo.xml")
  set_tests_properties(opencv_test_photo PROPERTIES  LABELS "Main;opencv_photo;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/photo/CMakeLists.txt;7;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/photo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_photo NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_photo "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_photod.exe" "--gtest_output=xml:opencv_perf_photo.xml")
  set_tests_properties(opencv_perf_photo PROPERTIES  LABELS "Main;opencv_photo;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/photo/CMakeLists.txt;7;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/photo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_photo "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_photo.exe" "--gtest_output=xml:opencv_perf_photo.xml")
  set_tests_properties(opencv_perf_photo PROPERTIES  LABELS "Main;opencv_photo;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/photo/CMakeLists.txt;7;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/photo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_photo NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_photo "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_photod.exe" "--gtest_output=xml:opencv_perf_photo.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_photo PROPERTIES  LABELS "Main;opencv_photo;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/photo/CMakeLists.txt;7;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/photo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_photo "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_photo.exe" "--gtest_output=xml:opencv_perf_photo.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_photo PROPERTIES  LABELS "Main;opencv_photo;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/photo/CMakeLists.txt;7;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/photo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_photo NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_saliency "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_saliencyd.exe" "--gtest_output=xml:opencv_test_saliency.xml")
  set_tests_properties(opencv_test_saliency PROPERTIES  LABELS "Extra;opencv_saliency;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/saliency/CMakeLists.txt;7;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/saliency/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_saliency "D:/AI/opencv/cudabuild/bin/Release/opencv_test_saliency.exe" "--gtest_output=xml:opencv_test_saliency.xml")
  set_tests_properties(opencv_test_saliency PROPERTIES  LABELS "Extra;opencv_saliency;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/saliency/CMakeLists.txt;7;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/saliency/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_saliency NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_text "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_textd.exe" "--gtest_output=xml:opencv_test_text.xml")
  set_tests_properties(opencv_test_text PROPERTIES  LABELS "Extra;opencv_text;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/text/CMakeLists.txt;7;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/text/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_text "D:/AI/opencv/cudabuild/bin/Release/opencv_test_text.exe" "--gtest_output=xml:opencv_test_text.xml")
  set_tests_properties(opencv_test_text PROPERTIES  LABELS "Extra;opencv_text;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/text/CMakeLists.txt;7;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/text/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_text NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_videoio "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_videoiod.exe" "--gtest_output=xml:opencv_test_videoio.xml")
  set_tests_properties(opencv_test_videoio PROPERTIES  LABELS "Main;opencv_videoio;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/videoio/CMakeLists.txt;277;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/videoio/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_videoio "D:/AI/opencv/cudabuild/bin/Release/opencv_test_videoio.exe" "--gtest_output=xml:opencv_test_videoio.xml")
  set_tests_properties(opencv_test_videoio PROPERTIES  LABELS "Main;opencv_videoio;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/videoio/CMakeLists.txt;277;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/videoio/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_videoio NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_videoio "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_videoiod.exe" "--gtest_output=xml:opencv_perf_videoio.xml")
  set_tests_properties(opencv_perf_videoio PROPERTIES  LABELS "Main;opencv_videoio;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/videoio/CMakeLists.txt;278;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/videoio/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_videoio "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_videoio.exe" "--gtest_output=xml:opencv_perf_videoio.xml")
  set_tests_properties(opencv_perf_videoio PROPERTIES  LABELS "Main;opencv_videoio;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/videoio/CMakeLists.txt;278;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/videoio/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_videoio NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_videoio "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_videoiod.exe" "--gtest_output=xml:opencv_perf_videoio.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_videoio PROPERTIES  LABELS "Main;opencv_videoio;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/videoio/CMakeLists.txt;278;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/videoio/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_videoio "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_videoio.exe" "--gtest_output=xml:opencv_perf_videoio.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_videoio PROPERTIES  LABELS "Main;opencv_videoio;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/videoio/CMakeLists.txt;278;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/videoio/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_videoio NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_xphoto "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_xphotod.exe" "--gtest_output=xml:opencv_test_xphoto.xml")
  set_tests_properties(opencv_test_xphoto PROPERTIES  LABELS "Extra;opencv_xphoto;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/xphoto/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/xphoto/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_xphoto "D:/AI/opencv/cudabuild/bin/Release/opencv_test_xphoto.exe" "--gtest_output=xml:opencv_test_xphoto.xml")
  set_tests_properties(opencv_test_xphoto PROPERTIES  LABELS "Extra;opencv_xphoto;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/xphoto/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/xphoto/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_xphoto NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_xphoto "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_xphotod.exe" "--gtest_output=xml:opencv_perf_xphoto.xml")
  set_tests_properties(opencv_perf_xphoto PROPERTIES  LABELS "Extra;opencv_xphoto;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/xphoto/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/xphoto/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_xphoto "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_xphoto.exe" "--gtest_output=xml:opencv_perf_xphoto.xml")
  set_tests_properties(opencv_perf_xphoto PROPERTIES  LABELS "Extra;opencv_xphoto;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/xphoto/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/xphoto/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_xphoto NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_xphoto "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_xphotod.exe" "--gtest_output=xml:opencv_perf_xphoto.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_xphoto PROPERTIES  LABELS "Extra;opencv_xphoto;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/xphoto/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/xphoto/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_xphoto "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_xphoto.exe" "--gtest_output=xml:opencv_perf_xphoto.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_xphoto PROPERTIES  LABELS "Extra;opencv_xphoto;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/xphoto/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/xphoto/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_xphoto NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_calib3d "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_calib3dd.exe" "--gtest_output=xml:opencv_test_calib3d.xml")
  set_tests_properties(opencv_test_calib3d PROPERTIES  LABELS "Main;opencv_calib3d;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/calib3d/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/calib3d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_calib3d "D:/AI/opencv/cudabuild/bin/Release/opencv_test_calib3d.exe" "--gtest_output=xml:opencv_test_calib3d.xml")
  set_tests_properties(opencv_test_calib3d PROPERTIES  LABELS "Main;opencv_calib3d;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/calib3d/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/calib3d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_calib3d NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_calib3d "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_calib3dd.exe" "--gtest_output=xml:opencv_perf_calib3d.xml")
  set_tests_properties(opencv_perf_calib3d PROPERTIES  LABELS "Main;opencv_calib3d;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/calib3d/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/calib3d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_calib3d "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_calib3d.exe" "--gtest_output=xml:opencv_perf_calib3d.xml")
  set_tests_properties(opencv_perf_calib3d PROPERTIES  LABELS "Main;opencv_calib3d;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/calib3d/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/calib3d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_calib3d NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_calib3d "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_calib3dd.exe" "--gtest_output=xml:opencv_perf_calib3d.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_calib3d PROPERTIES  LABELS "Main;opencv_calib3d;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/calib3d/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/calib3d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_calib3d "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_calib3d.exe" "--gtest_output=xml:opencv_perf_calib3d.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_calib3d PROPERTIES  LABELS "Main;opencv_calib3d;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/calib3d/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/calib3d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_calib3d NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_cudacodec "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_cudacodecd.exe" "--gtest_output=xml:opencv_test_cudacodec.xml")
  set_tests_properties(opencv_test_cudacodec PROPERTIES  LABELS "Extra;opencv_cudacodec;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudacodec/CMakeLists.txt;56;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudacodec/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_cudacodec "D:/AI/opencv/cudabuild/bin/Release/opencv_test_cudacodec.exe" "--gtest_output=xml:opencv_test_cudacodec.xml")
  set_tests_properties(opencv_test_cudacodec PROPERTIES  LABELS "Extra;opencv_cudacodec;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudacodec/CMakeLists.txt;56;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudacodec/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_cudacodec NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_cudacodec "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudacodecd.exe" "--gtest_output=xml:opencv_perf_cudacodec.xml")
  set_tests_properties(opencv_perf_cudacodec PROPERTIES  LABELS "Extra;opencv_cudacodec;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudacodec/CMakeLists.txt;57;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudacodec/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_cudacodec "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudacodec.exe" "--gtest_output=xml:opencv_perf_cudacodec.xml")
  set_tests_properties(opencv_perf_cudacodec PROPERTIES  LABELS "Extra;opencv_cudacodec;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudacodec/CMakeLists.txt;57;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudacodec/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_cudacodec NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_cudacodec "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudacodecd.exe" "--gtest_output=xml:opencv_perf_cudacodec.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudacodec PROPERTIES  LABELS "Extra;opencv_cudacodec;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudacodec/CMakeLists.txt;57;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudacodec/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_cudacodec "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudacodec.exe" "--gtest_output=xml:opencv_perf_cudacodec.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudacodec PROPERTIES  LABELS "Extra;opencv_cudacodec;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudacodec/CMakeLists.txt;57;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudacodec/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_cudacodec NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_cudafeatures2d "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_cudafeatures2dd.exe" "--gtest_output=xml:opencv_test_cudafeatures2d.xml")
  set_tests_properties(opencv_test_cudafeatures2d PROPERTIES  LABELS "Extra;opencv_cudafeatures2d;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafeatures2d/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafeatures2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_cudafeatures2d "D:/AI/opencv/cudabuild/bin/Release/opencv_test_cudafeatures2d.exe" "--gtest_output=xml:opencv_test_cudafeatures2d.xml")
  set_tests_properties(opencv_test_cudafeatures2d PROPERTIES  LABELS "Extra;opencv_cudafeatures2d;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafeatures2d/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafeatures2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_cudafeatures2d NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_cudafeatures2d "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudafeatures2dd.exe" "--gtest_output=xml:opencv_perf_cudafeatures2d.xml")
  set_tests_properties(opencv_perf_cudafeatures2d PROPERTIES  LABELS "Extra;opencv_cudafeatures2d;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafeatures2d/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafeatures2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_cudafeatures2d "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudafeatures2d.exe" "--gtest_output=xml:opencv_perf_cudafeatures2d.xml")
  set_tests_properties(opencv_perf_cudafeatures2d PROPERTIES  LABELS "Extra;opencv_cudafeatures2d;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafeatures2d/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafeatures2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_cudafeatures2d NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_cudafeatures2d "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudafeatures2dd.exe" "--gtest_output=xml:opencv_perf_cudafeatures2d.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudafeatures2d PROPERTIES  LABELS "Extra;opencv_cudafeatures2d;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafeatures2d/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafeatures2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_cudafeatures2d "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudafeatures2d.exe" "--gtest_output=xml:opencv_perf_cudafeatures2d.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudafeatures2d PROPERTIES  LABELS "Extra;opencv_cudafeatures2d;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafeatures2d/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudafeatures2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_cudafeatures2d NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_cudastereo "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_cudastereod.exe" "--gtest_output=xml:opencv_test_cudastereo.xml")
  set_tests_properties(opencv_test_cudastereo PROPERTIES  LABELS "Extra;opencv_cudastereo;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudastereo/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudastereo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_cudastereo "D:/AI/opencv/cudabuild/bin/Release/opencv_test_cudastereo.exe" "--gtest_output=xml:opencv_test_cudastereo.xml")
  set_tests_properties(opencv_test_cudastereo PROPERTIES  LABELS "Extra;opencv_cudastereo;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudastereo/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudastereo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_cudastereo NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_cudastereo "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudastereod.exe" "--gtest_output=xml:opencv_perf_cudastereo.xml")
  set_tests_properties(opencv_perf_cudastereo PROPERTIES  LABELS "Extra;opencv_cudastereo;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudastereo/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudastereo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_cudastereo "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudastereo.exe" "--gtest_output=xml:opencv_perf_cudastereo.xml")
  set_tests_properties(opencv_perf_cudastereo PROPERTIES  LABELS "Extra;opencv_cudastereo;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudastereo/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudastereo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_cudastereo NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_cudastereo "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudastereod.exe" "--gtest_output=xml:opencv_perf_cudastereo.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudastereo PROPERTIES  LABELS "Extra;opencv_cudastereo;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudastereo/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudastereo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_cudastereo "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudastereo.exe" "--gtest_output=xml:opencv_perf_cudastereo.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudastereo PROPERTIES  LABELS "Extra;opencv_cudastereo;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudastereo/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudastereo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_cudastereo NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_highgui "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_highguid.exe" "--gtest_output=xml:opencv_test_highgui.xml")
  set_tests_properties(opencv_test_highgui PROPERTIES  LABELS "Main;opencv_highgui;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/highgui/CMakeLists.txt;309;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/highgui/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_highgui "D:/AI/opencv/cudabuild/bin/Release/opencv_test_highgui.exe" "--gtest_output=xml:opencv_test_highgui.xml")
  set_tests_properties(opencv_test_highgui PROPERTIES  LABELS "Main;opencv_highgui;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/highgui/CMakeLists.txt;309;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/highgui/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_highgui NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_mcc "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_mccd.exe" "--gtest_output=xml:opencv_test_mcc.xml")
  set_tests_properties(opencv_test_mcc PROPERTIES  LABELS "Extra;opencv_mcc;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/mcc/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/mcc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_mcc "D:/AI/opencv/cudabuild/bin/Release/opencv_test_mcc.exe" "--gtest_output=xml:opencv_test_mcc.xml")
  set_tests_properties(opencv_test_mcc PROPERTIES  LABELS "Extra;opencv_mcc;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/mcc/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/mcc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_mcc NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_mcc "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_mccd.exe" "--gtest_output=xml:opencv_perf_mcc.xml")
  set_tests_properties(opencv_perf_mcc PROPERTIES  LABELS "Extra;opencv_mcc;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/mcc/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/mcc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_mcc "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_mcc.exe" "--gtest_output=xml:opencv_perf_mcc.xml")
  set_tests_properties(opencv_perf_mcc PROPERTIES  LABELS "Extra;opencv_mcc;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/mcc/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/mcc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_mcc NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_mcc "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_mccd.exe" "--gtest_output=xml:opencv_perf_mcc.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_mcc PROPERTIES  LABELS "Extra;opencv_mcc;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/mcc/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/mcc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_mcc "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_mcc.exe" "--gtest_output=xml:opencv_perf_mcc.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_mcc PROPERTIES  LABELS "Extra;opencv_mcc;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/mcc/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/mcc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_mcc NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_objdetect "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_objdetectd.exe" "--gtest_output=xml:opencv_test_objdetect.xml")
  set_tests_properties(opencv_test_objdetect PROPERTIES  LABELS "Main;opencv_objdetect;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/objdetect/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/objdetect/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_objdetect "D:/AI/opencv/cudabuild/bin/Release/opencv_test_objdetect.exe" "--gtest_output=xml:opencv_test_objdetect.xml")
  set_tests_properties(opencv_test_objdetect PROPERTIES  LABELS "Main;opencv_objdetect;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/objdetect/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/objdetect/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_objdetect NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_objdetect "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_objdetectd.exe" "--gtest_output=xml:opencv_perf_objdetect.xml")
  set_tests_properties(opencv_perf_objdetect PROPERTIES  LABELS "Main;opencv_objdetect;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/objdetect/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/objdetect/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_objdetect "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_objdetect.exe" "--gtest_output=xml:opencv_perf_objdetect.xml")
  set_tests_properties(opencv_perf_objdetect PROPERTIES  LABELS "Main;opencv_objdetect;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/objdetect/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/objdetect/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_objdetect NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_objdetect "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_objdetectd.exe" "--gtest_output=xml:opencv_perf_objdetect.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_objdetect PROPERTIES  LABELS "Main;opencv_objdetect;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/objdetect/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/objdetect/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_objdetect "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_objdetect.exe" "--gtest_output=xml:opencv_perf_objdetect.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_objdetect PROPERTIES  LABELS "Main;opencv_objdetect;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/objdetect/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/objdetect/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_objdetect NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_rapid "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_rapidd.exe" "--gtest_output=xml:opencv_test_rapid.xml")
  set_tests_properties(opencv_test_rapid PROPERTIES  LABELS "Extra;opencv_rapid;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/rapid/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/rapid/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_rapid "D:/AI/opencv/cudabuild/bin/Release/opencv_test_rapid.exe" "--gtest_output=xml:opencv_test_rapid.xml")
  set_tests_properties(opencv_test_rapid PROPERTIES  LABELS "Extra;opencv_rapid;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/rapid/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/rapid/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_rapid NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_rgbd "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_rgbdd.exe" "--gtest_output=xml:opencv_test_rgbd.xml")
  set_tests_properties(opencv_test_rgbd PROPERTIES  LABELS "Extra;opencv_rgbd;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/rgbd/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/rgbd/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_rgbd "D:/AI/opencv/cudabuild/bin/Release/opencv_test_rgbd.exe" "--gtest_output=xml:opencv_test_rgbd.xml")
  set_tests_properties(opencv_test_rgbd PROPERTIES  LABELS "Extra;opencv_rgbd;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/rgbd/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/rgbd/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_rgbd NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_rgbd "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_rgbdd.exe" "--gtest_output=xml:opencv_perf_rgbd.xml")
  set_tests_properties(opencv_perf_rgbd PROPERTIES  LABELS "Extra;opencv_rgbd;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/rgbd/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/rgbd/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_rgbd "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_rgbd.exe" "--gtest_output=xml:opencv_perf_rgbd.xml")
  set_tests_properties(opencv_perf_rgbd PROPERTIES  LABELS "Extra;opencv_rgbd;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/rgbd/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/rgbd/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_rgbd NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_rgbd "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_rgbdd.exe" "--gtest_output=xml:opencv_perf_rgbd.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_rgbd PROPERTIES  LABELS "Extra;opencv_rgbd;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/rgbd/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/rgbd/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_rgbd "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_rgbd.exe" "--gtest_output=xml:opencv_perf_rgbd.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_rgbd PROPERTIES  LABELS "Extra;opencv_rgbd;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/rgbd/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/rgbd/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_rgbd NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_shape "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_shaped.exe" "--gtest_output=xml:opencv_test_shape.xml")
  set_tests_properties(opencv_test_shape PROPERTIES  LABELS "Extra;opencv_shape;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/shape/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/shape/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_shape "D:/AI/opencv/cudabuild/bin/Release/opencv_test_shape.exe" "--gtest_output=xml:opencv_test_shape.xml")
  set_tests_properties(opencv_test_shape PROPERTIES  LABELS "Extra;opencv_shape;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/shape/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/shape/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_shape NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_structured_light "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_structured_lightd.exe" "--gtest_output=xml:opencv_test_structured_light.xml")
  set_tests_properties(opencv_test_structured_light PROPERTIES  LABELS "Extra;opencv_structured_light;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/structured_light/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/structured_light/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_structured_light "D:/AI/opencv/cudabuild/bin/Release/opencv_test_structured_light.exe" "--gtest_output=xml:opencv_test_structured_light.xml")
  set_tests_properties(opencv_test_structured_light PROPERTIES  LABELS "Extra;opencv_structured_light;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/structured_light/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/structured_light/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_structured_light NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_video "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_videod.exe" "--gtest_output=xml:opencv_test_video.xml")
  set_tests_properties(opencv_test_video PROPERTIES  LABELS "Main;opencv_video;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/video/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/video/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_video "D:/AI/opencv/cudabuild/bin/Release/opencv_test_video.exe" "--gtest_output=xml:opencv_test_video.xml")
  set_tests_properties(opencv_test_video PROPERTIES  LABELS "Main;opencv_video;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/video/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/video/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_video NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_video "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_videod.exe" "--gtest_output=xml:opencv_perf_video.xml")
  set_tests_properties(opencv_perf_video PROPERTIES  LABELS "Main;opencv_video;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/video/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/video/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_video "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_video.exe" "--gtest_output=xml:opencv_perf_video.xml")
  set_tests_properties(opencv_perf_video PROPERTIES  LABELS "Main;opencv_video;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/video/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/video/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_video NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_video "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_videod.exe" "--gtest_output=xml:opencv_perf_video.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_video PROPERTIES  LABELS "Main;opencv_video;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/video/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/video/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_video "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_video.exe" "--gtest_output=xml:opencv_perf_video.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_video PROPERTIES  LABELS "Main;opencv_video;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/video/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/video/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_video NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_wechat_qrcode "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_wechat_qrcoded.exe" "--gtest_output=xml:opencv_test_wechat_qrcode.xml")
  set_tests_properties(opencv_test_wechat_qrcode PROPERTIES  LABELS "Extra;opencv_wechat_qrcode;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/wechat_qrcode/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/wechat_qrcode/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_wechat_qrcode "D:/AI/opencv/cudabuild/bin/Release/opencv_test_wechat_qrcode.exe" "--gtest_output=xml:opencv_test_wechat_qrcode.xml")
  set_tests_properties(opencv_test_wechat_qrcode PROPERTIES  LABELS "Extra;opencv_wechat_qrcode;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/wechat_qrcode/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/wechat_qrcode/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_wechat_qrcode NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_wechat_qrcode "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_wechat_qrcoded.exe" "--gtest_output=xml:opencv_perf_wechat_qrcode.xml")
  set_tests_properties(opencv_perf_wechat_qrcode PROPERTIES  LABELS "Extra;opencv_wechat_qrcode;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/wechat_qrcode/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/wechat_qrcode/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_wechat_qrcode "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_wechat_qrcode.exe" "--gtest_output=xml:opencv_perf_wechat_qrcode.xml")
  set_tests_properties(opencv_perf_wechat_qrcode PROPERTIES  LABELS "Extra;opencv_wechat_qrcode;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/wechat_qrcode/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/wechat_qrcode/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_wechat_qrcode NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_wechat_qrcode "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_wechat_qrcoded.exe" "--gtest_output=xml:opencv_perf_wechat_qrcode.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_wechat_qrcode PROPERTIES  LABELS "Extra;opencv_wechat_qrcode;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/wechat_qrcode/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/wechat_qrcode/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_wechat_qrcode "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_wechat_qrcode.exe" "--gtest_output=xml:opencv_perf_wechat_qrcode.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_wechat_qrcode PROPERTIES  LABELS "Extra;opencv_wechat_qrcode;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/wechat_qrcode/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/wechat_qrcode/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_wechat_qrcode NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_xfeatures2d "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_xfeatures2dd.exe" "--gtest_output=xml:opencv_test_xfeatures2d.xml")
  set_tests_properties(opencv_test_xfeatures2d PROPERTIES  LABELS "Extra;opencv_xfeatures2d;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/xfeatures2d/CMakeLists.txt;6;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/xfeatures2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_xfeatures2d "D:/AI/opencv/cudabuild/bin/Release/opencv_test_xfeatures2d.exe" "--gtest_output=xml:opencv_test_xfeatures2d.xml")
  set_tests_properties(opencv_test_xfeatures2d PROPERTIES  LABELS "Extra;opencv_xfeatures2d;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/xfeatures2d/CMakeLists.txt;6;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/xfeatures2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_xfeatures2d NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_xfeatures2d "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_xfeatures2dd.exe" "--gtest_output=xml:opencv_perf_xfeatures2d.xml")
  set_tests_properties(opencv_perf_xfeatures2d PROPERTIES  LABELS "Extra;opencv_xfeatures2d;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/xfeatures2d/CMakeLists.txt;6;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/xfeatures2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_xfeatures2d "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_xfeatures2d.exe" "--gtest_output=xml:opencv_perf_xfeatures2d.xml")
  set_tests_properties(opencv_perf_xfeatures2d PROPERTIES  LABELS "Extra;opencv_xfeatures2d;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/xfeatures2d/CMakeLists.txt;6;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/xfeatures2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_xfeatures2d NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_xfeatures2d "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_xfeatures2dd.exe" "--gtest_output=xml:opencv_perf_xfeatures2d.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_xfeatures2d PROPERTIES  LABELS "Extra;opencv_xfeatures2d;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/xfeatures2d/CMakeLists.txt;6;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/xfeatures2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_xfeatures2d "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_xfeatures2d.exe" "--gtest_output=xml:opencv_perf_xfeatures2d.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_xfeatures2d PROPERTIES  LABELS "Extra;opencv_xfeatures2d;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/xfeatures2d/CMakeLists.txt;6;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/xfeatures2d/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_xfeatures2d NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_ximgproc "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_ximgprocd.exe" "--gtest_output=xml:opencv_test_ximgproc.xml")
  set_tests_properties(opencv_test_ximgproc PROPERTIES  LABELS "Extra;opencv_ximgproc;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_ximgproc "D:/AI/opencv/cudabuild/bin/Release/opencv_test_ximgproc.exe" "--gtest_output=xml:opencv_test_ximgproc.xml")
  set_tests_properties(opencv_test_ximgproc PROPERTIES  LABELS "Extra;opencv_ximgproc;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_ximgproc NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_ximgproc "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_ximgprocd.exe" "--gtest_output=xml:opencv_perf_ximgproc.xml")
  set_tests_properties(opencv_perf_ximgproc PROPERTIES  LABELS "Extra;opencv_ximgproc;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_ximgproc "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_ximgproc.exe" "--gtest_output=xml:opencv_perf_ximgproc.xml")
  set_tests_properties(opencv_perf_ximgproc PROPERTIES  LABELS "Extra;opencv_ximgproc;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_ximgproc NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_ximgproc "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_ximgprocd.exe" "--gtest_output=xml:opencv_perf_ximgproc.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_ximgproc PROPERTIES  LABELS "Extra;opencv_ximgproc;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_ximgproc "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_ximgproc.exe" "--gtest_output=xml:opencv_perf_ximgproc.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_ximgproc PROPERTIES  LABELS "Extra;opencv_ximgproc;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_ximgproc NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_bgsegm "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_bgsegmd.exe" "--gtest_output=xml:opencv_test_bgsegm.xml")
  set_tests_properties(opencv_test_bgsegm PROPERTIES  LABELS "Extra;opencv_bgsegm;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/bgsegm/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/bgsegm/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_bgsegm "D:/AI/opencv/cudabuild/bin/Release/opencv_test_bgsegm.exe" "--gtest_output=xml:opencv_test_bgsegm.xml")
  set_tests_properties(opencv_test_bgsegm PROPERTIES  LABELS "Extra;opencv_bgsegm;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/bgsegm/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/bgsegm/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_bgsegm NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_bioinspired "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_bioinspiredd.exe" "--gtest_output=xml:opencv_test_bioinspired.xml")
  set_tests_properties(opencv_test_bioinspired PROPERTIES  LABELS "Extra;opencv_bioinspired;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/bioinspired/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/bioinspired/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_bioinspired "D:/AI/opencv/cudabuild/bin/Release/opencv_test_bioinspired.exe" "--gtest_output=xml:opencv_test_bioinspired.xml")
  set_tests_properties(opencv_test_bioinspired PROPERTIES  LABELS "Extra;opencv_bioinspired;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/bioinspired/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/bioinspired/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_bioinspired NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_bioinspired "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_bioinspiredd.exe" "--gtest_output=xml:opencv_perf_bioinspired.xml")
  set_tests_properties(opencv_perf_bioinspired PROPERTIES  LABELS "Extra;opencv_bioinspired;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/bioinspired/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/bioinspired/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_bioinspired "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_bioinspired.exe" "--gtest_output=xml:opencv_perf_bioinspired.xml")
  set_tests_properties(opencv_perf_bioinspired PROPERTIES  LABELS "Extra;opencv_bioinspired;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/bioinspired/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/bioinspired/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_bioinspired NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_bioinspired "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_bioinspiredd.exe" "--gtest_output=xml:opencv_perf_bioinspired.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_bioinspired PROPERTIES  LABELS "Extra;opencv_bioinspired;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/bioinspired/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/bioinspired/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_bioinspired "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_bioinspired.exe" "--gtest_output=xml:opencv_perf_bioinspired.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_bioinspired PROPERTIES  LABELS "Extra;opencv_bioinspired;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/bioinspired/CMakeLists.txt;3;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/bioinspired/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_bioinspired NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_cudabgsegm "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_cudabgsegmd.exe" "--gtest_output=xml:opencv_test_cudabgsegm.xml")
  set_tests_properties(opencv_test_cudabgsegm PROPERTIES  LABELS "Extra;opencv_cudabgsegm;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudabgsegm/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudabgsegm/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_cudabgsegm "D:/AI/opencv/cudabuild/bin/Release/opencv_test_cudabgsegm.exe" "--gtest_output=xml:opencv_test_cudabgsegm.xml")
  set_tests_properties(opencv_test_cudabgsegm PROPERTIES  LABELS "Extra;opencv_cudabgsegm;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudabgsegm/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudabgsegm/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_cudabgsegm NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_cudabgsegm "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudabgsegmd.exe" "--gtest_output=xml:opencv_perf_cudabgsegm.xml")
  set_tests_properties(opencv_perf_cudabgsegm PROPERTIES  LABELS "Extra;opencv_cudabgsegm;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudabgsegm/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudabgsegm/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_cudabgsegm "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudabgsegm.exe" "--gtest_output=xml:opencv_perf_cudabgsegm.xml")
  set_tests_properties(opencv_perf_cudabgsegm PROPERTIES  LABELS "Extra;opencv_cudabgsegm;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudabgsegm/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudabgsegm/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_cudabgsegm NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_cudabgsegm "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudabgsegmd.exe" "--gtest_output=xml:opencv_perf_cudabgsegm.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudabgsegm PROPERTIES  LABELS "Extra;opencv_cudabgsegm;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudabgsegm/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudabgsegm/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_cudabgsegm "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudabgsegm.exe" "--gtest_output=xml:opencv_perf_cudabgsegm.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudabgsegm PROPERTIES  LABELS "Extra;opencv_cudabgsegm;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudabgsegm/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudabgsegm/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_cudabgsegm NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_cudalegacy "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_cudalegacyd.exe" "--gtest_output=xml:opencv_test_cudalegacy.xml")
  set_tests_properties(opencv_test_cudalegacy PROPERTIES  LABELS "Extra;opencv_cudalegacy;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudalegacy/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudalegacy/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_cudalegacy "D:/AI/opencv/cudabuild/bin/Release/opencv_test_cudalegacy.exe" "--gtest_output=xml:opencv_test_cudalegacy.xml")
  set_tests_properties(opencv_test_cudalegacy PROPERTIES  LABELS "Extra;opencv_cudalegacy;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudalegacy/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudalegacy/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_cudalegacy NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_cudalegacy "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudalegacyd.exe" "--gtest_output=xml:opencv_perf_cudalegacy.xml")
  set_tests_properties(opencv_perf_cudalegacy PROPERTIES  LABELS "Extra;opencv_cudalegacy;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudalegacy/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudalegacy/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_cudalegacy "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudalegacy.exe" "--gtest_output=xml:opencv_perf_cudalegacy.xml")
  set_tests_properties(opencv_perf_cudalegacy PROPERTIES  LABELS "Extra;opencv_cudalegacy;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudalegacy/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudalegacy/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_cudalegacy NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_cudalegacy "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudalegacyd.exe" "--gtest_output=xml:opencv_perf_cudalegacy.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudalegacy PROPERTIES  LABELS "Extra;opencv_cudalegacy;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudalegacy/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudalegacy/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_cudalegacy "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudalegacy.exe" "--gtest_output=xml:opencv_perf_cudalegacy.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudalegacy PROPERTIES  LABELS "Extra;opencv_cudalegacy;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudalegacy/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudalegacy/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_cudalegacy NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_cudaobjdetect "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_cudaobjdetectd.exe" "--gtest_output=xml:opencv_test_cudaobjdetect.xml")
  set_tests_properties(opencv_test_cudaobjdetect PROPERTIES  LABELS "Extra;opencv_cudaobjdetect;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaobjdetect/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaobjdetect/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_cudaobjdetect "D:/AI/opencv/cudabuild/bin/Release/opencv_test_cudaobjdetect.exe" "--gtest_output=xml:opencv_test_cudaobjdetect.xml")
  set_tests_properties(opencv_test_cudaobjdetect PROPERTIES  LABELS "Extra;opencv_cudaobjdetect;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaobjdetect/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaobjdetect/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_cudaobjdetect NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_cudaobjdetect "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudaobjdetectd.exe" "--gtest_output=xml:opencv_perf_cudaobjdetect.xml")
  set_tests_properties(opencv_perf_cudaobjdetect PROPERTIES  LABELS "Extra;opencv_cudaobjdetect;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaobjdetect/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaobjdetect/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_cudaobjdetect "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudaobjdetect.exe" "--gtest_output=xml:opencv_perf_cudaobjdetect.xml")
  set_tests_properties(opencv_perf_cudaobjdetect PROPERTIES  LABELS "Extra;opencv_cudaobjdetect;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaobjdetect/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaobjdetect/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_cudaobjdetect NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_cudaobjdetect "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudaobjdetectd.exe" "--gtest_output=xml:opencv_perf_cudaobjdetect.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudaobjdetect PROPERTIES  LABELS "Extra;opencv_cudaobjdetect;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaobjdetect/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaobjdetect/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_cudaobjdetect "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudaobjdetect.exe" "--gtest_output=xml:opencv_perf_cudaobjdetect.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudaobjdetect PROPERTIES  LABELS "Extra;opencv_cudaobjdetect;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaobjdetect/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaobjdetect/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_cudaobjdetect NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_face "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_faced.exe" "--gtest_output=xml:opencv_test_face.xml")
  set_tests_properties(opencv_test_face PROPERTIES  LABELS "Extra;opencv_face;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/face/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/face/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_face "D:/AI/opencv/cudabuild/bin/Release/opencv_test_face.exe" "--gtest_output=xml:opencv_test_face.xml")
  set_tests_properties(opencv_test_face PROPERTIES  LABELS "Extra;opencv_face;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/face/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/face/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_face NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_gapi "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_gapid.exe" "--gtest_output=xml:opencv_test_gapi.xml")
  set_tests_properties(opencv_test_gapi PROPERTIES  LABELS "Main;opencv_gapi;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/gapi/CMakeLists.txt;292;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/gapi/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_gapi "D:/AI/opencv/cudabuild/bin/Release/opencv_test_gapi.exe" "--gtest_output=xml:opencv_test_gapi.xml")
  set_tests_properties(opencv_test_gapi PROPERTIES  LABELS "Main;opencv_gapi;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/gapi/CMakeLists.txt;292;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/gapi/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_gapi NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_gapi "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_gapid.exe" "--gtest_output=xml:opencv_perf_gapi.xml")
  set_tests_properties(opencv_perf_gapi PROPERTIES  LABELS "Main;opencv_gapi;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/gapi/CMakeLists.txt;401;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/gapi/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_gapi "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_gapi.exe" "--gtest_output=xml:opencv_perf_gapi.xml")
  set_tests_properties(opencv_perf_gapi PROPERTIES  LABELS "Main;opencv_gapi;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/gapi/CMakeLists.txt;401;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/gapi/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_gapi NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_gapi "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_gapid.exe" "--gtest_output=xml:opencv_perf_gapi.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_gapi PROPERTIES  LABELS "Main;opencv_gapi;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/gapi/CMakeLists.txt;401;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/gapi/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_gapi "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_gapi.exe" "--gtest_output=xml:opencv_perf_gapi.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_gapi PROPERTIES  LABELS "Main;opencv_gapi;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/modules/gapi/CMakeLists.txt;401;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/gapi/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_gapi NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_optflow "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_optflowd.exe" "--gtest_output=xml:opencv_test_optflow.xml")
  set_tests_properties(opencv_test_optflow PROPERTIES  LABELS "Extra;opencv_optflow;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/optflow/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/optflow/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_optflow "D:/AI/opencv/cudabuild/bin/Release/opencv_test_optflow.exe" "--gtest_output=xml:opencv_test_optflow.xml")
  set_tests_properties(opencv_test_optflow PROPERTIES  LABELS "Extra;opencv_optflow;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/optflow/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/optflow/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_optflow NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_optflow "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_optflowd.exe" "--gtest_output=xml:opencv_perf_optflow.xml")
  set_tests_properties(opencv_perf_optflow PROPERTIES  LABELS "Extra;opencv_optflow;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/optflow/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/optflow/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_optflow "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_optflow.exe" "--gtest_output=xml:opencv_perf_optflow.xml")
  set_tests_properties(opencv_perf_optflow PROPERTIES  LABELS "Extra;opencv_optflow;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/optflow/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/optflow/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_optflow NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_optflow "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_optflowd.exe" "--gtest_output=xml:opencv_perf_optflow.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_optflow PROPERTIES  LABELS "Extra;opencv_optflow;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/optflow/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/optflow/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_optflow "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_optflow.exe" "--gtest_output=xml:opencv_perf_optflow.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_optflow PROPERTIES  LABELS "Extra;opencv_optflow;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/optflow/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/optflow/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_optflow NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_stitching "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_stitchingd.exe" "--gtest_output=xml:opencv_test_stitching.xml")
  set_tests_properties(opencv_test_stitching PROPERTIES  LABELS "Main;opencv_stitching;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/stitching/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/stitching/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_stitching "D:/AI/opencv/cudabuild/bin/Release/opencv_test_stitching.exe" "--gtest_output=xml:opencv_test_stitching.xml")
  set_tests_properties(opencv_test_stitching PROPERTIES  LABELS "Main;opencv_stitching;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv-4.10.0/modules/stitching/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/stitching/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_stitching NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_stitching "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_stitchingd.exe" "--gtest_output=xml:opencv_perf_stitching.xml")
  set_tests_properties(opencv_perf_stitching PROPERTIES  LABELS "Main;opencv_stitching;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/stitching/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/stitching/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_stitching "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_stitching.exe" "--gtest_output=xml:opencv_perf_stitching.xml")
  set_tests_properties(opencv_perf_stitching PROPERTIES  LABELS "Main;opencv_stitching;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/stitching/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/stitching/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_stitching NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_stitching "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_stitchingd.exe" "--gtest_output=xml:opencv_perf_stitching.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_stitching PROPERTIES  LABELS "Main;opencv_stitching;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/stitching/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/stitching/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_stitching "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_stitching.exe" "--gtest_output=xml:opencv_perf_stitching.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_stitching PROPERTIES  LABELS "Main;opencv_stitching;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv-4.10.0/modules/stitching/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv-4.10.0/modules/stitching/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_stitching NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_tracking "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_trackingd.exe" "--gtest_output=xml:opencv_test_tracking.xml")
  set_tests_properties(opencv_test_tracking PROPERTIES  LABELS "Extra;opencv_tracking;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/tracking/CMakeLists.txt;8;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/tracking/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_tracking "D:/AI/opencv/cudabuild/bin/Release/opencv_test_tracking.exe" "--gtest_output=xml:opencv_test_tracking.xml")
  set_tests_properties(opencv_test_tracking PROPERTIES  LABELS "Extra;opencv_tracking;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/tracking/CMakeLists.txt;8;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/tracking/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_tracking NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_tracking "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_trackingd.exe" "--gtest_output=xml:opencv_perf_tracking.xml")
  set_tests_properties(opencv_perf_tracking PROPERTIES  LABELS "Extra;opencv_tracking;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/tracking/CMakeLists.txt;8;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/tracking/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_tracking "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_tracking.exe" "--gtest_output=xml:opencv_perf_tracking.xml")
  set_tests_properties(opencv_perf_tracking PROPERTIES  LABELS "Extra;opencv_tracking;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/tracking/CMakeLists.txt;8;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/tracking/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_tracking NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_tracking "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_trackingd.exe" "--gtest_output=xml:opencv_perf_tracking.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_tracking PROPERTIES  LABELS "Extra;opencv_tracking;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/tracking/CMakeLists.txt;8;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/tracking/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_tracking "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_tracking.exe" "--gtest_output=xml:opencv_perf_tracking.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_tracking PROPERTIES  LABELS "Extra;opencv_tracking;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/tracking/CMakeLists.txt;8;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/tracking/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_tracking NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_cudaoptflow "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_cudaoptflowd.exe" "--gtest_output=xml:opencv_test_cudaoptflow.xml")
  set_tests_properties(opencv_test_cudaoptflow PROPERTIES  LABELS "Extra;opencv_cudaoptflow;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaoptflow/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaoptflow/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_cudaoptflow "D:/AI/opencv/cudabuild/bin/Release/opencv_test_cudaoptflow.exe" "--gtest_output=xml:opencv_test_cudaoptflow.xml")
  set_tests_properties(opencv_test_cudaoptflow PROPERTIES  LABELS "Extra;opencv_cudaoptflow;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaoptflow/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaoptflow/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_cudaoptflow NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_cudaoptflow "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudaoptflowd.exe" "--gtest_output=xml:opencv_perf_cudaoptflow.xml")
  set_tests_properties(opencv_perf_cudaoptflow PROPERTIES  LABELS "Extra;opencv_cudaoptflow;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaoptflow/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaoptflow/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_cudaoptflow "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudaoptflow.exe" "--gtest_output=xml:opencv_perf_cudaoptflow.xml")
  set_tests_properties(opencv_perf_cudaoptflow PROPERTIES  LABELS "Extra;opencv_cudaoptflow;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaoptflow/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaoptflow/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_cudaoptflow NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_cudaoptflow "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_cudaoptflowd.exe" "--gtest_output=xml:opencv_perf_cudaoptflow.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudaoptflow PROPERTIES  LABELS "Extra;opencv_cudaoptflow;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaoptflow/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaoptflow/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_cudaoptflow "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_cudaoptflow.exe" "--gtest_output=xml:opencv_perf_cudaoptflow.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_cudaoptflow PROPERTIES  LABELS "Extra;opencv_cudaoptflow;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaoptflow/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaoptflow/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_cudaoptflow NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_stereo "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_stereod.exe" "--gtest_output=xml:opencv_test_stereo.xml")
  set_tests_properties(opencv_test_stereo PROPERTIES  LABELS "Extra;opencv_stereo;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/stereo/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/stereo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_stereo "D:/AI/opencv/cudabuild/bin/Release/opencv_test_stereo.exe" "--gtest_output=xml:opencv_test_stereo.xml")
  set_tests_properties(opencv_test_stereo PROPERTIES  LABELS "Extra;opencv_stereo;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/stereo/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/stereo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_stereo NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_stereo "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_stereod.exe" "--gtest_output=xml:opencv_perf_stereo.xml")
  set_tests_properties(opencv_perf_stereo PROPERTIES  LABELS "Extra;opencv_stereo;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/stereo/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/stereo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_stereo "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_stereo.exe" "--gtest_output=xml:opencv_perf_stereo.xml")
  set_tests_properties(opencv_perf_stereo PROPERTIES  LABELS "Extra;opencv_stereo;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/stereo/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/stereo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_stereo NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_stereo "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_stereod.exe" "--gtest_output=xml:opencv_perf_stereo.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_stereo PROPERTIES  LABELS "Extra;opencv_stereo;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/stereo/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/stereo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_stereo "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_stereo.exe" "--gtest_output=xml:opencv_perf_stereo.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_stereo PROPERTIES  LABELS "Extra;opencv_stereo;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/stereo/CMakeLists.txt;2;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/stereo/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_stereo NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_superres "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_superresd.exe" "--gtest_output=xml:opencv_test_superres.xml")
  set_tests_properties(opencv_test_superres PROPERTIES  LABELS "Extra;opencv_superres;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/superres/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/superres/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_superres "D:/AI/opencv/cudabuild/bin/Release/opencv_test_superres.exe" "--gtest_output=xml:opencv_test_superres.xml")
  set_tests_properties(opencv_test_superres PROPERTIES  LABELS "Extra;opencv_superres;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/superres/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/superres/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_superres NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_perf_superres "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_superresd.exe" "--gtest_output=xml:opencv_perf_superres.xml")
  set_tests_properties(opencv_perf_superres PROPERTIES  LABELS "Extra;opencv_superres;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/superres/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/superres/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_perf_superres "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_superres.exe" "--gtest_output=xml:opencv_perf_superres.xml")
  set_tests_properties(opencv_perf_superres PROPERTIES  LABELS "Extra;opencv_superres;Performance" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/performance" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1274;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/superres/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/superres/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_perf_superres NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_sanity_superres "D:/AI/opencv/cudabuild/bin/Debug/opencv_perf_superresd.exe" "--gtest_output=xml:opencv_perf_superres.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_superres PROPERTIES  LABELS "Extra;opencv_superres;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/superres/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/superres/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_sanity_superres "D:/AI/opencv/cudabuild/bin/Release/opencv_perf_superres.exe" "--gtest_output=xml:opencv_perf_superres.xml" "--perf_min_samples=1" "--perf_force_samples=1" "--perf_verify_sanity")
  set_tests_properties(opencv_sanity_superres PROPERTIES  LABELS "Extra;opencv_superres;Sanity" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/sanity" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1275;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1134;ocv_add_perf_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/superres/CMakeLists.txt;9;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/superres/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_sanity_superres NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(opencv_test_videostab "D:/AI/opencv/cudabuild/bin/Debug/opencv_test_videostabd.exe" "--gtest_output=xml:opencv_test_videostab.xml")
  set_tests_properties(opencv_test_videostab PROPERTIES  LABELS "Extra;opencv_videostab;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/videostab/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/videostab/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(opencv_test_videostab "D:/AI/opencv/cudabuild/bin/Release/opencv_test_videostab.exe" "--gtest_output=xml:opencv_test_videostab.xml")
  set_tests_properties(opencv_test_videostab PROPERTIES  LABELS "Extra;opencv_videostab;Accuracy" WORKING_DIRECTORY "D:/AI/opencv/cudabuild/test-reports/accuracy" _BACKTRACE_TRIPLES "D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake;1795;add_test;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1375;ocv_add_test_from_target;D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake;1133;ocv_add_accuracy_tests;D:/AI/opencv/opencv_contrib-4.10.0/modules/videostab/CMakeLists.txt;11;ocv_define_module;D:/AI/opencv/opencv_contrib-4.10.0/modules/videostab/CMakeLists.txt;0;;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;13;include;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;50;include_one_module;D:/AI/opencv/opencv-4.10.0/modules/world/CMakeLists.txt;0;")
else()
  add_test(opencv_test_videostab NOT_AVAILABLE)
endif()
subdirs("tools")
