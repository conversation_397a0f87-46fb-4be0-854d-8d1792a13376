// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_System_Profile_1_H
#define WINRT_Windows_System_Profile_1_H
#include "winrt/impl/Windows.System.Profile.0.h"
WINRT_EXPORT namespace winrt::Windows::System::Profile
{
    struct WINRT_IMPL_EMPTY_BASES IAnalyticsInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnalyticsInfoStatics>
    {
        IAnalyticsInfoStatics(std::nullptr_t = nullptr) noexcept {}
        IAnalyticsInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAnalyticsInfoStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnalyticsInfoStatics2>
    {
        IAnalyticsInfoStatics2(std::nullptr_t = nullptr) noexcept {}
        IAnalyticsInfoStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAnalyticsVersionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnalyticsVersionInfo>
    {
        IAnalyticsVersionInfo(std::nullptr_t = nullptr) noexcept {}
        IAnalyticsVersionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAnalyticsVersionInfo2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnalyticsVersionInfo2>
    {
        IAnalyticsVersionInfo2(std::nullptr_t = nullptr) noexcept {}
        IAnalyticsVersionInfo2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppApplicabilityStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppApplicabilityStatics>
    {
        IAppApplicabilityStatics(std::nullptr_t = nullptr) noexcept {}
        IAppApplicabilityStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEducationSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEducationSettingsStatics>
    {
        IEducationSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        IEducationSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHardwareIdentificationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHardwareIdentificationStatics>
    {
        IHardwareIdentificationStatics(std::nullptr_t = nullptr) noexcept {}
        IHardwareIdentificationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHardwareToken :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHardwareToken>
    {
        IHardwareToken(std::nullptr_t = nullptr) noexcept {}
        IHardwareToken(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKnownRetailInfoPropertiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownRetailInfoPropertiesStatics>
    {
        IKnownRetailInfoPropertiesStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownRetailInfoPropertiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPlatformAutomaticAppSignInManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPlatformAutomaticAppSignInManagerStatics>
    {
        IPlatformAutomaticAppSignInManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IPlatformAutomaticAppSignInManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPlatformDiagnosticsAndUsageDataSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPlatformDiagnosticsAndUsageDataSettingsStatics>
    {
        IPlatformDiagnosticsAndUsageDataSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        IPlatformDiagnosticsAndUsageDataSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRetailInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRetailInfoStatics>
    {
        IRetailInfoStatics(std::nullptr_t = nullptr) noexcept {}
        IRetailInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISharedModeSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISharedModeSettingsStatics>
    {
        ISharedModeSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        ISharedModeSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISharedModeSettingsStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISharedModeSettingsStatics2>
    {
        ISharedModeSettingsStatics2(std::nullptr_t = nullptr) noexcept {}
        ISharedModeSettingsStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISmartAppControlPolicyStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISmartAppControlPolicyStatics>
    {
        ISmartAppControlPolicyStatics(std::nullptr_t = nullptr) noexcept {}
        ISmartAppControlPolicyStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemIdentificationInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemIdentificationInfo>
    {
        ISystemIdentificationInfo(std::nullptr_t = nullptr) noexcept {}
        ISystemIdentificationInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemIdentificationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemIdentificationStatics>
    {
        ISystemIdentificationStatics(std::nullptr_t = nullptr) noexcept {}
        ISystemIdentificationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemSetupInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemSetupInfoStatics>
    {
        ISystemSetupInfoStatics(std::nullptr_t = nullptr) noexcept {}
        ISystemSetupInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUnsupportedAppRequirement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUnsupportedAppRequirement>
    {
        IUnsupportedAppRequirement(std::nullptr_t = nullptr) noexcept {}
        IUnsupportedAppRequirement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsIntegrityPolicyStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsIntegrityPolicyStatics>
    {
        IWindowsIntegrityPolicyStatics(std::nullptr_t = nullptr) noexcept {}
        IWindowsIntegrityPolicyStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
