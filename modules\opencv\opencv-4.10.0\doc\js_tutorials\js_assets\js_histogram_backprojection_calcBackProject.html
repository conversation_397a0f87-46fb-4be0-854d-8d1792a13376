<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>Back Project Example</title>
<link href="js_example_style.css" rel="stylesheet" type="text/css" />
</head>
<body>
<h2>Back Project Example</h2>
<p>
    &lt;canvas&gt; elements named <b>srcCanvasInput</b>, <b>dstCanvasInput</b> and <b>canvasInput</b> have been prepared.<br>
    Click <b>Try it</b> button to see the result. You can choose another image.<br>
    You can change the code in the &lt;textarea&gt; to investigate more.
</p>
<div>
<div class="control"><button id="tryIt" disabled>Try it</button></div>
<textarea class="code" rows="9" cols="100" id="codeEditor" spellcheck="false">
</textarea>
<p class="err" id="errorMessage"></p>
</div>
<div>
    <table cellpadding="0" cellspacing="0" width="0" border="0">
    <tr>
        <td>
            <canvas id="srcCanvasInput" class="small"></canvas>
        </td>
        <td>
            <canvas id="dstCanvasInput" class="small"></canvas>
        </td>
        <td>
            <canvas id="canvasOutput" class="small"></canvas>
        </td>
    </tr>
    <tr>
        <td>
            <div class="caption">srcCanvasInput <input type="file" id="srcFileInput" name="file" accept="image/*" /></div>
        </td>
        <td>
            <div class="caption">dstCanvasInput <input type="file" id="dstFileInput" name="file" accept="image/*" /></div>
        </td>
        <td>
            <div class="caption">canvasOutput</div>
        </td>
    </tr>
    </table>
</div>
<script src="utils.js" type="text/javascript"></script>
<script id="codeSnippet" type="text/code-snippet">
let src = cv.imread('srcCanvasInput');
let dst = cv.imread('dstCanvasInput');
cv.cvtColor(src, src, cv.COLOR_RGB2HSV, 0);
cv.cvtColor(dst, dst, cv.COLOR_RGB2HSV, 0);
let srcVec = new cv.MatVector();
let dstVec = new cv.MatVector();
srcVec.push_back(src); dstVec.push_back(dst);
let backproj = new cv.Mat();
let none = new cv.Mat();
let mask = new cv.Mat();
let hist = new cv.Mat();
let channels = [0];
let histSize = [50];
let ranges = [0, 180];
let accumulate = false;
cv.calcHist(srcVec, channels, mask, hist, histSize, ranges, accumulate);
cv.normalize(hist, hist, 0, 255, cv.NORM_MINMAX, -1, none);
cv.calcBackProject(dstVec, channels, hist, backproj, ranges, 1);
cv.imshow('canvasOutput', backproj);
src.delete(); dst.delete(); srcVec.delete(); dstVec.delete();
backproj.delete(); mask.delete(); hist.delete(); none.delete();
</script>
<script type="text/javascript">
let utils = new Utils('errorMessage');

utils.loadCode('codeSnippet', 'codeEditor');
utils.loadImageToCanvas('handSrc.jpg', 'srcCanvasInput');
utils.loadImageToCanvas('handDst.jpg', 'dstCanvasInput');
utils.addFileInputHandler('srcFileInput', 'srcCanvasInput');
utils.addFileInputHandler('dstFileInput', 'dstCanvasInput');

let tryIt = document.getElementById('tryIt');
tryIt.addEventListener('click', () => {
    utils.executeCode('codeEditor');
});

utils.loadOpenCv(() => {
    tryIt.removeAttribute('disabled');
});
</script>
</body>
</html>
