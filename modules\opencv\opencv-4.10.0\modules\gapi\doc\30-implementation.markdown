# Implementation details {#gapi_impl}

[TOC]

# G-API Implementation details

@note this section is still in progress.

# API layer {#gapi_detail_api}

## Expression unrolling {#gapi_detail_expr}

## Parameter marshalling {#gapi_detail_params}

## Operations representation {#gapi_detail_operations}

# Graph compiler {#gapi_detail_compiler}

## ADE basics {#gapi_detail_ade}

## Graph model representation {#gapi_detail_gmodel}

## G-API metadata and passes {#gapi_detail_meta}

# Backends {#gapi_detail_backends}

## Backend scope of work {#gapi_backend_scope}

## Graph transformation {#gapi_backend_pass}
