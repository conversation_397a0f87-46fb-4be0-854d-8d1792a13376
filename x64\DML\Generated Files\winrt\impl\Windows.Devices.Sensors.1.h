// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Devices_Sensors_1_H
#define WINRT_Windows_Devices_Sensors_1_H
#include "winrt/impl/Windows.Devices.Sensors.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices::Sensors
{
    struct WINRT_IMPL_EMPTY_BASES IAccelerometer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometer>
    {
        IAccelerometer(std::nullptr_t = nullptr) noexcept {}
        IAccelerometer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccelerometer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometer2>
    {
        IAccelerometer2(std::nullptr_t = nullptr) noexcept {}
        IAccelerometer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccelerometer3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometer3>
    {
        IAccelerometer3(std::nullptr_t = nullptr) noexcept {}
        IAccelerometer3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccelerometer4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometer4>
    {
        IAccelerometer4(std::nullptr_t = nullptr) noexcept {}
        IAccelerometer4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccelerometer5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometer5>
    {
        IAccelerometer5(std::nullptr_t = nullptr) noexcept {}
        IAccelerometer5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccelerometerDataThreshold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerDataThreshold>
    {
        IAccelerometerDataThreshold(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerDataThreshold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccelerometerDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerDeviceId>
    {
        IAccelerometerDeviceId(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccelerometerReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerReading>
    {
        IAccelerometerReading(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccelerometerReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerReading2>
    {
        IAccelerometerReading2(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccelerometerReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerReadingChangedEventArgs>
    {
        IAccelerometerReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccelerometerShakenEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerShakenEventArgs>
    {
        IAccelerometerShakenEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerShakenEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccelerometerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerStatics>
    {
        IAccelerometerStatics(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccelerometerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerStatics2>
    {
        IAccelerometerStatics2(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccelerometerStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccelerometerStatics3>
    {
        IAccelerometerStatics3(std::nullptr_t = nullptr) noexcept {}
        IAccelerometerStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActivitySensor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActivitySensor>
    {
        IActivitySensor(std::nullptr_t = nullptr) noexcept {}
        IActivitySensor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActivitySensorReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActivitySensorReading>
    {
        IActivitySensorReading(std::nullptr_t = nullptr) noexcept {}
        IActivitySensorReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActivitySensorReadingChangeReport :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActivitySensorReadingChangeReport>
    {
        IActivitySensorReadingChangeReport(std::nullptr_t = nullptr) noexcept {}
        IActivitySensorReadingChangeReport(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActivitySensorReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActivitySensorReadingChangedEventArgs>
    {
        IActivitySensorReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IActivitySensorReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActivitySensorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActivitySensorStatics>
    {
        IActivitySensorStatics(std::nullptr_t = nullptr) noexcept {}
        IActivitySensorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActivitySensorTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActivitySensorTriggerDetails>
    {
        IActivitySensorTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        IActivitySensorTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAdaptiveDimmingOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdaptiveDimmingOptions>
    {
        IAdaptiveDimmingOptions(std::nullptr_t = nullptr) noexcept {}
        IAdaptiveDimmingOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAltimeter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAltimeter>
    {
        IAltimeter(std::nullptr_t = nullptr) noexcept {}
        IAltimeter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAltimeter2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAltimeter2>
    {
        IAltimeter2(std::nullptr_t = nullptr) noexcept {}
        IAltimeter2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAltimeterReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAltimeterReading>
    {
        IAltimeterReading(std::nullptr_t = nullptr) noexcept {}
        IAltimeterReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAltimeterReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAltimeterReading2>
    {
        IAltimeterReading2(std::nullptr_t = nullptr) noexcept {}
        IAltimeterReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAltimeterReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAltimeterReadingChangedEventArgs>
    {
        IAltimeterReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAltimeterReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAltimeterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAltimeterStatics>
    {
        IAltimeterStatics(std::nullptr_t = nullptr) noexcept {}
        IAltimeterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBarometer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometer>
    {
        IBarometer(std::nullptr_t = nullptr) noexcept {}
        IBarometer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBarometer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometer2>
    {
        IBarometer2(std::nullptr_t = nullptr) noexcept {}
        IBarometer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBarometer3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometer3>
    {
        IBarometer3(std::nullptr_t = nullptr) noexcept {}
        IBarometer3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBarometerDataThreshold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometerDataThreshold>
    {
        IBarometerDataThreshold(std::nullptr_t = nullptr) noexcept {}
        IBarometerDataThreshold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBarometerReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometerReading>
    {
        IBarometerReading(std::nullptr_t = nullptr) noexcept {}
        IBarometerReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBarometerReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometerReading2>
    {
        IBarometerReading2(std::nullptr_t = nullptr) noexcept {}
        IBarometerReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBarometerReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometerReadingChangedEventArgs>
    {
        IBarometerReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBarometerReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBarometerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometerStatics>
    {
        IBarometerStatics(std::nullptr_t = nullptr) noexcept {}
        IBarometerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBarometerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBarometerStatics2>
    {
        IBarometerStatics2(std::nullptr_t = nullptr) noexcept {}
        IBarometerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompass :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompass>
    {
        ICompass(std::nullptr_t = nullptr) noexcept {}
        ICompass(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompass2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompass2>
    {
        ICompass2(std::nullptr_t = nullptr) noexcept {}
        ICompass2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompass3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompass3>
    {
        ICompass3(std::nullptr_t = nullptr) noexcept {}
        ICompass3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompass4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompass4>
    {
        ICompass4(std::nullptr_t = nullptr) noexcept {}
        ICompass4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompassDataThreshold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompassDataThreshold>
    {
        ICompassDataThreshold(std::nullptr_t = nullptr) noexcept {}
        ICompassDataThreshold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompassDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompassDeviceId>
    {
        ICompassDeviceId(std::nullptr_t = nullptr) noexcept {}
        ICompassDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompassReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompassReading>
    {
        ICompassReading(std::nullptr_t = nullptr) noexcept {}
        ICompassReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompassReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompassReading2>
    {
        ICompassReading2(std::nullptr_t = nullptr) noexcept {}
        ICompassReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompassReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompassReadingChangedEventArgs>
    {
        ICompassReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICompassReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompassReadingHeadingAccuracy :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompassReadingHeadingAccuracy>
    {
        ICompassReadingHeadingAccuracy(std::nullptr_t = nullptr) noexcept {}
        ICompassReadingHeadingAccuracy(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompassStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompassStatics>
    {
        ICompassStatics(std::nullptr_t = nullptr) noexcept {}
        ICompassStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompassStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompassStatics2>
    {
        ICompassStatics2(std::nullptr_t = nullptr) noexcept {}
        ICompassStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDetectedPerson :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDetectedPerson>
    {
        IDetectedPerson(std::nullptr_t = nullptr) noexcept {}
        IDetectedPerson(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGyrometer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometer>
    {
        IGyrometer(std::nullptr_t = nullptr) noexcept {}
        IGyrometer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGyrometer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometer2>
    {
        IGyrometer2(std::nullptr_t = nullptr) noexcept {}
        IGyrometer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGyrometer3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometer3>
    {
        IGyrometer3(std::nullptr_t = nullptr) noexcept {}
        IGyrometer3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGyrometer4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometer4>
    {
        IGyrometer4(std::nullptr_t = nullptr) noexcept {}
        IGyrometer4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGyrometerDataThreshold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometerDataThreshold>
    {
        IGyrometerDataThreshold(std::nullptr_t = nullptr) noexcept {}
        IGyrometerDataThreshold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGyrometerDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometerDeviceId>
    {
        IGyrometerDeviceId(std::nullptr_t = nullptr) noexcept {}
        IGyrometerDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGyrometerReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometerReading>
    {
        IGyrometerReading(std::nullptr_t = nullptr) noexcept {}
        IGyrometerReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGyrometerReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometerReading2>
    {
        IGyrometerReading2(std::nullptr_t = nullptr) noexcept {}
        IGyrometerReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGyrometerReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometerReadingChangedEventArgs>
    {
        IGyrometerReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IGyrometerReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGyrometerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometerStatics>
    {
        IGyrometerStatics(std::nullptr_t = nullptr) noexcept {}
        IGyrometerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGyrometerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGyrometerStatics2>
    {
        IGyrometerStatics2(std::nullptr_t = nullptr) noexcept {}
        IGyrometerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHeadOrientation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHeadOrientation>
    {
        IHeadOrientation(std::nullptr_t = nullptr) noexcept {}
        IHeadOrientation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHeadPosition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHeadPosition>
    {
        IHeadPosition(std::nullptr_t = nullptr) noexcept {}
        IHeadPosition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHingeAngleReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHingeAngleReading>
    {
        IHingeAngleReading(std::nullptr_t = nullptr) noexcept {}
        IHingeAngleReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHingeAngleSensor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHingeAngleSensor>
    {
        IHingeAngleSensor(std::nullptr_t = nullptr) noexcept {}
        IHingeAngleSensor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHingeAngleSensorReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHingeAngleSensorReadingChangedEventArgs>
    {
        IHingeAngleSensorReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IHingeAngleSensorReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHingeAngleSensorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHingeAngleSensorStatics>
    {
        IHingeAngleSensorStatics(std::nullptr_t = nullptr) noexcept {}
        IHingeAngleSensorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceFeatures :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceFeatures>
    {
        IHumanPresenceFeatures(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceFeatures(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceFeatures2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceFeatures2>
    {
        IHumanPresenceFeatures2(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceFeatures2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceFeatures3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceFeatures3>
    {
        IHumanPresenceFeatures3(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceFeatures3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceSensor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceSensor>
    {
        IHumanPresenceSensor(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceSensor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceSensor2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceSensor2>
    {
        IHumanPresenceSensor2(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceSensor2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceSensor3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceSensor3>
    {
        IHumanPresenceSensor3(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceSensor3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceSensorExtension :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceSensorExtension>
    {
        IHumanPresenceSensorExtension(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceSensorExtension(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceSensorReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceSensorReading>
    {
        IHumanPresenceSensorReading(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceSensorReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceSensorReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceSensorReading2>
    {
        IHumanPresenceSensorReading2(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceSensorReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceSensorReading3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceSensorReading3>
    {
        IHumanPresenceSensorReading3(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceSensorReading3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceSensorReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceSensorReadingChangedEventArgs>
    {
        IHumanPresenceSensorReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceSensorReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceSensorReadingUpdate :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceSensorReadingUpdate>
    {
        IHumanPresenceSensorReadingUpdate(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceSensorReadingUpdate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceSensorReadingUpdate2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceSensorReadingUpdate2>
    {
        IHumanPresenceSensorReadingUpdate2(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceSensorReadingUpdate2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceSensorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceSensorStatics>
    {
        IHumanPresenceSensorStatics(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceSensorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceSensorStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceSensorStatics2>
    {
        IHumanPresenceSensorStatics2(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceSensorStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceSettings>
    {
        IHumanPresenceSettings(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceSettings2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceSettings2>
    {
        IHumanPresenceSettings2(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceSettings2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceSettings3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceSettings3>
    {
        IHumanPresenceSettings3(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceSettings3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHumanPresenceSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHumanPresenceSettingsStatics>
    {
        IHumanPresenceSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        IHumanPresenceSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInclinometer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometer>
    {
        IInclinometer(std::nullptr_t = nullptr) noexcept {}
        IInclinometer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInclinometer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometer2>
    {
        IInclinometer2(std::nullptr_t = nullptr) noexcept {}
        IInclinometer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInclinometer3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometer3>
    {
        IInclinometer3(std::nullptr_t = nullptr) noexcept {}
        IInclinometer3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInclinometer4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometer4>
    {
        IInclinometer4(std::nullptr_t = nullptr) noexcept {}
        IInclinometer4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInclinometerDataThreshold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerDataThreshold>
    {
        IInclinometerDataThreshold(std::nullptr_t = nullptr) noexcept {}
        IInclinometerDataThreshold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInclinometerDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerDeviceId>
    {
        IInclinometerDeviceId(std::nullptr_t = nullptr) noexcept {}
        IInclinometerDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInclinometerReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerReading>
    {
        IInclinometerReading(std::nullptr_t = nullptr) noexcept {}
        IInclinometerReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInclinometerReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerReading2>
    {
        IInclinometerReading2(std::nullptr_t = nullptr) noexcept {}
        IInclinometerReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInclinometerReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerReadingChangedEventArgs>
    {
        IInclinometerReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IInclinometerReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInclinometerReadingYawAccuracy :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerReadingYawAccuracy>
    {
        IInclinometerReadingYawAccuracy(std::nullptr_t = nullptr) noexcept {}
        IInclinometerReadingYawAccuracy(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInclinometerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerStatics>
    {
        IInclinometerStatics(std::nullptr_t = nullptr) noexcept {}
        IInclinometerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInclinometerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerStatics2>
    {
        IInclinometerStatics2(std::nullptr_t = nullptr) noexcept {}
        IInclinometerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInclinometerStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerStatics3>
    {
        IInclinometerStatics3(std::nullptr_t = nullptr) noexcept {}
        IInclinometerStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInclinometerStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInclinometerStatics4>
    {
        IInclinometerStatics4(std::nullptr_t = nullptr) noexcept {}
        IInclinometerStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILightSensor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensor>
    {
        ILightSensor(std::nullptr_t = nullptr) noexcept {}
        ILightSensor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILightSensor2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensor2>
    {
        ILightSensor2(std::nullptr_t = nullptr) noexcept {}
        ILightSensor2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILightSensor3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensor3>
    {
        ILightSensor3(std::nullptr_t = nullptr) noexcept {}
        ILightSensor3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILightSensor4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensor4>
    {
        ILightSensor4(std::nullptr_t = nullptr) noexcept {}
        ILightSensor4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILightSensorDataThreshold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensorDataThreshold>
    {
        ILightSensorDataThreshold(std::nullptr_t = nullptr) noexcept {}
        ILightSensorDataThreshold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILightSensorDataThreshold2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensorDataThreshold2>
    {
        ILightSensorDataThreshold2(std::nullptr_t = nullptr) noexcept {}
        ILightSensorDataThreshold2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILightSensorDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensorDeviceId>
    {
        ILightSensorDeviceId(std::nullptr_t = nullptr) noexcept {}
        ILightSensorDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILightSensorReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensorReading>
    {
        ILightSensorReading(std::nullptr_t = nullptr) noexcept {}
        ILightSensorReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILightSensorReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensorReading2>
    {
        ILightSensorReading2(std::nullptr_t = nullptr) noexcept {}
        ILightSensorReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILightSensorReading3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensorReading3>
    {
        ILightSensorReading3(std::nullptr_t = nullptr) noexcept {}
        ILightSensorReading3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILightSensorReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensorReadingChangedEventArgs>
    {
        ILightSensorReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ILightSensorReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILightSensorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensorStatics>
    {
        ILightSensorStatics(std::nullptr_t = nullptr) noexcept {}
        ILightSensorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILightSensorStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILightSensorStatics2>
    {
        ILightSensorStatics2(std::nullptr_t = nullptr) noexcept {}
        ILightSensorStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILockOnLeaveOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILockOnLeaveOptions>
    {
        ILockOnLeaveOptions(std::nullptr_t = nullptr) noexcept {}
        ILockOnLeaveOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMagnetometer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometer>
    {
        IMagnetometer(std::nullptr_t = nullptr) noexcept {}
        IMagnetometer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMagnetometer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometer2>
    {
        IMagnetometer2(std::nullptr_t = nullptr) noexcept {}
        IMagnetometer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMagnetometer3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometer3>
    {
        IMagnetometer3(std::nullptr_t = nullptr) noexcept {}
        IMagnetometer3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMagnetometer4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometer4>
    {
        IMagnetometer4(std::nullptr_t = nullptr) noexcept {}
        IMagnetometer4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMagnetometerDataThreshold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometerDataThreshold>
    {
        IMagnetometerDataThreshold(std::nullptr_t = nullptr) noexcept {}
        IMagnetometerDataThreshold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMagnetometerDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometerDeviceId>
    {
        IMagnetometerDeviceId(std::nullptr_t = nullptr) noexcept {}
        IMagnetometerDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMagnetometerReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometerReading>
    {
        IMagnetometerReading(std::nullptr_t = nullptr) noexcept {}
        IMagnetometerReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMagnetometerReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometerReading2>
    {
        IMagnetometerReading2(std::nullptr_t = nullptr) noexcept {}
        IMagnetometerReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMagnetometerReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometerReadingChangedEventArgs>
    {
        IMagnetometerReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMagnetometerReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMagnetometerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometerStatics>
    {
        IMagnetometerStatics(std::nullptr_t = nullptr) noexcept {}
        IMagnetometerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMagnetometerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMagnetometerStatics2>
    {
        IMagnetometerStatics2(std::nullptr_t = nullptr) noexcept {}
        IMagnetometerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOnlookerDetectionOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOnlookerDetectionOptions>
    {
        IOnlookerDetectionOptions(std::nullptr_t = nullptr) noexcept {}
        IOnlookerDetectionOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOrientationSensor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensor>
    {
        IOrientationSensor(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOrientationSensor2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensor2>
    {
        IOrientationSensor2(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensor2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOrientationSensor3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensor3>
    {
        IOrientationSensor3(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensor3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOrientationSensorDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorDeviceId>
    {
        IOrientationSensorDeviceId(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOrientationSensorReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorReading>
    {
        IOrientationSensorReading(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOrientationSensorReading2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorReading2>
    {
        IOrientationSensorReading2(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorReading2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOrientationSensorReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorReadingChangedEventArgs>
    {
        IOrientationSensorReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOrientationSensorReadingYawAccuracy :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorReadingYawAccuracy>
    {
        IOrientationSensorReadingYawAccuracy(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorReadingYawAccuracy(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOrientationSensorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorStatics>
    {
        IOrientationSensorStatics(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOrientationSensorStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorStatics2>
    {
        IOrientationSensorStatics2(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOrientationSensorStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorStatics3>
    {
        IOrientationSensorStatics3(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOrientationSensorStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientationSensorStatics4>
    {
        IOrientationSensorStatics4(std::nullptr_t = nullptr) noexcept {}
        IOrientationSensorStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPedometer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPedometer>
    {
        IPedometer(std::nullptr_t = nullptr) noexcept {}
        IPedometer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPedometer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPedometer2>
    {
        IPedometer2(std::nullptr_t = nullptr) noexcept {}
        IPedometer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPedometerDataThresholdFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPedometerDataThresholdFactory>
    {
        IPedometerDataThresholdFactory(std::nullptr_t = nullptr) noexcept {}
        IPedometerDataThresholdFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPedometerReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPedometerReading>
    {
        IPedometerReading(std::nullptr_t = nullptr) noexcept {}
        IPedometerReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPedometerReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPedometerReadingChangedEventArgs>
    {
        IPedometerReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPedometerReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPedometerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPedometerStatics>
    {
        IPedometerStatics(std::nullptr_t = nullptr) noexcept {}
        IPedometerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPedometerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPedometerStatics2>
    {
        IPedometerStatics2(std::nullptr_t = nullptr) noexcept {}
        IPedometerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProximitySensor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProximitySensor>
    {
        IProximitySensor(std::nullptr_t = nullptr) noexcept {}
        IProximitySensor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProximitySensorDataThresholdFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProximitySensorDataThresholdFactory>
    {
        IProximitySensorDataThresholdFactory(std::nullptr_t = nullptr) noexcept {}
        IProximitySensorDataThresholdFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProximitySensorReading :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProximitySensorReading>
    {
        IProximitySensorReading(std::nullptr_t = nullptr) noexcept {}
        IProximitySensorReading(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProximitySensorReadingChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProximitySensorReadingChangedEventArgs>
    {
        IProximitySensorReadingChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IProximitySensorReadingChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProximitySensorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProximitySensorStatics>
    {
        IProximitySensorStatics(std::nullptr_t = nullptr) noexcept {}
        IProximitySensorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProximitySensorStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProximitySensorStatics2>
    {
        IProximitySensorStatics2(std::nullptr_t = nullptr) noexcept {}
        IProximitySensorStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISensorDataThreshold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISensorDataThreshold>
    {
        ISensorDataThreshold(std::nullptr_t = nullptr) noexcept {}
        ISensorDataThreshold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISensorDataThresholdTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISensorDataThresholdTriggerDetails>
    {
        ISensorDataThresholdTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        ISensorDataThresholdTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISensorQuaternion :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISensorQuaternion>
    {
        ISensorQuaternion(std::nullptr_t = nullptr) noexcept {}
        ISensorQuaternion(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISensorRotationMatrix :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISensorRotationMatrix>
    {
        ISensorRotationMatrix(std::nullptr_t = nullptr) noexcept {}
        ISensorRotationMatrix(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISimpleOrientationSensor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISimpleOrientationSensor>
    {
        ISimpleOrientationSensor(std::nullptr_t = nullptr) noexcept {}
        ISimpleOrientationSensor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISimpleOrientationSensor2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISimpleOrientationSensor2>
    {
        ISimpleOrientationSensor2(std::nullptr_t = nullptr) noexcept {}
        ISimpleOrientationSensor2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISimpleOrientationSensorDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISimpleOrientationSensorDeviceId>
    {
        ISimpleOrientationSensorDeviceId(std::nullptr_t = nullptr) noexcept {}
        ISimpleOrientationSensorDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISimpleOrientationSensorOrientationChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISimpleOrientationSensorOrientationChangedEventArgs>
    {
        ISimpleOrientationSensorOrientationChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISimpleOrientationSensorOrientationChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISimpleOrientationSensorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISimpleOrientationSensorStatics>
    {
        ISimpleOrientationSensorStatics(std::nullptr_t = nullptr) noexcept {}
        ISimpleOrientationSensorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISimpleOrientationSensorStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISimpleOrientationSensorStatics2>
    {
        ISimpleOrientationSensorStatics2(std::nullptr_t = nullptr) noexcept {}
        ISimpleOrientationSensorStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWakeOnApproachOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWakeOnApproachOptions>
    {
        IWakeOnApproachOptions(std::nullptr_t = nullptr) noexcept {}
        IWakeOnApproachOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
