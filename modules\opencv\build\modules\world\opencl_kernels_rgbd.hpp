// This file is auto-generated. Do not edit!

#include "opencv2/core/ocl.hpp"
#include "opencv2/core/ocl_genbase.hpp"
#include "opencv2/core/opencl/ocl_defs.hpp"

#ifdef HAVE_OPENCL

namespace cv
{
namespace ocl
{
namespace rgbd
{

extern struct cv::ocl::internal::ProgramEntry hash_tsdf_oclsrc;
extern struct cv::ocl::internal::ProgramEntry icp_oclsrc;
extern struct cv::ocl::internal::ProgramEntry kinfu_frame_oclsrc;
extern struct cv::ocl::internal::ProgramEntry tsdf_oclsrc;
extern struct cv::ocl::internal::ProgramEntry tsdf_functions_oclsrc;

}}}
#endif
