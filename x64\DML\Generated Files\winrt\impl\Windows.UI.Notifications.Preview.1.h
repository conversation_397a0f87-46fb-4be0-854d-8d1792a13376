// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_UI_Notifications_Preview_1_H
#define WINRT_Windows_UI_Notifications_Preview_1_H
#include "winrt/impl/Windows.UI.Notifications.Preview.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Notifications::Preview
{
    struct WINRT_IMPL_EMPTY_BASES IToastOcclusionManagerPreviewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastOcclusionManagerPreviewStatics>
    {
        IToastOcclusionManagerPreviewStatics(std::nullptr_t = nullptr) noexcept {}
        IToastOcclusionManagerPreviewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
