D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\test\dct_image_denoising.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_xphoto.dir\Release\dct_image_denoising.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\test\simple_color_balance.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_xphoto.dir\Release\simple_color_balance.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\test\test_denoise_bm3d.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_xphoto.dir\Release\test_denoise_bm3d.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\test\test_grayworld.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_xphoto.dir\Release\test_grayworld.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\test\test_hdr.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_xphoto.dir\Release\test_hdr.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\test\test_inpainting.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_xphoto.dir\Release\test_inpainting.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\test\test_learning_based_color_balance.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_xphoto.dir\Release\test_learning_based_color_balance.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\test\test_main.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_xphoto.dir\Release\test_main.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\test\test_oil_painting.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_xphoto.dir\Release\test_oil_painting.obj
