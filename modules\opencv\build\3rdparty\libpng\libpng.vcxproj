﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{28D3AF5A-6F82-38AA-8B7C-69DC434D5C30}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>libpng</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\cudabuild\3rdparty\lib\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">libpng.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">libpngd</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\cudabuild\3rdparty\lib\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">libpng.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">libpng</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\libpng;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/AI/opencv/cudabuild" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;PNG_INTEL_SSE_OPT=1;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ProgramDataBaseFileName>D:\AI\opencv\cudabuild\3rdparty\lib\Debug\libpngd.pdb</ProgramDataBaseFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;PNG_INTEL_SSE_OPT=1;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\libpng;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\libpng;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4221</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\libpng;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/AI/opencv/cudabuild" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;NDEBUG;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;PNG_INTEL_SSE_OPT=1;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ProgramDataBaseFileName>D:\AI\opencv\cudabuild\3rdparty\lib\Release\libpng.pdb</ProgramDataBaseFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;NDEBUG;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;PNG_INTEL_SSE_OPT=1;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\libpng;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\libpng;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4221</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\png.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngerror.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngget.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngmem.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngpread.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngread.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngrio.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngrtran.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngrutil.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngset.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngtrans.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngwio.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngwrite.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngwtran.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngwutil.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\intel\intel_init.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\intel\filter_sse2_intrinsics.c" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\png.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngconf.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngdebug.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pnginfo.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pnglibconf.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngpriv.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngstruct.h" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\AI\opencv\cudabuild\3rdparty\zlib\zlib.vcxproj">
      <Project>{5BA6087C-B49F-31D7-A8F0-C37E5E8F7F80}</Project>
      <Name>zlib</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>