var searchData=
[
  ['values_0',['values',['../intro_guide.html#init_hints_values',1,'Supported and default values'],['../window_guide.html#window_hints_values',1,'Supported and default values']]],
  ['version_1',['version',['../intro_guide.html#intro_version_compile',1,'Compile-time version'],['../intro_guide.html#intro_version_runtime',1,'Run-time version']]],
  ['version_203_204_2',['Release notes for version 3.4',['../news.html',1,'']]],
  ['version_20and_20error_20reference_3',['Initialization, version and error reference',['../group__init.html',1,'']]],
  ['version_20compatibility_4',['Version compatibility',['../intro_guide.html#compatibility',1,'']]],
  ['version_20management_5',['Version management',['../intro_guide.html#intro_version',1,'']]],
  ['version_20string_6',['Version string',['../intro_guide.html#intro_version_string',1,'']]],
  ['version_20string_20format_20has_20been_20changed_7',['Version string format has been changed',['../news.html#version_string_caveat',1,'']]],
  ['versions_8',['Release notes for earlier versions',['../news.html#news_archive',1,'']]],
  ['versions_20of_20windows_20older_20than_20xp_9',['Support for versions of Windows older than XP',['../moving_guide.html#moving_windows',1,'']]],
  ['video_20mode_20enumeration_10',['Video mode enumeration',['../moving_guide.html#moving_video_modes',1,'']]],
  ['video_20modes_11',['Video modes',['../monitor_guide.html#monitor_modes',1,'']]],
  ['virtual_20position_12',['Virtual position',['../monitor_guide.html#monitor_pos',1,'']]],
  ['visibility_13',['Window visibility',['../window_guide.html#window_hide',1,'']]],
  ['vista_20support_20is_20deprecated_14',['Windows XP and Vista support is deprecated',['../news.html#winxp_deprecated',1,'']]],
  ['visual_20c_20and_20glfw_20binaries_15',['With Visual C++ and GLFW binaries',['../build_guide.html#build_link_win32',1,'']]],
  ['vulkan_20extensions_16',['Querying required Vulkan extensions',['../vulkan_guide.html#vulkan_ext',1,'']]],
  ['vulkan_20function_20pointers_17',['Querying Vulkan function pointers',['../vulkan_guide.html#vulkan_proc',1,'']]],
  ['vulkan_20guide_18',['Vulkan guide',['../vulkan_guide.html',1,'']]],
  ['vulkan_20header_20file_19',['Including the Vulkan header file',['../vulkan_guide.html#vulkan_include',1,'']]],
  ['vulkan_20loader_20',['Finding the Vulkan loader',['../vulkan_guide.html#vulkan_loader',1,'']]],
  ['vulkan_20loader_20and_20api_21',['Vulkan loader and API',['../compat_guide.html#compat_vulkan',1,'']]],
  ['vulkan_20presentation_20support_22',['Querying for Vulkan presentation support',['../vulkan_guide.html#vulkan_present',1,'']]],
  ['vulkan_20support_23',['Querying for Vulkan support',['../vulkan_guide.html#vulkan_support',1,'']]],
  ['vulkan_20support_20reference_24',['Vulkan support reference',['../group__vulkan.html',1,'']]],
  ['vulkan_20window_20surface_25',['Creating a Vulkan window surface',['../vulkan_guide.html#vulkan_surface',1,'']]],
  ['vulkan_20window_20surface_20hint_26',['X11 Vulkan window surface hint',['../news.html#x11_xcb_vulkan_surface',1,'']]],
  ['vulkan_20wsi_20extensions_27',['Vulkan WSI extensions',['../compat_guide.html#compat_wsi',1,'']]],
  ['vulkan_2emd_28',['vulkan.md',['../vulkan_8md.html',1,'']]]
];
