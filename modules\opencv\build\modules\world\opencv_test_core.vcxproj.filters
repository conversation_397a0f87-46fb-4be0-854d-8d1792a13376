﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\ocl\test_arithm.cpp">
      <Filter>opencv_core\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\ocl\test_channels.cpp">
      <Filter>opencv_core\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\ocl\test_dft.cpp">
      <Filter>opencv_core\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\ocl\test_gemm.cpp">
      <Filter>opencv_core\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\ocl\test_image2d.cpp">
      <Filter>opencv_core\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\ocl\test_matrix_expr.cpp">
      <Filter>opencv_core\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\ocl\test_matrix_operation.cpp">
      <Filter>opencv_core\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\ocl\test_opencl.cpp">
      <Filter>opencv_core\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_arithm.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_async.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_concatenation.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_conjugate_gradient.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_countnonzero.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_cuda.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_downhill_simplex.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_ds.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_dxt.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_eigen.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_hal_core.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_hasnonzero.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_intrin.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_intrin_emulator.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_io.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_logtagconfigparser.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_logtagmanager.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_lpsolver.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_main.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_mat.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_math.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_misc.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_opencl.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_operations.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_ptr.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_quaternion.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_rand.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_rotatedrect.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_umat.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_utils.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\ref_reduce_arg.impl.hpp">
      <Filter>opencv_core\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_intrin128.simd.hpp">
      <Filter>opencv_core\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_intrin256.simd.hpp">
      <Filter>opencv_core\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_intrin512.simd.hpp">
      <Filter>opencv_core\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_intrin_utils.hpp">
      <Filter>opencv_core\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_precomp.hpp">
      <Filter>opencv_core\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\core\test\test_utils_tls.impl.hpp">
      <Filter>opencv_core\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Object Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_SSE2.dir\$(Configuration)\test_intrin128.sse2.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_SSE3.dir\$(Configuration)\test_intrin128.sse3.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_SSE4_1.dir\$(Configuration)\test_intrin128.ssse3.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_SSE4_1.dir\$(Configuration)\test_intrin128.sse4_1.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_SSE4_2.dir\$(Configuration)\test_intrin128.sse4_2.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_FP16.dir\$(Configuration)\test_intrin128.fp16.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_AVX.dir\$(Configuration)\test_intrin128.avx.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_AVX2.dir\$(Configuration)\test_intrin128.avx2.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_AVX2.dir\$(Configuration)\test_intrin256.avx2.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_AVX512_SKX.dir\$(Configuration)\test_intrin128.avx512_skx.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_AVX512_SKX.dir\$(Configuration)\test_intrin256.avx512_skx.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_AVX512_SKX.dir\$(Configuration)\test_intrin512.avx512_skx.obj">
      <Filter>Object Libraries</Filter>
    </Object>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Object Libraries">
      <UniqueIdentifier>{EBD5911D-2884-32B2-ADB5-A849E0CBF498}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_core">
      <UniqueIdentifier>{D10889F9-FE1B-3344-A0CB-8FD035FF683E}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_core\Include">
      <UniqueIdentifier>{CB0FFE36-5A9C-3286-AFE8-956D499C76DF}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_core\Src">
      <UniqueIdentifier>{8C58C161-C746-3A49-912D-28B5DFBEEBF4}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_core\Src\ocl">
      <UniqueIdentifier>{C0F6E5B2-062D-3599-9E12-B97F48A0EC57}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
