name: PR:4.x

on:
  pull_request:
    branches:
      - 4.x

jobs:
  Ubuntu2004-ARM64:
    uses: opencv/ci-gha-workflow/.github/workflows/OCV-Contrib-PR-4.x-ARM64.yaml@main

  Ubuntu2004-x64:
    uses: opencv/ci-gha-workflow/.github/workflows/OCV-Contrib-PR-4.x-U20.yaml@main

  Ubuntu2204-x64:
    uses: opencv/ci-gha-workflow/.github/workflows/OCV-Contrib-PR-4.x-U22.yaml@main

  Ubuntu2404-x64:
    uses: opencv/ci-gha-workflow/.github/workflows/OCV-Contrib-PR-4.x-U24.yaml@main

  Ubuntu2004-x64-CUDA:
    uses: opencv/ci-gha-workflow/.github/workflows/OCV-Contrib-PR-4.x-U20-Cuda.yaml@main

  Windows10-x64:
    uses: opencv/ci-gha-workflow/.github/workflows/OCV-Contrib-PR-4.x-W10.yaml@main

  macOS-ARM64:
    uses: opencv/ci-gha-workflow/.github/workflows/OCV-Contrib-PR-4.x-macOS-ARM64.yaml@main

  macOS-X64:
    uses: opencv/ci-gha-workflow/.github/workflows/OCV-Contrib-PR-4.x-macOS-x86_64.yaml@main

  Linux-RISC-V-Clang:
    uses: opencv/ci-gha-workflow/.github/workflows/OCV-Contrib-PR-4.x-RISCV.yaml@main

  openEuler2203-x64:
    if: "${{ contains(github.event.pull_request.labels.*.name, 'category: cann') }}"
    uses: opencv/ci-gha-workflow/.github/workflows/OCV-Contrib-PR-4.x-O22-CANN.yaml@main
