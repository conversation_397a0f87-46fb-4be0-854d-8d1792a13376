{"sources": [{"file": "D:/AI/opencv/opencv-4.10.0/modules/video/test/ocl/test_bgfg_mog2.cpp", "labels": ["Main", "opencv_video", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/video/test/ocl/test_dis.cpp", "labels": ["Main", "opencv_video", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/video/test/ocl/test_optflow_farneback.cpp", "labels": ["Main", "opencv_video", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/video/test/ocl/test_optflowpyrlk.cpp", "labels": ["Main", "opencv_video", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/video/test/test_OF_accuracy.cpp", "labels": ["Main", "opencv_video", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/video/test/test_OF_reproducibility.cpp", "labels": ["Main", "opencv_video", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/video/test/test_accum.cpp", "labels": ["Main", "opencv_video", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/video/test/test_camshift.cpp", "labels": ["Main", "opencv_video", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/video/test/test_ecc.cpp", "labels": ["Main", "opencv_video", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/video/test/test_estimaterigid.cpp", "labels": ["Main", "opencv_video", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/video/test/test_kalman.cpp", "labels": ["Main", "opencv_video", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/video/test/test_main.cpp", "labels": ["Main", "opencv_video", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/video/test/test_optflowpyrlk.cpp", "labels": ["Main", "opencv_video", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/video/test/test_trackers.cpp", "labels": ["Main", "opencv_video", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/video/test/test_precomp.hpp", "labels": ["Main", "opencv_video", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/video/test/test_trackers.impl.hpp", "labels": ["Main", "opencv_video", "AccuracyTest"]}], "target": {"labels": ["Main", "opencv_video", "AccuracyTest"], "name": "opencv_test_video"}}