// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Navigation_H
#define WINRT_Windows_UI_Xaml_Navigation_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.240405.15"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.240405.15"
#include "winrt/Windows.UI.Xaml.h"
#include "winrt/impl/Windows.Foundation.2.h"
#include "winrt/impl/Windows.UI.Xaml.2.h"
#include "winrt/impl/Windows.UI.Xaml.Interop.2.h"
#include "winrt/impl/Windows.UI.Xaml.Media.Animation.2.h"
#include "winrt/impl/Windows.UI.Xaml.Navigation.2.h"
namespace winrt::impl
{
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_IFrameNavigationOptions<D>::IsNavigationStackEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::IFrameNavigationOptions)->get_IsNavigationStackEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_IFrameNavigationOptions<D>::IsNavigationStackEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::IFrameNavigationOptions)->put_IsNavigationStackEnabled(value));
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_IFrameNavigationOptions<D>::TransitionInfoOverride() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::IFrameNavigationOptions)->get_TransitionInfoOverride(&value));
        return winrt::Windows::UI::Xaml::Media::Animation::NavigationTransitionInfo{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_IFrameNavigationOptions<D>::TransitionInfoOverride(winrt::Windows::UI::Xaml::Media::Animation::NavigationTransitionInfo const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::IFrameNavigationOptions)->put_TransitionInfoOverride(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_IFrameNavigationOptionsFactory<D>::CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::IFrameNavigationOptionsFactory)->CreateInstance(*(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Windows::UI::Xaml::Navigation::FrameNavigationOptions{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigatingCancelEventArgs<D>::Cancel() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigatingCancelEventArgs)->get_Cancel(&value));
        return value;
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigatingCancelEventArgs<D>::Cancel(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigatingCancelEventArgs)->put_Cancel(value));
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigatingCancelEventArgs<D>::NavigationMode() const
    {
        winrt::Windows::UI::Xaml::Navigation::NavigationMode value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigatingCancelEventArgs)->get_NavigationMode(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigatingCancelEventArgs<D>::SourcePageType() const
    {
        winrt::Windows::UI::Xaml::Interop::TypeName value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigatingCancelEventArgs)->get_SourcePageType(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigatingCancelEventArgs2<D>::Parameter() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigatingCancelEventArgs2)->get_Parameter(&value));
        return winrt::Windows::Foundation::IInspectable{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigatingCancelEventArgs2<D>::NavigationTransitionInfo() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigatingCancelEventArgs2)->get_NavigationTransitionInfo(&value));
        return winrt::Windows::UI::Xaml::Media::Animation::NavigationTransitionInfo{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigationEventArgs<D>::Content() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigationEventArgs)->get_Content(&value));
        return winrt::Windows::Foundation::IInspectable{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigationEventArgs<D>::Parameter() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigationEventArgs)->get_Parameter(&value));
        return winrt::Windows::Foundation::IInspectable{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigationEventArgs<D>::SourcePageType() const
    {
        winrt::Windows::UI::Xaml::Interop::TypeName value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigationEventArgs)->get_SourcePageType(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigationEventArgs<D>::NavigationMode() const
    {
        winrt::Windows::UI::Xaml::Navigation::NavigationMode value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigationEventArgs)->get_NavigationMode(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigationEventArgs<D>::Uri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigationEventArgs)->get_Uri(&value));
        return winrt::Windows::Foundation::Uri{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigationEventArgs<D>::Uri(winrt::Windows::Foundation::Uri const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigationEventArgs)->put_Uri(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigationEventArgs2<D>::NavigationTransitionInfo() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigationEventArgs2)->get_NavigationTransitionInfo(&value));
        return winrt::Windows::UI::Xaml::Media::Animation::NavigationTransitionInfo{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigationFailedEventArgs<D>::Exception() const
    {
        winrt::hresult value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigationFailedEventArgs)->get_Exception(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigationFailedEventArgs<D>::Handled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigationFailedEventArgs)->get_Handled(&value));
        return value;
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigationFailedEventArgs<D>::Handled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigationFailedEventArgs)->put_Handled(value));
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_INavigationFailedEventArgs<D>::SourcePageType() const
    {
        winrt::Windows::UI::Xaml::Interop::TypeName value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::INavigationFailedEventArgs)->get_SourcePageType(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_IPageStackEntry<D>::SourcePageType() const
    {
        winrt::Windows::UI::Xaml::Interop::TypeName value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::IPageStackEntry)->get_SourcePageType(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_IPageStackEntry<D>::Parameter() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::IPageStackEntry)->get_Parameter(&value));
        return winrt::Windows::Foundation::IInspectable{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_IPageStackEntry<D>::NavigationTransitionInfo() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::IPageStackEntry)->get_NavigationTransitionInfo(&value));
        return winrt::Windows::UI::Xaml::Media::Animation::NavigationTransitionInfo{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_IPageStackEntryFactory<D>::CreateInstance(winrt::Windows::UI::Xaml::Interop::TypeName const& sourcePageType, winrt::Windows::Foundation::IInspectable const& parameter, winrt::Windows::UI::Xaml::Media::Animation::NavigationTransitionInfo const& navigationTransitionInfo) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::IPageStackEntryFactory)->CreateInstance(impl::bind_in(sourcePageType), *(void**)(&parameter), *(void**)(&navigationTransitionInfo), &value));
        return winrt::Windows::UI::Xaml::Navigation::PageStackEntry{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_UI_Xaml_Navigation_IPageStackEntryStatics<D>::SourcePageTypeProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::UI::Xaml::Navigation::IPageStackEntryStatics)->get_SourcePageTypeProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename H> struct delegate<winrt::Windows::UI::Xaml::Navigation::LoadCompletedEventHandler, H> final : implements_delegate<winrt::Windows::UI::Xaml::Navigation::LoadCompletedEventHandler, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Windows::UI::Xaml::Navigation::LoadCompletedEventHandler, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* sender, void* e) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&sender), *reinterpret_cast<winrt::Windows::UI::Xaml::Navigation::NavigationEventArgs const*>(&e));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Windows::UI::Xaml::Navigation::NavigatedEventHandler, H> final : implements_delegate<winrt::Windows::UI::Xaml::Navigation::NavigatedEventHandler, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Windows::UI::Xaml::Navigation::NavigatedEventHandler, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* sender, void* e) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&sender), *reinterpret_cast<winrt::Windows::UI::Xaml::Navigation::NavigationEventArgs const*>(&e));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Windows::UI::Xaml::Navigation::NavigatingCancelEventHandler, H> final : implements_delegate<winrt::Windows::UI::Xaml::Navigation::NavigatingCancelEventHandler, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Windows::UI::Xaml::Navigation::NavigatingCancelEventHandler, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* sender, void* e) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&sender), *reinterpret_cast<winrt::Windows::UI::Xaml::Navigation::NavigatingCancelEventArgs const*>(&e));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Windows::UI::Xaml::Navigation::NavigationFailedEventHandler, H> final : implements_delegate<winrt::Windows::UI::Xaml::Navigation::NavigationFailedEventHandler, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Windows::UI::Xaml::Navigation::NavigationFailedEventHandler, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* sender, void* e) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&sender), *reinterpret_cast<winrt::Windows::UI::Xaml::Navigation::NavigationFailedEventArgs const*>(&e));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Windows::UI::Xaml::Navigation::NavigationStoppedEventHandler, H> final : implements_delegate<winrt::Windows::UI::Xaml::Navigation::NavigationStoppedEventHandler, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Windows::UI::Xaml::Navigation::NavigationStoppedEventHandler, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* sender, void* e) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&sender), *reinterpret_cast<winrt::Windows::UI::Xaml::Navigation::NavigationEventArgs const*>(&e));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::UI::Xaml::Navigation::IFrameNavigationOptions> : produce_base<D, winrt::Windows::UI::Xaml::Navigation::IFrameNavigationOptions>
    {
        int32_t __stdcall get_IsNavigationStackEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsNavigationStackEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsNavigationStackEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsNavigationStackEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TransitionInfoOverride(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Media::Animation::NavigationTransitionInfo>(this->shim().TransitionInfoOverride());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_TransitionInfoOverride(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().TransitionInfoOverride(*reinterpret_cast<winrt::Windows::UI::Xaml::Media::Animation::NavigationTransitionInfo const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::UI::Xaml::Navigation::IFrameNavigationOptionsFactory> : produce_base<D, winrt::Windows::UI::Xaml::Navigation::IFrameNavigationOptionsFactory>
    {
        int32_t __stdcall CreateInstance(void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Navigation::FrameNavigationOptions>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::UI::Xaml::Navigation::INavigatingCancelEventArgs> : produce_base<D, winrt::Windows::UI::Xaml::Navigation::INavigatingCancelEventArgs>
    {
        int32_t __stdcall get_Cancel(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().Cancel());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Cancel(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Cancel(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_NavigationMode(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Navigation::NavigationMode>(this->shim().NavigationMode());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SourcePageType(struct struct_Windows_UI_Xaml_Interop_TypeName* value) noexcept final try
        {
            zero_abi<winrt::Windows::UI::Xaml::Interop::TypeName>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Interop::TypeName>(this->shim().SourcePageType());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::UI::Xaml::Navigation::INavigatingCancelEventArgs2> : produce_base<D, winrt::Windows::UI::Xaml::Navigation::INavigatingCancelEventArgs2>
    {
        int32_t __stdcall get_Parameter(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().Parameter());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_NavigationTransitionInfo(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Media::Animation::NavigationTransitionInfo>(this->shim().NavigationTransitionInfo());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::UI::Xaml::Navigation::INavigationEventArgs> : produce_base<D, winrt::Windows::UI::Xaml::Navigation::INavigationEventArgs>
    {
        int32_t __stdcall get_Content(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().Content());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Parameter(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().Parameter());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SourcePageType(struct struct_Windows_UI_Xaml_Interop_TypeName* value) noexcept final try
        {
            zero_abi<winrt::Windows::UI::Xaml::Interop::TypeName>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Interop::TypeName>(this->shim().SourcePageType());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_NavigationMode(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Navigation::NavigationMode>(this->shim().NavigationMode());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Uri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Uri>(this->shim().Uri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Uri(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Uri(*reinterpret_cast<winrt::Windows::Foundation::Uri const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::UI::Xaml::Navigation::INavigationEventArgs2> : produce_base<D, winrt::Windows::UI::Xaml::Navigation::INavigationEventArgs2>
    {
        int32_t __stdcall get_NavigationTransitionInfo(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Media::Animation::NavigationTransitionInfo>(this->shim().NavigationTransitionInfo());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::UI::Xaml::Navigation::INavigationFailedEventArgs> : produce_base<D, winrt::Windows::UI::Xaml::Navigation::INavigationFailedEventArgs>
    {
        int32_t __stdcall get_Exception(winrt::hresult* value) noexcept final try
        {
            zero_abi<winrt::hresult>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::hresult>(this->shim().Exception());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Handled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().Handled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Handled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Handled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SourcePageType(struct struct_Windows_UI_Xaml_Interop_TypeName* value) noexcept final try
        {
            zero_abi<winrt::Windows::UI::Xaml::Interop::TypeName>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Interop::TypeName>(this->shim().SourcePageType());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::UI::Xaml::Navigation::IPageStackEntry> : produce_base<D, winrt::Windows::UI::Xaml::Navigation::IPageStackEntry>
    {
        int32_t __stdcall get_SourcePageType(struct struct_Windows_UI_Xaml_Interop_TypeName* value) noexcept final try
        {
            zero_abi<winrt::Windows::UI::Xaml::Interop::TypeName>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Interop::TypeName>(this->shim().SourcePageType());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Parameter(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().Parameter());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_NavigationTransitionInfo(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Media::Animation::NavigationTransitionInfo>(this->shim().NavigationTransitionInfo());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::UI::Xaml::Navigation::IPageStackEntryFactory> : produce_base<D, winrt::Windows::UI::Xaml::Navigation::IPageStackEntryFactory>
    {
        int32_t __stdcall CreateInstance(struct struct_Windows_UI_Xaml_Interop_TypeName sourcePageType, void* parameter, void* navigationTransitionInfo, void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Navigation::PageStackEntry>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::UI::Xaml::Interop::TypeName const*>(&sourcePageType), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&parameter), *reinterpret_cast<winrt::Windows::UI::Xaml::Media::Animation::NavigationTransitionInfo const*>(&navigationTransitionInfo)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::UI::Xaml::Navigation::IPageStackEntryStatics> : produce_base<D, winrt::Windows::UI::Xaml::Navigation::IPageStackEntryStatics>
    {
        int32_t __stdcall get_SourcePageTypeProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().SourcePageTypeProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
}
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Navigation
{
    inline FrameNavigationOptions::FrameNavigationOptions()
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<FrameNavigationOptions, IFrameNavigationOptionsFactory>([&](IFrameNavigationOptionsFactory const& f) { return f.CreateInstance(baseInterface, innerInterface); });
    }
    inline PageStackEntry::PageStackEntry(winrt::Windows::UI::Xaml::Interop::TypeName const& sourcePageType, winrt::Windows::Foundation::IInspectable const& parameter, winrt::Windows::UI::Xaml::Media::Animation::NavigationTransitionInfo const& navigationTransitionInfo) :
        PageStackEntry(impl::call_factory<PageStackEntry, IPageStackEntryFactory>([&](IPageStackEntryFactory const& f) { return f.CreateInstance(sourcePageType, parameter, navigationTransitionInfo); }))
    {
    }
    inline auto PageStackEntry::SourcePageTypeProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IPageStackEntryStatics const&), PageStackEntry, IPageStackEntryStatics>([](IPageStackEntryStatics const& f) { return f.SourcePageTypeProperty(); });
    }
    template <typename L> LoadCompletedEventHandler::LoadCompletedEventHandler(L handler) :
        LoadCompletedEventHandler(impl::make_delegate<LoadCompletedEventHandler>(std::forward<L>(handler)))
    {
    }
    template <typename F> LoadCompletedEventHandler::LoadCompletedEventHandler(F* handler) :
        LoadCompletedEventHandler([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> LoadCompletedEventHandler::LoadCompletedEventHandler(O* object, M method) :
        LoadCompletedEventHandler([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> LoadCompletedEventHandler::LoadCompletedEventHandler(com_ptr<O>&& object, M method) :
        LoadCompletedEventHandler([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename LM> LoadCompletedEventHandler::LoadCompletedEventHandler(weak_ref<O>&& object, LM&& lambda_or_method) :
        LoadCompletedEventHandler([o = std::move(object), lm = std::forward<LM>(lambda_or_method)](auto&&... args) { if (auto s = o.get()) {
            if constexpr (std::is_member_function_pointer_v<LM>) ((*s).*(lm))(args...);
            else lm(args...);
        } })
    {
    }
    template <typename O, typename M> LoadCompletedEventHandler::LoadCompletedEventHandler(std::shared_ptr<O>&& object, M method) :
        LoadCompletedEventHandler([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename LM> LoadCompletedEventHandler::LoadCompletedEventHandler(std::weak_ptr<O>&& object, LM&& lambda_or_method) :
        LoadCompletedEventHandler([o = std::move(object), lm = std::forward<LM>(lambda_or_method)](auto&&... args) { if (auto s = o.lock()) {
            if constexpr (std::is_member_function_pointer_v<LM>) ((*s).*(lm))(args...);
            else lm(args...);
        } })
    {
    }
    inline auto LoadCompletedEventHandler::operator()(winrt::Windows::Foundation::IInspectable const& sender, winrt::Windows::UI::Xaml::Navigation::NavigationEventArgs const& e) const
    {
        check_hresult((*(impl::abi_t<LoadCompletedEventHandler>**)this)->Invoke(*(void**)(&sender), *(void**)(&e)));
    }
    template <typename L> NavigatedEventHandler::NavigatedEventHandler(L handler) :
        NavigatedEventHandler(impl::make_delegate<NavigatedEventHandler>(std::forward<L>(handler)))
    {
    }
    template <typename F> NavigatedEventHandler::NavigatedEventHandler(F* handler) :
        NavigatedEventHandler([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> NavigatedEventHandler::NavigatedEventHandler(O* object, M method) :
        NavigatedEventHandler([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> NavigatedEventHandler::NavigatedEventHandler(com_ptr<O>&& object, M method) :
        NavigatedEventHandler([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename LM> NavigatedEventHandler::NavigatedEventHandler(weak_ref<O>&& object, LM&& lambda_or_method) :
        NavigatedEventHandler([o = std::move(object), lm = std::forward<LM>(lambda_or_method)](auto&&... args) { if (auto s = o.get()) {
            if constexpr (std::is_member_function_pointer_v<LM>) ((*s).*(lm))(args...);
            else lm(args...);
        } })
    {
    }
    template <typename O, typename M> NavigatedEventHandler::NavigatedEventHandler(std::shared_ptr<O>&& object, M method) :
        NavigatedEventHandler([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename LM> NavigatedEventHandler::NavigatedEventHandler(std::weak_ptr<O>&& object, LM&& lambda_or_method) :
        NavigatedEventHandler([o = std::move(object), lm = std::forward<LM>(lambda_or_method)](auto&&... args) { if (auto s = o.lock()) {
            if constexpr (std::is_member_function_pointer_v<LM>) ((*s).*(lm))(args...);
            else lm(args...);
        } })
    {
    }
    inline auto NavigatedEventHandler::operator()(winrt::Windows::Foundation::IInspectable const& sender, winrt::Windows::UI::Xaml::Navigation::NavigationEventArgs const& e) const
    {
        check_hresult((*(impl::abi_t<NavigatedEventHandler>**)this)->Invoke(*(void**)(&sender), *(void**)(&e)));
    }
    template <typename L> NavigatingCancelEventHandler::NavigatingCancelEventHandler(L handler) :
        NavigatingCancelEventHandler(impl::make_delegate<NavigatingCancelEventHandler>(std::forward<L>(handler)))
    {
    }
    template <typename F> NavigatingCancelEventHandler::NavigatingCancelEventHandler(F* handler) :
        NavigatingCancelEventHandler([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> NavigatingCancelEventHandler::NavigatingCancelEventHandler(O* object, M method) :
        NavigatingCancelEventHandler([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> NavigatingCancelEventHandler::NavigatingCancelEventHandler(com_ptr<O>&& object, M method) :
        NavigatingCancelEventHandler([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename LM> NavigatingCancelEventHandler::NavigatingCancelEventHandler(weak_ref<O>&& object, LM&& lambda_or_method) :
        NavigatingCancelEventHandler([o = std::move(object), lm = std::forward<LM>(lambda_or_method)](auto&&... args) { if (auto s = o.get()) {
            if constexpr (std::is_member_function_pointer_v<LM>) ((*s).*(lm))(args...);
            else lm(args...);
        } })
    {
    }
    template <typename O, typename M> NavigatingCancelEventHandler::NavigatingCancelEventHandler(std::shared_ptr<O>&& object, M method) :
        NavigatingCancelEventHandler([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename LM> NavigatingCancelEventHandler::NavigatingCancelEventHandler(std::weak_ptr<O>&& object, LM&& lambda_or_method) :
        NavigatingCancelEventHandler([o = std::move(object), lm = std::forward<LM>(lambda_or_method)](auto&&... args) { if (auto s = o.lock()) {
            if constexpr (std::is_member_function_pointer_v<LM>) ((*s).*(lm))(args...);
            else lm(args...);
        } })
    {
    }
    inline auto NavigatingCancelEventHandler::operator()(winrt::Windows::Foundation::IInspectable const& sender, winrt::Windows::UI::Xaml::Navigation::NavigatingCancelEventArgs const& e) const
    {
        check_hresult((*(impl::abi_t<NavigatingCancelEventHandler>**)this)->Invoke(*(void**)(&sender), *(void**)(&e)));
    }
    template <typename L> NavigationFailedEventHandler::NavigationFailedEventHandler(L handler) :
        NavigationFailedEventHandler(impl::make_delegate<NavigationFailedEventHandler>(std::forward<L>(handler)))
    {
    }
    template <typename F> NavigationFailedEventHandler::NavigationFailedEventHandler(F* handler) :
        NavigationFailedEventHandler([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> NavigationFailedEventHandler::NavigationFailedEventHandler(O* object, M method) :
        NavigationFailedEventHandler([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> NavigationFailedEventHandler::NavigationFailedEventHandler(com_ptr<O>&& object, M method) :
        NavigationFailedEventHandler([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename LM> NavigationFailedEventHandler::NavigationFailedEventHandler(weak_ref<O>&& object, LM&& lambda_or_method) :
        NavigationFailedEventHandler([o = std::move(object), lm = std::forward<LM>(lambda_or_method)](auto&&... args) { if (auto s = o.get()) {
            if constexpr (std::is_member_function_pointer_v<LM>) ((*s).*(lm))(args...);
            else lm(args...);
        } })
    {
    }
    template <typename O, typename M> NavigationFailedEventHandler::NavigationFailedEventHandler(std::shared_ptr<O>&& object, M method) :
        NavigationFailedEventHandler([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename LM> NavigationFailedEventHandler::NavigationFailedEventHandler(std::weak_ptr<O>&& object, LM&& lambda_or_method) :
        NavigationFailedEventHandler([o = std::move(object), lm = std::forward<LM>(lambda_or_method)](auto&&... args) { if (auto s = o.lock()) {
            if constexpr (std::is_member_function_pointer_v<LM>) ((*s).*(lm))(args...);
            else lm(args...);
        } })
    {
    }
    inline auto NavigationFailedEventHandler::operator()(winrt::Windows::Foundation::IInspectable const& sender, winrt::Windows::UI::Xaml::Navigation::NavigationFailedEventArgs const& e) const
    {
        check_hresult((*(impl::abi_t<NavigationFailedEventHandler>**)this)->Invoke(*(void**)(&sender), *(void**)(&e)));
    }
    template <typename L> NavigationStoppedEventHandler::NavigationStoppedEventHandler(L handler) :
        NavigationStoppedEventHandler(impl::make_delegate<NavigationStoppedEventHandler>(std::forward<L>(handler)))
    {
    }
    template <typename F> NavigationStoppedEventHandler::NavigationStoppedEventHandler(F* handler) :
        NavigationStoppedEventHandler([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> NavigationStoppedEventHandler::NavigationStoppedEventHandler(O* object, M method) :
        NavigationStoppedEventHandler([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> NavigationStoppedEventHandler::NavigationStoppedEventHandler(com_ptr<O>&& object, M method) :
        NavigationStoppedEventHandler([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename LM> NavigationStoppedEventHandler::NavigationStoppedEventHandler(weak_ref<O>&& object, LM&& lambda_or_method) :
        NavigationStoppedEventHandler([o = std::move(object), lm = std::forward<LM>(lambda_or_method)](auto&&... args) { if (auto s = o.get()) {
            if constexpr (std::is_member_function_pointer_v<LM>) ((*s).*(lm))(args...);
            else lm(args...);
        } })
    {
    }
    template <typename O, typename M> NavigationStoppedEventHandler::NavigationStoppedEventHandler(std::shared_ptr<O>&& object, M method) :
        NavigationStoppedEventHandler([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename LM> NavigationStoppedEventHandler::NavigationStoppedEventHandler(std::weak_ptr<O>&& object, LM&& lambda_or_method) :
        NavigationStoppedEventHandler([o = std::move(object), lm = std::forward<LM>(lambda_or_method)](auto&&... args) { if (auto s = o.lock()) {
            if constexpr (std::is_member_function_pointer_v<LM>) ((*s).*(lm))(args...);
            else lm(args...);
        } })
    {
    }
    inline auto NavigationStoppedEventHandler::operator()(winrt::Windows::Foundation::IInspectable const& sender, winrt::Windows::UI::Xaml::Navigation::NavigationEventArgs const& e) const
    {
        check_hresult((*(impl::abi_t<NavigationStoppedEventHandler>**)this)->Invoke(*(void**)(&sender), *(void**)(&e)));
    }
    template <typename D, typename... Interfaces>
    struct FrameNavigationOptionsT :
        implements<D, winrt::Windows::Foundation::IInspectable, composing, Interfaces...>,
        impl::require<D, winrt::Windows::UI::Xaml::Navigation::IFrameNavigationOptions>,
        impl::base<D, FrameNavigationOptions>
    {
        using composable = FrameNavigationOptions;
    protected:
        FrameNavigationOptionsT()
        {
            impl::call_factory<FrameNavigationOptions, IFrameNavigationOptionsFactory>([&](IFrameNavigationOptionsFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
    template<> struct hash<winrt::Windows::UI::Xaml::Navigation::IFrameNavigationOptions> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::UI::Xaml::Navigation::IFrameNavigationOptionsFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::UI::Xaml::Navigation::INavigatingCancelEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::UI::Xaml::Navigation::INavigatingCancelEventArgs2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::UI::Xaml::Navigation::INavigationEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::UI::Xaml::Navigation::INavigationEventArgs2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::UI::Xaml::Navigation::INavigationFailedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::UI::Xaml::Navigation::IPageStackEntry> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::UI::Xaml::Navigation::IPageStackEntryFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::UI::Xaml::Navigation::IPageStackEntryStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::UI::Xaml::Navigation::FrameNavigationOptions> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::UI::Xaml::Navigation::NavigatingCancelEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::UI::Xaml::Navigation::NavigationEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::UI::Xaml::Navigation::NavigationFailedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::UI::Xaml::Navigation::PageStackEntry> : winrt::impl::hash_base {};
#endif
#ifdef __cpp_lib_format
#endif
}
#endif
