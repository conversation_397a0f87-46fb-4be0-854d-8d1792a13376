﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\png.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngerror.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngget.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngmem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngpread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngrio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngrtran.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngrutil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngset.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngtrans.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngwio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngwrite.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngwtran.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngwutil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\intel\intel_init.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\intel\filter_sse2_intrinsics.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\png.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngdebug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pnginfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pnglibconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngpriv.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libpng\pngstruct.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{792E8A66-904F-3E91-91DB-8CBB2E5E760B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{2A318434-2169-3E81-A653-C1C0490B5893}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
