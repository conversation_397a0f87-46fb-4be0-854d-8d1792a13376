package org.opencv.test.calib3d;

import org.opencv.test.OpenCVTestCase;

public class StereoSGBMTest extends OpenCVTestCase {

    public void testCompute() {
        fail("Not yet implemented");
    }

    public void testGet_disp12MaxDiff() {
        fail("Not yet implemented");
    }

    public void testGet_fullDP() {
        fail("Not yet implemented");
    }

    public void testGet_minDisparity() {
        fail("Not yet implemented");
    }

    public void testGet_numberOfDisparities() {
        fail("Not yet implemented");
    }

    public void testGet_P1() {
        fail("Not yet implemented");
    }

    public void testGet_P2() {
        fail("Not yet implemented");
    }

    public void testGet_preFilterCap() {
        fail("Not yet implemented");
    }

    public void testGet_SADWindowSize() {
        fail("Not yet implemented");
    }

    public void testGet_speckleRange() {
        fail("Not yet implemented");
    }

    public void testGet_speckleWindowSize() {
        fail("Not yet implemented");
    }

    public void testGet_uniquenessRatio() {
        fail("Not yet implemented");
    }

    public void testSet_disp12MaxDiff() {
        fail("Not yet implemented");
    }

    public void testSet_fullDP() {
        fail("Not yet implemented");
    }

    public void testSet_minDisparity() {
        fail("Not yet implemented");
    }

    public void testSet_numberOfDisparities() {
        fail("Not yet implemented");
    }

    public void testSet_P1() {
        fail("Not yet implemented");
    }

    public void testSet_P2() {
        fail("Not yet implemented");
    }

    public void testSet_preFilterCap() {
        fail("Not yet implemented");
    }

    public void testSet_SADWindowSize() {
        fail("Not yet implemented");
    }

    public void testSet_speckleRange() {
        fail("Not yet implemented");
    }

    public void testSet_speckleWindowSize() {
        fail("Not yet implemented");
    }

    public void testSet_uniquenessRatio() {
        fail("Not yet implemented");
    }

    public void testStereoSGBM() {
        fail("Not yet implemented");
    }

    public void testStereoSGBMIntIntInt() {
        fail("Not yet implemented");
    }

    public void testStereoSGBMIntIntIntInt() {
        fail("Not yet implemented");
    }

    public void testStereoSGBMIntIntIntIntInt() {
        fail("Not yet implemented");
    }

    public void testStereoSGBMIntIntIntIntIntInt() {
        fail("Not yet implemented");
    }

    public void testStereoSGBMIntIntIntIntIntIntInt() {
        fail("Not yet implemented");
    }

    public void testStereoSGBMIntIntIntIntIntIntIntInt() {
        fail("Not yet implemented");
    }

    public void testStereoSGBMIntIntIntIntIntIntIntIntInt() {
        fail("Not yet implemented");
    }

    public void testStereoSGBMIntIntIntIntIntIntIntIntIntInt() {
        fail("Not yet implemented");
    }

    public void testStereoSGBMIntIntIntIntIntIntIntIntIntIntBoolean() {
        fail("Not yet implemented");
    }

}
