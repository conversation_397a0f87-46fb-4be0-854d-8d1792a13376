## About

This is the documentation for the Objective-C/Swift OpenCV wrapper

To get started: add the OpenCV framework to your project and add the following to your source

###Objective-C

    #import <OpenCV/OpenCV.h>

###Swift

    import OpenCV



##Modules

`Core`, `Imgproc`, `Ml`, `Phase_unwrapping`, `Plot`, `Dnn`, `Features2d`, `Imgcodecs`, `Photo`, `Text`, `Videoio`, `Xphoto`, `Calib3d`, `Objdetect`, `Structured_light`, `Video`, `Wechat_qrcode`, `Xfeatures2d`, `Ximgproc`, `Aruco`, `Bgsegm`, `Bioinspired`, `Face`, `Tracking`, `Img_hash`