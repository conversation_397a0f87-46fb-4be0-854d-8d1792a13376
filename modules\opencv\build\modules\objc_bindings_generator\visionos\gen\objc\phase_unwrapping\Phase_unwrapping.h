//
// This file is auto-generated. Please don't modify it!
//
#pragma once

#ifdef __cplusplus
//#import "opencv.hpp"
#import "opencv2/phase_unwrapping.hpp"
#else
#define CV_EXPORTS
#endif

#import <Foundation/Foundation.h>





NS_ASSUME_NONNULL_BEGIN

// C++: class Phase_unwrapping
/**
 * The Phase_unwrapping module
 *
 * Member classes: `HistogramPhaseUnwrapping`, `HistogramPhaseUnwrappingParams`, `PhaseUnwrapping`
 *
 */
CV_EXPORTS @interface Phase_unwrapping : NSObject

#pragma mark - Methods



@end

NS_ASSUME_NONNULL_END


