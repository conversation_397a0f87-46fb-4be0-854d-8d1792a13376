<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Initialization, version and error reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#groups">Modules</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">Initialization, version and error reference</div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>This is the reference documentation for initialization and termination of the library, version management and error handling. For more task-oriented information, see the <a class="el" href="intro_guide.html">Introduction to the API</a>. </p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="groups" name="groups"></a>
Modules</h2></td></tr>
<tr class="memitem:group__errors" id="r_group__errors"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html">Error codes</a></td></tr>
<tr class="memdesc:group__errors"><td class="mdescLeft">&#160;</td><td class="mdescRight">Error codes. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:ga2744fbb29b5631bb28802dbe0cf36eba" id="r_ga2744fbb29b5631bb28802dbe0cf36eba"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga2744fbb29b5631bb28802dbe0cf36eba">GLFW_TRUE</a>&#160;&#160;&#160;1</td></tr>
<tr class="memdesc:ga2744fbb29b5631bb28802dbe0cf36eba"><td class="mdescLeft">&#160;</td><td class="mdescRight">One.  <br /></td></tr>
<tr class="separator:ga2744fbb29b5631bb28802dbe0cf36eba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac877fe3b627d21ef3a0a23e0a73ba8c5" id="r_gac877fe3b627d21ef3a0a23e0a73ba8c5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gac877fe3b627d21ef3a0a23e0a73ba8c5">GLFW_FALSE</a>&#160;&#160;&#160;0</td></tr>
<tr class="memdesc:gac877fe3b627d21ef3a0a23e0a73ba8c5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Zero.  <br /></td></tr>
<tr class="separator:gac877fe3b627d21ef3a0a23e0a73ba8c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab9c0534709fda03ec8959201da3a9a18" id="r_gab9c0534709fda03ec8959201da3a9a18"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gab9c0534709fda03ec8959201da3a9a18">GLFW_JOYSTICK_HAT_BUTTONS</a>&#160;&#160;&#160;0x00050001</td></tr>
<tr class="memdesc:gab9c0534709fda03ec8959201da3a9a18"><td class="mdescLeft">&#160;</td><td class="mdescRight">Joystick hat buttons init hint.  <br /></td></tr>
<tr class="separator:gab9c0534709fda03ec8959201da3a9a18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaec269b24cf549ab46292c0125d8bbdce" id="r_gaec269b24cf549ab46292c0125d8bbdce"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gaec269b24cf549ab46292c0125d8bbdce">GLFW_ANGLE_PLATFORM_TYPE</a>&#160;&#160;&#160;0x00050002</td></tr>
<tr class="memdesc:gaec269b24cf549ab46292c0125d8bbdce"><td class="mdescLeft">&#160;</td><td class="mdescRight">ANGLE rendering backend init hint.  <br /></td></tr>
<tr class="separator:gaec269b24cf549ab46292c0125d8bbdce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9d38bf1fdf4f91d6565401734a7cd967" id="r_ga9d38bf1fdf4f91d6565401734a7cd967"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga9d38bf1fdf4f91d6565401734a7cd967">GLFW_PLATFORM</a>&#160;&#160;&#160;0x00050003</td></tr>
<tr class="memdesc:ga9d38bf1fdf4f91d6565401734a7cd967"><td class="mdescLeft">&#160;</td><td class="mdescRight">Platform selection init hint.  <br /></td></tr>
<tr class="separator:ga9d38bf1fdf4f91d6565401734a7cd967"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab937983147a3158d45f88fad7129d9f2" id="r_gab937983147a3158d45f88fad7129d9f2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gab937983147a3158d45f88fad7129d9f2">GLFW_COCOA_CHDIR_RESOURCES</a>&#160;&#160;&#160;0x00051001</td></tr>
<tr class="memdesc:gab937983147a3158d45f88fad7129d9f2"><td class="mdescLeft">&#160;</td><td class="mdescRight">macOS specific init hint.  <br /></td></tr>
<tr class="separator:gab937983147a3158d45f88fad7129d9f2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga71e0b4ce2f2696a84a9b8c5e12dc70cf" id="r_ga71e0b4ce2f2696a84a9b8c5e12dc70cf"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga71e0b4ce2f2696a84a9b8c5e12dc70cf">GLFW_COCOA_MENUBAR</a>&#160;&#160;&#160;0x00051002</td></tr>
<tr class="memdesc:ga71e0b4ce2f2696a84a9b8c5e12dc70cf"><td class="mdescLeft">&#160;</td><td class="mdescRight">macOS specific init hint.  <br /></td></tr>
<tr class="separator:ga71e0b4ce2f2696a84a9b8c5e12dc70cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa341e303ebeb8e4199b8ab8be84351f6" id="r_gaa341e303ebeb8e4199b8ab8be84351f6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gaa341e303ebeb8e4199b8ab8be84351f6">GLFW_X11_XCB_VULKAN_SURFACE</a>&#160;&#160;&#160;0x00052001</td></tr>
<tr class="memdesc:gaa341e303ebeb8e4199b8ab8be84351f6"><td class="mdescLeft">&#160;</td><td class="mdescRight">X11 specific init hint.  <br /></td></tr>
<tr class="separator:gaa341e303ebeb8e4199b8ab8be84351f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2a3f2fd7695902c498b050215b3db452" id="r_ga2a3f2fd7695902c498b050215b3db452"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga2a3f2fd7695902c498b050215b3db452">GLFW_WAYLAND_LIBDECOR</a>&#160;&#160;&#160;0x00053001</td></tr>
<tr class="memdesc:ga2a3f2fd7695902c498b050215b3db452"><td class="mdescLeft">&#160;</td><td class="mdescRight">Wayland specific init hint.  <br /></td></tr>
<tr class="separator:ga2a3f2fd7695902c498b050215b3db452"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga18b2d37374d0dea28cd69194fa85b859" id="r_ga18b2d37374d0dea28cd69194fa85b859"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga18b2d37374d0dea28cd69194fa85b859">GLFW_ANY_PLATFORM</a>&#160;&#160;&#160;0x00060000</td></tr>
<tr class="memdesc:ga18b2d37374d0dea28cd69194fa85b859"><td class="mdescLeft">&#160;</td><td class="mdescRight">Hint value that enables automatic platform selection.  <br /></td></tr>
<tr class="separator:ga18b2d37374d0dea28cd69194fa85b859"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8d3d17df2ab57492cef665da52c603a1" id="r_ga8d3d17df2ab57492cef665da52c603a1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga8d3d17df2ab57492cef665da52c603a1">GLFW_PLATFORM_WIN32</a>&#160;&#160;&#160;0x00060001</td></tr>
<tr class="separator:ga8d3d17df2ab57492cef665da52c603a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga83b18714254f75bc2f0cdbafa0f10b6b" id="r_ga83b18714254f75bc2f0cdbafa0f10b6b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga83b18714254f75bc2f0cdbafa0f10b6b">GLFW_PLATFORM_COCOA</a>&#160;&#160;&#160;0x00060002</td></tr>
<tr class="separator:ga83b18714254f75bc2f0cdbafa0f10b6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac4b08906a3cbf26c518a4a543eedd740" id="r_gac4b08906a3cbf26c518a4a543eedd740"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gac4b08906a3cbf26c518a4a543eedd740">GLFW_PLATFORM_WAYLAND</a>&#160;&#160;&#160;0x00060003</td></tr>
<tr class="separator:gac4b08906a3cbf26c518a4a543eedd740"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf5333f3933e9c248a00cfda6523f386b" id="r_gaf5333f3933e9c248a00cfda6523f386b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gaf5333f3933e9c248a00cfda6523f386b">GLFW_PLATFORM_X11</a>&#160;&#160;&#160;0x00060004</td></tr>
<tr class="separator:gaf5333f3933e9c248a00cfda6523f386b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac06fad5a4866ae7a1d7b2675fac72d7f" id="r_gac06fad5a4866ae7a1d7b2675fac72d7f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gac06fad5a4866ae7a1d7b2675fac72d7f">GLFW_PLATFORM_NULL</a>&#160;&#160;&#160;0x00060005</td></tr>
<tr class="separator:gac06fad5a4866ae7a1d7b2675fac72d7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga4306a564e9f60f4de8cc8f31731a3120" id="r_ga4306a564e9f60f4de8cc8f31731a3120"><td class="memItemLeft" align="right" valign="top">typedef void *(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga4306a564e9f60f4de8cc8f31731a3120">GLFWallocatefun</a>) (size_t size, void *user)</td></tr>
<tr class="memdesc:ga4306a564e9f60f4de8cc8f31731a3120"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for memory allocation callbacks.  <br /></td></tr>
<tr class="separator:ga4306a564e9f60f4de8cc8f31731a3120"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3e88a829615d8efe8bec1746f7309c63" id="r_ga3e88a829615d8efe8bec1746f7309c63"><td class="memItemLeft" align="right" valign="top">typedef void *(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga3e88a829615d8efe8bec1746f7309c63">GLFWreallocatefun</a>) (void *block, size_t size, void *user)</td></tr>
<tr class="memdesc:ga3e88a829615d8efe8bec1746f7309c63"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for memory reallocation callbacks.  <br /></td></tr>
<tr class="separator:ga3e88a829615d8efe8bec1746f7309c63"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7181615eda94c4b07bd72bdcee39fa28" id="r_ga7181615eda94c4b07bd72bdcee39fa28"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga7181615eda94c4b07bd72bdcee39fa28">GLFWdeallocatefun</a>) (void *block, void *user)</td></tr>
<tr class="memdesc:ga7181615eda94c4b07bd72bdcee39fa28"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for memory deallocation callbacks.  <br /></td></tr>
<tr class="separator:ga7181615eda94c4b07bd72bdcee39fa28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8184701785c096b3862a75cda1bf44a3" id="r_ga8184701785c096b3862a75cda1bf44a3"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga8184701785c096b3862a75cda1bf44a3">GLFWerrorfun</a>) (int error_code, const char *description)</td></tr>
<tr class="memdesc:ga8184701785c096b3862a75cda1bf44a3"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for error callbacks.  <br /></td></tr>
<tr class="separator:ga8184701785c096b3862a75cda1bf44a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga145c57d7f2aeda0b704a5a4ba1d6104b" id="r_ga145c57d7f2aeda0b704a5a4ba1d6104b"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="struct_g_l_f_wallocator.html">GLFWallocator</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga145c57d7f2aeda0b704a5a4ba1d6104b">GLFWallocator</a></td></tr>
<tr class="memdesc:ga145c57d7f2aeda0b704a5a4ba1d6104b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Custom heap memory allocator.  <br /></td></tr>
<tr class="separator:ga145c57d7f2aeda0b704a5a4ba1d6104b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga317aac130a235ab08c6db0834907d85e" id="r_ga317aac130a235ab08c6db0834907d85e"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a> (void)</td></tr>
<tr class="memdesc:ga317aac130a235ab08c6db0834907d85e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initializes the GLFW library.  <br /></td></tr>
<tr class="separator:ga317aac130a235ab08c6db0834907d85e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaae48c0a18607ea4a4ba951d939f0901" id="r_gaaae48c0a18607ea4a4ba951d939f0901"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a> (void)</td></tr>
<tr class="memdesc:gaaae48c0a18607ea4a4ba951d939f0901"><td class="mdescLeft">&#160;</td><td class="mdescRight">Terminates the GLFW library.  <br /></td></tr>
<tr class="separator:gaaae48c0a18607ea4a4ba951d939f0901"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga110fd1d3f0412822b4f1908c026f724a" id="r_ga110fd1d3f0412822b4f1908c026f724a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga110fd1d3f0412822b4f1908c026f724a">glfwInitHint</a> (int hint, int value)</td></tr>
<tr class="memdesc:ga110fd1d3f0412822b4f1908c026f724a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the specified init hint to the desired value.  <br /></td></tr>
<tr class="separator:ga110fd1d3f0412822b4f1908c026f724a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9dde93e9891fa7dd17e4194c9f3ae7c6" id="r_ga9dde93e9891fa7dd17e4194c9f3ae7c6"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a> (const <a class="el" href="struct_g_l_f_wallocator.html">GLFWallocator</a> *allocator)</td></tr>
<tr class="memdesc:ga9dde93e9891fa7dd17e4194c9f3ae7c6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the init allocator to the desired value.  <br /></td></tr>
<tr class="separator:ga9dde93e9891fa7dd17e4194c9f3ae7c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga76af552d0307bb5f7791f245417d4752" id="r_ga76af552d0307bb5f7791f245417d4752"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga76af552d0307bb5f7791f245417d4752">glfwInitVulkanLoader</a> (PFN_vkGetInstanceProcAddr loader)</td></tr>
<tr class="memdesc:ga76af552d0307bb5f7791f245417d4752"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the desired Vulkan <code>vkGetInstanceProcAddr</code> function.  <br /></td></tr>
<tr class="separator:ga76af552d0307bb5f7791f245417d4752"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9f8ffaacf3c269cc48eafbf8b9b71197" id="r_ga9f8ffaacf3c269cc48eafbf8b9b71197"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga9f8ffaacf3c269cc48eafbf8b9b71197">glfwGetVersion</a> (int *major, int *minor, int *rev)</td></tr>
<tr class="memdesc:ga9f8ffaacf3c269cc48eafbf8b9b71197"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the version of the GLFW library.  <br /></td></tr>
<tr class="separator:ga9f8ffaacf3c269cc48eafbf8b9b71197"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga026abd003c8e6501981ab1662062f1c0" id="r_ga026abd003c8e6501981ab1662062f1c0"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga026abd003c8e6501981ab1662062f1c0">glfwGetVersionString</a> (void)</td></tr>
<tr class="memdesc:ga026abd003c8e6501981ab1662062f1c0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a string describing the compile-time configuration.  <br /></td></tr>
<tr class="separator:ga026abd003c8e6501981ab1662062f1c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga944986b4ec0b928d488141f92982aa18" id="r_ga944986b4ec0b928d488141f92982aa18"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga944986b4ec0b928d488141f92982aa18">glfwGetError</a> (const char **description)</td></tr>
<tr class="memdesc:ga944986b4ec0b928d488141f92982aa18"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns and clears the last error for the calling thread.  <br /></td></tr>
<tr class="separator:ga944986b4ec0b928d488141f92982aa18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaff45816610d53f0b83656092a4034f40" id="r_gaff45816610d53f0b83656092a4034f40"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__init.html#ga8184701785c096b3862a75cda1bf44a3">GLFWerrorfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gaff45816610d53f0b83656092a4034f40">glfwSetErrorCallback</a> (<a class="el" href="group__init.html#ga8184701785c096b3862a75cda1bf44a3">GLFWerrorfun</a> callback)</td></tr>
<tr class="memdesc:gaff45816610d53f0b83656092a4034f40"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the error callback.  <br /></td></tr>
<tr class="separator:gaff45816610d53f0b83656092a4034f40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6d6a983d38bd4e8fd786d7a9061d399e" id="r_ga6d6a983d38bd4e8fd786d7a9061d399e"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga6d6a983d38bd4e8fd786d7a9061d399e">glfwGetPlatform</a> (void)</td></tr>
<tr class="memdesc:ga6d6a983d38bd4e8fd786d7a9061d399e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the currently selected platform.  <br /></td></tr>
<tr class="separator:ga6d6a983d38bd4e8fd786d7a9061d399e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8785d2b6b36632368d803e78079d38ed" id="r_ga8785d2b6b36632368d803e78079d38ed"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga8785d2b6b36632368d803e78079d38ed">glfwPlatformSupported</a> (int platform)</td></tr>
<tr class="memdesc:ga8785d2b6b36632368d803e78079d38ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the library includes support for the specified platform.  <br /></td></tr>
<tr class="separator:ga8785d2b6b36632368d803e78079d38ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="ga6337d9ea43b22fc529b2bba066b4a576" name="ga6337d9ea43b22fc529b2bba066b4a576"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6337d9ea43b22fc529b2bba066b4a576">&#9670;&#160;</a></span>GLFW_VERSION_MAJOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_VERSION_MAJOR&#160;&#160;&#160;3</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The major version number of the GLFW header. This is incremented when the API is changed in non-compatible ways. </p>

</div>
</div>
<a id="gaf80d40f0aea7088ff337606e9c48f7a3" name="gaf80d40f0aea7088ff337606e9c48f7a3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf80d40f0aea7088ff337606e9c48f7a3">&#9670;&#160;</a></span>GLFW_VERSION_MINOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_VERSION_MINOR&#160;&#160;&#160;4</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The minor version number of the GLFW header. This is incremented when features are added to the API but it remains backward-compatible. </p>

</div>
</div>
<a id="gab72ae2e2035d9ea461abc3495eac0502" name="gab72ae2e2035d9ea461abc3495eac0502"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab72ae2e2035d9ea461abc3495eac0502">&#9670;&#160;</a></span>GLFW_VERSION_REVISION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_VERSION_REVISION&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The revision number of the GLFW header. This is incremented when a bug fix release is made that does not contain any API changes. </p>

</div>
</div>
<a id="ga2744fbb29b5631bb28802dbe0cf36eba" name="ga2744fbb29b5631bb28802dbe0cf36eba"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2744fbb29b5631bb28802dbe0cf36eba">&#9670;&#160;</a></span>GLFW_TRUE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_TRUE&#160;&#160;&#160;1</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is only semantic sugar for the number 1. You can instead use <code>1</code> or <code>true</code> or <code>_True</code> or <code>GL_TRUE</code> or <code>VK_TRUE</code> or anything else that is equal to one. </p>

</div>
</div>
<a id="gac877fe3b627d21ef3a0a23e0a73ba8c5" name="gac877fe3b627d21ef3a0a23e0a73ba8c5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac877fe3b627d21ef3a0a23e0a73ba8c5">&#9670;&#160;</a></span>GLFW_FALSE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_FALSE&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is only semantic sugar for the number 0. You can instead use <code>0</code> or <code>false</code> or <code>_False</code> or <code>GL_FALSE</code> or <code>VK_FALSE</code> or anything else that is equal to zero. </p>

</div>
</div>
<a id="gab9c0534709fda03ec8959201da3a9a18" name="gab9c0534709fda03ec8959201da3a9a18"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab9c0534709fda03ec8959201da3a9a18">&#9670;&#160;</a></span>GLFW_JOYSTICK_HAT_BUTTONS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_HAT_BUTTONS&#160;&#160;&#160;0x00050001</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Joystick hat buttons <a class="el" href="intro_guide.html#GLFW_JOYSTICK_HAT_BUTTONS">init hint</a>. </p>

</div>
</div>
<a id="gaec269b24cf549ab46292c0125d8bbdce" name="gaec269b24cf549ab46292c0125d8bbdce"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaec269b24cf549ab46292c0125d8bbdce">&#9670;&#160;</a></span>GLFW_ANGLE_PLATFORM_TYPE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ANGLE_PLATFORM_TYPE&#160;&#160;&#160;0x00050002</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>ANGLE rendering backend <a class="el" href="intro_guide.html#GLFW_ANGLE_PLATFORM_TYPE_hint">init hint</a>. </p>

</div>
</div>
<a id="ga9d38bf1fdf4f91d6565401734a7cd967" name="ga9d38bf1fdf4f91d6565401734a7cd967"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9d38bf1fdf4f91d6565401734a7cd967">&#9670;&#160;</a></span>GLFW_PLATFORM</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_PLATFORM&#160;&#160;&#160;0x00050003</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Platform selection <a class="el" href="intro_guide.html#GLFW_PLATFORM">init hint</a>. </p>

</div>
</div>
<a id="gab937983147a3158d45f88fad7129d9f2" name="gab937983147a3158d45f88fad7129d9f2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab937983147a3158d45f88fad7129d9f2">&#9670;&#160;</a></span>GLFW_COCOA_CHDIR_RESOURCES</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_COCOA_CHDIR_RESOURCES&#160;&#160;&#160;0x00051001</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>macOS specific <a class="el" href="intro_guide.html#GLFW_COCOA_CHDIR_RESOURCES_hint">init hint</a>. </p>

</div>
</div>
<a id="ga71e0b4ce2f2696a84a9b8c5e12dc70cf" name="ga71e0b4ce2f2696a84a9b8c5e12dc70cf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga71e0b4ce2f2696a84a9b8c5e12dc70cf">&#9670;&#160;</a></span>GLFW_COCOA_MENUBAR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_COCOA_MENUBAR&#160;&#160;&#160;0x00051002</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>macOS specific <a class="el" href="intro_guide.html#GLFW_COCOA_MENUBAR_hint">init hint</a>. </p>

</div>
</div>
<a id="gaa341e303ebeb8e4199b8ab8be84351f6" name="gaa341e303ebeb8e4199b8ab8be84351f6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa341e303ebeb8e4199b8ab8be84351f6">&#9670;&#160;</a></span>GLFW_X11_XCB_VULKAN_SURFACE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_X11_XCB_VULKAN_SURFACE&#160;&#160;&#160;0x00052001</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>X11 specific <a class="el" href="intro_guide.html#GLFW_X11_XCB_VULKAN_SURFACE_hint">init hint</a>. </p>

</div>
</div>
<a id="ga2a3f2fd7695902c498b050215b3db452" name="ga2a3f2fd7695902c498b050215b3db452"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2a3f2fd7695902c498b050215b3db452">&#9670;&#160;</a></span>GLFW_WAYLAND_LIBDECOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_WAYLAND_LIBDECOR&#160;&#160;&#160;0x00053001</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Wayland specific <a class="el" href="intro_guide.html#GLFW_WAYLAND_LIBDECOR_hint">init hint</a>. </p>

</div>
</div>
<a id="ga18b2d37374d0dea28cd69194fa85b859" name="ga18b2d37374d0dea28cd69194fa85b859"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga18b2d37374d0dea28cd69194fa85b859">&#9670;&#160;</a></span>GLFW_ANY_PLATFORM</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ANY_PLATFORM&#160;&#160;&#160;0x00060000</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Hint value for <a class="el" href="intro_guide.html#GLFW_PLATFORM">GLFW_PLATFORM</a> that enables automatic platform selection. </p>

</div>
</div>
<a id="ga8d3d17df2ab57492cef665da52c603a1" name="ga8d3d17df2ab57492cef665da52c603a1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8d3d17df2ab57492cef665da52c603a1">&#9670;&#160;</a></span>GLFW_PLATFORM_WIN32</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_PLATFORM_WIN32&#160;&#160;&#160;0x00060001</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga83b18714254f75bc2f0cdbafa0f10b6b" name="ga83b18714254f75bc2f0cdbafa0f10b6b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga83b18714254f75bc2f0cdbafa0f10b6b">&#9670;&#160;</a></span>GLFW_PLATFORM_COCOA</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_PLATFORM_COCOA&#160;&#160;&#160;0x00060002</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gac4b08906a3cbf26c518a4a543eedd740" name="gac4b08906a3cbf26c518a4a543eedd740"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac4b08906a3cbf26c518a4a543eedd740">&#9670;&#160;</a></span>GLFW_PLATFORM_WAYLAND</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_PLATFORM_WAYLAND&#160;&#160;&#160;0x00060003</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaf5333f3933e9c248a00cfda6523f386b" name="gaf5333f3933e9c248a00cfda6523f386b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf5333f3933e9c248a00cfda6523f386b">&#9670;&#160;</a></span>GLFW_PLATFORM_X11</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_PLATFORM_X11&#160;&#160;&#160;0x00060004</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gac06fad5a4866ae7a1d7b2675fac72d7f" name="gac06fad5a4866ae7a1d7b2675fac72d7f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac06fad5a4866ae7a1d7b2675fac72d7f">&#9670;&#160;</a></span>GLFW_PLATFORM_NULL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_PLATFORM_NULL&#160;&#160;&#160;0x00060005</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Typedef Documentation</h2>
<a id="ga4306a564e9f60f4de8cc8f31731a3120" name="ga4306a564e9f60f4de8cc8f31731a3120"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4306a564e9f60f4de8cc8f31731a3120">&#9670;&#160;</a></span>GLFWallocatefun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void *(* GLFWallocatefun) (size_t size, void *user)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for memory allocation callbacks. A memory allocation callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span>* function_name(<span class="keywordtype">size_t</span> size, <span class="keywordtype">void</span>* user)</div>
</div><!-- fragment --><p>This function must return either a memory block at least <code>size</code> bytes long, or <code>NULL</code> if allocation failed. Note that not all parts of GLFW handle allocation failures gracefully yet.</p>
<p>This function must support being called during <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a> but before the library is flagged as initialized, as well as during <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a> after the library is no longer flagged as initialized.</p>
<p>Any memory allocated via this function will be deallocated via the same allocator during library termination or earlier.</p>
<p>Any memory allocated via this function must be suitably aligned for any object type. If you are using C99 or earlier, this alignment is platform-dependent but will be the same as what <code>malloc</code> provides. If you are using C11 or later, this is the value of <code>alignof(max_align_t)</code>.</p>
<p>The size will always be greater than zero. Allocations of size zero are filtered out before reaching the custom allocator.</p>
<p>If this function returns <code>NULL</code>, GLFW will emit <a class="el" href="group__errors.html#ga9023953a2bcb98c2906afd071d21ee7f">GLFW_OUT_OF_MEMORY</a>.</p>
<p>This function must not call any GLFW function.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">size</td><td>The minimum size, in bytes, of the memory block. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">user</td><td>The user-defined pointer from the allocator. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The address of the newly allocated memory block, or <code>NULL</code> if an error occurred.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned memory block must be valid at least until it is deallocated.</dd></dl>
<dl class="section user"><dt>Reentrancy</dt><dd>This function should not call any GLFW function.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must support being called from any thread that calls GLFW functions.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="intro_guide.html#init_allocator">Custom heap memory allocator</a> </dd>
<dd>
<a class="el" href="struct_g_l_f_wallocator.html">GLFWallocator</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.4. </dd></dl>

</div>
</div>
<a id="ga3e88a829615d8efe8bec1746f7309c63" name="ga3e88a829615d8efe8bec1746f7309c63"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3e88a829615d8efe8bec1746f7309c63">&#9670;&#160;</a></span>GLFWreallocatefun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void *(* GLFWreallocatefun) (void *block, size_t size, void *user)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for memory reallocation callbacks. A memory reallocation callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span>* function_name(<span class="keywordtype">void</span>* block, <span class="keywordtype">size_t</span> size, <span class="keywordtype">void</span>* user)</div>
</div><!-- fragment --><p>This function must return a memory block at least <code>size</code> bytes long, or <code>NULL</code> if allocation failed. Note that not all parts of GLFW handle allocation failures gracefully yet.</p>
<p>This function must support being called during <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a> but before the library is flagged as initialized, as well as during <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a> after the library is no longer flagged as initialized.</p>
<p>Any memory allocated via this function will be deallocated via the same allocator during library termination or earlier.</p>
<p>Any memory allocated via this function must be suitably aligned for any object type. If you are using C99 or earlier, this alignment is platform-dependent but will be the same as what <code>realloc</code> provides. If you are using C11 or later, this is the value of <code>alignof(max_align_t)</code>.</p>
<p>The block address will never be <code>NULL</code> and the size will always be greater than zero. Reallocations of a block to size zero are converted into deallocations before reaching the custom allocator. Reallocations of <code>NULL</code> to a non-zero size are converted into regular allocations before reaching the custom allocator.</p>
<p>If this function returns <code>NULL</code>, GLFW will emit <a class="el" href="group__errors.html#ga9023953a2bcb98c2906afd071d21ee7f">GLFW_OUT_OF_MEMORY</a>.</p>
<p>This function must not call any GLFW function.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">block</td><td>The address of the memory block to reallocate. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">size</td><td>The new minimum size, in bytes, of the memory block. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">user</td><td>The user-defined pointer from the allocator. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The address of the newly allocated or resized memory block, or <code>NULL</code> if an error occurred.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned memory block must be valid at least until it is deallocated.</dd></dl>
<dl class="section user"><dt>Reentrancy</dt><dd>This function should not call any GLFW function.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must support being called from any thread that calls GLFW functions.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="intro_guide.html#init_allocator">Custom heap memory allocator</a> </dd>
<dd>
<a class="el" href="struct_g_l_f_wallocator.html">GLFWallocator</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.4. </dd></dl>

</div>
</div>
<a id="ga7181615eda94c4b07bd72bdcee39fa28" name="ga7181615eda94c4b07bd72bdcee39fa28"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7181615eda94c4b07bd72bdcee39fa28">&#9670;&#160;</a></span>GLFWdeallocatefun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWdeallocatefun) (void *block, void *user)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for memory deallocation callbacks. A memory deallocation callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<span class="keywordtype">void</span>* block, <span class="keywordtype">void</span>* user)</div>
</div><!-- fragment --><p>This function may deallocate the specified memory block. This memory block will have been allocated with the same allocator.</p>
<p>This function must support being called during <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a> but before the library is flagged as initialized, as well as during <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a> after the library is no longer flagged as initialized.</p>
<p>The block address will never be <code>NULL</code>. Deallocations of <code>NULL</code> are filtered out before reaching the custom allocator.</p>
<p>If this function returns <code>NULL</code>, GLFW will emit <a class="el" href="group__errors.html#ga9023953a2bcb98c2906afd071d21ee7f">GLFW_OUT_OF_MEMORY</a>.</p>
<p>This function must not call any GLFW function.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">block</td><td>The address of the memory block to deallocate. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">user</td><td>The user-defined pointer from the allocator.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The specified memory block will not be accessed by GLFW after this function is called.</dd></dl>
<dl class="section user"><dt>Reentrancy</dt><dd>This function should not call any GLFW function.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must support being called from any thread that calls GLFW functions.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="intro_guide.html#init_allocator">Custom heap memory allocator</a> </dd>
<dd>
<a class="el" href="struct_g_l_f_wallocator.html">GLFWallocator</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.4. </dd></dl>

</div>
</div>
<a id="ga8184701785c096b3862a75cda1bf44a3" name="ga8184701785c096b3862a75cda1bf44a3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8184701785c096b3862a75cda1bf44a3">&#9670;&#160;</a></span>GLFWerrorfun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWerrorfun) (int error_code, const char *description)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for error callbacks. An error callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> callback_name(<span class="keywordtype">int</span> error_code, <span class="keyword">const</span> <span class="keywordtype">char</span>* description)</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">error_code</td><td>An <a class="el" href="group__errors.html">error code</a>. Future releases may add more error codes. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">description</td><td>A UTF-8 encoded string describing the error.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The error description string is valid until the callback function returns.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="intro_guide.html#error_handling">Error handling</a> </dd>
<dd>
<a class="el" href="group__init.html#gaff45816610d53f0b83656092a4034f40">glfwSetErrorCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga145c57d7f2aeda0b704a5a4ba1d6104b" name="ga145c57d7f2aeda0b704a5a4ba1d6104b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga145c57d7f2aeda0b704a5a4ba1d6104b">&#9670;&#160;</a></span>GLFWallocator</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="struct_g_l_f_wallocator.html">GLFWallocator</a> <a class="el" href="struct_g_l_f_wallocator.html">GLFWallocator</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This describes a custom heap memory allocator for GLFW. To set an allocator, pass it to <a class="el" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a> before initializing the library.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="intro_guide.html#init_allocator">Custom heap memory allocator</a> </dd>
<dd>
<a class="el" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.4. </dd></dl>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="ga317aac130a235ab08c6db0834907d85e" name="ga317aac130a235ab08c6db0834907d85e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga317aac130a235ab08c6db0834907d85e">&#9670;&#160;</a></span>glfwInit()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwInit </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function initializes the GLFW library. Before most GLFW functions can be used, GLFW must be initialized, and before an application terminates GLFW should be terminated in order to free any resources allocated during or after initialization.</p>
<p>If this function fails, it calls <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a> before returning. If it succeeds, you should call <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a> before the application exits.</p>
<p>Additional calls to this function after successful initialization but before termination will return <code>GLFW_TRUE</code> immediately.</p>
<p>The <a class="el" href="intro_guide.html#GLFW_PLATFORM">GLFW_PLATFORM</a> init hint controls which platforms are considered during initialization. This also depends on which platforms the library was compiled to support.</p>
<dl class="section return"><dt>Returns</dt><dd><code>GLFW_TRUE</code> if successful, or <code>GLFW_FALSE</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>macOS:</b> This function will change the current directory of the application to the <code>Contents/Resources</code> subdirectory of the application's bundle, if present. This can be disabled with the <a class="el" href="group__init.html#gab937983147a3158d45f88fad7129d9f2">GLFW_COCOA_CHDIR_RESOURCES</a> init hint.</dd>
<dd>
<b>macOS:</b> This function will create the main menu and dock icon for the application. If GLFW finds a <code>MainMenu.nib</code> it is loaded and assumed to contain a menu bar. Otherwise a minimal menu bar is created manually with common commands like Hide, Quit and About. The About entry opens a minimal about dialog with information from the application's bundle. The menu bar and dock icon can be disabled entirely with the <a class="el" href="group__init.html#ga71e0b4ce2f2696a84a9b8c5e12dc70cf">GLFW_COCOA_MENUBAR</a> init hint.</dd>
<dd>
<b>Wayland, X11:</b> If the library was compiled with support for both Wayland and X11, and the <a class="el" href="intro_guide.html#GLFW_PLATFORM">GLFW_PLATFORM</a> init hint is set to <code>GLFW_ANY_PLATFORM</code>, the <code>XDG_SESSION_TYPE</code> environment variable affects which platform is picked. If the environment variable is not set, or is set to something other than <code>wayland</code> or <code>x11</code>, the regular detection mechanism will be used instead.</dd>
<dd>
<b>X11:</b> This function will set the <code>LC_CTYPE</code> category of the application locale according to the current environment if that category is still "C". This is because the "C" locale breaks Unicode text input.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="intro_guide.html#intro_init">Initialization and termination</a> </dd>
<dd>
<a class="el" href="group__init.html#ga110fd1d3f0412822b4f1908c026f724a">glfwInitHint</a> </dd>
<dd>
<a class="el" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a> </dd>
<dd>
<a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. </dd></dl>

</div>
</div>
<a id="gaaae48c0a18607ea4a4ba951d939f0901" name="gaaae48c0a18607ea4a4ba951d939f0901"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaaae48c0a18607ea4a4ba951d939f0901">&#9670;&#160;</a></span>glfwTerminate()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwTerminate </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function destroys all remaining windows and cursors, restores any modified gamma ramps and frees any other allocated resources. Once this function is called, you must again call <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a> successfully before you will be able to use most GLFW functions.</p>
<p>If GLFW has been successfully initialized, this function should be called before the application exits. If initialization fails, there is no need to call this function, as it is called by <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a> before it returns failure.</p>
<p>This function has no effect if GLFW is not initialized.</p>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>This function may be called before <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a>.</dd></dl>
<dl class="section warning"><dt>Warning</dt><dd>The contexts of any remaining windows must not be current on any other thread when this function is called.</dd></dl>
<dl class="section user"><dt>Reentrancy</dt><dd>This function must not be called from a callback.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="intro_guide.html#intro_init">Initialization and termination</a> </dd>
<dd>
<a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. </dd></dl>

</div>
</div>
<a id="ga110fd1d3f0412822b4f1908c026f724a" name="ga110fd1d3f0412822b4f1908c026f724a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga110fd1d3f0412822b4f1908c026f724a">&#9670;&#160;</a></span>glfwInitHint()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwInitHint </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>hint</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets hints for the next initialization of GLFW.</p>
<p>The values you set hints to are never reset by GLFW, but they only take effect during initialization. Once GLFW has been initialized, any values you set will be ignored until the library is terminated and initialized again.</p>
<p>Some hints are platform specific. These may be set on any platform but they will only affect their specific platform. Other platforms will ignore them. Setting these hints requires no platform specific headers or functions.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">hint</td><td>The <a class="el" href="intro_guide.html#init_hints">init hint</a> to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>The new value of the init hint.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a> and <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>This function may be called before <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd>init_hints </dd>
<dd>
<a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e" title="Initializes the GLFW library.">glfwInit</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga9dde93e9891fa7dd17e4194c9f3ae7c6" name="ga9dde93e9891fa7dd17e4194c9f3ae7c6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9dde93e9891fa7dd17e4194c9f3ae7c6">&#9670;&#160;</a></span>glfwInitAllocator()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwInitAllocator </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="struct_g_l_f_wallocator.html">GLFWallocator</a> *&#160;</td>
          <td class="paramname"><em>allocator</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>To use the default allocator, call this function with a <code>NULL</code> argument.</p>
<p>If you specify an allocator struct, every member must be a valid function pointer. If any member is <code>NULL</code>, this function will emit <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a> and the init allocator will be unchanged.</p>
<p>The functions in the allocator must fulfil a number of requirements. See the documentation for <a class="el" href="group__init.html#ga4306a564e9f60f4de8cc8f31731a3120">GLFWallocatefun</a>, <a class="el" href="group__init.html#ga3e88a829615d8efe8bec1746f7309c63">GLFWreallocatefun</a> and <a class="el" href="group__init.html#ga7181615eda94c4b07bd72bdcee39fa28">GLFWdeallocatefun</a> for details.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">allocator</td><td>The allocator to use at the next initialization, or <code>NULL</code> to use the default one.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The specified allocator is copied before this function returns.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="intro_guide.html#init_allocator">Custom heap memory allocator</a> </dd>
<dd>
<a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.4. </dd></dl>

</div>
</div>
<a id="ga76af552d0307bb5f7791f245417d4752" name="ga76af552d0307bb5f7791f245417d4752"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga76af552d0307bb5f7791f245417d4752">&#9670;&#160;</a></span>glfwInitVulkanLoader()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwInitVulkanLoader </td>
          <td>(</td>
          <td class="paramtype">PFN_vkGetInstanceProcAddr&#160;</td>
          <td class="paramname"><em>loader</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the <code>vkGetInstanceProcAddr</code> function that GLFW will use for all Vulkan related entry point queries.</p>
<p>This feature is mostly useful on macOS, if your copy of the Vulkan loader is in a location where GLFW cannot find it through dynamic loading, or if you are still using the static library version of the loader.</p>
<p>If set to <code>NULL</code>, GLFW will try to load the Vulkan loader dynamically by its standard name and get this function from there. This is the default behavior.</p>
<p>The standard name of the loader is <code>vulkan-1.dll</code> on Windows, <code>libvulkan.so.1</code> on Linux and other Unix-like systems and <code>libvulkan.1.dylib</code> on macOS. If your code is also loading it via these names then you probably don't need to use this function.</p>
<p>The function address you set is never reset by GLFW, but it only takes effect during initialization. Once GLFW has been initialized, any updates will be ignored until the library is terminated and initialized again.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">loader</td><td>The address of the function to use, or <code>NULL</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Loader function signature</dt><dd><div class="fragment"><div class="line">PFN_vkVoidFunction vkGetInstanceProcAddr(VkInstance instance, <span class="keyword">const</span> <span class="keywordtype">char</span>* name)</div>
</div><!-- fragment --> For more information about this function, see the <a href="https://www.khronos.org/registry/vulkan/">Vulkan Registry</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>None.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>This function may be called before <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="vulkan_guide.html#vulkan_loader">Finding the Vulkan loader</a> </dd>
<dd>
<a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.4. </dd></dl>

</div>
</div>
<a id="ga9f8ffaacf3c269cc48eafbf8b9b71197" name="ga9f8ffaacf3c269cc48eafbf8b9b71197"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9f8ffaacf3c269cc48eafbf8b9b71197">&#9670;&#160;</a></span>glfwGetVersion()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwGetVersion </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>major</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>minor</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>rev</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function retrieves the major, minor and revision numbers of the GLFW library. It is intended for when you are using GLFW as a shared library and want to ensure that you are using the minimum required version.</p>
<p>Any or all of the version arguments may be <code>NULL</code>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[out]</td><td class="paramname">major</td><td>Where to store the major version number, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">minor</td><td>Where to store the minor version number, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">rev</td><td>Where to store the revision number, or <code>NULL</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>None.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>This function may be called before <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="intro_guide.html#intro_version">Version management</a> </dd>
<dd>
<a class="el" href="group__init.html#ga026abd003c8e6501981ab1662062f1c0">glfwGetVersionString</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. </dd></dl>

</div>
</div>
<a id="ga026abd003c8e6501981ab1662062f1c0" name="ga026abd003c8e6501981ab1662062f1c0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga026abd003c8e6501981ab1662062f1c0">&#9670;&#160;</a></span>glfwGetVersionString()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char * glfwGetVersionString </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the compile-time generated <a class="el" href="intro_guide.html#intro_version_string">version string</a> of the GLFW library binary. It describes the version, platforms, compiler and any platform or operating system specific compile-time options. It should not be confused with the OpenGL or OpenGL ES version string, queried with <code>glGetString</code>.</p>
<p><b>Do not use the version string</b> to parse the GLFW library version. The <a class="el" href="group__init.html#ga9f8ffaacf3c269cc48eafbf8b9b71197">glfwGetVersion</a> function provides the version of the running library binary in numerical format.</p>
<p><b>Do not use the version string</b> to parse what platforms are supported. The <a class="el" href="group__init.html#ga8785d2b6b36632368d803e78079d38ed">glfwPlatformSupported</a> function lets you query platform support.</p>
<dl class="section return"><dt>Returns</dt><dd>The ASCII encoded GLFW version string.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>None.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>This function may be called before <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned string is static and compile-time generated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="intro_guide.html#intro_version">Version management</a> </dd>
<dd>
<a class="el" href="group__init.html#ga9f8ffaacf3c269cc48eafbf8b9b71197">glfwGetVersion</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga944986b4ec0b928d488141f92982aa18" name="ga944986b4ec0b928d488141f92982aa18"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga944986b4ec0b928d488141f92982aa18">&#9670;&#160;</a></span>glfwGetError()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwGetError </td>
          <td>(</td>
          <td class="paramtype">const char **&#160;</td>
          <td class="paramname"><em>description</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns and clears the <a class="el" href="group__errors.html">error code</a> of the last error that occurred on the calling thread, and optionally a UTF-8 encoded human-readable description of it. If no error has occurred since the last call, it returns <a class="el" href="group__errors.html#gafa30deee5db4d69c4c93d116ed87dbf4">GLFW_NO_ERROR</a> (zero) and the description pointer is set to <code>NULL</code>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">description</td><td>Where to store the error description pointer, or <code>NULL</code>. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The last error code for the calling thread, or <a class="el" href="group__errors.html#gafa30deee5db4d69c4c93d116ed87dbf4">GLFW_NO_ERROR</a> (zero).</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>None.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned string is allocated and freed by GLFW. You should not free it yourself. It is guaranteed to be valid only until the next error occurs or the library is terminated.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>This function may be called before <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="intro_guide.html#error_handling">Error handling</a> </dd>
<dd>
<a class="el" href="group__init.html#gaff45816610d53f0b83656092a4034f40">glfwSetErrorCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="gaff45816610d53f0b83656092a4034f40" name="gaff45816610d53f0b83656092a4034f40"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaff45816610d53f0b83656092a4034f40">&#9670;&#160;</a></span>glfwSetErrorCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__init.html#ga8184701785c096b3862a75cda1bf44a3">GLFWerrorfun</a> glfwSetErrorCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__init.html#ga8184701785c096b3862a75cda1bf44a3">GLFWerrorfun</a>&#160;</td>
          <td class="paramname"><em>callback</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the error callback, which is called with an error code and a human-readable description each time a GLFW error occurs.</p>
<p>The error code is set before the callback is called. Calling <a class="el" href="group__init.html#ga944986b4ec0b928d488141f92982aa18">glfwGetError</a> from the error callback will return the same value as the error code argument.</p>
<p>The error callback is called on the thread where the error occurred. If you are using GLFW from multiple threads, your error callback needs to be written accordingly.</p>
<p>Because the description string may have been generated specifically for that error, it is not guaranteed to be valid after the callback has returned. If you wish to use it after the callback returns, you need to make a copy.</p>
<p>Once set, the error callback remains set even after the library has been terminated.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> callback_name(<span class="keywordtype">int</span> error_code, <span class="keyword">const</span> <span class="keywordtype">char</span>* description)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__init.html#ga8184701785c096b3862a75cda1bf44a3">callback pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>None.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>This function may be called before <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="intro_guide.html#error_handling">Error handling</a> </dd>
<dd>
<a class="el" href="group__init.html#ga944986b4ec0b928d488141f92982aa18">glfwGetError</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga6d6a983d38bd4e8fd786d7a9061d399e" name="ga6d6a983d38bd4e8fd786d7a9061d399e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6d6a983d38bd4e8fd786d7a9061d399e">&#9670;&#160;</a></span>glfwGetPlatform()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwGetPlatform </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the platform that was selected during initialization. The returned value will be one of <code>GLFW_PLATFORM_WIN32</code>, <code>GLFW_PLATFORM_COCOA</code>, <code>GLFW_PLATFORM_WAYLAND</code>, <code>GLFW_PLATFORM_X11</code> or <code>GLFW_PLATFORM_NULL</code>.</p>
<dl class="section return"><dt>Returns</dt><dd>The currently selected platform, or zero if an error occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="intro_guide.html#platform">Runtime platform selection</a> </dd>
<dd>
<a class="el" href="group__init.html#ga8785d2b6b36632368d803e78079d38ed">glfwPlatformSupported</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.4. </dd></dl>

</div>
</div>
<a id="ga8785d2b6b36632368d803e78079d38ed" name="ga8785d2b6b36632368d803e78079d38ed"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8785d2b6b36632368d803e78079d38ed">&#9670;&#160;</a></span>glfwPlatformSupported()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwPlatformSupported </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>platform</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns whether the library was compiled with support for the specified platform. The platform must be one of <code>GLFW_PLATFORM_WIN32</code>, <code>GLFW_PLATFORM_COCOA</code>, <code>GLFW_PLATFORM_WAYLAND</code>, <code>GLFW_PLATFORM_X11</code> or <code>GLFW_PLATFORM_NULL</code>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">platform</td><td>The platform to query. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><code>GLFW_TRUE</code> if the platform is supported, or <code>GLFW_FALSE</code> otherwise.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>This function may be called before <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="intro_guide.html#platform">Runtime platform selection</a> </dd>
<dd>
<a class="el" href="group__init.html#ga6d6a983d38bd4e8fd786d7a9061d399e">glfwGetPlatform</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.4. </dd></dl>

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
