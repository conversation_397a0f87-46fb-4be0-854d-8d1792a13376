// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Devices_Perception_1_H
#define WINRT_Windows_Devices_Perception_1_H
#include "winrt/impl/Windows.Foundation.0.h"
#include "winrt/impl/Windows.Devices.Perception.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices::Perception
{
    struct WINRT_IMPL_EMPTY_BASES IKnownCameraIntrinsicsPropertiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownCameraIntrinsicsPropertiesStatics>
    {
        IKnownCameraIntrinsicsPropertiesStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownCameraIntrinsicsPropertiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKnownPerceptionColorFrameSourcePropertiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownPerceptionColorFrameSourcePropertiesStatics>
    {
        IKnownPerceptionColorFrameSourcePropertiesStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownPerceptionColorFrameSourcePropertiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKnownPerceptionDepthFrameSourcePropertiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownPerceptionDepthFrameSourcePropertiesStatics>
    {
        IKnownPerceptionDepthFrameSourcePropertiesStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownPerceptionDepthFrameSourcePropertiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKnownPerceptionFrameSourcePropertiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownPerceptionFrameSourcePropertiesStatics>
    {
        IKnownPerceptionFrameSourcePropertiesStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownPerceptionFrameSourcePropertiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKnownPerceptionFrameSourcePropertiesStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownPerceptionFrameSourcePropertiesStatics2>
    {
        IKnownPerceptionFrameSourcePropertiesStatics2(std::nullptr_t = nullptr) noexcept {}
        IKnownPerceptionFrameSourcePropertiesStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKnownPerceptionInfraredFrameSourcePropertiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownPerceptionInfraredFrameSourcePropertiesStatics>
    {
        IKnownPerceptionInfraredFrameSourcePropertiesStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownPerceptionInfraredFrameSourcePropertiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKnownPerceptionVideoFrameSourcePropertiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownPerceptionVideoFrameSourcePropertiesStatics>
    {
        IKnownPerceptionVideoFrameSourcePropertiesStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownPerceptionVideoFrameSourcePropertiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKnownPerceptionVideoProfilePropertiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownPerceptionVideoProfilePropertiesStatics>
    {
        IKnownPerceptionVideoProfilePropertiesStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownPerceptionVideoProfilePropertiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionColorFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionColorFrame>,
        impl::require<winrt::Windows::Devices::Perception::IPerceptionColorFrame, winrt::Windows::Foundation::IClosable>
    {
        IPerceptionColorFrame(std::nullptr_t = nullptr) noexcept {}
        IPerceptionColorFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionColorFrameArrivedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionColorFrameArrivedEventArgs>
    {
        IPerceptionColorFrameArrivedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPerceptionColorFrameArrivedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionColorFrameReader :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionColorFrameReader>,
        impl::require<winrt::Windows::Devices::Perception::IPerceptionColorFrameReader, winrt::Windows::Foundation::IClosable>
    {
        IPerceptionColorFrameReader(std::nullptr_t = nullptr) noexcept {}
        IPerceptionColorFrameReader(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionColorFrameSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionColorFrameSource>
    {
        IPerceptionColorFrameSource(std::nullptr_t = nullptr) noexcept {}
        IPerceptionColorFrameSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionColorFrameSource2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionColorFrameSource2>
    {
        IPerceptionColorFrameSource2(std::nullptr_t = nullptr) noexcept {}
        IPerceptionColorFrameSource2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionColorFrameSourceAddedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionColorFrameSourceAddedEventArgs>
    {
        IPerceptionColorFrameSourceAddedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPerceptionColorFrameSourceAddedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionColorFrameSourceRemovedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionColorFrameSourceRemovedEventArgs>
    {
        IPerceptionColorFrameSourceRemovedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPerceptionColorFrameSourceRemovedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionColorFrameSourceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionColorFrameSourceStatics>
    {
        IPerceptionColorFrameSourceStatics(std::nullptr_t = nullptr) noexcept {}
        IPerceptionColorFrameSourceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionColorFrameSourceWatcher :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionColorFrameSourceWatcher>
    {
        IPerceptionColorFrameSourceWatcher(std::nullptr_t = nullptr) noexcept {}
        IPerceptionColorFrameSourceWatcher(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionControlSession :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionControlSession>,
        impl::require<winrt::Windows::Devices::Perception::IPerceptionControlSession, winrt::Windows::Foundation::IClosable>
    {
        IPerceptionControlSession(std::nullptr_t = nullptr) noexcept {}
        IPerceptionControlSession(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionDepthCorrelatedCameraIntrinsics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionDepthCorrelatedCameraIntrinsics>
    {
        IPerceptionDepthCorrelatedCameraIntrinsics(std::nullptr_t = nullptr) noexcept {}
        IPerceptionDepthCorrelatedCameraIntrinsics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionDepthCorrelatedCoordinateMapper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionDepthCorrelatedCoordinateMapper>
    {
        IPerceptionDepthCorrelatedCoordinateMapper(std::nullptr_t = nullptr) noexcept {}
        IPerceptionDepthCorrelatedCoordinateMapper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionDepthFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionDepthFrame>,
        impl::require<winrt::Windows::Devices::Perception::IPerceptionDepthFrame, winrt::Windows::Foundation::IClosable>
    {
        IPerceptionDepthFrame(std::nullptr_t = nullptr) noexcept {}
        IPerceptionDepthFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionDepthFrameArrivedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionDepthFrameArrivedEventArgs>
    {
        IPerceptionDepthFrameArrivedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPerceptionDepthFrameArrivedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionDepthFrameReader :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionDepthFrameReader>,
        impl::require<winrt::Windows::Devices::Perception::IPerceptionDepthFrameReader, winrt::Windows::Foundation::IClosable>
    {
        IPerceptionDepthFrameReader(std::nullptr_t = nullptr) noexcept {}
        IPerceptionDepthFrameReader(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionDepthFrameSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionDepthFrameSource>
    {
        IPerceptionDepthFrameSource(std::nullptr_t = nullptr) noexcept {}
        IPerceptionDepthFrameSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionDepthFrameSource2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionDepthFrameSource2>
    {
        IPerceptionDepthFrameSource2(std::nullptr_t = nullptr) noexcept {}
        IPerceptionDepthFrameSource2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionDepthFrameSourceAddedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionDepthFrameSourceAddedEventArgs>
    {
        IPerceptionDepthFrameSourceAddedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPerceptionDepthFrameSourceAddedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionDepthFrameSourceRemovedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionDepthFrameSourceRemovedEventArgs>
    {
        IPerceptionDepthFrameSourceRemovedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPerceptionDepthFrameSourceRemovedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionDepthFrameSourceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionDepthFrameSourceStatics>
    {
        IPerceptionDepthFrameSourceStatics(std::nullptr_t = nullptr) noexcept {}
        IPerceptionDepthFrameSourceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionDepthFrameSourceWatcher :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionDepthFrameSourceWatcher>
    {
        IPerceptionDepthFrameSourceWatcher(std::nullptr_t = nullptr) noexcept {}
        IPerceptionDepthFrameSourceWatcher(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionFrameSourcePropertiesChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionFrameSourcePropertiesChangedEventArgs>
    {
        IPerceptionFrameSourcePropertiesChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPerceptionFrameSourcePropertiesChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionFrameSourcePropertyChangeResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionFrameSourcePropertyChangeResult>
    {
        IPerceptionFrameSourcePropertyChangeResult(std::nullptr_t = nullptr) noexcept {}
        IPerceptionFrameSourcePropertyChangeResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionInfraredFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionInfraredFrame>,
        impl::require<winrt::Windows::Devices::Perception::IPerceptionInfraredFrame, winrt::Windows::Foundation::IClosable>
    {
        IPerceptionInfraredFrame(std::nullptr_t = nullptr) noexcept {}
        IPerceptionInfraredFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionInfraredFrameArrivedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionInfraredFrameArrivedEventArgs>
    {
        IPerceptionInfraredFrameArrivedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPerceptionInfraredFrameArrivedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionInfraredFrameReader :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionInfraredFrameReader>,
        impl::require<winrt::Windows::Devices::Perception::IPerceptionInfraredFrameReader, winrt::Windows::Foundation::IClosable>
    {
        IPerceptionInfraredFrameReader(std::nullptr_t = nullptr) noexcept {}
        IPerceptionInfraredFrameReader(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionInfraredFrameSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionInfraredFrameSource>
    {
        IPerceptionInfraredFrameSource(std::nullptr_t = nullptr) noexcept {}
        IPerceptionInfraredFrameSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionInfraredFrameSource2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionInfraredFrameSource2>
    {
        IPerceptionInfraredFrameSource2(std::nullptr_t = nullptr) noexcept {}
        IPerceptionInfraredFrameSource2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionInfraredFrameSourceAddedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionInfraredFrameSourceAddedEventArgs>
    {
        IPerceptionInfraredFrameSourceAddedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPerceptionInfraredFrameSourceAddedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionInfraredFrameSourceRemovedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionInfraredFrameSourceRemovedEventArgs>
    {
        IPerceptionInfraredFrameSourceRemovedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPerceptionInfraredFrameSourceRemovedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionInfraredFrameSourceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionInfraredFrameSourceStatics>
    {
        IPerceptionInfraredFrameSourceStatics(std::nullptr_t = nullptr) noexcept {}
        IPerceptionInfraredFrameSourceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionInfraredFrameSourceWatcher :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionInfraredFrameSourceWatcher>
    {
        IPerceptionInfraredFrameSourceWatcher(std::nullptr_t = nullptr) noexcept {}
        IPerceptionInfraredFrameSourceWatcher(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionVideoProfile :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionVideoProfile>
    {
        IPerceptionVideoProfile(std::nullptr_t = nullptr) noexcept {}
        IPerceptionVideoProfile(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
