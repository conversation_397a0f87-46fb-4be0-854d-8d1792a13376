# CMake generated Testfile for 
# Source directory: D:/AI/opencv/opencv-4.10.0
# Build directory: D:/AI/opencv/cudabuild
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
subdirs("3rdparty/zlib")
subdirs("3rdparty/libjpeg-turbo")
subdirs("3rdparty/libtiff")
subdirs("3rdparty/libwebp")
subdirs("3rdparty/openjpeg")
subdirs("3rdparty/libpng")
subdirs("3rdparty/openexr")
subdirs("3rdparty/ippiw")
subdirs("3rdparty/protobuf")
subdirs("3rdparty/ittnotify")
subdirs("include")
subdirs("modules/.firstpass/calib3d")
subdirs("modules/.firstpass/core")
subdirs("modules/.firstpass/dnn")
subdirs("modules/.firstpass/features2d")
subdirs("modules/.firstpass/flann")
subdirs("modules/.firstpass/gapi")
subdirs("modules/.firstpass/highgui")
subdirs("modules/.firstpass/imgcodecs")
subdirs("modules/.firstpass/imgproc")
subdirs("modules/.firstpass/java")
subdirs("modules/.firstpass/js")
subdirs("modules/.firstpass/ml")
subdirs("modules/.firstpass/objc")
subdirs("modules/.firstpass/objdetect")
subdirs("modules/.firstpass/photo")
subdirs("modules/.firstpass/python")
subdirs("modules/.firstpass/stitching")
subdirs("modules/.firstpass/ts")
subdirs("modules/.firstpass/video")
subdirs("modules/.firstpass/videoio")
subdirs("modules/.firstpass/world")
subdirs("modules/.firstpass/alphamat")
subdirs("modules/.firstpass/aruco")
subdirs("modules/.firstpass/bgsegm")
subdirs("modules/.firstpass/bioinspired")
subdirs("modules/.firstpass/cannops")
subdirs("modules/.firstpass/ccalib")
subdirs("modules/.firstpass/cnn_3dobj")
subdirs("modules/.firstpass/cudaarithm")
subdirs("modules/.firstpass/cudabgsegm")
subdirs("modules/.firstpass/cudacodec")
subdirs("modules/.firstpass/cudafeatures2d")
subdirs("modules/.firstpass/cudafilters")
subdirs("modules/.firstpass/cudaimgproc")
subdirs("modules/.firstpass/cudalegacy")
subdirs("modules/.firstpass/cudaobjdetect")
subdirs("modules/.firstpass/cudaoptflow")
subdirs("modules/.firstpass/cudastereo")
subdirs("modules/.firstpass/cudawarping")
subdirs("modules/.firstpass/cudev")
subdirs("modules/.firstpass/cvv")
subdirs("modules/.firstpass/datasets")
subdirs("modules/.firstpass/dnn_objdetect")
subdirs("modules/.firstpass/dnn_superres")
subdirs("modules/.firstpass/dpm")
subdirs("modules/.firstpass/face")
subdirs("modules/.firstpass/freetype")
subdirs("modules/.firstpass/fuzzy")
subdirs("modules/.firstpass/hdf")
subdirs("modules/.firstpass/hfs")
subdirs("modules/.firstpass/img_hash")
subdirs("modules/.firstpass/intensity_transform")
subdirs("modules/.firstpass/julia")
subdirs("modules/.firstpass/line_descriptor")
subdirs("modules/.firstpass/matlab")
subdirs("modules/.firstpass/mcc")
subdirs("modules/.firstpass/optflow")
subdirs("modules/.firstpass/ovis")
subdirs("modules/.firstpass/phase_unwrapping")
subdirs("modules/.firstpass/plot")
subdirs("modules/.firstpass/quality")
subdirs("modules/.firstpass/rapid")
subdirs("modules/.firstpass/reg")
subdirs("modules/.firstpass/rgbd")
subdirs("modules/.firstpass/saliency")
subdirs("modules/.firstpass/sfm")
subdirs("modules/.firstpass/shape")
subdirs("modules/.firstpass/signal")
subdirs("modules/.firstpass/stereo")
subdirs("modules/.firstpass/structured_light")
subdirs("modules/.firstpass/superres")
subdirs("modules/.firstpass/surface_matching")
subdirs("modules/.firstpass/text")
subdirs("modules/.firstpass/tracking")
subdirs("modules/.firstpass/videostab")
subdirs("modules/.firstpass/viz")
subdirs("modules/.firstpass/wechat_qrcode")
subdirs("modules/.firstpass/xfeatures2d")
subdirs("modules/.firstpass/ximgproc")
subdirs("modules/.firstpass/xobjdetect")
subdirs("modules/.firstpass/xphoto")
subdirs("modules/python_tests")
subdirs("modules/world")
subdirs("modules/img_hash")
subdirs("modules/java_bindings_generator")
subdirs("modules/js_bindings_generator")
subdirs("modules/objc_bindings_generator")
subdirs("modules/python_bindings_generator")
subdirs("modules/ts")
subdirs("modules/python3")
subdirs("doc")
subdirs("data")
subdirs("apps")
