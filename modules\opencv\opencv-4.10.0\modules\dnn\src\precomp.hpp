/*M///////////////////////////////////////////////////////////////////////////////////////
//
//  IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
//
//  By downloading, copying, installing or using the software you agree to this license.
//  If you do not agree to this license, do not download, install,
//  copy or use the software.
//
//
//                           License Agreement
//                For Open Source Computer Vision Library
//
// Copyright (C) 2013, OpenCV Foundation, all rights reserved.
// Third party copyrights are property of their respective owners.
//
// Redistribution and use in source and binary forms, with or without modification,
// are permitted provided that the following conditions are met:
//
//   * Redistribution's of source code must retain the above copyright notice,
//     this list of conditions and the following disclaimer.
//
//   * Redistribution's in binary form must reproduce the above copyright notice,
//     this list of conditions and the following disclaimer in the documentation
//     and/or other materials provided with the distribution.
//
//   * The name of the copyright holders may not be used to endorse or promote products
//     derived from this software without specific prior written permission.
//
// This software is provided by the copyright holders and contributors "as is" and
// any express or implied warranties, including, but not limited to, the implied
// warranties of merchantability and fitness for a particular purpose are disclaimed.
// In no event shall the Intel Corporation or contributors be liable for any direct,
// indirect, incidental, special, exemplary, or consequential damages
// (including, but not limited to, procurement of substitute goods or services;
// loss of use, data, or profits; or business interruption) however caused
// and on any theory of liability, whether in contract, strict liability,
// or tort (including negligence or otherwise) arising in any way out of
// the use of this software, even if advised of the possibility of such damage.
//
//M*/

#if !defined(BUILD_PLUGIN)
#include "cvconfig.h"
#else
#include <opencv2/core/cvdef.h>
#undef __OPENCV_BUILD  // allow public API only
#endif

#include <opencv2/core.hpp>

#ifndef CV_OCL4DNN
#define CV_OCL4DNN 0
#endif

#if CV_OCL4DNN
#ifndef HAVE_OPENCL
#error "Configuration error: re-run CMake from clean build directory"
#endif
#else
#undef HAVE_OPENCL
#endif

#ifndef CV_CUDA4DNN
#define CV_CUDA4DNN 0
#endif

#if CV_CUDA4DNN
#ifndef HAVE_CUDA
#error "Configuration error: re-run CMake from clean build directory"
#endif
#else
#undef HAVE_CUDA
#endif

#include <numeric>
#include <memory>
#include <algorithm>
#include <fstream>
#include <sstream>
#include <vector>
#include <set>
#include <iterator>

#include <opencv2/core/ocl.hpp>
#include <opencv2/core/opencl/ocl_defs.hpp>

#include <opencv2/core/utils/trace.hpp>
#include <opencv2/dnn.hpp>
#include <opencv2/dnn/all_layers.hpp>
#include <opencv2/dnn/shape_utils.hpp>

#include "dnn_common.hpp"