﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\perf\opencl\perf_brute_force_matcher.cpp">
      <Filter>opencv_features2d\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\perf\opencl\perf_feature2d.cpp">
      <Filter>opencv_features2d\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\perf\perf_batchDistance.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\perf\perf_feature2d.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\perf\perf_main.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\perf\perf_feature2d.hpp">
      <Filter>opencv_features2d\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\perf\perf_precomp.hpp">
      <Filter>opencv_features2d\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_features2d">
      <UniqueIdentifier>{C24905E9-C93C-39F2-920C-0B0ABC5F4494}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_features2d\Include">
      <UniqueIdentifier>{332D887C-DBBD-3166-BFC2-1DE26103E2F4}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_features2d\Src">
      <UniqueIdentifier>{89F5B458-684E-33A2-AE1B-2BD20A1B4667}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_features2d\Src\opencl">
      <UniqueIdentifier>{796A2101-9560-3BDC-84B3-6EE242D819BB}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
