﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\npy_blob.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_backends.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_caffe_importer.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_common.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_darknet_importer.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_googlenet.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_graph_simplifier.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_ie_models.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_int8_layers.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_layers.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_main.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_misc.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_model.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_nms.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_onnx_conformance.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_onnx_importer.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_tf_importer.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_tflite_importer.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_torch_importer.cpp">
      <Filter>opencv_dnn\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\npy_blob.hpp">
      <Filter>opencv_dnn\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_common.hpp">
      <Filter>test_common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_common.impl.hpp">
      <Filter>test_common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_onnx_conformance_layer_filter__cuda_denylist.inl.hpp">
      <Filter>opencv_dnn\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_onnx_conformance_layer_filter__halide_denylist.inl.hpp">
      <Filter>opencv_dnn\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_onnx_conformance_layer_filter__openvino.inl.hpp">
      <Filter>opencv_dnn\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_onnx_conformance_layer_filter__vulkan_denylist.inl.hpp">
      <Filter>opencv_dnn\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_onnx_conformance_layer_filter_opencv_all_denylist.inl.hpp">
      <Filter>opencv_dnn\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_onnx_conformance_layer_filter_opencv_cpu_denylist.inl.hpp">
      <Filter>opencv_dnn\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_onnx_conformance_layer_filter_opencv_denylist.inl.hpp">
      <Filter>opencv_dnn\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_onnx_conformance_layer_filter_opencv_ocl_fp16_denylist.inl.hpp">
      <Filter>opencv_dnn\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_onnx_conformance_layer_filter_opencv_ocl_fp32_denylist.inl.hpp">
      <Filter>opencv_dnn\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_onnx_conformance_layer_parser_denylist.inl.hpp">
      <Filter>opencv_dnn\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_precomp.hpp">
      <Filter>opencv_dnn\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_dnn">
      <UniqueIdentifier>{FE93695A-2B17-351B-93DC-22910FCEB671}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_dnn\Include">
      <UniqueIdentifier>{F0AB4C79-BB50-3A9B-BE0C-940093D7DF92}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_dnn\Src">
      <UniqueIdentifier>{03809765-19F6-3A27-8D03-056778E7EFF4}</UniqueIdentifier>
    </Filter>
    <Filter Include="test_common">
      <UniqueIdentifier>{C47A5B31-D42B-3AEF-A551-8A65CBA791F6}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
