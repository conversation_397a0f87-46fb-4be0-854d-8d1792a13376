﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="D:\AI\opencv\cudabuild\CMakeFiles\3abc2d8730523df90ed3765ef9e72a5c\pyopencv_generated_enums.h.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\AI\opencv\cudabuild\CMakeFiles\ed40b52aabc93b37e7b0d31a9311c8ff\gen_opencv_python_source.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\AI\opencv\cudabuild\modules\python_bindings_generator\CMakeFiles\gen_opencv_python_source" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{D15E165F-1928-31B6-B5AD-589C9FB01FC7}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
