// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		5B8C4C2D243814E9003D9176 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 5B8C4C2C243814A0003D9176 /* libc++.tbd */; };
		5B8C4C31243849E9003D9176 /* ColorBlobDetector.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5B8C4C30243849E9003D9176 /* ColorBlobDetector.swift */; };
		5BBDB48A240D380F00BB10FE /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5BBDB489240D380F00BB10FE /* AppDelegate.swift */; };
		5BBDB48E240D380F00BB10FE /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5BBDB48D240D380F00BB10FE /* ViewController.swift */; };
		5BBDB491240D380F00BB10FE /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 5BBDB48F240D380F00BB10FE /* Main.storyboard */; };
		5BBDB493240D381000BB10FE /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 5BBDB492240D381000BB10FE /* Assets.xcassets */; };
		5BBDB496240D381000BB10FE /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 5BBDB494240D381000BB10FE /* LaunchScreen.storyboard */; };
		5BBDB517241923C800BB10FE /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5BBDB516241923C800BB10FE /* AVFoundation.framework */; };
		5BBDB5192419241200BB10FE /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5BBDB5182419241200BB10FE /* CoreMedia.framework */; };
		5BE7205625201342004E84AE /* OpenCV.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5B14CF7F2452E926003438BE /* OpenCV.framework */; };
		5BE7205725201342004E84AE /* OpenCV.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 5B14CF7F2452E926003438BE /* OpenCV.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		5BE7205825201342004E84AE /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				5BE7205725201342004E84AE /* OpenCV.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		5B14CF7F2452E926003438BE /* OpenCV.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenCV.framework; path = ../../../OpenCV.framework; sourceTree = "<group>"; };
		5B8C4C2C243814A0003D9176 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		5B8C4C30243849E9003D9176 /* ColorBlobDetector.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ColorBlobDetector.swift; sourceTree = "<group>"; };
		5BBDB486240D380F00BB10FE /* ColorBlobDetection.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ColorBlobDetection.app; sourceTree = BUILT_PRODUCTS_DIR; };
		5BBDB489240D380F00BB10FE /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		5BBDB48D240D380F00BB10FE /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		5BBDB490240D380F00BB10FE /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		5BBDB492240D381000BB10FE /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		5BBDB495240D381000BB10FE /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		5BBDB497240D381000BB10FE /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		5BBDB516241923C800BB10FE /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		5BBDB5182419241200BB10FE /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		5BBDB483240D380F00BB10FE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5BBDB5192419241200BB10FE /* CoreMedia.framework in Frameworks */,
				5B8C4C2D243814E9003D9176 /* libc++.tbd in Frameworks */,
				5BBDB517241923C800BB10FE /* AVFoundation.framework in Frameworks */,
				5BE7205625201342004E84AE /* OpenCV.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		5BBDB47D240D380F00BB10FE = {
			isa = PBXGroup;
			children = (
				5BBDB488240D380F00BB10FE /* ColorBlobDetection */,
				5BBDB487240D380F00BB10FE /* Products */,
				5BBDB515241923C800BB10FE /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		5BBDB487240D380F00BB10FE /* Products */ = {
			isa = PBXGroup;
			children = (
				5BBDB486240D380F00BB10FE /* ColorBlobDetection.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		5BBDB488240D380F00BB10FE /* ColorBlobDetection */ = {
			isa = PBXGroup;
			children = (
				5B14CF7F2452E926003438BE /* OpenCV.framework */,
				5BBDB489240D380F00BB10FE /* AppDelegate.swift */,
				5BBDB48D240D380F00BB10FE /* ViewController.swift */,
				5BBDB48F240D380F00BB10FE /* Main.storyboard */,
				5BBDB492240D381000BB10FE /* Assets.xcassets */,
				5BBDB494240D381000BB10FE /* LaunchScreen.storyboard */,
				5BBDB497240D381000BB10FE /* Info.plist */,
				5B8C4C30243849E9003D9176 /* ColorBlobDetector.swift */,
			);
			path = ColorBlobDetection;
			sourceTree = "<group>";
		};
		5BBDB515241923C800BB10FE /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				5B8C4C2C243814A0003D9176 /* libc++.tbd */,
				5BBDB5182419241200BB10FE /* CoreMedia.framework */,
				5BBDB516241923C800BB10FE /* AVFoundation.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		5BBDB485240D380F00BB10FE /* ColorBlobDetection */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5BBDB4B0240D381000BB10FE /* Build configuration list for PBXNativeTarget "ColorBlobDetection" */;
			buildPhases = (
				5BBDB482240D380F00BB10FE /* Sources */,
				5BBDB483240D380F00BB10FE /* Frameworks */,
				5BBDB484240D380F00BB10FE /* Resources */,
				5BE7205825201342004E84AE /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = ColorBlobDetection;
			productName = ColorBlobDetection;
			productReference = 5BBDB486240D380F00BB10FE /* ColorBlobDetection.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		5BBDB47E240D380F00BB10FE /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1130;
				LastUpgradeCheck = 1130;
				ORGANIZATIONNAME = OpenCV;
				TargetAttributes = {
					5BBDB485240D380F00BB10FE = {
						CreatedOnToolsVersion = 11.3.1;
						LastSwiftMigration = 1130;
					};
				};
			};
			buildConfigurationList = 5BBDB481240D380F00BB10FE /* Build configuration list for PBXProject "ColorBlobDetectionDynamic" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 5BBDB47D240D380F00BB10FE;
			productRefGroup = 5BBDB487240D380F00BB10FE /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				5BBDB485240D380F00BB10FE /* ColorBlobDetection */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		5BBDB484240D380F00BB10FE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5BBDB496240D381000BB10FE /* LaunchScreen.storyboard in Resources */,
				5BBDB493240D381000BB10FE /* Assets.xcassets in Resources */,
				5BBDB491240D380F00BB10FE /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		5BBDB482240D380F00BB10FE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5BBDB48E240D380F00BB10FE /* ViewController.swift in Sources */,
				5B8C4C31243849E9003D9176 /* ColorBlobDetector.swift in Sources */,
				5BBDB48A240D380F00BB10FE /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		5BBDB48F240D380F00BB10FE /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				5BBDB490240D380F00BB10FE /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		5BBDB494240D381000BB10FE /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				5BBDB495240D381000BB10FE /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		5BBDB4AE240D381000BB10FE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				FRAMEWORK_SEARCH_PATHS = ../..;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = "../../OpenCV/**";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = "";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OBJC_BRIDGING_HEADER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		5BBDB4AF240D381000BB10FE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				FRAMEWORK_SEARCH_PATHS = ../..;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = "../../OpenCV/**";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				OTHER_LDFLAGS = "";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OBJC_BRIDGING_HEADER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		5BBDB4B1240D381000BB10FE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = NO;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/../..",
				);
				INFOPLIST_FILE = ColorBlobDetection/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_SWIFT_FLAGS = "-Xcc -Wno-incomplete-umbrella";
				PRODUCT_BUNDLE_IDENTIFIER = org.opencv.ColorBlobDetection;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		5BBDB4B2240D381000BB10FE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = NO;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/../..",
				);
				INFOPLIST_FILE = ColorBlobDetection/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_SWIFT_FLAGS = "-Xcc -Wno-incomplete-umbrella";
				PRODUCT_BUNDLE_IDENTIFIER = org.opencv.ColorBlobDetection;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		5BBDB481240D380F00BB10FE /* Build configuration list for PBXProject "ColorBlobDetectionDynamic" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5BBDB4AE240D381000BB10FE /* Debug */,
				5BBDB4AF240D381000BB10FE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5BBDB4B0240D381000BB10FE /* Build configuration list for PBXNativeTarget "ColorBlobDetection" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5BBDB4B1240D381000BB10FE /* Debug */,
				5BBDB4B2240D381000BB10FE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 5BBDB47E240D380F00BB10FE /* Project object */;
}
