^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\3ABC2D8730523DF90ED3765EF9E72A5C\PYOPENCV_GENERATED_ENUMS.H.RULE
setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe D:/AI/opencv/opencv-4.10.0/modules/python/src2/gen2.py D:/AI/opencv/cudabuild/modules/python_bindings_generator D:/AI/opencv/cudabuild/modules/python_bindings_generator/headers.txt
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\ED40B52AABC93B37E7B0D31A9311C8FF\GEN_OPENCV_PYTHON_SOURCE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
