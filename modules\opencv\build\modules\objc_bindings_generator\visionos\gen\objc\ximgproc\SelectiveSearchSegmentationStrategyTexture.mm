//
// This file is auto-generated. Please don't modify it!
//

#import "SelectiveSearchSegmentationStrategyTexture.h"
#import "CVObjcUtil.h"



@implementation SelectiveSearchSegmentationStrategyTexture


- (instancetype)initWithNativePtr:(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyTexture>)nativePtr {
    self = [super initWithNativePtr:nativePtr];
    if (self) {
        _nativePtrSelectiveSearchSegmentationStrategyTexture = nativePtr;
    }
    return self;
}

+ (instancetype)fromNative:(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyTexture>)nativePtr {
    return [[SelectiveSearchSegmentationStrategyTexture alloc] initWithNativePtr:nativePtr];
}




@end


