// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Automation_1_H
#define WINRT_Windows_UI_Xaml_Automation_1_H
#include "winrt/impl/Windows.UI.Xaml.Automation.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Automation
{
    struct WINRT_IMPL_EMPTY_BASES IAnnotationPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnnotationPatternIdentifiers>
    {
        IAnnotationPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IAnnotationPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAnnotationPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnnotationPatternIdentifiersStatics>
    {
        IAnnotationPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IAnnotationPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationAnnotation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationAnnotation>
    {
        IAutomationAnnotation(std::nullptr_t = nullptr) noexcept {}
        IAutomationAnnotation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationAnnotationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationAnnotationFactory>
    {
        IAutomationAnnotationFactory(std::nullptr_t = nullptr) noexcept {}
        IAutomationAnnotationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationAnnotationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationAnnotationStatics>
    {
        IAutomationAnnotationStatics(std::nullptr_t = nullptr) noexcept {}
        IAutomationAnnotationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationElementIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiers>
    {
        IAutomationElementIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationElementIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiersStatics>
    {
        IAutomationElementIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationElementIdentifiersStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiersStatics2>
    {
        IAutomationElementIdentifiersStatics2(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiersStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationElementIdentifiersStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiersStatics3>
    {
        IAutomationElementIdentifiersStatics3(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiersStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationElementIdentifiersStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiersStatics4>
    {
        IAutomationElementIdentifiersStatics4(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiersStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationElementIdentifiersStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiersStatics5>
    {
        IAutomationElementIdentifiersStatics5(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiersStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationElementIdentifiersStatics6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiersStatics6>
    {
        IAutomationElementIdentifiersStatics6(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiersStatics6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationElementIdentifiersStatics7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiersStatics7>
    {
        IAutomationElementIdentifiersStatics7(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiersStatics7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationElementIdentifiersStatics8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationElementIdentifiersStatics8>
    {
        IAutomationElementIdentifiersStatics8(std::nullptr_t = nullptr) noexcept {}
        IAutomationElementIdentifiersStatics8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationProperties>
    {
        IAutomationProperties(std::nullptr_t = nullptr) noexcept {}
        IAutomationProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationPropertiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics>
    {
        IAutomationPropertiesStatics(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationPropertiesStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics2>
    {
        IAutomationPropertiesStatics2(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationPropertiesStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics3>
    {
        IAutomationPropertiesStatics3(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationPropertiesStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics4>
    {
        IAutomationPropertiesStatics4(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationPropertiesStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics5>
    {
        IAutomationPropertiesStatics5(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationPropertiesStatics6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics6>
    {
        IAutomationPropertiesStatics6(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationPropertiesStatics7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics7>
    {
        IAutomationPropertiesStatics7(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationPropertiesStatics8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics8>
    {
        IAutomationPropertiesStatics8(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationPropertiesStatics9 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPropertiesStatics9>
    {
        IAutomationPropertiesStatics9(std::nullptr_t = nullptr) noexcept {}
        IAutomationPropertiesStatics9(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutomationProperty :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationProperty>
    {
        IAutomationProperty(std::nullptr_t = nullptr) noexcept {}
        IAutomationProperty(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDockPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDockPatternIdentifiers>
    {
        IDockPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IDockPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDockPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDockPatternIdentifiersStatics>
    {
        IDockPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IDockPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDragPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragPatternIdentifiers>
    {
        IDragPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IDragPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDragPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragPatternIdentifiersStatics>
    {
        IDragPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IDragPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDropTargetPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropTargetPatternIdentifiers>
    {
        IDropTargetPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IDropTargetPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDropTargetPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropTargetPatternIdentifiersStatics>
    {
        IDropTargetPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IDropTargetPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IExpandCollapsePatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExpandCollapsePatternIdentifiers>
    {
        IExpandCollapsePatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IExpandCollapsePatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IExpandCollapsePatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExpandCollapsePatternIdentifiersStatics>
    {
        IExpandCollapsePatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IExpandCollapsePatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGridItemPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridItemPatternIdentifiers>
    {
        IGridItemPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IGridItemPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGridItemPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridItemPatternIdentifiersStatics>
    {
        IGridItemPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IGridItemPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGridPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridPatternIdentifiers>
    {
        IGridPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IGridPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGridPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridPatternIdentifiersStatics>
    {
        IGridPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IGridPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMultipleViewPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMultipleViewPatternIdentifiers>
    {
        IMultipleViewPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IMultipleViewPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMultipleViewPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMultipleViewPatternIdentifiersStatics>
    {
        IMultipleViewPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IMultipleViewPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRangeValuePatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRangeValuePatternIdentifiers>
    {
        IRangeValuePatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IRangeValuePatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRangeValuePatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRangeValuePatternIdentifiersStatics>
    {
        IRangeValuePatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IRangeValuePatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IScrollPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollPatternIdentifiers>
    {
        IScrollPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IScrollPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IScrollPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollPatternIdentifiersStatics>
    {
        IScrollPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IScrollPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISelectionItemPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectionItemPatternIdentifiers>
    {
        ISelectionItemPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        ISelectionItemPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISelectionItemPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectionItemPatternIdentifiersStatics>
    {
        ISelectionItemPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ISelectionItemPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISelectionPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectionPatternIdentifiers>
    {
        ISelectionPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        ISelectionPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISelectionPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectionPatternIdentifiersStatics>
    {
        ISelectionPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ISelectionPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISpreadsheetItemPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpreadsheetItemPatternIdentifiers>
    {
        ISpreadsheetItemPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        ISpreadsheetItemPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISpreadsheetItemPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpreadsheetItemPatternIdentifiersStatics>
    {
        ISpreadsheetItemPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ISpreadsheetItemPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStylesPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStylesPatternIdentifiers>
    {
        IStylesPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IStylesPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStylesPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStylesPatternIdentifiersStatics>
    {
        IStylesPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IStylesPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITableItemPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITableItemPatternIdentifiers>
    {
        ITableItemPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        ITableItemPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITableItemPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITableItemPatternIdentifiersStatics>
    {
        ITableItemPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ITableItemPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITablePatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITablePatternIdentifiers>
    {
        ITablePatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        ITablePatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITablePatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITablePatternIdentifiersStatics>
    {
        ITablePatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ITablePatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITogglePatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITogglePatternIdentifiers>
    {
        ITogglePatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        ITogglePatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITogglePatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITogglePatternIdentifiersStatics>
    {
        ITogglePatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ITogglePatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITransformPattern2Identifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITransformPattern2Identifiers>
    {
        ITransformPattern2Identifiers(std::nullptr_t = nullptr) noexcept {}
        ITransformPattern2Identifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITransformPattern2IdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITransformPattern2IdentifiersStatics>
    {
        ITransformPattern2IdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ITransformPattern2IdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITransformPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITransformPatternIdentifiers>
    {
        ITransformPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        ITransformPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITransformPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITransformPatternIdentifiersStatics>
    {
        ITransformPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        ITransformPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IValuePatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IValuePatternIdentifiers>
    {
        IValuePatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IValuePatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IValuePatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IValuePatternIdentifiersStatics>
    {
        IValuePatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IValuePatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowPatternIdentifiers :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowPatternIdentifiers>
    {
        IWindowPatternIdentifiers(std::nullptr_t = nullptr) noexcept {}
        IWindowPatternIdentifiers(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowPatternIdentifiersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowPatternIdentifiersStatics>
    {
        IWindowPatternIdentifiersStatics(std::nullptr_t = nullptr) noexcept {}
        IWindowPatternIdentifiersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
