PORTED FUNCs LIST (209 of 209):

 void cv::ml::ANN_MLP::setTrainMethod(int method, double param1 = 0, double param2 = 0)
 int cv::ml::ANN_MLP::getTrainMethod()
 void cv::ml::ANN_MLP::setActivationFunction(int type, double param1 = 0, double param2 = 0)
 void cv::ml::ANN_MLP::setLayerSizes(Mat _layer_sizes)
 Mat cv::ml::ANN_MLP::getLayerSizes()
 TermCriteria cv::ml::ANN_MLP::getTermCriteria()
 void cv::ml::ANN_MLP::setTermCriteria(TermCriteria val)
 double cv::ml::ANN_MLP::getBackpropWeightScale()
 void cv::ml::ANN_MLP::setBackpropWeightScale(double val)
 double cv::ml::ANN_MLP::getBackpropMomentumScale()
 void cv::ml::ANN_MLP::setBackpropMomentumScale(double val)
 double cv::ml::ANN_MLP::getRpropDW0()
 void cv::ml::ANN_MLP::setRpropDW0(double val)
 double cv::ml::ANN_MLP::getRpropDWPlus()
 void cv::ml::ANN_MLP::setRpropDWPlus(double val)
 double cv::ml::ANN_MLP::getRpropDWMinus()
 void cv::ml::ANN_MLP::setRpropDWMinus(double val)
 double cv::ml::ANN_MLP::getRpropDWMin()
 void cv::ml::ANN_MLP::setRpropDWMin(double val)
 double cv::ml::ANN_MLP::getRpropDWMax()
 void cv::ml::ANN_MLP::setRpropDWMax(double val)
 double cv::ml::ANN_MLP::getAnnealInitialT()
 void cv::ml::ANN_MLP::setAnnealInitialT(double val)
 double cv::ml::ANN_MLP::getAnnealFinalT()
 void cv::ml::ANN_MLP::setAnnealFinalT(double val)
 double cv::ml::ANN_MLP::getAnnealCoolingRatio()
 void cv::ml::ANN_MLP::setAnnealCoolingRatio(double val)
 int cv::ml::ANN_MLP::getAnnealItePerStep()
 void cv::ml::ANN_MLP::setAnnealItePerStep(int val)
 Mat cv::ml::ANN_MLP::getWeights(int layerIdx)
static Ptr_ANN_MLP cv::ml::ANN_MLP::create()
static Ptr_ANN_MLP cv::ml::ANN_MLP::load(String filepath)
 int cv::ml::Boost::getBoostType()
 void cv::ml::Boost::setBoostType(int val)
 int cv::ml::Boost::getWeakCount()
 void cv::ml::Boost::setWeakCount(int val)
 double cv::ml::Boost::getWeightTrimRate()
 void cv::ml::Boost::setWeightTrimRate(double val)
static Ptr_Boost cv::ml::Boost::create()
static Ptr_Boost cv::ml::Boost::load(String filepath, String nodeName = String())
 int cv::ml::DTrees::getMaxCategories()
 void cv::ml::DTrees::setMaxCategories(int val)
 int cv::ml::DTrees::getMaxDepth()
 void cv::ml::DTrees::setMaxDepth(int val)
 int cv::ml::DTrees::getMinSampleCount()
 void cv::ml::DTrees::setMinSampleCount(int val)
 int cv::ml::DTrees::getCVFolds()
 void cv::ml::DTrees::setCVFolds(int val)
 bool cv::ml::DTrees::getUseSurrogates()
 void cv::ml::DTrees::setUseSurrogates(bool val)
 bool cv::ml::DTrees::getUse1SERule()
 void cv::ml::DTrees::setUse1SERule(bool val)
 bool cv::ml::DTrees::getTruncatePrunedTree()
 void cv::ml::DTrees::setTruncatePrunedTree(bool val)
 float cv::ml::DTrees::getRegressionAccuracy()
 void cv::ml::DTrees::setRegressionAccuracy(float val)
 Mat cv::ml::DTrees::getPriors()
 void cv::ml::DTrees::setPriors(Mat val)
static Ptr_DTrees cv::ml::DTrees::create()
static Ptr_DTrees cv::ml::DTrees::load(String filepath, String nodeName = String())
 int cv::ml::EM::getClustersNumber()
 void cv::ml::EM::setClustersNumber(int val)
 int cv::ml::EM::getCovarianceMatrixType()
 void cv::ml::EM::setCovarianceMatrixType(int val)
 TermCriteria cv::ml::EM::getTermCriteria()
 void cv::ml::EM::setTermCriteria(TermCriteria val)
 Mat cv::ml::EM::getWeights()
 Mat cv::ml::EM::getMeans()
 void cv::ml::EM::getCovs(vector_Mat& covs)
 float cv::ml::EM::predict(Mat samples, Mat& results = Mat(), int flags = 0)
 Vec2d cv::ml::EM::predict2(Mat sample, Mat& probs)
 bool cv::ml::EM::trainEM(Mat samples, Mat& logLikelihoods = Mat(), Mat& labels = Mat(), Mat& probs = Mat())
 bool cv::ml::EM::trainE(Mat samples, Mat means0, Mat covs0 = Mat(), Mat weights0 = Mat(), Mat& logLikelihoods = Mat(), Mat& labels = Mat(), Mat& probs = Mat())
 bool cv::ml::EM::trainM(Mat samples, Mat probs0, Mat& logLikelihoods = Mat(), Mat& labels = Mat(), Mat& probs = Mat())
static Ptr_EM cv::ml::EM::create()
static Ptr_EM cv::ml::EM::load(String filepath, String nodeName = String())
 int cv::ml::KNearest::getDefaultK()
 void cv::ml::KNearest::setDefaultK(int val)
 bool cv::ml::KNearest::getIsClassifier()
 void cv::ml::KNearest::setIsClassifier(bool val)
 int cv::ml::KNearest::getEmax()
 void cv::ml::KNearest::setEmax(int val)
 int cv::ml::KNearest::getAlgorithmType()
 void cv::ml::KNearest::setAlgorithmType(int val)
 float cv::ml::KNearest::findNearest(Mat samples, int k, Mat& results, Mat& neighborResponses = Mat(), Mat& dist = Mat())
static Ptr_KNearest cv::ml::KNearest::create()
static Ptr_KNearest cv::ml::KNearest::load(String filepath)
 double cv::ml::LogisticRegression::getLearningRate()
 void cv::ml::LogisticRegression::setLearningRate(double val)
 int cv::ml::LogisticRegression::getIterations()
 void cv::ml::LogisticRegression::setIterations(int val)
 int cv::ml::LogisticRegression::getRegularization()
 void cv::ml::LogisticRegression::setRegularization(int val)
 int cv::ml::LogisticRegression::getTrainMethod()
 void cv::ml::LogisticRegression::setTrainMethod(int val)
 int cv::ml::LogisticRegression::getMiniBatchSize()
 void cv::ml::LogisticRegression::setMiniBatchSize(int val)
 TermCriteria cv::ml::LogisticRegression::getTermCriteria()
 void cv::ml::LogisticRegression::setTermCriteria(TermCriteria val)
 float cv::ml::LogisticRegression::predict(Mat samples, Mat& results = Mat(), int flags = 0)
 Mat cv::ml::LogisticRegression::get_learnt_thetas()
static Ptr_LogisticRegression cv::ml::LogisticRegression::create()
static Ptr_LogisticRegression cv::ml::LogisticRegression::load(String filepath, String nodeName = String())
 float cv::ml::NormalBayesClassifier::predictProb(Mat inputs, Mat& outputs, Mat& outputProbs, int flags = 0)
static Ptr_NormalBayesClassifier cv::ml::NormalBayesClassifier::create()
static Ptr_NormalBayesClassifier cv::ml::NormalBayesClassifier::load(String filepath, String nodeName = String())
static Ptr_ParamGrid cv::ml::ParamGrid::create(double minVal = 0., double maxVal = 0., double logstep = 1.)
 bool cv::ml::RTrees::getCalculateVarImportance()
 void cv::ml::RTrees::setCalculateVarImportance(bool val)
 int cv::ml::RTrees::getActiveVarCount()
 void cv::ml::RTrees::setActiveVarCount(int val)
 TermCriteria cv::ml::RTrees::getTermCriteria()
 void cv::ml::RTrees::setTermCriteria(TermCriteria val)
 Mat cv::ml::RTrees::getVarImportance()
 void cv::ml::RTrees::getVotes(Mat samples, Mat& results, int flags)
 double cv::ml::RTrees::getOOBError()
static Ptr_RTrees cv::ml::RTrees::create()
static Ptr_RTrees cv::ml::RTrees::load(String filepath, String nodeName = String())
 int cv::ml::SVM::getType()
 void cv::ml::SVM::setType(int val)
 double cv::ml::SVM::getGamma()
 void cv::ml::SVM::setGamma(double val)
 double cv::ml::SVM::getCoef0()
 void cv::ml::SVM::setCoef0(double val)
 double cv::ml::SVM::getDegree()
 void cv::ml::SVM::setDegree(double val)
 double cv::ml::SVM::getC()
 void cv::ml::SVM::setC(double val)
 double cv::ml::SVM::getNu()
 void cv::ml::SVM::setNu(double val)
 double cv::ml::SVM::getP()
 void cv::ml::SVM::setP(double val)
 Mat cv::ml::SVM::getClassWeights()
 void cv::ml::SVM::setClassWeights(Mat val)
 TermCriteria cv::ml::SVM::getTermCriteria()
 void cv::ml::SVM::setTermCriteria(TermCriteria val)
 int cv::ml::SVM::getKernelType()
 void cv::ml::SVM::setKernel(int kernelType)
 bool cv::ml::SVM::trainAuto(Mat samples, int layout, Mat responses, int kFold = 10, Ptr_ParamGrid Cgrid = SVM::getDefaultGridPtr(SVM::C), Ptr_ParamGrid gammaGrid = SVM::getDefaultGridPtr(SVM::GAMMA), Ptr_ParamGrid pGrid = SVM::getDefaultGridPtr(SVM::P), Ptr_ParamGrid nuGrid = SVM::getDefaultGridPtr(SVM::NU), Ptr_ParamGrid coeffGrid = SVM::getDefaultGridPtr(SVM::COEF), Ptr_ParamGrid degreeGrid = SVM::getDefaultGridPtr(SVM::DEGREE), bool balanced = false)
 Mat cv::ml::SVM::getSupportVectors()
 Mat cv::ml::SVM::getUncompressedSupportVectors()
 double cv::ml::SVM::getDecisionFunction(int i, Mat& alpha, Mat& svidx)
static Ptr_ParamGrid cv::ml::SVM::getDefaultGridPtr(int param_id)
static Ptr_SVM cv::ml::SVM::create()
static Ptr_SVM cv::ml::SVM::load(String filepath)
 Mat cv::ml::SVMSGD::getWeights()
 float cv::ml::SVMSGD::getShift()
static Ptr_SVMSGD cv::ml::SVMSGD::create()
static Ptr_SVMSGD cv::ml::SVMSGD::load(String filepath, String nodeName = String())
 void cv::ml::SVMSGD::setOptimalParameters(int svmsgdType = SVMSGD::ASGD, int marginType = SVMSGD::SOFT_MARGIN)
 int cv::ml::SVMSGD::getSvmsgdType()
 void cv::ml::SVMSGD::setSvmsgdType(int svmsgdType)
 int cv::ml::SVMSGD::getMarginType()
 void cv::ml::SVMSGD::setMarginType(int marginType)
 float cv::ml::SVMSGD::getMarginRegularization()
 void cv::ml::SVMSGD::setMarginRegularization(float marginRegularization)
 float cv::ml::SVMSGD::getInitialStepSize()
 void cv::ml::SVMSGD::setInitialStepSize(float InitialStepSize)
 float cv::ml::SVMSGD::getStepDecreasingPower()
 void cv::ml::SVMSGD::setStepDecreasingPower(float stepDecreasingPower)
 TermCriteria cv::ml::SVMSGD::getTermCriteria()
 void cv::ml::SVMSGD::setTermCriteria(TermCriteria val)
 int cv::ml::StatModel::getVarCount()
 bool cv::ml::StatModel::empty()
 bool cv::ml::StatModel::isTrained()
 bool cv::ml::StatModel::isClassifier()
 bool cv::ml::StatModel::train(Ptr_TrainData trainData, int flags = 0)
 bool cv::ml::StatModel::train(Mat samples, int layout, Mat responses)
 float cv::ml::StatModel::calcError(Ptr_TrainData data, bool test, Mat& resp)
 float cv::ml::StatModel::predict(Mat samples, Mat& results = Mat(), int flags = 0)
 int cv::ml::TrainData::getLayout()
 int cv::ml::TrainData::getNTrainSamples()
 int cv::ml::TrainData::getNTestSamples()
 int cv::ml::TrainData::getNSamples()
 int cv::ml::TrainData::getNVars()
 int cv::ml::TrainData::getNAllVars()
 void cv::ml::TrainData::getSample(Mat varIdx, int sidx, float* buf)
 Mat cv::ml::TrainData::getSamples()
 Mat cv::ml::TrainData::getMissing()
 Mat cv::ml::TrainData::getTrainSamples(int layout = ROW_SAMPLE, bool compressSamples = true, bool compressVars = true)
 Mat cv::ml::TrainData::getTrainResponses()
 Mat cv::ml::TrainData::getTrainNormCatResponses()
 Mat cv::ml::TrainData::getTestResponses()
 Mat cv::ml::TrainData::getTestNormCatResponses()
 Mat cv::ml::TrainData::getResponses()
 Mat cv::ml::TrainData::getNormCatResponses()
 Mat cv::ml::TrainData::getSampleWeights()
 Mat cv::ml::TrainData::getTrainSampleWeights()
 Mat cv::ml::TrainData::getTestSampleWeights()
 Mat cv::ml::TrainData::getVarIdx()
 Mat cv::ml::TrainData::getVarType()
 Mat cv::ml::TrainData::getVarSymbolFlags()
 int cv::ml::TrainData::getResponseType()
 Mat cv::ml::TrainData::getTrainSampleIdx()
 Mat cv::ml::TrainData::getTestSampleIdx()
 void cv::ml::TrainData::getValues(int vi, Mat sidx, float* values)
 Mat cv::ml::TrainData::getDefaultSubstValues()
 int cv::ml::TrainData::getCatCount(int vi)
 Mat cv::ml::TrainData::getClassLabels()
 Mat cv::ml::TrainData::getCatOfs()
 Mat cv::ml::TrainData::getCatMap()
 void cv::ml::TrainData::setTrainTestSplit(int count, bool shuffle = true)
 void cv::ml::TrainData::setTrainTestSplitRatio(double ratio, bool shuffle = true)
 void cv::ml::TrainData::shuffleTrainTest()
 Mat cv::ml::TrainData::getTestSamples()
 void cv::ml::TrainData::getNames(vector_String names)
static Mat cv::ml::TrainData::getSubVector(Mat vec, Mat idx)
static Mat cv::ml::TrainData::getSubMatrix(Mat matrix, Mat idx, int layout)
static Ptr_TrainData cv::ml::TrainData::create(Mat samples, int layout, Mat responses, Mat varIdx = Mat(), Mat sampleIdx = Mat(), Mat sampleWeights = Mat(), Mat varType = Mat())

SKIPPED FUNCs LIST (0 of 209):


0 def args - 184 funcs
1 def args - 11 funcs
2 def args - 7 funcs
3 def args - 4 funcs
4 def args - 1 funcs
5 def args - 1 funcs
8 def args - 1 funcs