<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: GLFWgamepadstate Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">GLFWgamepadstate Struct Reference<div class="ingroups"><a class="el" href="group__input.html">Input reference</a></div></div></div>
</div><!--header-->
<div class="contents">

<p>Gamepad input state.  
 <a href="struct_g_l_f_wgamepadstate.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a27e9896b51c65df15fba2c7139bfdb9a" id="r_a27e9896b51c65df15fba2c7139bfdb9a"><td class="memItemLeft" align="right" valign="top">unsigned char&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wgamepadstate.html#a27e9896b51c65df15fba2c7139bfdb9a">buttons</a> [15]</td></tr>
<tr class="separator:a27e9896b51c65df15fba2c7139bfdb9a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b2c8939b1d31458de5359998375c189" id="r_a8b2c8939b1d31458de5359998375c189"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wgamepadstate.html#a8b2c8939b1d31458de5359998375c189">axes</a> [6]</td></tr>
<tr class="separator:a8b2c8939b1d31458de5359998375c189"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>This describes the input state of a gamepad.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#gamepad">Gamepad input</a> </dd>
<dd>
<a class="el" href="group__input.html#gadccddea8bce6113fa459de379ddaf051">glfwGetGamepadState</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="a27e9896b51c65df15fba2c7139bfdb9a" name="a27e9896b51c65df15fba2c7139bfdb9a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a27e9896b51c65df15fba2c7139bfdb9a">&#9670;&#160;</a></span>buttons</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">unsigned char GLFWgamepadstate::buttons[15]</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The states of each <a class="el" href="group__gamepad__buttons.html">gamepad button</a>, <code>GLFW_PRESS</code> or <code>GLFW_RELEASE</code>. </p>

</div>
</div>
<a id="a8b2c8939b1d31458de5359998375c189" name="a8b2c8939b1d31458de5359998375c189"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8b2c8939b1d31458de5359998375c189">&#9670;&#160;</a></span>axes</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float GLFWgamepadstate::axes[6]</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The states of each <a class="el" href="group__gamepad__axes.html">gamepad axis</a>, in the range -1.0 to 1.0 inclusive. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="glfw3_8h_source.html">glfw3.h</a></li>
</ul>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
