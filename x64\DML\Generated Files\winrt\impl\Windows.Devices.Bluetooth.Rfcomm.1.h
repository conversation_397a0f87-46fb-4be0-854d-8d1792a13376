// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Devices_Bluetooth_Rfcomm_1_H
#define WINRT_Windows_Devices_Bluetooth_Rfcomm_1_H
#include "winrt/impl/Windows.Devices.Bluetooth.Rfcomm.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices::Bluetooth::Rfcomm
{
    struct WINRT_IMPL_EMPTY_BASES IRfcommDeviceService :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRfcommDeviceService>
    {
        IRfcommDeviceService(std::nullptr_t = nullptr) noexcept {}
        IRfcommDeviceService(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRfcommDeviceService2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRfcommDeviceService2>,
        impl::require<winrt::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceService2, winrt::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceService>
    {
        IRfcommDeviceService2(std::nullptr_t = nullptr) noexcept {}
        IRfcommDeviceService2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRfcommDeviceService3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRfcommDeviceService3>,
        impl::require<winrt::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceService3, winrt::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceService, winrt::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceService2>
    {
        IRfcommDeviceService3(std::nullptr_t = nullptr) noexcept {}
        IRfcommDeviceService3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRfcommDeviceServiceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRfcommDeviceServiceStatics>
    {
        IRfcommDeviceServiceStatics(std::nullptr_t = nullptr) noexcept {}
        IRfcommDeviceServiceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRfcommDeviceServiceStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRfcommDeviceServiceStatics2>,
        impl::require<winrt::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceServiceStatics2, winrt::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceServiceStatics>
    {
        IRfcommDeviceServiceStatics2(std::nullptr_t = nullptr) noexcept {}
        IRfcommDeviceServiceStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRfcommDeviceServicesResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRfcommDeviceServicesResult>
    {
        IRfcommDeviceServicesResult(std::nullptr_t = nullptr) noexcept {}
        IRfcommDeviceServicesResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRfcommServiceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRfcommServiceId>
    {
        IRfcommServiceId(std::nullptr_t = nullptr) noexcept {}
        IRfcommServiceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRfcommServiceIdStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRfcommServiceIdStatics>
    {
        IRfcommServiceIdStatics(std::nullptr_t = nullptr) noexcept {}
        IRfcommServiceIdStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRfcommServiceProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRfcommServiceProvider>
    {
        IRfcommServiceProvider(std::nullptr_t = nullptr) noexcept {}
        IRfcommServiceProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRfcommServiceProvider2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRfcommServiceProvider2>,
        impl::require<winrt::Windows::Devices::Bluetooth::Rfcomm::IRfcommServiceProvider2, winrt::Windows::Devices::Bluetooth::Rfcomm::IRfcommServiceProvider>
    {
        IRfcommServiceProvider2(std::nullptr_t = nullptr) noexcept {}
        IRfcommServiceProvider2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
        using impl::consume_t<IRfcommServiceProvider2, IRfcommServiceProvider2>::StartAdvertising;
        using impl::consume_t<IRfcommServiceProvider2, winrt::Windows::Devices::Bluetooth::Rfcomm::IRfcommServiceProvider>::StartAdvertising;
    };
    struct WINRT_IMPL_EMPTY_BASES IRfcommServiceProviderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRfcommServiceProviderStatics>
    {
        IRfcommServiceProviderStatics(std::nullptr_t = nullptr) noexcept {}
        IRfcommServiceProviderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
