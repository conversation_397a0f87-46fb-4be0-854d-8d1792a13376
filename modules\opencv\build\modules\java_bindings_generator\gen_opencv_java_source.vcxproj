﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{95B70A57-2775-38DA-BE3B-EB8AD440A880}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>gen_opencv_java_source</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\AI\opencv\cudabuild\CMakeFiles\b395ee7c0999e42287482932580994cb\gen_opencv_java_source.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generate files for Java bindings</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd D:\AI\opencv\cudabuild\modules\java_bindings_generator
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe D:/AI/opencv/opencv-4.10.0/modules/java/generator/../generator/gen_java.py -p D:/AI/opencv/opencv-4.10.0/modules/java/generator/../../python/src2/gen2.py -c D:/AI/opencv/cudabuild/modules/java_bindings_generator/gen_java.json
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch D:/AI/opencv/cudabuild/CMakeFiles/dephelper/gen_opencv_java_source
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\opencv-4.10.0\modules\java\generator\gen_java.py;D:\AI\opencv\opencv-4.10.0\modules\python\src2\gen2.py;D:\AI\opencv\opencv-4.10.0\modules\python\src2\hdr_parser.py;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android-21\java\org\opencv\android\Camera2Renderer.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android-21\java\org\opencv\android\CameraGLRendererBase.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android-21\java\org\opencv\android\CameraGLSurfaceView.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android-21\java\org\opencv\android\CameraRenderer.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android-21\java\org\opencv\android\JavaCamera2View.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android-24\java\org\opencv\android\NativeCameraView.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android\java\org\opencv\android\CameraActivity.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android\java\org\opencv\android\CameraBridgeViewBase.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android\java\org\opencv\android\FpsMeter.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android\java\org\opencv\android\JavaCameraView.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android\java\org\opencv\android\OpenCVLoader.java.in;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android\java\org\opencv\android\StaticHelper.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android\java\org\opencv\android\Utils.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\Mat.cpp;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\common.h;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\converters.cpp;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\converters.h;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\jni_part.cpp;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\listconverters.cpp;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\listconverters.hpp;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\opencv_java.cpp;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\opencv_java.hpp;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\utils.cpp;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\java\org\opencv\osgi\OpenCVInterface.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\java\org\opencv\osgi\OpenCVNativeLoader.java.in;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\java\org\opencv\utils\Converters.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\templates\cpp_module.template;D:\AI\opencv\opencv-4.10.0\modules\java\generator\templates\java_class.prolog;D:\AI\opencv\opencv-4.10.0\modules\java\generator\templates\java_class_inherited.prolog;D:\AI\opencv\opencv-4.10.0\modules\java\generator\templates\java_module.prolog;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\backend\parallel_for.openmp.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\backend\parallel_for.tbb.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\parallel_backend.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\ocl_defs.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\opencl_info.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\opencl_svm.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_clblas.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_clfft.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_core_wrappers.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_gl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_gl_wrappers.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_clblas.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_clfft.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_core_wrappers.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_gl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_gl_wrappers.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_svm_20.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_svm_definitions.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_svm_hsa_extension.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\block.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\border_interpolate.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\color.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\common.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\datamov_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\dynamic_smem.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\emulation.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\filters.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\funcattrib.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\functional.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\limits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\reduce.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\saturate_cast.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\scan.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\simd_functions.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\transform.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\type_traits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\utility.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\vec_distance.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\vec_math.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\vec_traits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\warp.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\warp_reduce.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\warp_shuffle.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\color_detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\reduce.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\reduce_key_val.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\transform_detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\type_traits_detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\vec_distance_detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\affine.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\async.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\base.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\bindings_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\bufferpool.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\check.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\core_c.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda_stream_accessor.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda_types.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cv_cpu_dispatch.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cv_cpu_helper.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvdef.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd_wrapper.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\directx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\dualquaternion.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\dualquaternion.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\eigen.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\fast_math.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\hal.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\interface.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_avx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_avx512.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_cpp.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_forward.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_lasx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_lsx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_msa.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_neon.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv071.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_010_compat_non-policy.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_010_compat_overloaded-non-policy.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_011_compat.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_compat_overloaded.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_scalable.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_sse.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_sse_em.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_vsx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_wasm.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\msa_macros.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\simd_utils.impl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\mat.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\mat.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\matx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\matx.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\neon_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\ocl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\ocl_genbase.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opengl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\operations.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\optim.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\ovx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\persistence.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\private.cuda.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\quaternion.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\quaternion.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\saturate.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\simd_intrinsics.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\softfloat.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\sse_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\traits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\types.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\types_c.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utility.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\allocator_stats.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\allocator_stats.impl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\buffer_area.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\configuration.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\filesystem.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\filesystem.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\fp_control.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\fp_control_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\instrumentation.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\lock.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logger.defines.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logger.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logtag.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\plugin_loader.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\tls.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\trace.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\trace.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\va_intel.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\version.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\vsx_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\async_promise.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\dispatch_helper.impl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\exception_ptr.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\filelist;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\cpp\core_manual.cpp;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\cpp\core_manual.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Core.jcode.in;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+CvException.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+CvType.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+DMatch.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+KeyPoint.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Mat.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatAt.kt;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatMatMul.kt;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfByte.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfDMatch.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfDouble.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfFloat.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfFloat4.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfFloat6.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfInt.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfInt4.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfKeyPoint.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfPoint.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfPoint2f.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfPoint3.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfPoint3f.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfRect.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfRect2d.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfRotatedRect.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Point.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Point3.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Range.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Rect.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Rect2d.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+RotatedRect.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Scalar.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Size.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+TermCriteria.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\CoreTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\CvTypeTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\DMatchTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\KeyPointTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\MatOfByteTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\MatTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\Point3Test.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\PointTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\RangeTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\RectTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\RotatedRectTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\ScalarTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\SizeTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\TermCriteriaTest.java;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\bindings.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\hal\hal.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\hal\interface.h;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\imgproc.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\imgproc_c.h;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\segmentation.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\types_c.h;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\detail\gcgraph.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\detail\legacy.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\java\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\java\src\java\imgproc+Moments.java;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\java\test\ImgprocTest.java;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\java\test\MomentsTest.java;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\java\test\Subdiv2DTest.java;D:\AI\opencv\opencv-4.10.0\modules\ml\include\opencv2\ml.hpp;D:\AI\opencv\opencv-4.10.0\modules\ml\include\opencv2\ml\ml.hpp;D:\AI\opencv\opencv-4.10.0\modules\ml\include\opencv2\ml\ml.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\ml\misc\java\test\MLTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include\opencv2\phase_unwrapping.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include\opencv2\phase_unwrapping\histogramphaseunwrapping.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include\opencv2\phase_unwrapping\phase_unwrapping.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\plot\include\opencv2\plot.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\all_layers.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dict.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dnn.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dnn.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer.details.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer_reg.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\shape_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\utils\debug_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\utils\inference_engine.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\version.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\java\filelist_common;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\java\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\java\src\cpp\dnn_converters.cpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\java\src\cpp\dnn_converters.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\java\test\DnnListRegressionTest.java;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\java\test\DnnTensorFlowTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\dnn_superres\include\opencv2\dnn_superres.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\dnn_superres\misc\java\test\DnnSuperresTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\include\opencv2\features2d.hpp;D:\AI\opencv\opencv-4.10.0\modules\features2d\include\opencv2\features2d\features2d.hpp;D:\AI\opencv\opencv-4.10.0\modules\features2d\include\opencv2\features2d\hal\interface.h;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\filelist;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\filelist_common;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\src\cpp\features2d_converters.cpp;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\src\cpp\features2d_converters.hpp;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\AGASTFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\AKAZEDescriptorExtractorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\BOWImgDescriptorExtractorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\BRIEFDescriptorExtractorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\BRISKDescriptorExtractorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\BruteForceDescriptorMatcherTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\BruteForceHammingDescriptorMatcherTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\BruteForceHammingLUTDescriptorMatcherTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\BruteForceL1DescriptorMatcherTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\BruteForceSL2DescriptorMatcherTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\DENSEFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\FASTFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\Features2dTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\FlannBasedDescriptorMatcherTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\GFTTFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\HARRISFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\KAZEDescriptorExtractorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\MSERFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\ORBDescriptorExtractorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\ORBFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\SIFTDescriptorExtractorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\SIFTFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\SIMPLEBLOBFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\imgcodecs.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\imgcodecs_c.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\ios.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\legacy\constants_c.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\macosx.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\java\test\ImgcodecsTest.java;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo\cuda.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo\legacy\constants_c.h;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo\photo.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\misc\java\test\PhotoTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text\erfilter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text\ocr.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text\swt_text_detection.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text\textDetector.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\cap_ios.h;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\container_avi.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\legacy\constants_c.h;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\registry.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\utils.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\videoio.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\videoio_c.h;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\precomp.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_dshow.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_obsensor_capture.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_obsensor\obsensor_stream_channel_interface.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_obsensor\obsensor_uvc_stream_channel.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_obsensor\obsensor_stream_channel_msmf.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\misc\java\test\VideoCaptureTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\bm3d_image_denoising.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\dct_image_denoising.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\inpainting.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\oilpainting.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\tonemap.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\white_balance.hpp;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include\opencv2\calib3d.hpp;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include\opencv2\calib3d\calib3d.hpp;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include\opencv2\calib3d\calib3d_c.h;D:\AI\opencv\opencv-4.10.0\modules\calib3d\misc\java\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\calib3d\misc\java\test\Calib3dTest.java;D:\AI\opencv\opencv-4.10.0\modules\calib3d\misc\java\test\StereoBMTest.java;D:\AI\opencv\opencv-4.10.0\modules\calib3d\misc\java\test\StereoSGBMTest.java;D:\AI\opencv\opencv-4.10.0\modules\highgui\include\opencv2\highgui.hpp;D:\AI\opencv\opencv-4.10.0\modules\highgui\include\opencv2\highgui\highgui.hpp;D:\AI\opencv\opencv-4.10.0\modules\highgui\include\opencv2\highgui\highgui_c.h;D:\AI\opencv\opencv-4.10.0\modules\highgui\misc\java\filelist;D:\AI\opencv\opencv-4.10.0\modules\highgui\misc\java\src\java\highgui+HighGui.java;D:\AI\opencv\opencv-4.10.0\modules\highgui\misc\java\src\java\highgui+ImageWindow.java;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_board.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_dictionary.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\barcode.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\charuco_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\detection_based_tracker.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\face.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\graphical_code_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\objdetect.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\misc\java\test\ArucoTest.java;D:\AI\opencv\opencv-4.10.0\modules\objdetect\misc\java\test\BarcodeDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\objdetect\misc\java\test\CascadeClassifierTest.java;D:\AI\opencv\opencv-4.10.0\modules\objdetect\misc\java\test\HOGDescriptorTest.java;D:\AI\opencv\opencv-4.10.0\modules\objdetect\misc\java\test\ObjdetectTest.java;D:\AI\opencv\opencv-4.10.0\modules\objdetect\misc\java\test\QRCodeDetectorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include\opencv2\structured_light.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include\opencv2\structured_light\graycodepattern.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include\opencv2\structured_light\sinusoidalpattern.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include\opencv2\structured_light\structured_light.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\background_segm.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\legacy\constants_c.h;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\tracking.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\video.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\detail\tracking.detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\misc\java\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\video\misc\java\test\BackgroundSubtractorMOGTest.java;D:\AI\opencv\opencv-4.10.0\modules\video\misc\java\test\KalmanFilterTest.java;D:\AI\opencv\opencv-4.10.0\modules\video\misc\java\test\TrackerCreateTest.java;D:\AI\opencv\opencv-4.10.0\modules\video\misc\java\test\VideoTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\wechat_qrcode\include\opencv2\wechat_qrcode.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include\opencv2\xfeatures2d.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include\opencv2\xfeatures2d\cuda.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include\opencv2\xfeatures2d\nonfree.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\DAISYDescriptorExtractorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\FREAKDescriptorExtractorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\HARRISFeatureDetectorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\LATCHDescriptorExtractorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\LUCIDDescriptorExtractorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\MSDFeatureDetectorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\STARFeatureDetectorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\SURFDescriptorExtractorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\SURFFeatureDetectorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\brightedges.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\color_match.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\deriche_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\disparity_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\edge_drawing.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\edge_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\edgeboxes.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\edgepreserving_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\estimated_covariance.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\fast_hough_transform.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\fast_line_detector.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\find_ellipses.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\fourier_descriptors.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\lsc.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\paillou_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\peilin.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\radon_transform.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\ridgefilter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\run_length_morphology.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\scansegment.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\seeds.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\segmentation.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\slic.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\sparse_match_interpolator.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\structured_edge_detection.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\weighted_median_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco\aruco_calib.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco\charuco.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bgsegm\include\opencv2\bgsegm.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired\bioinspired.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired\retina.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired\retinafasttonemapping.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired\transientareassegmentationmodule.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\bif.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\face_alignment.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facemark.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facemarkAAM.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facemarkLBF.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facemark_train.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facerec.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\mace.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\predict_collector.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\misc\java\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\feature.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\kalman_filters.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\onlineBoosting.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tldDataset.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tracking.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tracking_by_matching.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tracking_internals.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tracking_legacy.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\twist.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\misc\java\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\misc\java\test\TrackerCreateLegacyTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\misc\java\test\TrackerCreateTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\average_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\block_mean_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\color_moment_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\img_hash_base.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\marr_hildreth_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\phash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\radial_variance_hash.hpp;D:\AI\opencv\cudabuild\configured\modules\java\generator\android\java\org\opencv\android\OpenCVLoader.java;D:\AI\opencv\cudabuild\configured\modules\java\generator\src\java\org\opencv\osgi\OpenCVNativeLoader.java;D:\AI\opencv\cudabuild\configured\modules\core\misc\java\src\java\core+Core.jcode;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\cudabuild\CMakeFiles\dephelper\gen_opencv_java_source</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generate files for Java bindings</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd D:\AI\opencv\cudabuild\modules\java_bindings_generator
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe D:/AI/opencv/opencv-4.10.0/modules/java/generator/../generator/gen_java.py -p D:/AI/opencv/opencv-4.10.0/modules/java/generator/../../python/src2/gen2.py -c D:/AI/opencv/cudabuild/modules/java_bindings_generator/gen_java.json
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch D:/AI/opencv/cudabuild/CMakeFiles/dephelper/gen_opencv_java_source
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\opencv-4.10.0\modules\java\generator\gen_java.py;D:\AI\opencv\opencv-4.10.0\modules\python\src2\gen2.py;D:\AI\opencv\opencv-4.10.0\modules\python\src2\hdr_parser.py;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android-21\java\org\opencv\android\Camera2Renderer.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android-21\java\org\opencv\android\CameraGLRendererBase.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android-21\java\org\opencv\android\CameraGLSurfaceView.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android-21\java\org\opencv\android\CameraRenderer.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android-21\java\org\opencv\android\JavaCamera2View.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android-24\java\org\opencv\android\NativeCameraView.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android\java\org\opencv\android\CameraActivity.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android\java\org\opencv\android\CameraBridgeViewBase.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android\java\org\opencv\android\FpsMeter.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android\java\org\opencv\android\JavaCameraView.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android\java\org\opencv\android\OpenCVLoader.java.in;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android\java\org\opencv\android\StaticHelper.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\android\java\org\opencv\android\Utils.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\Mat.cpp;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\common.h;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\converters.cpp;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\converters.h;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\jni_part.cpp;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\listconverters.cpp;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\listconverters.hpp;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\opencv_java.cpp;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\opencv_java.hpp;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\cpp\utils.cpp;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\java\org\opencv\osgi\OpenCVInterface.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\java\org\opencv\osgi\OpenCVNativeLoader.java.in;D:\AI\opencv\opencv-4.10.0\modules\java\generator\src\java\org\opencv\utils\Converters.java;D:\AI\opencv\opencv-4.10.0\modules\java\generator\templates\cpp_module.template;D:\AI\opencv\opencv-4.10.0\modules\java\generator\templates\java_class.prolog;D:\AI\opencv\opencv-4.10.0\modules\java\generator\templates\java_class_inherited.prolog;D:\AI\opencv\opencv-4.10.0\modules\java\generator\templates\java_module.prolog;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\backend\parallel_for.openmp.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\backend\parallel_for.tbb.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\parallel_backend.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\ocl_defs.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\opencl_info.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\opencl_svm.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_clblas.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_clfft.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_core_wrappers.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_gl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_gl_wrappers.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_clblas.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_clfft.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_core_wrappers.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_gl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_gl_wrappers.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_svm_20.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_svm_definitions.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_svm_hsa_extension.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\block.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\border_interpolate.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\color.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\common.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\datamov_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\dynamic_smem.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\emulation.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\filters.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\funcattrib.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\functional.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\limits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\reduce.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\saturate_cast.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\scan.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\simd_functions.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\transform.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\type_traits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\utility.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\vec_distance.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\vec_math.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\vec_traits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\warp.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\warp_reduce.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\warp_shuffle.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\color_detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\reduce.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\reduce_key_val.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\transform_detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\type_traits_detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\vec_distance_detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\affine.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\async.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\base.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\bindings_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\bufferpool.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\check.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\core_c.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda_stream_accessor.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda_types.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cv_cpu_dispatch.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cv_cpu_helper.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvdef.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd_wrapper.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\directx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\dualquaternion.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\dualquaternion.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\eigen.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\fast_math.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\hal.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\interface.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_avx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_avx512.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_cpp.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_forward.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_lasx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_lsx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_msa.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_neon.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv071.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_010_compat_non-policy.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_010_compat_overloaded-non-policy.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_011_compat.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_compat_overloaded.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_scalable.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_sse.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_sse_em.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_vsx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_wasm.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\msa_macros.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\simd_utils.impl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\mat.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\mat.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\matx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\matx.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\neon_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\ocl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\ocl_genbase.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opengl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\operations.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\optim.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\ovx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\persistence.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\private.cuda.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\quaternion.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\quaternion.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\saturate.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\simd_intrinsics.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\softfloat.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\sse_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\traits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\types.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\types_c.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utility.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\allocator_stats.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\allocator_stats.impl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\buffer_area.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\configuration.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\filesystem.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\filesystem.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\fp_control.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\fp_control_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\instrumentation.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\lock.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logger.defines.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logger.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logtag.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\plugin_loader.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\tls.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\trace.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\trace.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\va_intel.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\version.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\vsx_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\async_promise.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\dispatch_helper.impl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\exception_ptr.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\filelist;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\cpp\core_manual.cpp;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\cpp\core_manual.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Core.jcode.in;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+CvException.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+CvType.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+DMatch.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+KeyPoint.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Mat.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatAt.kt;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatMatMul.kt;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfByte.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfDMatch.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfDouble.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfFloat.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfFloat4.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfFloat6.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfInt.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfInt4.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfKeyPoint.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfPoint.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfPoint2f.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfPoint3.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfPoint3f.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfRect.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfRect2d.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+MatOfRotatedRect.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Point.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Point3.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Range.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Rect.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Rect2d.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+RotatedRect.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Scalar.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+Size.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\src\java\core+TermCriteria.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\CoreTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\CvTypeTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\DMatchTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\KeyPointTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\MatOfByteTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\MatTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\Point3Test.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\PointTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\RangeTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\RectTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\RotatedRectTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\ScalarTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\SizeTest.java;D:\AI\opencv\opencv-4.10.0\modules\core\misc\java\test\TermCriteriaTest.java;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\bindings.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\hal\hal.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\hal\interface.h;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\imgproc.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\imgproc_c.h;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\segmentation.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\types_c.h;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\detail\gcgraph.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\detail\legacy.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\java\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\java\src\java\imgproc+Moments.java;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\java\test\ImgprocTest.java;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\java\test\MomentsTest.java;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\java\test\Subdiv2DTest.java;D:\AI\opencv\opencv-4.10.0\modules\ml\include\opencv2\ml.hpp;D:\AI\opencv\opencv-4.10.0\modules\ml\include\opencv2\ml\ml.hpp;D:\AI\opencv\opencv-4.10.0\modules\ml\include\opencv2\ml\ml.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\ml\misc\java\test\MLTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include\opencv2\phase_unwrapping.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include\opencv2\phase_unwrapping\histogramphaseunwrapping.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include\opencv2\phase_unwrapping\phase_unwrapping.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\plot\include\opencv2\plot.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\all_layers.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dict.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dnn.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dnn.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer.details.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer_reg.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\shape_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\utils\debug_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\utils\inference_engine.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\version.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\java\filelist_common;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\java\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\java\src\cpp\dnn_converters.cpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\java\src\cpp\dnn_converters.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\java\test\DnnListRegressionTest.java;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\java\test\DnnTensorFlowTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\dnn_superres\include\opencv2\dnn_superres.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\dnn_superres\misc\java\test\DnnSuperresTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\include\opencv2\features2d.hpp;D:\AI\opencv\opencv-4.10.0\modules\features2d\include\opencv2\features2d\features2d.hpp;D:\AI\opencv\opencv-4.10.0\modules\features2d\include\opencv2\features2d\hal\interface.h;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\filelist;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\filelist_common;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\src\cpp\features2d_converters.cpp;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\src\cpp\features2d_converters.hpp;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\AGASTFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\AKAZEDescriptorExtractorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\BOWImgDescriptorExtractorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\BRIEFDescriptorExtractorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\BRISKDescriptorExtractorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\BruteForceDescriptorMatcherTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\BruteForceHammingDescriptorMatcherTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\BruteForceHammingLUTDescriptorMatcherTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\BruteForceL1DescriptorMatcherTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\BruteForceSL2DescriptorMatcherTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\DENSEFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\FASTFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\Features2dTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\FlannBasedDescriptorMatcherTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\GFTTFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\HARRISFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\KAZEDescriptorExtractorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\MSERFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\ORBDescriptorExtractorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\ORBFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\SIFTDescriptorExtractorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\SIFTFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\java\test\SIMPLEBLOBFeatureDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\imgcodecs.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\imgcodecs_c.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\ios.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\legacy\constants_c.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\macosx.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\java\test\ImgcodecsTest.java;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo\cuda.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo\legacy\constants_c.h;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo\photo.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\misc\java\test\PhotoTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text\erfilter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text\ocr.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text\swt_text_detection.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text\textDetector.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\cap_ios.h;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\container_avi.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\legacy\constants_c.h;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\registry.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\utils.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\videoio.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\videoio_c.h;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\precomp.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_dshow.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_obsensor_capture.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_obsensor\obsensor_stream_channel_interface.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_obsensor\obsensor_uvc_stream_channel.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_obsensor\obsensor_stream_channel_msmf.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\misc\java\test\VideoCaptureTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\bm3d_image_denoising.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\dct_image_denoising.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\inpainting.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\oilpainting.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\tonemap.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\white_balance.hpp;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include\opencv2\calib3d.hpp;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include\opencv2\calib3d\calib3d.hpp;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include\opencv2\calib3d\calib3d_c.h;D:\AI\opencv\opencv-4.10.0\modules\calib3d\misc\java\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\calib3d\misc\java\test\Calib3dTest.java;D:\AI\opencv\opencv-4.10.0\modules\calib3d\misc\java\test\StereoBMTest.java;D:\AI\opencv\opencv-4.10.0\modules\calib3d\misc\java\test\StereoSGBMTest.java;D:\AI\opencv\opencv-4.10.0\modules\highgui\include\opencv2\highgui.hpp;D:\AI\opencv\opencv-4.10.0\modules\highgui\include\opencv2\highgui\highgui.hpp;D:\AI\opencv\opencv-4.10.0\modules\highgui\include\opencv2\highgui\highgui_c.h;D:\AI\opencv\opencv-4.10.0\modules\highgui\misc\java\filelist;D:\AI\opencv\opencv-4.10.0\modules\highgui\misc\java\src\java\highgui+HighGui.java;D:\AI\opencv\opencv-4.10.0\modules\highgui\misc\java\src\java\highgui+ImageWindow.java;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_board.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_dictionary.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\barcode.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\charuco_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\detection_based_tracker.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\face.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\graphical_code_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\objdetect.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\misc\java\test\ArucoTest.java;D:\AI\opencv\opencv-4.10.0\modules\objdetect\misc\java\test\BarcodeDetectorTest.java;D:\AI\opencv\opencv-4.10.0\modules\objdetect\misc\java\test\CascadeClassifierTest.java;D:\AI\opencv\opencv-4.10.0\modules\objdetect\misc\java\test\HOGDescriptorTest.java;D:\AI\opencv\opencv-4.10.0\modules\objdetect\misc\java\test\ObjdetectTest.java;D:\AI\opencv\opencv-4.10.0\modules\objdetect\misc\java\test\QRCodeDetectorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include\opencv2\structured_light.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include\opencv2\structured_light\graycodepattern.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include\opencv2\structured_light\sinusoidalpattern.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include\opencv2\structured_light\structured_light.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\background_segm.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\legacy\constants_c.h;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\tracking.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\video.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\detail\tracking.detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\misc\java\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\video\misc\java\test\BackgroundSubtractorMOGTest.java;D:\AI\opencv\opencv-4.10.0\modules\video\misc\java\test\KalmanFilterTest.java;D:\AI\opencv\opencv-4.10.0\modules\video\misc\java\test\TrackerCreateTest.java;D:\AI\opencv\opencv-4.10.0\modules\video\misc\java\test\VideoTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\wechat_qrcode\include\opencv2\wechat_qrcode.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include\opencv2\xfeatures2d.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include\opencv2\xfeatures2d\cuda.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include\opencv2\xfeatures2d\nonfree.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\DAISYDescriptorExtractorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\FREAKDescriptorExtractorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\HARRISFeatureDetectorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\LATCHDescriptorExtractorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\LUCIDDescriptorExtractorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\MSDFeatureDetectorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\STARFeatureDetectorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\SURFDescriptorExtractorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\java\test\SURFFeatureDetectorTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\brightedges.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\color_match.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\deriche_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\disparity_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\edge_drawing.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\edge_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\edgeboxes.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\edgepreserving_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\estimated_covariance.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\fast_hough_transform.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\fast_line_detector.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\find_ellipses.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\fourier_descriptors.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\lsc.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\paillou_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\peilin.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\radon_transform.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\ridgefilter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\run_length_morphology.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\scansegment.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\seeds.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\segmentation.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\slic.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\sparse_match_interpolator.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\structured_edge_detection.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\weighted_median_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco\aruco_calib.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco\charuco.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bgsegm\include\opencv2\bgsegm.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired\bioinspired.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired\retina.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired\retinafasttonemapping.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired\transientareassegmentationmodule.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\bif.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\face_alignment.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facemark.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facemarkAAM.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facemarkLBF.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facemark_train.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facerec.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\mace.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\predict_collector.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\misc\java\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\feature.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\kalman_filters.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\onlineBoosting.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tldDataset.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tracking.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tracking_by_matching.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tracking_internals.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tracking_legacy.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\twist.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\misc\java\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\misc\java\test\TrackerCreateLegacyTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\misc\java\test\TrackerCreateTest.java;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\average_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\block_mean_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\color_moment_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\img_hash_base.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\marr_hildreth_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\phash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\radial_variance_hash.hpp;D:\AI\opencv\cudabuild\configured\modules\java\generator\android\java\org\opencv\android\OpenCVLoader.java;D:\AI\opencv\cudabuild\configured\modules\java\generator\src\java\org\opencv\osgi\OpenCVNativeLoader.java;D:\AI\opencv\cudabuild\configured\modules\core\misc\java\src\java\core+Core.jcode;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\cudabuild\CMakeFiles\dephelper\gen_opencv_java_source</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\AI\opencv\cudabuild\CMakeFiles\4e97d2f893d69bb3ca62e2d9b0673e80\gen_opencv_java_source.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\cudabuild\CMakeFiles\dephelper\gen_opencv_java_source;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\cudabuild\modules\java_bindings_generator\CMakeFiles\gen_opencv_java_source</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\cudabuild\CMakeFiles\dephelper\gen_opencv_java_source;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\cudabuild\modules\java_bindings_generator\CMakeFiles\gen_opencv_java_source</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\AI\opencv\opencv-4.10.0\modules\java\generator\gen_java.py">
    </None>
    <None Include="D:\AI\opencv\cudabuild\modules\java_bindings_generator\gen_java.json">
    </None>
    <None Include="D:\AI\opencv\cudabuild\modules\java_bindings_generator\CMakeFiles\gen_opencv_java_source">
    </None>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>