PORTED FUNCs LIST (207 of 207):

 Ptr_LineSegmentDetector cv::createLineSegmentDetector(int refine = LSD_REFINE_STD, double scale = 0.8, double sigma_scale = 0.6, double quant = 2.0, double ang_th = 22.5, double log_eps = 0, double density_th = 0.7, int n_bins = 1024)
 Mat cv::getGaussianKernel(int ksize, double sigma, int ktype = CV_64F)
 void cv::getDerivKernels(Mat& kx, Mat& ky, int dx, int dy, int ksize, bool normalize = false, int ktype = CV_32F)
 Mat cv::getGaborKernel(Size ksize, double sigma, double theta, double lambd, double gamma, double psi = CV_PI*0.5, int ktype = CV_64F)
 Mat cv::getStructuringElement(MorphShapes shape, Size ksize, Point anchor = Point(-1,-1))
 void cv::medianBlur(Mat src, Mat& dst, int ksize)
 void cv::GaussianBlur(Mat src, Mat& dst, Size ksize, double sigmaX, double sigmaY = 0, BorderTypes borderType = BORDER_DEFAULT)
 void cv::bilateralFilter(Mat src, Mat& dst, int d, double sigmaColor, double sigmaSpace, BorderTypes borderType = BORDER_DEFAULT)
 void cv::boxFilter(Mat src, Mat& dst, int ddepth, Size ksize, Point anchor = Point(-1,-1), bool normalize = true, BorderTypes borderType = BORDER_DEFAULT)
 void cv::sqrBoxFilter(Mat src, Mat& dst, int ddepth, Size ksize, Point anchor = Point(-1, -1), bool normalize = true, BorderTypes borderType = BORDER_DEFAULT)
 void cv::blur(Mat src, Mat& dst, Size ksize, Point anchor = Point(-1,-1), BorderTypes borderType = BORDER_DEFAULT)
 void cv::stackBlur(Mat src, Mat& dst, Size ksize)
 void cv::filter2D(Mat src, Mat& dst, int ddepth, Mat kernel, Point anchor = Point(-1,-1), double delta = 0, BorderTypes borderType = BORDER_DEFAULT)
 void cv::sepFilter2D(Mat src, Mat& dst, int ddepth, Mat kernelX, Mat kernelY, Point anchor = Point(-1,-1), double delta = 0, BorderTypes borderType = BORDER_DEFAULT)
 void cv::Sobel(Mat src, Mat& dst, int ddepth, int dx, int dy, int ksize = 3, double scale = 1, double delta = 0, BorderTypes borderType = BORDER_DEFAULT)
 void cv::spatialGradient(Mat src, Mat& dx, Mat& dy, int ksize = 3, BorderTypes borderType = BORDER_DEFAULT)
 void cv::Scharr(Mat src, Mat& dst, int ddepth, int dx, int dy, double scale = 1, double delta = 0, BorderTypes borderType = BORDER_DEFAULT)
 void cv::Laplacian(Mat src, Mat& dst, int ddepth, int ksize = 1, double scale = 1, double delta = 0, BorderTypes borderType = BORDER_DEFAULT)
 void cv::Canny(Mat image, Mat& edges, double threshold1, double threshold2, int apertureSize = 3, bool L2gradient = false)
 void cv::Canny(Mat dx, Mat dy, Mat& edges, double threshold1, double threshold2, bool L2gradient = false)
 void cv::cornerMinEigenVal(Mat src, Mat& dst, int blockSize, int ksize = 3, BorderTypes borderType = BORDER_DEFAULT)
 void cv::cornerHarris(Mat src, Mat& dst, int blockSize, int ksize, double k, BorderTypes borderType = BORDER_DEFAULT)
 void cv::cornerEigenValsAndVecs(Mat src, Mat& dst, int blockSize, int ksize, BorderTypes borderType = BORDER_DEFAULT)
 void cv::preCornerDetect(Mat src, Mat& dst, int ksize, BorderTypes borderType = BORDER_DEFAULT)
 void cv::cornerSubPix(Mat image, Mat& corners, Size winSize, Size zeroZone, TermCriteria criteria)
 void cv::goodFeaturesToTrack(Mat image, vector_Point& corners, int maxCorners, double qualityLevel, double minDistance, Mat mask = Mat(), int blockSize = 3, bool useHarrisDetector = false, double k = 0.04)
 void cv::goodFeaturesToTrack(Mat image, vector_Point& corners, int maxCorners, double qualityLevel, double minDistance, Mat mask, int blockSize, int gradientSize, bool useHarrisDetector = false, double k = 0.04)
 void cv::goodFeaturesToTrack(Mat image, Mat& corners, int maxCorners, double qualityLevel, double minDistance, Mat mask, Mat& cornersQuality, int blockSize = 3, int gradientSize = 3, bool useHarrisDetector = false, double k = 0.04)
 void cv::HoughLines(Mat image, Mat& lines, double rho, double theta, int threshold, double srn = 0, double stn = 0, double min_theta = 0, double max_theta = CV_PI)
 void cv::HoughLinesP(Mat image, Mat& lines, double rho, double theta, int threshold, double minLineLength = 0, double maxLineGap = 0)
 void cv::HoughLinesPointSet(Mat point, Mat& lines, int lines_max, int threshold, double min_rho, double max_rho, double rho_step, double min_theta, double max_theta, double theta_step)
 void cv::HoughCircles(Mat image, Mat& circles, HoughModes method, double dp, double minDist, double param1 = 100, double param2 = 100, int minRadius = 0, int maxRadius = 0)
 void cv::erode(Mat src, Mat& dst, Mat kernel, Point anchor = Point(-1,-1), int iterations = 1, BorderTypes borderType = BORDER_CONSTANT, Scalar borderValue = morphologyDefaultBorderValue())
 void cv::dilate(Mat src, Mat& dst, Mat kernel, Point anchor = Point(-1,-1), int iterations = 1, BorderTypes borderType = BORDER_CONSTANT, Scalar borderValue = morphologyDefaultBorderValue())
 void cv::morphologyEx(Mat src, Mat& dst, MorphTypes op, Mat kernel, Point anchor = Point(-1,-1), int iterations = 1, BorderTypes borderType = BORDER_CONSTANT, Scalar borderValue = morphologyDefaultBorderValue())
 void cv::resize(Mat src, Mat& dst, Size dsize, double fx = 0, double fy = 0, int interpolation = INTER_LINEAR)
 void cv::warpAffine(Mat src, Mat& dst, Mat M, Size dsize, int flags = INTER_LINEAR, BorderTypes borderMode = BORDER_CONSTANT, Scalar borderValue = Scalar())
 void cv::warpPerspective(Mat src, Mat& dst, Mat M, Size dsize, int flags = INTER_LINEAR, BorderTypes borderMode = BORDER_CONSTANT, Scalar borderValue = Scalar())
 void cv::remap(Mat src, Mat& dst, Mat map1, Mat map2, int interpolation, BorderTypes borderMode = BORDER_CONSTANT, Scalar borderValue = Scalar())
 void cv::convertMaps(Mat map1, Mat map2, Mat& dstmap1, Mat& dstmap2, int dstmap1type, bool nninterpolation = false)
 Mat cv::getRotationMatrix2D(Point2f center, double angle, double scale)
 void cv::invertAffineTransform(Mat M, Mat& iM)
 Mat cv::getPerspectiveTransform(Mat src, Mat dst, int solveMethod = DECOMP_LU)
 Mat cv::getAffineTransform(vector_Point2f src, vector_Point2f dst)
 void cv::getRectSubPix(Mat image, Size patchSize, Point2f center, Mat& patch, int patchType = -1)
 void cv::logPolar(Mat src, Mat& dst, Point2f center, double M, int flags)
 void cv::linearPolar(Mat src, Mat& dst, Point2f center, double maxRadius, int flags)
 void cv::warpPolar(Mat src, Mat& dst, Size dsize, Point2f center, double maxRadius, int flags)
 void cv::integral(Mat src, Mat& sum, Mat& sqsum, Mat& tilted, int sdepth = -1, int sqdepth = -1)
 void cv::integral(Mat src, Mat& sum, int sdepth = -1)
 void cv::integral(Mat src, Mat& sum, Mat& sqsum, int sdepth = -1, int sqdepth = -1)
 void cv::accumulate(Mat src, Mat& dst, Mat mask = Mat())
 void cv::accumulateSquare(Mat src, Mat& dst, Mat mask = Mat())
 void cv::accumulateProduct(Mat src1, Mat src2, Mat& dst, Mat mask = Mat())
 void cv::accumulateWeighted(Mat src, Mat& dst, double alpha, Mat mask = Mat())
 Point2d cv::phaseCorrelate(Mat src1, Mat src2, Mat window = Mat(), double* response = 0)
 void cv::createHanningWindow(Mat& dst, Size winSize, int type)
 void cv::divSpectrums(Mat a, Mat b, Mat& c, int flags, bool conjB = false)
 double cv::threshold(Mat src, Mat& dst, double thresh, double maxval, ThresholdTypes type)
 void cv::adaptiveThreshold(Mat src, Mat& dst, double maxValue, AdaptiveThresholdTypes adaptiveMethod, ThresholdTypes thresholdType, int blockSize, double C)
 void cv::pyrDown(Mat src, Mat& dst, Size dstsize = Size(), BorderTypes borderType = BORDER_DEFAULT)
 void cv::pyrUp(Mat src, Mat& dst, Size dstsize = Size(), BorderTypes borderType = BORDER_DEFAULT)
 void cv::calcHist(vector_Mat images, vector_int channels, Mat mask, Mat& hist, vector_int histSize, vector_float ranges, bool accumulate = false)
 void cv::calcBackProject(vector_Mat images, vector_int channels, Mat hist, Mat& dst, vector_float ranges, double scale)
 double cv::compareHist(Mat H1, Mat H2, HistCompMethods method)
 void cv::equalizeHist(Mat src, Mat& dst)
 Ptr_CLAHE cv::createCLAHE(double clipLimit = 40.0, Size tileGridSize = Size(8, 8))
 float cv::wrapperEMD(Mat signature1, Mat signature2, DistanceTypes distType, Mat cost = Mat(),  _hidden_ & lowerBound = cv::Ptr<float>(), Mat& flow = Mat())
 void cv::watershed(Mat image, Mat& markers)
 void cv::pyrMeanShiftFiltering(Mat src, Mat& dst, double sp, double sr, int maxLevel = 1, TermCriteria termcrit = TermCriteria(TermCriteria::MAX_ITER+TermCriteria::EPS,5,1))
 void cv::grabCut(Mat img, Mat& mask, Rect rect, Mat& bgdModel, Mat& fgdModel, int iterCount, int mode = GC_EVAL)
 void cv::distanceTransform(Mat src, Mat& dst, Mat& labels, DistanceTypes distanceType, DistanceTransformMasks maskSize, DistanceTransformLabelTypes labelType = DIST_LABEL_CCOMP)
 void cv::distanceTransform(Mat src, Mat& dst, DistanceTypes distanceType, DistanceTransformMasks maskSize, int dstType = CV_32F)
 int cv::floodFill(Mat& image, Mat& mask, Point seedPoint, Scalar newVal, Rect* rect = 0, Scalar loDiff = Scalar(), Scalar upDiff = Scalar(), int flags = 4)
 void cv::blendLinear(Mat src1, Mat src2, Mat weights1, Mat weights2, Mat& dst)
 void cv::cvtColor(Mat src, Mat& dst, ColorConversionCodes code, int dstCn = 0)
 void cv::cvtColorTwoPlane(Mat src1, Mat src2, Mat& dst, int code)
 void cv::demosaicing(Mat src, Mat& dst, int code, int dstCn = 0)
 Moments cv::moments(Mat array, bool binaryImage = false)
 void cv::HuMoments(Moments m, Mat& hu)
 void cv::matchTemplate(Mat image, Mat templ, Mat& result, TemplateMatchModes method, Mat mask = Mat())
 int cv::connectedComponents(Mat image, Mat& labels, int connectivity, int ltype, int ccltype)
 int cv::connectedComponents(Mat image, Mat& labels, int connectivity = 8, int ltype = CV_32S)
 int cv::connectedComponentsWithStats(Mat image, Mat& labels, Mat& stats, Mat& centroids, int connectivity, int ltype, ConnectedComponentsAlgorithmsTypes ccltype)
 int cv::connectedComponentsWithStats(Mat image, Mat& labels, Mat& stats, Mat& centroids, int connectivity = 8, int ltype = CV_32S)
 void cv::findContours(Mat image, vector_vector_Point& contours, Mat& hierarchy, RetrievalModes mode, ContourApproximationModes method, Point offset = Point())
 void cv::findContoursLinkRuns(Mat image, vector_Mat& contours, Mat& hierarchy)
 void cv::findContoursLinkRuns(Mat image, vector_Mat& contours)
 void cv::approxPolyDP(vector_Point2f curve, vector_Point2f& approxCurve, double epsilon, bool closed)
 double cv::arcLength(vector_Point2f curve, bool closed)
 Rect cv::boundingRect(Mat array)
 double cv::contourArea(Mat contour, bool oriented = false)
 RotatedRect cv::minAreaRect(vector_Point2f points)
 void cv::boxPoints(RotatedRect box, Mat& points)
 void cv::minEnclosingCircle(vector_Point2f points, Point2f& center, float& radius)
 double cv::minEnclosingTriangle(Mat points, Mat& triangle)
 double cv::matchShapes(Mat contour1, Mat contour2, ShapeMatchModes method, double parameter)
 void cv::convexHull(vector_Point points, vector_int& hull, bool clockwise = false,  _hidden_  returnPoints = true)
 void cv::convexityDefects(vector_Point contour, vector_int convexhull, vector_Vec4i& convexityDefects)
 bool cv::isContourConvex(vector_Point contour)
 float cv::intersectConvexConvex(Mat p1, Mat p2, Mat& p12, bool handleNested = true)
 RotatedRect cv::fitEllipse(vector_Point2f points)
 RotatedRect cv::fitEllipseAMS(Mat points)
 RotatedRect cv::fitEllipseDirect(Mat points)
 void cv::fitLine(Mat points, Mat& line, DistanceTypes distType, double param, double reps, double aeps)
 double cv::pointPolygonTest(vector_Point2f contour, Point2f pt, bool measureDist)
 int cv::rotatedRectangleIntersection(RotatedRect rect1, RotatedRect rect2, Mat& intersectingRegion)
 Ptr_GeneralizedHoughBallard cv::createGeneralizedHoughBallard()
 Ptr_GeneralizedHoughGuil cv::createGeneralizedHoughGuil()
 void cv::applyColorMap(Mat src, Mat& dst, ColormapTypes colormap)
 void cv::applyColorMap(Mat src, Mat& dst, Mat userColor)
 void cv::line(Mat& img, Point pt1, Point pt2, Scalar color, int thickness = 1, LineTypes lineType = LINE_8, int shift = 0)
 void cv::arrowedLine(Mat& img, Point pt1, Point pt2, Scalar color, int thickness = 1, LineTypes line_type = 8, int shift = 0, double tipLength = 0.1)
 void cv::rectangle(Mat& img, Point pt1, Point pt2, Scalar color, int thickness = 1, LineTypes lineType = LINE_8, int shift = 0)
 void cv::rectangle(Mat& img, Rect rec, Scalar color, int thickness = 1, LineTypes lineType = LINE_8, int shift = 0)
 void cv::circle(Mat& img, Point center, int radius, Scalar color, int thickness = 1, LineTypes lineType = LINE_8, int shift = 0)
 void cv::ellipse(Mat& img, Point center, Size axes, double angle, double startAngle, double endAngle, Scalar color, int thickness = 1, LineTypes lineType = LINE_8, int shift = 0)
 void cv::ellipse(Mat& img, RotatedRect box, Scalar color, int thickness = 1, LineTypes lineType = LINE_8)
 void cv::drawMarker(Mat& img, Point position, Scalar color, MarkerTypes markerType = MARKER_CROSS, int markerSize = 20, int thickness = 1, LineTypes line_type = 8)
 void cv::fillConvexPoly(Mat& img, vector_Point points, Scalar color, LineTypes lineType = LINE_8, int shift = 0)
 void cv::fillPoly(Mat& img, vector_vector_Point pts, Scalar color, LineTypes lineType = LINE_8, int shift = 0, Point offset = Point())
 void cv::polylines(Mat& img, vector_vector_Point pts, bool isClosed, Scalar color, int thickness = 1, LineTypes lineType = LINE_8, int shift = 0)
 void cv::drawContours(Mat& image, vector_vector_Point contours, int contourIdx, Scalar color, int thickness = 1, LineTypes lineType = LINE_8, Mat hierarchy = Mat(), int maxLevel = INT_MAX, Point offset = Point())
 bool cv::clipLine(Rect imgRect, Point& pt1, Point& pt2)
 void cv::ellipse2Poly(Point center, Size axes, int angle, int arcStart, int arcEnd, int delta, vector_Point& pts)
 void cv::putText(Mat& img, String text, Point org, HersheyFonts fontFace, double fontScale, Scalar color, int thickness = 1, LineTypes lineType = LINE_8, bool bottomLeftOrigin = false)
 Size cv::getTextSize(String text, HersheyFonts fontFace, double fontScale, int thickness, int* baseLine)
 double cv::getFontScaleFromHeight(int fontFace, int pixelHeight, int thickness = 1)
 void cv::HoughLinesWithAccumulator(Mat image, Mat& lines, double rho, double theta, int threshold, double srn = 0, double stn = 0, double min_theta = 0, double max_theta = CV_PI)
 void cv::CLAHE::apply(Mat src, Mat& dst)
 void cv::CLAHE::setClipLimit(double clipLimit)
 double cv::CLAHE::getClipLimit()
 void cv::CLAHE::setTilesGridSize(Size tileGridSize)
 Size cv::CLAHE::getTilesGridSize()
 void cv::CLAHE::collectGarbage()
 void cv::GeneralizedHough::setTemplate(Mat templ, Point templCenter = Point(-1, -1))
 void cv::GeneralizedHough::setTemplate(Mat edges, Mat dx, Mat dy, Point templCenter = Point(-1, -1))
 void cv::GeneralizedHough::detect(Mat image, Mat& positions, Mat& votes = Mat())
 void cv::GeneralizedHough::detect(Mat edges, Mat dx, Mat dy, Mat& positions, Mat& votes = Mat())
 void cv::GeneralizedHough::setCannyLowThresh(int cannyLowThresh)
 int cv::GeneralizedHough::getCannyLowThresh()
 void cv::GeneralizedHough::setCannyHighThresh(int cannyHighThresh)
 int cv::GeneralizedHough::getCannyHighThresh()
 void cv::GeneralizedHough::setMinDist(double minDist)
 double cv::GeneralizedHough::getMinDist()
 void cv::GeneralizedHough::setDp(double dp)
 double cv::GeneralizedHough::getDp()
 void cv::GeneralizedHough::setMaxBufferSize(int maxBufferSize)
 int cv::GeneralizedHough::getMaxBufferSize()
 void cv::GeneralizedHoughBallard::setLevels(int levels)
 int cv::GeneralizedHoughBallard::getLevels()
 void cv::GeneralizedHoughBallard::setVotesThreshold(int votesThreshold)
 int cv::GeneralizedHoughBallard::getVotesThreshold()
 void cv::GeneralizedHoughGuil::setXi(double xi)
 double cv::GeneralizedHoughGuil::getXi()
 void cv::GeneralizedHoughGuil::setLevels(int levels)
 int cv::GeneralizedHoughGuil::getLevels()
 void cv::GeneralizedHoughGuil::setAngleEpsilon(double angleEpsilon)
 double cv::GeneralizedHoughGuil::getAngleEpsilon()
 void cv::GeneralizedHoughGuil::setMinAngle(double minAngle)
 double cv::GeneralizedHoughGuil::getMinAngle()
 void cv::GeneralizedHoughGuil::setMaxAngle(double maxAngle)
 double cv::GeneralizedHoughGuil::getMaxAngle()
 void cv::GeneralizedHoughGuil::setAngleStep(double angleStep)
 double cv::GeneralizedHoughGuil::getAngleStep()
 void cv::GeneralizedHoughGuil::setAngleThresh(int angleThresh)
 int cv::GeneralizedHoughGuil::getAngleThresh()
 void cv::GeneralizedHoughGuil::setMinScale(double minScale)
 double cv::GeneralizedHoughGuil::getMinScale()
 void cv::GeneralizedHoughGuil::setMaxScale(double maxScale)
 double cv::GeneralizedHoughGuil::getMaxScale()
 void cv::GeneralizedHoughGuil::setScaleStep(double scaleStep)
 double cv::GeneralizedHoughGuil::getScaleStep()
 void cv::GeneralizedHoughGuil::setScaleThresh(int scaleThresh)
 int cv::GeneralizedHoughGuil::getScaleThresh()
 void cv::GeneralizedHoughGuil::setPosThresh(int posThresh)
 int cv::GeneralizedHoughGuil::getPosThresh()
 void cv::LineSegmentDetector::detect(Mat image, Mat& lines, Mat& width = Mat(), Mat& prec = Mat(), Mat& nfa = Mat())
 void cv::LineSegmentDetector::drawSegments(Mat& image, Mat lines)
 int cv::LineSegmentDetector::compareSegments(Size size, Mat lines1, Mat lines2, Mat& image = Mat())
  cv::Subdiv2D::Subdiv2D()
  cv::Subdiv2D::Subdiv2D(Rect rect)
 void cv::Subdiv2D::initDelaunay(Rect rect)
 int cv::Subdiv2D::insert(Point2f pt)
 void cv::Subdiv2D::insert(vector_Point2f ptvec)
 int cv::Subdiv2D::locate(Point2f pt, int& edge, int& vertex)
 int cv::Subdiv2D::findNearest(Point2f pt, Point2f* nearestPt = 0)
 void cv::Subdiv2D::getEdgeList(vector_Vec4f& edgeList)
 void cv::Subdiv2D::getLeadingEdgeList(vector_int& leadingEdgeList)
 void cv::Subdiv2D::getTriangleList(vector_Vec6f& triangleList)
 void cv::Subdiv2D::getVoronoiFacetList(vector_int idx, vector_vector_Point2f& facetList, vector_Point2f& facetCenters)
 Point2f cv::Subdiv2D::getVertex(int vertex, int* firstEdge = 0)
 int cv::Subdiv2D::getEdge(int edge, int nextEdgeType)
 int cv::Subdiv2D::nextEdge(int edge)
 int cv::Subdiv2D::rotateEdge(int edge, int rotate)
 int cv::Subdiv2D::symEdge(int edge)
 int cv::Subdiv2D::edgeOrg(int edge, Point2f* orgpt = 0)
 int cv::Subdiv2D::edgeDst(int edge, Point2f* dstpt = 0)
  cv::segmentation::IntelligentScissorsMB::IntelligentScissorsMB()
 IntelligentScissorsMB cv::segmentation::IntelligentScissorsMB::setWeights(float weight_non_edge, float weight_gradient_direction, float weight_gradient_magnitude)
 IntelligentScissorsMB cv::segmentation::IntelligentScissorsMB::setGradientMagnitudeMaxLimit(float gradient_magnitude_threshold_max = 0.0f)
 IntelligentScissorsMB cv::segmentation::IntelligentScissorsMB::setEdgeFeatureZeroCrossingParameters(float gradient_magnitude_min_value = 0.0f)
 IntelligentScissorsMB cv::segmentation::IntelligentScissorsMB::setEdgeFeatureCannyParameters(double threshold1, double threshold2, int apertureSize = 3, bool L2gradient = false)
 IntelligentScissorsMB cv::segmentation::IntelligentScissorsMB::applyImage(Mat image)
 IntelligentScissorsMB cv::segmentation::IntelligentScissorsMB::applyImageFeatures(Mat non_edge, Mat gradient_direction, Mat gradient_magnitude, Mat image = Mat())
 void cv::segmentation::IntelligentScissorsMB::buildMap(Point sourcePt)
 void cv::segmentation::IntelligentScissorsMB::getContour(Point targetPt, Mat& contour, bool backward = false)

SKIPPED FUNCs LIST (0 of 207):


0 def args - 110 funcs
1 def args - 41 funcs
2 def args - 23 funcs
3 def args - 18 funcs
4 def args - 13 funcs
5 def args - 1 funcs
8 def args - 1 funcs