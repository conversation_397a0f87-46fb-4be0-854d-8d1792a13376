//
// This file is auto-generated. Please don't modify it!
//

#import "Tracking.h"
#import "CVObjcUtil.h"



@implementation Tracking

+ (int)TrackerContribSamplerCSC_MODE_INIT_POS {
    return 1;
}

+ (int)TrackerContribSamplerCSC_MODE_INIT_NEG {
    return 2;
}

+ (int)TrackerContribSamplerCSC_MODE_TRACK_POS {
    return 3;
}

+ (int)TrackerContribSamplerCSC_MODE_TRACK_NEG {
    return 4;
}

+ (int)TrackerContribSamplerCSC_MODE_DETECT {
    return 5;
}

+ (int)TrackerSamplerCS_MODE_POSITIVE {
    return 1;
}

+ (int)TrackerSamplerCS_MODE_NEGATIVE {
    return 2;
}

+ (int)TrackerSamplerCS_MODE_CLASSIFY {
    return 3;
}



@end


