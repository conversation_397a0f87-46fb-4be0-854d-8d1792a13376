﻿  TestCompact.cpp
  TestDrawRects.cpp
  TestHaarCascadeApplication.cpp
  TestHaarCascadeLoader.cpp
  TestHypothesesFilter.cpp
  TestHypothesesGrow.cpp
  TestIntegralImage.cpp
  TestIntegralImageSquared.cpp
  TestRectStdDev.cpp
  TestResize.cpp
  TestTranspose.cpp
  main_nvidia.cpp
  test_calib3d.cpp
  test_labeling.cpp
  test_main.cpp
  test_nvidia.cpp
    正在创建库 D:/AI/opencv/cudabuild/bin/Release/opencv_test_cudalegacy.lib 和对象 D:/AI/opencv/cudabuild/bin/Release/opencv_test_cudalegacy.exp
LINK : warning LNK4098: 默认库“LIBCMT”与其他库的使用冲突；请使用 /NODEFAULTLIB:library
  opencv_test_cudalegacy.vcxproj -> D:\AI\opencv\cudabuild\bin\Release\opencv_test_cudalegacy.exe
