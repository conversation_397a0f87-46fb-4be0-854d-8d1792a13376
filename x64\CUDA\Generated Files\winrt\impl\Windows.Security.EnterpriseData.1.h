// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Security_EnterpriseData_1_H
#define WINRT_Windows_Security_EnterpriseData_1_H
#include "winrt/impl/Windows.Security.EnterpriseData.0.h"
WINRT_EXPORT namespace winrt::Windows::Security::EnterpriseData
{
    struct WINRT_IMPL_EMPTY_BASES IBufferProtectUnprotectResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBufferProtectUnprotectResult>
    {
        IBufferProtectUnprotectResult(std::nullptr_t = nullptr) noexcept {}
        IBufferProtectUnprotectResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDataProtectionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataProtectionInfo>
    {
        IDataProtectionInfo(std::nullptr_t = nullptr) noexcept {}
        IDataProtectionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDataProtectionManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataProtectionManagerStatics>
    {
        IDataProtectionManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IDataProtectionManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFileProtectionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileProtectionInfo>
    {
        IFileProtectionInfo(std::nullptr_t = nullptr) noexcept {}
        IFileProtectionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFileProtectionInfo2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileProtectionInfo2>
    {
        IFileProtectionInfo2(std::nullptr_t = nullptr) noexcept {}
        IFileProtectionInfo2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFileProtectionManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileProtectionManagerStatics>
    {
        IFileProtectionManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IFileProtectionManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFileProtectionManagerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileProtectionManagerStatics2>
    {
        IFileProtectionManagerStatics2(std::nullptr_t = nullptr) noexcept {}
        IFileProtectionManagerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFileProtectionManagerStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileProtectionManagerStatics3>
    {
        IFileProtectionManagerStatics3(std::nullptr_t = nullptr) noexcept {}
        IFileProtectionManagerStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFileRevocationManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileRevocationManagerStatics>
    {
        IFileRevocationManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IFileRevocationManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFileUnprotectOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileUnprotectOptions>
    {
        IFileUnprotectOptions(std::nullptr_t = nullptr) noexcept {}
        IFileUnprotectOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFileUnprotectOptionsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileUnprotectOptionsFactory>
    {
        IFileUnprotectOptionsFactory(std::nullptr_t = nullptr) noexcept {}
        IFileUnprotectOptionsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProtectedAccessResumedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProtectedAccessResumedEventArgs>
    {
        IProtectedAccessResumedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IProtectedAccessResumedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProtectedAccessSuspendingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProtectedAccessSuspendingEventArgs>
    {
        IProtectedAccessSuspendingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IProtectedAccessSuspendingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProtectedContainerExportResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProtectedContainerExportResult>
    {
        IProtectedContainerExportResult(std::nullptr_t = nullptr) noexcept {}
        IProtectedContainerExportResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProtectedContainerImportResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProtectedContainerImportResult>
    {
        IProtectedContainerImportResult(std::nullptr_t = nullptr) noexcept {}
        IProtectedContainerImportResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProtectedContentRevokedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProtectedContentRevokedEventArgs>
    {
        IProtectedContentRevokedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IProtectedContentRevokedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProtectedFileCreateResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProtectedFileCreateResult>
    {
        IProtectedFileCreateResult(std::nullptr_t = nullptr) noexcept {}
        IProtectedFileCreateResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProtectionPolicyAuditInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProtectionPolicyAuditInfo>
    {
        IProtectionPolicyAuditInfo(std::nullptr_t = nullptr) noexcept {}
        IProtectionPolicyAuditInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProtectionPolicyAuditInfoFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProtectionPolicyAuditInfoFactory>
    {
        IProtectionPolicyAuditInfoFactory(std::nullptr_t = nullptr) noexcept {}
        IProtectionPolicyAuditInfoFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProtectionPolicyManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProtectionPolicyManager>
    {
        IProtectionPolicyManager(std::nullptr_t = nullptr) noexcept {}
        IProtectionPolicyManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProtectionPolicyManager2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProtectionPolicyManager2>
    {
        IProtectionPolicyManager2(std::nullptr_t = nullptr) noexcept {}
        IProtectionPolicyManager2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProtectionPolicyManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProtectionPolicyManagerStatics>
    {
        IProtectionPolicyManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IProtectionPolicyManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProtectionPolicyManagerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProtectionPolicyManagerStatics2>
    {
        IProtectionPolicyManagerStatics2(std::nullptr_t = nullptr) noexcept {}
        IProtectionPolicyManagerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProtectionPolicyManagerStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProtectionPolicyManagerStatics3>
    {
        IProtectionPolicyManagerStatics3(std::nullptr_t = nullptr) noexcept {}
        IProtectionPolicyManagerStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProtectionPolicyManagerStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProtectionPolicyManagerStatics4>
    {
        IProtectionPolicyManagerStatics4(std::nullptr_t = nullptr) noexcept {}
        IProtectionPolicyManagerStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IThreadNetworkContext :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IThreadNetworkContext>
    {
        IThreadNetworkContext(std::nullptr_t = nullptr) noexcept {}
        IThreadNetworkContext(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
