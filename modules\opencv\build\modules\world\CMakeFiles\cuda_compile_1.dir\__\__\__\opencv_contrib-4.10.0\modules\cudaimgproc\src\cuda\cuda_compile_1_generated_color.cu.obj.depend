# Generated by: make2cmake.cmake
SET(CUDA_NVCC_DEPEND
  "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_math.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_math_defines.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_share.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_startup.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_terminate.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wconio.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wctype.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wdirect.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wio.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wprocess.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wtime.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/crtdbg.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/ctype.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/float.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/locale.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/malloc.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/math.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/process.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/share.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/sys/stat.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/sys/types.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/time.h"
 "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/ucrt/wchar.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_bit_utils.hpp"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_chrono.hpp"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_heap_algorithms.hpp"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_iter_core.hpp"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_minmax.hpp"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_ostream.hpp"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_sanitizer_annotate_container.hpp"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_string_view.hpp"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_system_error_abi.hpp"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_threads_core.hpp"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_xlocinfo_types.hpp"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/ammintrin.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cctype"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cerrno"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cfloat"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/clocale"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/complex"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/crtdefs.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdarg"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdio"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdlib"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/ctime"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cwchar"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/eh.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/emmintrin.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/exception"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/immintrin.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/intrin.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/intrin0.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/intrin0.inl.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/ios"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iosfwd"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iostream"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/istream"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/mmintrin.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/mutex"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/nmmintrin.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/ostream"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/pmmintrin.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/ratio"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/setjmp.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/smmintrin.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sstream"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdexcept"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdint.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/streambuf"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/system_error"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/thread"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tmmintrin.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/typeinfo"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/use_ansi.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_exception.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_new.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_new_debug.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_startup.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_typeinfo.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/wmmintrin.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xatomic.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xcall_once.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xerrc.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xfacet"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xiosbase"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xkeycheck.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xlocale"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xlocinfo"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xlocnum"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xmemory"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xmmintrin.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xstring"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xthreads.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xtimec.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xtr1common"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xutility"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/ymath.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h"
 "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/zmmintrin.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/builtin_types.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/channel_descriptor.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/common_functions.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/cudacc_ext.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/device_double_functions.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/device_double_functions.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/device_functions.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/device_functions.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/host_config.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/host_defines.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/math_functions.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/math_functions.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/sm_100_rt.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/sm_100_rt.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/sm_70_rt.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/sm_70_rt.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/sm_80_rt.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/sm_80_rt.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/sm_90_rt.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/crt/sm_90_rt.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cub/config.cuh"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cub/detail/detect_cuda_runtime.cuh"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cub/detail/type_traits.cuh"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cub/util_arch.cuh"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cub/util_compiler.cuh"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cub/util_cpp_dialect.cuh"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cub/util_debug.cuh"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cub/util_deprecated.cuh"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cub/util_macro.cuh"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cub/util_namespace.cuh"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cub/version.cuh"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/__cccl_config"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/cmath"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__algorithm/comp.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__algorithm/comp_ref_type.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__algorithm/iter_swap.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__algorithm/max.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__algorithm/max_element.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__algorithm/min.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__algorithm/min_element.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__algorithm/search.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cccl/attributes.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cccl/compiler.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cccl/diagnostic.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cccl/dialect.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cccl/exceptions.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cccl/execution_space.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cccl/extended_floating_point.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cccl/is_non_narrowing_convertible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cccl/ptx_isa.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cccl/sequence_access.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cccl/system_header.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cccl/version.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cccl/visibility.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/_One_of.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/__concept_macros.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/all_of.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/arithmetic.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/assignable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/boolean_testable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/class_or_enum.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/common_reference_with.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/common_with.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/constructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/convertible_to.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/copyable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/derived_from.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/destructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/different_from.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/equality_comparable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/invocable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/movable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/predicate.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/regular.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/relation.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/same_as.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/semiregular.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/swappable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__concepts/totally_ordered.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cuda/climits_prelude.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cuda/cmath_nvbf16.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cuda/cmath_nvfp16.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cuda/cstddef_prelude.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__cuda/cstdint_prelude.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__exception/terminate.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/binary_function.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/binary_negate.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/bind.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/bind_back.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/bind_front.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/binder1st.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/binder2nd.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/compose.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/default_searcher.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/function.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/hash.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/identity.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/invoke.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/is_transparent.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/mem_fn.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/mem_fun_ref.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/not_fn.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/operations.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/perfect_forward.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/pointer_to_binary_function.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/pointer_to_unary_function.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/ranges_operations.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/reference_wrapper.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/unary_function.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/unary_negate.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/unwrap_ref.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__functional/weak_result_type.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__fwd/array.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__fwd/get.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__fwd/hash.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__fwd/memory_resource.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__fwd/pair.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__fwd/string.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__fwd/subrange.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__fwd/tuple.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__iterator/access.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__iterator/advance.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__iterator/concepts.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__iterator/incrementable_traits.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__iterator/iter_move.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__iterator/iterator_traits.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__iterator/readable_traits.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__memory/addressof.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__memory/allocator_arg_t.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__memory/allocator_destructor.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__memory/allocator_traits.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__memory/builtin_new_allocator.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__memory/compressed_pair.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__memory/construct_at.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__memory/pointer_traits.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__memory/unique_ptr.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__memory/uses_allocator.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__memory/voidify.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__new/allocate.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__new/bad_alloc.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__new/launder.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__new_"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__tuple_dir/apply_cv.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__tuple_dir/make_tuple_types.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__tuple_dir/sfinae_helpers.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__tuple_dir/structured_bindings.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__tuple_dir/tuple_element.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__tuple_dir/tuple_indices.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__tuple_dir/tuple_like.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__tuple_dir/tuple_like_ext.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__tuple_dir/tuple_size.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__tuple_dir/tuple_types.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__tuple_dir/vector_types.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/add_const.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/add_cv.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/add_lvalue_reference.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/add_pointer.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/add_rvalue_reference.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/add_volatile.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/aligned_storage.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/aligned_union.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/alignment_of.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/apply_cv.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/can_extract_key.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/common_reference.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/common_type.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/conditional.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/conjunction.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/copy_cv.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/copy_cvref.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/decay.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/dependent_type.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/disjunction.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/enable_if.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/extent.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/has_unique_object_representation.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/has_virtual_destructor.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/integral_constant.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_abstract.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_aggregate.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_allocator.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_arithmetic.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_array.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_assignable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_base_of.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_bounded_array.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_callable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_char_like_type.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_class.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_compound.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_const.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_constant_evaluated.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_constructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_convertible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_copy_assignable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_copy_constructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_core_convertible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_default_constructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_destructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_empty.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_enum.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_extended_floating_point.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_final.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_floating_point.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_function.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_fundamental.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_implicitly_default_constructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_integral.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_literal_type.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_member_function_pointer.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_member_object_pointer.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_member_pointer.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_move_assignable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_move_constructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_nothrow_assignable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_nothrow_constructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_nothrow_convertible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_nothrow_copy_assignable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_nothrow_copy_constructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_nothrow_default_constructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_nothrow_destructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_nothrow_move_assignable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_nothrow_move_constructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_null_pointer.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_object.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_pod.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_pointer.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_polymorphic.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_primary_template.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_reference.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_reference_wrapper.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_referenceable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_same.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_scalar.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_scoped_enum.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_signed.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_signed_integer.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_standard_layout.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_swappable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_trivial.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_trivially_assignable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_trivially_constructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_trivially_copy_assignable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_trivially_copy_constructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_trivially_copyable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_trivially_default_constructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_trivially_destructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_trivially_move_assignable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_trivially_move_constructible.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_unbounded_array.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_union.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_unsigned.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_unsigned_integer.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_valid_expansion.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_void.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/is_volatile.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/lazy.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/make_32_64_or_128_bit.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/make_const_lvalue_ref.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/make_signed.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/make_unsigned.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/maybe_const.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/nat.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/negation.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/promote.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/rank.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/remove_all_extents.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/remove_const.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/remove_const_ref.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/remove_cv.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/remove_cvref.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/remove_extent.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/remove_pointer.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/remove_reference.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/remove_volatile.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/result_of.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/type_identity.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/type_list.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/underlying_type.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__type_traits/void_t.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/as_const.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/auto_cast.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/cmp.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/convert_to_integral.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/declval.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/exchange.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/forward.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/forward_like.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/in_place.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/integer_sequence.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/move.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/pair.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/piecewise_construct.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/priority_tag.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/rel_ops.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/swap.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/to_underlying.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/__utility/unreachable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/climits"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/cmath"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/concepts"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/cstddef"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/cstdint"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/cstdlib"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/detail/__config"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/detail/libcxx/include/__assert"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/detail/libcxx/include/__availability"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/detail/libcxx/include/__config"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/detail/libcxx/include/__debug"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/detail/libcxx/include/__verbose_abort"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/detail/libcxx/include/climits"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/detail/libcxx/include/cmath"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/detail/libcxx/include/cstddef"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/detail/libcxx/include/cstdint"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/detail/libcxx/include/cstdlib"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/detail/libcxx/include/cstring"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/detail/libcxx/include/iosfwd"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/detail/libcxx/include/limits"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/detail/libcxx/include/support/win32/limits_msvc_win32.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/detail/libcxx/include/tuple"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/functional"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/initializer_list"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/limits"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/tuple"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/type_traits"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/utility"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/std/version"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda/version"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda_bf16.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda_bf16.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda_device_runtime_api.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda_fp16.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda_fp16.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda_runtime.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/cuda_runtime_api.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/device_atomic_functions.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/device_atomic_functions.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/device_launch_parameters.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/device_types.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/driver_functions.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/driver_types.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/library_types.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/nv/detail/__preprocessor"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/nv/detail/__target_macros"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/nv/target"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/sm_20_atomic_functions.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/sm_20_atomic_functions.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/sm_20_intrinsics.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/sm_20_intrinsics.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/sm_30_intrinsics.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/sm_30_intrinsics.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/sm_32_atomic_functions.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/sm_32_atomic_functions.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/sm_32_intrinsics.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/sm_32_intrinsics.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/sm_35_atomic_functions.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/sm_35_intrinsics.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/sm_60_atomic_functions.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/sm_60_atomic_functions.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/sm_61_intrinsics.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/sm_61_intrinsics.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/surface_indirect_functions.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/surface_types.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/texture_indirect_functions.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/texture_types.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/alignment.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/allocator_aware_execution_policy.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/config.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/config/compiler.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/config/config.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/config/cpp_compatibility.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/config/cpp_dialect.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/config/deprecated.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/config/device_system.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/config/global_workarounds.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/config/host_system.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/config/namespace.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/config/simple_defines.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/dependencies_aware_execution_policy.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/execute_with_allocator_fwd.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/execute_with_dependencies.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/execution_policy.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/preprocessor.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/raw_pointer_cast.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/static_assert.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/type_deduction.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/type_traits.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/type_traits/has_nested_type.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/type_traits/is_metafunction_defined.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/detail/type_traits/pointer_traits.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/iterator/detail/any_system_tag.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/iterator/detail/device_system_tag.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/iterator/detail/host_system_tag.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/iterator/detail/iterator_category_to_system.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/iterator/detail/iterator_category_to_traversal.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/iterator/detail/iterator_category_with_system_and_traversal.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/iterator/detail/iterator_traits.inl"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/iterator/detail/iterator_traversal_tags.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/iterator/detail/universal_categories.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/iterator/iterator_categories.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/iterator/iterator_traits.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/system/cpp/detail/execution_policy.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/system/cuda/config.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/system/cuda/detail/execution_policy.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/system/detail/sequential/execution_policy.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/tuple.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/type_traits/is_contiguous_iterator.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/type_traits/is_trivially_relocatable.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/type_traits/remove_cvref.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/thrust/version.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/vector_functions.h"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/vector_functions.hpp"
 "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/vector_types.h"
 "D:/AI/opencv/cudabuild/cv_cpu_config.h"
 "D:/AI/opencv/cudabuild/cvconfig.h"
 "D:/AI/opencv/cudabuild/opencv2/opencv_modules.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/base.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/bufferpool.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/check.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/cuda.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/cuda.inl.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/cuda_stream_accessor.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/cuda_types.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/cv_cpu_dispatch.h"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/cv_cpu_helper.h"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/cvdef.h"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/cvstd.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/cvstd.inl.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/cvstd_wrapper.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/fast_math.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/hal/interface.h"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/mat.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/mat.inl.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/matx.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/matx.inl.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/neon_utils.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/operations.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/optim.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/ovx.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/persistence.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/saturate.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/traits.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/types.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/utility.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/utils/logger.defines.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/utils/logger.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/utils/logtag.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/version.hpp"
 "D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/vsx_utils.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/color.cu"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudaimgproc/src/cvt_color_internal.h"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/block/block.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/block/detail/reduce.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/block/detail/reduce_key_val.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/block/dynamic_smem.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/block/reduce.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/block/scan.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/block/vec_distance.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/common.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/expr/binary_func.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/expr/binary_op.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/expr/color.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/expr/deriv.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/expr/expr.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/expr/per_element_func.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/expr/reduction.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/expr/unary_func.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/expr/unary_op.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/expr/warping.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/functional/color_cvt.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/functional/detail/color_cvt.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/functional/functional.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/functional/tuple_adapter.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/copy.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/detail/copy.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/detail/histogram.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/detail/integral.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/detail/minmaxloc.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/detail/pyr_down.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/detail/pyr_up.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/detail/reduce.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/detail/reduce_to_column.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/detail/reduce_to_row.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/detail/split_merge.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/detail/transform.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/detail/transpose.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/histogram.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/integral.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/pyramids.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/reduce.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/reduce_to_vec.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/split_merge.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/transform.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/grid/transpose.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/ptr2d/constant.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/ptr2d/deriv.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/ptr2d/detail/gpumat.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/ptr2d/extrapolation.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/ptr2d/glob.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/ptr2d/gpumat.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/ptr2d/interpolation.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/ptr2d/lut.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/ptr2d/mask.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/ptr2d/remap.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/ptr2d/resize.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/ptr2d/texture.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/ptr2d/traits.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/ptr2d/transform.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/ptr2d/warping.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/ptr2d/zip.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/util/atomic.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/util/detail/tuple.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/util/detail/type_traits.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/util/limits.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/util/saturate_cast.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/util/simd_functions.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/util/tuple.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/util/type_traits.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/util/vec_math.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/util/vec_traits.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/warp/detail/reduce.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/warp/detail/reduce_key_val.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/warp/reduce.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/warp/scan.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/warp/shuffle.hpp"
 "D:/AI/opencv/opencv_contrib-4.10.0/modules/cudev/include/opencv2/cudev/warp/warp.hpp"
)

