﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_compoundkernel_tests.cpp">
      <Filter>opencv_gapi\Src\common</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_core_tests.cpp">
      <Filter>opencv_gapi\Src\common</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_imgproc_tests.cpp">
      <Filter>opencv_gapi\Src\common</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_operators_tests.cpp">
      <Filter>opencv_gapi\Src\common</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_render_tests.cpp">
      <Filter>opencv_gapi\Src\common</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_stereo_tests.cpp">
      <Filter>opencv_gapi\Src\common</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_video_tests.cpp">
      <Filter>opencv_gapi\Src\common</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\cpu\gapi_core_tests_cpu.cpp">
      <Filter>opencv_gapi\Src\cpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\cpu\gapi_core_tests_fluid.cpp">
      <Filter>opencv_gapi\Src\cpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\cpu\gapi_imgproc_tests_cpu.cpp">
      <Filter>opencv_gapi\Src\cpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\cpu\gapi_imgproc_tests_fluid.cpp">
      <Filter>opencv_gapi\Src\cpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\cpu\gapi_ocv_stateful_kernel_tests.cpp">
      <Filter>opencv_gapi\Src\cpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\cpu\gapi_operators_tests_cpu.cpp">
      <Filter>opencv_gapi\Src\cpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\cpu\gapi_operators_tests_fluid.cpp">
      <Filter>opencv_gapi\Src\cpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\cpu\gapi_ot_tests_cpu.cpp">
      <Filter>opencv_gapi\Src\cpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\cpu\gapi_stereo_tests_cpu.cpp">
      <Filter>opencv_gapi\Src\cpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\cpu\gapi_video_tests_cpu.cpp">
      <Filter>opencv_gapi\Src\cpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\executor\gtbbexecutor_internal_tests.cpp">
      <Filter>opencv_gapi\Src\executor</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_array_tests.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_async_test.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_basic_hetero_tests.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_compile_args_tests.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_desc_tests.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_fluid_parallel_rois_test.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_fluid_resize_test.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_fluid_roi_test.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_fluid_test.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_fluid_test_kernels.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_frame_tests.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_gcompiled_tests.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_gcomputation_tests.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_gpu_test.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_graph_meta_tests.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_kernel_tests.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_mat_tests.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_opaque_tests.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_plaidml_pipelines.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_planar_test.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_scalar_tests.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_smoke_test.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_transform_tests.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_typed_tests.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_util_tests.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gpu\gapi_core_tests_gpu.cpp">
      <Filter>opencv_gapi\Src\gpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gpu\gapi_imgproc_tests_gpu.cpp">
      <Filter>opencv_gapi\Src\gpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gpu\gapi_operators_tests_gpu.cpp">
      <Filter>opencv_gapi\Src\gpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\infer\gapi_infer_ie_test.cpp">
      <Filter>opencv_gapi\Src\infer</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\infer\gapi_infer_onnx_test.cpp">
      <Filter>opencv_gapi\Src\infer</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\infer\gapi_infer_ov_tests.cpp">
      <Filter>opencv_gapi\Src\infer</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\infer\gapi_infer_tests.cpp">
      <Filter>opencv_gapi\Src\infer</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\internal\gapi_int_backend_tests.cpp">
      <Filter>opencv_gapi\Src\internal</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\internal\gapi_int_dynamic_graph.cpp">
      <Filter>opencv_gapi\Src\internal</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\internal\gapi_int_executor_tests.cpp">
      <Filter>opencv_gapi\Src\internal</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\internal\gapi_int_garg_test.cpp">
      <Filter>opencv_gapi\Src\internal</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\internal\gapi_int_gmetaarg_test.cpp">
      <Filter>opencv_gapi\Src\internal</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\internal\gapi_int_gmodel_builder_test.cpp">
      <Filter>opencv_gapi\Src\internal</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\internal\gapi_int_island_fusion_tests.cpp">
      <Filter>opencv_gapi\Src\internal</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\internal\gapi_int_island_tests.cpp">
      <Filter>opencv_gapi\Src\internal</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\internal\gapi_int_pattern_matching_test.cpp">
      <Filter>opencv_gapi\Src\internal</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\internal\gapi_int_perform_substitution_test.cpp">
      <Filter>opencv_gapi\Src\internal</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\internal\gapi_int_proto_tests.cpp">
      <Filter>opencv_gapi\Src\internal</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\internal\gapi_int_recompilation_test.cpp">
      <Filter>opencv_gapi\Src\internal</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\internal\gapi_int_vectorref_test.cpp">
      <Filter>opencv_gapi\Src\internal</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\internal\gapi_transactions_test.cpp">
      <Filter>opencv_gapi\Src\internal</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\oak\gapi_tests_oak.cpp">
      <Filter>opencv_gapi\Src\oak</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\own\conc_queue_tests.cpp">
      <Filter>opencv_gapi\Src\own</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\own\gapi_types_tests.cpp">
      <Filter>opencv_gapi\Src\own</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\own\last_written_value_tests.cpp">
      <Filter>opencv_gapi\Src\own</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\own\mat_tests.cpp">
      <Filter>opencv_gapi\Src\own</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\own\scalar_tests.cpp">
      <Filter>opencv_gapi\Src\own</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\own\thread_pool_tests.cpp">
      <Filter>opencv_gapi\Src\own</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\render\ftp_render_test.cpp">
      <Filter>opencv_gapi\Src\render</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\render\gapi_render_tests_ocv.cpp">
      <Filter>opencv_gapi\Src\render</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\rmat\rmat_integration_tests.cpp">
      <Filter>opencv_gapi\Src\rmat</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\rmat\rmat_tests.cpp">
      <Filter>opencv_gapi\Src\rmat</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\rmat\rmat_view_tests.cpp">
      <Filter>opencv_gapi\Src\rmat</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\s11n\gapi_s11n_tests.cpp">
      <Filter>opencv_gapi\Src\s11n</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\s11n\gapi_sample_pipelines_s11n.cpp">
      <Filter>opencv_gapi\Src\s11n</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\streaming\gapi_gstreamer_pipeline_facade_int_tests.cpp">
      <Filter>opencv_gapi\Src\streaming</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\streaming\gapi_gstreamersource_tests.cpp">
      <Filter>opencv_gapi\Src\streaming</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\streaming\gapi_streaming_queue_source_tests.cpp">
      <Filter>opencv_gapi\Src\streaming</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\streaming\gapi_streaming_sync_tests.cpp">
      <Filter>opencv_gapi\Src\streaming</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\streaming\gapi_streaming_tests.cpp">
      <Filter>opencv_gapi\Src\streaming</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\streaming\gapi_streaming_utils_test.cpp">
      <Filter>opencv_gapi\Src\streaming</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\streaming\gapi_streaming_vpl_core_test.cpp">
      <Filter>opencv_gapi\Src\streaming</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\streaming\gapi_streaming_vpl_data_provider.cpp">
      <Filter>opencv_gapi\Src\streaming</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\streaming\gapi_streaming_vpl_device_selector.cpp">
      <Filter>opencv_gapi\Src\streaming</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\streaming\gapi_streaming_vpp_preproc_test.cpp">
      <Filter>opencv_gapi\Src\streaming</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\test_main.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\util\any_tests.cpp">
      <Filter>opencv_gapi\Src\util</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\util\optional_tests.cpp">
      <Filter>opencv_gapi\Src\util</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\util\variant_tests.cpp">
      <Filter>opencv_gapi\Src\util</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_core_tests.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_core_tests_common.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_core_tests_inl.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_imgproc_tests.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_imgproc_tests_common.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_imgproc_tests_inl.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_operators_tests.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_operators_tests_inl.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_parsers_tests_common.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_render_tests.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_stereo_tests.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_stereo_tests_inl.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_streaming_tests_common.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_tests_common.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_tests_helpers.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_video_tests.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_video_tests_common.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\common\gapi_video_tests_inl.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\cpu\gapi_ocv_stateful_kernel_test_utils.hpp">
      <Filter>opencv_gapi\Include\cpu</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_fluid_test_kernels.hpp">
      <Filter>opencv_gapi\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_mock_kernels.hpp">
      <Filter>opencv_gapi\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\opencl_kernels_test_gapi.hpp">
      <Filter>opencv_gapi\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\rmat\rmat_test_common.hpp">
      <Filter>opencv_gapi\Include\rmat</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\test\test_precomp.hpp">
      <Filter>opencv_gapi\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_gapi">
      <UniqueIdentifier>{635964F1-90D1-30BB-83E2-A7321E84DF5B}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Include">
      <UniqueIdentifier>{3AACB4B7-2E16-3D87-A4AE-891F4290CB7F}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Include\common">
      <UniqueIdentifier>{FEB4466B-38A3-3EA9-AF52-7E635A89F8CD}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Include\cpu">
      <UniqueIdentifier>{D6768648-E9FE-307E-A4E5-287CA7D9A045}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Include\rmat">
      <UniqueIdentifier>{90D05880-5B62-3C3A-9549-0FC2F68CC3EC}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src">
      <UniqueIdentifier>{237426CF-E287-3A3E-9F1C-A63D7BCD5856}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\common">
      <UniqueIdentifier>{233E4BEC-C49B-3457-8F4D-904A1E8416A2}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\cpu">
      <UniqueIdentifier>{54F2A6C1-BA7E-3E2B-8C18-386E2D9F28A0}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\executor">
      <UniqueIdentifier>{EAE2BC70-C8EC-3A9F-BD40-56FBD71E92D6}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\gpu">
      <UniqueIdentifier>{9937F1DC-D06A-3C6E-9C44-004954ED0604}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\infer">
      <UniqueIdentifier>{0D00BD6A-DA43-33CA-A405-92733E0F5E77}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\internal">
      <UniqueIdentifier>{0FC6E801-193D-3AEA-92A0-CED01F0A6059}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\oak">
      <UniqueIdentifier>{F234339F-89D7-3FD0-AD39-BCEDEA03B8BE}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\own">
      <UniqueIdentifier>{F5604987-12EE-3740-91BB-7F4802884013}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\render">
      <UniqueIdentifier>{5227B584-CDF5-34A2-8A5B-6B0E25B58A18}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\rmat">
      <UniqueIdentifier>{7074AD68-52DA-3B51-A5BA-0C8F50AE446F}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\s11n">
      <UniqueIdentifier>{1835FED8-6265-35C7-8A07-86194E1DE571}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\streaming">
      <UniqueIdentifier>{9B419151-4635-3CC9-B0F8-EEBD6C724C77}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\util">
      <UniqueIdentifier>{86EE3E84-D00D-3D25-B09F-F474D7606169}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
