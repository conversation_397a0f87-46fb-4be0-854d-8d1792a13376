#define WIN32_LEAN_AND_MEAN
#define _WINSOCKAPI_
#include <winsock2.h>
#include <Windows.h>

#include <shellapi.h>

#include "imgui/imgui.h"
#include <imgui_internal.h>

#include "sunone_aimbot_cpp.h"
#include "include/other_tools.h"
#include "kmbox_net/picture.h"

std::string ghub_version = get_ghub_version();

int prev_fovX = config.fovX;
int prev_fovY = config.fovY;
float prev_minSpeedMultiplier = config.minSpeedMultiplier;
float prev_maxSpeedMultiplier = config.maxSpeedMultiplier;
float prev_predictionInterval = config.predictionInterval;
float prev_snapRadius = config.snapRadius;
float prev_nearRadius = config.nearRadius;
float prev_speedCurveExponent = config.speedCurveExponent;
float prev_snapBoostFactor = config.snapBoostFactor;

bool  prev_wind_mouse_enabled = config.wind_mouse_enabled;
float prev_wind_G = config.wind_G;
float prev_wind_W = config.wind_W;
float prev_wind_M = config.wind_M;
float prev_wind_D = config.wind_D;

bool prev_auto_shoot = config.auto_shoot;
float prev_bScope_multiplier = config.bScope_multiplier;

static void draw_target_correction_demo()
{
    if (ImGui::CollapsingHeader(u8"虚拟演示"))
    {
        ImVec2 canvas_sz(220, 220);
        ImGui::InvisibleButton("##tc_canvas", canvas_sz);

        ImVec2 p0 = ImGui::GetItemRectMin();
        ImVec2 p1 = ImGui::GetItemRectMax();
        ImVec2 center{ (p0.x + p1.x) * 0.5f, (p0.y + p1.y) * 0.5f };

        ImDrawList* dl = ImGui::GetWindowDrawList();
        dl->AddRectFilled(p0, p1, IM_COL32(25, 25, 25, 255));

        const float scale = 4.0f;
        float near_px = config.nearRadius * scale;
        float snap_px = config.snapRadius * scale;
        near_px = ImClamp(near_px, 10.0f, canvas_sz.x * 0.45f);
        snap_px = ImClamp(snap_px, 6.0f, near_px - 4.0f);

        dl->AddCircle(center, near_px, IM_COL32(80, 120, 255, 180), 64, 2.0f);
        dl->AddCircle(center, snap_px, IM_COL32(255, 100, 100, 180), 64, 2.0f);

        static float  dist_px = near_px;
        static float  vel_px = 0.0f;
        static double last_t = ImGui::GetTime();
        double now = ImGui::GetTime();
        double dt = now - last_t;
        last_t = now;

        double dist_units = dist_px / scale;
        double speed_mult;
        if (dist_units < config.snapRadius)
            speed_mult = config.minSpeedMultiplier * config.snapBoostFactor;
        else if (dist_units < config.nearRadius)
        {
            double t = dist_units / config.nearRadius;
            double crv = 1.0 - pow(1.0 - t, config.speedCurveExponent);
            speed_mult = config.minSpeedMultiplier +
                (config.maxSpeedMultiplier - config.minSpeedMultiplier) * crv;
        }
        else
        {
            double norm = ImClamp(dist_units / config.nearRadius, 0.0, 1.0);
            speed_mult = config.minSpeedMultiplier +
                (config.maxSpeedMultiplier - config.minSpeedMultiplier) * norm;
        }

        double base_px_s = 60.0;
        vel_px = static_cast<float>(base_px_s * speed_mult);
        dist_px -= vel_px * static_cast<float>(dt);
        if (dist_px <= 0.0f) dist_px = near_px;

        ImVec2 dot{ center.x - dist_px, center.y };
        dl->AddCircleFilled(dot, 4.0f, IM_COL32(255, 255, 80, 255));

        ImGui::Dummy(ImVec2(0, 4));
        ImGui::TextColored(ImVec4(0.31f, 0.48f, 1.0f, 1.0f), u8"减速范围");
        ImGui::SameLine(130);
        ImGui::TextColored(ImVec4(1.0f, 0.39f, 0.39f, 1.0f), u8"吸附范围");
    }
}

void draw_mouse()
{
    ImGui::PushStyleColor(ImGuiCol_ChildBg, ImVec4(0.11f, 0.11f, 0.16f, 0.95f));
    ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.25f, 0.25f, 0.35f, 0.6f));
    ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 4.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 0.3f);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(6, 4));
    
    float content_width = ImGui::GetContentRegionAvail().x;
    float item_width = content_width * 0.7f;
    float slider_width = content_width * 0.4f;
    
    if (ImGui::BeginChild("AimRangeCard", ImVec2(0, 100), true))
    {
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::Text(u8"瞄准范围");
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();
        
        ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(0.5f, 0.8f, 0.3f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.16f, 0.16f, 0.22f, 1.0f));
        
        ImGui::SetNextItemWidth(slider_width);
        ImGui::SliderInt(u8"水平范围", &config.fovX, 10, 120);
        ImGui::Spacing();
        ImGui::SetNextItemWidth(slider_width);
        ImGui::SliderInt(u8"垂直范围", &config.fovY, 10, 120);
        
        ImGui::PopStyleColor(3);
    }
    ImGui::EndChild();
    
    ImGui::Spacing();

    if (ImGui::BeginChild("MouseSpeedCard", ImVec2(0, 100), true))
    {
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::Text(u8"鼠标速度");
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();
        
        ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(0.5f, 0.8f, 0.3f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.16f, 0.16f, 0.22f, 1.0f));
        
        ImGui::SetNextItemWidth(slider_width);
        ImGui::SliderFloat(u8"最慢速度", &config.minSpeedMultiplier, 0.1f, 5.0f, "%.1f");
        ImGui::Spacing();
        ImGui::SetNextItemWidth(slider_width);
        ImGui::SliderFloat(u8"最快速度", &config.maxSpeedMultiplier, 0.1f, 5.0f, "%.1f");
        
        ImGui::PopStyleColor(3);
    }
    ImGui::EndChild();
    
    ImGui::Spacing();

    if (ImGui::BeginChild("PredictionCard", ImVec2(0, 130), true))
    {
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::Text(u8"预判设置");
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();
        
        ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(0.5f, 0.8f, 0.3f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.16f, 0.16f, 0.22f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(0.5f, 0.8f, 0.3f, 1.0f));
        
        ImGui::SetNextItemWidth(slider_width);
        ImGui::SliderFloat(u8"预判时间", &config.predictionInterval, 0.00f, 0.5f, "%.2f");
        ImGui::Spacing();
        ImGui::SetNextItemWidth(slider_width);
        ImGui::SliderInt(u8"预判点数", &config.prediction_futurePositions, 1, 50);
        ImGui::Spacing();
        ImGui::Checkbox(u8"显示预判点", &config.draw_futurePositions);
        
        ImGui::PopStyleColor(4);
    }
    ImGui::EndChild();
    
    ImGui::Spacing();

    if (ImGui::BeginChild("AimAdjustCard", ImVec2(0, 200), true))
    {
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::Text(u8"自瞄调节");
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();
        
        ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(0.5f, 0.8f, 0.3f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.16f, 0.16f, 0.22f, 1.0f));
        
        ImGui::SetNextItemWidth(slider_width);
        ImGui::SliderFloat(u8"吸附范围", &config.snapRadius, 0.1f, 10.0f, "%.1f");
        ImGui::Spacing();
        ImGui::SetNextItemWidth(slider_width);
        ImGui::SliderFloat(u8"减速范围", &config.nearRadius, 1.0f, 100.0f, "%.1f");
        ImGui::Spacing();
        ImGui::SetNextItemWidth(slider_width);
        ImGui::SliderFloat(u8"速度曲线", &config.speedCurveExponent, 0.1f, 10.0f, "%.1f");
        ImGui::Spacing();
        ImGui::SetNextItemWidth(slider_width);
        ImGui::SliderFloat(u8"吸附增强", &config.snapBoostFactor, 0.1f, 5.0f, "%.2f");
        
        ImGui::PopStyleColor(3);
        
        draw_target_correction_demo();
    }
    ImGui::EndChild();
    
    ImGui::Spacing();

    if (ImGui::BeginChild("GameConfigCard", ImVec2(0, 280), true))
    {
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::Text(u8"游戏配置");
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();
        
        std::vector<std::string> profile_names;
        for (const auto& kv : config.game_profiles)
            profile_names.push_back(kv.first);

        static int selected_index = 0;
        for (size_t i = 0; i < profile_names.size(); ++i)
        {
            if (profile_names[i] == config.active_game)
            {
                selected_index = static_cast<int>(i);
                break;
            }
        }

        std::vector<const char*> profile_items;
        for (const auto& name : profile_names)
            profile_items.push_back(name.c_str());

        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.16f, 0.16f, 0.22f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.20f, 0.20f, 0.28f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.18f, 0.18f, 0.25f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(0.5f, 0.8f, 0.3f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(0.5f, 0.8f, 0.3f, 1.0f));

        ImGui::SetNextItemWidth(item_width);
        if (ImGui::Combo(u8"当前游戏配置", &selected_index, profile_items.data(), static_cast<int>(profile_items.size())))
        {
            config.active_game = profile_names[selected_index];
            config.saveConfig();
            globalMouseThread->updateConfig(
                config.detection_resolution,
                config.fovX,
                config.fovY,
                config.minSpeedMultiplier,
                config.maxSpeedMultiplier,
                config.predictionInterval,
                config.auto_shoot,
                config.bScope_multiplier
            );
            input_method_changed.store(true);
        }

        const auto& gp = config.currentProfile();

        ImGui::Spacing();
        ImGui::Text(u8"当前配置: %s", gp.name.c_str());
        ImGui::Text(u8"灵敏度: %.4f", gp.sens);
        ImGui::Text(u8"水平灵敏度: %.4f", gp.yaw);
        ImGui::Text(u8"垂直灵敏度: %.4f", gp.pitch);
        ImGui::Text(u8"FOV缩放: %s", gp.fovScaled ? u8"开启" : u8"关闭");

        if (gp.name != "UNIFIED")
        {
            Config::GameProfile& modifiable = config.game_profiles[gp.name];
            bool changed = false;

            float sens_f = static_cast<float>(modifiable.sens);
            float yaw_f = static_cast<float>(modifiable.yaw);
            float pitch_f = static_cast<float>(modifiable.pitch);
            float baseFOV_f = static_cast<float>(modifiable.baseFOV);

            ImGui::Spacing();
            ImGui::SetNextItemWidth(slider_width);
            changed |= ImGui::SliderFloat(u8"游戏灵敏度", &sens_f, 0.001f, 10.0f, "%.4f");
            ImGui::Spacing();
            ImGui::SetNextItemWidth(slider_width);
            changed |= ImGui::SliderFloat(u8"水平灵敏度", &yaw_f, 0.001f, 0.1f, "%.4f");
            ImGui::Spacing();
            ImGui::SetNextItemWidth(slider_width);
            changed |= ImGui::SliderFloat(u8"垂直灵敏度", &pitch_f, 0.001f, 0.1f, "%.4f");
            ImGui::Spacing();

            changed |= ImGui::Checkbox(u8"FOV缩放", &modifiable.fovScaled);
            if (modifiable.fovScaled)
            {
                ImGui::Spacing();
                ImGui::SetNextItemWidth(slider_width);
                changed |= ImGui::SliderFloat(u8"基础FOV", &baseFOV_f, 10.0f, 180.0f, "%.1f");
            }

            if (changed)
            {
                modifiable.sens = static_cast<double>(sens_f);
                modifiable.yaw = static_cast<double>(yaw_f);

                if (gp.pitch == 0.0 || !gp.fovScaled)
                    modifiable.pitch = modifiable.yaw;
                else
                    modifiable.pitch = static_cast<double>(pitch_f);

                modifiable.baseFOV = static_cast<double>(baseFOV_f);

                config.saveConfig();
                input_method_changed.store(true);
            }
        }
        
        ImGui::PopStyleColor(6);
    }
    ImGui::EndChild();
    
    ImGui::Spacing();

    if (ImGui::BeginChild("ConfigManageCard", ImVec2(0, 120), true))
    {
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::Text(u8"配置管理");
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();

        static char new_profile_name[64] = "";
        
        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.16f, 0.16f, 0.22f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.18f, 0.18f, 0.25f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.25f, 0.25f, 0.35f, 1.0f));
        
        ImGui::SetNextItemWidth(item_width * 0.6f);
        ImGui::InputText(u8"新配置名称", new_profile_name, sizeof(new_profile_name));
        ImGui::SameLine();
        if (ImGui::Button(u8"添加配置"))
        {
            std::string name = std::string(new_profile_name);
            if (!name.empty() && config.game_profiles.count(name) == 0)
            {
                Config::GameProfile gp;
                gp.name = name;
                gp.sens = 1.0;
                gp.yaw = 0.022;
                gp.pitch = 0.022;
                gp.fovScaled = false;
                gp.baseFOV = 90.0;
                config.game_profiles[name] = gp;
                config.active_game = name;
                config.saveConfig();
                input_method_changed.store(true);
                new_profile_name[0] = '\0';
            }
        }

        const auto& gp = config.currentProfile();
        if (gp.name != "UNIFIED")
        {
            ImGui::Spacing();
            ImGui::PushStyleColor(ImGuiCol_Button, IM_COL32(200, 50, 50, 255));
            if (ImGui::Button(u8"删除当前配置"))
            {
                config.game_profiles.erase(gp.name);
                if (!config.game_profiles.empty())
                    config.active_game = config.game_profiles.begin()->first;
                else
                    config.active_game = "UNIFIED";

                config.saveConfig();
                input_method_changed.store(true);
            }
            ImGui::PopStyleColor();
        }
        
        ImGui::PopStyleColor(3);
    }
    ImGui::EndChild();
    
    ImGui::Spacing();

    if (ImGui::BeginChild("RecoilCard", ImVec2(0, 130), true))
    {
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::Text(u8"压枪助手");
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();
        
        ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(0.5f, 0.8f, 0.3f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(0.5f, 0.8f, 0.3f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.16f, 0.16f, 0.22f, 1.0f));
        
        ImGui::Checkbox(u8"启用压枪", &config.easynorecoil);
        if (config.easynorecoil)
        {
            ImGui::Spacing();
            ImGui::SetNextItemWidth(slider_width);
            ImGui::SliderFloat(u8"压枪强度", &config.easynorecoilstrength, 0.1f, 500.0f, "%.1f");
            ImGui::Spacing();
            ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), u8"左右方向键：调节压枪强度±10");
            
            if (config.easynorecoilstrength >= 100.0f)
            {
                ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), u8"警告：压枪强度过高可能被检测");
            }
        }
        
        ImGui::PopStyleColor(4);
    }
    ImGui::EndChild();
    
    ImGui::Spacing();

    if (ImGui::BeginChild("AutoShootCard", ImVec2(0, 100), true))
    {
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::Text(u8"自动开火");
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();
        
        ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(0.5f, 0.8f, 0.3f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(0.5f, 0.8f, 0.3f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.16f, 0.16f, 0.22f, 1.0f));
        
        ImGui::Checkbox(u8"启用自动开火", &config.auto_shoot);
        if (config.auto_shoot)
        {
            ImGui::Spacing();
            ImGui::SetNextItemWidth(slider_width);
            ImGui::SliderFloat(u8"开镜倍率", &config.bScope_multiplier, 0.5f, 2.0f, "%.1f");
        }
        
        ImGui::PopStyleColor(4);
    }
    ImGui::EndChild();
    
    ImGui::Spacing();

    if (ImGui::BeginChild("WindMouseCard", ImVec2(0, 190), true))
    {
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::Text(u8"风鼠标算法");
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();
        
        ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(0.5f, 0.8f, 0.3f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(0.5f, 0.8f, 0.3f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.16f, 0.16f, 0.22f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.18f, 0.18f, 0.25f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.25f, 0.25f, 0.35f, 1.0f));

        if (ImGui::Checkbox(u8"启用风鼠标", &config.wind_mouse_enabled))
        {
            config.saveConfig();
            input_method_changed.store(true);
        }

        if (config.wind_mouse_enabled)
        {
            ImGui::Spacing();
            ImGui::SetNextItemWidth(slider_width);
            if (ImGui::SliderFloat(u8"重力强度", &config.wind_G, 4.00f, 40.00f, "%.2f"))
            {
                config.saveConfig();
            }
            ImGui::Spacing();
            ImGui::SetNextItemWidth(slider_width);
            if (ImGui::SliderFloat(u8"风力波动", &config.wind_W, 1.00f, 40.00f, "%.2f"))
            {
                config.saveConfig();
            }
            ImGui::Spacing();
            ImGui::SetNextItemWidth(slider_width);
            if (ImGui::SliderFloat(u8"最大步长", &config.wind_M, 1.00f, 40.00f, "%.2f"))
            {
                config.saveConfig();
            }
            ImGui::Spacing();
            ImGui::SetNextItemWidth(slider_width);
            if (ImGui::SliderFloat(u8"行为改变距离", &config.wind_D, 1.00f, 40.00f, "%.2f"))
            {
                config.saveConfig();
            }
            ImGui::Spacing();
            if (ImGui::Button(u8"重置为默认设置"))
            {
                config.wind_G = 18.0f;
                config.wind_W = 15.0f;
                config.wind_M = 10.0f;
                config.wind_D = 8.0f;
                config.saveConfig();
            }
        }
        
        ImGui::PopStyleColor(6);
    }
    ImGui::EndChild();
    
    ImGui::Spacing();

    if (ImGui::BeginChild("InputMethodCard", ImVec2(0, 200), true))
    {
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.9f, 0.4f, 1.0f));
        ImGui::Text(u8"输入方式");
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();
        
        std::vector<std::string> input_methods = { "WIN32", "GHUB", "ARDUINO", "KMBOX_B", "KMBOX_NET"};

        std::vector<const char*> method_items;
        method_items.reserve(input_methods.size());
        for (const auto& item : input_methods)
        {
            method_items.push_back(item.c_str());
        }

        std::string combo_label = "Mouse Input method";
        int input_method_index = 0;
        for (size_t i = 0; i < input_methods.size(); ++i)
        {
            if (input_methods[i] == config.input_method)
            {
                input_method_index = static_cast<int>(i);
                break;
            }
        }

        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.16f, 0.16f, 0.22f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.20f, 0.20f, 0.28f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.18f, 0.18f, 0.25f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.25f, 0.25f, 0.35f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(0.5f, 0.8f, 0.3f, 1.0f));

        ImGui::SetNextItemWidth(item_width);
        if (ImGui::Combo(u8"鼠标输入方式", &input_method_index, method_items.data(), static_cast<int>(method_items.size())))
        {
            std::string new_input_method = input_methods[input_method_index];

            if (new_input_method != config.input_method)
            {
                config.input_method = new_input_method;
                config.saveConfig();
                input_method_changed.store(true);
            }
        }

        ImGui::Spacing();

        if (config.input_method == "ARDUINO")
        {
            if (arduinoSerial)
            {
                if (arduinoSerial->isOpen())
                {
                    ImGui::TextColored(ImVec4(0, 255, 0, 255), u8"Arduino已连接");
                }
                else
                {
                    ImGui::TextColored(ImVec4(255, 0, 0, 255), u8"Arduino未连接");
                }
            }

            std::vector<std::string> port_list;
            for (int i = 1; i <= 30; ++i)
            {
                port_list.push_back("COM" + std::to_string(i));
            }

            std::vector<const char*> port_items;
            port_items.reserve(port_list.size());
            for (const auto& port : port_list)
            {
                port_items.push_back(port.c_str());
            }

            int port_index = 0;
            for (size_t i = 0; i < port_list.size(); ++i)
            {
                if (port_list[i] == config.arduino_port)
                {
                    port_index = static_cast<int>(i);
                    break;
                }
            }

            ImGui::Spacing();
            ImGui::SetNextItemWidth(item_width);
            if (ImGui::Combo(u8"Arduino端口", &port_index, port_items.data(), static_cast<int>(port_items.size())))
            {
                config.arduino_port = port_list[port_index];
                config.saveConfig();
                input_method_changed.store(true);
            }

            std::vector<int> baud_rate_list = { 9600, 19200, 38400, 57600, 115200 };
            std::vector<std::string> baud_rate_str_list;
            for (const auto& rate : baud_rate_list)
            {
                baud_rate_str_list.push_back(std::to_string(rate));
            }

            std::vector<const char*> baud_rate_items;
            baud_rate_items.reserve(baud_rate_str_list.size());
            for (const auto& rate_str : baud_rate_str_list)
            {
                baud_rate_items.push_back(rate_str.c_str());
            }

            int baud_rate_index = 0;
            for (size_t i = 0; i < baud_rate_list.size(); ++i)
            {
                if (baud_rate_list[i] == config.arduino_baudrate)
                {
                    baud_rate_index = static_cast<int>(i);
                    break;
                }
            }

            ImGui::Spacing();
            ImGui::SetNextItemWidth(item_width);
            if (ImGui::Combo(u8"Arduino波特率", &baud_rate_index, baud_rate_items.data(), static_cast<int>(baud_rate_items.size())))
            {
                config.arduino_baudrate = baud_rate_list[baud_rate_index];
                config.saveConfig();
                input_method_changed.store(true);
            }

            ImGui::Spacing();
            if (ImGui::Checkbox(u8"Arduino 16位鼠标", &config.arduino_16_bit_mouse))
            {
                config.saveConfig();
                input_method_changed.store(true);
            }
            if (ImGui::Checkbox(u8"Arduino启用按键", &config.arduino_enable_keys))
            {
                config.saveConfig();
                input_method_changed.store(true);
            }
        }
        else if (config.input_method == "GHUB")
        {
            if (ghub_version == "13.1.4")
            {
                std::string ghub_version_label = u8"已安装正确版本的Ghub: " + ghub_version;
                ImGui::Text(ghub_version_label.c_str());
            }
            else
            {
                if (ghub_version == "")
                {
                    ghub_version = u8"未知";
                }

                std::string ghub_version_label = u8"已安装Ghub版本: " + ghub_version;
                ImGui::Text(ghub_version_label.c_str());
                ImGui::Text(u8"安装了错误版本的Ghub或未设置默认路径。\n默认系统路径: C:\\Program Files\\LGHUB");
                ImGui::Spacing();
                if (ImGui::Button(u8"GHub文档"))
                {
                    ShellExecute(0, 0, L"https://github.com/SunOner/sunone_aimbot_docs/blob/main/tips/ghub.md", 0, 0, SW_SHOW);
                }
            }
            ImGui::Spacing();
            ImGui::TextColored(ImVec4(255, 0, 0, 255), u8"使用风险自负，此方法在某些游戏中可能被检测。");
        }
        else if (config.input_method == "WIN32")
        {
            ImGui::TextColored(ImVec4(255, 255, 255, 255), u8"这是标准鼠标输入方法，在大多数游戏中可能无效。请使用GHUB或ARDUINO。");
            ImGui::Spacing();
            ImGui::TextColored(ImVec4(255, 0, 0, 255), u8"使用风险自负，此方法在某些游戏中可能被检测。");
        }
        else if (config.input_method == "KMBOX_B")
        {
            std::vector<std::string> port_list;
            for (int i = 1; i <= 30; ++i)
            {
                port_list.push_back("COM" + std::to_string(i));
            }
            std::vector<const char*> port_items;
            port_items.reserve(port_list.size());
            for (auto& p : port_list) port_items.push_back(p.c_str());

            int port_index = 0;
            for (size_t i = 0; i < port_list.size(); ++i)
            {
                if (port_list[i] == config.kmbox_b_port)
                {
                    port_index = (int)i;
                    break;
                }
            }

            ImGui::SetNextItemWidth(item_width);
            if (ImGui::Combo(u8"kmbox端口", &port_index, port_items.data(), (int)port_items.size()))
            {
                config.kmbox_b_port = port_list[port_index];
                config.saveConfig();
                input_method_changed.store(true);
            }

            std::vector<int> baud_list = { 9600, 19200, 38400, 57600, 115200 };
            std::vector<std::string> baud_str_list;
            for (int b : baud_list) baud_str_list.push_back(std::to_string(b));
            std::vector<const char*> baud_items;
            baud_items.reserve(baud_str_list.size());
            for (auto& bs : baud_str_list) baud_items.push_back(bs.c_str());

            int baud_index = 0;
            for (size_t i = 0; i < baud_list.size(); ++i)
            {
                if (baud_list[i] == config.kmbox_b_baudrate)
                {
                    baud_index = (int)i;
                    break;
                }
            }

            ImGui::Spacing();
            ImGui::SetNextItemWidth(item_width);
            if (ImGui::Combo(u8"kmbox波特率", &baud_index, baud_items.data(), (int)baud_items.size()))
            {
                config.kmbox_b_baudrate = baud_list[baud_index];
                config.saveConfig();
                input_method_changed.store(true);
            }

            ImGui::Spacing();
            if (ImGui::Button(u8"运行boot.py"))
            {
                kmboxSerial->start_boot();
            }
            ImGui::SameLine();
            if (ImGui::Button(u8"重启KMBOX"))
            {
                kmboxSerial->reboot();
            }
            ImGui::Spacing();
            if (ImGui::Button(u8"发送停止信号"))
            {
                kmboxSerial->send_stop();
            }
        }
        else if (config.input_method == "KMBOX_NET")
        {
            static char ip[32], port[8], uuid[16];
            strncpy(ip, config.kmbox_net_ip.c_str(), sizeof(ip));
            strncpy(port, config.kmbox_net_port.c_str(), sizeof(port));
            strncpy(uuid, config.kmbox_net_uuid.c_str(), sizeof(uuid));

            ImGui::SetNextItemWidth(item_width);
            ImGui::InputText(u8"kmboxNet IP地址", ip, sizeof(ip));
            ImGui::Spacing();
            ImGui::SetNextItemWidth(item_width);
            ImGui::InputText(u8"端口", port, sizeof(port));
            ImGui::Spacing();
            ImGui::SetNextItemWidth(item_width);
            ImGui::InputText(u8"UUID", uuid, sizeof(uuid));

            ImGui::Spacing();
            if (ImGui::Button(u8"保存并重连"))
            {
                config.kmbox_net_ip = ip;
                config.kmbox_net_port = port;
                config.kmbox_net_uuid = uuid;
                config.saveConfig();
                input_method_changed.store(true);
            }

            ImGui::Spacing();
            if (kmboxNetSerial && kmboxNetSerial->isOpen())
            {
                ImGui::TextColored(ImVec4(0, 255, 0, 255), u8"kmboxNet已连接");
            }
            else
            {
                ImGui::TextColored(ImVec4(255, 0, 0, 255), u8"kmboxNet未连接");
            }
            
            ImGui::Spacing();
            if (ImGui::Button(u8"重启设备"))
            {
                if (kmboxNetSerial)
                {
                    kmboxNetSerial->reboot();
                }
            }
            ImGui::SameLine();
            if (ImGui::Button(u8"更换Kmbox图像"))
            {
                if (kmboxNetSerial)
                {
                    kmboxNetSerial->lcdColor(0);
                    kmboxNetSerial->lcdPicture(gImage_128x160);
                }
            }
        }
        
        ImGui::PopStyleColor(5);
    }
    ImGui::EndChild();
    
    ImGui::Spacing();
    ImGui::Separator();
    ImGui::TextColored(ImVec4(255, 255, 255, 100), u8"请勿在界面打开时测试射击和瞄准功能。");
    
    ImGui::PopStyleColor(2);
    ImGui::PopStyleVar(3);

    // 保持原有的配置更新逻辑
    if (prev_fovX != config.fovX ||
        prev_fovY != config.fovY ||
        prev_minSpeedMultiplier != config.minSpeedMultiplier ||
        prev_maxSpeedMultiplier != config.maxSpeedMultiplier ||
        prev_predictionInterval != config.predictionInterval ||
        prev_snapRadius != config.snapRadius ||
        prev_nearRadius != config.nearRadius ||
        prev_speedCurveExponent != config.speedCurveExponent ||
        prev_snapBoostFactor != config.snapBoostFactor)
    {
        prev_fovX = config.fovX;
        prev_fovY = config.fovY;
        prev_minSpeedMultiplier = config.minSpeedMultiplier;
        prev_maxSpeedMultiplier = config.maxSpeedMultiplier;
        prev_predictionInterval = config.predictionInterval;
        prev_snapRadius = config.snapRadius;
        prev_nearRadius = config.nearRadius;
        prev_speedCurveExponent = config.speedCurveExponent;
        prev_snapBoostFactor = config.snapBoostFactor;

        globalMouseThread->updateConfig(
            config.detection_resolution,
            config.fovX,
            config.fovY,
            config.minSpeedMultiplier,
            config.maxSpeedMultiplier,
            config.predictionInterval,
            config.auto_shoot,
            config.bScope_multiplier);

        config.saveConfig();
    }

    if (prev_wind_mouse_enabled != config.wind_mouse_enabled ||
        prev_wind_G != config.wind_G ||
        prev_wind_W != config.wind_W ||
        prev_wind_M != config.wind_M ||
        prev_wind_D != config.wind_D)
    {
        prev_wind_mouse_enabled = config.wind_mouse_enabled;
        prev_wind_G = config.wind_G;
        prev_wind_W = config.wind_W;
        prev_wind_M = config.wind_M;
        prev_wind_D = config.wind_D;

        globalMouseThread->updateConfig(
            config.detection_resolution,
            config.fovX,
            config.fovY,
            config.minSpeedMultiplier,
            config.maxSpeedMultiplier,
            config.predictionInterval,
            config.auto_shoot,
            config.bScope_multiplier);

        config.saveConfig();
    }

    if (prev_auto_shoot != config.auto_shoot ||
        prev_bScope_multiplier != config.bScope_multiplier)
    {
        prev_auto_shoot = config.auto_shoot;
        prev_bScope_multiplier = config.bScope_multiplier;

        globalMouseThread->updateConfig(
            config.detection_resolution,
            config.fovX,
            config.fovY,
            config.minSpeedMultiplier,
            config.maxSpeedMultiplier,
            config.predictionInterval,
            config.auto_shoot,
            config.bScope_multiplier);

        config.saveConfig();
    }
}










