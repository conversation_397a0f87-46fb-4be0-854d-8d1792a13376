// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_ApplicationModel_Resources_Management_1_H
#define WINRT_Windows_ApplicationModel_Resources_Management_1_H
#include "winrt/impl/Windows.ApplicationModel.Resources.Management.0.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Resources::Management
{
    struct WINRT_IMPL_EMPTY_BASES IIndexedResourceCandidate :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIndexedResourceCandidate>
    {
        IIndexedResourceCandidate(std::nullptr_t = nullptr) noexcept {}
        IIndexedResourceCandidate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIndexedResourceQualifier :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIndexedResourceQualifier>
    {
        IIndexedResourceQualifier(std::nullptr_t = nullptr) noexcept {}
        IIndexedResourceQualifier(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IResourceIndexer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IResourceIndexer>
    {
        IResourceIndexer(std::nullptr_t = nullptr) noexcept {}
        IResourceIndexer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IResourceIndexerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IResourceIndexerFactory>
    {
        IResourceIndexerFactory(std::nullptr_t = nullptr) noexcept {}
        IResourceIndexerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IResourceIndexerFactory2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IResourceIndexerFactory2>
    {
        IResourceIndexerFactory2(std::nullptr_t = nullptr) noexcept {}
        IResourceIndexerFactory2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
