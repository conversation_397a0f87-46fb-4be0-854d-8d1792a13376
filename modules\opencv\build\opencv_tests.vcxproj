﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{12109AFD-EE7F-3205-8474-9A94D4A84718}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>opencv_tests</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_bgsegm.vcxproj">
      <Project>{AB274CE3-4130-34A2-B5EC-A7D68E30C8E4}</Project>
      <Name>opencv_test_bgsegm</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_bioinspired.vcxproj">
      <Project>{2A6FBB46-2663-3AB0-85F0-18D836292306}</Project>
      <Name>opencv_test_bioinspired</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_calib3d.vcxproj">
      <Project>{A9CE6310-C337-3620-855F-17BCA886D2D8}</Project>
      <Name>opencv_test_calib3d</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core.vcxproj">
      <Project>{20BCDCC2-EF6B-38D3-93C7-4F95DD5145FD}</Project>
      <Name>opencv_test_core</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudaarithm.vcxproj">
      <Project>{17AA757A-594D-3DEE-8D59-3ADEFED4AEBB}</Project>
      <Name>opencv_test_cudaarithm</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudabgsegm.vcxproj">
      <Project>{1DDD7B63-9F19-3C5C-89DA-304A093ACCDB}</Project>
      <Name>opencv_test_cudabgsegm</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudacodec.vcxproj">
      <Project>{F961C93E-6792-367A-9B0D-E628A1355FA3}</Project>
      <Name>opencv_test_cudacodec</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudafeatures2d.vcxproj">
      <Project>{6F77DF9F-18C7-3EDD-B1AC-F1A6012F1C07}</Project>
      <Name>opencv_test_cudafeatures2d</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudafilters.vcxproj">
      <Project>{1CC02697-45BB-3566-8530-18674ED91696}</Project>
      <Name>opencv_test_cudafilters</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudaimgproc.vcxproj">
      <Project>{43A1D6CD-7084-3CC5-AEE9-F66B0934BF16}</Project>
      <Name>opencv_test_cudaimgproc</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudalegacy.vcxproj">
      <Project>{5BC869F2-B849-372A-97C7-C0E483D5BF5F}</Project>
      <Name>opencv_test_cudalegacy</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudaobjdetect.vcxproj">
      <Project>{7F2F54A4-E8D8-32F3-AC99-785755C68408}</Project>
      <Name>opencv_test_cudaobjdetect</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudaoptflow.vcxproj">
      <Project>{F1D25643-E3A6-3502-A988-2E37D154D30B}</Project>
      <Name>opencv_test_cudaoptflow</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudastereo.vcxproj">
      <Project>{80710832-FE32-32D2-B677-D260AEB166B2}</Project>
      <Name>opencv_test_cudastereo</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudawarping.vcxproj">
      <Project>{2D8309F8-A147-32E4-A1DF-88FB846D9277}</Project>
      <Name>opencv_test_cudawarping</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_dnn.vcxproj">
      <Project>{CA46D433-1B5A-3B6B-A526-1DF97D2588E0}</Project>
      <Name>opencv_test_dnn</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_dnn_superres.vcxproj">
      <Project>{AFA7C989-F448-365B-9DC9-5B5F107D04AD}</Project>
      <Name>opencv_test_dnn_superres</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_face.vcxproj">
      <Project>{FA3383DA-1CB9-3C20-AD36-2942D3D1D154}</Project>
      <Name>opencv_test_face</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_features2d.vcxproj">
      <Project>{2E4C3488-B183-3E50-898C-DC184AB27AED}</Project>
      <Name>opencv_test_features2d</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_flann.vcxproj">
      <Project>{BF4D207B-3FA9-3AA7-9FE2-E74D00C9DC4F}</Project>
      <Name>opencv_test_flann</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_fuzzy.vcxproj">
      <Project>{80FB8534-759F-37E7-BF4E-87B22CD3411F}</Project>
      <Name>opencv_test_fuzzy</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_gapi.vcxproj">
      <Project>{7E3F3299-9AE9-3535-A393-D06BECB3AEFD}</Project>
      <Name>opencv_test_gapi</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_highgui.vcxproj">
      <Project>{E72852E4-19D5-3DDE-A8CD-BB6F3DBEF2BE}</Project>
      <Name>opencv_test_highgui</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\img_hash\opencv_test_img_hash.vcxproj">
      <Project>{9610D2F5-A759-3F64-BB1B-AD434162186D}</Project>
      <Name>opencv_test_img_hash</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_imgcodecs.vcxproj">
      <Project>{A763175A-C8AA-3B7B-996D-4380075FC3D7}</Project>
      <Name>opencv_test_imgcodecs</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_imgproc.vcxproj">
      <Project>{1BB61935-747B-3444-8C64-D3908443C5DB}</Project>
      <Name>opencv_test_imgproc</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_intensity_transform.vcxproj">
      <Project>{694084B4-D6E7-352E-BD15-EE36FE1D1D50}</Project>
      <Name>opencv_test_intensity_transform</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_line_descriptor.vcxproj">
      <Project>{F713356D-4283-3E70-846A-88939E5A015A}</Project>
      <Name>opencv_test_line_descriptor</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_mcc.vcxproj">
      <Project>{0D9F8B5C-08F2-3D9B-A40F-8053FF826DBA}</Project>
      <Name>opencv_test_mcc</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_ml.vcxproj">
      <Project>{ADE70FE5-1BCF-3439-80B7-7984F3347868}</Project>
      <Name>opencv_test_ml</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_objdetect.vcxproj">
      <Project>{FDE3F5E1-BEF3-31CC-9681-4A6EAE8E8EC0}</Project>
      <Name>opencv_test_objdetect</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_optflow.vcxproj">
      <Project>{FB291324-E518-3CE8-AB43-683A43ECFDE5}</Project>
      <Name>opencv_test_optflow</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_phase_unwrapping.vcxproj">
      <Project>{59D15469-9DFC-3E6A-A662-3AFE4DAE934B}</Project>
      <Name>opencv_test_phase_unwrapping</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_photo.vcxproj">
      <Project>{4A381F53-2CFD-3FE2-82FC-FE74977E11A4}</Project>
      <Name>opencv_test_photo</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_quality.vcxproj">
      <Project>{D51DD788-733B-3F2F-9645-E7DC0001A609}</Project>
      <Name>opencv_test_quality</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_rapid.vcxproj">
      <Project>{2A097E88-2AF4-3824-A7BD-62220CA48022}</Project>
      <Name>opencv_test_rapid</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_reg.vcxproj">
      <Project>{285FEFA0-FC04-328F-B708-ECDE7E8F742D}</Project>
      <Name>opencv_test_reg</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_rgbd.vcxproj">
      <Project>{4A4F57BF-D9B4-3775-BBE9-54784FDC7D16}</Project>
      <Name>opencv_test_rgbd</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_saliency.vcxproj">
      <Project>{D2B920E9-7EE2-3C6F-AF90-0EBC49A44D89}</Project>
      <Name>opencv_test_saliency</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_shape.vcxproj">
      <Project>{B514C5B2-8EED-3100-9065-17A4DFAB0584}</Project>
      <Name>opencv_test_shape</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_signal.vcxproj">
      <Project>{2480BE67-8FA8-358D-95CD-1D680651AEE4}</Project>
      <Name>opencv_test_signal</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_stereo.vcxproj">
      <Project>{C3CB63F4-AD2B-3566-9E81-A9EE3872264E}</Project>
      <Name>opencv_test_stereo</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_stitching.vcxproj">
      <Project>{17A02BA3-F75D-3258-9349-956F14111B3F}</Project>
      <Name>opencv_test_stitching</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_structured_light.vcxproj">
      <Project>{03C548BA-2900-3ACA-B8AF-7ED57B66E862}</Project>
      <Name>opencv_test_structured_light</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_superres.vcxproj">
      <Project>{32717AB4-1792-3A36-8CA1-3F75318B1007}</Project>
      <Name>opencv_test_superres</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_text.vcxproj">
      <Project>{D28E9863-5E52-3895-9EFC-61EA4141D5ED}</Project>
      <Name>opencv_test_text</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_tracking.vcxproj">
      <Project>{EC8691B2-4B87-39ED-AEE9-0AEC7DE34B53}</Project>
      <Name>opencv_test_tracking</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_video.vcxproj">
      <Project>{C96EB2D4-BCEE-3536-BBBE-E39F2945167D}</Project>
      <Name>opencv_test_video</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_videoio.vcxproj">
      <Project>{FEE9F549-931A-35BA-A257-5157B9F6338C}</Project>
      <Name>opencv_test_videoio</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_videostab.vcxproj">
      <Project>{1E3C01D7-9346-30C2-A5EE-AE039CAC8886}</Project>
      <Name>opencv_test_videostab</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_wechat_qrcode.vcxproj">
      <Project>{4DFA4B86-3BFC-35C5-81B4-216805D2163A}</Project>
      <Name>opencv_test_wechat_qrcode</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_xfeatures2d.vcxproj">
      <Project>{60F8BCD5-A50D-3EBB-BAE8-1AA720C90510}</Project>
      <Name>opencv_test_xfeatures2d</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.vcxproj">
      <Project>{2199F101-C616-3A57-ACF3-1D944B533DA3}</Project>
      <Name>opencv_test_ximgproc</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_xphoto.vcxproj">
      <Project>{2E23BA6E-575F-3796-B694-D4114A0E37E8}</Project>
      <Name>opencv_test_xphoto</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>