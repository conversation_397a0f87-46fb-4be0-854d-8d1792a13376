// This file is part of OpenCV project.
// It is subject to the license terms in the LICENSE file found in the top-level directory
// of this distribution and at http://opencv.org/license.html.

/*
 * MIT License
 *
 * Copyright (c) 2018 Pedro <PERSON>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

#ifndef _MCC_DICTIONARY_HPP
#define _MCC_DICTIONARY_HPP



namespace cv
{
namespace mcc
{
///////////////////////////////////////////////////////////////////////////////
/// CChartClassicModel
const float CChartClassicModelColors[24][9] = {

    //       sRGB              CIE L*a*b*             Munsell Notation
    // ---------------  ------------------------     Hue Value / Chroma
    // R     G      B        L*      a*       b*
    {115.0f, 82.0f, 68.0f, 37.986f, 13.555f, 14.059f, 3.00f, 3.70f, 3.2f},    //1.  dark shin
    {194.0f, 150.0f, 130.0f, 65.711f, 18.130f, 17.810f, 2.20f, 6.47f, 4.1f},  //2.  light skin
    {98.0f, 122.0f, 157.0f, 49.927f, -4.880f, -21.925f, 4.30f, 4.95f, 5.0f},  //3.  blue skin
    {87.0f, 108.0f, 67.0f, 43.139f, -13.095f, 21.905f, 6.70f, 4.20f, 4.1f},   //4.  foliage
    {133.0f, 128.0f, 177.0f, 55.112f, 8.844f, -25.399f, 9.70f, 5.47f, 6.7f},  //5.  blue flower
    {103.0f, 189.0f, 170.0f, 70.719f, -33.395f, 0.199f, 2.50f, 7.00f, 6.0f},  //6.  bluish green
    {214.0f, 126.0f, 44.0f, 62.661f, 36.067f, 57.096f, 5.00f, 6.00f, 11.0f},  //7.  orange
    {80.0f, 91.0f, 166.0f, 40.020f, 10.410f, -45.964f, 7.50f, 4.00f, 10.7f},  //8.  purplish blue
    {193.0f, 90.0f, 99.0f, 51.124f, 48.239f, 16.248f, 2.50f, 5.00f, 10.0f},   //9.  moderate red
    {94.0f, 60.0f, 108.0f, 30.325f, 22.976f, -21.587f, 5.00f, 3.00f, 7.0f},   //10. purple
    {157.0f, 188.0f, 64.0f, 72.532f, -23.709f, 57.255f, 5.00f, 7.10f, 9.1f},  //11. yelow green
    {224.0f, 163.0f, 46.0f, 71.941f, 19.363f, 67.857f, 10.00f, 7.00f, 10.5f}, //12. orange yellow
    {56.0f, 61.0f, 150.0f, 28.778f, 14.179f, -50.297f, 7.50f, 2.90f, 12.7f},  //13. blue
    {70.0f, 148.0f, 73.0f, 55.261f, -38.342f, 31.370f, 0.25f, 5.40f, 8.65f},  //14. green
    {175.0f, 54.0f, 60.0f, 42.101f, 53.378f, 28.190f, 5.00f, 4.00f, 12.0f},   //15. red
    {231.0f, 199.0f, 31.0f, 81.733f, 4.039f, 79.819f, 5.00f, 8.00f, 11.1f},   //16. yellow
    {187.0f, 86.0f, 149.0f, 51.935f, 49.986f, -14.574f, 2.50f, 5.00f, 12.0f}, //17. magenta
    {8.0f, 133.0f, 161.0f, 51.038f, -28.631f, -28.638f, 5.00f, 5.00f, 8.0f},  //18. cyan
    {243.0f, 243.0f, 242.0f, 96.539f, -0.425f, 1.186f, 0.00f, 9.50f, 0.0f},   //19. white(.05*)
    {200.0f, 200.0f, 200.0f, 81.257f, -0.638f, -0.335f, 0.00f, 8.00f, 0.0f},  //20. neutral 8(.23*)
    {160.0f, 160.0f, 160.0f, 66.766f, -0.734f, -0.504f, 0.00f, 6.50f, 0.0f},  //21. neutral 6.5(.44*)
    {122.0f, 122.0f, 121.0f, 50.867f, -0.153f, -0.270f, 0.00f, 5.00f, 0.0f},  //22. neutral 5(.70*)
    {85.0f, 85.0f, 85.0f, 35.656f, -0.421f, -1.231f, 0.00f, 3.50f, 0.0f},     //23. neutral 3.5(.1.05*)
    {52.0f, 52.0f, 52.0f, 20.461f, -0.079f, -0.973f, 0.00f, 2.00f, 0.0f},     //24. black(1.50*)

};

const cv::Point2f CChartClassicModelCellchart[96] = {
    {0.25f, 0.25f},
    {2.75f, 0.25f},
    {2.75f, 2.75f},
    {0.25f, 2.75f},
    {3.00f, 0.25f},
    {5.50f, 0.25f},
    {5.50f, 2.75f},
    {3.00f, 2.75f},
    {5.75f, 0.25f},
    {8.25f, 0.25f},
    {8.25f, 2.75f},
    {5.75f, 2.75f},
    {8.50f, 0.25f},
    {11.00f, 0.25f},
    {11.00f, 2.75f},
    {8.50f, 2.75f},
    {11.25f, 0.25f},
    {13.75f, 0.25f},
    {13.75f, 2.75f},
    {11.25f, 2.75f},
    {14.00f, 0.25f},
    {16.50f, 0.25f},
    {16.50f, 2.75f},
    {14.00f, 2.75f},
    {0.25f, 3.00f},
    {2.75f, 3.00f},
    {2.75f, 5.50f},
    {0.25f, 5.50f},
    {3.00f, 3.00f},
    {5.50f, 3.00f},
    {5.50f, 5.50f},
    {3.00f, 5.50f},
    {5.75f, 3.00f},
    {8.25f, 3.00f},
    {8.25f, 5.50f},
    {5.75f, 5.50f},
    {8.50f, 3.00f},
    {11.00f, 3.00f},
    {11.00f, 5.50f},
    {8.50f, 5.50f},
    {11.25f, 3.00f},
    {13.75f, 3.00f},
    {13.75f, 5.50f},
    {11.25f, 5.50f},
    {14.00f, 3.00f},
    {16.50f, 3.00f},
    {16.50f, 5.50f},
    {14.00f, 5.50f},
    {0.25f, 5.75f},
    {2.75f, 5.75f},
    {2.75f, 8.25f},
    {0.25f, 8.25f},
    {3.00f, 5.75f},
    {5.50f, 5.75f},
    {5.50f, 8.25f},
    {3.00f, 8.25f},
    {5.75f, 5.75f},
    {8.25f, 5.75f},
    {8.25f, 8.25f},
    {5.75f, 8.25f},
    {8.50f, 5.75f},
    {11.00f, 5.75f},
    {11.00f, 8.25f},
    {8.50f, 8.25f},
    {11.25f, 5.75f},
    {13.75f, 5.75f},
    {13.75f, 8.25f},
    {11.25f, 8.25f},
    {14.00f, 5.75f},
    {16.50f, 5.75f},
    {16.50f, 8.25f},
    {14.00f, 8.25f},
    {0.25f, 8.50f},
    {2.75f, 8.50f},
    {2.75f, 11.00f},
    {0.25f, 11.00f},
    {3.00f, 8.50f},
    {5.50f, 8.50f},
    {5.50f, 11.00f},
    {3.00f, 11.00f},
    {5.75f, 8.50f},
    {8.25f, 8.50f},
    {8.25f, 11.00f},
    {5.75f, 11.00f},
    {8.50f, 8.50f},
    {11.00f, 8.50f},
    {11.00f, 11.00f},
    {8.50f, 11.00f},
    {11.25f, 8.50f},
    {13.75f, 8.50f},
    {13.75f, 11.00f},
    {11.25f, 11.00f},
    {14.00f, 8.50f},
    {16.50f, 8.50f},
    {16.50f, 11.00f},
    {14.00f, 11.00f},

};

const cv::Point2f CChartClassicModelCenter[24] = {
    {1.50f, 1.50f},
    {4.25f, 1.50f},
    {7.00f, 1.50f},
    {9.75f, 1.50f},
    {12.50f, 1.50f},
    {15.25f, 1.50f},
    {1.50f, 4.25f},
    {4.25f, 4.25f},
    {7.00f, 4.25f},
    {9.75f, 4.25f},
    {12.50f, 4.25f},
    {15.25f, 4.25f},
    {1.50f, 7.00f},
    {4.25f, 7.00f},
    {7.00f, 7.00f},
    {9.75f, 7.00f},
    {12.50f, 7.00f},
    {15.25f, 7.00f},
    {1.50f, 9.75f},
    {4.25f, 9.75f},
    {7.00f, 9.75f},
    {9.75f, 9.75f},
    {12.50f, 9.75f},
    {15.25f, 9.75f},

};

//////////////////////////////////////////////////////////////////////////////////////////////
/// CChartDigitalSG
const float CChartDigitalSGColors[140][9] = {

    //       sRGB              CIE L*a*b*             Munsell Notation
    // ---------------  ------------------------     Hue Value / Chroma
    // R     G      B        L*      a*       b*
    {243.6785f, 245.5509f, 243.9088f, 96.55f, -0.91f, 0.57f, -1.0f, -1.0f, -1.0f},
    {19.8630f, 20.3003f, 20.8469f, 6.43f, -0.06f, -0.41f, -1.0f, -1.0f, -1.0f},
    {117.8584f, 118.2530f, 118.1025f, 49.7f, -0.18f, 0.03f, -1.0f, -1.0f, -1.0f},
    {243.5942f, 245.3933f, 243.7273f, 96.5f, -0.89f, 0.59f, -1.0f, -1.0f, -1.0f},
    {19.9993f, 20.4566f, 21.0413f, 6.5f, -0.06f, -0.44f, -1.0f, -1.0f, -1.0f},
    {117.7060f, 118.1645f, 118.0348f, 49.66f, -0.2f, 0.01f, -1.0f, -1.0f, -1.0f},
    {243.6013f, 245.4638f, 243.8033f, 96.52f, -0.91f, 0.58f, -1.0f, -1.0f, -1.0f},
    {20.1535f, 20.4065f, 20.7898f, 6.49f, -0.02f, -0.28f, -1.0f, -1.0f, -1.0f},
    {117.8806f, 118.3134f, 118.1353f, 49.72f, -0.2f, 0.04f, -1.0f, -1.0f, -1.0f},
    {243.4242f, 245.1981f, 243.3717f, 96.43f, -0.91f, 0.67f, -1.0f, -1.0f, -1.0f},
    {117.8668f, 118.3107f, 118.2028f, 49.72f, -0.19f, 0.0f, -1.0f, -1.0f, -1.0f},
    {141.3577f, 28.0252f, 95.0288f, 32.6f, 51.58f, -10.85f, -1.0f, -1.0f, -1.0f},
    {177.5963f, 131.1085f, 179.7258f, 60.75f, 26.22f, -18.6f, -1.0f, -1.0f, -1.0f},
    {108.2022f, 30.1190f, 128.8980f, 28.69f, 48.28f, -39.00f, -1.0f, -1.0f, -1.0f},
    {0.0f, 129.2029f, 199.4861f, 49.38f, -15.43f, -48.48f, -1.0f, -1.0f, -1.0f},
    {0.0f, 162.7924f, 191.4749f, 60.63f, -30.77f, -26.23f, -1.0f, -1.0f, -1.0f},
    {0.0f, 56.2386f, 55.1200f, 19.29f, -26.37f, -6.15f, -1.0f, -1.0f, -1.0f},
    {0.0f, 164.6157f, 165.9222f, 60.15f, -41.77f, -12.60f, -1.0f, -1.0f, -1.0f},
    {58.6915f, 50.1355f, 38.5574f, 21.42f, 1.67f, 8.79f, -1.0f, -1.0f, -1.0f},
    {117.7814f, 118.2399f, 118.1103f, 49.69f, -0.2f, 0.01f, -1.0f, -1.0f, -1.0f},
    {19.8846f, 20.4558f, 21.3702f, 6.5f, -0.03f, -0.67f, -1.0f, -1.0f, -1.0f},
    {64.2572f, 43.9020f, 79.5758f, 21.82f, 17.33f, -18.35f, -1.0f, -1.0f, -1.0f},
    {91.6752f, 90.6539f, 159.4087f, 41.53f, 18.48f, -37.26f, -1.0f, -1.0f, -1.0f},
    {0.0f, 51.7097f, 101.8051f, 19.99f, -0.16f, -36.29f, -1.0f, -1.0f, -1.0f},
    {16.4706f, 156.7786f, 199.5879f, 60.16f, -18.45f, -31.42f, -1.0f, -1.0f, -1.0f},
    {0.0f, 56.3863f, 78.4458f, 19.94f, -17.92f, -20.96f, -1.0f, -1.0f, -1.0f},
    {85.8767f, 152.3418f, 203.7400f, 60.68f, -6.05f, -32.81f, -1.0f, -1.0f, -1.0f},
    {0.0f, 141.8412f, 136.0915f, 50.81f, -49.8f, -9.63f, -1.0f, -1.0f, -1.0f},
    {73.4809f, 163.3852f, 108.1566f, 60.65f, -39.77f, 20.76f, -1.0f, -1.0f, -1.0f},
    {20.1214f, 20.5079f, 21.0922f, 6.53f, -0.03f, -0.43f, -1.0f, -1.0f, -1.0f},
    {243.7254f, 245.5782f, 243.8991f, 96.56f, -0.91f, 0.59f, -1.0f, -1.0f, -1.0f},
    {198.0681f, 211.7588f, 225.2718f, 84.19f, -1.95f, -8.23f, -1.0f, -1.0f, -1.0f},
    {239.3827f, 202.2618f, 211.6298f, 84.75f, 14.55f, 0.23f, -1.0f, -1.0f, -1.0f},
    {169.1422f, 222.5039f, 212.7889f, 84.87f, -19.07f, -0.82f, -1.0f, -1.0f, -1.0f},
    {243.3181f, 203.6053f, 200.4170f, 85.15f, 13.48f, 6.82f, -1.0f, -1.0f, -1.0f},
    {209.6511f, 214.2775f, 159.1455f, 84.17f, -10.45f, 26.78f, -1.0f, -1.0f, -1.0f},
    {215.5821f, 125.8663f, 86.0613f, 61.74f, 31.06f, 36.42f, -1.0f, -1.0f, -1.0f},
    {202.3497f, 141.5423f, 123.4314f, 64.37f, 20.82f, 18.92f, -1.0f, -1.0f, -1.0f},
    {0.0f, 140.4951f, 93.3754f, 50.4f, -53.22f, 14.62f, -1.0f, -1.0f, -1.0f},
    {243.6773f, 245.4176f, 243.6408f, 96.51f, -0.89f, 0.65f, -1.0f, -1.0f, -1.0f},
    {117.9409f, 118.3590f, 118.2027f, 49.74f, -0.19f, 0.03f, -1.0f, -1.0f, -1.0f},
    {110.5974f, 62.7615f, 41.3328f, 31.91f, 18.62f, 21.99f, -1.0f, -1.0f, -1.0f},
    {228.7579f, 115.4417f, 0.0f, 60.74f, 38.66f, 70.97f, -1.0f, -1.0f, -1.0f},
    {0.0f, 42.4374f, 135.4228f, 19.35f, 22.23f, -58.86f, -1.0f, -1.0f, -1.0f},
    {243.6376f, 245.4608f, 243.7265f, 96.52f, -0.91f, 0.62f, -1.0f, -1.0f, -1.0f},
    {20.5471f, 20.7706f, 21.1856f, 6.66f, 0.00f, -0.30f, -1.0f, -1.0f, -1.0f},
    {239.4052f, 173.7086f, 147.9643f, 76.51f, 20.81f, 22.72f, -1.0f, -1.0f, -1.0f},
    {241.8648f, 157.2827f, 136.0207f, 72.79f, 29.15f, 24.18f, -1.0f, -1.0f, -1.0f},
    {12.9473f, 61.0106f, 44.3207f, 22.33f, -20.70f, 5.75f, -1.0f, -1.0f, -1.0f},
    {117.8245f, 118.2597f, 118.1357f, 49.7f, -0.19f, 0.01f, -1.0f, -1.0f, -1.0f},
    {19.9625f, 20.5277f, 21.3483f, 6.53f, -0.05f, -0.61f, -1.0f, -1.0f, -1.0f},
    {198.8077f, 139.4916f, 120.4582f, 63.42f, 20.19f, 19.22f, -1.0f, -1.0f, -1.0f},
    {0.0f, 81.2537f, 163.8455f, 34.94f, 11.64f, -50.70f, -1.0f, -1.0f, -1.0f},
    {54.9984f, 141.2109f, 51.9967f, 52.03f, -44.15f, 39.04f, -1.0f, -1.0f, -1.0f},
    {197.1775f, 196.5945f, 197.0767f, 79.43f, 0.29f, -0.17f, -1.0f, -1.0f, -1.0f},
    {71.5553f, 72.2783f, 72.9901f, 30.67f, -0.14f, -0.53f, -1.0f, -1.0f, -1.0f},
    {193.4891f, 143.5716f, 108.3919f, 63.6f, 14.44f, 26.07f, -1.0f, -1.0f, -1.0f},
    {191.7291f, 146.0328f, 126.4483f, 64.37f, 14.50f, 17.05f, -1.0f, -1.0f, -1.0f},
    {9.3958f, 163.8812f, 128.3477f, 60.01f, -44.33f, 8.49f, -1.0f, -1.0f, -1.0f},
    {20.3474f, 20.7197f, 21.3632f, 6.63f, -0.01f, -0.47f, -1.0f, -1.0f, -1.0f},
    {243.6841f, 245.5904f, 243.8984f, 96.56f, -0.93f, 0.59f, -1.0f, -1.0f, -1.0f},
    {67.5763f, 114.3549f, 150.4402f, 46.37f, -5.09f, -24.46f, -1.0f, -1.0f, -1.0f},
    {195.8311f, 65.0081f, 80.2132f, 47.08f, 52.97f, 20.49f, -1.0f, -1.0f, -1.0f},
    {178.5490f, 0.0f, 27.2862f, 36.04f, 64.92f, 38.51f, -1.0f, -1.0f, -1.0f},
    {157.5618f, 157.8520f, 158.3960f, 65.05f, 0.00f, -0.32f, -1.0f, -1.0f, -1.0f},
    {93.9493f, 94.6924f, 95.1781f, 40.14f, -0.19f, -0.38f, -1.0f, -1.0f, -1.0f},
    {141.2464f, 92.1355f, 59.0432f, 43.77f, 16.46f, 27.12f, -1.0f, -1.0f, -1.0f},
    {195.4679f, 144.4185f, 127.4304f, 64.39f, 17.00f, 16.59f, -1.0f, -1.0f, -1.0f},
    {116.6521f, 159.0566f, 69.5398f, 60.79f, -29.74f, 41.50f, -1.0f, -1.0f, -1.0f},
    {243.5820f, 245.3320f, 243.5738f, 96.48f, -0.89f, 0.64f, -1.0f, -1.0f, -1.0f},
    {117.9142f, 118.3962f, 118.2609f, 49.75f, -0.21f, 0.01f, -1.0f, -1.0f, -1.0f},
    {78.3936f, 96.2850f, 37.2418f, 38.18f, -16.99f, 30.87f, -1.0f, -1.0f, -1.0f},
    {72.2142f, 34.3584f, 92.2239f, 21.31f, 29.14f, -27.51f, -1.0f, -1.0f, -1.0f},
    {244.5244f, 193.8503f, 0.0f, 80.57f, 3.85f, 89.61f, -1.0f, -1.0f, -1.0f},
    {117.8475f, 118.2889f, 118.1270f, 49.71f, -0.20f, 0.03f, -1.0f, -1.0f, -1.0f},
    {145.0665f, 145.2389f, 145.9725f, 60.27f, 0.08f, -0.41f, -1.0f, -1.0f, -1.0f},
    {199.9555f, 153.8996f, 134.3363f, 67.34f, 14.45f, 16.90f, -1.0f, -1.0f, -1.0f},
    {197.1786f, 145.1237f, 124.6796f, 64.69f, 16.95f, 18.57f, -1.0f, -1.0f, -1.0f},
    {36.6899f, 140.3283f, 37.0131f, 51.12f, -49.31f, 44.41f, -1.0f, -1.0f, -1.0f},
    {117.8144f, 118.2644f, 118.1186f, 49.7f, -0.20f, 0.02f, -1.0f, -1.0f, -1.0f},
    {20.2541f, 20.8349f, 21.6861f, 6.67f, -0.05f, -0.64f, -1.0f, -1.0f, -1.0f},
    {112.6726f, 119.9236f, 168.6891f, 51.56f, 9.16f, -26.88f, -1.0f, -1.0f, -1.0f},
    {163.7979f, 183.1273f, 39.4915f, 70.83f, -24.26f, 64.77f, -1.0f, -1.0f, -1.0f},
    {187.9194f, 68.6530f, 141.7077f, 48.06f, 55.33f, -15.61f, -1.0f, -1.0f, -1.0f},
    {82.5699f, 82.9620f, 83.2778f, 35.26f, -0.09f, -0.24f, -1.0f, -1.0f, -1.0f},
    {185.3466f, 184.8995f, 185.4040f, 75.16f, 0.25f, -0.20f, -1.0f, -1.0f, -1.0f},
    {158.9114f, 86.2593f, 40.1532f, 44.54f, 26.27f, 38.93f, -1.0f, -1.0f, -1.0f},
    {119.8508f, 73.3212f, 42.4862f, 35.91f, 16.59f, 26.46f, -1.0f, -1.0f, -1.0f},
    {60.8859f, 169.4042f, 57.1272f, 61.49f, -52.73f, 47.30f, -1.0f, -1.0f, -1.0f},
    {20.1742f, 20.6528f, 21.3198f, 6.59f, -0.05f, -0.50f, -1.0f, -1.0f, -1.0f},
    {243.8216f, 245.6282f, 243.9185f, 96.58f, -0.90f, 0.61f, -1.0f, -1.0f, -1.0f},
    {79.7809f, 185.0914f, 167.7500f, 68.93f, -34.58f, -0.34f, -1.0f, -1.0f, -1.0f},
    {232.6743f, 153.8163f, 0.0f, 69.65f, 20.09f, 78.57f, -1.0f, -1.0f, -1.0f},
    {0.0f, 130.0449f, 163.4567f, 47.79f, -33.18f, -30.21f, -1.0f, -1.0f, -1.0f},
    {38.1665f, 39.8882f, 41.3055f, 15.94f, -0.42f, -1.20f, -1.0f, -1.0f, -1.0f},
    {222.3959f, 223.8081f, 224.4490f, 89.02f, -0.36f, -0.48f, -1.0f, -1.0f, -1.0f},
    {209.4225f, 135.2473f, 108.2643f, 63.43f, 25.44f, 26.25f, -1.0f, -1.0f, -1.0f},
    {211.8297f, 143.7736f, 111.0546f, 65.75f, 22.06f, 27.82f, -1.0f, -1.0f, -1.0f},
    {198.7094f, 135.2492f, 55.8580f, 61.47f, 17.10f, 50.72f, -1.0f, -1.0f, -1.0f},
    {243.7438f, 245.4744f, 243.6791f, 96.53f, -0.89f, 0.66f, -1.0f, -1.0f, -1.0f},
    {118.0486f, 118.4902f, 118.3282f, 49.79f, -0.20f, 0.03f, -1.0f, -1.0f, -1.0f},
    {245.4618f, 204.6880f, 180.8277f, 85.17f, 10.89f, 17.26f, -1.0f, -1.0f, -1.0f},
    {196.2073f, 234.5558f, 213.2861f, 89.74f, -16.52f, 6.19f, -1.0f, -1.0f, -1.0f},
    {215.5873f, 208.3601f, 222.5759f, 84.55f, 5.07f, -6.12f, -1.0f, -1.0f, -1.0f},
    {169.8225f, 217.9657f, 225.3475f, 84.02f, -13.87f, -8.72f, -1.0f, -1.0f, -1.0f},
    {172.9317f, 173.0792f, 173.7270f, 70.76f, 0.07f, -0.35f, -1.0f, -1.0f, -1.0f},
    {107.9946f, 107.9158f, 107.5227f, 45.59f, -0.05f, 0.23f, -1.0f, -1.0f, -1.0f},
    {48.8294f, 48.9265f, 49.4057f, 20.3f, 0.07f, -0.32f, -1.0f, -1.0f, -1.0f},
    {154.4353f, 153.9054f, 41.5640f, 61.79f, -13.41f, 55.42f, -1.0f, -1.0f, -1.0f},
    {117.8827f, 118.3094f, 118.1692f, 49.72f, -0.19f, 0.02f, -1.0f, -1.0f, -1.0f},
    {20.6133f, 21.0396f, 21.6157f, 6.77f, -0.05f, -0.44f, -1.0f, -1.0f, -1.0f},
    {98.5619f, 24.5454f, 42.2740f, 21.85f, 34.37f, 7.83f, -1.0f, -1.0f, -1.0f},
    {203.5372f, 4.2888f, 23.0539f, 42.66f, 67.43f, 48.42f, -1.0f, -1.0f, -1.0f},
    {206.9131f, 119.5498f, 140.5764f, 60.33f, 36.56f, 3.56f, -1.0f, -1.0f, -1.0f},
    {215.5830f, 120.8032f, 119.0661f, 61.22f, 36.61f, 17.32f, -1.0f, -1.0f, -1.0f},
    {251.8782f, 103.8825f, 0.0f, 62.07f, 52.80f, 77.14f, -1.0f, -1.0f, -1.0f},
    {197.8463f, 179.7436f, 0.0f, 72.42f, -9.82f, 89.66f, -1.0f, -1.0f, -1.0f},
    {182.0047f, 145.2731f, 40.3520f, 62.03f, 3.53f, 57.01f, -1.0f, -1.0f, -1.0f},
    {163.8131f, 187.4426f, 0.0f, 71.95f, -27.34f, 73.69f, -1.0f, -1.0f, -1.0f},
    {20.2251f, 20.6453f, 21.2489f, 6.59f, -0.04f, -0.45f, -1.0f, -1.0f, -1.0f},
    {118.0243f, 118.4338f, 118.2614f, 49.77f, -0.19f, 0.04f, -1.0f, -1.0f, -1.0f},
    {188.2188f, 31.6548f, 85.2439f, 41.84f, 62.05f, 10.01f, -1.0f, -1.0f, -1.0f},
    {81.8360f, 27.8344f, 59.8794f, 19.78f, 29.16f, -7.85f, -1.0f, -1.0f, -1.0f},
    {190.3670f, 0.0f, 42.9164f, 39.56f, 65.98f, 33.71f, -1.0f, -1.0f, -1.0f},
    {236.3141f, 51.3514f, 46.5787f, 52.39f, 68.33f, 47.84f, -1.0f, -1.0f, -1.0f},
    {276.7517f, 181.6850f, 0.0f, 81.23f, 24.12f, 87.51f, -1.0f, -1.0f, -1.0f},
    {253.7462f, 195.2511f, 0.0f, 81.8f, 6.78f, 95.75f, -1.0f, -1.0f, -1.0f},
    {183.1389f, 181.4049f, 0.0f, 71.72f, -16.23f, 76.28f, -1.0f, -1.0f, -1.0f},
    {74.5051f, 40.0398f, 25.0285f, 20.31f, 14.45f, 16.74f, -1.0f, -1.0f, -1.0f},
    {117.8060f, 118.2068f, 118.0183f, 49.68f, -0.19f, 0.05f, -1.0f, -1.0f, -1.0f},
    {243.6388f, 245.3229f, 243.4973f, 96.48f, -0.88f, 0.68f, -1.0f, -1.0f, -1.0f},
    {117.8333f, 118.2279f, 118.0773f, 49.69f, -0.18f, 0.03f, -1.0f, -1.0f, -1.0f},
    {19.8618f, 20.1973f, 20.6441f, 6.39f, -0.04f, -0.33f, -1.0f, -1.0f, -1.0f},
    {243.7610f, 245.5085f, 243.6882f, 96.54f, -0.90f, 0.67f, -1.0f, -1.0f, -1.0f},
    {117.9245f, 118.3020f, 118.1192f, 49.72f, -0.18f, 0.05f, -1.0f, -1.0f, -1.0f},
    {20.0461f, 20.4187f, 20.9774f, 6.49f, -0.03f, -0.41f, -1.0f, -1.0f, -1.0f},
    {243.6929f, 245.4207f, 243.5636f, 96.51f, -0.90f, 0.69f, -1.0f, -1.0f, -1.0f},
    {117.8721f, 118.2558f, 118.0350f, 49.7f, -0.19f, 0.07f, -1.0f, -1.0f, -1.0f},
    {20.0704f, 20.3587f, 20.8917f, 6.47f, 0.00f, -0.38f, -1.0f, -1.0f, -1.0f},
    {243.5788f, 245.2700f, 243.4010f, 96.46f, -0.89f, 0.7f, -1.0f, -1.0f, -1.0f},

};

const cv::Point2f CChartDigitalSGCellchart[560] = {

    {0.25f, 0.25f},
    {2.75f, 0.25f},
    {2.75f, 2.75f},
    {0.25f, 2.75f},
    {3.0f, 0.25f},
    {5.5f, 0.25f},
    {5.5f, 2.75f},
    {3.0f, 2.75f},
    {5.75f, 0.25f},
    {8.25f, 0.25f},
    {8.25f, 2.75f},
    {5.75f, 2.75f},
    {8.5f, 0.25f},
    {11.0f, 0.25f},
    {11.0f, 2.75f},
    {8.5f, 2.75f},
    {11.25f, 0.25f},
    {13.75f, 0.25f},
    {13.75f, 2.75f},
    {11.25f, 2.75f},
    {14.0f, 0.25f},
    {16.5f, 0.25f},
    {16.5f, 2.75f},
    {14.0f, 2.75f},
    {16.75f, 0.25f},
    {19.25f, 0.25f},
    {19.25f, 2.75f},
    {16.75f, 2.75f},
    {19.5f, 0.25f},
    {22.0f, 0.25f},
    {22.0f, 2.75f},
    {19.5f, 2.75f},
    {22.25f, 0.25f},
    {24.75f, 0.25f},
    {24.75f, 2.75f},
    {22.25f, 2.75f},
    {25.0f, 0.25f},
    {27.5f, 0.25f},
    {27.5f, 2.75f},
    {25.0f, 2.75f},
    {27.75f, 0.25f},
    {30.25f, 0.25f},
    {30.25f, 2.75f},
    {27.75f, 2.75f},
    {30.5f, 0.25f},
    {33.0f, 0.25f},
    {33.0f, 2.75f},
    {30.5f, 2.75f},
    {33.25f, 0.25f},
    {35.75f, 0.25f},
    {35.75f, 2.75f},
    {33.25f, 2.75f},
    {36.0f, 0.25f},
    {38.5f, 0.25f},
    {38.5f, 2.75f},
    {36.0f, 2.75f},
    {0.25f, 3.0f},
    {2.75f, 3.0f},
    {2.75f, 5.5f},
    {0.25f, 5.5f},
    {3.0f, 3.0f},
    {5.5f, 3.0f},
    {5.5f, 5.5f},
    {3.0f, 5.5f},
    {5.75f, 3.0f},
    {8.25f, 3.0f},
    {8.25f, 5.5f},
    {5.75f, 5.5f},
    {8.5f, 3.0f},
    {11.0f, 3.0f},
    {11.0f, 5.5f},
    {8.5f, 5.5f},
    {11.25f, 3.0f},
    {13.75f, 3.0f},
    {13.75f, 5.5f},
    {11.25f, 5.5f},
    {14.0f, 3.0f},
    {16.5f, 3.0f},
    {16.5f, 5.5f},
    {14.0f, 5.5f},
    {16.75f, 3.0f},
    {19.25f, 3.0f},
    {19.25f, 5.5f},
    {16.75f, 5.5f},
    {19.5f, 3.0f},
    {22.0f, 3.0f},
    {22.0f, 5.5f},
    {19.5f, 5.5f},
    {22.25f, 3.0f},
    {24.75f, 3.0f},
    {24.75f, 5.5f},
    {22.25f, 5.5f},
    {25.0f, 3.0f},
    {27.5f, 3.0f},
    {27.5f, 5.5f},
    {25.0f, 5.5f},
    {27.75f, 3.0f},
    {30.25f, 3.0f},
    {30.25f, 5.5f},
    {27.75f, 5.5f},
    {30.5f, 3.0f},
    {33.0f, 3.0f},
    {33.0f, 5.5f},
    {30.5f, 5.5f},
    {33.25f, 3.0f},
    {35.75f, 3.0f},
    {35.75f, 5.5f},
    {33.25f, 5.5f},
    {36.0f, 3.0f},
    {38.5f, 3.0f},
    {38.5f, 5.5f},
    {36.0f, 5.5f},
    {0.25f, 5.75f},
    {2.75f, 5.75f},
    {2.75f, 8.25f},
    {0.25f, 8.25f},
    {3.0f, 5.75f},
    {5.5f, 5.75f},
    {5.5f, 8.25f},
    {3.0f, 8.25f},
    {5.75f, 5.75f},
    {8.25f, 5.75f},
    {8.25f, 8.25f},
    {5.75f, 8.25f},
    {8.5f, 5.75f},
    {11.0f, 5.75f},
    {11.0f, 8.25f},
    {8.5f, 8.25f},
    {11.25f, 5.75f},
    {13.75f, 5.75f},
    {13.75f, 8.25f},
    {11.25f, 8.25f},
    {14.0f, 5.75f},
    {16.5f, 5.75f},
    {16.5f, 8.25f},
    {14.0f, 8.25f},
    {16.75f, 5.75f},
    {19.25f, 5.75f},
    {19.25f, 8.25f},
    {16.75f, 8.25f},
    {19.5f, 5.75f},
    {22.0f, 5.75f},
    {22.0f, 8.25f},
    {19.5f, 8.25f},
    {22.25f, 5.75f},
    {24.75f, 5.75f},
    {24.75f, 8.25f},
    {22.25f, 8.25f},
    {25.0f, 5.75f},
    {27.5f, 5.75f},
    {27.5f, 8.25f},
    {25.0f, 8.25f},
    {27.75f, 5.75f},
    {30.25f, 5.75f},
    {30.25f, 8.25f},
    {27.75f, 8.25f},
    {30.5f, 5.75f},
    {33.0f, 5.75f},
    {33.0f, 8.25f},
    {30.5f, 8.25f},
    {33.25f, 5.75f},
    {35.75f, 5.75f},
    {35.75f, 8.25f},
    {33.25f, 8.25f},
    {36.0f, 5.75f},
    {38.5f, 5.75f},
    {38.5f, 8.25f},
    {36.0f, 8.25f},
    {0.25f, 8.5f},
    {2.75f, 8.5f},
    {2.75f, 11.0f},
    {0.25f, 11.0f},
    {3.0f, 8.5f},
    {5.5f, 8.5f},
    {5.5f, 11.0f},
    {3.0f, 11.0f},
    {5.75f, 8.5f},
    {8.25f, 8.5f},
    {8.25f, 11.0f},
    {5.75f, 11.0f},
    {8.5f, 8.5f},
    {11.0f, 8.5f},
    {11.0f, 11.0f},
    {8.5f, 11.0f},
    {11.25f, 8.5f},
    {13.75f, 8.5f},
    {13.75f, 11.0f},
    {11.25f, 11.0f},
    {14.0f, 8.5f},
    {16.5f, 8.5f},
    {16.5f, 11.0f},
    {14.0f, 11.0f},
    {16.75f, 8.5f},
    {19.25f, 8.5f},
    {19.25f, 11.0f},
    {16.75f, 11.0f},
    {19.5f, 8.5f},
    {22.0f, 8.5f},
    {22.0f, 11.0f},
    {19.5f, 11.0f},
    {22.25f, 8.5f},
    {24.75f, 8.5f},
    {24.75f, 11.0f},
    {22.25f, 11.0f},
    {25.0f, 8.5f},
    {27.5f, 8.5f},
    {27.5f, 11.0f},
    {25.0f, 11.0f},
    {27.75f, 8.5f},
    {30.25f, 8.5f},
    {30.25f, 11.0f},
    {27.75f, 11.0f},
    {30.5f, 8.5f},
    {33.0f, 8.5f},
    {33.0f, 11.0f},
    {30.5f, 11.0f},
    {33.25f, 8.5f},
    {35.75f, 8.5f},
    {35.75f, 11.0f},
    {33.25f, 11.0f},
    {36.0f, 8.5f},
    {38.5f, 8.5f},
    {38.5f, 11.0f},
    {36.0f, 11.0f},
    {0.25f, 11.25f},
    {2.75f, 11.25f},
    {2.75f, 13.75f},
    {0.25f, 13.75f},
    {3.0f, 11.25f},
    {5.5f, 11.25f},
    {5.5f, 13.75f},
    {3.0f, 13.75f},
    {5.75f, 11.25f},
    {8.25f, 11.25f},
    {8.25f, 13.75f},
    {5.75f, 13.75f},
    {8.5f, 11.25f},
    {11.0f, 11.25f},
    {11.0f, 13.75f},
    {8.5f, 13.75f},
    {11.25f, 11.25f},
    {13.75f, 11.25f},
    {13.75f, 13.75f},
    {11.25f, 13.75f},
    {14.0f, 11.25f},
    {16.5f, 11.25f},
    {16.5f, 13.75f},
    {14.0f, 13.75f},
    {16.75f, 11.25f},
    {19.25f, 11.25f},
    {19.25f, 13.75f},
    {16.75f, 13.75f},
    {19.5f, 11.25f},
    {22.0f, 11.25f},
    {22.0f, 13.75f},
    {19.5f, 13.75f},
    {22.25f, 11.25f},
    {24.75f, 11.25f},
    {24.75f, 13.75f},
    {22.25f, 13.75f},
    {25.0f, 11.25f},
    {27.5f, 11.25f},
    {27.5f, 13.75f},
    {25.0f, 13.75f},
    {27.75f, 11.25f},
    {30.25f, 11.25f},
    {30.25f, 13.75f},
    {27.75f, 13.75f},
    {30.5f, 11.25f},
    {33.0f, 11.25f},
    {33.0f, 13.75f},
    {30.5f, 13.75f},
    {33.25f, 11.25f},
    {35.75f, 11.25f},
    {35.75f, 13.75f},
    {33.25f, 13.75f},
    {36.0f, 11.25f},
    {38.5f, 11.25f},
    {38.5f, 13.75f},
    {36.0f, 13.75f},
    {0.25f, 14.0f},
    {2.75f, 14.0f},
    {2.75f, 16.5f},
    {0.25f, 16.5f},
    {3.0f, 14.0f},
    {5.5f, 14.0f},
    {5.5f, 16.5f},
    {3.0f, 16.5f},
    {5.75f, 14.0f},
    {8.25f, 14.0f},
    {8.25f, 16.5f},
    {5.75f, 16.5f},
    {8.5f, 14.0f},
    {11.0f, 14.0f},
    {11.0f, 16.5f},
    {8.5f, 16.5f},
    {11.25f, 14.0f},
    {13.75f, 14.0f},
    {13.75f, 16.5f},
    {11.25f, 16.5f},
    {14.0f, 14.0f},
    {16.5f, 14.0f},
    {16.5f, 16.5f},
    {14.0f, 16.5f},
    {16.75f, 14.0f},
    {19.25f, 14.0f},
    {19.25f, 16.5f},
    {16.75f, 16.5f},
    {19.5f, 14.0f},
    {22.0f, 14.0f},
    {22.0f, 16.5f},
    {19.5f, 16.5f},
    {22.25f, 14.0f},
    {24.75f, 14.0f},
    {24.75f, 16.5f},
    {22.25f, 16.5f},
    {25.0f, 14.0f},
    {27.5f, 14.0f},
    {27.5f, 16.5f},
    {25.0f, 16.5f},
    {27.75f, 14.0f},
    {30.25f, 14.0f},
    {30.25f, 16.5f},
    {27.75f, 16.5f},
    {30.5f, 14.0f},
    {33.0f, 14.0f},
    {33.0f, 16.5f},
    {30.5f, 16.5f},
    {33.25f, 14.0f},
    {35.75f, 14.0f},
    {35.75f, 16.5f},
    {33.25f, 16.5f},
    {36.0f, 14.0f},
    {38.5f, 14.0f},
    {38.5f, 16.5f},
    {36.0f, 16.5f},
    {0.25f, 16.75f},
    {2.75f, 16.75f},
    {2.75f, 19.25f},
    {0.25f, 19.25f},
    {3.0f, 16.75f},
    {5.5f, 16.75f},
    {5.5f, 19.25f},
    {3.0f, 19.25f},
    {5.75f, 16.75f},
    {8.25f, 16.75f},
    {8.25f, 19.25f},
    {5.75f, 19.25f},
    {8.5f, 16.75f},
    {11.0f, 16.75f},
    {11.0f, 19.25f},
    {8.5f, 19.25f},
    {11.25f, 16.75f},
    {13.75f, 16.75f},
    {13.75f, 19.25f},
    {11.25f, 19.25f},
    {14.0f, 16.75f},
    {16.5f, 16.75f},
    {16.5f, 19.25f},
    {14.0f, 19.25f},
    {16.75f, 16.75f},
    {19.25f, 16.75f},
    {19.25f, 19.25f},
    {16.75f, 19.25f},
    {19.5f, 16.75f},
    {22.0f, 16.75f},
    {22.0f, 19.25f},
    {19.5f, 19.25f},
    {22.25f, 16.75f},
    {24.75f, 16.75f},
    {24.75f, 19.25f},
    {22.25f, 19.25f},
    {25.0f, 16.75f},
    {27.5f, 16.75f},
    {27.5f, 19.25f},
    {25.0f, 19.25f},
    {27.75f, 16.75f},
    {30.25f, 16.75f},
    {30.25f, 19.25f},
    {27.75f, 19.25f},
    {30.5f, 16.75f},
    {33.0f, 16.75f},
    {33.0f, 19.25f},
    {30.5f, 19.25f},
    {33.25f, 16.75f},
    {35.75f, 16.75f},
    {35.75f, 19.25f},
    {33.25f, 19.25f},
    {36.0f, 16.75f},
    {38.5f, 16.75f},
    {38.5f, 19.25f},
    {36.0f, 19.25f},
    {0.25f, 19.5f},
    {2.75f, 19.5f},
    {2.75f, 22.0f},
    {0.25f, 22.0f},
    {3.0f, 19.5f},
    {5.5f, 19.5f},
    {5.5f, 22.0f},
    {3.0f, 22.0f},
    {5.75f, 19.5f},
    {8.25f, 19.5f},
    {8.25f, 22.0f},
    {5.75f, 22.0f},
    {8.5f, 19.5f},
    {11.0f, 19.5f},
    {11.0f, 22.0f},
    {8.5f, 22.0f},
    {11.25f, 19.5f},
    {13.75f, 19.5f},
    {13.75f, 22.0f},
    {11.25f, 22.0f},
    {14.0f, 19.5f},
    {16.5f, 19.5f},
    {16.5f, 22.0f},
    {14.0f, 22.0f},
    {16.75f, 19.5f},
    {19.25f, 19.5f},
    {19.25f, 22.0f},
    {16.75f, 22.0f},
    {19.5f, 19.5f},
    {22.0f, 19.5f},
    {22.0f, 22.0f},
    {19.5f, 22.0f},
    {22.25f, 19.5f},
    {24.75f, 19.5f},
    {24.75f, 22.0f},
    {22.25f, 22.0f},
    {25.0f, 19.5f},
    {27.5f, 19.5f},
    {27.5f, 22.0f},
    {25.0f, 22.0f},
    {27.75f, 19.5f},
    {30.25f, 19.5f},
    {30.25f, 22.0f},
    {27.75f, 22.0f},
    {30.5f, 19.5f},
    {33.0f, 19.5f},
    {33.0f, 22.0f},
    {30.5f, 22.0f},
    {33.25f, 19.5f},
    {35.75f, 19.5f},
    {35.75f, 22.0f},
    {33.25f, 22.0f},
    {36.0f, 19.5f},
    {38.5f, 19.5f},
    {38.5f, 22.0f},
    {36.0f, 22.0f},
    {0.25f, 22.25f},
    {2.75f, 22.25f},
    {2.75f, 24.75f},
    {0.25f, 24.75f},
    {3.0f, 22.25f},
    {5.5f, 22.25f},
    {5.5f, 24.75f},
    {3.0f, 24.75f},
    {5.75f, 22.25f},
    {8.25f, 22.25f},
    {8.25f, 24.75f},
    {5.75f, 24.75f},
    {8.5f, 22.25f},
    {11.0f, 22.25f},
    {11.0f, 24.75f},
    {8.5f, 24.75f},
    {11.25f, 22.25f},
    {13.75f, 22.25f},
    {13.75f, 24.75f},
    {11.25f, 24.75f},
    {14.0f, 22.25f},
    {16.5f, 22.25f},
    {16.5f, 24.75f},
    {14.0f, 24.75f},
    {16.75f, 22.25f},
    {19.25f, 22.25f},
    {19.25f, 24.75f},
    {16.75f, 24.75f},
    {19.5f, 22.25f},
    {22.0f, 22.25f},
    {22.0f, 24.75f},
    {19.5f, 24.75f},
    {22.25f, 22.25f},
    {24.75f, 22.25f},
    {24.75f, 24.75f},
    {22.25f, 24.75f},
    {25.0f, 22.25f},
    {27.5f, 22.25f},
    {27.5f, 24.75f},
    {25.0f, 24.75f},
    {27.75f, 22.25f},
    {30.25f, 22.25f},
    {30.25f, 24.75f},
    {27.75f, 24.75f},
    {30.5f, 22.25f},
    {33.0f, 22.25f},
    {33.0f, 24.75f},
    {30.5f, 24.75f},
    {33.25f, 22.25f},
    {35.75f, 22.25f},
    {35.75f, 24.75f},
    {33.25f, 24.75f},
    {36.0f, 22.25f},
    {38.5f, 22.25f},
    {38.5f, 24.75f},
    {36.0f, 24.75f},
    {0.25f, 25.0f},
    {2.75f, 25.0f},
    {2.75f, 27.5f},
    {0.25f, 27.5f},
    {3.0f, 25.0f},
    {5.5f, 25.0f},
    {5.5f, 27.5f},
    {3.0f, 27.5f},
    {5.75f, 25.0f},
    {8.25f, 25.0f},
    {8.25f, 27.5f},
    {5.75f, 27.5f},
    {8.5f, 25.0f},
    {11.0f, 25.0f},
    {11.0f, 27.5f},
    {8.5f, 27.5f},
    {11.25f, 25.0f},
    {13.75f, 25.0f},
    {13.75f, 27.5f},
    {11.25f, 27.5f},
    {14.0f, 25.0f},
    {16.5f, 25.0f},
    {16.5f, 27.5f},
    {14.0f, 27.5f},
    {16.75f, 25.0f},
    {19.25f, 25.0f},
    {19.25f, 27.5f},
    {16.75f, 27.5f},
    {19.5f, 25.0f},
    {22.0f, 25.0f},
    {22.0f, 27.5f},
    {19.5f, 27.5f},
    {22.25f, 25.0f},
    {24.75f, 25.0f},
    {24.75f, 27.5f},
    {22.25f, 27.5f},
    {25.0f, 25.0f},
    {27.5f, 25.0f},
    {27.5f, 27.5f},
    {25.0f, 27.5f},
    {27.75f, 25.0f},
    {30.25f, 25.0f},
    {30.25f, 27.5f},
    {27.75f, 27.5f},
    {30.5f, 25.0f},
    {33.0f, 25.0f},
    {33.0f, 27.5f},
    {30.5f, 27.5f},
    {33.25f, 25.0f},
    {35.75f, 25.0f},
    {35.75f, 27.5f},
    {33.25f, 27.5f},
    {36.0f, 25.0f},
    {38.5f, 25.0f},
    {38.5f, 27.5f},
    {36.0f, 27.5f}

};

const cv::Point2f CChartDigitalSGCenter[140] = {
    {1.5f, 1.5f},
    {4.25f, 1.5f},
    {7.0f, 1.5f},
    {9.75f, 1.5f},
    {12.5f, 1.5f},
    {15.25f, 1.5f},
    {18.0f, 1.5f},
    {20.75f, 1.5f},
    {23.5f, 1.5f},
    {26.25f, 1.5f},
    {29.0f, 1.5f},
    {31.75f, 1.5f},
    {34.5f, 1.5f},
    {37.25f, 1.5f},
    {1.5f, 4.25f},
    {4.25f, 4.25f},
    {7.0f, 4.25f},
    {9.75f, 4.25f},
    {12.5f, 4.25f},
    {15.25f, 4.25f},
    {18.0f, 4.25f},
    {20.75f, 4.25f},
    {23.5f, 4.25f},
    {26.25f, 4.25f},
    {29.0f, 4.25f},
    {31.75f, 4.25f},
    {34.5f, 4.25f},
    {37.25f, 4.25f},
    {1.5f, 7.0f},
    {4.25f, 7.0f},
    {7.0f, 7.0f},
    {9.75f, 7.0f},
    {12.5f, 7.0f},
    {15.25f, 7.0f},
    {18.0f, 7.0f},
    {20.75f, 7.0f},
    {23.5f, 7.0f},
    {26.25f, 7.0f},
    {29.0f, 7.0f},
    {31.75f, 7.0f},
    {34.5f, 7.0f},
    {37.25f, 7.0f},
    {1.5f, 9.75f},
    {4.25f, 9.75f},
    {7.0f, 9.75f},
    {9.75f, 9.75f},
    {12.5f, 9.75f},
    {15.25f, 9.75f},
    {18.0f, 9.75f},
    {20.75f, 9.75f},
    {23.5f, 9.75f},
    {26.25f, 9.75f},
    {29.0f, 9.75f},
    {31.75f, 9.75f},
    {34.5f, 9.75f},
    {37.25f, 9.75f},
    {1.5f, 12.5f},
    {4.25f, 12.5f},
    {7.0f, 12.5f},
    {9.75f, 12.5f},
    {12.5f, 12.5f},
    {15.25f, 12.5f},
    {18.0f, 12.5f},
    {20.75f, 12.5f},
    {23.5f, 12.5f},
    {26.25f, 12.5f},
    {29.0f, 12.5f},
    {31.75f, 12.5f},
    {34.5f, 12.5f},
    {37.25f, 12.5f},
    {1.5f, 15.25f},
    {4.25f, 15.25f},
    {7.0f, 15.25f},
    {9.75f, 15.25f},
    {12.5f, 15.25f},
    {15.25f, 15.25f},
    {18.0f, 15.25f},
    {20.75f, 15.25f},
    {23.5f, 15.25f},
    {26.25f, 15.25f},
    {29.0f, 15.25f},
    {31.75f, 15.25f},
    {34.5f, 15.25f},
    {37.25f, 15.25f},
    {1.5f, 18.0f},
    {4.25f, 18.0f},
    {7.0f, 18.0f},
    {9.75f, 18.0f},
    {12.5f, 18.0f},
    {15.25f, 18.0f},
    {18.0f, 18.0f},
    {20.75f, 18.0f},
    {23.5f, 18.0f},
    {26.25f, 18.0f},
    {29.0f, 18.0f},
    {31.75f, 18.0f},
    {34.5f, 18.0f},
    {37.25f, 18.0f},
    {1.5f, 20.75f},
    {4.25f, 20.75f},
    {7.0f, 20.75f},
    {9.75f, 20.75f},
    {12.5f, 20.75f},
    {15.25f, 20.75f},
    {18.0f, 20.75f},
    {20.75f, 20.75f},
    {23.5f, 20.75f},
    {26.25f, 20.75f},
    {29.0f, 20.75f},
    {31.75f, 20.75f},
    {34.5f, 20.75f},
    {37.25f, 20.75f},
    {1.5f, 23.5f},
    {4.25f, 23.5f},
    {7.0f, 23.5f},
    {9.75f, 23.5f},
    {12.5f, 23.5f},
    {15.25f, 23.5f},
    {18.0f, 23.5f},
    {20.75f, 23.5f},
    {23.5f, 23.5f},
    {26.25f, 23.5f},
    {29.0f, 23.5f},
    {31.75f, 23.5f},
    {34.5f, 23.5f},
    {37.25f, 23.5f},
    {1.5f, 26.25f},
    {4.25f, 26.25f},
    {7.0f, 26.25f},
    {9.75f, 26.25f},
    {12.5f, 26.25f},
    {15.25f, 26.25f},
    {18.0f, 26.25f},
    {20.75f, 26.25f},
    {23.5f, 26.25f},
    {26.25f, 26.25f},
    {29.0f, 26.25f},
    {31.75f, 26.25f},
    {34.5f, 26.25f},
    {37.25f, 26.25f},

};

//////////////////////////////////////////////////////////////////////////////////////////////
/// CChartVinyl

const float CChartVinylColors[18][9] = {
    //       sRGB              CIE L*a*b*             Munsell Notation
    // ---------------  ------------------------     Hue Value / Chroma
    // R     G      B        L*      a*       b*
    {255.0f, 255.0f, 255.0f, 100.0f, 0.0052f, -0.0104f, -1.0f, -1.0f, -1.0f},
    {176.0f, 180.0f, 183.0f, 73.0834f, -0.820f, -2.021f, -1.0f, -1.0f, -1.0f},
    {150.0f, 151.0f, 155.0f, 62.493f, 0.426f, -2.231f, -1.0f, -1.0f, -1.0f},
    {119.0f, 120.0f, 124.0f, 50.464f, 0.447f, -2.324f, -1.0f, -1.0f, -1.0f},
    {88.0f, 89.0f, 91.0f, 37.797f, 0.036f, -1.297f, -1.0f, -1.0f, -1.0f},
    {0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, -1.0f, -1.0f, -1.0f},
    {237.0f, 27.0f, 36.0f, 51.588f, 73.518f, 51.569f, -1.0f, -1.0f, -1.0f},
    {254.0f, 242.0f, 0.0f, 93.699f, -15.734f, 91.942f, -1.0f, -1.0f, -1.0f},
    {106.0f, 189.0f, 71.0f, 69.408f, -46.594f, 50.487f, -1.0f, -1.0f, -1.0f},
    {0.0f, 173.0f, 239.0f, 66.610f, -13.679f, -43.172f, -1.0f, -1.0f, -1.0f},
    {0.0f, 26.0f, 83.0f, 11.711f, 16.980f, -37.176f, -1.0f, -1.0f, -1.0f},
    {238.0f, 1.0f, 141.0f, 51.974f, 81.944f, -8.407f, -1.0f, -1.0f, -1.0f},
    {174.0f, 50.0f, 58.0f, 40.549f, 50.440f, 24.849f, -1.0f, -1.0f, -1.0f},
    {209.0f, 127.0f, 41.0f, 60.816f, 26.069f, 49.442f, -1.0f, -1.0f, -1.0f},
    {7.0f, 136.0f, 165.0f, 52.253f, -19.950f, -23.996f, -1.0f, -1.0f, -1.0f},
    {188.0f, 86.0f, 149.0f, 51.286f, 48.470f, -15.058f, -1.0f, -1.0f, -1.0f},
    {200.0f, 159.0f, 139.0f, 68.707f, 12.296f, 16.213f, -1.0f, -1.0f, -1.0f},
    {183.0f, 147.0f, 125.0f, 63.684f, 10.293f, 16.764f, -1.0f, -1.0f, -1.0f},
};

const cv::Point2f CChartVinylCellchart[72] = {
    {0.25f, 0.25f},
    {3.0f, 0.25f},
    {3.0f, 6.25f},
    {0.25f, 6.25f},
    {3.25f, 0.25f},
    {6.0f, 0.25f},
    {6.0f, 6.25f},
    {3.25f, 6.25f},
    {6.25f, 0.25f},
    {9.0f, 0.25f},
    {9.0f, 6.25f},
    {6.25f, 6.25f},
    {9.25f, 0.25f},
    {12.0f, 0.25f},
    {12.0f, 6.25f},
    {9.25f, 6.25f},
    {12.25f, 0.25f},
    {15.0f, 0.25f},
    {15.0f, 6.25f},
    {12.25f, 6.25f},
    {15.25f, 0.25f},
    {18.0f, 0.25f},
    {18.0f, 6.25f},
    {15.25f, 6.25f},
    {0.25f, 6.5f},
    {3.0f, 6.5f},
    {3.0f, 9.25f},
    {0.25f, 9.25f},
    {3.25f, 6.5f},
    {6.0f, 6.5f},
    {6.0f, 9.25f},
    {3.25f, 9.25f},
    {6.25f, 6.5f},
    {9.0f, 6.5f},
    {9.0f, 9.25f},
    {6.25f, 9.25f},
    {9.25f, 6.5f},
    {12.0f, 6.5f},
    {12.0f, 9.25f},
    {9.25f, 9.25f},
    {12.25f, 6.5f},
    {15.0f, 6.5f},
    {15.0f, 9.25f},
    {12.25f, 9.25f},
    {15.25f, 6.5f},
    {18.0f, 6.5f},
    {18.0f, 9.25f},
    {15.25f, 9.25f},
    {0.25f, 9.5f},
    {3.0f, 9.5f},
    {3.0f, 12.25f},
    {0.25f, 12.25f},
    {3.25f, 9.5f},
    {6.0f, 9.5f},
    {6.0f, 12.25f},
    {3.25f, 12.25f},
    {6.25f, 9.5f},
    {9.0f, 9.5f},
    {9.0f, 12.25f},
    {6.25f, 12.25f},
    {9.25f, 9.5f},
    {12.0f, 9.5f},
    {12.0f, 12.25f},
    {9.25f, 12.25f},
    {12.25f, 9.5f},
    {15.0f, 9.5f},
    {15.0f, 12.25f},
    {12.25f, 12.25f},
    {15.25f, 9.5f},
    {18.0f, 9.5f},
    {18.0f, 12.25f},
    {15.25f, 12.25f},

};

const cv::Point2f CChartVinylCenter[18] = {
    {1.625f, 3.25f},
    {4.625f, 3.25f},
    {7.625f, 3.25f},
    {10.625f, 3.25f},
    {13.625f, 3.25f},
    {16.625f, 3.25f},
    {1.625f, 7.875f},
    {4.625f, 7.875f},
    {7.625f, 7.875f},
    {10.625f, 7.875f},
    {13.625f, 7.875f},
    {16.625f, 7.875f},
    {1.625f, 10.875f},
    {4.625f, 10.875f},
    {7.625f, 10.875f},
    {10.625f, 10.875f},
    {13.625f, 10.875f},
    {16.625f, 10.875f},

};

} // namespace mcc
} // namespace cv

#endif
