<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Standards conformance</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div><div class="header">
  <div class="headertitle"><div class="title">Standards conformance</div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#compat_x11">X11 extensions, protocols and IPC standards</a></li>
<li class="level1"><a href="#compat_wayland">Wayland protocols and IPC standards</a></li>
<li class="level1"><a href="#compat_glx">GLX extensions</a></li>
<li class="level1"><a href="#compat_wgl">WGL extensions</a></li>
<li class="level1"><a href="#compat_osx">OpenGL on macOS</a></li>
<li class="level1"><a href="#compat_vulkan">Vulkan loader and API</a></li>
<li class="level1"><a href="#compat_wsi">Vulkan WSI extensions</a></li>
</ul>
</div>
<div class="textblock"><p>This guide describes the various API extensions used by this version of GLFW. It lists what are essentially implementation details, but which are nonetheless vital knowledge for developers intending to deploy their applications on a wide range of machines.</p>
<p>The information in this guide is not a part of GLFW API, but merely preconditions for some parts of the library to function on a given machine. Any part of this information may change in future versions of GLFW and that will not be considered a breaking API change.</p>
<h1><a class="anchor" id="compat_x11"></a>
X11 extensions, protocols and IPC standards</h1>
<p>As GLFW uses Xlib directly, without any intervening toolkit library, it has sole responsibility for interacting well with the many and varied window managers in use on Unix-like systems. In order for applications and window managers to work well together, a number of standards and conventions have been developed that regulate behavior outside the scope of the X11 API; most importantly the <a href="https://www.tronche.com/gui/x/icccm/">Inter-Client Communication Conventions Manual</a> (ICCCM) and <a href="https://standards.freedesktop.org/wm-spec/wm-spec-latest.html">Extended Window Manager Hints</a> (EWMH) standards.</p>
<p>GLFW uses the <code>_MOTIF_WM_HINTS</code> window property to support borderless windows. If the running window manager does not support this property, the <code>GLFW_DECORATED</code> hint will have no effect.</p>
<p>GLFW uses the ICCCM <code>WM_DELETE_WINDOW</code> protocol to intercept the user attempting to close the GLFW window. If the running window manager does not support this protocol, the close callback will never be called.</p>
<p>GLFW uses the EWMH <code>_NET_WM_PING</code> protocol, allowing the window manager notify the user when the application has stopped responding, i.e. when it has ceased to process events. If the running window manager does not support this protocol, the user will not be notified if the application locks up.</p>
<p>GLFW uses the EWMH <code>_NET_WM_STATE_FULLSCREEN</code> window state to tell the window manager to make the GLFW window full screen. If the running window manager does not support this state, full screen windows may not work properly. GLFW has a fallback code path in case this state is unavailable, but every window manager behaves slightly differently in this regard.</p>
<p>GLFW uses the EWMH <code>_NET_WM_BYPASS_COMPOSITOR</code> window property to tell a compositing window manager to un-redirect full screen GLFW windows. If the running window manager uses compositing but does not support this property then additional copying may be performed for each buffer swap of full screen windows.</p>
<p>GLFW uses the <a href="https://www.freedesktop.org/wiki/ClipboardManager/">clipboard manager protocol</a> to push a clipboard string (i.e. selection) owned by a GLFW window about to be destroyed to the clipboard manager. If there is no running clipboard manager, the clipboard string will be unavailable once the window has been destroyed.</p>
<p>GLFW uses the <a href="https://www.freedesktop.org/wiki/Specifications/XDND/">X drag-and-drop protocol</a> to provide file drop events. If the application originating the drag does not support this protocol, drag and drop will not work.</p>
<p>GLFW uses the XRandR 1.3 extension to provide multi-monitor support. If the running X server does not support this version of this extension, multi-monitor support will not function and only a single, desktop-spanning monitor will be reported.</p>
<p>GLFW uses the XRandR 1.3 and Xf86vidmode extensions to provide gamma ramp support. If the running X server does not support either or both of these extensions, gamma ramp support will not function.</p>
<p>GLFW uses the Xkb extension and detectable auto-repeat to provide keyboard input. If the running X server does not support this extension, a non-Xkb fallback path is used.</p>
<p>GLFW uses the XInput2 extension to provide raw, non-accelerated mouse motion when the cursor is disabled. If the running X server does not support this extension, regular accelerated mouse motion will be used.</p>
<p>GLFW uses both the XRender extension and the compositing manager to support transparent window framebuffers. If the running X server does not support this extension or there is no running compositing manager, the <code>GLFW_TRANSPARENT_FRAMEBUFFER</code> framebuffer hint will have no effect.</p>
<p>GLFW uses both the Xcursor extension and the freedesktop cursor conventions to provide an expanded set of standard cursor shapes. If the running X server does not support this extension or the current cursor theme does not support the conventions, the <code>GLFW_RESIZE_NWSE_CURSOR</code>, <code>GLFW_RESIZE_NESW_CURSOR</code> and <code>GLFW_NOT_ALLOWED_CURSOR</code> shapes will not be available and other shapes may use legacy images.</p>
<h1><a class="anchor" id="compat_wayland"></a>
Wayland protocols and IPC standards</h1>
<p>As GLFW uses libwayland directly, without any intervening toolkit library, it has sole responsibility for interacting well with every compositor in use on Unix-like systems. Most of the features are provided by the core protocol, while cursor support is provided by the libwayland-cursor helper library, EGL integration by libwayland-egl, and keyboard handling by <a href="https://xkbcommon.org/">libxkbcommon</a>. In addition, GLFW uses some additional Wayland protocols to implement certain features if the compositor supports them.</p>
<p>GLFW uses xkbcommon 0.5.0 to provide key and text input support. Earlier versions are not supported.</p>
<p>GLFW uses the <a href="https://wayland.app/protocols/xdg-shell">xdg-shell</a> protocol to provide better window management. This protocol is mandatory for GLFW to display a window.</p>
<p>GLFW uses the <a href="https://wayland.app/protocols/relative-pointer-unstable-v1">relative-pointer-unstable-v1</a> protocol alongside the <a href="https://wayland.app/protocols/pointer-constraints-unstable-v1">pointer-constraints-unstable-v1</a> protocol to implement disabled cursor. If the running compositor does not support both of these protocols, disabling the cursor will have no effect.</p>
<p>GLFW uses the <a href="https://wayland.app/protocols/idle-inhibit-unstable-v1">idle-inhibit-unstable-v1</a> protocol to prohibit the screensaver from starting. If the running compositor does not support this protocol, the screensaver may start even for full screen windows.</p>
<p>GLFW uses the <a href="https://gitlab.freedesktop.org/libdecor/libdecor">libdecor</a> library for window decorations, where available. This in turn provides good quality client-side decorations (drawn by the application) on desktop systems that do not support server-side decorations (drawn by the window manager). On systems that do not provide either libdecor or xdg-decoration, very basic window decorations are provided. These do not include the window title or any caption buttons.</p>
<p>GLFW uses the <a href="https://wayland.app/protocols/xdg-decoration-unstable-v1">xdg-decoration-unstable-v1</a> protocol to request decorations to be drawn around its windows. This protocol is part of wayland-protocols 1.15, and mandatory at build time. If the running compositor does not support this protocol, a very simple frame will be drawn by GLFW itself, using the <a href="https://wayland.app/protocols/viewporter">viewporter</a> protocol alongside subsurfaces. If the running compositor does not support these protocols either, no decorations will be drawn around windows.</p>
<p>GLFW uses the <a href="https://wayland.app/protocols/xdg-activation-v1">xdg-activation-v1</a> protocol to implement window focus and attention requests. If the running compositor does not support this protocol, window focus and attention requests do nothing.</p>
<p>GLFW uses the <a href="https://wayland.app/protocols/fractional-scale-v1">fractional-scale-v1</a> protocol to implement fine-grained framebuffer scaling. If the running compositor does not support this protocol, the <a class="el" href="group__window.html#gaa5a9c6b4722670fd33d6e8a88f2e21bc">GLFW_SCALE_FRAMEBUFFER</a> window hint will only be able to scale the framebuffer by integer scales. This will typically be the smallest integer not less than the actual scale.</p>
<h1><a class="anchor" id="compat_glx"></a>
GLX extensions</h1>
<p>The GLX API is the default API used to create OpenGL contexts on Unix-like systems using the X Window System.</p>
<p>GLFW uses the GLX 1.3 <code>GLXFBConfig</code> functions to enumerate and select framebuffer pixel formats. If GLX 1.3 is not supported, <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a> will fail.</p>
<p>GLFW uses the <code>GLX_MESA_swap_control,</code> <code>GLX_EXT_swap_control</code> and <code>GLX_SGI_swap_control</code> extensions to provide vertical retrace synchronization (or <em>vsync</em>), in that order of preference. When none of these extensions are available, calling <a class="el" href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed">glfwSwapInterval</a> will have no effect.</p>
<p>GLFW uses the <code>GLX_ARB_multisample</code> extension to create contexts with multisampling anti-aliasing. Where this extension is unavailable, the <code>GLFW_SAMPLES</code> hint will have no effect.</p>
<p>GLFW uses the <code>GLX_ARB_create_context</code> extension when available, even when creating OpenGL contexts of version 2.1 and below. Where this extension is unavailable, the <code>GLFW_CONTEXT_VERSION_MAJOR</code> and <code>GLFW_CONTEXT_VERSION_MINOR</code> hints will only be partially supported, the <code>GLFW_CONTEXT_DEBUG</code> hint will have no effect, and setting the <code>GLFW_OPENGL_PROFILE</code> or <code>GLFW_OPENGL_FORWARD_COMPAT</code> hints to <code>GLFW_TRUE</code> will cause <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a> to fail.</p>
<p>GLFW uses the <code>GLX_ARB_create_context_profile</code> extension to provide support for context profiles. Where this extension is unavailable, setting the <code>GLFW_OPENGL_PROFILE</code> hint to anything but <code>GLFW_OPENGL_ANY_PROFILE</code>, or setting <code>GLFW_CLIENT_API</code> to anything but <code>GLFW_OPENGL_API</code> or <code>GLFW_NO_API</code> will cause <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a> to fail.</p>
<p>GLFW uses the <code>GLX_ARB_context_flush_control</code> extension to provide control over whether a context is flushed when it is released (made non-current). Where this extension is unavailable, the <code>GLFW_CONTEXT_RELEASE_BEHAVIOR</code> hint will have no effect and the context will always be flushed when released.</p>
<p>GLFW uses the <code>GLX_ARB_framebuffer_sRGB</code> and <code>GLX_EXT_framebuffer_sRGB</code> extensions to provide support for sRGB framebuffers. Where both of these extensions are unavailable, the <code>GLFW_SRGB_CAPABLE</code> hint will have no effect.</p>
<h1><a class="anchor" id="compat_wgl"></a>
WGL extensions</h1>
<p>The WGL API is used to create OpenGL contexts on Microsoft Windows and other implementations of the Win32 API, such as Wine.</p>
<p>GLFW uses either the <code>WGL_EXT_extension_string</code> or the <code>WGL_ARB_extension_string</code> extension to check for the presence of all other WGL extensions listed below. If both are available, the EXT one is preferred. If neither is available, no other extensions are used and many GLFW features related to context creation will have no effect or cause errors when used.</p>
<p>GLFW uses the <code>WGL_EXT_swap_control</code> extension to provide vertical retrace synchronization (or <em>vsync</em>). Where this extension is unavailable, calling <a class="el" href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed">glfwSwapInterval</a> will have no effect.</p>
<p>GLFW uses the <code>WGL_ARB_pixel_format</code> and <code>WGL_ARB_multisample</code> extensions to create contexts with multisampling anti-aliasing. Where these extensions are unavailable, the <code>GLFW_SAMPLES</code> hint will have no effect.</p>
<p>GLFW uses the <code>WGL_ARB_create_context</code> extension when available, even when creating OpenGL contexts of version 2.1 and below. Where this extension is unavailable, the <code>GLFW_CONTEXT_VERSION_MAJOR</code> and <code>GLFW_CONTEXT_VERSION_MINOR</code> hints will only be partially supported, the <code>GLFW_CONTEXT_DEBUG</code> hint will have no effect, and setting the <code>GLFW_OPENGL_PROFILE</code> or <code>GLFW_OPENGL_FORWARD_COMPAT</code> hints to <code>GLFW_TRUE</code> will cause <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a> to fail.</p>
<p>GLFW uses the <code>WGL_ARB_create_context_profile</code> extension to provide support for context profiles. Where this extension is unavailable, setting the <code>GLFW_OPENGL_PROFILE</code> hint to anything but <code>GLFW_OPENGL_ANY_PROFILE</code> will cause <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a> to fail.</p>
<p>GLFW uses the <code>WGL_ARB_context_flush_control</code> extension to provide control over whether a context is flushed when it is released (made non-current). Where this extension is unavailable, the <code>GLFW_CONTEXT_RELEASE_BEHAVIOR</code> hint will have no effect and the context will always be flushed when released.</p>
<p>GLFW uses the <code>WGL_ARB_framebuffer_sRGB</code> and <code>WGL_EXT_framebuffer_sRGB</code> extensions to provide support for sRGB framebuffers. When both of these extensions are unavailable, the <code>GLFW_SRGB_CAPABLE</code> hint will have no effect.</p>
<h1><a class="anchor" id="compat_osx"></a>
OpenGL on macOS</h1>
<p>Support for OpenGL 3.2 and above was introduced with OS X 10.7 and even then only forward-compatible, core profile contexts are supported. Support for OpenGL 4.1 was introduced with OS X 10.9, also limited to forward-compatible, core profile contexts. There is also still no mechanism for requesting debug contexts or no-error contexts. Versions of Mac OS X earlier than 10.7 support at most OpenGL version 2.1.</p>
<p>Because of this, on OS X 10.7 and later, the <code>GLFW_CONTEXT_VERSION_MAJOR</code> and <code>GLFW_CONTEXT_VERSION_MINOR</code> hints will cause <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a> to fail if given version 3.0 or 3.1. The <code>GLFW_OPENGL_PROFILE</code> hint must be set to <code>GLFW_OPENGL_CORE_PROFILE</code> when creating OpenGL 3.2 and later contexts. The <code>GLFW_CONTEXT_DEBUG</code> and <code>GLFW_CONTEXT_NO_ERROR</code> hints are ignored.</p>
<p>Also, on Mac OS X 10.6 and below, the <code>GLFW_CONTEXT_VERSION_MAJOR</code> and <code>GLFW_CONTEXT_VERSION_MINOR</code> hints will fail if given a version above 2.1, setting the <code>GLFW_OPENGL_PROFILE</code> or <code>GLFW_OPENGL_FORWARD_COMPAT</code> hints to a non-default value will cause <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a> to fail and the <code>GLFW_CONTEXT_DEBUG</code> hint is ignored.</p>
<h1><a class="anchor" id="compat_vulkan"></a>
Vulkan loader and API</h1>
<p>By default, GLFW uses the standard system-wide Vulkan loader to access the Vulkan API on all platforms except macOS. This is installed by both graphics drivers and Vulkan SDKs. If either the loader or at least one minimally functional ICD is missing, <a class="el" href="group__vulkan.html#ga2e7f30931e02464b5bc8d0d4b6f9fe2b">glfwVulkanSupported</a> will return <code>GLFW_FALSE</code> and all other Vulkan-related functions will fail with an <a class="el" href="group__errors.html#ga56882b290db23261cc6c053c40c2d08e">GLFW_API_UNAVAILABLE</a> error.</p>
<h1><a class="anchor" id="compat_wsi"></a>
Vulkan WSI extensions</h1>
<p>The Vulkan WSI extensions are used to create Vulkan surfaces for GLFW windows on all supported platforms.</p>
<p>GLFW uses the <code>VK_KHR_surface</code> and <code>VK_KHR_win32_surface</code> extensions to create surfaces on Microsoft Windows. If any of these extensions are not available, <a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a> will return an empty list and window surface creation will fail.</p>
<p>GLFW uses the <code>VK_KHR_surface</code> and either the <code>VK_MVK_macos_surface</code> or <code>VK_EXT_metal_surface</code> extensions to create surfaces on macOS. If any of these extensions are not available, <a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a> will return an empty list and window surface creation will fail.</p>
<p>GLFW uses the <code>VK_KHR_surface</code> and either the <code>VK_KHR_xlib_surface</code> or <code>VK_KHR_xcb_surface</code> extensions to create surfaces on X11. If <code>VK_KHR_surface</code> or both <code>VK_KHR_xlib_surface</code> and <code>VK_KHR_xcb_surface</code> are not available, <a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a> will return an empty list and window surface creation will fail.</p>
<p>GLFW uses the <code>VK_KHR_surface</code> and <code>VK_KHR_wayland_surface</code> extensions to create surfaces on Wayland. If any of these extensions are not available, <a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a> will return an empty list and window surface creation will fail. </p>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
