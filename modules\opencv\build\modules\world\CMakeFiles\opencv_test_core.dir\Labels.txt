# Target labels
 Main
 opencv_core
 AccuracyTest
# Source files and their labels
D:/AI/opencv/opencv-4.10.0/modules/core/test/ocl/test_arithm.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/ocl/test_channels.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/ocl/test_dft.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/ocl/test_gemm.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/ocl/test_image2d.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/ocl/test_matrix_expr.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/ocl/test_matrix_operation.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/ocl/test_opencl.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_arithm.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_async.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_concatenation.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_conjugate_gradient.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_countnonzero.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_cuda.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_downhill_simplex.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_ds.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_dxt.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_eigen.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_hal_core.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_hasnonzero.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_intrin.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_intrin_emulator.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_io.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_logtagconfigparser.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_logtagmanager.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_lpsolver.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_main.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_mat.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_math.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_misc.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_opencl.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_operations.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_ptr.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_quaternion.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_rand.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_rotatedrect.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_umat.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_utils.cpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/ref_reduce_arg.impl.hpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_intrin128.simd.hpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_intrin256.simd.hpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_intrin512.simd.hpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_intrin_utils.hpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_precomp.hpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/opencv-4.10.0/modules/core/test/test_utils_tls.impl.hpp
 Main
 opencv_core
 AccuracyTest
D:/AI/opencv/cudabuild/modules/world/opencv_test_core_SSE2.dir/$(Configuration)/test_intrin128.sse2.obj
D:/AI/opencv/cudabuild/modules/world/opencv_test_core_SSE3.dir/$(Configuration)/test_intrin128.sse3.obj
D:/AI/opencv/cudabuild/modules/world/opencv_test_core_SSE4_1.dir/$(Configuration)/test_intrin128.ssse3.obj
D:/AI/opencv/cudabuild/modules/world/opencv_test_core_SSE4_1.dir/$(Configuration)/test_intrin128.sse4_1.obj
D:/AI/opencv/cudabuild/modules/world/opencv_test_core_SSE4_2.dir/$(Configuration)/test_intrin128.sse4_2.obj
D:/AI/opencv/cudabuild/modules/world/opencv_test_core_FP16.dir/$(Configuration)/test_intrin128.fp16.obj
D:/AI/opencv/cudabuild/modules/world/opencv_test_core_AVX.dir/$(Configuration)/test_intrin128.avx.obj
D:/AI/opencv/cudabuild/modules/world/opencv_test_core_AVX2.dir/$(Configuration)/test_intrin128.avx2.obj
D:/AI/opencv/cudabuild/modules/world/opencv_test_core_AVX2.dir/$(Configuration)/test_intrin256.avx2.obj
D:/AI/opencv/cudabuild/modules/world/opencv_test_core_AVX512_SKX.dir/$(Configuration)/test_intrin128.avx512_skx.obj
D:/AI/opencv/cudabuild/modules/world/opencv_test_core_AVX512_SKX.dir/$(Configuration)/test_intrin256.avx512_skx.obj
D:/AI/opencv/cudabuild/modules/world/opencv_test_core_AVX512_SKX.dir/$(Configuration)/test_intrin512.avx512_skx.obj
