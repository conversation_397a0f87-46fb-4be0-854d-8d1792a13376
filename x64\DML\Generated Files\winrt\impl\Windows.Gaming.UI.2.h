// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Gaming_UI_2_H
#define WINRT_Windows_Gaming_UI_2_H
#include "winrt/impl/Windows.ApplicationModel.Activation.1.h"
#include "winrt/impl/Windows.Foundation.1.h"
#include "winrt/impl/Windows.Gaming.UI.1.h"
WINRT_EXPORT namespace winrt::Windows::Gaming::UI
{
    struct GameBar
    {
        GameBar() = delete;
        static auto VisibilityChanged(winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler);
        using VisibilityChanged_revoker = impl::factory_event_revoker<winrt::Windows::Gaming::UI::IGameBarStatics, &impl::abi_t<winrt::Windows::Gaming::UI::IGameBarStatics>::remove_VisibilityChanged>;
        [[nodiscard]] static auto VisibilityChanged(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler);
        static auto VisibilityChanged(winrt::event_token const& token);
        static auto IsInputRedirectedChanged(winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler);
        using IsInputRedirectedChanged_revoker = impl::factory_event_revoker<winrt::Windows::Gaming::UI::IGameBarStatics, &impl::abi_t<winrt::Windows::Gaming::UI::IGameBarStatics>::remove_IsInputRedirectedChanged>;
        [[nodiscard]] static auto IsInputRedirectedChanged(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler);
        static auto IsInputRedirectedChanged(winrt::event_token const& token);
        [[nodiscard]] static auto Visible();
        [[nodiscard]] static auto IsInputRedirected();
    };
    struct WINRT_IMPL_EMPTY_BASES GameChatMessageReceivedEventArgs : winrt::Windows::Gaming::UI::IGameChatMessageReceivedEventArgs
    {
        GameChatMessageReceivedEventArgs(std::nullptr_t) noexcept {}
        GameChatMessageReceivedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Gaming::UI::IGameChatMessageReceivedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES GameChatOverlay : winrt::Windows::Gaming::UI::IGameChatOverlay
    {
        GameChatOverlay(std::nullptr_t) noexcept {}
        GameChatOverlay(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Gaming::UI::IGameChatOverlay(ptr, take_ownership_from_abi) {}
        static auto GetDefault();
    };
    struct WINRT_IMPL_EMPTY_BASES GameChatOverlayMessageSource : winrt::Windows::Gaming::UI::IGameChatOverlayMessageSource
    {
        GameChatOverlayMessageSource(std::nullptr_t) noexcept {}
        GameChatOverlayMessageSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Gaming::UI::IGameChatOverlayMessageSource(ptr, take_ownership_from_abi) {}
        GameChatOverlayMessageSource();
    };
    struct WINRT_IMPL_EMPTY_BASES GameUIProviderActivatedEventArgs : winrt::Windows::Gaming::UI::IGameUIProviderActivatedEventArgs
    {
        GameUIProviderActivatedEventArgs(std::nullptr_t) noexcept {}
        GameUIProviderActivatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Gaming::UI::IGameUIProviderActivatedEventArgs(ptr, take_ownership_from_abi) {}
    };
}
#endif
