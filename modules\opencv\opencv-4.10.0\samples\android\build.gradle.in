// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {

    repositories {
        google()
        jcenter()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:@ANDROID_GRADLE_PLUGIN_VERSION@'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:@KOTLIN_PLUGIN_VERSION@'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        jcenter()
        if (gradle.opencv_source == "maven_local") {
            maven {
                url gradle.opencv_maven_path
            }
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

//allprojects {
//    gradle.projectsEvaluated {
//        tasks.withType(JavaCompile) {
//            options.compilerArgs << "-Xlint:unchecked"
//            options.compilerArgs << "-Xlint:deprecation"
//        }
//    }
//}

def opencv_strict_build_configuration = @ANDROID_STRICT_BUILD_CONFIGURATION@;

gradle.afterProject { project ->
    if (project.pluginManager.hasPlugin('com.android.application')
            || project.pluginManager.hasPlugin('com.android.library')
            || project.pluginManager.hasPlugin('com.android.test')
            || project.pluginManager.hasPlugin('com.android.feature') ) {
        if (true) {
            gradle.println("Override build ABIs for the project ${project.name}")
            project.android {
                splits {
                    abi {
                        enable true
                        universalApk false
                        @ANDROID_ABI_FILTER@
                    }
                }
            }
        }

        if (true) {
            gradle.println("Override lintOptions for the project ${project.name}")
            project.android {
                lintOptions {
                    // checkReleaseBuilds false
                    abortOnError false
                }
            }
        }

        // (you still need to re-build OpenCV with debug information to debug it)
        if (true) {
            gradle.println("Override doNotStrip-debug for the project ${project.name}")
            project.android {
                buildTypes {
                    debug {
                        packagingOptions {
                            doNotStrip '**/*.so'  // controlled by OpenCV CMake scripts
                        }
                    }
                }
            }
        }
        if (false || project.hasProperty("doNotStrip")) {
            gradle.println("Override doNotStrip-release for the project ${project.name}")
            project.android {
                buildTypes {
                    release {
                        packagingOptions {
                            doNotStrip '**/*.so'  // controlled by OpenCV CMake scripts
                        }
                    }
                }
            }
        }

        // Android Gradle Plugin (AGP) 3.5+ is required
        // https://github.com/android/ndk-samples/wiki/Configure-NDK-Path
        def isNdkVersionSupported = project.android.metaClass.getProperties().find { it.name == 'ndkVersion' } != null
        if ((false || opencv_strict_build_configuration) && isNdkVersionSupported) {
            gradle.println("Override ndkVersion for the project ${project.name}")
            project.android {
                ndkVersion '@ANDROID_NDK_REVISION@'
            }
        }
    }
}
