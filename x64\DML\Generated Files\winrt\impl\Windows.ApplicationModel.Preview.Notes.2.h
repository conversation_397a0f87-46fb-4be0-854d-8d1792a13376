// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_ApplicationModel_Preview_Notes_2_H
#define WINRT_Windows_ApplicationModel_Preview_Notes_2_H
#include "winrt/impl/Windows.ApplicationModel.Preview.Notes.1.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Preview::Notes
{
    struct WINRT_IMPL_EMPTY_BASES NotePlacementChangedPreviewEventArgs : winrt::Windows::ApplicationModel::Preview::Notes::INotePlacementChangedPreviewEventArgs
    {
        NotePlacementChangedPreviewEventArgs(std::nullptr_t) noexcept {}
        NotePlacementChangedPreviewEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Preview::Notes::INotePlacementChangedPreviewEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES NoteVisibilityChangedPreviewEventArgs : winrt::Windows::ApplicationModel::Preview::Notes::INoteVisibilityChangedPreviewEventArgs
    {
        NoteVisibilityChangedPreviewEventArgs(std::nullptr_t) noexcept {}
        NoteVisibilityChangedPreviewEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Preview::Notes::INoteVisibilityChangedPreviewEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES NotesWindowManagerPreview : winrt::Windows::ApplicationModel::Preview::Notes::INotesWindowManagerPreview,
        impl::require<NotesWindowManagerPreview, winrt::Windows::ApplicationModel::Preview::Notes::INotesWindowManagerPreview2>
    {
        NotesWindowManagerPreview(std::nullptr_t) noexcept {}
        NotesWindowManagerPreview(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Preview::Notes::INotesWindowManagerPreview(ptr, take_ownership_from_abi) {}
        using winrt::Windows::ApplicationModel::Preview::Notes::INotesWindowManagerPreview::ShowNoteRelativeTo;
        using impl::consume_t<NotesWindowManagerPreview, winrt::Windows::ApplicationModel::Preview::Notes::INotesWindowManagerPreview2>::ShowNoteRelativeTo;
        using winrt::Windows::ApplicationModel::Preview::Notes::INotesWindowManagerPreview::ShowNoteWithPlacement;
        using impl::consume_t<NotesWindowManagerPreview, winrt::Windows::ApplicationModel::Preview::Notes::INotesWindowManagerPreview2>::ShowNoteWithPlacement;
        static auto GetForCurrentApp();
    };
    struct WINRT_IMPL_EMPTY_BASES NotesWindowManagerPreviewShowNoteOptions : winrt::Windows::ApplicationModel::Preview::Notes::INotesWindowManagerPreviewShowNoteOptions
    {
        NotesWindowManagerPreviewShowNoteOptions(std::nullptr_t) noexcept {}
        NotesWindowManagerPreviewShowNoteOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Preview::Notes::INotesWindowManagerPreviewShowNoteOptions(ptr, take_ownership_from_abi) {}
        NotesWindowManagerPreviewShowNoteOptions();
    };
}
#endif
