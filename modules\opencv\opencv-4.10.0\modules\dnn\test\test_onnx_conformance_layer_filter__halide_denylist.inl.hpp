"test_abs",
"test_add",
"test_add_bcast",
"test_add_uint8",
"test_argmax_default_axis_example",
"test_argmax_default_axis_example_select_last_index",
"test_argmax_default_axis_random",
"test_argmax_default_axis_random_select_last_index",
"test_argmax_keepdims_example",
"test_argmax_keepdims_example_select_last_index",
"test_argmax_keepdims_random",
"test_argmax_keepdims_random_select_last_index",
"test_argmax_negative_axis_keepdims_example",
"test_argmax_negative_axis_keepdims_example_select_last_index",
"test_argmax_negative_axis_keepdims_random",
"test_argmax_negative_axis_keepdims_random_select_last_index",
"test_argmax_no_keepdims_example",
"test_argmax_no_keepdims_example_select_last_index",
"test_argmax_no_keepdims_random",
"test_argmax_no_keepdims_random_select_last_index",
"test_argmin_default_axis_example",
"test_argmin_default_axis_example_select_last_index",
"test_argmin_default_axis_random",
"test_argmin_default_axis_random_select_last_index",
"test_argmin_keepdims_example",
"test_argmin_keepdims_example_select_last_index",
"test_argmin_keepdims_random",
"test_argmin_keepdims_random_select_last_index",
"test_argmin_negative_axis_keepdims_example",
"test_argmin_negative_axis_keepdims_example_select_last_index",
"test_argmin_negative_axis_keepdims_random",
"test_argmin_negative_axis_keepdims_random_select_last_index",
"test_argmin_no_keepdims_example",
"test_argmin_no_keepdims_example_select_last_index",
"test_argmin_no_keepdims_random",
"test_argmin_no_keepdims_random_select_last_index",
"test_averagepool_2d_ceil",
"test_averagepool_2d_pads_count_include_pad",
"test_averagepool_2d_precomputed_pads_count_include_pad",
"test_averagepool_2d_precomputed_strides",
"test_averagepool_2d_same_lower",
"test_averagepool_2d_same_upper",
"test_cast_FLOAT_to_STRING",
"test_cast_STRING_to_FLOAT",
"test_castlike_FLOAT_to_STRING_expanded",
"test_castlike_STRING_to_FLOAT_expanded",
"test_ceil",
"test_cumsum_1d",
"test_cumsum_1d_exclusive",
"test_cumsum_1d_reverse",
"test_cumsum_1d_reverse_exclusive",
"test_cumsum_2d_axis_0",
"test_cumsum_2d_axis_1",
"test_cumsum_2d_negative_axis",
"test_concat_1d_axis_negative_1",
"test_concat_3d_axis_1",
"test_div",
"test_div_bcast",
"test_div_uint8",
"test_elu",
"test_elu_default",
"test_exp",
"test_floor",
"test_leakyrelu",
"test_leakyrelu_default",
"test_log",
"test_logsoftmax_axis_1",
"test_logsoftmax_axis_1_expanded",
"test_logsoftmax_default_axis",
"test_logsoftmax_example_1",
"test_logsoftmax_large_number",
"test_matmul_2d",
"test_matmul_3d",
"test_matmul_4d",
"test_maxpool_2d_dilations",
"test_maxpool_2d_same_lower",
"test_maxpool_2d_uint8",
"test_maxpool_with_argmax_2d_precomputed_pads",
"test_maxpool_with_argmax_2d_precomputed_strides",
"test_maxunpool_export_with_output_shape",
"test_mul",
"test_mul_bcast",
"test_mul_uint8",
"test_neg",
"test_reduce_max_default_axes_keepdim_example",
"test_reduce_max_default_axes_keepdims_random",
"test_reduce_max_do_not_keepdims_example",
"test_reduce_max_do_not_keepdims_random",
"test_reduce_max_keepdims_example",
"test_reduce_max_keepdims_random",
"test_reduce_max_negative_axes_keepdims_example",
"test_reduce_max_negative_axes_keepdims_random",
"test_relu",
"test_sigmoid",
"test_softmax_axis_1",
"test_softmax_axis_1_expanded",
"test_softmax_default_axis",
"test_softmax_large_number",
"test_sqrt",
"test_sub",
"test_sub_bcast",
"test_sub_uint8",
"test_tanh",
"test_upsample_nearest",
"test_scatter_elements_with_axis",
"test_scatter_elements_with_duplicate_indices",
"test_scatter_elements_with_negative_indices",
"test_scatter_elements_with_reduction_max",
"test_scatter_elements_with_reduction_min",
"test_scatter_elements_without_axis",
"test_scatter_with_axis",
"test_scatter_without_axis",
"test_scatternd",
"test_scatternd_add",
"test_scatternd_max",
"test_scatternd_min",
"test_scatternd_multiply",
