﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\ocl\test_brute_force_matcher.cpp">
      <Filter>opencv_features2d\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\ocl\test_feature2d.cpp">
      <Filter>opencv_features2d\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_affine_feature.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_agast.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_akaze.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_blobdetector.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_brisk.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_descriptors_invariance.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_descriptors_regression.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_detectors_invariance.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_detectors_regression.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_drawing.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_fast.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_keypoints.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_main.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_matchers_algorithmic.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_mser.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_nearestneighbors.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_orb.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_sift.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_utils.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_descriptors_invariance.impl.hpp">
      <Filter>opencv_features2d\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_descriptors_regression.impl.hpp">
      <Filter>opencv_features2d\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_detectors_invariance.impl.hpp">
      <Filter>opencv_features2d\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_detectors_regression.impl.hpp">
      <Filter>opencv_features2d\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_invariance_utils.hpp">
      <Filter>opencv_features2d\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\test\test_precomp.hpp">
      <Filter>opencv_features2d\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_features2d">
      <UniqueIdentifier>{C24905E9-C93C-39F2-920C-0B0ABC5F4494}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_features2d\Include">
      <UniqueIdentifier>{332D887C-DBBD-3166-BFC2-1DE26103E2F4}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_features2d\Src">
      <UniqueIdentifier>{89F5B458-684E-33A2-AE1B-2BD20A1B4667}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_features2d\Src\ocl">
      <UniqueIdentifier>{5F514884-A77C-36CE-99DF-373A7BBFC8C7}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
