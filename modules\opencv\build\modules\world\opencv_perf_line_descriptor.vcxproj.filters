﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\line_descriptor\perf\perf_descriptors.cpp">
      <Filter>opencv_line_descriptor\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\line_descriptor\perf\perf_detection.cpp">
      <Filter>opencv_line_descriptor\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\line_descriptor\perf\perf_main.cpp">
      <Filter>opencv_line_descriptor\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\line_descriptor\perf\perf_matching.cpp">
      <Filter>opencv_line_descriptor\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\line_descriptor\perf\perf_precomp.hpp">
      <Filter>opencv_line_descriptor\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_line_descriptor">
      <UniqueIdentifier>{852F400C-B61D-3E09-9C1F-E7A9589C0ACF}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_line_descriptor\Include">
      <UniqueIdentifier>{10086B5F-372F-3F01-BF9A-58FC8B0B07DB}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_line_descriptor\Src">
      <UniqueIdentifier>{ADB4E458-7757-3B09-AFE3-B75A95F9243B}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
