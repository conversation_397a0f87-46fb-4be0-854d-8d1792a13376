__all__: list[str] = []

# Enumerations
male: int
MALE: int
female: int
FEMALE: int
none: int
NONE: int
genderType = int
"""One of [male, <PERSON>LE, female, FEMALE, none, NONE]"""

circle: int
CIRCLE: int
triangle: int
TRIANGLE: int
updown: int
UPDOWN: int
rightleft: int
RIGHTLEFT: int
wave: int
WAVE: int
z: int
Z: int
cross: int
CROSS: int
comehere: int
COMEHERE: int
turnaround: int
TURNAROUND: int
pat: int
PAT: int
actionType = int
"""One of [circle, CIRCLE, triangle, TRIANGLE, updown, UPDOWN, rightleft, RIGHTLEFT, wave, WAVE, z, Z, cross, CROSS, comehere, COMEHERE, turnaround, TURNAROUND, pat, PAT]"""

fist: int
FIST: int
index: int
INDEX: int
flat: int
FLAT: int
poseType = int
"""One of [fist, FIST, index, INDEX, flat, FLAT]"""

light: int
LIGHT: int
dark: int
DARK: int
illuminationType = int
"""One of [light, LIGHT, dark, DARK]"""

woodenBoard: int
WOODEN_BOARD: int
whitePaper: int
WHITE_PAPER: int
paperWithCharacters: int
PAPER_WITH_CHARACTERS: int
backgroundType = int
"""One of [woodenBoard, WOODEN_BOARD, whitePaper, WHITE_PAPER, paperWithCharacters, PAPER_WITH_CHARACTERS]"""

humaneva_1: int
HUMANEVA_1: int
humaneva_2: int
HUMANEVA_2: int
datasetType = int
"""One of [humaneva_1, HUMANEVA_1, humaneva_2, HUMANEVA_2]"""

POS: int
NEG: int
sampleType = int
"""One of [POS, NEG]"""

LEFT: int
RIGHT: int
LADYBUG: int
imageType = int
"""One of [LEFT, RIGHT, LADYBUG]"""



