﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\mathfuncs_core.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\stat.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\arithm.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\convert.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\convert_scale.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\count_non_zero.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\has_non_zero.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\matmul.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\mean.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\merge.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\split.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\sum.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\src\imgwarp.avx2.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\src\resize.avx2.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\accum.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\bilateral_filter.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\box_filter.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\filter.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\color_hsv.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\color_rgb.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\color_yuv.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\median_blur.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\morph.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\smooth.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\sumpixels.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\layers\layers_common.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\int8layers\layers_common.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\layers\cpu_kernels\conv_block.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\layers\cpu_kernels\conv_depthwise.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\layers\cpu_kernels\conv_winograd_f63.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\layers\cpu_kernels\fast_gemm_kernels.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\features2d\src\fast.avx2.cpp">
      <Filter>opencv_features2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\sift.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\undistort.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\backends\fluid\gfluidimgproc_func.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\backends\fluid\gfluidcore_func.avx2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{2A318434-2169-3E81-A653-C1C0490B5893}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_features2d">
      <UniqueIdentifier>{C24905E9-C93C-39F2-920C-0B0ABC5F4494}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_features2d\Src">
      <UniqueIdentifier>{89F5B458-684E-33A2-AE1B-2BD20A1B4667}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_imgproc">
      <UniqueIdentifier>{FA23004A-22DB-30E6-B8FD-718F497FB629}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_imgproc\Src">
      <UniqueIdentifier>{7A7ACE0E-D197-310E-909D-212CE8311A24}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
