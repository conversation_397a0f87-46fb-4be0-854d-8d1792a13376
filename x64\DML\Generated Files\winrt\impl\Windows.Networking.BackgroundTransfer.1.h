// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Networking_BackgroundTransfer_1_H
#define WINRT_Windows_Networking_BackgroundTransfer_1_H
#include "winrt/impl/Windows.Networking.BackgroundTransfer.0.h"
WINRT_EXPORT namespace winrt::Windows::Networking::BackgroundTransfer
{
    struct WINRT_IMPL_EMPTY_BASES IBackgroundDownloader :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundDownloader>,
        impl::require<winrt::Windows::Networking::BackgroundTransfer::IBackgroundDownloader, winrt::Windows::Networking::BackgroundTransfer::IBackgroundTransferBase>
    {
        IBackgroundDownloader(std::nullptr_t = nullptr) noexcept {}
        IBackgroundDownloader(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundDownloader2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundDownloader2>
    {
        IBackgroundDownloader2(std::nullptr_t = nullptr) noexcept {}
        IBackgroundDownloader2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundDownloader3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundDownloader3>
    {
        IBackgroundDownloader3(std::nullptr_t = nullptr) noexcept {}
        IBackgroundDownloader3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundDownloaderFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundDownloaderFactory>
    {
        IBackgroundDownloaderFactory(std::nullptr_t = nullptr) noexcept {}
        IBackgroundDownloaderFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundDownloaderStaticMethods :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundDownloaderStaticMethods>
    {
        IBackgroundDownloaderStaticMethods(std::nullptr_t = nullptr) noexcept {}
        IBackgroundDownloaderStaticMethods(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundDownloaderStaticMethods2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundDownloaderStaticMethods2>
    {
        IBackgroundDownloaderStaticMethods2(std::nullptr_t = nullptr) noexcept {}
        IBackgroundDownloaderStaticMethods2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundDownloaderUserConsent :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundDownloaderUserConsent>
    {
        IBackgroundDownloaderUserConsent(std::nullptr_t = nullptr) noexcept {}
        IBackgroundDownloaderUserConsent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTransferBase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTransferBase>
    {
        IBackgroundTransferBase(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTransferBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTransferCompletionGroup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTransferCompletionGroup>
    {
        IBackgroundTransferCompletionGroup(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTransferCompletionGroup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTransferCompletionGroupTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTransferCompletionGroupTriggerDetails>
    {
        IBackgroundTransferCompletionGroupTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTransferCompletionGroupTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTransferContentPart :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTransferContentPart>
    {
        IBackgroundTransferContentPart(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTransferContentPart(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTransferContentPartFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTransferContentPartFactory>
    {
        IBackgroundTransferContentPartFactory(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTransferContentPartFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTransferErrorStaticMethods :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTransferErrorStaticMethods>
    {
        IBackgroundTransferErrorStaticMethods(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTransferErrorStaticMethods(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTransferGroup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTransferGroup>
    {
        IBackgroundTransferGroup(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTransferGroup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTransferGroupStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTransferGroupStatics>
    {
        IBackgroundTransferGroupStatics(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTransferGroupStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTransferOperation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTransferOperation>
    {
        IBackgroundTransferOperation(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTransferOperation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTransferOperationPriority :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTransferOperationPriority>
    {
        IBackgroundTransferOperationPriority(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTransferOperationPriority(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTransferRangesDownloadedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTransferRangesDownloadedEventArgs>
    {
        IBackgroundTransferRangesDownloadedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTransferRangesDownloadedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundUploader :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundUploader>,
        impl::require<winrt::Windows::Networking::BackgroundTransfer::IBackgroundUploader, winrt::Windows::Networking::BackgroundTransfer::IBackgroundTransferBase>
    {
        IBackgroundUploader(std::nullptr_t = nullptr) noexcept {}
        IBackgroundUploader(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundUploader2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundUploader2>
    {
        IBackgroundUploader2(std::nullptr_t = nullptr) noexcept {}
        IBackgroundUploader2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundUploader3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundUploader3>
    {
        IBackgroundUploader3(std::nullptr_t = nullptr) noexcept {}
        IBackgroundUploader3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundUploaderFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundUploaderFactory>
    {
        IBackgroundUploaderFactory(std::nullptr_t = nullptr) noexcept {}
        IBackgroundUploaderFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundUploaderStaticMethods :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundUploaderStaticMethods>
    {
        IBackgroundUploaderStaticMethods(std::nullptr_t = nullptr) noexcept {}
        IBackgroundUploaderStaticMethods(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundUploaderStaticMethods2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundUploaderStaticMethods2>
    {
        IBackgroundUploaderStaticMethods2(std::nullptr_t = nullptr) noexcept {}
        IBackgroundUploaderStaticMethods2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundUploaderUserConsent :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundUploaderUserConsent>
    {
        IBackgroundUploaderUserConsent(std::nullptr_t = nullptr) noexcept {}
        IBackgroundUploaderUserConsent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IContentPrefetcher :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentPrefetcher>
    {
        IContentPrefetcher(std::nullptr_t = nullptr) noexcept {}
        IContentPrefetcher(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IContentPrefetcherTime :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentPrefetcherTime>
    {
        IContentPrefetcherTime(std::nullptr_t = nullptr) noexcept {}
        IContentPrefetcherTime(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDownloadOperation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDownloadOperation>,
        impl::require<winrt::Windows::Networking::BackgroundTransfer::IDownloadOperation, winrt::Windows::Networking::BackgroundTransfer::IBackgroundTransferOperation>
    {
        IDownloadOperation(std::nullptr_t = nullptr) noexcept {}
        IDownloadOperation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDownloadOperation2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDownloadOperation2>
    {
        IDownloadOperation2(std::nullptr_t = nullptr) noexcept {}
        IDownloadOperation2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDownloadOperation3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDownloadOperation3>
    {
        IDownloadOperation3(std::nullptr_t = nullptr) noexcept {}
        IDownloadOperation3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDownloadOperation4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDownloadOperation4>
    {
        IDownloadOperation4(std::nullptr_t = nullptr) noexcept {}
        IDownloadOperation4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDownloadOperation5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDownloadOperation5>
    {
        IDownloadOperation5(std::nullptr_t = nullptr) noexcept {}
        IDownloadOperation5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IResponseInformation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IResponseInformation>
    {
        IResponseInformation(std::nullptr_t = nullptr) noexcept {}
        IResponseInformation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUnconstrainedTransferRequestResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUnconstrainedTransferRequestResult>
    {
        IUnconstrainedTransferRequestResult(std::nullptr_t = nullptr) noexcept {}
        IUnconstrainedTransferRequestResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUploadOperation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUploadOperation>,
        impl::require<winrt::Windows::Networking::BackgroundTransfer::IUploadOperation, winrt::Windows::Networking::BackgroundTransfer::IBackgroundTransferOperation>
    {
        IUploadOperation(std::nullptr_t = nullptr) noexcept {}
        IUploadOperation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUploadOperation2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUploadOperation2>
    {
        IUploadOperation2(std::nullptr_t = nullptr) noexcept {}
        IUploadOperation2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUploadOperation3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUploadOperation3>
    {
        IUploadOperation3(std::nullptr_t = nullptr) noexcept {}
        IUploadOperation3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUploadOperation4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUploadOperation4>
    {
        IUploadOperation4(std::nullptr_t = nullptr) noexcept {}
        IUploadOperation4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
