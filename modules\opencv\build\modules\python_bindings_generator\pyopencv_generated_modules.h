CVPY_MODULE("", cv);
CVPY_MODULE(".Error", Error);
CVPY_MODULE(".aruco", aruco);
CVPY_MODULE(".barcode", barcode);
CVPY_MODULE(".bgsegm", bgsegm);
CVPY_MODULE(".bioinspired", bioinspired);
CVPY_MODULE(".ccm", ccm);
CVPY_MODULE(".colored_kinfu", colored_kinfu);
CVPY_MODULE(".cuda", cuda);
CVPY_MODULE(".cudacodec", cudacodec);
CVPY_MODULE(".datasets", datasets);
CVPY_MODULE(".detail", detail);
CVPY_MODULE(".dnn", dnn);
CVPY_MODULE(".dnn_superres", dnn_superres);
CVPY_MODULE(".dpm", dpm);
CVPY_MODULE(".dynafu", dynafu);
CVPY_MODULE(".face", face);
CVPY_MODULE(".fisheye", fisheye);
CVPY_MODULE(".flann", flann);
CVPY_MODULE(".ft", ft);
CVPY_MODULE(".gapi", gapi);
CVPY_MODULE(".gapi.core.cpu", gapi_core_cpu);
CVPY_MODULE(".gapi.core.fluid", gapi_core_fluid);
CVPY_MODULE(".gapi.core.ocl", gapi_core_ocl);
CVPY_MODULE(".gapi.ie", gapi_ie);
CVPY_MODULE(".gapi.ie.detail", gapi_ie_detail);
CVPY_MODULE(".gapi.imgproc.fluid", gapi_imgproc_fluid);
CVPY_MODULE(".gapi.oak", gapi_oak);
CVPY_MODULE(".gapi.onnx", gapi_onnx);
CVPY_MODULE(".gapi.onnx.ep", gapi_onnx_ep);
CVPY_MODULE(".gapi.ot", gapi_ot);
CVPY_MODULE(".gapi.ot.cpu", gapi_ot_cpu);
CVPY_MODULE(".gapi.ov", gapi_ov);
CVPY_MODULE(".gapi.own.detail", gapi_own_detail);
CVPY_MODULE(".gapi.render.ocv", gapi_render_ocv);
CVPY_MODULE(".gapi.streaming", gapi_streaming);
CVPY_MODULE(".gapi.video", gapi_video);
CVPY_MODULE(".gapi.wip", gapi_wip);
CVPY_MODULE(".gapi.wip.draw", gapi_wip_draw);
CVPY_MODULE(".gapi.wip.gst", gapi_wip_gst);
CVPY_MODULE(".gapi.wip.onevpl", gapi_wip_onevpl);
CVPY_MODULE(".hfs", hfs);
CVPY_MODULE(".img_hash", img_hash);
CVPY_MODULE(".intensity_transform", intensity_transform);
CVPY_MODULE(".ipp", ipp);
CVPY_MODULE(".kinfu", kinfu);
CVPY_MODULE(".kinfu.detail", kinfu_detail);
CVPY_MODULE(".large_kinfu", large_kinfu);
CVPY_MODULE(".legacy", legacy);
CVPY_MODULE(".line_descriptor", line_descriptor);
CVPY_MODULE(".linemod", linemod);
CVPY_MODULE(".mcc", mcc);
CVPY_MODULE(".ml", ml);
CVPY_MODULE(".motempl", motempl);
CVPY_MODULE(".multicalib", multicalib);
CVPY_MODULE(".ocl", ocl);
CVPY_MODULE(".ogl", ogl);
CVPY_MODULE(".omnidir", omnidir);
CVPY_MODULE(".optflow", optflow);
CVPY_MODULE(".parallel", parallel);
CVPY_MODULE(".phase_unwrapping", phase_unwrapping);
CVPY_MODULE(".plot", plot);
CVPY_MODULE(".ppf_match_3d", ppf_match_3d);
CVPY_MODULE(".quality", quality);
CVPY_MODULE(".rapid", rapid);
CVPY_MODULE(".reg", reg);
CVPY_MODULE(".rgbd", rgbd);
CVPY_MODULE(".saliency", saliency);
CVPY_MODULE(".samples", samples);
CVPY_MODULE(".segmentation", segmentation);
CVPY_MODULE(".signal", signal);
CVPY_MODULE(".stereo", stereo);
CVPY_MODULE(".structured_light", structured_light);
CVPY_MODULE(".text", text);
CVPY_MODULE(".utils", utils);
CVPY_MODULE(".utils.fs", utils_fs);
CVPY_MODULE(".utils.nested", utils_nested);
CVPY_MODULE(".videoio_registry", videoio_registry);
CVPY_MODULE(".videostab", videostab);
CVPY_MODULE(".wechat_qrcode", wechat_qrcode);
CVPY_MODULE(".xfeatures2d", xfeatures2d);
CVPY_MODULE(".ximgproc", ximgproc);
CVPY_MODULE(".ximgproc.segmentation", ximgproc_segmentation);
CVPY_MODULE(".xphoto", xphoto);
