-overwrite -name sunoneaimbotcpp -pch . -prefix -comp "x64\DML\Generated Files\sources"  -ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.Actions.ActionsContract\*******\Windows.AI.Actions.ActionsContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.MachineLearning.MachineLearningContract\*******\Windows.AI.MachineLearning.MachineLearningContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract\*******\Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.AI.ModelContextProtocol.ModelContextProtocolContract\*******\Windows.AI.ModelContextProtocol.ModelContextProtocolContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ActivatedEventsContract\*******\Windows.ApplicationModel.Activation.ActivatedEventsContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ActivationCameraSettingsContract\*******\Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.ContactActivatedEventsContract\*******\Windows.ApplicationModel.Activation.ContactActivatedEventsContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract\*******\Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract\*******\Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.Background.CallsBackgroundContract\*******\Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.CallsPhoneContract\*******\Windows.ApplicationModel.Calls.CallsPhoneContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.CallsVoipContract\*******\Windows.ApplicationModel.Calls.CallsVoipContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Calls.LockScreenCallContract\*******\Windows.ApplicationModel.Calls.LockScreenCallContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract\*******\Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.FullTrustAppContract\*******\Windows.ApplicationModel.FullTrustAppContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract\*******\Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Preview.Notes.PreviewNotesContract\*******\Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Resources.Management.ResourceIndexerContract\*******\Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Search.Core.SearchCoreContract\*******\Windows.ApplicationModel.Search.Core.SearchCoreContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Search.SearchContract\*******\Windows.ApplicationModel.Search.SearchContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.SocialInfo.SocialInfoContract\*******\Windows.ApplicationModel.SocialInfo.SocialInfoContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.StartupTaskContract\3.0.0.0\Windows.ApplicationModel.StartupTaskContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.ApplicationModel.Wallet.WalletContract\*******\Windows.ApplicationModel.Wallet.WalletContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Custom.CustomDeviceContract\*******\Windows.Devices.Custom.CustomDeviceContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.DevicesLowLevelContract\3.0.0.0\Windows.Devices.DevicesLowLevelContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Portable.PortableDeviceContract\*******\Windows.Devices.Portable.PortableDeviceContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Power.PowerGridApiContract\*******\Windows.Devices.Power.PowerGridApiContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Printers.Extensions.ExtensionsContract\*******\Windows.Devices.Printers.Extensions.ExtensionsContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Printers.PrintersContract\*******\Windows.Devices.Printers.PrintersContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Scanners.ScannerDeviceContract\*******\Windows.Devices.Scanners.ScannerDeviceContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract\3.0.0.0\Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.SmartCards.SmartCardEmulatorContract\6.0.0.0\Windows.Devices.SmartCards.SmartCardEmulatorContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Devices.Sms.LegacySmsApiContract\*******\Windows.Devices.Sms.LegacySmsApiContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Foundation.FoundationContract\*******\Windows.Foundation.FoundationContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Foundation.UniversalApiContract\19.0.0.0\Windows.Foundation.UniversalApiContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.Input.GamingInputPreviewContract\*******\Windows.Gaming.Input.GamingInputPreviewContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.Preview.GamesEnumerationContract\*******\Windows.Gaming.Preview.GamesEnumerationContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.UI.GameChatOverlayContract\*******\Windows.Gaming.UI.GameChatOverlayContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.UI.GamingUIProviderContract\*******\Windows.Gaming.UI.GamingUIProviderContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Gaming.XboxLive.StorageApiContract\*******\Windows.Gaming.XboxLive.StorageApiContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract\*******\Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Graphics.Printing3D.Printing3DContract\*******\Windows.Graphics.Printing3D.Printing3DContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Deployment.Preview.DeploymentPreviewContract\*******\Windows.Management.Deployment.Preview.DeploymentPreviewContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Deployment.SharedPackageContainerContract\*******\Windows.Management.Deployment.SharedPackageContainerContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Update.WindowsUpdateContract\*******\Windows.Management.Update.WindowsUpdateContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Management.Workplace.WorkplaceSettingsContract\*******\Windows.Management.Workplace.WorkplaceSettingsContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.AppBroadcasting.AppBroadcastingContract\*******\Windows.Media.AppBroadcasting.AppBroadcastingContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.AppRecording.AppRecordingContract\*******\Windows.Media.AppRecording.AppRecordingContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppBroadcastContract\*******\Windows.Media.Capture.AppBroadcastContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppCaptureContract\*******\Windows.Media.Capture.AppCaptureContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.AppCaptureMetadataContract\*******\Windows.Media.Capture.AppCaptureMetadataContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.CameraCaptureUIContract\*******\Windows.Media.Capture.CameraCaptureUIContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Capture.GameBarContract\*******\Windows.Media.Capture.GameBarContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Devices.CallControlContract\*******\Windows.Media.Devices.CallControlContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.MediaControlContract\*******\Windows.Media.MediaControlContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Playlists.PlaylistsContract\*******\Windows.Media.Playlists.PlaylistsContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Media.Protection.ProtectionRenewalContract\*******\Windows.Media.Protection.ProtectionRenewalContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.Connectivity.WwanContract\3.0.0.0\Windows.Networking.Connectivity.WwanContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract\*******\Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.Sockets.ControlChannelTriggerContract\3.0.0.0\Windows.Networking.Sockets.ControlChannelTriggerContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Networking.XboxLive.XboxLiveSecureSocketsContract\*******\Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Perception.Automation.Core.PerceptionAutomationCoreContract\*******\Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.EnterpriseData.EnterpriseDataContract\*******\Windows.Security.EnterpriseData.EnterpriseDataContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.ExchangeActiveSyncProvisioning.EasContract\*******\Windows.Security.ExchangeActiveSyncProvisioning.EasContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Security.Isolation.Isolatedwindowsenvironmentcontract\*******\Windows.Security.Isolation.Isolatedwindowsenvironmentcontract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Maps.GuidanceContract\3.0.0.0\Windows.Services.Maps.GuidanceContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Maps.LocalSearchContract\*******\Windows.Services.Maps.LocalSearchContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.Store.StoreContract\*******\Windows.Services.Store.StoreContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Services.TargetedContent.TargetedContentContract\*******\Windows.Services.TargetedContent.TargetedContentContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Storage.Provider.CloudFilesContract\*******\Windows.Storage.Provider.CloudFilesContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\windows.system.profile.platformautomaticappsignincontract\*******\windows.system.profile.platformautomaticappsignincontract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileHardwareTokenContract\*******\Windows.System.Profile.ProfileHardwareTokenContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileRetailInfoContract\*******\Windows.System.Profile.ProfileRetailInfoContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.ProfileSharedModeContract\*******\Windows.System.Profile.ProfileSharedModeContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.Profile.SystemManufacturers.SystemManufacturersContract\3.0.0.0\Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.SystemManagementContract\*******\Windows.System.SystemManagementContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.UserProfile.UserProfileContract\*******\Windows.System.UserProfile.UserProfileContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.System.UserProfile.UserProfileLockScreenContract\*******\Windows.System.UserProfile.UserProfileLockScreenContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.ApplicationSettings.ApplicationsSettingsContract\*******\Windows.UI.ApplicationSettings.ApplicationsSettingsContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Core.AnimationMetrics.AnimationMetricsContract\*******\Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Core.CoreWindowDialogsContract\*******\Windows.UI.Core.CoreWindowDialogsContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Shell.SecurityAppManagerContract\*******\Windows.UI.Shell.SecurityAppManagerContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Shell.WindowTabManagerContract\*******\Windows.UI.Shell.WindowTabManagerContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.UIAutomation.UIAutomationContract\*******\Windows.UI.UIAutomation.UIAutomationContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.ViewManagement.ViewManagementViewScalingContract\*******\Windows.UI.ViewManagement.ViewManagementViewScalingContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Xaml.Core.Direct.XamlDirectContract\*******\Windows.UI.Xaml.Core.Direct.XamlDirectContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.UI.Xaml.Hosting.HostingContract\*******\Windows.UI.Xaml.Hosting.HostingContract.winmd"
-ref "C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0\Windows.Web.Http.Diagnostics.HttpDiagnosticsContract\*******\Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.winmd" -out "x64\DML\Generated Files\."
