Gui Features in OpenCV {#tutorial_py_table_of_contents_gui}
======================

-   @ref tutorial_display_image

    Learn to load an
    image, display it, and save it back

-   @subpage tutorial_py_video_display

    <PERSON><PERSON> to play videos,
    capture videos from a camera, and write videos

-   @subpage tutorial_py_drawing_functions

    Learn to draw lines,
    rectangles, ellipses, circles, etc with OpenCV

-   @subpage tutorial_py_mouse_handling

    Draw stuff with your
    mouse

-   @subpage tutorial_py_trackbar

    Create trackbar to
    control certain parameters
