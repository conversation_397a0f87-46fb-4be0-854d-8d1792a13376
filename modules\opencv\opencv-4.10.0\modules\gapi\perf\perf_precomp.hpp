// This file is part of OpenCV project.
// It is subject to the license terms in the LICENSE file found in the top-level directory
// of this distribution and at http://opencv.org/license.html.
//
// Copyright (C) 2018-2020 Intel Corporation


#ifndef __OPENCV_GAPI_PERF_PRECOMP_HPP__
#define __OPENCV_GAPI_PERF_PRECOMP_HPP__

#include <cstdint>
#include <vector>

#include <opencv2/ts.hpp>
#include <opencv2/gapi.hpp>
#include <opencv2/gapi/imgproc.hpp>
#include <opencv2/gapi/video.hpp>
#include <opencv2/gapi/core.hpp>
#include <opencv2/gapi/cpu/gcpukernel.hpp>
#include <opencv2/gapi/gpu/ggpukernel.hpp>
#include <opencv2/gapi/gpu/imgproc.hpp>
#include <opencv2/gapi/gpu/core.hpp>
#include <opencv2/gapi/operators.hpp>

#include <opencv2/gapi/fluid/core.hpp>
#include <opencv2/gapi/fluid/imgproc.hpp>

#endif // __OPENCV_GAPI_PERF_PRECOMP_HPP__
