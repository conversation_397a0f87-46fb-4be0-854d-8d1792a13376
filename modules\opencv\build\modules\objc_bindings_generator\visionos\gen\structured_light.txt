PORTED FUNCs LIST (14 of 14):

static Ptr_GrayCodePattern cv::structured_light::GrayCodePattern::create(int width, int height)
 size_t cv::structured_light::GrayCodePattern::getNumberOfPatternImages()
 void cv::structured_light::GrayCodePattern::setWhiteThreshold(size_t value)
 void cv::structured_light::GrayCodePattern::setBlackThreshold(size_t value)
 void cv::structured_light::GrayCodePattern::getImagesForShadowMasks(Mat& blackImage, Mat& whiteImage)
 bool cv::structured_light::GrayCodePattern::getProjPixel(vector_Mat patternImages, int x, int y, Point& projPix)
static Ptr_SinusoidalPattern cv::structured_light::SinusoidalPattern::create(Ptr_SinusoidalPattern_Params parameters = makePtr<SinusoidalPattern::Params>())
 void cv::structured_light::SinusoidalPattern::computePhaseMap(vector_Mat patternImages, Mat& wrappedPhaseMap, Mat& shadowMask = Mat(), Mat fundamental = Mat())
 void cv::structured_light::SinusoidalPattern::unwrapPhaseMap(Mat wrappedPhaseMap, Mat& unwrappedPhaseMap, Size camSize, Mat shadowMask = Mat())
 void cv::structured_light::SinusoidalPattern::findProCamMatches(Mat projUnwrappedPhaseMap, Mat camUnwrappedPhaseMap, vector_Mat& matches)
 void cv::structured_light::SinusoidalPattern::computeDataModulationTerm(vector_Mat patternImages, Mat& dataModulationTerm, Mat shadowMask)
  cv::structured_light::SinusoidalPattern::Params::Params()
 bool cv::structured_light::StructuredLightPattern::generate(vector_Mat& patternImages)
 bool cv::structured_light::StructuredLightPattern::decode(vector_vector_Mat patternImages, Mat& disparityMap, vector_Mat blackImages = vector_Mat(), vector_Mat whiteImages = vector_Mat(), int flags = DECODE_3D_UNDERWORLD)

SKIPPED FUNCs LIST (0 of 14):


0 def args - 10 funcs
1 def args - 2 funcs
2 def args - 1 funcs
3 def args - 1 funcs