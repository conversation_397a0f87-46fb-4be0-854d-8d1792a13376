// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_UI_Composition_Interactions_1_H
#define WINRT_Windows_UI_Composition_Interactions_1_H
#include "winrt/impl/Windows.UI.Composition.Interactions.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Composition::Interactions
{
    struct WINRT_IMPL_EMPTY_BASES ICompositionConditionalValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionConditionalValue>
    {
        ICompositionConditionalValue(std::nullptr_t = nullptr) noexcept {}
        ICompositionConditionalValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionConditionalValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionConditionalValueStatics>
    {
        ICompositionConditionalValueStatics(std::nullptr_t = nullptr) noexcept {}
        ICompositionConditionalValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionInteractionSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionInteractionSource>
    {
        ICompositionInteractionSource(std::nullptr_t = nullptr) noexcept {}
        ICompositionInteractionSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionInteractionSourceCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionInteractionSourceCollection>
    {
        ICompositionInteractionSourceCollection(std::nullptr_t = nullptr) noexcept {}
        ICompositionInteractionSourceCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionSourceConfiguration :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionSourceConfiguration>
    {
        IInteractionSourceConfiguration(std::nullptr_t = nullptr) noexcept {}
        IInteractionSourceConfiguration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTracker :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTracker>
    {
        IInteractionTracker(std::nullptr_t = nullptr) noexcept {}
        IInteractionTracker(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTracker2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTracker2>
    {
        IInteractionTracker2(std::nullptr_t = nullptr) noexcept {}
        IInteractionTracker2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTracker3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTracker3>
    {
        IInteractionTracker3(std::nullptr_t = nullptr) noexcept {}
        IInteractionTracker3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTracker4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTracker4>
    {
        IInteractionTracker4(std::nullptr_t = nullptr) noexcept {}
        IInteractionTracker4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTracker5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTracker5>
    {
        IInteractionTracker5(std::nullptr_t = nullptr) noexcept {}
        IInteractionTracker5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerCustomAnimationStateEnteredArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerCustomAnimationStateEnteredArgs>
    {
        IInteractionTrackerCustomAnimationStateEnteredArgs(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerCustomAnimationStateEnteredArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerCustomAnimationStateEnteredArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerCustomAnimationStateEnteredArgs2>
    {
        IInteractionTrackerCustomAnimationStateEnteredArgs2(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerCustomAnimationStateEnteredArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerIdleStateEnteredArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerIdleStateEnteredArgs>
    {
        IInteractionTrackerIdleStateEnteredArgs(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerIdleStateEnteredArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerIdleStateEnteredArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerIdleStateEnteredArgs2>
    {
        IInteractionTrackerIdleStateEnteredArgs2(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerIdleStateEnteredArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerInertiaModifier :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerInertiaModifier>
    {
        IInteractionTrackerInertiaModifier(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerInertiaModifier(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerInertiaModifierFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerInertiaModifierFactory>
    {
        IInteractionTrackerInertiaModifierFactory(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerInertiaModifierFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerInertiaMotion :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerInertiaMotion>
    {
        IInteractionTrackerInertiaMotion(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerInertiaMotion(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerInertiaMotionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerInertiaMotionStatics>
    {
        IInteractionTrackerInertiaMotionStatics(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerInertiaMotionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerInertiaNaturalMotion :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerInertiaNaturalMotion>
    {
        IInteractionTrackerInertiaNaturalMotion(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerInertiaNaturalMotion(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerInertiaNaturalMotionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerInertiaNaturalMotionStatics>
    {
        IInteractionTrackerInertiaNaturalMotionStatics(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerInertiaNaturalMotionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerInertiaRestingValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerInertiaRestingValue>
    {
        IInteractionTrackerInertiaRestingValue(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerInertiaRestingValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerInertiaRestingValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerInertiaRestingValueStatics>
    {
        IInteractionTrackerInertiaRestingValueStatics(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerInertiaRestingValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerInertiaStateEnteredArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerInertiaStateEnteredArgs>
    {
        IInteractionTrackerInertiaStateEnteredArgs(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerInertiaStateEnteredArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerInertiaStateEnteredArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerInertiaStateEnteredArgs2>
    {
        IInteractionTrackerInertiaStateEnteredArgs2(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerInertiaStateEnteredArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerInertiaStateEnteredArgs3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerInertiaStateEnteredArgs3>
    {
        IInteractionTrackerInertiaStateEnteredArgs3(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerInertiaStateEnteredArgs3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerInteractingStateEnteredArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerInteractingStateEnteredArgs>
    {
        IInteractionTrackerInteractingStateEnteredArgs(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerInteractingStateEnteredArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerInteractingStateEnteredArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerInteractingStateEnteredArgs2>
    {
        IInteractionTrackerInteractingStateEnteredArgs2(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerInteractingStateEnteredArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerOwner :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerOwner>
    {
        IInteractionTrackerOwner(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerOwner(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerRequestIgnoredArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerRequestIgnoredArgs>
    {
        IInteractionTrackerRequestIgnoredArgs(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerRequestIgnoredArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerStatics>
    {
        IInteractionTrackerStatics(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerStatics2>
    {
        IInteractionTrackerStatics2(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerValuesChangedArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerValuesChangedArgs>
    {
        IInteractionTrackerValuesChangedArgs(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerValuesChangedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerVector2InertiaModifier :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerVector2InertiaModifier>
    {
        IInteractionTrackerVector2InertiaModifier(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerVector2InertiaModifier(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerVector2InertiaModifierFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerVector2InertiaModifierFactory>
    {
        IInteractionTrackerVector2InertiaModifierFactory(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerVector2InertiaModifierFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerVector2InertiaNaturalMotion :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerVector2InertiaNaturalMotion>
    {
        IInteractionTrackerVector2InertiaNaturalMotion(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerVector2InertiaNaturalMotion(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInteractionTrackerVector2InertiaNaturalMotionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInteractionTrackerVector2InertiaNaturalMotionStatics>
    {
        IInteractionTrackerVector2InertiaNaturalMotionStatics(std::nullptr_t = nullptr) noexcept {}
        IInteractionTrackerVector2InertiaNaturalMotionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualInteractionSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualInteractionSource>
    {
        IVisualInteractionSource(std::nullptr_t = nullptr) noexcept {}
        IVisualInteractionSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualInteractionSource2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualInteractionSource2>
    {
        IVisualInteractionSource2(std::nullptr_t = nullptr) noexcept {}
        IVisualInteractionSource2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualInteractionSource3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualInteractionSource3>
    {
        IVisualInteractionSource3(std::nullptr_t = nullptr) noexcept {}
        IVisualInteractionSource3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualInteractionSourceObjectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualInteractionSourceObjectFactory>
    {
        IVisualInteractionSourceObjectFactory(std::nullptr_t = nullptr) noexcept {}
        IVisualInteractionSourceObjectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualInteractionSourceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualInteractionSourceStatics>
    {
        IVisualInteractionSourceStatics(std::nullptr_t = nullptr) noexcept {}
        IVisualInteractionSourceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualInteractionSourceStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualInteractionSourceStatics2>
    {
        IVisualInteractionSourceStatics2(std::nullptr_t = nullptr) noexcept {}
        IVisualInteractionSourceStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
