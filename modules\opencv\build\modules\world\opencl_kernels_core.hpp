// This file is auto-generated. Do not edit!

#include "opencv2/core/ocl.hpp"
#include "opencv2/core/ocl_genbase.hpp"
#include "opencv2/core/opencl/ocl_defs.hpp"

#ifdef HAVE_OPENCL

namespace cv
{
namespace ocl
{
namespace core
{

extern struct cv::ocl::internal::ProgramEntry arithm_oclsrc;
extern struct cv::ocl::internal::ProgramEntry convert_oclsrc;
extern struct cv::ocl::internal::ProgramEntry copymakeborder_oclsrc;
extern struct cv::ocl::internal::ProgramEntry copyset_oclsrc;
extern struct cv::ocl::internal::ProgramEntry cvtclr_dx_oclsrc;
extern struct cv::ocl::internal::ProgramEntry fft_oclsrc;
extern struct cv::ocl::internal::ProgramEntry flip_oclsrc;
extern struct cv::ocl::internal::ProgramEntry gemm_oclsrc;
extern struct cv::ocl::internal::ProgramEntry halfconvert_oclsrc;
extern struct cv::ocl::internal::ProgramEntry inrange_oclsrc;
extern struct cv::ocl::internal::ProgramEntry intel_gemm_oclsrc;
extern struct cv::ocl::internal::ProgramEntry lut_oclsrc;
extern struct cv::ocl::internal::ProgramEntry meanstddev_oclsrc;
extern struct cv::ocl::internal::ProgramEntry minmaxloc_oclsrc;
extern struct cv::ocl::internal::ProgramEntry mixchannels_oclsrc;
extern struct cv::ocl::internal::ProgramEntry mulspectrums_oclsrc;
extern struct cv::ocl::internal::ProgramEntry normalize_oclsrc;
extern struct cv::ocl::internal::ProgramEntry reduce_oclsrc;
extern struct cv::ocl::internal::ProgramEntry reduce2_oclsrc;
extern struct cv::ocl::internal::ProgramEntry repeat_oclsrc;
extern struct cv::ocl::internal::ProgramEntry set_identity_oclsrc;
extern struct cv::ocl::internal::ProgramEntry split_merge_oclsrc;
extern struct cv::ocl::internal::ProgramEntry transpose_oclsrc;

}}}
#endif
