﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\alloc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\assert.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\check_cycles.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\edge.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\execution_engine.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\graph.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\memory_accessor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\memory_descriptor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\memory_descriptor_ref.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\memory_descriptor_view.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\metadata.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\metatypes.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\node.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\passes\communications.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\search.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\subgraphs.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\source\topological_sort.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\communication\callback_connector.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\communication\comm_buffer.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\communication\comm_interface.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\edge.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\execution_engine\backend.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\execution_engine\executable.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\execution_engine\execution_engine.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\graph.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\graph_listener.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\handle.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\helpers\search.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\helpers\subgraphs.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\memory\alloc.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\memory\memory_access_listener.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\memory\memory_accessor.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\memory\memory_descriptor.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\memory\memory_descriptor_ref.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\memory\memory_descriptor_view.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\memory\memory_types.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\metatypes\metatypes.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\node.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\passes\check_cycles.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\passes\communications.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\passes\pass_base.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\passes\topological_sort.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\passmanager.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\typed_graph.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\typed_metadata.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\algorithm.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\assert.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\chain_range.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\checked_cast.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\container_helper.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\filter_range.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\func_ref.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\hash.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\intrusive_list.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\iota_range.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\map_range.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\math.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\md_cast.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\md_io.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\md_size.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\md_span.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\md_view.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\memory_range.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\range.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\range_iterator.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\tuple.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\type_traits.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include\ade\util\zip_range.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{792E8A66-904F-3E91-91DB-8CBB2E5E760B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{2A318434-2169-3E81-A653-C1C0490B5893}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
