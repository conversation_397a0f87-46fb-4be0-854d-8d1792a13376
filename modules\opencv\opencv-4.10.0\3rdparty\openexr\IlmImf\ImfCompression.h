///////////////////////////////////////////////////////////////////////////
//
// Copyright (c) 2004, Industrial Light & Magic, a division of Lucas
// Digital Ltd. LLC
// 
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
// *       Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
// *       Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
// *       Neither the name of Industrial Light & Magic nor the names of
// its contributors may be used to endorse or promote products derived
// from this software without specific prior written permission. 
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
///////////////////////////////////////////////////////////////////////////



#ifndef INCLUDED_IMF_COMPRESSION_H
#define INCLUDED_IMF_COMPRESSION_H

//-----------------------------------------------------------------------------
//
//	enum Compression
//
//-----------------------------------------------------------------------------
#include "ImfNamespace.h"

OPENEXR_IMF_INTERNAL_NAMESPACE_HEADER_ENTER

enum Compression
{
    NO_COMPRESSION  = 0,	// no compression

    RLE_COMPRESSION = 1,	// run length encoding

    ZIPS_COMPRESSION = 2,	// zlib compression, one scan line at a time

    ZIP_COMPRESSION = 3,	// zlib compression, in blocks of 16 scan lines

    PIZ_COMPRESSION = 4,	// piz-based wavelet compression

    PXR24_COMPRESSION = 5,	// lossy 24-bit float compression

    B44_COMPRESSION = 6,	// lossy 4-by-4 pixel block compression,
    				// fixed compression rate

    B44A_COMPRESSION = 7,	// lossy 4-by-4 pixel block compression,
    				// flat fields are compressed more

    DWAA_COMPRESSION = 8,       // lossy DCT based compression, in blocks
                                // of 32 scanlines. More efficient for partial
                                // buffer access.

    DWAB_COMPRESSION = 9,       // lossy DCT based compression, in blocks
                                // of 256 scanlines. More efficient space
                                // wise and faster to decode full frames
                                // than DWAA_COMPRESSION.

    NUM_COMPRESSION_METHODS	// number of different compression methods
};

OPENEXR_IMF_INTERNAL_NAMESPACE_HEADER_EXIT


#endif
