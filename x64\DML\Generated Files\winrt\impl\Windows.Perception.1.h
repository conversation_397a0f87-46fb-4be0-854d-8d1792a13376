// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Perception_1_H
#define WINRT_Windows_Perception_1_H
#include "winrt/impl/Windows.Perception.0.h"
WINRT_EXPORT namespace winrt::Windows::Perception
{
    struct WINRT_IMPL_EMPTY_BASES IPerceptionTimestamp :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionTimestamp>
    {
        IPerceptionTimestamp(std::nullptr_t = nullptr) noexcept {}
        IPerceptionTimestamp(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionTimestamp2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionTimestamp2>
    {
        IPerceptionTimestamp2(std::nullptr_t = nullptr) noexcept {}
        IPerceptionTimestamp2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionTimestampHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionTimestampHelperStatics>
    {
        IPerceptionTimestampHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IPerceptionTimestampHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionTimestampHelperStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionTimestampHelperStatics2>
    {
        IPerceptionTimestampHelperStatics2(std::nullptr_t = nullptr) noexcept {}
        IPerceptionTimestampHelperStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
