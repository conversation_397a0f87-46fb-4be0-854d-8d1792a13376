// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Devices_Input_1_H
#define WINRT_Windows_Devices_Input_1_H
#include "winrt/impl/Windows.Devices.Input.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices::Input
{
    struct WINRT_IMPL_EMPTY_BASES IKeyboardCapabilities :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyboardCapabilities>
    {
        IKeyboardCapabilities(std::nullptr_t = nullptr) noexcept {}
        IKeyboardCapabilities(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMouseCapabilities :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMouseCapabilities>
    {
        IMouseCapabilities(std::nullptr_t = nullptr) noexcept {}
        IMouseCapabilities(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMouseDevice :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMouseDevice>
    {
        IMouseDevice(std::nullptr_t = nullptr) noexcept {}
        IMouseDevice(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMouseDeviceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMouseDeviceStatics>
    {
        IMouseDeviceStatics(std::nullptr_t = nullptr) noexcept {}
        IMouseDeviceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMouseEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMouseEventArgs>
    {
        IMouseEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMouseEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPenButtonListener :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPenButtonListener>
    {
        IPenButtonListener(std::nullptr_t = nullptr) noexcept {}
        IPenButtonListener(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPenButtonListenerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPenButtonListenerStatics>
    {
        IPenButtonListenerStatics(std::nullptr_t = nullptr) noexcept {}
        IPenButtonListenerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPenDevice :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPenDevice>
    {
        IPenDevice(std::nullptr_t = nullptr) noexcept {}
        IPenDevice(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPenDevice2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPenDevice2>
    {
        IPenDevice2(std::nullptr_t = nullptr) noexcept {}
        IPenDevice2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPenDeviceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPenDeviceStatics>
    {
        IPenDeviceStatics(std::nullptr_t = nullptr) noexcept {}
        IPenDeviceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPenDockListener :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPenDockListener>
    {
        IPenDockListener(std::nullptr_t = nullptr) noexcept {}
        IPenDockListener(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPenDockListenerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPenDockListenerStatics>
    {
        IPenDockListenerStatics(std::nullptr_t = nullptr) noexcept {}
        IPenDockListenerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPenDockedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPenDockedEventArgs>
    {
        IPenDockedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPenDockedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPenTailButtonClickedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPenTailButtonClickedEventArgs>
    {
        IPenTailButtonClickedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPenTailButtonClickedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPenTailButtonDoubleClickedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPenTailButtonDoubleClickedEventArgs>
    {
        IPenTailButtonDoubleClickedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPenTailButtonDoubleClickedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPenTailButtonLongPressedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPenTailButtonLongPressedEventArgs>
    {
        IPenTailButtonLongPressedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPenTailButtonLongPressedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPenUndockedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPenUndockedEventArgs>
    {
        IPenUndockedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPenUndockedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerDevice :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerDevice>
    {
        IPointerDevice(std::nullptr_t = nullptr) noexcept {}
        IPointerDevice(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerDevice2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerDevice2>
    {
        IPointerDevice2(std::nullptr_t = nullptr) noexcept {}
        IPointerDevice2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerDeviceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerDeviceStatics>
    {
        IPointerDeviceStatics(std::nullptr_t = nullptr) noexcept {}
        IPointerDeviceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITouchCapabilities :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITouchCapabilities>
    {
        ITouchCapabilities(std::nullptr_t = nullptr) noexcept {}
        ITouchCapabilities(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
