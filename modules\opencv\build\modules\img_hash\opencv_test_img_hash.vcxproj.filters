﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\test\test_average_hash.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\test\test_block_mean_hash.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\test\test_main.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\test\test_marr_hildreth_hash.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\test\test_phash.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\test\test_radial_variance_hash.cpp">
      <Filter>Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\test\test_precomp.hpp">
      <Filter>Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Include">
      <UniqueIdentifier>{0D522D53-72BF-369F-A761-1665CACACBF7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Src">
      <UniqueIdentifier>{4C321FAF-208D-3921-91F6-A2582E33B23F}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
