<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: glfw3.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_13577e2d8b9423099662de029791bd7d.html">glfw-3.4</a></li><li class="navelem"><a class="el" href="dir_b11153cd0f4fd04a7564cc166f482635.html">include</a></li><li class="navelem"><a class="el" href="dir_7f92719a7fe62e5b064f87d7a3c220b1.html">GLFW</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">glfw3.h File Reference</div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<div class="textblock"><p>This is the header file of the GLFW 3 API. It defines all its types and declares all its functions.</p>
<p>For more information about how to use this file, see <a class="el" href="build_guide.html#build_include">Including the GLFW header file</a>. </p>
</div>
<p><a href="glfw3_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:a8a8538c5500308b4211844f2fb26c7b9" id="r_a8a8538c5500308b4211844f2fb26c7b9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a8a8538c5500308b4211844f2fb26c7b9">GLFW_APIENTRY_DEFINED</a></td></tr>
<tr class="separator:a8a8538c5500308b4211844f2fb26c7b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2744fbb29b5631bb28802dbe0cf36eba" id="r_ga2744fbb29b5631bb28802dbe0cf36eba"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga2744fbb29b5631bb28802dbe0cf36eba">GLFW_TRUE</a>&#160;&#160;&#160;1</td></tr>
<tr class="memdesc:ga2744fbb29b5631bb28802dbe0cf36eba"><td class="mdescLeft">&#160;</td><td class="mdescRight">One.  <br /></td></tr>
<tr class="separator:ga2744fbb29b5631bb28802dbe0cf36eba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac877fe3b627d21ef3a0a23e0a73ba8c5" id="r_gac877fe3b627d21ef3a0a23e0a73ba8c5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gac877fe3b627d21ef3a0a23e0a73ba8c5">GLFW_FALSE</a>&#160;&#160;&#160;0</td></tr>
<tr class="memdesc:gac877fe3b627d21ef3a0a23e0a73ba8c5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Zero.  <br /></td></tr>
<tr class="separator:gac877fe3b627d21ef3a0a23e0a73ba8c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae2c0bcb7aec609e4736437554f6638fd" id="r_gae2c0bcb7aec609e4736437554f6638fd"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#gae2c0bcb7aec609e4736437554f6638fd">GLFW_HAT_CENTERED</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:gae2c0bcb7aec609e4736437554f6638fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8c9720c76cd1b912738159ed74c85b36" id="r_ga8c9720c76cd1b912738159ed74c85b36"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#ga8c9720c76cd1b912738159ed74c85b36">GLFW_HAT_UP</a>&#160;&#160;&#160;1</td></tr>
<tr class="separator:ga8c9720c76cd1b912738159ed74c85b36"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga252586e3bbde75f4b0e07ad3124867f5" id="r_ga252586e3bbde75f4b0e07ad3124867f5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#ga252586e3bbde75f4b0e07ad3124867f5">GLFW_HAT_RIGHT</a>&#160;&#160;&#160;2</td></tr>
<tr class="separator:ga252586e3bbde75f4b0e07ad3124867f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad60d1fd0dc85c18f2642cbae96d3deff" id="r_gad60d1fd0dc85c18f2642cbae96d3deff"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#gad60d1fd0dc85c18f2642cbae96d3deff">GLFW_HAT_DOWN</a>&#160;&#160;&#160;4</td></tr>
<tr class="separator:gad60d1fd0dc85c18f2642cbae96d3deff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac775f4b3154fdf5db93eb432ba546dff" id="r_gac775f4b3154fdf5db93eb432ba546dff"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#gac775f4b3154fdf5db93eb432ba546dff">GLFW_HAT_LEFT</a>&#160;&#160;&#160;8</td></tr>
<tr class="separator:gac775f4b3154fdf5db93eb432ba546dff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga94aea0ae241a8b902883536c592ee693" id="r_ga94aea0ae241a8b902883536c592ee693"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#ga94aea0ae241a8b902883536c592ee693">GLFW_HAT_RIGHT_UP</a>&#160;&#160;&#160;(<a class="el" href="group__hat__state.html#ga252586e3bbde75f4b0e07ad3124867f5">GLFW_HAT_RIGHT</a> | <a class="el" href="group__hat__state.html#ga8c9720c76cd1b912738159ed74c85b36">GLFW_HAT_UP</a>)</td></tr>
<tr class="separator:ga94aea0ae241a8b902883536c592ee693"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad7f0e4f52fd68d734863aaeadab3a3f5" id="r_gad7f0e4f52fd68d734863aaeadab3a3f5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#gad7f0e4f52fd68d734863aaeadab3a3f5">GLFW_HAT_RIGHT_DOWN</a>&#160;&#160;&#160;(<a class="el" href="group__hat__state.html#ga252586e3bbde75f4b0e07ad3124867f5">GLFW_HAT_RIGHT</a> | <a class="el" href="group__hat__state.html#gad60d1fd0dc85c18f2642cbae96d3deff">GLFW_HAT_DOWN</a>)</td></tr>
<tr class="separator:gad7f0e4f52fd68d734863aaeadab3a3f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga638f0e20dc5de90de21a33564e8ce129" id="r_ga638f0e20dc5de90de21a33564e8ce129"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#ga638f0e20dc5de90de21a33564e8ce129">GLFW_HAT_LEFT_UP</a>&#160;&#160;&#160;(<a class="el" href="group__hat__state.html#gac775f4b3154fdf5db93eb432ba546dff">GLFW_HAT_LEFT</a>  | <a class="el" href="group__hat__state.html#ga8c9720c76cd1b912738159ed74c85b36">GLFW_HAT_UP</a>)</td></tr>
<tr class="separator:ga638f0e20dc5de90de21a33564e8ce129"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga76c02baf1ea345fcbe3e8ff176a73e19" id="r_ga76c02baf1ea345fcbe3e8ff176a73e19"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#ga76c02baf1ea345fcbe3e8ff176a73e19">GLFW_HAT_LEFT_DOWN</a>&#160;&#160;&#160;(<a class="el" href="group__hat__state.html#gac775f4b3154fdf5db93eb432ba546dff">GLFW_HAT_LEFT</a>  | <a class="el" href="group__hat__state.html#gad60d1fd0dc85c18f2642cbae96d3deff">GLFW_HAT_DOWN</a>)</td></tr>
<tr class="separator:ga76c02baf1ea345fcbe3e8ff176a73e19"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga99aacc875b6b27a072552631e13775c7" id="r_ga99aacc875b6b27a072552631e13775c7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga99aacc875b6b27a072552631e13775c7">GLFW_KEY_UNKNOWN</a>&#160;&#160;&#160;-1</td></tr>
<tr class="separator:ga99aacc875b6b27a072552631e13775c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaddb2c23772b97fd7e26e8ee66f1ad014" id="r_gaddb2c23772b97fd7e26e8ee66f1ad014"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaddb2c23772b97fd7e26e8ee66f1ad014">GLFW_KEY_SPACE</a>&#160;&#160;&#160;32</td></tr>
<tr class="separator:gaddb2c23772b97fd7e26e8ee66f1ad014"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6059b0b048ba6980b6107fffbd3b4b24" id="r_ga6059b0b048ba6980b6107fffbd3b4b24"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga6059b0b048ba6980b6107fffbd3b4b24">GLFW_KEY_APOSTROPHE</a>&#160;&#160;&#160;39  /* ' */</td></tr>
<tr class="separator:ga6059b0b048ba6980b6107fffbd3b4b24"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab3d5d72e59d3055f494627b0a524926c" id="r_gab3d5d72e59d3055f494627b0a524926c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gab3d5d72e59d3055f494627b0a524926c">GLFW_KEY_COMMA</a>&#160;&#160;&#160;44  /* , */</td></tr>
<tr class="separator:gab3d5d72e59d3055f494627b0a524926c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac556b360f7f6fca4b70ba0aecf313fd4" id="r_gac556b360f7f6fca4b70ba0aecf313fd4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gac556b360f7f6fca4b70ba0aecf313fd4">GLFW_KEY_MINUS</a>&#160;&#160;&#160;45  /* - */</td></tr>
<tr class="separator:gac556b360f7f6fca4b70ba0aecf313fd4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga37e296b650eab419fc474ff69033d927" id="r_ga37e296b650eab419fc474ff69033d927"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga37e296b650eab419fc474ff69033d927">GLFW_KEY_PERIOD</a>&#160;&#160;&#160;46  /* . */</td></tr>
<tr class="separator:ga37e296b650eab419fc474ff69033d927"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadf3d753b2d479148d711de34b83fd0db" id="r_gadf3d753b2d479148d711de34b83fd0db"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gadf3d753b2d479148d711de34b83fd0db">GLFW_KEY_SLASH</a>&#160;&#160;&#160;47  /* / */</td></tr>
<tr class="separator:gadf3d753b2d479148d711de34b83fd0db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga50391730e9d7112ad4fd42d0bd1597c1" id="r_ga50391730e9d7112ad4fd42d0bd1597c1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga50391730e9d7112ad4fd42d0bd1597c1">GLFW_KEY_0</a>&#160;&#160;&#160;48</td></tr>
<tr class="separator:ga50391730e9d7112ad4fd42d0bd1597c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga05e4cae9ddb8d40cf6d82c8f11f2502f" id="r_ga05e4cae9ddb8d40cf6d82c8f11f2502f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga05e4cae9ddb8d40cf6d82c8f11f2502f">GLFW_KEY_1</a>&#160;&#160;&#160;49</td></tr>
<tr class="separator:ga05e4cae9ddb8d40cf6d82c8f11f2502f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadc8e66b3a4c4b5c39ad1305cf852863c" id="r_gadc8e66b3a4c4b5c39ad1305cf852863c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gadc8e66b3a4c4b5c39ad1305cf852863c">GLFW_KEY_2</a>&#160;&#160;&#160;50</td></tr>
<tr class="separator:gadc8e66b3a4c4b5c39ad1305cf852863c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga812f0273fe1a981e1fa002ae73e92271" id="r_ga812f0273fe1a981e1fa002ae73e92271"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga812f0273fe1a981e1fa002ae73e92271">GLFW_KEY_3</a>&#160;&#160;&#160;51</td></tr>
<tr class="separator:ga812f0273fe1a981e1fa002ae73e92271"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9e14b6975a9cc8f66cdd5cb3d3861356" id="r_ga9e14b6975a9cc8f66cdd5cb3d3861356"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga9e14b6975a9cc8f66cdd5cb3d3861356">GLFW_KEY_4</a>&#160;&#160;&#160;52</td></tr>
<tr class="separator:ga9e14b6975a9cc8f66cdd5cb3d3861356"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4d74ddaa5d4c609993b4d4a15736c924" id="r_ga4d74ddaa5d4c609993b4d4a15736c924"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga4d74ddaa5d4c609993b4d4a15736c924">GLFW_KEY_5</a>&#160;&#160;&#160;53</td></tr>
<tr class="separator:ga4d74ddaa5d4c609993b4d4a15736c924"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9ea4ab80c313a227b14d0a7c6f810b5d" id="r_ga9ea4ab80c313a227b14d0a7c6f810b5d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga9ea4ab80c313a227b14d0a7c6f810b5d">GLFW_KEY_6</a>&#160;&#160;&#160;54</td></tr>
<tr class="separator:ga9ea4ab80c313a227b14d0a7c6f810b5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab79b1cfae7bd630cfc4604c1f263c666" id="r_gab79b1cfae7bd630cfc4604c1f263c666"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gab79b1cfae7bd630cfc4604c1f263c666">GLFW_KEY_7</a>&#160;&#160;&#160;55</td></tr>
<tr class="separator:gab79b1cfae7bd630cfc4604c1f263c666"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadeaa109a0f9f5afc94fe4a108e686f6f" id="r_gadeaa109a0f9f5afc94fe4a108e686f6f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gadeaa109a0f9f5afc94fe4a108e686f6f">GLFW_KEY_8</a>&#160;&#160;&#160;56</td></tr>
<tr class="separator:gadeaa109a0f9f5afc94fe4a108e686f6f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2924cb5349ebbf97c8987f3521c44f39" id="r_ga2924cb5349ebbf97c8987f3521c44f39"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga2924cb5349ebbf97c8987f3521c44f39">GLFW_KEY_9</a>&#160;&#160;&#160;57</td></tr>
<tr class="separator:ga2924cb5349ebbf97c8987f3521c44f39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga84233de9ee5bb3e8788a5aa07d80af7d" id="r_ga84233de9ee5bb3e8788a5aa07d80af7d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga84233de9ee5bb3e8788a5aa07d80af7d">GLFW_KEY_SEMICOLON</a>&#160;&#160;&#160;59  /* ; */</td></tr>
<tr class="separator:ga84233de9ee5bb3e8788a5aa07d80af7d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae1a2de47240d6664423c204bdd91bd17" id="r_gae1a2de47240d6664423c204bdd91bd17"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gae1a2de47240d6664423c204bdd91bd17">GLFW_KEY_EQUAL</a>&#160;&#160;&#160;61  /* = */</td></tr>
<tr class="separator:gae1a2de47240d6664423c204bdd91bd17"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga03e842608e1ea323370889d33b8f70ff" id="r_ga03e842608e1ea323370889d33b8f70ff"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga03e842608e1ea323370889d33b8f70ff">GLFW_KEY_A</a>&#160;&#160;&#160;65</td></tr>
<tr class="separator:ga03e842608e1ea323370889d33b8f70ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8e3fb647ff3aca9e8dbf14fe66332941" id="r_ga8e3fb647ff3aca9e8dbf14fe66332941"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga8e3fb647ff3aca9e8dbf14fe66332941">GLFW_KEY_B</a>&#160;&#160;&#160;66</td></tr>
<tr class="separator:ga8e3fb647ff3aca9e8dbf14fe66332941"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga00ccf3475d9ee2e679480d540d554669" id="r_ga00ccf3475d9ee2e679480d540d554669"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga00ccf3475d9ee2e679480d540d554669">GLFW_KEY_C</a>&#160;&#160;&#160;67</td></tr>
<tr class="separator:ga00ccf3475d9ee2e679480d540d554669"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga011f7cdc9a654da984a2506479606933" id="r_ga011f7cdc9a654da984a2506479606933"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga011f7cdc9a654da984a2506479606933">GLFW_KEY_D</a>&#160;&#160;&#160;68</td></tr>
<tr class="separator:ga011f7cdc9a654da984a2506479606933"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabf48fcc3afbe69349df432b470c96ef2" id="r_gabf48fcc3afbe69349df432b470c96ef2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gabf48fcc3afbe69349df432b470c96ef2">GLFW_KEY_E</a>&#160;&#160;&#160;69</td></tr>
<tr class="separator:gabf48fcc3afbe69349df432b470c96ef2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5df402e02aca08444240058fd9b42a55" id="r_ga5df402e02aca08444240058fd9b42a55"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga5df402e02aca08444240058fd9b42a55">GLFW_KEY_F</a>&#160;&#160;&#160;70</td></tr>
<tr class="separator:ga5df402e02aca08444240058fd9b42a55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae74ecddf7cc96104ab23989b1cdab536" id="r_gae74ecddf7cc96104ab23989b1cdab536"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gae74ecddf7cc96104ab23989b1cdab536">GLFW_KEY_G</a>&#160;&#160;&#160;71</td></tr>
<tr class="separator:gae74ecddf7cc96104ab23989b1cdab536"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad4cc98fc8f35f015d9e2fb94bf136076" id="r_gad4cc98fc8f35f015d9e2fb94bf136076"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gad4cc98fc8f35f015d9e2fb94bf136076">GLFW_KEY_H</a>&#160;&#160;&#160;72</td></tr>
<tr class="separator:gad4cc98fc8f35f015d9e2fb94bf136076"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga274655c8bfe39742684ca393cf8ed093" id="r_ga274655c8bfe39742684ca393cf8ed093"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga274655c8bfe39742684ca393cf8ed093">GLFW_KEY_I</a>&#160;&#160;&#160;73</td></tr>
<tr class="separator:ga274655c8bfe39742684ca393cf8ed093"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga65ff2aedb129a3149ad9cb3e4159a75f" id="r_ga65ff2aedb129a3149ad9cb3e4159a75f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga65ff2aedb129a3149ad9cb3e4159a75f">GLFW_KEY_J</a>&#160;&#160;&#160;74</td></tr>
<tr class="separator:ga65ff2aedb129a3149ad9cb3e4159a75f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4ae8debadf6d2a691badae0b53ea3ba0" id="r_ga4ae8debadf6d2a691badae0b53ea3ba0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga4ae8debadf6d2a691badae0b53ea3ba0">GLFW_KEY_K</a>&#160;&#160;&#160;75</td></tr>
<tr class="separator:ga4ae8debadf6d2a691badae0b53ea3ba0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaa8b54a13f6b1eed85ac86f82d550db2" id="r_gaaa8b54a13f6b1eed85ac86f82d550db2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaaa8b54a13f6b1eed85ac86f82d550db2">GLFW_KEY_L</a>&#160;&#160;&#160;76</td></tr>
<tr class="separator:gaaa8b54a13f6b1eed85ac86f82d550db2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4d7f0260c82e4ea3d6ebc7a21d6e3716" id="r_ga4d7f0260c82e4ea3d6ebc7a21d6e3716"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga4d7f0260c82e4ea3d6ebc7a21d6e3716">GLFW_KEY_M</a>&#160;&#160;&#160;77</td></tr>
<tr class="separator:ga4d7f0260c82e4ea3d6ebc7a21d6e3716"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae00856dfeb5d13aafebf59d44de5cdda" id="r_gae00856dfeb5d13aafebf59d44de5cdda"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gae00856dfeb5d13aafebf59d44de5cdda">GLFW_KEY_N</a>&#160;&#160;&#160;78</td></tr>
<tr class="separator:gae00856dfeb5d13aafebf59d44de5cdda"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaecbbb79130df419d58dd7f09a169efe9" id="r_gaecbbb79130df419d58dd7f09a169efe9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaecbbb79130df419d58dd7f09a169efe9">GLFW_KEY_O</a>&#160;&#160;&#160;79</td></tr>
<tr class="separator:gaecbbb79130df419d58dd7f09a169efe9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8fc15819c1094fb2afa01d84546b33e1" id="r_ga8fc15819c1094fb2afa01d84546b33e1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga8fc15819c1094fb2afa01d84546b33e1">GLFW_KEY_P</a>&#160;&#160;&#160;80</td></tr>
<tr class="separator:ga8fc15819c1094fb2afa01d84546b33e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafdd01e38b120d67cf51e348bb47f3964" id="r_gafdd01e38b120d67cf51e348bb47f3964"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gafdd01e38b120d67cf51e348bb47f3964">GLFW_KEY_Q</a>&#160;&#160;&#160;81</td></tr>
<tr class="separator:gafdd01e38b120d67cf51e348bb47f3964"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4ce6c70a0c98c50b3fe4ab9a728d4d36" id="r_ga4ce6c70a0c98c50b3fe4ab9a728d4d36"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga4ce6c70a0c98c50b3fe4ab9a728d4d36">GLFW_KEY_R</a>&#160;&#160;&#160;82</td></tr>
<tr class="separator:ga4ce6c70a0c98c50b3fe4ab9a728d4d36"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1570e2ccaab036ea82bed66fc1dab2a9" id="r_ga1570e2ccaab036ea82bed66fc1dab2a9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga1570e2ccaab036ea82bed66fc1dab2a9">GLFW_KEY_S</a>&#160;&#160;&#160;83</td></tr>
<tr class="separator:ga1570e2ccaab036ea82bed66fc1dab2a9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga90e0560422ec7a30e7f3f375bc9f37f9" id="r_ga90e0560422ec7a30e7f3f375bc9f37f9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga90e0560422ec7a30e7f3f375bc9f37f9">GLFW_KEY_T</a>&#160;&#160;&#160;84</td></tr>
<tr class="separator:ga90e0560422ec7a30e7f3f375bc9f37f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacad52f3bf7d378fc0ffa72a76769256d" id="r_gacad52f3bf7d378fc0ffa72a76769256d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gacad52f3bf7d378fc0ffa72a76769256d">GLFW_KEY_U</a>&#160;&#160;&#160;85</td></tr>
<tr class="separator:gacad52f3bf7d378fc0ffa72a76769256d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga22c7763899ecf7788862e5f90eacce6b" id="r_ga22c7763899ecf7788862e5f90eacce6b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga22c7763899ecf7788862e5f90eacce6b">GLFW_KEY_V</a>&#160;&#160;&#160;86</td></tr>
<tr class="separator:ga22c7763899ecf7788862e5f90eacce6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa06a712e6202661fc03da5bdb7b6e545" id="r_gaa06a712e6202661fc03da5bdb7b6e545"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaa06a712e6202661fc03da5bdb7b6e545">GLFW_KEY_W</a>&#160;&#160;&#160;87</td></tr>
<tr class="separator:gaa06a712e6202661fc03da5bdb7b6e545"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac1c42c0bf4192cea713c55598b06b744" id="r_gac1c42c0bf4192cea713c55598b06b744"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gac1c42c0bf4192cea713c55598b06b744">GLFW_KEY_X</a>&#160;&#160;&#160;88</td></tr>
<tr class="separator:gac1c42c0bf4192cea713c55598b06b744"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafd9f115a549effdf8e372a787c360313" id="r_gafd9f115a549effdf8e372a787c360313"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gafd9f115a549effdf8e372a787c360313">GLFW_KEY_Y</a>&#160;&#160;&#160;89</td></tr>
<tr class="separator:gafd9f115a549effdf8e372a787c360313"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac489e208c26afda8d4938ed88718760a" id="r_gac489e208c26afda8d4938ed88718760a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gac489e208c26afda8d4938ed88718760a">GLFW_KEY_Z</a>&#160;&#160;&#160;90</td></tr>
<tr class="separator:gac489e208c26afda8d4938ed88718760a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad1c8d9adac53925276ecb1d592511d8a" id="r_gad1c8d9adac53925276ecb1d592511d8a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gad1c8d9adac53925276ecb1d592511d8a">GLFW_KEY_LEFT_BRACKET</a>&#160;&#160;&#160;91  /* [ */</td></tr>
<tr class="separator:gad1c8d9adac53925276ecb1d592511d8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab8155ea99d1ab27ff56f24f8dc73f8d1" id="r_gab8155ea99d1ab27ff56f24f8dc73f8d1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gab8155ea99d1ab27ff56f24f8dc73f8d1">GLFW_KEY_BACKSLASH</a>&#160;&#160;&#160;92  /* \ */</td></tr>
<tr class="separator:gab8155ea99d1ab27ff56f24f8dc73f8d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga86ef225fd6a66404caae71044cdd58d8" id="r_ga86ef225fd6a66404caae71044cdd58d8"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga86ef225fd6a66404caae71044cdd58d8">GLFW_KEY_RIGHT_BRACKET</a>&#160;&#160;&#160;93  /* ] */</td></tr>
<tr class="separator:ga86ef225fd6a66404caae71044cdd58d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7a3701fb4e2a0b136ff4b568c3c8d668" id="r_ga7a3701fb4e2a0b136ff4b568c3c8d668"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga7a3701fb4e2a0b136ff4b568c3c8d668">GLFW_KEY_GRAVE_ACCENT</a>&#160;&#160;&#160;96  /* ` */</td></tr>
<tr class="separator:ga7a3701fb4e2a0b136ff4b568c3c8d668"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadc78dad3dab76bcd4b5c20114052577a" id="r_gadc78dad3dab76bcd4b5c20114052577a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gadc78dad3dab76bcd4b5c20114052577a">GLFW_KEY_WORLD_1</a>&#160;&#160;&#160;161 /* non-US #1 */</td></tr>
<tr class="separator:gadc78dad3dab76bcd4b5c20114052577a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga20494bfebf0bb4fc9503afca18ab2c5e" id="r_ga20494bfebf0bb4fc9503afca18ab2c5e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga20494bfebf0bb4fc9503afca18ab2c5e">GLFW_KEY_WORLD_2</a>&#160;&#160;&#160;162 /* non-US #2 */</td></tr>
<tr class="separator:ga20494bfebf0bb4fc9503afca18ab2c5e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaac6596c350b635c245113b81c2123b93" id="r_gaac6596c350b635c245113b81c2123b93"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaac6596c350b635c245113b81c2123b93">GLFW_KEY_ESCAPE</a>&#160;&#160;&#160;256</td></tr>
<tr class="separator:gaac6596c350b635c245113b81c2123b93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9555a92ecbecdbc1f3435219c571d667" id="r_ga9555a92ecbecdbc1f3435219c571d667"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga9555a92ecbecdbc1f3435219c571d667">GLFW_KEY_ENTER</a>&#160;&#160;&#160;257</td></tr>
<tr class="separator:ga9555a92ecbecdbc1f3435219c571d667"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6908a4bda9950a3e2b73f794bbe985df" id="r_ga6908a4bda9950a3e2b73f794bbe985df"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga6908a4bda9950a3e2b73f794bbe985df">GLFW_KEY_TAB</a>&#160;&#160;&#160;258</td></tr>
<tr class="separator:ga6908a4bda9950a3e2b73f794bbe985df"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6c0df1fe2f156bbd5a98c66d76ff3635" id="r_ga6c0df1fe2f156bbd5a98c66d76ff3635"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga6c0df1fe2f156bbd5a98c66d76ff3635">GLFW_KEY_BACKSPACE</a>&#160;&#160;&#160;259</td></tr>
<tr class="separator:ga6c0df1fe2f156bbd5a98c66d76ff3635"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga373ac7365435d6b0eb1068f470e34f47" id="r_ga373ac7365435d6b0eb1068f470e34f47"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga373ac7365435d6b0eb1068f470e34f47">GLFW_KEY_INSERT</a>&#160;&#160;&#160;260</td></tr>
<tr class="separator:ga373ac7365435d6b0eb1068f470e34f47"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadb111e4df74b8a715f2c05dad58d2682" id="r_gadb111e4df74b8a715f2c05dad58d2682"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gadb111e4df74b8a715f2c05dad58d2682">GLFW_KEY_DELETE</a>&#160;&#160;&#160;261</td></tr>
<tr class="separator:gadb111e4df74b8a715f2c05dad58d2682"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga06ba07662e8c291a4a84535379ffc7ac" id="r_ga06ba07662e8c291a4a84535379ffc7ac"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga06ba07662e8c291a4a84535379ffc7ac">GLFW_KEY_RIGHT</a>&#160;&#160;&#160;262</td></tr>
<tr class="separator:ga06ba07662e8c291a4a84535379ffc7ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae12a010d33c309a67ab9460c51eb2462" id="r_gae12a010d33c309a67ab9460c51eb2462"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gae12a010d33c309a67ab9460c51eb2462">GLFW_KEY_LEFT</a>&#160;&#160;&#160;263</td></tr>
<tr class="separator:gae12a010d33c309a67ab9460c51eb2462"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae2e3958c71595607416aa7bf082be2f9" id="r_gae2e3958c71595607416aa7bf082be2f9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gae2e3958c71595607416aa7bf082be2f9">GLFW_KEY_DOWN</a>&#160;&#160;&#160;264</td></tr>
<tr class="separator:gae2e3958c71595607416aa7bf082be2f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2f3342b194020d3544c67e3506b6f144" id="r_ga2f3342b194020d3544c67e3506b6f144"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga2f3342b194020d3544c67e3506b6f144">GLFW_KEY_UP</a>&#160;&#160;&#160;265</td></tr>
<tr class="separator:ga2f3342b194020d3544c67e3506b6f144"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3ab731f9622f0db280178a5f3cc6d586" id="r_ga3ab731f9622f0db280178a5f3cc6d586"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga3ab731f9622f0db280178a5f3cc6d586">GLFW_KEY_PAGE_UP</a>&#160;&#160;&#160;266</td></tr>
<tr class="separator:ga3ab731f9622f0db280178a5f3cc6d586"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaee0a8fa442001cc2147812f84b59041c" id="r_gaee0a8fa442001cc2147812f84b59041c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaee0a8fa442001cc2147812f84b59041c">GLFW_KEY_PAGE_DOWN</a>&#160;&#160;&#160;267</td></tr>
<tr class="separator:gaee0a8fa442001cc2147812f84b59041c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga41452c7287195d481e43207318c126a7" id="r_ga41452c7287195d481e43207318c126a7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga41452c7287195d481e43207318c126a7">GLFW_KEY_HOME</a>&#160;&#160;&#160;268</td></tr>
<tr class="separator:ga41452c7287195d481e43207318c126a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga86587ea1df19a65978d3e3b8439bedd9" id="r_ga86587ea1df19a65978d3e3b8439bedd9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga86587ea1df19a65978d3e3b8439bedd9">GLFW_KEY_END</a>&#160;&#160;&#160;269</td></tr>
<tr class="separator:ga86587ea1df19a65978d3e3b8439bedd9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga92c1d2c9d63485f3d70f94f688d48672" id="r_ga92c1d2c9d63485f3d70f94f688d48672"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga92c1d2c9d63485f3d70f94f688d48672">GLFW_KEY_CAPS_LOCK</a>&#160;&#160;&#160;280</td></tr>
<tr class="separator:ga92c1d2c9d63485f3d70f94f688d48672"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf622b63b9537f7084c2ab649b8365630" id="r_gaf622b63b9537f7084c2ab649b8365630"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaf622b63b9537f7084c2ab649b8365630">GLFW_KEY_SCROLL_LOCK</a>&#160;&#160;&#160;281</td></tr>
<tr class="separator:gaf622b63b9537f7084c2ab649b8365630"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3946edc362aeff213b2be6304296cf43" id="r_ga3946edc362aeff213b2be6304296cf43"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga3946edc362aeff213b2be6304296cf43">GLFW_KEY_NUM_LOCK</a>&#160;&#160;&#160;282</td></tr>
<tr class="separator:ga3946edc362aeff213b2be6304296cf43"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf964c2e65e97d0cf785a5636ee8df642" id="r_gaf964c2e65e97d0cf785a5636ee8df642"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaf964c2e65e97d0cf785a5636ee8df642">GLFW_KEY_PRINT_SCREEN</a>&#160;&#160;&#160;283</td></tr>
<tr class="separator:gaf964c2e65e97d0cf785a5636ee8df642"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8116b9692d87382afb5849b6d8907f18" id="r_ga8116b9692d87382afb5849b6d8907f18"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga8116b9692d87382afb5849b6d8907f18">GLFW_KEY_PAUSE</a>&#160;&#160;&#160;284</td></tr>
<tr class="separator:ga8116b9692d87382afb5849b6d8907f18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafb8d66c573acf22e364049477dcbea30" id="r_gafb8d66c573acf22e364049477dcbea30"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gafb8d66c573acf22e364049477dcbea30">GLFW_KEY_F1</a>&#160;&#160;&#160;290</td></tr>
<tr class="separator:gafb8d66c573acf22e364049477dcbea30"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0900750aff94889b940f5e428c07daee" id="r_ga0900750aff94889b940f5e428c07daee"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga0900750aff94889b940f5e428c07daee">GLFW_KEY_F2</a>&#160;&#160;&#160;291</td></tr>
<tr class="separator:ga0900750aff94889b940f5e428c07daee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaed7cd729c0147a551bb8b7bb36c17015" id="r_gaed7cd729c0147a551bb8b7bb36c17015"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaed7cd729c0147a551bb8b7bb36c17015">GLFW_KEY_F3</a>&#160;&#160;&#160;292</td></tr>
<tr class="separator:gaed7cd729c0147a551bb8b7bb36c17015"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9b61ebd0c63b44b7332fda2c9763eaa6" id="r_ga9b61ebd0c63b44b7332fda2c9763eaa6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga9b61ebd0c63b44b7332fda2c9763eaa6">GLFW_KEY_F4</a>&#160;&#160;&#160;293</td></tr>
<tr class="separator:ga9b61ebd0c63b44b7332fda2c9763eaa6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf258dda9947daa428377938ed577c8c2" id="r_gaf258dda9947daa428377938ed577c8c2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaf258dda9947daa428377938ed577c8c2">GLFW_KEY_F5</a>&#160;&#160;&#160;294</td></tr>
<tr class="separator:gaf258dda9947daa428377938ed577c8c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6dc2d3f87b9d51ffbbbe2ef0299d8e1d" id="r_ga6dc2d3f87b9d51ffbbbe2ef0299d8e1d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga6dc2d3f87b9d51ffbbbe2ef0299d8e1d">GLFW_KEY_F6</a>&#160;&#160;&#160;295</td></tr>
<tr class="separator:ga6dc2d3f87b9d51ffbbbe2ef0299d8e1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacca6ef8a2162c52a0ac1d881e8d9c38a" id="r_gacca6ef8a2162c52a0ac1d881e8d9c38a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gacca6ef8a2162c52a0ac1d881e8d9c38a">GLFW_KEY_F7</a>&#160;&#160;&#160;296</td></tr>
<tr class="separator:gacca6ef8a2162c52a0ac1d881e8d9c38a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac9d39390336ae14e4a93e295de43c7e8" id="r_gac9d39390336ae14e4a93e295de43c7e8"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gac9d39390336ae14e4a93e295de43c7e8">GLFW_KEY_F8</a>&#160;&#160;&#160;297</td></tr>
<tr class="separator:gac9d39390336ae14e4a93e295de43c7e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae40de0de1c9f21cd26c9afa3d7050851" id="r_gae40de0de1c9f21cd26c9afa3d7050851"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gae40de0de1c9f21cd26c9afa3d7050851">GLFW_KEY_F9</a>&#160;&#160;&#160;298</td></tr>
<tr class="separator:gae40de0de1c9f21cd26c9afa3d7050851"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga718d11d2f7d57471a2f6a894235995b1" id="r_ga718d11d2f7d57471a2f6a894235995b1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga718d11d2f7d57471a2f6a894235995b1">GLFW_KEY_F10</a>&#160;&#160;&#160;299</td></tr>
<tr class="separator:ga718d11d2f7d57471a2f6a894235995b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0bc04b11627e7d69339151e7306b2832" id="r_ga0bc04b11627e7d69339151e7306b2832"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga0bc04b11627e7d69339151e7306b2832">GLFW_KEY_F11</a>&#160;&#160;&#160;300</td></tr>
<tr class="separator:ga0bc04b11627e7d69339151e7306b2832"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf5908fa9b0a906ae03fc2c61ac7aa3e2" id="r_gaf5908fa9b0a906ae03fc2c61ac7aa3e2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaf5908fa9b0a906ae03fc2c61ac7aa3e2">GLFW_KEY_F12</a>&#160;&#160;&#160;301</td></tr>
<tr class="separator:gaf5908fa9b0a906ae03fc2c61ac7aa3e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad637f4308655e1001bd6ad942bc0fd4b" id="r_gad637f4308655e1001bd6ad942bc0fd4b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gad637f4308655e1001bd6ad942bc0fd4b">GLFW_KEY_F13</a>&#160;&#160;&#160;302</td></tr>
<tr class="separator:gad637f4308655e1001bd6ad942bc0fd4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf14c66cff3396e5bd46e803c035e6c1f" id="r_gaf14c66cff3396e5bd46e803c035e6c1f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaf14c66cff3396e5bd46e803c035e6c1f">GLFW_KEY_F14</a>&#160;&#160;&#160;303</td></tr>
<tr class="separator:gaf14c66cff3396e5bd46e803c035e6c1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f70970db6e8be1794da8516a6d14058" id="r_ga7f70970db6e8be1794da8516a6d14058"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga7f70970db6e8be1794da8516a6d14058">GLFW_KEY_F15</a>&#160;&#160;&#160;304</td></tr>
<tr class="separator:ga7f70970db6e8be1794da8516a6d14058"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa582dbb1d2ba2050aa1dca0838095b27" id="r_gaa582dbb1d2ba2050aa1dca0838095b27"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaa582dbb1d2ba2050aa1dca0838095b27">GLFW_KEY_F16</a>&#160;&#160;&#160;305</td></tr>
<tr class="separator:gaa582dbb1d2ba2050aa1dca0838095b27"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga972ce5c365e2394b36104b0e3125c748" id="r_ga972ce5c365e2394b36104b0e3125c748"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga972ce5c365e2394b36104b0e3125c748">GLFW_KEY_F17</a>&#160;&#160;&#160;306</td></tr>
<tr class="separator:ga972ce5c365e2394b36104b0e3125c748"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaebf6391058d5566601e357edc5ea737c" id="r_gaebf6391058d5566601e357edc5ea737c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaebf6391058d5566601e357edc5ea737c">GLFW_KEY_F18</a>&#160;&#160;&#160;307</td></tr>
<tr class="separator:gaebf6391058d5566601e357edc5ea737c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaec011d9ba044058cb54529da710e9791" id="r_gaec011d9ba044058cb54529da710e9791"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaec011d9ba044058cb54529da710e9791">GLFW_KEY_F19</a>&#160;&#160;&#160;308</td></tr>
<tr class="separator:gaec011d9ba044058cb54529da710e9791"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga82b9c721ada04cd5ca8de767da38022f" id="r_ga82b9c721ada04cd5ca8de767da38022f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga82b9c721ada04cd5ca8de767da38022f">GLFW_KEY_F20</a>&#160;&#160;&#160;309</td></tr>
<tr class="separator:ga82b9c721ada04cd5ca8de767da38022f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga356afb14d3440ff2bb378f74f7ebc60f" id="r_ga356afb14d3440ff2bb378f74f7ebc60f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga356afb14d3440ff2bb378f74f7ebc60f">GLFW_KEY_F21</a>&#160;&#160;&#160;310</td></tr>
<tr class="separator:ga356afb14d3440ff2bb378f74f7ebc60f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga90960bd2a155f2b09675324d3dff1565" id="r_ga90960bd2a155f2b09675324d3dff1565"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga90960bd2a155f2b09675324d3dff1565">GLFW_KEY_F22</a>&#160;&#160;&#160;311</td></tr>
<tr class="separator:ga90960bd2a155f2b09675324d3dff1565"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga43c21099aac10952d1be909a8ddee4d5" id="r_ga43c21099aac10952d1be909a8ddee4d5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga43c21099aac10952d1be909a8ddee4d5">GLFW_KEY_F23</a>&#160;&#160;&#160;312</td></tr>
<tr class="separator:ga43c21099aac10952d1be909a8ddee4d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8150374677b5bed3043408732152dea2" id="r_ga8150374677b5bed3043408732152dea2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga8150374677b5bed3043408732152dea2">GLFW_KEY_F24</a>&#160;&#160;&#160;313</td></tr>
<tr class="separator:ga8150374677b5bed3043408732152dea2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa4bbd93ed73bb4c6ae7d83df880b7199" id="r_gaa4bbd93ed73bb4c6ae7d83df880b7199"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaa4bbd93ed73bb4c6ae7d83df880b7199">GLFW_KEY_F25</a>&#160;&#160;&#160;314</td></tr>
<tr class="separator:gaa4bbd93ed73bb4c6ae7d83df880b7199"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga10515dafc55b71e7683f5b4fedd1c70d" id="r_ga10515dafc55b71e7683f5b4fedd1c70d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga10515dafc55b71e7683f5b4fedd1c70d">GLFW_KEY_KP_0</a>&#160;&#160;&#160;320</td></tr>
<tr class="separator:ga10515dafc55b71e7683f5b4fedd1c70d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf3a29a334402c5eaf0b3439edf5587c3" id="r_gaf3a29a334402c5eaf0b3439edf5587c3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaf3a29a334402c5eaf0b3439edf5587c3">GLFW_KEY_KP_1</a>&#160;&#160;&#160;321</td></tr>
<tr class="separator:gaf3a29a334402c5eaf0b3439edf5587c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf82d5a802ab8213c72653d7480c16f13" id="r_gaf82d5a802ab8213c72653d7480c16f13"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaf82d5a802ab8213c72653d7480c16f13">GLFW_KEY_KP_2</a>&#160;&#160;&#160;322</td></tr>
<tr class="separator:gaf82d5a802ab8213c72653d7480c16f13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7e25ff30d56cd512828c1d4ae8d54ef2" id="r_ga7e25ff30d56cd512828c1d4ae8d54ef2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga7e25ff30d56cd512828c1d4ae8d54ef2">GLFW_KEY_KP_3</a>&#160;&#160;&#160;323</td></tr>
<tr class="separator:ga7e25ff30d56cd512828c1d4ae8d54ef2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gada7ec86778b85e0b4de0beea72234aea" id="r_gada7ec86778b85e0b4de0beea72234aea"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gada7ec86778b85e0b4de0beea72234aea">GLFW_KEY_KP_4</a>&#160;&#160;&#160;324</td></tr>
<tr class="separator:gada7ec86778b85e0b4de0beea72234aea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9a5be274434866c51738cafbb6d26b45" id="r_ga9a5be274434866c51738cafbb6d26b45"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga9a5be274434866c51738cafbb6d26b45">GLFW_KEY_KP_5</a>&#160;&#160;&#160;325</td></tr>
<tr class="separator:ga9a5be274434866c51738cafbb6d26b45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafc141b0f8450519084c01092a3157faa" id="r_gafc141b0f8450519084c01092a3157faa"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gafc141b0f8450519084c01092a3157faa">GLFW_KEY_KP_6</a>&#160;&#160;&#160;326</td></tr>
<tr class="separator:gafc141b0f8450519084c01092a3157faa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8882f411f05d04ec77a9563974bbfa53" id="r_ga8882f411f05d04ec77a9563974bbfa53"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga8882f411f05d04ec77a9563974bbfa53">GLFW_KEY_KP_7</a>&#160;&#160;&#160;327</td></tr>
<tr class="separator:ga8882f411f05d04ec77a9563974bbfa53"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab2ea2e6a12f89d315045af520ac78cec" id="r_gab2ea2e6a12f89d315045af520ac78cec"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gab2ea2e6a12f89d315045af520ac78cec">GLFW_KEY_KP_8</a>&#160;&#160;&#160;328</td></tr>
<tr class="separator:gab2ea2e6a12f89d315045af520ac78cec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafb21426b630ed4fcc084868699ba74c1" id="r_gafb21426b630ed4fcc084868699ba74c1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gafb21426b630ed4fcc084868699ba74c1">GLFW_KEY_KP_9</a>&#160;&#160;&#160;329</td></tr>
<tr class="separator:gafb21426b630ed4fcc084868699ba74c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4e231d968796331a9ea0dbfb98d4005b" id="r_ga4e231d968796331a9ea0dbfb98d4005b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga4e231d968796331a9ea0dbfb98d4005b">GLFW_KEY_KP_DECIMAL</a>&#160;&#160;&#160;330</td></tr>
<tr class="separator:ga4e231d968796331a9ea0dbfb98d4005b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabca1733780a273d549129ad0f250d1e5" id="r_gabca1733780a273d549129ad0f250d1e5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gabca1733780a273d549129ad0f250d1e5">GLFW_KEY_KP_DIVIDE</a>&#160;&#160;&#160;331</td></tr>
<tr class="separator:gabca1733780a273d549129ad0f250d1e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9ada267eb0e78ed2ada8701dd24a56ef" id="r_ga9ada267eb0e78ed2ada8701dd24a56ef"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga9ada267eb0e78ed2ada8701dd24a56ef">GLFW_KEY_KP_MULTIPLY</a>&#160;&#160;&#160;332</td></tr>
<tr class="separator:ga9ada267eb0e78ed2ada8701dd24a56ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa3dbd60782ff93d6082a124bce1fa236" id="r_gaa3dbd60782ff93d6082a124bce1fa236"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaa3dbd60782ff93d6082a124bce1fa236">GLFW_KEY_KP_SUBTRACT</a>&#160;&#160;&#160;333</td></tr>
<tr class="separator:gaa3dbd60782ff93d6082a124bce1fa236"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad09c7c98acc79e89aa6a0a91275becac" id="r_gad09c7c98acc79e89aa6a0a91275becac"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gad09c7c98acc79e89aa6a0a91275becac">GLFW_KEY_KP_ADD</a>&#160;&#160;&#160;334</td></tr>
<tr class="separator:gad09c7c98acc79e89aa6a0a91275becac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4f728f8738f2986bd63eedd3d412e8cf" id="r_ga4f728f8738f2986bd63eedd3d412e8cf"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga4f728f8738f2986bd63eedd3d412e8cf">GLFW_KEY_KP_ENTER</a>&#160;&#160;&#160;335</td></tr>
<tr class="separator:ga4f728f8738f2986bd63eedd3d412e8cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaebdc76d4a808191e6d21b7e4ad2acd97" id="r_gaebdc76d4a808191e6d21b7e4ad2acd97"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaebdc76d4a808191e6d21b7e4ad2acd97">GLFW_KEY_KP_EQUAL</a>&#160;&#160;&#160;336</td></tr>
<tr class="separator:gaebdc76d4a808191e6d21b7e4ad2acd97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8a530a28a65c44ab5d00b759b756d3f6" id="r_ga8a530a28a65c44ab5d00b759b756d3f6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga8a530a28a65c44ab5d00b759b756d3f6">GLFW_KEY_LEFT_SHIFT</a>&#160;&#160;&#160;340</td></tr>
<tr class="separator:ga8a530a28a65c44ab5d00b759b756d3f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9f97b743e81460ac4b2deddecd10a464" id="r_ga9f97b743e81460ac4b2deddecd10a464"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga9f97b743e81460ac4b2deddecd10a464">GLFW_KEY_LEFT_CONTROL</a>&#160;&#160;&#160;341</td></tr>
<tr class="separator:ga9f97b743e81460ac4b2deddecd10a464"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f27dabf63a7789daa31e1c96790219b" id="r_ga7f27dabf63a7789daa31e1c96790219b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga7f27dabf63a7789daa31e1c96790219b">GLFW_KEY_LEFT_ALT</a>&#160;&#160;&#160;342</td></tr>
<tr class="separator:ga7f27dabf63a7789daa31e1c96790219b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafb1207c91997fc295afd1835fbc5641a" id="r_gafb1207c91997fc295afd1835fbc5641a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gafb1207c91997fc295afd1835fbc5641a">GLFW_KEY_LEFT_SUPER</a>&#160;&#160;&#160;343</td></tr>
<tr class="separator:gafb1207c91997fc295afd1835fbc5641a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaffca36b99c9dce1a19cb9befbadce691" id="r_gaffca36b99c9dce1a19cb9befbadce691"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gaffca36b99c9dce1a19cb9befbadce691">GLFW_KEY_RIGHT_SHIFT</a>&#160;&#160;&#160;344</td></tr>
<tr class="separator:gaffca36b99c9dce1a19cb9befbadce691"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad1ca2094b2694e7251d0ab1fd34f8519" id="r_gad1ca2094b2694e7251d0ab1fd34f8519"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gad1ca2094b2694e7251d0ab1fd34f8519">GLFW_KEY_RIGHT_CONTROL</a>&#160;&#160;&#160;345</td></tr>
<tr class="separator:gad1ca2094b2694e7251d0ab1fd34f8519"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga687b38009131cfdd07a8d05fff8fa446" id="r_ga687b38009131cfdd07a8d05fff8fa446"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga687b38009131cfdd07a8d05fff8fa446">GLFW_KEY_RIGHT_ALT</a>&#160;&#160;&#160;346</td></tr>
<tr class="separator:ga687b38009131cfdd07a8d05fff8fa446"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad4547a3e8e247594acb60423fe6502db" id="r_gad4547a3e8e247594acb60423fe6502db"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#gad4547a3e8e247594acb60423fe6502db">GLFW_KEY_RIGHT_SUPER</a>&#160;&#160;&#160;347</td></tr>
<tr class="separator:gad4547a3e8e247594acb60423fe6502db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9845be48a745fc232045c9ec174d8820" id="r_ga9845be48a745fc232045c9ec174d8820"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga9845be48a745fc232045c9ec174d8820">GLFW_KEY_MENU</a>&#160;&#160;&#160;348</td></tr>
<tr class="separator:ga9845be48a745fc232045c9ec174d8820"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga442cbaef7bfb9a4ba13594dd7fbf2789" id="r_ga442cbaef7bfb9a4ba13594dd7fbf2789"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html#ga442cbaef7bfb9a4ba13594dd7fbf2789">GLFW_KEY_LAST</a>&#160;&#160;&#160;<a class="el" href="group__keys.html#ga9845be48a745fc232045c9ec174d8820">GLFW_KEY_MENU</a></td></tr>
<tr class="separator:ga442cbaef7bfb9a4ba13594dd7fbf2789"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga14994d3196c290aaa347248e51740274" id="r_ga14994d3196c290aaa347248e51740274"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__mods.html#ga14994d3196c290aaa347248e51740274">GLFW_MOD_SHIFT</a>&#160;&#160;&#160;0x0001</td></tr>
<tr class="memdesc:ga14994d3196c290aaa347248e51740274"><td class="mdescLeft">&#160;</td><td class="mdescRight">If this bit is set one or more Shift keys were held down.  <br /></td></tr>
<tr class="separator:ga14994d3196c290aaa347248e51740274"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6ed94871c3208eefd85713fa929d45aa" id="r_ga6ed94871c3208eefd85713fa929d45aa"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__mods.html#ga6ed94871c3208eefd85713fa929d45aa">GLFW_MOD_CONTROL</a>&#160;&#160;&#160;0x0002</td></tr>
<tr class="memdesc:ga6ed94871c3208eefd85713fa929d45aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">If this bit is set one or more Control keys were held down.  <br /></td></tr>
<tr class="separator:ga6ed94871c3208eefd85713fa929d45aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad2acd5633463c29e07008687ea73c0f4" id="r_gad2acd5633463c29e07008687ea73c0f4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__mods.html#gad2acd5633463c29e07008687ea73c0f4">GLFW_MOD_ALT</a>&#160;&#160;&#160;0x0004</td></tr>
<tr class="memdesc:gad2acd5633463c29e07008687ea73c0f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">If this bit is set one or more Alt keys were held down.  <br /></td></tr>
<tr class="separator:gad2acd5633463c29e07008687ea73c0f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6b64ba10ea0227cf6f42efd0a220aba1" id="r_ga6b64ba10ea0227cf6f42efd0a220aba1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__mods.html#ga6b64ba10ea0227cf6f42efd0a220aba1">GLFW_MOD_SUPER</a>&#160;&#160;&#160;0x0008</td></tr>
<tr class="memdesc:ga6b64ba10ea0227cf6f42efd0a220aba1"><td class="mdescLeft">&#160;</td><td class="mdescRight">If this bit is set one or more Super keys were held down.  <br /></td></tr>
<tr class="separator:ga6b64ba10ea0227cf6f42efd0a220aba1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaefeef8fcf825a6e43e241b337897200f" id="r_gaefeef8fcf825a6e43e241b337897200f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__mods.html#gaefeef8fcf825a6e43e241b337897200f">GLFW_MOD_CAPS_LOCK</a>&#160;&#160;&#160;0x0010</td></tr>
<tr class="memdesc:gaefeef8fcf825a6e43e241b337897200f"><td class="mdescLeft">&#160;</td><td class="mdescRight">If this bit is set the Caps Lock key is enabled.  <br /></td></tr>
<tr class="separator:gaefeef8fcf825a6e43e241b337897200f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga64e020b8a42af8376e944baf61feecbe" id="r_ga64e020b8a42af8376e944baf61feecbe"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__mods.html#ga64e020b8a42af8376e944baf61feecbe">GLFW_MOD_NUM_LOCK</a>&#160;&#160;&#160;0x0020</td></tr>
<tr class="memdesc:ga64e020b8a42af8376e944baf61feecbe"><td class="mdescLeft">&#160;</td><td class="mdescRight">If this bit is set the Num Lock key is enabled.  <br /></td></tr>
<tr class="separator:ga64e020b8a42af8376e944baf61feecbe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga181a6e875251fd8671654eff00f9112e" id="r_ga181a6e875251fd8671654eff00f9112e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#ga181a6e875251fd8671654eff00f9112e">GLFW_MOUSE_BUTTON_1</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:ga181a6e875251fd8671654eff00f9112e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga604b39b92c88ce9bd332e97fc3f4156c" id="r_ga604b39b92c88ce9bd332e97fc3f4156c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#ga604b39b92c88ce9bd332e97fc3f4156c">GLFW_MOUSE_BUTTON_2</a>&#160;&#160;&#160;1</td></tr>
<tr class="separator:ga604b39b92c88ce9bd332e97fc3f4156c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0130d505563d0236a6f85545f19e1721" id="r_ga0130d505563d0236a6f85545f19e1721"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#ga0130d505563d0236a6f85545f19e1721">GLFW_MOUSE_BUTTON_3</a>&#160;&#160;&#160;2</td></tr>
<tr class="separator:ga0130d505563d0236a6f85545f19e1721"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga53f4097bb01d5521c7d9513418c91ca9" id="r_ga53f4097bb01d5521c7d9513418c91ca9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#ga53f4097bb01d5521c7d9513418c91ca9">GLFW_MOUSE_BUTTON_4</a>&#160;&#160;&#160;3</td></tr>
<tr class="separator:ga53f4097bb01d5521c7d9513418c91ca9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf08c4ddecb051d3d9667db1d5e417c9c" id="r_gaf08c4ddecb051d3d9667db1d5e417c9c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#gaf08c4ddecb051d3d9667db1d5e417c9c">GLFW_MOUSE_BUTTON_5</a>&#160;&#160;&#160;4</td></tr>
<tr class="separator:gaf08c4ddecb051d3d9667db1d5e417c9c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae8513e06aab8aa393b595f22c6d8257a" id="r_gae8513e06aab8aa393b595f22c6d8257a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#gae8513e06aab8aa393b595f22c6d8257a">GLFW_MOUSE_BUTTON_6</a>&#160;&#160;&#160;5</td></tr>
<tr class="separator:gae8513e06aab8aa393b595f22c6d8257a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8b02a1ab55dde45b3a3883d54ffd7dc7" id="r_ga8b02a1ab55dde45b3a3883d54ffd7dc7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#ga8b02a1ab55dde45b3a3883d54ffd7dc7">GLFW_MOUSE_BUTTON_7</a>&#160;&#160;&#160;6</td></tr>
<tr class="separator:ga8b02a1ab55dde45b3a3883d54ffd7dc7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga35d5c4263e0dc0d0a4731ca6c562f32c" id="r_ga35d5c4263e0dc0d0a4731ca6c562f32c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#ga35d5c4263e0dc0d0a4731ca6c562f32c">GLFW_MOUSE_BUTTON_8</a>&#160;&#160;&#160;7</td></tr>
<tr class="separator:ga35d5c4263e0dc0d0a4731ca6c562f32c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab1fd86a4518a9141ec7bcde2e15a2fdf" id="r_gab1fd86a4518a9141ec7bcde2e15a2fdf"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#gab1fd86a4518a9141ec7bcde2e15a2fdf">GLFW_MOUSE_BUTTON_LAST</a>&#160;&#160;&#160;<a class="el" href="group__buttons.html#ga35d5c4263e0dc0d0a4731ca6c562f32c">GLFW_MOUSE_BUTTON_8</a></td></tr>
<tr class="separator:gab1fd86a4518a9141ec7bcde2e15a2fdf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf37100431dcd5082d48f95ee8bc8cd56" id="r_gaf37100431dcd5082d48f95ee8bc8cd56"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#gaf37100431dcd5082d48f95ee8bc8cd56">GLFW_MOUSE_BUTTON_LEFT</a>&#160;&#160;&#160;<a class="el" href="group__buttons.html#ga181a6e875251fd8671654eff00f9112e">GLFW_MOUSE_BUTTON_1</a></td></tr>
<tr class="separator:gaf37100431dcd5082d48f95ee8bc8cd56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3e2f2cf3c4942df73cc094247d275e74" id="r_ga3e2f2cf3c4942df73cc094247d275e74"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#ga3e2f2cf3c4942df73cc094247d275e74">GLFW_MOUSE_BUTTON_RIGHT</a>&#160;&#160;&#160;<a class="el" href="group__buttons.html#ga604b39b92c88ce9bd332e97fc3f4156c">GLFW_MOUSE_BUTTON_2</a></td></tr>
<tr class="separator:ga3e2f2cf3c4942df73cc094247d275e74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga34a4d2a701434f763fd93a2ff842b95a" id="r_ga34a4d2a701434f763fd93a2ff842b95a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#ga34a4d2a701434f763fd93a2ff842b95a">GLFW_MOUSE_BUTTON_MIDDLE</a>&#160;&#160;&#160;<a class="el" href="group__buttons.html#ga0130d505563d0236a6f85545f19e1721">GLFW_MOUSE_BUTTON_3</a></td></tr>
<tr class="separator:ga34a4d2a701434f763fd93a2ff842b95a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga34a0443d059e9f22272cd4669073f73d" id="r_ga34a0443d059e9f22272cd4669073f73d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga34a0443d059e9f22272cd4669073f73d">GLFW_JOYSTICK_1</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:ga34a0443d059e9f22272cd4669073f73d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6eab65ec88e65e0850ef8413504cb50c" id="r_ga6eab65ec88e65e0850ef8413504cb50c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga6eab65ec88e65e0850ef8413504cb50c">GLFW_JOYSTICK_2</a>&#160;&#160;&#160;1</td></tr>
<tr class="separator:ga6eab65ec88e65e0850ef8413504cb50c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae6f3eedfeb42424c2f5e3161efb0b654" id="r_gae6f3eedfeb42424c2f5e3161efb0b654"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#gae6f3eedfeb42424c2f5e3161efb0b654">GLFW_JOYSTICK_3</a>&#160;&#160;&#160;2</td></tr>
<tr class="separator:gae6f3eedfeb42424c2f5e3161efb0b654"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga97ddbcad02b7f48d74fad4ddb08fff59" id="r_ga97ddbcad02b7f48d74fad4ddb08fff59"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga97ddbcad02b7f48d74fad4ddb08fff59">GLFW_JOYSTICK_4</a>&#160;&#160;&#160;3</td></tr>
<tr class="separator:ga97ddbcad02b7f48d74fad4ddb08fff59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae43281bc66d3fa5089fb50c3e7a28695" id="r_gae43281bc66d3fa5089fb50c3e7a28695"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#gae43281bc66d3fa5089fb50c3e7a28695">GLFW_JOYSTICK_5</a>&#160;&#160;&#160;4</td></tr>
<tr class="separator:gae43281bc66d3fa5089fb50c3e7a28695"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga74771620aa53bd68a487186dea66fd77" id="r_ga74771620aa53bd68a487186dea66fd77"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga74771620aa53bd68a487186dea66fd77">GLFW_JOYSTICK_6</a>&#160;&#160;&#160;5</td></tr>
<tr class="separator:ga74771620aa53bd68a487186dea66fd77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga20a9f4f3aaefed9ea5e66072fc588b87" id="r_ga20a9f4f3aaefed9ea5e66072fc588b87"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga20a9f4f3aaefed9ea5e66072fc588b87">GLFW_JOYSTICK_7</a>&#160;&#160;&#160;6</td></tr>
<tr class="separator:ga20a9f4f3aaefed9ea5e66072fc588b87"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga21a934c940bcf25db0e4c8fe9b364bdb" id="r_ga21a934c940bcf25db0e4c8fe9b364bdb"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga21a934c940bcf25db0e4c8fe9b364bdb">GLFW_JOYSTICK_8</a>&#160;&#160;&#160;7</td></tr>
<tr class="separator:ga21a934c940bcf25db0e4c8fe9b364bdb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga87689d47df0ba6f9f5fcbbcaf7b3cecf" id="r_ga87689d47df0ba6f9f5fcbbcaf7b3cecf"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga87689d47df0ba6f9f5fcbbcaf7b3cecf">GLFW_JOYSTICK_9</a>&#160;&#160;&#160;8</td></tr>
<tr class="separator:ga87689d47df0ba6f9f5fcbbcaf7b3cecf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaef55389ee605d6dfc31aef6fe98c54ec" id="r_gaef55389ee605d6dfc31aef6fe98c54ec"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#gaef55389ee605d6dfc31aef6fe98c54ec">GLFW_JOYSTICK_10</a>&#160;&#160;&#160;9</td></tr>
<tr class="separator:gaef55389ee605d6dfc31aef6fe98c54ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae7d26e3df447c2c14a569fcc18516af4" id="r_gae7d26e3df447c2c14a569fcc18516af4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#gae7d26e3df447c2c14a569fcc18516af4">GLFW_JOYSTICK_11</a>&#160;&#160;&#160;10</td></tr>
<tr class="separator:gae7d26e3df447c2c14a569fcc18516af4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab91bbf5b7ca6be8d3ac5c4d89ff48ac7" id="r_gab91bbf5b7ca6be8d3ac5c4d89ff48ac7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#gab91bbf5b7ca6be8d3ac5c4d89ff48ac7">GLFW_JOYSTICK_12</a>&#160;&#160;&#160;11</td></tr>
<tr class="separator:gab91bbf5b7ca6be8d3ac5c4d89ff48ac7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5c84fb4e49bf661d7d7c78eb4018c508" id="r_ga5c84fb4e49bf661d7d7c78eb4018c508"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga5c84fb4e49bf661d7d7c78eb4018c508">GLFW_JOYSTICK_13</a>&#160;&#160;&#160;12</td></tr>
<tr class="separator:ga5c84fb4e49bf661d7d7c78eb4018c508"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga89540873278ae5a42b3e70d64164dc74" id="r_ga89540873278ae5a42b3e70d64164dc74"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga89540873278ae5a42b3e70d64164dc74">GLFW_JOYSTICK_14</a>&#160;&#160;&#160;13</td></tr>
<tr class="separator:ga89540873278ae5a42b3e70d64164dc74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7b02ab70daf7a78bcc942d5d4cc1dcf9" id="r_ga7b02ab70daf7a78bcc942d5d4cc1dcf9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga7b02ab70daf7a78bcc942d5d4cc1dcf9">GLFW_JOYSTICK_15</a>&#160;&#160;&#160;14</td></tr>
<tr class="separator:ga7b02ab70daf7a78bcc942d5d4cc1dcf9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga453edeeabf350827646b6857df4f80ce" id="r_ga453edeeabf350827646b6857df4f80ce"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga453edeeabf350827646b6857df4f80ce">GLFW_JOYSTICK_16</a>&#160;&#160;&#160;15</td></tr>
<tr class="separator:ga453edeeabf350827646b6857df4f80ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9ca13ebf24c331dd98df17d84a4b72c9" id="r_ga9ca13ebf24c331dd98df17d84a4b72c9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga9ca13ebf24c331dd98df17d84a4b72c9">GLFW_JOYSTICK_LAST</a>&#160;&#160;&#160;<a class="el" href="group__joysticks.html#ga453edeeabf350827646b6857df4f80ce">GLFW_JOYSTICK_16</a></td></tr>
<tr class="separator:ga9ca13ebf24c331dd98df17d84a4b72c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae055a12fbf4b48b5954c8e1cd129b810" id="r_gae055a12fbf4b48b5954c8e1cd129b810"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gae055a12fbf4b48b5954c8e1cd129b810">GLFW_GAMEPAD_BUTTON_A</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:gae055a12fbf4b48b5954c8e1cd129b810"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2228a6512fd5950cdb51ba07846546fa" id="r_ga2228a6512fd5950cdb51ba07846546fa"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga2228a6512fd5950cdb51ba07846546fa">GLFW_GAMEPAD_BUTTON_B</a>&#160;&#160;&#160;1</td></tr>
<tr class="separator:ga2228a6512fd5950cdb51ba07846546fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga52cc94785cf3fe9a12e246539259887c" id="r_ga52cc94785cf3fe9a12e246539259887c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga52cc94785cf3fe9a12e246539259887c">GLFW_GAMEPAD_BUTTON_X</a>&#160;&#160;&#160;2</td></tr>
<tr class="separator:ga52cc94785cf3fe9a12e246539259887c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafc931248bda494b530cbe057f386a5ed" id="r_gafc931248bda494b530cbe057f386a5ed"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gafc931248bda494b530cbe057f386a5ed">GLFW_GAMEPAD_BUTTON_Y</a>&#160;&#160;&#160;3</td></tr>
<tr class="separator:gafc931248bda494b530cbe057f386a5ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga17d67b4f39a39d6b813bd1567a3507c3" id="r_ga17d67b4f39a39d6b813bd1567a3507c3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga17d67b4f39a39d6b813bd1567a3507c3">GLFW_GAMEPAD_BUTTON_LEFT_BUMPER</a>&#160;&#160;&#160;4</td></tr>
<tr class="separator:ga17d67b4f39a39d6b813bd1567a3507c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadfbc9ea9bf3aae896b79fa49fdc85c7f" id="r_gadfbc9ea9bf3aae896b79fa49fdc85c7f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gadfbc9ea9bf3aae896b79fa49fdc85c7f">GLFW_GAMEPAD_BUTTON_RIGHT_BUMPER</a>&#160;&#160;&#160;5</td></tr>
<tr class="separator:gadfbc9ea9bf3aae896b79fa49fdc85c7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabc7c0264ce778835b516a472b47f6caf" id="r_gabc7c0264ce778835b516a472b47f6caf"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gabc7c0264ce778835b516a472b47f6caf">GLFW_GAMEPAD_BUTTON_BACK</a>&#160;&#160;&#160;6</td></tr>
<tr class="separator:gabc7c0264ce778835b516a472b47f6caf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga04606949dd9139434b8a1bedf4ac1021" id="r_ga04606949dd9139434b8a1bedf4ac1021"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga04606949dd9139434b8a1bedf4ac1021">GLFW_GAMEPAD_BUTTON_START</a>&#160;&#160;&#160;7</td></tr>
<tr class="separator:ga04606949dd9139434b8a1bedf4ac1021"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7fa48c32e5b2f5db2f080aa0b8b573dc" id="r_ga7fa48c32e5b2f5db2f080aa0b8b573dc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga7fa48c32e5b2f5db2f080aa0b8b573dc">GLFW_GAMEPAD_BUTTON_GUIDE</a>&#160;&#160;&#160;8</td></tr>
<tr class="separator:ga7fa48c32e5b2f5db2f080aa0b8b573dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3e089787327454f7bfca7364d6ca206a" id="r_ga3e089787327454f7bfca7364d6ca206a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga3e089787327454f7bfca7364d6ca206a">GLFW_GAMEPAD_BUTTON_LEFT_THUMB</a>&#160;&#160;&#160;9</td></tr>
<tr class="separator:ga3e089787327454f7bfca7364d6ca206a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1c003f52b5aebb45272475b48953b21a" id="r_ga1c003f52b5aebb45272475b48953b21a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga1c003f52b5aebb45272475b48953b21a">GLFW_GAMEPAD_BUTTON_RIGHT_THUMB</a>&#160;&#160;&#160;10</td></tr>
<tr class="separator:ga1c003f52b5aebb45272475b48953b21a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4f1ed6f974a47bc8930d4874a283476a" id="r_ga4f1ed6f974a47bc8930d4874a283476a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga4f1ed6f974a47bc8930d4874a283476a">GLFW_GAMEPAD_BUTTON_DPAD_UP</a>&#160;&#160;&#160;11</td></tr>
<tr class="separator:ga4f1ed6f974a47bc8930d4874a283476a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae2a780d2a8c79e0b77c0b7b601ca57c6" id="r_gae2a780d2a8c79e0b77c0b7b601ca57c6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gae2a780d2a8c79e0b77c0b7b601ca57c6">GLFW_GAMEPAD_BUTTON_DPAD_RIGHT</a>&#160;&#160;&#160;12</td></tr>
<tr class="separator:gae2a780d2a8c79e0b77c0b7b601ca57c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8f2b731b97d80f90f11967a83207665c" id="r_ga8f2b731b97d80f90f11967a83207665c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga8f2b731b97d80f90f11967a83207665c">GLFW_GAMEPAD_BUTTON_DPAD_DOWN</a>&#160;&#160;&#160;13</td></tr>
<tr class="separator:ga8f2b731b97d80f90f11967a83207665c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf0697e0e8607b2ebe1c93b0c6befe301" id="r_gaf0697e0e8607b2ebe1c93b0c6befe301"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gaf0697e0e8607b2ebe1c93b0c6befe301">GLFW_GAMEPAD_BUTTON_DPAD_LEFT</a>&#160;&#160;&#160;14</td></tr>
<tr class="separator:gaf0697e0e8607b2ebe1c93b0c6befe301"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5cc98882f4f81dacf761639a567f61eb" id="r_ga5cc98882f4f81dacf761639a567f61eb"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga5cc98882f4f81dacf761639a567f61eb">GLFW_GAMEPAD_BUTTON_LAST</a>&#160;&#160;&#160;<a class="el" href="group__gamepad__buttons.html#gaf0697e0e8607b2ebe1c93b0c6befe301">GLFW_GAMEPAD_BUTTON_DPAD_LEFT</a></td></tr>
<tr class="separator:ga5cc98882f4f81dacf761639a567f61eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf08d0df26527c9305253422bd98ed63a" id="r_gaf08d0df26527c9305253422bd98ed63a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gaf08d0df26527c9305253422bd98ed63a">GLFW_GAMEPAD_BUTTON_CROSS</a>&#160;&#160;&#160;<a class="el" href="group__gamepad__buttons.html#gae055a12fbf4b48b5954c8e1cd129b810">GLFW_GAMEPAD_BUTTON_A</a></td></tr>
<tr class="separator:gaf08d0df26527c9305253422bd98ed63a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaef094b3dacbf15f272b274516839b82" id="r_gaaef094b3dacbf15f272b274516839b82"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gaaef094b3dacbf15f272b274516839b82">GLFW_GAMEPAD_BUTTON_CIRCLE</a>&#160;&#160;&#160;<a class="el" href="group__gamepad__buttons.html#ga2228a6512fd5950cdb51ba07846546fa">GLFW_GAMEPAD_BUTTON_B</a></td></tr>
<tr class="separator:gaaef094b3dacbf15f272b274516839b82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafc7821e87d77d41ed2cd3e1f726ec35f" id="r_gafc7821e87d77d41ed2cd3e1f726ec35f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#gafc7821e87d77d41ed2cd3e1f726ec35f">GLFW_GAMEPAD_BUTTON_SQUARE</a>&#160;&#160;&#160;<a class="el" href="group__gamepad__buttons.html#ga52cc94785cf3fe9a12e246539259887c">GLFW_GAMEPAD_BUTTON_X</a></td></tr>
<tr class="separator:gafc7821e87d77d41ed2cd3e1f726ec35f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3a7ef6bcb768a08cd3bf142f7f09f802" id="r_ga3a7ef6bcb768a08cd3bf142f7f09f802"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html#ga3a7ef6bcb768a08cd3bf142f7f09f802">GLFW_GAMEPAD_BUTTON_TRIANGLE</a>&#160;&#160;&#160;<a class="el" href="group__gamepad__buttons.html#gafc931248bda494b530cbe057f386a5ed">GLFW_GAMEPAD_BUTTON_Y</a></td></tr>
<tr class="separator:ga3a7ef6bcb768a08cd3bf142f7f09f802"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga544e396d092036a7d80c1e5f233f7a38" id="r_ga544e396d092036a7d80c1e5f233f7a38"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__axes.html#ga544e396d092036a7d80c1e5f233f7a38">GLFW_GAMEPAD_AXIS_LEFT_X</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:ga544e396d092036a7d80c1e5f233f7a38"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga64dcf2c6e9be50b7c556ff7671996dd5" id="r_ga64dcf2c6e9be50b7c556ff7671996dd5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__axes.html#ga64dcf2c6e9be50b7c556ff7671996dd5">GLFW_GAMEPAD_AXIS_LEFT_Y</a>&#160;&#160;&#160;1</td></tr>
<tr class="separator:ga64dcf2c6e9be50b7c556ff7671996dd5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabd6785106cd3c5a044a6e49a395ee2fc" id="r_gabd6785106cd3c5a044a6e49a395ee2fc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__axes.html#gabd6785106cd3c5a044a6e49a395ee2fc">GLFW_GAMEPAD_AXIS_RIGHT_X</a>&#160;&#160;&#160;2</td></tr>
<tr class="separator:gabd6785106cd3c5a044a6e49a395ee2fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1cc20566d44d521b7183681a8e88e2e4" id="r_ga1cc20566d44d521b7183681a8e88e2e4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__axes.html#ga1cc20566d44d521b7183681a8e88e2e4">GLFW_GAMEPAD_AXIS_RIGHT_Y</a>&#160;&#160;&#160;3</td></tr>
<tr class="separator:ga1cc20566d44d521b7183681a8e88e2e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6d79561dd8907c37354426242901b86e" id="r_ga6d79561dd8907c37354426242901b86e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__axes.html#ga6d79561dd8907c37354426242901b86e">GLFW_GAMEPAD_AXIS_LEFT_TRIGGER</a>&#160;&#160;&#160;4</td></tr>
<tr class="separator:ga6d79561dd8907c37354426242901b86e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga121a7d5d20589a423cd1634dd6ee6eab" id="r_ga121a7d5d20589a423cd1634dd6ee6eab"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__axes.html#ga121a7d5d20589a423cd1634dd6ee6eab">GLFW_GAMEPAD_AXIS_RIGHT_TRIGGER</a>&#160;&#160;&#160;5</td></tr>
<tr class="separator:ga121a7d5d20589a423cd1634dd6ee6eab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0818fd9433e1359692b7443293e5ac86" id="r_ga0818fd9433e1359692b7443293e5ac86"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__axes.html#ga0818fd9433e1359692b7443293e5ac86">GLFW_GAMEPAD_AXIS_LAST</a>&#160;&#160;&#160;<a class="el" href="group__gamepad__axes.html#ga121a7d5d20589a423cd1634dd6ee6eab">GLFW_GAMEPAD_AXIS_RIGHT_TRIGGER</a></td></tr>
<tr class="separator:ga0818fd9433e1359692b7443293e5ac86"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafa30deee5db4d69c4c93d116ed87dbf4" id="r_gafa30deee5db4d69c4c93d116ed87dbf4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#gafa30deee5db4d69c4c93d116ed87dbf4">GLFW_NO_ERROR</a>&#160;&#160;&#160;0</td></tr>
<tr class="memdesc:gafa30deee5db4d69c4c93d116ed87dbf4"><td class="mdescLeft">&#160;</td><td class="mdescRight">No error has occurred.  <br /></td></tr>
<tr class="separator:gafa30deee5db4d69c4c93d116ed87dbf4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2374ee02c177f12e1fa76ff3ed15e14a" id="r_ga2374ee02c177f12e1fa76ff3ed15e14a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>&#160;&#160;&#160;0x00010001</td></tr>
<tr class="memdesc:ga2374ee02c177f12e1fa76ff3ed15e14a"><td class="mdescLeft">&#160;</td><td class="mdescRight">GLFW has not been initialized.  <br /></td></tr>
<tr class="separator:ga2374ee02c177f12e1fa76ff3ed15e14a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa8290386e9528ccb9e42a3a4e16fc0d0" id="r_gaa8290386e9528ccb9e42a3a4e16fc0d0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#gaa8290386e9528ccb9e42a3a4e16fc0d0">GLFW_NO_CURRENT_CONTEXT</a>&#160;&#160;&#160;0x00010002</td></tr>
<tr class="memdesc:gaa8290386e9528ccb9e42a3a4e16fc0d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">No context is current for this thread.  <br /></td></tr>
<tr class="separator:gaa8290386e9528ccb9e42a3a4e16fc0d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga76f6bb9c4eea73db675f096b404593ce" id="r_ga76f6bb9c4eea73db675f096b404593ce"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a>&#160;&#160;&#160;0x00010003</td></tr>
<tr class="memdesc:ga76f6bb9c4eea73db675f096b404593ce"><td class="mdescLeft">&#160;</td><td class="mdescRight">One of the arguments to the function was an invalid enum value.  <br /></td></tr>
<tr class="separator:ga76f6bb9c4eea73db675f096b404593ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaf2ef9aa8202c2b82ac2d921e554c687" id="r_gaaf2ef9aa8202c2b82ac2d921e554c687"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a>&#160;&#160;&#160;0x00010004</td></tr>
<tr class="memdesc:gaaf2ef9aa8202c2b82ac2d921e554c687"><td class="mdescLeft">&#160;</td><td class="mdescRight">One of the arguments to the function was an invalid value.  <br /></td></tr>
<tr class="separator:gaaf2ef9aa8202c2b82ac2d921e554c687"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9023953a2bcb98c2906afd071d21ee7f" id="r_ga9023953a2bcb98c2906afd071d21ee7f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga9023953a2bcb98c2906afd071d21ee7f">GLFW_OUT_OF_MEMORY</a>&#160;&#160;&#160;0x00010005</td></tr>
<tr class="memdesc:ga9023953a2bcb98c2906afd071d21ee7f"><td class="mdescLeft">&#160;</td><td class="mdescRight">A memory allocation failed.  <br /></td></tr>
<tr class="separator:ga9023953a2bcb98c2906afd071d21ee7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga56882b290db23261cc6c053c40c2d08e" id="r_ga56882b290db23261cc6c053c40c2d08e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga56882b290db23261cc6c053c40c2d08e">GLFW_API_UNAVAILABLE</a>&#160;&#160;&#160;0x00010006</td></tr>
<tr class="memdesc:ga56882b290db23261cc6c053c40c2d08e"><td class="mdescLeft">&#160;</td><td class="mdescRight">GLFW could not find support for the requested API on the system.  <br /></td></tr>
<tr class="separator:ga56882b290db23261cc6c053c40c2d08e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad16c5565b4a69f9c2a9ac2c0dbc89462" id="r_gad16c5565b4a69f9c2a9ac2c0dbc89462"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#gad16c5565b4a69f9c2a9ac2c0dbc89462">GLFW_VERSION_UNAVAILABLE</a>&#160;&#160;&#160;0x00010007</td></tr>
<tr class="memdesc:gad16c5565b4a69f9c2a9ac2c0dbc89462"><td class="mdescLeft">&#160;</td><td class="mdescRight">The requested OpenGL or OpenGL ES version is not available.  <br /></td></tr>
<tr class="separator:gad16c5565b4a69f9c2a9ac2c0dbc89462"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad44162d78100ea5e87cdd38426b8c7a1" id="r_gad44162d78100ea5e87cdd38426b8c7a1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>&#160;&#160;&#160;0x00010008</td></tr>
<tr class="memdesc:gad44162d78100ea5e87cdd38426b8c7a1"><td class="mdescLeft">&#160;</td><td class="mdescRight">A platform-specific error occurred that does not match any of the more specific categories.  <br /></td></tr>
<tr class="separator:gad44162d78100ea5e87cdd38426b8c7a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga196e125ef261d94184e2b55c05762f14" id="r_ga196e125ef261d94184e2b55c05762f14"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga196e125ef261d94184e2b55c05762f14">GLFW_FORMAT_UNAVAILABLE</a>&#160;&#160;&#160;0x00010009</td></tr>
<tr class="memdesc:ga196e125ef261d94184e2b55c05762f14"><td class="mdescLeft">&#160;</td><td class="mdescRight">The requested format is not supported or available.  <br /></td></tr>
<tr class="separator:ga196e125ef261d94184e2b55c05762f14"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacff24d2757da752ae4c80bf452356487" id="r_gacff24d2757da752ae4c80bf452356487"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a>&#160;&#160;&#160;0x0001000A</td></tr>
<tr class="memdesc:gacff24d2757da752ae4c80bf452356487"><td class="mdescLeft">&#160;</td><td class="mdescRight">The specified window does not have an OpenGL or OpenGL ES context.  <br /></td></tr>
<tr class="separator:gacff24d2757da752ae4c80bf452356487"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga09d6943923a70ddef3a085f5baee786c" id="r_ga09d6943923a70ddef3a085f5baee786c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga09d6943923a70ddef3a085f5baee786c">GLFW_CURSOR_UNAVAILABLE</a>&#160;&#160;&#160;0x0001000B</td></tr>
<tr class="memdesc:ga09d6943923a70ddef3a085f5baee786c"><td class="mdescLeft">&#160;</td><td class="mdescRight">The specified cursor shape is not available.  <br /></td></tr>
<tr class="separator:ga09d6943923a70ddef3a085f5baee786c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga526fba20a01504a8086c763b6ca53ce5" id="r_ga526fba20a01504a8086c763b6ca53ce5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a>&#160;&#160;&#160;0x0001000C</td></tr>
<tr class="memdesc:ga526fba20a01504a8086c763b6ca53ce5"><td class="mdescLeft">&#160;</td><td class="mdescRight">The requested feature is not provided by the platform.  <br /></td></tr>
<tr class="separator:ga526fba20a01504a8086c763b6ca53ce5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5dda77e023e83151e8bd55a6758f946a" id="r_ga5dda77e023e83151e8bd55a6758f946a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga5dda77e023e83151e8bd55a6758f946a">GLFW_FEATURE_UNIMPLEMENTED</a>&#160;&#160;&#160;0x0001000D</td></tr>
<tr class="memdesc:ga5dda77e023e83151e8bd55a6758f946a"><td class="mdescLeft">&#160;</td><td class="mdescRight">The requested feature is not implemented for the platform.  <br /></td></tr>
<tr class="separator:ga5dda77e023e83151e8bd55a6758f946a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3608c6c29ab7a72f3bf019f4c3a2563d" id="r_ga3608c6c29ab7a72f3bf019f4c3a2563d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>&#160;&#160;&#160;0x0001000E</td></tr>
<tr class="memdesc:ga3608c6c29ab7a72f3bf019f4c3a2563d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Platform unavailable or no matching platform was found.  <br /></td></tr>
<tr class="separator:ga3608c6c29ab7a72f3bf019f4c3a2563d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga54ddb14825a1541a56e22afb5f832a9e" id="r_ga54ddb14825a1541a56e22afb5f832a9e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga54ddb14825a1541a56e22afb5f832a9e">GLFW_FOCUSED</a>&#160;&#160;&#160;0x00020001</td></tr>
<tr class="memdesc:ga54ddb14825a1541a56e22afb5f832a9e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Input focus window hint and attribute.  <br /></td></tr>
<tr class="separator:ga54ddb14825a1541a56e22afb5f832a9e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga39d44b7c056e55e581355a92d240b58a" id="r_ga39d44b7c056e55e581355a92d240b58a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga39d44b7c056e55e581355a92d240b58a">GLFW_ICONIFIED</a>&#160;&#160;&#160;0x00020002</td></tr>
<tr class="memdesc:ga39d44b7c056e55e581355a92d240b58a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window iconification window attribute.  <br /></td></tr>
<tr class="separator:ga39d44b7c056e55e581355a92d240b58a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadba13c7a1b3aa40831eb2beedbd5bd1d" id="r_gadba13c7a1b3aa40831eb2beedbd5bd1d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gadba13c7a1b3aa40831eb2beedbd5bd1d">GLFW_RESIZABLE</a>&#160;&#160;&#160;0x00020003</td></tr>
<tr class="memdesc:gadba13c7a1b3aa40831eb2beedbd5bd1d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window resize-ability window hint and attribute.  <br /></td></tr>
<tr class="separator:gadba13c7a1b3aa40831eb2beedbd5bd1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafb3cdc45297e06d8f1eb13adc69ca6c4" id="r_gafb3cdc45297e06d8f1eb13adc69ca6c4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gafb3cdc45297e06d8f1eb13adc69ca6c4">GLFW_VISIBLE</a>&#160;&#160;&#160;0x00020004</td></tr>
<tr class="memdesc:gafb3cdc45297e06d8f1eb13adc69ca6c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window visibility window hint and attribute.  <br /></td></tr>
<tr class="separator:gafb3cdc45297e06d8f1eb13adc69ca6c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga21b854d36314c94d65aed84405b2f25e" id="r_ga21b854d36314c94d65aed84405b2f25e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga21b854d36314c94d65aed84405b2f25e">GLFW_DECORATED</a>&#160;&#160;&#160;0x00020005</td></tr>
<tr class="memdesc:ga21b854d36314c94d65aed84405b2f25e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window decoration window hint and attribute.  <br /></td></tr>
<tr class="separator:ga21b854d36314c94d65aed84405b2f25e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9d9874fc928200136a6dcdad726aa252" id="r_ga9d9874fc928200136a6dcdad726aa252"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga9d9874fc928200136a6dcdad726aa252">GLFW_AUTO_ICONIFY</a>&#160;&#160;&#160;0x00020006</td></tr>
<tr class="memdesc:ga9d9874fc928200136a6dcdad726aa252"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window auto-iconification window hint and attribute.  <br /></td></tr>
<tr class="separator:ga9d9874fc928200136a6dcdad726aa252"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7fb0be51407783b41adbf5bec0b09d80" id="r_ga7fb0be51407783b41adbf5bec0b09d80"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga7fb0be51407783b41adbf5bec0b09d80">GLFW_FLOATING</a>&#160;&#160;&#160;0x00020007</td></tr>
<tr class="memdesc:ga7fb0be51407783b41adbf5bec0b09d80"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window decoration window hint and attribute.  <br /></td></tr>
<tr class="separator:ga7fb0be51407783b41adbf5bec0b09d80"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad8ccb396253ad0b72c6d4c917eb38a03" id="r_gad8ccb396253ad0b72c6d4c917eb38a03"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gad8ccb396253ad0b72c6d4c917eb38a03">GLFW_MAXIMIZED</a>&#160;&#160;&#160;0x00020008</td></tr>
<tr class="memdesc:gad8ccb396253ad0b72c6d4c917eb38a03"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window maximization window hint and attribute.  <br /></td></tr>
<tr class="separator:gad8ccb396253ad0b72c6d4c917eb38a03"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5ac0847c0aa0b3619f2855707b8a7a77" id="r_ga5ac0847c0aa0b3619f2855707b8a7a77"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga5ac0847c0aa0b3619f2855707b8a7a77">GLFW_CENTER_CURSOR</a>&#160;&#160;&#160;0x00020009</td></tr>
<tr class="memdesc:ga5ac0847c0aa0b3619f2855707b8a7a77"><td class="mdescLeft">&#160;</td><td class="mdescRight">Cursor centering window hint.  <br /></td></tr>
<tr class="separator:ga5ac0847c0aa0b3619f2855707b8a7a77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga60a0578c3b9449027d683a9c6abb9f14" id="r_ga60a0578c3b9449027d683a9c6abb9f14"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga60a0578c3b9449027d683a9c6abb9f14">GLFW_TRANSPARENT_FRAMEBUFFER</a>&#160;&#160;&#160;0x0002000A</td></tr>
<tr class="memdesc:ga60a0578c3b9449027d683a9c6abb9f14"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window framebuffer transparency hint and attribute.  <br /></td></tr>
<tr class="separator:ga60a0578c3b9449027d683a9c6abb9f14"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8665c71c6fa3d22425c6a0e8a3f89d8a" id="r_ga8665c71c6fa3d22425c6a0e8a3f89d8a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga8665c71c6fa3d22425c6a0e8a3f89d8a">GLFW_HOVERED</a>&#160;&#160;&#160;0x0002000B</td></tr>
<tr class="memdesc:ga8665c71c6fa3d22425c6a0e8a3f89d8a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Mouse cursor hover window attribute.  <br /></td></tr>
<tr class="separator:ga8665c71c6fa3d22425c6a0e8a3f89d8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafa94b1da34bfd6488c0d709761504dfc" id="r_gafa94b1da34bfd6488c0d709761504dfc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gafa94b1da34bfd6488c0d709761504dfc">GLFW_FOCUS_ON_SHOW</a>&#160;&#160;&#160;0x0002000C</td></tr>
<tr class="memdesc:gafa94b1da34bfd6488c0d709761504dfc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Input focus on calling show window hint and attribute.  <br /></td></tr>
<tr class="separator:gafa94b1da34bfd6488c0d709761504dfc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga88981797d29800808ec242274ab5c03a" id="r_ga88981797d29800808ec242274ab5c03a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga88981797d29800808ec242274ab5c03a">GLFW_MOUSE_PASSTHROUGH</a>&#160;&#160;&#160;0x0002000D</td></tr>
<tr class="memdesc:ga88981797d29800808ec242274ab5c03a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Mouse input transparency window hint and attribute.  <br /></td></tr>
<tr class="separator:ga88981797d29800808ec242274ab5c03a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaededa6b208b8e31343da56bb349c6fb2" id="r_gaededa6b208b8e31343da56bb349c6fb2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaededa6b208b8e31343da56bb349c6fb2">GLFW_POSITION_X</a>&#160;&#160;&#160;0x0002000E</td></tr>
<tr class="memdesc:gaededa6b208b8e31343da56bb349c6fb2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initial position x-coordinate window hint.  <br /></td></tr>
<tr class="separator:gaededa6b208b8e31343da56bb349c6fb2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6b3ccf63683c81f479e2a98f5027200e" id="r_ga6b3ccf63683c81f479e2a98f5027200e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga6b3ccf63683c81f479e2a98f5027200e">GLFW_POSITION_Y</a>&#160;&#160;&#160;0x0002000F</td></tr>
<tr class="memdesc:ga6b3ccf63683c81f479e2a98f5027200e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initial position y-coordinate window hint.  <br /></td></tr>
<tr class="separator:ga6b3ccf63683c81f479e2a98f5027200e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf78ed8e417dbcc1e354906cc2708c982" id="r_gaf78ed8e417dbcc1e354906cc2708c982"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaf78ed8e417dbcc1e354906cc2708c982">GLFW_RED_BITS</a>&#160;&#160;&#160;0x00021001</td></tr>
<tr class="memdesc:gaf78ed8e417dbcc1e354906cc2708c982"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:gaf78ed8e417dbcc1e354906cc2708c982"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafba3b72638c914e5fb8a237dd4c50d4d" id="r_gafba3b72638c914e5fb8a237dd4c50d4d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gafba3b72638c914e5fb8a237dd4c50d4d">GLFW_GREEN_BITS</a>&#160;&#160;&#160;0x00021002</td></tr>
<tr class="memdesc:gafba3b72638c914e5fb8a237dd4c50d4d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:gafba3b72638c914e5fb8a237dd4c50d4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab292ea403db6d514537b515311bf9ae3" id="r_gab292ea403db6d514537b515311bf9ae3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gab292ea403db6d514537b515311bf9ae3">GLFW_BLUE_BITS</a>&#160;&#160;&#160;0x00021003</td></tr>
<tr class="memdesc:gab292ea403db6d514537b515311bf9ae3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:gab292ea403db6d514537b515311bf9ae3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafed79a3f468997877da86c449bd43e8c" id="r_gafed79a3f468997877da86c449bd43e8c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gafed79a3f468997877da86c449bd43e8c">GLFW_ALPHA_BITS</a>&#160;&#160;&#160;0x00021004</td></tr>
<tr class="memdesc:gafed79a3f468997877da86c449bd43e8c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:gafed79a3f468997877da86c449bd43e8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga318a55eac1fee57dfe593b6d38149d07" id="r_ga318a55eac1fee57dfe593b6d38149d07"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga318a55eac1fee57dfe593b6d38149d07">GLFW_DEPTH_BITS</a>&#160;&#160;&#160;0x00021005</td></tr>
<tr class="memdesc:ga318a55eac1fee57dfe593b6d38149d07"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:ga318a55eac1fee57dfe593b6d38149d07"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5339890a45a1fb38e93cb9fcc5fd069d" id="r_ga5339890a45a1fb38e93cb9fcc5fd069d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga5339890a45a1fb38e93cb9fcc5fd069d">GLFW_STENCIL_BITS</a>&#160;&#160;&#160;0x00021006</td></tr>
<tr class="memdesc:ga5339890a45a1fb38e93cb9fcc5fd069d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:ga5339890a45a1fb38e93cb9fcc5fd069d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaead34a9a683b2bc20eecf30ba738bfc6" id="r_gaead34a9a683b2bc20eecf30ba738bfc6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaead34a9a683b2bc20eecf30ba738bfc6">GLFW_ACCUM_RED_BITS</a>&#160;&#160;&#160;0x00021007</td></tr>
<tr class="memdesc:gaead34a9a683b2bc20eecf30ba738bfc6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:gaead34a9a683b2bc20eecf30ba738bfc6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga65713cee1326f8e9d806fdf93187b471" id="r_ga65713cee1326f8e9d806fdf93187b471"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga65713cee1326f8e9d806fdf93187b471">GLFW_ACCUM_GREEN_BITS</a>&#160;&#160;&#160;0x00021008</td></tr>
<tr class="memdesc:ga65713cee1326f8e9d806fdf93187b471"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:ga65713cee1326f8e9d806fdf93187b471"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga22bbe9104a8ce1f8b88fb4f186aa36ce" id="r_ga22bbe9104a8ce1f8b88fb4f186aa36ce"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga22bbe9104a8ce1f8b88fb4f186aa36ce">GLFW_ACCUM_BLUE_BITS</a>&#160;&#160;&#160;0x00021009</td></tr>
<tr class="memdesc:ga22bbe9104a8ce1f8b88fb4f186aa36ce"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:ga22bbe9104a8ce1f8b88fb4f186aa36ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae829b55591c18169a40ab4067a041b1f" id="r_gae829b55591c18169a40ab4067a041b1f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gae829b55591c18169a40ab4067a041b1f">GLFW_ACCUM_ALPHA_BITS</a>&#160;&#160;&#160;0x0002100A</td></tr>
<tr class="memdesc:gae829b55591c18169a40ab4067a041b1f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:gae829b55591c18169a40ab4067a041b1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab05108c5029443b371112b031d1fa174" id="r_gab05108c5029443b371112b031d1fa174"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gab05108c5029443b371112b031d1fa174">GLFW_AUX_BUFFERS</a>&#160;&#160;&#160;0x0002100B</td></tr>
<tr class="memdesc:gab05108c5029443b371112b031d1fa174"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer auxiliary buffer hint.  <br /></td></tr>
<tr class="separator:gab05108c5029443b371112b031d1fa174"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga83d991efca02537e2d69969135b77b03" id="r_ga83d991efca02537e2d69969135b77b03"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga83d991efca02537e2d69969135b77b03">GLFW_STEREO</a>&#160;&#160;&#160;0x0002100C</td></tr>
<tr class="memdesc:ga83d991efca02537e2d69969135b77b03"><td class="mdescLeft">&#160;</td><td class="mdescRight">OpenGL stereoscopic rendering hint.  <br /></td></tr>
<tr class="separator:ga83d991efca02537e2d69969135b77b03"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2cdf86fdcb7722fb8829c4e201607535" id="r_ga2cdf86fdcb7722fb8829c4e201607535"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga2cdf86fdcb7722fb8829c4e201607535">GLFW_SAMPLES</a>&#160;&#160;&#160;0x0002100D</td></tr>
<tr class="memdesc:ga2cdf86fdcb7722fb8829c4e201607535"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer MSAA samples hint.  <br /></td></tr>
<tr class="separator:ga2cdf86fdcb7722fb8829c4e201607535"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga444a8f00414a63220591f9fdb7b5642b" id="r_ga444a8f00414a63220591f9fdb7b5642b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga444a8f00414a63220591f9fdb7b5642b">GLFW_SRGB_CAPABLE</a>&#160;&#160;&#160;0x0002100E</td></tr>
<tr class="memdesc:ga444a8f00414a63220591f9fdb7b5642b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer sRGB hint.  <br /></td></tr>
<tr class="separator:ga444a8f00414a63220591f9fdb7b5642b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0f20825e6e47ee8ba389024519682212" id="r_ga0f20825e6e47ee8ba389024519682212"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga0f20825e6e47ee8ba389024519682212">GLFW_REFRESH_RATE</a>&#160;&#160;&#160;0x0002100F</td></tr>
<tr class="memdesc:ga0f20825e6e47ee8ba389024519682212"><td class="mdescLeft">&#160;</td><td class="mdescRight">Monitor refresh rate hint.  <br /></td></tr>
<tr class="separator:ga0f20825e6e47ee8ba389024519682212"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga714a5d569e8a274ea58fdfa020955339" id="r_ga714a5d569e8a274ea58fdfa020955339"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga714a5d569e8a274ea58fdfa020955339">GLFW_DOUBLEBUFFER</a>&#160;&#160;&#160;0x00021010</td></tr>
<tr class="memdesc:ga714a5d569e8a274ea58fdfa020955339"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer double buffering hint and attribute.  <br /></td></tr>
<tr class="separator:ga714a5d569e8a274ea58fdfa020955339"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga649309cf72a3d3de5b1348ca7936c95b" id="r_ga649309cf72a3d3de5b1348ca7936c95b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga649309cf72a3d3de5b1348ca7936c95b">GLFW_CLIENT_API</a>&#160;&#160;&#160;0x00022001</td></tr>
<tr class="memdesc:ga649309cf72a3d3de5b1348ca7936c95b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Context client API hint and attribute.  <br /></td></tr>
<tr class="separator:ga649309cf72a3d3de5b1348ca7936c95b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafe5e4922de1f9932d7e9849bb053b0c0" id="r_gafe5e4922de1f9932d7e9849bb053b0c0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gafe5e4922de1f9932d7e9849bb053b0c0">GLFW_CONTEXT_VERSION_MAJOR</a>&#160;&#160;&#160;0x00022002</td></tr>
<tr class="memdesc:gafe5e4922de1f9932d7e9849bb053b0c0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Context client API major version hint and attribute.  <br /></td></tr>
<tr class="separator:gafe5e4922de1f9932d7e9849bb053b0c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga31aca791e4b538c4e4a771eb95cc2d07" id="r_ga31aca791e4b538c4e4a771eb95cc2d07"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga31aca791e4b538c4e4a771eb95cc2d07">GLFW_CONTEXT_VERSION_MINOR</a>&#160;&#160;&#160;0x00022003</td></tr>
<tr class="memdesc:ga31aca791e4b538c4e4a771eb95cc2d07"><td class="mdescLeft">&#160;</td><td class="mdescRight">Context client API minor version hint and attribute.  <br /></td></tr>
<tr class="separator:ga31aca791e4b538c4e4a771eb95cc2d07"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafb9475071aa77c6fb05ca5a5c8678a08" id="r_gafb9475071aa77c6fb05ca5a5c8678a08"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gafb9475071aa77c6fb05ca5a5c8678a08">GLFW_CONTEXT_REVISION</a>&#160;&#160;&#160;0x00022004</td></tr>
<tr class="memdesc:gafb9475071aa77c6fb05ca5a5c8678a08"><td class="mdescLeft">&#160;</td><td class="mdescRight">Context client API revision number attribute.  <br /></td></tr>
<tr class="separator:gafb9475071aa77c6fb05ca5a5c8678a08"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gade3593916b4c507900aa2d6844810e00" id="r_gade3593916b4c507900aa2d6844810e00"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gade3593916b4c507900aa2d6844810e00">GLFW_CONTEXT_ROBUSTNESS</a>&#160;&#160;&#160;0x00022005</td></tr>
<tr class="memdesc:gade3593916b4c507900aa2d6844810e00"><td class="mdescLeft">&#160;</td><td class="mdescRight">Context robustness hint and attribute.  <br /></td></tr>
<tr class="separator:gade3593916b4c507900aa2d6844810e00"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga13d24b12465da8b28985f46c8557925b" id="r_ga13d24b12465da8b28985f46c8557925b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga13d24b12465da8b28985f46c8557925b">GLFW_OPENGL_FORWARD_COMPAT</a>&#160;&#160;&#160;0x00022006</td></tr>
<tr class="memdesc:ga13d24b12465da8b28985f46c8557925b"><td class="mdescLeft">&#160;</td><td class="mdescRight">OpenGL forward-compatibility hint and attribute.  <br /></td></tr>
<tr class="separator:ga13d24b12465da8b28985f46c8557925b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8d55e3afec73c7de0509c3b7ad1d9e3f" id="r_ga8d55e3afec73c7de0509c3b7ad1d9e3f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga8d55e3afec73c7de0509c3b7ad1d9e3f">GLFW_CONTEXT_DEBUG</a>&#160;&#160;&#160;0x00022007</td></tr>
<tr class="memdesc:ga8d55e3afec73c7de0509c3b7ad1d9e3f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Debug mode context hint and attribute.  <br /></td></tr>
<tr class="separator:ga8d55e3afec73c7de0509c3b7ad1d9e3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga87ec2df0b915201e950ca42d5d0831e1" id="r_ga87ec2df0b915201e950ca42d5d0831e1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga87ec2df0b915201e950ca42d5d0831e1">GLFW_OPENGL_DEBUG_CONTEXT</a>&#160;&#160;&#160;<a class="el" href="group__window.html#ga8d55e3afec73c7de0509c3b7ad1d9e3f">GLFW_CONTEXT_DEBUG</a></td></tr>
<tr class="memdesc:ga87ec2df0b915201e950ca42d5d0831e1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Legacy name for compatibility.  <br /></td></tr>
<tr class="separator:ga87ec2df0b915201e950ca42d5d0831e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga44f3a6b4261fbe351e0b950b0f372e12" id="r_ga44f3a6b4261fbe351e0b950b0f372e12"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga44f3a6b4261fbe351e0b950b0f372e12">GLFW_OPENGL_PROFILE</a>&#160;&#160;&#160;0x00022008</td></tr>
<tr class="memdesc:ga44f3a6b4261fbe351e0b950b0f372e12"><td class="mdescLeft">&#160;</td><td class="mdescRight">OpenGL profile hint and attribute.  <br /></td></tr>
<tr class="separator:ga44f3a6b4261fbe351e0b950b0f372e12"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga72b648a8378fe3310c7c7bbecc0f7be6" id="r_ga72b648a8378fe3310c7c7bbecc0f7be6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga72b648a8378fe3310c7c7bbecc0f7be6">GLFW_CONTEXT_RELEASE_BEHAVIOR</a>&#160;&#160;&#160;0x00022009</td></tr>
<tr class="memdesc:ga72b648a8378fe3310c7c7bbecc0f7be6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Context flush-on-release hint and attribute.  <br /></td></tr>
<tr class="separator:ga72b648a8378fe3310c7c7bbecc0f7be6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5a52fdfd46d8249c211f923675728082" id="r_ga5a52fdfd46d8249c211f923675728082"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga5a52fdfd46d8249c211f923675728082">GLFW_CONTEXT_NO_ERROR</a>&#160;&#160;&#160;0x0002200A</td></tr>
<tr class="memdesc:ga5a52fdfd46d8249c211f923675728082"><td class="mdescLeft">&#160;</td><td class="mdescRight">Context error suppression hint and attribute.  <br /></td></tr>
<tr class="separator:ga5a52fdfd46d8249c211f923675728082"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5154cebfcd831c1cc63a4d5ac9bb4486" id="r_ga5154cebfcd831c1cc63a4d5ac9bb4486"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga5154cebfcd831c1cc63a4d5ac9bb4486">GLFW_CONTEXT_CREATION_API</a>&#160;&#160;&#160;0x0002200B</td></tr>
<tr class="memdesc:ga5154cebfcd831c1cc63a4d5ac9bb4486"><td class="mdescLeft">&#160;</td><td class="mdescRight">Context creation API hint and attribute.  <br /></td></tr>
<tr class="separator:ga5154cebfcd831c1cc63a4d5ac9bb4486"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga620bc4280c7eab81ac9f02204500ed47" id="r_ga620bc4280c7eab81ac9f02204500ed47"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga620bc4280c7eab81ac9f02204500ed47">GLFW_SCALE_TO_MONITOR</a>&#160;&#160;&#160;0x0002200C</td></tr>
<tr class="memdesc:ga620bc4280c7eab81ac9f02204500ed47"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window content area scaling window <a class="el" href="window_guide.html#GLFW_SCALE_TO_MONITOR">window hint</a>.  <br /></td></tr>
<tr class="separator:ga620bc4280c7eab81ac9f02204500ed47"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa5a9c6b4722670fd33d6e8a88f2e21bc" id="r_gaa5a9c6b4722670fd33d6e8a88f2e21bc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaa5a9c6b4722670fd33d6e8a88f2e21bc">GLFW_SCALE_FRAMEBUFFER</a>&#160;&#160;&#160;0x0002200D</td></tr>
<tr class="memdesc:gaa5a9c6b4722670fd33d6e8a88f2e21bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window framebuffer scaling <a class="el" href="window_guide.html#GLFW_SCALE_FRAMEBUFFER_hint">window hint</a>.  <br /></td></tr>
<tr class="separator:gaa5a9c6b4722670fd33d6e8a88f2e21bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab6ef2d02eb55800d249ccf1af253c35e" id="r_gab6ef2d02eb55800d249ccf1af253c35e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gab6ef2d02eb55800d249ccf1af253c35e">GLFW_COCOA_RETINA_FRAMEBUFFER</a>&#160;&#160;&#160;0x00023001</td></tr>
<tr class="memdesc:gab6ef2d02eb55800d249ccf1af253c35e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Legacy name for compatibility.  <br /></td></tr>
<tr class="separator:gab6ef2d02eb55800d249ccf1af253c35e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga70fa0fbc745de6aa824df79a580e84b5" id="r_ga70fa0fbc745de6aa824df79a580e84b5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga70fa0fbc745de6aa824df79a580e84b5">GLFW_COCOA_FRAME_NAME</a>&#160;&#160;&#160;0x00023002</td></tr>
<tr class="memdesc:ga70fa0fbc745de6aa824df79a580e84b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">macOS specific <a class="el" href="window_guide.html#GLFW_COCOA_FRAME_NAME_hint">window hint</a>.  <br /></td></tr>
<tr class="separator:ga70fa0fbc745de6aa824df79a580e84b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga53c84ed2ddd94e15bbd44b1f6f7feafc" id="r_ga53c84ed2ddd94e15bbd44b1f6f7feafc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga53c84ed2ddd94e15bbd44b1f6f7feafc">GLFW_COCOA_GRAPHICS_SWITCHING</a>&#160;&#160;&#160;0x00023003</td></tr>
<tr class="memdesc:ga53c84ed2ddd94e15bbd44b1f6f7feafc"><td class="mdescLeft">&#160;</td><td class="mdescRight">macOS specific <a class="el" href="window_guide.html#GLFW_COCOA_GRAPHICS_SWITCHING_hint">window hint</a>.  <br /></td></tr>
<tr class="separator:ga53c84ed2ddd94e15bbd44b1f6f7feafc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae5a9ea2fccccd92edbd343fc56461114" id="r_gae5a9ea2fccccd92edbd343fc56461114"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gae5a9ea2fccccd92edbd343fc56461114">GLFW_X11_CLASS_NAME</a>&#160;&#160;&#160;0x00024001</td></tr>
<tr class="memdesc:gae5a9ea2fccccd92edbd343fc56461114"><td class="mdescLeft">&#160;</td><td class="mdescRight">X11 specific <a class="el" href="window_guide.html#GLFW_X11_CLASS_NAME_hint">window hint</a>.  <br /></td></tr>
<tr class="separator:gae5a9ea2fccccd92edbd343fc56461114"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga494c3c0d911e4b860b946530a3e389e8" id="r_ga494c3c0d911e4b860b946530a3e389e8"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga494c3c0d911e4b860b946530a3e389e8">GLFW_X11_INSTANCE_NAME</a>&#160;&#160;&#160;0x00024002</td></tr>
<tr class="memdesc:ga494c3c0d911e4b860b946530a3e389e8"><td class="mdescLeft">&#160;</td><td class="mdescRight">X11 specific <a class="el" href="window_guide.html#GLFW_X11_CLASS_NAME_hint">window hint</a>.  <br /></td></tr>
<tr class="separator:ga494c3c0d911e4b860b946530a3e389e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf65ea8dafdc0edb07b821b9a336d5043" id="r_gaf65ea8dafdc0edb07b821b9a336d5043"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaf65ea8dafdc0edb07b821b9a336d5043">GLFW_WIN32_KEYBOARD_MENU</a>&#160;&#160;&#160;0x00025001</td></tr>
<tr class="separator:gaf65ea8dafdc0edb07b821b9a336d5043"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gace10f3846571de62243b46f75d978487" id="r_gace10f3846571de62243b46f75d978487"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gace10f3846571de62243b46f75d978487">GLFW_WIN32_SHOWDEFAULT</a>&#160;&#160;&#160;0x00025002</td></tr>
<tr class="memdesc:gace10f3846571de62243b46f75d978487"><td class="mdescLeft">&#160;</td><td class="mdescRight">Win32 specific <a class="el" href="window_guide.html#GLFW_WIN32_SHOWDEFAULT_hint">window hint</a>.  <br /></td></tr>
<tr class="separator:gace10f3846571de62243b46f75d978487"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafbf1ce7a4362c75e602a4df9e1bdecd3" id="r_gafbf1ce7a4362c75e602a4df9e1bdecd3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gafbf1ce7a4362c75e602a4df9e1bdecd3">GLFW_WAYLAND_APP_ID</a>&#160;&#160;&#160;0x00026001</td></tr>
<tr class="memdesc:gafbf1ce7a4362c75e602a4df9e1bdecd3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Wayland specific <a class="el" href="window_guide.html#GLFW_WAYLAND_APP_ID_hint">window hint</a>.  <br /></td></tr>
<tr class="separator:gafbf1ce7a4362c75e602a4df9e1bdecd3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8f6dcdc968d214ff14779564f1389264" id="r_a8f6dcdc968d214ff14779564f1389264"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a8f6dcdc968d214ff14779564f1389264">GLFW_NO_API</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:a8f6dcdc968d214ff14779564f1389264"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a01b3f66db266341425e9abee6b257db2" id="r_a01b3f66db266341425e9abee6b257db2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a01b3f66db266341425e9abee6b257db2">GLFW_OPENGL_API</a>&#160;&#160;&#160;0x00030001</td></tr>
<tr class="separator:a01b3f66db266341425e9abee6b257db2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a28d9b3bc6c2a522d815c8e146595051f" id="r_a28d9b3bc6c2a522d815c8e146595051f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a28d9b3bc6c2a522d815c8e146595051f">GLFW_OPENGL_ES_API</a>&#160;&#160;&#160;0x00030002</td></tr>
<tr class="separator:a28d9b3bc6c2a522d815c8e146595051f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b306cb27f5bb0d6d67c7356a0e0fc34" id="r_a8b306cb27f5bb0d6d67c7356a0e0fc34"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a8b306cb27f5bb0d6d67c7356a0e0fc34">GLFW_NO_ROBUSTNESS</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:a8b306cb27f5bb0d6d67c7356a0e0fc34"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aee84a679230d205005e22487ff678a85" id="r_aee84a679230d205005e22487ff678a85"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#aee84a679230d205005e22487ff678a85">GLFW_NO_RESET_NOTIFICATION</a>&#160;&#160;&#160;0x00031001</td></tr>
<tr class="separator:aee84a679230d205005e22487ff678a85"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aec1132f245143fc915b2f0995228564c" id="r_aec1132f245143fc915b2f0995228564c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#aec1132f245143fc915b2f0995228564c">GLFW_LOSE_CONTEXT_ON_RESET</a>&#160;&#160;&#160;0x00031002</td></tr>
<tr class="separator:aec1132f245143fc915b2f0995228564c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad6f2335d6f21cc9bab96633b1c111d5f" id="r_ad6f2335d6f21cc9bab96633b1c111d5f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#ad6f2335d6f21cc9bab96633b1c111d5f">GLFW_OPENGL_ANY_PROFILE</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:ad6f2335d6f21cc9bab96633b1c111d5f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af094bb16da76f66ebceb19ee213b3de8" id="r_af094bb16da76f66ebceb19ee213b3de8"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#af094bb16da76f66ebceb19ee213b3de8">GLFW_OPENGL_CORE_PROFILE</a>&#160;&#160;&#160;0x00032001</td></tr>
<tr class="separator:af094bb16da76f66ebceb19ee213b3de8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac06b663d79c8fcf04669cc8fcc0b7670" id="r_ac06b663d79c8fcf04669cc8fcc0b7670"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#ac06b663d79c8fcf04669cc8fcc0b7670">GLFW_OPENGL_COMPAT_PROFILE</a>&#160;&#160;&#160;0x00032002</td></tr>
<tr class="separator:ac06b663d79c8fcf04669cc8fcc0b7670"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aade31da5b884a84a7625c6b059b9132c" id="r_aade31da5b884a84a7625c6b059b9132c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#aade31da5b884a84a7625c6b059b9132c">GLFW_CURSOR</a>&#160;&#160;&#160;0x00033001</td></tr>
<tr class="separator:aade31da5b884a84a7625c6b059b9132c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae3bbe2315b7691ab088159eb6c9110fc" id="r_ae3bbe2315b7691ab088159eb6c9110fc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#ae3bbe2315b7691ab088159eb6c9110fc">GLFW_STICKY_KEYS</a>&#160;&#160;&#160;0x00033002</td></tr>
<tr class="separator:ae3bbe2315b7691ab088159eb6c9110fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4d7ce8ce71030c3b04e2b78145bc59d1" id="r_a4d7ce8ce71030c3b04e2b78145bc59d1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a4d7ce8ce71030c3b04e2b78145bc59d1">GLFW_STICKY_MOUSE_BUTTONS</a>&#160;&#160;&#160;0x00033003</td></tr>
<tr class="separator:a4d7ce8ce71030c3b04e2b78145bc59d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a07b84de0b52143e1958f88a7d9105947" id="r_a07b84de0b52143e1958f88a7d9105947"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a07b84de0b52143e1958f88a7d9105947">GLFW_LOCK_KEY_MODS</a>&#160;&#160;&#160;0x00033004</td></tr>
<tr class="separator:a07b84de0b52143e1958f88a7d9105947"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeeda1be76a44a1fc97c1282e06281fbb" id="r_aeeda1be76a44a1fc97c1282e06281fbb"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#aeeda1be76a44a1fc97c1282e06281fbb">GLFW_RAW_MOUSE_MOTION</a>&#160;&#160;&#160;0x00033005</td></tr>
<tr class="separator:aeeda1be76a44a1fc97c1282e06281fbb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae04dd25c8577e19fa8c97368561f6c68" id="r_ae04dd25c8577e19fa8c97368561f6c68"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#ae04dd25c8577e19fa8c97368561f6c68">GLFW_CURSOR_NORMAL</a>&#160;&#160;&#160;0x00034001</td></tr>
<tr class="separator:ae04dd25c8577e19fa8c97368561f6c68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac4d5cb9d78de8573349c58763d53bf11" id="r_ac4d5cb9d78de8573349c58763d53bf11"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#ac4d5cb9d78de8573349c58763d53bf11">GLFW_CURSOR_HIDDEN</a>&#160;&#160;&#160;0x00034002</td></tr>
<tr class="separator:ac4d5cb9d78de8573349c58763d53bf11"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2315b99a329ce53e6a13a9d46fd5ca88" id="r_a2315b99a329ce53e6a13a9d46fd5ca88"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a2315b99a329ce53e6a13a9d46fd5ca88">GLFW_CURSOR_DISABLED</a>&#160;&#160;&#160;0x00034003</td></tr>
<tr class="separator:a2315b99a329ce53e6a13a9d46fd5ca88"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac1dbfa0cb4641a0edc93412ade0895dc" id="r_ac1dbfa0cb4641a0edc93412ade0895dc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#ac1dbfa0cb4641a0edc93412ade0895dc">GLFW_CURSOR_CAPTURED</a>&#160;&#160;&#160;0x00034004</td></tr>
<tr class="separator:ac1dbfa0cb4641a0edc93412ade0895dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b47d806f285efe9bfd7aeec667297ee" id="r_a6b47d806f285efe9bfd7aeec667297ee"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a6b47d806f285efe9bfd7aeec667297ee">GLFW_ANY_RELEASE_BEHAVIOR</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:a6b47d806f285efe9bfd7aeec667297ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a999961d391db49cb4f949c1dece0e13b" id="r_a999961d391db49cb4f949c1dece0e13b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a999961d391db49cb4f949c1dece0e13b">GLFW_RELEASE_BEHAVIOR_FLUSH</a>&#160;&#160;&#160;0x00035001</td></tr>
<tr class="separator:a999961d391db49cb4f949c1dece0e13b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afca09088eccacdce4b59036cfae349c5" id="r_afca09088eccacdce4b59036cfae349c5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#afca09088eccacdce4b59036cfae349c5">GLFW_RELEASE_BEHAVIOR_NONE</a>&#160;&#160;&#160;0x00035002</td></tr>
<tr class="separator:afca09088eccacdce4b59036cfae349c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0494c9bfd3f584ab41e6dbeeaa0e6a19" id="r_a0494c9bfd3f584ab41e6dbeeaa0e6a19"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a0494c9bfd3f584ab41e6dbeeaa0e6a19">GLFW_NATIVE_CONTEXT_API</a>&#160;&#160;&#160;0x00036001</td></tr>
<tr class="separator:a0494c9bfd3f584ab41e6dbeeaa0e6a19"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a03cf65c9ab01fc8b872ba58842c531c9" id="r_a03cf65c9ab01fc8b872ba58842c531c9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a03cf65c9ab01fc8b872ba58842c531c9">GLFW_EGL_CONTEXT_API</a>&#160;&#160;&#160;0x00036002</td></tr>
<tr class="separator:a03cf65c9ab01fc8b872ba58842c531c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd34a473af9fa81f317910ea371b19e3" id="r_afd34a473af9fa81f317910ea371b19e3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#afd34a473af9fa81f317910ea371b19e3">GLFW_OSMESA_CONTEXT_API</a>&#160;&#160;&#160;0x00036003</td></tr>
<tr class="separator:afd34a473af9fa81f317910ea371b19e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae78e673449c2a2b8c560ca1b1e283228" id="r_ae78e673449c2a2b8c560ca1b1e283228"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#ae78e673449c2a2b8c560ca1b1e283228">GLFW_ANGLE_PLATFORM_TYPE_NONE</a>&#160;&#160;&#160;0x00037001</td></tr>
<tr class="separator:ae78e673449c2a2b8c560ca1b1e283228"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad8d9e97ed7790811470366b338833623" id="r_ad8d9e97ed7790811470366b338833623"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#ad8d9e97ed7790811470366b338833623">GLFW_ANGLE_PLATFORM_TYPE_OPENGL</a>&#160;&#160;&#160;0x00037002</td></tr>
<tr class="separator:ad8d9e97ed7790811470366b338833623"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0003c089da020cbf957218e70245bb65" id="r_a0003c089da020cbf957218e70245bb65"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a0003c089da020cbf957218e70245bb65">GLFW_ANGLE_PLATFORM_TYPE_OPENGLES</a>&#160;&#160;&#160;0x00037003</td></tr>
<tr class="separator:a0003c089da020cbf957218e70245bb65"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e8fdc83113d247ad792bb5c4e82c894" id="r_a6e8fdc83113d247ad792bb5c4e82c894"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a6e8fdc83113d247ad792bb5c4e82c894">GLFW_ANGLE_PLATFORM_TYPE_D3D9</a>&#160;&#160;&#160;0x00037004</td></tr>
<tr class="separator:a6e8fdc83113d247ad792bb5c4e82c894"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad6eae659811a52a5cdc43c362aedfa33" id="r_ad6eae659811a52a5cdc43c362aedfa33"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#ad6eae659811a52a5cdc43c362aedfa33">GLFW_ANGLE_PLATFORM_TYPE_D3D11</a>&#160;&#160;&#160;0x00037005</td></tr>
<tr class="separator:ad6eae659811a52a5cdc43c362aedfa33"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a579ac83506c7546709dad91960cc7ca1" id="r_a579ac83506c7546709dad91960cc7ca1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a579ac83506c7546709dad91960cc7ca1">GLFW_ANGLE_PLATFORM_TYPE_VULKAN</a>&#160;&#160;&#160;0x00037007</td></tr>
<tr class="separator:a579ac83506c7546709dad91960cc7ca1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab56d91b26cf223dc67590a93a2f8507d" id="r_ab56d91b26cf223dc67590a93a2f8507d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#ab56d91b26cf223dc67590a93a2f8507d">GLFW_ANGLE_PLATFORM_TYPE_METAL</a>&#160;&#160;&#160;0x00037008</td></tr>
<tr class="separator:ab56d91b26cf223dc67590a93a2f8507d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a92b0d7e0eaeeefaccc0ccc2ccb130e99" id="r_a92b0d7e0eaeeefaccc0ccc2ccb130e99"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a92b0d7e0eaeeefaccc0ccc2ccb130e99">GLFW_WAYLAND_PREFER_LIBDECOR</a>&#160;&#160;&#160;0x00038001</td></tr>
<tr class="separator:a92b0d7e0eaeeefaccc0ccc2ccb130e99"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aadcea7c6afbf86b848404457c4253fd7" id="r_aadcea7c6afbf86b848404457c4253fd7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#aadcea7c6afbf86b848404457c4253fd7">GLFW_WAYLAND_DISABLE_LIBDECOR</a>&#160;&#160;&#160;0x00038002</td></tr>
<tr class="separator:aadcea7c6afbf86b848404457c4253fd7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa0e681bf859ef1bb8355692a70b0ee92" id="r_aa0e681bf859ef1bb8355692a70b0ee92"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#aa0e681bf859ef1bb8355692a70b0ee92">GLFW_ANY_POSITION</a>&#160;&#160;&#160;0x80000000</td></tr>
<tr class="separator:aa0e681bf859ef1bb8355692a70b0ee92"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8ab0e717245b85506cb0eaefdea39d0a" id="r_ga8ab0e717245b85506cb0eaefdea39d0a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#ga8ab0e717245b85506cb0eaefdea39d0a">GLFW_ARROW_CURSOR</a>&#160;&#160;&#160;0x00036001</td></tr>
<tr class="memdesc:ga8ab0e717245b85506cb0eaefdea39d0a"><td class="mdescLeft">&#160;</td><td class="mdescRight">The regular arrow cursor shape.  <br /></td></tr>
<tr class="separator:ga8ab0e717245b85506cb0eaefdea39d0a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga36185f4375eaada1b04e431244774c86" id="r_ga36185f4375eaada1b04e431244774c86"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#ga36185f4375eaada1b04e431244774c86">GLFW_IBEAM_CURSOR</a>&#160;&#160;&#160;0x00036002</td></tr>
<tr class="memdesc:ga36185f4375eaada1b04e431244774c86"><td class="mdescLeft">&#160;</td><td class="mdescRight">The text input I-beam cursor shape.  <br /></td></tr>
<tr class="separator:ga36185f4375eaada1b04e431244774c86"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8af88c0ea05ab9e8f9ac1530e8873c22" id="r_ga8af88c0ea05ab9e8f9ac1530e8873c22"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#ga8af88c0ea05ab9e8f9ac1530e8873c22">GLFW_CROSSHAIR_CURSOR</a>&#160;&#160;&#160;0x00036003</td></tr>
<tr class="memdesc:ga8af88c0ea05ab9e8f9ac1530e8873c22"><td class="mdescLeft">&#160;</td><td class="mdescRight">The crosshair cursor shape.  <br /></td></tr>
<tr class="separator:ga8af88c0ea05ab9e8f9ac1530e8873c22"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaad01a50929fb515bf27e4462c51f6ed0" id="r_gaad01a50929fb515bf27e4462c51f6ed0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#gaad01a50929fb515bf27e4462c51f6ed0">GLFW_POINTING_HAND_CURSOR</a>&#160;&#160;&#160;0x00036004</td></tr>
<tr class="memdesc:gaad01a50929fb515bf27e4462c51f6ed0"><td class="mdescLeft">&#160;</td><td class="mdescRight">The pointing hand cursor shape.  <br /></td></tr>
<tr class="separator:gaad01a50929fb515bf27e4462c51f6ed0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2010a43dc1050a7c9154148a63cf01ad" id="r_ga2010a43dc1050a7c9154148a63cf01ad"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#ga2010a43dc1050a7c9154148a63cf01ad">GLFW_RESIZE_EW_CURSOR</a>&#160;&#160;&#160;0x00036005</td></tr>
<tr class="memdesc:ga2010a43dc1050a7c9154148a63cf01ad"><td class="mdescLeft">&#160;</td><td class="mdescRight">The horizontal resize/move arrow shape.  <br /></td></tr>
<tr class="separator:ga2010a43dc1050a7c9154148a63cf01ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa59214e8cdc8c8adf08fdf125ed68388" id="r_gaa59214e8cdc8c8adf08fdf125ed68388"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#gaa59214e8cdc8c8adf08fdf125ed68388">GLFW_RESIZE_NS_CURSOR</a>&#160;&#160;&#160;0x00036006</td></tr>
<tr class="memdesc:gaa59214e8cdc8c8adf08fdf125ed68388"><td class="mdescLeft">&#160;</td><td class="mdescRight">The vertical resize/move arrow shape.  <br /></td></tr>
<tr class="separator:gaa59214e8cdc8c8adf08fdf125ed68388"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadf2c0a495ec9cef4e1a364cc99aa78da" id="r_gadf2c0a495ec9cef4e1a364cc99aa78da"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#gadf2c0a495ec9cef4e1a364cc99aa78da">GLFW_RESIZE_NWSE_CURSOR</a>&#160;&#160;&#160;0x00036007</td></tr>
<tr class="memdesc:gadf2c0a495ec9cef4e1a364cc99aa78da"><td class="mdescLeft">&#160;</td><td class="mdescRight">The top-left to bottom-right diagonal resize/move arrow shape.  <br /></td></tr>
<tr class="separator:gadf2c0a495ec9cef4e1a364cc99aa78da"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab06bba3b407f92807ba9b48de667a323" id="r_gab06bba3b407f92807ba9b48de667a323"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#gab06bba3b407f92807ba9b48de667a323">GLFW_RESIZE_NESW_CURSOR</a>&#160;&#160;&#160;0x00036008</td></tr>
<tr class="memdesc:gab06bba3b407f92807ba9b48de667a323"><td class="mdescLeft">&#160;</td><td class="mdescRight">The top-right to bottom-left diagonal resize/move arrow shape.  <br /></td></tr>
<tr class="separator:gab06bba3b407f92807ba9b48de667a323"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3a5f4811155f95ccafbbb4c9a899fc1d" id="r_ga3a5f4811155f95ccafbbb4c9a899fc1d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#ga3a5f4811155f95ccafbbb4c9a899fc1d">GLFW_RESIZE_ALL_CURSOR</a>&#160;&#160;&#160;0x00036009</td></tr>
<tr class="memdesc:ga3a5f4811155f95ccafbbb4c9a899fc1d"><td class="mdescLeft">&#160;</td><td class="mdescRight">The omni-directional resize/move cursor shape.  <br /></td></tr>
<tr class="separator:ga3a5f4811155f95ccafbbb4c9a899fc1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga297c503095b034bc8891393b637844b1" id="r_ga297c503095b034bc8891393b637844b1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#ga297c503095b034bc8891393b637844b1">GLFW_NOT_ALLOWED_CURSOR</a>&#160;&#160;&#160;0x0003600A</td></tr>
<tr class="memdesc:ga297c503095b034bc8891393b637844b1"><td class="mdescLeft">&#160;</td><td class="mdescRight">The operation-not-allowed shape.  <br /></td></tr>
<tr class="separator:ga297c503095b034bc8891393b637844b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabb3eb0109f11bb808fc34659177ca962" id="r_gabb3eb0109f11bb808fc34659177ca962"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#gabb3eb0109f11bb808fc34659177ca962">GLFW_HRESIZE_CURSOR</a>&#160;&#160;&#160;<a class="el" href="group__shapes.html#ga2010a43dc1050a7c9154148a63cf01ad">GLFW_RESIZE_EW_CURSOR</a></td></tr>
<tr class="memdesc:gabb3eb0109f11bb808fc34659177ca962"><td class="mdescLeft">&#160;</td><td class="mdescRight">Legacy name for compatibility.  <br /></td></tr>
<tr class="separator:gabb3eb0109f11bb808fc34659177ca962"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf024f0e1ff8366fb2b5c260509a1fce5" id="r_gaf024f0e1ff8366fb2b5c260509a1fce5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#gaf024f0e1ff8366fb2b5c260509a1fce5">GLFW_VRESIZE_CURSOR</a>&#160;&#160;&#160;<a class="el" href="group__shapes.html#gaa59214e8cdc8c8adf08fdf125ed68388">GLFW_RESIZE_NS_CURSOR</a></td></tr>
<tr class="memdesc:gaf024f0e1ff8366fb2b5c260509a1fce5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Legacy name for compatibility.  <br /></td></tr>
<tr class="separator:gaf024f0e1ff8366fb2b5c260509a1fce5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1db35e20849e0837c82e3dc1fd797263" id="r_ga1db35e20849e0837c82e3dc1fd797263"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#ga1db35e20849e0837c82e3dc1fd797263">GLFW_HAND_CURSOR</a>&#160;&#160;&#160;<a class="el" href="group__shapes.html#gaad01a50929fb515bf27e4462c51f6ed0">GLFW_POINTING_HAND_CURSOR</a></td></tr>
<tr class="memdesc:ga1db35e20849e0837c82e3dc1fd797263"><td class="mdescLeft">&#160;</td><td class="mdescRight">Legacy name for compatibility.  <br /></td></tr>
<tr class="separator:ga1db35e20849e0837c82e3dc1fd797263"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe11513fd1ffbee5bb9b173f06028b9e" id="r_abe11513fd1ffbee5bb9b173f06028b9e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#abe11513fd1ffbee5bb9b173f06028b9e">GLFW_CONNECTED</a>&#160;&#160;&#160;0x00040001</td></tr>
<tr class="separator:abe11513fd1ffbee5bb9b173f06028b9e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab64b25921ef21d89252d6f0a71bfc32" id="r_aab64b25921ef21d89252d6f0a71bfc32"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#aab64b25921ef21d89252d6f0a71bfc32">GLFW_DISCONNECTED</a>&#160;&#160;&#160;0x00040002</td></tr>
<tr class="separator:aab64b25921ef21d89252d6f0a71bfc32"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab9c0534709fda03ec8959201da3a9a18" id="r_gab9c0534709fda03ec8959201da3a9a18"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gab9c0534709fda03ec8959201da3a9a18">GLFW_JOYSTICK_HAT_BUTTONS</a>&#160;&#160;&#160;0x00050001</td></tr>
<tr class="memdesc:gab9c0534709fda03ec8959201da3a9a18"><td class="mdescLeft">&#160;</td><td class="mdescRight">Joystick hat buttons init hint.  <br /></td></tr>
<tr class="separator:gab9c0534709fda03ec8959201da3a9a18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaec269b24cf549ab46292c0125d8bbdce" id="r_gaec269b24cf549ab46292c0125d8bbdce"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gaec269b24cf549ab46292c0125d8bbdce">GLFW_ANGLE_PLATFORM_TYPE</a>&#160;&#160;&#160;0x00050002</td></tr>
<tr class="memdesc:gaec269b24cf549ab46292c0125d8bbdce"><td class="mdescLeft">&#160;</td><td class="mdescRight">ANGLE rendering backend init hint.  <br /></td></tr>
<tr class="separator:gaec269b24cf549ab46292c0125d8bbdce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9d38bf1fdf4f91d6565401734a7cd967" id="r_ga9d38bf1fdf4f91d6565401734a7cd967"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga9d38bf1fdf4f91d6565401734a7cd967">GLFW_PLATFORM</a>&#160;&#160;&#160;0x00050003</td></tr>
<tr class="memdesc:ga9d38bf1fdf4f91d6565401734a7cd967"><td class="mdescLeft">&#160;</td><td class="mdescRight">Platform selection init hint.  <br /></td></tr>
<tr class="separator:ga9d38bf1fdf4f91d6565401734a7cd967"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab937983147a3158d45f88fad7129d9f2" id="r_gab937983147a3158d45f88fad7129d9f2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gab937983147a3158d45f88fad7129d9f2">GLFW_COCOA_CHDIR_RESOURCES</a>&#160;&#160;&#160;0x00051001</td></tr>
<tr class="memdesc:gab937983147a3158d45f88fad7129d9f2"><td class="mdescLeft">&#160;</td><td class="mdescRight">macOS specific init hint.  <br /></td></tr>
<tr class="separator:gab937983147a3158d45f88fad7129d9f2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga71e0b4ce2f2696a84a9b8c5e12dc70cf" id="r_ga71e0b4ce2f2696a84a9b8c5e12dc70cf"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga71e0b4ce2f2696a84a9b8c5e12dc70cf">GLFW_COCOA_MENUBAR</a>&#160;&#160;&#160;0x00051002</td></tr>
<tr class="memdesc:ga71e0b4ce2f2696a84a9b8c5e12dc70cf"><td class="mdescLeft">&#160;</td><td class="mdescRight">macOS specific init hint.  <br /></td></tr>
<tr class="separator:ga71e0b4ce2f2696a84a9b8c5e12dc70cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa341e303ebeb8e4199b8ab8be84351f6" id="r_gaa341e303ebeb8e4199b8ab8be84351f6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gaa341e303ebeb8e4199b8ab8be84351f6">GLFW_X11_XCB_VULKAN_SURFACE</a>&#160;&#160;&#160;0x00052001</td></tr>
<tr class="memdesc:gaa341e303ebeb8e4199b8ab8be84351f6"><td class="mdescLeft">&#160;</td><td class="mdescRight">X11 specific init hint.  <br /></td></tr>
<tr class="separator:gaa341e303ebeb8e4199b8ab8be84351f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2a3f2fd7695902c498b050215b3db452" id="r_ga2a3f2fd7695902c498b050215b3db452"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga2a3f2fd7695902c498b050215b3db452">GLFW_WAYLAND_LIBDECOR</a>&#160;&#160;&#160;0x00053001</td></tr>
<tr class="memdesc:ga2a3f2fd7695902c498b050215b3db452"><td class="mdescLeft">&#160;</td><td class="mdescRight">Wayland specific init hint.  <br /></td></tr>
<tr class="separator:ga2a3f2fd7695902c498b050215b3db452"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga18b2d37374d0dea28cd69194fa85b859" id="r_ga18b2d37374d0dea28cd69194fa85b859"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga18b2d37374d0dea28cd69194fa85b859">GLFW_ANY_PLATFORM</a>&#160;&#160;&#160;0x00060000</td></tr>
<tr class="memdesc:ga18b2d37374d0dea28cd69194fa85b859"><td class="mdescLeft">&#160;</td><td class="mdescRight">Hint value that enables automatic platform selection.  <br /></td></tr>
<tr class="separator:ga18b2d37374d0dea28cd69194fa85b859"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8d3d17df2ab57492cef665da52c603a1" id="r_ga8d3d17df2ab57492cef665da52c603a1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga8d3d17df2ab57492cef665da52c603a1">GLFW_PLATFORM_WIN32</a>&#160;&#160;&#160;0x00060001</td></tr>
<tr class="separator:ga8d3d17df2ab57492cef665da52c603a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga83b18714254f75bc2f0cdbafa0f10b6b" id="r_ga83b18714254f75bc2f0cdbafa0f10b6b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga83b18714254f75bc2f0cdbafa0f10b6b">GLFW_PLATFORM_COCOA</a>&#160;&#160;&#160;0x00060002</td></tr>
<tr class="separator:ga83b18714254f75bc2f0cdbafa0f10b6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac4b08906a3cbf26c518a4a543eedd740" id="r_gac4b08906a3cbf26c518a4a543eedd740"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gac4b08906a3cbf26c518a4a543eedd740">GLFW_PLATFORM_WAYLAND</a>&#160;&#160;&#160;0x00060003</td></tr>
<tr class="separator:gac4b08906a3cbf26c518a4a543eedd740"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf5333f3933e9c248a00cfda6523f386b" id="r_gaf5333f3933e9c248a00cfda6523f386b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gaf5333f3933e9c248a00cfda6523f386b">GLFW_PLATFORM_X11</a>&#160;&#160;&#160;0x00060004</td></tr>
<tr class="separator:gaf5333f3933e9c248a00cfda6523f386b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac06fad5a4866ae7a1d7b2675fac72d7f" id="r_gac06fad5a4866ae7a1d7b2675fac72d7f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gac06fad5a4866ae7a1d7b2675fac72d7f">GLFW_PLATFORM_NULL</a>&#160;&#160;&#160;0x00060005</td></tr>
<tr class="separator:gac06fad5a4866ae7a1d7b2675fac72d7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7a2edf2c18446833d27d07f1b7f3d571" id="r_a7a2edf2c18446833d27d07f1b7f3d571"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a7a2edf2c18446833d27d07f1b7f3d571">GLFW_DONT_CARE</a>&#160;&#160;&#160;-1</td></tr>
<tr class="separator:a7a2edf2c18446833d27d07f1b7f3d571"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa97755eb47e4bf2727ad45d610e18206" id="r_aa97755eb47e4bf2727ad45d610e18206"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#aa97755eb47e4bf2727ad45d610e18206">GLAPIENTRY</a>&#160;&#160;&#160;APIENTRY</td></tr>
<tr class="separator:aa97755eb47e4bf2727ad45d610e18206"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b526ac796be993406ea2f1642c25fc3" id="r_a3b526ac796be993406ea2f1642c25fc3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="glfw3_8h.html#a3b526ac796be993406ea2f1642c25fc3">GLFW_GLAPIENTRY_DEFINED</a></td></tr>
<tr class="separator:a3b526ac796be993406ea2f1642c25fc3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr><td colspan="2"><div class="groupHeader">GLFW version macros</div></td></tr>
<tr class="memitem:ga6337d9ea43b22fc529b2bba066b4a576" id="r_ga6337d9ea43b22fc529b2bba066b4a576"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga6337d9ea43b22fc529b2bba066b4a576">GLFW_VERSION_MAJOR</a>&#160;&#160;&#160;3</td></tr>
<tr class="memdesc:ga6337d9ea43b22fc529b2bba066b4a576"><td class="mdescLeft">&#160;</td><td class="mdescRight">The major version number of the GLFW header.  <br /></td></tr>
<tr class="separator:ga6337d9ea43b22fc529b2bba066b4a576"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf80d40f0aea7088ff337606e9c48f7a3" id="r_gaf80d40f0aea7088ff337606e9c48f7a3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gaf80d40f0aea7088ff337606e9c48f7a3">GLFW_VERSION_MINOR</a>&#160;&#160;&#160;4</td></tr>
<tr class="memdesc:gaf80d40f0aea7088ff337606e9c48f7a3"><td class="mdescLeft">&#160;</td><td class="mdescRight">The minor version number of the GLFW header.  <br /></td></tr>
<tr class="separator:gaf80d40f0aea7088ff337606e9c48f7a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab72ae2e2035d9ea461abc3495eac0502" id="r_gab72ae2e2035d9ea461abc3495eac0502"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gab72ae2e2035d9ea461abc3495eac0502">GLFW_VERSION_REVISION</a>&#160;&#160;&#160;0</td></tr>
<tr class="memdesc:gab72ae2e2035d9ea461abc3495eac0502"><td class="mdescLeft">&#160;</td><td class="mdescRight">The revision number of the GLFW header.  <br /></td></tr>
<tr class="separator:gab72ae2e2035d9ea461abc3495eac0502"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr><td colspan="2"><div class="groupHeader">Key and button actions</div></td></tr>
<tr class="memitem:gada11d965c4da13090ad336e030e4d11f" id="r_gada11d965c4da13090ad336e030e4d11f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gada11d965c4da13090ad336e030e4d11f">GLFW_RELEASE</a>&#160;&#160;&#160;0</td></tr>
<tr class="memdesc:gada11d965c4da13090ad336e030e4d11f"><td class="mdescLeft">&#160;</td><td class="mdescRight">The key or mouse button was released.  <br /></td></tr>
<tr class="separator:gada11d965c4da13090ad336e030e4d11f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2485743d0b59df3791c45951c4195265" id="r_ga2485743d0b59df3791c45951c4195265"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga2485743d0b59df3791c45951c4195265">GLFW_PRESS</a>&#160;&#160;&#160;1</td></tr>
<tr class="memdesc:ga2485743d0b59df3791c45951c4195265"><td class="mdescLeft">&#160;</td><td class="mdescRight">The key or mouse button was pressed.  <br /></td></tr>
<tr class="separator:ga2485743d0b59df3791c45951c4195265"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac96fd3b9fc66c6f0eebaf6532595338f" id="r_gac96fd3b9fc66c6f0eebaf6532595338f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gac96fd3b9fc66c6f0eebaf6532595338f">GLFW_REPEAT</a>&#160;&#160;&#160;2</td></tr>
<tr class="memdesc:gac96fd3b9fc66c6f0eebaf6532595338f"><td class="mdescLeft">&#160;</td><td class="mdescRight">The key was held down until it repeated.  <br /></td></tr>
<tr class="separator:gac96fd3b9fc66c6f0eebaf6532595338f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga3d47c2d2fbe0be9c505d0e04e91a133c" id="r_ga3d47c2d2fbe0be9c505d0e04e91a133c"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__context.html#ga3d47c2d2fbe0be9c505d0e04e91a133c">GLFWglproc</a>) (void)</td></tr>
<tr class="memdesc:ga3d47c2d2fbe0be9c505d0e04e91a133c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Client API function pointer type.  <br /></td></tr>
<tr class="separator:ga3d47c2d2fbe0be9c505d0e04e91a133c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga70c01918dc9d233a4fbe0681a43018af" id="r_ga70c01918dc9d233a4fbe0681a43018af"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__vulkan.html#ga70c01918dc9d233a4fbe0681a43018af">GLFWvkproc</a>) (void)</td></tr>
<tr class="memdesc:ga70c01918dc9d233a4fbe0681a43018af"><td class="mdescLeft">&#160;</td><td class="mdescRight">Vulkan API function pointer type.  <br /></td></tr>
<tr class="separator:ga70c01918dc9d233a4fbe0681a43018af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8d9efd1cde9426692c73fe40437d0ae3" id="r_ga8d9efd1cde9426692c73fe40437d0ae3"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a></td></tr>
<tr class="memdesc:ga8d9efd1cde9426692c73fe40437d0ae3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Opaque monitor object.  <br /></td></tr>
<tr class="separator:ga8d9efd1cde9426692c73fe40437d0ae3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3c96d80d363e67d13a41b5d1821f3242" id="r_ga3c96d80d363e67d13a41b5d1821f3242"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a></td></tr>
<tr class="memdesc:ga3c96d80d363e67d13a41b5d1821f3242"><td class="mdescLeft">&#160;</td><td class="mdescRight">Opaque window object.  <br /></td></tr>
<tr class="separator:ga3c96d80d363e67d13a41b5d1821f3242"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga89261ae18c75e863aaf2656ecdd238f4" id="r_ga89261ae18c75e863aaf2656ecdd238f4"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a></td></tr>
<tr class="memdesc:ga89261ae18c75e863aaf2656ecdd238f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Opaque cursor object.  <br /></td></tr>
<tr class="separator:ga89261ae18c75e863aaf2656ecdd238f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4306a564e9f60f4de8cc8f31731a3120" id="r_ga4306a564e9f60f4de8cc8f31731a3120"><td class="memItemLeft" align="right" valign="top">typedef void *(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga4306a564e9f60f4de8cc8f31731a3120">GLFWallocatefun</a>) (size_t size, void *user)</td></tr>
<tr class="memdesc:ga4306a564e9f60f4de8cc8f31731a3120"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for memory allocation callbacks.  <br /></td></tr>
<tr class="separator:ga4306a564e9f60f4de8cc8f31731a3120"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3e88a829615d8efe8bec1746f7309c63" id="r_ga3e88a829615d8efe8bec1746f7309c63"><td class="memItemLeft" align="right" valign="top">typedef void *(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga3e88a829615d8efe8bec1746f7309c63">GLFWreallocatefun</a>) (void *block, size_t size, void *user)</td></tr>
<tr class="memdesc:ga3e88a829615d8efe8bec1746f7309c63"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for memory reallocation callbacks.  <br /></td></tr>
<tr class="separator:ga3e88a829615d8efe8bec1746f7309c63"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7181615eda94c4b07bd72bdcee39fa28" id="r_ga7181615eda94c4b07bd72bdcee39fa28"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga7181615eda94c4b07bd72bdcee39fa28">GLFWdeallocatefun</a>) (void *block, void *user)</td></tr>
<tr class="memdesc:ga7181615eda94c4b07bd72bdcee39fa28"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for memory deallocation callbacks.  <br /></td></tr>
<tr class="separator:ga7181615eda94c4b07bd72bdcee39fa28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8184701785c096b3862a75cda1bf44a3" id="r_ga8184701785c096b3862a75cda1bf44a3"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga8184701785c096b3862a75cda1bf44a3">GLFWerrorfun</a>) (int error_code, const char *description)</td></tr>
<tr class="memdesc:ga8184701785c096b3862a75cda1bf44a3"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for error callbacks.  <br /></td></tr>
<tr class="separator:ga8184701785c096b3862a75cda1bf44a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabe287973a21a8f927cde4db06b8dcbe9" id="r_gabe287973a21a8f927cde4db06b8dcbe9"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gabe287973a21a8f927cde4db06b8dcbe9">GLFWwindowposfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int xpos, int ypos)</td></tr>
<tr class="memdesc:gabe287973a21a8f927cde4db06b8dcbe9"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for window position callbacks.  <br /></td></tr>
<tr class="separator:gabe287973a21a8f927cde4db06b8dcbe9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaec0282944bb810f6f3163ec02da90350" id="r_gaec0282944bb810f6f3163ec02da90350"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaec0282944bb810f6f3163ec02da90350">GLFWwindowsizefun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int width, int height)</td></tr>
<tr class="memdesc:gaec0282944bb810f6f3163ec02da90350"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for window size callbacks.  <br /></td></tr>
<tr class="separator:gaec0282944bb810f6f3163ec02da90350"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabf859b936d80961b7d39013a9694cc3e" id="r_gabf859b936d80961b7d39013a9694cc3e"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gabf859b936d80961b7d39013a9694cc3e">GLFWwindowclosefun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:gabf859b936d80961b7d39013a9694cc3e"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for window close callbacks.  <br /></td></tr>
<tr class="separator:gabf859b936d80961b7d39013a9694cc3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga431663a1427d2eb3a273bc398b6737b5" id="r_ga431663a1427d2eb3a273bc398b6737b5"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga431663a1427d2eb3a273bc398b6737b5">GLFWwindowrefreshfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga431663a1427d2eb3a273bc398b6737b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for window content refresh callbacks.  <br /></td></tr>
<tr class="separator:ga431663a1427d2eb3a273bc398b6737b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabc58c47e9d93f6eb1862d615c3680f46" id="r_gabc58c47e9d93f6eb1862d615c3680f46"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gabc58c47e9d93f6eb1862d615c3680f46">GLFWwindowfocusfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int focused)</td></tr>
<tr class="memdesc:gabc58c47e9d93f6eb1862d615c3680f46"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for window focus callbacks.  <br /></td></tr>
<tr class="separator:gabc58c47e9d93f6eb1862d615c3680f46"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga35c658cccba236f26e7adee0e25f6a4f" id="r_ga35c658cccba236f26e7adee0e25f6a4f"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga35c658cccba236f26e7adee0e25f6a4f">GLFWwindowiconifyfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int iconified)</td></tr>
<tr class="memdesc:ga35c658cccba236f26e7adee0e25f6a4f"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for window iconify callbacks.  <br /></td></tr>
<tr class="separator:ga35c658cccba236f26e7adee0e25f6a4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3017196fdaec33ac3e095765176c2a90" id="r_ga3017196fdaec33ac3e095765176c2a90"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga3017196fdaec33ac3e095765176c2a90">GLFWwindowmaximizefun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int maximized)</td></tr>
<tr class="memdesc:ga3017196fdaec33ac3e095765176c2a90"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for window maximize callbacks.  <br /></td></tr>
<tr class="separator:ga3017196fdaec33ac3e095765176c2a90"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae18026e294dde685ed2e5f759533144d" id="r_gae18026e294dde685ed2e5f759533144d"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gae18026e294dde685ed2e5f759533144d">GLFWframebuffersizefun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int width, int height)</td></tr>
<tr class="memdesc:gae18026e294dde685ed2e5f759533144d"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for framebuffer size callbacks.  <br /></td></tr>
<tr class="separator:gae18026e294dde685ed2e5f759533144d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga77f288a2d04bb3c77c7d9615d08cf70e" id="r_ga77f288a2d04bb3c77c7d9615d08cf70e"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga77f288a2d04bb3c77c7d9615d08cf70e">GLFWwindowcontentscalefun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, float xscale, float yscale)</td></tr>
<tr class="memdesc:ga77f288a2d04bb3c77c7d9615d08cf70e"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for window content scale callbacks.  <br /></td></tr>
<tr class="separator:ga77f288a2d04bb3c77c7d9615d08cf70e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0184dcb59f6d85d735503dcaae809727" id="r_ga0184dcb59f6d85d735503dcaae809727"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga0184dcb59f6d85d735503dcaae809727">GLFWmousebuttonfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int button, int action, int mods)</td></tr>
<tr class="memdesc:ga0184dcb59f6d85d735503dcaae809727"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for mouse button callbacks.  <br /></td></tr>
<tr class="separator:ga0184dcb59f6d85d735503dcaae809727"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad6fae41b3ac2e4209aaa87b596c57f68" id="r_gad6fae41b3ac2e4209aaa87b596c57f68"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gad6fae41b3ac2e4209aaa87b596c57f68">GLFWcursorposfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, double xpos, double ypos)</td></tr>
<tr class="memdesc:gad6fae41b3ac2e4209aaa87b596c57f68"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for cursor position callbacks.  <br /></td></tr>
<tr class="separator:gad6fae41b3ac2e4209aaa87b596c57f68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa93dc4818ac9ab32532909d53a337cbe" id="r_gaa93dc4818ac9ab32532909d53a337cbe"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaa93dc4818ac9ab32532909d53a337cbe">GLFWcursorenterfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int entered)</td></tr>
<tr class="memdesc:gaa93dc4818ac9ab32532909d53a337cbe"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for cursor enter/leave callbacks.  <br /></td></tr>
<tr class="separator:gaa93dc4818ac9ab32532909d53a337cbe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf656112c33de3efdb227fa58f0134cf5" id="r_gaf656112c33de3efdb227fa58f0134cf5"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaf656112c33de3efdb227fa58f0134cf5">GLFWscrollfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, double xoffset, double yoffset)</td></tr>
<tr class="memdesc:gaf656112c33de3efdb227fa58f0134cf5"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for scroll callbacks.  <br /></td></tr>
<tr class="separator:gaf656112c33de3efdb227fa58f0134cf5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5bd751b27b90f865d2ea613533f0453c" id="r_ga5bd751b27b90f865d2ea613533f0453c"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga5bd751b27b90f865d2ea613533f0453c">GLFWkeyfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int key, int scancode, int action, int mods)</td></tr>
<tr class="memdesc:ga5bd751b27b90f865d2ea613533f0453c"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for keyboard key callbacks.  <br /></td></tr>
<tr class="separator:ga5bd751b27b90f865d2ea613533f0453c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1ab90a55cf3f58639b893c0f4118cb6e" id="r_ga1ab90a55cf3f58639b893c0f4118cb6e"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga1ab90a55cf3f58639b893c0f4118cb6e">GLFWcharfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, unsigned int codepoint)</td></tr>
<tr class="memdesc:ga1ab90a55cf3f58639b893c0f4118cb6e"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for Unicode character callbacks.  <br /></td></tr>
<tr class="separator:ga1ab90a55cf3f58639b893c0f4118cb6e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac3cf64f90b6219c05ac7b7822d5a4b8f" id="r_gac3cf64f90b6219c05ac7b7822d5a4b8f"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gac3cf64f90b6219c05ac7b7822d5a4b8f">GLFWcharmodsfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, unsigned int codepoint, int mods)</td></tr>
<tr class="memdesc:gac3cf64f90b6219c05ac7b7822d5a4b8f"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for Unicode character with modifiers callbacks.  <br /></td></tr>
<tr class="separator:gac3cf64f90b6219c05ac7b7822d5a4b8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaba73c3274062c18723b7f05862d94b2" id="r_gaaba73c3274062c18723b7f05862d94b2"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaaba73c3274062c18723b7f05862d94b2">GLFWdropfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int path_count, const char *paths[])</td></tr>
<tr class="memdesc:gaaba73c3274062c18723b7f05862d94b2"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for path drop callbacks.  <br /></td></tr>
<tr class="separator:gaaba73c3274062c18723b7f05862d94b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaabe16caca8dea952504dfdebdf4cd249" id="r_gaabe16caca8dea952504dfdebdf4cd249"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#gaabe16caca8dea952504dfdebdf4cd249">GLFWmonitorfun</a>) (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, int event)</td></tr>
<tr class="memdesc:gaabe16caca8dea952504dfdebdf4cd249"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for monitor configuration callbacks.  <br /></td></tr>
<tr class="separator:gaabe16caca8dea952504dfdebdf4cd249"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa21ad5986ae9a26077a40142efb56243" id="r_gaa21ad5986ae9a26077a40142efb56243"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaa21ad5986ae9a26077a40142efb56243">GLFWjoystickfun</a>) (int jid, int event)</td></tr>
<tr class="memdesc:gaa21ad5986ae9a26077a40142efb56243"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for joystick configuration callbacks.  <br /></td></tr>
<tr class="separator:gaa21ad5986ae9a26077a40142efb56243"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga902c2816ac9b34b757282daab59b2565" id="r_ga902c2816ac9b34b757282daab59b2565"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga902c2816ac9b34b757282daab59b2565">GLFWvidmode</a></td></tr>
<tr class="memdesc:ga902c2816ac9b34b757282daab59b2565"><td class="mdescLeft">&#160;</td><td class="mdescRight">Video mode type.  <br /></td></tr>
<tr class="separator:ga902c2816ac9b34b757282daab59b2565"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga939cf093cb0af0498b7b54dc2e181404" id="r_ga939cf093cb0af0498b7b54dc2e181404"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga939cf093cb0af0498b7b54dc2e181404">GLFWgammaramp</a></td></tr>
<tr class="memdesc:ga939cf093cb0af0498b7b54dc2e181404"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gamma ramp.  <br /></td></tr>
<tr class="separator:ga939cf093cb0af0498b7b54dc2e181404"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7cc0a09de172fa7250872046f8c4d2ca" id="r_ga7cc0a09de172fa7250872046f8c4d2ca"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="struct_g_l_f_wimage.html">GLFWimage</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga7cc0a09de172fa7250872046f8c4d2ca">GLFWimage</a></td></tr>
<tr class="memdesc:ga7cc0a09de172fa7250872046f8c4d2ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">Image data.  <br /></td></tr>
<tr class="separator:ga7cc0a09de172fa7250872046f8c4d2ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga61acfb1f28f751438dd221225c5e725d" id="r_ga61acfb1f28f751438dd221225c5e725d"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="struct_g_l_f_wgamepadstate.html">GLFWgamepadstate</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga61acfb1f28f751438dd221225c5e725d">GLFWgamepadstate</a></td></tr>
<tr class="memdesc:ga61acfb1f28f751438dd221225c5e725d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gamepad input state.  <br /></td></tr>
<tr class="separator:ga61acfb1f28f751438dd221225c5e725d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga145c57d7f2aeda0b704a5a4ba1d6104b" id="r_ga145c57d7f2aeda0b704a5a4ba1d6104b"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="struct_g_l_f_wallocator.html">GLFWallocator</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga145c57d7f2aeda0b704a5a4ba1d6104b">GLFWallocator</a></td></tr>
<tr class="memdesc:ga145c57d7f2aeda0b704a5a4ba1d6104b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Custom heap memory allocator.  <br /></td></tr>
<tr class="separator:ga145c57d7f2aeda0b704a5a4ba1d6104b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga317aac130a235ab08c6db0834907d85e" id="r_ga317aac130a235ab08c6db0834907d85e"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a> (void)</td></tr>
<tr class="memdesc:ga317aac130a235ab08c6db0834907d85e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initializes the GLFW library.  <br /></td></tr>
<tr class="separator:ga317aac130a235ab08c6db0834907d85e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaae48c0a18607ea4a4ba951d939f0901" id="r_gaaae48c0a18607ea4a4ba951d939f0901"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a> (void)</td></tr>
<tr class="memdesc:gaaae48c0a18607ea4a4ba951d939f0901"><td class="mdescLeft">&#160;</td><td class="mdescRight">Terminates the GLFW library.  <br /></td></tr>
<tr class="separator:gaaae48c0a18607ea4a4ba951d939f0901"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga110fd1d3f0412822b4f1908c026f724a" id="r_ga110fd1d3f0412822b4f1908c026f724a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga110fd1d3f0412822b4f1908c026f724a">glfwInitHint</a> (int hint, int value)</td></tr>
<tr class="memdesc:ga110fd1d3f0412822b4f1908c026f724a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the specified init hint to the desired value.  <br /></td></tr>
<tr class="separator:ga110fd1d3f0412822b4f1908c026f724a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9dde93e9891fa7dd17e4194c9f3ae7c6" id="r_ga9dde93e9891fa7dd17e4194c9f3ae7c6"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a> (const <a class="el" href="struct_g_l_f_wallocator.html">GLFWallocator</a> *allocator)</td></tr>
<tr class="memdesc:ga9dde93e9891fa7dd17e4194c9f3ae7c6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the init allocator to the desired value.  <br /></td></tr>
<tr class="separator:ga9dde93e9891fa7dd17e4194c9f3ae7c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga76af552d0307bb5f7791f245417d4752" id="r_ga76af552d0307bb5f7791f245417d4752"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga76af552d0307bb5f7791f245417d4752">glfwInitVulkanLoader</a> (PFN_vkGetInstanceProcAddr loader)</td></tr>
<tr class="memdesc:ga76af552d0307bb5f7791f245417d4752"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the desired Vulkan <code>vkGetInstanceProcAddr</code> function.  <br /></td></tr>
<tr class="separator:ga76af552d0307bb5f7791f245417d4752"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9f8ffaacf3c269cc48eafbf8b9b71197" id="r_ga9f8ffaacf3c269cc48eafbf8b9b71197"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga9f8ffaacf3c269cc48eafbf8b9b71197">glfwGetVersion</a> (int *major, int *minor, int *rev)</td></tr>
<tr class="memdesc:ga9f8ffaacf3c269cc48eafbf8b9b71197"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the version of the GLFW library.  <br /></td></tr>
<tr class="separator:ga9f8ffaacf3c269cc48eafbf8b9b71197"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga026abd003c8e6501981ab1662062f1c0" id="r_ga026abd003c8e6501981ab1662062f1c0"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga026abd003c8e6501981ab1662062f1c0">glfwGetVersionString</a> (void)</td></tr>
<tr class="memdesc:ga026abd003c8e6501981ab1662062f1c0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a string describing the compile-time configuration.  <br /></td></tr>
<tr class="separator:ga026abd003c8e6501981ab1662062f1c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga944986b4ec0b928d488141f92982aa18" id="r_ga944986b4ec0b928d488141f92982aa18"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga944986b4ec0b928d488141f92982aa18">glfwGetError</a> (const char **description)</td></tr>
<tr class="memdesc:ga944986b4ec0b928d488141f92982aa18"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns and clears the last error for the calling thread.  <br /></td></tr>
<tr class="separator:ga944986b4ec0b928d488141f92982aa18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaff45816610d53f0b83656092a4034f40" id="r_gaff45816610d53f0b83656092a4034f40"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__init.html#ga8184701785c096b3862a75cda1bf44a3">GLFWerrorfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#gaff45816610d53f0b83656092a4034f40">glfwSetErrorCallback</a> (<a class="el" href="group__init.html#ga8184701785c096b3862a75cda1bf44a3">GLFWerrorfun</a> callback)</td></tr>
<tr class="memdesc:gaff45816610d53f0b83656092a4034f40"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the error callback.  <br /></td></tr>
<tr class="separator:gaff45816610d53f0b83656092a4034f40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6d6a983d38bd4e8fd786d7a9061d399e" id="r_ga6d6a983d38bd4e8fd786d7a9061d399e"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga6d6a983d38bd4e8fd786d7a9061d399e">glfwGetPlatform</a> (void)</td></tr>
<tr class="memdesc:ga6d6a983d38bd4e8fd786d7a9061d399e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the currently selected platform.  <br /></td></tr>
<tr class="separator:ga6d6a983d38bd4e8fd786d7a9061d399e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8785d2b6b36632368d803e78079d38ed" id="r_ga8785d2b6b36632368d803e78079d38ed"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__init.html#ga8785d2b6b36632368d803e78079d38ed">glfwPlatformSupported</a> (int platform)</td></tr>
<tr class="memdesc:ga8785d2b6b36632368d803e78079d38ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the library includes support for the specified platform.  <br /></td></tr>
<tr class="separator:ga8785d2b6b36632368d803e78079d38ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga70b1156d5d24e9928f145d6c864369d2" id="r_ga70b1156d5d24e9928f145d6c864369d2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> **&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga70b1156d5d24e9928f145d6c864369d2">glfwGetMonitors</a> (int *count)</td></tr>
<tr class="memdesc:ga70b1156d5d24e9928f145d6c864369d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the currently connected monitors.  <br /></td></tr>
<tr class="separator:ga70b1156d5d24e9928f145d6c864369d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac3adb24947eb709e1874028272e5dfc5" id="r_gac3adb24947eb709e1874028272e5dfc5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#gac3adb24947eb709e1874028272e5dfc5">glfwGetPrimaryMonitor</a> (void)</td></tr>
<tr class="memdesc:gac3adb24947eb709e1874028272e5dfc5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the primary monitor.  <br /></td></tr>
<tr class="separator:gac3adb24947eb709e1874028272e5dfc5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga102f54e7acc9149edbcf0997152df8c9" id="r_ga102f54e7acc9149edbcf0997152df8c9"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga102f54e7acc9149edbcf0997152df8c9">glfwGetMonitorPos</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, int *xpos, int *ypos)</td></tr>
<tr class="memdesc:ga102f54e7acc9149edbcf0997152df8c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the position of the monitor's viewport on the virtual screen.  <br /></td></tr>
<tr class="separator:ga102f54e7acc9149edbcf0997152df8c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0" id="r_ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0">glfwGetMonitorWorkarea</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, int *xpos, int *ypos, int *width, int *height)</td></tr>
<tr class="memdesc:ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the work area of the monitor.  <br /></td></tr>
<tr class="separator:ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7d8bffc6c55539286a6bd20d32a8d7ea" id="r_ga7d8bffc6c55539286a6bd20d32a8d7ea"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga7d8bffc6c55539286a6bd20d32a8d7ea">glfwGetMonitorPhysicalSize</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, int *widthMM, int *heightMM)</td></tr>
<tr class="memdesc:ga7d8bffc6c55539286a6bd20d32a8d7ea"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the physical size of the monitor.  <br /></td></tr>
<tr class="separator:ga7d8bffc6c55539286a6bd20d32a8d7ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad3152e84465fa620b601265ebfcdb21b" id="r_gad3152e84465fa620b601265ebfcdb21b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#gad3152e84465fa620b601265ebfcdb21b">glfwGetMonitorContentScale</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, float *xscale, float *yscale)</td></tr>
<tr class="memdesc:gad3152e84465fa620b601265ebfcdb21b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the content scale for the specified monitor.  <br /></td></tr>
<tr class="separator:gad3152e84465fa620b601265ebfcdb21b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7af83e13489d90379588fb331b9e4b68" id="r_ga7af83e13489d90379588fb331b9e4b68"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga7af83e13489d90379588fb331b9e4b68">glfwGetMonitorName</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:ga7af83e13489d90379588fb331b9e4b68"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the name of the specified monitor.  <br /></td></tr>
<tr class="separator:ga7af83e13489d90379588fb331b9e4b68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga702750e24313a686d3637297b6e85fda" id="r_ga702750e24313a686d3637297b6e85fda"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga702750e24313a686d3637297b6e85fda">glfwSetMonitorUserPointer</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, void *pointer)</td></tr>
<tr class="memdesc:ga702750e24313a686d3637297b6e85fda"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the user pointer of the specified monitor.  <br /></td></tr>
<tr class="separator:ga702750e24313a686d3637297b6e85fda"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1adbfbfb8cd58b23cfee82e574fbbdc5" id="r_ga1adbfbfb8cd58b23cfee82e574fbbdc5"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga1adbfbfb8cd58b23cfee82e574fbbdc5">glfwGetMonitorUserPointer</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:ga1adbfbfb8cd58b23cfee82e574fbbdc5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the user pointer of the specified monitor.  <br /></td></tr>
<tr class="separator:ga1adbfbfb8cd58b23cfee82e574fbbdc5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab39df645587c8518192aa746c2fb06c3" id="r_gab39df645587c8518192aa746c2fb06c3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__monitor.html#gaabe16caca8dea952504dfdebdf4cd249">GLFWmonitorfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#gab39df645587c8518192aa746c2fb06c3">glfwSetMonitorCallback</a> (<a class="el" href="group__monitor.html#gaabe16caca8dea952504dfdebdf4cd249">GLFWmonitorfun</a> callback)</td></tr>
<tr class="memdesc:gab39df645587c8518192aa746c2fb06c3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the monitor configuration callback.  <br /></td></tr>
<tr class="separator:gab39df645587c8518192aa746c2fb06c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad2e24d2843cb7d6c26202cddd530fc1b" id="r_gad2e24d2843cb7d6c26202cddd530fc1b"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#gad2e24d2843cb7d6c26202cddd530fc1b">glfwGetVideoModes</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, int *count)</td></tr>
<tr class="memdesc:gad2e24d2843cb7d6c26202cddd530fc1b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the available video modes for the specified monitor.  <br /></td></tr>
<tr class="separator:gad2e24d2843cb7d6c26202cddd530fc1b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaba376fa7e76634b4788bddc505d6c9d5" id="r_gaba376fa7e76634b4788bddc505d6c9d5"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:gaba376fa7e76634b4788bddc505d6c9d5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the current mode of the specified monitor.  <br /></td></tr>
<tr class="separator:gaba376fa7e76634b4788bddc505d6c9d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6ac582625c990220785ddd34efa3169a" id="r_ga6ac582625c990220785ddd34efa3169a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga6ac582625c990220785ddd34efa3169a">glfwSetGamma</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, float gamma)</td></tr>
<tr class="memdesc:ga6ac582625c990220785ddd34efa3169a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generates a gamma ramp and sets it for the specified monitor.  <br /></td></tr>
<tr class="separator:ga6ac582625c990220785ddd34efa3169a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga76ba90debcf0062b5c4b73052b24f96f" id="r_ga76ba90debcf0062b5c4b73052b24f96f"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga76ba90debcf0062b5c4b73052b24f96f">glfwGetGammaRamp</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:ga76ba90debcf0062b5c4b73052b24f96f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the current gamma ramp for the specified monitor.  <br /></td></tr>
<tr class="separator:ga76ba90debcf0062b5c4b73052b24f96f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga583f0ffd0d29613d8cd172b996bbf0dd" id="r_ga583f0ffd0d29613d8cd172b996bbf0dd"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga583f0ffd0d29613d8cd172b996bbf0dd">glfwSetGammaRamp</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, const <a class="el" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a> *ramp)</td></tr>
<tr class="memdesc:ga583f0ffd0d29613d8cd172b996bbf0dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the current gamma ramp for the specified monitor.  <br /></td></tr>
<tr class="separator:ga583f0ffd0d29613d8cd172b996bbf0dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa77c4898dfb83344a6b4f76aa16b9a4a" id="r_gaa77c4898dfb83344a6b4f76aa16b9a4a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaa77c4898dfb83344a6b4f76aa16b9a4a">glfwDefaultWindowHints</a> (void)</td></tr>
<tr class="memdesc:gaa77c4898dfb83344a6b4f76aa16b9a4a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Resets all window hints to their default values.  <br /></td></tr>
<tr class="separator:gaa77c4898dfb83344a6b4f76aa16b9a4a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7d9c8c62384b1e2821c4dc48952d2033" id="r_ga7d9c8c62384b1e2821c4dc48952d2033"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a> (int hint, int value)</td></tr>
<tr class="memdesc:ga7d9c8c62384b1e2821c4dc48952d2033"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the specified window hint to the desired value.  <br /></td></tr>
<tr class="separator:ga7d9c8c62384b1e2821c4dc48952d2033"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8cb2782861c9d997bcf2dea97f363e5f" id="r_ga8cb2782861c9d997bcf2dea97f363e5f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga8cb2782861c9d997bcf2dea97f363e5f">glfwWindowHintString</a> (int hint, const char *value)</td></tr>
<tr class="memdesc:ga8cb2782861c9d997bcf2dea97f363e5f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the specified window hint to the desired value.  <br /></td></tr>
<tr class="separator:ga8cb2782861c9d997bcf2dea97f363e5f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3555a418df92ad53f917597fe2f64aeb" id="r_ga3555a418df92ad53f917597fe2f64aeb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a> (int width, int height, const char *title, <a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, <a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *share)</td></tr>
<tr class="memdesc:ga3555a418df92ad53f917597fe2f64aeb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a window and its associated context.  <br /></td></tr>
<tr class="separator:ga3555a418df92ad53f917597fe2f64aeb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacdf43e51376051d2c091662e9fe3d7b2" id="r_gacdf43e51376051d2c091662e9fe3d7b2"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:gacdf43e51376051d2c091662e9fe3d7b2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Destroys the specified window and its context.  <br /></td></tr>
<tr class="separator:gacdf43e51376051d2c091662e9fe3d7b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga24e02fbfefbb81fc45320989f8140ab5" id="r_ga24e02fbfefbb81fc45320989f8140ab5"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga24e02fbfefbb81fc45320989f8140ab5">glfwWindowShouldClose</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga24e02fbfefbb81fc45320989f8140ab5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks the close flag of the specified window.  <br /></td></tr>
<tr class="separator:ga24e02fbfefbb81fc45320989f8140ab5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga49c449dde2a6f87d996f4daaa09d6708" id="r_ga49c449dde2a6f87d996f4daaa09d6708"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga49c449dde2a6f87d996f4daaa09d6708">glfwSetWindowShouldClose</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int value)</td></tr>
<tr class="memdesc:ga49c449dde2a6f87d996f4daaa09d6708"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the close flag of the specified window.  <br /></td></tr>
<tr class="separator:ga49c449dde2a6f87d996f4daaa09d6708"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac6151765c54b789c4fe66c6bc6215953" id="r_gac6151765c54b789c4fe66c6bc6215953"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gac6151765c54b789c4fe66c6bc6215953">glfwGetWindowTitle</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:gac6151765c54b789c4fe66c6bc6215953"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the title of the specified window.  <br /></td></tr>
<tr class="separator:gac6151765c54b789c4fe66c6bc6215953"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5d877f09e968cef7a360b513306f17ff" id="r_ga5d877f09e968cef7a360b513306f17ff"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga5d877f09e968cef7a360b513306f17ff">glfwSetWindowTitle</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, const char *title)</td></tr>
<tr class="memdesc:ga5d877f09e968cef7a360b513306f17ff"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the title of the specified window.  <br /></td></tr>
<tr class="separator:ga5d877f09e968cef7a360b513306f17ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadd7ccd39fe7a7d1f0904666ae5932dc5" id="r_gadd7ccd39fe7a7d1f0904666ae5932dc5"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gadd7ccd39fe7a7d1f0904666ae5932dc5">glfwSetWindowIcon</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int count, const <a class="el" href="struct_g_l_f_wimage.html">GLFWimage</a> *images)</td></tr>
<tr class="memdesc:gadd7ccd39fe7a7d1f0904666ae5932dc5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the icon for the specified window.  <br /></td></tr>
<tr class="separator:gadd7ccd39fe7a7d1f0904666ae5932dc5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga73cb526c000876fd8ddf571570fdb634" id="r_ga73cb526c000876fd8ddf571570fdb634"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga73cb526c000876fd8ddf571570fdb634">glfwGetWindowPos</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int *xpos, int *ypos)</td></tr>
<tr class="memdesc:ga73cb526c000876fd8ddf571570fdb634"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the position of the content area of the specified window.  <br /></td></tr>
<tr class="separator:ga73cb526c000876fd8ddf571570fdb634"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1abb6d690e8c88e0c8cd1751356dbca8" id="r_ga1abb6d690e8c88e0c8cd1751356dbca8"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga1abb6d690e8c88e0c8cd1751356dbca8">glfwSetWindowPos</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int xpos, int ypos)</td></tr>
<tr class="memdesc:ga1abb6d690e8c88e0c8cd1751356dbca8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the position of the content area of the specified window.  <br /></td></tr>
<tr class="separator:ga1abb6d690e8c88e0c8cd1751356dbca8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeea7cbc03373a41fb51cfbf9f2a5d4c6" id="r_gaeea7cbc03373a41fb51cfbf9f2a5d4c6"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaeea7cbc03373a41fb51cfbf9f2a5d4c6">glfwGetWindowSize</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int *width, int *height)</td></tr>
<tr class="memdesc:gaeea7cbc03373a41fb51cfbf9f2a5d4c6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the size of the content area of the specified window.  <br /></td></tr>
<tr class="separator:gaeea7cbc03373a41fb51cfbf9f2a5d4c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac314fa6cec7d2d307be9963e2709cc90" id="r_gac314fa6cec7d2d307be9963e2709cc90"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gac314fa6cec7d2d307be9963e2709cc90">glfwSetWindowSizeLimits</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int minwidth, int minheight, int maxwidth, int maxheight)</td></tr>
<tr class="memdesc:gac314fa6cec7d2d307be9963e2709cc90"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the size limits of the specified window.  <br /></td></tr>
<tr class="separator:gac314fa6cec7d2d307be9963e2709cc90"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga72ac8cb1ee2e312a878b55153d81b937" id="r_ga72ac8cb1ee2e312a878b55153d81b937"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga72ac8cb1ee2e312a878b55153d81b937">glfwSetWindowAspectRatio</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int numer, int denom)</td></tr>
<tr class="memdesc:ga72ac8cb1ee2e312a878b55153d81b937"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the aspect ratio of the specified window.  <br /></td></tr>
<tr class="separator:ga72ac8cb1ee2e312a878b55153d81b937"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga371911f12c74c504dd8d47d832d095cb" id="r_ga371911f12c74c504dd8d47d832d095cb"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga371911f12c74c504dd8d47d832d095cb">glfwSetWindowSize</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int width, int height)</td></tr>
<tr class="memdesc:ga371911f12c74c504dd8d47d832d095cb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the size of the content area of the specified window.  <br /></td></tr>
<tr class="separator:ga371911f12c74c504dd8d47d832d095cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0e2637a4161afb283f5300c7f94785c9" id="r_ga0e2637a4161afb283f5300c7f94785c9"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga0e2637a4161afb283f5300c7f94785c9">glfwGetFramebufferSize</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int *width, int *height)</td></tr>
<tr class="memdesc:ga0e2637a4161afb283f5300c7f94785c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the size of the framebuffer of the specified window.  <br /></td></tr>
<tr class="separator:ga0e2637a4161afb283f5300c7f94785c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1a9fd382058c53101b21cf211898f1f1" id="r_ga1a9fd382058c53101b21cf211898f1f1"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga1a9fd382058c53101b21cf211898f1f1">glfwGetWindowFrameSize</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int *left, int *top, int *right, int *bottom)</td></tr>
<tr class="memdesc:ga1a9fd382058c53101b21cf211898f1f1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the size of the frame of the window.  <br /></td></tr>
<tr class="separator:ga1a9fd382058c53101b21cf211898f1f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf5d31de9c19c4f994facea64d2b3106c" id="r_gaf5d31de9c19c4f994facea64d2b3106c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaf5d31de9c19c4f994facea64d2b3106c">glfwGetWindowContentScale</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, float *xscale, float *yscale)</td></tr>
<tr class="memdesc:gaf5d31de9c19c4f994facea64d2b3106c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the content scale for the specified window.  <br /></td></tr>
<tr class="separator:gaf5d31de9c19c4f994facea64d2b3106c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad09f0bd7a6307c4533b7061828480a84" id="r_gad09f0bd7a6307c4533b7061828480a84"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gad09f0bd7a6307c4533b7061828480a84">glfwGetWindowOpacity</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:gad09f0bd7a6307c4533b7061828480a84"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the opacity of the whole window.  <br /></td></tr>
<tr class="separator:gad09f0bd7a6307c4533b7061828480a84"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac31caeb3d1088831b13d2c8a156802e9" id="r_gac31caeb3d1088831b13d2c8a156802e9"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gac31caeb3d1088831b13d2c8a156802e9">glfwSetWindowOpacity</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, float opacity)</td></tr>
<tr class="memdesc:gac31caeb3d1088831b13d2c8a156802e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the opacity of the whole window.  <br /></td></tr>
<tr class="separator:gac31caeb3d1088831b13d2c8a156802e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1bb559c0ebaad63c5c05ad2a066779c4" id="r_ga1bb559c0ebaad63c5c05ad2a066779c4"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga1bb559c0ebaad63c5c05ad2a066779c4">glfwIconifyWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga1bb559c0ebaad63c5c05ad2a066779c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Iconifies the specified window.  <br /></td></tr>
<tr class="separator:ga1bb559c0ebaad63c5c05ad2a066779c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga52527a5904b47d802b6b4bb519cdebc7" id="r_ga52527a5904b47d802b6b4bb519cdebc7"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga52527a5904b47d802b6b4bb519cdebc7">glfwRestoreWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga52527a5904b47d802b6b4bb519cdebc7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Restores the specified window.  <br /></td></tr>
<tr class="separator:ga52527a5904b47d802b6b4bb519cdebc7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3f541387449d911274324ae7f17ec56b" id="r_ga3f541387449d911274324ae7f17ec56b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga3f541387449d911274324ae7f17ec56b">glfwMaximizeWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga3f541387449d911274324ae7f17ec56b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximizes the specified window.  <br /></td></tr>
<tr class="separator:ga3f541387449d911274324ae7f17ec56b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga61be47917b72536a148300f46494fc66" id="r_ga61be47917b72536a148300f46494fc66"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga61be47917b72536a148300f46494fc66">glfwShowWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga61be47917b72536a148300f46494fc66"><td class="mdescLeft">&#160;</td><td class="mdescRight">Makes the specified window visible.  <br /></td></tr>
<tr class="separator:ga61be47917b72536a148300f46494fc66"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga49401f82a1ba5f15db5590728314d47c" id="r_ga49401f82a1ba5f15db5590728314d47c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga49401f82a1ba5f15db5590728314d47c">glfwHideWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga49401f82a1ba5f15db5590728314d47c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Hides the specified window.  <br /></td></tr>
<tr class="separator:ga49401f82a1ba5f15db5590728314d47c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga873780357abd3f3a081d71a40aae45a1" id="r_ga873780357abd3f3a081d71a40aae45a1"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga873780357abd3f3a081d71a40aae45a1">glfwFocusWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga873780357abd3f3a081d71a40aae45a1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Brings the specified window to front and sets input focus.  <br /></td></tr>
<tr class="separator:ga873780357abd3f3a081d71a40aae45a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2f8d59323fc4692c1d54ba08c863a703" id="r_ga2f8d59323fc4692c1d54ba08c863a703"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga2f8d59323fc4692c1d54ba08c863a703">glfwRequestWindowAttention</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga2f8d59323fc4692c1d54ba08c863a703"><td class="mdescLeft">&#160;</td><td class="mdescRight">Requests user attention to the specified window.  <br /></td></tr>
<tr class="separator:ga2f8d59323fc4692c1d54ba08c863a703"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4d766499ac02c60f02221a9dfab87299" id="r_ga4d766499ac02c60f02221a9dfab87299"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga4d766499ac02c60f02221a9dfab87299">glfwGetWindowMonitor</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga4d766499ac02c60f02221a9dfab87299"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the monitor that the window uses for full screen mode.  <br /></td></tr>
<tr class="separator:ga4d766499ac02c60f02221a9dfab87299"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga81c76c418af80a1cce7055bccb0ae0a7" id="r_ga81c76c418af80a1cce7055bccb0ae0a7"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga81c76c418af80a1cce7055bccb0ae0a7">glfwSetWindowMonitor</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, int xpos, int ypos, int width, int height, int refreshRate)</td></tr>
<tr class="memdesc:ga81c76c418af80a1cce7055bccb0ae0a7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the mode, monitor, video mode and placement of a window.  <br /></td></tr>
<tr class="separator:ga81c76c418af80a1cce7055bccb0ae0a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacccb29947ea4b16860ebef42c2cb9337" id="r_gacccb29947ea4b16860ebef42c2cb9337"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int attrib)</td></tr>
<tr class="memdesc:gacccb29947ea4b16860ebef42c2cb9337"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an attribute of the specified window.  <br /></td></tr>
<tr class="separator:gacccb29947ea4b16860ebef42c2cb9337"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gace2afda29b4116ec012e410a6819033e" id="r_gace2afda29b4116ec012e410a6819033e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gace2afda29b4116ec012e410a6819033e">glfwSetWindowAttrib</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int attrib, int value)</td></tr>
<tr class="memdesc:gace2afda29b4116ec012e410a6819033e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets an attribute of the specified window.  <br /></td></tr>
<tr class="separator:gace2afda29b4116ec012e410a6819033e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3d2fc6026e690ab31a13f78bc9fd3651" id="r_ga3d2fc6026e690ab31a13f78bc9fd3651"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga3d2fc6026e690ab31a13f78bc9fd3651">glfwSetWindowUserPointer</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, void *pointer)</td></tr>
<tr class="memdesc:ga3d2fc6026e690ab31a13f78bc9fd3651"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the user pointer of the specified window.  <br /></td></tr>
<tr class="separator:ga3d2fc6026e690ab31a13f78bc9fd3651"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae77a4add0d2023ca21ff1443ced01653" id="r_gae77a4add0d2023ca21ff1443ced01653"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gae77a4add0d2023ca21ff1443ced01653">glfwGetWindowUserPointer</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:gae77a4add0d2023ca21ff1443ced01653"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the user pointer of the specified window.  <br /></td></tr>
<tr class="separator:gae77a4add0d2023ca21ff1443ced01653"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga08bdfbba88934f9c4f92fd757979ac74" id="r_ga08bdfbba88934f9c4f92fd757979ac74"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#gabe287973a21a8f927cde4db06b8dcbe9">GLFWwindowposfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga08bdfbba88934f9c4f92fd757979ac74">glfwSetWindowPosCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#gabe287973a21a8f927cde4db06b8dcbe9">GLFWwindowposfun</a> callback)</td></tr>
<tr class="memdesc:ga08bdfbba88934f9c4f92fd757979ac74"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the position callback for the specified window.  <br /></td></tr>
<tr class="separator:ga08bdfbba88934f9c4f92fd757979ac74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad91b8b047a0c4c6033c38853864c34f8" id="r_gad91b8b047a0c4c6033c38853864c34f8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#gaec0282944bb810f6f3163ec02da90350">GLFWwindowsizefun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gad91b8b047a0c4c6033c38853864c34f8">glfwSetWindowSizeCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#gaec0282944bb810f6f3163ec02da90350">GLFWwindowsizefun</a> callback)</td></tr>
<tr class="memdesc:gad91b8b047a0c4c6033c38853864c34f8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the size callback for the specified window.  <br /></td></tr>
<tr class="separator:gad91b8b047a0c4c6033c38853864c34f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gada646d775a7776a95ac000cfc1885331" id="r_gada646d775a7776a95ac000cfc1885331"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#gabf859b936d80961b7d39013a9694cc3e">GLFWwindowclosefun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gada646d775a7776a95ac000cfc1885331">glfwSetWindowCloseCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#gabf859b936d80961b7d39013a9694cc3e">GLFWwindowclosefun</a> callback)</td></tr>
<tr class="memdesc:gada646d775a7776a95ac000cfc1885331"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the close callback for the specified window.  <br /></td></tr>
<tr class="separator:gada646d775a7776a95ac000cfc1885331"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1c5c7eb889c33c7f4d10dd35b327654e" id="r_ga1c5c7eb889c33c7f4d10dd35b327654e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#ga431663a1427d2eb3a273bc398b6737b5">GLFWwindowrefreshfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga1c5c7eb889c33c7f4d10dd35b327654e">glfwSetWindowRefreshCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#ga431663a1427d2eb3a273bc398b6737b5">GLFWwindowrefreshfun</a> callback)</td></tr>
<tr class="memdesc:ga1c5c7eb889c33c7f4d10dd35b327654e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the refresh callback for the specified window.  <br /></td></tr>
<tr class="separator:ga1c5c7eb889c33c7f4d10dd35b327654e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac2d83c4a10f071baf841f6730528e66c" id="r_gac2d83c4a10f071baf841f6730528e66c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#gabc58c47e9d93f6eb1862d615c3680f46">GLFWwindowfocusfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gac2d83c4a10f071baf841f6730528e66c">glfwSetWindowFocusCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#gabc58c47e9d93f6eb1862d615c3680f46">GLFWwindowfocusfun</a> callback)</td></tr>
<tr class="memdesc:gac2d83c4a10f071baf841f6730528e66c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the focus callback for the specified window.  <br /></td></tr>
<tr class="separator:gac2d83c4a10f071baf841f6730528e66c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac793e9efd255567b5fb8b445052cfd3e" id="r_gac793e9efd255567b5fb8b445052cfd3e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#ga35c658cccba236f26e7adee0e25f6a4f">GLFWwindowiconifyfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gac793e9efd255567b5fb8b445052cfd3e">glfwSetWindowIconifyCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#ga35c658cccba236f26e7adee0e25f6a4f">GLFWwindowiconifyfun</a> callback)</td></tr>
<tr class="memdesc:gac793e9efd255567b5fb8b445052cfd3e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the iconify callback for the specified window.  <br /></td></tr>
<tr class="separator:gac793e9efd255567b5fb8b445052cfd3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacbe64c339fbd94885e62145563b6dc93" id="r_gacbe64c339fbd94885e62145563b6dc93"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#ga3017196fdaec33ac3e095765176c2a90">GLFWwindowmaximizefun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gacbe64c339fbd94885e62145563b6dc93">glfwSetWindowMaximizeCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#ga3017196fdaec33ac3e095765176c2a90">GLFWwindowmaximizefun</a> callback)</td></tr>
<tr class="memdesc:gacbe64c339fbd94885e62145563b6dc93"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the maximize callback for the specified window.  <br /></td></tr>
<tr class="separator:gacbe64c339fbd94885e62145563b6dc93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab3fb7c3366577daef18c0023e2a8591f" id="r_gab3fb7c3366577daef18c0023e2a8591f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#gae18026e294dde685ed2e5f759533144d">GLFWframebuffersizefun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gab3fb7c3366577daef18c0023e2a8591f">glfwSetFramebufferSizeCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#gae18026e294dde685ed2e5f759533144d">GLFWframebuffersizefun</a> callback)</td></tr>
<tr class="memdesc:gab3fb7c3366577daef18c0023e2a8591f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the framebuffer resize callback for the specified window.  <br /></td></tr>
<tr class="separator:gab3fb7c3366577daef18c0023e2a8591f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf2832ebb5aa6c252a2d261de002c92d6" id="r_gaf2832ebb5aa6c252a2d261de002c92d6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#ga77f288a2d04bb3c77c7d9615d08cf70e">GLFWwindowcontentscalefun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaf2832ebb5aa6c252a2d261de002c92d6">glfwSetWindowContentScaleCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#ga77f288a2d04bb3c77c7d9615d08cf70e">GLFWwindowcontentscalefun</a> callback)</td></tr>
<tr class="memdesc:gaf2832ebb5aa6c252a2d261de002c92d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the window content scale callback for the specified window.  <br /></td></tr>
<tr class="separator:gaf2832ebb5aa6c252a2d261de002c92d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga37bd57223967b4211d60ca1a0bf3c832" id="r_ga37bd57223967b4211d60ca1a0bf3c832"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a> (void)</td></tr>
<tr class="memdesc:ga37bd57223967b4211d60ca1a0bf3c832"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processes all pending events.  <br /></td></tr>
<tr class="separator:ga37bd57223967b4211d60ca1a0bf3c832"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga554e37d781f0a997656c26b2c56c835e" id="r_ga554e37d781f0a997656c26b2c56c835e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga554e37d781f0a997656c26b2c56c835e">glfwWaitEvents</a> (void)</td></tr>
<tr class="memdesc:ga554e37d781f0a997656c26b2c56c835e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Waits until events are queued and processes them.  <br /></td></tr>
<tr class="separator:ga554e37d781f0a997656c26b2c56c835e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga605a178db92f1a7f1a925563ef3ea2cf" id="r_ga605a178db92f1a7f1a925563ef3ea2cf"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga605a178db92f1a7f1a925563ef3ea2cf">glfwWaitEventsTimeout</a> (double timeout)</td></tr>
<tr class="memdesc:ga605a178db92f1a7f1a925563ef3ea2cf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Waits with timeout until events are queued and processes them.  <br /></td></tr>
<tr class="separator:ga605a178db92f1a7f1a925563ef3ea2cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab5997a25187e9fd5c6f2ecbbc8dfd7e9" id="r_gab5997a25187e9fd5c6f2ecbbc8dfd7e9"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gab5997a25187e9fd5c6f2ecbbc8dfd7e9">glfwPostEmptyEvent</a> (void)</td></tr>
<tr class="memdesc:gab5997a25187e9fd5c6f2ecbbc8dfd7e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Posts an empty event to the event queue.  <br /></td></tr>
<tr class="separator:gab5997a25187e9fd5c6f2ecbbc8dfd7e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf5b859dbe19bdf434e42695ea45cc5f4" id="r_gaf5b859dbe19bdf434e42695ea45cc5f4"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaf5b859dbe19bdf434e42695ea45cc5f4">glfwGetInputMode</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int mode)</td></tr>
<tr class="memdesc:gaf5b859dbe19bdf434e42695ea45cc5f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the value of an input option for the specified window.  <br /></td></tr>
<tr class="separator:gaf5b859dbe19bdf434e42695ea45cc5f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa92336e173da9c8834558b54ee80563b" id="r_gaa92336e173da9c8834558b54ee80563b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int mode, int value)</td></tr>
<tr class="memdesc:gaa92336e173da9c8834558b54ee80563b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets an input option for the specified window.  <br /></td></tr>
<tr class="separator:gaa92336e173da9c8834558b54ee80563b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae4ee0dbd0d256183e1ea4026d897e1c2" id="r_gae4ee0dbd0d256183e1ea4026d897e1c2"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gae4ee0dbd0d256183e1ea4026d897e1c2">glfwRawMouseMotionSupported</a> (void)</td></tr>
<tr class="memdesc:gae4ee0dbd0d256183e1ea4026d897e1c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether raw mouse motion is supported.  <br /></td></tr>
<tr class="separator:gae4ee0dbd0d256183e1ea4026d897e1c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeaed62e69c3bd62b7ff8f7b19913ce4f" id="r_gaeaed62e69c3bd62b7ff8f7b19913ce4f"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaeaed62e69c3bd62b7ff8f7b19913ce4f">glfwGetKeyName</a> (int key, int scancode)</td></tr>
<tr class="memdesc:gaeaed62e69c3bd62b7ff8f7b19913ce4f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the layout-specific name of the specified printable key.  <br /></td></tr>
<tr class="separator:gaeaed62e69c3bd62b7ff8f7b19913ce4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga67ddd1b7dcbbaff03e4a76c0ea67103a" id="r_ga67ddd1b7dcbbaff03e4a76c0ea67103a"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga67ddd1b7dcbbaff03e4a76c0ea67103a">glfwGetKeyScancode</a> (int key)</td></tr>
<tr class="memdesc:ga67ddd1b7dcbbaff03e4a76c0ea67103a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the platform-specific scancode of the specified key.  <br /></td></tr>
<tr class="separator:ga67ddd1b7dcbbaff03e4a76c0ea67103a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadd341da06bc8d418b4dc3a3518af9ad2" id="r_gadd341da06bc8d418b4dc3a3518af9ad2"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gadd341da06bc8d418b4dc3a3518af9ad2">glfwGetKey</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int key)</td></tr>
<tr class="memdesc:gadd341da06bc8d418b4dc3a3518af9ad2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the last reported state of a keyboard key for the specified window.  <br /></td></tr>
<tr class="separator:gadd341da06bc8d418b4dc3a3518af9ad2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac1473feacb5996c01a7a5a33b5066704" id="r_gac1473feacb5996c01a7a5a33b5066704"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gac1473feacb5996c01a7a5a33b5066704">glfwGetMouseButton</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int button)</td></tr>
<tr class="memdesc:gac1473feacb5996c01a7a5a33b5066704"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the last reported state of a mouse button for the specified window.  <br /></td></tr>
<tr class="separator:gac1473feacb5996c01a7a5a33b5066704"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga01d37b6c40133676b9cea60ca1d7c0cc" id="r_ga01d37b6c40133676b9cea60ca1d7c0cc"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga01d37b6c40133676b9cea60ca1d7c0cc">glfwGetCursorPos</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, double *xpos, double *ypos)</td></tr>
<tr class="memdesc:ga01d37b6c40133676b9cea60ca1d7c0cc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the position of the cursor relative to the content area of the window.  <br /></td></tr>
<tr class="separator:ga01d37b6c40133676b9cea60ca1d7c0cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga04b03af936d906ca123c8f4ee08b39e7" id="r_ga04b03af936d906ca123c8f4ee08b39e7"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga04b03af936d906ca123c8f4ee08b39e7">glfwSetCursorPos</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, double xpos, double ypos)</td></tr>
<tr class="memdesc:ga04b03af936d906ca123c8f4ee08b39e7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the position of the cursor, relative to the content area of the window.  <br /></td></tr>
<tr class="separator:ga04b03af936d906ca123c8f4ee08b39e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga556f604f73af156c0db0e97c081373c3" id="r_ga556f604f73af156c0db0e97c081373c3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga556f604f73af156c0db0e97c081373c3">glfwCreateCursor</a> (const <a class="el" href="struct_g_l_f_wimage.html">GLFWimage</a> *image, int xhot, int yhot)</td></tr>
<tr class="memdesc:ga556f604f73af156c0db0e97c081373c3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a custom cursor.  <br /></td></tr>
<tr class="separator:ga556f604f73af156c0db0e97c081373c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf2fb2eb2c9dd842d1cef8a34e3c6403e" id="r_gaf2fb2eb2c9dd842d1cef8a34e3c6403e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaf2fb2eb2c9dd842d1cef8a34e3c6403e">glfwCreateStandardCursor</a> (int shape)</td></tr>
<tr class="memdesc:gaf2fb2eb2c9dd842d1cef8a34e3c6403e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a cursor with a standard shape.  <br /></td></tr>
<tr class="separator:gaf2fb2eb2c9dd842d1cef8a34e3c6403e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga81b952cd1764274d0db7fb3c5a79ba6a" id="r_ga81b952cd1764274d0db7fb3c5a79ba6a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga81b952cd1764274d0db7fb3c5a79ba6a">glfwDestroyCursor</a> (<a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a> *cursor)</td></tr>
<tr class="memdesc:ga81b952cd1764274d0db7fb3c5a79ba6a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Destroys a cursor.  <br /></td></tr>
<tr class="separator:ga81b952cd1764274d0db7fb3c5a79ba6a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad3b4f38c8d5dae036bc8fa959e18343e" id="r_gad3b4f38c8d5dae036bc8fa959e18343e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gad3b4f38c8d5dae036bc8fa959e18343e">glfwSetCursor</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a> *cursor)</td></tr>
<tr class="memdesc:gad3b4f38c8d5dae036bc8fa959e18343e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the cursor for the window.  <br /></td></tr>
<tr class="separator:gad3b4f38c8d5dae036bc8fa959e18343e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1caf18159767e761185e49a3be019f8d" id="r_ga1caf18159767e761185e49a3be019f8d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#ga5bd751b27b90f865d2ea613533f0453c">GLFWkeyfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga1caf18159767e761185e49a3be019f8d">glfwSetKeyCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#ga5bd751b27b90f865d2ea613533f0453c">GLFWkeyfun</a> callback)</td></tr>
<tr class="memdesc:ga1caf18159767e761185e49a3be019f8d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the key callback.  <br /></td></tr>
<tr class="separator:ga1caf18159767e761185e49a3be019f8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab25c4a220fd8f5717718dbc487828996" id="r_gab25c4a220fd8f5717718dbc487828996"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#ga1ab90a55cf3f58639b893c0f4118cb6e">GLFWcharfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gab25c4a220fd8f5717718dbc487828996">glfwSetCharCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#ga1ab90a55cf3f58639b893c0f4118cb6e">GLFWcharfun</a> callback)</td></tr>
<tr class="memdesc:gab25c4a220fd8f5717718dbc487828996"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the Unicode character callback.  <br /></td></tr>
<tr class="separator:gab25c4a220fd8f5717718dbc487828996"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0b7f4ad13c2b17435ff13b6dcfb4e43c" id="r_ga0b7f4ad13c2b17435ff13b6dcfb4e43c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#gac3cf64f90b6219c05ac7b7822d5a4b8f">GLFWcharmodsfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga0b7f4ad13c2b17435ff13b6dcfb4e43c">glfwSetCharModsCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#gac3cf64f90b6219c05ac7b7822d5a4b8f">GLFWcharmodsfun</a> callback)</td></tr>
<tr class="memdesc:ga0b7f4ad13c2b17435ff13b6dcfb4e43c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the Unicode character with modifiers callback.  <br /></td></tr>
<tr class="separator:ga0b7f4ad13c2b17435ff13b6dcfb4e43c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6ab84420974d812bee700e45284a723c" id="r_ga6ab84420974d812bee700e45284a723c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#ga0184dcb59f6d85d735503dcaae809727">GLFWmousebuttonfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga6ab84420974d812bee700e45284a723c">glfwSetMouseButtonCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#ga0184dcb59f6d85d735503dcaae809727">GLFWmousebuttonfun</a> callback)</td></tr>
<tr class="memdesc:ga6ab84420974d812bee700e45284a723c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the mouse button callback.  <br /></td></tr>
<tr class="separator:ga6ab84420974d812bee700e45284a723c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac1f879ab7435d54d4d79bb469fe225d7" id="r_gac1f879ab7435d54d4d79bb469fe225d7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#gad6fae41b3ac2e4209aaa87b596c57f68">GLFWcursorposfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gac1f879ab7435d54d4d79bb469fe225d7">glfwSetCursorPosCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#gad6fae41b3ac2e4209aaa87b596c57f68">GLFWcursorposfun</a> callback)</td></tr>
<tr class="memdesc:gac1f879ab7435d54d4d79bb469fe225d7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the cursor position callback.  <br /></td></tr>
<tr class="separator:gac1f879ab7435d54d4d79bb469fe225d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad27f8ad0142c038a281466c0966817d8" id="r_gad27f8ad0142c038a281466c0966817d8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#gaa93dc4818ac9ab32532909d53a337cbe">GLFWcursorenterfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gad27f8ad0142c038a281466c0966817d8">glfwSetCursorEnterCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#gaa93dc4818ac9ab32532909d53a337cbe">GLFWcursorenterfun</a> callback)</td></tr>
<tr class="memdesc:gad27f8ad0142c038a281466c0966817d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the cursor enter/leave callback.  <br /></td></tr>
<tr class="separator:gad27f8ad0142c038a281466c0966817d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga571e45a030ae4061f746ed56cb76aede" id="r_ga571e45a030ae4061f746ed56cb76aede"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#gaf656112c33de3efdb227fa58f0134cf5">GLFWscrollfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga571e45a030ae4061f746ed56cb76aede">glfwSetScrollCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#gaf656112c33de3efdb227fa58f0134cf5">GLFWscrollfun</a> callback)</td></tr>
<tr class="memdesc:ga571e45a030ae4061f746ed56cb76aede"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the scroll callback.  <br /></td></tr>
<tr class="separator:ga571e45a030ae4061f746ed56cb76aede"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab773f0ee0a07cff77a210cea40bc1f6b" id="r_gab773f0ee0a07cff77a210cea40bc1f6b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#gaaba73c3274062c18723b7f05862d94b2">GLFWdropfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gab773f0ee0a07cff77a210cea40bc1f6b">glfwSetDropCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#gaaba73c3274062c18723b7f05862d94b2">GLFWdropfun</a> callback)</td></tr>
<tr class="memdesc:gab773f0ee0a07cff77a210cea40bc1f6b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the path drop callback.  <br /></td></tr>
<tr class="separator:gab773f0ee0a07cff77a210cea40bc1f6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaed0966cee139d815317f9ffcba64c9f1" id="r_gaed0966cee139d815317f9ffcba64c9f1"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a> (int jid)</td></tr>
<tr class="memdesc:gaed0966cee139d815317f9ffcba64c9f1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the specified joystick is present.  <br /></td></tr>
<tr class="separator:gaed0966cee139d815317f9ffcba64c9f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeb1c0191d3140a233a682987c61eb408" id="r_gaeb1c0191d3140a233a682987c61eb408"><td class="memItemLeft" align="right" valign="top">const float *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaeb1c0191d3140a233a682987c61eb408">glfwGetJoystickAxes</a> (int jid, int *count)</td></tr>
<tr class="memdesc:gaeb1c0191d3140a233a682987c61eb408"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the values of all axes of the specified joystick.  <br /></td></tr>
<tr class="separator:gaeb1c0191d3140a233a682987c61eb408"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5ffe34739d3dc97efe432ed2d81d9938" id="r_ga5ffe34739d3dc97efe432ed2d81d9938"><td class="memItemLeft" align="right" valign="top">const unsigned char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga5ffe34739d3dc97efe432ed2d81d9938">glfwGetJoystickButtons</a> (int jid, int *count)</td></tr>
<tr class="memdesc:ga5ffe34739d3dc97efe432ed2d81d9938"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the state of all buttons of the specified joystick.  <br /></td></tr>
<tr class="separator:ga5ffe34739d3dc97efe432ed2d81d9938"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga06e660841b3e79c54da4f54a932c5a2c" id="r_ga06e660841b3e79c54da4f54a932c5a2c"><td class="memItemLeft" align="right" valign="top">const unsigned char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga06e660841b3e79c54da4f54a932c5a2c">glfwGetJoystickHats</a> (int jid, int *count)</td></tr>
<tr class="memdesc:ga06e660841b3e79c54da4f54a932c5a2c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the state of all hats of the specified joystick.  <br /></td></tr>
<tr class="separator:ga06e660841b3e79c54da4f54a932c5a2c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac6a8e769e18e0bcfa9097793fc2c3978" id="r_gac6a8e769e18e0bcfa9097793fc2c3978"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gac6a8e769e18e0bcfa9097793fc2c3978">glfwGetJoystickName</a> (int jid)</td></tr>
<tr class="memdesc:gac6a8e769e18e0bcfa9097793fc2c3978"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the name of the specified joystick.  <br /></td></tr>
<tr class="separator:gac6a8e769e18e0bcfa9097793fc2c3978"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6659411aec3c7fcef27780e2cb2d9600" id="r_ga6659411aec3c7fcef27780e2cb2d9600"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga6659411aec3c7fcef27780e2cb2d9600">glfwGetJoystickGUID</a> (int jid)</td></tr>
<tr class="memdesc:ga6659411aec3c7fcef27780e2cb2d9600"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the SDL compatible GUID of the specified joystick.  <br /></td></tr>
<tr class="separator:ga6659411aec3c7fcef27780e2cb2d9600"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6b2f72d64d636b48a727b437cbb7489e" id="r_ga6b2f72d64d636b48a727b437cbb7489e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga6b2f72d64d636b48a727b437cbb7489e">glfwSetJoystickUserPointer</a> (int jid, void *pointer)</td></tr>
<tr class="memdesc:ga6b2f72d64d636b48a727b437cbb7489e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the user pointer of the specified joystick.  <br /></td></tr>
<tr class="separator:ga6b2f72d64d636b48a727b437cbb7489e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga18cefd7265d1fa04f3fd38a6746db5f3" id="r_ga18cefd7265d1fa04f3fd38a6746db5f3"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga18cefd7265d1fa04f3fd38a6746db5f3">glfwGetJoystickUserPointer</a> (int jid)</td></tr>
<tr class="memdesc:ga18cefd7265d1fa04f3fd38a6746db5f3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the user pointer of the specified joystick.  <br /></td></tr>
<tr class="separator:ga18cefd7265d1fa04f3fd38a6746db5f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad0f676860f329d80f7e47e9f06a96f00" id="r_gad0f676860f329d80f7e47e9f06a96f00"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gad0f676860f329d80f7e47e9f06a96f00">glfwJoystickIsGamepad</a> (int jid)</td></tr>
<tr class="memdesc:gad0f676860f329d80f7e47e9f06a96f00"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the specified joystick has a gamepad mapping.  <br /></td></tr>
<tr class="separator:gad0f676860f329d80f7e47e9f06a96f00"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c" id="r_ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#gaa21ad5986ae9a26077a40142efb56243">GLFWjoystickfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c">glfwSetJoystickCallback</a> (<a class="el" href="group__input.html#gaa21ad5986ae9a26077a40142efb56243">GLFWjoystickfun</a> callback)</td></tr>
<tr class="memdesc:ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the joystick configuration callback.  <br /></td></tr>
<tr class="separator:ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaed5104612f2fa8e66aa6e846652ad00f" id="r_gaed5104612f2fa8e66aa6e846652ad00f"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaed5104612f2fa8e66aa6e846652ad00f">glfwUpdateGamepadMappings</a> (const char *string)</td></tr>
<tr class="memdesc:gaed5104612f2fa8e66aa6e846652ad00f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds the specified SDL_GameControllerDB gamepad mappings.  <br /></td></tr>
<tr class="separator:gaed5104612f2fa8e66aa6e846652ad00f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8aea73a1a25cc6c0486a617019f56728" id="r_ga8aea73a1a25cc6c0486a617019f56728"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga8aea73a1a25cc6c0486a617019f56728">glfwGetGamepadName</a> (int jid)</td></tr>
<tr class="memdesc:ga8aea73a1a25cc6c0486a617019f56728"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the human-readable gamepad name for the specified joystick.  <br /></td></tr>
<tr class="separator:ga8aea73a1a25cc6c0486a617019f56728"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadccddea8bce6113fa459de379ddaf051" id="r_gadccddea8bce6113fa459de379ddaf051"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gadccddea8bce6113fa459de379ddaf051">glfwGetGamepadState</a> (int jid, <a class="el" href="struct_g_l_f_wgamepadstate.html">GLFWgamepadstate</a> *state)</td></tr>
<tr class="memdesc:gadccddea8bce6113fa459de379ddaf051"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the state of the specified joystick remapped as a gamepad.  <br /></td></tr>
<tr class="separator:gadccddea8bce6113fa459de379ddaf051"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaba1f022c5eb07dfac421df34cdcd31dd" id="r_gaba1f022c5eb07dfac421df34cdcd31dd"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaba1f022c5eb07dfac421df34cdcd31dd">glfwSetClipboardString</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, const char *string)</td></tr>
<tr class="memdesc:gaba1f022c5eb07dfac421df34cdcd31dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the clipboard to the specified string.  <br /></td></tr>
<tr class="separator:gaba1f022c5eb07dfac421df34cdcd31dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga71a5b20808ea92193d65c21b82580355" id="r_ga71a5b20808ea92193d65c21b82580355"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga71a5b20808ea92193d65c21b82580355">glfwGetClipboardString</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga71a5b20808ea92193d65c21b82580355"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the contents of the clipboard as a string.  <br /></td></tr>
<tr class="separator:ga71a5b20808ea92193d65c21b82580355"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa6cf4e7a77158a3b8fd00328b1720a4a" id="r_gaa6cf4e7a77158a3b8fd00328b1720a4a"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaa6cf4e7a77158a3b8fd00328b1720a4a">glfwGetTime</a> (void)</td></tr>
<tr class="memdesc:gaa6cf4e7a77158a3b8fd00328b1720a4a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the GLFW time.  <br /></td></tr>
<tr class="separator:gaa6cf4e7a77158a3b8fd00328b1720a4a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf59589ef6e8b8c8b5ad184b25afd4dc0" id="r_gaf59589ef6e8b8c8b5ad184b25afd4dc0"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaf59589ef6e8b8c8b5ad184b25afd4dc0">glfwSetTime</a> (double time)</td></tr>
<tr class="memdesc:gaf59589ef6e8b8c8b5ad184b25afd4dc0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the GLFW time.  <br /></td></tr>
<tr class="separator:gaf59589ef6e8b8c8b5ad184b25afd4dc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga09b2bd37d328e0b9456c7ec575cc26aa" id="r_ga09b2bd37d328e0b9456c7ec575cc26aa"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga09b2bd37d328e0b9456c7ec575cc26aa">glfwGetTimerValue</a> (void)</td></tr>
<tr class="memdesc:ga09b2bd37d328e0b9456c7ec575cc26aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the current value of the raw timer.  <br /></td></tr>
<tr class="separator:ga09b2bd37d328e0b9456c7ec575cc26aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3289ee876572f6e91f06df3a24824443" id="r_ga3289ee876572f6e91f06df3a24824443"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga3289ee876572f6e91f06df3a24824443">glfwGetTimerFrequency</a> (void)</td></tr>
<tr class="memdesc:ga3289ee876572f6e91f06df3a24824443"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the frequency, in Hz, of the raw timer.  <br /></td></tr>
<tr class="separator:ga3289ee876572f6e91f06df3a24824443"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1c04dc242268f827290fe40aa1c91157" id="r_ga1c04dc242268f827290fe40aa1c91157"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__context.html#ga1c04dc242268f827290fe40aa1c91157">glfwMakeContextCurrent</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga1c04dc242268f827290fe40aa1c91157"><td class="mdescLeft">&#160;</td><td class="mdescRight">Makes the context of the specified window current for the calling thread.  <br /></td></tr>
<tr class="separator:ga1c04dc242268f827290fe40aa1c91157"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad94e80185397a6cf5fe2ab30567af71c" id="r_gad94e80185397a6cf5fe2ab30567af71c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__context.html#gad94e80185397a6cf5fe2ab30567af71c">glfwGetCurrentContext</a> (void)</td></tr>
<tr class="memdesc:gad94e80185397a6cf5fe2ab30567af71c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the window whose context is current on the calling thread.  <br /></td></tr>
<tr class="separator:gad94e80185397a6cf5fe2ab30567af71c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga15a5a1ee5b3c2ca6b15ca209a12efd14" id="r_ga15a5a1ee5b3c2ca6b15ca209a12efd14"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga15a5a1ee5b3c2ca6b15ca209a12efd14"><td class="mdescLeft">&#160;</td><td class="mdescRight">Swaps the front and back buffers of the specified window.  <br /></td></tr>
<tr class="separator:ga15a5a1ee5b3c2ca6b15ca209a12efd14"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6d4e0cdf151b5e579bd67f13202994ed" id="r_ga6d4e0cdf151b5e579bd67f13202994ed"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed">glfwSwapInterval</a> (int interval)</td></tr>
<tr class="memdesc:ga6d4e0cdf151b5e579bd67f13202994ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the swap interval for the current context.  <br /></td></tr>
<tr class="separator:ga6d4e0cdf151b5e579bd67f13202994ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga87425065c011cef1ebd6aac75e059dfa" id="r_ga87425065c011cef1ebd6aac75e059dfa"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__context.html#ga87425065c011cef1ebd6aac75e059dfa">glfwExtensionSupported</a> (const char *extension)</td></tr>
<tr class="memdesc:ga87425065c011cef1ebd6aac75e059dfa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the specified extension is available.  <br /></td></tr>
<tr class="separator:ga87425065c011cef1ebd6aac75e059dfa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga35f1837e6f666781842483937612f163" id="r_ga35f1837e6f666781842483937612f163"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__context.html#ga3d47c2d2fbe0be9c505d0e04e91a133c">GLFWglproc</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a> (const char *procname)</td></tr>
<tr class="memdesc:ga35f1837e6f666781842483937612f163"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the address of the specified function for the current context.  <br /></td></tr>
<tr class="separator:ga35f1837e6f666781842483937612f163"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2e7f30931e02464b5bc8d0d4b6f9fe2b" id="r_ga2e7f30931e02464b5bc8d0d4b6f9fe2b"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__vulkan.html#ga2e7f30931e02464b5bc8d0d4b6f9fe2b">glfwVulkanSupported</a> (void)</td></tr>
<tr class="memdesc:ga2e7f30931e02464b5bc8d0d4b6f9fe2b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the Vulkan loader and an ICD have been found.  <br /></td></tr>
<tr class="separator:ga2e7f30931e02464b5bc8d0d4b6f9fe2b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga99ad342d82f4a3421e2864978cb6d1d6" id="r_ga99ad342d82f4a3421e2864978cb6d1d6"><td class="memItemLeft" align="right" valign="top">const char **&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a> (uint32_t *count)</td></tr>
<tr class="memdesc:ga99ad342d82f4a3421e2864978cb6d1d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the Vulkan instance extensions required by GLFW.  <br /></td></tr>
<tr class="separator:ga99ad342d82f4a3421e2864978cb6d1d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadf228fac94c5fd8f12423ec9af9ff1e9" id="r_gadf228fac94c5fd8f12423ec9af9ff1e9"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__vulkan.html#ga70c01918dc9d233a4fbe0681a43018af">GLFWvkproc</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__vulkan.html#gadf228fac94c5fd8f12423ec9af9ff1e9">glfwGetInstanceProcAddress</a> (VkInstance instance, const char *procname)</td></tr>
<tr class="memdesc:gadf228fac94c5fd8f12423ec9af9ff1e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the address of the specified Vulkan instance function.  <br /></td></tr>
<tr class="separator:gadf228fac94c5fd8f12423ec9af9ff1e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaff3823355cdd7e2f3f9f4d9ea9518d92" id="r_gaff3823355cdd7e2f3f9f4d9ea9518d92"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__vulkan.html#gaff3823355cdd7e2f3f9f4d9ea9518d92">glfwGetPhysicalDevicePresentationSupport</a> (VkInstance instance, VkPhysicalDevice device, uint32_t queuefamily)</td></tr>
<tr class="memdesc:gaff3823355cdd7e2f3f9f4d9ea9518d92"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the specified queue family can present images.  <br /></td></tr>
<tr class="separator:gaff3823355cdd7e2f3f9f4d9ea9518d92"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1a24536bec3f80b08ead18e28e6ae965" id="r_ga1a24536bec3f80b08ead18e28e6ae965"><td class="memItemLeft" align="right" valign="top">VkResult&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__vulkan.html#ga1a24536bec3f80b08ead18e28e6ae965">glfwCreateWindowSurface</a> (VkInstance instance, <a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, const VkAllocationCallbacks *allocator, VkSurfaceKHR *surface)</td></tr>
<tr class="memdesc:ga1a24536bec3f80b08ead18e28e6ae965"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a Vulkan surface for the specified window.  <br /></td></tr>
<tr class="separator:ga1a24536bec3f80b08ead18e28e6ae965"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="a8a8538c5500308b4211844f2fb26c7b9" name="a8a8538c5500308b4211844f2fb26c7b9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8a8538c5500308b4211844f2fb26c7b9">&#9670;&#160;</a></span>GLFW_APIENTRY_DEFINED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_APIENTRY_DEFINED</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8f6dcdc968d214ff14779564f1389264" name="a8f6dcdc968d214ff14779564f1389264"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8f6dcdc968d214ff14779564f1389264">&#9670;&#160;</a></span>GLFW_NO_API</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_NO_API&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a01b3f66db266341425e9abee6b257db2" name="a01b3f66db266341425e9abee6b257db2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a01b3f66db266341425e9abee6b257db2">&#9670;&#160;</a></span>GLFW_OPENGL_API</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_OPENGL_API&#160;&#160;&#160;0x00030001</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a28d9b3bc6c2a522d815c8e146595051f" name="a28d9b3bc6c2a522d815c8e146595051f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a28d9b3bc6c2a522d815c8e146595051f">&#9670;&#160;</a></span>GLFW_OPENGL_ES_API</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_OPENGL_ES_API&#160;&#160;&#160;0x00030002</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8b306cb27f5bb0d6d67c7356a0e0fc34" name="a8b306cb27f5bb0d6d67c7356a0e0fc34"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8b306cb27f5bb0d6d67c7356a0e0fc34">&#9670;&#160;</a></span>GLFW_NO_ROBUSTNESS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_NO_ROBUSTNESS&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aee84a679230d205005e22487ff678a85" name="aee84a679230d205005e22487ff678a85"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aee84a679230d205005e22487ff678a85">&#9670;&#160;</a></span>GLFW_NO_RESET_NOTIFICATION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_NO_RESET_NOTIFICATION&#160;&#160;&#160;0x00031001</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aec1132f245143fc915b2f0995228564c" name="aec1132f245143fc915b2f0995228564c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aec1132f245143fc915b2f0995228564c">&#9670;&#160;</a></span>GLFW_LOSE_CONTEXT_ON_RESET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_LOSE_CONTEXT_ON_RESET&#160;&#160;&#160;0x00031002</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad6f2335d6f21cc9bab96633b1c111d5f" name="ad6f2335d6f21cc9bab96633b1c111d5f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad6f2335d6f21cc9bab96633b1c111d5f">&#9670;&#160;</a></span>GLFW_OPENGL_ANY_PROFILE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_OPENGL_ANY_PROFILE&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af094bb16da76f66ebceb19ee213b3de8" name="af094bb16da76f66ebceb19ee213b3de8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af094bb16da76f66ebceb19ee213b3de8">&#9670;&#160;</a></span>GLFW_OPENGL_CORE_PROFILE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_OPENGL_CORE_PROFILE&#160;&#160;&#160;0x00032001</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ac06b663d79c8fcf04669cc8fcc0b7670" name="ac06b663d79c8fcf04669cc8fcc0b7670"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac06b663d79c8fcf04669cc8fcc0b7670">&#9670;&#160;</a></span>GLFW_OPENGL_COMPAT_PROFILE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_OPENGL_COMPAT_PROFILE&#160;&#160;&#160;0x00032002</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aade31da5b884a84a7625c6b059b9132c" name="aade31da5b884a84a7625c6b059b9132c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aade31da5b884a84a7625c6b059b9132c">&#9670;&#160;</a></span>GLFW_CURSOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CURSOR&#160;&#160;&#160;0x00033001</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae3bbe2315b7691ab088159eb6c9110fc" name="ae3bbe2315b7691ab088159eb6c9110fc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae3bbe2315b7691ab088159eb6c9110fc">&#9670;&#160;</a></span>GLFW_STICKY_KEYS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_STICKY_KEYS&#160;&#160;&#160;0x00033002</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a4d7ce8ce71030c3b04e2b78145bc59d1" name="a4d7ce8ce71030c3b04e2b78145bc59d1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4d7ce8ce71030c3b04e2b78145bc59d1">&#9670;&#160;</a></span>GLFW_STICKY_MOUSE_BUTTONS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_STICKY_MOUSE_BUTTONS&#160;&#160;&#160;0x00033003</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a07b84de0b52143e1958f88a7d9105947" name="a07b84de0b52143e1958f88a7d9105947"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a07b84de0b52143e1958f88a7d9105947">&#9670;&#160;</a></span>GLFW_LOCK_KEY_MODS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_LOCK_KEY_MODS&#160;&#160;&#160;0x00033004</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aeeda1be76a44a1fc97c1282e06281fbb" name="aeeda1be76a44a1fc97c1282e06281fbb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aeeda1be76a44a1fc97c1282e06281fbb">&#9670;&#160;</a></span>GLFW_RAW_MOUSE_MOTION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_RAW_MOUSE_MOTION&#160;&#160;&#160;0x00033005</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae04dd25c8577e19fa8c97368561f6c68" name="ae04dd25c8577e19fa8c97368561f6c68"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae04dd25c8577e19fa8c97368561f6c68">&#9670;&#160;</a></span>GLFW_CURSOR_NORMAL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CURSOR_NORMAL&#160;&#160;&#160;0x00034001</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ac4d5cb9d78de8573349c58763d53bf11" name="ac4d5cb9d78de8573349c58763d53bf11"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac4d5cb9d78de8573349c58763d53bf11">&#9670;&#160;</a></span>GLFW_CURSOR_HIDDEN</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CURSOR_HIDDEN&#160;&#160;&#160;0x00034002</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2315b99a329ce53e6a13a9d46fd5ca88" name="a2315b99a329ce53e6a13a9d46fd5ca88"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2315b99a329ce53e6a13a9d46fd5ca88">&#9670;&#160;</a></span>GLFW_CURSOR_DISABLED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CURSOR_DISABLED&#160;&#160;&#160;0x00034003</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ac1dbfa0cb4641a0edc93412ade0895dc" name="ac1dbfa0cb4641a0edc93412ade0895dc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac1dbfa0cb4641a0edc93412ade0895dc">&#9670;&#160;</a></span>GLFW_CURSOR_CAPTURED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CURSOR_CAPTURED&#160;&#160;&#160;0x00034004</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a6b47d806f285efe9bfd7aeec667297ee" name="a6b47d806f285efe9bfd7aeec667297ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6b47d806f285efe9bfd7aeec667297ee">&#9670;&#160;</a></span>GLFW_ANY_RELEASE_BEHAVIOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ANY_RELEASE_BEHAVIOR&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a999961d391db49cb4f949c1dece0e13b" name="a999961d391db49cb4f949c1dece0e13b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a999961d391db49cb4f949c1dece0e13b">&#9670;&#160;</a></span>GLFW_RELEASE_BEHAVIOR_FLUSH</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_RELEASE_BEHAVIOR_FLUSH&#160;&#160;&#160;0x00035001</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="afca09088eccacdce4b59036cfae349c5" name="afca09088eccacdce4b59036cfae349c5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afca09088eccacdce4b59036cfae349c5">&#9670;&#160;</a></span>GLFW_RELEASE_BEHAVIOR_NONE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_RELEASE_BEHAVIOR_NONE&#160;&#160;&#160;0x00035002</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0494c9bfd3f584ab41e6dbeeaa0e6a19" name="a0494c9bfd3f584ab41e6dbeeaa0e6a19"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0494c9bfd3f584ab41e6dbeeaa0e6a19">&#9670;&#160;</a></span>GLFW_NATIVE_CONTEXT_API</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_NATIVE_CONTEXT_API&#160;&#160;&#160;0x00036001</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a03cf65c9ab01fc8b872ba58842c531c9" name="a03cf65c9ab01fc8b872ba58842c531c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a03cf65c9ab01fc8b872ba58842c531c9">&#9670;&#160;</a></span>GLFW_EGL_CONTEXT_API</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_EGL_CONTEXT_API&#160;&#160;&#160;0x00036002</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="afd34a473af9fa81f317910ea371b19e3" name="afd34a473af9fa81f317910ea371b19e3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afd34a473af9fa81f317910ea371b19e3">&#9670;&#160;</a></span>GLFW_OSMESA_CONTEXT_API</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_OSMESA_CONTEXT_API&#160;&#160;&#160;0x00036003</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae78e673449c2a2b8c560ca1b1e283228" name="ae78e673449c2a2b8c560ca1b1e283228"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae78e673449c2a2b8c560ca1b1e283228">&#9670;&#160;</a></span>GLFW_ANGLE_PLATFORM_TYPE_NONE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ANGLE_PLATFORM_TYPE_NONE&#160;&#160;&#160;0x00037001</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad8d9e97ed7790811470366b338833623" name="ad8d9e97ed7790811470366b338833623"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad8d9e97ed7790811470366b338833623">&#9670;&#160;</a></span>GLFW_ANGLE_PLATFORM_TYPE_OPENGL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ANGLE_PLATFORM_TYPE_OPENGL&#160;&#160;&#160;0x00037002</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0003c089da020cbf957218e70245bb65" name="a0003c089da020cbf957218e70245bb65"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0003c089da020cbf957218e70245bb65">&#9670;&#160;</a></span>GLFW_ANGLE_PLATFORM_TYPE_OPENGLES</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ANGLE_PLATFORM_TYPE_OPENGLES&#160;&#160;&#160;0x00037003</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a6e8fdc83113d247ad792bb5c4e82c894" name="a6e8fdc83113d247ad792bb5c4e82c894"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6e8fdc83113d247ad792bb5c4e82c894">&#9670;&#160;</a></span>GLFW_ANGLE_PLATFORM_TYPE_D3D9</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ANGLE_PLATFORM_TYPE_D3D9&#160;&#160;&#160;0x00037004</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad6eae659811a52a5cdc43c362aedfa33" name="ad6eae659811a52a5cdc43c362aedfa33"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad6eae659811a52a5cdc43c362aedfa33">&#9670;&#160;</a></span>GLFW_ANGLE_PLATFORM_TYPE_D3D11</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ANGLE_PLATFORM_TYPE_D3D11&#160;&#160;&#160;0x00037005</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a579ac83506c7546709dad91960cc7ca1" name="a579ac83506c7546709dad91960cc7ca1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a579ac83506c7546709dad91960cc7ca1">&#9670;&#160;</a></span>GLFW_ANGLE_PLATFORM_TYPE_VULKAN</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ANGLE_PLATFORM_TYPE_VULKAN&#160;&#160;&#160;0x00037007</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab56d91b26cf223dc67590a93a2f8507d" name="ab56d91b26cf223dc67590a93a2f8507d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab56d91b26cf223dc67590a93a2f8507d">&#9670;&#160;</a></span>GLFW_ANGLE_PLATFORM_TYPE_METAL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ANGLE_PLATFORM_TYPE_METAL&#160;&#160;&#160;0x00037008</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a92b0d7e0eaeeefaccc0ccc2ccb130e99" name="a92b0d7e0eaeeefaccc0ccc2ccb130e99"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a92b0d7e0eaeeefaccc0ccc2ccb130e99">&#9670;&#160;</a></span>GLFW_WAYLAND_PREFER_LIBDECOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_WAYLAND_PREFER_LIBDECOR&#160;&#160;&#160;0x00038001</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aadcea7c6afbf86b848404457c4253fd7" name="aadcea7c6afbf86b848404457c4253fd7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aadcea7c6afbf86b848404457c4253fd7">&#9670;&#160;</a></span>GLFW_WAYLAND_DISABLE_LIBDECOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_WAYLAND_DISABLE_LIBDECOR&#160;&#160;&#160;0x00038002</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa0e681bf859ef1bb8355692a70b0ee92" name="aa0e681bf859ef1bb8355692a70b0ee92"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa0e681bf859ef1bb8355692a70b0ee92">&#9670;&#160;</a></span>GLFW_ANY_POSITION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ANY_POSITION&#160;&#160;&#160;0x80000000</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="abe11513fd1ffbee5bb9b173f06028b9e" name="abe11513fd1ffbee5bb9b173f06028b9e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abe11513fd1ffbee5bb9b173f06028b9e">&#9670;&#160;</a></span>GLFW_CONNECTED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CONNECTED&#160;&#160;&#160;0x00040001</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aab64b25921ef21d89252d6f0a71bfc32" name="aab64b25921ef21d89252d6f0a71bfc32"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aab64b25921ef21d89252d6f0a71bfc32">&#9670;&#160;</a></span>GLFW_DISCONNECTED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_DISCONNECTED&#160;&#160;&#160;0x00040002</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a7a2edf2c18446833d27d07f1b7f3d571" name="a7a2edf2c18446833d27d07f1b7f3d571"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7a2edf2c18446833d27d07f1b7f3d571">&#9670;&#160;</a></span>GLFW_DONT_CARE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_DONT_CARE&#160;&#160;&#160;-1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa97755eb47e4bf2727ad45d610e18206" name="aa97755eb47e4bf2727ad45d610e18206"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa97755eb47e4bf2727ad45d610e18206">&#9670;&#160;</a></span>GLAPIENTRY</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLAPIENTRY&#160;&#160;&#160;APIENTRY</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3b526ac796be993406ea2f1642c25fc3" name="a3b526ac796be993406ea2f1642c25fc3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3b526ac796be993406ea2f1642c25fc3">&#9670;&#160;</a></span>GLFW_GLAPIENTRY_DEFINED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GLAPIENTRY_DEFINED</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
