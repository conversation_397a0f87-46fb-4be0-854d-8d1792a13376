﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{6003A4BA-E7FA-3CE7-AAC1-0583BE9220D1}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>IlmImf</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\cudabuild\3rdparty\lib\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">IlmImf.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">IlmImfd</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\cudabuild\3rdparty\lib\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">IlmImf.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">IlmImf</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\openexr;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half;D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/AI/opencv/cudabuild" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4127;4251;4324;4275;4512;4589;4819;4018;4099;4100;4101;4127;4189;4245;4305;4389;4512;4701;4702;4706;4800;4334;4244;4267;4456;4819</DisableSpecificWarnings>
      <ExceptionHandling>Async</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ProgramDataBaseFileName>D:\AI\opencv\cudabuild\3rdparty\lib\Debug\IlmImfd.pdb</ProgramDataBaseFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\openexr;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half;D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\openexr;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half;D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4221</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\openexr;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half;D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/AI/opencv/cudabuild" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4127;4251;4324;4275;4512;4589;4819;4018;4099;4100;4101;4127;4189;4245;4305;4389;4512;4701;4702;4706;4800;4334;4244;4267;4456;4819</DisableSpecificWarnings>
      <ExceptionHandling>Async</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;NDEBUG;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ProgramDataBaseFileName>D:\AI\opencv\cudabuild\3rdparty\lib\Release\IlmImf.pdb</ProgramDataBaseFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;NDEBUG;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\openexr;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half;D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\openexr;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half;D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4221</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half\eLut.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half\half.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half\halfExport.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half\halfFunction.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half\halfLimits.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half\toFloat.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\Iex.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexBaseExc.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexErrnoExc.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexExport.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexForward.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexMacros.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexMathExc.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexNamespace.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexThrowErrnoExc.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfAcesFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfArray.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfAutoArray.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfB44Compressor.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfBoxAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCRgbaFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfChannelList.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfChannelListAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCheckedArithmetic.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfChromaticities.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfChromaticitiesAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCompositeDeepScanLine.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCompression.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCompressionAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCompressor.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfConvert.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepCompositing.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepFrameBuffer.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepImageState.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepImageStateAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepScanLineInputFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepScanLineInputPart.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepScanLineOutputFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepScanLineOutputPart.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepTiledInputFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepTiledInputPart.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepTiledOutputFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepTiledOutputPart.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDoubleAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDwaCompressor.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDwaCompressorSimd.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfEnvmap.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfEnvmapAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfExport.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFastHuf.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFloatAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFloatVectorAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfForward.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFrameBuffer.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFramesPerSecond.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfGenericInputFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfGenericOutputFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfHeader.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfHuf.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfIO.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfInputFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfInputPart.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfInputPartData.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfInputStreamMutex.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfInt64.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfIntAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfKeyCode.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfKeyCodeAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfLineOrder.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfLineOrderAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfLut.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMatrixAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMisc.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMultiPartInputFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMultiPartOutputFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMultiView.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfName.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfNamespace.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOpaqueAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOptimizedPixelReading.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOutputFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOutputPart.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOutputPartData.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOutputStreamMutex.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPartHelper.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPartType.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPixelType.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPizCompressor.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPreviewImage.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPreviewImageAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPxr24Compressor.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRational.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRationalAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRgba.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRgbaFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRgbaYca.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRle.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRleCompressor.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfScanLineInputFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfSimd.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfStandardAttributes.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfStdIO.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfStringAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfStringVectorAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfSystemSpecific.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTestFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfThreading.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTileDescription.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTileDescriptionAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTileOffsets.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledInputFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledInputPart.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledMisc.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledOutputFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledOutputPart.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledRgbaFile.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTimeCode.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTimeCodeAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfVecAttribute.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfVersion.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfWav.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfXdr.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfZip.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfZipCompressor.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\b44ExpLogTable.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\dwaLookups.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThread.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadExport.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadForward.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadMutex.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadNamespace.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadPool.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadSemaphore.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathBox.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathBoxAlgo.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathColor.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathColorAlgo.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathEuler.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathExc.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathExport.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathForward.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathFrame.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathFrustum.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathFrustumTest.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathFun.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathHalfLimits.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathInt64.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathInterval.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathLimits.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathLine.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathLineAlgo.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathMath.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathMatrix.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathMatrixAlgo.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathNamespace.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathPlane.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathPlatform.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathQuat.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathRandom.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathRoots.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathShear.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathSphere.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathVec.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathVecAlgo.h" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\openexr\IlmBaseConfig.h" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\openexr\OpenEXRConfig.h" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half\half.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexBaseExc.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexThrowErrnoExc.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfAcesFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfB44Compressor.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfBoxAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCRgbaFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfChannelList.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfChannelListAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfChromaticities.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfChromaticitiesAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCompositeDeepScanLine.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCompressionAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCompressor.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfConvert.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepCompositing.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepFrameBuffer.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepImageStateAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepScanLineInputFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepScanLineInputPart.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepScanLineOutputFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepScanLineOutputPart.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepTiledInputFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepTiledInputPart.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepTiledOutputFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepTiledOutputPart.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDoubleAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDwaCompressor.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfEnvmap.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfEnvmapAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFastHuf.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFloatAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFloatVectorAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFrameBuffer.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFramesPerSecond.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfGenericInputFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfGenericOutputFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfHeader.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfHuf.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfIO.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfInputFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfInputPart.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfInputPartData.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfIntAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfKeyCode.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfKeyCodeAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfLineOrderAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfLut.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMatrixAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMisc.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMultiPartInputFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMultiPartOutputFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMultiView.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOpaqueAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOutputFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOutputPart.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOutputPartData.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPartType.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPizCompressor.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPreviewImage.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPreviewImageAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPxr24Compressor.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRational.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRationalAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRgbaFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRgbaYca.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRle.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRleCompressor.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfScanLineInputFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfStandardAttributes.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfStdIO.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfStringAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfStringVectorAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfSystemSpecific.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTestFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfThreading.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTileDescriptionAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTileOffsets.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledInputFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledInputPart.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledMisc.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledOutputFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledOutputPart.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledRgbaFile.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTimeCode.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTimeCodeAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfVecAttribute.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfVersion.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfWav.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfZip.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfZipCompressor.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\dwaLookups.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThread.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadMutex.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadMutexWin32.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadPool.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadSemaphore.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadSemaphoreWin32.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadWin32.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathBox.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathColorAlgo.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathFun.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathMatrixAlgo.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathRandom.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathShear.cpp" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathVec.cpp" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\AI\opencv\cudabuild\3rdparty\zlib\zlib.vcxproj">
      <Project>{5BA6087C-B49F-31D7-A8F0-C37E5E8F7F80}</Project>
      <Name>zlib</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>