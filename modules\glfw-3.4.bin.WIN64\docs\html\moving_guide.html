<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Moving from GLFW 2 to 3</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div><div class="header">
  <div class="headertitle"><div class="title">Moving from GLFW 2 to 3</div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#moving_removed">Changed and removed features</a><ul><li class="level2"><a href="#moving_renamed_files">Renamed library and header file</a></li>
<li class="level2"><a href="#moving_threads">Removal of threading functions</a></li>
<li class="level2"><a href="#moving_image">Removal of image and texture loading</a></li>
<li class="level2"><a href="#moving_stdcall">Removal of GLFWCALL macro</a></li>
<li class="level2"><a href="#moving_window_handles">Window handle parameters</a></li>
<li class="level2"><a href="#moving_monitor">Explicit monitor selection</a></li>
<li class="level2"><a href="#moving_autopoll">Removal of automatic event polling</a></li>
<li class="level2"><a href="#moving_context">Explicit context management</a></li>
<li class="level2"><a href="#moving_hidpi">Separation of window and framebuffer sizes</a></li>
<li class="level2"><a href="#moving_window_close">Window closing changes</a></li>
<li class="level2"><a href="#moving_hints">Persistent window hints</a></li>
<li class="level2"><a href="#moving_video_modes">Video mode enumeration</a></li>
<li class="level2"><a href="#moving_char_up">Removal of character actions</a></li>
<li class="level2"><a href="#moving_cursorpos">Cursor position changes</a></li>
<li class="level2"><a href="#moving_wheel">Wheel position replaced by scroll offsets</a></li>
<li class="level2"><a href="#moving_repeat">Key repeat action</a></li>
<li class="level2"><a href="#moving_keys">Physical key input</a></li>
<li class="level2"><a href="#moving_joystick">Joystick function changes</a></li>
<li class="level2"><a href="#moving_mbcs">Win32 MBCS support</a></li>
<li class="level2"><a href="#moving_windows">Support for versions of Windows older than XP</a></li>
<li class="level2"><a href="#moving_syskeys">Capture of system-wide hotkeys</a></li>
<li class="level2"><a href="#moving_terminate">Automatic termination</a></li>
<li class="level2"><a href="#moving_glu">GLU header inclusion</a></li>
</ul>
</li>
<li class="level1"><a href="#moving_tables">Name change tables</a><ul><li class="level2"><a href="#moving_renamed_functions">Renamed functions</a></li>
<li class="level2"><a href="#moving_renamed_types">Renamed types</a></li>
<li class="level2"><a href="#moving_renamed_tokens">Renamed tokens</a></li>
</ul>
</li>
</ul>
</div>
<div class="textblock"><p>This is a transition guide for moving from GLFW 2 to 3. It describes what has changed or been removed, but does <em>not</em> include <a class="el" href="news.html">new features</a> unless they are required when moving an existing code base onto the new API. For example, the new multi-monitor functions are required to create full screen windows with GLFW 3.</p>
<h1><a class="anchor" id="moving_removed"></a>
Changed and removed features</h1>
<h2><a class="anchor" id="moving_renamed_files"></a>
Renamed library and header file</h2>
<p>The GLFW 3 header is named <a class="el" href="glfw3_8h.html">glfw3.h</a> and moved to the <code>GLFW</code> directory, to avoid collisions with the headers of other major versions. Similarly, the GLFW 3 library is named <code>glfw3,</code> except when it's installed as a shared library on Unix-like systems, where it uses the <a href="https://en.wikipedia.org/wiki/soname">soname</a> <code>libglfw.so.3</code>.</p>
<p><b>Old syntax</b> </p><div class="fragment"><div class="line"><span class="preprocessor">#include &lt;GL/glfw.h&gt;</span></div>
</div><!-- fragment --><p><b>New syntax</b> </p><div class="fragment"><div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3_8h.html">GLFW/glfw3.h</a>&gt;</span></div>
<div class="ttc" id="aglfw3_8h_html"><div class="ttname"><a href="glfw3_8h.html">glfw3.h</a></div><div class="ttdoc">The header of the GLFW 3 API.</div></div>
</div><!-- fragment --><h2><a class="anchor" id="moving_threads"></a>
Removal of threading functions</h2>
<p>The threading functions have been removed, including the per-thread sleep function. They were fairly primitive, under-used, poorly integrated and took time away from the focus of GLFW (i.e. context, input and window). There are better threading libraries available and native threading support is available in both <a href="https://en.cppreference.com/w/cpp/thread">C++11</a> and <a href="https://en.cppreference.com/w/c/thread">C11</a>, both of which are gaining traction.</p>
<p>If you wish to use the C++11 or C11 facilities but your compiler doesn't yet support them, see the <a href="https://gitorious.org/tinythread/tinythreadpp">TinyThread++</a> and <a href="https://github.com/tinycthread/tinycthread">TinyCThread</a> projects created by the original author of GLFW. These libraries implement a usable subset of the threading APIs in C++11 and C11, and in fact some GLFW 3 test programs use TinyCThread.</p>
<p>However, GLFW 3 has better support for <em>use from multiple threads</em> than GLFW 2 had. Contexts can be made current on any thread, although only a single thread at a time, and the documentation explicitly states which functions may be used from any thread and which must only be used from the main thread.</p>
<p><b>Removed functions</b> </p><blockquote class="doxtable">
<p>&zwj;<code>glfwSleep</code>, <code>glfwCreateThread</code>, <code>glfwDestroyThread</code>, <code>glfwWaitThread</code>, <code>glfwGetThreadID</code>, <code>glfwCreateMutex</code>, <code>glfwDestroyMutex</code>, <code>glfwLockMutex</code>, <code>glfwUnlockMutex</code>, <code>glfwCreateCond</code>, <code>glfwDestroyCond</code>, <code>glfwWaitCond</code>, <code>glfwSignalCond</code>, <code>glfwBroadcastCond</code> and <code>glfwGetNumberOfProcessors</code>. </p>
</blockquote>
<p><b>Removed types</b> </p><blockquote class="doxtable">
<p>&zwj;<code>GLFWthreadfun</code> </p>
</blockquote>
<h2><a class="anchor" id="moving_image"></a>
Removal of image and texture loading</h2>
<p>The image and texture loading functions have been removed. They only supported the Targa image format, making them mostly useful for beginner level examples. To become of sufficiently high quality to warrant keeping them in GLFW 3, they would need not only to support other formats, but also modern extensions to OpenGL texturing. This would either add a number of external dependencies (libjpeg, libpng, etc.), or force GLFW to ship with inline versions of these libraries.</p>
<p>As there already are libraries doing this, it is unnecessary both to duplicate the work and to tie the duplicate to GLFW. The resulting library would also be platform-independent, as both OpenGL and stdio are available wherever GLFW is.</p>
<p><b>Removed functions</b> </p><blockquote class="doxtable">
<p>&zwj;<code>glfwReadImage</code>, <code>glfwReadMemoryImage</code>, <code>glfwFreeImage</code>, <code>glfwLoadTexture2D</code>, <code>glfwLoadMemoryTexture2D</code> and <code>glfwLoadTextureImage2D</code>. </p>
</blockquote>
<h2><a class="anchor" id="moving_stdcall"></a>
Removal of GLFWCALL macro</h2>
<p>The <code>GLFWCALL</code> macro, which made callback functions use <a href="https://msdn.microsoft.com/en-us/library/zxk0tw93.aspx">__stdcall</a> on Windows, has been removed. GLFW is written in C, not Pascal. Removing this macro means there's one less thing for application programmers to remember, i.e. the requirement to mark all callback functions with <code>GLFWCALL</code>. It also simplifies the creation of DLLs and DLL link libraries, as there's no need to explicitly disable <code>@n</code> entry point suffixes.</p>
<p><b>Old syntax</b> </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> GLFWCALL callback_function(...);</div>
</div><!-- fragment --><p><b>New syntax</b> </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> callback_function(...);</div>
</div><!-- fragment --><h2><a class="anchor" id="moving_window_handles"></a>
Window handle parameters</h2>
<p>Because GLFW 3 supports multiple windows, window handle parameters have been added to all window-related GLFW functions and callbacks. The handle of a newly created window is returned by <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a> (formerly <code>glfwOpenWindow</code>). Window handles are pointers to the <a href="https://en.wikipedia.org/wiki/Opaque_data_type">opaque</a> type <a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>.</p>
<p><b>Old syntax</b> </p><div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga5d877f09e968cef7a360b513306f17ff">glfwSetWindowTitle</a>(<span class="stringliteral">&quot;New Window Title&quot;</span>);</div>
<div class="ttc" id="agroup__window_html_ga5d877f09e968cef7a360b513306f17ff"><div class="ttname"><a href="group__window.html#ga5d877f09e968cef7a360b513306f17ff">glfwSetWindowTitle</a></div><div class="ttdeci">void glfwSetWindowTitle(GLFWwindow *window, const char *title)</div><div class="ttdoc">Sets the title of the specified window.</div></div>
</div><!-- fragment --><p><b>New syntax</b> </p><div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga5d877f09e968cef7a360b513306f17ff">glfwSetWindowTitle</a>(window, <span class="stringliteral">&quot;New Window Title&quot;</span>);</div>
</div><!-- fragment --><h2><a class="anchor" id="moving_monitor"></a>
Explicit monitor selection</h2>
<p>GLFW 3 provides support for multiple monitors. To request a full screen mode window, instead of passing <code>GLFW_FULLSCREEN</code> you specify which monitor you wish the window to use. The <a class="el" href="group__monitor.html#gac3adb24947eb709e1874028272e5dfc5">glfwGetPrimaryMonitor</a> function returns the monitor that GLFW 2 would have selected, but there are many other <a class="el" href="monitor_guide.html">monitor functions</a>. Monitor handles are pointers to the <a href="https://en.wikipedia.org/wiki/Opaque_data_type">opaque</a> type <a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>.</p>
<p><b>Old basic full screen</b> </p><div class="fragment"><div class="line">glfwOpenWindow(640, 480, 8, 8, 8, 0, 24, 0, GLFW_FULLSCREEN);</div>
</div><!-- fragment --><p><b>New basic full screen</b> </p><div class="fragment"><div class="line">window = <a class="code hl_function" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>(640, 480, <span class="stringliteral">&quot;My Window&quot;</span>, <a class="code hl_function" href="group__monitor.html#gac3adb24947eb709e1874028272e5dfc5">glfwGetPrimaryMonitor</a>(), NULL);</div>
<div class="ttc" id="agroup__monitor_html_gac3adb24947eb709e1874028272e5dfc5"><div class="ttname"><a href="group__monitor.html#gac3adb24947eb709e1874028272e5dfc5">glfwGetPrimaryMonitor</a></div><div class="ttdeci">GLFWmonitor * glfwGetPrimaryMonitor(void)</div><div class="ttdoc">Returns the primary monitor.</div></div>
<div class="ttc" id="agroup__window_html_ga3555a418df92ad53f917597fe2f64aeb"><div class="ttname"><a href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a></div><div class="ttdeci">GLFWwindow * glfwCreateWindow(int width, int height, const char *title, GLFWmonitor *monitor, GLFWwindow *share)</div><div class="ttdoc">Creates a window and its associated context.</div></div>
</div><!-- fragment --><dl class="section note"><dt>Note</dt><dd>The framebuffer bit depth parameters of <code>glfwOpenWindow</code> have been turned into <a class="el" href="window_guide.html#window_hints">window hints</a>, but as they have been given <a class="el" href="window_guide.html#window_hints_values">sane defaults</a> you rarely need to set these hints.</dd></dl>
<h2><a class="anchor" id="moving_autopoll"></a>
Removal of automatic event polling</h2>
<p>GLFW 3 does not automatically poll for events in <a class="el" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a>, meaning you need to call <a class="el" href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a> or <a class="el" href="group__window.html#ga554e37d781f0a997656c26b2c56c835e">glfwWaitEvents</a> yourself. Unlike buffer swap, which acts on a single window, the event processing functions act on all windows at once.</p>
<p><b>Old basic main loop</b> </p><div class="fragment"><div class="line"><span class="keywordflow">while</span> (...)</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// Process input</span></div>
<div class="line">    <span class="comment">// Render output</span></div>
<div class="line">    <a class="code hl_function" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a>();</div>
<div class="line">}</div>
<div class="ttc" id="agroup__window_html_ga15a5a1ee5b3c2ca6b15ca209a12efd14"><div class="ttname"><a href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a></div><div class="ttdeci">void glfwSwapBuffers(GLFWwindow *window)</div><div class="ttdoc">Swaps the front and back buffers of the specified window.</div></div>
</div><!-- fragment --><p><b>New basic main loop</b> </p><div class="fragment"><div class="line"><span class="keywordflow">while</span> (...)</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// Process input</span></div>
<div class="line">    <span class="comment">// Render output</span></div>
<div class="line">    <a class="code hl_function" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a>(window);</div>
<div class="line">    <a class="code hl_function" href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a>();</div>
<div class="line">}</div>
<div class="ttc" id="agroup__window_html_ga37bd57223967b4211d60ca1a0bf3c832"><div class="ttname"><a href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a></div><div class="ttdeci">void glfwPollEvents(void)</div><div class="ttdoc">Processes all pending events.</div></div>
</div><!-- fragment --><h2><a class="anchor" id="moving_context"></a>
Explicit context management</h2>
<p>Each GLFW 3 window has its own OpenGL context and only you, the application programmer, can know which context should be current on which thread at any given time. Therefore, GLFW 3 leaves that decision to you.</p>
<p>This means that you need to call <a class="el" href="group__context.html#ga1c04dc242268f827290fe40aa1c91157">glfwMakeContextCurrent</a> after creating a window before you can call any OpenGL functions.</p>
<h2><a class="anchor" id="moving_hidpi"></a>
Separation of window and framebuffer sizes</h2>
<p>Window positions and sizes now use screen coordinates, which may not be the same as pixels on machines with high-DPI monitors. This is important as OpenGL uses pixels, not screen coordinates. For example, the rectangle specified with <code>glViewport</code> needs to use pixels. Therefore, framebuffer size functions have been added. You can retrieve the size of the framebuffer of a window with <a class="el" href="group__window.html#ga0e2637a4161afb283f5300c7f94785c9">glfwGetFramebufferSize</a> function. A framebuffer size callback has also been added, which can be set with <a class="el" href="group__window.html#gab3fb7c3366577daef18c0023e2a8591f">glfwSetFramebufferSizeCallback</a>.</p>
<p><b>Old basic viewport setup</b> </p><div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#gaeea7cbc03373a41fb51cfbf9f2a5d4c6">glfwGetWindowSize</a>(&amp;width, &amp;height);</div>
<div class="line">glViewport(0, 0, width, height);</div>
<div class="ttc" id="agroup__window_html_gaeea7cbc03373a41fb51cfbf9f2a5d4c6"><div class="ttname"><a href="group__window.html#gaeea7cbc03373a41fb51cfbf9f2a5d4c6">glfwGetWindowSize</a></div><div class="ttdeci">void glfwGetWindowSize(GLFWwindow *window, int *width, int *height)</div><div class="ttdoc">Retrieves the size of the content area of the specified window.</div></div>
</div><!-- fragment --><p><b>New basic viewport setup</b> </p><div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga0e2637a4161afb283f5300c7f94785c9">glfwGetFramebufferSize</a>(window, &amp;width, &amp;height);</div>
<div class="line">glViewport(0, 0, width, height);</div>
<div class="ttc" id="agroup__window_html_ga0e2637a4161afb283f5300c7f94785c9"><div class="ttname"><a href="group__window.html#ga0e2637a4161afb283f5300c7f94785c9">glfwGetFramebufferSize</a></div><div class="ttdeci">void glfwGetFramebufferSize(GLFWwindow *window, int *width, int *height)</div><div class="ttdoc">Retrieves the size of the framebuffer of the specified window.</div></div>
</div><!-- fragment --><h2><a class="anchor" id="moving_window_close"></a>
Window closing changes</h2>
<p>The <code>GLFW_OPENED</code> window parameter has been removed. As long as the window has not been destroyed, whether through <a class="el" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a> or <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a>, the window is "open".</p>
<p>A user attempting to close a window is now just an event like any other. Unlike GLFW 2, windows and contexts created with GLFW 3 will never be destroyed unless you choose them to be. Each window now has a close flag that is set to <code>GLFW_TRUE</code> when the user attempts to close that window. By default, nothing else happens and the window stays visible. It is then up to you to either destroy the window, take some other action or ignore the request.</p>
<p>You can query the close flag at any time with <a class="el" href="group__window.html#ga24e02fbfefbb81fc45320989f8140ab5">glfwWindowShouldClose</a> and set it at any time with <a class="el" href="group__window.html#ga49c449dde2a6f87d996f4daaa09d6708">glfwSetWindowShouldClose</a>.</p>
<p><b>Old basic main loop</b> </p><div class="fragment"><div class="line"><span class="keywordflow">while</span> (glfwGetWindowParam(GLFW_OPENED))</div>
<div class="line">{</div>
<div class="line">    ...</div>
<div class="line">}</div>
</div><!-- fragment --><p><b>New basic main loop</b> </p><div class="fragment"><div class="line"><span class="keywordflow">while</span> (!<a class="code hl_function" href="group__window.html#ga24e02fbfefbb81fc45320989f8140ab5">glfwWindowShouldClose</a>(window))</div>
<div class="line">{</div>
<div class="line">    ...</div>
<div class="line">}</div>
<div class="ttc" id="agroup__window_html_ga24e02fbfefbb81fc45320989f8140ab5"><div class="ttname"><a href="group__window.html#ga24e02fbfefbb81fc45320989f8140ab5">glfwWindowShouldClose</a></div><div class="ttdeci">int glfwWindowShouldClose(GLFWwindow *window)</div><div class="ttdoc">Checks the close flag of the specified window.</div></div>
</div><!-- fragment --><p>The close callback no longer returns a value. Instead, it is called after the close flag has been set, so it can optionally override its value, before event processing completes. You may however not call <a class="el" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a> from the close callback (or any other window related callback).</p>
<p><b>Old syntax</b> </p><div class="fragment"><div class="line"><span class="keywordtype">int</span> GLFWCALL window_close_callback(<span class="keywordtype">void</span>);</div>
</div><!-- fragment --><p><b>New syntax</b> </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> window_close_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="ttc" id="agroup__window_html_ga3c96d80d363e67d13a41b5d1821f3242"><div class="ttname"><a href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a></div><div class="ttdeci">struct GLFWwindow GLFWwindow</div><div class="ttdoc">Opaque window object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1403</div></div>
</div><!-- fragment --><dl class="section note"><dt>Note</dt><dd>GLFW never clears the close flag to <code>GLFW_FALSE</code>, meaning you can use it for other reasons to close the window as well, for example the user choosing Quit from an in-game menu.</dd></dl>
<h2><a class="anchor" id="moving_hints"></a>
Persistent window hints</h2>
<p>The <code>glfwOpenWindowHint</code> function has been renamed to <a class="el" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>.</p>
<p>Window hints are no longer reset to their default values on window creation, but instead retain their values until modified by <a class="el" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a> or <a class="el" href="group__window.html#gaa77c4898dfb83344a6b4f76aa16b9a4a">glfwDefaultWindowHints</a>, or until the library is terminated and re-initialized.</p>
<h2><a class="anchor" id="moving_video_modes"></a>
Video mode enumeration</h2>
<p>Video mode enumeration is now per-monitor. The <a class="el" href="group__monitor.html#gad2e24d2843cb7d6c26202cddd530fc1b">glfwGetVideoModes</a> function now returns all available modes for a specific monitor instead of requiring you to guess how large an array you need. The <code>glfwGetDesktopMode</code> function, which had poorly defined behavior, has been replaced by <a class="el" href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a>, which returns the current mode of a monitor.</p>
<h2><a class="anchor" id="moving_char_up"></a>
Removal of character actions</h2>
<p>The action parameter of the <a class="el" href="group__input.html#ga1ab90a55cf3f58639b893c0f4118cb6e">character callback</a> has been removed. This was an artefact of the origin of GLFW, i.e. being developed in English by a Swede. However, many keyboard layouts require more than one key to produce characters with diacritical marks. Even the Swedish keyboard layout requires this for uncommon cases like ü.</p>
<p><b>Old syntax</b> </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> GLFWCALL character_callback(<span class="keywordtype">int</span> character, <span class="keywordtype">int</span> action);</div>
</div><!-- fragment --><p><b>New syntax</b> </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> character_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> character);</div>
</div><!-- fragment --><h2><a class="anchor" id="moving_cursorpos"></a>
Cursor position changes</h2>
<p>The <code>glfwGetMousePos</code> function has been renamed to <a class="el" href="group__input.html#ga01d37b6c40133676b9cea60ca1d7c0cc">glfwGetCursorPos</a>, <code>glfwSetMousePos</code> to <a class="el" href="group__input.html#ga04b03af936d906ca123c8f4ee08b39e7">glfwSetCursorPos</a> and <code>glfwSetMousePosCallback</code> to <a class="el" href="group__input.html#gac1f879ab7435d54d4d79bb469fe225d7">glfwSetCursorPosCallback</a>.</p>
<p>The cursor position is now <code>double</code> instead of <code>int</code>, both for the direct functions and for the callback. Some platforms can provide sub-pixel cursor movement and this data is now passed on to the application where available. On platforms where this is not provided, the decimal part is zero.</p>
<p>GLFW 3 only allows you to position the cursor within a window using <a class="el" href="group__input.html#ga04b03af936d906ca123c8f4ee08b39e7">glfwSetCursorPos</a> (formerly <code>glfwSetMousePos</code>) when that window is active. Unless the window is active, the function fails silently.</p>
<h2><a class="anchor" id="moving_wheel"></a>
Wheel position replaced by scroll offsets</h2>
<p>The <code>glfwGetMouseWheel</code> function has been removed. Scrolling is the input of offsets and has no absolute position. The mouse wheel callback has been replaced by a <a class="el" href="group__input.html#gaf656112c33de3efdb227fa58f0134cf5">scroll callback</a> that receives two-dimensional floating point scroll offsets. This allows you to receive precise scroll data from for example modern touchpads.</p>
<p><b>Old syntax</b> </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> GLFWCALL mouse_wheel_callback(<span class="keywordtype">int</span> position);</div>
</div><!-- fragment --><p><b>New syntax</b> </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> scroll_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">double</span> xoffset, <span class="keywordtype">double</span> yoffset);</div>
</div><!-- fragment --><p><b>Removed functions</b> </p><blockquote class="doxtable">
<p>&zwj;<code>glfwGetMouseWheel</code> </p>
</blockquote>
<h2><a class="anchor" id="moving_repeat"></a>
Key repeat action</h2>
<p>The <code>GLFW_KEY_REPEAT</code> enable has been removed and key repeat is always enabled for both keys and characters. A new key action, <code>GLFW_REPEAT</code>, has been added to allow the <a class="el" href="group__input.html#ga5bd751b27b90f865d2ea613533f0453c">key callback</a> to distinguish an initial key press from a repeat. Note that <a class="el" href="group__input.html#gadd341da06bc8d418b4dc3a3518af9ad2">glfwGetKey</a> still returns only <code>GLFW_PRESS</code> or <code>GLFW_RELEASE</code>.</p>
<h2><a class="anchor" id="moving_keys"></a>
Physical key input</h2>
<p>GLFW 3 key tokens map to physical keys, unlike in GLFW 2 where they mapped to the values generated by the current keyboard layout. The tokens are named according to the values they would have in the standard US layout, but this is only a convenience, as most programmers are assumed to know that layout. This means that (for example) <code>GLFW_KEY_LEFT_BRACKET</code> is always a single key and is the same key in the same place regardless of what keyboard layouts the users of your program have.</p>
<p>The key input facility was never meant for text input, although using it that way worked slightly better in GLFW 2. If you were using it to input text, you should be using the character callback instead, on both GLFW 2 and 3. This will give you the characters being input, as opposed to the keys being pressed.</p>
<p>GLFW 3 has key tokens for all keys on a standard 105 key keyboard, so instead of having to remember whether to check for <code>a</code> or <code>A</code>, you now check for <a class="el" href="group__keys.html#ga03e842608e1ea323370889d33b8f70ff">GLFW_KEY_A</a>.</p>
<h2><a class="anchor" id="moving_joystick"></a>
Joystick function changes</h2>
<p>The <code>glfwGetJoystickPos</code> function has been renamed to <a class="el" href="group__input.html#gaeb1c0191d3140a233a682987c61eb408">glfwGetJoystickAxes</a>.</p>
<p>The <code>glfwGetJoystickParam</code> function and the <code>GLFW_PRESENT</code>, <code>GLFW_AXES</code> and <code>GLFW_BUTTONS</code> tokens have been replaced by the <a class="el" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a> function as well as axis and button counts returned by the <a class="el" href="group__input.html#gaeb1c0191d3140a233a682987c61eb408">glfwGetJoystickAxes</a> and <a class="el" href="group__input.html#ga5ffe34739d3dc97efe432ed2d81d9938">glfwGetJoystickButtons</a> functions.</p>
<h2><a class="anchor" id="moving_mbcs"></a>
Win32 MBCS support</h2>
<p>The Win32 port of GLFW 3 will not compile in <a href="https://msdn.microsoft.com/en-us/library/5z097dxa.aspx">MBCS mode</a>. However, because the use of the Unicode version of the Win32 API doesn't affect the process as a whole, but only those windows created using it, it's perfectly possible to call MBCS functions from other parts of the same application. Therefore, even if an application using GLFW has MBCS mode code, there's no need for GLFW itself to support it.</p>
<h2><a class="anchor" id="moving_windows"></a>
Support for versions of Windows older than XP</h2>
<p>All explicit support for version of Windows older than XP has been removed. There is no code that actively prevents GLFW 3 from running on these earlier versions, but it uses Win32 functions that those versions lack.</p>
<p>Windows XP was released in 2001, and by now (January 2015) it has not only replaced almost all earlier versions of Windows, but is itself rapidly being replaced by Windows 7 and 8. The MSDN library doesn't even provide documentation for version older than Windows 2000, making it difficult to maintain compatibility with these versions even if it was deemed worth the effort.</p>
<p>The Win32 API has also not stood still, and GLFW 3 uses many functions only present on Windows XP or later. Even supporting an OS as new as XP (new from the perspective of GLFW 2, which still supports Windows 95) requires runtime checking for a number of functions that are present only on modern version of Windows.</p>
<h2><a class="anchor" id="moving_syskeys"></a>
Capture of system-wide hotkeys</h2>
<p>The ability to disable and capture system-wide hotkeys like Alt+Tab has been removed. Modern applications, whether they're games, scientific visualisations or something else, are nowadays expected to be good desktop citizens and allow these hotkeys to function even when running in full screen mode.</p>
<h2><a class="anchor" id="moving_terminate"></a>
Automatic termination</h2>
<p>GLFW 3 does not register <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a> with <code>atexit</code> at initialization, because <code>exit</code> calls registered functions from the calling thread and while it is permitted to call <code>exit</code> from any thread, <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a> must only be called from the main thread.</p>
<p>To release all resources allocated by GLFW, you should call <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a> yourself, from the main thread, before the program terminates. Note that this destroys all windows not already destroyed with <a class="el" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a>, invalidating any window handles you may still have.</p>
<h2><a class="anchor" id="moving_glu"></a>
GLU header inclusion</h2>
<p>GLFW 3 does not by default include the GLU header and GLU itself has been deprecated by <a href="https://en.wikipedia.org/wiki/Khronos_Group">Khronos</a>. <b>New projects should not use GLU</b>, but if you need it for legacy code that has been moved to GLFW 3, you can request that the GLFW header includes it by defining <a class="el" href="build_guide.html#GLFW_INCLUDE_GLU">GLFW_INCLUDE_GLU</a> before the inclusion of the GLFW header.</p>
<p><b>Old syntax</b> </p><div class="fragment"><div class="line"><span class="preprocessor">#include &lt;GL/glfw.h&gt;</span></div>
</div><!-- fragment --><p><b>New syntax</b> </p><div class="fragment"><div class="line"><span class="preprocessor">#define GLFW_INCLUDE_GLU</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3_8h.html">GLFW/glfw3.h</a>&gt;</span></div>
</div><!-- fragment --><p>There are many libraries that offer replacements for the functionality offered by GLU. For the matrix helper functions, see math libraries like <a href="https://github.com/g-truc/glm">GLM</a> (for C++), <a href="https://github.com/datenwolf/linmath.h">linmath.h</a> (for C) and others. For the tessellation functions, see for example <a href="https://github.com/memononen/libtess2">libtess2</a>.</p>
<h1><a class="anchor" id="moving_tables"></a>
Name change tables</h1>
<h2><a class="anchor" id="moving_renamed_functions"></a>
Renamed functions</h2>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">GLFW 2   </th><th class="markdownTableHeadNone">GLFW 3   </th><th class="markdownTableHeadNone">Notes    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>glfwOpenWindow</code>   </td><td class="markdownTableBodyNone"><a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>   </td><td class="markdownTableBodyNone">All channel bit depths are now hints    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>glfwCloseWindow</code>   </td><td class="markdownTableBodyNone"><a class="el" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>glfwOpenWindowHint</code>   </td><td class="markdownTableBodyNone"><a class="el" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>   </td><td class="markdownTableBodyNone">Now accepts all <code>GLFW_*_BITS</code> tokens    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>glfwEnable</code>   </td><td class="markdownTableBodyNone"><a class="el" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>glfwDisable</code>   </td><td class="markdownTableBodyNone"><a class="el" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>glfwGetMousePos</code>   </td><td class="markdownTableBodyNone"><a class="el" href="group__input.html#ga01d37b6c40133676b9cea60ca1d7c0cc">glfwGetCursorPos</a>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>glfwSetMousePos</code>   </td><td class="markdownTableBodyNone"><a class="el" href="group__input.html#ga04b03af936d906ca123c8f4ee08b39e7">glfwSetCursorPos</a>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>glfwSetMousePosCallback</code>   </td><td class="markdownTableBodyNone"><a class="el" href="group__input.html#gac1f879ab7435d54d4d79bb469fe225d7">glfwSetCursorPosCallback</a>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>glfwSetMouseWheelCallback</code>   </td><td class="markdownTableBodyNone"><a class="el" href="group__input.html#ga571e45a030ae4061f746ed56cb76aede">glfwSetScrollCallback</a>   </td><td class="markdownTableBodyNone">Accepts two-dimensional scroll offsets as doubles    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>glfwGetJoystickPos</code>   </td><td class="markdownTableBodyNone"><a class="el" href="group__input.html#gaeb1c0191d3140a233a682987c61eb408">glfwGetJoystickAxes</a>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>glfwGetWindowParam</code>   </td><td class="markdownTableBodyNone"><a class="el" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>glfwGetGLVersion</code>   </td><td class="markdownTableBodyNone"><a class="el" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>   </td><td class="markdownTableBodyNone">Use <code>GLFW_CONTEXT_VERSION_MAJOR</code>, <code>GLFW_CONTEXT_VERSION_MINOR</code> and <code>GLFW_CONTEXT_REVISION</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>glfwGetDesktopMode</code>   </td><td class="markdownTableBodyNone"><a class="el" href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a>   </td><td class="markdownTableBodyNone">Returns the current mode of a monitor    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>glfwGetJoystickParam</code>   </td><td class="markdownTableBodyNone"><a class="el" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a>   </td><td class="markdownTableBodyNone">The axis and button counts are provided by <a class="el" href="group__input.html#gaeb1c0191d3140a233a682987c61eb408">glfwGetJoystickAxes</a> and <a class="el" href="group__input.html#ga5ffe34739d3dc97efe432ed2d81d9938">glfwGetJoystickButtons</a>   </td></tr>
</table>
<h2><a class="anchor" id="moving_renamed_types"></a>
Renamed types</h2>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">GLFW 2   </th><th class="markdownTableHeadNone">GLFW 3   </th><th class="markdownTableHeadNone">Notes    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFWmousewheelfun</code>   </td><td class="markdownTableBodyNone"><a class="el" href="group__input.html#gaf656112c33de3efdb227fa58f0134cf5">GLFWscrollfun</a>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFWmouseposfun</code>   </td><td class="markdownTableBodyNone"><a class="el" href="group__input.html#gad6fae41b3ac2e4209aaa87b596c57f68">GLFWcursorposfun</a>   </td><td class="markdownTableBodyNone"></td></tr>
</table>
<h2><a class="anchor" id="moving_renamed_tokens"></a>
Renamed tokens</h2>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">GLFW 2   </th><th class="markdownTableHeadNone">GLFW 3   </th><th class="markdownTableHeadNone">Notes    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_OPENGL_VERSION_MAJOR</code>   </td><td class="markdownTableBodyNone"><code>GLFW_CONTEXT_VERSION_MAJOR</code>   </td><td class="markdownTableBodyNone">Renamed as it applies to OpenGL ES as well    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_OPENGL_VERSION_MINOR</code>   </td><td class="markdownTableBodyNone"><code>GLFW_CONTEXT_VERSION_MINOR</code>   </td><td class="markdownTableBodyNone">Renamed as it applies to OpenGL ES as well    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_FSAA_SAMPLES</code>   </td><td class="markdownTableBodyNone"><code>GLFW_SAMPLES</code>   </td><td class="markdownTableBodyNone">Renamed to match the OpenGL API    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_ACTIVE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_FOCUSED</code>   </td><td class="markdownTableBodyNone">Renamed to match the window focus callback    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_WINDOW_NO_RESIZE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_RESIZABLE</code>   </td><td class="markdownTableBodyNone">The default has been inverted    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_MOUSE_CURSOR</code>   </td><td class="markdownTableBodyNone"><code>GLFW_CURSOR</code>   </td><td class="markdownTableBodyNone">Used with <a class="el" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_KEY_ESC</code>   </td><td class="markdownTableBodyNone"><code>GLFW_KEY_ESCAPE</code>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_KEY_DEL</code>   </td><td class="markdownTableBodyNone"><code>GLFW_KEY_DELETE</code>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_KEY_PAGEUP</code>   </td><td class="markdownTableBodyNone"><code>GLFW_KEY_PAGE_UP</code>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_KEY_PAGEDOWN</code>   </td><td class="markdownTableBodyNone"><code>GLFW_KEY_PAGE_DOWN</code>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_KEY_KP_NUM_LOCK</code>   </td><td class="markdownTableBodyNone"><code>GLFW_KEY_NUM_LOCK</code>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_KEY_LCTRL</code>   </td><td class="markdownTableBodyNone"><code>GLFW_KEY_LEFT_CONTROL</code>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_KEY_LSHIFT</code>   </td><td class="markdownTableBodyNone"><code>GLFW_KEY_LEFT_SHIFT</code>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_KEY_LALT</code>   </td><td class="markdownTableBodyNone"><code>GLFW_KEY_LEFT_ALT</code>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_KEY_LSUPER</code>   </td><td class="markdownTableBodyNone"><code>GLFW_KEY_LEFT_SUPER</code>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_KEY_RCTRL</code>   </td><td class="markdownTableBodyNone"><code>GLFW_KEY_RIGHT_CONTROL</code>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_KEY_RSHIFT</code>   </td><td class="markdownTableBodyNone"><code>GLFW_KEY_RIGHT_SHIFT</code>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_KEY_RALT</code>   </td><td class="markdownTableBodyNone"><code>GLFW_KEY_RIGHT_ALT</code>   </td><td class="markdownTableBodyNone"></td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_KEY_RSUPER</code>   </td><td class="markdownTableBodyNone"><code>GLFW_KEY_RIGHT_SUPER</code>   </td><td class="markdownTableBodyNone"></td></tr>
</table>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
