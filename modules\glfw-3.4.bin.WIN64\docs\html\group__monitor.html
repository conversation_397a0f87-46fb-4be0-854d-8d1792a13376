<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Monitor reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">Monitor reference</div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>This is the reference documentation for monitor related functions and types. For more task-oriented information, see the <a class="el" href="monitor_guide.html">Monitor guide</a>. </p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga8d9efd1cde9426692c73fe40437d0ae3" id="r_ga8d9efd1cde9426692c73fe40437d0ae3"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a></td></tr>
<tr class="memdesc:ga8d9efd1cde9426692c73fe40437d0ae3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Opaque monitor object.  <br /></td></tr>
<tr class="separator:ga8d9efd1cde9426692c73fe40437d0ae3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaabe16caca8dea952504dfdebdf4cd249" id="r_gaabe16caca8dea952504dfdebdf4cd249"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#gaabe16caca8dea952504dfdebdf4cd249">GLFWmonitorfun</a>) (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, int event)</td></tr>
<tr class="memdesc:gaabe16caca8dea952504dfdebdf4cd249"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for monitor configuration callbacks.  <br /></td></tr>
<tr class="separator:gaabe16caca8dea952504dfdebdf4cd249"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga902c2816ac9b34b757282daab59b2565" id="r_ga902c2816ac9b34b757282daab59b2565"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga902c2816ac9b34b757282daab59b2565">GLFWvidmode</a></td></tr>
<tr class="memdesc:ga902c2816ac9b34b757282daab59b2565"><td class="mdescLeft">&#160;</td><td class="mdescRight">Video mode type.  <br /></td></tr>
<tr class="separator:ga902c2816ac9b34b757282daab59b2565"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga939cf093cb0af0498b7b54dc2e181404" id="r_ga939cf093cb0af0498b7b54dc2e181404"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga939cf093cb0af0498b7b54dc2e181404">GLFWgammaramp</a></td></tr>
<tr class="memdesc:ga939cf093cb0af0498b7b54dc2e181404"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gamma ramp.  <br /></td></tr>
<tr class="separator:ga939cf093cb0af0498b7b54dc2e181404"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga70b1156d5d24e9928f145d6c864369d2" id="r_ga70b1156d5d24e9928f145d6c864369d2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> **&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga70b1156d5d24e9928f145d6c864369d2">glfwGetMonitors</a> (int *count)</td></tr>
<tr class="memdesc:ga70b1156d5d24e9928f145d6c864369d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the currently connected monitors.  <br /></td></tr>
<tr class="separator:ga70b1156d5d24e9928f145d6c864369d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac3adb24947eb709e1874028272e5dfc5" id="r_gac3adb24947eb709e1874028272e5dfc5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#gac3adb24947eb709e1874028272e5dfc5">glfwGetPrimaryMonitor</a> (void)</td></tr>
<tr class="memdesc:gac3adb24947eb709e1874028272e5dfc5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the primary monitor.  <br /></td></tr>
<tr class="separator:gac3adb24947eb709e1874028272e5dfc5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga102f54e7acc9149edbcf0997152df8c9" id="r_ga102f54e7acc9149edbcf0997152df8c9"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga102f54e7acc9149edbcf0997152df8c9">glfwGetMonitorPos</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, int *xpos, int *ypos)</td></tr>
<tr class="memdesc:ga102f54e7acc9149edbcf0997152df8c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the position of the monitor's viewport on the virtual screen.  <br /></td></tr>
<tr class="separator:ga102f54e7acc9149edbcf0997152df8c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0" id="r_ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0">glfwGetMonitorWorkarea</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, int *xpos, int *ypos, int *width, int *height)</td></tr>
<tr class="memdesc:ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the work area of the monitor.  <br /></td></tr>
<tr class="separator:ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7d8bffc6c55539286a6bd20d32a8d7ea" id="r_ga7d8bffc6c55539286a6bd20d32a8d7ea"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga7d8bffc6c55539286a6bd20d32a8d7ea">glfwGetMonitorPhysicalSize</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, int *widthMM, int *heightMM)</td></tr>
<tr class="memdesc:ga7d8bffc6c55539286a6bd20d32a8d7ea"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the physical size of the monitor.  <br /></td></tr>
<tr class="separator:ga7d8bffc6c55539286a6bd20d32a8d7ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad3152e84465fa620b601265ebfcdb21b" id="r_gad3152e84465fa620b601265ebfcdb21b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#gad3152e84465fa620b601265ebfcdb21b">glfwGetMonitorContentScale</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, float *xscale, float *yscale)</td></tr>
<tr class="memdesc:gad3152e84465fa620b601265ebfcdb21b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the content scale for the specified monitor.  <br /></td></tr>
<tr class="separator:gad3152e84465fa620b601265ebfcdb21b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7af83e13489d90379588fb331b9e4b68" id="r_ga7af83e13489d90379588fb331b9e4b68"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga7af83e13489d90379588fb331b9e4b68">glfwGetMonitorName</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:ga7af83e13489d90379588fb331b9e4b68"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the name of the specified monitor.  <br /></td></tr>
<tr class="separator:ga7af83e13489d90379588fb331b9e4b68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga702750e24313a686d3637297b6e85fda" id="r_ga702750e24313a686d3637297b6e85fda"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga702750e24313a686d3637297b6e85fda">glfwSetMonitorUserPointer</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, void *pointer)</td></tr>
<tr class="memdesc:ga702750e24313a686d3637297b6e85fda"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the user pointer of the specified monitor.  <br /></td></tr>
<tr class="separator:ga702750e24313a686d3637297b6e85fda"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1adbfbfb8cd58b23cfee82e574fbbdc5" id="r_ga1adbfbfb8cd58b23cfee82e574fbbdc5"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga1adbfbfb8cd58b23cfee82e574fbbdc5">glfwGetMonitorUserPointer</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:ga1adbfbfb8cd58b23cfee82e574fbbdc5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the user pointer of the specified monitor.  <br /></td></tr>
<tr class="separator:ga1adbfbfb8cd58b23cfee82e574fbbdc5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab39df645587c8518192aa746c2fb06c3" id="r_gab39df645587c8518192aa746c2fb06c3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__monitor.html#gaabe16caca8dea952504dfdebdf4cd249">GLFWmonitorfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#gab39df645587c8518192aa746c2fb06c3">glfwSetMonitorCallback</a> (<a class="el" href="group__monitor.html#gaabe16caca8dea952504dfdebdf4cd249">GLFWmonitorfun</a> callback)</td></tr>
<tr class="memdesc:gab39df645587c8518192aa746c2fb06c3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the monitor configuration callback.  <br /></td></tr>
<tr class="separator:gab39df645587c8518192aa746c2fb06c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad2e24d2843cb7d6c26202cddd530fc1b" id="r_gad2e24d2843cb7d6c26202cddd530fc1b"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#gad2e24d2843cb7d6c26202cddd530fc1b">glfwGetVideoModes</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, int *count)</td></tr>
<tr class="memdesc:gad2e24d2843cb7d6c26202cddd530fc1b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the available video modes for the specified monitor.  <br /></td></tr>
<tr class="separator:gad2e24d2843cb7d6c26202cddd530fc1b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaba376fa7e76634b4788bddc505d6c9d5" id="r_gaba376fa7e76634b4788bddc505d6c9d5"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:gaba376fa7e76634b4788bddc505d6c9d5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the current mode of the specified monitor.  <br /></td></tr>
<tr class="separator:gaba376fa7e76634b4788bddc505d6c9d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6ac582625c990220785ddd34efa3169a" id="r_ga6ac582625c990220785ddd34efa3169a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga6ac582625c990220785ddd34efa3169a">glfwSetGamma</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, float gamma)</td></tr>
<tr class="memdesc:ga6ac582625c990220785ddd34efa3169a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generates a gamma ramp and sets it for the specified monitor.  <br /></td></tr>
<tr class="separator:ga6ac582625c990220785ddd34efa3169a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga76ba90debcf0062b5c4b73052b24f96f" id="r_ga76ba90debcf0062b5c4b73052b24f96f"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga76ba90debcf0062b5c4b73052b24f96f">glfwGetGammaRamp</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:ga76ba90debcf0062b5c4b73052b24f96f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the current gamma ramp for the specified monitor.  <br /></td></tr>
<tr class="separator:ga76ba90debcf0062b5c4b73052b24f96f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga583f0ffd0d29613d8cd172b996bbf0dd" id="r_ga583f0ffd0d29613d8cd172b996bbf0dd"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__monitor.html#ga583f0ffd0d29613d8cd172b996bbf0dd">glfwSetGammaRamp</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, const <a class="el" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a> *ramp)</td></tr>
<tr class="memdesc:ga583f0ffd0d29613d8cd172b996bbf0dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the current gamma ramp for the specified monitor.  <br /></td></tr>
<tr class="separator:ga583f0ffd0d29613d8cd172b996bbf0dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Typedef Documentation</h2>
<a id="ga8d9efd1cde9426692c73fe40437d0ae3" name="ga8d9efd1cde9426692c73fe40437d0ae3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8d9efd1cde9426692c73fe40437d0ae3">&#9670;&#160;</a></span>GLFWmonitor</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> <a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Opaque monitor object.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_object">Monitor objects</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gaabe16caca8dea952504dfdebdf4cd249" name="gaabe16caca8dea952504dfdebdf4cd249"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaabe16caca8dea952504dfdebdf4cd249">&#9670;&#160;</a></span>GLFWmonitorfun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWmonitorfun) (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, int event)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for monitor configuration callbacks. A monitor callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor, <span class="keywordtype">int</span> event)</div>
<div class="ttc" id="agroup__monitor_html_ga8d9efd1cde9426692c73fe40437d0ae3"><div class="ttname"><a href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a></div><div class="ttdeci">struct GLFWmonitor GLFWmonitor</div><div class="ttdoc">Opaque monitor object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1391</div></div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">monitor</td><td>The monitor that was connected or disconnected. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">event</td><td>One of <code>GLFW_CONNECTED</code> or <code>GLFW_DISCONNECTED</code>. Future releases may add more events.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_event">Monitor configuration changes</a> </dd>
<dd>
<a class="el" href="group__monitor.html#gab39df645587c8518192aa746c2fb06c3">glfwSetMonitorCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga902c2816ac9b34b757282daab59b2565" name="ga902c2816ac9b34b757282daab59b2565"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga902c2816ac9b34b757282daab59b2565">&#9670;&#160;</a></span>GLFWvidmode</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a> <a class="el" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This describes a single video mode.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_modes">Video modes</a> </dd>
<dd>
<a class="el" href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a> </dd>
<dd>
<a class="el" href="group__monitor.html#gad2e24d2843cb7d6c26202cddd530fc1b">glfwGetVideoModes</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. <b>GLFW 3:</b> Added refresh rate member. </dd></dl>

</div>
</div>
<a id="ga939cf093cb0af0498b7b54dc2e181404" name="ga939cf093cb0af0498b7b54dc2e181404"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga939cf093cb0af0498b7b54dc2e181404">&#9670;&#160;</a></span>GLFWgammaramp</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a> <a class="el" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This describes the gamma ramp for a monitor.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_gamma">Gamma ramp</a> </dd>
<dd>
<a class="el" href="group__monitor.html#ga76ba90debcf0062b5c4b73052b24f96f">glfwGetGammaRamp</a> </dd>
<dd>
<a class="el" href="group__monitor.html#ga583f0ffd0d29613d8cd172b996bbf0dd">glfwSetGammaRamp</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="ga70b1156d5d24e9928f145d6c864369d2" name="ga70b1156d5d24e9928f145d6c864369d2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga70b1156d5d24e9928f145d6c864369d2">&#9670;&#160;</a></span>glfwGetMonitors()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> ** glfwGetMonitors </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>count</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns an array of handles for all currently connected monitors. The primary monitor is always first in the returned array. If no monitors were found, this function returns <code>NULL</code>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[out]</td><td class="paramname">count</td><td>Where to store the number of monitors in the returned array. This is set to zero if an error occurred. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>An array of monitor handles, or <code>NULL</code> if no monitors were found or if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned array is allocated and freed by GLFW. You should not free it yourself. It is guaranteed to be valid only until the monitor configuration changes or the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_monitors">Retrieving monitors</a> </dd>
<dd>
<a class="el" href="monitor_guide.html#monitor_event">Monitor configuration changes</a> </dd>
<dd>
<a class="el" href="group__monitor.html#gac3adb24947eb709e1874028272e5dfc5">glfwGetPrimaryMonitor</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gac3adb24947eb709e1874028272e5dfc5" name="gac3adb24947eb709e1874028272e5dfc5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac3adb24947eb709e1874028272e5dfc5">&#9670;&#160;</a></span>glfwGetPrimaryMonitor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> * glfwGetPrimaryMonitor </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the primary monitor. This is usually the monitor where elements like the task bar or global menu bar are located.</p>
<dl class="section return"><dt>Returns</dt><dd>The primary monitor, or <code>NULL</code> if no monitors were found or if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>The primary monitor is always first in the array returned by <a class="el" href="group__monitor.html#ga70b1156d5d24e9928f145d6c864369d2">glfwGetMonitors</a>.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_monitors">Retrieving monitors</a> </dd>
<dd>
<a class="el" href="group__monitor.html#ga70b1156d5d24e9928f145d6c864369d2">glfwGetMonitors</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga102f54e7acc9149edbcf0997152df8c9" name="ga102f54e7acc9149edbcf0997152df8c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga102f54e7acc9149edbcf0997152df8c9">&#9670;&#160;</a></span>glfwGetMonitorPos()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwGetMonitorPos </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>xpos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>ypos</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the position, in screen coordinates, of the upper-left corner of the specified monitor.</p>
<p>Any or all of the position arguments may be <code>NULL</code>. If an error occurs, all non-<code>NULL</code> position arguments will be set to zero.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">monitor</td><td>The monitor to query. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">xpos</td><td>Where to store the monitor x-coordinate, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">ypos</td><td>Where to store the monitor y-coordinate, or <code>NULL</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_properties">Monitor properties</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0" name="ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0">&#9670;&#160;</a></span>glfwGetMonitorWorkarea()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwGetMonitorWorkarea </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>xpos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>ypos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>height</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the position, in screen coordinates, of the upper-left corner of the work area of the specified monitor along with the work area size in screen coordinates. The work area is defined as the area of the monitor not occluded by the window system task bar where present. If no task bar exists then the work area is the monitor resolution in screen coordinates.</p>
<p>Any or all of the position and size arguments may be <code>NULL</code>. If an error occurs, all non-<code>NULL</code> position and size arguments will be set to zero.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">monitor</td><td>The monitor to query. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">xpos</td><td>Where to store the monitor x-coordinate, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">ypos</td><td>Where to store the monitor y-coordinate, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">width</td><td>Where to store the monitor width, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">height</td><td>Where to store the monitor height, or <code>NULL</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_workarea">Work area</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga7d8bffc6c55539286a6bd20d32a8d7ea" name="ga7d8bffc6c55539286a6bd20d32a8d7ea"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7d8bffc6c55539286a6bd20d32a8d7ea">&#9670;&#160;</a></span>glfwGetMonitorPhysicalSize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwGetMonitorPhysicalSize </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>widthMM</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>heightMM</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the size, in millimetres, of the display area of the specified monitor.</p>
<p>Some platforms do not provide accurate monitor size information, either because the monitor <a href="https://en.wikipedia.org/wiki/Extended_display_identification_data">EDID</a> data is incorrect or because the driver does not report it accurately.</p>
<p>Any or all of the size arguments may be <code>NULL</code>. If an error occurs, all non-<code>NULL</code> size arguments will be set to zero.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">monitor</td><td>The monitor to query. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">widthMM</td><td>Where to store the width, in millimetres, of the monitor's display area, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">heightMM</td><td>Where to store the height, in millimetres, of the monitor's display area, or <code>NULL</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>Windows:</b> On Windows 8 and earlier the physical size is calculated from the current resolution and system DPI instead of querying the monitor EDID data.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_properties">Monitor properties</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gad3152e84465fa620b601265ebfcdb21b" name="gad3152e84465fa620b601265ebfcdb21b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad3152e84465fa620b601265ebfcdb21b">&#9670;&#160;</a></span>glfwGetMonitorContentScale()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwGetMonitorContentScale </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float *&#160;</td>
          <td class="paramname"><em>xscale</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float *&#160;</td>
          <td class="paramname"><em>yscale</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function retrieves the content scale for the specified monitor. The content scale is the ratio between the current DPI and the platform's default DPI. This is especially important for text and any UI elements. If the pixel dimensions of your UI scaled by this look appropriate on your machine then it should appear at a reasonable size on other machines regardless of their DPI and scaling settings. This relies on the system DPI and scaling settings being somewhat correct.</p>
<p>The content scale may depend on both the monitor resolution and pixel density and on user settings. It may be very different from the raw DPI calculated from the physical size and current resolution.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">monitor</td><td>The monitor to query. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">xscale</td><td>Where to store the x-axis content scale, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">yscale</td><td>Where to store the y-axis content scale, or <code>NULL</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>Wayland:</b> Fractional scaling information is not yet available for monitors, so this function only returns integer content scales.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_scale">Content scale</a> </dd>
<dd>
<a class="el" href="group__window.html#gaf5d31de9c19c4f994facea64d2b3106c">glfwGetWindowContentScale</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga7af83e13489d90379588fb331b9e4b68" name="ga7af83e13489d90379588fb331b9e4b68"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7af83e13489d90379588fb331b9e4b68">&#9670;&#160;</a></span>glfwGetMonitorName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char * glfwGetMonitorName </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns a human-readable name, encoded as UTF-8, of the specified monitor. The name typically reflects the make and model of the monitor and is not guaranteed to be unique among the connected monitors.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">monitor</td><td>The monitor to query. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The UTF-8 encoded name of the monitor, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned string is allocated and freed by GLFW. You should not free it yourself. It is valid until the specified monitor is disconnected or the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_properties">Monitor properties</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga702750e24313a686d3637297b6e85fda" name="ga702750e24313a686d3637297b6e85fda"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga702750e24313a686d3637297b6e85fda">&#9670;&#160;</a></span>glfwSetMonitorUserPointer()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetMonitorUserPointer </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>pointer</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the user-defined pointer of the specified monitor. The current value is retained until the monitor is disconnected. The initial value is <code>NULL</code>.</p>
<p>This function may be called from the monitor callback, even for a monitor that is being disconnected.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">monitor</td><td>The monitor whose pointer to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">pointer</td><td>The new value.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_userptr">User pointer</a> </dd>
<dd>
<a class="el" href="group__monitor.html#ga1adbfbfb8cd58b23cfee82e574fbbdc5">glfwGetMonitorUserPointer</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga1adbfbfb8cd58b23cfee82e574fbbdc5" name="ga1adbfbfb8cd58b23cfee82e574fbbdc5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1adbfbfb8cd58b23cfee82e574fbbdc5">&#9670;&#160;</a></span>glfwGetMonitorUserPointer()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void * glfwGetMonitorUserPointer </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the current value of the user-defined pointer of the specified monitor. The initial value is <code>NULL</code>.</p>
<p>This function may be called from the monitor callback, even for a monitor that is being disconnected.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">monitor</td><td>The monitor whose pointer to return.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_userptr">User pointer</a> </dd>
<dd>
<a class="el" href="group__monitor.html#ga702750e24313a686d3637297b6e85fda">glfwSetMonitorUserPointer</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="gab39df645587c8518192aa746c2fb06c3" name="gab39df645587c8518192aa746c2fb06c3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab39df645587c8518192aa746c2fb06c3">&#9670;&#160;</a></span>glfwSetMonitorCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__monitor.html#gaabe16caca8dea952504dfdebdf4cd249">GLFWmonitorfun</a> glfwSetMonitorCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#gaabe16caca8dea952504dfdebdf4cd249">GLFWmonitorfun</a>&#160;</td>
          <td class="paramname"><em>callback</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the monitor configuration callback, or removes the currently set callback. This is called when a monitor is connected to or disconnected from the system.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor, <span class="keywordtype">int</span> event)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__monitor.html#gaabe16caca8dea952504dfdebdf4cd249">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_event">Monitor configuration changes</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gad2e24d2843cb7d6c26202cddd530fc1b" name="gad2e24d2843cb7d6c26202cddd530fc1b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad2e24d2843cb7d6c26202cddd530fc1b">&#9670;&#160;</a></span>glfwGetVideoModes()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a> * glfwGetVideoModes </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>count</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns an array of all video modes supported by the specified monitor. The returned array is sorted in ascending order, first by color bit depth (the sum of all channel depths), then by resolution area (the product of width and height), then resolution width and finally by refresh rate.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">monitor</td><td>The monitor to query. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">count</td><td>Where to store the number of video modes in the returned array. This is set to zero if an error occurred. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>An array of video modes, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned array is allocated and freed by GLFW. You should not free it yourself. It is valid until the specified monitor is disconnected, this function is called again for that monitor or the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_modes">Video modes</a> </dd>
<dd>
<a class="el" href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. <b>GLFW 3:</b> Changed to return an array of modes for a specific monitor. </dd></dl>

</div>
</div>
<a id="gaba376fa7e76634b4788bddc505d6c9d5" name="gaba376fa7e76634b4788bddc505d6c9d5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaba376fa7e76634b4788bddc505d6c9d5">&#9670;&#160;</a></span>glfwGetVideoMode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a> * glfwGetVideoMode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the current video mode of the specified monitor. If you have created a full screen window for that monitor, the return value will depend on whether that window is iconified.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">monitor</td><td>The monitor to query. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The current mode of the monitor, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned array is allocated and freed by GLFW. You should not free it yourself. It is valid until the specified monitor is disconnected or the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_modes">Video modes</a> </dd>
<dd>
<a class="el" href="group__monitor.html#gad2e24d2843cb7d6c26202cddd530fc1b">glfwGetVideoModes</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. Replaces <code>glfwGetDesktopMode</code>. </dd></dl>

</div>
</div>
<a id="ga6ac582625c990220785ddd34efa3169a" name="ga6ac582625c990220785ddd34efa3169a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6ac582625c990220785ddd34efa3169a">&#9670;&#160;</a></span>glfwSetGamma()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetGamma </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>gamma</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function generates an appropriately sized gamma ramp from the specified exponent and then calls <a class="el" href="group__monitor.html#ga583f0ffd0d29613d8cd172b996bbf0dd">glfwSetGammaRamp</a> with it. The value must be a finite number greater than zero.</p>
<p>The software controlled gamma ramp is applied <em>in addition</em> to the hardware gamma correction, which today is usually an approximation of sRGB gamma. This means that setting a perfectly linear ramp, or gamma 1.0, will produce the default (usually sRGB-like) behavior.</p>
<p>For gamma correct rendering with OpenGL or OpenGL ES, see the <a class="el" href="window_guide.html#GLFW_SRGB_CAPABLE">GLFW_SRGB_CAPABLE</a> hint.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">monitor</td><td>The monitor whose gamma ramp to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">gamma</td><td>The desired exponent.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a>, <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a> and <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a> (see remarks).</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>Wayland:</b> Gamma handling is a privileged protocol, this function will thus never be implemented and emits <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_gamma">Gamma ramp</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga76ba90debcf0062b5c4b73052b24f96f" name="ga76ba90debcf0062b5c4b73052b24f96f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga76ba90debcf0062b5c4b73052b24f96f">&#9670;&#160;</a></span>glfwGetGammaRamp()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a> * glfwGetGammaRamp </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the current gamma ramp of the specified monitor.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">monitor</td><td>The monitor to query. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The current gamma ramp, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a> and <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a> (see remarks).</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>Wayland:</b> Gamma handling is a privileged protocol, this function will thus never be implemented and emits <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a> while returning <code>NULL</code>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned structure and its arrays are allocated and freed by GLFW. You should not free them yourself. They are valid until the specified monitor is disconnected, this function is called again for that monitor or the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_gamma">Gamma ramp</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga583f0ffd0d29613d8cd172b996bbf0dd" name="ga583f0ffd0d29613d8cd172b996bbf0dd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga583f0ffd0d29613d8cd172b996bbf0dd">&#9670;&#160;</a></span>glfwSetGammaRamp()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetGammaRamp </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a> *&#160;</td>
          <td class="paramname"><em>ramp</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the current gamma ramp for the specified monitor. The original gamma ramp for that monitor is saved by GLFW the first time this function is called and is restored by <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a>.</p>
<p>The software controlled gamma ramp is applied <em>in addition</em> to the hardware gamma correction, which today is usually an approximation of sRGB gamma. This means that setting a perfectly linear ramp, or gamma 1.0, will produce the default (usually sRGB-like) behavior.</p>
<p>For gamma correct rendering with OpenGL or OpenGL ES, see the <a class="el" href="window_guide.html#GLFW_SRGB_CAPABLE">GLFW_SRGB_CAPABLE</a> hint.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">monitor</td><td>The monitor whose gamma ramp to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ramp</td><td>The gamma ramp to use.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a> and <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a> (see remarks).</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>The size of the specified gamma ramp should match the size of the current ramp for that monitor.</dd>
<dd>
<b>Windows:</b> The gamma ramp size must be 256.</dd>
<dd>
<b>Wayland:</b> Gamma handling is a privileged protocol, this function will thus never be implemented and emits <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The specified gamma ramp is copied before this function returns.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_gamma">Gamma ramp</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
