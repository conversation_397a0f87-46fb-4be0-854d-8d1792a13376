__all__: list[str] = []

import cv2
import cv2.typing
import typing as _typing


# Enumerations
FEATURE_SET_COMPUTE_10: int
FEATURE_SET_COMPUTE_11: int
FEATURE_SET_COMPUTE_12: int
FEATURE_SET_COMPUTE_13: int
FEATURE_SET_COMPUTE_20: int
FEATURE_SET_COMPUTE_21: int
FEATURE_SET_COMPUTE_30: int
FEATURE_SET_COMPUTE_32: int
FEATURE_SET_COMPUTE_35: int
FEATURE_SET_COMPUTE_50: int
GLOBAL_ATOMICS: int
SHARED_ATOMICS: int
NATIVE_DOUBLE: int
WARP_SHUFFLE_FUNCTIONS: int
DYNAMIC_PARALLELISM: int
FeatureSet = int
"""One of [FEATURE_SET_COMPUTE_10, FEATURE_SET_COMPUTE_11, FEATURE_SET_COMPUTE_12, FEATURE_SET_COMPUTE_13, FEATURE_SET_COMPUTE_20, FEATURE_SET_COMPUTE_21, FEATURE_SET_COMPUTE_30, FEATURE_SET_COMPUTE_32, FEATURE_SET_COMPUTE_35, FEATURE_SET_COMPUTE_50, GLOBAL_ATOMICS, SHARED_ATOMICS, NATIVE_DOUBLE, WARP_SHUFFLE_FUNCTIONS, DYNAMIC_PARALLELISM]"""

COLOR_BayerBG2BGR_MHT: int
COLOR_BAYER_BG2BGR_MHT: int
COLOR_BayerGB2BGR_MHT: int
COLOR_BAYER_GB2BGR_MHT: int
COLOR_BayerRG2BGR_MHT: int
COLOR_BAYER_RG2BGR_MHT: int
COLOR_BayerGR2BGR_MHT: int
COLOR_BAYER_GR2BGR_MHT: int
COLOR_BayerBG2RGB_MHT: int
COLOR_BAYER_BG2RGB_MHT: int
COLOR_BayerGB2RGB_MHT: int
COLOR_BAYER_GB2RGB_MHT: int
COLOR_BayerRG2RGB_MHT: int
COLOR_BAYER_RG2RGB_MHT: int
COLOR_BayerGR2RGB_MHT: int
COLOR_BAYER_GR2RGB_MHT: int
COLOR_BayerBG2GRAY_MHT: int
COLOR_BAYER_BG2GRAY_MHT: int
COLOR_BayerGB2GRAY_MHT: int
COLOR_BAYER_GB2GRAY_MHT: int
COLOR_BayerRG2GRAY_MHT: int
COLOR_BAYER_RG2GRAY_MHT: int
COLOR_BayerGR2GRAY_MHT: int
COLOR_BAYER_GR2GRAY_MHT: int
DemosaicTypes = int
"""One of [COLOR_BayerBG2BGR_MHT, COLOR_BAYER_BG2BGR_MHT, COLOR_BayerGB2BGR_MHT, COLOR_BAYER_GB2BGR_MHT, COLOR_BayerRG2BGR_MHT, COLOR_BAYER_RG2BGR_MHT, COLOR_BayerGR2BGR_MHT, COLOR_BAYER_GR2BGR_MHT, COLOR_BayerBG2RGB_MHT, COLOR_BAYER_BG2RGB_MHT, COLOR_BayerGB2RGB_MHT, COLOR_BAYER_GB2RGB_MHT, COLOR_BayerRG2RGB_MHT, COLOR_BAYER_RG2RGB_MHT, COLOR_BayerGR2RGB_MHT, COLOR_BAYER_GR2RGB_MHT, COLOR_BayerBG2GRAY_MHT, COLOR_BAYER_BG2GRAY_MHT, COLOR_BayerGB2GRAY_MHT, COLOR_BAYER_GB2GRAY_MHT, COLOR_BayerRG2GRAY_MHT, COLOR_BAYER_RG2GRAY_MHT, COLOR_BayerGR2GRAY_MHT, COLOR_BAYER_GR2GRAY_MHT]"""

ALPHA_OVER: int
ALPHA_IN: int
ALPHA_OUT: int
ALPHA_ATOP: int
ALPHA_XOR: int
ALPHA_PLUS: int
ALPHA_OVER_PREMUL: int
ALPHA_IN_PREMUL: int
ALPHA_OUT_PREMUL: int
ALPHA_ATOP_PREMUL: int
ALPHA_XOR_PREMUL: int
ALPHA_PLUS_PREMUL: int
ALPHA_PREMUL: int
AlphaCompTypes = int
"""One of [ALPHA_OVER, ALPHA_IN, ALPHA_OUT, ALPHA_ATOP, ALPHA_XOR, ALPHA_PLUS, ALPHA_OVER_PREMUL, ALPHA_IN_PREMUL, ALPHA_OUT_PREMUL, ALPHA_ATOP_PREMUL, ALPHA_XOR_PREMUL, ALPHA_PLUS_PREMUL, ALPHA_PREMUL]"""

CCL_DEFAULT: int
CCL_BKE: int
ConnectedComponentsAlgorithmsTypes = int
"""One of [CCL_DEFAULT, CCL_BKE]"""

FIRST_ORDER_MOMENTS: int
SECOND_ORDER_MOMENTS: int
THIRD_ORDER_MOMENTS: int
MomentsOrder = int
"""One of [FIRST_ORDER_MOMENTS, SECOND_ORDER_MOMENTS, THIRD_ORDER_MOMENTS]"""


HostMem_PAGE_LOCKED: int
HOST_MEM_PAGE_LOCKED: int
HostMem_SHARED: int
HOST_MEM_SHARED: int
HostMem_WRITE_COMBINED: int
HOST_MEM_WRITE_COMBINED: int
HostMem_AllocType = int
"""One of [HostMem_PAGE_LOCKED, HOST_MEM_PAGE_LOCKED, HostMem_SHARED, HOST_MEM_SHARED, HostMem_WRITE_COMBINED, HOST_MEM_WRITE_COMBINED]"""

Event_DEFAULT: int
EVENT_DEFAULT: int
Event_BLOCKING_SYNC: int
EVENT_BLOCKING_SYNC: int
Event_DISABLE_TIMING: int
EVENT_DISABLE_TIMING: int
Event_INTERPROCESS: int
EVENT_INTERPROCESS: int
Event_CreateFlags = int
"""One of [Event_DEFAULT, EVENT_DEFAULT, Event_BLOCKING_SYNC, EVENT_BLOCKING_SYNC, Event_DISABLE_TIMING, EVENT_DISABLE_TIMING, Event_INTERPROCESS, EVENT_INTERPROCESS]"""

DeviceInfo_ComputeModeDefault: int
DEVICE_INFO_COMPUTE_MODE_DEFAULT: int
DeviceInfo_ComputeModeExclusive: int
DEVICE_INFO_COMPUTE_MODE_EXCLUSIVE: int
DeviceInfo_ComputeModeProhibited: int
DEVICE_INFO_COMPUTE_MODE_PROHIBITED: int
DeviceInfo_ComputeModeExclusiveProcess: int
DEVICE_INFO_COMPUTE_MODE_EXCLUSIVE_PROCESS: int
DeviceInfo_ComputeMode = int
"""One of [DeviceInfo_ComputeModeDefault, DEVICE_INFO_COMPUTE_MODE_DEFAULT, DeviceInfo_ComputeModeExclusive, DEVICE_INFO_COMPUTE_MODE_EXCLUSIVE, DeviceInfo_ComputeModeProhibited, DEVICE_INFO_COMPUTE_MODE_PROHIBITED, DeviceInfo_ComputeModeExclusiveProcess, DEVICE_INFO_COMPUTE_MODE_EXCLUSIVE_PROCESS]"""

SURF_CUDA_X_ROW: int
SURF_CUDA_Y_ROW: int
SURF_CUDA_LAPLACIAN_ROW: int
SURF_CUDA_OCTAVE_ROW: int
SURF_CUDA_SIZE_ROW: int
SURF_CUDA_ANGLE_ROW: int
SURF_CUDA_HESSIAN_ROW: int
SURF_CUDA_ROWS_COUNT: int
SURF_CUDA_KeypointLayout = int
"""One of [SURF_CUDA_X_ROW, SURF_CUDA_Y_ROW, SURF_CUDA_LAPLACIAN_ROW, SURF_CUDA_OCTAVE_ROW, SURF_CUDA_SIZE_ROW, SURF_CUDA_ANGLE_ROW, SURF_CUDA_HESSIAN_ROW, SURF_CUDA_ROWS_COUNT]"""

NvidiaOpticalFlow_1_0_NV_OF_PERF_LEVEL_UNDEFINED: int
NVIDIA_OPTICAL_FLOW_1_0_NV_OF_PERF_LEVEL_UNDEFINED: int
NvidiaOpticalFlow_1_0_NV_OF_PERF_LEVEL_SLOW: int
NVIDIA_OPTICAL_FLOW_1_0_NV_OF_PERF_LEVEL_SLOW: int
NvidiaOpticalFlow_1_0_NV_OF_PERF_LEVEL_MEDIUM: int
NVIDIA_OPTICAL_FLOW_1_0_NV_OF_PERF_LEVEL_MEDIUM: int
NvidiaOpticalFlow_1_0_NV_OF_PERF_LEVEL_FAST: int
NVIDIA_OPTICAL_FLOW_1_0_NV_OF_PERF_LEVEL_FAST: int
NvidiaOpticalFlow_1_0_NV_OF_PERF_LEVEL_MAX: int
NVIDIA_OPTICAL_FLOW_1_0_NV_OF_PERF_LEVEL_MAX: int
NvidiaOpticalFlow_1_0_NVIDIA_OF_PERF_LEVEL = int
"""One of [NvidiaOpticalFlow_1_0_NV_OF_PERF_LEVEL_UNDEFINED, NVIDIA_OPTICAL_FLOW_1_0_NV_OF_PERF_LEVEL_UNDEFINED, NvidiaOpticalFlow_1_0_NV_OF_PERF_LEVEL_SLOW, NVIDIA_OPTICAL_FLOW_1_0_NV_OF_PERF_LEVEL_SLOW, NvidiaOpticalFlow_1_0_NV_OF_PERF_LEVEL_MEDIUM, NVIDIA_OPTICAL_FLOW_1_0_NV_OF_PERF_LEVEL_MEDIUM, NvidiaOpticalFlow_1_0_NV_OF_PERF_LEVEL_FAST, NVIDIA_OPTICAL_FLOW_1_0_NV_OF_PERF_LEVEL_FAST, NvidiaOpticalFlow_1_0_NV_OF_PERF_LEVEL_MAX, NVIDIA_OPTICAL_FLOW_1_0_NV_OF_PERF_LEVEL_MAX]"""

NvidiaOpticalFlow_2_0_NV_OF_PERF_LEVEL_UNDEFINED: int
NVIDIA_OPTICAL_FLOW_2_0_NV_OF_PERF_LEVEL_UNDEFINED: int
NvidiaOpticalFlow_2_0_NV_OF_PERF_LEVEL_SLOW: int
NVIDIA_OPTICAL_FLOW_2_0_NV_OF_PERF_LEVEL_SLOW: int
NvidiaOpticalFlow_2_0_NV_OF_PERF_LEVEL_MEDIUM: int
NVIDIA_OPTICAL_FLOW_2_0_NV_OF_PERF_LEVEL_MEDIUM: int
NvidiaOpticalFlow_2_0_NV_OF_PERF_LEVEL_FAST: int
NVIDIA_OPTICAL_FLOW_2_0_NV_OF_PERF_LEVEL_FAST: int
NvidiaOpticalFlow_2_0_NV_OF_PERF_LEVEL_MAX: int
NVIDIA_OPTICAL_FLOW_2_0_NV_OF_PERF_LEVEL_MAX: int
NvidiaOpticalFlow_2_0_NVIDIA_OF_PERF_LEVEL = int
"""One of [NvidiaOpticalFlow_2_0_NV_OF_PERF_LEVEL_UNDEFINED, NVIDIA_OPTICAL_FLOW_2_0_NV_OF_PERF_LEVEL_UNDEFINED, NvidiaOpticalFlow_2_0_NV_OF_PERF_LEVEL_SLOW, NVIDIA_OPTICAL_FLOW_2_0_NV_OF_PERF_LEVEL_SLOW, NvidiaOpticalFlow_2_0_NV_OF_PERF_LEVEL_MEDIUM, NVIDIA_OPTICAL_FLOW_2_0_NV_OF_PERF_LEVEL_MEDIUM, NvidiaOpticalFlow_2_0_NV_OF_PERF_LEVEL_FAST, NVIDIA_OPTICAL_FLOW_2_0_NV_OF_PERF_LEVEL_FAST, NvidiaOpticalFlow_2_0_NV_OF_PERF_LEVEL_MAX, NVIDIA_OPTICAL_FLOW_2_0_NV_OF_PERF_LEVEL_MAX]"""

NvidiaOpticalFlow_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_UNDEFINED: int
NVIDIA_OPTICAL_FLOW_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_UNDEFINED: int
NvidiaOpticalFlow_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_1: int
NVIDIA_OPTICAL_FLOW_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_1: int
NvidiaOpticalFlow_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_2: int
NVIDIA_OPTICAL_FLOW_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_2: int
NvidiaOpticalFlow_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_4: int
NVIDIA_OPTICAL_FLOW_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_4: int
NvidiaOpticalFlow_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_MAX: int
NVIDIA_OPTICAL_FLOW_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_MAX: int
NvidiaOpticalFlow_2_0_NVIDIA_OF_OUTPUT_VECTOR_GRID_SIZE = int
"""One of [NvidiaOpticalFlow_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_UNDEFINED, NVIDIA_OPTICAL_FLOW_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_UNDEFINED, NvidiaOpticalFlow_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_1, NVIDIA_OPTICAL_FLOW_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_1, NvidiaOpticalFlow_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_2, NVIDIA_OPTICAL_FLOW_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_2, NvidiaOpticalFlow_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_4, NVIDIA_OPTICAL_FLOW_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_4, NvidiaOpticalFlow_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_MAX, NVIDIA_OPTICAL_FLOW_2_0_NV_OF_OUTPUT_VECTOR_GRID_SIZE_MAX]"""

NvidiaOpticalFlow_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_UNDEFINED: int
NVIDIA_OPTICAL_FLOW_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_UNDEFINED: int
NvidiaOpticalFlow_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_1: int
NVIDIA_OPTICAL_FLOW_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_1: int
NvidiaOpticalFlow_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_2: int
NVIDIA_OPTICAL_FLOW_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_2: int
NvidiaOpticalFlow_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_4: int
NVIDIA_OPTICAL_FLOW_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_4: int
NvidiaOpticalFlow_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_8: int
NVIDIA_OPTICAL_FLOW_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_8: int
NvidiaOpticalFlow_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_MAX: int
NVIDIA_OPTICAL_FLOW_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_MAX: int
NvidiaOpticalFlow_2_0_NVIDIA_OF_HINT_VECTOR_GRID_SIZE = int
"""One of [NvidiaOpticalFlow_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_UNDEFINED, NVIDIA_OPTICAL_FLOW_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_UNDEFINED, NvidiaOpticalFlow_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_1, NVIDIA_OPTICAL_FLOW_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_1, NvidiaOpticalFlow_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_2, NVIDIA_OPTICAL_FLOW_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_2, NvidiaOpticalFlow_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_4, NVIDIA_OPTICAL_FLOW_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_4, NvidiaOpticalFlow_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_8, NVIDIA_OPTICAL_FLOW_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_8, NvidiaOpticalFlow_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_MAX, NVIDIA_OPTICAL_FLOW_2_0_NV_OF_HINT_VECTOR_GRID_SIZE_MAX]"""


# Classes
class GpuMat:
    @property
    def step(self) -> int: ...

    # Classes
    class Allocator:
        ...


    # Functions
    @_typing.overload
    def __init__(self, allocator: GpuMat.Allocator = ...) -> None: ...
    @_typing.overload
    def __init__(self, rows: int, cols: int, type: int, allocator: GpuMat.Allocator = ...) -> None: ...
    @_typing.overload
    def __init__(self, size: cv2.typing.Size, type: int, allocator: GpuMat.Allocator = ...) -> None: ...
    @_typing.overload
    def __init__(self, rows: int, cols: int, type: int, s: cv2.typing.Scalar, allocator: GpuMat.Allocator = ...) -> None: ...
    @_typing.overload
    def __init__(self, size: cv2.typing.Size, type: int, s: cv2.typing.Scalar, allocator: GpuMat.Allocator = ...) -> None: ...
    @_typing.overload
    def __init__(self, m: GpuMat) -> None: ...
    @_typing.overload
    def __init__(self, m: GpuMat, rowRange: cv2.typing.Range, colRange: cv2.typing.Range) -> None: ...
    @_typing.overload
    def __init__(self, m: GpuMat, roi: cv2.typing.Rect) -> None: ...
    @_typing.overload
    def __init__(self, arr: cv2.typing.MatLike, allocator: GpuMat.Allocator = ...) -> None: ...
    @_typing.overload
    def __init__(self, arr: GpuMat, allocator: GpuMat.Allocator = ...) -> None: ...
    @_typing.overload
    def __init__(self, arr: cv2.UMat, allocator: GpuMat.Allocator = ...) -> None: ...

    @staticmethod
    def defaultAllocator() -> GpuMat.Allocator: ...

    @staticmethod
    def setDefaultAllocator(allocator: GpuMat.Allocator) -> None: ...

    @_typing.overload
    def create(self, rows: int, cols: int, type: int) -> None: ...
    @_typing.overload
    def create(self, size: cv2.typing.Size, type: int) -> None: ...

    def release(self) -> None: ...

    def swap(self, mat: GpuMat) -> None: ...

    @_typing.overload
    def upload(self, arr: cv2.typing.MatLike) -> None: ...
    @_typing.overload
    def upload(self, arr: GpuMat) -> None: ...
    @_typing.overload
    def upload(self, arr: cv2.UMat) -> None: ...
    @_typing.overload
    def upload(self, arr: cv2.typing.MatLike, stream: Stream) -> None: ...
    @_typing.overload
    def upload(self, arr: GpuMat, stream: Stream) -> None: ...
    @_typing.overload
    def upload(self, arr: cv2.UMat, stream: Stream) -> None: ...

    @_typing.overload
    def download(self, dst: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def download(self, dst: GpuMat | None = ...) -> GpuMat: ...
    @_typing.overload
    def download(self, dst: cv2.UMat | None = ...) -> cv2.UMat: ...
    @_typing.overload
    def download(self, stream: Stream, dst: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def download(self, stream: Stream, dst: GpuMat | None = ...) -> GpuMat: ...
    @_typing.overload
    def download(self, stream: Stream, dst: cv2.UMat | None = ...) -> cv2.UMat: ...

    def clone(self) -> GpuMat: ...

    @_typing.overload
    def copyTo(self, dst: GpuMat | None = ...) -> GpuMat: ...
    @_typing.overload
    def copyTo(self, stream: Stream, dst: GpuMat | None = ...) -> GpuMat: ...
    @_typing.overload
    def copyTo(self, mask: GpuMat, dst: GpuMat | None = ...) -> GpuMat: ...
    @_typing.overload
    def copyTo(self, mask: GpuMat, stream: Stream, dst: GpuMat | None = ...) -> GpuMat: ...

    @_typing.overload
    def setTo(self, s: cv2.typing.Scalar) -> GpuMat: ...
    @_typing.overload
    def setTo(self, s: cv2.typing.Scalar, stream: Stream) -> GpuMat: ...
    @_typing.overload
    def setTo(self, s: cv2.typing.Scalar, mask: cv2.typing.MatLike) -> GpuMat: ...
    @_typing.overload
    def setTo(self, s: cv2.typing.Scalar, mask: GpuMat) -> GpuMat: ...
    @_typing.overload
    def setTo(self, s: cv2.typing.Scalar, mask: cv2.UMat) -> GpuMat: ...
    @_typing.overload
    def setTo(self, s: cv2.typing.Scalar, mask: cv2.typing.MatLike, stream: Stream) -> GpuMat: ...
    @_typing.overload
    def setTo(self, s: cv2.typing.Scalar, mask: GpuMat, stream: Stream) -> GpuMat: ...
    @_typing.overload
    def setTo(self, s: cv2.typing.Scalar, mask: cv2.UMat, stream: Stream) -> GpuMat: ...

    @_typing.overload
    def convertTo(self, rtype: int, stream: Stream, dst: GpuMat | None = ...) -> GpuMat: ...
    @_typing.overload
    def convertTo(self, rtype: int, dst: GpuMat | None = ..., alpha: float = ..., beta: float = ...) -> GpuMat: ...
    @_typing.overload
    def convertTo(self, rtype: int, alpha: float, beta: float, stream: Stream, dst: GpuMat | None = ...) -> GpuMat: ...

    def assignTo(self, m: GpuMat, type: int = ...) -> None: ...

    def row(self, y: int) -> GpuMat: ...

    def col(self, x: int) -> GpuMat: ...

    @_typing.overload
    def rowRange(self, startrow: int, endrow: int) -> GpuMat: ...
    @_typing.overload
    def rowRange(self, r: cv2.typing.Range) -> GpuMat: ...

    @_typing.overload
    def colRange(self, startcol: int, endcol: int) -> GpuMat: ...
    @_typing.overload
    def colRange(self, r: cv2.typing.Range) -> GpuMat: ...

    def reshape(self, cn: int, rows: int = ...) -> GpuMat: ...

    def locateROI(self, wholeSize: cv2.typing.Size, ofs: cv2.typing.Point) -> None: ...

    def adjustROI(self, dtop: int, dbottom: int, dleft: int, dright: int) -> GpuMat: ...

    def isContinuous(self) -> bool: ...

    def elemSize(self) -> int: ...

    def elemSize1(self) -> int: ...

    def type(self) -> int: ...

    def depth(self) -> int: ...

    def channels(self) -> int: ...

    def step1(self) -> int: ...

    def size(self) -> cv2.typing.Size: ...

    def empty(self) -> bool: ...

    def cudaPtr(self) -> cv2.typing.IntPointer: ...

    def updateContinuityFlag(self) -> None: ...


class GpuData:
    ...

class GpuMatND:
    ...

class BufferPool:
    # Functions
    def __init__(self, stream: Stream) -> None: ...

    @_typing.overload
    def getBuffer(self, rows: int, cols: int, type: int) -> GpuMat: ...
    @_typing.overload
    def getBuffer(self, size: cv2.typing.Size, type: int) -> GpuMat: ...

    def getAllocator(self) -> GpuMat.Allocator: ...


class HostMem:
    @property
    def step(self) -> int: ...

    # Functions
    @_typing.overload
    def __init__(self, alloc_type: HostMem_AllocType = ...) -> None: ...
    @_typing.overload
    def __init__(self, rows: int, cols: int, type: int, alloc_type: HostMem_AllocType = ...) -> None: ...
    @_typing.overload
    def __init__(self, size: cv2.typing.Size, type: int, alloc_type: HostMem_AllocType = ...) -> None: ...
    @_typing.overload
    def __init__(self, arr: cv2.typing.MatLike, alloc_type: HostMem_AllocType = ...) -> None: ...
    @_typing.overload
    def __init__(self, arr: GpuMat, alloc_type: HostMem_AllocType = ...) -> None: ...
    @_typing.overload
    def __init__(self, arr: cv2.UMat, alloc_type: HostMem_AllocType = ...) -> None: ...

    def swap(self, b: HostMem) -> None: ...

    def clone(self) -> HostMem: ...

    def create(self, rows: int, cols: int, type: int) -> None: ...

    def reshape(self, cn: int, rows: int = ...) -> HostMem: ...

    def createMatHeader(self) -> cv2.typing.MatLike: ...

    def isContinuous(self) -> bool: ...

    def elemSize(self) -> int: ...

    def elemSize1(self) -> int: ...

    def type(self) -> int: ...

    def depth(self) -> int: ...

    def channels(self) -> int: ...

    def step1(self) -> int: ...

    def size(self) -> cv2.typing.Size: ...

    def empty(self) -> bool: ...


class Stream:
    # Functions
    @_typing.overload
    def __init__(self) -> None: ...
    @_typing.overload
    def __init__(self, allocator: GpuMat.Allocator) -> None: ...
    @_typing.overload
    def __init__(self, cudaFlags: int) -> None: ...

    def queryIfComplete(self) -> bool: ...

    def waitForCompletion(self) -> None: ...

    def waitEvent(self, event: Event) -> None: ...

    @classmethod
    def Null(cls) -> Stream: ...

    def cudaPtr(self) -> cv2.typing.IntPointer: ...


class Event:
    # Functions
    def __init__(self, flags: Event_CreateFlags = ...) -> None: ...

    def record(self, stream: Stream = ...) -> None: ...

    def queryIfComplete(self) -> bool: ...

    def waitForCompletion(self) -> None: ...

    @staticmethod
    def elapsedTime(start: Event, end: Event) -> float: ...


class TargetArchs:
    # Functions
    @staticmethod
    def has(major: int, minor: int) -> bool: ...

    @staticmethod
    def hasPtx(major: int, minor: int) -> bool: ...

    @staticmethod
    def hasBin(major: int, minor: int) -> bool: ...

    @staticmethod
    def hasEqualOrLessPtx(major: int, minor: int) -> bool: ...

    @staticmethod
    def hasEqualOrGreater(major: int, minor: int) -> bool: ...

    @staticmethod
    def hasEqualOrGreaterPtx(major: int, minor: int) -> bool: ...

    @staticmethod
    def hasEqualOrGreaterBin(major: int, minor: int) -> bool: ...


class DeviceInfo:
    # Functions
    @_typing.overload
    def __init__(self) -> None: ...
    @_typing.overload
    def __init__(self, device_id: int) -> None: ...

    def deviceID(self) -> int: ...

    def totalGlobalMem(self) -> int: ...

    def sharedMemPerBlock(self) -> int: ...

    def regsPerBlock(self) -> int: ...

    def warpSize(self) -> int: ...

    def memPitch(self) -> int: ...

    def maxThreadsPerBlock(self) -> int: ...

    def maxThreadsDim(self) -> cv2.typing.Vec3i: ...

    def maxGridSize(self) -> cv2.typing.Vec3i: ...

    def clockRate(self) -> int: ...

    def totalConstMem(self) -> int: ...

    def majorVersion(self) -> int: ...

    def minorVersion(self) -> int: ...

    def textureAlignment(self) -> int: ...

    def texturePitchAlignment(self) -> int: ...

    def multiProcessorCount(self) -> int: ...

    def kernelExecTimeoutEnabled(self) -> bool: ...

    def integrated(self) -> bool: ...

    def canMapHostMemory(self) -> bool: ...

    def computeMode(self) -> DeviceInfo_ComputeMode: ...

    def maxTexture1D(self) -> int: ...

    def maxTexture1DMipmap(self) -> int: ...

    def maxTexture1DLinear(self) -> int: ...

    def maxTexture2D(self) -> cv2.typing.Vec2i: ...

    def maxTexture2DMipmap(self) -> cv2.typing.Vec2i: ...

    def maxTexture2DLinear(self) -> cv2.typing.Vec3i: ...

    def maxTexture2DGather(self) -> cv2.typing.Vec2i: ...

    def maxTexture3D(self) -> cv2.typing.Vec3i: ...

    def maxTextureCubemap(self) -> int: ...

    def maxTexture1DLayered(self) -> cv2.typing.Vec2i: ...

    def maxTexture2DLayered(self) -> cv2.typing.Vec3i: ...

    def maxTextureCubemapLayered(self) -> cv2.typing.Vec2i: ...

    def maxSurface1D(self) -> int: ...

    def maxSurface2D(self) -> cv2.typing.Vec2i: ...

    def maxSurface3D(self) -> cv2.typing.Vec3i: ...

    def maxSurface1DLayered(self) -> cv2.typing.Vec2i: ...

    def maxSurface2DLayered(self) -> cv2.typing.Vec3i: ...

    def maxSurfaceCubemap(self) -> int: ...

    def maxSurfaceCubemapLayered(self) -> cv2.typing.Vec2i: ...

    def surfaceAlignment(self) -> int: ...

    def concurrentKernels(self) -> bool: ...

    def ECCEnabled(self) -> bool: ...

    def pciBusID(self) -> int: ...

    def pciDeviceID(self) -> int: ...

    def pciDomainID(self) -> int: ...

    def tccDriver(self) -> bool: ...

    def asyncEngineCount(self) -> int: ...

    def unifiedAddressing(self) -> bool: ...

    def memoryClockRate(self) -> int: ...

    def memoryBusWidth(self) -> int: ...

    def l2CacheSize(self) -> int: ...

    def maxThreadsPerMultiProcessor(self) -> int: ...

    def queryMemory(self, totalMemory: int, freeMemory: int) -> None: ...

    def freeMemory(self) -> int: ...

    def totalMemory(self) -> int: ...

    def isCompatible(self) -> bool: ...


class LookUpTable(cv2.Algorithm):
    # Functions
    @_typing.overload
    def transform(self, src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def transform(self, src: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def transform(self, src: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...


class DFT(cv2.Algorithm):
    # Functions
    @_typing.overload
    def compute(self, image: cv2.typing.MatLike, result: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def compute(self, image: GpuMat, result: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def compute(self, image: cv2.UMat, result: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...


class Convolution(cv2.Algorithm):
    # Functions
    @_typing.overload
    def convolve(self, image: cv2.typing.MatLike, templ: cv2.typing.MatLike, result: cv2.typing.MatLike | None = ..., ccorr: bool = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def convolve(self, image: GpuMat, templ: GpuMat, result: GpuMat | None = ..., ccorr: bool = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def convolve(self, image: cv2.UMat, templ: cv2.UMat, result: cv2.UMat | None = ..., ccorr: bool = ..., stream: Stream = ...) -> cv2.UMat: ...


class Filter(cv2.Algorithm):
    # Functions
    @_typing.overload
    def apply(self, src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def apply(self, src: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def apply(self, src: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...


class CLAHE(cv2.CLAHE):
    # Functions
    @_typing.overload
    def apply(self, src: cv2.typing.MatLike, stream: Stream, dst: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def apply(self, src: GpuMat, stream: Stream, dst: GpuMat | None = ...) -> GpuMat: ...
    @_typing.overload
    def apply(self, src: cv2.UMat, stream: Stream, dst: cv2.UMat | None = ...) -> cv2.UMat: ...


class CannyEdgeDetector(cv2.Algorithm):
    # Functions
    @_typing.overload
    def detect(self, image: cv2.typing.MatLike, edges: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def detect(self, image: GpuMat, edges: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def detect(self, image: cv2.UMat, edges: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...
    @_typing.overload
    def detect(self, dx: cv2.typing.MatLike, dy: cv2.typing.MatLike, edges: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def detect(self, dx: GpuMat, dy: GpuMat, edges: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def detect(self, dx: cv2.UMat, dy: cv2.UMat, edges: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

    def setLowThreshold(self, low_thresh: float) -> None: ...

    def getLowThreshold(self) -> float: ...

    def setHighThreshold(self, high_thresh: float) -> None: ...

    def getHighThreshold(self) -> float: ...

    def setAppertureSize(self, apperture_size: int) -> None: ...

    def getAppertureSize(self) -> int: ...

    def setL2Gradient(self, L2gradient: bool) -> None: ...

    def getL2Gradient(self) -> bool: ...


class HoughLinesDetector(cv2.Algorithm):
    # Functions
    @_typing.overload
    def detect(self, src: cv2.typing.MatLike, lines: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def detect(self, src: GpuMat, lines: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def detect(self, src: cv2.UMat, lines: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

    @_typing.overload
    def downloadResults(self, d_lines: cv2.typing.MatLike, h_lines: cv2.typing.MatLike | None = ..., h_votes: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> tuple[cv2.typing.MatLike, cv2.typing.MatLike]: ...
    @_typing.overload
    def downloadResults(self, d_lines: GpuMat, h_lines: GpuMat | None = ..., h_votes: GpuMat | None = ..., stream: Stream = ...) -> tuple[GpuMat, GpuMat]: ...
    @_typing.overload
    def downloadResults(self, d_lines: cv2.UMat, h_lines: cv2.UMat | None = ..., h_votes: cv2.UMat | None = ..., stream: Stream = ...) -> tuple[cv2.UMat, cv2.UMat]: ...

    def setRho(self, rho: float) -> None: ...

    def getRho(self) -> float: ...

    def setTheta(self, theta: float) -> None: ...

    def getTheta(self) -> float: ...

    def setThreshold(self, threshold: int) -> None: ...

    def getThreshold(self) -> int: ...

    def setDoSort(self, doSort: bool) -> None: ...

    def getDoSort(self) -> bool: ...

    def setMaxLines(self, maxLines: int) -> None: ...

    def getMaxLines(self) -> int: ...


class HoughSegmentDetector(cv2.Algorithm):
    # Functions
    @_typing.overload
    def detect(self, src: cv2.typing.MatLike, lines: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def detect(self, src: GpuMat, lines: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def detect(self, src: cv2.UMat, lines: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

    def setRho(self, rho: float) -> None: ...

    def getRho(self) -> float: ...

    def setTheta(self, theta: float) -> None: ...

    def getTheta(self) -> float: ...

    def setMinLineLength(self, minLineLength: int) -> None: ...

    def getMinLineLength(self) -> int: ...

    def setMaxLineGap(self, maxLineGap: int) -> None: ...

    def getMaxLineGap(self) -> int: ...

    def setMaxLines(self, maxLines: int) -> None: ...

    def getMaxLines(self) -> int: ...

    def setThreshold(self, threshold: int) -> None: ...

    def getThreshold(self) -> int: ...


class HoughCirclesDetector(cv2.Algorithm):
    # Functions
    @_typing.overload
    def detect(self, src: cv2.typing.MatLike, circles: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def detect(self, src: GpuMat, circles: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def detect(self, src: cv2.UMat, circles: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

    def setDp(self, dp: float) -> None: ...

    def getDp(self) -> float: ...

    def setMinDist(self, minDist: float) -> None: ...

    def getMinDist(self) -> float: ...

    def setCannyThreshold(self, cannyThreshold: int) -> None: ...

    def getCannyThreshold(self) -> int: ...

    def setVotesThreshold(self, votesThreshold: int) -> None: ...

    def getVotesThreshold(self) -> int: ...

    def setMinRadius(self, minRadius: int) -> None: ...

    def getMinRadius(self) -> int: ...

    def setMaxRadius(self, maxRadius: int) -> None: ...

    def getMaxRadius(self) -> int: ...

    def setMaxCircles(self, maxCircles: int) -> None: ...

    def getMaxCircles(self) -> int: ...


class CornernessCriteria(cv2.Algorithm):
    # Functions
    @_typing.overload
    def compute(self, src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def compute(self, src: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def compute(self, src: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...


class CornersDetector(cv2.Algorithm):
    # Functions
    @_typing.overload
    def detect(self, image: cv2.typing.MatLike, corners: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def detect(self, image: GpuMat, corners: GpuMat | None = ..., mask: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def detect(self, image: cv2.UMat, corners: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

    def setMaxCorners(self, maxCorners: int) -> None: ...

    def setMinDistance(self, minDistance: float) -> None: ...


class TemplateMatching(cv2.Algorithm):
    # Functions
    @_typing.overload
    def match(self, image: cv2.typing.MatLike, templ: cv2.typing.MatLike, result: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def match(self, image: GpuMat, templ: GpuMat, result: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def match(self, image: cv2.UMat, templ: cv2.UMat, result: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...


class DescriptorMatcher(cv2.Algorithm):
    # Functions
    @classmethod
    def createBFMatcher(cls, normType: int = ...) -> DescriptorMatcher: ...

    def isMaskSupported(self) -> bool: ...

    def add(self, descriptors: _typing.Sequence[GpuMat]) -> None: ...

    def getTrainDescriptors(self) -> _typing.Sequence[GpuMat]: ...

    def clear(self) -> None: ...

    def empty(self) -> bool: ...

    def train(self) -> None: ...

    @_typing.overload
    def match(self, queryDescriptors: cv2.typing.MatLike, trainDescriptors: cv2.typing.MatLike, mask: cv2.typing.MatLike | None = ...) -> _typing.Sequence[cv2.DMatch]: ...
    @_typing.overload
    def match(self, queryDescriptors: GpuMat, trainDescriptors: GpuMat, mask: GpuMat | None = ...) -> _typing.Sequence[cv2.DMatch]: ...
    @_typing.overload
    def match(self, queryDescriptors: cv2.UMat, trainDescriptors: cv2.UMat, mask: cv2.UMat | None = ...) -> _typing.Sequence[cv2.DMatch]: ...
    @_typing.overload
    def match(self, queryDescriptors: cv2.typing.MatLike, masks: _typing.Sequence[GpuMat] | None = ...) -> _typing.Sequence[cv2.DMatch]: ...
    @_typing.overload
    def match(self, queryDescriptors: GpuMat, masks: _typing.Sequence[GpuMat] | None = ...) -> _typing.Sequence[cv2.DMatch]: ...
    @_typing.overload
    def match(self, queryDescriptors: cv2.UMat, masks: _typing.Sequence[GpuMat] | None = ...) -> _typing.Sequence[cv2.DMatch]: ...

    @_typing.overload
    def matchAsync(self, queryDescriptors: cv2.typing.MatLike, trainDescriptors: cv2.typing.MatLike, matches: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def matchAsync(self, queryDescriptors: GpuMat, trainDescriptors: GpuMat, matches: GpuMat | None = ..., mask: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def matchAsync(self, queryDescriptors: cv2.UMat, trainDescriptors: cv2.UMat, matches: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...
    @_typing.overload
    def matchAsync(self, queryDescriptors: cv2.typing.MatLike, matches: cv2.typing.MatLike | None = ..., masks: _typing.Sequence[GpuMat] | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def matchAsync(self, queryDescriptors: GpuMat, matches: GpuMat | None = ..., masks: _typing.Sequence[GpuMat] | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def matchAsync(self, queryDescriptors: cv2.UMat, matches: cv2.UMat | None = ..., masks: _typing.Sequence[GpuMat] | None = ..., stream: Stream = ...) -> cv2.UMat: ...

    @_typing.overload
    def matchConvert(self, gpu_matches: cv2.typing.MatLike) -> _typing.Sequence[cv2.DMatch]: ...
    @_typing.overload
    def matchConvert(self, gpu_matches: GpuMat) -> _typing.Sequence[cv2.DMatch]: ...
    @_typing.overload
    def matchConvert(self, gpu_matches: cv2.UMat) -> _typing.Sequence[cv2.DMatch]: ...

    @_typing.overload
    def knnMatch(self, queryDescriptors: cv2.typing.MatLike, trainDescriptors: cv2.typing.MatLike, k: int, mask: cv2.typing.MatLike | None = ..., compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...
    @_typing.overload
    def knnMatch(self, queryDescriptors: GpuMat, trainDescriptors: GpuMat, k: int, mask: GpuMat | None = ..., compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...
    @_typing.overload
    def knnMatch(self, queryDescriptors: cv2.UMat, trainDescriptors: cv2.UMat, k: int, mask: cv2.UMat | None = ..., compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...
    @_typing.overload
    def knnMatch(self, queryDescriptors: cv2.typing.MatLike, k: int, masks: _typing.Sequence[GpuMat] | None = ..., compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...
    @_typing.overload
    def knnMatch(self, queryDescriptors: GpuMat, k: int, masks: _typing.Sequence[GpuMat] | None = ..., compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...
    @_typing.overload
    def knnMatch(self, queryDescriptors: cv2.UMat, k: int, masks: _typing.Sequence[GpuMat] | None = ..., compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...

    @_typing.overload
    def knnMatchAsync(self, queryDescriptors: cv2.typing.MatLike, trainDescriptors: cv2.typing.MatLike, k: int, matches: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def knnMatchAsync(self, queryDescriptors: GpuMat, trainDescriptors: GpuMat, k: int, matches: GpuMat | None = ..., mask: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def knnMatchAsync(self, queryDescriptors: cv2.UMat, trainDescriptors: cv2.UMat, k: int, matches: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...
    @_typing.overload
    def knnMatchAsync(self, queryDescriptors: cv2.typing.MatLike, k: int, matches: cv2.typing.MatLike | None = ..., masks: _typing.Sequence[GpuMat] | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def knnMatchAsync(self, queryDescriptors: GpuMat, k: int, matches: GpuMat | None = ..., masks: _typing.Sequence[GpuMat] | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def knnMatchAsync(self, queryDescriptors: cv2.UMat, k: int, matches: cv2.UMat | None = ..., masks: _typing.Sequence[GpuMat] | None = ..., stream: Stream = ...) -> cv2.UMat: ...

    @_typing.overload
    def knnMatchConvert(self, gpu_matches: cv2.typing.MatLike, compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...
    @_typing.overload
    def knnMatchConvert(self, gpu_matches: GpuMat, compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...
    @_typing.overload
    def knnMatchConvert(self, gpu_matches: cv2.UMat, compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...

    @_typing.overload
    def radiusMatch(self, queryDescriptors: cv2.typing.MatLike, trainDescriptors: cv2.typing.MatLike, maxDistance: float, mask: cv2.typing.MatLike | None = ..., compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...
    @_typing.overload
    def radiusMatch(self, queryDescriptors: GpuMat, trainDescriptors: GpuMat, maxDistance: float, mask: GpuMat | None = ..., compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...
    @_typing.overload
    def radiusMatch(self, queryDescriptors: cv2.UMat, trainDescriptors: cv2.UMat, maxDistance: float, mask: cv2.UMat | None = ..., compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...
    @_typing.overload
    def radiusMatch(self, queryDescriptors: cv2.typing.MatLike, maxDistance: float, masks: _typing.Sequence[GpuMat] | None = ..., compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...
    @_typing.overload
    def radiusMatch(self, queryDescriptors: GpuMat, maxDistance: float, masks: _typing.Sequence[GpuMat] | None = ..., compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...
    @_typing.overload
    def radiusMatch(self, queryDescriptors: cv2.UMat, maxDistance: float, masks: _typing.Sequence[GpuMat] | None = ..., compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...

    @_typing.overload
    def radiusMatchAsync(self, queryDescriptors: cv2.typing.MatLike, trainDescriptors: cv2.typing.MatLike, maxDistance: float, matches: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def radiusMatchAsync(self, queryDescriptors: GpuMat, trainDescriptors: GpuMat, maxDistance: float, matches: GpuMat | None = ..., mask: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def radiusMatchAsync(self, queryDescriptors: cv2.UMat, trainDescriptors: cv2.UMat, maxDistance: float, matches: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...
    @_typing.overload
    def radiusMatchAsync(self, queryDescriptors: cv2.typing.MatLike, maxDistance: float, matches: cv2.typing.MatLike | None = ..., masks: _typing.Sequence[GpuMat] | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def radiusMatchAsync(self, queryDescriptors: GpuMat, maxDistance: float, matches: GpuMat | None = ..., masks: _typing.Sequence[GpuMat] | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def radiusMatchAsync(self, queryDescriptors: cv2.UMat, maxDistance: float, matches: cv2.UMat | None = ..., masks: _typing.Sequence[GpuMat] | None = ..., stream: Stream = ...) -> cv2.UMat: ...

    @_typing.overload
    def radiusMatchConvert(self, gpu_matches: cv2.typing.MatLike, compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...
    @_typing.overload
    def radiusMatchConvert(self, gpu_matches: GpuMat, compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...
    @_typing.overload
    def radiusMatchConvert(self, gpu_matches: cv2.UMat, compactResult: bool = ...) -> _typing.Sequence[_typing.Sequence[cv2.DMatch]]: ...


class Feature2DAsync(cv2.Feature2D):
    # Functions
    @_typing.overload
    def detectAsync(self, image: cv2.typing.MatLike, keypoints: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def detectAsync(self, image: GpuMat, keypoints: GpuMat | None = ..., mask: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def detectAsync(self, image: cv2.UMat, keypoints: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

    @_typing.overload
    def computeAsync(self, image: cv2.typing.MatLike, keypoints: cv2.typing.MatLike | None = ..., descriptors: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> tuple[cv2.typing.MatLike, cv2.typing.MatLike]: ...
    @_typing.overload
    def computeAsync(self, image: GpuMat, keypoints: GpuMat | None = ..., descriptors: GpuMat | None = ..., stream: Stream = ...) -> tuple[GpuMat, GpuMat]: ...
    @_typing.overload
    def computeAsync(self, image: cv2.UMat, keypoints: cv2.UMat | None = ..., descriptors: cv2.UMat | None = ..., stream: Stream = ...) -> tuple[cv2.UMat, cv2.UMat]: ...

    @_typing.overload
    def detectAndComputeAsync(self, image: cv2.typing.MatLike, mask: cv2.typing.MatLike, keypoints: cv2.typing.MatLike | None = ..., descriptors: cv2.typing.MatLike | None = ..., useProvidedKeypoints: bool = ..., stream: Stream = ...) -> tuple[cv2.typing.MatLike, cv2.typing.MatLike]: ...
    @_typing.overload
    def detectAndComputeAsync(self, image: GpuMat, mask: GpuMat, keypoints: GpuMat | None = ..., descriptors: GpuMat | None = ..., useProvidedKeypoints: bool = ..., stream: Stream = ...) -> tuple[GpuMat, GpuMat]: ...
    @_typing.overload
    def detectAndComputeAsync(self, image: cv2.UMat, mask: cv2.UMat, keypoints: cv2.UMat | None = ..., descriptors: cv2.UMat | None = ..., useProvidedKeypoints: bool = ..., stream: Stream = ...) -> tuple[cv2.UMat, cv2.UMat]: ...

    @_typing.overload
    def convert(self, gpu_keypoints: cv2.typing.MatLike) -> _typing.Sequence[cv2.KeyPoint]: ...
    @_typing.overload
    def convert(self, gpu_keypoints: GpuMat) -> _typing.Sequence[cv2.KeyPoint]: ...
    @_typing.overload
    def convert(self, gpu_keypoints: cv2.UMat) -> _typing.Sequence[cv2.KeyPoint]: ...


class FastFeatureDetector(Feature2DAsync):
    # Functions
    @classmethod
    def create(cls, threshold: int = ..., nonmaxSuppression: bool = ..., type: int = ..., max_npoints: int = ...) -> FastFeatureDetector: ...

    def setThreshold(self, threshold: int) -> None: ...

    def setMaxNumPoints(self, max_npoints: int) -> None: ...

    def getMaxNumPoints(self) -> int: ...


class ORB(Feature2DAsync):
    # Functions
    @classmethod
    def create(cls, nfeatures: int = ..., scaleFactor: float = ..., nlevels: int = ..., edgeThreshold: int = ..., firstLevel: int = ..., WTA_K: int = ..., scoreType: int = ..., patchSize: int = ..., fastThreshold: int = ..., blurForDescriptor: bool = ...) -> ORB: ...

    def setMaxFeatures(self, maxFeatures: int) -> None: ...

    def getMaxFeatures(self) -> int: ...

    def setScaleFactor(self, scaleFactor: float) -> None: ...

    def getScaleFactor(self) -> float: ...

    def setNLevels(self, nlevels: int) -> None: ...

    def getNLevels(self) -> int: ...

    def setEdgeThreshold(self, edgeThreshold: int) -> None: ...

    def getEdgeThreshold(self) -> int: ...

    def setFirstLevel(self, firstLevel: int) -> None: ...

    def getFirstLevel(self) -> int: ...

    def setWTA_K(self, wta_k: int) -> None: ...

    def getWTA_K(self) -> int: ...

    def setScoreType(self, scoreType: int) -> None: ...

    def getScoreType(self) -> int: ...

    def setPatchSize(self, patchSize: int) -> None: ...

    def getPatchSize(self) -> int: ...

    def setFastThreshold(self, fastThreshold: int) -> None: ...

    def getFastThreshold(self) -> int: ...

    def setBlurForDescriptor(self, blurForDescriptor: bool) -> None: ...

    def getBlurForDescriptor(self) -> bool: ...


class StereoBM(cv2.StereoBM):
    # Functions
    @_typing.overload
    def compute(self, left: cv2.typing.MatLike, right: cv2.typing.MatLike, stream: Stream, disparity: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def compute(self, left: GpuMat, right: GpuMat, stream: Stream, disparity: GpuMat | None = ...) -> GpuMat: ...
    @_typing.overload
    def compute(self, left: cv2.UMat, right: cv2.UMat, stream: Stream, disparity: cv2.UMat | None = ...) -> cv2.UMat: ...


class StereoBeliefPropagation(cv2.StereoMatcher):
    # Functions
    @_typing.overload
    def compute(self, left: cv2.typing.MatLike, right: cv2.typing.MatLike, stream: Stream, disparity: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def compute(self, left: GpuMat, right: GpuMat, stream: Stream, disparity: GpuMat | None = ...) -> GpuMat: ...
    @_typing.overload
    def compute(self, left: cv2.UMat, right: cv2.UMat, stream: Stream, disparity: cv2.UMat | None = ...) -> cv2.UMat: ...
    @_typing.overload
    def compute(self, data: cv2.typing.MatLike, disparity: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def compute(self, data: GpuMat, disparity: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def compute(self, data: cv2.UMat, disparity: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

    def getNumIters(self) -> int: ...

    def setNumIters(self, iters: int) -> None: ...

    def getNumLevels(self) -> int: ...

    def setNumLevels(self, levels: int) -> None: ...

    def getMaxDataTerm(self) -> float: ...

    def setMaxDataTerm(self, max_data_term: float) -> None: ...

    def getDataWeight(self) -> float: ...

    def setDataWeight(self, data_weight: float) -> None: ...

    def getMaxDiscTerm(self) -> float: ...

    def setMaxDiscTerm(self, max_disc_term: float) -> None: ...

    def getDiscSingleJump(self) -> float: ...

    def setDiscSingleJump(self, disc_single_jump: float) -> None: ...

    def getMsgType(self) -> int: ...

    def setMsgType(self, msg_type: int) -> None: ...

    @staticmethod
    def estimateRecommendedParams(width: int, height: int, ndisp: int, iters: int, levels: int) -> None: ...


class StereoConstantSpaceBP(StereoBeliefPropagation):
    # Functions
    def getNrPlane(self) -> int: ...

    def setNrPlane(self, nr_plane: int) -> None: ...

    def getUseLocalInitDataCost(self) -> bool: ...

    def setUseLocalInitDataCost(self, use_local_init_data_cost: bool) -> None: ...

    @staticmethod
    def estimateRecommendedParams(width: int, height: int, ndisp: int, iters: int, levels: int, nr_plane: int) -> None: ...


class StereoSGM(cv2.StereoSGBM):
    # Functions
    @_typing.overload
    def compute(self, left: cv2.typing.MatLike, right: cv2.typing.MatLike, disparity: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def compute(self, left: GpuMat, right: GpuMat, disparity: GpuMat | None = ...) -> GpuMat: ...
    @_typing.overload
    def compute(self, left: cv2.UMat, right: cv2.UMat, disparity: cv2.UMat | None = ...) -> cv2.UMat: ...

    @_typing.overload
    def compute_with_stream(self, left: cv2.typing.MatLike, right: cv2.typing.MatLike, stream: Stream, disparity: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def compute_with_stream(self, left: GpuMat, right: GpuMat, stream: Stream, disparity: GpuMat | None = ...) -> GpuMat: ...
    @_typing.overload
    def compute_with_stream(self, left: cv2.UMat, right: cv2.UMat, stream: Stream, disparity: cv2.UMat | None = ...) -> cv2.UMat: ...


class DisparityBilateralFilter(cv2.Algorithm):
    # Functions
    @_typing.overload
    def apply(self, disparity: cv2.typing.MatLike, image: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def apply(self, disparity: GpuMat, image: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def apply(self, disparity: cv2.UMat, image: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

    def getNumDisparities(self) -> int: ...

    def setNumDisparities(self, numDisparities: int) -> None: ...

    def getRadius(self) -> int: ...

    def setRadius(self, radius: int) -> None: ...

    def getNumIters(self) -> int: ...

    def setNumIters(self, iters: int) -> None: ...

    def getEdgeThreshold(self) -> float: ...

    def setEdgeThreshold(self, edge_threshold: float) -> None: ...

    def getMaxDiscThreshold(self) -> float: ...

    def setMaxDiscThreshold(self, max_disc_threshold: float) -> None: ...

    def getSigmaRange(self) -> float: ...

    def setSigmaRange(self, sigma_range: float) -> None: ...


class SURF_CUDA:
    @property
    def hessianThreshold(self) -> float: ...
    @property
    def nOctaves(self) -> int: ...
    @property
    def nOctaveLayers(self) -> int: ...
    @property
    def extended(self) -> bool: ...
    @property
    def upright(self) -> bool: ...
    @property
    def keypointsRatio(self) -> float: ...

    # Functions
    @classmethod
    def create(cls, _hessianThreshold: float, _nOctaves: int = ..., _nOctaveLayers: int = ..., _extended: bool = ..., _keypointsRatio: float = ..., _upright: bool = ...) -> SURF_CUDA: ...

    def descriptorSize(self) -> int: ...

    def defaultNorm(self) -> int: ...

    def downloadKeypoints(self, keypointsGPU: GpuMat) -> _typing.Sequence[cv2.KeyPoint]: ...

    def detect(self, img: GpuMat, mask: GpuMat, keypoints: GpuMat | None = ...) -> GpuMat: ...

    def detectWithDescriptors(self, img: GpuMat, mask: GpuMat, keypoints: GpuMat | None = ..., descriptors: GpuMat | None = ..., useProvidedKeypoints: bool = ...) -> tuple[GpuMat, GpuMat]: ...


class BackgroundSubtractorMOG(cv2.BackgroundSubtractor):
    # Functions
    @_typing.overload
    def apply(self, image: cv2.typing.MatLike, learningRate: float, stream: Stream, fgmask: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def apply(self, image: GpuMat, learningRate: float, stream: Stream, fgmask: GpuMat | None = ...) -> GpuMat: ...
    @_typing.overload
    def apply(self, image: cv2.UMat, learningRate: float, stream: Stream, fgmask: cv2.UMat | None = ...) -> cv2.UMat: ...

    def getBackgroundImage(self, stream: Stream, backgroundImage: GpuMat | None = ...) -> GpuMat: ...

    def getHistory(self) -> int: ...

    def setHistory(self, nframes: int) -> None: ...

    def getNMixtures(self) -> int: ...

    def setNMixtures(self, nmix: int) -> None: ...

    def getBackgroundRatio(self) -> float: ...

    def setBackgroundRatio(self, backgroundRatio: float) -> None: ...

    def getNoiseSigma(self) -> float: ...

    def setNoiseSigma(self, noiseSigma: float) -> None: ...


class BackgroundSubtractorMOG2(cv2.BackgroundSubtractorMOG2):
    # Functions
    @_typing.overload
    def apply(self, image: cv2.typing.MatLike, learningRate: float, stream: Stream, fgmask: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def apply(self, image: GpuMat, learningRate: float, stream: Stream, fgmask: GpuMat | None = ...) -> GpuMat: ...
    @_typing.overload
    def apply(self, image: cv2.UMat, learningRate: float, stream: Stream, fgmask: cv2.UMat | None = ...) -> cv2.UMat: ...

    def getBackgroundImage(self, stream: Stream, backgroundImage: GpuMat | None = ...) -> GpuMat: ...


class HOG(cv2.Algorithm):
    # Functions
    @classmethod
    def create(cls, win_size: cv2.typing.Size = ..., block_size: cv2.typing.Size = ..., block_stride: cv2.typing.Size = ..., cell_size: cv2.typing.Size = ..., nbins: int = ...) -> HOG: ...

    def setWinSigma(self, win_sigma: float) -> None: ...

    def getWinSigma(self) -> float: ...

    def setL2HysThreshold(self, threshold_L2hys: float) -> None: ...

    def getL2HysThreshold(self) -> float: ...

    def setGammaCorrection(self, gamma_correction: bool) -> None: ...

    def getGammaCorrection(self) -> bool: ...

    def setNumLevels(self, nlevels: int) -> None: ...

    def getNumLevels(self) -> int: ...

    def setHitThreshold(self, hit_threshold: float) -> None: ...

    def getHitThreshold(self) -> float: ...

    def setWinStride(self, win_stride: cv2.typing.Size) -> None: ...

    def getWinStride(self) -> cv2.typing.Size: ...

    def setScaleFactor(self, scale0: float) -> None: ...

    def getScaleFactor(self) -> float: ...

    def setGroupThreshold(self, group_threshold: int) -> None: ...

    def getGroupThreshold(self) -> int: ...

    def setDescriptorFormat(self, descr_format: cv2.HOGDescriptor_DescriptorStorageFormat) -> None: ...

    def getDescriptorFormat(self) -> cv2.HOGDescriptor_DescriptorStorageFormat: ...

    def getDescriptorSize(self) -> int: ...

    def getBlockHistogramSize(self) -> int: ...

    @_typing.overload
    def setSVMDetector(self, detector: cv2.typing.MatLike) -> None: ...
    @_typing.overload
    def setSVMDetector(self, detector: GpuMat) -> None: ...
    @_typing.overload
    def setSVMDetector(self, detector: cv2.UMat) -> None: ...

    def getDefaultPeopleDetector(self) -> cv2.typing.MatLike: ...

    @_typing.overload
    def detect(self, img: cv2.typing.MatLike) -> tuple[_typing.Sequence[cv2.typing.Point], _typing.Sequence[float]]: ...
    @_typing.overload
    def detect(self, img: GpuMat) -> tuple[_typing.Sequence[cv2.typing.Point], _typing.Sequence[float]]: ...
    @_typing.overload
    def detect(self, img: cv2.UMat) -> tuple[_typing.Sequence[cv2.typing.Point], _typing.Sequence[float]]: ...

    @_typing.overload
    def detectWithoutConf(self, img: cv2.typing.MatLike) -> _typing.Sequence[cv2.typing.Point]: ...
    @_typing.overload
    def detectWithoutConf(self, img: GpuMat) -> _typing.Sequence[cv2.typing.Point]: ...
    @_typing.overload
    def detectWithoutConf(self, img: cv2.UMat) -> _typing.Sequence[cv2.typing.Point]: ...

    @_typing.overload
    def detectMultiScale(self, img: cv2.typing.MatLike) -> tuple[_typing.Sequence[cv2.typing.Rect], _typing.Sequence[float]]: ...
    @_typing.overload
    def detectMultiScale(self, img: GpuMat) -> tuple[_typing.Sequence[cv2.typing.Rect], _typing.Sequence[float]]: ...
    @_typing.overload
    def detectMultiScale(self, img: cv2.UMat) -> tuple[_typing.Sequence[cv2.typing.Rect], _typing.Sequence[float]]: ...

    @_typing.overload
    def detectMultiScaleWithoutConf(self, img: cv2.typing.MatLike) -> _typing.Sequence[cv2.typing.Rect]: ...
    @_typing.overload
    def detectMultiScaleWithoutConf(self, img: GpuMat) -> _typing.Sequence[cv2.typing.Rect]: ...
    @_typing.overload
    def detectMultiScaleWithoutConf(self, img: cv2.UMat) -> _typing.Sequence[cv2.typing.Rect]: ...

    @_typing.overload
    def compute(self, img: cv2.typing.MatLike, descriptors: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def compute(self, img: GpuMat, descriptors: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def compute(self, img: cv2.UMat, descriptors: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...


class CascadeClassifier(cv2.Algorithm):
    # Functions
    @classmethod
    def create(cls, filename: str) -> CascadeClassifier: ...

    def setMaxObjectSize(self, maxObjectSize: cv2.typing.Size) -> None: ...

    def getMaxObjectSize(self) -> cv2.typing.Size: ...

    def setMinObjectSize(self, minSize: cv2.typing.Size) -> None: ...

    def getMinObjectSize(self) -> cv2.typing.Size: ...

    def setScaleFactor(self, scaleFactor: float) -> None: ...

    def getScaleFactor(self) -> float: ...

    def setMinNeighbors(self, minNeighbors: int) -> None: ...

    def getMinNeighbors(self) -> int: ...

    def setFindLargestObject(self, findLargestObject: bool) -> None: ...

    def getFindLargestObject(self) -> bool: ...

    def setMaxNumObjects(self, maxNumObjects: int) -> None: ...

    def getMaxNumObjects(self) -> int: ...

    def getClassifierSize(self) -> cv2.typing.Size: ...

    @_typing.overload
    def detectMultiScale(self, image: cv2.typing.MatLike, objects: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def detectMultiScale(self, image: GpuMat, objects: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def detectMultiScale(self, image: cv2.UMat, objects: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

    @_typing.overload
    def convert(self, objects: _typing.Sequence[cv2.typing.Rect], gpu_objects: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def convert(self, objects: _typing.Sequence[cv2.typing.Rect], gpu_objects: GpuMat | None = ...) -> GpuMat: ...
    @_typing.overload
    def convert(self, objects: _typing.Sequence[cv2.typing.Rect], gpu_objects: cv2.UMat | None = ...) -> cv2.UMat: ...


class DenseOpticalFlow(cv2.Algorithm):
    # Functions
    @_typing.overload
    def calc(self, I0: cv2.typing.MatLike, I1: cv2.typing.MatLike, flow: cv2.typing.MatLike, stream: Stream = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def calc(self, I0: GpuMat, I1: GpuMat, flow: GpuMat, stream: Stream = ...) -> GpuMat: ...
    @_typing.overload
    def calc(self, I0: cv2.UMat, I1: cv2.UMat, flow: cv2.UMat, stream: Stream = ...) -> cv2.UMat: ...


class SparseOpticalFlow(cv2.Algorithm):
    # Functions
    @_typing.overload
    def calc(self, prevImg: cv2.typing.MatLike, nextImg: cv2.typing.MatLike, prevPts: cv2.typing.MatLike, nextPts: cv2.typing.MatLike, status: cv2.typing.MatLike | None = ..., err: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> tuple[cv2.typing.MatLike, cv2.typing.MatLike, cv2.typing.MatLike]: ...
    @_typing.overload
    def calc(self, prevImg: GpuMat, nextImg: GpuMat, prevPts: GpuMat, nextPts: GpuMat, status: GpuMat | None = ..., err: GpuMat | None = ..., stream: Stream = ...) -> tuple[GpuMat, GpuMat, GpuMat]: ...
    @_typing.overload
    def calc(self, prevImg: cv2.UMat, nextImg: cv2.UMat, prevPts: cv2.UMat, nextPts: cv2.UMat, status: cv2.UMat | None = ..., err: cv2.UMat | None = ..., stream: Stream = ...) -> tuple[cv2.UMat, cv2.UMat, cv2.UMat]: ...


class NvidiaHWOpticalFlow(cv2.Algorithm):
    # Functions
    @_typing.overload
    def calc(self, inputImage: cv2.typing.MatLike, referenceImage: cv2.typing.MatLike, flow: cv2.typing.MatLike, stream: Stream = ..., hint: cv2.typing.MatLike | None = ..., cost: cv2.typing.MatLike | None = ...) -> tuple[cv2.typing.MatLike, cv2.typing.MatLike]: ...
    @_typing.overload
    def calc(self, inputImage: GpuMat, referenceImage: GpuMat, flow: GpuMat, stream: Stream = ..., hint: GpuMat | None = ..., cost: GpuMat | None = ...) -> tuple[GpuMat, GpuMat]: ...
    @_typing.overload
    def calc(self, inputImage: cv2.UMat, referenceImage: cv2.UMat, flow: cv2.UMat, stream: Stream = ..., hint: cv2.UMat | None = ..., cost: cv2.UMat | None = ...) -> tuple[cv2.UMat, cv2.UMat]: ...

    def collectGarbage(self) -> None: ...

    def getGridSize(self) -> int: ...


class BroxOpticalFlow(DenseOpticalFlow):
    # Functions
    def getFlowSmoothness(self) -> float: ...

    def setFlowSmoothness(self, alpha: float) -> None: ...

    def getGradientConstancyImportance(self) -> float: ...

    def setGradientConstancyImportance(self, gamma: float) -> None: ...

    def getPyramidScaleFactor(self) -> float: ...

    def setPyramidScaleFactor(self, scale_factor: float) -> None: ...

    def getInnerIterations(self) -> int: ...

    def setInnerIterations(self, inner_iterations: int) -> None: ...

    def getOuterIterations(self) -> int: ...

    def setOuterIterations(self, outer_iterations: int) -> None: ...

    def getSolverIterations(self) -> int: ...

    def setSolverIterations(self, solver_iterations: int) -> None: ...

    @classmethod
    def create(cls, alpha: float = ..., gamma: float = ..., scale_factor: float = ..., inner_iterations: int = ..., outer_iterations: int = ..., solver_iterations: int = ...) -> BroxOpticalFlow: ...


class SparsePyrLKOpticalFlow(SparseOpticalFlow):
    # Functions
    def getWinSize(self) -> cv2.typing.Size: ...

    def setWinSize(self, winSize: cv2.typing.Size) -> None: ...

    def getMaxLevel(self) -> int: ...

    def setMaxLevel(self, maxLevel: int) -> None: ...

    def getNumIters(self) -> int: ...

    def setNumIters(self, iters: int) -> None: ...

    def getUseInitialFlow(self) -> bool: ...

    def setUseInitialFlow(self, useInitialFlow: bool) -> None: ...

    @classmethod
    def create(cls, winSize: cv2.typing.Size = ..., maxLevel: int = ..., iters: int = ..., useInitialFlow: bool = ...) -> SparsePyrLKOpticalFlow: ...


class DensePyrLKOpticalFlow(DenseOpticalFlow):
    # Functions
    def getWinSize(self) -> cv2.typing.Size: ...

    def setWinSize(self, winSize: cv2.typing.Size) -> None: ...

    def getMaxLevel(self) -> int: ...

    def setMaxLevel(self, maxLevel: int) -> None: ...

    def getNumIters(self) -> int: ...

    def setNumIters(self, iters: int) -> None: ...

    def getUseInitialFlow(self) -> bool: ...

    def setUseInitialFlow(self, useInitialFlow: bool) -> None: ...

    @classmethod
    def create(cls, winSize: cv2.typing.Size = ..., maxLevel: int = ..., iters: int = ..., useInitialFlow: bool = ...) -> DensePyrLKOpticalFlow: ...


class FarnebackOpticalFlow(DenseOpticalFlow):
    # Functions
    def getNumLevels(self) -> int: ...

    def setNumLevels(self, numLevels: int) -> None: ...

    def getPyrScale(self) -> float: ...

    def setPyrScale(self, pyrScale: float) -> None: ...

    def getFastPyramids(self) -> bool: ...

    def setFastPyramids(self, fastPyramids: bool) -> None: ...

    def getWinSize(self) -> int: ...

    def setWinSize(self, winSize: int) -> None: ...

    def getNumIters(self) -> int: ...

    def setNumIters(self, numIters: int) -> None: ...

    def getPolyN(self) -> int: ...

    def setPolyN(self, polyN: int) -> None: ...

    def getPolySigma(self) -> float: ...

    def setPolySigma(self, polySigma: float) -> None: ...

    def getFlags(self) -> int: ...

    def setFlags(self, flags: int) -> None: ...

    @classmethod
    def create(cls, numLevels: int = ..., pyrScale: float = ..., fastPyramids: bool = ..., winSize: int = ..., numIters: int = ..., polyN: int = ..., polySigma: float = ..., flags: int = ...) -> FarnebackOpticalFlow: ...


class OpticalFlowDual_TVL1(DenseOpticalFlow):
    # Functions
    def getTau(self) -> float: ...

    def setTau(self, tau: float) -> None: ...

    def getLambda(self) -> float: ...

    def setLambda(self, lambda_: float) -> None: ...

    def getGamma(self) -> float: ...

    def setGamma(self, gamma: float) -> None: ...

    def getTheta(self) -> float: ...

    def setTheta(self, theta: float) -> None: ...

    def getNumScales(self) -> int: ...

    def setNumScales(self, nscales: int) -> None: ...

    def getNumWarps(self) -> int: ...

    def setNumWarps(self, warps: int) -> None: ...

    def getEpsilon(self) -> float: ...

    def setEpsilon(self, epsilon: float) -> None: ...

    def getNumIterations(self) -> int: ...

    def setNumIterations(self, iterations: int) -> None: ...

    def getScaleStep(self) -> float: ...

    def setScaleStep(self, scaleStep: float) -> None: ...

    def getUseInitialFlow(self) -> bool: ...

    def setUseInitialFlow(self, useInitialFlow: bool) -> None: ...

    @classmethod
    def create(cls, tau: float = ..., lambda_: float = ..., theta: float = ..., nscales: int = ..., warps: int = ..., epsilon: float = ..., iterations: int = ..., scaleStep: float = ..., gamma: float = ..., useInitialFlow: bool = ...) -> OpticalFlowDual_TVL1: ...


class NvidiaOpticalFlow_1_0(NvidiaHWOpticalFlow):
    # Functions
    @_typing.overload
    def upSampler(self, flow: cv2.typing.MatLike, imageSize: cv2.typing.Size, gridSize: int, upsampledFlow: cv2.typing.MatLike) -> cv2.typing.MatLike: ...
    @_typing.overload
    def upSampler(self, flow: GpuMat, imageSize: cv2.typing.Size, gridSize: int, upsampledFlow: GpuMat) -> GpuMat: ...
    @_typing.overload
    def upSampler(self, flow: cv2.UMat, imageSize: cv2.typing.Size, gridSize: int, upsampledFlow: cv2.UMat) -> cv2.UMat: ...

    @classmethod
    def create(cls, imageSize: cv2.typing.Size, perfPreset: NvidiaOpticalFlow_1_0_NVIDIA_OF_PERF_LEVEL = ..., enableTemporalHints: bool = ..., enableExternalHints: bool = ..., enableCostBuffer: bool = ..., gpuId: int = ..., inputStream: Stream = ..., outputStream: Stream = ...) -> NvidiaOpticalFlow_1_0: ...


class NvidiaOpticalFlow_2_0(NvidiaHWOpticalFlow):
    # Functions
    @_typing.overload
    def convertToFloat(self, flow: cv2.typing.MatLike, floatFlow: cv2.typing.MatLike) -> cv2.typing.MatLike: ...
    @_typing.overload
    def convertToFloat(self, flow: GpuMat, floatFlow: GpuMat) -> GpuMat: ...
    @_typing.overload
    def convertToFloat(self, flow: cv2.UMat, floatFlow: cv2.UMat) -> cv2.UMat: ...

    @classmethod
    @_typing.overload
    def create(cls, imageSize: cv2.typing.Size, perfPreset: NvidiaOpticalFlow_2_0_NVIDIA_OF_PERF_LEVEL = ..., outputGridSize: NvidiaOpticalFlow_2_0_NVIDIA_OF_OUTPUT_VECTOR_GRID_SIZE = ..., hintGridSize: NvidiaOpticalFlow_2_0_NVIDIA_OF_HINT_VECTOR_GRID_SIZE = ..., enableTemporalHints: bool = ..., enableExternalHints: bool = ..., enableCostBuffer: bool = ..., gpuId: int = ..., inputStream: Stream = ..., outputStream: Stream = ...) -> NvidiaOpticalFlow_2_0: ...
    @classmethod
    @_typing.overload
    def create(cls, imageSize: cv2.typing.Size, roiData: _typing.Sequence[cv2.typing.Rect], perfPreset: NvidiaOpticalFlow_2_0_NVIDIA_OF_PERF_LEVEL = ..., outputGridSize: NvidiaOpticalFlow_2_0_NVIDIA_OF_OUTPUT_VECTOR_GRID_SIZE = ..., hintGridSize: NvidiaOpticalFlow_2_0_NVIDIA_OF_HINT_VECTOR_GRID_SIZE = ..., enableTemporalHints: bool = ..., enableExternalHints: bool = ..., enableCostBuffer: bool = ..., gpuId: int = ..., inputStream: Stream = ..., outputStream: Stream = ...) -> NvidiaOpticalFlow_2_0: ...



# Functions
@_typing.overload
def abs(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def abs(src: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def abs(src: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def absSum(src: cv2.typing.MatLike, mask: cv2.typing.MatLike | None = ...) -> cv2.typing.Scalar: ...
@_typing.overload
def absSum(src: GpuMat, mask: GpuMat | None = ...) -> cv2.typing.Scalar: ...
@_typing.overload
def absSum(src: cv2.UMat, mask: cv2.UMat | None = ...) -> cv2.typing.Scalar: ...

@_typing.overload
def absdiff(src1: cv2.typing.MatLike, src2: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def absdiff(src1: GpuMat, src2: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def absdiff(src1: cv2.UMat, src2: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def add(src1: cv2.typing.MatLike, src2: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., dtype: int = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def add(src1: GpuMat, src2: GpuMat, dst: GpuMat | None = ..., mask: GpuMat | None = ..., dtype: int = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def add(src1: cv2.UMat, src2: cv2.UMat, dst: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., dtype: int = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def addWeighted(src1: cv2.typing.MatLike, alpha: float, src2: cv2.typing.MatLike, beta: float, gamma: float, dst: cv2.typing.MatLike | None = ..., dtype: int = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def addWeighted(src1: GpuMat, alpha: float, src2: GpuMat, beta: float, gamma: float, dst: GpuMat | None = ..., dtype: int = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def addWeighted(src1: cv2.UMat, alpha: float, src2: cv2.UMat, beta: float, gamma: float, dst: cv2.UMat | None = ..., dtype: int = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def alphaComp(img1: cv2.typing.MatLike, img2: cv2.typing.MatLike, alpha_op: int, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def alphaComp(img1: GpuMat, img2: GpuMat, alpha_op: int, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def alphaComp(img1: cv2.UMat, img2: cv2.UMat, alpha_op: int, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def bilateralFilter(src: cv2.typing.MatLike, kernel_size: int, sigma_color: float, sigma_spatial: float, dst: cv2.typing.MatLike | None = ..., borderMode: int = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def bilateralFilter(src: GpuMat, kernel_size: int, sigma_color: float, sigma_spatial: float, dst: GpuMat | None = ..., borderMode: int = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def bilateralFilter(src: cv2.UMat, kernel_size: int, sigma_color: float, sigma_spatial: float, dst: cv2.UMat | None = ..., borderMode: int = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def bitwise_and(src1: cv2.typing.MatLike, src2: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def bitwise_and(src1: GpuMat, src2: GpuMat, dst: GpuMat | None = ..., mask: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def bitwise_and(src1: cv2.UMat, src2: cv2.UMat, dst: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def bitwise_not(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def bitwise_not(src: GpuMat, dst: GpuMat | None = ..., mask: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def bitwise_not(src: cv2.UMat, dst: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def bitwise_or(src1: cv2.typing.MatLike, src2: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def bitwise_or(src1: GpuMat, src2: GpuMat, dst: GpuMat | None = ..., mask: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def bitwise_or(src1: cv2.UMat, src2: cv2.UMat, dst: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def bitwise_xor(src1: cv2.typing.MatLike, src2: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def bitwise_xor(src1: GpuMat, src2: GpuMat, dst: GpuMat | None = ..., mask: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def bitwise_xor(src1: cv2.UMat, src2: cv2.UMat, dst: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def blendLinear(img1: cv2.typing.MatLike, img2: cv2.typing.MatLike, weights1: cv2.typing.MatLike, weights2: cv2.typing.MatLike, result: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def blendLinear(img1: GpuMat, img2: GpuMat, weights1: GpuMat, weights2: GpuMat, result: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def blendLinear(img1: cv2.UMat, img2: cv2.UMat, weights1: cv2.UMat, weights2: cv2.UMat, result: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def buildWarpAffineMaps(M: cv2.UMat, inverse: bool, dsize: cv2.typing.Size, xmap: GpuMat | None = ..., ymap: GpuMat | None = ..., stream: Stream = ...) -> tuple[GpuMat, GpuMat]: ...
@_typing.overload
def buildWarpAffineMaps(M: cv2.typing.MatLike, inverse: bool, dsize: cv2.typing.Size, xmap: GpuMat | None = ..., ymap: GpuMat | None = ..., stream: Stream = ...) -> tuple[GpuMat, GpuMat]: ...

@_typing.overload
def buildWarpPerspectiveMaps(M: cv2.UMat, inverse: bool, dsize: cv2.typing.Size, xmap: GpuMat | None = ..., ymap: GpuMat | None = ..., stream: Stream = ...) -> tuple[GpuMat, GpuMat]: ...
@_typing.overload
def buildWarpPerspectiveMaps(M: cv2.typing.MatLike, inverse: bool, dsize: cv2.typing.Size, xmap: GpuMat | None = ..., ymap: GpuMat | None = ..., stream: Stream = ...) -> tuple[GpuMat, GpuMat]: ...

@_typing.overload
def calcAbsSum(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def calcAbsSum(src: GpuMat, dst: GpuMat | None = ..., mask: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def calcAbsSum(src: cv2.UMat, dst: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def calcHist(src: cv2.typing.MatLike, hist: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def calcHist(src: GpuMat, hist: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def calcHist(src: cv2.UMat, hist: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...
@_typing.overload
def calcHist(src: cv2.typing.MatLike, mask: cv2.typing.MatLike, hist: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def calcHist(src: GpuMat, mask: GpuMat, hist: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def calcHist(src: cv2.UMat, mask: cv2.UMat, hist: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def calcNorm(src: cv2.typing.MatLike, normType: int, dst: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def calcNorm(src: GpuMat, normType: int, dst: GpuMat | None = ..., mask: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def calcNorm(src: cv2.UMat, normType: int, dst: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def calcNormDiff(src1: cv2.typing.MatLike, src2: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., normType: int = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def calcNormDiff(src1: GpuMat, src2: GpuMat, dst: GpuMat | None = ..., normType: int = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def calcNormDiff(src1: cv2.UMat, src2: cv2.UMat, dst: cv2.UMat | None = ..., normType: int = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def calcSqrSum(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def calcSqrSum(src: GpuMat, dst: GpuMat | None = ..., mask: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def calcSqrSum(src: cv2.UMat, dst: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def calcSum(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def calcSum(src: GpuMat, dst: GpuMat | None = ..., mask: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def calcSum(src: cv2.UMat, dst: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def cartToPolar(x: cv2.typing.MatLike, y: cv2.typing.MatLike, magnitude: cv2.typing.MatLike | None = ..., angle: cv2.typing.MatLike | None = ..., angleInDegrees: bool = ..., stream: Stream = ...) -> tuple[cv2.typing.MatLike, cv2.typing.MatLike]: ...
@_typing.overload
def cartToPolar(x: GpuMat, y: GpuMat, magnitude: GpuMat | None = ..., angle: GpuMat | None = ..., angleInDegrees: bool = ..., stream: Stream = ...) -> tuple[GpuMat, GpuMat]: ...
@_typing.overload
def cartToPolar(x: cv2.UMat, y: cv2.UMat, magnitude: cv2.UMat | None = ..., angle: cv2.UMat | None = ..., angleInDegrees: bool = ..., stream: Stream = ...) -> tuple[cv2.UMat, cv2.UMat]: ...

@_typing.overload
def compare(src1: cv2.typing.MatLike, src2: cv2.typing.MatLike, cmpop: int, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def compare(src1: GpuMat, src2: GpuMat, cmpop: int, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def compare(src1: cv2.UMat, src2: cv2.UMat, cmpop: int, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def connectedComponents(image: cv2.typing.MatLike, labels: cv2.typing.MatLike | None = ..., connectivity: int = ..., ltype: int = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def connectedComponents(image: GpuMat, labels: GpuMat | None = ..., connectivity: int = ..., ltype: int = ...) -> GpuMat: ...
@_typing.overload
def connectedComponents(image: cv2.UMat, labels: cv2.UMat | None = ..., connectivity: int = ..., ltype: int = ...) -> cv2.UMat: ...

@_typing.overload
def connectedComponentsWithAlgorithm(image: cv2.typing.MatLike, connectivity: int, ltype: int, ccltype: ConnectedComponentsAlgorithmsTypes, labels: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def connectedComponentsWithAlgorithm(image: GpuMat, connectivity: int, ltype: int, ccltype: ConnectedComponentsAlgorithmsTypes, labels: GpuMat | None = ...) -> GpuMat: ...
@_typing.overload
def connectedComponentsWithAlgorithm(image: cv2.UMat, connectivity: int, ltype: int, ccltype: ConnectedComponentsAlgorithmsTypes, labels: cv2.UMat | None = ...) -> cv2.UMat: ...

def convertSpatialMoments(spatialMoments: cv2.typing.MatLike, order: MomentsOrder, momentsType: int) -> cv2.typing.Moments: ...

@_typing.overload
def copyMakeBorder(src: cv2.typing.MatLike, top: int, bottom: int, left: int, right: int, borderType: int, dst: cv2.typing.MatLike | None = ..., value: cv2.typing.Scalar = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def copyMakeBorder(src: GpuMat, top: int, bottom: int, left: int, right: int, borderType: int, dst: GpuMat | None = ..., value: cv2.typing.Scalar = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def copyMakeBorder(src: cv2.UMat, top: int, bottom: int, left: int, right: int, borderType: int, dst: cv2.UMat | None = ..., value: cv2.typing.Scalar = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def countNonZero(src: cv2.typing.MatLike) -> int: ...
@_typing.overload
def countNonZero(src: GpuMat) -> int: ...
@_typing.overload
def countNonZero(src: cv2.UMat) -> int: ...
@_typing.overload
def countNonZero(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def countNonZero(src: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def countNonZero(src: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

def createBackgroundSubtractorMOG(history: int = ..., nmixtures: int = ..., backgroundRatio: float = ..., noiseSigma: float = ...) -> BackgroundSubtractorMOG: ...

def createBackgroundSubtractorMOG2(history: int = ..., varThreshold: float = ..., detectShadows: bool = ...) -> BackgroundSubtractorMOG2: ...

def createBoxFilter(srcType: int, dstType: int, ksize: cv2.typing.Size, anchor: cv2.typing.Point = ..., borderMode: int = ..., borderVal: cv2.typing.Scalar = ...) -> Filter: ...

def createBoxMaxFilter(srcType: int, ksize: cv2.typing.Size, anchor: cv2.typing.Point = ..., borderMode: int = ..., borderVal: cv2.typing.Scalar = ...) -> Filter: ...

def createBoxMinFilter(srcType: int, ksize: cv2.typing.Size, anchor: cv2.typing.Point = ..., borderMode: int = ..., borderVal: cv2.typing.Scalar = ...) -> Filter: ...

def createCLAHE(clipLimit: float = ..., tileGridSize: cv2.typing.Size = ...) -> CLAHE: ...

def createCannyEdgeDetector(low_thresh: float, high_thresh: float, apperture_size: int = ..., L2gradient: bool = ...) -> CannyEdgeDetector: ...

def createColumnSumFilter(srcType: int, dstType: int, ksize: int, anchor: int = ..., borderMode: int = ..., borderVal: cv2.typing.Scalar = ...) -> Filter: ...

@_typing.overload
def createContinuous(rows: int, cols: int, type: int, arr: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def createContinuous(rows: int, cols: int, type: int, arr: GpuMat | None = ...) -> GpuMat: ...
@_typing.overload
def createContinuous(rows: int, cols: int, type: int, arr: cv2.UMat | None = ...) -> cv2.UMat: ...

def createConvolution(user_block_size: cv2.typing.Size = ...) -> Convolution: ...

def createDFT(dft_size: cv2.typing.Size, flags: int) -> DFT: ...

def createDerivFilter(srcType: int, dstType: int, dx: int, dy: int, ksize: int, normalize: bool = ..., scale: float = ..., rowBorderMode: int = ..., columnBorderMode: int = ...) -> Filter: ...

def createDisparityBilateralFilter(ndisp: int = ..., radius: int = ..., iters: int = ...) -> DisparityBilateralFilter: ...

def createGaussianFilter(srcType: int, dstType: int, ksize: cv2.typing.Size, sigma1: float, sigma2: float = ..., rowBorderMode: int = ..., columnBorderMode: int = ...) -> Filter: ...

def createGeneralizedHoughBallard() -> cv2.GeneralizedHoughBallard: ...

def createGeneralizedHoughGuil() -> cv2.GeneralizedHoughGuil: ...

def createGoodFeaturesToTrackDetector(srcType: int, maxCorners: int = ..., qualityLevel: float = ..., minDistance: float = ..., blockSize: int = ..., useHarrisDetector: bool = ..., harrisK: float = ...) -> CornersDetector: ...

@_typing.overload
def createGpuMatFromCudaMemory(rows: int, cols: int, type: int, cudaMemoryAddress: int, step: int = ...) -> GpuMat: ...
@_typing.overload
def createGpuMatFromCudaMemory(size: cv2.typing.Size, type: int, cudaMemoryAddress: int, step: int = ...) -> GpuMat: ...

def createHarrisCorner(srcType: int, blockSize: int, ksize: int, k: float, borderType: int = ...) -> CornernessCriteria: ...

def createHoughCirclesDetector(dp: float, minDist: float, cannyThreshold: int, votesThreshold: int, minRadius: int, maxRadius: int, maxCircles: int = ...) -> HoughCirclesDetector: ...

def createHoughLinesDetector(rho: float, theta: float, threshold: int, doSort: bool = ..., maxLines: int = ...) -> HoughLinesDetector: ...

def createHoughSegmentDetector(rho: float, theta: float, minLineLength: int, maxLineGap: int, maxLines: int = ..., threshold: int = ...) -> HoughSegmentDetector: ...

def createLaplacianFilter(srcType: int, dstType: int, ksize: int = ..., scale: float = ..., borderMode: int = ..., borderVal: cv2.typing.Scalar = ...) -> Filter: ...

@_typing.overload
def createLinearFilter(srcType: int, dstType: int, kernel: cv2.typing.MatLike, anchor: cv2.typing.Point = ..., borderMode: int = ..., borderVal: cv2.typing.Scalar = ...) -> Filter: ...
@_typing.overload
def createLinearFilter(srcType: int, dstType: int, kernel: GpuMat, anchor: cv2.typing.Point = ..., borderMode: int = ..., borderVal: cv2.typing.Scalar = ...) -> Filter: ...
@_typing.overload
def createLinearFilter(srcType: int, dstType: int, kernel: cv2.UMat, anchor: cv2.typing.Point = ..., borderMode: int = ..., borderVal: cv2.typing.Scalar = ...) -> Filter: ...

@_typing.overload
def createLookUpTable(lut: cv2.typing.MatLike) -> LookUpTable: ...
@_typing.overload
def createLookUpTable(lut: GpuMat) -> LookUpTable: ...
@_typing.overload
def createLookUpTable(lut: cv2.UMat) -> LookUpTable: ...

def createMedianFilter(srcType: int, windowSize: int, partition: int = ...) -> Filter: ...

def createMinEigenValCorner(srcType: int, blockSize: int, ksize: int, borderType: int = ...) -> CornernessCriteria: ...

@_typing.overload
def createMorphologyFilter(op: int, srcType: int, kernel: cv2.typing.MatLike, anchor: cv2.typing.Point = ..., iterations: int = ...) -> Filter: ...
@_typing.overload
def createMorphologyFilter(op: int, srcType: int, kernel: GpuMat, anchor: cv2.typing.Point = ..., iterations: int = ...) -> Filter: ...
@_typing.overload
def createMorphologyFilter(op: int, srcType: int, kernel: cv2.UMat, anchor: cv2.typing.Point = ..., iterations: int = ...) -> Filter: ...

def createRowSumFilter(srcType: int, dstType: int, ksize: int, anchor: int = ..., borderMode: int = ..., borderVal: cv2.typing.Scalar = ...) -> Filter: ...

def createScharrFilter(srcType: int, dstType: int, dx: int, dy: int, scale: float = ..., rowBorderMode: int = ..., columnBorderMode: int = ...) -> Filter: ...

@_typing.overload
def createSeparableLinearFilter(srcType: int, dstType: int, rowKernel: cv2.typing.MatLike, columnKernel: cv2.typing.MatLike, anchor: cv2.typing.Point = ..., rowBorderMode: int = ..., columnBorderMode: int = ...) -> Filter: ...
@_typing.overload
def createSeparableLinearFilter(srcType: int, dstType: int, rowKernel: GpuMat, columnKernel: GpuMat, anchor: cv2.typing.Point = ..., rowBorderMode: int = ..., columnBorderMode: int = ...) -> Filter: ...
@_typing.overload
def createSeparableLinearFilter(srcType: int, dstType: int, rowKernel: cv2.UMat, columnKernel: cv2.UMat, anchor: cv2.typing.Point = ..., rowBorderMode: int = ..., columnBorderMode: int = ...) -> Filter: ...

def createSobelFilter(srcType: int, dstType: int, dx: int, dy: int, ksize: int = ..., scale: float = ..., rowBorderMode: int = ..., columnBorderMode: int = ...) -> Filter: ...

def createStereoBM(numDisparities: int = ..., blockSize: int = ...) -> StereoBM: ...

def createStereoBeliefPropagation(ndisp: int = ..., iters: int = ..., levels: int = ..., msg_type: int = ...) -> StereoBeliefPropagation: ...

def createStereoConstantSpaceBP(ndisp: int = ..., iters: int = ..., levels: int = ..., nr_plane: int = ..., msg_type: int = ...) -> StereoConstantSpaceBP: ...

def createStereoSGM(minDisparity: int = ..., numDisparities: int = ..., P1: int = ..., P2: int = ..., uniquenessRatio: int = ..., mode: int = ...) -> StereoSGM: ...

def createTemplateMatching(srcType: int, method: int, user_block_size: cv2.typing.Size = ...) -> TemplateMatching: ...

@_typing.overload
def cvtColor(src: cv2.typing.MatLike, code: int, dst: cv2.typing.MatLike | None = ..., dcn: int = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def cvtColor(src: GpuMat, code: int, dst: GpuMat | None = ..., dcn: int = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def cvtColor(src: cv2.UMat, code: int, dst: cv2.UMat | None = ..., dcn: int = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def demosaicing(src: cv2.typing.MatLike, code: int, dst: cv2.typing.MatLike | None = ..., dcn: int = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def demosaicing(src: GpuMat, code: int, dst: GpuMat | None = ..., dcn: int = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def demosaicing(src: cv2.UMat, code: int, dst: cv2.UMat | None = ..., dcn: int = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def dft(src: cv2.typing.MatLike, dft_size: cv2.typing.Size, dst: cv2.typing.MatLike | None = ..., flags: int = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def dft(src: GpuMat, dft_size: cv2.typing.Size, dst: GpuMat | None = ..., flags: int = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def dft(src: cv2.UMat, dft_size: cv2.typing.Size, dst: cv2.UMat | None = ..., flags: int = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def divide(src1: cv2.typing.MatLike, src2: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., scale: float = ..., dtype: int = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def divide(src1: GpuMat, src2: GpuMat, dst: GpuMat | None = ..., scale: float = ..., dtype: int = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def divide(src1: cv2.UMat, src2: cv2.UMat, dst: cv2.UMat | None = ..., scale: float = ..., dtype: int = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def drawColorDisp(src_disp: cv2.typing.MatLike, ndisp: int, dst_disp: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def drawColorDisp(src_disp: GpuMat, ndisp: int, dst_disp: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def drawColorDisp(src_disp: cv2.UMat, ndisp: int, dst_disp: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def ensureSizeIsEnough(rows: int, cols: int, type: int, arr: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def ensureSizeIsEnough(rows: int, cols: int, type: int, arr: GpuMat | None = ...) -> GpuMat: ...
@_typing.overload
def ensureSizeIsEnough(rows: int, cols: int, type: int, arr: cv2.UMat | None = ...) -> cv2.UMat: ...

@_typing.overload
def equalizeHist(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def equalizeHist(src: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def equalizeHist(src: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def evenLevels(nLevels: int, lowerLevel: int, upperLevel: int, levels: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def evenLevels(nLevels: int, lowerLevel: int, upperLevel: int, levels: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def evenLevels(nLevels: int, lowerLevel: int, upperLevel: int, levels: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def exp(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def exp(src: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def exp(src: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

def fastNlMeansDenoising(src: GpuMat, h: float, dst: GpuMat | None = ..., search_window: int = ..., block_size: int = ..., stream: Stream = ...) -> GpuMat: ...

def fastNlMeansDenoisingColored(src: GpuMat, h_luminance: float, photo_render: float, dst: GpuMat | None = ..., search_window: int = ..., block_size: int = ..., stream: Stream = ...) -> GpuMat: ...

@_typing.overload
def findMinMax(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def findMinMax(src: GpuMat, dst: GpuMat | None = ..., mask: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def findMinMax(src: cv2.UMat, dst: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def findMinMaxLoc(src: cv2.typing.MatLike, minMaxVals: cv2.typing.MatLike | None = ..., loc: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> tuple[cv2.typing.MatLike, cv2.typing.MatLike]: ...
@_typing.overload
def findMinMaxLoc(src: GpuMat, minMaxVals: GpuMat | None = ..., loc: GpuMat | None = ..., mask: GpuMat | None = ..., stream: Stream = ...) -> tuple[GpuMat, GpuMat]: ...
@_typing.overload
def findMinMaxLoc(src: cv2.UMat, minMaxVals: cv2.UMat | None = ..., loc: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., stream: Stream = ...) -> tuple[cv2.UMat, cv2.UMat]: ...

@_typing.overload
def flip(src: cv2.typing.MatLike, flipCode: int, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def flip(src: GpuMat, flipCode: int, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def flip(src: cv2.UMat, flipCode: int, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def gammaCorrection(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., forward: bool = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def gammaCorrection(src: GpuMat, dst: GpuMat | None = ..., forward: bool = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def gammaCorrection(src: cv2.UMat, dst: cv2.UMat | None = ..., forward: bool = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def gemm(src1: cv2.typing.MatLike, src2: cv2.typing.MatLike, alpha: float, src3: cv2.typing.MatLike, beta: float, dst: cv2.typing.MatLike | None = ..., flags: int = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def gemm(src1: GpuMat, src2: GpuMat, alpha: float, src3: GpuMat, beta: float, dst: GpuMat | None = ..., flags: int = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def gemm(src1: cv2.UMat, src2: cv2.UMat, alpha: float, src3: cv2.UMat, beta: float, dst: cv2.UMat | None = ..., flags: int = ..., stream: Stream = ...) -> cv2.UMat: ...

def getCudaEnabledDeviceCount() -> int: ...

def getDevice() -> int: ...

@_typing.overload
def histEven(src: cv2.typing.MatLike, histSize: int, lowerLevel: int, upperLevel: int, hist: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def histEven(src: GpuMat, histSize: int, lowerLevel: int, upperLevel: int, hist: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def histEven(src: cv2.UMat, histSize: int, lowerLevel: int, upperLevel: int, hist: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...
@_typing.overload
def histEven(src: cv2.typing.MatLike, hist: GpuMat, histSize: int, lowerLevel: int, upperLevel: int, stream: Stream = ...) -> None: ...
@_typing.overload
def histEven(src: GpuMat, hist: GpuMat, histSize: int, lowerLevel: int, upperLevel: int, stream: Stream = ...) -> None: ...
@_typing.overload
def histEven(src: cv2.UMat, hist: GpuMat, histSize: int, lowerLevel: int, upperLevel: int, stream: Stream = ...) -> None: ...

@_typing.overload
def histRange(src: cv2.typing.MatLike, levels: cv2.typing.MatLike, hist: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def histRange(src: GpuMat, levels: GpuMat, hist: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def histRange(src: cv2.UMat, levels: cv2.UMat, hist: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...
@_typing.overload
def histRange(src: cv2.typing.MatLike, hist: GpuMat, levels: GpuMat, stream: Stream = ...) -> None: ...
@_typing.overload
def histRange(src: GpuMat, hist: GpuMat, levels: GpuMat, stream: Stream = ...) -> None: ...
@_typing.overload
def histRange(src: cv2.UMat, hist: GpuMat, levels: GpuMat, stream: Stream = ...) -> None: ...

@_typing.overload
def inRange(src: cv2.typing.MatLike, lowerb: cv2.typing.Scalar, upperb: cv2.typing.Scalar, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def inRange(src: GpuMat, lowerb: cv2.typing.Scalar, upperb: cv2.typing.Scalar, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def inRange(src: cv2.UMat, lowerb: cv2.typing.Scalar, upperb: cv2.typing.Scalar, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def integral(src: cv2.typing.MatLike, sum: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def integral(src: GpuMat, sum: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def integral(src: cv2.UMat, sum: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def log(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def log(src: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def log(src: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def lshift(src: cv2.typing.MatLike, val: cv2.typing.Scalar, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def lshift(src: GpuMat, val: cv2.typing.Scalar, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def lshift(src: cv2.UMat, val: cv2.typing.Scalar, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def magnitude(xy: cv2.typing.MatLike, magnitude: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def magnitude(xy: GpuMat, magnitude: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def magnitude(xy: cv2.UMat, magnitude: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...
@_typing.overload
def magnitude(x: cv2.typing.MatLike, y: cv2.typing.MatLike, magnitude: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def magnitude(x: GpuMat, y: GpuMat, magnitude: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def magnitude(x: cv2.UMat, y: cv2.UMat, magnitude: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def magnitudeSqr(xy: cv2.typing.MatLike, magnitude: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def magnitudeSqr(xy: GpuMat, magnitude: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def magnitudeSqr(xy: cv2.UMat, magnitude: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...
@_typing.overload
def magnitudeSqr(x: cv2.typing.MatLike, y: cv2.typing.MatLike, magnitude: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def magnitudeSqr(x: GpuMat, y: GpuMat, magnitude: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def magnitudeSqr(x: cv2.UMat, y: cv2.UMat, magnitude: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def max(src1: cv2.typing.MatLike, src2: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def max(src1: GpuMat, src2: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def max(src1: cv2.UMat, src2: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def meanShiftFiltering(src: cv2.typing.MatLike, sp: int, sr: int, dst: cv2.typing.MatLike | None = ..., criteria: cv2.typing.TermCriteria = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def meanShiftFiltering(src: GpuMat, sp: int, sr: int, dst: GpuMat | None = ..., criteria: cv2.typing.TermCriteria = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def meanShiftFiltering(src: cv2.UMat, sp: int, sr: int, dst: cv2.UMat | None = ..., criteria: cv2.typing.TermCriteria = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def meanShiftProc(src: cv2.typing.MatLike, sp: int, sr: int, dstr: cv2.typing.MatLike | None = ..., dstsp: cv2.typing.MatLike | None = ..., criteria: cv2.typing.TermCriteria = ..., stream: Stream = ...) -> tuple[cv2.typing.MatLike, cv2.typing.MatLike]: ...
@_typing.overload
def meanShiftProc(src: GpuMat, sp: int, sr: int, dstr: GpuMat | None = ..., dstsp: GpuMat | None = ..., criteria: cv2.typing.TermCriteria = ..., stream: Stream = ...) -> tuple[GpuMat, GpuMat]: ...
@_typing.overload
def meanShiftProc(src: cv2.UMat, sp: int, sr: int, dstr: cv2.UMat | None = ..., dstsp: cv2.UMat | None = ..., criteria: cv2.typing.TermCriteria = ..., stream: Stream = ...) -> tuple[cv2.UMat, cv2.UMat]: ...

@_typing.overload
def meanShiftSegmentation(src: cv2.typing.MatLike, sp: int, sr: int, minsize: int, dst: cv2.typing.MatLike | None = ..., criteria: cv2.typing.TermCriteria = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def meanShiftSegmentation(src: GpuMat, sp: int, sr: int, minsize: int, dst: GpuMat | None = ..., criteria: cv2.typing.TermCriteria = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def meanShiftSegmentation(src: cv2.UMat, sp: int, sr: int, minsize: int, dst: cv2.UMat | None = ..., criteria: cv2.typing.TermCriteria = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def meanStdDev(src: cv2.typing.MatLike, mask: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def meanStdDev(src: GpuMat, mask: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def meanStdDev(src: cv2.UMat, mask: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...
@_typing.overload
def meanStdDev(mtx: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def meanStdDev(mtx: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def meanStdDev(mtx: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...
@_typing.overload
def meanStdDev(src: cv2.typing.MatLike, mask: cv2.typing.MatLike) -> tuple[cv2.typing.Scalar, cv2.typing.Scalar]: ...
@_typing.overload
def meanStdDev(src: GpuMat, mask: GpuMat) -> tuple[cv2.typing.Scalar, cv2.typing.Scalar]: ...
@_typing.overload
def meanStdDev(src: cv2.UMat, mask: cv2.UMat) -> tuple[cv2.typing.Scalar, cv2.typing.Scalar]: ...
@_typing.overload
def meanStdDev(mtx: cv2.typing.MatLike) -> tuple[cv2.typing.Scalar, cv2.typing.Scalar]: ...
@_typing.overload
def meanStdDev(mtx: GpuMat) -> tuple[cv2.typing.Scalar, cv2.typing.Scalar]: ...
@_typing.overload
def meanStdDev(mtx: cv2.UMat) -> tuple[cv2.typing.Scalar, cv2.typing.Scalar]: ...

@_typing.overload
def merge(src: _typing.Sequence[GpuMat], dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def merge(src: _typing.Sequence[GpuMat], dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def merge(src: _typing.Sequence[GpuMat], dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def min(src1: cv2.typing.MatLike, src2: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def min(src1: GpuMat, src2: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def min(src1: cv2.UMat, src2: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def minMax(src: cv2.typing.MatLike, mask: cv2.typing.MatLike | None = ...) -> tuple[float, float]: ...
@_typing.overload
def minMax(src: GpuMat, mask: GpuMat | None = ...) -> tuple[float, float]: ...
@_typing.overload
def minMax(src: cv2.UMat, mask: cv2.UMat | None = ...) -> tuple[float, float]: ...

@_typing.overload
def minMaxLoc(src: cv2.typing.MatLike, mask: cv2.typing.MatLike | None = ...) -> tuple[float, float, cv2.typing.Point, cv2.typing.Point]: ...
@_typing.overload
def minMaxLoc(src: GpuMat, mask: GpuMat | None = ...) -> tuple[float, float, cv2.typing.Point, cv2.typing.Point]: ...
@_typing.overload
def minMaxLoc(src: cv2.UMat, mask: cv2.UMat | None = ...) -> tuple[float, float, cv2.typing.Point, cv2.typing.Point]: ...

@_typing.overload
def moments(src: cv2.typing.MatLike, binaryImage: bool = ..., order: MomentsOrder = ..., momentsType: int = ...) -> cv2.typing.Moments: ...
@_typing.overload
def moments(src: GpuMat, binaryImage: bool = ..., order: MomentsOrder = ..., momentsType: int = ...) -> cv2.typing.Moments: ...
@_typing.overload
def moments(src: cv2.UMat, binaryImage: bool = ..., order: MomentsOrder = ..., momentsType: int = ...) -> cv2.typing.Moments: ...

@_typing.overload
def mulAndScaleSpectrums(src1: cv2.typing.MatLike, src2: cv2.typing.MatLike, flags: int, scale: float, dst: cv2.typing.MatLike | None = ..., conjB: bool = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def mulAndScaleSpectrums(src1: GpuMat, src2: GpuMat, flags: int, scale: float, dst: GpuMat | None = ..., conjB: bool = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def mulAndScaleSpectrums(src1: cv2.UMat, src2: cv2.UMat, flags: int, scale: float, dst: cv2.UMat | None = ..., conjB: bool = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def mulSpectrums(src1: cv2.typing.MatLike, src2: cv2.typing.MatLike, flags: int, dst: cv2.typing.MatLike | None = ..., conjB: bool = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def mulSpectrums(src1: GpuMat, src2: GpuMat, flags: int, dst: GpuMat | None = ..., conjB: bool = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def mulSpectrums(src1: cv2.UMat, src2: cv2.UMat, flags: int, dst: cv2.UMat | None = ..., conjB: bool = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def multiply(src1: cv2.typing.MatLike, src2: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., scale: float = ..., dtype: int = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def multiply(src1: GpuMat, src2: GpuMat, dst: GpuMat | None = ..., scale: float = ..., dtype: int = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def multiply(src1: cv2.UMat, src2: cv2.UMat, dst: cv2.UMat | None = ..., scale: float = ..., dtype: int = ..., stream: Stream = ...) -> cv2.UMat: ...

def nonLocalMeans(src: GpuMat, h: float, dst: GpuMat | None = ..., search_window: int = ..., block_size: int = ..., borderMode: int = ..., stream: Stream = ...) -> GpuMat: ...

@_typing.overload
def norm(src1: cv2.typing.MatLike, normType: int, mask: cv2.typing.MatLike | None = ...) -> float: ...
@_typing.overload
def norm(src1: GpuMat, normType: int, mask: GpuMat | None = ...) -> float: ...
@_typing.overload
def norm(src1: cv2.UMat, normType: int, mask: cv2.UMat | None = ...) -> float: ...
@_typing.overload
def norm(src1: cv2.typing.MatLike, src2: cv2.typing.MatLike, normType: int = ...) -> float: ...
@_typing.overload
def norm(src1: GpuMat, src2: GpuMat, normType: int = ...) -> float: ...
@_typing.overload
def norm(src1: cv2.UMat, src2: cv2.UMat, normType: int = ...) -> float: ...

@_typing.overload
def normalize(src: cv2.typing.MatLike, alpha: float, beta: float, norm_type: int, dtype: int, dst: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def normalize(src: GpuMat, alpha: float, beta: float, norm_type: int, dtype: int, dst: GpuMat | None = ..., mask: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def normalize(src: cv2.UMat, alpha: float, beta: float, norm_type: int, dtype: int, dst: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

def numMoments(order: MomentsOrder) -> int: ...

@_typing.overload
def phase(x: cv2.typing.MatLike, y: cv2.typing.MatLike, angle: cv2.typing.MatLike | None = ..., angleInDegrees: bool = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def phase(x: GpuMat, y: GpuMat, angle: GpuMat | None = ..., angleInDegrees: bool = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def phase(x: cv2.UMat, y: cv2.UMat, angle: cv2.UMat | None = ..., angleInDegrees: bool = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def polarToCart(magnitude: cv2.typing.MatLike, angle: cv2.typing.MatLike, x: cv2.typing.MatLike | None = ..., y: cv2.typing.MatLike | None = ..., angleInDegrees: bool = ..., stream: Stream = ...) -> tuple[cv2.typing.MatLike, cv2.typing.MatLike]: ...
@_typing.overload
def polarToCart(magnitude: GpuMat, angle: GpuMat, x: GpuMat | None = ..., y: GpuMat | None = ..., angleInDegrees: bool = ..., stream: Stream = ...) -> tuple[GpuMat, GpuMat]: ...
@_typing.overload
def polarToCart(magnitude: cv2.UMat, angle: cv2.UMat, x: cv2.UMat | None = ..., y: cv2.UMat | None = ..., angleInDegrees: bool = ..., stream: Stream = ...) -> tuple[cv2.UMat, cv2.UMat]: ...

@_typing.overload
def pow(src: cv2.typing.MatLike, power: float, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def pow(src: GpuMat, power: float, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def pow(src: cv2.UMat, power: float, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

def printCudaDeviceInfo(device: int) -> None: ...

def printShortCudaDeviceInfo(device: int) -> None: ...

@_typing.overload
def pyrDown(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def pyrDown(src: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def pyrDown(src: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def pyrUp(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def pyrUp(src: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def pyrUp(src: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def rectStdDev(src: cv2.typing.MatLike, sqr: cv2.typing.MatLike, rect: cv2.typing.Rect, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def rectStdDev(src: GpuMat, sqr: GpuMat, rect: cv2.typing.Rect, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def rectStdDev(src: cv2.UMat, sqr: cv2.UMat, rect: cv2.typing.Rect, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def reduce(mtx: cv2.typing.MatLike, dim: int, reduceOp: int, vec: cv2.typing.MatLike | None = ..., dtype: int = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def reduce(mtx: GpuMat, dim: int, reduceOp: int, vec: GpuMat | None = ..., dtype: int = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def reduce(mtx: cv2.UMat, dim: int, reduceOp: int, vec: cv2.UMat | None = ..., dtype: int = ..., stream: Stream = ...) -> cv2.UMat: ...

def registerPageLocked(m: cv2.typing.MatLike) -> None: ...

@_typing.overload
def remap(src: cv2.typing.MatLike, xmap: cv2.typing.MatLike, ymap: cv2.typing.MatLike, interpolation: int, dst: cv2.typing.MatLike | None = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def remap(src: GpuMat, xmap: GpuMat, ymap: GpuMat, interpolation: int, dst: GpuMat | None = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def remap(src: cv2.UMat, xmap: cv2.UMat, ymap: cv2.UMat, interpolation: int, dst: cv2.UMat | None = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ..., stream: Stream = ...) -> cv2.UMat: ...

def reprojectImageTo3D(disp: GpuMat, Q: cv2.typing.MatLike, xyzw: GpuMat | None = ..., dst_cn: int = ..., stream: Stream = ...) -> GpuMat: ...

def resetDevice() -> None: ...

@_typing.overload
def resize(src: cv2.typing.MatLike, dsize: cv2.typing.Size, dst: cv2.typing.MatLike | None = ..., fx: float = ..., fy: float = ..., interpolation: int = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def resize(src: GpuMat, dsize: cv2.typing.Size, dst: GpuMat | None = ..., fx: float = ..., fy: float = ..., interpolation: int = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def resize(src: cv2.UMat, dsize: cv2.typing.Size, dst: cv2.UMat | None = ..., fx: float = ..., fy: float = ..., interpolation: int = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def rotate(src: cv2.typing.MatLike, dsize: cv2.typing.Size, angle: float, dst: cv2.typing.MatLike | None = ..., xShift: float = ..., yShift: float = ..., interpolation: int = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def rotate(src: GpuMat, dsize: cv2.typing.Size, angle: float, dst: GpuMat | None = ..., xShift: float = ..., yShift: float = ..., interpolation: int = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def rotate(src: cv2.UMat, dsize: cv2.typing.Size, angle: float, dst: cv2.UMat | None = ..., xShift: float = ..., yShift: float = ..., interpolation: int = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def rshift(src: cv2.typing.MatLike, val: cv2.typing.Scalar, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def rshift(src: GpuMat, val: cv2.typing.Scalar, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def rshift(src: cv2.UMat, val: cv2.typing.Scalar, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

def setBufferPoolConfig(deviceId: int, stackSize: int, stackCount: int) -> None: ...

def setBufferPoolUsage(on: bool) -> None: ...

def setDevice(device: int) -> None: ...

@_typing.overload
def spatialMoments(src: cv2.typing.MatLike, moments: cv2.typing.MatLike | None = ..., binaryImage: bool = ..., order: MomentsOrder = ..., momentsType: int = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def spatialMoments(src: GpuMat, moments: GpuMat | None = ..., binaryImage: bool = ..., order: MomentsOrder = ..., momentsType: int = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def spatialMoments(src: cv2.UMat, moments: cv2.UMat | None = ..., binaryImage: bool = ..., order: MomentsOrder = ..., momentsType: int = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def split(src: cv2.typing.MatLike, dst: _typing.Sequence[GpuMat] | None = ..., stream: Stream = ...) -> _typing.Sequence[GpuMat]: ...
@_typing.overload
def split(src: GpuMat, dst: _typing.Sequence[GpuMat] | None = ..., stream: Stream = ...) -> _typing.Sequence[GpuMat]: ...
@_typing.overload
def split(src: cv2.UMat, dst: _typing.Sequence[GpuMat] | None = ..., stream: Stream = ...) -> _typing.Sequence[GpuMat]: ...

@_typing.overload
def sqr(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def sqr(src: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def sqr(src: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def sqrIntegral(src: cv2.typing.MatLike, sqsum: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def sqrIntegral(src: GpuMat, sqsum: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def sqrIntegral(src: cv2.UMat, sqsum: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def sqrSum(src: cv2.typing.MatLike, mask: cv2.typing.MatLike | None = ...) -> cv2.typing.Scalar: ...
@_typing.overload
def sqrSum(src: GpuMat, mask: GpuMat | None = ...) -> cv2.typing.Scalar: ...
@_typing.overload
def sqrSum(src: cv2.UMat, mask: cv2.UMat | None = ...) -> cv2.typing.Scalar: ...

@_typing.overload
def sqrt(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def sqrt(src: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def sqrt(src: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def subtract(src1: cv2.typing.MatLike, src2: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ..., dtype: int = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def subtract(src1: GpuMat, src2: GpuMat, dst: GpuMat | None = ..., mask: GpuMat | None = ..., dtype: int = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def subtract(src1: cv2.UMat, src2: cv2.UMat, dst: cv2.UMat | None = ..., mask: cv2.UMat | None = ..., dtype: int = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def sum(src: cv2.typing.MatLike, mask: cv2.typing.MatLike | None = ...) -> cv2.typing.Scalar: ...
@_typing.overload
def sum(src: GpuMat, mask: GpuMat | None = ...) -> cv2.typing.Scalar: ...
@_typing.overload
def sum(src: cv2.UMat, mask: cv2.UMat | None = ...) -> cv2.typing.Scalar: ...

@_typing.overload
def threshold(src: cv2.typing.MatLike, thresh: float, maxval: float, type: int, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> tuple[float, cv2.typing.MatLike]: ...
@_typing.overload
def threshold(src: GpuMat, thresh: float, maxval: float, type: int, dst: GpuMat | None = ..., stream: Stream = ...) -> tuple[float, GpuMat]: ...
@_typing.overload
def threshold(src: cv2.UMat, thresh: float, maxval: float, type: int, dst: cv2.UMat | None = ..., stream: Stream = ...) -> tuple[float, cv2.UMat]: ...

@_typing.overload
def transpose(src1: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def transpose(src1: GpuMat, dst: GpuMat | None = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def transpose(src1: cv2.UMat, dst: cv2.UMat | None = ..., stream: Stream = ...) -> cv2.UMat: ...

def unregisterPageLocked(m: cv2.typing.MatLike) -> None: ...

@_typing.overload
def warpAffine(src: cv2.typing.MatLike, M: cv2.UMat, dsize: cv2.typing.Size, dst: cv2.typing.MatLike | None = ..., flags: int = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def warpAffine(src: GpuMat, M: cv2.UMat, dsize: cv2.typing.Size, dst: GpuMat | None = ..., flags: int = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def warpAffine(src: cv2.UMat, M: cv2.UMat, dsize: cv2.typing.Size, dst: cv2.UMat | None = ..., flags: int = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ..., stream: Stream = ...) -> cv2.UMat: ...
@_typing.overload
def warpAffine(src: cv2.typing.MatLike, M: cv2.typing.MatLike, dsize: cv2.typing.Size, dst: cv2.typing.MatLike | None = ..., flags: int = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def warpAffine(src: GpuMat, M: cv2.typing.MatLike, dsize: cv2.typing.Size, dst: GpuMat | None = ..., flags: int = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def warpAffine(src: cv2.UMat, M: cv2.typing.MatLike, dsize: cv2.typing.Size, dst: cv2.UMat | None = ..., flags: int = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ..., stream: Stream = ...) -> cv2.UMat: ...

@_typing.overload
def warpPerspective(src: cv2.typing.MatLike, M: cv2.UMat, dsize: cv2.typing.Size, dst: cv2.typing.MatLike | None = ..., flags: int = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def warpPerspective(src: GpuMat, M: cv2.UMat, dsize: cv2.typing.Size, dst: GpuMat | None = ..., flags: int = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def warpPerspective(src: cv2.UMat, M: cv2.UMat, dsize: cv2.typing.Size, dst: cv2.UMat | None = ..., flags: int = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ..., stream: Stream = ...) -> cv2.UMat: ...
@_typing.overload
def warpPerspective(src: cv2.typing.MatLike, M: cv2.typing.MatLike, dsize: cv2.typing.Size, dst: cv2.typing.MatLike | None = ..., flags: int = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ..., stream: Stream = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def warpPerspective(src: GpuMat, M: cv2.typing.MatLike, dsize: cv2.typing.Size, dst: GpuMat | None = ..., flags: int = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ..., stream: Stream = ...) -> GpuMat: ...
@_typing.overload
def warpPerspective(src: cv2.UMat, M: cv2.typing.MatLike, dsize: cv2.typing.Size, dst: cv2.UMat | None = ..., flags: int = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ..., stream: Stream = ...) -> cv2.UMat: ...

def wrapStream(cudaStreamMemoryAddress: int) -> Stream: ...


