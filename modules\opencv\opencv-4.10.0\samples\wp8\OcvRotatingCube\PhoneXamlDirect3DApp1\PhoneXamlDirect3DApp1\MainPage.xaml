﻿<phone:PhoneApplicationPage x:Class="PhoneXamlDirect3DApp1.MainPage"
                            xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                            xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                            xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                            xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                            xmlns:phone="clr-namespace:Microsoft.Phone.Controls;assembly=Microsoft.Phone"
                            xmlns:shell="clr-namespace:Microsoft.Phone.Shell;assembly=Microsoft.Phone"
                            FontFamily="{StaticResource PhoneFontFamilyNormal}"
                            FontSize="{StaticResource PhoneFontSizeNormal}"
                            Foreground="{StaticResource PhoneForegroundBrush}"
                            Orientation="Portrait"
                            SupportedOrientations="Portrait"
                            shell:SystemTray.IsVisible="True"
                            mc:Ignorable="d">

    <!--  LayoutRoot is the root grid where all page content is placed  -->
    <Grid x:Name="LayoutRoot" Background="Transparent">
        <DrawingSurface x:Name="DrawingSurface" Loaded="DrawingSurface_Loaded" />
        <StackPanel Margin="40">
            <RadioButton x:Name="Normal"
                         Checked="RadioButton_Checked"
                         Content="Normal"
                         GroupName="Group1"
                         IsChecked="True" />
            <RadioButton x:Name="Gray"
                         Checked="RadioButton_Checked"
                         Content="Gray"
                         GroupName="Group1" />
            <RadioButton x:Name="Canny"
                         Checked="RadioButton_Checked"
                         Content="Canny"
                         GroupName="Group1" />
            <RadioButton x:Name="Sepia"
                         Checked="RadioButton_Checked"
                         Content="Sepia"
                         GroupName="Group1" />
        </StackPanel>
    </Grid>

</phone:PhoneApplicationPage>