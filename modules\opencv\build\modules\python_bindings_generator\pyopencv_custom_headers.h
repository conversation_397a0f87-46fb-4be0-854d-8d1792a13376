//user-defined headers
#include "D:/AI/opencv/opencv-4.10.0/modules/core/misc/python/pyopencv_async.hpp"
#include "D:/AI/opencv/opencv-4.10.0/modules/core/misc/python/pyopencv_core.hpp"
#include "D:/AI/opencv/opencv-4.10.0/modules/core/misc/python/pyopencv_cuda.hpp"
#include "D:/AI/opencv/opencv-4.10.0/modules/core/misc/python/pyopencv_umat.hpp"
#include "D:/AI/opencv/opencv-4.10.0/modules/flann/misc/python/pyopencv_flann.hpp"
#include "D:/AI/opencv/opencv-4.10.0/modules/ml/misc/python/pyopencv_ml.hpp"
#include "D:/AI/opencv/opencv_contrib-4.10.0/modules/phase_unwrapping/misc/python/pyopencv_phase_unwrapping.hpp"
#include "D:/AI/opencv/opencv_contrib-4.10.0/modules/surface_matching/misc/python/pyopencv_ppf_match_3d.hpp"
#include "D:/AI/opencv/opencv-4.10.0/modules/dnn/misc/python/pyopencv_dnn.hpp"
#include "D:/AI/opencv/opencv-4.10.0/modules/features2d/misc/python/pyopencv_features2d.hpp"
#include "D:/AI/opencv/opencv_contrib-4.10.0/modules/line_descriptor/misc/python/pyopencv_LSDDetector.hpp"
#include "D:/AI/opencv/opencv-4.10.0/modules/videoio/misc/python/pyopencv_videoio.hpp"
#include "D:/AI/opencv/opencv_contrib-4.10.0/modules/mcc/misc/python/pyopencv_cchecker.hpp"
#include "D:/AI/opencv/opencv-4.10.0/modules/objdetect/misc/python/pyopencv_objdetect.hpp"
#include "D:/AI/opencv/opencv_contrib-4.10.0/modules/rgbd/misc/python/pyopencv_linemod.hpp"
#include "D:/AI/opencv/opencv-4.10.0/modules/video/misc/python/pyopencv_video.hpp"
#include "D:/AI/opencv/opencv_contrib-4.10.0/modules/xfeatures2d/misc/python/pyopencv_sift.hpp"
#include "D:/AI/opencv/opencv_contrib-4.10.0/modules/xfeatures2d/misc/python/pyopencv_xfeatures2d.hpp"
#include "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/misc/python/pyopencv_ximgproc.hpp"
#include "D:/AI/opencv/opencv-4.10.0/modules/gapi/misc/python/python_bridge.hpp"
#include "D:/AI/opencv/opencv-4.10.0/modules/gapi/misc/python/pyopencv_gapi.hpp"
#include "D:/AI/opencv/opencv-4.10.0/modules/stitching/misc/python/pyopencv_stitching.hpp"
#include "D:/AI/opencv/opencv_contrib-4.10.0/modules/tracking/misc/python/pyopencv_tracking.hpp"
#include "D:/AI/opencv/opencv_contrib-4.10.0/modules/stereo/misc/python/pyopencv_stereo.hpp"
