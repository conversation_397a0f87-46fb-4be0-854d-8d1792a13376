﻿  sharpyuv.c
  sharpyuv_cpu.c
  sharpyuv_csp.c
  sharpyuv_dsp.c
  sharpyuv_gamma.c
  sharpyuv_neon.c
  sharpyuv_sse2.c
  alpha_dec.c
  buffer_dec.c
  frame_dec.c
  idec_dec.c
  io_dec.c
  quant_dec.c
  tree_dec.c
  vp8_dec.c
  vp8l_dec.c
  webp_dec.c
  anim_decode.c
  demux.c
  alpha_processing.c
  alpha_processing_mips_dsp_r2.c
  alpha_processing_neon.c
  alpha_processing_sse2.c
  alpha_processing_sse41.c
  cost.c
  cost_mips32.c
  cost_mips_dsp_r2.c
  cost_neon.c
  cost_sse2.c
  cpu.c
  dec.c
  dec_clip_tables.c
  dec_mips32.c
  dec_mips_dsp_r2.c
  dec_msa.c
  dec_neon.c
  dec_sse2.c
  dec_sse41.c
  enc.c
  enc_mips32.c
  enc_mips_dsp_r2.c
  enc_msa.c
  enc_neon.c
  enc_sse2.c
  enc_sse41.c
  filters.c
  filters_mips_dsp_r2.c
  filters_msa.c
  filters_neon.c
  filters_sse2.c
  lossless.c
  lossless_enc.c
  lossless_enc_mips32.c
  lossless_enc_mips_dsp_r2.c
  lossless_enc_msa.c
  lossless_enc_neon.c
  lossless_enc_sse2.c
  lossless_enc_sse41.c
  lossless_mips_dsp_r2.c
  lossless_msa.c
  lossless_neon.c
  lossless_sse2.c
  lossless_sse41.c
  rescaler.c
  rescaler_mips32.c
  rescaler_mips_dsp_r2.c
  rescaler_msa.c
  rescaler_neon.c
  rescaler_sse2.c
  ssim.c
  ssim_sse2.c
  upsampling.c
  upsampling_mips_dsp_r2.c
  upsampling_msa.c
  upsampling_neon.c
  upsampling_sse2.c
  upsampling_sse41.c
  yuv.c
  yuv_mips32.c
  yuv_mips_dsp_r2.c
  yuv_neon.c
  yuv_sse2.c
  yuv_sse41.c
  alpha_enc.c
  analysis_enc.c
  backward_references_cost_enc.c
  backward_references_enc.c
  config_enc.c
  cost_enc.c
  filter_enc.c
  frame_enc.c
  histogram_enc.c
  iterator_enc.c
  near_lossless_enc.c
  picture_csp_enc.c
  picture_enc.c
  picture_psnr_enc.c
  picture_rescale_enc.c
  picture_tools_enc.c
  predictor_enc.c
  quant_enc.c
  syntax_enc.c
  token_enc.c
  tree_enc.c
  vp8l_enc.c
  webp_enc.c
  anim_encode.c
  muxedit.c
  muxinternal.c
  muxread.c
  bit_reader_utils.c
  bit_writer_utils.c
  color_cache_utils.c
  filters_utils.c
  huffman_encode_utils.c
  huffman_utils.c
  palette.c
  quant_levels_dec_utils.c
  quant_levels_utils.c
  random_utils.c
  rescaler_utils.c
  thread_utils.c
  utils.c
  libwebp.vcxproj -> D:\AI\opencv\cudabuild\3rdparty\lib\Release\libwebp.lib
