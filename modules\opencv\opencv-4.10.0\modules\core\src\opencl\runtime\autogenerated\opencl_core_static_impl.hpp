//
// AUTOGENERATED, DO NOT EDIT
//
// generated by parser_cl.py
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clBuildProgram_pfn)(cl_program, cl_uint, const cl_device_id*, const char*, void (CL_CALLBACK*) (cl_program, void*), void*) = clBuildProgram;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clCompileProgram_pfn)(cl_program, cl_uint, const cl_device_id*, const char*, cl_uint, const cl_program*, const char**, void (CL_CALLBACK*) (cl_program, void*), void*) = clCompileProgram;
CL_RUNTIME_EXPORT cl_mem (CL_API_CALL*clCreateBuffer_pfn)(cl_context, cl_mem_flags, size_t, void*, cl_int*) = clCreateBuffer;
CL_RUNTIME_EXPORT cl_command_queue (CL_API_CALL*clCreateCommandQueue_pfn)(cl_context, cl_device_id, cl_command_queue_properties, cl_int*) = clCreateCommandQueue;
CL_RUNTIME_EXPORT cl_context (CL_API_CALL*clCreateContext_pfn)(const cl_context_properties*, cl_uint, const cl_device_id*, void (CL_CALLBACK*) (const char*, const void*, size_t, void*), void*, cl_int*) = clCreateContext;
CL_RUNTIME_EXPORT cl_context (CL_API_CALL*clCreateContextFromType_pfn)(const cl_context_properties*, cl_device_type, void (CL_CALLBACK*) (const char*, const void*, size_t, void*), void*, cl_int*) = clCreateContextFromType;
CL_RUNTIME_EXPORT cl_mem (CL_API_CALL*clCreateImage_pfn)(cl_context, cl_mem_flags, const cl_image_format*, const cl_image_desc*, void*, cl_int*) = clCreateImage;
CL_RUNTIME_EXPORT cl_mem (CL_API_CALL*clCreateImage2D_pfn)(cl_context, cl_mem_flags, const cl_image_format*, size_t, size_t, size_t, void*, cl_int*) = clCreateImage2D;
CL_RUNTIME_EXPORT cl_mem (CL_API_CALL*clCreateImage3D_pfn)(cl_context, cl_mem_flags, const cl_image_format*, size_t, size_t, size_t, size_t, size_t, void*, cl_int*) = clCreateImage3D;
CL_RUNTIME_EXPORT cl_kernel (CL_API_CALL*clCreateKernel_pfn)(cl_program, const char*, cl_int*) = clCreateKernel;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clCreateKernelsInProgram_pfn)(cl_program, cl_uint, cl_kernel*, cl_uint*) = clCreateKernelsInProgram;
CL_RUNTIME_EXPORT cl_program (CL_API_CALL*clCreateProgramWithBinary_pfn)(cl_context, cl_uint, const cl_device_id*, const size_t*, const unsigned char**, cl_int*, cl_int*) = clCreateProgramWithBinary;
CL_RUNTIME_EXPORT cl_program (CL_API_CALL*clCreateProgramWithBuiltInKernels_pfn)(cl_context, cl_uint, const cl_device_id*, const char*, cl_int*) = clCreateProgramWithBuiltInKernels;
CL_RUNTIME_EXPORT cl_program (CL_API_CALL*clCreateProgramWithSource_pfn)(cl_context, cl_uint, const char**, const size_t*, cl_int*) = clCreateProgramWithSource;
CL_RUNTIME_EXPORT cl_sampler (CL_API_CALL*clCreateSampler_pfn)(cl_context, cl_bool, cl_addressing_mode, cl_filter_mode, cl_int*) = clCreateSampler;
CL_RUNTIME_EXPORT cl_mem (CL_API_CALL*clCreateSubBuffer_pfn)(cl_mem, cl_mem_flags, cl_buffer_create_type, const void*, cl_int*) = clCreateSubBuffer;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clCreateSubDevices_pfn)(cl_device_id, const cl_device_partition_property*, cl_uint, cl_device_id*, cl_uint*) = clCreateSubDevices;
CL_RUNTIME_EXPORT cl_event (CL_API_CALL*clCreateUserEvent_pfn)(cl_context, cl_int*) = clCreateUserEvent;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueBarrier_pfn)(cl_command_queue) = clEnqueueBarrier;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueBarrierWithWaitList_pfn)(cl_command_queue, cl_uint, const cl_event*, cl_event*) = clEnqueueBarrierWithWaitList;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueCopyBuffer_pfn)(cl_command_queue, cl_mem, cl_mem, size_t, size_t, size_t, cl_uint, const cl_event*, cl_event*) = clEnqueueCopyBuffer;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueCopyBufferRect_pfn)(cl_command_queue, cl_mem, cl_mem, const size_t*, const size_t*, const size_t*, size_t, size_t, size_t, size_t, cl_uint, const cl_event*, cl_event*) = clEnqueueCopyBufferRect;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueCopyBufferToImage_pfn)(cl_command_queue, cl_mem, cl_mem, size_t, const size_t*, const size_t*, cl_uint, const cl_event*, cl_event*) = clEnqueueCopyBufferToImage;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueCopyImage_pfn)(cl_command_queue, cl_mem, cl_mem, const size_t*, const size_t*, const size_t*, cl_uint, const cl_event*, cl_event*) = clEnqueueCopyImage;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueCopyImageToBuffer_pfn)(cl_command_queue, cl_mem, cl_mem, const size_t*, const size_t*, size_t, cl_uint, const cl_event*, cl_event*) = clEnqueueCopyImageToBuffer;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueFillBuffer_pfn)(cl_command_queue, cl_mem, const void*, size_t, size_t, size_t, cl_uint, const cl_event*, cl_event*) = clEnqueueFillBuffer;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueFillImage_pfn)(cl_command_queue, cl_mem, const void*, const size_t*, const size_t*, cl_uint, const cl_event*, cl_event*) = clEnqueueFillImage;
CL_RUNTIME_EXPORT void* (CL_API_CALL*clEnqueueMapBuffer_pfn)(cl_command_queue, cl_mem, cl_bool, cl_map_flags, size_t, size_t, cl_uint, const cl_event*, cl_event*, cl_int*) = clEnqueueMapBuffer;
CL_RUNTIME_EXPORT void* (CL_API_CALL*clEnqueueMapImage_pfn)(cl_command_queue, cl_mem, cl_bool, cl_map_flags, const size_t*, const size_t*, size_t*, size_t*, cl_uint, const cl_event*, cl_event*, cl_int*) = clEnqueueMapImage;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueMarker_pfn)(cl_command_queue, cl_event*) = clEnqueueMarker;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueMarkerWithWaitList_pfn)(cl_command_queue, cl_uint, const cl_event*, cl_event*) = clEnqueueMarkerWithWaitList;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueMigrateMemObjects_pfn)(cl_command_queue, cl_uint, const cl_mem*, cl_mem_migration_flags, cl_uint, const cl_event*, cl_event*) = clEnqueueMigrateMemObjects;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueNDRangeKernel_pfn)(cl_command_queue, cl_kernel, cl_uint, const size_t*, const size_t*, const size_t*, cl_uint, const cl_event*, cl_event*) = clEnqueueNDRangeKernel;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueNativeKernel_pfn)(cl_command_queue, void (CL_CALLBACK*) (void*), void*, size_t, cl_uint, const cl_mem*, const void**, cl_uint, const cl_event*, cl_event*) = clEnqueueNativeKernel;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueReadBuffer_pfn)(cl_command_queue, cl_mem, cl_bool, size_t, size_t, void*, cl_uint, const cl_event*, cl_event*) = clEnqueueReadBuffer;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueReadBufferRect_pfn)(cl_command_queue, cl_mem, cl_bool, const size_t*, const size_t*, const size_t*, size_t, size_t, size_t, size_t, void*, cl_uint, const cl_event*, cl_event*) = clEnqueueReadBufferRect;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueReadImage_pfn)(cl_command_queue, cl_mem, cl_bool, const size_t*, const size_t*, size_t, size_t, void*, cl_uint, const cl_event*, cl_event*) = clEnqueueReadImage;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueTask_pfn)(cl_command_queue, cl_kernel, cl_uint, const cl_event*, cl_event*) = clEnqueueTask;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueUnmapMemObject_pfn)(cl_command_queue, cl_mem, void*, cl_uint, const cl_event*, cl_event*) = clEnqueueUnmapMemObject;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueWaitForEvents_pfn)(cl_command_queue, cl_uint, const cl_event*) = clEnqueueWaitForEvents;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueWriteBuffer_pfn)(cl_command_queue, cl_mem, cl_bool, size_t, size_t, const void*, cl_uint, const cl_event*, cl_event*) = clEnqueueWriteBuffer;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueWriteBufferRect_pfn)(cl_command_queue, cl_mem, cl_bool, const size_t*, const size_t*, const size_t*, size_t, size_t, size_t, size_t, const void*, cl_uint, const cl_event*, cl_event*) = clEnqueueWriteBufferRect;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clEnqueueWriteImage_pfn)(cl_command_queue, cl_mem, cl_bool, const size_t*, const size_t*, size_t, size_t, const void*, cl_uint, const cl_event*, cl_event*) = clEnqueueWriteImage;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clFinish_pfn)(cl_command_queue) = clFinish;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clFlush_pfn)(cl_command_queue) = clFlush;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetCommandQueueInfo_pfn)(cl_command_queue, cl_command_queue_info, size_t, void*, size_t*) = clGetCommandQueueInfo;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetContextInfo_pfn)(cl_context, cl_context_info, size_t, void*, size_t*) = clGetContextInfo;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetDeviceIDs_pfn)(cl_platform_id, cl_device_type, cl_uint, cl_device_id*, cl_uint*) = clGetDeviceIDs;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetDeviceInfo_pfn)(cl_device_id, cl_device_info, size_t, void*, size_t*) = clGetDeviceInfo;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetEventInfo_pfn)(cl_event, cl_event_info, size_t, void*, size_t*) = clGetEventInfo;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetEventProfilingInfo_pfn)(cl_event, cl_profiling_info, size_t, void*, size_t*) = clGetEventProfilingInfo;
CL_RUNTIME_EXPORT void* (CL_API_CALL*clGetExtensionFunctionAddress_pfn)(const char*) = clGetExtensionFunctionAddress;
CL_RUNTIME_EXPORT void* (CL_API_CALL*clGetExtensionFunctionAddressForPlatform_pfn)(cl_platform_id, const char*) = clGetExtensionFunctionAddressForPlatform;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetImageInfo_pfn)(cl_mem, cl_image_info, size_t, void*, size_t*) = clGetImageInfo;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetKernelArgInfo_pfn)(cl_kernel, cl_uint, cl_kernel_arg_info, size_t, void*, size_t*) = clGetKernelArgInfo;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetKernelInfo_pfn)(cl_kernel, cl_kernel_info, size_t, void*, size_t*) = clGetKernelInfo;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetKernelWorkGroupInfo_pfn)(cl_kernel, cl_device_id, cl_kernel_work_group_info, size_t, void*, size_t*) = clGetKernelWorkGroupInfo;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetMemObjectInfo_pfn)(cl_mem, cl_mem_info, size_t, void*, size_t*) = clGetMemObjectInfo;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetPlatformIDs_pfn)(cl_uint, cl_platform_id*, cl_uint*) = clGetPlatformIDs;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetPlatformInfo_pfn)(cl_platform_id, cl_platform_info, size_t, void*, size_t*) = clGetPlatformInfo;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetProgramBuildInfo_pfn)(cl_program, cl_device_id, cl_program_build_info, size_t, void*, size_t*) = clGetProgramBuildInfo;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetProgramInfo_pfn)(cl_program, cl_program_info, size_t, void*, size_t*) = clGetProgramInfo;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetSamplerInfo_pfn)(cl_sampler, cl_sampler_info, size_t, void*, size_t*) = clGetSamplerInfo;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clGetSupportedImageFormats_pfn)(cl_context, cl_mem_flags, cl_mem_object_type, cl_uint, cl_image_format*, cl_uint*) = clGetSupportedImageFormats;
CL_RUNTIME_EXPORT cl_program (CL_API_CALL*clLinkProgram_pfn)(cl_context, cl_uint, const cl_device_id*, const char*, cl_uint, const cl_program*, void (CL_CALLBACK*) (cl_program, void*), void*, cl_int*) = clLinkProgram;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clReleaseCommandQueue_pfn)(cl_command_queue) = clReleaseCommandQueue;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clReleaseContext_pfn)(cl_context) = clReleaseContext;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clReleaseDevice_pfn)(cl_device_id) = clReleaseDevice;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clReleaseEvent_pfn)(cl_event) = clReleaseEvent;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clReleaseKernel_pfn)(cl_kernel) = clReleaseKernel;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clReleaseMemObject_pfn)(cl_mem) = clReleaseMemObject;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clReleaseProgram_pfn)(cl_program) = clReleaseProgram;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clReleaseSampler_pfn)(cl_sampler) = clReleaseSampler;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clRetainCommandQueue_pfn)(cl_command_queue) = clRetainCommandQueue;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clRetainContext_pfn)(cl_context) = clRetainContext;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clRetainDevice_pfn)(cl_device_id) = clRetainDevice;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clRetainEvent_pfn)(cl_event) = clRetainEvent;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clRetainKernel_pfn)(cl_kernel) = clRetainKernel;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clRetainMemObject_pfn)(cl_mem) = clRetainMemObject;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clRetainProgram_pfn)(cl_program) = clRetainProgram;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clRetainSampler_pfn)(cl_sampler) = clRetainSampler;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clSetEventCallback_pfn)(cl_event, cl_int, void (CL_CALLBACK*) (cl_event, cl_int, void*), void*) = clSetEventCallback;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clSetKernelArg_pfn)(cl_kernel, cl_uint, size_t, const void*) = clSetKernelArg;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clSetMemObjectDestructorCallback_pfn)(cl_mem, void (CL_CALLBACK*) (cl_mem, void*), void*) = clSetMemObjectDestructorCallback;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clSetUserEventStatus_pfn)(cl_event, cl_int) = clSetUserEventStatus;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clUnloadCompiler_pfn)() = clUnloadCompiler;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clUnloadPlatformCompiler_pfn)(cl_platform_id) = clUnloadPlatformCompiler;
CL_RUNTIME_EXPORT cl_int (CL_API_CALL*clWaitForEvents_pfn)(cl_uint, const cl_event*) = clWaitForEvents;
