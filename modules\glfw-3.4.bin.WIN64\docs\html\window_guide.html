<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Window guide</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div><div class="header">
  <div class="headertitle"><div class="title">Window guide</div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#window_object">Window objects</a><ul><li class="level2"><a href="#window_creation">Window creation</a><ul><li class="level3"><a href="#window_full_screen">Full screen windows</a></li>
<li class="level3"><a href="#window_windowed_full_screen">&quot;Windowed full screen&quot; windows</a></li>
</ul>
</li>
<li class="level2"><a href="#window_destruction">Window destruction</a></li>
<li class="level2"><a href="#window_hints">Window creation hints</a><ul><li class="level3"><a href="#window_hints_hard">Hard and soft constraints</a></li>
<li class="level3"><a href="#window_hints_wnd">Window related hints</a></li>
<li class="level3"><a href="#window_hints_fb">Framebuffer related hints</a></li>
<li class="level3"><a href="#window_hints_mtr">Monitor related hints</a></li>
<li class="level3"><a href="#window_hints_ctx">Context related hints</a></li>
<li class="level3"><a href="#window_hints_win32">Win32 specific hints</a></li>
<li class="level3"><a href="#window_hints_osx">macOS specific hints</a></li>
<li class="level3"><a href="#window_hints_wayland">Wayland specific window hints</a></li>
<li class="level3"><a href="#window_hints_x11">X11 specific window hints</a></li>
<li class="level3"><a href="#window_hints_values">Supported and default values</a></li>
</ul>
</li>
</ul>
</li>
<li class="level1"><a href="#window_events">Window event processing</a></li>
<li class="level1"><a href="#window_properties">Window properties and events</a><ul><li class="level2"><a href="#window_userptr">User pointer</a></li>
<li class="level2"><a href="#window_close">Window closing and close flag</a></li>
<li class="level2"><a href="#window_size">Window size</a></li>
<li class="level2"><a href="#window_fbsize">Framebuffer size</a></li>
<li class="level2"><a href="#window_scale">Window content scale</a></li>
<li class="level2"><a href="#window_sizelimits">Window size limits</a></li>
<li class="level2"><a href="#window_pos">Window position</a></li>
<li class="level2"><a href="#window_title">Window title</a></li>
<li class="level2"><a href="#window_icon">Window icon</a></li>
<li class="level2"><a href="#window_monitor">Window monitor</a></li>
<li class="level2"><a href="#window_iconify">Window iconification</a></li>
<li class="level2"><a href="#window_maximize">Window maximization</a></li>
<li class="level2"><a href="#window_hide">Window visibility</a></li>
<li class="level2"><a href="#window_focus">Window input focus</a></li>
<li class="level2"><a href="#window_attention">Window attention request</a></li>
<li class="level2"><a href="#window_refresh">Window damage and refresh</a></li>
<li class="level2"><a href="#window_transparency">Window transparency</a></li>
<li class="level2"><a href="#window_attribs">Window attributes</a><ul><li class="level3"><a href="#window_attribs_wnd">Window related attributes</a></li>
<li class="level3"><a href="#window_attribs_ctx">Context related attributes</a></li>
<li class="level3"><a href="#window_attribs_fb">Framebuffer related attributes</a></li>
</ul>
</li>
</ul>
</li>
<li class="level1"><a href="#buffer_swap">Buffer swapping</a></li>
</ul>
</div>
<div class="textblock"><p>This guide introduces the window related functions of GLFW. For details on a specific function in this category, see the <a class="el" href="group__window.html">Window reference</a>. There are also guides for the other areas of GLFW.</p>
<ul>
<li><a class="el" href="intro_guide.html">Introduction to the API</a></li>
<li><a class="el" href="context_guide.html">Context guide</a></li>
<li><a class="el" href="vulkan_guide.html">Vulkan guide</a></li>
<li><a class="el" href="monitor_guide.html">Monitor guide</a></li>
<li><a class="el" href="input_guide.html">Input guide</a></li>
</ul>
<h1><a class="anchor" id="window_object"></a>
Window objects</h1>
<p>The <a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> object encapsulates both a window and a context. They are created with <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a> and destroyed with <a class="el" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a>, or <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a>, if any remain. As the window and context are inseparably linked, the object pointer is used as both a context and window handle.</p>
<p>To see the event stream provided to the various window related callbacks, run the <code>events</code> test program.</p>
<h2><a class="anchor" id="window_creation"></a>
Window creation</h2>
<p>A window and its OpenGL or OpenGL ES context are created with <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>, which returns a handle to the created window object. For example, this creates a 640 by 480 windowed mode window:</p>
<div class="fragment"><div class="line"><a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window = <a class="code hl_function" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>(640, 480, <span class="stringliteral">&quot;My Title&quot;</span>, NULL, NULL);</div>
<div class="ttc" id="agroup__window_html_ga3555a418df92ad53f917597fe2f64aeb"><div class="ttname"><a href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a></div><div class="ttdeci">GLFWwindow * glfwCreateWindow(int width, int height, const char *title, GLFWmonitor *monitor, GLFWwindow *share)</div><div class="ttdoc">Creates a window and its associated context.</div></div>
<div class="ttc" id="agroup__window_html_ga3c96d80d363e67d13a41b5d1821f3242"><div class="ttname"><a href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a></div><div class="ttdeci">struct GLFWwindow GLFWwindow</div><div class="ttdoc">Opaque window object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1403</div></div>
</div><!-- fragment --><p>If window creation fails, <code>NULL</code> will be returned, so it is necessary to check the return value.</p>
<p>The window handle is passed to all window related functions and is provided to along with all input events, so event handlers can tell which window received the event.</p>
<h3><a class="anchor" id="window_full_screen"></a>
Full screen windows</h3>
<p>To create a full screen window, you need to specify which monitor the window should use. In most cases, the user's primary monitor is a good choice. For more information about retrieving monitors, see <a class="el" href="monitor_guide.html#monitor_monitors">Retrieving monitors</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window = <a class="code hl_function" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>(640, 480, <span class="stringliteral">&quot;My Title&quot;</span>, <a class="code hl_function" href="group__monitor.html#gac3adb24947eb709e1874028272e5dfc5">glfwGetPrimaryMonitor</a>(), NULL);</div>
<div class="ttc" id="agroup__monitor_html_gac3adb24947eb709e1874028272e5dfc5"><div class="ttname"><a href="group__monitor.html#gac3adb24947eb709e1874028272e5dfc5">glfwGetPrimaryMonitor</a></div><div class="ttdeci">GLFWmonitor * glfwGetPrimaryMonitor(void)</div><div class="ttdoc">Returns the primary monitor.</div></div>
</div><!-- fragment --><p>Full screen windows cover the entire display area of a monitor, have no border or decorations.</p>
<p>Windowed mode windows can be made full screen by setting a monitor with <a class="el" href="group__window.html#ga81c76c418af80a1cce7055bccb0ae0a7">glfwSetWindowMonitor</a>, and full screen ones can be made windowed by unsetting it with the same function.</p>
<p>Each field of the <a class="el" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a> structure corresponds to a function parameter or window hint and combine to form the <em>desired video mode</em> for that window. The supported video mode most closely matching the desired video mode will be set for the chosen monitor as long as the window has input focus. For more information about retrieving video modes, see <a class="el" href="monitor_guide.html#monitor_modes">Video modes</a>.</p>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Video mode field   </th><th class="markdownTableHeadNone">Corresponds to    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><a class="el" href="struct_g_l_f_wvidmode.html#a698dcb200562051a7249cb6ae154c71d">GLFWvidmode.width</a>   </td><td class="markdownTableBodyNone"><code>width</code> parameter of <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><a class="el" href="struct_g_l_f_wvidmode.html#ac65942a5f6981695517437a9d571d03c">GLFWvidmode.height</a>   </td><td class="markdownTableBodyNone"><code>height</code> parameter of <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><a class="el" href="struct_g_l_f_wvidmode.html#a6066c4ecd251098700062d3b735dba1b">GLFWvidmode.redBits</a>   </td><td class="markdownTableBodyNone"><a class="el" href="window_guide.html#GLFW_RED_BITS">GLFW_RED_BITS</a> hint    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><a class="el" href="struct_g_l_f_wvidmode.html#a292fdd281f3485fb3ff102a5bda43faa">GLFWvidmode.greenBits</a>   </td><td class="markdownTableBodyNone"><a class="el" href="window_guide.html#GLFW_GREEN_BITS">GLFW_GREEN_BITS</a> hint    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><a class="el" href="struct_g_l_f_wvidmode.html#af310977f58d2e3b188175b6e3d314047">GLFWvidmode.blueBits</a>   </td><td class="markdownTableBodyNone"><a class="el" href="window_guide.html#GLFW_BLUE_BITS">GLFW_BLUE_BITS</a> hint    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><a class="el" href="struct_g_l_f_wvidmode.html#a791bdd6c7697b09f7e9c97054bf05649">GLFWvidmode.refreshRate</a>   </td><td class="markdownTableBodyNone"><a class="el" href="window_guide.html#GLFW_REFRESH_RATE">GLFW_REFRESH_RATE</a> hint   </td></tr>
</table>
<p>Once you have a full screen window, you can change its resolution, refresh rate and monitor with <a class="el" href="group__window.html#ga81c76c418af80a1cce7055bccb0ae0a7">glfwSetWindowMonitor</a>. If you only need change its resolution you can also call <a class="el" href="group__window.html#ga371911f12c74c504dd8d47d832d095cb">glfwSetWindowSize</a>. In all cases, the new video mode will be selected the same way as the video mode chosen by <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>. If the window has an OpenGL or OpenGL ES context, it will be unaffected.</p>
<p>By default, the original video mode of the monitor will be restored and the window iconified if it loses input focus, to allow the user to switch back to the desktop. This behavior can be disabled with the <a class="el" href="window_guide.html#GLFW_AUTO_ICONIFY_hint">GLFW_AUTO_ICONIFY</a> window hint, for example if you wish to simultaneously cover multiple monitors with full screen windows.</p>
<p>If a monitor is disconnected, all windows that are full screen on that monitor will be switched to windowed mode. See <a class="el" href="monitor_guide.html#monitor_event">Monitor configuration changes</a> for more information.</p>
<h3><a class="anchor" id="window_windowed_full_screen"></a>
"Windowed full screen" windows</h3>
<p>If the closest match for the desired video mode is the current one, the video mode will not be changed, making window creation faster and application switching much smoother. This is sometimes called <em>windowed full screen</em> or <em>borderless full screen</em> window and counts as a full screen window. To create such a window, request the current video mode.</p>
<div class="fragment"><div class="line"><span class="keyword">const</span> <a class="code hl_struct" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a>* mode = <a class="code hl_function" href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a>(monitor);</div>
<div class="line"> </div>
<div class="line"><a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#gaf78ed8e417dbcc1e354906cc2708c982">GLFW_RED_BITS</a>, mode-&gt;<a class="code hl_variable" href="struct_g_l_f_wvidmode.html#a6066c4ecd251098700062d3b735dba1b">redBits</a>);</div>
<div class="line"><a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#gafba3b72638c914e5fb8a237dd4c50d4d">GLFW_GREEN_BITS</a>, mode-&gt;<a class="code hl_variable" href="struct_g_l_f_wvidmode.html#a292fdd281f3485fb3ff102a5bda43faa">greenBits</a>);</div>
<div class="line"><a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#gab292ea403db6d514537b515311bf9ae3">GLFW_BLUE_BITS</a>, mode-&gt;<a class="code hl_variable" href="struct_g_l_f_wvidmode.html#af310977f58d2e3b188175b6e3d314047">blueBits</a>);</div>
<div class="line"><a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#ga0f20825e6e47ee8ba389024519682212">GLFW_REFRESH_RATE</a>, mode-&gt;<a class="code hl_variable" href="struct_g_l_f_wvidmode.html#a791bdd6c7697b09f7e9c97054bf05649">refreshRate</a>);</div>
<div class="line"> </div>
<div class="line"><a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window = <a class="code hl_function" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>(mode-&gt;<a class="code hl_variable" href="struct_g_l_f_wvidmode.html#a698dcb200562051a7249cb6ae154c71d">width</a>, mode-&gt;<a class="code hl_variable" href="struct_g_l_f_wvidmode.html#ac65942a5f6981695517437a9d571d03c">height</a>, <span class="stringliteral">&quot;My Title&quot;</span>, monitor, NULL);</div>
<div class="ttc" id="agroup__monitor_html_gaba376fa7e76634b4788bddc505d6c9d5"><div class="ttname"><a href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a></div><div class="ttdeci">const GLFWvidmode * glfwGetVideoMode(GLFWmonitor *monitor)</div><div class="ttdoc">Returns the current mode of the specified monitor.</div></div>
<div class="ttc" id="agroup__window_html_ga0f20825e6e47ee8ba389024519682212"><div class="ttname"><a href="group__window.html#ga0f20825e6e47ee8ba389024519682212">GLFW_REFRESH_RATE</a></div><div class="ttdeci">#define GLFW_REFRESH_RATE</div><div class="ttdoc">Monitor refresh rate hint.</div><div class="ttdef"><b>Definition</b> glfw3.h:1018</div></div>
<div class="ttc" id="agroup__window_html_ga7d9c8c62384b1e2821c4dc48952d2033"><div class="ttname"><a href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a></div><div class="ttdeci">void glfwWindowHint(int hint, int value)</div><div class="ttdoc">Sets the specified window hint to the desired value.</div></div>
<div class="ttc" id="agroup__window_html_gab292ea403db6d514537b515311bf9ae3"><div class="ttname"><a href="group__window.html#gab292ea403db6d514537b515311bf9ae3">GLFW_BLUE_BITS</a></div><div class="ttdeci">#define GLFW_BLUE_BITS</div><div class="ttdoc">Framebuffer bit depth hint.</div><div class="ttdef"><b>Definition</b> glfw3.h:958</div></div>
<div class="ttc" id="agroup__window_html_gaf78ed8e417dbcc1e354906cc2708c982"><div class="ttname"><a href="group__window.html#gaf78ed8e417dbcc1e354906cc2708c982">GLFW_RED_BITS</a></div><div class="ttdeci">#define GLFW_RED_BITS</div><div class="ttdoc">Framebuffer bit depth hint.</div><div class="ttdef"><b>Definition</b> glfw3.h:948</div></div>
<div class="ttc" id="agroup__window_html_gafba3b72638c914e5fb8a237dd4c50d4d"><div class="ttname"><a href="group__window.html#gafba3b72638c914e5fb8a237dd4c50d4d">GLFW_GREEN_BITS</a></div><div class="ttdeci">#define GLFW_GREEN_BITS</div><div class="ttdoc">Framebuffer bit depth hint.</div><div class="ttdef"><b>Definition</b> glfw3.h:953</div></div>
<div class="ttc" id="astruct_g_l_f_wvidmode_html"><div class="ttname"><a href="struct_g_l_f_wvidmode.html">GLFWvidmode</a></div><div class="ttdoc">Video mode type.</div><div class="ttdef"><b>Definition</b> glfw3.h:2027</div></div>
<div class="ttc" id="astruct_g_l_f_wvidmode_html_a292fdd281f3485fb3ff102a5bda43faa"><div class="ttname"><a href="struct_g_l_f_wvidmode.html#a292fdd281f3485fb3ff102a5bda43faa">GLFWvidmode::greenBits</a></div><div class="ttdeci">int greenBits</div><div class="ttdef"><b>Definition</b> glfw3.h:2039</div></div>
<div class="ttc" id="astruct_g_l_f_wvidmode_html_a6066c4ecd251098700062d3b735dba1b"><div class="ttname"><a href="struct_g_l_f_wvidmode.html#a6066c4ecd251098700062d3b735dba1b">GLFWvidmode::redBits</a></div><div class="ttdeci">int redBits</div><div class="ttdef"><b>Definition</b> glfw3.h:2036</div></div>
<div class="ttc" id="astruct_g_l_f_wvidmode_html_a698dcb200562051a7249cb6ae154c71d"><div class="ttname"><a href="struct_g_l_f_wvidmode.html#a698dcb200562051a7249cb6ae154c71d">GLFWvidmode::width</a></div><div class="ttdeci">int width</div><div class="ttdef"><b>Definition</b> glfw3.h:2030</div></div>
<div class="ttc" id="astruct_g_l_f_wvidmode_html_a791bdd6c7697b09f7e9c97054bf05649"><div class="ttname"><a href="struct_g_l_f_wvidmode.html#a791bdd6c7697b09f7e9c97054bf05649">GLFWvidmode::refreshRate</a></div><div class="ttdeci">int refreshRate</div><div class="ttdef"><b>Definition</b> glfw3.h:2045</div></div>
<div class="ttc" id="astruct_g_l_f_wvidmode_html_ac65942a5f6981695517437a9d571d03c"><div class="ttname"><a href="struct_g_l_f_wvidmode.html#ac65942a5f6981695517437a9d571d03c">GLFWvidmode::height</a></div><div class="ttdeci">int height</div><div class="ttdef"><b>Definition</b> glfw3.h:2033</div></div>
<div class="ttc" id="astruct_g_l_f_wvidmode_html_af310977f58d2e3b188175b6e3d314047"><div class="ttname"><a href="struct_g_l_f_wvidmode.html#af310977f58d2e3b188175b6e3d314047">GLFWvidmode::blueBits</a></div><div class="ttdeci">int blueBits</div><div class="ttdef"><b>Definition</b> glfw3.h:2042</div></div>
</div><!-- fragment --><p>This also works for windowed mode windows that are made full screen.</p>
<div class="fragment"><div class="line"><span class="keyword">const</span> <a class="code hl_struct" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a>* mode = <a class="code hl_function" href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a>(monitor);</div>
<div class="line"> </div>
<div class="line"><a class="code hl_function" href="group__window.html#ga81c76c418af80a1cce7055bccb0ae0a7">glfwSetWindowMonitor</a>(window, monitor, 0, 0, mode-&gt;<a class="code hl_variable" href="struct_g_l_f_wvidmode.html#a698dcb200562051a7249cb6ae154c71d">width</a>, mode-&gt;<a class="code hl_variable" href="struct_g_l_f_wvidmode.html#ac65942a5f6981695517437a9d571d03c">height</a>, mode-&gt;<a class="code hl_variable" href="struct_g_l_f_wvidmode.html#a791bdd6c7697b09f7e9c97054bf05649">refreshRate</a>);</div>
<div class="ttc" id="agroup__window_html_ga81c76c418af80a1cce7055bccb0ae0a7"><div class="ttname"><a href="group__window.html#ga81c76c418af80a1cce7055bccb0ae0a7">glfwSetWindowMonitor</a></div><div class="ttdeci">void glfwSetWindowMonitor(GLFWwindow *window, GLFWmonitor *monitor, int xpos, int ypos, int width, int height, int refreshRate)</div><div class="ttdoc">Sets the mode, monitor, video mode and placement of a window.</div></div>
</div><!-- fragment --><p>Note that <a class="el" href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a> returns the <em>current</em> video mode of a monitor, so if you already have a full screen window on that monitor that you want to make windowed full screen, you need to have saved the desktop resolution before.</p>
<h2><a class="anchor" id="window_destruction"></a>
Window destruction</h2>
<p>When a window is no longer needed, destroy it with <a class="el" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a>(window);</div>
<div class="ttc" id="agroup__window_html_gacdf43e51376051d2c091662e9fe3d7b2"><div class="ttname"><a href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a></div><div class="ttdeci">void glfwDestroyWindow(GLFWwindow *window)</div><div class="ttdoc">Destroys the specified window and its context.</div></div>
</div><!-- fragment --><p>Window destruction always succeeds. Before the actual destruction, all callbacks are removed so no further events will be delivered for the window. All windows remaining when <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a> is called are destroyed as well.</p>
<p>When a full screen window is destroyed, the original video mode of its monitor is restored, but the gamma ramp is left untouched.</p>
<h2><a class="anchor" id="window_hints"></a>
Window creation hints</h2>
<p>There are a number of hints that can be set before the creation of a window and context. Some affect the window itself, others affect the framebuffer or context. These hints are set to their default values each time the library is initialized with <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a>. Integer value hints can be set individually with <a class="el" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a> and string value hints with <a class="el" href="group__window.html#ga8cb2782861c9d997bcf2dea97f363e5f">glfwWindowHintString</a>. You can reset all at once to their defaults with <a class="el" href="group__window.html#gaa77c4898dfb83344a6b4f76aa16b9a4a">glfwDefaultWindowHints</a>.</p>
<p>Some hints are platform specific. These are always valid to set on any platform but they will only affect their specific platform. Other platforms will ignore them. Setting these hints requires no platform specific headers or calls.</p>
<dl class="section note"><dt>Note</dt><dd>Window hints need to be set before the creation of the window and context you wish to have the specified attributes. They function as additional arguments to <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>.</dd></dl>
<h3><a class="anchor" id="window_hints_hard"></a>
Hard and soft constraints</h3>
<p>Some window hints are hard constraints. These must match the available capabilities <em>exactly</em> for window and context creation to succeed. Hints that are not hard constraints are matched as closely as possible, but the resulting context and framebuffer may differ from what these hints requested.</p>
<p>The following hints are always hard constraints:</p><ul>
<li><a class="el" href="window_guide.html#GLFW_STEREO">GLFW_STEREO</a></li>
<li><a class="el" href="window_guide.html#GLFW_DOUBLEBUFFER">GLFW_DOUBLEBUFFER</a></li>
<li><a class="el" href="window_guide.html#GLFW_CLIENT_API_hint">GLFW_CLIENT_API</a></li>
<li><a class="el" href="window_guide.html#GLFW_CONTEXT_CREATION_API_hint">GLFW_CONTEXT_CREATION_API</a></li>
</ul>
<p>The following additional hints are hard constraints when requesting an OpenGL context, but are ignored when requesting an OpenGL ES context:</p><ul>
<li><a class="el" href="window_guide.html#GLFW_OPENGL_FORWARD_COMPAT_hint">GLFW_OPENGL_FORWARD_COMPAT</a></li>
<li><a class="el" href="window_guide.html#GLFW_OPENGL_PROFILE_hint">GLFW_OPENGL_PROFILE</a></li>
</ul>
<h3><a class="anchor" id="window_hints_wnd"></a>
Window related hints</h3>
<p><a class="anchor" id="GLFW_RESIZABLE_hint"></a><b>GLFW_RESIZABLE</b> specifies whether the windowed mode window will be resizable <em>by the user</em>. The window will still be resizable using the <a class="el" href="group__window.html#ga371911f12c74c504dd8d47d832d095cb">glfwSetWindowSize</a> function. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>. This hint is ignored for full screen and undecorated windows.</p>
<p><a class="anchor" id="GLFW_VISIBLE_hint"></a><b>GLFW_VISIBLE</b> specifies whether the windowed mode window will be initially visible. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>. This hint is ignored for full screen windows.</p>
<p><a class="anchor" id="GLFW_DECORATED_hint"></a><b>GLFW_DECORATED</b> specifies whether the windowed mode window will have window decorations such as a border, a close widget, etc. An undecorated window will not be resizable by the user but will still allow the user to generate close events on some platforms. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>. This hint is ignored for full screen windows.</p>
<p><a class="anchor" id="GLFW_FOCUSED_hint"></a><b>GLFW_FOCUSED</b> specifies whether the windowed mode window will be given input focus when created. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>. This hint is ignored for full screen and initially hidden windows.</p>
<p><a class="anchor" id="GLFW_AUTO_ICONIFY_hint"></a><b>GLFW_AUTO_ICONIFY</b> specifies whether the full screen window will automatically iconify and restore the previous video mode on input focus loss. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>. This hint is ignored for windowed mode windows.</p>
<p><a class="anchor" id="GLFW_FLOATING_hint"></a><b>GLFW_FLOATING</b> specifies whether the windowed mode window will be floating above other regular windows, also called topmost or always-on-top. This is intended primarily for debugging purposes and cannot be used to implement proper full screen windows. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>. This hint is ignored for full screen windows.</p>
<p><a class="anchor" id="GLFW_MAXIMIZED_hint"></a><b>GLFW_MAXIMIZED</b> specifies whether the windowed mode window will be maximized when created. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>. This hint is ignored for full screen windows.</p>
<p><a class="anchor" id="GLFW_CENTER_CURSOR_hint"></a><b>GLFW_CENTER_CURSOR</b> specifies whether the cursor should be centered over newly created full screen windows. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>. This hint is ignored for windowed mode windows.</p>
<p><a class="anchor" id="GLFW_TRANSPARENT_FRAMEBUFFER_hint"></a><b>GLFW_TRANSPARENT_FRAMEBUFFER</b> specifies whether the window framebuffer will be transparent. If enabled and supported by the system, the window framebuffer alpha channel will be used to combine the framebuffer with the background. This does not affect window decorations. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>.</p>
<p><a class="anchor" id="GLFW_FOCUS_ON_SHOW_hint"></a><b>GLFW_FOCUS_ON_SHOW</b> specifies whether the window will be given input focus when <a class="el" href="group__window.html#ga61be47917b72536a148300f46494fc66">glfwShowWindow</a> is called. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>.</p>
<p><a class="anchor" id="GLFW_SCALE_TO_MONITOR"></a><b>GLFW_SCALE_TO_MONITOR</b> specified whether the window content area should be resized based on <a class="el" href="window_guide.html#window_scale">content scale</a> changes. This can be because of a global user settings change or because the window was moved to a monitor with different scale settings.</p>
<p>This hint only has an effect on platforms where screen coordinates and pixels always map 1:1, such as Windows and X11. On platforms like macOS the resolution of the framebuffer can change independently of the window size.</p>
<p><a class="anchor" id="GLFW_SCALE_FRAMEBUFFER_hint"></a><a class="anchor" id="GLFW_COCOA_RETINA_FRAMEBUFFER_hint"></a><b>GLFW_SCALE_FRAMEBUFFER</b> specifies whether the framebuffer should be resized based on <a class="el" href="window_guide.html#window_scale">content scale</a> changes. This can be because of a global user settings change or because the window was moved to a monitor with different scale settings.</p>
<p>This hint only has an effect on platforms where screen coordinates can be scaled relative to pixel coordinates, such as macOS and Wayland. On platforms like Windows and X11 the framebuffer and window content area sizes always map 1:1.</p>
<p>This is the new name, introduced in GLFW 3.4. The older <code>GLFW_COCOA_RETINA_FRAMEBUFFER</code> name is also available for compatibility. Both names modify the same hint value.</p>
<p><a class="anchor" id="GLFW_MOUSE_PASSTHROUGH_hint"></a><b>GLFW_MOUSE_PASSTHROUGH</b> specifies whether the window is transparent to mouse input, letting any mouse events pass through to whatever window is behind it. This is only supported for undecorated windows. Decorated windows with this enabled will behave differently between platforms. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>.</p>
<p><a class="anchor" id="GLFW_POSITION_X"></a><a class="anchor" id="GLFW_POSITION_Y"></a><b>GLFW_POSITION_X</b> and <b>GLFW_POSITION_Y</b> specify the desired initial position of the window. The window manager may modify or ignore these coordinates. If either or both of these hints are set to <code>GLFW_ANY_POSITION</code> then the window manager will position the window where it thinks the user will prefer it. Possible values are any valid screen coordinates and <code>GLFW_ANY_POSITION</code>.</p>
<h3><a class="anchor" id="window_hints_fb"></a>
Framebuffer related hints</h3>
<p><a class="anchor" id="GLFW_RED_BITS"></a><a class="anchor" id="GLFW_GREEN_BITS"></a><a class="anchor" id="GLFW_BLUE_BITS"></a><a class="anchor" id="GLFW_ALPHA_BITS"></a><a class="anchor" id="GLFW_DEPTH_BITS"></a><a class="anchor" id="GLFW_STENCIL_BITS"></a><b>GLFW_RED_BITS</b>, <b>GLFW_GREEN_BITS</b>, <b>GLFW_BLUE_BITS</b>, <b>GLFW_ALPHA_BITS</b>, <b>GLFW_DEPTH_BITS</b> and <b>GLFW_STENCIL_BITS</b> specify the desired bit depths of the various components of the default framebuffer. A value of <code>GLFW_DONT_CARE</code> means the application has no preference.</p>
<p><a class="anchor" id="GLFW_ACCUM_RED_BITS"></a><a class="anchor" id="GLFW_ACCUM_GREEN_BITS"></a><a class="anchor" id="GLFW_ACCUM_BLUE_BITS"></a><a class="anchor" id="GLFW_ACCUM_ALPHA_BITS"></a><b>GLFW_ACCUM_RED_BITS</b>, <b>GLFW_ACCUM_GREEN_BITS</b>, <b>GLFW_ACCUM_BLUE_BITS</b> and <b>GLFW_ACCUM_ALPHA_BITS</b> specify the desired bit depths of the various components of the accumulation buffer. A value of <code>GLFW_DONT_CARE</code> means the application has no preference.</p>
<p>Accumulation buffers are a legacy OpenGL feature and should not be used in new code.</p>
<p><a class="anchor" id="GLFW_AUX_BUFFERS"></a><b>GLFW_AUX_BUFFERS</b> specifies the desired number of auxiliary buffers. A value of <code>GLFW_DONT_CARE</code> means the application has no preference.</p>
<p>Auxiliary buffers are a legacy OpenGL feature and should not be used in new code.</p>
<p><a class="anchor" id="GLFW_STEREO"></a><b>GLFW_STEREO</b> specifies whether to use OpenGL stereoscopic rendering. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>. This is a hard constraint.</p>
<p><a class="anchor" id="GLFW_SAMPLES"></a><b>GLFW_SAMPLES</b> specifies the desired number of samples to use for multisampling. Zero disables multisampling. A value of <code>GLFW_DONT_CARE</code> means the application has no preference.</p>
<p><a class="anchor" id="GLFW_SRGB_CAPABLE"></a><b>GLFW_SRGB_CAPABLE</b> specifies whether the framebuffer should be sRGB capable. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>.</p>
<dl class="section note"><dt>Note</dt><dd><b>OpenGL:</b> If enabled and supported by the system, the <code>GL_FRAMEBUFFER_SRGB</code> enable will control sRGB rendering. By default, sRGB rendering will be disabled.</dd>
<dd>
<b>OpenGL ES:</b> If enabled and supported by the system, the context will always have sRGB rendering enabled.</dd></dl>
<p><a class="anchor" id="GLFW_DOUBLEBUFFER"></a><a class="anchor" id="GLFW_DOUBLEBUFFER_hint"></a><b>GLFW_DOUBLEBUFFER</b> specifies whether the framebuffer should be double buffered. You nearly always want to use double buffering. This is a hard constraint. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>.</p>
<h3><a class="anchor" id="window_hints_mtr"></a>
Monitor related hints</h3>
<p><a class="anchor" id="GLFW_REFRESH_RATE"></a><b>GLFW_REFRESH_RATE</b> specifies the desired refresh rate for full screen windows. A value of <code>GLFW_DONT_CARE</code> means the highest available refresh rate will be used. This hint is ignored for windowed mode windows.</p>
<h3><a class="anchor" id="window_hints_ctx"></a>
Context related hints</h3>
<p><a class="anchor" id="GLFW_CLIENT_API_hint"></a><b>GLFW_CLIENT_API</b> specifies which client API to create the context for. Possible values are <code>GLFW_OPENGL_API</code>, <code>GLFW_OPENGL_ES_API</code> and <code>GLFW_NO_API</code>. This is a hard constraint.</p>
<p><a class="anchor" id="GLFW_CONTEXT_CREATION_API_hint"></a><b>GLFW_CONTEXT_CREATION_API</b> specifies which context creation API to use to create the context. Possible values are <code>GLFW_NATIVE_CONTEXT_API</code>, <code>GLFW_EGL_CONTEXT_API</code> and <code>GLFW_OSMESA_CONTEXT_API</code>. This is a hard constraint. If no client API is requested, this hint is ignored.</p>
<p>An <a class="el" href="context_guide.html#context_glext_auto">extension loader library</a> that assumes it knows which API was used to create the current context may fail if you change this hint. This can be resolved by having it load functions via <a class="el" href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a>.</p>
<dl class="section note"><dt>Note</dt><dd><b>Wayland:</b> The EGL API <em>is</em> the native context creation API, so this hint will have no effect.</dd>
<dd>
<b>X11:</b> On some Linux systems, creating contexts via both the native and EGL APIs in a single process will cause the application to segfault. Stick to one API or the other on Linux for now.</dd>
<dd>
<b>OSMesa:</b> As its name implies, an OpenGL context created with OSMesa does not update the window contents when its buffers are swapped. Use OpenGL functions or the OSMesa native access functions <a class="el" href="group__native.html#ga3b36e3e3dcf308b776427b6bd73cc132">glfwGetOSMesaColorBuffer</a> and <a class="el" href="group__native.html#ga6b64039ffc88a7a2f57f0956c0c75d53">glfwGetOSMesaDepthBuffer</a> to retrieve the framebuffer contents.</dd></dl>
<p><a class="anchor" id="GLFW_CONTEXT_VERSION_MAJOR_hint"></a><a class="anchor" id="GLFW_CONTEXT_VERSION_MINOR_hint"></a><b>GLFW_CONTEXT_VERSION_MAJOR</b> and <b>GLFW_CONTEXT_VERSION_MINOR</b> specify the client API version that the created context must be compatible with. The exact behavior of these hints depend on the requested client API.</p>
<p>While there is no way to ask the driver for a context of the highest supported version, GLFW will attempt to provide this when you ask for a version 1.0 context, which is the default for these hints.</p>
<p>Do not confuse these hints with <a class="el" href="group__init.html#ga6337d9ea43b22fc529b2bba066b4a576">GLFW_VERSION_MAJOR</a> and <a class="el" href="group__init.html#gaf80d40f0aea7088ff337606e9c48f7a3">GLFW_VERSION_MINOR</a>, which provide the API version of the GLFW header.</p>
<dl class="section note"><dt>Note</dt><dd><b>OpenGL:</b> These hints are not hard constraints, but creation will fail if the OpenGL version of the created context is less than the one requested. It is therefore perfectly safe to use the default of version 1.0 for legacy code and you will still get backwards-compatible contexts of version 3.0 and above when available.</dd>
<dd>
<b>OpenGL ES:</b> These hints are not hard constraints, but creation will fail if the OpenGL ES version of the created context is less than the one requested. Additionally, OpenGL ES 1.x cannot be returned if 2.0 or later was requested, and vice versa. This is because OpenGL ES 3.x is backward compatible with 2.0, but OpenGL ES 2.0 is not backward compatible with 1.x.</dd>
<dd>
<b>macOS:</b> The OS only supports core profile contexts for OpenGL versions 3.2 and later. Before creating an OpenGL context of version 3.2 or later you must set the <a class="el" href="window_guide.html#GLFW_OPENGL_PROFILE_hint">GLFW_OPENGL_PROFILE</a> hint accordingly. OpenGL 3.0 and 3.1 contexts are not supported at all on macOS.</dd></dl>
<p><a class="anchor" id="GLFW_OPENGL_FORWARD_COMPAT_hint"></a><b>GLFW_OPENGL_FORWARD_COMPAT</b> specifies whether the OpenGL context should be forward-compatible, i.e. one where all functionality deprecated in the requested version of OpenGL is removed. This must only be used if the requested OpenGL version is 3.0 or above. If OpenGL ES is requested, this hint is ignored.</p>
<p>Forward-compatibility is described in detail in the <a href="https://www.opengl.org/registry/">OpenGL Reference Manual</a>.</p>
<p><a class="anchor" id="GLFW_CONTEXT_DEBUG_hint"></a><a class="anchor" id="GLFW_OPENGL_DEBUG_CONTEXT_hint"></a><b>GLFW_CONTEXT_DEBUG</b> specifies whether the context should be created in debug mode, which may provide additional error and diagnostic reporting functionality. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>.</p>
<p>Debug contexts for OpenGL and OpenGL ES are described in detail by the <a href="https://www.khronos.org/registry/OpenGL/extensions/KHR/KHR_debug.txt">GL_KHR_debug</a> extension.</p>
<dl class="section note"><dt>Note</dt><dd><code>GLFW_CONTEXT_DEBUG</code> is the new name introduced in GLFW 3.4. The older <code>GLFW_OPENGL_DEBUG_CONTEXT</code> name is also available for compatibility.</dd></dl>
<p><a class="anchor" id="GLFW_OPENGL_PROFILE_hint"></a><b>GLFW_OPENGL_PROFILE</b> specifies which OpenGL profile to create the context for. Possible values are one of <code>GLFW_OPENGL_CORE_PROFILE</code> or <code>GLFW_OPENGL_COMPAT_PROFILE</code>, or <code>GLFW_OPENGL_ANY_PROFILE</code> to not request a specific profile. If requesting an OpenGL version below 3.2, <code>GLFW_OPENGL_ANY_PROFILE</code> must be used. If OpenGL ES is requested, this hint is ignored.</p>
<p>OpenGL profiles are described in detail in the <a href="https://www.opengl.org/registry/">OpenGL Reference Manual</a>.</p>
<p><a class="anchor" id="GLFW_CONTEXT_ROBUSTNESS_hint"></a><b>GLFW_CONTEXT_ROBUSTNESS</b> specifies the robustness strategy to be used by the context. This can be one of <code>GLFW_NO_RESET_NOTIFICATION</code> or <code>GLFW_LOSE_CONTEXT_ON_RESET</code>, or <code>GLFW_NO_ROBUSTNESS</code> to not request a robustness strategy.</p>
<p><a class="anchor" id="GLFW_CONTEXT_RELEASE_BEHAVIOR_hint"></a><b>GLFW_CONTEXT_RELEASE_BEHAVIOR</b> specifies the release behavior to be used by the context. Possible values are one of <code>GLFW_ANY_RELEASE_BEHAVIOR</code>, <code>GLFW_RELEASE_BEHAVIOR_FLUSH</code> or <code>GLFW_RELEASE_BEHAVIOR_NONE</code>. If the behavior is <code>GLFW_ANY_RELEASE_BEHAVIOR</code>, the default behavior of the context creation API will be used. If the behavior is <code>GLFW_RELEASE_BEHAVIOR_FLUSH</code>, the pipeline will be flushed whenever the context is released from being the current one. If the behavior is <code>GLFW_RELEASE_BEHAVIOR_NONE</code>, the pipeline will not be flushed on release.</p>
<p>Context release behaviors are described in detail by the <a href="https://www.opengl.org/registry/specs/KHR/context_flush_control.txt">GL_KHR_context_flush_control</a> extension.</p>
<p><a class="anchor" id="GLFW_CONTEXT_NO_ERROR_hint"></a><b>GLFW_CONTEXT_NO_ERROR</b> specifies whether errors should be generated by the context. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>. If enabled, situations that would have generated errors instead cause undefined behavior.</p>
<p>The no error mode for OpenGL and OpenGL ES is described in detail by the <a href="https://www.opengl.org/registry/specs/KHR/no_error.txt">GL_KHR_no_error</a> extension.</p>
<h3><a class="anchor" id="window_hints_win32"></a>
Win32 specific hints</h3>
<p><a class="anchor" id="GLFW_WIN32_KEYBOARD_MENU_hint"></a><b>GLFW_WIN32_KEYBOARD_MENU</b> specifies whether to allow access to the window menu via the Alt+Space and Alt-and-then-Space keyboard shortcuts. This is ignored on other platforms.</p>
<p><a class="anchor" id="GLFW_WIN32_SHOWDEFAULT_hint"></a><b>GLFW_WIN32_SHOWDEFAULT</b> specifies whether to show the window the way specified in the program's <code>STARTUPINFO</code> when it is shown for the first time. This is the same information as the <code>Run</code> option in the shortcut properties window. If this information was not specified when the program was started, GLFW behaves as if this hint was set to <code>GLFW_FALSE</code>. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>. This is ignored on other platforms.</p>
<h3><a class="anchor" id="window_hints_osx"></a>
macOS specific hints</h3>
<p><a class="anchor" id="GLFW_COCOA_FRAME_NAME_hint"></a><b>GLFW_COCOA_FRAME_NAME</b> specifies the UTF-8 encoded name to use for autosaving the window frame, or if empty disables frame autosaving for the window. This is ignored on other platforms. This is set with <a class="el" href="group__window.html#ga8cb2782861c9d997bcf2dea97f363e5f">glfwWindowHintString</a>.</p>
<p><a class="anchor" id="GLFW_COCOA_GRAPHICS_SWITCHING_hint"></a><b>GLFW_COCOA_GRAPHICS_SWITCHING</b> specifies whether to in Automatic Graphics Switching, i.e. to allow the system to choose the integrated GPU for the OpenGL context and move it between GPUs if necessary or whether to force it to always run on the discrete GPU. This only affects systems with both integrated and discrete GPUs. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>. This is ignored on other platforms.</p>
<p>Simpler programs and tools may want to enable this to save power, while games and other applications performing advanced rendering will want to leave it disabled.</p>
<p>A bundled application that wishes to participate in Automatic Graphics Switching should also declare this in its <code>Info.plist</code> by setting the <code>NSSupportsAutomaticGraphicsSwitching</code> key to <code>true</code>.</p>
<h3><a class="anchor" id="window_hints_wayland"></a>
Wayland specific window hints</h3>
<p><a class="anchor" id="GLFW_WAYLAND_APP_ID_hint"></a><b>GLFW_WAYLAND_APP_ID</b> specifies the Wayland app_id for a window, used by window managers to identify types of windows. This is set with <a class="el" href="group__window.html#ga8cb2782861c9d997bcf2dea97f363e5f">glfwWindowHintString</a>.</p>
<h3><a class="anchor" id="window_hints_x11"></a>
X11 specific window hints</h3>
<p><a class="anchor" id="GLFW_X11_CLASS_NAME_hint"></a><a class="anchor" id="GLFW_X11_INSTANCE_NAME_hint"></a><b>GLFW_X11_CLASS_NAME</b> and <b>GLFW_X11_INSTANCE_NAME</b> specifies the desired ASCII encoded class and instance parts of the ICCCM <code>WM_CLASS</code> window property. Both hints need to be set to something other than an empty string for them to take effect. These are set with <a class="el" href="group__window.html#ga8cb2782861c9d997bcf2dea97f363e5f">glfwWindowHintString</a>.</p>
<h3><a class="anchor" id="window_hints_values"></a>
Supported and default values</h3>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Window hint   </th><th class="markdownTableHeadNone">Default value   </th><th class="markdownTableHeadNone">Supported values    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_RESIZABLE   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_VISIBLE   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_DECORATED   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_FOCUSED   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_AUTO_ICONIFY   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_FLOATING   </td><td class="markdownTableBodyNone"><code>GLFW_FALSE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_MAXIMIZED   </td><td class="markdownTableBodyNone"><code>GLFW_FALSE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_CENTER_CURSOR   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_TRANSPARENT_FRAMEBUFFER   </td><td class="markdownTableBodyNone"><code>GLFW_FALSE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_FOCUS_ON_SHOW   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_SCALE_TO_MONITOR   </td><td class="markdownTableBodyNone"><code>GLFW_FALSE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_SCALE_FRAMEBUFFER   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_MOUSE_PASSTHROUGH   </td><td class="markdownTableBodyNone"><code>GLFW_FALSE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_POSITION_X   </td><td class="markdownTableBodyNone"><code>GLFW_ANY_POSITION</code>   </td><td class="markdownTableBodyNone">Any valid screen x-coordinate or <code>GLFW_ANY_POSITION</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_POSITION_Y   </td><td class="markdownTableBodyNone"><code>GLFW_ANY_POSITION</code>   </td><td class="markdownTableBodyNone">Any valid screen y-coordinate or <code>GLFW_ANY_POSITION</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_RED_BITS   </td><td class="markdownTableBodyNone">8   </td><td class="markdownTableBodyNone">0 to <code>INT_MAX</code> or <code>GLFW_DONT_CARE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_GREEN_BITS   </td><td class="markdownTableBodyNone">8   </td><td class="markdownTableBodyNone">0 to <code>INT_MAX</code> or <code>GLFW_DONT_CARE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_BLUE_BITS   </td><td class="markdownTableBodyNone">8   </td><td class="markdownTableBodyNone">0 to <code>INT_MAX</code> or <code>GLFW_DONT_CARE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_ALPHA_BITS   </td><td class="markdownTableBodyNone">8   </td><td class="markdownTableBodyNone">0 to <code>INT_MAX</code> or <code>GLFW_DONT_CARE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_DEPTH_BITS   </td><td class="markdownTableBodyNone">24   </td><td class="markdownTableBodyNone">0 to <code>INT_MAX</code> or <code>GLFW_DONT_CARE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_STENCIL_BITS   </td><td class="markdownTableBodyNone">8   </td><td class="markdownTableBodyNone">0 to <code>INT_MAX</code> or <code>GLFW_DONT_CARE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_ACCUM_RED_BITS   </td><td class="markdownTableBodyNone">0   </td><td class="markdownTableBodyNone">0 to <code>INT_MAX</code> or <code>GLFW_DONT_CARE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_ACCUM_GREEN_BITS   </td><td class="markdownTableBodyNone">0   </td><td class="markdownTableBodyNone">0 to <code>INT_MAX</code> or <code>GLFW_DONT_CARE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_ACCUM_BLUE_BITS   </td><td class="markdownTableBodyNone">0   </td><td class="markdownTableBodyNone">0 to <code>INT_MAX</code> or <code>GLFW_DONT_CARE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_ACCUM_ALPHA_BITS   </td><td class="markdownTableBodyNone">0   </td><td class="markdownTableBodyNone">0 to <code>INT_MAX</code> or <code>GLFW_DONT_CARE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_AUX_BUFFERS   </td><td class="markdownTableBodyNone">0   </td><td class="markdownTableBodyNone">0 to <code>INT_MAX</code> or <code>GLFW_DONT_CARE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_SAMPLES   </td><td class="markdownTableBodyNone">0   </td><td class="markdownTableBodyNone">0 to <code>INT_MAX</code> or <code>GLFW_DONT_CARE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_REFRESH_RATE   </td><td class="markdownTableBodyNone"><code>GLFW_DONT_CARE</code>   </td><td class="markdownTableBodyNone">0 to <code>INT_MAX</code> or <code>GLFW_DONT_CARE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_STEREO   </td><td class="markdownTableBodyNone"><code>GLFW_FALSE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_SRGB_CAPABLE   </td><td class="markdownTableBodyNone"><code>GLFW_FALSE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_DOUBLEBUFFER   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_CLIENT_API   </td><td class="markdownTableBodyNone"><code>GLFW_OPENGL_API</code>   </td><td class="markdownTableBodyNone"><code>GLFW_OPENGL_API</code>, <code>GLFW_OPENGL_ES_API</code> or <code>GLFW_NO_API</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_CONTEXT_CREATION_API   </td><td class="markdownTableBodyNone"><code>GLFW_NATIVE_CONTEXT_API</code>   </td><td class="markdownTableBodyNone"><code>GLFW_NATIVE_CONTEXT_API</code>, <code>GLFW_EGL_CONTEXT_API</code> or <code>GLFW_OSMESA_CONTEXT_API</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_CONTEXT_VERSION_MAJOR   </td><td class="markdownTableBodyNone">1   </td><td class="markdownTableBodyNone">Any valid major version number of the chosen client API    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_CONTEXT_VERSION_MINOR   </td><td class="markdownTableBodyNone">0   </td><td class="markdownTableBodyNone">Any valid minor version number of the chosen client API    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_CONTEXT_ROBUSTNESS   </td><td class="markdownTableBodyNone"><code>GLFW_NO_ROBUSTNESS</code>   </td><td class="markdownTableBodyNone"><code>GLFW_NO_ROBUSTNESS</code>, <code>GLFW_NO_RESET_NOTIFICATION</code> or <code>GLFW_LOSE_CONTEXT_ON_RESET</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_CONTEXT_RELEASE_BEHAVIOR   </td><td class="markdownTableBodyNone"><code>GLFW_ANY_RELEASE_BEHAVIOR</code>   </td><td class="markdownTableBodyNone"><code>GLFW_ANY_RELEASE_BEHAVIOR</code>, <code>GLFW_RELEASE_BEHAVIOR_FLUSH</code> or <code>GLFW_RELEASE_BEHAVIOR_NONE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_OPENGL_FORWARD_COMPAT   </td><td class="markdownTableBodyNone"><code>GLFW_FALSE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_CONTEXT_DEBUG   </td><td class="markdownTableBodyNone"><code>GLFW_FALSE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_OPENGL_PROFILE   </td><td class="markdownTableBodyNone"><code>GLFW_OPENGL_ANY_PROFILE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_OPENGL_ANY_PROFILE</code>, <code>GLFW_OPENGL_COMPAT_PROFILE</code> or <code>GLFW_OPENGL_CORE_PROFILE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_WIN32_KEYBOARD_MENU   </td><td class="markdownTableBodyNone"><code>GLFW_FALSE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_WIN32_SHOWDEFAULT   </td><td class="markdownTableBodyNone"><code>GLFW_FALSE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_COCOA_FRAME_NAME   </td><td class="markdownTableBodyNone"><code>""</code>   </td><td class="markdownTableBodyNone">A UTF-8 encoded frame autosave name    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_COCOA_GRAPHICS_SWITCHING   </td><td class="markdownTableBodyNone"><code>GLFW_FALSE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_WAYLAND_APP_ID   </td><td class="markdownTableBodyNone"><code>""</code>   </td><td class="markdownTableBodyNone">An ASCII encoded Wayland <code>app_id</code> name    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">GLFW_X11_CLASS_NAME   </td><td class="markdownTableBodyNone"><code>""</code>   </td><td class="markdownTableBodyNone">An ASCII encoded <code>WM_CLASS</code> class name    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">GLFW_X11_INSTANCE_NAME   </td><td class="markdownTableBodyNone"><code>""</code>   </td><td class="markdownTableBodyNone">An ASCII encoded <code>WM_CLASS</code> instance name   </td></tr>
</table>
<h1><a class="anchor" id="window_events"></a>
Window event processing</h1>
<p>See <a class="el" href="input_guide.html#events">Event processing</a>.</p>
<h1><a class="anchor" id="window_properties"></a>
Window properties and events</h1>
<h2><a class="anchor" id="window_userptr"></a>
User pointer</h2>
<p>Each window has a user pointer that can be set with <a class="el" href="group__window.html#ga3d2fc6026e690ab31a13f78bc9fd3651">glfwSetWindowUserPointer</a> and queried with <a class="el" href="group__window.html#gae77a4add0d2023ca21ff1443ced01653">glfwGetWindowUserPointer</a>. This can be used for any purpose you need and will not be modified by GLFW throughout the life-time of the window.</p>
<p>The initial value of the pointer is <code>NULL</code>.</p>
<h2><a class="anchor" id="window_close"></a>
Window closing and close flag</h2>
<p>When the user attempts to close the window, for example by clicking the close widget or using a key chord like Alt+F4, the <em>close flag</em> of the window is set. The window is however not actually destroyed and, unless you watch for this state change, nothing further happens.</p>
<p>The current state of the close flag is returned by <a class="el" href="group__window.html#ga24e02fbfefbb81fc45320989f8140ab5">glfwWindowShouldClose</a> and can be set or cleared directly with <a class="el" href="group__window.html#ga49c449dde2a6f87d996f4daaa09d6708">glfwSetWindowShouldClose</a>. A common pattern is to use the close flag as a main loop condition.</p>
<div class="fragment"><div class="line"><span class="keywordflow">while</span> (!<a class="code hl_function" href="group__window.html#ga24e02fbfefbb81fc45320989f8140ab5">glfwWindowShouldClose</a>(window))</div>
<div class="line">{</div>
<div class="line">    render(window);</div>
<div class="line"> </div>
<div class="line">    <a class="code hl_function" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a>(window);</div>
<div class="line">    <a class="code hl_function" href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a>();</div>
<div class="line">}</div>
<div class="ttc" id="agroup__window_html_ga15a5a1ee5b3c2ca6b15ca209a12efd14"><div class="ttname"><a href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a></div><div class="ttdeci">void glfwSwapBuffers(GLFWwindow *window)</div><div class="ttdoc">Swaps the front and back buffers of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga24e02fbfefbb81fc45320989f8140ab5"><div class="ttname"><a href="group__window.html#ga24e02fbfefbb81fc45320989f8140ab5">glfwWindowShouldClose</a></div><div class="ttdeci">int glfwWindowShouldClose(GLFWwindow *window)</div><div class="ttdoc">Checks the close flag of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga37bd57223967b4211d60ca1a0bf3c832"><div class="ttname"><a href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a></div><div class="ttdeci">void glfwPollEvents(void)</div><div class="ttdoc">Processes all pending events.</div></div>
</div><!-- fragment --><p>If you wish to be notified when the user attempts to close a window, set a close callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#gada646d775a7776a95ac000cfc1885331">glfwSetWindowCloseCallback</a>(window, window_close_callback);</div>
<div class="ttc" id="agroup__window_html_gada646d775a7776a95ac000cfc1885331"><div class="ttname"><a href="group__window.html#gada646d775a7776a95ac000cfc1885331">glfwSetWindowCloseCallback</a></div><div class="ttdeci">GLFWwindowclosefun glfwSetWindowCloseCallback(GLFWwindow *window, GLFWwindowclosefun callback)</div><div class="ttdoc">Sets the close callback for the specified window.</div></div>
</div><!-- fragment --><p>The callback function is called directly <em>after</em> the close flag has been set. It can be used for example to filter close requests and clear the close flag again unless certain conditions are met.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> window_close_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window)</div>
<div class="line">{</div>
<div class="line">    <span class="keywordflow">if</span> (!time_to_close)</div>
<div class="line">        <a class="code hl_function" href="group__window.html#ga49c449dde2a6f87d996f4daaa09d6708">glfwSetWindowShouldClose</a>(window, <a class="code hl_define" href="group__init.html#gac877fe3b627d21ef3a0a23e0a73ba8c5">GLFW_FALSE</a>);</div>
<div class="line">}</div>
<div class="ttc" id="agroup__init_html_gac877fe3b627d21ef3a0a23e0a73ba8c5"><div class="ttname"><a href="group__init.html#gac877fe3b627d21ef3a0a23e0a73ba8c5">GLFW_FALSE</a></div><div class="ttdeci">#define GLFW_FALSE</div><div class="ttdoc">Zero.</div><div class="ttdef"><b>Definition</b> glfw3.h:321</div></div>
<div class="ttc" id="agroup__window_html_ga49c449dde2a6f87d996f4daaa09d6708"><div class="ttname"><a href="group__window.html#ga49c449dde2a6f87d996f4daaa09d6708">glfwSetWindowShouldClose</a></div><div class="ttdeci">void glfwSetWindowShouldClose(GLFWwindow *window, int value)</div><div class="ttdoc">Sets the close flag of the specified window.</div></div>
</div><!-- fragment --><h2><a class="anchor" id="window_size"></a>
Window size</h2>
<p>The size of a window can be changed with <a class="el" href="group__window.html#ga371911f12c74c504dd8d47d832d095cb">glfwSetWindowSize</a>. For windowed mode windows, this sets the size, in <a class="el" href="intro_guide.html#coordinate_systems">screen coordinates</a> of the <em>content area</em> or <em>content area</em> of the window. The window system may impose limits on window size.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga371911f12c74c504dd8d47d832d095cb">glfwSetWindowSize</a>(window, 640, 480);</div>
<div class="ttc" id="agroup__window_html_ga371911f12c74c504dd8d47d832d095cb"><div class="ttname"><a href="group__window.html#ga371911f12c74c504dd8d47d832d095cb">glfwSetWindowSize</a></div><div class="ttdeci">void glfwSetWindowSize(GLFWwindow *window, int width, int height)</div><div class="ttdoc">Sets the size of the content area of the specified window.</div></div>
</div><!-- fragment --><p>For full screen windows, the specified size becomes the new resolution of the window's desired video mode. The video mode most closely matching the new desired video mode is set immediately. The window is resized to fit the resolution of the set video mode.</p>
<p>If you wish to be notified when a window is resized, whether by the user, the system or your own code, set a size callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#gad91b8b047a0c4c6033c38853864c34f8">glfwSetWindowSizeCallback</a>(window, window_size_callback);</div>
<div class="ttc" id="agroup__window_html_gad91b8b047a0c4c6033c38853864c34f8"><div class="ttname"><a href="group__window.html#gad91b8b047a0c4c6033c38853864c34f8">glfwSetWindowSizeCallback</a></div><div class="ttdeci">GLFWwindowsizefun glfwSetWindowSizeCallback(GLFWwindow *window, GLFWwindowsizefun callback)</div><div class="ttdoc">Sets the size callback for the specified window.</div></div>
</div><!-- fragment --><p>The callback function receives the new size, in screen coordinates, of the content area of the window when the window is resized.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> window_size_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> width, <span class="keywordtype">int</span> height)</div>
<div class="line">{</div>
<div class="line">}</div>
</div><!-- fragment --><p>There is also <a class="el" href="group__window.html#gaeea7cbc03373a41fb51cfbf9f2a5d4c6">glfwGetWindowSize</a> for directly retrieving the current size of a window.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> width, height;</div>
<div class="line"><a class="code hl_function" href="group__window.html#gaeea7cbc03373a41fb51cfbf9f2a5d4c6">glfwGetWindowSize</a>(window, &amp;width, &amp;height);</div>
<div class="ttc" id="agroup__window_html_gaeea7cbc03373a41fb51cfbf9f2a5d4c6"><div class="ttname"><a href="group__window.html#gaeea7cbc03373a41fb51cfbf9f2a5d4c6">glfwGetWindowSize</a></div><div class="ttdeci">void glfwGetWindowSize(GLFWwindow *window, int *width, int *height)</div><div class="ttdoc">Retrieves the size of the content area of the specified window.</div></div>
</div><!-- fragment --><dl class="section note"><dt>Note</dt><dd>Do not pass the window size to <code>glViewport</code> or other pixel-based OpenGL calls. The window size is in screen coordinates, not pixels. Use the <a class="el" href="window_guide.html#window_fbsize">framebuffer size</a>, which is in pixels, for pixel-based calls.</dd></dl>
<p>The above functions work with the size of the content area, but decorated windows typically have title bars and window frames around this rectangle. You can retrieve the extents of these with <a class="el" href="group__window.html#ga1a9fd382058c53101b21cf211898f1f1">glfwGetWindowFrameSize</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> left, top, right, bottom;</div>
<div class="line"><a class="code hl_function" href="group__window.html#ga1a9fd382058c53101b21cf211898f1f1">glfwGetWindowFrameSize</a>(window, &amp;left, &amp;top, &amp;right, &amp;bottom);</div>
<div class="ttc" id="agroup__window_html_ga1a9fd382058c53101b21cf211898f1f1"><div class="ttname"><a href="group__window.html#ga1a9fd382058c53101b21cf211898f1f1">glfwGetWindowFrameSize</a></div><div class="ttdeci">void glfwGetWindowFrameSize(GLFWwindow *window, int *left, int *top, int *right, int *bottom)</div><div class="ttdoc">Retrieves the size of the frame of the window.</div></div>
</div><!-- fragment --><p>The returned values are the distances, in screen coordinates, from the edges of the content area to the corresponding edges of the full window. As they are distances and not coordinates, they are always zero or positive.</p>
<h2><a class="anchor" id="window_fbsize"></a>
Framebuffer size</h2>
<p>While the size of a window is measured in screen coordinates, OpenGL works with pixels. The size you pass into <code>glViewport</code>, for example, should be in pixels. On some machines screen coordinates and pixels are the same, but on others they will not be. There is a second set of functions to retrieve the size, in pixels, of the framebuffer of a window.</p>
<p>If you wish to be notified when the framebuffer of a window is resized, whether by the user or the system, set a size callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#gab3fb7c3366577daef18c0023e2a8591f">glfwSetFramebufferSizeCallback</a>(window, framebuffer_size_callback);</div>
<div class="ttc" id="agroup__window_html_gab3fb7c3366577daef18c0023e2a8591f"><div class="ttname"><a href="group__window.html#gab3fb7c3366577daef18c0023e2a8591f">glfwSetFramebufferSizeCallback</a></div><div class="ttdeci">GLFWframebuffersizefun glfwSetFramebufferSizeCallback(GLFWwindow *window, GLFWframebuffersizefun callback)</div><div class="ttdoc">Sets the framebuffer resize callback for the specified window.</div></div>
</div><!-- fragment --><p>The callback function receives the new size of the framebuffer when it is resized, which can for example be used to update the OpenGL viewport.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> framebuffer_size_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> width, <span class="keywordtype">int</span> height)</div>
<div class="line">{</div>
<div class="line">    glViewport(0, 0, width, height);</div>
<div class="line">}</div>
</div><!-- fragment --><p>There is also <a class="el" href="group__window.html#ga0e2637a4161afb283f5300c7f94785c9">glfwGetFramebufferSize</a> for directly retrieving the current size of the framebuffer of a window.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> width, height;</div>
<div class="line"><a class="code hl_function" href="group__window.html#ga0e2637a4161afb283f5300c7f94785c9">glfwGetFramebufferSize</a>(window, &amp;width, &amp;height);</div>
<div class="line">glViewport(0, 0, width, height);</div>
<div class="ttc" id="agroup__window_html_ga0e2637a4161afb283f5300c7f94785c9"><div class="ttname"><a href="group__window.html#ga0e2637a4161afb283f5300c7f94785c9">glfwGetFramebufferSize</a></div><div class="ttdeci">void glfwGetFramebufferSize(GLFWwindow *window, int *width, int *height)</div><div class="ttdoc">Retrieves the size of the framebuffer of the specified window.</div></div>
</div><!-- fragment --><p>The size of a framebuffer may change independently of the size of a window, for example if the window is dragged between a regular monitor and a high-DPI one.</p>
<h2><a class="anchor" id="window_scale"></a>
Window content scale</h2>
<p>The content scale for a window can be retrieved with <a class="el" href="group__window.html#gaf5d31de9c19c4f994facea64d2b3106c">glfwGetWindowContentScale</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">float</span> xscale, yscale;</div>
<div class="line"><a class="code hl_function" href="group__window.html#gaf5d31de9c19c4f994facea64d2b3106c">glfwGetWindowContentScale</a>(window, &amp;xscale, &amp;yscale);</div>
<div class="ttc" id="agroup__window_html_gaf5d31de9c19c4f994facea64d2b3106c"><div class="ttname"><a href="group__window.html#gaf5d31de9c19c4f994facea64d2b3106c">glfwGetWindowContentScale</a></div><div class="ttdeci">void glfwGetWindowContentScale(GLFWwindow *window, float *xscale, float *yscale)</div><div class="ttdoc">Retrieves the content scale for the specified window.</div></div>
</div><!-- fragment --><p>The content scale can be thought of as the ratio between the current DPI and the platform's default DPI. It is intended to be a scaling factor to apply to the pixel dimensions of text and other UI elements. If the dimensions scaled by this factor looks appropriate on your machine then it should appear at a reasonable size on other machines with different DPI and scaling settings.</p>
<p>This relies on the DPI and scaling settings on both machines being appropriate.</p>
<p>The content scale may depend on both the monitor resolution and pixel density and on user settings like DPI or a scaling percentage. It may be very different from the raw DPI calculated from the physical size and current resolution.</p>
<p>On systems where each monitors can have its own content scale, the window content scale will depend on which monitor or monitors the system considers the window to be "on".</p>
<p>If you wish to be notified when the content scale of a window changes, whether because of a system setting change or because it was moved to a monitor with a different scale, set a content scale callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#gaf2832ebb5aa6c252a2d261de002c92d6">glfwSetWindowContentScaleCallback</a>(window, window_content_scale_callback);</div>
<div class="ttc" id="agroup__window_html_gaf2832ebb5aa6c252a2d261de002c92d6"><div class="ttname"><a href="group__window.html#gaf2832ebb5aa6c252a2d261de002c92d6">glfwSetWindowContentScaleCallback</a></div><div class="ttdeci">GLFWwindowcontentscalefun glfwSetWindowContentScaleCallback(GLFWwindow *window, GLFWwindowcontentscalefun callback)</div><div class="ttdoc">Sets the window content scale callback for the specified window.</div></div>
</div><!-- fragment --><p>The callback function receives the new content scale of the window.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> window_content_scale_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">float</span> xscale, <span class="keywordtype">float</span> yscale)</div>
<div class="line">{</div>
<div class="line">    set_interface_scale(xscale, yscale);</div>
<div class="line">}</div>
</div><!-- fragment --><p>On platforms where pixels and screen coordinates always map 1:1, the window will need to be resized to appear the same size when it is moved to a monitor with a different content scale. To have this done automatically both when the window is created and when its content scale later changes, set the <a class="el" href="window_guide.html#GLFW_SCALE_TO_MONITOR">GLFW_SCALE_TO_MONITOR</a> window hint.</p>
<p>On platforms where pixels do not necessarily equal screen coordinates, the framebuffer will instead need to be sized to provide a full resolution image for the window. When the window moves between monitors with different content scales, the window size will remain the same but the framebuffer size will change. This is done automatically by default. To disable this resizing, set the <a class="el" href="group__window.html#gaa5a9c6b4722670fd33d6e8a88f2e21bc">GLFW_SCALE_FRAMEBUFFER</a> window hint.</p>
<p>Both of these hints also apply when the window is created. Every window starts out with a content scale of one. A window with one or both of these hints set will adapt to the appropriate scale in the process of being created, set up and shown.</p>
<h2><a class="anchor" id="window_sizelimits"></a>
Window size limits</h2>
<p>The minimum and maximum size of the content area of a windowed mode window can be enforced with <a class="el" href="group__window.html#gac314fa6cec7d2d307be9963e2709cc90">glfwSetWindowSizeLimits</a>. The user may resize the window to any size and aspect ratio within the specified limits, unless the aspect ratio is also set.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#gac314fa6cec7d2d307be9963e2709cc90">glfwSetWindowSizeLimits</a>(window, 200, 200, 400, 400);</div>
<div class="ttc" id="agroup__window_html_gac314fa6cec7d2d307be9963e2709cc90"><div class="ttname"><a href="group__window.html#gac314fa6cec7d2d307be9963e2709cc90">glfwSetWindowSizeLimits</a></div><div class="ttdeci">void glfwSetWindowSizeLimits(GLFWwindow *window, int minwidth, int minheight, int maxwidth, int maxheight)</div><div class="ttdoc">Sets the size limits of the specified window.</div></div>
</div><!-- fragment --><p>To specify only a minimum size or only a maximum one, set the other pair to <code>GLFW_DONT_CARE</code>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#gac314fa6cec7d2d307be9963e2709cc90">glfwSetWindowSizeLimits</a>(window, 640, 480, <a class="code hl_define" href="glfw3_8h.html#a7a2edf2c18446833d27d07f1b7f3d571">GLFW_DONT_CARE</a>, <a class="code hl_define" href="glfw3_8h.html#a7a2edf2c18446833d27d07f1b7f3d571">GLFW_DONT_CARE</a>);</div>
<div class="ttc" id="aglfw3_8h_html_a7a2edf2c18446833d27d07f1b7f3d571"><div class="ttname"><a href="glfw3_8h.html#a7a2edf2c18446833d27d07f1b7f3d571">GLFW_DONT_CARE</a></div><div class="ttdeci">#define GLFW_DONT_CARE</div><div class="ttdef"><b>Definition</b> glfw3.h:1346</div></div>
</div><!-- fragment --><p>To disable size limits for a window, set them all to <code>GLFW_DONT_CARE</code>.</p>
<p>The aspect ratio of the content area of a windowed mode window can be enforced with <a class="el" href="group__window.html#ga72ac8cb1ee2e312a878b55153d81b937">glfwSetWindowAspectRatio</a>. The user may resize the window freely unless size limits are also set, but the size will be constrained to maintain the aspect ratio.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga72ac8cb1ee2e312a878b55153d81b937">glfwSetWindowAspectRatio</a>(window, 16, 9);</div>
<div class="ttc" id="agroup__window_html_ga72ac8cb1ee2e312a878b55153d81b937"><div class="ttname"><a href="group__window.html#ga72ac8cb1ee2e312a878b55153d81b937">glfwSetWindowAspectRatio</a></div><div class="ttdeci">void glfwSetWindowAspectRatio(GLFWwindow *window, int numer, int denom)</div><div class="ttdoc">Sets the aspect ratio of the specified window.</div></div>
</div><!-- fragment --><p>The aspect ratio is specified as a numerator and denominator, corresponding to the width and height, respectively. If you want a window to maintain its current aspect ratio, use its current size as the ratio.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> width, height;</div>
<div class="line"><a class="code hl_function" href="group__window.html#gaeea7cbc03373a41fb51cfbf9f2a5d4c6">glfwGetWindowSize</a>(window, &amp;width, &amp;height);</div>
<div class="line"><a class="code hl_function" href="group__window.html#ga72ac8cb1ee2e312a878b55153d81b937">glfwSetWindowAspectRatio</a>(window, width, height);</div>
</div><!-- fragment --><p>To disable the aspect ratio limit for a window, set both terms to <code>GLFW_DONT_CARE</code>.</p>
<p>You can have both size limits and aspect ratio set for a window, but the results are undefined if they conflict.</p>
<h2><a class="anchor" id="window_pos"></a>
Window position</h2>
<p>By default, the window manager chooses the position of new windowed mode windows, based on its size and which monitor the user appears to be working on. This is most often the right choice. If you need to create a window at a specific position, you can set the desired position with the <a class="el" href="window_guide.html#GLFW_POSITION_X">GLFW_POSITION_X</a> and <a class="el" href="window_guide.html#GLFW_POSITION_Y">GLFW_POSITION_Y</a> window hints.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#gaededa6b208b8e31343da56bb349c6fb2">GLFW_POSITION_X</a>, 70);</div>
<div class="line"><a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#ga6b3ccf63683c81f479e2a98f5027200e">GLFW_POSITION_Y</a>, 83);</div>
<div class="ttc" id="agroup__window_html_ga6b3ccf63683c81f479e2a98f5027200e"><div class="ttname"><a href="group__window.html#ga6b3ccf63683c81f479e2a98f5027200e">GLFW_POSITION_Y</a></div><div class="ttdeci">#define GLFW_POSITION_Y</div><div class="ttdoc">Initial position y-coordinate window hint.</div><div class="ttdef"><b>Definition</b> glfw3.h:942</div></div>
<div class="ttc" id="agroup__window_html_gaededa6b208b8e31343da56bb349c6fb2"><div class="ttname"><a href="group__window.html#gaededa6b208b8e31343da56bb349c6fb2">GLFW_POSITION_X</a></div><div class="ttdeci">#define GLFW_POSITION_X</div><div class="ttdoc">Initial position x-coordinate window hint.</div><div class="ttdef"><b>Definition</b> glfw3.h:936</div></div>
</div><!-- fragment --><p>To restore the previous behavior, set these hints to <code>GLFW_ANY_POSITION</code>.</p>
<p>The position of a windowed mode window can be changed with <a class="el" href="group__window.html#ga1abb6d690e8c88e0c8cd1751356dbca8">glfwSetWindowPos</a>. This moves the window so that the upper-left corner of its content area has the specified <a class="el" href="intro_guide.html#coordinate_systems">screen coordinates</a>. The window system may put limitations on window placement.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga1abb6d690e8c88e0c8cd1751356dbca8">glfwSetWindowPos</a>(window, 100, 100);</div>
<div class="ttc" id="agroup__window_html_ga1abb6d690e8c88e0c8cd1751356dbca8"><div class="ttname"><a href="group__window.html#ga1abb6d690e8c88e0c8cd1751356dbca8">glfwSetWindowPos</a></div><div class="ttdeci">void glfwSetWindowPos(GLFWwindow *window, int xpos, int ypos)</div><div class="ttdoc">Sets the position of the content area of the specified window.</div></div>
</div><!-- fragment --><p>If you wish to be notified when a window is moved, whether by the user, the system or your own code, set a position callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga08bdfbba88934f9c4f92fd757979ac74">glfwSetWindowPosCallback</a>(window, window_pos_callback);</div>
<div class="ttc" id="agroup__window_html_ga08bdfbba88934f9c4f92fd757979ac74"><div class="ttname"><a href="group__window.html#ga08bdfbba88934f9c4f92fd757979ac74">glfwSetWindowPosCallback</a></div><div class="ttdeci">GLFWwindowposfun glfwSetWindowPosCallback(GLFWwindow *window, GLFWwindowposfun callback)</div><div class="ttdoc">Sets the position callback for the specified window.</div></div>
</div><!-- fragment --><p>The callback function receives the new position, in screen coordinates, of the upper-left corner of the content area when the window is moved.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> window_pos_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> xpos, <span class="keywordtype">int</span> ypos)</div>
<div class="line">{</div>
<div class="line">}</div>
</div><!-- fragment --><p>There is also <a class="el" href="group__window.html#ga73cb526c000876fd8ddf571570fdb634">glfwGetWindowPos</a> for directly retrieving the current position of the content area of the window.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> xpos, ypos;</div>
<div class="line"><a class="code hl_function" href="group__window.html#ga73cb526c000876fd8ddf571570fdb634">glfwGetWindowPos</a>(window, &amp;xpos, &amp;ypos);</div>
<div class="ttc" id="agroup__window_html_ga73cb526c000876fd8ddf571570fdb634"><div class="ttname"><a href="group__window.html#ga73cb526c000876fd8ddf571570fdb634">glfwGetWindowPos</a></div><div class="ttdeci">void glfwGetWindowPos(GLFWwindow *window, int *xpos, int *ypos)</div><div class="ttdoc">Retrieves the position of the content area of the specified window.</div></div>
</div><!-- fragment --><h2><a class="anchor" id="window_title"></a>
Window title</h2>
<p>All GLFW windows have a title, although undecorated or full screen windows may not display it or only display it in a task bar or similar interface. You can set a new UTF-8 encoded window title with <a class="el" href="group__window.html#ga5d877f09e968cef7a360b513306f17ff">glfwSetWindowTitle</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga5d877f09e968cef7a360b513306f17ff">glfwSetWindowTitle</a>(window, <span class="stringliteral">&quot;My Window&quot;</span>);</div>
<div class="ttc" id="agroup__window_html_ga5d877f09e968cef7a360b513306f17ff"><div class="ttname"><a href="group__window.html#ga5d877f09e968cef7a360b513306f17ff">glfwSetWindowTitle</a></div><div class="ttdeci">void glfwSetWindowTitle(GLFWwindow *window, const char *title)</div><div class="ttdoc">Sets the title of the specified window.</div></div>
</div><!-- fragment --><p>The specified string is copied before the function returns, so there is no need to keep it around.</p>
<p>As long as your source file is encoded as UTF-8, you can use any Unicode characters directly in the source.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga5d877f09e968cef7a360b513306f17ff">glfwSetWindowTitle</a>(window, <span class="stringliteral">&quot;ラストエグザイル&quot;</span>);</div>
</div><!-- fragment --><p>If you are using C++11 or C11, you can use a UTF-8 string literal.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga5d877f09e968cef7a360b513306f17ff">glfwSetWindowTitle</a>(window, u8<span class="stringliteral">&quot;This is always a UTF-8 string&quot;</span>);</div>
</div><!-- fragment --><p>The current window title can be queried with <a class="el" href="group__window.html#gac6151765c54b789c4fe66c6bc6215953">glfwGetWindowTitle</a>.</p>
<div class="fragment"><div class="line"><span class="keyword">const</span> <span class="keywordtype">char</span>* title = <a class="code hl_function" href="group__window.html#gac6151765c54b789c4fe66c6bc6215953">glfwGetWindowTitle</a>(window);</div>
<div class="ttc" id="agroup__window_html_gac6151765c54b789c4fe66c6bc6215953"><div class="ttname"><a href="group__window.html#gac6151765c54b789c4fe66c6bc6215953">glfwGetWindowTitle</a></div><div class="ttdeci">const char * glfwGetWindowTitle(GLFWwindow *window)</div><div class="ttdoc">Returns the title of the specified window.</div></div>
</div><!-- fragment --><h2><a class="anchor" id="window_icon"></a>
Window icon</h2>
<p>Decorated windows have icons on some platforms. You can set this icon by specifying a list of candidate images with <a class="el" href="group__window.html#gadd7ccd39fe7a7d1f0904666ae5932dc5">glfwSetWindowIcon</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_struct" href="struct_g_l_f_wimage.html">GLFWimage</a> images[2];</div>
<div class="line">images[0] = load_icon(<span class="stringliteral">&quot;my_icon.png&quot;</span>);</div>
<div class="line">images[1] = load_icon(<span class="stringliteral">&quot;my_icon_small.png&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><a class="code hl_function" href="group__window.html#gadd7ccd39fe7a7d1f0904666ae5932dc5">glfwSetWindowIcon</a>(window, 2, images);</div>
<div class="ttc" id="agroup__window_html_gadd7ccd39fe7a7d1f0904666ae5932dc5"><div class="ttname"><a href="group__window.html#gadd7ccd39fe7a7d1f0904666ae5932dc5">glfwSetWindowIcon</a></div><div class="ttdeci">void glfwSetWindowIcon(GLFWwindow *window, int count, const GLFWimage *images)</div><div class="ttdoc">Sets the icon for the specified window.</div></div>
<div class="ttc" id="astruct_g_l_f_wimage_html"><div class="ttname"><a href="struct_g_l_f_wimage.html">GLFWimage</a></div><div class="ttdoc">Image data.</div><div class="ttdef"><b>Definition</b> glfw3.h:2090</div></div>
</div><!-- fragment --><p>The image data is 32-bit, little-endian, non-premultiplied RGBA, i.e. eight bits per channel with the red channel first. The pixels are arranged canonically as sequential rows, starting from the top-left corner.</p>
<p>To revert to the default window icon, pass in an empty image array.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#gadd7ccd39fe7a7d1f0904666ae5932dc5">glfwSetWindowIcon</a>(window, 0, NULL);</div>
</div><!-- fragment --><h2><a class="anchor" id="window_monitor"></a>
Window monitor</h2>
<p>Full screen windows are associated with a specific monitor. You can get the handle for this monitor with <a class="el" href="group__window.html#ga4d766499ac02c60f02221a9dfab87299">glfwGetWindowMonitor</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor = <a class="code hl_function" href="group__window.html#ga4d766499ac02c60f02221a9dfab87299">glfwGetWindowMonitor</a>(window);</div>
<div class="ttc" id="agroup__monitor_html_ga8d9efd1cde9426692c73fe40437d0ae3"><div class="ttname"><a href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a></div><div class="ttdeci">struct GLFWmonitor GLFWmonitor</div><div class="ttdoc">Opaque monitor object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1391</div></div>
<div class="ttc" id="agroup__window_html_ga4d766499ac02c60f02221a9dfab87299"><div class="ttname"><a href="group__window.html#ga4d766499ac02c60f02221a9dfab87299">glfwGetWindowMonitor</a></div><div class="ttdeci">GLFWmonitor * glfwGetWindowMonitor(GLFWwindow *window)</div><div class="ttdoc">Returns the monitor that the window uses for full screen mode.</div></div>
</div><!-- fragment --><p>This monitor handle is one of those returned by <a class="el" href="group__monitor.html#ga70b1156d5d24e9928f145d6c864369d2">glfwGetMonitors</a>.</p>
<p>For windowed mode windows, this function returns <code>NULL</code>. This is how to tell full screen windows from windowed mode windows.</p>
<p>You can move windows between monitors or between full screen and windowed mode with <a class="el" href="group__window.html#ga81c76c418af80a1cce7055bccb0ae0a7">glfwSetWindowMonitor</a>. When making a window full screen on the same or on a different monitor, specify the desired monitor, resolution and refresh rate. The position arguments are ignored.</p>
<div class="fragment"><div class="line"><span class="keyword">const</span> <a class="code hl_struct" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a>* mode = <a class="code hl_function" href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a>(monitor);</div>
<div class="line"> </div>
<div class="line"><a class="code hl_function" href="group__window.html#ga81c76c418af80a1cce7055bccb0ae0a7">glfwSetWindowMonitor</a>(window, monitor, 0, 0, mode-&gt;<a class="code hl_variable" href="struct_g_l_f_wvidmode.html#a698dcb200562051a7249cb6ae154c71d">width</a>, mode-&gt;<a class="code hl_variable" href="struct_g_l_f_wvidmode.html#ac65942a5f6981695517437a9d571d03c">height</a>, mode-&gt;<a class="code hl_variable" href="struct_g_l_f_wvidmode.html#a791bdd6c7697b09f7e9c97054bf05649">refreshRate</a>);</div>
</div><!-- fragment --><p>When making the window windowed, specify the desired position and size. The refresh rate argument is ignored.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga81c76c418af80a1cce7055bccb0ae0a7">glfwSetWindowMonitor</a>(window, NULL, xpos, ypos, width, height, 0);</div>
</div><!-- fragment --><p>This restores any previous window settings such as whether it is decorated, floating, resizable, has size or aspect ratio limits, etc.. To restore a window that was originally windowed to its original size and position, save these before making it full screen and then pass them in as above.</p>
<h2><a class="anchor" id="window_iconify"></a>
Window iconification</h2>
<p>Windows can be iconified (i.e. minimized) with <a class="el" href="group__window.html#ga1bb559c0ebaad63c5c05ad2a066779c4">glfwIconifyWindow</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga1bb559c0ebaad63c5c05ad2a066779c4">glfwIconifyWindow</a>(window);</div>
<div class="ttc" id="agroup__window_html_ga1bb559c0ebaad63c5c05ad2a066779c4"><div class="ttname"><a href="group__window.html#ga1bb559c0ebaad63c5c05ad2a066779c4">glfwIconifyWindow</a></div><div class="ttdeci">void glfwIconifyWindow(GLFWwindow *window)</div><div class="ttdoc">Iconifies the specified window.</div></div>
</div><!-- fragment --><p>When a full screen window is iconified, the original video mode of its monitor is restored until the user or application restores the window.</p>
<p>Iconified windows can be restored with <a class="el" href="group__window.html#ga52527a5904b47d802b6b4bb519cdebc7">glfwRestoreWindow</a>. This function also restores windows from maximization.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga52527a5904b47d802b6b4bb519cdebc7">glfwRestoreWindow</a>(window);</div>
<div class="ttc" id="agroup__window_html_ga52527a5904b47d802b6b4bb519cdebc7"><div class="ttname"><a href="group__window.html#ga52527a5904b47d802b6b4bb519cdebc7">glfwRestoreWindow</a></div><div class="ttdeci">void glfwRestoreWindow(GLFWwindow *window)</div><div class="ttdoc">Restores the specified window.</div></div>
</div><!-- fragment --><p>When a full screen window is restored, the desired video mode is restored to its monitor as well.</p>
<p>If you wish to be notified when a window is iconified or restored, whether by the user, system or your own code, set an iconify callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#gac793e9efd255567b5fb8b445052cfd3e">glfwSetWindowIconifyCallback</a>(window, window_iconify_callback);</div>
<div class="ttc" id="agroup__window_html_gac793e9efd255567b5fb8b445052cfd3e"><div class="ttname"><a href="group__window.html#gac793e9efd255567b5fb8b445052cfd3e">glfwSetWindowIconifyCallback</a></div><div class="ttdeci">GLFWwindowiconifyfun glfwSetWindowIconifyCallback(GLFWwindow *window, GLFWwindowiconifyfun callback)</div><div class="ttdoc">Sets the iconify callback for the specified window.</div></div>
</div><!-- fragment --><p>The callback function receives changes in the iconification state of the window.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> window_iconify_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> iconified)</div>
<div class="line">{</div>
<div class="line">    <span class="keywordflow">if</span> (iconified)</div>
<div class="line">    {</div>
<div class="line">        <span class="comment">// The window was iconified</span></div>
<div class="line">    }</div>
<div class="line">    <span class="keywordflow">else</span></div>
<div class="line">    {</div>
<div class="line">        <span class="comment">// The window was restored</span></div>
<div class="line">    }</div>
<div class="line">}</div>
</div><!-- fragment --><p>You can also get the current iconification state with <a class="el" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> iconified = <a class="code hl_function" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>(window, <a class="code hl_define" href="group__window.html#ga39d44b7c056e55e581355a92d240b58a">GLFW_ICONIFIED</a>);</div>
<div class="ttc" id="agroup__window_html_ga39d44b7c056e55e581355a92d240b58a"><div class="ttname"><a href="group__window.html#ga39d44b7c056e55e581355a92d240b58a">GLFW_ICONIFIED</a></div><div class="ttdeci">#define GLFW_ICONIFIED</div><div class="ttdoc">Window iconification window attribute.</div><div class="ttdef"><b>Definition</b> glfw3.h:864</div></div>
<div class="ttc" id="agroup__window_html_gacccb29947ea4b16860ebef42c2cb9337"><div class="ttname"><a href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a></div><div class="ttdeci">int glfwGetWindowAttrib(GLFWwindow *window, int attrib)</div><div class="ttdoc">Returns an attribute of the specified window.</div></div>
</div><!-- fragment --><h2><a class="anchor" id="window_maximize"></a>
Window maximization</h2>
<p>Windows can be maximized (i.e. zoomed) with <a class="el" href="group__window.html#ga3f541387449d911274324ae7f17ec56b">glfwMaximizeWindow</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga3f541387449d911274324ae7f17ec56b">glfwMaximizeWindow</a>(window);</div>
<div class="ttc" id="agroup__window_html_ga3f541387449d911274324ae7f17ec56b"><div class="ttname"><a href="group__window.html#ga3f541387449d911274324ae7f17ec56b">glfwMaximizeWindow</a></div><div class="ttdeci">void glfwMaximizeWindow(GLFWwindow *window)</div><div class="ttdoc">Maximizes the specified window.</div></div>
</div><!-- fragment --><p>Full screen windows cannot be maximized and passing a full screen window to this function does nothing.</p>
<p>Maximized windows can be restored with <a class="el" href="group__window.html#ga52527a5904b47d802b6b4bb519cdebc7">glfwRestoreWindow</a>. This function also restores windows from iconification.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga52527a5904b47d802b6b4bb519cdebc7">glfwRestoreWindow</a>(window);</div>
</div><!-- fragment --><p>If you wish to be notified when a window is maximized or restored, whether by the user, system or your own code, set a maximize callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#gacbe64c339fbd94885e62145563b6dc93">glfwSetWindowMaximizeCallback</a>(window, window_maximize_callback);</div>
<div class="ttc" id="agroup__window_html_gacbe64c339fbd94885e62145563b6dc93"><div class="ttname"><a href="group__window.html#gacbe64c339fbd94885e62145563b6dc93">glfwSetWindowMaximizeCallback</a></div><div class="ttdeci">GLFWwindowmaximizefun glfwSetWindowMaximizeCallback(GLFWwindow *window, GLFWwindowmaximizefun callback)</div><div class="ttdoc">Sets the maximize callback for the specified window.</div></div>
</div><!-- fragment --><p>The callback function receives changes in the maximization state of the window.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> window_maximize_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> maximized)</div>
<div class="line">{</div>
<div class="line">    <span class="keywordflow">if</span> (maximized)</div>
<div class="line">    {</div>
<div class="line">        <span class="comment">// The window was maximized</span></div>
<div class="line">    }</div>
<div class="line">    <span class="keywordflow">else</span></div>
<div class="line">    {</div>
<div class="line">        <span class="comment">// The window was restored</span></div>
<div class="line">    }</div>
<div class="line">}</div>
</div><!-- fragment --><p>You can also get the current maximization state with <a class="el" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> maximized = <a class="code hl_function" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>(window, <a class="code hl_define" href="group__window.html#gad8ccb396253ad0b72c6d4c917eb38a03">GLFW_MAXIMIZED</a>);</div>
<div class="ttc" id="agroup__window_html_gad8ccb396253ad0b72c6d4c917eb38a03"><div class="ttname"><a href="group__window.html#gad8ccb396253ad0b72c6d4c917eb38a03">GLFW_MAXIMIZED</a></div><div class="ttdeci">#define GLFW_MAXIMIZED</div><div class="ttdoc">Window maximization window hint and attribute.</div><div class="ttdef"><b>Definition</b> glfw3.h:900</div></div>
</div><!-- fragment --><p>By default, newly created windows are not maximized. You can change this behavior by setting the <a class="el" href="window_guide.html#GLFW_MAXIMIZED_hint">GLFW_MAXIMIZED</a> window hint before creating the window.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#gad8ccb396253ad0b72c6d4c917eb38a03">GLFW_MAXIMIZED</a>, <a class="code hl_define" href="group__init.html#ga2744fbb29b5631bb28802dbe0cf36eba">GLFW_TRUE</a>);</div>
<div class="ttc" id="agroup__init_html_ga2744fbb29b5631bb28802dbe0cf36eba"><div class="ttname"><a href="group__init.html#ga2744fbb29b5631bb28802dbe0cf36eba">GLFW_TRUE</a></div><div class="ttdeci">#define GLFW_TRUE</div><div class="ttdoc">One.</div><div class="ttdef"><b>Definition</b> glfw3.h:312</div></div>
</div><!-- fragment --><h2><a class="anchor" id="window_hide"></a>
Window visibility</h2>
<p>Windowed mode windows can be hidden with <a class="el" href="group__window.html#ga49401f82a1ba5f15db5590728314d47c">glfwHideWindow</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga49401f82a1ba5f15db5590728314d47c">glfwHideWindow</a>(window);</div>
<div class="ttc" id="agroup__window_html_ga49401f82a1ba5f15db5590728314d47c"><div class="ttname"><a href="group__window.html#ga49401f82a1ba5f15db5590728314d47c">glfwHideWindow</a></div><div class="ttdeci">void glfwHideWindow(GLFWwindow *window)</div><div class="ttdoc">Hides the specified window.</div></div>
</div><!-- fragment --><p>This makes the window completely invisible to the user, including removing it from the task bar, dock or window list. Full screen windows cannot be hidden and calling <a class="el" href="group__window.html#ga49401f82a1ba5f15db5590728314d47c">glfwHideWindow</a> on a full screen window does nothing.</p>
<p>Hidden windows can be shown with <a class="el" href="group__window.html#ga61be47917b72536a148300f46494fc66">glfwShowWindow</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga61be47917b72536a148300f46494fc66">glfwShowWindow</a>(window);</div>
<div class="ttc" id="agroup__window_html_ga61be47917b72536a148300f46494fc66"><div class="ttname"><a href="group__window.html#ga61be47917b72536a148300f46494fc66">glfwShowWindow</a></div><div class="ttdeci">void glfwShowWindow(GLFWwindow *window)</div><div class="ttdoc">Makes the specified window visible.</div></div>
</div><!-- fragment --><p>By default, this function will also set the input focus to that window. Set the <a class="el" href="window_guide.html#GLFW_FOCUS_ON_SHOW_hint">GLFW_FOCUS_ON_SHOW</a> window hint to change this behavior for all newly created windows, or change the behavior for an existing window with <a class="el" href="group__window.html#gace2afda29b4116ec012e410a6819033e">glfwSetWindowAttrib</a>.</p>
<p>You can also get the current visibility state with <a class="el" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> visible = <a class="code hl_function" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>(window, <a class="code hl_define" href="group__window.html#gafb3cdc45297e06d8f1eb13adc69ca6c4">GLFW_VISIBLE</a>);</div>
<div class="ttc" id="agroup__window_html_gafb3cdc45297e06d8f1eb13adc69ca6c4"><div class="ttname"><a href="group__window.html#gafb3cdc45297e06d8f1eb13adc69ca6c4">GLFW_VISIBLE</a></div><div class="ttdeci">#define GLFW_VISIBLE</div><div class="ttdoc">Window visibility window hint and attribute.</div><div class="ttdef"><b>Definition</b> glfw3.h:876</div></div>
</div><!-- fragment --><p>By default, newly created windows are visible. You can change this behavior by setting the <a class="el" href="window_guide.html#GLFW_VISIBLE_hint">GLFW_VISIBLE</a> window hint before creating the window.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#gafb3cdc45297e06d8f1eb13adc69ca6c4">GLFW_VISIBLE</a>, <a class="code hl_define" href="group__init.html#gac877fe3b627d21ef3a0a23e0a73ba8c5">GLFW_FALSE</a>);</div>
</div><!-- fragment --><p>Windows created hidden are completely invisible to the user until shown. This can be useful if you need to set up your window further before showing it, for example moving it to a specific location.</p>
<h2><a class="anchor" id="window_focus"></a>
Window input focus</h2>
<p>Windows can be given input focus and brought to the front with <a class="el" href="group__window.html#ga873780357abd3f3a081d71a40aae45a1">glfwFocusWindow</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga873780357abd3f3a081d71a40aae45a1">glfwFocusWindow</a>(window);</div>
<div class="ttc" id="agroup__window_html_ga873780357abd3f3a081d71a40aae45a1"><div class="ttname"><a href="group__window.html#ga873780357abd3f3a081d71a40aae45a1">glfwFocusWindow</a></div><div class="ttdeci">void glfwFocusWindow(GLFWwindow *window)</div><div class="ttdoc">Brings the specified window to front and sets input focus.</div></div>
</div><!-- fragment --><p>Keep in mind that it can be very disruptive to the user when a window is forced to the top. For a less disruptive way of getting the user's attention, see <a class="el" href="window_guide.html#window_attention">attention requests</a>.</p>
<p>If you wish to be notified when a window gains or loses input focus, whether by the user, system or your own code, set a focus callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#gac2d83c4a10f071baf841f6730528e66c">glfwSetWindowFocusCallback</a>(window, window_focus_callback);</div>
<div class="ttc" id="agroup__window_html_gac2d83c4a10f071baf841f6730528e66c"><div class="ttname"><a href="group__window.html#gac2d83c4a10f071baf841f6730528e66c">glfwSetWindowFocusCallback</a></div><div class="ttdeci">GLFWwindowfocusfun glfwSetWindowFocusCallback(GLFWwindow *window, GLFWwindowfocusfun callback)</div><div class="ttdoc">Sets the focus callback for the specified window.</div></div>
</div><!-- fragment --><p>The callback function receives changes in the input focus state of the window.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> window_focus_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> focused)</div>
<div class="line">{</div>
<div class="line">    <span class="keywordflow">if</span> (focused)</div>
<div class="line">    {</div>
<div class="line">        <span class="comment">// The window gained input focus</span></div>
<div class="line">    }</div>
<div class="line">    <span class="keywordflow">else</span></div>
<div class="line">    {</div>
<div class="line">        <span class="comment">// The window lost input focus</span></div>
<div class="line">    }</div>
<div class="line">}</div>
</div><!-- fragment --><p>You can also get the current input focus state with <a class="el" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> focused = <a class="code hl_function" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>(window, <a class="code hl_define" href="group__window.html#ga54ddb14825a1541a56e22afb5f832a9e">GLFW_FOCUSED</a>);</div>
<div class="ttc" id="agroup__window_html_ga54ddb14825a1541a56e22afb5f832a9e"><div class="ttname"><a href="group__window.html#ga54ddb14825a1541a56e22afb5f832a9e">GLFW_FOCUSED</a></div><div class="ttdeci">#define GLFW_FOCUSED</div><div class="ttdoc">Input focus window hint and attribute.</div><div class="ttdef"><b>Definition</b> glfw3.h:859</div></div>
</div><!-- fragment --><p>By default, newly created windows are given input focus. You can change this behavior by setting the <a class="el" href="window_guide.html#GLFW_FOCUSED_hint">GLFW_FOCUSED</a> window hint before creating the window.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#ga54ddb14825a1541a56e22afb5f832a9e">GLFW_FOCUSED</a>, <a class="code hl_define" href="group__init.html#gac877fe3b627d21ef3a0a23e0a73ba8c5">GLFW_FALSE</a>);</div>
</div><!-- fragment --><h2><a class="anchor" id="window_attention"></a>
Window attention request</h2>
<p>If you wish to notify the user of an event without interrupting, you can request attention with <a class="el" href="group__window.html#ga2f8d59323fc4692c1d54ba08c863a703">glfwRequestWindowAttention</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga2f8d59323fc4692c1d54ba08c863a703">glfwRequestWindowAttention</a>(window);</div>
<div class="ttc" id="agroup__window_html_ga2f8d59323fc4692c1d54ba08c863a703"><div class="ttname"><a href="group__window.html#ga2f8d59323fc4692c1d54ba08c863a703">glfwRequestWindowAttention</a></div><div class="ttdeci">void glfwRequestWindowAttention(GLFWwindow *window)</div><div class="ttdoc">Requests user attention to the specified window.</div></div>
</div><!-- fragment --><p>The system will highlight the specified window, or on platforms where this is not supported, the application as a whole. Once the user has given it attention, the system will automatically end the request.</p>
<h2><a class="anchor" id="window_refresh"></a>
Window damage and refresh</h2>
<p>If you wish to be notified when the contents of a window is damaged and needs to be refreshed, set a window refresh callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga1c5c7eb889c33c7f4d10dd35b327654e">glfwSetWindowRefreshCallback</a>(m_handle, window_refresh_callback);</div>
<div class="ttc" id="agroup__window_html_ga1c5c7eb889c33c7f4d10dd35b327654e"><div class="ttname"><a href="group__window.html#ga1c5c7eb889c33c7f4d10dd35b327654e">glfwSetWindowRefreshCallback</a></div><div class="ttdeci">GLFWwindowrefreshfun glfwSetWindowRefreshCallback(GLFWwindow *window, GLFWwindowrefreshfun callback)</div><div class="ttdoc">Sets the refresh callback for the specified window.</div></div>
</div><!-- fragment --><p>The callback function is called when the contents of the window needs to be refreshed.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> window_refresh_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window)</div>
<div class="line">{</div>
<div class="line">    draw_editor_ui(window);</div>
<div class="line">    <a class="code hl_function" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a>(window);</div>
<div class="line">}</div>
</div><!-- fragment --><dl class="section note"><dt>Note</dt><dd>On compositing window systems such as Aero, Compiz or Aqua, where the window contents are saved off-screen, this callback might only be called when the window or framebuffer is resized.</dd></dl>
<h2><a class="anchor" id="window_transparency"></a>
Window transparency</h2>
<p>GLFW supports two kinds of transparency for windows; framebuffer transparency and whole window transparency. A single window may not use both methods. The results of doing this are undefined.</p>
<p>Both methods require the platform to support it and not every version of every platform GLFW supports does this, so there are mechanisms to check whether the window really is transparent.</p>
<p>Window framebuffers can be made transparent on a per-pixel per-frame basis with the <a class="el" href="window_guide.html#GLFW_TRANSPARENT_FRAMEBUFFER_hint">GLFW_TRANSPARENT_FRAMEBUFFER</a> window hint.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#ga60a0578c3b9449027d683a9c6abb9f14">GLFW_TRANSPARENT_FRAMEBUFFER</a>, <a class="code hl_define" href="group__init.html#ga2744fbb29b5631bb28802dbe0cf36eba">GLFW_TRUE</a>);</div>
<div class="ttc" id="agroup__window_html_ga60a0578c3b9449027d683a9c6abb9f14"><div class="ttname"><a href="group__window.html#ga60a0578c3b9449027d683a9c6abb9f14">GLFW_TRANSPARENT_FRAMEBUFFER</a></div><div class="ttdeci">#define GLFW_TRANSPARENT_FRAMEBUFFER</div><div class="ttdoc">Window framebuffer transparency hint and attribute.</div><div class="ttdef"><b>Definition</b> glfw3.h:912</div></div>
</div><!-- fragment --><p>If supported by the system, the window content area will be composited with the background using the framebuffer per-pixel alpha channel. This requires desktop compositing to be enabled on the system. It does not affect window decorations.</p>
<p>You can check whether the window framebuffer was successfully made transparent with the <a class="el" href="window_guide.html#GLFW_TRANSPARENT_FRAMEBUFFER_attrib">GLFW_TRANSPARENT_FRAMEBUFFER</a> window attribute.</p>
<div class="fragment"><div class="line"><span class="keywordflow">if</span> (<a class="code hl_function" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>(window, <a class="code hl_define" href="group__window.html#ga60a0578c3b9449027d683a9c6abb9f14">GLFW_TRANSPARENT_FRAMEBUFFER</a>))</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// window framebuffer is currently transparent</span></div>
<div class="line">}</div>
</div><!-- fragment --><p>GLFW comes with an example that enabled framebuffer transparency called <code>gears</code>.</p>
<p>The opacity of the whole window, including any decorations, can be set with <a class="el" href="group__window.html#gac31caeb3d1088831b13d2c8a156802e9">glfwSetWindowOpacity</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#gac31caeb3d1088831b13d2c8a156802e9">glfwSetWindowOpacity</a>(window, 0.5f);</div>
<div class="ttc" id="agroup__window_html_gac31caeb3d1088831b13d2c8a156802e9"><div class="ttname"><a href="group__window.html#gac31caeb3d1088831b13d2c8a156802e9">glfwSetWindowOpacity</a></div><div class="ttdeci">void glfwSetWindowOpacity(GLFWwindow *window, float opacity)</div><div class="ttdoc">Sets the opacity of the whole window.</div></div>
</div><!-- fragment --><p>The opacity (or alpha) value is a positive finite number between zero and one, where 0 (zero) is fully transparent and 1 (one) is fully opaque. The initial opacity value for newly created windows is 1.</p>
<p>The current opacity of a window can be queried with <a class="el" href="group__window.html#gad09f0bd7a6307c4533b7061828480a84">glfwGetWindowOpacity</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">float</span> opacity = <a class="code hl_function" href="group__window.html#gad09f0bd7a6307c4533b7061828480a84">glfwGetWindowOpacity</a>(window);</div>
<div class="ttc" id="agroup__window_html_gad09f0bd7a6307c4533b7061828480a84"><div class="ttname"><a href="group__window.html#gad09f0bd7a6307c4533b7061828480a84">glfwGetWindowOpacity</a></div><div class="ttdeci">float glfwGetWindowOpacity(GLFWwindow *window)</div><div class="ttdoc">Returns the opacity of the whole window.</div></div>
</div><!-- fragment --><p>If the system does not support whole window transparency, this function always returns one.</p>
<p>GLFW comes with a test program that lets you control whole window transparency at run-time called <code>window</code>.</p>
<p>If you want to use either of these transparency methods to display a temporary overlay like for example a notification, the <a class="el" href="group__window.html#ga7fb0be51407783b41adbf5bec0b09d80">GLFW_FLOATING</a> and <a class="el" href="group__window.html#ga88981797d29800808ec242274ab5c03a">GLFW_MOUSE_PASSTHROUGH</a> window hints and attributes may be useful.</p>
<h2><a class="anchor" id="window_attribs"></a>
Window attributes</h2>
<p>Windows have a number of attributes that can be returned using <a class="el" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>. Some reflect state that may change as a result of user interaction, (e.g. whether it has input focus), while others reflect inherent properties of the window (e.g. what kind of border it has). Some are related to the window and others to its OpenGL or OpenGL ES context.</p>
<div class="fragment"><div class="line"><span class="keywordflow">if</span> (<a class="code hl_function" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>(window, <a class="code hl_define" href="group__window.html#ga54ddb14825a1541a56e22afb5f832a9e">GLFW_FOCUSED</a>))</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// window has input focus</span></div>
<div class="line">}</div>
</div><!-- fragment --><p>The <a class="el" href="window_guide.html#GLFW_DECORATED_attrib">GLFW_DECORATED</a>, <a class="el" href="window_guide.html#GLFW_RESIZABLE_attrib">GLFW_RESIZABLE</a>, <a class="el" href="window_guide.html#GLFW_FLOATING_attrib">GLFW_FLOATING</a>, <a class="el" href="window_guide.html#GLFW_AUTO_ICONIFY_attrib">GLFW_AUTO_ICONIFY</a> and <a class="el" href="window_guide.html#GLFW_FOCUS_ON_SHOW_attrib">GLFW_FOCUS_ON_SHOW</a> window attributes can be changed with <a class="el" href="group__window.html#gace2afda29b4116ec012e410a6819033e">glfwSetWindowAttrib</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#gace2afda29b4116ec012e410a6819033e">glfwSetWindowAttrib</a>(window, <a class="code hl_define" href="group__window.html#gadba13c7a1b3aa40831eb2beedbd5bd1d">GLFW_RESIZABLE</a>, <a class="code hl_define" href="group__init.html#gac877fe3b627d21ef3a0a23e0a73ba8c5">GLFW_FALSE</a>);</div>
<div class="ttc" id="agroup__window_html_gace2afda29b4116ec012e410a6819033e"><div class="ttname"><a href="group__window.html#gace2afda29b4116ec012e410a6819033e">glfwSetWindowAttrib</a></div><div class="ttdeci">void glfwSetWindowAttrib(GLFWwindow *window, int attrib, int value)</div><div class="ttdoc">Sets an attribute of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_gadba13c7a1b3aa40831eb2beedbd5bd1d"><div class="ttname"><a href="group__window.html#gadba13c7a1b3aa40831eb2beedbd5bd1d">GLFW_RESIZABLE</a></div><div class="ttdeci">#define GLFW_RESIZABLE</div><div class="ttdoc">Window resize-ability window hint and attribute.</div><div class="ttdef"><b>Definition</b> glfw3.h:870</div></div>
</div><!-- fragment --><h3><a class="anchor" id="window_attribs_wnd"></a>
Window related attributes</h3>
<p><a class="anchor" id="GLFW_FOCUSED_attrib"></a><b>GLFW_FOCUSED</b> indicates whether the specified window has input focus. See <a class="el" href="window_guide.html#window_focus">Window input focus</a> for details.</p>
<p><a class="anchor" id="GLFW_ICONIFIED_attrib"></a><b>GLFW_ICONIFIED</b> indicates whether the specified window is iconified. See <a class="el" href="window_guide.html#window_iconify">Window iconification</a> for details.</p>
<p><a class="anchor" id="GLFW_MAXIMIZED_attrib"></a><b>GLFW_MAXIMIZED</b> indicates whether the specified window is maximized. See <a class="el" href="window_guide.html#window_maximize">Window maximization</a> for details.</p>
<p><a class="anchor" id="GLFW_HOVERED_attrib"></a><b>GLFW_HOVERED</b> indicates whether the cursor is currently directly over the content area of the window, with no other windows between. See <a class="el" href="input_guide.html#cursor_enter">Cursor enter/leave events</a> for details.</p>
<p><a class="anchor" id="GLFW_VISIBLE_attrib"></a><b>GLFW_VISIBLE</b> indicates whether the specified window is visible. See <a class="el" href="window_guide.html#window_hide">Window visibility</a> for details.</p>
<p><a class="anchor" id="GLFW_RESIZABLE_attrib"></a><b>GLFW_RESIZABLE</b> indicates whether the specified window is resizable <em>by the user</em>. This can be set before creation with the <a class="el" href="window_guide.html#GLFW_RESIZABLE_hint">GLFW_RESIZABLE</a> window hint or after with <a class="el" href="group__window.html#gace2afda29b4116ec012e410a6819033e">glfwSetWindowAttrib</a>.</p>
<p><a class="anchor" id="GLFW_DECORATED_attrib"></a><b>GLFW_DECORATED</b> indicates whether the specified window has decorations such as a border, a close widget, etc. This can be set before creation with the <a class="el" href="window_guide.html#GLFW_DECORATED_hint">GLFW_DECORATED</a> window hint or after with <a class="el" href="group__window.html#gace2afda29b4116ec012e410a6819033e">glfwSetWindowAttrib</a>.</p>
<p><a class="anchor" id="GLFW_AUTO_ICONIFY_attrib"></a><b>GLFW_AUTO_ICONIFY</b> indicates whether the specified full screen window is iconified on focus loss, a close widget, etc. This can be set before creation with the <a class="el" href="window_guide.html#GLFW_AUTO_ICONIFY_hint">GLFW_AUTO_ICONIFY</a> window hint or after with <a class="el" href="group__window.html#gace2afda29b4116ec012e410a6819033e">glfwSetWindowAttrib</a>.</p>
<p><a class="anchor" id="GLFW_FLOATING_attrib"></a><b>GLFW_FLOATING</b> indicates whether the specified window is floating, also called topmost or always-on-top. This can be set before creation with the <a class="el" href="window_guide.html#GLFW_FLOATING_hint">GLFW_FLOATING</a> window hint or after with <a class="el" href="group__window.html#gace2afda29b4116ec012e410a6819033e">glfwSetWindowAttrib</a>.</p>
<p><a class="anchor" id="GLFW_TRANSPARENT_FRAMEBUFFER_attrib"></a><b>GLFW_TRANSPARENT_FRAMEBUFFER</b> indicates whether the specified window has a transparent framebuffer, i.e. the window contents is composited with the background using the window framebuffer alpha channel. See <a class="el" href="window_guide.html#window_transparency">Window transparency</a> for details.</p>
<p><a class="anchor" id="GLFW_FOCUS_ON_SHOW_attrib"></a><b>GLFW_FOCUS_ON_SHOW</b> specifies whether the window will be given input focus when <a class="el" href="group__window.html#ga61be47917b72536a148300f46494fc66">glfwShowWindow</a> is called. This can be set before creation with the <a class="el" href="window_guide.html#GLFW_FOCUS_ON_SHOW_hint">GLFW_FOCUS_ON_SHOW</a> window hint or after with <a class="el" href="group__window.html#gace2afda29b4116ec012e410a6819033e">glfwSetWindowAttrib</a>.</p>
<p><a class="anchor" id="GLFW_MOUSE_PASSTHROUGH_attrib"></a><b>GLFW_MOUSE_PASSTHROUGH</b> specifies whether the window is transparent to mouse input, letting any mouse events pass through to whatever window is behind it. This can be set before creation with the <a class="el" href="window_guide.html#GLFW_MOUSE_PASSTHROUGH_hint">GLFW_MOUSE_PASSTHROUGH</a> window hint or after with <a class="el" href="group__window.html#gace2afda29b4116ec012e410a6819033e">glfwSetWindowAttrib</a>. This is only supported for undecorated windows. Decorated windows with this enabled will behave differently between platforms.</p>
<h3><a class="anchor" id="window_attribs_ctx"></a>
Context related attributes</h3>
<p><a class="anchor" id="GLFW_CLIENT_API_attrib"></a><b>GLFW_CLIENT_API</b> indicates the client API provided by the window's context; either <code>GLFW_OPENGL_API</code>, <code>GLFW_OPENGL_ES_API</code> or <code>GLFW_NO_API</code>.</p>
<p><a class="anchor" id="GLFW_CONTEXT_CREATION_API_attrib"></a><b>GLFW_CONTEXT_CREATION_API</b> indicates the context creation API used to create the window's context; either <code>GLFW_NATIVE_CONTEXT_API</code>, <code>GLFW_EGL_CONTEXT_API</code> or <code>GLFW_OSMESA_CONTEXT_API</code>.</p>
<p><a class="anchor" id="GLFW_CONTEXT_VERSION_MAJOR_attrib"></a><a class="anchor" id="GLFW_CONTEXT_VERSION_MINOR_attrib"></a><a class="anchor" id="GLFW_CONTEXT_REVISION_attrib"></a><b>GLFW_CONTEXT_VERSION_MAJOR</b>, <b>GLFW_CONTEXT_VERSION_MINOR</b> and <b>GLFW_CONTEXT_REVISION</b> indicate the client API version of the window's context.</p>
<dl class="section note"><dt>Note</dt><dd>Do not confuse these attributes with <code>GLFW_VERSION_MAJOR</code>, <code>GLFW_VERSION_MINOR</code> and <code>GLFW_VERSION_REVISION</code> which provide the API version of the GLFW header.</dd></dl>
<p><a class="anchor" id="GLFW_OPENGL_FORWARD_COMPAT_attrib"></a><b>GLFW_OPENGL_FORWARD_COMPAT</b> is <code>GLFW_TRUE</code> if the window's context is an OpenGL forward-compatible one, or <code>GLFW_FALSE</code> otherwise.</p>
<p><a class="anchor" id="GLFW_CONTEXT_DEBUG_attrib"></a><a class="anchor" id="GLFW_OPENGL_DEBUG_CONTEXT_attrib"></a><b>GLFW_CONTEXT_DEBUG</b> is <code>GLFW_TRUE</code> if the window's context is in debug mode, or <code>GLFW_FALSE</code> otherwise.</p>
<p>This is the new name, introduced in GLFW 3.4. The older <code>GLFW_OPENGL_DEBUG_CONTEXT</code> name is also available for compatibility.</p>
<p><a class="anchor" id="GLFW_OPENGL_PROFILE_attrib"></a><b>GLFW_OPENGL_PROFILE</b> indicates the OpenGL profile used by the context. This is <code>GLFW_OPENGL_CORE_PROFILE</code> or <code>GLFW_OPENGL_COMPAT_PROFILE</code> if the context uses a known profile, or <code>GLFW_OPENGL_ANY_PROFILE</code> if the OpenGL profile is unknown or the context is an OpenGL ES context. Note that the returned profile may not match the profile bits of the context flags, as GLFW will try other means of detecting the profile when no bits are set.</p>
<p><a class="anchor" id="GLFW_CONTEXT_RELEASE_BEHAVIOR_attrib"></a><b>GLFW_CONTEXT_RELEASE_BEHAVIOR</b> indicates the release used by the context. Possible values are one of <code>GLFW_ANY_RELEASE_BEHAVIOR</code>, <code>GLFW_RELEASE_BEHAVIOR_FLUSH</code> or <code>GLFW_RELEASE_BEHAVIOR_NONE</code>. If the behavior is <code>GLFW_ANY_RELEASE_BEHAVIOR</code>, the default behavior of the context creation API will be used. If the behavior is <code>GLFW_RELEASE_BEHAVIOR_FLUSH</code>, the pipeline will be flushed whenever the context is released from being the current one. If the behavior is <code>GLFW_RELEASE_BEHAVIOR_NONE</code>, the pipeline will not be flushed on release.</p>
<p><a class="anchor" id="GLFW_CONTEXT_NO_ERROR_attrib"></a><b>GLFW_CONTEXT_NO_ERROR</b> indicates whether errors are generated by the context. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>. If enabled, situations that would have generated errors instead cause undefined behavior.</p>
<p><a class="anchor" id="GLFW_CONTEXT_ROBUSTNESS_attrib"></a><b>GLFW_CONTEXT_ROBUSTNESS</b> indicates the robustness strategy used by the context. This is <code>GLFW_LOSE_CONTEXT_ON_RESET</code> or <code>GLFW_NO_RESET_NOTIFICATION</code> if the window's context supports robustness, or <code>GLFW_NO_ROBUSTNESS</code> otherwise.</p>
<h3><a class="anchor" id="window_attribs_fb"></a>
Framebuffer related attributes</h3>
<p>GLFW does not expose most attributes of the default framebuffer (i.e. the framebuffer attached to the window) as these can be queried directly with either OpenGL, OpenGL ES or Vulkan. The one exception is <a class="el" href="window_guide.html#GLFW_DOUBLEBUFFER_attrib">GLFW_DOUBLEBUFFER</a>, as this is not provided by OpenGL ES.</p>
<p>If you are using version 3.0 or later of OpenGL or OpenGL ES, the <code>glGetFramebufferAttachmentParameteriv</code> function can be used to retrieve the number of bits for the red, green, blue, alpha, depth and stencil buffer channels. Otherwise, the <code>glGetIntegerv</code> function can be used.</p>
<p>The number of MSAA samples are always retrieved with <code>glGetIntegerv</code>. For contexts supporting framebuffer objects, the number of samples of the currently bound framebuffer is returned.</p>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Attribute   </th><th class="markdownTableHeadNone">glGetIntegerv   </th><th class="markdownTableHeadNone">glGetFramebufferAttachmentParameteriv    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">Red bits   </td><td class="markdownTableBodyNone"><code>GL_RED_BITS</code>   </td><td class="markdownTableBodyNone"><code>GL_FRAMEBUFFER_ATTACHMENT_RED_SIZE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">Green bits   </td><td class="markdownTableBodyNone"><code>GL_GREEN_BITS</code>   </td><td class="markdownTableBodyNone"><code>GL_FRAMEBUFFER_ATTACHMENT_GREEN_SIZE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">Blue bits   </td><td class="markdownTableBodyNone"><code>GL_BLUE_BITS</code>   </td><td class="markdownTableBodyNone"><code>GL_FRAMEBUFFER_ATTACHMENT_BLUE_SIZE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">Alpha bits   </td><td class="markdownTableBodyNone"><code>GL_ALPHA_BITS</code>   </td><td class="markdownTableBodyNone"><code>GL_FRAMEBUFFER_ATTACHMENT_ALPHA_SIZE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">Depth bits   </td><td class="markdownTableBodyNone"><code>GL_DEPTH_BITS</code>   </td><td class="markdownTableBodyNone"><code>GL_FRAMEBUFFER_ATTACHMENT_DEPTH_SIZE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">Stencil bits   </td><td class="markdownTableBodyNone"><code>GL_STENCIL_BITS</code>   </td><td class="markdownTableBodyNone"><code>GL_FRAMEBUFFER_ATTACHMENT_STENCIL_SIZE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">MSAA samples   </td><td class="markdownTableBodyNone"><code>GL_SAMPLES</code>   </td><td class="markdownTableBodyNone"><em>Not provided by this function</em>   </td></tr>
</table>
<p>When calling <code>glGetFramebufferAttachmentParameteriv</code>, the red, green, blue and alpha sizes are queried from the <code>GL_BACK_LEFT</code>, while the depth and stencil sizes are queried from the <code>GL_DEPTH</code> and <code>GL_STENCIL</code> attachments, respectively.</p>
<p><a class="anchor" id="GLFW_DOUBLEBUFFER_attrib"></a><b>GLFW_DOUBLEBUFFER</b> indicates whether the specified window is double-buffered when rendering with OpenGL or OpenGL ES. This can be set before creation with the <a class="el" href="window_guide.html#GLFW_DOUBLEBUFFER_hint">GLFW_DOUBLEBUFFER</a> window hint.</p>
<h1><a class="anchor" id="buffer_swap"></a>
Buffer swapping</h1>
<p>GLFW windows are by default double buffered. That means that you have two rendering buffers; a front buffer and a back buffer. The front buffer is the one being displayed and the back buffer the one you render to.</p>
<p>When the entire frame has been rendered, it is time to swap the back and the front buffers in order to display what has been rendered and begin rendering a new frame. This is done with <a class="el" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a>(window);</div>
</div><!-- fragment --><p>Sometimes it can be useful to select when the buffer swap will occur. With the function <a class="el" href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed">glfwSwapInterval</a> it is possible to select the minimum number of monitor refreshes the driver should wait from the time <a class="el" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a> was called before swapping the buffers:</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed">glfwSwapInterval</a>(1);</div>
<div class="ttc" id="agroup__context_html_ga6d4e0cdf151b5e579bd67f13202994ed"><div class="ttname"><a href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed">glfwSwapInterval</a></div><div class="ttdeci">void glfwSwapInterval(int interval)</div><div class="ttdoc">Sets the swap interval for the current context.</div></div>
</div><!-- fragment --><p>If the interval is zero, the swap will take place immediately when <a class="el" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a> is called without waiting for a refresh. Otherwise at least interval retraces will pass between each buffer swap. Using a swap interval of zero can be useful for benchmarking purposes, when it is not desirable to measure the time it takes to wait for the vertical retrace. However, a swap interval of one lets you avoid tearing.</p>
<p>Note that this may not work on all machines, as some drivers have user-controlled settings that override any swap interval the application requests.</p>
<p>A context that supports either the <code>WGL_EXT_swap_control_tear</code> or the <code>GLX_EXT_swap_control_tear</code> extension also accepts <em>negative</em> swap intervals, which allows the driver to swap immediately even if a frame arrives a little bit late. This trades the risk of visible tears for greater framerate stability. You can check for these extensions with <a class="el" href="group__context.html#ga87425065c011cef1ebd6aac75e059dfa">glfwExtensionSupported</a>. </p>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
