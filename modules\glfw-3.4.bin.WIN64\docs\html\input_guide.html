<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Input guide</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div><div class="header">
  <div class="headertitle"><div class="title">Input guide</div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#events">Event processing</a></li>
<li class="level1"><a href="#input_keyboard">Keyboard input</a><ul><li class="level2"><a href="#input_key">Key input</a></li>
<li class="level2"><a href="#input_char">Text input</a></li>
<li class="level2"><a href="#input_key_name">Key names</a></li>
</ul>
</li>
<li class="level1"><a href="#input_mouse">Mouse input</a><ul><li class="level2"><a href="#cursor_pos">Cursor position</a></li>
<li class="level2"><a href="#cursor_mode">Cursor mode</a></li>
<li class="level2"><a href="#raw_mouse_motion">Raw mouse motion</a></li>
<li class="level2"><a href="#cursor_object">Cursor objects</a><ul><li class="level3"><a href="#cursor_custom">Custom cursor creation</a></li>
<li class="level3"><a href="#cursor_standard">Standard cursor creation</a></li>
<li class="level3"><a href="#cursor_destruction">Cursor destruction</a></li>
<li class="level3"><a href="#cursor_set">Cursor setting</a></li>
</ul>
</li>
<li class="level2"><a href="#cursor_enter">Cursor enter/leave events</a></li>
<li class="level2"><a href="#input_mouse_button">Mouse button input</a></li>
<li class="level2"><a href="#scrolling">Scroll input</a></li>
</ul>
</li>
<li class="level1"><a href="#joystick">Joystick input</a><ul><li class="level2"><a href="#joystick_axis">Joystick axis states</a></li>
<li class="level2"><a href="#joystick_button">Joystick button states</a></li>
<li class="level2"><a href="#joystick_hat">Joystick hat states</a></li>
<li class="level2"><a href="#joystick_name">Joystick name</a></li>
<li class="level2"><a href="#joystick_userptr">Joystick user pointer</a></li>
<li class="level2"><a href="#joystick_event">Joystick configuration changes</a></li>
<li class="level2"><a href="#gamepad">Gamepad input</a></li>
<li class="level2"><a href="#gamepad_mapping">Gamepad mappings</a></li>
</ul>
</li>
<li class="level1"><a href="#time">Time input</a></li>
<li class="level1"><a href="#clipboard">Clipboard input and output</a></li>
<li class="level1"><a href="#path_drop">Path drop input</a></li>
</ul>
</div>
<div class="textblock"><p>This guide introduces the input related functions of GLFW. For details on a specific function in this category, see the <a class="el" href="group__input.html">Input reference</a>. There are also guides for the other areas of GLFW.</p>
<ul>
<li><a class="el" href="intro_guide.html">Introduction to the API</a></li>
<li><a class="el" href="window_guide.html">Window guide</a></li>
<li><a class="el" href="context_guide.html">Context guide</a></li>
<li><a class="el" href="vulkan_guide.html">Vulkan guide</a></li>
<li><a class="el" href="monitor_guide.html">Monitor guide</a></li>
</ul>
<p>GLFW provides many kinds of input. While some can only be polled, like time, or only received via callbacks, like scrolling, many provide both callbacks and polling. Callbacks are more work to use than polling but is less CPU intensive and guarantees that you do not miss state changes.</p>
<p>All input callbacks receive a window handle. By using the <a class="el" href="window_guide.html#window_userptr">window user pointer</a>, you can access non-global structures or objects from your callbacks.</p>
<p>To get a better feel for how the various events callbacks behave, run the <code>events</code> test program. It registers every callback supported by GLFW and prints out all arguments provided for every event, along with time and sequence information.</p>
<h1><a class="anchor" id="events"></a>
Event processing</h1>
<p>GLFW needs to poll the window system for events both to provide input to the application and to prove to the window system that the application hasn't locked up. Event processing is normally done each frame after <a class="el" href="window_guide.html#buffer_swap">buffer swapping</a>. Even when you have no windows, event polling needs to be done in order to receive monitor and joystick connection events.</p>
<p>There are three functions for processing pending events. <a class="el" href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a>, processes only those events that have already been received and then returns immediately.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a>();</div>
<div class="ttc" id="agroup__window_html_ga37bd57223967b4211d60ca1a0bf3c832"><div class="ttname"><a href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a></div><div class="ttdeci">void glfwPollEvents(void)</div><div class="ttdoc">Processes all pending events.</div></div>
</div><!-- fragment --><p>This is the best choice when rendering continuously, like most games do.</p>
<p>If you only need to update the contents of the window when you receive new input, <a class="el" href="group__window.html#ga554e37d781f0a997656c26b2c56c835e">glfwWaitEvents</a> is a better choice.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga554e37d781f0a997656c26b2c56c835e">glfwWaitEvents</a>();</div>
<div class="ttc" id="agroup__window_html_ga554e37d781f0a997656c26b2c56c835e"><div class="ttname"><a href="group__window.html#ga554e37d781f0a997656c26b2c56c835e">glfwWaitEvents</a></div><div class="ttdeci">void glfwWaitEvents(void)</div><div class="ttdoc">Waits until events are queued and processes them.</div></div>
</div><!-- fragment --><p>It puts the thread to sleep until at least one event has been received and then processes all received events. This saves a great deal of CPU cycles and is useful for, for example, editing tools.</p>
<p>If you want to wait for events but have UI elements or other tasks that need periodic updates, <a class="el" href="group__window.html#ga605a178db92f1a7f1a925563ef3ea2cf">glfwWaitEventsTimeout</a> lets you specify a timeout.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga605a178db92f1a7f1a925563ef3ea2cf">glfwWaitEventsTimeout</a>(0.7);</div>
<div class="ttc" id="agroup__window_html_ga605a178db92f1a7f1a925563ef3ea2cf"><div class="ttname"><a href="group__window.html#ga605a178db92f1a7f1a925563ef3ea2cf">glfwWaitEventsTimeout</a></div><div class="ttdeci">void glfwWaitEventsTimeout(double timeout)</div><div class="ttdoc">Waits with timeout until events are queued and processes them.</div></div>
</div><!-- fragment --><p>It puts the thread to sleep until at least one event has been received, or until the specified number of seconds have elapsed. It then processes any received events.</p>
<p>If the main thread is sleeping in <a class="el" href="group__window.html#ga554e37d781f0a997656c26b2c56c835e">glfwWaitEvents</a>, you can wake it from another thread by posting an empty event to the event queue with <a class="el" href="group__window.html#gab5997a25187e9fd5c6f2ecbbc8dfd7e9">glfwPostEmptyEvent</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#gab5997a25187e9fd5c6f2ecbbc8dfd7e9">glfwPostEmptyEvent</a>();</div>
<div class="ttc" id="agroup__window_html_gab5997a25187e9fd5c6f2ecbbc8dfd7e9"><div class="ttname"><a href="group__window.html#gab5997a25187e9fd5c6f2ecbbc8dfd7e9">glfwPostEmptyEvent</a></div><div class="ttdeci">void glfwPostEmptyEvent(void)</div><div class="ttdoc">Posts an empty event to the event queue.</div></div>
</div><!-- fragment --><p>Do not assume that callbacks will <em>only</em> be called in response to the above functions. While it is necessary to process events in one or more of the ways above, window systems that require GLFW to register callbacks of its own can pass events to GLFW in response to many window system function calls. GLFW will pass those events on to the application callbacks before returning.</p>
<p>For example, on Windows the system function that <a class="el" href="group__window.html#ga371911f12c74c504dd8d47d832d095cb">glfwSetWindowSize</a> is implemented with will send window size events directly to the event callback that every window has and that GLFW implements for its windows. If you have set a <a class="el" href="window_guide.html#window_size">window size callback</a> GLFW will call it in turn with the new size before everything returns back out of the <a class="el" href="group__window.html#ga371911f12c74c504dd8d47d832d095cb">glfwSetWindowSize</a> call.</p>
<h1><a class="anchor" id="input_keyboard"></a>
Keyboard input</h1>
<p>GLFW divides keyboard input into two categories; key events and character events. Key events relate to actual physical keyboard keys, whereas character events relate to the text that is generated by pressing some of them.</p>
<p>Keys and characters do not map 1:1. A single key press may produce several characters, and a single character may require several keys to produce. This may not be the case on your machine, but your users are likely not all using the same keyboard layout, input method or even operating system as you.</p>
<h2><a class="anchor" id="input_key"></a>
Key input</h2>
<p>If you wish to be notified when a physical key is pressed or released or when it repeats, set a key callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#ga1caf18159767e761185e49a3be019f8d">glfwSetKeyCallback</a>(window, key_callback);</div>
<div class="ttc" id="agroup__input_html_ga1caf18159767e761185e49a3be019f8d"><div class="ttname"><a href="group__input.html#ga1caf18159767e761185e49a3be019f8d">glfwSetKeyCallback</a></div><div class="ttdeci">GLFWkeyfun glfwSetKeyCallback(GLFWwindow *window, GLFWkeyfun callback)</div><div class="ttdoc">Sets the key callback.</div></div>
</div><!-- fragment --><p>The callback function receives the <a class="el" href="group__keys.html">keyboard key</a>, platform-specific scancode, key action and <a class="el" href="group__mods.html">modifier bits</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> key_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> key, <span class="keywordtype">int</span> scancode, <span class="keywordtype">int</span> action, <span class="keywordtype">int</span> mods)</div>
<div class="line">{</div>
<div class="line">    <span class="keywordflow">if</span> (key == <a class="code hl_define" href="group__keys.html#gabf48fcc3afbe69349df432b470c96ef2">GLFW_KEY_E</a> &amp;&amp; action == <a class="code hl_define" href="group__input.html#ga2485743d0b59df3791c45951c4195265">GLFW_PRESS</a>)</div>
<div class="line">        activate_airship();</div>
<div class="line">}</div>
<div class="ttc" id="agroup__input_html_ga2485743d0b59df3791c45951c4195265"><div class="ttname"><a href="group__input.html#ga2485743d0b59df3791c45951c4195265">GLFW_PRESS</a></div><div class="ttdeci">#define GLFW_PRESS</div><div class="ttdoc">The key or mouse button was pressed.</div><div class="ttdef"><b>Definition</b> glfw3.h:338</div></div>
<div class="ttc" id="agroup__keys_html_gabf48fcc3afbe69349df432b470c96ef2"><div class="ttname"><a href="group__keys.html#gabf48fcc3afbe69349df432b470c96ef2">GLFW_KEY_E</a></div><div class="ttdeci">#define GLFW_KEY_E</div><div class="ttdef"><b>Definition</b> glfw3.h:418</div></div>
<div class="ttc" id="agroup__window_html_ga3c96d80d363e67d13a41b5d1821f3242"><div class="ttname"><a href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a></div><div class="ttdeci">struct GLFWwindow GLFWwindow</div><div class="ttdoc">Opaque window object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1403</div></div>
</div><!-- fragment --><p>The action is one of <code>GLFW_PRESS</code>, <code>GLFW_REPEAT</code> or <code>GLFW_RELEASE</code>. Events with <code>GLFW_PRESS</code> and <code>GLFW_RELEASE</code> actions are emitted for every key press. Most keys will also emit events with <code>GLFW_REPEAT</code> actions while a key is held down.</p>
<p>Note that many keyboards have a limit on how many keys being simultaneous held down that they can detect. This limit is called <a href="https://en.wikipedia.org/wiki/Key_rollover">key rollover</a>.</p>
<p>Key events with <code>GLFW_REPEAT</code> actions are intended for text input. They are emitted at the rate set in the user's keyboard settings. At most one key is repeated even if several keys are held down. <code>GLFW_REPEAT</code> actions should not be relied on to know which keys are being held down or to drive animation. Instead you should either save the state of relevant keys based on <code>GLFW_PRESS</code> and <code>GLFW_RELEASE</code> actions, or call <a class="el" href="group__input.html#gadd341da06bc8d418b4dc3a3518af9ad2">glfwGetKey</a>, which provides basic cached key state.</p>
<p>The key will be one of the existing <a class="el" href="group__keys.html">key tokens</a>, or <code>GLFW_KEY_UNKNOWN</code> if GLFW lacks a token for it, for example <em>E-mail</em> and <em>Play</em> keys.</p>
<p>The scancode is unique for every key, regardless of whether it has a key token. Scancodes are platform-specific but consistent over time, so keys will have different scancodes depending on the platform but they are safe to save to disk. You can query the scancode for any <a class="el" href="group__keys.html">key token</a> supported on the current platform with <a class="el" href="group__input.html#ga67ddd1b7dcbbaff03e4a76c0ea67103a">glfwGetKeyScancode</a>.</p>
<div class="fragment"><div class="line"><span class="keyword">const</span> <span class="keywordtype">int</span> scancode = <a class="code hl_function" href="group__input.html#ga67ddd1b7dcbbaff03e4a76c0ea67103a">glfwGetKeyScancode</a>(<a class="code hl_define" href="group__keys.html#gac1c42c0bf4192cea713c55598b06b744">GLFW_KEY_X</a>);</div>
<div class="line">set_key_mapping(scancode, swap_weapons);</div>
<div class="ttc" id="agroup__input_html_ga67ddd1b7dcbbaff03e4a76c0ea67103a"><div class="ttname"><a href="group__input.html#ga67ddd1b7dcbbaff03e4a76c0ea67103a">glfwGetKeyScancode</a></div><div class="ttdeci">int glfwGetKeyScancode(int key)</div><div class="ttdoc">Returns the platform-specific scancode of the specified key.</div></div>
<div class="ttc" id="agroup__keys_html_gac1c42c0bf4192cea713c55598b06b744"><div class="ttname"><a href="group__keys.html#gac1c42c0bf4192cea713c55598b06b744">GLFW_KEY_X</a></div><div class="ttdeci">#define GLFW_KEY_X</div><div class="ttdef"><b>Definition</b> glfw3.h:437</div></div>
</div><!-- fragment --><p>The last reported state for every physical key with a <a class="el" href="group__keys.html">key token</a> is also saved in per-window state arrays that can be polled with <a class="el" href="group__input.html#gadd341da06bc8d418b4dc3a3518af9ad2">glfwGetKey</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> state = <a class="code hl_function" href="group__input.html#gadd341da06bc8d418b4dc3a3518af9ad2">glfwGetKey</a>(window, <a class="code hl_define" href="group__keys.html#gabf48fcc3afbe69349df432b470c96ef2">GLFW_KEY_E</a>);</div>
<div class="line"><span class="keywordflow">if</span> (state == <a class="code hl_define" href="group__input.html#ga2485743d0b59df3791c45951c4195265">GLFW_PRESS</a>)</div>
<div class="line">{</div>
<div class="line">    activate_airship();</div>
<div class="line">}</div>
<div class="ttc" id="agroup__input_html_gadd341da06bc8d418b4dc3a3518af9ad2"><div class="ttname"><a href="group__input.html#gadd341da06bc8d418b4dc3a3518af9ad2">glfwGetKey</a></div><div class="ttdeci">int glfwGetKey(GLFWwindow *window, int key)</div><div class="ttdoc">Returns the last reported state of a keyboard key for the specified window.</div></div>
</div><!-- fragment --><p>The returned state is one of <code>GLFW_PRESS</code> or <code>GLFW_RELEASE</code>.</p>
<p>This function only returns cached key event state. It does not poll the system for the current state of the physical key. It also does not provide any key repeat information.</p>
<p><a class="anchor" id="GLFW_STICKY_KEYS"></a>Whenever you poll state, you risk missing the state change you are looking for. If a pressed key is released again before you poll its state, you will have missed the key press. The recommended solution for this is to use a key callback, but there is also the <code>GLFW_STICKY_KEYS</code> input mode.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a>(window, <a class="code hl_define" href="glfw3_8h.html#ae3bbe2315b7691ab088159eb6c9110fc">GLFW_STICKY_KEYS</a>, <a class="code hl_define" href="group__init.html#ga2744fbb29b5631bb28802dbe0cf36eba">GLFW_TRUE</a>);</div>
<div class="ttc" id="aglfw3_8h_html_ae3bbe2315b7691ab088159eb6c9110fc"><div class="ttname"><a href="glfw3_8h.html#ae3bbe2315b7691ab088159eb6c9110fc">GLFW_STICKY_KEYS</a></div><div class="ttdeci">#define GLFW_STICKY_KEYS</div><div class="ttdef"><b>Definition</b> glfw3.h:1153</div></div>
<div class="ttc" id="agroup__init_html_ga2744fbb29b5631bb28802dbe0cf36eba"><div class="ttname"><a href="group__init.html#ga2744fbb29b5631bb28802dbe0cf36eba">GLFW_TRUE</a></div><div class="ttdeci">#define GLFW_TRUE</div><div class="ttdoc">One.</div><div class="ttdef"><b>Definition</b> glfw3.h:312</div></div>
<div class="ttc" id="agroup__input_html_gaa92336e173da9c8834558b54ee80563b"><div class="ttname"><a href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a></div><div class="ttdeci">void glfwSetInputMode(GLFWwindow *window, int mode, int value)</div><div class="ttdoc">Sets an input option for the specified window.</div></div>
</div><!-- fragment --><p>When sticky keys mode is enabled, the pollable state of a key will remain <code>GLFW_PRESS</code> until the state of that key is polled with <a class="el" href="group__input.html#gadd341da06bc8d418b4dc3a3518af9ad2">glfwGetKey</a>. Once it has been polled, if a key release event had been processed in the meantime, the state will reset to <code>GLFW_RELEASE</code>, otherwise it will remain <code>GLFW_PRESS</code>.</p>
<p><a class="anchor" id="GLFW_LOCK_KEY_MODS"></a>If you wish to know what the state of the Caps Lock and Num Lock keys was when input events were generated, set the <code>GLFW_LOCK_KEY_MODS</code> input mode.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a>(window, <a class="code hl_define" href="glfw3_8h.html#a07b84de0b52143e1958f88a7d9105947">GLFW_LOCK_KEY_MODS</a>, <a class="code hl_define" href="group__init.html#ga2744fbb29b5631bb28802dbe0cf36eba">GLFW_TRUE</a>);</div>
<div class="ttc" id="aglfw3_8h_html_a07b84de0b52143e1958f88a7d9105947"><div class="ttname"><a href="glfw3_8h.html#a07b84de0b52143e1958f88a7d9105947">GLFW_LOCK_KEY_MODS</a></div><div class="ttdeci">#define GLFW_LOCK_KEY_MODS</div><div class="ttdef"><b>Definition</b> glfw3.h:1155</div></div>
</div><!-- fragment --><p>When this input mode is enabled, any callback that receives <a class="el" href="group__mods.html">modifier bits</a> will have the <a class="el" href="group__mods.html#gaefeef8fcf825a6e43e241b337897200f">GLFW_MOD_CAPS_LOCK</a> bit set if Caps Lock was on when the event occurred and the <a class="el" href="group__mods.html#ga64e020b8a42af8376e944baf61feecbe">GLFW_MOD_NUM_LOCK</a> bit set if Num Lock was on.</p>
<p>The <code>GLFW_KEY_LAST</code> constant holds the highest value of any <a class="el" href="group__keys.html">key token</a>.</p>
<h2><a class="anchor" id="input_char"></a>
Text input</h2>
<p>GLFW supports text input in the form of a stream of <a href="https://en.wikipedia.org/wiki/Unicode">Unicode code points</a>, as produced by the operating system text input system. Unlike key input, text input is affected by keyboard layouts and modifier keys and supports composing characters using <a href="https://en.wikipedia.org/wiki/Dead_key">dead keys</a>. Once received, you can encode the code points into UTF-8 or any other encoding you prefer.</p>
<p>Because an <code>unsigned int</code> is 32 bits long on all platforms supported by GLFW, you can treat the code point argument as native endian UTF-32.</p>
<p>If you wish to offer regular text input, set a character callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#gab25c4a220fd8f5717718dbc487828996">glfwSetCharCallback</a>(window, character_callback);</div>
<div class="ttc" id="agroup__input_html_gab25c4a220fd8f5717718dbc487828996"><div class="ttname"><a href="group__input.html#gab25c4a220fd8f5717718dbc487828996">glfwSetCharCallback</a></div><div class="ttdeci">GLFWcharfun glfwSetCharCallback(GLFWwindow *window, GLFWcharfun callback)</div><div class="ttdoc">Sets the Unicode character callback.</div></div>
</div><!-- fragment --><p>The callback function receives Unicode code points for key events that would have led to regular text input and generally behaves as a standard text field on that platform.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> character_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> codepoint)</div>
<div class="line">{</div>
<div class="line">}</div>
</div><!-- fragment --><h2><a class="anchor" id="input_key_name"></a>
Key names</h2>
<p>If you wish to refer to keys by name, you can query the keyboard layout dependent name of printable keys with <a class="el" href="group__input.html#gaeaed62e69c3bd62b7ff8f7b19913ce4f">glfwGetKeyName</a>.</p>
<div class="fragment"><div class="line"><span class="keyword">const</span> <span class="keywordtype">char</span>* key_name = <a class="code hl_function" href="group__input.html#gaeaed62e69c3bd62b7ff8f7b19913ce4f">glfwGetKeyName</a>(<a class="code hl_define" href="group__keys.html#gaa06a712e6202661fc03da5bdb7b6e545">GLFW_KEY_W</a>, 0);</div>
<div class="line">show_tutorial_hint(<span class="stringliteral">&quot;Press %s to move forward&quot;</span>, key_name);</div>
<div class="ttc" id="agroup__input_html_gaeaed62e69c3bd62b7ff8f7b19913ce4f"><div class="ttname"><a href="group__input.html#gaeaed62e69c3bd62b7ff8f7b19913ce4f">glfwGetKeyName</a></div><div class="ttdeci">const char * glfwGetKeyName(int key, int scancode)</div><div class="ttdoc">Returns the layout-specific name of the specified printable key.</div></div>
<div class="ttc" id="agroup__keys_html_gaa06a712e6202661fc03da5bdb7b6e545"><div class="ttname"><a href="group__keys.html#gaa06a712e6202661fc03da5bdb7b6e545">GLFW_KEY_W</a></div><div class="ttdeci">#define GLFW_KEY_W</div><div class="ttdef"><b>Definition</b> glfw3.h:436</div></div>
</div><!-- fragment --><p>This function can handle both <a class="el" href="input_guide.html#input_key">keys and scancodes</a>. If the specified key is <code>GLFW_KEY_UNKNOWN</code> then the scancode is used, otherwise it is ignored. This matches the behavior of the key callback, meaning the callback arguments can always be passed unmodified to this function.</p>
<h1><a class="anchor" id="input_mouse"></a>
Mouse input</h1>
<p>Mouse input comes in many forms, including mouse motion, button presses and scrolling offsets. The cursor appearance can also be changed, either to a custom image or a standard cursor shape from the system theme.</p>
<h2><a class="anchor" id="cursor_pos"></a>
Cursor position</h2>
<p>If you wish to be notified when the cursor moves over the window, set a cursor position callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#gac1f879ab7435d54d4d79bb469fe225d7">glfwSetCursorPosCallback</a>(window, cursor_position_callback);</div>
<div class="ttc" id="agroup__input_html_gac1f879ab7435d54d4d79bb469fe225d7"><div class="ttname"><a href="group__input.html#gac1f879ab7435d54d4d79bb469fe225d7">glfwSetCursorPosCallback</a></div><div class="ttdeci">GLFWcursorposfun glfwSetCursorPosCallback(GLFWwindow *window, GLFWcursorposfun callback)</div><div class="ttdoc">Sets the cursor position callback.</div></div>
</div><!-- fragment --><p>The callback functions receives the cursor position, measured in screen coordinates but relative to the top-left corner of the window content area. On platforms that provide it, the full sub-pixel cursor position is passed on.</p>
<div class="fragment"><div class="line"><span class="keyword">static</span> <span class="keywordtype">void</span> cursor_position_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">double</span> xpos, <span class="keywordtype">double</span> ypos)</div>
<div class="line">{</div>
<div class="line">}</div>
</div><!-- fragment --><p>The cursor position is also saved per-window and can be polled with <a class="el" href="group__input.html#ga01d37b6c40133676b9cea60ca1d7c0cc">glfwGetCursorPos</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">double</span> xpos, ypos;</div>
<div class="line"><a class="code hl_function" href="group__input.html#ga01d37b6c40133676b9cea60ca1d7c0cc">glfwGetCursorPos</a>(window, &amp;xpos, &amp;ypos);</div>
<div class="ttc" id="agroup__input_html_ga01d37b6c40133676b9cea60ca1d7c0cc"><div class="ttname"><a href="group__input.html#ga01d37b6c40133676b9cea60ca1d7c0cc">glfwGetCursorPos</a></div><div class="ttdeci">void glfwGetCursorPos(GLFWwindow *window, double *xpos, double *ypos)</div><div class="ttdoc">Retrieves the position of the cursor relative to the content area of the window.</div></div>
</div><!-- fragment --><h2><a class="anchor" id="cursor_mode"></a>
Cursor mode</h2>
<p><a class="anchor" id="GLFW_CURSOR"></a>The <code>GLFW_CURSOR</code> input mode provides several cursor modes for special forms of mouse motion input. By default, the cursor mode is <code>GLFW_CURSOR_NORMAL</code>, meaning the regular arrow cursor (or another cursor set with <a class="el" href="group__input.html#gad3b4f38c8d5dae036bc8fa959e18343e">glfwSetCursor</a>) is used and cursor motion is not limited.</p>
<p>If you wish to implement mouse motion based camera controls or other input schemes that require unlimited mouse movement, set the cursor mode to <code>GLFW_CURSOR_DISABLED</code>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a>(window, <a class="code hl_define" href="glfw3_8h.html#aade31da5b884a84a7625c6b059b9132c">GLFW_CURSOR</a>, <a class="code hl_define" href="glfw3_8h.html#a2315b99a329ce53e6a13a9d46fd5ca88">GLFW_CURSOR_DISABLED</a>);</div>
<div class="ttc" id="aglfw3_8h_html_a2315b99a329ce53e6a13a9d46fd5ca88"><div class="ttname"><a href="glfw3_8h.html#a2315b99a329ce53e6a13a9d46fd5ca88">GLFW_CURSOR_DISABLED</a></div><div class="ttdeci">#define GLFW_CURSOR_DISABLED</div><div class="ttdef"><b>Definition</b> glfw3.h:1160</div></div>
<div class="ttc" id="aglfw3_8h_html_aade31da5b884a84a7625c6b059b9132c"><div class="ttname"><a href="glfw3_8h.html#aade31da5b884a84a7625c6b059b9132c">GLFW_CURSOR</a></div><div class="ttdeci">#define GLFW_CURSOR</div><div class="ttdef"><b>Definition</b> glfw3.h:1152</div></div>
</div><!-- fragment --><p>This will hide the cursor and lock it to the specified window. GLFW will then take care of all the details of cursor re-centering and offset calculation and providing the application with a virtual cursor position. This virtual position is provided normally via both the cursor position callback and through polling.</p>
<dl class="section note"><dt>Note</dt><dd>You should not implement your own version of this functionality using other features of GLFW. It is not supported and will not work as robustly as <code>GLFW_CURSOR_DISABLED</code>.</dd></dl>
<p>If you only wish the cursor to become hidden when it is over a window but still want it to behave normally, set the cursor mode to <code>GLFW_CURSOR_HIDDEN</code>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a>(window, <a class="code hl_define" href="glfw3_8h.html#aade31da5b884a84a7625c6b059b9132c">GLFW_CURSOR</a>, <a class="code hl_define" href="glfw3_8h.html#ac4d5cb9d78de8573349c58763d53bf11">GLFW_CURSOR_HIDDEN</a>);</div>
<div class="ttc" id="aglfw3_8h_html_ac4d5cb9d78de8573349c58763d53bf11"><div class="ttname"><a href="glfw3_8h.html#ac4d5cb9d78de8573349c58763d53bf11">GLFW_CURSOR_HIDDEN</a></div><div class="ttdeci">#define GLFW_CURSOR_HIDDEN</div><div class="ttdef"><b>Definition</b> glfw3.h:1159</div></div>
</div><!-- fragment --><p>This mode puts no limit on the motion of the cursor.</p>
<p>If you wish the cursor to be visible but confined to the content area of the window, set the cursor mode to <code>GLFW_CURSOR_CAPTURED</code>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a>(window, <a class="code hl_define" href="glfw3_8h.html#aade31da5b884a84a7625c6b059b9132c">GLFW_CURSOR</a>, <a class="code hl_define" href="glfw3_8h.html#ac1dbfa0cb4641a0edc93412ade0895dc">GLFW_CURSOR_CAPTURED</a>);</div>
<div class="ttc" id="aglfw3_8h_html_ac1dbfa0cb4641a0edc93412ade0895dc"><div class="ttname"><a href="glfw3_8h.html#ac1dbfa0cb4641a0edc93412ade0895dc">GLFW_CURSOR_CAPTURED</a></div><div class="ttdeci">#define GLFW_CURSOR_CAPTURED</div><div class="ttdef"><b>Definition</b> glfw3.h:1161</div></div>
</div><!-- fragment --><p>The cursor will behave normally inside the content area but will not be able to leave unless the window loses focus.</p>
<p>To exit out of either of these special modes, restore the <code>GLFW_CURSOR_NORMAL</code> cursor mode.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a>(window, <a class="code hl_define" href="glfw3_8h.html#aade31da5b884a84a7625c6b059b9132c">GLFW_CURSOR</a>, <a class="code hl_define" href="glfw3_8h.html#ae04dd25c8577e19fa8c97368561f6c68">GLFW_CURSOR_NORMAL</a>);</div>
<div class="ttc" id="aglfw3_8h_html_ae04dd25c8577e19fa8c97368561f6c68"><div class="ttname"><a href="glfw3_8h.html#ae04dd25c8577e19fa8c97368561f6c68">GLFW_CURSOR_NORMAL</a></div><div class="ttdeci">#define GLFW_CURSOR_NORMAL</div><div class="ttdef"><b>Definition</b> glfw3.h:1158</div></div>
</div><!-- fragment --><p>If the cursor was disabled, this will move it back to its last visible position.</p>
<p><a class="anchor" id="GLFW_RAW_MOUSE_MOTION"></a></p>
<h2><a class="anchor" id="raw_mouse_motion"></a>
Raw mouse motion</h2>
<p>When the cursor is disabled, raw (unscaled and unaccelerated) mouse motion can be enabled if available.</p>
<p>Raw mouse motion is closer to the actual motion of the mouse across a surface. It is not affected by the scaling and acceleration applied to the motion of the desktop cursor. That processing is suitable for a cursor while raw motion is better for controlling for example a 3D camera. Because of this, raw mouse motion is only provided when the cursor is disabled.</p>
<p>Call <a class="el" href="group__input.html#gae4ee0dbd0d256183e1ea4026d897e1c2">glfwRawMouseMotionSupported</a> to check if the current machine provides raw motion and set the <code>GLFW_RAW_MOUSE_MOTION</code> input mode to enable it. It is disabled by default.</p>
<div class="fragment"><div class="line"><span class="keywordflow">if</span> (<a class="code hl_function" href="group__input.html#gae4ee0dbd0d256183e1ea4026d897e1c2">glfwRawMouseMotionSupported</a>())</div>
<div class="line">    <a class="code hl_function" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a>(window, <a class="code hl_define" href="glfw3_8h.html#aeeda1be76a44a1fc97c1282e06281fbb">GLFW_RAW_MOUSE_MOTION</a>, <a class="code hl_define" href="group__init.html#ga2744fbb29b5631bb28802dbe0cf36eba">GLFW_TRUE</a>);</div>
<div class="ttc" id="aglfw3_8h_html_aeeda1be76a44a1fc97c1282e06281fbb"><div class="ttname"><a href="glfw3_8h.html#aeeda1be76a44a1fc97c1282e06281fbb">GLFW_RAW_MOUSE_MOTION</a></div><div class="ttdeci">#define GLFW_RAW_MOUSE_MOTION</div><div class="ttdef"><b>Definition</b> glfw3.h:1156</div></div>
<div class="ttc" id="agroup__input_html_gae4ee0dbd0d256183e1ea4026d897e1c2"><div class="ttname"><a href="group__input.html#gae4ee0dbd0d256183e1ea4026d897e1c2">glfwRawMouseMotionSupported</a></div><div class="ttdeci">int glfwRawMouseMotionSupported(void)</div><div class="ttdoc">Returns whether raw mouse motion is supported.</div></div>
</div><!-- fragment --><p>If supported, raw mouse motion can be enabled or disabled per-window and at any time but it will only be provided when the cursor is disabled.</p>
<h2><a class="anchor" id="cursor_object"></a>
Cursor objects</h2>
<p>GLFW supports creating both custom and system theme cursor images, encapsulated as <a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a> objects. They are created with <a class="el" href="group__input.html#ga556f604f73af156c0db0e97c081373c3">glfwCreateCursor</a> or <a class="el" href="group__input.html#gaf2fb2eb2c9dd842d1cef8a34e3c6403e">glfwCreateStandardCursor</a> and destroyed with <a class="el" href="group__input.html#ga81b952cd1764274d0db7fb3c5a79ba6a">glfwDestroyCursor</a>, or <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a>, if any remain.</p>
<h3><a class="anchor" id="cursor_custom"></a>
Custom cursor creation</h3>
<p>A custom cursor is created with <a class="el" href="group__input.html#ga556f604f73af156c0db0e97c081373c3">glfwCreateCursor</a>, which returns a handle to the created cursor object. For example, this creates a 16x16 white square cursor with the hot-spot in the upper-left corner:</p>
<div class="fragment"><div class="line"><span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> pixels[16 * 16 * 4];</div>
<div class="line">memset(pixels, 0xff, <span class="keyword">sizeof</span>(pixels));</div>
<div class="line"> </div>
<div class="line"><a class="code hl_struct" href="struct_g_l_f_wimage.html">GLFWimage</a> image;</div>
<div class="line">image.<a class="code hl_variable" href="struct_g_l_f_wimage.html#af6a71cc999fe6d3aea31dd7e9687d835">width</a> = 16;</div>
<div class="line">image.<a class="code hl_variable" href="struct_g_l_f_wimage.html#a0b7d95368f0c80d5e5c9875057c7dbec">height</a> = 16;</div>
<div class="line">image.<a class="code hl_variable" href="struct_g_l_f_wimage.html#a0c532a5c2bb715555279b7817daba0fb">pixels</a> = pixels;</div>
<div class="line"> </div>
<div class="line"><a class="code hl_typedef" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a>* cursor = <a class="code hl_function" href="group__input.html#ga556f604f73af156c0db0e97c081373c3">glfwCreateCursor</a>(&amp;image, 0, 0);</div>
<div class="ttc" id="agroup__input_html_ga556f604f73af156c0db0e97c081373c3"><div class="ttname"><a href="group__input.html#ga556f604f73af156c0db0e97c081373c3">glfwCreateCursor</a></div><div class="ttdeci">GLFWcursor * glfwCreateCursor(const GLFWimage *image, int xhot, int yhot)</div><div class="ttdoc">Creates a custom cursor.</div></div>
<div class="ttc" id="agroup__input_html_ga89261ae18c75e863aaf2656ecdd238f4"><div class="ttname"><a href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a></div><div class="ttdeci">struct GLFWcursor GLFWcursor</div><div class="ttdoc">Opaque cursor object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1415</div></div>
<div class="ttc" id="astruct_g_l_f_wimage_html"><div class="ttname"><a href="struct_g_l_f_wimage.html">GLFWimage</a></div><div class="ttdoc">Image data.</div><div class="ttdef"><b>Definition</b> glfw3.h:2090</div></div>
<div class="ttc" id="astruct_g_l_f_wimage_html_a0b7d95368f0c80d5e5c9875057c7dbec"><div class="ttname"><a href="struct_g_l_f_wimage.html#a0b7d95368f0c80d5e5c9875057c7dbec">GLFWimage::height</a></div><div class="ttdeci">int height</div><div class="ttdef"><b>Definition</b> glfw3.h:2096</div></div>
<div class="ttc" id="astruct_g_l_f_wimage_html_a0c532a5c2bb715555279b7817daba0fb"><div class="ttname"><a href="struct_g_l_f_wimage.html#a0c532a5c2bb715555279b7817daba0fb">GLFWimage::pixels</a></div><div class="ttdeci">unsigned char * pixels</div><div class="ttdef"><b>Definition</b> glfw3.h:2099</div></div>
<div class="ttc" id="astruct_g_l_f_wimage_html_af6a71cc999fe6d3aea31dd7e9687d835"><div class="ttname"><a href="struct_g_l_f_wimage.html#af6a71cc999fe6d3aea31dd7e9687d835">GLFWimage::width</a></div><div class="ttdeci">int width</div><div class="ttdef"><b>Definition</b> glfw3.h:2093</div></div>
</div><!-- fragment --><p>If cursor creation fails, <code>NULL</code> will be returned, so it is necessary to check the return value.</p>
<p>The image data is 32-bit, little-endian, non-premultiplied RGBA, i.e. eight bits per channel with the red channel first. The pixels are arranged canonically as sequential rows, starting from the top-left corner.</p>
<h3><a class="anchor" id="cursor_standard"></a>
Standard cursor creation</h3>
<p>A cursor with a <a class="el" href="group__shapes.html">standard shape</a> from the current system cursor theme can be created with <a class="el" href="group__input.html#gaf2fb2eb2c9dd842d1cef8a34e3c6403e">glfwCreateStandardCursor</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_typedef" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a>* url_cursor = <a class="code hl_function" href="group__input.html#gaf2fb2eb2c9dd842d1cef8a34e3c6403e">glfwCreateStandardCursor</a>(<a class="code hl_define" href="group__shapes.html#gaad01a50929fb515bf27e4462c51f6ed0">GLFW_POINTING_HAND_CURSOR</a>);</div>
<div class="ttc" id="agroup__input_html_gaf2fb2eb2c9dd842d1cef8a34e3c6403e"><div class="ttname"><a href="group__input.html#gaf2fb2eb2c9dd842d1cef8a34e3c6403e">glfwCreateStandardCursor</a></div><div class="ttdeci">GLFWcursor * glfwCreateStandardCursor(int shape)</div><div class="ttdoc">Creates a cursor with a standard shape.</div></div>
<div class="ttc" id="agroup__shapes_html_gaad01a50929fb515bf27e4462c51f6ed0"><div class="ttname"><a href="group__shapes.html#gaad01a50929fb515bf27e4462c51f6ed0">GLFW_POINTING_HAND_CURSOR</a></div><div class="ttdeci">#define GLFW_POINTING_HAND_CURSOR</div><div class="ttdoc">The pointing hand cursor shape.</div><div class="ttdef"><b>Definition</b> glfw3.h:1212</div></div>
</div><!-- fragment --><p>These cursor objects behave in the exact same way as those created with <a class="el" href="group__input.html#ga556f604f73af156c0db0e97c081373c3">glfwCreateCursor</a> except that the system cursor theme provides the actual image.</p>
<p>A few of these shapes are not available everywhere. If a shape is unavailable, <code>NULL</code> is returned. See <a class="el" href="group__input.html#gaf2fb2eb2c9dd842d1cef8a34e3c6403e">glfwCreateStandardCursor</a> for details.</p>
<h3><a class="anchor" id="cursor_destruction"></a>
Cursor destruction</h3>
<p>When a cursor is no longer needed, destroy it with <a class="el" href="group__input.html#ga81b952cd1764274d0db7fb3c5a79ba6a">glfwDestroyCursor</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#ga81b952cd1764274d0db7fb3c5a79ba6a">glfwDestroyCursor</a>(cursor);</div>
<div class="ttc" id="agroup__input_html_ga81b952cd1764274d0db7fb3c5a79ba6a"><div class="ttname"><a href="group__input.html#ga81b952cd1764274d0db7fb3c5a79ba6a">glfwDestroyCursor</a></div><div class="ttdeci">void glfwDestroyCursor(GLFWcursor *cursor)</div><div class="ttdoc">Destroys a cursor.</div></div>
</div><!-- fragment --><p>Cursor destruction always succeeds. If the cursor is current for any window, that window will revert to the default cursor. This does not affect the cursor mode. All remaining cursors are destroyed when <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a> is called.</p>
<h3><a class="anchor" id="cursor_set"></a>
Cursor setting</h3>
<p>A cursor can be set as current for a window with <a class="el" href="group__input.html#gad3b4f38c8d5dae036bc8fa959e18343e">glfwSetCursor</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#gad3b4f38c8d5dae036bc8fa959e18343e">glfwSetCursor</a>(window, cursor);</div>
<div class="ttc" id="agroup__input_html_gad3b4f38c8d5dae036bc8fa959e18343e"><div class="ttname"><a href="group__input.html#gad3b4f38c8d5dae036bc8fa959e18343e">glfwSetCursor</a></div><div class="ttdeci">void glfwSetCursor(GLFWwindow *window, GLFWcursor *cursor)</div><div class="ttdoc">Sets the cursor for the window.</div></div>
</div><!-- fragment --><p>Once set, the cursor image will be used as long as the system cursor is over the content area of the window and the <a class="el" href="input_guide.html#cursor_mode">cursor mode</a> is set to <code>GLFW_CURSOR_NORMAL</code>.</p>
<p>A single cursor may be set for any number of windows.</p>
<p>To revert to the default cursor, set the cursor of that window to <code>NULL</code>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#gad3b4f38c8d5dae036bc8fa959e18343e">glfwSetCursor</a>(window, NULL);</div>
</div><!-- fragment --><p>When a cursor is destroyed, any window that has it set will revert to the default cursor. This does not affect the cursor mode.</p>
<h2><a class="anchor" id="cursor_enter"></a>
Cursor enter/leave events</h2>
<p>If you wish to be notified when the cursor enters or leaves the content area of a window, set a cursor enter/leave callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#gad27f8ad0142c038a281466c0966817d8">glfwSetCursorEnterCallback</a>(window, cursor_enter_callback);</div>
<div class="ttc" id="agroup__input_html_gad27f8ad0142c038a281466c0966817d8"><div class="ttname"><a href="group__input.html#gad27f8ad0142c038a281466c0966817d8">glfwSetCursorEnterCallback</a></div><div class="ttdeci">GLFWcursorenterfun glfwSetCursorEnterCallback(GLFWwindow *window, GLFWcursorenterfun callback)</div><div class="ttdoc">Sets the cursor enter/leave callback.</div></div>
</div><!-- fragment --><p>The callback function receives the new classification of the cursor.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> cursor_enter_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> entered)</div>
<div class="line">{</div>
<div class="line">    <span class="keywordflow">if</span> (entered)</div>
<div class="line">    {</div>
<div class="line">        <span class="comment">// The cursor entered the content area of the window</span></div>
<div class="line">    }</div>
<div class="line">    <span class="keywordflow">else</span></div>
<div class="line">    {</div>
<div class="line">        <span class="comment">// The cursor left the content area of the window</span></div>
<div class="line">    }</div>
<div class="line">}</div>
</div><!-- fragment --><p>You can query whether the cursor is currently inside the content area of the window with the <a class="el" href="window_guide.html#GLFW_HOVERED_attrib">GLFW_HOVERED</a> window attribute.</p>
<div class="fragment"><div class="line"><span class="keywordflow">if</span> (<a class="code hl_function" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>(window, <a class="code hl_define" href="group__window.html#ga8665c71c6fa3d22425c6a0e8a3f89d8a">GLFW_HOVERED</a>))</div>
<div class="line">{</div>
<div class="line">    highlight_interface();</div>
<div class="line">}</div>
<div class="ttc" id="agroup__window_html_ga8665c71c6fa3d22425c6a0e8a3f89d8a"><div class="ttname"><a href="group__window.html#ga8665c71c6fa3d22425c6a0e8a3f89d8a">GLFW_HOVERED</a></div><div class="ttdeci">#define GLFW_HOVERED</div><div class="ttdoc">Mouse cursor hover window attribute.</div><div class="ttdef"><b>Definition</b> glfw3.h:917</div></div>
<div class="ttc" id="agroup__window_html_gacccb29947ea4b16860ebef42c2cb9337"><div class="ttname"><a href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a></div><div class="ttdeci">int glfwGetWindowAttrib(GLFWwindow *window, int attrib)</div><div class="ttdoc">Returns an attribute of the specified window.</div></div>
</div><!-- fragment --><h2><a class="anchor" id="input_mouse_button"></a>
Mouse button input</h2>
<p>If you wish to be notified when a mouse button is pressed or released, set a mouse button callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#ga6ab84420974d812bee700e45284a723c">glfwSetMouseButtonCallback</a>(window, mouse_button_callback);</div>
<div class="ttc" id="agroup__input_html_ga6ab84420974d812bee700e45284a723c"><div class="ttname"><a href="group__input.html#ga6ab84420974d812bee700e45284a723c">glfwSetMouseButtonCallback</a></div><div class="ttdeci">GLFWmousebuttonfun glfwSetMouseButtonCallback(GLFWwindow *window, GLFWmousebuttonfun callback)</div><div class="ttdoc">Sets the mouse button callback.</div></div>
</div><!-- fragment --><p>The callback function receives the <a class="el" href="group__buttons.html">mouse button</a>, button action and <a class="el" href="group__mods.html">modifier bits</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> mouse_button_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> button, <span class="keywordtype">int</span> action, <span class="keywordtype">int</span> mods)</div>
<div class="line">{</div>
<div class="line">    <span class="keywordflow">if</span> (button == <a class="code hl_define" href="group__buttons.html#ga3e2f2cf3c4942df73cc094247d275e74">GLFW_MOUSE_BUTTON_RIGHT</a> &amp;&amp; action == <a class="code hl_define" href="group__input.html#ga2485743d0b59df3791c45951c4195265">GLFW_PRESS</a>)</div>
<div class="line">        popup_menu();</div>
<div class="line">}</div>
<div class="ttc" id="agroup__buttons_html_ga3e2f2cf3c4942df73cc094247d275e74"><div class="ttname"><a href="group__buttons.html#ga3e2f2cf3c4942df73cc094247d275e74">GLFW_MOUSE_BUTTON_RIGHT</a></div><div class="ttdeci">#define GLFW_MOUSE_BUTTON_RIGHT</div><div class="ttdef"><b>Definition</b> glfw3.h:583</div></div>
</div><!-- fragment --><p>The action is one of <code>GLFW_PRESS</code> or <code>GLFW_RELEASE</code>.</p>
<p>The last reported state for every <a class="el" href="group__buttons.html">supported mouse button</a> is also saved in per-window state arrays that can be polled with <a class="el" href="group__input.html#gac1473feacb5996c01a7a5a33b5066704">glfwGetMouseButton</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> state = <a class="code hl_function" href="group__input.html#gac1473feacb5996c01a7a5a33b5066704">glfwGetMouseButton</a>(window, <a class="code hl_define" href="group__buttons.html#gaf37100431dcd5082d48f95ee8bc8cd56">GLFW_MOUSE_BUTTON_LEFT</a>);</div>
<div class="line"><span class="keywordflow">if</span> (state == <a class="code hl_define" href="group__input.html#ga2485743d0b59df3791c45951c4195265">GLFW_PRESS</a>)</div>
<div class="line">{</div>
<div class="line">    upgrade_cow();</div>
<div class="line">}</div>
<div class="ttc" id="agroup__buttons_html_gaf37100431dcd5082d48f95ee8bc8cd56"><div class="ttname"><a href="group__buttons.html#gaf37100431dcd5082d48f95ee8bc8cd56">GLFW_MOUSE_BUTTON_LEFT</a></div><div class="ttdeci">#define GLFW_MOUSE_BUTTON_LEFT</div><div class="ttdef"><b>Definition</b> glfw3.h:582</div></div>
<div class="ttc" id="agroup__input_html_gac1473feacb5996c01a7a5a33b5066704"><div class="ttname"><a href="group__input.html#gac1473feacb5996c01a7a5a33b5066704">glfwGetMouseButton</a></div><div class="ttdeci">int glfwGetMouseButton(GLFWwindow *window, int button)</div><div class="ttdoc">Returns the last reported state of a mouse button for the specified window.</div></div>
</div><!-- fragment --><p>The returned state is one of <code>GLFW_PRESS</code> or <code>GLFW_RELEASE</code>.</p>
<p>This function only returns cached mouse button event state. It does not poll the system for the current state of the mouse button.</p>
<p><a class="anchor" id="GLFW_STICKY_MOUSE_BUTTONS"></a>Whenever you poll state, you risk missing the state change you are looking for. If a pressed mouse button is released again before you poll its state, you will have missed the button press. The recommended solution for this is to use a mouse button callback, but there is also the <code>GLFW_STICKY_MOUSE_BUTTONS</code> input mode.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a>(window, <a class="code hl_define" href="glfw3_8h.html#a4d7ce8ce71030c3b04e2b78145bc59d1">GLFW_STICKY_MOUSE_BUTTONS</a>, <a class="code hl_define" href="group__init.html#ga2744fbb29b5631bb28802dbe0cf36eba">GLFW_TRUE</a>);</div>
<div class="ttc" id="aglfw3_8h_html_a4d7ce8ce71030c3b04e2b78145bc59d1"><div class="ttname"><a href="glfw3_8h.html#a4d7ce8ce71030c3b04e2b78145bc59d1">GLFW_STICKY_MOUSE_BUTTONS</a></div><div class="ttdeci">#define GLFW_STICKY_MOUSE_BUTTONS</div><div class="ttdef"><b>Definition</b> glfw3.h:1154</div></div>
</div><!-- fragment --><p>When sticky mouse buttons mode is enabled, the pollable state of a mouse button will remain <code>GLFW_PRESS</code> until the state of that button is polled with <a class="el" href="group__input.html#gac1473feacb5996c01a7a5a33b5066704">glfwGetMouseButton</a>. Once it has been polled, if a mouse button release event had been processed in the meantime, the state will reset to <code>GLFW_RELEASE</code>, otherwise it will remain <code>GLFW_PRESS</code>.</p>
<p>The <code>GLFW_MOUSE_BUTTON_LAST</code> constant holds the highest value of any <a class="el" href="group__buttons.html">supported mouse button</a>.</p>
<h2><a class="anchor" id="scrolling"></a>
Scroll input</h2>
<p>If you wish to be notified when the user scrolls, whether with a mouse wheel or touchpad gesture, set a scroll callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#ga571e45a030ae4061f746ed56cb76aede">glfwSetScrollCallback</a>(window, scroll_callback);</div>
<div class="ttc" id="agroup__input_html_ga571e45a030ae4061f746ed56cb76aede"><div class="ttname"><a href="group__input.html#ga571e45a030ae4061f746ed56cb76aede">glfwSetScrollCallback</a></div><div class="ttdeci">GLFWscrollfun glfwSetScrollCallback(GLFWwindow *window, GLFWscrollfun callback)</div><div class="ttdoc">Sets the scroll callback.</div></div>
</div><!-- fragment --><p>The callback function receives two-dimensional scroll offsets.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> scroll_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">double</span> xoffset, <span class="keywordtype">double</span> yoffset)</div>
<div class="line">{</div>
<div class="line">}</div>
</div><!-- fragment --><p>A normal mouse wheel, being vertical, provides offsets along the Y-axis.</p>
<h1><a class="anchor" id="joystick"></a>
Joystick input</h1>
<p>The joystick functions expose connected joysticks and controllers, with both referred to as joysticks. It supports up to sixteen joysticks, ranging from <code>GLFW_JOYSTICK_1</code>, <code>GLFW_JOYSTICK_2</code> up to and including <code>GLFW_JOYSTICK_16</code> or <code>GLFW_JOYSTICK_LAST</code>. You can test whether a <a class="el" href="group__joysticks.html">joystick</a> is present with <a class="el" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> present = <a class="code hl_function" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a>(<a class="code hl_define" href="group__joysticks.html#ga34a0443d059e9f22272cd4669073f73d">GLFW_JOYSTICK_1</a>);</div>
<div class="ttc" id="agroup__input_html_gaed0966cee139d815317f9ffcba64c9f1"><div class="ttname"><a href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a></div><div class="ttdeci">int glfwJoystickPresent(int jid)</div><div class="ttdoc">Returns whether the specified joystick is present.</div></div>
<div class="ttc" id="agroup__joysticks_html_ga34a0443d059e9f22272cd4669073f73d"><div class="ttname"><a href="group__joysticks.html#ga34a0443d059e9f22272cd4669073f73d">GLFW_JOYSTICK_1</a></div><div class="ttdeci">#define GLFW_JOYSTICK_1</div><div class="ttdef"><b>Definition</b> glfw3.h:594</div></div>
</div><!-- fragment --><p>Each joystick has zero or more axes, zero or more buttons, zero or more hats, a human-readable name, a user pointer and an SDL compatible GUID.</p>
<p>Detected joysticks are added to the beginning of the array. Once a joystick is detected, it keeps its assigned ID until it is disconnected or the library is terminated, so as joysticks are connected and disconnected, there may appear gaps in the IDs.</p>
<p>Joystick axis, button and hat state is updated when polled and does not require a window to be created or events to be processed. However, if you want joystick connection and disconnection events reliably delivered to the <a class="el" href="input_guide.html#joystick_event">joystick callback</a> then you must <a class="el" href="input_guide.html#events">process events</a>.</p>
<p>To see all the properties of all connected joysticks in real-time, run the <code>joysticks</code> test program.</p>
<h2><a class="anchor" id="joystick_axis"></a>
Joystick axis states</h2>
<p>The positions of all axes of a joystick are returned by <a class="el" href="group__input.html#gaeb1c0191d3140a233a682987c61eb408">glfwGetJoystickAxes</a>. See the reference documentation for the lifetime of the returned array.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> count;</div>
<div class="line"><span class="keyword">const</span> <span class="keywordtype">float</span>* axes = <a class="code hl_function" href="group__input.html#gaeb1c0191d3140a233a682987c61eb408">glfwGetJoystickAxes</a>(<a class="code hl_define" href="group__joysticks.html#gae43281bc66d3fa5089fb50c3e7a28695">GLFW_JOYSTICK_5</a>, &amp;count);</div>
<div class="ttc" id="agroup__input_html_gaeb1c0191d3140a233a682987c61eb408"><div class="ttname"><a href="group__input.html#gaeb1c0191d3140a233a682987c61eb408">glfwGetJoystickAxes</a></div><div class="ttdeci">const float * glfwGetJoystickAxes(int jid, int *count)</div><div class="ttdoc">Returns the values of all axes of the specified joystick.</div></div>
<div class="ttc" id="agroup__joysticks_html_gae43281bc66d3fa5089fb50c3e7a28695"><div class="ttname"><a href="group__joysticks.html#gae43281bc66d3fa5089fb50c3e7a28695">GLFW_JOYSTICK_5</a></div><div class="ttdeci">#define GLFW_JOYSTICK_5</div><div class="ttdef"><b>Definition</b> glfw3.h:598</div></div>
</div><!-- fragment --><p>Each element in the returned array is a value between -1.0 and 1.0.</p>
<h2><a class="anchor" id="joystick_button"></a>
Joystick button states</h2>
<p>The states of all buttons of a joystick are returned by <a class="el" href="group__input.html#ga5ffe34739d3dc97efe432ed2d81d9938">glfwGetJoystickButtons</a>. See the reference documentation for the lifetime of the returned array.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> count;</div>
<div class="line"><span class="keyword">const</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>* buttons = <a class="code hl_function" href="group__input.html#ga5ffe34739d3dc97efe432ed2d81d9938">glfwGetJoystickButtons</a>(<a class="code hl_define" href="group__joysticks.html#gae6f3eedfeb42424c2f5e3161efb0b654">GLFW_JOYSTICK_3</a>, &amp;count);</div>
<div class="ttc" id="agroup__input_html_ga5ffe34739d3dc97efe432ed2d81d9938"><div class="ttname"><a href="group__input.html#ga5ffe34739d3dc97efe432ed2d81d9938">glfwGetJoystickButtons</a></div><div class="ttdeci">const unsigned char * glfwGetJoystickButtons(int jid, int *count)</div><div class="ttdoc">Returns the state of all buttons of the specified joystick.</div></div>
<div class="ttc" id="agroup__joysticks_html_gae6f3eedfeb42424c2f5e3161efb0b654"><div class="ttname"><a href="group__joysticks.html#gae6f3eedfeb42424c2f5e3161efb0b654">GLFW_JOYSTICK_3</a></div><div class="ttdeci">#define GLFW_JOYSTICK_3</div><div class="ttdef"><b>Definition</b> glfw3.h:596</div></div>
</div><!-- fragment --><p>Each element in the returned array is either <code>GLFW_PRESS</code> or <code>GLFW_RELEASE</code>.</p>
<p>For backward compatibility with earlier versions that did not have <a class="el" href="group__input.html#ga06e660841b3e79c54da4f54a932c5a2c">glfwGetJoystickHats</a>, the button array by default also includes all hats. See the reference documentation for <a class="el" href="group__input.html#ga5ffe34739d3dc97efe432ed2d81d9938">glfwGetJoystickButtons</a> for details.</p>
<h2><a class="anchor" id="joystick_hat"></a>
Joystick hat states</h2>
<p>The states of all hats are returned by <a class="el" href="group__input.html#ga06e660841b3e79c54da4f54a932c5a2c">glfwGetJoystickHats</a>. See the reference documentation for the lifetime of the returned array.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> count;</div>
<div class="line"><span class="keyword">const</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>* hats = <a class="code hl_function" href="group__input.html#ga06e660841b3e79c54da4f54a932c5a2c">glfwGetJoystickHats</a>(<a class="code hl_define" href="group__joysticks.html#ga20a9f4f3aaefed9ea5e66072fc588b87">GLFW_JOYSTICK_7</a>, &amp;count);</div>
<div class="ttc" id="agroup__input_html_ga06e660841b3e79c54da4f54a932c5a2c"><div class="ttname"><a href="group__input.html#ga06e660841b3e79c54da4f54a932c5a2c">glfwGetJoystickHats</a></div><div class="ttdeci">const unsigned char * glfwGetJoystickHats(int jid, int *count)</div><div class="ttdoc">Returns the state of all hats of the specified joystick.</div></div>
<div class="ttc" id="agroup__joysticks_html_ga20a9f4f3aaefed9ea5e66072fc588b87"><div class="ttname"><a href="group__joysticks.html#ga20a9f4f3aaefed9ea5e66072fc588b87">GLFW_JOYSTICK_7</a></div><div class="ttdeci">#define GLFW_JOYSTICK_7</div><div class="ttdef"><b>Definition</b> glfw3.h:600</div></div>
</div><!-- fragment --><p>Each element in the returned array is one of the following:</p>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Name   </th><th class="markdownTableHeadNone">Value    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_HAT_CENTERED</code>   </td><td class="markdownTableBodyNone">0    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_HAT_UP</code>   </td><td class="markdownTableBodyNone">1    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_HAT_RIGHT</code>   </td><td class="markdownTableBodyNone">2    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_HAT_DOWN</code>   </td><td class="markdownTableBodyNone">4    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_HAT_LEFT</code>   </td><td class="markdownTableBodyNone">8    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_HAT_RIGHT_UP</code>   </td><td class="markdownTableBodyNone"><code>GLFW_HAT_RIGHT</code> | <code>GLFW_HAT_UP</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_HAT_RIGHT_DOWN</code>   </td><td class="markdownTableBodyNone"><code>GLFW_HAT_RIGHT</code> | <code>GLFW_HAT_DOWN</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_HAT_LEFT_UP</code>   </td><td class="markdownTableBodyNone"><code>GLFW_HAT_LEFT</code> | <code>GLFW_HAT_UP</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_HAT_LEFT_DOWN</code>   </td><td class="markdownTableBodyNone"><code>GLFW_HAT_LEFT</code> | <code>GLFW_HAT_DOWN</code>   </td></tr>
</table>
<p>The diagonal directions are bitwise combinations of the primary (up, right, down and left) directions and you can test for these individually by ANDing it with the corresponding direction.</p>
<div class="fragment"><div class="line"><span class="keywordflow">if</span> (hats[2] &amp; <a class="code hl_define" href="group__hat__state.html#ga252586e3bbde75f4b0e07ad3124867f5">GLFW_HAT_RIGHT</a>)</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// State of hat 2 could be right-up, right or right-down</span></div>
<div class="line">}</div>
<div class="ttc" id="agroup__hat__state_html_ga252586e3bbde75f4b0e07ad3124867f5"><div class="ttname"><a href="group__hat__state.html#ga252586e3bbde75f4b0e07ad3124867f5">GLFW_HAT_RIGHT</a></div><div class="ttdeci">#define GLFW_HAT_RIGHT</div><div class="ttdef"><b>Definition</b> glfw3.h:357</div></div>
</div><!-- fragment --><p>For backward compatibility with earlier versions that did not have <a class="el" href="group__input.html#ga06e660841b3e79c54da4f54a932c5a2c">glfwGetJoystickHats</a>, all hats are by default also included in the button array. See the reference documentation for <a class="el" href="group__input.html#ga5ffe34739d3dc97efe432ed2d81d9938">glfwGetJoystickButtons</a> for details.</p>
<h2><a class="anchor" id="joystick_name"></a>
Joystick name</h2>
<p>The human-readable, UTF-8 encoded name of a joystick is returned by <a class="el" href="group__input.html#gac6a8e769e18e0bcfa9097793fc2c3978">glfwGetJoystickName</a>. See the reference documentation for the lifetime of the returned string.</p>
<div class="fragment"><div class="line"><span class="keyword">const</span> <span class="keywordtype">char</span>* name = <a class="code hl_function" href="group__input.html#gac6a8e769e18e0bcfa9097793fc2c3978">glfwGetJoystickName</a>(<a class="code hl_define" href="group__joysticks.html#ga97ddbcad02b7f48d74fad4ddb08fff59">GLFW_JOYSTICK_4</a>);</div>
<div class="ttc" id="agroup__input_html_gac6a8e769e18e0bcfa9097793fc2c3978"><div class="ttname"><a href="group__input.html#gac6a8e769e18e0bcfa9097793fc2c3978">glfwGetJoystickName</a></div><div class="ttdeci">const char * glfwGetJoystickName(int jid)</div><div class="ttdoc">Returns the name of the specified joystick.</div></div>
<div class="ttc" id="agroup__joysticks_html_ga97ddbcad02b7f48d74fad4ddb08fff59"><div class="ttname"><a href="group__joysticks.html#ga97ddbcad02b7f48d74fad4ddb08fff59">GLFW_JOYSTICK_4</a></div><div class="ttdeci">#define GLFW_JOYSTICK_4</div><div class="ttdef"><b>Definition</b> glfw3.h:597</div></div>
</div><!-- fragment --><p>Joystick names are not guaranteed to be unique. Two joysticks of the same model and make may have the same name. Only the <a class="el" href="group__joysticks.html">joystick ID</a> is guaranteed to be unique, and only until that joystick is disconnected.</p>
<h2><a class="anchor" id="joystick_userptr"></a>
Joystick user pointer</h2>
<p>Each joystick has a user pointer that can be set with <a class="el" href="group__input.html#ga6b2f72d64d636b48a727b437cbb7489e">glfwSetJoystickUserPointer</a> and queried with <a class="el" href="group__input.html#ga18cefd7265d1fa04f3fd38a6746db5f3">glfwGetJoystickUserPointer</a>. This can be used for any purpose you need and will not be modified by GLFW. The value will be kept until the joystick is disconnected or until the library is terminated.</p>
<p>The initial value of the pointer is <code>NULL</code>.</p>
<h2><a class="anchor" id="joystick_event"></a>
Joystick configuration changes</h2>
<p>If you wish to be notified when a joystick is connected or disconnected, set a joystick callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c">glfwSetJoystickCallback</a>(joystick_callback);</div>
<div class="ttc" id="agroup__input_html_ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c"><div class="ttname"><a href="group__input.html#ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c">glfwSetJoystickCallback</a></div><div class="ttdeci">GLFWjoystickfun glfwSetJoystickCallback(GLFWjoystickfun callback)</div><div class="ttdoc">Sets the joystick configuration callback.</div></div>
</div><!-- fragment --><p>The callback function receives the ID of the joystick that has been connected and disconnected and the event that occurred.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> joystick_callback(<span class="keywordtype">int</span> jid, <span class="keywordtype">int</span> event)</div>
<div class="line">{</div>
<div class="line">    <span class="keywordflow">if</span> (event == <a class="code hl_define" href="glfw3_8h.html#abe11513fd1ffbee5bb9b173f06028b9e">GLFW_CONNECTED</a>)</div>
<div class="line">    {</div>
<div class="line">        <span class="comment">// The joystick was connected</span></div>
<div class="line">    }</div>
<div class="line">    <span class="keywordflow">else</span> <span class="keywordflow">if</span> (event == <a class="code hl_define" href="glfw3_8h.html#aab64b25921ef21d89252d6f0a71bfc32">GLFW_DISCONNECTED</a>)</div>
<div class="line">    {</div>
<div class="line">        <span class="comment">// The joystick was disconnected</span></div>
<div class="line">    }</div>
<div class="line">}</div>
<div class="ttc" id="aglfw3_8h_html_aab64b25921ef21d89252d6f0a71bfc32"><div class="ttname"><a href="glfw3_8h.html#aab64b25921ef21d89252d6f0a71bfc32">GLFW_DISCONNECTED</a></div><div class="ttdeci">#define GLFW_DISCONNECTED</div><div class="ttdef"><b>Definition</b> glfw3.h:1291</div></div>
<div class="ttc" id="aglfw3_8h_html_abe11513fd1ffbee5bb9b173f06028b9e"><div class="ttname"><a href="glfw3_8h.html#abe11513fd1ffbee5bb9b173f06028b9e">GLFW_CONNECTED</a></div><div class="ttdeci">#define GLFW_CONNECTED</div><div class="ttdef"><b>Definition</b> glfw3.h:1290</div></div>
</div><!-- fragment --><p>For joystick connection and disconnection events to be delivered on all platforms, you need to call one of the <a class="el" href="input_guide.html#events">event processing</a> functions. Joystick disconnection may also be detected and the callback called by joystick functions. The function will then return whatever it returns for a disconnected joystick.</p>
<p>Only <a class="el" href="group__input.html#gac6a8e769e18e0bcfa9097793fc2c3978">glfwGetJoystickName</a> and <a class="el" href="group__input.html#ga18cefd7265d1fa04f3fd38a6746db5f3">glfwGetJoystickUserPointer</a> will return useful values for a disconnected joystick and only before the monitor callback returns.</p>
<h2><a class="anchor" id="gamepad"></a>
Gamepad input</h2>
<p>The joystick functions provide unlabeled axes, buttons and hats, with no indication of where they are located on the device. Their order may also vary between platforms even with the same device.</p>
<p>To solve this problem the SDL community crowdsourced the <a href="https://github.com/gabomdq/SDL_GameControllerDB">SDL_GameControllerDB</a> project, a database of mappings from many different devices to an Xbox-like gamepad.</p>
<p>GLFW supports this mapping format and contains a copy of the mappings available at the time of release. See <a class="el" href="input_guide.html#gamepad_mapping">Gamepad mappings</a> for how to update this at runtime. Mappings will be assigned to joysticks automatically any time a joystick is connected or the mappings are updated.</p>
<p>You can check whether a joystick is both present and has a gamepad mapping with <a class="el" href="group__input.html#gad0f676860f329d80f7e47e9f06a96f00">glfwJoystickIsGamepad</a>.</p>
<div class="fragment"><div class="line"><span class="keywordflow">if</span> (<a class="code hl_function" href="group__input.html#gad0f676860f329d80f7e47e9f06a96f00">glfwJoystickIsGamepad</a>(<a class="code hl_define" href="group__joysticks.html#ga6eab65ec88e65e0850ef8413504cb50c">GLFW_JOYSTICK_2</a>))</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// Use as gamepad</span></div>
<div class="line">}</div>
<div class="ttc" id="agroup__input_html_gad0f676860f329d80f7e47e9f06a96f00"><div class="ttname"><a href="group__input.html#gad0f676860f329d80f7e47e9f06a96f00">glfwJoystickIsGamepad</a></div><div class="ttdeci">int glfwJoystickIsGamepad(int jid)</div><div class="ttdoc">Returns whether the specified joystick has a gamepad mapping.</div></div>
<div class="ttc" id="agroup__joysticks_html_ga6eab65ec88e65e0850ef8413504cb50c"><div class="ttname"><a href="group__joysticks.html#ga6eab65ec88e65e0850ef8413504cb50c">GLFW_JOYSTICK_2</a></div><div class="ttdeci">#define GLFW_JOYSTICK_2</div><div class="ttdef"><b>Definition</b> glfw3.h:595</div></div>
</div><!-- fragment --><p>If you are only interested in gamepad input you can use this function instead of <a class="el" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a>.</p>
<p>You can query the human-readable name provided by the gamepad mapping with <a class="el" href="group__input.html#ga8aea73a1a25cc6c0486a617019f56728">glfwGetGamepadName</a>. This may or may not be the same as the <a class="el" href="input_guide.html#joystick_name">joystick name</a>.</p>
<div class="fragment"><div class="line"><span class="keyword">const</span> <span class="keywordtype">char</span>* name = <a class="code hl_function" href="group__input.html#ga8aea73a1a25cc6c0486a617019f56728">glfwGetGamepadName</a>(<a class="code hl_define" href="group__joysticks.html#ga20a9f4f3aaefed9ea5e66072fc588b87">GLFW_JOYSTICK_7</a>);</div>
<div class="ttc" id="agroup__input_html_ga8aea73a1a25cc6c0486a617019f56728"><div class="ttname"><a href="group__input.html#ga8aea73a1a25cc6c0486a617019f56728">glfwGetGamepadName</a></div><div class="ttdeci">const char * glfwGetGamepadName(int jid)</div><div class="ttdoc">Returns the human-readable gamepad name for the specified joystick.</div></div>
</div><!-- fragment --><p>To retrieve the gamepad state of a joystick, call <a class="el" href="group__input.html#gadccddea8bce6113fa459de379ddaf051">glfwGetGamepadState</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_struct" href="struct_g_l_f_wgamepadstate.html">GLFWgamepadstate</a> state;</div>
<div class="line"> </div>
<div class="line"><span class="keywordflow">if</span> (<a class="code hl_function" href="group__input.html#gadccddea8bce6113fa459de379ddaf051">glfwGetGamepadState</a>(<a class="code hl_define" href="group__joysticks.html#gae6f3eedfeb42424c2f5e3161efb0b654">GLFW_JOYSTICK_3</a>, &amp;state))</div>
<div class="line">{</div>
<div class="line">    <span class="keywordflow">if</span> (state.<a class="code hl_variable" href="struct_g_l_f_wgamepadstate.html#a27e9896b51c65df15fba2c7139bfdb9a">buttons</a>[<a class="code hl_define" href="group__gamepad__buttons.html#gae055a12fbf4b48b5954c8e1cd129b810">GLFW_GAMEPAD_BUTTON_A</a>])</div>
<div class="line">    {</div>
<div class="line">        input_jump();</div>
<div class="line">    }</div>
<div class="line"> </div>
<div class="line">    input_speed(state.<a class="code hl_variable" href="struct_g_l_f_wgamepadstate.html#a8b2c8939b1d31458de5359998375c189">axes</a>[<a class="code hl_define" href="group__gamepad__axes.html#ga121a7d5d20589a423cd1634dd6ee6eab">GLFW_GAMEPAD_AXIS_RIGHT_TRIGGER</a>]);</div>
<div class="line">}</div>
<div class="ttc" id="agroup__gamepad__axes_html_ga121a7d5d20589a423cd1634dd6ee6eab"><div class="ttname"><a href="group__gamepad__axes.html#ga121a7d5d20589a423cd1634dd6ee6eab">GLFW_GAMEPAD_AXIS_RIGHT_TRIGGER</a></div><div class="ttdeci">#define GLFW_GAMEPAD_AXIS_RIGHT_TRIGGER</div><div class="ttdef"><b>Definition</b> glfw3.h:655</div></div>
<div class="ttc" id="agroup__gamepad__buttons_html_gae055a12fbf4b48b5954c8e1cd129b810"><div class="ttname"><a href="group__gamepad__buttons.html#gae055a12fbf4b48b5954c8e1cd129b810">GLFW_GAMEPAD_BUTTON_A</a></div><div class="ttdeci">#define GLFW_GAMEPAD_BUTTON_A</div><div class="ttdef"><b>Definition</b> glfw3.h:620</div></div>
<div class="ttc" id="agroup__input_html_gadccddea8bce6113fa459de379ddaf051"><div class="ttname"><a href="group__input.html#gadccddea8bce6113fa459de379ddaf051">glfwGetGamepadState</a></div><div class="ttdeci">int glfwGetGamepadState(int jid, GLFWgamepadstate *state)</div><div class="ttdoc">Retrieves the state of the specified joystick remapped as a gamepad.</div></div>
<div class="ttc" id="astruct_g_l_f_wgamepadstate_html"><div class="ttname"><a href="struct_g_l_f_wgamepadstate.html">GLFWgamepadstate</a></div><div class="ttdoc">Gamepad input state.</div><div class="ttdef"><b>Definition</b> glfw3.h:2114</div></div>
<div class="ttc" id="astruct_g_l_f_wgamepadstate_html_a27e9896b51c65df15fba2c7139bfdb9a"><div class="ttname"><a href="struct_g_l_f_wgamepadstate.html#a27e9896b51c65df15fba2c7139bfdb9a">GLFWgamepadstate::buttons</a></div><div class="ttdeci">unsigned char buttons[15]</div><div class="ttdef"><b>Definition</b> glfw3.h:2118</div></div>
<div class="ttc" id="astruct_g_l_f_wgamepadstate_html_a8b2c8939b1d31458de5359998375c189"><div class="ttname"><a href="struct_g_l_f_wgamepadstate.html#a8b2c8939b1d31458de5359998375c189">GLFWgamepadstate::axes</a></div><div class="ttdeci">float axes[6]</div><div class="ttdef"><b>Definition</b> glfw3.h:2122</div></div>
</div><!-- fragment --><p>The <a class="el" href="struct_g_l_f_wgamepadstate.html">GLFWgamepadstate</a> struct has two arrays; one for button states and one for axis states. The values for each button and axis are the same as for the <a class="el" href="group__input.html#ga5ffe34739d3dc97efe432ed2d81d9938">glfwGetJoystickButtons</a> and <a class="el" href="group__input.html#gaeb1c0191d3140a233a682987c61eb408">glfwGetJoystickAxes</a> functions, i.e. <code>GLFW_PRESS</code> or <code>GLFW_RELEASE</code> for buttons and -1.0 to 1.0 inclusive for axes.</p>
<p>The sizes of the arrays and the positions within each array are fixed.</p>
<p>The <a class="el" href="group__gamepad__buttons.html">button indices</a> are <code>GLFW_GAMEPAD_BUTTON_A</code>, <code>GLFW_GAMEPAD_BUTTON_B</code>, <code>GLFW_GAMEPAD_BUTTON_X</code>, <code>GLFW_GAMEPAD_BUTTON_Y</code>, <code>GLFW_GAMEPAD_BUTTON_LEFT_BUMPER</code>, <code>GLFW_GAMEPAD_BUTTON_RIGHT_BUMPER</code>, <code>GLFW_GAMEPAD_BUTTON_BACK</code>, <code>GLFW_GAMEPAD_BUTTON_START</code>, <code>GLFW_GAMEPAD_BUTTON_GUIDE</code>, <code>GLFW_GAMEPAD_BUTTON_LEFT_THUMB</code>, <code>GLFW_GAMEPAD_BUTTON_RIGHT_THUMB</code>, <code>GLFW_GAMEPAD_BUTTON_DPAD_UP</code>, <code>GLFW_GAMEPAD_BUTTON_DPAD_RIGHT</code>, <code>GLFW_GAMEPAD_BUTTON_DPAD_DOWN</code> and <code>GLFW_GAMEPAD_BUTTON_DPAD_LEFT</code>.</p>
<p>For those who prefer, there are also the <code>GLFW_GAMEPAD_BUTTON_CROSS</code>, <code>GLFW_GAMEPAD_BUTTON_CIRCLE</code>, <code>GLFW_GAMEPAD_BUTTON_SQUARE</code> and <code>GLFW_GAMEPAD_BUTTON_TRIANGLE</code> aliases for the A, B, X and Y button indices.</p>
<p>The <a class="el" href="group__gamepad__axes.html">axis indices</a> are <code>GLFW_GAMEPAD_AXIS_LEFT_X</code>, <code>GLFW_GAMEPAD_AXIS_LEFT_Y</code>, <code>GLFW_GAMEPAD_AXIS_RIGHT_X</code>, <code>GLFW_GAMEPAD_AXIS_RIGHT_Y</code>, <code>GLFW_GAMEPAD_AXIS_LEFT_TRIGGER</code> and <code>GLFW_GAMEPAD_AXIS_RIGHT_TRIGGER</code>.</p>
<p>The <code>GLFW_GAMEPAD_BUTTON_LAST</code> and <code>GLFW_GAMEPAD_AXIS_LAST</code> constants equal the largest available index for each array.</p>
<h2><a class="anchor" id="gamepad_mapping"></a>
Gamepad mappings</h2>
<p>GLFW contains a copy of the mappings available in <a href="https://github.com/gabomdq/SDL_GameControllerDB">SDL_GameControllerDB</a> at the time of release. Newer ones can be added at runtime with <a class="el" href="group__input.html#gaed5104612f2fa8e66aa6e846652ad00f">glfwUpdateGamepadMappings</a>.</p>
<div class="fragment"><div class="line"><span class="keyword">const</span> <span class="keywordtype">char</span>* mappings = load_file_contents(<span class="stringliteral">&quot;game/data/gamecontrollerdb.txt&quot;</span>);</div>
<div class="line"> </div>
<div class="line"><a class="code hl_function" href="group__input.html#gaed5104612f2fa8e66aa6e846652ad00f">glfwUpdateGamepadMappings</a>(mappings);</div>
<div class="ttc" id="agroup__input_html_gaed5104612f2fa8e66aa6e846652ad00f"><div class="ttname"><a href="group__input.html#gaed5104612f2fa8e66aa6e846652ad00f">glfwUpdateGamepadMappings</a></div><div class="ttdeci">int glfwUpdateGamepadMappings(const char *string)</div><div class="ttdoc">Adds the specified SDL_GameControllerDB gamepad mappings.</div></div>
</div><!-- fragment --><p>This function supports everything from single lines up to and including the unmodified contents of the whole <code>gamecontrollerdb.txt</code> file.</p>
<p>If you are compiling GLFW from source with CMake you can update the built-in mappings by building the <em>update_mappings</em> target. This runs the <code>GenerateMappings.cmake</code> CMake script, which downloads <code>gamecontrollerdb.txt</code> and regenerates the <code>mappings.h</code> header file.</p>
<p>Below is a description of the mapping format. Please keep in mind that <b>this description is not authoritative</b>. The format is defined by the SDL and SDL_GameControllerDB projects and their documentation and code takes precedence.</p>
<p>Each mapping is a single line of comma-separated values describing the GUID, name and layout of the gamepad. Lines that do not begin with a hexadecimal digit are ignored.</p>
<p>The first value is always the gamepad GUID, a 32 character long hexadecimal string that typically identifies its make, model, revision and the type of connection to the computer. When this information is not available, the GUID is generated using the gamepad name. GLFW uses the SDL 2.0.5+ GUID format but can convert from the older formats.</p>
<p>The second value is always the human-readable name of the gamepad.</p>
<p>All subsequent values are in the form <code>&lt;field&gt;:&lt;value&gt;</code> and describe the layout of the mapping. These fields may not all be present and may occur in any order.</p>
<p>The button fields are <code>a</code>, <code>b</code>, <code>x</code>, <code>y</code>, <code>back</code>, <code>start</code>, <code>guide</code>, <code>dpup</code>, <code>dpright</code>, <code>dpdown</code>, <code>dpleft</code>, <code>leftshoulder</code>, <code>rightshoulder</code>, <code>leftstick</code> and <code>rightstick</code>.</p>
<p>The axis fields are <code>leftx</code>, <code>lefty</code>, <code>rightx</code>, <code>righty</code>, <code>lefttrigger</code> and <code>righttrigger</code>.</p>
<p>The value of an axis or button field can be a joystick button, a joystick axis, a hat bitmask or empty. Joystick buttons are specified as <code>bN</code>, for example <code>b2</code> for the third button. Joystick axes are specified as <code>aN</code>, for example <code>a7</code> for the eighth button. Joystick hat bit masks are specified as <code>hN.N</code>, for example <code>h0.8</code> for left on the first hat. More than one bit may be set in the mask.</p>
<p>Before an axis there may be a <code>+</code> or <code>-</code> range modifier, for example <code>+a3</code> for the positive half of the fourth axis. This restricts input to only the positive or negative halves of the joystick axis. After an axis or half-axis there may be the <code>~</code> inversion modifier, for example <code>a2~</code> or <code>-a7~</code>. This negates the values of the gamepad axis.</p>
<p>The hat bit mask match the <a class="el" href="group__hat__state.html">hat states</a> in the joystick functions.</p>
<p>There is also the special <code>platform</code> field that specifies which platform the mapping is valid for. Possible values are <code>Windows</code>, <code>Mac OS X</code> and <code>Linux</code>.</p>
<p>Below is an example of what a gamepad mapping might look like. It is the one built into GLFW for Xbox controllers accessed via the XInput API on Windows. This example has been broken into several lines to fit on the page, but real gamepad mappings must be a single line.</p>
<div class="fragment"><div class="line">78696e70757401000000000000000000,XInput Gamepad (GLFW),platform:Windows,a:b0,</div>
<div class="line">b:b1,x:b2,y:b3,leftshoulder:b4,rightshoulder:b5,back:b6,start:b7,leftstick:b8,</div>
<div class="line">rightstick:b9,leftx:a0,lefty:a1,rightx:a2,righty:a3,lefttrigger:a4,</div>
<div class="line">righttrigger:a5,dpup:h0.1,dpright:h0.2,dpdown:h0.4,dpleft:h0.8,</div>
</div><!-- fragment --><dl class="section note"><dt>Note</dt><dd>GLFW does not yet support the output range and modifiers <code>+</code> and <code>-</code> that were recently added to SDL. The input modifiers <code>+</code>, <code>-</code> and <code>~</code> are supported and described above.</dd></dl>
<h1><a class="anchor" id="time"></a>
Time input</h1>
<p>GLFW provides high-resolution time input, in seconds, with <a class="el" href="group__input.html#gaa6cf4e7a77158a3b8fd00328b1720a4a">glfwGetTime</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">double</span> seconds = <a class="code hl_function" href="group__input.html#gaa6cf4e7a77158a3b8fd00328b1720a4a">glfwGetTime</a>();</div>
<div class="ttc" id="agroup__input_html_gaa6cf4e7a77158a3b8fd00328b1720a4a"><div class="ttname"><a href="group__input.html#gaa6cf4e7a77158a3b8fd00328b1720a4a">glfwGetTime</a></div><div class="ttdeci">double glfwGetTime(void)</div><div class="ttdoc">Returns the GLFW time.</div></div>
</div><!-- fragment --><p>It returns the number of seconds since the library was initialized with <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a>. The platform-specific time sources used typically have micro- or nanosecond resolution.</p>
<p>You can modify the base time with <a class="el" href="group__input.html#gaf59589ef6e8b8c8b5ad184b25afd4dc0">glfwSetTime</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#gaf59589ef6e8b8c8b5ad184b25afd4dc0">glfwSetTime</a>(4.0);</div>
<div class="ttc" id="agroup__input_html_gaf59589ef6e8b8c8b5ad184b25afd4dc0"><div class="ttname"><a href="group__input.html#gaf59589ef6e8b8c8b5ad184b25afd4dc0">glfwSetTime</a></div><div class="ttdeci">void glfwSetTime(double time)</div><div class="ttdoc">Sets the GLFW time.</div></div>
</div><!-- fragment --><p>This sets the time to the specified time, in seconds, and it continues to count from there.</p>
<p>You can also access the raw timer used to implement the functions above, with <a class="el" href="group__input.html#ga09b2bd37d328e0b9456c7ec575cc26aa">glfwGetTimerValue</a>.</p>
<div class="fragment"><div class="line">uint64_t value = <a class="code hl_function" href="group__input.html#ga09b2bd37d328e0b9456c7ec575cc26aa">glfwGetTimerValue</a>();</div>
<div class="ttc" id="agroup__input_html_ga09b2bd37d328e0b9456c7ec575cc26aa"><div class="ttname"><a href="group__input.html#ga09b2bd37d328e0b9456c7ec575cc26aa">glfwGetTimerValue</a></div><div class="ttdeci">uint64_t glfwGetTimerValue(void)</div><div class="ttdoc">Returns the current value of the raw timer.</div></div>
</div><!-- fragment --><p>This value is in 1&#160;/&#160;frequency seconds. The frequency of the raw timer varies depending on the operating system and hardware. You can query the frequency, in Hz, with <a class="el" href="group__input.html#ga3289ee876572f6e91f06df3a24824443">glfwGetTimerFrequency</a>.</p>
<div class="fragment"><div class="line">uint64_t frequency = <a class="code hl_function" href="group__input.html#ga3289ee876572f6e91f06df3a24824443">glfwGetTimerFrequency</a>();</div>
<div class="ttc" id="agroup__input_html_ga3289ee876572f6e91f06df3a24824443"><div class="ttname"><a href="group__input.html#ga3289ee876572f6e91f06df3a24824443">glfwGetTimerFrequency</a></div><div class="ttdeci">uint64_t glfwGetTimerFrequency(void)</div><div class="ttdoc">Returns the frequency, in Hz, of the raw timer.</div></div>
</div><!-- fragment --><h1><a class="anchor" id="clipboard"></a>
Clipboard input and output</h1>
<p>If the system clipboard contains a UTF-8 encoded string or if it can be converted to one, you can retrieve it with <a class="el" href="group__input.html#ga71a5b20808ea92193d65c21b82580355">glfwGetClipboardString</a>. See the reference documentation for the lifetime of the returned string.</p>
<div class="fragment"><div class="line"><span class="keyword">const</span> <span class="keywordtype">char</span>* text = <a class="code hl_function" href="group__input.html#ga71a5b20808ea92193d65c21b82580355">glfwGetClipboardString</a>(NULL);</div>
<div class="line"><span class="keywordflow">if</span> (text)</div>
<div class="line">{</div>
<div class="line">    insert_text(text);</div>
<div class="line">}</div>
<div class="ttc" id="agroup__input_html_ga71a5b20808ea92193d65c21b82580355"><div class="ttname"><a href="group__input.html#ga71a5b20808ea92193d65c21b82580355">glfwGetClipboardString</a></div><div class="ttdeci">const char * glfwGetClipboardString(GLFWwindow *window)</div><div class="ttdoc">Returns the contents of the clipboard as a string.</div></div>
</div><!-- fragment --><p>If the clipboard is empty or if its contents could not be converted, <code>NULL</code> is returned.</p>
<p>The contents of the system clipboard can be set to a UTF-8 encoded string with <a class="el" href="group__input.html#gaba1f022c5eb07dfac421df34cdcd31dd">glfwSetClipboardString</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#gaba1f022c5eb07dfac421df34cdcd31dd">glfwSetClipboardString</a>(NULL, <span class="stringliteral">&quot;A string with words in it&quot;</span>);</div>
<div class="ttc" id="agroup__input_html_gaba1f022c5eb07dfac421df34cdcd31dd"><div class="ttname"><a href="group__input.html#gaba1f022c5eb07dfac421df34cdcd31dd">glfwSetClipboardString</a></div><div class="ttdeci">void glfwSetClipboardString(GLFWwindow *window, const char *string)</div><div class="ttdoc">Sets the clipboard to the specified string.</div></div>
</div><!-- fragment --><h1><a class="anchor" id="path_drop"></a>
Path drop input</h1>
<p>If you wish to receive the paths of files and/or directories dropped on a window, set a file drop callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#gab773f0ee0a07cff77a210cea40bc1f6b">glfwSetDropCallback</a>(window, drop_callback);</div>
<div class="ttc" id="agroup__input_html_gab773f0ee0a07cff77a210cea40bc1f6b"><div class="ttname"><a href="group__input.html#gab773f0ee0a07cff77a210cea40bc1f6b">glfwSetDropCallback</a></div><div class="ttdeci">GLFWdropfun glfwSetDropCallback(GLFWwindow *window, GLFWdropfun callback)</div><div class="ttdoc">Sets the path drop callback.</div></div>
</div><!-- fragment --><p>The callback function receives an array of paths encoded as UTF-8.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> drop_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> count, <span class="keyword">const</span> <span class="keywordtype">char</span>** paths)</div>
<div class="line">{</div>
<div class="line">    <span class="keywordtype">int</span> i;</div>
<div class="line">    <span class="keywordflow">for</span> (i = 0;  i &lt; count;  i++)</div>
<div class="line">        handle_dropped_file(paths[i]);</div>
<div class="line">}</div>
</div><!-- fragment --><p>The path array and its strings are only valid until the file drop callback returns, as they may have been generated specifically for that event. You need to make a deep copy of the array if you want to keep the paths. </p>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
