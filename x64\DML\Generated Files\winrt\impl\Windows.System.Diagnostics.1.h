// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_System_Diagnostics_1_H
#define WINRT_Windows_System_Diagnostics_1_H
#include "winrt/impl/Windows.System.Diagnostics.0.h"
WINRT_EXPORT namespace winrt::Windows::System::Diagnostics
{
    struct WINRT_IMPL_EMPTY_BASES IDiagnosticActionResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDiagnosticActionResult>
    {
        IDiagnosticActionResult(std::nullptr_t = nullptr) noexcept {}
        IDiagnosticActionResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDiagnosticInvoker :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDiagnosticInvoker>
    {
        IDiagnosticInvoker(std::nullptr_t = nullptr) noexcept {}
        IDiagnosticInvoker(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDiagnosticInvoker2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDiagnosticInvoker2>
    {
        IDiagnosticInvoker2(std::nullptr_t = nullptr) noexcept {}
        IDiagnosticInvoker2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDiagnosticInvokerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDiagnosticInvokerStatics>
    {
        IDiagnosticInvokerStatics(std::nullptr_t = nullptr) noexcept {}
        IDiagnosticInvokerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProcessCpuUsage :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProcessCpuUsage>
    {
        IProcessCpuUsage(std::nullptr_t = nullptr) noexcept {}
        IProcessCpuUsage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProcessCpuUsageReport :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProcessCpuUsageReport>
    {
        IProcessCpuUsageReport(std::nullptr_t = nullptr) noexcept {}
        IProcessCpuUsageReport(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProcessDiagnosticInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProcessDiagnosticInfo>
    {
        IProcessDiagnosticInfo(std::nullptr_t = nullptr) noexcept {}
        IProcessDiagnosticInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProcessDiagnosticInfo2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProcessDiagnosticInfo2>
    {
        IProcessDiagnosticInfo2(std::nullptr_t = nullptr) noexcept {}
        IProcessDiagnosticInfo2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProcessDiagnosticInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProcessDiagnosticInfoStatics>
    {
        IProcessDiagnosticInfoStatics(std::nullptr_t = nullptr) noexcept {}
        IProcessDiagnosticInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProcessDiagnosticInfoStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProcessDiagnosticInfoStatics2>
    {
        IProcessDiagnosticInfoStatics2(std::nullptr_t = nullptr) noexcept {}
        IProcessDiagnosticInfoStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProcessDiskUsage :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProcessDiskUsage>
    {
        IProcessDiskUsage(std::nullptr_t = nullptr) noexcept {}
        IProcessDiskUsage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProcessDiskUsageReport :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProcessDiskUsageReport>
    {
        IProcessDiskUsageReport(std::nullptr_t = nullptr) noexcept {}
        IProcessDiskUsageReport(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProcessMemoryUsage :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProcessMemoryUsage>
    {
        IProcessMemoryUsage(std::nullptr_t = nullptr) noexcept {}
        IProcessMemoryUsage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProcessMemoryUsageReport :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProcessMemoryUsageReport>
    {
        IProcessMemoryUsageReport(std::nullptr_t = nullptr) noexcept {}
        IProcessMemoryUsageReport(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemCpuUsage :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemCpuUsage>
    {
        ISystemCpuUsage(std::nullptr_t = nullptr) noexcept {}
        ISystemCpuUsage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemCpuUsageReport :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemCpuUsageReport>
    {
        ISystemCpuUsageReport(std::nullptr_t = nullptr) noexcept {}
        ISystemCpuUsageReport(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemDiagnosticInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemDiagnosticInfo>
    {
        ISystemDiagnosticInfo(std::nullptr_t = nullptr) noexcept {}
        ISystemDiagnosticInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemDiagnosticInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemDiagnosticInfoStatics>
    {
        ISystemDiagnosticInfoStatics(std::nullptr_t = nullptr) noexcept {}
        ISystemDiagnosticInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemDiagnosticInfoStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemDiagnosticInfoStatics2>
    {
        ISystemDiagnosticInfoStatics2(std::nullptr_t = nullptr) noexcept {}
        ISystemDiagnosticInfoStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemMemoryUsage :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemMemoryUsage>
    {
        ISystemMemoryUsage(std::nullptr_t = nullptr) noexcept {}
        ISystemMemoryUsage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemMemoryUsageReport :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemMemoryUsageReport>
    {
        ISystemMemoryUsageReport(std::nullptr_t = nullptr) noexcept {}
        ISystemMemoryUsageReport(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
