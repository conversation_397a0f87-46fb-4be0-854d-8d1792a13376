﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ts\src\cuda_perf.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ts\src\cuda_test.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ts\src\ocl_perf.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ts\src\ocl_test.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ts\src\ts.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ts\src\ts_arrtest.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ts\src\ts_func.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ts\src\ts_gtest.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ts\src\ts_perf.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ts\src\ts_tags.cpp">
      <Filter>Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\ts\include\opencv2\ts.hpp">
      <Filter>Include\opencv2</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\ts\include\opencv2\ts\cuda_perf.hpp">
      <Filter>Include\opencv2\ts</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\ts\include\opencv2\ts\cuda_test.hpp">
      <Filter>Include\opencv2\ts</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\ts\include\opencv2\ts\ocl_perf.hpp">
      <Filter>Include\opencv2\ts</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\ts\include\opencv2\ts\ocl_test.hpp">
      <Filter>Include\opencv2\ts</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\ts\include\opencv2\ts\ts_ext.hpp">
      <Filter>Include\opencv2\ts</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\ts\include\opencv2\ts\ts_gtest.h">
      <Filter>Include\opencv2\ts</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\ts\include\opencv2\ts\ts_perf.hpp">
      <Filter>Include\opencv2\ts</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\ts\src\precomp.hpp">
      <Filter>Src</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\ts\src\ts_tags.hpp">
      <Filter>Src</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\cvconfig.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\opencv2\opencv_modules.hpp">
      <Filter>Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Include">
      <UniqueIdentifier>{0D522D53-72BF-369F-A761-1665CACACBF7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Include\opencv2">
      <UniqueIdentifier>{0C987C71-7C33-37AF-8FEA-10E4920FC58F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Include\opencv2\ts">
      <UniqueIdentifier>{2048C462-D1BD-3594-93DD-7F6E8419BCAD}</UniqueIdentifier>
    </Filter>
    <Filter Include="Src">
      <UniqueIdentifier>{4C321FAF-208D-3921-91F6-A2582E33B23F}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
