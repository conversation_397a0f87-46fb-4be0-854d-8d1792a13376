﻿  1>
  -- Install configuration: "Release"
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/ippicv-readme.htm
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/ippicv-EULA.rtf
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/ippicv-third-party-programs.txt
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/ippiw-support.txt
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/ippiw-third-party-programs.txt
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/ippiw-EULA.rtf
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/flatbuffers-LICENSE.txt
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/opencl-headers-LICENSE.txt
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/ade-LICENSE
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/ffmpeg-license.txt
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/ffmpeg-readme.txt
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cvconfig.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/opencv_modules.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/x64/vc17/lib/OpenCVModules.cmake
  -- Installing: D:/AI/opencv/cudabuild/install/x64/vc17/lib/OpenCVModules-release.cmake
  -- Installing: D:/AI/opencv/cudabuild/install/x64/vc17/lib/OpenCVConfig-version.cmake
  -- Installing: D:/AI/opencv/cudabuild/install/x64/vc17/lib/OpenCVConfig.cmake
  -- Installing: D:/AI/opencv/cudabuild/install/./OpenCVConfig-version.cmake
  -- Installing: D:/AI/opencv/cudabuild/install/./OpenCVConfig.cmake
  -- Installing: D:/AI/opencv/cudabuild/install/./LICENSE
  -- Installing: D:/AI/opencv/cudabuild/install/./setup_vars_opencv4.cmd
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/zlib-LICENSE
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/libjpeg-turbo-README.md
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/libjpeg-turbo-LICENSE.md
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/libjpeg-turbo-README.ijg
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/libtiff-COPYRIGHT
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/libopenjp2-README.md
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/libopenjp2-LICENSE
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/libpng-LICENSE
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/libpng-README
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/openexr-LICENSE
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/openexr-AUTHORS.ilmbase
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/openexr-AUTHORS.openexr
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/protobuf-LICENSE
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/protobuf-README.md
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/ittnotify-LICENSE.BSD
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/ittnotify-LICENSE.GPL
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/opencv.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/SoftFloat-COPYING.txt
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/mscr-chi_table_LICENSE.txt
  -- Installing: D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_videoio_ffmpeg4100_64.dll
  -- Installing: D:/AI/opencv/cudabuild/install/etc/licenses/vasot-LICENSE.txt
  -- Installing: D:/AI/opencv/cudabuild/install/x64/vc17/lib/opencv_world4100.lib
  -- Installing: D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_world4100.dll
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/calib3d.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/calib3d/calib3d.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/calib3d/calib3d_c.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/affine.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/async.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/base.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/bindings_utils.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/bufferpool.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/check.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/core.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/core_c.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda.inl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/block.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/border_interpolate.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/color.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/common.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/datamov_utils.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/detail/color_detail.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/detail/reduce.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/detail/reduce_key_val.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/detail/transform_detail.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/detail/type_traits_detail.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/detail/vec_distance_detail.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/dynamic_smem.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/emulation.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/filters.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/funcattrib.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/functional.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/limits.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/reduce.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/saturate_cast.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/scan.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/simd_functions.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/transform.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/type_traits.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/utility.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/vec_distance.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/vec_math.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/vec_traits.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/warp.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/warp_reduce.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda/warp_shuffle.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda_stream_accessor.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cuda_types.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cv_cpu_dispatch.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cv_cpu_helper.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cvdef.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cvstd.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cvstd.inl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/cvstd_wrapper.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/detail/async_promise.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/detail/dispatch_helper.impl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/detail/exception_ptr.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/directx.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/dualquaternion.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/dualquaternion.inl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/eigen.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/fast_math.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/hal.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/interface.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_avx.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_avx512.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_cpp.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_forward.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_lasx.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_lsx.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_msa.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_neon.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_rvv.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_rvv071.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_rvv_010_compat_non-policy.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_rvv_010_compat_overloaded-non-policy.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_rvv_011_compat.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_rvv_compat_overloaded.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_rvv_scalable.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_sse.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_sse_em.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_vsx.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/intrin_wasm.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/msa_macros.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/hal/simd_utils.impl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/mat.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/mat.inl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/matx.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/matx.inl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/neon_utils.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/ocl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/ocl_genbase.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/ocl_defs.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/opencl_info.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/opencl_svm.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_clblas.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_clfft.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_core.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_core_wrappers.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_gl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_gl_wrappers.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_clblas.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_clfft.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_core.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_core_wrappers.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_gl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_gl_wrappers.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_svm_20.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_svm_definitions.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opencl/runtime/opencl_svm_hsa_extension.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/opengl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/operations.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/optim.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/ovx.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/parallel/backend/parallel_for.openmp.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/parallel/backend/parallel_for.tbb.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/parallel/parallel_backend.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/persistence.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/quaternion.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/quaternion.inl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/saturate.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/simd_intrinsics.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/softfloat.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/sse_utils.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/traits.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/types.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/types_c.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/utility.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/allocator_stats.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/allocator_stats.impl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/filesystem.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/fp_control_utils.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/instrumentation.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/logger.defines.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/logger.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/logtag.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/tls.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/utils/trace.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/va_intel.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/version.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core/vsx_utils.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/dnn.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/dnn/all_layers.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/dnn/dict.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/dnn/dnn.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/dnn/dnn.inl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/dnn/layer.details.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/dnn/layer.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/dnn/shape_utils.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/dnn/utils/debug_utils.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/dnn/utils/inference_engine.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/dnn/version.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/features2d.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/features2d/features2d.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/features2d/hal/interface.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/all_indices.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/allocator.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/any.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/autotuned_index.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/composite_index.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/config.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/defines.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/dist.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/dummy.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/dynamic_bitset.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/flann.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/flann_base.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/general.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/ground_truth.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/hdf5.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/heap.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/hierarchical_clustering_index.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/index_testing.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/kdtree_index.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/kdtree_single_index.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/kmeans_index.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/linear_index.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/logger.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/lsh_index.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/lsh_table.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/matrix.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/miniflann.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/nn_index.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/object_factory.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/params.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/random.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/result_set.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/sampling.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/saving.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/simplex_downhill.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/flann/timer.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/core.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/cpu/core.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/cpu/gcpukernel.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/cpu/imgproc.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/cpu/ot.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/cpu/stereo.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/cpu/video.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/fluid/core.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/fluid/gfluidbuffer.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/fluid/gfluidkernel.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/fluid/imgproc.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/garg.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/garray.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gasync_context.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gcall.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gcommon.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gcompiled.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gcompiled_async.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gcompoundkernel.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gcomputation.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gcomputation_async.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gframe.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gkernel.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gmat.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gmetaarg.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gopaque.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gproto.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gpu/core.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gpu/ggpukernel.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gpu/imgproc.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gscalar.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gstreaming.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gtransform.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gtype_traits.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/gtyped.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/imgproc.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/infer.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/infer/bindings_ie.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/infer/bindings_onnx.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/infer/bindings_ov.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/infer/ie.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/infer/onnx.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/infer/ov.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/infer/parsers.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/media.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/oak/infer.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/oak/oak.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/ocl/core.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/ocl/goclkernel.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/ocl/imgproc.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/opencv_includes.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/operators.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/ot.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/own/assert.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/own/convert.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/own/cvdefs.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/own/exports.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/own/mat.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/own/saturate.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/own/scalar.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/own/types.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/plaidml/core.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/plaidml/gplaidmlkernel.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/plaidml/plaidml.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/python/python.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/render.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/render/render.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/render/render_types.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/rmat.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/s11n.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/s11n/base.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/stereo.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/cap.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/desync.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/format.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/gstreamer/gstreamerpipeline.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/gstreamer/gstreamersource.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/meta.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/onevpl/accel_types.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/onevpl/cfg_params.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/onevpl/data_provider_interface.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/onevpl/default.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/onevpl/device_selector_interface.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/onevpl/source.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/queue_source.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/source.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/streaming/sync.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/util/any.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/util/compiler_hints.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/util/copy_through_move.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/util/optional.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/util/throw.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/util/type_traits.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/util/util.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/util/variant.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/gapi/video.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/highgui.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/highgui/highgui.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/highgui/highgui_c.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/imgcodecs.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/imgcodecs/imgcodecs.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/imgcodecs/imgcodecs_c.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/imgcodecs/ios.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/imgcodecs/legacy/constants_c.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/imgcodecs/macosx.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/imgproc.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/bindings.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/detail/gcgraph.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/detail/legacy.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/hal/hal.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/hal/interface.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/imgproc.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/imgproc_c.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/segmentation.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/imgproc/types_c.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ml.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ml/ml.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ml/ml.inl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/objdetect.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/aruco_board.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/aruco_detector.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/aruco_dictionary.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/barcode.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/charuco_detector.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/detection_based_tracker.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/face.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/graphical_code_detector.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/objdetect/objdetect.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/photo.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/photo/cuda.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/photo/legacy/constants_c.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/photo/photo.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stitching.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/autocalib.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/blenders.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/camera.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/exposure_compensate.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/matchers.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/motion_estimators.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/seam_finders.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/timelapsers.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/util.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/util_inl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/warpers.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stitching/detail/warpers_inl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stitching/warpers.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/video.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/video/background_segm.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/video/detail/tracking.detail.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/video/legacy/constants_c.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/video/tracking.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/video/video.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videoio.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videoio/cap_ios.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videoio/legacy/constants_c.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videoio/registry.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videoio/videoio.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videoio/videoio_c.h
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/world.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/aruco.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/aruco/aruco_calib.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/aruco/charuco.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/bgsegm.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/bioinspired.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/bioinspired/bioinspired.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/bioinspired/retina.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/bioinspired/retinafasttonemapping.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/bioinspired/transientareassegmentationmodule.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ccalib.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ccalib/multicalib.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ccalib/omnidir.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ccalib/randpattern.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudaarithm.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudabgsegm.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudacodec.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudafeatures2d.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudafilters.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudaimgproc.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudalegacy.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudalegacy/NCV.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudalegacy/NCVBroxOpticalFlow.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudalegacy/NCVHaarObjectDetection.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudalegacy/NCVPyramid.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudalegacy/NPP_staging.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudaobjdetect.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudaoptflow.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudastereo.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudawarping.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/block/block.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/block/detail/reduce.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/block/detail/reduce_key_val.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/block/dynamic_smem.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/block/reduce.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/block/scan.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/block/vec_distance.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/common.hpp
  -- Up-to-date: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/common.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/binary_func.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/binary_op.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/color.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/deriv.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/expr.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/per_element_func.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/reduction.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/unary_func.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/unary_op.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/expr/warping.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/functional/color_cvt.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/functional/detail/color_cvt.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/functional/functional.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/functional/tuple_adapter.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/copy.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/copy.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/histogram.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/integral.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/minmaxloc.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/pyr_down.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/pyr_up.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/reduce.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/reduce_to_column.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/reduce_to_row.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/split_merge.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/transform.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/detail/transpose.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/histogram.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/integral.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/pyramids.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/reduce.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/reduce_to_vec.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/split_merge.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/transform.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/grid/transpose.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/constant.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/deriv.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/detail/gpumat.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/extrapolation.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/glob.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/gpumat.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/interpolation.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/lut.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/mask.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/remap.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/resize.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/texture.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/traits.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/transform.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/warping.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/ptr2d/zip.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/atomic.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/detail/tuple.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/detail/type_traits.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/limits.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/saturate_cast.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/simd_functions.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/tuple.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/type_traits.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/vec_math.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/util/vec_traits.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/warp/detail/reduce.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/warp/detail/reduce_key_val.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/warp/reduce.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/warp/scan.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/warp/shuffle.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/cudev/warp/warp.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/ar_hmdb.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/ar_sports.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/dataset.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/fr_adience.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/fr_lfw.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/gr_chalearn.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/gr_skig.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/hpe_humaneva.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/hpe_parse.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/ir_affine.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/ir_robot.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/is_bsds.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/is_weizmann.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/msm_epfl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/msm_middlebury.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/or_imagenet.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/or_mnist.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/or_pascal.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/or_sun.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/pd_caltech.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/pd_inria.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/slam_kitti.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/slam_tumindoor.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/sr_bsds.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/sr_div2k.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/sr_general100.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/tr_chars.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/tr_icdar.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/tr_svt.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/track_alov.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/track_vot.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/datasets/util.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/core_detect.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/dnn_superres.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/dpm.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/face.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/face/bif.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/face/face_alignment.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/face/facemark.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/face/facemarkAAM.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/face/facemarkLBF.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/face/facemark_train.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/face/facerec.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/face/mace.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/face/predict_collector.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/fuzzy.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/fuzzy/fuzzy_F0_math.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/fuzzy/fuzzy_F1_math.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/fuzzy/fuzzy_image.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/fuzzy/types.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/hfs.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/intensity_transform.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/line_descriptor.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/line_descriptor/descriptor.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/mcc.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/mcc/ccm.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/mcc/checker_detector.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/mcc/checker_model.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/optflow.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/optflow/motempl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/optflow/pcaflow.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/optflow/rlofflow.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/optflow/sparse_matching_gpc.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/phase_unwrapping.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/phase_unwrapping/histogramphaseunwrapping.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/phase_unwrapping/phase_unwrapping.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/plot.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/quality.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/quality/quality_utils.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/quality/qualitybase.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/quality/qualitybrisque.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/quality/qualitygmsd.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/quality/qualitymse.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/quality/qualitypsnr.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/quality/qualityssim.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/rapid.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/reg/map.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/reg/mapaffine.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/reg/mapper.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/reg/mappergradaffine.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/reg/mappergradeuclid.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/reg/mappergradproj.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/reg/mappergradshift.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/reg/mappergradsimilar.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/reg/mapperpyramid.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/reg/mapprojec.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/reg/mapshift.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/rgbd.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/colored_kinfu.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/depth.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/detail/pose_graph.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/dynafu.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/intrinsics.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/kinfu.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/large_kinfu.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/linemod.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/rgbd/volume.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/saliency.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/saliency/saliencyBaseClasses.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/saliency/saliencySpecializedClasses.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/shape.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/shape/emdL1.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/shape/hist_cost.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/shape/shape.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/shape/shape_distance.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/shape/shape_transformer.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/signal.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/signal/signal_resample.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stereo.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stereo/descriptor.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stereo/quasi_dense_stereo.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/stereo/stereo.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/structured_light.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/structured_light/graycodepattern.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/structured_light/sinusoidalpattern.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/structured_light/structured_light.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/superres.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/superres/optical_flow.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/surface_matching.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/surface_matching/icp.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/surface_matching/pose_3d.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/surface_matching/ppf_helpers.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/surface_matching/ppf_match_3d.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/surface_matching/t_hash_int.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/text.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/text/erfilter.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/text/ocr.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/text/swt_text_detection.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/text/textDetector.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/tracking.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/tracking/feature.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/tracking/kalman_filters.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/tracking/onlineBoosting.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/tracking/tldDataset.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/tracking/tracking.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/tracking/tracking_by_matching.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/tracking/tracking_internals.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/tracking/tracking_legacy.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/tracking/twist.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videostab.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videostab/deblurring.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videostab/fast_marching.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videostab/fast_marching_inl.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videostab/frame_source.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videostab/global_motion.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videostab/inpainting.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videostab/log.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videostab/motion_core.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videostab/motion_stabilizing.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videostab/optical_flow.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videostab/outlier_rejection.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videostab/ring_buffer.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videostab/stabilizer.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/videostab/wobble_suppression.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/wechat_qrcode.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/xfeatures2d.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/xfeatures2d/cuda.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/xfeatures2d/nonfree.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/brightedges.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/color_match.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/deriche_filter.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/disparity_filter.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/edge_drawing.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/edge_filter.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/edgeboxes.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/edgepreserving_filter.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/estimated_covariance.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/fast_hough_transform.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/fast_line_detector.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/find_ellipses.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/fourier_descriptors.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/lsc.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/paillou_filter.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/peilin.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/radon_transform.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/ridgefilter.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/run_length_morphology.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/scansegment.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/seeds.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/segmentation.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/slic.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/sparse_match_interpolator.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/structured_edge_detection.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/ximgproc/weighted_median_filter.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/xobjdetect.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/xphoto.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/xphoto/bm3d_image_denoising.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/xphoto/dct_image_denoising.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/xphoto/inpainting.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/xphoto/oilpainting.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/xphoto/tonemap.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/xphoto/white_balance.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/bin/opencv_waldboost_detector.exe
  -- Installing: D:/AI/opencv/cudabuild/install/x64/vc17/lib/opencv_img_hash4100.lib
  -- Installing: D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_img_hash4100.dll
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/img_hash.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/img_hash/average_hash.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/img_hash/block_mean_hash.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/img_hash/color_moment_hash.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/img_hash/img_hash_base.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/img_hash/marr_hildreth_hash.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/img_hash/phash.hpp
  -- Installing: D:/AI/opencv/cudabuild/install/include/opencv2/img_hash/radial_variance_hash.hpp
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/__init__.py
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/load_config_py2.py
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/load_config_py3.py
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/config.py
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/misc/__init__.py
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/misc/version.py
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/mat_wrapper/__init__.py
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/utils/__init__.py
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/__init__.py
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/aruco
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/aruco/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/barcode
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/barcode/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/bgsegm
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/bgsegm/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/bioinspired
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/bioinspired/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ccm
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ccm/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/colored_kinfu
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/colored_kinfu/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/cuda
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/cuda/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/cudacodec
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/cudacodec/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/datasets
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/datasets/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/detail
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/detail/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/dnn
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/dnn/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/dnn_superres
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/dnn_superres/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/dpm
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/dpm/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/dynafu
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/dynafu/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/Error
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/Error/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/face
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/face/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/fisheye
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/fisheye/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/flann
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/flann/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ft
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ft/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/core
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/core/cpu
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/core/cpu/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/core/fluid
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/core/fluid/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/core/ocl
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/core/ocl/__init__.pyi
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/core/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/ie
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/ie/detail
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/ie/detail/__init__.pyi
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/ie/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/imgproc
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/imgproc/fluid
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/imgproc/fluid/__init__.pyi
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/imgproc/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/oak
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/oak/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/onnx
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/onnx/ep
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/onnx/ep/__init__.pyi
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/onnx/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/ot
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/ot/cpu
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/ot/cpu/__init__.pyi
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/ot/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/ov
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/ov/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/own
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/own/detail
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/own/detail/__init__.pyi
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/own/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/render
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/render/ocv
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/render/ocv/__init__.pyi
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/render/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/streaming
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/streaming/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/video
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/video/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/wip
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/wip/draw
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/wip/draw/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/wip/gst
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/wip/gst/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/wip/onevpl
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/wip/onevpl/__init__.pyi
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/wip/__init__.pyi
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/gapi/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/hfs
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/hfs/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/img_hash
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/img_hash/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/intensity_transform
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/intensity_transform/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ipp
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ipp/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/kinfu
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/kinfu/detail
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/kinfu/detail/__init__.pyi
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/kinfu/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/large_kinfu
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/large_kinfu/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/legacy
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/legacy/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/linemod
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/linemod/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/line_descriptor
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/line_descriptor/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/mcc
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/mcc/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ml
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ml/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/motempl
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/motempl/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/multicalib
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/multicalib/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ocl
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ocl/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ogl
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ogl/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/omnidir
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/omnidir/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/optflow
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/optflow/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/parallel
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/parallel/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/phase_unwrapping
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/phase_unwrapping/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/plot
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/plot/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ppf_match_3d
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ppf_match_3d/__init__.pyi
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/py.typed
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/quality
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/quality/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/rapid
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/rapid/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/reg
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/reg/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/rgbd
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/rgbd/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/saliency
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/saliency/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/samples
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/samples/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/segmentation
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/segmentation/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/signal
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/signal/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/stereo
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/stereo/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/structured_light
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/structured_light/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/text
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/text/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/typing
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/typing/__init__.py
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/utils
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/utils/fs
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/utils/fs/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/utils/nested
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/utils/nested/__init__.pyi
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/utils/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/videoio_registry
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/videoio_registry/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/videostab
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/videostab/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/wechat_qrcode
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/wechat_qrcode/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/xfeatures2d
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/xfeatures2d/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ximgproc
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ximgproc/segmentation
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ximgproc/segmentation/__init__.pyi
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/ximgproc/__init__.pyi
  -- Up-to-date: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/xphoto
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/xphoto/__init__.pyi
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/__init__.pyi
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/python-3.11/cv2.cp311-win_amd64.pyd
  -- Installing: C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/config-3.11.py
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_eye.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_eye_tree_eyeglasses.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_frontalcatface.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_frontalcatface_extended.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_frontalface_alt.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_frontalface_alt2.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_frontalface_alt_tree.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_frontalface_default.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_fullbody.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_lefteye_2splits.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_license_plate_rus_16stages.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_lowerbody.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_profileface.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_righteye_2splits.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_russian_plate_number.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_smile.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/haarcascades/haarcascade_upperbody.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/lbpcascades/lbpcascade_frontalcatface.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/lbpcascades/lbpcascade_frontalface.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/lbpcascades/lbpcascade_frontalface_improved.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/lbpcascades/lbpcascade_profileface.xml
  -- Installing: D:/AI/opencv/cudabuild/install/etc/lbpcascades/lbpcascade_silverware.xml
  -- Installing: D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_annotation.exe
  -- Installing: D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_visualisation.exe
  -- Installing: D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_interactive-calibration.exe
  -- Installing: D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_version.exe
  -- Installing: D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_version_win32.exe
  -- Installing: D:/AI/opencv/cudabuild/install/x64/vc17/bin/opencv_model_diagnostics.exe
