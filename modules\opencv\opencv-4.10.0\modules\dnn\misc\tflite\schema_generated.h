// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SCHEMA_OPENCV_TFLITE_H_
#define FLATBUFFERS_GENERATED_SCHEMA_OPENCV_TFLITE_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 23 &&
              FLATBUFFERS_VERSION_MINOR == 5 &&
              FLATBUFFERS_VERSION_REVISION == 9,
             "Non-compatible flatbuffers version included");

namespace opencv_tflite {

struct CustomQuantization;
struct CustomQuantizationBuilder;

struct QuantizationParameters;
struct QuantizationParametersBuilder;

struct Int32Vector;
struct Int32VectorBuilder;

struct Uint16Vector;
struct Uint16VectorBuilder;

struct Uint8Vector;
struct Uint8VectorBuilder;

struct DimensionMetadata;
struct DimensionMetadataBuilder;

struct SparsityParameters;
struct SparsityParametersBuilder;

struct VariantSubType;
struct VariantSubTypeBuilder;

struct Tensor;
struct TensorBuilder;

struct Conv2DOptions;
struct Conv2DOptionsBuilder;

struct Conv3DOptions;
struct Conv3DOptionsBuilder;

struct Pool2DOptions;
struct Pool2DOptionsBuilder;

struct DepthwiseConv2DOptions;
struct DepthwiseConv2DOptionsBuilder;

struct ConcatEmbeddingsOptions;
struct ConcatEmbeddingsOptionsBuilder;

struct LSHProjectionOptions;
struct LSHProjectionOptionsBuilder;

struct SVDFOptions;
struct SVDFOptionsBuilder;

struct RNNOptions;
struct RNNOptionsBuilder;

struct SequenceRNNOptions;
struct SequenceRNNOptionsBuilder;

struct BidirectionalSequenceRNNOptions;
struct BidirectionalSequenceRNNOptionsBuilder;

struct FullyConnectedOptions;
struct FullyConnectedOptionsBuilder;

struct SoftmaxOptions;
struct SoftmaxOptionsBuilder;

struct ConcatenationOptions;
struct ConcatenationOptionsBuilder;

struct AddOptions;
struct AddOptionsBuilder;

struct MulOptions;
struct MulOptionsBuilder;

struct L2NormOptions;
struct L2NormOptionsBuilder;

struct LocalResponseNormalizationOptions;
struct LocalResponseNormalizationOptionsBuilder;

struct LSTMOptions;
struct LSTMOptionsBuilder;

struct UnidirectionalSequenceLSTMOptions;
struct UnidirectionalSequenceLSTMOptionsBuilder;

struct BidirectionalSequenceLSTMOptions;
struct BidirectionalSequenceLSTMOptionsBuilder;

struct ResizeBilinearOptions;
struct ResizeBilinearOptionsBuilder;

struct ResizeNearestNeighborOptions;
struct ResizeNearestNeighborOptionsBuilder;

struct CallOptions;
struct CallOptionsBuilder;

struct PadOptions;
struct PadOptionsBuilder;

struct PadV2Options;
struct PadV2OptionsBuilder;

struct ReshapeOptions;
struct ReshapeOptionsBuilder;

struct SpaceToBatchNDOptions;
struct SpaceToBatchNDOptionsBuilder;

struct BatchToSpaceNDOptions;
struct BatchToSpaceNDOptionsBuilder;

struct SkipGramOptions;
struct SkipGramOptionsBuilder;

struct SpaceToDepthOptions;
struct SpaceToDepthOptionsBuilder;

struct DepthToSpaceOptions;
struct DepthToSpaceOptionsBuilder;

struct SubOptions;
struct SubOptionsBuilder;

struct DivOptions;
struct DivOptionsBuilder;

struct TopKV2Options;
struct TopKV2OptionsBuilder;

struct EmbeddingLookupSparseOptions;
struct EmbeddingLookupSparseOptionsBuilder;

struct GatherOptions;
struct GatherOptionsBuilder;

struct TransposeOptions;
struct TransposeOptionsBuilder;

struct ExpOptions;
struct ExpOptionsBuilder;

struct CosOptions;
struct CosOptionsBuilder;

struct ReducerOptions;
struct ReducerOptionsBuilder;

struct SqueezeOptions;
struct SqueezeOptionsBuilder;

struct SplitOptions;
struct SplitOptionsBuilder;

struct SplitVOptions;
struct SplitVOptionsBuilder;

struct StridedSliceOptions;
struct StridedSliceOptionsBuilder;

struct LogSoftmaxOptions;
struct LogSoftmaxOptionsBuilder;

struct CastOptions;
struct CastOptionsBuilder;

struct DequantizeOptions;
struct DequantizeOptionsBuilder;

struct MaximumMinimumOptions;
struct MaximumMinimumOptionsBuilder;

struct TileOptions;
struct TileOptionsBuilder;

struct ArgMaxOptions;
struct ArgMaxOptionsBuilder;

struct ArgMinOptions;
struct ArgMinOptionsBuilder;

struct GreaterOptions;
struct GreaterOptionsBuilder;

struct GreaterEqualOptions;
struct GreaterEqualOptionsBuilder;

struct LessOptions;
struct LessOptionsBuilder;

struct LessEqualOptions;
struct LessEqualOptionsBuilder;

struct NegOptions;
struct NegOptionsBuilder;

struct SelectOptions;
struct SelectOptionsBuilder;

struct SliceOptions;
struct SliceOptionsBuilder;

struct TransposeConvOptions;
struct TransposeConvOptionsBuilder;

struct ExpandDimsOptions;
struct ExpandDimsOptionsBuilder;

struct SparseToDenseOptions;
struct SparseToDenseOptionsBuilder;

struct EqualOptions;
struct EqualOptionsBuilder;

struct NotEqualOptions;
struct NotEqualOptionsBuilder;

struct ShapeOptions;
struct ShapeOptionsBuilder;

struct RankOptions;
struct RankOptionsBuilder;

struct PowOptions;
struct PowOptionsBuilder;

struct FakeQuantOptions;
struct FakeQuantOptionsBuilder;

struct PackOptions;
struct PackOptionsBuilder;

struct LogicalOrOptions;
struct LogicalOrOptionsBuilder;

struct OneHotOptions;
struct OneHotOptionsBuilder;

struct AbsOptions;
struct AbsOptionsBuilder;

struct HardSwishOptions;
struct HardSwishOptionsBuilder;

struct LogicalAndOptions;
struct LogicalAndOptionsBuilder;

struct LogicalNotOptions;
struct LogicalNotOptionsBuilder;

struct UnpackOptions;
struct UnpackOptionsBuilder;

struct FloorDivOptions;
struct FloorDivOptionsBuilder;

struct SquareOptions;
struct SquareOptionsBuilder;

struct ZerosLikeOptions;
struct ZerosLikeOptionsBuilder;

struct FillOptions;
struct FillOptionsBuilder;

struct FloorModOptions;
struct FloorModOptionsBuilder;

struct RangeOptions;
struct RangeOptionsBuilder;

struct LeakyReluOptions;
struct LeakyReluOptionsBuilder;

struct SquaredDifferenceOptions;
struct SquaredDifferenceOptionsBuilder;

struct MirrorPadOptions;
struct MirrorPadOptionsBuilder;

struct UniqueOptions;
struct UniqueOptionsBuilder;

struct ReverseV2Options;
struct ReverseV2OptionsBuilder;

struct AddNOptions;
struct AddNOptionsBuilder;

struct GatherNdOptions;
struct GatherNdOptionsBuilder;

struct WhereOptions;
struct WhereOptionsBuilder;

struct ReverseSequenceOptions;
struct ReverseSequenceOptionsBuilder;

struct MatrixDiagOptions;
struct MatrixDiagOptionsBuilder;

struct QuantizeOptions;
struct QuantizeOptionsBuilder;

struct MatrixSetDiagOptions;
struct MatrixSetDiagOptionsBuilder;

struct IfOptions;
struct IfOptionsBuilder;

struct CallOnceOptions;
struct CallOnceOptionsBuilder;

struct WhileOptions;
struct WhileOptionsBuilder;

struct NonMaxSuppressionV4Options;
struct NonMaxSuppressionV4OptionsBuilder;

struct NonMaxSuppressionV5Options;
struct NonMaxSuppressionV5OptionsBuilder;

struct ScatterNdOptions;
struct ScatterNdOptionsBuilder;

struct SelectV2Options;
struct SelectV2OptionsBuilder;

struct DensifyOptions;
struct DensifyOptionsBuilder;

struct SegmentSumOptions;
struct SegmentSumOptionsBuilder;

struct BatchMatMulOptions;
struct BatchMatMulOptionsBuilder;

struct CumsumOptions;
struct CumsumOptionsBuilder;

struct BroadcastToOptions;
struct BroadcastToOptionsBuilder;

struct Rfft2dOptions;
struct Rfft2dOptionsBuilder;

struct HashtableOptions;
struct HashtableOptionsBuilder;

struct HashtableFindOptions;
struct HashtableFindOptionsBuilder;

struct HashtableImportOptions;
struct HashtableImportOptionsBuilder;

struct HashtableSizeOptions;
struct HashtableSizeOptionsBuilder;

struct VarHandleOptions;
struct VarHandleOptionsBuilder;

struct ReadVariableOptions;
struct ReadVariableOptionsBuilder;

struct AssignVariableOptions;
struct AssignVariableOptionsBuilder;

struct RandomOptions;
struct RandomOptionsBuilder;

struct BucketizeOptions;
struct BucketizeOptionsBuilder;

struct GeluOptions;
struct GeluOptionsBuilder;

struct DynamicUpdateSliceOptions;
struct DynamicUpdateSliceOptionsBuilder;

struct UnsortedSegmentProdOptions;
struct UnsortedSegmentProdOptionsBuilder;

struct UnsortedSegmentMaxOptions;
struct UnsortedSegmentMaxOptionsBuilder;

struct UnsortedSegmentSumOptions;
struct UnsortedSegmentSumOptionsBuilder;

struct ATan2Options;
struct ATan2OptionsBuilder;

struct UnsortedSegmentMinOptions;
struct UnsortedSegmentMinOptionsBuilder;

struct SignOptions;
struct SignOptionsBuilder;

struct OperatorCode;
struct OperatorCodeBuilder;

struct Operator;
struct OperatorBuilder;

struct SubGraph;
struct SubGraphBuilder;

struct Buffer;
struct BufferBuilder;

struct Metadata;
struct MetadataBuilder;

struct TensorMap;
struct TensorMapBuilder;

struct SignatureDef;
struct SignatureDefBuilder;

struct Model;
struct ModelBuilder;

enum TensorType : int8_t {
  TensorType_FLOAT32 = 0,
  TensorType_FLOAT16 = 1,
  TensorType_INT32 = 2,
  TensorType_UINT8 = 3,
  TensorType_INT64 = 4,
  TensorType_STRING = 5,
  TensorType_BOOL = 6,
  TensorType_INT16 = 7,
  TensorType_COMPLEX64 = 8,
  TensorType_INT8 = 9,
  TensorType_FLOAT64 = 10,
  TensorType_COMPLEX128 = 11,
  TensorType_UINT64 = 12,
  TensorType_RESOURCE = 13,
  TensorType_VARIANT = 14,
  TensorType_UINT32 = 15,
  TensorType_UINT16 = 16,
  TensorType_INT4 = 17,
  TensorType_MIN = TensorType_FLOAT32,
  TensorType_MAX = TensorType_INT4
};

inline const TensorType (&EnumValuesTensorType())[18] {
  static const TensorType values[] = {
    TensorType_FLOAT32,
    TensorType_FLOAT16,
    TensorType_INT32,
    TensorType_UINT8,
    TensorType_INT64,
    TensorType_STRING,
    TensorType_BOOL,
    TensorType_INT16,
    TensorType_COMPLEX64,
    TensorType_INT8,
    TensorType_FLOAT64,
    TensorType_COMPLEX128,
    TensorType_UINT64,
    TensorType_RESOURCE,
    TensorType_VARIANT,
    TensorType_UINT32,
    TensorType_UINT16,
    TensorType_INT4
  };
  return values;
}

inline const char * const *EnumNamesTensorType() {
  static const char * const names[19] = {
    "FLOAT32",
    "FLOAT16",
    "INT32",
    "UINT8",
    "INT64",
    "STRING",
    "BOOL",
    "INT16",
    "COMPLEX64",
    "INT8",
    "FLOAT64",
    "COMPLEX128",
    "UINT64",
    "RESOURCE",
    "VARIANT",
    "UINT32",
    "UINT16",
    "INT4",
    nullptr
  };
  return names;
}

inline const char *EnumNameTensorType(TensorType e) {
  if (::flatbuffers::IsOutRange(e, TensorType_FLOAT32, TensorType_INT4)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesTensorType()[index];
}

enum QuantizationDetails : uint8_t {
  QuantizationDetails_NONE = 0,
  QuantizationDetails_CustomQuantization = 1,
  QuantizationDetails_MIN = QuantizationDetails_NONE,
  QuantizationDetails_MAX = QuantizationDetails_CustomQuantization
};

inline const QuantizationDetails (&EnumValuesQuantizationDetails())[2] {
  static const QuantizationDetails values[] = {
    QuantizationDetails_NONE,
    QuantizationDetails_CustomQuantization
  };
  return values;
}

inline const char * const *EnumNamesQuantizationDetails() {
  static const char * const names[3] = {
    "NONE",
    "CustomQuantization",
    nullptr
  };
  return names;
}

inline const char *EnumNameQuantizationDetails(QuantizationDetails e) {
  if (::flatbuffers::IsOutRange(e, QuantizationDetails_NONE, QuantizationDetails_CustomQuantization)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesQuantizationDetails()[index];
}

template<typename T> struct QuantizationDetailsTraits {
  static const QuantizationDetails enum_value = QuantizationDetails_NONE;
};

template<> struct QuantizationDetailsTraits<opencv_tflite::CustomQuantization> {
  static const QuantizationDetails enum_value = QuantizationDetails_CustomQuantization;
};

bool VerifyQuantizationDetails(::flatbuffers::Verifier &verifier, const void *obj, QuantizationDetails type);
bool VerifyQuantizationDetailsVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

enum DimensionType : int8_t {
  DimensionType_DENSE = 0,
  DimensionType_SPARSE_CSR = 1,
  DimensionType_MIN = DimensionType_DENSE,
  DimensionType_MAX = DimensionType_SPARSE_CSR
};

inline const DimensionType (&EnumValuesDimensionType())[2] {
  static const DimensionType values[] = {
    DimensionType_DENSE,
    DimensionType_SPARSE_CSR
  };
  return values;
}

inline const char * const *EnumNamesDimensionType() {
  static const char * const names[3] = {
    "DENSE",
    "SPARSE_CSR",
    nullptr
  };
  return names;
}

inline const char *EnumNameDimensionType(DimensionType e) {
  if (::flatbuffers::IsOutRange(e, DimensionType_DENSE, DimensionType_SPARSE_CSR)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDimensionType()[index];
}

enum SparseIndexVector : uint8_t {
  SparseIndexVector_NONE = 0,
  SparseIndexVector_Int32Vector = 1,
  SparseIndexVector_Uint16Vector = 2,
  SparseIndexVector_Uint8Vector = 3,
  SparseIndexVector_MIN = SparseIndexVector_NONE,
  SparseIndexVector_MAX = SparseIndexVector_Uint8Vector
};

inline const SparseIndexVector (&EnumValuesSparseIndexVector())[4] {
  static const SparseIndexVector values[] = {
    SparseIndexVector_NONE,
    SparseIndexVector_Int32Vector,
    SparseIndexVector_Uint16Vector,
    SparseIndexVector_Uint8Vector
  };
  return values;
}

inline const char * const *EnumNamesSparseIndexVector() {
  static const char * const names[5] = {
    "NONE",
    "Int32Vector",
    "Uint16Vector",
    "Uint8Vector",
    nullptr
  };
  return names;
}

inline const char *EnumNameSparseIndexVector(SparseIndexVector e) {
  if (::flatbuffers::IsOutRange(e, SparseIndexVector_NONE, SparseIndexVector_Uint8Vector)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesSparseIndexVector()[index];
}

template<typename T> struct SparseIndexVectorTraits {
  static const SparseIndexVector enum_value = SparseIndexVector_NONE;
};

template<> struct SparseIndexVectorTraits<opencv_tflite::Int32Vector> {
  static const SparseIndexVector enum_value = SparseIndexVector_Int32Vector;
};

template<> struct SparseIndexVectorTraits<opencv_tflite::Uint16Vector> {
  static const SparseIndexVector enum_value = SparseIndexVector_Uint16Vector;
};

template<> struct SparseIndexVectorTraits<opencv_tflite::Uint8Vector> {
  static const SparseIndexVector enum_value = SparseIndexVector_Uint8Vector;
};

bool VerifySparseIndexVector(::flatbuffers::Verifier &verifier, const void *obj, SparseIndexVector type);
bool VerifySparseIndexVectorVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

enum BuiltinOperator : int32_t {
  BuiltinOperator_ADD = 0,
  BuiltinOperator_AVERAGE_POOL_2D = 1,
  BuiltinOperator_CONCATENATION = 2,
  BuiltinOperator_CONV_2D = 3,
  BuiltinOperator_DEPTHWISE_CONV_2D = 4,
  BuiltinOperator_DEPTH_TO_SPACE = 5,
  BuiltinOperator_DEQUANTIZE = 6,
  BuiltinOperator_EMBEDDING_LOOKUP = 7,
  BuiltinOperator_FLOOR = 8,
  BuiltinOperator_FULLY_CONNECTED = 9,
  BuiltinOperator_HASHTABLE_LOOKUP = 10,
  BuiltinOperator_L2_NORMALIZATION = 11,
  BuiltinOperator_L2_POOL_2D = 12,
  BuiltinOperator_LOCAL_RESPONSE_NORMALIZATION = 13,
  BuiltinOperator_LOGISTIC = 14,
  BuiltinOperator_LSH_PROJECTION = 15,
  BuiltinOperator_LSTM = 16,
  BuiltinOperator_MAX_POOL_2D = 17,
  BuiltinOperator_MUL = 18,
  BuiltinOperator_RELU = 19,
  BuiltinOperator_RELU_N1_TO_1 = 20,
  BuiltinOperator_RELU6 = 21,
  BuiltinOperator_RESHAPE = 22,
  BuiltinOperator_RESIZE_BILINEAR = 23,
  BuiltinOperator_RNN = 24,
  BuiltinOperator_SOFTMAX = 25,
  BuiltinOperator_SPACE_TO_DEPTH = 26,
  BuiltinOperator_SVDF = 27,
  BuiltinOperator_TANH = 28,
  BuiltinOperator_CONCAT_EMBEDDINGS = 29,
  BuiltinOperator_SKIP_GRAM = 30,
  BuiltinOperator_CALL = 31,
  BuiltinOperator_CUSTOM = 32,
  BuiltinOperator_EMBEDDING_LOOKUP_SPARSE = 33,
  BuiltinOperator_PAD = 34,
  BuiltinOperator_UNIDIRECTIONAL_SEQUENCE_RNN = 35,
  BuiltinOperator_GATHER = 36,
  BuiltinOperator_BATCH_TO_SPACE_ND = 37,
  BuiltinOperator_SPACE_TO_BATCH_ND = 38,
  BuiltinOperator_TRANSPOSE = 39,
  BuiltinOperator_MEAN = 40,
  BuiltinOperator_SUB = 41,
  BuiltinOperator_DIV = 42,
  BuiltinOperator_SQUEEZE = 43,
  BuiltinOperator_UNIDIRECTIONAL_SEQUENCE_LSTM = 44,
  BuiltinOperator_STRIDED_SLICE = 45,
  BuiltinOperator_BIDIRECTIONAL_SEQUENCE_RNN = 46,
  BuiltinOperator_EXP = 47,
  BuiltinOperator_TOPK_V2 = 48,
  BuiltinOperator_SPLIT = 49,
  BuiltinOperator_LOG_SOFTMAX = 50,
  BuiltinOperator_DELEGATE = 51,
  BuiltinOperator_BIDIRECTIONAL_SEQUENCE_LSTM = 52,
  BuiltinOperator_CAST = 53,
  BuiltinOperator_PRELU = 54,
  BuiltinOperator_MAXIMUM = 55,
  BuiltinOperator_ARG_MAX = 56,
  BuiltinOperator_MINIMUM = 57,
  BuiltinOperator_LESS = 58,
  BuiltinOperator_NEG = 59,
  BuiltinOperator_PADV2 = 60,
  BuiltinOperator_GREATER = 61,
  BuiltinOperator_GREATER_EQUAL = 62,
  BuiltinOperator_LESS_EQUAL = 63,
  BuiltinOperator_SELECT = 64,
  BuiltinOperator_SLICE = 65,
  BuiltinOperator_SIN = 66,
  BuiltinOperator_TRANSPOSE_CONV = 67,
  BuiltinOperator_SPARSE_TO_DENSE = 68,
  BuiltinOperator_TILE = 69,
  BuiltinOperator_EXPAND_DIMS = 70,
  BuiltinOperator_EQUAL = 71,
  BuiltinOperator_NOT_EQUAL = 72,
  BuiltinOperator_LOG = 73,
  BuiltinOperator_SUM = 74,
  BuiltinOperator_SQRT = 75,
  BuiltinOperator_RSQRT = 76,
  BuiltinOperator_SHAPE = 77,
  BuiltinOperator_POW = 78,
  BuiltinOperator_ARG_MIN = 79,
  BuiltinOperator_FAKE_QUANT = 80,
  BuiltinOperator_REDUCE_PROD = 81,
  BuiltinOperator_REDUCE_MAX = 82,
  BuiltinOperator_PACK = 83,
  BuiltinOperator_LOGICAL_OR = 84,
  BuiltinOperator_ONE_HOT = 85,
  BuiltinOperator_LOGICAL_AND = 86,
  BuiltinOperator_LOGICAL_NOT = 87,
  BuiltinOperator_UNPACK = 88,
  BuiltinOperator_REDUCE_MIN = 89,
  BuiltinOperator_FLOOR_DIV = 90,
  BuiltinOperator_REDUCE_ANY = 91,
  BuiltinOperator_SQUARE = 92,
  BuiltinOperator_ZEROS_LIKE = 93,
  BuiltinOperator_FILL = 94,
  BuiltinOperator_FLOOR_MOD = 95,
  BuiltinOperator_RANGE = 96,
  BuiltinOperator_RESIZE_NEAREST_NEIGHBOR = 97,
  BuiltinOperator_LEAKY_RELU = 98,
  BuiltinOperator_SQUARED_DIFFERENCE = 99,
  BuiltinOperator_MIRROR_PAD = 100,
  BuiltinOperator_ABS = 101,
  BuiltinOperator_SPLIT_V = 102,
  BuiltinOperator_UNIQUE = 103,
  BuiltinOperator_CEIL = 104,
  BuiltinOperator_REVERSE_V2 = 105,
  BuiltinOperator_ADD_N = 106,
  BuiltinOperator_GATHER_ND = 107,
  BuiltinOperator_COS = 108,
  BuiltinOperator_WHERE = 109,
  BuiltinOperator_RANK = 110,
  BuiltinOperator_ELU = 111,
  BuiltinOperator_REVERSE_SEQUENCE = 112,
  BuiltinOperator_MATRIX_DIAG = 113,
  BuiltinOperator_QUANTIZE = 114,
  BuiltinOperator_MATRIX_SET_DIAG = 115,
  BuiltinOperator_ROUND = 116,
  BuiltinOperator_HARD_SWISH = 117,
  BuiltinOperator_IF = 118,
  BuiltinOperator_WHILE = 119,
  BuiltinOperator_NON_MAX_SUPPRESSION_V4 = 120,
  BuiltinOperator_NON_MAX_SUPPRESSION_V5 = 121,
  BuiltinOperator_SCATTER_ND = 122,
  BuiltinOperator_SELECT_V2 = 123,
  BuiltinOperator_DENSIFY = 124,
  BuiltinOperator_SEGMENT_SUM = 125,
  BuiltinOperator_BATCH_MATMUL = 126,
  BuiltinOperator_PLACEHOLDER_FOR_GREATER_OP_CODES = 127,
  BuiltinOperator_CUMSUM = 128,
  BuiltinOperator_CALL_ONCE = 129,
  BuiltinOperator_BROADCAST_TO = 130,
  BuiltinOperator_RFFT2D = 131,
  BuiltinOperator_CONV_3D = 132,
  BuiltinOperator_IMAG = 133,
  BuiltinOperator_REAL = 134,
  BuiltinOperator_COMPLEX_ABS = 135,
  BuiltinOperator_HASHTABLE = 136,
  BuiltinOperator_HASHTABLE_FIND = 137,
  BuiltinOperator_HASHTABLE_IMPORT = 138,
  BuiltinOperator_HASHTABLE_SIZE = 139,
  BuiltinOperator_REDUCE_ALL = 140,
  BuiltinOperator_CONV_3D_TRANSPOSE = 141,
  BuiltinOperator_VAR_HANDLE = 142,
  BuiltinOperator_READ_VARIABLE = 143,
  BuiltinOperator_ASSIGN_VARIABLE = 144,
  BuiltinOperator_BROADCAST_ARGS = 145,
  BuiltinOperator_RANDOM_STANDARD_NORMAL = 146,
  BuiltinOperator_BUCKETIZE = 147,
  BuiltinOperator_RANDOM_UNIFORM = 148,
  BuiltinOperator_MULTINOMIAL = 149,
  BuiltinOperator_GELU = 150,
  BuiltinOperator_DYNAMIC_UPDATE_SLICE = 151,
  BuiltinOperator_RELU_0_TO_1 = 152,
  BuiltinOperator_UNSORTED_SEGMENT_PROD = 153,
  BuiltinOperator_UNSORTED_SEGMENT_MAX = 154,
  BuiltinOperator_UNSORTED_SEGMENT_SUM = 155,
  BuiltinOperator_ATAN2 = 156,
  BuiltinOperator_UNSORTED_SEGMENT_MIN = 157,
  BuiltinOperator_SIGN = 158,
  BuiltinOperator_MIN = BuiltinOperator_ADD,
  BuiltinOperator_MAX = BuiltinOperator_SIGN
};

inline const BuiltinOperator (&EnumValuesBuiltinOperator())[159] {
  static const BuiltinOperator values[] = {
    BuiltinOperator_ADD,
    BuiltinOperator_AVERAGE_POOL_2D,
    BuiltinOperator_CONCATENATION,
    BuiltinOperator_CONV_2D,
    BuiltinOperator_DEPTHWISE_CONV_2D,
    BuiltinOperator_DEPTH_TO_SPACE,
    BuiltinOperator_DEQUANTIZE,
    BuiltinOperator_EMBEDDING_LOOKUP,
    BuiltinOperator_FLOOR,
    BuiltinOperator_FULLY_CONNECTED,
    BuiltinOperator_HASHTABLE_LOOKUP,
    BuiltinOperator_L2_NORMALIZATION,
    BuiltinOperator_L2_POOL_2D,
    BuiltinOperator_LOCAL_RESPONSE_NORMALIZATION,
    BuiltinOperator_LOGISTIC,
    BuiltinOperator_LSH_PROJECTION,
    BuiltinOperator_LSTM,
    BuiltinOperator_MAX_POOL_2D,
    BuiltinOperator_MUL,
    BuiltinOperator_RELU,
    BuiltinOperator_RELU_N1_TO_1,
    BuiltinOperator_RELU6,
    BuiltinOperator_RESHAPE,
    BuiltinOperator_RESIZE_BILINEAR,
    BuiltinOperator_RNN,
    BuiltinOperator_SOFTMAX,
    BuiltinOperator_SPACE_TO_DEPTH,
    BuiltinOperator_SVDF,
    BuiltinOperator_TANH,
    BuiltinOperator_CONCAT_EMBEDDINGS,
    BuiltinOperator_SKIP_GRAM,
    BuiltinOperator_CALL,
    BuiltinOperator_CUSTOM,
    BuiltinOperator_EMBEDDING_LOOKUP_SPARSE,
    BuiltinOperator_PAD,
    BuiltinOperator_UNIDIRECTIONAL_SEQUENCE_RNN,
    BuiltinOperator_GATHER,
    BuiltinOperator_BATCH_TO_SPACE_ND,
    BuiltinOperator_SPACE_TO_BATCH_ND,
    BuiltinOperator_TRANSPOSE,
    BuiltinOperator_MEAN,
    BuiltinOperator_SUB,
    BuiltinOperator_DIV,
    BuiltinOperator_SQUEEZE,
    BuiltinOperator_UNIDIRECTIONAL_SEQUENCE_LSTM,
    BuiltinOperator_STRIDED_SLICE,
    BuiltinOperator_BIDIRECTIONAL_SEQUENCE_RNN,
    BuiltinOperator_EXP,
    BuiltinOperator_TOPK_V2,
    BuiltinOperator_SPLIT,
    BuiltinOperator_LOG_SOFTMAX,
    BuiltinOperator_DELEGATE,
    BuiltinOperator_BIDIRECTIONAL_SEQUENCE_LSTM,
    BuiltinOperator_CAST,
    BuiltinOperator_PRELU,
    BuiltinOperator_MAXIMUM,
    BuiltinOperator_ARG_MAX,
    BuiltinOperator_MINIMUM,
    BuiltinOperator_LESS,
    BuiltinOperator_NEG,
    BuiltinOperator_PADV2,
    BuiltinOperator_GREATER,
    BuiltinOperator_GREATER_EQUAL,
    BuiltinOperator_LESS_EQUAL,
    BuiltinOperator_SELECT,
    BuiltinOperator_SLICE,
    BuiltinOperator_SIN,
    BuiltinOperator_TRANSPOSE_CONV,
    BuiltinOperator_SPARSE_TO_DENSE,
    BuiltinOperator_TILE,
    BuiltinOperator_EXPAND_DIMS,
    BuiltinOperator_EQUAL,
    BuiltinOperator_NOT_EQUAL,
    BuiltinOperator_LOG,
    BuiltinOperator_SUM,
    BuiltinOperator_SQRT,
    BuiltinOperator_RSQRT,
    BuiltinOperator_SHAPE,
    BuiltinOperator_POW,
    BuiltinOperator_ARG_MIN,
    BuiltinOperator_FAKE_QUANT,
    BuiltinOperator_REDUCE_PROD,
    BuiltinOperator_REDUCE_MAX,
    BuiltinOperator_PACK,
    BuiltinOperator_LOGICAL_OR,
    BuiltinOperator_ONE_HOT,
    BuiltinOperator_LOGICAL_AND,
    BuiltinOperator_LOGICAL_NOT,
    BuiltinOperator_UNPACK,
    BuiltinOperator_REDUCE_MIN,
    BuiltinOperator_FLOOR_DIV,
    BuiltinOperator_REDUCE_ANY,
    BuiltinOperator_SQUARE,
    BuiltinOperator_ZEROS_LIKE,
    BuiltinOperator_FILL,
    BuiltinOperator_FLOOR_MOD,
    BuiltinOperator_RANGE,
    BuiltinOperator_RESIZE_NEAREST_NEIGHBOR,
    BuiltinOperator_LEAKY_RELU,
    BuiltinOperator_SQUARED_DIFFERENCE,
    BuiltinOperator_MIRROR_PAD,
    BuiltinOperator_ABS,
    BuiltinOperator_SPLIT_V,
    BuiltinOperator_UNIQUE,
    BuiltinOperator_CEIL,
    BuiltinOperator_REVERSE_V2,
    BuiltinOperator_ADD_N,
    BuiltinOperator_GATHER_ND,
    BuiltinOperator_COS,
    BuiltinOperator_WHERE,
    BuiltinOperator_RANK,
    BuiltinOperator_ELU,
    BuiltinOperator_REVERSE_SEQUENCE,
    BuiltinOperator_MATRIX_DIAG,
    BuiltinOperator_QUANTIZE,
    BuiltinOperator_MATRIX_SET_DIAG,
    BuiltinOperator_ROUND,
    BuiltinOperator_HARD_SWISH,
    BuiltinOperator_IF,
    BuiltinOperator_WHILE,
    BuiltinOperator_NON_MAX_SUPPRESSION_V4,
    BuiltinOperator_NON_MAX_SUPPRESSION_V5,
    BuiltinOperator_SCATTER_ND,
    BuiltinOperator_SELECT_V2,
    BuiltinOperator_DENSIFY,
    BuiltinOperator_SEGMENT_SUM,
    BuiltinOperator_BATCH_MATMUL,
    BuiltinOperator_PLACEHOLDER_FOR_GREATER_OP_CODES,
    BuiltinOperator_CUMSUM,
    BuiltinOperator_CALL_ONCE,
    BuiltinOperator_BROADCAST_TO,
    BuiltinOperator_RFFT2D,
    BuiltinOperator_CONV_3D,
    BuiltinOperator_IMAG,
    BuiltinOperator_REAL,
    BuiltinOperator_COMPLEX_ABS,
    BuiltinOperator_HASHTABLE,
    BuiltinOperator_HASHTABLE_FIND,
    BuiltinOperator_HASHTABLE_IMPORT,
    BuiltinOperator_HASHTABLE_SIZE,
    BuiltinOperator_REDUCE_ALL,
    BuiltinOperator_CONV_3D_TRANSPOSE,
    BuiltinOperator_VAR_HANDLE,
    BuiltinOperator_READ_VARIABLE,
    BuiltinOperator_ASSIGN_VARIABLE,
    BuiltinOperator_BROADCAST_ARGS,
    BuiltinOperator_RANDOM_STANDARD_NORMAL,
    BuiltinOperator_BUCKETIZE,
    BuiltinOperator_RANDOM_UNIFORM,
    BuiltinOperator_MULTINOMIAL,
    BuiltinOperator_GELU,
    BuiltinOperator_DYNAMIC_UPDATE_SLICE,
    BuiltinOperator_RELU_0_TO_1,
    BuiltinOperator_UNSORTED_SEGMENT_PROD,
    BuiltinOperator_UNSORTED_SEGMENT_MAX,
    BuiltinOperator_UNSORTED_SEGMENT_SUM,
    BuiltinOperator_ATAN2,
    BuiltinOperator_UNSORTED_SEGMENT_MIN,
    BuiltinOperator_SIGN
  };
  return values;
}

inline const char * const *EnumNamesBuiltinOperator() {
  static const char * const names[160] = {
    "ADD",
    "AVERAGE_POOL_2D",
    "CONCATENATION",
    "CONV_2D",
    "DEPTHWISE_CONV_2D",
    "DEPTH_TO_SPACE",
    "DEQUANTIZE",
    "EMBEDDING_LOOKUP",
    "FLOOR",
    "FULLY_CONNECTED",
    "HASHTABLE_LOOKUP",
    "L2_NORMALIZATION",
    "L2_POOL_2D",
    "LOCAL_RESPONSE_NORMALIZATION",
    "LOGISTIC",
    "LSH_PROJECTION",
    "LSTM",
    "MAX_POOL_2D",
    "MUL",
    "RELU",
    "RELU_N1_TO_1",
    "RELU6",
    "RESHAPE",
    "RESIZE_BILINEAR",
    "RNN",
    "SOFTMAX",
    "SPACE_TO_DEPTH",
    "SVDF",
    "TANH",
    "CONCAT_EMBEDDINGS",
    "SKIP_GRAM",
    "CALL",
    "CUSTOM",
    "EMBEDDING_LOOKUP_SPARSE",
    "PAD",
    "UNIDIRECTIONAL_SEQUENCE_RNN",
    "GATHER",
    "BATCH_TO_SPACE_ND",
    "SPACE_TO_BATCH_ND",
    "TRANSPOSE",
    "MEAN",
    "SUB",
    "DIV",
    "SQUEEZE",
    "UNIDIRECTIONAL_SEQUENCE_LSTM",
    "STRIDED_SLICE",
    "BIDIRECTIONAL_SEQUENCE_RNN",
    "EXP",
    "TOPK_V2",
    "SPLIT",
    "LOG_SOFTMAX",
    "DELEGATE",
    "BIDIRECTIONAL_SEQUENCE_LSTM",
    "CAST",
    "PRELU",
    "MAXIMUM",
    "ARG_MAX",
    "MINIMUM",
    "LESS",
    "NEG",
    "PADV2",
    "GREATER",
    "GREATER_EQUAL",
    "LESS_EQUAL",
    "SELECT",
    "SLICE",
    "SIN",
    "TRANSPOSE_CONV",
    "SPARSE_TO_DENSE",
    "TILE",
    "EXPAND_DIMS",
    "EQUAL",
    "NOT_EQUAL",
    "LOG",
    "SUM",
    "SQRT",
    "RSQRT",
    "SHAPE",
    "POW",
    "ARG_MIN",
    "FAKE_QUANT",
    "REDUCE_PROD",
    "REDUCE_MAX",
    "PACK",
    "LOGICAL_OR",
    "ONE_HOT",
    "LOGICAL_AND",
    "LOGICAL_NOT",
    "UNPACK",
    "REDUCE_MIN",
    "FLOOR_DIV",
    "REDUCE_ANY",
    "SQUARE",
    "ZEROS_LIKE",
    "FILL",
    "FLOOR_MOD",
    "RANGE",
    "RESIZE_NEAREST_NEIGHBOR",
    "LEAKY_RELU",
    "SQUARED_DIFFERENCE",
    "MIRROR_PAD",
    "ABS",
    "SPLIT_V",
    "UNIQUE",
    "CEIL",
    "REVERSE_V2",
    "ADD_N",
    "GATHER_ND",
    "COS",
    "WHERE",
    "RANK",
    "ELU",
    "REVERSE_SEQUENCE",
    "MATRIX_DIAG",
    "QUANTIZE",
    "MATRIX_SET_DIAG",
    "ROUND",
    "HARD_SWISH",
    "IF",
    "WHILE",
    "NON_MAX_SUPPRESSION_V4",
    "NON_MAX_SUPPRESSION_V5",
    "SCATTER_ND",
    "SELECT_V2",
    "DENSIFY",
    "SEGMENT_SUM",
    "BATCH_MATMUL",
    "PLACEHOLDER_FOR_GREATER_OP_CODES",
    "CUMSUM",
    "CALL_ONCE",
    "BROADCAST_TO",
    "RFFT2D",
    "CONV_3D",
    "IMAG",
    "REAL",
    "COMPLEX_ABS",
    "HASHTABLE",
    "HASHTABLE_FIND",
    "HASHTABLE_IMPORT",
    "HASHTABLE_SIZE",
    "REDUCE_ALL",
    "CONV_3D_TRANSPOSE",
    "VAR_HANDLE",
    "READ_VARIABLE",
    "ASSIGN_VARIABLE",
    "BROADCAST_ARGS",
    "RANDOM_STANDARD_NORMAL",
    "BUCKETIZE",
    "RANDOM_UNIFORM",
    "MULTINOMIAL",
    "GELU",
    "DYNAMIC_UPDATE_SLICE",
    "RELU_0_TO_1",
    "UNSORTED_SEGMENT_PROD",
    "UNSORTED_SEGMENT_MAX",
    "UNSORTED_SEGMENT_SUM",
    "ATAN2",
    "UNSORTED_SEGMENT_MIN",
    "SIGN",
    nullptr
  };
  return names;
}

inline const char *EnumNameBuiltinOperator(BuiltinOperator e) {
  if (::flatbuffers::IsOutRange(e, BuiltinOperator_ADD, BuiltinOperator_SIGN)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesBuiltinOperator()[index];
}

enum BuiltinOptions : uint8_t {
  BuiltinOptions_NONE = 0,
  BuiltinOptions_Conv2DOptions = 1,
  BuiltinOptions_DepthwiseConv2DOptions = 2,
  BuiltinOptions_ConcatEmbeddingsOptions = 3,
  BuiltinOptions_LSHProjectionOptions = 4,
  BuiltinOptions_Pool2DOptions = 5,
  BuiltinOptions_SVDFOptions = 6,
  BuiltinOptions_RNNOptions = 7,
  BuiltinOptions_FullyConnectedOptions = 8,
  BuiltinOptions_SoftmaxOptions = 9,
  BuiltinOptions_ConcatenationOptions = 10,
  BuiltinOptions_AddOptions = 11,
  BuiltinOptions_L2NormOptions = 12,
  BuiltinOptions_LocalResponseNormalizationOptions = 13,
  BuiltinOptions_LSTMOptions = 14,
  BuiltinOptions_ResizeBilinearOptions = 15,
  BuiltinOptions_CallOptions = 16,
  BuiltinOptions_ReshapeOptions = 17,
  BuiltinOptions_SkipGramOptions = 18,
  BuiltinOptions_SpaceToDepthOptions = 19,
  BuiltinOptions_EmbeddingLookupSparseOptions = 20,
  BuiltinOptions_MulOptions = 21,
  BuiltinOptions_PadOptions = 22,
  BuiltinOptions_GatherOptions = 23,
  BuiltinOptions_BatchToSpaceNDOptions = 24,
  BuiltinOptions_SpaceToBatchNDOptions = 25,
  BuiltinOptions_TransposeOptions = 26,
  BuiltinOptions_ReducerOptions = 27,
  BuiltinOptions_SubOptions = 28,
  BuiltinOptions_DivOptions = 29,
  BuiltinOptions_SqueezeOptions = 30,
  BuiltinOptions_SequenceRNNOptions = 31,
  BuiltinOptions_StridedSliceOptions = 32,
  BuiltinOptions_ExpOptions = 33,
  BuiltinOptions_TopKV2Options = 34,
  BuiltinOptions_SplitOptions = 35,
  BuiltinOptions_LogSoftmaxOptions = 36,
  BuiltinOptions_CastOptions = 37,
  BuiltinOptions_DequantizeOptions = 38,
  BuiltinOptions_MaximumMinimumOptions = 39,
  BuiltinOptions_ArgMaxOptions = 40,
  BuiltinOptions_LessOptions = 41,
  BuiltinOptions_NegOptions = 42,
  BuiltinOptions_PadV2Options = 43,
  BuiltinOptions_GreaterOptions = 44,
  BuiltinOptions_GreaterEqualOptions = 45,
  BuiltinOptions_LessEqualOptions = 46,
  BuiltinOptions_SelectOptions = 47,
  BuiltinOptions_SliceOptions = 48,
  BuiltinOptions_TransposeConvOptions = 49,
  BuiltinOptions_SparseToDenseOptions = 50,
  BuiltinOptions_TileOptions = 51,
  BuiltinOptions_ExpandDimsOptions = 52,
  BuiltinOptions_EqualOptions = 53,
  BuiltinOptions_NotEqualOptions = 54,
  BuiltinOptions_ShapeOptions = 55,
  BuiltinOptions_PowOptions = 56,
  BuiltinOptions_ArgMinOptions = 57,
  BuiltinOptions_FakeQuantOptions = 58,
  BuiltinOptions_PackOptions = 59,
  BuiltinOptions_LogicalOrOptions = 60,
  BuiltinOptions_OneHotOptions = 61,
  BuiltinOptions_LogicalAndOptions = 62,
  BuiltinOptions_LogicalNotOptions = 63,
  BuiltinOptions_UnpackOptions = 64,
  BuiltinOptions_FloorDivOptions = 65,
  BuiltinOptions_SquareOptions = 66,
  BuiltinOptions_ZerosLikeOptions = 67,
  BuiltinOptions_FillOptions = 68,
  BuiltinOptions_BidirectionalSequenceLSTMOptions = 69,
  BuiltinOptions_BidirectionalSequenceRNNOptions = 70,
  BuiltinOptions_UnidirectionalSequenceLSTMOptions = 71,
  BuiltinOptions_FloorModOptions = 72,
  BuiltinOptions_RangeOptions = 73,
  BuiltinOptions_ResizeNearestNeighborOptions = 74,
  BuiltinOptions_LeakyReluOptions = 75,
  BuiltinOptions_SquaredDifferenceOptions = 76,
  BuiltinOptions_MirrorPadOptions = 77,
  BuiltinOptions_AbsOptions = 78,
  BuiltinOptions_SplitVOptions = 79,
  BuiltinOptions_UniqueOptions = 80,
  BuiltinOptions_ReverseV2Options = 81,
  BuiltinOptions_AddNOptions = 82,
  BuiltinOptions_GatherNdOptions = 83,
  BuiltinOptions_CosOptions = 84,
  BuiltinOptions_WhereOptions = 85,
  BuiltinOptions_RankOptions = 86,
  BuiltinOptions_ReverseSequenceOptions = 87,
  BuiltinOptions_MatrixDiagOptions = 88,
  BuiltinOptions_QuantizeOptions = 89,
  BuiltinOptions_MatrixSetDiagOptions = 90,
  BuiltinOptions_HardSwishOptions = 91,
  BuiltinOptions_IfOptions = 92,
  BuiltinOptions_WhileOptions = 93,
  BuiltinOptions_DepthToSpaceOptions = 94,
  BuiltinOptions_NonMaxSuppressionV4Options = 95,
  BuiltinOptions_NonMaxSuppressionV5Options = 96,
  BuiltinOptions_ScatterNdOptions = 97,
  BuiltinOptions_SelectV2Options = 98,
  BuiltinOptions_DensifyOptions = 99,
  BuiltinOptions_SegmentSumOptions = 100,
  BuiltinOptions_BatchMatMulOptions = 101,
  BuiltinOptions_CumsumOptions = 102,
  BuiltinOptions_CallOnceOptions = 103,
  BuiltinOptions_BroadcastToOptions = 104,
  BuiltinOptions_Rfft2dOptions = 105,
  BuiltinOptions_Conv3DOptions = 106,
  BuiltinOptions_HashtableOptions = 107,
  BuiltinOptions_HashtableFindOptions = 108,
  BuiltinOptions_HashtableImportOptions = 109,
  BuiltinOptions_HashtableSizeOptions = 110,
  BuiltinOptions_VarHandleOptions = 111,
  BuiltinOptions_ReadVariableOptions = 112,
  BuiltinOptions_AssignVariableOptions = 113,
  BuiltinOptions_RandomOptions = 114,
  BuiltinOptions_BucketizeOptions = 115,
  BuiltinOptions_GeluOptions = 116,
  BuiltinOptions_DynamicUpdateSliceOptions = 117,
  BuiltinOptions_UnsortedSegmentProdOptions = 118,
  BuiltinOptions_UnsortedSegmentMaxOptions = 119,
  BuiltinOptions_UnsortedSegmentMinOptions = 120,
  BuiltinOptions_UnsortedSegmentSumOptions = 121,
  BuiltinOptions_ATan2Options = 122,
  BuiltinOptions_SignOptions = 123,
  BuiltinOptions_MIN = BuiltinOptions_NONE,
  BuiltinOptions_MAX = BuiltinOptions_SignOptions
};

inline const BuiltinOptions (&EnumValuesBuiltinOptions())[124] {
  static const BuiltinOptions values[] = {
    BuiltinOptions_NONE,
    BuiltinOptions_Conv2DOptions,
    BuiltinOptions_DepthwiseConv2DOptions,
    BuiltinOptions_ConcatEmbeddingsOptions,
    BuiltinOptions_LSHProjectionOptions,
    BuiltinOptions_Pool2DOptions,
    BuiltinOptions_SVDFOptions,
    BuiltinOptions_RNNOptions,
    BuiltinOptions_FullyConnectedOptions,
    BuiltinOptions_SoftmaxOptions,
    BuiltinOptions_ConcatenationOptions,
    BuiltinOptions_AddOptions,
    BuiltinOptions_L2NormOptions,
    BuiltinOptions_LocalResponseNormalizationOptions,
    BuiltinOptions_LSTMOptions,
    BuiltinOptions_ResizeBilinearOptions,
    BuiltinOptions_CallOptions,
    BuiltinOptions_ReshapeOptions,
    BuiltinOptions_SkipGramOptions,
    BuiltinOptions_SpaceToDepthOptions,
    BuiltinOptions_EmbeddingLookupSparseOptions,
    BuiltinOptions_MulOptions,
    BuiltinOptions_PadOptions,
    BuiltinOptions_GatherOptions,
    BuiltinOptions_BatchToSpaceNDOptions,
    BuiltinOptions_SpaceToBatchNDOptions,
    BuiltinOptions_TransposeOptions,
    BuiltinOptions_ReducerOptions,
    BuiltinOptions_SubOptions,
    BuiltinOptions_DivOptions,
    BuiltinOptions_SqueezeOptions,
    BuiltinOptions_SequenceRNNOptions,
    BuiltinOptions_StridedSliceOptions,
    BuiltinOptions_ExpOptions,
    BuiltinOptions_TopKV2Options,
    BuiltinOptions_SplitOptions,
    BuiltinOptions_LogSoftmaxOptions,
    BuiltinOptions_CastOptions,
    BuiltinOptions_DequantizeOptions,
    BuiltinOptions_MaximumMinimumOptions,
    BuiltinOptions_ArgMaxOptions,
    BuiltinOptions_LessOptions,
    BuiltinOptions_NegOptions,
    BuiltinOptions_PadV2Options,
    BuiltinOptions_GreaterOptions,
    BuiltinOptions_GreaterEqualOptions,
    BuiltinOptions_LessEqualOptions,
    BuiltinOptions_SelectOptions,
    BuiltinOptions_SliceOptions,
    BuiltinOptions_TransposeConvOptions,
    BuiltinOptions_SparseToDenseOptions,
    BuiltinOptions_TileOptions,
    BuiltinOptions_ExpandDimsOptions,
    BuiltinOptions_EqualOptions,
    BuiltinOptions_NotEqualOptions,
    BuiltinOptions_ShapeOptions,
    BuiltinOptions_PowOptions,
    BuiltinOptions_ArgMinOptions,
    BuiltinOptions_FakeQuantOptions,
    BuiltinOptions_PackOptions,
    BuiltinOptions_LogicalOrOptions,
    BuiltinOptions_OneHotOptions,
    BuiltinOptions_LogicalAndOptions,
    BuiltinOptions_LogicalNotOptions,
    BuiltinOptions_UnpackOptions,
    BuiltinOptions_FloorDivOptions,
    BuiltinOptions_SquareOptions,
    BuiltinOptions_ZerosLikeOptions,
    BuiltinOptions_FillOptions,
    BuiltinOptions_BidirectionalSequenceLSTMOptions,
    BuiltinOptions_BidirectionalSequenceRNNOptions,
    BuiltinOptions_UnidirectionalSequenceLSTMOptions,
    BuiltinOptions_FloorModOptions,
    BuiltinOptions_RangeOptions,
    BuiltinOptions_ResizeNearestNeighborOptions,
    BuiltinOptions_LeakyReluOptions,
    BuiltinOptions_SquaredDifferenceOptions,
    BuiltinOptions_MirrorPadOptions,
    BuiltinOptions_AbsOptions,
    BuiltinOptions_SplitVOptions,
    BuiltinOptions_UniqueOptions,
    BuiltinOptions_ReverseV2Options,
    BuiltinOptions_AddNOptions,
    BuiltinOptions_GatherNdOptions,
    BuiltinOptions_CosOptions,
    BuiltinOptions_WhereOptions,
    BuiltinOptions_RankOptions,
    BuiltinOptions_ReverseSequenceOptions,
    BuiltinOptions_MatrixDiagOptions,
    BuiltinOptions_QuantizeOptions,
    BuiltinOptions_MatrixSetDiagOptions,
    BuiltinOptions_HardSwishOptions,
    BuiltinOptions_IfOptions,
    BuiltinOptions_WhileOptions,
    BuiltinOptions_DepthToSpaceOptions,
    BuiltinOptions_NonMaxSuppressionV4Options,
    BuiltinOptions_NonMaxSuppressionV5Options,
    BuiltinOptions_ScatterNdOptions,
    BuiltinOptions_SelectV2Options,
    BuiltinOptions_DensifyOptions,
    BuiltinOptions_SegmentSumOptions,
    BuiltinOptions_BatchMatMulOptions,
    BuiltinOptions_CumsumOptions,
    BuiltinOptions_CallOnceOptions,
    BuiltinOptions_BroadcastToOptions,
    BuiltinOptions_Rfft2dOptions,
    BuiltinOptions_Conv3DOptions,
    BuiltinOptions_HashtableOptions,
    BuiltinOptions_HashtableFindOptions,
    BuiltinOptions_HashtableImportOptions,
    BuiltinOptions_HashtableSizeOptions,
    BuiltinOptions_VarHandleOptions,
    BuiltinOptions_ReadVariableOptions,
    BuiltinOptions_AssignVariableOptions,
    BuiltinOptions_RandomOptions,
    BuiltinOptions_BucketizeOptions,
    BuiltinOptions_GeluOptions,
    BuiltinOptions_DynamicUpdateSliceOptions,
    BuiltinOptions_UnsortedSegmentProdOptions,
    BuiltinOptions_UnsortedSegmentMaxOptions,
    BuiltinOptions_UnsortedSegmentMinOptions,
    BuiltinOptions_UnsortedSegmentSumOptions,
    BuiltinOptions_ATan2Options,
    BuiltinOptions_SignOptions
  };
  return values;
}

inline const char * const *EnumNamesBuiltinOptions() {
  static const char * const names[125] = {
    "NONE",
    "Conv2DOptions",
    "DepthwiseConv2DOptions",
    "ConcatEmbeddingsOptions",
    "LSHProjectionOptions",
    "Pool2DOptions",
    "SVDFOptions",
    "RNNOptions",
    "FullyConnectedOptions",
    "SoftmaxOptions",
    "ConcatenationOptions",
    "AddOptions",
    "L2NormOptions",
    "LocalResponseNormalizationOptions",
    "LSTMOptions",
    "ResizeBilinearOptions",
    "CallOptions",
    "ReshapeOptions",
    "SkipGramOptions",
    "SpaceToDepthOptions",
    "EmbeddingLookupSparseOptions",
    "MulOptions",
    "PadOptions",
    "GatherOptions",
    "BatchToSpaceNDOptions",
    "SpaceToBatchNDOptions",
    "TransposeOptions",
    "ReducerOptions",
    "SubOptions",
    "DivOptions",
    "SqueezeOptions",
    "SequenceRNNOptions",
    "StridedSliceOptions",
    "ExpOptions",
    "TopKV2Options",
    "SplitOptions",
    "LogSoftmaxOptions",
    "CastOptions",
    "DequantizeOptions",
    "MaximumMinimumOptions",
    "ArgMaxOptions",
    "LessOptions",
    "NegOptions",
    "PadV2Options",
    "GreaterOptions",
    "GreaterEqualOptions",
    "LessEqualOptions",
    "SelectOptions",
    "SliceOptions",
    "TransposeConvOptions",
    "SparseToDenseOptions",
    "TileOptions",
    "ExpandDimsOptions",
    "EqualOptions",
    "NotEqualOptions",
    "ShapeOptions",
    "PowOptions",
    "ArgMinOptions",
    "FakeQuantOptions",
    "PackOptions",
    "LogicalOrOptions",
    "OneHotOptions",
    "LogicalAndOptions",
    "LogicalNotOptions",
    "UnpackOptions",
    "FloorDivOptions",
    "SquareOptions",
    "ZerosLikeOptions",
    "FillOptions",
    "BidirectionalSequenceLSTMOptions",
    "BidirectionalSequenceRNNOptions",
    "UnidirectionalSequenceLSTMOptions",
    "FloorModOptions",
    "RangeOptions",
    "ResizeNearestNeighborOptions",
    "LeakyReluOptions",
    "SquaredDifferenceOptions",
    "MirrorPadOptions",
    "AbsOptions",
    "SplitVOptions",
    "UniqueOptions",
    "ReverseV2Options",
    "AddNOptions",
    "GatherNdOptions",
    "CosOptions",
    "WhereOptions",
    "RankOptions",
    "ReverseSequenceOptions",
    "MatrixDiagOptions",
    "QuantizeOptions",
    "MatrixSetDiagOptions",
    "HardSwishOptions",
    "IfOptions",
    "WhileOptions",
    "DepthToSpaceOptions",
    "NonMaxSuppressionV4Options",
    "NonMaxSuppressionV5Options",
    "ScatterNdOptions",
    "SelectV2Options",
    "DensifyOptions",
    "SegmentSumOptions",
    "BatchMatMulOptions",
    "CumsumOptions",
    "CallOnceOptions",
    "BroadcastToOptions",
    "Rfft2dOptions",
    "Conv3DOptions",
    "HashtableOptions",
    "HashtableFindOptions",
    "HashtableImportOptions",
    "HashtableSizeOptions",
    "VarHandleOptions",
    "ReadVariableOptions",
    "AssignVariableOptions",
    "RandomOptions",
    "BucketizeOptions",
    "GeluOptions",
    "DynamicUpdateSliceOptions",
    "UnsortedSegmentProdOptions",
    "UnsortedSegmentMaxOptions",
    "UnsortedSegmentMinOptions",
    "UnsortedSegmentSumOptions",
    "ATan2Options",
    "SignOptions",
    nullptr
  };
  return names;
}

inline const char *EnumNameBuiltinOptions(BuiltinOptions e) {
  if (::flatbuffers::IsOutRange(e, BuiltinOptions_NONE, BuiltinOptions_SignOptions)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesBuiltinOptions()[index];
}

template<typename T> struct BuiltinOptionsTraits {
  static const BuiltinOptions enum_value = BuiltinOptions_NONE;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::Conv2DOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_Conv2DOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::DepthwiseConv2DOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_DepthwiseConv2DOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ConcatEmbeddingsOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_ConcatEmbeddingsOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::LSHProjectionOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_LSHProjectionOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::Pool2DOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_Pool2DOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SVDFOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SVDFOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::RNNOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_RNNOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::FullyConnectedOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_FullyConnectedOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SoftmaxOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SoftmaxOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ConcatenationOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_ConcatenationOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::AddOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_AddOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::L2NormOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_L2NormOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::LocalResponseNormalizationOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_LocalResponseNormalizationOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::LSTMOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_LSTMOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ResizeBilinearOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_ResizeBilinearOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::CallOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_CallOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ReshapeOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_ReshapeOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SkipGramOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SkipGramOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SpaceToDepthOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SpaceToDepthOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::EmbeddingLookupSparseOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_EmbeddingLookupSparseOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::MulOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_MulOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::PadOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_PadOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::GatherOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_GatherOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::BatchToSpaceNDOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_BatchToSpaceNDOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SpaceToBatchNDOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SpaceToBatchNDOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::TransposeOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_TransposeOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ReducerOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_ReducerOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SubOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SubOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::DivOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_DivOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SqueezeOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SqueezeOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SequenceRNNOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SequenceRNNOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::StridedSliceOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_StridedSliceOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ExpOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_ExpOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::TopKV2Options> {
  static const BuiltinOptions enum_value = BuiltinOptions_TopKV2Options;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SplitOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SplitOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::LogSoftmaxOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_LogSoftmaxOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::CastOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_CastOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::DequantizeOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_DequantizeOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::MaximumMinimumOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_MaximumMinimumOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ArgMaxOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_ArgMaxOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::LessOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_LessOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::NegOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_NegOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::PadV2Options> {
  static const BuiltinOptions enum_value = BuiltinOptions_PadV2Options;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::GreaterOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_GreaterOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::GreaterEqualOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_GreaterEqualOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::LessEqualOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_LessEqualOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SelectOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SelectOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SliceOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SliceOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::TransposeConvOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_TransposeConvOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SparseToDenseOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SparseToDenseOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::TileOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_TileOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ExpandDimsOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_ExpandDimsOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::EqualOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_EqualOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::NotEqualOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_NotEqualOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ShapeOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_ShapeOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::PowOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_PowOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ArgMinOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_ArgMinOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::FakeQuantOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_FakeQuantOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::PackOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_PackOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::LogicalOrOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_LogicalOrOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::OneHotOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_OneHotOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::LogicalAndOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_LogicalAndOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::LogicalNotOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_LogicalNotOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::UnpackOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_UnpackOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::FloorDivOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_FloorDivOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SquareOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SquareOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ZerosLikeOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_ZerosLikeOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::FillOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_FillOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::BidirectionalSequenceLSTMOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_BidirectionalSequenceLSTMOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::BidirectionalSequenceRNNOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_BidirectionalSequenceRNNOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::UnidirectionalSequenceLSTMOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_UnidirectionalSequenceLSTMOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::FloorModOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_FloorModOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::RangeOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_RangeOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ResizeNearestNeighborOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_ResizeNearestNeighborOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::LeakyReluOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_LeakyReluOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SquaredDifferenceOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SquaredDifferenceOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::MirrorPadOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_MirrorPadOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::AbsOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_AbsOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SplitVOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SplitVOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::UniqueOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_UniqueOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ReverseV2Options> {
  static const BuiltinOptions enum_value = BuiltinOptions_ReverseV2Options;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::AddNOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_AddNOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::GatherNdOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_GatherNdOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::CosOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_CosOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::WhereOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_WhereOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::RankOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_RankOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ReverseSequenceOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_ReverseSequenceOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::MatrixDiagOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_MatrixDiagOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::QuantizeOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_QuantizeOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::MatrixSetDiagOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_MatrixSetDiagOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::HardSwishOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_HardSwishOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::IfOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_IfOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::WhileOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_WhileOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::DepthToSpaceOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_DepthToSpaceOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::NonMaxSuppressionV4Options> {
  static const BuiltinOptions enum_value = BuiltinOptions_NonMaxSuppressionV4Options;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::NonMaxSuppressionV5Options> {
  static const BuiltinOptions enum_value = BuiltinOptions_NonMaxSuppressionV5Options;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ScatterNdOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_ScatterNdOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SelectV2Options> {
  static const BuiltinOptions enum_value = BuiltinOptions_SelectV2Options;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::DensifyOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_DensifyOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SegmentSumOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SegmentSumOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::BatchMatMulOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_BatchMatMulOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::CumsumOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_CumsumOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::CallOnceOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_CallOnceOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::BroadcastToOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_BroadcastToOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::Rfft2dOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_Rfft2dOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::Conv3DOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_Conv3DOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::HashtableOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_HashtableOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::HashtableFindOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_HashtableFindOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::HashtableImportOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_HashtableImportOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::HashtableSizeOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_HashtableSizeOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::VarHandleOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_VarHandleOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ReadVariableOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_ReadVariableOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::AssignVariableOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_AssignVariableOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::RandomOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_RandomOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::BucketizeOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_BucketizeOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::GeluOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_GeluOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::DynamicUpdateSliceOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_DynamicUpdateSliceOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::UnsortedSegmentProdOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_UnsortedSegmentProdOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::UnsortedSegmentMaxOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_UnsortedSegmentMaxOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::UnsortedSegmentMinOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_UnsortedSegmentMinOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::UnsortedSegmentSumOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_UnsortedSegmentSumOptions;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::ATan2Options> {
  static const BuiltinOptions enum_value = BuiltinOptions_ATan2Options;
};

template<> struct BuiltinOptionsTraits<opencv_tflite::SignOptions> {
  static const BuiltinOptions enum_value = BuiltinOptions_SignOptions;
};

bool VerifyBuiltinOptions(::flatbuffers::Verifier &verifier, const void *obj, BuiltinOptions type);
bool VerifyBuiltinOptionsVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

enum Padding : int8_t {
  Padding_SAME = 0,
  Padding_VALID = 1,
  Padding_MIN = Padding_SAME,
  Padding_MAX = Padding_VALID
};

inline const Padding (&EnumValuesPadding())[2] {
  static const Padding values[] = {
    Padding_SAME,
    Padding_VALID
  };
  return values;
}

inline const char * const *EnumNamesPadding() {
  static const char * const names[3] = {
    "SAME",
    "VALID",
    nullptr
  };
  return names;
}

inline const char *EnumNamePadding(Padding e) {
  if (::flatbuffers::IsOutRange(e, Padding_SAME, Padding_VALID)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesPadding()[index];
}

enum ActivationFunctionType : int8_t {
  ActivationFunctionType_NONE = 0,
  ActivationFunctionType_RELU = 1,
  ActivationFunctionType_RELU_N1_TO_1 = 2,
  ActivationFunctionType_RELU6 = 3,
  ActivationFunctionType_TANH = 4,
  ActivationFunctionType_SIGN_BIT = 5,
  ActivationFunctionType_MIN = ActivationFunctionType_NONE,
  ActivationFunctionType_MAX = ActivationFunctionType_SIGN_BIT
};

inline const ActivationFunctionType (&EnumValuesActivationFunctionType())[6] {
  static const ActivationFunctionType values[] = {
    ActivationFunctionType_NONE,
    ActivationFunctionType_RELU,
    ActivationFunctionType_RELU_N1_TO_1,
    ActivationFunctionType_RELU6,
    ActivationFunctionType_TANH,
    ActivationFunctionType_SIGN_BIT
  };
  return values;
}

inline const char * const *EnumNamesActivationFunctionType() {
  static const char * const names[7] = {
    "NONE",
    "RELU",
    "RELU_N1_TO_1",
    "RELU6",
    "TANH",
    "SIGN_BIT",
    nullptr
  };
  return names;
}

inline const char *EnumNameActivationFunctionType(ActivationFunctionType e) {
  if (::flatbuffers::IsOutRange(e, ActivationFunctionType_NONE, ActivationFunctionType_SIGN_BIT)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesActivationFunctionType()[index];
}

enum LSHProjectionType : int8_t {
  LSHProjectionType_UNKNOWN = 0,
  LSHProjectionType_SPARSE = 1,
  LSHProjectionType_DENSE = 2,
  LSHProjectionType_MIN = LSHProjectionType_UNKNOWN,
  LSHProjectionType_MAX = LSHProjectionType_DENSE
};

inline const LSHProjectionType (&EnumValuesLSHProjectionType())[3] {
  static const LSHProjectionType values[] = {
    LSHProjectionType_UNKNOWN,
    LSHProjectionType_SPARSE,
    LSHProjectionType_DENSE
  };
  return values;
}

inline const char * const *EnumNamesLSHProjectionType() {
  static const char * const names[4] = {
    "UNKNOWN",
    "SPARSE",
    "DENSE",
    nullptr
  };
  return names;
}

inline const char *EnumNameLSHProjectionType(LSHProjectionType e) {
  if (::flatbuffers::IsOutRange(e, LSHProjectionType_UNKNOWN, LSHProjectionType_DENSE)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesLSHProjectionType()[index];
}

enum FullyConnectedOptionsWeightsFormat : int8_t {
  FullyConnectedOptionsWeightsFormat_DEFAULT = 0,
  FullyConnectedOptionsWeightsFormat_SHUFFLED4x16INT8 = 1,
  FullyConnectedOptionsWeightsFormat_MIN = FullyConnectedOptionsWeightsFormat_DEFAULT,
  FullyConnectedOptionsWeightsFormat_MAX = FullyConnectedOptionsWeightsFormat_SHUFFLED4x16INT8
};

inline const FullyConnectedOptionsWeightsFormat (&EnumValuesFullyConnectedOptionsWeightsFormat())[2] {
  static const FullyConnectedOptionsWeightsFormat values[] = {
    FullyConnectedOptionsWeightsFormat_DEFAULT,
    FullyConnectedOptionsWeightsFormat_SHUFFLED4x16INT8
  };
  return values;
}

inline const char * const *EnumNamesFullyConnectedOptionsWeightsFormat() {
  static const char * const names[3] = {
    "DEFAULT",
    "SHUFFLED4x16INT8",
    nullptr
  };
  return names;
}

inline const char *EnumNameFullyConnectedOptionsWeightsFormat(FullyConnectedOptionsWeightsFormat e) {
  if (::flatbuffers::IsOutRange(e, FullyConnectedOptionsWeightsFormat_DEFAULT, FullyConnectedOptionsWeightsFormat_SHUFFLED4x16INT8)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesFullyConnectedOptionsWeightsFormat()[index];
}

enum LSTMKernelType : int8_t {
  LSTMKernelType_FULL = 0,
  LSTMKernelType_BASIC = 1,
  LSTMKernelType_MIN = LSTMKernelType_FULL,
  LSTMKernelType_MAX = LSTMKernelType_BASIC
};

inline const LSTMKernelType (&EnumValuesLSTMKernelType())[2] {
  static const LSTMKernelType values[] = {
    LSTMKernelType_FULL,
    LSTMKernelType_BASIC
  };
  return values;
}

inline const char * const *EnumNamesLSTMKernelType() {
  static const char * const names[3] = {
    "FULL",
    "BASIC",
    nullptr
  };
  return names;
}

inline const char *EnumNameLSTMKernelType(LSTMKernelType e) {
  if (::flatbuffers::IsOutRange(e, LSTMKernelType_FULL, LSTMKernelType_BASIC)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesLSTMKernelType()[index];
}

enum CombinerType : int8_t {
  CombinerType_SUM = 0,
  CombinerType_MEAN = 1,
  CombinerType_SQRTN = 2,
  CombinerType_MIN = CombinerType_SUM,
  CombinerType_MAX = CombinerType_SQRTN
};

inline const CombinerType (&EnumValuesCombinerType())[3] {
  static const CombinerType values[] = {
    CombinerType_SUM,
    CombinerType_MEAN,
    CombinerType_SQRTN
  };
  return values;
}

inline const char * const *EnumNamesCombinerType() {
  static const char * const names[4] = {
    "SUM",
    "MEAN",
    "SQRTN",
    nullptr
  };
  return names;
}

inline const char *EnumNameCombinerType(CombinerType e) {
  if (::flatbuffers::IsOutRange(e, CombinerType_SUM, CombinerType_SQRTN)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesCombinerType()[index];
}

enum MirrorPadMode : int8_t {
  MirrorPadMode_REFLECT = 0,
  MirrorPadMode_SYMMETRIC = 1,
  MirrorPadMode_MIN = MirrorPadMode_REFLECT,
  MirrorPadMode_MAX = MirrorPadMode_SYMMETRIC
};

inline const MirrorPadMode (&EnumValuesMirrorPadMode())[2] {
  static const MirrorPadMode values[] = {
    MirrorPadMode_REFLECT,
    MirrorPadMode_SYMMETRIC
  };
  return values;
}

inline const char * const *EnumNamesMirrorPadMode() {
  static const char * const names[3] = {
    "REFLECT",
    "SYMMETRIC",
    nullptr
  };
  return names;
}

inline const char *EnumNameMirrorPadMode(MirrorPadMode e) {
  if (::flatbuffers::IsOutRange(e, MirrorPadMode_REFLECT, MirrorPadMode_SYMMETRIC)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesMirrorPadMode()[index];
}

enum CustomOptionsFormat : int8_t {
  CustomOptionsFormat_FLEXBUFFERS = 0,
  CustomOptionsFormat_MIN = CustomOptionsFormat_FLEXBUFFERS,
  CustomOptionsFormat_MAX = CustomOptionsFormat_FLEXBUFFERS
};

inline const CustomOptionsFormat (&EnumValuesCustomOptionsFormat())[1] {
  static const CustomOptionsFormat values[] = {
    CustomOptionsFormat_FLEXBUFFERS
  };
  return values;
}

inline const char * const *EnumNamesCustomOptionsFormat() {
  static const char * const names[2] = {
    "FLEXBUFFERS",
    nullptr
  };
  return names;
}

inline const char *EnumNameCustomOptionsFormat(CustomOptionsFormat e) {
  if (::flatbuffers::IsOutRange(e, CustomOptionsFormat_FLEXBUFFERS, CustomOptionsFormat_FLEXBUFFERS)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesCustomOptionsFormat()[index];
}

struct CustomQuantization FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef CustomQuantizationBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CUSTOM = 4
  };
  const ::flatbuffers::Vector<uint8_t> *custom() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_CUSTOM);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_CUSTOM) &&
           verifier.VerifyVector(custom()) &&
           verifier.EndTable();
  }
};

struct CustomQuantizationBuilder {
  typedef CustomQuantization Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_custom(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> custom) {
    fbb_.AddOffset(CustomQuantization::VT_CUSTOM, custom);
  }
  explicit CustomQuantizationBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<CustomQuantization> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<CustomQuantization>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<CustomQuantization> CreateCustomQuantization(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> custom = 0) {
  CustomQuantizationBuilder builder_(_fbb);
  builder_.add_custom(custom);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<CustomQuantization> CreateCustomQuantizationDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *custom = nullptr) {
  if (custom) { _fbb.ForceVectorAlignment(custom->size(), sizeof(uint8_t), 16); }
  auto custom__ = custom ? _fbb.CreateVector<uint8_t>(*custom) : 0;
  return opencv_tflite::CreateCustomQuantization(
      _fbb,
      custom__);
}

struct QuantizationParameters FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef QuantizationParametersBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MIN = 4,
    VT_MAX = 6,
    VT_SCALE = 8,
    VT_ZERO_POINT = 10,
    VT_DETAILS_TYPE = 12,
    VT_DETAILS = 14,
    VT_QUANTIZED_DIMENSION = 16
  };
  const ::flatbuffers::Vector<float> *min() const {
    return GetPointer<const ::flatbuffers::Vector<float> *>(VT_MIN);
  }
  const ::flatbuffers::Vector<float> *max() const {
    return GetPointer<const ::flatbuffers::Vector<float> *>(VT_MAX);
  }
  const ::flatbuffers::Vector<float> *scale() const {
    return GetPointer<const ::flatbuffers::Vector<float> *>(VT_SCALE);
  }
  const ::flatbuffers::Vector<int64_t> *zero_point() const {
    return GetPointer<const ::flatbuffers::Vector<int64_t> *>(VT_ZERO_POINT);
  }
  opencv_tflite::QuantizationDetails details_type() const {
    return static_cast<opencv_tflite::QuantizationDetails>(GetField<uint8_t>(VT_DETAILS_TYPE, 0));
  }
  const void *details() const {
    return GetPointer<const void *>(VT_DETAILS);
  }
  template<typename T> const T *details_as() const;
  const opencv_tflite::CustomQuantization *details_as_CustomQuantization() const {
    return details_type() == opencv_tflite::QuantizationDetails_CustomQuantization ? static_cast<const opencv_tflite::CustomQuantization *>(details()) : nullptr;
  }
  int32_t quantized_dimension() const {
    return GetField<int32_t>(VT_QUANTIZED_DIMENSION, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MIN) &&
           verifier.VerifyVector(min()) &&
           VerifyOffset(verifier, VT_MAX) &&
           verifier.VerifyVector(max()) &&
           VerifyOffset(verifier, VT_SCALE) &&
           verifier.VerifyVector(scale()) &&
           VerifyOffset(verifier, VT_ZERO_POINT) &&
           verifier.VerifyVector(zero_point()) &&
           VerifyField<uint8_t>(verifier, VT_DETAILS_TYPE, 1) &&
           VerifyOffset(verifier, VT_DETAILS) &&
           VerifyQuantizationDetails(verifier, details(), details_type()) &&
           VerifyField<int32_t>(verifier, VT_QUANTIZED_DIMENSION, 4) &&
           verifier.EndTable();
  }
};

template<> inline const opencv_tflite::CustomQuantization *QuantizationParameters::details_as<opencv_tflite::CustomQuantization>() const {
  return details_as_CustomQuantization();
}

struct QuantizationParametersBuilder {
  typedef QuantizationParameters Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_min(::flatbuffers::Offset<::flatbuffers::Vector<float>> min) {
    fbb_.AddOffset(QuantizationParameters::VT_MIN, min);
  }
  void add_max(::flatbuffers::Offset<::flatbuffers::Vector<float>> max) {
    fbb_.AddOffset(QuantizationParameters::VT_MAX, max);
  }
  void add_scale(::flatbuffers::Offset<::flatbuffers::Vector<float>> scale) {
    fbb_.AddOffset(QuantizationParameters::VT_SCALE, scale);
  }
  void add_zero_point(::flatbuffers::Offset<::flatbuffers::Vector<int64_t>> zero_point) {
    fbb_.AddOffset(QuantizationParameters::VT_ZERO_POINT, zero_point);
  }
  void add_details_type(opencv_tflite::QuantizationDetails details_type) {
    fbb_.AddElement<uint8_t>(QuantizationParameters::VT_DETAILS_TYPE, static_cast<uint8_t>(details_type), 0);
  }
  void add_details(::flatbuffers::Offset<void> details) {
    fbb_.AddOffset(QuantizationParameters::VT_DETAILS, details);
  }
  void add_quantized_dimension(int32_t quantized_dimension) {
    fbb_.AddElement<int32_t>(QuantizationParameters::VT_QUANTIZED_DIMENSION, quantized_dimension, 0);
  }
  explicit QuantizationParametersBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<QuantizationParameters> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<QuantizationParameters>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<QuantizationParameters> CreateQuantizationParameters(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<float>> min = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<float>> max = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<float>> scale = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<int64_t>> zero_point = 0,
    opencv_tflite::QuantizationDetails details_type = opencv_tflite::QuantizationDetails_NONE,
    ::flatbuffers::Offset<void> details = 0,
    int32_t quantized_dimension = 0) {
  QuantizationParametersBuilder builder_(_fbb);
  builder_.add_quantized_dimension(quantized_dimension);
  builder_.add_details(details);
  builder_.add_zero_point(zero_point);
  builder_.add_scale(scale);
  builder_.add_max(max);
  builder_.add_min(min);
  builder_.add_details_type(details_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<QuantizationParameters> CreateQuantizationParametersDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<float> *min = nullptr,
    const std::vector<float> *max = nullptr,
    const std::vector<float> *scale = nullptr,
    const std::vector<int64_t> *zero_point = nullptr,
    opencv_tflite::QuantizationDetails details_type = opencv_tflite::QuantizationDetails_NONE,
    ::flatbuffers::Offset<void> details = 0,
    int32_t quantized_dimension = 0) {
  auto min__ = min ? _fbb.CreateVector<float>(*min) : 0;
  auto max__ = max ? _fbb.CreateVector<float>(*max) : 0;
  auto scale__ = scale ? _fbb.CreateVector<float>(*scale) : 0;
  auto zero_point__ = zero_point ? _fbb.CreateVector<int64_t>(*zero_point) : 0;
  return opencv_tflite::CreateQuantizationParameters(
      _fbb,
      min__,
      max__,
      scale__,
      zero_point__,
      details_type,
      details,
      quantized_dimension);
}

struct Int32Vector FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef Int32VectorBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VALUES = 4
  };
  const ::flatbuffers::Vector<int32_t> *values() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_VALUES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_VALUES) &&
           verifier.VerifyVector(values()) &&
           verifier.EndTable();
  }
};

struct Int32VectorBuilder {
  typedef Int32Vector Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_values(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> values) {
    fbb_.AddOffset(Int32Vector::VT_VALUES, values);
  }
  explicit Int32VectorBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Int32Vector> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Int32Vector>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Int32Vector> CreateInt32Vector(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> values = 0) {
  Int32VectorBuilder builder_(_fbb);
  builder_.add_values(values);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Int32Vector> CreateInt32VectorDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<int32_t> *values = nullptr) {
  auto values__ = values ? _fbb.CreateVector<int32_t>(*values) : 0;
  return opencv_tflite::CreateInt32Vector(
      _fbb,
      values__);
}

struct Uint16Vector FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef Uint16VectorBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VALUES = 4
  };
  const ::flatbuffers::Vector<uint16_t> *values() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_VALUES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_VALUES) &&
           verifier.VerifyVector(values()) &&
           verifier.EndTable();
  }
};

struct Uint16VectorBuilder {
  typedef Uint16Vector Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_values(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> values) {
    fbb_.AddOffset(Uint16Vector::VT_VALUES, values);
  }
  explicit Uint16VectorBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Uint16Vector> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Uint16Vector>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Uint16Vector> CreateUint16Vector(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> values = 0) {
  Uint16VectorBuilder builder_(_fbb);
  builder_.add_values(values);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Uint16Vector> CreateUint16VectorDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint16_t> *values = nullptr) {
  if (values) { _fbb.ForceVectorAlignment(values->size(), sizeof(uint16_t), 4); }
  auto values__ = values ? _fbb.CreateVector<uint16_t>(*values) : 0;
  return opencv_tflite::CreateUint16Vector(
      _fbb,
      values__);
}

struct Uint8Vector FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef Uint8VectorBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VALUES = 4
  };
  const ::flatbuffers::Vector<uint8_t> *values() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_VALUES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_VALUES) &&
           verifier.VerifyVector(values()) &&
           verifier.EndTable();
  }
};

struct Uint8VectorBuilder {
  typedef Uint8Vector Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_values(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> values) {
    fbb_.AddOffset(Uint8Vector::VT_VALUES, values);
  }
  explicit Uint8VectorBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Uint8Vector> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Uint8Vector>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Uint8Vector> CreateUint8Vector(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> values = 0) {
  Uint8VectorBuilder builder_(_fbb);
  builder_.add_values(values);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Uint8Vector> CreateUint8VectorDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *values = nullptr) {
  if (values) { _fbb.ForceVectorAlignment(values->size(), sizeof(uint8_t), 4); }
  auto values__ = values ? _fbb.CreateVector<uint8_t>(*values) : 0;
  return opencv_tflite::CreateUint8Vector(
      _fbb,
      values__);
}

struct DimensionMetadata FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DimensionMetadataBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FORMAT = 4,
    VT_DENSE_SIZE = 6,
    VT_ARRAY_SEGMENTS_TYPE = 8,
    VT_ARRAY_SEGMENTS = 10,
    VT_ARRAY_INDICES_TYPE = 12,
    VT_ARRAY_INDICES = 14
  };
  opencv_tflite::DimensionType format() const {
    return static_cast<opencv_tflite::DimensionType>(GetField<int8_t>(VT_FORMAT, 0));
  }
  int32_t dense_size() const {
    return GetField<int32_t>(VT_DENSE_SIZE, 0);
  }
  opencv_tflite::SparseIndexVector array_segments_type() const {
    return static_cast<opencv_tflite::SparseIndexVector>(GetField<uint8_t>(VT_ARRAY_SEGMENTS_TYPE, 0));
  }
  const void *array_segments() const {
    return GetPointer<const void *>(VT_ARRAY_SEGMENTS);
  }
  template<typename T> const T *array_segments_as() const;
  const opencv_tflite::Int32Vector *array_segments_as_Int32Vector() const {
    return array_segments_type() == opencv_tflite::SparseIndexVector_Int32Vector ? static_cast<const opencv_tflite::Int32Vector *>(array_segments()) : nullptr;
  }
  const opencv_tflite::Uint16Vector *array_segments_as_Uint16Vector() const {
    return array_segments_type() == opencv_tflite::SparseIndexVector_Uint16Vector ? static_cast<const opencv_tflite::Uint16Vector *>(array_segments()) : nullptr;
  }
  const opencv_tflite::Uint8Vector *array_segments_as_Uint8Vector() const {
    return array_segments_type() == opencv_tflite::SparseIndexVector_Uint8Vector ? static_cast<const opencv_tflite::Uint8Vector *>(array_segments()) : nullptr;
  }
  opencv_tflite::SparseIndexVector array_indices_type() const {
    return static_cast<opencv_tflite::SparseIndexVector>(GetField<uint8_t>(VT_ARRAY_INDICES_TYPE, 0));
  }
  const void *array_indices() const {
    return GetPointer<const void *>(VT_ARRAY_INDICES);
  }
  template<typename T> const T *array_indices_as() const;
  const opencv_tflite::Int32Vector *array_indices_as_Int32Vector() const {
    return array_indices_type() == opencv_tflite::SparseIndexVector_Int32Vector ? static_cast<const opencv_tflite::Int32Vector *>(array_indices()) : nullptr;
  }
  const opencv_tflite::Uint16Vector *array_indices_as_Uint16Vector() const {
    return array_indices_type() == opencv_tflite::SparseIndexVector_Uint16Vector ? static_cast<const opencv_tflite::Uint16Vector *>(array_indices()) : nullptr;
  }
  const opencv_tflite::Uint8Vector *array_indices_as_Uint8Vector() const {
    return array_indices_type() == opencv_tflite::SparseIndexVector_Uint8Vector ? static_cast<const opencv_tflite::Uint8Vector *>(array_indices()) : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_FORMAT, 1) &&
           VerifyField<int32_t>(verifier, VT_DENSE_SIZE, 4) &&
           VerifyField<uint8_t>(verifier, VT_ARRAY_SEGMENTS_TYPE, 1) &&
           VerifyOffset(verifier, VT_ARRAY_SEGMENTS) &&
           VerifySparseIndexVector(verifier, array_segments(), array_segments_type()) &&
           VerifyField<uint8_t>(verifier, VT_ARRAY_INDICES_TYPE, 1) &&
           VerifyOffset(verifier, VT_ARRAY_INDICES) &&
           VerifySparseIndexVector(verifier, array_indices(), array_indices_type()) &&
           verifier.EndTable();
  }
};

template<> inline const opencv_tflite::Int32Vector *DimensionMetadata::array_segments_as<opencv_tflite::Int32Vector>() const {
  return array_segments_as_Int32Vector();
}

template<> inline const opencv_tflite::Uint16Vector *DimensionMetadata::array_segments_as<opencv_tflite::Uint16Vector>() const {
  return array_segments_as_Uint16Vector();
}

template<> inline const opencv_tflite::Uint8Vector *DimensionMetadata::array_segments_as<opencv_tflite::Uint8Vector>() const {
  return array_segments_as_Uint8Vector();
}

template<> inline const opencv_tflite::Int32Vector *DimensionMetadata::array_indices_as<opencv_tflite::Int32Vector>() const {
  return array_indices_as_Int32Vector();
}

template<> inline const opencv_tflite::Uint16Vector *DimensionMetadata::array_indices_as<opencv_tflite::Uint16Vector>() const {
  return array_indices_as_Uint16Vector();
}

template<> inline const opencv_tflite::Uint8Vector *DimensionMetadata::array_indices_as<opencv_tflite::Uint8Vector>() const {
  return array_indices_as_Uint8Vector();
}

struct DimensionMetadataBuilder {
  typedef DimensionMetadata Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_format(opencv_tflite::DimensionType format) {
    fbb_.AddElement<int8_t>(DimensionMetadata::VT_FORMAT, static_cast<int8_t>(format), 0);
  }
  void add_dense_size(int32_t dense_size) {
    fbb_.AddElement<int32_t>(DimensionMetadata::VT_DENSE_SIZE, dense_size, 0);
  }
  void add_array_segments_type(opencv_tflite::SparseIndexVector array_segments_type) {
    fbb_.AddElement<uint8_t>(DimensionMetadata::VT_ARRAY_SEGMENTS_TYPE, static_cast<uint8_t>(array_segments_type), 0);
  }
  void add_array_segments(::flatbuffers::Offset<void> array_segments) {
    fbb_.AddOffset(DimensionMetadata::VT_ARRAY_SEGMENTS, array_segments);
  }
  void add_array_indices_type(opencv_tflite::SparseIndexVector array_indices_type) {
    fbb_.AddElement<uint8_t>(DimensionMetadata::VT_ARRAY_INDICES_TYPE, static_cast<uint8_t>(array_indices_type), 0);
  }
  void add_array_indices(::flatbuffers::Offset<void> array_indices) {
    fbb_.AddOffset(DimensionMetadata::VT_ARRAY_INDICES, array_indices);
  }
  explicit DimensionMetadataBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DimensionMetadata> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DimensionMetadata>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DimensionMetadata> CreateDimensionMetadata(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::DimensionType format = opencv_tflite::DimensionType_DENSE,
    int32_t dense_size = 0,
    opencv_tflite::SparseIndexVector array_segments_type = opencv_tflite::SparseIndexVector_NONE,
    ::flatbuffers::Offset<void> array_segments = 0,
    opencv_tflite::SparseIndexVector array_indices_type = opencv_tflite::SparseIndexVector_NONE,
    ::flatbuffers::Offset<void> array_indices = 0) {
  DimensionMetadataBuilder builder_(_fbb);
  builder_.add_array_indices(array_indices);
  builder_.add_array_segments(array_segments);
  builder_.add_dense_size(dense_size);
  builder_.add_array_indices_type(array_indices_type);
  builder_.add_array_segments_type(array_segments_type);
  builder_.add_format(format);
  return builder_.Finish();
}

struct SparsityParameters FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SparsityParametersBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TRAVERSAL_ORDER = 4,
    VT_BLOCK_MAP = 6,
    VT_DIM_METADATA = 8
  };
  const ::flatbuffers::Vector<int32_t> *traversal_order() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_TRAVERSAL_ORDER);
  }
  const ::flatbuffers::Vector<int32_t> *block_map() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_BLOCK_MAP);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::DimensionMetadata>> *dim_metadata() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::DimensionMetadata>> *>(VT_DIM_METADATA);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_TRAVERSAL_ORDER) &&
           verifier.VerifyVector(traversal_order()) &&
           VerifyOffset(verifier, VT_BLOCK_MAP) &&
           verifier.VerifyVector(block_map()) &&
           VerifyOffset(verifier, VT_DIM_METADATA) &&
           verifier.VerifyVector(dim_metadata()) &&
           verifier.VerifyVectorOfTables(dim_metadata()) &&
           verifier.EndTable();
  }
};

struct SparsityParametersBuilder {
  typedef SparsityParameters Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_traversal_order(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> traversal_order) {
    fbb_.AddOffset(SparsityParameters::VT_TRAVERSAL_ORDER, traversal_order);
  }
  void add_block_map(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> block_map) {
    fbb_.AddOffset(SparsityParameters::VT_BLOCK_MAP, block_map);
  }
  void add_dim_metadata(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::DimensionMetadata>>> dim_metadata) {
    fbb_.AddOffset(SparsityParameters::VT_DIM_METADATA, dim_metadata);
  }
  explicit SparsityParametersBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SparsityParameters> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SparsityParameters>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SparsityParameters> CreateSparsityParameters(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> traversal_order = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> block_map = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::DimensionMetadata>>> dim_metadata = 0) {
  SparsityParametersBuilder builder_(_fbb);
  builder_.add_dim_metadata(dim_metadata);
  builder_.add_block_map(block_map);
  builder_.add_traversal_order(traversal_order);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<SparsityParameters> CreateSparsityParametersDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<int32_t> *traversal_order = nullptr,
    const std::vector<int32_t> *block_map = nullptr,
    const std::vector<::flatbuffers::Offset<opencv_tflite::DimensionMetadata>> *dim_metadata = nullptr) {
  auto traversal_order__ = traversal_order ? _fbb.CreateVector<int32_t>(*traversal_order) : 0;
  auto block_map__ = block_map ? _fbb.CreateVector<int32_t>(*block_map) : 0;
  auto dim_metadata__ = dim_metadata ? _fbb.CreateVector<::flatbuffers::Offset<opencv_tflite::DimensionMetadata>>(*dim_metadata) : 0;
  return opencv_tflite::CreateSparsityParameters(
      _fbb,
      traversal_order__,
      block_map__,
      dim_metadata__);
}

struct VariantSubType FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef VariantSubTypeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SHAPE = 4,
    VT_TYPE = 6,
    VT_HAS_RANK = 8
  };
  const ::flatbuffers::Vector<int32_t> *shape() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_SHAPE);
  }
  opencv_tflite::TensorType type() const {
    return static_cast<opencv_tflite::TensorType>(GetField<int8_t>(VT_TYPE, 0));
  }
  bool has_rank() const {
    return GetField<uint8_t>(VT_HAS_RANK, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_SHAPE) &&
           verifier.VerifyVector(shape()) &&
           VerifyField<int8_t>(verifier, VT_TYPE, 1) &&
           VerifyField<uint8_t>(verifier, VT_HAS_RANK, 1) &&
           verifier.EndTable();
  }
};

struct VariantSubTypeBuilder {
  typedef VariantSubType Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_shape(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> shape) {
    fbb_.AddOffset(VariantSubType::VT_SHAPE, shape);
  }
  void add_type(opencv_tflite::TensorType type) {
    fbb_.AddElement<int8_t>(VariantSubType::VT_TYPE, static_cast<int8_t>(type), 0);
  }
  void add_has_rank(bool has_rank) {
    fbb_.AddElement<uint8_t>(VariantSubType::VT_HAS_RANK, static_cast<uint8_t>(has_rank), 0);
  }
  explicit VariantSubTypeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<VariantSubType> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<VariantSubType>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<VariantSubType> CreateVariantSubType(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> shape = 0,
    opencv_tflite::TensorType type = opencv_tflite::TensorType_FLOAT32,
    bool has_rank = false) {
  VariantSubTypeBuilder builder_(_fbb);
  builder_.add_shape(shape);
  builder_.add_has_rank(has_rank);
  builder_.add_type(type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<VariantSubType> CreateVariantSubTypeDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<int32_t> *shape = nullptr,
    opencv_tflite::TensorType type = opencv_tflite::TensorType_FLOAT32,
    bool has_rank = false) {
  auto shape__ = shape ? _fbb.CreateVector<int32_t>(*shape) : 0;
  return opencv_tflite::CreateVariantSubType(
      _fbb,
      shape__,
      type,
      has_rank);
}

struct Tensor FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef TensorBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SHAPE = 4,
    VT_TYPE = 6,
    VT_BUFFER = 8,
    VT_NAME = 10,
    VT_QUANTIZATION = 12,
    VT_IS_VARIABLE = 14,
    VT_SPARSITY = 16,
    VT_SHAPE_SIGNATURE = 18,
    VT_HAS_RANK = 20,
    VT_VARIANT_TENSORS = 22
  };
  const ::flatbuffers::Vector<int32_t> *shape() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_SHAPE);
  }
  opencv_tflite::TensorType type() const {
    return static_cast<opencv_tflite::TensorType>(GetField<int8_t>(VT_TYPE, 0));
  }
  uint32_t buffer() const {
    return GetField<uint32_t>(VT_BUFFER, 0);
  }
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  const opencv_tflite::QuantizationParameters *quantization() const {
    return GetPointer<const opencv_tflite::QuantizationParameters *>(VT_QUANTIZATION);
  }
  bool is_variable() const {
    return GetField<uint8_t>(VT_IS_VARIABLE, 0) != 0;
  }
  const opencv_tflite::SparsityParameters *sparsity() const {
    return GetPointer<const opencv_tflite::SparsityParameters *>(VT_SPARSITY);
  }
  const ::flatbuffers::Vector<int32_t> *shape_signature() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_SHAPE_SIGNATURE);
  }
  bool has_rank() const {
    return GetField<uint8_t>(VT_HAS_RANK, 0) != 0;
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::VariantSubType>> *variant_tensors() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::VariantSubType>> *>(VT_VARIANT_TENSORS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_SHAPE) &&
           verifier.VerifyVector(shape()) &&
           VerifyField<int8_t>(verifier, VT_TYPE, 1) &&
           VerifyField<uint32_t>(verifier, VT_BUFFER, 4) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_QUANTIZATION) &&
           verifier.VerifyTable(quantization()) &&
           VerifyField<uint8_t>(verifier, VT_IS_VARIABLE, 1) &&
           VerifyOffset(verifier, VT_SPARSITY) &&
           verifier.VerifyTable(sparsity()) &&
           VerifyOffset(verifier, VT_SHAPE_SIGNATURE) &&
           verifier.VerifyVector(shape_signature()) &&
           VerifyField<uint8_t>(verifier, VT_HAS_RANK, 1) &&
           VerifyOffset(verifier, VT_VARIANT_TENSORS) &&
           verifier.VerifyVector(variant_tensors()) &&
           verifier.VerifyVectorOfTables(variant_tensors()) &&
           verifier.EndTable();
  }
};

struct TensorBuilder {
  typedef Tensor Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_shape(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> shape) {
    fbb_.AddOffset(Tensor::VT_SHAPE, shape);
  }
  void add_type(opencv_tflite::TensorType type) {
    fbb_.AddElement<int8_t>(Tensor::VT_TYPE, static_cast<int8_t>(type), 0);
  }
  void add_buffer(uint32_t buffer) {
    fbb_.AddElement<uint32_t>(Tensor::VT_BUFFER, buffer, 0);
  }
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(Tensor::VT_NAME, name);
  }
  void add_quantization(::flatbuffers::Offset<opencv_tflite::QuantizationParameters> quantization) {
    fbb_.AddOffset(Tensor::VT_QUANTIZATION, quantization);
  }
  void add_is_variable(bool is_variable) {
    fbb_.AddElement<uint8_t>(Tensor::VT_IS_VARIABLE, static_cast<uint8_t>(is_variable), 0);
  }
  void add_sparsity(::flatbuffers::Offset<opencv_tflite::SparsityParameters> sparsity) {
    fbb_.AddOffset(Tensor::VT_SPARSITY, sparsity);
  }
  void add_shape_signature(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> shape_signature) {
    fbb_.AddOffset(Tensor::VT_SHAPE_SIGNATURE, shape_signature);
  }
  void add_has_rank(bool has_rank) {
    fbb_.AddElement<uint8_t>(Tensor::VT_HAS_RANK, static_cast<uint8_t>(has_rank), 0);
  }
  void add_variant_tensors(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::VariantSubType>>> variant_tensors) {
    fbb_.AddOffset(Tensor::VT_VARIANT_TENSORS, variant_tensors);
  }
  explicit TensorBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Tensor> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Tensor>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Tensor> CreateTensor(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> shape = 0,
    opencv_tflite::TensorType type = opencv_tflite::TensorType_FLOAT32,
    uint32_t buffer = 0,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<opencv_tflite::QuantizationParameters> quantization = 0,
    bool is_variable = false,
    ::flatbuffers::Offset<opencv_tflite::SparsityParameters> sparsity = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> shape_signature = 0,
    bool has_rank = false,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::VariantSubType>>> variant_tensors = 0) {
  TensorBuilder builder_(_fbb);
  builder_.add_variant_tensors(variant_tensors);
  builder_.add_shape_signature(shape_signature);
  builder_.add_sparsity(sparsity);
  builder_.add_quantization(quantization);
  builder_.add_name(name);
  builder_.add_buffer(buffer);
  builder_.add_shape(shape);
  builder_.add_has_rank(has_rank);
  builder_.add_is_variable(is_variable);
  builder_.add_type(type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Tensor> CreateTensorDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<int32_t> *shape = nullptr,
    opencv_tflite::TensorType type = opencv_tflite::TensorType_FLOAT32,
    uint32_t buffer = 0,
    const char *name = nullptr,
    ::flatbuffers::Offset<opencv_tflite::QuantizationParameters> quantization = 0,
    bool is_variable = false,
    ::flatbuffers::Offset<opencv_tflite::SparsityParameters> sparsity = 0,
    const std::vector<int32_t> *shape_signature = nullptr,
    bool has_rank = false,
    const std::vector<::flatbuffers::Offset<opencv_tflite::VariantSubType>> *variant_tensors = nullptr) {
  auto shape__ = shape ? _fbb.CreateVector<int32_t>(*shape) : 0;
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto shape_signature__ = shape_signature ? _fbb.CreateVector<int32_t>(*shape_signature) : 0;
  auto variant_tensors__ = variant_tensors ? _fbb.CreateVector<::flatbuffers::Offset<opencv_tflite::VariantSubType>>(*variant_tensors) : 0;
  return opencv_tflite::CreateTensor(
      _fbb,
      shape__,
      type,
      buffer,
      name__,
      quantization,
      is_variable,
      sparsity,
      shape_signature__,
      has_rank,
      variant_tensors__);
}

struct Conv2DOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef Conv2DOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PADDING = 4,
    VT_STRIDE_W = 6,
    VT_STRIDE_H = 8,
    VT_FUSED_ACTIVATION_FUNCTION = 10,
    VT_DILATION_W_FACTOR = 12,
    VT_DILATION_H_FACTOR = 14
  };
  opencv_tflite::Padding padding() const {
    return static_cast<opencv_tflite::Padding>(GetField<int8_t>(VT_PADDING, 0));
  }
  int32_t stride_w() const {
    return GetField<int32_t>(VT_STRIDE_W, 0);
  }
  int32_t stride_h() const {
    return GetField<int32_t>(VT_STRIDE_H, 0);
  }
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  int32_t dilation_w_factor() const {
    return GetField<int32_t>(VT_DILATION_W_FACTOR, 1);
  }
  int32_t dilation_h_factor() const {
    return GetField<int32_t>(VT_DILATION_H_FACTOR, 1);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_PADDING, 1) &&
           VerifyField<int32_t>(verifier, VT_STRIDE_W, 4) &&
           VerifyField<int32_t>(verifier, VT_STRIDE_H, 4) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           VerifyField<int32_t>(verifier, VT_DILATION_W_FACTOR, 4) &&
           VerifyField<int32_t>(verifier, VT_DILATION_H_FACTOR, 4) &&
           verifier.EndTable();
  }
};

struct Conv2DOptionsBuilder {
  typedef Conv2DOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_padding(opencv_tflite::Padding padding) {
    fbb_.AddElement<int8_t>(Conv2DOptions::VT_PADDING, static_cast<int8_t>(padding), 0);
  }
  void add_stride_w(int32_t stride_w) {
    fbb_.AddElement<int32_t>(Conv2DOptions::VT_STRIDE_W, stride_w, 0);
  }
  void add_stride_h(int32_t stride_h) {
    fbb_.AddElement<int32_t>(Conv2DOptions::VT_STRIDE_H, stride_h, 0);
  }
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(Conv2DOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  void add_dilation_w_factor(int32_t dilation_w_factor) {
    fbb_.AddElement<int32_t>(Conv2DOptions::VT_DILATION_W_FACTOR, dilation_w_factor, 1);
  }
  void add_dilation_h_factor(int32_t dilation_h_factor) {
    fbb_.AddElement<int32_t>(Conv2DOptions::VT_DILATION_H_FACTOR, dilation_h_factor, 1);
  }
  explicit Conv2DOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Conv2DOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Conv2DOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Conv2DOptions> CreateConv2DOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::Padding padding = opencv_tflite::Padding_SAME,
    int32_t stride_w = 0,
    int32_t stride_h = 0,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE,
    int32_t dilation_w_factor = 1,
    int32_t dilation_h_factor = 1) {
  Conv2DOptionsBuilder builder_(_fbb);
  builder_.add_dilation_h_factor(dilation_h_factor);
  builder_.add_dilation_w_factor(dilation_w_factor);
  builder_.add_stride_h(stride_h);
  builder_.add_stride_w(stride_w);
  builder_.add_fused_activation_function(fused_activation_function);
  builder_.add_padding(padding);
  return builder_.Finish();
}

struct Conv3DOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef Conv3DOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PADDING = 4,
    VT_STRIDE_D = 6,
    VT_STRIDE_W = 8,
    VT_STRIDE_H = 10,
    VT_FUSED_ACTIVATION_FUNCTION = 12,
    VT_DILATION_D_FACTOR = 14,
    VT_DILATION_W_FACTOR = 16,
    VT_DILATION_H_FACTOR = 18
  };
  opencv_tflite::Padding padding() const {
    return static_cast<opencv_tflite::Padding>(GetField<int8_t>(VT_PADDING, 0));
  }
  int32_t stride_d() const {
    return GetField<int32_t>(VT_STRIDE_D, 0);
  }
  int32_t stride_w() const {
    return GetField<int32_t>(VT_STRIDE_W, 0);
  }
  int32_t stride_h() const {
    return GetField<int32_t>(VT_STRIDE_H, 0);
  }
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  int32_t dilation_d_factor() const {
    return GetField<int32_t>(VT_DILATION_D_FACTOR, 1);
  }
  int32_t dilation_w_factor() const {
    return GetField<int32_t>(VT_DILATION_W_FACTOR, 1);
  }
  int32_t dilation_h_factor() const {
    return GetField<int32_t>(VT_DILATION_H_FACTOR, 1);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_PADDING, 1) &&
           VerifyField<int32_t>(verifier, VT_STRIDE_D, 4) &&
           VerifyField<int32_t>(verifier, VT_STRIDE_W, 4) &&
           VerifyField<int32_t>(verifier, VT_STRIDE_H, 4) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           VerifyField<int32_t>(verifier, VT_DILATION_D_FACTOR, 4) &&
           VerifyField<int32_t>(verifier, VT_DILATION_W_FACTOR, 4) &&
           VerifyField<int32_t>(verifier, VT_DILATION_H_FACTOR, 4) &&
           verifier.EndTable();
  }
};

struct Conv3DOptionsBuilder {
  typedef Conv3DOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_padding(opencv_tflite::Padding padding) {
    fbb_.AddElement<int8_t>(Conv3DOptions::VT_PADDING, static_cast<int8_t>(padding), 0);
  }
  void add_stride_d(int32_t stride_d) {
    fbb_.AddElement<int32_t>(Conv3DOptions::VT_STRIDE_D, stride_d, 0);
  }
  void add_stride_w(int32_t stride_w) {
    fbb_.AddElement<int32_t>(Conv3DOptions::VT_STRIDE_W, stride_w, 0);
  }
  void add_stride_h(int32_t stride_h) {
    fbb_.AddElement<int32_t>(Conv3DOptions::VT_STRIDE_H, stride_h, 0);
  }
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(Conv3DOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  void add_dilation_d_factor(int32_t dilation_d_factor) {
    fbb_.AddElement<int32_t>(Conv3DOptions::VT_DILATION_D_FACTOR, dilation_d_factor, 1);
  }
  void add_dilation_w_factor(int32_t dilation_w_factor) {
    fbb_.AddElement<int32_t>(Conv3DOptions::VT_DILATION_W_FACTOR, dilation_w_factor, 1);
  }
  void add_dilation_h_factor(int32_t dilation_h_factor) {
    fbb_.AddElement<int32_t>(Conv3DOptions::VT_DILATION_H_FACTOR, dilation_h_factor, 1);
  }
  explicit Conv3DOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Conv3DOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Conv3DOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Conv3DOptions> CreateConv3DOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::Padding padding = opencv_tflite::Padding_SAME,
    int32_t stride_d = 0,
    int32_t stride_w = 0,
    int32_t stride_h = 0,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE,
    int32_t dilation_d_factor = 1,
    int32_t dilation_w_factor = 1,
    int32_t dilation_h_factor = 1) {
  Conv3DOptionsBuilder builder_(_fbb);
  builder_.add_dilation_h_factor(dilation_h_factor);
  builder_.add_dilation_w_factor(dilation_w_factor);
  builder_.add_dilation_d_factor(dilation_d_factor);
  builder_.add_stride_h(stride_h);
  builder_.add_stride_w(stride_w);
  builder_.add_stride_d(stride_d);
  builder_.add_fused_activation_function(fused_activation_function);
  builder_.add_padding(padding);
  return builder_.Finish();
}

struct Pool2DOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef Pool2DOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PADDING = 4,
    VT_STRIDE_W = 6,
    VT_STRIDE_H = 8,
    VT_FILTER_WIDTH = 10,
    VT_FILTER_HEIGHT = 12,
    VT_FUSED_ACTIVATION_FUNCTION = 14
  };
  opencv_tflite::Padding padding() const {
    return static_cast<opencv_tflite::Padding>(GetField<int8_t>(VT_PADDING, 0));
  }
  int32_t stride_w() const {
    return GetField<int32_t>(VT_STRIDE_W, 0);
  }
  int32_t stride_h() const {
    return GetField<int32_t>(VT_STRIDE_H, 0);
  }
  int32_t filter_width() const {
    return GetField<int32_t>(VT_FILTER_WIDTH, 0);
  }
  int32_t filter_height() const {
    return GetField<int32_t>(VT_FILTER_HEIGHT, 0);
  }
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_PADDING, 1) &&
           VerifyField<int32_t>(verifier, VT_STRIDE_W, 4) &&
           VerifyField<int32_t>(verifier, VT_STRIDE_H, 4) &&
           VerifyField<int32_t>(verifier, VT_FILTER_WIDTH, 4) &&
           VerifyField<int32_t>(verifier, VT_FILTER_HEIGHT, 4) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           verifier.EndTable();
  }
};

struct Pool2DOptionsBuilder {
  typedef Pool2DOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_padding(opencv_tflite::Padding padding) {
    fbb_.AddElement<int8_t>(Pool2DOptions::VT_PADDING, static_cast<int8_t>(padding), 0);
  }
  void add_stride_w(int32_t stride_w) {
    fbb_.AddElement<int32_t>(Pool2DOptions::VT_STRIDE_W, stride_w, 0);
  }
  void add_stride_h(int32_t stride_h) {
    fbb_.AddElement<int32_t>(Pool2DOptions::VT_STRIDE_H, stride_h, 0);
  }
  void add_filter_width(int32_t filter_width) {
    fbb_.AddElement<int32_t>(Pool2DOptions::VT_FILTER_WIDTH, filter_width, 0);
  }
  void add_filter_height(int32_t filter_height) {
    fbb_.AddElement<int32_t>(Pool2DOptions::VT_FILTER_HEIGHT, filter_height, 0);
  }
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(Pool2DOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  explicit Pool2DOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Pool2DOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Pool2DOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Pool2DOptions> CreatePool2DOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::Padding padding = opencv_tflite::Padding_SAME,
    int32_t stride_w = 0,
    int32_t stride_h = 0,
    int32_t filter_width = 0,
    int32_t filter_height = 0,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE) {
  Pool2DOptionsBuilder builder_(_fbb);
  builder_.add_filter_height(filter_height);
  builder_.add_filter_width(filter_width);
  builder_.add_stride_h(stride_h);
  builder_.add_stride_w(stride_w);
  builder_.add_fused_activation_function(fused_activation_function);
  builder_.add_padding(padding);
  return builder_.Finish();
}

struct DepthwiseConv2DOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DepthwiseConv2DOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PADDING = 4,
    VT_STRIDE_W = 6,
    VT_STRIDE_H = 8,
    VT_DEPTH_MULTIPLIER = 10,
    VT_FUSED_ACTIVATION_FUNCTION = 12,
    VT_DILATION_W_FACTOR = 14,
    VT_DILATION_H_FACTOR = 16
  };
  opencv_tflite::Padding padding() const {
    return static_cast<opencv_tflite::Padding>(GetField<int8_t>(VT_PADDING, 0));
  }
  int32_t stride_w() const {
    return GetField<int32_t>(VT_STRIDE_W, 0);
  }
  int32_t stride_h() const {
    return GetField<int32_t>(VT_STRIDE_H, 0);
  }
  int32_t depth_multiplier() const {
    return GetField<int32_t>(VT_DEPTH_MULTIPLIER, 0);
  }
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  int32_t dilation_w_factor() const {
    return GetField<int32_t>(VT_DILATION_W_FACTOR, 1);
  }
  int32_t dilation_h_factor() const {
    return GetField<int32_t>(VT_DILATION_H_FACTOR, 1);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_PADDING, 1) &&
           VerifyField<int32_t>(verifier, VT_STRIDE_W, 4) &&
           VerifyField<int32_t>(verifier, VT_STRIDE_H, 4) &&
           VerifyField<int32_t>(verifier, VT_DEPTH_MULTIPLIER, 4) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           VerifyField<int32_t>(verifier, VT_DILATION_W_FACTOR, 4) &&
           VerifyField<int32_t>(verifier, VT_DILATION_H_FACTOR, 4) &&
           verifier.EndTable();
  }
};

struct DepthwiseConv2DOptionsBuilder {
  typedef DepthwiseConv2DOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_padding(opencv_tflite::Padding padding) {
    fbb_.AddElement<int8_t>(DepthwiseConv2DOptions::VT_PADDING, static_cast<int8_t>(padding), 0);
  }
  void add_stride_w(int32_t stride_w) {
    fbb_.AddElement<int32_t>(DepthwiseConv2DOptions::VT_STRIDE_W, stride_w, 0);
  }
  void add_stride_h(int32_t stride_h) {
    fbb_.AddElement<int32_t>(DepthwiseConv2DOptions::VT_STRIDE_H, stride_h, 0);
  }
  void add_depth_multiplier(int32_t depth_multiplier) {
    fbb_.AddElement<int32_t>(DepthwiseConv2DOptions::VT_DEPTH_MULTIPLIER, depth_multiplier, 0);
  }
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(DepthwiseConv2DOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  void add_dilation_w_factor(int32_t dilation_w_factor) {
    fbb_.AddElement<int32_t>(DepthwiseConv2DOptions::VT_DILATION_W_FACTOR, dilation_w_factor, 1);
  }
  void add_dilation_h_factor(int32_t dilation_h_factor) {
    fbb_.AddElement<int32_t>(DepthwiseConv2DOptions::VT_DILATION_H_FACTOR, dilation_h_factor, 1);
  }
  explicit DepthwiseConv2DOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DepthwiseConv2DOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DepthwiseConv2DOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DepthwiseConv2DOptions> CreateDepthwiseConv2DOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::Padding padding = opencv_tflite::Padding_SAME,
    int32_t stride_w = 0,
    int32_t stride_h = 0,
    int32_t depth_multiplier = 0,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE,
    int32_t dilation_w_factor = 1,
    int32_t dilation_h_factor = 1) {
  DepthwiseConv2DOptionsBuilder builder_(_fbb);
  builder_.add_dilation_h_factor(dilation_h_factor);
  builder_.add_dilation_w_factor(dilation_w_factor);
  builder_.add_depth_multiplier(depth_multiplier);
  builder_.add_stride_h(stride_h);
  builder_.add_stride_w(stride_w);
  builder_.add_fused_activation_function(fused_activation_function);
  builder_.add_padding(padding);
  return builder_.Finish();
}

struct ConcatEmbeddingsOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ConcatEmbeddingsOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NUM_CHANNELS = 4,
    VT_NUM_COLUMNS_PER_CHANNEL = 6,
    VT_EMBEDDING_DIM_PER_CHANNEL = 8
  };
  int32_t num_channels() const {
    return GetField<int32_t>(VT_NUM_CHANNELS, 0);
  }
  const ::flatbuffers::Vector<int32_t> *num_columns_per_channel() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_NUM_COLUMNS_PER_CHANNEL);
  }
  const ::flatbuffers::Vector<int32_t> *embedding_dim_per_channel() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_EMBEDDING_DIM_PER_CHANNEL);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_NUM_CHANNELS, 4) &&
           VerifyOffset(verifier, VT_NUM_COLUMNS_PER_CHANNEL) &&
           verifier.VerifyVector(num_columns_per_channel()) &&
           VerifyOffset(verifier, VT_EMBEDDING_DIM_PER_CHANNEL) &&
           verifier.VerifyVector(embedding_dim_per_channel()) &&
           verifier.EndTable();
  }
};

struct ConcatEmbeddingsOptionsBuilder {
  typedef ConcatEmbeddingsOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_num_channels(int32_t num_channels) {
    fbb_.AddElement<int32_t>(ConcatEmbeddingsOptions::VT_NUM_CHANNELS, num_channels, 0);
  }
  void add_num_columns_per_channel(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> num_columns_per_channel) {
    fbb_.AddOffset(ConcatEmbeddingsOptions::VT_NUM_COLUMNS_PER_CHANNEL, num_columns_per_channel);
  }
  void add_embedding_dim_per_channel(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> embedding_dim_per_channel) {
    fbb_.AddOffset(ConcatEmbeddingsOptions::VT_EMBEDDING_DIM_PER_CHANNEL, embedding_dim_per_channel);
  }
  explicit ConcatEmbeddingsOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ConcatEmbeddingsOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ConcatEmbeddingsOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ConcatEmbeddingsOptions> CreateConcatEmbeddingsOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t num_channels = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> num_columns_per_channel = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> embedding_dim_per_channel = 0) {
  ConcatEmbeddingsOptionsBuilder builder_(_fbb);
  builder_.add_embedding_dim_per_channel(embedding_dim_per_channel);
  builder_.add_num_columns_per_channel(num_columns_per_channel);
  builder_.add_num_channels(num_channels);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<ConcatEmbeddingsOptions> CreateConcatEmbeddingsOptionsDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t num_channels = 0,
    const std::vector<int32_t> *num_columns_per_channel = nullptr,
    const std::vector<int32_t> *embedding_dim_per_channel = nullptr) {
  auto num_columns_per_channel__ = num_columns_per_channel ? _fbb.CreateVector<int32_t>(*num_columns_per_channel) : 0;
  auto embedding_dim_per_channel__ = embedding_dim_per_channel ? _fbb.CreateVector<int32_t>(*embedding_dim_per_channel) : 0;
  return opencv_tflite::CreateConcatEmbeddingsOptions(
      _fbb,
      num_channels,
      num_columns_per_channel__,
      embedding_dim_per_channel__);
}

struct LSHProjectionOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef LSHProjectionOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TYPE = 4
  };
  opencv_tflite::LSHProjectionType type() const {
    return static_cast<opencv_tflite::LSHProjectionType>(GetField<int8_t>(VT_TYPE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_TYPE, 1) &&
           verifier.EndTable();
  }
};

struct LSHProjectionOptionsBuilder {
  typedef LSHProjectionOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_type(opencv_tflite::LSHProjectionType type) {
    fbb_.AddElement<int8_t>(LSHProjectionOptions::VT_TYPE, static_cast<int8_t>(type), 0);
  }
  explicit LSHProjectionOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<LSHProjectionOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<LSHProjectionOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<LSHProjectionOptions> CreateLSHProjectionOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::LSHProjectionType type = opencv_tflite::LSHProjectionType_UNKNOWN) {
  LSHProjectionOptionsBuilder builder_(_fbb);
  builder_.add_type(type);
  return builder_.Finish();
}

struct SVDFOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SVDFOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_RANK = 4,
    VT_FUSED_ACTIVATION_FUNCTION = 6,
    VT_ASYMMETRIC_QUANTIZE_INPUTS = 8
  };
  int32_t rank() const {
    return GetField<int32_t>(VT_RANK, 0);
  }
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  bool asymmetric_quantize_inputs() const {
    return GetField<uint8_t>(VT_ASYMMETRIC_QUANTIZE_INPUTS, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_RANK, 4) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           VerifyField<uint8_t>(verifier, VT_ASYMMETRIC_QUANTIZE_INPUTS, 1) &&
           verifier.EndTable();
  }
};

struct SVDFOptionsBuilder {
  typedef SVDFOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_rank(int32_t rank) {
    fbb_.AddElement<int32_t>(SVDFOptions::VT_RANK, rank, 0);
  }
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(SVDFOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  void add_asymmetric_quantize_inputs(bool asymmetric_quantize_inputs) {
    fbb_.AddElement<uint8_t>(SVDFOptions::VT_ASYMMETRIC_QUANTIZE_INPUTS, static_cast<uint8_t>(asymmetric_quantize_inputs), 0);
  }
  explicit SVDFOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SVDFOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SVDFOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SVDFOptions> CreateSVDFOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t rank = 0,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE,
    bool asymmetric_quantize_inputs = false) {
  SVDFOptionsBuilder builder_(_fbb);
  builder_.add_rank(rank);
  builder_.add_asymmetric_quantize_inputs(asymmetric_quantize_inputs);
  builder_.add_fused_activation_function(fused_activation_function);
  return builder_.Finish();
}

struct RNNOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef RNNOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FUSED_ACTIVATION_FUNCTION = 4,
    VT_ASYMMETRIC_QUANTIZE_INPUTS = 6
  };
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  bool asymmetric_quantize_inputs() const {
    return GetField<uint8_t>(VT_ASYMMETRIC_QUANTIZE_INPUTS, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           VerifyField<uint8_t>(verifier, VT_ASYMMETRIC_QUANTIZE_INPUTS, 1) &&
           verifier.EndTable();
  }
};

struct RNNOptionsBuilder {
  typedef RNNOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(RNNOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  void add_asymmetric_quantize_inputs(bool asymmetric_quantize_inputs) {
    fbb_.AddElement<uint8_t>(RNNOptions::VT_ASYMMETRIC_QUANTIZE_INPUTS, static_cast<uint8_t>(asymmetric_quantize_inputs), 0);
  }
  explicit RNNOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<RNNOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<RNNOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<RNNOptions> CreateRNNOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE,
    bool asymmetric_quantize_inputs = false) {
  RNNOptionsBuilder builder_(_fbb);
  builder_.add_asymmetric_quantize_inputs(asymmetric_quantize_inputs);
  builder_.add_fused_activation_function(fused_activation_function);
  return builder_.Finish();
}

struct SequenceRNNOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SequenceRNNOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TIME_MAJOR = 4,
    VT_FUSED_ACTIVATION_FUNCTION = 6,
    VT_ASYMMETRIC_QUANTIZE_INPUTS = 8
  };
  bool time_major() const {
    return GetField<uint8_t>(VT_TIME_MAJOR, 0) != 0;
  }
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  bool asymmetric_quantize_inputs() const {
    return GetField<uint8_t>(VT_ASYMMETRIC_QUANTIZE_INPUTS, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_TIME_MAJOR, 1) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           VerifyField<uint8_t>(verifier, VT_ASYMMETRIC_QUANTIZE_INPUTS, 1) &&
           verifier.EndTable();
  }
};

struct SequenceRNNOptionsBuilder {
  typedef SequenceRNNOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_time_major(bool time_major) {
    fbb_.AddElement<uint8_t>(SequenceRNNOptions::VT_TIME_MAJOR, static_cast<uint8_t>(time_major), 0);
  }
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(SequenceRNNOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  void add_asymmetric_quantize_inputs(bool asymmetric_quantize_inputs) {
    fbb_.AddElement<uint8_t>(SequenceRNNOptions::VT_ASYMMETRIC_QUANTIZE_INPUTS, static_cast<uint8_t>(asymmetric_quantize_inputs), 0);
  }
  explicit SequenceRNNOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SequenceRNNOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SequenceRNNOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SequenceRNNOptions> CreateSequenceRNNOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool time_major = false,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE,
    bool asymmetric_quantize_inputs = false) {
  SequenceRNNOptionsBuilder builder_(_fbb);
  builder_.add_asymmetric_quantize_inputs(asymmetric_quantize_inputs);
  builder_.add_fused_activation_function(fused_activation_function);
  builder_.add_time_major(time_major);
  return builder_.Finish();
}

struct BidirectionalSequenceRNNOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef BidirectionalSequenceRNNOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TIME_MAJOR = 4,
    VT_FUSED_ACTIVATION_FUNCTION = 6,
    VT_MERGE_OUTPUTS = 8,
    VT_ASYMMETRIC_QUANTIZE_INPUTS = 10
  };
  bool time_major() const {
    return GetField<uint8_t>(VT_TIME_MAJOR, 0) != 0;
  }
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  bool merge_outputs() const {
    return GetField<uint8_t>(VT_MERGE_OUTPUTS, 0) != 0;
  }
  bool asymmetric_quantize_inputs() const {
    return GetField<uint8_t>(VT_ASYMMETRIC_QUANTIZE_INPUTS, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_TIME_MAJOR, 1) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           VerifyField<uint8_t>(verifier, VT_MERGE_OUTPUTS, 1) &&
           VerifyField<uint8_t>(verifier, VT_ASYMMETRIC_QUANTIZE_INPUTS, 1) &&
           verifier.EndTable();
  }
};

struct BidirectionalSequenceRNNOptionsBuilder {
  typedef BidirectionalSequenceRNNOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_time_major(bool time_major) {
    fbb_.AddElement<uint8_t>(BidirectionalSequenceRNNOptions::VT_TIME_MAJOR, static_cast<uint8_t>(time_major), 0);
  }
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(BidirectionalSequenceRNNOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  void add_merge_outputs(bool merge_outputs) {
    fbb_.AddElement<uint8_t>(BidirectionalSequenceRNNOptions::VT_MERGE_OUTPUTS, static_cast<uint8_t>(merge_outputs), 0);
  }
  void add_asymmetric_quantize_inputs(bool asymmetric_quantize_inputs) {
    fbb_.AddElement<uint8_t>(BidirectionalSequenceRNNOptions::VT_ASYMMETRIC_QUANTIZE_INPUTS, static_cast<uint8_t>(asymmetric_quantize_inputs), 0);
  }
  explicit BidirectionalSequenceRNNOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<BidirectionalSequenceRNNOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<BidirectionalSequenceRNNOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<BidirectionalSequenceRNNOptions> CreateBidirectionalSequenceRNNOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool time_major = false,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE,
    bool merge_outputs = false,
    bool asymmetric_quantize_inputs = false) {
  BidirectionalSequenceRNNOptionsBuilder builder_(_fbb);
  builder_.add_asymmetric_quantize_inputs(asymmetric_quantize_inputs);
  builder_.add_merge_outputs(merge_outputs);
  builder_.add_fused_activation_function(fused_activation_function);
  builder_.add_time_major(time_major);
  return builder_.Finish();
}

struct FullyConnectedOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef FullyConnectedOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FUSED_ACTIVATION_FUNCTION = 4,
    VT_WEIGHTS_FORMAT = 6,
    VT_KEEP_NUM_DIMS = 8,
    VT_ASYMMETRIC_QUANTIZE_INPUTS = 10
  };
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  opencv_tflite::FullyConnectedOptionsWeightsFormat weights_format() const {
    return static_cast<opencv_tflite::FullyConnectedOptionsWeightsFormat>(GetField<int8_t>(VT_WEIGHTS_FORMAT, 0));
  }
  bool keep_num_dims() const {
    return GetField<uint8_t>(VT_KEEP_NUM_DIMS, 0) != 0;
  }
  bool asymmetric_quantize_inputs() const {
    return GetField<uint8_t>(VT_ASYMMETRIC_QUANTIZE_INPUTS, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           VerifyField<int8_t>(verifier, VT_WEIGHTS_FORMAT, 1) &&
           VerifyField<uint8_t>(verifier, VT_KEEP_NUM_DIMS, 1) &&
           VerifyField<uint8_t>(verifier, VT_ASYMMETRIC_QUANTIZE_INPUTS, 1) &&
           verifier.EndTable();
  }
};

struct FullyConnectedOptionsBuilder {
  typedef FullyConnectedOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(FullyConnectedOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  void add_weights_format(opencv_tflite::FullyConnectedOptionsWeightsFormat weights_format) {
    fbb_.AddElement<int8_t>(FullyConnectedOptions::VT_WEIGHTS_FORMAT, static_cast<int8_t>(weights_format), 0);
  }
  void add_keep_num_dims(bool keep_num_dims) {
    fbb_.AddElement<uint8_t>(FullyConnectedOptions::VT_KEEP_NUM_DIMS, static_cast<uint8_t>(keep_num_dims), 0);
  }
  void add_asymmetric_quantize_inputs(bool asymmetric_quantize_inputs) {
    fbb_.AddElement<uint8_t>(FullyConnectedOptions::VT_ASYMMETRIC_QUANTIZE_INPUTS, static_cast<uint8_t>(asymmetric_quantize_inputs), 0);
  }
  explicit FullyConnectedOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<FullyConnectedOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<FullyConnectedOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<FullyConnectedOptions> CreateFullyConnectedOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE,
    opencv_tflite::FullyConnectedOptionsWeightsFormat weights_format = opencv_tflite::FullyConnectedOptionsWeightsFormat_DEFAULT,
    bool keep_num_dims = false,
    bool asymmetric_quantize_inputs = false) {
  FullyConnectedOptionsBuilder builder_(_fbb);
  builder_.add_asymmetric_quantize_inputs(asymmetric_quantize_inputs);
  builder_.add_keep_num_dims(keep_num_dims);
  builder_.add_weights_format(weights_format);
  builder_.add_fused_activation_function(fused_activation_function);
  return builder_.Finish();
}

struct SoftmaxOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SoftmaxOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_BETA = 4
  };
  float beta() const {
    return GetField<float>(VT_BETA, 0.0f);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_BETA, 4) &&
           verifier.EndTable();
  }
};

struct SoftmaxOptionsBuilder {
  typedef SoftmaxOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_beta(float beta) {
    fbb_.AddElement<float>(SoftmaxOptions::VT_BETA, beta, 0.0f);
  }
  explicit SoftmaxOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SoftmaxOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SoftmaxOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SoftmaxOptions> CreateSoftmaxOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    float beta = 0.0f) {
  SoftmaxOptionsBuilder builder_(_fbb);
  builder_.add_beta(beta);
  return builder_.Finish();
}

struct ConcatenationOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ConcatenationOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_AXIS = 4,
    VT_FUSED_ACTIVATION_FUNCTION = 6
  };
  int32_t axis() const {
    return GetField<int32_t>(VT_AXIS, 0);
  }
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_AXIS, 4) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           verifier.EndTable();
  }
};

struct ConcatenationOptionsBuilder {
  typedef ConcatenationOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_axis(int32_t axis) {
    fbb_.AddElement<int32_t>(ConcatenationOptions::VT_AXIS, axis, 0);
  }
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(ConcatenationOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  explicit ConcatenationOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ConcatenationOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ConcatenationOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ConcatenationOptions> CreateConcatenationOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t axis = 0,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE) {
  ConcatenationOptionsBuilder builder_(_fbb);
  builder_.add_axis(axis);
  builder_.add_fused_activation_function(fused_activation_function);
  return builder_.Finish();
}

struct AddOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef AddOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FUSED_ACTIVATION_FUNCTION = 4,
    VT_POT_SCALE_INT16 = 6
  };
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  bool pot_scale_int16() const {
    return GetField<uint8_t>(VT_POT_SCALE_INT16, 1) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           VerifyField<uint8_t>(verifier, VT_POT_SCALE_INT16, 1) &&
           verifier.EndTable();
  }
};

struct AddOptionsBuilder {
  typedef AddOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(AddOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  void add_pot_scale_int16(bool pot_scale_int16) {
    fbb_.AddElement<uint8_t>(AddOptions::VT_POT_SCALE_INT16, static_cast<uint8_t>(pot_scale_int16), 1);
  }
  explicit AddOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<AddOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<AddOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<AddOptions> CreateAddOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE,
    bool pot_scale_int16 = true) {
  AddOptionsBuilder builder_(_fbb);
  builder_.add_pot_scale_int16(pot_scale_int16);
  builder_.add_fused_activation_function(fused_activation_function);
  return builder_.Finish();
}

struct MulOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MulOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FUSED_ACTIVATION_FUNCTION = 4
  };
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           verifier.EndTable();
  }
};

struct MulOptionsBuilder {
  typedef MulOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(MulOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  explicit MulOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MulOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MulOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MulOptions> CreateMulOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE) {
  MulOptionsBuilder builder_(_fbb);
  builder_.add_fused_activation_function(fused_activation_function);
  return builder_.Finish();
}

struct L2NormOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef L2NormOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FUSED_ACTIVATION_FUNCTION = 4
  };
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           verifier.EndTable();
  }
};

struct L2NormOptionsBuilder {
  typedef L2NormOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(L2NormOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  explicit L2NormOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<L2NormOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<L2NormOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<L2NormOptions> CreateL2NormOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE) {
  L2NormOptionsBuilder builder_(_fbb);
  builder_.add_fused_activation_function(fused_activation_function);
  return builder_.Finish();
}

struct LocalResponseNormalizationOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef LocalResponseNormalizationOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_RADIUS = 4,
    VT_BIAS = 6,
    VT_ALPHA = 8,
    VT_BETA = 10
  };
  int32_t radius() const {
    return GetField<int32_t>(VT_RADIUS, 0);
  }
  float bias() const {
    return GetField<float>(VT_BIAS, 0.0f);
  }
  float alpha() const {
    return GetField<float>(VT_ALPHA, 0.0f);
  }
  float beta() const {
    return GetField<float>(VT_BETA, 0.0f);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_RADIUS, 4) &&
           VerifyField<float>(verifier, VT_BIAS, 4) &&
           VerifyField<float>(verifier, VT_ALPHA, 4) &&
           VerifyField<float>(verifier, VT_BETA, 4) &&
           verifier.EndTable();
  }
};

struct LocalResponseNormalizationOptionsBuilder {
  typedef LocalResponseNormalizationOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_radius(int32_t radius) {
    fbb_.AddElement<int32_t>(LocalResponseNormalizationOptions::VT_RADIUS, radius, 0);
  }
  void add_bias(float bias) {
    fbb_.AddElement<float>(LocalResponseNormalizationOptions::VT_BIAS, bias, 0.0f);
  }
  void add_alpha(float alpha) {
    fbb_.AddElement<float>(LocalResponseNormalizationOptions::VT_ALPHA, alpha, 0.0f);
  }
  void add_beta(float beta) {
    fbb_.AddElement<float>(LocalResponseNormalizationOptions::VT_BETA, beta, 0.0f);
  }
  explicit LocalResponseNormalizationOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<LocalResponseNormalizationOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<LocalResponseNormalizationOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<LocalResponseNormalizationOptions> CreateLocalResponseNormalizationOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t radius = 0,
    float bias = 0.0f,
    float alpha = 0.0f,
    float beta = 0.0f) {
  LocalResponseNormalizationOptionsBuilder builder_(_fbb);
  builder_.add_beta(beta);
  builder_.add_alpha(alpha);
  builder_.add_bias(bias);
  builder_.add_radius(radius);
  return builder_.Finish();
}

struct LSTMOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef LSTMOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FUSED_ACTIVATION_FUNCTION = 4,
    VT_CELL_CLIP = 6,
    VT_PROJ_CLIP = 8,
    VT_KERNEL_TYPE = 10,
    VT_ASYMMETRIC_QUANTIZE_INPUTS = 12
  };
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  float cell_clip() const {
    return GetField<float>(VT_CELL_CLIP, 0.0f);
  }
  float proj_clip() const {
    return GetField<float>(VT_PROJ_CLIP, 0.0f);
  }
  opencv_tflite::LSTMKernelType kernel_type() const {
    return static_cast<opencv_tflite::LSTMKernelType>(GetField<int8_t>(VT_KERNEL_TYPE, 0));
  }
  bool asymmetric_quantize_inputs() const {
    return GetField<uint8_t>(VT_ASYMMETRIC_QUANTIZE_INPUTS, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           VerifyField<float>(verifier, VT_CELL_CLIP, 4) &&
           VerifyField<float>(verifier, VT_PROJ_CLIP, 4) &&
           VerifyField<int8_t>(verifier, VT_KERNEL_TYPE, 1) &&
           VerifyField<uint8_t>(verifier, VT_ASYMMETRIC_QUANTIZE_INPUTS, 1) &&
           verifier.EndTable();
  }
};

struct LSTMOptionsBuilder {
  typedef LSTMOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(LSTMOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  void add_cell_clip(float cell_clip) {
    fbb_.AddElement<float>(LSTMOptions::VT_CELL_CLIP, cell_clip, 0.0f);
  }
  void add_proj_clip(float proj_clip) {
    fbb_.AddElement<float>(LSTMOptions::VT_PROJ_CLIP, proj_clip, 0.0f);
  }
  void add_kernel_type(opencv_tflite::LSTMKernelType kernel_type) {
    fbb_.AddElement<int8_t>(LSTMOptions::VT_KERNEL_TYPE, static_cast<int8_t>(kernel_type), 0);
  }
  void add_asymmetric_quantize_inputs(bool asymmetric_quantize_inputs) {
    fbb_.AddElement<uint8_t>(LSTMOptions::VT_ASYMMETRIC_QUANTIZE_INPUTS, static_cast<uint8_t>(asymmetric_quantize_inputs), 0);
  }
  explicit LSTMOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<LSTMOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<LSTMOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<LSTMOptions> CreateLSTMOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE,
    float cell_clip = 0.0f,
    float proj_clip = 0.0f,
    opencv_tflite::LSTMKernelType kernel_type = opencv_tflite::LSTMKernelType_FULL,
    bool asymmetric_quantize_inputs = false) {
  LSTMOptionsBuilder builder_(_fbb);
  builder_.add_proj_clip(proj_clip);
  builder_.add_cell_clip(cell_clip);
  builder_.add_asymmetric_quantize_inputs(asymmetric_quantize_inputs);
  builder_.add_kernel_type(kernel_type);
  builder_.add_fused_activation_function(fused_activation_function);
  return builder_.Finish();
}

struct UnidirectionalSequenceLSTMOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef UnidirectionalSequenceLSTMOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FUSED_ACTIVATION_FUNCTION = 4,
    VT_CELL_CLIP = 6,
    VT_PROJ_CLIP = 8,
    VT_TIME_MAJOR = 10,
    VT_ASYMMETRIC_QUANTIZE_INPUTS = 12,
    VT_DIAGONAL_RECURRENT_TENSORS = 14
  };
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  float cell_clip() const {
    return GetField<float>(VT_CELL_CLIP, 0.0f);
  }
  float proj_clip() const {
    return GetField<float>(VT_PROJ_CLIP, 0.0f);
  }
  bool time_major() const {
    return GetField<uint8_t>(VT_TIME_MAJOR, 0) != 0;
  }
  bool asymmetric_quantize_inputs() const {
    return GetField<uint8_t>(VT_ASYMMETRIC_QUANTIZE_INPUTS, 0) != 0;
  }
  bool diagonal_recurrent_tensors() const {
    return GetField<uint8_t>(VT_DIAGONAL_RECURRENT_TENSORS, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           VerifyField<float>(verifier, VT_CELL_CLIP, 4) &&
           VerifyField<float>(verifier, VT_PROJ_CLIP, 4) &&
           VerifyField<uint8_t>(verifier, VT_TIME_MAJOR, 1) &&
           VerifyField<uint8_t>(verifier, VT_ASYMMETRIC_QUANTIZE_INPUTS, 1) &&
           VerifyField<uint8_t>(verifier, VT_DIAGONAL_RECURRENT_TENSORS, 1) &&
           verifier.EndTable();
  }
};

struct UnidirectionalSequenceLSTMOptionsBuilder {
  typedef UnidirectionalSequenceLSTMOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(UnidirectionalSequenceLSTMOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  void add_cell_clip(float cell_clip) {
    fbb_.AddElement<float>(UnidirectionalSequenceLSTMOptions::VT_CELL_CLIP, cell_clip, 0.0f);
  }
  void add_proj_clip(float proj_clip) {
    fbb_.AddElement<float>(UnidirectionalSequenceLSTMOptions::VT_PROJ_CLIP, proj_clip, 0.0f);
  }
  void add_time_major(bool time_major) {
    fbb_.AddElement<uint8_t>(UnidirectionalSequenceLSTMOptions::VT_TIME_MAJOR, static_cast<uint8_t>(time_major), 0);
  }
  void add_asymmetric_quantize_inputs(bool asymmetric_quantize_inputs) {
    fbb_.AddElement<uint8_t>(UnidirectionalSequenceLSTMOptions::VT_ASYMMETRIC_QUANTIZE_INPUTS, static_cast<uint8_t>(asymmetric_quantize_inputs), 0);
  }
  void add_diagonal_recurrent_tensors(bool diagonal_recurrent_tensors) {
    fbb_.AddElement<uint8_t>(UnidirectionalSequenceLSTMOptions::VT_DIAGONAL_RECURRENT_TENSORS, static_cast<uint8_t>(diagonal_recurrent_tensors), 0);
  }
  explicit UnidirectionalSequenceLSTMOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<UnidirectionalSequenceLSTMOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<UnidirectionalSequenceLSTMOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<UnidirectionalSequenceLSTMOptions> CreateUnidirectionalSequenceLSTMOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE,
    float cell_clip = 0.0f,
    float proj_clip = 0.0f,
    bool time_major = false,
    bool asymmetric_quantize_inputs = false,
    bool diagonal_recurrent_tensors = false) {
  UnidirectionalSequenceLSTMOptionsBuilder builder_(_fbb);
  builder_.add_proj_clip(proj_clip);
  builder_.add_cell_clip(cell_clip);
  builder_.add_diagonal_recurrent_tensors(diagonal_recurrent_tensors);
  builder_.add_asymmetric_quantize_inputs(asymmetric_quantize_inputs);
  builder_.add_time_major(time_major);
  builder_.add_fused_activation_function(fused_activation_function);
  return builder_.Finish();
}

struct BidirectionalSequenceLSTMOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef BidirectionalSequenceLSTMOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FUSED_ACTIVATION_FUNCTION = 4,
    VT_CELL_CLIP = 6,
    VT_PROJ_CLIP = 8,
    VT_MERGE_OUTPUTS = 10,
    VT_TIME_MAJOR = 12,
    VT_ASYMMETRIC_QUANTIZE_INPUTS = 14
  };
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  float cell_clip() const {
    return GetField<float>(VT_CELL_CLIP, 0.0f);
  }
  float proj_clip() const {
    return GetField<float>(VT_PROJ_CLIP, 0.0f);
  }
  bool merge_outputs() const {
    return GetField<uint8_t>(VT_MERGE_OUTPUTS, 0) != 0;
  }
  bool time_major() const {
    return GetField<uint8_t>(VT_TIME_MAJOR, 1) != 0;
  }
  bool asymmetric_quantize_inputs() const {
    return GetField<uint8_t>(VT_ASYMMETRIC_QUANTIZE_INPUTS, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           VerifyField<float>(verifier, VT_CELL_CLIP, 4) &&
           VerifyField<float>(verifier, VT_PROJ_CLIP, 4) &&
           VerifyField<uint8_t>(verifier, VT_MERGE_OUTPUTS, 1) &&
           VerifyField<uint8_t>(verifier, VT_TIME_MAJOR, 1) &&
           VerifyField<uint8_t>(verifier, VT_ASYMMETRIC_QUANTIZE_INPUTS, 1) &&
           verifier.EndTable();
  }
};

struct BidirectionalSequenceLSTMOptionsBuilder {
  typedef BidirectionalSequenceLSTMOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(BidirectionalSequenceLSTMOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  void add_cell_clip(float cell_clip) {
    fbb_.AddElement<float>(BidirectionalSequenceLSTMOptions::VT_CELL_CLIP, cell_clip, 0.0f);
  }
  void add_proj_clip(float proj_clip) {
    fbb_.AddElement<float>(BidirectionalSequenceLSTMOptions::VT_PROJ_CLIP, proj_clip, 0.0f);
  }
  void add_merge_outputs(bool merge_outputs) {
    fbb_.AddElement<uint8_t>(BidirectionalSequenceLSTMOptions::VT_MERGE_OUTPUTS, static_cast<uint8_t>(merge_outputs), 0);
  }
  void add_time_major(bool time_major) {
    fbb_.AddElement<uint8_t>(BidirectionalSequenceLSTMOptions::VT_TIME_MAJOR, static_cast<uint8_t>(time_major), 1);
  }
  void add_asymmetric_quantize_inputs(bool asymmetric_quantize_inputs) {
    fbb_.AddElement<uint8_t>(BidirectionalSequenceLSTMOptions::VT_ASYMMETRIC_QUANTIZE_INPUTS, static_cast<uint8_t>(asymmetric_quantize_inputs), 0);
  }
  explicit BidirectionalSequenceLSTMOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<BidirectionalSequenceLSTMOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<BidirectionalSequenceLSTMOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<BidirectionalSequenceLSTMOptions> CreateBidirectionalSequenceLSTMOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE,
    float cell_clip = 0.0f,
    float proj_clip = 0.0f,
    bool merge_outputs = false,
    bool time_major = true,
    bool asymmetric_quantize_inputs = false) {
  BidirectionalSequenceLSTMOptionsBuilder builder_(_fbb);
  builder_.add_proj_clip(proj_clip);
  builder_.add_cell_clip(cell_clip);
  builder_.add_asymmetric_quantize_inputs(asymmetric_quantize_inputs);
  builder_.add_time_major(time_major);
  builder_.add_merge_outputs(merge_outputs);
  builder_.add_fused_activation_function(fused_activation_function);
  return builder_.Finish();
}

struct ResizeBilinearOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ResizeBilinearOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ALIGN_CORNERS = 8,
    VT_HALF_PIXEL_CENTERS = 10
  };
  bool align_corners() const {
    return GetField<uint8_t>(VT_ALIGN_CORNERS, 0) != 0;
  }
  bool half_pixel_centers() const {
    return GetField<uint8_t>(VT_HALF_PIXEL_CENTERS, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_ALIGN_CORNERS, 1) &&
           VerifyField<uint8_t>(verifier, VT_HALF_PIXEL_CENTERS, 1) &&
           verifier.EndTable();
  }
};

struct ResizeBilinearOptionsBuilder {
  typedef ResizeBilinearOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_align_corners(bool align_corners) {
    fbb_.AddElement<uint8_t>(ResizeBilinearOptions::VT_ALIGN_CORNERS, static_cast<uint8_t>(align_corners), 0);
  }
  void add_half_pixel_centers(bool half_pixel_centers) {
    fbb_.AddElement<uint8_t>(ResizeBilinearOptions::VT_HALF_PIXEL_CENTERS, static_cast<uint8_t>(half_pixel_centers), 0);
  }
  explicit ResizeBilinearOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ResizeBilinearOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ResizeBilinearOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ResizeBilinearOptions> CreateResizeBilinearOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool align_corners = false,
    bool half_pixel_centers = false) {
  ResizeBilinearOptionsBuilder builder_(_fbb);
  builder_.add_half_pixel_centers(half_pixel_centers);
  builder_.add_align_corners(align_corners);
  return builder_.Finish();
}

struct ResizeNearestNeighborOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ResizeNearestNeighborOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ALIGN_CORNERS = 4,
    VT_HALF_PIXEL_CENTERS = 6
  };
  bool align_corners() const {
    return GetField<uint8_t>(VT_ALIGN_CORNERS, 0) != 0;
  }
  bool half_pixel_centers() const {
    return GetField<uint8_t>(VT_HALF_PIXEL_CENTERS, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_ALIGN_CORNERS, 1) &&
           VerifyField<uint8_t>(verifier, VT_HALF_PIXEL_CENTERS, 1) &&
           verifier.EndTable();
  }
};

struct ResizeNearestNeighborOptionsBuilder {
  typedef ResizeNearestNeighborOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_align_corners(bool align_corners) {
    fbb_.AddElement<uint8_t>(ResizeNearestNeighborOptions::VT_ALIGN_CORNERS, static_cast<uint8_t>(align_corners), 0);
  }
  void add_half_pixel_centers(bool half_pixel_centers) {
    fbb_.AddElement<uint8_t>(ResizeNearestNeighborOptions::VT_HALF_PIXEL_CENTERS, static_cast<uint8_t>(half_pixel_centers), 0);
  }
  explicit ResizeNearestNeighborOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ResizeNearestNeighborOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ResizeNearestNeighborOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ResizeNearestNeighborOptions> CreateResizeNearestNeighborOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool align_corners = false,
    bool half_pixel_centers = false) {
  ResizeNearestNeighborOptionsBuilder builder_(_fbb);
  builder_.add_half_pixel_centers(half_pixel_centers);
  builder_.add_align_corners(align_corners);
  return builder_.Finish();
}

struct CallOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef CallOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SUBGRAPH = 4
  };
  uint32_t subgraph() const {
    return GetField<uint32_t>(VT_SUBGRAPH, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_SUBGRAPH, 4) &&
           verifier.EndTable();
  }
};

struct CallOptionsBuilder {
  typedef CallOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_subgraph(uint32_t subgraph) {
    fbb_.AddElement<uint32_t>(CallOptions::VT_SUBGRAPH, subgraph, 0);
  }
  explicit CallOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<CallOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<CallOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<CallOptions> CreateCallOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t subgraph = 0) {
  CallOptionsBuilder builder_(_fbb);
  builder_.add_subgraph(subgraph);
  return builder_.Finish();
}

struct PadOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef PadOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct PadOptionsBuilder {
  typedef PadOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit PadOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<PadOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<PadOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<PadOptions> CreatePadOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  PadOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct PadV2Options FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef PadV2OptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct PadV2OptionsBuilder {
  typedef PadV2Options Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit PadV2OptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<PadV2Options> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<PadV2Options>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<PadV2Options> CreatePadV2Options(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  PadV2OptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct ReshapeOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ReshapeOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NEW_SHAPE = 4
  };
  const ::flatbuffers::Vector<int32_t> *new_shape() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_NEW_SHAPE);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NEW_SHAPE) &&
           verifier.VerifyVector(new_shape()) &&
           verifier.EndTable();
  }
};

struct ReshapeOptionsBuilder {
  typedef ReshapeOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_new_shape(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> new_shape) {
    fbb_.AddOffset(ReshapeOptions::VT_NEW_SHAPE, new_shape);
  }
  explicit ReshapeOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ReshapeOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ReshapeOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ReshapeOptions> CreateReshapeOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> new_shape = 0) {
  ReshapeOptionsBuilder builder_(_fbb);
  builder_.add_new_shape(new_shape);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<ReshapeOptions> CreateReshapeOptionsDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<int32_t> *new_shape = nullptr) {
  auto new_shape__ = new_shape ? _fbb.CreateVector<int32_t>(*new_shape) : 0;
  return opencv_tflite::CreateReshapeOptions(
      _fbb,
      new_shape__);
}

struct SpaceToBatchNDOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SpaceToBatchNDOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct SpaceToBatchNDOptionsBuilder {
  typedef SpaceToBatchNDOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit SpaceToBatchNDOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SpaceToBatchNDOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SpaceToBatchNDOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SpaceToBatchNDOptions> CreateSpaceToBatchNDOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  SpaceToBatchNDOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct BatchToSpaceNDOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef BatchToSpaceNDOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct BatchToSpaceNDOptionsBuilder {
  typedef BatchToSpaceNDOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit BatchToSpaceNDOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<BatchToSpaceNDOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<BatchToSpaceNDOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<BatchToSpaceNDOptions> CreateBatchToSpaceNDOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  BatchToSpaceNDOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct SkipGramOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SkipGramOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NGRAM_SIZE = 4,
    VT_MAX_SKIP_SIZE = 6,
    VT_INCLUDE_ALL_NGRAMS = 8
  };
  int32_t ngram_size() const {
    return GetField<int32_t>(VT_NGRAM_SIZE, 0);
  }
  int32_t max_skip_size() const {
    return GetField<int32_t>(VT_MAX_SKIP_SIZE, 0);
  }
  bool include_all_ngrams() const {
    return GetField<uint8_t>(VT_INCLUDE_ALL_NGRAMS, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_NGRAM_SIZE, 4) &&
           VerifyField<int32_t>(verifier, VT_MAX_SKIP_SIZE, 4) &&
           VerifyField<uint8_t>(verifier, VT_INCLUDE_ALL_NGRAMS, 1) &&
           verifier.EndTable();
  }
};

struct SkipGramOptionsBuilder {
  typedef SkipGramOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_ngram_size(int32_t ngram_size) {
    fbb_.AddElement<int32_t>(SkipGramOptions::VT_NGRAM_SIZE, ngram_size, 0);
  }
  void add_max_skip_size(int32_t max_skip_size) {
    fbb_.AddElement<int32_t>(SkipGramOptions::VT_MAX_SKIP_SIZE, max_skip_size, 0);
  }
  void add_include_all_ngrams(bool include_all_ngrams) {
    fbb_.AddElement<uint8_t>(SkipGramOptions::VT_INCLUDE_ALL_NGRAMS, static_cast<uint8_t>(include_all_ngrams), 0);
  }
  explicit SkipGramOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SkipGramOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SkipGramOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SkipGramOptions> CreateSkipGramOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t ngram_size = 0,
    int32_t max_skip_size = 0,
    bool include_all_ngrams = false) {
  SkipGramOptionsBuilder builder_(_fbb);
  builder_.add_max_skip_size(max_skip_size);
  builder_.add_ngram_size(ngram_size);
  builder_.add_include_all_ngrams(include_all_ngrams);
  return builder_.Finish();
}

struct SpaceToDepthOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SpaceToDepthOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_BLOCK_SIZE = 4
  };
  int32_t block_size() const {
    return GetField<int32_t>(VT_BLOCK_SIZE, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_BLOCK_SIZE, 4) &&
           verifier.EndTable();
  }
};

struct SpaceToDepthOptionsBuilder {
  typedef SpaceToDepthOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_block_size(int32_t block_size) {
    fbb_.AddElement<int32_t>(SpaceToDepthOptions::VT_BLOCK_SIZE, block_size, 0);
  }
  explicit SpaceToDepthOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SpaceToDepthOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SpaceToDepthOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SpaceToDepthOptions> CreateSpaceToDepthOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t block_size = 0) {
  SpaceToDepthOptionsBuilder builder_(_fbb);
  builder_.add_block_size(block_size);
  return builder_.Finish();
}

struct DepthToSpaceOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DepthToSpaceOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_BLOCK_SIZE = 4
  };
  int32_t block_size() const {
    return GetField<int32_t>(VT_BLOCK_SIZE, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_BLOCK_SIZE, 4) &&
           verifier.EndTable();
  }
};

struct DepthToSpaceOptionsBuilder {
  typedef DepthToSpaceOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_block_size(int32_t block_size) {
    fbb_.AddElement<int32_t>(DepthToSpaceOptions::VT_BLOCK_SIZE, block_size, 0);
  }
  explicit DepthToSpaceOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DepthToSpaceOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DepthToSpaceOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DepthToSpaceOptions> CreateDepthToSpaceOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t block_size = 0) {
  DepthToSpaceOptionsBuilder builder_(_fbb);
  builder_.add_block_size(block_size);
  return builder_.Finish();
}

struct SubOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SubOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FUSED_ACTIVATION_FUNCTION = 4,
    VT_POT_SCALE_INT16 = 6
  };
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  bool pot_scale_int16() const {
    return GetField<uint8_t>(VT_POT_SCALE_INT16, 1) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           VerifyField<uint8_t>(verifier, VT_POT_SCALE_INT16, 1) &&
           verifier.EndTable();
  }
};

struct SubOptionsBuilder {
  typedef SubOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(SubOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  void add_pot_scale_int16(bool pot_scale_int16) {
    fbb_.AddElement<uint8_t>(SubOptions::VT_POT_SCALE_INT16, static_cast<uint8_t>(pot_scale_int16), 1);
  }
  explicit SubOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SubOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SubOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SubOptions> CreateSubOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE,
    bool pot_scale_int16 = true) {
  SubOptionsBuilder builder_(_fbb);
  builder_.add_pot_scale_int16(pot_scale_int16);
  builder_.add_fused_activation_function(fused_activation_function);
  return builder_.Finish();
}

struct DivOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DivOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FUSED_ACTIVATION_FUNCTION = 4
  };
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           verifier.EndTable();
  }
};

struct DivOptionsBuilder {
  typedef DivOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(DivOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  explicit DivOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DivOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DivOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DivOptions> CreateDivOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE) {
  DivOptionsBuilder builder_(_fbb);
  builder_.add_fused_activation_function(fused_activation_function);
  return builder_.Finish();
}

struct TopKV2Options FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef TopKV2OptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct TopKV2OptionsBuilder {
  typedef TopKV2Options Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit TopKV2OptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<TopKV2Options> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<TopKV2Options>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<TopKV2Options> CreateTopKV2Options(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  TopKV2OptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct EmbeddingLookupSparseOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef EmbeddingLookupSparseOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_COMBINER = 4
  };
  opencv_tflite::CombinerType combiner() const {
    return static_cast<opencv_tflite::CombinerType>(GetField<int8_t>(VT_COMBINER, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_COMBINER, 1) &&
           verifier.EndTable();
  }
};

struct EmbeddingLookupSparseOptionsBuilder {
  typedef EmbeddingLookupSparseOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_combiner(opencv_tflite::CombinerType combiner) {
    fbb_.AddElement<int8_t>(EmbeddingLookupSparseOptions::VT_COMBINER, static_cast<int8_t>(combiner), 0);
  }
  explicit EmbeddingLookupSparseOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<EmbeddingLookupSparseOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<EmbeddingLookupSparseOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<EmbeddingLookupSparseOptions> CreateEmbeddingLookupSparseOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::CombinerType combiner = opencv_tflite::CombinerType_SUM) {
  EmbeddingLookupSparseOptionsBuilder builder_(_fbb);
  builder_.add_combiner(combiner);
  return builder_.Finish();
}

struct GatherOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef GatherOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_AXIS = 4,
    VT_BATCH_DIMS = 6
  };
  int32_t axis() const {
    return GetField<int32_t>(VT_AXIS, 0);
  }
  int32_t batch_dims() const {
    return GetField<int32_t>(VT_BATCH_DIMS, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_AXIS, 4) &&
           VerifyField<int32_t>(verifier, VT_BATCH_DIMS, 4) &&
           verifier.EndTable();
  }
};

struct GatherOptionsBuilder {
  typedef GatherOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_axis(int32_t axis) {
    fbb_.AddElement<int32_t>(GatherOptions::VT_AXIS, axis, 0);
  }
  void add_batch_dims(int32_t batch_dims) {
    fbb_.AddElement<int32_t>(GatherOptions::VT_BATCH_DIMS, batch_dims, 0);
  }
  explicit GatherOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<GatherOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<GatherOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<GatherOptions> CreateGatherOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t axis = 0,
    int32_t batch_dims = 0) {
  GatherOptionsBuilder builder_(_fbb);
  builder_.add_batch_dims(batch_dims);
  builder_.add_axis(axis);
  return builder_.Finish();
}

struct TransposeOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef TransposeOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct TransposeOptionsBuilder {
  typedef TransposeOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit TransposeOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<TransposeOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<TransposeOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<TransposeOptions> CreateTransposeOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  TransposeOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct ExpOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ExpOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct ExpOptionsBuilder {
  typedef ExpOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit ExpOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ExpOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ExpOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ExpOptions> CreateExpOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  ExpOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct CosOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef CosOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct CosOptionsBuilder {
  typedef CosOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit CosOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<CosOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<CosOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<CosOptions> CreateCosOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  CosOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct ReducerOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ReducerOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_KEEP_DIMS = 4
  };
  bool keep_dims() const {
    return GetField<uint8_t>(VT_KEEP_DIMS, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_KEEP_DIMS, 1) &&
           verifier.EndTable();
  }
};

struct ReducerOptionsBuilder {
  typedef ReducerOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_keep_dims(bool keep_dims) {
    fbb_.AddElement<uint8_t>(ReducerOptions::VT_KEEP_DIMS, static_cast<uint8_t>(keep_dims), 0);
  }
  explicit ReducerOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ReducerOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ReducerOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ReducerOptions> CreateReducerOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool keep_dims = false) {
  ReducerOptionsBuilder builder_(_fbb);
  builder_.add_keep_dims(keep_dims);
  return builder_.Finish();
}

struct SqueezeOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SqueezeOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SQUEEZE_DIMS = 4
  };
  const ::flatbuffers::Vector<int32_t> *squeeze_dims() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_SQUEEZE_DIMS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_SQUEEZE_DIMS) &&
           verifier.VerifyVector(squeeze_dims()) &&
           verifier.EndTable();
  }
};

struct SqueezeOptionsBuilder {
  typedef SqueezeOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_squeeze_dims(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> squeeze_dims) {
    fbb_.AddOffset(SqueezeOptions::VT_SQUEEZE_DIMS, squeeze_dims);
  }
  explicit SqueezeOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SqueezeOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SqueezeOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SqueezeOptions> CreateSqueezeOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> squeeze_dims = 0) {
  SqueezeOptionsBuilder builder_(_fbb);
  builder_.add_squeeze_dims(squeeze_dims);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<SqueezeOptions> CreateSqueezeOptionsDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<int32_t> *squeeze_dims = nullptr) {
  auto squeeze_dims__ = squeeze_dims ? _fbb.CreateVector<int32_t>(*squeeze_dims) : 0;
  return opencv_tflite::CreateSqueezeOptions(
      _fbb,
      squeeze_dims__);
}

struct SplitOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SplitOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NUM_SPLITS = 4
  };
  int32_t num_splits() const {
    return GetField<int32_t>(VT_NUM_SPLITS, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_NUM_SPLITS, 4) &&
           verifier.EndTable();
  }
};

struct SplitOptionsBuilder {
  typedef SplitOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_num_splits(int32_t num_splits) {
    fbb_.AddElement<int32_t>(SplitOptions::VT_NUM_SPLITS, num_splits, 0);
  }
  explicit SplitOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SplitOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SplitOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SplitOptions> CreateSplitOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t num_splits = 0) {
  SplitOptionsBuilder builder_(_fbb);
  builder_.add_num_splits(num_splits);
  return builder_.Finish();
}

struct SplitVOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SplitVOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NUM_SPLITS = 4
  };
  int32_t num_splits() const {
    return GetField<int32_t>(VT_NUM_SPLITS, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_NUM_SPLITS, 4) &&
           verifier.EndTable();
  }
};

struct SplitVOptionsBuilder {
  typedef SplitVOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_num_splits(int32_t num_splits) {
    fbb_.AddElement<int32_t>(SplitVOptions::VT_NUM_SPLITS, num_splits, 0);
  }
  explicit SplitVOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SplitVOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SplitVOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SplitVOptions> CreateSplitVOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t num_splits = 0) {
  SplitVOptionsBuilder builder_(_fbb);
  builder_.add_num_splits(num_splits);
  return builder_.Finish();
}

struct StridedSliceOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef StridedSliceOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_BEGIN_MASK = 4,
    VT_END_MASK = 6,
    VT_ELLIPSIS_MASK = 8,
    VT_NEW_AXIS_MASK = 10,
    VT_SHRINK_AXIS_MASK = 12
  };
  int32_t begin_mask() const {
    return GetField<int32_t>(VT_BEGIN_MASK, 0);
  }
  int32_t end_mask() const {
    return GetField<int32_t>(VT_END_MASK, 0);
  }
  int32_t ellipsis_mask() const {
    return GetField<int32_t>(VT_ELLIPSIS_MASK, 0);
  }
  int32_t new_axis_mask() const {
    return GetField<int32_t>(VT_NEW_AXIS_MASK, 0);
  }
  int32_t shrink_axis_mask() const {
    return GetField<int32_t>(VT_SHRINK_AXIS_MASK, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_BEGIN_MASK, 4) &&
           VerifyField<int32_t>(verifier, VT_END_MASK, 4) &&
           VerifyField<int32_t>(verifier, VT_ELLIPSIS_MASK, 4) &&
           VerifyField<int32_t>(verifier, VT_NEW_AXIS_MASK, 4) &&
           VerifyField<int32_t>(verifier, VT_SHRINK_AXIS_MASK, 4) &&
           verifier.EndTable();
  }
};

struct StridedSliceOptionsBuilder {
  typedef StridedSliceOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_begin_mask(int32_t begin_mask) {
    fbb_.AddElement<int32_t>(StridedSliceOptions::VT_BEGIN_MASK, begin_mask, 0);
  }
  void add_end_mask(int32_t end_mask) {
    fbb_.AddElement<int32_t>(StridedSliceOptions::VT_END_MASK, end_mask, 0);
  }
  void add_ellipsis_mask(int32_t ellipsis_mask) {
    fbb_.AddElement<int32_t>(StridedSliceOptions::VT_ELLIPSIS_MASK, ellipsis_mask, 0);
  }
  void add_new_axis_mask(int32_t new_axis_mask) {
    fbb_.AddElement<int32_t>(StridedSliceOptions::VT_NEW_AXIS_MASK, new_axis_mask, 0);
  }
  void add_shrink_axis_mask(int32_t shrink_axis_mask) {
    fbb_.AddElement<int32_t>(StridedSliceOptions::VT_SHRINK_AXIS_MASK, shrink_axis_mask, 0);
  }
  explicit StridedSliceOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<StridedSliceOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<StridedSliceOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<StridedSliceOptions> CreateStridedSliceOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t begin_mask = 0,
    int32_t end_mask = 0,
    int32_t ellipsis_mask = 0,
    int32_t new_axis_mask = 0,
    int32_t shrink_axis_mask = 0) {
  StridedSliceOptionsBuilder builder_(_fbb);
  builder_.add_shrink_axis_mask(shrink_axis_mask);
  builder_.add_new_axis_mask(new_axis_mask);
  builder_.add_ellipsis_mask(ellipsis_mask);
  builder_.add_end_mask(end_mask);
  builder_.add_begin_mask(begin_mask);
  return builder_.Finish();
}

struct LogSoftmaxOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef LogSoftmaxOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct LogSoftmaxOptionsBuilder {
  typedef LogSoftmaxOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit LogSoftmaxOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<LogSoftmaxOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<LogSoftmaxOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<LogSoftmaxOptions> CreateLogSoftmaxOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  LogSoftmaxOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct CastOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef CastOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_IN_DATA_TYPE = 4,
    VT_OUT_DATA_TYPE = 6
  };
  opencv_tflite::TensorType in_data_type() const {
    return static_cast<opencv_tflite::TensorType>(GetField<int8_t>(VT_IN_DATA_TYPE, 0));
  }
  opencv_tflite::TensorType out_data_type() const {
    return static_cast<opencv_tflite::TensorType>(GetField<int8_t>(VT_OUT_DATA_TYPE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_IN_DATA_TYPE, 1) &&
           VerifyField<int8_t>(verifier, VT_OUT_DATA_TYPE, 1) &&
           verifier.EndTable();
  }
};

struct CastOptionsBuilder {
  typedef CastOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_in_data_type(opencv_tflite::TensorType in_data_type) {
    fbb_.AddElement<int8_t>(CastOptions::VT_IN_DATA_TYPE, static_cast<int8_t>(in_data_type), 0);
  }
  void add_out_data_type(opencv_tflite::TensorType out_data_type) {
    fbb_.AddElement<int8_t>(CastOptions::VT_OUT_DATA_TYPE, static_cast<int8_t>(out_data_type), 0);
  }
  explicit CastOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<CastOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<CastOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<CastOptions> CreateCastOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::TensorType in_data_type = opencv_tflite::TensorType_FLOAT32,
    opencv_tflite::TensorType out_data_type = opencv_tflite::TensorType_FLOAT32) {
  CastOptionsBuilder builder_(_fbb);
  builder_.add_out_data_type(out_data_type);
  builder_.add_in_data_type(in_data_type);
  return builder_.Finish();
}

struct DequantizeOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DequantizeOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct DequantizeOptionsBuilder {
  typedef DequantizeOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit DequantizeOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DequantizeOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DequantizeOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DequantizeOptions> CreateDequantizeOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  DequantizeOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct MaximumMinimumOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MaximumMinimumOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct MaximumMinimumOptionsBuilder {
  typedef MaximumMinimumOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit MaximumMinimumOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MaximumMinimumOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MaximumMinimumOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MaximumMinimumOptions> CreateMaximumMinimumOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  MaximumMinimumOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct TileOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef TileOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct TileOptionsBuilder {
  typedef TileOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit TileOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<TileOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<TileOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<TileOptions> CreateTileOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  TileOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct ArgMaxOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ArgMaxOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_OUTPUT_TYPE = 4
  };
  opencv_tflite::TensorType output_type() const {
    return static_cast<opencv_tflite::TensorType>(GetField<int8_t>(VT_OUTPUT_TYPE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_OUTPUT_TYPE, 1) &&
           verifier.EndTable();
  }
};

struct ArgMaxOptionsBuilder {
  typedef ArgMaxOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_output_type(opencv_tflite::TensorType output_type) {
    fbb_.AddElement<int8_t>(ArgMaxOptions::VT_OUTPUT_TYPE, static_cast<int8_t>(output_type), 0);
  }
  explicit ArgMaxOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ArgMaxOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ArgMaxOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ArgMaxOptions> CreateArgMaxOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::TensorType output_type = opencv_tflite::TensorType_FLOAT32) {
  ArgMaxOptionsBuilder builder_(_fbb);
  builder_.add_output_type(output_type);
  return builder_.Finish();
}

struct ArgMinOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ArgMinOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_OUTPUT_TYPE = 4
  };
  opencv_tflite::TensorType output_type() const {
    return static_cast<opencv_tflite::TensorType>(GetField<int8_t>(VT_OUTPUT_TYPE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_OUTPUT_TYPE, 1) &&
           verifier.EndTable();
  }
};

struct ArgMinOptionsBuilder {
  typedef ArgMinOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_output_type(opencv_tflite::TensorType output_type) {
    fbb_.AddElement<int8_t>(ArgMinOptions::VT_OUTPUT_TYPE, static_cast<int8_t>(output_type), 0);
  }
  explicit ArgMinOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ArgMinOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ArgMinOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ArgMinOptions> CreateArgMinOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::TensorType output_type = opencv_tflite::TensorType_FLOAT32) {
  ArgMinOptionsBuilder builder_(_fbb);
  builder_.add_output_type(output_type);
  return builder_.Finish();
}

struct GreaterOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef GreaterOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct GreaterOptionsBuilder {
  typedef GreaterOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit GreaterOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<GreaterOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<GreaterOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<GreaterOptions> CreateGreaterOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  GreaterOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct GreaterEqualOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef GreaterEqualOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct GreaterEqualOptionsBuilder {
  typedef GreaterEqualOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit GreaterEqualOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<GreaterEqualOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<GreaterEqualOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<GreaterEqualOptions> CreateGreaterEqualOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  GreaterEqualOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct LessOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef LessOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct LessOptionsBuilder {
  typedef LessOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit LessOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<LessOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<LessOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<LessOptions> CreateLessOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  LessOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct LessEqualOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef LessEqualOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct LessEqualOptionsBuilder {
  typedef LessEqualOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit LessEqualOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<LessEqualOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<LessEqualOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<LessEqualOptions> CreateLessEqualOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  LessEqualOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct NegOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef NegOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct NegOptionsBuilder {
  typedef NegOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit NegOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<NegOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<NegOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<NegOptions> CreateNegOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  NegOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct SelectOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SelectOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct SelectOptionsBuilder {
  typedef SelectOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit SelectOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SelectOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SelectOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SelectOptions> CreateSelectOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  SelectOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct SliceOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SliceOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct SliceOptionsBuilder {
  typedef SliceOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit SliceOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SliceOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SliceOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SliceOptions> CreateSliceOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  SliceOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct TransposeConvOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef TransposeConvOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PADDING = 4,
    VT_STRIDE_W = 6,
    VT_STRIDE_H = 8,
    VT_FUSED_ACTIVATION_FUNCTION = 10
  };
  opencv_tflite::Padding padding() const {
    return static_cast<opencv_tflite::Padding>(GetField<int8_t>(VT_PADDING, 0));
  }
  int32_t stride_w() const {
    return GetField<int32_t>(VT_STRIDE_W, 0);
  }
  int32_t stride_h() const {
    return GetField<int32_t>(VT_STRIDE_H, 0);
  }
  opencv_tflite::ActivationFunctionType fused_activation_function() const {
    return static_cast<opencv_tflite::ActivationFunctionType>(GetField<int8_t>(VT_FUSED_ACTIVATION_FUNCTION, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_PADDING, 1) &&
           VerifyField<int32_t>(verifier, VT_STRIDE_W, 4) &&
           VerifyField<int32_t>(verifier, VT_STRIDE_H, 4) &&
           VerifyField<int8_t>(verifier, VT_FUSED_ACTIVATION_FUNCTION, 1) &&
           verifier.EndTable();
  }
};

struct TransposeConvOptionsBuilder {
  typedef TransposeConvOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_padding(opencv_tflite::Padding padding) {
    fbb_.AddElement<int8_t>(TransposeConvOptions::VT_PADDING, static_cast<int8_t>(padding), 0);
  }
  void add_stride_w(int32_t stride_w) {
    fbb_.AddElement<int32_t>(TransposeConvOptions::VT_STRIDE_W, stride_w, 0);
  }
  void add_stride_h(int32_t stride_h) {
    fbb_.AddElement<int32_t>(TransposeConvOptions::VT_STRIDE_H, stride_h, 0);
  }
  void add_fused_activation_function(opencv_tflite::ActivationFunctionType fused_activation_function) {
    fbb_.AddElement<int8_t>(TransposeConvOptions::VT_FUSED_ACTIVATION_FUNCTION, static_cast<int8_t>(fused_activation_function), 0);
  }
  explicit TransposeConvOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<TransposeConvOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<TransposeConvOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<TransposeConvOptions> CreateTransposeConvOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::Padding padding = opencv_tflite::Padding_SAME,
    int32_t stride_w = 0,
    int32_t stride_h = 0,
    opencv_tflite::ActivationFunctionType fused_activation_function = opencv_tflite::ActivationFunctionType_NONE) {
  TransposeConvOptionsBuilder builder_(_fbb);
  builder_.add_stride_h(stride_h);
  builder_.add_stride_w(stride_w);
  builder_.add_fused_activation_function(fused_activation_function);
  builder_.add_padding(padding);
  return builder_.Finish();
}

struct ExpandDimsOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ExpandDimsOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct ExpandDimsOptionsBuilder {
  typedef ExpandDimsOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit ExpandDimsOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ExpandDimsOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ExpandDimsOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ExpandDimsOptions> CreateExpandDimsOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  ExpandDimsOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct SparseToDenseOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SparseToDenseOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VALIDATE_INDICES = 4
  };
  bool validate_indices() const {
    return GetField<uint8_t>(VT_VALIDATE_INDICES, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_VALIDATE_INDICES, 1) &&
           verifier.EndTable();
  }
};

struct SparseToDenseOptionsBuilder {
  typedef SparseToDenseOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_validate_indices(bool validate_indices) {
    fbb_.AddElement<uint8_t>(SparseToDenseOptions::VT_VALIDATE_INDICES, static_cast<uint8_t>(validate_indices), 0);
  }
  explicit SparseToDenseOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SparseToDenseOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SparseToDenseOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SparseToDenseOptions> CreateSparseToDenseOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool validate_indices = false) {
  SparseToDenseOptionsBuilder builder_(_fbb);
  builder_.add_validate_indices(validate_indices);
  return builder_.Finish();
}

struct EqualOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef EqualOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct EqualOptionsBuilder {
  typedef EqualOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit EqualOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<EqualOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<EqualOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<EqualOptions> CreateEqualOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  EqualOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct NotEqualOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef NotEqualOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct NotEqualOptionsBuilder {
  typedef NotEqualOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit NotEqualOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<NotEqualOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<NotEqualOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<NotEqualOptions> CreateNotEqualOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  NotEqualOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct ShapeOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ShapeOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_OUT_TYPE = 4
  };
  opencv_tflite::TensorType out_type() const {
    return static_cast<opencv_tflite::TensorType>(GetField<int8_t>(VT_OUT_TYPE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_OUT_TYPE, 1) &&
           verifier.EndTable();
  }
};

struct ShapeOptionsBuilder {
  typedef ShapeOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_out_type(opencv_tflite::TensorType out_type) {
    fbb_.AddElement<int8_t>(ShapeOptions::VT_OUT_TYPE, static_cast<int8_t>(out_type), 0);
  }
  explicit ShapeOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ShapeOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ShapeOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ShapeOptions> CreateShapeOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::TensorType out_type = opencv_tflite::TensorType_FLOAT32) {
  ShapeOptionsBuilder builder_(_fbb);
  builder_.add_out_type(out_type);
  return builder_.Finish();
}

struct RankOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef RankOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct RankOptionsBuilder {
  typedef RankOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit RankOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<RankOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<RankOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<RankOptions> CreateRankOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  RankOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct PowOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef PowOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct PowOptionsBuilder {
  typedef PowOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit PowOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<PowOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<PowOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<PowOptions> CreatePowOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  PowOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct FakeQuantOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef FakeQuantOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MIN = 4,
    VT_MAX = 6,
    VT_NUM_BITS = 8,
    VT_NARROW_RANGE = 10
  };
  float min() const {
    return GetField<float>(VT_MIN, 0.0f);
  }
  float max() const {
    return GetField<float>(VT_MAX, 0.0f);
  }
  int32_t num_bits() const {
    return GetField<int32_t>(VT_NUM_BITS, 0);
  }
  bool narrow_range() const {
    return GetField<uint8_t>(VT_NARROW_RANGE, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_MIN, 4) &&
           VerifyField<float>(verifier, VT_MAX, 4) &&
           VerifyField<int32_t>(verifier, VT_NUM_BITS, 4) &&
           VerifyField<uint8_t>(verifier, VT_NARROW_RANGE, 1) &&
           verifier.EndTable();
  }
};

struct FakeQuantOptionsBuilder {
  typedef FakeQuantOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_min(float min) {
    fbb_.AddElement<float>(FakeQuantOptions::VT_MIN, min, 0.0f);
  }
  void add_max(float max) {
    fbb_.AddElement<float>(FakeQuantOptions::VT_MAX, max, 0.0f);
  }
  void add_num_bits(int32_t num_bits) {
    fbb_.AddElement<int32_t>(FakeQuantOptions::VT_NUM_BITS, num_bits, 0);
  }
  void add_narrow_range(bool narrow_range) {
    fbb_.AddElement<uint8_t>(FakeQuantOptions::VT_NARROW_RANGE, static_cast<uint8_t>(narrow_range), 0);
  }
  explicit FakeQuantOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<FakeQuantOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<FakeQuantOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<FakeQuantOptions> CreateFakeQuantOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    float min = 0.0f,
    float max = 0.0f,
    int32_t num_bits = 0,
    bool narrow_range = false) {
  FakeQuantOptionsBuilder builder_(_fbb);
  builder_.add_num_bits(num_bits);
  builder_.add_max(max);
  builder_.add_min(min);
  builder_.add_narrow_range(narrow_range);
  return builder_.Finish();
}

struct PackOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef PackOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VALUES_COUNT = 4,
    VT_AXIS = 6
  };
  int32_t values_count() const {
    return GetField<int32_t>(VT_VALUES_COUNT, 0);
  }
  int32_t axis() const {
    return GetField<int32_t>(VT_AXIS, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_VALUES_COUNT, 4) &&
           VerifyField<int32_t>(verifier, VT_AXIS, 4) &&
           verifier.EndTable();
  }
};

struct PackOptionsBuilder {
  typedef PackOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_values_count(int32_t values_count) {
    fbb_.AddElement<int32_t>(PackOptions::VT_VALUES_COUNT, values_count, 0);
  }
  void add_axis(int32_t axis) {
    fbb_.AddElement<int32_t>(PackOptions::VT_AXIS, axis, 0);
  }
  explicit PackOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<PackOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<PackOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<PackOptions> CreatePackOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t values_count = 0,
    int32_t axis = 0) {
  PackOptionsBuilder builder_(_fbb);
  builder_.add_axis(axis);
  builder_.add_values_count(values_count);
  return builder_.Finish();
}

struct LogicalOrOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef LogicalOrOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct LogicalOrOptionsBuilder {
  typedef LogicalOrOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit LogicalOrOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<LogicalOrOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<LogicalOrOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<LogicalOrOptions> CreateLogicalOrOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  LogicalOrOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct OneHotOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef OneHotOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_AXIS = 4
  };
  int32_t axis() const {
    return GetField<int32_t>(VT_AXIS, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_AXIS, 4) &&
           verifier.EndTable();
  }
};

struct OneHotOptionsBuilder {
  typedef OneHotOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_axis(int32_t axis) {
    fbb_.AddElement<int32_t>(OneHotOptions::VT_AXIS, axis, 0);
  }
  explicit OneHotOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<OneHotOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<OneHotOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<OneHotOptions> CreateOneHotOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t axis = 0) {
  OneHotOptionsBuilder builder_(_fbb);
  builder_.add_axis(axis);
  return builder_.Finish();
}

struct AbsOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef AbsOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct AbsOptionsBuilder {
  typedef AbsOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit AbsOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<AbsOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<AbsOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<AbsOptions> CreateAbsOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  AbsOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct HardSwishOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef HardSwishOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct HardSwishOptionsBuilder {
  typedef HardSwishOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit HardSwishOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<HardSwishOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<HardSwishOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<HardSwishOptions> CreateHardSwishOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  HardSwishOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct LogicalAndOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef LogicalAndOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct LogicalAndOptionsBuilder {
  typedef LogicalAndOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit LogicalAndOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<LogicalAndOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<LogicalAndOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<LogicalAndOptions> CreateLogicalAndOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  LogicalAndOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct LogicalNotOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef LogicalNotOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct LogicalNotOptionsBuilder {
  typedef LogicalNotOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit LogicalNotOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<LogicalNotOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<LogicalNotOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<LogicalNotOptions> CreateLogicalNotOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  LogicalNotOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct UnpackOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef UnpackOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NUM = 4,
    VT_AXIS = 6
  };
  int32_t num() const {
    return GetField<int32_t>(VT_NUM, 0);
  }
  int32_t axis() const {
    return GetField<int32_t>(VT_AXIS, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_NUM, 4) &&
           VerifyField<int32_t>(verifier, VT_AXIS, 4) &&
           verifier.EndTable();
  }
};

struct UnpackOptionsBuilder {
  typedef UnpackOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_num(int32_t num) {
    fbb_.AddElement<int32_t>(UnpackOptions::VT_NUM, num, 0);
  }
  void add_axis(int32_t axis) {
    fbb_.AddElement<int32_t>(UnpackOptions::VT_AXIS, axis, 0);
  }
  explicit UnpackOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<UnpackOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<UnpackOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<UnpackOptions> CreateUnpackOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t num = 0,
    int32_t axis = 0) {
  UnpackOptionsBuilder builder_(_fbb);
  builder_.add_axis(axis);
  builder_.add_num(num);
  return builder_.Finish();
}

struct FloorDivOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef FloorDivOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct FloorDivOptionsBuilder {
  typedef FloorDivOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit FloorDivOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<FloorDivOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<FloorDivOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<FloorDivOptions> CreateFloorDivOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  FloorDivOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct SquareOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SquareOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct SquareOptionsBuilder {
  typedef SquareOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit SquareOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SquareOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SquareOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SquareOptions> CreateSquareOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  SquareOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct ZerosLikeOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ZerosLikeOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct ZerosLikeOptionsBuilder {
  typedef ZerosLikeOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit ZerosLikeOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ZerosLikeOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ZerosLikeOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ZerosLikeOptions> CreateZerosLikeOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  ZerosLikeOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct FillOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef FillOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct FillOptionsBuilder {
  typedef FillOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit FillOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<FillOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<FillOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<FillOptions> CreateFillOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  FillOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct FloorModOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef FloorModOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct FloorModOptionsBuilder {
  typedef FloorModOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit FloorModOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<FloorModOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<FloorModOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<FloorModOptions> CreateFloorModOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  FloorModOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct RangeOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef RangeOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct RangeOptionsBuilder {
  typedef RangeOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit RangeOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<RangeOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<RangeOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<RangeOptions> CreateRangeOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  RangeOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct LeakyReluOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef LeakyReluOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ALPHA = 4
  };
  float alpha() const {
    return GetField<float>(VT_ALPHA, 0.0f);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_ALPHA, 4) &&
           verifier.EndTable();
  }
};

struct LeakyReluOptionsBuilder {
  typedef LeakyReluOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_alpha(float alpha) {
    fbb_.AddElement<float>(LeakyReluOptions::VT_ALPHA, alpha, 0.0f);
  }
  explicit LeakyReluOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<LeakyReluOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<LeakyReluOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<LeakyReluOptions> CreateLeakyReluOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    float alpha = 0.0f) {
  LeakyReluOptionsBuilder builder_(_fbb);
  builder_.add_alpha(alpha);
  return builder_.Finish();
}

struct SquaredDifferenceOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SquaredDifferenceOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct SquaredDifferenceOptionsBuilder {
  typedef SquaredDifferenceOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit SquaredDifferenceOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SquaredDifferenceOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SquaredDifferenceOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SquaredDifferenceOptions> CreateSquaredDifferenceOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  SquaredDifferenceOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct MirrorPadOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MirrorPadOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MODE = 4
  };
  opencv_tflite::MirrorPadMode mode() const {
    return static_cast<opencv_tflite::MirrorPadMode>(GetField<int8_t>(VT_MODE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_MODE, 1) &&
           verifier.EndTable();
  }
};

struct MirrorPadOptionsBuilder {
  typedef MirrorPadOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_mode(opencv_tflite::MirrorPadMode mode) {
    fbb_.AddElement<int8_t>(MirrorPadOptions::VT_MODE, static_cast<int8_t>(mode), 0);
  }
  explicit MirrorPadOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MirrorPadOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MirrorPadOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MirrorPadOptions> CreateMirrorPadOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::MirrorPadMode mode = opencv_tflite::MirrorPadMode_REFLECT) {
  MirrorPadOptionsBuilder builder_(_fbb);
  builder_.add_mode(mode);
  return builder_.Finish();
}

struct UniqueOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef UniqueOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_IDX_OUT_TYPE = 4
  };
  opencv_tflite::TensorType idx_out_type() const {
    return static_cast<opencv_tflite::TensorType>(GetField<int8_t>(VT_IDX_OUT_TYPE, 2));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_IDX_OUT_TYPE, 1) &&
           verifier.EndTable();
  }
};

struct UniqueOptionsBuilder {
  typedef UniqueOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_idx_out_type(opencv_tflite::TensorType idx_out_type) {
    fbb_.AddElement<int8_t>(UniqueOptions::VT_IDX_OUT_TYPE, static_cast<int8_t>(idx_out_type), 2);
  }
  explicit UniqueOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<UniqueOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<UniqueOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<UniqueOptions> CreateUniqueOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    opencv_tflite::TensorType idx_out_type = opencv_tflite::TensorType_INT32) {
  UniqueOptionsBuilder builder_(_fbb);
  builder_.add_idx_out_type(idx_out_type);
  return builder_.Finish();
}

struct ReverseV2Options FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ReverseV2OptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct ReverseV2OptionsBuilder {
  typedef ReverseV2Options Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit ReverseV2OptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ReverseV2Options> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ReverseV2Options>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ReverseV2Options> CreateReverseV2Options(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  ReverseV2OptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct AddNOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef AddNOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct AddNOptionsBuilder {
  typedef AddNOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit AddNOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<AddNOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<AddNOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<AddNOptions> CreateAddNOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  AddNOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct GatherNdOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef GatherNdOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct GatherNdOptionsBuilder {
  typedef GatherNdOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit GatherNdOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<GatherNdOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<GatherNdOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<GatherNdOptions> CreateGatherNdOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  GatherNdOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct WhereOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef WhereOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct WhereOptionsBuilder {
  typedef WhereOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit WhereOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<WhereOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<WhereOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<WhereOptions> CreateWhereOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  WhereOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct ReverseSequenceOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ReverseSequenceOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SEQ_DIM = 4,
    VT_BATCH_DIM = 6
  };
  int32_t seq_dim() const {
    return GetField<int32_t>(VT_SEQ_DIM, 0);
  }
  int32_t batch_dim() const {
    return GetField<int32_t>(VT_BATCH_DIM, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_SEQ_DIM, 4) &&
           VerifyField<int32_t>(verifier, VT_BATCH_DIM, 4) &&
           verifier.EndTable();
  }
};

struct ReverseSequenceOptionsBuilder {
  typedef ReverseSequenceOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_seq_dim(int32_t seq_dim) {
    fbb_.AddElement<int32_t>(ReverseSequenceOptions::VT_SEQ_DIM, seq_dim, 0);
  }
  void add_batch_dim(int32_t batch_dim) {
    fbb_.AddElement<int32_t>(ReverseSequenceOptions::VT_BATCH_DIM, batch_dim, 0);
  }
  explicit ReverseSequenceOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ReverseSequenceOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ReverseSequenceOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ReverseSequenceOptions> CreateReverseSequenceOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t seq_dim = 0,
    int32_t batch_dim = 0) {
  ReverseSequenceOptionsBuilder builder_(_fbb);
  builder_.add_batch_dim(batch_dim);
  builder_.add_seq_dim(seq_dim);
  return builder_.Finish();
}

struct MatrixDiagOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MatrixDiagOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct MatrixDiagOptionsBuilder {
  typedef MatrixDiagOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit MatrixDiagOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MatrixDiagOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MatrixDiagOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MatrixDiagOptions> CreateMatrixDiagOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  MatrixDiagOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct QuantizeOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef QuantizeOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct QuantizeOptionsBuilder {
  typedef QuantizeOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit QuantizeOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<QuantizeOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<QuantizeOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<QuantizeOptions> CreateQuantizeOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  QuantizeOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct MatrixSetDiagOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MatrixSetDiagOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct MatrixSetDiagOptionsBuilder {
  typedef MatrixSetDiagOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit MatrixSetDiagOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MatrixSetDiagOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MatrixSetDiagOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MatrixSetDiagOptions> CreateMatrixSetDiagOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  MatrixSetDiagOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct IfOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef IfOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_THEN_SUBGRAPH_INDEX = 4,
    VT_ELSE_SUBGRAPH_INDEX = 6
  };
  int32_t then_subgraph_index() const {
    return GetField<int32_t>(VT_THEN_SUBGRAPH_INDEX, 0);
  }
  int32_t else_subgraph_index() const {
    return GetField<int32_t>(VT_ELSE_SUBGRAPH_INDEX, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_THEN_SUBGRAPH_INDEX, 4) &&
           VerifyField<int32_t>(verifier, VT_ELSE_SUBGRAPH_INDEX, 4) &&
           verifier.EndTable();
  }
};

struct IfOptionsBuilder {
  typedef IfOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_then_subgraph_index(int32_t then_subgraph_index) {
    fbb_.AddElement<int32_t>(IfOptions::VT_THEN_SUBGRAPH_INDEX, then_subgraph_index, 0);
  }
  void add_else_subgraph_index(int32_t else_subgraph_index) {
    fbb_.AddElement<int32_t>(IfOptions::VT_ELSE_SUBGRAPH_INDEX, else_subgraph_index, 0);
  }
  explicit IfOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<IfOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<IfOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<IfOptions> CreateIfOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t then_subgraph_index = 0,
    int32_t else_subgraph_index = 0) {
  IfOptionsBuilder builder_(_fbb);
  builder_.add_else_subgraph_index(else_subgraph_index);
  builder_.add_then_subgraph_index(then_subgraph_index);
  return builder_.Finish();
}

struct CallOnceOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef CallOnceOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_INIT_SUBGRAPH_INDEX = 4
  };
  int32_t init_subgraph_index() const {
    return GetField<int32_t>(VT_INIT_SUBGRAPH_INDEX, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_INIT_SUBGRAPH_INDEX, 4) &&
           verifier.EndTable();
  }
};

struct CallOnceOptionsBuilder {
  typedef CallOnceOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_init_subgraph_index(int32_t init_subgraph_index) {
    fbb_.AddElement<int32_t>(CallOnceOptions::VT_INIT_SUBGRAPH_INDEX, init_subgraph_index, 0);
  }
  explicit CallOnceOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<CallOnceOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<CallOnceOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<CallOnceOptions> CreateCallOnceOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t init_subgraph_index = 0) {
  CallOnceOptionsBuilder builder_(_fbb);
  builder_.add_init_subgraph_index(init_subgraph_index);
  return builder_.Finish();
}

struct WhileOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef WhileOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_COND_SUBGRAPH_INDEX = 4,
    VT_BODY_SUBGRAPH_INDEX = 6
  };
  int32_t cond_subgraph_index() const {
    return GetField<int32_t>(VT_COND_SUBGRAPH_INDEX, 0);
  }
  int32_t body_subgraph_index() const {
    return GetField<int32_t>(VT_BODY_SUBGRAPH_INDEX, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_COND_SUBGRAPH_INDEX, 4) &&
           VerifyField<int32_t>(verifier, VT_BODY_SUBGRAPH_INDEX, 4) &&
           verifier.EndTable();
  }
};

struct WhileOptionsBuilder {
  typedef WhileOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_cond_subgraph_index(int32_t cond_subgraph_index) {
    fbb_.AddElement<int32_t>(WhileOptions::VT_COND_SUBGRAPH_INDEX, cond_subgraph_index, 0);
  }
  void add_body_subgraph_index(int32_t body_subgraph_index) {
    fbb_.AddElement<int32_t>(WhileOptions::VT_BODY_SUBGRAPH_INDEX, body_subgraph_index, 0);
  }
  explicit WhileOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<WhileOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<WhileOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<WhileOptions> CreateWhileOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t cond_subgraph_index = 0,
    int32_t body_subgraph_index = 0) {
  WhileOptionsBuilder builder_(_fbb);
  builder_.add_body_subgraph_index(body_subgraph_index);
  builder_.add_cond_subgraph_index(cond_subgraph_index);
  return builder_.Finish();
}

struct NonMaxSuppressionV4Options FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef NonMaxSuppressionV4OptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct NonMaxSuppressionV4OptionsBuilder {
  typedef NonMaxSuppressionV4Options Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit NonMaxSuppressionV4OptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<NonMaxSuppressionV4Options> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<NonMaxSuppressionV4Options>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<NonMaxSuppressionV4Options> CreateNonMaxSuppressionV4Options(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  NonMaxSuppressionV4OptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct NonMaxSuppressionV5Options FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef NonMaxSuppressionV5OptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct NonMaxSuppressionV5OptionsBuilder {
  typedef NonMaxSuppressionV5Options Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit NonMaxSuppressionV5OptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<NonMaxSuppressionV5Options> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<NonMaxSuppressionV5Options>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<NonMaxSuppressionV5Options> CreateNonMaxSuppressionV5Options(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  NonMaxSuppressionV5OptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct ScatterNdOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ScatterNdOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct ScatterNdOptionsBuilder {
  typedef ScatterNdOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit ScatterNdOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ScatterNdOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ScatterNdOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ScatterNdOptions> CreateScatterNdOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  ScatterNdOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct SelectV2Options FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SelectV2OptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct SelectV2OptionsBuilder {
  typedef SelectV2Options Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit SelectV2OptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SelectV2Options> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SelectV2Options>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SelectV2Options> CreateSelectV2Options(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  SelectV2OptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct DensifyOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DensifyOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct DensifyOptionsBuilder {
  typedef DensifyOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit DensifyOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DensifyOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DensifyOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DensifyOptions> CreateDensifyOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  DensifyOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct SegmentSumOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SegmentSumOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct SegmentSumOptionsBuilder {
  typedef SegmentSumOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit SegmentSumOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SegmentSumOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SegmentSumOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SegmentSumOptions> CreateSegmentSumOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  SegmentSumOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct BatchMatMulOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef BatchMatMulOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ADJ_X = 4,
    VT_ADJ_Y = 6,
    VT_ASYMMETRIC_QUANTIZE_INPUTS = 8
  };
  bool adj_x() const {
    return GetField<uint8_t>(VT_ADJ_X, 0) != 0;
  }
  bool adj_y() const {
    return GetField<uint8_t>(VT_ADJ_Y, 0) != 0;
  }
  bool asymmetric_quantize_inputs() const {
    return GetField<uint8_t>(VT_ASYMMETRIC_QUANTIZE_INPUTS, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_ADJ_X, 1) &&
           VerifyField<uint8_t>(verifier, VT_ADJ_Y, 1) &&
           VerifyField<uint8_t>(verifier, VT_ASYMMETRIC_QUANTIZE_INPUTS, 1) &&
           verifier.EndTable();
  }
};

struct BatchMatMulOptionsBuilder {
  typedef BatchMatMulOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_adj_x(bool adj_x) {
    fbb_.AddElement<uint8_t>(BatchMatMulOptions::VT_ADJ_X, static_cast<uint8_t>(adj_x), 0);
  }
  void add_adj_y(bool adj_y) {
    fbb_.AddElement<uint8_t>(BatchMatMulOptions::VT_ADJ_Y, static_cast<uint8_t>(adj_y), 0);
  }
  void add_asymmetric_quantize_inputs(bool asymmetric_quantize_inputs) {
    fbb_.AddElement<uint8_t>(BatchMatMulOptions::VT_ASYMMETRIC_QUANTIZE_INPUTS, static_cast<uint8_t>(asymmetric_quantize_inputs), 0);
  }
  explicit BatchMatMulOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<BatchMatMulOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<BatchMatMulOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<BatchMatMulOptions> CreateBatchMatMulOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool adj_x = false,
    bool adj_y = false,
    bool asymmetric_quantize_inputs = false) {
  BatchMatMulOptionsBuilder builder_(_fbb);
  builder_.add_asymmetric_quantize_inputs(asymmetric_quantize_inputs);
  builder_.add_adj_y(adj_y);
  builder_.add_adj_x(adj_x);
  return builder_.Finish();
}

struct CumsumOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef CumsumOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_EXCLUSIVE = 4,
    VT_REVERSE = 6
  };
  bool exclusive() const {
    return GetField<uint8_t>(VT_EXCLUSIVE, 0) != 0;
  }
  bool reverse() const {
    return GetField<uint8_t>(VT_REVERSE, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_EXCLUSIVE, 1) &&
           VerifyField<uint8_t>(verifier, VT_REVERSE, 1) &&
           verifier.EndTable();
  }
};

struct CumsumOptionsBuilder {
  typedef CumsumOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_exclusive(bool exclusive) {
    fbb_.AddElement<uint8_t>(CumsumOptions::VT_EXCLUSIVE, static_cast<uint8_t>(exclusive), 0);
  }
  void add_reverse(bool reverse) {
    fbb_.AddElement<uint8_t>(CumsumOptions::VT_REVERSE, static_cast<uint8_t>(reverse), 0);
  }
  explicit CumsumOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<CumsumOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<CumsumOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<CumsumOptions> CreateCumsumOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool exclusive = false,
    bool reverse = false) {
  CumsumOptionsBuilder builder_(_fbb);
  builder_.add_reverse(reverse);
  builder_.add_exclusive(exclusive);
  return builder_.Finish();
}

struct BroadcastToOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef BroadcastToOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct BroadcastToOptionsBuilder {
  typedef BroadcastToOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit BroadcastToOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<BroadcastToOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<BroadcastToOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<BroadcastToOptions> CreateBroadcastToOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  BroadcastToOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct Rfft2dOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef Rfft2dOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct Rfft2dOptionsBuilder {
  typedef Rfft2dOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit Rfft2dOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Rfft2dOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Rfft2dOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Rfft2dOptions> CreateRfft2dOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  Rfft2dOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct HashtableOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef HashtableOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TABLE_ID = 4,
    VT_KEY_DTYPE = 6,
    VT_VALUE_DTYPE = 8
  };
  int32_t table_id() const {
    return GetField<int32_t>(VT_TABLE_ID, 0);
  }
  opencv_tflite::TensorType key_dtype() const {
    return static_cast<opencv_tflite::TensorType>(GetField<int8_t>(VT_KEY_DTYPE, 0));
  }
  opencv_tflite::TensorType value_dtype() const {
    return static_cast<opencv_tflite::TensorType>(GetField<int8_t>(VT_VALUE_DTYPE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_TABLE_ID, 4) &&
           VerifyField<int8_t>(verifier, VT_KEY_DTYPE, 1) &&
           VerifyField<int8_t>(verifier, VT_VALUE_DTYPE, 1) &&
           verifier.EndTable();
  }
};

struct HashtableOptionsBuilder {
  typedef HashtableOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_table_id(int32_t table_id) {
    fbb_.AddElement<int32_t>(HashtableOptions::VT_TABLE_ID, table_id, 0);
  }
  void add_key_dtype(opencv_tflite::TensorType key_dtype) {
    fbb_.AddElement<int8_t>(HashtableOptions::VT_KEY_DTYPE, static_cast<int8_t>(key_dtype), 0);
  }
  void add_value_dtype(opencv_tflite::TensorType value_dtype) {
    fbb_.AddElement<int8_t>(HashtableOptions::VT_VALUE_DTYPE, static_cast<int8_t>(value_dtype), 0);
  }
  explicit HashtableOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<HashtableOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<HashtableOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<HashtableOptions> CreateHashtableOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t table_id = 0,
    opencv_tflite::TensorType key_dtype = opencv_tflite::TensorType_FLOAT32,
    opencv_tflite::TensorType value_dtype = opencv_tflite::TensorType_FLOAT32) {
  HashtableOptionsBuilder builder_(_fbb);
  builder_.add_table_id(table_id);
  builder_.add_value_dtype(value_dtype);
  builder_.add_key_dtype(key_dtype);
  return builder_.Finish();
}

struct HashtableFindOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef HashtableFindOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct HashtableFindOptionsBuilder {
  typedef HashtableFindOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit HashtableFindOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<HashtableFindOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<HashtableFindOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<HashtableFindOptions> CreateHashtableFindOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  HashtableFindOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct HashtableImportOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef HashtableImportOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct HashtableImportOptionsBuilder {
  typedef HashtableImportOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit HashtableImportOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<HashtableImportOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<HashtableImportOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<HashtableImportOptions> CreateHashtableImportOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  HashtableImportOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct HashtableSizeOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef HashtableSizeOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct HashtableSizeOptionsBuilder {
  typedef HashtableSizeOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit HashtableSizeOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<HashtableSizeOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<HashtableSizeOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<HashtableSizeOptions> CreateHashtableSizeOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  HashtableSizeOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct VarHandleOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef VarHandleOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CONTAINER = 4,
    VT_SHARED_NAME = 6
  };
  const ::flatbuffers::String *container() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CONTAINER);
  }
  const ::flatbuffers::String *shared_name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SHARED_NAME);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_CONTAINER) &&
           verifier.VerifyString(container()) &&
           VerifyOffset(verifier, VT_SHARED_NAME) &&
           verifier.VerifyString(shared_name()) &&
           verifier.EndTable();
  }
};

struct VarHandleOptionsBuilder {
  typedef VarHandleOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_container(::flatbuffers::Offset<::flatbuffers::String> container) {
    fbb_.AddOffset(VarHandleOptions::VT_CONTAINER, container);
  }
  void add_shared_name(::flatbuffers::Offset<::flatbuffers::String> shared_name) {
    fbb_.AddOffset(VarHandleOptions::VT_SHARED_NAME, shared_name);
  }
  explicit VarHandleOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<VarHandleOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<VarHandleOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<VarHandleOptions> CreateVarHandleOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> container = 0,
    ::flatbuffers::Offset<::flatbuffers::String> shared_name = 0) {
  VarHandleOptionsBuilder builder_(_fbb);
  builder_.add_shared_name(shared_name);
  builder_.add_container(container);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<VarHandleOptions> CreateVarHandleOptionsDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *container = nullptr,
    const char *shared_name = nullptr) {
  auto container__ = container ? _fbb.CreateString(container) : 0;
  auto shared_name__ = shared_name ? _fbb.CreateString(shared_name) : 0;
  return opencv_tflite::CreateVarHandleOptions(
      _fbb,
      container__,
      shared_name__);
}

struct ReadVariableOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ReadVariableOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct ReadVariableOptionsBuilder {
  typedef ReadVariableOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit ReadVariableOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ReadVariableOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ReadVariableOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ReadVariableOptions> CreateReadVariableOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  ReadVariableOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct AssignVariableOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef AssignVariableOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct AssignVariableOptionsBuilder {
  typedef AssignVariableOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit AssignVariableOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<AssignVariableOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<AssignVariableOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<AssignVariableOptions> CreateAssignVariableOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  AssignVariableOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct RandomOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef RandomOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SEED = 4,
    VT_SEED2 = 6
  };
  int64_t seed() const {
    return GetField<int64_t>(VT_SEED, 0);
  }
  int64_t seed2() const {
    return GetField<int64_t>(VT_SEED2, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int64_t>(verifier, VT_SEED, 8) &&
           VerifyField<int64_t>(verifier, VT_SEED2, 8) &&
           verifier.EndTable();
  }
};

struct RandomOptionsBuilder {
  typedef RandomOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_seed(int64_t seed) {
    fbb_.AddElement<int64_t>(RandomOptions::VT_SEED, seed, 0);
  }
  void add_seed2(int64_t seed2) {
    fbb_.AddElement<int64_t>(RandomOptions::VT_SEED2, seed2, 0);
  }
  explicit RandomOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<RandomOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<RandomOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<RandomOptions> CreateRandomOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int64_t seed = 0,
    int64_t seed2 = 0) {
  RandomOptionsBuilder builder_(_fbb);
  builder_.add_seed2(seed2);
  builder_.add_seed(seed);
  return builder_.Finish();
}

struct BucketizeOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef BucketizeOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_BOUNDARIES = 4
  };
  const ::flatbuffers::Vector<float> *boundaries() const {
    return GetPointer<const ::flatbuffers::Vector<float> *>(VT_BOUNDARIES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_BOUNDARIES) &&
           verifier.VerifyVector(boundaries()) &&
           verifier.EndTable();
  }
};

struct BucketizeOptionsBuilder {
  typedef BucketizeOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_boundaries(::flatbuffers::Offset<::flatbuffers::Vector<float>> boundaries) {
    fbb_.AddOffset(BucketizeOptions::VT_BOUNDARIES, boundaries);
  }
  explicit BucketizeOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<BucketizeOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<BucketizeOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<BucketizeOptions> CreateBucketizeOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<float>> boundaries = 0) {
  BucketizeOptionsBuilder builder_(_fbb);
  builder_.add_boundaries(boundaries);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<BucketizeOptions> CreateBucketizeOptionsDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<float> *boundaries = nullptr) {
  auto boundaries__ = boundaries ? _fbb.CreateVector<float>(*boundaries) : 0;
  return opencv_tflite::CreateBucketizeOptions(
      _fbb,
      boundaries__);
}

struct GeluOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef GeluOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_APPROXIMATE = 4
  };
  bool approximate() const {
    return GetField<uint8_t>(VT_APPROXIMATE, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_APPROXIMATE, 1) &&
           verifier.EndTable();
  }
};

struct GeluOptionsBuilder {
  typedef GeluOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_approximate(bool approximate) {
    fbb_.AddElement<uint8_t>(GeluOptions::VT_APPROXIMATE, static_cast<uint8_t>(approximate), 0);
  }
  explicit GeluOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<GeluOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<GeluOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<GeluOptions> CreateGeluOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool approximate = false) {
  GeluOptionsBuilder builder_(_fbb);
  builder_.add_approximate(approximate);
  return builder_.Finish();
}

struct DynamicUpdateSliceOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DynamicUpdateSliceOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct DynamicUpdateSliceOptionsBuilder {
  typedef DynamicUpdateSliceOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit DynamicUpdateSliceOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DynamicUpdateSliceOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DynamicUpdateSliceOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DynamicUpdateSliceOptions> CreateDynamicUpdateSliceOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  DynamicUpdateSliceOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct UnsortedSegmentProdOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef UnsortedSegmentProdOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct UnsortedSegmentProdOptionsBuilder {
  typedef UnsortedSegmentProdOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit UnsortedSegmentProdOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<UnsortedSegmentProdOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<UnsortedSegmentProdOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<UnsortedSegmentProdOptions> CreateUnsortedSegmentProdOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  UnsortedSegmentProdOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct UnsortedSegmentMaxOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef UnsortedSegmentMaxOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct UnsortedSegmentMaxOptionsBuilder {
  typedef UnsortedSegmentMaxOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit UnsortedSegmentMaxOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<UnsortedSegmentMaxOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<UnsortedSegmentMaxOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<UnsortedSegmentMaxOptions> CreateUnsortedSegmentMaxOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  UnsortedSegmentMaxOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct UnsortedSegmentSumOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef UnsortedSegmentSumOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct UnsortedSegmentSumOptionsBuilder {
  typedef UnsortedSegmentSumOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit UnsortedSegmentSumOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<UnsortedSegmentSumOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<UnsortedSegmentSumOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<UnsortedSegmentSumOptions> CreateUnsortedSegmentSumOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  UnsortedSegmentSumOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct ATan2Options FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ATan2OptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct ATan2OptionsBuilder {
  typedef ATan2Options Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit ATan2OptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ATan2Options> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ATan2Options>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ATan2Options> CreateATan2Options(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  ATan2OptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct UnsortedSegmentMinOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef UnsortedSegmentMinOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct UnsortedSegmentMinOptionsBuilder {
  typedef UnsortedSegmentMinOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit UnsortedSegmentMinOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<UnsortedSegmentMinOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<UnsortedSegmentMinOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<UnsortedSegmentMinOptions> CreateUnsortedSegmentMinOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  UnsortedSegmentMinOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct SignOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SignOptionsBuilder Builder;
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct SignOptionsBuilder {
  typedef SignOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  explicit SignOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SignOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SignOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SignOptions> CreateSignOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb) {
  SignOptionsBuilder builder_(_fbb);
  return builder_.Finish();
}

struct OperatorCode FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef OperatorCodeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DEPRECATED_BUILTIN_CODE = 4,
    VT_CUSTOM_CODE = 6,
    VT_VERSION = 8,
    VT_BUILTIN_CODE = 10
  };
  int8_t deprecated_builtin_code() const {
    return GetField<int8_t>(VT_DEPRECATED_BUILTIN_CODE, 0);
  }
  const ::flatbuffers::String *custom_code() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CUSTOM_CODE);
  }
  int32_t version() const {
    return GetField<int32_t>(VT_VERSION, 1);
  }
  opencv_tflite::BuiltinOperator builtin_code() const {
    return static_cast<opencv_tflite::BuiltinOperator>(GetField<int32_t>(VT_BUILTIN_CODE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_DEPRECATED_BUILTIN_CODE, 1) &&
           VerifyOffset(verifier, VT_CUSTOM_CODE) &&
           verifier.VerifyString(custom_code()) &&
           VerifyField<int32_t>(verifier, VT_VERSION, 4) &&
           VerifyField<int32_t>(verifier, VT_BUILTIN_CODE, 4) &&
           verifier.EndTable();
  }
};

struct OperatorCodeBuilder {
  typedef OperatorCode Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_deprecated_builtin_code(int8_t deprecated_builtin_code) {
    fbb_.AddElement<int8_t>(OperatorCode::VT_DEPRECATED_BUILTIN_CODE, deprecated_builtin_code, 0);
  }
  void add_custom_code(::flatbuffers::Offset<::flatbuffers::String> custom_code) {
    fbb_.AddOffset(OperatorCode::VT_CUSTOM_CODE, custom_code);
  }
  void add_version(int32_t version) {
    fbb_.AddElement<int32_t>(OperatorCode::VT_VERSION, version, 1);
  }
  void add_builtin_code(opencv_tflite::BuiltinOperator builtin_code) {
    fbb_.AddElement<int32_t>(OperatorCode::VT_BUILTIN_CODE, static_cast<int32_t>(builtin_code), 0);
  }
  explicit OperatorCodeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<OperatorCode> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<OperatorCode>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<OperatorCode> CreateOperatorCode(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int8_t deprecated_builtin_code = 0,
    ::flatbuffers::Offset<::flatbuffers::String> custom_code = 0,
    int32_t version = 1,
    opencv_tflite::BuiltinOperator builtin_code = opencv_tflite::BuiltinOperator_ADD) {
  OperatorCodeBuilder builder_(_fbb);
  builder_.add_builtin_code(builtin_code);
  builder_.add_version(version);
  builder_.add_custom_code(custom_code);
  builder_.add_deprecated_builtin_code(deprecated_builtin_code);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<OperatorCode> CreateOperatorCodeDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int8_t deprecated_builtin_code = 0,
    const char *custom_code = nullptr,
    int32_t version = 1,
    opencv_tflite::BuiltinOperator builtin_code = opencv_tflite::BuiltinOperator_ADD) {
  auto custom_code__ = custom_code ? _fbb.CreateString(custom_code) : 0;
  return opencv_tflite::CreateOperatorCode(
      _fbb,
      deprecated_builtin_code,
      custom_code__,
      version,
      builtin_code);
}

struct Operator FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef OperatorBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_OPCODE_INDEX = 4,
    VT_INPUTS = 6,
    VT_OUTPUTS = 8,
    VT_BUILTIN_OPTIONS_TYPE = 10,
    VT_BUILTIN_OPTIONS = 12,
    VT_CUSTOM_OPTIONS = 14,
    VT_CUSTOM_OPTIONS_FORMAT = 16,
    VT_MUTATING_VARIABLE_INPUTS = 18,
    VT_INTERMEDIATES = 20
  };
  uint32_t opcode_index() const {
    return GetField<uint32_t>(VT_OPCODE_INDEX, 0);
  }
  const ::flatbuffers::Vector<int32_t> *inputs() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_INPUTS);
  }
  const ::flatbuffers::Vector<int32_t> *outputs() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_OUTPUTS);
  }
  opencv_tflite::BuiltinOptions builtin_options_type() const {
    return static_cast<opencv_tflite::BuiltinOptions>(GetField<uint8_t>(VT_BUILTIN_OPTIONS_TYPE, 0));
  }
  const void *builtin_options() const {
    return GetPointer<const void *>(VT_BUILTIN_OPTIONS);
  }
  template<typename T> const T *builtin_options_as() const;
  const opencv_tflite::Conv2DOptions *builtin_options_as_Conv2DOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_Conv2DOptions ? static_cast<const opencv_tflite::Conv2DOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::DepthwiseConv2DOptions *builtin_options_as_DepthwiseConv2DOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_DepthwiseConv2DOptions ? static_cast<const opencv_tflite::DepthwiseConv2DOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ConcatEmbeddingsOptions *builtin_options_as_ConcatEmbeddingsOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ConcatEmbeddingsOptions ? static_cast<const opencv_tflite::ConcatEmbeddingsOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::LSHProjectionOptions *builtin_options_as_LSHProjectionOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_LSHProjectionOptions ? static_cast<const opencv_tflite::LSHProjectionOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::Pool2DOptions *builtin_options_as_Pool2DOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_Pool2DOptions ? static_cast<const opencv_tflite::Pool2DOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SVDFOptions *builtin_options_as_SVDFOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SVDFOptions ? static_cast<const opencv_tflite::SVDFOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::RNNOptions *builtin_options_as_RNNOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_RNNOptions ? static_cast<const opencv_tflite::RNNOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::FullyConnectedOptions *builtin_options_as_FullyConnectedOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_FullyConnectedOptions ? static_cast<const opencv_tflite::FullyConnectedOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SoftmaxOptions *builtin_options_as_SoftmaxOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SoftmaxOptions ? static_cast<const opencv_tflite::SoftmaxOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ConcatenationOptions *builtin_options_as_ConcatenationOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ConcatenationOptions ? static_cast<const opencv_tflite::ConcatenationOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::AddOptions *builtin_options_as_AddOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_AddOptions ? static_cast<const opencv_tflite::AddOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::L2NormOptions *builtin_options_as_L2NormOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_L2NormOptions ? static_cast<const opencv_tflite::L2NormOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::LocalResponseNormalizationOptions *builtin_options_as_LocalResponseNormalizationOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_LocalResponseNormalizationOptions ? static_cast<const opencv_tflite::LocalResponseNormalizationOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::LSTMOptions *builtin_options_as_LSTMOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_LSTMOptions ? static_cast<const opencv_tflite::LSTMOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ResizeBilinearOptions *builtin_options_as_ResizeBilinearOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ResizeBilinearOptions ? static_cast<const opencv_tflite::ResizeBilinearOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::CallOptions *builtin_options_as_CallOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_CallOptions ? static_cast<const opencv_tflite::CallOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ReshapeOptions *builtin_options_as_ReshapeOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ReshapeOptions ? static_cast<const opencv_tflite::ReshapeOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SkipGramOptions *builtin_options_as_SkipGramOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SkipGramOptions ? static_cast<const opencv_tflite::SkipGramOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SpaceToDepthOptions *builtin_options_as_SpaceToDepthOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SpaceToDepthOptions ? static_cast<const opencv_tflite::SpaceToDepthOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::EmbeddingLookupSparseOptions *builtin_options_as_EmbeddingLookupSparseOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_EmbeddingLookupSparseOptions ? static_cast<const opencv_tflite::EmbeddingLookupSparseOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::MulOptions *builtin_options_as_MulOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_MulOptions ? static_cast<const opencv_tflite::MulOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::PadOptions *builtin_options_as_PadOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_PadOptions ? static_cast<const opencv_tflite::PadOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::GatherOptions *builtin_options_as_GatherOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_GatherOptions ? static_cast<const opencv_tflite::GatherOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::BatchToSpaceNDOptions *builtin_options_as_BatchToSpaceNDOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_BatchToSpaceNDOptions ? static_cast<const opencv_tflite::BatchToSpaceNDOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SpaceToBatchNDOptions *builtin_options_as_SpaceToBatchNDOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SpaceToBatchNDOptions ? static_cast<const opencv_tflite::SpaceToBatchNDOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::TransposeOptions *builtin_options_as_TransposeOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_TransposeOptions ? static_cast<const opencv_tflite::TransposeOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ReducerOptions *builtin_options_as_ReducerOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ReducerOptions ? static_cast<const opencv_tflite::ReducerOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SubOptions *builtin_options_as_SubOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SubOptions ? static_cast<const opencv_tflite::SubOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::DivOptions *builtin_options_as_DivOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_DivOptions ? static_cast<const opencv_tflite::DivOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SqueezeOptions *builtin_options_as_SqueezeOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SqueezeOptions ? static_cast<const opencv_tflite::SqueezeOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SequenceRNNOptions *builtin_options_as_SequenceRNNOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SequenceRNNOptions ? static_cast<const opencv_tflite::SequenceRNNOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::StridedSliceOptions *builtin_options_as_StridedSliceOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_StridedSliceOptions ? static_cast<const opencv_tflite::StridedSliceOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ExpOptions *builtin_options_as_ExpOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ExpOptions ? static_cast<const opencv_tflite::ExpOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::TopKV2Options *builtin_options_as_TopKV2Options() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_TopKV2Options ? static_cast<const opencv_tflite::TopKV2Options *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SplitOptions *builtin_options_as_SplitOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SplitOptions ? static_cast<const opencv_tflite::SplitOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::LogSoftmaxOptions *builtin_options_as_LogSoftmaxOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_LogSoftmaxOptions ? static_cast<const opencv_tflite::LogSoftmaxOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::CastOptions *builtin_options_as_CastOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_CastOptions ? static_cast<const opencv_tflite::CastOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::DequantizeOptions *builtin_options_as_DequantizeOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_DequantizeOptions ? static_cast<const opencv_tflite::DequantizeOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::MaximumMinimumOptions *builtin_options_as_MaximumMinimumOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_MaximumMinimumOptions ? static_cast<const opencv_tflite::MaximumMinimumOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ArgMaxOptions *builtin_options_as_ArgMaxOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ArgMaxOptions ? static_cast<const opencv_tflite::ArgMaxOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::LessOptions *builtin_options_as_LessOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_LessOptions ? static_cast<const opencv_tflite::LessOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::NegOptions *builtin_options_as_NegOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_NegOptions ? static_cast<const opencv_tflite::NegOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::PadV2Options *builtin_options_as_PadV2Options() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_PadV2Options ? static_cast<const opencv_tflite::PadV2Options *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::GreaterOptions *builtin_options_as_GreaterOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_GreaterOptions ? static_cast<const opencv_tflite::GreaterOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::GreaterEqualOptions *builtin_options_as_GreaterEqualOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_GreaterEqualOptions ? static_cast<const opencv_tflite::GreaterEqualOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::LessEqualOptions *builtin_options_as_LessEqualOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_LessEqualOptions ? static_cast<const opencv_tflite::LessEqualOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SelectOptions *builtin_options_as_SelectOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SelectOptions ? static_cast<const opencv_tflite::SelectOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SliceOptions *builtin_options_as_SliceOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SliceOptions ? static_cast<const opencv_tflite::SliceOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::TransposeConvOptions *builtin_options_as_TransposeConvOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_TransposeConvOptions ? static_cast<const opencv_tflite::TransposeConvOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SparseToDenseOptions *builtin_options_as_SparseToDenseOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SparseToDenseOptions ? static_cast<const opencv_tflite::SparseToDenseOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::TileOptions *builtin_options_as_TileOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_TileOptions ? static_cast<const opencv_tflite::TileOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ExpandDimsOptions *builtin_options_as_ExpandDimsOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ExpandDimsOptions ? static_cast<const opencv_tflite::ExpandDimsOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::EqualOptions *builtin_options_as_EqualOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_EqualOptions ? static_cast<const opencv_tflite::EqualOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::NotEqualOptions *builtin_options_as_NotEqualOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_NotEqualOptions ? static_cast<const opencv_tflite::NotEqualOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ShapeOptions *builtin_options_as_ShapeOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ShapeOptions ? static_cast<const opencv_tflite::ShapeOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::PowOptions *builtin_options_as_PowOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_PowOptions ? static_cast<const opencv_tflite::PowOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ArgMinOptions *builtin_options_as_ArgMinOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ArgMinOptions ? static_cast<const opencv_tflite::ArgMinOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::FakeQuantOptions *builtin_options_as_FakeQuantOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_FakeQuantOptions ? static_cast<const opencv_tflite::FakeQuantOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::PackOptions *builtin_options_as_PackOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_PackOptions ? static_cast<const opencv_tflite::PackOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::LogicalOrOptions *builtin_options_as_LogicalOrOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_LogicalOrOptions ? static_cast<const opencv_tflite::LogicalOrOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::OneHotOptions *builtin_options_as_OneHotOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_OneHotOptions ? static_cast<const opencv_tflite::OneHotOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::LogicalAndOptions *builtin_options_as_LogicalAndOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_LogicalAndOptions ? static_cast<const opencv_tflite::LogicalAndOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::LogicalNotOptions *builtin_options_as_LogicalNotOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_LogicalNotOptions ? static_cast<const opencv_tflite::LogicalNotOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::UnpackOptions *builtin_options_as_UnpackOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_UnpackOptions ? static_cast<const opencv_tflite::UnpackOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::FloorDivOptions *builtin_options_as_FloorDivOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_FloorDivOptions ? static_cast<const opencv_tflite::FloorDivOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SquareOptions *builtin_options_as_SquareOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SquareOptions ? static_cast<const opencv_tflite::SquareOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ZerosLikeOptions *builtin_options_as_ZerosLikeOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ZerosLikeOptions ? static_cast<const opencv_tflite::ZerosLikeOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::FillOptions *builtin_options_as_FillOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_FillOptions ? static_cast<const opencv_tflite::FillOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::BidirectionalSequenceLSTMOptions *builtin_options_as_BidirectionalSequenceLSTMOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_BidirectionalSequenceLSTMOptions ? static_cast<const opencv_tflite::BidirectionalSequenceLSTMOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::BidirectionalSequenceRNNOptions *builtin_options_as_BidirectionalSequenceRNNOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_BidirectionalSequenceRNNOptions ? static_cast<const opencv_tflite::BidirectionalSequenceRNNOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::UnidirectionalSequenceLSTMOptions *builtin_options_as_UnidirectionalSequenceLSTMOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_UnidirectionalSequenceLSTMOptions ? static_cast<const opencv_tflite::UnidirectionalSequenceLSTMOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::FloorModOptions *builtin_options_as_FloorModOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_FloorModOptions ? static_cast<const opencv_tflite::FloorModOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::RangeOptions *builtin_options_as_RangeOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_RangeOptions ? static_cast<const opencv_tflite::RangeOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ResizeNearestNeighborOptions *builtin_options_as_ResizeNearestNeighborOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ResizeNearestNeighborOptions ? static_cast<const opencv_tflite::ResizeNearestNeighborOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::LeakyReluOptions *builtin_options_as_LeakyReluOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_LeakyReluOptions ? static_cast<const opencv_tflite::LeakyReluOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SquaredDifferenceOptions *builtin_options_as_SquaredDifferenceOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SquaredDifferenceOptions ? static_cast<const opencv_tflite::SquaredDifferenceOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::MirrorPadOptions *builtin_options_as_MirrorPadOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_MirrorPadOptions ? static_cast<const opencv_tflite::MirrorPadOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::AbsOptions *builtin_options_as_AbsOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_AbsOptions ? static_cast<const opencv_tflite::AbsOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SplitVOptions *builtin_options_as_SplitVOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SplitVOptions ? static_cast<const opencv_tflite::SplitVOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::UniqueOptions *builtin_options_as_UniqueOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_UniqueOptions ? static_cast<const opencv_tflite::UniqueOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ReverseV2Options *builtin_options_as_ReverseV2Options() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ReverseV2Options ? static_cast<const opencv_tflite::ReverseV2Options *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::AddNOptions *builtin_options_as_AddNOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_AddNOptions ? static_cast<const opencv_tflite::AddNOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::GatherNdOptions *builtin_options_as_GatherNdOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_GatherNdOptions ? static_cast<const opencv_tflite::GatherNdOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::CosOptions *builtin_options_as_CosOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_CosOptions ? static_cast<const opencv_tflite::CosOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::WhereOptions *builtin_options_as_WhereOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_WhereOptions ? static_cast<const opencv_tflite::WhereOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::RankOptions *builtin_options_as_RankOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_RankOptions ? static_cast<const opencv_tflite::RankOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ReverseSequenceOptions *builtin_options_as_ReverseSequenceOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ReverseSequenceOptions ? static_cast<const opencv_tflite::ReverseSequenceOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::MatrixDiagOptions *builtin_options_as_MatrixDiagOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_MatrixDiagOptions ? static_cast<const opencv_tflite::MatrixDiagOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::QuantizeOptions *builtin_options_as_QuantizeOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_QuantizeOptions ? static_cast<const opencv_tflite::QuantizeOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::MatrixSetDiagOptions *builtin_options_as_MatrixSetDiagOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_MatrixSetDiagOptions ? static_cast<const opencv_tflite::MatrixSetDiagOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::HardSwishOptions *builtin_options_as_HardSwishOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_HardSwishOptions ? static_cast<const opencv_tflite::HardSwishOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::IfOptions *builtin_options_as_IfOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_IfOptions ? static_cast<const opencv_tflite::IfOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::WhileOptions *builtin_options_as_WhileOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_WhileOptions ? static_cast<const opencv_tflite::WhileOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::DepthToSpaceOptions *builtin_options_as_DepthToSpaceOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_DepthToSpaceOptions ? static_cast<const opencv_tflite::DepthToSpaceOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::NonMaxSuppressionV4Options *builtin_options_as_NonMaxSuppressionV4Options() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_NonMaxSuppressionV4Options ? static_cast<const opencv_tflite::NonMaxSuppressionV4Options *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::NonMaxSuppressionV5Options *builtin_options_as_NonMaxSuppressionV5Options() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_NonMaxSuppressionV5Options ? static_cast<const opencv_tflite::NonMaxSuppressionV5Options *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ScatterNdOptions *builtin_options_as_ScatterNdOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ScatterNdOptions ? static_cast<const opencv_tflite::ScatterNdOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SelectV2Options *builtin_options_as_SelectV2Options() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SelectV2Options ? static_cast<const opencv_tflite::SelectV2Options *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::DensifyOptions *builtin_options_as_DensifyOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_DensifyOptions ? static_cast<const opencv_tflite::DensifyOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SegmentSumOptions *builtin_options_as_SegmentSumOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SegmentSumOptions ? static_cast<const opencv_tflite::SegmentSumOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::BatchMatMulOptions *builtin_options_as_BatchMatMulOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_BatchMatMulOptions ? static_cast<const opencv_tflite::BatchMatMulOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::CumsumOptions *builtin_options_as_CumsumOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_CumsumOptions ? static_cast<const opencv_tflite::CumsumOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::CallOnceOptions *builtin_options_as_CallOnceOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_CallOnceOptions ? static_cast<const opencv_tflite::CallOnceOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::BroadcastToOptions *builtin_options_as_BroadcastToOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_BroadcastToOptions ? static_cast<const opencv_tflite::BroadcastToOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::Rfft2dOptions *builtin_options_as_Rfft2dOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_Rfft2dOptions ? static_cast<const opencv_tflite::Rfft2dOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::Conv3DOptions *builtin_options_as_Conv3DOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_Conv3DOptions ? static_cast<const opencv_tflite::Conv3DOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::HashtableOptions *builtin_options_as_HashtableOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_HashtableOptions ? static_cast<const opencv_tflite::HashtableOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::HashtableFindOptions *builtin_options_as_HashtableFindOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_HashtableFindOptions ? static_cast<const opencv_tflite::HashtableFindOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::HashtableImportOptions *builtin_options_as_HashtableImportOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_HashtableImportOptions ? static_cast<const opencv_tflite::HashtableImportOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::HashtableSizeOptions *builtin_options_as_HashtableSizeOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_HashtableSizeOptions ? static_cast<const opencv_tflite::HashtableSizeOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::VarHandleOptions *builtin_options_as_VarHandleOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_VarHandleOptions ? static_cast<const opencv_tflite::VarHandleOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ReadVariableOptions *builtin_options_as_ReadVariableOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ReadVariableOptions ? static_cast<const opencv_tflite::ReadVariableOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::AssignVariableOptions *builtin_options_as_AssignVariableOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_AssignVariableOptions ? static_cast<const opencv_tflite::AssignVariableOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::RandomOptions *builtin_options_as_RandomOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_RandomOptions ? static_cast<const opencv_tflite::RandomOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::BucketizeOptions *builtin_options_as_BucketizeOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_BucketizeOptions ? static_cast<const opencv_tflite::BucketizeOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::GeluOptions *builtin_options_as_GeluOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_GeluOptions ? static_cast<const opencv_tflite::GeluOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::DynamicUpdateSliceOptions *builtin_options_as_DynamicUpdateSliceOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_DynamicUpdateSliceOptions ? static_cast<const opencv_tflite::DynamicUpdateSliceOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::UnsortedSegmentProdOptions *builtin_options_as_UnsortedSegmentProdOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_UnsortedSegmentProdOptions ? static_cast<const opencv_tflite::UnsortedSegmentProdOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::UnsortedSegmentMaxOptions *builtin_options_as_UnsortedSegmentMaxOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_UnsortedSegmentMaxOptions ? static_cast<const opencv_tflite::UnsortedSegmentMaxOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::UnsortedSegmentMinOptions *builtin_options_as_UnsortedSegmentMinOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_UnsortedSegmentMinOptions ? static_cast<const opencv_tflite::UnsortedSegmentMinOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::UnsortedSegmentSumOptions *builtin_options_as_UnsortedSegmentSumOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_UnsortedSegmentSumOptions ? static_cast<const opencv_tflite::UnsortedSegmentSumOptions *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::ATan2Options *builtin_options_as_ATan2Options() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_ATan2Options ? static_cast<const opencv_tflite::ATan2Options *>(builtin_options()) : nullptr;
  }
  const opencv_tflite::SignOptions *builtin_options_as_SignOptions() const {
    return builtin_options_type() == opencv_tflite::BuiltinOptions_SignOptions ? static_cast<const opencv_tflite::SignOptions *>(builtin_options()) : nullptr;
  }
  const ::flatbuffers::Vector<uint8_t> *custom_options() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_CUSTOM_OPTIONS);
  }
  opencv_tflite::CustomOptionsFormat custom_options_format() const {
    return static_cast<opencv_tflite::CustomOptionsFormat>(GetField<int8_t>(VT_CUSTOM_OPTIONS_FORMAT, 0));
  }
  const ::flatbuffers::Vector<uint8_t> *mutating_variable_inputs() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_MUTATING_VARIABLE_INPUTS);
  }
  const ::flatbuffers::Vector<int32_t> *intermediates() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_INTERMEDIATES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_OPCODE_INDEX, 4) &&
           VerifyOffset(verifier, VT_INPUTS) &&
           verifier.VerifyVector(inputs()) &&
           VerifyOffset(verifier, VT_OUTPUTS) &&
           verifier.VerifyVector(outputs()) &&
           VerifyField<uint8_t>(verifier, VT_BUILTIN_OPTIONS_TYPE, 1) &&
           VerifyOffset(verifier, VT_BUILTIN_OPTIONS) &&
           VerifyBuiltinOptions(verifier, builtin_options(), builtin_options_type()) &&
           VerifyOffset(verifier, VT_CUSTOM_OPTIONS) &&
           verifier.VerifyVector(custom_options()) &&
           VerifyField<int8_t>(verifier, VT_CUSTOM_OPTIONS_FORMAT, 1) &&
           VerifyOffset(verifier, VT_MUTATING_VARIABLE_INPUTS) &&
           verifier.VerifyVector(mutating_variable_inputs()) &&
           VerifyOffset(verifier, VT_INTERMEDIATES) &&
           verifier.VerifyVector(intermediates()) &&
           verifier.EndTable();
  }
};

template<> inline const opencv_tflite::Conv2DOptions *Operator::builtin_options_as<opencv_tflite::Conv2DOptions>() const {
  return builtin_options_as_Conv2DOptions();
}

template<> inline const opencv_tflite::DepthwiseConv2DOptions *Operator::builtin_options_as<opencv_tflite::DepthwiseConv2DOptions>() const {
  return builtin_options_as_DepthwiseConv2DOptions();
}

template<> inline const opencv_tflite::ConcatEmbeddingsOptions *Operator::builtin_options_as<opencv_tflite::ConcatEmbeddingsOptions>() const {
  return builtin_options_as_ConcatEmbeddingsOptions();
}

template<> inline const opencv_tflite::LSHProjectionOptions *Operator::builtin_options_as<opencv_tflite::LSHProjectionOptions>() const {
  return builtin_options_as_LSHProjectionOptions();
}

template<> inline const opencv_tflite::Pool2DOptions *Operator::builtin_options_as<opencv_tflite::Pool2DOptions>() const {
  return builtin_options_as_Pool2DOptions();
}

template<> inline const opencv_tflite::SVDFOptions *Operator::builtin_options_as<opencv_tflite::SVDFOptions>() const {
  return builtin_options_as_SVDFOptions();
}

template<> inline const opencv_tflite::RNNOptions *Operator::builtin_options_as<opencv_tflite::RNNOptions>() const {
  return builtin_options_as_RNNOptions();
}

template<> inline const opencv_tflite::FullyConnectedOptions *Operator::builtin_options_as<opencv_tflite::FullyConnectedOptions>() const {
  return builtin_options_as_FullyConnectedOptions();
}

template<> inline const opencv_tflite::SoftmaxOptions *Operator::builtin_options_as<opencv_tflite::SoftmaxOptions>() const {
  return builtin_options_as_SoftmaxOptions();
}

template<> inline const opencv_tflite::ConcatenationOptions *Operator::builtin_options_as<opencv_tflite::ConcatenationOptions>() const {
  return builtin_options_as_ConcatenationOptions();
}

template<> inline const opencv_tflite::AddOptions *Operator::builtin_options_as<opencv_tflite::AddOptions>() const {
  return builtin_options_as_AddOptions();
}

template<> inline const opencv_tflite::L2NormOptions *Operator::builtin_options_as<opencv_tflite::L2NormOptions>() const {
  return builtin_options_as_L2NormOptions();
}

template<> inline const opencv_tflite::LocalResponseNormalizationOptions *Operator::builtin_options_as<opencv_tflite::LocalResponseNormalizationOptions>() const {
  return builtin_options_as_LocalResponseNormalizationOptions();
}

template<> inline const opencv_tflite::LSTMOptions *Operator::builtin_options_as<opencv_tflite::LSTMOptions>() const {
  return builtin_options_as_LSTMOptions();
}

template<> inline const opencv_tflite::ResizeBilinearOptions *Operator::builtin_options_as<opencv_tflite::ResizeBilinearOptions>() const {
  return builtin_options_as_ResizeBilinearOptions();
}

template<> inline const opencv_tflite::CallOptions *Operator::builtin_options_as<opencv_tflite::CallOptions>() const {
  return builtin_options_as_CallOptions();
}

template<> inline const opencv_tflite::ReshapeOptions *Operator::builtin_options_as<opencv_tflite::ReshapeOptions>() const {
  return builtin_options_as_ReshapeOptions();
}

template<> inline const opencv_tflite::SkipGramOptions *Operator::builtin_options_as<opencv_tflite::SkipGramOptions>() const {
  return builtin_options_as_SkipGramOptions();
}

template<> inline const opencv_tflite::SpaceToDepthOptions *Operator::builtin_options_as<opencv_tflite::SpaceToDepthOptions>() const {
  return builtin_options_as_SpaceToDepthOptions();
}

template<> inline const opencv_tflite::EmbeddingLookupSparseOptions *Operator::builtin_options_as<opencv_tflite::EmbeddingLookupSparseOptions>() const {
  return builtin_options_as_EmbeddingLookupSparseOptions();
}

template<> inline const opencv_tflite::MulOptions *Operator::builtin_options_as<opencv_tflite::MulOptions>() const {
  return builtin_options_as_MulOptions();
}

template<> inline const opencv_tflite::PadOptions *Operator::builtin_options_as<opencv_tflite::PadOptions>() const {
  return builtin_options_as_PadOptions();
}

template<> inline const opencv_tflite::GatherOptions *Operator::builtin_options_as<opencv_tflite::GatherOptions>() const {
  return builtin_options_as_GatherOptions();
}

template<> inline const opencv_tflite::BatchToSpaceNDOptions *Operator::builtin_options_as<opencv_tflite::BatchToSpaceNDOptions>() const {
  return builtin_options_as_BatchToSpaceNDOptions();
}

template<> inline const opencv_tflite::SpaceToBatchNDOptions *Operator::builtin_options_as<opencv_tflite::SpaceToBatchNDOptions>() const {
  return builtin_options_as_SpaceToBatchNDOptions();
}

template<> inline const opencv_tflite::TransposeOptions *Operator::builtin_options_as<opencv_tflite::TransposeOptions>() const {
  return builtin_options_as_TransposeOptions();
}

template<> inline const opencv_tflite::ReducerOptions *Operator::builtin_options_as<opencv_tflite::ReducerOptions>() const {
  return builtin_options_as_ReducerOptions();
}

template<> inline const opencv_tflite::SubOptions *Operator::builtin_options_as<opencv_tflite::SubOptions>() const {
  return builtin_options_as_SubOptions();
}

template<> inline const opencv_tflite::DivOptions *Operator::builtin_options_as<opencv_tflite::DivOptions>() const {
  return builtin_options_as_DivOptions();
}

template<> inline const opencv_tflite::SqueezeOptions *Operator::builtin_options_as<opencv_tflite::SqueezeOptions>() const {
  return builtin_options_as_SqueezeOptions();
}

template<> inline const opencv_tflite::SequenceRNNOptions *Operator::builtin_options_as<opencv_tflite::SequenceRNNOptions>() const {
  return builtin_options_as_SequenceRNNOptions();
}

template<> inline const opencv_tflite::StridedSliceOptions *Operator::builtin_options_as<opencv_tflite::StridedSliceOptions>() const {
  return builtin_options_as_StridedSliceOptions();
}

template<> inline const opencv_tflite::ExpOptions *Operator::builtin_options_as<opencv_tflite::ExpOptions>() const {
  return builtin_options_as_ExpOptions();
}

template<> inline const opencv_tflite::TopKV2Options *Operator::builtin_options_as<opencv_tflite::TopKV2Options>() const {
  return builtin_options_as_TopKV2Options();
}

template<> inline const opencv_tflite::SplitOptions *Operator::builtin_options_as<opencv_tflite::SplitOptions>() const {
  return builtin_options_as_SplitOptions();
}

template<> inline const opencv_tflite::LogSoftmaxOptions *Operator::builtin_options_as<opencv_tflite::LogSoftmaxOptions>() const {
  return builtin_options_as_LogSoftmaxOptions();
}

template<> inline const opencv_tflite::CastOptions *Operator::builtin_options_as<opencv_tflite::CastOptions>() const {
  return builtin_options_as_CastOptions();
}

template<> inline const opencv_tflite::DequantizeOptions *Operator::builtin_options_as<opencv_tflite::DequantizeOptions>() const {
  return builtin_options_as_DequantizeOptions();
}

template<> inline const opencv_tflite::MaximumMinimumOptions *Operator::builtin_options_as<opencv_tflite::MaximumMinimumOptions>() const {
  return builtin_options_as_MaximumMinimumOptions();
}

template<> inline const opencv_tflite::ArgMaxOptions *Operator::builtin_options_as<opencv_tflite::ArgMaxOptions>() const {
  return builtin_options_as_ArgMaxOptions();
}

template<> inline const opencv_tflite::LessOptions *Operator::builtin_options_as<opencv_tflite::LessOptions>() const {
  return builtin_options_as_LessOptions();
}

template<> inline const opencv_tflite::NegOptions *Operator::builtin_options_as<opencv_tflite::NegOptions>() const {
  return builtin_options_as_NegOptions();
}

template<> inline const opencv_tflite::PadV2Options *Operator::builtin_options_as<opencv_tflite::PadV2Options>() const {
  return builtin_options_as_PadV2Options();
}

template<> inline const opencv_tflite::GreaterOptions *Operator::builtin_options_as<opencv_tflite::GreaterOptions>() const {
  return builtin_options_as_GreaterOptions();
}

template<> inline const opencv_tflite::GreaterEqualOptions *Operator::builtin_options_as<opencv_tflite::GreaterEqualOptions>() const {
  return builtin_options_as_GreaterEqualOptions();
}

template<> inline const opencv_tflite::LessEqualOptions *Operator::builtin_options_as<opencv_tflite::LessEqualOptions>() const {
  return builtin_options_as_LessEqualOptions();
}

template<> inline const opencv_tflite::SelectOptions *Operator::builtin_options_as<opencv_tflite::SelectOptions>() const {
  return builtin_options_as_SelectOptions();
}

template<> inline const opencv_tflite::SliceOptions *Operator::builtin_options_as<opencv_tflite::SliceOptions>() const {
  return builtin_options_as_SliceOptions();
}

template<> inline const opencv_tflite::TransposeConvOptions *Operator::builtin_options_as<opencv_tflite::TransposeConvOptions>() const {
  return builtin_options_as_TransposeConvOptions();
}

template<> inline const opencv_tflite::SparseToDenseOptions *Operator::builtin_options_as<opencv_tflite::SparseToDenseOptions>() const {
  return builtin_options_as_SparseToDenseOptions();
}

template<> inline const opencv_tflite::TileOptions *Operator::builtin_options_as<opencv_tflite::TileOptions>() const {
  return builtin_options_as_TileOptions();
}

template<> inline const opencv_tflite::ExpandDimsOptions *Operator::builtin_options_as<opencv_tflite::ExpandDimsOptions>() const {
  return builtin_options_as_ExpandDimsOptions();
}

template<> inline const opencv_tflite::EqualOptions *Operator::builtin_options_as<opencv_tflite::EqualOptions>() const {
  return builtin_options_as_EqualOptions();
}

template<> inline const opencv_tflite::NotEqualOptions *Operator::builtin_options_as<opencv_tflite::NotEqualOptions>() const {
  return builtin_options_as_NotEqualOptions();
}

template<> inline const opencv_tflite::ShapeOptions *Operator::builtin_options_as<opencv_tflite::ShapeOptions>() const {
  return builtin_options_as_ShapeOptions();
}

template<> inline const opencv_tflite::PowOptions *Operator::builtin_options_as<opencv_tflite::PowOptions>() const {
  return builtin_options_as_PowOptions();
}

template<> inline const opencv_tflite::ArgMinOptions *Operator::builtin_options_as<opencv_tflite::ArgMinOptions>() const {
  return builtin_options_as_ArgMinOptions();
}

template<> inline const opencv_tflite::FakeQuantOptions *Operator::builtin_options_as<opencv_tflite::FakeQuantOptions>() const {
  return builtin_options_as_FakeQuantOptions();
}

template<> inline const opencv_tflite::PackOptions *Operator::builtin_options_as<opencv_tflite::PackOptions>() const {
  return builtin_options_as_PackOptions();
}

template<> inline const opencv_tflite::LogicalOrOptions *Operator::builtin_options_as<opencv_tflite::LogicalOrOptions>() const {
  return builtin_options_as_LogicalOrOptions();
}

template<> inline const opencv_tflite::OneHotOptions *Operator::builtin_options_as<opencv_tflite::OneHotOptions>() const {
  return builtin_options_as_OneHotOptions();
}

template<> inline const opencv_tflite::LogicalAndOptions *Operator::builtin_options_as<opencv_tflite::LogicalAndOptions>() const {
  return builtin_options_as_LogicalAndOptions();
}

template<> inline const opencv_tflite::LogicalNotOptions *Operator::builtin_options_as<opencv_tflite::LogicalNotOptions>() const {
  return builtin_options_as_LogicalNotOptions();
}

template<> inline const opencv_tflite::UnpackOptions *Operator::builtin_options_as<opencv_tflite::UnpackOptions>() const {
  return builtin_options_as_UnpackOptions();
}

template<> inline const opencv_tflite::FloorDivOptions *Operator::builtin_options_as<opencv_tflite::FloorDivOptions>() const {
  return builtin_options_as_FloorDivOptions();
}

template<> inline const opencv_tflite::SquareOptions *Operator::builtin_options_as<opencv_tflite::SquareOptions>() const {
  return builtin_options_as_SquareOptions();
}

template<> inline const opencv_tflite::ZerosLikeOptions *Operator::builtin_options_as<opencv_tflite::ZerosLikeOptions>() const {
  return builtin_options_as_ZerosLikeOptions();
}

template<> inline const opencv_tflite::FillOptions *Operator::builtin_options_as<opencv_tflite::FillOptions>() const {
  return builtin_options_as_FillOptions();
}

template<> inline const opencv_tflite::BidirectionalSequenceLSTMOptions *Operator::builtin_options_as<opencv_tflite::BidirectionalSequenceLSTMOptions>() const {
  return builtin_options_as_BidirectionalSequenceLSTMOptions();
}

template<> inline const opencv_tflite::BidirectionalSequenceRNNOptions *Operator::builtin_options_as<opencv_tflite::BidirectionalSequenceRNNOptions>() const {
  return builtin_options_as_BidirectionalSequenceRNNOptions();
}

template<> inline const opencv_tflite::UnidirectionalSequenceLSTMOptions *Operator::builtin_options_as<opencv_tflite::UnidirectionalSequenceLSTMOptions>() const {
  return builtin_options_as_UnidirectionalSequenceLSTMOptions();
}

template<> inline const opencv_tflite::FloorModOptions *Operator::builtin_options_as<opencv_tflite::FloorModOptions>() const {
  return builtin_options_as_FloorModOptions();
}

template<> inline const opencv_tflite::RangeOptions *Operator::builtin_options_as<opencv_tflite::RangeOptions>() const {
  return builtin_options_as_RangeOptions();
}

template<> inline const opencv_tflite::ResizeNearestNeighborOptions *Operator::builtin_options_as<opencv_tflite::ResizeNearestNeighborOptions>() const {
  return builtin_options_as_ResizeNearestNeighborOptions();
}

template<> inline const opencv_tflite::LeakyReluOptions *Operator::builtin_options_as<opencv_tflite::LeakyReluOptions>() const {
  return builtin_options_as_LeakyReluOptions();
}

template<> inline const opencv_tflite::SquaredDifferenceOptions *Operator::builtin_options_as<opencv_tflite::SquaredDifferenceOptions>() const {
  return builtin_options_as_SquaredDifferenceOptions();
}

template<> inline const opencv_tflite::MirrorPadOptions *Operator::builtin_options_as<opencv_tflite::MirrorPadOptions>() const {
  return builtin_options_as_MirrorPadOptions();
}

template<> inline const opencv_tflite::AbsOptions *Operator::builtin_options_as<opencv_tflite::AbsOptions>() const {
  return builtin_options_as_AbsOptions();
}

template<> inline const opencv_tflite::SplitVOptions *Operator::builtin_options_as<opencv_tflite::SplitVOptions>() const {
  return builtin_options_as_SplitVOptions();
}

template<> inline const opencv_tflite::UniqueOptions *Operator::builtin_options_as<opencv_tflite::UniqueOptions>() const {
  return builtin_options_as_UniqueOptions();
}

template<> inline const opencv_tflite::ReverseV2Options *Operator::builtin_options_as<opencv_tflite::ReverseV2Options>() const {
  return builtin_options_as_ReverseV2Options();
}

template<> inline const opencv_tflite::AddNOptions *Operator::builtin_options_as<opencv_tflite::AddNOptions>() const {
  return builtin_options_as_AddNOptions();
}

template<> inline const opencv_tflite::GatherNdOptions *Operator::builtin_options_as<opencv_tflite::GatherNdOptions>() const {
  return builtin_options_as_GatherNdOptions();
}

template<> inline const opencv_tflite::CosOptions *Operator::builtin_options_as<opencv_tflite::CosOptions>() const {
  return builtin_options_as_CosOptions();
}

template<> inline const opencv_tflite::WhereOptions *Operator::builtin_options_as<opencv_tflite::WhereOptions>() const {
  return builtin_options_as_WhereOptions();
}

template<> inline const opencv_tflite::RankOptions *Operator::builtin_options_as<opencv_tflite::RankOptions>() const {
  return builtin_options_as_RankOptions();
}

template<> inline const opencv_tflite::ReverseSequenceOptions *Operator::builtin_options_as<opencv_tflite::ReverseSequenceOptions>() const {
  return builtin_options_as_ReverseSequenceOptions();
}

template<> inline const opencv_tflite::MatrixDiagOptions *Operator::builtin_options_as<opencv_tflite::MatrixDiagOptions>() const {
  return builtin_options_as_MatrixDiagOptions();
}

template<> inline const opencv_tflite::QuantizeOptions *Operator::builtin_options_as<opencv_tflite::QuantizeOptions>() const {
  return builtin_options_as_QuantizeOptions();
}

template<> inline const opencv_tflite::MatrixSetDiagOptions *Operator::builtin_options_as<opencv_tflite::MatrixSetDiagOptions>() const {
  return builtin_options_as_MatrixSetDiagOptions();
}

template<> inline const opencv_tflite::HardSwishOptions *Operator::builtin_options_as<opencv_tflite::HardSwishOptions>() const {
  return builtin_options_as_HardSwishOptions();
}

template<> inline const opencv_tflite::IfOptions *Operator::builtin_options_as<opencv_tflite::IfOptions>() const {
  return builtin_options_as_IfOptions();
}

template<> inline const opencv_tflite::WhileOptions *Operator::builtin_options_as<opencv_tflite::WhileOptions>() const {
  return builtin_options_as_WhileOptions();
}

template<> inline const opencv_tflite::DepthToSpaceOptions *Operator::builtin_options_as<opencv_tflite::DepthToSpaceOptions>() const {
  return builtin_options_as_DepthToSpaceOptions();
}

template<> inline const opencv_tflite::NonMaxSuppressionV4Options *Operator::builtin_options_as<opencv_tflite::NonMaxSuppressionV4Options>() const {
  return builtin_options_as_NonMaxSuppressionV4Options();
}

template<> inline const opencv_tflite::NonMaxSuppressionV5Options *Operator::builtin_options_as<opencv_tflite::NonMaxSuppressionV5Options>() const {
  return builtin_options_as_NonMaxSuppressionV5Options();
}

template<> inline const opencv_tflite::ScatterNdOptions *Operator::builtin_options_as<opencv_tflite::ScatterNdOptions>() const {
  return builtin_options_as_ScatterNdOptions();
}

template<> inline const opencv_tflite::SelectV2Options *Operator::builtin_options_as<opencv_tflite::SelectV2Options>() const {
  return builtin_options_as_SelectV2Options();
}

template<> inline const opencv_tflite::DensifyOptions *Operator::builtin_options_as<opencv_tflite::DensifyOptions>() const {
  return builtin_options_as_DensifyOptions();
}

template<> inline const opencv_tflite::SegmentSumOptions *Operator::builtin_options_as<opencv_tflite::SegmentSumOptions>() const {
  return builtin_options_as_SegmentSumOptions();
}

template<> inline const opencv_tflite::BatchMatMulOptions *Operator::builtin_options_as<opencv_tflite::BatchMatMulOptions>() const {
  return builtin_options_as_BatchMatMulOptions();
}

template<> inline const opencv_tflite::CumsumOptions *Operator::builtin_options_as<opencv_tflite::CumsumOptions>() const {
  return builtin_options_as_CumsumOptions();
}

template<> inline const opencv_tflite::CallOnceOptions *Operator::builtin_options_as<opencv_tflite::CallOnceOptions>() const {
  return builtin_options_as_CallOnceOptions();
}

template<> inline const opencv_tflite::BroadcastToOptions *Operator::builtin_options_as<opencv_tflite::BroadcastToOptions>() const {
  return builtin_options_as_BroadcastToOptions();
}

template<> inline const opencv_tflite::Rfft2dOptions *Operator::builtin_options_as<opencv_tflite::Rfft2dOptions>() const {
  return builtin_options_as_Rfft2dOptions();
}

template<> inline const opencv_tflite::Conv3DOptions *Operator::builtin_options_as<opencv_tflite::Conv3DOptions>() const {
  return builtin_options_as_Conv3DOptions();
}

template<> inline const opencv_tflite::HashtableOptions *Operator::builtin_options_as<opencv_tflite::HashtableOptions>() const {
  return builtin_options_as_HashtableOptions();
}

template<> inline const opencv_tflite::HashtableFindOptions *Operator::builtin_options_as<opencv_tflite::HashtableFindOptions>() const {
  return builtin_options_as_HashtableFindOptions();
}

template<> inline const opencv_tflite::HashtableImportOptions *Operator::builtin_options_as<opencv_tflite::HashtableImportOptions>() const {
  return builtin_options_as_HashtableImportOptions();
}

template<> inline const opencv_tflite::HashtableSizeOptions *Operator::builtin_options_as<opencv_tflite::HashtableSizeOptions>() const {
  return builtin_options_as_HashtableSizeOptions();
}

template<> inline const opencv_tflite::VarHandleOptions *Operator::builtin_options_as<opencv_tflite::VarHandleOptions>() const {
  return builtin_options_as_VarHandleOptions();
}

template<> inline const opencv_tflite::ReadVariableOptions *Operator::builtin_options_as<opencv_tflite::ReadVariableOptions>() const {
  return builtin_options_as_ReadVariableOptions();
}

template<> inline const opencv_tflite::AssignVariableOptions *Operator::builtin_options_as<opencv_tflite::AssignVariableOptions>() const {
  return builtin_options_as_AssignVariableOptions();
}

template<> inline const opencv_tflite::RandomOptions *Operator::builtin_options_as<opencv_tflite::RandomOptions>() const {
  return builtin_options_as_RandomOptions();
}

template<> inline const opencv_tflite::BucketizeOptions *Operator::builtin_options_as<opencv_tflite::BucketizeOptions>() const {
  return builtin_options_as_BucketizeOptions();
}

template<> inline const opencv_tflite::GeluOptions *Operator::builtin_options_as<opencv_tflite::GeluOptions>() const {
  return builtin_options_as_GeluOptions();
}

template<> inline const opencv_tflite::DynamicUpdateSliceOptions *Operator::builtin_options_as<opencv_tflite::DynamicUpdateSliceOptions>() const {
  return builtin_options_as_DynamicUpdateSliceOptions();
}

template<> inline const opencv_tflite::UnsortedSegmentProdOptions *Operator::builtin_options_as<opencv_tflite::UnsortedSegmentProdOptions>() const {
  return builtin_options_as_UnsortedSegmentProdOptions();
}

template<> inline const opencv_tflite::UnsortedSegmentMaxOptions *Operator::builtin_options_as<opencv_tflite::UnsortedSegmentMaxOptions>() const {
  return builtin_options_as_UnsortedSegmentMaxOptions();
}

template<> inline const opencv_tflite::UnsortedSegmentMinOptions *Operator::builtin_options_as<opencv_tflite::UnsortedSegmentMinOptions>() const {
  return builtin_options_as_UnsortedSegmentMinOptions();
}

template<> inline const opencv_tflite::UnsortedSegmentSumOptions *Operator::builtin_options_as<opencv_tflite::UnsortedSegmentSumOptions>() const {
  return builtin_options_as_UnsortedSegmentSumOptions();
}

template<> inline const opencv_tflite::ATan2Options *Operator::builtin_options_as<opencv_tflite::ATan2Options>() const {
  return builtin_options_as_ATan2Options();
}

template<> inline const opencv_tflite::SignOptions *Operator::builtin_options_as<opencv_tflite::SignOptions>() const {
  return builtin_options_as_SignOptions();
}

struct OperatorBuilder {
  typedef Operator Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_opcode_index(uint32_t opcode_index) {
    fbb_.AddElement<uint32_t>(Operator::VT_OPCODE_INDEX, opcode_index, 0);
  }
  void add_inputs(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> inputs) {
    fbb_.AddOffset(Operator::VT_INPUTS, inputs);
  }
  void add_outputs(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> outputs) {
    fbb_.AddOffset(Operator::VT_OUTPUTS, outputs);
  }
  void add_builtin_options_type(opencv_tflite::BuiltinOptions builtin_options_type) {
    fbb_.AddElement<uint8_t>(Operator::VT_BUILTIN_OPTIONS_TYPE, static_cast<uint8_t>(builtin_options_type), 0);
  }
  void add_builtin_options(::flatbuffers::Offset<void> builtin_options) {
    fbb_.AddOffset(Operator::VT_BUILTIN_OPTIONS, builtin_options);
  }
  void add_custom_options(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> custom_options) {
    fbb_.AddOffset(Operator::VT_CUSTOM_OPTIONS, custom_options);
  }
  void add_custom_options_format(opencv_tflite::CustomOptionsFormat custom_options_format) {
    fbb_.AddElement<int8_t>(Operator::VT_CUSTOM_OPTIONS_FORMAT, static_cast<int8_t>(custom_options_format), 0);
  }
  void add_mutating_variable_inputs(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> mutating_variable_inputs) {
    fbb_.AddOffset(Operator::VT_MUTATING_VARIABLE_INPUTS, mutating_variable_inputs);
  }
  void add_intermediates(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> intermediates) {
    fbb_.AddOffset(Operator::VT_INTERMEDIATES, intermediates);
  }
  explicit OperatorBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Operator> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Operator>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Operator> CreateOperator(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t opcode_index = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> inputs = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> outputs = 0,
    opencv_tflite::BuiltinOptions builtin_options_type = opencv_tflite::BuiltinOptions_NONE,
    ::flatbuffers::Offset<void> builtin_options = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> custom_options = 0,
    opencv_tflite::CustomOptionsFormat custom_options_format = opencv_tflite::CustomOptionsFormat_FLEXBUFFERS,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> mutating_variable_inputs = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> intermediates = 0) {
  OperatorBuilder builder_(_fbb);
  builder_.add_intermediates(intermediates);
  builder_.add_mutating_variable_inputs(mutating_variable_inputs);
  builder_.add_custom_options(custom_options);
  builder_.add_builtin_options(builtin_options);
  builder_.add_outputs(outputs);
  builder_.add_inputs(inputs);
  builder_.add_opcode_index(opcode_index);
  builder_.add_custom_options_format(custom_options_format);
  builder_.add_builtin_options_type(builtin_options_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Operator> CreateOperatorDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t opcode_index = 0,
    const std::vector<int32_t> *inputs = nullptr,
    const std::vector<int32_t> *outputs = nullptr,
    opencv_tflite::BuiltinOptions builtin_options_type = opencv_tflite::BuiltinOptions_NONE,
    ::flatbuffers::Offset<void> builtin_options = 0,
    const std::vector<uint8_t> *custom_options = nullptr,
    opencv_tflite::CustomOptionsFormat custom_options_format = opencv_tflite::CustomOptionsFormat_FLEXBUFFERS,
    const std::vector<uint8_t> *mutating_variable_inputs = nullptr,
    const std::vector<int32_t> *intermediates = nullptr) {
  auto inputs__ = inputs ? _fbb.CreateVector<int32_t>(*inputs) : 0;
  auto outputs__ = outputs ? _fbb.CreateVector<int32_t>(*outputs) : 0;
  auto custom_options__ = custom_options ? _fbb.CreateVector<uint8_t>(*custom_options) : 0;
  auto mutating_variable_inputs__ = mutating_variable_inputs ? _fbb.CreateVector<uint8_t>(*mutating_variable_inputs) : 0;
  auto intermediates__ = intermediates ? _fbb.CreateVector<int32_t>(*intermediates) : 0;
  return opencv_tflite::CreateOperator(
      _fbb,
      opcode_index,
      inputs__,
      outputs__,
      builtin_options_type,
      builtin_options,
      custom_options__,
      custom_options_format,
      mutating_variable_inputs__,
      intermediates__);
}

struct SubGraph FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SubGraphBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TENSORS = 4,
    VT_INPUTS = 6,
    VT_OUTPUTS = 8,
    VT_OPERATORS = 10,
    VT_NAME = 12
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::Tensor>> *tensors() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::Tensor>> *>(VT_TENSORS);
  }
  const ::flatbuffers::Vector<int32_t> *inputs() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_INPUTS);
  }
  const ::flatbuffers::Vector<int32_t> *outputs() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_OUTPUTS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::Operator>> *operators() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::Operator>> *>(VT_OPERATORS);
  }
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_TENSORS) &&
           verifier.VerifyVector(tensors()) &&
           verifier.VerifyVectorOfTables(tensors()) &&
           VerifyOffset(verifier, VT_INPUTS) &&
           verifier.VerifyVector(inputs()) &&
           VerifyOffset(verifier, VT_OUTPUTS) &&
           verifier.VerifyVector(outputs()) &&
           VerifyOffset(verifier, VT_OPERATORS) &&
           verifier.VerifyVector(operators()) &&
           verifier.VerifyVectorOfTables(operators()) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           verifier.EndTable();
  }
};

struct SubGraphBuilder {
  typedef SubGraph Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_tensors(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::Tensor>>> tensors) {
    fbb_.AddOffset(SubGraph::VT_TENSORS, tensors);
  }
  void add_inputs(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> inputs) {
    fbb_.AddOffset(SubGraph::VT_INPUTS, inputs);
  }
  void add_outputs(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> outputs) {
    fbb_.AddOffset(SubGraph::VT_OUTPUTS, outputs);
  }
  void add_operators(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::Operator>>> operators) {
    fbb_.AddOffset(SubGraph::VT_OPERATORS, operators);
  }
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(SubGraph::VT_NAME, name);
  }
  explicit SubGraphBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SubGraph> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SubGraph>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SubGraph> CreateSubGraph(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::Tensor>>> tensors = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> inputs = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> outputs = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::Operator>>> operators = 0,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0) {
  SubGraphBuilder builder_(_fbb);
  builder_.add_name(name);
  builder_.add_operators(operators);
  builder_.add_outputs(outputs);
  builder_.add_inputs(inputs);
  builder_.add_tensors(tensors);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<SubGraph> CreateSubGraphDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<opencv_tflite::Tensor>> *tensors = nullptr,
    const std::vector<int32_t> *inputs = nullptr,
    const std::vector<int32_t> *outputs = nullptr,
    const std::vector<::flatbuffers::Offset<opencv_tflite::Operator>> *operators = nullptr,
    const char *name = nullptr) {
  auto tensors__ = tensors ? _fbb.CreateVector<::flatbuffers::Offset<opencv_tflite::Tensor>>(*tensors) : 0;
  auto inputs__ = inputs ? _fbb.CreateVector<int32_t>(*inputs) : 0;
  auto outputs__ = outputs ? _fbb.CreateVector<int32_t>(*outputs) : 0;
  auto operators__ = operators ? _fbb.CreateVector<::flatbuffers::Offset<opencv_tflite::Operator>>(*operators) : 0;
  auto name__ = name ? _fbb.CreateString(name) : 0;
  return opencv_tflite::CreateSubGraph(
      _fbb,
      tensors__,
      inputs__,
      outputs__,
      operators__,
      name__);
}

struct Buffer FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef BufferBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DATA = 4
  };
  const ::flatbuffers::Vector<uint8_t> *data() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_DATA);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.VerifyVector(data()) &&
           verifier.EndTable();
  }
};

struct BufferBuilder {
  typedef Buffer Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_data(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> data) {
    fbb_.AddOffset(Buffer::VT_DATA, data);
  }
  explicit BufferBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Buffer> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Buffer>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Buffer> CreateBuffer(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> data = 0) {
  BufferBuilder builder_(_fbb);
  builder_.add_data(data);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Buffer> CreateBufferDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *data = nullptr) {
  if (data) { _fbb.ForceVectorAlignment(data->size(), sizeof(uint8_t), 16); }
  auto data__ = data ? _fbb.CreateVector<uint8_t>(*data) : 0;
  return opencv_tflite::CreateBuffer(
      _fbb,
      data__);
}

struct Metadata FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MetadataBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_BUFFER = 6
  };
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  uint32_t buffer() const {
    return GetField<uint32_t>(VT_BUFFER, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<uint32_t>(verifier, VT_BUFFER, 4) &&
           verifier.EndTable();
  }
};

struct MetadataBuilder {
  typedef Metadata Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(Metadata::VT_NAME, name);
  }
  void add_buffer(uint32_t buffer) {
    fbb_.AddElement<uint32_t>(Metadata::VT_BUFFER, buffer, 0);
  }
  explicit MetadataBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Metadata> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Metadata>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Metadata> CreateMetadata(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    uint32_t buffer = 0) {
  MetadataBuilder builder_(_fbb);
  builder_.add_buffer(buffer);
  builder_.add_name(name);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Metadata> CreateMetadataDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    uint32_t buffer = 0) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  return opencv_tflite::CreateMetadata(
      _fbb,
      name__,
      buffer);
}

struct TensorMap FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef TensorMapBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_TENSOR_INDEX = 6
  };
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  uint32_t tensor_index() const {
    return GetField<uint32_t>(VT_TENSOR_INDEX, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<uint32_t>(verifier, VT_TENSOR_INDEX, 4) &&
           verifier.EndTable();
  }
};

struct TensorMapBuilder {
  typedef TensorMap Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(TensorMap::VT_NAME, name);
  }
  void add_tensor_index(uint32_t tensor_index) {
    fbb_.AddElement<uint32_t>(TensorMap::VT_TENSOR_INDEX, tensor_index, 0);
  }
  explicit TensorMapBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<TensorMap> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<TensorMap>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<TensorMap> CreateTensorMap(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    uint32_t tensor_index = 0) {
  TensorMapBuilder builder_(_fbb);
  builder_.add_tensor_index(tensor_index);
  builder_.add_name(name);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<TensorMap> CreateTensorMapDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    uint32_t tensor_index = 0) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  return opencv_tflite::CreateTensorMap(
      _fbb,
      name__,
      tensor_index);
}

struct SignatureDef FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef SignatureDefBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_INPUTS = 4,
    VT_OUTPUTS = 6,
    VT_SIGNATURE_KEY = 8,
    VT_SUBGRAPH_INDEX = 12
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::TensorMap>> *inputs() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::TensorMap>> *>(VT_INPUTS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::TensorMap>> *outputs() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::TensorMap>> *>(VT_OUTPUTS);
  }
  const ::flatbuffers::String *signature_key() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SIGNATURE_KEY);
  }
  uint32_t subgraph_index() const {
    return GetField<uint32_t>(VT_SUBGRAPH_INDEX, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_INPUTS) &&
           verifier.VerifyVector(inputs()) &&
           verifier.VerifyVectorOfTables(inputs()) &&
           VerifyOffset(verifier, VT_OUTPUTS) &&
           verifier.VerifyVector(outputs()) &&
           verifier.VerifyVectorOfTables(outputs()) &&
           VerifyOffset(verifier, VT_SIGNATURE_KEY) &&
           verifier.VerifyString(signature_key()) &&
           VerifyField<uint32_t>(verifier, VT_SUBGRAPH_INDEX, 4) &&
           verifier.EndTable();
  }
};

struct SignatureDefBuilder {
  typedef SignatureDef Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_inputs(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::TensorMap>>> inputs) {
    fbb_.AddOffset(SignatureDef::VT_INPUTS, inputs);
  }
  void add_outputs(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::TensorMap>>> outputs) {
    fbb_.AddOffset(SignatureDef::VT_OUTPUTS, outputs);
  }
  void add_signature_key(::flatbuffers::Offset<::flatbuffers::String> signature_key) {
    fbb_.AddOffset(SignatureDef::VT_SIGNATURE_KEY, signature_key);
  }
  void add_subgraph_index(uint32_t subgraph_index) {
    fbb_.AddElement<uint32_t>(SignatureDef::VT_SUBGRAPH_INDEX, subgraph_index, 0);
  }
  explicit SignatureDefBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<SignatureDef> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<SignatureDef>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<SignatureDef> CreateSignatureDef(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::TensorMap>>> inputs = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::TensorMap>>> outputs = 0,
    ::flatbuffers::Offset<::flatbuffers::String> signature_key = 0,
    uint32_t subgraph_index = 0) {
  SignatureDefBuilder builder_(_fbb);
  builder_.add_subgraph_index(subgraph_index);
  builder_.add_signature_key(signature_key);
  builder_.add_outputs(outputs);
  builder_.add_inputs(inputs);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<SignatureDef> CreateSignatureDefDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<opencv_tflite::TensorMap>> *inputs = nullptr,
    const std::vector<::flatbuffers::Offset<opencv_tflite::TensorMap>> *outputs = nullptr,
    const char *signature_key = nullptr,
    uint32_t subgraph_index = 0) {
  auto inputs__ = inputs ? _fbb.CreateVector<::flatbuffers::Offset<opencv_tflite::TensorMap>>(*inputs) : 0;
  auto outputs__ = outputs ? _fbb.CreateVector<::flatbuffers::Offset<opencv_tflite::TensorMap>>(*outputs) : 0;
  auto signature_key__ = signature_key ? _fbb.CreateString(signature_key) : 0;
  return opencv_tflite::CreateSignatureDef(
      _fbb,
      inputs__,
      outputs__,
      signature_key__,
      subgraph_index);
}

struct Model FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ModelBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VERSION = 4,
    VT_OPERATOR_CODES = 6,
    VT_SUBGRAPHS = 8,
    VT_DESCRIPTION = 10,
    VT_BUFFERS = 12,
    VT_METADATA_BUFFER = 14,
    VT_METADATA = 16,
    VT_SIGNATURE_DEFS = 18
  };
  uint32_t version() const {
    return GetField<uint32_t>(VT_VERSION, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::OperatorCode>> *operator_codes() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::OperatorCode>> *>(VT_OPERATOR_CODES);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::SubGraph>> *subgraphs() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::SubGraph>> *>(VT_SUBGRAPHS);
  }
  const ::flatbuffers::String *description() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::Buffer>> *buffers() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::Buffer>> *>(VT_BUFFERS);
  }
  const ::flatbuffers::Vector<int32_t> *metadata_buffer() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_METADATA_BUFFER);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::Metadata>> *metadata() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::Metadata>> *>(VT_METADATA);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::SignatureDef>> *signature_defs() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::SignatureDef>> *>(VT_SIGNATURE_DEFS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_VERSION, 4) &&
           VerifyOffset(verifier, VT_OPERATOR_CODES) &&
           verifier.VerifyVector(operator_codes()) &&
           verifier.VerifyVectorOfTables(operator_codes()) &&
           VerifyOffset(verifier, VT_SUBGRAPHS) &&
           verifier.VerifyVector(subgraphs()) &&
           verifier.VerifyVectorOfTables(subgraphs()) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyString(description()) &&
           VerifyOffset(verifier, VT_BUFFERS) &&
           verifier.VerifyVector(buffers()) &&
           verifier.VerifyVectorOfTables(buffers()) &&
           VerifyOffset(verifier, VT_METADATA_BUFFER) &&
           verifier.VerifyVector(metadata_buffer()) &&
           VerifyOffset(verifier, VT_METADATA) &&
           verifier.VerifyVector(metadata()) &&
           verifier.VerifyVectorOfTables(metadata()) &&
           VerifyOffset(verifier, VT_SIGNATURE_DEFS) &&
           verifier.VerifyVector(signature_defs()) &&
           verifier.VerifyVectorOfTables(signature_defs()) &&
           verifier.EndTable();
  }
};

struct ModelBuilder {
  typedef Model Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_version(uint32_t version) {
    fbb_.AddElement<uint32_t>(Model::VT_VERSION, version, 0);
  }
  void add_operator_codes(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::OperatorCode>>> operator_codes) {
    fbb_.AddOffset(Model::VT_OPERATOR_CODES, operator_codes);
  }
  void add_subgraphs(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::SubGraph>>> subgraphs) {
    fbb_.AddOffset(Model::VT_SUBGRAPHS, subgraphs);
  }
  void add_description(::flatbuffers::Offset<::flatbuffers::String> description) {
    fbb_.AddOffset(Model::VT_DESCRIPTION, description);
  }
  void add_buffers(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::Buffer>>> buffers) {
    fbb_.AddOffset(Model::VT_BUFFERS, buffers);
  }
  void add_metadata_buffer(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> metadata_buffer) {
    fbb_.AddOffset(Model::VT_METADATA_BUFFER, metadata_buffer);
  }
  void add_metadata(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::Metadata>>> metadata) {
    fbb_.AddOffset(Model::VT_METADATA, metadata);
  }
  void add_signature_defs(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::SignatureDef>>> signature_defs) {
    fbb_.AddOffset(Model::VT_SIGNATURE_DEFS, signature_defs);
  }
  explicit ModelBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Model> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Model>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Model> CreateModel(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t version = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::OperatorCode>>> operator_codes = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::SubGraph>>> subgraphs = 0,
    ::flatbuffers::Offset<::flatbuffers::String> description = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::Buffer>>> buffers = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> metadata_buffer = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::Metadata>>> metadata = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<opencv_tflite::SignatureDef>>> signature_defs = 0) {
  ModelBuilder builder_(_fbb);
  builder_.add_signature_defs(signature_defs);
  builder_.add_metadata(metadata);
  builder_.add_metadata_buffer(metadata_buffer);
  builder_.add_buffers(buffers);
  builder_.add_description(description);
  builder_.add_subgraphs(subgraphs);
  builder_.add_operator_codes(operator_codes);
  builder_.add_version(version);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Model> CreateModelDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t version = 0,
    const std::vector<::flatbuffers::Offset<opencv_tflite::OperatorCode>> *operator_codes = nullptr,
    const std::vector<::flatbuffers::Offset<opencv_tflite::SubGraph>> *subgraphs = nullptr,
    const char *description = nullptr,
    const std::vector<::flatbuffers::Offset<opencv_tflite::Buffer>> *buffers = nullptr,
    const std::vector<int32_t> *metadata_buffer = nullptr,
    const std::vector<::flatbuffers::Offset<opencv_tflite::Metadata>> *metadata = nullptr,
    const std::vector<::flatbuffers::Offset<opencv_tflite::SignatureDef>> *signature_defs = nullptr) {
  auto operator_codes__ = operator_codes ? _fbb.CreateVector<::flatbuffers::Offset<opencv_tflite::OperatorCode>>(*operator_codes) : 0;
  auto subgraphs__ = subgraphs ? _fbb.CreateVector<::flatbuffers::Offset<opencv_tflite::SubGraph>>(*subgraphs) : 0;
  auto description__ = description ? _fbb.CreateString(description) : 0;
  auto buffers__ = buffers ? _fbb.CreateVector<::flatbuffers::Offset<opencv_tflite::Buffer>>(*buffers) : 0;
  auto metadata_buffer__ = metadata_buffer ? _fbb.CreateVector<int32_t>(*metadata_buffer) : 0;
  auto metadata__ = metadata ? _fbb.CreateVector<::flatbuffers::Offset<opencv_tflite::Metadata>>(*metadata) : 0;
  auto signature_defs__ = signature_defs ? _fbb.CreateVector<::flatbuffers::Offset<opencv_tflite::SignatureDef>>(*signature_defs) : 0;
  return opencv_tflite::CreateModel(
      _fbb,
      version,
      operator_codes__,
      subgraphs__,
      description__,
      buffers__,
      metadata_buffer__,
      metadata__,
      signature_defs__);
}

inline bool VerifyQuantizationDetails(::flatbuffers::Verifier &verifier, const void *obj, QuantizationDetails type) {
  switch (type) {
    case QuantizationDetails_NONE: {
      return true;
    }
    case QuantizationDetails_CustomQuantization: {
      auto ptr = reinterpret_cast<const opencv_tflite::CustomQuantization *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyQuantizationDetailsVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyQuantizationDetails(
        verifier,  values->Get(i), types->GetEnum<QuantizationDetails>(i))) {
      return false;
    }
  }
  return true;
}

inline bool VerifySparseIndexVector(::flatbuffers::Verifier &verifier, const void *obj, SparseIndexVector type) {
  switch (type) {
    case SparseIndexVector_NONE: {
      return true;
    }
    case SparseIndexVector_Int32Vector: {
      auto ptr = reinterpret_cast<const opencv_tflite::Int32Vector *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case SparseIndexVector_Uint16Vector: {
      auto ptr = reinterpret_cast<const opencv_tflite::Uint16Vector *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case SparseIndexVector_Uint8Vector: {
      auto ptr = reinterpret_cast<const opencv_tflite::Uint8Vector *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifySparseIndexVectorVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifySparseIndexVector(
        verifier,  values->Get(i), types->GetEnum<SparseIndexVector>(i))) {
      return false;
    }
  }
  return true;
}

inline bool VerifyBuiltinOptions(::flatbuffers::Verifier &verifier, const void *obj, BuiltinOptions type) {
  switch (type) {
    case BuiltinOptions_NONE: {
      return true;
    }
    case BuiltinOptions_Conv2DOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::Conv2DOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_DepthwiseConv2DOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::DepthwiseConv2DOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ConcatEmbeddingsOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::ConcatEmbeddingsOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_LSHProjectionOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::LSHProjectionOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_Pool2DOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::Pool2DOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SVDFOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SVDFOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_RNNOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::RNNOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_FullyConnectedOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::FullyConnectedOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SoftmaxOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SoftmaxOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ConcatenationOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::ConcatenationOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_AddOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::AddOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_L2NormOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::L2NormOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_LocalResponseNormalizationOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::LocalResponseNormalizationOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_LSTMOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::LSTMOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ResizeBilinearOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::ResizeBilinearOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_CallOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::CallOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ReshapeOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::ReshapeOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SkipGramOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SkipGramOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SpaceToDepthOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SpaceToDepthOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_EmbeddingLookupSparseOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::EmbeddingLookupSparseOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_MulOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::MulOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_PadOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::PadOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_GatherOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::GatherOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_BatchToSpaceNDOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::BatchToSpaceNDOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SpaceToBatchNDOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SpaceToBatchNDOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_TransposeOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::TransposeOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ReducerOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::ReducerOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SubOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SubOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_DivOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::DivOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SqueezeOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SqueezeOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SequenceRNNOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SequenceRNNOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_StridedSliceOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::StridedSliceOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ExpOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::ExpOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_TopKV2Options: {
      auto ptr = reinterpret_cast<const opencv_tflite::TopKV2Options *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SplitOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SplitOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_LogSoftmaxOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::LogSoftmaxOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_CastOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::CastOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_DequantizeOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::DequantizeOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_MaximumMinimumOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::MaximumMinimumOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ArgMaxOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::ArgMaxOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_LessOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::LessOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_NegOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::NegOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_PadV2Options: {
      auto ptr = reinterpret_cast<const opencv_tflite::PadV2Options *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_GreaterOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::GreaterOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_GreaterEqualOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::GreaterEqualOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_LessEqualOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::LessEqualOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SelectOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SelectOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SliceOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SliceOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_TransposeConvOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::TransposeConvOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SparseToDenseOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SparseToDenseOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_TileOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::TileOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ExpandDimsOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::ExpandDimsOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_EqualOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::EqualOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_NotEqualOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::NotEqualOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ShapeOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::ShapeOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_PowOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::PowOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ArgMinOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::ArgMinOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_FakeQuantOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::FakeQuantOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_PackOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::PackOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_LogicalOrOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::LogicalOrOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_OneHotOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::OneHotOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_LogicalAndOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::LogicalAndOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_LogicalNotOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::LogicalNotOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_UnpackOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::UnpackOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_FloorDivOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::FloorDivOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SquareOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SquareOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ZerosLikeOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::ZerosLikeOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_FillOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::FillOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_BidirectionalSequenceLSTMOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::BidirectionalSequenceLSTMOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_BidirectionalSequenceRNNOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::BidirectionalSequenceRNNOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_UnidirectionalSequenceLSTMOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::UnidirectionalSequenceLSTMOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_FloorModOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::FloorModOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_RangeOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::RangeOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ResizeNearestNeighborOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::ResizeNearestNeighborOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_LeakyReluOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::LeakyReluOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SquaredDifferenceOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SquaredDifferenceOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_MirrorPadOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::MirrorPadOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_AbsOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::AbsOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SplitVOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SplitVOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_UniqueOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::UniqueOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ReverseV2Options: {
      auto ptr = reinterpret_cast<const opencv_tflite::ReverseV2Options *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_AddNOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::AddNOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_GatherNdOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::GatherNdOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_CosOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::CosOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_WhereOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::WhereOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_RankOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::RankOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ReverseSequenceOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::ReverseSequenceOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_MatrixDiagOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::MatrixDiagOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_QuantizeOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::QuantizeOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_MatrixSetDiagOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::MatrixSetDiagOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_HardSwishOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::HardSwishOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_IfOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::IfOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_WhileOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::WhileOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_DepthToSpaceOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::DepthToSpaceOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_NonMaxSuppressionV4Options: {
      auto ptr = reinterpret_cast<const opencv_tflite::NonMaxSuppressionV4Options *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_NonMaxSuppressionV5Options: {
      auto ptr = reinterpret_cast<const opencv_tflite::NonMaxSuppressionV5Options *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ScatterNdOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::ScatterNdOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SelectV2Options: {
      auto ptr = reinterpret_cast<const opencv_tflite::SelectV2Options *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_DensifyOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::DensifyOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SegmentSumOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SegmentSumOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_BatchMatMulOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::BatchMatMulOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_CumsumOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::CumsumOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_CallOnceOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::CallOnceOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_BroadcastToOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::BroadcastToOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_Rfft2dOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::Rfft2dOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_Conv3DOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::Conv3DOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_HashtableOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::HashtableOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_HashtableFindOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::HashtableFindOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_HashtableImportOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::HashtableImportOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_HashtableSizeOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::HashtableSizeOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_VarHandleOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::VarHandleOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ReadVariableOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::ReadVariableOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_AssignVariableOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::AssignVariableOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_RandomOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::RandomOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_BucketizeOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::BucketizeOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_GeluOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::GeluOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_DynamicUpdateSliceOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::DynamicUpdateSliceOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_UnsortedSegmentProdOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::UnsortedSegmentProdOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_UnsortedSegmentMaxOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::UnsortedSegmentMaxOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_UnsortedSegmentMinOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::UnsortedSegmentMinOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_UnsortedSegmentSumOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::UnsortedSegmentSumOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_ATan2Options: {
      auto ptr = reinterpret_cast<const opencv_tflite::ATan2Options *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case BuiltinOptions_SignOptions: {
      auto ptr = reinterpret_cast<const opencv_tflite::SignOptions *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyBuiltinOptionsVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyBuiltinOptions(
        verifier,  values->Get(i), types->GetEnum<BuiltinOptions>(i))) {
      return false;
    }
  }
  return true;
}

inline const opencv_tflite::Model *GetModel(const void *buf) {
  return ::flatbuffers::GetRoot<opencv_tflite::Model>(buf);
}

inline const opencv_tflite::Model *GetSizePrefixedModel(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<opencv_tflite::Model>(buf);
}

inline const char *ModelIdentifier() {
  return "TFL3";
}

inline bool ModelBufferHasIdentifier(const void *buf) {
  return ::flatbuffers::BufferHasIdentifier(
      buf, ModelIdentifier());
}

inline bool SizePrefixedModelBufferHasIdentifier(const void *buf) {
  return ::flatbuffers::BufferHasIdentifier(
      buf, ModelIdentifier(), true);
}

inline bool VerifyModelBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<opencv_tflite::Model>(ModelIdentifier());
}

inline bool VerifySizePrefixedModelBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<opencv_tflite::Model>(ModelIdentifier());
}

inline const char *ModelExtension() {
  return "tflite";
}

inline void FinishModelBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<opencv_tflite::Model> root) {
  fbb.Finish(root, ModelIdentifier());
}

inline void FinishSizePrefixedModelBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<opencv_tflite::Model> root) {
  fbb.FinishSizePrefixed(root, ModelIdentifier());
}

}  // namespace opencv_tflite

#endif  // FLATBUFFERS_GENERATED_SCHEMA_OPENCV_TFLITE_H_
