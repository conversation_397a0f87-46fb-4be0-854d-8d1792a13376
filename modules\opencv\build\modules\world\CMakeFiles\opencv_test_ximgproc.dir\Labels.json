{"sources": [{"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_adaptive_manifold.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_adaptive_manifold_ref_impl.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_anisodiff.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_bilateral_texture_filter.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_deriche_filter.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_disparity_wls_filter.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_domain_transform.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_edgeboxes.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_edgepreserving_filter.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_fast_hough_transform.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_fbs_filter.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_fgs_filter.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_find_ellipses.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_fld.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_fourier_descriptors.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_guided_filter.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_joint_bilateral_filter.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_l0_smooth.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_main.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_matchcolortemplate.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_niblack_threshold.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_radon_transform.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_ridge_detection_filter.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_rolling_guidance_filter.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_run_length_morphology.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_scansegment.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_slic.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_sparse_match_interpolator.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_structured_edge_detection.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_thinning.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_weighted_median_filter.cpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/test/test_precomp.hpp", "labels": ["Extra", "opencv_ximgproc", "AccuracyTest"]}], "target": {"labels": ["Extra", "opencv_ximgproc", "AccuracyTest"], "name": "opencv_test_ximgproc"}}