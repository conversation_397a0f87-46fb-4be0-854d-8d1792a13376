﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="D:\AI\opencv\cudabuild\CMakeFiles\b395ee7c0999e42287482932580994cb\gen_opencv_objc_source_ios.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\AI\opencv\cudabuild\CMakeFiles\b813899294a05925b323f4dcb8f385f1\gen_opencv_objc_source_ios.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\AI\opencv\opencv-4.10.0\modules\objc\generator\gen_objc.py" />
    <None Include="D:\AI\opencv\opencv-4.10.0\modules\objc\generator\templates\cmakelists.template" />
    <None Include="D:\AI\opencv\cudabuild\modules\objc_bindings_generator\gen_objc.json" />
    <None Include="D:\AI\opencv\cudabuild\modules\objc_bindings_generator\CMakeFiles\gen_opencv_objc_source_ios" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{D15E165F-1928-31B6-B5AD-589C9FB01FC7}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
