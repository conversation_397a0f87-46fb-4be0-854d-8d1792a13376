﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\thread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\bio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\cio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\dwt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\event.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\ht_dec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\image.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\invert.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\j2k.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\jp2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\mct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\mqc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\openjpeg.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\opj_clock.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\pi.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\t1.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\t2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\tcd.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\tgt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\function_list.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\opj_malloc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2\sparse_array.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{2A318434-2169-3E81-A653-C1C0490B5893}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
