﻿  mathfuncs_core.avx2.cpp
  stat.avx2.cpp
  arithm.avx2.cpp
  convert.avx2.cpp
  convert_scale.avx2.cpp
  count_non_zero.avx2.cpp
  has_non_zero.avx2.cpp
  matmul.avx2.cpp
  mean.avx2.cpp
  merge.avx2.cpp
  split.avx2.cpp
  sum.avx2.cpp
  imgwarp.avx2.cpp
  resize.avx2.cpp
  accum.avx2.cpp
  bilateral_filter.avx2.cpp
  box_filter.avx2.cpp
  filter.avx2.cpp
  color_hsv.avx2.cpp
  color_rgb.avx2.cpp
  color_yuv.avx2.cpp
  median_blur.avx2.cpp
  morph.avx2.cpp
  smooth.avx2.cpp
  sumpixels.avx2.cpp
  conv_block.avx2.cpp
  conv_depthwise.avx2.cpp
  conv_winograd_f63.avx2.cpp
  fast_gemm_kernels.avx2.cpp
  fast.avx2.cpp
  sift.avx2.cpp
  undistort.avx2.cpp
  gfluidimgproc_func.avx2.cpp
  gfluidcore_func.avx2.cpp
  layers_common.avx2.cpp
  layers_common.avx2.cpp
  opencv_world_AVX2.vcxproj -> D:\AI\opencv\cudabuild\modules\world\opencv_world_AVX2.dir\Release\opencv_world_AVX2.lib
