﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{1194FD45-04D8-3032-BA5E-534CA63F920C}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ippiw</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\cudabuild\3rdparty\lib\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ippiw.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ippiwd</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\cudabuild\3rdparty\lib\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ippiw.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ippiw</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/AI/opencv/cudabuild" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;IW_BUILD;ICV_BASE;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ProgramDataBaseFileName>D:\AI\opencv\cudabuild\3rdparty\lib\Debug\ippiwd.pdb</ProgramDataBaseFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;IW_BUILD;ICV_BASE;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4221</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/AI/opencv/cudabuild" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;NDEBUG;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;IW_BUILD;ICV_BASE;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ProgramDataBaseFileName>D:\AI\opencv\cudabuild\3rdparty\lib\Release\ippiw.pdb</ProgramDataBaseFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;NDEBUG;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;IW_BUILD;ICV_BASE;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4221</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_core.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_color_convert_all.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_color_convert_rgbs.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_bilateral.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_box.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_canny.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_gaussian.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_general.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_laplacian.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_morphology.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_scharr.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_filter_sobel.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_copy.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_copy_channel.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_copy_make_border.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_copy_merge.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_copy_split.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_scale.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_set.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_set_channel.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_op_swap_channels.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_transform_mirror.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_transform_resize.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_transform_rotate.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_image_transform_warpaffine.c" />
    <ClCompile Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\src\iw_own.c" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw++\iw.hpp" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw++\iw_core.hpp" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw++\iw_image.hpp" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw++\iw_image_color.hpp" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw++\iw_image_filter.hpp" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw++\iw_image_op.hpp" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw++\iw_image_transform.hpp" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw.h" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw_core.h" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw_image.h" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw_image_color.h" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw_image_filter.h" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw_image_op.h" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw_image_transform.h" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw_ll.h" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw\iw_version.h" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw_config.h" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw_own.h" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include\iw_owni.h" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>