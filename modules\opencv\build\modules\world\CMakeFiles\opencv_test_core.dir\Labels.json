{"sources": [{"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/ocl/test_arithm.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/ocl/test_channels.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/ocl/test_dft.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/ocl/test_gemm.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/ocl/test_image2d.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/ocl/test_matrix_expr.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/ocl/test_matrix_operation.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/ocl/test_opencl.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_arithm.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_async.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_concatenation.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_conjugate_gradient.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_countnonzero.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_cuda.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_downhill_simplex.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_ds.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_dxt.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_eigen.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_hal_core.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_hasnonzero.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_intrin.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_intrin_emulator.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_io.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_logtagconfigparser.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_logtagmanager.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_lpsolver.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_main.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_mat.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_math.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_misc.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_opencl.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_operations.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_ptr.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_quaternion.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_rand.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_rotatedrect.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_umat.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_utils.cpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/ref_reduce_arg.impl.hpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_intrin128.simd.hpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_intrin256.simd.hpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_intrin512.simd.hpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_intrin_utils.hpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_precomp.hpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/core/test/test_utils_tls.impl.hpp", "labels": ["Main", "opencv_core", "AccuracyTest"]}, {"file": "D:/AI/opencv/cudabuild/modules/world/opencv_test_core_SSE2.dir/$(Configuration)/test_intrin128.sse2.obj"}, {"file": "D:/AI/opencv/cudabuild/modules/world/opencv_test_core_SSE3.dir/$(Configuration)/test_intrin128.sse3.obj"}, {"file": "D:/AI/opencv/cudabuild/modules/world/opencv_test_core_SSE4_1.dir/$(Configuration)/test_intrin128.ssse3.obj"}, {"file": "D:/AI/opencv/cudabuild/modules/world/opencv_test_core_SSE4_1.dir/$(Configuration)/test_intrin128.sse4_1.obj"}, {"file": "D:/AI/opencv/cudabuild/modules/world/opencv_test_core_SSE4_2.dir/$(Configuration)/test_intrin128.sse4_2.obj"}, {"file": "D:/AI/opencv/cudabuild/modules/world/opencv_test_core_FP16.dir/$(Configuration)/test_intrin128.fp16.obj"}, {"file": "D:/AI/opencv/cudabuild/modules/world/opencv_test_core_AVX.dir/$(Configuration)/test_intrin128.avx.obj"}, {"file": "D:/AI/opencv/cudabuild/modules/world/opencv_test_core_AVX2.dir/$(Configuration)/test_intrin128.avx2.obj"}, {"file": "D:/AI/opencv/cudabuild/modules/world/opencv_test_core_AVX2.dir/$(Configuration)/test_intrin256.avx2.obj"}, {"file": "D:/AI/opencv/cudabuild/modules/world/opencv_test_core_AVX512_SKX.dir/$(Configuration)/test_intrin128.avx512_skx.obj"}, {"file": "D:/AI/opencv/cudabuild/modules/world/opencv_test_core_AVX512_SKX.dir/$(Configuration)/test_intrin256.avx512_skx.obj"}, {"file": "D:/AI/opencv/cudabuild/modules/world/opencv_test_core_AVX512_SKX.dir/$(Configuration)/test_intrin512.avx512_skx.obj"}], "target": {"labels": ["Main", "opencv_core", "AccuracyTest"], "name": "opencv_test_core"}}