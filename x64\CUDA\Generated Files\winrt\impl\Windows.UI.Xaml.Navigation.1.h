// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Navigation_1_H
#define WINRT_Windows_UI_Xaml_Navigation_1_H
#include "winrt/impl/Windows.UI.Xaml.Navigation.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Navigation
{
    struct WINRT_IMPL_EMPTY_BASES IFrameNavigationOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameNavigationOptions>
    {
        IFrameNavigationOptions(std::nullptr_t = nullptr) noexcept {}
        IFrameNavigationOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameNavigationOptionsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameNavigationOptionsFactory>
    {
        IFrameNavigationOptionsFactory(std::nullptr_t = nullptr) noexcept {}
        IFrameNavigationOptionsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigatingCancelEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigatingCancelEventArgs>
    {
        INavigatingCancelEventArgs(std::nullptr_t = nullptr) noexcept {}
        INavigatingCancelEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigatingCancelEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigatingCancelEventArgs2>
    {
        INavigatingCancelEventArgs2(std::nullptr_t = nullptr) noexcept {}
        INavigatingCancelEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationEventArgs>
    {
        INavigationEventArgs(std::nullptr_t = nullptr) noexcept {}
        INavigationEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationEventArgs2>
    {
        INavigationEventArgs2(std::nullptr_t = nullptr) noexcept {}
        INavigationEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationFailedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationFailedEventArgs>
    {
        INavigationFailedEventArgs(std::nullptr_t = nullptr) noexcept {}
        INavigationFailedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPageStackEntry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPageStackEntry>
    {
        IPageStackEntry(std::nullptr_t = nullptr) noexcept {}
        IPageStackEntry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPageStackEntryFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPageStackEntryFactory>
    {
        IPageStackEntryFactory(std::nullptr_t = nullptr) noexcept {}
        IPageStackEntryFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPageStackEntryStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPageStackEntryStatics>
    {
        IPageStackEntryStatics(std::nullptr_t = nullptr) noexcept {}
        IPageStackEntryStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
