set(__OCV_MODE_VARS_DIR "${CMAKE_CURRENT_LIST_DIR}")

macro(ocv_change_mode_var)
  set(__var "${ARGV0}")
  set(__mode "${ARGV1}")
  set(__value "${ARGV2}")
  if(__mode STREQUAL "MODIFIED_ACCESS" AND __value)
    if(NOT __applied_mode_${__var})
      include("${__OCV_MODE_VARS_DIR}/${__var}.cmake")
      set(__applied_mode_${__var} 1)
    else()
      #message("Mode is already applied: ${__var}")
    endif()
  endif()
endmacro()

variable_watch(OPENCV_DISABLE_THREAD_SUPPORT ocv_change_mode_var)
set(OPENCV_DISABLE_THREAD_SUPPORT "${OPENCV_DISABLE_THREAD_SUPPORT}")

variable_watch(OPENCV_SEMIHOSTING ocv_change_mode_var)
set(OPENCV_SEMIHOSTING "${OPENCV_SEMIHOSTING}")
