set(the_description "FreeType module. It enables to draw strings with outlines and mono-bitmaps/gray-bitmaps.")
if(APPLE_FRAMEWORK)
  ocv_module_disable(freetype)
endif()

ocv_check_modules(FREETYPE freetype2)
ocv_check_modules(HARFBUZZ harfbuzz)

if(OPENCV_INITIAL_PASS)
  if(NOT FREETYPE_FOUND)
    message(STATUS "freetype2:   NO")
  else()
    message(STATUS "freetype2:   YES (ver ${FREETYPE_VERSION})")
  endif()

  if(NOT HARFBUZZ_FOUND)
    message(STATUS "harfbuzz:    NO")
  else()
    message(STATUS "harfbuzz:    YES (ver ${HARFBUZZ_VERSION})")
  endif()
endif()

if(FREETYPE_FOUND AND HARFBUZZ_FOUND)
  ocv_define_module(freetype opencv_core opencv_imgproc WRAP python)
  ocv_target_link_libraries(${the_module} ${FREETYPE_LIBRARIES} ${HARFBUZZ_LIBRARIES})
  ocv_include_directories( ${FREETYPE_INCLUDE_DIRS} ${HARFBUZZ_INCLUDE_DIRS} )
else()
  ocv_module_disable(freetype)
endif()
