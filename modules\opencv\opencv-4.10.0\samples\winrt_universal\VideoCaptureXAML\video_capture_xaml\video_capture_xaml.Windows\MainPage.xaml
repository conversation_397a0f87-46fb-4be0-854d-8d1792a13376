﻿<Page x:Class="video_capture_xaml.MainPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="using:video_capture_xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      mc:Ignorable="d">

    <Grid Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
        <TextBlock Margin="20,35,0,0"
                   HorizontalAlignment="Left"
                   VerticalAlignment="Top"
                   FontSize="24"
                   TextWrapping="Wrap">
            <Run Text="OpenCV: videoio implementation using XAML and Universal App Framework" />
            <Run />
        </TextBlock>
        <Image Name="cvImage"
               Width="640"
               Height="480"
               Margin="20,100,0,0"
               HorizontalAlignment="Left"
               VerticalAlignment="Top" />
    </Grid>
</Page>