﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\face\test\test_bif.cpp">
      <Filter>opencv_face\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\face\test\test_face_align.cpp">
      <Filter>opencv_face\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\face\test\test_facemark.cpp">
      <Filter>opencv_face\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\face\test\test_facemark_aam.cpp">
      <Filter>opencv_face\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\face\test\test_facemark_lbf.cpp">
      <Filter>opencv_face\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\face\test\test_loadsave.cpp">
      <Filter>opencv_face\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\face\test\test_mace.cpp">
      <Filter>opencv_face\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\face\test\test_main.cpp">
      <Filter>opencv_face\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\face\test\test_precomp.hpp">
      <Filter>opencv_face\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_face">
      <UniqueIdentifier>{5D74BCB1-F7DF-32F2-AA9A-0C9E018E9727}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_face\Include">
      <UniqueIdentifier>{D41038F9-FE38-3F24-A30E-032AC49A513C}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_face\Src">
      <UniqueIdentifier>{B826AC6E-7E73-3FCC-9D2F-9CF921A60CD3}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
