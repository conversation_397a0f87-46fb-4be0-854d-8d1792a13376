Intelligent Scissors Demo {#tutorial_js_intelligent_scissors}
=========================

Goal
----

- Here you can check how to use IntelligentScissors tool for image segmentation task.
- Available methods and parameters: @ref cv::segmentation::IntelligentScissorsMB

@note The feature is integrated into [CVAT](https://github.com/openvinotoolkit/cvat) annotation tool and you can try it online on https://cvat.org

\htmlonly
<iframe src="../../js_intelligent_scissors.html" width="100%"
        onload="this.style.height=this.contentDocument.body.scrollHeight +'px';">
</iframe>
\endhtmlonly
