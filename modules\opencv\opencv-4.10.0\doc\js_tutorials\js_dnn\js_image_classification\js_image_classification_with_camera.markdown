Image Classification Example with Camera {#tutorial_js_image_classification_with_camera}
=======================================

Goal
----

- In this tutorial you will learn how to use OpenCV.js dnn module for image classification example with camera.

@note  If you don't know how to capture video from camera, please review @ref tutorial_js_video_display.

\htmlonly
<iframe src="../../js_image_classification_with_camera.html" width="100%"
        onload="this.style.height=this.contentDocument.body.scrollHeight +'px';">
</iframe>
\endhtmlonly