// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Media_Devices_2_H
#define WINRT_Windows_Media_Devices_2_H
#include "winrt/impl/Windows.Foundation.1.h"
#include "winrt/impl/Windows.Media.Devices.1.h"
WINRT_EXPORT namespace winrt::Windows::Media::Devices
{
    struct CallControlEventHandler : winrt::Windows::Foundation::IUnknown
    {
        CallControlEventHandler(std::nullptr_t = nullptr) noexcept {}
        CallControlEventHandler(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> CallControlEventHandler(L lambda);
        template <typename F> CallControlEventHandler(F* function);
        template <typename O, typename M> CallControlEventHandler(O* object, M method);
        template <typename O, typename M> CallControlEventHandler(com_ptr<O>&& object, M method);
        template <typename O, typename LM> CallControlEventHandler(weak_ref<O>&& object, LM&& lambda_or_method);
        template <typename O, typename M> CallControlEventHandler(std::shared_ptr<O>&& object, M method);
        template <typename O, typename LM> CallControlEventHandler(std::weak_ptr<O>&& object, LM&& lambda_or_method);
        auto operator()(winrt::Windows::Media::Devices::CallControl const& sender) const;
    };
    struct DialRequestedEventHandler : winrt::Windows::Foundation::IUnknown
    {
        DialRequestedEventHandler(std::nullptr_t = nullptr) noexcept {}
        DialRequestedEventHandler(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> DialRequestedEventHandler(L lambda);
        template <typename F> DialRequestedEventHandler(F* function);
        template <typename O, typename M> DialRequestedEventHandler(O* object, M method);
        template <typename O, typename M> DialRequestedEventHandler(com_ptr<O>&& object, M method);
        template <typename O, typename LM> DialRequestedEventHandler(weak_ref<O>&& object, LM&& lambda_or_method);
        template <typename O, typename M> DialRequestedEventHandler(std::shared_ptr<O>&& object, M method);
        template <typename O, typename LM> DialRequestedEventHandler(std::weak_ptr<O>&& object, LM&& lambda_or_method);
        auto operator()(winrt::Windows::Media::Devices::CallControl const& sender, winrt::Windows::Media::Devices::DialRequestedEventArgs const& e) const;
    };
    struct KeypadPressedEventHandler : winrt::Windows::Foundation::IUnknown
    {
        KeypadPressedEventHandler(std::nullptr_t = nullptr) noexcept {}
        KeypadPressedEventHandler(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> KeypadPressedEventHandler(L lambda);
        template <typename F> KeypadPressedEventHandler(F* function);
        template <typename O, typename M> KeypadPressedEventHandler(O* object, M method);
        template <typename O, typename M> KeypadPressedEventHandler(com_ptr<O>&& object, M method);
        template <typename O, typename LM> KeypadPressedEventHandler(weak_ref<O>&& object, LM&& lambda_or_method);
        template <typename O, typename M> KeypadPressedEventHandler(std::shared_ptr<O>&& object, M method);
        template <typename O, typename LM> KeypadPressedEventHandler(std::weak_ptr<O>&& object, LM&& lambda_or_method);
        auto operator()(winrt::Windows::Media::Devices::CallControl const& sender, winrt::Windows::Media::Devices::KeypadPressedEventArgs const& e) const;
    };
    struct RedialRequestedEventHandler : winrt::Windows::Foundation::IUnknown
    {
        RedialRequestedEventHandler(std::nullptr_t = nullptr) noexcept {}
        RedialRequestedEventHandler(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> RedialRequestedEventHandler(L lambda);
        template <typename F> RedialRequestedEventHandler(F* function);
        template <typename O, typename M> RedialRequestedEventHandler(O* object, M method);
        template <typename O, typename M> RedialRequestedEventHandler(com_ptr<O>&& object, M method);
        template <typename O, typename LM> RedialRequestedEventHandler(weak_ref<O>&& object, LM&& lambda_or_method);
        template <typename O, typename M> RedialRequestedEventHandler(std::shared_ptr<O>&& object, M method);
        template <typename O, typename LM> RedialRequestedEventHandler(std::weak_ptr<O>&& object, LM&& lambda_or_method);
        auto operator()(winrt::Windows::Media::Devices::CallControl const& sender, winrt::Windows::Media::Devices::RedialRequestedEventArgs const& e) const;
    };
    struct WINRT_IMPL_EMPTY_BASES AdvancedPhotoCaptureSettings : winrt::Windows::Media::Devices::IAdvancedPhotoCaptureSettings
    {
        AdvancedPhotoCaptureSettings(std::nullptr_t) noexcept {}
        AdvancedPhotoCaptureSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IAdvancedPhotoCaptureSettings(ptr, take_ownership_from_abi) {}
        AdvancedPhotoCaptureSettings();
    };
    struct WINRT_IMPL_EMPTY_BASES AdvancedPhotoControl : winrt::Windows::Media::Devices::IAdvancedPhotoControl
    {
        AdvancedPhotoControl(std::nullptr_t) noexcept {}
        AdvancedPhotoControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IAdvancedPhotoControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES AudioDeviceController : winrt::Windows::Media::Devices::IAudioDeviceController,
        impl::require<AudioDeviceController, winrt::Windows::Media::Devices::IAudioDeviceController2>
    {
        AudioDeviceController(std::nullptr_t) noexcept {}
        AudioDeviceController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IAudioDeviceController(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES AudioDeviceModule : winrt::Windows::Media::Devices::IAudioDeviceModule
    {
        AudioDeviceModule(std::nullptr_t) noexcept {}
        AudioDeviceModule(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IAudioDeviceModule(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES AudioDeviceModuleNotificationEventArgs : winrt::Windows::Media::Devices::IAudioDeviceModuleNotificationEventArgs
    {
        AudioDeviceModuleNotificationEventArgs(std::nullptr_t) noexcept {}
        AudioDeviceModuleNotificationEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IAudioDeviceModuleNotificationEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES AudioDeviceModulesManager : winrt::Windows::Media::Devices::IAudioDeviceModulesManager
    {
        AudioDeviceModulesManager(std::nullptr_t) noexcept {}
        AudioDeviceModulesManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IAudioDeviceModulesManager(ptr, take_ownership_from_abi) {}
        explicit AudioDeviceModulesManager(param::hstring const& deviceId);
    };
    struct WINRT_IMPL_EMPTY_BASES CallControl : winrt::Windows::Media::Devices::ICallControl
    {
        CallControl(std::nullptr_t) noexcept {}
        CallControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::ICallControl(ptr, take_ownership_from_abi) {}
        static auto GetDefault();
        static auto FromId(param::hstring const& deviceId);
    };
    struct WINRT_IMPL_EMPTY_BASES CameraOcclusionInfo : winrt::Windows::Media::Devices::ICameraOcclusionInfo
    {
        CameraOcclusionInfo(std::nullptr_t) noexcept {}
        CameraOcclusionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::ICameraOcclusionInfo(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES CameraOcclusionState : winrt::Windows::Media::Devices::ICameraOcclusionState
    {
        CameraOcclusionState(std::nullptr_t) noexcept {}
        CameraOcclusionState(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::ICameraOcclusionState(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES CameraOcclusionStateChangedEventArgs : winrt::Windows::Media::Devices::ICameraOcclusionStateChangedEventArgs
    {
        CameraOcclusionStateChangedEventArgs(std::nullptr_t) noexcept {}
        CameraOcclusionStateChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::ICameraOcclusionStateChangedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES DefaultAudioCaptureDeviceChangedEventArgs : winrt::Windows::Media::Devices::IDefaultAudioDeviceChangedEventArgs
    {
        DefaultAudioCaptureDeviceChangedEventArgs(std::nullptr_t) noexcept {}
        DefaultAudioCaptureDeviceChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IDefaultAudioDeviceChangedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES DefaultAudioRenderDeviceChangedEventArgs : winrt::Windows::Media::Devices::IDefaultAudioDeviceChangedEventArgs
    {
        DefaultAudioRenderDeviceChangedEventArgs(std::nullptr_t) noexcept {}
        DefaultAudioRenderDeviceChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IDefaultAudioDeviceChangedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES DialRequestedEventArgs : winrt::Windows::Media::Devices::IDialRequestedEventArgs
    {
        DialRequestedEventArgs(std::nullptr_t) noexcept {}
        DialRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IDialRequestedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES DigitalWindowBounds : winrt::Windows::Media::Devices::IDigitalWindowBounds
    {
        DigitalWindowBounds(std::nullptr_t) noexcept {}
        DigitalWindowBounds(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IDigitalWindowBounds(ptr, take_ownership_from_abi) {}
        DigitalWindowBounds();
    };
    struct WINRT_IMPL_EMPTY_BASES DigitalWindowCapability : winrt::Windows::Media::Devices::IDigitalWindowCapability
    {
        DigitalWindowCapability(std::nullptr_t) noexcept {}
        DigitalWindowCapability(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IDigitalWindowCapability(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES DigitalWindowControl : winrt::Windows::Media::Devices::IDigitalWindowControl
    {
        DigitalWindowControl(std::nullptr_t) noexcept {}
        DigitalWindowControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IDigitalWindowControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ExposureCompensationControl : winrt::Windows::Media::Devices::IExposureCompensationControl
    {
        ExposureCompensationControl(std::nullptr_t) noexcept {}
        ExposureCompensationControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IExposureCompensationControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ExposureControl : winrt::Windows::Media::Devices::IExposureControl
    {
        ExposureControl(std::nullptr_t) noexcept {}
        ExposureControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IExposureControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ExposurePriorityVideoControl : winrt::Windows::Media::Devices::IExposurePriorityVideoControl
    {
        ExposurePriorityVideoControl(std::nullptr_t) noexcept {}
        ExposurePriorityVideoControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IExposurePriorityVideoControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES FlashControl : winrt::Windows::Media::Devices::IFlashControl,
        impl::require<FlashControl, winrt::Windows::Media::Devices::IFlashControl2>
    {
        FlashControl(std::nullptr_t) noexcept {}
        FlashControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IFlashControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES FocusControl : winrt::Windows::Media::Devices::IFocusControl,
        impl::require<FocusControl, winrt::Windows::Media::Devices::IFocusControl2>
    {
        FocusControl(std::nullptr_t) noexcept {}
        FocusControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IFocusControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES FocusSettings : winrt::Windows::Media::Devices::IFocusSettings
    {
        FocusSettings(std::nullptr_t) noexcept {}
        FocusSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IFocusSettings(ptr, take_ownership_from_abi) {}
        FocusSettings();
    };
    struct WINRT_IMPL_EMPTY_BASES HdrVideoControl : winrt::Windows::Media::Devices::IHdrVideoControl
    {
        HdrVideoControl(std::nullptr_t) noexcept {}
        HdrVideoControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IHdrVideoControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES InfraredTorchControl : winrt::Windows::Media::Devices::IInfraredTorchControl
    {
        InfraredTorchControl(std::nullptr_t) noexcept {}
        InfraredTorchControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IInfraredTorchControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IsoSpeedControl : winrt::Windows::Media::Devices::IIsoSpeedControl,
        impl::require<IsoSpeedControl, winrt::Windows::Media::Devices::IIsoSpeedControl2>
    {
        IsoSpeedControl(std::nullptr_t) noexcept {}
        IsoSpeedControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IIsoSpeedControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES KeypadPressedEventArgs : winrt::Windows::Media::Devices::IKeypadPressedEventArgs
    {
        KeypadPressedEventArgs(std::nullptr_t) noexcept {}
        KeypadPressedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IKeypadPressedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES LowLagPhotoControl : winrt::Windows::Media::Devices::ILowLagPhotoControl
    {
        LowLagPhotoControl(std::nullptr_t) noexcept {}
        LowLagPhotoControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::ILowLagPhotoControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES LowLagPhotoSequenceControl : winrt::Windows::Media::Devices::ILowLagPhotoSequenceControl
    {
        LowLagPhotoSequenceControl(std::nullptr_t) noexcept {}
        LowLagPhotoSequenceControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::ILowLagPhotoSequenceControl(ptr, take_ownership_from_abi) {}
    };
    struct MediaDevice
    {
        MediaDevice() = delete;
        static auto GetAudioCaptureSelector();
        static auto GetAudioRenderSelector();
        static auto GetVideoCaptureSelector();
        static auto GetDefaultAudioCaptureId(winrt::Windows::Media::Devices::AudioDeviceRole const& role);
        static auto GetDefaultAudioRenderId(winrt::Windows::Media::Devices::AudioDeviceRole const& role);
        static auto DefaultAudioCaptureDeviceChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Foundation::IInspectable, winrt::Windows::Media::Devices::DefaultAudioCaptureDeviceChangedEventArgs> const& handler);
        using DefaultAudioCaptureDeviceChanged_revoker = impl::factory_event_revoker<winrt::Windows::Media::Devices::IMediaDeviceStatics, &impl::abi_t<winrt::Windows::Media::Devices::IMediaDeviceStatics>::remove_DefaultAudioCaptureDeviceChanged>;
        [[nodiscard]] static auto DefaultAudioCaptureDeviceChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Foundation::IInspectable, winrt::Windows::Media::Devices::DefaultAudioCaptureDeviceChangedEventArgs> const& handler);
        static auto DefaultAudioCaptureDeviceChanged(winrt::event_token const& cookie);
        static auto DefaultAudioRenderDeviceChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Foundation::IInspectable, winrt::Windows::Media::Devices::DefaultAudioRenderDeviceChangedEventArgs> const& handler);
        using DefaultAudioRenderDeviceChanged_revoker = impl::factory_event_revoker<winrt::Windows::Media::Devices::IMediaDeviceStatics, &impl::abi_t<winrt::Windows::Media::Devices::IMediaDeviceStatics>::remove_DefaultAudioRenderDeviceChanged>;
        [[nodiscard]] static auto DefaultAudioRenderDeviceChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Foundation::IInspectable, winrt::Windows::Media::Devices::DefaultAudioRenderDeviceChangedEventArgs> const& handler);
        static auto DefaultAudioRenderDeviceChanged(winrt::event_token const& cookie);
    };
    struct WINRT_IMPL_EMPTY_BASES MediaDeviceControl : winrt::Windows::Media::Devices::IMediaDeviceControl
    {
        MediaDeviceControl(std::nullptr_t) noexcept {}
        MediaDeviceControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IMediaDeviceControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES MediaDeviceControlCapabilities : winrt::Windows::Media::Devices::IMediaDeviceControlCapabilities
    {
        MediaDeviceControlCapabilities(std::nullptr_t) noexcept {}
        MediaDeviceControlCapabilities(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IMediaDeviceControlCapabilities(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ModuleCommandResult : winrt::Windows::Media::Devices::IModuleCommandResult
    {
        ModuleCommandResult(std::nullptr_t) noexcept {}
        ModuleCommandResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IModuleCommandResult(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES OpticalImageStabilizationControl : winrt::Windows::Media::Devices::IOpticalImageStabilizationControl
    {
        OpticalImageStabilizationControl(std::nullptr_t) noexcept {}
        OpticalImageStabilizationControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IOpticalImageStabilizationControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PanelBasedOptimizationControl : winrt::Windows::Media::Devices::IPanelBasedOptimizationControl
    {
        PanelBasedOptimizationControl(std::nullptr_t) noexcept {}
        PanelBasedOptimizationControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IPanelBasedOptimizationControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PhotoConfirmationControl : winrt::Windows::Media::Devices::IPhotoConfirmationControl
    {
        PhotoConfirmationControl(std::nullptr_t) noexcept {}
        PhotoConfirmationControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IPhotoConfirmationControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES RedialRequestedEventArgs : winrt::Windows::Media::Devices::IRedialRequestedEventArgs
    {
        RedialRequestedEventArgs(std::nullptr_t) noexcept {}
        RedialRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IRedialRequestedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES RegionOfInterest : winrt::Windows::Media::Devices::IRegionOfInterest,
        impl::require<RegionOfInterest, winrt::Windows::Media::Devices::IRegionOfInterest2>
    {
        RegionOfInterest(std::nullptr_t) noexcept {}
        RegionOfInterest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IRegionOfInterest(ptr, take_ownership_from_abi) {}
        RegionOfInterest();
    };
    struct WINRT_IMPL_EMPTY_BASES RegionsOfInterestControl : winrt::Windows::Media::Devices::IRegionsOfInterestControl
    {
        RegionsOfInterestControl(std::nullptr_t) noexcept {}
        RegionsOfInterestControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IRegionsOfInterestControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES SceneModeControl : winrt::Windows::Media::Devices::ISceneModeControl
    {
        SceneModeControl(std::nullptr_t) noexcept {}
        SceneModeControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::ISceneModeControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES TorchControl : winrt::Windows::Media::Devices::ITorchControl
    {
        TorchControl(std::nullptr_t) noexcept {}
        TorchControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::ITorchControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES VideoDeviceController : winrt::Windows::Media::Devices::IVideoDeviceController,
        impl::require<VideoDeviceController, winrt::Windows::Media::Devices::IAdvancedVideoCaptureDeviceController, winrt::Windows::Media::Devices::IAdvancedVideoCaptureDeviceController2, winrt::Windows::Media::Devices::IAdvancedVideoCaptureDeviceController3, winrt::Windows::Media::Devices::IAdvancedVideoCaptureDeviceController4, winrt::Windows::Media::Devices::IAdvancedVideoCaptureDeviceController5, winrt::Windows::Media::Devices::IAdvancedVideoCaptureDeviceController6, winrt::Windows::Media::Devices::IAdvancedVideoCaptureDeviceController7, winrt::Windows::Media::Devices::IAdvancedVideoCaptureDeviceController8, winrt::Windows::Media::Devices::IAdvancedVideoCaptureDeviceController9, winrt::Windows::Media::Devices::IAdvancedVideoCaptureDeviceController10, winrt::Windows::Media::Devices::IAdvancedVideoCaptureDeviceController11>
    {
        VideoDeviceController(std::nullptr_t) noexcept {}
        VideoDeviceController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IVideoDeviceController(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES VideoDeviceControllerGetDevicePropertyResult : winrt::Windows::Media::Devices::IVideoDeviceControllerGetDevicePropertyResult
    {
        VideoDeviceControllerGetDevicePropertyResult(std::nullptr_t) noexcept {}
        VideoDeviceControllerGetDevicePropertyResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IVideoDeviceControllerGetDevicePropertyResult(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES VideoTemporalDenoisingControl : winrt::Windows::Media::Devices::IVideoTemporalDenoisingControl
    {
        VideoTemporalDenoisingControl(std::nullptr_t) noexcept {}
        VideoTemporalDenoisingControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IVideoTemporalDenoisingControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES WhiteBalanceControl : winrt::Windows::Media::Devices::IWhiteBalanceControl
    {
        WhiteBalanceControl(std::nullptr_t) noexcept {}
        WhiteBalanceControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IWhiteBalanceControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ZoomControl : winrt::Windows::Media::Devices::IZoomControl,
        impl::require<ZoomControl, winrt::Windows::Media::Devices::IZoomControl2>
    {
        ZoomControl(std::nullptr_t) noexcept {}
        ZoomControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IZoomControl(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ZoomSettings : winrt::Windows::Media::Devices::IZoomSettings
    {
        ZoomSettings(std::nullptr_t) noexcept {}
        ZoomSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Devices::IZoomSettings(ptr, take_ownership_from_abi) {}
        ZoomSettings();
    };
}
#endif
