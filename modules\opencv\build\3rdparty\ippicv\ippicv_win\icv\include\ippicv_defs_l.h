/*
// Copyright 2015 Intel Corporation All Rights Reserved.
//
//
// This software and the related documents are Intel copyrighted materials, and your use of them is governed by
// the express license under which they were provided to you ('License'). Unless the License provides otherwise,
// you may not use, modify, copy, publish, distribute, disclose or transmit this software or the related
// documents without Intel's prior written permission.
// This software and the related documents are provided as is, with no express or implied warranties, other than
// those that are expressly stated in the License.
//
*/

/*
//              Intel(R) Integrated Performance Primitives (Intel(R) IPP)
//              Common Types and Macro Definitions
//
//
*/


#if !defined( __IPPICV_DEFS_L_H__ ) || defined( _OWN_BLDPCS )
#define __IPPICV_DEFS_L_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "ippicv_defs.h"
#include "ippicv_types_l.h"

#ifdef __cplusplus
}
#endif

#endif /* __IPPICV_DEFS_L_H__ */
