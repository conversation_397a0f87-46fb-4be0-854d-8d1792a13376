﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Assets">
      <UniqueIdentifier>{ef9b8c45-f2b7-4eec-a8b4-a9b340be770b}</UniqueIdentifier>
      <Extensions>bmp;fbx;gif;jpg;jpeg;tga;tiff;tif;png</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <Page Include="MainPage.xaml" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="MainPage.xaml.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="MainPage.xaml.h" />
  </ItemGroup>
  <ItemGroup>
    <AppxManifest Include="Package.appxmanifest" />
  </ItemGroup>
  <ItemGroup>
    <None Include="video_capture_xaml.Windows_TemporaryKey.pfx" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="Assets\Logo.scale-100.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SmallLogo.scale-100.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\StoreLogo.scale-100.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SplashScreen.scale-100.png">
      <Filter>Assets</Filter>
    </Image>
  </ItemGroup>
  <ItemGroup>
    <Text Include="readme.txt" />
  </ItemGroup>
  <ItemGroup>
    <Xml Include="Assets\haarcascade_frontalface_alt.xml">
      <Filter>Assets</Filter>
    </Xml>
  </ItemGroup>
</Project>