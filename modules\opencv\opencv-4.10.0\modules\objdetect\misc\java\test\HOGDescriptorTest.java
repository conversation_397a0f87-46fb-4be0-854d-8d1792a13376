package org.opencv.test.objdetect;

import org.opencv.objdetect.HOGDescriptor;
import org.opencv.test.OpenCVTestCase;

public class HOGDescriptorTest extends OpenCVTestCase {

    public void testCheckDetectorSize() {
        fail("Not yet implemented");
    }

    public void testComputeGradientMatMatMat() {
        fail("Not yet implemented");
    }

    public void testComputeGradientMatMatMatSize() {
        fail("Not yet implemented");
    }

    public void testComputeGradientMatMatMatSizeSize() {
        fail("Not yet implemented");
    }

    public void testComputeMatListOfFloat() {
        fail("Not yet implemented");
    }

    public void testComputeMatListOfFloatSize() {
        fail("Not yet implemented");
    }

    public void testComputeMatListOfFloatSizeSize() {
        fail("Not yet implemented");
    }

    public void testComputeMatListOfFloatSizeSizeListOfPoint() {
        fail("Not yet implemented");
    }

    public void testDetectMatListOfPoint() {
        fail("Not yet implemented");
    }

    public void testDetectMatListOfPointDouble() {
        fail("Not yet implemented");
    }

    public void testDetectMatListOfPointDoubleSize() {
        fail("Not yet implemented");
    }

    public void testDetectMatListOfPointDoubleSizeSize() {
        fail("Not yet implemented");
    }

    public void testDetectMatListOfPointDoubleSizeSizeListOfPoint() {
        fail("Not yet implemented");
    }

    public void testDetectMatListOfPointListOfDouble() {
        fail("Not yet implemented");
    }

    public void testDetectMatListOfPointListOfDoubleDouble() {
        fail("Not yet implemented");
    }

    public void testDetectMatListOfPointListOfDoubleDoubleSize() {
        fail("Not yet implemented");
    }

    public void testDetectMatListOfPointListOfDoubleDoubleSizeSize() {
        fail("Not yet implemented");
    }

    public void testDetectMatListOfPointListOfDoubleDoubleSizeSizeListOfPoint() {
        fail("Not yet implemented");
    }

    public void testDetectMultiScaleMatListOfRect() {
        fail("Not yet implemented");
    }

    public void testDetectMultiScaleMatListOfRectDouble() {
        fail("Not yet implemented");
    }

    public void testDetectMultiScaleMatListOfRectDoubleSize() {
        fail("Not yet implemented");
    }

    public void testDetectMultiScaleMatListOfRectDoubleSizeSize() {
        fail("Not yet implemented");
    }

    public void testDetectMultiScaleMatListOfRectDoubleSizeSizeDouble() {
        fail("Not yet implemented");
    }

    public void testDetectMultiScaleMatListOfRectDoubleSizeSizeDoubleDouble() {
        fail("Not yet implemented");
    }

    public void testDetectMultiScaleMatListOfRectDoubleSizeSizeDoubleDoubleBoolean() {
        fail("Not yet implemented");
    }

    public void testDetectMultiScaleMatListOfRectListOfDouble() {
        fail("Not yet implemented");
    }

    public void testDetectMultiScaleMatListOfRectListOfDoubleDouble() {
        fail("Not yet implemented");
    }

    public void testDetectMultiScaleMatListOfRectListOfDoubleDoubleSize() {
        fail("Not yet implemented");
    }

    public void testDetectMultiScaleMatListOfRectListOfDoubleDoubleSizeSize() {
        fail("Not yet implemented");
    }

    public void testDetectMultiScaleMatListOfRectListOfDoubleDoubleSizeSizeDouble() {
        fail("Not yet implemented");
    }

    public void testDetectMultiScaleMatListOfRectListOfDoubleDoubleSizeSizeDoubleDouble() {
        fail("Not yet implemented");
    }

    public void testDetectMultiScaleMatListOfRectListOfDoubleDoubleSizeSizeDoubleDoubleBoolean() {
        fail("Not yet implemented");
    }

    public void testGet_blockSize() {
        fail("Not yet implemented");
    }

    public void testGet_blockStride() {
        fail("Not yet implemented");
    }

    public void testGet_cellSize() {
        fail("Not yet implemented");
    }

    public void testGet_derivAperture() {
        fail("Not yet implemented");
    }

    public void testGet_gammaCorrection() {
        fail("Not yet implemented");
    }

    public void testGet_histogramNormType() {
        fail("Not yet implemented");
    }

    public void testGet_L2HysThreshold() {
        fail("Not yet implemented");
    }

    public void testGet_nbins() {
        fail("Not yet implemented");
    }

    public void testGet_nlevels() {
        fail("Not yet implemented");
    }

    public void testGet_svmDetector() {
        fail("Not yet implemented");
    }

    public void testGet_winSigma() {
        fail("Not yet implemented");
    }

    public void testGet_winSize() {
        fail("Not yet implemented");
    }

    public void testGetDaimlerPeopleDetector() {
        fail("Not yet implemented");
    }

    public void testGetDefaultPeopleDetector() {
        fail("Not yet implemented");
    }

    public void testGetDescriptorSize() {
        fail("Not yet implemented");
    }

    public void testGetWinSigma() {
        fail("Not yet implemented");
    }

    public void testHOGDescriptor() {
        HOGDescriptor hog = new HOGDescriptor();

        assertNotNull(hog);
        assertEquals(HOGDescriptor.DEFAULT_NLEVELS, hog.get_nlevels());
    }

    public void testHOGDescriptorSizeSizeSizeSizeInt() {
        fail("Not yet implemented");
    }

    public void testHOGDescriptorSizeSizeSizeSizeIntInt() {
        fail("Not yet implemented");
    }

    public void testHOGDescriptorSizeSizeSizeSizeIntIntDouble() {
        fail("Not yet implemented");
    }

    public void testHOGDescriptorSizeSizeSizeSizeIntIntDoubleInt() {
        fail("Not yet implemented");
    }

    public void testHOGDescriptorSizeSizeSizeSizeIntIntDoubleIntDouble() {
        fail("Not yet implemented");
    }

    public void testHOGDescriptorSizeSizeSizeSizeIntIntDoubleIntDoubleBoolean() {
        fail("Not yet implemented");
    }

    public void testHOGDescriptorSizeSizeSizeSizeIntIntDoubleIntDoubleBooleanInt() {
        fail("Not yet implemented");
    }

    public void testHOGDescriptorString() {
        fail("Not yet implemented");
    }

    public void testLoadString() {
        fail("Not yet implemented");
    }

    public void testLoadStringString() {
        fail("Not yet implemented");
    }

    public void testSaveString() {
        fail("Not yet implemented");
    }

    public void testSaveStringString() {
        fail("Not yet implemented");
    }

    public void testSetSVMDetector() {
        fail("Not yet implemented");
    }

}
