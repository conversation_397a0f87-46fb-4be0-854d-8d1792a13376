// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Management_Setup_0_H
#define WINRT_Windows_Management_Setup_0_H
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    struct EventRegistrationToken;
    template <typename T> struct WINRT_IMPL_EMPTY_BASES IReference;
    template <typename TSender, typename TResult> struct WINRT_IMPL_EMPTY_BASES TypedEventHandler;
    struct Uri;
}
WINRT_EXPORT namespace winrt::Windows::Management::Setup
{
    enum class DeploymentAgentProgressState : int32_t
    {
        NotStarted = 0,
        Initializing = 1,
        InProgress = 2,
        Completed = 3,
        ErrorOccurred = 4,
        RebootRequired = 5,
        Canceled = 6,
    };
    enum class DeploymentSessionConnectionChange : int32_t
    {
        NoChange = 0,
        HostConnectionLost = 1,
        HostConnectionRestored = 2,
        AgentConnectionLost = 3,
        AgentConnectionRestored = 4,
        InternetConnectionLost = 5,
        InternetConnectionRestored = 6,
    };
    enum class DeploymentSessionStateChange : int32_t
    {
        NoChange = 0,
        CancelRequestedByUser = 1,
        RetryRequestedByUser = 2,
    };
    enum class DeploymentWorkloadState : int32_t
    {
        NotStarted = 0,
        InProgress = 1,
        Completed = 2,
        Failed = 3,
        Canceled = 4,
        Skipped = 5,
        Uninstalled = 6,
        RebootRequired = 7,
    };
    struct IAgentProvisioningProgressReport;
    struct IDeploymentSessionConnectionChangedEventArgs;
    struct IDeploymentSessionHeartbeatRequestedEventArgs;
    struct IDeploymentSessionStateChangedEventArgs;
    struct IDeploymentWorkload;
    struct IDeploymentWorkloadBatch;
    struct IDeploymentWorkloadBatchFactory;
    struct IDeploymentWorkloadFactory;
    struct IDevicePreparationExecutionContext;
    struct IMachineProvisioningProgressReporter;
    struct IMachineProvisioningProgressReporterStatics;
    struct AgentProvisioningProgressReport;
    struct DeploymentSessionConnectionChangedEventArgs;
    struct DeploymentSessionHeartbeatRequestedEventArgs;
    struct DeploymentSessionStateChangedEventArgs;
    struct DeploymentWorkload;
    struct DeploymentWorkloadBatch;
    struct DevicePreparationExecutionContext;
    struct MachineProvisioningProgressReporter;
    struct DeploymentSessionHeartbeatRequested;
}
namespace winrt::impl
{
    template <> struct category<winrt::Windows::Management::Setup::IAgentProvisioningProgressReport>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Setup::IDeploymentSessionConnectionChangedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Setup::IDeploymentSessionHeartbeatRequestedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Setup::IDeploymentSessionStateChangedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Setup::IDeploymentWorkload>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Setup::IDeploymentWorkloadBatch>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Setup::IDeploymentWorkloadBatchFactory>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Setup::IDeploymentWorkloadFactory>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Setup::IDevicePreparationExecutionContext>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Setup::IMachineProvisioningProgressReporter>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Setup::IMachineProvisioningProgressReporterStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Setup::AgentProvisioningProgressReport>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Setup::DeploymentSessionConnectionChangedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Setup::DeploymentSessionHeartbeatRequestedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Setup::DeploymentSessionStateChangedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Setup::DeploymentWorkload>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Setup::DeploymentWorkloadBatch>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Setup::DevicePreparationExecutionContext>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Setup::MachineProvisioningProgressReporter>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Setup::DeploymentAgentProgressState>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Management::Setup::DeploymentSessionConnectionChange>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Management::Setup::DeploymentSessionStateChange>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Management::Setup::DeploymentWorkloadState>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Management::Setup::DeploymentSessionHeartbeatRequested>{ using type = delegate_category; };
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::AgentProvisioningProgressReport> = L"Windows.Management.Setup.AgentProvisioningProgressReport";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::DeploymentSessionConnectionChangedEventArgs> = L"Windows.Management.Setup.DeploymentSessionConnectionChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::DeploymentSessionHeartbeatRequestedEventArgs> = L"Windows.Management.Setup.DeploymentSessionHeartbeatRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::DeploymentSessionStateChangedEventArgs> = L"Windows.Management.Setup.DeploymentSessionStateChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::DeploymentWorkload> = L"Windows.Management.Setup.DeploymentWorkload";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::DeploymentWorkloadBatch> = L"Windows.Management.Setup.DeploymentWorkloadBatch";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::DevicePreparationExecutionContext> = L"Windows.Management.Setup.DevicePreparationExecutionContext";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::MachineProvisioningProgressReporter> = L"Windows.Management.Setup.MachineProvisioningProgressReporter";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::DeploymentAgentProgressState> = L"Windows.Management.Setup.DeploymentAgentProgressState";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::DeploymentSessionConnectionChange> = L"Windows.Management.Setup.DeploymentSessionConnectionChange";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::DeploymentSessionStateChange> = L"Windows.Management.Setup.DeploymentSessionStateChange";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::DeploymentWorkloadState> = L"Windows.Management.Setup.DeploymentWorkloadState";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::IAgentProvisioningProgressReport> = L"Windows.Management.Setup.IAgentProvisioningProgressReport";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::IDeploymentSessionConnectionChangedEventArgs> = L"Windows.Management.Setup.IDeploymentSessionConnectionChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::IDeploymentSessionHeartbeatRequestedEventArgs> = L"Windows.Management.Setup.IDeploymentSessionHeartbeatRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::IDeploymentSessionStateChangedEventArgs> = L"Windows.Management.Setup.IDeploymentSessionStateChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::IDeploymentWorkload> = L"Windows.Management.Setup.IDeploymentWorkload";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::IDeploymentWorkloadBatch> = L"Windows.Management.Setup.IDeploymentWorkloadBatch";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::IDeploymentWorkloadBatchFactory> = L"Windows.Management.Setup.IDeploymentWorkloadBatchFactory";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::IDeploymentWorkloadFactory> = L"Windows.Management.Setup.IDeploymentWorkloadFactory";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::IDevicePreparationExecutionContext> = L"Windows.Management.Setup.IDevicePreparationExecutionContext";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::IMachineProvisioningProgressReporter> = L"Windows.Management.Setup.IMachineProvisioningProgressReporter";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::IMachineProvisioningProgressReporterStatics> = L"Windows.Management.Setup.IMachineProvisioningProgressReporterStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Setup::DeploymentSessionHeartbeatRequested> = L"Windows.Management.Setup.DeploymentSessionHeartbeatRequested";
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Setup::IAgentProvisioningProgressReport>{ 0x5097398A,0x70CC,0x5181,{ 0xA7,0xAF,0xD3,0x1C,0x16,0x73,0x23,0xD1 } }; // 5097398A-70CC-5181-A7AF-D31C167323D1
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Setup::IDeploymentSessionConnectionChangedEventArgs>{ 0x8D40C631,0x6E4B,0x5D59,{ 0x92,0xF8,0x0D,0xE5,0x4C,0x2A,0x3C,0x6B } }; // 8D40C631-6E4B-5D59-92F8-0DE54C2A3C6B
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Setup::IDeploymentSessionHeartbeatRequestedEventArgs>{ 0x09D81FA0,0x1036,0x58E6,{ 0xB6,0x3B,0xFE,0x34,0x3C,0x45,0x00,0x5F } }; // 09D81FA0-1036-58E6-B63B-FE343C45005F
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Setup::IDeploymentSessionStateChangedEventArgs>{ 0xFBD3B7F3,0x88CB,0x5703,{ 0xB8,0xA5,0x02,0x18,0xDE,0x8F,0xED,0x81 } }; // FBD3B7F3-88CB-5703-B8A5-0218DE8FED81
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Setup::IDeploymentWorkload>{ 0x1CEFD3D4,0x456C,0x50D1,{ 0x93,0x12,0xCC,0x5C,0x81,0x8F,0xC1,0x2E } }; // 1CEFD3D4-456C-50D1-9312-CC5C818FC12E
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Setup::IDeploymentWorkloadBatch>{ 0x5E56E3DF,0xB9C0,0x5FEE,{ 0xBA,0x3F,0xE8,0x9D,0x80,0x0A,0x9B,0xF2 } }; // 5E56E3DF-B9C0-5FEE-BA3F-E89D800A9BF2
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Setup::IDeploymentWorkloadBatchFactory>{ 0xD0209697,0x9560,0x5A05,{ 0xBD,0xF6,0xF1,0xAF,0x53,0x5C,0xB0,0xD4 } }; // D0209697-9560-5A05-BDF6-F1AF535CB0D4
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Setup::IDeploymentWorkloadFactory>{ 0x41426C72,0x22A3,0x5339,{ 0xBD,0xF1,0x51,0x26,0x81,0x69,0xAA,0x61 } }; // 41426C72-22A3-5339-BDF1-51268169AA61
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Setup::IDevicePreparationExecutionContext>{ 0x084F221B,0x2484,0x5E81,{ 0xA4,0xE7,0x83,0xF6,0xCA,0xF1,0x9D,0xC4 } }; // 084F221B-2484-5E81-A4E7-83F6CAF19DC4
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Setup::IMachineProvisioningProgressReporter>{ 0xEBD8677F,0xDFD2,0x59DA,{ 0xAC,0x3D,0x75,0x3E,0xE1,0x66,0x7C,0xBB } }; // EBD8677F-DFD2-59DA-AC3D-753EE1667CBB
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Setup::IMachineProvisioningProgressReporterStatics>{ 0x77682C17,0x5DA3,0x51FC,{ 0xA0,0x42,0xC7,0xB5,0x34,0x58,0xDD,0xB5 } }; // 77682C17-5DA3-51FC-A042-C7B53458DDB5
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Setup::DeploymentSessionHeartbeatRequested>{ 0xC94A770B,0x5B05,0x4595,{ 0x9E,0x69,0x79,0x07,0x04,0x84,0x37,0x7E } }; // C94A770B-5B05-4595-9E69-79070484377E
    template <> struct default_interface<winrt::Windows::Management::Setup::AgentProvisioningProgressReport>{ using type = winrt::Windows::Management::Setup::IAgentProvisioningProgressReport; };
    template <> struct default_interface<winrt::Windows::Management::Setup::DeploymentSessionConnectionChangedEventArgs>{ using type = winrt::Windows::Management::Setup::IDeploymentSessionConnectionChangedEventArgs; };
    template <> struct default_interface<winrt::Windows::Management::Setup::DeploymentSessionHeartbeatRequestedEventArgs>{ using type = winrt::Windows::Management::Setup::IDeploymentSessionHeartbeatRequestedEventArgs; };
    template <> struct default_interface<winrt::Windows::Management::Setup::DeploymentSessionStateChangedEventArgs>{ using type = winrt::Windows::Management::Setup::IDeploymentSessionStateChangedEventArgs; };
    template <> struct default_interface<winrt::Windows::Management::Setup::DeploymentWorkload>{ using type = winrt::Windows::Management::Setup::IDeploymentWorkload; };
    template <> struct default_interface<winrt::Windows::Management::Setup::DeploymentWorkloadBatch>{ using type = winrt::Windows::Management::Setup::IDeploymentWorkloadBatch; };
    template <> struct default_interface<winrt::Windows::Management::Setup::DevicePreparationExecutionContext>{ using type = winrt::Windows::Management::Setup::IDevicePreparationExecutionContext; };
    template <> struct default_interface<winrt::Windows::Management::Setup::MachineProvisioningProgressReporter>{ using type = winrt::Windows::Management::Setup::IMachineProvisioningProgressReporter; };
    template <> struct abi<winrt::Windows::Management::Setup::IAgentProvisioningProgressReport>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_State(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_State(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_ProgressPercentage(double*) noexcept = 0;
            virtual int32_t __stdcall put_ProgressPercentage(double) noexcept = 0;
            virtual int32_t __stdcall get_EstimatedTimeRemaining(int64_t*) noexcept = 0;
            virtual int32_t __stdcall put_EstimatedTimeRemaining(int64_t) noexcept = 0;
            virtual int32_t __stdcall get_DisplayProgress(void**) noexcept = 0;
            virtual int32_t __stdcall put_DisplayProgress(void*) noexcept = 0;
            virtual int32_t __stdcall get_DisplayProgressSecondary(void**) noexcept = 0;
            virtual int32_t __stdcall put_DisplayProgressSecondary(void*) noexcept = 0;
            virtual int32_t __stdcall get_Batches(void**) noexcept = 0;
            virtual int32_t __stdcall get_CurrentBatchIndex(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_CurrentBatchIndex(uint32_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Setup::IDeploymentSessionConnectionChangedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_SessionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_Change(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Setup::IDeploymentSessionHeartbeatRequestedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Handled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Handled(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Setup::IDeploymentSessionStateChangedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_SessionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_Change(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Setup::IDeploymentWorkload>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Id(void**) noexcept = 0;
            virtual int32_t __stdcall get_DisplayFriendlyName(void**) noexcept = 0;
            virtual int32_t __stdcall put_DisplayFriendlyName(void*) noexcept = 0;
            virtual int32_t __stdcall get_StartTime(void**) noexcept = 0;
            virtual int32_t __stdcall put_StartTime(void*) noexcept = 0;
            virtual int32_t __stdcall get_EndTime(void**) noexcept = 0;
            virtual int32_t __stdcall put_EndTime(void*) noexcept = 0;
            virtual int32_t __stdcall get_ErrorCode(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_ErrorCode(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_ErrorMessage(void**) noexcept = 0;
            virtual int32_t __stdcall put_ErrorMessage(void*) noexcept = 0;
            virtual int32_t __stdcall get_PossibleCause(void**) noexcept = 0;
            virtual int32_t __stdcall put_PossibleCause(void*) noexcept = 0;
            virtual int32_t __stdcall get_PossibleResolution(void**) noexcept = 0;
            virtual int32_t __stdcall put_PossibleResolution(void*) noexcept = 0;
            virtual int32_t __stdcall get_State(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_State(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_StateDetails(void**) noexcept = 0;
            virtual int32_t __stdcall put_StateDetails(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Setup::IDeploymentWorkloadBatch>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Id(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall get_DisplayCategoryTitle(void**) noexcept = 0;
            virtual int32_t __stdcall put_DisplayCategoryTitle(void*) noexcept = 0;
            virtual int32_t __stdcall get_BatchWorkloads(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Setup::IDeploymentWorkloadBatchFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(uint32_t, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Setup::IDeploymentWorkloadFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Setup::IDevicePreparationExecutionContext>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Context(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Setup::IMachineProvisioningProgressReporter>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_SessionId(winrt::guid*) noexcept = 0;
            virtual int32_t __stdcall get_SessionConnection(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_SessionState(int32_t*) noexcept = 0;
            virtual int32_t __stdcall add_SessionStateChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_SessionStateChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_SessionConnectionChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_SessionConnectionChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall ReportProgress(void*) noexcept = 0;
            virtual int32_t __stdcall GetDevicePreparationExecutionContextAsync(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Setup::IMachineProvisioningProgressReporterStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetForLaunchUri(void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Setup::DeploymentSessionHeartbeatRequested>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Windows_Management_Setup_IAgentProvisioningProgressReport
    {
        [[nodiscard]] auto State() const;
        auto State(winrt::Windows::Management::Setup::DeploymentAgentProgressState const& value) const;
        [[nodiscard]] auto ProgressPercentage() const;
        auto ProgressPercentage(double value) const;
        [[nodiscard]] auto EstimatedTimeRemaining() const;
        auto EstimatedTimeRemaining(winrt::Windows::Foundation::TimeSpan const& value) const;
        [[nodiscard]] auto DisplayProgress() const;
        auto DisplayProgress(param::hstring const& value) const;
        [[nodiscard]] auto DisplayProgressSecondary() const;
        auto DisplayProgressSecondary(param::hstring const& value) const;
        [[nodiscard]] auto Batches() const;
        [[nodiscard]] auto CurrentBatchIndex() const;
        auto CurrentBatchIndex(uint32_t value) const;
    };
    template <> struct consume<winrt::Windows::Management::Setup::IAgentProvisioningProgressReport>
    {
        template <typename D> using type = consume_Windows_Management_Setup_IAgentProvisioningProgressReport<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Setup_IDeploymentSessionConnectionChangedEventArgs
    {
        [[nodiscard]] auto SessionId() const;
        [[nodiscard]] auto Change() const;
    };
    template <> struct consume<winrt::Windows::Management::Setup::IDeploymentSessionConnectionChangedEventArgs>
    {
        template <typename D> using type = consume_Windows_Management_Setup_IDeploymentSessionConnectionChangedEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Setup_IDeploymentSessionHeartbeatRequestedEventArgs
    {
        [[nodiscard]] auto Handled() const;
        auto Handled(bool value) const;
    };
    template <> struct consume<winrt::Windows::Management::Setup::IDeploymentSessionHeartbeatRequestedEventArgs>
    {
        template <typename D> using type = consume_Windows_Management_Setup_IDeploymentSessionHeartbeatRequestedEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Setup_IDeploymentSessionStateChangedEventArgs
    {
        [[nodiscard]] auto SessionId() const;
        [[nodiscard]] auto Change() const;
    };
    template <> struct consume<winrt::Windows::Management::Setup::IDeploymentSessionStateChangedEventArgs>
    {
        template <typename D> using type = consume_Windows_Management_Setup_IDeploymentSessionStateChangedEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Setup_IDeploymentWorkload
    {
        [[nodiscard]] auto Id() const;
        [[nodiscard]] auto DisplayFriendlyName() const;
        auto DisplayFriendlyName(param::hstring const& value) const;
        [[nodiscard]] auto StartTime() const;
        auto StartTime(winrt::Windows::Foundation::IReference<winrt::Windows::Foundation::DateTime> const& value) const;
        [[nodiscard]] auto EndTime() const;
        auto EndTime(winrt::Windows::Foundation::IReference<winrt::Windows::Foundation::DateTime> const& value) const;
        [[nodiscard]] auto ErrorCode() const;
        auto ErrorCode(uint32_t value) const;
        [[nodiscard]] auto ErrorMessage() const;
        auto ErrorMessage(param::hstring const& value) const;
        [[nodiscard]] auto PossibleCause() const;
        auto PossibleCause(param::hstring const& value) const;
        [[nodiscard]] auto PossibleResolution() const;
        auto PossibleResolution(param::hstring const& value) const;
        [[nodiscard]] auto State() const;
        auto State(winrt::Windows::Management::Setup::DeploymentWorkloadState const& value) const;
        [[nodiscard]] auto StateDetails() const;
        auto StateDetails(param::hstring const& value) const;
    };
    template <> struct consume<winrt::Windows::Management::Setup::IDeploymentWorkload>
    {
        template <typename D> using type = consume_Windows_Management_Setup_IDeploymentWorkload<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Setup_IDeploymentWorkloadBatch
    {
        [[nodiscard]] auto Id() const;
        [[nodiscard]] auto DisplayCategoryTitle() const;
        auto DisplayCategoryTitle(param::hstring const& value) const;
        [[nodiscard]] auto BatchWorkloads() const;
    };
    template <> struct consume<winrt::Windows::Management::Setup::IDeploymentWorkloadBatch>
    {
        template <typename D> using type = consume_Windows_Management_Setup_IDeploymentWorkloadBatch<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Setup_IDeploymentWorkloadBatchFactory
    {
        auto CreateInstance(uint32_t id) const;
    };
    template <> struct consume<winrt::Windows::Management::Setup::IDeploymentWorkloadBatchFactory>
    {
        template <typename D> using type = consume_Windows_Management_Setup_IDeploymentWorkloadBatchFactory<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Setup_IDeploymentWorkloadFactory
    {
        auto CreateInstance(param::hstring const& id) const;
    };
    template <> struct consume<winrt::Windows::Management::Setup::IDeploymentWorkloadFactory>
    {
        template <typename D> using type = consume_Windows_Management_Setup_IDeploymentWorkloadFactory<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Setup_IDevicePreparationExecutionContext
    {
        [[nodiscard]] auto Context() const;
    };
    template <> struct consume<winrt::Windows::Management::Setup::IDevicePreparationExecutionContext>
    {
        template <typename D> using type = consume_Windows_Management_Setup_IDevicePreparationExecutionContext<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Setup_IMachineProvisioningProgressReporter
    {
        [[nodiscard]] auto SessionId() const;
        [[nodiscard]] auto SessionConnection() const;
        [[nodiscard]] auto SessionState() const;
        auto SessionStateChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Management::Setup::MachineProvisioningProgressReporter, winrt::Windows::Management::Setup::DeploymentSessionStateChangedEventArgs> const& handler) const;
        using SessionStateChanged_revoker = impl::event_revoker<winrt::Windows::Management::Setup::IMachineProvisioningProgressReporter, &impl::abi_t<winrt::Windows::Management::Setup::IMachineProvisioningProgressReporter>::remove_SessionStateChanged>;
        [[nodiscard]] auto SessionStateChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Management::Setup::MachineProvisioningProgressReporter, winrt::Windows::Management::Setup::DeploymentSessionStateChangedEventArgs> const& handler) const;
        auto SessionStateChanged(winrt::event_token const& token) const noexcept;
        auto SessionConnectionChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Management::Setup::MachineProvisioningProgressReporter, winrt::Windows::Management::Setup::DeploymentSessionConnectionChangedEventArgs> const& handler) const;
        using SessionConnectionChanged_revoker = impl::event_revoker<winrt::Windows::Management::Setup::IMachineProvisioningProgressReporter, &impl::abi_t<winrt::Windows::Management::Setup::IMachineProvisioningProgressReporter>::remove_SessionConnectionChanged>;
        [[nodiscard]] auto SessionConnectionChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Management::Setup::MachineProvisioningProgressReporter, winrt::Windows::Management::Setup::DeploymentSessionConnectionChangedEventArgs> const& handler) const;
        auto SessionConnectionChanged(winrt::event_token const& token) const noexcept;
        auto ReportProgress(winrt::Windows::Management::Setup::AgentProvisioningProgressReport const& updateReport) const;
        auto GetDevicePreparationExecutionContextAsync() const;
    };
    template <> struct consume<winrt::Windows::Management::Setup::IMachineProvisioningProgressReporter>
    {
        template <typename D> using type = consume_Windows_Management_Setup_IMachineProvisioningProgressReporter<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Setup_IMachineProvisioningProgressReporterStatics
    {
        auto GetForLaunchUri(winrt::Windows::Foundation::Uri const& launchUri, winrt::Windows::Management::Setup::DeploymentSessionHeartbeatRequested const& heartbeatHandler) const;
    };
    template <> struct consume<winrt::Windows::Management::Setup::IMachineProvisioningProgressReporterStatics>
    {
        template <typename D> using type = consume_Windows_Management_Setup_IMachineProvisioningProgressReporterStatics<D>;
    };
}
#endif
