<?xml version="1.0"?>
<!--
    Tree-based 20x20 right eye detector.
    The detector is trained by 6665 positive samples from FERET, VALID and BioID face databases. 
    Created by <PERSON><PERSON> (http://yushiqi.cn/research/eyedetection).

////////////////////////////////////////////////////////////////////////////////////////

  IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.

  By downloading, copying, installing or using the software you agree to this license.
  If you do not agree to this license, do not download, install,
  copy or use the software.


                        Intel License Agreement
                For Open Source Computer Vision Library

 Copyright (C) 2000, Intel Corporation, all rights reserved.
 Third party copyrights are property of their respective owners.

 Redistribution and use in source and binary forms, with or without modification,
 are permitted provided that the following conditions are met:

   * Redistribution's of source code must retain the above copyright notice,
     this list of conditions and the following disclaimer.

   * Redistribution's in binary form must reproduce the above copyright notice,
     this list of conditions and the following disclaimer in the documentation
     and/or other materials provided with the distribution.

   * The name of Intel Corporation may not be used to endorse or promote products
     derived from this software without specific prior written permission.

 This software is provided by the copyright holders and contributors "as is" and
 any express or implied warranties, including, but not limited to, the implied
 warranties of merchantability and fitness for a particular purpose are disclaimed.
 In no event shall the Intel Corporation or contributors be liable for any direct,
 indirect, incidental, special, exemplary, or consequential damages
 (including, but not limited to, procurement of substitute goods or services;
 loss of use, data, or profits; or business interruption) however caused
 and on any theory of liability, whether in contract, strict liability,
 or tort (including negligence or otherwise) arising in any way out of
 the use of this software, even if advised of the possibility of such damage.
-->
<opencv_storage>
<cascade type_id="opencv-cascade-classifier"><stageType>BOOST</stageType>
  <featureType>HAAR</featureType>
  <height>20</height>
  <width>20</width>
  <stageParams>
    <maxWeakCount>34</maxWeakCount></stageParams>
  <featureParams>
    <maxCatCount>0</maxCatCount></featureParams>
  <stageNum>20</stageNum>
  <stages>
    <_>
      <maxWeakCount>5</maxWeakCount>
      <stageThreshold>-2.2325520515441895e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 0 -4.8210550099611282e-02 -1 -2 1
            -4.1576199233531952e-02</internalNodes>
          <leafValues>
            -8.6140447854995728e-01 9.1769057512283325e-01
            -2.1284009516239166e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 2 9.3528684228658676e-03 -1 -2 3 -2.2144919785205275e-04</internalNodes>
          <leafValues>
            -6.9785767793655396e-01 7.9523372650146484e-01
            -4.8948091268539429e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 4 -2.1853350102901459e-02 -1 -2 5 9.9672928452491760e-02</internalNodes>
          <leafValues>
            7.0574641227722168e-01 -7.0666241645812988e-01
            7.9210978746414185e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 6 -2.1664820611476898e-02 -1 -2 7
            -7.5680727604776621e-04</internalNodes>
          <leafValues>
            -6.0898607969284058e-01 7.1685701608657837e-01
            -3.0464568734169006e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 8 -1.3333049602806568e-02 -1 -2 9 9.2925298959016800e-03</internalNodes>
          <leafValues>
            -4.6844691038131714e-01 6.4235931634902954e-01
            -5.1180428266525269e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>5</maxWeakCount>
      <stageThreshold>-2.1598019599914551e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 1 10 -3.3948719501495361e-01 -1 -2 11
            -1.3672479987144470e-01</internalNodes>
          <leafValues>
            7.7913260459899902e-01 2.6421278715133667e-01
            -8.7910091876983643e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 12 3.1394500285387039e-02 -1 -2 13
            -1.0828140191733837e-02</internalNodes>
          <leafValues>
            -6.9956701993942261e-01 7.6504492759704590e-01
            -4.3719211220741272e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 14 -4.2506768368184566e-03 -1 -2 15
            -2.2675469517707825e-02</internalNodes>
          <leafValues>
            -5.7561582326889038e-01 7.4080592393875122e-01
            -3.6677250266075134e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 16 3.9161480963230133e-02 -1 -2 17
            -3.1934089493006468e-03</internalNodes>
          <leafValues>
            6.4045161008834839e-01 1.6047589480876923e-01
            -7.1010977029800415e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 18 2.5321990251541138e-02 -1 -2 19
            7.7583367237821221e-04</internalNodes>
          <leafValues>
            4.9574860930442810e-01 -7.1737897396087646e-01
            -1.8581770360469818e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>8</maxWeakCount>
      <stageThreshold>-2.3451159000396729e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 20 -2.6554059982299805e-01 -1 -2 21
            -2.2532779723405838e-02</internalNodes>
          <leafValues>
            -8.4712451696395874e-01 8.7977188825607300e-01
            -3.3394691348075867e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 22 8.5310067515820265e-04 -1 -2 23
            1.5820249973330647e-04</internalNodes>
          <leafValues>
            -8.2032448053359985e-01 -7.5176358222961426e-01
            6.7769712209701538e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 24 -1.0837490117410198e-04 -1 -2 25
            2.6810260023921728e-03</internalNodes>
          <leafValues>
            -8.3314001560211182e-01 5.3844749927520752e-01
            -7.6534157991409302e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 26 8.5202371701598167e-04 -1 -2 27
            -1.2241739779710770e-02</internalNodes>
          <leafValues>
            -7.7514898777008057e-01 6.3240152597427368e-01
            -6.3395208120346069e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 28 6.2314196838997304e-05 -1 -2 29
            -7.1911108493804932e-01</internalNodes>
          <leafValues>
            4.4290411472320557e-01 8.0135929584503174e-01
            -5.3431099653244019e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 30 -2.4280339479446411e-02 -1 -2 31
            3.4558640327304602e-03</internalNodes>
          <leafValues>
            -6.7797917127609253e-01 4.9030610918998718e-01
            -8.8447982072830200e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 32 -6.2993327446747571e-05 -1 -2 33
            -4.6443562023341656e-03</internalNodes>
          <leafValues>
            -5.7883417606353760e-01 -8.5878807306289673e-01
            5.2454602718353271e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 34 -4.0299328247783706e-05 -1 -2 35
            -3.7485519424080849e-03</internalNodes>
          <leafValues>
            -5.2713459730148315e-01 -8.5626190900802612e-01
            4.8944610357284546e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>10</maxWeakCount>
      <stageThreshold>-2.3431489467620850e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 1 36 -3.8377079367637634e-01 -1 -2 37
            -1.3837030529975891e-01</internalNodes>
          <leafValues>
            7.1715021133422852e-01 3.4392359852790833e-01
            -7.9931277036666870e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 38 3.3107071067206562e-04 -1 -2 39
            -5.1273438148200512e-03</internalNodes>
          <leafValues>
            -6.8352431058883667e-01 5.8250617980957031e-01
            -4.0955001115798950e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 40 -2.6100680232048035e-02 -1 -2 41
            -1.0628979653120041e-03</internalNodes>
          <leafValues>
            -4.3713301420211792e-01 7.0680737495422363e-01
            -2.6817938685417175e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 42 -9.7854852676391602e-02 -1 -2 43
            -1.1829820275306702e-01</internalNodes>
          <leafValues>
            7.3940038681030273e-01 6.3814181089401245e-01
            -3.8721871376037598e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 44 -7.5409049168229103e-03 -1 -2 45
            2.6851659640669823e-03</internalNodes>
          <leafValues>
            -4.8803019523620605e-01 3.9083468914031982e-01
            -6.5561538934707642e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 46 1.6870240215212107e-03 -1 -2 47
            -3.8136160001158714e-03</internalNodes>
          <leafValues>
            -4.9891749024391174e-01 -6.6405588388442993e-01
            4.0650749206542969e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 48 2.0289309322834015e-03 -1 -2 49
            -7.6308869756758213e-03</internalNodes>
          <leafValues>
            -6.9989210367202759e-01 4.3206840753555298e-01
            -2.9664969444274902e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 50 -3.3815231290645897e-04 -1 -2 51
            7.5163291767239571e-03</internalNodes>
          <leafValues>
            -4.6808540821075439e-01 3.6521491408348083e-01
            -7.6014542579650879e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 52 6.1479508876800537e-02 -1 -2 53
            -4.6286579221487045e-02</internalNodes>
          <leafValues>
            5.6990629434585571e-01 2.2625060379505157e-01
            -4.5330780744552612e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 54 4.6903551556169987e-03 -1 -2 55
            1.8803169950842857e-03</internalNodes>
          <leafValues>
            -7.7286708354949951e-01 2.7349120378494263e-01
            -6.6667830944061279e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>8</maxWeakCount>
      <stageThreshold>-2.1268370151519775e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 56 -5.5420672893524170e-01 -1 -2 57
            -6.9329799152910709e-03</internalNodes>
          <leafValues>
            -6.0620260238647461e-01 7.8542029857635498e-01
            -3.5522121191024780e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 58 -2.1169960498809814e-02 -1 -2 59
            -6.7428398132324219e-01</internalNodes>
          <leafValues>
            5.2947688102722168e-01 4.6065220236778259e-01
            -7.0058208703994751e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 60 -4.2725078761577606e-02 -1 -2 61
            -1.0109329596161842e-02</internalNodes>
          <leafValues>
            -5.9904807806015015e-01 6.8109220266342163e-01
            -2.0731879770755768e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 62 6.5861130133271217e-03 -1 -2 63
            -7.6380418613553047e-03</internalNodes>
          <leafValues>
            -5.2420848608016968e-01 -7.0169782638549805e-01
            4.4100138545036316e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 64 -9.7681581974029541e-02 -1 -2 65
            1.0197360068559647e-02</internalNodes>
          <leafValues>
            5.7708740234375000e-01 -9.8518550395965576e-02
            -8.8111698627471924e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 66 -2.5724549777805805e-03 -1 -2 67
            2.6594230439513922e-03</internalNodes>
          <leafValues>
            -8.3233338594436646e-01 3.0995351076126099e-01
            -8.1609177589416504e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 68 -1.0042720241472125e-03 -1 -2 69
            2.6080000679939985e-03</internalNodes>
          <leafValues>
            -4.3558520078659058e-01 3.3566600084304810e-01
            -8.1889331340789795e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 70 4.9724509008228779e-03 -1 -2 71
            1.2243240140378475e-02</internalNodes>
          <leafValues>
            -7.7048182487487793e-01 2.2534200549125671e-01
            -6.8695551156997681e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>10</maxWeakCount>
      <stageThreshold>-2.0604379177093506e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 72 -5.7784929871559143e-02 -1 -2 73
            -1.7517809756100178e-03</internalNodes>
          <leafValues>
            -7.0516008138656616e-01 8.5655921697616577e-01
            -9.2403419315814972e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 74 -1.1522379703819752e-02 -1 -2 75
            -3.8323760963976383e-03</internalNodes>
          <leafValues>
            -4.2749640345573425e-01 7.5913530588150024e-01
            -1.0894049704074860e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 76 -8.0922387540340424e-02 -1 -2 77
            -6.2537011690437794e-03</internalNodes>
          <leafValues>
            -3.1364768743515015e-01 6.9995921850204468e-01
            -1.1805690079927444e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 78 -1.2227860093116760e-01 -1 -2 79
            -6.4168110489845276e-02</internalNodes>
          <leafValues>
            5.2072501182556152e-01 3.9272749423980713e-01
            -4.2194411158561707e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 80 -5.3712888620793819e-04 -1 -2 81
            -2.8175620827823877e-03</internalNodes>
          <leafValues>
            -4.9524548649787903e-01 4.1350141167640686e-01
            -3.8919278979301453e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 82 -3.6368549335747957e-03 -1 -2 83
            -1.3223909772932529e-03</internalNodes>
          <leafValues>
            6.7615020275115967e-01 4.3426999449729919e-01
            -3.7642130255699158e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 84 3.7143539520911872e-04 -1 -2 85
            -5.0255712121725082e-03</internalNodes>
          <leafValues>
            -5.5630880594253540e-01 -5.2328592538833618e-01
            3.4646821022033691e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 86 -9.2711612523999065e-05 -1 -2 87
            1.9847028888761997e-03</internalNodes>
          <leafValues>
            -4.9652668833732605e-01 3.3401641249656677e-01
            -6.2446892261505127e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 88 4.7203440219163895e-02 -1 -2 89
            -6.8562600063160062e-05</internalNodes>
          <leafValues>
            5.7562619447708130e-01 2.6172660291194916e-02
            -6.0849070549011230e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 90 7.5034219771623611e-03 -1 -2 91
            6.3834791071712971e-03</internalNodes>
          <leafValues>
            -6.8576759099960327e-01 -1.7312510311603546e-01
            3.8560429215431213e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>12</maxWeakCount>
      <stageThreshold>-2.3187489509582520e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 92 -1.5584450215101242e-02 -1 -2 93
            1.4557019807398319e-02</internalNodes>
          <leafValues>
            -6.6648960113525391e-01 -4.3745130300521851e-01
            7.2227817773818970e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 94 -5.7889888994395733e-03 -1 -2 95
            -8.1936769187450409e-02</internalNodes>
          <leafValues>
            -4.3183240294456482e-01 6.8467652797698975e-01
            -2.2546729445457458e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 96 -4.2995368130505085e-03 -1 -2 97
            -1.3736640103161335e-02</internalNodes>
          <leafValues>
            -5.2409631013870239e-01 6.1626207828521729e-01
            -3.5893160104751587e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 98 -4.8069912008941174e-03 -1 -2 99
            -7.7131099998950958e-02</internalNodes>
          <leafValues>
            -4.2382389307022095e-01 6.0599362850189209e-01
            -3.1555330753326416e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 100 4.4640208943746984e-04 -1 -2 101
            3.4841578453779221e-02</internalNodes>
          <leafValues>
            -4.9206110835075378e-01 -4.1017889976501465e-02
            6.1330878734588623e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 102 8.2969048526138067e-04 -1 -2 103
            -7.8510129242204130e-05</internalNodes>
          <leafValues>
            -4.5479419827461243e-01 4.0007328987121582e-01
            -2.0888769626617432e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 104 4.6054688282310963e-03 -1 -2 105
            -7.1904482319951057e-03</internalNodes>
          <leafValues>
            -6.7931377887725830e-01 4.7060671448707581e-01
            -1.4138610661029816e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 106 -5.5724480189383030e-03 -1 -2 107
            -7.0458237314596772e-04</internalNodes>
          <leafValues>
            -7.0525509119033813e-01 3.6097851395606995e-01
            -1.8361540138721466e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 108 1.8595060333609581e-02 -1 -2 109
            5.0072550773620605e-02</internalNodes>
          <leafValues>
            4.1765761375427246e-01 -4.1869449615478516e-01
            2.8186509013175964e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 110 -2.0355919376015663e-02 -1 -2 111
            -2.8686519712209702e-02</internalNodes>
          <leafValues>
            -3.6494150757789612e-01 -5.3867787122726440e-01
            3.4767881035804749e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 112 -7.1101690991781652e-05 -1 -2 113
            2.0686469506472349e-03</internalNodes>
          <leafValues>
            -4.0156790614128113e-01 3.2963660359382629e-01
            -7.0951050519943237e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 114 1.1430920567363501e-03 -1 -2 115
            -8.8636036962270737e-03</internalNodes>
          <leafValues>
            4.4172981381416321e-01 1.8426130712032318e-01
            -4.1275170445442200e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>15</maxWeakCount>
      <stageThreshold>-2.2203750610351562e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 116 -7.7637642621994019e-02 -1 -2 117
            -8.4830820560455322e-03</internalNodes>
          <leafValues>
            -4.9321529269218445e-01 7.8138542175292969e-01
            -3.6062291264533997e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 118 -1.7180460272356868e-03 -1 -2 119
            2.4740949273109436e-02</internalNodes>
          <leafValues>
            -4.7690048813819885e-01 -3.2420080900192261e-01
            5.9280002117156982e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 120 3.3028100151568651e-03 -1 -2 121
            -3.4622039645910263e-02</internalNodes>
          <leafValues>
            -5.3991597890853882e-01 5.2076727151870728e-01
            -3.3530798554420471e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 122 -7.1505777304992080e-04 -1 -2 123
            -9.0145105496048927e-03</internalNodes>
          <leafValues>
            -4.8981699347496033e-01 -7.7969801425933838e-01
            3.6586359143257141e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 124 -1.0250939521938562e-03 -1 -2 125
            -5.5693178437650204e-03</internalNodes>
          <leafValues>
            -4.6970510482788086e-01 -6.9695621728897095e-01
            3.5025438666343689e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 126 1.3235070509836078e-03 -1 -2 127
            -3.3737940248101950e-03</internalNodes>
          <leafValues>
            -4.4707980751991272e-01 -5.6195151805877686e-01
            3.1833809614181519e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 128 -6.4095242123585194e-05 -1 -2 129
            -2.7294119354337454e-03</internalNodes>
          <leafValues>
            -3.5473638772964478e-01 4.1285240650177002e-01
            -3.1416821479797363e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 130 6.3087652961257845e-05 -1 -2 131
            -1.5436099842190742e-02</internalNodes>
          <leafValues>
            -3.5946568846702576e-01 -6.1329078674316406e-01
            3.4301999211311340e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 132 -2.1025019232183695e-03 -1 -2 133
            -1.6849569976329803e-02</internalNodes>
          <leafValues>
            -7.6962250471115112e-01 3.6569809913635254e-01
            -2.1210379898548126e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 134 5.6847798987291753e-05 -1 -2 135
            5.9984489344060421e-03</internalNodes>
          <leafValues>
            -4.0466558933258057e-01 2.8503778576850891e-01
            -5.8756178617477417e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 136 6.1389962211251259e-03 -1 -2 137
            -2.8117469628341496e-04</internalNodes>
          <leafValues>
            -8.7189829349517822e-01 2.5182509422302246e-01
            -3.1868219375610352e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 138 -4.5429798774421215e-03 -1 -2 139
            -3.2167110592126846e-02</internalNodes>
          <leafValues>
            -3.6724218726158142e-01 -7.9481202363967896e-01
            2.8887200355529785e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 140 5.0912089645862579e-03 -1 -2 141
            -1.5173070132732391e-03</internalNodes>
          <leafValues>
            -7.1477490663528442e-01 4.4514629244804382e-01
            -9.5207341015338898e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 142 -6.0079508693888783e-04 -1 -2 143
            4.4868541881442070e-03</internalNodes>
          <leafValues>
            -3.6021450161933899e-01 2.8276360034942627e-01
            -7.2084128856658936e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 144 -3.7957848981022835e-03 -1 -2 145
            -9.1829998418688774e-03</internalNodes>
          <leafValues>
            -2.8717440366744995e-01 5.0479042530059814e-01
            -7.0781037211418152e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>17</maxWeakCount>
      <stageThreshold>-2.1757249832153320e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 146 -5.5760249495506287e-02 -1 -2 147
            -5.9436690062284470e-02</internalNodes>
          <leafValues>
            -5.5854648351669312e-01 6.8943697214126587e-01
            -3.7195080518722534e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 148 -5.4637178778648376e-02 -1 -2 149
            2.3608359694480896e-01</internalNodes>
          <leafValues>
            5.3040331602096558e-01 -4.7355309128761292e-01
            4.6322488784790039e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 150 -9.4560505822300911e-03 -1 -2 151
            -5.3182709962129593e-02</internalNodes>
          <leafValues>
            -3.2544779777526855e-01 6.3468569517135620e-01
            -2.8268361091613770e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 152 -1.0638199746608734e-02 -1 -2 153
            -2.1207019686698914e-02</internalNodes>
          <leafValues>
            -5.5776351690292358e-01 3.9049190282821655e-01
            -4.2111930251121521e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 154 -5.6731878430582583e-05 -1 -2 155
            -4.4976451317779720e-04</internalNodes>
          <leafValues>
            -4.1803309321403503e-01 3.7355789542198181e-01
            -3.9199641346931458e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 156 2.7574670966714621e-03 -1 -2 157
            2.5649419985711575e-03</internalNodes>
          <leafValues>
            -7.9104632139205933e-01 1.9258180260658264e-01
            -7.5344461202621460e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 158 -9.4359368085861206e-03 -1 -2 159
            1.4136210083961487e-03</internalNodes>
          <leafValues>
            4.4834750890731812e-01 -3.3878430724143982e-01
            4.4291919469833374e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 160 3.9976350963115692e-03 -1 -2 161
            -1.5278969658538699e-03</internalNodes>
          <leafValues>
            -6.6637581586837769e-01 3.1292399764060974e-01
            -2.8027990460395813e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 162 -3.2376639865105972e-05 -1 -2 163
            1.6323389718309045e-03</internalNodes>
          <leafValues>
            -4.6672090888023376e-01 2.7995559573173523e-01
            -6.1321508884429932e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 164 7.7096219174563885e-03 -1 -2 165
            -7.8599318861961365e-02</internalNodes>
          <leafValues>
            2.0352549850940704e-01 7.2726912796497345e-02
            -6.8677097558975220e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 166 -3.6581400781869888e-03 -1 -2 167
            -4.2612198740243912e-02</internalNodes>
          <leafValues>
            -6.8079459667205811e-01 -8.4551781415939331e-01
            1.5990570187568665e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 168 -4.8822778626345098e-04 -1 -2 169
            -4.6951142139732838e-03</internalNodes>
          <leafValues>
            -4.7945699095726013e-01 -8.2234281301498413e-01
            2.0431579649448395e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 170 6.1706348787993193e-05 -1 -2 171
            1.3809910044074059e-02</internalNodes>
          <leafValues>
            -3.1742820143699646e-01 3.0769300460815430e-01
            -4.3544968962669373e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 172 -4.2187729850411415e-03 -1 -2 173
            -3.9540808647871017e-03</internalNodes>
          <leafValues>
            6.2499982118606567e-01 1.3225209712982178e-01
            -3.9745101332664490e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 174 2.2203531116247177e-03 -1 -2 175
            6.2806582718621939e-05</internalNodes>
          <leafValues>
            -6.0045331716537476e-01 -2.2429980337619781e-01
            2.9768520593643188e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 176 2.3292789701372385e-03 -1 -2 177
            -5.3711822256445885e-03</internalNodes>
          <leafValues>
            -7.5982081890106201e-01 2.6484918594360352e-01
            -2.6005539298057556e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 178 6.4782587287481874e-05 -1 -2 179
            7.6606678776443005e-03</internalNodes>
          <leafValues>
            -3.2119300961494446e-01 2.4176409840583801e-01
            -8.3822727203369141e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>19</maxWeakCount>
      <stageThreshold>-2.2618789672851562e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 180 -1.4848279766738415e-02 -1 -2 181
            -1.6066679963842034e-03</internalNodes>
          <leafValues>
            -5.3391128778457642e-01 7.6002711057662964e-01
            -2.1091739833354950e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 182 -1.5651920437812805e-01 -1 -2 183
            -5.5439779534935951e-03</internalNodes>
          <leafValues>
            -4.2818549275398254e-01 6.5620750188827515e-01
            -2.2949840128421783e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 184 -1.9448339939117432e-02 -1 -2 185
            7.6653067953884602e-03</internalNodes>
          <leafValues>
            -4.4212520122528076e-01 -3.3950591087341309e-01
            4.6587219834327698e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 186 -2.1142010390758514e-01 -1 -2 187
            -1.0628429800271988e-01</internalNodes>
          <leafValues>
            5.5007970333099365e-01 6.8280947208404541e-01
            -3.0987739562988281e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 188 -5.2653599530458450e-02 -1 -2 189
            -5.3522300731856376e-05</internalNodes>
          <leafValues>
            -3.4818819165229797e-01 5.0566762685775757e-01
            -2.5229519605636597e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 190 -5.7972650974988937e-03 -1 -2 191
            -3.7428899668157101e-03</internalNodes>
          <leafValues>
            3.0238011479377747e-01 2.2873230278491974e-01
            -4.8366579413414001e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 192 -5.2694038458866999e-05 -1 -2 193
            -1.1983739677816629e-03</internalNodes>
          <leafValues>
            -3.7988960742950439e-01 -6.7442452907562256e-01
            2.8611260652542114e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 194 2.2544799372553825e-02 -1 -2 195
            3.1783939339220524e-03</internalNodes>
          <leafValues>
            4.7565719485282898e-01 -2.8893348574638367e-01
            5.5509638786315918e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 196 3.4742769785225391e-03 -1 -2 197
            -8.1408787518739700e-03</internalNodes>
          <leafValues>
            -5.9826552867889404e-01 -5.5933791399002075e-01
            2.2349210083484650e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 198 -3.0238809995353222e-03 -1 -2 199
            -5.9159598313271999e-03</internalNodes>
          <leafValues>
            4.5917978882789612e-01 6.2234902381896973e-01
            -2.4468150734901428e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 200 2.3184430319815874e-03 -1 -2 201
            7.7198208309710026e-03</internalNodes>
          <leafValues>
            -6.0478079319000244e-01 2.1004509925842285e-01
            -6.4331281185150146e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 202 -5.5973320268094540e-03 -1 -2 203
            2.0320380281191319e-04</internalNodes>
          <leafValues>
            -7.1625810861587524e-01 -3.8018029928207397e-01
            2.1336899697780609e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 204 -3.8205389864742756e-03 -1 -2 205
            4.8883338458836079e-03</internalNodes>
          <leafValues>
            -3.5957258939743042e-01 2.6471930742263794e-01
            -5.8996689319610596e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 206 -1.3334590476006269e-03 -1 -2 207
            -1.5447080368176103e-03</internalNodes>
          <leafValues>
            3.2258489727973938e-01 3.6971050500869751e-01
            -3.1308570504188538e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 208 7.5150746852159500e-05 -1 -2 209
            -1.1108840117231011e-03</internalNodes>
          <leafValues>
            -3.4674531221389771e-01 -5.7477539777755737e-01
            2.9201140999794006e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 210 -1.6881119518075138e-04 -1 -2 211
            -1.2814450019504875e-04</internalNodes>
          <leafValues>
            -3.6041781306266785e-01 3.5043209791183472e-01
            -2.2014050185680389e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 212 1.9546970725059509e-02 -1 -2 213
            -1.1061180382966995e-02</internalNodes>
          <leafValues>
            4.1295918822288513e-01 2.5962719321250916e-01
            -3.4875950217247009e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 214 1.8147419905290008e-03 -1 -2 215
            -7.1724010631442070e-03</internalNodes>
          <leafValues>
            -5.2019888162612915e-01 2.7452668547630310e-01
            -2.6828849315643311e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 216 2.2158189676702023e-03 -1 -2 217
            -9.6856858581304550e-03</internalNodes>
          <leafValues>
            -5.7340908050537109e-01 -5.8028572797775269e-01
            1.8564410507678986e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>19</maxWeakCount>
      <stageThreshold>-2.0994780063629150e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 1 218 -1.2065219692885876e-02 -1 -2 219
            -4.9067771434783936e-01</internalNodes>
          <leafValues>
            6.1679571866989136e-01 1.4063939452171326e-01
            -5.5357742309570312e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 220 -6.6585717722773552e-03 -1 -2 221
            1.5827560797333717e-02</internalNodes>
          <leafValues>
            -5.1332288980484009e-01 -3.6301520466804504e-01
            4.3343341350555420e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 222 -1.4081180095672607e-02 -1 -2 223
            -1.2139449827373028e-02</internalNodes>
          <leafValues>
            5.4223722219467163e-01 4.4281288981437683e-01
            -3.4171119332313538e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 224 7.8055798076093197e-03 -1 -2 225
            -7.0759910158813000e-05</internalNodes>
          <leafValues>
            -4.8659759759902954e-01 3.4818679094314575e-01
            -3.2806739211082458e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 226 -1.8199630081653595e-02 -1 -2 227
            -2.5289389304816723e-03</internalNodes>
          <leafValues>
            5.6594151258468628e-01 1.1310060322284698e-01
            -4.0772381424903870e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 228 1.0156990028917789e-03 -1 -2 229
            2.9432660085149109e-04</internalNodes>
          <leafValues>
            -5.9842979907989502e-01 2.8439450263977051e-01
            -3.2190230488777161e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 230 2.0865290425717831e-03 -1 -2 231
            -1.7371569992974401e-03</internalNodes>
          <leafValues>
            -7.8285712003707886e-01 3.3585301041603088e-01
            -2.0582370460033417e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 232 -7.0026202592998743e-05 -1 -2 233
            -1.4891549944877625e-03</internalNodes>
          <leafValues>
            -3.9109349250793457e-01 -4.6953418850898743e-01
            2.7609241008758545e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 234 -1.1788429692387581e-02 -1 -2 235
            -1.5155089786276221e-03</internalNodes>
          <leafValues>
            -4.0114149451255798e-01 -7.4290478229522705e-01
            2.7695629000663757e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 236 6.8396717309951782e-02 -1 -2 237
            -7.6441407203674316e-02</internalNodes>
          <leafValues>
            4.5235648751258850e-01 4.2848169803619385e-01
            -3.1636309623718262e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 238 6.8310201168060303e-02 -1 -2 239
            -6.4508013427257538e-02</internalNodes>
          <leafValues>
            5.1404279470443726e-01 1.8081870675086975e-01
            -3.4217950701713562e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 240 -2.8335719835013151e-03 -1 -2 241
            -9.9732237868010998e-04</internalNodes>
          <leafValues>
            -6.9509768486022949e-01 -4.3724590539932251e-01
            2.0226080715656281e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 242 -2.2869910299777985e-01 -1 -2 243
            2.9855249449610710e-03</internalNodes>
          <leafValues>
            6.4662200212478638e-01 8.1149758771061897e-03
            -6.0210299491882324e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 244 -2.9535989742726088e-03 -1 -2 245
            -2.1225619129836559e-03</internalNodes>
          <leafValues>
            -7.2013127803802490e-01 5.0875622034072876e-01
            -5.9366609901189804e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 246 -2.9382819775491953e-03 -1 -2 247
            -5.8961478061974049e-03</internalNodes>
          <leafValues>
            3.9287531375885010e-01 4.1866040229797363e-01
            -2.5405511260032654e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 248 2.5730929337441921e-03 -1 -2 249
            1.6647739335894585e-02</internalNodes>
          <leafValues>
            -5.8707278966903687e-01 1.9208480417728424e-01
            -6.0388940572738647e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 250 2.4041840806603432e-03 -1 -2 251
            -9.0452830772846937e-04</internalNodes>
          <leafValues>
            -5.7192337512969971e-01 3.4860768914222717e-01
            -1.3049240410327911e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 252 4.0814210660755634e-03 -1 -2 253
            3.3811479806900024e-03</internalNodes>
          <leafValues>
            5.1778018474578857e-01 -6.3828541897237301e-03
            -6.1447817087173462e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 254 -2.7499340940266848e-03 -1 -2 255
            -4.8207710497081280e-03</internalNodes>
          <leafValues>
            -6.5407788753509521e-01 -6.0029619932174683e-01
            1.4374589920043945e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>21</maxWeakCount>
      <stageThreshold>-2.1254189014434814e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 1 256 7.9710120335221291e-03 -1 -2 257
            -9.7160867881029844e-04</internalNodes>
          <leafValues>
            -6.1992239952087402e-01 5.4877161979675293e-01
            -4.0606960654258728e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 258 -1.0945869609713554e-02 -1 -2 259
            -6.1174821108579636e-02</internalNodes>
          <leafValues>
            4.6936869621276855e-01 3.0570849776268005e-01
            -4.4459891319274902e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 260 -2.3100150283426046e-03 -1 -2 261
            -4.7585051506757736e-02</internalNodes>
          <leafValues>
            -3.7816441059112549e-01 4.8865839838981628e-01
            -2.9728868603706360e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 262 -2.5944279041141272e-03 -1 -2 263
            -3.9469371549785137e-03</internalNodes>
          <leafValues>
            -5.4405367374420166e-01 3.6382490396499634e-01
            -3.0469849705696106e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 264 3.1871569808572531e-04 -1 -2 265
            -2.6655721012502909e-03</internalNodes>
          <leafValues>
            -4.6822971105575562e-01 3.3131968975067139e-01
            -2.9918238520622253e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 266 -3.9534650743007660e-02 -1 -2 267
            -9.4085611635819077e-04</internalNodes>
          <leafValues>
            -3.5316830873489380e-01 4.4447100162506104e-01
            -1.1088660359382629e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 268 6.9526307925116271e-05 -1 -2 269
            -9.6976682543754578e-03</internalNodes>
          <leafValues>
            -3.9403268694877625e-01 5.7181888818740845e-01
            -1.6370950266718864e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 270 3.9469040930271149e-02 -1 -2 271
            -8.2811042666435242e-03</internalNodes>
          <leafValues>
            6.9152122735977173e-01 1.3349990546703339e-01
            -4.7064480185508728e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 272 -4.3219728395342827e-03 -1 -2 273
            -5.5436040274798870e-03</internalNodes>
          <leafValues>
            3.8239258527755737e-01 1.5645879507064819e-01
            -4.1088208556175232e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 274 -5.9953341406071559e-05 -1 -2 275
            -5.9089371934533119e-03</internalNodes>
          <leafValues>
            -3.9221799373626709e-01 -5.9083867073059082e-01
            2.7924481034278870e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 276 -4.4721391052007675e-02 -1 -2 277
            4.1267018765211105e-02</internalNodes>
          <leafValues>
            4.1454491019248962e-01 -3.2242009043693542e-01
            3.7849879264831543e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 278 5.6728709751041606e-05 -1 -2 279
            -6.2427870929241180e-02</internalNodes>
          <leafValues>
            -3.2228040695190430e-01 -5.9666448831558228e-01
            2.8915780782699585e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 280 -5.6994128972291946e-03 -1 -2 281
            7.5202910229563713e-03</internalNodes>
          <leafValues>
            3.7499341368675232e-01 -2.8132459521293640e-01
            5.0988858938217163e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 282 -3.3640549518167973e-03 -1 -2 283
            -6.8076648749411106e-03</internalNodes>
          <leafValues>
            -6.3978207111358643e-01 -7.3105818033218384e-01
            1.4475250244140625e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 284 1.2633459642529488e-02 -1 -2 285
            -2.9199919663369656e-03</internalNodes>
          <leafValues>
            -7.7725297212600708e-01 2.3258599638938904e-01
            -2.0490600168704987e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 286 -3.0582249164581299e-02 -1 -2 287
            -2.7796169742941856e-03</internalNodes>
          <leafValues>
            -6.5738821029663086e-01 -5.4888349771499634e-01
            1.3837890326976776e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 288 -7.6163080520927906e-03 -1 -2 289
            -1.8409560434520245e-03</internalNodes>
          <leafValues>
            -3.5912349820137024e-01 2.2404469549655914e-01
            -3.7881860136985779e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 290 -3.9200261235237122e-02 -1 -2 291
            -2.2543789818882942e-03</internalNodes>
          <leafValues>
            5.0090551376342773e-01 3.1364008784294128e-01
            -2.2131860256195068e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 292 2.3894659243524075e-03 -1 -2 293
            -1.0725490283221006e-03</internalNodes>
          <leafValues>
            -5.8699512481689453e-01 4.7141209244728088e-01
            -3.2570488750934601e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 294 8.9095337898470461e-05 -1 -2 295
            1.6920049674808979e-03</internalNodes>
          <leafValues>
            -3.0444309115409851e-01 3.0280891060829163e-01
            -3.8902729749679565e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 296 1.1784000322222710e-02 -1 -2 297
            3.9335917681455612e-03</internalNodes>
          <leafValues>
            -6.8993437290191650e-01 -6.7763939499855042e-02
            4.6499788761138916e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>22</maxWeakCount>
      <stageThreshold>-2.0614759922027588e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 1 298 1.1430840007960796e-02 -1 -2 299
            -3.2242920249700546e-02</internalNodes>
          <leafValues>
            -3.9274570345878601e-01 6.5568798780441284e-01
            -3.1068810820579529e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 300 -1.8382760463282466e-03 -1 -2 301
            -1.0764399915933609e-01</internalNodes>
          <leafValues>
            -4.0825068950653076e-01 4.3280079960823059e-01
            -4.2263451218605042e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 302 -2.3866090923547745e-03 -1 -2 303
            8.6586214601993561e-03</internalNodes>
          <leafValues>
            -4.6435201168060303e-01 -4.0673071146011353e-01
            4.1267868876457214e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 304 -1.6437229933217168e-03 -1 -2 305
            -9.8511137068271637e-02</internalNodes>
          <leafValues>
            -2.1344049274921417e-01 6.8432319164276123e-01
            -9.7035013139247894e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 306 4.4292360544204712e-03 -1 -2 307
            4.6966210938990116e-03</internalNodes>
          <leafValues>
            -3.9498910307884216e-01 -1.1345980316400528e-01
            4.9681991338729858e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 308 -8.8480701670050621e-03 -1 -2 309
            -6.7258379422128201e-03</internalNodes>
          <leafValues>
            -3.1293100118637085e-01 -6.1635792255401611e-01
            3.1764769554138184e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 310 2.0052040927112103e-03 -1 -2 311
            -1.3407340273261070e-02</internalNodes>
          <leafValues>
            3.1724271178245544e-01 1.9735060632228851e-01
            -3.7199181318283081e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 312 -4.4199679978191853e-03 -1 -2 313
            -3.2800938934087753e-02</internalNodes>
          <leafValues>
            -5.7164478302001953e-01 3.0599930882453918e-01
            -1.7397969961166382e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 314 4.9407979531679302e-05 -1 -2 315
            4.1550169698894024e-03</internalNodes>
          <leafValues>
            -2.8270530700683594e-01 2.9686808586120605e-01
            -4.8494309186935425e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 316 -7.5589967309497297e-05 -1 -2 317
            -3.2147730235010386e-03</internalNodes>
          <leafValues>
            -3.8531139492988586e-01 -6.3306808471679688e-01
            2.3434750735759735e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 318 1.6021779738366604e-03 -1 -2 319
            -1.9478019326925278e-02</internalNodes>
          <leafValues>
            -2.9579049348831177e-01 -4.9625208973884583e-01
            2.6092579960823059e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 320 -2.5193750858306885e-02 -1 -2 321
            -4.6487729996442795e-02</internalNodes>
          <leafValues>
            3.9384880661964417e-01 2.2168830037117004e-01
            -2.9691740870475769e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 322 4.3414267711341381e-03 -1 -2 323
            -2.4886759929358959e-03</internalNodes>
          <leafValues>
            -6.7661178112030029e-01 2.0509929955005646e-01
            -2.9771140217781067e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 324 -5.8827269822359085e-03 -1 -2 325
            9.0498890494927764e-04</internalNodes>
          <leafValues>
            -6.1301797628402710e-01 -3.4023219347000122e-01
            1.8168209493160248e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 326 -9.8338901996612549e-02 -1 -2 327
            5.6141808629035950e-02</internalNodes>
          <leafValues>
            4.7729569673538208e-01 -2.2904439270496368e-01
            3.4410089254379272e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 328 -5.5787130258977413e-03 -1 -2 329
            1.5108759980648756e-03</internalNodes>
          <leafValues>
            -3.5910171270370483e-01 2.4900430440902710e-01
            -4.3798071146011353e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 330 -6.0129738412797451e-03 -1 -2 331
            -7.9341192031279206e-04</internalNodes>
          <leafValues>
            3.1164181232452393e-01 2.6759660243988037e-01
            -3.6802908778190613e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 332 6.1855330131947994e-03 -1 -2 333
            -7.3785060085356236e-03</internalNodes>
          <leafValues>
            -7.2153317928314209e-01 -5.3714382648468018e-01
            1.3824890553951263e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 334 -6.7488732747733593e-04 -1 -2 335
            -1.3102099765092134e-03</internalNodes>
          <leafValues>
            3.7406051158905029e-01 1.9003790616989136e-01
            -3.1632271409034729e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 336 4.9453211249783635e-04 -1 -2 337
            1.2824690202251077e-03</internalNodes>
          <leafValues>
            -2.3283170163631439e-01 3.0463808774948120e-01
            -4.8092108964920044e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 338 -2.2624820470809937e-02 -1 -2 339
            4.3685249984264374e-03</internalNodes>
          <leafValues>
            -6.8783479928970337e-01 1.2403090298175812e-01
            -7.9220730066299438e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 340 5.6756488047540188e-03 -1 -2 341
            -8.1769213080406189e-02</internalNodes>
          <leafValues>
            1.7611420154571533e-01 3.8942161202430725e-01
            -4.5094010233879089e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>24</maxWeakCount>
      <stageThreshold>-1.9795049428939819e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 342 -2.0003549754619598e-02 -1 -2 343
            -3.2621208578348160e-02</internalNodes>
          <leafValues>
            -5.6650751829147339e-01 5.0807082653045654e-01
            -4.5345708727836609e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 344 1.0668139904737473e-02 -1 -2 345
            -1.6276689246296883e-02</internalNodes>
          <leafValues>
            -3.2316839694976807e-01 6.0189497470855713e-01
            -2.4059510231018066e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 346 -2.8211208991706371e-03 -1 -2 347
            -1.4291180297732353e-02</internalNodes>
          <leafValues>
            -4.7181150317192078e-01 5.1280087232589722e-01
            -1.0744000226259232e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 348 1.0120410006493330e-03 -1 -2 349
            -5.9822672046720982e-03</internalNodes>
          <leafValues>
            -3.8844698667526245e-01 4.6928858757019043e-01
            -9.1355919837951660e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 350 -2.4705699179321527e-03 -1 -2 351
            2.4079859722405672e-03</internalNodes>
          <leafValues>
            -4.5964410901069641e-01 2.1830670535564423e-01
            -5.9373402595520020e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 352 -1.4312269631773233e-03 -1 -2 353
            2.9141810955479741e-04</internalNodes>
          <leafValues>
            -2.4731670320034027e-01 -2.5972241163253784e-01
            3.8206368684768677e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 354 -3.2818811014294624e-03 -1 -2 355
            -1.0365940397605300e-03</internalNodes>
          <leafValues>
            -7.7180129289627075e-01 2.3569859564304352e-01
            -2.2067700326442719e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 356 -2.2078400943428278e-03 -1 -2 357
            3.5239339340478182e-03</internalNodes>
          <leafValues>
            3.0886119604110718e-01 -2.8496000170707703e-01
            4.7544300556182861e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 358 -6.1774807982146740e-03 -1 -2 359
            -3.2023619860410690e-03</internalNodes>
          <leafValues>
            -7.0318382978439331e-01 -5.1361310482025146e-01
            1.5656259655952454e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 360 -8.7003601947799325e-04 -1 -2 361
            -3.8079950027167797e-03</internalNodes>
          <leafValues>
            -2.9925128817558289e-01 5.5215638875961304e-01
            -8.0608041025698185e-04</leafValues></_>
        <_>
          <internalNodes>
            1 0 362 4.9994210712611675e-03 -1 -2 363
            -1.0323170572519302e-03</internalNodes>
          <leafValues>
            -4.3541741371154785e-01 5.4992151260375977e-01
            -5.0770761445164680e-03</leafValues></_>
        <_>
          <internalNodes>
            1 0 364 6.9215619005262852e-03 -1 -2 365
            -8.1578325480222702e-03</internalNodes>
          <leafValues>
            3.3900010585784912e-01 3.4354889392852783e-01
            -2.4483889341354370e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 366 -1.6159559600055218e-03 -1 -2 367
            4.7165839932858944e-03</internalNodes>
          <leafValues>
            -7.4653702974319458e-01 1.1855059862136841e-01
            -7.1803867816925049e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 368 -1.6093119978904724e-02 -1 -2 369
            -5.9861610643565655e-03</internalNodes>
          <leafValues>
            -3.2987210154533386e-01 3.1263980269432068e-01
            -2.3194029927253723e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 370 6.4122617244720459e-02 -1 -2 371
            2.1518159657716751e-02</internalNodes>
          <leafValues>
            4.6239149570465088e-01 -2.4277320504188538e-01
            4.0963909029960632e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 372 -2.8541380167007446e-01 -1 -2 373
            2.7372559998184443e-04</internalNodes>
          <leafValues>
            4.4521799683570862e-01 -4.7307610511779785e-01
            7.6739721000194550e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 374 -6.4039281569421291e-03 -1 -2 375
            1.4279670082032681e-02</internalNodes>
          <leafValues>
            -5.6167787313461304e-01 -6.7311890423297882e-02
            4.3806758522987366e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 376 -1.3179860077798367e-02 -1 -2 377
            6.6828072071075439e-02</internalNodes>
          <leafValues>
            -6.7672669887542725e-01 -3.2182909548282623e-02
            5.1308721303939819e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 378 6.3021448440849781e-03 -1 -2 379
            -1.6806010389700532e-03</internalNodes>
          <leafValues>
            -2.0082660019397736e-01 -5.1767241954803467e-01
            3.8576510548591614e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 380 -1.5057720011100173e-03 -1 -2 381
            1.1699240421876311e-03</internalNodes>
          <leafValues>
            3.9358091354370117e-01 -2.5579568743705750e-01
            3.1927299499511719e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 382 7.2735180146992207e-03 -1 -2 383
            7.8693883551750332e-05</internalNodes>
          <leafValues>
            -7.1667242050170898e-01 -1.8908829987049103e-01
            2.3849080502986908e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 384 1.9624589476734400e-03 -1 -2 385
            -3.1472831033170223e-03</internalNodes>
          <leafValues>
            -5.1583772897720337e-01 4.8033049702644348e-01
            -3.6237910389900208e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 386 5.0133569166064262e-03 -1 -2 387
            -6.5994369797408581e-03</internalNodes>
          <leafValues>
            -5.2729338407516479e-01 -6.9400531053543091e-01
            1.2275890260934830e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 388 -4.2700361460447311e-02 -1 -2 389
            -3.5096149076707661e-05</internalNodes>
          <leafValues>
            -6.8218547105789185e-01 1.2160310149192810e-01
            -4.2142289876937866e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>24</maxWeakCount>
      <stageThreshold>-1.9048260450363159e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 1 390 8.7128365412354469e-03 -1 -2 391
            -4.0675927884876728e-03</internalNodes>
          <leafValues>
            -4.4048839807510376e-01 6.0030102729797363e-01
            -2.6042649149894714e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 392 -8.3933398127555847e-02 -1 -2 393
            -2.2626180201768875e-02</internalNodes>
          <leafValues>
            -3.7943989038467407e-01 5.2529489994049072e-01
            -3.2733321189880371e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 394 -3.5725389607250690e-03 -1 -2 395
            -1.6297569964081049e-03</internalNodes>
          <leafValues>
            -2.6030939817428589e-01 4.8434230685234070e-01
            -3.8363268971443176e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 396 -8.0011576414108276e-02 -1 -2 397
            -9.6061453223228455e-02</internalNodes>
          <leafValues>
            3.9579561352729797e-01 4.2874181270599365e-01
            -2.9096639156341553e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 398 -9.3183852732181549e-03 -1 -2 399
            9.2205153778195381e-03</internalNodes>
          <leafValues>
            -3.9325499534606934e-01 -2.9857379198074341e-01
            3.1733301281929016e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 400 2.3208750411868095e-02 -1 -2 401
            1.6389730153605342e-03</internalNodes>
          <leafValues>
            3.9295229315757751e-01 -5.4035997390747070e-01
            -2.1836880594491959e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 402 2.8872499242424965e-03 -1 -2 403
            4.7465260140597820e-03</internalNodes>
          <leafValues>
            -7.8172737360000610e-01 1.4474189281463623e-01
            -6.4237701892852783e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 404 -5.7432148605585098e-03 -1 -2 405
            -8.5324952378869057e-03</internalNodes>
          <leafValues>
            -6.5556287765502930e-01 2.2090309858322144e-01
            -2.5790300965309143e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 406 -8.8752172887325287e-03 -1 -2 407
            -7.7129527926445007e-03</internalNodes>
          <leafValues>
            4.6596860885620117e-01 2.5279781222343445e-01
            -2.6170450448989868e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 408 7.6909800991415977e-03 -1 -2 409
            2.6657560374587774e-03</internalNodes>
          <leafValues>
            -5.9350818395614624e-01 1.6969729959964752e-01
            -5.4123950004577637e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 410 -4.4685939792543650e-04 -1 -2 411
            -1.5998890157788992e-03</internalNodes>
          <leafValues>
            -3.0383870005607605e-01 -5.4817748069763184e-01
            2.4971559643745422e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 412 1.9368670182302594e-03 -1 -2 413
            -2.4878541007637978e-03</internalNodes>
          <leafValues>
            -6.3200348615646362e-01 4.7051379084587097e-01
            -4.5187219977378845e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 414 -2.8134910389780998e-03 -1 -2 415
            -1.4107710449025035e-03</internalNodes>
          <leafValues>
            3.9270851016044617e-01 1.8017080426216125e-01
            -2.5714579224586487e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 416 -6.9013070315122604e-03 -1 -2 417
            -1.1458620429039001e-03</internalNodes>
          <leafValues>
            -5.3386241197586060e-01 2.8174358606338501e-01
            -1.6080249845981598e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 418 9.2800445854663849e-03 -1 -2 419
            -4.1281301528215408e-02</internalNodes>
          <leafValues>
            -3.0028960108757019e-01 -6.2409067153930664e-01
            2.0549909770488739e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 420 -3.5625360906124115e-02 -1 -2 421
            -4.1647539474070072e-03</internalNodes>
          <leafValues>
            -5.2529340982437134e-01 -6.3538008928298950e-01
            1.2846650183200836e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 422 -9.5598259940743446e-04 -1 -2 423
            -8.9347851462662220e-04</internalNodes>
          <leafValues>
            2.6505509018898010e-01 1.8266810476779938e-01
            -3.7531790137290955e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 424 2.5431478861719370e-03 -1 -2 425
            -1.5853889286518097e-02</internalNodes>
          <leafValues>
            -6.1057221889495850e-01 3.0754768848419189e-01
            -9.8143920302391052e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 426 -4.1315760463476181e-02 -1 -2 427
            -6.8226549774408340e-04</internalNodes>
          <leafValues>
            4.9247589707374573e-01 6.2975943088531494e-02
            -4.2634299397468567e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 428 6.3098431564867496e-04 -1 -2 429
            -2.8946860693395138e-03</internalNodes>
          <leafValues>
            3.1397339701652527e-01 2.8590971231460571e-01
            -2.5623229146003723e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 430 -1.0244140401482582e-02 -1 -2 431
            -1.6979850828647614e-02</internalNodes>
          <leafValues>
            -6.9737482070922852e-01 -7.3125731945037842e-01
            1.0389179736375809e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 432 -7.0198569446802139e-03 -1 -2 433
            -6.0688778758049011e-03</internalNodes>
          <leafValues>
            -3.5070639848709106e-01 -5.3395807743072510e-01
            1.7334850132465363e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 434 -9.6911415457725525e-03 -1 -2 435
            8.5460003465414047e-03</internalNodes>
          <leafValues>
            5.6399798393249512e-01 -2.4716490507125854e-01
            1.8216520547866821e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 436 -4.9479231238365173e-03 -1 -2 437
            1.9269150216132402e-03</internalNodes>
          <leafValues>
            -2.8333988785743713e-01 -6.8196073174476624e-02
            3.7787199020385742e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>28</maxWeakCount>
      <stageThreshold>-1.9407349824905396e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 438 -2.8639819473028183e-02 -1 -2 439
            -4.2176660150289536e-02</internalNodes>
          <leafValues>
            -3.7718260288238525e-01 7.2298699617385864e-01
            -7.6141163706779480e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 440 -2.2537210024893284e-03 -1 -2 441
            -3.0683329328894615e-02</internalNodes>
          <leafValues>
            -3.2727459073066711e-01 5.1505237817764282e-01
            -2.2235199809074402e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 442 -1.2341269850730896e-01 -1 -2 443
            -2.3674150928854942e-02</internalNodes>
          <leafValues>
            4.4699010252952576e-01 3.4708538651466370e-01
            -3.1773900985717773e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 444 3.1951239798218012e-03 -1 -2 445
            -1.4915530337020755e-03</internalNodes>
          <leafValues>
            -4.9775049090385437e-01 2.6384419202804565e-01
            -3.8912549614906311e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 446 8.8097527623176575e-04 -1 -2 447
            -5.8355771005153656e-02</internalNodes>
          <leafValues>
            -4.0939790010452271e-01 3.2287618517875671e-01
            -2.3045599460601807e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 448 5.1132370717823505e-03 -1 -2 449
            -4.5418320223689079e-03</internalNodes>
          <leafValues>
            -5.1353681087493896e-01 5.3011757135391235e-01
            -3.0649330466985703e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 450 1.6811339883133769e-03 -1 -2 451
            2.8129699639976025e-03</internalNodes>
          <leafValues>
            -5.3161472082138062e-01 -6.7524053156375885e-02
            3.8542249798774719e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 452 2.1835418883711100e-03 -1 -2 453
            -2.4335379712283611e-03</internalNodes>
          <leafValues>
            -6.4298832416534424e-01 -6.6313308477401733e-01
            1.3882370293140411e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 454 3.0736608896404505e-03 -1 -2 455
            -9.6425544470548630e-03</internalNodes>
          <leafValues>
            -6.3433158397674561e-01 3.8696160912513733e-01
            -6.8737797439098358e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 456 -7.2082108817994595e-03 -1 -2 457
            -8.0191977322101593e-03</internalNodes>
          <leafValues>
            1.6121250391006470e-01 3.8011130690574646e-01
            -4.1397979855537415e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 458 -7.2479159571230412e-03 -1 -2 459
            -2.2631640732288361e-01</internalNodes>
          <leafValues>
            2.4351879954338074e-01 6.0667949914932251e-01
            -2.2521880269050598e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 460 -7.0091613451950252e-05 -1 -2 461
            -1.8161399662494659e-01</internalNodes>
          <leafValues>
            1.7115320265293121e-01 5.2725982666015625e-01
            -3.5247540473937988e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 462 -9.4038434326648712e-03 -1 -2 463
            -2.1289030555635691e-03</internalNodes>
          <leafValues>
            3.4970518946647644e-01 5.5878698825836182e-02
            -4.9816590547561646e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 464 -5.1798550412058830e-03 -1 -2 465
            -6.5030192490667105e-04</internalNodes>
          <leafValues>
            -6.3095641136169434e-01 3.5856458544731140e-01
            -7.8281052410602570e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 466 -1.0555930435657501e-02 -1 -2 467
            -5.1852981559932232e-03</internalNodes>
          <leafValues>
            -5.5502831935882568e-01 3.5548681020736694e-01
            -6.8892292678356171e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 468 -7.8725479543209076e-03 -1 -2 469
            -6.5342970192432404e-03</internalNodes>
          <leafValues>
            -4.8596179485321045e-01 2.1178959310054779e-01
            -2.3174080252647400e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 470 -1.3909920118749142e-02 -1 -2 471
            1.5418450348079205e-03</internalNodes>
          <leafValues>
            5.9936982393264771e-01 -9.5086917281150818e-03
            -6.4796131849288940e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 472 -1.1549900518730283e-03 -1 -2 473
            -3.2687030732631683e-02</internalNodes>
          <leafValues>
            -2.7501720190048218e-01 -6.7336207628250122e-01
            1.9520400464534760e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 474 -2.6422590017318726e-01 -1 -2 475
            6.9438670761883259e-03</internalNodes>
          <leafValues>
            3.6986869573593140e-01 -3.0029740929603577e-01
            1.4998969435691833e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 476 -1.2077920138835907e-02 -1 -2 477
            -1.3986700214445591e-03</internalNodes>
          <leafValues>
            4.1644129157066345e-01 4.1248729825019836e-01
            -1.9533659517765045e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 478 1.3138339854776859e-02 -1 -2 479
            7.2417110204696655e-03</internalNodes>
          <leafValues>
            -6.4204931259155273e-01 1.1359360069036484e-01
            -7.3838871717453003e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 480 -7.4837901629507542e-03 -1 -2 481
            6.8022231571376324e-03</internalNodes>
          <leafValues>
            -6.9246298074722290e-01 9.2873439192771912e-02
            -6.0047471523284912e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 482 4.5322909951210022e-01 -1 -2 483
            -5.5721630342304707e-03</internalNodes>
          <leafValues>
            5.6260532140731812e-01 7.7820159494876862e-02
            -3.3990600705146790e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 484 3.1583961099386215e-02 -1 -2 485
            -5.7926177978515625e-03</internalNodes>
          <leafValues>
            3.2292670011520386e-01 1.5534450113773346e-01
            -3.5717839002609253e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 486 -7.6025379821658134e-03 -1 -2 487
            9.5151038840413094e-04</internalNodes>
          <leafValues>
            -5.1859498023986816e-01 -2.9570670798420906e-02
            4.6027511358261108e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 488 1.9723300356417894e-03 -1 -2 489
            2.3158260155469179e-03</internalNodes>
          <leafValues>
            3.6926651000976562e-01 -2.1299740672111511e-01
            2.6948541402816772e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 490 2.1179600153118372e-03 -1 -2 491
            -2.6946600992232561e-03</internalNodes>
          <leafValues>
            -4.8369500041007996e-01 1.8545660376548767e-01
            -2.9411968588829041e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 492 5.8865409344434738e-02 -1 -2 493
            -6.8408921360969543e-03</internalNodes>
          <leafValues>
            -4.6770378947257996e-01 -6.6371321678161621e-01
            1.2721349298954010e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>26</maxWeakCount>
      <stageThreshold>-1.8931059837341309e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 494 -1.2766489759087563e-02 -1 -2 495
            3.7821640726178885e-03</internalNodes>
          <leafValues>
            -3.7968099117279053e-01 -1.6001829504966736e-01
            6.1953288316726685e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 496 -3.3049881458282471e-02 -1 -2 497
            4.5050241053104401e-02</internalNodes>
          <leafValues>
            -3.6825481057167053e-01 9.3770343810319901e-03
            7.1570581197738647e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 498 -3.5275409463793039e-03 -1 -2 499
            2.2250709589570761e-03</internalNodes>
          <leafValues>
            -3.7336608767509460e-01 -6.6712491214275360e-02
            4.9906119704246521e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 500 1.3609490124508739e-03 -1 -2 501
            -2.9087859392166138e-01</internalNodes>
          <leafValues>
            1.7162929475307465e-01 3.6158901453018188e-01
            -5.0871372222900391e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 502 3.3148950897157192e-03 -1 -2 503
            -8.8641437469050288e-04</internalNodes>
          <leafValues>
            -7.1788138151168823e-01 2.5713619589805603e-01
            -1.7978949844837189e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 504 1.1313590221107006e-03 -1 -2 505
            -3.0621800106018782e-03</internalNodes>
          <leafValues>
            3.5387420654296875e-01 3.0790808796882629e-01
            -3.1217241287231445e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 506 2.5443620979785919e-03 -1 -2 507
            -6.7088878713548183e-03</internalNodes>
          <leafValues>
            -5.6788551807403564e-01 2.1222899854183197e-01
            -2.6821109652519226e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 508 -1.6446809470653534e-01 -1 -2 509
            4.0828108787536621e-02</internalNodes>
          <leafValues>
            4.9016961455345154e-01 -3.1217470765113831e-01
            2.4748149514198303e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 510 -3.6051510833203793e-03 -1 -2 511
            -2.3608640767633915e-03</internalNodes>
          <leafValues>
            3.4355860948562622e-01 2.6566460728645325e-01
            -2.8644719719886780e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 512 1.2965350179001689e-03 -1 -2 513
            6.0111000202596188e-03</internalNodes>
          <leafValues>
            -2.9317760467529297e-01 2.1941700577735901e-01
            -6.0014218091964722e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 514 -6.1628420371562243e-04 -1 -2 515
            2.0573718938976526e-03</internalNodes>
          <leafValues>
            -3.1292331218719482e-01 2.8763169050216675e-01
            -3.7320709228515625e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 516 -7.7166007831692696e-03 -1 -2 517
            -2.8222459368407726e-03</internalNodes>
          <leafValues>
            -7.1683251857757568e-01 4.2501831054687500e-01
            -5.3294889628887177e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 518 -7.3861207056324929e-05 -1 -2 519
            5.8680498041212559e-03</internalNodes>
          <leafValues>
            1.4903450012207031e-01 -5.8436650037765503e-01
            1.0724759846925735e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 520 -7.9013723880052567e-03 -1 -2 521
            2.7825690340250731e-03</internalNodes>
          <leafValues>
            -3.4319949150085449e-01 1.7655360698699951e-01
            -6.1473757028579712e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 522 3.2751538674347103e-04 -1 -2 523
            3.0700899660587311e-02</internalNodes>
          <leafValues>
            -3.3837568759918213e-01 1.8566130101680756e-01
            -5.3450268507003784e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 524 5.6932470761239529e-03 -1 -2 525
            2.1375140547752380e-01</internalNodes>
          <leafValues>
            -5.1750451326370239e-01 1.2332399934530258e-01
            -6.4288139343261719e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 526 -4.4024959206581116e-03 -1 -2 527
            -4.5719969784840941e-04</internalNodes>
          <leafValues>
            5.8535677194595337e-01 2.3368820548057556e-01
            -1.9039009511470795e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 528 -4.2587839998304844e-03 -1 -2 529
            -2.3462621029466391e-03</internalNodes>
          <leafValues>
            -5.1190847158432007e-01 -4.7164770960807800e-01
            1.4783400297164917e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 530 -6.5065571106970310e-05 -1 -2 531
            -5.5082160979509354e-03</internalNodes>
          <leafValues>
            -2.9886341094970703e-01 -4.8508960008621216e-01
            2.0014910399913788e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 532 1.8942790105938911e-02 -1 -2 533
            6.9123771972954273e-03</internalNodes>
          <leafValues>
            3.1028950214385986e-01 -2.8701239824295044e-01
            2.0534069836139679e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 534 8.1696882843971252e-03 -1 -2 535
            1.0069769807159901e-02</internalNodes>
          <leafValues>
            4.5810830593109131e-01 -2.4175919592380524e-01
            1.7593820393085480e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 536 2.1663580555468798e-03 -1 -2 537
            1.0505730286240578e-02</internalNodes>
          <leafValues>
            -4.9877908825874329e-01 1.6231280565261841e-01
            -4.2988869547843933e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 538 5.7576788822188973e-04 -1 -2 539
            -3.0608899891376495e-02</internalNodes>
          <leafValues>
            -3.1012570858001709e-01 -7.4064302444458008e-01
            1.6217179596424103e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 540 -1.3430659659206867e-02 -1 -2 541
            1.1859040241688490e-03</internalNodes>
          <leafValues>
            4.5505639910697937e-01 -2.7227258682250977e-01
            2.2475010156631470e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 542 -4.9311347538605332e-04 -1 -2 543
            -2.4509918875992298e-03</internalNodes>
          <leafValues>
            -3.9598318934440613e-01 2.5004211068153381e-01
            -1.6140510141849518e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 544 1.3641949743032455e-02 -1 -2 545
            -3.6733329296112061e-02</internalNodes>
          <leafValues>
            -6.4525490999221802e-01 3.4197059273719788e-01
            -6.5968327224254608e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>29</maxWeakCount>
      <stageThreshold>-1.9677840471267700e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 1 546 1.3613830087706447e-03 -1 -2 547
            1.2211060151457787e-02</internalNodes>
          <leafValues>
            -3.4383928775787354e-01 -4.0358600020408630e-01
            5.7873630523681641e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 548 3.2929528970271349e-03 -1 -2 549
            -2.4831980466842651e-02</internalNodes>
          <leafValues>
            -2.2164349257946014e-01 5.4256910085678101e-01
            -4.7585600614547729e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 550 -3.4081530570983887e-01 -1 -2 551
            6.0929641127586365e-02</internalNodes>
          <leafValues>
            5.3438740968704224e-01 -2.6015359163284302e-01
            3.7626558542251587e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 552 -1.4399300562217832e-03 -1 -2 553
            -7.5711178779602051e-01</internalNodes>
          <leafValues>
            -4.1635149717330933e-01 4.7764539718627930e-01
            -1.2374229729175568e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 554 -5.9891431592404842e-03 -1 -2 555
            -8.9398561976850033e-04</internalNodes>
          <leafValues>
            2.1848620474338531e-01 1.7726029455661774e-01
            -5.4815018177032471e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 556 2.9013510793447495e-03 -1 -2 557
            4.4361278414726257e-03</internalNodes>
          <leafValues>
            -5.6709182262420654e-01 1.4183780550956726e-01
            -5.8784419298171997e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 558 -5.3319290600484237e-05 -1 -2 559
            2.5481029879301786e-03</internalNodes>
          <leafValues>
            -3.4821888804435730e-01 1.9745320081710815e-01
            -5.5979222059249878e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 560 7.4882939457893372e-02 -1 -2 561
            4.8816308379173279e-02</internalNodes>
          <leafValues>
            4.6647951006889343e-01 -2.2575210034847260e-01
            3.2325819134712219e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 562 -3.9128339849412441e-03 -1 -2 563
            -1.3820629566907883e-02</internalNodes>
          <leafValues>
            -5.9772872924804688e-01 2.6031211018562317e-01
            -2.0211410522460938e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 564 9.4047200400382280e-04 -1 -2 565
            -4.6419431455433369e-03</internalNodes>
          <leafValues>
            -3.4005248546600342e-01 -4.5187801122665405e-01
            2.1054859459400177e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 566 -3.1960941851139069e-02 -1 -2 567
            -1.2651160068344325e-04</internalNodes>
          <leafValues>
            -2.0826019346714020e-01 3.8553190231323242e-01
            -2.3116420209407806e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 568 -5.0413709133863449e-02 -1 -2 569
            -2.0950778853148222e-03</internalNodes>
          <leafValues>
            2.2846159338951111e-01 3.2639551162719727e-01
            -3.4385430812835693e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 570 -1.1017880402505398e-02 -1 -2 571
            -9.7415763884782791e-03</internalNodes>
          <leafValues>
            -7.7388781309127808e-01 3.6731991171836853e-01
            -6.5746001899242401e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 572 5.3386680519906804e-05 -1 -2 573
            5.9820311143994331e-03</internalNodes>
          <leafValues>
            -3.5571750998497009e-01 1.7653119564056396e-01
            -4.6110078692436218e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 574 -1.9558269996196032e-03 -1 -2 575
            7.6739699579775333e-03</internalNodes>
          <leafValues>
            -3.6172690987586975e-01 1.8038579821586609e-01
            -4.0452030301094055e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 576 4.2935381643474102e-03 -1 -2 577
            1.4181300066411495e-03</internalNodes>
          <leafValues>
            5.2086359262466431e-01 -2.2085809707641602e-01
            2.7357560396194458e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 578 -2.8263099491596222e-02 -1 -2 579
            6.3434068579226732e-04</internalNodes>
          <leafValues>
            -6.3833731412887573e-01 1.5636380016803741e-01
            -3.2148900628089905e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 580 -7.2387307882308960e-03 -1 -2 581
            -9.9928081035614014e-03</internalNodes>
          <leafValues>
            2.3126259446144104e-01 3.0397319793701172e-01
            -2.4478439986705780e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 582 6.4995248976629227e-05 -1 -2 583
            -5.3049270063638687e-03</internalNodes>
          <leafValues>
            1.5132980048656464e-01 2.0417870581150055e-01
            -4.6260431408882141e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 584 -1.6613099724054337e-02 -1 -2 585
            -1.1630290187895298e-02</internalNodes>
          <leafValues>
            3.3399769663810730e-01 3.7053430080413818e-01
            -1.9361549615859985e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 586 1.9068180117756128e-03 -1 -2 587
            -5.6926468387246132e-03</internalNodes>
          <leafValues>
            -3.8105058670043945e-01 5.0645208358764648e-01
            6.5170922316610813e-03</leafValues></_>
        <_>
          <internalNodes>
            1 0 588 -2.2453670680988580e-04 -1 -2 589
            9.5565039664506912e-03</internalNodes>
          <leafValues>
            -3.1526011228561401e-01 -5.3035598993301392e-01
            2.0532760024070740e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 590 3.1540619675070047e-03 -1 -2 591
            -3.0681329965591431e-01</internalNodes>
          <leafValues>
            -4.5928329229354858e-01 5.0717717409133911e-01
            -1.4439250342547894e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 592 2.8239809907972813e-03 -1 -2 593
            -3.3063529990613461e-03</internalNodes>
          <leafValues>
            -1.5437939763069153e-01 -4.3571388721466064e-01
            3.9342719316482544e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 594 3.7848789361305535e-04 -1 -2 595
            -3.0488630291074514e-03</internalNodes>
          <leafValues>
            2.5212600827217102e-01 4.6662339568138123e-01
            -2.2792230546474457e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 596 -1.4724380336701870e-02 -1 -2 597
            3.6062300205230713e-02</internalNodes>
          <leafValues>
            -7.8602111339569092e-01 -6.8571321666240692e-02
            3.6698839068412781e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 598 -2.2327410988509655e-03 -1 -2 599
            -7.8541820403188467e-04</internalNodes>
          <leafValues>
            -5.9740197658538818e-01 2.0273469388484955e-01
            -1.7221680283546448e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 600 7.8553898492828012e-04 -1 -2 601
            1.0078109800815582e-02</internalNodes>
          <leafValues>
            -4.3407449126243591e-01 1.2464140355587006e-01
            -4.8391419649124146e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 602 2.0928790792822838e-02 -1 -2 603
            1.3340089935809374e-03</internalNodes>
          <leafValues>
            5.6864207983016968e-01 1.4524639584124088e-02
            -4.6003210544586182e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>34</maxWeakCount>
      <stageThreshold>-1.9657919406890869e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 604 -1.5313959680497646e-02 -1 -2 605
            -1.4265860430896282e-02</internalNodes>
          <leafValues>
            -3.4347689151763916e-01 5.8209532499313354e-01
            -3.5527399182319641e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 606 1.2652979930862784e-03 -1 -2 607
            -7.3807648732326925e-05</internalNodes>
          <leafValues>
            -3.1498318910598755e-01 4.7249591350555420e-01
            -2.6380801200866699e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 608 -3.8527030497789383e-02 -1 -2 609
            -1.4758770354092121e-02</internalNodes>
          <leafValues>
            4.1556850075721741e-01 1.5677249431610107e-01
            -3.7650239467620850e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 610 -1.5448270132765174e-03 -1 -2 611
            6.4564580097794533e-03</internalNodes>
          <leafValues>
            -3.5932019352912903e-01 2.1276639401912689e-01
            -7.2287178039550781e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 612 1.0267349891364574e-02 -1 -2 613
            -8.6422899039462209e-04</internalNodes>
          <leafValues>
            -4.6045809984207153e-01 2.4920259416103363e-01
            -2.6721361279487610e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 614 3.2311889808624983e-03 -1 -2 615
            1.3676529750227928e-02</internalNodes>
          <leafValues>
            -4.0939199924468994e-01 -2.7391690760850906e-02
            4.5259070396423340e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 616 3.2787120435386896e-03 -1 -2 617
            -1.4256529975682497e-03</internalNodes>
          <leafValues>
            -7.0025652647018433e-01 2.5787800550460815e-01
            -1.5093439817428589e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 618 -2.2095029707998037e-03 -1 -2 619
            -8.7701372802257538e-02</internalNodes>
          <leafValues>
            3.5148110985755920e-01 4.1978740692138672e-01
            -2.3600180447101593e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 620 -2.8805620968341827e-03 -1 -2 621
            -2.5028509553521872e-03</internalNodes>
          <leafValues>
            3.0479869246482849e-01 1.3316699862480164e-01
            -3.1691300868988037e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 622 -5.1710562547668815e-04 -1 -2 623
            6.7088729701936245e-03</internalNodes>
          <leafValues>
            -3.5199090838432312e-01 2.0163150131702423e-01
            -6.0948008298873901e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 624 -7.6058752834796906e-02 -1 -2 625
            -3.0889140907675028e-03</internalNodes>
          <leafValues>
            -6.3694208860397339e-01 -7.9025340080261230e-01
            1.0366079956293106e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 626 2.5740528944879770e-03 -1 -2 627
            -5.4877097718417645e-03</internalNodes>
          <leafValues>
            -4.5424199104309082e-01 2.1481299400329590e-01
            -1.9329510629177094e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 628 -1.2507289648056030e-03 -1 -2 629
            -4.3231048621237278e-03</internalNodes>
          <leafValues>
            -2.1651449799537659e-01 -6.2799078226089478e-01
            2.4270740151405334e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 630 4.3724630959331989e-03 -1 -2 631
            7.4632692849263549e-04</internalNodes>
          <leafValues>
            -5.1889377832412720e-01 -1.1378680169582367e-01
            2.8224378824234009e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 632 -1.3375070411711931e-03 -1 -2 633
            -2.9367550741881132e-03</internalNodes>
          <leafValues>
            2.4589119851589203e-01 2.4335819482803345e-01
            -2.9112818837165833e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 634 6.3193867390509695e-05 -1 -2 635
            -5.1338938064873219e-03</internalNodes>
          <leafValues>
            -2.5806590914726257e-01 -4.6110409498214722e-01
            2.4333980679512024e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 636 4.9400608986616135e-03 -1 -2 637
            -5.6112580932676792e-03</internalNodes>
          <leafValues>
            -3.9632990956306458e-01 2.4502380192279816e-01
            -1.5639010071754456e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 638 4.2950599454343319e-03 -1 -2 639
            4.5142881572246552e-03</internalNodes>
          <leafValues>
            -4.7671678662300110e-01 1.0698430240154266e-01
            -9.0471321344375610e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 640 7.5297639705240726e-03 -1 -2 641
            -1.2225280515849590e-03</internalNodes>
          <leafValues>
            4.1239809989929199e-01 2.8488171100616455e-01
            -1.9815699756145477e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 642 -3.4703810233622789e-03 -1 -2 643
            8.3724651485681534e-03</internalNodes>
          <leafValues>
            -4.4967961311340332e-01 1.5324249863624573e-01
            -3.8666850328445435e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 644 -3.3934618841158226e-05 -1 -2 645
            -2.7241709828376770e-01</internalNodes>
          <leafValues>
            -3.1429070234298706e-01 -5.5842101573944092e-01
            1.6627819836139679e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 646 -2.7582740876823664e-03 -1 -2 647
            2.5530489161610603e-02</internalNodes>
          <leafValues>
            2.7189570665359497e-01 -1.9172009825706482e-01
            4.3780499696731567e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 648 4.2080380953848362e-03 -1 -2 649
            -8.2151442766189575e-03</internalNodes>
          <leafValues>
            -4.4684138894081116e-01 2.2786709666252136e-01
            -1.7441789805889130e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 650 -2.9405429959297180e-03 -1 -2 651
            -9.4840265810489655e-03</internalNodes>
          <leafValues>
            -7.2643548250198364e-01 2.0794290304183960e-01
            -1.5239919722080231e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 652 4.2596450075507164e-03 -1 -2 653
            -1.7117479583248496e-03</internalNodes>
          <leafValues>
            6.1772680282592773e-01 -7.1106612682342529e-01
            -6.1875251121819019e-03</leafValues></_>
        <_>
          <internalNodes>
            0 1 654 -1.3266160385683179e-03 -1 -2 655
            9.1314306482672691e-03</internalNodes>
          <leafValues>
            1.7181269824504852e-01 -4.1138759255409241e-01
            1.8124279379844666e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 656 6.8382360041141510e-03 -1 -2 657
            7.5181988067924976e-03</internalNodes>
          <leafValues>
            -5.7601082324981689e-01 -1.0819079726934433e-01
            2.9561421275138855e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 658 -7.2788819670677185e-03 -1 -2 659
            -1.8039470538496971e-02</internalNodes>
          <leafValues>
            -5.8113521337509155e-01 4.5183068513870239e-01
            -2.7083089575171471e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 660 -1.0126599809154868e-03 -1 -2 661
            -6.7263199016451836e-03</internalNodes>
          <leafValues>
            2.4344119429588318e-01 1.6870440542697906e-01
            -2.7007728815078735e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 662 -3.2334970310330391e-03 -1 -2 663
            -7.7852200774941593e-05</internalNodes>
          <leafValues>
            -6.0048222541809082e-01 2.4241769313812256e-01
            -1.2413249909877777e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 664 -6.7774722992908210e-05 -1 -2 665
            7.1789676439948380e-05</internalNodes>
          <leafValues>
            1.5729150176048279e-01 -5.2893507480621338e-01
            -3.1665571033954620e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 666 1.0024299845099449e-02 -1 -2 667
            9.4298496842384338e-03</internalNodes>
          <leafValues>
            -4.8646959662437439e-01 1.1240869760513306e-01
            -4.2570489645004272e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 668 -7.4433721601963043e-04 -1 -2 669
            1.1660560034215450e-02</internalNodes>
          <leafValues>
            2.7540761232376099e-01 -2.3117260634899139e-01
            2.2442330420017242e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 670 3.9079408161342144e-03 -1 -2 671
            1.6550149768590927e-02</internalNodes>
          <leafValues>
            -6.3519638776779175e-01 1.0619100183248520e-01
            -4.7654989361763000e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>32</maxWeakCount>
      <stageThreshold>-1.7649420499801636e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 672 -1.8439030274748802e-02 -1 -2 673
            -5.3364519029855728e-02</internalNodes>
          <leafValues>
            -4.8745709657669067e-01 5.1037812232971191e-01
            -2.2670130431652069e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 674 -7.5706318020820618e-02 -1 -2 675
            -1.5329009620472789e-03</internalNodes>
          <leafValues>
            4.1487750411033630e-01 8.5764937102794647e-02
            -4.3470910191535950e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 676 -2.4494890123605728e-02 -1 -2 677
            -3.8144161226227880e-04</internalNodes>
          <leafValues>
            -2.7532699704170227e-01 3.8043969869613647e-01
            -4.3967849016189575e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 678 -8.8816778734326363e-03 -1 -2 679
            -3.9625130593776703e-02</internalNodes>
          <leafValues>
            -4.3258818984031677e-01 2.4481220543384552e-01
            -2.6193639636039734e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 680 -3.5907390993088484e-03 -1 -2 681
            3.7008870393037796e-02</internalNodes>
          <leafValues>
            -3.6199480295181274e-01 2.2637460380792618e-02
            5.5778437852859497e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 682 7.8503930126316845e-05 -1 -2 683
            -4.7969701699912548e-03</internalNodes>
          <leafValues>
            -3.3861130475997925e-01 3.1856098771095276e-01
            -1.6600249707698822e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 684 -1.1298010125756264e-02 -1 -2 685
            -4.4886539690196514e-03</internalNodes>
          <leafValues>
            3.7305471301078796e-01 2.9692959785461426e-01
            -2.5235760211944580e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 686 -2.2497780155390501e-03 -1 -2 687
            2.9247230850160122e-03</internalNodes>
          <leafValues>
            3.4263029694557190e-01 -5.6593239307403564e-02
            -7.0626032352447510e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 688 1.7976630479097366e-03 -1 -2 689
            1.9808609504252672e-03</internalNodes>
          <leafValues>
            -5.4180228710174561e-01 -2.5643008947372437e-01
            1.8446870148181915e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 690 -4.7688339836895466e-03 -1 -2 691
            -1.5755610540509224e-02</internalNodes>
          <leafValues>
            -2.9698228836059570e-01 2.8959378600120544e-01
            -1.6480749845504761e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 692 -1.1919640004634857e-02 -1 -2 693
            4.2308131232857704e-03</internalNodes>
          <leafValues>
            -5.8567219972610474e-01 1.3601270318031311e-01
            -4.8162451386451721e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 694 2.0548550412058830e-02 -1 -2 695
            -7.3943338356912136e-03</internalNodes>
          <leafValues>
            3.0143499374389648e-01 4.6367760747671127e-02
            -4.2379519343376160e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 696 -6.2137800268828869e-03 -1 -2 697
            1.4182809973135591e-03</internalNodes>
          <leafValues>
            4.5724278688430786e-01 -3.0143639445304871e-01
            1.8204510211944580e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 698 4.1609420441091061e-03 -1 -2 699
            -3.7915320135653019e-03</internalNodes>
          <leafValues>
            -5.2654838562011719e-01 -5.8677071332931519e-01
            1.1703660339117050e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 700 2.0879150833934546e-03 -1 -2 701
            1.5018540434539318e-03</internalNodes>
          <leafValues>
            -3.5307729244232178e-01 1.8624800443649292e-01
            -3.2729730010032654e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 702 2.1248809993267059e-02 -1 -2 703
            -5.5249751312658191e-04</internalNodes>
          <leafValues>
            -3.1979259848594666e-01 2.3370230197906494e-01
            -1.7386199533939362e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 704 -3.0085169710218906e-03 -1 -2 705
            -1.1611919617280364e-03</internalNodes>
          <leafValues>
            1.7596049606800079e-01 1.6033430397510529e-01
            -3.9680978655815125e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 706 -3.9655580185353756e-03 -1 -2 707
            -6.5836100839078426e-03</internalNodes>
          <leafValues>
            3.6691769957542419e-01 -6.2966358661651611e-01
            -2.4926450103521347e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 708 -9.0950471349060535e-04 -1 -2 709
            -5.7984529994428158e-03</internalNodes>
          <leafValues>
            3.9574980735778809e-01 1.7492240667343140e-01
            -2.6837408542633057e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 710 -5.7758802175521851e-01 -1 -2 711
            -1.5161310322582722e-02</internalNodes>
          <leafValues>
            5.9611392021179199e-01 -6.6131639480590820e-01
            3.3608361263759434e-04</leafValues></_>
        <_>
          <internalNodes>
            1 0 712 7.6604672358371317e-05 -1 -2 713
            2.7769979089498520e-02</internalNodes>
          <leafValues>
            2.0401589572429657e-01 -3.2097330689430237e-01
            2.2317400574684143e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 714 -2.6336179580539465e-03 -1 -2 715
            8.3722146227955818e-03</internalNodes>
          <leafValues>
            -3.9656499028205872e-01 1.3883970677852631e-01
            -5.8006221055984497e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 716 -7.0203031646087766e-04 -1 -2 717
            -4.8448870074935257e-04</internalNodes>
          <leafValues>
            2.7777281403541565e-01 2.1628519892692566e-01
            -2.9692250490188599e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 718 -3.3638171851634979e-02 -1 -2 719
            4.4241230934858322e-03</internalNodes>
          <leafValues>
            3.5791969299316406e-01 -8.6632027523592114e-04
            -5.5872720479965210e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 720 1.1545260436832905e-02 -1 -2 721
            -1.5816639643162489e-03</internalNodes>
          <leafValues>
            3.3837619423866272e-01 2.8660699725151062e-02
            -3.5041970014572144e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 722 1.3838140293955803e-02 -1 -2 723
            2.8327409178018570e-02</internalNodes>
          <leafValues>
            -7.7886807918548584e-01 -1.8604910001158714e-02
            6.2147867679595947e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 724 -8.8482163846492767e-03 -1 -2 725
            -1.1661020107567310e-03</internalNodes>
          <leafValues>
            2.6369819045066833e-01 1.0302580147981644e-01
            -3.2680010795593262e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 726 -3.2252211123704910e-02 -1 -2 727
            -9.4921119511127472e-02</internalNodes>
          <leafValues>
            -5.0046241283416748e-01 -7.2761011123657227e-01
            1.0330100357532501e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 728 2.5177269708365202e-03 -1 -2 729
            -4.0892168879508972e-02</internalNodes>
          <leafValues>
            -6.3938027620315552e-01 -5.7345229387283325e-01
            8.1502526998519897e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 730 -1.9293189980089664e-03 -1 -2 731
            -1.4116390375420451e-03</internalNodes>
          <leafValues>
            2.4177229404449463e-01 8.0363817512989044e-02
            -3.6146539449691772e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 732 -3.8812779821455479e-03 -1 -2 733
            4.4630360789597034e-03</internalNodes>
          <leafValues>
            -5.7638782262802124e-01 9.1835789382457733e-02
            -6.8039101362228394e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 734 2.9870839789509773e-03 -1 -2 735
            9.4975335523486137e-03</internalNodes>
          <leafValues>
            -1.0236640274524689e-01 4.9150609970092773e-01
            -3.8011389970779419e-01</leafValues></_></weakClassifiers></_></stages>
  <features>
    <_>
      <rects>
        <_>
          8 7 3 12 -1.</_>
        <_>
          8 11 3 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 7 8 3 -1.</_>
        <_>
          10 9 4 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 13 2 6 -1.</_>
        <_>
          9 16 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 2 12 8 -1.</_>
        <_>
          11 2 6 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 0 6 6 -1.</_>
        <_>
          14 3 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 5 12 -1.</_>
        <_>
          8 4 5 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 8 3 12 -1.</_>
        <_>
          1 12 3 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 11 2 7 -1.</_>
        <_>
          1 11 1 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 12 9 7 -1.</_>
        <_>
          9 12 3 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 4 6 9 -1.</_>
        <_>
          15 4 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 12 12 -1.</_>
        <_>
          8 11 4 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          15 0 4 20 -1.</_>
        <_>
          15 5 4 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 5 8 -1.</_>
        <_>
          0 16 5 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 2 12 8 -1.</_>
        <_>
          12 2 4 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 0 1 8 -1.</_>
        <_>
          19 4 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 7 3 12 -1.</_>
        <_>
          9 11 3 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 2 8 8 -1.</_>
        <_>
          1 6 8 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 4 4 -1.</_>
        <_>
          2 12 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 7 6 8 -1.</_>
        <_>
          9 7 3 4 2.</_>
        <_>
          12 11 3 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 18 7 2 -1.</_>
        <_>
          13 19 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 12 12 -1.</_>
        <_>
          8 11 4 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 5 12 -1.</_>
        <_>
          0 12 5 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 0 4 8 -1.</_>
        <_>
          18 0 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 12 1 8 -1.</_>
        <_>
          16 16 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 1 9 9 -1.</_>
        <_>
          12 1 3 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 16 1 3 -1.</_>
        <_>
          15 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 14 2 4 -1.</_>
        <_>
          2 16 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 12 9 3 -1.</_>
        <_>
          9 12 3 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 5 2 -1.</_>
        <_>
          0 19 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 18 12 -1.</_>
        <_>
          7 11 6 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 16 12 -1.</_>
        <_>
          4 0 8 6 2.</_>
        <_>
          12 6 8 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 3 2 5 -1.</_>
        <_>
          9 3 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 17 1 2 -1.</_>
        <_>
          17 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 16 1 3 -1.</_>
        <_>
          17 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 9 2 6 -1.</_>
        <_>
          1 9 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 3 3 4 -1.</_>
        <_>
          4 3 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 12 12 -1.</_>
        <_>
          8 11 4 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 7 8 -1.</_>
        <_>
          10 4 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 0 2 9 -1.</_>
        <_>
          19 0 1 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 13 1 4 -1.</_>
        <_>
          4 13 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 8 6 2 -1.</_>
        <_>
          12 10 2 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 11 4 7 -1.</_>
        <_>
          15 11 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 13 8 -1.</_>
        <_>
          4 2 13 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 1 7 8 -1.</_>
        <_>
          9 5 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 12 9 -1.</_>
        <_>
          10 0 6 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 3 4 4 -1.</_>
        <_>
          15 3 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 4 4 -1.</_>
        <_>
          0 18 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 17 2 1 -1.</_>
        <_>
          3 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 16 1 3 -1.</_>
        <_>
          16 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 10 6 4 -1.</_>
        <_>
          10 11 6 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 0 1 4 -1.</_>
        <_>
          19 2 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 0 3 3 -1.</_>
        <_>
          18 1 1 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          2 1 12 6 -1.</_>
        <_>
          2 4 12 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 2 1 16 -1.</_>
        <_>
          15 6 1 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 2 4 6 -1.</_>
        <_>
          13 2 2 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 3 3 3 -1.</_>
        <_>
          12 3 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 18 12 -1.</_>
        <_>
          7 11 6 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 12 9 -1.</_>
        <_>
          12 1 4 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 0 2 10 -1.</_>
        <_>
          18 5 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 5 12 15 -1.</_>
        <_>
          8 10 4 5 9.</_></rects></_>
    <_>
      <rects>
        <_>
          1 8 4 12 -1.</_>
        <_>
          1 12 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 13 8 2 -1.</_>
        <_>
          8 13 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 0 4 15 -1.</_>
        <_>
          18 0 2 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 0 4 8 -1.</_>
        <_>
          15 0 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 8 9 -1.</_>
        <_>
          5 3 8 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 6 6 -1.</_>
        <_>
          10 0 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 17 3 3 -1.</_>
        <_>
          11 17 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 17 4 3 -1.</_>
        <_>
          11 17 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 12 4 4 -1.</_>
        <_>
          15 12 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 18 4 2 -1.</_>
        <_>
          9 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 1 4 5 -1.</_>
        <_>
          7 1 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 6 5 -1.</_>
        <_>
          4 0 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 7 8 3 -1.</_>
        <_>
          10 9 4 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 12 4 3 -1.</_>
        <_>
          15 12 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 10 3 4 -1.</_>
        <_>
          9 11 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 0 2 6 -1.</_>
        <_>
          17 3 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 9 6 9 -1.</_>
        <_>
          3 12 2 3 9.</_></rects></_>
    <_>
      <rects>
        <_>
          5 11 8 4 -1.</_>
        <_>
          9 11 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 16 6 -1.</_>
        <_>
          1 3 16 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 14 6 -1.</_>
        <_>
          2 2 14 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 11 2 9 -1.</_>
        <_>
          1 11 1 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 11 1 8 -1.</_>
        <_>
          18 11 1 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 12 3 2 -1.</_>
        <_>
          11 12 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 13 3 1 -1.</_>
        <_>
          12 13 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 0 4 8 -1.</_>
        <_>
          17 0 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 17 4 3 -1.</_>
        <_>
          14 17 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 17 1 2 -1.</_>
        <_>
          15 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 16 1 3 -1.</_>
        <_>
          14 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 0 14 8 -1.</_>
        <_>
          3 2 14 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 1 1 2 -1.</_>
        <_>
          18 2 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 8 3 -1.</_>
        <_>
          8 0 4 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 4 1 9 -1.</_>
        <_>
          9 7 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 13 9 2 -1.</_>
        <_>
          9 13 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 13 5 6 -1.</_>
        <_>
          0 16 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 12 6 4 -1.</_>
        <_>
          15 12 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 6 12 2 -1.</_>
        <_>
          8 10 4 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 0 1 8 -1.</_>
        <_>
          19 4 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 2 12 8 -1.</_>
        <_>
          11 2 6 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 4 4 -1.</_>
        <_>
          2 12 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 8 13 9 -1.</_>
        <_>
          7 11 13 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 1 2 6 -1.</_>
        <_>
          19 1 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 4 5 8 -1.</_>
        <_>
          7 6 5 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 18 9 2 -1.</_>
        <_>
          11 19 9 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 7 2 3 -1.</_>
        <_>
          11 7 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 18 6 2 -1.</_>
        <_>
          6 18 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 13 6 7 -1.</_>
        <_>
          8 13 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 18 6 2 -1.</_>
        <_>
          7 18 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 5 2 2 -1.</_>
        <_>
          18 6 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 2 9 4 -1.</_>
        <_>
          6 4 9 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 0 7 4 -1.</_>
        <_>
          13 0 7 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 9 3 6 -1.</_>
        <_>
          11 11 3 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 8 4 6 -1.</_>
        <_>
          16 11 4 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 2 1 2 -1.</_>
        <_>
          19 3 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 1 1 3 -1.</_>
        <_>
          19 2 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 12 2 4 -1.</_>
        <_>
          13 12 1 2 2.</_>
        <_>
          14 14 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 9 3 5 -1.</_>
        <_>
          15 10 1 5 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 8 3 -1.</_>
        <_>
          10 9 4 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 7 9 4 -1.</_>
        <_>
          6 8 9 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 11 2 6 -1.</_>
        <_>
          1 11 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 13 5 6 -1.</_>
        <_>
          0 16 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 2 4 6 -1.</_>
        <_>
          18 2 2 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 5 6 7 -1.</_>
        <_>
          15 7 2 7 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 2 1 4 -1.</_>
        <_>
          19 4 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 1 6 2 -1.</_>
        <_>
          16 1 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 12 4 5 -1.</_>
        <_>
          15 12 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 15 2 3 -1.</_>
        <_>
          17 16 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 16 3 4 -1.</_>
        <_>
          14 18 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 16 1 2 -1.</_>
        <_>
          16 16 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 0 1 2 -1.</_>
        <_>
          18 1 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 8 1 6 -1.</_>
        <_>
          9 11 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 5 2 1 -1.</_>
        <_>
          19 5 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 3 6 4 -1.</_>
        <_>
          16 3 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 18 4 2 -1.</_>
        <_>
          9 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 13 9 7 -1.</_>
        <_>
          9 13 3 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 16 2 2 -1.</_>
        <_>
          1 17 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 3 4 -1.</_>
        <_>
          0 17 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 4 5 -1.</_>
        <_>
          9 1 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 1 6 9 -1.</_>
        <_>
          12 1 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 10 4 -1.</_>
        <_>
          10 10 10 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 8 5 4 -1.</_>
        <_>
          15 10 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 1 3 2 -1.</_>
        <_>
          18 2 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 11 3 5 -1.</_>
        <_>
          14 11 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 7 4 3 -1.</_>
        <_>
          10 7 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 8 1 -1.</_>
        <_>
          5 0 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 13 6 5 -1.</_>
        <_>
          3 13 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 9 3 5 -1.</_>
        <_>
          14 10 1 5 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 8 4 6 -1.</_>
        <_>
          9 10 4 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 7 6 6 -1.</_>
        <_>
          13 9 2 6 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 0 7 6 -1.</_>
        <_>
          7 3 7 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 1 10 12 -1.</_>
        <_>
          3 5 10 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 12 6 4 -1.</_>
        <_>
          15 12 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 6 9 -1.</_>
        <_>
          2 12 2 3 9.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 12 11 -1.</_>
        <_>
          12 0 4 11 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 1 8 -1.</_>
        <_>
          13 11 1 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 4 1 2 -1.</_>
        <_>
          19 5 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 15 1 2 -1.</_>
        <_>
          2 15 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 16 2 2 -1.</_>
        <_>
          17 16 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 16 1 3 -1.</_>
        <_>
          15 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 11 3 2 -1.</_>
        <_>
          6 12 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 11 2 2 -1.</_>
        <_>
          4 11 1 1 2.</_>
        <_>
          5 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 7 3 2 -1.</_>
        <_>
          18 8 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 9 3 8 -1.</_>
        <_>
          16 11 3 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 0 1 4 -1.</_>
        <_>
          19 2 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 0 1 3 -1.</_>
        <_>
          19 1 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 10 3 -1.</_>
        <_>
          14 0 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 3 15 17 -1.</_>
        <_>
          8 3 5 17 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 4 4 -1.</_>
        <_>
          9 0 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 11 8 1 -1.</_>
        <_>
          1 11 4 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 10 2 4 -1.</_>
        <_>
          3 11 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 17 4 3 -1.</_>
        <_>
          5 17 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 7 2 1 -1.</_>
        <_>
          19 7 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 7 18 3 -1.</_>
        <_>
          11 7 9 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 11 4 2 -1.</_>
        <_>
          4 11 2 1 2.</_>
        <_>
          6 12 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 9 2 4 -1.</_>
        <_>
          4 11 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 1 3 1 -1.</_>
        <_>
          17 2 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 18 1 2 -1.</_>
        <_>
          4 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 18 4 2 -1.</_>
        <_>
          10 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 11 5 4 -1.</_>
        <_>
          11 12 5 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 2 2 1 -1.</_>
        <_>
          19 2 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 6 2 -1.</_>
        <_>
          9 0 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 13 8 2 -1.</_>
        <_>
          8 13 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 12 4 4 -1.</_>
        <_>
          15 12 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 8 17 9 -1.</_>
        <_>
          3 11 17 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 4 3 -1.</_>
        <_>
          2 12 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 3 12 6 -1.</_>
        <_>
          12 3 4 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 14 3 6 -1.</_>
        <_>
          0 17 3 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 13 9 -1.</_>
        <_>
          3 3 13 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 2 8 6 -1.</_>
        <_>
          8 5 8 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 11 18 3 -1.</_>
        <_>
          7 11 6 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 17 1 2 -1.</_>
        <_>
          16 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 12 6 4 -1.</_>
        <_>
          16 12 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 4 5 -1.</_>
        <_>
          14 11 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 3 1 2 -1.</_>
        <_>
          19 4 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 0 1 3 -1.</_>
        <_>
          19 1 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 2 8 4 -1.</_>
        <_>
          7 4 8 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 12 3 2 -1.</_>
        <_>
          10 12 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 8 3 2 -1.</_>
        <_>
          16 9 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 15 3 2 -1.</_>
        <_>
          16 15 3 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 12 3 3 -1.</_>
        <_>
          7 12 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 12 3 1 -1.</_>
        <_>
          14 13 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 0 1 3 -1.</_>
        <_>
          3 1 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 2 6 4 -1.</_>
        <_>
          10 2 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 15 2 3 -1.</_>
        <_>
          14 16 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 18 8 2 -1.</_>
        <_>
          12 19 8 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 12 6 7 -1.</_>
        <_>
          9 12 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 18 6 2 -1.</_>
        <_>
          6 18 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 12 3 3 -1.</_>
        <_>
          12 12 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 12 2 2 -1.</_>
        <_>
          13 12 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 5 2 1 -1.</_>
        <_>
          19 5 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 19 4 1 -1.</_>
        <_>
          6 19 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 11 5 2 -1.</_>
        <_>
          0 12 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 0 2 2 -1.</_>
        <_>
          18 1 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 12 6 -1.</_>
        <_>
          1 2 12 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 1 6 1 -1.</_>
        <_>
          3 3 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 9 3 1 -1.</_>
        <_>
          17 10 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 10 1 6 -1.</_>
        <_>
          12 12 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 1 1 3 -1.</_>
        <_>
          2 2 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 0 4 3 -1.</_>
        <_>
          2 1 4 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 14 8 1 -1.</_>
        <_>
          8 14 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 8 18 9 -1.</_>
        <_>
          7 11 6 3 9.</_></rects></_>
    <_>
      <rects>
        <_>
          19 0 1 18 -1.</_>
        <_>
          19 6 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 13 3 6 -1.</_>
        <_>
          1 16 3 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 10 7 3 -1.</_>
        <_>
          6 11 7 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 7 3 -1.</_>
        <_>
          6 10 7 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 1 6 8 -1.</_>
        <_>
          17 1 3 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 6 2 4 -1.</_>
        <_>
          10 6 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 11 7 2 -1.</_>
        <_>
          6 12 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 11 3 6 -1.</_>
        <_>
          18 12 1 6 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 17 1 2 -1.</_>
        <_>
          19 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 9 4 2 -1.</_>
        <_>
          17 10 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 18 4 2 -1.</_>
        <_>
          7 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 12 4 4 -1.</_>
        <_>
          3 12 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 2 1 2 -1.</_>
        <_>
          19 3 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 2 1 3 -1.</_>
        <_>
          19 3 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 12 12 3 -1.</_>
        <_>
          7 12 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 18 4 1 -1.</_>
        <_>
          7 18 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 2 12 6 -1.</_>
        <_>
          5 5 12 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 1 6 6 -1.</_>
        <_>
          9 4 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 11 9 -1.</_>
        <_>
          7 3 11 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 8 9 -1.</_>
        <_>
          2 3 8 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 3 4 3 -1.</_>
        <_>
          6 3 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 3 2 -1.</_>
        <_>
          0 19 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 10 19 -1.</_>
        <_>
          6 0 5 19 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 8 2 3 -1.</_>
        <_>
          2 9 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 17 4 3 -1.</_>
        <_>
          11 17 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 13 3 2 -1.</_>
        <_>
          12 13 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 12 3 2 -1.</_>
        <_>
          11 12 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 11 3 3 -1.</_>
        <_>
          10 11 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 2 3 1 -1.</_>
        <_>
          18 3 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 0 6 13 -1.</_>
        <_>
          14 0 2 13 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 0 3 1 -1.</_>
        <_>
          17 1 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 11 1 2 -1.</_>
        <_>
          5 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 11 4 2 -1.</_>
        <_>
          2 11 2 1 2.</_>
        <_>
          4 12 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 15 2 3 -1.</_>
        <_>
          15 16 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 17 4 2 -1.</_>
        <_>
          9 17 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 4 3 -1.</_>
        <_>
          0 17 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 13 6 2 -1.</_>
        <_>
          12 13 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 14 1 2 -1.</_>
        <_>
          2 14 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 10 8 3 -1.</_>
        <_>
          5 11 8 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 0 3 8 -1.</_>
        <_>
          13 2 3 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 11 4 7 -1.</_>
        <_>
          15 11 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 11 15 4 -1.</_>
        <_>
          8 11 5 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 1 9 9 -1.</_>
        <_>
          12 1 3 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 11 4 7 -1.</_>
        <_>
          2 11 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 1 4 -1.</_>
        <_>
          0 18 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 0 1 6 -1.</_>
        <_>
          19 3 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 8 9 9 -1.</_>
        <_>
          11 11 9 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 17 8 3 -1.</_>
        <_>
          11 17 4 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 4 2 2 -1.</_>
        <_>
          19 4 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 11 3 3 -1.</_>
        <_>
          9 12 1 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          13 2 3 4 -1.</_>
        <_>
          13 2 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 6 16 3 -1.</_>
        <_>
          12 6 8 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 12 1 3 -1.</_>
        <_>
          9 13 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 12 3 3 -1.</_>
        <_>
          9 13 1 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          17 17 1 2 -1.</_>
        <_>
          17 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 16 2 2 -1.</_>
        <_>
          16 16 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 0 9 6 -1.</_>
        <_>
          6 2 9 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 10 8 -1.</_>
        <_>
          5 2 10 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 5 2 1 -1.</_>
        <_>
          18 5 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 9 9 -1.</_>
        <_>
          14 0 3 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 7 3 -1.</_>
        <_>
          6 10 7 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 12 6 2 -1.</_>
        <_>
          3 12 3 1 2.</_>
        <_>
          6 13 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 10 1 2 -1.</_>
        <_>
          2 10 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 15 2 3 -1.</_>
        <_>
          12 16 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 2 6 5 -1.</_>
        <_>
          9 2 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 13 6 3 -1.</_>
        <_>
          15 13 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 9 3 8 -1.</_>
        <_>
          17 11 3 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 3 4 3 -1.</_>
        <_>
          9 3 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 6 2 12 -1.</_>
        <_>
          15 6 1 12 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 14 4 2 -1.</_>
        <_>
          11 14 4 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 2 5 4 -1.</_>
        <_>
          9 4 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 12 3 3 -1.</_>
        <_>
          14 12 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 1 2 3 -1.</_>
        <_>
          18 2 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 13 4 1 -1.</_>
        <_>
          6 13 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 10 2 2 -1.</_>
        <_>
          5 10 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 11 1 2 -1.</_>
        <_>
          2 11 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 3 2 6 -1.</_>
        <_>
          18 5 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 4 6 2 -1.</_>
        <_>
          10 5 6 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 13 6 2 -1.</_>
        <_>
          13 13 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 11 3 4 -1.</_>
        <_>
          9 11 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 11 2 5 -1.</_>
        <_>
          1 11 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 20 9 -1.</_>
        <_>
          0 11 20 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 0 1 6 -1.</_>
        <_>
          18 3 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 1 6 7 -1.</_>
        <_>
          17 1 3 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 13 2 4 -1.</_>
        <_>
          4 13 1 2 2.</_>
        <_>
          5 15 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 9 18 6 -1.</_>
        <_>
          7 9 6 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 5 4 -1.</_>
        <_>
          0 18 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 14 3 4 -1.</_>
        <_>
          8 15 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 8 3 -1.</_>
        <_>
          11 7 4 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 3 4 7 -1.</_>
        <_>
          13 3 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 12 2 8 -1.</_>
        <_>
          13 12 1 4 2.</_>
        <_>
          14 16 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 10 3 5 -1.</_>
        <_>
          14 11 1 5 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 5 4 5 -1.</_>
        <_>
          11 5 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 11 18 2 -1.</_>
        <_>
          8 11 6 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 1 2 -1.</_>
        <_>
          2 0 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 0 1 2 -1.</_>
        <_>
          2 0 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 17 1 2 -1.</_>
        <_>
          15 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 16 1 3 -1.</_>
        <_>
          16 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 0 2 10 -1.</_>
        <_>
          19 0 1 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 2 6 7 -1.</_>
        <_>
          16 2 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 0 4 4 -1.</_>
        <_>
          12 0 4 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 3 15 6 -1.</_>
        <_>
          0 5 15 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 1 4 4 -1.</_>
        <_>
          6 1 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 13 6 7 -1.</_>
        <_>
          9 13 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 18 6 2 -1.</_>
        <_>
          8 18 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 5 2 -1.</_>
        <_>
          0 16 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 1 12 6 -1.</_>
        <_>
          4 3 12 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 13 8 -1.</_>
        <_>
          5 2 13 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 10 6 6 -1.</_>
        <_>
          15 12 2 2 9.</_></rects></_>
    <_>
      <rects>
        <_>
          15 9 3 1 -1.</_>
        <_>
          16 10 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 11 3 3 -1.</_>
        <_>
          6 12 1 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          6 11 2 2 -1.</_>
        <_>
          6 11 1 1 2.</_>
        <_>
          7 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 3 3 2 -1.</_>
        <_>
          18 4 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 3 3 3 -1.</_>
        <_>
          17 4 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 13 3 1 -1.</_>
        <_>
          13 13 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 12 3 2 -1.</_>
        <_>
          12 12 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 1 2 -1.</_>
        <_>
          10 0 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 13 1 6 -1.</_>
        <_>
          17 13 1 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 14 2 4 -1.</_>
        <_>
          16 14 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 0 4 3 -1.</_>
        <_>
          4 0 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 14 1 -1.</_>
        <_>
          13 0 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 15 18 5 -1.</_>
        <_>
          8 15 6 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 11 8 5 -1.</_>
        <_>
          8 11 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 5 12 -1.</_>
        <_>
          0 11 5 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 0 6 2 -1.</_>
        <_>
          14 0 6 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 8 4 5 -1.</_>
        <_>
          14 9 2 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 11 4 9 -1.</_>
        <_>
          2 11 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 2 6 -1.</_>
        <_>
          6 11 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 18 4 2 -1.</_>
        <_>
          12 19 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 13 6 2 -1.</_>
        <_>
          16 13 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 9 1 10 -1.</_>
        <_>
          19 9 1 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 5 4 4 -1.</_>
        <_>
          12 5 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 12 3 5 -1.</_>
        <_>
          15 12 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 0 2 6 -1.</_>
        <_>
          18 0 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 16 3 3 -1.</_>
        <_>
          14 16 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 0 1 4 -1.</_>
        <_>
          19 2 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 13 4 2 -1.</_>
        <_>
          7 13 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 11 3 3 -1.</_>
        <_>
          10 11 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 15 2 3 -1.</_>
        <_>
          13 16 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 7 3 4 -1.</_>
        <_>
          12 7 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 12 1 3 -1.</_>
        <_>
          4 13 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 11 6 2 -1.</_>
        <_>
          1 11 3 1 2.</_>
        <_>
          4 12 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 2 3 -1.</_>
        <_>
          4 8 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 12 2 2 -1.</_>
        <_>
          5 12 1 1 2.</_>
        <_>
          6 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 8 4 3 -1.</_>
        <_>
          8 9 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 8 5 3 -1.</_>
        <_>
          7 9 5 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 19 4 1 -1.</_>
        <_>
          7 19 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 4 4 -1.</_>
        <_>
          6 0 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 16 8 -1.</_>
        <_>
          8 0 8 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 11 3 4 -1.</_>
        <_>
          11 12 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 4 20 6 -1.</_>
        <_>
          5 4 10 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 2 2 4 -1.</_>
        <_>
          13 2 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 5 14 15 -1.</_>
        <_>
          7 5 7 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 18 3 2 -1.</_>
        <_>
          1 19 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 6 3 3 -1.</_>
        <_>
          2 7 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 1 6 8 -1.</_>
        <_>
          0 1 3 4 2.</_>
        <_>
          3 5 3 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 6 6 -1.</_>
        <_>
          7 0 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 1 15 8 -1.</_>
        <_>
          1 3 15 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 16 1 -1.</_>
        <_>
          8 0 8 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 1 2 -1.</_>
        <_>
          3 0 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 13 4 1 -1.</_>
        <_>
          4 13 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 11 2 2 -1.</_>
        <_>
          4 11 1 1 2.</_>
        <_>
          5 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 2 3 3 -1.</_>
        <_>
          18 3 1 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          16 3 2 1 -1.</_>
        <_>
          17 3 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 11 3 2 -1.</_>
        <_>
          0 12 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 11 4 2 -1.</_>
        <_>
          4 11 2 1 2.</_>
        <_>
          6 12 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 4 11 -1.</_>
        <_>
          11 0 2 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 15 2 3 -1.</_>
        <_>
          17 16 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 11 8 1 -1.</_>
        <_>
          2 11 4 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 13 1 6 -1.</_>
        <_>
          17 13 1 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 13 6 2 -1.</_>
        <_>
          13 13 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 0 1 10 -1.</_>
        <_>
          19 5 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 8 7 9 -1.</_>
        <_>
          2 11 7 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 11 20 2 -1.</_>
        <_>
          5 11 10 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 14 6 1 -1.</_>
        <_>
          8 14 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 3 8 7 -1.</_>
        <_>
          12 3 4 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 5 9 -1.</_>
        <_>
          7 3 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 16 6 -1.</_>
        <_>
          0 2 16 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 10 2 6 -1.</_>
        <_>
          4 12 2 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 0 4 14 -1.</_>
        <_>
          18 0 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 9 6 -1.</_>
        <_>
          6 2 9 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 18 12 2 -1.</_>
        <_>
          8 19 12 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 17 4 3 -1.</_>
        <_>
          11 17 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 1 4 -1.</_>
        <_>
          4 1 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 6 2 2 -1.</_>
        <_>
          18 6 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 10 3 4 -1.</_>
        <_>
          11 11 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 9 4 3 -1.</_>
        <_>
          9 10 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 10 4 3 -1.</_>
        <_>
          9 11 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 4 3 4 -1.</_>
        <_>
          18 5 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 0 2 3 -1.</_>
        <_>
          18 1 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 1 2 2 -1.</_>
        <_>
          18 2 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 1 1 3 -1.</_>
        <_>
          19 2 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 18 4 2 -1.</_>
        <_>
          9 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 13 4 2 -1.</_>
        <_>
          2 13 2 1 2.</_>
        <_>
          4 14 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 11 4 2 -1.</_>
        <_>
          3 11 2 1 2.</_>
        <_>
          5 12 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 10 4 2 -1.</_>
        <_>
          2 10 2 1 2.</_>
        <_>
          4 11 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 9 2 3 -1.</_>
        <_>
          4 10 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 10 4 6 -1.</_>
        <_>
          3 10 2 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 0 6 8 -1.</_>
        <_>
          16 0 3 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 8 9 -1.</_>
        <_>
          12 0 4 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 11 8 1 -1.</_>
        <_>
          1 11 4 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 0 1 3 -1.</_>
        <_>
          2 1 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 13 2 2 -1.</_>
        <_>
          14 13 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 12 3 4 -1.</_>
        <_>
          5 12 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 4 3 -1.</_>
        <_>
          7 17 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 1 2 6 -1.</_>
        <_>
          14 1 2 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 4 8 4 -1.</_>
        <_>
          8 6 8 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 3 4 5 -1.</_>
        <_>
          10 3 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 12 2 2 -1.</_>
        <_>
          13 12 1 1 2.</_>
        <_>
          14 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 12 3 3 -1.</_>
        <_>
          7 12 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 3 3 -1.</_>
        <_>
          4 8 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 10 5 4 -1.</_>
        <_>
          15 11 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 8 4 9 -1.</_>
        <_>
          14 11 4 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 9 4 3 -1.</_>
        <_>
          16 10 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 7 2 13 -1.</_>
        <_>
          19 7 1 13 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 16 1 -1.</_>
        <_>
          8 0 8 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 11 5 4 -1.</_>
        <_>
          11 12 5 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 13 2 4 -1.</_>
        <_>
          18 13 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 13 9 2 -1.</_>
        <_>
          9 13 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 8 6 8 -1.</_>
        <_>
          3 10 6 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 12 4 3 -1.</_>
        <_>
          15 12 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 6 6 4 -1.</_>
        <_>
          14 8 2 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 0 12 6 -1.</_>
        <_>
          4 3 12 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 17 2 -1.</_>
        <_>
          0 1 17 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 14 1 6 -1.</_>
        <_>
          2 17 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 10 3 3 -1.</_>
        <_>
          2 11 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 2 2 9 -1.</_>
        <_>
          19 2 1 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 9 13 8 -1.</_>
        <_>
          7 11 13 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 6 3 4 -1.</_>
        <_>
          18 7 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 13 2 2 -1.</_>
        <_>
          7 13 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 16 1 3 -1.</_>
        <_>
          14 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 16 6 4 -1.</_>
        <_>
          11 16 3 2 2.</_>
        <_>
          14 18 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 0 1 4 -1.</_>
        <_>
          19 1 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 0 1 2 -1.</_>
        <_>
          19 1 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 3 3 6 -1.</_>
        <_>
          13 3 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 10 4 3 -1.</_>
        <_>
          8 11 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 0 1 8 -1.</_>
        <_>
          19 4 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 0 6 6 -1.</_>
        <_>
          14 0 3 3 2.</_>
        <_>
          17 3 3 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 11 3 3 -1.</_>
        <_>
          9 12 1 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          1 6 10 12 -1.</_>
        <_>
          6 6 5 12 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 6 2 1 -1.</_>
        <_>
          11 6 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 7 10 -1.</_>
        <_>
          8 6 7 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 3 3 -1.</_>
        <_>
          14 12 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 13 4 4 -1.</_>
        <_>
          10 13 2 2 2.</_>
        <_>
          12 15 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 15 2 3 -1.</_>
        <_>
          14 16 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 13 3 1 -1.</_>
        <_>
          14 13 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 4 6 3 -1.</_>
        <_>
          12 4 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 6 4 -1.</_>
        <_>
          1 7 3 2 2.</_>
        <_>
          4 9 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 7 4 2 -1.</_>
        <_>
          16 8 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 4 9 6 -1.</_>
        <_>
          13 4 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 2 6 2 -1.</_>
        <_>
          14 2 6 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 18 4 2 -1.</_>
        <_>
          6 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 2 8 -1.</_>
        <_>
          1 12 1 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 19 18 1 -1.</_>
        <_>
          10 19 9 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 12 20 -1.</_>
        <_>
          8 0 6 20 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 14 1 -1.</_>
        <_>
          9 0 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 9 8 3 -1.</_>
        <_>
          7 10 8 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 11 2 2 -1.</_>
        <_>
          3 11 1 1 2.</_>
        <_>
          4 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 9 2 -1.</_>
        <_>
          14 0 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 9 1 -1.</_>
        <_>
          9 0 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 8 1 4 -1.</_>
        <_>
          3 9 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 9 3 3 -1.</_>
        <_>
          0 10 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 4 15 12 -1.</_>
        <_>
          8 8 5 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          7 13 6 6 -1.</_>
        <_>
          9 13 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 1 12 6 -1.</_>
        <_>
          2 3 12 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 1 6 1 -1.</_>
        <_>
          3 3 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 4 5 3 -1.</_>
        <_>
          2 5 5 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 12 2 2 -1.</_>
        <_>
          2 12 1 1 2.</_>
        <_>
          3 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 11 3 3 -1.</_>
        <_>
          9 11 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 11 3 4 -1.</_>
        <_>
          10 11 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 2 3 1 -1.</_>
        <_>
          18 3 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 11 6 3 -1.</_>
        <_>
          8 11 3 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 12 12 8 -1.</_>
        <_>
          2 12 6 4 2.</_>
        <_>
          8 16 6 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 15 2 3 -1.</_>
        <_>
          12 16 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 14 9 1 -1.</_>
        <_>
          8 14 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 13 4 6 -1.</_>
        <_>
          13 13 2 3 2.</_>
        <_>
          15 16 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 7 9 1 -1.</_>
        <_>
          11 10 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 0 4 4 -1.</_>
        <_>
          16 0 4 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 13 2 2 -1.</_>
        <_>
          2 13 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 12 2 2 -1.</_>
        <_>
          5 13 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 2 4 -1.</_>
        <_>
          0 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 14 11 -1.</_>
        <_>
          7 8 7 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 17 4 3 -1.</_>
        <_>
          5 17 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 12 3 5 -1.</_>
        <_>
          4 12 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 11 1 3 -1.</_>
        <_>
          5 12 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 10 4 2 -1.</_>
        <_>
          4 10 2 1 2.</_>
        <_>
          6 11 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 9 3 1 -1.</_>
        <_>
          16 10 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 0 16 7 -1.</_>
        <_>
          7 0 8 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 2 17 6 -1.</_>
        <_>
          2 5 17 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 4 14 6 -1.</_>
        <_>
          2 6 14 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 9 6 2 -1.</_>
        <_>
          2 9 3 1 2.</_>
        <_>
          5 10 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 11 4 2 -1.</_>
        <_>
          3 11 2 1 2.</_>
        <_>
          5 12 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 13 4 2 -1.</_>
        <_>
          18 13 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 7 3 2 -1.</_>
        <_>
          16 8 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 11 4 2 -1.</_>
        <_>
          0 12 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 9 2 3 -1.</_>
        <_>
          3 10 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 18 6 2 -1.</_>
        <_>
          5 18 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 12 3 2 -1.</_>
        <_>
          12 12 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 0 1 2 -1.</_>
        <_>
          19 1 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 14 1 -1.</_>
        <_>
          7 0 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 10 3 4 -1.</_>
        <_>
          10 11 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 16 1 3 -1.</_>
        <_>
          13 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 1 2 4 -1.</_>
        <_>
          19 1 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 13 5 6 -1.</_>
        <_>
          15 15 5 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 4 3 3 -1.</_>
        <_>
          17 5 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 6 16 14 -1.</_>
        <_>
          12 6 8 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 12 3 1 -1.</_>
        <_>
          11 12 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 12 2 2 -1.</_>
        <_>
          5 12 1 1 2.</_>
        <_>
          6 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 3 4 5 -1.</_>
        <_>
          10 3 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 1 2 3 -1.</_>
        <_>
          18 2 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 17 1 2 -1.</_>
        <_>
          19 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 16 2 2 -1.</_>
        <_>
          17 16 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 2 7 6 -1.</_>
        <_>
          10 4 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 13 4 -1.</_>
        <_>
          2 1 13 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 2 2 -1.</_>
        <_>
          2 0 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 3 6 8 -1.</_>
        <_>
          3 3 3 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 1 3 -1.</_>
        <_>
          2 1 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 0 6 9 -1.</_>
        <_>
          10 0 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 9 3 2 -1.</_>
        <_>
          18 10 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 8 4 6 -1.</_>
        <_>
          16 10 4 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 7 3 -1.</_>
        <_>
          6 10 7 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 10 3 4 -1.</_>
        <_>
          2 11 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 8 1 6 -1.</_>
        <_>
          15 8 1 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 3 1 12 -1.</_>
        <_>
          19 7 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 5 2 -1.</_>
        <_>
          2 0 5 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 3 11 6 -1.</_>
        <_>
          1 5 11 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 13 2 4 -1.</_>
        <_>
          14 13 1 2 2.</_>
        <_>
          15 15 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 11 10 3 -1.</_>
        <_>
          13 11 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 11 1 4 -1.</_>
        <_>
          6 13 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 9 3 9 -1.</_>
        <_>
          3 12 1 3 9.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 15 9 -1.</_>
        <_>
          9 3 5 3 9.</_></rects></_>
    <_>
      <rects>
        <_>
          12 0 6 4 -1.</_>
        <_>
          12 0 6 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 5 4 5 -1.</_>
        <_>
          12 5 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 18 12 -1.</_>
        <_>
          7 11 6 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          14 12 6 4 -1.</_>
        <_>
          16 12 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 12 3 3 -1.</_>
        <_>
          14 12 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 9 4 1 -1.</_>
        <_>
          15 10 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 7 3 2 -1.</_>
        <_>
          18 8 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 3 1 2 -1.</_>
        <_>
          19 4 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 1 1 4 -1.</_>
        <_>
          19 2 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 2 12 8 -1.</_>
        <_>
          3 4 12 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 16 6 -1.</_>
        <_>
          1 2 16 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 8 3 1 -1.</_>
        <_>
          17 9 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 13 6 3 -1.</_>
        <_>
          9 14 2 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          11 18 6 2 -1.</_>
        <_>
          11 19 6 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 17 5 3 -1.</_>
        <_>
          15 18 5 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 1 18 4 -1.</_>
        <_>
          8 1 6 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 1 2 -1.</_>
        <_>
          5 1 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 11 6 6 -1.</_>
        <_>
          3 13 2 2 9.</_></rects></_>
    <_>
      <rects>
        <_>
          3 12 4 2 -1.</_>
        <_>
          3 12 2 1 2.</_>
        <_>
          5 13 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 3 3 -1.</_>
        <_>
          2 1 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 10 3 3 -1.</_>
        <_>
          9 11 1 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 2 2 -1.</_>
        <_>
          0 17 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 4 3 -1.</_>
        <_>
          0 17 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 13 12 1 -1.</_>
        <_>
          6 13 6 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 2 6 9 -1.</_>
        <_>
          15 2 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 11 3 3 -1.</_>
        <_>
          9 11 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 11 3 4 -1.</_>
        <_>
          10 11 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 0 6 10 -1.</_>
        <_>
          15 0 2 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 10 1 4 -1.</_>
        <_>
          3 11 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 11 3 3 -1.</_>
        <_>
          10 12 1 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          6 12 3 3 -1.</_>
        <_>
          5 13 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 6 2 1 -1.</_>
        <_>
          18 6 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 2 1 4 -1.</_>
        <_>
          16 2 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 5 13 4 -1.</_>
        <_>
          2 6 13 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 4 6 2 -1.</_>
        <_>
          14 4 6 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 8 1 3 -1.</_>
        <_>
          2 9 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 7 8 3 -1.</_>
        <_>
          7 8 8 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 8 4 3 -1.</_>
        <_>
          10 8 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 11 3 8 -1.</_>
        <_>
          10 15 3 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 15 2 3 -1.</_>
        <_>
          12 16 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 0 12 20 -1.</_>
        <_>
          6 0 6 20 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 10 1 -1.</_>
        <_>
          5 0 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 6 3 -1.</_>
        <_>
          0 1 6 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 13 2 2 -1.</_>
        <_>
          14 13 1 1 2.</_>
        <_>
          15 14 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 10 4 2 -1.</_>
        <_>
          12 10 2 1 2.</_>
        <_>
          14 11 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 6 4 -1.</_>
        <_>
          9 0 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 10 10 -1.</_>
        <_>
          0 0 5 5 2.</_>
        <_>
          5 5 5 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 3 4 2 -1.</_>
        <_>
          7 3 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 5 4 11 -1.</_>
        <_>
          2 5 2 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 8 3 1 -1.</_>
        <_>
          13 8 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 2 6 2 -1.</_>
        <_>
          2 2 6 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 5 7 3 -1.</_>
        <_>
          12 6 7 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 7 3 4 -1.</_>
        <_>
          14 7 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 12 3 2 -1.</_>
        <_>
          8 12 3 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 10 4 8 -1.</_>
        <_>
          0 12 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 13 2 6 -1.</_>
        <_>
          14 13 1 3 2.</_>
        <_>
          15 16 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 17 1 2 -1.</_>
        <_>
          16 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 0 3 6 -1.</_>
        <_>
          10 2 3 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 10 14 3 -1.</_>
        <_>
          4 11 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 4 1 12 -1.</_>
        <_>
          19 8 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 2 1 6 -1.</_>
        <_>
          19 4 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 12 12 3 -1.</_>
        <_>
          14 12 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 13 2 3 -1.</_>
        <_>
          1 13 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 0 4 9 -1.</_>
        <_>
          18 0 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 2 6 4 -1.</_>
        <_>
          9 4 6 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 2 3 1 -1.</_>
        <_>
          17 3 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 12 3 6 -1.</_>
        <_>
          16 12 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 12 3 3 -1.</_>
        <_>
          14 12 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 3 15 4 -1.</_>
        <_>
          3 5 15 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 11 3 4 -1.</_>
        <_>
          12 11 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 11 3 3 -1.</_>
        <_>
          11 11 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          19 0 1 4 -1.</_>
        <_>
          19 2 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 0 3 3 -1.</_>
        <_>
          15 1 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 10 8 2 -1.</_>
        <_>
          2 10 4 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 18 4 2 -1.</_>
        <_>
          10 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 4 9 -1.</_>
        <_>
          11 0 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 10 5 6 -1.</_>
        <_>
          15 12 5 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 13 4 2 -1.</_>
        <_>
          3 13 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 15 4 1 -1.</_>
        <_>
          3 16 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 8 3 2 -1.</_>
        <_>
          16 9 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 6 4 2 -1.</_>
        <_>
          2 6 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 17 6 1 -1.</_>
        <_>
          12 17 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 19 6 1 -1.</_>
        <_>
          17 19 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 18 1 2 -1.</_>
        <_>
          17 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 16 2 2 -1.</_>
        <_>
          17 16 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 3 1 9 -1.</_>
        <_>
          19 6 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 10 3 3 -1.</_>
        <_>
          9 11 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 0 3 3 -1.</_>
        <_>
          2 1 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 16 2 2 -1.</_>
        <_>
          17 16 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 11 3 3 -1.</_>
        <_>
          6 12 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 11 2 2 -1.</_>
        <_>
          3 11 1 1 2.</_>
        <_>
          4 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 9 2 2 -1.</_>
        <_>
          16 9 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 9 2 2 -1.</_>
        <_>
          4 9 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 10 2 3 -1.</_>
        <_>
          2 11 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 0 20 20 -1.</_>
        <_>
          0 0 10 10 2.</_>
        <_>
          10 10 10 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 16 5 3 -1.</_>
        <_>
          7 17 5 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 1 3 6 -1.</_>
        <_>
          12 3 3 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 0 4 7 -1.</_>
        <_>
          7 0 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 5 9 6 -1.</_>
        <_>
          12 5 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 18 4 2 -1.</_>
        <_>
          6 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 6 8 -1.</_>
        <_>
          9 7 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 16 2 4 -1.</_>
        <_>
          18 16 1 2 2.</_>
        <_>
          19 18 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 18 2 2 -1.</_>
        <_>
          12 18 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 2 5 2 -1.</_>
        <_>
          3 3 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 6 4 -1.</_>
        <_>
          7 3 6 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 2 2 -1.</_>
        <_>
          2 0 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 1 16 1 -1.</_>
        <_>
          8 1 8 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 1 3 10 -1.</_>
        <_>
          12 1 1 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 4 4 -1.</_>
        <_>
          5 1 2 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 13 3 2 -1.</_>
        <_>
          5 13 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 11 4 3 -1.</_>
        <_>
          7 12 4 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 17 4 3 -1.</_>
        <_>
          8 17 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 19 2 1 -1.</_>
        <_>
          6 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 2 2 -1.</_>
        <_>
          0 9 1 1 2.</_>
        <_>
          1 10 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 2 2 -1.</_>
        <_>
          0 9 1 1 2.</_>
        <_>
          1 10 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 2 2 -1.</_>
        <_>
          6 9 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 10 5 3 -1.</_>
        <_>
          0 11 5 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 10 2 2 -1.</_>
        <_>
          3 10 1 1 2.</_>
        <_>
          4 11 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 10 18 1 -1.</_>
        <_>
          6 10 6 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 4 3 1 -1.</_>
        <_>
          18 5 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 1 2 7 -1.</_>
        <_>
          17 1 1 7 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 13 9 2 -1.</_>
        <_>
          9 13 3 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 9 16 6 -1.</_>
        <_>
          4 11 16 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 1 16 4 -1.</_>
        <_>
          1 3 16 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 12 3 3 -1.</_>
        <_>
          15 12 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 9 6 2 -1.</_>
        <_>
          4 11 2 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 0 8 10 -1.</_>
        <_>
          12 0 4 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 12 16 4 -1.</_>
        <_>
          5 12 8 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 8 6 9 -1.</_>
        <_>
          15 11 2 3 9.</_></rects></_>
    <_>
      <rects>
        <_>
          19 0 1 8 -1.</_>
        <_>
          19 4 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 2 10 6 -1.</_>
        <_>
          8 5 10 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 7 2 1 -1.</_>
        <_>
          19 7 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 4 1 12 -1.</_>
        <_>
          19 7 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 11 3 3 -1.</_>
        <_>
          9 12 1 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          7 12 3 3 -1.</_>
        <_>
          8 12 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 13 3 2 -1.</_>
        <_>
          7 13 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 15 3 2 -1.</_>
        <_>
          17 15 3 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 6 3 3 -1.</_>
        <_>
          12 6 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 2 4 -1.</_>
        <_>
          0 17 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 9 7 2 -1.</_>
        <_>
          12 9 7 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 5 8 7 -1.</_>
        <_>
          10 5 4 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 8 3 -1.</_>
        <_>
          8 17 4 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 4 3 -1.</_>
        <_>
          0 18 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 1 10 6 -1.</_>
        <_>
          5 3 10 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 2 18 2 -1.</_>
        <_>
          6 2 6 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 8 6 3 -1.</_>
        <_>
          7 9 6 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 1 3 -1.</_>
        <_>
          10 9 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 1 3 2 -1.</_>
        <_>
          17 2 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 10 1 2 -1.</_>
        <_>
          2 10 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 9 1 2 -1.</_>
        <_>
          2 9 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 9 2 3 -1.</_>
        <_>
          2 10 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 14 12 6 -1.</_>
        <_>
          2 14 6 3 2.</_>
        <_>
          8 17 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 17 1 2 -1.</_>
        <_>
          15 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 11 3 3 -1.</_>
        <_>
          18 12 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 12 3 2 -1.</_>
        <_>
          14 12 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 18 4 2 -1.</_>
        <_>
          18 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 14 2 4 -1.</_>
        <_>
          17 15 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 13 3 1 -1.</_>
        <_>
          13 13 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 12 3 3 -1.</_>
        <_>
          12 13 1 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 16 20 -1.</_>
        <_>
          8 0 8 20 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 8 5 -1.</_>
        <_>
          5 0 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 2 1 -1.</_>
        <_>
          1 0 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 2 19 4 -1.</_>
        <_>
          1 4 19 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 7 3 4 -1.</_>
        <_>
          13 7 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 6 3 3 -1.</_>
        <_>
          16 7 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 13 2 2 -1.</_>
        <_>
          3 13 1 1 2.</_>
        <_>
          4 14 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 12 2 2 -1.</_>
        <_>
          2 12 1 1 2.</_>
        <_>
          3 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 19 4 -1.</_>
        <_>
          0 4 19 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 7 3 4 -1.</_>
        <_>
          18 8 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 8 3 4 -1.</_>
        <_>
          5 9 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 11 4 6 -1.</_>
        <_>
          15 11 2 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 3 2 6 -1.</_>
        <_>
          18 5 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 3 2 4 -1.</_>
        <_>
          14 3 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 9 5 4 -1.</_>
        <_>
          7 10 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 11 8 2 -1.</_>
        <_>
          12 12 8 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 13 3 4 -1.</_>
        <_>
          16 13 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 7 5 9 -1.</_>
        <_>
          14 10 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 1 3 -1.</_>
        <_>
          0 13 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 6 3 6 -1.</_>
        <_>
          4 8 3 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 9 9 1 -1.</_>
        <_>
          3 9 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 6 2 -1.</_>
        <_>
          0 9 3 1 2.</_>
        <_>
          3 10 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 2 4 4 -1.</_>
        <_>
          4 2 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 3 2 3 -1.</_>
        <_>
          18 4 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 16 3 3 -1.</_>
        <_>
          6 17 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 16 6 3 -1.</_>
        <_>
          1 17 6 1 3.</_></rects></_></features></cascade>
</opencv_storage>
