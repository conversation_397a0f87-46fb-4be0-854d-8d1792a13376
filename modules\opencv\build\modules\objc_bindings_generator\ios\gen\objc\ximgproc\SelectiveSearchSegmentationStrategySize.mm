//
// This file is auto-generated. Please don't modify it!
//

#import "SelectiveSearchSegmentationStrategySize.h"
#import "CVObjcUtil.h"



@implementation SelectiveSearchSegmentationStrategySize


- (instancetype)initWithNativePtr:(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategySize>)nativePtr {
    self = [super initWithNativePtr:nativePtr];
    if (self) {
        _nativePtrSelectiveSearchSegmentationStrategySize = nativePtr;
    }
    return self;
}

+ (instancetype)fromNative:(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategySize>)nativePtr {
    return [[SelectiveSearchSegmentationStrategySize alloc] initWithNativePtr:nativePtr];
}




@end


