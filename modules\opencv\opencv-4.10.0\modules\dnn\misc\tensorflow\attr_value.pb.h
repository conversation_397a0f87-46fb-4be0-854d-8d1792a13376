// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: attr_value.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_attr_5fvalue_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_attr_5fvalue_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensor.pb.h"
#include "tensor_shape.pb.h"
#include "types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_attr_5fvalue_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_attr_5fvalue_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_attr_5fvalue_2eproto;
namespace opencv_tensorflow {
class AttrValue;
struct AttrValueDefaultTypeInternal;
extern AttrValueDefaultTypeInternal _AttrValue_default_instance_;
class AttrValue_ListValue;
struct AttrValue_ListValueDefaultTypeInternal;
extern AttrValue_ListValueDefaultTypeInternal _AttrValue_ListValue_default_instance_;
class NameAttrList;
struct NameAttrListDefaultTypeInternal;
extern NameAttrListDefaultTypeInternal _NameAttrList_default_instance_;
class NameAttrList_AttrEntry_DoNotUse;
struct NameAttrList_AttrEntry_DoNotUseDefaultTypeInternal;
extern NameAttrList_AttrEntry_DoNotUseDefaultTypeInternal _NameAttrList_AttrEntry_DoNotUse_default_instance_;
}  // namespace opencv_tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::opencv_tensorflow::AttrValue* Arena::CreateMaybeMessage<::opencv_tensorflow::AttrValue>(Arena*);
template<> ::opencv_tensorflow::AttrValue_ListValue* Arena::CreateMaybeMessage<::opencv_tensorflow::AttrValue_ListValue>(Arena*);
template<> ::opencv_tensorflow::NameAttrList* Arena::CreateMaybeMessage<::opencv_tensorflow::NameAttrList>(Arena*);
template<> ::opencv_tensorflow::NameAttrList_AttrEntry_DoNotUse* Arena::CreateMaybeMessage<::opencv_tensorflow::NameAttrList_AttrEntry_DoNotUse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace opencv_tensorflow {

// ===================================================================

class AttrValue_ListValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_tensorflow.AttrValue.ListValue) */ {
 public:
  inline AttrValue_ListValue() : AttrValue_ListValue(nullptr) {}
  ~AttrValue_ListValue() override;
  explicit constexpr AttrValue_ListValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AttrValue_ListValue(const AttrValue_ListValue& from);
  AttrValue_ListValue(AttrValue_ListValue&& from) noexcept
    : AttrValue_ListValue() {
    *this = ::std::move(from);
  }

  inline AttrValue_ListValue& operator=(const AttrValue_ListValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline AttrValue_ListValue& operator=(AttrValue_ListValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AttrValue_ListValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const AttrValue_ListValue* internal_default_instance() {
    return reinterpret_cast<const AttrValue_ListValue*>(
               &_AttrValue_ListValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(AttrValue_ListValue& a, AttrValue_ListValue& b) {
    a.Swap(&b);
  }
  inline void Swap(AttrValue_ListValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AttrValue_ListValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AttrValue_ListValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AttrValue_ListValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AttrValue_ListValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AttrValue_ListValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AttrValue_ListValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_tensorflow.AttrValue.ListValue";
  }
  protected:
  explicit AttrValue_ListValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSFieldNumber = 2,
    kIFieldNumber = 3,
    kFFieldNumber = 4,
    kBFieldNumber = 5,
    kTypeFieldNumber = 6,
    kShapeFieldNumber = 7,
    kTensorFieldNumber = 8,
  };
  // repeated bytes s = 2;
  int s_size() const;
  private:
  int _internal_s_size() const;
  public:
  void clear_s();
  const std::string& s(int index) const;
  std::string* mutable_s(int index);
  void set_s(int index, const std::string& value);
  void set_s(int index, std::string&& value);
  void set_s(int index, const char* value);
  void set_s(int index, const void* value, size_t size);
  std::string* add_s();
  void add_s(const std::string& value);
  void add_s(std::string&& value);
  void add_s(const char* value);
  void add_s(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& s() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_s();
  private:
  const std::string& _internal_s(int index) const;
  std::string* _internal_add_s();
  public:

  // repeated int64 i = 3 [packed = true];
  int i_size() const;
  private:
  int _internal_i_size() const;
  public:
  void clear_i();
  private:
  int64_t _internal_i(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_i() const;
  void _internal_add_i(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_i();
  public:
  int64_t i(int index) const;
  void set_i(int index, int64_t value);
  void add_i(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      i() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_i();

  // repeated float f = 4 [packed = true];
  int f_size() const;
  private:
  int _internal_f_size() const;
  public:
  void clear_f();
  private:
  float _internal_f(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_f() const;
  void _internal_add_f(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_f();
  public:
  float f(int index) const;
  void set_f(int index, float value);
  void add_f(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      f() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_f();

  // repeated bool b = 5 [packed = true];
  int b_size() const;
  private:
  int _internal_b_size() const;
  public:
  void clear_b();
  private:
  bool _internal_b(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      _internal_b() const;
  void _internal_add_b(bool value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      _internal_mutable_b();
  public:
  bool b(int index) const;
  void set_b(int index, bool value);
  void add_b(bool value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      b() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      mutable_b();

  // repeated .opencv_tensorflow.DataType type = 6 [packed = true];
  int type_size() const;
  private:
  int _internal_type_size() const;
  public:
  void clear_type();
  private:
  ::opencv_tensorflow::DataType _internal_type(int index) const;
  void _internal_add_type(::opencv_tensorflow::DataType value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* _internal_mutable_type();
  public:
  ::opencv_tensorflow::DataType type(int index) const;
  void set_type(int index, ::opencv_tensorflow::DataType value);
  void add_type(::opencv_tensorflow::DataType value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>& type() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* mutable_type();

  // repeated .opencv_tensorflow.TensorShapeProto shape = 7;
  int shape_size() const;
  private:
  int _internal_shape_size() const;
  public:
  void clear_shape();
  ::opencv_tensorflow::TensorShapeProto* mutable_shape(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::TensorShapeProto >*
      mutable_shape();
  private:
  const ::opencv_tensorflow::TensorShapeProto& _internal_shape(int index) const;
  ::opencv_tensorflow::TensorShapeProto* _internal_add_shape();
  public:
  const ::opencv_tensorflow::TensorShapeProto& shape(int index) const;
  ::opencv_tensorflow::TensorShapeProto* add_shape();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::TensorShapeProto >&
      shape() const;

  // repeated .opencv_tensorflow.TensorProto tensor = 8;
  int tensor_size() const;
  private:
  int _internal_tensor_size() const;
  public:
  void clear_tensor();
  ::opencv_tensorflow::TensorProto* mutable_tensor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::TensorProto >*
      mutable_tensor();
  private:
  const ::opencv_tensorflow::TensorProto& _internal_tensor(int index) const;
  ::opencv_tensorflow::TensorProto* _internal_add_tensor();
  public:
  const ::opencv_tensorflow::TensorProto& tensor(int index) const;
  ::opencv_tensorflow::TensorProto* add_tensor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::TensorProto >&
      tensor() const;

  // @@protoc_insertion_point(class_scope:opencv_tensorflow.AttrValue.ListValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> s_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > i_;
  mutable std::atomic<int> _i_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > f_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool > b_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int> type_;
  mutable std::atomic<int> _type_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::TensorShapeProto > shape_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::TensorProto > tensor_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_attr_5fvalue_2eproto;
};
// -------------------------------------------------------------------

class AttrValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_tensorflow.AttrValue) */ {
 public:
  inline AttrValue() : AttrValue(nullptr) {}
  ~AttrValue() override;
  explicit constexpr AttrValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AttrValue(const AttrValue& from);
  AttrValue(AttrValue&& from) noexcept
    : AttrValue() {
    *this = ::std::move(from);
  }

  inline AttrValue& operator=(const AttrValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline AttrValue& operator=(AttrValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AttrValue& default_instance() {
    return *internal_default_instance();
  }
  enum ValueCase {
    kS = 2,
    kI = 3,
    kF = 4,
    kB = 5,
    kType = 6,
    kShape = 7,
    kTensor = 8,
    kList = 1,
    kFunc = 10,
    kPlaceholder = 9,
    VALUE_NOT_SET = 0,
  };

  static inline const AttrValue* internal_default_instance() {
    return reinterpret_cast<const AttrValue*>(
               &_AttrValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(AttrValue& a, AttrValue& b) {
    a.Swap(&b);
  }
  inline void Swap(AttrValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AttrValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AttrValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AttrValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AttrValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AttrValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AttrValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_tensorflow.AttrValue";
  }
  protected:
  explicit AttrValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef AttrValue_ListValue ListValue;

  // accessors -------------------------------------------------------

  enum : int {
    kSFieldNumber = 2,
    kIFieldNumber = 3,
    kFFieldNumber = 4,
    kBFieldNumber = 5,
    kTypeFieldNumber = 6,
    kShapeFieldNumber = 7,
    kTensorFieldNumber = 8,
    kListFieldNumber = 1,
    kFuncFieldNumber = 10,
    kPlaceholderFieldNumber = 9,
  };
  // bytes s = 2;
  bool has_s() const;
  private:
  bool _internal_has_s() const;
  public:
  void clear_s();
  const std::string& s() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_s(ArgT0&& arg0, ArgT... args);
  std::string* mutable_s();
  PROTOBUF_NODISCARD std::string* release_s();
  void set_allocated_s(std::string* s);
  private:
  const std::string& _internal_s() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_s(const std::string& value);
  std::string* _internal_mutable_s();
  public:

  // int64 i = 3;
  bool has_i() const;
  private:
  bool _internal_has_i() const;
  public:
  void clear_i();
  int64_t i() const;
  void set_i(int64_t value);
  private:
  int64_t _internal_i() const;
  void _internal_set_i(int64_t value);
  public:

  // float f = 4;
  bool has_f() const;
  private:
  bool _internal_has_f() const;
  public:
  void clear_f();
  float f() const;
  void set_f(float value);
  private:
  float _internal_f() const;
  void _internal_set_f(float value);
  public:

  // bool b = 5;
  bool has_b() const;
  private:
  bool _internal_has_b() const;
  public:
  void clear_b();
  bool b() const;
  void set_b(bool value);
  private:
  bool _internal_b() const;
  void _internal_set_b(bool value);
  public:

  // .opencv_tensorflow.DataType type = 6;
  bool has_type() const;
  private:
  bool _internal_has_type() const;
  public:
  void clear_type();
  ::opencv_tensorflow::DataType type() const;
  void set_type(::opencv_tensorflow::DataType value);
  private:
  ::opencv_tensorflow::DataType _internal_type() const;
  void _internal_set_type(::opencv_tensorflow::DataType value);
  public:

  // .opencv_tensorflow.TensorShapeProto shape = 7;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::opencv_tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::opencv_tensorflow::TensorShapeProto* release_shape();
  ::opencv_tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::opencv_tensorflow::TensorShapeProto* shape);
  private:
  const ::opencv_tensorflow::TensorShapeProto& _internal_shape() const;
  ::opencv_tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::opencv_tensorflow::TensorShapeProto* shape);
  ::opencv_tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .opencv_tensorflow.TensorProto tensor = 8;
  bool has_tensor() const;
  private:
  bool _internal_has_tensor() const;
  public:
  void clear_tensor();
  const ::opencv_tensorflow::TensorProto& tensor() const;
  PROTOBUF_NODISCARD ::opencv_tensorflow::TensorProto* release_tensor();
  ::opencv_tensorflow::TensorProto* mutable_tensor();
  void set_allocated_tensor(::opencv_tensorflow::TensorProto* tensor);
  private:
  const ::opencv_tensorflow::TensorProto& _internal_tensor() const;
  ::opencv_tensorflow::TensorProto* _internal_mutable_tensor();
  public:
  void unsafe_arena_set_allocated_tensor(
      ::opencv_tensorflow::TensorProto* tensor);
  ::opencv_tensorflow::TensorProto* unsafe_arena_release_tensor();

  // .opencv_tensorflow.AttrValue.ListValue list = 1;
  bool has_list() const;
  private:
  bool _internal_has_list() const;
  public:
  void clear_list();
  const ::opencv_tensorflow::AttrValue_ListValue& list() const;
  PROTOBUF_NODISCARD ::opencv_tensorflow::AttrValue_ListValue* release_list();
  ::opencv_tensorflow::AttrValue_ListValue* mutable_list();
  void set_allocated_list(::opencv_tensorflow::AttrValue_ListValue* list);
  private:
  const ::opencv_tensorflow::AttrValue_ListValue& _internal_list() const;
  ::opencv_tensorflow::AttrValue_ListValue* _internal_mutable_list();
  public:
  void unsafe_arena_set_allocated_list(
      ::opencv_tensorflow::AttrValue_ListValue* list);
  ::opencv_tensorflow::AttrValue_ListValue* unsafe_arena_release_list();

  // .opencv_tensorflow.NameAttrList func = 10;
  bool has_func() const;
  private:
  bool _internal_has_func() const;
  public:
  void clear_func();
  const ::opencv_tensorflow::NameAttrList& func() const;
  PROTOBUF_NODISCARD ::opencv_tensorflow::NameAttrList* release_func();
  ::opencv_tensorflow::NameAttrList* mutable_func();
  void set_allocated_func(::opencv_tensorflow::NameAttrList* func);
  private:
  const ::opencv_tensorflow::NameAttrList& _internal_func() const;
  ::opencv_tensorflow::NameAttrList* _internal_mutable_func();
  public:
  void unsafe_arena_set_allocated_func(
      ::opencv_tensorflow::NameAttrList* func);
  ::opencv_tensorflow::NameAttrList* unsafe_arena_release_func();

  // string placeholder = 9;
  bool has_placeholder() const;
  private:
  bool _internal_has_placeholder() const;
  public:
  void clear_placeholder();
  const std::string& placeholder() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_placeholder(ArgT0&& arg0, ArgT... args);
  std::string* mutable_placeholder();
  PROTOBUF_NODISCARD std::string* release_placeholder();
  void set_allocated_placeholder(std::string* placeholder);
  private:
  const std::string& _internal_placeholder() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_placeholder(const std::string& value);
  std::string* _internal_mutable_placeholder();
  public:

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:opencv_tensorflow.AttrValue)
 private:
  class _Internal;
  void set_has_s();
  void set_has_i();
  void set_has_f();
  void set_has_b();
  void set_has_type();
  void set_has_shape();
  void set_has_tensor();
  void set_has_list();
  void set_has_func();
  void set_has_placeholder();

  inline bool has_value() const;
  inline void clear_has_value();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union ValueUnion {
    constexpr ValueUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr s_;
    int64_t i_;
    float f_;
    bool b_;
    int type_;
    ::opencv_tensorflow::TensorShapeProto* shape_;
    ::opencv_tensorflow::TensorProto* tensor_;
    ::opencv_tensorflow::AttrValue_ListValue* list_;
    ::opencv_tensorflow::NameAttrList* func_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr placeholder_;
  } value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_attr_5fvalue_2eproto;
};
// -------------------------------------------------------------------

class NameAttrList_AttrEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<NameAttrList_AttrEntry_DoNotUse,
    std::string, ::opencv_tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<NameAttrList_AttrEntry_DoNotUse,
    std::string, ::opencv_tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  NameAttrList_AttrEntry_DoNotUse();
  explicit constexpr NameAttrList_AttrEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit NameAttrList_AttrEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const NameAttrList_AttrEntry_DoNotUse& other);
  static const NameAttrList_AttrEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const NameAttrList_AttrEntry_DoNotUse*>(&_NameAttrList_AttrEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "opencv_tensorflow.NameAttrList.AttrEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class NameAttrList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_tensorflow.NameAttrList) */ {
 public:
  inline NameAttrList() : NameAttrList(nullptr) {}
  ~NameAttrList() override;
  explicit constexpr NameAttrList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NameAttrList(const NameAttrList& from);
  NameAttrList(NameAttrList&& from) noexcept
    : NameAttrList() {
    *this = ::std::move(from);
  }

  inline NameAttrList& operator=(const NameAttrList& from) {
    CopyFrom(from);
    return *this;
  }
  inline NameAttrList& operator=(NameAttrList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NameAttrList& default_instance() {
    return *internal_default_instance();
  }
  static inline const NameAttrList* internal_default_instance() {
    return reinterpret_cast<const NameAttrList*>(
               &_NameAttrList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(NameAttrList& a, NameAttrList& b) {
    a.Swap(&b);
  }
  inline void Swap(NameAttrList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NameAttrList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NameAttrList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NameAttrList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NameAttrList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const NameAttrList& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NameAttrList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_tensorflow.NameAttrList";
  }
  protected:
  explicit NameAttrList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kAttrFieldNumber = 2,
    kNameFieldNumber = 1,
  };
  // map<string, .opencv_tensorflow.AttrValue> attr = 2;
  int attr_size() const;
  private:
  int _internal_attr_size() const;
  public:
  void clear_attr();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::opencv_tensorflow::AttrValue >&
      _internal_attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::opencv_tensorflow::AttrValue >*
      _internal_mutable_attr();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::opencv_tensorflow::AttrValue >&
      attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::opencv_tensorflow::AttrValue >*
      mutable_attr();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:opencv_tensorflow.NameAttrList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      NameAttrList_AttrEntry_DoNotUse,
      std::string, ::opencv_tensorflow::AttrValue,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> attr_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_attr_5fvalue_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AttrValue_ListValue

// repeated bytes s = 2;
inline int AttrValue_ListValue::_internal_s_size() const {
  return s_.size();
}
inline int AttrValue_ListValue::s_size() const {
  return _internal_s_size();
}
inline void AttrValue_ListValue::clear_s() {
  s_.Clear();
}
inline std::string* AttrValue_ListValue::add_s() {
  std::string* _s = _internal_add_s();
  // @@protoc_insertion_point(field_add_mutable:opencv_tensorflow.AttrValue.ListValue.s)
  return _s;
}
inline const std::string& AttrValue_ListValue::_internal_s(int index) const {
  return s_.Get(index);
}
inline const std::string& AttrValue_ListValue::s(int index) const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.ListValue.s)
  return _internal_s(index);
}
inline std::string* AttrValue_ListValue::mutable_s(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.AttrValue.ListValue.s)
  return s_.Mutable(index);
}
inline void AttrValue_ListValue::set_s(int index, const std::string& value) {
  s_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:opencv_tensorflow.AttrValue.ListValue.s)
}
inline void AttrValue_ListValue::set_s(int index, std::string&& value) {
  s_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:opencv_tensorflow.AttrValue.ListValue.s)
}
inline void AttrValue_ListValue::set_s(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  s_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:opencv_tensorflow.AttrValue.ListValue.s)
}
inline void AttrValue_ListValue::set_s(int index, const void* value, size_t size) {
  s_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:opencv_tensorflow.AttrValue.ListValue.s)
}
inline std::string* AttrValue_ListValue::_internal_add_s() {
  return s_.Add();
}
inline void AttrValue_ListValue::add_s(const std::string& value) {
  s_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:opencv_tensorflow.AttrValue.ListValue.s)
}
inline void AttrValue_ListValue::add_s(std::string&& value) {
  s_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:opencv_tensorflow.AttrValue.ListValue.s)
}
inline void AttrValue_ListValue::add_s(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  s_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:opencv_tensorflow.AttrValue.ListValue.s)
}
inline void AttrValue_ListValue::add_s(const void* value, size_t size) {
  s_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:opencv_tensorflow.AttrValue.ListValue.s)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
AttrValue_ListValue::s() const {
  // @@protoc_insertion_point(field_list:opencv_tensorflow.AttrValue.ListValue.s)
  return s_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
AttrValue_ListValue::mutable_s() {
  // @@protoc_insertion_point(field_mutable_list:opencv_tensorflow.AttrValue.ListValue.s)
  return &s_;
}

// repeated int64 i = 3 [packed = true];
inline int AttrValue_ListValue::_internal_i_size() const {
  return i_.size();
}
inline int AttrValue_ListValue::i_size() const {
  return _internal_i_size();
}
inline void AttrValue_ListValue::clear_i() {
  i_.Clear();
}
inline int64_t AttrValue_ListValue::_internal_i(int index) const {
  return i_.Get(index);
}
inline int64_t AttrValue_ListValue::i(int index) const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.ListValue.i)
  return _internal_i(index);
}
inline void AttrValue_ListValue::set_i(int index, int64_t value) {
  i_.Set(index, value);
  // @@protoc_insertion_point(field_set:opencv_tensorflow.AttrValue.ListValue.i)
}
inline void AttrValue_ListValue::_internal_add_i(int64_t value) {
  i_.Add(value);
}
inline void AttrValue_ListValue::add_i(int64_t value) {
  _internal_add_i(value);
  // @@protoc_insertion_point(field_add:opencv_tensorflow.AttrValue.ListValue.i)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
AttrValue_ListValue::_internal_i() const {
  return i_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
AttrValue_ListValue::i() const {
  // @@protoc_insertion_point(field_list:opencv_tensorflow.AttrValue.ListValue.i)
  return _internal_i();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
AttrValue_ListValue::_internal_mutable_i() {
  return &i_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
AttrValue_ListValue::mutable_i() {
  // @@protoc_insertion_point(field_mutable_list:opencv_tensorflow.AttrValue.ListValue.i)
  return _internal_mutable_i();
}

// repeated float f = 4 [packed = true];
inline int AttrValue_ListValue::_internal_f_size() const {
  return f_.size();
}
inline int AttrValue_ListValue::f_size() const {
  return _internal_f_size();
}
inline void AttrValue_ListValue::clear_f() {
  f_.Clear();
}
inline float AttrValue_ListValue::_internal_f(int index) const {
  return f_.Get(index);
}
inline float AttrValue_ListValue::f(int index) const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.ListValue.f)
  return _internal_f(index);
}
inline void AttrValue_ListValue::set_f(int index, float value) {
  f_.Set(index, value);
  // @@protoc_insertion_point(field_set:opencv_tensorflow.AttrValue.ListValue.f)
}
inline void AttrValue_ListValue::_internal_add_f(float value) {
  f_.Add(value);
}
inline void AttrValue_ListValue::add_f(float value) {
  _internal_add_f(value);
  // @@protoc_insertion_point(field_add:opencv_tensorflow.AttrValue.ListValue.f)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
AttrValue_ListValue::_internal_f() const {
  return f_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
AttrValue_ListValue::f() const {
  // @@protoc_insertion_point(field_list:opencv_tensorflow.AttrValue.ListValue.f)
  return _internal_f();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
AttrValue_ListValue::_internal_mutable_f() {
  return &f_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
AttrValue_ListValue::mutable_f() {
  // @@protoc_insertion_point(field_mutable_list:opencv_tensorflow.AttrValue.ListValue.f)
  return _internal_mutable_f();
}

// repeated bool b = 5 [packed = true];
inline int AttrValue_ListValue::_internal_b_size() const {
  return b_.size();
}
inline int AttrValue_ListValue::b_size() const {
  return _internal_b_size();
}
inline void AttrValue_ListValue::clear_b() {
  b_.Clear();
}
inline bool AttrValue_ListValue::_internal_b(int index) const {
  return b_.Get(index);
}
inline bool AttrValue_ListValue::b(int index) const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.ListValue.b)
  return _internal_b(index);
}
inline void AttrValue_ListValue::set_b(int index, bool value) {
  b_.Set(index, value);
  // @@protoc_insertion_point(field_set:opencv_tensorflow.AttrValue.ListValue.b)
}
inline void AttrValue_ListValue::_internal_add_b(bool value) {
  b_.Add(value);
}
inline void AttrValue_ListValue::add_b(bool value) {
  _internal_add_b(value);
  // @@protoc_insertion_point(field_add:opencv_tensorflow.AttrValue.ListValue.b)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
AttrValue_ListValue::_internal_b() const {
  return b_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
AttrValue_ListValue::b() const {
  // @@protoc_insertion_point(field_list:opencv_tensorflow.AttrValue.ListValue.b)
  return _internal_b();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
AttrValue_ListValue::_internal_mutable_b() {
  return &b_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
AttrValue_ListValue::mutable_b() {
  // @@protoc_insertion_point(field_mutable_list:opencv_tensorflow.AttrValue.ListValue.b)
  return _internal_mutable_b();
}

// repeated .opencv_tensorflow.DataType type = 6 [packed = true];
inline int AttrValue_ListValue::_internal_type_size() const {
  return type_.size();
}
inline int AttrValue_ListValue::type_size() const {
  return _internal_type_size();
}
inline void AttrValue_ListValue::clear_type() {
  type_.Clear();
}
inline ::opencv_tensorflow::DataType AttrValue_ListValue::_internal_type(int index) const {
  return static_cast< ::opencv_tensorflow::DataType >(type_.Get(index));
}
inline ::opencv_tensorflow::DataType AttrValue_ListValue::type(int index) const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.ListValue.type)
  return _internal_type(index);
}
inline void AttrValue_ListValue::set_type(int index, ::opencv_tensorflow::DataType value) {
  type_.Set(index, value);
  // @@protoc_insertion_point(field_set:opencv_tensorflow.AttrValue.ListValue.type)
}
inline void AttrValue_ListValue::_internal_add_type(::opencv_tensorflow::DataType value) {
  type_.Add(value);
}
inline void AttrValue_ListValue::add_type(::opencv_tensorflow::DataType value) {
  _internal_add_type(value);
  // @@protoc_insertion_point(field_add:opencv_tensorflow.AttrValue.ListValue.type)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>&
AttrValue_ListValue::type() const {
  // @@protoc_insertion_point(field_list:opencv_tensorflow.AttrValue.ListValue.type)
  return type_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
AttrValue_ListValue::_internal_mutable_type() {
  return &type_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
AttrValue_ListValue::mutable_type() {
  // @@protoc_insertion_point(field_mutable_list:opencv_tensorflow.AttrValue.ListValue.type)
  return _internal_mutable_type();
}

// repeated .opencv_tensorflow.TensorShapeProto shape = 7;
inline int AttrValue_ListValue::_internal_shape_size() const {
  return shape_.size();
}
inline int AttrValue_ListValue::shape_size() const {
  return _internal_shape_size();
}
inline ::opencv_tensorflow::TensorShapeProto* AttrValue_ListValue::mutable_shape(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.AttrValue.ListValue.shape)
  return shape_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::TensorShapeProto >*
AttrValue_ListValue::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:opencv_tensorflow.AttrValue.ListValue.shape)
  return &shape_;
}
inline const ::opencv_tensorflow::TensorShapeProto& AttrValue_ListValue::_internal_shape(int index) const {
  return shape_.Get(index);
}
inline const ::opencv_tensorflow::TensorShapeProto& AttrValue_ListValue::shape(int index) const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.ListValue.shape)
  return _internal_shape(index);
}
inline ::opencv_tensorflow::TensorShapeProto* AttrValue_ListValue::_internal_add_shape() {
  return shape_.Add();
}
inline ::opencv_tensorflow::TensorShapeProto* AttrValue_ListValue::add_shape() {
  ::opencv_tensorflow::TensorShapeProto* _add = _internal_add_shape();
  // @@protoc_insertion_point(field_add:opencv_tensorflow.AttrValue.ListValue.shape)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::TensorShapeProto >&
AttrValue_ListValue::shape() const {
  // @@protoc_insertion_point(field_list:opencv_tensorflow.AttrValue.ListValue.shape)
  return shape_;
}

// repeated .opencv_tensorflow.TensorProto tensor = 8;
inline int AttrValue_ListValue::_internal_tensor_size() const {
  return tensor_.size();
}
inline int AttrValue_ListValue::tensor_size() const {
  return _internal_tensor_size();
}
inline ::opencv_tensorflow::TensorProto* AttrValue_ListValue::mutable_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.AttrValue.ListValue.tensor)
  return tensor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::TensorProto >*
AttrValue_ListValue::mutable_tensor() {
  // @@protoc_insertion_point(field_mutable_list:opencv_tensorflow.AttrValue.ListValue.tensor)
  return &tensor_;
}
inline const ::opencv_tensorflow::TensorProto& AttrValue_ListValue::_internal_tensor(int index) const {
  return tensor_.Get(index);
}
inline const ::opencv_tensorflow::TensorProto& AttrValue_ListValue::tensor(int index) const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.ListValue.tensor)
  return _internal_tensor(index);
}
inline ::opencv_tensorflow::TensorProto* AttrValue_ListValue::_internal_add_tensor() {
  return tensor_.Add();
}
inline ::opencv_tensorflow::TensorProto* AttrValue_ListValue::add_tensor() {
  ::opencv_tensorflow::TensorProto* _add = _internal_add_tensor();
  // @@protoc_insertion_point(field_add:opencv_tensorflow.AttrValue.ListValue.tensor)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::TensorProto >&
AttrValue_ListValue::tensor() const {
  // @@protoc_insertion_point(field_list:opencv_tensorflow.AttrValue.ListValue.tensor)
  return tensor_;
}

// -------------------------------------------------------------------

// AttrValue

// bytes s = 2;
inline bool AttrValue::_internal_has_s() const {
  return value_case() == kS;
}
inline bool AttrValue::has_s() const {
  return _internal_has_s();
}
inline void AttrValue::set_has_s() {
  _oneof_case_[0] = kS;
}
inline void AttrValue::clear_s() {
  if (_internal_has_s()) {
    value_.s_.Destroy(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
    clear_has_value();
  }
}
inline const std::string& AttrValue::s() const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.s)
  return _internal_s();
}
template <typename ArgT0, typename... ArgT>
inline void AttrValue::set_s(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_s()) {
    clear_value();
    set_has_s();
    value_.s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.s_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_tensorflow.AttrValue.s)
}
inline std::string* AttrValue::mutable_s() {
  std::string* _s = _internal_mutable_s();
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.AttrValue.s)
  return _s;
}
inline const std::string& AttrValue::_internal_s() const {
  if (_internal_has_s()) {
    return value_.s_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void AttrValue::_internal_set_s(const std::string& value) {
  if (!_internal_has_s()) {
    clear_value();
    set_has_s();
    value_.s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.s_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AttrValue::_internal_mutable_s() {
  if (!_internal_has_s()) {
    clear_value();
    set_has_s();
    value_.s_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return value_.s_.Mutable(
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AttrValue::release_s() {
  // @@protoc_insertion_point(field_release:opencv_tensorflow.AttrValue.s)
  if (_internal_has_s()) {
    clear_has_value();
    return value_.s_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
  } else {
    return nullptr;
  }
}
inline void AttrValue::set_allocated_s(std::string* s) {
  if (has_value()) {
    clear_value();
  }
  if (s != nullptr) {
    set_has_s();
    value_.s_.UnsafeSetDefault(s);
    ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaForAllocation();
    if (arena != nullptr) {
      arena->Own(s);
    }
  }
  // @@protoc_insertion_point(field_set_allocated:opencv_tensorflow.AttrValue.s)
}

// int64 i = 3;
inline bool AttrValue::_internal_has_i() const {
  return value_case() == kI;
}
inline bool AttrValue::has_i() const {
  return _internal_has_i();
}
inline void AttrValue::set_has_i() {
  _oneof_case_[0] = kI;
}
inline void AttrValue::clear_i() {
  if (_internal_has_i()) {
    value_.i_ = int64_t{0};
    clear_has_value();
  }
}
inline int64_t AttrValue::_internal_i() const {
  if (_internal_has_i()) {
    return value_.i_;
  }
  return int64_t{0};
}
inline void AttrValue::_internal_set_i(int64_t value) {
  if (!_internal_has_i()) {
    clear_value();
    set_has_i();
  }
  value_.i_ = value;
}
inline int64_t AttrValue::i() const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.i)
  return _internal_i();
}
inline void AttrValue::set_i(int64_t value) {
  _internal_set_i(value);
  // @@protoc_insertion_point(field_set:opencv_tensorflow.AttrValue.i)
}

// float f = 4;
inline bool AttrValue::_internal_has_f() const {
  return value_case() == kF;
}
inline bool AttrValue::has_f() const {
  return _internal_has_f();
}
inline void AttrValue::set_has_f() {
  _oneof_case_[0] = kF;
}
inline void AttrValue::clear_f() {
  if (_internal_has_f()) {
    value_.f_ = 0;
    clear_has_value();
  }
}
inline float AttrValue::_internal_f() const {
  if (_internal_has_f()) {
    return value_.f_;
  }
  return 0;
}
inline void AttrValue::_internal_set_f(float value) {
  if (!_internal_has_f()) {
    clear_value();
    set_has_f();
  }
  value_.f_ = value;
}
inline float AttrValue::f() const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.f)
  return _internal_f();
}
inline void AttrValue::set_f(float value) {
  _internal_set_f(value);
  // @@protoc_insertion_point(field_set:opencv_tensorflow.AttrValue.f)
}

// bool b = 5;
inline bool AttrValue::_internal_has_b() const {
  return value_case() == kB;
}
inline bool AttrValue::has_b() const {
  return _internal_has_b();
}
inline void AttrValue::set_has_b() {
  _oneof_case_[0] = kB;
}
inline void AttrValue::clear_b() {
  if (_internal_has_b()) {
    value_.b_ = false;
    clear_has_value();
  }
}
inline bool AttrValue::_internal_b() const {
  if (_internal_has_b()) {
    return value_.b_;
  }
  return false;
}
inline void AttrValue::_internal_set_b(bool value) {
  if (!_internal_has_b()) {
    clear_value();
    set_has_b();
  }
  value_.b_ = value;
}
inline bool AttrValue::b() const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.b)
  return _internal_b();
}
inline void AttrValue::set_b(bool value) {
  _internal_set_b(value);
  // @@protoc_insertion_point(field_set:opencv_tensorflow.AttrValue.b)
}

// .opencv_tensorflow.DataType type = 6;
inline bool AttrValue::_internal_has_type() const {
  return value_case() == kType;
}
inline bool AttrValue::has_type() const {
  return _internal_has_type();
}
inline void AttrValue::set_has_type() {
  _oneof_case_[0] = kType;
}
inline void AttrValue::clear_type() {
  if (_internal_has_type()) {
    value_.type_ = 0;
    clear_has_value();
  }
}
inline ::opencv_tensorflow::DataType AttrValue::_internal_type() const {
  if (_internal_has_type()) {
    return static_cast< ::opencv_tensorflow::DataType >(value_.type_);
  }
  return static_cast< ::opencv_tensorflow::DataType >(0);
}
inline ::opencv_tensorflow::DataType AttrValue::type() const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.type)
  return _internal_type();
}
inline void AttrValue::_internal_set_type(::opencv_tensorflow::DataType value) {
  if (!_internal_has_type()) {
    clear_value();
    set_has_type();
  }
  value_.type_ = value;
}
inline void AttrValue::set_type(::opencv_tensorflow::DataType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:opencv_tensorflow.AttrValue.type)
}

// .opencv_tensorflow.TensorShapeProto shape = 7;
inline bool AttrValue::_internal_has_shape() const {
  return value_case() == kShape;
}
inline bool AttrValue::has_shape() const {
  return _internal_has_shape();
}
inline void AttrValue::set_has_shape() {
  _oneof_case_[0] = kShape;
}
inline ::opencv_tensorflow::TensorShapeProto* AttrValue::release_shape() {
  // @@protoc_insertion_point(field_release:opencv_tensorflow.AttrValue.shape)
  if (_internal_has_shape()) {
    clear_has_value();
      ::opencv_tensorflow::TensorShapeProto* temp = value_.shape_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.shape_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::opencv_tensorflow::TensorShapeProto& AttrValue::_internal_shape() const {
  return _internal_has_shape()
      ? *value_.shape_
      : reinterpret_cast< ::opencv_tensorflow::TensorShapeProto&>(::opencv_tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::opencv_tensorflow::TensorShapeProto& AttrValue::shape() const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.shape)
  return _internal_shape();
}
inline ::opencv_tensorflow::TensorShapeProto* AttrValue::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:opencv_tensorflow.AttrValue.shape)
  if (_internal_has_shape()) {
    clear_has_value();
    ::opencv_tensorflow::TensorShapeProto* temp = value_.shape_;
    value_.shape_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AttrValue::unsafe_arena_set_allocated_shape(::opencv_tensorflow::TensorShapeProto* shape) {
  clear_value();
  if (shape) {
    set_has_shape();
    value_.shape_ = shape;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:opencv_tensorflow.AttrValue.shape)
}
inline ::opencv_tensorflow::TensorShapeProto* AttrValue::_internal_mutable_shape() {
  if (!_internal_has_shape()) {
    clear_value();
    set_has_shape();
    value_.shape_ = CreateMaybeMessage< ::opencv_tensorflow::TensorShapeProto >(GetArenaForAllocation());
  }
  return value_.shape_;
}
inline ::opencv_tensorflow::TensorShapeProto* AttrValue::mutable_shape() {
  ::opencv_tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.AttrValue.shape)
  return _msg;
}

// .opencv_tensorflow.TensorProto tensor = 8;
inline bool AttrValue::_internal_has_tensor() const {
  return value_case() == kTensor;
}
inline bool AttrValue::has_tensor() const {
  return _internal_has_tensor();
}
inline void AttrValue::set_has_tensor() {
  _oneof_case_[0] = kTensor;
}
inline ::opencv_tensorflow::TensorProto* AttrValue::release_tensor() {
  // @@protoc_insertion_point(field_release:opencv_tensorflow.AttrValue.tensor)
  if (_internal_has_tensor()) {
    clear_has_value();
      ::opencv_tensorflow::TensorProto* temp = value_.tensor_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::opencv_tensorflow::TensorProto& AttrValue::_internal_tensor() const {
  return _internal_has_tensor()
      ? *value_.tensor_
      : reinterpret_cast< ::opencv_tensorflow::TensorProto&>(::opencv_tensorflow::_TensorProto_default_instance_);
}
inline const ::opencv_tensorflow::TensorProto& AttrValue::tensor() const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.tensor)
  return _internal_tensor();
}
inline ::opencv_tensorflow::TensorProto* AttrValue::unsafe_arena_release_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:opencv_tensorflow.AttrValue.tensor)
  if (_internal_has_tensor()) {
    clear_has_value();
    ::opencv_tensorflow::TensorProto* temp = value_.tensor_;
    value_.tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AttrValue::unsafe_arena_set_allocated_tensor(::opencv_tensorflow::TensorProto* tensor) {
  clear_value();
  if (tensor) {
    set_has_tensor();
    value_.tensor_ = tensor;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:opencv_tensorflow.AttrValue.tensor)
}
inline ::opencv_tensorflow::TensorProto* AttrValue::_internal_mutable_tensor() {
  if (!_internal_has_tensor()) {
    clear_value();
    set_has_tensor();
    value_.tensor_ = CreateMaybeMessage< ::opencv_tensorflow::TensorProto >(GetArenaForAllocation());
  }
  return value_.tensor_;
}
inline ::opencv_tensorflow::TensorProto* AttrValue::mutable_tensor() {
  ::opencv_tensorflow::TensorProto* _msg = _internal_mutable_tensor();
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.AttrValue.tensor)
  return _msg;
}

// .opencv_tensorflow.AttrValue.ListValue list = 1;
inline bool AttrValue::_internal_has_list() const {
  return value_case() == kList;
}
inline bool AttrValue::has_list() const {
  return _internal_has_list();
}
inline void AttrValue::set_has_list() {
  _oneof_case_[0] = kList;
}
inline void AttrValue::clear_list() {
  if (_internal_has_list()) {
    if (GetArenaForAllocation() == nullptr) {
      delete value_.list_;
    }
    clear_has_value();
  }
}
inline ::opencv_tensorflow::AttrValue_ListValue* AttrValue::release_list() {
  // @@protoc_insertion_point(field_release:opencv_tensorflow.AttrValue.list)
  if (_internal_has_list()) {
    clear_has_value();
      ::opencv_tensorflow::AttrValue_ListValue* temp = value_.list_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::opencv_tensorflow::AttrValue_ListValue& AttrValue::_internal_list() const {
  return _internal_has_list()
      ? *value_.list_
      : reinterpret_cast< ::opencv_tensorflow::AttrValue_ListValue&>(::opencv_tensorflow::_AttrValue_ListValue_default_instance_);
}
inline const ::opencv_tensorflow::AttrValue_ListValue& AttrValue::list() const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.list)
  return _internal_list();
}
inline ::opencv_tensorflow::AttrValue_ListValue* AttrValue::unsafe_arena_release_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:opencv_tensorflow.AttrValue.list)
  if (_internal_has_list()) {
    clear_has_value();
    ::opencv_tensorflow::AttrValue_ListValue* temp = value_.list_;
    value_.list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AttrValue::unsafe_arena_set_allocated_list(::opencv_tensorflow::AttrValue_ListValue* list) {
  clear_value();
  if (list) {
    set_has_list();
    value_.list_ = list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:opencv_tensorflow.AttrValue.list)
}
inline ::opencv_tensorflow::AttrValue_ListValue* AttrValue::_internal_mutable_list() {
  if (!_internal_has_list()) {
    clear_value();
    set_has_list();
    value_.list_ = CreateMaybeMessage< ::opencv_tensorflow::AttrValue_ListValue >(GetArenaForAllocation());
  }
  return value_.list_;
}
inline ::opencv_tensorflow::AttrValue_ListValue* AttrValue::mutable_list() {
  ::opencv_tensorflow::AttrValue_ListValue* _msg = _internal_mutable_list();
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.AttrValue.list)
  return _msg;
}

// .opencv_tensorflow.NameAttrList func = 10;
inline bool AttrValue::_internal_has_func() const {
  return value_case() == kFunc;
}
inline bool AttrValue::has_func() const {
  return _internal_has_func();
}
inline void AttrValue::set_has_func() {
  _oneof_case_[0] = kFunc;
}
inline void AttrValue::clear_func() {
  if (_internal_has_func()) {
    if (GetArenaForAllocation() == nullptr) {
      delete value_.func_;
    }
    clear_has_value();
  }
}
inline ::opencv_tensorflow::NameAttrList* AttrValue::release_func() {
  // @@protoc_insertion_point(field_release:opencv_tensorflow.AttrValue.func)
  if (_internal_has_func()) {
    clear_has_value();
      ::opencv_tensorflow::NameAttrList* temp = value_.func_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.func_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::opencv_tensorflow::NameAttrList& AttrValue::_internal_func() const {
  return _internal_has_func()
      ? *value_.func_
      : reinterpret_cast< ::opencv_tensorflow::NameAttrList&>(::opencv_tensorflow::_NameAttrList_default_instance_);
}
inline const ::opencv_tensorflow::NameAttrList& AttrValue::func() const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.func)
  return _internal_func();
}
inline ::opencv_tensorflow::NameAttrList* AttrValue::unsafe_arena_release_func() {
  // @@protoc_insertion_point(field_unsafe_arena_release:opencv_tensorflow.AttrValue.func)
  if (_internal_has_func()) {
    clear_has_value();
    ::opencv_tensorflow::NameAttrList* temp = value_.func_;
    value_.func_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AttrValue::unsafe_arena_set_allocated_func(::opencv_tensorflow::NameAttrList* func) {
  clear_value();
  if (func) {
    set_has_func();
    value_.func_ = func;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:opencv_tensorflow.AttrValue.func)
}
inline ::opencv_tensorflow::NameAttrList* AttrValue::_internal_mutable_func() {
  if (!_internal_has_func()) {
    clear_value();
    set_has_func();
    value_.func_ = CreateMaybeMessage< ::opencv_tensorflow::NameAttrList >(GetArenaForAllocation());
  }
  return value_.func_;
}
inline ::opencv_tensorflow::NameAttrList* AttrValue::mutable_func() {
  ::opencv_tensorflow::NameAttrList* _msg = _internal_mutable_func();
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.AttrValue.func)
  return _msg;
}

// string placeholder = 9;
inline bool AttrValue::_internal_has_placeholder() const {
  return value_case() == kPlaceholder;
}
inline bool AttrValue::has_placeholder() const {
  return _internal_has_placeholder();
}
inline void AttrValue::set_has_placeholder() {
  _oneof_case_[0] = kPlaceholder;
}
inline void AttrValue::clear_placeholder() {
  if (_internal_has_placeholder()) {
    value_.placeholder_.Destroy(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
    clear_has_value();
  }
}
inline const std::string& AttrValue::placeholder() const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.AttrValue.placeholder)
  return _internal_placeholder();
}
template <typename ArgT0, typename... ArgT>
inline void AttrValue::set_placeholder(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_placeholder()) {
    clear_value();
    set_has_placeholder();
    value_.placeholder_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.placeholder_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_tensorflow.AttrValue.placeholder)
}
inline std::string* AttrValue::mutable_placeholder() {
  std::string* _s = _internal_mutable_placeholder();
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.AttrValue.placeholder)
  return _s;
}
inline const std::string& AttrValue::_internal_placeholder() const {
  if (_internal_has_placeholder()) {
    return value_.placeholder_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void AttrValue::_internal_set_placeholder(const std::string& value) {
  if (!_internal_has_placeholder()) {
    clear_value();
    set_has_placeholder();
    value_.placeholder_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.placeholder_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AttrValue::_internal_mutable_placeholder() {
  if (!_internal_has_placeholder()) {
    clear_value();
    set_has_placeholder();
    value_.placeholder_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return value_.placeholder_.Mutable(
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AttrValue::release_placeholder() {
  // @@protoc_insertion_point(field_release:opencv_tensorflow.AttrValue.placeholder)
  if (_internal_has_placeholder()) {
    clear_has_value();
    return value_.placeholder_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
  } else {
    return nullptr;
  }
}
inline void AttrValue::set_allocated_placeholder(std::string* placeholder) {
  if (has_value()) {
    clear_value();
  }
  if (placeholder != nullptr) {
    set_has_placeholder();
    value_.placeholder_.UnsafeSetDefault(placeholder);
    ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaForAllocation();
    if (arena != nullptr) {
      arena->Own(placeholder);
    }
  }
  // @@protoc_insertion_point(field_set_allocated:opencv_tensorflow.AttrValue.placeholder)
}

inline bool AttrValue::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void AttrValue::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline AttrValue::ValueCase AttrValue::value_case() const {
  return AttrValue::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// NameAttrList

// string name = 1;
inline void NameAttrList::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& NameAttrList::name() const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.NameAttrList.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NameAttrList::set_name(ArgT0&& arg0, ArgT... args) {

 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_tensorflow.NameAttrList.name)
}
inline std::string* NameAttrList::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.NameAttrList.name)
  return _s;
}
inline const std::string& NameAttrList::_internal_name() const {
  return name_.Get();
}
inline void NameAttrList::_internal_set_name(const std::string& value) {

  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* NameAttrList::_internal_mutable_name() {

  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* NameAttrList::release_name() {
  // @@protoc_insertion_point(field_release:opencv_tensorflow.NameAttrList.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void NameAttrList::set_allocated_name(std::string* name) {
  if (name != nullptr) {

  } else {

  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_tensorflow.NameAttrList.name)
}

// map<string, .opencv_tensorflow.AttrValue> attr = 2;
inline int NameAttrList::_internal_attr_size() const {
  return attr_.size();
}
inline int NameAttrList::attr_size() const {
  return _internal_attr_size();
}
inline void NameAttrList::clear_attr() {
  attr_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::opencv_tensorflow::AttrValue >&
NameAttrList::_internal_attr() const {
  return attr_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::opencv_tensorflow::AttrValue >&
NameAttrList::attr() const {
  // @@protoc_insertion_point(field_map:opencv_tensorflow.NameAttrList.attr)
  return _internal_attr();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::opencv_tensorflow::AttrValue >*
NameAttrList::_internal_mutable_attr() {
  return attr_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::opencv_tensorflow::AttrValue >*
NameAttrList::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_map:opencv_tensorflow.NameAttrList.attr)
  return _internal_mutable_attr();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace opencv_tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_attr_5fvalue_2eproto
