﻿  iw_core.c
  iw_image.c
  iw_image_color_convert_all.c
  iw_image_color_convert_rgbs.c
  iw_image_filter_bilateral.c
  iw_image_filter_box.c
  iw_image_filter_canny.c
  iw_image_filter_gaussian.c
  iw_image_filter_general.c
  iw_image_filter_laplacian.c
  iw_image_filter_morphology.c
  iw_image_filter_scharr.c
  iw_image_filter_sobel.c
  iw_image_op_copy.c
  iw_image_op_copy_channel.c
  iw_image_op_copy_make_border.c
  iw_image_op_copy_merge.c
  iw_image_op_copy_split.c
  iw_image_op_scale.c
  iw_image_op_set.c
  iw_image_op_set_channel.c
  iw_image_op_swap_channels.c
  iw_image_transform_mirror.c
  iw_image_transform_resize.c
  iw_image_transform_rotate.c
  iw_image_transform_warpaffine.c
  iw_own.c
  ippiw.vcxproj -> D:\AI\opencv\cudabuild\3rdparty\lib\Release\ippiw.lib
