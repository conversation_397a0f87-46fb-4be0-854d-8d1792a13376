PORTED FUNCs LIST (4 of 4):

  cv::wechat_qrcode::WeChatQRCode::WeChatQRCode(string detector_prototxt_path = "", string detector_caffe_model_path = "", string super_resolution_prototxt_path = "", string super_resolution_caffe_model_path = "")
 vector_string cv::wechat_qrcode::WeChatQRCode::detectAndDecode(Mat img, vector_Mat& points = vector_Mat())
 void cv::wechat_qrcode::WeChatQRCode::setScaleFactor(float _scalingFactor)
 float cv::wechat_qrcode::WeChatQRCode::getScaleFactor()

SKIPPED FUNCs LIST (0 of 4):


0 def args - 2 funcs
1 def args - 1 funcs
4 def args - 1 funcs