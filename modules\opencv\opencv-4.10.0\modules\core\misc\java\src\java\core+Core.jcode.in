    // these constants are wrapped inside functions to prevent inlining
    private static String getVersion() { return "@OPENCV_VERSION@"; }
    private static String getNativeLibraryName() { return "opencv_java@OPENCV_VERSION_MAJOR@@OPENCV_VERSION_MINOR@@OPENCV_VERSION_PATCH@"; }
    private static int getVersionMajorJ() { return @OPENCV_VERSION_MAJOR@; }
    private static int getVersionMinorJ() { return @OPENCV_VERSION_MINOR@; }
    private static int getVersionRevisionJ() { return @OPENCV_VERSION_PATCH@; }
    private static String getVersionStatusJ() { return "@OPENCV_VERSION_STATUS@"; }

    public static final String VERSION = getVersion();
    public static final String NATIVE_LIBRARY_NAME = getNativeLibraryName();
    public static final int VERSION_MAJOR = getVersionMajorJ();
    public static final int VERSION_MINOR = getVersionMinorJ();
    public static final int VERSION_REVISION = getVersionRevisionJ();
    public static final String VERSION_STATUS = getVersionStatusJ();
