<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Modifier key flags</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle"><div class="title">Modifier key flags<div class="ingroups"><a class="el" href="group__input.html">Input reference</a></div></div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>See <a class="el" href="input_guide.html#input_key">key input</a> for how these are used. </p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:ga14994d3196c290aaa347248e51740274" id="r_ga14994d3196c290aaa347248e51740274"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__mods.html#ga14994d3196c290aaa347248e51740274">GLFW_MOD_SHIFT</a>&#160;&#160;&#160;0x0001</td></tr>
<tr class="memdesc:ga14994d3196c290aaa347248e51740274"><td class="mdescLeft">&#160;</td><td class="mdescRight">If this bit is set one or more Shift keys were held down.  <br /></td></tr>
<tr class="separator:ga14994d3196c290aaa347248e51740274"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6ed94871c3208eefd85713fa929d45aa" id="r_ga6ed94871c3208eefd85713fa929d45aa"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__mods.html#ga6ed94871c3208eefd85713fa929d45aa">GLFW_MOD_CONTROL</a>&#160;&#160;&#160;0x0002</td></tr>
<tr class="memdesc:ga6ed94871c3208eefd85713fa929d45aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">If this bit is set one or more Control keys were held down.  <br /></td></tr>
<tr class="separator:ga6ed94871c3208eefd85713fa929d45aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad2acd5633463c29e07008687ea73c0f4" id="r_gad2acd5633463c29e07008687ea73c0f4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__mods.html#gad2acd5633463c29e07008687ea73c0f4">GLFW_MOD_ALT</a>&#160;&#160;&#160;0x0004</td></tr>
<tr class="memdesc:gad2acd5633463c29e07008687ea73c0f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">If this bit is set one or more Alt keys were held down.  <br /></td></tr>
<tr class="separator:gad2acd5633463c29e07008687ea73c0f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6b64ba10ea0227cf6f42efd0a220aba1" id="r_ga6b64ba10ea0227cf6f42efd0a220aba1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__mods.html#ga6b64ba10ea0227cf6f42efd0a220aba1">GLFW_MOD_SUPER</a>&#160;&#160;&#160;0x0008</td></tr>
<tr class="memdesc:ga6b64ba10ea0227cf6f42efd0a220aba1"><td class="mdescLeft">&#160;</td><td class="mdescRight">If this bit is set one or more Super keys were held down.  <br /></td></tr>
<tr class="separator:ga6b64ba10ea0227cf6f42efd0a220aba1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaefeef8fcf825a6e43e241b337897200f" id="r_gaefeef8fcf825a6e43e241b337897200f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__mods.html#gaefeef8fcf825a6e43e241b337897200f">GLFW_MOD_CAPS_LOCK</a>&#160;&#160;&#160;0x0010</td></tr>
<tr class="memdesc:gaefeef8fcf825a6e43e241b337897200f"><td class="mdescLeft">&#160;</td><td class="mdescRight">If this bit is set the Caps Lock key is enabled.  <br /></td></tr>
<tr class="separator:gaefeef8fcf825a6e43e241b337897200f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga64e020b8a42af8376e944baf61feecbe" id="r_ga64e020b8a42af8376e944baf61feecbe"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__mods.html#ga64e020b8a42af8376e944baf61feecbe">GLFW_MOD_NUM_LOCK</a>&#160;&#160;&#160;0x0020</td></tr>
<tr class="memdesc:ga64e020b8a42af8376e944baf61feecbe"><td class="mdescLeft">&#160;</td><td class="mdescRight">If this bit is set the Num Lock key is enabled.  <br /></td></tr>
<tr class="separator:ga64e020b8a42af8376e944baf61feecbe"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="ga14994d3196c290aaa347248e51740274" name="ga14994d3196c290aaa347248e51740274"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga14994d3196c290aaa347248e51740274">&#9670;&#160;</a></span>GLFW_MOD_SHIFT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOD_SHIFT&#160;&#160;&#160;0x0001</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If this bit is set one or more Shift keys were held down. </p>

</div>
</div>
<a id="ga6ed94871c3208eefd85713fa929d45aa" name="ga6ed94871c3208eefd85713fa929d45aa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6ed94871c3208eefd85713fa929d45aa">&#9670;&#160;</a></span>GLFW_MOD_CONTROL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOD_CONTROL&#160;&#160;&#160;0x0002</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If this bit is set one or more Control keys were held down. </p>

</div>
</div>
<a id="gad2acd5633463c29e07008687ea73c0f4" name="gad2acd5633463c29e07008687ea73c0f4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad2acd5633463c29e07008687ea73c0f4">&#9670;&#160;</a></span>GLFW_MOD_ALT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOD_ALT&#160;&#160;&#160;0x0004</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If this bit is set one or more Alt keys were held down. </p>

</div>
</div>
<a id="ga6b64ba10ea0227cf6f42efd0a220aba1" name="ga6b64ba10ea0227cf6f42efd0a220aba1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6b64ba10ea0227cf6f42efd0a220aba1">&#9670;&#160;</a></span>GLFW_MOD_SUPER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOD_SUPER&#160;&#160;&#160;0x0008</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If this bit is set one or more Super keys were held down. </p>

</div>
</div>
<a id="gaefeef8fcf825a6e43e241b337897200f" name="gaefeef8fcf825a6e43e241b337897200f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaefeef8fcf825a6e43e241b337897200f">&#9670;&#160;</a></span>GLFW_MOD_CAPS_LOCK</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOD_CAPS_LOCK&#160;&#160;&#160;0x0010</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If this bit is set the Caps Lock key is enabled and the <a class="el" href="input_guide.html#GLFW_LOCK_KEY_MODS">GLFW_LOCK_KEY_MODS</a> input mode is set. </p>

</div>
</div>
<a id="ga64e020b8a42af8376e944baf61feecbe" name="ga64e020b8a42af8376e944baf61feecbe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga64e020b8a42af8376e944baf61feecbe">&#9670;&#160;</a></span>GLFW_MOD_NUM_LOCK</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOD_NUM_LOCK&#160;&#160;&#160;0x0020</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If this bit is set the Num Lock key is enabled and the <a class="el" href="input_guide.html#GLFW_LOCK_KEY_MODS">GLFW_LOCK_KEY_MODS</a> input mode is set. </p>

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
