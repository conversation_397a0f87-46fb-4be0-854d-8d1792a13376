<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Vulkan support reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">Vulkan support reference</div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>This is the reference documentation for Vulkan related functions and types. For more task-oriented information, see the <a class="el" href="vulkan_guide.html">Vulkan guide</a>. </p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga70c01918dc9d233a4fbe0681a43018af" id="r_ga70c01918dc9d233a4fbe0681a43018af"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__vulkan.html#ga70c01918dc9d233a4fbe0681a43018af">GLFWvkproc</a>) (void)</td></tr>
<tr class="memdesc:ga70c01918dc9d233a4fbe0681a43018af"><td class="mdescLeft">&#160;</td><td class="mdescRight">Vulkan API function pointer type.  <br /></td></tr>
<tr class="separator:ga70c01918dc9d233a4fbe0681a43018af"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga2e7f30931e02464b5bc8d0d4b6f9fe2b" id="r_ga2e7f30931e02464b5bc8d0d4b6f9fe2b"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__vulkan.html#ga2e7f30931e02464b5bc8d0d4b6f9fe2b">glfwVulkanSupported</a> (void)</td></tr>
<tr class="memdesc:ga2e7f30931e02464b5bc8d0d4b6f9fe2b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the Vulkan loader and an ICD have been found.  <br /></td></tr>
<tr class="separator:ga2e7f30931e02464b5bc8d0d4b6f9fe2b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga99ad342d82f4a3421e2864978cb6d1d6" id="r_ga99ad342d82f4a3421e2864978cb6d1d6"><td class="memItemLeft" align="right" valign="top">const char **&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a> (uint32_t *count)</td></tr>
<tr class="memdesc:ga99ad342d82f4a3421e2864978cb6d1d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the Vulkan instance extensions required by GLFW.  <br /></td></tr>
<tr class="separator:ga99ad342d82f4a3421e2864978cb6d1d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadf228fac94c5fd8f12423ec9af9ff1e9" id="r_gadf228fac94c5fd8f12423ec9af9ff1e9"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__vulkan.html#ga70c01918dc9d233a4fbe0681a43018af">GLFWvkproc</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__vulkan.html#gadf228fac94c5fd8f12423ec9af9ff1e9">glfwGetInstanceProcAddress</a> (VkInstance instance, const char *procname)</td></tr>
<tr class="memdesc:gadf228fac94c5fd8f12423ec9af9ff1e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the address of the specified Vulkan instance function.  <br /></td></tr>
<tr class="separator:gadf228fac94c5fd8f12423ec9af9ff1e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaff3823355cdd7e2f3f9f4d9ea9518d92" id="r_gaff3823355cdd7e2f3f9f4d9ea9518d92"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__vulkan.html#gaff3823355cdd7e2f3f9f4d9ea9518d92">glfwGetPhysicalDevicePresentationSupport</a> (VkInstance instance, VkPhysicalDevice device, uint32_t queuefamily)</td></tr>
<tr class="memdesc:gaff3823355cdd7e2f3f9f4d9ea9518d92"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the specified queue family can present images.  <br /></td></tr>
<tr class="separator:gaff3823355cdd7e2f3f9f4d9ea9518d92"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1a24536bec3f80b08ead18e28e6ae965" id="r_ga1a24536bec3f80b08ead18e28e6ae965"><td class="memItemLeft" align="right" valign="top">VkResult&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__vulkan.html#ga1a24536bec3f80b08ead18e28e6ae965">glfwCreateWindowSurface</a> (VkInstance instance, <a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, const VkAllocationCallbacks *allocator, VkSurfaceKHR *surface)</td></tr>
<tr class="memdesc:ga1a24536bec3f80b08ead18e28e6ae965"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a Vulkan surface for the specified window.  <br /></td></tr>
<tr class="separator:ga1a24536bec3f80b08ead18e28e6ae965"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Typedef Documentation</h2>
<a id="ga70c01918dc9d233a4fbe0681a43018af" name="ga70c01918dc9d233a4fbe0681a43018af"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga70c01918dc9d233a4fbe0681a43018af">&#9670;&#160;</a></span>GLFWvkproc</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWvkproc) (void)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Generic function pointer used for returning Vulkan API function pointers without forcing a cast from a regular pointer.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="vulkan_guide.html#vulkan_proc">Querying Vulkan function pointers</a> </dd>
<dd>
<a class="el" href="group__vulkan.html#gadf228fac94c5fd8f12423ec9af9ff1e9">glfwGetInstanceProcAddress</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="ga2e7f30931e02464b5bc8d0d4b6f9fe2b" name="ga2e7f30931e02464b5bc8d0d4b6f9fe2b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2e7f30931e02464b5bc8d0d4b6f9fe2b">&#9670;&#160;</a></span>glfwVulkanSupported()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwVulkanSupported </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns whether the Vulkan loader and any minimally functional ICD have been found.</p>
<p>The availability of a Vulkan loader and even an ICD does not by itself guarantee that surface creation or even instance creation is possible. Call <a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a> to check whether the extensions necessary for Vulkan surface creation are available and <a class="el" href="group__vulkan.html#gaff3823355cdd7e2f3f9f4d9ea9518d92">glfwGetPhysicalDevicePresentationSupport</a> to check whether a queue family of a physical device supports image presentation.</p>
<dl class="section return"><dt>Returns</dt><dd><code>GLFW_TRUE</code> if Vulkan is minimally available, or <code>GLFW_FALSE</code> otherwise.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="vulkan_guide.html#vulkan_support">Querying for Vulkan support</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="ga99ad342d82f4a3421e2864978cb6d1d6" name="ga99ad342d82f4a3421e2864978cb6d1d6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga99ad342d82f4a3421e2864978cb6d1d6">&#9670;&#160;</a></span>glfwGetRequiredInstanceExtensions()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char ** glfwGetRequiredInstanceExtensions </td>
          <td>(</td>
          <td class="paramtype">uint32_t *&#160;</td>
          <td class="paramname"><em>count</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns an array of names of Vulkan instance extensions required by GLFW for creating Vulkan surfaces for GLFW windows. If successful, the list will always contain <code>VK_KHR_surface</code>, so if you don't require any additional extensions you can pass this list directly to the <code>VkInstanceCreateInfo</code> struct.</p>
<p>If Vulkan is not available on the machine, this function returns <code>NULL</code> and generates a <a class="el" href="group__errors.html#ga56882b290db23261cc6c053c40c2d08e">GLFW_API_UNAVAILABLE</a> error. Call <a class="el" href="group__vulkan.html#ga2e7f30931e02464b5bc8d0d4b6f9fe2b">glfwVulkanSupported</a> to check whether Vulkan is at least minimally available.</p>
<p>If Vulkan is available but no set of extensions allowing window surface creation was found, this function returns <code>NULL</code>. You may still use Vulkan for off-screen rendering and compute work.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[out]</td><td class="paramname">count</td><td>Where to store the number of extensions in the returned array. This is set to zero if an error occurred. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>An array of ASCII encoded extension names, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga56882b290db23261cc6c053c40c2d08e">GLFW_API_UNAVAILABLE</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>Additional extensions may be required by future versions of GLFW. You should check if any extensions you wish to enable are already in the returned array, as it is an error to specify an extension more than once in the <code>VkInstanceCreateInfo</code> struct.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned array is allocated and freed by GLFW. You should not free it yourself. It is guaranteed to be valid only until the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="vulkan_guide.html#vulkan_ext">Querying required Vulkan extensions</a> </dd>
<dd>
<a class="el" href="group__vulkan.html#ga1a24536bec3f80b08ead18e28e6ae965">glfwCreateWindowSurface</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="gadf228fac94c5fd8f12423ec9af9ff1e9" name="gadf228fac94c5fd8f12423ec9af9ff1e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadf228fac94c5fd8f12423ec9af9ff1e9">&#9670;&#160;</a></span>glfwGetInstanceProcAddress()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__vulkan.html#ga70c01918dc9d233a4fbe0681a43018af">GLFWvkproc</a> glfwGetInstanceProcAddress </td>
          <td>(</td>
          <td class="paramtype">VkInstance&#160;</td>
          <td class="paramname"><em>instance</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>procname</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the address of the specified Vulkan core or extension function for the specified instance. If instance is set to <code>NULL</code> it can return any function exported from the Vulkan loader, including at least the following functions:</p>
<ul>
<li><code>vkEnumerateInstanceExtensionProperties</code></li>
<li><code>vkEnumerateInstanceLayerProperties</code></li>
<li><code>vkCreateInstance</code></li>
<li><code>vkGetInstanceProcAddr</code></li>
</ul>
<p>If Vulkan is not available on the machine, this function returns <code>NULL</code> and generates a <a class="el" href="group__errors.html#ga56882b290db23261cc6c053c40c2d08e">GLFW_API_UNAVAILABLE</a> error. Call <a class="el" href="group__vulkan.html#ga2e7f30931e02464b5bc8d0d4b6f9fe2b">glfwVulkanSupported</a> to check whether Vulkan is at least minimally available.</p>
<p>This function is equivalent to calling <code>vkGetInstanceProcAddr</code> with a platform-specific query of the Vulkan loader as a fallback.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">instance</td><td>The Vulkan instance to query, or <code>NULL</code> to retrieve functions related to instance creation. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">procname</td><td>The ASCII encoded name of the function. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The address of the function, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga56882b290db23261cc6c053c40c2d08e">GLFW_API_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned function pointer is valid until the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="vulkan_guide.html#vulkan_proc">Querying Vulkan function pointers</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="gaff3823355cdd7e2f3f9f4d9ea9518d92" name="gaff3823355cdd7e2f3f9f4d9ea9518d92"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaff3823355cdd7e2f3f9f4d9ea9518d92">&#9670;&#160;</a></span>glfwGetPhysicalDevicePresentationSupport()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwGetPhysicalDevicePresentationSupport </td>
          <td>(</td>
          <td class="paramtype">VkInstance&#160;</td>
          <td class="paramname"><em>instance</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">VkPhysicalDevice&#160;</td>
          <td class="paramname"><em>device</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>queuefamily</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns whether the specified queue family of the specified physical device supports presentation to the platform GLFW was built for.</p>
<p>If Vulkan or the required window surface creation instance extensions are not available on the machine, or if the specified instance was not created with the required extensions, this function returns <code>GLFW_FALSE</code> and generates a <a class="el" href="group__errors.html#ga56882b290db23261cc6c053c40c2d08e">GLFW_API_UNAVAILABLE</a> error. Call <a class="el" href="group__vulkan.html#ga2e7f30931e02464b5bc8d0d4b6f9fe2b">glfwVulkanSupported</a> to check whether Vulkan is at least minimally available and <a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a> to check what instance extensions are required.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">instance</td><td>The instance that the physical device belongs to. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">device</td><td>The physical device that the queue family belongs to. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">queuefamily</td><td>The index of the queue family to query. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><code>GLFW_TRUE</code> if the queue family supports presentation, or <code>GLFW_FALSE</code> otherwise.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga56882b290db23261cc6c053c40c2d08e">GLFW_API_UNAVAILABLE</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>macOS:</b> This function currently always returns <code>GLFW_TRUE</code>, as the <code>VK_MVK_macos_surface</code> and <code>VK_EXT_metal_surface</code> extensions do not provide a <code>vkGetPhysicalDevice*PresentationSupport</code> type function.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. For synchronization details of Vulkan objects, see the Vulkan specification.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="vulkan_guide.html#vulkan_present">Querying for Vulkan presentation support</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="ga1a24536bec3f80b08ead18e28e6ae965" name="ga1a24536bec3f80b08ead18e28e6ae965"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1a24536bec3f80b08ead18e28e6ae965">&#9670;&#160;</a></span>glfwCreateWindowSurface()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VkResult glfwCreateWindowSurface </td>
          <td>(</td>
          <td class="paramtype">VkInstance&#160;</td>
          <td class="paramname"><em>instance</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const VkAllocationCallbacks *&#160;</td>
          <td class="paramname"><em>allocator</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">VkSurfaceKHR *&#160;</td>
          <td class="paramname"><em>surface</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function creates a Vulkan surface for the specified window.</p>
<p>If the Vulkan loader or at least one minimally functional ICD were not found, this function returns <code>VK_ERROR_INITIALIZATION_FAILED</code> and generates a <a class="el" href="group__errors.html#ga56882b290db23261cc6c053c40c2d08e">GLFW_API_UNAVAILABLE</a> error. Call <a class="el" href="group__vulkan.html#ga2e7f30931e02464b5bc8d0d4b6f9fe2b">glfwVulkanSupported</a> to check whether Vulkan is at least minimally available.</p>
<p>If the required window surface creation instance extensions are not available or if the specified instance was not created with these extensions enabled, this function returns <code>VK_ERROR_EXTENSION_NOT_PRESENT</code> and generates a <a class="el" href="group__errors.html#ga56882b290db23261cc6c053c40c2d08e">GLFW_API_UNAVAILABLE</a> error. Call <a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a> to check what instance extensions are required.</p>
<p>The window surface cannot be shared with another API so the window must have been created with the <a class="el" href="window_guide.html#GLFW_CLIENT_API_attrib">client api hint</a> set to <code>GLFW_NO_API</code> otherwise it generates a <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a> error and returns <code>VK_ERROR_NATIVE_WINDOW_IN_USE_KHR</code>.</p>
<p>The window surface must be destroyed before the specified Vulkan instance. It is the responsibility of the caller to destroy the window surface. GLFW does not destroy it for you. Call <code>vkDestroySurfaceKHR</code> to destroy the surface.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">instance</td><td>The Vulkan instance to create the surface in. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to create the surface for. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">allocator</td><td>The allocator to use, or <code>NULL</code> to use the default allocator. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">surface</td><td>Where to store the handle of the surface. This is set to <code>VK_NULL_HANDLE</code> if an error occurred. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><code>VK_SUCCESS</code> if successful, or a Vulkan error code if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga56882b290db23261cc6c053c40c2d08e">GLFW_API_UNAVAILABLE</a>, <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a> and <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a></dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>If an error occurs before the creation call is made, GLFW returns the Vulkan error code most appropriate for the error. Appropriate use of <a class="el" href="group__vulkan.html#ga2e7f30931e02464b5bc8d0d4b6f9fe2b">glfwVulkanSupported</a> and <a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a> should eliminate almost all occurrences of these errors.</dd>
<dd>
<b>macOS:</b> GLFW prefers the <code>VK_EXT_metal_surface</code> extension, with the <code>VK_MVK_macos_surface</code> extension as a fallback. The name of the selected extension, if any, is included in the array returned by <a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a>.</dd>
<dd>
<b>macOS:</b> This function creates and sets a <code>CAMetalLayer</code> instance for the window content view, which is required for MoltenVK to function.</dd>
<dd>
<b>X11:</b> By default GLFW prefers the <code>VK_KHR_xcb_surface</code> extension, with the <code>VK_KHR_xlib_surface</code> extension as a fallback. You can make <code>VK_KHR_xlib_surface</code> the preferred extension by setting the <a class="el" href="intro_guide.html#GLFW_X11_XCB_VULKAN_SURFACE_hint">GLFW_X11_XCB_VULKAN_SURFACE</a> init hint. The name of the selected extension, if any, is included in the array returned by <a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. For synchronization details of Vulkan objects, see the Vulkan specification.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="vulkan_guide.html#vulkan_surface">Creating a Vulkan window surface</a> </dd>
<dd>
<a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
