// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Services_Maps_LocalSearch_1_H
#define WINRT_Windows_Services_Maps_LocalSearch_1_H
#include "winrt/impl/Windows.Services.Maps.LocalSearch.0.h"
WINRT_EXPORT namespace winrt::Windows::Services::Maps::LocalSearch
{
    struct WINRT_IMPL_EMPTY_BASES ILocalCategoriesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILocalCategoriesStatics>
    {
        ILocalCategoriesStatics(std::nullptr_t = nullptr) noexcept {}
        ILocalCategoriesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILocalLocation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILocalLocation>
    {
        ILocalLocation(std::nullptr_t = nullptr) noexcept {}
        ILocalLocation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILocalLocation2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILocalLocation2>
    {
        ILocalLocation2(std::nullptr_t = nullptr) noexcept {}
        ILocalLocation2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILocalLocationFinderResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILocalLocationFinderResult>
    {
        ILocalLocationFinderResult(std::nullptr_t = nullptr) noexcept {}
        ILocalLocationFinderResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILocalLocationFinderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILocalLocationFinderStatics>
    {
        ILocalLocationFinderStatics(std::nullptr_t = nullptr) noexcept {}
        ILocalLocationFinderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILocalLocationHoursOfOperationItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILocalLocationHoursOfOperationItem>
    {
        ILocalLocationHoursOfOperationItem(std::nullptr_t = nullptr) noexcept {}
        ILocalLocationHoursOfOperationItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILocalLocationRatingInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILocalLocationRatingInfo>
    {
        ILocalLocationRatingInfo(std::nullptr_t = nullptr) noexcept {}
        ILocalLocationRatingInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPlaceInfoHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPlaceInfoHelperStatics>
    {
        IPlaceInfoHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IPlaceInfoHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
