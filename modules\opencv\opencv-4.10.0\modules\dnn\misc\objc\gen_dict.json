{"func_arg_fix": {"Dnn": {"(Net*)readNetFromCaffe:(NSString*)prototxt caffeModel:(NSString*)caffeModel": {"readNetFromCaffe": {"name": "readNetFromCaffeFile"}}, "(Net*)readNetFromCaffe:(ByteVector*)bufferProto bufferModel:(ByteVector*)bufferModel": {"readNetFromCaffe": {"name": "readNetFromCaffeBuffer"}}, "(Net*)readNetFromDarknet:(NSString*)cfgFile darknetModel:(NSString*)darknetModel": {"readNetFromDarknet": {"name": "readNetFromDarknetFile"}}, "(Net*)readNetFromDarknet:(ByteVector*)bufferCfg bufferModel:(ByteVector*)bufferModel": {"readNetFromDarknet": {"name": "readNetFromDarknetBuffer"}}, "(Net*)readNetFromONNX:(NSString*)onnxFile": {"readNetFromONNX": {"name": "readNetFromONNXFile"}}, "(Net*)readNetFromONNX:(ByteVector*)buffer": {"readNetFromONNX": {"name": "readNetFromONNXBuffer"}}, "(Net*)readNetFromTensorflow:(NSString*)model config:(NSString*)config": {"readNetFromTensorflow": {"name": "readNetFromTensorflowFile"}}, "(Net*)readNetFromTensorflow:(ByteVector*)bufferModel bufferConfig:(ByteVector*)bufferConfig": {"readNetFromTensorflow": {"name": "readNetFromTensorflowBuffer"}}, "(Net*)readNetFromTFLite:(NSString*)model": {"readNetFromTFLite": {"name": "readNetFromTFLiteFile"}}, "(Net*)readNetFromTFLite:(ByteVector*)buffer": {"readNetFromTFLite": {"name": "readNetFromTFLiteBuffer"}}}, "Net": {"(void)forward:(NSMutableArray<Mat*>*)outputBlobs outputName:(NSString*)outputName": {"forward": {"name": "forwardOutputBlobs"}}, "(void)forward:(NSMutableArray<Mat*>*)outputBlobs outBlobNames:(NSArray<NSString*>*)outBlobNames": {"forward": {"name": "forwardOutputBlobs"}}, "(void)forwardAndRetrieve:(NSMutableArray<NSMutableArray<Mat*>*>*)outputBlobs outBlobNames:(NSArray<NSString*>*)outBlobNames": {"forward": {"swift_name": "forwardAndRetrieve"}}, "(long)getFLOPS:(IntVector*)netInputShape": {"getFLOPS": {"name": "getFLOPSWithNetInputShape"}}, "(long)getFLOPS:(NSArray<IntVector*>*)netInputShapes": {"getFLOPS": {"name": "getFLOPSWithNetInputShapes"}}, "(long)getFLOPS:(int)layerId netInputShape:(IntVector*)netInputShape": {"getFLOPS": {"name": "getFLOPSWithLayerId"}}, "(long)getFLOPS:(int)layerId netInputShapes:(NSArray<IntVector*>*)netInputShapes": {"getFLOPS": {"name": "getFLOPSWithLayerId"}}, "(Layer*)getLayer:(NSString*)layerName": {"getLayer": {"name": "getLayerByName"}}, "(Layer*)getLayer:(DictValue*)layerId": {"getLayer": {"name": "getLayerByDictValue"}}, "(void)getLayersShapes:(IntVector*)netInputShape layersIds:(IntVector*)layersIds inLayersShapes:(NSMutableArray<NSMutableArray<IntVector*>*>*)inLayersShapes outLayersShapes:(NSMutableArray<NSMutableArray<IntVector*>*>*)outLayersShapes": {"getLayersShapes": {"name": "getLayersShapesWithNetInputShape"}}, "(void)getLayersShapes:(NSArray<IntVector*>*)netInputShapes layersIds:(IntVector*)layersIds inLayersShapes:(NSMutableArray<NSMutableArray<IntVector*>*>*)inLayersShapes outLayersShapes:(NSMutableArray<NSMutableArray<IntVector*>*>*)outLayersShapes": {"getLayersShapes": {"name": "getLayersShapesWithNetInputShapes"}}, "(Mat*)getParam:(NSString*)layerName numParam:(int)numParam": {"getParam": {"name": "getParamByName"}}, "(void)setParam:(NSString*)layerName numParam:(int)numParam blob:(Mat*)blob": {"setParam": {"name": "setParamByName"}}}}, "type_dict": {"MatShape": {"objc_type": "IntVector*", "to_cpp": "%(n)s.nativeRef", "from_cpp": "[IntVector fromNative:%(n)s]", "cast_to": "std::vector<int>"}, "vector_MatShape": {"objc_type": "IntVector*", "v_type": "IntVector"}, "vector_vector_MatShape": {"objc_type": "IntVector*", "v_v_type": "IntVector"}, "LayerId": {"objc_type": "DictValue*", "to_cpp": "*(cv::dnn::DictValue*)(%(n)s.nativePtr)", "from_cpp": "[DictValue fromNative:%(n)s]"}}}