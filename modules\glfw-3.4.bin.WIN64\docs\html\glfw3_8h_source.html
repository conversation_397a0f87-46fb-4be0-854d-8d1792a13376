<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: glfw3.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function() { init_codefold(0); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_13577e2d8b9423099662de029791bd7d.html">glfw-3.4</a></li><li class="navelem"><a class="el" href="dir_b11153cd0f4fd04a7564cc166f482635.html">include</a></li><li class="navelem"><a class="el" href="dir_7f92719a7fe62e5b064f87d7a3c220b1.html">GLFW</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle"><div class="title">glfw3.h</div></div>
</div><!--header-->
<div class="contents">
<a href="glfw3_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">/*************************************************************************</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="comment"> * GLFW 3.4 - www.glfw.org</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="comment"> * A library for OpenGL, window and input</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="comment"> *------------------------------------------------------------------------</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment"> * Copyright (c) 2002-2006 Marcus Geelnard</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="comment"> * Copyright (c) 2006-2019 Camilla Löwy &lt;<EMAIL>&gt;</span></div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span><span class="comment"> *</span></div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="comment"> * This software is provided &#39;as-is&#39;, without any express or implied</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span><span class="comment"> * warranty. In no event will the authors be held liable for any damages</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span><span class="comment"> * arising from the use of this software.</span></div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="comment"> *</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span><span class="comment"> * Permission is granted to anyone to use this software for any purpose,</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="comment"> * including commercial applications, and to alter it and redistribute it</span></div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span><span class="comment"> * freely, subject to the following restrictions:</span></div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="comment"> *</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span><span class="comment"> * 1. The origin of this software must not be misrepresented; you must not</span></div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno">   17</span><span class="comment"> *    claim that you wrote the original software. If you use this software</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno">   18</span><span class="comment"> *    in a product, an acknowledgment in the product documentation would</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span><span class="comment"> *    be appreciated but is not required.</span></div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno">   20</span><span class="comment"> *</span></div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno">   21</span><span class="comment"> * 2. Altered source versions must be plainly marked as such, and must not</span></div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span><span class="comment"> *    be misrepresented as being the original software.</span></div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span><span class="comment"> *</span></div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno">   24</span><span class="comment"> * 3. This notice may not be removed or altered from any source</span></div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span><span class="comment"> *    distribution.</span></div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno">   26</span><span class="comment"> *</span></div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno">   27</span><span class="comment"> *************************************************************************/</span></div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno">   28</span> </div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno">   29</span><span class="preprocessor">#ifndef _glfw3_h_</span></div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span><span class="preprocessor">#define _glfw3_h_</span></div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno">   31</span> </div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno">   32</span><span class="preprocessor">#ifdef __cplusplus</span></div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span><span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> {</div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno">   35</span> </div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno">   36</span> </div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno">   37</span><span class="comment">/*************************************************************************</span></div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno">   38</span><span class="comment"> * Doxygen documentation</span></div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno">   39</span><span class="comment"> *************************************************************************/</span></div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span> </div>
<div class="line"><a id="l00089" name="l00089"></a><span class="lineno">   89</span><span class="comment">/*************************************************************************</span></div>
<div class="line"><a id="l00090" name="l00090"></a><span class="lineno">   90</span><span class="comment"> * Compiler- and platform-specific preprocessor work</span></div>
<div class="line"><a id="l00091" name="l00091"></a><span class="lineno">   91</span><span class="comment"> *************************************************************************/</span></div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno">   92</span> </div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span><span class="comment">/* If we are we on Windows, we want a single define for it.</span></div>
<div class="line"><a id="l00094" name="l00094"></a><span class="lineno">   94</span><span class="comment"> */</span></div>
<div class="line"><a id="l00095" name="l00095"></a><span class="lineno">   95</span><span class="preprocessor">#if !defined(_WIN32) &amp;&amp; (defined(__WIN32__) || defined(WIN32) || defined(__MINGW32__))</span></div>
<div class="line"><a id="l00096" name="l00096"></a><span class="lineno">   96</span><span class="preprocessor"> #define _WIN32</span></div>
<div class="line"><a id="l00097" name="l00097"></a><span class="lineno">   97</span><span class="preprocessor">#endif </span><span class="comment">/* _WIN32 */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00098" name="l00098"></a><span class="lineno">   98</span> </div>
<div class="line"><a id="l00099" name="l00099"></a><span class="lineno">   99</span><span class="comment">/* Include because most Windows GLU headers need wchar_t and</span></div>
<div class="line"><a id="l00100" name="l00100"></a><span class="lineno">  100</span><span class="comment"> * the macOS OpenGL header blocks the definition of ptrdiff_t by glext.h.</span></div>
<div class="line"><a id="l00101" name="l00101"></a><span class="lineno">  101</span><span class="comment"> * Include it unconditionally to avoid surprising side-effects.</span></div>
<div class="line"><a id="l00102" name="l00102"></a><span class="lineno">  102</span><span class="comment"> */</span></div>
<div class="line"><a id="l00103" name="l00103"></a><span class="lineno">  103</span><span class="preprocessor">#include &lt;stddef.h&gt;</span></div>
<div class="line"><a id="l00104" name="l00104"></a><span class="lineno">  104</span> </div>
<div class="line"><a id="l00105" name="l00105"></a><span class="lineno">  105</span><span class="comment">/* Include because it is needed by Vulkan and related functions.</span></div>
<div class="line"><a id="l00106" name="l00106"></a><span class="lineno">  106</span><span class="comment"> * Include it unconditionally to avoid surprising side-effects.</span></div>
<div class="line"><a id="l00107" name="l00107"></a><span class="lineno">  107</span><span class="comment"> */</span></div>
<div class="line"><a id="l00108" name="l00108"></a><span class="lineno">  108</span><span class="preprocessor">#include &lt;stdint.h&gt;</span></div>
<div class="line"><a id="l00109" name="l00109"></a><span class="lineno">  109</span> </div>
<div class="line"><a id="l00110" name="l00110"></a><span class="lineno">  110</span><span class="preprocessor">#if defined(GLFW_INCLUDE_VULKAN)</span></div>
<div class="line"><a id="l00111" name="l00111"></a><span class="lineno">  111</span><span class="preprocessor">  #include &lt;vulkan/vulkan.h&gt;</span></div>
<div class="line"><a id="l00112" name="l00112"></a><span class="lineno">  112</span><span class="preprocessor">#endif </span><span class="comment">/* Vulkan header */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00113" name="l00113"></a><span class="lineno">  113</span> </div>
<div class="line"><a id="l00114" name="l00114"></a><span class="lineno">  114</span><span class="comment">/* The Vulkan header may have indirectly included windows.h (because of</span></div>
<div class="line"><a id="l00115" name="l00115"></a><span class="lineno">  115</span><span class="comment"> * VK_USE_PLATFORM_WIN32_KHR) so we offer our replacement symbols after it.</span></div>
<div class="line"><a id="l00116" name="l00116"></a><span class="lineno">  116</span><span class="comment"> */</span></div>
<div class="line"><a id="l00117" name="l00117"></a><span class="lineno">  117</span> </div>
<div class="line"><a id="l00118" name="l00118"></a><span class="lineno">  118</span><span class="comment">/* It is customary to use APIENTRY for OpenGL function pointer declarations on</span></div>
<div class="line"><a id="l00119" name="l00119"></a><span class="lineno">  119</span><span class="comment"> * all platforms.  Additionally, the Windows OpenGL header needs APIENTRY.</span></div>
<div class="line"><a id="l00120" name="l00120"></a><span class="lineno">  120</span><span class="comment"> */</span></div>
<div class="line"><a id="l00121" name="l00121"></a><span class="lineno">  121</span><span class="preprocessor">#if !defined(APIENTRY)</span></div>
<div class="line"><a id="l00122" name="l00122"></a><span class="lineno">  122</span><span class="preprocessor"> #if defined(_WIN32)</span></div>
<div class="line"><a id="l00123" name="l00123"></a><span class="lineno">  123</span><span class="preprocessor">  #define APIENTRY __stdcall</span></div>
<div class="line"><a id="l00124" name="l00124"></a><span class="lineno">  124</span><span class="preprocessor"> #else</span></div>
<div class="line"><a id="l00125" name="l00125"></a><span class="lineno">  125</span><span class="preprocessor">  #define APIENTRY</span></div>
<div class="line"><a id="l00126" name="l00126"></a><span class="lineno">  126</span><span class="preprocessor"> #endif</span></div>
<div class="line"><a id="l00127" name="l00127"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a8a8538c5500308b4211844f2fb26c7b9">  127</a></span><span class="preprocessor"> #define GLFW_APIENTRY_DEFINED</span></div>
<div class="line"><a id="l00128" name="l00128"></a><span class="lineno">  128</span><span class="preprocessor">#endif </span><span class="comment">/* APIENTRY */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00129" name="l00129"></a><span class="lineno">  129</span> </div>
<div class="line"><a id="l00130" name="l00130"></a><span class="lineno">  130</span><span class="comment">/* Some Windows OpenGL headers need this.</span></div>
<div class="line"><a id="l00131" name="l00131"></a><span class="lineno">  131</span><span class="comment"> */</span></div>
<div class="line"><a id="l00132" name="l00132"></a><span class="lineno">  132</span><span class="preprocessor">#if !defined(WINGDIAPI) &amp;&amp; defined(_WIN32)</span></div>
<div class="line"><a id="l00133" name="l00133"></a><span class="lineno">  133</span><span class="preprocessor"> #define WINGDIAPI __declspec(dllimport)</span></div>
<div class="line"><a id="l00134" name="l00134"></a><span class="lineno">  134</span><span class="preprocessor"> #define GLFW_WINGDIAPI_DEFINED</span></div>
<div class="line"><a id="l00135" name="l00135"></a><span class="lineno">  135</span><span class="preprocessor">#endif </span><span class="comment">/* WINGDIAPI */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00136" name="l00136"></a><span class="lineno">  136</span> </div>
<div class="line"><a id="l00137" name="l00137"></a><span class="lineno">  137</span><span class="comment">/* Some Windows GLU headers need this.</span></div>
<div class="line"><a id="l00138" name="l00138"></a><span class="lineno">  138</span><span class="comment"> */</span></div>
<div class="line"><a id="l00139" name="l00139"></a><span class="lineno">  139</span><span class="preprocessor">#if !defined(CALLBACK) &amp;&amp; defined(_WIN32)</span></div>
<div class="line"><a id="l00140" name="l00140"></a><span class="lineno">  140</span><span class="preprocessor"> #define CALLBACK __stdcall</span></div>
<div class="line"><a id="l00141" name="l00141"></a><span class="lineno">  141</span><span class="preprocessor"> #define GLFW_CALLBACK_DEFINED</span></div>
<div class="line"><a id="l00142" name="l00142"></a><span class="lineno">  142</span><span class="preprocessor">#endif </span><span class="comment">/* CALLBACK */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00143" name="l00143"></a><span class="lineno">  143</span> </div>
<div class="line"><a id="l00144" name="l00144"></a><span class="lineno">  144</span><span class="comment">/* Include the chosen OpenGL or OpenGL ES headers.</span></div>
<div class="line"><a id="l00145" name="l00145"></a><span class="lineno">  145</span><span class="comment"> */</span></div>
<div class="line"><a id="l00146" name="l00146"></a><span class="lineno">  146</span><span class="preprocessor">#if defined(GLFW_INCLUDE_ES1)</span></div>
<div class="line"><a id="l00147" name="l00147"></a><span class="lineno">  147</span> </div>
<div class="line"><a id="l00148" name="l00148"></a><span class="lineno">  148</span><span class="preprocessor"> #include &lt;GLES/gl.h&gt;</span></div>
<div class="line"><a id="l00149" name="l00149"></a><span class="lineno">  149</span><span class="preprocessor"> #if defined(GLFW_INCLUDE_GLEXT)</span></div>
<div class="line"><a id="l00150" name="l00150"></a><span class="lineno">  150</span><span class="preprocessor">  #include &lt;GLES/glext.h&gt;</span></div>
<div class="line"><a id="l00151" name="l00151"></a><span class="lineno">  151</span><span class="preprocessor"> #endif</span></div>
<div class="line"><a id="l00152" name="l00152"></a><span class="lineno">  152</span> </div>
<div class="line"><a id="l00153" name="l00153"></a><span class="lineno">  153</span><span class="preprocessor">#elif defined(GLFW_INCLUDE_ES2)</span></div>
<div class="line"><a id="l00154" name="l00154"></a><span class="lineno">  154</span> </div>
<div class="line"><a id="l00155" name="l00155"></a><span class="lineno">  155</span><span class="preprocessor"> #include &lt;GLES2/gl2.h&gt;</span></div>
<div class="line"><a id="l00156" name="l00156"></a><span class="lineno">  156</span><span class="preprocessor"> #if defined(GLFW_INCLUDE_GLEXT)</span></div>
<div class="line"><a id="l00157" name="l00157"></a><span class="lineno">  157</span><span class="preprocessor">  #include &lt;GLES2/gl2ext.h&gt;</span></div>
<div class="line"><a id="l00158" name="l00158"></a><span class="lineno">  158</span><span class="preprocessor"> #endif</span></div>
<div class="line"><a id="l00159" name="l00159"></a><span class="lineno">  159</span> </div>
<div class="line"><a id="l00160" name="l00160"></a><span class="lineno">  160</span><span class="preprocessor">#elif defined(GLFW_INCLUDE_ES3)</span></div>
<div class="line"><a id="l00161" name="l00161"></a><span class="lineno">  161</span> </div>
<div class="line"><a id="l00162" name="l00162"></a><span class="lineno">  162</span><span class="preprocessor"> #include &lt;GLES3/gl3.h&gt;</span></div>
<div class="line"><a id="l00163" name="l00163"></a><span class="lineno">  163</span><span class="preprocessor"> #if defined(GLFW_INCLUDE_GLEXT)</span></div>
<div class="line"><a id="l00164" name="l00164"></a><span class="lineno">  164</span><span class="preprocessor">  #include &lt;GLES2/gl2ext.h&gt;</span></div>
<div class="line"><a id="l00165" name="l00165"></a><span class="lineno">  165</span><span class="preprocessor"> #endif</span></div>
<div class="line"><a id="l00166" name="l00166"></a><span class="lineno">  166</span> </div>
<div class="line"><a id="l00167" name="l00167"></a><span class="lineno">  167</span><span class="preprocessor">#elif defined(GLFW_INCLUDE_ES31)</span></div>
<div class="line"><a id="l00168" name="l00168"></a><span class="lineno">  168</span> </div>
<div class="line"><a id="l00169" name="l00169"></a><span class="lineno">  169</span><span class="preprocessor"> #include &lt;GLES3/gl31.h&gt;</span></div>
<div class="line"><a id="l00170" name="l00170"></a><span class="lineno">  170</span><span class="preprocessor"> #if defined(GLFW_INCLUDE_GLEXT)</span></div>
<div class="line"><a id="l00171" name="l00171"></a><span class="lineno">  171</span><span class="preprocessor">  #include &lt;GLES2/gl2ext.h&gt;</span></div>
<div class="line"><a id="l00172" name="l00172"></a><span class="lineno">  172</span><span class="preprocessor"> #endif</span></div>
<div class="line"><a id="l00173" name="l00173"></a><span class="lineno">  173</span> </div>
<div class="line"><a id="l00174" name="l00174"></a><span class="lineno">  174</span><span class="preprocessor">#elif defined(GLFW_INCLUDE_ES32)</span></div>
<div class="line"><a id="l00175" name="l00175"></a><span class="lineno">  175</span> </div>
<div class="line"><a id="l00176" name="l00176"></a><span class="lineno">  176</span><span class="preprocessor"> #include &lt;GLES3/gl32.h&gt;</span></div>
<div class="line"><a id="l00177" name="l00177"></a><span class="lineno">  177</span><span class="preprocessor"> #if defined(GLFW_INCLUDE_GLEXT)</span></div>
<div class="line"><a id="l00178" name="l00178"></a><span class="lineno">  178</span><span class="preprocessor">  #include &lt;GLES2/gl2ext.h&gt;</span></div>
<div class="line"><a id="l00179" name="l00179"></a><span class="lineno">  179</span><span class="preprocessor"> #endif</span></div>
<div class="line"><a id="l00180" name="l00180"></a><span class="lineno">  180</span> </div>
<div class="line"><a id="l00181" name="l00181"></a><span class="lineno">  181</span><span class="preprocessor">#elif defined(GLFW_INCLUDE_GLCOREARB)</span></div>
<div class="line"><a id="l00182" name="l00182"></a><span class="lineno">  182</span> </div>
<div class="line"><a id="l00183" name="l00183"></a><span class="lineno">  183</span><span class="preprocessor"> #if defined(__APPLE__)</span></div>
<div class="line"><a id="l00184" name="l00184"></a><span class="lineno">  184</span> </div>
<div class="line"><a id="l00185" name="l00185"></a><span class="lineno">  185</span><span class="preprocessor">  #include &lt;OpenGL/gl3.h&gt;</span></div>
<div class="line"><a id="l00186" name="l00186"></a><span class="lineno">  186</span><span class="preprocessor">  #if defined(GLFW_INCLUDE_GLEXT)</span></div>
<div class="line"><a id="l00187" name="l00187"></a><span class="lineno">  187</span><span class="preprocessor">   #include &lt;OpenGL/gl3ext.h&gt;</span></div>
<div class="line"><a id="l00188" name="l00188"></a><span class="lineno">  188</span><span class="preprocessor">  #endif </span><span class="comment">/*GLFW_INCLUDE_GLEXT*/</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00189" name="l00189"></a><span class="lineno">  189</span> </div>
<div class="line"><a id="l00190" name="l00190"></a><span class="lineno">  190</span><span class="preprocessor"> #else </span><span class="comment">/*__APPLE__*/</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00191" name="l00191"></a><span class="lineno">  191</span> </div>
<div class="line"><a id="l00192" name="l00192"></a><span class="lineno">  192</span><span class="preprocessor">  #include &lt;GL/glcorearb.h&gt;</span></div>
<div class="line"><a id="l00193" name="l00193"></a><span class="lineno">  193</span><span class="preprocessor">  #if defined(GLFW_INCLUDE_GLEXT)</span></div>
<div class="line"><a id="l00194" name="l00194"></a><span class="lineno">  194</span><span class="preprocessor">   #include &lt;GL/glext.h&gt;</span></div>
<div class="line"><a id="l00195" name="l00195"></a><span class="lineno">  195</span><span class="preprocessor">  #endif</span></div>
<div class="line"><a id="l00196" name="l00196"></a><span class="lineno">  196</span> </div>
<div class="line"><a id="l00197" name="l00197"></a><span class="lineno">  197</span><span class="preprocessor"> #endif </span><span class="comment">/*__APPLE__*/</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00198" name="l00198"></a><span class="lineno">  198</span> </div>
<div class="line"><a id="l00199" name="l00199"></a><span class="lineno">  199</span><span class="preprocessor">#elif defined(GLFW_INCLUDE_GLU)</span></div>
<div class="line"><a id="l00200" name="l00200"></a><span class="lineno">  200</span> </div>
<div class="line"><a id="l00201" name="l00201"></a><span class="lineno">  201</span><span class="preprocessor"> #if defined(__APPLE__)</span></div>
<div class="line"><a id="l00202" name="l00202"></a><span class="lineno">  202</span> </div>
<div class="line"><a id="l00203" name="l00203"></a><span class="lineno">  203</span><span class="preprocessor">  #if defined(GLFW_INCLUDE_GLU)</span></div>
<div class="line"><a id="l00204" name="l00204"></a><span class="lineno">  204</span><span class="preprocessor">   #include &lt;OpenGL/glu.h&gt;</span></div>
<div class="line"><a id="l00205" name="l00205"></a><span class="lineno">  205</span><span class="preprocessor">  #endif</span></div>
<div class="line"><a id="l00206" name="l00206"></a><span class="lineno">  206</span> </div>
<div class="line"><a id="l00207" name="l00207"></a><span class="lineno">  207</span><span class="preprocessor"> #else </span><span class="comment">/*__APPLE__*/</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00208" name="l00208"></a><span class="lineno">  208</span> </div>
<div class="line"><a id="l00209" name="l00209"></a><span class="lineno">  209</span><span class="preprocessor">  #if defined(GLFW_INCLUDE_GLU)</span></div>
<div class="line"><a id="l00210" name="l00210"></a><span class="lineno">  210</span><span class="preprocessor">   #include &lt;GL/glu.h&gt;</span></div>
<div class="line"><a id="l00211" name="l00211"></a><span class="lineno">  211</span><span class="preprocessor">  #endif</span></div>
<div class="line"><a id="l00212" name="l00212"></a><span class="lineno">  212</span> </div>
<div class="line"><a id="l00213" name="l00213"></a><span class="lineno">  213</span><span class="preprocessor"> #endif </span><span class="comment">/*__APPLE__*/</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00214" name="l00214"></a><span class="lineno">  214</span> </div>
<div class="line"><a id="l00215" name="l00215"></a><span class="lineno">  215</span><span class="preprocessor">#elif !defined(GLFW_INCLUDE_NONE) &amp;&amp; \</span></div>
<div class="line"><a id="l00216" name="l00216"></a><span class="lineno">  216</span><span class="preprocessor">      !defined(__gl_h_) &amp;&amp; \</span></div>
<div class="line"><a id="l00217" name="l00217"></a><span class="lineno">  217</span><span class="preprocessor">      !defined(__gles1_gl_h_) &amp;&amp; \</span></div>
<div class="line"><a id="l00218" name="l00218"></a><span class="lineno">  218</span><span class="preprocessor">      !defined(__gles2_gl2_h_) &amp;&amp; \</span></div>
<div class="line"><a id="l00219" name="l00219"></a><span class="lineno">  219</span><span class="preprocessor">      !defined(__gles2_gl3_h_) &amp;&amp; \</span></div>
<div class="line"><a id="l00220" name="l00220"></a><span class="lineno">  220</span><span class="preprocessor">      !defined(__gles2_gl31_h_) &amp;&amp; \</span></div>
<div class="line"><a id="l00221" name="l00221"></a><span class="lineno">  221</span><span class="preprocessor">      !defined(__gles2_gl32_h_) &amp;&amp; \</span></div>
<div class="line"><a id="l00222" name="l00222"></a><span class="lineno">  222</span><span class="preprocessor">      !defined(__gl_glcorearb_h_) &amp;&amp; \</span></div>
<div class="line"><a id="l00223" name="l00223"></a><span class="lineno">  223</span><span class="preprocessor">      !defined(__gl2_h_) </span><span class="comment">/*legacy*/</span><span class="preprocessor"> &amp;&amp; \</span></div>
<div class="line"><a id="l00224" name="l00224"></a><span class="lineno">  224</span><span class="preprocessor">      !defined(__gl3_h_) </span><span class="comment">/*legacy*/</span><span class="preprocessor"> &amp;&amp; \</span></div>
<div class="line"><a id="l00225" name="l00225"></a><span class="lineno">  225</span><span class="preprocessor">      !defined(__gl31_h_) </span><span class="comment">/*legacy*/</span><span class="preprocessor"> &amp;&amp; \</span></div>
<div class="line"><a id="l00226" name="l00226"></a><span class="lineno">  226</span><span class="preprocessor">      !defined(__gl32_h_) </span><span class="comment">/*legacy*/</span><span class="preprocessor"> &amp;&amp; \</span></div>
<div class="line"><a id="l00227" name="l00227"></a><span class="lineno">  227</span><span class="preprocessor">      !defined(__glcorearb_h_) </span><span class="comment">/*legacy*/</span><span class="preprocessor"> &amp;&amp; \</span></div>
<div class="line"><a id="l00228" name="l00228"></a><span class="lineno">  228</span><span class="preprocessor">      !defined(__GL_H__) </span><span class="comment">/*non-standard*/</span><span class="preprocessor"> &amp;&amp; \</span></div>
<div class="line"><a id="l00229" name="l00229"></a><span class="lineno">  229</span><span class="preprocessor">      !defined(__gltypes_h_) </span><span class="comment">/*non-standard*/</span><span class="preprocessor"> &amp;&amp; \</span></div>
<div class="line"><a id="l00230" name="l00230"></a><span class="lineno">  230</span><span class="preprocessor">      !defined(__glee_h_) </span><span class="comment">/*non-standard*/</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00231" name="l00231"></a><span class="lineno">  231</span> </div>
<div class="line"><a id="l00232" name="l00232"></a><span class="lineno">  232</span><span class="preprocessor"> #if defined(__APPLE__)</span></div>
<div class="line"><a id="l00233" name="l00233"></a><span class="lineno">  233</span> </div>
<div class="line"><a id="l00234" name="l00234"></a><span class="lineno">  234</span><span class="preprocessor">  #if !defined(GLFW_INCLUDE_GLEXT)</span></div>
<div class="line"><a id="l00235" name="l00235"></a><span class="lineno">  235</span><span class="preprocessor">   #define GL_GLEXT_LEGACY</span></div>
<div class="line"><a id="l00236" name="l00236"></a><span class="lineno">  236</span><span class="preprocessor">  #endif</span></div>
<div class="line"><a id="l00237" name="l00237"></a><span class="lineno">  237</span><span class="preprocessor">  #include &lt;OpenGL/gl.h&gt;</span></div>
<div class="line"><a id="l00238" name="l00238"></a><span class="lineno">  238</span> </div>
<div class="line"><a id="l00239" name="l00239"></a><span class="lineno">  239</span><span class="preprocessor"> #else </span><span class="comment">/*__APPLE__*/</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00240" name="l00240"></a><span class="lineno">  240</span> </div>
<div class="line"><a id="l00241" name="l00241"></a><span class="lineno">  241</span><span class="preprocessor">  #include &lt;GL/gl.h&gt;</span></div>
<div class="line"><a id="l00242" name="l00242"></a><span class="lineno">  242</span><span class="preprocessor">  #if defined(GLFW_INCLUDE_GLEXT)</span></div>
<div class="line"><a id="l00243" name="l00243"></a><span class="lineno">  243</span><span class="preprocessor">   #include &lt;GL/glext.h&gt;</span></div>
<div class="line"><a id="l00244" name="l00244"></a><span class="lineno">  244</span><span class="preprocessor">  #endif</span></div>
<div class="line"><a id="l00245" name="l00245"></a><span class="lineno">  245</span> </div>
<div class="line"><a id="l00246" name="l00246"></a><span class="lineno">  246</span><span class="preprocessor"> #endif </span><span class="comment">/*__APPLE__*/</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00247" name="l00247"></a><span class="lineno">  247</span> </div>
<div class="line"><a id="l00248" name="l00248"></a><span class="lineno">  248</span><span class="preprocessor">#endif </span><span class="comment">/* OpenGL and OpenGL ES headers */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00249" name="l00249"></a><span class="lineno">  249</span> </div>
<div class="line"><a id="l00250" name="l00250"></a><span class="lineno">  250</span><span class="preprocessor">#if defined(GLFW_DLL) &amp;&amp; defined(_GLFW_BUILD_DLL)</span></div>
<div class="line"><a id="l00251" name="l00251"></a><span class="lineno">  251</span> <span class="comment">/* GLFW_DLL must be defined by applications that are linking against the DLL</span></div>
<div class="line"><a id="l00252" name="l00252"></a><span class="lineno">  252</span><span class="comment">  * version of the GLFW library.  _GLFW_BUILD_DLL is defined by the GLFW</span></div>
<div class="line"><a id="l00253" name="l00253"></a><span class="lineno">  253</span><span class="comment">  * configuration header when compiling the DLL version of the library.</span></div>
<div class="line"><a id="l00254" name="l00254"></a><span class="lineno">  254</span><span class="comment">  */</span></div>
<div class="line"><a id="l00255" name="l00255"></a><span class="lineno">  255</span><span class="preprocessor"> #error &quot;You must not have both GLFW_DLL and _GLFW_BUILD_DLL defined&quot;</span></div>
<div class="line"><a id="l00256" name="l00256"></a><span class="lineno">  256</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00257" name="l00257"></a><span class="lineno">  257</span> </div>
<div class="line"><a id="l00258" name="l00258"></a><span class="lineno">  258</span><span class="comment">/* GLFWAPI is used to declare public API functions for export</span></div>
<div class="line"><a id="l00259" name="l00259"></a><span class="lineno">  259</span><span class="comment"> * from the DLL / shared library / dynamic library.</span></div>
<div class="line"><a id="l00260" name="l00260"></a><span class="lineno">  260</span><span class="comment"> */</span></div>
<div class="line"><a id="l00261" name="l00261"></a><span class="lineno">  261</span><span class="preprocessor">#if defined(_WIN32) &amp;&amp; defined(_GLFW_BUILD_DLL)</span></div>
<div class="line"><a id="l00262" name="l00262"></a><span class="lineno">  262</span> <span class="comment">/* We are building GLFW as a Win32 DLL */</span></div>
<div class="line"><a id="l00263" name="l00263"></a><span class="lineno">  263</span><span class="preprocessor"> #define GLFWAPI __declspec(dllexport)</span></div>
<div class="line"><a id="l00264" name="l00264"></a><span class="lineno">  264</span><span class="preprocessor">#elif defined(_WIN32) &amp;&amp; defined(GLFW_DLL)</span></div>
<div class="line"><a id="l00265" name="l00265"></a><span class="lineno">  265</span> <span class="comment">/* We are calling a GLFW Win32 DLL */</span></div>
<div class="line"><a id="l00266" name="l00266"></a><span class="lineno">  266</span><span class="preprocessor"> #define GLFWAPI __declspec(dllimport)</span></div>
<div class="line"><a id="l00267" name="l00267"></a><span class="lineno">  267</span><span class="preprocessor">#elif defined(__GNUC__) &amp;&amp; defined(_GLFW_BUILD_DLL)</span></div>
<div class="line"><a id="l00268" name="l00268"></a><span class="lineno">  268</span> <span class="comment">/* We are building GLFW as a Unix shared library */</span></div>
<div class="line"><a id="l00269" name="l00269"></a><span class="lineno">  269</span><span class="preprocessor"> #define GLFWAPI __attribute__((visibility(&quot;default&quot;)))</span></div>
<div class="line"><a id="l00270" name="l00270"></a><span class="lineno">  270</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00271" name="l00271"></a><span class="lineno">  271</span><span class="preprocessor"> #define GLFWAPI</span></div>
<div class="line"><a id="l00272" name="l00272"></a><span class="lineno">  272</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00273" name="l00273"></a><span class="lineno">  273</span> </div>
<div class="line"><a id="l00274" name="l00274"></a><span class="lineno">  274</span> </div>
<div class="line"><a id="l00275" name="l00275"></a><span class="lineno">  275</span><span class="comment">/*************************************************************************</span></div>
<div class="line"><a id="l00276" name="l00276"></a><span class="lineno">  276</span><span class="comment"> * GLFW API tokens</span></div>
<div class="line"><a id="l00277" name="l00277"></a><span class="lineno">  277</span><span class="comment"> *************************************************************************/</span></div>
<div class="line"><a id="l00278" name="l00278"></a><span class="lineno">  278</span> </div>
<div class="line"><a id="l00287" name="l00287"></a><span class="lineno"><a class="line" href="group__init.html#ga6337d9ea43b22fc529b2bba066b4a576">  287</a></span><span class="preprocessor">#define GLFW_VERSION_MAJOR          3</span></div>
<div class="line"><a id="l00294" name="l00294"></a><span class="lineno"><a class="line" href="group__init.html#gaf80d40f0aea7088ff337606e9c48f7a3">  294</a></span><span class="preprocessor">#define GLFW_VERSION_MINOR          4</span></div>
<div class="line"><a id="l00301" name="l00301"></a><span class="lineno"><a class="line" href="group__init.html#gab72ae2e2035d9ea461abc3495eac0502">  301</a></span><span class="preprocessor">#define GLFW_VERSION_REVISION       0</span></div>
<div class="line"><a id="l00312" name="l00312"></a><span class="lineno"><a class="line" href="group__init.html#ga2744fbb29b5631bb28802dbe0cf36eba">  312</a></span><span class="preprocessor">#define GLFW_TRUE                   1</span></div>
<div class="line"><a id="l00321" name="l00321"></a><span class="lineno"><a class="line" href="group__init.html#gac877fe3b627d21ef3a0a23e0a73ba8c5">  321</a></span><span class="preprocessor">#define GLFW_FALSE                  0</span></div>
<div class="line"><a id="l00322" name="l00322"></a><span class="lineno">  322</span> </div>
<div class="line"><a id="l00331" name="l00331"></a><span class="lineno"><a class="line" href="group__input.html#gada11d965c4da13090ad336e030e4d11f">  331</a></span><span class="preprocessor">#define GLFW_RELEASE                0</span></div>
<div class="line"><a id="l00338" name="l00338"></a><span class="lineno"><a class="line" href="group__input.html#ga2485743d0b59df3791c45951c4195265">  338</a></span><span class="preprocessor">#define GLFW_PRESS                  1</span></div>
<div class="line"><a id="l00345" name="l00345"></a><span class="lineno"><a class="line" href="group__input.html#gac96fd3b9fc66c6f0eebaf6532595338f">  345</a></span><span class="preprocessor">#define GLFW_REPEAT                 2</span></div>
<div class="line"><a id="l00355" name="l00355"></a><span class="lineno"><a class="line" href="group__hat__state.html#gae2c0bcb7aec609e4736437554f6638fd">  355</a></span><span class="preprocessor">#define GLFW_HAT_CENTERED           0</span></div>
<div class="line"><a id="l00356" name="l00356"></a><span class="lineno"><a class="line" href="group__hat__state.html#ga8c9720c76cd1b912738159ed74c85b36">  356</a></span><span class="preprocessor">#define GLFW_HAT_UP                 1</span></div>
<div class="line"><a id="l00357" name="l00357"></a><span class="lineno"><a class="line" href="group__hat__state.html#ga252586e3bbde75f4b0e07ad3124867f5">  357</a></span><span class="preprocessor">#define GLFW_HAT_RIGHT              2</span></div>
<div class="line"><a id="l00358" name="l00358"></a><span class="lineno"><a class="line" href="group__hat__state.html#gad60d1fd0dc85c18f2642cbae96d3deff">  358</a></span><span class="preprocessor">#define GLFW_HAT_DOWN               4</span></div>
<div class="line"><a id="l00359" name="l00359"></a><span class="lineno"><a class="line" href="group__hat__state.html#gac775f4b3154fdf5db93eb432ba546dff">  359</a></span><span class="preprocessor">#define GLFW_HAT_LEFT               8</span></div>
<div class="line"><a id="l00360" name="l00360"></a><span class="lineno"><a class="line" href="group__hat__state.html#ga94aea0ae241a8b902883536c592ee693">  360</a></span><span class="preprocessor">#define GLFW_HAT_RIGHT_UP           (GLFW_HAT_RIGHT | GLFW_HAT_UP)</span></div>
<div class="line"><a id="l00361" name="l00361"></a><span class="lineno"><a class="line" href="group__hat__state.html#gad7f0e4f52fd68d734863aaeadab3a3f5">  361</a></span><span class="preprocessor">#define GLFW_HAT_RIGHT_DOWN         (GLFW_HAT_RIGHT | GLFW_HAT_DOWN)</span></div>
<div class="line"><a id="l00362" name="l00362"></a><span class="lineno"><a class="line" href="group__hat__state.html#ga638f0e20dc5de90de21a33564e8ce129">  362</a></span><span class="preprocessor">#define GLFW_HAT_LEFT_UP            (GLFW_HAT_LEFT  | GLFW_HAT_UP)</span></div>
<div class="line"><a id="l00363" name="l00363"></a><span class="lineno"><a class="line" href="group__hat__state.html#ga76c02baf1ea345fcbe3e8ff176a73e19">  363</a></span><span class="preprocessor">#define GLFW_HAT_LEFT_DOWN          (GLFW_HAT_LEFT  | GLFW_HAT_DOWN)</span></div>
<div class="line"><a id="l00364" name="l00364"></a><span class="lineno">  364</span> </div>
<div class="line"><a id="l00367" name="l00367"></a><span class="lineno"><a class="line" href="group__input.html#ga99aacc875b6b27a072552631e13775c7">  367</a></span><span class="preprocessor">#define GLFW_KEY_UNKNOWN            -1</span></div>
<div class="line"><a id="l00368" name="l00368"></a><span class="lineno">  368</span> </div>
<div class="line"><a id="l00395" name="l00395"></a><span class="lineno">  395</span><span class="comment">/* Printable keys */</span></div>
<div class="line"><a id="l00396" name="l00396"></a><span class="lineno"><a class="line" href="group__keys.html#gaddb2c23772b97fd7e26e8ee66f1ad014">  396</a></span><span class="preprocessor">#define GLFW_KEY_SPACE              32</span></div>
<div class="line"><a id="l00397" name="l00397"></a><span class="lineno"><a class="line" href="group__keys.html#ga6059b0b048ba6980b6107fffbd3b4b24">  397</a></span><span class="preprocessor">#define GLFW_KEY_APOSTROPHE         39  </span><span class="comment">/* &#39; */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00398" name="l00398"></a><span class="lineno"><a class="line" href="group__keys.html#gab3d5d72e59d3055f494627b0a524926c">  398</a></span><span class="preprocessor">#define GLFW_KEY_COMMA              44  </span><span class="comment">/* , */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00399" name="l00399"></a><span class="lineno"><a class="line" href="group__keys.html#gac556b360f7f6fca4b70ba0aecf313fd4">  399</a></span><span class="preprocessor">#define GLFW_KEY_MINUS              45  </span><span class="comment">/* - */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00400" name="l00400"></a><span class="lineno"><a class="line" href="group__keys.html#ga37e296b650eab419fc474ff69033d927">  400</a></span><span class="preprocessor">#define GLFW_KEY_PERIOD             46  </span><span class="comment">/* . */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00401" name="l00401"></a><span class="lineno"><a class="line" href="group__keys.html#gadf3d753b2d479148d711de34b83fd0db">  401</a></span><span class="preprocessor">#define GLFW_KEY_SLASH              47  </span><span class="comment">/* / */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00402" name="l00402"></a><span class="lineno"><a class="line" href="group__keys.html#ga50391730e9d7112ad4fd42d0bd1597c1">  402</a></span><span class="preprocessor">#define GLFW_KEY_0                  48</span></div>
<div class="line"><a id="l00403" name="l00403"></a><span class="lineno"><a class="line" href="group__keys.html#ga05e4cae9ddb8d40cf6d82c8f11f2502f">  403</a></span><span class="preprocessor">#define GLFW_KEY_1                  49</span></div>
<div class="line"><a id="l00404" name="l00404"></a><span class="lineno"><a class="line" href="group__keys.html#gadc8e66b3a4c4b5c39ad1305cf852863c">  404</a></span><span class="preprocessor">#define GLFW_KEY_2                  50</span></div>
<div class="line"><a id="l00405" name="l00405"></a><span class="lineno"><a class="line" href="group__keys.html#ga812f0273fe1a981e1fa002ae73e92271">  405</a></span><span class="preprocessor">#define GLFW_KEY_3                  51</span></div>
<div class="line"><a id="l00406" name="l00406"></a><span class="lineno"><a class="line" href="group__keys.html#ga9e14b6975a9cc8f66cdd5cb3d3861356">  406</a></span><span class="preprocessor">#define GLFW_KEY_4                  52</span></div>
<div class="line"><a id="l00407" name="l00407"></a><span class="lineno"><a class="line" href="group__keys.html#ga4d74ddaa5d4c609993b4d4a15736c924">  407</a></span><span class="preprocessor">#define GLFW_KEY_5                  53</span></div>
<div class="line"><a id="l00408" name="l00408"></a><span class="lineno"><a class="line" href="group__keys.html#ga9ea4ab80c313a227b14d0a7c6f810b5d">  408</a></span><span class="preprocessor">#define GLFW_KEY_6                  54</span></div>
<div class="line"><a id="l00409" name="l00409"></a><span class="lineno"><a class="line" href="group__keys.html#gab79b1cfae7bd630cfc4604c1f263c666">  409</a></span><span class="preprocessor">#define GLFW_KEY_7                  55</span></div>
<div class="line"><a id="l00410" name="l00410"></a><span class="lineno"><a class="line" href="group__keys.html#gadeaa109a0f9f5afc94fe4a108e686f6f">  410</a></span><span class="preprocessor">#define GLFW_KEY_8                  56</span></div>
<div class="line"><a id="l00411" name="l00411"></a><span class="lineno"><a class="line" href="group__keys.html#ga2924cb5349ebbf97c8987f3521c44f39">  411</a></span><span class="preprocessor">#define GLFW_KEY_9                  57</span></div>
<div class="line"><a id="l00412" name="l00412"></a><span class="lineno"><a class="line" href="group__keys.html#ga84233de9ee5bb3e8788a5aa07d80af7d">  412</a></span><span class="preprocessor">#define GLFW_KEY_SEMICOLON          59  </span><span class="comment">/* ; */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00413" name="l00413"></a><span class="lineno"><a class="line" href="group__keys.html#gae1a2de47240d6664423c204bdd91bd17">  413</a></span><span class="preprocessor">#define GLFW_KEY_EQUAL              61  </span><span class="comment">/* = */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00414" name="l00414"></a><span class="lineno"><a class="line" href="group__keys.html#ga03e842608e1ea323370889d33b8f70ff">  414</a></span><span class="preprocessor">#define GLFW_KEY_A                  65</span></div>
<div class="line"><a id="l00415" name="l00415"></a><span class="lineno"><a class="line" href="group__keys.html#ga8e3fb647ff3aca9e8dbf14fe66332941">  415</a></span><span class="preprocessor">#define GLFW_KEY_B                  66</span></div>
<div class="line"><a id="l00416" name="l00416"></a><span class="lineno"><a class="line" href="group__keys.html#ga00ccf3475d9ee2e679480d540d554669">  416</a></span><span class="preprocessor">#define GLFW_KEY_C                  67</span></div>
<div class="line"><a id="l00417" name="l00417"></a><span class="lineno"><a class="line" href="group__keys.html#ga011f7cdc9a654da984a2506479606933">  417</a></span><span class="preprocessor">#define GLFW_KEY_D                  68</span></div>
<div class="line"><a id="l00418" name="l00418"></a><span class="lineno"><a class="line" href="group__keys.html#gabf48fcc3afbe69349df432b470c96ef2">  418</a></span><span class="preprocessor">#define GLFW_KEY_E                  69</span></div>
<div class="line"><a id="l00419" name="l00419"></a><span class="lineno"><a class="line" href="group__keys.html#ga5df402e02aca08444240058fd9b42a55">  419</a></span><span class="preprocessor">#define GLFW_KEY_F                  70</span></div>
<div class="line"><a id="l00420" name="l00420"></a><span class="lineno"><a class="line" href="group__keys.html#gae74ecddf7cc96104ab23989b1cdab536">  420</a></span><span class="preprocessor">#define GLFW_KEY_G                  71</span></div>
<div class="line"><a id="l00421" name="l00421"></a><span class="lineno"><a class="line" href="group__keys.html#gad4cc98fc8f35f015d9e2fb94bf136076">  421</a></span><span class="preprocessor">#define GLFW_KEY_H                  72</span></div>
<div class="line"><a id="l00422" name="l00422"></a><span class="lineno"><a class="line" href="group__keys.html#ga274655c8bfe39742684ca393cf8ed093">  422</a></span><span class="preprocessor">#define GLFW_KEY_I                  73</span></div>
<div class="line"><a id="l00423" name="l00423"></a><span class="lineno"><a class="line" href="group__keys.html#ga65ff2aedb129a3149ad9cb3e4159a75f">  423</a></span><span class="preprocessor">#define GLFW_KEY_J                  74</span></div>
<div class="line"><a id="l00424" name="l00424"></a><span class="lineno"><a class="line" href="group__keys.html#ga4ae8debadf6d2a691badae0b53ea3ba0">  424</a></span><span class="preprocessor">#define GLFW_KEY_K                  75</span></div>
<div class="line"><a id="l00425" name="l00425"></a><span class="lineno"><a class="line" href="group__keys.html#gaaa8b54a13f6b1eed85ac86f82d550db2">  425</a></span><span class="preprocessor">#define GLFW_KEY_L                  76</span></div>
<div class="line"><a id="l00426" name="l00426"></a><span class="lineno"><a class="line" href="group__keys.html#ga4d7f0260c82e4ea3d6ebc7a21d6e3716">  426</a></span><span class="preprocessor">#define GLFW_KEY_M                  77</span></div>
<div class="line"><a id="l00427" name="l00427"></a><span class="lineno"><a class="line" href="group__keys.html#gae00856dfeb5d13aafebf59d44de5cdda">  427</a></span><span class="preprocessor">#define GLFW_KEY_N                  78</span></div>
<div class="line"><a id="l00428" name="l00428"></a><span class="lineno"><a class="line" href="group__keys.html#gaecbbb79130df419d58dd7f09a169efe9">  428</a></span><span class="preprocessor">#define GLFW_KEY_O                  79</span></div>
<div class="line"><a id="l00429" name="l00429"></a><span class="lineno"><a class="line" href="group__keys.html#ga8fc15819c1094fb2afa01d84546b33e1">  429</a></span><span class="preprocessor">#define GLFW_KEY_P                  80</span></div>
<div class="line"><a id="l00430" name="l00430"></a><span class="lineno"><a class="line" href="group__keys.html#gafdd01e38b120d67cf51e348bb47f3964">  430</a></span><span class="preprocessor">#define GLFW_KEY_Q                  81</span></div>
<div class="line"><a id="l00431" name="l00431"></a><span class="lineno"><a class="line" href="group__keys.html#ga4ce6c70a0c98c50b3fe4ab9a728d4d36">  431</a></span><span class="preprocessor">#define GLFW_KEY_R                  82</span></div>
<div class="line"><a id="l00432" name="l00432"></a><span class="lineno"><a class="line" href="group__keys.html#ga1570e2ccaab036ea82bed66fc1dab2a9">  432</a></span><span class="preprocessor">#define GLFW_KEY_S                  83</span></div>
<div class="line"><a id="l00433" name="l00433"></a><span class="lineno"><a class="line" href="group__keys.html#ga90e0560422ec7a30e7f3f375bc9f37f9">  433</a></span><span class="preprocessor">#define GLFW_KEY_T                  84</span></div>
<div class="line"><a id="l00434" name="l00434"></a><span class="lineno"><a class="line" href="group__keys.html#gacad52f3bf7d378fc0ffa72a76769256d">  434</a></span><span class="preprocessor">#define GLFW_KEY_U                  85</span></div>
<div class="line"><a id="l00435" name="l00435"></a><span class="lineno"><a class="line" href="group__keys.html#ga22c7763899ecf7788862e5f90eacce6b">  435</a></span><span class="preprocessor">#define GLFW_KEY_V                  86</span></div>
<div class="line"><a id="l00436" name="l00436"></a><span class="lineno"><a class="line" href="group__keys.html#gaa06a712e6202661fc03da5bdb7b6e545">  436</a></span><span class="preprocessor">#define GLFW_KEY_W                  87</span></div>
<div class="line"><a id="l00437" name="l00437"></a><span class="lineno"><a class="line" href="group__keys.html#gac1c42c0bf4192cea713c55598b06b744">  437</a></span><span class="preprocessor">#define GLFW_KEY_X                  88</span></div>
<div class="line"><a id="l00438" name="l00438"></a><span class="lineno"><a class="line" href="group__keys.html#gafd9f115a549effdf8e372a787c360313">  438</a></span><span class="preprocessor">#define GLFW_KEY_Y                  89</span></div>
<div class="line"><a id="l00439" name="l00439"></a><span class="lineno"><a class="line" href="group__keys.html#gac489e208c26afda8d4938ed88718760a">  439</a></span><span class="preprocessor">#define GLFW_KEY_Z                  90</span></div>
<div class="line"><a id="l00440" name="l00440"></a><span class="lineno"><a class="line" href="group__keys.html#gad1c8d9adac53925276ecb1d592511d8a">  440</a></span><span class="preprocessor">#define GLFW_KEY_LEFT_BRACKET       91  </span><span class="comment">/* [ */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00441" name="l00441"></a><span class="lineno"><a class="line" href="group__keys.html#gab8155ea99d1ab27ff56f24f8dc73f8d1">  441</a></span><span class="preprocessor">#define GLFW_KEY_BACKSLASH          92  </span><span class="comment">/* \ */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00442" name="l00442"></a><span class="lineno"><a class="line" href="group__keys.html#ga86ef225fd6a66404caae71044cdd58d8">  442</a></span><span class="preprocessor">#define GLFW_KEY_RIGHT_BRACKET      93  </span><span class="comment">/* ] */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00443" name="l00443"></a><span class="lineno"><a class="line" href="group__keys.html#ga7a3701fb4e2a0b136ff4b568c3c8d668">  443</a></span><span class="preprocessor">#define GLFW_KEY_GRAVE_ACCENT       96  </span><span class="comment">/* ` */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00444" name="l00444"></a><span class="lineno"><a class="line" href="group__keys.html#gadc78dad3dab76bcd4b5c20114052577a">  444</a></span><span class="preprocessor">#define GLFW_KEY_WORLD_1            161 </span><span class="comment">/* non-US #1 */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00445" name="l00445"></a><span class="lineno"><a class="line" href="group__keys.html#ga20494bfebf0bb4fc9503afca18ab2c5e">  445</a></span><span class="preprocessor">#define GLFW_KEY_WORLD_2            162 </span><span class="comment">/* non-US #2 */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00446" name="l00446"></a><span class="lineno">  446</span> </div>
<div class="line"><a id="l00447" name="l00447"></a><span class="lineno">  447</span><span class="comment">/* Function keys */</span></div>
<div class="line"><a id="l00448" name="l00448"></a><span class="lineno"><a class="line" href="group__keys.html#gaac6596c350b635c245113b81c2123b93">  448</a></span><span class="preprocessor">#define GLFW_KEY_ESCAPE             256</span></div>
<div class="line"><a id="l00449" name="l00449"></a><span class="lineno"><a class="line" href="group__keys.html#ga9555a92ecbecdbc1f3435219c571d667">  449</a></span><span class="preprocessor">#define GLFW_KEY_ENTER              257</span></div>
<div class="line"><a id="l00450" name="l00450"></a><span class="lineno"><a class="line" href="group__keys.html#ga6908a4bda9950a3e2b73f794bbe985df">  450</a></span><span class="preprocessor">#define GLFW_KEY_TAB                258</span></div>
<div class="line"><a id="l00451" name="l00451"></a><span class="lineno"><a class="line" href="group__keys.html#ga6c0df1fe2f156bbd5a98c66d76ff3635">  451</a></span><span class="preprocessor">#define GLFW_KEY_BACKSPACE          259</span></div>
<div class="line"><a id="l00452" name="l00452"></a><span class="lineno"><a class="line" href="group__keys.html#ga373ac7365435d6b0eb1068f470e34f47">  452</a></span><span class="preprocessor">#define GLFW_KEY_INSERT             260</span></div>
<div class="line"><a id="l00453" name="l00453"></a><span class="lineno"><a class="line" href="group__keys.html#gadb111e4df74b8a715f2c05dad58d2682">  453</a></span><span class="preprocessor">#define GLFW_KEY_DELETE             261</span></div>
<div class="line"><a id="l00454" name="l00454"></a><span class="lineno"><a class="line" href="group__keys.html#ga06ba07662e8c291a4a84535379ffc7ac">  454</a></span><span class="preprocessor">#define GLFW_KEY_RIGHT              262</span></div>
<div class="line"><a id="l00455" name="l00455"></a><span class="lineno"><a class="line" href="group__keys.html#gae12a010d33c309a67ab9460c51eb2462">  455</a></span><span class="preprocessor">#define GLFW_KEY_LEFT               263</span></div>
<div class="line"><a id="l00456" name="l00456"></a><span class="lineno"><a class="line" href="group__keys.html#gae2e3958c71595607416aa7bf082be2f9">  456</a></span><span class="preprocessor">#define GLFW_KEY_DOWN               264</span></div>
<div class="line"><a id="l00457" name="l00457"></a><span class="lineno"><a class="line" href="group__keys.html#ga2f3342b194020d3544c67e3506b6f144">  457</a></span><span class="preprocessor">#define GLFW_KEY_UP                 265</span></div>
<div class="line"><a id="l00458" name="l00458"></a><span class="lineno"><a class="line" href="group__keys.html#ga3ab731f9622f0db280178a5f3cc6d586">  458</a></span><span class="preprocessor">#define GLFW_KEY_PAGE_UP            266</span></div>
<div class="line"><a id="l00459" name="l00459"></a><span class="lineno"><a class="line" href="group__keys.html#gaee0a8fa442001cc2147812f84b59041c">  459</a></span><span class="preprocessor">#define GLFW_KEY_PAGE_DOWN          267</span></div>
<div class="line"><a id="l00460" name="l00460"></a><span class="lineno"><a class="line" href="group__keys.html#ga41452c7287195d481e43207318c126a7">  460</a></span><span class="preprocessor">#define GLFW_KEY_HOME               268</span></div>
<div class="line"><a id="l00461" name="l00461"></a><span class="lineno"><a class="line" href="group__keys.html#ga86587ea1df19a65978d3e3b8439bedd9">  461</a></span><span class="preprocessor">#define GLFW_KEY_END                269</span></div>
<div class="line"><a id="l00462" name="l00462"></a><span class="lineno"><a class="line" href="group__keys.html#ga92c1d2c9d63485f3d70f94f688d48672">  462</a></span><span class="preprocessor">#define GLFW_KEY_CAPS_LOCK          280</span></div>
<div class="line"><a id="l00463" name="l00463"></a><span class="lineno"><a class="line" href="group__keys.html#gaf622b63b9537f7084c2ab649b8365630">  463</a></span><span class="preprocessor">#define GLFW_KEY_SCROLL_LOCK        281</span></div>
<div class="line"><a id="l00464" name="l00464"></a><span class="lineno"><a class="line" href="group__keys.html#ga3946edc362aeff213b2be6304296cf43">  464</a></span><span class="preprocessor">#define GLFW_KEY_NUM_LOCK           282</span></div>
<div class="line"><a id="l00465" name="l00465"></a><span class="lineno"><a class="line" href="group__keys.html#gaf964c2e65e97d0cf785a5636ee8df642">  465</a></span><span class="preprocessor">#define GLFW_KEY_PRINT_SCREEN       283</span></div>
<div class="line"><a id="l00466" name="l00466"></a><span class="lineno"><a class="line" href="group__keys.html#ga8116b9692d87382afb5849b6d8907f18">  466</a></span><span class="preprocessor">#define GLFW_KEY_PAUSE              284</span></div>
<div class="line"><a id="l00467" name="l00467"></a><span class="lineno"><a class="line" href="group__keys.html#gafb8d66c573acf22e364049477dcbea30">  467</a></span><span class="preprocessor">#define GLFW_KEY_F1                 290</span></div>
<div class="line"><a id="l00468" name="l00468"></a><span class="lineno"><a class="line" href="group__keys.html#ga0900750aff94889b940f5e428c07daee">  468</a></span><span class="preprocessor">#define GLFW_KEY_F2                 291</span></div>
<div class="line"><a id="l00469" name="l00469"></a><span class="lineno"><a class="line" href="group__keys.html#gaed7cd729c0147a551bb8b7bb36c17015">  469</a></span><span class="preprocessor">#define GLFW_KEY_F3                 292</span></div>
<div class="line"><a id="l00470" name="l00470"></a><span class="lineno"><a class="line" href="group__keys.html#ga9b61ebd0c63b44b7332fda2c9763eaa6">  470</a></span><span class="preprocessor">#define GLFW_KEY_F4                 293</span></div>
<div class="line"><a id="l00471" name="l00471"></a><span class="lineno"><a class="line" href="group__keys.html#gaf258dda9947daa428377938ed577c8c2">  471</a></span><span class="preprocessor">#define GLFW_KEY_F5                 294</span></div>
<div class="line"><a id="l00472" name="l00472"></a><span class="lineno"><a class="line" href="group__keys.html#ga6dc2d3f87b9d51ffbbbe2ef0299d8e1d">  472</a></span><span class="preprocessor">#define GLFW_KEY_F6                 295</span></div>
<div class="line"><a id="l00473" name="l00473"></a><span class="lineno"><a class="line" href="group__keys.html#gacca6ef8a2162c52a0ac1d881e8d9c38a">  473</a></span><span class="preprocessor">#define GLFW_KEY_F7                 296</span></div>
<div class="line"><a id="l00474" name="l00474"></a><span class="lineno"><a class="line" href="group__keys.html#gac9d39390336ae14e4a93e295de43c7e8">  474</a></span><span class="preprocessor">#define GLFW_KEY_F8                 297</span></div>
<div class="line"><a id="l00475" name="l00475"></a><span class="lineno"><a class="line" href="group__keys.html#gae40de0de1c9f21cd26c9afa3d7050851">  475</a></span><span class="preprocessor">#define GLFW_KEY_F9                 298</span></div>
<div class="line"><a id="l00476" name="l00476"></a><span class="lineno"><a class="line" href="group__keys.html#ga718d11d2f7d57471a2f6a894235995b1">  476</a></span><span class="preprocessor">#define GLFW_KEY_F10                299</span></div>
<div class="line"><a id="l00477" name="l00477"></a><span class="lineno"><a class="line" href="group__keys.html#ga0bc04b11627e7d69339151e7306b2832">  477</a></span><span class="preprocessor">#define GLFW_KEY_F11                300</span></div>
<div class="line"><a id="l00478" name="l00478"></a><span class="lineno"><a class="line" href="group__keys.html#gaf5908fa9b0a906ae03fc2c61ac7aa3e2">  478</a></span><span class="preprocessor">#define GLFW_KEY_F12                301</span></div>
<div class="line"><a id="l00479" name="l00479"></a><span class="lineno"><a class="line" href="group__keys.html#gad637f4308655e1001bd6ad942bc0fd4b">  479</a></span><span class="preprocessor">#define GLFW_KEY_F13                302</span></div>
<div class="line"><a id="l00480" name="l00480"></a><span class="lineno"><a class="line" href="group__keys.html#gaf14c66cff3396e5bd46e803c035e6c1f">  480</a></span><span class="preprocessor">#define GLFW_KEY_F14                303</span></div>
<div class="line"><a id="l00481" name="l00481"></a><span class="lineno"><a class="line" href="group__keys.html#ga7f70970db6e8be1794da8516a6d14058">  481</a></span><span class="preprocessor">#define GLFW_KEY_F15                304</span></div>
<div class="line"><a id="l00482" name="l00482"></a><span class="lineno"><a class="line" href="group__keys.html#gaa582dbb1d2ba2050aa1dca0838095b27">  482</a></span><span class="preprocessor">#define GLFW_KEY_F16                305</span></div>
<div class="line"><a id="l00483" name="l00483"></a><span class="lineno"><a class="line" href="group__keys.html#ga972ce5c365e2394b36104b0e3125c748">  483</a></span><span class="preprocessor">#define GLFW_KEY_F17                306</span></div>
<div class="line"><a id="l00484" name="l00484"></a><span class="lineno"><a class="line" href="group__keys.html#gaebf6391058d5566601e357edc5ea737c">  484</a></span><span class="preprocessor">#define GLFW_KEY_F18                307</span></div>
<div class="line"><a id="l00485" name="l00485"></a><span class="lineno"><a class="line" href="group__keys.html#gaec011d9ba044058cb54529da710e9791">  485</a></span><span class="preprocessor">#define GLFW_KEY_F19                308</span></div>
<div class="line"><a id="l00486" name="l00486"></a><span class="lineno"><a class="line" href="group__keys.html#ga82b9c721ada04cd5ca8de767da38022f">  486</a></span><span class="preprocessor">#define GLFW_KEY_F20                309</span></div>
<div class="line"><a id="l00487" name="l00487"></a><span class="lineno"><a class="line" href="group__keys.html#ga356afb14d3440ff2bb378f74f7ebc60f">  487</a></span><span class="preprocessor">#define GLFW_KEY_F21                310</span></div>
<div class="line"><a id="l00488" name="l00488"></a><span class="lineno"><a class="line" href="group__keys.html#ga90960bd2a155f2b09675324d3dff1565">  488</a></span><span class="preprocessor">#define GLFW_KEY_F22                311</span></div>
<div class="line"><a id="l00489" name="l00489"></a><span class="lineno"><a class="line" href="group__keys.html#ga43c21099aac10952d1be909a8ddee4d5">  489</a></span><span class="preprocessor">#define GLFW_KEY_F23                312</span></div>
<div class="line"><a id="l00490" name="l00490"></a><span class="lineno"><a class="line" href="group__keys.html#ga8150374677b5bed3043408732152dea2">  490</a></span><span class="preprocessor">#define GLFW_KEY_F24                313</span></div>
<div class="line"><a id="l00491" name="l00491"></a><span class="lineno"><a class="line" href="group__keys.html#gaa4bbd93ed73bb4c6ae7d83df880b7199">  491</a></span><span class="preprocessor">#define GLFW_KEY_F25                314</span></div>
<div class="line"><a id="l00492" name="l00492"></a><span class="lineno"><a class="line" href="group__keys.html#ga10515dafc55b71e7683f5b4fedd1c70d">  492</a></span><span class="preprocessor">#define GLFW_KEY_KP_0               320</span></div>
<div class="line"><a id="l00493" name="l00493"></a><span class="lineno"><a class="line" href="group__keys.html#gaf3a29a334402c5eaf0b3439edf5587c3">  493</a></span><span class="preprocessor">#define GLFW_KEY_KP_1               321</span></div>
<div class="line"><a id="l00494" name="l00494"></a><span class="lineno"><a class="line" href="group__keys.html#gaf82d5a802ab8213c72653d7480c16f13">  494</a></span><span class="preprocessor">#define GLFW_KEY_KP_2               322</span></div>
<div class="line"><a id="l00495" name="l00495"></a><span class="lineno"><a class="line" href="group__keys.html#ga7e25ff30d56cd512828c1d4ae8d54ef2">  495</a></span><span class="preprocessor">#define GLFW_KEY_KP_3               323</span></div>
<div class="line"><a id="l00496" name="l00496"></a><span class="lineno"><a class="line" href="group__keys.html#gada7ec86778b85e0b4de0beea72234aea">  496</a></span><span class="preprocessor">#define GLFW_KEY_KP_4               324</span></div>
<div class="line"><a id="l00497" name="l00497"></a><span class="lineno"><a class="line" href="group__keys.html#ga9a5be274434866c51738cafbb6d26b45">  497</a></span><span class="preprocessor">#define GLFW_KEY_KP_5               325</span></div>
<div class="line"><a id="l00498" name="l00498"></a><span class="lineno"><a class="line" href="group__keys.html#gafc141b0f8450519084c01092a3157faa">  498</a></span><span class="preprocessor">#define GLFW_KEY_KP_6               326</span></div>
<div class="line"><a id="l00499" name="l00499"></a><span class="lineno"><a class="line" href="group__keys.html#ga8882f411f05d04ec77a9563974bbfa53">  499</a></span><span class="preprocessor">#define GLFW_KEY_KP_7               327</span></div>
<div class="line"><a id="l00500" name="l00500"></a><span class="lineno"><a class="line" href="group__keys.html#gab2ea2e6a12f89d315045af520ac78cec">  500</a></span><span class="preprocessor">#define GLFW_KEY_KP_8               328</span></div>
<div class="line"><a id="l00501" name="l00501"></a><span class="lineno"><a class="line" href="group__keys.html#gafb21426b630ed4fcc084868699ba74c1">  501</a></span><span class="preprocessor">#define GLFW_KEY_KP_9               329</span></div>
<div class="line"><a id="l00502" name="l00502"></a><span class="lineno"><a class="line" href="group__keys.html#ga4e231d968796331a9ea0dbfb98d4005b">  502</a></span><span class="preprocessor">#define GLFW_KEY_KP_DECIMAL         330</span></div>
<div class="line"><a id="l00503" name="l00503"></a><span class="lineno"><a class="line" href="group__keys.html#gabca1733780a273d549129ad0f250d1e5">  503</a></span><span class="preprocessor">#define GLFW_KEY_KP_DIVIDE          331</span></div>
<div class="line"><a id="l00504" name="l00504"></a><span class="lineno"><a class="line" href="group__keys.html#ga9ada267eb0e78ed2ada8701dd24a56ef">  504</a></span><span class="preprocessor">#define GLFW_KEY_KP_MULTIPLY        332</span></div>
<div class="line"><a id="l00505" name="l00505"></a><span class="lineno"><a class="line" href="group__keys.html#gaa3dbd60782ff93d6082a124bce1fa236">  505</a></span><span class="preprocessor">#define GLFW_KEY_KP_SUBTRACT        333</span></div>
<div class="line"><a id="l00506" name="l00506"></a><span class="lineno"><a class="line" href="group__keys.html#gad09c7c98acc79e89aa6a0a91275becac">  506</a></span><span class="preprocessor">#define GLFW_KEY_KP_ADD             334</span></div>
<div class="line"><a id="l00507" name="l00507"></a><span class="lineno"><a class="line" href="group__keys.html#ga4f728f8738f2986bd63eedd3d412e8cf">  507</a></span><span class="preprocessor">#define GLFW_KEY_KP_ENTER           335</span></div>
<div class="line"><a id="l00508" name="l00508"></a><span class="lineno"><a class="line" href="group__keys.html#gaebdc76d4a808191e6d21b7e4ad2acd97">  508</a></span><span class="preprocessor">#define GLFW_KEY_KP_EQUAL           336</span></div>
<div class="line"><a id="l00509" name="l00509"></a><span class="lineno"><a class="line" href="group__keys.html#ga8a530a28a65c44ab5d00b759b756d3f6">  509</a></span><span class="preprocessor">#define GLFW_KEY_LEFT_SHIFT         340</span></div>
<div class="line"><a id="l00510" name="l00510"></a><span class="lineno"><a class="line" href="group__keys.html#ga9f97b743e81460ac4b2deddecd10a464">  510</a></span><span class="preprocessor">#define GLFW_KEY_LEFT_CONTROL       341</span></div>
<div class="line"><a id="l00511" name="l00511"></a><span class="lineno"><a class="line" href="group__keys.html#ga7f27dabf63a7789daa31e1c96790219b">  511</a></span><span class="preprocessor">#define GLFW_KEY_LEFT_ALT           342</span></div>
<div class="line"><a id="l00512" name="l00512"></a><span class="lineno"><a class="line" href="group__keys.html#gafb1207c91997fc295afd1835fbc5641a">  512</a></span><span class="preprocessor">#define GLFW_KEY_LEFT_SUPER         343</span></div>
<div class="line"><a id="l00513" name="l00513"></a><span class="lineno"><a class="line" href="group__keys.html#gaffca36b99c9dce1a19cb9befbadce691">  513</a></span><span class="preprocessor">#define GLFW_KEY_RIGHT_SHIFT        344</span></div>
<div class="line"><a id="l00514" name="l00514"></a><span class="lineno"><a class="line" href="group__keys.html#gad1ca2094b2694e7251d0ab1fd34f8519">  514</a></span><span class="preprocessor">#define GLFW_KEY_RIGHT_CONTROL      345</span></div>
<div class="line"><a id="l00515" name="l00515"></a><span class="lineno"><a class="line" href="group__keys.html#ga687b38009131cfdd07a8d05fff8fa446">  515</a></span><span class="preprocessor">#define GLFW_KEY_RIGHT_ALT          346</span></div>
<div class="line"><a id="l00516" name="l00516"></a><span class="lineno"><a class="line" href="group__keys.html#gad4547a3e8e247594acb60423fe6502db">  516</a></span><span class="preprocessor">#define GLFW_KEY_RIGHT_SUPER        347</span></div>
<div class="line"><a id="l00517" name="l00517"></a><span class="lineno"><a class="line" href="group__keys.html#ga9845be48a745fc232045c9ec174d8820">  517</a></span><span class="preprocessor">#define GLFW_KEY_MENU               348</span></div>
<div class="line"><a id="l00518" name="l00518"></a><span class="lineno">  518</span> </div>
<div class="line"><a id="l00519" name="l00519"></a><span class="lineno"><a class="line" href="group__keys.html#ga442cbaef7bfb9a4ba13594dd7fbf2789">  519</a></span><span class="preprocessor">#define GLFW_KEY_LAST               GLFW_KEY_MENU</span></div>
<div class="line"><a id="l00520" name="l00520"></a><span class="lineno">  520</span> </div>
<div class="line"><a id="l00535" name="l00535"></a><span class="lineno"><a class="line" href="group__mods.html#ga14994d3196c290aaa347248e51740274">  535</a></span><span class="preprocessor">#define GLFW_MOD_SHIFT           0x0001</span></div>
<div class="line"><a id="l00540" name="l00540"></a><span class="lineno"><a class="line" href="group__mods.html#ga6ed94871c3208eefd85713fa929d45aa">  540</a></span><span class="preprocessor">#define GLFW_MOD_CONTROL         0x0002</span></div>
<div class="line"><a id="l00545" name="l00545"></a><span class="lineno"><a class="line" href="group__mods.html#gad2acd5633463c29e07008687ea73c0f4">  545</a></span><span class="preprocessor">#define GLFW_MOD_ALT             0x0004</span></div>
<div class="line"><a id="l00550" name="l00550"></a><span class="lineno"><a class="line" href="group__mods.html#ga6b64ba10ea0227cf6f42efd0a220aba1">  550</a></span><span class="preprocessor">#define GLFW_MOD_SUPER           0x0008</span></div>
<div class="line"><a id="l00556" name="l00556"></a><span class="lineno"><a class="line" href="group__mods.html#gaefeef8fcf825a6e43e241b337897200f">  556</a></span><span class="preprocessor">#define GLFW_MOD_CAPS_LOCK       0x0010</span></div>
<div class="line"><a id="l00562" name="l00562"></a><span class="lineno"><a class="line" href="group__mods.html#ga64e020b8a42af8376e944baf61feecbe">  562</a></span><span class="preprocessor">#define GLFW_MOD_NUM_LOCK        0x0020</span></div>
<div class="line"><a id="l00563" name="l00563"></a><span class="lineno">  563</span> </div>
<div class="line"><a id="l00573" name="l00573"></a><span class="lineno"><a class="line" href="group__buttons.html#ga181a6e875251fd8671654eff00f9112e">  573</a></span><span class="preprocessor">#define GLFW_MOUSE_BUTTON_1         0</span></div>
<div class="line"><a id="l00574" name="l00574"></a><span class="lineno"><a class="line" href="group__buttons.html#ga604b39b92c88ce9bd332e97fc3f4156c">  574</a></span><span class="preprocessor">#define GLFW_MOUSE_BUTTON_2         1</span></div>
<div class="line"><a id="l00575" name="l00575"></a><span class="lineno"><a class="line" href="group__buttons.html#ga0130d505563d0236a6f85545f19e1721">  575</a></span><span class="preprocessor">#define GLFW_MOUSE_BUTTON_3         2</span></div>
<div class="line"><a id="l00576" name="l00576"></a><span class="lineno"><a class="line" href="group__buttons.html#ga53f4097bb01d5521c7d9513418c91ca9">  576</a></span><span class="preprocessor">#define GLFW_MOUSE_BUTTON_4         3</span></div>
<div class="line"><a id="l00577" name="l00577"></a><span class="lineno"><a class="line" href="group__buttons.html#gaf08c4ddecb051d3d9667db1d5e417c9c">  577</a></span><span class="preprocessor">#define GLFW_MOUSE_BUTTON_5         4</span></div>
<div class="line"><a id="l00578" name="l00578"></a><span class="lineno"><a class="line" href="group__buttons.html#gae8513e06aab8aa393b595f22c6d8257a">  578</a></span><span class="preprocessor">#define GLFW_MOUSE_BUTTON_6         5</span></div>
<div class="line"><a id="l00579" name="l00579"></a><span class="lineno"><a class="line" href="group__buttons.html#ga8b02a1ab55dde45b3a3883d54ffd7dc7">  579</a></span><span class="preprocessor">#define GLFW_MOUSE_BUTTON_7         6</span></div>
<div class="line"><a id="l00580" name="l00580"></a><span class="lineno"><a class="line" href="group__buttons.html#ga35d5c4263e0dc0d0a4731ca6c562f32c">  580</a></span><span class="preprocessor">#define GLFW_MOUSE_BUTTON_8         7</span></div>
<div class="line"><a id="l00581" name="l00581"></a><span class="lineno"><a class="line" href="group__buttons.html#gab1fd86a4518a9141ec7bcde2e15a2fdf">  581</a></span><span class="preprocessor">#define GLFW_MOUSE_BUTTON_LAST      GLFW_MOUSE_BUTTON_8</span></div>
<div class="line"><a id="l00582" name="l00582"></a><span class="lineno"><a class="line" href="group__buttons.html#gaf37100431dcd5082d48f95ee8bc8cd56">  582</a></span><span class="preprocessor">#define GLFW_MOUSE_BUTTON_LEFT      GLFW_MOUSE_BUTTON_1</span></div>
<div class="line"><a id="l00583" name="l00583"></a><span class="lineno"><a class="line" href="group__buttons.html#ga3e2f2cf3c4942df73cc094247d275e74">  583</a></span><span class="preprocessor">#define GLFW_MOUSE_BUTTON_RIGHT     GLFW_MOUSE_BUTTON_2</span></div>
<div class="line"><a id="l00584" name="l00584"></a><span class="lineno"><a class="line" href="group__buttons.html#ga34a4d2a701434f763fd93a2ff842b95a">  584</a></span><span class="preprocessor">#define GLFW_MOUSE_BUTTON_MIDDLE    GLFW_MOUSE_BUTTON_3</span></div>
<div class="line"><a id="l00594" name="l00594"></a><span class="lineno"><a class="line" href="group__joysticks.html#ga34a0443d059e9f22272cd4669073f73d">  594</a></span><span class="preprocessor">#define GLFW_JOYSTICK_1             0</span></div>
<div class="line"><a id="l00595" name="l00595"></a><span class="lineno"><a class="line" href="group__joysticks.html#ga6eab65ec88e65e0850ef8413504cb50c">  595</a></span><span class="preprocessor">#define GLFW_JOYSTICK_2             1</span></div>
<div class="line"><a id="l00596" name="l00596"></a><span class="lineno"><a class="line" href="group__joysticks.html#gae6f3eedfeb42424c2f5e3161efb0b654">  596</a></span><span class="preprocessor">#define GLFW_JOYSTICK_3             2</span></div>
<div class="line"><a id="l00597" name="l00597"></a><span class="lineno"><a class="line" href="group__joysticks.html#ga97ddbcad02b7f48d74fad4ddb08fff59">  597</a></span><span class="preprocessor">#define GLFW_JOYSTICK_4             3</span></div>
<div class="line"><a id="l00598" name="l00598"></a><span class="lineno"><a class="line" href="group__joysticks.html#gae43281bc66d3fa5089fb50c3e7a28695">  598</a></span><span class="preprocessor">#define GLFW_JOYSTICK_5             4</span></div>
<div class="line"><a id="l00599" name="l00599"></a><span class="lineno"><a class="line" href="group__joysticks.html#ga74771620aa53bd68a487186dea66fd77">  599</a></span><span class="preprocessor">#define GLFW_JOYSTICK_6             5</span></div>
<div class="line"><a id="l00600" name="l00600"></a><span class="lineno"><a class="line" href="group__joysticks.html#ga20a9f4f3aaefed9ea5e66072fc588b87">  600</a></span><span class="preprocessor">#define GLFW_JOYSTICK_7             6</span></div>
<div class="line"><a id="l00601" name="l00601"></a><span class="lineno"><a class="line" href="group__joysticks.html#ga21a934c940bcf25db0e4c8fe9b364bdb">  601</a></span><span class="preprocessor">#define GLFW_JOYSTICK_8             7</span></div>
<div class="line"><a id="l00602" name="l00602"></a><span class="lineno"><a class="line" href="group__joysticks.html#ga87689d47df0ba6f9f5fcbbcaf7b3cecf">  602</a></span><span class="preprocessor">#define GLFW_JOYSTICK_9             8</span></div>
<div class="line"><a id="l00603" name="l00603"></a><span class="lineno"><a class="line" href="group__joysticks.html#gaef55389ee605d6dfc31aef6fe98c54ec">  603</a></span><span class="preprocessor">#define GLFW_JOYSTICK_10            9</span></div>
<div class="line"><a id="l00604" name="l00604"></a><span class="lineno"><a class="line" href="group__joysticks.html#gae7d26e3df447c2c14a569fcc18516af4">  604</a></span><span class="preprocessor">#define GLFW_JOYSTICK_11            10</span></div>
<div class="line"><a id="l00605" name="l00605"></a><span class="lineno"><a class="line" href="group__joysticks.html#gab91bbf5b7ca6be8d3ac5c4d89ff48ac7">  605</a></span><span class="preprocessor">#define GLFW_JOYSTICK_12            11</span></div>
<div class="line"><a id="l00606" name="l00606"></a><span class="lineno"><a class="line" href="group__joysticks.html#ga5c84fb4e49bf661d7d7c78eb4018c508">  606</a></span><span class="preprocessor">#define GLFW_JOYSTICK_13            12</span></div>
<div class="line"><a id="l00607" name="l00607"></a><span class="lineno"><a class="line" href="group__joysticks.html#ga89540873278ae5a42b3e70d64164dc74">  607</a></span><span class="preprocessor">#define GLFW_JOYSTICK_14            13</span></div>
<div class="line"><a id="l00608" name="l00608"></a><span class="lineno"><a class="line" href="group__joysticks.html#ga7b02ab70daf7a78bcc942d5d4cc1dcf9">  608</a></span><span class="preprocessor">#define GLFW_JOYSTICK_15            14</span></div>
<div class="line"><a id="l00609" name="l00609"></a><span class="lineno"><a class="line" href="group__joysticks.html#ga453edeeabf350827646b6857df4f80ce">  609</a></span><span class="preprocessor">#define GLFW_JOYSTICK_16            15</span></div>
<div class="line"><a id="l00610" name="l00610"></a><span class="lineno"><a class="line" href="group__joysticks.html#ga9ca13ebf24c331dd98df17d84a4b72c9">  610</a></span><span class="preprocessor">#define GLFW_JOYSTICK_LAST          GLFW_JOYSTICK_16</span></div>
<div class="line"><a id="l00620" name="l00620"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#gae055a12fbf4b48b5954c8e1cd129b810">  620</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_A               0</span></div>
<div class="line"><a id="l00621" name="l00621"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#ga2228a6512fd5950cdb51ba07846546fa">  621</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_B               1</span></div>
<div class="line"><a id="l00622" name="l00622"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#ga52cc94785cf3fe9a12e246539259887c">  622</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_X               2</span></div>
<div class="line"><a id="l00623" name="l00623"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#gafc931248bda494b530cbe057f386a5ed">  623</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_Y               3</span></div>
<div class="line"><a id="l00624" name="l00624"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#ga17d67b4f39a39d6b813bd1567a3507c3">  624</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_LEFT_BUMPER     4</span></div>
<div class="line"><a id="l00625" name="l00625"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#gadfbc9ea9bf3aae896b79fa49fdc85c7f">  625</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_RIGHT_BUMPER    5</span></div>
<div class="line"><a id="l00626" name="l00626"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#gabc7c0264ce778835b516a472b47f6caf">  626</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_BACK            6</span></div>
<div class="line"><a id="l00627" name="l00627"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#ga04606949dd9139434b8a1bedf4ac1021">  627</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_START           7</span></div>
<div class="line"><a id="l00628" name="l00628"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#ga7fa48c32e5b2f5db2f080aa0b8b573dc">  628</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_GUIDE           8</span></div>
<div class="line"><a id="l00629" name="l00629"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#ga3e089787327454f7bfca7364d6ca206a">  629</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_LEFT_THUMB      9</span></div>
<div class="line"><a id="l00630" name="l00630"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#ga1c003f52b5aebb45272475b48953b21a">  630</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_RIGHT_THUMB     10</span></div>
<div class="line"><a id="l00631" name="l00631"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#ga4f1ed6f974a47bc8930d4874a283476a">  631</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_DPAD_UP         11</span></div>
<div class="line"><a id="l00632" name="l00632"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#gae2a780d2a8c79e0b77c0b7b601ca57c6">  632</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_DPAD_RIGHT      12</span></div>
<div class="line"><a id="l00633" name="l00633"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#ga8f2b731b97d80f90f11967a83207665c">  633</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_DPAD_DOWN       13</span></div>
<div class="line"><a id="l00634" name="l00634"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#gaf0697e0e8607b2ebe1c93b0c6befe301">  634</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_DPAD_LEFT       14</span></div>
<div class="line"><a id="l00635" name="l00635"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#ga5cc98882f4f81dacf761639a567f61eb">  635</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_LAST            GLFW_GAMEPAD_BUTTON_DPAD_LEFT</span></div>
<div class="line"><a id="l00636" name="l00636"></a><span class="lineno">  636</span> </div>
<div class="line"><a id="l00637" name="l00637"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#gaf08d0df26527c9305253422bd98ed63a">  637</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_CROSS       GLFW_GAMEPAD_BUTTON_A</span></div>
<div class="line"><a id="l00638" name="l00638"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#gaaef094b3dacbf15f272b274516839b82">  638</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_CIRCLE      GLFW_GAMEPAD_BUTTON_B</span></div>
<div class="line"><a id="l00639" name="l00639"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#gafc7821e87d77d41ed2cd3e1f726ec35f">  639</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_SQUARE      GLFW_GAMEPAD_BUTTON_X</span></div>
<div class="line"><a id="l00640" name="l00640"></a><span class="lineno"><a class="line" href="group__gamepad__buttons.html#ga3a7ef6bcb768a08cd3bf142f7f09f802">  640</a></span><span class="preprocessor">#define GLFW_GAMEPAD_BUTTON_TRIANGLE    GLFW_GAMEPAD_BUTTON_Y</span></div>
<div class="line"><a id="l00650" name="l00650"></a><span class="lineno"><a class="line" href="group__gamepad__axes.html#ga544e396d092036a7d80c1e5f233f7a38">  650</a></span><span class="preprocessor">#define GLFW_GAMEPAD_AXIS_LEFT_X        0</span></div>
<div class="line"><a id="l00651" name="l00651"></a><span class="lineno"><a class="line" href="group__gamepad__axes.html#ga64dcf2c6e9be50b7c556ff7671996dd5">  651</a></span><span class="preprocessor">#define GLFW_GAMEPAD_AXIS_LEFT_Y        1</span></div>
<div class="line"><a id="l00652" name="l00652"></a><span class="lineno"><a class="line" href="group__gamepad__axes.html#gabd6785106cd3c5a044a6e49a395ee2fc">  652</a></span><span class="preprocessor">#define GLFW_GAMEPAD_AXIS_RIGHT_X       2</span></div>
<div class="line"><a id="l00653" name="l00653"></a><span class="lineno"><a class="line" href="group__gamepad__axes.html#ga1cc20566d44d521b7183681a8e88e2e4">  653</a></span><span class="preprocessor">#define GLFW_GAMEPAD_AXIS_RIGHT_Y       3</span></div>
<div class="line"><a id="l00654" name="l00654"></a><span class="lineno"><a class="line" href="group__gamepad__axes.html#ga6d79561dd8907c37354426242901b86e">  654</a></span><span class="preprocessor">#define GLFW_GAMEPAD_AXIS_LEFT_TRIGGER  4</span></div>
<div class="line"><a id="l00655" name="l00655"></a><span class="lineno"><a class="line" href="group__gamepad__axes.html#ga121a7d5d20589a423cd1634dd6ee6eab">  655</a></span><span class="preprocessor">#define GLFW_GAMEPAD_AXIS_RIGHT_TRIGGER 5</span></div>
<div class="line"><a id="l00656" name="l00656"></a><span class="lineno"><a class="line" href="group__gamepad__axes.html#ga0818fd9433e1359692b7443293e5ac86">  656</a></span><span class="preprocessor">#define GLFW_GAMEPAD_AXIS_LAST          GLFW_GAMEPAD_AXIS_RIGHT_TRIGGER</span></div>
<div class="line"><a id="l00672" name="l00672"></a><span class="lineno"><a class="line" href="group__errors.html#gafa30deee5db4d69c4c93d116ed87dbf4">  672</a></span><span class="preprocessor">#define GLFW_NO_ERROR               0</span></div>
<div class="line"><a id="l00681" name="l00681"></a><span class="lineno"><a class="line" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">  681</a></span><span class="preprocessor">#define GLFW_NOT_INITIALIZED        0x00010001</span></div>
<div class="line"><a id="l00691" name="l00691"></a><span class="lineno"><a class="line" href="group__errors.html#gaa8290386e9528ccb9e42a3a4e16fc0d0">  691</a></span><span class="preprocessor">#define GLFW_NO_CURRENT_CONTEXT     0x00010002</span></div>
<div class="line"><a id="l00699" name="l00699"></a><span class="lineno"><a class="line" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">  699</a></span><span class="preprocessor">#define GLFW_INVALID_ENUM           0x00010003</span></div>
<div class="line"><a id="l00710" name="l00710"></a><span class="lineno"><a class="line" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">  710</a></span><span class="preprocessor">#define GLFW_INVALID_VALUE          0x00010004</span></div>
<div class="line"><a id="l00718" name="l00718"></a><span class="lineno"><a class="line" href="group__errors.html#ga9023953a2bcb98c2906afd071d21ee7f">  718</a></span><span class="preprocessor">#define GLFW_OUT_OF_MEMORY          0x00010005</span></div>
<div class="line"><a id="l00734" name="l00734"></a><span class="lineno"><a class="line" href="group__errors.html#ga56882b290db23261cc6c053c40c2d08e">  734</a></span><span class="preprocessor">#define GLFW_API_UNAVAILABLE        0x00010006</span></div>
<div class="line"><a id="l00751" name="l00751"></a><span class="lineno"><a class="line" href="group__errors.html#gad16c5565b4a69f9c2a9ac2c0dbc89462">  751</a></span><span class="preprocessor">#define GLFW_VERSION_UNAVAILABLE    0x00010007</span></div>
<div class="line"><a id="l00762" name="l00762"></a><span class="lineno"><a class="line" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">  762</a></span><span class="preprocessor">#define GLFW_PLATFORM_ERROR         0x00010008</span></div>
<div class="line"><a id="l00781" name="l00781"></a><span class="lineno"><a class="line" href="group__errors.html#ga196e125ef261d94184e2b55c05762f14">  781</a></span><span class="preprocessor">#define GLFW_FORMAT_UNAVAILABLE     0x00010009</span></div>
<div class="line"><a id="l00789" name="l00789"></a><span class="lineno"><a class="line" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">  789</a></span><span class="preprocessor">#define GLFW_NO_WINDOW_CONTEXT      0x0001000A</span></div>
<div class="line"><a id="l00800" name="l00800"></a><span class="lineno"><a class="line" href="group__errors.html#ga09d6943923a70ddef3a085f5baee786c">  800</a></span><span class="preprocessor">#define GLFW_CURSOR_UNAVAILABLE     0x0001000B</span></div>
<div class="line"><a id="l00814" name="l00814"></a><span class="lineno"><a class="line" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">  814</a></span><span class="preprocessor">#define GLFW_FEATURE_UNAVAILABLE    0x0001000C</span></div>
<div class="line"><a id="l00827" name="l00827"></a><span class="lineno"><a class="line" href="group__errors.html#ga5dda77e023e83151e8bd55a6758f946a">  827</a></span><span class="preprocessor">#define GLFW_FEATURE_UNIMPLEMENTED  0x0001000D</span></div>
<div class="line"><a id="l00849" name="l00849"></a><span class="lineno"><a class="line" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">  849</a></span><span class="preprocessor">#define GLFW_PLATFORM_UNAVAILABLE   0x0001000E</span></div>
<div class="line"><a id="l00859" name="l00859"></a><span class="lineno"><a class="line" href="group__window.html#ga54ddb14825a1541a56e22afb5f832a9e">  859</a></span><span class="preprocessor">#define GLFW_FOCUSED                0x00020001</span></div>
<div class="line"><a id="l00864" name="l00864"></a><span class="lineno"><a class="line" href="group__window.html#ga39d44b7c056e55e581355a92d240b58a">  864</a></span><span class="preprocessor">#define GLFW_ICONIFIED              0x00020002</span></div>
<div class="line"><a id="l00870" name="l00870"></a><span class="lineno"><a class="line" href="group__window.html#gadba13c7a1b3aa40831eb2beedbd5bd1d">  870</a></span><span class="preprocessor">#define GLFW_RESIZABLE              0x00020003</span></div>
<div class="line"><a id="l00876" name="l00876"></a><span class="lineno"><a class="line" href="group__window.html#gafb3cdc45297e06d8f1eb13adc69ca6c4">  876</a></span><span class="preprocessor">#define GLFW_VISIBLE                0x00020004</span></div>
<div class="line"><a id="l00882" name="l00882"></a><span class="lineno"><a class="line" href="group__window.html#ga21b854d36314c94d65aed84405b2f25e">  882</a></span><span class="preprocessor">#define GLFW_DECORATED              0x00020005</span></div>
<div class="line"><a id="l00888" name="l00888"></a><span class="lineno"><a class="line" href="group__window.html#ga9d9874fc928200136a6dcdad726aa252">  888</a></span><span class="preprocessor">#define GLFW_AUTO_ICONIFY           0x00020006</span></div>
<div class="line"><a id="l00894" name="l00894"></a><span class="lineno"><a class="line" href="group__window.html#ga7fb0be51407783b41adbf5bec0b09d80">  894</a></span><span class="preprocessor">#define GLFW_FLOATING               0x00020007</span></div>
<div class="line"><a id="l00900" name="l00900"></a><span class="lineno"><a class="line" href="group__window.html#gad8ccb396253ad0b72c6d4c917eb38a03">  900</a></span><span class="preprocessor">#define GLFW_MAXIMIZED              0x00020008</span></div>
<div class="line"><a id="l00905" name="l00905"></a><span class="lineno"><a class="line" href="group__window.html#ga5ac0847c0aa0b3619f2855707b8a7a77">  905</a></span><span class="preprocessor">#define GLFW_CENTER_CURSOR          0x00020009</span></div>
<div class="line"><a id="l00912" name="l00912"></a><span class="lineno"><a class="line" href="group__window.html#ga60a0578c3b9449027d683a9c6abb9f14">  912</a></span><span class="preprocessor">#define GLFW_TRANSPARENT_FRAMEBUFFER 0x0002000A</span></div>
<div class="line"><a id="l00917" name="l00917"></a><span class="lineno"><a class="line" href="group__window.html#ga8665c71c6fa3d22425c6a0e8a3f89d8a">  917</a></span><span class="preprocessor">#define GLFW_HOVERED                0x0002000B</span></div>
<div class="line"><a id="l00923" name="l00923"></a><span class="lineno"><a class="line" href="group__window.html#gafa94b1da34bfd6488c0d709761504dfc">  923</a></span><span class="preprocessor">#define GLFW_FOCUS_ON_SHOW          0x0002000C</span></div>
<div class="line"><a id="l00924" name="l00924"></a><span class="lineno">  924</span> </div>
<div class="line"><a id="l00930" name="l00930"></a><span class="lineno"><a class="line" href="group__window.html#ga88981797d29800808ec242274ab5c03a">  930</a></span><span class="preprocessor">#define GLFW_MOUSE_PASSTHROUGH      0x0002000D</span></div>
<div class="line"><a id="l00931" name="l00931"></a><span class="lineno">  931</span> </div>
<div class="line"><a id="l00936" name="l00936"></a><span class="lineno"><a class="line" href="group__window.html#gaededa6b208b8e31343da56bb349c6fb2">  936</a></span><span class="preprocessor">#define GLFW_POSITION_X             0x0002000E</span></div>
<div class="line"><a id="l00937" name="l00937"></a><span class="lineno">  937</span> </div>
<div class="line"><a id="l00942" name="l00942"></a><span class="lineno"><a class="line" href="group__window.html#ga6b3ccf63683c81f479e2a98f5027200e">  942</a></span><span class="preprocessor">#define GLFW_POSITION_Y             0x0002000F</span></div>
<div class="line"><a id="l00943" name="l00943"></a><span class="lineno">  943</span> </div>
<div class="line"><a id="l00948" name="l00948"></a><span class="lineno"><a class="line" href="group__window.html#gaf78ed8e417dbcc1e354906cc2708c982">  948</a></span><span class="preprocessor">#define GLFW_RED_BITS               0x00021001</span></div>
<div class="line"><a id="l00953" name="l00953"></a><span class="lineno"><a class="line" href="group__window.html#gafba3b72638c914e5fb8a237dd4c50d4d">  953</a></span><span class="preprocessor">#define GLFW_GREEN_BITS             0x00021002</span></div>
<div class="line"><a id="l00958" name="l00958"></a><span class="lineno"><a class="line" href="group__window.html#gab292ea403db6d514537b515311bf9ae3">  958</a></span><span class="preprocessor">#define GLFW_BLUE_BITS              0x00021003</span></div>
<div class="line"><a id="l00963" name="l00963"></a><span class="lineno"><a class="line" href="group__window.html#gafed79a3f468997877da86c449bd43e8c">  963</a></span><span class="preprocessor">#define GLFW_ALPHA_BITS             0x00021004</span></div>
<div class="line"><a id="l00968" name="l00968"></a><span class="lineno"><a class="line" href="group__window.html#ga318a55eac1fee57dfe593b6d38149d07">  968</a></span><span class="preprocessor">#define GLFW_DEPTH_BITS             0x00021005</span></div>
<div class="line"><a id="l00973" name="l00973"></a><span class="lineno"><a class="line" href="group__window.html#ga5339890a45a1fb38e93cb9fcc5fd069d">  973</a></span><span class="preprocessor">#define GLFW_STENCIL_BITS           0x00021006</span></div>
<div class="line"><a id="l00978" name="l00978"></a><span class="lineno"><a class="line" href="group__window.html#gaead34a9a683b2bc20eecf30ba738bfc6">  978</a></span><span class="preprocessor">#define GLFW_ACCUM_RED_BITS         0x00021007</span></div>
<div class="line"><a id="l00983" name="l00983"></a><span class="lineno"><a class="line" href="group__window.html#ga65713cee1326f8e9d806fdf93187b471">  983</a></span><span class="preprocessor">#define GLFW_ACCUM_GREEN_BITS       0x00021008</span></div>
<div class="line"><a id="l00988" name="l00988"></a><span class="lineno"><a class="line" href="group__window.html#ga22bbe9104a8ce1f8b88fb4f186aa36ce">  988</a></span><span class="preprocessor">#define GLFW_ACCUM_BLUE_BITS        0x00021009</span></div>
<div class="line"><a id="l00993" name="l00993"></a><span class="lineno"><a class="line" href="group__window.html#gae829b55591c18169a40ab4067a041b1f">  993</a></span><span class="preprocessor">#define GLFW_ACCUM_ALPHA_BITS       0x0002100A</span></div>
<div class="line"><a id="l00998" name="l00998"></a><span class="lineno"><a class="line" href="group__window.html#gab05108c5029443b371112b031d1fa174">  998</a></span><span class="preprocessor">#define GLFW_AUX_BUFFERS            0x0002100B</span></div>
<div class="line"><a id="l01003" name="l01003"></a><span class="lineno"><a class="line" href="group__window.html#ga83d991efca02537e2d69969135b77b03"> 1003</a></span><span class="preprocessor">#define GLFW_STEREO                 0x0002100C</span></div>
<div class="line"><a id="l01008" name="l01008"></a><span class="lineno"><a class="line" href="group__window.html#ga2cdf86fdcb7722fb8829c4e201607535"> 1008</a></span><span class="preprocessor">#define GLFW_SAMPLES                0x0002100D</span></div>
<div class="line"><a id="l01013" name="l01013"></a><span class="lineno"><a class="line" href="group__window.html#ga444a8f00414a63220591f9fdb7b5642b"> 1013</a></span><span class="preprocessor">#define GLFW_SRGB_CAPABLE           0x0002100E</span></div>
<div class="line"><a id="l01018" name="l01018"></a><span class="lineno"><a class="line" href="group__window.html#ga0f20825e6e47ee8ba389024519682212"> 1018</a></span><span class="preprocessor">#define GLFW_REFRESH_RATE           0x0002100F</span></div>
<div class="line"><a id="l01024" name="l01024"></a><span class="lineno"><a class="line" href="group__window.html#ga714a5d569e8a274ea58fdfa020955339"> 1024</a></span><span class="preprocessor">#define GLFW_DOUBLEBUFFER           0x00021010</span></div>
<div class="line"><a id="l01025" name="l01025"></a><span class="lineno"> 1025</span> </div>
<div class="line"><a id="l01031" name="l01031"></a><span class="lineno"><a class="line" href="group__window.html#ga649309cf72a3d3de5b1348ca7936c95b"> 1031</a></span><span class="preprocessor">#define GLFW_CLIENT_API             0x00022001</span></div>
<div class="line"><a id="l01037" name="l01037"></a><span class="lineno"><a class="line" href="group__window.html#gafe5e4922de1f9932d7e9849bb053b0c0"> 1037</a></span><span class="preprocessor">#define GLFW_CONTEXT_VERSION_MAJOR  0x00022002</span></div>
<div class="line"><a id="l01043" name="l01043"></a><span class="lineno"><a class="line" href="group__window.html#ga31aca791e4b538c4e4a771eb95cc2d07"> 1043</a></span><span class="preprocessor">#define GLFW_CONTEXT_VERSION_MINOR  0x00022003</span></div>
<div class="line"><a id="l01049" name="l01049"></a><span class="lineno"><a class="line" href="group__window.html#gafb9475071aa77c6fb05ca5a5c8678a08"> 1049</a></span><span class="preprocessor">#define GLFW_CONTEXT_REVISION       0x00022004</span></div>
<div class="line"><a id="l01055" name="l01055"></a><span class="lineno"><a class="line" href="group__window.html#gade3593916b4c507900aa2d6844810e00"> 1055</a></span><span class="preprocessor">#define GLFW_CONTEXT_ROBUSTNESS     0x00022005</span></div>
<div class="line"><a id="l01061" name="l01061"></a><span class="lineno"><a class="line" href="group__window.html#ga13d24b12465da8b28985f46c8557925b"> 1061</a></span><span class="preprocessor">#define GLFW_OPENGL_FORWARD_COMPAT  0x00022006</span></div>
<div class="line"><a id="l01067" name="l01067"></a><span class="lineno"><a class="line" href="group__window.html#ga8d55e3afec73c7de0509c3b7ad1d9e3f"> 1067</a></span><span class="preprocessor">#define GLFW_CONTEXT_DEBUG          0x00022007</span></div>
<div class="line"><a id="l01072" name="l01072"></a><span class="lineno"><a class="line" href="group__window.html#ga87ec2df0b915201e950ca42d5d0831e1"> 1072</a></span><span class="preprocessor">#define GLFW_OPENGL_DEBUG_CONTEXT   GLFW_CONTEXT_DEBUG</span></div>
<div class="line"><a id="l01078" name="l01078"></a><span class="lineno"><a class="line" href="group__window.html#ga44f3a6b4261fbe351e0b950b0f372e12"> 1078</a></span><span class="preprocessor">#define GLFW_OPENGL_PROFILE         0x00022008</span></div>
<div class="line"><a id="l01084" name="l01084"></a><span class="lineno"><a class="line" href="group__window.html#ga72b648a8378fe3310c7c7bbecc0f7be6"> 1084</a></span><span class="preprocessor">#define GLFW_CONTEXT_RELEASE_BEHAVIOR 0x00022009</span></div>
<div class="line"><a id="l01090" name="l01090"></a><span class="lineno"><a class="line" href="group__window.html#ga5a52fdfd46d8249c211f923675728082"> 1090</a></span><span class="preprocessor">#define GLFW_CONTEXT_NO_ERROR       0x0002200A</span></div>
<div class="line"><a id="l01096" name="l01096"></a><span class="lineno"><a class="line" href="group__window.html#ga5154cebfcd831c1cc63a4d5ac9bb4486"> 1096</a></span><span class="preprocessor">#define GLFW_CONTEXT_CREATION_API   0x0002200B</span></div>
<div class="line"><a id="l01100" name="l01100"></a><span class="lineno"><a class="line" href="group__window.html#ga620bc4280c7eab81ac9f02204500ed47"> 1100</a></span><span class="preprocessor">#define GLFW_SCALE_TO_MONITOR       0x0002200C</span></div>
<div class="line"><a id="l01104" name="l01104"></a><span class="lineno"><a class="line" href="group__window.html#gaa5a9c6b4722670fd33d6e8a88f2e21bc"> 1104</a></span><span class="preprocessor">#define GLFW_SCALE_FRAMEBUFFER      0x0002200D</span></div>
<div class="line"><a id="l01111" name="l01111"></a><span class="lineno"><a class="line" href="group__window.html#gab6ef2d02eb55800d249ccf1af253c35e"> 1111</a></span><span class="preprocessor">#define GLFW_COCOA_RETINA_FRAMEBUFFER 0x00023001</span></div>
<div class="line"><a id="l01115" name="l01115"></a><span class="lineno"><a class="line" href="group__window.html#ga70fa0fbc745de6aa824df79a580e84b5"> 1115</a></span><span class="preprocessor">#define GLFW_COCOA_FRAME_NAME         0x00023002</span></div>
<div class="line"><a id="l01119" name="l01119"></a><span class="lineno"><a class="line" href="group__window.html#ga53c84ed2ddd94e15bbd44b1f6f7feafc"> 1119</a></span><span class="preprocessor">#define GLFW_COCOA_GRAPHICS_SWITCHING 0x00023003</span></div>
<div class="line"><a id="l01123" name="l01123"></a><span class="lineno"><a class="line" href="group__window.html#gae5a9ea2fccccd92edbd343fc56461114"> 1123</a></span><span class="preprocessor">#define GLFW_X11_CLASS_NAME         0x00024001</span></div>
<div class="line"><a id="l01127" name="l01127"></a><span class="lineno"><a class="line" href="group__window.html#ga494c3c0d911e4b860b946530a3e389e8"> 1127</a></span><span class="preprocessor">#define GLFW_X11_INSTANCE_NAME      0x00024002</span></div>
<div class="line"><a id="l01128" name="l01128"></a><span class="lineno"><a class="line" href="group__window.html#gaf65ea8dafdc0edb07b821b9a336d5043"> 1128</a></span><span class="preprocessor">#define GLFW_WIN32_KEYBOARD_MENU    0x00025001</span></div>
<div class="line"><a id="l01131" name="l01131"></a><span class="lineno"><a class="line" href="group__window.html#gace10f3846571de62243b46f75d978487"> 1131</a></span><span class="preprocessor">#define GLFW_WIN32_SHOWDEFAULT      0x00025002</span></div>
<div class="line"><a id="l01137" name="l01137"></a><span class="lineno"><a class="line" href="group__window.html#gafbf1ce7a4362c75e602a4df9e1bdecd3"> 1137</a></span><span class="preprocessor">#define GLFW_WAYLAND_APP_ID         0x00026001</span></div>
<div class="line"><a id="l01140" name="l01140"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a8f6dcdc968d214ff14779564f1389264"> 1140</a></span><span class="preprocessor">#define GLFW_NO_API                          0</span></div>
<div class="line"><a id="l01141" name="l01141"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a01b3f66db266341425e9abee6b257db2"> 1141</a></span><span class="preprocessor">#define GLFW_OPENGL_API             0x00030001</span></div>
<div class="line"><a id="l01142" name="l01142"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a28d9b3bc6c2a522d815c8e146595051f"> 1142</a></span><span class="preprocessor">#define GLFW_OPENGL_ES_API          0x00030002</span></div>
<div class="line"><a id="l01143" name="l01143"></a><span class="lineno"> 1143</span> </div>
<div class="line"><a id="l01144" name="l01144"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a8b306cb27f5bb0d6d67c7356a0e0fc34"> 1144</a></span><span class="preprocessor">#define GLFW_NO_ROBUSTNESS                   0</span></div>
<div class="line"><a id="l01145" name="l01145"></a><span class="lineno"><a class="line" href="glfw3_8h.html#aee84a679230d205005e22487ff678a85"> 1145</a></span><span class="preprocessor">#define GLFW_NO_RESET_NOTIFICATION  0x00031001</span></div>
<div class="line"><a id="l01146" name="l01146"></a><span class="lineno"><a class="line" href="glfw3_8h.html#aec1132f245143fc915b2f0995228564c"> 1146</a></span><span class="preprocessor">#define GLFW_LOSE_CONTEXT_ON_RESET  0x00031002</span></div>
<div class="line"><a id="l01147" name="l01147"></a><span class="lineno"> 1147</span> </div>
<div class="line"><a id="l01148" name="l01148"></a><span class="lineno"><a class="line" href="glfw3_8h.html#ad6f2335d6f21cc9bab96633b1c111d5f"> 1148</a></span><span class="preprocessor">#define GLFW_OPENGL_ANY_PROFILE              0</span></div>
<div class="line"><a id="l01149" name="l01149"></a><span class="lineno"><a class="line" href="glfw3_8h.html#af094bb16da76f66ebceb19ee213b3de8"> 1149</a></span><span class="preprocessor">#define GLFW_OPENGL_CORE_PROFILE    0x00032001</span></div>
<div class="line"><a id="l01150" name="l01150"></a><span class="lineno"><a class="line" href="glfw3_8h.html#ac06b663d79c8fcf04669cc8fcc0b7670"> 1150</a></span><span class="preprocessor">#define GLFW_OPENGL_COMPAT_PROFILE  0x00032002</span></div>
<div class="line"><a id="l01151" name="l01151"></a><span class="lineno"> 1151</span> </div>
<div class="line"><a id="l01152" name="l01152"></a><span class="lineno"><a class="line" href="glfw3_8h.html#aade31da5b884a84a7625c6b059b9132c"> 1152</a></span><span class="preprocessor">#define GLFW_CURSOR                 0x00033001</span></div>
<div class="line"><a id="l01153" name="l01153"></a><span class="lineno"><a class="line" href="glfw3_8h.html#ae3bbe2315b7691ab088159eb6c9110fc"> 1153</a></span><span class="preprocessor">#define GLFW_STICKY_KEYS            0x00033002</span></div>
<div class="line"><a id="l01154" name="l01154"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a4d7ce8ce71030c3b04e2b78145bc59d1"> 1154</a></span><span class="preprocessor">#define GLFW_STICKY_MOUSE_BUTTONS   0x00033003</span></div>
<div class="line"><a id="l01155" name="l01155"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a07b84de0b52143e1958f88a7d9105947"> 1155</a></span><span class="preprocessor">#define GLFW_LOCK_KEY_MODS          0x00033004</span></div>
<div class="line"><a id="l01156" name="l01156"></a><span class="lineno"><a class="line" href="glfw3_8h.html#aeeda1be76a44a1fc97c1282e06281fbb"> 1156</a></span><span class="preprocessor">#define GLFW_RAW_MOUSE_MOTION       0x00033005</span></div>
<div class="line"><a id="l01157" name="l01157"></a><span class="lineno"> 1157</span> </div>
<div class="line"><a id="l01158" name="l01158"></a><span class="lineno"><a class="line" href="glfw3_8h.html#ae04dd25c8577e19fa8c97368561f6c68"> 1158</a></span><span class="preprocessor">#define GLFW_CURSOR_NORMAL          0x00034001</span></div>
<div class="line"><a id="l01159" name="l01159"></a><span class="lineno"><a class="line" href="glfw3_8h.html#ac4d5cb9d78de8573349c58763d53bf11"> 1159</a></span><span class="preprocessor">#define GLFW_CURSOR_HIDDEN          0x00034002</span></div>
<div class="line"><a id="l01160" name="l01160"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a2315b99a329ce53e6a13a9d46fd5ca88"> 1160</a></span><span class="preprocessor">#define GLFW_CURSOR_DISABLED        0x00034003</span></div>
<div class="line"><a id="l01161" name="l01161"></a><span class="lineno"><a class="line" href="glfw3_8h.html#ac1dbfa0cb4641a0edc93412ade0895dc"> 1161</a></span><span class="preprocessor">#define GLFW_CURSOR_CAPTURED        0x00034004</span></div>
<div class="line"><a id="l01162" name="l01162"></a><span class="lineno"> 1162</span> </div>
<div class="line"><a id="l01163" name="l01163"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a6b47d806f285efe9bfd7aeec667297ee"> 1163</a></span><span class="preprocessor">#define GLFW_ANY_RELEASE_BEHAVIOR            0</span></div>
<div class="line"><a id="l01164" name="l01164"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a999961d391db49cb4f949c1dece0e13b"> 1164</a></span><span class="preprocessor">#define GLFW_RELEASE_BEHAVIOR_FLUSH 0x00035001</span></div>
<div class="line"><a id="l01165" name="l01165"></a><span class="lineno"><a class="line" href="glfw3_8h.html#afca09088eccacdce4b59036cfae349c5"> 1165</a></span><span class="preprocessor">#define GLFW_RELEASE_BEHAVIOR_NONE  0x00035002</span></div>
<div class="line"><a id="l01166" name="l01166"></a><span class="lineno"> 1166</span> </div>
<div class="line"><a id="l01167" name="l01167"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a0494c9bfd3f584ab41e6dbeeaa0e6a19"> 1167</a></span><span class="preprocessor">#define GLFW_NATIVE_CONTEXT_API     0x00036001</span></div>
<div class="line"><a id="l01168" name="l01168"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a03cf65c9ab01fc8b872ba58842c531c9"> 1168</a></span><span class="preprocessor">#define GLFW_EGL_CONTEXT_API        0x00036002</span></div>
<div class="line"><a id="l01169" name="l01169"></a><span class="lineno"><a class="line" href="glfw3_8h.html#afd34a473af9fa81f317910ea371b19e3"> 1169</a></span><span class="preprocessor">#define GLFW_OSMESA_CONTEXT_API     0x00036003</span></div>
<div class="line"><a id="l01170" name="l01170"></a><span class="lineno"> 1170</span> </div>
<div class="line"><a id="l01171" name="l01171"></a><span class="lineno"><a class="line" href="glfw3_8h.html#ae78e673449c2a2b8c560ca1b1e283228"> 1171</a></span><span class="preprocessor">#define GLFW_ANGLE_PLATFORM_TYPE_NONE    0x00037001</span></div>
<div class="line"><a id="l01172" name="l01172"></a><span class="lineno"><a class="line" href="glfw3_8h.html#ad8d9e97ed7790811470366b338833623"> 1172</a></span><span class="preprocessor">#define GLFW_ANGLE_PLATFORM_TYPE_OPENGL  0x00037002</span></div>
<div class="line"><a id="l01173" name="l01173"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a0003c089da020cbf957218e70245bb65"> 1173</a></span><span class="preprocessor">#define GLFW_ANGLE_PLATFORM_TYPE_OPENGLES 0x00037003</span></div>
<div class="line"><a id="l01174" name="l01174"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a6e8fdc83113d247ad792bb5c4e82c894"> 1174</a></span><span class="preprocessor">#define GLFW_ANGLE_PLATFORM_TYPE_D3D9    0x00037004</span></div>
<div class="line"><a id="l01175" name="l01175"></a><span class="lineno"><a class="line" href="glfw3_8h.html#ad6eae659811a52a5cdc43c362aedfa33"> 1175</a></span><span class="preprocessor">#define GLFW_ANGLE_PLATFORM_TYPE_D3D11   0x00037005</span></div>
<div class="line"><a id="l01176" name="l01176"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a579ac83506c7546709dad91960cc7ca1"> 1176</a></span><span class="preprocessor">#define GLFW_ANGLE_PLATFORM_TYPE_VULKAN  0x00037007</span></div>
<div class="line"><a id="l01177" name="l01177"></a><span class="lineno"><a class="line" href="glfw3_8h.html#ab56d91b26cf223dc67590a93a2f8507d"> 1177</a></span><span class="preprocessor">#define GLFW_ANGLE_PLATFORM_TYPE_METAL   0x00037008</span></div>
<div class="line"><a id="l01178" name="l01178"></a><span class="lineno"> 1178</span> </div>
<div class="line"><a id="l01179" name="l01179"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a92b0d7e0eaeeefaccc0ccc2ccb130e99"> 1179</a></span><span class="preprocessor">#define GLFW_WAYLAND_PREFER_LIBDECOR    0x00038001</span></div>
<div class="line"><a id="l01180" name="l01180"></a><span class="lineno"><a class="line" href="glfw3_8h.html#aadcea7c6afbf86b848404457c4253fd7"> 1180</a></span><span class="preprocessor">#define GLFW_WAYLAND_DISABLE_LIBDECOR   0x00038002</span></div>
<div class="line"><a id="l01181" name="l01181"></a><span class="lineno"> 1181</span> </div>
<div class="line"><a id="l01182" name="l01182"></a><span class="lineno"><a class="line" href="glfw3_8h.html#aa0e681bf859ef1bb8355692a70b0ee92"> 1182</a></span><span class="preprocessor">#define GLFW_ANY_POSITION           0x80000000</span></div>
<div class="line"><a id="l01183" name="l01183"></a><span class="lineno"> 1183</span> </div>
<div class="line"><a id="l01197" name="l01197"></a><span class="lineno"><a class="line" href="group__shapes.html#ga8ab0e717245b85506cb0eaefdea39d0a"> 1197</a></span><span class="preprocessor">#define GLFW_ARROW_CURSOR           0x00036001</span></div>
<div class="line"><a id="l01202" name="l01202"></a><span class="lineno"><a class="line" href="group__shapes.html#ga36185f4375eaada1b04e431244774c86"> 1202</a></span><span class="preprocessor">#define GLFW_IBEAM_CURSOR           0x00036002</span></div>
<div class="line"><a id="l01207" name="l01207"></a><span class="lineno"><a class="line" href="group__shapes.html#ga8af88c0ea05ab9e8f9ac1530e8873c22"> 1207</a></span><span class="preprocessor">#define GLFW_CROSSHAIR_CURSOR       0x00036003</span></div>
<div class="line"><a id="l01212" name="l01212"></a><span class="lineno"><a class="line" href="group__shapes.html#gaad01a50929fb515bf27e4462c51f6ed0"> 1212</a></span><span class="preprocessor">#define GLFW_POINTING_HAND_CURSOR   0x00036004</span></div>
<div class="line"><a id="l01218" name="l01218"></a><span class="lineno"><a class="line" href="group__shapes.html#ga2010a43dc1050a7c9154148a63cf01ad"> 1218</a></span><span class="preprocessor">#define GLFW_RESIZE_EW_CURSOR       0x00036005</span></div>
<div class="line"><a id="l01224" name="l01224"></a><span class="lineno"><a class="line" href="group__shapes.html#gaa59214e8cdc8c8adf08fdf125ed68388"> 1224</a></span><span class="preprocessor">#define GLFW_RESIZE_NS_CURSOR       0x00036006</span></div>
<div class="line"><a id="l01239" name="l01239"></a><span class="lineno"><a class="line" href="group__shapes.html#gadf2c0a495ec9cef4e1a364cc99aa78da"> 1239</a></span><span class="preprocessor">#define GLFW_RESIZE_NWSE_CURSOR     0x00036007</span></div>
<div class="line"><a id="l01254" name="l01254"></a><span class="lineno"><a class="line" href="group__shapes.html#gab06bba3b407f92807ba9b48de667a323"> 1254</a></span><span class="preprocessor">#define GLFW_RESIZE_NESW_CURSOR     0x00036008</span></div>
<div class="line"><a id="l01260" name="l01260"></a><span class="lineno"><a class="line" href="group__shapes.html#ga3a5f4811155f95ccafbbb4c9a899fc1d"> 1260</a></span><span class="preprocessor">#define GLFW_RESIZE_ALL_CURSOR      0x00036009</span></div>
<div class="line"><a id="l01272" name="l01272"></a><span class="lineno"><a class="line" href="group__shapes.html#ga297c503095b034bc8891393b637844b1"> 1272</a></span><span class="preprocessor">#define GLFW_NOT_ALLOWED_CURSOR     0x0003600A</span></div>
<div class="line"><a id="l01277" name="l01277"></a><span class="lineno"><a class="line" href="group__shapes.html#gabb3eb0109f11bb808fc34659177ca962"> 1277</a></span><span class="preprocessor">#define GLFW_HRESIZE_CURSOR         GLFW_RESIZE_EW_CURSOR</span></div>
<div class="line"><a id="l01282" name="l01282"></a><span class="lineno"><a class="line" href="group__shapes.html#gaf024f0e1ff8366fb2b5c260509a1fce5"> 1282</a></span><span class="preprocessor">#define GLFW_VRESIZE_CURSOR         GLFW_RESIZE_NS_CURSOR</span></div>
<div class="line"><a id="l01287" name="l01287"></a><span class="lineno"><a class="line" href="group__shapes.html#ga1db35e20849e0837c82e3dc1fd797263"> 1287</a></span><span class="preprocessor">#define GLFW_HAND_CURSOR            GLFW_POINTING_HAND_CURSOR</span></div>
<div class="line"><a id="l01290" name="l01290"></a><span class="lineno"><a class="line" href="glfw3_8h.html#abe11513fd1ffbee5bb9b173f06028b9e"> 1290</a></span><span class="preprocessor">#define GLFW_CONNECTED              0x00040001</span></div>
<div class="line"><a id="l01291" name="l01291"></a><span class="lineno"><a class="line" href="glfw3_8h.html#aab64b25921ef21d89252d6f0a71bfc32"> 1291</a></span><span class="preprocessor">#define GLFW_DISCONNECTED           0x00040002</span></div>
<div class="line"><a id="l01292" name="l01292"></a><span class="lineno"> 1292</span> </div>
<div class="line"><a id="l01299" name="l01299"></a><span class="lineno"><a class="line" href="group__init.html#gab9c0534709fda03ec8959201da3a9a18"> 1299</a></span><span class="preprocessor">#define GLFW_JOYSTICK_HAT_BUTTONS   0x00050001</span></div>
<div class="line"><a id="l01304" name="l01304"></a><span class="lineno"><a class="line" href="group__init.html#gaec269b24cf549ab46292c0125d8bbdce"> 1304</a></span><span class="preprocessor">#define GLFW_ANGLE_PLATFORM_TYPE    0x00050002</span></div>
<div class="line"><a id="l01309" name="l01309"></a><span class="lineno"><a class="line" href="group__init.html#ga9d38bf1fdf4f91d6565401734a7cd967"> 1309</a></span><span class="preprocessor">#define GLFW_PLATFORM               0x00050003</span></div>
<div class="line"><a id="l01314" name="l01314"></a><span class="lineno"><a class="line" href="group__init.html#gab937983147a3158d45f88fad7129d9f2"> 1314</a></span><span class="preprocessor">#define GLFW_COCOA_CHDIR_RESOURCES  0x00051001</span></div>
<div class="line"><a id="l01319" name="l01319"></a><span class="lineno"><a class="line" href="group__init.html#ga71e0b4ce2f2696a84a9b8c5e12dc70cf"> 1319</a></span><span class="preprocessor">#define GLFW_COCOA_MENUBAR          0x00051002</span></div>
<div class="line"><a id="l01324" name="l01324"></a><span class="lineno"><a class="line" href="group__init.html#gaa341e303ebeb8e4199b8ab8be84351f6"> 1324</a></span><span class="preprocessor">#define GLFW_X11_XCB_VULKAN_SURFACE 0x00052001</span></div>
<div class="line"><a id="l01329" name="l01329"></a><span class="lineno"><a class="line" href="group__init.html#ga2a3f2fd7695902c498b050215b3db452"> 1329</a></span><span class="preprocessor">#define GLFW_WAYLAND_LIBDECOR       0x00053001</span></div>
<div class="line"><a id="l01338" name="l01338"></a><span class="lineno"><a class="line" href="group__init.html#ga18b2d37374d0dea28cd69194fa85b859"> 1338</a></span><span class="preprocessor">#define GLFW_ANY_PLATFORM           0x00060000</span></div>
<div class="line"><a id="l01339" name="l01339"></a><span class="lineno"><a class="line" href="group__init.html#ga8d3d17df2ab57492cef665da52c603a1"> 1339</a></span><span class="preprocessor">#define GLFW_PLATFORM_WIN32         0x00060001</span></div>
<div class="line"><a id="l01340" name="l01340"></a><span class="lineno"><a class="line" href="group__init.html#ga83b18714254f75bc2f0cdbafa0f10b6b"> 1340</a></span><span class="preprocessor">#define GLFW_PLATFORM_COCOA         0x00060002</span></div>
<div class="line"><a id="l01341" name="l01341"></a><span class="lineno"><a class="line" href="group__init.html#gac4b08906a3cbf26c518a4a543eedd740"> 1341</a></span><span class="preprocessor">#define GLFW_PLATFORM_WAYLAND       0x00060003</span></div>
<div class="line"><a id="l01342" name="l01342"></a><span class="lineno"><a class="line" href="group__init.html#gaf5333f3933e9c248a00cfda6523f386b"> 1342</a></span><span class="preprocessor">#define GLFW_PLATFORM_X11           0x00060004</span></div>
<div class="line"><a id="l01343" name="l01343"></a><span class="lineno"><a class="line" href="group__init.html#gac06fad5a4866ae7a1d7b2675fac72d7f"> 1343</a></span><span class="preprocessor">#define GLFW_PLATFORM_NULL          0x00060005</span></div>
<div class="line"><a id="l01346" name="l01346"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a7a2edf2c18446833d27d07f1b7f3d571"> 1346</a></span><span class="preprocessor">#define GLFW_DONT_CARE              -1</span></div>
<div class="line"><a id="l01347" name="l01347"></a><span class="lineno"> 1347</span> </div>
<div class="line"><a id="l01348" name="l01348"></a><span class="lineno"> 1348</span> </div>
<div class="line"><a id="l01349" name="l01349"></a><span class="lineno"> 1349</span><span class="comment">/*************************************************************************</span></div>
<div class="line"><a id="l01350" name="l01350"></a><span class="lineno"> 1350</span><span class="comment"> * GLFW API types</span></div>
<div class="line"><a id="l01351" name="l01351"></a><span class="lineno"> 1351</span><span class="comment"> *************************************************************************/</span></div>
<div class="line"><a id="l01352" name="l01352"></a><span class="lineno"> 1352</span> </div>
<div class="line"><a id="l01365" name="l01365"></a><span class="lineno"><a class="line" href="group__context.html#ga3d47c2d2fbe0be9c505d0e04e91a133c"> 1365</a></span><span class="keyword">typedef</span> void (*<a class="code hl_typedef" href="group__context.html#ga3d47c2d2fbe0be9c505d0e04e91a133c">GLFWglproc</a>)(void);</div>
<div class="line"><a id="l01366" name="l01366"></a><span class="lineno"> 1366</span> </div>
<div class="line"><a id="l01379" name="l01379"></a><span class="lineno"><a class="line" href="group__vulkan.html#ga70c01918dc9d233a4fbe0681a43018af"> 1379</a></span><span class="keyword">typedef</span> void (*<a class="code hl_typedef" href="group__vulkan.html#ga70c01918dc9d233a4fbe0681a43018af">GLFWvkproc</a>)(void);</div>
<div class="line"><a id="l01380" name="l01380"></a><span class="lineno"> 1380</span> </div>
<div class="line"><a id="l01391" name="l01391"></a><span class="lineno"><a class="line" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3"> 1391</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> <a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>;</div>
<div class="line"><a id="l01392" name="l01392"></a><span class="lineno"> 1392</span> </div>
<div class="line"><a id="l01403" name="l01403"></a><span class="lineno"><a class="line" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242"> 1403</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> <a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>;</div>
<div class="line"><a id="l01404" name="l01404"></a><span class="lineno"> 1404</span> </div>
<div class="line"><a id="l01415" name="l01415"></a><span class="lineno"><a class="line" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4"> 1415</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_typedef" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a> <a class="code hl_typedef" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a>;</div>
<div class="line"><a id="l01416" name="l01416"></a><span class="lineno"> 1416</span> </div>
<div class="line"><a id="l01468" name="l01468"></a><span class="lineno"><a class="line" href="group__init.html#ga4306a564e9f60f4de8cc8f31731a3120"> 1468</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span>* (* GLFWallocatefun)(<span class="keywordtype">size_t</span> size, <span class="keywordtype">void</span>* user);</div>
<div class="line"><a id="l01469" name="l01469"></a><span class="lineno"> 1469</span> </div>
<div class="line"><a id="l01524" name="l01524"></a><span class="lineno"><a class="line" href="group__init.html#ga3e88a829615d8efe8bec1746f7309c63"> 1524</a></span><span class="keyword">typedef</span> <span class="keywordtype">void</span>* (* GLFWreallocatefun)(<span class="keywordtype">void</span>* block, <span class="keywordtype">size_t</span> size, <span class="keywordtype">void</span>* user);</div>
<div class="line"><a id="l01525" name="l01525"></a><span class="lineno"> 1525</span> </div>
<div class="line"><a id="l01566" name="l01566"></a><span class="lineno"><a class="line" href="group__init.html#ga7181615eda94c4b07bd72bdcee39fa28"> 1566</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__init.html#ga7181615eda94c4b07bd72bdcee39fa28">GLFWdeallocatefun</a>)(<span class="keywordtype">void</span>* block, <span class="keywordtype">void</span>* user);</div>
<div class="line"><a id="l01567" name="l01567"></a><span class="lineno"> 1567</span> </div>
<div class="line"><a id="l01590" name="l01590"></a><span class="lineno"><a class="line" href="group__init.html#ga8184701785c096b3862a75cda1bf44a3"> 1590</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__init.html#ga8184701785c096b3862a75cda1bf44a3">GLFWerrorfun</a>)(<span class="keywordtype">int</span> error_code, <span class="keyword">const</span> <span class="keywordtype">char</span>* description);</div>
<div class="line"><a id="l01591" name="l01591"></a><span class="lineno"> 1591</span> </div>
<div class="line"><a id="l01613" name="l01613"></a><span class="lineno"><a class="line" href="group__window.html#gabe287973a21a8f927cde4db06b8dcbe9"> 1613</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__window.html#gabe287973a21a8f927cde4db06b8dcbe9">GLFWwindowposfun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> xpos, <span class="keywordtype">int</span> ypos);</div>
<div class="line"><a id="l01614" name="l01614"></a><span class="lineno"> 1614</span> </div>
<div class="line"><a id="l01635" name="l01635"></a><span class="lineno"><a class="line" href="group__window.html#gaec0282944bb810f6f3163ec02da90350"> 1635</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__window.html#gaec0282944bb810f6f3163ec02da90350">GLFWwindowsizefun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> width, <span class="keywordtype">int</span> height);</div>
<div class="line"><a id="l01636" name="l01636"></a><span class="lineno"> 1636</span> </div>
<div class="line"><a id="l01655" name="l01655"></a><span class="lineno"><a class="line" href="group__window.html#gabf859b936d80961b7d39013a9694cc3e"> 1655</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__window.html#gabf859b936d80961b7d39013a9694cc3e">GLFWwindowclosefun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l01656" name="l01656"></a><span class="lineno"> 1656</span> </div>
<div class="line"><a id="l01675" name="l01675"></a><span class="lineno"><a class="line" href="group__window.html#ga431663a1427d2eb3a273bc398b6737b5"> 1675</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__window.html#ga431663a1427d2eb3a273bc398b6737b5">GLFWwindowrefreshfun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l01676" name="l01676"></a><span class="lineno"> 1676</span> </div>
<div class="line"><a id="l01696" name="l01696"></a><span class="lineno"><a class="line" href="group__window.html#gabc58c47e9d93f6eb1862d615c3680f46"> 1696</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__window.html#gabc58c47e9d93f6eb1862d615c3680f46">GLFWwindowfocusfun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> focused);</div>
<div class="line"><a id="l01697" name="l01697"></a><span class="lineno"> 1697</span> </div>
<div class="line"><a id="l01717" name="l01717"></a><span class="lineno"><a class="line" href="group__window.html#ga35c658cccba236f26e7adee0e25f6a4f"> 1717</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__window.html#ga35c658cccba236f26e7adee0e25f6a4f">GLFWwindowiconifyfun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> iconified);</div>
<div class="line"><a id="l01718" name="l01718"></a><span class="lineno"> 1718</span> </div>
<div class="line"><a id="l01738" name="l01738"></a><span class="lineno"><a class="line" href="group__window.html#ga3017196fdaec33ac3e095765176c2a90"> 1738</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__window.html#ga3017196fdaec33ac3e095765176c2a90">GLFWwindowmaximizefun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> maximized);</div>
<div class="line"><a id="l01739" name="l01739"></a><span class="lineno"> 1739</span> </div>
<div class="line"><a id="l01759" name="l01759"></a><span class="lineno"><a class="line" href="group__window.html#gae18026e294dde685ed2e5f759533144d"> 1759</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__window.html#gae18026e294dde685ed2e5f759533144d">GLFWframebuffersizefun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> width, <span class="keywordtype">int</span> height);</div>
<div class="line"><a id="l01760" name="l01760"></a><span class="lineno"> 1760</span> </div>
<div class="line"><a id="l01780" name="l01780"></a><span class="lineno"><a class="line" href="group__window.html#ga77f288a2d04bb3c77c7d9615d08cf70e"> 1780</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__window.html#ga77f288a2d04bb3c77c7d9615d08cf70e">GLFWwindowcontentscalefun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">float</span> xscale, <span class="keywordtype">float</span> yscale);</div>
<div class="line"><a id="l01781" name="l01781"></a><span class="lineno"> 1781</span> </div>
<div class="line"><a id="l01806" name="l01806"></a><span class="lineno"><a class="line" href="group__input.html#ga0184dcb59f6d85d735503dcaae809727"> 1806</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__input.html#ga0184dcb59f6d85d735503dcaae809727">GLFWmousebuttonfun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> button, <span class="keywordtype">int</span> action, <span class="keywordtype">int</span> mods);</div>
<div class="line"><a id="l01807" name="l01807"></a><span class="lineno"> 1807</span> </div>
<div class="line"><a id="l01829" name="l01829"></a><span class="lineno"><a class="line" href="group__input.html#gad6fae41b3ac2e4209aaa87b596c57f68"> 1829</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__input.html#gad6fae41b3ac2e4209aaa87b596c57f68">GLFWcursorposfun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">double</span> xpos, <span class="keywordtype">double</span> ypos);</div>
<div class="line"><a id="l01830" name="l01830"></a><span class="lineno"> 1830</span> </div>
<div class="line"><a id="l01850" name="l01850"></a><span class="lineno"><a class="line" href="group__input.html#gaa93dc4818ac9ab32532909d53a337cbe"> 1850</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__input.html#gaa93dc4818ac9ab32532909d53a337cbe">GLFWcursorenterfun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> entered);</div>
<div class="line"><a id="l01851" name="l01851"></a><span class="lineno"> 1851</span> </div>
<div class="line"><a id="l01871" name="l01871"></a><span class="lineno"><a class="line" href="group__input.html#gaf656112c33de3efdb227fa58f0134cf5"> 1871</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__input.html#gaf656112c33de3efdb227fa58f0134cf5">GLFWscrollfun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">double</span> xoffset, <span class="keywordtype">double</span> yoffset);</div>
<div class="line"><a id="l01872" name="l01872"></a><span class="lineno"> 1872</span> </div>
<div class="line"><a id="l01897" name="l01897"></a><span class="lineno"><a class="line" href="group__input.html#ga5bd751b27b90f865d2ea613533f0453c"> 1897</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__input.html#ga5bd751b27b90f865d2ea613533f0453c">GLFWkeyfun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> key, <span class="keywordtype">int</span> scancode, <span class="keywordtype">int</span> action, <span class="keywordtype">int</span> mods);</div>
<div class="line"><a id="l01898" name="l01898"></a><span class="lineno"> 1898</span> </div>
<div class="line"><a id="l01918" name="l01918"></a><span class="lineno"><a class="line" href="group__input.html#ga1ab90a55cf3f58639b893c0f4118cb6e"> 1918</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__input.html#ga1ab90a55cf3f58639b893c0f4118cb6e">GLFWcharfun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> codepoint);</div>
<div class="line"><a id="l01919" name="l01919"></a><span class="lineno"> 1919</span> </div>
<div class="line"><a id="l01945" name="l01945"></a><span class="lineno"><a class="line" href="group__input.html#gac3cf64f90b6219c05ac7b7822d5a4b8f"> 1945</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__input.html#gac3cf64f90b6219c05ac7b7822d5a4b8f">GLFWcharmodsfun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> codepoint, <span class="keywordtype">int</span> mods);</div>
<div class="line"><a id="l01946" name="l01946"></a><span class="lineno"> 1946</span> </div>
<div class="line"><a id="l01969" name="l01969"></a><span class="lineno"><a class="line" href="group__input.html#gaaba73c3274062c18723b7f05862d94b2"> 1969</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__input.html#gaaba73c3274062c18723b7f05862d94b2">GLFWdropfun</a>)(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> path_count, <span class="keyword">const</span> <span class="keywordtype">char</span>* paths[]);</div>
<div class="line"><a id="l01970" name="l01970"></a><span class="lineno"> 1970</span> </div>
<div class="line"><a id="l01990" name="l01990"></a><span class="lineno"><a class="line" href="group__monitor.html#gaabe16caca8dea952504dfdebdf4cd249"> 1990</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__monitor.html#gaabe16caca8dea952504dfdebdf4cd249">GLFWmonitorfun</a>)(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor, <span class="keywordtype">int</span> event);</div>
<div class="line"><a id="l01991" name="l01991"></a><span class="lineno"> 1991</span> </div>
<div class="line"><a id="l02011" name="l02011"></a><span class="lineno"><a class="line" href="group__input.html#gaa21ad5986ae9a26077a40142efb56243"> 2011</a></span><span class="keyword">typedef</span> void (* <a class="code hl_typedef" href="group__input.html#gaa21ad5986ae9a26077a40142efb56243">GLFWjoystickfun</a>)(<span class="keywordtype">int</span> jid, <span class="keywordtype">int</span> event);</div>
<div class="line"><a id="l02012" name="l02012"></a><span class="lineno"> 2012</span> </div>
<div class="foldopen" id="foldopen02026" data-start="{" data-end="};">
<div class="line"><a id="l02026" name="l02026"></a><span class="lineno"><a class="line" href="struct_g_l_f_wvidmode.html"> 2026</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a></div>
<div class="line"><a id="l02027" name="l02027"></a><span class="lineno"> 2027</span>{</div>
<div class="line"><a id="l02030" name="l02030"></a><span class="lineno"><a class="line" href="struct_g_l_f_wvidmode.html#a698dcb200562051a7249cb6ae154c71d"> 2030</a></span>    <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_g_l_f_wvidmode.html#a698dcb200562051a7249cb6ae154c71d">width</a>;</div>
<div class="line"><a id="l02033" name="l02033"></a><span class="lineno"><a class="line" href="struct_g_l_f_wvidmode.html#ac65942a5f6981695517437a9d571d03c"> 2033</a></span>    <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_g_l_f_wvidmode.html#ac65942a5f6981695517437a9d571d03c">height</a>;</div>
<div class="line"><a id="l02036" name="l02036"></a><span class="lineno"><a class="line" href="struct_g_l_f_wvidmode.html#a6066c4ecd251098700062d3b735dba1b"> 2036</a></span>    <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_g_l_f_wvidmode.html#a6066c4ecd251098700062d3b735dba1b">redBits</a>;</div>
<div class="line"><a id="l02039" name="l02039"></a><span class="lineno"><a class="line" href="struct_g_l_f_wvidmode.html#a292fdd281f3485fb3ff102a5bda43faa"> 2039</a></span>    <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_g_l_f_wvidmode.html#a292fdd281f3485fb3ff102a5bda43faa">greenBits</a>;</div>
<div class="line"><a id="l02042" name="l02042"></a><span class="lineno"><a class="line" href="struct_g_l_f_wvidmode.html#af310977f58d2e3b188175b6e3d314047"> 2042</a></span>    <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_g_l_f_wvidmode.html#af310977f58d2e3b188175b6e3d314047">blueBits</a>;</div>
<div class="line"><a id="l02045" name="l02045"></a><span class="lineno"><a class="line" href="struct_g_l_f_wvidmode.html#a791bdd6c7697b09f7e9c97054bf05649"> 2045</a></span>    <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_g_l_f_wvidmode.html#a791bdd6c7697b09f7e9c97054bf05649">refreshRate</a>;</div>
<div class="line"><a id="l02046" name="l02046"></a><span class="lineno"><a class="line" href="group__monitor.html#ga902c2816ac9b34b757282daab59b2565"> 2046</a></span>} <a class="code hl_struct" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a>;</div>
</div>
<div class="line"><a id="l02047" name="l02047"></a><span class="lineno"> 2047</span> </div>
<div class="foldopen" id="foldopen02060" data-start="{" data-end="};">
<div class="line"><a id="l02060" name="l02060"></a><span class="lineno"><a class="line" href="struct_g_l_f_wgammaramp.html"> 2060</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a></div>
<div class="line"><a id="l02061" name="l02061"></a><span class="lineno"> 2061</span>{</div>
<div class="line"><a id="l02064" name="l02064"></a><span class="lineno"><a class="line" href="struct_g_l_f_wgammaramp.html#a2cce5d968734b685623eef913e635138"> 2064</a></span>    <span class="keywordtype">unsigned</span> <span class="keywordtype">short</span>* <a class="code hl_variable" href="struct_g_l_f_wgammaramp.html#a2cce5d968734b685623eef913e635138">red</a>;</div>
<div class="line"><a id="l02067" name="l02067"></a><span class="lineno"><a class="line" href="struct_g_l_f_wgammaramp.html#affccc6f5df47820b6562d709da3a5a3a"> 2067</a></span>    <span class="keywordtype">unsigned</span> <span class="keywordtype">short</span>* <a class="code hl_variable" href="struct_g_l_f_wgammaramp.html#affccc6f5df47820b6562d709da3a5a3a">green</a>;</div>
<div class="line"><a id="l02070" name="l02070"></a><span class="lineno"><a class="line" href="struct_g_l_f_wgammaramp.html#acf0c836d0efe29c392fe8d1a1042744b"> 2070</a></span>    <span class="keywordtype">unsigned</span> <span class="keywordtype">short</span>* <a class="code hl_variable" href="struct_g_l_f_wgammaramp.html#acf0c836d0efe29c392fe8d1a1042744b">blue</a>;</div>
<div class="line"><a id="l02073" name="l02073"></a><span class="lineno"><a class="line" href="struct_g_l_f_wgammaramp.html#ad620e1cffbff9a32c51bca46301b59a5"> 2073</a></span>    <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_g_l_f_wgammaramp.html#ad620e1cffbff9a32c51bca46301b59a5">size</a>;</div>
<div class="line"><a id="l02074" name="l02074"></a><span class="lineno"><a class="line" href="group__monitor.html#ga939cf093cb0af0498b7b54dc2e181404"> 2074</a></span>} <a class="code hl_struct" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a>;</div>
</div>
<div class="line"><a id="l02075" name="l02075"></a><span class="lineno"> 2075</span> </div>
<div class="foldopen" id="foldopen02089" data-start="{" data-end="};">
<div class="line"><a id="l02089" name="l02089"></a><span class="lineno"><a class="line" href="struct_g_l_f_wimage.html"> 2089</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="struct_g_l_f_wimage.html">GLFWimage</a></div>
<div class="line"><a id="l02090" name="l02090"></a><span class="lineno"> 2090</span>{</div>
<div class="line"><a id="l02093" name="l02093"></a><span class="lineno"><a class="line" href="struct_g_l_f_wimage.html#af6a71cc999fe6d3aea31dd7e9687d835"> 2093</a></span>    <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_g_l_f_wimage.html#af6a71cc999fe6d3aea31dd7e9687d835">width</a>;</div>
<div class="line"><a id="l02096" name="l02096"></a><span class="lineno"><a class="line" href="struct_g_l_f_wimage.html#a0b7d95368f0c80d5e5c9875057c7dbec"> 2096</a></span>    <span class="keywordtype">int</span> <a class="code hl_variable" href="struct_g_l_f_wimage.html#a0b7d95368f0c80d5e5c9875057c7dbec">height</a>;</div>
<div class="line"><a id="l02099" name="l02099"></a><span class="lineno"><a class="line" href="struct_g_l_f_wimage.html#a0c532a5c2bb715555279b7817daba0fb"> 2099</a></span>    <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>* <a class="code hl_variable" href="struct_g_l_f_wimage.html#a0c532a5c2bb715555279b7817daba0fb">pixels</a>;</div>
<div class="line"><a id="l02100" name="l02100"></a><span class="lineno"><a class="line" href="group__window.html#ga7cc0a09de172fa7250872046f8c4d2ca"> 2100</a></span>} <a class="code hl_struct" href="struct_g_l_f_wimage.html">GLFWimage</a>;</div>
</div>
<div class="line"><a id="l02101" name="l02101"></a><span class="lineno"> 2101</span> </div>
<div class="foldopen" id="foldopen02113" data-start="{" data-end="};">
<div class="line"><a id="l02113" name="l02113"></a><span class="lineno"><a class="line" href="struct_g_l_f_wgamepadstate.html"> 2113</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="struct_g_l_f_wgamepadstate.html">GLFWgamepadstate</a></div>
<div class="line"><a id="l02114" name="l02114"></a><span class="lineno"> 2114</span>{</div>
<div class="line"><a id="l02118" name="l02118"></a><span class="lineno"><a class="line" href="struct_g_l_f_wgamepadstate.html#a27e9896b51c65df15fba2c7139bfdb9a"> 2118</a></span>    <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> <a class="code hl_variable" href="struct_g_l_f_wgamepadstate.html#a27e9896b51c65df15fba2c7139bfdb9a">buttons</a>[15];</div>
<div class="line"><a id="l02122" name="l02122"></a><span class="lineno"><a class="line" href="struct_g_l_f_wgamepadstate.html#a8b2c8939b1d31458de5359998375c189"> 2122</a></span>    <span class="keywordtype">float</span> <a class="code hl_variable" href="struct_g_l_f_wgamepadstate.html#a8b2c8939b1d31458de5359998375c189">axes</a>[6];</div>
<div class="line"><a id="l02123" name="l02123"></a><span class="lineno"><a class="line" href="group__input.html#ga61acfb1f28f751438dd221225c5e725d"> 2123</a></span>} <a class="code hl_struct" href="struct_g_l_f_wgamepadstate.html">GLFWgamepadstate</a>;</div>
</div>
<div class="line"><a id="l02124" name="l02124"></a><span class="lineno"> 2124</span> </div>
<div class="foldopen" id="foldopen02137" data-start="{" data-end="};">
<div class="line"><a id="l02137" name="l02137"></a><span class="lineno"><a class="line" href="struct_g_l_f_wallocator.html"> 2137</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="struct_g_l_f_wallocator.html">GLFWallocator</a></div>
<div class="line"><a id="l02138" name="l02138"></a><span class="lineno"> 2138</span>{</div>
<div class="line"><a id="l02142" name="l02142"></a><span class="lineno"><a class="line" href="struct_g_l_f_wallocator.html#a18a798136f17a9cb105be18312193bf7"> 2142</a></span>    <a class="code hl_typedef" href="group__init.html#ga4306a564e9f60f4de8cc8f31731a3120">GLFWallocatefun</a> <a class="code hl_variable" href="struct_g_l_f_wallocator.html#a18a798136f17a9cb105be18312193bf7">allocate</a>;</div>
<div class="line"><a id="l02146" name="l02146"></a><span class="lineno"><a class="line" href="struct_g_l_f_wallocator.html#af5a674af9e170095b968f467233437be"> 2146</a></span>    <a class="code hl_typedef" href="group__init.html#ga3e88a829615d8efe8bec1746f7309c63">GLFWreallocatefun</a> <a class="code hl_variable" href="struct_g_l_f_wallocator.html#af5a674af9e170095b968f467233437be">reallocate</a>;</div>
<div class="line"><a id="l02150" name="l02150"></a><span class="lineno"><a class="line" href="struct_g_l_f_wallocator.html#ab74cf9a969e73e6eb65a6112a591a988"> 2150</a></span>    <a class="code hl_typedef" href="group__init.html#ga7181615eda94c4b07bd72bdcee39fa28">GLFWdeallocatefun</a> <a class="code hl_variable" href="struct_g_l_f_wallocator.html#ab74cf9a969e73e6eb65a6112a591a988">deallocate</a>;</div>
<div class="line"><a id="l02154" name="l02154"></a><span class="lineno"><a class="line" href="struct_g_l_f_wallocator.html#af6153be74dbaf7f0a7e8bd3bfc039910"> 2154</a></span>    <span class="keywordtype">void</span>* <a class="code hl_variable" href="struct_g_l_f_wallocator.html#af6153be74dbaf7f0a7e8bd3bfc039910">user</a>;</div>
<div class="line"><a id="l02155" name="l02155"></a><span class="lineno"><a class="line" href="group__init.html#ga145c57d7f2aeda0b704a5a4ba1d6104b"> 2155</a></span>} <a class="code hl_struct" href="struct_g_l_f_wallocator.html">GLFWallocator</a>;</div>
</div>
<div class="line"><a id="l02156" name="l02156"></a><span class="lineno"> 2156</span> </div>
<div class="line"><a id="l02157" name="l02157"></a><span class="lineno"> 2157</span> </div>
<div class="line"><a id="l02158" name="l02158"></a><span class="lineno"> 2158</span><span class="comment">/*************************************************************************</span></div>
<div class="line"><a id="l02159" name="l02159"></a><span class="lineno"> 2159</span><span class="comment"> * GLFW API functions</span></div>
<div class="line"><a id="l02160" name="l02160"></a><span class="lineno"> 2160</span><span class="comment"> *************************************************************************/</span></div>
<div class="line"><a id="l02161" name="l02161"></a><span class="lineno"> 2161</span> </div>
<div class="line"><a id="l02220" name="l02220"></a><span class="lineno"><a class="line" href="group__init.html#ga317aac130a235ab08c6db0834907d85e"> 2220</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l02221" name="l02221"></a><span class="lineno"> 2221</span> </div>
<div class="line"><a id="l02254" name="l02254"></a><span class="lineno"><a class="line" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901"> 2254</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l02255" name="l02255"></a><span class="lineno"> 2255</span> </div>
<div class="line"><a id="l02286" name="l02286"></a><span class="lineno"><a class="line" href="group__init.html#ga110fd1d3f0412822b4f1908c026f724a"> 2286</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__init.html#ga110fd1d3f0412822b4f1908c026f724a">glfwInitHint</a>(<span class="keywordtype">int</span> hint, <span class="keywordtype">int</span> value);</div>
<div class="line"><a id="l02287" name="l02287"></a><span class="lineno"> 2287</span> </div>
<div class="line"><a id="l02317" name="l02317"></a><span class="lineno"><a class="line" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6"> 2317</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a>(<span class="keyword">const</span> <a class="code hl_struct" href="struct_g_l_f_wallocator.html">GLFWallocator</a>* allocator);</div>
<div class="line"><a id="l02318" name="l02318"></a><span class="lineno"> 2318</span> </div>
<div class="line"><a id="l02319" name="l02319"></a><span class="lineno"> 2319</span><span class="preprocessor">#if defined(VK_VERSION_1_0)</span></div>
<div class="line"><a id="l02320" name="l02320"></a><span class="lineno"> 2320</span> </div>
<div class="line"><a id="l02363" name="l02363"></a><span class="lineno"><a class="line" href="group__init.html#ga76af552d0307bb5f7791f245417d4752"> 2363</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__init.html#ga76af552d0307bb5f7791f245417d4752">glfwInitVulkanLoader</a>(PFN_vkGetInstanceProcAddr loader);</div>
<div class="line"><a id="l02364" name="l02364"></a><span class="lineno"> 2364</span> </div>
<div class="line"><a id="l02365" name="l02365"></a><span class="lineno"> 2365</span><span class="preprocessor">#endif </span><span class="comment">/*VK_VERSION_1_0*/</span><span class="preprocessor"></span></div>
<div class="line"><a id="l02366" name="l02366"></a><span class="lineno"> 2366</span> </div>
<div class="line"><a id="l02392" name="l02392"></a><span class="lineno"><a class="line" href="group__init.html#ga9f8ffaacf3c269cc48eafbf8b9b71197"> 2392</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__init.html#ga9f8ffaacf3c269cc48eafbf8b9b71197">glfwGetVersion</a>(<span class="keywordtype">int</span>* major, <span class="keywordtype">int</span>* minor, <span class="keywordtype">int</span>* rev);</div>
<div class="line"><a id="l02393" name="l02393"></a><span class="lineno"> 2393</span> </div>
<div class="line"><a id="l02426" name="l02426"></a><span class="lineno"><a class="line" href="group__init.html#ga026abd003c8e6501981ab1662062f1c0"> 2426</a></span>GLFWAPI <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="group__init.html#ga026abd003c8e6501981ab1662062f1c0">glfwGetVersionString</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l02427" name="l02427"></a><span class="lineno"> 2427</span> </div>
<div class="line"><a id="l02457" name="l02457"></a><span class="lineno"><a class="line" href="group__init.html#ga944986b4ec0b928d488141f92982aa18"> 2457</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__init.html#ga944986b4ec0b928d488141f92982aa18">glfwGetError</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>** description);</div>
<div class="line"><a id="l02458" name="l02458"></a><span class="lineno"> 2458</span> </div>
<div class="line"><a id="l02503" name="l02503"></a><span class="lineno"><a class="line" href="group__init.html#gaff45816610d53f0b83656092a4034f40"> 2503</a></span>GLFWAPI <a class="code hl_typedef" href="group__init.html#ga8184701785c096b3862a75cda1bf44a3">GLFWerrorfun</a> <a class="code hl_function" href="group__init.html#gaff45816610d53f0b83656092a4034f40">glfwSetErrorCallback</a>(<a class="code hl_typedef" href="group__init.html#ga8184701785c096b3862a75cda1bf44a3">GLFWerrorfun</a> callback);</div>
<div class="line"><a id="l02504" name="l02504"></a><span class="lineno"> 2504</span> </div>
<div class="line"><a id="l02524" name="l02524"></a><span class="lineno"><a class="line" href="group__init.html#ga6d6a983d38bd4e8fd786d7a9061d399e"> 2524</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__init.html#ga6d6a983d38bd4e8fd786d7a9061d399e">glfwGetPlatform</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l02525" name="l02525"></a><span class="lineno"> 2525</span> </div>
<div class="line"><a id="l02548" name="l02548"></a><span class="lineno"><a class="line" href="group__init.html#ga8785d2b6b36632368d803e78079d38ed"> 2548</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__init.html#ga8785d2b6b36632368d803e78079d38ed">glfwPlatformSupported</a>(<span class="keywordtype">int</span> platform);</div>
<div class="line"><a id="l02549" name="l02549"></a><span class="lineno"> 2549</span> </div>
<div class="line"><a id="l02577" name="l02577"></a><span class="lineno"><a class="line" href="group__monitor.html#ga70b1156d5d24e9928f145d6c864369d2"> 2577</a></span>GLFWAPI <a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>** <a class="code hl_function" href="group__monitor.html#ga70b1156d5d24e9928f145d6c864369d2">glfwGetMonitors</a>(<span class="keywordtype">int</span>* count);</div>
<div class="line"><a id="l02578" name="l02578"></a><span class="lineno"> 2578</span> </div>
<div class="line"><a id="l02601" name="l02601"></a><span class="lineno"><a class="line" href="group__monitor.html#gac3adb24947eb709e1874028272e5dfc5"> 2601</a></span>GLFWAPI <a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* <a class="code hl_function" href="group__monitor.html#gac3adb24947eb709e1874028272e5dfc5">glfwGetPrimaryMonitor</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l02602" name="l02602"></a><span class="lineno"> 2602</span> </div>
<div class="line"><a id="l02626" name="l02626"></a><span class="lineno"><a class="line" href="group__monitor.html#ga102f54e7acc9149edbcf0997152df8c9"> 2626</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__monitor.html#ga102f54e7acc9149edbcf0997152df8c9">glfwGetMonitorPos</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor, <span class="keywordtype">int</span>* xpos, <span class="keywordtype">int</span>* ypos);</div>
<div class="line"><a id="l02627" name="l02627"></a><span class="lineno"> 2627</span> </div>
<div class="line"><a id="l02657" name="l02657"></a><span class="lineno"><a class="line" href="group__monitor.html#ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0"> 2657</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__monitor.html#ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0">glfwGetMonitorWorkarea</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor, <span class="keywordtype">int</span>* xpos, <span class="keywordtype">int</span>* ypos, <span class="keywordtype">int</span>* width, <span class="keywordtype">int</span>* height);</div>
<div class="line"><a id="l02658" name="l02658"></a><span class="lineno"> 2658</span> </div>
<div class="line"><a id="l02692" name="l02692"></a><span class="lineno"><a class="line" href="group__monitor.html#ga7d8bffc6c55539286a6bd20d32a8d7ea"> 2692</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__monitor.html#ga7d8bffc6c55539286a6bd20d32a8d7ea">glfwGetMonitorPhysicalSize</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor, <span class="keywordtype">int</span>* widthMM, <span class="keywordtype">int</span>* heightMM);</div>
<div class="line"><a id="l02693" name="l02693"></a><span class="lineno"> 2693</span> </div>
<div class="line"><a id="l02727" name="l02727"></a><span class="lineno"><a class="line" href="group__monitor.html#gad3152e84465fa620b601265ebfcdb21b"> 2727</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__monitor.html#gad3152e84465fa620b601265ebfcdb21b">glfwGetMonitorContentScale</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor, <span class="keywordtype">float</span>* xscale, <span class="keywordtype">float</span>* yscale);</div>
<div class="line"><a id="l02728" name="l02728"></a><span class="lineno"> 2728</span> </div>
<div class="line"><a id="l02753" name="l02753"></a><span class="lineno"><a class="line" href="group__monitor.html#ga7af83e13489d90379588fb331b9e4b68"> 2753</a></span>GLFWAPI <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="group__monitor.html#ga7af83e13489d90379588fb331b9e4b68">glfwGetMonitorName</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor);</div>
<div class="line"><a id="l02754" name="l02754"></a><span class="lineno"> 2754</span> </div>
<div class="line"><a id="l02779" name="l02779"></a><span class="lineno"><a class="line" href="group__monitor.html#ga702750e24313a686d3637297b6e85fda"> 2779</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__monitor.html#ga702750e24313a686d3637297b6e85fda">glfwSetMonitorUserPointer</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor, <span class="keywordtype">void</span>* pointer);</div>
<div class="line"><a id="l02780" name="l02780"></a><span class="lineno"> 2780</span> </div>
<div class="line"><a id="l02803" name="l02803"></a><span class="lineno"><a class="line" href="group__monitor.html#ga1adbfbfb8cd58b23cfee82e574fbbdc5"> 2803</a></span>GLFWAPI <span class="keywordtype">void</span>* <a class="code hl_function" href="group__monitor.html#ga1adbfbfb8cd58b23cfee82e574fbbdc5">glfwGetMonitorUserPointer</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor);</div>
<div class="line"><a id="l02804" name="l02804"></a><span class="lineno"> 2804</span> </div>
<div class="line"><a id="l02833" name="l02833"></a><span class="lineno"><a class="line" href="group__monitor.html#gab39df645587c8518192aa746c2fb06c3"> 2833</a></span>GLFWAPI <a class="code hl_typedef" href="group__monitor.html#gaabe16caca8dea952504dfdebdf4cd249">GLFWmonitorfun</a> <a class="code hl_function" href="group__monitor.html#gab39df645587c8518192aa746c2fb06c3">glfwSetMonitorCallback</a>(<a class="code hl_typedef" href="group__monitor.html#gaabe16caca8dea952504dfdebdf4cd249">GLFWmonitorfun</a> callback);</div>
<div class="line"><a id="l02834" name="l02834"></a><span class="lineno"> 2834</span> </div>
<div class="line"><a id="l02867" name="l02867"></a><span class="lineno"><a class="line" href="group__monitor.html#gad2e24d2843cb7d6c26202cddd530fc1b"> 2867</a></span>GLFWAPI <span class="keyword">const</span> <a class="code hl_struct" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a>* <a class="code hl_function" href="group__monitor.html#gad2e24d2843cb7d6c26202cddd530fc1b">glfwGetVideoModes</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor, <span class="keywordtype">int</span>* count);</div>
<div class="line"><a id="l02868" name="l02868"></a><span class="lineno"> 2868</span> </div>
<div class="line"><a id="l02895" name="l02895"></a><span class="lineno"><a class="line" href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5"> 2895</a></span>GLFWAPI <span class="keyword">const</span> <a class="code hl_struct" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a>* <a class="code hl_function" href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor);</div>
<div class="line"><a id="l02896" name="l02896"></a><span class="lineno"> 2896</span> </div>
<div class="line"><a id="l02928" name="l02928"></a><span class="lineno"><a class="line" href="group__monitor.html#ga6ac582625c990220785ddd34efa3169a"> 2928</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__monitor.html#ga6ac582625c990220785ddd34efa3169a">glfwSetGamma</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor, <span class="keywordtype">float</span> gamma);</div>
<div class="line"><a id="l02929" name="l02929"></a><span class="lineno"> 2929</span> </div>
<div class="line"><a id="l02958" name="l02958"></a><span class="lineno"><a class="line" href="group__monitor.html#ga76ba90debcf0062b5c4b73052b24f96f"> 2958</a></span>GLFWAPI <span class="keyword">const</span> <a class="code hl_struct" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a>* <a class="code hl_function" href="group__monitor.html#ga76ba90debcf0062b5c4b73052b24f96f">glfwGetGammaRamp</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor);</div>
<div class="line"><a id="l02959" name="l02959"></a><span class="lineno"> 2959</span> </div>
<div class="line"><a id="l02999" name="l02999"></a><span class="lineno"><a class="line" href="group__monitor.html#ga583f0ffd0d29613d8cd172b996bbf0dd"> 2999</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__monitor.html#ga583f0ffd0d29613d8cd172b996bbf0dd">glfwSetGammaRamp</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor, <span class="keyword">const</span> <a class="code hl_struct" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a>* ramp);</div>
<div class="line"><a id="l03000" name="l03000"></a><span class="lineno"> 3000</span> </div>
<div class="line"><a id="l03018" name="l03018"></a><span class="lineno"><a class="line" href="group__window.html#gaa77c4898dfb83344a6b4f76aa16b9a4a"> 3018</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#gaa77c4898dfb83344a6b4f76aa16b9a4a">glfwDefaultWindowHints</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l03019" name="l03019"></a><span class="lineno"> 3019</span> </div>
<div class="line"><a id="l03053" name="l03053"></a><span class="lineno"><a class="line" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033"> 3053</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<span class="keywordtype">int</span> hint, <span class="keywordtype">int</span> value);</div>
<div class="line"><a id="l03054" name="l03054"></a><span class="lineno"> 3054</span> </div>
<div class="line"><a id="l03091" name="l03091"></a><span class="lineno"><a class="line" href="group__window.html#ga8cb2782861c9d997bcf2dea97f363e5f"> 3091</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga8cb2782861c9d997bcf2dea97f363e5f">glfwWindowHintString</a>(<span class="keywordtype">int</span> hint, <span class="keyword">const</span> <span class="keywordtype">char</span>* value);</div>
<div class="line"><a id="l03092" name="l03092"></a><span class="lineno"> 3092</span> </div>
<div class="line"><a id="l03235" name="l03235"></a><span class="lineno"><a class="line" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb"> 3235</a></span>GLFWAPI <a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* <a class="code hl_function" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>(<span class="keywordtype">int</span> width, <span class="keywordtype">int</span> height, <span class="keyword">const</span> <span class="keywordtype">char</span>* title, <a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor, <a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* share);</div>
<div class="line"><a id="l03236" name="l03236"></a><span class="lineno"> 3236</span> </div>
<div class="line"><a id="l03264" name="l03264"></a><span class="lineno"><a class="line" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2"> 3264</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l03265" name="l03265"></a><span class="lineno"> 3265</span> </div>
<div class="line"><a id="l03284" name="l03284"></a><span class="lineno"><a class="line" href="group__window.html#ga24e02fbfefbb81fc45320989f8140ab5"> 3284</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__window.html#ga24e02fbfefbb81fc45320989f8140ab5">glfwWindowShouldClose</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l03285" name="l03285"></a><span class="lineno"> 3285</span> </div>
<div class="line"><a id="l03306" name="l03306"></a><span class="lineno"><a class="line" href="group__window.html#ga49c449dde2a6f87d996f4daaa09d6708"> 3306</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga49c449dde2a6f87d996f4daaa09d6708">glfwSetWindowShouldClose</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> value);</div>
<div class="line"><a id="l03307" name="l03307"></a><span class="lineno"> 3307</span> </div>
<div class="line"><a id="l03338" name="l03338"></a><span class="lineno"><a class="line" href="group__window.html#gac6151765c54b789c4fe66c6bc6215953"> 3338</a></span>GLFWAPI <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="group__window.html#gac6151765c54b789c4fe66c6bc6215953">glfwGetWindowTitle</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l03339" name="l03339"></a><span class="lineno"> 3339</span> </div>
<div class="line"><a id="l03364" name="l03364"></a><span class="lineno"><a class="line" href="group__window.html#ga5d877f09e968cef7a360b513306f17ff"> 3364</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga5d877f09e968cef7a360b513306f17ff">glfwSetWindowTitle</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keyword">const</span> <span class="keywordtype">char</span>* title);</div>
<div class="line"><a id="l03365" name="l03365"></a><span class="lineno"> 3365</span> </div>
<div class="line"><a id="l03413" name="l03413"></a><span class="lineno"><a class="line" href="group__window.html#gadd7ccd39fe7a7d1f0904666ae5932dc5"> 3413</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#gadd7ccd39fe7a7d1f0904666ae5932dc5">glfwSetWindowIcon</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> count, <span class="keyword">const</span> <a class="code hl_struct" href="struct_g_l_f_wimage.html">GLFWimage</a>* images);</div>
<div class="line"><a id="l03414" name="l03414"></a><span class="lineno"> 3414</span> </div>
<div class="line"><a id="l03445" name="l03445"></a><span class="lineno"><a class="line" href="group__window.html#ga73cb526c000876fd8ddf571570fdb634"> 3445</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga73cb526c000876fd8ddf571570fdb634">glfwGetWindowPos</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span>* xpos, <span class="keywordtype">int</span>* ypos);</div>
<div class="line"><a id="l03446" name="l03446"></a><span class="lineno"> 3446</span> </div>
<div class="line"><a id="l03480" name="l03480"></a><span class="lineno"><a class="line" href="group__window.html#ga1abb6d690e8c88e0c8cd1751356dbca8"> 3480</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga1abb6d690e8c88e0c8cd1751356dbca8">glfwSetWindowPos</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> xpos, <span class="keywordtype">int</span> ypos);</div>
<div class="line"><a id="l03481" name="l03481"></a><span class="lineno"> 3481</span> </div>
<div class="line"><a id="l03510" name="l03510"></a><span class="lineno"><a class="line" href="group__window.html#gaeea7cbc03373a41fb51cfbf9f2a5d4c6"> 3510</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#gaeea7cbc03373a41fb51cfbf9f2a5d4c6">glfwGetWindowSize</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span>* width, <span class="keywordtype">int</span>* height);</div>
<div class="line"><a id="l03511" name="l03511"></a><span class="lineno"> 3511</span> </div>
<div class="line"><a id="l03553" name="l03553"></a><span class="lineno"><a class="line" href="group__window.html#gac314fa6cec7d2d307be9963e2709cc90"> 3553</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#gac314fa6cec7d2d307be9963e2709cc90">glfwSetWindowSizeLimits</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> minwidth, <span class="keywordtype">int</span> minheight, <span class="keywordtype">int</span> maxwidth, <span class="keywordtype">int</span> maxheight);</div>
<div class="line"><a id="l03554" name="l03554"></a><span class="lineno"> 3554</span> </div>
<div class="line"><a id="l03596" name="l03596"></a><span class="lineno"><a class="line" href="group__window.html#ga72ac8cb1ee2e312a878b55153d81b937"> 3596</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga72ac8cb1ee2e312a878b55153d81b937">glfwSetWindowAspectRatio</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> numer, <span class="keywordtype">int</span> denom);</div>
<div class="line"><a id="l03597" name="l03597"></a><span class="lineno"> 3597</span> </div>
<div class="line"><a id="l03634" name="l03634"></a><span class="lineno"><a class="line" href="group__window.html#ga371911f12c74c504dd8d47d832d095cb"> 3634</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga371911f12c74c504dd8d47d832d095cb">glfwSetWindowSize</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> width, <span class="keywordtype">int</span> height);</div>
<div class="line"><a id="l03635" name="l03635"></a><span class="lineno"> 3635</span> </div>
<div class="line"><a id="l03663" name="l03663"></a><span class="lineno"><a class="line" href="group__window.html#ga0e2637a4161afb283f5300c7f94785c9"> 3663</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga0e2637a4161afb283f5300c7f94785c9">glfwGetFramebufferSize</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span>* width, <span class="keywordtype">int</span>* height);</div>
<div class="line"><a id="l03664" name="l03664"></a><span class="lineno"> 3664</span> </div>
<div class="line"><a id="l03700" name="l03700"></a><span class="lineno"><a class="line" href="group__window.html#ga1a9fd382058c53101b21cf211898f1f1"> 3700</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga1a9fd382058c53101b21cf211898f1f1">glfwGetWindowFrameSize</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span>* left, <span class="keywordtype">int</span>* top, <span class="keywordtype">int</span>* right, <span class="keywordtype">int</span>* bottom);</div>
<div class="line"><a id="l03701" name="l03701"></a><span class="lineno"> 3701</span> </div>
<div class="line"><a id="l03733" name="l03733"></a><span class="lineno"><a class="line" href="group__window.html#gaf5d31de9c19c4f994facea64d2b3106c"> 3733</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#gaf5d31de9c19c4f994facea64d2b3106c">glfwGetWindowContentScale</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">float</span>* xscale, <span class="keywordtype">float</span>* yscale);</div>
<div class="line"><a id="l03734" name="l03734"></a><span class="lineno"> 3734</span> </div>
<div class="line"><a id="l03760" name="l03760"></a><span class="lineno"><a class="line" href="group__window.html#gad09f0bd7a6307c4533b7061828480a84"> 3760</a></span>GLFWAPI <span class="keywordtype">float</span> <a class="code hl_function" href="group__window.html#gad09f0bd7a6307c4533b7061828480a84">glfwGetWindowOpacity</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l03761" name="l03761"></a><span class="lineno"> 3761</span> </div>
<div class="line"><a id="l03792" name="l03792"></a><span class="lineno"><a class="line" href="group__window.html#gac31caeb3d1088831b13d2c8a156802e9"> 3792</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#gac31caeb3d1088831b13d2c8a156802e9">glfwSetWindowOpacity</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">float</span> opacity);</div>
<div class="line"><a id="l03793" name="l03793"></a><span class="lineno"> 3793</span> </div>
<div class="line"><a id="l03824" name="l03824"></a><span class="lineno"><a class="line" href="group__window.html#ga1bb559c0ebaad63c5c05ad2a066779c4"> 3824</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga1bb559c0ebaad63c5c05ad2a066779c4">glfwIconifyWindow</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l03825" name="l03825"></a><span class="lineno"> 3825</span> </div>
<div class="line"><a id="l03851" name="l03851"></a><span class="lineno"><a class="line" href="group__window.html#ga52527a5904b47d802b6b4bb519cdebc7"> 3851</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga52527a5904b47d802b6b4bb519cdebc7">glfwRestoreWindow</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l03852" name="l03852"></a><span class="lineno"> 3852</span> </div>
<div class="line"><a id="l03876" name="l03876"></a><span class="lineno"><a class="line" href="group__window.html#ga3f541387449d911274324ae7f17ec56b"> 3876</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga3f541387449d911274324ae7f17ec56b">glfwMaximizeWindow</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l03877" name="l03877"></a><span class="lineno"> 3877</span> </div>
<div class="line"><a id="l03908" name="l03908"></a><span class="lineno"><a class="line" href="group__window.html#ga61be47917b72536a148300f46494fc66"> 3908</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga61be47917b72536a148300f46494fc66">glfwShowWindow</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l03909" name="l03909"></a><span class="lineno"> 3909</span> </div>
<div class="line"><a id="l03930" name="l03930"></a><span class="lineno"><a class="line" href="group__window.html#ga49401f82a1ba5f15db5590728314d47c"> 3930</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga49401f82a1ba5f15db5590728314d47c">glfwHideWindow</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l03931" name="l03931"></a><span class="lineno"> 3931</span> </div>
<div class="line"><a id="l03969" name="l03969"></a><span class="lineno"><a class="line" href="group__window.html#ga873780357abd3f3a081d71a40aae45a1"> 3969</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga873780357abd3f3a081d71a40aae45a1">glfwFocusWindow</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l03970" name="l03970"></a><span class="lineno"> 3970</span> </div>
<div class="line"><a id="l03996" name="l03996"></a><span class="lineno"><a class="line" href="group__window.html#ga2f8d59323fc4692c1d54ba08c863a703"> 3996</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga2f8d59323fc4692c1d54ba08c863a703">glfwRequestWindowAttention</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l03997" name="l03997"></a><span class="lineno"> 3997</span> </div>
<div class="line"><a id="l04018" name="l04018"></a><span class="lineno"><a class="line" href="group__window.html#ga4d766499ac02c60f02221a9dfab87299"> 4018</a></span>GLFWAPI <a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* <a class="code hl_function" href="group__window.html#ga4d766499ac02c60f02221a9dfab87299">glfwGetWindowMonitor</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l04019" name="l04019"></a><span class="lineno"> 4019</span> </div>
<div class="line"><a id="l04074" name="l04074"></a><span class="lineno"><a class="line" href="group__window.html#ga81c76c418af80a1cce7055bccb0ae0a7"> 4074</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga81c76c418af80a1cce7055bccb0ae0a7">glfwSetWindowMonitor</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor, <span class="keywordtype">int</span> xpos, <span class="keywordtype">int</span> ypos, <span class="keywordtype">int</span> width, <span class="keywordtype">int</span> height, <span class="keywordtype">int</span> refreshRate);</div>
<div class="line"><a id="l04075" name="l04075"></a><span class="lineno"> 4075</span> </div>
<div class="line"><a id="l04111" name="l04111"></a><span class="lineno"><a class="line" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337"> 4111</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> attrib);</div>
<div class="line"><a id="l04112" name="l04112"></a><span class="lineno"> 4112</span> </div>
<div class="line"><a id="l04153" name="l04153"></a><span class="lineno"><a class="line" href="group__window.html#gace2afda29b4116ec012e410a6819033e"> 4153</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#gace2afda29b4116ec012e410a6819033e">glfwSetWindowAttrib</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> attrib, <span class="keywordtype">int</span> value);</div>
<div class="line"><a id="l04154" name="l04154"></a><span class="lineno"> 4154</span> </div>
<div class="line"><a id="l04176" name="l04176"></a><span class="lineno"><a class="line" href="group__window.html#ga3d2fc6026e690ab31a13f78bc9fd3651"> 4176</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga3d2fc6026e690ab31a13f78bc9fd3651">glfwSetWindowUserPointer</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">void</span>* pointer);</div>
<div class="line"><a id="l04177" name="l04177"></a><span class="lineno"> 4177</span> </div>
<div class="line"><a id="l04197" name="l04197"></a><span class="lineno"><a class="line" href="group__window.html#gae77a4add0d2023ca21ff1443ced01653"> 4197</a></span>GLFWAPI <span class="keywordtype">void</span>* <a class="code hl_function" href="group__window.html#gae77a4add0d2023ca21ff1443ced01653">glfwGetWindowUserPointer</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l04198" name="l04198"></a><span class="lineno"> 4198</span> </div>
<div class="line"><a id="l04232" name="l04232"></a><span class="lineno"><a class="line" href="group__window.html#ga08bdfbba88934f9c4f92fd757979ac74"> 4232</a></span>GLFWAPI <a class="code hl_typedef" href="group__window.html#gabe287973a21a8f927cde4db06b8dcbe9">GLFWwindowposfun</a> <a class="code hl_function" href="group__window.html#ga08bdfbba88934f9c4f92fd757979ac74">glfwSetWindowPosCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__window.html#gabe287973a21a8f927cde4db06b8dcbe9">GLFWwindowposfun</a> callback);</div>
<div class="line"><a id="l04233" name="l04233"></a><span class="lineno"> 4233</span> </div>
<div class="line"><a id="l04264" name="l04264"></a><span class="lineno"><a class="line" href="group__window.html#gad91b8b047a0c4c6033c38853864c34f8"> 4264</a></span>GLFWAPI <a class="code hl_typedef" href="group__window.html#gaec0282944bb810f6f3163ec02da90350">GLFWwindowsizefun</a> <a class="code hl_function" href="group__window.html#gad91b8b047a0c4c6033c38853864c34f8">glfwSetWindowSizeCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__window.html#gaec0282944bb810f6f3163ec02da90350">GLFWwindowsizefun</a> callback);</div>
<div class="line"><a id="l04265" name="l04265"></a><span class="lineno"> 4265</span> </div>
<div class="line"><a id="l04304" name="l04304"></a><span class="lineno"><a class="line" href="group__window.html#gada646d775a7776a95ac000cfc1885331"> 4304</a></span>GLFWAPI <a class="code hl_typedef" href="group__window.html#gabf859b936d80961b7d39013a9694cc3e">GLFWwindowclosefun</a> <a class="code hl_function" href="group__window.html#gada646d775a7776a95ac000cfc1885331">glfwSetWindowCloseCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__window.html#gabf859b936d80961b7d39013a9694cc3e">GLFWwindowclosefun</a> callback);</div>
<div class="line"><a id="l04305" name="l04305"></a><span class="lineno"> 4305</span> </div>
<div class="line"><a id="l04340" name="l04340"></a><span class="lineno"><a class="line" href="group__window.html#ga1c5c7eb889c33c7f4d10dd35b327654e"> 4340</a></span>GLFWAPI <a class="code hl_typedef" href="group__window.html#ga431663a1427d2eb3a273bc398b6737b5">GLFWwindowrefreshfun</a> <a class="code hl_function" href="group__window.html#ga1c5c7eb889c33c7f4d10dd35b327654e">glfwSetWindowRefreshCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__window.html#ga431663a1427d2eb3a273bc398b6737b5">GLFWwindowrefreshfun</a> callback);</div>
<div class="line"><a id="l04341" name="l04341"></a><span class="lineno"> 4341</span> </div>
<div class="line"><a id="l04375" name="l04375"></a><span class="lineno"><a class="line" href="group__window.html#gac2d83c4a10f071baf841f6730528e66c"> 4375</a></span>GLFWAPI <a class="code hl_typedef" href="group__window.html#gabc58c47e9d93f6eb1862d615c3680f46">GLFWwindowfocusfun</a> <a class="code hl_function" href="group__window.html#gac2d83c4a10f071baf841f6730528e66c">glfwSetWindowFocusCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__window.html#gabc58c47e9d93f6eb1862d615c3680f46">GLFWwindowfocusfun</a> callback);</div>
<div class="line"><a id="l04376" name="l04376"></a><span class="lineno"> 4376</span> </div>
<div class="line"><a id="l04405" name="l04405"></a><span class="lineno"><a class="line" href="group__window.html#gac793e9efd255567b5fb8b445052cfd3e"> 4405</a></span>GLFWAPI <a class="code hl_typedef" href="group__window.html#ga35c658cccba236f26e7adee0e25f6a4f">GLFWwindowiconifyfun</a> <a class="code hl_function" href="group__window.html#gac793e9efd255567b5fb8b445052cfd3e">glfwSetWindowIconifyCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__window.html#ga35c658cccba236f26e7adee0e25f6a4f">GLFWwindowiconifyfun</a> callback);</div>
<div class="line"><a id="l04406" name="l04406"></a><span class="lineno"> 4406</span> </div>
<div class="line"><a id="l04435" name="l04435"></a><span class="lineno"><a class="line" href="group__window.html#gacbe64c339fbd94885e62145563b6dc93"> 4435</a></span>GLFWAPI <a class="code hl_typedef" href="group__window.html#ga3017196fdaec33ac3e095765176c2a90">GLFWwindowmaximizefun</a> <a class="code hl_function" href="group__window.html#gacbe64c339fbd94885e62145563b6dc93">glfwSetWindowMaximizeCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__window.html#ga3017196fdaec33ac3e095765176c2a90">GLFWwindowmaximizefun</a> callback);</div>
<div class="line"><a id="l04436" name="l04436"></a><span class="lineno"> 4436</span> </div>
<div class="line"><a id="l04465" name="l04465"></a><span class="lineno"><a class="line" href="group__window.html#gab3fb7c3366577daef18c0023e2a8591f"> 4465</a></span>GLFWAPI <a class="code hl_typedef" href="group__window.html#gae18026e294dde685ed2e5f759533144d">GLFWframebuffersizefun</a> <a class="code hl_function" href="group__window.html#gab3fb7c3366577daef18c0023e2a8591f">glfwSetFramebufferSizeCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__window.html#gae18026e294dde685ed2e5f759533144d">GLFWframebuffersizefun</a> callback);</div>
<div class="line"><a id="l04466" name="l04466"></a><span class="lineno"> 4466</span> </div>
<div class="line"><a id="l04496" name="l04496"></a><span class="lineno"><a class="line" href="group__window.html#gaf2832ebb5aa6c252a2d261de002c92d6"> 4496</a></span>GLFWAPI <a class="code hl_typedef" href="group__window.html#ga77f288a2d04bb3c77c7d9615d08cf70e">GLFWwindowcontentscalefun</a> <a class="code hl_function" href="group__window.html#gaf2832ebb5aa6c252a2d261de002c92d6">glfwSetWindowContentScaleCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__window.html#ga77f288a2d04bb3c77c7d9615d08cf70e">GLFWwindowcontentscalefun</a> callback);</div>
<div class="line"><a id="l04497" name="l04497"></a><span class="lineno"> 4497</span> </div>
<div class="line"><a id="l04534" name="l04534"></a><span class="lineno"><a class="line" href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832"> 4534</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l04535" name="l04535"></a><span class="lineno"> 4535</span> </div>
<div class="line"><a id="l04579" name="l04579"></a><span class="lineno"><a class="line" href="group__window.html#ga554e37d781f0a997656c26b2c56c835e"> 4579</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga554e37d781f0a997656c26b2c56c835e">glfwWaitEvents</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l04580" name="l04580"></a><span class="lineno"> 4580</span> </div>
<div class="line"><a id="l04628" name="l04628"></a><span class="lineno"><a class="line" href="group__window.html#ga605a178db92f1a7f1a925563ef3ea2cf"> 4628</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga605a178db92f1a7f1a925563ef3ea2cf">glfwWaitEventsTimeout</a>(<span class="keywordtype">double</span> timeout);</div>
<div class="line"><a id="l04629" name="l04629"></a><span class="lineno"> 4629</span> </div>
<div class="line"><a id="l04648" name="l04648"></a><span class="lineno"><a class="line" href="group__window.html#gab5997a25187e9fd5c6f2ecbbc8dfd7e9"> 4648</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#gab5997a25187e9fd5c6f2ecbbc8dfd7e9">glfwPostEmptyEvent</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l04649" name="l04649"></a><span class="lineno"> 4649</span> </div>
<div class="line"><a id="l04673" name="l04673"></a><span class="lineno"><a class="line" href="group__input.html#gaf5b859dbe19bdf434e42695ea45cc5f4"> 4673</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__input.html#gaf5b859dbe19bdf434e42695ea45cc5f4">glfwGetInputMode</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> mode);</div>
<div class="line"><a id="l04674" name="l04674"></a><span class="lineno"> 4674</span> </div>
<div class="line"><a id="l04738" name="l04738"></a><span class="lineno"><a class="line" href="group__input.html#gaa92336e173da9c8834558b54ee80563b"> 4738</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> mode, <span class="keywordtype">int</span> value);</div>
<div class="line"><a id="l04739" name="l04739"></a><span class="lineno"> 4739</span> </div>
<div class="line"><a id="l04767" name="l04767"></a><span class="lineno"><a class="line" href="group__input.html#gae4ee0dbd0d256183e1ea4026d897e1c2"> 4767</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__input.html#gae4ee0dbd0d256183e1ea4026d897e1c2">glfwRawMouseMotionSupported</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l04768" name="l04768"></a><span class="lineno"> 4768</span> </div>
<div class="line"><a id="l04835" name="l04835"></a><span class="lineno"><a class="line" href="group__input.html#gaeaed62e69c3bd62b7ff8f7b19913ce4f"> 4835</a></span>GLFWAPI <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="group__input.html#gaeaed62e69c3bd62b7ff8f7b19913ce4f">glfwGetKeyName</a>(<span class="keywordtype">int</span> key, <span class="keywordtype">int</span> scancode);</div>
<div class="line"><a id="l04836" name="l04836"></a><span class="lineno"> 4836</span> </div>
<div class="line"><a id="l04862" name="l04862"></a><span class="lineno"><a class="line" href="group__input.html#ga67ddd1b7dcbbaff03e4a76c0ea67103a"> 4862</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__input.html#ga67ddd1b7dcbbaff03e4a76c0ea67103a">glfwGetKeyScancode</a>(<span class="keywordtype">int</span> key);</div>
<div class="line"><a id="l04863" name="l04863"></a><span class="lineno"> 4863</span> </div>
<div class="line"><a id="l04901" name="l04901"></a><span class="lineno"><a class="line" href="group__input.html#gadd341da06bc8d418b4dc3a3518af9ad2"> 4901</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__input.html#gadd341da06bc8d418b4dc3a3518af9ad2">glfwGetKey</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> key);</div>
<div class="line"><a id="l04902" name="l04902"></a><span class="lineno"> 4902</span> </div>
<div class="line"><a id="l04930" name="l04930"></a><span class="lineno"><a class="line" href="group__input.html#gac1473feacb5996c01a7a5a33b5066704"> 4930</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__input.html#gac1473feacb5996c01a7a5a33b5066704">glfwGetMouseButton</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> button);</div>
<div class="line"><a id="l04931" name="l04931"></a><span class="lineno"> 4931</span> </div>
<div class="line"><a id="l04968" name="l04968"></a><span class="lineno"><a class="line" href="group__input.html#ga01d37b6c40133676b9cea60ca1d7c0cc"> 4968</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__input.html#ga01d37b6c40133676b9cea60ca1d7c0cc">glfwGetCursorPos</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">double</span>* xpos, <span class="keywordtype">double</span>* ypos);</div>
<div class="line"><a id="l04969" name="l04969"></a><span class="lineno"> 4969</span> </div>
<div class="line"><a id="l05008" name="l05008"></a><span class="lineno"><a class="line" href="group__input.html#ga04b03af936d906ca123c8f4ee08b39e7"> 5008</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__input.html#ga04b03af936d906ca123c8f4ee08b39e7">glfwSetCursorPos</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">double</span> xpos, <span class="keywordtype">double</span> ypos);</div>
<div class="line"><a id="l05009" name="l05009"></a><span class="lineno"> 5009</span> </div>
<div class="line"><a id="l05046" name="l05046"></a><span class="lineno"><a class="line" href="group__input.html#ga556f604f73af156c0db0e97c081373c3"> 5046</a></span>GLFWAPI <a class="code hl_typedef" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a>* <a class="code hl_function" href="group__input.html#ga556f604f73af156c0db0e97c081373c3">glfwCreateCursor</a>(<span class="keyword">const</span> <a class="code hl_struct" href="struct_g_l_f_wimage.html">GLFWimage</a>* image, <span class="keywordtype">int</span> xhot, <span class="keywordtype">int</span> yhot);</div>
<div class="line"><a id="l05047" name="l05047"></a><span class="lineno"> 5047</span> </div>
<div class="line"><a id="l05094" name="l05094"></a><span class="lineno"><a class="line" href="group__input.html#gaf2fb2eb2c9dd842d1cef8a34e3c6403e"> 5094</a></span>GLFWAPI <a class="code hl_typedef" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a>* <a class="code hl_function" href="group__input.html#gaf2fb2eb2c9dd842d1cef8a34e3c6403e">glfwCreateStandardCursor</a>(<span class="keywordtype">int</span> shape);</div>
<div class="line"><a id="l05095" name="l05095"></a><span class="lineno"> 5095</span> </div>
<div class="line"><a id="l05121" name="l05121"></a><span class="lineno"><a class="line" href="group__input.html#ga81b952cd1764274d0db7fb3c5a79ba6a"> 5121</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__input.html#ga81b952cd1764274d0db7fb3c5a79ba6a">glfwDestroyCursor</a>(<a class="code hl_typedef" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a>* cursor);</div>
<div class="line"><a id="l05122" name="l05122"></a><span class="lineno"> 5122</span> </div>
<div class="line"><a id="l05148" name="l05148"></a><span class="lineno"><a class="line" href="group__input.html#gad3b4f38c8d5dae036bc8fa959e18343e"> 5148</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__input.html#gad3b4f38c8d5dae036bc8fa959e18343e">glfwSetCursor</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a>* cursor);</div>
<div class="line"><a id="l05149" name="l05149"></a><span class="lineno"> 5149</span> </div>
<div class="line"><a id="l05198" name="l05198"></a><span class="lineno"><a class="line" href="group__input.html#ga1caf18159767e761185e49a3be019f8d"> 5198</a></span>GLFWAPI <a class="code hl_typedef" href="group__input.html#ga5bd751b27b90f865d2ea613533f0453c">GLFWkeyfun</a> <a class="code hl_function" href="group__input.html#ga1caf18159767e761185e49a3be019f8d">glfwSetKeyCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__input.html#ga5bd751b27b90f865d2ea613533f0453c">GLFWkeyfun</a> callback);</div>
<div class="line"><a id="l05199" name="l05199"></a><span class="lineno"> 5199</span> </div>
<div class="line"><a id="l05241" name="l05241"></a><span class="lineno"><a class="line" href="group__input.html#gab25c4a220fd8f5717718dbc487828996"> 5241</a></span>GLFWAPI <a class="code hl_typedef" href="group__input.html#ga1ab90a55cf3f58639b893c0f4118cb6e">GLFWcharfun</a> <a class="code hl_function" href="group__input.html#gab25c4a220fd8f5717718dbc487828996">glfwSetCharCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__input.html#ga1ab90a55cf3f58639b893c0f4118cb6e">GLFWcharfun</a> callback);</div>
<div class="line"><a id="l05242" name="l05242"></a><span class="lineno"> 5242</span> </div>
<div class="line"><a id="l05283" name="l05283"></a><span class="lineno"><a class="line" href="group__input.html#ga0b7f4ad13c2b17435ff13b6dcfb4e43c"> 5283</a></span>GLFWAPI <a class="code hl_typedef" href="group__input.html#gac3cf64f90b6219c05ac7b7822d5a4b8f">GLFWcharmodsfun</a> <a class="code hl_function" href="group__input.html#ga0b7f4ad13c2b17435ff13b6dcfb4e43c">glfwSetCharModsCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__input.html#gac3cf64f90b6219c05ac7b7822d5a4b8f">GLFWcharmodsfun</a> callback);</div>
<div class="line"><a id="l05284" name="l05284"></a><span class="lineno"> 5284</span> </div>
<div class="line"><a id="l05320" name="l05320"></a><span class="lineno"><a class="line" href="group__input.html#ga6ab84420974d812bee700e45284a723c"> 5320</a></span>GLFWAPI <a class="code hl_typedef" href="group__input.html#ga0184dcb59f6d85d735503dcaae809727">GLFWmousebuttonfun</a> <a class="code hl_function" href="group__input.html#ga6ab84420974d812bee700e45284a723c">glfwSetMouseButtonCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__input.html#ga0184dcb59f6d85d735503dcaae809727">GLFWmousebuttonfun</a> callback);</div>
<div class="line"><a id="l05321" name="l05321"></a><span class="lineno"> 5321</span> </div>
<div class="line"><a id="l05352" name="l05352"></a><span class="lineno"><a class="line" href="group__input.html#gac1f879ab7435d54d4d79bb469fe225d7"> 5352</a></span>GLFWAPI <a class="code hl_typedef" href="group__input.html#gad6fae41b3ac2e4209aaa87b596c57f68">GLFWcursorposfun</a> <a class="code hl_function" href="group__input.html#gac1f879ab7435d54d4d79bb469fe225d7">glfwSetCursorPosCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__input.html#gad6fae41b3ac2e4209aaa87b596c57f68">GLFWcursorposfun</a> callback);</div>
<div class="line"><a id="l05353" name="l05353"></a><span class="lineno"> 5353</span> </div>
<div class="line"><a id="l05383" name="l05383"></a><span class="lineno"><a class="line" href="group__input.html#gad27f8ad0142c038a281466c0966817d8"> 5383</a></span>GLFWAPI <a class="code hl_typedef" href="group__input.html#gaa93dc4818ac9ab32532909d53a337cbe">GLFWcursorenterfun</a> <a class="code hl_function" href="group__input.html#gad27f8ad0142c038a281466c0966817d8">glfwSetCursorEnterCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__input.html#gaa93dc4818ac9ab32532909d53a337cbe">GLFWcursorenterfun</a> callback);</div>
<div class="line"><a id="l05384" name="l05384"></a><span class="lineno"> 5384</span> </div>
<div class="line"><a id="l05417" name="l05417"></a><span class="lineno"><a class="line" href="group__input.html#ga571e45a030ae4061f746ed56cb76aede"> 5417</a></span>GLFWAPI <a class="code hl_typedef" href="group__input.html#gaf656112c33de3efdb227fa58f0134cf5">GLFWscrollfun</a> <a class="code hl_function" href="group__input.html#ga571e45a030ae4061f746ed56cb76aede">glfwSetScrollCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__input.html#gaf656112c33de3efdb227fa58f0134cf5">GLFWscrollfun</a> callback);</div>
<div class="line"><a id="l05418" name="l05418"></a><span class="lineno"> 5418</span> </div>
<div class="line"><a id="l05452" name="l05452"></a><span class="lineno"><a class="line" href="group__input.html#gab773f0ee0a07cff77a210cea40bc1f6b"> 5452</a></span>GLFWAPI <a class="code hl_typedef" href="group__input.html#gaaba73c3274062c18723b7f05862d94b2">GLFWdropfun</a> <a class="code hl_function" href="group__input.html#gab773f0ee0a07cff77a210cea40bc1f6b">glfwSetDropCallback</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <a class="code hl_typedef" href="group__input.html#gaaba73c3274062c18723b7f05862d94b2">GLFWdropfun</a> callback);</div>
<div class="line"><a id="l05453" name="l05453"></a><span class="lineno"> 5453</span> </div>
<div class="line"><a id="l05476" name="l05476"></a><span class="lineno"><a class="line" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1"> 5476</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a>(<span class="keywordtype">int</span> jid);</div>
<div class="line"><a id="l05477" name="l05477"></a><span class="lineno"> 5477</span> </div>
<div class="line"><a id="l05509" name="l05509"></a><span class="lineno"><a class="line" href="group__input.html#gaeb1c0191d3140a233a682987c61eb408"> 5509</a></span>GLFWAPI <span class="keyword">const</span> <span class="keywordtype">float</span>* <a class="code hl_function" href="group__input.html#gaeb1c0191d3140a233a682987c61eb408">glfwGetJoystickAxes</a>(<span class="keywordtype">int</span> jid, <span class="keywordtype">int</span>* count);</div>
<div class="line"><a id="l05510" name="l05510"></a><span class="lineno"> 5510</span> </div>
<div class="line"><a id="l05550" name="l05550"></a><span class="lineno"><a class="line" href="group__input.html#ga5ffe34739d3dc97efe432ed2d81d9938"> 5550</a></span>GLFWAPI <span class="keyword">const</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="group__input.html#ga5ffe34739d3dc97efe432ed2d81d9938">glfwGetJoystickButtons</a>(<span class="keywordtype">int</span> jid, <span class="keywordtype">int</span>* count);</div>
<div class="line"><a id="l05551" name="l05551"></a><span class="lineno"> 5551</span> </div>
<div class="line"><a id="l05607" name="l05607"></a><span class="lineno"><a class="line" href="group__input.html#ga06e660841b3e79c54da4f54a932c5a2c"> 5607</a></span>GLFWAPI <span class="keyword">const</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="group__input.html#ga06e660841b3e79c54da4f54a932c5a2c">glfwGetJoystickHats</a>(<span class="keywordtype">int</span> jid, <span class="keywordtype">int</span>* count);</div>
<div class="line"><a id="l05608" name="l05608"></a><span class="lineno"> 5608</span> </div>
<div class="line"><a id="l05638" name="l05638"></a><span class="lineno"><a class="line" href="group__input.html#gac6a8e769e18e0bcfa9097793fc2c3978"> 5638</a></span>GLFWAPI <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="group__input.html#gac6a8e769e18e0bcfa9097793fc2c3978">glfwGetJoystickName</a>(<span class="keywordtype">int</span> jid);</div>
<div class="line"><a id="l05639" name="l05639"></a><span class="lineno"> 5639</span> </div>
<div class="line"><a id="l05679" name="l05679"></a><span class="lineno"><a class="line" href="group__input.html#ga6659411aec3c7fcef27780e2cb2d9600"> 5679</a></span>GLFWAPI <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="group__input.html#ga6659411aec3c7fcef27780e2cb2d9600">glfwGetJoystickGUID</a>(<span class="keywordtype">int</span> jid);</div>
<div class="line"><a id="l05680" name="l05680"></a><span class="lineno"> 5680</span> </div>
<div class="line"><a id="l05705" name="l05705"></a><span class="lineno"><a class="line" href="group__input.html#ga6b2f72d64d636b48a727b437cbb7489e"> 5705</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__input.html#ga6b2f72d64d636b48a727b437cbb7489e">glfwSetJoystickUserPointer</a>(<span class="keywordtype">int</span> jid, <span class="keywordtype">void</span>* pointer);</div>
<div class="line"><a id="l05706" name="l05706"></a><span class="lineno"> 5706</span> </div>
<div class="line"><a id="l05729" name="l05729"></a><span class="lineno"><a class="line" href="group__input.html#ga18cefd7265d1fa04f3fd38a6746db5f3"> 5729</a></span>GLFWAPI <span class="keywordtype">void</span>* <a class="code hl_function" href="group__input.html#ga18cefd7265d1fa04f3fd38a6746db5f3">glfwGetJoystickUserPointer</a>(<span class="keywordtype">int</span> jid);</div>
<div class="line"><a id="l05730" name="l05730"></a><span class="lineno"> 5730</span> </div>
<div class="line"><a id="l05757" name="l05757"></a><span class="lineno"><a class="line" href="group__input.html#gad0f676860f329d80f7e47e9f06a96f00"> 5757</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__input.html#gad0f676860f329d80f7e47e9f06a96f00">glfwJoystickIsGamepad</a>(<span class="keywordtype">int</span> jid);</div>
<div class="line"><a id="l05758" name="l05758"></a><span class="lineno"> 5758</span> </div>
<div class="line"><a id="l05793" name="l05793"></a><span class="lineno"><a class="line" href="group__input.html#ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c"> 5793</a></span>GLFWAPI <a class="code hl_typedef" href="group__input.html#gaa21ad5986ae9a26077a40142efb56243">GLFWjoystickfun</a> <a class="code hl_function" href="group__input.html#ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c">glfwSetJoystickCallback</a>(<a class="code hl_typedef" href="group__input.html#gaa21ad5986ae9a26077a40142efb56243">GLFWjoystickfun</a> callback);</div>
<div class="line"><a id="l05794" name="l05794"></a><span class="lineno"> 5794</span> </div>
<div class="line"><a id="l05827" name="l05827"></a><span class="lineno"><a class="line" href="group__input.html#gaed5104612f2fa8e66aa6e846652ad00f"> 5827</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__input.html#gaed5104612f2fa8e66aa6e846652ad00f">glfwUpdateGamepadMappings</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* <span class="keywordtype">string</span>);</div>
<div class="line"><a id="l05828" name="l05828"></a><span class="lineno"> 5828</span> </div>
<div class="line"><a id="l05859" name="l05859"></a><span class="lineno"><a class="line" href="group__input.html#ga8aea73a1a25cc6c0486a617019f56728"> 5859</a></span>GLFWAPI <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="group__input.html#ga8aea73a1a25cc6c0486a617019f56728">glfwGetGamepadName</a>(<span class="keywordtype">int</span> jid);</div>
<div class="line"><a id="l05860" name="l05860"></a><span class="lineno"> 5860</span> </div>
<div class="line"><a id="l05897" name="l05897"></a><span class="lineno"><a class="line" href="group__input.html#gadccddea8bce6113fa459de379ddaf051"> 5897</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__input.html#gadccddea8bce6113fa459de379ddaf051">glfwGetGamepadState</a>(<span class="keywordtype">int</span> jid, <a class="code hl_struct" href="struct_g_l_f_wgamepadstate.html">GLFWgamepadstate</a>* state);</div>
<div class="line"><a id="l05898" name="l05898"></a><span class="lineno"> 5898</span> </div>
<div class="line"><a id="l05927" name="l05927"></a><span class="lineno"><a class="line" href="group__input.html#gaba1f022c5eb07dfac421df34cdcd31dd"> 5927</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__input.html#gaba1f022c5eb07dfac421df34cdcd31dd">glfwSetClipboardString</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keyword">const</span> <span class="keywordtype">char</span>* <span class="keywordtype">string</span>);</div>
<div class="line"><a id="l05928" name="l05928"></a><span class="lineno"> 5928</span> </div>
<div class="line"><a id="l05962" name="l05962"></a><span class="lineno"><a class="line" href="group__input.html#ga71a5b20808ea92193d65c21b82580355"> 5962</a></span>GLFWAPI <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="group__input.html#ga71a5b20808ea92193d65c21b82580355">glfwGetClipboardString</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l05963" name="l05963"></a><span class="lineno"> 5963</span> </div>
<div class="line"><a id="l05992" name="l05992"></a><span class="lineno"><a class="line" href="group__input.html#gaa6cf4e7a77158a3b8fd00328b1720a4a"> 5992</a></span>GLFWAPI <span class="keywordtype">double</span> <a class="code hl_function" href="group__input.html#gaa6cf4e7a77158a3b8fd00328b1720a4a">glfwGetTime</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l05993" name="l05993"></a><span class="lineno"> 5993</span> </div>
<div class="line"><a id="l06022" name="l06022"></a><span class="lineno"><a class="line" href="group__input.html#gaf59589ef6e8b8c8b5ad184b25afd4dc0"> 6022</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__input.html#gaf59589ef6e8b8c8b5ad184b25afd4dc0">glfwSetTime</a>(<span class="keywordtype">double</span> time);</div>
<div class="line"><a id="l06023" name="l06023"></a><span class="lineno"> 6023</span> </div>
<div class="line"><a id="l06044" name="l06044"></a><span class="lineno"><a class="line" href="group__input.html#ga09b2bd37d328e0b9456c7ec575cc26aa"> 6044</a></span>GLFWAPI uint64_t <a class="code hl_function" href="group__input.html#ga09b2bd37d328e0b9456c7ec575cc26aa">glfwGetTimerValue</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l06045" name="l06045"></a><span class="lineno"> 6045</span> </div>
<div class="line"><a id="l06064" name="l06064"></a><span class="lineno"><a class="line" href="group__input.html#ga3289ee876572f6e91f06df3a24824443"> 6064</a></span>GLFWAPI uint64_t <a class="code hl_function" href="group__input.html#ga3289ee876572f6e91f06df3a24824443">glfwGetTimerFrequency</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l06065" name="l06065"></a><span class="lineno"> 6065</span> </div>
<div class="line"><a id="l06109" name="l06109"></a><span class="lineno"><a class="line" href="group__context.html#ga1c04dc242268f827290fe40aa1c91157"> 6109</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__context.html#ga1c04dc242268f827290fe40aa1c91157">glfwMakeContextCurrent</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l06110" name="l06110"></a><span class="lineno"> 6110</span> </div>
<div class="line"><a id="l06130" name="l06130"></a><span class="lineno"><a class="line" href="group__context.html#gad94e80185397a6cf5fe2ab30567af71c"> 6130</a></span>GLFWAPI <a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* <a class="code hl_function" href="group__context.html#gad94e80185397a6cf5fe2ab30567af71c">glfwGetCurrentContext</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l06131" name="l06131"></a><span class="lineno"> 6131</span> </div>
<div class="line"><a id="l06164" name="l06164"></a><span class="lineno"><a class="line" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14"> 6164</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l06165" name="l06165"></a><span class="lineno"> 6165</span> </div>
<div class="line"><a id="l06210" name="l06210"></a><span class="lineno"><a class="line" href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed"> 6210</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed">glfwSwapInterval</a>(<span class="keywordtype">int</span> interval);</div>
<div class="line"><a id="l06211" name="l06211"></a><span class="lineno"> 6211</span> </div>
<div class="line"><a id="l06248" name="l06248"></a><span class="lineno"><a class="line" href="group__context.html#ga87425065c011cef1ebd6aac75e059dfa"> 6248</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__context.html#ga87425065c011cef1ebd6aac75e059dfa">glfwExtensionSupported</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* extension);</div>
<div class="line"><a id="l06249" name="l06249"></a><span class="lineno"> 6249</span> </div>
<div class="line"><a id="l06290" name="l06290"></a><span class="lineno"><a class="line" href="group__context.html#ga35f1837e6f666781842483937612f163"> 6290</a></span>GLFWAPI <a class="code hl_typedef" href="group__context.html#ga3d47c2d2fbe0be9c505d0e04e91a133c">GLFWglproc</a> <a class="code hl_function" href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* procname);</div>
<div class="line"><a id="l06291" name="l06291"></a><span class="lineno"> 6291</span> </div>
<div class="line"><a id="l06316" name="l06316"></a><span class="lineno"><a class="line" href="group__vulkan.html#ga2e7f30931e02464b5bc8d0d4b6f9fe2b"> 6316</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__vulkan.html#ga2e7f30931e02464b5bc8d0d4b6f9fe2b">glfwVulkanSupported</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l06317" name="l06317"></a><span class="lineno"> 6317</span> </div>
<div class="line"><a id="l06360" name="l06360"></a><span class="lineno"><a class="line" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6"> 6360</a></span>GLFWAPI <span class="keyword">const</span> <span class="keywordtype">char</span>** <a class="code hl_function" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a>(uint32_t* count);</div>
<div class="line"><a id="l06361" name="l06361"></a><span class="lineno"> 6361</span> </div>
<div class="line"><a id="l06362" name="l06362"></a><span class="lineno"> 6362</span><span class="preprocessor">#if defined(VK_VERSION_1_0)</span></div>
<div class="line"><a id="l06363" name="l06363"></a><span class="lineno"> 6363</span> </div>
<div class="line"><a id="l06403" name="l06403"></a><span class="lineno"><a class="line" href="group__vulkan.html#gadf228fac94c5fd8f12423ec9af9ff1e9"> 6403</a></span>GLFWAPI <a class="code hl_typedef" href="group__vulkan.html#ga70c01918dc9d233a4fbe0681a43018af">GLFWvkproc</a> <a class="code hl_function" href="group__vulkan.html#gadf228fac94c5fd8f12423ec9af9ff1e9">glfwGetInstanceProcAddress</a>(VkInstance instance, <span class="keyword">const</span> <span class="keywordtype">char</span>* procname);</div>
<div class="line"><a id="l06404" name="l06404"></a><span class="lineno"> 6404</span> </div>
<div class="line"><a id="l06440" name="l06440"></a><span class="lineno"><a class="line" href="group__vulkan.html#gaff3823355cdd7e2f3f9f4d9ea9518d92"> 6440</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__vulkan.html#gaff3823355cdd7e2f3f9f4d9ea9518d92">glfwGetPhysicalDevicePresentationSupport</a>(VkInstance instance, VkPhysicalDevice device, uint32_t queuefamily);</div>
<div class="line"><a id="l06441" name="l06441"></a><span class="lineno"> 6441</span> </div>
<div class="line"><a id="l06510" name="l06510"></a><span class="lineno"><a class="line" href="group__vulkan.html#ga1a24536bec3f80b08ead18e28e6ae965"> 6510</a></span>GLFWAPI VkResult <a class="code hl_function" href="group__vulkan.html#ga1a24536bec3f80b08ead18e28e6ae965">glfwCreateWindowSurface</a>(VkInstance instance, <a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keyword">const</span> VkAllocationCallbacks* allocator, VkSurfaceKHR* surface);</div>
<div class="line"><a id="l06511" name="l06511"></a><span class="lineno"> 6511</span> </div>
<div class="line"><a id="l06512" name="l06512"></a><span class="lineno"> 6512</span><span class="preprocessor">#endif </span><span class="comment">/*VK_VERSION_1_0*/</span><span class="preprocessor"></span></div>
<div class="line"><a id="l06513" name="l06513"></a><span class="lineno"> 6513</span> </div>
<div class="line"><a id="l06514" name="l06514"></a><span class="lineno"> 6514</span> </div>
<div class="line"><a id="l06515" name="l06515"></a><span class="lineno"> 6515</span><span class="comment">/*************************************************************************</span></div>
<div class="line"><a id="l06516" name="l06516"></a><span class="lineno"> 6516</span><span class="comment"> * Global definition cleanup</span></div>
<div class="line"><a id="l06517" name="l06517"></a><span class="lineno"> 6517</span><span class="comment"> *************************************************************************/</span></div>
<div class="line"><a id="l06518" name="l06518"></a><span class="lineno"> 6518</span> </div>
<div class="line"><a id="l06519" name="l06519"></a><span class="lineno"> 6519</span><span class="comment">/* ------------------- BEGIN SYSTEM/COMPILER SPECIFIC -------------------- */</span></div>
<div class="line"><a id="l06520" name="l06520"></a><span class="lineno"> 6520</span> </div>
<div class="line"><a id="l06521" name="l06521"></a><span class="lineno"> 6521</span><span class="preprocessor">#ifdef GLFW_WINGDIAPI_DEFINED</span></div>
<div class="line"><a id="l06522" name="l06522"></a><span class="lineno"> 6522</span><span class="preprocessor"> #undef WINGDIAPI</span></div>
<div class="line"><a id="l06523" name="l06523"></a><span class="lineno"> 6523</span><span class="preprocessor"> #undef GLFW_WINGDIAPI_DEFINED</span></div>
<div class="line"><a id="l06524" name="l06524"></a><span class="lineno"> 6524</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l06525" name="l06525"></a><span class="lineno"> 6525</span> </div>
<div class="line"><a id="l06526" name="l06526"></a><span class="lineno"> 6526</span><span class="preprocessor">#ifdef GLFW_CALLBACK_DEFINED</span></div>
<div class="line"><a id="l06527" name="l06527"></a><span class="lineno"> 6527</span><span class="preprocessor"> #undef CALLBACK</span></div>
<div class="line"><a id="l06528" name="l06528"></a><span class="lineno"> 6528</span><span class="preprocessor"> #undef GLFW_CALLBACK_DEFINED</span></div>
<div class="line"><a id="l06529" name="l06529"></a><span class="lineno"> 6529</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l06530" name="l06530"></a><span class="lineno"> 6530</span> </div>
<div class="line"><a id="l06531" name="l06531"></a><span class="lineno"> 6531</span><span class="comment">/* Some OpenGL related headers need GLAPIENTRY, but it is unconditionally</span></div>
<div class="line"><a id="l06532" name="l06532"></a><span class="lineno"> 6532</span><span class="comment"> * defined by some gl.h variants (OpenBSD) so define it after if needed.</span></div>
<div class="line"><a id="l06533" name="l06533"></a><span class="lineno"> 6533</span><span class="comment"> */</span></div>
<div class="line"><a id="l06534" name="l06534"></a><span class="lineno"> 6534</span><span class="preprocessor">#ifndef GLAPIENTRY</span></div>
<div class="line"><a id="l06535" name="l06535"></a><span class="lineno"><a class="line" href="glfw3_8h.html#aa97755eb47e4bf2727ad45d610e18206"> 6535</a></span><span class="preprocessor"> #define GLAPIENTRY APIENTRY</span></div>
<div class="line"><a id="l06536" name="l06536"></a><span class="lineno"><a class="line" href="glfw3_8h.html#a3b526ac796be993406ea2f1642c25fc3"> 6536</a></span><span class="preprocessor"> #define GLFW_GLAPIENTRY_DEFINED</span></div>
<div class="line"><a id="l06537" name="l06537"></a><span class="lineno"> 6537</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l06538" name="l06538"></a><span class="lineno"> 6538</span> </div>
<div class="line"><a id="l06539" name="l06539"></a><span class="lineno"> 6539</span><span class="comment">/* -------------------- END SYSTEM/COMPILER SPECIFIC --------------------- */</span></div>
<div class="line"><a id="l06540" name="l06540"></a><span class="lineno"> 6540</span> </div>
<div class="line"><a id="l06541" name="l06541"></a><span class="lineno"> 6541</span> </div>
<div class="line"><a id="l06542" name="l06542"></a><span class="lineno"> 6542</span><span class="preprocessor">#ifdef __cplusplus</span></div>
<div class="line"><a id="l06543" name="l06543"></a><span class="lineno"> 6543</span>}</div>
<div class="line"><a id="l06544" name="l06544"></a><span class="lineno"> 6544</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l06545" name="l06545"></a><span class="lineno"> 6545</span> </div>
<div class="line"><a id="l06546" name="l06546"></a><span class="lineno"> 6546</span><span class="preprocessor">#endif </span><span class="comment">/* _glfw3_h_ */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l06547" name="l06547"></a><span class="lineno"> 6547</span> </div>
<div class="ttc" id="agroup__context_html_ga1c04dc242268f827290fe40aa1c91157"><div class="ttname"><a href="group__context.html#ga1c04dc242268f827290fe40aa1c91157">glfwMakeContextCurrent</a></div><div class="ttdeci">void glfwMakeContextCurrent(GLFWwindow *window)</div><div class="ttdoc">Makes the context of the specified window current for the calling thread.</div></div>
<div class="ttc" id="agroup__context_html_ga35f1837e6f666781842483937612f163"><div class="ttname"><a href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a></div><div class="ttdeci">GLFWglproc glfwGetProcAddress(const char *procname)</div><div class="ttdoc">Returns the address of the specified function for the current context.</div></div>
<div class="ttc" id="agroup__context_html_ga3d47c2d2fbe0be9c505d0e04e91a133c"><div class="ttname"><a href="group__context.html#ga3d47c2d2fbe0be9c505d0e04e91a133c">GLFWglproc</a></div><div class="ttdeci">void(* GLFWglproc)(void)</div><div class="ttdoc">Client API function pointer type.</div><div class="ttdef"><b>Definition</b> glfw3.h:1365</div></div>
<div class="ttc" id="agroup__context_html_ga6d4e0cdf151b5e579bd67f13202994ed"><div class="ttname"><a href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed">glfwSwapInterval</a></div><div class="ttdeci">void glfwSwapInterval(int interval)</div><div class="ttdoc">Sets the swap interval for the current context.</div></div>
<div class="ttc" id="agroup__context_html_ga87425065c011cef1ebd6aac75e059dfa"><div class="ttname"><a href="group__context.html#ga87425065c011cef1ebd6aac75e059dfa">glfwExtensionSupported</a></div><div class="ttdeci">int glfwExtensionSupported(const char *extension)</div><div class="ttdoc">Returns whether the specified extension is available.</div></div>
<div class="ttc" id="agroup__context_html_gad94e80185397a6cf5fe2ab30567af71c"><div class="ttname"><a href="group__context.html#gad94e80185397a6cf5fe2ab30567af71c">glfwGetCurrentContext</a></div><div class="ttdeci">GLFWwindow * glfwGetCurrentContext(void)</div><div class="ttdoc">Returns the window whose context is current on the calling thread.</div></div>
<div class="ttc" id="agroup__init_html_ga026abd003c8e6501981ab1662062f1c0"><div class="ttname"><a href="group__init.html#ga026abd003c8e6501981ab1662062f1c0">glfwGetVersionString</a></div><div class="ttdeci">const char * glfwGetVersionString(void)</div><div class="ttdoc">Returns a string describing the compile-time configuration.</div></div>
<div class="ttc" id="agroup__init_html_ga110fd1d3f0412822b4f1908c026f724a"><div class="ttname"><a href="group__init.html#ga110fd1d3f0412822b4f1908c026f724a">glfwInitHint</a></div><div class="ttdeci">void glfwInitHint(int hint, int value)</div><div class="ttdoc">Sets the specified init hint to the desired value.</div></div>
<div class="ttc" id="agroup__init_html_ga317aac130a235ab08c6db0834907d85e"><div class="ttname"><a href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a></div><div class="ttdeci">int glfwInit(void)</div><div class="ttdoc">Initializes the GLFW library.</div></div>
<div class="ttc" id="agroup__init_html_ga3e88a829615d8efe8bec1746f7309c63"><div class="ttname"><a href="group__init.html#ga3e88a829615d8efe8bec1746f7309c63">GLFWreallocatefun</a></div><div class="ttdeci">void *(* GLFWreallocatefun)(void *block, size_t size, void *user)</div><div class="ttdoc">The function pointer type for memory reallocation callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1524</div></div>
<div class="ttc" id="agroup__init_html_ga4306a564e9f60f4de8cc8f31731a3120"><div class="ttname"><a href="group__init.html#ga4306a564e9f60f4de8cc8f31731a3120">GLFWallocatefun</a></div><div class="ttdeci">void *(* GLFWallocatefun)(size_t size, void *user)</div><div class="ttdoc">The function pointer type for memory allocation callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1468</div></div>
<div class="ttc" id="agroup__init_html_ga6d6a983d38bd4e8fd786d7a9061d399e"><div class="ttname"><a href="group__init.html#ga6d6a983d38bd4e8fd786d7a9061d399e">glfwGetPlatform</a></div><div class="ttdeci">int glfwGetPlatform(void)</div><div class="ttdoc">Returns the currently selected platform.</div></div>
<div class="ttc" id="agroup__init_html_ga7181615eda94c4b07bd72bdcee39fa28"><div class="ttname"><a href="group__init.html#ga7181615eda94c4b07bd72bdcee39fa28">GLFWdeallocatefun</a></div><div class="ttdeci">void(* GLFWdeallocatefun)(void *block, void *user)</div><div class="ttdoc">The function pointer type for memory deallocation callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1566</div></div>
<div class="ttc" id="agroup__init_html_ga76af552d0307bb5f7791f245417d4752"><div class="ttname"><a href="group__init.html#ga76af552d0307bb5f7791f245417d4752">glfwInitVulkanLoader</a></div><div class="ttdeci">void glfwInitVulkanLoader(PFN_vkGetInstanceProcAddr loader)</div><div class="ttdoc">Sets the desired Vulkan vkGetInstanceProcAddr function.</div></div>
<div class="ttc" id="agroup__init_html_ga8184701785c096b3862a75cda1bf44a3"><div class="ttname"><a href="group__init.html#ga8184701785c096b3862a75cda1bf44a3">GLFWerrorfun</a></div><div class="ttdeci">void(* GLFWerrorfun)(int error_code, const char *description)</div><div class="ttdoc">The function pointer type for error callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1590</div></div>
<div class="ttc" id="agroup__init_html_ga8785d2b6b36632368d803e78079d38ed"><div class="ttname"><a href="group__init.html#ga8785d2b6b36632368d803e78079d38ed">glfwPlatformSupported</a></div><div class="ttdeci">int glfwPlatformSupported(int platform)</div><div class="ttdoc">Returns whether the library includes support for the specified platform.</div></div>
<div class="ttc" id="agroup__init_html_ga944986b4ec0b928d488141f92982aa18"><div class="ttname"><a href="group__init.html#ga944986b4ec0b928d488141f92982aa18">glfwGetError</a></div><div class="ttdeci">int glfwGetError(const char **description)</div><div class="ttdoc">Returns and clears the last error for the calling thread.</div></div>
<div class="ttc" id="agroup__init_html_ga9dde93e9891fa7dd17e4194c9f3ae7c6"><div class="ttname"><a href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a></div><div class="ttdeci">void glfwInitAllocator(const GLFWallocator *allocator)</div><div class="ttdoc">Sets the init allocator to the desired value.</div></div>
<div class="ttc" id="agroup__init_html_ga9f8ffaacf3c269cc48eafbf8b9b71197"><div class="ttname"><a href="group__init.html#ga9f8ffaacf3c269cc48eafbf8b9b71197">glfwGetVersion</a></div><div class="ttdeci">void glfwGetVersion(int *major, int *minor, int *rev)</div><div class="ttdoc">Retrieves the version of the GLFW library.</div></div>
<div class="ttc" id="agroup__init_html_gaaae48c0a18607ea4a4ba951d939f0901"><div class="ttname"><a href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a></div><div class="ttdeci">void glfwTerminate(void)</div><div class="ttdoc">Terminates the GLFW library.</div></div>
<div class="ttc" id="agroup__init_html_gaff45816610d53f0b83656092a4034f40"><div class="ttname"><a href="group__init.html#gaff45816610d53f0b83656092a4034f40">glfwSetErrorCallback</a></div><div class="ttdeci">GLFWerrorfun glfwSetErrorCallback(GLFWerrorfun callback)</div><div class="ttdoc">Sets the error callback.</div></div>
<div class="ttc" id="agroup__input_html_ga0184dcb59f6d85d735503dcaae809727"><div class="ttname"><a href="group__input.html#ga0184dcb59f6d85d735503dcaae809727">GLFWmousebuttonfun</a></div><div class="ttdeci">void(* GLFWmousebuttonfun)(GLFWwindow *window, int button, int action, int mods)</div><div class="ttdoc">The function pointer type for mouse button callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1806</div></div>
<div class="ttc" id="agroup__input_html_ga01d37b6c40133676b9cea60ca1d7c0cc"><div class="ttname"><a href="group__input.html#ga01d37b6c40133676b9cea60ca1d7c0cc">glfwGetCursorPos</a></div><div class="ttdeci">void glfwGetCursorPos(GLFWwindow *window, double *xpos, double *ypos)</div><div class="ttdoc">Retrieves the position of the cursor relative to the content area of the window.</div></div>
<div class="ttc" id="agroup__input_html_ga04b03af936d906ca123c8f4ee08b39e7"><div class="ttname"><a href="group__input.html#ga04b03af936d906ca123c8f4ee08b39e7">glfwSetCursorPos</a></div><div class="ttdeci">void glfwSetCursorPos(GLFWwindow *window, double xpos, double ypos)</div><div class="ttdoc">Sets the position of the cursor, relative to the content area of the window.</div></div>
<div class="ttc" id="agroup__input_html_ga06e660841b3e79c54da4f54a932c5a2c"><div class="ttname"><a href="group__input.html#ga06e660841b3e79c54da4f54a932c5a2c">glfwGetJoystickHats</a></div><div class="ttdeci">const unsigned char * glfwGetJoystickHats(int jid, int *count)</div><div class="ttdoc">Returns the state of all hats of the specified joystick.</div></div>
<div class="ttc" id="agroup__input_html_ga09b2bd37d328e0b9456c7ec575cc26aa"><div class="ttname"><a href="group__input.html#ga09b2bd37d328e0b9456c7ec575cc26aa">glfwGetTimerValue</a></div><div class="ttdeci">uint64_t glfwGetTimerValue(void)</div><div class="ttdoc">Returns the current value of the raw timer.</div></div>
<div class="ttc" id="agroup__input_html_ga0b7f4ad13c2b17435ff13b6dcfb4e43c"><div class="ttname"><a href="group__input.html#ga0b7f4ad13c2b17435ff13b6dcfb4e43c">glfwSetCharModsCallback</a></div><div class="ttdeci">GLFWcharmodsfun glfwSetCharModsCallback(GLFWwindow *window, GLFWcharmodsfun callback)</div><div class="ttdoc">Sets the Unicode character with modifiers callback.</div></div>
<div class="ttc" id="agroup__input_html_ga18cefd7265d1fa04f3fd38a6746db5f3"><div class="ttname"><a href="group__input.html#ga18cefd7265d1fa04f3fd38a6746db5f3">glfwGetJoystickUserPointer</a></div><div class="ttdeci">void * glfwGetJoystickUserPointer(int jid)</div><div class="ttdoc">Returns the user pointer of the specified joystick.</div></div>
<div class="ttc" id="agroup__input_html_ga1ab90a55cf3f58639b893c0f4118cb6e"><div class="ttname"><a href="group__input.html#ga1ab90a55cf3f58639b893c0f4118cb6e">GLFWcharfun</a></div><div class="ttdeci">void(* GLFWcharfun)(GLFWwindow *window, unsigned int codepoint)</div><div class="ttdoc">The function pointer type for Unicode character callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1918</div></div>
<div class="ttc" id="agroup__input_html_ga1caf18159767e761185e49a3be019f8d"><div class="ttname"><a href="group__input.html#ga1caf18159767e761185e49a3be019f8d">glfwSetKeyCallback</a></div><div class="ttdeci">GLFWkeyfun glfwSetKeyCallback(GLFWwindow *window, GLFWkeyfun callback)</div><div class="ttdoc">Sets the key callback.</div></div>
<div class="ttc" id="agroup__input_html_ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c"><div class="ttname"><a href="group__input.html#ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c">glfwSetJoystickCallback</a></div><div class="ttdeci">GLFWjoystickfun glfwSetJoystickCallback(GLFWjoystickfun callback)</div><div class="ttdoc">Sets the joystick configuration callback.</div></div>
<div class="ttc" id="agroup__input_html_ga3289ee876572f6e91f06df3a24824443"><div class="ttname"><a href="group__input.html#ga3289ee876572f6e91f06df3a24824443">glfwGetTimerFrequency</a></div><div class="ttdeci">uint64_t glfwGetTimerFrequency(void)</div><div class="ttdoc">Returns the frequency, in Hz, of the raw timer.</div></div>
<div class="ttc" id="agroup__input_html_ga556f604f73af156c0db0e97c081373c3"><div class="ttname"><a href="group__input.html#ga556f604f73af156c0db0e97c081373c3">glfwCreateCursor</a></div><div class="ttdeci">GLFWcursor * glfwCreateCursor(const GLFWimage *image, int xhot, int yhot)</div><div class="ttdoc">Creates a custom cursor.</div></div>
<div class="ttc" id="agroup__input_html_ga571e45a030ae4061f746ed56cb76aede"><div class="ttname"><a href="group__input.html#ga571e45a030ae4061f746ed56cb76aede">glfwSetScrollCallback</a></div><div class="ttdeci">GLFWscrollfun glfwSetScrollCallback(GLFWwindow *window, GLFWscrollfun callback)</div><div class="ttdoc">Sets the scroll callback.</div></div>
<div class="ttc" id="agroup__input_html_ga5bd751b27b90f865d2ea613533f0453c"><div class="ttname"><a href="group__input.html#ga5bd751b27b90f865d2ea613533f0453c">GLFWkeyfun</a></div><div class="ttdeci">void(* GLFWkeyfun)(GLFWwindow *window, int key, int scancode, int action, int mods)</div><div class="ttdoc">The function pointer type for keyboard key callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1897</div></div>
<div class="ttc" id="agroup__input_html_ga5ffe34739d3dc97efe432ed2d81d9938"><div class="ttname"><a href="group__input.html#ga5ffe34739d3dc97efe432ed2d81d9938">glfwGetJoystickButtons</a></div><div class="ttdeci">const unsigned char * glfwGetJoystickButtons(int jid, int *count)</div><div class="ttdoc">Returns the state of all buttons of the specified joystick.</div></div>
<div class="ttc" id="agroup__input_html_ga6659411aec3c7fcef27780e2cb2d9600"><div class="ttname"><a href="group__input.html#ga6659411aec3c7fcef27780e2cb2d9600">glfwGetJoystickGUID</a></div><div class="ttdeci">const char * glfwGetJoystickGUID(int jid)</div><div class="ttdoc">Returns the SDL compatible GUID of the specified joystick.</div></div>
<div class="ttc" id="agroup__input_html_ga67ddd1b7dcbbaff03e4a76c0ea67103a"><div class="ttname"><a href="group__input.html#ga67ddd1b7dcbbaff03e4a76c0ea67103a">glfwGetKeyScancode</a></div><div class="ttdeci">int glfwGetKeyScancode(int key)</div><div class="ttdoc">Returns the platform-specific scancode of the specified key.</div></div>
<div class="ttc" id="agroup__input_html_ga6ab84420974d812bee700e45284a723c"><div class="ttname"><a href="group__input.html#ga6ab84420974d812bee700e45284a723c">glfwSetMouseButtonCallback</a></div><div class="ttdeci">GLFWmousebuttonfun glfwSetMouseButtonCallback(GLFWwindow *window, GLFWmousebuttonfun callback)</div><div class="ttdoc">Sets the mouse button callback.</div></div>
<div class="ttc" id="agroup__input_html_ga6b2f72d64d636b48a727b437cbb7489e"><div class="ttname"><a href="group__input.html#ga6b2f72d64d636b48a727b437cbb7489e">glfwSetJoystickUserPointer</a></div><div class="ttdeci">void glfwSetJoystickUserPointer(int jid, void *pointer)</div><div class="ttdoc">Sets the user pointer of the specified joystick.</div></div>
<div class="ttc" id="agroup__input_html_ga71a5b20808ea92193d65c21b82580355"><div class="ttname"><a href="group__input.html#ga71a5b20808ea92193d65c21b82580355">glfwGetClipboardString</a></div><div class="ttdeci">const char * glfwGetClipboardString(GLFWwindow *window)</div><div class="ttdoc">Returns the contents of the clipboard as a string.</div></div>
<div class="ttc" id="agroup__input_html_ga81b952cd1764274d0db7fb3c5a79ba6a"><div class="ttname"><a href="group__input.html#ga81b952cd1764274d0db7fb3c5a79ba6a">glfwDestroyCursor</a></div><div class="ttdeci">void glfwDestroyCursor(GLFWcursor *cursor)</div><div class="ttdoc">Destroys a cursor.</div></div>
<div class="ttc" id="agroup__input_html_ga89261ae18c75e863aaf2656ecdd238f4"><div class="ttname"><a href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a></div><div class="ttdeci">struct GLFWcursor GLFWcursor</div><div class="ttdoc">Opaque cursor object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1415</div></div>
<div class="ttc" id="agroup__input_html_ga8aea73a1a25cc6c0486a617019f56728"><div class="ttname"><a href="group__input.html#ga8aea73a1a25cc6c0486a617019f56728">glfwGetGamepadName</a></div><div class="ttdeci">const char * glfwGetGamepadName(int jid)</div><div class="ttdoc">Returns the human-readable gamepad name for the specified joystick.</div></div>
<div class="ttc" id="agroup__input_html_gaa21ad5986ae9a26077a40142efb56243"><div class="ttname"><a href="group__input.html#gaa21ad5986ae9a26077a40142efb56243">GLFWjoystickfun</a></div><div class="ttdeci">void(* GLFWjoystickfun)(int jid, int event)</div><div class="ttdoc">The function pointer type for joystick configuration callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:2011</div></div>
<div class="ttc" id="agroup__input_html_gaa6cf4e7a77158a3b8fd00328b1720a4a"><div class="ttname"><a href="group__input.html#gaa6cf4e7a77158a3b8fd00328b1720a4a">glfwGetTime</a></div><div class="ttdeci">double glfwGetTime(void)</div><div class="ttdoc">Returns the GLFW time.</div></div>
<div class="ttc" id="agroup__input_html_gaa92336e173da9c8834558b54ee80563b"><div class="ttname"><a href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a></div><div class="ttdeci">void glfwSetInputMode(GLFWwindow *window, int mode, int value)</div><div class="ttdoc">Sets an input option for the specified window.</div></div>
<div class="ttc" id="agroup__input_html_gaa93dc4818ac9ab32532909d53a337cbe"><div class="ttname"><a href="group__input.html#gaa93dc4818ac9ab32532909d53a337cbe">GLFWcursorenterfun</a></div><div class="ttdeci">void(* GLFWcursorenterfun)(GLFWwindow *window, int entered)</div><div class="ttdoc">The function pointer type for cursor enter/leave callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1850</div></div>
<div class="ttc" id="agroup__input_html_gaaba73c3274062c18723b7f05862d94b2"><div class="ttname"><a href="group__input.html#gaaba73c3274062c18723b7f05862d94b2">GLFWdropfun</a></div><div class="ttdeci">void(* GLFWdropfun)(GLFWwindow *window, int path_count, const char *paths[])</div><div class="ttdoc">The function pointer type for path drop callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1969</div></div>
<div class="ttc" id="agroup__input_html_gab25c4a220fd8f5717718dbc487828996"><div class="ttname"><a href="group__input.html#gab25c4a220fd8f5717718dbc487828996">glfwSetCharCallback</a></div><div class="ttdeci">GLFWcharfun glfwSetCharCallback(GLFWwindow *window, GLFWcharfun callback)</div><div class="ttdoc">Sets the Unicode character callback.</div></div>
<div class="ttc" id="agroup__input_html_gab773f0ee0a07cff77a210cea40bc1f6b"><div class="ttname"><a href="group__input.html#gab773f0ee0a07cff77a210cea40bc1f6b">glfwSetDropCallback</a></div><div class="ttdeci">GLFWdropfun glfwSetDropCallback(GLFWwindow *window, GLFWdropfun callback)</div><div class="ttdoc">Sets the path drop callback.</div></div>
<div class="ttc" id="agroup__input_html_gaba1f022c5eb07dfac421df34cdcd31dd"><div class="ttname"><a href="group__input.html#gaba1f022c5eb07dfac421df34cdcd31dd">glfwSetClipboardString</a></div><div class="ttdeci">void glfwSetClipboardString(GLFWwindow *window, const char *string)</div><div class="ttdoc">Sets the clipboard to the specified string.</div></div>
<div class="ttc" id="agroup__input_html_gac1473feacb5996c01a7a5a33b5066704"><div class="ttname"><a href="group__input.html#gac1473feacb5996c01a7a5a33b5066704">glfwGetMouseButton</a></div><div class="ttdeci">int glfwGetMouseButton(GLFWwindow *window, int button)</div><div class="ttdoc">Returns the last reported state of a mouse button for the specified window.</div></div>
<div class="ttc" id="agroup__input_html_gac1f879ab7435d54d4d79bb469fe225d7"><div class="ttname"><a href="group__input.html#gac1f879ab7435d54d4d79bb469fe225d7">glfwSetCursorPosCallback</a></div><div class="ttdeci">GLFWcursorposfun glfwSetCursorPosCallback(GLFWwindow *window, GLFWcursorposfun callback)</div><div class="ttdoc">Sets the cursor position callback.</div></div>
<div class="ttc" id="agroup__input_html_gac3cf64f90b6219c05ac7b7822d5a4b8f"><div class="ttname"><a href="group__input.html#gac3cf64f90b6219c05ac7b7822d5a4b8f">GLFWcharmodsfun</a></div><div class="ttdeci">void(* GLFWcharmodsfun)(GLFWwindow *window, unsigned int codepoint, int mods)</div><div class="ttdoc">The function pointer type for Unicode character with modifiers callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1945</div></div>
<div class="ttc" id="agroup__input_html_gac6a8e769e18e0bcfa9097793fc2c3978"><div class="ttname"><a href="group__input.html#gac6a8e769e18e0bcfa9097793fc2c3978">glfwGetJoystickName</a></div><div class="ttdeci">const char * glfwGetJoystickName(int jid)</div><div class="ttdoc">Returns the name of the specified joystick.</div></div>
<div class="ttc" id="agroup__input_html_gad0f676860f329d80f7e47e9f06a96f00"><div class="ttname"><a href="group__input.html#gad0f676860f329d80f7e47e9f06a96f00">glfwJoystickIsGamepad</a></div><div class="ttdeci">int glfwJoystickIsGamepad(int jid)</div><div class="ttdoc">Returns whether the specified joystick has a gamepad mapping.</div></div>
<div class="ttc" id="agroup__input_html_gad27f8ad0142c038a281466c0966817d8"><div class="ttname"><a href="group__input.html#gad27f8ad0142c038a281466c0966817d8">glfwSetCursorEnterCallback</a></div><div class="ttdeci">GLFWcursorenterfun glfwSetCursorEnterCallback(GLFWwindow *window, GLFWcursorenterfun callback)</div><div class="ttdoc">Sets the cursor enter/leave callback.</div></div>
<div class="ttc" id="agroup__input_html_gad3b4f38c8d5dae036bc8fa959e18343e"><div class="ttname"><a href="group__input.html#gad3b4f38c8d5dae036bc8fa959e18343e">glfwSetCursor</a></div><div class="ttdeci">void glfwSetCursor(GLFWwindow *window, GLFWcursor *cursor)</div><div class="ttdoc">Sets the cursor for the window.</div></div>
<div class="ttc" id="agroup__input_html_gad6fae41b3ac2e4209aaa87b596c57f68"><div class="ttname"><a href="group__input.html#gad6fae41b3ac2e4209aaa87b596c57f68">GLFWcursorposfun</a></div><div class="ttdeci">void(* GLFWcursorposfun)(GLFWwindow *window, double xpos, double ypos)</div><div class="ttdoc">The function pointer type for cursor position callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1829</div></div>
<div class="ttc" id="agroup__input_html_gadccddea8bce6113fa459de379ddaf051"><div class="ttname"><a href="group__input.html#gadccddea8bce6113fa459de379ddaf051">glfwGetGamepadState</a></div><div class="ttdeci">int glfwGetGamepadState(int jid, GLFWgamepadstate *state)</div><div class="ttdoc">Retrieves the state of the specified joystick remapped as a gamepad.</div></div>
<div class="ttc" id="agroup__input_html_gadd341da06bc8d418b4dc3a3518af9ad2"><div class="ttname"><a href="group__input.html#gadd341da06bc8d418b4dc3a3518af9ad2">glfwGetKey</a></div><div class="ttdeci">int glfwGetKey(GLFWwindow *window, int key)</div><div class="ttdoc">Returns the last reported state of a keyboard key for the specified window.</div></div>
<div class="ttc" id="agroup__input_html_gae4ee0dbd0d256183e1ea4026d897e1c2"><div class="ttname"><a href="group__input.html#gae4ee0dbd0d256183e1ea4026d897e1c2">glfwRawMouseMotionSupported</a></div><div class="ttdeci">int glfwRawMouseMotionSupported(void)</div><div class="ttdoc">Returns whether raw mouse motion is supported.</div></div>
<div class="ttc" id="agroup__input_html_gaeaed62e69c3bd62b7ff8f7b19913ce4f"><div class="ttname"><a href="group__input.html#gaeaed62e69c3bd62b7ff8f7b19913ce4f">glfwGetKeyName</a></div><div class="ttdeci">const char * glfwGetKeyName(int key, int scancode)</div><div class="ttdoc">Returns the layout-specific name of the specified printable key.</div></div>
<div class="ttc" id="agroup__input_html_gaeb1c0191d3140a233a682987c61eb408"><div class="ttname"><a href="group__input.html#gaeb1c0191d3140a233a682987c61eb408">glfwGetJoystickAxes</a></div><div class="ttdeci">const float * glfwGetJoystickAxes(int jid, int *count)</div><div class="ttdoc">Returns the values of all axes of the specified joystick.</div></div>
<div class="ttc" id="agroup__input_html_gaed0966cee139d815317f9ffcba64c9f1"><div class="ttname"><a href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a></div><div class="ttdeci">int glfwJoystickPresent(int jid)</div><div class="ttdoc">Returns whether the specified joystick is present.</div></div>
<div class="ttc" id="agroup__input_html_gaed5104612f2fa8e66aa6e846652ad00f"><div class="ttname"><a href="group__input.html#gaed5104612f2fa8e66aa6e846652ad00f">glfwUpdateGamepadMappings</a></div><div class="ttdeci">int glfwUpdateGamepadMappings(const char *string)</div><div class="ttdoc">Adds the specified SDL_GameControllerDB gamepad mappings.</div></div>
<div class="ttc" id="agroup__input_html_gaf2fb2eb2c9dd842d1cef8a34e3c6403e"><div class="ttname"><a href="group__input.html#gaf2fb2eb2c9dd842d1cef8a34e3c6403e">glfwCreateStandardCursor</a></div><div class="ttdeci">GLFWcursor * glfwCreateStandardCursor(int shape)</div><div class="ttdoc">Creates a cursor with a standard shape.</div></div>
<div class="ttc" id="agroup__input_html_gaf59589ef6e8b8c8b5ad184b25afd4dc0"><div class="ttname"><a href="group__input.html#gaf59589ef6e8b8c8b5ad184b25afd4dc0">glfwSetTime</a></div><div class="ttdeci">void glfwSetTime(double time)</div><div class="ttdoc">Sets the GLFW time.</div></div>
<div class="ttc" id="agroup__input_html_gaf5b859dbe19bdf434e42695ea45cc5f4"><div class="ttname"><a href="group__input.html#gaf5b859dbe19bdf434e42695ea45cc5f4">glfwGetInputMode</a></div><div class="ttdeci">int glfwGetInputMode(GLFWwindow *window, int mode)</div><div class="ttdoc">Returns the value of an input option for the specified window.</div></div>
<div class="ttc" id="agroup__input_html_gaf656112c33de3efdb227fa58f0134cf5"><div class="ttname"><a href="group__input.html#gaf656112c33de3efdb227fa58f0134cf5">GLFWscrollfun</a></div><div class="ttdeci">void(* GLFWscrollfun)(GLFWwindow *window, double xoffset, double yoffset)</div><div class="ttdoc">The function pointer type for scroll callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1871</div></div>
<div class="ttc" id="agroup__monitor_html_ga102f54e7acc9149edbcf0997152df8c9"><div class="ttname"><a href="group__monitor.html#ga102f54e7acc9149edbcf0997152df8c9">glfwGetMonitorPos</a></div><div class="ttdeci">void glfwGetMonitorPos(GLFWmonitor *monitor, int *xpos, int *ypos)</div><div class="ttdoc">Returns the position of the monitor's viewport on the virtual screen.</div></div>
<div class="ttc" id="agroup__monitor_html_ga1adbfbfb8cd58b23cfee82e574fbbdc5"><div class="ttname"><a href="group__monitor.html#ga1adbfbfb8cd58b23cfee82e574fbbdc5">glfwGetMonitorUserPointer</a></div><div class="ttdeci">void * glfwGetMonitorUserPointer(GLFWmonitor *monitor)</div><div class="ttdoc">Returns the user pointer of the specified monitor.</div></div>
<div class="ttc" id="agroup__monitor_html_ga583f0ffd0d29613d8cd172b996bbf0dd"><div class="ttname"><a href="group__monitor.html#ga583f0ffd0d29613d8cd172b996bbf0dd">glfwSetGammaRamp</a></div><div class="ttdeci">void glfwSetGammaRamp(GLFWmonitor *monitor, const GLFWgammaramp *ramp)</div><div class="ttdoc">Sets the current gamma ramp for the specified monitor.</div></div>
<div class="ttc" id="agroup__monitor_html_ga6ac582625c990220785ddd34efa3169a"><div class="ttname"><a href="group__monitor.html#ga6ac582625c990220785ddd34efa3169a">glfwSetGamma</a></div><div class="ttdeci">void glfwSetGamma(GLFWmonitor *monitor, float gamma)</div><div class="ttdoc">Generates a gamma ramp and sets it for the specified monitor.</div></div>
<div class="ttc" id="agroup__monitor_html_ga702750e24313a686d3637297b6e85fda"><div class="ttname"><a href="group__monitor.html#ga702750e24313a686d3637297b6e85fda">glfwSetMonitorUserPointer</a></div><div class="ttdeci">void glfwSetMonitorUserPointer(GLFWmonitor *monitor, void *pointer)</div><div class="ttdoc">Sets the user pointer of the specified monitor.</div></div>
<div class="ttc" id="agroup__monitor_html_ga70b1156d5d24e9928f145d6c864369d2"><div class="ttname"><a href="group__monitor.html#ga70b1156d5d24e9928f145d6c864369d2">glfwGetMonitors</a></div><div class="ttdeci">GLFWmonitor ** glfwGetMonitors(int *count)</div><div class="ttdoc">Returns the currently connected monitors.</div></div>
<div class="ttc" id="agroup__monitor_html_ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0"><div class="ttname"><a href="group__monitor.html#ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0">glfwGetMonitorWorkarea</a></div><div class="ttdeci">void glfwGetMonitorWorkarea(GLFWmonitor *monitor, int *xpos, int *ypos, int *width, int *height)</div><div class="ttdoc">Retrieves the work area of the monitor.</div></div>
<div class="ttc" id="agroup__monitor_html_ga76ba90debcf0062b5c4b73052b24f96f"><div class="ttname"><a href="group__monitor.html#ga76ba90debcf0062b5c4b73052b24f96f">glfwGetGammaRamp</a></div><div class="ttdeci">const GLFWgammaramp * glfwGetGammaRamp(GLFWmonitor *monitor)</div><div class="ttdoc">Returns the current gamma ramp for the specified monitor.</div></div>
<div class="ttc" id="agroup__monitor_html_ga7af83e13489d90379588fb331b9e4b68"><div class="ttname"><a href="group__monitor.html#ga7af83e13489d90379588fb331b9e4b68">glfwGetMonitorName</a></div><div class="ttdeci">const char * glfwGetMonitorName(GLFWmonitor *monitor)</div><div class="ttdoc">Returns the name of the specified monitor.</div></div>
<div class="ttc" id="agroup__monitor_html_ga7d8bffc6c55539286a6bd20d32a8d7ea"><div class="ttname"><a href="group__monitor.html#ga7d8bffc6c55539286a6bd20d32a8d7ea">glfwGetMonitorPhysicalSize</a></div><div class="ttdeci">void glfwGetMonitorPhysicalSize(GLFWmonitor *monitor, int *widthMM, int *heightMM)</div><div class="ttdoc">Returns the physical size of the monitor.</div></div>
<div class="ttc" id="agroup__monitor_html_ga8d9efd1cde9426692c73fe40437d0ae3"><div class="ttname"><a href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a></div><div class="ttdeci">struct GLFWmonitor GLFWmonitor</div><div class="ttdoc">Opaque monitor object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1391</div></div>
<div class="ttc" id="agroup__monitor_html_gaabe16caca8dea952504dfdebdf4cd249"><div class="ttname"><a href="group__monitor.html#gaabe16caca8dea952504dfdebdf4cd249">GLFWmonitorfun</a></div><div class="ttdeci">void(* GLFWmonitorfun)(GLFWmonitor *monitor, int event)</div><div class="ttdoc">The function pointer type for monitor configuration callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1990</div></div>
<div class="ttc" id="agroup__monitor_html_gab39df645587c8518192aa746c2fb06c3"><div class="ttname"><a href="group__monitor.html#gab39df645587c8518192aa746c2fb06c3">glfwSetMonitorCallback</a></div><div class="ttdeci">GLFWmonitorfun glfwSetMonitorCallback(GLFWmonitorfun callback)</div><div class="ttdoc">Sets the monitor configuration callback.</div></div>
<div class="ttc" id="agroup__monitor_html_gaba376fa7e76634b4788bddc505d6c9d5"><div class="ttname"><a href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a></div><div class="ttdeci">const GLFWvidmode * glfwGetVideoMode(GLFWmonitor *monitor)</div><div class="ttdoc">Returns the current mode of the specified monitor.</div></div>
<div class="ttc" id="agroup__monitor_html_gac3adb24947eb709e1874028272e5dfc5"><div class="ttname"><a href="group__monitor.html#gac3adb24947eb709e1874028272e5dfc5">glfwGetPrimaryMonitor</a></div><div class="ttdeci">GLFWmonitor * glfwGetPrimaryMonitor(void)</div><div class="ttdoc">Returns the primary monitor.</div></div>
<div class="ttc" id="agroup__monitor_html_gad2e24d2843cb7d6c26202cddd530fc1b"><div class="ttname"><a href="group__monitor.html#gad2e24d2843cb7d6c26202cddd530fc1b">glfwGetVideoModes</a></div><div class="ttdeci">const GLFWvidmode * glfwGetVideoModes(GLFWmonitor *monitor, int *count)</div><div class="ttdoc">Returns the available video modes for the specified monitor.</div></div>
<div class="ttc" id="agroup__monitor_html_gad3152e84465fa620b601265ebfcdb21b"><div class="ttname"><a href="group__monitor.html#gad3152e84465fa620b601265ebfcdb21b">glfwGetMonitorContentScale</a></div><div class="ttdeci">void glfwGetMonitorContentScale(GLFWmonitor *monitor, float *xscale, float *yscale)</div><div class="ttdoc">Retrieves the content scale for the specified monitor.</div></div>
<div class="ttc" id="agroup__vulkan_html_ga1a24536bec3f80b08ead18e28e6ae965"><div class="ttname"><a href="group__vulkan.html#ga1a24536bec3f80b08ead18e28e6ae965">glfwCreateWindowSurface</a></div><div class="ttdeci">VkResult glfwCreateWindowSurface(VkInstance instance, GLFWwindow *window, const VkAllocationCallbacks *allocator, VkSurfaceKHR *surface)</div><div class="ttdoc">Creates a Vulkan surface for the specified window.</div></div>
<div class="ttc" id="agroup__vulkan_html_ga2e7f30931e02464b5bc8d0d4b6f9fe2b"><div class="ttname"><a href="group__vulkan.html#ga2e7f30931e02464b5bc8d0d4b6f9fe2b">glfwVulkanSupported</a></div><div class="ttdeci">int glfwVulkanSupported(void)</div><div class="ttdoc">Returns whether the Vulkan loader and an ICD have been found.</div></div>
<div class="ttc" id="agroup__vulkan_html_ga70c01918dc9d233a4fbe0681a43018af"><div class="ttname"><a href="group__vulkan.html#ga70c01918dc9d233a4fbe0681a43018af">GLFWvkproc</a></div><div class="ttdeci">void(* GLFWvkproc)(void)</div><div class="ttdoc">Vulkan API function pointer type.</div><div class="ttdef"><b>Definition</b> glfw3.h:1379</div></div>
<div class="ttc" id="agroup__vulkan_html_ga99ad342d82f4a3421e2864978cb6d1d6"><div class="ttname"><a href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a></div><div class="ttdeci">const char ** glfwGetRequiredInstanceExtensions(uint32_t *count)</div><div class="ttdoc">Returns the Vulkan instance extensions required by GLFW.</div></div>
<div class="ttc" id="agroup__vulkan_html_gadf228fac94c5fd8f12423ec9af9ff1e9"><div class="ttname"><a href="group__vulkan.html#gadf228fac94c5fd8f12423ec9af9ff1e9">glfwGetInstanceProcAddress</a></div><div class="ttdeci">GLFWvkproc glfwGetInstanceProcAddress(VkInstance instance, const char *procname)</div><div class="ttdoc">Returns the address of the specified Vulkan instance function.</div></div>
<div class="ttc" id="agroup__vulkan_html_gaff3823355cdd7e2f3f9f4d9ea9518d92"><div class="ttname"><a href="group__vulkan.html#gaff3823355cdd7e2f3f9f4d9ea9518d92">glfwGetPhysicalDevicePresentationSupport</a></div><div class="ttdeci">int glfwGetPhysicalDevicePresentationSupport(VkInstance instance, VkPhysicalDevice device, uint32_t queuefamily)</div><div class="ttdoc">Returns whether the specified queue family can present images.</div></div>
<div class="ttc" id="agroup__window_html_ga08bdfbba88934f9c4f92fd757979ac74"><div class="ttname"><a href="group__window.html#ga08bdfbba88934f9c4f92fd757979ac74">glfwSetWindowPosCallback</a></div><div class="ttdeci">GLFWwindowposfun glfwSetWindowPosCallback(GLFWwindow *window, GLFWwindowposfun callback)</div><div class="ttdoc">Sets the position callback for the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga0e2637a4161afb283f5300c7f94785c9"><div class="ttname"><a href="group__window.html#ga0e2637a4161afb283f5300c7f94785c9">glfwGetFramebufferSize</a></div><div class="ttdeci">void glfwGetFramebufferSize(GLFWwindow *window, int *width, int *height)</div><div class="ttdoc">Retrieves the size of the framebuffer of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga15a5a1ee5b3c2ca6b15ca209a12efd14"><div class="ttname"><a href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a></div><div class="ttdeci">void glfwSwapBuffers(GLFWwindow *window)</div><div class="ttdoc">Swaps the front and back buffers of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga1a9fd382058c53101b21cf211898f1f1"><div class="ttname"><a href="group__window.html#ga1a9fd382058c53101b21cf211898f1f1">glfwGetWindowFrameSize</a></div><div class="ttdeci">void glfwGetWindowFrameSize(GLFWwindow *window, int *left, int *top, int *right, int *bottom)</div><div class="ttdoc">Retrieves the size of the frame of the window.</div></div>
<div class="ttc" id="agroup__window_html_ga1abb6d690e8c88e0c8cd1751356dbca8"><div class="ttname"><a href="group__window.html#ga1abb6d690e8c88e0c8cd1751356dbca8">glfwSetWindowPos</a></div><div class="ttdeci">void glfwSetWindowPos(GLFWwindow *window, int xpos, int ypos)</div><div class="ttdoc">Sets the position of the content area of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga1bb559c0ebaad63c5c05ad2a066779c4"><div class="ttname"><a href="group__window.html#ga1bb559c0ebaad63c5c05ad2a066779c4">glfwIconifyWindow</a></div><div class="ttdeci">void glfwIconifyWindow(GLFWwindow *window)</div><div class="ttdoc">Iconifies the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga1c5c7eb889c33c7f4d10dd35b327654e"><div class="ttname"><a href="group__window.html#ga1c5c7eb889c33c7f4d10dd35b327654e">glfwSetWindowRefreshCallback</a></div><div class="ttdeci">GLFWwindowrefreshfun glfwSetWindowRefreshCallback(GLFWwindow *window, GLFWwindowrefreshfun callback)</div><div class="ttdoc">Sets the refresh callback for the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga24e02fbfefbb81fc45320989f8140ab5"><div class="ttname"><a href="group__window.html#ga24e02fbfefbb81fc45320989f8140ab5">glfwWindowShouldClose</a></div><div class="ttdeci">int glfwWindowShouldClose(GLFWwindow *window)</div><div class="ttdoc">Checks the close flag of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga2f8d59323fc4692c1d54ba08c863a703"><div class="ttname"><a href="group__window.html#ga2f8d59323fc4692c1d54ba08c863a703">glfwRequestWindowAttention</a></div><div class="ttdeci">void glfwRequestWindowAttention(GLFWwindow *window)</div><div class="ttdoc">Requests user attention to the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga3017196fdaec33ac3e095765176c2a90"><div class="ttname"><a href="group__window.html#ga3017196fdaec33ac3e095765176c2a90">GLFWwindowmaximizefun</a></div><div class="ttdeci">void(* GLFWwindowmaximizefun)(GLFWwindow *window, int maximized)</div><div class="ttdoc">The function pointer type for window maximize callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1738</div></div>
<div class="ttc" id="agroup__window_html_ga3555a418df92ad53f917597fe2f64aeb"><div class="ttname"><a href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a></div><div class="ttdeci">GLFWwindow * glfwCreateWindow(int width, int height, const char *title, GLFWmonitor *monitor, GLFWwindow *share)</div><div class="ttdoc">Creates a window and its associated context.</div></div>
<div class="ttc" id="agroup__window_html_ga35c658cccba236f26e7adee0e25f6a4f"><div class="ttname"><a href="group__window.html#ga35c658cccba236f26e7adee0e25f6a4f">GLFWwindowiconifyfun</a></div><div class="ttdeci">void(* GLFWwindowiconifyfun)(GLFWwindow *window, int iconified)</div><div class="ttdoc">The function pointer type for window iconify callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1717</div></div>
<div class="ttc" id="agroup__window_html_ga371911f12c74c504dd8d47d832d095cb"><div class="ttname"><a href="group__window.html#ga371911f12c74c504dd8d47d832d095cb">glfwSetWindowSize</a></div><div class="ttdeci">void glfwSetWindowSize(GLFWwindow *window, int width, int height)</div><div class="ttdoc">Sets the size of the content area of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga37bd57223967b4211d60ca1a0bf3c832"><div class="ttname"><a href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a></div><div class="ttdeci">void glfwPollEvents(void)</div><div class="ttdoc">Processes all pending events.</div></div>
<div class="ttc" id="agroup__window_html_ga3c96d80d363e67d13a41b5d1821f3242"><div class="ttname"><a href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a></div><div class="ttdeci">struct GLFWwindow GLFWwindow</div><div class="ttdoc">Opaque window object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1403</div></div>
<div class="ttc" id="agroup__window_html_ga3d2fc6026e690ab31a13f78bc9fd3651"><div class="ttname"><a href="group__window.html#ga3d2fc6026e690ab31a13f78bc9fd3651">glfwSetWindowUserPointer</a></div><div class="ttdeci">void glfwSetWindowUserPointer(GLFWwindow *window, void *pointer)</div><div class="ttdoc">Sets the user pointer of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga3f541387449d911274324ae7f17ec56b"><div class="ttname"><a href="group__window.html#ga3f541387449d911274324ae7f17ec56b">glfwMaximizeWindow</a></div><div class="ttdeci">void glfwMaximizeWindow(GLFWwindow *window)</div><div class="ttdoc">Maximizes the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga431663a1427d2eb3a273bc398b6737b5"><div class="ttname"><a href="group__window.html#ga431663a1427d2eb3a273bc398b6737b5">GLFWwindowrefreshfun</a></div><div class="ttdeci">void(* GLFWwindowrefreshfun)(GLFWwindow *window)</div><div class="ttdoc">The function pointer type for window content refresh callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1675</div></div>
<div class="ttc" id="agroup__window_html_ga49401f82a1ba5f15db5590728314d47c"><div class="ttname"><a href="group__window.html#ga49401f82a1ba5f15db5590728314d47c">glfwHideWindow</a></div><div class="ttdeci">void glfwHideWindow(GLFWwindow *window)</div><div class="ttdoc">Hides the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga49c449dde2a6f87d996f4daaa09d6708"><div class="ttname"><a href="group__window.html#ga49c449dde2a6f87d996f4daaa09d6708">glfwSetWindowShouldClose</a></div><div class="ttdeci">void glfwSetWindowShouldClose(GLFWwindow *window, int value)</div><div class="ttdoc">Sets the close flag of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga4d766499ac02c60f02221a9dfab87299"><div class="ttname"><a href="group__window.html#ga4d766499ac02c60f02221a9dfab87299">glfwGetWindowMonitor</a></div><div class="ttdeci">GLFWmonitor * glfwGetWindowMonitor(GLFWwindow *window)</div><div class="ttdoc">Returns the monitor that the window uses for full screen mode.</div></div>
<div class="ttc" id="agroup__window_html_ga52527a5904b47d802b6b4bb519cdebc7"><div class="ttname"><a href="group__window.html#ga52527a5904b47d802b6b4bb519cdebc7">glfwRestoreWindow</a></div><div class="ttdeci">void glfwRestoreWindow(GLFWwindow *window)</div><div class="ttdoc">Restores the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga554e37d781f0a997656c26b2c56c835e"><div class="ttname"><a href="group__window.html#ga554e37d781f0a997656c26b2c56c835e">glfwWaitEvents</a></div><div class="ttdeci">void glfwWaitEvents(void)</div><div class="ttdoc">Waits until events are queued and processes them.</div></div>
<div class="ttc" id="agroup__window_html_ga5d877f09e968cef7a360b513306f17ff"><div class="ttname"><a href="group__window.html#ga5d877f09e968cef7a360b513306f17ff">glfwSetWindowTitle</a></div><div class="ttdeci">void glfwSetWindowTitle(GLFWwindow *window, const char *title)</div><div class="ttdoc">Sets the title of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga605a178db92f1a7f1a925563ef3ea2cf"><div class="ttname"><a href="group__window.html#ga605a178db92f1a7f1a925563ef3ea2cf">glfwWaitEventsTimeout</a></div><div class="ttdeci">void glfwWaitEventsTimeout(double timeout)</div><div class="ttdoc">Waits with timeout until events are queued and processes them.</div></div>
<div class="ttc" id="agroup__window_html_ga61be47917b72536a148300f46494fc66"><div class="ttname"><a href="group__window.html#ga61be47917b72536a148300f46494fc66">glfwShowWindow</a></div><div class="ttdeci">void glfwShowWindow(GLFWwindow *window)</div><div class="ttdoc">Makes the specified window visible.</div></div>
<div class="ttc" id="agroup__window_html_ga72ac8cb1ee2e312a878b55153d81b937"><div class="ttname"><a href="group__window.html#ga72ac8cb1ee2e312a878b55153d81b937">glfwSetWindowAspectRatio</a></div><div class="ttdeci">void glfwSetWindowAspectRatio(GLFWwindow *window, int numer, int denom)</div><div class="ttdoc">Sets the aspect ratio of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga73cb526c000876fd8ddf571570fdb634"><div class="ttname"><a href="group__window.html#ga73cb526c000876fd8ddf571570fdb634">glfwGetWindowPos</a></div><div class="ttdeci">void glfwGetWindowPos(GLFWwindow *window, int *xpos, int *ypos)</div><div class="ttdoc">Retrieves the position of the content area of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga77f288a2d04bb3c77c7d9615d08cf70e"><div class="ttname"><a href="group__window.html#ga77f288a2d04bb3c77c7d9615d08cf70e">GLFWwindowcontentscalefun</a></div><div class="ttdeci">void(* GLFWwindowcontentscalefun)(GLFWwindow *window, float xscale, float yscale)</div><div class="ttdoc">The function pointer type for window content scale callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1780</div></div>
<div class="ttc" id="agroup__window_html_ga7d9c8c62384b1e2821c4dc48952d2033"><div class="ttname"><a href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a></div><div class="ttdeci">void glfwWindowHint(int hint, int value)</div><div class="ttdoc">Sets the specified window hint to the desired value.</div></div>
<div class="ttc" id="agroup__window_html_ga81c76c418af80a1cce7055bccb0ae0a7"><div class="ttname"><a href="group__window.html#ga81c76c418af80a1cce7055bccb0ae0a7">glfwSetWindowMonitor</a></div><div class="ttdeci">void glfwSetWindowMonitor(GLFWwindow *window, GLFWmonitor *monitor, int xpos, int ypos, int width, int height, int refreshRate)</div><div class="ttdoc">Sets the mode, monitor, video mode and placement of a window.</div></div>
<div class="ttc" id="agroup__window_html_ga873780357abd3f3a081d71a40aae45a1"><div class="ttname"><a href="group__window.html#ga873780357abd3f3a081d71a40aae45a1">glfwFocusWindow</a></div><div class="ttdeci">void glfwFocusWindow(GLFWwindow *window)</div><div class="ttdoc">Brings the specified window to front and sets input focus.</div></div>
<div class="ttc" id="agroup__window_html_ga8cb2782861c9d997bcf2dea97f363e5f"><div class="ttname"><a href="group__window.html#ga8cb2782861c9d997bcf2dea97f363e5f">glfwWindowHintString</a></div><div class="ttdeci">void glfwWindowHintString(int hint, const char *value)</div><div class="ttdoc">Sets the specified window hint to the desired value.</div></div>
<div class="ttc" id="agroup__window_html_gaa77c4898dfb83344a6b4f76aa16b9a4a"><div class="ttname"><a href="group__window.html#gaa77c4898dfb83344a6b4f76aa16b9a4a">glfwDefaultWindowHints</a></div><div class="ttdeci">void glfwDefaultWindowHints(void)</div><div class="ttdoc">Resets all window hints to their default values.</div></div>
<div class="ttc" id="agroup__window_html_gab3fb7c3366577daef18c0023e2a8591f"><div class="ttname"><a href="group__window.html#gab3fb7c3366577daef18c0023e2a8591f">glfwSetFramebufferSizeCallback</a></div><div class="ttdeci">GLFWframebuffersizefun glfwSetFramebufferSizeCallback(GLFWwindow *window, GLFWframebuffersizefun callback)</div><div class="ttdoc">Sets the framebuffer resize callback for the specified window.</div></div>
<div class="ttc" id="agroup__window_html_gab5997a25187e9fd5c6f2ecbbc8dfd7e9"><div class="ttname"><a href="group__window.html#gab5997a25187e9fd5c6f2ecbbc8dfd7e9">glfwPostEmptyEvent</a></div><div class="ttdeci">void glfwPostEmptyEvent(void)</div><div class="ttdoc">Posts an empty event to the event queue.</div></div>
<div class="ttc" id="agroup__window_html_gabc58c47e9d93f6eb1862d615c3680f46"><div class="ttname"><a href="group__window.html#gabc58c47e9d93f6eb1862d615c3680f46">GLFWwindowfocusfun</a></div><div class="ttdeci">void(* GLFWwindowfocusfun)(GLFWwindow *window, int focused)</div><div class="ttdoc">The function pointer type for window focus callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1696</div></div>
<div class="ttc" id="agroup__window_html_gabe287973a21a8f927cde4db06b8dcbe9"><div class="ttname"><a href="group__window.html#gabe287973a21a8f927cde4db06b8dcbe9">GLFWwindowposfun</a></div><div class="ttdeci">void(* GLFWwindowposfun)(GLFWwindow *window, int xpos, int ypos)</div><div class="ttdoc">The function pointer type for window position callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1613</div></div>
<div class="ttc" id="agroup__window_html_gabf859b936d80961b7d39013a9694cc3e"><div class="ttname"><a href="group__window.html#gabf859b936d80961b7d39013a9694cc3e">GLFWwindowclosefun</a></div><div class="ttdeci">void(* GLFWwindowclosefun)(GLFWwindow *window)</div><div class="ttdoc">The function pointer type for window close callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1655</div></div>
<div class="ttc" id="agroup__window_html_gac2d83c4a10f071baf841f6730528e66c"><div class="ttname"><a href="group__window.html#gac2d83c4a10f071baf841f6730528e66c">glfwSetWindowFocusCallback</a></div><div class="ttdeci">GLFWwindowfocusfun glfwSetWindowFocusCallback(GLFWwindow *window, GLFWwindowfocusfun callback)</div><div class="ttdoc">Sets the focus callback for the specified window.</div></div>
<div class="ttc" id="agroup__window_html_gac314fa6cec7d2d307be9963e2709cc90"><div class="ttname"><a href="group__window.html#gac314fa6cec7d2d307be9963e2709cc90">glfwSetWindowSizeLimits</a></div><div class="ttdeci">void glfwSetWindowSizeLimits(GLFWwindow *window, int minwidth, int minheight, int maxwidth, int maxheight)</div><div class="ttdoc">Sets the size limits of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_gac31caeb3d1088831b13d2c8a156802e9"><div class="ttname"><a href="group__window.html#gac31caeb3d1088831b13d2c8a156802e9">glfwSetWindowOpacity</a></div><div class="ttdeci">void glfwSetWindowOpacity(GLFWwindow *window, float opacity)</div><div class="ttdoc">Sets the opacity of the whole window.</div></div>
<div class="ttc" id="agroup__window_html_gac6151765c54b789c4fe66c6bc6215953"><div class="ttname"><a href="group__window.html#gac6151765c54b789c4fe66c6bc6215953">glfwGetWindowTitle</a></div><div class="ttdeci">const char * glfwGetWindowTitle(GLFWwindow *window)</div><div class="ttdoc">Returns the title of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_gac793e9efd255567b5fb8b445052cfd3e"><div class="ttname"><a href="group__window.html#gac793e9efd255567b5fb8b445052cfd3e">glfwSetWindowIconifyCallback</a></div><div class="ttdeci">GLFWwindowiconifyfun glfwSetWindowIconifyCallback(GLFWwindow *window, GLFWwindowiconifyfun callback)</div><div class="ttdoc">Sets the iconify callback for the specified window.</div></div>
<div class="ttc" id="agroup__window_html_gacbe64c339fbd94885e62145563b6dc93"><div class="ttname"><a href="group__window.html#gacbe64c339fbd94885e62145563b6dc93">glfwSetWindowMaximizeCallback</a></div><div class="ttdeci">GLFWwindowmaximizefun glfwSetWindowMaximizeCallback(GLFWwindow *window, GLFWwindowmaximizefun callback)</div><div class="ttdoc">Sets the maximize callback for the specified window.</div></div>
<div class="ttc" id="agroup__window_html_gacccb29947ea4b16860ebef42c2cb9337"><div class="ttname"><a href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a></div><div class="ttdeci">int glfwGetWindowAttrib(GLFWwindow *window, int attrib)</div><div class="ttdoc">Returns an attribute of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_gacdf43e51376051d2c091662e9fe3d7b2"><div class="ttname"><a href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a></div><div class="ttdeci">void glfwDestroyWindow(GLFWwindow *window)</div><div class="ttdoc">Destroys the specified window and its context.</div></div>
<div class="ttc" id="agroup__window_html_gace2afda29b4116ec012e410a6819033e"><div class="ttname"><a href="group__window.html#gace2afda29b4116ec012e410a6819033e">glfwSetWindowAttrib</a></div><div class="ttdeci">void glfwSetWindowAttrib(GLFWwindow *window, int attrib, int value)</div><div class="ttdoc">Sets an attribute of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_gad09f0bd7a6307c4533b7061828480a84"><div class="ttname"><a href="group__window.html#gad09f0bd7a6307c4533b7061828480a84">glfwGetWindowOpacity</a></div><div class="ttdeci">float glfwGetWindowOpacity(GLFWwindow *window)</div><div class="ttdoc">Returns the opacity of the whole window.</div></div>
<div class="ttc" id="agroup__window_html_gad91b8b047a0c4c6033c38853864c34f8"><div class="ttname"><a href="group__window.html#gad91b8b047a0c4c6033c38853864c34f8">glfwSetWindowSizeCallback</a></div><div class="ttdeci">GLFWwindowsizefun glfwSetWindowSizeCallback(GLFWwindow *window, GLFWwindowsizefun callback)</div><div class="ttdoc">Sets the size callback for the specified window.</div></div>
<div class="ttc" id="agroup__window_html_gada646d775a7776a95ac000cfc1885331"><div class="ttname"><a href="group__window.html#gada646d775a7776a95ac000cfc1885331">glfwSetWindowCloseCallback</a></div><div class="ttdeci">GLFWwindowclosefun glfwSetWindowCloseCallback(GLFWwindow *window, GLFWwindowclosefun callback)</div><div class="ttdoc">Sets the close callback for the specified window.</div></div>
<div class="ttc" id="agroup__window_html_gadd7ccd39fe7a7d1f0904666ae5932dc5"><div class="ttname"><a href="group__window.html#gadd7ccd39fe7a7d1f0904666ae5932dc5">glfwSetWindowIcon</a></div><div class="ttdeci">void glfwSetWindowIcon(GLFWwindow *window, int count, const GLFWimage *images)</div><div class="ttdoc">Sets the icon for the specified window.</div></div>
<div class="ttc" id="agroup__window_html_gae18026e294dde685ed2e5f759533144d"><div class="ttname"><a href="group__window.html#gae18026e294dde685ed2e5f759533144d">GLFWframebuffersizefun</a></div><div class="ttdeci">void(* GLFWframebuffersizefun)(GLFWwindow *window, int width, int height)</div><div class="ttdoc">The function pointer type for framebuffer size callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1759</div></div>
<div class="ttc" id="agroup__window_html_gae77a4add0d2023ca21ff1443ced01653"><div class="ttname"><a href="group__window.html#gae77a4add0d2023ca21ff1443ced01653">glfwGetWindowUserPointer</a></div><div class="ttdeci">void * glfwGetWindowUserPointer(GLFWwindow *window)</div><div class="ttdoc">Returns the user pointer of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_gaec0282944bb810f6f3163ec02da90350"><div class="ttname"><a href="group__window.html#gaec0282944bb810f6f3163ec02da90350">GLFWwindowsizefun</a></div><div class="ttdeci">void(* GLFWwindowsizefun)(GLFWwindow *window, int width, int height)</div><div class="ttdoc">The function pointer type for window size callbacks.</div><div class="ttdef"><b>Definition</b> glfw3.h:1635</div></div>
<div class="ttc" id="agroup__window_html_gaeea7cbc03373a41fb51cfbf9f2a5d4c6"><div class="ttname"><a href="group__window.html#gaeea7cbc03373a41fb51cfbf9f2a5d4c6">glfwGetWindowSize</a></div><div class="ttdeci">void glfwGetWindowSize(GLFWwindow *window, int *width, int *height)</div><div class="ttdoc">Retrieves the size of the content area of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_gaf2832ebb5aa6c252a2d261de002c92d6"><div class="ttname"><a href="group__window.html#gaf2832ebb5aa6c252a2d261de002c92d6">glfwSetWindowContentScaleCallback</a></div><div class="ttdeci">GLFWwindowcontentscalefun glfwSetWindowContentScaleCallback(GLFWwindow *window, GLFWwindowcontentscalefun callback)</div><div class="ttdoc">Sets the window content scale callback for the specified window.</div></div>
<div class="ttc" id="agroup__window_html_gaf5d31de9c19c4f994facea64d2b3106c"><div class="ttname"><a href="group__window.html#gaf5d31de9c19c4f994facea64d2b3106c">glfwGetWindowContentScale</a></div><div class="ttdeci">void glfwGetWindowContentScale(GLFWwindow *window, float *xscale, float *yscale)</div><div class="ttdoc">Retrieves the content scale for the specified window.</div></div>
<div class="ttc" id="astruct_g_l_f_wallocator_html"><div class="ttname"><a href="struct_g_l_f_wallocator.html">GLFWallocator</a></div><div class="ttdoc">Custom heap memory allocator.</div><div class="ttdef"><b>Definition</b> glfw3.h:2138</div></div>
<div class="ttc" id="astruct_g_l_f_wallocator_html_a18a798136f17a9cb105be18312193bf7"><div class="ttname"><a href="struct_g_l_f_wallocator.html#a18a798136f17a9cb105be18312193bf7">GLFWallocator::allocate</a></div><div class="ttdeci">GLFWallocatefun allocate</div><div class="ttdef"><b>Definition</b> glfw3.h:2142</div></div>
<div class="ttc" id="astruct_g_l_f_wallocator_html_ab74cf9a969e73e6eb65a6112a591a988"><div class="ttname"><a href="struct_g_l_f_wallocator.html#ab74cf9a969e73e6eb65a6112a591a988">GLFWallocator::deallocate</a></div><div class="ttdeci">GLFWdeallocatefun deallocate</div><div class="ttdef"><b>Definition</b> glfw3.h:2150</div></div>
<div class="ttc" id="astruct_g_l_f_wallocator_html_af5a674af9e170095b968f467233437be"><div class="ttname"><a href="struct_g_l_f_wallocator.html#af5a674af9e170095b968f467233437be">GLFWallocator::reallocate</a></div><div class="ttdeci">GLFWreallocatefun reallocate</div><div class="ttdef"><b>Definition</b> glfw3.h:2146</div></div>
<div class="ttc" id="astruct_g_l_f_wallocator_html_af6153be74dbaf7f0a7e8bd3bfc039910"><div class="ttname"><a href="struct_g_l_f_wallocator.html#af6153be74dbaf7f0a7e8bd3bfc039910">GLFWallocator::user</a></div><div class="ttdeci">void * user</div><div class="ttdef"><b>Definition</b> glfw3.h:2154</div></div>
<div class="ttc" id="astruct_g_l_f_wgamepadstate_html"><div class="ttname"><a href="struct_g_l_f_wgamepadstate.html">GLFWgamepadstate</a></div><div class="ttdoc">Gamepad input state.</div><div class="ttdef"><b>Definition</b> glfw3.h:2114</div></div>
<div class="ttc" id="astruct_g_l_f_wgamepadstate_html_a27e9896b51c65df15fba2c7139bfdb9a"><div class="ttname"><a href="struct_g_l_f_wgamepadstate.html#a27e9896b51c65df15fba2c7139bfdb9a">GLFWgamepadstate::buttons</a></div><div class="ttdeci">unsigned char buttons[15]</div><div class="ttdef"><b>Definition</b> glfw3.h:2118</div></div>
<div class="ttc" id="astruct_g_l_f_wgamepadstate_html_a8b2c8939b1d31458de5359998375c189"><div class="ttname"><a href="struct_g_l_f_wgamepadstate.html#a8b2c8939b1d31458de5359998375c189">GLFWgamepadstate::axes</a></div><div class="ttdeci">float axes[6]</div><div class="ttdef"><b>Definition</b> glfw3.h:2122</div></div>
<div class="ttc" id="astruct_g_l_f_wgammaramp_html"><div class="ttname"><a href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a></div><div class="ttdoc">Gamma ramp.</div><div class="ttdef"><b>Definition</b> glfw3.h:2061</div></div>
<div class="ttc" id="astruct_g_l_f_wgammaramp_html_a2cce5d968734b685623eef913e635138"><div class="ttname"><a href="struct_g_l_f_wgammaramp.html#a2cce5d968734b685623eef913e635138">GLFWgammaramp::red</a></div><div class="ttdeci">unsigned short * red</div><div class="ttdef"><b>Definition</b> glfw3.h:2064</div></div>
<div class="ttc" id="astruct_g_l_f_wgammaramp_html_acf0c836d0efe29c392fe8d1a1042744b"><div class="ttname"><a href="struct_g_l_f_wgammaramp.html#acf0c836d0efe29c392fe8d1a1042744b">GLFWgammaramp::blue</a></div><div class="ttdeci">unsigned short * blue</div><div class="ttdef"><b>Definition</b> glfw3.h:2070</div></div>
<div class="ttc" id="astruct_g_l_f_wgammaramp_html_ad620e1cffbff9a32c51bca46301b59a5"><div class="ttname"><a href="struct_g_l_f_wgammaramp.html#ad620e1cffbff9a32c51bca46301b59a5">GLFWgammaramp::size</a></div><div class="ttdeci">unsigned int size</div><div class="ttdef"><b>Definition</b> glfw3.h:2073</div></div>
<div class="ttc" id="astruct_g_l_f_wgammaramp_html_affccc6f5df47820b6562d709da3a5a3a"><div class="ttname"><a href="struct_g_l_f_wgammaramp.html#affccc6f5df47820b6562d709da3a5a3a">GLFWgammaramp::green</a></div><div class="ttdeci">unsigned short * green</div><div class="ttdef"><b>Definition</b> glfw3.h:2067</div></div>
<div class="ttc" id="astruct_g_l_f_wimage_html"><div class="ttname"><a href="struct_g_l_f_wimage.html">GLFWimage</a></div><div class="ttdoc">Image data.</div><div class="ttdef"><b>Definition</b> glfw3.h:2090</div></div>
<div class="ttc" id="astruct_g_l_f_wimage_html_a0b7d95368f0c80d5e5c9875057c7dbec"><div class="ttname"><a href="struct_g_l_f_wimage.html#a0b7d95368f0c80d5e5c9875057c7dbec">GLFWimage::height</a></div><div class="ttdeci">int height</div><div class="ttdef"><b>Definition</b> glfw3.h:2096</div></div>
<div class="ttc" id="astruct_g_l_f_wimage_html_a0c532a5c2bb715555279b7817daba0fb"><div class="ttname"><a href="struct_g_l_f_wimage.html#a0c532a5c2bb715555279b7817daba0fb">GLFWimage::pixels</a></div><div class="ttdeci">unsigned char * pixels</div><div class="ttdef"><b>Definition</b> glfw3.h:2099</div></div>
<div class="ttc" id="astruct_g_l_f_wimage_html_af6a71cc999fe6d3aea31dd7e9687d835"><div class="ttname"><a href="struct_g_l_f_wimage.html#af6a71cc999fe6d3aea31dd7e9687d835">GLFWimage::width</a></div><div class="ttdeci">int width</div><div class="ttdef"><b>Definition</b> glfw3.h:2093</div></div>
<div class="ttc" id="astruct_g_l_f_wvidmode_html"><div class="ttname"><a href="struct_g_l_f_wvidmode.html">GLFWvidmode</a></div><div class="ttdoc">Video mode type.</div><div class="ttdef"><b>Definition</b> glfw3.h:2027</div></div>
<div class="ttc" id="astruct_g_l_f_wvidmode_html_a292fdd281f3485fb3ff102a5bda43faa"><div class="ttname"><a href="struct_g_l_f_wvidmode.html#a292fdd281f3485fb3ff102a5bda43faa">GLFWvidmode::greenBits</a></div><div class="ttdeci">int greenBits</div><div class="ttdef"><b>Definition</b> glfw3.h:2039</div></div>
<div class="ttc" id="astruct_g_l_f_wvidmode_html_a6066c4ecd251098700062d3b735dba1b"><div class="ttname"><a href="struct_g_l_f_wvidmode.html#a6066c4ecd251098700062d3b735dba1b">GLFWvidmode::redBits</a></div><div class="ttdeci">int redBits</div><div class="ttdef"><b>Definition</b> glfw3.h:2036</div></div>
<div class="ttc" id="astruct_g_l_f_wvidmode_html_a698dcb200562051a7249cb6ae154c71d"><div class="ttname"><a href="struct_g_l_f_wvidmode.html#a698dcb200562051a7249cb6ae154c71d">GLFWvidmode::width</a></div><div class="ttdeci">int width</div><div class="ttdef"><b>Definition</b> glfw3.h:2030</div></div>
<div class="ttc" id="astruct_g_l_f_wvidmode_html_a791bdd6c7697b09f7e9c97054bf05649"><div class="ttname"><a href="struct_g_l_f_wvidmode.html#a791bdd6c7697b09f7e9c97054bf05649">GLFWvidmode::refreshRate</a></div><div class="ttdeci">int refreshRate</div><div class="ttdef"><b>Definition</b> glfw3.h:2045</div></div>
<div class="ttc" id="astruct_g_l_f_wvidmode_html_ac65942a5f6981695517437a9d571d03c"><div class="ttname"><a href="struct_g_l_f_wvidmode.html#ac65942a5f6981695517437a9d571d03c">GLFWvidmode::height</a></div><div class="ttdeci">int height</div><div class="ttdef"><b>Definition</b> glfw3.h:2033</div></div>
<div class="ttc" id="astruct_g_l_f_wvidmode_html_af310977f58d2e3b188175b6e3d314047"><div class="ttname"><a href="struct_g_l_f_wvidmode.html#af310977f58d2e3b188175b6e3d314047">GLFWvidmode::blueBits</a></div><div class="ttdeci">int blueBits</div><div class="ttdef"><b>Definition</b> glfw3.h:2042</div></div>
</div><!-- fragment --></div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
