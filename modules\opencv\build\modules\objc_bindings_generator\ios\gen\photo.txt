PORTED FUNCs LIST (81 of 84):

 void cv::inpaint(<PERSON> src, <PERSON> inpaintMask, Mat& dst, double inpaintRadius, int flags)
 void cv::fastNlMeansDenoising(<PERSON> src, <PERSON>& dst, float h = 3, int templateWindowSize = 7, int searchWindowSize = 21)
 void cv::fastNlMeansDenoising(<PERSON> src, Mat& dst, vector_float hVector, int templateWindowSize = 7, int searchWindowSize = 21, int normType = NORM_L2)
 void cv::fastNlMeansDenoisingColored(Mat src, Mat& dst, float h = 3, float hColor = 3, int templateWindowSize = 7, int searchWindowSize = 21)
 void cv::fastNlMeansDenoisingMulti(vector_Mat srcImgs, Mat& dst, int imgToDenoiseIndex, int temporalWindowSize, float h = 3, int templateWindowSize = 7, int searchWindowSize = 21)
 void cv::fastNlMeansDenoisingMulti(vector_Mat srcImgs, Mat& dst, int imgToDenoiseIndex, int temporalWindowSize, vector_float hVector, int templateWindowSize = 7, int searchWindowSize = 21, int normType = NORM_L2)
 void cv::fastNlMeansDenoisingColoredMulti(vector_Mat srcImgs, Mat& dst, int imgToDenoiseIndex, int temporalWindowSize, float h = 3, float hColor = 3, int templateWindowSize = 7, int searchWindowSize = 21)
 void cv::denoise_TVL1(vector_Mat observations, Mat result, double lambda = 1.0, int niters = 30)
 Ptr_Tonemap cv::createTonemap(float gamma = 1.0f)
 Ptr_TonemapDrago cv::createTonemapDrago(float gamma = 1.0f, float saturation = 1.0f, float bias = 0.85f)
 Ptr_TonemapReinhard cv::createTonemapReinhard(float gamma = 1.0f, float intensity = 0.0f, float light_adapt = 1.0f, float color_adapt = 0.0f)
 Ptr_TonemapMantiuk cv::createTonemapMantiuk(float gamma = 1.0f, float scale = 0.7f, float saturation = 1.0f)
 Ptr_AlignMTB cv::createAlignMTB(int max_bits = 6, int exclude_range = 4, bool cut = true)
 Ptr_CalibrateDebevec cv::createCalibrateDebevec(int samples = 70, float lambda = 10.0f, bool random = false)
 Ptr_CalibrateRobertson cv::createCalibrateRobertson(int max_iter = 30, float threshold = 0.01f)
 Ptr_MergeDebevec cv::createMergeDebevec()
 Ptr_MergeMertens cv::createMergeMertens(float contrast_weight = 1.0f, float saturation_weight = 1.0f, float exposure_weight = 0.0f)
 Ptr_MergeRobertson cv::createMergeRobertson()
 void cv::decolor(Mat src, Mat& grayscale, Mat& color_boost)
 void cv::seamlessClone(Mat src, Mat dst, Mat mask, Point p, Mat& blend, int flags)
 void cv::colorChange(Mat src, Mat mask, Mat& dst, float red_mul = 1.0f, float green_mul = 1.0f, float blue_mul = 1.0f)
 void cv::illuminationChange(Mat src, Mat mask, Mat& dst, float alpha = 0.2f, float beta = 0.4f)
 void cv::textureFlattening(Mat src, Mat mask, Mat& dst, float low_threshold = 30, float high_threshold = 45, int kernel_size = 3)
 void cv::edgePreservingFilter(Mat src, Mat& dst, int flags = 1, float sigma_s = 60, float sigma_r = 0.4f)
 void cv::detailEnhance(Mat src, Mat& dst, float sigma_s = 10, float sigma_r = 0.15f)
 void cv::pencilSketch(Mat src, Mat& dst1, Mat& dst2, float sigma_s = 60, float sigma_r = 0.07f, float shade_factor = 0.02f)
 void cv::stylization(Mat src, Mat& dst, float sigma_s = 60, float sigma_r = 0.45f)
 void cv::AlignExposures::process(vector_Mat src, vector_Mat dst, Mat times, Mat response)
 void cv::AlignMTB::process(vector_Mat src, vector_Mat dst, Mat times, Mat response)
 void cv::AlignMTB::process(vector_Mat src, vector_Mat dst)
 Point cv::AlignMTB::calculateShift(Mat img0, Mat img1)
 void cv::AlignMTB::shiftMat(Mat src, Mat& dst, Point shift)
 void cv::AlignMTB::computeBitmaps(Mat img, Mat& tb, Mat& eb)
 int cv::AlignMTB::getMaxBits()
 void cv::AlignMTB::setMaxBits(int max_bits)
 int cv::AlignMTB::getExcludeRange()
 void cv::AlignMTB::setExcludeRange(int exclude_range)
 bool cv::AlignMTB::getCut()
 void cv::AlignMTB::setCut(bool value)
 void cv::CalibrateCRF::process(vector_Mat src, Mat& dst, Mat times)
 float cv::CalibrateDebevec::getLambda()
 void cv::CalibrateDebevec::setLambda(float lambda)
 int cv::CalibrateDebevec::getSamples()
 void cv::CalibrateDebevec::setSamples(int samples)
 bool cv::CalibrateDebevec::getRandom()
 void cv::CalibrateDebevec::setRandom(bool random)
 int cv::CalibrateRobertson::getMaxIter()
 void cv::CalibrateRobertson::setMaxIter(int max_iter)
 float cv::CalibrateRobertson::getThreshold()
 void cv::CalibrateRobertson::setThreshold(float threshold)
 Mat cv::CalibrateRobertson::getRadiance()
 void cv::MergeDebevec::process(vector_Mat src, Mat& dst, Mat times, Mat response)
 void cv::MergeDebevec::process(vector_Mat src, Mat& dst, Mat times)
 void cv::MergeExposures::process(vector_Mat src, Mat& dst, Mat times, Mat response)
 void cv::MergeMertens::process(vector_Mat src, Mat& dst, Mat times, Mat response)
 void cv::MergeMertens::process(vector_Mat src, Mat& dst)
 float cv::MergeMertens::getContrastWeight()
 void cv::MergeMertens::setContrastWeight(float contrast_weiht)
 float cv::MergeMertens::getSaturationWeight()
 void cv::MergeMertens::setSaturationWeight(float saturation_weight)
 float cv::MergeMertens::getExposureWeight()
 void cv::MergeMertens::setExposureWeight(float exposure_weight)
 void cv::MergeRobertson::process(vector_Mat src, Mat& dst, Mat times, Mat response)
 void cv::MergeRobertson::process(vector_Mat src, Mat& dst, Mat times)
 void cv::Tonemap::process(Mat src, Mat& dst)
 float cv::Tonemap::getGamma()
 void cv::Tonemap::setGamma(float gamma)
 float cv::TonemapDrago::getSaturation()
 void cv::TonemapDrago::setSaturation(float saturation)
 float cv::TonemapDrago::getBias()
 void cv::TonemapDrago::setBias(float bias)
 float cv::TonemapMantiuk::getScale()
 void cv::TonemapMantiuk::setScale(float scale)
 float cv::TonemapMantiuk::getSaturation()
 void cv::TonemapMantiuk::setSaturation(float saturation)
 float cv::TonemapReinhard::getIntensity()
 void cv::TonemapReinhard::setIntensity(float intensity)
 float cv::TonemapReinhard::getLightAdaptation()
 void cv::TonemapReinhard::setLightAdaptation(float light_adapt)
 float cv::TonemapReinhard::getColorAdaptation()
 void cv::TonemapReinhard::setColorAdaptation(float color_adapt)

SKIPPED FUNCs LIST (3 of 84):

 void cv::cuda::nonLocalMeans(GpuMat src, GpuMat& dst, float h, int search_window = 21, int block_size = 7, int borderMode = BORDER_DEFAULT,  _hidden_  stream = Stream::Null())
// Unknown type 'GpuMat' (I), skipping the function

 void cv::cuda::fastNlMeansDenoising(GpuMat src, GpuMat& dst, float h, int search_window = 21, int block_size = 7, Stream stream = Stream::Null())
// Unknown type 'GpuMat' (I), skipping the function

 void cv::cuda::fastNlMeansDenoisingColored(GpuMat src, GpuMat& dst, float h_luminance, float photo_render, int search_window = 21, int block_size = 7, Stream stream = Stream::Null())
// Unknown type 'GpuMat' (I), skipping the function


0 def args - 59 funcs
1 def args - 1 funcs
2 def args - 5 funcs
3 def args - 15 funcs
4 def args - 4 funcs