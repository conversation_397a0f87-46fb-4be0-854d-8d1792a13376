// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_System_Power_1_H
#define WINRT_Windows_System_Power_1_H
#include "winrt/impl/Windows.System.Power.0.h"
WINRT_EXPORT namespace winrt::Windows::System::Power
{
    struct WINRT_IMPL_EMPTY_BASES IBackgroundEnergyManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundEnergyManagerStatics>
    {
        IBackgroundEnergyManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IBackgroundEnergyManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IForegroundEnergyManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IForegroundEnergyManagerStatics>
    {
        IForegroundEnergyManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IForegroundEnergyManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPowerManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPowerManagerStatics>
    {
        IPowerManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IPowerManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
