/*
 *
 * Header exported from binary.
 * [./export-boostdesc.py BINBOOST binboost_128.bin]
 *
 */

// dimensionality of learner
static const int nDim = 128;

// orientations
static const int orientQuant = 8;

// patch size
static const int patchSize = 32;

// gradient assignment type
static const int iGradAssignType = ASSIGN_SOFT;

// number of weak learners
static const int nWLs = 32;

// threshold array (128 x 32)
static const unsigned int thresh[] =
{
 0x3dce789e, 0x3dc2a1eb, 0x3e2b2570, 0x3dd417ca, 0x3ded4ed3, 0x3dee9bbf, 0x3e31eaff, 0x3e2d056c,
 0x3dd4f052, 0x3de0168b, 0x3db00ff0, 0x3dcf965b, 0x3de7b1c0, 0x3e464d3c, 0x3def8056, 0x3dcf7db7,
 0x3e2ab25b, 0x3d6be81b, 0x3da45c79, 0x3e17f3cf, 0x3e419a41, 0x3dd5c7ce, 0x3df25786, 0x3e3eba28,
 0x3e1fa6df, 0x3dfe6ff8, 0x3ddbf056, 0x3de63d81, 0x3e03c686, 0x3e3d7d7c, 0x3e0b877b, 0x3dd0635a,
 0x3e31c715, 0x3ddba3ca, 0x3e3119ce, 0x3df24074, 0x3e3affb0, 0x3dd09071, 0x3dc77ac1, 0x3df77c89,
 0x3df17508, 0x3dd847f5, 0x3e4b9453, 0x3dc1aa24, 0x3e4c42a6, 0x3e2055fc, 0x3d5afa8d, 0x3e2adafd,
 0x3e412fd3, 0x3dfd145e, 0x3dd8d4fe, 0x3e044a62, 0x3dcd759f, 0x3dd6659d, 0x3d8b8979, 0x3df11d7a,
 0x3dd17721, 0x3de88377, 0x3e04c448, 0x3dc63d16, 0x3e211e00, 0x3de5cb36, 0x3da7f90e, 0x3e21a3f4,
 0x3dcb2e09, 0x3e3433d7, 0x3de56a38, 0x3e4ef912, 0x3dc8d442, 0x3dd64f12, 0x3df70bd0, 0x3dda493d,
 0x3de6f933, 0x3df13059, 0x3df18223, 0x3e4ba55d, 0x3dd67a10, 0x3dcf459e, 0x3dd7e34c, 0x3e404ee8,
 0x3e21d7dc, 0x3dc6937d, 0x3dd14120, 0x3df9fa98, 0x3e03b0c4, 0x3da202da, 0x3e325636, 0x3dc110c9,
 0x3da54ecc, 0x3daf55f9, 0x3de86f8b, 0x3e2c3090, 0x3e3230ba, 0x3d9c972a, 0x3df2fcad, 0x3dd4538f,
 0x3dcbf734, 0x3de04966, 0x3df1f4f5, 0x3dd5e5b0, 0x3dfaf3a1, 0x3e3b3073, 0x3dd1337f, 0x3e3a3f46,
 0x3d9902de, 0x3e2c89b1, 0x3dd3f39d, 0x3def2f98, 0x3ddffcdb, 0x3dc2e283, 0x3dc823bb, 0x3df4994a,
 0x3df5e743, 0x3de7ded7, 0x3e3e8188, 0x3db3ad4f, 0x3e25c076, 0x3e0c1df3, 0x3de37ac4, 0x3dcae544,
 0x3e25daf0, 0x3dc003e1, 0x3db1e372, 0x3de9667b, 0x3e084d77, 0x3da1537a, 0x3d962b0d, 0x3dfa2728,
 0x3de35a42, 0x3de7607c, 0x3e20a3d7, 0x3e22e771, 0x3dec0054, 0x3def56eb, 0x3e230447, 0x3de19221,
 0x3dd1ab08, 0x3dc8557c, 0x3de465a5, 0x3dfb7846, 0x3dcddf01, 0x3dd7d06c, 0x3dcc2d5d, 0x3e3f30e8,
 0x3df434a0, 0x3dd67b1c, 0x3e232d2c, 0x3ded6dc2, 0x3e50663c, 0x3df94b7b, 0x3df74928, 0x3de43c3a,
 0x3dce5040, 0x3dc18cbb, 0x3dda1123, 0x3dfbcec8, 0x3e4cbd12, 0x3daa0a2a, 0x3def8b59, 0x3ddf4c2b,
 0x3df03b82, 0x3e1e6fb5, 0x3dea4b99, 0x3dd16b12, 0x3df0df9c, 0x3e1fb4c4, 0x3db6deef, 0x3e24217d,
 0x3dfa77e6, 0x3dd538ac, 0x3df65669, 0x3e1bc48f, 0x3de3ec03, 0x3df01755, 0x3e1ffef4, 0x3e520ddc,
 0x3e574c0a, 0x3e07d34e, 0x3dec4b4d, 0x3df043e5, 0x3dc0c9b2, 0x3da78705, 0x3dcb76a6, 0x3dc1db87,
 0x3dee171a, 0x3de73cd5, 0x3decc78f, 0x3db1cfd6, 0x3e02d341, 0x3de27d89, 0x3dd159c5, 0x3db58073,
 0x3dbf7dfa, 0x3e4bf445, 0x3deca926, 0x3dd35dab, 0x3d9aa682, 0x3dc833c6, 0x3e36dacb, 0x3e0b4f1e,
 0x3e2976bc, 0x3dfddb12, 0x3dd9b457, 0x3e316c61, 0x3e13f5b6, 0x3e20902e, 0x3dd5fe54, 0x3df4434e,
 0x3dd5b3e1, 0x3deced4e, 0x3da08462, 0x3d927e0f, 0x3df22186, 0x3dfc45cc, 0x3dc902c3, 0x3d95b5aa,
 0x3deb3893, 0x3de1871e, 0x3db5035a, 0x3decd749, 0x3dd329c3, 0x3e059d99, 0x3e450b0f, 0x3deeb570,
 0x3e2f227d, 0x3dd0bfa1, 0x3d46a3f4, 0x3e219ce0, 0x3e342891, 0x3dca2644, 0x3de89870, 0x3de1b64e,
 0x3e22cb64, 0x3e5c21e2, 0x3dcf2c73, 0x3df9945b, 0x3ddf47fa, 0x3dc978c8, 0x3dd32053, 0x3dd401c5,
 0x3dc6a3b0, 0x3d42730c, 0x3dd21038, 0x3db2e2e1, 0x3e020104, 0x3e223748, 0x3da2b679, 0x3df682f9,
 0x3deeb357, 0x3e1f969e, 0x3df8eeaf, 0x3df9207d, 0x3dee3bcd, 0x3defeb4a, 0x3ddf223a, 0x3dcd117b,
 0x3dfa1ec5, 0x3e3af102, 0x3df244a6, 0x3e2fae79, 0x3dc92395, 0x3df3bafe, 0x3d96ce1b, 0x3dbf3875,
 0x3ddcf13d, 0x3e23bafe, 0x3ddd6f11, 0x3dd6ae7d, 0x3e2eb52d, 0x3e2340d5, 0x3cb4c27f, 0x3de7da1f,
 0x3df39fba, 0x3de80842, 0x3df83a54, 0x3e13b7d8, 0x3dc76de8, 0x3d9bd747, 0x3e2f5771, 0x3e5636b1,
 0x3df743e9, 0x3df578a3, 0x3e2943e1, 0x3e240b35, 0x3dfa73b4, 0x3e174de0, 0x3df53f7d, 0x3dd757d6,
 0x3e19e668, 0x3ded2136, 0x3dc38378, 0x3d89f087, 0x3e2b4203, 0x3dfa6b51, 0x3deb228e, 0x3dc6dd42,
 0x3dd28848, 0x3e2a0b5f, 0x3db28f6a, 0x3df2ebe6, 0x3dfbb9d0, 0x3e0152b1, 0x3dbd38f6, 0x3e1da1a9,
 0x3e172da1, 0x3dfc9928, 0x3dfe8a2f, 0x3dc7d0e5, 0x3df5885d, 0x3df5464e, 0x3db689a2, 0x3dce0978,
 0x3dd19371, 0x3db943e1, 0x3d9f53c5, 0x3e009bfa, 0x3dc2eb45, 0x3e004103, 0x3e0dfdf0, 0x3deb228e,
 0x3dcaa6fb, 0x3e3cca71, 0x3dd03105, 0x3e2f0a5f, 0x3dfd933e, 0x3df8433d, 0x3e242e99, 0x3de2046c,
 0x3dffd827, 0x3d8e4855, 0x3de9f948, 0x3df79852, 0x3dd2fb1a, 0x3e04bb5e, 0x3df2ba9d, 0x3e1b9a5b,
 0x3e219eb6, 0x3db3b9e4, 0x3e00fe48, 0x3dd741d1, 0x3ded373b, 0x3dfbc5df, 0x3df26634, 0x3e048a16,
 0x3dd5df65, 0x3dd73322, 0x3deef998, 0x3dd9f7f9, 0x3e27a701, 0x3dfdc161, 0x3ded7f95, 0x3de7136a,
 0x3e38fa7b, 0x3e1ab192, 0x3e505682, 0x3dc3e2fe, 0x3e20ded3, 0x3e4271bd, 0x3e00b84a, 0x3ddf6a94,
 0x3dfd5800, 0x3e37f456, 0x3dd57a35, 0x3df06f69, 0x3dcd80a1, 0x3dec2b52, 0x3de09dd0, 0x3e066384,
 0x3e0358f3, 0x3d840bfe, 0x3ddaa92e, 0x3e343244, 0x3e1a5376, 0x3d86b419, 0x3df04ddb, 0x3d9b5328,
 0x3e020e63, 0x3defc611, 0x3dfc7043, 0x3de7cd8a, 0x3dde1c9b, 0x3df71194, 0x3e078034, 0x3e0ea4a9,
 0x3dc17f92, 0x3daf18d7, 0x3dbda47e, 0x3e24317b, 0x3e45b35b, 0x3dd15a4b, 0x3df3397e, 0x3e190f73,
 0x3dfb78cd, 0x3df013a9, 0x3e13f35a, 0x3e63bbc7, 0x3e462670, 0x3da1a90a, 0x3dc76f2a, 0x3df477bc,
 0x3de347e9, 0x3dd4213a, 0x3df1db44, 0x3dfbb0e6, 0x3df7214f, 0x3dd81e8a, 0x3df8e970, 0x3db93327,
 0x3df2e515, 0x3da2c146, 0x3df224ab, 0x3dd1bd62, 0x3dedf612, 0x3dc30596, 0x3e12fa0d, 0x3dd7a5f4,
 0x3e593965, 0x3e380885, 0x3deef1bb, 0x3de4180d, 0x3dc3fb1c, 0x3e35b079, 0x3dd004fb, 0x3e503750,
 0x3dd4b2fb, 0x3df39757, 0x3df9bb28, 0x3de67ad9, 0x3da950ac, 0x3e206e1a, 0x3deb51bd, 0x3e2e69f0,
 0x3e2c6e2b, 0x3dcc8af3, 0x3df5d24a, 0x3d8e18c7, 0x3e03cab8, 0x3e2c3c9f, 0x3ddf6c27, 0x3e43454a,
 0x3e11f6cb, 0x3e43fd0d, 0x3dda5daf, 0x3df61cbd, 0x3de979e1, 0x3e0c52e7, 0x3e1472c1, 0x3e1a57eb,
 0x3e456ead, 0x3dec8cd6, 0x3dc77278, 0x3dc86006, 0x3dc4519e, 0x3e2366d8, 0x3df85511, 0x3de8b008,
 0x3e55043e, 0x3df3636f, 0x3dc44773, 0x3dd9dece, 0x3e02fb1a, 0x3dd49e88, 0x3db90d0a, 0x3dc5eff7,
 0x3df4e2b0, 0x3e53f812, 0x3dd7d73d, 0x3dd5d80e, 0x3e0c0c63, 0x3e265b64, 0x3e2bcf0b, 0x3df1ed9e,
 0x3df4141f, 0x3d93793f, 0x3dc0f0c1, 0x3e04cb5c, 0x3df36f7e, 0x3e2716d3, 0x3ddd9f4d, 0x3de976bc,
 0x3ddb65ed, 0x3db3344d, 0x3d9460d3, 0x3dd415b1, 0x3e5b2a28, 0x3dea009f, 0x3dfc011d, 0x3dae6ce0,
 0x3df2d773, 0x3e1f9a07, 0x3df21ee6, 0x3e64d595, 0x3de85016, 0x3dde78e2, 0x3dc2ade0, 0x3dfb6673,
 0x3e20574b, 0x3dcfd32c, 0x3dcb6b03, 0x3dd06fef, 0x3e00a287, 0x3dd8bbd4, 0x3d8cb619, 0x3e235372,
 0x3db9f10d, 0x3df9abf3, 0x3e006855, 0x3df7ab32, 0x3e510625, 0x3e21e215, 0x3df27807, 0x3d910873,
 0x3deaa088, 0x3d85989e, 0x3e2cfb76, 0x3dcb485b, 0x3db9ed39, 0x3e49d1f6, 0x3e10bbb2, 0x3e3cd035,
 0x3db07f8f, 0x3db6370f, 0x3dd197a2, 0x3dadbc16, 0x3dcedbb6, 0x3dd4e337, 0x3df56324, 0x3dcf8c65,
 0x3d89b025, 0x3e1c5048, 0x3e3dd2af, 0x3dfc7150, 0x3dfe9292, 0x3dfd76ee, 0x3e16b0d9, 0x3dfda2f9,
 0x3db90dee, 0x3e4a0c6b, 0x3e43bd5a, 0x3ddbde83, 0x3ddfcb92, 0x3dfac3eb, 0x3df83622, 0x3df70d63,
 0x3dbcb6d5, 0x3d8364a4, 0x3dcb9537, 0x3d90a493, 0x3e584fd3, 0x3e3393ab, 0x3db54e38, 0x3e26c871,
 0x3df59ff5, 0x3db7bf97, 0x3df09396, 0x3dfd7a9a, 0x3de9ccb8, 0x3db5ee21, 0x3e23c03c, 0x3e4c90c5,
 0x3df264a1, 0x3df8665e, 0x3dd09aed, 0x3dfcea6c, 0x3e06b8b7, 0x3dd70479, 0x3e04e5d6, 0x3dfb1855,
 0x3dbba875, 0x3e38d0cc, 0x3e035504, 0x3db6eeed, 0x3e1b7d41, 0x3e12aa19, 0x3dd16095, 0x3dd9ed7c,
 0x3df68be3, 0x3ddbc4d2, 0x3d7b0eca, 0x3df481b2, 0x3da81f89, 0x3dc492d7, 0x3e46b010, 0x3de66773,
 0x3dde08af, 0x3e1f15e8, 0x3df4c900, 0x3d7fc90e, 0x3dc127b3, 0x3db379fb, 0x3dda97e1, 0x3d874836,
 0x3dd5b5fa, 0x3e27db2b, 0x3de2efd4, 0x3de24edf, 0x3e326b2f, 0x3e04e486, 0x3defa7a8, 0x3dce9ab3,
 0x3e022d95, 0x3df1c21a, 0x3defa58f, 0x3dcbe03e, 0x3e476534, 0x3df1819d, 0x3dd7f173, 0x3defe692,
 0x3defab54, 0x3df6a37b, 0x3dd8ef35, 0x3dac43ce, 0x3df3165d, 0x3df1b36c, 0x3de691ea, 0x3e16c094,
 0x3dd44d44, 0x3dfd6627, 0x3db471cf, 0x3db2ba32, 0x3e3bb5e1, 0x3dc104ad, 0x3e27b5f2, 0x3df0307f,
 0x3db1dc0e, 0x3d2a7067, 0x3def74cd, 0x3e0e86c6, 0x3df0fe8b, 0x3e3c48f1, 0x3e100b03, 0x3dff7b5b,
 0x3e378b37, 0x3df19503, 0x3e45aaf8, 0x3ded7cf6, 0x3ddeb5b3, 0x3df3c4f4, 0x3de37bd0, 0x3db5f03a,
 0x3e476d11, 0x3db0bdb0, 0x3e4627bf, 0x3debfb9c, 0x3dd2a9d6, 0x3daa9103, 0x3d267ffc, 0x3e4fd439,
 0x3dceba28, 0x3dcab7dc, 0x3dc97204, 0x3dfb9e8c, 0x3e28f10b, 0x3e03b9f1, 0x3d8f75a4, 0x3df22292,
 0x3dd02eec, 0x3e0e95fb, 0x3e0cb1cd, 0x3dce429e, 0x3d75a477, 0x3e055b8a, 0x3dbec7c9, 0x3e1c6584,
 0x3da86021, 0x3dd757d6, 0x3e004253, 0x3df4d50f, 0x3df753a4, 0x3de0e842, 0x3e34c09c, 0x3dfbcd36,
 0x3dd8ac9f, 0x3dc7c02b, 0x3e307464, 0x3e53d35f, 0x3dc0677f, 0x3db5dcab, 0x3dde18f0, 0x3ddf78bc,
 0x3dc329de, 0x3e43d46b, 0x3de55e29, 0x3dfa4df4, 0x3dec710d, 0x3df1d580, 0x3e06d19e, 0x3df8e970,
 0x3df15b57, 0x3df2fcad, 0x3d8669cf, 0x3e0bacf7, 0x3e08754f, 0x3df4f616, 0x3e23c85c, 0x3e050acc,
 0x3dc52b25, 0x3e031df7, 0x3e382667, 0x3e3edd05, 0x3d946321, 0x3df1e4b4, 0x3dcf965b, 0x3dfd4739,
 0x3de05037, 0x3e4ad213, 0x3dd45953, 0x3d89dd64, 0x3df0bae9, 0x3e125786, 0x3e04245f, 0x3de3d817,
 0x3e4da273, 0x3dc25b5a, 0x3dc50085, 0x3def1d3f, 0x3ded5e07, 0x3dcb66d1, 0x3de5796c, 0x3df5fb2f,
 0x3e02680a, 0x3dbf9c63, 0x3d9c72ba, 0x3de40182, 0x3e154691, 0x3df9378f, 0x3e03ae25, 0x3dfa8c58,
 0x3dfccc8a, 0x3de12f90, 0x3e05de9c, 0x3df5c5b5, 0x3e327957, 0x3e1d2f5e, 0x3e0b9c73, 0x3dc05d88,
 0x3e5b3f21, 0x3e1d4068, 0x3dfb0ab3, 0x3e3a57a8, 0x3db18e32, 0x3e220ff5, 0x3dd3dc05, 0x3dcc6a72,
 0x3e32ac75, 0x3dd3fa6e, 0x3e410a57, 0x3da59fcd, 0x3e0a3444, 0x3dfaaef3, 0x3dfff045, 0x3e4a9b8d,
 0x3e4db403, 0x3e4bdd33, 0x3dcfd545, 0x3deeb91b, 0x3de3fdd6, 0x3df2a38c, 0x3ddeeb2d, 0x3df34df0,
 0x3de4d877, 0x3d8bc2b9, 0x3e403600, 0x3dfa57eb, 0x3ded5a5c, 0x3dd4a44c, 0x3e2f9a4a, 0x3d928eae,
 0x3dd97aab, 0x3de338b4, 0x3dc7e084, 0x3e2e7211, 0x3df63d3e, 0x3d8ea8cd, 0x3e289ce5, 0x3e3b239a,
 0x3dfee45c, 0x3dfbe6e6, 0x3e52a561, 0x3df2d3c8, 0x3e0b0c89, 0x3e000325, 0x3e5ead4f, 0x3ddb1e19,
 0x3deffd1e, 0x3e0ec13c, 0x3d97fce1, 0x3ded013b, 0x3df85a4f, 0x3dde0c5b, 0x3def09d9, 0x3e0579f2,
 0x3dd53a3f, 0x3de27aea, 0x3df17efe, 0x3de174c5, 0x3e21ee67, 0x3dfb0bbf, 0x3e06c0d7, 0x3ddac259,
 0x3dd57796, 0x3e5129cc, 0x3e29c348, 0x3d846cb9, 0x3dfa1cac, 0x3e1faa04, 0x3df6fe2e, 0x3e3af252,
 0x3ddf3e03, 0x3dc21d7c, 0x3ddfa440, 0x3dd0c1b9, 0x3de58c4c, 0x3e2111f1, 0x3defba02, 0x3df5d0b7,
 0x3dee5e68, 0x3de7fb26, 0x3e211234, 0x3e4cf744, 0x3df9c62a, 0x3dfb773a, 0x3df4dbe0, 0x3e00fd3b,
 0x3df3300e, 0x3db3ad69, 0x3df55b46, 0x3df2567a, 0x3dea3c64, 0x3e28316a, 0x3e009b74, 0x3e2adafd,
 0x3e3d7668, 0x3de2ba5a, 0x3df75ea6, 0x3d57f29b, 0x3dd4db59, 0x3dfe7b81, 0x3e28fabe, 0x3e4487fd,
 0x3e023359, 0x3d85cf90, 0x3df3311a, 0x3e21c21a, 0x3ddf8f47, 0x3ddb688c, 0x3de0da1b, 0x3df9389b,
 0x3e137931, 0x3d92f314, 0x3e696c83, 0x3d905db1, 0x3d92cc20, 0x3e0f48c3, 0x3e5d3997, 0x3dcd184c,
 0x3de809d5, 0x3d809a17, 0x3dd4fce7, 0x3e08e608, 0x3d7898b3, 0x3ddc7bcc, 0x3dd24c83, 0x3d8852dd,
 0x3db1a346, 0x3e367be5, 0x3df37436, 0x3dd6d86f, 0x3dc8bd59, 0x3d8456b4, 0x3dfb71fc, 0x3df0318c,
 0x3e007b35, 0x3e233615, 0x3deb07d1, 0x3dc66285, 0x3c9add9c, 0x3d43254e, 0x3e15521a, 0x3dc03b67,
 0x3e2b62c7, 0x3dd4cb9f, 0x3de365cb, 0x3dd4677b, 0x3df0a2cb, 0x3def2bed, 0x3dfc304d, 0x3d8639ae,
 0x3df8b47c, 0x3e114c66, 0x3dee45c3, 0x3df9ffd6, 0x3de152b1, 0x3de7967d, 0x3e0b08dd, 0x3dea93f3,
 0x3dbe755e, 0x3dd87d70, 0x3daea8f5, 0x3df3b081, 0x3db650f5, 0x3dd6d19e, 0x3e4b5e0f, 0x3dc400fc,
 0x3e29ca9f, 0x3dcee286, 0x3de49a99, 0x3de36c9c, 0x3de21a72, 0x3dc9310f, 0x3e3de508, 0x3e22274a,
 0x3d8a1e67, 0x3db19ded, 0x3d337f39, 0x3db54958, 0x3e0a9cdc, 0x3ddcee18, 0x3df0d5a6, 0x3dfd840a,
 0x3df94749, 0x3e116b55, 0x3e0aed14, 0x3de90710, 0x3df9e9d1, 0x3dd3964a, 0x3e03ab43, 0x3deae85c,
 0x3e309918, 0x3df2dc2b, 0x3e265dc0, 0x3dc5a675, 0x3e4df86e, 0x3e01c582, 0x3d8f965b, 0x3e038477,
 0x3dd126e9, 0x3df2fe3f, 0x3e034b51, 0x3e01dc94, 0x3dfa06a7, 0x3e20f6f1, 0x3dc5f8ab, 0x3da0b07a,
 0x3dd3b752, 0x3d8837ea, 0x3df6d4c3, 0x3e58711d, 0x3db73e76, 0x3de9f05f, 0x3db6fe57, 0x3e393144,
 0x3dfff1d8, 0x3e0292c5, 0x3db161e5, 0x3dfdcc64, 0x3dd973da, 0x3ded80a1, 0x3defa93b, 0x3dcdce3a,
 0x3e04cd31, 0x3d801422, 0x3d7be985, 0x3df87f88, 0x3d933d0e, 0x3dcd141a, 0x3df08030, 0x3dd76ee7,
 0x3e60370d, 0x3dc0e5d9, 0x3dc6015f, 0x3de8e608, 0x3e0321e6, 0x3e1fce74, 0x3e39a0ae, 0x3de19c9d,
 0x3e022ea1, 0x3df61f5c, 0x3dfb645a, 0x3b81a55c, 0x3de81909, 0x3d8d80ca, 0x3df1a6d7, 0x3dc99fc9,
 0x3e01904b, 0x3e03910c, 0x3e100b89, 0x3dc25113, 0x3e03bc0a, 0x3da85b84, 0x3df24181, 0x3dca31f5,
 0x3dfe236c, 0x3debd2b7, 0x3df5579b, 0x3df35222, 0x3dfc1c61, 0x3df6c7a8, 0x3dfe3b8a, 0x3dd5d0b7,
 0x3d6abca2, 0x3e289b95, 0x3dbfd1a7, 0x3df4506a, 0x3e27429a, 0x3dcadfb5, 0x3e2fa2f0, 0x3db910eb,
 0x3dea9824, 0x3e286b9c, 0x3dd92e1f, 0x3e03e187, 0x3de580c3, 0x3dbbcd86, 0x3dc7cf1c, 0x3e4d2418,
 0x3da37a8e, 0x3e09a34d, 0x3e0a832c, 0x3de719b5, 0x3dd91eeb, 0x3df10f52, 0x3e004bc2, 0x3e154b06,
 0x3e3c6328, 0x3d9a24cc, 0x3dbfffaf, 0x3dd06f69, 0x3defcefb, 0x3e01cb8a, 0x3e4ce63a, 0x3e454e6e,
 0x3dfd3aa3, 0x3dcec6bd, 0x3be91604, 0x3e0d2242, 0x3e0b4f61, 0x3dfc8755, 0x3daa5bd9, 0x3e168273,
 0x3d85d837, 0x3e36ddf0, 0x3d8b5576, 0x3e11c9f7, 0x3e0616b5, 0x3e0c60cc, 0x3dc65626, 0x3dfc2870,
 0x3e32ebe6, 0x3dc4875c, 0x3ddab3ab, 0x3de896dd, 0x3df4a01b, 0x3df88ca4, 0x3df58b82, 0x3d9a1ce2,
 0x3e00f51b, 0x3df159c5, 0x3df3b107, 0x3db81d99, 0x3db61383, 0x3dccc964, 0x3df30510, 0x3e2fe048,
 0x3e395a29, 0x3d997e3b, 0x3d381c8c, 0x3dd5f139, 0x3de9b49a, 0x3d9eb48c, 0x3d8d56b0, 0x3df1a976,
 0x3dbbcc51, 0x3e554d62, 0x3e131833, 0x3de99d02, 0x3dfe0ce1, 0x3df67a96, 0x3e03d641, 0x3dedbf05,
 0x3e083ec9, 0x3d453fc0, 0x3e013986, 0x3dd818c6, 0x3e070a81, 0x3dd90108, 0x3dceb13e, 0x3defb118,
 0x3ddf212d, 0x3def1e4b, 0x3dd5fb2f, 0x3e4f0026, 0x3dfa7f3d, 0x3e04cdb8, 0x3dc8a2c4, 0x3ddb6fe3,
 0x3d935273, 0x3d81d771, 0x3df27781, 0x3de05ee5, 0x3df0d845, 0x3de3f78c, 0x3e11975f, 0x3de128bf,
 0x3da6fc59, 0x3e3dc11e, 0x3d96c68e, 0x3dfb64e0, 0x3e500f34, 0x3dadf952, 0x3dfc98a2, 0x3df40896,
 0x3db24c40, 0x3dfbe7f3, 0x3de3f1c7, 0x3de3e6c5, 0x3e030e80, 0x3deb178b, 0x3dfb1184, 0x3def038e,
 0x3dc1263b, 0x3df640ea, 0x3dd13f07, 0x3d9f3733, 0x3e094a6f, 0x3e4192ea, 0x3de352eb, 0x3dd0856e,
 0x3de83c29, 0x3e253198, 0x3d7070fc, 0x3e1a732e, 0x3d8d9bca, 0x3df8b8ae, 0x3de4f00f, 0x3e071045,
 0x3e2aa25e, 0x3e0965b2, 0x3dce3bcd, 0x3dfed84d, 0x3e04d2f6, 0x3dc2125f, 0x3dfd96ea, 0x3e08f96e,
 0x3dedd723, 0x3e139cd8, 0x3e065e89, 0x3e1ad7d8, 0x3df72474, 0x3e310e88, 0x3dc2c8ed, 0x3d8400d3,
 0x3df5ee9a, 0x3e471972, 0x3e00ad47, 0x3dc4b9d9, 0x3db9bf67, 0x3dfdfeb9, 0x3db1ae2e, 0x3def5e42,
 0x3e0da76e, 0x3df92d99, 0x3de8bb90, 0x3e18f57f, 0x3ddb5bf7, 0x3daa5a47, 0x3e03b214, 0x3da738a4,
 0x3dd9f905, 0x3d2960b7, 0x3df7df1a, 0x3e035dee, 0x3e0f9f45, 0x3ddca31e, 0x3dc25d4a, 0x3df7a5f4,
 0x3e043138, 0x3e10ba1f, 0x3de57d9e, 0x3dc28325, 0x3df70c56, 0x3d86ddf0, 0x3d8b917e, 0x3df4855e,
 0x3dd90eaa, 0x3df5bab2, 0x3da4cd5a, 0x3e08e68e, 0x3da69bd3, 0x3e014446, 0x3dd336df, 0x3e40fc72,
 0x3df424e6, 0x3db43fe6, 0x3dc076b3, 0x3db66ac0, 0x3df22d0e, 0x3e5966be, 0x3dcd3cff, 0x3d8ceba1,
 0x3e3d40ab, 0x3dc26a8e, 0x3ddee4e2, 0x3df9c843, 0x3dfbaecd, 0x3e0e77d5, 0x3e00da1b, 0x3e28df7a,
 0x3df98288, 0x3d80eb17, 0x3e4afa72, 0x3e07db6e, 0x3d8d7577, 0x3dfb9f99, 0x3db22ec9, 0x3dedd1e5,
 0x3dc97f55, 0x3df2aae3, 0x3e11b047, 0x3dbd12d8, 0x3e06e47e, 0x3e03ab43, 0x3dbca743, 0x3e29fe87,
 0x3dd4face, 0x3df3d428, 0x3d935eed, 0x3ddeab36, 0x3e01a049, 0x3debd231, 0x3d848832, 0x3dfa7c18,
 0x3dfd3133, 0x3e1e2da5, 0x3d9abe92, 0x3dff7404, 0x3e0a31e8, 0x3d926e11, 0x3df94291, 0x3e0cfaf0,
 0x3e4b2af1, 0x3dcb5d6e, 0x3e1041cc, 0x3e003d9b, 0x3e173e68, 0x3dbc54bd, 0x3dfa5b10, 0x3dd9780c,
 0x3df265ae, 0x3e05f783, 0x3da78606, 0x3d9a9de9, 0x3d93c37c, 0x3d4f3bdd, 0x3dcdba4d, 0x3dd9af19,
 0x3d6c12ae, 0x3e053437, 0x3db3ca32, 0x3dbb07b6, 0x3e230d30, 0x3defb97c, 0x3e051e75, 0x3dd42785,
 0x3de1e710, 0x3e09b2c4, 0x3e0a511a, 0x3dd7b310, 0x3e044d87, 0x3e4dc550, 0x3dd9f234, 0x3d9c28b3,
 0x3dfbd167, 0x3e066f93, 0x3e0bf940, 0x3d9f6888, 0x3e00e0a8, 0x3ddcf030, 0x3e10ac3b, 0x3de23e18,
 0x3e0a3012, 0x3d9da0fb, 0x3de28fe2, 0x3df9bcba, 0x3db222c8, 0x3e3ec56d, 0x3dc1bb49, 0x3e21a650,
 0x3df53ac5, 0x3dd2dc2b, 0x3d90be94, 0x3e007421, 0x3dde4de4, 0x3e2f8a4c, 0x3de09aaa, 0x3d97b777,
 0x3e04fa8b, 0x3e06ae7d, 0x3d93c212, 0x3dacc4f0, 0x3da1d522, 0x3d894848, 0x3de3fc44, 0x3e09b0ee,
 0x3e62ae08, 0x3e016228, 0x3d3914bf, 0x3de11a11, 0x3df03a75, 0x3e33d70a, 0x3e074ff8, 0x3dd31013,
 0x3e042e12, 0x3dcbfdea, 0x3da43dcd, 0x3dfa1c26, 0x3e00168b, 0x3dea686e, 0x3d80fc4a, 0x3dd63993,
 0x3dac6038, 0x3df78dd6, 0x3de1bfbe, 0x3e11001d, 0x3df3b4b3, 0x3de33c60, 0x3e0f5cf2, 0x3de2834d,
 0x3dc6e72a, 0x3da318c7, 0x3e0753a4, 0x3dd3e250, 0x3e549907, 0x3e05ac8a, 0x3e0d4cba, 0x3e055ac0,
 0x3dbb3c8f, 0x3e09335d, 0x3e1ac53b, 0x3dd86877, 0x3d85d661, 0x3e26f47b, 0x3df918a0, 0x3deafa2f,
 0x3e0ad8a1, 0x3d994192, 0x3dfeec39, 0x3dd07faa, 0x3d87f116, 0x3dec6cdb, 0x3e0a12b6, 0x3e00ff97,
 0x3de8943e, 0x3d6fd022, 0x3e0224ee, 0x3df5ef20, 0x3daf6e3f, 0x3d9e77ad, 0x3e0de3fc, 0x3dfd1e54,
 0x3dd538ac, 0x3e41d25b, 0x3e0072d2, 0x3de831ad, 0x3db2f4c2, 0x3dfa6e76, 0x3d920f47, 0x3e05479d,
 0x3dc089c8, 0x3da881f2, 0x3de9eb20, 0x3df363f5, 0x3987752a, 0x3de8eb46, 0x3e0e5e24, 0x3d99ffa0,
 0x3dfef0f1, 0x3e07d6b6, 0x3e04c33b, 0x3dedb057, 0x3dfc4c9d, 0x3df9e0e7, 0x3e00ac7e, 0x3df4c661,
 0x3e03da73, 0x3d81370f, 0x3dfa543f, 0x3dd8bb4d, 0x3e447b67, 0x3e4dece5, 0x3dfa92a3, 0x3e09dc2f,
 0x3de979e1, 0x3de7a5b1, 0x3debfca8, 0x3dc5ec8e, 0x3daef3ee, 0x3d9ef284, 0x3df317f0, 0x3e053112,
 0x3dc2f888, 0x3e146174, 0x3df7cbb4, 0x3de60f5e, 0x3dcda986, 0x3de84838, 0x3df72043, 0x3d9e2534,
 0x3dd74470, 0x3de71bce, 0x3df5a188, 0x3dd09396, 0x3d6ead27, 0x3e0013ec, 0x3df36e72, 0x3de5c033,
 0x3dea8199, 0x3dff16b1, 0x3d82c2cb, 0x3de007dd, 0x3dd19696, 0x3e0735c2, 0x3db05377, 0x3dfa5ca3,
 0x3df5af29, 0x3e02fcad, 0x3e0351df, 0x3da0c2ee, 0x3e013b9f, 0x3dfdd65a, 0x3e0729b3, 0x3df594f2,
 0x3da1c68f, 0x3dcc45d9, 0x3e062457, 0x3e05ebfb, 0x3e02263e, 0x3dc547ee, 0x3de6e43b, 0x3db4babd,
 0x3e0b5632, 0x3de775fb, 0x3de0ff54, 0x3de4af0c, 0x3dbbb25e, 0x3e064064, 0x3e036695, 0x3df53a3f,
 0x3df9e40d, 0x3d9da702, 0x3db1b300, 0x3de52157, 0x3e4c83a9, 0x3e0885d3, 0x3e0e9468, 0x3c9a45fc,
 0x3dfec13c, 0x3e33ae25, 0x3df31b15, 0x3dee7e63, 0x3dd9aae7, 0x3ded6ece, 0x3dc88f43, 0x3dbe3b6f,
 0x3e042b73, 0x3e299cbf, 0x3db99da3, 0x3e06f08d, 0x3e0c7608, 0x3def69cb, 0x3dfb0ff1, 0x3dfd7b20,
 0x3d7da547, 0x3e045671, 0x3dce3bcd, 0x3de5accd, 0x3dd93f6c, 0x3e00114d, 0x3e4f5233, 0x3deebb34,
 0x3dd2a6b1, 0x3e088e37, 0x3df98fa3, 0x3e037d63, 0x3e0ea5f8, 0x3e00e9d5, 0x3dd8f57f, 0x3ddf10ed,
 0x3e041cc5, 0x3df82b1f, 0x3e1c7d5f, 0x3d8ac00a, 0x3e0ecb75, 0x3dea12f9, 0x3e3bd3c3, 0x3dff9e7c,
 0x3d987981, 0x3dea8d22, 0x3df6fe2e, 0x3de2699c, 0x3d8800a8, 0x3de83ec9, 0x3e008bfc, 0x3df2e408,
 0x3dfce209, 0x3dd47c74, 0x3dd367a1, 0x3df43e96, 0x3e0040c0, 0x3e07ddca, 0x3e021d54, 0x3dfbdbe4,
 0x3dbaf01e, 0x3e652a84, 0x3dfc5e70, 0x3e09b49a, 0x3debc83b, 0x3dffaf42, 0x3d99b43c, 0x3e1122fb,
 0x3e0eed46, 0x3e069729, 0x3dc32fd8, 0x3e0b313c, 0x3dffc6da, 0x3df4fa48, 0x3de30caa, 0x3e04e5d6,
 0x3d9b9438, 0x3dabc6d0, 0x3e1027d9, 0x3dcdaa93, 0x3df54d1f, 0x3dd5aa71, 0x3de6c6de, 0x3dd0507a,
 0x3ddde2ef, 0x3dfc219f, 0x3dd230ba, 0x3d8e93fd, 0x3de6c00e, 0x3dcd6dc2, 0x3e5c98a2, 0x3e5a17f4,
 0x3e001bca, 0x3e13dd98, 0x3dde8816, 0x3df4ff86, 0x3e520e1f, 0x3e10a783, 0x3dc0e2a6, 0x3e0d4c77,
 0x3de9ba5e, 0x3dfb3b75, 0x3e0074a7, 0x3dd5653d, 0x3dd86fce, 0x3e0e642c, 0x3e0c7d5f, 0x3dcf52b9,
 0x3e137eb3, 0x3d0ad348, 0x3e226070, 0x3da9a06a, 0x3df22c02, 0x3dfc0db2, 0x3e003eea, 0x3e02f33d,
 0x3e00fb22, 0x3dee67d7, 0x3dd234ec, 0x3ddb3637, 0x3dccbef5, 0x3e0b3d4b, 0x3da83f9f, 0x3d9a9bb5,
 0x3e545fe1, 0x3dc4613e, 0x3df1e860, 0x3ddd5c31, 0x3de8f8e8, 0x3e07ccc0, 0x3dd28284, 0x3e054089,
 0x3ddd48cb, 0x3e0eb570, 0x3e064f98, 0x3e02de01, 0x3dea73f7, 0x3d7e69e3, 0x3e03cf2d, 0x3e007cc8,
 0x3d997fb3, 0x3e0498c4, 0x3da84655, 0x3dc599fb, 0x3e013123, 0x3d982c2c, 0x3dcc3cc7, 0x3df2e59b,
 0x3d860d53, 0x3df553ef, 0x3de22ee4, 0x3dc29960, 0x3dabf460, 0x3dadb5f3, 0x3dd65fd9, 0x3df5d788,
 0x3df0fe8b, 0x3d8d84ee, 0x3e17caea, 0x3e043f60, 0x3da01770, 0x3dfc4d23, 0x3e05f45e, 0x3dfecbb8,
 0x3e08a97a, 0x3dd927d4, 0x3db25205, 0x3e08eef2, 0x3e063e4b, 0x3e0be61d, 0x3de9fe87, 0x3e02dba5,
 0x3df5757d, 0x3dcfd545, 0x3ddb5c7d, 0x3dbd9176, 0x3e063e08, 0x3db4c009, 0x3dd77100, 0x3de1e2df,
 0x3e26d699, 0x3dc9bc34, 0x3e14d120, 0x3e0bc515, 0x3d88699e, 0x3d843e53, 0x3e001d9f, 0x3da6ec83,
 0x3df88ca4, 0x3e006573, 0x3e060392, 0x3e00318c, 0x3e039eae, 0x3de09fe8, 0x3e12b303, 0x3e01f42c,
 0x3dfdd5d4, 0x3de0639d, 0x3de1b435, 0x3e04e875, 0x3de2749f, 0x3e13111f, 0x3e060914, 0x3dbbc0d6,
 0x3deb29e5, 0x3e02d01c, 0x3dfba1b2, 0x3e021f2a, 0x3dda9e2c, 0x3d1607fa, 0x3e03547e, 0x3e03a9f4,
 0x3e0dedf2, 0x3de65626, 0x3dc41538, 0x3de3dddb, 0x3e056ffc, 0x3e085be2, 0x3e0f75da, 0x3e526959,
 0x3de4d445, 0x3de5a038, 0x3df9a390, 0x3e0b938a, 0x3dc39e50, 0x3dea1dfc, 0x3e0d2b2c, 0x3dfbd599,
 0x3defe47a, 0x3e000c0f, 0x3dc0073c, 0x3e2b4bb6, 0x3dd70e6f, 0x3e0662bb, 0x3dccd291, 0x3e040c84,
 0x3df2ba17, 0x3e10c38f, 0x3dd22d95, 0x3e03c750, 0x3dd1afc0, 0x3e06106b, 0x3e08e087, 0x3e086c22,
 0x3de78c00, 0x3e030b5b, 0x3da0eaac, 0x3d83496e, 0x3de6a11f, 0x3e48ccde, 0x3dba59f6, 0x3dd56862,
 0x3e4fc7a4, 0x3e032379, 0x3ddda943, 0x3de8a698, 0x3e08c2e7, 0x3df22fad, 0x3db3ce8c, 0x3d919b76,
 0x3d9e2ac3, 0x3dae22f3, 0x3ddd3ed5, 0x3e05e0f8, 0x3dfca6ca, 0x3e01322f, 0x3e493251, 0x3e03c750,
 0x3e0739b0, 0x3de6687f, 0x3dfd48cb, 0x3da09198, 0x3e0402d1, 0x3dd7d06c, 0x3e20ab2e, 0x3ddcd680,
 0x3e04a665, 0x3deb6178, 0x3da11b61, 0x3df6abde, 0x3de5668c, 0x3dcce9a3, 0x3e4f75da, 0x3e03e0bd,
 0x3d13d5a0, 0x3e129d41, 0x3def35e3, 0x3e0b7f9d, 0x3deb5c3a, 0x3e52f406, 0x3e016723, 0x3e0c93a7,
 0x3ddb1d0d, 0x3e021c8a, 0x3e10e23b, 0x3da788f6, 0x3dbeb789, 0x3df8ff76, 0x3e01083e, 0x3df75968,
 0x3dadfb94, 0x3dfd4739, 0x3da9142b, 0x3d177d7a, 0x3dd7c88e, 0x3ded2e51, 0x3d686057, 0x3debb55b,
 0x3dccd20b, 0x3d80dc26, 0x3e05d5b2, 0x3e0a47ed, 0x3ddc89f4, 0x3de1b329, 0x3dd23b36, 0x3e012d77,
 0x3dfbff8b, 0x3e0970b5, 0x3e01016d, 0x3db24fc4, 0x3e0593a3, 0x3dfe0829, 0x3df62ae5, 0x3e0b3dd1,
 0x3dd57bc8, 0x3daf33d8, 0x3deabde4, 0x3df1e109, 0x3e0355ce, 0x3dc13de0, 0x3dc960aa, 0x3e00abf7,
 0x3dbe3a70, 0x3cd380b1, 0x3e2f062d, 0x3dd97a25, 0x3e063843, 0x3d91e499, 0x3decdb7b, 0x3e0d0a24,
 0x3e077964, 0x3db0b524, 0x3de1bd1f, 0x3e058dde, 0x3df2d4d4, 0x3ddd4f9c, 0x3dee6299, 0x3dfb5526,
 0x3d95d3dd, 0x3df45e92, 0x3dc76592, 0x3dd91cd2, 0x3de6b50b, 0x3dfe0158, 0x3dc413a6, 0x3e0a8da8,
 0x3df88db0, 0x3dd015c2, 0x3df1348b, 0x3e02a8ca, 0x3dd62ae5, 0x3e0abe27, 0x3e046c76, 0x3dd69ec3,
 0x3dbd36ea, 0x3de8eb46, 0x3ded39da, 0x3dbb853a, 0x3dcec850, 0x3e05bc02, 0x3d9a99d2, 0x3e066a12,
 0x3dc264ff, 0x3e1f737e, 0x3df81388, 0x3dfc054f, 0x3df4445b, 0x3dfc08fa, 0x3de68e3f, 0x3e0faca3,
 0x3e13dea4, 0x3def7447, 0x3e0ab7dc, 0x3dff6018, 0x3dc3cb16, 0x3de0d5e9, 0x3de1d649, 0x3dfe58e6,
 0x3ddf034b, 0x3df615ec, 0x3df1fd59, 0x3e04eb14, 0x3e12888b, 0x3dfb1c86, 0x3df99cbf, 0x3d800a62,
 0x3dc4b414, 0x3defba88, 0x3dec4b4d, 0x3e0a0b1c, 0x3d9f465a, 0x3df6ce79, 0x3cecd4e0, 0x3e063ed1,
 0x3ddfea81, 0x3de1c0ca, 0x3de9a77e, 0x3dc03600, 0x3df479d5, 0x3dbe3f6b, 0x3db3ea2d, 0x3dd90fb6,
 0x3ddd9989, 0x3deccff2, 0x3de3675e, 0x3e19fc2b, 0x3dec3548, 0x3dab3a9e, 0x3dfd68c7, 0x3df5dc40,
 0x3dd5feda, 0x3ddbad3a, 0x3d965d11, 0x3d72f901, 0x3dfbc2b9, 0x3db92161, 0x3e238759, 0x3d192e1f,
 0x3db983ca, 0x3e0812be, 0x3da10518, 0x3df9799e, 0x3db2d68f, 0x3e02ebe6, 0x3e0855da, 0x3dd7585c,
 0x3dcbb92f, 0x3d97bdb4, 0x3e4c11e4, 0x3dfd0397, 0x3e0bcf0b, 0x3de7aa69, 0x3dc10533, 0x3d76ec76,
 0x3dfc9eed, 0x3e09c172, 0x3e10ae10, 0x3de90258, 0x3dea7b4e, 0x3e081345, 0x3df56755, 0x3e4d872f,
 0x3dab8edf, 0x3e00acc1, 0x3ddf42bb, 0x3e05cb79, 0x3e17baaa, 0x3dfa0be5, 0x3da0178a, 0x3df91e64,
 0x3da1084b, 0x3ddfb3fa, 0x3e002928, 0x3d96fc16, 0x3dfd46b2, 0x3e0a17f4, 0x3de5521a, 0x3df023ea,
 0x3dffd695, 0x3dfe5109, 0x3e0075b4, 0x3dce12e8, 0x3de73040, 0x3df30596, 0x3d9185f7, 0x3d0763f2,
 0x3de86df8, 0x3e129c78, 0x3e017df2, 0x3dfa5658, 0x3e0a1987, 0x3db04253, 0x3dc67d93, 0x3e183f92,
 0x3da0edb6, 0x3df595ff, 0x3ddcbee8, 0x3db4a7f8, 0x3d82883b, 0x3df76146, 0x3d9ae7d5, 0x3dfa9d1f,
 0x3df6fa83, 0x3e05f06f, 0x3daf7a41, 0x3e520685, 0x3dfc579f, 0x3ddd8d7a, 0x3d881883, 0x3dd6b767,
 0x3e131ceb, 0x3db98004, 0x3e078b7a, 0x3e00cae6, 0x3de3bb41, 0x3e0a1123, 0x3d944765, 0x3df79182,
 0x3e25e13b, 0x3dcd7b63, 0x3e3f91a3, 0x3e04aa97, 0x3e50092d, 0x3dc281c8, 0x3db7c7d3, 0x3defe9b8,
 0x3de9dae0, 0x3e0c7086, 0x3ded6c2f, 0x3de47a9e, 0x3dfed95a, 0x3e20dc33, 0x3df2a51e, 0x3ded2e51,
 0x3da21526, 0x3df4e1a4, 0x3e24534c, 0x3ddfea81, 0x3e105c46, 0x3e09e0e7, 0x3df7c0b1, 0x3dc4a622,
 0x3ddeed46, 0x3df8348f, 0x3dce379b, 0x3dfb688c, 0x3e066a12, 0x3dda4d6e, 0x3dee8d11, 0x3e488616,
 0x3db50cf2, 0x3df56191, 0x3dfe2bd0, 0x3e596356, 0x3e0c8430, 0x3e0b7e91, 0x3dade340, 0x3e4b5914,
 0x3e02220c, 0x3deb0963, 0x3d9979fc, 0x3ddec0b5, 0x3dc6b02b, 0x3deb838c, 0x3dc054e2, 0x3de3454a,
 0x3debd662, 0x3d9859bb, 0x3df1e18f, 0x3e0eb745, 0x3e39e668, 0x3dfaab47, 0x3dec2e77, 0x3df9b2c4,
 0x3dec3116, 0x3defc8b0, 0x3e06ffc1, 0x3dfbc1ad, 0x3dd2d4d4, 0x3dcdf71f, 0x3dfa921d, 0x3e0679cc,
 0x3e420b3d, 0x3deac42f, 0x3e0523f6, 0x3df69015, 0x3d8015a7, 0x3dd717df, 0x3d86494d, 0x3db87394,
 0x3df9f87f, 0x3dfb0b39, 0x3de31f04, 0x3dd71002, 0x3de2bf98, 0x3d209303, 0x3dbe51c5, 0x3df6512b,
 0x3d0ce506, 0x3de6b160, 0x3dcb8e08, 0x3e06b0d9, 0x3decc250, 0x3d97e2ee, 0x3e0dedf2, 0x3e180c31,
 0x3e137cdd, 0x3de44178, 0x3dd2f6e8, 0x3d649002, 0x3de4abe7, 0x3dedaa0d, 0x3e134efd, 0x3e0809d5,
 0x3df769a9, 0x3dd053a0, 0x3dd07c85, 0x3d80b9b4, 0x3d9cacc4, 0x3de5e13b, 0x3df254e7, 0x3dda7547,
 0x3dc2eaf4, 0x3dd3775c, 0x3e01d0c8, 0x3d704197, 0x3df4d7ae, 0x3e0224ee, 0x3de77a2d, 0x3dfc0874,
 0x3dbd617d, 0x3dca74ce, 0x3dfc9258, 0x3e089d6b, 0x3e01e9af, 0x3e0de00d, 0x3de12dfd, 0x3dce4c0e,
 0x3df0d280, 0x3e4a4f01, 0x3dc82e37, 0x3de1b2a2, 0x3d9ef000, 0x3e0f7553, 0x3df9042e, 0x3e0a54c5,
 0x3db9294c, 0x3e03250b, 0x3e2d95dd, 0x3e0ce2d2, 0x3e39cb25, 0x3df29889, 0x3dfa8501, 0x3e132d6f,
 0x3dd50d28, 0x3dd8a32f, 0x3decf4a5, 0x3e03b214, 0x3b86d9ce, 0x3df11f0c, 0x3dd30d74, 0x3d878898,
 0x3ddd12cb, 0x3e1167ec, 0x3d80e030, 0x3e057082, 0x3dc65640, 0x3e08c21e, 0x3df6976c, 0x3dc6ca1f,
 0x3d753297, 0x3d8da6a4, 0x3d894af5, 0x3e018dac, 0x3e00f3cb, 0x3df7af64, 0x3e069be1, 0x3da32d7c,
 0x3db8f565, 0x3e143cc0, 0x3ddeb075, 0x3e029521, 0x3db62c85, 0x3d38ed1c, 0x3dc82cda, 0x3dcbdd33,
 0x3df8d79d, 0x3e10e12f, 0x3e034d6a, 0x3d8e8cce, 0x3dd543af, 0x3dedbe7f, 0x3e4cbb3d, 0x3dfb5aea,
 0x3da7773d, 0x3e246174, 0x3de7418d, 0x3df496ab, 0x3e188b54, 0x3e0a5bd9, 0x3dc0cbca, 0x3e0d4d83,
 0x3e37dfe3, 0x3dc24dd3, 0x3dcf5126, 0x3e0da8bd, 0x3def6c6a, 0x3e029cbb, 0x3e02deca, 0x3de0d3d0,
 0x3de91d15, 0x3e04e69f, 0x3e156042, 0x3e18e25d, 0x3d7cb17c, 0x3e08476f, 0x3da25786, 0x3e0774ac,
 0x3dbf299e, 0x3decd0ff, 0x3e124702, 0x3dfe0efa, 0x3e0e5322, 0x3dd3bfb6, 0x3e573083, 0x3df6976c,
 0x3de3a4b5, 0x3dd69b9d, 0x3e188ebd, 0x3da4e2e6, 0x3df85f07, 0x3d9350e0, 0x3ddfaca3, 0x3df11c6d,
 0x3e56c58f, 0x3dd85597, 0x3d9611ba, 0x3e0c12ae, 0x3e0670e3, 0x3db89382, 0x3dd5c183, 0x3e086402,
 0x3df24d90, 0x3dafe685, 0x3dd05964, 0x3dea93f3, 0x3dd13059, 0x3db44e5e, 0x3e011a54, 0x3dcf33ca,
 0x3e21db01, 0x3de007dd, 0x3e0ab218, 0x3e4e9c88, 0x3e07036d, 0x3dcc67aa, 0x3e0b45f1, 0x3dfbe8ff,
 0x3dc882f1, 0x3db8f19e, 0x3d9304b2, 0x3e068b1a, 0x3ddcdcca, 0x3e01df76, 0x3d91c194, 0x3df0aaa8,
 0x3df7329c, 0x3e0d31ba, 0x3de7cf1c, 0x3d891c9c, 0x3ddb0ee5, 0x3e141249, 0x3de76c05, 0x3dc7f03f,
 0x3dadbc9c, 0x3de11340, 0x3df593e6, 0x3da43577, 0x3d34bbe4, 0x3df8811b, 0x3dabc30a, 0x3d8983af,
 0x3de7b917, 0x3e0543f2, 0x3e01d1d4, 0x3e01f0c3, 0x3ddff152, 0x3e03e4ef, 0x3deb72c5, 0x3e223ab0,
 0x3dd518b1, 0x3e04113c, 0x3e12d59d, 0x3db68cc7, 0x3dc9a77e, 0x3db6f70d, 0x3e0405b4, 0x3ddd2631,
 0x3d9e4a60, 0x3dc5ca5f, 0x3de7db2b, 0x3e090796, 0x3e045847, 0x3e0b2bbb, 0x3dfd5e4a, 0x3de8038a,
 0x3e01348b, 0x3dd40fed, 0x3dddb1a7, 0x3dfa1f4b, 0x3dd191de, 0x3df52b0a, 0x3df1a544, 0x3dd8b157,
 0x3e035bd5, 0x3dea52f0, 0x3e02c27a, 0x3e144a1f, 0x3dfe39f7, 0x3dbe0130, 0x3e06b1e6, 0x3e0508b3,
 0x3ddff3f1, 0x3e0605ab, 0x3dfc382a, 0x3de6e86c, 0x3e1f4e44, 0x3dd9d3cc, 0x3db73b35, 0x3e008744,
 0x3de22a2c, 0x3d6f1cd3, 0x3ded2c38, 0x3dd79f23, 0x3ddcf030, 0x3dc0cb52, 0x3daab69a, 0x3da3c354,
 0x3db2b4f4, 0x3e089763, 0x3dec3223, 0x3dca8f05, 0x3ddabf34, 0x3d7ffd7c, 0x3dfa98ee, 0x3df08cc5,
 0x3da2c2a3, 0x3d63108b, 0x3e0c22ee, 0x3dffcf3e, 0x3de69f8c, 0x3df359ff, 0x3d9c8b36, 0x3df75c07,
 0x3de58601, 0x3e06cca3, 0x3da0d555, 0x3df0fc72, 0x3df00ef1, 0x3ddefae8, 0x3dcde00d, 0x3dc3e2ae,
 0x3e1ec248, 0x3dceb357, 0x3dd1422d, 0x3dd9f773, 0x3e092ab7, 0x3defb86f, 0x3decc250, 0x3e3f5558,
 0x3df39c0f, 0x3df22f27, 0x3de83b1d, 0x3e0489d2, 0x3d8a9aa9, 0x3de6c11a, 0x3dd07a6c, 0x3db16ed8,
 0x3dbfb0fd, 0x3e105921, 0x3dcce78a, 0x3e08d152, 0x3df40fed, 0x3df9d777, 0x3ddeb420, 0x3dbb0806,
 0x3de0e411, 0x3e0b8bac, 0x3df54e2b, 0x3df7279a, 0x3def2b67, 0x3de6fa40, 0x3db0380c, 0x3de4ecea,
 0x3e00b029, 0x3de808c8, 0x3df34d6a, 0x3dbf3db3, 0x3e02dfd7, 0x3d4bfe05, 0x3dc1bfcb, 0x3d5d44cf,
 0x3de95183, 0x3d82a3dc, 0x3e08f53c, 0x3d8a47b7, 0x3de93b7e, 0x3d998344, 0x3dc57d4d, 0x3e194318,
 0x3d9c37cc, 0x3dda5333, 0x3dc19a19, 0x3ded7f95, 0x3e04f052, 0x3d9c9bad, 0x3de0a394, 0x3dfd5b25,
 0x3de98bb5, 0x3e053a82, 0x3e0071c5, 0x3debbcb2, 0x3dd90753, 0x3e030878, 0x3df38045, 0x3dfa8262,
 0x3cf566ea, 0x3de97c81, 0x3dd52652, 0x3d8e326b, 0x3dc63107, 0x3e07c84b, 0x3e010e45, 0x3e025743,
 0x3de515cf, 0x3df95810, 0x3e0a6bd7, 0x3dc8b2cf, 0x3dd15c64, 0x3db4b960, 0x3de5e3da, 0x3de3504c,
 0x3dcd43d0, 0x3e20c133, 0x3de6b2f2, 0x3db033bf, 0x3de990f3, 0x3ddb584b, 0x3e0613d3, 0x3df890d6,
 0x3dec3fc4, 0x3e4dc93f, 0x3dfaeddd, 0x3d9b5e7b, 0x3df104d5, 0x3de45b29, 0x3de4a277, 0x3e677f28,
 0x3dc9c299, 0x3dc4837a, 0x3dff9379, 0x3e1b70ac, 0x3e499d45, 0x3dea209b, 0x3df1cfbc, 0x3de239e7,
 0x3df9c7bd, 0x3e1970f8, 0x3dd6c82e, 0x3dfb36bd, 0x3df287c2, 0x3df77d0f, 0x3d606dc9, 0x3e03b603,
 0x3e0a7eb7, 0x3de21102, 0x3dfe5647, 0x3e059f2c, 0x3dd9abf3, 0x3d9e6bee, 0x3df34efd, 0x3ddcee18,
 0x3e15eaab, 0x3df68ad7, 0x3dca7661, 0x3dea73f7, 0x3e0c358b, 0x3e03f184, 0x3ddd93c4, 0x3e0b9a9e,
 0x3e046ed2, 0x3dd6b5d4, 0x3df3e4ef, 0x3da28e35, 0x3e2c74fb, 0x3e0600b0, 0x3dca4f95, 0x3d8e7d3c,
 0x3de82731, 0x3e041398, 0x3df20429, 0x3e055001, 0x3dc60129, 0x3dd9b670, 0x3de5b424, 0x3e099136,
 0x3e677a2d, 0x3dd550ca, 0x3de9270b, 0x3df31983, 0x3dd1476b, 0x3dc9cd8f, 0x3dbb0476, 0x3e0439de,
 0x3df8e326, 0x3e042dcf, 0x3ddb4edb, 0x3e0b7910, 0x3dcef673, 0x3dc6c0bc, 0x3de51eb8, 0x3dedeb10,
 0x3dec6046, 0x3d9f4af7, 0x3dcecaef, 0x3de10f0f, 0x3ddeb207, 0x3d6bda0e, 0x3e00025c, 0x3df20d99,
 0x3d9f8bc4, 0x3e06b767, 0x3e0297c0, 0x3df7fae3, 0x3dc758f0, 0x3d9a0fd4, 0x3da7a8ff, 0x3df90082,
 0x3e7421c0, 0x3e11c865, 0x3e006078, 0x3dca2d80, 0x3e09003f, 0x3def5904, 0x3e0234ec, 0x3ddfb507,
 0x3e05e397, 0x3dd933e3, 0x3dc6ab80, 0x3e189b52, 0x3e0a4a06, 0x3dd9d90a, 0x3d96606c, 0x3ded972d,
 0x3e0f0fe0, 0x3d87c973, 0x3ddb8a1a, 0x3de1b003, 0x3df9d346, 0x3e07be55, 0x3e36a0dc, 0x3e075e20,
 0x3dffa4c6, 0x3de2278d, 0x3dee49f5, 0x3dff52fc, 0x3df17721, 0x3dc785f9, 0x3dcd6239, 0x3de6655a,
 0x3dd769a9, 0x3daff89c, 0x3dcd716d, 0x3dd8d046, 0x3dee8102, 0x3dd7c1be, 0x3dffdc59, 0x3dd4c554,
 0x3d9fc0ee, 0x3df2178f, 0x3de958da, 0x3de69835, 0x3e01d2e1, 0x3e066c6e, 0x3dda493d, 0x3dbb5be9,
 0x3df5019f, 0x3e00cc36, 0x3e0702e6, 0x3df1bc56, 0x3db08d8f, 0x3e0093d9, 0x3dc8349d, 0x3e251c9f,
 0x3ded6cb5, 0x3dcb7c1a, 0x3e020536, 0x3d535bad, 0x3d52ea6e, 0x3e010ecb, 0x3dfc1ce7, 0x3d933e43,
 0x3dc7376f, 0x3dd32485, 0x3dbfffbd, 0x3dfc4977, 0x3de39ffd, 0x3dedd378, 0x3dcbfd7f, 0x3d91452a,
 0x3de58a33, 0x3d46f4e7, 0x3dc1ae13, 0x3decc78f, 0x3e20ee4a, 0x3d99f567, 0x3e061523, 0x3dbdf381,
 0x3df4a340, 0x3dda7daa, 0x3da540e7, 0x3df9053a, 0x3e082f51, 0x3e033a8a, 0x3dc24eb7, 0x3dce275b,
 0x3dc1135b, 0x3d99b92a, 0x3dfe3e29, 0x3e442e99, 0x3e102689, 0x3d65cd85, 0x3e422813, 0x3da03c9c,
 0x3d81c865, 0x3e0a7c9e, 0x3e0c64bb, 0x3e49cb68, 0x3da8ec60, 0x3d887ac3, 0x3e01537a, 0x3dec13ba,
 0x3de69d73, 0x3e0c2dae, 0x3dcb3403, 0x3e18d5c7, 0x3dd6c15d, 0x3ded727a, 0x3de00d1b, 0x3e4ddc1e,
 0x3de4e26d, 0x3dfa0169, 0x3dfd0bfa, 0x3e1feda6, 0x3debe48a, 0x3d4745da, 0x3dd00c52, 0x3e01565c,
 0x3dd92fb2, 0x3dca0b51, 0x3dc544f1, 0x3e023359, 0x3dcd538b, 0x3dfaaef3, 0x3d90407d, 0x3e1a187a,
 0x3e02c6ac, 0x3de01ffb, 0x3dbc390e, 0x3e03bb41, 0x3dd57f73, 0x3df6aa4c, 0x3dea3593, 0x3dc335c5,
 0x3e0ba7b9, 0x3e0d32c6, 0x3e134fc6, 0x3ddd1bb5, 0x3dac471b, 0x3e03ceea, 0x3df6bfcb, 0x3df2aa5c,
 0x3dfe6bc6, 0x3dd07421, 0x3d67b16f, 0x3db090ea, 0x3ddaa1d7, 0x3e0011d3, 0x3de465a5, 0x3df06898,
 0x3dc59a9c, 0x3dd6a6a0, 0x3dca8bb7, 0x3def544c, 0x3dfb1a6d, 0x3dc54ef4, 0x3db852b5, 0x3e540810,
 0x3dda7979, 0x3dfd9125, 0x3dea89fc, 0x3dd36156, 0x3d929091, 0x3e6d9b1b, 0x3db082dd, 0x3e070da6,
 0x3de9e55c, 0x3dd6ce79, 0x3ddedc7f, 0x3db4d9e2, 0x3de90603, 0x3dfe8ff3, 0x3df347a6, 0x3df2ccf7,
 0x3df6a6a0, 0x3e069caa, 0x3da9c894, 0x3e0c4eb5, 0x3e14ab60, 0x3dfcbd55, 0x3ddf1e8e, 0x3e1e2a80,
 0x3debbdbe, 0x3e04c09c, 0x3e079464, 0x3de351df, 0x3e626fa4, 0x3e285059, 0x3db4babd, 0x3e08a0d3,
 0x3db246cc, 0x3dfbb40b, 0x3d77a299, 0x3deb4aed, 0x3d22d5d3, 0x3e03c07f, 0x3dec33b5, 0x3dde8039,
 0x3dca3b30, 0x3deb25b3, 0x3dc75537, 0x3e063d3e, 0x3df28284, 0x3dc61820, 0x3df83f0c, 0x3e01565c,
 0x3df450f0, 0x3daa45e2, 0x3e03558a, 0x3e581a15, 0x3db56997, 0x3d9ef277, 0x3d961805, 0x3dec78ea,
 0x3dce2435, 0x3df831f0, 0x3dff7271, 0x3dd6cdf2, 0x3df3fd0d, 0x3e061bf3, 0x3dc6f09a, 0x3df523b3,
 0x3a2c54b2, 0x3e078466, 0x3ddb4edb, 0x3dd96af0, 0x3d1a73ea, 0x3e09b520, 0x3e04c405, 0x3dee2fbe,
 0x3d72e9e8, 0x3df53932, 0x3dfaa8a8, 0x3e0e9fae, 0x3dd735c2, 0x3da3581c, 0x3df2d98c, 0x3dad9a45,
 0x3e0f28c8, 0x3df46e4c, 0x3dd6cf85, 0x3dcbca6e, 0x3df21ee6, 0x3d942a74, 0x3dd4bcf1, 0x3e04a340,
 0x3db56572, 0x3df17e78, 0x3dfb877b, 0x3e029a1c, 0x3cd37f03, 0x3d962f16, 0x3dd72043, 0x3df058de,
 0x3e04ead1, 0x3dfeb8d8, 0x3de03d57, 0x3e3a4830, 0x3db8bf57, 0x3def5ec8, 0x3df1bb49, 0x3de895d1,
 0x3dddb8fe, 0x3e04aba4, 0x3de89870, 0x3e07b741, 0x3e199e95, 0x3d86e5a5, 0x3df51e75, 0x3dd8d9b6,
 0x3dfae03b, 0x3e11c476, 0x3da6c7c3, 0x3dd673c5, 0x3dfba238, 0x3d5643bf, 0x3ddd9770, 0x3e053975,
 0x3d987a22, 0x3e091a76, 0x3dceb3dd, 0x3e084945, 0x3dbde417, 0x3dee31d7, 0x3df497b7, 0x3de889c2,
 0x3dc76cb4, 0x3dd3daf9, 0x3dbfe4d8, 0x3df1537a, 0x3de52696, 0x3db95564, 0x3dddf19d, 0x3dda2e7f,
 0x3e076a2f, 0x3de8b114, 0x3dfc04c9, 0x3dd1f794, 0x3e074928, 0x3dee63a6, 0x3e053198, 0x3e073b43,
 0x3de2107b, 0x3de9b1fb, 0x3ded1f1d, 0x3e044fe3, 0x3e072757, 0x3dcdc769, 0x3e18012e, 0x3dd971c1,
 0x3dd8be73, 0x3e1162ae, 0x3db85d4c, 0x3de73eee, 0x3e01a8ef, 0x3dfb927d, 0x3dbce15a, 0x3e056ef0,
 0x3dfecbb8, 0x3da8cd8c, 0x3de0ae97, 0x3dddf7e8, 0x3dcbb4e2, 0x3dbf84f3, 0x3df09b74, 0x3d65ba6f,
 0x3db8d9d1, 0x3debd1ab, 0x3e0d4aa1, 0x3dc94c52, 0x3dff7be1, 0x3e00f627, 0x3de382a1, 0x3df4e7ef,
 0x3dc85498, 0x3df07a6c, 0x3dc57e3f, 0x3e69ca5c, 0x3dd3e1ca, 0x3dc60f6c, 0x3dfb4917, 0x3dc9aa2b,
 0x3df60fa1, 0x3dc3cf12, 0x3de0999e, 0x3de7c287, 0x3e22e558, 0x3ddbaa9b, 0x3ddc1f00, 0x3e5e3eaf,
 0x3e0b90a8, 0x3dbedc2e, 0x3dd3f9e8, 0x3e0b25f6, 0x3dce221d, 0x3da61cf2, 0x3df60419, 0x3dd287c2,
 0x3deaf3e4, 0x3e08f29d, 0x3d85d53a, 0x3def6838, 0x3dbdfc6a, 0x3e0f95d5, 0x3e100b46, 0x3de929aa,
 0x3e07e34c, 0x3de24a27, 0x3d9dc7fd, 0x3dcb9d8d, 0x3db20d3b, 0x3e171e2a, 0x3e1645a2, 0x3dc840ef,
 0x3defc04d, 0x3dcdcdb3, 0x3db5c480, 0x3dd101b0, 0x3da20dcf, 0x3db3260a, 0x3e0fc505, 0x3db17cbd,
 0x3da4c2eb, 0x3db1a08c, 0x3deb1d50, 0x3dc2c50c, 0x3d5f728c, 0x3e114cec, 0x3debb342, 0x3e003dde,
 0x3dc9c91a, 0x3dbc4e57, 0x3e0243dd, 0x3dc2854b, 0x3dc3da65, 0x3e0142b3, 0x3dda9c99, 0x3dd7cf5f,
 0x3dccb238, 0x3dcfb331, 0x3e03a323, 0x3e0b50f4, 0x3ddac8a3, 0x3dcaac7c, 0x3e0f7be1, 0x3e042dcf,
 0x3dde8039, 0x3d2bce50, 0x3e496851, 0x3de16c61, 0x3dcc0bea, 0x3df38477, 0x3dd87e7c, 0x3d903eea,
 0x3e019610, 0x3e07fd3f, 0x3e0a2a4e, 0x3df300de, 0x3c8161ca, 0x3dd6049f, 0x3de9f72f, 0x3e066623,
 0x3df23a2a, 0x3dea19ca, 0x3dfdcf89, 0x3dd2e2fc, 0x3dd85943, 0x3e077dd8, 0x3d888c1e, 0x3e12873c,
 0x3df0995b, 0x3df1a198, 0x3deb19a4, 0x3dee1ef7, 0x3dd1886e, 0x3df8fffc, 0x3db1d9e7, 0x3df4384c,
 0x3e1950fc, 0x3e03fbbd, 0x3de67946, 0x3de70a81, 0x3ddf15a5, 0x3e04bf90, 0x3e1233df, 0x3dfbbb62,
 0x3e050870, 0x3db45bca, 0x3e02e83a, 0x3dc6804d, 0x3e0aa088, 0x3dcdf1e1, 0x3e007d0b, 0x3e051117,
 0x3dbba3e5, 0x3dd37b8d, 0x3d565d8a, 0x3d1dd187, 0x3deb955f, 0x3cb5f8e0, 0x3d88abfe, 0x3df295ea,
 0x3de5c78a, 0x3e08a7a4, 0x3de69403, 0x3d9889f7, 0x3dfca212, 0x3dc91f05, 0x3df14b16, 0x3dd9f3c7,
 0x3dc65e6e, 0x3d8ac63a, 0x3dcf8a4c, 0x3dad0310, 0x3deb90a8, 0x3e038477, 0x3d309dc2, 0x3e094f27,
 0x3d80c4ec, 0x3de772d6, 0x3e02a3cf, 0x3d904ac3, 0x3d51ef16, 0x3dfc94f7, 0x3dcaa3e3, 0x3e09eba7,
 0x3de007dd, 0x3dd0b0f2, 0x3de83c29, 0x3e002bc7, 0x3df3fb7a, 0x3e019c9d, 0x3daa677d, 0x3dd476b0,
 0x3dcea077, 0x3da37528, 0x3dd4f616, 0x3e04cdb8, 0x3e08bfc2, 0x3e6cd078, 0x3dc49335, 0x3e0baa15,
 0x3da5742e, 0x3db92c8c, 0x3dc93236, 0x3de2ed35, 0x3dd98f1d, 0x3e16c93a, 0x3e017721, 0x3df58a76,
 0x3dcb1989, 0x3de0485a, 0x3dea0c28, 0x3dfbb8c3, 0x3dc35d3f, 0x3e126070, 0x3e0cf5f5, 0x3da9108d,
 0x3dab8de0, 0x3df40a28, 0x3ddcdcca, 0x3dff01b8, 0x3da04d12, 0x3dde41d5, 0x3d929f32, 0x3dedf698,
 0x3de249a1, 0x3de849cb, 0x3dd289db, 0x3d851fb7, 0x3ddf9df5, 0x3e0ae925, 0x3df0d845, 0x3df79421,
 0x3de80c74, 0x3e0f33ca, 0x3db54b7f, 0x3dda6efc, 0x3dbbdfaa, 0x3e4e3cda, 0x3dbea29d, 0x3de09785,
 0x3dfd7668, 0x3de96edf, 0x3d8d55a4, 0x3dfc1483, 0x3e092ccf, 0x3dfc7582, 0x3e0ff5c7, 0x3df2d341,
 0x3daf695f, 0x3e45e17e, 0x3d9b3220, 0x3ddff8a9, 0x3dfcb0c0, 0x3de5c4eb, 0x3dce59b0, 0x3dc10f52,
 0x3dd4623d, 0x3d89170d, 0x3d098097, 0x3d848429, 0x3dcd2e51, 0x3e09adc9, 0x3e07a3db, 0x3df8c9fb,
 0x3d585c83, 0x3e018bd6, 0x3d9f21b4, 0x3e02018a, 0x3e02a6b1, 0x3e0c69b6, 0x3de0578e, 0x3dedc22b,
 0x3e0b8130, 0x3dbb2ffa, 0x3dda4f01, 0x3dd177a7, 0x3db5e6e5, 0x3e0eefa2, 0x3daee830, 0x3d0ee617,
 0x3e09802c, 0x3e0a8deb, 0x3dfef0f1, 0x3debcf0b, 0x3dfc347f, 0x3dee464a, 0x3df2b0a7, 0x3e369510,
 0x3deeb24a, 0x3daeaf4d, 0x3dc4879f, 0x3e38ae75, 0x3dbd3cff, 0x3dd89acc, 0x3dc11772, 0x3dc836b6,
 0x3e164452, 0x3e2e79ab, 0x3dc10a64, 0x3e2badc1, 0x3e061805, 0x3def42ff, 0x3e054cdb, 0x3dae5182,
 0x3d528dbc, 0x3e554ac3, 0x3dc383d6, 0x3dfb6ed6, 0x3dfc47e5, 0x3dd78e5c, 0x3df6a93f, 0x3dd4b9cb,
 0x3e0232d3, 0x3de62563, 0x3e043676, 0x3df17375, 0x3e13c1ce, 0x3ddaeee9, 0x3dc32f01, 0x3de8e608,
 0x3dfcd899, 0x3db9dcd0, 0x3d93487c, 0x3df9ab6d, 0x3debbc2c, 0x3cd430da, 0x3dbf86a1, 0x3dec75c5,
 0x3e17a9e3, 0x3ded0679, 0x3df1f8a1, 0x3e50c671, 0x3d2ab6c3, 0x3dfce6c1, 0x3dff2778, 0x3db75e3b,
 0x3e01f1d0, 0x3e031bdf, 0x3d7b213e, 0x3e1c0c63, 0x3d83230d, 0x3e124463, 0x3d85bd87, 0x3e016378,
 0x3df4c76d, 0x3de5ed4a, 0x3dc9473c, 0x3df6ab58, 0x3e00318c, 0x3e0c0ebf, 0x3def965b, 0x3e150d6b,
 0x3dbe75ff, 0x3e04e22a, 0x3df77d0f, 0x3deb1a2a, 0x3e0482bf, 0x3de65cf6, 0x3ddfa11b, 0x3ddeabbd,
 0x3d512902, 0x3df3db7f, 0x3e12263e, 0x3e02af9b, 0x3dcb181f, 0x3dd1e42e, 0x3e025c81, 0x3e082f94,
 0x3dc96638, 0x3dc76b9a, 0x3dc5fd70, 0x3dfc9e67, 0x3ddd9d34, 0x3e5fe436, 0x3e6038e3, 0x3dee0d24,
 0x3e04d09a, 0x3de4a277, 0x3de071c5, 0x3deff972, 0x3dc0d1aa, 0x3de5907e, 0x3d141b25, 0x3d9d6056,
 0x3daf059a, 0x3e2db50f, 0x3de2b735, 0x3de0b913, 0x3e12a3cf, 0x3e04fa05, 0x3de40421, 0x3e3264a1,
 0x3de26245, 0x3dc52993, 0x3d9747e5, 0x3dfb17ce, 0x3e10d6f5, 0x3dedb489, 0x3df77e1c, 0x3dff5d79,
 0x3dba8a90, 0x3d85a5ef, 0x3df63e4b, 0x3df52a84, 0x3dc9ad00, 0x3e010c2c, 0x3de9973e, 0x3ddaf31b,
 0x3ddf5515, 0x3dfa4ce8, 0x3da118dc, 0x3dec6de7, 0x3dfbc340, 0x3dc662f1, 0x3d65dbe2, 0x3de1511e,
 0x3e0350d3, 0x3deed677, 0x3dfbcdbc, 0x3e264064, 0x3dcf78ff, 0x3e04c4ce, 0x3e1fd263, 0x3dacfdc5,
 0x3dc27e45, 0x3dcd7bea, 0x3e54bba1, 0x3d9c0783, 0x3e0d5736, 0x3e08904f, 0x3de7f456, 0x3dc662e3,
 0x3dde4d5e, 0x3ddf2e49, 0x3dca5ffe, 0x3d846603, 0x3e450614, 0x3d8db7ff, 0x3de7f23d, 0x3d94d537,
 0x3e053b4b, 0x3df765fe, 0x3dfe8d54, 0x3dbe2b8c, 0x3dd82a99, 0x3c809049, 0x3d9828a9, 0x3df2fec5,
 0x3e0152b1, 0x3db2e8ce, 0x3dca8c9c, 0x3de4e161, 0x3e0d7236, 0x3dd0fadf, 0x3e4e0e73, 0x3d7da05a,
 0x3dc02e16, 0x3e03f67f, 0x3e0eae9f, 0x3e0fc548, 0x3dd29025, 0x3dcee393, 0x3dc2fa51, 0x3de730c6,
 0x3e507464, 0x3df6249a, 0x3cbd90f0, 0x3dbaf4d6, 0x3dcc1b11, 0x3de48c72, 0x3dd74bc7, 0x3de6bf01,
 0x3def7b18, 0x3deb3248, 0x3dbe30cb, 0x3e0071c5, 0x3e06d267, 0x3dfb035c, 0x3dc7d468, 0x3defed63,
 0x3dd232d3, 0x3d6e6736, 0x3dc97827, 0x3df8127b, 0x3d8f855e, 0x3e0002e2, 0x3de5b531, 0x3de875d5,
 0x3ddae600, 0x3dd9c737, 0x3d90b8f8, 0x3e02018a, 0x3db32808, 0x3d789a46, 0x3d8edb58, 0x3d7f0183,
 0x3df29995, 0x3de56bca, 0x3d877476, 0x3dbf6599, 0x3df0e6f3, 0x3d41e852, 0x3db452b8, 0x3dbb1dd6,
 0x3dd8a32f, 0x3de0f990, 0x3dda3f46, 0x3db81c2e, 0x3e0f39d2, 0x3db8e2a0, 0x3e101cd6, 0x3e0171e3,
 0x3dcd9408, 0x3dbf5530, 0x3dda1987, 0x3dd46dc6, 0x3d9d9dfe, 0x3d9c481a, 0x3dcd2c38, 0x3dcac520,
 0x3de2845a, 0x3c598039, 0x3e0167ec, 0x3e0b4fe8, 0x3dd592da, 0x3e0379b7, 0x3dbe6879, 0x3dcdcec0,
 0x3da378b8, 0x3de367e4, 0x3dd02c4d, 0x3e0340d5, 0x3dfd0c80, 0x3dfbbe01, 0x3d98765c, 0x3de93d10,
 0x3d9931a2, 0x3df526d9, 0x3e0f811f, 0x3dbad90c, 0x3dd9ac79, 0x3dd23ee2, 0x3df88228, 0x3e1aa7df,
 0x3d92cc99, 0x3dd1e29b, 0x3e032336, 0x3dc90280, 0x3dceb3dd, 0x3dd42bb6, 0x3d1adae2, 0x3de6db51,
 0x3dfe9af6, 0x3e062175, 0x3da645a2, 0x3dba1067, 0x3e10a783, 0x3df21dda, 0x3de02b84, 0x3da7bbde,
 0x3dd276fb, 0x3e3270b0, 0x3d9b5ac2, 0x3dd2b5e5, 0x3ddf6988, 0x3de9a997, 0x3de8d003, 0x3e0e8c05,
 0x3de8e582, 0x3da22658, 0x3ddb0143, 0x3d870cb4, 0x3dda5a04, 0x3ded4aa1, 0x3de7e176, 0x3e10ef13,
 0x3dcc2459, 0x3dcbfa32, 0x3de433d7, 0x3e045fe1, 0x3deabc51, 0x3decbd98, 0x3ddd8948, 0x3e07646b,
 0x3d879c84, 0x3d82e6dd, 0x3deb74de, 0x3de4db9c, 0x3df92c06, 0x3de726d0, 0x3df0e1b5, 0x3e023960,
 0x3ddbce42, 0x3dd92104, 0x3de2a887, 0x3e537e6f, 0x3e06d3fa, 0x3db17f69, 0x3e0eabbd, 0x3df721d5,
 0x3dcfdc16, 0x3e1987c6, 0x3e031056, 0x3e036934, 0x3dc09594, 0x3dc935fc, 0x3d168f23, 0x3db55872,
 0x3ce0b79b, 0x3df46aa1, 0x3e4f50a0, 0x3df0d1fa, 0x3ddabda1, 0x3e54fe37, 0x3dfc792d, 0x3ce9f780,
 0x3dbc2dbb, 0x3df13598, 0x3dea71de, 0x3dfd5d3e, 0x3e0df330, 0x3dd6edee, 0x3dfc44bf, 0x3dc2075c,
 0x3deaf816, 0x3dd2c7b9, 0x3ddb2464, 0x3d6708d3, 0x3dea8651, 0x3e087011, 0x3dcc9b41, 0x3e163172,
 0x3dc0a790, 0x3d8f23b2, 0x3dc7b31d, 0x3d803f7e, 0x3e06da01, 0x3e0c56d6, 0x3df9d990, 0x3ddaa9b5,
 0x3df08e58, 0x3dc01747, 0x3de7c17b, 0x3e0c3b0c, 0x3d96fecf, 0x3dd579af, 0x3df04ab6, 0x3dec3fc4,
 0x3de22681, 0x3e00f5e4, 0x3dcc2ed5, 0x3dac710d, 0x3decdf26, 0x3e09b307, 0x3e66f827, 0x3dd4866a,
 0x3de299d9, 0x3df84f4c, 0x3def4d7b, 0x3ddc47e5, 0x3dd6249a, 0x3db1391b, 0x3df3c57a, 0x3db9e8c4,
 0x3de1016d, 0x3e0648c7, 0x3d8553ac, 0x3e0b152f, 0x3df357e6, 0x3df465e9, 0x3da138bd, 0x3dde8251,
 0x3df66b61, 0x3dd8194c, 0x3dea41a2, 0x3e07607c, 0x3e05753a, 0x3e02c94b, 0x3dfd04a3, 0x3ddd8490,
 0x3d820975, 0x3e592924, 0x3ddb8dc5, 0x3e386d72, 0x3dd37e2c, 0x3d59459c, 0x3e02367e, 0x3de49b20,
 0x3df22fad, 0x3dccbb3d, 0x3debc276, 0x3db2b0b4, 0x3e0cdab2, 0x3d94ee6f, 0x3e104d98, 0x3dc4e908,
 0x3dbd9557, 0x3dc0d0ee, 0x3ddefa61, 0x3dfe35c6, 0x3dd55bcd, 0x3dbfdda9, 0x3d6fbd78, 0x3dcf8162,
 0x3e0d4888, 0x3df4245f, 0x3e01e710, 0x3dcc4b90, 0x3e1443d4, 0x3dc83f84, 0x3e064e05, 0x3dd1cf35,
 0x3de3c42a, 0x3daa71a9, 0x3da49782, 0x3dff8e3b, 0x3df29d41, 0x3d4c6102, 0x3de1b64e, 0x3e08676a,
 0x3da60906, 0x3dd4afd5, 0x3ddc50ce, 0x3dfa8ac6, 0x3d1fd60f, 0x3dd3e5fb, 0x3e0924af, 0x3da4d4d9,
 0x3d96d620, 0x3dfd1f60, 0x3dc63d67, 0x3dffe64f, 0x3dfceaf2, 0x3de8dc98, 0x3dcddce8, 0x3dd09e13,
 0x3e091b82, 0x3dbf890a, 0x3df29670, 0x3dec60cc, 0x3e0e529c, 0x3de3b689, 0x3e025fe9, 0x3dc2fd9e,
 0x3dacb0a5, 0x3dd45bf2, 0x3dbf8dc2, 0x3dedd378, 0x3e0ebfa9, 0x3d8d1a07, 0x3e07c99b, 0x3def4cf5,
 0x3dda11a9, 0x3dd5e6bc, 0x3e0f876a, 0x3dc7e514, 0x3dc44f78, 0x3d98ce70, 0x3dd21c47, 0x3db93c7d,
 0x3e4d466f, 0x3e004f6e, 0x3d9b2bbb, 0x3dc945b7, 0x3daf3fd9, 0x3d990456, 0x3de67f0b, 0x3df23897,
 0x3dea8d22, 0x3dd2923e, 0x3d973fd2, 0x3dab39d5, 0x3dcb10e3, 0x3e0290ef, 0x3ded05f3, 0x3dcc0676,
 0x3db78e19, 0x3dee3e6c, 0x3e09ba5e, 0x3dccfd8f, 0x3dc04cb4, 0x3df4317b, 0x3dd5653d, 0x3e07bd49,
 0x3e129889, 0x3db7a596, 0x3dd906cd, 0x3e0af7d3, 0x3ddbc558, 0x3d9c694a, 0x3dca7180, 0x3ddffc54,
 0x3e06fac6, 0x3dc1c949, 0x3de3f24e, 0x3d9f8833, 0x3df49386, 0x3dfd9125, 0x3da536a1, 0x3e0caafc,
 0x3dd68a50, 0x3dddf4c3, 0x3c81417e, 0x3df59818, 0x3dd99781, 0x3dbcb832, 0x3dd78141, 0x3deef4e0,
 0x3dddf6dc, 0x3ddabfba, 0x3de58ff7, 0x3dc3afd3, 0x3dfb6aa5, 0x3e0fd1dd, 0x3dbcfcfb, 0x3e0539fc,
 0x3e0147ae, 0x3da1ff06, 0x3df4ea8e, 0x3df50d28, 0x3dd150db, 0x3e019503, 0x3db6bd1e, 0x3dfef9db,
 0x3dde5c92, 0x3da1e995, 0x3dcf66a5, 0x3e0b4635, 0x3df02795, 0x3df5e7c9, 0x3dffc32f, 0x3def1349,
 0x3de8a9bd, 0x3dd68dfc, 0x3dc69007, 0x3e0b2f23, 0x3dddc400, 0x3dfa0f91, 0x3e0999dd, 0x3e06ba06,
 0x3e04f6e0, 0x3de37154, 0x3e0894c4, 0x3dccb0db, 0x3e1a9585, 0x3de6ddf0, 0x3d829259, 0x3e065493,
 0x3df66623, 0x3dd6dc1a, 0x3df7995f, 0x3ddf8c22, 0x3e09a240, 0x3d928904, 0x3de9aed5, 0x3e0538ac,
 0x3dd2b23a, 0x3dfee3d6, 0x3db27350, 0x3dcc9024, 0x3dd6bb13, 0x3d8fbc6b, 0x3dcf6f09, 0x3e015a4b,
 0x3df224ab, 0x3d863b40, 0x3cbc957d, 0x3dce5c4f, 0x3dad968c, 0x3e00285f, 0x3dccafe9, 0x3e0260f6,
 0x3dd8255b, 0x3dc72911, 0x3db4b945, 0x3dca19d7, 0x3df73c92, 0x3e07de94, 0x3d9ff7e0, 0x3ddedf1e,
 0x3e3afab5, 0x3dd9841b, 0x3db31d7f, 0x3de308ff, 0x3e080e49, 0x3dcb2067, 0x3de7429a, 0x3dcc2027,
 0x3e2799e5, 0x3e02c626, 0x3ddb3b75, 0x3d95bf92, 0x3e02d98c, 0x3dde07a3, 0x3df9bed3, 0x3dcce704,
 0x3df070fc, 0x3da4658b, 0x3df2f1aa, 0x3d9e0752, 0x3e11ce29, 0x3e00485a, 0x3e15dd90, 0x3d8259ba,
 0x3dea4c1f, 0x3df88228, 0x3db5ef8b, 0x3dfbc127, 0x3decdea0, 0x3da7085a, 0x3dca7dfb, 0x3dcbc713,
 0x3ddbe4cd, 0x3de97a68, 0x3dd2f2b6, 0x3dfdc161, 0x3dcff759, 0x3ded5843, 0x3e00a0b2, 0x3de36fc1,
 0x3e2370ce, 0x3e00a287, 0x3d732cb3, 0x3debfb9c, 0x3ded82ba, 0x3e078812, 0x3e02c1b1, 0x3e031ceb,
 0x3deda76e, 0x3d601af3, 0x3d9f56f8, 0x3ddbedb7, 0x3dc7b2f5, 0x3e06c3fc, 0x3de7496b, 0x3d8c2c6c,
 0x3d80ecaa, 0x3deda3c2, 0x3deee9dd, 0x3ded8772, 0x3dce6fb5, 0x3dfa4a49, 0x3e0d1783, 0x3e001a7a,
 0x3dbefffd, 0x3dee3715, 0x3dfd21ff, 0x3e0a2ced, 0x3dda0a52, 0x3deacd9f, 0x3d8a078b, 0x3df28f19,
 0x3df0c34c, 0x3e11e6cd, 0x3dc46e3f, 0x3da777c4, 0x3e04bf4d, 0x3d336502, 0x3e01a438, 0x3e08e582,
 0x3de4dd2f, 0x3dd543af, 0x3de875d5, 0x3d696894, 0x3e09a459, 0x3e162307, 0x3deb85a5, 0x3e0eb4a6,
 0x3df8f9b1, 0x3db6d3a9, 0x3d90ff2c, 0x3e0d9019, 0x3defa1e4, 0x3dc4f194, 0x3d6d6020, 0x3e0121ab,
 0x3e00281c, 0x3db8013b, 0x3df248d8, 0x3dd7d6b6, 0x3da24710, 0x3d48c826, 0x3db4d262, 0x3dc0d9e5,
 0x3de0a0f5, 0x3cbeb86d, 0x3dbdb736, 0x3e062a5e, 0x3dd4eb9a, 0x3e0e9ff1, 0x3e0300de, 0x3dc43a7f,
 0x3e0f3cb4, 0x3df9e27a, 0x3dd7d0f2, 0x3e066d37, 0x3dad9480, 0x3da29abd, 0x3dcd288d, 0x3defb763,
 0x3e001605, 0x3da29937, 0x3deb86b1, 0x3dc167a9, 0x3d9fd27e, 0x3e0bd7f5, 0x3d88cbb6, 0x3e4c78ea,
 0x3e082880, 0x3e4dde37, 0x3e0f3908, 0x3de690de, 0x3dae45de, 0x3dcf605b, 0x3dee6fb5, 0x3e033eff,
 0x3dbd1493, 0x3e0c6c98, 0x3df69a0b, 0x3dce23af, 0x3df6bb13, 0x3de5100a, 0x3dfe2bd0, 0x3da37e2c,
 0x3e0b19a4, 0x3e1a2a0b, 0x3dd4a7f8, 0x3dddc726, 0x3e0ce67d, 0x3db7fa78, 0x3e00d068, 0x3e051b93,
 0x3dbc9214, 0x3dda059a, 0x3dfbedb7, 0x3e0093d9, 0x3dc08a9f, 0x3dd1e109, 0x3dc6f1b4, 0x3dff7165,
 0x3dbf6af2, 0x3df20c8d, 0x3df4e65c, 0x3dc65a4a, 0x3dba9e8a, 0x3d9770bd, 0x3dd1eeaa, 0x3def0069,
 0x3e23e2d6, 0x3e083dff, 0x3df801b4, 0x3de1da7b, 0x3dc6484e, 0x39ce9dbb, 0x3e048dc1, 0x3df05ea2,
 0x3dcfb118, 0x3e0abf77, 0x3e182060, 0x3dcf5f4e, 0x3defd114, 0x3de7f0aa, 0x3dba90b2, 0x3dcba012,
 0x3dcf7660, 0x3df2f1aa, 0x3e0cdff0, 0x3dd9ba1b, 0x3db2f5ce, 0x3dcec7c9, 0x3dfe731d, 0x3dd857b0,
 0x3ddaabcd, 0x3e06e7e6, 0x3ddf40a3, 0x3db244c1, 0x3ded362f, 0x3df95c42, 0x3dd62d84, 0x3df44a1f,
 0x3df019f4, 0x3dd9cbef, 0x3df54196, 0x3e3cefed, 0x3ddaf102, 0x3d39b888, 0x3e23ea2d, 0x3df1d4fa,
 0x3de0913a, 0x3e021c04, 0x3dfb0575, 0x3c2c6e1d, 0x3db850ed, 0x3da0715a, 0x3de23fab, 0x3e048cf8,
 0x3dcbb088, 0x3db02bd5, 0x3dfdd97f, 0x3df3892f, 0x3dd23811, 0x3da64049, 0x3de4ddb5, 0x3d22e62f,
 0x3e01e57e, 0x3ddecc3e, 0x3b8c558e, 0x3de1a3f4, 0x3db02539, 0x3df0fb65, 0x3dc003b9, 0x3dfc254a,
 0x3e39e343, 0x3df9f2bb, 0x3ddcbc49, 0x3db2cf96, 0x3d9ebb0c, 0x3df5d9a1, 0x3e45e3da, 0x3e00dc77,
 0x3e05eb31, 0x3dfef523, 0x3dd439de, 0x3dd5ed8d, 0x3e0b588e, 0x3e11dead, 0x3dcc45b1, 0x3dcebf66,
 0x3e010300, 0x3e0553ac, 0x3ddd1eda, 0x3e1d0a24, 0x3dedd3fe, 0x3e0a63b7, 0x3e03a8a4, 0x3e043bf7,
 0x3de982cb, 0x3dcae8ef, 0x3e0ac797, 0x3de87a07, 0x3de90eed, 0x3dc9c0c4, 0x3dd60f1b, 0x3dfb4c3c,
 0x3d2416e6, 0x3e0180d4, 0x3e3cc426, 0x3e03c31e, 0x3dc812e7, 0x3e07620f, 0x3e0101f3, 0x3deb0ea2,
 0x3de9ff93, 0x3e123e9f, 0x3cf6c051, 0x3de5a35d, 0x3e13508f, 0x3da91b17, 0x3da3789e, 0x3e0a4fca,
 0x3e0523f6, 0x3ded6cb5, 0x3e09628d, 0x3dce4d1a, 0x3ddcc294, 0x3e0aa1d7, 0x3de5af6c, 0x3e134f40,
 0x3dc52f21, 0x3dffaf42, 0x3e03e682, 0x3e0a7829, 0x3e019610, 0x3e0c3f81, 0x3e050050, 0x3db24d17,
 0x3e05bb7b, 0x3ddfb481, 0x3d4cffde, 0x3dcebd4d, 0x3e00928a, 0x3cdfb720, 0x3d952eb6, 0x3e2c5048,
 0x3de42071, 0x3de0999e, 0x3e0ae792, 0x3d9e8fa3, 0x3de7bcc3, 0x3dfbfceb, 0x3e06d8b2, 0x3dcf8833,
 0x3de83b1d, 0x3dd2d667, 0x3e18d003, 0x3e05b142, 0x3dc923b0, 0x3dffe868, 0x3dfa4591, 0x3d6a2d9b,
 0x3deb2fa9, 0x3df63023, 0x3da8aee0, 0x3e00c5a8, 0x3dc333c7, 0x3dfb6912, 0x3d4bfb81, 0x3d961a53,
 0x3e100219, 0x3e040dd4, 0x3e0a582e, 0x3defba88, 0x3dc9201f, 0x3e05a07b, 0x3de352eb, 0x3e05d181,
 0x3dd43e96, 0x3dd8a3b5, 0x3ddc19c1, 0x3e02a023, 0x3e131a09, 0x3e06e3f8, 0x3dd26395, 0x3dcf5b1d,
 0x3dcdb728, 0x3dd3ce64, 0x3dce3c53, 0x3e0c94f7, 0x3dc826fb, 0x3e2e98dd, 0x3df1a006, 0x3e101c0d,
 0x3e141e15, 0x3de16d6e, 0x3dc7575d, 0x3df6c82e, 0x3dc143e8, 0x3de9dbec, 0x3dc5dd67, 0x3da87379,
 0x3e00a809, 0x3dc189f3, 0x3d910419, 0x3d9140eb, 0x3dd62bf1, 0x3e0988d3, 0x3dcd9168, 0x3dd774ac,
 0x3db09056, 0x3e15c24c, 0x3de026cc, 0x3e224463, 0x3da95358, 0x3dbe028d, 0x3de149c7, 0x3da55532,
 0x3dc6aa23, 0x3dda3876, 0x3e18b60f, 0x3da2cbcf, 0x3e10d9d7, 0x3ddd86a9, 0x3d87b429, 0x39860991,
 0x3dc571aa, 0x3dc87740, 0x3db0d48c, 0x3e0f41af, 0x3dfcfcc6, 0x3dd5e41d, 0x3dab7316, 0x3dd84dba,
 0x3e3bc276, 0x3d78da8d, 0x3db9f242, 0x3ddc27e9, 0x3d1acfc5, 0x3de54d62, 0x3d99d261, 0x3de81883,
 0x3e05e13b, 0x3de9d0ea, 0x3e0fd3b3, 0x3dd4d728, 0x3d5e9384, 0x3d68d9de, 0x3dee496f, 0x3e06c3fc,
 0x3e06fd65, 0x3def0c78, 0x3ddf6124, 0x3dc4ddde, 0x3e1985f0, 0x3e0aa045, 0x3dabbd2b, 0x3dd6188b,
 0x3dafd37d, 0x3e125b31, 0x3df3ade2, 0x3e0383ae, 0x3df2df50, 0x3df9799e, 0x3da30030, 0x3de88df3,
 0x3dc493bb, 0x3dea64c3, 0x3e03ff26, 0x3e042c3d, 0x3df524c0, 0x3dfaf4ae, 0x3dfcce1c, 0x3dfd1245
};

// orientation array (128 x 32)
static const int orient[] =
{
 0x02, 0x02, 0x00, 0x04, 0x03, 0x01, 0x00, 0x07,
 0x02, 0x05, 0x03, 0x05, 0x03, 0x00, 0x03, 0x02,
 0x07, 0x04, 0x05, 0x07, 0x07, 0x03, 0x01, 0x07,
 0x00, 0x06, 0x02, 0x04, 0x01, 0x00, 0x00, 0x02,
 0x00, 0x03, 0x00, 0x03, 0x00, 0x04, 0x04, 0x01,
 0x04, 0x05, 0x07, 0x02, 0x07, 0x07, 0x03, 0x07,
 0x07, 0x01, 0x03, 0x01, 0x02, 0x02, 0x04, 0x01,
 0x02, 0x06, 0x01, 0x05, 0x00, 0x04, 0x02, 0x07,
 0x05, 0x00, 0x04, 0x00, 0x05, 0x03, 0x01, 0x05,
 0x01, 0x03, 0x03, 0x07, 0x03, 0x02, 0x02, 0x00,
 0x00, 0x05, 0x02, 0x06, 0x06, 0x03, 0x00, 0x05,
 0x03, 0x04, 0x04, 0x07, 0x00, 0x03, 0x06, 0x04,
 0x04, 0x04, 0x03, 0x02, 0x01, 0x00, 0x05, 0x07,
 0x04, 0x00, 0x02, 0x06, 0x03, 0x05, 0x05, 0x03,
 0x01, 0x06, 0x00, 0x04, 0x00, 0x07, 0x04, 0x05,
 0x07, 0x02, 0x05, 0x04, 0x04, 0x02, 0x02, 0x01,
 0x03, 0x04, 0x00, 0x00, 0x04, 0x03, 0x07, 0x03,
 0x02, 0x05, 0x03, 0x06, 0x05, 0x04, 0x02, 0x00,
 0x01, 0x02, 0x07, 0x01, 0x00, 0x03, 0x06, 0x02,
 0x03, 0x05, 0x05, 0x01, 0x00, 0x03, 0x06, 0x05,
 0x01, 0x07, 0x03, 0x02, 0x03, 0x00, 0x05, 0x00,
 0x06, 0x05, 0x06, 0x00, 0x04, 0x04, 0x07, 0x07,
 0x00, 0x01, 0x01, 0x01, 0x02, 0x04, 0x05, 0x02,
 0x06, 0x03, 0x01, 0x05, 0x03, 0x05, 0x02, 0x05,
 0x03, 0x07, 0x03, 0x05, 0x04, 0x02, 0x07, 0x00,
 0x00, 0x03, 0x04, 0x07, 0x00, 0x00, 0x05, 0x06,
 0x04, 0x01, 0x02, 0x03, 0x01, 0x03, 0x03, 0x02,
 0x05, 0x02, 0x02, 0x01, 0x05, 0x06, 0x07, 0x05,
 0x07, 0x03, 0x05, 0x00, 0x00, 0x05, 0x03, 0x05,
 0x07, 0x00, 0x02, 0x04, 0x04, 0x04, 0x03, 0x05,
 0x06, 0x04, 0x02, 0x04, 0x01, 0x00, 0x04, 0x01,
 0x06, 0x07, 0x04, 0x04, 0x06, 0x06, 0x02, 0x05,
 0x01, 0x00, 0x01, 0x00, 0x05, 0x03, 0x03, 0x02,
 0x04, 0x07, 0x05, 0x04, 0x07, 0x07, 0x03, 0x03,
 0x04, 0x01, 0x06, 0x00, 0x02, 0x04, 0x00, 0x07,
 0x01, 0x04, 0x07, 0x00, 0x06, 0x00, 0x06, 0x05,
 0x07, 0x03, 0x05, 0x03, 0x00, 0x06, 0x04, 0x04,
 0x02, 0x07, 0x05, 0x03, 0x03, 0x06, 0x05, 0x00,
 0x07, 0x04, 0x01, 0x05, 0x06, 0x01, 0x04, 0x02,
 0x05, 0x03, 0x04, 0x01, 0x02, 0x06, 0x07, 0x01,
 0x05, 0x00, 0x05, 0x07, 0x03, 0x04, 0x07, 0x04,
 0x03, 0x03, 0x04, 0x01, 0x04, 0x06, 0x03, 0x07,
 0x00, 0x05, 0x01, 0x02, 0x01, 0x06, 0x06, 0x06,
 0x05, 0x05, 0x06, 0x02, 0x00, 0x06, 0x03, 0x05,
 0x07, 0x07, 0x00, 0x04, 0x07, 0x00, 0x01, 0x05,
 0x04, 0x00, 0x02, 0x03, 0x04, 0x06, 0x03, 0x00,
 0x04, 0x03, 0x05, 0x00, 0x00, 0x03, 0x06, 0x05,
 0x01, 0x06, 0x01, 0x02, 0x02, 0x01, 0x01, 0x00,
 0x04, 0x04, 0x03, 0x00, 0x07, 0x02, 0x03, 0x07,
 0x06, 0x04, 0x00, 0x07, 0x07, 0x02, 0x05, 0x06,
 0x05, 0x05, 0x01, 0x06, 0x01, 0x02, 0x06, 0x04,
 0x06, 0x05, 0x01, 0x02, 0x04, 0x05, 0x07, 0x05,
 0x00, 0x00, 0x04, 0x02, 0x04, 0x00, 0x05, 0x07,
 0x05, 0x06, 0x06, 0x03, 0x04, 0x00, 0x06, 0x07,
 0x07, 0x02, 0x03, 0x05, 0x06, 0x07, 0x02, 0x00,
 0x07, 0x00, 0x05, 0x04, 0x03, 0x01, 0x07, 0x00,
 0x00, 0x01, 0x03, 0x05, 0x05, 0x00, 0x06, 0x04,
 0x00, 0x06, 0x05, 0x05, 0x01, 0x02, 0x04, 0x02,
 0x01, 0x07, 0x02, 0x04, 0x00, 0x00, 0x00, 0x01,
 0x03, 0x05, 0x02, 0x06, 0x03, 0x00, 0x06, 0x01,
 0x02, 0x02, 0x03, 0x05, 0x00, 0x04, 0x03, 0x02,
 0x06, 0x07, 0x06, 0x00, 0x05, 0x03, 0x05, 0x06,
 0x00, 0x02, 0x05, 0x05, 0x04, 0x02, 0x03, 0x07,
 0x04, 0x06, 0x01, 0x05, 0x07, 0x00, 0x01, 0x05,
 0x03, 0x04, 0x07, 0x02, 0x05, 0x07, 0x00, 0x00,
 0x04, 0x05, 0x02, 0x02, 0x02, 0x02, 0x06, 0x05,
 0x04, 0x07, 0x07, 0x01, 0x06, 0x01, 0x00, 0x06,
 0x05, 0x00, 0x07, 0x04, 0x04, 0x06, 0x06, 0x01,
 0x04, 0x03, 0x05, 0x04, 0x00, 0x07, 0x04, 0x07,
 0x06, 0x02, 0x01, 0x06, 0x04, 0x02, 0x00, 0x07,
 0x06, 0x06, 0x04, 0x03, 0x01, 0x02, 0x06, 0x01,
 0x05, 0x00, 0x01, 0x05, 0x07, 0x00, 0x05, 0x05,
 0x06, 0x03, 0x04, 0x01, 0x02, 0x02, 0x00, 0x04,
 0x03, 0x07, 0x06, 0x03, 0x05, 0x04, 0x02, 0x05,
 0x04, 0x00, 0x05, 0x02, 0x00, 0x01, 0x01, 0x04,
 0x01, 0x06, 0x06, 0x05, 0x07, 0x06, 0x05, 0x01,
 0x06, 0x01, 0x03, 0x02, 0x06, 0x04, 0x06, 0x07,
 0x02, 0x01, 0x05, 0x05, 0x07, 0x02, 0x00, 0x01,
 0x04, 0x03, 0x06, 0x07, 0x06, 0x00, 0x00, 0x06,
 0x07, 0x04, 0x00, 0x03, 0x04, 0x01, 0x02, 0x02,
 0x07, 0x05, 0x00, 0x01, 0x02, 0x05, 0x03, 0x00,
 0x03, 0x02, 0x02, 0x01, 0x07, 0x06, 0x04, 0x06,
 0x05, 0x07, 0x01, 0x05, 0x05, 0x04, 0x02, 0x00,
 0x03, 0x05, 0x01, 0x03, 0x01, 0x01, 0x00, 0x01,
 0x03, 0x05, 0x07, 0x07, 0x05, 0x04, 0x03, 0x05,
 0x02, 0x00, 0x04, 0x04, 0x02, 0x05, 0x06, 0x06,
 0x02, 0x01, 0x04, 0x00, 0x00, 0x01, 0x07, 0x06,
 0x04, 0x01, 0x00, 0x07, 0x03, 0x06, 0x02, 0x06,
 0x06, 0x00, 0x05, 0x03, 0x06, 0x07, 0x00, 0x04,
 0x07, 0x05, 0x05, 0x04, 0x06, 0x02, 0x03, 0x03,
 0x04, 0x05, 0x04, 0x02, 0x00, 0x01, 0x06, 0x01,
 0x06, 0x06, 0x01, 0x04, 0x07, 0x07, 0x00, 0x02,
 0x07, 0x07, 0x06, 0x07, 0x03, 0x00, 0x05, 0x05,
 0x00, 0x02, 0x00, 0x04, 0x01, 0x06, 0x01, 0x00,
 0x00, 0x00, 0x03, 0x01, 0x04, 0x06, 0x02, 0x01,
 0x05, 0x05, 0x00, 0x06, 0x01, 0x05, 0x00, 0x03,
 0x05, 0x03, 0x05, 0x00, 0x03, 0x04, 0x07, 0x07,
 0x06, 0x06, 0x07, 0x01, 0x00, 0x06, 0x07, 0x02,
 0x04, 0x00, 0x05, 0x01, 0x06, 0x01, 0x06, 0x06,
 0x02, 0x06, 0x01, 0x01, 0x07, 0x05, 0x01, 0x05,
 0x04, 0x00, 0x00, 0x04, 0x06, 0x07, 0x06, 0x00,
 0x03, 0x05, 0x03, 0x02, 0x04, 0x07, 0x01, 0x06,
 0x04, 0x02, 0x07, 0x00, 0x00, 0x01, 0x05, 0x01,
 0x06, 0x02, 0x03, 0x06, 0x06, 0x00, 0x06, 0x00,
 0x00, 0x04, 0x03, 0x04, 0x02, 0x06, 0x00, 0x07,
 0x06, 0x04, 0x06, 0x07, 0x06, 0x04, 0x01, 0x03,
 0x07, 0x05, 0x07, 0x05, 0x02, 0x00, 0x00, 0x05,
 0x01, 0x02, 0x04, 0x07, 0x04, 0x02, 0x02, 0x05,
 0x02, 0x07, 0x06, 0x04, 0x05, 0x04, 0x06, 0x03,
 0x06, 0x07, 0x06, 0x05, 0x03, 0x04, 0x00, 0x05,
 0x00, 0x03, 0x04, 0x02, 0x05, 0x01, 0x06, 0x04,
 0x01, 0x00, 0x01, 0x06, 0x04, 0x02, 0x00, 0x06,
 0x02, 0x04, 0x02, 0x01, 0x03, 0x05, 0x00, 0x02,
 0x07, 0x05, 0x06, 0x04, 0x01, 0x05, 0x00, 0x07,
 0x04, 0x04, 0x03, 0x05, 0x00, 0x05, 0x06, 0x01,
 0x01, 0x06, 0x06, 0x01, 0x01, 0x02, 0x00, 0x06,
 0x07, 0x04, 0x07, 0x05, 0x00, 0x01, 0x04, 0x04,
 0x02, 0x01, 0x04, 0x00, 0x01, 0x07, 0x02, 0x03,
 0x05, 0x04, 0x01, 0x00, 0x02, 0x04, 0x02, 0x07,
 0x06, 0x01, 0x05, 0x06, 0x06, 0x03, 0x04, 0x02,
 0x00, 0x04, 0x04, 0x01, 0x02, 0x04, 0x06, 0x05,
 0x00, 0x02, 0x05, 0x04, 0x06, 0x00, 0x00, 0x05,
 0x06, 0x01, 0x06, 0x05, 0x04, 0x02, 0x01, 0x03,
 0x00, 0x01, 0x06, 0x06, 0x00, 0x03, 0x01, 0x02,
 0x06, 0x04, 0x03, 0x01, 0x06, 0x06, 0x01, 0x05,
 0x05, 0x07, 0x02, 0x01, 0x07, 0x05, 0x07, 0x04,
 0x01, 0x00, 0x06, 0x04, 0x03, 0x02, 0x02, 0x07,
 0x05, 0x00, 0x01, 0x06, 0x03, 0x06, 0x00, 0x00,
 0x07, 0x03, 0x04, 0x02, 0x01, 0x03, 0x07, 0x07,
 0x01, 0x05, 0x05, 0x03, 0x07, 0x06, 0x02, 0x07,
 0x02, 0x07, 0x03, 0x00, 0x07, 0x01, 0x02, 0x06,
 0x00, 0x05, 0x05, 0x07, 0x06, 0x01, 0x07, 0x05,
 0x01, 0x04, 0x06, 0x04, 0x05, 0x04, 0x01, 0x00,
 0x07, 0x05, 0x05, 0x02, 0x04, 0x04, 0x02, 0x06,
 0x01, 0x07, 0x07, 0x07, 0x01, 0x01, 0x07, 0x00,
 0x07, 0x05, 0x06, 0x01, 0x06, 0x02, 0x05, 0x06,
 0x03, 0x06, 0x04, 0x00, 0x03, 0x07, 0x05, 0x04,
 0x05, 0x02, 0x01, 0x01, 0x01, 0x04, 0x07, 0x06,
 0x03, 0x07, 0x02, 0x06, 0x07, 0x03, 0x01, 0x00,
 0x05, 0x01, 0x01, 0x02, 0x06, 0x05, 0x01, 0x06,
 0x04, 0x04, 0x05, 0x03, 0x06, 0x07, 0x04, 0x02,
 0x01, 0x00, 0x04, 0x00, 0x03, 0x06, 0x01, 0x01,
 0x07, 0x01, 0x04, 0x00, 0x00, 0x02, 0x06, 0x06,
 0x01, 0x07, 0x04, 0x00, 0x06, 0x07, 0x05, 0x03,
 0x01, 0x07, 0x01, 0x05, 0x04, 0x04, 0x04, 0x06,
 0x07, 0x01, 0x04, 0x07, 0x05, 0x05, 0x01, 0x05,
 0x02, 0x04, 0x07, 0x06, 0x01, 0x04, 0x02, 0x03,
 0x00, 0x00, 0x01, 0x05, 0x06, 0x04, 0x02, 0x06,
 0x02, 0x04, 0x02, 0x06, 0x04, 0x06, 0x05, 0x07,
 0x06, 0x02, 0x02, 0x05, 0x01, 0x00, 0x05, 0x03,
 0x00, 0x04, 0x01, 0x01, 0x01, 0x03, 0x06, 0x07,
 0x06, 0x05, 0x07, 0x07, 0x04, 0x01, 0x03, 0x05,
 0x05, 0x01, 0x00, 0x02, 0x03, 0x06, 0x02, 0x07,
 0x02, 0x01, 0x02, 0x05, 0x01, 0x01, 0x04, 0x01,
 0x01, 0x07, 0x04, 0x06, 0x00, 0x02, 0x01, 0x07,
 0x07, 0x05, 0x00, 0x06, 0x07, 0x05, 0x02, 0x03,
 0x03, 0x00, 0x03, 0x04, 0x02, 0x03, 0x05, 0x04,
 0x03, 0x06, 0x05, 0x05, 0x07, 0x01, 0x07, 0x04,
 0x04, 0x04, 0x01, 0x01, 0x06, 0x07, 0x02, 0x02,
 0x06, 0x06, 0x06, 0x05, 0x01, 0x02, 0x07, 0x03,
 0x06, 0x05, 0x04, 0x07, 0x02, 0x07, 0x02, 0x00,
 0x01, 0x05, 0x03, 0x01, 0x01, 0x00, 0x06, 0x02,
 0x06, 0x06, 0x04, 0x02, 0x04, 0x02, 0x07, 0x07,
 0x07, 0x00, 0x03, 0x04, 0x01, 0x00, 0x01, 0x05,
 0x00, 0x05, 0x04, 0x06, 0x01, 0x01, 0x05, 0x06,
 0x04, 0x06, 0x01, 0x01, 0x06, 0x05, 0x00, 0x01,
 0x05, 0x04, 0x06, 0x03, 0x07, 0x01, 0x04, 0x06,
 0x05, 0x01, 0x01, 0x04, 0x03, 0x00, 0x06, 0x02,
 0x01, 0x04, 0x06, 0x04, 0x02, 0x04, 0x06, 0x01,
 0x04, 0x04, 0x01, 0x01, 0x03, 0x04, 0x01, 0x01,
 0x02, 0x07, 0x06, 0x03, 0x05, 0x01, 0x05, 0x01,
 0x03, 0x02, 0x00, 0x06, 0x04, 0x06, 0x01, 0x03,
 0x06, 0x06, 0x06, 0x03, 0x01, 0x06, 0x06, 0x06,
 0x01, 0x03, 0x06, 0x05, 0x00, 0x00, 0x01, 0x06,
 0x01, 0x04, 0x00, 0x05, 0x02, 0x02, 0x06, 0x06,
 0x02, 0x00, 0x06, 0x01, 0x01, 0x06, 0x01, 0x05,
 0x04, 0x04, 0x06, 0x05, 0x04, 0x03, 0x06, 0x06,
 0x05, 0x07, 0x03, 0x04, 0x05, 0x01, 0x05, 0x06,
 0x04, 0x04, 0x07, 0x03, 0x06, 0x01, 0x01, 0x01,
 0x04, 0x05, 0x06, 0x01, 0x00, 0x02, 0x00, 0x04,
 0x00, 0x05, 0x04, 0x05, 0x04, 0x01, 0x06, 0x06,
 0x06, 0x04, 0x04, 0x00, 0x07, 0x01, 0x06, 0x03,
 0x06, 0x07, 0x01, 0x01, 0x02, 0x00, 0x05, 0x02,
 0x01, 0x07, 0x02, 0x03, 0x06, 0x01, 0x06, 0x01,
 0x03, 0x01, 0x05, 0x03, 0x05, 0x06, 0x00, 0x01,
 0x05, 0x01, 0x01, 0x00, 0x01, 0x04, 0x01, 0x01,
 0x07, 0x06, 0x07, 0x04, 0x06, 0x03, 0x07, 0x04,
 0x02, 0x05, 0x00, 0x06, 0x02, 0x01, 0x01, 0x07,
 0x06, 0x05, 0x03, 0x04, 0x06, 0x00, 0x00, 0x01,
 0x05, 0x00, 0x06, 0x01, 0x07, 0x01, 0x04, 0x06,
 0x03, 0x07, 0x05, 0x07, 0x06, 0x01, 0x01, 0x00,
 0x04, 0x02, 0x01, 0x05, 0x07, 0x06, 0x01, 0x02,
 0x05, 0x06, 0x05, 0x03, 0x04, 0x05, 0x00, 0x00,
 0x01, 0x07, 0x01, 0x06, 0x00, 0x00, 0x02, 0x06,
 0x01, 0x04, 0x06, 0x02, 0x05, 0x00, 0x06, 0x05,
 0x00, 0x04, 0x07, 0x05, 0x06, 0x01, 0x01, 0x01,
 0x01, 0x03, 0x02, 0x05, 0x05, 0x01, 0x02, 0x04,
 0x00, 0x05, 0x01, 0x01, 0x06, 0x01, 0x02, 0x00,
 0x06, 0x07, 0x01, 0x01, 0x05, 0x04, 0x01, 0x01,
 0x04, 0x04, 0x02, 0x05, 0x04, 0x04, 0x03, 0x06,
 0x03, 0x01, 0x00, 0x05, 0x04, 0x05, 0x05, 0x01,
 0x04, 0x02, 0x00, 0x01, 0x05, 0x01, 0x00, 0x06,
 0x01, 0x01, 0x05, 0x01, 0x01, 0x06, 0x06, 0x04,
 0x06, 0x05, 0x02, 0x01, 0x01, 0x02, 0x01, 0x07,
 0x00, 0x05, 0x07, 0x00, 0x05, 0x04, 0x07, 0x03,
 0x01, 0x06, 0x06, 0x03, 0x01, 0x05, 0x00, 0x01,
 0x06, 0x06, 0x04, 0x00, 0x01, 0x00, 0x01, 0x05,
 0x06, 0x06, 0x01, 0x01, 0x01, 0x04, 0x01, 0x01,
 0x01, 0x01, 0x04, 0x04, 0x06, 0x01, 0x07, 0x00,
 0x01, 0x05, 0x04, 0x06, 0x05, 0x06, 0x07, 0x06,
 0x01, 0x03, 0x04, 0x07, 0x01, 0x06, 0x04, 0x00,
 0x06, 0x00, 0x05, 0x06, 0x01, 0x07, 0x06, 0x01,
 0x01, 0x03, 0x03, 0x03, 0x06, 0x00, 0x04, 0x05,
 0x07, 0x00, 0x05, 0x03, 0x07, 0x05, 0x02, 0x04,
 0x05, 0x05, 0x06, 0x06, 0x06, 0x01, 0x07, 0x01,
 0x01, 0x01, 0x06, 0x02, 0x01, 0x05, 0x00, 0x01,
 0x01, 0x01, 0x04, 0x06, 0x01, 0x05, 0x07, 0x00,
 0x03, 0x00, 0x01, 0x07, 0x01, 0x00, 0x06, 0x00,
 0x05, 0x01, 0x01, 0x04, 0x05, 0x03, 0x06, 0x01,
 0x05, 0x01, 0x05, 0x04, 0x00, 0x03, 0x02, 0x05,
 0x02, 0x03, 0x07, 0x01, 0x04, 0x05, 0x06, 0x04,
 0x01, 0x00, 0x01, 0x05, 0x06, 0x06, 0x01, 0x01,
 0x05, 0x04, 0x01, 0x00, 0x06, 0x04, 0x05, 0x01,
 0x02, 0x05, 0x00, 0x04, 0x06, 0x03, 0x06, 0x01,
 0x01, 0x05, 0x01, 0x04, 0x06, 0x05, 0x06, 0x06,
 0x02, 0x01, 0x05, 0x01, 0x01, 0x01, 0x05, 0x07,
 0x06, 0x06, 0x01, 0x00, 0x02, 0x07, 0x07, 0x01,
 0x04, 0x06, 0x02, 0x01, 0x02, 0x07, 0x03, 0x01,
 0x05, 0x07, 0x05, 0x06, 0x01, 0x04, 0x06, 0x07,
 0x00, 0x01, 0x01, 0x04, 0x02, 0x05, 0x06, 0x01,
 0x05, 0x01, 0x06, 0x01, 0x06, 0x01, 0x07, 0x04,
 0x01, 0x01, 0x01, 0x00, 0x05, 0x01, 0x04, 0x07,
 0x05, 0x06, 0x05, 0x05, 0x01, 0x02, 0x05, 0x04,
 0x03, 0x01, 0x01, 0x07, 0x04, 0x03, 0x07, 0x01,
 0x04, 0x01, 0x05, 0x02, 0x00, 0x02, 0x00, 0x02,
 0x05, 0x06, 0x03, 0x00, 0x04, 0x01, 0x01, 0x01,
 0x02, 0x03, 0x00, 0x06, 0x03, 0x07, 0x03, 0x02,
 0x03, 0x06, 0x01, 0x06, 0x00, 0x06, 0x01, 0x00,
 0x02, 0x03, 0x01, 0x06, 0x00, 0x00, 0x05, 0x05,
 0x05, 0x05, 0x06, 0x04, 0x01, 0x07, 0x06, 0x04,
 0x01, 0x01, 0x01, 0x05, 0x07, 0x07, 0x02, 0x03,
 0x05, 0x01, 0x03, 0x01, 0x06, 0x02, 0x06, 0x07,
 0x04, 0x01, 0x06, 0x04, 0x05, 0x00, 0x03, 0x01,
 0x01, 0x07, 0x05, 0x07, 0x01, 0x07, 0x05, 0x01,
 0x07, 0x06, 0x01, 0x06, 0x00, 0x01, 0x05, 0x06,
 0x00, 0x01, 0x00, 0x06, 0x00, 0x05, 0x04, 0x06,
 0x00, 0x00, 0x05, 0x04, 0x01, 0x07, 0x01, 0x00,
 0x03, 0x01, 0x07, 0x01, 0x00, 0x01, 0x00, 0x01,
 0x01, 0x01, 0x05, 0x03, 0x06, 0x01, 0x07, 0x07,
 0x04, 0x01, 0x01, 0x00, 0x00, 0x01, 0x05, 0x00,
 0x06, 0x00, 0x04, 0x06, 0x01, 0x07, 0x04, 0x06,
 0x01, 0x04, 0x06, 0x01, 0x00, 0x01, 0x04, 0x00,
 0x05, 0x05, 0x00, 0x01, 0x05, 0x05, 0x04, 0x00,
 0x00, 0x07, 0x01, 0x06, 0x04, 0x01, 0x03, 0x05,
 0x07, 0x00, 0x06, 0x06, 0x00, 0x03, 0x05, 0x01,
 0x02, 0x03, 0x05, 0x01, 0x01, 0x02, 0x07, 0x07,
 0x00, 0x01, 0x01, 0x04, 0x03, 0x07, 0x06, 0x06,
 0x06, 0x01, 0x05, 0x03, 0x05, 0x05, 0x01, 0x01,
 0x01, 0x05, 0x07, 0x05, 0x06, 0x06, 0x06, 0x04,
 0x05, 0x05, 0x06, 0x03, 0x01, 0x07, 0x01, 0x01,
 0x00, 0x07, 0x05, 0x01, 0x05, 0x01, 0x00, 0x01,
 0x05, 0x07, 0x00, 0x00, 0x00, 0x00, 0x01, 0x07,
 0x07, 0x01, 0x01, 0x01, 0x05, 0x03, 0x06, 0x02,
 0x00, 0x04, 0x04, 0x00, 0x05, 0x00, 0x06, 0x06,
 0x03, 0x04, 0x05, 0x07, 0x07, 0x01, 0x03, 0x05,
 0x03, 0x00, 0x01, 0x06, 0x01, 0x04, 0x05, 0x05,
 0x04, 0x07, 0x06, 0x04, 0x04, 0x00, 0x00, 0x05,
 0x06, 0x07, 0x07, 0x03, 0x06, 0x00, 0x05, 0x01,
 0x00, 0x05, 0x05, 0x07, 0x00, 0x01, 0x01, 0x00,
 0x01, 0x06, 0x07, 0x07, 0x03, 0x01, 0x05, 0x01,
 0x05, 0x06, 0x00, 0x03, 0x06, 0x01, 0x00, 0x01,
 0x04, 0x05, 0x07, 0x02, 0x01, 0x03, 0x05, 0x01,
 0x00, 0x01, 0x05, 0x06, 0x07, 0x04, 0x05, 0x07,
 0x06, 0x03, 0x05, 0x01, 0x05, 0x05, 0x06, 0x06,
 0x07, 0x02, 0x07, 0x00, 0x06, 0x05, 0x06, 0x01,
 0x06, 0x05, 0x03, 0x06, 0x07, 0x01, 0x05, 0x07,
 0x03, 0x06, 0x03, 0x02, 0x05, 0x07, 0x06, 0x05,
 0x04, 0x05, 0x01, 0x05, 0x04, 0x01, 0x04, 0x05,
 0x06, 0x06, 0x07, 0x00, 0x01, 0x03, 0x01, 0x00,
 0x00, 0x06, 0x00, 0x05, 0x05, 0x05, 0x01, 0x05,
 0x04, 0x05, 0x06, 0x07, 0x07, 0x06, 0x06, 0x06,
 0x06, 0x04, 0x06, 0x01, 0x05, 0x01, 0x00, 0x06,
 0x07, 0x06, 0x07, 0x03, 0x07, 0x04, 0x07, 0x01,
 0x06, 0x06, 0x07, 0x05, 0x04, 0x01, 0x04, 0x00,
 0x02, 0x03, 0x01, 0x05, 0x05, 0x05, 0x04, 0x04,
 0x03, 0x01, 0x04, 0x06, 0x01, 0x03, 0x06, 0x01,
 0x05, 0x02, 0x01, 0x01, 0x06, 0x01, 0x05, 0x01,
 0x07, 0x01, 0x05, 0x01, 0x01, 0x00, 0x01, 0x01,
 0x07, 0x06, 0x01, 0x05, 0x07, 0x01, 0x05, 0x00,
 0x04, 0x01, 0x05, 0x01, 0x05, 0x07, 0x05, 0x02,
 0x06, 0x01, 0x04, 0x07, 0x01, 0x06, 0x06, 0x05,
 0x01, 0x01, 0x07, 0x00, 0x02, 0x04, 0x05, 0x05,
 0x06, 0x06, 0x07, 0x04, 0x04, 0x03, 0x05, 0x04,
 0x06, 0x05, 0x01, 0x04, 0x03, 0x04, 0x05, 0x06,
 0x02, 0x05, 0x01, 0x06, 0x06, 0x03, 0x05, 0x06,
 0x06, 0x07, 0x06, 0x00, 0x01, 0x01, 0x01, 0x06,
 0x04, 0x00, 0x05, 0x02, 0x07, 0x00, 0x00, 0x05,
 0x02, 0x04, 0x06, 0x02, 0x06, 0x02, 0x06, 0x01,
 0x01, 0x00, 0x07, 0x05, 0x01, 0x02, 0x05, 0x01,
 0x07, 0x00, 0x04, 0x04, 0x03, 0x06, 0x01, 0x00,
 0x02, 0x02, 0x04, 0x07, 0x07, 0x01, 0x05, 0x01,
 0x01, 0x00, 0x05, 0x01, 0x06, 0x01, 0x02, 0x06,
 0x06, 0x06, 0x07, 0x00, 0x01, 0x05, 0x00, 0x01,
 0x06, 0x01, 0x05, 0x06, 0x06, 0x01, 0x05, 0x03,
 0x06, 0x01, 0x05, 0x04, 0x07, 0x06, 0x01, 0x04,
 0x06, 0x04, 0x01, 0x07, 0x05, 0x01, 0x06, 0x06,
 0x00, 0x05, 0x06, 0x04, 0x06, 0x01, 0x02, 0x07,
 0x00, 0x05, 0x05, 0x03, 0x02, 0x05, 0x01, 0x07,
 0x01, 0x05, 0x01, 0x00, 0x01, 0x05, 0x02, 0x06,
 0x05, 0x07, 0x04, 0x06, 0x01, 0x02, 0x04, 0x06,
 0x00, 0x01, 0x01, 0x01, 0x04, 0x06, 0x06, 0x00,
 0x06, 0x01, 0x02, 0x07, 0x06, 0x06, 0x04, 0x07,
 0x04, 0x05, 0x05, 0x07, 0x04, 0x00, 0x07, 0x07,
 0x06, 0x06, 0x05, 0x00, 0x01, 0x02, 0x05, 0x01,
 0x06, 0x05, 0x01, 0x06, 0x00, 0x07, 0x06, 0x05,
 0x02, 0x06, 0x07, 0x06, 0x07, 0x06, 0x06, 0x05,
 0x06, 0x04, 0x04, 0x04, 0x04, 0x07, 0x02, 0x07,
 0x00, 0x05, 0x01, 0x04, 0x04, 0x00, 0x07, 0x02,
 0x01, 0x01, 0x02, 0x01, 0x06, 0x04, 0x04, 0x05,
 0x07, 0x04, 0x04, 0x06, 0x07, 0x05, 0x01, 0x06,
 0x07, 0x06, 0x04, 0x07, 0x07, 0x00, 0x00, 0x01,
 0x02, 0x05, 0x01, 0x00, 0x01, 0x03, 0x00, 0x02,
 0x05, 0x01, 0x06, 0x00, 0x02, 0x02, 0x02, 0x07,
 0x00, 0x01, 0x01, 0x01, 0x06, 0x00, 0x00, 0x00,
 0x01, 0x03, 0x04, 0x07, 0x04, 0x03, 0x05, 0x00,
 0x01, 0x02, 0x06, 0x04, 0x02, 0x06, 0x05, 0x03,
 0x01, 0x01, 0x02, 0x01, 0x01, 0x00, 0x07, 0x05,
 0x01, 0x00, 0x00, 0x04, 0x06, 0x04, 0x00, 0x06,
 0x01, 0x00, 0x07, 0x05, 0x06, 0x01, 0x01, 0x07,
 0x05, 0x06, 0x06, 0x00, 0x00, 0x07, 0x02, 0x07,
 0x01, 0x06, 0x03, 0x06, 0x05, 0x07, 0x02, 0x07,
 0x07, 0x06, 0x07, 0x05, 0x06, 0x06, 0x01, 0x01,
 0x01, 0x06, 0x05, 0x01, 0x03, 0x04, 0x06, 0x00,
 0x00, 0x01, 0x01, 0x07, 0x00, 0x07, 0x05, 0x06,
 0x04, 0x01, 0x05, 0x06, 0x04, 0x00, 0x06, 0x04,
 0x03, 0x07, 0x01, 0x06, 0x07, 0x01, 0x01, 0x00,
 0x06, 0x05, 0x07, 0x07, 0x05, 0x05, 0x03, 0x06,
 0x06, 0x06, 0x01, 0x06, 0x04, 0x06, 0x05, 0x01,
 0x04, 0x06, 0x01, 0x06, 0x03, 0x00, 0x00, 0x00,
 0x05, 0x00, 0x00, 0x01, 0x01, 0x05, 0x07, 0x04,
 0x06, 0x06, 0x07, 0x05, 0x01, 0x04, 0x06, 0x06,
 0x06, 0x04, 0x00, 0x01, 0x03, 0x05, 0x06, 0x00,
 0x07, 0x01, 0x06, 0x00, 0x00, 0x06, 0x00, 0x07,
 0x00, 0x01, 0x00, 0x06, 0x07, 0x04, 0x06, 0x01,
 0x01, 0x00, 0x04, 0x06, 0x07, 0x04, 0x05, 0x00,
 0x03, 0x07, 0x06, 0x06, 0x05, 0x06, 0x01, 0x07,
 0x05, 0x02, 0x05, 0x06, 0x07, 0x02, 0x00, 0x06,
 0x06, 0x07, 0x01, 0x05, 0x07, 0x06, 0x06, 0x01,
 0x02, 0x06, 0x01, 0x07, 0x06, 0x04, 0x00, 0x02,
 0x06, 0x01, 0x05, 0x07, 0x01, 0x06, 0x05, 0x01,
 0x06, 0x02, 0x06, 0x04, 0x04, 0x05, 0x04, 0x04,
 0x05, 0x01, 0x01, 0x06, 0x01, 0x01, 0x07, 0x06,
 0x01, 0x06, 0x04, 0x07, 0x07, 0x02, 0x07, 0x01,
 0x07, 0x06, 0x01, 0x06, 0x00, 0x04, 0x07, 0x07,
 0x00, 0x01, 0x06, 0x00, 0x01, 0x03, 0x07, 0x05,
 0x07, 0x06, 0x02, 0x04, 0x02, 0x01, 0x07, 0x03,
 0x07, 0x01, 0x04, 0x05, 0x02, 0x07, 0x01, 0x02,
 0x06, 0x04, 0x02, 0x01, 0x04, 0x02, 0x07, 0x06,
 0x07, 0x02, 0x01, 0x05, 0x02, 0x03, 0x01, 0x01,
 0x05, 0x06, 0x04, 0x05, 0x06, 0x01, 0x02, 0x02,
 0x04, 0x00, 0x06, 0x06, 0x06, 0x06, 0x01, 0x06,
 0x00, 0x03, 0x00, 0x07, 0x05, 0x06, 0x01, 0x04,
 0x07, 0x01, 0x01, 0x01, 0x03, 0x05, 0x01, 0x06,
 0x00, 0x06, 0x06, 0x06, 0x06, 0x07, 0x02, 0x07,
 0x00, 0x06, 0x04, 0x00, 0x05, 0x06, 0x02, 0x01,
 0x00, 0x07, 0x01, 0x04, 0x06, 0x06, 0x00, 0x04,
 0x01, 0x03, 0x06, 0x05, 0x00, 0x06, 0x06, 0x01,
 0x05, 0x01, 0x04, 0x03, 0x06, 0x04, 0x05, 0x07,
 0x06, 0x00, 0x06, 0x02, 0x06, 0x06, 0x06, 0x02,
 0x06, 0x05, 0x01, 0x04, 0x01, 0x07, 0x05, 0x06,
 0x02, 0x07, 0x01, 0x04, 0x02, 0x06, 0x01, 0x01,
 0x06, 0x06, 0x01, 0x01, 0x04, 0x07, 0x04, 0x01,
 0x02, 0x05, 0x06, 0x01, 0x06, 0x00, 0x05, 0x06,
 0x02, 0x06, 0x06, 0x01, 0x04, 0x00, 0x00, 0x06,
 0x01, 0x03, 0x01, 0x07, 0x05, 0x07, 0x01, 0x05,
 0x04, 0x06, 0x00, 0x04, 0x03, 0x00, 0x05, 0x06,
 0x01, 0x06, 0x00, 0x03, 0x01, 0x04, 0x06, 0x02,
 0x00, 0x04, 0x01, 0x05, 0x01, 0x00, 0x06, 0x06,
 0x06, 0x07, 0x04, 0x06, 0x06, 0x05, 0x07, 0x00,
 0x06, 0x00, 0x02, 0x01, 0x03, 0x07, 0x04, 0x04,
 0x02, 0x04, 0x05, 0x02, 0x06, 0x00, 0x07, 0x06,
 0x03, 0x06, 0x05, 0x06, 0x01, 0x06, 0x06, 0x06,
 0x01, 0x01, 0x04, 0x01, 0x00, 0x06, 0x06, 0x03,
 0x00, 0x06, 0x04, 0x02, 0x01, 0x04, 0x07, 0x07,
 0x06, 0x04, 0x06, 0x00, 0x02, 0x00, 0x06, 0x04,
 0x07, 0x00, 0x05, 0x07, 0x00, 0x06, 0x06, 0x02,
 0x04, 0x07, 0x00, 0x06, 0x00, 0x01, 0x06, 0x04,
 0x06, 0x04, 0x04, 0x03, 0x06, 0x05, 0x02, 0x01,
 0x06, 0x04, 0x02, 0x06, 0x00, 0x04, 0x05, 0x01,
 0x07, 0x01, 0x04, 0x07, 0x04, 0x00, 0x07, 0x02,
 0x07, 0x06, 0x02, 0x00, 0x02, 0x01, 0x05, 0x07,
 0x05, 0x01, 0x02, 0x01, 0x01, 0x06, 0x06, 0x06,
 0x06, 0x00, 0x07, 0x06, 0x06, 0x06, 0x01, 0x02,
 0x05, 0x01, 0x06, 0x01, 0x05, 0x02, 0x01, 0x00,
 0x02, 0x06, 0x01, 0x01, 0x01, 0x00, 0x00, 0x07,
 0x04, 0x06, 0x05, 0x07, 0x01, 0x06, 0x05, 0x02,
 0x05, 0x07, 0x07, 0x01, 0x06, 0x07, 0x01, 0x07,
 0x04, 0x03, 0x05, 0x01, 0x01, 0x00, 0x02, 0x00,
 0x02, 0x02, 0x06, 0x06, 0x05, 0x06, 0x06, 0x06,
 0x02, 0x06, 0x04, 0x01, 0x06, 0x04, 0x05, 0x06,
 0x07, 0x00, 0x06, 0x00, 0x01, 0x07, 0x00, 0x05,
 0x01, 0x01, 0x07, 0x04, 0x01, 0x06, 0x06, 0x06,
 0x07, 0x06, 0x04, 0x05, 0x00, 0x05, 0x06, 0x02,
 0x06, 0x06, 0x01, 0x01, 0x00, 0x00, 0x03, 0x00,
 0x06, 0x02, 0x02, 0x00, 0x06, 0x02, 0x00, 0x03,
 0x05, 0x06, 0x04, 0x01, 0x06, 0x06, 0x01, 0x01,
 0x00, 0x00, 0x02, 0x06, 0x06, 0x07, 0x07, 0x07,
 0x06, 0x01, 0x05, 0x01, 0x06, 0x01, 0x06, 0x01,
 0x04, 0x03, 0x05, 0x04, 0x04, 0x07, 0x01, 0x03,
 0x05, 0x01, 0x02, 0x06, 0x02, 0x03, 0x05, 0x03,
 0x00, 0x00, 0x05, 0x05, 0x04, 0x03, 0x06, 0x01,
 0x01, 0x02, 0x02, 0x05, 0x07, 0x02, 0x07, 0x06,
 0x06, 0x02, 0x07, 0x01, 0x03, 0x01, 0x00, 0x01,
 0x06, 0x04, 0x01, 0x01, 0x07, 0x04, 0x06, 0x06,
 0x02, 0x01, 0x02, 0x06, 0x06, 0x01, 0x02, 0x06,
 0x04, 0x01, 0x06, 0x05, 0x06, 0x06, 0x01, 0x07,
 0x05, 0x07, 0x01, 0x04, 0x07, 0x02, 0x03, 0x01,
 0x00, 0x01, 0x04, 0x06, 0x06, 0x04, 0x05, 0x02,
 0x01, 0x00, 0x02, 0x01, 0x01, 0x06, 0x06, 0x06,
 0x07, 0x04, 0x02, 0x04, 0x01, 0x06, 0x01, 0x00,
 0x05, 0x00, 0x06, 0x07, 0x00, 0x00, 0x07, 0x01,
 0x03, 0x05, 0x01, 0x01, 0x07, 0x00, 0x01, 0x00,
 0x06, 0x06, 0x01, 0x07, 0x01, 0x05, 0x01, 0x01,
 0x01, 0x00, 0x06, 0x06, 0x01, 0x06, 0x05, 0x04,
 0x04, 0x07, 0x00, 0x07, 0x06, 0x07, 0x06, 0x02,
 0x05, 0x06, 0x07, 0x07, 0x01, 0x07, 0x07, 0x05,
 0x05, 0x06, 0x04, 0x02, 0x06, 0x07, 0x06, 0x00,
 0x01, 0x04, 0x02, 0x05, 0x01, 0x06, 0x06, 0x06,
 0x06, 0x02, 0x04, 0x06, 0x02, 0x01, 0x06, 0x06,
 0x01, 0x03, 0x06, 0x07, 0x00, 0x01, 0x00, 0x05,
 0x06, 0x00, 0x04, 0x05, 0x02, 0x02, 0x06, 0x04,
 0x01, 0x07, 0x05, 0x07, 0x01, 0x07, 0x02, 0x04,
 0x06, 0x06, 0x01, 0x07, 0x01, 0x01, 0x03, 0x01,
 0x00, 0x07, 0x01, 0x00, 0x06, 0x07, 0x06, 0x04,
 0x06, 0x01, 0x00, 0x06, 0x00, 0x05, 0x01, 0x06,
 0x01, 0x05, 0x06, 0x06, 0x01, 0x05, 0x03, 0x05,
 0x06, 0x06, 0x01, 0x06, 0x07, 0x06, 0x01, 0x07,
 0x05, 0x06, 0x02, 0x03, 0x01, 0x02, 0x02, 0x06,
 0x01, 0x06, 0x06, 0x06, 0x04, 0x01, 0x01, 0x05,
 0x05, 0x06, 0x05, 0x01, 0x06, 0x03, 0x05, 0x06,
 0x01, 0x02, 0x01, 0x01, 0x01, 0x05, 0x01, 0x01,
 0x06, 0x03, 0x05, 0x03, 0x06, 0x05, 0x06, 0x07,
 0x00, 0x06, 0x01, 0x01, 0x05, 0x05, 0x05, 0x03,
 0x07, 0x01, 0x02, 0x05, 0x02, 0x06, 0x06, 0x01,
 0x01, 0x04, 0x02, 0x02, 0x06, 0x07, 0x06, 0x00,
 0x05, 0x01, 0x03, 0x03, 0x06, 0x07, 0x03, 0x01,
 0x06, 0x06, 0x01, 0x03, 0x06, 0x03, 0x07, 0x07,
 0x07, 0x05, 0x01, 0x05, 0x00, 0x01, 0x02, 0x06,
 0x06, 0x00, 0x02, 0x01, 0x05, 0x05, 0x00, 0x01,
 0x05, 0x04, 0x00, 0x05, 0x06, 0x01, 0x06, 0x03,
 0x06, 0x05, 0x04, 0x06, 0x05, 0x01, 0x06, 0x01,
 0x04, 0x01, 0x02, 0x01, 0x02, 0x01, 0x06, 0x06,
 0x04, 0x06, 0x06, 0x07, 0x02, 0x07, 0x01, 0x01,
 0x07, 0x06, 0x01, 0x05, 0x00, 0x06, 0x02, 0x04,
 0x07, 0x01, 0x07, 0x07, 0x07, 0x04, 0x07, 0x01,
 0x06, 0x06, 0x05, 0x05, 0x01, 0x05, 0x05, 0x04,
 0x03, 0x07, 0x03, 0x05, 0x02, 0x06, 0x07, 0x00,
 0x01, 0x06, 0x05, 0x06, 0x02, 0x06, 0x06, 0x05,
 0x00, 0x06, 0x03, 0x02, 0x06, 0x06, 0x06, 0x06,
 0x07, 0x00, 0x07, 0x04, 0x06, 0x01, 0x07, 0x01,
 0x07, 0x02, 0x02, 0x02, 0x01, 0x00, 0x07, 0x05,
 0x01, 0x00, 0x07, 0x01, 0x07, 0x05, 0x06, 0x05,
 0x02, 0x06, 0x02, 0x01, 0x05, 0x00, 0x01, 0x05,
 0x07, 0x01, 0x04, 0x03, 0x01, 0x06, 0x06, 0x00,
 0x05, 0x02, 0x03, 0x06, 0x04, 0x07, 0x01, 0x05,
 0x02, 0x07, 0x06, 0x07, 0x02, 0x00, 0x06, 0x07,
 0x06, 0x01, 0x06, 0x06, 0x07, 0x03, 0x02, 0x06,
 0x07, 0x01, 0x05, 0x02, 0x07, 0x05, 0x01, 0x00,
 0x01, 0x05, 0x06, 0x02, 0x06, 0x00, 0x04, 0x01,
 0x06, 0x06, 0x03, 0x04, 0x01, 0x02, 0x05, 0x01,
 0x00, 0x05, 0x07, 0x03, 0x02, 0x07, 0x02, 0x05,
 0x07, 0x05, 0x05, 0x06, 0x00, 0x00, 0x06, 0x06,
 0x03, 0x01, 0x05, 0x01, 0x00, 0x03, 0x07, 0x00,
 0x07, 0x02, 0x07, 0x01, 0x03, 0x01, 0x02, 0x00,
 0x01, 0x00, 0x01, 0x00, 0x01, 0x01, 0x06, 0x06,
 0x05, 0x00, 0x01, 0x06, 0x02, 0x01, 0x06, 0x05,
 0x06, 0x00, 0x01, 0x07, 0x01, 0x06, 0x07, 0x06,
 0x06, 0x01, 0x01, 0x06, 0x06, 0x06, 0x05, 0x01,
 0x04, 0x06, 0x00, 0x04, 0x02, 0x01, 0x00, 0x07,
 0x00, 0x06, 0x07, 0x06, 0x07, 0x05, 0x01, 0x02,
 0x01, 0x06, 0x00, 0x01, 0x01, 0x05, 0x05, 0x02,
 0x02, 0x00, 0x06, 0x05, 0x05, 0x07, 0x05, 0x06,
 0x05, 0x06, 0x07, 0x07, 0x00, 0x01, 0x00, 0x00,
 0x01, 0x01, 0x06, 0x07, 0x06, 0x05, 0x07, 0x01,
 0x07, 0x06, 0x01, 0x01, 0x02, 0x05, 0x00, 0x01,
 0x01, 0x06, 0x00, 0x01, 0x05, 0x00, 0x03, 0x02,
 0x01, 0x01, 0x05, 0x06, 0x01, 0x01, 0x06, 0x06,
 0x00, 0x07, 0x07, 0x05, 0x05, 0x01, 0x00, 0x01,
 0x00, 0x07, 0x06, 0x06, 0x01, 0x07, 0x00, 0x00,
 0x07, 0x07, 0x06, 0x00, 0x01, 0x01, 0x06, 0x04,
 0x01, 0x02, 0x01, 0x06, 0x07, 0x01, 0x07, 0x01,
 0x05, 0x07, 0x00, 0x06, 0x02, 0x00, 0x07, 0x01,
 0x03, 0x07, 0x02, 0x01, 0x01, 0x04, 0x02, 0x04,
 0x01, 0x01, 0x06, 0x04, 0x01, 0x06, 0x04, 0x00,
 0x05, 0x06, 0x06, 0x06, 0x01, 0x01, 0x06, 0x05,
 0x01, 0x06, 0x00, 0x04, 0x06, 0x05, 0x05, 0x07,
 0x06, 0x06, 0x01, 0x05, 0x00, 0x06, 0x03, 0x06,
 0x05, 0x00, 0x07, 0x01, 0x01, 0x06, 0x01, 0x02,
 0x06, 0x01, 0x05, 0x01, 0x05, 0x01, 0x02, 0x02,
 0x04, 0x00, 0x04, 0x01, 0x06, 0x06, 0x06, 0x01,
 0x01, 0x00, 0x01, 0x06, 0x01, 0x07, 0x07, 0x04,
 0x06, 0x06, 0x02, 0x00, 0x06, 0x00, 0x02, 0x01,
 0x00, 0x01, 0x01, 0x07, 0x01, 0x01, 0x05, 0x07,
 0x07, 0x06, 0x02, 0x06, 0x02, 0x06, 0x02, 0x06,
 0x01, 0x03, 0x00, 0x00, 0x05, 0x05, 0x07, 0x05,
 0x01, 0x03, 0x07, 0x01, 0x04, 0x06, 0x05, 0x02,
 0x04, 0x06, 0x06, 0x01, 0x01, 0x06, 0x03, 0x06,
 0x07, 0x05, 0x06, 0x06, 0x07, 0x07, 0x02, 0x00,
 0x04, 0x01, 0x06, 0x06, 0x05, 0x02, 0x00, 0x01,
 0x03, 0x01, 0x01, 0x04, 0x03, 0x07, 0x04, 0x01,
 0x02, 0x07, 0x01, 0x03, 0x06, 0x04, 0x06, 0x01,
 0x00, 0x01, 0x06, 0x01, 0x01, 0x06, 0x01, 0x01
};

// Y min array (128 x 32)
static const int y_min[] =
{
 0x05, 0x0d, 0x0e, 0x0c, 0x04, 0x04, 0x09, 0x06,
 0x01, 0x07, 0x0d, 0x0c, 0x10, 0x0f, 0x02, 0x02,
 0x06, 0x0f, 0x0c, 0x03, 0x0d, 0x0b, 0x02, 0x0c,
 0x01, 0x01, 0x02, 0x05, 0x0b, 0x0d, 0x14, 0x07,
 0x0d, 0x09, 0x08, 0x02, 0x0f, 0x0d, 0x09, 0x03,
 0x02, 0x07, 0x0c, 0x09, 0x0e, 0x05, 0x10, 0x0f,
 0x0d, 0x06, 0x07, 0x0e, 0x10, 0x00, 0x0d, 0x03,
 0x0b, 0x03, 0x0a, 0x0e, 0x07, 0x07, 0x0c, 0x0d,
 0x05, 0x0a, 0x02, 0x0d, 0x07, 0x07, 0x01, 0x02,
 0x01, 0x04, 0x03, 0x0d, 0x0b, 0x04, 0x07, 0x0a,
 0x06, 0x09, 0x00, 0x05, 0x0d, 0x0e, 0x0f, 0x08,
 0x10, 0x0c, 0x0f, 0x08, 0x11, 0x0c, 0x03, 0x07,
 0x0c, 0x0f, 0x04, 0x05, 0x02, 0x0a, 0x02, 0x0d,
 0x0c, 0x0f, 0x04, 0x01, 0x08, 0x0a, 0x11, 0x02,
 0x03, 0x03, 0x0d, 0x13, 0x08, 0x01, 0x13, 0x07,
 0x06, 0x0f, 0x0d, 0x0f, 0x01, 0x10, 0x0c, 0x0b,
 0x04, 0x03, 0x06, 0x07, 0x03, 0x03, 0x0c, 0x04,
 0x05, 0x07, 0x09, 0x05, 0x03, 0x05, 0x06, 0x0b,
 0x10, 0x08, 0x11, 0x00, 0x10, 0x00, 0x01, 0x07,
 0x09, 0x0f, 0x01, 0x0c, 0x10, 0x0c, 0x03, 0x05,
 0x04, 0x03, 0x07, 0x0a, 0x03, 0x07, 0x0a, 0x0f,
 0x01, 0x06, 0x05, 0x05, 0x0e, 0x01, 0x08, 0x0f,
 0x0e, 0x0c, 0x09, 0x00, 0x0a, 0x0e, 0x07, 0x10,
 0x08, 0x07, 0x03, 0x0e, 0x07, 0x02, 0x07, 0x0a,
 0x0c, 0x0d, 0x02, 0x07, 0x0e, 0x06, 0x0a, 0x02,
 0x09, 0x02, 0x0c, 0x0e, 0x11, 0x07, 0x04, 0x07,
 0x0c, 0x02, 0x0e, 0x0c, 0x06, 0x04, 0x09, 0x0e,
 0x01, 0x03, 0x0f, 0x05, 0x11, 0x0b, 0x10, 0x05,
 0x06, 0x07, 0x0f, 0x0b, 0x09, 0x07, 0x00, 0x01,
 0x0c, 0x0d, 0x07, 0x04, 0x06, 0x0c, 0x0c, 0x10,
 0x0f, 0x0f, 0x06, 0x0e, 0x0d, 0x07, 0x0d, 0x0e,
 0x01, 0x07, 0x09, 0x07, 0x01, 0x04, 0x12, 0x03,
 0x08, 0x09, 0x02, 0x08, 0x06, 0x05, 0x0d, 0x0c,
 0x04, 0x0c, 0x06, 0x0f, 0x0d, 0x08, 0x0d, 0x01,
 0x02, 0x04, 0x00, 0x05, 0x08, 0x0c, 0x0a, 0x10,
 0x0c, 0x01, 0x09, 0x0f, 0x08, 0x12, 0x05, 0x0c,
 0x02, 0x04, 0x06, 0x0c, 0x0e, 0x0a, 0x09, 0x06,
 0x07, 0x09, 0x0c, 0x00, 0x12, 0x00, 0x09, 0x06,
 0x03, 0x00, 0x0f, 0x0c, 0x03, 0x00, 0x10, 0x0b,
 0x09, 0x09, 0x0b, 0x09, 0x0a, 0x0d, 0x01, 0x0b,
 0x0d, 0x0c, 0x06, 0x0e, 0x04, 0x13, 0x07, 0x0c,
 0x13, 0x0c, 0x04, 0x08, 0x08, 0x0c, 0x01, 0x01,
 0x0b, 0x0c, 0x07, 0x06, 0x06, 0x00, 0x01, 0x0d,
 0x03, 0x0e, 0x07, 0x04, 0x11, 0x05, 0x02, 0x13,
 0x0f, 0x04, 0x0d, 0x0c, 0x10, 0x0b, 0x05, 0x0e,
 0x04, 0x0a, 0x06, 0x06, 0x08, 0x03, 0x04, 0x04,
 0x15, 0x10, 0x01, 0x0f, 0x05, 0x0c, 0x01, 0x0c,
 0x07, 0x03, 0x0c, 0x01, 0x13, 0x0a, 0x0b, 0x13,
 0x09, 0x10, 0x0a, 0x05, 0x0c, 0x08, 0x01, 0x02,
 0x06, 0x04, 0x07, 0x0e, 0x11, 0x0e, 0x06, 0x01,
 0x04, 0x09, 0x03, 0x09, 0x07, 0x02, 0x0b, 0x0a,
 0x00, 0x0d, 0x03, 0x0f, 0x13, 0x0d, 0x09, 0x12,
 0x0f, 0x0a, 0x00, 0x00, 0x0b, 0x0d, 0x05, 0x0d,
 0x11, 0x02, 0x02, 0x01, 0x0d, 0x08, 0x07, 0x06,
 0x12, 0x12, 0x13, 0x0e, 0x0e, 0x0d, 0x00, 0x0f,
 0x01, 0x0b, 0x12, 0x14, 0x08, 0x0f, 0x12, 0x02,
 0x0b, 0x0d, 0x07, 0x0a, 0x09, 0x0e, 0x04, 0x02,
 0x0e, 0x0d, 0x0e, 0x02, 0x07, 0x05, 0x09, 0x0a,
 0x06, 0x0d, 0x01, 0x0b, 0x01, 0x0c, 0x07, 0x04,
 0x14, 0x0f, 0x10, 0x0e, 0x07, 0x07, 0x01, 0x04,
 0x02, 0x0d, 0x0d, 0x02, 0x0c, 0x07, 0x02, 0x0d,
 0x00, 0x10, 0x07, 0x10, 0x06, 0x06, 0x09, 0x02,
 0x10, 0x03, 0x10, 0x0f, 0x14, 0x13, 0x0e, 0x05,
 0x0b, 0x0a, 0x0c, 0x05, 0x10, 0x06, 0x06, 0x10,
 0x02, 0x0d, 0x0d, 0x07, 0x0c, 0x0d, 0x05, 0x10,
 0x0d, 0x0b, 0x0b, 0x0b, 0x05, 0x01, 0x07, 0x05,
 0x0e, 0x12, 0x0c, 0x09, 0x08, 0x05, 0x02, 0x09,
 0x0a, 0x10, 0x0d, 0x12, 0x0f, 0x07, 0x02, 0x0a,
 0x0a, 0x0e, 0x0d, 0x0e, 0x0f, 0x0b, 0x0b, 0x0c,
 0x05, 0x0a, 0x01, 0x0b, 0x00, 0x0a, 0x08, 0x0d,
 0x00, 0x0b, 0x05, 0x06, 0x0f, 0x04, 0x0f, 0x0c,
 0x0b, 0x0d, 0x0e, 0x0e, 0x12, 0x01, 0x08, 0x10,
 0x03, 0x07, 0x0d, 0x04, 0x0d, 0x0c, 0x0d, 0x04,
 0x0d, 0x07, 0x0b, 0x0f, 0x08, 0x0f, 0x07, 0x0f,
 0x0b, 0x08, 0x0f, 0x00, 0x0a, 0x08, 0x00, 0x08,
 0x0b, 0x0b, 0x03, 0x06, 0x0f, 0x0f, 0x12, 0x06,
 0x04, 0x02, 0x05, 0x0f, 0x02, 0x13, 0x0e, 0x00,
 0x07, 0x08, 0x0a, 0x0f, 0x11, 0x0f, 0x0c, 0x01,
 0x0d, 0x0e, 0x09, 0x03, 0x03, 0x0c, 0x12, 0x09,
 0x0b, 0x11, 0x10, 0x03, 0x07, 0x02, 0x14, 0x11,
 0x0d, 0x0c, 0x0d, 0x07, 0x10, 0x0b, 0x10, 0x0e,
 0x06, 0x0b, 0x05, 0x08, 0x08, 0x0d, 0x0e, 0x01,
 0x0a, 0x03, 0x0d, 0x08, 0x0f, 0x01, 0x0e, 0x04,
 0x0d, 0x11, 0x10, 0x00, 0x07, 0x06, 0x0a, 0x09,
 0x0b, 0x05, 0x09, 0x0f, 0x0b, 0x0d, 0x0c, 0x02,
 0x06, 0x0d, 0x01, 0x03, 0x05, 0x02, 0x03, 0x04,
 0x04, 0x0b, 0x0e, 0x13, 0x03, 0x03, 0x07, 0x0c,
 0x14, 0x0c, 0x0a, 0x10, 0x0d, 0x00, 0x13, 0x11,
 0x05, 0x0c, 0x02, 0x0f, 0x0f, 0x01, 0x14, 0x12,
 0x0e, 0x0a, 0x07, 0x04, 0x02, 0x08, 0x05, 0x06,
 0x02, 0x0a, 0x0d, 0x14, 0x10, 0x07, 0x04, 0x01,
 0x0c, 0x00, 0x0a, 0x14, 0x0a, 0x02, 0x14, 0x11,
 0x0f, 0x04, 0x0d, 0x0d, 0x0b, 0x06, 0x09, 0x06,
 0x08, 0x06, 0x0c, 0x0d, 0x10, 0x05, 0x0a, 0x0f,
 0x10, 0x0e, 0x09, 0x12, 0x02, 0x05, 0x0f, 0x03,
 0x12, 0x0b, 0x0b, 0x07, 0x05, 0x06, 0x10, 0x0d,
 0x05, 0x05, 0x0a, 0x0b, 0x02, 0x0e, 0x0a, 0x0b,
 0x0d, 0x01, 0x0f, 0x0b, 0x13, 0x0e, 0x10, 0x12,
 0x03, 0x09, 0x0d, 0x04, 0x06, 0x06, 0x07, 0x0c,
 0x05, 0x05, 0x0f, 0x08, 0x12, 0x00, 0x0c, 0x06,
 0x11, 0x0f, 0x06, 0x0d, 0x06, 0x12, 0x03, 0x09,
 0x0f, 0x09, 0x05, 0x11, 0x13, 0x0d, 0x03, 0x0a,
 0x02, 0x04, 0x06, 0x0f, 0x15, 0x0e, 0x06, 0x0b,
 0x03, 0x09, 0x13, 0x01, 0x02, 0x08, 0x0d, 0x08,
 0x0d, 0x0f, 0x05, 0x0f, 0x11, 0x09, 0x09, 0x0e,
 0x0a, 0x0d, 0x09, 0x0c, 0x00, 0x0b, 0x04, 0x01,
 0x13, 0x0d, 0x0d, 0x0f, 0x0f, 0x12, 0x0d, 0x06,
 0x02, 0x10, 0x12, 0x14, 0x0d, 0x10, 0x08, 0x0e,
 0x0f, 0x10, 0x00, 0x06, 0x08, 0x0d, 0x01, 0x01,
 0x0c, 0x0d, 0x03, 0x09, 0x0e, 0x10, 0x13, 0x09,
 0x0e, 0x08, 0x03, 0x11, 0x03, 0x10, 0x0c, 0x0c,
 0x09, 0x00, 0x07, 0x00, 0x0b, 0x02, 0x13, 0x08,
 0x0d, 0x0a, 0x0d, 0x04, 0x0a, 0x04, 0x10, 0x0e,
 0x0a, 0x0f, 0x08, 0x0a, 0x04, 0x0d, 0x0c, 0x04,
 0x0c, 0x0d, 0x11, 0x0a, 0x15, 0x10, 0x03, 0x0c,
 0x02, 0x10, 0x0c, 0x01, 0x0c, 0x0f, 0x14, 0x01,
 0x08, 0x03, 0x11, 0x0b, 0x0d, 0x08, 0x0e, 0x03,
 0x06, 0x07, 0x02, 0x13, 0x0b, 0x02, 0x10, 0x0d,
 0x09, 0x0e, 0x07, 0x0c, 0x09, 0x01, 0x09, 0x0a,
 0x0b, 0x0c, 0x10, 0x0e, 0x01, 0x04, 0x11, 0x11,
 0x14, 0x10, 0x0e, 0x0a, 0x10, 0x0f, 0x03, 0x02,
 0x0d, 0x0f, 0x10, 0x01, 0x08, 0x0b, 0x0a, 0x00,
 0x10, 0x02, 0x09, 0x0f, 0x12, 0x0d, 0x07, 0x0b,
 0x15, 0x0f, 0x0b, 0x0e, 0x14, 0x0a, 0x02, 0x11,
 0x09, 0x04, 0x00, 0x03, 0x02, 0x0a, 0x0c, 0x03,
 0x0e, 0x0f, 0x09, 0x08, 0x06, 0x0f, 0x11, 0x0b,
 0x09, 0x12, 0x0a, 0x01, 0x01, 0x0c, 0x0a, 0x0c,
 0x10, 0x13, 0x0e, 0x04, 0x09, 0x0f, 0x01, 0x08,
 0x0c, 0x0d, 0x0e, 0x10, 0x00, 0x02, 0x10, 0x0e,
 0x09, 0x06, 0x0f, 0x01, 0x00, 0x05, 0x0d, 0x14,
 0x0f, 0x11, 0x0c, 0x13, 0x15, 0x0b, 0x05, 0x05,
 0x0e, 0x0e, 0x14, 0x04, 0x08, 0x05, 0x03, 0x0e,
 0x03, 0x12, 0x06, 0x0e, 0x0c, 0x06, 0x0a, 0x11,
 0x0c, 0x0e, 0x0e, 0x0f, 0x01, 0x0c, 0x0e, 0x02,
 0x0f, 0x0d, 0x01, 0x01, 0x12, 0x11, 0x16, 0x03,
 0x00, 0x0e, 0x0e, 0x04, 0x09, 0x15, 0x02, 0x0b,
 0x0c, 0x06, 0x0e, 0x0e, 0x01, 0x04, 0x11, 0x0d,
 0x10, 0x0f, 0x09, 0x09, 0x00, 0x03, 0x13, 0x04,
 0x0a, 0x0d, 0x0c, 0x04, 0x0f, 0x0b, 0x0b, 0x14,
 0x08, 0x06, 0x01, 0x02, 0x0a, 0x14, 0x0d, 0x05,
 0x09, 0x02, 0x09, 0x0c, 0x0f, 0x0d, 0x11, 0x04,
 0x03, 0x0d, 0x0f, 0x01, 0x0e, 0x03, 0x03, 0x0b,
 0x08, 0x0e, 0x0e, 0x00, 0x14, 0x0f, 0x0c, 0x0c,
 0x01, 0x13, 0x16, 0x13, 0x09, 0x0a, 0x05, 0x0e,
 0x03, 0x0e, 0x0a, 0x11, 0x09, 0x00, 0x0a, 0x06,
 0x02, 0x06, 0x04, 0x10, 0x0e, 0x11, 0x09, 0x0d,
 0x14, 0x0f, 0x00, 0x09, 0x0c, 0x0a, 0x08, 0x00,
 0x13, 0x12, 0x0b, 0x09, 0x00, 0x11, 0x0d, 0x07,
 0x01, 0x12, 0x0c, 0x0b, 0x0b, 0x0e, 0x12, 0x0c,
 0x04, 0x0d, 0x07, 0x0d, 0x0a, 0x0e, 0x09, 0x0f,
 0x0d, 0x11, 0x03, 0x0d, 0x08, 0x00, 0x0c, 0x0e,
 0x05, 0x0d, 0x0e, 0x14, 0x0b, 0x11, 0x0b, 0x01,
 0x0a, 0x05, 0x12, 0x0a, 0x02, 0x0e, 0x0b, 0x10,
 0x06, 0x0b, 0x0e, 0x02, 0x0e, 0x09, 0x0d, 0x0e,
 0x0b, 0x04, 0x0c, 0x0c, 0x05, 0x10, 0x0d, 0x14,
 0x0e, 0x0e, 0x13, 0x04, 0x13, 0x0a, 0x01, 0x03,
 0x06, 0x14, 0x0c, 0x0c, 0x0e, 0x0e, 0x09, 0x10,
 0x0c, 0x0c, 0x0f, 0x09, 0x11, 0x0c, 0x01, 0x0c,
 0x11, 0x01, 0x0e, 0x10, 0x09, 0x0d, 0x00, 0x0f,
 0x0a, 0x0b, 0x0e, 0x0d, 0x0f, 0x01, 0x05, 0x0b,
 0x0e, 0x10, 0x11, 0x14, 0x11, 0x0b, 0x05, 0x07,
 0x09, 0x03, 0x0e, 0x0c, 0x02, 0x0b, 0x04, 0x11,
 0x0f, 0x0d, 0x0e, 0x0d, 0x0d, 0x0c, 0x18, 0x14,
 0x10, 0x15, 0x0e, 0x13, 0x11, 0x10, 0x08, 0x06,
 0x02, 0x06, 0x0f, 0x01, 0x09, 0x0f, 0x0f, 0x07,
 0x0b, 0x00, 0x19, 0x0d, 0x02, 0x02, 0x12, 0x10,
 0x0d, 0x0e, 0x0c, 0x0e, 0x0f, 0x0c, 0x04, 0x0b,
 0x0a, 0x0a, 0x0f, 0x08, 0x11, 0x10, 0x13, 0x02,
 0x0c, 0x0c, 0x07, 0x12, 0x0e, 0x11, 0x0d, 0x0f,
 0x12, 0x0d, 0x0b, 0x0a, 0x0a, 0x0d, 0x10, 0x09,
 0x08, 0x0e, 0x0d, 0x03, 0x0d, 0x0b, 0x10, 0x08,
 0x0a, 0x0f, 0x15, 0x0d, 0x0e, 0x0b, 0x0d, 0x10,
 0x00, 0x0d, 0x0b, 0x04, 0x0b, 0x13, 0x04, 0x0b,
 0x04, 0x0d, 0x08, 0x11, 0x0c, 0x0e, 0x0f, 0x09,
 0x0c, 0x12, 0x02, 0x0e, 0x0e, 0x0b, 0x11, 0x10,
 0x06, 0x13, 0x11, 0x16, 0x06, 0x08, 0x0f, 0x10,
 0x13, 0x02, 0x0d, 0x01, 0x0f, 0x06, 0x0a, 0x07,
 0x07, 0x15, 0x0f, 0x0f, 0x07, 0x0b, 0x10, 0x06,
 0x14, 0x04, 0x01, 0x11, 0x0e, 0x07, 0x09, 0x0f,
 0x0c, 0x03, 0x04, 0x0b, 0x14, 0x08, 0x01, 0x0c,
 0x13, 0x13, 0x0f, 0x00, 0x0c, 0x04, 0x0a, 0x0d,
 0x08, 0x0e, 0x09, 0x15, 0x0e, 0x10, 0x0c, 0x0f,
 0x0e, 0x0d, 0x0a, 0x07, 0x11, 0x14, 0x08, 0x02,
 0x0c, 0x11, 0x0f, 0x16, 0x0e, 0x08, 0x09, 0x0e,
 0x10, 0x0b, 0x06, 0x06, 0x06, 0x0c, 0x0d, 0x00,
 0x11, 0x0a, 0x0e, 0x02, 0x0f, 0x00, 0x04, 0x06,
 0x04, 0x03, 0x11, 0x0e, 0x09, 0x08, 0x0f, 0x16,
 0x10, 0x05, 0x02, 0x04, 0x0c, 0x0c, 0x0e, 0x16,
 0x06, 0x01, 0x05, 0x15, 0x0a, 0x13, 0x14, 0x0b,
 0x0b, 0x0e, 0x00, 0x0d, 0x17, 0x0d, 0x0f, 0x0d,
 0x00, 0x14, 0x10, 0x13, 0x0a, 0x11, 0x10, 0x02,
 0x0d, 0x0a, 0x10, 0x03, 0x15, 0x07, 0x09, 0x09,
 0x02, 0x0b, 0x01, 0x0b, 0x04, 0x05, 0x0e, 0x0e,
 0x07, 0x02, 0x05, 0x08, 0x11, 0x01, 0x0c, 0x0c,
 0x0d, 0x12, 0x0f, 0x09, 0x13, 0x13, 0x0b, 0x12,
 0x13, 0x10, 0x0a, 0x0d, 0x0c, 0x0a, 0x0d, 0x0a,
 0x09, 0x03, 0x09, 0x02, 0x0d, 0x0e, 0x0d, 0x0e,
 0x0d, 0x09, 0x12, 0x01, 0x07, 0x0e, 0x17, 0x14,
 0x08, 0x14, 0x0f, 0x0a, 0x01, 0x0d, 0x0e, 0x0e,
 0x0e, 0x00, 0x0a, 0x14, 0x16, 0x0d, 0x09, 0x0a,
 0x0d, 0x0d, 0x05, 0x0a, 0x0a, 0x0c, 0x0f, 0x0d,
 0x02, 0x0e, 0x04, 0x0d, 0x0e, 0x0d, 0x01, 0x06,
 0x0b, 0x07, 0x0d, 0x0e, 0x10, 0x0c, 0x07, 0x15,
 0x03, 0x0f, 0x04, 0x15, 0x0e, 0x0c, 0x13, 0x15,
 0x09, 0x11, 0x00, 0x13, 0x10, 0x0d, 0x02, 0x0b,
 0x12, 0x0d, 0x0e, 0x02, 0x0b, 0x03, 0x01, 0x05,
 0x0c, 0x09, 0x09, 0x05, 0x08, 0x14, 0x0a, 0x12,
 0x07, 0x0e, 0x09, 0x0e, 0x07, 0x11, 0x0e, 0x0b,
 0x0e, 0x0b, 0x0b, 0x04, 0x0b, 0x0c, 0x13, 0x0e,
 0x14, 0x01, 0x01, 0x0f, 0x10, 0x0c, 0x00, 0x0b,
 0x07, 0x01, 0x0f, 0x12, 0x11, 0x0a, 0x09, 0x13,
 0x05, 0x02, 0x12, 0x0b, 0x14, 0x13, 0x0a, 0x0d,
 0x06, 0x02, 0x10, 0x0d, 0x05, 0x0e, 0x08, 0x07,
 0x0e, 0x14, 0x03, 0x10, 0x14, 0x02, 0x08, 0x0e,
 0x10, 0x0c, 0x08, 0x0c, 0x0d, 0x0c, 0x10, 0x0e,
 0x0d, 0x0b, 0x08, 0x0f, 0x0e, 0x01, 0x0d, 0x00,
 0x0b, 0x02, 0x0d, 0x07, 0x11, 0x0f, 0x0f, 0x14,
 0x0e, 0x06, 0x01, 0x14, 0x09, 0x0d, 0x0d, 0x14,
 0x02, 0x0c, 0x0d, 0x0d, 0x0e, 0x00, 0x10, 0x10,
 0x0e, 0x0c, 0x0e, 0x0e, 0x05, 0x12, 0x0e, 0x03,
 0x05, 0x0e, 0x14, 0x0a, 0x07, 0x01, 0x05, 0x03,
 0x08, 0x13, 0x0d, 0x09, 0x0c, 0x0b, 0x0c, 0x0f,
 0x03, 0x0b, 0x11, 0x04, 0x0f, 0x0e, 0x08, 0x05,
 0x0f, 0x0d, 0x12, 0x12, 0x0c, 0x0f, 0x17, 0x0f,
 0x0c, 0x0d, 0x0b, 0x01, 0x07, 0x02, 0x08, 0x0b,
 0x0d, 0x06, 0x06, 0x12, 0x03, 0x08, 0x12, 0x14,
 0x01, 0x08, 0x06, 0x14, 0x02, 0x00, 0x15, 0x02,
 0x09, 0x0a, 0x06, 0x03, 0x10, 0x15, 0x0e, 0x0e,
 0x0c, 0x14, 0x05, 0x08, 0x10, 0x02, 0x0e, 0x01,
 0x13, 0x09, 0x0f, 0x01, 0x0b, 0x13, 0x09, 0x0e,
 0x13, 0x0a, 0x08, 0x10, 0x0f, 0x09, 0x16, 0x0d,
 0x19, 0x0b, 0x0a, 0x01, 0x08, 0x0f, 0x0e, 0x13,
 0x04, 0x06, 0x01, 0x12, 0x09, 0x0a, 0x0b, 0x07,
 0x08, 0x07, 0x09, 0x13, 0x02, 0x0b, 0x00, 0x0e,
 0x0f, 0x08, 0x0c, 0x0d, 0x14, 0x13, 0x08, 0x13,
 0x07, 0x05, 0x0e, 0x19, 0x0a, 0x09, 0x0d, 0x1a,
 0x06, 0x0a, 0x0e, 0x09, 0x03, 0x01, 0x09, 0x0e,
 0x17, 0x0e, 0x0c, 0x0a, 0x14, 0x0e, 0x08, 0x0d,
 0x0c, 0x01, 0x08, 0x0d, 0x12, 0x15, 0x08, 0x03,
 0x0b, 0x17, 0x0c, 0x0d, 0x10, 0x14, 0x02, 0x02,
 0x12, 0x0c, 0x09, 0x07, 0x17, 0x02, 0x0b, 0x0e,
 0x01, 0x0c, 0x00, 0x11, 0x0d, 0x0c, 0x11, 0x14,
 0x0e, 0x0f, 0x0b, 0x0a, 0x0d, 0x02, 0x0d, 0x04,
 0x04, 0x13, 0x12, 0x10, 0x0e, 0x18, 0x0d, 0x12,
 0x07, 0x07, 0x0b, 0x08, 0x16, 0x0c, 0x0d, 0x0a,
 0x12, 0x06, 0x0d, 0x0c, 0x0d, 0x07, 0x10, 0x0d,
 0x00, 0x12, 0x03, 0x08, 0x0b, 0x13, 0x0f, 0x17,
 0x0a, 0x0d, 0x05, 0x07, 0x13, 0x0f, 0x14, 0x13,
 0x08, 0x09, 0x0d, 0x00, 0x0f, 0x07, 0x01, 0x0f,
 0x0d, 0x04, 0x10, 0x0f, 0x14, 0x12, 0x08, 0x0e,
 0x0c, 0x15, 0x0e, 0x03, 0x15, 0x17, 0x09, 0x06,
 0x13, 0x0e, 0x07, 0x0d, 0x12, 0x08, 0x17, 0x17,
 0x02, 0x03, 0x14, 0x0b, 0x00, 0x03, 0x00, 0x14,
 0x10, 0x14, 0x0f, 0x02, 0x0b, 0x01, 0x0e, 0x0b,
 0x01, 0x15, 0x11, 0x0c, 0x04, 0x12, 0x0d, 0x09,
 0x0f, 0x03, 0x13, 0x0c, 0x0d, 0x0e, 0x13, 0x08,
 0x13, 0x04, 0x16, 0x0f, 0x08, 0x02, 0x0d, 0x0c,
 0x07, 0x14, 0x12, 0x0d, 0x0d, 0x15, 0x0a, 0x15,
 0x14, 0x14, 0x17, 0x0e, 0x09, 0x07, 0x09, 0x14,
 0x10, 0x12, 0x0b, 0x00, 0x09, 0x14, 0x0e, 0x09,
 0x14, 0x10, 0x0c, 0x12, 0x0c, 0x0d, 0x08, 0x0c,
 0x0d, 0x00, 0x12, 0x13, 0x0c, 0x14, 0x0b, 0x14,
 0x00, 0x07, 0x12, 0x0d, 0x0e, 0x18, 0x14, 0x0f,
 0x18, 0x02, 0x0e, 0x06, 0x0e, 0x15, 0x0b, 0x14,
 0x0d, 0x0e, 0x0a, 0x02, 0x16, 0x0f, 0x00, 0x0d,
 0x10, 0x13, 0x07, 0x0d, 0x18, 0x0d, 0x03, 0x10,
 0x01, 0x14, 0x0d, 0x0e, 0x08, 0x02, 0x0d, 0x00,
 0x0a, 0x06, 0x01, 0x03, 0x10, 0x06, 0x0f, 0x0c,
 0x0d, 0x0a, 0x0e, 0x14, 0x14, 0x11, 0x11, 0x16,
 0x13, 0x08, 0x12, 0x09, 0x0e, 0x07, 0x0e, 0x0e,
 0x00, 0x07, 0x09, 0x00, 0x0c, 0x16, 0x0d, 0x0f,
 0x16, 0x10, 0x13, 0x0e, 0x0d, 0x0c, 0x04, 0x0b,
 0x0d, 0x00, 0x0c, 0x0d, 0x02, 0x0a, 0x01, 0x13,
 0x07, 0x0b, 0x00, 0x0e, 0x0a, 0x09, 0x0f, 0x00,
 0x12, 0x18, 0x14, 0x0d, 0x0b, 0x09, 0x0e, 0x10,
 0x07, 0x09, 0x0c, 0x12, 0x17, 0x0e, 0x10, 0x01,
 0x14, 0x0f, 0x03, 0x0f, 0x05, 0x00, 0x08, 0x0e,
 0x09, 0x05, 0x0b, 0x0e, 0x0e, 0x12, 0x0d, 0x0d,
 0x08, 0x0e, 0x15, 0x15, 0x00, 0x00, 0x02, 0x09,
 0x14, 0x0e, 0x12, 0x0f, 0x0e, 0x15, 0x10, 0x11,
 0x10, 0x10, 0x05, 0x14, 0x02, 0x0d, 0x0b, 0x0c,
 0x11, 0x07, 0x07, 0x0b, 0x06, 0x04, 0x16, 0x08,
 0x01, 0x08, 0x15, 0x01, 0x06, 0x0b, 0x15, 0x0f,
 0x18, 0x0a, 0x05, 0x09, 0x01, 0x09, 0x0c, 0x13,
 0x02, 0x0d, 0x0b, 0x07, 0x02, 0x02, 0x0a, 0x0d,
 0x0b, 0x10, 0x18, 0x05, 0x13, 0x10, 0x08, 0x08,
 0x0e, 0x11, 0x0a, 0x0d, 0x05, 0x0c, 0x11, 0x0b,
 0x17, 0x0f, 0x0f, 0x0e, 0x0a, 0x00, 0x09, 0x17,
 0x0f, 0x04, 0x12, 0x00, 0x15, 0x0e, 0x02, 0x0d,
 0x00, 0x0d, 0x07, 0x0c, 0x0e, 0x01, 0x02, 0x10,
 0x05, 0x0f, 0x0a, 0x14, 0x10, 0x08, 0x0e, 0x07,
 0x07, 0x0e, 0x00, 0x16, 0x00, 0x02, 0x0a, 0x00,
 0x10, 0x01, 0x15, 0x09, 0x16, 0x14, 0x12, 0x0e,
 0x01, 0x0e, 0x0f, 0x10, 0x03, 0x0c, 0x09, 0x0c,
 0x11, 0x07, 0x0f, 0x01, 0x0e, 0x0e, 0x01, 0x10,
 0x0a, 0x14, 0x10, 0x18, 0x14, 0x0c, 0x0b, 0x0f,
 0x0e, 0x16, 0x04, 0x13, 0x16, 0x13, 0x16, 0x05,
 0x0a, 0x01, 0x0d, 0x08, 0x14, 0x13, 0x0f, 0x0d,
 0x14, 0x13, 0x00, 0x07, 0x11, 0x15, 0x02, 0x08,
 0x07, 0x0d, 0x01, 0x0e, 0x14, 0x12, 0x09, 0x0f,
 0x04, 0x09, 0x18, 0x14, 0x10, 0x08, 0x17, 0x14,
 0x0b, 0x14, 0x12, 0x0d, 0x11, 0x00, 0x0e, 0x0e,
 0x0c, 0x0a, 0x16, 0x02, 0x06, 0x0d, 0x02, 0x02,
 0x0f, 0x08, 0x0b, 0x09, 0x0c, 0x12, 0x08, 0x02,
 0x0e, 0x17, 0x1a, 0x0b, 0x0a, 0x0f, 0x14, 0x0d,
 0x09, 0x01, 0x08, 0x05, 0x00, 0x18, 0x03, 0x0f,
 0x10, 0x0a, 0x16, 0x02, 0x16, 0x17, 0x0b, 0x06,
 0x15, 0x02, 0x03, 0x02, 0x09, 0x00, 0x15, 0x17,
 0x09, 0x08, 0x17, 0x00, 0x06, 0x0f, 0x01, 0x07,
 0x0e, 0x05, 0x18, 0x09, 0x08, 0x10, 0x0b, 0x01,
 0x0e, 0x10, 0x0b, 0x12, 0x16, 0x11, 0x0c, 0x04,
 0x0c, 0x06, 0x08, 0x01, 0x12, 0x00, 0x0d, 0x05,
 0x01, 0x10, 0x09, 0x16, 0x18, 0x14, 0x0b, 0x02,
 0x0d, 0x0b, 0x01, 0x04, 0x09, 0x0a, 0x09, 0x0c,
 0x09, 0x0b, 0x16, 0x14, 0x18, 0x01, 0x0c, 0x18,
 0x0f, 0x0c, 0x01, 0x12, 0x01, 0x0b, 0x12, 0x09,
 0x03, 0x15, 0x16, 0x08, 0x0a, 0x17, 0x07, 0x06,
 0x02, 0x05, 0x0b, 0x11, 0x10, 0x14, 0x00, 0x13,
 0x18, 0x0d, 0x11, 0x0e, 0x01, 0x06, 0x09, 0x0e,
 0x17, 0x0f, 0x09, 0x0c, 0x12, 0x0a, 0x0a, 0x1a,
 0x16, 0x00, 0x0d, 0x05, 0x14, 0x14, 0x13, 0x18,
 0x0b, 0x0b, 0x0f, 0x0e, 0x10, 0x10, 0x11, 0x0f,
 0x11, 0x0d, 0x0c, 0x0d, 0x0c, 0x12, 0x01, 0x07,
 0x1a, 0x0e, 0x14, 0x0f, 0x09, 0x03, 0x1a, 0x0d,
 0x03, 0x00, 0x16, 0x0a, 0x02, 0x0d, 0x06, 0x15,
 0x0a, 0x17, 0x04, 0x18, 0x0e, 0x0f, 0x0e, 0x01,
 0x05, 0x19, 0x0b, 0x0c, 0x16, 0x15, 0x06, 0x03,
 0x0f, 0x14, 0x13, 0x0c, 0x14, 0x01, 0x01, 0x09,
 0x06, 0x1a, 0x18, 0x07, 0x07, 0x11, 0x00, 0x00,
 0x0f, 0x18, 0x00, 0x02, 0x01, 0x19, 0x15, 0x0c,
 0x08, 0x00, 0x19, 0x01, 0x0f, 0x10, 0x0d, 0x07,
 0x18, 0x07, 0x18, 0x10, 0x0b, 0x0c, 0x0d, 0x09,
 0x10, 0x0b, 0x09, 0x10, 0x01, 0x08, 0x14, 0x13,
 0x15, 0x0e, 0x0e, 0x17, 0x10, 0x0b, 0x19, 0x0d,
 0x0a, 0x0b, 0x0f, 0x18, 0x0e, 0x14, 0x14, 0x09,
 0x0a, 0x16, 0x08, 0x0e, 0x01, 0x17, 0x0f, 0x03,
 0x0d, 0x0c, 0x14, 0x10, 0x0e, 0x0e, 0x0e, 0x0c,
 0x18, 0x02, 0x0c, 0x0b, 0x18, 0x0f, 0x0c, 0x0b,
 0x11, 0x0e, 0x0b, 0x05, 0x13, 0x14, 0x08, 0x14,
 0x0f, 0x02, 0x07, 0x0f, 0x07, 0x0d, 0x14, 0x12,
 0x0c, 0x09, 0x19, 0x04, 0x09, 0x0b, 0x18, 0x0e,
 0x15, 0x01, 0x07, 0x0f, 0x10, 0x10, 0x02, 0x17,
 0x15, 0x0b, 0x01, 0x0e, 0x19, 0x08, 0x04, 0x00,
 0x02, 0x10, 0x01, 0x10, 0x04, 0x0b, 0x02, 0x00,
 0x0d, 0x03, 0x0b, 0x03, 0x01, 0x11, 0x16, 0x06,
 0x0e, 0x00, 0x13, 0x0f, 0x08, 0x0a, 0x0e, 0x17,
 0x03, 0x08, 0x09, 0x12, 0x1b, 0x0b, 0x02, 0x17,
 0x0e, 0x00, 0x0e, 0x14, 0x14, 0x02, 0x0d, 0x0e,
 0x14, 0x0d, 0x09, 0x15, 0x0c, 0x06, 0x12, 0x11,
 0x15, 0x0f, 0x05, 0x00, 0x0e, 0x0e, 0x01, 0x0d,
 0x0b, 0x0d, 0x16, 0x14, 0x0c, 0x10, 0x1b, 0x0b,
 0x11, 0x03, 0x0e, 0x00, 0x0b, 0x10, 0x03, 0x12,
 0x02, 0x02, 0x09, 0x0e, 0x19, 0x08, 0x06, 0x05,
 0x14, 0x03, 0x05, 0x08, 0x10, 0x1b, 0x19, 0x0e,
 0x0a, 0x18, 0x15, 0x14, 0x06, 0x13, 0x0b, 0x12,
 0x01, 0x0d, 0x0d, 0x18, 0x03, 0x0b, 0x13, 0x19,
 0x14, 0x02, 0x10, 0x12, 0x0a, 0x13, 0x0d, 0x09,
 0x09, 0x0a, 0x11, 0x13, 0x0b, 0x10, 0x04, 0x16,
 0x17, 0x10, 0x0b, 0x05, 0x0d, 0x01, 0x0a, 0x0a,
 0x13, 0x06, 0x16, 0x14, 0x16, 0x12, 0x04, 0x00,
 0x09, 0x14, 0x08, 0x08, 0x09, 0x03, 0x0f, 0x0c,
 0x16, 0x0f, 0x11, 0x17, 0x04, 0x10, 0x03, 0x0d,
 0x15, 0x10, 0x0c, 0x12, 0x14, 0x13, 0x13, 0x0d,
 0x18, 0x06, 0x0d, 0x08, 0x08, 0x01, 0x12, 0x06,
 0x16, 0x08, 0x15, 0x01, 0x19, 0x0d, 0x0d, 0x0b,
 0x14, 0x07, 0x07, 0x17, 0x14, 0x0e, 0x13, 0x14,
 0x0d, 0x0a, 0x0e, 0x03, 0x06, 0x01, 0x10, 0x0b,
 0x02, 0x00, 0x0b, 0x0f, 0x09, 0x12, 0x0c, 0x00,
 0x0c, 0x13, 0x08, 0x0d, 0x10, 0x06, 0x11, 0x00,
 0x0a, 0x0f, 0x1c, 0x09, 0x08, 0x17, 0x0e, 0x0f,
 0x0f, 0x09, 0x0a, 0x0c, 0x0c, 0x12, 0x15, 0x0c,
 0x0a, 0x04, 0x12, 0x0b, 0x18, 0x08, 0x0c, 0x09,
 0x0a, 0x09, 0x09, 0x0b, 0x0f, 0x0d, 0x09, 0x10,
 0x09, 0x14, 0x15, 0x00, 0x08, 0x14, 0x15, 0x09,
 0x01, 0x06, 0x10, 0x08, 0x12, 0x06, 0x0d, 0x09,
 0x0c, 0x0a, 0x00, 0x18, 0x0c, 0x17, 0x0c, 0x10,
 0x01, 0x07, 0x04, 0x0c, 0x01, 0x00, 0x06, 0x00,
 0x02, 0x00, 0x15, 0x15, 0x15, 0x0d, 0x14, 0x11,
 0x0a, 0x19, 0x0b, 0x0a, 0x10, 0x07, 0x00, 0x00,
 0x18, 0x0f, 0x0f, 0x05, 0x04, 0x00, 0x1a, 0x0e,
 0x14, 0x0e, 0x0e, 0x0d, 0x03, 0x02, 0x14, 0x0b,
 0x0e, 0x0a, 0x0f, 0x05, 0x11, 0x0e, 0x14, 0x11,
 0x0f, 0x18, 0x1a, 0x13, 0x17, 0x0d, 0x01, 0x0f,
 0x14, 0x0f, 0x16, 0x03, 0x0e, 0x18, 0x17, 0x0a,
 0x0e, 0x0b, 0x17, 0x12, 0x12, 0x00, 0x11, 0x08,
 0x0a, 0x11, 0x11, 0x0a, 0x13, 0x0c, 0x0e, 0x1d,
 0x0f, 0x10, 0x04, 0x0b, 0x16, 0x12, 0x06, 0x17,
 0x0e, 0x18, 0x06, 0x1b, 0x0f, 0x16, 0x1a, 0x0a,
 0x0f, 0x0a, 0x0a, 0x0b, 0x03, 0x0e, 0x06, 0x12,
 0x13, 0x05, 0x19, 0x0e, 0x10, 0x14, 0x17, 0x13,
 0x15, 0x0f, 0x0f, 0x13, 0x11, 0x11, 0x0b, 0x01,
 0x02, 0x0d, 0x0a, 0x0c, 0x0f, 0x11, 0x0b, 0x0f,
 0x16, 0x16, 0x16, 0x0b, 0x0d, 0x00, 0x0a, 0x19,
 0x0e, 0x14, 0x0e, 0x0e, 0x1a, 0x14, 0x0c, 0x03,
 0x0d, 0x16, 0x03, 0x0d, 0x07, 0x0e, 0x0e, 0x01,
 0x07, 0x15, 0x19, 0x17, 0x0b, 0x11, 0x0f, 0x11,
 0x0b, 0x0a, 0x03, 0x13, 0x0e, 0x16, 0x14, 0x0b,
 0x15, 0x0c, 0x10, 0x0e, 0x0d, 0x15, 0x00, 0x14,
 0x0c, 0x12, 0x08, 0x09, 0x18, 0x0e, 0x07, 0x05,
 0x13, 0x0d, 0x0c, 0x0c, 0x12, 0x0b, 0x10, 0x06,
 0x17, 0x01, 0x0c, 0x0e, 0x1d, 0x14, 0x0a, 0x11,
 0x02, 0x14, 0x0e, 0x0e, 0x0e, 0x0d, 0x07, 0x00,
 0x16, 0x0f, 0x0a, 0x10, 0x11, 0x11, 0x0c, 0x0d,
 0x0e, 0x08, 0x0e, 0x0c, 0x16, 0x16, 0x0c, 0x00,
 0x10, 0x09, 0x11, 0x05, 0x0e, 0x11, 0x0e, 0x0e,
 0x12, 0x0b, 0x02, 0x0c, 0x02, 0x0c, 0x14, 0x09,
 0x0d, 0x07, 0x10, 0x08, 0x11, 0x02, 0x02, 0x18,
 0x0d, 0x15, 0x14, 0x0e, 0x0e, 0x0d, 0x02, 0x11,
 0x0d, 0x0e, 0x14, 0x01, 0x0c, 0x16, 0x0c, 0x13,
 0x18, 0x10, 0x10, 0x0d, 0x12, 0x0e, 0x11, 0x0c,
 0x16, 0x01, 0x0e, 0x0c, 0x07, 0x0e, 0x1a, 0x13,
 0x13, 0x17, 0x15, 0x0f, 0x13, 0x14, 0x14, 0x11,
 0x04, 0x0b, 0x1b, 0x16, 0x0d, 0x16, 0x1c, 0x16,
 0x02, 0x0f, 0x11, 0x10, 0x16, 0x02, 0x06, 0x04,
 0x0f, 0x12, 0x11, 0x06, 0x12, 0x0d, 0x06, 0x09,
 0x0f, 0x0d, 0x10, 0x0d, 0x12, 0x0e, 0x1c, 0x06,
 0x0e, 0x18, 0x0a, 0x0e, 0x01, 0x19, 0x0d, 0x09,
 0x17, 0x0c, 0x0c, 0x01, 0x12, 0x01, 0x06, 0x10,
 0x13, 0x0a, 0x0b, 0x13, 0x12, 0x0c, 0x0c, 0x0f,
 0x03, 0x0b, 0x1c, 0x10, 0x01, 0x02, 0x0a, 0x12,
 0x09, 0x15, 0x04, 0x16, 0x01, 0x16, 0x1c, 0x0f,
 0x0d, 0x0c, 0x06, 0x0e, 0x16, 0x16, 0x0e, 0x15,
 0x00, 0x04, 0x05, 0x0d, 0x10, 0x12, 0x10, 0x0b,
 0x03, 0x13, 0x0f, 0x07, 0x14, 0x14, 0x0f, 0x0a,
 0x0e, 0x04, 0x0f, 0x17, 0x06, 0x0e, 0x10, 0x0c,
 0x04, 0x0c, 0x14, 0x14, 0x0c, 0x19, 0x06, 0x15,
 0x17, 0x08, 0x17, 0x12, 0x07, 0x01, 0x01, 0x12,
 0x15, 0x10, 0x06, 0x10, 0x0e, 0x0a, 0x0d, 0x0e,
 0x02, 0x03, 0x13, 0x0d, 0x0c, 0x03, 0x11, 0x09,
 0x15, 0x03, 0x0b, 0x17, 0x14, 0x0f, 0x0e, 0x11,
 0x07, 0x13, 0x15, 0x14, 0x03, 0x11, 0x00, 0x18,
 0x0f, 0x15, 0x11, 0x13, 0x0e, 0x17, 0x05, 0x16,
 0x0a, 0x14, 0x08, 0x15, 0x0b, 0x0a, 0x07, 0x10,
 0x1e, 0x0d, 0x05, 0x0d, 0x0d, 0x18, 0x0b, 0x08,
 0x15, 0x00, 0x00, 0x12, 0x14, 0x0a, 0x12, 0x15,
 0x02, 0x0d, 0x13, 0x03, 0x0e, 0x03, 0x0d, 0x11,
 0x10, 0x0b, 0x12, 0x0d, 0x06, 0x01, 0x0e, 0x17,
 0x1a, 0x04, 0x13, 0x00, 0x0c, 0x0c, 0x17, 0x0a,
 0x15, 0x1c, 0x12, 0x0a, 0x14, 0x16, 0x0e, 0x0b,
 0x0e, 0x0d, 0x16, 0x0d, 0x08, 0x07, 0x18, 0x08,
 0x0e, 0x10, 0x00, 0x0c, 0x0d, 0x09, 0x03, 0x06,
 0x02, 0x13, 0x1d, 0x19, 0x0c, 0x11, 0x0e, 0x17,
 0x00, 0x05, 0x0f, 0x17, 0x13, 0x0a, 0x00, 0x09,
 0x10, 0x0c, 0x11, 0x15, 0x08, 0x12, 0x05, 0x0d,
 0x12, 0x09, 0x11, 0x0a, 0x06, 0x03, 0x09, 0x03,
 0x11, 0x0b, 0x02, 0x09, 0x14, 0x15, 0x1b, 0x0a,
 0x0b, 0x16, 0x13, 0x00, 0x0a, 0x0b, 0x00, 0x16,
 0x04, 0x11, 0x17, 0x0e, 0x09, 0x10, 0x0f, 0x0c,
 0x00, 0x01, 0x10, 0x0c, 0x06, 0x0f, 0x01, 0x06,
 0x15, 0x14, 0x18, 0x12, 0x0e, 0x0e, 0x08, 0x00,
 0x0d, 0x10, 0x00, 0x01, 0x01, 0x09, 0x14, 0x0a,
 0x15, 0x12, 0x02, 0x0c, 0x19, 0x13, 0x07, 0x02,
 0x16, 0x08, 0x17, 0x13, 0x15, 0x04, 0x0e, 0x0f,
 0x14, 0x06, 0x0c, 0x09, 0x13, 0x0b, 0x0c, 0x00,
 0x02, 0x00, 0x15, 0x00, 0x08, 0x0e, 0x16, 0x0f,
 0x01, 0x0b, 0x0c, 0x12, 0x13, 0x10, 0x14, 0x00,
 0x18, 0x18, 0x10, 0x19, 0x13, 0x0b, 0x19, 0x14,
 0x14, 0x11, 0x08, 0x03, 0x02, 0x0a, 0x1c, 0x17,
 0x12, 0x08, 0x0b, 0x15, 0x09, 0x06, 0x1b, 0x15,
 0x0a, 0x14, 0x16, 0x0f, 0x0e, 0x13, 0x17, 0x14,
 0x00, 0x09, 0x02, 0x08, 0x0f, 0x06, 0x09, 0x0a,
 0x02, 0x14, 0x04, 0x0e, 0x16, 0x0d, 0x01, 0x14,
 0x00, 0x00, 0x08, 0x0e, 0x17, 0x00, 0x11, 0x17,
 0x12, 0x0e, 0x0b, 0x08, 0x11, 0x0f, 0x0e, 0x14,
 0x05, 0x0f, 0x0a, 0x0b, 0x0a, 0x03, 0x00, 0x0d,
 0x0c, 0x17, 0x01, 0x07, 0x03, 0x00, 0x0a, 0x00,
 0x15, 0x09, 0x02, 0x0c, 0x14, 0x1a, 0x0e, 0x0a,
 0x02, 0x0e, 0x13, 0x13, 0x15, 0x0e, 0x0e, 0x13,
 0x09, 0x16, 0x05, 0x0e, 0x0d, 0x0a, 0x03, 0x0e,
 0x10, 0x01, 0x0d, 0x03, 0x0c, 0x1c, 0x0a, 0x10,
 0x01, 0x1a, 0x14, 0x17, 0x0b, 0x19, 0x10, 0x0f,
 0x19, 0x0e, 0x1c, 0x0b, 0x16, 0x13, 0x0e, 0x08,
 0x01, 0x06, 0x1b, 0x0b, 0x00, 0x0b, 0x01, 0x03,
 0x15, 0x11, 0x17, 0x19, 0x0b, 0x0f, 0x16, 0x0e,
 0x11, 0x11, 0x0f, 0x06, 0x11, 0x11, 0x05, 0x0e,
 0x1b, 0x13, 0x0c, 0x00, 0x01, 0x13, 0x0a, 0x0c,
 0x0c, 0x13, 0x00, 0x14, 0x0e, 0x13, 0x15, 0x0c,
 0x07, 0x11, 0x12, 0x10, 0x09, 0x09, 0x0a, 0x08,
 0x0b, 0x0d, 0x01, 0x09, 0x17, 0x13, 0x00, 0x15,
 0x0a, 0x0e, 0x18, 0x08, 0x15, 0x0f, 0x10, 0x06,
 0x15, 0x0d, 0x11, 0x14, 0x10, 0x14, 0x0b, 0x01,
 0x00, 0x02, 0x0a, 0x1b, 0x07, 0x06, 0x00, 0x01,
 0x10, 0x0a, 0x06, 0x17, 0x15, 0x04, 0x03, 0x01,
 0x0a, 0x11, 0x0d, 0x11, 0x01, 0x0e, 0x0a, 0x10,
 0x15, 0x0f, 0x09, 0x14, 0x07, 0x0b, 0x05, 0x10,
 0x01, 0x03, 0x03, 0x11, 0x0f, 0x00, 0x06, 0x0e,
 0x09, 0x11, 0x13, 0x0a, 0x13, 0x0b, 0x01, 0x0e,
 0x12, 0x15, 0x19, 0x14, 0x11, 0x0c, 0x11, 0x10,
 0x02, 0x14, 0x18, 0x14, 0x0c, 0x14, 0x02, 0x14,
 0x14, 0x15, 0x0c, 0x12, 0x11, 0x0d, 0x0e, 0x07,
 0x0d, 0x19, 0x10, 0x0c, 0x03, 0x0e, 0x1c, 0x11,
 0x0a, 0x15, 0x10, 0x0f, 0x16, 0x13, 0x01, 0x0a,
 0x13, 0x13, 0x12, 0x01, 0x10, 0x0a, 0x0b, 0x01,
 0x0f, 0x14, 0x0e, 0x09, 0x0c, 0x0d, 0x0f, 0x00,
 0x12, 0x02, 0x09, 0x0e, 0x0f, 0x0f, 0x0c, 0x08,
 0x0f, 0x09, 0x18, 0x19, 0x11, 0x0f, 0x08, 0x09,
 0x05, 0x08, 0x0e, 0x0c, 0x01, 0x0d, 0x00, 0x00,
 0x15, 0x16, 0x08, 0x0d, 0x0d, 0x0e, 0x0e, 0x0d,
 0x12, 0x0f, 0x0c, 0x11, 0x17, 0x09, 0x0f, 0x0e,
 0x01, 0x14, 0x05, 0x0a, 0x01, 0x0f, 0x02, 0x0e,
 0x1a, 0x02, 0x11, 0x12, 0x10, 0x0a, 0x03, 0x13,
 0x0a, 0x14, 0x07, 0x14, 0x0a, 0x11, 0x19, 0x0f,
 0x00, 0x0e, 0x08, 0x06, 0x07, 0x0b, 0x06, 0x00,
 0x15, 0x08, 0x0a, 0x16, 0x11, 0x0f, 0x03, 0x01,
 0x16, 0x02, 0x03, 0x12, 0x1c, 0x00, 0x1a, 0x07,
 0x00, 0x0c, 0x12, 0x01, 0x02, 0x13, 0x10, 0x1e,
 0x09, 0x0b, 0x05, 0x0d, 0x0b, 0x00, 0x0e, 0x13,
 0x0c, 0x0c, 0x01, 0x00, 0x15, 0x15, 0x09, 0x13,
 0x02, 0x05, 0x0e, 0x08, 0x10, 0x0e, 0x01, 0x14,
 0x00, 0x0d, 0x0e, 0x0a, 0x02, 0x15, 0x0c, 0x08,
 0x07, 0x13, 0x11, 0x03, 0x08, 0x01, 0x14, 0x0c,
 0x1b, 0x09, 0x12, 0x10, 0x11, 0x13, 0x0b, 0x10
};

// Y max array (128 x 32)
static const int y_max[] =
{
 0x17, 0x18, 0x1b, 0x1f, 0x11, 0x1b, 0x12, 0x15,
 0x16, 0x0e, 0x14, 0x1d, 0x1f, 0x11, 0x12, 0x19,
 0x10, 0x12, 0x11, 0x0f, 0x0e, 0x1a, 0x10, 0x13,
 0x15, 0x0f, 0x10, 0x0f, 0x17, 0x0e, 0x1e, 0x1b,
 0x1c, 0x1b, 0x13, 0x12, 0x17, 0x1d, 0x15, 0x18,
 0x19, 0x1b, 0x13, 0x0f, 0x13, 0x12, 0x13, 0x18,
 0x0f, 0x1b, 0x14, 0x16, 0x19, 0x17, 0x10, 0x18,
 0x1f, 0x16, 0x15, 0x1a, 0x0f, 0x0e, 0x10, 0x1d,
 0x12, 0x14, 0x16, 0x0f, 0x16, 0x17, 0x1a, 0x1b,
 0x1d, 0x1a, 0x10, 0x12, 0x1a, 0x1b, 0x13, 0x13,
 0x0f, 0x17, 0x1a, 0x10, 0x12, 0x12, 0x18, 0x16,
 0x15, 0x12, 0x1e, 0x12, 0x16, 0x11, 0x10, 0x10,
 0x1f, 0x1e, 0x1a, 0x1a, 0x16, 0x14, 0x19, 0x15,
 0x11, 0x1a, 0x16, 0x0e, 0x1c, 0x16, 0x1c, 0x11,
 0x19, 0x17, 0x0e, 0x16, 0x11, 0x0e, 0x19, 0x0d,
 0x12, 0x1c, 0x13, 0x1f, 0x0c, 0x15, 0x15, 0x1c,
 0x11, 0x13, 0x11, 0x19, 0x1d, 0x1f, 0x1d, 0x14,
 0x15, 0x1b, 0x1f, 0x15, 0x13, 0x12, 0x1a, 0x13,
 0x1e, 0x10, 0x19, 0x10, 0x13, 0x13, 0x1d, 0x0d,
 0x0f, 0x17, 0x12, 0x17, 0x14, 0x13, 0x1e, 0x10,
 0x1b, 0x19, 0x1c, 0x1c, 0x14, 0x1b, 0x19, 0x18,
 0x13, 0x19, 0x1f, 0x0f, 0x1f, 0x15, 0x10, 0x12,
 0x12, 0x13, 0x12, 0x12, 0x10, 0x15, 0x19, 0x1a,
 0x1c, 0x1b, 0x1f, 0x10, 0x0b, 0x10, 0x1f, 0x12,
 0x15, 0x10, 0x19, 0x1d, 0x12, 0x13, 0x12, 0x0d,
 0x10, 0x10, 0x1b, 0x1a, 0x1f, 0x0f, 0x1b, 0x1c,
 0x1d, 0x1b, 0x13, 0x10, 0x0f, 0x0d, 0x12, 0x10,
 0x10, 0x0f, 0x18, 0x11, 0x1a, 0x13, 0x12, 0x0c,
 0x15, 0x13, 0x10, 0x1d, 0x15, 0x17, 0x18, 0x10,
 0x1e, 0x10, 0x18, 0x0e, 0x1a, 0x19, 0x19, 0x1b,
 0x10, 0x11, 0x15, 0x14, 0x1e, 0x1a, 0x15, 0x1a,
 0x1e, 0x10, 0x0c, 0x0f, 0x19, 0x19, 0x1a, 0x10,
 0x1e, 0x11, 0x14, 0x15, 0x19, 0x1f, 0x13, 0x1c,
 0x12, 0x1c, 0x0f, 0x1f, 0x18, 0x10, 0x0f, 0x14,
 0x18, 0x0b, 0x15, 0x0f, 0x14, 0x11, 0x16, 0x12,
 0x1c, 0x1e, 0x0f, 0x1d, 0x19, 0x1e, 0x1e, 0x1e,
 0x10, 0x12, 0x18, 0x12, 0x18, 0x1b, 0x1c, 0x14,
 0x1c, 0x0f, 0x12, 0x13, 0x1d, 0x12, 0x15, 0x1e,
 0x0f, 0x1f, 0x1d, 0x18, 0x1f, 0x1c, 0x15, 0x1b,
 0x0e, 0x13, 0x11, 0x12, 0x0e, 0x17, 0x0e, 0x1e,
 0x1a, 0x11, 0x1a, 0x17, 0x0d, 0x1d, 0x10, 0x1f,
 0x1e, 0x12, 0x0f, 0x1f, 0x12, 0x14, 0x11, 0x1b,
 0x1e, 0x0f, 0x18, 0x17, 0x16, 0x11, 0x1b, 0x1a,
 0x1a, 0x1b, 0x13, 0x1b, 0x17, 0x1a, 0x19, 0x1d,
 0x18, 0x15, 0x12, 0x18, 0x1c, 0x10, 0x15, 0x1f,
 0x0e, 0x17, 0x0f, 0x1f, 0x12, 0x1e, 0x16, 0x0c,
 0x1e, 0x13, 0x12, 0x15, 0x0f, 0x10, 0x1d, 0x14,
 0x14, 0x16, 0x1e, 0x11, 0x1d, 0x1e, 0x11, 0x1f,
 0x15, 0x16, 0x14, 0x12, 0x11, 0x1a, 0x1a, 0x14,
 0x19, 0x1b, 0x0d, 0x0f, 0x12, 0x15, 0x18, 0x11,
 0x0f, 0x1f, 0x16, 0x1b, 0x15, 0x18, 0x15, 0x0f,
 0x11, 0x10, 0x17, 0x1e, 0x1d, 0x18, 0x0c, 0x1a,
 0x11, 0x12, 0x1b, 0x14, 0x19, 0x17, 0x11, 0x0f,
 0x17, 0x17, 0x10, 0x18, 0x13, 0x11, 0x12, 0x13,
 0x18, 0x1d, 0x1e, 0x0f, 0x0f, 0x0f, 0x1a, 0x12,
 0x10, 0x11, 0x1c, 0x1f, 0x0e, 0x11, 0x1c, 0x10,
 0x0f, 0x0f, 0x13, 0x1a, 0x17, 0x1d, 0x18, 0x12,
 0x11, 0x1a, 0x19, 0x12, 0x15, 0x1f, 0x12, 0x1d,
 0x10, 0x10, 0x17, 0x1e, 0x0e, 0x1d, 0x11, 0x11,
 0x1a, 0x12, 0x1a, 0x13, 0x0e, 0x10, 0x0c, 0x17,
 0x19, 0x11, 0x11, 0x19, 0x10, 0x1d, 0x12, 0x14,
 0x1d, 0x1f, 0x1d, 0x11, 0x11, 0x11, 0x18, 0x14,
 0x1e, 0x18, 0x1b, 0x1b, 0x1d, 0x19, 0x11, 0x15,
 0x11, 0x17, 0x1c, 0x0d, 0x15, 0x0f, 0x1b, 0x13,
 0x12, 0x12, 0x19, 0x18, 0x16, 0x10, 0x0e, 0x14,
 0x16, 0x12, 0x1f, 0x0f, 0x11, 0x18, 0x1a, 0x14,
 0x12, 0x19, 0x11, 0x19, 0x18, 0x11, 0x11, 0x1f,
 0x16, 0x11, 0x10, 0x19, 0x1e, 0x17, 0x1f, 0x13,
 0x13, 0x12, 0x1b, 0x14, 0x12, 0x16, 0x13, 0x1a,
 0x1f, 0x16, 0x17, 0x12, 0x16, 0x15, 0x0f, 0x10,
 0x11, 0x18, 0x15, 0x0d, 0x16, 0x1b, 0x12, 0x1a,
 0x0f, 0x17, 0x14, 0x15, 0x19, 0x0f, 0x0f, 0x19,
 0x1c, 0x10, 0x10, 0x1a, 0x14, 0x1c, 0x11, 0x12,
 0x1d, 0x0f, 0x1d, 0x13, 0x14, 0x16, 0x13, 0x11,
 0x1b, 0x19, 0x1d, 0x0c, 0x10, 0x12, 0x16, 0x10,
 0x18, 0x1e, 0x10, 0x1b, 0x11, 0x1a, 0x1e, 0x0b,
 0x1b, 0x0f, 0x18, 0x15, 0x1e, 0x1d, 0x1c, 0x1b,
 0x12, 0x19, 0x0f, 0x17, 0x16, 0x17, 0x1d, 0x1c,
 0x11, 0x10, 0x16, 0x0e, 0x1d, 0x11, 0x1f, 0x1a,
 0x0f, 0x1f, 0x13, 0x10, 0x10, 0x16, 0x1e, 0x16,
 0x0e, 0x13, 0x11, 0x18, 0x1c, 0x10, 0x12, 0x0f,
 0x13, 0x1d, 0x18, 0x1d, 0x10, 0x10, 0x12, 0x11,
 0x11, 0x0e, 0x13, 0x1b, 0x11, 0x0e, 0x1b, 0x11,
 0x16, 0x1c, 0x1f, 0x1f, 0x15, 0x11, 0x0f, 0x10,
 0x1a, 0x13, 0x10, 0x12, 0x17, 0x15, 0x1f, 0x1f,
 0x16, 0x0f, 0x13, 0x1d, 0x0f, 0x0d, 0x0f, 0x1a,
 0x0e, 0x11, 0x10, 0x1a, 0x0d, 0x19, 0x0f, 0x1c,
 0x18, 0x15, 0x11, 0x15, 0x14, 0x14, 0x1d, 0x1d,
 0x10, 0x10, 0x16, 0x14, 0x1a, 0x11, 0x19, 0x1e,
 0x11, 0x17, 0x0d, 0x1e, 0x13, 0x1c, 0x11, 0x0f,
 0x0e, 0x10, 0x15, 0x1e, 0x1f, 0x1e, 0x15, 0x11,
 0x1c, 0x0d, 0x13, 0x1b, 0x12, 0x15, 0x1a, 0x1a,
 0x13, 0x11, 0x1a, 0x18, 0x15, 0x10, 0x15, 0x12,
 0x10, 0x1a, 0x15, 0x11, 0x12, 0x19, 0x14, 0x12,
 0x11, 0x0f, 0x0e, 0x1e, 0x13, 0x1e, 0x1f, 0x0d,
 0x1e, 0x11, 0x0f, 0x16, 0x19, 0x11, 0x11, 0x13,
 0x18, 0x10, 0x1a, 0x1a, 0x1f, 0x12, 0x19, 0x13,
 0x12, 0x1c, 0x12, 0x1b, 0x1e, 0x0f, 0x12, 0x1e,
 0x11, 0x0c, 0x10, 0x0e, 0x1f, 0x0c, 0x14, 0x14,
 0x13, 0x0d, 0x19, 0x0d, 0x17, 0x0c, 0x15, 0x0f,
 0x1a, 0x13, 0x13, 0x12, 0x1d, 0x1a, 0x18, 0x12,
 0x1b, 0x1a, 0x15, 0x1d, 0x18, 0x1d, 0x14, 0x10,
 0x11, 0x12, 0x1a, 0x10, 0x1c, 0x19, 0x0d, 0x1d,
 0x18, 0x10, 0x1f, 0x0a, 0x11, 0x1a, 0x15, 0x12,
 0x12, 0x1f, 0x0e, 0x11, 0x1e, 0x15, 0x12, 0x12,
 0x1c, 0x13, 0x18, 0x1f, 0x0d, 0x1f, 0x13, 0x11,
 0x1e, 0x10, 0x0f, 0x11, 0x12, 0x1f, 0x0e, 0x1c,
 0x0c, 0x12, 0x19, 0x1e, 0x10, 0x1d, 0x13, 0x10,
 0x14, 0x16, 0x11, 0x13, 0x1d, 0x14, 0x1c, 0x15,
 0x10, 0x1a, 0x1e, 0x19, 0x0f, 0x11, 0x18, 0x0d,
 0x18, 0x10, 0x14, 0x1e, 0x0d, 0x19, 0x19, 0x0f,
 0x14, 0x0f, 0x1c, 0x18, 0x1d, 0x0b, 0x1b, 0x11,
 0x1a, 0x1d, 0x17, 0x1e, 0x13, 0x17, 0x11, 0x1a,
 0x15, 0x1c, 0x11, 0x1f, 0x12, 0x18, 0x0f, 0x17,
 0x12, 0x15, 0x12, 0x15, 0x1c, 0x19, 0x12, 0x18,
 0x1f, 0x14, 0x12, 0x12, 0x11, 0x1d, 0x1b, 0x0f,
 0x14, 0x1c, 0x1a, 0x1b, 0x12, 0x13, 0x11, 0x0d,
 0x0e, 0x1c, 0x0f, 0x1e, 0x1a, 0x14, 0x16, 0x13,
 0x1c, 0x10, 0x1b, 0x10, 0x1a, 0x18, 0x15, 0x0f,
 0x1f, 0x1e, 0x13, 0x15, 0x11, 0x0f, 0x1a, 0x18,
 0x1e, 0x12, 0x11, 0x1b, 0x13, 0x1b, 0x11, 0x1a,
 0x10, 0x1a, 0x18, 0x13, 0x13, 0x1e, 0x11, 0x15,
 0x13, 0x1e, 0x12, 0x10, 0x1f, 0x12, 0x1b, 0x18,
 0x1f, 0x14, 0x0f, 0x0f, 0x1d, 0x0f, 0x0b, 0x1e,
 0x1c, 0x11, 0x0f, 0x1e, 0x12, 0x11, 0x1a, 0x1f,
 0x12, 0x1d, 0x14, 0x13, 0x13, 0x1b, 0x17, 0x12,
 0x11, 0x17, 0x11, 0x10, 0x15, 0x0e, 0x1a, 0x0e,
 0x11, 0x18, 0x11, 0x0f, 0x17, 0x15, 0x0b, 0x0c,
 0x12, 0x11, 0x17, 0x1c, 0x1a, 0x0d, 0x13, 0x0f,
 0x13, 0x1e, 0x10, 0x0b, 0x0e, 0x17, 0x13, 0x18,
 0x11, 0x12, 0x11, 0x19, 0x1f, 0x0f, 0x16, 0x10,
 0x16, 0x18, 0x1f, 0x0b, 0x14, 0x16, 0x0b, 0x10,
 0x16, 0x1e, 0x1f, 0x15, 0x18, 0x14, 0x17, 0x17,
 0x0f, 0x10, 0x0f, 0x1c, 0x11, 0x11, 0x12, 0x15,
 0x11, 0x11, 0x0f, 0x08, 0x19, 0x15, 0x1e, 0x08,
 0x0d, 0x10, 0x12, 0x0f, 0x16, 0x1f, 0x17, 0x12,
 0x1f, 0x18, 0x1b, 0x11, 0x15, 0x0c, 0x1b, 0x1c,
 0x13, 0x13, 0x11, 0x0c, 0x10, 0x13, 0x1e, 0x13,
 0x13, 0x10, 0x12, 0x0f, 0x12, 0x13, 0x19, 0x1d,
 0x11, 0x17, 0x12, 0x10, 0x15, 0x1b, 0x19, 0x17,
 0x11, 0x10, 0x1d, 0x10, 0x10, 0x11, 0x1e, 0x0e,
 0x16, 0x1d, 0x12, 0x18, 0x12, 0x0e, 0x0d, 0x16,
 0x11, 0x15, 0x19, 0x0b, 0x1b, 0x1a, 0x1d, 0x13,
 0x19, 0x16, 0x1f, 0x19, 0x1b, 0x0e, 0x0f, 0x10,
 0x15, 0x11, 0x19, 0x16, 0x0f, 0x0f, 0x11, 0x1c,
 0x10, 0x14, 0x13, 0x1f, 0x1e, 0x16, 0x17, 0x11,
 0x1d, 0x10, 0x0b, 0x15, 0x12, 0x1b, 0x19, 0x15,
 0x1b, 0x1d, 0x0f, 0x0d, 0x0c, 0x14, 0x13, 0x12,
 0x14, 0x1d, 0x0f, 0x13, 0x11, 0x1a, 0x18, 0x11,
 0x16, 0x0f, 0x14, 0x15, 0x12, 0x12, 0x19, 0x13,
 0x0f, 0x17, 0x0e, 0x16, 0x15, 0x08, 0x13, 0x1c,
 0x18, 0x0f, 0x11, 0x1f, 0x10, 0x16, 0x13, 0x0c,
 0x1c, 0x13, 0x1f, 0x12, 0x0e, 0x19, 0x1a, 0x19,
 0x12, 0x12, 0x0f, 0x1e, 0x16, 0x12, 0x0f, 0x11,
 0x19, 0x16, 0x11, 0x1b, 0x0c, 0x14, 0x17, 0x1b,
 0x12, 0x1e, 0x1d, 0x11, 0x1a, 0x11, 0x09, 0x16,
 0x0e, 0x19, 0x11, 0x11, 0x12, 0x0f, 0x18, 0x1f,
 0x10, 0x14, 0x13, 0x17, 0x1a, 0x1e, 0x0a, 0x1f,
 0x1a, 0x0e, 0x11, 0x12, 0x16, 0x12, 0x13, 0x12,
 0x12, 0x11, 0x12, 0x14, 0x12, 0x0d, 0x0d, 0x1f,
 0x14, 0x17, 0x1d, 0x1a, 0x15, 0x0f, 0x15, 0x12,
 0x1f, 0x1d, 0x13, 0x17, 0x15, 0x10, 0x13, 0x14,
 0x12, 0x16, 0x14, 0x15, 0x13, 0x0f, 0x1f, 0x1f,
 0x13, 0x1f, 0x10, 0x1b, 0x16, 0x12, 0x14, 0x0f,
 0x0d, 0x15, 0x16, 0x1a, 0x18, 0x11, 0x11, 0x0d,
 0x12, 0x19, 0x1f, 0x11, 0x10, 0x15, 0x1c, 0x1e,
 0x1b, 0x13, 0x14, 0x1a, 0x11, 0x15, 0x0a, 0x12,
 0x10, 0x11, 0x10, 0x10, 0x13, 0x11, 0x1a, 0x0f,
 0x0f, 0x13, 0x11, 0x18, 0x0f, 0x1e, 0x16, 0x11,
 0x1f, 0x10, 0x16, 0x19, 0x13, 0x12, 0x11, 0x17,
 0x0c, 0x11, 0x13, 0x13, 0x14, 0x14, 0x12, 0x0f,
 0x0e, 0x15, 0x1d, 0x17, 0x0f, 0x1a, 0x12, 0x14,
 0x18, 0x15, 0x1c, 0x11, 0x1f, 0x19, 0x10, 0x13,
 0x14, 0x12, 0x16, 0x1b, 0x10, 0x11, 0x1a, 0x11,
 0x17, 0x1f, 0x0a, 0x1b, 0x18, 0x13, 0x1d, 0x11,
 0x0c, 0x1a, 0x15, 0x1f, 0x0b, 0x11, 0x16, 0x15,
 0x18, 0x13, 0x1f, 0x19, 0x12, 0x0e, 0x1d, 0x0c,
 0x0d, 0x1f, 0x12, 0x1e, 0x1f, 0x13, 0x11, 0x0f,
 0x1e, 0x0d, 0x0c, 0x15, 0x12, 0x17, 0x12, 0x19,
 0x11, 0x16, 0x11, 0x15, 0x1d, 0x18, 0x07, 0x14,
 0x1d, 0x1e, 0x1c, 0x17, 0x14, 0x10, 0x16, 0x15,
 0x11, 0x13, 0x14, 0x1d, 0x11, 0x14, 0x11, 0x11,
 0x1e, 0x0e, 0x14, 0x12, 0x1f, 0x1e, 0x18, 0x0e,
 0x11, 0x17, 0x1b, 0x1f, 0x10, 0x11, 0x1d, 0x17,
 0x13, 0x14, 0x1b, 0x12, 0x12, 0x1b, 0x0f, 0x1c,
 0x1b, 0x14, 0x19, 0x0b, 0x11, 0x0f, 0x10, 0x11,
 0x0c, 0x1a, 0x19, 0x13, 0x12, 0x0c, 0x16, 0x1e,
 0x14, 0x0f, 0x07, 0x13, 0x0e, 0x1e, 0x17, 0x1e,
 0x12, 0x15, 0x12, 0x1e, 0x1c, 0x1b, 0x1d, 0x1a,
 0x17, 0x0f, 0x1e, 0x14, 0x1f, 0x18, 0x15, 0x10,
 0x0b, 0x18, 0x18, 0x1a, 0x12, 0x1c, 0x1c, 0x0b,
 0x13, 0x0f, 0x13, 0x0c, 0x18, 0x0d, 0x11, 0x0d,
 0x13, 0x1c, 0x1b, 0x11, 0x17, 0x19, 0x0f, 0x12,
 0x16, 0x10, 0x0d, 0x11, 0x12, 0x0f, 0x18, 0x12,
 0x0f, 0x1d, 0x1e, 0x10, 0x1b, 0x1f, 0x11, 0x18,
 0x1b, 0x11, 0x0e, 0x11, 0x1b, 0x1b, 0x13, 0x11,
 0x16, 0x10, 0x0f, 0x0e, 0x1d, 0x11, 0x12, 0x13,
 0x10, 0x18, 0x18, 0x0c, 0x12, 0x16, 0x1e, 0x17,
 0x10, 0x1a, 0x14, 0x16, 0x14, 0x12, 0x13, 0x14,
 0x14, 0x0d, 0x10, 0x19, 0x1e, 0x12, 0x0d, 0x11,
 0x10, 0x1f, 0x0a, 0x10, 0x12, 0x13, 0x1d, 0x15,
 0x11, 0x13, 0x1d, 0x13, 0x12, 0x1a, 0x0d, 0x11,
 0x12, 0x11, 0x11, 0x14, 0x14, 0x13, 0x0f, 0x1e,
 0x17, 0x1b, 0x0b, 0x1b, 0x13, 0x18, 0x19, 0x1e,
 0x0e, 0x19, 0x14, 0x1e, 0x11, 0x10, 0x0b, 0x15,
 0x1a, 0x1f, 0x13, 0x0d, 0x13, 0x14, 0x10, 0x12,
 0x11, 0x12, 0x0c, 0x0b, 0x10, 0x17, 0x10, 0x17,
 0x1e, 0x15, 0x18, 0x12, 0x0d, 0x12, 0x11, 0x16,
 0x12, 0x19, 0x17, 0x10, 0x12, 0x15, 0x1b, 0x12,
 0x1f, 0x0c, 0x11, 0x15, 0x18, 0x11, 0x0d, 0x1a,
 0x1b, 0x10, 0x19, 0x15, 0x1d, 0x16, 0x0f, 0x1e,
 0x1e, 0x0f, 0x19, 0x15, 0x1f, 0x17, 0x10, 0x0f,
 0x10, 0x0e, 0x15, 0x11, 0x1a, 0x13, 0x12, 0x0f,
 0x0f, 0x19, 0x0b, 0x1f, 0x18, 0x0d, 0x0e, 0x12,
 0x11, 0x16, 0x0d, 0x13, 0x11, 0x18, 0x12, 0x13,
 0x12, 0x10, 0x1a, 0x13, 0x10, 0x0c, 0x1f, 0x0d,
 0x12, 0x15, 0x12, 0x13, 0x16, 0x19, 0x12, 0x1e,
 0x0f, 0x0d, 0x0e, 0x1e, 0x11, 0x11, 0x15, 0x1e,
 0x11, 0x17, 0x10, 0x15, 0x1b, 0x1e, 0x11, 0x1e,
 0x15, 0x12, 0x16, 0x0f, 0x09, 0x1f, 0x10, 0x0d,
 0x1d, 0x11, 0x1b, 0x12, 0x18, 0x1f, 0x0c, 0x0d,
 0x1c, 0x1a, 0x15, 0x13, 0x11, 0x13, 0x17, 0x11,
 0x1d, 0x11, 0x18, 0x08, 0x12, 0x16, 0x1c, 0x16,
 0x17, 0x0f, 0x17, 0x1a, 0x13, 0x13, 0x1f, 0x12,
 0x13, 0x13, 0x0e, 0x0e, 0x1f, 0x1e, 0x10, 0x0f,
 0x11, 0x18, 0x1d, 0x1d, 0x14, 0x14, 0x1d, 0x1b,
 0x0f, 0x0e, 0x15, 0x1f, 0x17, 0x0d, 0x1e, 0x0a,
 0x11, 0x11, 0x0e, 0x0b, 0x17, 0x1e, 0x13, 0x12,
 0x1d, 0x17, 0x0d, 0x1a, 0x18, 0x1f, 0x0f, 0x0e,
 0x1f, 0x0e, 0x14, 0x0f, 0x0d, 0x19, 0x0e, 0x12,
 0x18, 0x17, 0x11, 0x14, 0x12, 0x15, 0x1e, 0x13,
 0x1e, 0x1d, 0x0b, 0x0d, 0x0f, 0x18, 0x10, 0x1d,
 0x0e, 0x12, 0x0d, 0x1b, 0x1b, 0x10, 0x12, 0x11,
 0x0c, 0x13, 0x0e, 0x18, 0x14, 0x11, 0x0c, 0x1c,
 0x1d, 0x0c, 0x10, 0x10, 0x19, 0x15, 0x1b, 0x14,
 0x0f, 0x12, 0x13, 0x1f, 0x11, 0x18, 0x14, 0x1e,
 0x11, 0x0f, 0x10, 0x1b, 0x0d, 0x09, 0x12, 0x12,
 0x1e, 0x0f, 0x11, 0x10, 0x1c, 0x10, 0x16, 0x11,
 0x15, 0x10, 0x0d, 0x13, 0x1d, 0x1c, 0x14, 0x0c,
 0x15, 0x1f, 0x13, 0x11, 0x1f, 0x19, 0x0b, 0x13,
 0x17, 0x1a, 0x16, 0x1d, 0x1d, 0x0a, 0x12, 0x10,
 0x0f, 0x12, 0x0e, 0x19, 0x13, 0x15, 0x12, 0x18,
 0x12, 0x1f, 0x0e, 0x13, 0x12, 0x0a, 0x13, 0x14,
 0x11, 0x19, 0x18, 0x11, 0x17, 0x1d, 0x10, 0x19,
 0x0d, 0x0c, 0x11, 0x14, 0x1d, 0x12, 0x0f, 0x12,
 0x17, 0x0f, 0x13, 0x17, 0x10, 0x0c, 0x15, 0x11,
 0x08, 0x1f, 0x0d, 0x0d, 0x1b, 0x16, 0x1a, 0x1d,
 0x14, 0x19, 0x10, 0x0d, 0x1e, 0x13, 0x1a, 0x1d,
 0x0e, 0x1d, 0x1b, 0x10, 0x10, 0x11, 0x0a, 0x10,
 0x11, 0x1a, 0x18, 0x12, 0x19, 0x13, 0x10, 0x0f,
 0x13, 0x1d, 0x12, 0x0d, 0x19, 0x1f, 0x10, 0x15,
 0x17, 0x11, 0x13, 0x10, 0x14, 0x10, 0x1c, 0x1e,
 0x0d, 0x0e, 0x1c, 0x1a, 0x11, 0x18, 0x11, 0x1b,
 0x11, 0x1a, 0x11, 0x17, 0x11, 0x0f, 0x11, 0x13,
 0x0c, 0x1d, 0x15, 0x10, 0x08, 0x13, 0x0f, 0x1a,
 0x10, 0x13, 0x17, 0x11, 0x10, 0x10, 0x18, 0x0c,
 0x17, 0x10, 0x1f, 0x12, 0x0c, 0x07, 0x10, 0x0f,
 0x12, 0x16, 0x1b, 0x11, 0x10, 0x1b, 0x1e, 0x1d,
 0x1d, 0x1b, 0x1d, 0x11, 0x10, 0x14, 0x11, 0x1e,
 0x1c, 0x1d, 0x1b, 0x0d, 0x14, 0x1b, 0x11, 0x0e,
 0x18, 0x12, 0x1e, 0x16, 0x14, 0x12, 0x0b, 0x12,
 0x15, 0x0d, 0x15, 0x18, 0x0f, 0x1a, 0x10, 0x16,
 0x09, 0x11, 0x17, 0x13, 0x0f, 0x1f, 0x1e, 0x13,
 0x1f, 0x09, 0x10, 0x0c, 0x1a, 0x19, 0x13, 0x1d,
 0x11, 0x12, 0x10, 0x0c, 0x1d, 0x15, 0x0e, 0x11,
 0x15, 0x1e, 0x0f, 0x16, 0x1d, 0x10, 0x1a, 0x15,
 0x1a, 0x18, 0x13, 0x12, 0x0b, 0x09, 0x0f, 0x0a,
 0x0b, 0x12, 0x08, 0x11, 0x11, 0x0c, 0x17, 0x12,
 0x0f, 0x11, 0x1d, 0x19, 0x1d, 0x13, 0x13, 0x19,
 0x14, 0x11, 0x1f, 0x0c, 0x10, 0x11, 0x15, 0x12,
 0x11, 0x0f, 0x0c, 0x1d, 0x0f, 0x1c, 0x0e, 0x16,
 0x1f, 0x18, 0x18, 0x16, 0x0f, 0x14, 0x10, 0x11,
 0x0e, 0x12, 0x12, 0x14, 0x0b, 0x11, 0x1b, 0x1d,
 0x12, 0x11, 0x11, 0x18, 0x19, 0x14, 0x11, 0x0d,
 0x18, 0x1f, 0x1d, 0x10, 0x11, 0x1d, 0x14, 0x11,
 0x0d, 0x15, 0x11, 0x13, 0x1b, 0x12, 0x12, 0x08,
 0x1e, 0x12, 0x11, 0x12, 0x0d, 0x0f, 0x0f, 0x1e,
 0x0f, 0x10, 0x12, 0x12, 0x11, 0x17, 0x14, 0x0f,
 0x0e, 0x12, 0x1a, 0x1c, 0x10, 0x0d, 0x17, 0x0d,
 0x18, 0x11, 0x18, 0x16, 0x18, 0x19, 0x13, 0x1d,
 0x14, 0x15, 0x11, 0x1f, 0x0c, 0x10, 0x14, 0x16,
 0x13, 0x13, 0x0a, 0x1d, 0x0e, 0x0b, 0x1f, 0x0e,
 0x0b, 0x10, 0x17, 0x08, 0x0b, 0x14, 0x19, 0x11,
 0x1f, 0x12, 0x0c, 0x0e, 0x06, 0x0e, 0x15, 0x17,
 0x19, 0x0e, 0x12, 0x1e, 0x11, 0x12, 0x0c, 0x11,
 0x11, 0x15, 0x1f, 0x0d, 0x1f, 0x14, 0x14, 0x1a,
 0x13, 0x12, 0x10, 0x13, 0x15, 0x0e, 0x15, 0x1b,
 0x1c, 0x11, 0x15, 0x17, 0x14, 0x08, 0x0c, 0x1f,
 0x1e, 0x10, 0x18, 0x19, 0x1d, 0x1e, 0x0c, 0x0f,
 0x13, 0x16, 0x0f, 0x16, 0x0f, 0x08, 0x1d, 0x13,
 0x0e, 0x10, 0x0d, 0x19, 0x17, 0x14, 0x10, 0x0d,
 0x0f, 0x0f, 0x0b, 0x1d, 0x0f, 0x10, 0x0d, 0x0a,
 0x17, 0x11, 0x1b, 0x0c, 0x1e, 0x15, 0x1a, 0x10,
 0x07, 0x0f, 0x11, 0x13, 0x13, 0x13, 0x11, 0x12,
 0x13, 0x0b, 0x10, 0x1c, 0x15, 0x14, 0x0c, 0x12,
 0x0f, 0x18, 0x16, 0x1f, 0x1f, 0x10, 0x13, 0x14,
 0x10, 0x1e, 0x0d, 0x15, 0x19, 0x1e, 0x1f, 0x09,
 0x0b, 0x0e, 0x0e, 0x0c, 0x19, 0x19, 0x11, 0x10,
 0x16, 0x15, 0x0b, 0x0f, 0x15, 0x19, 0x0a, 0x16,
 0x0a, 0x10, 0x11, 0x12, 0x1e, 0x16, 0x0f, 0x10,
 0x0d, 0x0e, 0x1e, 0x15, 0x12, 0x0b, 0x1a, 0x1e,
 0x0f, 0x15, 0x1d, 0x0f, 0x12, 0x15, 0x0f, 0x13,
 0x12, 0x10, 0x1d, 0x0b, 0x0a, 0x14, 0x07, 0x0f,
 0x12, 0x15, 0x11, 0x0f, 0x0f, 0x15, 0x0f, 0x0c,
 0x0f, 0x1b, 0x1f, 0x11, 0x0e, 0x16, 0x1d, 0x12,
 0x10, 0x0e, 0x17, 0x0d, 0x13, 0x1d, 0x11, 0x12,
 0x12, 0x1a, 0x1f, 0x12, 0x1e, 0x1e, 0x13, 0x0d,
 0x1f, 0x0a, 0x10, 0x08, 0x0d, 0x11, 0x1f, 0x1e,
 0x11, 0x0e, 0x1b, 0x09, 0x0e, 0x13, 0x0f, 0x17,
 0x15, 0x0b, 0x1e, 0x1e, 0x0d, 0x11, 0x11, 0x13,
 0x0f, 0x11, 0x15, 0x14, 0x1f, 0x14, 0x0f, 0x0a,
 0x0e, 0x10, 0x0e, 0x13, 0x14, 0x07, 0x11, 0x0c,
 0x08, 0x12, 0x1f, 0x1d, 0x1e, 0x1c, 0x0f, 0x0a,
 0x10, 0x0e, 0x0c, 0x0b, 0x14, 0x1a, 0x12, 0x10,
 0x10, 0x18, 0x1d, 0x1c, 0x1e, 0x06, 0x11, 0x1d,
 0x15, 0x0f, 0x08, 0x15, 0x0c, 0x0d, 0x17, 0x0c,
 0x10, 0x1f, 0x1f, 0x0c, 0x12, 0x1d, 0x15, 0x12,
 0x09, 0x18, 0x0f, 0x12, 0x11, 0x1c, 0x09, 0x16,
 0x1f, 0x0f, 0x1c, 0x0f, 0x05, 0x0e, 0x0e, 0x15,
 0x1f, 0x13, 0x0c, 0x12, 0x14, 0x0c, 0x0e, 0x1d,
 0x1e, 0x0d, 0x15, 0x0a, 0x16, 0x19, 0x14, 0x1e,
 0x1a, 0x11, 0x19, 0x14, 0x13, 0x13, 0x16, 0x13,
 0x12, 0x12, 0x0f, 0x12, 0x0f, 0x13, 0x06, 0x09,
 0x1e, 0x12, 0x19, 0x10, 0x0f, 0x0a, 0x1e, 0x0f,
 0x16, 0x13, 0x1e, 0x0d, 0x1f, 0x12, 0x1c, 0x16,
 0x0f, 0x1c, 0x0b, 0x1e, 0x1d, 0x13, 0x0f, 0x07,
 0x10, 0x1f, 0x17, 0x18, 0x1e, 0x1c, 0x0a, 0x0c,
 0x13, 0x1a, 0x1c, 0x1f, 0x16, 0x05, 0x0b, 0x1d,
 0x12, 0x1e, 0x19, 0x16, 0x11, 0x13, 0x13, 0x0a,
 0x18, 0x1f, 0x07, 0x07, 0x09, 0x1c, 0x1c, 0x10,
 0x11, 0x10, 0x1d, 0x0e, 0x14, 0x12, 0x0e, 0x0c,
 0x1e, 0x17, 0x1b, 0x15, 0x10, 0x1d, 0x18, 0x1a,
 0x19, 0x10, 0x11, 0x14, 0x0a, 0x0d, 0x1b, 0x17,
 0x1a, 0x12, 0x13, 0x19, 0x12, 0x0f, 0x1c, 0x11,
 0x0e, 0x12, 0x12, 0x1e, 0x11, 0x19, 0x15, 0x0e,
 0x13, 0x19, 0x0e, 0x16, 0x0a, 0x1f, 0x12, 0x0b,
 0x12, 0x13, 0x19, 0x11, 0x15, 0x11, 0x14, 0x0f,
 0x1e, 0x0d, 0x14, 0x0e, 0x1e, 0x12, 0x1f, 0x12,
 0x12, 0x12, 0x0f, 0x0e, 0x14, 0x19, 0x0c, 0x18,
 0x10, 0x0a, 0x0b, 0x12, 0x0f, 0x11, 0x16, 0x16,
 0x10, 0x12, 0x1f, 0x1b, 0x15, 0x0e, 0x1f, 0x14,
 0x1b, 0x1f, 0x0b, 0x12, 0x11, 0x13, 0x07, 0x1d,
 0x1c, 0x13, 0x0a, 0x0f, 0x1b, 0x1e, 0x0a, 0x08,
 0x06, 0x16, 0x08, 0x12, 0x0f, 0x0f, 0x17, 0x0c,
 0x11, 0x0d, 0x12, 0x10, 0x0b, 0x13, 0x1c, 0x0c,
 0x12, 0x10, 0x1a, 0x11, 0x15, 0x11, 0x14, 0x1b,
 0x15, 0x0c, 0x0c, 0x1f, 0x1f, 0x0e, 0x06, 0x1c,
 0x13, 0x08, 0x1b, 0x1f, 0x1c, 0x0c, 0x10, 0x0f,
 0x1c, 0x1b, 0x12, 0x1a, 0x0f, 0x11, 0x17, 0x1d,
 0x1e, 0x10, 0x0d, 0x08, 0x11, 0x15, 0x0f, 0x14,
 0x0c, 0x15, 0x1b, 0x17, 0x15, 0x17, 0x1f, 0x0d,
 0x16, 0x0f, 0x13, 0x08, 0x16, 0x17, 0x07, 0x15,
 0x0c, 0x13, 0x10, 0x11, 0x1a, 0x0e, 0x0a, 0x0b,
 0x1f, 0x0d, 0x0b, 0x12, 0x1a, 0x1f, 0x1c, 0x10,
 0x0c, 0x1f, 0x1d, 0x18, 0x0f, 0x17, 0x0c, 0x1d,
 0x0a, 0x11, 0x0f, 0x1e, 0x10, 0x10, 0x1a, 0x1f,
 0x1a, 0x11, 0x14, 0x1b, 0x0b, 0x17, 0x10, 0x1b,
 0x11, 0x0d, 0x17, 0x16, 0x13, 0x18, 0x0d, 0x19,
 0x19, 0x16, 0x10, 0x0b, 0x14, 0x07, 0x0f, 0x12,
 0x19, 0x0d, 0x1e, 0x19, 0x1b, 0x13, 0x0b, 0x0c,
 0x0e, 0x17, 0x1e, 0x0d, 0x0c, 0x0b, 0x13, 0x15,
 0x1d, 0x10, 0x12, 0x1f, 0x0c, 0x1f, 0x10, 0x13,
 0x1a, 0x15, 0x13, 0x19, 0x15, 0x1c, 0x17, 0x16,
 0x1f, 0x17, 0x10, 0x0b, 0x0e, 0x09, 0x14, 0x0e,
 0x1d, 0x13, 0x1e, 0x08, 0x1e, 0x15, 0x1a, 0x13,
 0x15, 0x0c, 0x10, 0x1f, 0x1d, 0x13, 0x1c, 0x1b,
 0x12, 0x14, 0x12, 0x0a, 0x0c, 0x0f, 0x1a, 0x0f,
 0x0f, 0x0b, 0x0d, 0x10, 0x0f, 0x13, 0x13, 0x0a,
 0x0f, 0x1a, 0x11, 0x15, 0x15, 0x0d, 0x16, 0x08,
 0x0e, 0x13, 0x1e, 0x0e, 0x11, 0x1f, 0x10, 0x14,
 0x11, 0x0b, 0x12, 0x11, 0x0e, 0x15, 0x1a, 0x13,
 0x0b, 0x10, 0x13, 0x10, 0x1e, 0x0d, 0x11, 0x0d,
 0x0f, 0x10, 0x0f, 0x12, 0x12, 0x0e, 0x0e, 0x13,
 0x0d, 0x18, 0x17, 0x11, 0x0f, 0x1b, 0x1f, 0x11,
 0x0d, 0x10, 0x13, 0x0c, 0x16, 0x0d, 0x13, 0x0e,
 0x0e, 0x0f, 0x09, 0x1f, 0x0f, 0x1f, 0x11, 0x1f,
 0x0e, 0x0f, 0x07, 0x0f, 0x05, 0x07, 0x1e, 0x09,
 0x07, 0x07, 0x18, 0x1b, 0x19, 0x10, 0x1b, 0x13,
 0x0f, 0x1f, 0x0f, 0x1d, 0x14, 0x0a, 0x1d, 0x0a,
 0x1e, 0x10, 0x13, 0x1e, 0x0b, 0x09, 0x1f, 0x16,
 0x1c, 0x11, 0x0f, 0x13, 0x10, 0x0d, 0x18, 0x16,
 0x10, 0x13, 0x15, 0x0d, 0x14, 0x13, 0x17, 0x18,
 0x12, 0x1e, 0x1f, 0x16, 0x1b, 0x12, 0x06, 0x10,
 0x17, 0x16, 0x1f, 0x10, 0x12, 0x1f, 0x1b, 0x0e,
 0x14, 0x11, 0x1f, 0x14, 0x16, 0x08, 0x12, 0x10,
 0x0c, 0x14, 0x19, 0x0d, 0x19, 0x16, 0x0f, 0x1e,
 0x11, 0x11, 0x06, 0x0f, 0x1e, 0x14, 0x14, 0x1e,
 0x19, 0x1b, 0x0c, 0x1f, 0x10, 0x1e, 0x1e, 0x0c,
 0x10, 0x10, 0x10, 0x0d, 0x0a, 0x0f, 0x1d, 0x1c,
 0x16, 0x16, 0x1e, 0x12, 0x11, 0x16, 0x1f, 0x19,
 0x1e, 0x12, 0x14, 0x15, 0x13, 0x14, 0x11, 0x0a,
 0x08, 0x17, 0x1b, 0x13, 0x15, 0x13, 0x10, 0x10,
 0x1b, 0x1b, 0x18, 0x19, 0x13, 0x14, 0x0d, 0x1a,
 0x11, 0x16, 0x0f, 0x13, 0x1f, 0x1a, 0x12, 0x0e,
 0x1a, 0x1e, 0x0d, 0x1b, 0x0f, 0x11, 0x11, 0x08,
 0x09, 0x1e, 0x1a, 0x1e, 0x11, 0x16, 0x10, 0x15,
 0x12, 0x16, 0x0a, 0x15, 0x10, 0x1a, 0x16, 0x0f,
 0x1f, 0x15, 0x11, 0x11, 0x0f, 0x17, 0x05, 0x17,
 0x11, 0x16, 0x1d, 0x10, 0x1d, 0x16, 0x0b, 0x0d,
 0x1a, 0x1f, 0x0e, 0x0e, 0x18, 0x13, 0x11, 0x0c,
 0x1d, 0x07, 0x0f, 0x0f, 0x1e, 0x16, 0x0f, 0x1d,
 0x05, 0x18, 0x12, 0x12, 0x11, 0x0f, 0x11, 0x0a,
 0x18, 0x12, 0x0d, 0x12, 0x14, 0x14, 0x0d, 0x13,
 0x0f, 0x1a, 0x11, 0x0f, 0x1b, 0x17, 0x11, 0x07,
 0x1e, 0x0c, 0x18, 0x09, 0x18, 0x1a, 0x12, 0x12,
 0x13, 0x16, 0x08, 0x12, 0x0a, 0x0d, 0x1d, 0x0f,
 0x11, 0x0b, 0x13, 0x0b, 0x14, 0x08, 0x06, 0x1e,
 0x10, 0x1f, 0x1e, 0x11, 0x10, 0x0f, 0x07, 0x15,
 0x1a, 0x13, 0x18, 0x1b, 0x10, 0x1d, 0x0e, 0x1f,
 0x1e, 0x18, 0x14, 0x15, 0x18, 0x10, 0x14, 0x10,
 0x1a, 0x07, 0x11, 0x19, 0x0e, 0x10, 0x1e, 0x14,
 0x16, 0x18, 0x1d, 0x13, 0x1b, 0x18, 0x18, 0x15,
 0x0d, 0x16, 0x1e, 0x1e, 0x13, 0x1a, 0x1f, 0x17,
 0x0c, 0x11, 0x13, 0x14, 0x17, 0x0e, 0x0d, 0x0b,
 0x15, 0x14, 0x1a, 0x15, 0x15, 0x16, 0x0e, 0x15,
 0x12, 0x0f, 0x13, 0x1d, 0x17, 0x0f, 0x1f, 0x0e,
 0x15, 0x1f, 0x0e, 0x14, 0x07, 0x1f, 0x0e, 0x10,
 0x18, 0x12, 0x12, 0x0a, 0x14, 0x11, 0x0b, 0x11,
 0x15, 0x0d, 0x10, 0x1a, 0x18, 0x0d, 0x10, 0x11,
 0x0a, 0x0d, 0x1f, 0x12, 0x0c, 0x09, 0x13, 0x16,
 0x15, 0x18, 0x12, 0x19, 0x06, 0x1b, 0x1e, 0x14,
 0x12, 0x0e, 0x13, 0x10, 0x1a, 0x1a, 0x10, 0x1d,
 0x0d, 0x0d, 0x12, 0x0e, 0x12, 0x17, 0x11, 0x0e,
 0x0a, 0x15, 0x12, 0x14, 0x17, 0x17, 0x12, 0x11,
 0x0f, 0x09, 0x11, 0x1d, 0x0c, 0x10, 0x12, 0x0d,
 0x0f, 0x17, 0x1c, 0x1a, 0x10, 0x1d, 0x0b, 0x1c,
 0x1a, 0x0c, 0x1d, 0x13, 0x17, 0x0b, 0x09, 0x17,
 0x17, 0x14, 0x0a, 0x12, 0x17, 0x0d, 0x18, 0x11,
 0x19, 0x0f, 0x1d, 0x13, 0x0f, 0x07, 0x12, 0x0f,
 0x1f, 0x0e, 0x10, 0x1a, 0x15, 0x14, 0x0f, 0x1b,
 0x0c, 0x17, 0x1b, 0x1b, 0x0a, 0x19, 0x0f, 0x1e,
 0x1d, 0x1e, 0x14, 0x1c, 0x12, 0x1c, 0x10, 0x1d,
 0x1d, 0x1b, 0x11, 0x16, 0x13, 0x13, 0x0c, 0x12,
 0x1f, 0x11, 0x11, 0x0f, 0x0e, 0x19, 0x0f, 0x0a,
 0x1d, 0x0b, 0x08, 0x15, 0x17, 0x0f, 0x13, 0x1c,
 0x0b, 0x16, 0x1c, 0x0e, 0x0f, 0x0e, 0x10, 0x1e,
 0x12, 0x14, 0x14, 0x11, 0x0e, 0x0d, 0x12, 0x1f,
 0x1c, 0x0a, 0x1a, 0x0f, 0x1a, 0x0f, 0x1e, 0x10,
 0x19, 0x1f, 0x1f, 0x14, 0x15, 0x1f, 0x10, 0x0e,
 0x11, 0x15, 0x1e, 0x13, 0x12, 0x0d, 0x1c, 0x0d,
 0x16, 0x1d, 0x04, 0x0e, 0x13, 0x0c, 0x13, 0x0b,
 0x05, 0x1f, 0x1e, 0x1f, 0x10, 0x13, 0x12, 0x1d,
 0x05, 0x0e, 0x12, 0x1e, 0x18, 0x0b, 0x0b, 0x0f,
 0x11, 0x11, 0x16, 0x1b, 0x0d, 0x13, 0x1d, 0x11,
 0x16, 0x0e, 0x15, 0x14, 0x0c, 0x0b, 0x11, 0x06,
 0x15, 0x14, 0x08, 0x0e, 0x1d, 0x1c, 0x1e, 0x10,
 0x12, 0x17, 0x1a, 0x07, 0x10, 0x0d, 0x06, 0x18,
 0x0c, 0x1e, 0x1f, 0x18, 0x0c, 0x14, 0x16, 0x0f,
 0x11, 0x06, 0x11, 0x16, 0x0b, 0x1a, 0x06, 0x16,
 0x1a, 0x1e, 0x1f, 0x1a, 0x15, 0x0f, 0x0b, 0x07,
 0x0f, 0x1b, 0x15, 0x0e, 0x0a, 0x17, 0x1b, 0x0e,
 0x1f, 0x15, 0x0d, 0x15, 0x1a, 0x16, 0x12, 0x18,
 0x1f, 0x0e, 0x1b, 0x1b, 0x1d, 0x0a, 0x12, 0x14,
 0x1a, 0x0c, 0x13, 0x0a, 0x16, 0x11, 0x0d, 0x0c,
 0x06, 0x0b, 0x1c, 0x08, 0x0b, 0x12, 0x1e, 0x10,
 0x03, 0x12, 0x15, 0x15, 0x16, 0x17, 0x19, 0x0a,
 0x1e, 0x1b, 0x11, 0x1e, 0x16, 0x0e, 0x1c, 0x1c,
 0x16, 0x19, 0x0f, 0x0c, 0x05, 0x10, 0x1d, 0x1c,
 0x13, 0x0d, 0x11, 0x1f, 0x0e, 0x0d, 0x1f, 0x19,
 0x0d, 0x1d, 0x18, 0x14, 0x12, 0x15, 0x1c, 0x17,
 0x09, 0x0d, 0x08, 0x0d, 0x10, 0x0c, 0x0d, 0x11,
 0x0f, 0x1b, 0x07, 0x12, 0x1c, 0x10, 0x04, 0x1e,
 0x0a, 0x09, 0x0b, 0x17, 0x1e, 0x07, 0x15, 0x1a,
 0x17, 0x11, 0x0d, 0x0c, 0x13, 0x12, 0x15, 0x1a,
 0x0b, 0x10, 0x0c, 0x11, 0x0d, 0x0a, 0x04, 0x12,
 0x12, 0x1a, 0x05, 0x0b, 0x0c, 0x08, 0x10, 0x0b,
 0x18, 0x19, 0x19, 0x12, 0x15, 0x1f, 0x11, 0x0c,
 0x0b, 0x12, 0x1d, 0x16, 0x1d, 0x10, 0x11, 0x15,
 0x11, 0x1f, 0x1f, 0x12, 0x0f, 0x0b, 0x0e, 0x10,
 0x1f, 0x0a, 0x0f, 0x04, 0x11, 0x1d, 0x0e, 0x11,
 0x0b, 0x1d, 0x16, 0x1f, 0x14, 0x1a, 0x16, 0x1e,
 0x1d, 0x0f, 0x1e, 0x0e, 0x18, 0x15, 0x12, 0x0a,
 0x0c, 0x17, 0x1f, 0x0c, 0x05, 0x0c, 0x06, 0x06,
 0x1e, 0x17, 0x1d, 0x1e, 0x0e, 0x10, 0x19, 0x11,
 0x13, 0x12, 0x11, 0x09, 0x12, 0x15, 0x09, 0x13,
 0x1f, 0x1b, 0x13, 0x06, 0x04, 0x14, 0x11, 0x10,
 0x13, 0x15, 0x0f, 0x15, 0x0f, 0x16, 0x1a, 0x11,
 0x0d, 0x12, 0x14, 0x15, 0x0d, 0x11, 0x1e, 0x0c,
 0x0e, 0x16, 0x08, 0x0e, 0x1a, 0x16, 0x06, 0x17,
 0x0c, 0x12, 0x1b, 0x13, 0x16, 0x10, 0x12, 0x08,
 0x1b, 0x11, 0x17, 0x17, 0x19, 0x1f, 0x10, 0x19,
 0x06, 0x06, 0x10, 0x1f, 0x0d, 0x08, 0x06, 0x0d,
 0x1e, 0x17, 0x08, 0x19, 0x17, 0x12, 0x05, 0x07,
 0x0f, 0x1f, 0x11, 0x14, 0x06, 0x0f, 0x0d, 0x17,
 0x18, 0x19, 0x19, 0x15, 0x0e, 0x12, 0x0a, 0x14,
 0x07, 0x0b, 0x0b, 0x14, 0x1f, 0x03, 0x13, 0x10,
 0x14, 0x1d, 0x14, 0x0f, 0x15, 0x14, 0x09, 0x1c,
 0x14, 0x1d, 0x1e, 0x1c, 0x12, 0x18, 0x13, 0x16,
 0x0c, 0x19, 0x1e, 0x17, 0x0e, 0x17, 0x05, 0x16,
 0x19, 0x18, 0x15, 0x18, 0x15, 0x10, 0x16, 0x0d,
 0x0f, 0x1c, 0x11, 0x10, 0x09, 0x0f, 0x1e, 0x14,
 0x0c, 0x17, 0x12, 0x11, 0x19, 0x16, 0x0c, 0x1b,
 0x1c, 0x1a, 0x13, 0x09, 0x12, 0x0b, 0x0e, 0x03,
 0x10, 0x17, 0x13, 0x0a, 0x12, 0x12, 0x1d, 0x0e,
 0x1d, 0x17, 0x0f, 0x13, 0x15, 0x13, 0x13, 0x0d,
 0x13, 0x0e, 0x1a, 0x1b, 0x14, 0x10, 0x0c, 0x0e,
 0x19, 0x13, 0x12, 0x0d, 0x09, 0x0e, 0x09, 0x12,
 0x1c, 0x17, 0x0d, 0x12, 0x0f, 0x0f, 0x12, 0x0e,
 0x1c, 0x14, 0x11, 0x14, 0x1a, 0x0d, 0x14, 0x11,
 0x07, 0x15, 0x09, 0x0e, 0x0c, 0x12, 0x11, 0x13,
 0x1e, 0x05, 0x13, 0x13, 0x11, 0x0c, 0x08, 0x18,
 0x18, 0x17, 0x0c, 0x17, 0x0c, 0x14, 0x1f, 0x11,
 0x15, 0x12, 0x0d, 0x09, 0x0e, 0x1c, 0x09, 0x04,
 0x16, 0x0d, 0x12, 0x19, 0x1e, 0x14, 0x0f, 0x03,
 0x19, 0x05, 0x0a, 0x16, 0x1e, 0x10, 0x1d, 0x0e,
 0x08, 0x16, 0x1e, 0x05, 0x06, 0x15, 0x13, 0x1f,
 0x0c, 0x0f, 0x0b, 0x0e, 0x14, 0x11, 0x13, 0x17,
 0x0e, 0x12, 0x05, 0x09, 0x16, 0x17, 0x15, 0x14,
 0x0c, 0x0a, 0x10, 0x0d, 0x12, 0x11, 0x07, 0x16,
 0x0d, 0x16, 0x1c, 0x0b, 0x08, 0x17, 0x12, 0x0e,
 0x0e, 0x16, 0x1a, 0x05, 0x11, 0x0d, 0x18, 0x0f,
 0x1f, 0x12, 0x16, 0x12, 0x15, 0x1a, 0x14, 0x11
};

// X min array (128 x 32)
static const int x_min[] =
{
 0x04, 0x00, 0x02, 0x07, 0x08, 0x02, 0x09, 0x0a,
 0x02, 0x05, 0x01, 0x0c, 0x01, 0x0f, 0x06, 0x0a,
 0x04, 0x0c, 0x06, 0x06, 0x0e, 0x02, 0x07, 0x03,
 0x02, 0x00, 0x06, 0x06, 0x11, 0x0b, 0x0d, 0x01,
 0x0b, 0x06, 0x04, 0x06, 0x0c, 0x06, 0x09, 0x0c,
 0x05, 0x03, 0x0c, 0x02, 0x07, 0x06, 0x0b, 0x0d,
 0x09, 0x06, 0x00, 0x04, 0x05, 0x11, 0x0d, 0x01,
 0x02, 0x0f, 0x12, 0x0c, 0x08, 0x02, 0x0e, 0x04,
 0x09, 0x08, 0x04, 0x0e, 0x00, 0x06, 0x08, 0x02,
 0x01, 0x02, 0x08, 0x0e, 0x02, 0x00, 0x11, 0x0a,
 0x08, 0x03, 0x0c, 0x03, 0x0d, 0x0b, 0x07, 0x10,
 0x0d, 0x0a, 0x05, 0x10, 0x0f, 0x0d, 0x08, 0x05,
 0x07, 0x04, 0x04, 0x01, 0x0c, 0x0a, 0x0b, 0x08,
 0x09, 0x05, 0x0e, 0x02, 0x01, 0x03, 0x0c, 0x0e,
 0x02, 0x10, 0x09, 0x0c, 0x03, 0x05, 0x0f, 0x0a,
 0x07, 0x0a, 0x04, 0x07, 0x05, 0x0e, 0x0f, 0x09,
 0x08, 0x0b, 0x03, 0x04, 0x0f, 0x0d, 0x01, 0x0d,
 0x02, 0x0a, 0x01, 0x03, 0x07, 0x03, 0x01, 0x08,
 0x07, 0x10, 0x07, 0x06, 0x0d, 0x08, 0x06, 0x0e,
 0x0b, 0x0a, 0x03, 0x06, 0x0c, 0x03, 0x00, 0x02,
 0x02, 0x03, 0x01, 0x01, 0x07, 0x00, 0x0d, 0x07,
 0x07, 0x03, 0x08, 0x04, 0x01, 0x03, 0x0c, 0x0d,
 0x0f, 0x12, 0x06, 0x08, 0x10, 0x0b, 0x00, 0x04,
 0x12, 0x04, 0x11, 0x09, 0x06, 0x03, 0x11, 0x0b,
 0x06, 0x0d, 0x05, 0x05, 0x0d, 0x0f, 0x0f, 0x05,
 0x01, 0x02, 0x08, 0x05, 0x08, 0x06, 0x0a, 0x13,
 0x0a, 0x05, 0x0d, 0x0b, 0x03, 0x09, 0x03, 0x0e,
 0x00, 0x08, 0x04, 0x01, 0x00, 0x10, 0x10, 0x08,
 0x0a, 0x01, 0x10, 0x08, 0x04, 0x08, 0x04, 0x02,
 0x03, 0x0f, 0x11, 0x06, 0x0c, 0x11, 0x02, 0x0d,
 0x0e, 0x10, 0x0b, 0x06, 0x02, 0x03, 0x08, 0x05,
 0x04, 0x0c, 0x0a, 0x05, 0x00, 0x14, 0x03, 0x09,
 0x07, 0x0c, 0x00, 0x07, 0x01, 0x05, 0x0c, 0x08,
 0x0d, 0x07, 0x08, 0x0b, 0x01, 0x12, 0x10, 0x0c,
 0x01, 0x03, 0x04, 0x01, 0x0d, 0x0d, 0x12, 0x0d,
 0x02, 0x09, 0x05, 0x0a, 0x11, 0x08, 0x05, 0x13,
 0x01, 0x06, 0x0b, 0x0c, 0x09, 0x02, 0x00, 0x09,
 0x07, 0x08, 0x00, 0x02, 0x01, 0x02, 0x09, 0x00,
 0x05, 0x02, 0x00, 0x15, 0x00, 0x0d, 0x0f, 0x01,
 0x03, 0x0c, 0x0c, 0x02, 0x11, 0x03, 0x05, 0x14,
 0x01, 0x01, 0x01, 0x09, 0x04, 0x05, 0x04, 0x06,
 0x02, 0x0d, 0x08, 0x02, 0x05, 0x01, 0x09, 0x00,
 0x0b, 0x0b, 0x13, 0x0f, 0x02, 0x00, 0x08, 0x10,
 0x08, 0x0a, 0x10, 0x08, 0x05, 0x00, 0x04, 0x0a,
 0x0a, 0x10, 0x0d, 0x01, 0x04, 0x09, 0x0f, 0x02,
 0x07, 0x11, 0x06, 0x0f, 0x02, 0x06, 0x08, 0x00,
 0x08, 0x0d, 0x04, 0x0c, 0x07, 0x0a, 0x05, 0x0e,
 0x12, 0x0c, 0x00, 0x0f, 0x02, 0x10, 0x0d, 0x02,
 0x11, 0x08, 0x0a, 0x01, 0x09, 0x01, 0x07, 0x0d,
 0x0a, 0x02, 0x08, 0x0a, 0x0c, 0x0a, 0x0e, 0x00,
 0x00, 0x02, 0x09, 0x02, 0x00, 0x0b, 0x13, 0x0d,
 0x04, 0x0e, 0x0e, 0x04, 0x0b, 0x11, 0x0c, 0x02,
 0x0f, 0x0b, 0x08, 0x0e, 0x08, 0x0a, 0x08, 0x11,
 0x0d, 0x00, 0x02, 0x09, 0x0b, 0x02, 0x10, 0x09,
 0x0c, 0x03, 0x00, 0x0c, 0x10, 0x0e, 0x10, 0x07,
 0x0f, 0x08, 0x0c, 0x0a, 0x06, 0x0c, 0x06, 0x06,
 0x0d, 0x0d, 0x05, 0x09, 0x06, 0x0c, 0x03, 0x05,
 0x0e, 0x03, 0x04, 0x01, 0x11, 0x00, 0x0d, 0x05,
 0x03, 0x09, 0x0d, 0x0c, 0x0d, 0x03, 0x08, 0x05,
 0x0b, 0x0c, 0x0a, 0x13, 0x0f, 0x0a, 0x00, 0x01,
 0x0a, 0x0b, 0x0a, 0x01, 0x0d, 0x03, 0x00, 0x0c,
 0x01, 0x0b, 0x01, 0x0b, 0x03, 0x08, 0x0d, 0x02,
 0x05, 0x07, 0x0d, 0x0e, 0x01, 0x05, 0x0a, 0x11,
 0x0a, 0x11, 0x01, 0x07, 0x0e, 0x08, 0x05, 0x0e,
 0x05, 0x09, 0x05, 0x11, 0x0c, 0x0b, 0x08, 0x08,
 0x09, 0x08, 0x03, 0x03, 0x10, 0x0f, 0x11, 0x0b,
 0x0f, 0x09, 0x0f, 0x03, 0x02, 0x0e, 0x03, 0x03,
 0x14, 0x0e, 0x0e, 0x08, 0x0a, 0x10, 0x04, 0x07,
 0x04, 0x0d, 0x14, 0x0d, 0x0c, 0x02, 0x03, 0x01,
 0x00, 0x0e, 0x02, 0x10, 0x05, 0x02, 0x0b, 0x0e,
 0x01, 0x12, 0x0d, 0x0f, 0x02, 0x0f, 0x0d, 0x11,
 0x0a, 0x09, 0x02, 0x0f, 0x05, 0x06, 0x05, 0x0d,
 0x0b, 0x03, 0x0e, 0x11, 0x02, 0x01, 0x0b, 0x05,
 0x00, 0x05, 0x03, 0x0b, 0x04, 0x0c, 0x0f, 0x10,
 0x11, 0x05, 0x10, 0x06, 0x03, 0x03, 0x00, 0x05,
 0x04, 0x0f, 0x09, 0x00, 0x0d, 0x06, 0x13, 0x07,
 0x03, 0x08, 0x05, 0x03, 0x05, 0x09, 0x04, 0x08,
 0x0c, 0x12, 0x0c, 0x0d, 0x0d, 0x01, 0x0d, 0x02,
 0x08, 0x0e, 0x12, 0x07, 0x16, 0x06, 0x01, 0x07,
 0x0d, 0x09, 0x0f, 0x06, 0x09, 0x00, 0x01, 0x0c,
 0x0e, 0x08, 0x04, 0x01, 0x06, 0x0b, 0x0d, 0x0c,
 0x06, 0x02, 0x0d, 0x13, 0x05, 0x10, 0x09, 0x05,
 0x00, 0x10, 0x10, 0x0b, 0x10, 0x0d, 0x10, 0x00,
 0x06, 0x09, 0x01, 0x00, 0x04, 0x04, 0x08, 0x08,
 0x02, 0x0c, 0x00, 0x0b, 0x0d, 0x07, 0x00, 0x02,
 0x03, 0x0d, 0x00, 0x05, 0x14, 0x05, 0x02, 0x01,
 0x0e, 0x02, 0x0b, 0x0a, 0x05, 0x0c, 0x0b, 0x13,
 0x0c, 0x0f, 0x0c, 0x08, 0x0c, 0x10, 0x04, 0x11,
 0x10, 0x0c, 0x04, 0x0d, 0x01, 0x08, 0x0d, 0x01,
 0x0e, 0x0e, 0x0e, 0x0b, 0x11, 0x13, 0x0f, 0x0c,
 0x00, 0x05, 0x0c, 0x04, 0x02, 0x0e, 0x01, 0x09,
 0x01, 0x09, 0x11, 0x06, 0x11, 0x10, 0x09, 0x04,
 0x0c, 0x0b, 0x05, 0x0a, 0x06, 0x0a, 0x03, 0x03,
 0x0a, 0x07, 0x07, 0x0b, 0x10, 0x01, 0x09, 0x0e,
 0x10, 0x0b, 0x08, 0x06, 0x0c, 0x0b, 0x03, 0x07,
 0x0f, 0x11, 0x0a, 0x0f, 0x05, 0x03, 0x0e, 0x0e,
 0x05, 0x03, 0x10, 0x03, 0x01, 0x05, 0x06, 0x01,
 0x0d, 0x00, 0x0f, 0x11, 0x09, 0x10, 0x0c, 0x03,
 0x09, 0x0a, 0x0d, 0x07, 0x05, 0x08, 0x14, 0x0f,
 0x10, 0x09, 0x10, 0x05, 0x05, 0x03, 0x05, 0x09,
 0x0a, 0x0c, 0x08, 0x0e, 0x05, 0x09, 0x04, 0x09,
 0x00, 0x11, 0x0e, 0x06, 0x01, 0x05, 0x01, 0x0d,
 0x01, 0x0f, 0x01, 0x0b, 0x07, 0x01, 0x01, 0x0a,
 0x14, 0x0c, 0x02, 0x07, 0x0c, 0x07, 0x13, 0x00,
 0x0c, 0x0a, 0x02, 0x0d, 0x00, 0x12, 0x01, 0x0e,
 0x05, 0x0c, 0x12, 0x02, 0x04, 0x10, 0x03, 0x10,
 0x00, 0x0a, 0x0a, 0x0f, 0x0f, 0x03, 0x0d, 0x04,
 0x0d, 0x0f, 0x0d, 0x04, 0x0d, 0x03, 0x10, 0x0c,
 0x06, 0x06, 0x03, 0x0a, 0x10, 0x0f, 0x04, 0x10,
 0x0f, 0x04, 0x0f, 0x0f, 0x0f, 0x0e, 0x0d, 0x0d,
 0x01, 0x0a, 0x10, 0x03, 0x02, 0x0f, 0x13, 0x0c,
 0x05, 0x05, 0x05, 0x01, 0x13, 0x10, 0x0d, 0x11,
 0x02, 0x09, 0x01, 0x02, 0x03, 0x01, 0x0f, 0x08,
 0x10, 0x08, 0x11, 0x02, 0x01, 0x07, 0x0d, 0x00,
 0x10, 0x07, 0x0c, 0x0a, 0x08, 0x0f, 0x0b, 0x0f,
 0x15, 0x12, 0x07, 0x02, 0x0b, 0x02, 0x06, 0x06,
 0x00, 0x05, 0x0d, 0x10, 0x08, 0x04, 0x0a, 0x07,
 0x0e, 0x02, 0x02, 0x06, 0x12, 0x00, 0x05, 0x0c,
 0x07, 0x0e, 0x0f, 0x0b, 0x03, 0x0d, 0x00, 0x0a,
 0x01, 0x06, 0x10, 0x14, 0x14, 0x07, 0x14, 0x09,
 0x0d, 0x0f, 0x0a, 0x12, 0x0d, 0x0d, 0x0a, 0x09,
 0x0e, 0x0d, 0x0d, 0x00, 0x04, 0x0f, 0x09, 0x01,
 0x0d, 0x01, 0x0f, 0x10, 0x05, 0x0b, 0x08, 0x00,
 0x09, 0x10, 0x08, 0x11, 0x0e, 0x0c, 0x0e, 0x01,
 0x03, 0x0c, 0x01, 0x03, 0x07, 0x0f, 0x0f, 0x13,
 0x0e, 0x06, 0x0f, 0x00, 0x09, 0x0c, 0x04, 0x02,
 0x04, 0x0b, 0x13, 0x04, 0x07, 0x0d, 0x08, 0x0c,
 0x0a, 0x0e, 0x10, 0x0b, 0x00, 0x0c, 0x03, 0x0b,
 0x07, 0x08, 0x08, 0x00, 0x03, 0x05, 0x0e, 0x0c,
 0x13, 0x12, 0x10, 0x06, 0x00, 0x05, 0x13, 0x0d,
 0x0f, 0x0f, 0x0f, 0x01, 0x0a, 0x10, 0x03, 0x08,
 0x03, 0x0c, 0x07, 0x11, 0x13, 0x0f, 0x0e, 0x0c,
 0x01, 0x03, 0x01, 0x0d, 0x11, 0x11, 0x01, 0x0c,
 0x0e, 0x09, 0x0d, 0x02, 0x11, 0x08, 0x10, 0x0a,
 0x10, 0x10, 0x0c, 0x04, 0x04, 0x11, 0x07, 0x07,
 0x03, 0x10, 0x0f, 0x00, 0x09, 0x0b, 0x14, 0x15,
 0x09, 0x0f, 0x0f, 0x0b, 0x0a, 0x0c, 0x0a, 0x0e,
 0x0d, 0x0d, 0x07, 0x02, 0x03, 0x0d, 0x0d, 0x13,
 0x08, 0x11, 0x09, 0x00, 0x0e, 0x09, 0x0f, 0x0e,
 0x0e, 0x00, 0x00, 0x15, 0x15, 0x0d, 0x0e, 0x10,
 0x0a, 0x01, 0x0e, 0x0a, 0x10, 0x0d, 0x00, 0x01,
 0x02, 0x00, 0x0d, 0x00, 0x0c, 0x05, 0x0c, 0x0e,
 0x12, 0x10, 0x0a, 0x00, 0x09, 0x06, 0x00, 0x08,
 0x00, 0x0a, 0x0e, 0x0a, 0x03, 0x09, 0x15, 0x0e,
 0x10, 0x0e, 0x0b, 0x0b, 0x0e, 0x0d, 0x10, 0x01,
 0x10, 0x02, 0x02, 0x09, 0x10, 0x0c, 0x0f, 0x0a,
 0x00, 0x0f, 0x0c, 0x03, 0x11, 0x11, 0x11, 0x0a,
 0x0e, 0x10, 0x07, 0x0a, 0x05, 0x10, 0x0d, 0x0b,
 0x0d, 0x0e, 0x0d, 0x09, 0x04, 0x0f, 0x13, 0x10,
 0x11, 0x0b, 0x0f, 0x13, 0x09, 0x0f, 0x04, 0x0b,
 0x07, 0x0a, 0x05, 0x10, 0x08, 0x05, 0x0f, 0x07,
 0x0a, 0x0e, 0x0f, 0x08, 0x11, 0x09, 0x03, 0x03,
 0x13, 0x04, 0x08, 0x0e, 0x10, 0x0f, 0x0d, 0x0f,
 0x11, 0x01, 0x0d, 0x07, 0x11, 0x08, 0x0e, 0x0b,
 0x11, 0x0b, 0x11, 0x04, 0x08, 0x0c, 0x11, 0x08,
 0x0d, 0x0e, 0x08, 0x09, 0x0f, 0x07, 0x10, 0x08,
 0x09, 0x0b, 0x0c, 0x08, 0x0d, 0x0c, 0x15, 0x0a,
 0x0d, 0x11, 0x0e, 0x0e, 0x07, 0x11, 0x01, 0x02,
 0x08, 0x02, 0x10, 0x0d, 0x11, 0x0f, 0x13, 0x0f,
 0x09, 0x06, 0x08, 0x0c, 0x0e, 0x00, 0x10, 0x04,
 0x10, 0x0d, 0x05, 0x0b, 0x0e, 0x0e, 0x0e, 0x04,
 0x0e, 0x07, 0x0c, 0x10, 0x03, 0x0a, 0x12, 0x0d,
 0x11, 0x12, 0x0c, 0x0a, 0x0f, 0x05, 0x08, 0x07,
 0x0e, 0x0c, 0x0c, 0x13, 0x0e, 0x0e, 0x13, 0x06,
 0x03, 0x06, 0x0f, 0x05, 0x0a, 0x0d, 0x10, 0x0d,
 0x07, 0x05, 0x03, 0x10, 0x00, 0x01, 0x11, 0x11,
 0x0c, 0x08, 0x0f, 0x0b, 0x0d, 0x03, 0x03, 0x09,
 0x05, 0x10, 0x10, 0x04, 0x0b, 0x0f, 0x0d, 0x15,
 0x0e, 0x0f, 0x06, 0x0c, 0x0f, 0x09, 0x10, 0x0f,
 0x03, 0x0f, 0x05, 0x13, 0x07, 0x07, 0x0f, 0x08,
 0x0e, 0x11, 0x0f, 0x15, 0x0d, 0x01, 0x0e, 0x0f,
 0x09, 0x11, 0x0d, 0x09, 0x0e, 0x06, 0x0d, 0x0f,
 0x01, 0x12, 0x05, 0x04, 0x02, 0x11, 0x07, 0x11,
 0x10, 0x0f, 0x10, 0x0d, 0x09, 0x10, 0x01, 0x03,
 0x13, 0x11, 0x06, 0x01, 0x0b, 0x0c, 0x06, 0x0c,
 0x07, 0x0b, 0x0b, 0x0f, 0x02, 0x0e, 0x11, 0x10,
 0x0d, 0x0c, 0x02, 0x0f, 0x10, 0x10, 0x12, 0x0c,
 0x09, 0x08, 0x08, 0x06, 0x11, 0x0c, 0x10, 0x09,
 0x04, 0x00, 0x01, 0x09, 0x0d, 0x05, 0x10, 0x0f,
 0x10, 0x11, 0x07, 0x0f, 0x07, 0x16, 0x03, 0x06,
 0x00, 0x01, 0x0c, 0x00, 0x12, 0x10, 0x07, 0x11,
 0x11, 0x12, 0x0a, 0x0c, 0x0c, 0x0a, 0x0c, 0x12,
 0x03, 0x10, 0x09, 0x03, 0x03, 0x0f, 0x12, 0x06,
 0x0b, 0x0d, 0x13, 0x03, 0x06, 0x02, 0x03, 0x0e,
 0x0b, 0x11, 0x04, 0x05, 0x02, 0x06, 0x0c, 0x0f,
 0x0f, 0x12, 0x0c, 0x00, 0x0e, 0x04, 0x03, 0x02,
 0x0c, 0x05, 0x07, 0x08, 0x0c, 0x0e, 0x08, 0x08,
 0x0b, 0x04, 0x03, 0x12, 0x0d, 0x12, 0x0f, 0x04,
 0x02, 0x0c, 0x01, 0x01, 0x0c, 0x06, 0x10, 0x01,
 0x16, 0x0a, 0x00, 0x12, 0x04, 0x11, 0x10, 0x0d,
 0x07, 0x06, 0x10, 0x08, 0x0d, 0x01, 0x0d, 0x0d,
 0x0f, 0x0e, 0x08, 0x12, 0x0a, 0x10, 0x08, 0x0d,
 0x06, 0x11, 0x07, 0x0d, 0x02, 0x06, 0x0b, 0x0e,
 0x10, 0x0c, 0x00, 0x0d, 0x0f, 0x00, 0x01, 0x0a,
 0x10, 0x0d, 0x0f, 0x13, 0x0d, 0x01, 0x05, 0x09,
 0x0b, 0x0d, 0x13, 0x11, 0x04, 0x12, 0x05, 0x02,
 0x16, 0x01, 0x12, 0x0c, 0x15, 0x11, 0x0e, 0x04,
 0x0f, 0x10, 0x0b, 0x00, 0x0d, 0x01, 0x0d, 0x0d,
 0x13, 0x0d, 0x03, 0x12, 0x00, 0x0f, 0x0f, 0x13,
 0x13, 0x09, 0x11, 0x06, 0x03, 0x0c, 0x11, 0x13,
 0x0b, 0x0d, 0x0a, 0x06, 0x0c, 0x15, 0x09, 0x13,
 0x09, 0x0a, 0x01, 0x0b, 0x0d, 0x05, 0x09, 0x00,
 0x0f, 0x07, 0x09, 0x06, 0x0a, 0x0b, 0x0d, 0x08,
 0x12, 0x11, 0x01, 0x12, 0x0c, 0x0f, 0x0d, 0x0c,
 0x0c, 0x13, 0x12, 0x05, 0x0f, 0x0f, 0x07, 0x0c,
 0x02, 0x0d, 0x13, 0x04, 0x07, 0x03, 0x00, 0x11,
 0x10, 0x13, 0x08, 0x07, 0x0c, 0x04, 0x13, 0x10,
 0x05, 0x0f, 0x13, 0x0c, 0x09, 0x10, 0x07, 0x11,
 0x10, 0x13, 0x0c, 0x0f, 0x10, 0x10, 0x06, 0x0b,
 0x07, 0x00, 0x07, 0x10, 0x0c, 0x12, 0x00, 0x12,
 0x03, 0x09, 0x07, 0x0a, 0x10, 0x09, 0x0f, 0x12,
 0x03, 0x02, 0x0e, 0x11, 0x0b, 0x09, 0x0b, 0x0e,
 0x0b, 0x08, 0x0c, 0x0c, 0x14, 0x11, 0x0d, 0x07,
 0x0a, 0x0a, 0x0c, 0x09, 0x0c, 0x03, 0x0d, 0x0e,
 0x0f, 0x0d, 0x0d, 0x0d, 0x14, 0x11, 0x0e, 0x0e,
 0x13, 0x08, 0x07, 0x0d, 0x0f, 0x10, 0x10, 0x00,
 0x15, 0x02, 0x0a, 0x0e, 0x11, 0x15, 0x12, 0x09,
 0x0d, 0x08, 0x10, 0x0c, 0x06, 0x0d, 0x14, 0x01,
 0x00, 0x12, 0x10, 0x0a, 0x01, 0x17, 0x0f, 0x03,
 0x08, 0x0a, 0x12, 0x0b, 0x09, 0x01, 0x0a, 0x01,
 0x04, 0x0c, 0x08, 0x12, 0x0c, 0x03, 0x0e, 0x04,
 0x05, 0x0d, 0x08, 0x12, 0x0d, 0x12, 0x0f, 0x03,
 0x09, 0x0c, 0x0e, 0x0a, 0x0f, 0x06, 0x18, 0x10,
 0x00, 0x10, 0x0a, 0x04, 0x0a, 0x0f, 0x0b, 0x01,
 0x10, 0x11, 0x08, 0x00, 0x03, 0x00, 0x0c, 0x10,
 0x0f, 0x0f, 0x11, 0x11, 0x00, 0x16, 0x06, 0x0d,
 0x07, 0x12, 0x04, 0x0b, 0x14, 0x0a, 0x04, 0x05,
 0x13, 0x13, 0x10, 0x03, 0x05, 0x10, 0x09, 0x06,
 0x12, 0x0e, 0x01, 0x13, 0x0d, 0x09, 0x0d, 0x12,
 0x02, 0x04, 0x0b, 0x07, 0x0e, 0x0f, 0x0f, 0x10,
 0x12, 0x04, 0x0b, 0x0c, 0x0c, 0x12, 0x0e, 0x0e,
 0x0b, 0x0e, 0x0b, 0x02, 0x13, 0x0e, 0x12, 0x09,
 0x0b, 0x13, 0x03, 0x0e, 0x04, 0x10, 0x0a, 0x08,
 0x0c, 0x02, 0x06, 0x0e, 0x0a, 0x13, 0x09, 0x04,
 0x08, 0x04, 0x0d, 0x0b, 0x0d, 0x0d, 0x00, 0x0f,
 0x11, 0x08, 0x0a, 0x03, 0x12, 0x11, 0x0d, 0x04,
 0x03, 0x0e, 0x0c, 0x0a, 0x0f, 0x0f, 0x0f, 0x0d,
 0x09, 0x0a, 0x0f, 0x11, 0x0e, 0x0a, 0x13, 0x0a,
 0x11, 0x19, 0x0c, 0x11, 0x03, 0x0b, 0x10, 0x05,
 0x10, 0x0a, 0x12, 0x0a, 0x01, 0x09, 0x06, 0x03,
 0x03, 0x0e, 0x11, 0x0a, 0x0e, 0x09, 0x0e, 0x10,
 0x00, 0x11, 0x0a, 0x07, 0x10, 0x00, 0x0e, 0x08,
 0x08, 0x04, 0x13, 0x13, 0x0f, 0x0d, 0x0d, 0x0b,
 0x10, 0x0a, 0x0c, 0x0c, 0x14, 0x06, 0x11, 0x0f,
 0x10, 0x0f, 0x05, 0x0b, 0x0a, 0x0f, 0x0a, 0x10,
 0x0f, 0x04, 0x09, 0x0f, 0x0f, 0x0f, 0x10, 0x10,
 0x08, 0x0e, 0x03, 0x10, 0x01, 0x0a, 0x0d, 0x0f,
 0x0a, 0x10, 0x03, 0x0c, 0x00, 0x08, 0x13, 0x15,
 0x03, 0x0d, 0x10, 0x0b, 0x0f, 0x04, 0x0a, 0x11,
 0x06, 0x10, 0x0e, 0x0e, 0x07, 0x0b, 0x16, 0x0d,
 0x09, 0x0d, 0x0a, 0x0c, 0x10, 0x0a, 0x0c, 0x16,
 0x0e, 0x0e, 0x12, 0x10, 0x0b, 0x0f, 0x0c, 0x13,
 0x02, 0x06, 0x0c, 0x04, 0x0a, 0x03, 0x05, 0x0b,
 0x11, 0x08, 0x10, 0x05, 0x11, 0x09, 0x0e, 0x15,
 0x0f, 0x0e, 0x09, 0x15, 0x03, 0x10, 0x07, 0x12,
 0x10, 0x09, 0x0f, 0x10, 0x01, 0x0b, 0x09, 0x08,
 0x11, 0x04, 0x0b, 0x10, 0x0f, 0x01, 0x0c, 0x0e,
 0x0b, 0x0e, 0x0e, 0x0d, 0x0a, 0x03, 0x14, 0x07,
 0x12, 0x05, 0x03, 0x09, 0x10, 0x0a, 0x14, 0x0b,
 0x00, 0x18, 0x01, 0x17, 0x10, 0x05, 0x10, 0x03,
 0x12, 0x0e, 0x0f, 0x0d, 0x0f, 0x10, 0x09, 0x12,
 0x16, 0x0c, 0x0a, 0x12, 0x0e, 0x0e, 0x0b, 0x0d,
 0x0d, 0x01, 0x10, 0x0c, 0x0e, 0x09, 0x08, 0x11,
 0x12, 0x08, 0x05, 0x05, 0x03, 0x0a, 0x13, 0x09,
 0x11, 0x10, 0x13, 0x0e, 0x06, 0x0f, 0x11, 0x0a,
 0x09, 0x0e, 0x05, 0x07, 0x0d, 0x10, 0x11, 0x10,
 0x16, 0x0a, 0x13, 0x0a, 0x0d, 0x07, 0x0e, 0x01,
 0x12, 0x14, 0x07, 0x19, 0x0c, 0x08, 0x17, 0x12,
 0x0e, 0x05, 0x0d, 0x05, 0x10, 0x09, 0x0b, 0x0b,
 0x0d, 0x0a, 0x08, 0x0c, 0x07, 0x11, 0x0f, 0x13,
 0x11, 0x0e, 0x08, 0x17, 0x08, 0x0c, 0x0c, 0x11,
 0x13, 0x13, 0x11, 0x10, 0x10, 0x0d, 0x01, 0x0d,
 0x0f, 0x02, 0x12, 0x0a, 0x0d, 0x0d, 0x04, 0x02,
 0x10, 0x0f, 0x16, 0x12, 0x01, 0x0f, 0x11, 0x15,
 0x09, 0x00, 0x0e, 0x0f, 0x0e, 0x00, 0x0b, 0x0e,
 0x0f, 0x0d, 0x0f, 0x0e, 0x06, 0x0b, 0x10, 0x09,
 0x08, 0x0c, 0x02, 0x0e, 0x0a, 0x04, 0x10, 0x14,
 0x0d, 0x06, 0x01, 0x0e, 0x0c, 0x07, 0x06, 0x09,
 0x0c, 0x12, 0x02, 0x09, 0x01, 0x12, 0x16, 0x0d,
 0x0f, 0x0e, 0x0b, 0x0c, 0x0e, 0x01, 0x0c, 0x0f,
 0x0b, 0x0f, 0x11, 0x0d, 0x08, 0x09, 0x0d, 0x10,
 0x0b, 0x07, 0x0b, 0x11, 0x05, 0x0f, 0x0d, 0x12,
 0x01, 0x11, 0x0f, 0x00, 0x10, 0x12, 0x0d, 0x05,
 0x00, 0x0a, 0x0e, 0x00, 0x08, 0x02, 0x11, 0x10,
 0x11, 0x0a, 0x10, 0x04, 0x06, 0x0f, 0x0f, 0x0d,
 0x06, 0x0d, 0x0f, 0x10, 0x16, 0x0e, 0x10, 0x15,
 0x07, 0x0d, 0x10, 0x06, 0x13, 0x02, 0x09, 0x13,
 0x0f, 0x0b, 0x0e, 0x14, 0x00, 0x05, 0x0e, 0x10,
 0x0f, 0x13, 0x10, 0x05, 0x11, 0x0d, 0x04, 0x0e,
 0x01, 0x15, 0x02, 0x11, 0x0e, 0x10, 0x0e, 0x0e,
 0x19, 0x0f, 0x09, 0x0c, 0x11, 0x0d, 0x13, 0x10,
 0x06, 0x0f, 0x0f, 0x0e, 0x14, 0x12, 0x0b, 0x10,
 0x07, 0x0b, 0x09, 0x0c, 0x10, 0x0d, 0x01, 0x0f,
 0x01, 0x0f, 0x10, 0x08, 0x10, 0x11, 0x0c, 0x09,
 0x0d, 0x07, 0x11, 0x04, 0x10, 0x10, 0x0d, 0x10,
 0x10, 0x13, 0x0e, 0x06, 0x0f, 0x0b, 0x01, 0x11,
 0x0e, 0x0e, 0x0b, 0x0d, 0x0c, 0x0d, 0x00, 0x05,
 0x0f, 0x10, 0x0c, 0x06, 0x09, 0x0f, 0x10, 0x06,
 0x12, 0x0e, 0x15, 0x15, 0x0f, 0x08, 0x05, 0x09,
 0x0d, 0x10, 0x03, 0x0b, 0x13, 0x05, 0x0d, 0x12,
 0x09, 0x0c, 0x09, 0x09, 0x0f, 0x07, 0x0c, 0x05,
 0x08, 0x0b, 0x07, 0x01, 0x0f, 0x12, 0x0d, 0x04,
 0x0c, 0x12, 0x08, 0x07, 0x08, 0x14, 0x00, 0x06,
 0x10, 0x09, 0x04, 0x12, 0x0d, 0x0b, 0x01, 0x10,
 0x0f, 0x05, 0x05, 0x10, 0x0e, 0x10, 0x0b, 0x0d,
 0x10, 0x01, 0x19, 0x01, 0x18, 0x0b, 0x12, 0x0b,
 0x0e, 0x01, 0x05, 0x1a, 0x03, 0x0b, 0x16, 0x0d,
 0x10, 0x03, 0x03, 0x0f, 0x0d, 0x12, 0x05, 0x06,
 0x02, 0x15, 0x0c, 0x08, 0x06, 0x0d, 0x16, 0x17,
 0x0e, 0x04, 0x0b, 0x01, 0x07, 0x0d, 0x0f, 0x02,
 0x0c, 0x0e, 0x06, 0x0e, 0x0d, 0x0b, 0x0d, 0x09,
 0x0a, 0x05, 0x0a, 0x14, 0x0c, 0x0c, 0x0b, 0x10,
 0x03, 0x11, 0x02, 0x10, 0x0d, 0x0d, 0x0f, 0x01,
 0x10, 0x15, 0x00, 0x07, 0x08, 0x13, 0x06, 0x0b,
 0x12, 0x0e, 0x11, 0x09, 0x06, 0x09, 0x10, 0x18,
 0x0b, 0x0e, 0x01, 0x0f, 0x0f, 0x09, 0x00, 0x0c,
 0x09, 0x01, 0x08, 0x0c, 0x0d, 0x01, 0x10, 0x14,
 0x05, 0x10, 0x10, 0x0f, 0x09, 0x08, 0x05, 0x12,
 0x13, 0x10, 0x12, 0x0b, 0x04, 0x18, 0x0e, 0x0e,
 0x03, 0x10, 0x0b, 0x17, 0x12, 0x12, 0x12, 0x02,
 0x01, 0x0e, 0x0d, 0x0c, 0x0f, 0x10, 0x0f, 0x0f,
 0x01, 0x17, 0x02, 0x11, 0x0e, 0x0f, 0x0d, 0x0d,
 0x10, 0x12, 0x07, 0x0e, 0x0d, 0x0d, 0x0b, 0x0f,
 0x05, 0x08, 0x14, 0x10, 0x12, 0x08, 0x07, 0x0e,
 0x08, 0x11, 0x0c, 0x0e, 0x19, 0x10, 0x17, 0x0b,
 0x06, 0x0f, 0x16, 0x09, 0x14, 0x12, 0x08, 0x0a,
 0x0e, 0x02, 0x15, 0x0f, 0x11, 0x06, 0x0c, 0x14,
 0x0d, 0x07, 0x00, 0x0f, 0x0b, 0x09, 0x0d, 0x00,
 0x0d, 0x08, 0x0e, 0x18, 0x11, 0x0b, 0x07, 0x03,
 0x0e, 0x03, 0x0e, 0x02, 0x01, 0x0c, 0x13, 0x0f,
 0x04, 0x05, 0x09, 0x15, 0x0f, 0x0e, 0x10, 0x0d,
 0x00, 0x1a, 0x09, 0x0e, 0x13, 0x03, 0x10, 0x04,
 0x0a, 0x0c, 0x13, 0x09, 0x0f, 0x09, 0x09, 0x0c,
 0x0e, 0x11, 0x0f, 0x0d, 0x0e, 0x16, 0x0a, 0x0e,
 0x0c, 0x05, 0x10, 0x03, 0x0c, 0x0f, 0x0a, 0x0a,
 0x00, 0x0c, 0x07, 0x09, 0x06, 0x0d, 0x12, 0x05,
 0x0d, 0x09, 0x09, 0x0b, 0x18, 0x0b, 0x0f, 0x11,
 0x07, 0x03, 0x0a, 0x12, 0x11, 0x0c, 0x17, 0x07,
 0x0e, 0x04, 0x01, 0x0d, 0x0e, 0x0d, 0x08, 0x11,
 0x0d, 0x00, 0x0c, 0x06, 0x06, 0x0b, 0x0e, 0x16,
 0x05, 0x11, 0x09, 0x13, 0x08, 0x0f, 0x01, 0x11,
 0x08, 0x18, 0x04, 0x0d, 0x0f, 0x10, 0x08, 0x02,
 0x00, 0x16, 0x05, 0x10, 0x0b, 0x12, 0x0c, 0x12,
 0x0a, 0x03, 0x01, 0x0e, 0x13, 0x0f, 0x11, 0x0a,
 0x08, 0x11, 0x14, 0x16, 0x07, 0x12, 0x06, 0x0d,
 0x0f, 0x12, 0x07, 0x0f, 0x0e, 0x13, 0x11, 0x02,
 0x15, 0x01, 0x0e, 0x15, 0x00, 0x0d, 0x04, 0x08,
 0x11, 0x00, 0x13, 0x0e, 0x03, 0x06, 0x09, 0x10,
 0x00, 0x10, 0x09, 0x05, 0x0c, 0x15, 0x11, 0x07,
 0x10, 0x0d, 0x13, 0x02, 0x09, 0x12, 0x11, 0x10,
 0x0d, 0x0e, 0x0d, 0x0e, 0x14, 0x0f, 0x03, 0x11,
 0x0d, 0x0e, 0x10, 0x13, 0x12, 0x01, 0x05, 0x0d,
 0x03, 0x0a, 0x0d, 0x0c, 0x0e, 0x0e, 0x09, 0x0d,
 0x07, 0x16, 0x0c, 0x11, 0x00, 0x09, 0x07, 0x0b,
 0x09, 0x0a, 0x00, 0x0d, 0x06, 0x08, 0x0e, 0x01,
 0x0e, 0x11, 0x0c, 0x05, 0x07, 0x10, 0x0b, 0x0a,
 0x00, 0x08, 0x0b, 0x17, 0x09, 0x0c, 0x0c, 0x16,
 0x0f, 0x08, 0x11, 0x13, 0x10, 0x00, 0x0d, 0x09,
 0x0b, 0x18, 0x04, 0x12, 0x10, 0x0c, 0x09, 0x09,
 0x0a, 0x14, 0x00, 0x0c, 0x0a, 0x07, 0x06, 0x0a,
 0x0a, 0x10, 0x01, 0x04, 0x0d, 0x0d, 0x04, 0x0f,
 0x0f, 0x0e, 0x0f, 0x07, 0x13, 0x00, 0x06, 0x11,
 0x03, 0x00, 0x0e, 0x04, 0x0e, 0x0f, 0x0b, 0x10,
 0x07, 0x18, 0x10, 0x0e, 0x0e, 0x01, 0x0c, 0x0a,
 0x10, 0x0d, 0x17, 0x02, 0x03, 0x10, 0x13, 0x08,
 0x07, 0x10, 0x07, 0x11, 0x08, 0x14, 0x15, 0x12,
 0x06, 0x06, 0x0a, 0x14, 0x09, 0x14, 0x0d, 0x0b,
 0x15, 0x02, 0x0c, 0x09, 0x0e, 0x0f, 0x0d, 0x03,
 0x13, 0x0f, 0x10, 0x10, 0x10, 0x13, 0x09, 0x02,
 0x13, 0x0e, 0x06, 0x15, 0x02, 0x0d, 0x10, 0x08,
 0x10, 0x0c, 0x0d, 0x0d, 0x0d, 0x0c, 0x0c, 0x08,
 0x0d, 0x15, 0x10, 0x15, 0x02, 0x11, 0x08, 0x03,
 0x0f, 0x12, 0x13, 0x16, 0x0f, 0x0e, 0x0c, 0x12,
 0x0a, 0x07, 0x0c, 0x09, 0x0a, 0x00, 0x01, 0x0f,
 0x09, 0x04, 0x12, 0x10, 0x10, 0x0e, 0x0a, 0x0d,
 0x0d, 0x10, 0x01, 0x02, 0x03, 0x0b, 0x0d, 0x01,
 0x00, 0x12, 0x03, 0x0e, 0x01, 0x09, 0x01, 0x01,
 0x0b, 0x02, 0x12, 0x12, 0x0f, 0x0c, 0x02, 0x0d,
 0x0f, 0x07, 0x0a, 0x01, 0x10, 0x01, 0x17, 0x12,
 0x09, 0x09, 0x0d, 0x00, 0x00, 0x0d, 0x09, 0x01,
 0x07, 0x0b, 0x0f, 0x0c, 0x14, 0x09, 0x08, 0x10,
 0x0c, 0x11, 0x0c, 0x07, 0x09, 0x11, 0x06, 0x01,
 0x08, 0x0c, 0x15, 0x12, 0x0a, 0x0d, 0x0f, 0x10,
 0x0c, 0x10, 0x0d, 0x15, 0x16, 0x12, 0x01, 0x0a,
 0x05, 0x0d, 0x07, 0x0d, 0x0e, 0x07, 0x09, 0x08,
 0x0b, 0x15, 0x0c, 0x0b, 0x15, 0x0d, 0x0f, 0x0f,
 0x0f, 0x11, 0x08, 0x0b, 0x19, 0x0e, 0x11, 0x15,
 0x0b, 0x12, 0x0d, 0x00, 0x11, 0x0a, 0x0c, 0x06,
 0x10, 0x0c, 0x0e, 0x0d, 0x0a, 0x0c, 0x1b, 0x0a,
 0x10, 0x16, 0x06, 0x0f, 0x0e, 0x10, 0x10, 0x0f,
 0x0f, 0x0b, 0x0f, 0x11, 0x14, 0x08, 0x0e, 0x01,
 0x07, 0x18, 0x13, 0x13, 0x13, 0x0b, 0x11, 0x0d,
 0x0e, 0x02, 0x0b, 0x18, 0x13, 0x0f, 0x02, 0x05,
 0x11, 0x08, 0x0a, 0x11, 0x13, 0x02, 0x10, 0x00,
 0x01, 0x11, 0x04, 0x04, 0x05, 0x11, 0x0c, 0x01,
 0x0c, 0x12, 0x0b, 0x06, 0x0b, 0x00, 0x0e, 0x0d,
 0x09, 0x12, 0x12, 0x14, 0x0e, 0x01, 0x0a, 0x13,
 0x19, 0x19, 0x0d, 0x0a, 0x10, 0x0c, 0x0c, 0x15,
 0x13, 0x0f, 0x0d, 0x0f, 0x16, 0x0c, 0x0b, 0x0e,
 0x08, 0x04, 0x0d, 0x0e, 0x0f, 0x17, 0x11, 0x0d,
 0x09, 0x02, 0x10, 0x0e, 0x07, 0x07, 0x01, 0x04,
 0x03, 0x0d, 0x0f, 0x13, 0x10, 0x11, 0x12, 0x15,
 0x0b, 0x10, 0x0b, 0x0a, 0x0d, 0x0d, 0x12, 0x08,
 0x0e, 0x02, 0x07, 0x10, 0x10, 0x14, 0x0b, 0x15,
 0x12, 0x10, 0x04, 0x0f, 0x10, 0x01, 0x10, 0x10,
 0x01, 0x11, 0x02, 0x10, 0x0d, 0x0f, 0x13, 0x07,
 0x0d, 0x04, 0x10, 0x13, 0x0b, 0x0d, 0x15, 0x0e,
 0x14, 0x01, 0x00, 0x12, 0x0f, 0x0f, 0x0e, 0x10,
 0x0b, 0x11, 0x03, 0x15, 0x0c, 0x0e, 0x0d, 0x01,
 0x15, 0x15, 0x16, 0x10, 0x12, 0x0e, 0x16, 0x0a,
 0x03, 0x01, 0x10, 0x16, 0x0b, 0x0d, 0x02, 0x12,
 0x0e, 0x01, 0x09, 0x16, 0x01, 0x13, 0x0f, 0x0f,
 0x16, 0x03, 0x04, 0x08, 0x0f, 0x10, 0x0e, 0x0b,
 0x0b, 0x10, 0x0d, 0x0a, 0x10, 0x08, 0x10, 0x0e,
 0x11, 0x12, 0x01, 0x02, 0x0a, 0x13, 0x0e, 0x18,
 0x12, 0x09, 0x0e, 0x1a, 0x00, 0x13, 0x02, 0x07,
 0x0b, 0x08, 0x0a, 0x15, 0x12, 0x0e, 0x0c, 0x0c,
 0x01, 0x11, 0x11, 0x12, 0x11, 0x10, 0x0c, 0x09,
 0x12, 0x12, 0x0d, 0x01, 0x0d, 0x12, 0x11, 0x09,
 0x0d, 0x0d, 0x05, 0x10, 0x09, 0x01, 0x09, 0x19,
 0x02, 0x10, 0x10, 0x0b, 0x0d, 0x0b, 0x00, 0x0e,
 0x10, 0x10, 0x0d, 0x12, 0x0f, 0x0d, 0x00, 0x1a,
 0x0e, 0x0c, 0x05, 0x11, 0x0b, 0x11, 0x0e, 0x06,
 0x01, 0x0a, 0x0f, 0x0d, 0x12, 0x0b, 0x0e, 0x0b,
 0x0d, 0x04, 0x0d, 0x04, 0x0c, 0x0d, 0x10, 0x0f,
 0x10, 0x1a, 0x06, 0x07, 0x13, 0x0c, 0x0d, 0x18,
 0x0c, 0x0f, 0x00, 0x0e, 0x11, 0x02, 0x10, 0x13,
 0x0e, 0x0f, 0x09, 0x0c, 0x03, 0x04, 0x0c, 0x13,
 0x14, 0x04, 0x18, 0x11, 0x0b, 0x0d, 0x09, 0x10,
 0x08, 0x12, 0x14, 0x0d, 0x0b, 0x0f, 0x0d, 0x10,
 0x10, 0x10, 0x15, 0x00, 0x04, 0x12, 0x02, 0x18,
 0x17, 0x13, 0x13, 0x00, 0x0f, 0x0f, 0x06, 0x1c,
 0x00, 0x06, 0x0d, 0x0b, 0x0d, 0x17, 0x14, 0x12,
 0x0a, 0x0f, 0x00, 0x0d, 0x0e, 0x10, 0x10, 0x0b,
 0x15, 0x00, 0x02, 0x13, 0x0f, 0x10, 0x0a, 0x08,
 0x01, 0x19, 0x01, 0x06, 0x11, 0x16, 0x0e, 0x17,
 0x0d, 0x01, 0x0e, 0x13, 0x12, 0x11, 0x11, 0x0c,
 0x0b, 0x14, 0x14, 0x07, 0x02, 0x0c, 0x01, 0x09,
 0x12, 0x0b, 0x0a, 0x0f, 0x0c, 0x0f, 0x0b, 0x0c,
 0x06, 0x12, 0x19, 0x08, 0x03, 0x16, 0x19, 0x10,
 0x08, 0x02, 0x0f, 0x0d, 0x0d, 0x07, 0x14, 0x03,
 0x13, 0x00, 0x09, 0x03, 0x0d, 0x0f, 0x0c, 0x01,
 0x07, 0x11, 0x0c, 0x0e, 0x04, 0x0e, 0x17, 0x0c,
 0x0d, 0x08, 0x11, 0x00, 0x00, 0x0a, 0x1b, 0x08,
 0x0f, 0x0d, 0x0c, 0x0e, 0x0f, 0x04, 0x0f, 0x09,
 0x0e, 0x10, 0x10, 0x0d, 0x0f, 0x07, 0x02, 0x0d,
 0x0b, 0x0b, 0x0c, 0x05, 0x16, 0x11, 0x0d, 0x07,
 0x0e, 0x05, 0x02, 0x0a, 0x0e, 0x0b, 0x12, 0x07,
 0x14, 0x0b, 0x0d, 0x0e, 0x10, 0x03, 0x05, 0x1a,
 0x0c, 0x19, 0x02, 0x01, 0x0f, 0x0d, 0x11, 0x03,
 0x10, 0x05, 0x01, 0x05, 0x12, 0x03, 0x04, 0x09,
 0x05, 0x15, 0x09, 0x0f, 0x01, 0x0c, 0x0d, 0x12,
 0x00, 0x10, 0x04, 0x03, 0x07, 0x09, 0x0a, 0x07,
 0x01, 0x0c, 0x10, 0x0e, 0x0e, 0x15, 0x0f, 0x0b,
 0x00, 0x05, 0x14, 0x10, 0x0c, 0x05, 0x04, 0x10,
 0x0b, 0x00, 0x15, 0x13, 0x0c, 0x0b, 0x05, 0x1b,
 0x02, 0x12, 0x0e, 0x0e, 0x0c, 0x0c, 0x04, 0x13,
 0x0e, 0x04, 0x0e, 0x17, 0x01, 0x0d, 0x05, 0x08,
 0x0d, 0x10, 0x0f, 0x00, 0x07, 0x11, 0x05, 0x07,
 0x0c, 0x14, 0x05, 0x12, 0x0e, 0x15, 0x0e, 0x0e,
 0x11, 0x0a, 0x11, 0x04, 0x0a, 0x0e, 0x0b, 0x11,
 0x11, 0x03, 0x0d, 0x0b, 0x12, 0x0c, 0x0d, 0x0a,
 0x05, 0x06, 0x09, 0x05, 0x18, 0x09, 0x10, 0x04,
 0x0c, 0x08, 0x11, 0x0f, 0x10, 0x10, 0x0d, 0x16,
 0x07, 0x07, 0x0f, 0x19, 0x0b, 0x06, 0x0a, 0x0d,
 0x10, 0x08, 0x01, 0x0c, 0x0a, 0x17, 0x0b, 0x00,
 0x06, 0x0d, 0x03, 0x0d, 0x0c, 0x07, 0x0b, 0x0b,
 0x0d, 0x0d, 0x00, 0x10, 0x11, 0x11, 0x11, 0x0b,
 0x10, 0x05, 0x19, 0x12, 0x0d, 0x09, 0x18, 0x0e,
 0x0e, 0x1a, 0x04, 0x06, 0x19, 0x08, 0x17, 0x0c,
 0x01, 0x03, 0x0c, 0x1a, 0x0f, 0x0a, 0x11, 0x0f,
 0x00, 0x0d, 0x05, 0x0c, 0x07, 0x0d, 0x0c, 0x10,
 0x14, 0x13, 0x13, 0x10, 0x0e, 0x10, 0x07, 0x0a,
 0x0c, 0x19, 0x0d, 0x10, 0x0e, 0x11, 0x11, 0x0f,
 0x0b, 0x0d, 0x10, 0x0e, 0x10, 0x13, 0x01, 0x0f,
 0x16, 0x00, 0x0a, 0x13, 0x02, 0x10, 0x0e, 0x09,
 0x0c, 0x0e, 0x03, 0x0b, 0x11, 0x00, 0x0d, 0x0e,
 0x18, 0x10, 0x0a, 0x0a, 0x12, 0x19, 0x1a, 0x0b,
 0x07, 0x1a, 0x03, 0x07, 0x14, 0x16, 0x0f, 0x0a,
 0x0c, 0x0f, 0x19, 0x18, 0x0e, 0x10, 0x11, 0x14,
 0x0b, 0x10, 0x1a, 0x0d, 0x0e, 0x02, 0x08, 0x1c,
 0x0c, 0x08, 0x06, 0x00, 0x12, 0x0f, 0x02, 0x14,
 0x18, 0x10, 0x06, 0x09, 0x0b, 0x1b, 0x0a, 0x01,
 0x09, 0x1a, 0x00, 0x11, 0x0a, 0x0d, 0x0b, 0x0e,
 0x0c, 0x0c, 0x18, 0x13, 0x0b, 0x10, 0x12, 0x0b,
 0x00, 0x17, 0x0d, 0x0c, 0x17, 0x0a, 0x00, 0x0e,
 0x10, 0x1b, 0x0d, 0x12, 0x12, 0x09, 0x10, 0x11,
 0x0b, 0x01, 0x0a, 0x18, 0x0d, 0x08, 0x10, 0x10,
 0x12, 0x08, 0x00, 0x03, 0x08, 0x05, 0x02, 0x10,
 0x05, 0x08, 0x1b, 0x00, 0x11, 0x0c, 0x16, 0x02,
 0x0a, 0x06, 0x0f, 0x11, 0x05, 0x0a, 0x05, 0x0a,
 0x17, 0x05, 0x0e, 0x05, 0x01, 0x10, 0x0d, 0x12,
 0x00, 0x17, 0x10, 0x0a, 0x05, 0x0e, 0x0b, 0x01,
 0x05, 0x08, 0x0c, 0x07, 0x08, 0x0d, 0x1d, 0x16,
 0x16, 0x01, 0x0e, 0x0b, 0x0b, 0x0c, 0x0d, 0x11,
 0x06, 0x0f, 0x14, 0x01, 0x0a, 0x1d, 0x11, 0x0e,
 0x09, 0x18, 0x11, 0x0f, 0x07, 0x0f, 0x00, 0x19,
 0x14, 0x07, 0x0b, 0x10, 0x08, 0x10, 0x0e, 0x08,
 0x15, 0x10, 0x08, 0x0c, 0x0f, 0x0e, 0x10, 0x08,
 0x02, 0x0d, 0x0d, 0x0f, 0x13, 0x12, 0x19, 0x0b,
 0x04, 0x11, 0x15, 0x0b, 0x0c, 0x0c, 0x0e, 0x06,
 0x1e, 0x04, 0x06, 0x04, 0x11, 0x13, 0x00, 0x0b,
 0x01, 0x10, 0x09, 0x0c, 0x0c, 0x1b, 0x11, 0x13,
 0x10, 0x11, 0x0c, 0x09, 0x06, 0x10, 0x0b, 0x08,
 0x17, 0x05, 0x03, 0x0e, 0x18, 0x1a, 0x02, 0x16,
 0x05, 0x17, 0x14, 0x09, 0x09, 0x0a, 0x10, 0x00,
 0x0c, 0x14, 0x16, 0x0e, 0x12, 0x17, 0x17, 0x0d,
 0x0f, 0x10, 0x14, 0x09, 0x0b, 0x03, 0x0b, 0x0e,
 0x03, 0x0c, 0x0e, 0x12, 0x11, 0x0d, 0x1b, 0x03,
 0x07, 0x15, 0x16, 0x0e, 0x0d, 0x0d, 0x12, 0x08,
 0x01, 0x09, 0x0c, 0x0b, 0x11, 0x12, 0x0d, 0x0b,
 0x06, 0x0f, 0x0c, 0x0f, 0x0e, 0x11, 0x09, 0x06
};

// X max array (128 x 32)
static const int x_max[] =
{
 0x15, 0x11, 0x16, 0x1e, 0x1c, 0x1f, 0x19, 0x16,
 0x1e, 0x10, 0x1c, 0x1c, 0x1e, 0x10, 0x1f, 0x1c,
 0x13, 0x16, 0x16, 0x19, 0x12, 0x17, 0x1a, 0x13,
 0x16, 0x0f, 0x1e, 0x1a, 0x1b, 0x16, 0x16, 0x12,
 0x1c, 0x15, 0x19, 0x19, 0x17, 0x1f, 0x1c, 0x1c,
 0x1f, 0x11, 0x15, 0x13, 0x14, 0x1f, 0x12, 0x1e,
 0x18, 0x1e, 0x1e, 0x0f, 0x18, 0x1d, 0x16, 0x12,
 0x12, 0x1d, 0x1d, 0x1d, 0x1a, 0x0d, 0x13, 0x17,
 0x1c, 0x1b, 0x1d, 0x12, 0x19, 0x1a, 0x1e, 0x1b,
 0x10, 0x1c, 0x1f, 0x12, 0x17, 0x10, 0x1d, 0x16,
 0x18, 0x1d, 0x1f, 0x1c, 0x0f, 0x16, 0x17, 0x1c,
 0x0f, 0x17, 0x1e, 0x19, 0x19, 0x12, 0x13, 0x18,
 0x1e, 0x1b, 0x18, 0x1f, 0x1c, 0x19, 0x1e, 0x15,
 0x18, 0x1d, 0x19, 0x1b, 0x1c, 0x13, 0x19, 0x19,
 0x1c, 0x1f, 0x12, 0x15, 0x12, 0x18, 0x18, 0x15,
 0x1e, 0x1c, 0x1a, 0x1b, 0x19, 0x16, 0x12, 0x1f,
 0x1c, 0x1d, 0x14, 0x1c, 0x1e, 0x1b, 0x17, 0x1d,
 0x19, 0x1f, 0x14, 0x17, 0x18, 0x19, 0x1f, 0x17,
 0x1c, 0x15, 0x1d, 0x19, 0x14, 0x1e, 0x19, 0x18,
 0x18, 0x1e, 0x16, 0x0f, 0x19, 0x0e, 0x18, 0x11,
 0x1f, 0x1b, 0x1d, 0x13, 0x19, 0x1f, 0x1a, 0x1f,
 0x12, 0x17, 0x1f, 0x12, 0x16, 0x18, 0x1d, 0x10,
 0x13, 0x18, 0x0d, 0x18, 0x16, 0x16, 0x1c, 0x10,
 0x18, 0x1c, 0x1e, 0x10, 0x1c, 0x18, 0x1d, 0x1f,
 0x1c, 0x11, 0x19, 0x1d, 0x11, 0x17, 0x19, 0x15,
 0x17, 0x18, 0x17, 0x11, 0x1f, 0x18, 0x18, 0x1e,
 0x17, 0x14, 0x13, 0x17, 0x1c, 0x13, 0x1e, 0x14,
 0x10, 0x1a, 0x15, 0x15, 0x18, 0x1a, 0x12, 0x14,
 0x16, 0x1b, 0x12, 0x1c, 0x12, 0x18, 0x1c, 0x15,
 0x1e, 0x13, 0x1c, 0x15, 0x1d, 0x1c, 0x19, 0x1e,
 0x0f, 0x13, 0x1f, 0x1a, 0x18, 0x17, 0x16, 0x1d,
 0x14, 0x1b, 0x12, 0x0c, 0x1f, 0x1e, 0x12, 0x14,
 0x1d, 0x12, 0x15, 0x1a, 0x1c, 0x1f, 0x12, 0x15,
 0x1c, 0x1a, 0x12, 0x1c, 0x13, 0x16, 0x11, 0x16,
 0x1f, 0x1f, 0x15, 0x12, 0x16, 0x13, 0x19, 0x12,
 0x17, 0x1b, 0x18, 0x19, 0x19, 0x14, 0x1d, 0x1d,
 0x1c, 0x1d, 0x1b, 0x12, 0x1d, 0x1a, 0x15, 0x12,
 0x18, 0x1b, 0x19, 0x15, 0x1c, 0x14, 0x10, 0x1c,
 0x19, 0x18, 0x14, 0x1f, 0x15, 0x1a, 0x1a, 0x18,
 0x1d, 0x14, 0x14, 0x18, 0x18, 0x0e, 0x1d, 0x1f,
 0x1d, 0x14, 0x17, 0x1a, 0x16, 0x1b, 0x1b, 0x1a,
 0x1e, 0x11, 0x1e, 0x1d, 0x1a, 0x12, 0x13, 0x1e,
 0x1b, 0x13, 0x1f, 0x1e, 0x0c, 0x13, 0x1f, 0x1c,
 0x1f, 0x1b, 0x19, 0x1e, 0x18, 0x13, 0x1d, 0x1a,
 0x17, 0x1e, 0x13, 0x1c, 0x19, 0x15, 0x1d, 0x1b,
 0x1d, 0x18, 0x1f, 0x1f, 0x1c, 0x10, 0x12, 0x1f,
 0x1e, 0x11, 0x15, 0x19, 0x16, 0x12, 0x1b, 0x15,
 0x17, 0x1c, 0x10, 0x1f, 0x13, 0x1d, 0x0e, 0x1d,
 0x1b, 0x18, 0x1c, 0x13, 0x1a, 0x11, 0x1c, 0x1c,
 0x1b, 0x1f, 0x18, 0x13, 0x12, 0x14, 0x1e, 0x14,
 0x17, 0x0f, 0x10, 0x16, 0x13, 0x18, 0x1b, 0x1a,
 0x18, 0x18, 0x1d, 0x1d, 0x1c, 0x1e, 0x15, 0x1c,
 0x10, 0x18, 0x1f, 0x1e, 0x1d, 0x19, 0x11, 0x12,
 0x1a, 0x13, 0x1d, 0x14, 0x1a, 0x0f, 0x1b, 0x13,
 0x14, 0x1d, 0x1e, 0x0f, 0x15, 0x10, 0x1b, 0x13,
 0x17, 0x18, 0x1b, 0x1d, 0x1a, 0x0f, 0x1d, 0x17,
 0x17, 0x0f, 0x1d, 0x1d, 0x13, 0x1f, 0x18, 0x16,
 0x12, 0x0e, 0x1c, 0x1c, 0x1b, 0x0e, 0x17, 0x10,
 0x15, 0x12, 0x1c, 0x1e, 0x15, 0x1f, 0x14, 0x18,
 0x10, 0x13, 0x1a, 0x1d, 0x19, 0x10, 0x1b, 0x0e,
 0x1c, 0x19, 0x19, 0x15, 0x13, 0x14, 0x1e, 0x14,
 0x18, 0x1c, 0x0b, 0x13, 0x0f, 0x1a, 0x19, 0x1d,
 0x1d, 0x1f, 0x1d, 0x1e, 0x1d, 0x13, 0x13, 0x1d,
 0x1d, 0x1c, 0x1d, 0x11, 0x10, 0x14, 0x19, 0x11,
 0x19, 0x13, 0x11, 0x1b, 0x1d, 0x15, 0x15, 0x1b,
 0x16, 0x12, 0x12, 0x17, 0x1a, 0x1d, 0x1f, 0x17,
 0x19, 0x1c, 0x16, 0x1b, 0x0c, 0x15, 0x10, 0x12,
 0x1f, 0x13, 0x11, 0x19, 0x15, 0x16, 0x0f, 0x0c,
 0x14, 0x11, 0x1e, 0x11, 0x13, 0x1a, 0x16, 0x18,
 0x13, 0x1e, 0x17, 0x15, 0x1b, 0x16, 0x12, 0x16,
 0x19, 0x1d, 0x17, 0x1d, 0x0d, 0x1c, 0x10, 0x1e,
 0x14, 0x14, 0x10, 0x18, 0x15, 0x18, 0x0e, 0x19,
 0x1b, 0x17, 0x0f, 0x1d, 0x15, 0x14, 0x11, 0x19,
 0x16, 0x1d, 0x12, 0x12, 0x19, 0x18, 0x1c, 0x12,
 0x1f, 0x0b, 0x1c, 0x1a, 0x1d, 0x1d, 0x0e, 0x1a,
 0x0d, 0x19, 0x1d, 0x1e, 0x0f, 0x10, 0x1f, 0x19,
 0x0f, 0x1e, 0x19, 0x0d, 0x18, 0x18, 0x0f, 0x19,
 0x1e, 0x1d, 0x15, 0x16, 0x14, 0x15, 0x1e, 0x14,
 0x15, 0x12, 0x1b, 0x1c, 0x1e, 0x10, 0x17, 0x15,
 0x12, 0x1f, 0x14, 0x15, 0x17, 0x11, 0x15, 0x17,
 0x12, 0x1d, 0x16, 0x11, 0x1e, 0x15, 0x10, 0x0f,
 0x13, 0x15, 0x19, 0x1e, 0x1b, 0x13, 0x13, 0x1a,
 0x0e, 0x1e, 0x18, 0x1f, 0x14, 0x19, 0x18, 0x11,
 0x13, 0x1a, 0x17, 0x1c, 0x18, 0x0e, 0x14, 0x10,
 0x17, 0x1c, 0x1c, 0x14, 0x1e, 0x13, 0x1f, 0x13,
 0x14, 0x19, 0x1d, 0x18, 0x1e, 0x18, 0x11, 0x18,
 0x1f, 0x0b, 0x12, 0x15, 0x17, 0x18, 0x16, 0x19,
 0x10, 0x18, 0x16, 0x16, 0x12, 0x18, 0x17, 0x1e,
 0x1c, 0x10, 0x17, 0x0f, 0x12, 0x1f, 0x19, 0x1a,
 0x14, 0x18, 0x1a, 0x1c, 0x1b, 0x1c, 0x1d, 0x1c,
 0x1c, 0x1b, 0x11, 0x1f, 0x1a, 0x1d, 0x15, 0x1e,
 0x12, 0x1f, 0x18, 0x1b, 0x1a, 0x1a, 0x1f, 0x0f,
 0x13, 0x1f, 0x19, 0x17, 0x12, 0x1e, 0x0f, 0x15,
 0x13, 0x1f, 0x14, 0x1e, 0x14, 0x11, 0x10, 0x16,
 0x13, 0x14, 0x18, 0x1c, 0x1a, 0x1c, 0x1b, 0x1f,
 0x1b, 0x12, 0x14, 0x1b, 0x11, 0x0e, 0x10, 0x11,
 0x0f, 0x16, 0x1a, 0x11, 0x1f, 0x16, 0x1c, 0x18,
 0x0f, 0x1e, 0x17, 0x19, 0x14, 0x18, 0x13, 0x17,
 0x1c, 0x0f, 0x16, 0x1d, 0x17, 0x14, 0x1f, 0x1b,
 0x1b, 0x1f, 0x16, 0x12, 0x14, 0x12, 0x10, 0x17,
 0x13, 0x11, 0x14, 0x13, 0x0f, 0x1b, 0x1e, 0x15,
 0x10, 0x1e, 0x1e, 0x1c, 0x1b, 0x18, 0x16, 0x1b,
 0x1c, 0x1c, 0x0a, 0x14, 0x19, 0x0b, 0x14, 0x1d,
 0x1d, 0x15, 0x0e, 0x12, 0x19, 0x0f, 0x1a, 0x14,
 0x1e, 0x16, 0x1c, 0x11, 0x1e, 0x1d, 0x16, 0x10,
 0x10, 0x13, 0x1f, 0x16, 0x1b, 0x1c, 0x12, 0x1f,
 0x15, 0x0e, 0x0f, 0x12, 0x16, 0x1b, 0x14, 0x10,
 0x17, 0x16, 0x1b, 0x1a, 0x14, 0x0f, 0x1a, 0x0f,
 0x0f, 0x16, 0x16, 0x16, 0x1f, 0x14, 0x0e, 0x1a,
 0x12, 0x18, 0x1b, 0x18, 0x12, 0x15, 0x1e, 0x16,
 0x17, 0x1a, 0x1f, 0x0e, 0x11, 0x1a, 0x1d, 0x12,
 0x0d, 0x1b, 0x0d, 0x1f, 0x1e, 0x19, 0x15, 0x1e,
 0x1a, 0x1f, 0x12, 0x16, 0x16, 0x12, 0x14, 0x13,
 0x1c, 0x18, 0x1f, 0x1a, 0x0c, 0x1e, 0x15, 0x08,
 0x12, 0x16, 0x14, 0x0f, 0x17, 0x1d, 0x18, 0x1a,
 0x1f, 0x1b, 0x1b, 0x0c, 0x0f, 0x0f, 0x15, 0x18,
 0x1a, 0x13, 0x1a, 0x1e, 0x10, 0x16, 0x19, 0x1c,
 0x15, 0x16, 0x13, 0x1c, 0x1c, 0x1c, 0x1b, 0x0f,
 0x11, 0x13, 0x1a, 0x13, 0x0c, 0x1b, 0x0d, 0x11,
 0x1a, 0x0d, 0x14, 0x1e, 0x1d, 0x1d, 0x1b, 0x15,
 0x16, 0x16, 0x16, 0x1e, 0x10, 0x1c, 0x19, 0x1f,
 0x14, 0x18, 0x1a, 0x1c, 0x11, 0x1a, 0x12, 0x14,
 0x0f, 0x13, 0x1b, 0x11, 0x1c, 0x10, 0x10, 0x11,
 0x17, 0x18, 0x0f, 0x13, 0x17, 0x11, 0x1e, 0x0e,
 0x11, 0x1c, 0x1c, 0x19, 0x17, 0x17, 0x1a, 0x1e,
 0x0f, 0x15, 0x18, 0x0d, 0x18, 0x1b, 0x0f, 0x14,
 0x0e, 0x17, 0x18, 0x0f, 0x10, 0x18, 0x1c, 0x11,
 0x18, 0x15, 0x19, 0x1d, 0x10, 0x0f, 0x1b, 0x17,
 0x12, 0x1a, 0x1f, 0x1e, 0x11, 0x13, 0x0f, 0x17,
 0x1d, 0x1e, 0x11, 0x1f, 0x14, 0x18, 0x1c, 0x19,
 0x13, 0x11, 0x11, 0x16, 0x1c, 0x17, 0x13, 0x19,
 0x12, 0x1f, 0x13, 0x18, 0x1a, 0x17, 0x19, 0x0f,
 0x1a, 0x16, 0x1e, 0x1c, 0x19, 0x17, 0x0c, 0x17,
 0x15, 0x0f, 0x10, 0x0d, 0x1a, 0x16, 0x13, 0x1d,
 0x11, 0x12, 0x1b, 0x14, 0x14, 0x19, 0x1e, 0x15,
 0x1a, 0x12, 0x18, 0x11, 0x10, 0x18, 0x1f, 0x1f,
 0x1d, 0x1e, 0x1d, 0x13, 0x1f, 0x1b, 0x1c, 0x1b,
 0x11, 0x0e, 0x14, 0x11, 0x1d, 0x1b, 0x18, 0x17,
 0x10, 0x12, 0x0d, 0x15, 0x10, 0x11, 0x1f, 0x1b,
 0x1a, 0x10, 0x0d, 0x1c, 0x1a, 0x1e, 0x16, 0x17,
 0x0f, 0x17, 0x1f, 0x15, 0x15, 0x13, 0x18, 0x18,
 0x12, 0x1a, 0x10, 0x0c, 0x13, 0x1a, 0x16, 0x18,
 0x18, 0x19, 0x1d, 0x0d, 0x19, 0x0f, 0x15, 0x0d,
 0x0c, 0x1a, 0x1c, 0x15, 0x0e, 0x11, 0x1e, 0x10,
 0x17, 0x11, 0x1c, 0x1c, 0x14, 0x1f, 0x15, 0x14,
 0x1c, 0x16, 0x1e, 0x1f, 0x1d, 0x13, 0x19, 0x10,
 0x11, 0x10, 0x17, 0x0e, 0x13, 0x1c, 0x19, 0x1e,
 0x1e, 0x1f, 0x0e, 0x16, 0x12, 0x13, 0x11, 0x1a,
 0x1c, 0x1f, 0x13, 0x11, 0x15, 0x1a, 0x1a, 0x12,
 0x19, 0x17, 0x17, 0x1e, 0x0c, 0x12, 0x13, 0x12,
 0x10, 0x1e, 0x11, 0x1f, 0x0d, 0x18, 0x18, 0x1c,
 0x10, 0x15, 0x10, 0x17, 0x16, 0x16, 0x10, 0x19,
 0x1f, 0x13, 0x1a, 0x17, 0x1e, 0x1c, 0x15, 0x1c,
 0x19, 0x0d, 0x11, 0x1e, 0x1d, 0x0c, 0x16, 0x10,
 0x1c, 0x1b, 0x1b, 0x0d, 0x17, 0x10, 0x16, 0x13,
 0x13, 0x1f, 0x18, 0x13, 0x17, 0x13, 0x19, 0x11,
 0x1b, 0x19, 0x17, 0x13, 0x11, 0x13, 0x1e, 0x17,
 0x0e, 0x1b, 0x18, 0x1e, 0x1d, 0x1d, 0x1c, 0x18,
 0x1d, 0x0f, 0x13, 0x0f, 0x1a, 0x12, 0x1c, 0x14,
 0x0f, 0x0e, 0x1f, 0x13, 0x12, 0x16, 0x13, 0x18,
 0x19, 0x13, 0x17, 0x14, 0x19, 0x14, 0x14, 0x0e,
 0x19, 0x1f, 0x14, 0x1b, 0x0b, 0x0d, 0x1a, 0x14,
 0x14, 0x19, 0x0e, 0x0e, 0x1d, 0x0e, 0x19, 0x19,
 0x11, 0x18, 0x10, 0x1e, 0x18, 0x18, 0x1c, 0x0e,
 0x0f, 0x0f, 0x15, 0x10, 0x18, 0x10, 0x14, 0x1e,
 0x15, 0x0d, 0x1f, 0x17, 0x16, 0x0c, 0x1f, 0x1f,
 0x1f, 0x18, 0x19, 0x10, 0x10, 0x12, 0x1a, 0x0e,
 0x13, 0x1e, 0x16, 0x0e, 0x10, 0x10, 0x1d, 0x1f,
 0x12, 0x11, 0x1b, 0x1b, 0x13, 0x1d, 0x19, 0x11,
 0x17, 0x10, 0x17, 0x1f, 0x11, 0x11, 0x16, 0x11,
 0x15, 0x16, 0x12, 0x1c, 0x19, 0x0d, 0x12, 0x16,
 0x12, 0x17, 0x12, 0x10, 0x10, 0x0e, 0x12, 0x10,
 0x16, 0x1d, 0x1e, 0x1f, 0x0b, 0x19, 0x13, 0x19,
 0x1e, 0x12, 0x19, 0x17, 0x10, 0x11, 0x17, 0x10,
 0x17, 0x1f, 0x17, 0x0e, 0x18, 0x10, 0x14, 0x10,
 0x10, 0x1e, 0x12, 0x1f, 0x10, 0x1f, 0x1b, 0x12,
 0x1a, 0x1c, 0x16, 0x19, 0x14, 0x1a, 0x1c, 0x15,
 0x0f, 0x14, 0x14, 0x16, 0x1e, 0x1c, 0x14, 0x17,
 0x18, 0x1d, 0x1b, 0x13, 0x12, 0x0e, 0x14, 0x1d,
 0x1c, 0x1d, 0x0f, 0x12, 0x12, 0x1f, 0x13, 0x15,
 0x1b, 0x1d, 0x19, 0x0c, 0x1b, 0x17, 0x10, 0x1f,
 0x1b, 0x16, 0x11, 0x18, 0x11, 0x0e, 0x13, 0x14,
 0x1e, 0x11, 0x0f, 0x17, 0x0e, 0x19, 0x1e, 0x0e,
 0x10, 0x1b, 0x1c, 0x1a, 0x11, 0x0e, 0x10, 0x1d,
 0x12, 0x15, 0x14, 0x1b, 0x11, 0x0e, 0x11, 0x19,
 0x1b, 0x1a, 0x1e, 0x1f, 0x14, 0x17, 0x0e, 0x10,
 0x15, 0x14, 0x1f, 0x0e, 0x0e, 0x16, 0x13, 0x1d,
 0x12, 0x0e, 0x1d, 0x1d, 0x11, 0x19, 0x15, 0x17,
 0x1d, 0x1e, 0x18, 0x14, 0x18, 0x18, 0x1c, 0x0f,
 0x1f, 0x0f, 0x1d, 0x16, 0x10, 0x17, 0x12, 0x0e,
 0x1b, 0x14, 0x18, 0x16, 0x12, 0x0b, 0x1f, 0x1e,
 0x15, 0x12, 0x12, 0x1e, 0x12, 0x19, 0x0d, 0x17,
 0x0f, 0x1e, 0x1e, 0x10, 0x1a, 0x11, 0x15, 0x14,
 0x16, 0x1f, 0x17, 0x10, 0x11, 0x10, 0x0c, 0x10,
 0x11, 0x1b, 0x18, 0x1b, 0x19, 0x1d, 0x10, 0x18,
 0x12, 0x10, 0x19, 0x1e, 0x10, 0x1a, 0x0c, 0x16,
 0x1f, 0x1c, 0x18, 0x1a, 0x1e, 0x12, 0x13, 0x17,
 0x10, 0x18, 0x18, 0x17, 0x1b, 0x0f, 0x1c, 0x14,
 0x18, 0x18, 0x0f, 0x1d, 0x0b, 0x15, 0x18, 0x17,
 0x17, 0x1b, 0x15, 0x14, 0x19, 0x11, 0x19, 0x1a,
 0x13, 0x18, 0x10, 0x12, 0x12, 0x1e, 0x1d, 0x1c,
 0x14, 0x0e, 0x0c, 0x0e, 0x11, 0x12, 0x15, 0x1c,
 0x16, 0x0e, 0x0f, 0x13, 0x11, 0x0f, 0x19, 0x1b,
 0x1b, 0x1d, 0x10, 0x1c, 0x0f, 0x17, 0x15, 0x11,
 0x12, 0x18, 0x1e, 0x16, 0x15, 0x19, 0x17, 0x0f,
 0x18, 0x1b, 0x1a, 0x12, 0x15, 0x0f, 0x17, 0x1f,
 0x17, 0x19, 0x10, 0x15, 0x0f, 0x15, 0x18, 0x19,
 0x0c, 0x1a, 0x17, 0x10, 0x11, 0x12, 0x0f, 0x1e,
 0x13, 0x1d, 0x1d, 0x1f, 0x11, 0x15, 0x1c, 0x12,
 0x1c, 0x1f, 0x16, 0x17, 0x1a, 0x17, 0x11, 0x19,
 0x11, 0x17, 0x0f, 0x14, 0x18, 0x10, 0x13, 0x1d,
 0x11, 0x0c, 0x1c, 0x15, 0x1e, 0x11, 0x15, 0x10,
 0x12, 0x15, 0x0f, 0x0f, 0x1b, 0x16, 0x11, 0x11,
 0x14, 0x14, 0x1a, 0x17, 0x14, 0x1a, 0x12, 0x14,
 0x12, 0x17, 0x18, 0x10, 0x19, 0x1d, 0x10, 0x0f,
 0x17, 0x0e, 0x11, 0x11, 0x11, 0x18, 0x1c, 0x13,
 0x1a, 0x11, 0x19, 0x14, 0x18, 0x1f, 0x14, 0x13,
 0x12, 0x18, 0x1a, 0x1a, 0x0c, 0x0f, 0x19, 0x0e,
 0x16, 0x18, 0x13, 0x0f, 0x10, 0x1c, 0x13, 0x0c,
 0x13, 0x10, 0x18, 0x10, 0x14, 0x08, 0x0e, 0x18,
 0x12, 0x10, 0x11, 0x19, 0x19, 0x0f, 0x1b, 0x1a,
 0x10, 0x1e, 0x0d, 0x19, 0x17, 0x19, 0x1e, 0x0e,
 0x1a, 0x1a, 0x16, 0x17, 0x11, 0x1e, 0x1f, 0x15,
 0x1c, 0x11, 0x10, 0x0e, 0x0d, 0x1a, 0x1d, 0x13,
 0x13, 0x19, 0x0d, 0x19, 0x1b, 0x12, 0x1e, 0x16,
 0x13, 0x17, 0x1f, 0x1f, 0x12, 0x1d, 0x15, 0x1d,
 0x10, 0x19, 0x0c, 0x1d, 0x1e, 0x1e, 0x18, 0x18,
 0x17, 0x18, 0x1d, 0x0b, 0x0e, 0x1e, 0x14, 0x0b,
 0x1e, 0x16, 0x0d, 0x1b, 0x18, 0x1d, 0x0f, 0x1e,
 0x15, 0x13, 0x12, 0x12, 0x13, 0x17, 0x1d, 0x11,
 0x1c, 0x0c, 0x1f, 0x10, 0x0e, 0x18, 0x1a, 0x12,
 0x1b, 0x1f, 0x15, 0x0d, 0x1c, 0x1a, 0x14, 0x17,
 0x13, 0x1f, 0x17, 0x15, 0x0e, 0x17, 0x0d, 0x0f,
 0x11, 0x10, 0x13, 0x17, 0x17, 0x19, 0x1a, 0x0c,
 0x1b, 0x11, 0x10, 0x12, 0x16, 0x15, 0x08, 0x11,
 0x19, 0x0f, 0x17, 0x1c, 0x18, 0x19, 0x1a, 0x1f,
 0x1d, 0x11, 0x0f, 0x17, 0x1c, 0x19, 0x15, 0x0e,
 0x11, 0x10, 0x14, 0x16, 0x15, 0x0f, 0x17, 0x16,
 0x18, 0x1f, 0x12, 0x18, 0x1a, 0x14, 0x12, 0x0c,
 0x12, 0x1b, 0x19, 0x11, 0x11, 0x13, 0x1f, 0x0e,
 0x10, 0x18, 0x1c, 0x14, 0x17, 0x15, 0x0f, 0x12,
 0x0c, 0x15, 0x1d, 0x17, 0x16, 0x18, 0x10, 0x19,
 0x18, 0x0d, 0x19, 0x18, 0x13, 0x1a, 0x11, 0x12,
 0x15, 0x12, 0x17, 0x10, 0x1c, 0x0e, 0x12, 0x1a,
 0x16, 0x1a, 0x17, 0x11, 0x13, 0x14, 0x0f, 0x19,
 0x17, 0x0d, 0x19, 0x12, 0x14, 0x15, 0x14, 0x13,
 0x13, 0x1e, 0x12, 0x15, 0x0c, 0x13, 0x1a, 0x18,
 0x0e, 0x1a, 0x07, 0x13, 0x09, 0x13, 0x1e, 0x1a,
 0x0b, 0x1a, 0x1a, 0x1c, 0x14, 0x0c, 0x18, 0x13,
 0x1c, 0x1a, 0x12, 0x14, 0x19, 0x11, 0x1c, 0x13,
 0x0b, 0x15, 0x15, 0x1c, 0x1b, 0x11, 0x13, 0x1e,
 0x18, 0x11, 0x19, 0x15, 0x15, 0x12, 0x14, 0x1d,
 0x16, 0x11, 0x17, 0x10, 0x1f, 0x0e, 0x1f, 0x15,
 0x12, 0x0c, 0x15, 0x10, 0x12, 0x11, 0x10, 0x1d,
 0x17, 0x1c, 0x0e, 0x18, 0x12, 0x15, 0x13, 0x18,
 0x11, 0x1c, 0x1c, 0x14, 0x0c, 0x11, 0x11, 0x11,
 0x17, 0x11, 0x1a, 0x17, 0x14, 0x1a, 0x12, 0x12,
 0x10, 0x13, 0x16, 0x15, 0x0f, 0x1a, 0x1f, 0x1b,
 0x1e, 0x15, 0x11, 0x0c, 0x18, 0x0f, 0x17, 0x1c,
 0x0a, 0x1e, 0x0a, 0x1f, 0x12, 0x13, 0x11, 0x0e,
 0x18, 0x11, 0x1d, 0x18, 0x18, 0x12, 0x13, 0x18,
 0x1e, 0x1c, 0x0f, 0x19, 0x0f, 0x18, 0x10, 0x17,
 0x17, 0x0c, 0x15, 0x10, 0x0f, 0x10, 0x13, 0x13,
 0x1b, 0x1a, 0x13, 0x14, 0x0c, 0x14, 0x18, 0x0f,
 0x14, 0x18, 0x17, 0x1d, 0x1b, 0x15, 0x1e, 0x0f,
 0x15, 0x1a, 0x0d, 0x0e, 0x18, 0x12, 0x1e, 0x1c,
 0x1f, 0x1a, 0x19, 0x17, 0x13, 0x16, 0x14, 0x19,
 0x16, 0x1c, 0x1f, 0x1e, 0x10, 0x0f, 0x1d, 0x1d,
 0x12, 0x0d, 0x19, 0x0d, 0x15, 0x11, 0x0e, 0x15,
 0x18, 0x10, 0x19, 0x13, 0x14, 0x1b, 0x13, 0x19,
 0x18, 0x1e, 0x12, 0x1f, 0x12, 0x1c, 0x0f, 0x12,
 0x1f, 0x1e, 0x18, 0x15, 0x12, 0x10, 0x0e, 0x0f,
 0x15, 0x0c, 0x18, 0x11, 0x19, 0x14, 0x0c, 0x10,
 0x18, 0x13, 0x1e, 0x1a, 0x0b, 0x1c, 0x14, 0x1d,
 0x16, 0x15, 0x1f, 0x15, 0x11, 0x0a, 0x0e, 0x0f,
 0x16, 0x0f, 0x12, 0x11, 0x0e, 0x0e, 0x13, 0x1f,
 0x12, 0x12, 0x15, 0x10, 0x18, 0x0f, 0x1b, 0x1d,
 0x0f, 0x0f, 0x08, 0x11, 0x0e, 0x15, 0x18, 0x0b,
 0x1b, 0x18, 0x14, 0x12, 0x10, 0x1e, 0x1d, 0x12,
 0x11, 0x11, 0x13, 0x18, 0x1a, 0x0e, 0x14, 0x19,
 0x18, 0x1b, 0x16, 0x1a, 0x17, 0x0f, 0x0f, 0x11,
 0x12, 0x12, 0x1a, 0x1e, 0x1f, 0x18, 0x15, 0x18,
 0x17, 0x13, 0x15, 0x1f, 0x16, 0x1e, 0x1c, 0x10,
 0x1c, 0x0d, 0x16, 0x0d, 0x16, 0x0d, 0x18, 0x13,
 0x1e, 0x12, 0x11, 0x11, 0x12, 0x1d, 0x17, 0x1e,
 0x10, 0x13, 0x1f, 0x1a, 0x1f, 0x13, 0x1a, 0x1a,
 0x0e, 0x10, 0x1f, 0x0c, 0x17, 0x0d, 0x0f, 0x19,
 0x16, 0x0f, 0x14, 0x17, 0x0d, 0x12, 0x11, 0x17,
 0x1d, 0x1a, 0x14, 0x0c, 0x19, 0x1c, 0x14, 0x10,
 0x0e, 0x1b, 0x0a, 0x15, 0x11, 0x19, 0x12, 0x14,
 0x1f, 0x15, 0x11, 0x17, 0x14, 0x10, 0x17, 0x18,
 0x11, 0x10, 0x18, 0x17, 0x1d, 0x17, 0x10, 0x1e,
 0x18, 0x19, 0x11, 0x12, 0x1f, 0x12, 0x11, 0x16,
 0x15, 0x15, 0x16, 0x11, 0x1c, 0x16, 0x0f, 0x0f,
 0x14, 0x1f, 0x12, 0x08, 0x1b, 0x12, 0x1d, 0x14,
 0x1b, 0x16, 0x10, 0x1f, 0x19, 0x11, 0x0b, 0x12,
 0x0f, 0x12, 0x17, 0x14, 0x12, 0x13, 0x1c, 0x10,
 0x14, 0x1b, 0x11, 0x14, 0x10, 0x1b, 0x11, 0x0b,
 0x1c, 0x13, 0x1d, 0x1c, 0x13, 0x1b, 0x0f, 0x0e,
 0x19, 0x12, 0x12, 0x11, 0x1d, 0x0f, 0x0e, 0x14,
 0x0f, 0x14, 0x1e, 0x18, 0x10, 0x16, 0x1d, 0x1f,
 0x10, 0x13, 0x1a, 0x0a, 0x11, 0x1e, 0x10, 0x0d,
 0x12, 0x16, 0x10, 0x1d, 0x12, 0x17, 0x1c, 0x12,
 0x12, 0x0e, 0x0c, 0x18, 0x10, 0x13, 0x07, 0x1c,
 0x14, 0x16, 0x1e, 0x16, 0x10, 0x16, 0x1a, 0x16,
 0x1d, 0x19, 0x1e, 0x16, 0x1d, 0x1d, 0x1c, 0x10,
 0x10, 0x0b, 0x1d, 0x1e, 0x0b, 0x18, 0x19, 0x19,
 0x17, 0x11, 0x09, 0x19, 0x16, 0x1e, 0x1f, 0x10,
 0x0d, 0x1b, 0x1e, 0x1a, 0x12, 0x0e, 0x1c, 0x1e,
 0x14, 0x13, 0x18, 0x0b, 0x0f, 0x12, 0x17, 0x09,
 0x13, 0x12, 0x11, 0x0f, 0x1b, 0x0e, 0x12, 0x10,
 0x12, 0x0e, 0x13, 0x18, 0x14, 0x18, 0x1d, 0x15,
 0x1c, 0x14, 0x0f, 0x13, 0x18, 0x16, 0x11, 0x0c,
 0x13, 0x19, 0x18, 0x14, 0x0f, 0x1b, 0x0b, 0x0d,
 0x17, 0x11, 0x1e, 0x14, 0x1a, 0x1a, 0x1a, 0x1e,
 0x11, 0x11, 0x16, 0x11, 0x1e, 0x0e, 0x08, 0x11,
 0x13, 0x1a, 0x19, 0x10, 0x12, 0x1c, 0x14, 0x18,
 0x10, 0x19, 0x12, 0x14, 0x11, 0x14, 0x0f, 0x18,
 0x1e, 0x11, 0x1d, 0x0f, 0x17, 0x1e, 0x15, 0x0f,
 0x10, 0x11, 0x0e, 0x1c, 0x13, 0x1a, 0x18, 0x0d,
 0x08, 0x1d, 0x16, 0x13, 0x16, 0x1a, 0x10, 0x1d,
 0x09, 0x1b, 0x08, 0x1b, 0x13, 0x12, 0x0e, 0x0f,
 0x13, 0x15, 0x0d, 0x11, 0x13, 0x13, 0x1c, 0x18,
 0x1d, 0x0c, 0x1e, 0x13, 0x14, 0x13, 0x1c, 0x11,
 0x0d, 0x18, 0x19, 0x11, 0x1f, 0x12, 0x1d, 0x12,
 0x0e, 0x19, 0x1e, 0x1e, 0x1e, 0x16, 0x10, 0x1a,
 0x1a, 0x16, 0x1c, 0x15, 0x1f, 0x15, 0x14, 0x1c,
 0x11, 0x18, 0x07, 0x1c, 0x0f, 0x1f, 0x13, 0x07,
 0x1a, 0x12, 0x13, 0x1f, 0x1a, 0x11, 0x0d, 0x1c,
 0x1e, 0x14, 0x1d, 0x1c, 0x14, 0x11, 0x1d, 0x12,
 0x0b, 0x0f, 0x13, 0x1d, 0x11, 0x11, 0x14, 0x1b,
 0x15, 0x1f, 0x10, 0x18, 0x19, 0x10, 0x18, 0x10,
 0x1e, 0x11, 0x1c, 0x0f, 0x1f, 0x0f, 0x14, 0x13,
 0x17, 0x19, 0x11, 0x15, 0x11, 0x1a, 0x13, 0x12,
 0x12, 0x0e, 0x11, 0x1e, 0x0d, 0x18, 0x15, 0x11,
 0x08, 0x17, 0x0a, 0x0e, 0x1f, 0x19, 0x14, 0x16,
 0x0f, 0x0b, 0x17, 0x0f, 0x1d, 0x15, 0x13, 0x1a,
 0x14, 0x0c, 0x0c, 0x17, 0x1b, 0x0e, 0x1f, 0x0b,
 0x11, 0x12, 0x0b, 0x18, 0x11, 0x1f, 0x0c, 0x14,
 0x10, 0x0b, 0x12, 0x11, 0x0c, 0x0e, 0x15, 0x1c,
 0x12, 0x13, 0x15, 0x1e, 0x0f, 0x10, 0x13, 0x17,
 0x0e, 0x1e, 0x15, 0x0e, 0x11, 0x14, 0x1f, 0x17,
 0x10, 0x1f, 0x0f, 0x11, 0x16, 0x16, 0x14, 0x1c,
 0x1a, 0x0f, 0x0e, 0x11, 0x15, 0x10, 0x1d, 0x11,
 0x0f, 0x1d, 0x18, 0x1e, 0x16, 0x16, 0x1b, 0x13,
 0x16, 0x1e, 0x0e, 0x16, 0x12, 0x17, 0x14, 0x09,
 0x1b, 0x13, 0x18, 0x1a, 0x1b, 0x16, 0x12, 0x16,
 0x1e, 0x17, 0x18, 0x12, 0x0d, 0x0e, 0x0d, 0x17,
 0x0e, 0x11, 0x0e, 0x11, 0x12, 0x19, 0x17, 0x10,
 0x17, 0x11, 0x1e, 0x1d, 0x10, 0x16, 0x16, 0x12,
 0x12, 0x12, 0x18, 0x19, 0x1e, 0x16, 0x1d, 0x14,
 0x18, 0x11, 0x15, 0x1f, 0x16, 0x07, 0x12, 0x13,
 0x10, 0x0e, 0x1e, 0x0d, 0x19, 0x14, 0x17, 0x10,
 0x10, 0x1b, 0x19, 0x19, 0x0d, 0x18, 0x19, 0x0f,
 0x14, 0x15, 0x15, 0x17, 0x0f, 0x09, 0x0f, 0x10,
 0x13, 0x18, 0x10, 0x1e, 0x10, 0x13, 0x15, 0x0c,
 0x17, 0x10, 0x12, 0x1f, 0x12, 0x14, 0x0f, 0x1f,
 0x12, 0x0e, 0x17, 0x19, 0x11, 0x06, 0x13, 0x0e,
 0x0e, 0x1f, 0x08, 0x19, 0x11, 0x19, 0x0f, 0x0d,
 0x18, 0x18, 0x16, 0x15, 0x10, 0x0e, 0x13, 0x11,
 0x13, 0x11, 0x0a, 0x0c, 0x12, 0x15, 0x0f, 0x14,
 0x13, 0x12, 0x12, 0x0e, 0x1c, 0x10, 0x0d, 0x18,
 0x10, 0x0f, 0x12, 0x15, 0x11, 0x19, 0x14, 0x17,
 0x12, 0x1e, 0x19, 0x15, 0x0f, 0x09, 0x10, 0x14,
 0x1a, 0x0f, 0x1f, 0x17, 0x15, 0x11, 0x1a, 0x0c,
 0x11, 0x19, 0x11, 0x1a, 0x17, 0x15, 0x1f, 0x1f,
 0x0f, 0x0e, 0x0f, 0x1e, 0x18, 0x1f, 0x14, 0x12,
 0x1f, 0x0f, 0x0d, 0x0f, 0x13, 0x12, 0x0e, 0x0d,
 0x19, 0x17, 0x17, 0x12, 0x14, 0x1b, 0x11, 0x0c,
 0x17, 0x11, 0x14, 0x17, 0x0c, 0x1f, 0x11, 0x11,
 0x16, 0x14, 0x0e, 0x12, 0x0f, 0x10, 0x14, 0x12,
 0x12, 0x18, 0x15, 0x1b, 0x18, 0x14, 0x0d, 0x0d,
 0x15, 0x1a, 0x17, 0x1c, 0x18, 0x10, 0x1a, 0x1a,
 0x0e, 0x0b, 0x12, 0x0d, 0x0f, 0x09, 0x0d, 0x11,
 0x0d, 0x10, 0x16, 0x15, 0x16, 0x15, 0x12, 0x10,
 0x1b, 0x18, 0x07, 0x18, 0x0b, 0x13, 0x0f, 0x0f,
 0x15, 0x19, 0x1b, 0x12, 0x1c, 0x1b, 0x08, 0x1c,
 0x14, 0x12, 0x18, 0x18, 0x17, 0x11, 0x09, 0x10,
 0x14, 0x1a, 0x0d, 0x0a, 0x14, 0x17, 0x1e, 0x1d,
 0x0f, 0x0c, 0x12, 0x05, 0x07, 0x1f, 0x13, 0x0a,
 0x1a, 0x13, 0x10, 0x0e, 0x19, 0x12, 0x10, 0x11,
 0x10, 0x15, 0x10, 0x0d, 0x0c, 0x16, 0x11, 0x0e,
 0x11, 0x1b, 0x1c, 0x1a, 0x0c, 0x12, 0x16, 0x15,
 0x14, 0x12, 0x1f, 0x1e, 0x1c, 0x1c, 0x18, 0x0d,
 0x0a, 0x19, 0x0f, 0x17, 0x1f, 0x10, 0x0c, 0x0c,
 0x12, 0x17, 0x19, 0x11, 0x19, 0x0e, 0x13, 0x18,
 0x14, 0x12, 0x11, 0x0e, 0x1d, 0x13, 0x15, 0x1e,
 0x12, 0x1b, 0x13, 0x15, 0x17, 0x14, 0x15, 0x11,
 0x14, 0x17, 0x10, 0x10, 0x12, 0x10, 0x1f, 0x16,
 0x12, 0x1e, 0x1b, 0x11, 0x17, 0x14, 0x1d, 0x18,
 0x14, 0x0e, 0x10, 0x16, 0x18, 0x10, 0x0f, 0x08,
 0x1a, 0x1f, 0x1d, 0x1b, 0x1a, 0x13, 0x18, 0x13,
 0x14, 0x12, 0x17, 0x1e, 0x19, 0x15, 0x0d, 0x11,
 0x12, 0x18, 0x0f, 0x17, 0x1d, 0x0a, 0x11, 0x0f,
 0x07, 0x15, 0x0b, 0x0d, 0x10, 0x12, 0x0f, 0x14,
 0x12, 0x17, 0x1e, 0x0e, 0x0c, 0x0c, 0x0f, 0x0f,
 0x0e, 0x1e, 0x17, 0x1c, 0x11, 0x13, 0x15, 0x15,
 0x1f, 0x1d, 0x14, 0x0e, 0x15, 0x1b, 0x16, 0x17,
 0x1a, 0x11, 0x12, 0x14, 0x1f, 0x10, 0x16, 0x16,
 0x1c, 0x0f, 0x13, 0x12, 0x16, 0x1f, 0x14, 0x17,
 0x1c, 0x11, 0x16, 0x11, 0x1e, 0x11, 0x0a, 0x09,
 0x0e, 0x18, 0x12, 0x1c, 0x15, 0x16, 0x19, 0x1f,
 0x18, 0x11, 0x17, 0x0d, 0x14, 0x10, 0x16, 0x0b,
 0x16, 0x0e, 0x0b, 0x11, 0x13, 0x15, 0x10, 0x1d,
 0x1d, 0x13, 0x0a, 0x13, 0x13, 0x09, 0x12, 0x16,
 0x1a, 0x15, 0x10, 0x17, 0x1e, 0x10, 0x1a, 0x11,
 0x0e, 0x0e, 0x11, 0x17, 0x0d, 0x15, 0x1a, 0x17,
 0x17, 0x19, 0x09, 0x16, 0x1a, 0x11, 0x17, 0x13,
 0x0f, 0x13, 0x14, 0x1f, 0x10, 0x1b, 0x10, 0x08,
 0x1e, 0x1a, 0x1c, 0x11, 0x1c, 0x11, 0x1b, 0x0c,
 0x0e, 0x06, 0x11, 0x1e, 0x10, 0x11, 0x0a, 0x16,
 0x1a, 0x0c, 0x14, 0x1d, 0x15, 0x1e, 0x13, 0x14,
 0x1e, 0x09, 0x13, 0x1b, 0x11, 0x14, 0x1e, 0x15,
 0x1e, 0x11, 0x11, 0x13, 0x18, 0x0d, 0x14, 0x16,
 0x17, 0x16, 0x07, 0x0b, 0x10, 0x17, 0x0f, 0x1e,
 0x1c, 0x10, 0x13, 0x1f, 0x06, 0x15, 0x19, 0x10,
 0x0e, 0x0f, 0x17, 0x1d, 0x19, 0x18, 0x13, 0x10,
 0x0c, 0x15, 0x1b, 0x1a, 0x17, 0x1f, 0x13, 0x13,
 0x1e, 0x13, 0x0f, 0x03, 0x0f, 0x17, 0x12, 0x0d,
 0x1b, 0x17, 0x11, 0x15, 0x14, 0x09, 0x0c, 0x1d,
 0x07, 0x12, 0x17, 0x15, 0x1d, 0x14, 0x18, 0x14,
 0x11, 0x15, 0x0f, 0x13, 0x17, 0x14, 0x0d, 0x1f,
 0x16, 0x1b, 0x0e, 0x13, 0x0e, 0x15, 0x17, 0x13,
 0x0c, 0x15, 0x10, 0x0e, 0x19, 0x11, 0x0f, 0x12,
 0x14, 0x11, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x10,
 0x16, 0x1e, 0x09, 0x0f, 0x16, 0x15, 0x16, 0x1b,
 0x15, 0x19, 0x0b, 0x11, 0x15, 0x09, 0x18, 0x18,
 0x18, 0x14, 0x11, 0x11, 0x0e, 0x0e, 0x0f, 0x14,
 0x1f, 0x0d, 0x1d, 0x12, 0x11, 0x15, 0x0d, 0x14,
 0x17, 0x1f, 0x16, 0x12, 0x10, 0x13, 0x13, 0x18,
 0x1b, 0x11, 0x1b, 0x18, 0x0f, 0x1a, 0x1c, 0x1a,
 0x1a, 0x1f, 0x16, 0x0b, 0x11, 0x1e, 0x07, 0x1f,
 0x0c, 0x14, 0x0f, 0x14, 0x0f, 0x1d, 0x1b, 0x15,
 0x15, 0x12, 0x0a, 0x0f, 0x10, 0x13, 0x13, 0x12,
 0x1f, 0x09, 0x05, 0x14, 0x1d, 0x17, 0x0f, 0x0e,
 0x09, 0x1c, 0x11, 0x10, 0x12, 0x1e, 0x10, 0x1b,
 0x10, 0x06, 0x16, 0x15, 0x14, 0x19, 0x13, 0x10,
 0x1f, 0x1a, 0x1b, 0x19, 0x06, 0x0d, 0x0f, 0x11,
 0x16, 0x1f, 0x0f, 0x13, 0x10, 0x17, 0x11, 0x0e,
 0x0a, 0x14, 0x1e, 0x0a, 0x05, 0x1a, 0x1f, 0x1b,
 0x0e, 0x05, 0x1f, 0x0e, 0x12, 0x1b, 0x18, 0x0b,
 0x19, 0x05, 0x14, 0x16, 0x11, 0x12, 0x14, 0x13,
 0x18, 0x18, 0x16, 0x1a, 0x10, 0x11, 0x1d, 0x0e,
 0x0e, 0x13, 0x17, 0x0f, 0x09, 0x0c, 0x1f, 0x0a,
 0x14, 0x1b, 0x11, 0x12, 0x19, 0x0f, 0x1a, 0x10,
 0x19, 0x12, 0x1c, 0x11, 0x12, 0x0b, 0x14, 0x10,
 0x0e, 0x11, 0x19, 0x16, 0x19, 0x18, 0x16, 0x0f,
 0x14, 0x0e, 0x16, 0x0d, 0x11, 0x10, 0x16, 0x0f,
 0x18, 0x1e, 0x0e, 0x0f, 0x1d, 0x0a, 0x14, 0x1d,
 0x1e, 0x1e, 0x14, 0x10, 0x12, 0x11, 0x15, 0x0e,
 0x13, 0x08, 0x09, 0x10, 0x19, 0x09, 0x0c, 0x13,
 0x0e, 0x18, 0x10, 0x12, 0x16, 0x14, 0x12, 0x17,
 0x0f, 0x18, 0x0d, 0x0f, 0x16, 0x13, 0x13, 0x0e,
 0x06, 0x14, 0x17, 0x1b, 0x17, 0x19, 0x11, 0x1e,
 0x17, 0x0d, 0x1c, 0x1b, 0x15, 0x0f, 0x0c, 0x14,
 0x19, 0x08, 0x1c, 0x1a, 0x10, 0x0f, 0x12, 0x1d,
 0x0e, 0x14, 0x12, 0x1f, 0x1a, 0x15, 0x0c, 0x18,
 0x13, 0x06, 0x11, 0x1d, 0x18, 0x12, 0x0b, 0x1a,
 0x16, 0x16, 0x1b, 0x0b, 0x0f, 0x1b, 0x16, 0x14,
 0x14, 0x1c, 0x0c, 0x16, 0x13, 0x1a, 0x1f, 0x15,
 0x1d, 0x0e, 0x16, 0x08, 0x12, 0x14, 0x12, 0x12,
 0x13, 0x0a, 0x12, 0x0c, 0x16, 0x0f, 0x17, 0x12,
 0x0b, 0x11, 0x14, 0x0e, 0x1f, 0x1d, 0x13, 0x19,
 0x10, 0x0d, 0x13, 0x11, 0x11, 0x15, 0x0f, 0x19,
 0x16, 0x0b, 0x11, 0x1c, 0x12, 0x0d, 0x1e, 0x0f,
 0x11, 0x14, 0x14, 0x12, 0x12, 0x1f, 0x11, 0x12,
 0x0e, 0x0e, 0x06, 0x10, 0x11, 0x11, 0x0f, 0x19,
 0x12, 0x13, 0x0c, 0x1a, 0x1e, 0x13, 0x12, 0x10,
 0x12, 0x13, 0x1f, 0x13, 0x16, 0x18, 0x1a, 0x14,
 0x11, 0x1f, 0x11, 0x19, 0x1c, 0x15, 0x19, 0x0e,
 0x0f, 0x0e, 0x13, 0x1f, 0x11, 0x0e, 0x18, 0x13,
 0x07, 0x0e, 0x12, 0x11, 0x0d, 0x10, 0x0e, 0x16,
 0x19, 0x17, 0x1f, 0x16, 0x10, 0x15, 0x1c, 0x1e,
 0x10, 0x1e, 0x13, 0x1b, 0x0f, 0x18, 0x15, 0x12,
 0x16, 0x11, 0x16, 0x12, 0x11, 0x18, 0x10, 0x1a,
 0x1f, 0x09, 0x0e, 0x1c, 0x18, 0x19, 0x10, 0x0d,
 0x11, 0x15, 0x10, 0x0e, 0x15, 0x06, 0x1b, 0x10,
 0x1e, 0x12, 0x11, 0x0f, 0x16, 0x1d, 0x1e, 0x1a,
 0x10, 0x1e, 0x1e, 0x0f, 0x1f, 0x17, 0x15, 0x10,
 0x11, 0x12, 0x1f, 0x1f, 0x11, 0x11, 0x14, 0x19,
 0x1a, 0x12, 0x1f, 0x14, 0x0f, 0x1f, 0x0d, 0x1f,
 0x10, 0x19, 0x11, 0x10, 0x18, 0x16, 0x0a, 0x1d,
 0x1e, 0x11, 0x14, 0x10, 0x14, 0x1e, 0x11, 0x1e,
 0x0e, 0x1e, 0x08, 0x13, 0x14, 0x10, 0x11, 0x13,
 0x0f, 0x12, 0x1e, 0x14, 0x10, 0x11, 0x14, 0x10,
 0x0a, 0x1b, 0x1b, 0x10, 0x1f, 0x12, 0x07, 0x0f,
 0x12, 0x1d, 0x0e, 0x13, 0x16, 0x0d, 0x15, 0x12,
 0x14, 0x07, 0x12, 0x1f, 0x12, 0x0b, 0x16, 0x13,
 0x1f, 0x0c, 0x0b, 0x10, 0x18, 0x15, 0x0e, 0x11,
 0x0f, 0x10, 0x1e, 0x07, 0x17, 0x14, 0x1c, 0x0b,
 0x0f, 0x0d, 0x17, 0x17, 0x11, 0x0c, 0x17, 0x0e,
 0x18, 0x12, 0x11, 0x0a, 0x06, 0x11, 0x15, 0x16,
 0x08, 0x1a, 0x12, 0x1b, 0x0b, 0x15, 0x12, 0x0f,
 0x0e, 0x14, 0x10, 0x0f, 0x0b, 0x13, 0x1f, 0x1f,
 0x1a, 0x07, 0x11, 0x0f, 0x0c, 0x12, 0x0f, 0x1d,
 0x0a, 0x10, 0x15, 0x0e, 0x0f, 0x1f, 0x14, 0x0f,
 0x0c, 0x1f, 0x13, 0x16, 0x16, 0x13, 0x13, 0x1d,
 0x1f, 0x10, 0x11, 0x15, 0x0b, 0x17, 0x10, 0x0d,
 0x18, 0x13, 0x0b, 0x0f, 0x16, 0x12, 0x11, 0x10,
 0x16, 0x10, 0x16, 0x10, 0x19, 0x1c, 0x1e, 0x0f,
 0x1e, 0x1d, 0x1a, 0x10, 0x14, 0x0d, 0x0f, 0x0c,
 0x1f, 0x0c, 0x11, 0x13, 0x18, 0x16, 0x10, 0x0f,
 0x06, 0x12, 0x0f, 0x11, 0x0d, 0x1f, 0x18, 0x14,
 0x18, 0x14, 0x0f, 0x0d, 0x0b, 0x1b, 0x12, 0x16,
 0x1e, 0x1d, 0x09, 0x12, 0x1f, 0x1e, 0x0b, 0x1e,
 0x13, 0x1f, 0x16, 0x0d, 0x0d, 0x0f, 0x11, 0x02,
 0x10, 0x15, 0x1b, 0x16, 0x19, 0x1d, 0x1f, 0x10,
 0x10, 0x11, 0x1f, 0x16, 0x0d, 0x0e, 0x0d, 0x0f,
 0x15, 0x17, 0x13, 0x1b, 0x13, 0x0e, 0x1d, 0x12,
 0x13, 0x1e, 0x1b, 0x12, 0x1e, 0x19, 0x1b, 0x0d,
 0x0b, 0x11, 0x11, 0x1e, 0x18, 0x1a, 0x0f, 0x0e,
 0x15, 0x10, 0x14, 0x12, 0x13, 0x19, 0x0b, 0x0b
};

// alpha array (128 x 32)
static const unsigned int alpha[] =
{
 0x3cef7503, 0x3cfd2ba5, 0x3cf05aa6, 0x3cefcc41, 0x3cf38cf5, 0x3cee9758, 0x3ceb7fab, 0x3ce21a72,
 0x3ce314a2, 0x3cd8e905, 0x3cd7099c, 0x3cd63fa8, 0x3cd746d9, 0x3cd5926e, 0x3ccf1bfd, 0x3cc94f34,
 0x3cc3fd50, 0x3cc22ccb, 0x3cbef467, 0x3cbb2e75, 0x3cd8b60f, 0x3cb7036d, 0x3cb49ea3, 0x3cb4dd22,
 0x3caabf5c, 0x3cab61e3, 0x3ca8cfcd, 0x3cb089a0, 0x3ca72b88, 0x3cabd49a, 0x3ca47489, 0x3c9e6b5b,
 0x3ce56f91, 0x3cf2ac90, 0x3cea7d82, 0x3ce75ddd, 0x3ce70959, 0x3ce42b8e, 0x3cdf7795, 0x3cde1991,
 0x3cd31261, 0x3cd29a36, 0x3cd5138d, 0x3cca20b6, 0x3cc635da, 0x3cc6be96, 0x3cc4885b, 0x3cbead85,
 0x3cc1de27, 0x3cbcd757, 0x3cbba8ee, 0x3cba83bf, 0x3cb83962, 0x3cb9d401, 0x3cb9d727, 0x3cb13fde,
 0x3cae0f72, 0x3cad2964, 0x3ca61ee3, 0x3ca680d3, 0x3ca92df7, 0x3c9f746f, 0x3ca31437, 0x3c99db3e,
 0x3cea02d3, 0x3cf1baf9, 0x3cf26253, 0x3cf99c6e, 0x3cf41180, 0x3ce89c00, 0x3cec3182, 0x3ce1be61,
 0x3cd77bb2, 0x3cd8c3b1, 0x3cd763ca, 0x3cd03537, 0x3cd17f4f, 0x3cc8ed44, 0x3cc556b7, 0x3cc3abbc,
 0x3cbdaff9, 0x3cc50706, 0x3cba8f48, 0x3cba7b26, 0x3cbc5f97, 0x3cb68888, 0x3cb1d34c, 0x3cad5e3d,
 0x3cb2cb2e, 0x3caa16bf, 0x3cb1857e, 0x3ca6f38a, 0x3ca16865, 0x3ca3a6ce, 0x3ca1b882, 0x3ca2958c,
 0x3ce9c165, 0x3cf6ce93, 0x3cf33806, 0x3cf35929, 0x3ce572b6, 0x3ce96f65, 0x3ce0a70a, 0x3ce1e34a,
 0x3cd67705, 0x3cd3d16e, 0x3cd1753e, 0x3ccb6376, 0x3cc8ff18, 0x3cca361a, 0x3cc0dc69, 0x3cc2c435,
 0x3cc11f4f, 0x3cc0c232, 0x3cb8f86f, 0x3cb22c6d, 0x3cb5acc0, 0x3cb99151, 0x3cad121c, 0x3cacf4f6,
 0x3ca97686, 0x3cae9574, 0x3cab5ef4, 0x3ca380d9, 0x3ca690f9, 0x3ca1ee32, 0x3c985b41, 0x3c9d183f,
 0x3ce845cf, 0x3cec6e53, 0x3cedaf9b, 0x3ceb792b, 0x3ce22051, 0x3cde9948, 0x3cdbc416, 0x3cd5cfab,
 0x3cdb37ff, 0x3cccdaa4, 0x3cd96a34, 0x3ccb0d45, 0x3cc55f86, 0x3cc4ad0e, 0x3cc230c7, 0x3cbe0afe,
 0x3cbb242e, 0x3cbcd64a, 0x3cbc30d3, 0x3cb449b4, 0x3cba860e, 0x3cacdd43, 0x3cacac66, 0x3caa916e,
 0x3ca81df7, 0x3ca589fd, 0x3ca7e480, 0x3ca30f2e, 0x3ca3821b, 0x3c9ff7b7, 0x3c9cdd86, 0x3c9b54f0,
 0x3ce3d01f, 0x3ce5f0c0, 0x3ce59734, 0x3cdf7e80, 0x3cd7419b, 0x3cd8c1ce, 0x3ccf4913, 0x3cc99e7a,
 0x3cca515d, 0x3cc411dd, 0x3cc3f812, 0x3cc15d98, 0x3cc1bff4, 0x3cbbc0d6, 0x3cbcbdc1, 0x3cceae19,
 0x3cb5e636, 0x3cb25b31, 0x3cb46c4e, 0x3cb698e4, 0x3cb2f4ea, 0x3caf0938, 0x3ca70959, 0x3ca70486,
 0x3ca1d898, 0x3c9f6124, 0x3c9edee8, 0x3c9e4d28, 0x3c9a6b1b, 0x3c9a3098, 0x3c9a9725, 0x3c945309,
 0x3cda0cbc, 0x3cef6dac, 0x3ce41695, 0x3ce290b9, 0x3ce3d633, 0x3cdd0df8, 0x3cd74ed1, 0x3cccf180,
 0x3cce72f5, 0x3cceddb4, 0x3cca89c7, 0x3cbe3814, 0x3cbd4b50, 0x3cbf20a7, 0x3cb6611b, 0x3cb6541a,
 0x3cb3ee44, 0x3cb36d4b, 0x3cbbf21f, 0x3cb24024, 0x3cad952f, 0x3caa8728, 0x3ca9043b, 0x3ca7ce7b,
 0x3ca779c2, 0x3ca1e852, 0x3c9db080, 0x3c9bfd8d, 0x3c9b95f3, 0x3c9cc63f, 0x3ca0a237, 0x3c955d2a,
 0x3cdaed57, 0x3ce3f82d, 0x3ceb7168, 0x3cdea649, 0x3cdd6719, 0x3cd70ea5, 0x3cd18d91, 0x3cd00bb1,
 0x3cccdbb1, 0x3cd3a23f, 0x3cc45092, 0x3cc757ad, 0x3cbe7297, 0x3cb6db29, 0x3cbb3e9a, 0x3cb9d32b,
 0x3cd6bef4, 0x3caea59a, 0x3cb88730, 0x3cab8e59, 0x3cab97ff, 0x3ca552a0, 0x3ca6e257, 0x3ca57bbb,
 0x3ca3fcca, 0x3ca50e0c, 0x3c9f6307, 0x3c97e564, 0x3ca8ab85, 0x3c9b6cd8, 0x3c9e08ca, 0x3c931ec1,
 0x3cce6b32, 0x3ceb74c3, 0x3ce7dd29, 0x3ce51fe0, 0x3cd6967a, 0x3cd52ed1, 0x3cd7844b, 0x3cc9ff93,
 0x3ccc07e1, 0x3cc7e465, 0x3cc7c2a2, 0x3cc8eb2b, 0x3cbd7e46, 0x3cb754cb, 0x3cbbbe88, 0x3cb5dd67,
 0x3cba8496, 0x3cadf033, 0x3cb5092c, 0x3cab8950, 0x3cad835b, 0x3cac8f75, 0x3ca12f5a, 0x3cb9b090,
 0x3ca3765c, 0x3c9fe005, 0x3c9dd59e, 0x3c9c91d1, 0x3c99a954, 0x3c9a610a, 0x3c97c980, 0x3c9571b7,
 0x3ccd2044, 0x3ceb03ef, 0x3cdd1af9, 0x3ce30553, 0x3cdd6429, 0x3cda5368, 0x3cd2e657, 0x3ccd6e7e,
 0x3ccebd32, 0x3cce728a, 0x3cc56565, 0x3cc58dde, 0x3cbd9cae, 0x3cbaf6e1, 0x3cb99f28, 0x3cbab06b,
 0x3cb9dcd0, 0x3cb834fb, 0x3cb3ca9d, 0x3cb3db2f, 0x3caf5ce5, 0x3caa7fd1, 0x3ca7cff3, 0x3ca819c5,
 0x3ca4c86c, 0x3ca48b2f, 0x3ca6fa40, 0x3ca2f8f4, 0x3c9bcb38, 0x3c9a5e50, 0x3c9c574f, 0x3c9556a9,
 0x3ccd2fc9, 0x3cd83c6d, 0x3cdbeaad, 0x3cd8102d, 0x3cd68258, 0x3cd04959, 0x3ccb054c, 0x3cc857f3,
 0x3cbf7ed1, 0x3cc4ed3a, 0x3cbac2a9, 0x3cc398a6, 0x3cb6d8da, 0x3cb49782, 0x3cb332fe, 0x3cb1787e,
 0x3cad1684, 0x3cb199d6, 0x3cb113d4, 0x3cabf2cd, 0x3ca830d6, 0x3ca45abe, 0x3ca4ba5f, 0x3c9e7bec,
 0x3c9f5c1c, 0x3c98da21, 0x3ca052f1, 0x3c9b0d01, 0x3c94082a, 0x3c938a56, 0x3c8e7d71, 0x3c8a44fd,
 0x3cccb3d8, 0x3cd6436e, 0x3ce10a06, 0x3cd25969, 0x3cd0ca1d, 0x3ccaea3f, 0x3ccc43db, 0x3cc51838,
 0x3cc97f05, 0x3cbb81ec, 0x3cb9954d, 0x3cc078cc, 0x3cb9da4c, 0x3cb42d64, 0x3cb6357c, 0x3caf67cd,
 0x3caf7ae2, 0x3cb3845c, 0x3cad2e01, 0x3cae613c, 0x3caaa84a, 0x3ca586d8, 0x3ca534d8, 0x3c9fb3fa,
 0x3ca2af57, 0x3c9b8ad6, 0x3ca08f57, 0x3c9aa4fd, 0x3c95c19e, 0x3c9ce945, 0x3c91cd52, 0x3c91c98c,
 0x3cbe9f0d, 0x3ccf0ec6, 0x3cd61b96, 0x3cd486a0, 0x3ccc334a, 0x3cc628d9, 0x3cd082cf, 0x3cc11e79,
 0x3cbfabb2, 0x3cbdd65a, 0x3cba87f1, 0x3cbd298c, 0x3cb7b03b, 0x3cb4da9d, 0x3cb2e317, 0x3cb469ca,
 0x3cae66b0, 0x3cb14ec2, 0x3ca7041b, 0x3ca7cbf7, 0x3ca5aaea, 0x3c9dd2af, 0x3c9dbca9, 0x3c9cb868,
 0x3ca074eb, 0x3c98b27e, 0x3c9a191b, 0x3c983a1e, 0x3c93db14, 0x3c949873, 0x3c8d9d77, 0x3c8e0074,
 0x3cd5025b, 0x3cd57a1a, 0x3cd42f2c, 0x3ccbbc2c, 0x3cc82101, 0x3cca488e, 0x3cc57c76, 0x3ccd03bf,
 0x3cba2dc4, 0x3cbadb68, 0x3cb83bb1, 0x3cb12ab0, 0x3cb4f575, 0x3cb4ee8a, 0x3cb1d2ab, 0x3cac0288,
 0x3cab6bf4, 0x3ca82441, 0x3ca459b1, 0x3cb976e4, 0x3c9ddcf5, 0x3caaa98c, 0x3c9d2e44, 0x3c9b0758,
 0x3c9c1250, 0x3c97d432, 0x3c9852a7, 0x3c9275b9, 0x3c93179f, 0x3c9e33c8, 0x3c9001a0, 0x3c8768e0,
 0x3cc6b9c3, 0x3cd7ef40, 0x3ccc78b4, 0x3cd1390d, 0x3cc5ef99, 0x3cc56854, 0x3cc2d3ba, 0x3cc05b3a,
 0x3cc07896, 0x3cb9280a, 0x3cb8d4fe, 0x3cb48a81, 0x3cb568cd, 0x3cb16d2b, 0x3cb3c9fc, 0x3ca87d2c,
 0x3cacfb76, 0x3cadd8b6, 0x3ca7eacb, 0x3ca14f05, 0x3c9d5ff8, 0x3ca0c862, 0x3c9b99ef, 0x3c970098,
 0x3c98ac34, 0x3c950947, 0x3c9333b9, 0x3c9588fe, 0x3c8d0170, 0x3c8e7b58, 0x3c916477, 0x3c942f98,
 0x3cc50e5d, 0x3cd87ab6, 0x3cdb7a44, 0x3cd3bb33, 0x3ccc99d7, 0x3ccbe7ca, 0x3ccb2be3, 0x3cc3745e,
 0x3cc89e34, 0x3cc23a6d, 0x3cb7a919, 0x3cbef757, 0x3cb5b482, 0x3cb4ccfc, 0x3cb3c84f, 0x3cb280f1,
 0x3ca91502, 0x3cacc3e3, 0x3caf709b, 0x3c9fb862, 0x3ca36205, 0x3ca12514, 0x3ca00fa0, 0x3c9923f3,
 0x3ca04a8e, 0x3c9b3403, 0x3c9e21f4, 0x3c96b79d, 0x3c91acd1, 0x3c90092d, 0x3c93aa01, 0x3c8d05a2,
 0x3cbe34b9, 0x3cd2f068, 0x3cd207d5, 0x3cd34efd, 0x3cc3d46b, 0x3cc3a67e, 0x3cc0280e, 0x3cbe0d82,
 0x3cbd0cd1, 0x3cbbe122, 0x3cb05f79, 0x3cb7f900, 0x3caebdee, 0x3cb30c9d, 0x3cab986a, 0x3ca64e48,
 0x3ca57499, 0x3ca60c6f, 0x3c9ed6f0, 0x3ca01472, 0x3ca0d710, 0x3c9b7897, 0x3c9d27c4, 0x3c9dcccf,
 0x3c9223b9, 0x3caded79, 0x3ca21717, 0x3c97833f, 0x3c8ff3c9, 0x3c8bfcc3, 0x3c916583, 0x3c9368c8,
 0x3cbcb197, 0x3cdba4d7, 0x3cccb22b, 0x3ccc2614, 0x3cc91695, 0x3cc468b0, 0x3cc0a329, 0x3cbe73d9,
 0x3cbd482a, 0x3cb5a16d, 0x3cb58553, 0x3cb76f38, 0x3cb6413a, 0x3cac0cce, 0x3cae93fd, 0x3ca8a9d8,
 0x3ca51d91, 0x3ca21a72, 0x3c9f5c87, 0x3c9e518f, 0x3c9e405d, 0x3c9c1326, 0x3c9a1a5d, 0x3c9b72b8,
 0x3c9ccbe9, 0x3c95ebc5, 0x3c91a61b, 0x3c92502f, 0x3c98b2ea, 0x3c9011c6, 0x3c8d6eb3, 0x3c8ad942,
 0x3cb574c1, 0x3ccad221, 0x3cc98f2b, 0x3cc3e99a, 0x3cc43e1d, 0x3cba78a2, 0x3cc26d2d, 0x3cbadb68,
 0x3cb540bf, 0x3cb49427, 0x3cafe6ad, 0x3cb2b488, 0x3cac0a4a, 0x3ca886d2, 0x3ca7feed, 0x3cb67430,
 0x3ca3b221, 0x3c9fa97e, 0x3ca11340, 0x3c9deefe, 0x3c987684, 0x3c986b9c, 0x3c9d9fee, 0x3c958a0b,
 0x3c95be43, 0x3c90ff2c, 0x3c8ebf15, 0x3c8e3e87, 0x3c9a18e6, 0x3c8b7e18, 0x3c87d5b7, 0x3c858bc6,
 0x3cb78f18, 0x3cc4a7d0, 0x3cc80c74, 0x3cbfdf13, 0x3cc6876e, 0x3cb816fd, 0x3cbde0a1, 0x3cb51952,
 0x3cb7963a, 0x3caaf725, 0x3caf2694, 0x3cb10419, 0x3cad4af2, 0x3ca86bfa, 0x3ca52d81, 0x3ca40f74,
 0x3ca6882a, 0x3caeb04c, 0x3ca32676, 0x3ca0ec3f, 0x3c8e185c, 0x3c972515, 0x3c94cf65, 0x3c9a554c,
 0x3c9c2d5d, 0x3c8edef6, 0x3c9290fc, 0x3c8cb627, 0x3c8722d4, 0x3c8a5e5e, 0x3c89dbb6, 0x3c8698d6,
 0x3cc9586e, 0x3cc70c99, 0x3cc9f4ab, 0x3cc57845, 0x3cbd30e3, 0x3cba2bab, 0x3cbb0849, 0x3cc5857b,
 0x3cb1e18f, 0x3cadbeb5, 0x3cad6777, 0x3ca8c11f, 0x3cadfe40, 0x3cb86a0a, 0x3ca90979, 0x3ca68b4f,
 0x3ca030c2, 0x3ca43d62, 0x3ca19404, 0x3ca0e304, 0x3ca0d2a9, 0x3c96a452, 0x3c94d940, 0x3c91d54a,
 0x3c8dd3c8, 0x3c9271bd, 0x3c8999a7, 0x3c8afb71, 0x3c8688b0, 0x3c98636e, 0x3c87d912, 0x3c8715d4,
 0x3cb37a81, 0x3cc248e5, 0x3cbd7a7f, 0x3cbc2408, 0x3cb2ed5d, 0x3cb38aa7, 0x3cb797e7, 0x3cb58ac7,
 0x3cb0ce34, 0x3cac530f, 0x3cb071ee, 0x3ca3f141, 0x3ca761a4, 0x3ca0a7e1, 0x3ca5fdc1, 0x3c9f0abd,
 0x3c9aa7b7, 0x3ca17a03, 0x3ca04b65, 0x3c9b188a, 0x3c96fda8, 0x3c92af9b, 0x3c92150b, 0x3c8b295f,
 0x3c89c4db, 0x3c8d6d3b, 0x3c8abf0b, 0x3c886c4b, 0x3c8f2e06, 0x3c84000a, 0x3c835c0b, 0x3c831ceb,
 0x3ca52701, 0x3cc65981, 0x3cc2a928, 0x3cbb8f22, 0x3cb36446, 0x3cbd7a49, 0x3cb04684, 0x3cb3ca9d,
 0x3cb1bd47, 0x3caec398, 0x3ca55483, 0x3ca907cc, 0x3ca96afe, 0x3c9f4ff2, 0x3ca8bdc4, 0x3c9fd7d7,
 0x3ca11ec9, 0x3c95c81e, 0x3c989e27, 0x3c959d56, 0x3c92a305, 0x3c94ff6c, 0x3c91485d, 0x3c924512,
 0x3c8b58fa, 0x3c8e2f38, 0x3c8ed9b8, 0x3c824afe, 0x3c868372, 0x3c80e67a, 0x3c842529, 0x3c8380f4,
 0x3cad3c0e, 0x3cbe81e6, 0x3cb75968, 0x3cb75679, 0x3cb97064, 0x3cb6a005, 0x3cb6e13e, 0x3cac4a76,
 0x3cafd8a0, 0x3ca6e0e0, 0x3ca2f52d, 0x3ca2afc3, 0x3cab4451, 0x3ca28f41, 0x3c9f15a5, 0x3ca1225a,
 0x3cae2867, 0x3ca92776, 0x3c9cf1a8, 0x3c964f98, 0x3c914d2f, 0x3c9123df, 0x3c93d3f2, 0x3c8f4fff,
 0x3c9074c2, 0x3c91a4d9, 0x3c8d6ddc, 0x3c8ff10f, 0x3c89037f, 0x3c8873d7, 0x3c993b3a, 0x3c8a1710,
 0x3ca7bf97, 0x3cbebd0a, 0x3cb92ec0, 0x3cb2173f, 0x3cb35f73, 0x3cae5e17, 0x3caca728, 0x3ca6b4d5,
 0x3cb1c0a2, 0x3ca845cf, 0x3ca088a1, 0x3c9fed71, 0x3ca0a486, 0x3c9ee31a, 0x3ca3864d, 0x3c9b48fc,
 0x3c9b8d5a, 0x3c9df472, 0x3c9bdcd5, 0x3c963d59, 0x3c8d8051, 0x3c91fe65, 0x3c93c2f6, 0x3c8fb4df,
 0x3c8fa5fb, 0x3c873182, 0x3c8392c7, 0x3c845275, 0x3c87541d, 0x3c8248b0, 0x3c87bc22, 0x3c7c957d,
 0x3ca2a5b2, 0x3cbf444e, 0x3cbe61d0, 0x3cb5dfec, 0x3cad5c24, 0x3cb5f0e8, 0x3cac62ca, 0x3ca8bced,
 0x3cad2dcb, 0x3ca045bb, 0x3c9ffa06, 0x3c9cf7f3, 0x3c9e9659, 0x3c9d8369, 0x3c9e2768, 0x3c985887,
 0x3c9b9ac6, 0x3c9322f2, 0x3c901953, 0x3c94d113, 0x3c91e42e, 0x3c89c5e7, 0x3c8b9311, 0x3c8c0fa3,
 0x3c87c881, 0x3c89e3af, 0x3c86214d, 0x3c88d5fd, 0x3c825403, 0x3c8047ef, 0x3c81cdcb, 0x3c7ac95f,
 0x3ca004ee, 0x3cb98a9b, 0x3cb4a193, 0x3cbc96f5, 0x3cad301a, 0x3ca96554, 0x3ca893d3, 0x3cabb590,
 0x3ca90547, 0x3ca36f71, 0x3ca0a3af, 0x3ca3eb62, 0x3c9e8526, 0x3c950e85, 0x3c989f9f, 0x3c96c9a6,
 0x3c950b2a, 0x3c92aa5c, 0x3c93726e, 0x3c971b70, 0x3c995e76, 0x3c8e1ea7, 0x3c959e2d, 0x3c88893b,
 0x3c8c4fcf, 0x3c87e506, 0x3c8504b7, 0x3c878fe2, 0x3c841d66, 0x3c85875e, 0x3c795f67, 0x3c83c7d6,
 0x3ca17962, 0x3cb442c8, 0x3cb62124, 0x3cb4fdd9, 0x3cabd1ab, 0x3ca4d1a6, 0x3ca67763, 0x3ca6c318,
 0x3ca2368c, 0x3ca215d4, 0x3c9f6307, 0x3c9cb75b, 0x3ca8df52, 0x3ca34abe, 0x3c9a86ff, 0x3c95ad10,
 0x3c92490e, 0x3c9a133c, 0x3c900e00, 0x3c912125, 0x3c8fe627, 0x3c8f71f8, 0x3c8a951a, 0x3c90c14e,
 0x3c90ead4, 0x3c834f40, 0x3c86c5b7, 0x3c82a1d1, 0x3c7d665d, 0x3c78982d, 0x3c7bf7c8, 0x3c7e018e,
 0x3c9eae77, 0x3cb50be6, 0x3cb5ab48, 0x3cb5b340, 0x3ca969bc, 0x3caef72f, 0x3caf979d, 0x3ca27bc0,
 0x3ca05bc0, 0x3c9f7a84, 0x3ca15550, 0x3c9cd8e9, 0x3c9e488b, 0x3c94eaa8, 0x3c9a92f4, 0x3c95b905,
 0x3c99369d, 0x3c92391d, 0x3c8ea408, 0x3c8f9a07, 0x3c8d056c, 0x3c878672, 0x3c8be115, 0x3c8e55ce,
 0x3c86f07f, 0x3c82ad59, 0x3c862259, 0x3c847d08, 0x3c8d690a, 0x3c7adb68, 0x3c7d35b6, 0x3c7ab5a9,
 0x3c9473c0, 0x3caf170f, 0x3cade72e, 0x3cb011e1, 0x3cb9eea4, 0x3cacaa83, 0x3caaa26b, 0x3ca619a5,
 0x3ca47885, 0x3c9d79f9, 0x3c9f34e4, 0x3c977d60, 0x3c97aa0b, 0x3c95d986, 0x3c97e05c, 0x3c96b9b6,
 0x3c8e9630, 0x3c940c26, 0x3c8e9b39, 0x3c8e3293, 0x3c8ef1a0, 0x3c87d25c, 0x3c8a93a2, 0x3c80df8e,
 0x3c8a9e1e, 0x3c841cfb, 0x3c84cc83, 0x3c83067a, 0x3c7eed46, 0x3c8333fd, 0x3c7535a2, 0x3c710f37,
 0x3c98affa, 0x3cb75d64, 0x3cb0e073, 0x3caf8777, 0x3cad9092, 0x3ca5e496, 0x3ca8c5f2, 0x3ca54e6e,
 0x3cab8dee, 0x3ca1c1bc, 0x3c9c8755, 0x3ca0d23d, 0x3c9769df, 0x3c9567dc, 0x3c994b96, 0x3c8f8544,
 0x3c908b9e, 0x3c935d0a, 0x3c8d760a, 0x3caab657, 0x3c8a460a, 0x3c8bf71a, 0x3c85ac47, 0x3c83cf98,
 0x3c881832, 0x3c84e9df, 0x3c858eeb, 0x3c836b90, 0x3c753968, 0x3c860f43, 0x3c8092cd, 0x3c747299,
 0x3c9b1382, 0x3cb036ca, 0x3cacad07, 0x3ca92d8b, 0x3cae3e6c, 0x3cae1d14, 0x3c9ff1a2, 0x3c9f6988,
 0x3cb3c66c, 0x3c9a176e, 0x3c991bc5, 0x3c9996aa, 0x3c949441, 0x3c993c47, 0x3c8d9abe, 0x3c925d65,
 0x3c9172b9, 0x3c8c377c, 0x3c916c39, 0x3c8c366f, 0x3c8ac307, 0x3c8941c8, 0x3c8310c1, 0x3c842345,
 0x3c8080fa, 0x3c794d5e, 0x3c7fe451, 0x3c82ef9f, 0x3c7cd390, 0x3c83b023, 0x3c80e0d1, 0x3c7b8257,
 0x3c9363c0, 0x3cae8ca6, 0x3caa48a9, 0x3ca3d01f, 0x3c9d0636, 0x3c9dc4a2, 0x3cab1caf, 0x3c990cac,
 0x3c980ff7, 0x3c97957e, 0x3cb327e0, 0x3c94db8f, 0x3c8f5bbe, 0x3c942e8b, 0x3c90525e, 0x3c8e1933,
 0x3c92ac0a, 0x3c942ec1, 0x3c8ca743, 0x3c87820a, 0x3c85128e, 0x3c89f150, 0x3c83d5ad, 0x3c864163,
 0x3c810e1d, 0x3c7be1f9, 0x3c7a7bc7, 0x3c7fd827, 0x3c804a08, 0x3c7dca9c, 0x3c77b7c8, 0x3c7ffb63,
 0x3c92bd07, 0x3ca789e7, 0x3ca9bbf1, 0x3c9fefbf, 0x3c9e40fe, 0x3ca020d2, 0x3c9e9440, 0x3c9c9cef,
 0x3c9e1a68, 0x3c971f00, 0x3ca252a6, 0x3c964693, 0x3c96de69, 0x3c8fcc91, 0x3c8d279b, 0x3c89b7a4,
 0x3caa3197, 0x3c8df698, 0x3c878b0f, 0x3c81d0f0, 0x3c83e319, 0x3c810152, 0x3c815edb, 0x3c80d837,
 0x3c7ea019, 0x3c8c6859, 0x3c7c83df, 0x3c7558dd, 0x3c7bf254, 0x3c6c8f0a, 0x3c7be04b, 0x3c6ded43,
 0x3c92443b, 0x3ca595f1, 0x3ca3dc48, 0x3ca7cfbd, 0x3c9e7104, 0x3c9c867e, 0x3c9b8f3d, 0x3c93bd4c,
 0x3c923847, 0x3c98fe33, 0x3c93264d, 0x3c991e14, 0x3c89d495, 0x3c8c1b97, 0x3c87c4bb, 0x3c8955ea,
 0x3c8e7f54, 0x3c94face, 0x3c898301, 0x3c844d6d, 0x3c8fef2c, 0x3c802f65, 0x3c811178, 0x3c7e4d78,
 0x3c7190d1, 0x3c754379, 0x3c8106fc, 0x3c799d0f, 0x3c73a9b0, 0x3c6b93cd, 0x3c6ee4ba, 0x3c6a3238,
 0x3c92b50e, 0x3ca8a109, 0x3ca3a414, 0x3c996c18, 0x3ca18988, 0x3c9bc375, 0x3c9b9552, 0x3c98820d,
 0x3c9a7d24, 0x3c92882d, 0x3c95212f, 0x3c90932b, 0x3c9553ba, 0x3c8f16f4, 0x3c890994, 0x3c911963,
 0x3c90c6f8, 0x3c835082, 0x3c840075, 0x3c7fc2c3, 0x3c7e5789, 0x3c83013c, 0x3c771e45, 0x3c821e53,
 0x3c797fb3, 0x3c70039e, 0x3c6a6d5c, 0x3c713272, 0x3c784373, 0x3c71f007, 0x3c67e0ba, 0x3c78d5d5,
 0x3c8fa707, 0x3ca27ff2, 0x3c9b07f9, 0x3c9de7a7, 0x3ca3f141, 0x3c962896, 0x3c96b3d6, 0x3c91ecc7,
 0x3c98c5ca, 0x3c9085f5, 0x3c8d54e8, 0x3c9078f4, 0x3c90173a, 0x3c883dbc, 0x3c8b6f6a, 0x3c8e8b13,
 0x3c8665aa, 0x3c860ed8, 0x3c837967, 0x3c7f51f0, 0x3c8a13eb, 0x3c7bc721, 0x3c7906e7, 0x3c79d61a,
 0x3c7314cb, 0x3c73032d, 0x3c7f8a24, 0x3c6fa24f, 0x3c6f4100, 0x3c66531b, 0x3c6e83d7, 0x3c73dc05,
 0x3c944567, 0x3c9c3555, 0x3c9e378e, 0x3c9659a9, 0x3c9bf198, 0x3c93db7f, 0x3c955242, 0x3c9a14b4,
 0x3c950e85, 0x3c8d760a, 0x3c8f19ae, 0x3c90dcfd, 0x3c906c29, 0x3c93d387, 0x3c874dd2, 0x3c8cb301,
 0x3c862ca0, 0x3c84831c, 0x3c883375, 0x3c848edb, 0x3c7c2580, 0x3c7c6251, 0x3c76fcb7, 0x3c786933,
 0x3c6b93cd, 0x3c833ee4, 0x3c7c059f, 0x3c735562, 0x3c6cd4aa, 0x3c62a074, 0x3c725deb, 0x3c675c9b,
 0x3c8defe2, 0x3c9b12ab, 0x3ca2d00f, 0x3ca454de, 0x3c992535, 0x3c94a000, 0x3c8eb108, 0x3c9029ae,
 0x3c968fc4, 0x3c933d29, 0x3c946222, 0x3c8b62d5, 0x3c8ad9ae, 0x3c8901d2, 0x3c882492, 0x3c93caee,
 0x3c80fba9, 0x3c810010, 0x3c83d394, 0x3c801baf, 0x3c77065c, 0x3c7d18c5, 0x3c722186, 0x3c82359a,
 0x3c8099b9, 0x3c72cf96, 0x3c6a7702, 0x3c75cbca, 0x3c69dec1, 0x3c6f1928, 0x3c5c1fbc, 0x3c5d305d,
 0x3c8a5086, 0x3c9dd4fd, 0x3c9eeea3, 0x3c9cd127, 0x3c970494, 0x3c9a39d3, 0x3c944b7c, 0x3c97d4d3,
 0x3c8b5fb0, 0x3c8e7a4c, 0x3c8bd704, 0x3c8b15de, 0x3c84e110, 0x3c86dfee, 0x3c8328a9, 0x3c871aa6,
 0x3c807aaf, 0x3c79de7e, 0x3c8ef4c5, 0x3c86b73f, 0x3c7a871a, 0x3c7baa9b, 0x3c7a5028, 0x3c8149ac,
 0x3c68d039, 0x3c72e2e1, 0x3c6fc951, 0x3c669dc4, 0x3c5fb217, 0x3c6db4a4, 0x3c595506, 0x3c625b3f,
 0x3c8e19d4, 0x3c9fade6, 0x3c955384, 0x3c9c4ff8, 0x3c9318e1, 0x3c961805, 0x3c8cf978, 0x3c940612,
 0x3c91598f, 0x3c9127a5, 0x3c909361, 0x3c8ef5d2, 0x3c87bbb6, 0x3c84d18b, 0x3c824844, 0x3c89949f,
 0x3c830aac, 0x3c8c7c45, 0x3c7a4c62, 0x3c7a25cb, 0x3c7fc90e, 0x3c793f1c, 0x3c80460b, 0x3c74411a,
 0x3c852172, 0x3c6ecda9, 0x3c77ede3, 0x3c63d592, 0x3c6d6b3d, 0x3c5c11e4, 0x3c69ddea, 0x3c629d84,
 0x3c879520, 0x3c9e646f, 0x3c9de44c, 0x3ca4e238, 0x3c962033, 0x3c970718, 0x3c97689d, 0x3c8ce083,
 0x3c95bab2, 0x3c914644, 0x3c8b9d8d, 0x3c8ce44a, 0x3c8cceb0, 0x3c8a4aa7, 0x3c838c7c, 0x3c83c18b,
 0x3c88e8dd, 0x3c8ced84, 0x3c7bbfff, 0x3c78f839, 0x3c916b98, 0x3c7a6811, 0x3c73eda3, 0x3c736c74,
 0x3c754e61, 0x3c700fc8, 0x3c727bb3, 0x3c6ddb3a, 0x3c617c87, 0x3c8541be, 0x3c5ae163, 0x3c6089ae,
 0x3c8585b1, 0x3c99d2a4, 0x3c93d4ff, 0x3c8d9d42, 0x3c96a70b, 0x3c95f387, 0x3c8e5273, 0x3ca07a29,
 0x3c86173c, 0x3c9434a0, 0x3c84728b, 0x3c83855b, 0x3c8bdcad, 0x3c7edc7f, 0x3c7e68bc, 0x3c82dc53,
 0x3c8384ba, 0x3c78fc6b, 0x3c8849e6, 0x3c6dffb8, 0x3c775968, 0x3c7349a4, 0x3c79dcd0, 0x3c62bae0,
 0x3c668357, 0x3c6513eb, 0x3c6a2aac, 0x3c650324, 0x3c718d76, 0x3c5d0310, 0x3c54e1da, 0x3c5967e6,
 0x3c84c2dd, 0x3c98f748, 0x3c8e7727, 0x3c99f7c3, 0x3c8b9b3f, 0x3c88bd08, 0x3c8c5eb3, 0x3c8b7cd6,
 0x3c88fb1c, 0x3c8e60b6, 0x3c84717f, 0x3c85c1ab, 0x3c82447e, 0x3c8b7473, 0x3c800c60, 0x3c7807e4,
 0x3c7d3621, 0x3c6ebce2, 0x3c78c50e, 0x3c666fa1, 0x3c6736db, 0x3c66e0e0, 0x3c701d34, 0x3c8c38be,
 0x3c5ec8e3, 0x3c5aff2a, 0x3c6cd3d3, 0x3c5b390c, 0x3c5f12b5, 0x3c5885d3, 0x3c5d9fee, 0x3c54f2a1,
 0x3c7fd9d5, 0x3c91697f, 0x3c9538e2, 0x3c87efee, 0x3c8fc5db, 0x3c86fac6, 0x3c885dd2, 0x3c81882b,
 0x3c836e4a, 0x3c86b195, 0x3c86efa9, 0x3c813f9b, 0x3c82a4c0, 0x3c7fa4fc, 0x3c858eeb, 0x3c707040,
 0x3c7387b7, 0x3c6f529e, 0x3c6e6751, 0x3c6839a5, 0x3c6ab515, 0x3c680b82, 0x3c667367, 0x3c6165e1,
 0x3c5c16ed, 0x3c56a4bd, 0x3c67ec78, 0x3c5a4dbf, 0x3c56d4f9, 0x3c522bb1, 0x3c56e123, 0x3c4d3466,
 0x3c82c0a5, 0x3c8bb4d5, 0x3c8b5c8a, 0x3c8b4e7d, 0x3c8da753, 0x3c848b15, 0x3c87a1b5, 0x3c829e76,
 0x3c891e8d, 0x3c8424bd, 0x3c84de8c, 0x3c83844f, 0x3c7faf78, 0x3c71e307, 0x3c793b55, 0x3c6f8777,
 0x3c6d923f, 0x3c8b5137, 0x3c729b28, 0x3c7371e8, 0x3c6daec5, 0x3c6729db, 0x3c68a71e, 0x3c70ccf2,
 0x3c70bd6d, 0x3c6825ef, 0x3c7064ed, 0x3c548b73, 0x3c5bfe99, 0x3c5f1108, 0x3c65cfd3, 0x3c4e732b,
 0x3c86b4f0, 0x3c92f505, 0x3c8eff42, 0x3c922bb1, 0x3c8cb479, 0x3c895f5a, 0x3c98ad40, 0x3c884bff,
 0x3c85d5cd, 0x3c812cf1, 0x3c87093e, 0x3c82ac83, 0x3c7e87c5, 0x3c7e3880, 0x3c760c61, 0x3c7dd9b5,
 0x3c83f2d4, 0x3c694904, 0x3c6a5000, 0x3c663ef9, 0x3c6a8544, 0x3c6da87a, 0x3c56d422, 0x3c6c06b9,
 0x3c73ea48, 0x3c60814a, 0x3c5d33b8, 0x3c56e986, 0x3c6604ac, 0x3c59e2ca, 0x3c536f13, 0x3c5e3bf5,
 0x3c721d54, 0x3c914f48, 0x3c8c7d87, 0x3c89c2f7, 0x3c86f700, 0x3c869ddf, 0x3c8887f9, 0x3c81bff4,
 0x3c8728b3, 0x3c87e71f, 0x3c815a08, 0x3c7c002c, 0x3c751fd2, 0x3c6e57cc, 0x3c77fd68, 0x3c822000,
 0x3c758cdf, 0x3c75e6a2, 0x3c6a3ece, 0x3c681883, 0x3c65c704, 0x3c62f167, 0x3c61353a, 0x3c60f005,
 0x3c68335b, 0x3c7549c4, 0x3c5e0feb, 0x3c5b99ef, 0x3c54bbaf, 0x3c532bf7, 0x3c56fc66, 0x3c5a54e0,
 0x3c7bd19d, 0x3c8651f4, 0x3c86d5a7, 0x3c8bfd9a, 0x3c81dc43, 0x3c80cce4, 0x3c8fd1cf, 0x3c867c51,
 0x3c8540b2, 0x3c7e31ca, 0x3c7e817b, 0x3c734069, 0x3c970429, 0x3c6e6d9c, 0x3c6c82e0, 0x3c76010e,
 0x3c6e6249, 0x3c6f1059, 0x3c6459b1, 0x3c623c35, 0x3c6be313, 0x3c67e267, 0x3c70a07c, 0x3c5c2b0f,
 0x3c560e95, 0x3c660ebd, 0x3c52d1ca, 0x3c58b1dd, 0x3c56f9e2, 0x3c512811, 0x3c4cac16, 0x3c45acb2,
 0x3c6e8de8, 0x3c8aa89b, 0x3c86ef3d, 0x3c85d5cd, 0x3c886021, 0x3c9ed726, 0x3c84915f, 0x3c81ebc8,
 0x3c8c1c6e, 0x3c7cf158, 0x3c83a42f, 0x3c71ed83, 0x3c71e307, 0x3c7c0d98, 0x3c6a15b3, 0x3c756fef,
 0x3c6f8fdb, 0x3c6ab0e3, 0x3c6fddde, 0x3c62888b, 0x3c62e9da, 0x3c6d626f, 0x3c61df18, 0x3c5ada41,
 0x3c53798f, 0x3c55ba47, 0x3c55446b, 0x3c53b3dc, 0x3c5bc15c, 0x3c61b669, 0x3c45dcef, 0x3c5a286b,
 0x3c8343b7, 0x3c896b84, 0x3c863ea9, 0x3c85465b, 0x3c8aaf51, 0x3c813d82, 0x3c77cb7e, 0x3c82388a,
 0x3c738be9, 0x3c7b2e75, 0x3c71a204, 0x3c705060, 0x3c7cdd36, 0x3c6a5f19, 0x3c718365, 0x3c6b412c,
 0x3c65b34e, 0x3c6be597, 0x3c6d3bd8, 0x3c6b0e00, 0x3c60a98e, 0x3c607018, 0x3c5d2c96, 0x3c648917,
 0x3c53bded, 0x3c4d7c8b, 0x3c4a6eb9, 0x3c57914c, 0x3c60cc5e, 0x3c5153b0, 0x3c4d76ab, 0x3c4ce018,
 0x3c82ff59, 0x3c8445e0, 0x3c8944ed, 0x3c826adf, 0x3c856be5, 0x3c804535, 0x3c7b593d, 0x3c7a15db,
 0x3c6f44c7, 0x3c6b04c6, 0x3c7c76df, 0x3c611acd, 0x3c67197f, 0x3c6966cc, 0x3c5d9fee, 0x3c63596c,
 0x3c788766, 0x3c53c6bc, 0x3c5f2742, 0x3c5a81c1, 0x3c72cf96, 0x3c502d3f, 0x3c634eef, 0x3c50c43e,
 0x3c4d6f1f, 0x3c4d9183, 0x3c524689, 0x3c5c6343, 0x3c55a767, 0x3c4d46db, 0x3c5c7765, 0x3c4646a1,
 0x3c855758, 0x3c80a6b9, 0x3c8487ba, 0x3c829bbc, 0x3c8dec52, 0x3c846a93, 0x3c7d1138, 0x3c767324,
 0x3c756217, 0x3c77f85f, 0x3c6aa4ba, 0x3c70eb90, 0x3c7467b1, 0x3c6d78a9, 0x3c7057ec, 0x3c60f868,
 0x3c657fb7, 0x3c5c42f7, 0x3c56b6c6, 0x3c60dcba, 0x3c53ea63, 0x3c5e0788, 0x3c6248ca, 0x3c4f71c3,
 0x3c564b66, 0x3c4cd8f7, 0x3c46470c, 0x3c51d4df, 0x3c4613e1, 0x3c44885b, 0x3c45dbac, 0x3c651dfc,
 0x3c650177, 0x3c753826, 0x3c7cc8a8, 0x3c7f7602, 0x3c7ab904, 0x3c735b41, 0x3c76e829, 0x3c7bd711,
 0x3c7e8b20, 0x3c6abe4f, 0x3c7318fc, 0x3c5ff88e, 0x3c6205af, 0x3c70797a, 0x3c606cbd, 0x3c6a8255,
 0x3c72bb08, 0x3c5c329b, 0x3c60a98e, 0x3c67323e, 0x3c502daa, 0x3c512f9d, 0x3c648cdd, 0x3c5d194b,
 0x3c51e109, 0x3c539904, 0x3c472dbc, 0x3c4bc0c9, 0x3c607952, 0x3c4666ed, 0x3c3e751b, 0x3c413db8,
 0x3c575a5a, 0x3c7b58d1, 0x3c81adeb, 0x3c788ef2, 0x3c79b8be, 0x3c76a0dc, 0x3c6a1cd4, 0x3c787dc0,
 0x3c7f653b, 0x3c6fc230, 0x3c62e9da, 0x3c6a35ff, 0x3c629b00, 0x3c74751d, 0x3c65427a, 0x3c5b6f92,
 0x3c64c0df, 0x3c5c5495, 0x3c70a94b, 0x3c50a966, 0x3c579af2, 0x3c4da67c, 0x3c549d7c, 0x3c481400,
 0x3c45890c, 0x3c4e36c5, 0x3c44f715, 0x3c55ca37, 0x3c4b223d, 0x3c401747, 0x3c409ec1, 0x3c4472f7,
 0x3c5fe17d, 0x3c8ab8f6, 0x3c77e366, 0x3c7dc37a, 0x3c80771e, 0x3c6dfb86, 0x3c6bc68d, 0x3c6cedd5,
 0x3c6a7d4c, 0x3c67b6c8, 0x3c71418c, 0x3c6acf82, 0x3c5b078e, 0x3c551fed, 0x3c5970b5, 0x3c5c1ee5,
 0x3c51a1b3, 0x3c5d2d6d, 0x3c543790, 0x3c55ed72, 0x3c65796c, 0x3c4ed550, 0x3c4853c1, 0x3c45366b,
 0x3c454de8, 0x3c58127b, 0x3c4b64ee, 0x3c41c1d7, 0x3c5bc1c8, 0x3c3e55a6, 0x3c39d5e5, 0x3c434565,
 0x3c66be10, 0x3c849922, 0x3c8101f3, 0x3c7f935e, 0x3c6f1562, 0x3c684209, 0x3c6f6223, 0x3c75b6d1,
 0x3c76a070, 0x3c6f2e8c, 0x3c6894a9, 0x3c5d179e, 0x3c68d1e6, 0x3c4e2669, 0x3c5d65a1, 0x3c5bd9b0,
 0x3c5ae4be, 0x3c5ea7c1, 0x3c5c53be, 0x3c5487ac, 0x3c573f17, 0x3c517cca, 0x3c4d071a, 0x3c49c398,
 0x3c4be2c2, 0x3c4cd8f7, 0x3c466ccc, 0x3c37a65f, 0x3c3ff914, 0x3c4139f2, 0x3c43a2ed, 0x3c3e42c6,
 0x3c5b2fd1, 0x3c78ec10, 0x3c81f213, 0x3c6f4674, 0x3c76485c, 0x3c71857e, 0x3c7877e1, 0x3c65fd1f,
 0x3c6717d2, 0x3c695b0d, 0x3c6be52b, 0x3c625c16, 0x3c55bf4f, 0x3c6134ce, 0x3c57bb3d, 0x3c85d024,
 0x3c5d70f4, 0x3c522b46, 0x3c57a71b, 0x3c54b77d, 0x3c585f3d, 0x3c4b06fa, 0x3c5c7765, 0x3c537dc1,
 0x3c446c41, 0x3c41e3d0, 0x3c3748d7, 0x3c450706, 0x3c3f5d79, 0x3c3d01e9, 0x3c3a577f, 0x3c351e5a,
 0x3c658454, 0x3c6d05bd, 0x3c66f644, 0x3c62e898, 0x3c6b2f23, 0x3c63681a, 0x3c6af7c6, 0x3c5effd5,
 0x3c58e509, 0x3c5e254f, 0x3c5a4d53, 0x3c59301d, 0x3c4fe22b, 0x3c5459f4, 0x3c56e844, 0x3c516fca,
 0x3c48e4e1, 0x3c48c42a, 0x3c49b04d, 0x3c4f1364, 0x3c53528d, 0x3c4e003e, 0x3c3e38b5, 0x3c3c6dda,
 0x3c42b303, 0x3c3af244, 0x3c3ed9c5, 0x3c328b6e, 0x3c489f41, 0x3c3ca750, 0x3c371bf6, 0x3c2c6ef4,
 0x3c5b5c47, 0x3c7569a4, 0x3c83f1fd, 0x3c6378e1, 0x3c623eb9, 0x3c6298e7, 0x3c6646f1, 0x3c674c3f,
 0x3c584323, 0x3c734217, 0x3c710526, 0x3c61c369, 0x3c598d3a, 0x3c6afb8c, 0x3c5eb456, 0x3c5a0961,
 0x3c5a3f7c, 0x3c4f14a6, 0x3c4a7bba, 0x3c6a63b7, 0x3c421ef4, 0x3c4b7407, 0x3c52e8db, 0x3c4c1ebd,
 0x3c401091, 0x3c3a82b3, 0x3c417b60, 0x3c4e29c4, 0x3c41813f, 0x3c42cd04, 0x3c40ad6f, 0x3c3ae32b,
 0x3c528573, 0x3c7a545a, 0x3c72027c, 0x3c647560, 0x3c68504b, 0x3c6deb2b, 0x3c6ab580, 0x3c763795,
 0x3c6871d9, 0x3c57a27e, 0x3c58d9b6, 0x3c5bf55f, 0x3c608008, 0x3c4d9a52, 0x3c4f3e2c, 0x3c5a865e,
 0x3c555f43, 0x3c4c1af6, 0x3c53f4df, 0x3c525bed, 0x3c55fc8c, 0x3c3b3f06, 0x3c48a58b, 0x3c3dff3f,
 0x3c3edb73, 0x3c43fdf1, 0x3c35dedf, 0x3c5c84d1, 0x3c3a4a13, 0x3c44899d, 0x3c3db6af, 0x3c37dba4,
 0x3c5fa79b, 0x3c6499de, 0x3c64b5f8, 0x3c65f527, 0x3c62386f, 0x3c68bc82, 0x3c6dcadf, 0x3c58523c,
 0x3c60440d, 0x3c4e1750, 0x3c4a3832, 0x3c56c432, 0x3c501190, 0x3c5bcf34, 0x3c4f7737, 0x3c674ded,
 0x3c51c55a, 0x3c53cd72, 0x3c509833, 0x3c4df480, 0x3c48981f, 0x3c3ec38a, 0x3c3dd624, 0x3c411ff0,
 0x3c39b599, 0x3c3c932e, 0x3c30cbe5, 0x3c3645a2, 0x3c2ff4ba, 0x3c387379, 0x3c2cfd24, 0x3c2f1023,
 0x3c55ef20, 0x3c6cfb40, 0x3c60eafc, 0x3c694fba, 0x3c60758c, 0x3c5ec9ba, 0x3c625c81, 0x3c5becfb,
 0x3c55b82e, 0x3c545d4f, 0x3c5eba35, 0x3c505441, 0x3c4ba733, 0x3c4ca41e, 0x3c4a29f0, 0x3c649038,
 0x3c5b2413, 0x3c564ec1, 0x3c4a1131, 0x3c4ce87b, 0x3c43efae, 0x3c3fa8f8, 0x3c524bfd, 0x3c3b4e1f,
 0x3c3cda11, 0x3c4ec850, 0x3c4101bd, 0x3c3fd2e9, 0x3c3742f8, 0x3c41fc24, 0x3c362703, 0x3c2bd571,
 0x3c5ee569, 0x3c83126f, 0x3c674158, 0x3c65738d, 0x3c5e5017, 0x3c6452fb, 0x3c648627, 0x3c552927,
 0x3c7578be, 0x3c5bc233, 0x3c68e531, 0x3c4f9061, 0x3c5f3e54, 0x3c4accad, 0x3c59b0e1, 0x3c53a9cb,
 0x3c5131b6, 0x3c4a5cb0, 0x3c48486e, 0x3c506fef, 0x3c433b54, 0x3c42a4c0, 0x3c41362b, 0x3c43a0d4,
 0x3c4ff434, 0x3c36f778, 0x3c438e60, 0x3c3b86bf, 0x3c3c0e39, 0x3c3c9c69, 0x3c3f0132, 0x3c2ee4f0,
 0x3c52ae23, 0x3c5ace17, 0x3c620da7, 0x3c57914c, 0x3c58f7e9, 0x3c4a86a1, 0x3c5c5210, 0x3c4c590a,
 0x3c5405a6, 0x3c507a6c, 0x3c5985ad, 0x3c5311f6, 0x3c59eef4, 0x3c43bd5a, 0x3c47f48b, 0x3c3fc4a7,
 0x3c36ff71, 0x3c3c66b9, 0x3c400d36, 0x3c3ac853, 0x3c3f9683, 0x3c3ad411, 0x3c3be29a, 0x3c2ef043,
 0x3c31fe80, 0x3c30e2f7, 0x3c3a182a, 0x3c48b28c, 0x3c3ca38a, 0x3c281202, 0x3c5ebb77, 0x3c2a6960,
 0x3c3f4d1d, 0x3c7b1c6b, 0x3c5d392c, 0x3c60dc4e, 0x3c67e415, 0x3c633fd6, 0x3c482964, 0x3c68f73a,
 0x3c5167d2, 0x3c4d3177, 0x3c535075, 0x3c502115, 0x3c550368, 0x3c48e39f, 0x3c481111, 0x3c4cbef5,
 0x3c41bab5, 0x3c3c0783, 0x3c49ed1e, 0x3c44aa54, 0x3c55c3ec, 0x3c470a81, 0x3c31a66b, 0x3c3573eb,
 0x3c2b876d, 0x3c2cd9e8, 0x3c3aa2ff, 0x3c36f926, 0x3c2e9616, 0x3c3037d6, 0x3c433509, 0x3c25e99f,
 0x3c5a3adf, 0x3c69da24, 0x3c59d7e3, 0x3c5b86a4, 0x3c5ea24d, 0x3c540bf1, 0x3c5c2961, 0x3c58cc4a,
 0x3c4a8926, 0x3c4678f6, 0x3c480cdf, 0x3c501e91, 0x3c506f84, 0x3c468aff, 0x3c42cd70, 0x3c44b3fa,
 0x3c390bba, 0x3c369ef8, 0x3c398776, 0x3c3ab431, 0x3c36bed9, 0x3c3ede62, 0x3c45be50, 0x3c404f7b,
 0x3c345939, 0x3c2f1dfb, 0x3c333ac0, 0x3c31098d, 0x3c3b3dc4, 0x3c339fd5, 0x3c316568, 0x3c318040,
 0x3c740aff, 0x3c66e6bf, 0x3c4f41f2, 0x3c4e802b, 0x3c47b388, 0x3c508a5c, 0x3c424082, 0x3c4ff434,
 0x3c4a3bf9, 0x3c4394aa, 0x3c3cb30f, 0x3c5294f8, 0x3c414ab9, 0x3c38bce0, 0x3c46e598, 0x3c4481a5,
 0x3c33d65c, 0x3c349675, 0x3c38d7b8, 0x3c3c3464, 0x3c2cc195, 0x3c3574c1, 0x3c3a7e81, 0x3c35f4af,
 0x3c354c12, 0x3c270883, 0x3c25cf32, 0x3c2512df, 0x3c3b25db, 0x3c242579, 0x3c2536f1, 0x3c44b323,
 0x3c43c0b5, 0x3c5259d5, 0x3c63beec, 0x3c645c36, 0x3c559e2d, 0x3c5ffb12, 0x3c5c0915, 0x3c4a1639,
 0x3c5e9913, 0x3c54690e, 0x3c4d72e5, 0x3c4828f9, 0x3c48612d, 0x3c3f3cc1, 0x3c43d17c, 0x3c46fd15,
 0x3c384b36, 0x3c35ccd6, 0x3c3d41aa, 0x3c3ec3f6, 0x3c3f70c4, 0x3c408cb8, 0x3c3f840f, 0x3c494848,
 0x3c418216, 0x3c348b22, 0x3c2e9d37, 0x3c368a6b, 0x3c37cdcd, 0x3c4f9dcd, 0x3c4ded5e, 0x3c35e1cf,
 0x3c44ab2b, 0x3c6128a4, 0x3c5170a0, 0x3c59bffa, 0x3c620401, 0x3c458e14, 0x3c598a4a, 0x3c43c983,
 0x3c48c932, 0x3c4fa9f7, 0x3c550225, 0x3c49a5d1, 0x3c401316, 0x3c4383e3, 0x3c3ab9a5, 0x3c41d3e0,
 0x3c454c3a, 0x3c46a13a, 0x3c3acfe0, 0x3c3c1128, 0x3c3b38bb, 0x3c300a1e, 0x3c34fe7a, 0x3c3b411f,
 0x3c2fbf76, 0x3c4463dd, 0x3c333475, 0x3c329797, 0x3c2fc3a8, 0x3c2764c9, 0x3c22a049, 0x3c227bc0,
 0x3c46f446, 0x3c42e705, 0x3c4a2db6, 0x3c4068a6, 0x3c5081f9, 0x3c52164d, 0x3c4d95b5, 0x3c4bbc2c,
 0x3c375501, 0x3c3f4590, 0x3c355ef2, 0x3c3c17de, 0x3c3a04df, 0x3c36be6e, 0x3c4393d4, 0x3c37c7ed,
 0x3c36ca97, 0x3c311daf, 0x3c2d9a6d, 0x3c22c7b6, 0x3c371ab4, 0x3c2f0b1b, 0x3c22d9df, 0x3c2ed2e7,
 0x3c1fa7db, 0x3c31cd02, 0x3c2d4548, 0x3c22dd45, 0x3c27674d, 0x3c31f7ca, 0x3c193530, 0x3c1ab4ac,
 0x3c463c24, 0x3c6096ae, 0x3c6c5ece, 0x3c4c3273, 0x3c5083a6, 0x3c470004, 0x3c5045fe, 0x3c4c7fa0,
 0x3c3fe55e, 0x3c387817, 0x3c3fbec7, 0x3c54b422, 0x3c3b3992, 0x3c580eb5, 0x3c3d1d98, 0x3c37a3db,
 0x3c37bbc4, 0x3c4cdaa4, 0x3c3185b4, 0x3c4599d3, 0x3c317192, 0x3c363b91, 0x3c351662, 0x3c34c9a1,
 0x3c33baad, 0x3c4cb3a2, 0x3c291f49, 0x3c2db0a8, 0x3c2bf5bd, 0x3c2f4858, 0x3c287493, 0x3c240090,
 0x3c390789, 0x3c4d1c13, 0x3c4c14ac, 0x3c44270c, 0x3c3f6d69, 0x3c44c7b0, 0x3c47d582, 0x3c7627a5,
 0x3c3cfa5c, 0x3c3e2d62, 0x3c43f0f1, 0x3c44b5a7, 0x3c4477ff, 0x3c41871e, 0x3c3600d8, 0x3c34091c,
 0x3c2d9854, 0x3c35c549, 0x3c344806, 0x3c2a12f9, 0x3c2c82aa, 0x3c33537f, 0x3c263bd4, 0x3c2922a4,
 0x3c26d04e, 0x3c366293, 0x3c276c56, 0x3c2bee30, 0x3c2257f9, 0x3c17fd0c, 0x3c3139c9, 0x3c1c393c,
 0x3c330f8c, 0x3c46ca54, 0x3c3f2eea, 0x3c460e01, 0x3c3790fb, 0x3c36904a, 0x3c333333, 0x3c3e8287,
 0x3c37fd32, 0x3c2dd378, 0x3c3dc12c, 0x3c2e7203, 0x3c28d2f3, 0x3c298c56, 0x3c3ad91a, 0x3c22c196,
 0x3c32d392, 0x3c228ce8, 0x3c543bc1, 0x3c1ec4d2, 0x3c248ca7, 0x3c299b04, 0x3c1eb1b1, 0x3c28310c,
 0x3c208444, 0x3c2669f7, 0x3c1dbb07, 0x3c196292, 0x3c1fa044, 0x3c259337, 0x3c0f040a, 0x3c207af5,
 0x3c62a795, 0x3c55b3fc, 0x3c33127c, 0x3c383d5e, 0x3c3ed238, 0x3c39dc9b, 0x3c2ffd1e, 0x3c35fbd0,
 0x3c3a9ecd, 0x3c2e53d0, 0x3c4d0cf9, 0x3c2dfc93, 0x3c26ee16, 0x3c29cb40, 0x3c673b0d, 0x3c356c5e,
 0x3c33340a, 0x3c281f03, 0x3c2d6cb5, 0x3c348401, 0x3c24befc, 0x3c17ea2d, 0x3c23fbf3, 0x3c262315,
 0x3c1773a5, 0x3c1e22c0, 0x3c208dd5, 0x3c1e99e9, 0x3c176232, 0x3c1a21b5, 0x3c17142e, 0x3c17b137,
 0x3c372b7b, 0x3c3e5901, 0x3c3f8338, 0x3c3419e3, 0x3c4bc134, 0x3c357ed2, 0x3c2b2e17, 0x3c38320b,
 0x3c36e5db, 0x3c317192, 0x3c37c782, 0x3c25f55d, 0x3c367939, 0x3c229dcf, 0x3c352d74, 0x3c3af16e,
 0x3c2846dc, 0x3c409ec1, 0x3c2974d9, 0x3c21e293, 0x3c23da65, 0x3c219bfc, 0x3c23014c, 0x3c1aff40,
 0x3c3833b9, 0x3c1bc3c0, 0x3c25a3fe, 0x3c27348d, 0x3c2a5391, 0x3c1e31af, 0x3c16a2c4, 0x3c15151b,
 0x3c4edb2f, 0x3c494772, 0x3c383fe2, 0x3c3efa7c, 0x3c4cbc71, 0x3c46f80c, 0x3c393c62, 0x3c448429,
 0x3c3c4fa7, 0x3c3b7374, 0x3c2b6509, 0x3c38aad7, 0x3c2a8438, 0x3c2d8432, 0x3c27b76a, 0x3c34861a,
 0x3c309c80, 0x3c2b169a, 0x3c2477af, 0x3c28621f, 0x3c2486c8, 0x3c22fb6d, 0x3c1f4515, 0x3c213b24,
 0x3c25d222, 0x3c234b94, 0x3c20baeb, 0x3c200aad, 0x3c1a6821, 0x3c194952, 0x3c1a3226, 0x3c219363,
 0x3c496610, 0x3c37dba4, 0x3c34f759, 0x3c367f18, 0x3c2c49a0, 0x3c448bb6, 0x3c2ca072, 0x3c2ed3be,
 0x3c2c4213, 0x3c286e49, 0x3c2bd5dc, 0x3c2c089d, 0x3c2a285d, 0x3c2fa35c, 0x3c25b74a, 0x3c22b1a6,
 0x3c2aed14, 0x3c2f7030, 0x3c1fde0c, 0x3c1e2336, 0x3c239a03, 0x3c263589, 0x3c1c91c7, 0x3c1985ad,
 0x3c19a02f, 0x3c19da92, 0x3c166897, 0x3c0f85c4, 0x3c1bb116, 0x3c0d3e01, 0x3c1429ae, 0x3c172907,
 0x3c482be9, 0x3c4571fa, 0x3c36460d, 0x3c35016a, 0x3c2ab2c7, 0x3c2c94b4, 0x3c2b95b0, 0x3c34605a,
 0x3c552fdd, 0x3c236de4, 0x3c24e4bc, 0x3c31da6e, 0x3c271de7, 0x3c25e786, 0x3c259c06, 0x3c2369f2,
 0x3c2ec794, 0x3c12d7d4, 0x3c15d905, 0x3c1c0829, 0x3c1bd137, 0x3c1ff365, 0x3c194f31, 0x3c13ae28,
 0x3c16a675, 0x3c1b9f2d, 0x3c18fb84, 0x3c18f03c, 0x3c0f26af, 0x3c160b0f, 0x3c0a0e41, 0x3c0be379,
 0x3c3ae61a, 0x3c2f5706, 0x3c35e23a, 0x3c2f4426, 0x3c475163, 0x3c28e780, 0x3c261bf3, 0x3c248586,
 0x3c2ff0f4, 0x3c281c13, 0x3c3cc5ef, 0x3c2d4116, 0x3c26ffb4, 0x3c227abf, 0x3c1f5cdd, 0x3c24c180,
 0x3c1b5c3c, 0x3c396008, 0x3c1b7c1d, 0x3c3aaf94, 0x3c20374d, 0x3c184531, 0x3c256580, 0x3c1f41e5,
 0x3c18e285, 0x3c206fac, 0x3c19002c, 0x3c0cdc31, 0x3c1148bd, 0x3c18e4b3, 0x3c1fbeed, 0x3c1d9e8c,
 0x3c2510c6, 0x3c37b0dc, 0x3c372962, 0x3c3bd167, 0x3c3042be, 0x3c28fd4f, 0x3c29519e, 0x3c36a975,
 0x3c282c04, 0x3c2e08bc, 0x3c1e76d9, 0x3c29d5bc, 0x3c361c87, 0x3c26e257, 0x3c1f9054, 0x3c2587e4,
 0x3c236a73, 0x3c2cfb0b, 0x3c319a41, 0x3c23e54d, 0x3c22bc78, 0x3c0ef3d9, 0x3c160f81, 0x3c0f9676,
 0x3c1f7363, 0x3c26bbc1, 0x3c153be7, 0x3c20c3c5, 0x3c0d3a50, 0x3c13fa89, 0x3c131378, 0x3c28b2a7,
 0x3c36da1c, 0x3c2b6284, 0x3c372e6a, 0x3c3786ea, 0x3c2c55c9, 0x3c3ce7e8, 0x3c1c092b, 0x3c23cbc2,
 0x3c26bd6f, 0x3c1e4a79, 0x3c2685a6, 0x3c1b782c, 0x3c251a6c, 0x3c3257d7, 0x3c22bd6f, 0x3c1fcb6d,
 0x3c1fa6e4, 0x3c183291, 0x3c254102, 0x3c1450f0, 0x3c1f7363, 0x3c21c664, 0x3c13326d, 0x3c0f1405,
 0x3c21ba5a, 0x3c17c6d1, 0x3c115cdf, 0x3c10313b, 0x3c15ff86, 0x3c164115, 0x3c1ea862, 0x3c18e10d,
 0x3c283970, 0x3c2bf1f6, 0x3c2333cc, 0x3c2d9a6d, 0x3c2fd03d, 0x3c343237, 0x3c27c683, 0x3c28c155,
 0x3c1c8735, 0x3c268ee0, 0x3c248bd1, 0x3c1fea4b, 0x3c21e8b3, 0x3c16b2f5, 0x3c1e22c0, 0x3c23a7ba,
 0x3c219612, 0x3c190f66, 0x3c0e4b07, 0x3c14471f, 0x3c19fbb4, 0x3c1ec7c1, 0x3c0f6d71, 0x3c14d606,
 0x3c110f07, 0x3c11be64, 0x3c0f0aeb, 0x3c18e8e5, 0x3c1a3c37, 0x3c0aa7e4, 0x3c0aa4f5, 0x3bf729d8,
 0x3c23bb91, 0x3c271ca4, 0x3c2f7f4a, 0x3c3843a9, 0x3c2b39d5, 0x3c2be927, 0x3c2932ff, 0x3c34d55f,
 0x3c3b7953, 0x3c23edb0, 0x3c20c53d, 0x3c1ee9db, 0x3c3f1c76, 0x3c1c3e65, 0x3c1e083e, 0x3c2e49bf,
 0x3c2101c3, 0x3c27ef9e, 0x3c2507f7, 0x3c0edf36, 0x3c1d553b, 0x3c220cfb, 0x3c18d6bc, 0x3c151363,
 0x3c11000d, 0x3c144760, 0x3c0ffce3, 0x3c106162, 0x3c0bf7a5, 0x3c151c3c, 0x3c11d76e, 0x3c093243,
 0x3c607449, 0x3c2989d2, 0x3c34f89b, 0x3c2dd6d3, 0x3c22b516, 0x3c284091, 0x3c2b0c1d, 0x3c2c9d17,
 0x3c1ba6c5, 0x3c227759, 0x3c2a5391, 0x3c333044, 0x3c188dd6, 0x3c2d9a6d, 0x3c1a23ee, 0x3c202fec,
 0x3c208155, 0x3c1c50c4, 0x3c106218, 0x3c0f84c3, 0x3c175f83, 0x3c112dfb, 0x3c13aa82, 0x3c1a6668,
 0x3c186bbc, 0x3c17fc15, 0x3c23bf02, 0x3c17928e, 0x3c0c8d4c, 0x3c0e72d5, 0x3c12fe5f, 0x3c0a9e54,
 0x3c2dff17, 0x3c2dbc66, 0x3c241bd4, 0x3c2023ac, 0x3c205bd6, 0x3c2e627e, 0x3c1c07e9, 0x3c24a05e,
 0x3c24a34d, 0x3c13ea8e, 0x3c1f784b, 0x3c285c40, 0x3c2e67f2, 0x3c17bf39, 0x3c105fdf, 0x3c112790,
 0x3c0b52fa, 0x3c1b0e39, 0x3c0d48d3, 0x3c1fa4ec, 0x3c115cdf, 0x3c1156bf, 0x3c124009, 0x3c1aa014,
 0x3c146263, 0x3c12a244, 0x3c132003, 0x3c1229ae, 0x3c06c4ab, 0x3c14308f, 0x3c024a93, 0x3c00a415,
 0x3c398054, 0x3c2d62a4, 0x3c33566f, 0x3c1cc92f, 0x3c22d766, 0x3c2166c3, 0x3c166278, 0x3c224f60,
 0x3c20e55e, 0x3c1eb5e3, 0x3c160b4f, 0x3c1ddf0e, 0x3c1191ee, 0x3c28b66d, 0x3c150230, 0x3c141414,
 0x3c0a9f4b, 0x3c0ddaff, 0x3c14a990, 0x3c0f2f07, 0x3c0bd2c7, 0x3c145dfb, 0x3c1adb6e, 0x3c0d97ae,
 0x3c131729, 0x3c153aaf, 0x3c047d3d, 0x3c08f63e, 0x3c05f2de, 0x3c01828c, 0x3c0f0581, 0x3c0ab46f,
 0x3c33df96, 0x3c17dc2a, 0x3c244920, 0x3c1600fe, 0x3c221412, 0x3c116481, 0x3c216743, 0x3c12bad8,
 0x3c1fbeed, 0x3c29746e, 0x3c15cf2a, 0x3c13b58a, 0x3c15cc7b, 0x3c16ca06, 0x3c0ad3e4, 0x3c2d051c,
 0x3c123110, 0x3c015ae9, 0x3c1420d4, 0x3c071299, 0x3c0ee9fe, 0x3c09a880, 0x3c055a1d, 0x3c023f80,
 0x3c0527b2, 0x3c03f33f, 0x3c1770b5, 0x3c0e498f, 0x3c05b4e0, 0x3c0b0cef, 0x3c08b073, 0x3c05e06a,
 0x3c2c9016, 0x3c19a97f, 0x3c1c024a, 0x3c163704, 0x3c1384ed, 0x3c20e78c, 0x3c17c792, 0x3c1b7d14,
 0x3c22c837, 0x3c09333a, 0x3c1f361b, 0x3c145420, 0x3c0e5bc3, 0x3c13e8a0, 0x3c09c9ee, 0x3c332d54,
 0x3c18622c, 0x3c0de49a, 0x3c07a6de, 0x3c102572, 0x3bfdcb32, 0x3c0b974d, 0x3c1ef752, 0x3c091b27,
 0x3c1cf0b1, 0x3c0d46db, 0x3c26b003, 0x3c0300a6, 0x3c071ee3, 0x3c17cfaa, 0x3c0cad83, 0x3bf5ded4,
 0x3c2aac7c, 0x3c2a00f0, 0x3c2c3512, 0x3c20dacc, 0x3c1fda5b, 0x3c18eb13, 0x3c40010c, 0x3c1b26f8,
 0x3c3475be, 0x3c0e536a, 0x3c17bdc2, 0x3c1a98f3, 0x3c1a3226, 0x3c13d3fd, 0x3c149c8f, 0x3c1547b0,
 0x3c0d9b5f, 0x3c070126, 0x3c11a610, 0x3c14d687, 0x3c2e2d3a, 0x3c095fbb, 0x3c1f4d63, 0x3c111c07,
 0x3c05f5ce, 0x3c0acaca, 0x3c076e94, 0x3c1c1179, 0x3c0354c9, 0x3c0434ae, 0x3bffd289, 0x3c01b7b0,
 0x3c31b809, 0x3c2afff3, 0x3c232e6e, 0x3c177c73, 0x3c210823, 0x3c29336b, 0x3c2fd403, 0x3c20f355,
 0x3c23ddc0, 0x3c2ad7af, 0x3c2ef39e, 0x3c23b669, 0x3c1cec4a, 0x3c139d36, 0x3c13b219, 0x3c16ef9b,
 0x3c129442, 0x3c1b3fc2, 0x3c1d1732, 0x3c04456a, 0x3c0fe688, 0x3c21103b, 0x3c05c94d, 0x3c0d68ff,
 0x3c09c92d, 0x3c06fbfd, 0x3c0c0a4f, 0x3c124674, 0x3c0682fc, 0x3c07f166, 0x3c033bb4, 0x3c0a3360,
 0x3c23d491, 0x3c23f53d, 0x3c1adaad, 0x3c1ac3a6, 0x3c1e0e28, 0x3c2810c0, 0x3c190e2e, 0x3c283b1d,
 0x3c22e61f, 0x3c160921, 0x3c130388, 0x3c109a57, 0x3c11103e, 0x3c0e13ff, 0x3c0641ce, 0x3c07bdfa,
 0x3c1510b3, 0x3c11b8fb, 0x3c1d3032, 0x3c0e41f8, 0x3c050da6, 0x3c11febb, 0x3c079dce, 0x3c09df52,
 0x3c08fa70, 0x3c109767, 0x3c03da2a, 0x3bfcc5b9, 0x3c0c9a58, 0x3c00cde6, 0x3bf79827, 0x3c0689dd,
 0x3c2e1cde, 0x3c0a9dd3, 0x3c33e8d0, 0x3c1b53a3, 0x3c2256c2, 0x3c147ca4, 0x3c13994f, 0x3c01193a,
 0x3c18585c, 0x3c0c41a2, 0x3bf9ae82, 0x3c15b4e8, 0x3c15ea2d, 0x3c14fb1a, 0x3c401889, 0x3c044722,
 0x3c001a82, 0x3c1313b9, 0x3c07bc0c, 0x3c03673e, 0x3bf5585c, 0x3c0f29df, 0x3c111a0e, 0x3c0359bc,
 0x3c02f944, 0x3bf39d06, 0x3c01087e, 0x3bf7cb69, 0x3c060d6b, 0x3c025728, 0x3c032cb0, 0x3bee980e,
 0x3c27f796, 0x3c130e50, 0x3c23239c, 0x3c15a3f6, 0x3c1a6dca, 0x3c41644e, 0x3c05bcc3, 0x3c18f4ae,
 0x3c1d4164, 0x3c05157e, 0x3c0af318, 0x3c0f2b57, 0x3c1035e3, 0x3c02a6d9, 0x3c0ab8e1, 0x3c0b3a66,
 0x3c0ba981, 0x3c0837b2, 0x3bf2cb24, 0x3c1aff40, 0x3c003ab8, 0x3c083f14, 0x3c009e2b, 0x3c00511e,
 0x3bfd89ae, 0x3bf2fb75, 0x3c01eb1c, 0x3c017c21, 0x3bf4d555, 0x3c0239a1, 0x3c042239, 0x3c08414d,
 0x3c19922d, 0x3c18c31a, 0x3c08dbb1, 0x3c197d95, 0x3c0c5584, 0x3c0bd43f, 0x3c107b63, 0x3c0f957f,
 0x3c20900e, 0x3c0c2bfe, 0x3c13a650, 0x3c088c0b, 0x3c0c1acb, 0x3c16350b, 0x3c072355, 0x3c025ad9,
 0x3c112a09, 0x3c01320f, 0x3bfc9a70, 0x3c05dc43, 0x3bfb5f47, 0x3bfae539, 0x3bf95165, 0x3c0299ce,
 0x3bee902c, 0x3c02f49c, 0x3bf62739, 0x3c0328bf, 0x3bf026af, 0x3c06a764, 0x3bf94510, 0x3bf5ec2b,
 0x3c1f3753, 0x3c23eac1, 0x3c2bb30c, 0x3c1757eb, 0x3c12f67d, 0x3c0ef9f9, 0x3c08f547, 0x3c0fd4df,
 0x3c16dcb1, 0x3c133b06, 0x3c083441, 0x3c012aad, 0x3c0b21d2, 0x3c096854, 0x3bfd4c9c, 0x3bfe254a,
 0x3c069530, 0x3be682ec, 0x3c01ec94, 0x3c000c75, 0x3bf9670a, 0x3c048a89, 0x3bf19e28, 0x3c0a0470,
 0x3c023cd1, 0x3c059c0c, 0x3bf97f32, 0x3c004abe, 0x3bfa9760, 0x3bd4af19, 0x3be66f35, 0x3becc0f3,
 0x3c0ec951, 0x3c1f9dd5, 0x3c117a5c, 0x3c159015, 0x3c0a818e, 0x3c165f07, 0x3c04e644, 0x3c072e68,
 0x3c0ef800, 0x3c0fb8db, 0x3c08cbb6, 0x3c06c231, 0x3c13a360, 0x3c13c67c, 0x3c31c285, 0x3c01892d,
 0x3c170b95, 0x3c061d66, 0x3c0dd266, 0x3c0d2d85, 0x3bf71d02, 0x3bfa249f, 0x3c0b5f44, 0x3bfa26a2,
 0x3beb4406, 0x3bfe99f9, 0x3c0ac56b, 0x3c0e4e01, 0x3bf6311f, 0x3bf36075, 0x3bedaf05, 0x3bef9d47,
 0x3c330eb6, 0x3c06de36, 0x3c0a9773, 0x3c24baca, 0x3c0ce21b, 0x3c1ca796, 0x3c015e19, 0x3c01bd19,
 0x3c109376, 0x3bf19d26, 0x3c074edf, 0x3c026f46, 0x3c059764, 0x3c17f5b5, 0x3c0052d6, 0x3bf92027,
 0x3c039e1a, 0x3c0c8ade, 0x3c03c1cc, 0x3c04816f, 0x3bed6198, 0x3c011f24, 0x3c16b4ee, 0x3bec3406,
 0x3bfb4444, 0x3bf2f124, 0x3bf89740, 0x3bf61505, 0x3bf18122, 0x3c035e24, 0x3c0d541c, 0x3c023b9a,
 0x3c141703, 0x3c066ecf, 0x3c107503, 0x3c0be282, 0x3c0d1bd2, 0x3c0eab5f, 0x3c147f5e, 0x3c062117,
 0x3c2a1a1a, 0x3c05aec1, 0x3c0560c8, 0x3c043c50, 0x3bfa3855, 0x3c11d76e, 0x3c0e498f, 0x3bf62739,
 0x3bfcc9aa, 0x3bf515d7, 0x3c096fb6, 0x3be7920b, 0x3c004eaf, 0x3c0d5263, 0x3c03d8e8, 0x3c02a272,
 0x3bfe1b64, 0x3bd8b477, 0x3be354e4, 0x3bf19b38, 0x3bf4b55f, 0x3bfa8e11, 0x3c1739b8, 0x3be6283e,
 0x3c20735d, 0x3bf952e8, 0x3c15dee4, 0x3c02072c, 0x3c35f8e0, 0x3c0d246b, 0x3bfecc23, 0x3c0786a7,
 0x3c0c9cc6, 0x3c016444, 0x3c0caefa, 0x3c12901b, 0x3c1f6043, 0x3bef6411, 0x3bfce731, 0x3bf23e4b,
 0x3bfb2728, 0x3c077245, 0x3c0436a6, 0x3bfc7216, 0x3bf83e15, 0x3c0528b4, 0x3c04cfe9, 0x3c00c2d3,
 0x3be8f50c, 0x3beb6bf4, 0x3bf10b5b, 0x3c08b16a, 0x3bea0a0a, 0x3becf145, 0x3bfb3f51, 0x3bf16ae7,
 0x3c136288, 0x3c061645, 0x3c2614d2, 0x3c161efb, 0x3c02bcbe, 0x3c0b91a4, 0x3bfb795e, 0x3c00e77c,
 0x3c0b86d2, 0x3c06a23b, 0x3c07df32, 0x3bf99949, 0x3c059ec6, 0x3bf54a19, 0x3bf21cbd, 0x3bf54339,
 0x3be85f24, 0x3c0ab007, 0x3beebf7b, 0x3bfbaf0d, 0x3bf750c4, 0x3bdb365d, 0x3bf8573f, 0x3bf7f153,
 0x3bed98ca, 0x3be03285, 0x3bfad762, 0x3be4ae15, 0x3bf6ad30, 0x3bde92b2, 0x3bf1820e, 0x3be82111,
 0x3c1a448f, 0x3c07d74f, 0x3c02507d, 0x3bf45c33, 0x3c032ab8, 0x3bff3ee5, 0x3c0f982e, 0x3bfec934,
 0x3be986ed, 0x3c071dec, 0x3bfd8d1f, 0x3c0af318, 0x3bf5cc1f, 0x3beccada, 0x3be74e02, 0x3be07d03,
 0x3c040b5d, 0x3bdeb2be, 0x3c0bf1fc, 0x3c01718f, 0x3be8f9ea, 0x3bee0c22, 0x3bd9f6ac, 0x3bf6c8ca,
 0x3c07ee36, 0x3be063d8, 0x3bcf2847, 0x3bf2dae9, 0x3bee2b2c, 0x3c109d87, 0x3beeecdd, 0x3bfbce17,
 0x3bf29f5a, 0x3c0b5142, 0x3c0368c0, 0x3bfed589, 0x3c071258, 0x3c0965da, 0x3c097697, 0x3bffe14c,
 0x3c0a14ec, 0x3c035021, 0x3bf1fc47, 0x3c16f052, 0x3c03683f, 0x3c0ba2d6, 0x3bfc6d23, 0x3c033122,
 0x3be4ef18, 0x3bfae727, 0x3bfe6f1c, 0x3bf0fa13, 0x3bea987a, 0x3bffd48c, 0x3bd5e20a, 0x3bf5db64,
 0x3bea1dc1, 0x3be28342, 0x3bd06a3b, 0x3beeee4a, 0x3bc67606, 0x3beab906, 0x3bd40fa2, 0x3be11acd,
 0x3c1cf422, 0x3c0379b2, 0x3c07d557, 0x3c05b622, 0x3c0a882f, 0x3c070786, 0x3c0369b7, 0x3c060802,
 0x3c11cc9c, 0x3c051447, 0x3c1fab4c, 0x3bfcfdd8, 0x3bf4c320, 0x3beb6782, 0x3bfb624c, 0x3bf38a51,
 0x3bfb2d1d, 0x3c06c7db, 0x3bf3bd11, 0x3bda43ae, 0x3c0c5be4, 0x3be5b3e4, 0x3c04dee2, 0x3beaa941,
 0x3bf47837, 0x3bed2deb, 0x3bd955dd, 0x3be60b22, 0x3bdea10b, 0x3bf3b0bc, 0x3bd30891, 0x3be6f5c3,
 0x3c10daef, 0x3c05da0a, 0x3bf4850d, 0x3c023f0a, 0x3c000594, 0x3beda22f, 0x3bf18803, 0x3bfda649,
 0x3be668d5, 0x3c00b740, 0x3bf4dc35, 0x3be64c3a, 0x3bf83b25, 0x3bfaffd1, 0x3be1c8dd, 0x3becb12e,
 0x3bf8e605, 0x3be615f4, 0x3bf397a7, 0x3bd956de, 0x3bf1b752, 0x3bf2b7ee, 0x3bfb49a2, 0x3bfa7379,
 0x3bd8a924, 0x3be542fb, 0x3bde7a8a, 0x3be964de, 0x3bc89cd2, 0x3bcf28c8, 0x3be068cb, 0x3be568e5,
 0x3c16d849, 0x3c07463b, 0x3bfd4aae, 0x3beb7935, 0x3bf8087a, 0x3bf4801a, 0x3be3d592, 0x3bfc7013,
 0x3bfc7608, 0x3c06a8dc, 0x3bd62896, 0x3bf273fb, 0x3c0059c2, 0x3c067e54, 0x3bdde29f, 0x3bda61cb,
 0x3be7b9f9, 0x3bdd5789, 0x3bbbc37b, 0x3be57149, 0x3c043dc8, 0x3bddbab1, 0x3be90f23, 0x3be5cefc,
 0x3bcba2ab, 0x3be627bd, 0x3bdbf93b, 0x3bcf57ac, 0x3be7643d, 0x3bfdd604, 0x3bf71b15, 0x3bd9a3cb,
 0x3c18764e, 0x3bff8b26, 0x3c014834, 0x3c04f837, 0x3bee42d4, 0x3c061b78, 0x3c131861, 0x3bebb9cd,
 0x3beb880e, 0x3bd6f8e0, 0x3bec9b09, 0x3bf8de23, 0x3c0242fb, 0x3bd89a60, 0x3bf8d8c4, 0x3bd396c0,
 0x3be2fe12, 0x3bdfb837, 0x3bf0e6dd, 0x3be6ad49, 0x3be37dd4, 0x3be5581e, 0x3be3c4cb, 0x3bef890f,
 0x3bff85c7, 0x3bf35130, 0x3bc8cea6, 0x3c015387, 0x3bd2f4da, 0x3bdacfc5, 0x3bdc48ab, 0x3bec8d32,
 0x3c0e104f, 0x3c015098, 0x3beaa4cf, 0x3c1b7c5d, 0x3c02fc7f, 0x3be27fd2, 0x3bf8fabe, 0x3c0ae9c9,
 0x3bfd8c32, 0x3beb3e92, 0x3c0378fc, 0x3bdc3763, 0x3bfade43, 0x3be8ed29, 0x3bdc7912, 0x3bee4c39,
 0x3bc46fdc, 0x3bf24f12, 0x3bc203c6, 0x3be40ad7, 0x3bd0ff4c, 0x3be6b72f, 0x3bcd74d3, 0x3bcecd02,
 0x3bd34bac, 0x3bef9d47, 0x3bd86094, 0x3bcd7eb9, 0x3c025728, 0x3bd3cf75, 0x3bd06f2e, 0x3bcfdb8a,
 0x3c12c0f8, 0x3bf9b1f3, 0x3c060a3b, 0x3be7a2d1, 0x3c0a8ae9, 0x3be23ac8, 0x3bd406bd, 0x3bee68be,
 0x3bde24b9, 0x3be4191a, 0x3beb2f4e, 0x3c08d44f, 0x3bcca2f1, 0x3bfb2c9c, 0x3be9caf5, 0x3bddd2da,
 0x3bd1daff, 0x3bfa2b95, 0x3bebdd49, 0x3bbff77c, 0x3bdb6a34, 0x3bcbfa95, 0x3be409d5, 0x3be9b2cc,
 0x3bc4ae9b, 0x3bbd87cb, 0x3bdbc7e7, 0x3bdb6831, 0x3c0cccf8, 0x3bd328b1, 0x3bd13592, 0x3bc6e7b0,
 0x3c25df8e, 0x3c073879, 0x3c05292a, 0x3c0bd8b1, 0x3befc0c3, 0x3bfb6c1d, 0x3c0e5b42, 0x3bf69bfe,
 0x3bf4592e, 0x3c030db1, 0x3bea2914, 0x3bd2d9ac, 0x3bf169fb, 0x3befbf40, 0x3be9da39, 0x3bdcd7c7,
 0x3c0adfad, 0x3bd5b5aa, 0x3bdb9a05, 0x3bdca8e3, 0x3be9c3fe, 0x3bc74d71, 0x3c07e694, 0x3bed6ff0,
 0x3bddc807, 0x3bd788fe, 0x3be43b28, 0x3bf67a05, 0x3be1df18, 0x3bc8e3f5, 0x3bd6b0d1, 0x3bf7a3fb,
 0x3c142445, 0x3bf3da17, 0x3bec731b, 0x3c07f9bf, 0x3befdd48, 0x3be90c33, 0x3bdbc005, 0x3bf15b22,
 0x3bdeb0d0, 0x3bed0bdd, 0x3bd423d9, 0x3bd2ecf7, 0x3be28ba6, 0x3bebe4ab, 0x3bd091a8, 0x3bdf5220,
 0x3be724a7, 0x3bee578c, 0x3bf4114a, 0x3bc72cd0, 0x3bd0b3cd, 0x3be27ee6, 0x3bd3f8fb, 0x3bd821eb,
 0x3be02c91, 0x3bc92f74, 0x3bbc2fc7, 0x3bbf60be, 0x3bcedfb7, 0x3bf8573f, 0x3bd5479b, 0x3bc7aec0,
 0x3c227bc0, 0x3c0056c8, 0x3be70eed, 0x3bde68c1, 0x3be25c56, 0x3bdc1e4e, 0x3be79510, 0x3bf6b036,
 0x3be900e0, 0x3bec60e7, 0x3bd2daae, 0x3beecc51, 0x3bd77b27, 0x3be54c4b, 0x3be0942a, 0x3be4a922,
 0x3bd0f5e7, 0x3bdb0dee, 0x3bcc0bdd, 0x3bdaccd5, 0x3bc3fdb1, 0x3bd7dfd0, 0x3be2469c, 0x3bd98d25,
 0x3bd9574a, 0x3bc7a2d7, 0x3bc899cd, 0x3bc1cb67, 0x3bd2a4d3, 0x3bd0c580, 0x3bcb5239, 0x3bd0d9cd,
 0x3c077535, 0x3bec92a5, 0x3c1f04d3, 0x3bfae539, 0x3be9fd34, 0x3bebb4da, 0x3bcff2b2, 0x3bd4f5a6,
 0x3bda9710, 0x3bd65fde, 0x3be32af3, 0x3bc6c50b, 0x3bd0c906, 0x3bfcde62, 0x3be9d74a, 0x3bcd5243,
 0x3c105888, 0x3bc5f0a5, 0x3bd880b5, 0x3bc77372, 0x3bc38e0a, 0x3bc9e9ae, 0x3bc89cd2, 0x3bd06caa,
 0x3bcc99a1, 0x3bbca7e7, 0x3bc8156d, 0x3bbaa8c8, 0x3bb4e565, 0x3be5552f, 0x3bc1baa0, 0x3bc2505d,
 0x3c03dc18, 0x3bfedf59, 0x3bdda578, 0x3bdbb7a1, 0x3bd9af9f, 0x3be069b8, 0x3c002946, 0x3beccd48,
 0x3be91111, 0x3bd1503f, 0x3bece571, 0x3bc80426, 0x3be20417, 0x3bdbc8e9, 0x3c06f552, 0x3bc7b1b0,
 0x3bdd25b5, 0x3bc22859, 0x3bbfe5b4, 0x3bd93840, 0x3bb90215, 0x3bc3118d, 0x3be8eb26, 0x3c082f59,
 0x3bda958d, 0x3bc97f7b, 0x3bccf668, 0x3bd209e3, 0x3bcfaf96, 0x3bcbae95, 0x3bb870b5, 0x3bd75a9a,
 0x3c06b75f, 0x3bdfbd2a, 0x3bfcfe58, 0x3be58580, 0x3bdd2b95, 0x3be88d07, 0x3be56205, 0x3bebdf37,
 0x3bd7f9fd, 0x3bf19b38, 0x3bbe286f, 0x3bd94e7b, 0x3bd51da9, 0x3bdec116, 0x3bd2b0bc, 0x3bca928b,
 0x3bd059e0, 0x3bdb47a4, 0x3bcd23e0, 0x3bd7c2b5, 0x3bd451d1, 0x3bd496db, 0x3bc988e0, 0x3bd281d8,
 0x3bd96a95, 0x3bcf2bcd, 0x3bd4e06d, 0x3bbe63be, 0x3be8764c, 0x3bbfd75b, 0x3be0a673, 0x3bcdc84a,
 0x3c1846e9, 0x3bdd39ed, 0x3be87dad, 0x3bfa2a93, 0x3bfb1001, 0x3be10ef9, 0x3bda42ac, 0x3bec2b21,
 0x3be307f8, 0x3bc0dcbf, 0x3beb6310, 0x3c04ad39, 0x3bc7477d, 0x3bc1c014, 0x3bcbfd19, 0x3bc26617,
 0x3bc6eca4, 0x3bbee13c, 0x3bd12db0, 0x3bceade3, 0x3bcd5243, 0x3bcaf1ec, 0x3be4d603, 0x3bbb2fc1,
 0x3bb94068, 0x3bcf45f9, 0x3bc9baca, 0x3bcad7c0, 0x3bd6540a, 0x3be478d0, 0x3bd8c8af, 0x3bb91850,
 0x3c0aa184, 0x3bd8e3dc, 0x3c035021, 0x3bc428b9, 0x3c01e6eb, 0x3be4d212, 0x3be67efb, 0x3bca35c4,
 0x3bedb9d7, 0x3bc605f4, 0x3bcd9dd8, 0x3be2164b, 0x3bdefb4e, 0x3bc7dda5, 0x3bc49acf, 0x3bc8d22c,
 0x3bc50b78, 0x3bc476bd, 0x3bc30132, 0x3bc9b8c6, 0x3bd4236e, 0x3ba9fa7a, 0x3bd97adb, 0x3bb90585,
 0x3bc822c4, 0x3bb3bbaf, 0x3bc86778, 0x3bc4a8a6, 0x3bd6781c, 0x3c02a328, 0x3bdb5d5e, 0x3be07aff,
 0x3c0b1b31, 0x3bff1219, 0x3bdd11ff, 0x3bd2470a, 0x3be55b24, 0x3bc5eeb7, 0x3bccc413, 0x3bdbd3bb,
 0x3bcd7655, 0x3bea9ae9, 0x3bc787a9, 0x3bc72dd1, 0x3bc5f329, 0x3bcc92ab, 0x3bd11eec, 0x3bf2c3c2,
 0x3bcf8f09, 0x3bbb38a6, 0x3be0989c, 0x3bbd9e87, 0x3bdfa7f1, 0x3bd612f2, 0x3bc31e63, 0x3bbd2174,
 0x3bc747fe, 0x3bc80898, 0x3bccd4da, 0x3bbe87d0, 0x3bc5d48b, 0x3bcde9d8, 0x3bc0ba2f, 0x3bcc60d7,
 0x3bf43e15, 0x3bb94155, 0x3bc99042, 0x3bdaf153, 0x3bcb01b1, 0x3be86605, 0x3bb9494d, 0x3bce0115,
 0x3bf25e57, 0x3bcb2542, 0x3bcddb15, 0x3bba05a0, 0x3bd6a6eb, 0x3bd60cfd, 0x3bb0b1b9, 0x3bed0009,
 0x3bb79f94, 0x3be38c16, 0x3be0b4b6, 0x3bb6fa7e, 0x3bfc1087, 0x3bd70bab, 0x3ba7c75a, 0x3bb57151,
 0x3bb0f972, 0x3bb56d60, 0x3be00625, 0x3bc687cf, 0x3bba58ac, 0x3bd24a0f, 0x3bc90293, 0x3bb01e56,
 0x3c0dd0ee, 0x3bd23fa8, 0x3bb7952d, 0x3bcbab90, 0x3bf1acec, 0x3bb21856, 0x3bdc1ccc, 0x3bc7918f,
 0x3bcf1d75, 0x3c0cf5fd, 0x3bb1490e, 0x3bb9e8ef, 0x3bb56c5e, 0x3bd158a3, 0x3bb04166, 0x3bb1992a,
 0x3bdd7df5, 0x3bb19d9c, 0x3be96cc1, 0x3bad8473, 0x3bb51f47, 0x3ba7f3e5, 0x3bac638b, 0x3bb6c38c,
 0x3bbb8c1d, 0x3be567e4, 0x3bb31ee6, 0x3bb815c6, 0x3beef23c, 0x3c04fcea, 0x3bd35a86, 0x3bb8c3c1,
 0x3c09c4c5, 0x3bede434, 0x3bbe6d8e, 0x3bd38086, 0x3bc0a663, 0x3ba928ae, 0x3bb8e3f7, 0x3baa3761,
 0x3bacb799, 0x3bd644c6, 0x3badc637, 0x3ba00051, 0x3bd283c6, 0x3bae8a2c, 0x3bcf369f, 0x3bc3b58c,
 0x3b9f8495, 0x3bac871d, 0x3ba8178c, 0x3bbe825c, 0x3b9f3d48, 0x3b9f2d6d, 0x3bad6452, 0x3b9372a4,
 0x3bbde5a9, 0x3ba3a993, 0x3bd0259d, 0x3bb05344, 0x3bba8044, 0x3bff9b6c, 0x3bb108cc, 0x3b9fc8de,
 0x3c06158e, 0x3bbb8ab0, 0x3bbdb542, 0x3bc5d87c, 0x3bdd8369, 0x3bd9c9cb, 0x3bb5beff, 0x3bca0bbd,
 0x3bccf9d9, 0x3bccc393, 0x3bcb0e87, 0x3be8104a, 0x3bb46eb2, 0x3bd2accb, 0x3bc7f5e3, 0x3bd5b798,
 0x3bb05caa, 0x3bb09488, 0x3bc19d84, 0x3bb8047e, 0x3baec48f, 0x3bb0d3dd, 0x3ba84396, 0x3bc22859,
 0x3bb0dd43, 0x3bb61982, 0x3ba690f9, 0x3ba4fe92, 0x3baacbc6, 0x3bb1b7de, 0x3bb39497, 0x3bb18472,
 0x3c036f20, 0x3bd46cea, 0x3bc0aec7, 0x3bd2d3b7, 0x3bb65160, 0x3bcf6888, 0x3bc920b0, 0x3be505d4,
 0x3bd317d5, 0x3bbf78fc, 0x3ba55d1c, 0x3ba87290, 0x3be3c1dc, 0x3bb9de89, 0x3ba77d9d, 0x3ba2f360,
 0x3bbe9999, 0x3ba481d5, 0x3bd30891, 0x3bb73348, 0x3ba9b6b3, 0x3bbfe822, 0x3bad2278, 0x3bd0baae,
 0x3bbbc781, 0x3ba37220, 0x3bc7e486, 0x3b9ad91f, 0x3baf7b99, 0x3bc0a8e8, 0x3bb65841, 0x3ba1a13a,
 0x3bf0493e, 0x3bb84932, 0x3bcffd99, 0x3bad2d60, 0x3ba9e04e, 0x3bcb2d25, 0x3bc2967e, 0x3bbcc314,
 0x3bbdad60, 0x3bbad741, 0x3bbfa7e1, 0x3bb3b940, 0x3bc7e689, 0x3bbe92a2, 0x3badd48f, 0x3b9f7fa2,
 0x3baab8fc, 0x3ba5d176, 0x3bc7a358, 0x3bbed655, 0x3bcfcac4, 0x3bbdc487, 0x3bcbe16a, 0x3ba41574,
 0x3bb61f61, 0x3bc0e5a3, 0x3baa8691, 0x3bb171a8, 0x3be14d22, 0x3b956a8b, 0x3b99c335, 0x3bb73d2e,
 0x3bd857b0, 0x3be3d903, 0x3bebbdbe, 0x3bd8a6b5, 0x3bcac576, 0x3bcd27d1, 0x3bb74a85, 0x3bb1b168,
 0x3bc5539c, 0x3bc88a88, 0x3baf0c48, 0x3bdd2f1b, 0x3bd22b71, 0x3bce0689, 0x3bfb300d, 0x3bb35cb9,
 0x3bbd16a1, 0x3bbc4291, 0x3bc2a9c9, 0x3bc00843, 0x3baf664a, 0x3bbbac3e, 0x3bdb38e1, 0x3ba06652,
 0x3bb26a60, 0x3bda8fae, 0x3bac9387, 0x3bb43653, 0x3baead3d, 0x3bb01ad0, 0x3bb11e86, 0x3bd8fb03,
 0x3c0143cd, 0x3bbf1819, 0x3bd349bf, 0x3bcb157d, 0x3bb5a14d, 0x3bcc5670, 0x3bbe3443, 0x3bba0c96,
 0x3bad8081, 0x3bb66699, 0x3bca65aa, 0x3ba02edf, 0x3badb2ec, 0x3bbb2255, 0x3babaa7e, 0x3be4a531,
 0x3ba01620, 0x3be8ad13, 0x3baf7792, 0x3ba1c460, 0x3ba6947f, 0x3ba75be5, 0x3bb03a85, 0x3bb3b54f,
 0x3bba6b76, 0x3bbcb2b9, 0x3bcbfa95, 0x3b9f171c, 0x3b8d8712, 0x3b98610a, 0x3b9ae3f2, 0x3b9c954d,
 0x3be3d715, 0x3bb40266, 0x3bbba5de, 0x3bc72fbf, 0x3bbddf49, 0x3bd847eb, 0x3bb1bfc1, 0x3b9b0f90,
 0x3bbb7e46, 0x3b9b4c77, 0x3b9d68c7, 0x3bbacbd9, 0x3b9b02a5, 0x3bdc510f, 0x3bc6e7b0, 0x3ba72284,
 0x3bd4d687, 0x3bb5608a, 0x3bbb118e, 0x3bc817f2, 0x3b956d0f, 0x3bc0cd7b, 0x3b9d4723, 0x3ba142b0,
 0x3bb3a864, 0x3bc784b9, 0x3bd6eb09, 0x3bad8762, 0x3b9f2001, 0x3bb6d95b, 0x3b99eb4e, 0x3bb4bf4f,
 0x3c063645, 0x3bb7a385, 0x3bd88a86, 0x3bb8b37b, 0x3bbf5368, 0x3bd2257c, 0x3bcd67fd, 0x3ba43224,
 0x3bbc1606, 0x3b9ec37a, 0x3b9af25f, 0x3bb82b80, 0x3ba40228, 0x3bb4de84, 0x3ba4205b, 0x3baea94b,
 0x3ba1cb57, 0x3ba98433, 0x3bb1b75d, 0x3ba9a8db, 0x3baea36c, 0x3b9f792d, 0x3bd090bc, 0x3bbc796d,
 0x3b94a757, 0x3ba21c8a, 0x3b9f4c21, 0x3bdfcb6d, 0x3bb94a4f, 0x3b97606f, 0x3bb7c899, 0x3bca2dcc
};

// beta array (128 x 32)
static const unsigned int beta[] =
{
 0x3f800000, 0x3b291538, 0x397ba882, 0x3b1bf9c6, 0x3a08509c, 0x3927c5ac, 0x3b2e5365, 0x3a08509c,
 0xbb1d4952, 0xbb54562e, 0xbacc78ea, 0xb9c73abd, 0x3b6a9e6f, 0xbb0461fa, 0x3a83126f, 0x3b79096c,
 0x3b1aaa3b, 0x3b30f27c, 0x3a9aaa3b, 0x3b2fa2f0, 0xba83126f, 0x39f12c28, 0xba473abd, 0x3a712c28,
 0x3a1d4952, 0x3a22877f, 0x3966afcd, 0x3a956c0d, 0x3a1d4952, 0x3a22877f, 0xb8d1b717, 0xba0d8ec9,
 0x3f80624e, 0x3aee8d11, 0x3acc78ea, 0xbbc49ba6, 0x3b195aaf, 0x3b7a58f7, 0x3ae6afcd, 0xba51b717,
 0x3a6bedfa, 0x3b05b185, 0x39fba882, 0x3b5ed289, 0x3b05b185, 0xb7a7c5ac, 0x3b434c1b, 0xbb9374bc,
 0x3a807358, 0x3b102de0, 0x39c73abd, 0x3b656042, 0xba766a55, 0xba7ba882, 0x3acf1801, 0xbb0c3f3e,
 0x3b922531, 0x3ac73abd, 0x3ab24207, 0xb9d1b717, 0xb8fba882, 0xbb1bf9c6, 0x3b1fe868, 0x3a473abd,
 0x3f7ab368, 0x39c73abd, 0x3b05b185, 0x3ba1dfb9, 0x3b0d8ec9, 0xbb09a027, 0x3b50678c, 0xbb88509c,
 0x00000000, 0x3b03126f, 0xbb2e5365, 0xbb0d8ec9, 0xbb180b24, 0x3b41fc8f, 0x3afba882, 0xbb8c3f3e,
 0x39e6afcd, 0x3a27c5ac, 0xba22877f, 0x3b1e98dd, 0xbb7ba882, 0xbabf5d79, 0xba08509c, 0xbaaa64c3,
 0x3b73cb3e, 0xbb34e11e, 0xbb27c5ac, 0xbac1fc8f, 0xba378034, 0xba980b24, 0xba12ccf7, 0x3b49d9d3,
 0x3f80cb29, 0x3a83126f, 0x3966afcd, 0x3a5c3372, 0x3af9096c, 0xbae410b6, 0x3a27c5ac, 0x00000000,
 0xbb0461fa, 0xbaf3cb3e, 0x3ad6f545, 0xb827c5ac, 0xbb102de0, 0x3af3cb3e, 0xbb8aefb3, 0x3a8aefb3,
 0x3b3cbe62, 0xbaf12c28, 0x3a980b24, 0x39fba882, 0x3aafa2f0, 0x399d4952, 0x3b2a64c3, 0x3b4dc875,
 0xb9b24207, 0xbb156c0d, 0x3b1d4952, 0xba92ccf7, 0xbab24207, 0x3b656042, 0xb992ccf7, 0xba180b24,
 0x3f7d4fdf, 0xbb0d8ec9, 0x3b0aefb3, 0xba66afcd, 0xbb2a64c3, 0xb927c5ac, 0x3aa2877f, 0x39f12c28,
 0xb9d1b717, 0xbb2a64c3, 0x3b102de0, 0x3ae6afcd, 0x3b34e11e, 0xbb3f5d79, 0xbb01c2e3, 0x3b339192,
 0xbac73abd, 0xbac1fc8f, 0xba2d03da, 0xb992ccf7, 0x3b30f27c, 0xb827c5ac, 0x3ac49ba6, 0x3966afcd,
 0xbafba882, 0xbb5306a3, 0xb8fba882, 0x38fba882, 0xba4c78ea, 0xb912ccf7, 0x39e6afcd, 0xbb267621,
 0x3f815810, 0xbac1fc8f, 0x3992ccf7, 0x3b7cf80e, 0x3ab24207, 0x3a9aaa3b, 0xb927c5ac, 0xbb712c28,
 0xbafba882, 0x3ad6f545, 0xbb9aaa3b, 0xba8aefb3, 0x3a3cbe62, 0x3a56f545, 0xbaa52696, 0xbb7ba882,
 0xba8aefb3, 0x3a9fe868, 0x3b05b185, 0xbb6fdc9c, 0x3aee8d11, 0x3ab4e11e, 0x3b7f9724, 0x3a956c0d,
 0x3b267621, 0x3b0c3f3e, 0xbaee8d11, 0xbacf1801, 0x3acf1801, 0xb99d4952, 0xba6bedfa, 0x3b73cb3e,
 0x3f811687, 0x3a4c78ea, 0xbb826aa9, 0xb912ccf7, 0x3ab24207, 0xba902de0, 0x3b6bedfa, 0xb99d4952,
 0xbb59945b, 0xb99d4952, 0x3a85b185, 0x3a66afcd, 0xb912ccf7, 0xbae94ee4, 0xbb16bb99, 0x3a378034,
 0x3aa52696, 0xb9a7c5ac, 0x3acf1801, 0xbb08509c, 0xbabcbe62, 0xbb1aaa3b, 0x3a9aaa3b, 0x39a7c5ac,
 0xba180b24, 0x3ac1fc8f, 0xbb378034, 0x3ad4562e, 0xbaba1f4b, 0xbb1d4952, 0xbb117d6b, 0x3b3f5d79,
 0x3f818fc5, 0x3ae6afcd, 0x3b0d8ec9, 0x3827c5ac, 0xbaf12c28, 0x3b49d9d3, 0xba766a55, 0x3b1bf9c6,
 0xbb09a027, 0xbb870111, 0x3a3cbe62, 0x3b1d4952, 0xbb61719f, 0xba92ccf7, 0x3a83126f, 0xbb449ba6,
 0xbb1fe868, 0xbb66afcd, 0xba85b185, 0x3b180b24, 0xbb05b185, 0x3aee8d11, 0xbb0461fa, 0xbb473abd,
 0xbab78034, 0xbb2a64c3, 0xb99d4952, 0x3b0d8ec9, 0xbb195aaf, 0x39bcbe62, 0xbb88f862, 0xba85b185,
 0x3f810ff9, 0x3a180b24, 0xb992ccf7, 0x3a8aefb3, 0xba9d4952, 0x3b30f27c, 0x3b1aaa3b, 0x3b3a1f4b,
 0xbaafa2f0, 0x3b30f27c, 0xb9fba882, 0xb9bcbe62, 0xbb16bb99, 0xbb01c2e3, 0x3b117d6b, 0x3b55a5b9,
 0xb8fba882, 0xbabf5d79, 0xbb2137f4, 0x3a83126f, 0xbaf66a55, 0x3a66afcd, 0xbab78034, 0xbb291538,
 0xba27c5ac, 0xbb38cfc0, 0x3b8509c0, 0xbabf5d79, 0xbaa2877f, 0xba2d03da, 0xbb8b9778, 0xbb40ad04,
 0x3f7c1bda, 0xbb2a64c3, 0x39c73abd, 0x3b007358, 0x3abcbe62, 0x3b12ccf7, 0x3b03126f, 0xb927c5ac,
 0x3bb4e11e, 0x3b180b24, 0x3a9aaa3b, 0x3b1bf9c6, 0x3ac73abd, 0xbabcbe62, 0x3a980b24, 0x3b0d8ec9,
 0xbb1bf9c6, 0xba66afcd, 0x3b89a027, 0xba766a55, 0x3b1bf9c6, 0x3af12c28, 0xba92ccf7, 0x3a7ba882,
 0x3b41fc8f, 0x3a6bedfa, 0xb827c5ac, 0x3ad9945b, 0xbaf66a55, 0xb87ba882, 0xba7ba882, 0x3ab78034,
 0x3f7dfa44, 0x39f12c28, 0x3b2bb44e, 0xbabcbe62, 0xbaa52696, 0xba22877f, 0xba27c5ac, 0x39dc3372,
 0x3ad4562e, 0xbaaa64c3, 0x3a180b24, 0x3a12ccf7, 0x3a66afcd, 0x39dc3372, 0x387ba882, 0xbaaa64c3,
 0xbabf5d79, 0xb9b24207, 0xbab4e11e, 0x3ad4562e, 0x3b03126f, 0x3b007358, 0x3a88509c, 0xba3cbe62,
 0x3a324207, 0xbb0ede55, 0xba8aefb3, 0x3912ccf7, 0xba378034, 0xba0d8ec9, 0x3b54562e, 0x39d1b717,
 0x3f807c85, 0x3a956c0d, 0xbadc3372, 0xbb751aca, 0x3b2a64c3, 0x3b23d70a, 0x3b117d6b, 0x3b070111,
 0xbb77b9e0, 0xbb4f1801, 0xbac9d9d3, 0xbb62c12b, 0xba12ccf7, 0x3a51b717, 0x3af66a55, 0xba9aaa3b,
 0xbb8b9778, 0x3a4c78ea, 0x3aee8d11, 0x3a980b24, 0x3bc154ca, 0x37a7c5ac, 0x39b24207, 0x3a4c78ea,
 0x3afe4799, 0xbb0ede55, 0x3a324207, 0x3a03126f, 0x3af3cb3e, 0x3b12ccf7, 0xbb0461fa, 0xbbdc3372,
 0x3f7ef9db, 0xb927c5ac, 0x3aafa2f0, 0xbb1d4952, 0x3aa2877f, 0x3b30f27c, 0x3b0ede55, 0x3b3630a9,
 0xb9d1b717, 0xb9fba882, 0x3ae410b6, 0xbabcbe62, 0x3a3cbe62, 0xba92ccf7, 0xba8aefb3, 0xb951b717,
 0xb93cbe62, 0x3b0461fa, 0x3b0d8ec9, 0x3a56f545, 0xbafba882, 0x3ae410b6, 0xb8a7c5ac, 0xbab4e11e,
 0xb9fba882, 0xb8fba882, 0x3b2e5365, 0x3b27c5ac, 0x3a03126f, 0x3b5306a3, 0xbae94ee4, 0x3aba1f4b,
 0x3f7f06f7, 0xbacf1801, 0x39b24207, 0x39a7c5ac, 0x3a956c0d, 0xba324207, 0xb9d1b717, 0xbb08509c,
 0x3a7ba882, 0x387ba882, 0x3aded289, 0xba9d4952, 0xba7ba882, 0xbb4f1801, 0x3b117d6b, 0x393cbe62,
 0xba807358, 0xbb9bf9c6, 0x387ba882, 0xbb324207, 0xbb449ba6, 0xbaafa2f0, 0x3b0c3f3e, 0xb9dc3372,
 0x3988509c, 0xba0d8ec9, 0xba41fc8f, 0xb951b717, 0xb912ccf7, 0xbb449ba6, 0x3b9aaa3b, 0x3aebedfa,
 0x3f7d07c8, 0xb9b24207, 0x3abf5d79, 0xb8a7c5ac, 0xbac49ba6, 0xb992ccf7, 0x3a712c28, 0x3b0aefb3,
 0x3966afcd, 0xba92ccf7, 0xb827c5ac, 0xba83126f, 0x397ba882, 0xbb03126f, 0x3aba1f4b, 0x3aded289,
 0x37a7c5ac, 0x3b0c3f3e, 0xbaa7c5ac, 0xbbc9d9d3, 0xb9fba882, 0xba92ccf7, 0xb99d4952, 0x3b51b717,
 0xba4c78ea, 0x39fba882, 0xbb30f27c, 0x3a83126f, 0x3a766a55, 0x3a92ccf7, 0xba807358, 0x3988509c,
 0x3f7c49ba, 0xbaa2877f, 0x39f12c28, 0xbb473abd, 0xbaf66a55, 0xbb656042, 0xb97ba882, 0x00000000,
 0xbb79096c, 0xba3cbe62, 0x3ab24207, 0x3b08509c, 0xba03126f, 0x3acc78ea, 0x3a766a55, 0xbb83ba34,
 0x3a41fc8f, 0xbae410b6, 0xbad6f545, 0xba12ccf7, 0x3a1d4952, 0x3b870111, 0xb966afcd, 0x3a956c0d,
 0xba324207, 0x3b070111, 0xba6bedfa, 0x3b291538, 0xb966afcd, 0x38d1b717, 0xbaa7c5ac, 0xbb34e11e,
 0x3f8185f0, 0xba712c28, 0x3b252696, 0x00000000, 0xba2d03da, 0x3a83126f, 0x3ad6f545, 0xba5c3372,
 0xb99d4952, 0x3b378034, 0x3b83ba34, 0xbb9aaa3b, 0xba378034, 0x3aded289, 0x3a378034, 0xb988509c,
 0xbaba1f4b, 0x3a956c0d, 0xbb378034, 0x3a712c28, 0x3a9aaa3b, 0xba980b24, 0x3a807358, 0xbaf12c28,
 0x3a12ccf7, 0x3a473abd, 0xbb252696, 0x3ac73abd, 0xb87ba882, 0xbb8aefb3, 0x00000000, 0x3a7ba882,
 0x3f7ce704, 0xbb5ae3e7, 0x3af12c28, 0xba980b24, 0xb951b717, 0xba9fe868, 0xba324207, 0xbb41fc8f,
 0x3b807358, 0xba8d8ec9, 0x3b0ede55, 0x3b41fc8f, 0xb827c5ac, 0xba92ccf7, 0xba7ba882, 0xbb8e368f,
 0x3b05b185, 0xba3cbe62, 0x3a0d8ec9, 0x3b49d9d3, 0x39fba882, 0xb827c5ac, 0x3a5c3372, 0x3a22877f,
 0x3a7ba882, 0x3b0461fa, 0xba61719f, 0xbb378034, 0xba956c0d, 0x3b102de0, 0x3aad03da, 0xbb141c82,
 0x3f807fcc, 0x3a83126f, 0xbab78034, 0x3a902de0, 0x39c73abd, 0xbb811b1e, 0x3951b717, 0xbb50678c,
 0x3b30f27c, 0x3a03126f, 0xbb34e11e, 0xba56f545, 0xbb2d03da, 0xbb980b24, 0xba2d03da, 0x3b3b6ed6,
 0x3b4c78ea, 0x39b24207, 0x3b902de0, 0x3a0d8ec9, 0xba92ccf7, 0xbaad03da, 0x3ad1b717, 0xbb291538,
 0x3ac73abd, 0x3b102de0, 0x3ab78034, 0x3912ccf7, 0xb912ccf7, 0xb912ccf7, 0xb9c73abd, 0xba12ccf7,
 0x3f8130be, 0xbb102de0, 0x3992ccf7, 0x38fba882, 0xb992ccf7, 0xbaebedfa, 0x38fba882, 0x3a88509c,
 0x3a4c78ea, 0xba5c3372, 0x3b252696, 0x37a7c5ac, 0xbbaefb2b, 0xbad1b717, 0xba473abd, 0xba6bedfa,
 0x3a61719f, 0x3abf5d79, 0xbb007358, 0xbb870111, 0x3a180b24, 0xbbbc169c, 0xbaaa64c3, 0x399d4952,
 0xba9d4952, 0xb9c73abd, 0x39fba882, 0xbb03126f, 0xbb3a1f4b, 0x3afe4799, 0x39e6afcd, 0x3ac9d9d3,
 0x3f7e353f, 0xbb55a5b9, 0xba3cbe62, 0x3b2bb44e, 0xbb180b24, 0xbb2a64c3, 0xb966afcd, 0xbb7e4799,
 0xb7a7c5ac, 0x3b007358, 0x399d4952, 0x3b2a64c3, 0xbae94ee4, 0xba61719f, 0xba56f545, 0xb951b717,
 0xba66afcd, 0x3aaa64c3, 0xb8d1b717, 0x3b88f862, 0x3ab78034, 0xba180b24, 0xbb4c78ea, 0x3af66a55,
 0x39d1b717, 0x3a180b24, 0xbabcbe62, 0xbad6f545, 0xbaf9096c, 0xb93cbe62, 0x39e6afcd, 0xba85b185,
 0x3f8068dc, 0x38fba882, 0x39dc3372, 0x3aafa2f0, 0xbb08509c, 0xbb40ad04, 0xbb81c2e3, 0xba807358,
 0x3a56f545, 0x3b01c2e3, 0x3a807358, 0x3b117d6b, 0xbb30f27c, 0x393cbe62, 0x3a9d4952, 0x3afe4799,
 0xbb4c78ea, 0x39f12c28, 0x3b5ae3e7, 0xbb070111, 0x3a61719f, 0x3b2137f4, 0x393cbe62, 0x3a51b717,
 0x3b8b9778, 0x3a88509c, 0xba92ccf7, 0x3a6bedfa, 0xb8a7c5ac, 0xbb3b6ed6, 0x3b22877f, 0xbaebedfa,
 0x3f7e0ded, 0xb9dc3372, 0x3a61719f, 0xbb7a58f7, 0x3a61719f, 0xbaebedfa, 0xbb902de0, 0xbbaefb2b,
 0x3992ccf7, 0xba56f545, 0x3a51b717, 0x3a9aaa3b, 0x3b141c82, 0x39c73abd, 0x3ae1719f, 0xbb79096c,
 0x3a712c28, 0xbaf3cb3e, 0xbb0d8ec9, 0x3ac9d9d3, 0x3a27c5ac, 0xbb324207, 0x3966afcd, 0xbb5844d0,
 0xbabf5d79, 0x3b070111, 0xbb156c0d, 0xbb49d9d3, 0xb827c5ac, 0xbb870111, 0x3992ccf7, 0x3a8aefb3,
 0x3f7fe5c9, 0x387ba882, 0x3b2d03da, 0x37a7c5ac, 0xbb488a48, 0xbae94ee4, 0x39d1b717, 0x39e6afcd,
 0xbb03126f, 0xbb5306a3, 0xbb2bb44e, 0xbb4dc875, 0xbb55a5b9, 0xbb73cb3e, 0x3b195aaf, 0xbb49d9d3,
 0xba51b717, 0x3a1d4952, 0xbab78034, 0xb9c73abd, 0xbacf1801, 0x3ad4562e, 0xb9dc3372, 0xbaf9096c,
 0x3b1bf9c6, 0xba473abd, 0x3a980b24, 0x3a3cbe62, 0xbb1e98dd, 0xba5c3372, 0x3b180b24, 0x3b1e98dd,
 0x3f806595, 0xba8d8ec9, 0xbab4e11e, 0xb9e6afcd, 0xb992ccf7, 0x3b81c2e3, 0xba6bedfa, 0xbaf9096c,
 0xba4c78ea, 0x3b2bb44e, 0xbb324207, 0xbad4562e, 0xb9f12c28, 0x3ae6afcd, 0x39a7c5ac, 0x3a378034,
 0xbb807358, 0x3b22877f, 0xba2d03da, 0xbaf12c28, 0x3b05b185, 0x3ab24207, 0xb9dc3372, 0x3ad9945b,
 0xbbaefb2b, 0x3b7cf80e, 0x3adc3372, 0xbadc3372, 0x3966afcd, 0x38fba882, 0x37a7c5ac, 0xbb180b24,
 0x3f7fb15b, 0xba56f545, 0xba83126f, 0x3b16bb99, 0xba83126f, 0x3aebedfa, 0xbb2a64c3, 0x3a8d8ec9,
 0xb9b24207, 0xbaf12c28, 0xba3cbe62, 0xbb85b185, 0xbb16bb99, 0xba66afcd, 0x3a27c5ac, 0x3b2a64c3,
 0xb9c73abd, 0x3a378034, 0x3b08509c, 0xbb83ba34, 0x3ad4562e, 0x3b324207, 0x3b267621, 0x393cbe62,
 0xbb751aca, 0x3a51b717, 0x3a88509c, 0xbb102de0, 0x3a88509c, 0x3966afcd, 0xba902de0, 0xba51b717,
 0x3f813a93, 0x39fba882, 0x3b62c12b, 0x3a8aefb3, 0xbb1e98dd, 0xb9b24207, 0xbae1719f, 0xbb8b9778,
 0x3b3630a9, 0x3abcbe62, 0xb912ccf7, 0xbb434c1b, 0x3ae1719f, 0xb8a7c5ac, 0xb9f12c28, 0x3a766a55,
 0x39bcbe62, 0x397ba882, 0xbabf5d79, 0x3992ccf7, 0xba27c5ac, 0x3aa7c5ac, 0x399d4952, 0xba41fc8f,
 0xba1d4952, 0x3a8d8ec9, 0x3adc3372, 0x3a1d4952, 0xbb4c78ea, 0x3b102de0, 0x3aad03da, 0x37a7c5ac,
 0x3f800347, 0x39bcbe62, 0xba83126f, 0xbabf5d79, 0x3a473abd, 0xba9aaa3b, 0xbaee8d11, 0xbacc78ea,
 0x3af12c28, 0x3ab24207, 0xba956c0d, 0x3a378034, 0xbaaa64c3, 0x3a5c3372, 0x3b1bf9c6, 0xbb9a0275,
 0xba92ccf7, 0xba83126f, 0xb9f12c28, 0x39d1b717, 0x3ae94ee4, 0xbb51b717, 0x3b0aefb3, 0x3a473abd,
 0x3a8aefb3, 0xb9bcbe62, 0xba378034, 0xbb070111, 0x3a902de0, 0xbaf3cb3e, 0x3b22877f, 0xba7ba882,
 0x3f7f41f2, 0xbb727bb3, 0x3b05b185, 0xb9a7c5ac, 0x3927c5ac, 0x39a7c5ac, 0xbad6f545, 0x3988509c,
 0x3ab4e11e, 0x3ad1b717, 0x3b6410b6, 0xba08509c, 0xb966afcd, 0xbad1b717, 0xbb0461fa, 0xbb5ed289,
 0x3b8e368f, 0x3b1d4952, 0x3acf1801, 0x3aaa64c3, 0xb9f12c28, 0xbae6afcd, 0xba324207, 0x3b252696,
 0xba66afcd, 0x3b94c448, 0xbacf1801, 0x3b195aaf, 0xbae1719f, 0x3a41fc8f, 0xbb1e98dd, 0xbba91538,
 0x3f7cbfb1, 0xba08509c, 0xbadc3372, 0x3a22877f, 0x38a7c5ac, 0xbae1719f, 0x3b378034, 0x3ab4e11e,
 0x397ba882, 0xba85b185, 0x3ab78034, 0x3ac9d9d3, 0x3992ccf7, 0x3b89a027, 0xbb0ede55, 0x3b5d82fd,
 0xba980b24, 0x3abf5d79, 0x3ae6afcd, 0x3ae94ee4, 0x3abcbe62, 0x3b2137f4, 0x3b62c12b, 0x397ba882,
 0xbb0ede55, 0x3a2d03da, 0x3b195aaf, 0x3a92ccf7, 0xbb8ce704, 0xb9e6afcd, 0x3b378034, 0x3a180b24,
 0x3f7bedfa, 0xb912ccf7, 0xbb09a027, 0x3a1d4952, 0xbb54562e, 0xbb070111, 0xb9e6afcd, 0xbaa52696,
 0x3acc78ea, 0x3b0461fa, 0xbb180b24, 0x3827c5ac, 0xbabf5d79, 0xba56f545, 0x3a712c28, 0xba4c78ea,
 0x39f12c28, 0x3b656042, 0x3a8aefb3, 0xbaf3cb3e, 0xbac1fc8f, 0x3b5c3372, 0x3b6d3d86, 0x3966afcd,
 0xbb291538, 0x3a9aaa3b, 0x3b1aaa3b, 0xbb117d6b, 0x397ba882, 0x3b01c2e3, 0xbb8ede55, 0xbaa52696,
 0x3f77e282, 0x3b007358, 0x3afe4799, 0xba0d8ec9, 0xb992ccf7, 0x3aebedfa, 0x3a41fc8f, 0xb9dc3372,
 0xba2d03da, 0xbb070111, 0x3988509c, 0xb93cbe62, 0xb7a7c5ac, 0xbb1e98dd, 0x3b49d9d3, 0x3a61719f,
 0xbacc78ea, 0xbacc78ea, 0xbb1d4952, 0x3b4b295f, 0x3a56f545, 0x3b1fe868, 0x3b62c12b, 0xbaf66a55,
 0x3aba1f4b, 0xbb007358, 0x39c73abd, 0x39e6afcd, 0xbb0461fa, 0xbb0461fa, 0xbb3b6ed6, 0x3ab24207,
 0x3f7e0ded, 0xbb3f5d79, 0x3b339192, 0xb992ccf7, 0x3a12ccf7, 0x3b59945b, 0x3a88509c, 0xbb08509c,
 0xbb2137f4, 0xbb378034, 0x3aba1f4b, 0xb8d1b717, 0x3ae410b6, 0x3b4c78ea, 0x3b1aaa3b, 0xba980b24,
 0xbb2e5365, 0xb988509c, 0xb9dc3372, 0x3ae6afcd, 0x3a9fe868, 0xbb2a64c3, 0x3b3630a9, 0x3827c5ac,
 0x3b267621, 0x3a12ccf7, 0xbafe4799, 0xbab78034, 0x3a4c78ea, 0xbb66afcd, 0x3a22877f, 0x3afba882,
 0x3f7c7e28, 0xbac73abd, 0x3aebedfa, 0xb966afcd, 0xba378034, 0xbb007358, 0xba08509c, 0xba0d8ec9,
 0x3988509c, 0xbb141c82, 0xbb070111, 0x3afba882, 0x3b03126f, 0x3b712c28, 0x3abf5d79, 0xb9f12c28,
 0x3b5306a3, 0xbb7cf80e, 0x3ad4562e, 0x3b9b5200, 0x3a8aefb3, 0x3ac1fc8f, 0x3a4c78ea, 0xb93cbe62,
 0x3b0d8ec9, 0x3a08509c, 0xbb180b24, 0xbb1bf9c6, 0x3a41fc8f, 0xb992ccf7, 0xba0d8ec9, 0xb9f12c28,
 0x3f80b439, 0xbb22877f, 0x39fba882, 0x3aaa64c3, 0xb9d1b717, 0x3a56f545, 0x3988509c, 0xbb3cbe62,
 0x3a9d4952, 0x3ac9d9d3, 0xba7ba882, 0x3acf1801, 0x3a03126f, 0x39d1b717, 0x39dc3372, 0x3992ccf7,
 0x3a956c0d, 0xbb08509c, 0xbb01c2e3, 0xba378034, 0x3a980b24, 0xba1d4952, 0xbaf3cb3e, 0xbb01c2e3,
 0xba0d8ec9, 0xbb0ede55, 0xbb1fe868, 0xba766a55, 0xbaf9096c, 0x3a980b24, 0x39c73abd, 0x3966afcd,
 0x3f806c22, 0x3a9d4952, 0x3abcbe62, 0x39d1b717, 0x3a9aaa3b, 0xbaf66a55, 0xbae410b6, 0x3aaa64c3,
 0xba4c78ea, 0xbac73abd, 0xb8fba882, 0xb9f12c28, 0xba56f545, 0x3a08509c, 0xbb5ae3e7, 0x3ad1b717,
 0xb912ccf7, 0xbb007358, 0xbaf9096c, 0xba473abd, 0xbb34e11e, 0xba5c3372, 0x3ad6f545, 0xb97ba882,
 0xba41fc8f, 0xbabcbe62, 0xba51b717, 0xbb291538, 0xb8fba882, 0xb827c5ac, 0x3a473abd, 0xba22877f,
 0x3f7e69ad, 0xb992ccf7, 0x3b51b717, 0xb87ba882, 0xbaaa64c3, 0xbb180b24, 0x3ae94ee4, 0x3b49d9d3,
 0xb9d1b717, 0x3b55a5b9, 0xba956c0d, 0xbb8ede55, 0xbb2137f4, 0xbb03126f, 0xbac73abd, 0x3a5c3372,
 0x3b0c3f3e, 0xbb01c2e3, 0xbb488a48, 0xbb23d70a, 0xb8a7c5ac, 0xbb0461fa, 0xbadc3372, 0xbb1e98dd,
 0x3b4b295f, 0x3abf5d79, 0xba378034, 0x38a7c5ac, 0x3a8aefb3, 0xb9b24207, 0x3a83126f, 0x3b09a027,
 0x3f803e42, 0x3af9096c, 0x3b0aefb3, 0x39b24207, 0x3b3b6ed6, 0x39f12c28, 0x3b694ee4, 0x3a9aaa3b,
 0x3a66afcd, 0x3ba67621, 0xba7ba882, 0x3b473abd, 0xbaf66a55, 0x3adc3372, 0x3aad03da, 0x3b23d70a,
 0xba83126f, 0xb827c5ac, 0x3b45eb31, 0x3aba1f4b, 0xbb38cfc0, 0xbb86594b, 0xb9c73abd, 0xbb117d6b,
 0x3a378034, 0x3b12ccf7, 0xbaebedfa, 0x3aa52696, 0x3ac9d9d3, 0x3b267621, 0x3a766a55, 0x3ae6afcd,
 0x3f7f5c29, 0xba5c3372, 0xbac9d9d3, 0x39fba882, 0x3ac73abd, 0x3aad03da, 0xbabf5d79, 0x3b61719f,
 0x3827c5ac, 0xba180b24, 0xbb01c2e3, 0x3b3b6ed6, 0x39dc3372, 0xbb5c3372, 0xbaf9096c, 0x3ad9945b,
 0x3a807358, 0x3a807358, 0xb87ba882, 0x3af66a55, 0xba8d8ec9, 0xbab4e11e, 0x38d1b717, 0x37a7c5ac,
 0xbb5ed289, 0x3ad6f545, 0xbb656042, 0xbacf1801, 0x3b156c0d, 0x3af12c28, 0x3a56f545, 0x3adc3372,
 0x3f7f2e49, 0x3a0d8ec9, 0xba12ccf7, 0xbaded289, 0x3b0d8ec9, 0x3a712c28, 0x39c73abd, 0xb8fba882,
 0xbaa7c5ac, 0x393cbe62, 0xbb97635e, 0x3b79096c, 0x3a22877f, 0xbb0ede55, 0xbb66afcd, 0x399d4952,
 0xbb102de0, 0xbb267621, 0x3992ccf7, 0x3a85b185, 0x3a180b24, 0x37a7c5ac, 0xbb1fe868, 0xb827c5ac,
 0xbb83ba34, 0xbaee8d11, 0x39a7c5ac, 0xba61719f, 0x397ba882, 0xb8a7c5ac, 0x3a0d8ec9, 0x3b195aaf,
 0x3f7ed917, 0x3a4c78ea, 0x3aebedfa, 0x3aa2877f, 0xbae1719f, 0xb992ccf7, 0xbaad03da, 0x3a88509c,
 0xbae1719f, 0xba66afcd, 0x3a66afcd, 0xbb1e98dd, 0xbb54562e, 0x3a9fe868, 0x3a12ccf7, 0xbb3b6ed6,
 0x3b180b24, 0x3a378034, 0xbb0461fa, 0x3b070111, 0xba324207, 0xbac1fc8f, 0xba6bedfa, 0x3b195aaf,
 0xb7a7c5ac, 0x3a88509c, 0xbb73cb3e, 0x3ac49ba6, 0x3b38cfc0, 0x3afe4799, 0xba980b24, 0x3b5d82fd,
 0x3f7c985f, 0xb8a7c5ac, 0x3b291538, 0x39e6afcd, 0x3b291538, 0x3b2a64c3, 0xbb0aefb3, 0xba27c5ac,
 0x3b694ee4, 0x3b30f27c, 0x3a12ccf7, 0xba4c78ea, 0x38d1b717, 0x3b27c5ac, 0x00000000, 0xba9aaa3b,
 0xbb01c2e3, 0xbaee8d11, 0x3b102de0, 0x3ab24207, 0x3b8509c0, 0xba56f545, 0x3a83126f, 0x39dc3372,
 0x3b1d4952, 0x3a473abd, 0xbac9d9d3, 0xbb4dc875, 0x3b5306a3, 0x3a27c5ac, 0xbaf3cb3e, 0xba0d8ec9,
 0x3f7b9f56, 0x3a766a55, 0x3b03126f, 0x39bcbe62, 0x39dc3372, 0xba902de0, 0x38a7c5ac, 0x3b8e368f,
 0xbb141c82, 0xb93cbe62, 0xba6bedfa, 0x39d1b717, 0xb8a7c5ac, 0x3a56f545, 0x3a956c0d, 0xb7a7c5ac,
 0x3b267621, 0xbba32f45, 0x3b473abd, 0x3b62c12b, 0xb9f12c28, 0x3b73cb3e, 0x3b40ad04, 0x3afba882,
 0x3af3cb3e, 0xb827c5ac, 0x3b291538, 0xba807358, 0x3ae94ee4, 0xba324207, 0xbb05b185, 0xba3cbe62,
 0x3f7e2196, 0x3a66afcd, 0x38fba882, 0xba902de0, 0x3af66a55, 0x3b1d4952, 0xb9f12c28, 0xb9d1b717,
 0xbb03126f, 0x3a8aefb3, 0x399d4952, 0xbb1bf9c6, 0xbb38cfc0, 0x3aee8d11, 0xba66afcd, 0x3b6fdc9c,
 0xb99d4952, 0xba956c0d, 0x3ae6afcd, 0x393cbe62, 0xb8fba882, 0x3aa7c5ac, 0xb8fba882, 0x3aba1f4b,
 0x3b27c5ac, 0xba03126f, 0xb87ba882, 0x3ab4e11e, 0x3adc3372, 0xba8aefb3, 0x3b1aaa3b, 0x3b0461fa,
 0x3f8089a0, 0x3b007358, 0x3afe4799, 0x3b3a1f4b, 0x39dc3372, 0x3b070111, 0xb966afcd, 0x38a7c5ac,
 0x3b007358, 0xbaa2877f, 0x3a03126f, 0x3ae1719f, 0x3b09a027, 0xbac9d9d3, 0x3a1d4952, 0xba83126f,
 0xbab24207, 0x3a12ccf7, 0xba9fe868, 0xb992ccf7, 0x3abf5d79, 0x3ac49ba6, 0x3a902de0, 0x3b8f861a,
 0x3a9aaa3b, 0xbad6f545, 0xbaa7c5ac, 0xbad6f545, 0xbbab0c89, 0xb8d1b717, 0x3af3cb3e, 0x39d1b717,
 0x3f7df3b6, 0x38a7c5ac, 0x3b05b185, 0x3a9aaa3b, 0x3b2bb44e, 0xbb1aaa3b, 0xbb007358, 0x3a2d03da,
 0xbab78034, 0x3a378034, 0xbad1b717, 0xb8a7c5ac, 0x3a4c78ea, 0xbb27c5ac, 0xbae1719f, 0xbb3e0ded,
 0xbabcbe62, 0xbac73abd, 0x3ad9945b, 0x39e6afcd, 0xbabcbe62, 0xbad1b717, 0x3b55a5b9, 0xb9b24207,
 0x38fba882, 0x39bcbe62, 0x3acf1801, 0x3b807358, 0x3b88509c, 0xba0d8ec9, 0xbaaa64c3, 0xbb6bedfa,
 0x3f821965, 0x3b22877f, 0x3a41fc8f, 0x3a56f545, 0x3951b717, 0x39fba882, 0xba9aaa3b, 0xb99d4952,
 0xbb59945b, 0xbb1e98dd, 0x3ad6f545, 0xbb473abd, 0x3ae94ee4, 0x37a7c5ac, 0x397ba882, 0x3b83126f,
 0x3aad03da, 0x3b0d8ec9, 0x3ad1b717, 0xb9c73abd, 0x3adc3372, 0xb9bcbe62, 0xb992ccf7, 0xbaf66a55,
 0x3ad6f545, 0x3ac49ba6, 0x3b90d5a6, 0xba7ba882, 0x3a4c78ea, 0x3b291538, 0xb99d4952, 0x3b79096c,
 0x3f80c155, 0x3b4dc875, 0x39a7c5ac, 0xba41fc8f, 0x3a61719f, 0xba41fc8f, 0xb988509c, 0xbaaa64c3,
 0x3b727bb3, 0x3b656042, 0xb8d1b717, 0xb9b24207, 0xb927c5ac, 0x3a956c0d, 0x3988509c, 0x37a7c5ac,
 0x3ac9d9d3, 0x3a4c78ea, 0xb988509c, 0xb9d1b717, 0xbb324207, 0xbb180b24, 0x39fba882, 0xbb5c3372,
 0xba3cbe62, 0xba56f545, 0x3bab0c89, 0x3a902de0, 0x3b40ad04, 0x3a8aefb3, 0xba08509c, 0xbb4dc875,
 0x3f8072b0, 0xb9dc3372, 0x3a1d4952, 0xb9f12c28, 0xb9a7c5ac, 0x3b27c5ac, 0x3a9fe868, 0x3a1d4952,
 0xb966afcd, 0x39d1b717, 0xbb16bb99, 0xbadc3372, 0x38fba882, 0xbb50678c, 0xbb5306a3, 0xbb712c28,
 0xbb9aaa3b, 0xbab78034, 0x38d1b717, 0x39d1b717, 0xb8fba882, 0xb9c73abd, 0xbb180b24, 0xbb09a027,
 0xbb7ba882, 0xb9d1b717, 0x3afba882, 0x39fba882, 0x3a712c28, 0xb87ba882, 0xbaaa64c3, 0xba766a55,
 0x3f816873, 0xbb324207, 0x3aa2877f, 0xba2d03da, 0xb988509c, 0xbb102de0, 0x3af3cb3e, 0xbb602214,
 0xbac9d9d3, 0x3a473abd, 0xba27c5ac, 0xbad4562e, 0x3a83126f, 0x3992ccf7, 0xbb61719f, 0xbb86594b,
 0xbb5844d0, 0xbb89a027, 0x3951b717, 0xbb007358, 0x3abf5d79, 0x3b56f545, 0x3ad1b717, 0xbb45eb31,
 0x3a712c28, 0xba03126f, 0xba180b24, 0xba61719f, 0xba88509c, 0xb9b24207, 0xbb03126f, 0x3a980b24,
 0x3f7d07c8, 0x3a766a55, 0xba980b24, 0x3b3f5d79, 0x3b378034, 0xbad1b717, 0xbb488a48, 0xba27c5ac,
 0xb93cbe62, 0x393cbe62, 0x3a8aefb3, 0xb93cbe62, 0xbaad03da, 0x3a378034, 0x3992ccf7, 0x3a8d8ec9,
 0xbb0ede55, 0x39b24207, 0x3b03126f, 0x39fba882, 0xbaded289, 0x387ba882, 0xba88509c, 0xbaebedfa,
 0xbae6afcd, 0xbb49d9d3, 0xbb3e0ded, 0xba712c28, 0x3ac73abd, 0x3a7ba882, 0xba03126f, 0x3a41fc8f,
 0x3f7e5604, 0x3a56f545, 0x3a712c28, 0xb992ccf7, 0xba9d4952, 0x39a7c5ac, 0x3ae94ee4, 0x38d1b717,
 0xb912ccf7, 0xba324207, 0xbaded289, 0xbb77b9e0, 0x3b102de0, 0xb966afcd, 0x3ac1fc8f, 0xba7ba882,
 0x3ad6f545, 0x3a61719f, 0xbb0aefb3, 0x38fba882, 0x3a5c3372, 0x3aaa64c3, 0x3a902de0, 0xbaf9096c,
 0xbab24207, 0xb8d1b717, 0xbba52696, 0xbb51b717, 0x3a902de0, 0x3a180b24, 0x39dc3372, 0x3a902de0,
 0x3f81205c, 0x39f12c28, 0xbaaa64c3, 0x3ad4562e, 0x3adc3372, 0xba473abd, 0xba1d4952, 0xba956c0d,
 0xbabf5d79, 0xb7a7c5ac, 0xbb5306a3, 0xbaa2877f, 0x3a03126f, 0x38d1b717, 0xbb2a64c3, 0x3b16bb99,
 0xbb488a48, 0x3951b717, 0xba5c3372, 0x3aded289, 0xbb08509c, 0xbb3b6ed6, 0x3b070111, 0x3ab4e11e,
 0x3a956c0d, 0xba712c28, 0x39d1b717, 0xbb38cfc0, 0xb912ccf7, 0xba5c3372, 0xbb0c3f3e, 0xba83126f,
 0x3f80d845, 0xbad4562e, 0xba5c3372, 0x3b01c2e3, 0xbb67ff58, 0x3aafa2f0, 0x3afe4799, 0x3b23d70a,
 0xb912ccf7, 0xbacc78ea, 0xbb1d4952, 0xba378034, 0x3a766a55, 0x3a378034, 0xb97ba882, 0xbb40ad04,
 0x3b1bf9c6, 0xbad9945b, 0x3a85b185, 0xbb03126f, 0x3abf5d79, 0xba51b717, 0x3a807358, 0xbb3e0ded,
 0x3a9d4952, 0xba4c78ea, 0x3abf5d79, 0xba712c28, 0x3b8b9778, 0x3aad03da, 0xbb34e11e, 0xb9bcbe62,
 0x3f7e2196, 0x3b56f545, 0x3abcbe62, 0x3a83126f, 0xbb7f9724, 0x3b2a64c3, 0xba92ccf7, 0xb951b717,
 0xbaf66a55, 0xb9d1b717, 0x3ad6f545, 0xba3cbe62, 0x3abcbe62, 0x3b5c3372, 0xbb656042, 0xbbafa2f0,
 0xba9aaa3b, 0xb9fba882, 0xbb0461fa, 0x3b03126f, 0xba9aaa3b, 0x3a473abd, 0x3a9aaa3b, 0xba66afcd,
 0x3b3a1f4b, 0x3b449ba6, 0x3b195aaf, 0x3a473abd, 0x3b339192, 0xb9dc3372, 0xbb38cfc0, 0xbb488a48,
 0x3f800d1b, 0xbb3cbe62, 0x3af3cb3e, 0xba12ccf7, 0x3a8d8ec9, 0x3a0d8ec9, 0x3aee8d11, 0x3a9fe868,
 0x3a324207, 0xbaba1f4b, 0xba85b185, 0xbb2a64c3, 0xba6bedfa, 0x3927c5ac, 0xb8fba882, 0x3b41fc8f,
 0xbae6afcd, 0xbb656042, 0xbb434c1b, 0xb9fba882, 0xbb1bf9c6, 0x39e6afcd, 0x3b54562e, 0x00000000,
 0xb8d1b717, 0xba7ba882, 0x3aaa64c3, 0x3b180b24, 0x37a7c5ac, 0x00000000, 0xbaa7c5ac, 0xbb55a5b9,
 0x3f7c6a7f, 0xb9d1b717, 0xba6bedfa, 0xba3cbe62, 0xbb3f5d79, 0xbb16bb99, 0xbafba882, 0x3af9096c,
 0x3a5c3372, 0x3a1d4952, 0xbae1719f, 0xb8a7c5ac, 0xba88509c, 0x3b980b24, 0x3b1fe868, 0x39b24207,
 0xbb6a9e6f, 0x3b08509c, 0x3b180b24, 0x3b05b185, 0x3ae94ee4, 0xbb0d8ec9, 0xbb8a47ed, 0xba807358,
 0xbaaa64c3, 0xbad6f545, 0x3acf1801, 0xbaaa64c3, 0xbb54562e, 0x3a0d8ec9, 0xb99d4952, 0xba7ba882,
 0x3f828588, 0xbafe4799, 0xba378034, 0xbb01c2e3, 0x3a8d8ec9, 0xbb488a48, 0xbb0461fa, 0xba08509c,
 0x3a5c3372, 0x3b324207, 0xbb2d03da, 0xba4c78ea, 0x3b2137f4, 0xbb77b9e0, 0xbaad03da, 0x3aebedfa,
 0xbaf66a55, 0xb9a7c5ac, 0xbb8e368f, 0xbb117d6b, 0xbb0d8ec9, 0xba473abd, 0x3a85b185, 0xbb8ce704,
 0xbb54562e, 0xbb09a027, 0xba0d8ec9, 0x3ae410b6, 0x3af9096c, 0xbafe4799, 0xbaaa64c3, 0xb827c5ac,
 0x3f802de0, 0xb9fba882, 0xbb602214, 0x39e6afcd, 0x3a12ccf7, 0x3a7ba882, 0xb827c5ac, 0x3b38cfc0,
 0x3a22877f, 0x3abf5d79, 0xbae94ee4, 0xbb61719f, 0xba66afcd, 0x39dc3372, 0xbb51b717, 0x3a1d4952,
 0x3a180b24, 0x3a12ccf7, 0xba8aefb3, 0x3a807358, 0x3b03126f, 0xbafe4799, 0xb988509c, 0xbaa7c5ac,
 0xb9dc3372, 0xba9d4952, 0xba180b24, 0x3ba7c5ac, 0xbab78034, 0x3a980b24, 0xb992ccf7, 0x3a08509c,
 0x3f7bac71, 0x3b5844d0, 0xb8d1b717, 0xba902de0, 0xbb807358, 0xbab4e11e, 0xbae6afcd, 0xbaf66a55,
 0x3a7ba882, 0x3ac9d9d3, 0x3a3cbe62, 0xb9fba882, 0x3b4dc875, 0x3a324207, 0xbb0d8ec9, 0x3b55a5b9,
 0xbb102de0, 0xba88509c, 0x3b956c0d, 0x37a7c5ac, 0x3aafa2f0, 0xb9a7c5ac, 0xbae410b6, 0x38a7c5ac,
 0x3b980b24, 0x3abf5d79, 0xba03126f, 0xba1d4952, 0x3b751aca, 0xbb180b24, 0x3b2fa2f0, 0x3966afcd,
 0x3f7db22d, 0xb8d1b717, 0x39c73abd, 0x3b8b9778, 0x3b7ba882, 0x3a0d8ec9, 0x3abcbe62, 0xba807358,
 0xbb12ccf7, 0xbb41fc8f, 0x38d1b717, 0x3b40ad04, 0x3827c5ac, 0x3ae94ee4, 0xbb694ee4, 0x3b49d9d3,
 0xbb117d6b, 0xba61719f, 0xbb3b6ed6, 0xbae6afcd, 0x38d1b717, 0x3951b717, 0x3b180b24, 0xbb0c3f3e,
 0x3abcbe62, 0x3abcbe62, 0x3b870111, 0xb8fba882, 0x3912ccf7, 0xbb16bb99, 0xbaf12c28, 0xba956c0d,
 0x3f81652c, 0xbb1bf9c6, 0xba3cbe62, 0x3b5306a3, 0x3b7f9724, 0x3b141c82, 0x3b3630a9, 0x38d1b717,
 0x3af12c28, 0xb8a7c5ac, 0x3a6bedfa, 0x3b59945b, 0xbaa2877f, 0x3aa7c5ac, 0xbb434c1b, 0x3b156c0d,
 0xb97ba882, 0xba4c78ea, 0x3a41fc8f, 0x3b40ad04, 0xbb2bb44e, 0x3b141c82, 0x3af12c28, 0xb9b24207,
 0xbb488a48, 0xbb85b185, 0xba980b24, 0x39fba882, 0xbac49ba6, 0xb9f12c28, 0x3adc3372, 0x3a766a55,
 0x3f7e3bcd, 0xbb34e11e, 0x3a08509c, 0x3a2d03da, 0x3aa7c5ac, 0xb8d1b717, 0xbae410b6, 0x399d4952,
 0x3b8aefb3, 0xbaa7c5ac, 0xbafe4799, 0xba980b24, 0x3b45eb31, 0x3a766a55, 0xba88509c, 0x3abcbe62,
 0xba22877f, 0xba27c5ac, 0x3951b717, 0xbb2a64c3, 0xba92ccf7, 0xba3cbe62, 0xbaa7c5ac, 0x3b81c2e3,
 0xbad9945b, 0x3992ccf7, 0xb9d1b717, 0xbb0461fa, 0x3a08509c, 0xbb7cf80e, 0xbb102de0, 0xb912ccf7,
 0x3f7f5c29, 0xb8fba882, 0x3af66a55, 0xbac49ba6, 0xbb3f5d79, 0x3a88509c, 0xb912ccf7, 0x3b3b6ed6,
 0x38a7c5ac, 0xbabf5d79, 0x3af12c28, 0x3be8a71e, 0x39bcbe62, 0xbb267621, 0x3a9fe868, 0x3a8d8ec9,
 0x3a180b24, 0xbb2e5365, 0x3b378034, 0x3b195aaf, 0x3a66afcd, 0x3b49d9d3, 0x3a712c28, 0xba902de0,
 0x3a51b717, 0x38fba882, 0x3acf1801, 0x3b16bb99, 0xbb83126f, 0xb9c73abd, 0x3b180b24, 0xbab4e11e,
 0x3f7e2824, 0xbae6afcd, 0xbba91538, 0x3b59945b, 0x3b41fc8f, 0x3b12ccf7, 0x3b267621, 0xba5c3372,
 0xbb08509c, 0x3b252696, 0x3a83126f, 0xb8d1b717, 0x3b94c448, 0xb9d1b717, 0x3a324207, 0xbb2bb44e,
 0xba180b24, 0xba92ccf7, 0x3b2137f4, 0x399d4952, 0x3b694ee4, 0xbb3b6ed6, 0x3a22877f, 0x3b94c448,
 0x3a2d03da, 0xbb2bb44e, 0xbae6afcd, 0x3acc78ea, 0x3b5ae3e7, 0xba51b717, 0xb8a7c5ac, 0x3b9e98dd,
 0x3f802de0, 0xbad4562e, 0xbafba882, 0xbb0aefb3, 0x3912ccf7, 0xba0d8ec9, 0x3af66a55, 0x3a5c3372,
 0xb9bcbe62, 0x3a9fe868, 0x3a41fc8f, 0xba41fc8f, 0x3b4c78ea, 0xbb2137f4, 0x3b1fe868, 0xbb16bb99,
 0xba27c5ac, 0xbb0aefb3, 0x3ba3d70a, 0x3b27c5ac, 0xbaf66a55, 0xbb3630a9, 0x3aa2877f, 0x3acf1801,
 0x3a766a55, 0xbb85b185, 0x3a56f545, 0xb9d1b717, 0x3b8c3f3e, 0x3b34e11e, 0xbb5306a3, 0x3b3630a9,
 0x3f7eb1c4, 0x39e6afcd, 0x3ac1fc8f, 0x3aaa64c3, 0x3b2e5365, 0xbab78034, 0xba56f545, 0x3b5ae3e7,
 0x3a324207, 0xbb156c0d, 0x3aa52696, 0xbb712c28, 0x3b2bb44e, 0x3aad03da, 0xbaa52696, 0x3a7ba882,
 0xbaf9096c, 0x3ad9945b, 0x3ba91538, 0xbac1fc8f, 0x3992ccf7, 0x3a1d4952, 0x3a6bedfa, 0x3ad6f545,
 0x38a7c5ac, 0x3a12ccf7, 0xb99d4952, 0xbb1e98dd, 0x3b51b717, 0xbb1aaa3b, 0xbb3b6ed6, 0x3a8d8ec9,
 0x3f8154ca, 0x3ac1fc8f, 0xba9fe868, 0x39dc3372, 0xbb5844d0, 0x3951b717, 0x3ad6f545, 0x3ae410b6,
 0x3b16bb99, 0x3b2137f4, 0x3988509c, 0xbab4e11e, 0xbae6afcd, 0xbb62c12b, 0x3a88509c, 0x3b38cfc0,
 0xbb9374bc, 0x3b08509c, 0xba27c5ac, 0x3a378034, 0xb827c5ac, 0x3b1bf9c6, 0x3a5c3372, 0x3b54562e,
 0xb992ccf7, 0xba61719f, 0xba92ccf7, 0xb7a7c5ac, 0xbb2a64c3, 0x3ab24207, 0x3b9df117, 0xba807358,
 0x3f7fdf3b, 0x39dc3372, 0x3b1d4952, 0xba08509c, 0xbac49ba6, 0xbb2a64c3, 0xb9f12c28, 0xbaaa64c3,
 0xba980b24, 0xb9b24207, 0xb827c5ac, 0xbb03126f, 0x3a12ccf7, 0xba9fe868, 0xbb7cf80e, 0x39fba882,
 0x38d1b717, 0xbb195aaf, 0xb9f12c28, 0x3b6410b6, 0x3aa52696, 0xbac49ba6, 0xba8d8ec9, 0xbaebedfa,
 0xba41fc8f, 0xb927c5ac, 0x39f12c28, 0x3951b717, 0x3ae1719f, 0x3b30f27c, 0xbafe4799, 0x3a180b24,
 0x3f7f34d7, 0xbb0c3f3e, 0x39bcbe62, 0x3827c5ac, 0xbb16bb99, 0xbb156c0d, 0x3a51b717, 0xba88509c,
 0x3827c5ac, 0x3af66a55, 0x3b7e4799, 0xba85b185, 0x3b09a027, 0xbb45eb31, 0x3a324207, 0xba41fc8f,
 0x3b449ba6, 0x3a85b185, 0xbab4e11e, 0xb93cbe62, 0x3acf1801, 0xba41fc8f, 0xba8d8ec9, 0x3a3cbe62,
 0x3b23d70a, 0x3abcbe62, 0x3ab4e11e, 0x3b7cf80e, 0x3a56f545, 0x3ad9945b, 0xb9d1b717, 0x3a4c78ea,
 0x3f7f41f2, 0xbb473abd, 0xba324207, 0x3aba1f4b, 0xbb378034, 0x3afba882, 0xba378034, 0xbb0aefb3,
 0xbb66afcd, 0xba83126f, 0x3aafa2f0, 0xba807358, 0x3a22877f, 0xb9d1b717, 0xba88509c, 0xbb0d8ec9,
 0xba12ccf7, 0x3a51b717, 0xba4c78ea, 0xbaa52696, 0xb966afcd, 0xb9bcbe62, 0x393cbe62, 0xbb2fa2f0,
 0x3b826aa9, 0xba41fc8f, 0xbab4e11e, 0xbb8aefb3, 0x3a85b185, 0xbb23d70a, 0xbb2d03da, 0x3a378034,
 0x3f820903, 0x3a41fc8f, 0x39a7c5ac, 0xba27c5ac, 0xbb1aaa3b, 0x3b6fdc9c, 0xba324207, 0xbac73abd,
 0xbb54562e, 0x3b77b9e0, 0x37a7c5ac, 0xbb1fe868, 0xbae410b6, 0x38a7c5ac, 0x3b0461fa, 0xba9d4952,
 0xb951b717, 0x3b339192, 0x3a22877f, 0xbb324207, 0xba3cbe62, 0xba4c78ea, 0xbb0aefb3, 0x3a92ccf7,
 0xb966afcd, 0xb87ba882, 0xbb766a55, 0xba12ccf7, 0x3a8d8ec9, 0x3ba47ed0, 0x3a3cbe62, 0x3a22877f,
 0x3f809a02, 0x3a6bedfa, 0x3aa52696, 0xb99d4952, 0x3a2d03da, 0xba902de0, 0x3b102de0, 0x3b08509c,
 0x39a7c5ac, 0xba180b24, 0xbb03126f, 0x3ab4e11e, 0x3a4c78ea, 0x3a9aaa3b, 0xbb0d8ec9, 0x3a08509c,
 0xbaebedfa, 0xbaa52696, 0xbb77b9e0, 0x3ab4e11e, 0xb9fba882, 0xbb6a9e6f, 0x397ba882, 0xba807358,
 0x3b55a5b9, 0xb951b717, 0x3b4dc875, 0x00000000, 0xbba5ce5b, 0x3a9aaa3b, 0xba5c3372, 0x3a92ccf7,
 0x3f7a5e35, 0x3b05b185, 0x397ba882, 0x3b94c448, 0xbb0461fa, 0x3b488a48, 0x3aa52696, 0x3adc3372,
 0xb9b24207, 0x3ac9d9d3, 0x3b0aefb3, 0xba66afcd, 0xbbb24207, 0x3a7ba882, 0x3ae1719f, 0xba85b185,
 0xb992ccf7, 0xb99d4952, 0xba9aaa3b, 0x3a85b185, 0xba324207, 0xbb5306a3, 0xba08509c, 0x3b0c3f3e,
 0xba61719f, 0xbb324207, 0xbaad03da, 0x387ba882, 0xb966afcd, 0x3acc78ea, 0x3b41fc8f, 0xba51b717,
 0x3f7f6fd2, 0xbb811b1e, 0x37a7c5ac, 0x39f12c28, 0x3a61719f, 0x3927c5ac, 0x3ad4562e, 0xb9bcbe62,
 0x3a180b24, 0xbb08509c, 0x3a766a55, 0xbb5844d0, 0x3a66afcd, 0x3a324207, 0xba0d8ec9, 0xba180b24,
 0x3a712c28, 0x3b267621, 0xba956c0d, 0x3ad4562e, 0x3abcbe62, 0x3b61719f, 0xb927c5ac, 0xbb339192,
 0xbb070111, 0x3a378034, 0x3b8b9778, 0x3b291538, 0xb951b717, 0xbb324207, 0xb9bcbe62, 0x39f12c28,
 0x3f8185f0, 0x3af12c28, 0x3afe4799, 0xbb45eb31, 0x3988509c, 0xbb3a1f4b, 0x39dc3372, 0x3b03126f,
 0x3a807358, 0xba12ccf7, 0x3b694ee4, 0x3aafa2f0, 0x3b0c3f3e, 0x3a83126f, 0xb988509c, 0x3b55a5b9,
 0x3ac49ba6, 0x39f12c28, 0xb988509c, 0x3b0c3f3e, 0x39d1b717, 0x3b2e5365, 0x3a56f545, 0xbb77b9e0,
 0x3827c5ac, 0xba88509c, 0xbb291538, 0xbb2fa2f0, 0xbb08509c, 0xbb4dc875, 0x38d1b717, 0x3aded289,
 0x3f80346e, 0x3a61719f, 0x3988509c, 0x3b1bf9c6, 0xb9a7c5ac, 0x3a980b24, 0x3a61719f, 0xba61719f,
 0x3a7ba882, 0xba180b24, 0xbb22877f, 0xba180b24, 0x3aebedfa, 0x3b40ad04, 0x3aba1f4b, 0xbad1b717,
 0xb8d1b717, 0xbac1fc8f, 0x3a473abd, 0xbaded289, 0x3a712c28, 0x3ad9945b, 0xbadc3372, 0xb99d4952,
 0x3b62c12b, 0xbbc5436c, 0xba956c0d, 0xba4c78ea, 0xbb5c3372, 0xbb6a9e6f, 0x3b324207, 0x3aaa64c3,
 0x3f80ff97, 0xbb40ad04, 0x3b09a027, 0x3a180b24, 0xba9fe868, 0x3b3630a9, 0xbae1719f, 0xb827c5ac,
 0x3a9d4952, 0x3b656042, 0x3acf1801, 0xbaa2877f, 0x3912ccf7, 0x3b9374bc, 0x3a9aaa3b, 0xbaafa2f0,
 0xbb40ad04, 0x3b2e5365, 0xbafe4799, 0x3a6bedfa, 0x3a27c5ac, 0x3a56f545, 0x3ab4e11e, 0x3a2d03da,
 0xb9f12c28, 0x3b62c12b, 0x3a766a55, 0x3b50678c, 0x3912ccf7, 0xba51b717, 0x39fba882, 0xbaf66a55,
 0x3f7f6fd2, 0xb97ba882, 0x3b45eb31, 0x3b6410b6, 0x3b1fe868, 0xbb007358, 0x3ad4562e, 0x3b45eb31,
 0xbb1d4952, 0x3a7ba882, 0x3aba1f4b, 0xbb66afcd, 0x3827c5ac, 0xbb267621, 0x38fba882, 0xbb34e11e,
 0xbb3630a9, 0xb9a7c5ac, 0xbb3e0ded, 0xbb751aca, 0xbaf3cb3e, 0x3b27c5ac, 0xbaba1f4b, 0x3a92ccf7,
 0x3a88509c, 0xbb0ede55, 0xb992ccf7, 0xba08509c, 0xb966afcd, 0xbadc3372, 0xbb378034, 0x3a03126f,
 0x3f809a02, 0xbaafa2f0, 0xba4c78ea, 0x3b3b6ed6, 0xb9a7c5ac, 0x3a83126f, 0xbb3b6ed6, 0x3ac1fc8f,
 0xbb070111, 0x3af66a55, 0x3b41fc8f, 0xba27c5ac, 0x3a03126f, 0xbb0c3f3e, 0x3ae410b6, 0xbb98b2ea,
 0xb992ccf7, 0x39bcbe62, 0xbab4e11e, 0x3827c5ac, 0x3b45eb31, 0xb9a7c5ac, 0xb87ba882, 0x397ba882,
 0xb9fba882, 0x3b83126f, 0x3b4dc875, 0x3aa2877f, 0x3aa7c5ac, 0x3ae1719f, 0xba2d03da, 0xbaf9096c,
 0x3f815810, 0xbaf12c28, 0x3b05b185, 0xba766a55, 0x3aaa64c3, 0x39dc3372, 0xbb61719f, 0x387ba882,
 0xb8d1b717, 0x3a807358, 0x39e6afcd, 0x38fba882, 0xbb766a55, 0xbaee8d11, 0xbac9d9d3, 0xbb0c3f3e,
 0x3b03126f, 0xbb49d9d3, 0xb9bcbe62, 0x3a0d8ec9, 0x3a8aefb3, 0xb87ba882, 0xbb070111, 0xbb007358,
 0x3a6bedfa, 0xbb180b24, 0x3927c5ac, 0x38fba882, 0xbad9945b, 0xbb156c0d, 0xb827c5ac, 0x3abf5d79,
 0x3f7e075f, 0x3b0ede55, 0x3b007358, 0xbb3f5d79, 0xbb1d4952, 0xb966afcd, 0x3a92ccf7, 0x3afe4799,
 0xba41fc8f, 0xbb1d4952, 0xbb5c3372, 0xbb473abd, 0xbb0461fa, 0xba92ccf7, 0xba9fe868, 0x3a1d4952,
 0xba56f545, 0x3a2d03da, 0x3a41fc8f, 0x3ac1fc8f, 0x3b12ccf7, 0xbaba1f4b, 0xba4c78ea, 0xbab24207,
 0x3a712c28, 0x38fba882, 0xb951b717, 0x3b1e98dd, 0xbb2137f4, 0xba9aaa3b, 0x3a712c28, 0x3b09a027,
 0x3f8072b0, 0xbb3f5d79, 0x3b1e98dd, 0xbb03126f, 0x3b0ede55, 0xbb3cbe62, 0x3b1bf9c6, 0x3b2fa2f0,
 0x39d1b717, 0x3b156c0d, 0x3992ccf7, 0xb9b24207, 0x3aba1f4b, 0x3b5d82fd, 0x3ae94ee4, 0x3aee8d11,
 0x3b55a5b9, 0xba712c28, 0xbb05b185, 0xba3cbe62, 0xbb3e0ded, 0x3aad03da, 0x3a180b24, 0x397ba882,
 0x3babb44e, 0xb9d1b717, 0xba4c78ea, 0x3a0d8ec9, 0xbb156c0d, 0x39b24207, 0x3966afcd, 0xb9dc3372,
 0x3f7f8a09, 0xba9d4952, 0xb97ba882, 0xb93cbe62, 0xb9b24207, 0xb9e6afcd, 0x3afe4799, 0xbbc9320e,
 0xba56f545, 0xbae94ee4, 0x3a378034, 0x3ad1b717, 0x3912ccf7, 0x3b38cfc0, 0xbb27c5ac, 0xbb8509c0,
 0x39bcbe62, 0xbb09a027, 0xbacf1801, 0x3ab78034, 0xbb5306a3, 0x3b117d6b, 0xba4c78ea, 0x3a66afcd,
 0x3acf1801, 0x3a8aefb3, 0x38d1b717, 0xbb1bf9c6, 0xbab4e11e, 0x3a378034, 0xbb59945b, 0x3a9aaa3b,
 0x3f81f213, 0x3b88f862, 0x3b01c2e3, 0xbac1fc8f, 0xbb87a8d6, 0xba9aaa3b, 0x3aa52696, 0xbb007358,
 0x3b2bb44e, 0xb9fba882, 0x3b09a027, 0xbb16bb99, 0xbb156c0d, 0xbafe4799, 0xbb291538, 0xb97ba882,
 0x3aaa64c3, 0xbb156c0d, 0xb988509c, 0xbb070111, 0xbae1719f, 0xbb5c3372, 0xb9d1b717, 0x39e6afcd,
 0xb827c5ac, 0x3a85b185, 0x39b24207, 0x39fba882, 0xbaa2877f, 0x3afba882, 0xb9e6afcd, 0x3b434c1b,
 0x3f801d7e, 0x3b956c0d, 0xba980b24, 0x3b2a64c3, 0xba12ccf7, 0x3b3630a9, 0x3aaa64c3, 0x3b1d4952,
 0x3aa52696, 0xbac9d9d3, 0xbad9945b, 0x3bab0c89, 0x3b180b24, 0x3ab24207, 0xbb40ad04, 0x3a88509c,
 0xbb449ba6, 0x3a51b717, 0x3b4c78ea, 0xbb22877f, 0xba6bedfa, 0xbb449ba6, 0x37a7c5ac, 0xbb195aaf,
 0x3a56f545, 0xb8a7c5ac, 0xbaf9096c, 0x3a902de0, 0xb9a7c5ac, 0x3a473abd, 0x3a4c78ea, 0xbb05b185,
 0x3f81999a, 0x3b5c3372, 0x3b7ba882, 0xbb267621, 0x3b3cbe62, 0x3ab24207, 0x39c73abd, 0x39b24207,
 0x393cbe62, 0xba12ccf7, 0xba4c78ea, 0x39e6afcd, 0x3a378034, 0xba08509c, 0x3ab78034, 0xbaba1f4b,
 0xbab78034, 0xbb1bf9c6, 0xbb1bf9c6, 0xbb3630a9, 0x3af3cb3e, 0x3ae6afcd, 0x3aa52696, 0xbaa7c5ac,
 0x3b156c0d, 0x3b22877f, 0xba712c28, 0x3ae94ee4, 0x3ac49ba6, 0x3b45eb31, 0x3b56f545, 0xb97ba882,
 0x3f81bda5, 0xb9dc3372, 0x3b50678c, 0xbaf3cb3e, 0xbb102de0, 0xb951b717, 0x3ae94ee4, 0x3b267621,
 0x3a0d8ec9, 0x3b05b185, 0x3b141c82, 0xb9d1b717, 0x3ba0902e, 0x3ab78034, 0xbb6d3d86, 0xbbaefb2b,
 0x3966afcd, 0x3aa52696, 0x3a3cbe62, 0x3aafa2f0, 0x3a6bedfa, 0x393cbe62, 0xb9b24207, 0x3966afcd,
 0x3b09a027, 0xbb2bb44e, 0x3b8a47ed, 0xbb4f1801, 0x39fba882, 0xbb2bb44e, 0xbafba882, 0xba8d8ec9,
 0x3f802de0, 0x3a378034, 0xb912ccf7, 0x00000000, 0x3b08509c, 0xbb141c82, 0x3aba1f4b, 0x3af12c28,
 0xbb05b185, 0x3a1d4952, 0xbb08509c, 0x397ba882, 0x3a6bedfa, 0xbaaa64c3, 0xb9f12c28, 0x38a7c5ac,
 0x3b23d70a, 0x3adc3372, 0x3a88509c, 0x3b102de0, 0x3b45eb31, 0x3af9096c, 0x3a902de0, 0xbb0aefb3,
 0x3a51b717, 0xb9c73abd, 0xbbe9f6a9, 0xb912ccf7, 0xba83126f, 0x3ab24207, 0xba8aefb3, 0xbb2d03da,
 0x3f7f0d84, 0x3a9aaa3b, 0x3992ccf7, 0x3b0c3f3e, 0x3b9ca18c, 0xbb252696, 0xbb2bb44e, 0xbb8b9778,
 0xbb488a48, 0xbaaa64c3, 0x3a712c28, 0xbb38cfc0, 0xb8d1b717, 0x3a8aefb3, 0xbb8e368f, 0x38a7c5ac,
 0x3a88509c, 0xbaafa2f0, 0xbacc78ea, 0xb9d1b717, 0x38fba882, 0x3b0d8ec9, 0xba324207, 0x397ba882,
 0x3b88f862, 0xb9fba882, 0x3afe4799, 0xbb007358, 0x393cbe62, 0x3a4c78ea, 0xbabcbe62, 0x3912ccf7,
 0x3f7cf41f, 0xbb5ae3e7, 0xb7a7c5ac, 0x39f12c28, 0xba4c78ea, 0xbb1d4952, 0x3af9096c, 0xb966afcd,
 0x3ab4e11e, 0xbb3b6ed6, 0x3a03126f, 0x3b0d8ec9, 0xbaf3cb3e, 0x3b3f5d79, 0x3a88509c, 0xbb87a8d6,
 0x3a85b185, 0x3a473abd, 0xbb22877f, 0xbad1b717, 0x3912ccf7, 0x3827c5ac, 0xba83126f, 0x3aafa2f0,
 0xba08509c, 0xbb007358, 0x3b55a5b9, 0xbb27c5ac, 0x00000000, 0x3acc78ea, 0xbaf66a55, 0x3a180b24,
 0x3f80c49c, 0x3ac49ba6, 0x387ba882, 0x399d4952, 0xbb195aaf, 0x3b22877f, 0x3b826aa9, 0xbac49ba6,
 0x3a3cbe62, 0x3b5d82fd, 0x3abf5d79, 0xb93cbe62, 0x3b01c2e3, 0x3b27c5ac, 0x39a7c5ac, 0xbab78034,
 0x3aa7c5ac, 0x3a9d4952, 0x3b2e5365, 0xba324207, 0xb992ccf7, 0x39d1b717, 0xb9b24207, 0x3b434c1b,
 0x3af3cb3e, 0x3b27c5ac, 0x3988509c, 0xbaa2877f, 0xba766a55, 0xbb252696, 0x3a83126f, 0x3b22877f,
 0x3f7f6944, 0x3912ccf7, 0xbb05b185, 0x39f12c28, 0x3af66a55, 0xbacc78ea, 0xbacc78ea, 0x3abf5d79,
 0x3b1d4952, 0xbad9945b, 0xbaa52696, 0x3b59945b, 0xbb141c82, 0xb827c5ac, 0xbb870111, 0x397ba882,
 0xba92ccf7, 0xbb117d6b, 0x3acf1801, 0x3b712c28, 0x3ae94ee4, 0x3ae410b6, 0x3ad4562e, 0xb97ba882,
 0xb7a7c5ac, 0xba1d4952, 0x3b291538, 0xb988509c, 0xbb16bb99, 0xbaf3cb3e, 0x397ba882, 0xba66afcd,
 0x3f7b089a, 0x38a7c5ac, 0xbb6410b6, 0x3ad4562e, 0xbaf9096c, 0x3b5c3372, 0x3b602214, 0xb8a7c5ac,
 0xbb51b717, 0x3a9aaa3b, 0xb9f12c28, 0xbaaa64c3, 0x39d1b717, 0x3b23d70a, 0xbadc3372, 0xbac1fc8f,
 0xba61719f, 0xbab4e11e, 0xbb45eb31, 0xbb751aca, 0xbb8b9778, 0xb827c5ac, 0xbae1719f, 0x3a712c28,
 0x3aa7c5ac, 0xba83126f, 0xb9b24207, 0x3afba882, 0x3b1aaa3b, 0x3b434c1b, 0xbaa52696, 0x3ac9d9d3,
 0x3f821ff3, 0xbafba882, 0xbaba1f4b, 0x3b23d70a, 0xb97ba882, 0xba83126f, 0x3a378034, 0xbab4e11e,
 0xba8aefb3, 0x3acc78ea, 0x387ba882, 0xba03126f, 0xbaa7c5ac, 0x3992ccf7, 0xbb291538, 0x3b102de0,
 0x3aee8d11, 0x3a9aaa3b, 0x3a08509c, 0xba1d4952, 0x3b01c2e3, 0x3acf1801, 0x3a3cbe62, 0x3ab78034,
 0x3ab78034, 0x3a41fc8f, 0x3a378034, 0xba956c0d, 0xba1d4952, 0xbabcbe62, 0x3988509c, 0x3b22877f,
 0x3f812d77, 0x39b24207, 0xbb1aaa3b, 0x3b1d4952, 0xbb488a48, 0xbb5c3372, 0x39f12c28, 0x3966afcd,
 0xbb5ed289, 0x393cbe62, 0xba324207, 0x3951b717, 0xba9aaa3b, 0x3a712c28, 0x3b070111, 0x3b1e98dd,
 0xb988509c, 0xb912ccf7, 0xbb252696, 0x3b50678c, 0xba9aaa3b, 0x3a27c5ac, 0x3951b717, 0xb9f12c28,
 0xbb712c28, 0x3ae410b6, 0x3b34e11e, 0x3a4c78ea, 0x39c73abd, 0xbaba1f4b, 0x38fba882, 0xb9fba882,
 0x3f7f1aa0, 0x3b2fa2f0, 0x3a180b24, 0xbb0461fa, 0xb87ba882, 0x3a5c3372, 0xbb8f861a, 0xba712c28,
 0xb9dc3372, 0x3b0ede55, 0xba6bedfa, 0xb9f12c28, 0xba324207, 0xbb6d3d86, 0x3af66a55, 0x3ac73abd,
 0xbacc78ea, 0x3a9d4952, 0xba0d8ec9, 0x3a88509c, 0x39dc3372, 0x3b2a64c3, 0xbaded289, 0x3b5d82fd,
 0x3b195aaf, 0x3a5c3372, 0xbb6bedfa, 0x3a6bedfa, 0xbac73abd, 0x3b473abd, 0xbb27c5ac, 0xbb712c28,
 0x3f8020c5, 0xbb27c5ac, 0x3aa2877f, 0x3a41fc8f, 0x3b0aefb3, 0xb7a7c5ac, 0x3ae1719f, 0xbac49ba6,
 0x3b378034, 0x3927c5ac, 0x3af9096c, 0x3b09a027, 0x3a8aefb3, 0xba378034, 0xba56f545, 0xbae94ee4,
 0x3b03126f, 0xbad4562e, 0x3aaa64c3, 0x3a473abd, 0xbb811b1e, 0x3b2d03da, 0xbac1fc8f, 0x3b4c78ea,
 0x3ac1fc8f, 0x3a8d8ec9, 0xbb66afcd, 0xbaafa2f0, 0x3bc2a455, 0x3992ccf7, 0x3aded289, 0x3a766a55,
 0x3f828c15, 0x39a7c5ac, 0x3aa2877f, 0xbab24207, 0x3a56f545, 0x3a9aaa3b, 0x3a9d4952, 0x3b1bf9c6,
 0xbb1d4952, 0x3b6a9e6f, 0x3988509c, 0xbb56f545, 0xb9f12c28, 0x3951b717, 0x3b1d4952, 0xbba137f4,
 0xba7ba882, 0xbac1fc8f, 0x3b4c78ea, 0xbb0aefb3, 0xb966afcd, 0x00000000, 0x3b3e0ded, 0xba766a55,
 0xba12ccf7, 0x3a807358, 0x39e6afcd, 0x39fba882, 0x38fba882, 0xbb070111, 0xb8fba882, 0x3b0aefb3,
 0x3f800347, 0xba902de0, 0x3aad03da, 0x39bcbe62, 0xbaf9096c, 0x3b4f1801, 0xbb01c2e3, 0xba56f545,
 0xbba91538, 0xbb22877f, 0xb9b24207, 0xbb5306a3, 0xbb66afcd, 0xba83126f, 0x3b488a48, 0xb7a7c5ac,
 0x3a473abd, 0x3b195aaf, 0x3a51b717, 0x3b22877f, 0xba766a55, 0x3abf5d79, 0xbb08509c, 0x3b2137f4,
 0x3ac49ba6, 0x39bcbe62, 0x39c73abd, 0xba956c0d, 0xba766a55, 0xbb4c78ea, 0x38fba882, 0xba92ccf7,
 0x3f80f5c3, 0x3a0d8ec9, 0x3ac49ba6, 0x3b88509c, 0x3a0d8ec9, 0x3b89a027, 0x3b73cb3e, 0x3b339192,
 0xba56f545, 0x3aebedfa, 0xbad9945b, 0x3b4c78ea, 0xbb77b9e0, 0xbb08509c, 0xbacf1801, 0x3aee8d11,
 0x3b995aaf, 0xba83126f, 0xba66afcd, 0xb912ccf7, 0x3951b717, 0xba807358, 0x3b23d70a, 0x3acc78ea,
 0xb9dc3372, 0xbaf9096c, 0x3b01c2e3, 0x3a56f545, 0x3966afcd, 0x3a3cbe62, 0xbb67ff58, 0x39bcbe62,
 0x3f7af4f1, 0xb9e6afcd, 0x3a08509c, 0xba61719f, 0x3a03126f, 0x3afe4799, 0xb927c5ac, 0xbacc78ea,
 0x3b6e8d11, 0x3b870111, 0xb9dc3372, 0xb97ba882, 0xb8d1b717, 0x3a22877f, 0xba3cbe62, 0xb8d1b717,
 0x3a61719f, 0x3ac49ba6, 0xbaf3cb3e, 0x3a88509c, 0x3b0461fa, 0xbab4e11e, 0xb9bcbe62, 0x3ae94ee4,
 0xba4c78ea, 0x3b2137f4, 0x3b90d5a6, 0xbad1b717, 0x3a12ccf7, 0x39dc3372, 0x3a12ccf7, 0x3b324207,
 0x3f7f4880, 0x3b324207, 0x3b61719f, 0x3ac9d9d3, 0x3a85b185, 0xbb45eb31, 0xbb141c82, 0xbbb0f27c,
 0x3ae6afcd, 0x3a56f545, 0xbb2a64c3, 0xbadc3372, 0xbb4dc875, 0xb8d1b717, 0x3b0ede55, 0x3adc3372,
 0x3a378034, 0x3acf1801, 0xba1d4952, 0xbb27c5ac, 0xba61719f, 0x3a83126f, 0x39a7c5ac, 0x3992ccf7,
 0x3aaa64c3, 0x3b8b9778, 0x3ac49ba6, 0x3b05b185, 0x397ba882, 0xb9a7c5ac, 0x3b34e11e, 0x3a1d4952,
 0x3f7b851f, 0xba08509c, 0xbacc78ea, 0xba807358, 0xb9fba882, 0x3a88509c, 0x00000000, 0xbb30f27c,
 0xbacc78ea, 0x39dc3372, 0xbaa2877f, 0xb912ccf7, 0x3ae410b6, 0x37a7c5ac, 0xba83126f, 0x3b08509c,
 0xbae410b6, 0xbafba882, 0xb99d4952, 0x38fba882, 0x3ba3d70a, 0x3b05b185, 0xbb0ede55, 0x3ae1719f,
 0x3912ccf7, 0xb827c5ac, 0x3b0461fa, 0xbae410b6, 0x3aded289, 0xb9bcbe62, 0x39bcbe62, 0x3a902de0,
 0x3f7e075f, 0x3b2137f4, 0xb9dc3372, 0x3b007358, 0x3afba882, 0xbaf66a55, 0xbb59945b, 0x3951b717,
 0x3b6fdc9c, 0xbb49d9d3, 0x3a61719f, 0xbaf66a55, 0xbb22877f, 0xbb83ba34, 0x3a9d4952, 0x393cbe62,
 0x3af12c28, 0xbaf12c28, 0xba27c5ac, 0xbaad03da, 0x3a2d03da, 0x3a22877f, 0xbae410b6, 0xb9a7c5ac,
 0x3b449ba6, 0xba51b717, 0xba9d4952, 0x3b156c0d, 0x39a7c5ac, 0x3b070111, 0x3a956c0d, 0x3acf1801,
 0x3f8013a9, 0xbb73cb3e, 0x39fba882, 0xba88509c, 0x3a9fe868, 0x3a03126f, 0xba807358, 0xba980b24,
 0x3b0aefb3, 0xbb2137f4, 0x3a4c78ea, 0xb8a7c5ac, 0x3b1aaa3b, 0x3a9aaa3b, 0xba766a55, 0x39bcbe62,
 0xb9f12c28, 0xbb23d70a, 0x39b24207, 0x39fba882, 0x3a8d8ec9, 0x39b24207, 0xbb141c82, 0x39dc3372,
 0x3a7ba882, 0x3988509c, 0x3b01c2e3, 0xbae1719f, 0xb992ccf7, 0x3b5306a3, 0xba807358, 0x3a378034,
 0x3f80be0e, 0xb951b717, 0xb9f12c28, 0xbabf5d79, 0x3b1bf9c6, 0xb99d4952, 0xb9f12c28, 0xbb117d6b,
 0xba12ccf7, 0xb966afcd, 0x3a473abd, 0xbad9945b, 0x39b24207, 0xb8a7c5ac, 0xba22877f, 0xb9dc3372,
 0xbae6afcd, 0xba378034, 0xbb5306a3, 0xbbb24207, 0xba9aaa3b, 0xbabcbe62, 0xba7ba882, 0x3a766a55,
 0xb9c73abd, 0x3b727bb3, 0xb9f12c28, 0xba92ccf7, 0x39dc3372, 0x3a88509c, 0x3ac9d9d3, 0xba9aaa3b,
 0x3f7e6320, 0x3a8d8ec9, 0x3b070111, 0x3b49d9d3, 0x3acf1801, 0xbacf1801, 0x3a712c28, 0x3acc78ea,
 0x3bab0c89, 0xba8d8ec9, 0xbb62c12b, 0xb8d1b717, 0xbacc78ea, 0xbab4e11e, 0xbb3a1f4b, 0x3b8aefb3,
 0x3b73cb3e, 0xb7a7c5ac, 0x3b73cb3e, 0xbab24207, 0x3b0c3f3e, 0xba6bedfa, 0xbb0c3f3e, 0x3ac49ba6,
 0xb8a7c5ac, 0x3b5306a3, 0x3adc3372, 0xbb807358, 0x39a7c5ac, 0xbbc154ca, 0x3b6e8d11, 0x3ad1b717,
 0x3f7ecbfb, 0x39fba882, 0xbb08509c, 0xbb0ede55, 0xbab24207, 0xb87ba882, 0x3abcbe62, 0x3ac73abd,
 0xbac9d9d3, 0xba4c78ea, 0x3a9d4952, 0x3b49d9d3, 0x3ab4e11e, 0x3a712c28, 0xba9aaa3b, 0xba980b24,
 0x3a12ccf7, 0x3b434c1b, 0xbb50678c, 0xbadc3372, 0xbac73abd, 0xbb2e5365, 0xbae6afcd, 0x3b27c5ac,
 0xba4c78ea, 0x3966afcd, 0xba7ba882, 0x3bae5365, 0xba56f545, 0xb951b717, 0xba473abd, 0xba8aefb3,
 0x3f824dd3, 0xb9e6afcd, 0x3a56f545, 0x3ab78034, 0x3b0461fa, 0x3b0ede55, 0xbaa52696, 0xbadc3372,
 0xbaf9096c, 0xb99d4952, 0xbaa52696, 0xba51b717, 0x3a180b24, 0x3a980b24, 0x3b3f5d79, 0xb966afcd,
 0xb827c5ac, 0x3827c5ac, 0x3b102de0, 0xb9f12c28, 0x3a712c28, 0x3927c5ac, 0x3abf5d79, 0xbb2d03da,
 0xbb751aca, 0xbb05b185, 0x39d1b717, 0xbb5844d0, 0xb97ba882, 0x38a7c5ac, 0xba08509c, 0x3ab4e11e,
 0x3f7d42c4, 0xba902de0, 0x3b3a1f4b, 0xbb291538, 0xb9d1b717, 0x3a03126f, 0x3adc3372, 0x3b0aefb3,
 0x3a902de0, 0x387ba882, 0x3ad1b717, 0xbaebedfa, 0xb951b717, 0x3aee8d11, 0xba956c0d, 0xbb12ccf7,
 0xbae410b6, 0xbaebedfa, 0xba88509c, 0x3827c5ac, 0x3827c5ac, 0x3ad4562e, 0x3a12ccf7, 0xbadc3372,
 0x3b267621, 0xba8aefb3, 0x3a473abd, 0x3912ccf7, 0xba8d8ec9, 0x3b180b24, 0x3b473abd, 0x393cbe62,
 0x3f805879, 0xbb007358, 0xbb007358, 0xbae410b6, 0xbba7c5ac, 0x3b0aefb3, 0xb97ba882, 0xba766a55,
 0xbb2e5365, 0x00000000, 0x3a4c78ea, 0x3a12ccf7, 0x3a66afcd, 0x3a61719f, 0x3adc3372, 0x397ba882,
 0x3ab4e11e, 0xb827c5ac, 0xbaa52696, 0x3a22877f, 0xbb6bedfa, 0x3ae94ee4, 0x3aebedfa, 0xbb2e5365,
 0x3a473abd, 0x3b1bf9c6, 0xba378034, 0x38fba882, 0xba956c0d, 0xbb180b24, 0xba807358, 0x39c73abd,
 0x3f8126e9, 0xbad4562e, 0xba12ccf7, 0x3b751aca, 0x3b5d82fd, 0xbb1bf9c6, 0xba180b24, 0xbb0ede55,
 0x3a7ba882, 0x3966afcd, 0xb9fba882, 0xba6bedfa, 0x3a61719f, 0xbab4e11e, 0x39dc3372, 0x37a7c5ac,
 0x3a8aefb3, 0x3ae94ee4, 0xb927c5ac, 0x39d1b717, 0xbbd0678c, 0x3a92ccf7, 0x3a66afcd, 0xba324207,
 0x3a03126f, 0x3a27c5ac, 0x3a7ba882, 0x3afba882, 0x39bcbe62, 0x3927c5ac, 0x3ab24207, 0x39dc3372,
 0x3f830be1, 0xb9a7c5ac, 0x3b88509c, 0x38a7c5ac, 0xbb0ede55, 0xbb09a027, 0x3a8d8ec9, 0x3a766a55,
 0xb827c5ac, 0x3a956c0d, 0x3b694ee4, 0xba22877f, 0xb912ccf7, 0x3af9096c, 0x3aebedfa, 0x3a03126f,
 0xbb2bb44e, 0x3adc3372, 0xbb0461fa, 0x3b61719f, 0x3a51b717, 0xb97ba882, 0x3a0d8ec9, 0xb9fba882,
 0x3a0d8ec9, 0xbbb04ab6, 0x3b8ce704, 0xbb0ede55, 0x3a61719f, 0x37a7c5ac, 0x3a92ccf7, 0xb912ccf7,
 0x3f7e075f, 0xbb61719f, 0x3b9613d3, 0x3aa52696, 0xb97ba882, 0xb9e6afcd, 0xbb4b295f, 0x3ac73abd,
 0xbaf66a55, 0xba378034, 0xbb79096c, 0x3a22877f, 0xba2d03da, 0x3a3cbe62, 0x3ab24207, 0x3b6fdc9c,
 0xba51b717, 0x39bcbe62, 0xbb97635e, 0xbb40ad04, 0xbb141c82, 0xb9fba882, 0x3a9aaa3b, 0xbadc3372,
 0x3b434c1b, 0xb951b717, 0xb992ccf7, 0x37a7c5ac, 0x3b79096c, 0xbb712c28, 0x3b08509c, 0x3b0aefb3,
 0x3f7e703b, 0xbaf12c28, 0x3a41fc8f, 0xbb324207, 0x3966afcd, 0xbae6afcd, 0xbb96bb99, 0xba27c5ac,
 0xbaded289, 0x3a8aefb3, 0xbb473abd, 0x3a27c5ac, 0x39c73abd, 0x3a7ba882, 0x3a56f545, 0xb9bcbe62,
 0x3a08509c, 0xb87ba882, 0xba03126f, 0xb99d4952, 0xb93cbe62, 0x3aba1f4b, 0xb912ccf7, 0x3988509c,
 0xbb694ee4, 0xb966afcd, 0xbb1fe868, 0x3b23d70a, 0xb99d4952, 0x38a7c5ac, 0x39a7c5ac, 0x3992ccf7,
 0x3f78ef35, 0x3a902de0, 0x3b156c0d, 0x3af66a55, 0xba4c78ea, 0xb927c5ac, 0xbb766a55, 0x3abcbe62,
 0x3a51b717, 0x00000000, 0x3992ccf7, 0x3af66a55, 0xbbbe0ded, 0xba41fc8f, 0xba8d8ec9, 0xbaf12c28,
 0x3b0d8ec9, 0x3a324207, 0xbaf66a55, 0xbb0aefb3, 0xbb08509c, 0xbab24207, 0x3b09a027, 0xba6bedfa,
 0xba8aefb3, 0x3aad03da, 0xba5c3372, 0xbb01c2e3, 0xb9c73abd, 0x3b01c2e3, 0xbb0aefb3, 0x3ab24207,
 0x3f7ef34d, 0xb9fba882, 0x38d1b717, 0x3a03126f, 0xbb7a58f7, 0x3a92ccf7, 0xba980b24, 0x3b2bb44e,
 0x3aafa2f0, 0xba56f545, 0xbb070111, 0x3ae1719f, 0xba61719f, 0x3a51b717, 0xbb602214, 0xba22877f,
 0x00000000, 0x3ac49ba6, 0xba8aefb3, 0xba12ccf7, 0xb93cbe62, 0xbb378034, 0xba473abd, 0x39b24207,
 0xbb0ede55, 0x3bc5436c, 0xba0d8ec9, 0x3a902de0, 0x3b03126f, 0x3b0c3f3e, 0x3bb24207, 0x397ba882,
 0x3f812a30, 0x3ac73abd, 0xbb0ede55, 0xb9b24207, 0xba12ccf7, 0x3b0aefb3, 0x3af9096c, 0xb9dc3372,
 0x39d1b717, 0xbb5306a3, 0xb8a7c5ac, 0x39c73abd, 0x3b2d03da, 0x39f12c28, 0xbb0ede55, 0x3afe4799,
 0x3a8aefb3, 0x3ac9d9d3, 0x3b1fe868, 0x3a66afcd, 0xba378034, 0xbb90d5a6, 0xbb9e98dd, 0x3a66afcd,
 0x3a12ccf7, 0x3a12ccf7, 0xbb751aca, 0x3a3cbe62, 0x3bcb295f, 0xbb0c3f3e, 0x3927c5ac, 0x3a807358,
 0x3f804817, 0xba22877f, 0x3af66a55, 0x3a92ccf7, 0xb93cbe62, 0x3b01c2e3, 0x3a8d8ec9, 0x3ba9bcfd,
 0xb827c5ac, 0xb8d1b717, 0x3a980b24, 0x3adc3372, 0xba6bedfa, 0x3abf5d79, 0xb912ccf7, 0xba4c78ea,
 0xba88509c, 0xbb16bb99, 0xb9f12c28, 0x3b117d6b, 0xbb73cb3e, 0x3b811b1e, 0x3ba2877f, 0xba0d8ec9,
 0xbbadab9f, 0xbb16bb99, 0xba61719f, 0x3b1fe868, 0x39dc3372, 0xba12ccf7, 0xbb12ccf7, 0x3b01c2e3,
 0x3f7c49ba, 0xba5c3372, 0xbb339192, 0x3abf5d79, 0x3b3e0ded, 0xbaa52696, 0xbb2fa2f0, 0xb966afcd,
 0xba12ccf7, 0xbb751aca, 0x38a7c5ac, 0x3b5844d0, 0x3b434c1b, 0x3b4b295f, 0xbaa52696, 0xbaad03da,
 0x3a03126f, 0xba980b24, 0xbb34e11e, 0x39fba882, 0xb9f12c28, 0xbaa7c5ac, 0xbb3b6ed6, 0xbb01c2e3,
 0x3b5ae3e7, 0xbb27c5ac, 0xbae6afcd, 0x39a7c5ac, 0x3966afcd, 0xbaa7c5ac, 0x3b6410b6, 0xbb30f27c,
 0x3f7b7e91, 0xba03126f, 0xba12ccf7, 0xbb5306a3, 0xbab4e11e, 0x3a980b24, 0xba56f545, 0x3a712c28,
 0xbb2d03da, 0x3a56f545, 0xbad6f545, 0xbb1fe868, 0xba56f545, 0xba8aefb3, 0xbae94ee4, 0x3aee8d11,
 0x3b08509c, 0x3b180b24, 0x3b007358, 0xbb007358, 0xbaafa2f0, 0x3a61719f, 0x3a7ba882, 0xbb0461fa,
 0x3a7ba882, 0xbac9d9d3, 0xbb1d4952, 0x3951b717, 0x3b195aaf, 0xbb156c0d, 0x3ac1fc8f, 0x3a08509c,
 0x3f8212d7, 0x3a83126f, 0x00000000, 0x3827c5ac, 0xbb9374bc, 0x399d4952, 0xbad6f545, 0x3ae410b6,
 0x3aebedfa, 0x3b12ccf7, 0x37a7c5ac, 0xb99d4952, 0xb9d1b717, 0xba03126f, 0xbb1fe868, 0x3b378034,
 0x3af66a55, 0xba8aefb3, 0xbb38cfc0, 0x3b434c1b, 0xba22877f, 0xbb291538, 0xbafba882, 0xba8aefb3,
 0x3a9d4952, 0x3aee8d11, 0x3ab78034, 0xbb2bb44e, 0x3af9096c, 0xbb23d70a, 0x3b1e98dd, 0x3ab24207,
 0x3f80d4fe, 0x3b49d9d3, 0xbb7e4799, 0xbaba1f4b, 0x3ab24207, 0xbb54562e, 0x3a0d8ec9, 0xba324207,
 0xb8a7c5ac, 0x3b9d4952, 0x3a56f545, 0xbb3e0ded, 0xbb66afcd, 0x3b12ccf7, 0xba712c28, 0x393cbe62,
 0x3b811b1e, 0xba6bedfa, 0xbb180b24, 0x3b7f9724, 0x3a2d03da, 0xbab78034, 0xbb1aaa3b, 0xbb117d6b,
 0x397ba882, 0x3951b717, 0xba66afcd, 0x39a7c5ac, 0x3b0ede55, 0xbb8ce704, 0xba56f545, 0xba5c3372,
 0x3f7cf41f, 0x3a88509c, 0x3b22877f, 0x3b9ca18c, 0xbb5ed289, 0x3aaa64c3, 0xbb102de0, 0xbab24207,
 0xb9bcbe62, 0x39e6afcd, 0xb9a7c5ac, 0x3988509c, 0xbae1719f, 0x3927c5ac, 0x3b41fc8f, 0xbb92ccf7,
 0x3a180b24, 0xbabf5d79, 0xbae6afcd, 0x3a12ccf7, 0x3a180b24, 0xbb87a8d6, 0x39d1b717, 0x3b6fdc9c,
 0xbb141c82, 0x3a324207, 0xbb0d8ec9, 0x3af9096c, 0x3a08509c, 0x3aaa64c3, 0xb93cbe62, 0xb988509c,
 0x3f803e42, 0x3a7ba882, 0xbabf5d79, 0x3aa52696, 0xba902de0, 0xbb05b185, 0x3aa2877f, 0xbad4562e,
 0x3a27c5ac, 0x3b61719f, 0x38fba882, 0xbb30f27c, 0x3ba2877f, 0x3a4c78ea, 0x3aaa64c3, 0xba1d4952,
 0x3ae1719f, 0x3a1d4952, 0xbacc78ea, 0xb9f12c28, 0xb951b717, 0xbb3b6ed6, 0xb9a7c5ac, 0x3a08509c,
 0xba8d8ec9, 0x3b5306a3, 0xbaded289, 0xba27c5ac, 0x3b7ba882, 0x3b1e98dd, 0x3b41fc8f, 0xb9b24207,
 0x3f7e00d2, 0xb93cbe62, 0xbb267621, 0xbae410b6, 0x38fba882, 0x3b05b185, 0x3a473abd, 0x3a712c28,
 0x3b30f27c, 0xba766a55, 0x38a7c5ac, 0x3a473abd, 0x3ac49ba6, 0xb988509c, 0x3a5c3372, 0xbb22877f,
 0xbb291538, 0xba51b717, 0x3b252696, 0x39fba882, 0x3b2bb44e, 0xba92ccf7, 0x3b291538, 0x3a27c5ac,
 0x3b67ff58, 0xba324207, 0x3988509c, 0x39c73abd, 0xba12ccf7, 0x3a378034, 0xbb49d9d3, 0xbaaa64c3,
 0x3f7e2196, 0xba3cbe62, 0x3a473abd, 0xba956c0d, 0x3ae94ee4, 0xb9b24207, 0xba324207, 0x3b766a55,
 0x3a22877f, 0xbac49ba6, 0xbb378034, 0xb9dc3372, 0x3a4c78ea, 0x3b102de0, 0x39fba882, 0xbac1fc8f,
 0x3b449ba6, 0x3b141c82, 0xba27c5ac, 0xbb1aaa3b, 0xba41fc8f, 0xb9c73abd, 0xbb1d4952, 0xba03126f,
 0x3a980b24, 0xb8a7c5ac, 0x3aba1f4b, 0xb966afcd, 0x3a712c28, 0x393cbe62, 0xbb3630a9, 0x3aad03da
};
