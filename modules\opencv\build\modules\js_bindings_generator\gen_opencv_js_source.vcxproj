﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{9A3FFBD3-6827-3FFB-986E-33E18091B5DA}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>gen_opencv_js_source</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\AI\opencv\cudabuild\CMakeFiles\3e01e28ea25ca1305f9a03ec4c8891c5\bindings.cpp.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generate source files for JavaScript bindings</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd D:\AI\opencv\cudabuild\modules\js_bindings_generator\gen
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe D:/AI/opencv/opencv-4.10.0/modules/js/generator/embindgen.py D:/AI/opencv/opencv-4.10.0/modules/js/generator/../../python/src2/hdr_parser.py D:/AI/opencv/cudabuild/modules/js_bindings_generator/gen/bindings.cpp D:/AI/opencv/cudabuild/modules/js_bindings_generator/headers.txt D:/AI/opencv/opencv-4.10.0/modules/js/generator/../src/core_bindings.cpp D:/AI/opencv/opencv-4.10.0/platforms/js/opencv_js.config.py
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch D:/AI/opencv/cudabuild/CMakeFiles/dephelper/gen_opencv_js_source
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\opencv-4.10.0\modules\js\src\core_bindings.cpp;D:\AI\opencv\opencv-4.10.0\modules\js\generator\embindgen.py;D:\AI\opencv\opencv-4.10.0\modules\js\generator\templates.py;D:\AI\opencv\opencv-4.10.0\platforms\js\opencv_js.config.py;D:\AI\opencv\opencv-4.10.0\modules\python\src2\hdr_parser.py;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\backend\parallel_for.openmp.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\backend\parallel_for.tbb.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\parallel_backend.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\affine.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\async.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\base.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\bindings_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\bufferpool.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\check.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd_wrapper.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\directx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\dualquaternion.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\dualquaternion.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\eigen.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\fast_math.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\mat.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\mat.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\matx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\matx.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\neon_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\ocl_genbase.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\operations.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\optim.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\ovx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\persistence.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\private.cuda.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\quaternion.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\quaternion.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\saturate.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\simd_intrinsics.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\softfloat.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\sse_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\traits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\types.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utility.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\allocator_stats.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\allocator_stats.impl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\buffer_area.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\configuration.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\filesystem.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\filesystem.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\fp_control.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\fp_control_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\lock.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logger.defines.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logger.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logtag.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\plugin_loader.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\tls.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\va_intel.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\version.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\vsx_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\async_promise.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\dispatch_helper.impl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\exception_ptr.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\bindings.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\hal\hal.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\imgproc.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\segmentation.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\detail\gcgraph.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\detail\legacy.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\all_layers.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dict.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dnn.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dnn.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer.details.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer_reg.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\shape_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\utils\debug_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\utils\inference_engine.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\version.hpp;D:\AI\opencv\opencv-4.10.0\modules\features2d\include\opencv2\features2d.hpp;D:\AI\opencv\opencv-4.10.0\modules\features2d\include\opencv2\features2d\features2d.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo\cuda.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo\photo.hpp;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include\opencv2\calib3d.hpp;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include\opencv2\calib3d\calib3d.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_board.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_dictionary.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\barcode.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\charuco_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\face.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\graphical_code_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\objdetect.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\background_segm.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\tracking.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\video.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\detail\tracking.detail.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\wechat_qrcode\include\opencv2\wechat_qrcode.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco\aruco_calib.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco\charuco.hpp;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\cudabuild\modules\js_bindings_generator\gen\bindings.cpp;D:\AI\opencv\cudabuild\CMakeFiles\dephelper\gen_opencv_js_source</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generate source files for JavaScript bindings</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd D:\AI\opencv\cudabuild\modules\js_bindings_generator\gen
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe D:/AI/opencv/opencv-4.10.0/modules/js/generator/embindgen.py D:/AI/opencv/opencv-4.10.0/modules/js/generator/../../python/src2/hdr_parser.py D:/AI/opencv/cudabuild/modules/js_bindings_generator/gen/bindings.cpp D:/AI/opencv/cudabuild/modules/js_bindings_generator/headers.txt D:/AI/opencv/opencv-4.10.0/modules/js/generator/../src/core_bindings.cpp D:/AI/opencv/opencv-4.10.0/platforms/js/opencv_js.config.py
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch D:/AI/opencv/cudabuild/CMakeFiles/dephelper/gen_opencv_js_source
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\opencv-4.10.0\modules\js\src\core_bindings.cpp;D:\AI\opencv\opencv-4.10.0\modules\js\generator\embindgen.py;D:\AI\opencv\opencv-4.10.0\modules\js\generator\templates.py;D:\AI\opencv\opencv-4.10.0\platforms\js\opencv_js.config.py;D:\AI\opencv\opencv-4.10.0\modules\python\src2\hdr_parser.py;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\backend\parallel_for.openmp.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\backend\parallel_for.tbb.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\parallel_backend.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\affine.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\async.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\base.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\bindings_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\bufferpool.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\check.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd_wrapper.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\directx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\dualquaternion.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\dualquaternion.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\eigen.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\fast_math.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\mat.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\mat.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\matx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\matx.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\neon_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\ocl_genbase.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\operations.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\optim.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\ovx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\persistence.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\private.cuda.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\quaternion.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\quaternion.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\saturate.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\simd_intrinsics.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\softfloat.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\sse_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\traits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\types.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utility.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\allocator_stats.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\allocator_stats.impl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\buffer_area.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\configuration.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\filesystem.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\filesystem.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\fp_control.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\fp_control_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\lock.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logger.defines.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logger.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logtag.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\plugin_loader.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\tls.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\va_intel.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\version.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\vsx_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\async_promise.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\dispatch_helper.impl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\exception_ptr.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\bindings.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\hal\hal.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\imgproc.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\segmentation.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\detail\gcgraph.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\detail\legacy.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\all_layers.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dict.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dnn.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dnn.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer.details.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer_reg.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\shape_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\utils\debug_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\utils\inference_engine.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\version.hpp;D:\AI\opencv\opencv-4.10.0\modules\features2d\include\opencv2\features2d.hpp;D:\AI\opencv\opencv-4.10.0\modules\features2d\include\opencv2\features2d\features2d.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo\cuda.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo\photo.hpp;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include\opencv2\calib3d.hpp;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include\opencv2\calib3d\calib3d.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_board.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_dictionary.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\barcode.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\charuco_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\face.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\graphical_code_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\objdetect.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\background_segm.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\tracking.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\video.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\detail\tracking.detail.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\wechat_qrcode\include\opencv2\wechat_qrcode.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco\aruco_calib.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco\charuco.hpp;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\cudabuild\modules\js_bindings_generator\gen\bindings.cpp;D:\AI\opencv\cudabuild\CMakeFiles\dephelper\gen_opencv_js_source</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\AI\opencv\cudabuild\CMakeFiles\77872ca115049222fae6cfb40204c4c2\gen_opencv_js_source.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\cudabuild\modules\js_bindings_generator\gen\bindings.cpp;D:\AI\opencv\cudabuild\CMakeFiles\dephelper\gen_opencv_js_source;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\cudabuild\modules\js_bindings_generator\CMakeFiles\gen_opencv_js_source</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\cudabuild\modules\js_bindings_generator\gen\bindings.cpp;D:\AI\opencv\cudabuild\CMakeFiles\dephelper\gen_opencv_js_source;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\cudabuild\modules\js_bindings_generator\CMakeFiles\gen_opencv_js_source</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\AI\opencv\opencv-4.10.0\modules\js\src\core_bindings.cpp">
    </None>
    <None Include="D:\AI\opencv\opencv-4.10.0\modules\js\generator\embindgen.py">
    </None>
    <None Include="D:\AI\opencv\opencv-4.10.0\modules\js\generator\templates.py">
    </None>
    <None Include="D:\AI\opencv\cudabuild\modules\js_bindings_generator\CMakeFiles\gen_opencv_js_source">
    </None>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>