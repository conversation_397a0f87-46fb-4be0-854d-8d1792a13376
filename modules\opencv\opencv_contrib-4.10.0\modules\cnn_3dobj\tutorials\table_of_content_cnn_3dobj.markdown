CNN for 3D Object Classification and Pose Estimation {#tutorial_table_of_content_cnn_3dobj}
==========

-   @subpage tutorial_data_generation

    *Compatibility:* \> OpenCV 3.0.0

    *Author:* <PERSON><PERSON>

    You will learn how to generate training images from 3D models with proper poses for CNN training.

-   @subpage tutorial_feature_classification

    *Compatibility:* \> OpenCV 3.0.0

    *Author:* <PERSON><PERSON>

    You will learn how to extract features from images and make a prediction using descriptor.

-   @subpage tutorial_model_analysis

    *Compatibility:* \> OpenCV 3.0.0

    *Author:* <PERSON><PERSON>

    You will learn how to analyze performance of a trained Model.
