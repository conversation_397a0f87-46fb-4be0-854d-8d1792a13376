// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_AI_Actions_0_H
#define WINRT_Windows_AI_Actions_0_H
WINRT_EXPORT namespace winrt::Windows::AI::Actions::Hosting
{
    struct ActionCatalog;
}
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Contacts
{
    struct Contact;
}
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    struct EventRegistrationToken;
    struct HResult;
    template <typename TSender, typename TResult> struct WINRT_IMPL_EMPTY_BASES TypedEventHandler;
    struct Uri;
}
WINRT_EXPORT namespace winrt::Windows::UI
{
    struct WindowId;
}
WINRT_EXPORT namespace winrt::Windows::AI::Actions
{
    enum class ActionEntityKind : int32_t
    {
        None = 0,
        Document = 1,
        File = 2,
        Photo = 3,
        Text = 4,
        StreamingText = 5,
        RemoteFile = 6,
        Table = 7,
        Contact = 8,
    };
    enum class ActionEntityTextFormat : int32_t
    {
        Plain = 0,
        Markdown = 1,
    };
    enum class ActionFeedbackKind : int32_t
    {
        Positive = 0,
        Negative = 1,
    };
    enum class ActionInvocationHelpKind : int32_t
    {
        None = 0,
        Error = 1,
        Warning = 2,
    };
    enum class ActionInvocationResult : int32_t
    {
        Success = 0,
        UserCanceled = 1,
        Unsupported = 2,
        Unavailable = 3,
    };
    enum class RemoteFileKind : int32_t
    {
        Document = 0,
        Photo = 1,
        File = 2,
    };
    struct IActionEntity;
    struct IActionEntity2;
    struct IActionEntityDisplayInfo;
    struct IActionEntityFactory;
    struct IActionEntityFactory2;
    struct IActionEntityFactory3;
    struct IActionEntityFactory4;
    struct IActionEntityFactoryFactory;
    struct IActionFeedback;
    struct IActionInvocationContext;
    struct IActionInvocationContext2;
    struct IActionInvocationHelpDetails;
    struct IActionRuntime;
    struct IActionRuntime2;
    struct IActionRuntime3;
    struct IActionRuntimeFactory;
    struct IContactActionEntity;
    struct IDocumentActionEntity;
    struct IFileActionEntity;
    struct INamedActionEntity;
    struct IPhotoActionEntity;
    struct IRemoteFileActionEntity;
    struct IStreamingTextActionEntity;
    struct IStreamingTextActionEntityTextChangedArgs;
    struct IStreamingTextActionEntityWriter;
    struct ITableActionEntity;
    struct ITextActionEntity;
    struct ITextActionEntity2;
    struct ActionEntity;
    struct ActionEntityDisplayInfo;
    struct ActionEntityFactory;
    struct ActionFeedback;
    struct ActionInvocationContext;
    struct ActionInvocationHelpDetails;
    struct ActionRuntime;
    struct ContactActionEntity;
    struct DocumentActionEntity;
    struct FileActionEntity;
    struct NamedActionEntity;
    struct PhotoActionEntity;
    struct RemoteFileActionEntity;
    struct StreamingTextActionEntity;
    struct StreamingTextActionEntityTextChangedArgs;
    struct StreamingTextActionEntityWriter;
    struct TableActionEntity;
    struct TextActionEntity;
    struct ActionsContract;
}
namespace winrt::impl
{
    template <> struct category<winrt::Windows::AI::Actions::IActionEntity>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IActionEntity2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IActionEntityDisplayInfo>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IActionEntityFactory>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IActionEntityFactory2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IActionEntityFactory3>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IActionEntityFactory4>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IActionEntityFactoryFactory>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IActionFeedback>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IActionInvocationContext>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IActionInvocationContext2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IActionInvocationHelpDetails>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IActionRuntime>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IActionRuntime2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IActionRuntime3>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IActionRuntimeFactory>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IContactActionEntity>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IDocumentActionEntity>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IFileActionEntity>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::INamedActionEntity>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IPhotoActionEntity>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IRemoteFileActionEntity>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IStreamingTextActionEntity>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IStreamingTextActionEntityTextChangedArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::IStreamingTextActionEntityWriter>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::ITableActionEntity>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::ITextActionEntity>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::ITextActionEntity2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::ActionEntity>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::ActionEntityDisplayInfo>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::ActionEntityFactory>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::ActionFeedback>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::ActionInvocationContext>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::ActionInvocationHelpDetails>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::ActionRuntime>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::ContactActionEntity>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::DocumentActionEntity>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::FileActionEntity>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::NamedActionEntity>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::PhotoActionEntity>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::RemoteFileActionEntity>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::StreamingTextActionEntity>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::StreamingTextActionEntityTextChangedArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::StreamingTextActionEntityWriter>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::TableActionEntity>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::TextActionEntity>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::Actions::ActionEntityKind>{ using type = enum_category; };
    template <> struct category<winrt::Windows::AI::Actions::ActionEntityTextFormat>{ using type = enum_category; };
    template <> struct category<winrt::Windows::AI::Actions::ActionFeedbackKind>{ using type = enum_category; };
    template <> struct category<winrt::Windows::AI::Actions::ActionInvocationHelpKind>{ using type = enum_category; };
    template <> struct category<winrt::Windows::AI::Actions::ActionInvocationResult>{ using type = enum_category; };
    template <> struct category<winrt::Windows::AI::Actions::RemoteFileKind>{ using type = enum_category; };
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ActionEntity> = L"Windows.AI.Actions.ActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ActionEntityDisplayInfo> = L"Windows.AI.Actions.ActionEntityDisplayInfo";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ActionEntityFactory> = L"Windows.AI.Actions.ActionEntityFactory";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ActionFeedback> = L"Windows.AI.Actions.ActionFeedback";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ActionInvocationContext> = L"Windows.AI.Actions.ActionInvocationContext";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ActionInvocationHelpDetails> = L"Windows.AI.Actions.ActionInvocationHelpDetails";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ActionRuntime> = L"Windows.AI.Actions.ActionRuntime";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ContactActionEntity> = L"Windows.AI.Actions.ContactActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::DocumentActionEntity> = L"Windows.AI.Actions.DocumentActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::FileActionEntity> = L"Windows.AI.Actions.FileActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::NamedActionEntity> = L"Windows.AI.Actions.NamedActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::PhotoActionEntity> = L"Windows.AI.Actions.PhotoActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::RemoteFileActionEntity> = L"Windows.AI.Actions.RemoteFileActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::StreamingTextActionEntity> = L"Windows.AI.Actions.StreamingTextActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::StreamingTextActionEntityTextChangedArgs> = L"Windows.AI.Actions.StreamingTextActionEntityTextChangedArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::StreamingTextActionEntityWriter> = L"Windows.AI.Actions.StreamingTextActionEntityWriter";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::TableActionEntity> = L"Windows.AI.Actions.TableActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::TextActionEntity> = L"Windows.AI.Actions.TextActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ActionEntityKind> = L"Windows.AI.Actions.ActionEntityKind";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ActionEntityTextFormat> = L"Windows.AI.Actions.ActionEntityTextFormat";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ActionFeedbackKind> = L"Windows.AI.Actions.ActionFeedbackKind";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ActionInvocationHelpKind> = L"Windows.AI.Actions.ActionInvocationHelpKind";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ActionInvocationResult> = L"Windows.AI.Actions.ActionInvocationResult";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::RemoteFileKind> = L"Windows.AI.Actions.RemoteFileKind";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IActionEntity> = L"Windows.AI.Actions.IActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IActionEntity2> = L"Windows.AI.Actions.IActionEntity2";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IActionEntityDisplayInfo> = L"Windows.AI.Actions.IActionEntityDisplayInfo";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IActionEntityFactory> = L"Windows.AI.Actions.IActionEntityFactory";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IActionEntityFactory2> = L"Windows.AI.Actions.IActionEntityFactory2";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IActionEntityFactory3> = L"Windows.AI.Actions.IActionEntityFactory3";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IActionEntityFactory4> = L"Windows.AI.Actions.IActionEntityFactory4";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IActionEntityFactoryFactory> = L"Windows.AI.Actions.IActionEntityFactoryFactory";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IActionFeedback> = L"Windows.AI.Actions.IActionFeedback";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IActionInvocationContext> = L"Windows.AI.Actions.IActionInvocationContext";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IActionInvocationContext2> = L"Windows.AI.Actions.IActionInvocationContext2";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IActionInvocationHelpDetails> = L"Windows.AI.Actions.IActionInvocationHelpDetails";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IActionRuntime> = L"Windows.AI.Actions.IActionRuntime";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IActionRuntime2> = L"Windows.AI.Actions.IActionRuntime2";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IActionRuntime3> = L"Windows.AI.Actions.IActionRuntime3";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IActionRuntimeFactory> = L"Windows.AI.Actions.IActionRuntimeFactory";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IContactActionEntity> = L"Windows.AI.Actions.IContactActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IDocumentActionEntity> = L"Windows.AI.Actions.IDocumentActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IFileActionEntity> = L"Windows.AI.Actions.IFileActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::INamedActionEntity> = L"Windows.AI.Actions.INamedActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IPhotoActionEntity> = L"Windows.AI.Actions.IPhotoActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IRemoteFileActionEntity> = L"Windows.AI.Actions.IRemoteFileActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IStreamingTextActionEntity> = L"Windows.AI.Actions.IStreamingTextActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IStreamingTextActionEntityTextChangedArgs> = L"Windows.AI.Actions.IStreamingTextActionEntityTextChangedArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::IStreamingTextActionEntityWriter> = L"Windows.AI.Actions.IStreamingTextActionEntityWriter";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ITableActionEntity> = L"Windows.AI.Actions.ITableActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ITextActionEntity> = L"Windows.AI.Actions.ITextActionEntity";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ITextActionEntity2> = L"Windows.AI.Actions.ITextActionEntity2";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::ActionsContract> = L"Windows.AI.Actions.ActionsContract";
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IActionEntity>{ 0x445E700F,0x2122,0x5668,{ 0x9A,0x16,0x4C,0xAB,0x29,0x82,0xC5,0xF4 } }; // 445E700F-2122-5668-9A16-4CAB2982C5F4
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IActionEntity2>{ 0x98FE136D,0xDD3A,0x58C1,{ 0xAF,0x76,0xFE,0xB4,0xE1,0x9D,0xCE,0x9E } }; // 98FE136D-DD3A-58C1-AF76-FEB4E19DCE9E
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IActionEntityDisplayInfo>{ 0x057A9EDE,0x03E1,0x55C6,{ 0xAC,0xBA,0xC7,0x05,0x62,0x16,0x73,0x5A } }; // 057A9EDE-03E1-55C6-ACBA-C7056216735A
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IActionEntityFactory>{ 0x9CB752A0,0x5BF8,0x5BE2,{ 0x91,0x6E,0xB0,0x0E,0xFF,0x80,0x08,0x8D } }; // 9CB752A0-5BF8-5BE2-916E-B00EFF80088D
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IActionEntityFactory2>{ 0xEA2FB6A5,0xEC6D,0x5180,{ 0x9D,0x30,0xBC,0x66,0x3B,0x84,0xE7,0xB8 } }; // EA2FB6A5-EC6D-5180-9D30-BC663B84E7B8
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IActionEntityFactory3>{ 0x4910E689,0x00B5,0x56BB,{ 0x9C,0x65,0x0F,0xCC,0x76,0x21,0x52,0x83 } }; // 4910E689-00B5-56BB-9C65-0FCC76215283
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IActionEntityFactory4>{ 0x332EDA05,0xDE0E,0x5A58,{ 0xB3,0x18,0xA2,0xAD,0x77,0x1F,0x01,0x3D } }; // 332EDA05-DE0E-5A58-B318-A2AD771F013D
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IActionEntityFactoryFactory>{ 0xC9147D8F,0x88A0,0x5EC0,{ 0xA5,0x64,0x47,0xE2,0xA1,0x08,0x14,0x12 } }; // C9147D8F-88A0-5EC0-A564-47E2A1081412
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IActionFeedback>{ 0xA12EE7AB,0x2454,0x56C9,{ 0xBB,0xDF,0xC0,0x89,0x45,0x7F,0xBC,0x5E } }; // A12EE7AB-2454-56C9-BBDF-C089457FBC5E
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IActionInvocationContext>{ 0xC32B622E,0x86E1,0x5EBA,{ 0x96,0x61,0x60,0x59,0x10,0x10,0x49,0x78 } }; // C32B622E-86E1-5EBA-9661-************
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IActionInvocationContext2>{ 0x7C843086,0x9279,0x5BCD,{ 0x8F,0x2E,0xD1,0x51,0x21,0xE7,0xA8,0x27 } }; // 7C843086-9279-5BCD-8F2E-D15121E7A827
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IActionInvocationHelpDetails>{ 0x5430F272,0x078F,0x5722,{ 0x8F,0x7D,0x90,0xCF,0x8D,0xDD,0x59,0x5E } }; // 5430F272-078F-5722-8F7D-90CF8DDD595E
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IActionRuntime>{ 0x206EFA2C,0xC909,0x508A,{ 0xB4,0xB0,0x94,0x82,0xBE,0x96,0xDB,0x9C } }; // 206EFA2C-C909-508A-B4B0-9482BE96DB9C
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IActionRuntime2>{ 0x2DA4D2C0,0xE593,0x5350,{ 0x81,0x43,0x15,0xBB,0x24,0xF6,0x34,0x11 } }; // 2DA4D2C0-E593-5350-8143-15BB24F63411
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IActionRuntime3>{ 0xF020C3C0,0xCAEC,0x5928,{ 0xAD,0x00,0x81,0x06,0x9B,0x80,0xFB,0xC1 } }; // F020C3C0-CAEC-5928-AD00-81069B80FBC1
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IActionRuntimeFactory>{ 0xD3F366E9,0x8DC9,0x50A0,{ 0x80,0x40,0xE5,0xC1,0x4F,0xA6,0x09,0xD6 } }; // D3F366E9-8DC9-50A0-8040-E5C14FA609D6
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IContactActionEntity>{ 0x458C3E07,0x5892,0x5485,{ 0xBD,0x9B,0x8F,0x7A,0x54,0x0C,0x95,0x01 } }; // 458C3E07-5892-5485-BD9B-8F7A540C9501
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IDocumentActionEntity>{ 0x56715297,0x960B,0x59FF,{ 0xAF,0x4B,0xEC,0xE1,0x09,0x8B,0x2E,0x36 } }; // 56715297-960B-59FF-AF4B-ECE1098B2E36
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IFileActionEntity>{ 0xF20AB43F,0x4C80,0x5904,{ 0xBD,0x42,0x3E,0x62,0x48,0xBA,0xBF,0xCF } }; // F20AB43F-4C80-5904-BD42-3E6248BABFCF
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::INamedActionEntity>{ 0x1AAEBEEF,0x435B,0x5A0D,{ 0x81,0x82,0x05,0xFE,0x4D,0xD4,0x77,0x12 } }; // 1AAEBEEF-435B-5A0D-8182-05FE4DD47712
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IPhotoActionEntity>{ 0x425123B3,0x20EF,0x51A6,{ 0xB3,0x5F,0x84,0x14,0x38,0x47,0x65,0xC5 } }; // 425123B3-20EF-51A6-B35F-8414384765C5
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IRemoteFileActionEntity>{ 0xA5D8EC21,0xA2BD,0x545A,{ 0xAB,0xFC,0xD7,0xAA,0x79,0xFD,0x0B,0x81 } }; // A5D8EC21-A2BD-545A-ABFC-D7AA79FD0B81
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IStreamingTextActionEntity>{ 0x44CD8A16,0xABC9,0x5703,{ 0xB4,0xBF,0x6F,0xE8,0xB7,0xA8,0x02,0xFD } }; // 44CD8A16-ABC9-5703-B4BF-6FE8B7A802FD
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IStreamingTextActionEntityTextChangedArgs>{ 0x2C62011F,0x3E06,0x588B,{ 0xA3,0xBD,0xD7,0x26,0xBD,0x82,0xFB,0x13 } }; // 2C62011F-3E06-588B-A3BD-D726BD82FB13
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::IStreamingTextActionEntityWriter>{ 0x6BCE2F76,0xA8AF,0x5FF2,{ 0x83,0x3C,0x10,0x87,0x37,0xBA,0x0F,0x42 } }; // 6BCE2F76-A8AF-5FF2-833C-108737BA0F42
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::ITableActionEntity>{ 0x0F252CDB,0xBA24,0x5DBB,{ 0x9D,0x17,0x1B,0x30,0x07,0x73,0xD1,0x41 } }; // 0F252CDB-BA24-5DBB-9D17-1B300773D141
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::ITextActionEntity>{ 0x3C4EC25F,0x5ADB,0x5F73,{ 0xB8,0xF3,0x08,0x0F,0xBE,0xAD,0xD6,0x12 } }; // 3C4EC25F-5ADB-5F73-B8F3-080FBEADD612
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::ITextActionEntity2>{ 0x7C500889,0xCF08,0x51E7,{ 0xBE,0xCA,0xF0,0xBB,0xC7,0xA7,0x48,0x6C } }; // 7C500889-CF08-51E7-BECA-F0BBC7A7486C
    template <> struct default_interface<winrt::Windows::AI::Actions::ActionEntity>{ using type = winrt::Windows::AI::Actions::IActionEntity; };
    template <> struct default_interface<winrt::Windows::AI::Actions::ActionEntityDisplayInfo>{ using type = winrt::Windows::AI::Actions::IActionEntityDisplayInfo; };
    template <> struct default_interface<winrt::Windows::AI::Actions::ActionEntityFactory>{ using type = winrt::Windows::AI::Actions::IActionEntityFactory2; };
    template <> struct default_interface<winrt::Windows::AI::Actions::ActionFeedback>{ using type = winrt::Windows::AI::Actions::IActionFeedback; };
    template <> struct default_interface<winrt::Windows::AI::Actions::ActionInvocationContext>{ using type = winrt::Windows::AI::Actions::IActionInvocationContext; };
    template <> struct default_interface<winrt::Windows::AI::Actions::ActionInvocationHelpDetails>{ using type = winrt::Windows::AI::Actions::IActionInvocationHelpDetails; };
    template <> struct default_interface<winrt::Windows::AI::Actions::ActionRuntime>{ using type = winrt::Windows::AI::Actions::IActionRuntime; };
    template <> struct default_interface<winrt::Windows::AI::Actions::ContactActionEntity>{ using type = winrt::Windows::AI::Actions::IContactActionEntity; };
    template <> struct default_interface<winrt::Windows::AI::Actions::DocumentActionEntity>{ using type = winrt::Windows::AI::Actions::IDocumentActionEntity; };
    template <> struct default_interface<winrt::Windows::AI::Actions::FileActionEntity>{ using type = winrt::Windows::AI::Actions::IFileActionEntity; };
    template <> struct default_interface<winrt::Windows::AI::Actions::NamedActionEntity>{ using type = winrt::Windows::AI::Actions::INamedActionEntity; };
    template <> struct default_interface<winrt::Windows::AI::Actions::PhotoActionEntity>{ using type = winrt::Windows::AI::Actions::IPhotoActionEntity; };
    template <> struct default_interface<winrt::Windows::AI::Actions::RemoteFileActionEntity>{ using type = winrt::Windows::AI::Actions::IRemoteFileActionEntity; };
    template <> struct default_interface<winrt::Windows::AI::Actions::StreamingTextActionEntity>{ using type = winrt::Windows::AI::Actions::IStreamingTextActionEntity; };
    template <> struct default_interface<winrt::Windows::AI::Actions::StreamingTextActionEntityTextChangedArgs>{ using type = winrt::Windows::AI::Actions::IStreamingTextActionEntityTextChangedArgs; };
    template <> struct default_interface<winrt::Windows::AI::Actions::StreamingTextActionEntityWriter>{ using type = winrt::Windows::AI::Actions::IStreamingTextActionEntityWriter; };
    template <> struct default_interface<winrt::Windows::AI::Actions::TableActionEntity>{ using type = winrt::Windows::AI::Actions::ITableActionEntity; };
    template <> struct default_interface<winrt::Windows::AI::Actions::TextActionEntity>{ using type = winrt::Windows::AI::Actions::ITextActionEntity; };
    template <> struct abi<winrt::Windows::AI::Actions::IActionEntity>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Kind(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_DisplayInfo(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IActionEntity2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Id(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IActionEntityDisplayInfo>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Title(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IActionEntityFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IActionEntityFactory2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateFileEntity(void*, void**) noexcept = 0;
            virtual int32_t __stdcall CreateDocumentEntity(void*, void**) noexcept = 0;
            virtual int32_t __stdcall CreatePhotoEntity(void*, void**) noexcept = 0;
            virtual int32_t __stdcall CreateTextEntity(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IActionEntityFactory3>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateRemoteFileEntity(void*, int32_t, void*, void*, void*, void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall CreateTextEntityWithTextFormat(void*, int32_t, void**) noexcept = 0;
            virtual int32_t __stdcall CreateStreamingTextActionEntityWriter(int32_t, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IActionEntityFactory4>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateTableEntity(uint32_t, void**, uint32_t, void**) noexcept = 0;
            virtual int32_t __stdcall CreateContactEntity(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IActionEntityFactoryFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IActionFeedback>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FeedbackKind(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IActionInvocationContext>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_EntityFactory(void**) noexcept = 0;
            virtual int32_t __stdcall SetInputEntity(void*, void*) noexcept = 0;
            virtual int32_t __stdcall GetInputEntities(uint32_t* __resultSize, void***) noexcept = 0;
            virtual int32_t __stdcall SetOutputEntity(void*, void*) noexcept = 0;
            virtual int32_t __stdcall GetOutputEntities(uint32_t* __resultSize, void***) noexcept = 0;
            virtual int32_t __stdcall get_Result(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Result(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_ExtendedError(winrt::hresult*) noexcept = 0;
            virtual int32_t __stdcall put_ExtendedError(winrt::hresult) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IActionInvocationContext2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_InvokerWindowId(struct struct_Windows_UI_WindowId*) noexcept = 0;
            virtual int32_t __stdcall get_HelpDetails(void**) noexcept = 0;
            virtual int32_t __stdcall get_ActionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_InvokerAppUserModelId(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IActionInvocationHelpDetails>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Kind(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Kind(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_Title(void**) noexcept = 0;
            virtual int32_t __stdcall put_Title(void*) noexcept = 0;
            virtual int32_t __stdcall get_Description(void**) noexcept = 0;
            virtual int32_t __stdcall put_Description(void*) noexcept = 0;
            virtual int32_t __stdcall get_HelpUri(void**) noexcept = 0;
            virtual int32_t __stdcall put_HelpUri(void*) noexcept = 0;
            virtual int32_t __stdcall get_HelpUriDescription(void**) noexcept = 0;
            virtual int32_t __stdcall put_HelpUriDescription(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IActionRuntime>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ActionCatalog(void**) noexcept = 0;
            virtual int32_t __stdcall get_EntityFactory(void**) noexcept = 0;
            virtual int32_t __stdcall CreateInvocationContext(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IActionRuntime2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateActionFeedback(int32_t, void**) noexcept = 0;
            virtual int32_t __stdcall SetActionAvailability(void*, bool) noexcept = 0;
            virtual int32_t __stdcall GetActionAvailability(void*, bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IActionRuntime3>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInvocationContextWithWindowId(void*, struct struct_Windows_UI_WindowId, void**) noexcept = 0;
            virtual int32_t __stdcall GetActionEntityById(void*, void**) noexcept = 0;
            virtual int32_t __stdcall get_LatestSupportedSchemaVersion(uint32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IActionRuntimeFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IContactActionEntity>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Contact(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IDocumentActionEntity>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FullPath(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IFileActionEntity>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FullPath(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::INamedActionEntity>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall put_Name(void*) noexcept = 0;
            virtual int32_t __stdcall get_Entity(void**) noexcept = 0;
            virtual int32_t __stdcall put_Entity(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IPhotoActionEntity>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FullPath(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IRemoteFileActionEntity>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_SourceId(void**) noexcept = 0;
            virtual int32_t __stdcall get_FileKind(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_SourceUri(void**) noexcept = 0;
            virtual int32_t __stdcall get_FileId(void**) noexcept = 0;
            virtual int32_t __stdcall get_ContentType(void**) noexcept = 0;
            virtual int32_t __stdcall get_DriveId(void**) noexcept = 0;
            virtual int32_t __stdcall get_AccountId(void**) noexcept = 0;
            virtual int32_t __stdcall get_Extension(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IStreamingTextActionEntity>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsComplete(bool*) noexcept = 0;
            virtual int32_t __stdcall GetText(void**) noexcept = 0;
            virtual int32_t __stdcall get_TextFormat(int32_t*) noexcept = 0;
            virtual int32_t __stdcall add_TextChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_TextChanged(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IStreamingTextActionEntityTextChangedArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Text(void**) noexcept = 0;
            virtual int32_t __stdcall get_IsComplete(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::IStreamingTextActionEntityWriter>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ReaderEntity(void**) noexcept = 0;
            virtual int32_t __stdcall get_TextFormat(int32_t*) noexcept = 0;
            virtual int32_t __stdcall SetText(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::ITableActionEntity>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetTextContent(uint32_t* __resultSize, void***) noexcept = 0;
            virtual int32_t __stdcall get_RowCount(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall get_ColumnCount(uint32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::ITextActionEntity>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Text(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::ITextActionEntity2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_TextFormat(int32_t*) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IActionEntity
    {
        [[nodiscard]] auto Kind() const;
        [[nodiscard]] auto DisplayInfo() const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IActionEntity>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IActionEntity<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IActionEntity2
    {
        [[nodiscard]] auto Id() const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IActionEntity2>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IActionEntity2<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IActionEntityDisplayInfo
    {
        [[nodiscard]] auto Title() const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IActionEntityDisplayInfo>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IActionEntityDisplayInfo<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IActionEntityFactory
    {
    };
    template <> struct consume<winrt::Windows::AI::Actions::IActionEntityFactory>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IActionEntityFactory<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IActionEntityFactory2
    {
        auto CreateFileEntity(param::hstring const& path) const;
        auto CreateDocumentEntity(param::hstring const& path) const;
        auto CreatePhotoEntity(param::hstring const& path) const;
        auto CreateTextEntity(param::hstring const& text) const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IActionEntityFactory2>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IActionEntityFactory2<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IActionEntityFactory3
    {
        auto CreateRemoteFileEntity(param::hstring const& sourceId, winrt::Windows::AI::Actions::RemoteFileKind const& fileKind, winrt::Windows::Foundation::Uri const& sourceUri, param::hstring const& fileId, param::hstring const& contentType, param::hstring const& driveId, param::hstring const& accountId, param::hstring const& extension) const;
        auto CreateTextEntity(param::hstring const& text, winrt::Windows::AI::Actions::ActionEntityTextFormat const& textFormat) const;
        auto CreateStreamingTextActionEntityWriter(winrt::Windows::AI::Actions::ActionEntityTextFormat const& textFormat) const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IActionEntityFactory3>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IActionEntityFactory3<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IActionEntityFactory4
    {
        auto CreateTableEntity(array_view<hstring const> data, uint32_t columnCount) const;
        auto CreateContactEntity(winrt::Windows::ApplicationModel::Contacts::Contact const& contact) const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IActionEntityFactory4>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IActionEntityFactory4<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IActionEntityFactoryFactory
    {
    };
    template <> struct consume<winrt::Windows::AI::Actions::IActionEntityFactoryFactory>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IActionEntityFactoryFactory<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IActionFeedback
    {
        [[nodiscard]] auto FeedbackKind() const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IActionFeedback>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IActionFeedback<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IActionInvocationContext
    {
        [[nodiscard]] auto EntityFactory() const;
        auto SetInputEntity(param::hstring const& inputName, winrt::Windows::AI::Actions::ActionEntity const& inputValue) const;
        auto GetInputEntities() const;
        auto SetOutputEntity(param::hstring const& outputName, winrt::Windows::AI::Actions::ActionEntity const& outputValue) const;
        auto GetOutputEntities() const;
        [[nodiscard]] auto Result() const;
        auto Result(winrt::Windows::AI::Actions::ActionInvocationResult const& value) const;
        [[nodiscard]] auto ExtendedError() const;
        auto ExtendedError(winrt::hresult const& value) const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IActionInvocationContext>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IActionInvocationContext<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IActionInvocationContext2
    {
        [[nodiscard]] auto InvokerWindowId() const;
        [[nodiscard]] auto HelpDetails() const;
        [[nodiscard]] auto ActionId() const;
        [[nodiscard]] auto InvokerAppUserModelId() const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IActionInvocationContext2>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IActionInvocationContext2<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IActionInvocationHelpDetails
    {
        [[nodiscard]] auto Kind() const;
        auto Kind(winrt::Windows::AI::Actions::ActionInvocationHelpKind const& value) const;
        [[nodiscard]] auto Title() const;
        auto Title(param::hstring const& value) const;
        [[nodiscard]] auto Description() const;
        auto Description(param::hstring const& value) const;
        [[nodiscard]] auto HelpUri() const;
        auto HelpUri(winrt::Windows::Foundation::Uri const& value) const;
        [[nodiscard]] auto HelpUriDescription() const;
        auto HelpUriDescription(param::hstring const& value) const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IActionInvocationHelpDetails>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IActionInvocationHelpDetails<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IActionRuntime
    {
        [[nodiscard]] auto ActionCatalog() const;
        [[nodiscard]] auto EntityFactory() const;
        auto CreateInvocationContext(param::hstring const& actionId) const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IActionRuntime>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IActionRuntime<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IActionRuntime2
    {
        auto CreateActionFeedback(winrt::Windows::AI::Actions::ActionFeedbackKind const& feedbackKind) const;
        auto SetActionAvailability(param::hstring const& actionId, bool isAvailable) const;
        auto GetActionAvailability(param::hstring const& actionId) const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IActionRuntime2>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IActionRuntime2<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IActionRuntime3
    {
        auto CreateInvocationContextWithWindowId(param::hstring const& actionId, winrt::Windows::UI::WindowId const& invokerWindowId) const;
        auto GetActionEntityById(param::hstring const& entityId) const;
        [[nodiscard]] auto LatestSupportedSchemaVersion() const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IActionRuntime3>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IActionRuntime3<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IActionRuntimeFactory
    {
    };
    template <> struct consume<winrt::Windows::AI::Actions::IActionRuntimeFactory>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IActionRuntimeFactory<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IContactActionEntity
    {
        [[nodiscard]] auto Contact() const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IContactActionEntity>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IContactActionEntity<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IDocumentActionEntity
    {
        [[nodiscard]] auto FullPath() const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IDocumentActionEntity>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IDocumentActionEntity<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IFileActionEntity
    {
        [[nodiscard]] auto FullPath() const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IFileActionEntity>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IFileActionEntity<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_INamedActionEntity
    {
        [[nodiscard]] auto Name() const;
        auto Name(param::hstring const& value) const;
        [[nodiscard]] auto Entity() const;
        auto Entity(winrt::Windows::AI::Actions::ActionEntity const& value) const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::INamedActionEntity>
    {
        template <typename D> using type = consume_Windows_AI_Actions_INamedActionEntity<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IPhotoActionEntity
    {
        [[nodiscard]] auto FullPath() const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IPhotoActionEntity>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IPhotoActionEntity<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IRemoteFileActionEntity
    {
        [[nodiscard]] auto SourceId() const;
        [[nodiscard]] auto FileKind() const;
        [[nodiscard]] auto SourceUri() const;
        [[nodiscard]] auto FileId() const;
        [[nodiscard]] auto ContentType() const;
        [[nodiscard]] auto DriveId() const;
        [[nodiscard]] auto AccountId() const;
        [[nodiscard]] auto Extension() const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IRemoteFileActionEntity>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IRemoteFileActionEntity<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IStreamingTextActionEntity
    {
        [[nodiscard]] auto IsComplete() const;
        auto GetText() const;
        [[nodiscard]] auto TextFormat() const;
        auto TextChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::AI::Actions::StreamingTextActionEntity, winrt::Windows::AI::Actions::StreamingTextActionEntityTextChangedArgs> const& handler) const;
        using TextChanged_revoker = impl::event_revoker<winrt::Windows::AI::Actions::IStreamingTextActionEntity, &impl::abi_t<winrt::Windows::AI::Actions::IStreamingTextActionEntity>::remove_TextChanged>;
        [[nodiscard]] auto TextChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::AI::Actions::StreamingTextActionEntity, winrt::Windows::AI::Actions::StreamingTextActionEntityTextChangedArgs> const& handler) const;
        auto TextChanged(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IStreamingTextActionEntity>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IStreamingTextActionEntity<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IStreamingTextActionEntityTextChangedArgs
    {
        [[nodiscard]] auto Text() const;
        [[nodiscard]] auto IsComplete() const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IStreamingTextActionEntityTextChangedArgs>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IStreamingTextActionEntityTextChangedArgs<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_IStreamingTextActionEntityWriter
    {
        [[nodiscard]] auto ReaderEntity() const;
        [[nodiscard]] auto TextFormat() const;
        auto SetText(param::hstring const& text) const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::IStreamingTextActionEntityWriter>
    {
        template <typename D> using type = consume_Windows_AI_Actions_IStreamingTextActionEntityWriter<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_ITableActionEntity
    {
        auto GetTextContent() const;
        [[nodiscard]] auto RowCount() const;
        [[nodiscard]] auto ColumnCount() const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::ITableActionEntity>
    {
        template <typename D> using type = consume_Windows_AI_Actions_ITableActionEntity<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_ITextActionEntity
    {
        [[nodiscard]] auto Text() const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::ITextActionEntity>
    {
        template <typename D> using type = consume_Windows_AI_Actions_ITextActionEntity<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_ITextActionEntity2
    {
        [[nodiscard]] auto TextFormat() const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::ITextActionEntity2>
    {
        template <typename D> using type = consume_Windows_AI_Actions_ITextActionEntity2<D>;
    };
}
#endif
