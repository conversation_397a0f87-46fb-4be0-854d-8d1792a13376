﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\test\test_detectors.cpp">
      <Filter>opencv_xfeatures2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\test\test_features2d.cpp">
      <Filter>opencv_xfeatures2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\test\test_gms_matcher.cpp">
      <Filter>opencv_xfeatures2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\test\test_keypoints.cpp">
      <Filter>opencv_xfeatures2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\test\test_logos_matcher.cpp">
      <Filter>opencv_xfeatures2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\test\test_main.cpp">
      <Filter>opencv_xfeatures2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\test\test_rotation_and_scale_invariance.cpp">
      <Filter>opencv_xfeatures2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\test\test_surf.cuda.cpp">
      <Filter>opencv_xfeatures2d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\test\test_surf.ocl.cpp">
      <Filter>opencv_xfeatures2d\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\test\test_precomp.hpp">
      <Filter>opencv_xfeatures2d\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_xfeatures2d">
      <UniqueIdentifier>{3FFD040F-1110-350F-B850-CBF0D18D9D9F}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_xfeatures2d\Include">
      <UniqueIdentifier>{A8A6C1ED-F7E5-35DD-BC86-D5FF08AE6F6E}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_xfeatures2d\Src">
      <UniqueIdentifier>{A05576A8-5CFE-3AD8-A531-44A0ACC330AD}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
