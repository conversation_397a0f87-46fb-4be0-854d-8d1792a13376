<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Error codes</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle"><div class="title">Error codes<div class="ingroups"><a class="el" href="group__init.html">Initialization, version and error reference</a></div></div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>See <a class="el" href="intro_guide.html#error_handling">error handling</a> for how these are used. </p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:gafa30deee5db4d69c4c93d116ed87dbf4" id="r_gafa30deee5db4d69c4c93d116ed87dbf4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#gafa30deee5db4d69c4c93d116ed87dbf4">GLFW_NO_ERROR</a>&#160;&#160;&#160;0</td></tr>
<tr class="memdesc:gafa30deee5db4d69c4c93d116ed87dbf4"><td class="mdescLeft">&#160;</td><td class="mdescRight">No error has occurred.  <br /></td></tr>
<tr class="separator:gafa30deee5db4d69c4c93d116ed87dbf4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2374ee02c177f12e1fa76ff3ed15e14a" id="r_ga2374ee02c177f12e1fa76ff3ed15e14a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>&#160;&#160;&#160;0x00010001</td></tr>
<tr class="memdesc:ga2374ee02c177f12e1fa76ff3ed15e14a"><td class="mdescLeft">&#160;</td><td class="mdescRight">GLFW has not been initialized.  <br /></td></tr>
<tr class="separator:ga2374ee02c177f12e1fa76ff3ed15e14a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa8290386e9528ccb9e42a3a4e16fc0d0" id="r_gaa8290386e9528ccb9e42a3a4e16fc0d0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#gaa8290386e9528ccb9e42a3a4e16fc0d0">GLFW_NO_CURRENT_CONTEXT</a>&#160;&#160;&#160;0x00010002</td></tr>
<tr class="memdesc:gaa8290386e9528ccb9e42a3a4e16fc0d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">No context is current for this thread.  <br /></td></tr>
<tr class="separator:gaa8290386e9528ccb9e42a3a4e16fc0d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga76f6bb9c4eea73db675f096b404593ce" id="r_ga76f6bb9c4eea73db675f096b404593ce"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a>&#160;&#160;&#160;0x00010003</td></tr>
<tr class="memdesc:ga76f6bb9c4eea73db675f096b404593ce"><td class="mdescLeft">&#160;</td><td class="mdescRight">One of the arguments to the function was an invalid enum value.  <br /></td></tr>
<tr class="separator:ga76f6bb9c4eea73db675f096b404593ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaf2ef9aa8202c2b82ac2d921e554c687" id="r_gaaf2ef9aa8202c2b82ac2d921e554c687"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a>&#160;&#160;&#160;0x00010004</td></tr>
<tr class="memdesc:gaaf2ef9aa8202c2b82ac2d921e554c687"><td class="mdescLeft">&#160;</td><td class="mdescRight">One of the arguments to the function was an invalid value.  <br /></td></tr>
<tr class="separator:gaaf2ef9aa8202c2b82ac2d921e554c687"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9023953a2bcb98c2906afd071d21ee7f" id="r_ga9023953a2bcb98c2906afd071d21ee7f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga9023953a2bcb98c2906afd071d21ee7f">GLFW_OUT_OF_MEMORY</a>&#160;&#160;&#160;0x00010005</td></tr>
<tr class="memdesc:ga9023953a2bcb98c2906afd071d21ee7f"><td class="mdescLeft">&#160;</td><td class="mdescRight">A memory allocation failed.  <br /></td></tr>
<tr class="separator:ga9023953a2bcb98c2906afd071d21ee7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga56882b290db23261cc6c053c40c2d08e" id="r_ga56882b290db23261cc6c053c40c2d08e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga56882b290db23261cc6c053c40c2d08e">GLFW_API_UNAVAILABLE</a>&#160;&#160;&#160;0x00010006</td></tr>
<tr class="memdesc:ga56882b290db23261cc6c053c40c2d08e"><td class="mdescLeft">&#160;</td><td class="mdescRight">GLFW could not find support for the requested API on the system.  <br /></td></tr>
<tr class="separator:ga56882b290db23261cc6c053c40c2d08e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad16c5565b4a69f9c2a9ac2c0dbc89462" id="r_gad16c5565b4a69f9c2a9ac2c0dbc89462"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#gad16c5565b4a69f9c2a9ac2c0dbc89462">GLFW_VERSION_UNAVAILABLE</a>&#160;&#160;&#160;0x00010007</td></tr>
<tr class="memdesc:gad16c5565b4a69f9c2a9ac2c0dbc89462"><td class="mdescLeft">&#160;</td><td class="mdescRight">The requested OpenGL or OpenGL ES version is not available.  <br /></td></tr>
<tr class="separator:gad16c5565b4a69f9c2a9ac2c0dbc89462"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad44162d78100ea5e87cdd38426b8c7a1" id="r_gad44162d78100ea5e87cdd38426b8c7a1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>&#160;&#160;&#160;0x00010008</td></tr>
<tr class="memdesc:gad44162d78100ea5e87cdd38426b8c7a1"><td class="mdescLeft">&#160;</td><td class="mdescRight">A platform-specific error occurred that does not match any of the more specific categories.  <br /></td></tr>
<tr class="separator:gad44162d78100ea5e87cdd38426b8c7a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga196e125ef261d94184e2b55c05762f14" id="r_ga196e125ef261d94184e2b55c05762f14"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga196e125ef261d94184e2b55c05762f14">GLFW_FORMAT_UNAVAILABLE</a>&#160;&#160;&#160;0x00010009</td></tr>
<tr class="memdesc:ga196e125ef261d94184e2b55c05762f14"><td class="mdescLeft">&#160;</td><td class="mdescRight">The requested format is not supported or available.  <br /></td></tr>
<tr class="separator:ga196e125ef261d94184e2b55c05762f14"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacff24d2757da752ae4c80bf452356487" id="r_gacff24d2757da752ae4c80bf452356487"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a>&#160;&#160;&#160;0x0001000A</td></tr>
<tr class="memdesc:gacff24d2757da752ae4c80bf452356487"><td class="mdescLeft">&#160;</td><td class="mdescRight">The specified window does not have an OpenGL or OpenGL ES context.  <br /></td></tr>
<tr class="separator:gacff24d2757da752ae4c80bf452356487"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga09d6943923a70ddef3a085f5baee786c" id="r_ga09d6943923a70ddef3a085f5baee786c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga09d6943923a70ddef3a085f5baee786c">GLFW_CURSOR_UNAVAILABLE</a>&#160;&#160;&#160;0x0001000B</td></tr>
<tr class="memdesc:ga09d6943923a70ddef3a085f5baee786c"><td class="mdescLeft">&#160;</td><td class="mdescRight">The specified cursor shape is not available.  <br /></td></tr>
<tr class="separator:ga09d6943923a70ddef3a085f5baee786c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga526fba20a01504a8086c763b6ca53ce5" id="r_ga526fba20a01504a8086c763b6ca53ce5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a>&#160;&#160;&#160;0x0001000C</td></tr>
<tr class="memdesc:ga526fba20a01504a8086c763b6ca53ce5"><td class="mdescLeft">&#160;</td><td class="mdescRight">The requested feature is not provided by the platform.  <br /></td></tr>
<tr class="separator:ga526fba20a01504a8086c763b6ca53ce5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5dda77e023e83151e8bd55a6758f946a" id="r_ga5dda77e023e83151e8bd55a6758f946a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga5dda77e023e83151e8bd55a6758f946a">GLFW_FEATURE_UNIMPLEMENTED</a>&#160;&#160;&#160;0x0001000D</td></tr>
<tr class="memdesc:ga5dda77e023e83151e8bd55a6758f946a"><td class="mdescLeft">&#160;</td><td class="mdescRight">The requested feature is not implemented for the platform.  <br /></td></tr>
<tr class="separator:ga5dda77e023e83151e8bd55a6758f946a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3608c6c29ab7a72f3bf019f4c3a2563d" id="r_ga3608c6c29ab7a72f3bf019f4c3a2563d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>&#160;&#160;&#160;0x0001000E</td></tr>
<tr class="memdesc:ga3608c6c29ab7a72f3bf019f4c3a2563d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Platform unavailable or no matching platform was found.  <br /></td></tr>
<tr class="separator:ga3608c6c29ab7a72f3bf019f4c3a2563d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="gafa30deee5db4d69c4c93d116ed87dbf4" name="gafa30deee5db4d69c4c93d116ed87dbf4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafa30deee5db4d69c4c93d116ed87dbf4">&#9670;&#160;</a></span>GLFW_NO_ERROR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_NO_ERROR&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>No error has occurred.</p>
<dl class="section user"><dt>Analysis</dt><dd>Yay. </dd></dl>

</div>
</div>
<a id="ga2374ee02c177f12e1fa76ff3ed15e14a" name="ga2374ee02c177f12e1fa76ff3ed15e14a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2374ee02c177f12e1fa76ff3ed15e14a">&#9670;&#160;</a></span>GLFW_NOT_INITIALIZED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_NOT_INITIALIZED&#160;&#160;&#160;0x00010001</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This occurs if a GLFW function was called that must not be called unless the library is <a class="el" href="intro_guide.html#intro_init">initialized</a>.</p>
<dl class="section user"><dt>Analysis</dt><dd>Application programmer error. Initialize GLFW before calling any function that requires initialization. </dd></dl>

</div>
</div>
<a id="gaa8290386e9528ccb9e42a3a4e16fc0d0" name="gaa8290386e9528ccb9e42a3a4e16fc0d0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa8290386e9528ccb9e42a3a4e16fc0d0">&#9670;&#160;</a></span>GLFW_NO_CURRENT_CONTEXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_NO_CURRENT_CONTEXT&#160;&#160;&#160;0x00010002</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This occurs if a GLFW function was called that needs and operates on the current OpenGL or OpenGL ES context but no context is current on the calling thread. One such function is <a class="el" href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed">glfwSwapInterval</a>.</p>
<dl class="section user"><dt>Analysis</dt><dd>Application programmer error. Ensure a context is current before calling functions that require a current context. </dd></dl>

</div>
</div>
<a id="ga76f6bb9c4eea73db675f096b404593ce" name="ga76f6bb9c4eea73db675f096b404593ce"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga76f6bb9c4eea73db675f096b404593ce">&#9670;&#160;</a></span>GLFW_INVALID_ENUM</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_INVALID_ENUM&#160;&#160;&#160;0x00010003</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>One of the arguments to the function was an invalid enum value, for example requesting <a class="el" href="window_guide.html#GLFW_RED_BITS">GLFW_RED_BITS</a> with <a class="el" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>.</p>
<dl class="section user"><dt>Analysis</dt><dd>Application programmer error. Fix the offending call. </dd></dl>

</div>
</div>
<a id="gaaf2ef9aa8202c2b82ac2d921e554c687" name="gaaf2ef9aa8202c2b82ac2d921e554c687"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaaf2ef9aa8202c2b82ac2d921e554c687">&#9670;&#160;</a></span>GLFW_INVALID_VALUE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_INVALID_VALUE&#160;&#160;&#160;0x00010004</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>One of the arguments to the function was an invalid value, for example requesting a non-existent OpenGL or OpenGL ES version like 2.7.</p>
<p>Requesting a valid but unavailable OpenGL or OpenGL ES version will instead result in a <a class="el" href="group__errors.html#gad16c5565b4a69f9c2a9ac2c0dbc89462">GLFW_VERSION_UNAVAILABLE</a> error.</p>
<dl class="section user"><dt>Analysis</dt><dd>Application programmer error. Fix the offending call. </dd></dl>

</div>
</div>
<a id="ga9023953a2bcb98c2906afd071d21ee7f" name="ga9023953a2bcb98c2906afd071d21ee7f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9023953a2bcb98c2906afd071d21ee7f">&#9670;&#160;</a></span>GLFW_OUT_OF_MEMORY</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_OUT_OF_MEMORY&#160;&#160;&#160;0x00010005</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A memory allocation failed.</p>
<dl class="section user"><dt>Analysis</dt><dd>A bug in GLFW or the underlying operating system. Report the bug to our <a href="https://github.com/glfw/glfw/issues">issue tracker</a>. </dd></dl>

</div>
</div>
<a id="ga56882b290db23261cc6c053c40c2d08e" name="ga56882b290db23261cc6c053c40c2d08e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga56882b290db23261cc6c053c40c2d08e">&#9670;&#160;</a></span>GLFW_API_UNAVAILABLE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_API_UNAVAILABLE&#160;&#160;&#160;0x00010006</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>GLFW could not find support for the requested API on the system.</p>
<dl class="section user"><dt>Analysis</dt><dd>The installed graphics driver does not support the requested API, or does not support it via the chosen context creation API. Below are a few examples.</dd></dl>
<dl class="section user"><dt></dt><dd>Some pre-installed Windows graphics drivers do not support OpenGL. AMD only supports OpenGL ES via EGL, while Nvidia and Intel only support it via a WGL or GLX extension. macOS does not provide OpenGL ES at all. The Mesa EGL, OpenGL and OpenGL ES libraries do not interface with the Nvidia binary driver. Older graphics drivers do not support Vulkan. </dd></dl>

</div>
</div>
<a id="gad16c5565b4a69f9c2a9ac2c0dbc89462" name="gad16c5565b4a69f9c2a9ac2c0dbc89462"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad16c5565b4a69f9c2a9ac2c0dbc89462">&#9670;&#160;</a></span>GLFW_VERSION_UNAVAILABLE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_VERSION_UNAVAILABLE&#160;&#160;&#160;0x00010007</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The requested OpenGL or OpenGL ES version (including any requested context or framebuffer hints) is not available on this machine.</p>
<dl class="section user"><dt>Analysis</dt><dd>The machine does not support your requirements. If your application is sufficiently flexible, downgrade your requirements and try again. Otherwise, inform the user that their machine does not match your requirements.</dd></dl>
<dl class="section user"><dt></dt><dd>Future invalid OpenGL and OpenGL ES versions, for example OpenGL 4.8 if 5.0 comes out before the 4.x series gets that far, also fail with this error and not <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a>, because GLFW cannot know what future versions will exist. </dd></dl>

</div>
</div>
<a id="gad44162d78100ea5e87cdd38426b8c7a1" name="gad44162d78100ea5e87cdd38426b8c7a1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad44162d78100ea5e87cdd38426b8c7a1">&#9670;&#160;</a></span>GLFW_PLATFORM_ERROR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_PLATFORM_ERROR&#160;&#160;&#160;0x00010008</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A platform-specific error occurred that does not match any of the more specific categories.</p>
<dl class="section user"><dt>Analysis</dt><dd>A bug or configuration error in GLFW, the underlying operating system or its drivers, or a lack of required resources. Report the issue to our <a href="https://github.com/glfw/glfw/issues">issue tracker</a>. </dd></dl>

</div>
</div>
<a id="ga196e125ef261d94184e2b55c05762f14" name="ga196e125ef261d94184e2b55c05762f14"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga196e125ef261d94184e2b55c05762f14">&#9670;&#160;</a></span>GLFW_FORMAT_UNAVAILABLE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_FORMAT_UNAVAILABLE&#160;&#160;&#160;0x00010009</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If emitted during window creation, the requested pixel format is not supported.</p>
<p>If emitted when querying the clipboard, the contents of the clipboard could not be converted to the requested format.</p>
<dl class="section user"><dt>Analysis</dt><dd>If emitted during window creation, one or more <a class="el" href="window_guide.html#window_hints_hard">hard constraints</a> did not match any of the available pixel formats. If your application is sufficiently flexible, downgrade your requirements and try again. Otherwise, inform the user that their machine does not match your requirements.</dd></dl>
<dl class="section user"><dt></dt><dd>If emitted when querying the clipboard, ignore the error or report it to the user, as appropriate. </dd></dl>

</div>
</div>
<a id="gacff24d2757da752ae4c80bf452356487" name="gacff24d2757da752ae4c80bf452356487"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gacff24d2757da752ae4c80bf452356487">&#9670;&#160;</a></span>GLFW_NO_WINDOW_CONTEXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_NO_WINDOW_CONTEXT&#160;&#160;&#160;0x0001000A</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>A window that does not have an OpenGL or OpenGL ES context was passed to a function that requires it to have one.</p>
<dl class="section user"><dt>Analysis</dt><dd>Application programmer error. Fix the offending call. </dd></dl>

</div>
</div>
<a id="ga09d6943923a70ddef3a085f5baee786c" name="ga09d6943923a70ddef3a085f5baee786c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga09d6943923a70ddef3a085f5baee786c">&#9670;&#160;</a></span>GLFW_CURSOR_UNAVAILABLE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CURSOR_UNAVAILABLE&#160;&#160;&#160;0x0001000B</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The specified standard cursor shape is not available, either because the current platform cursor theme does not provide it or because it is not available on the platform.</p>
<dl class="section user"><dt>Analysis</dt><dd>Platform or system settings limitation. Pick another <a class="el" href="group__shapes.html">standard cursor shape</a> or create a <a class="el" href="input_guide.html#cursor_custom">custom cursor</a>. </dd></dl>

</div>
</div>
<a id="ga526fba20a01504a8086c763b6ca53ce5" name="ga526fba20a01504a8086c763b6ca53ce5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga526fba20a01504a8086c763b6ca53ce5">&#9670;&#160;</a></span>GLFW_FEATURE_UNAVAILABLE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_FEATURE_UNAVAILABLE&#160;&#160;&#160;0x0001000C</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The requested feature is not provided by the platform, so GLFW is unable to implement it. The documentation for each function notes if it could emit this error.</p>
<dl class="section user"><dt>Analysis</dt><dd>Platform or platform version limitation. The error can be ignored unless the feature is critical to the application.</dd></dl>
<dl class="section user"><dt></dt><dd>A function call that emits this error has no effect other than the error and updating any existing out parameters. </dd></dl>

</div>
</div>
<a id="ga5dda77e023e83151e8bd55a6758f946a" name="ga5dda77e023e83151e8bd55a6758f946a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5dda77e023e83151e8bd55a6758f946a">&#9670;&#160;</a></span>GLFW_FEATURE_UNIMPLEMENTED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_FEATURE_UNIMPLEMENTED&#160;&#160;&#160;0x0001000D</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The requested feature has not yet been implemented in GLFW for this platform.</p>
<dl class="section user"><dt>Analysis</dt><dd>An incomplete implementation of GLFW for this platform, hopefully fixed in a future release. The error can be ignored unless the feature is critical to the application.</dd></dl>
<dl class="section user"><dt></dt><dd>A function call that emits this error has no effect other than the error and updating any existing out parameters. </dd></dl>

</div>
</div>
<a id="ga3608c6c29ab7a72f3bf019f4c3a2563d" name="ga3608c6c29ab7a72f3bf019f4c3a2563d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3608c6c29ab7a72f3bf019f4c3a2563d">&#9670;&#160;</a></span>GLFW_PLATFORM_UNAVAILABLE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_PLATFORM_UNAVAILABLE&#160;&#160;&#160;0x0001000E</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If emitted during initialization, no matching platform was found. If the <a class="el" href="intro_guide.html#GLFW_PLATFORM">GLFW_PLATFORM</a> init hint was set to <code>GLFW_ANY_PLATFORM</code>, GLFW could not detect any of the platforms supported by this library binary, except for the Null platform. If the init hint was set to a specific platform, it is either not supported by this library binary or GLFW was not able to detect it.</p>
<p>If emitted by a native access function, GLFW was initialized for a different platform than the function is for.</p>
<dl class="section user"><dt>Analysis</dt><dd>Failure to detect any platform usually only happens on non-macOS Unix systems, either when no window system is running or the program was run from a terminal that does not have the necessary environment variables. Fall back to a different platform if possible or notify the user that no usable platform was detected.</dd></dl>
<p>Failure to detect a specific platform may have the same cause as above or be because support for that platform was not compiled in. Call <a class="el" href="group__init.html#ga8785d2b6b36632368d803e78079d38ed">glfwPlatformSupported</a> to check whether a specific platform is supported by a library binary. </p>

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
