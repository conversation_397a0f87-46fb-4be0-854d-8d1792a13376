//
// This file is auto-generated. Please don't modify it!
//
#pragma once

#ifdef __cplusplus
//#import "opencv.hpp"
#import "opencv2/bioinspired.hpp"
#else
#define CV_EXPORTS
#endif

#import <Foundation/Foundation.h>





NS_ASSUME_NONNULL_BEGIN

// C++: class Bioinspired
/**
 * The Bioinspired module
 *
 * Member classes: `Retina`, `RetinaFastToneMapping`, `TransientAreasSegmentationModule`
 *
 */
CV_EXPORTS @interface Bioinspired : NSObject

#pragma mark - Class Constants


@property (class, readonly) int RETINA_COLOR_RANDOM NS_SWIFT_NAME(RETINA_COLOR_RANDOM);
@property (class, readonly) int RETINA_COLOR_DIAGONAL NS_SWIFT_NAME(RETINA_COLOR_DIAGONAL);
@property (class, readonly) int RETINA_COLOR_BAYER NS_SWIFT_NAME(RETINA_COLOR_BAYER);

#pragma mark - Methods



@end

NS_ASSUME_NONNULL_END


