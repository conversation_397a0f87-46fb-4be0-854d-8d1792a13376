// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Services_Cortana_1_H
#define WINRT_Windows_Services_Cortana_1_H
#include "winrt/impl/Windows.Services.Cortana.0.h"
WINRT_EXPORT namespace winrt::Windows::Services::Cortana
{
    struct WINRT_IMPL_EMPTY_BASES ICortanaActionableInsights :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICortanaActionableInsights>
    {
        ICortanaActionableInsights(std::nullptr_t = nullptr) noexcept {}
        ICortanaActionableInsights(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICortanaActionableInsightsOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICortanaActionableInsightsOptions>
    {
        ICortanaActionableInsightsOptions(std::nullptr_t = nullptr) noexcept {}
        ICortanaActionableInsightsOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICortanaActionableInsightsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICortanaActionableInsightsStatics>
    {
        ICortanaActionableInsightsStatics(std::nullptr_t = nullptr) noexcept {}
        ICortanaActionableInsightsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICortanaPermissionsManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICortanaPermissionsManager>
    {
        ICortanaPermissionsManager(std::nullptr_t = nullptr) noexcept {}
        ICortanaPermissionsManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICortanaPermissionsManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICortanaPermissionsManagerStatics>
    {
        ICortanaPermissionsManagerStatics(std::nullptr_t = nullptr) noexcept {}
        ICortanaPermissionsManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICortanaSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICortanaSettings>
    {
        ICortanaSettings(std::nullptr_t = nullptr) noexcept {}
        ICortanaSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICortanaSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICortanaSettingsStatics>
    {
        ICortanaSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        ICortanaSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
