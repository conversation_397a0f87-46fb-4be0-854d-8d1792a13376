PORTED FUNCs LIST (32 of 32):

 Size cv::bioinspired::Retina::getInputSize()
 Size cv::bioinspired::Retina::getOutputSize()
 void cv::bioinspired::Retina::setup(String retinaParameterFile = "", bool applyDefaultSetupOnFailure = true)
 String cv::bioinspired::Retina::printSetup()
 void cv::bioinspired::Retina::write(String fs)
 void cv::bioinspired::Retina::setupOPLandIPLParvoChannel(bool colorMode = true, bool normaliseOutput = true, float photoreceptorsLocalAdaptationSensitivity = 0.7f, float photoreceptorsTemporalConstant = 0.5f, float photoreceptorsSpatialConstant = 0.53f, float horizontalCellsGain = 0.f, float HcellsTemporalConstant = 1.f, float HcellsSpatialConstant = 7.f, float ganglionCellsSensitivity = 0.7f)
 void cv::bioinspired::Retina::setupIPLMagnoChannel(bool normaliseOutput = true, float parasolCells_beta = 0.f, float parasolCells_tau = 0.f, float parasolCells_k = 7.f, float amacrinCellsTemporalCutFrequency = 1.2f, float V0CompressionParameter = 0.95f, float localAdaptintegration_tau = 0.f, float localAdaptintegration_k = 7.f)
 void cv::bioinspired::Retina::run(Mat inputImage)
 void cv::bioinspired::Retina::applyFastToneMapping(Mat inputImage, Mat& outputToneMappedImage)
 void cv::bioinspired::Retina::getParvo(Mat& retinaOutput_parvo)
 void cv::bioinspired::Retina::getParvoRAW(Mat& retinaOutput_parvo)
 void cv::bioinspired::Retina::getMagno(Mat& retinaOutput_magno)
 void cv::bioinspired::Retina::getMagnoRAW(Mat& retinaOutput_magno)
 Mat cv::bioinspired::Retina::getMagnoRAW()
 Mat cv::bioinspired::Retina::getParvoRAW()
 void cv::bioinspired::Retina::setColorSaturation(bool saturateColors = true, float colorSaturationValue = 4.0f)
 void cv::bioinspired::Retina::clearBuffers()
 void cv::bioinspired::Retina::activateMovingContoursProcessing(bool activate)
 void cv::bioinspired::Retina::activateContoursProcessing(bool activate)
static Ptr_Retina cv::bioinspired::Retina::create(Size inputSize)
static Ptr_Retina cv::bioinspired::Retina::create(Size inputSize, bool colorMode, int colorSamplingMethod = RETINA_COLOR_BAYER, bool useRetinaLogSampling = false, float reductionFactor = 1.0f, float samplingStrength = 10.0f)
 void cv::bioinspired::RetinaFastToneMapping::applyFastToneMapping(Mat inputImage, Mat& outputToneMappedImage)
 void cv::bioinspired::RetinaFastToneMapping::setup(float photoreceptorsNeighborhoodRadius = 3.f, float ganglioncellsNeighborhoodRadius = 1.f, float meanLuminanceModulatorK = 1.f)
static Ptr_RetinaFastToneMapping cv::bioinspired::RetinaFastToneMapping::create(Size inputSize)
 Size cv::bioinspired::TransientAreasSegmentationModule::getSize()
 void cv::bioinspired::TransientAreasSegmentationModule::setup(String segmentationParameterFile = "", bool applyDefaultSetupOnFailure = true)
 String cv::bioinspired::TransientAreasSegmentationModule::printSetup()
 void cv::bioinspired::TransientAreasSegmentationModule::write(String fs)
 void cv::bioinspired::TransientAreasSegmentationModule::run(Mat inputToSegment, int channelIndex = 0)
 void cv::bioinspired::TransientAreasSegmentationModule::getSegmentationPicture(Mat& transientAreas)
 void cv::bioinspired::TransientAreasSegmentationModule::clearAllBuffers()
static Ptr_TransientAreasSegmentationModule cv::bioinspired::TransientAreasSegmentationModule::create(Size inputSize)

SKIPPED FUNCs LIST (0 of 32):


0 def args - 24 funcs
1 def args - 1 funcs
2 def args - 3 funcs
3 def args - 1 funcs
4 def args - 1 funcs
8 def args - 1 funcs
9 def args - 1 funcs