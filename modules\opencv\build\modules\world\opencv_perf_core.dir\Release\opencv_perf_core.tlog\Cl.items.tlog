D:\AI\opencv\opencv-4.10.0\modules\core\perf\cuda\perf_gpumat.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_gpumat.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\opencl\perf_arithm.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\__\core\perf\opencl\perf_arithm.cpp.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\opencl\perf_bufferpool.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_bufferpool.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\opencl\perf_channels.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_channels.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\opencl\perf_dxt.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_dxt.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\opencl\perf_gemm.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_gemm.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\opencl\perf_matop.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_matop.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\opencl\perf_usage_flags.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_usage_flags.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_abs.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_abs.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_addWeighted.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_addWeighted.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_allocation.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_allocation.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_arithm.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\__\core\perf\perf_arithm.cpp.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_bitwise.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_bitwise.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_compare.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_compare.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_convertTo.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_convertTo.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_cvround.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_cvround.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_dft.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_dft.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_dot.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_dot.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_inRange.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_inRange.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_io_base64.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_io_base64.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_lut.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_lut.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_main.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_main.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_mat.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_mat.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_math.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_math.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_merge.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_merge.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_minmaxloc.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_minmaxloc.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_norm.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_norm.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_reduce.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_reduce.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_sort.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_sort.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_split.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_split.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_stat.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_stat.obj
D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_umat.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.dir\Release\perf_umat.obj
