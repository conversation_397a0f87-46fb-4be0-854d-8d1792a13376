<!DOCTYPE html>
<html lang="en">
    <head>
        <title>OpenCV JS Tests</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
        <link rel="stylesheet" href="http://code.jquery.com/qunit/qunit-1.20.0.css" type="text/css" media="screen">
        <style>
            body {
                font-family: Monospace;
                background-color: #ffffff;
                margin: 0px;
            }
            a {
                color: #0040ff;
            }
        </style>
        <script src="http://code.jquery.com/qunit/qunit-2.0.1.js"></script>
        <script type="text/javascript">
          QUnit.config.autostart = false;

          QUnit.log(function(details) {
            if (details.result) {
              return;
            }
            var loc = details.module + ": " + details.name + ": ",
                output = "FAILED: " + loc + ( details.message ? details.message : "" )
                prefix = details.message ? ", " : "";

            if (details.actual) {
              output += prefix + "expected: " + details.expected + ", actual: " + details.actual;
              prefix = ', ';
            }
            if (details.source) {
              output += prefix + details.source;
            }
            console.warn(output);
          });
          QUnit.done(function(details) {
            console.log("Total: " + details.total + " Failed: " + details.failed + " Passed: " + details.passed);
            console.log("Time(ms): " + details.runtime);
          });

          // Helper for opencv.js (see below)
          var Module = {
            preRun: [function() {
              Module.FS_createPreloadedFile('/', 'haarcascade_frontalface_default.xml', 'haarcascade_frontalface_default.xml', true, false);
            }],
            postRun: [] ,
            onRuntimeInitialized: function() {
              console.log("Emscripten runtime is ready, launching QUnit tests...");
              if (window.cv instanceof Promise) {
                window.cv.then((target) => {
                   window.cv = target;
                   console.log(cv.getBuildInformation());
                   QUnit.start();
                })
              } else {
                // for backward compatible
                console.log(cv.getBuildInformation());
                QUnit.start();
              }
            },
            print: (function() {
              var element = document.getElementById('output');
              if (element) element.value = ''; // clear browser cache
              return function(text) {
                console.log(text);
                if (element) {
                  element.value += text + "\n";
                  element.scrollTop = element.scrollHeight; // focus on bottom
                }
              };
            })(),
            printErr: function(text) {
              console.error(text);
            },
            setStatus: function(text) {
              console.log(text);
            },
            totalDependencies: 0
          };

          Module.setStatus('Downloading...');
          window.onerror = function(event) {
            Module.setStatus('Exception thrown, see JavaScript console');
            Module.setStatus = function(text) {
              if (text) Module.printErr('[post-exception status] ' + text);
            };
          };

          function opencvjs_LoadError() {
            Module.printErr('Failed to load/initialize opencv.js');
            QUnit.module('LoaderFatalError', {});
            QUnit.config.module = 'LoaderFatalError';
            QUnit.only("Failed to load OpenCV.js", function(assert) {
              assert.ok(false, "Can't load/initialize opencv.js");
            });
            QUnit.start();
          }
        </script>
    </head>
    <body>

        <div id="qunit"></div>
        <div id="qunit-fixture"></div>

        <script type="application/javascript" async src="opencv.js" onerror="opencvjs_LoadError()"></script>
        <script type="application/javascript" src="test_mat.js"></script>
        <script type="application/javascript" src="test_utils.js"></script>
        <script type="application/javascript" src="test_core.js"></script>
        <script type="application/javascript" src="test_imgproc.js"></script>
        <script type="application/javascript" src="test_objdetect.js"></script>
        <script type="application/javascript" src="test_video.js"></script>
        <script type="application/javascript" src="test_photo.js"></script>
        <script type="application/javascript" src="test_features2d.js"></script>
        <script type="application/javascript" src="test_calib3d.js"></script>
    </body>
</html>
