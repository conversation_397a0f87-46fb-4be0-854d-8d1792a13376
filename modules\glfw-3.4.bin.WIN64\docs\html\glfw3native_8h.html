<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: glfw3native.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_13577e2d8b9423099662de029791bd7d.html">glfw-3.4</a></li><li class="navelem"><a class="el" href="dir_b11153cd0f4fd04a7564cc166f482635.html">include</a></li><li class="navelem"><a class="el" href="dir_7f92719a7fe62e5b064f87d7a3c220b1.html">GLFW</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">glfw3native.h File Reference</div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<div class="textblock"><p>This is the header file of the native access functions. See <a class="el" href="group__native.html">Native access</a> for more information. </p>
</div>
<p><a href="glfw3native_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gad4d3e9242536c0ba6be88a98f4c73a41" id="r_gad4d3e9242536c0ba6be88a98f4c73a41"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gad4d3e9242536c0ba6be88a98f4c73a41">glfwGetWin32Adapter</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:gad4d3e9242536c0ba6be88a98f4c73a41"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the adapter device name of the specified monitor.  <br /></td></tr>
<tr class="separator:gad4d3e9242536c0ba6be88a98f4c73a41"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac845f7dbe4c1d7fdd682a3c6fdae6766" id="r_gac845f7dbe4c1d7fdd682a3c6fdae6766"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gac845f7dbe4c1d7fdd682a3c6fdae6766">glfwGetWin32Monitor</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:gac845f7dbe4c1d7fdd682a3c6fdae6766"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the display device name of the specified monitor.  <br /></td></tr>
<tr class="separator:gac845f7dbe4c1d7fdd682a3c6fdae6766"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafe5079aa79038b0079fc09d5f0a8e667" id="r_gafe5079aa79038b0079fc09d5f0a8e667"><td class="memItemLeft" align="right" valign="top">HWND&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gafe5079aa79038b0079fc09d5f0a8e667">glfwGetWin32Window</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:gafe5079aa79038b0079fc09d5f0a8e667"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>HWND</code> of the specified window.  <br /></td></tr>
<tr class="separator:gafe5079aa79038b0079fc09d5f0a8e667"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadc4010d91d9cc1134d040eeb1202a143" id="r_gadc4010d91d9cc1134d040eeb1202a143"><td class="memItemLeft" align="right" valign="top">HGLRC&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gadc4010d91d9cc1134d040eeb1202a143">glfwGetWGLContext</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:gadc4010d91d9cc1134d040eeb1202a143"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>HGLRC</code> of the specified window.  <br /></td></tr>
<tr class="separator:gadc4010d91d9cc1134d040eeb1202a143"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf22f429aec4b1aab316142d66d9be3e6" id="r_gaf22f429aec4b1aab316142d66d9be3e6"><td class="memItemLeft" align="right" valign="top">CGDirectDisplayID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gaf22f429aec4b1aab316142d66d9be3e6">glfwGetCocoaMonitor</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:gaf22f429aec4b1aab316142d66d9be3e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>CGDirectDisplayID</code> of the specified monitor.  <br /></td></tr>
<tr class="separator:gaf22f429aec4b1aab316142d66d9be3e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac3ed9d495d0c2bb9652de5a50c648715" id="r_gac3ed9d495d0c2bb9652de5a50c648715"><td class="memItemLeft" align="right" valign="top">id&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gac3ed9d495d0c2bb9652de5a50c648715">glfwGetCocoaWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:gac3ed9d495d0c2bb9652de5a50c648715"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>NSWindow</code> of the specified window.  <br /></td></tr>
<tr class="separator:gac3ed9d495d0c2bb9652de5a50c648715"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7274fb6595894e880fc95dc63156e9b1" id="r_ga7274fb6595894e880fc95dc63156e9b1"><td class="memItemLeft" align="right" valign="top">id&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga7274fb6595894e880fc95dc63156e9b1">glfwGetCocoaView</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga7274fb6595894e880fc95dc63156e9b1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>NSView</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga7274fb6595894e880fc95dc63156e9b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga559e002e3cd63c979881770cd4dc63bc" id="r_ga559e002e3cd63c979881770cd4dc63bc"><td class="memItemLeft" align="right" valign="top">id&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga559e002e3cd63c979881770cd4dc63bc">glfwGetNSGLContext</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga559e002e3cd63c979881770cd4dc63bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>NSOpenGLContext</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga559e002e3cd63c979881770cd4dc63bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6e7822385cc8a1cc3b18f60352830189" id="r_ga6e7822385cc8a1cc3b18f60352830189"><td class="memItemLeft" align="right" valign="top">Display *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga6e7822385cc8a1cc3b18f60352830189">glfwGetX11Display</a> (void)</td></tr>
<tr class="memdesc:ga6e7822385cc8a1cc3b18f60352830189"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>Display</code> used by GLFW.  <br /></td></tr>
<tr class="separator:ga6e7822385cc8a1cc3b18f60352830189"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga088fbfa80f50569402b41be71ad66e40" id="r_ga088fbfa80f50569402b41be71ad66e40"><td class="memItemLeft" align="right" valign="top">RRCrtc&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga088fbfa80f50569402b41be71ad66e40">glfwGetX11Adapter</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:ga088fbfa80f50569402b41be71ad66e40"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>RRCrtc</code> of the specified monitor.  <br /></td></tr>
<tr class="separator:ga088fbfa80f50569402b41be71ad66e40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab2f8cc043905e9fa9b12bfdbbcfe874c" id="r_gab2f8cc043905e9fa9b12bfdbbcfe874c"><td class="memItemLeft" align="right" valign="top">RROutput&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gab2f8cc043905e9fa9b12bfdbbcfe874c">glfwGetX11Monitor</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:gab2f8cc043905e9fa9b12bfdbbcfe874c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>RROutput</code> of the specified monitor.  <br /></td></tr>
<tr class="separator:gab2f8cc043905e9fa9b12bfdbbcfe874c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga90ca676322740842db446999a1b1f21d" id="r_ga90ca676322740842db446999a1b1f21d"><td class="memItemLeft" align="right" valign="top">Window&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga90ca676322740842db446999a1b1f21d">glfwGetX11Window</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga90ca676322740842db446999a1b1f21d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>Window</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga90ca676322740842db446999a1b1f21d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga55f879ab02d93367f966186b6f0133f7" id="r_ga55f879ab02d93367f966186b6f0133f7"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga55f879ab02d93367f966186b6f0133f7">glfwSetX11SelectionString</a> (const char *string)</td></tr>
<tr class="memdesc:ga55f879ab02d93367f966186b6f0133f7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the current primary selection to the specified string.  <br /></td></tr>
<tr class="separator:ga55f879ab02d93367f966186b6f0133f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae084ef64dc0db140b455b1427256d3f7" id="r_gae084ef64dc0db140b455b1427256d3f7"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gae084ef64dc0db140b455b1427256d3f7">glfwGetX11SelectionString</a> (void)</td></tr>
<tr class="memdesc:gae084ef64dc0db140b455b1427256d3f7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the contents of the current primary selection as a string.  <br /></td></tr>
<tr class="separator:gae084ef64dc0db140b455b1427256d3f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga62d884114b0abfcdc2930e89f20867e2" id="r_ga62d884114b0abfcdc2930e89f20867e2"><td class="memItemLeft" align="right" valign="top">GLXContext&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga62d884114b0abfcdc2930e89f20867e2">glfwGetGLXContext</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga62d884114b0abfcdc2930e89f20867e2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>GLXContext</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga62d884114b0abfcdc2930e89f20867e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1ed27b8766e859a21381e8f8ce18d049" id="r_ga1ed27b8766e859a21381e8f8ce18d049"><td class="memItemLeft" align="right" valign="top">GLXWindow&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga1ed27b8766e859a21381e8f8ce18d049">glfwGetGLXWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga1ed27b8766e859a21381e8f8ce18d049"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>GLXWindow</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga1ed27b8766e859a21381e8f8ce18d049"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacbe11f93ce20621de82989bbba94e62a" id="r_gacbe11f93ce20621de82989bbba94e62a"><td class="memItemLeft" align="right" valign="top">struct wl_display *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gacbe11f93ce20621de82989bbba94e62a">glfwGetWaylandDisplay</a> (void)</td></tr>
<tr class="memdesc:gacbe11f93ce20621de82989bbba94e62a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>struct wl_display*</code> used by GLFW.  <br /></td></tr>
<tr class="separator:gacbe11f93ce20621de82989bbba94e62a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4f16066bd4c59e2f99418adfcb43dd16" id="r_ga4f16066bd4c59e2f99418adfcb43dd16"><td class="memItemLeft" align="right" valign="top">struct wl_output *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga4f16066bd4c59e2f99418adfcb43dd16">glfwGetWaylandMonitor</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:ga4f16066bd4c59e2f99418adfcb43dd16"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>struct wl_output*</code> of the specified monitor.  <br /></td></tr>
<tr class="separator:ga4f16066bd4c59e2f99418adfcb43dd16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5c597f2841229d9626f0811cca41ceb3" id="r_ga5c597f2841229d9626f0811cca41ceb3"><td class="memItemLeft" align="right" valign="top">struct wl_surface *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga5c597f2841229d9626f0811cca41ceb3">glfwGetWaylandWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga5c597f2841229d9626f0811cca41ceb3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the main <code>struct wl_surface*</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga5c597f2841229d9626f0811cca41ceb3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1cd8d973f47aacb5532d368147cc3138" id="r_ga1cd8d973f47aacb5532d368147cc3138"><td class="memItemLeft" align="right" valign="top">EGLDisplay&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga1cd8d973f47aacb5532d368147cc3138">glfwGetEGLDisplay</a> (void)</td></tr>
<tr class="memdesc:ga1cd8d973f47aacb5532d368147cc3138"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>EGLDisplay</code> used by GLFW.  <br /></td></tr>
<tr class="separator:ga1cd8d973f47aacb5532d368147cc3138"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga671c5072becd085f4ab5771a9c8efcf1" id="r_ga671c5072becd085f4ab5771a9c8efcf1"><td class="memItemLeft" align="right" valign="top">EGLContext&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga671c5072becd085f4ab5771a9c8efcf1">glfwGetEGLContext</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga671c5072becd085f4ab5771a9c8efcf1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>EGLContext</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga671c5072becd085f4ab5771a9c8efcf1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2199b36117a6a695fec8441d8052eee6" id="r_ga2199b36117a6a695fec8441d8052eee6"><td class="memItemLeft" align="right" valign="top">EGLSurface&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga2199b36117a6a695fec8441d8052eee6">glfwGetEGLSurface</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga2199b36117a6a695fec8441d8052eee6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>EGLSurface</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga2199b36117a6a695fec8441d8052eee6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3b36e3e3dcf308b776427b6bd73cc132" id="r_ga3b36e3e3dcf308b776427b6bd73cc132"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga3b36e3e3dcf308b776427b6bd73cc132">glfwGetOSMesaColorBuffer</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int *width, int *height, int *format, void **buffer)</td></tr>
<tr class="memdesc:ga3b36e3e3dcf308b776427b6bd73cc132"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the color buffer associated with the specified window.  <br /></td></tr>
<tr class="separator:ga3b36e3e3dcf308b776427b6bd73cc132"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6b64039ffc88a7a2f57f0956c0c75d53" id="r_ga6b64039ffc88a7a2f57f0956c0c75d53"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga6b64039ffc88a7a2f57f0956c0c75d53">glfwGetOSMesaDepthBuffer</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int *width, int *height, int *bytesPerValue, void **buffer)</td></tr>
<tr class="memdesc:ga6b64039ffc88a7a2f57f0956c0c75d53"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the depth buffer associated with the specified window.  <br /></td></tr>
<tr class="separator:ga6b64039ffc88a7a2f57f0956c0c75d53"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9e47700080094eb569cb053afaa88773" id="r_ga9e47700080094eb569cb053afaa88773"><td class="memItemLeft" align="right" valign="top">OSMesaContext&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga9e47700080094eb569cb053afaa88773">glfwGetOSMesaContext</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga9e47700080094eb569cb053afaa88773"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>OSMesaContext</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga9e47700080094eb569cb053afaa88773"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
