
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:17 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake:54 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:61 (_cmake_find_compiler)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    mode: "program"
    variable: "CMAKE_ASM_NASM_COMPILER"
    description: "ASM_NASM compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "nasm"
      - "yasm"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/nasm.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/nasm.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/nasm"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/yasm.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/yasm.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/yasm"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.1.0\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is GNU using "--version" did not match "(GNU assembler)|(GCC)|(Free Software Foundation)":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is AppleClang using "--version" did not match "(Apple (clang|LLVM) version)":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is Clang using "--version" did not match "(clang version)":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is ARMClang using "--version" did not match "armclang":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is OrangeC using "--version" did not match "occ \\(OrangeC\\) Version":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is HP using "-V" did not match "HP C":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is Intel using "--version" did not match "(ICC)":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is IntelLLVM using "--version" did not match "(Intel[^
      ]+oneAPI)":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is SunPro using "-V" did not match "Sun C":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is XL using "-qversion" did not match "XL C":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is MSVC using "-?" did not match "Microsoft.*Macro Assembler":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is TI using "-h" did not match "Texas Instruments":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is TIClang using "--version" did not match "(TI (.*) Clang Compiler)":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is IAR using "" did not match "IAR Assembler":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is Diab using "-V" did not match "Wind River Systems":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is ARMCC using "" did not match "(ARM Compiler)|(ARM Assembler)|(Arm Compiler)":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is NASM using "-v" did not match "(NASM version)":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is YASM using "--version" did not match "(yasm)":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is ADSP using "-version" did not match "Analog Devices":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is QCC using "-V" did not match "gcc_nto":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is Tasking using "--version" did not match "TASKING":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1303 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    message: |
      Checking whether the ASM_NASM compiler is Renesas using "-v" did not match "(RX Family C/C\\+\\+ Compiler)|(RL78 Family Compiler)|(RH850 Family Compiler)":
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:103 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:267 (include)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
    searched_directories:
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/link.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/link.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/link"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/link.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/link.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/link"
      - "C:/Windows/System32/link.com"
      - "C:/Windows/System32/link.exe"
      - "C:/Windows/System32/link"
      - "C:/Windows/link.com"
      - "C:/Windows/link.exe"
      - "C:/Windows/link"
      - "C:/Windows/System32/wbem/link.com"
      - "C:/Windows/System32/wbem/link.exe"
      - "C:/Windows/System32/wbem/link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/link"
      - "C:/Windows/System32/OpenSSH/link.com"
      - "C:/Windows/System32/OpenSSH/link.exe"
      - "C:/Windows/System32/OpenSSH/link"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/link.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/link.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/link"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/link"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/link.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/link.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/link"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/link.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/link"
      - "C:/Program Files/CMake/bin/link.com"
      - "C:/Program Files/CMake/bin/link.exe"
      - "C:/Program Files/CMake/bin/link"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/link.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/link.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.1.0\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:267 (include)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
    searched_directories:
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/lld-link.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/lld-link.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/lld-link"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/lld-link.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/lld-link.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/lld-link.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/lld-link.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/lld-link.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/lld-link.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.1.0\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:267 (include)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
    searched_directories:
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/link.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/link.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/link"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/link.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/link.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/link"
      - "C:/Windows/System32/link.com"
      - "C:/Windows/System32/link.exe"
      - "C:/Windows/System32/link"
      - "C:/Windows/link.com"
      - "C:/Windows/link.exe"
      - "C:/Windows/link"
      - "C:/Windows/System32/wbem/link.com"
      - "C:/Windows/System32/wbem/link.exe"
      - "C:/Windows/System32/wbem/link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/link"
      - "C:/Windows/System32/OpenSSH/link.com"
      - "C:/Windows/System32/OpenSSH/link.exe"
      - "C:/Windows/System32/OpenSSH/link"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/link.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/link.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/link"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/link"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/link.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/link.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/link"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/link.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/link"
      - "C:/Program Files/CMake/bin/link.com"
      - "C:/Program Files/CMake/bin/link.exe"
      - "C:/Program Files/CMake/bin/link"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/link.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/link.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.1.0\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:267 (include)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    mode: "program"
    variable: "CMAKE_MT"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mt"
    candidate_directories:
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
    searched_directories:
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/mt.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/mt.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/mt"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/mt.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/mt.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/mt"
      - "C:/Windows/System32/mt.com"
      - "C:/Windows/System32/mt.exe"
      - "C:/Windows/System32/mt"
      - "C:/Windows/mt.com"
      - "C:/Windows/mt.exe"
      - "C:/Windows/mt"
      - "C:/Windows/System32/wbem/mt.com"
      - "C:/Windows/System32/wbem/mt.exe"
      - "C:/Windows/System32/wbem/mt"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt"
      - "C:/Windows/System32/OpenSSH/mt.com"
      - "C:/Windows/System32/OpenSSH/mt.exe"
      - "C:/Windows/System32/OpenSSH/mt"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mt.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mt.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/mt.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/mt.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/mt"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt"
      - "C:/Program Files/CMake/bin/mt.com"
      - "C:/Program Files/CMake/bin/mt.exe"
      - "C:/Program Files/CMake/bin/mt"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/mt.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/mt.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/mt"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.1.0\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:267 (include)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASM_NASMCompiler.cmake:22 (include)"
      - "CMakeLists.txt:17 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lib"
    candidate_directories:
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
    searched_directories:
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/lib.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/lib.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/lib"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/lib.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/lib.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/libnvvp/lib"
      - "C:/Windows/System32/lib.com"
      - "C:/Windows/System32/lib.exe"
      - "C:/Windows/System32/lib"
      - "C:/Windows/lib.com"
      - "C:/Windows/lib.exe"
      - "C:/Windows/lib"
      - "C:/Windows/System32/wbem/lib.com"
      - "C:/Windows/System32/wbem/lib.exe"
      - "C:/Windows/System32/wbem/lib"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lib.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lib.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lib"
      - "C:/Windows/System32/OpenSSH/lib.com"
      - "C:/Windows/System32/OpenSSH/lib.exe"
      - "C:/Windows/System32/OpenSSH/lib"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lib.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lib.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lib"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lib.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lib.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lib"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/lib.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/lib.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/lib"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lib.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lib.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lib"
      - "C:/Program Files/CMake/bin/lib.com"
      - "C:/Program Files/CMake/bin/lib.exe"
      - "C:/Program Files/CMake/bin/lib"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/lib.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/lib.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.1.0/lib"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/lib.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/lib.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Scripts/lib"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/lib.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/lib.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python311/lib"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lib.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lib.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lib"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.1.0\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
...
