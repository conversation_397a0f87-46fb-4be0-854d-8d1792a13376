/* The standard CSS for doxygen 1.8.6 */

body, table, div, p, dl {
    font: 400 14px/22px Helvetica, 'Segoe UI', <PERSON><PERSON>, freesans, sans-serif;
    word-wrap: break-word;
}

code {
    font-size: 85%;
    font-family: "SFMono-Regular",<PERSON><PERSON><PERSON>,"Liberation Mono",<PERSON><PERSON>,Courier,monospace;
    white-space: pre-wrap;
    padding: 1px 5px;
    background-color: rgb(223, 229, 241);
    vertical-align: baseline;
}

body {
    background-image: url(bodybg.png);
    margin: 0 auto;
}

div.fragment {
    padding: 3px;
    padding-bottom: 0px;
}

div.line {
    padding-bottom: 3px;
    font-family: "SFMono-Regular",<PERSON><PERSON><PERSON>,"Liberation Mono",<PERSON>lo,Courier,monospace;
}

div.contents {
    width: 980px;
    margin: 0 auto;
    padding: 15px 15px;
    border: 1px solid rgb(10, 80, 122);
    background-color: #fff;
}

span.arrow {
    height: 13px;
}

div.image img{
    max-width: 900px;
}

#projectlogo
{
    text-align: center;
    vertical-align: middle;
    border-collapse: separate;
    padding-left: 0.5em;
}
