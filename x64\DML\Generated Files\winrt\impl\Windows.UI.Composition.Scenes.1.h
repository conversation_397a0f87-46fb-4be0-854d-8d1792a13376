// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_UI_Composition_Scenes_1_H
#define WINRT_Windows_UI_Composition_Scenes_1_H
#include "winrt/impl/Windows.UI.Composition.Scenes.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Composition::Scenes
{
    struct WINRT_IMPL_EMPTY_BASES ISceneBoundingBox :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneBoundingBox>
    {
        ISceneBoundingBox(std::nullptr_t = nullptr) noexcept {}
        ISceneBoundingBox(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneComponent :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneComponent>
    {
        ISceneComponent(std::nullptr_t = nullptr) noexcept {}
        ISceneComponent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneComponentCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneComponentCollection>
    {
        ISceneComponentCollection(std::nullptr_t = nullptr) noexcept {}
        ISceneComponentCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneComponentFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneComponentFactory>
    {
        ISceneComponentFactory(std::nullptr_t = nullptr) noexcept {}
        ISceneComponentFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneMaterial :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMaterial>
    {
        ISceneMaterial(std::nullptr_t = nullptr) noexcept {}
        ISceneMaterial(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneMaterialFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMaterialFactory>
    {
        ISceneMaterialFactory(std::nullptr_t = nullptr) noexcept {}
        ISceneMaterialFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneMaterialInput :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMaterialInput>
    {
        ISceneMaterialInput(std::nullptr_t = nullptr) noexcept {}
        ISceneMaterialInput(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneMaterialInputFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMaterialInputFactory>
    {
        ISceneMaterialInputFactory(std::nullptr_t = nullptr) noexcept {}
        ISceneMaterialInputFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneMesh :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMesh>
    {
        ISceneMesh(std::nullptr_t = nullptr) noexcept {}
        ISceneMesh(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneMeshMaterialAttributeMap :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMeshMaterialAttributeMap>
    {
        ISceneMeshMaterialAttributeMap(std::nullptr_t = nullptr) noexcept {}
        ISceneMeshMaterialAttributeMap(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneMeshRendererComponent :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMeshRendererComponent>
    {
        ISceneMeshRendererComponent(std::nullptr_t = nullptr) noexcept {}
        ISceneMeshRendererComponent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneMeshRendererComponentStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMeshRendererComponentStatics>
    {
        ISceneMeshRendererComponentStatics(std::nullptr_t = nullptr) noexcept {}
        ISceneMeshRendererComponentStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneMeshStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMeshStatics>
    {
        ISceneMeshStatics(std::nullptr_t = nullptr) noexcept {}
        ISceneMeshStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneMetallicRoughnessMaterial :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMetallicRoughnessMaterial>
    {
        ISceneMetallicRoughnessMaterial(std::nullptr_t = nullptr) noexcept {}
        ISceneMetallicRoughnessMaterial(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneMetallicRoughnessMaterialStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMetallicRoughnessMaterialStatics>
    {
        ISceneMetallicRoughnessMaterialStatics(std::nullptr_t = nullptr) noexcept {}
        ISceneMetallicRoughnessMaterialStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneModelTransform :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneModelTransform>
    {
        ISceneModelTransform(std::nullptr_t = nullptr) noexcept {}
        ISceneModelTransform(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneNode :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneNode>
    {
        ISceneNode(std::nullptr_t = nullptr) noexcept {}
        ISceneNode(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneNodeCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneNodeCollection>
    {
        ISceneNodeCollection(std::nullptr_t = nullptr) noexcept {}
        ISceneNodeCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneNodeStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneNodeStatics>
    {
        ISceneNodeStatics(std::nullptr_t = nullptr) noexcept {}
        ISceneNodeStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneObject :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneObject>
    {
        ISceneObject(std::nullptr_t = nullptr) noexcept {}
        ISceneObject(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneObjectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneObjectFactory>
    {
        ISceneObjectFactory(std::nullptr_t = nullptr) noexcept {}
        ISceneObjectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IScenePbrMaterial :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScenePbrMaterial>
    {
        IScenePbrMaterial(std::nullptr_t = nullptr) noexcept {}
        IScenePbrMaterial(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IScenePbrMaterialFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScenePbrMaterialFactory>
    {
        IScenePbrMaterialFactory(std::nullptr_t = nullptr) noexcept {}
        IScenePbrMaterialFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneRendererComponent :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneRendererComponent>
    {
        ISceneRendererComponent(std::nullptr_t = nullptr) noexcept {}
        ISceneRendererComponent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneRendererComponentFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneRendererComponentFactory>
    {
        ISceneRendererComponentFactory(std::nullptr_t = nullptr) noexcept {}
        ISceneRendererComponentFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneSurfaceMaterialInput :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneSurfaceMaterialInput>
    {
        ISceneSurfaceMaterialInput(std::nullptr_t = nullptr) noexcept {}
        ISceneSurfaceMaterialInput(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneSurfaceMaterialInputStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneSurfaceMaterialInputStatics>
    {
        ISceneSurfaceMaterialInputStatics(std::nullptr_t = nullptr) noexcept {}
        ISceneSurfaceMaterialInputStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneVisual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneVisual>
    {
        ISceneVisual(std::nullptr_t = nullptr) noexcept {}
        ISceneVisual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISceneVisualStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneVisualStatics>
    {
        ISceneVisualStatics(std::nullptr_t = nullptr) noexcept {}
        ISceneVisualStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
