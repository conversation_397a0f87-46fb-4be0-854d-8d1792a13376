Image Processing in OpenCV {#tutorial_py_table_of_contents_imgproc}
==========================

-   @subpage tutorial_py_colorspaces

    Learn to change images between different color spaces.
    Plus learn to track a colored object in a video.

-   @subpage tutorial_py_geometric_transformations

    Learn to apply different geometric transformations to images like rotation, translation etc.

-   @subpage tutorial_py_thresholding

    Learn
    to convert images to binary images using global thresholding, Adaptive thresholding, <PERSON><PERSON>'s
    binarization etc

-   @subpage tutorial_py_filtering

    Learn
    to blur the images, filter the images with custom kernels etc.

-   @subpage tutorial_py_morphological_ops

    Learn about morphological transformations like E<PERSON>ion, Dilation, Opening, Closing etc

-   @subpage tutorial_py_gradients

    Learn
    to find image gradients, edges etc.

-   @subpage tutorial_py_canny

    Learn
    to find edges with Canny Edge Detection

-   @subpage tutorial_py_pyramids

    Learn about image pyramids and how to use them for image blending

-   @subpage tutorial_py_table_of_contents_contours

    All
    about Contours in OpenCV

-   @subpage tutorial_py_table_of_contents_histograms

    All
    about histograms in OpenCV

-   @subpage tutorial_py_table_of_contents_transforms

    Meet
    different Image Transforms in OpenCV like Fourier Transform, Cosine Transform etc.

-   @subpage tutorial_py_template_matching

    Learn
    to search for an object in an image using Template Matching

-   @subpage tutorial_py_houghlines

    Learn to detect lines in an image

-   @subpage tutorial_py_houghcircles

    Learn to detect circles in an image

-   @subpage tutorial_py_watershed

    Learn to segment images with watershed segmentation

-   @subpage tutorial_py_grabcut

    Learn to extract foreground with GrabCut algorithm
