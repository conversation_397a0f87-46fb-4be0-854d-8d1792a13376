﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\optflow\perf\opencl\perf_optflow_dualTVL1.cpp">
      <Filter>opencv_optflow\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\optflow\perf\perf_deepflow.cpp">
      <Filter>opencv_optflow\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\optflow\perf\perf_main.cpp">
      <Filter>opencv_optflow\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\optflow\perf\perf_rlof.cpp">
      <Filter>opencv_optflow\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\optflow\perf\perf_tvl1optflow.cpp">
      <Filter>opencv_optflow\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\optflow\perf\perf_precomp.hpp">
      <Filter>opencv_optflow\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_optflow">
      <UniqueIdentifier>{95B6605D-0F9F-3AFB-9C9C-B64F5055E586}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_optflow\Include">
      <UniqueIdentifier>{789BFA7B-08A7-35AA-960E-2F323C9B9B64}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_optflow\Src">
      <UniqueIdentifier>{FF28A6E8-B1C3-3233-919C-EA45FDE0DA69}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_optflow\Src\opencl">
      <UniqueIdentifier>{0297C67C-1773-37C4-9FD5-1F83167DAABE}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
