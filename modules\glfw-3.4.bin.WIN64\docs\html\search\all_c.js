var searchData=
[
  ['features_0',['features',['../moving_guide.html#moving_removed',1,'Changed and removed features'],['../news.html#features',1,'New features']]],
  ['fetching_20function_20pointers_1',['Fetching function pointers',['../context_guide.html#context_glext_proc',1,'']]],
  ['file_2',['file',['../build_guide.html#build_include',1,'Including the GLFW header file'],['../vulkan_guide.html#vulkan_include',1,'Including the Vulkan header file'],['../moving_guide.html#moving_renamed_files',1,'Renamed library and header file']]],
  ['files_20with_20cmake_3',['Generating build files with CMake',['../compile_guide.html#compile_generate',1,'']]],
  ['finding_20the_20vulkan_20loader_4',['Finding the Vulkan loader',['../vulkan_guide.html#vulkan_loader',1,'']]],
  ['flag_5',['flag',['../quick_guide.html#quick_window_close',1,'Checking the window close flag'],['../window_guide.html#window_close',1,'Window closing and close flag']]],
  ['flags_6',['Modifier key flags',['../group__mods.html',1,'']]],
  ['focus_7',['Window input focus',['../window_guide.html#window_focus',1,'']]],
  ['for_20custom_20heap_20memory_20allocator_8',['Support for custom heap memory allocator',['../news.html#custom_heap_allocator',1,'']]],
  ['for_20earlier_20versions_9',['Release notes for earlier versions',['../news.html#news_archive',1,'']]],
  ['for_20extensions_10',['Checking for extensions',['../context_guide.html#context_glext_string',1,'']]],
  ['for_20framebuffer_20scaling_11',['Window hint for framebuffer scaling',['../news.html#scale_framebuffer_hint',1,'']]],
  ['for_20initial_20window_20position_12',['Window hints for initial window position',['../news.html#window_position_hint',1,'']]],
  ['for_20version_203_204_13',['Release notes for version 3.4',['../news.html',1,'']]],
  ['for_20versions_20of_20windows_20older_20than_20xp_14',['Support for versions of Windows older than XP',['../moving_guide.html#moving_windows',1,'']]],
  ['for_20vulkan_20presentation_20support_15',['Querying for Vulkan presentation support',['../vulkan_guide.html#vulkan_present',1,'']]],
  ['for_20vulkan_20support_16',['Querying for Vulkan support',['../vulkan_guide.html#vulkan_support',1,'']]],
  ['for_20wayland_20and_20x11_17',['Dependencies for Wayland and X11',['../compile_guide.html#compile_deps_wayland',1,'']]],
  ['format_20has_20been_20changed_18',['Version string format has been changed',['../news.html#version_string_caveat',1,'']]],
  ['framebuffer_20may_20lack_20alpha_20channel_20on_20older_20systems_19',['Wayland framebuffer may lack alpha channel on older systems',['../news.html#wayland_alpha_caveat',1,'']]],
  ['framebuffer_20related_20attributes_20',['Framebuffer related attributes',['../window_guide.html#window_attribs_fb',1,'']]],
  ['framebuffer_20related_20hints_21',['Framebuffer related hints',['../window_guide.html#window_hints_fb',1,'']]],
  ['framebuffer_20scaling_22',['Window hint for framebuffer scaling',['../news.html#scale_framebuffer_hint',1,'']]],
  ['framebuffer_20size_23',['Framebuffer size',['../window_guide.html#window_fbsize',1,'']]],
  ['framebuffer_20sizes_24',['Separation of window and framebuffer sizes',['../moving_guide.html#moving_hidpi',1,'']]],
  ['framebuffer_20transparency_20requires_20dwm_20transparency_25',['Windows 7 framebuffer transparency requires DWM transparency',['../news.html#win7_framebuffer_caveat',1,'']]],
  ['from_20glfw_202_20to_203_26',['Moving from GLFW 2 to 3',['../moving_guide.html',1,'']]],
  ['full_20screen_20windows_27',['full screen windows',['../window_guide.html#window_windowed_full_screen',1,'&quot;Windowed full screen&quot; windows'],['../window_guide.html#window_full_screen',1,'Full screen windows']]],
  ['function_28',['Cocoa NSView native access function',['../news.html#cocoa_nsview_function',1,'']]],
  ['function_20changes_29',['Joystick function changes',['../moving_guide.html#moving_joystick',1,'']]],
  ['function_20pointers_30',['function pointers',['../context_guide.html#context_glext_proc',1,'Fetching function pointers'],['../vulkan_guide.html#vulkan_proc',1,'Querying Vulkan function pointers']]],
  ['functions_31',['functions',['../news.html#multiplatform_caveat',1,'Multiple sets of native access functions'],['../news.html#new_functions',1,'New functions'],['../moving_guide.html#moving_threads',1,'Removal of threading functions'],['../moving_guide.html#moving_renamed_functions',1,'Renamed functions'],['../internals_guide.html#internals_static',1,'Static functions']]]
];
