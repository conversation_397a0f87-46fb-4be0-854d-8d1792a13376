// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Graphics_1_H
#define WINRT_Windows_Graphics_1_H
#include "winrt/impl/Windows.Graphics.0.h"
WINRT_EXPORT namespace winrt::Windows::Graphics
{
    struct WINRT_IMPL_EMPTY_BASES IGeometrySource2D :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGeometrySource2D>
    {
        IGeometrySource2D(std::nullptr_t = nullptr) noexcept {}
        IGeometrySource2D(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
