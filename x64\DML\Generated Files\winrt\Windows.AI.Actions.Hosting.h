// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_AI_Actions_Hosting_H
#define WINRT_Windows_AI_Actions_Hosting_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.240405.15"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.240405.15"
#include "winrt/Windows.AI.Actions.h"
#include "winrt/impl/Windows.AI.Actions.2.h"
#include "winrt/impl/Windows.Foundation.2.h"
#include "winrt/impl/Windows.UI.2.h"
#include "winrt/impl/Windows.AI.Actions.Hosting.2.h"
namespace winrt::impl
{
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionCatalog<D>::GetAllActions() const
    {
        uint32_t result_impl_size{};
        void** result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionCatalog)->GetAllActions(&result_impl_size, &result));
        return com_array<winrt::Windows::AI::Actions::Hosting::ActionDefinition>{ result, result_impl_size, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionCatalog<D>::Changed(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::AI::Actions::Hosting::ActionCatalog, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionCatalog)->add_Changed(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionCatalog<D>::Changed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::AI::Actions::Hosting::ActionCatalog, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, Changed_revoker>(this, Changed(handler));
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionCatalog<D>::Changed(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionCatalog)->remove_Changed(impl::bind_in(token));
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionCatalog2<D>::GetActionsForInputs(array_view<winrt::Windows::AI::Actions::ActionEntity const> inputEntities) const
    {
        uint32_t result_impl_size{};
        void** result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionCatalog2)->GetActionsForInputs(inputEntities.size(), get_abi(inputEntities), &result_impl_size, &result));
        return com_array<winrt::Windows::AI::Actions::Hosting::ActionInstance>{ result, result_impl_size, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionCatalog2<D>::GetActionsForInputs(array_view<winrt::Windows::AI::Actions::ActionEntity const> inputEntities, winrt::Windows::UI::WindowId const& invokerWindowId) const
    {
        uint32_t result_impl_size{};
        void** result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionCatalog2)->GetActionsForInputs2(inputEntities.size(), get_abi(inputEntities), impl::bind_in(invokerWindowId), &result_impl_size, &result));
        return com_array<winrt::Windows::AI::Actions::Hosting::ActionInstance>{ result, result_impl_size, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionDefinition<D>::Id() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionDefinition)->get_Id(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionDefinition<D>::Description() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionDefinition)->get_Description(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionDefinition<D>::IconFullPath() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionDefinition)->get_IconFullPath(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionDefinition<D>::PackageFamilyName() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionDefinition)->get_PackageFamilyName(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionDefinition<D>::GetInputs() const
    {
        uint32_t result_impl_size{};
        void** result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionDefinition)->GetInputs(&result_impl_size, &result));
        return com_array<winrt::Windows::AI::Actions::Hosting::ActionEntityRegistrationInfo>{ result, result_impl_size, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionDefinition<D>::GetOutputs() const
    {
        uint32_t result_impl_size{};
        void** result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionDefinition)->GetOutputs(&result_impl_size, &result));
        return com_array<winrt::Windows::AI::Actions::Hosting::ActionEntityRegistrationInfo>{ result, result_impl_size, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionDefinition<D>::GetOverloads() const
    {
        uint32_t result_impl_size{};
        void** result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionDefinition)->GetOverloads(&result_impl_size, &result));
        return com_array<winrt::Windows::AI::Actions::Hosting::ActionOverload>{ result, result_impl_size, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionDefinition2<D>::DisplaysUI() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionDefinition2)->get_DisplaysUI(&value));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionDefinition2<D>::UsesGenerativeAI() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionDefinition2)->get_UsesGenerativeAI(&value));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionDefinition2<D>::SchemaVersion() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionDefinition2)->get_SchemaVersion(&value));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionDefinition3<D>::PackageRelativeApplicationId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionDefinition3)->get_PackageRelativeApplicationId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionEntityRegistrationInfo<D>::Name() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionEntityRegistrationInfo)->get_Name(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionEntityRegistrationInfo<D>::Name(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionEntityRegistrationInfo)->put_Name(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionEntityRegistrationInfo<D>::Kind() const
    {
        winrt::Windows::AI::Actions::ActionEntityKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionEntityRegistrationInfo)->get_Kind(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionEntityRegistrationInfo<D>::Kind(winrt::Windows::AI::Actions::ActionEntityKind const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionEntityRegistrationInfo)->put_Kind(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionInstance<D>::DisplayInfo() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionInstance)->get_DisplayInfo(&value));
        return winrt::Windows::AI::Actions::Hosting::ActionInstanceDisplayInfo{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionInstance<D>::Definition() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionInstance)->get_Definition(&value));
        return winrt::Windows::AI::Actions::Hosting::ActionDefinition{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionInstance<D>::Context() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionInstance)->get_Context(&value));
        return winrt::Windows::AI::Actions::ActionInvocationContext{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionInstance<D>::InvokeAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionInstance)->InvokeAsync(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionInstanceDisplayInfo<D>::Description() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionInstanceDisplayInfo)->get_Description(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionOverload<D>::DescriptionTemplate() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionOverload)->get_DescriptionTemplate(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionOverload<D>::GetInputs() const
    {
        uint32_t result_impl_size{};
        void** result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionOverload)->GetInputs(&result_impl_size, &result));
        return com_array<winrt::Windows::AI::Actions::Hosting::ActionEntityRegistrationInfo>{ result, result_impl_size, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionOverload<D>::InvokeAsync(winrt::Windows::AI::Actions::ActionInvocationContext const& context) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionOverload)->InvokeAsync(*(void**)(&context), &operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionOverload2<D>::InvokeFeedbackAsync(winrt::Windows::AI::Actions::ActionInvocationContext const& context, winrt::Windows::AI::Actions::ActionFeedback const& feedback) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionOverload2)->InvokeFeedbackAsync(*(void**)(&context), *(void**)(&feedback), &operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Hosting_IActionOverload2<D>::GetSupportsFeedback() const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Hosting::IActionOverload2)->GetSupportsFeedback(&result));
        return result;
    }
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::Hosting::IActionCatalog> : produce_base<D, winrt::Windows::AI::Actions::Hosting::IActionCatalog>
    {
        int32_t __stdcall GetAllActions(uint32_t* __resultSize, void*** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            std::tie(*__resultSize, *result) = detach_abi(this->shim().GetAllActions());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_Changed(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().Changed(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::AI::Actions::Hosting::ActionCatalog, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_Changed(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Changed(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::Hosting::IActionCatalog2> : produce_base<D, winrt::Windows::AI::Actions::Hosting::IActionCatalog2>
    {
        int32_t __stdcall GetActionsForInputs(uint32_t __inputEntitiesSize, void** inputEntities, uint32_t* __resultSize, void*** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            std::tie(*__resultSize, *result) = detach_abi(this->shim().GetActionsForInputs(array_view<winrt::Windows::AI::Actions::ActionEntity const>(reinterpret_cast<winrt::Windows::AI::Actions::ActionEntity const *>(inputEntities), reinterpret_cast<winrt::Windows::AI::Actions::ActionEntity const *>(inputEntities) + __inputEntitiesSize)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetActionsForInputs2(uint32_t __inputEntitiesSize, void** inputEntities, struct struct_Windows_UI_WindowId invokerWindowId, uint32_t* __resultSize, void*** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            std::tie(*__resultSize, *result) = detach_abi(this->shim().GetActionsForInputs(array_view<winrt::Windows::AI::Actions::ActionEntity const>(reinterpret_cast<winrt::Windows::AI::Actions::ActionEntity const *>(inputEntities), reinterpret_cast<winrt::Windows::AI::Actions::ActionEntity const *>(inputEntities) + __inputEntitiesSize), *reinterpret_cast<winrt::Windows::UI::WindowId const*>(&invokerWindowId)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::Hosting::IActionDefinition> : produce_base<D, winrt::Windows::AI::Actions::Hosting::IActionDefinition>
    {
        int32_t __stdcall get_Id(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Id());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Description(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Description());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IconFullPath(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().IconFullPath());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PackageFamilyName(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().PackageFamilyName());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetInputs(uint32_t* __resultSize, void*** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            std::tie(*__resultSize, *result) = detach_abi(this->shim().GetInputs());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetOutputs(uint32_t* __resultSize, void*** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            std::tie(*__resultSize, *result) = detach_abi(this->shim().GetOutputs());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetOverloads(uint32_t* __resultSize, void*** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            std::tie(*__resultSize, *result) = detach_abi(this->shim().GetOverloads());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::Hosting::IActionDefinition2> : produce_base<D, winrt::Windows::AI::Actions::Hosting::IActionDefinition2>
    {
        int32_t __stdcall get_DisplaysUI(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().DisplaysUI());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_UsesGenerativeAI(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().UsesGenerativeAI());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SchemaVersion(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().SchemaVersion());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::Hosting::IActionDefinition3> : produce_base<D, winrt::Windows::AI::Actions::Hosting::IActionDefinition3>
    {
        int32_t __stdcall get_PackageRelativeApplicationId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().PackageRelativeApplicationId());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::Hosting::IActionEntityRegistrationInfo> : produce_base<D, winrt::Windows::AI::Actions::Hosting::IActionEntityRegistrationInfo>
    {
        int32_t __stdcall get_Name(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Name());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Name(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Name(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Kind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::ActionEntityKind>(this->shim().Kind());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Kind(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Kind(*reinterpret_cast<winrt::Windows::AI::Actions::ActionEntityKind const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::Hosting::IActionInstance> : produce_base<D, winrt::Windows::AI::Actions::Hosting::IActionInstance>
    {
        int32_t __stdcall get_DisplayInfo(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::Hosting::ActionInstanceDisplayInfo>(this->shim().DisplayInfo());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Definition(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::Hosting::ActionDefinition>(this->shim().Definition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Context(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::ActionInvocationContext>(this->shim().Context());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall InvokeAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().InvokeAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::Hosting::IActionInstanceDisplayInfo> : produce_base<D, winrt::Windows::AI::Actions::Hosting::IActionInstanceDisplayInfo>
    {
        int32_t __stdcall get_Description(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Description());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::Hosting::IActionOverload> : produce_base<D, winrt::Windows::AI::Actions::Hosting::IActionOverload>
    {
        int32_t __stdcall get_DescriptionTemplate(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().DescriptionTemplate());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetInputs(uint32_t* __resultSize, void*** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            std::tie(*__resultSize, *result) = detach_abi(this->shim().GetInputs());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall InvokeAsync(void* context, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().InvokeAsync(*reinterpret_cast<winrt::Windows::AI::Actions::ActionInvocationContext const*>(&context)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::Hosting::IActionOverload2> : produce_base<D, winrt::Windows::AI::Actions::Hosting::IActionOverload2>
    {
        int32_t __stdcall InvokeFeedbackAsync(void* context, void* feedback, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().InvokeFeedbackAsync(*reinterpret_cast<winrt::Windows::AI::Actions::ActionInvocationContext const*>(&context), *reinterpret_cast<winrt::Windows::AI::Actions::ActionFeedback const*>(&feedback)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetSupportsFeedback(bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().GetSupportsFeedback());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
}
WINRT_EXPORT namespace winrt::Windows::AI::Actions::Hosting
{
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
    template<> struct hash<winrt::Windows::AI::Actions::Hosting::IActionCatalog> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::Hosting::IActionCatalog2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::Hosting::IActionDefinition> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::Hosting::IActionDefinition2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::Hosting::IActionDefinition3> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::Hosting::IActionEntityRegistrationInfo> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::Hosting::IActionInstance> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::Hosting::IActionInstanceDisplayInfo> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::Hosting::IActionOverload> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::Hosting::IActionOverload2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::Hosting::ActionCatalog> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::Hosting::ActionDefinition> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::Hosting::ActionEntityRegistrationInfo> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::Hosting::ActionInstance> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::Hosting::ActionInstanceDisplayInfo> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::Hosting::ActionOverload> : winrt::impl::hash_base {};
#endif
#ifdef __cpp_lib_format
#endif
}
#endif
