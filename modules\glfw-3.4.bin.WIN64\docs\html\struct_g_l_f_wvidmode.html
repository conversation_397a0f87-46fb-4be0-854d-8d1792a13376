<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: GLFWvidmode Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">GLFWvidmode Struct Reference<div class="ingroups"><a class="el" href="group__monitor.html">Monitor reference</a></div></div></div>
</div><!--header-->
<div class="contents">

<p>Video mode type.  
 <a href="struct_g_l_f_wvidmode.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a698dcb200562051a7249cb6ae154c71d" id="r_a698dcb200562051a7249cb6ae154c71d"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wvidmode.html#a698dcb200562051a7249cb6ae154c71d">width</a></td></tr>
<tr class="separator:a698dcb200562051a7249cb6ae154c71d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac65942a5f6981695517437a9d571d03c" id="r_ac65942a5f6981695517437a9d571d03c"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wvidmode.html#ac65942a5f6981695517437a9d571d03c">height</a></td></tr>
<tr class="separator:ac65942a5f6981695517437a9d571d03c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6066c4ecd251098700062d3b735dba1b" id="r_a6066c4ecd251098700062d3b735dba1b"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wvidmode.html#a6066c4ecd251098700062d3b735dba1b">redBits</a></td></tr>
<tr class="separator:a6066c4ecd251098700062d3b735dba1b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a292fdd281f3485fb3ff102a5bda43faa" id="r_a292fdd281f3485fb3ff102a5bda43faa"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wvidmode.html#a292fdd281f3485fb3ff102a5bda43faa">greenBits</a></td></tr>
<tr class="separator:a292fdd281f3485fb3ff102a5bda43faa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af310977f58d2e3b188175b6e3d314047" id="r_af310977f58d2e3b188175b6e3d314047"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wvidmode.html#af310977f58d2e3b188175b6e3d314047">blueBits</a></td></tr>
<tr class="separator:af310977f58d2e3b188175b6e3d314047"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a791bdd6c7697b09f7e9c97054bf05649" id="r_a791bdd6c7697b09f7e9c97054bf05649"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wvidmode.html#a791bdd6c7697b09f7e9c97054bf05649">refreshRate</a></td></tr>
<tr class="separator:a791bdd6c7697b09f7e9c97054bf05649"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>This describes a single video mode.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_modes">Video modes</a> </dd>
<dd>
<a class="el" href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a> </dd>
<dd>
<a class="el" href="group__monitor.html#gad2e24d2843cb7d6c26202cddd530fc1b">glfwGetVideoModes</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. <b>GLFW 3:</b> Added refresh rate member. </dd></dl>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="a698dcb200562051a7249cb6ae154c71d" name="a698dcb200562051a7249cb6ae154c71d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a698dcb200562051a7249cb6ae154c71d">&#9670;&#160;</a></span>width</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int GLFWvidmode::width</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The width, in screen coordinates, of the video mode. </p>

</div>
</div>
<a id="ac65942a5f6981695517437a9d571d03c" name="ac65942a5f6981695517437a9d571d03c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac65942a5f6981695517437a9d571d03c">&#9670;&#160;</a></span>height</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int GLFWvidmode::height</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The height, in screen coordinates, of the video mode. </p>

</div>
</div>
<a id="a6066c4ecd251098700062d3b735dba1b" name="a6066c4ecd251098700062d3b735dba1b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6066c4ecd251098700062d3b735dba1b">&#9670;&#160;</a></span>redBits</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int GLFWvidmode::redBits</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The bit depth of the red channel of the video mode. </p>

</div>
</div>
<a id="a292fdd281f3485fb3ff102a5bda43faa" name="a292fdd281f3485fb3ff102a5bda43faa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a292fdd281f3485fb3ff102a5bda43faa">&#9670;&#160;</a></span>greenBits</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int GLFWvidmode::greenBits</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The bit depth of the green channel of the video mode. </p>

</div>
</div>
<a id="af310977f58d2e3b188175b6e3d314047" name="af310977f58d2e3b188175b6e3d314047"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af310977f58d2e3b188175b6e3d314047">&#9670;&#160;</a></span>blueBits</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int GLFWvidmode::blueBits</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The bit depth of the blue channel of the video mode. </p>

</div>
</div>
<a id="a791bdd6c7697b09f7e9c97054bf05649" name="a791bdd6c7697b09f7e9c97054bf05649"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a791bdd6c7697b09f7e9c97054bf05649">&#9670;&#160;</a></span>refreshRate</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int GLFWvidmode::refreshRate</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The refresh rate, in Hz, of the video mode. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="glfw3_8h_source.html">glfw3.h</a></li>
</ul>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
