﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\test\test_arithm.cpp">
      <Filter>opencv_cudaarithm\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\test\test_buffer_pool.cpp">
      <Filter>opencv_cudaarithm\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\test\test_core.cpp">
      <Filter>opencv_cudaarithm\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\test\test_element_operations.cpp">
      <Filter>opencv_cudaarithm\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\test\test_event.cpp">
      <Filter>opencv_cudaarithm\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\test\test_gpumat.cpp">
      <Filter>opencv_cudaarithm\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\test\test_main.cpp">
      <Filter>opencv_cudaarithm\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\test\test_opengl.cpp">
      <Filter>opencv_cudaarithm\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\test\test_reductions.cpp">
      <Filter>opencv_cudaarithm\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\test\test_stream.cpp">
      <Filter>opencv_cudaarithm\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\test\test_precomp.hpp">
      <Filter>opencv_cudaarithm\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_cudaarithm">
      <UniqueIdentifier>{01EFAAA2-1FC1-3C33-B474-58E2FA761CE6}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_cudaarithm\Include">
      <UniqueIdentifier>{091D0E9D-7C8C-347A-A8CA-42163D81F861}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_cudaarithm\Src">
      <UniqueIdentifier>{CDA47ABE-DE6A-3E5A-9179-EBD2342DB8F8}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
