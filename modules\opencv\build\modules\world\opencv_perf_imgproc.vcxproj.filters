﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\opencl\perf_3vs4.cpp">
      <Filter>opencv_imgproc\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\opencl\perf_accumulate.cpp">
      <Filter>opencv_imgproc\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\opencl\perf_blend.cpp">
      <Filter>opencv_imgproc\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\opencl\perf_color.cpp">
      <Filter>opencv_imgproc\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\opencl\perf_filters.cpp">
      <Filter>opencv_imgproc\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\opencl\perf_gftt.cpp">
      <Filter>opencv_imgproc\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\opencl\perf_houghlines.cpp">
      <Filter>opencv_imgproc\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\opencl\perf_imgproc.cpp">
      <Filter>opencv_imgproc\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\opencl\perf_imgwarp.cpp">
      <Filter>opencv_imgproc\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\opencl\perf_matchTemplate.cpp">
      <Filter>opencv_imgproc\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\opencl\perf_moments.cpp">
      <Filter>opencv_imgproc\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\opencl\perf_pyramid.cpp">
      <Filter>opencv_imgproc\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_accumulate.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_bilateral.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_blur.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_canny.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_contours.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_corners.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_cvt_color.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_distanceTransform.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_filter2d.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_floodfill.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_goodFeaturesToTrack.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_histogram.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_houghcircles.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_houghlines.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_integral.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_intelligent_scissors.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_main.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_matchTemplate.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_moments.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_morph.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_phasecorr.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_pyramids.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_remap.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_resize.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_sepfilters.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_spatialgradient.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_threshold.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_warp.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\perf\perf_precomp.hpp">
      <Filter>opencv_imgproc\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_imgproc">
      <UniqueIdentifier>{FA23004A-22DB-30E6-B8FD-718F497FB629}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_imgproc\Include">
      <UniqueIdentifier>{AABF94F6-4F3F-3B4E-B7E7-DFBBB12077CC}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_imgproc\Src">
      <UniqueIdentifier>{7A7ACE0E-D197-310E-909D-212CE8311A24}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_imgproc\Src\opencl">
      <UniqueIdentifier>{3F90E34C-5138-3AED-944F-38566CD770A9}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
