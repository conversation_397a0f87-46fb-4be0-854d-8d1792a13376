clBuildProgram
clCompileProgram
clCreateBuffer
clCreateCommandQueue
clCreateContext
clCreateContextFromType
clCreateImage
clCreateImage2D
clCreateImage3D
clCreateKernel
clCreateKernelsInProgram
clCreateProgramWithBinary
clCreateProgramWithBuiltInKernels
clCreateProgramWithSource
clCreateSampler
clCreateSubBuffer
clCreateSubDevices
clCreateUserEvent
clEnqueueBarrier
clEnqueueBarrierWithWaitList
clEnqueueCopyBuffer
clEnqueueCopyBufferRect
clEnqueueCopyBufferToImage
clEnqueueCopyImage
clEnqueueCopyImageToBuffer
clEnqueueFillBuffer
clEnqueueFillImage
clEnqueueMapBuffer
clEnqueueMapImage
clEnqueueMarker
clEnqueueMarkerWithWaitList
clEnqueueMigrateMemObjects
clEnqueueNDRangeKernel
clEnqueueNativeKernel
clEnqueueReadBuffer
clEnqueueReadBufferRect
clEnqueueReadImage
clEnqueueTask
clEnqueueUnmapMemObject
clEnqueueWaitForEvents
clEnqueueWriteBuffer
clEnqueueWriteBufferRect
clEnqueueWriteImage
clFinish
clFlush
clGetCommandQueueInfo
clGetContextInfo
clGetDeviceIDs
clGetDeviceInfo
clGetEventInfo
clGetEventProfilingInfo
clGetExtensionFunctionAddress
clGetExtensionFunctionAddressForPlatform
clGetImageInfo
clGetKernelArgInfo
clGetKernelInfo
clGetKernelWorkGroupInfo
clGetMemObjectInfo
clGetPlatformIDs
clGetPlatformInfo
clGetProgramBuildInfo
clGetProgramInfo
clGetSamplerInfo
clGetSupportedImageFormats
clLinkProgram
clReleaseCommandQueue
clReleaseContext
clReleaseDevice
clReleaseEvent
clReleaseKernel
clReleaseMemObject
clReleaseProgram
clReleaseSampler
clRetainCommandQueue
clRetainContext
clRetainDevice
clRetainEvent
clRetainKernel
clRetainMemObject
clRetainProgram
clRetainSampler
clSetEventCallback
clSetKernelArg
clSetMemObjectDestructorCallback
clSetUserEventStatus
clUnloadCompiler
clUnloadPlatformCompiler
clWaitForEvents
#total 88
