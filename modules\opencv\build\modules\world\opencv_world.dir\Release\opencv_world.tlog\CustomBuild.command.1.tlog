^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\245D2A09B4A1476B84D6F688E65BE50C\OPENCL_KERNELS_CORE.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=core -DCL_DIR=D:/AI/opencv/opencv-4.10.0/modules/core/src/opencl -DOUTPUT=D:/AI/opencv/cudabuild/modules/world/opencl_kernels_core.cpp -P D:/AI/opencv/opencv-4.10.0/cmake/cl2cpp.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\CORE\SRC\CUDA\GPU_MAT.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\core\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/core/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/core/src/cuda/Release/cuda_compile_1_generated_gpu_mat.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/core/src/cuda/Release/cuda_compile_1_generated_gpu_mat.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/core/src/cuda/cuda_compile_1_generated_gpu_mat.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\CORE\SRC\CUDA\GPU_MAT_ND.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\core\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/core/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/core/src/cuda/Release/cuda_compile_1_generated_gpu_mat_nd.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/core/src/cuda/Release/cuda_compile_1_generated_gpu_mat_nd.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/core/src/cuda/cuda_compile_1_generated_gpu_mat_nd.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\ABSDIFF_MAT.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_absdiff_mat.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_absdiff_mat.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_absdiff_mat.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\ABSDIFF_SCALAR.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_absdiff_scalar.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_absdiff_scalar.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_absdiff_scalar.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\ADD_MAT.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_add_mat.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_add_mat.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_add_mat.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\ADD_SCALAR.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_add_scalar.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_add_scalar.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_add_scalar.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\ADD_WEIGHTED.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_add_weighted.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_add_weighted.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_add_weighted.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\BITWISE_MAT.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_bitwise_mat.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_bitwise_mat.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_bitwise_mat.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\BITWISE_SCALAR.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_bitwise_scalar.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_bitwise_scalar.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_bitwise_scalar.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\CMP_MAT.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_cmp_mat.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_cmp_mat.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_cmp_mat.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\CMP_SCALAR.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_cmp_scalar.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_cmp_scalar.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_cmp_scalar.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\COPY_MAKE_BORDER.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_copy_make_border.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_copy_make_border.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_copy_make_border.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\COUNTNONZERO.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_countnonzero.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_countnonzero.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_countnonzero.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\DIV_MAT.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_div_mat.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_div_mat.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_div_mat.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\DIV_SCALAR.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_div_scalar.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_div_scalar.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_div_scalar.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\IN_RANGE.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_in_range.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_in_range.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_in_range.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\INTEGRAL.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_integral.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_integral.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_integral.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\LUT.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_lut.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_lut.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_lut.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\MATH.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_math.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_math.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_math.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\MINMAX.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_minmax.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_minmax.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_minmax.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\MINMAX_MAT.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_minmax_mat.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_minmax_mat.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_minmax_mat.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\MINMAXLOC.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_minmaxloc.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_minmaxloc.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_minmaxloc.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\MUL_MAT.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_mul_mat.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_mul_mat.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_mul_mat.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\MUL_SCALAR.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_mul_scalar.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_mul_scalar.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_mul_scalar.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\MUL_SPECTRUMS.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_mul_spectrums.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_mul_spectrums.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_mul_spectrums.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\NORM.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_norm.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_norm.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_norm.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\NORMALIZE.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_normalize.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_normalize.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_normalize.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\POLAR_CART.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_polar_cart.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_polar_cart.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_polar_cart.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\REDUCE.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_reduce.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_reduce.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_reduce.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\SPLIT_MERGE.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_split_merge.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_split_merge.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_split_merge.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\SUB_MAT.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_sub_mat.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_sub_mat.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_sub_mat.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\SUB_SCALAR.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_sub_scalar.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_sub_scalar.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_sub_scalar.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\SUM.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_sum.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_sum.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_sum.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\THRESHOLD.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_threshold.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_threshold.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_threshold.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAARITHM\SRC\CUDA\TRANSPOSE.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaarithm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_transpose.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/Release/cuda_compile_1_generated_transpose.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaarithm/src/cuda/cuda_compile_1_generated_transpose.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\245D2A09B4A1476B84D6F688E65BE50C\OPENCL_KERNELS_IMGPROC.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=imgproc -DCL_DIR=D:/AI/opencv/opencv-4.10.0/modules/imgproc/src/opencl -DOUTPUT=D:/AI/opencv/cudabuild/modules/world/opencl_kernels_imgproc.cpp -P D:/AI/opencv/opencv-4.10.0/cmake/cl2cpp.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\COLUMN_FILTER.16SC1.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.16sc1.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.16sc1.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_column_filter.16sc1.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\COLUMN_FILTER.16SC3.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.16sc3.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.16sc3.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_column_filter.16sc3.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\COLUMN_FILTER.16SC4.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.16sc4.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.16sc4.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_column_filter.16sc4.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\COLUMN_FILTER.16UC1.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.16uc1.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.16uc1.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_column_filter.16uc1.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\COLUMN_FILTER.16UC3.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.16uc3.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.16uc3.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_column_filter.16uc3.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\COLUMN_FILTER.16UC4.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.16uc4.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.16uc4.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_column_filter.16uc4.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\COLUMN_FILTER.32FC1.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.32fc1.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.32fc1.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_column_filter.32fc1.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\COLUMN_FILTER.32FC3.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.32fc3.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.32fc3.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_column_filter.32fc3.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\COLUMN_FILTER.32FC4.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.32fc4.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.32fc4.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_column_filter.32fc4.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\COLUMN_FILTER.32SC1.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.32sc1.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.32sc1.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_column_filter.32sc1.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\COLUMN_FILTER.32SC3.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.32sc3.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.32sc3.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_column_filter.32sc3.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\COLUMN_FILTER.32SC4.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.32sc4.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.32sc4.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_column_filter.32sc4.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\COLUMN_FILTER.8UC1.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.8uc1.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.8uc1.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_column_filter.8uc1.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\COLUMN_FILTER.8UC3.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.8uc3.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.8uc3.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_column_filter.8uc3.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\COLUMN_FILTER.8UC4.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.8uc4.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_column_filter.8uc4.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_column_filter.8uc4.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\FILTER2D.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_filter2d.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_filter2d.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_filter2d.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\MEDIAN_FILTER.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_median_filter.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_median_filter.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_median_filter.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\ROW_FILTER.16SC1.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.16sc1.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.16sc1.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_row_filter.16sc1.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\ROW_FILTER.16SC3.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.16sc3.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.16sc3.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_row_filter.16sc3.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\ROW_FILTER.16SC4.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.16sc4.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.16sc4.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_row_filter.16sc4.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\ROW_FILTER.16UC1.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.16uc1.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.16uc1.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_row_filter.16uc1.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\ROW_FILTER.16UC3.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.16uc3.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.16uc3.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_row_filter.16uc3.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\ROW_FILTER.16UC4.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.16uc4.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.16uc4.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_row_filter.16uc4.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\ROW_FILTER.32FC1.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.32fc1.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.32fc1.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_row_filter.32fc1.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\ROW_FILTER.32FC3.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.32fc3.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.32fc3.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_row_filter.32fc3.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\ROW_FILTER.32FC4.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.32fc4.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.32fc4.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_row_filter.32fc4.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\ROW_FILTER.32SC1.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.32sc1.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.32sc1.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_row_filter.32sc1.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\ROW_FILTER.32SC3.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.32sc3.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.32sc3.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_row_filter.32sc3.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\ROW_FILTER.32SC4.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.32sc4.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.32sc4.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_row_filter.32sc4.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\ROW_FILTER.8UC1.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.8uc1.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.8uc1.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_row_filter.8uc1.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\ROW_FILTER.8UC3.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.8uc3.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.8uc3.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_row_filter.8uc3.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFILTERS\SRC\CUDA\ROW_FILTER.8UC4.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafilters\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.8uc4.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/Release/cuda_compile_1_generated_row_filter.8uc4.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafilters/src/cuda/cuda_compile_1_generated_row_filter.8uc4.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\BILATERAL_FILTER.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_bilateral_filter.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_bilateral_filter.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_bilateral_filter.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\BLEND.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_blend.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_blend.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_blend.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\BUILD_POINT_LIST.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_build_point_list.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_build_point_list.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_build_point_list.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\CANNY.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_canny.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_canny.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_canny.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\CLAHE.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_clahe.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_clahe.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_clahe.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\COLOR.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_color.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_color.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_color.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\CONNECTEDCOMPONENTS.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_connectedcomponents.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_connectedcomponents.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_connectedcomponents.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\CORNERS.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_corners.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_corners.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_corners.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\DEBAYER.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_debayer.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_debayer.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_debayer.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\GENERALIZED_HOUGH.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_generalized_hough.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_generalized_hough.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_generalized_hough.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\GFTT.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_gftt.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_gftt.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_gftt.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\HIST.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_hist.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_hist.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_hist.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\HOUGH_CIRCLES.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_hough_circles.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_hough_circles.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_hough_circles.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\HOUGH_LINES.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_hough_lines.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_hough_lines.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_hough_lines.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\HOUGH_SEGMENTS.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_hough_segments.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_hough_segments.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_hough_segments.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\MATCH_TEMPLATE.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_match_template.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_match_template.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_match_template.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\MEAN_SHIFT.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_mean_shift.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_mean_shift.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_mean_shift.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAIMGPROC\SRC\CUDA\MOMENTS.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaimgproc\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_moments.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/Release/cuda_compile_1_generated_moments.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaimgproc/src/cuda/cuda_compile_1_generated_moments.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAWARPING\SRC\CUDA\PYR_DOWN.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudawarping\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/Release/cuda_compile_1_generated_pyr_down.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/Release/cuda_compile_1_generated_pyr_down.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/cuda_compile_1_generated_pyr_down.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAWARPING\SRC\CUDA\PYR_UP.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudawarping\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/Release/cuda_compile_1_generated_pyr_up.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/Release/cuda_compile_1_generated_pyr_up.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/cuda_compile_1_generated_pyr_up.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAWARPING\SRC\CUDA\REMAP.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudawarping\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/Release/cuda_compile_1_generated_remap.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/Release/cuda_compile_1_generated_remap.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/cuda_compile_1_generated_remap.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAWARPING\SRC\CUDA\RESIZE.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudawarping\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/Release/cuda_compile_1_generated_resize.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/Release/cuda_compile_1_generated_resize.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/cuda_compile_1_generated_resize.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAWARPING\SRC\CUDA\WARP.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudawarping\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/Release/cuda_compile_1_generated_warp.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/Release/cuda_compile_1_generated_warp.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudawarping/src/cuda/cuda_compile_1_generated_warp.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\245D2A09B4A1476B84D6F688E65BE50C\OPENCL_KERNELS_DNN.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=dnn -DCL_DIR=D:/AI/opencv/opencv-4.10.0/modules/dnn/src/opencl -DOUTPUT=D:/AI/opencv/cudabuild/modules/world/opencl_kernels_dnn.cpp -P D:/AI/opencv/opencv-4.10.0/cmake/cl2cpp.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\ACTIVATION_ELTWISE.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_activation_eltwise.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_activation_eltwise.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_activation_eltwise.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\ACTIVATIONS.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_activations.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_activations.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_activations.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\BIAS_ACTIVATION.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_bias_activation.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_bias_activation.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_bias_activation.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\BIAS_ACTIVATION_ELTWISE.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_bias_activation_eltwise.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_bias_activation_eltwise.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_bias_activation_eltwise.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\BIAS_ELTWISE_ACTIVATION.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_bias_eltwise_activation.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_bias_eltwise_activation.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_bias_eltwise_activation.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\CONCAT.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_concat.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_concat.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_concat.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\CROP_AND_RESIZE.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_crop_and_resize.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_crop_and_resize.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_crop_and_resize.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\DETECTION_OUTPUT.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_detection_output.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_detection_output.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_detection_output.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\ELTWISE_ACTIVATION.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_eltwise_activation.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_eltwise_activation.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_eltwise_activation.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\ELTWISE_OPS.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_eltwise_ops.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_eltwise_ops.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_eltwise_ops.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\FILL_COPY.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_fill_copy.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_fill_copy.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_fill_copy.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\FP_CONVERSION.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_fp_conversion.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_fp_conversion.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_fp_conversion.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\GRID_NMS.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_grid_nms.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_grid_nms.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_grid_nms.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\MAX_UNPOOLING.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_max_unpooling.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_max_unpooling.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_max_unpooling.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\MVN.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_mvn.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_mvn.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_mvn.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\NORMALIZE.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_normalize.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_normalize.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_normalize.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\PADDING.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_padding.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_padding.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_padding.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\PERMUTE.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_permute.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_permute.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_permute.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\PRIOR_BOX.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_prior_box.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_prior_box.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_prior_box.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\REGION.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_region.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_region.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_region.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\RESIZE.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_resize.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_resize.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_resize.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\ROI_POOLING.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_roi_pooling.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_roi_pooling.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_roi_pooling.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\SCALE_SHIFT.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_scale_shift.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_scale_shift.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_scale_shift.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\SHORTCUT.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_shortcut.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_shortcut.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_shortcut.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\DNN\SRC\CUDA\SLICE.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\dnn\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_slice.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/Release/cuda_compile_1_generated_slice.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/dnn/src/cuda/cuda_compile_1_generated_slice.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\245D2A09B4A1476B84D6F688E65BE50C\OPENCL_KERNELS_FEATURES2D.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=features2d -DCL_DIR=D:/AI/opencv/opencv-4.10.0/modules/features2d/src/opencl -DOUTPUT=D:/AI/opencv/cudabuild/modules/world/opencl_kernels_features2d.cpp -P D:/AI/opencv/opencv-4.10.0/cmake/cl2cpp.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\HFS\SRC\CUDA\GSLIC_SEG_ENGINE_GPU.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\hfs\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/hfs/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/hfs/src/cuda/Release/cuda_compile_1_generated_gslic_seg_engine_gpu.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/hfs/src/cuda/Release/cuda_compile_1_generated_gslic_seg_engine_gpu.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/hfs/src/cuda/cuda_compile_1_generated_gslic_seg_engine_gpu.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\HFS\SRC\CUDA\MAGNITUDE.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\hfs\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/hfs/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/hfs/src/cuda/Release/cuda_compile_1_generated_magnitude.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/hfs/src/cuda/Release/cuda_compile_1_generated_magnitude.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/hfs/src/cuda/cuda_compile_1_generated_magnitude.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\245D2A09B4A1476B84D6F688E65BE50C\OPENCL_KERNELS_PHOTO.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=photo -DCL_DIR=D:/AI/opencv/opencv-4.10.0/modules/photo/src/opencl -DOUTPUT=D:/AI/opencv/cudabuild/modules/world/opencl_kernels_photo.cpp -P D:/AI/opencv/opencv-4.10.0/cmake/cl2cpp.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\PHOTO\SRC\CUDA\NLM.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\photo\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/photo/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/photo/src/cuda/Release/cuda_compile_1_generated_nlm.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/photo/src/cuda/Release/cuda_compile_1_generated_nlm.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/photo/src/cuda/cuda_compile_1_generated_nlm.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\245D2A09B4A1476B84D6F688E65BE50C\OPENCL_KERNELS_CALIB3D.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=calib3d -DCL_DIR=D:/AI/opencv/opencv-4.10.0/modules/calib3d/src/opencl -DOUTPUT=D:/AI/opencv/cudabuild/modules/world/opencl_kernels_calib3d.cpp -P D:/AI/opencv/opencv-4.10.0/cmake/cl2cpp.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDACODEC\SRC\CUDA\NV12_TO_RGB.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudacodec\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudacodec/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudacodec/src/cuda/Release/cuda_compile_1_generated_nv12_to_rgb.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudacodec/src/cuda/Release/cuda_compile_1_generated_nv12_to_rgb.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudacodec/src/cuda/cuda_compile_1_generated_nv12_to_rgb.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDACODEC\SRC\CUDA\RGB_TO_YV12.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudacodec\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudacodec/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudacodec/src/cuda/Release/cuda_compile_1_generated_rgb_to_yv12.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudacodec/src/cuda/Release/cuda_compile_1_generated_rgb_to_yv12.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudacodec/src/cuda/cuda_compile_1_generated_rgb_to_yv12.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFEATURES2D\SRC\CUDA\BF_KNNMATCH.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafeatures2d\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/Release/cuda_compile_1_generated_bf_knnmatch.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/Release/cuda_compile_1_generated_bf_knnmatch.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/cuda_compile_1_generated_bf_knnmatch.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFEATURES2D\SRC\CUDA\BF_MATCH.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafeatures2d\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/Release/cuda_compile_1_generated_bf_match.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/Release/cuda_compile_1_generated_bf_match.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/cuda_compile_1_generated_bf_match.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFEATURES2D\SRC\CUDA\BF_RADIUS_MATCH.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafeatures2d\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/Release/cuda_compile_1_generated_bf_radius_match.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/Release/cuda_compile_1_generated_bf_radius_match.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/cuda_compile_1_generated_bf_radius_match.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFEATURES2D\SRC\CUDA\FAST.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafeatures2d\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/Release/cuda_compile_1_generated_fast.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/Release/cuda_compile_1_generated_fast.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/cuda_compile_1_generated_fast.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAFEATURES2D\SRC\CUDA\ORB.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudafeatures2d\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/Release/cuda_compile_1_generated_orb.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/Release/cuda_compile_1_generated_orb.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudafeatures2d/src/cuda/cuda_compile_1_generated_orb.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDASTEREO\SRC\CUDA\DISPARITY_BILATERAL_FILTER.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudastereo\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release/cuda_compile_1_generated_disparity_bilateral_filter.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release/cuda_compile_1_generated_disparity_bilateral_filter.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/cuda_compile_1_generated_disparity_bilateral_filter.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDASTEREO\SRC\CUDA\STEREOBM.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudastereo\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release/cuda_compile_1_generated_stereobm.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release/cuda_compile_1_generated_stereobm.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/cuda_compile_1_generated_stereobm.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDASTEREO\SRC\CUDA\STEREOBP.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudastereo\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release/cuda_compile_1_generated_stereobp.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release/cuda_compile_1_generated_stereobp.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/cuda_compile_1_generated_stereobp.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDASTEREO\SRC\CUDA\STEREOCSBP.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudastereo\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release/cuda_compile_1_generated_stereocsbp.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release/cuda_compile_1_generated_stereocsbp.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/cuda_compile_1_generated_stereocsbp.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDASTEREO\SRC\CUDA\STEREOSGM.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudastereo\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release/cuda_compile_1_generated_stereosgm.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release/cuda_compile_1_generated_stereosgm.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/cuda_compile_1_generated_stereosgm.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDASTEREO\SRC\CUDA\UTIL.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudastereo\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release/cuda_compile_1_generated_util.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/Release/cuda_compile_1_generated_util.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudastereo/src/cuda/cuda_compile_1_generated_util.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\245D2A09B4A1476B84D6F688E65BE50C\OPENCL_KERNELS_OBJDETECT.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=objdetect -DCL_DIR=D:/AI/opencv/opencv-4.10.0/modules/objdetect/src/opencl -DOUTPUT=D:/AI/opencv/cudabuild/modules/world/opencl_kernels_objdetect.cpp -P D:/AI/opencv/opencv-4.10.0/cmake/cl2cpp.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\245D2A09B4A1476B84D6F688E65BE50C\OPENCL_KERNELS_RGBD.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=rgbd -DCL_DIR=D:/AI/opencv/opencv_contrib-4.10.0/modules/rgbd/src/opencl -DOUTPUT=D:/AI/opencv/cudabuild/modules/world/opencl_kernels_rgbd.cpp -P D:/AI/opencv/opencv-4.10.0/cmake/cl2cpp.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\245D2A09B4A1476B84D6F688E65BE50C\OPENCL_KERNELS_VIDEO.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=video -DCL_DIR=D:/AI/opencv/opencv-4.10.0/modules/video/src/opencl -DOUTPUT=D:/AI/opencv/cudabuild/modules/world/opencl_kernels_video.cpp -P D:/AI/opencv/opencv-4.10.0/cmake/cl2cpp.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\245D2A09B4A1476B84D6F688E65BE50C\OPENCL_KERNELS_XFEATURES2D.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=xfeatures2d -DCL_DIR=D:/AI/opencv/opencv_contrib-4.10.0/modules/xfeatures2d/src/opencl -DOUTPUT=D:/AI/opencv/cudabuild/modules/world/opencl_kernels_xfeatures2d.cpp -P D:/AI/opencv/opencv-4.10.0/cmake/cl2cpp.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\XFEATURES2D\SRC\CUDA\SURF.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\xfeatures2d\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/xfeatures2d/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/xfeatures2d/src/cuda/Release/cuda_compile_1_generated_surf.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/xfeatures2d/src/cuda/Release/cuda_compile_1_generated_surf.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/xfeatures2d/src/cuda/cuda_compile_1_generated_surf.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\245D2A09B4A1476B84D6F688E65BE50C\OPENCL_KERNELS_XIMGPROC.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=ximgproc -DCL_DIR=D:/AI/opencv/opencv_contrib-4.10.0/modules/ximgproc/src/opencl -DOUTPUT=D:/AI/opencv/cudabuild/modules/world/opencl_kernels_ximgproc.cpp -P D:/AI/opencv/opencv-4.10.0/cmake/cl2cpp.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\245D2A09B4A1476B84D6F688E65BE50C\OPENCL_KERNELS_BIOINSPIRED.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=bioinspired -DCL_DIR=D:/AI/opencv/opencv_contrib-4.10.0/modules/bioinspired/src/opencl -DOUTPUT=D:/AI/opencv/cudabuild/modules/world/opencl_kernels_bioinspired.cpp -P D:/AI/opencv/opencv-4.10.0/cmake/cl2cpp.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDABGSEGM\SRC\CUDA\MOG.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudabgsegm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudabgsegm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudabgsegm/src/cuda/Release/cuda_compile_1_generated_mog.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudabgsegm/src/cuda/Release/cuda_compile_1_generated_mog.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudabgsegm/src/cuda/cuda_compile_1_generated_mog.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDABGSEGM\SRC\CUDA\MOG2.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudabgsegm\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudabgsegm/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudabgsegm/src/cuda/Release/cuda_compile_1_generated_mog2.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudabgsegm/src/cuda/Release/cuda_compile_1_generated_mog2.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudabgsegm/src/cuda/cuda_compile_1_generated_mog2.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDALEGACY\SRC\CUDA\NCV.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudalegacy\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_NCV.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_NCV.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/cuda_compile_1_generated_NCV.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDALEGACY\SRC\CUDA\NCVBROXOPTICALFLOW.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudalegacy\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_NCVBroxOpticalFlow.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_NCVBroxOpticalFlow.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/cuda_compile_1_generated_NCVBroxOpticalFlow.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDALEGACY\SRC\CUDA\NCVHAAROBJECTDETECTION.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudalegacy\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_NCVHaarObjectDetection.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_NCVHaarObjectDetection.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/cuda_compile_1_generated_NCVHaarObjectDetection.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDALEGACY\SRC\CUDA\NCVPYRAMID.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudalegacy\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_NCVPyramid.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_NCVPyramid.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/cuda_compile_1_generated_NCVPyramid.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDALEGACY\SRC\CUDA\NPP_STAGING.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudalegacy\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_NPP_staging.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_NPP_staging.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/cuda_compile_1_generated_NPP_staging.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDALEGACY\SRC\CUDA\BM.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudalegacy\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_bm.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_bm.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/cuda_compile_1_generated_bm.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDALEGACY\SRC\CUDA\BM_FAST.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudalegacy\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_bm_fast.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_bm_fast.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/cuda_compile_1_generated_bm_fast.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDALEGACY\SRC\CUDA\CALIB3D.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudalegacy\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_calib3d.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_calib3d.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/cuda_compile_1_generated_calib3d.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDALEGACY\SRC\CUDA\CCOMPONETNS.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudalegacy\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_ccomponetns.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_ccomponetns.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/cuda_compile_1_generated_ccomponetns.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDALEGACY\SRC\CUDA\FGD.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudalegacy\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_fgd.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_fgd.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/cuda_compile_1_generated_fgd.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDALEGACY\SRC\CUDA\GMG.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudalegacy\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_gmg.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_gmg.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/cuda_compile_1_generated_gmg.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDALEGACY\SRC\CUDA\NEEDLE_MAP.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudalegacy\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_needle_map.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/Release/cuda_compile_1_generated_needle_map.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudalegacy/src/cuda/cuda_compile_1_generated_needle_map.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAOBJDETECT\SRC\CUDA\HOG.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaobjdetect\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaobjdetect/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaobjdetect/src/cuda/Release/cuda_compile_1_generated_hog.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaobjdetect/src/cuda/Release/cuda_compile_1_generated_hog.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaobjdetect/src/cuda/cuda_compile_1_generated_hog.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAOBJDETECT\SRC\CUDA\LBP.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaobjdetect\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaobjdetect/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaobjdetect/src/cuda/Release/cuda_compile_1_generated_lbp.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaobjdetect/src/cuda/Release/cuda_compile_1_generated_lbp.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaobjdetect/src/cuda/cuda_compile_1_generated_lbp.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\245D2A09B4A1476B84D6F688E65BE50C\OPENCL_KERNELS_OPTFLOW.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=optflow -DCL_DIR=D:/AI/opencv/opencv_contrib-4.10.0/modules/optflow/src/opencl -DOUTPUT=D:/AI/opencv/cudabuild/modules/world/opencl_kernels_optflow.cpp -P D:/AI/opencv/opencv-4.10.0/cmake/cl2cpp.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\245D2A09B4A1476B84D6F688E65BE50C\OPENCL_KERNELS_STITCHING.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=stitching -DCL_DIR=D:/AI/opencv/opencv-4.10.0/modules/stitching/src/opencl -DOUTPUT=D:/AI/opencv/cudabuild/modules/world/opencl_kernels_stitching.cpp -P D:/AI/opencv/opencv-4.10.0/cmake/cl2cpp.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\STITCHING\SRC\CUDA\BUILD_WARP_MAPS.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\stitching\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/stitching/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/stitching/src/cuda/Release/cuda_compile_1_generated_build_warp_maps.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/stitching/src/cuda/Release/cuda_compile_1_generated_build_warp_maps.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/stitching/src/cuda/cuda_compile_1_generated_build_warp_maps.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV-4.10.0\MODULES\STITCHING\SRC\CUDA\MULTIBAND_BLEND.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\stitching\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/stitching/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/stitching/src/cuda/Release/cuda_compile_1_generated_multiband_blend.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/stitching/src/cuda/Release/cuda_compile_1_generated_multiband_blend.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/stitching/src/cuda/cuda_compile_1_generated_multiband_blend.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\245D2A09B4A1476B84D6F688E65BE50C\OPENCL_KERNELS_TRACKING.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=tracking -DCL_DIR=D:/AI/opencv/opencv_contrib-4.10.0/modules/tracking/src/opencl -DOUTPUT=D:/AI/opencv/cudabuild/modules/world/opencl_kernels_tracking.cpp -P D:/AI/opencv/opencv-4.10.0/cmake/cl2cpp.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAOPTFLOW\SRC\CUDA\FARNEBACK.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaoptflow\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaoptflow/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaoptflow/src/cuda/Release/cuda_compile_1_generated_farneback.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaoptflow/src/cuda/Release/cuda_compile_1_generated_farneback.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaoptflow/src/cuda/cuda_compile_1_generated_farneback.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAOPTFLOW\SRC\CUDA\NVIDIAOPTICALFLOW.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaoptflow\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaoptflow/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaoptflow/src/cuda/Release/cuda_compile_1_generated_nvidiaOpticalFlow.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaoptflow/src/cuda/Release/cuda_compile_1_generated_nvidiaOpticalFlow.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaoptflow/src/cuda/cuda_compile_1_generated_nvidiaOpticalFlow.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAOPTFLOW\SRC\CUDA\PYRLK.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaoptflow\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaoptflow/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaoptflow/src/cuda/Release/cuda_compile_1_generated_pyrlk.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaoptflow/src/cuda/Release/cuda_compile_1_generated_pyrlk.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaoptflow/src/cuda/cuda_compile_1_generated_pyrlk.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\CUDAOPTFLOW\SRC\CUDA\TVL1FLOW.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\cudaoptflow\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaoptflow/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaoptflow/src/cuda/Release/cuda_compile_1_generated_tvl1flow.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaoptflow/src/cuda/Release/cuda_compile_1_generated_tvl1flow.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/cudaoptflow/src/cuda/cuda_compile_1_generated_tvl1flow.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\245D2A09B4A1476B84D6F688E65BE50C\OPENCL_KERNELS_SUPERRES.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=superres -DCL_DIR=D:/AI/opencv/opencv_contrib-4.10.0/modules/superres/src/opencl -DOUTPUT=D:/AI/opencv/cudabuild/modules/world/opencl_kernels_superres.cpp -P D:/AI/opencv/opencv-4.10.0/cmake/cl2cpp.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\SUPERRES\SRC\CUDA\BTV_L1_GPU.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\superres\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/superres/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/superres/src/cuda/Release/cuda_compile_1_generated_btv_l1_gpu.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/superres/src/cuda/Release/cuda_compile_1_generated_btv_l1_gpu.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/superres/src/cuda/cuda_compile_1_generated_btv_l1_gpu.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\OPENCV_CONTRIB-4.10.0\MODULES\VIDEOSTAB\SRC\CUDA\GLOBAL_MOTION.CU
setlocal
cd D:\AI\opencv\cudabuild\modules\world\CMakeFiles\cuda_compile_1.dir\__\__\__\opencv_contrib-4.10.0\modules\videostab\src\cuda
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/videostab/src/cuda/Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -D verbose:BOOL=OFF -D "CCBIN:PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools/MSVC/14.44.35207/bin/Hostx64/x64" -D build_configuration:STRING=Release -D generated_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/videostab/src/cuda/Release/cuda_compile_1_generated_global_motion.cu.obj -D generated_cubin_file:STRING=D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/videostab/src/cuda/Release/cuda_compile_1_generated_global_motion.cu.obj.cubin.txt -P D:/AI/opencv/cudabuild/modules/world/CMakeFiles/cuda_compile_1.dir/__/__/__/opencv_contrib-4.10.0/modules/videostab/src/cuda/cuda_compile_1_generated_global_motion.cu.obj.Release.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
