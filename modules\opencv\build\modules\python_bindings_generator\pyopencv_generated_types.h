CVPY_TYPE(Algorithm, Algorithm, Ptr<cv::Algorithm>, Ptr, NoBase, 0, "")
CVPY_TYPE(AsyncArray, AsyncArray, Ptr<cv::AsyncArray>, Ptr, NoBase, pyopencv_cv_AsyncArray_AsyncArray, "")
CVPY_TYPE(ClassWithKeywordProperties, utils_ClassWithKeywordProperties, cv::utils::ClassWithKeywordProperties, ClassWithKeywordProperties, NoBase, pyopencv_cv_utils_utils_ClassWithKeywordProperties_ClassWithKeywordProperties, ".utils")
CVPY_TYPE(ExportClassName, utils_nested_OriginalClassName, Ptr<cv::utils::nested::OriginalClassName>, Ptr, NoBase, 0, ".utils.nested")
CVPY_TYPE(Params, utils_nested_OriginalClassName_Params, cv::utils::nested::OriginalClassName::<PERSON><PERSON>, <PERSON><PERSON>, NoBase, pyopencv_cv_utils_nested_utils_nested_OriginalClassName_Params_OriginalClassName_Params, ".utils.nested.ExportClassName")
CVPY_TYPE(GpuMat, cuda_GpuMat, Ptr<cv::cuda::GpuMat>, Ptr, NoBase, pyopencv_cv_cuda_cuda_GpuMat_GpuMat, ".cuda")
CVPY_TYPE(Allocator, cuda_GpuMat_Allocator, Ptr<cv::cuda::GpuMat::Allocator>, Ptr, NoBase, 0, ".cuda.GpuMat")
CVPY_TYPE(GpuData, cuda_GpuData, Ptr<cv::cuda::GpuData>, Ptr, NoBase, 0, ".cuda")
CVPY_TYPE(GpuMatND, cuda_GpuMatND, Ptr<cv::cuda::GpuMatND>, Ptr, NoBase, 0, ".cuda")
CVPY_TYPE(BufferPool, cuda_BufferPool, Ptr<cv::cuda::BufferPool>, Ptr, NoBase, pyopencv_cv_cuda_cuda_BufferPool_BufferPool, ".cuda")
CVPY_TYPE(HostMem, cuda_HostMem, Ptr<cv::cuda::HostMem>, Ptr, NoBase, pyopencv_cv_cuda_cuda_HostMem_HostMem, ".cuda")
CVPY_TYPE(Stream, cuda_Stream, Ptr<cv::cuda::Stream>, Ptr, NoBase, pyopencv_cv_cuda_cuda_Stream_Stream, ".cuda")
CVPY_TYPE(Event, cuda_Event, Ptr<cv::cuda::Event>, Ptr, NoBase, pyopencv_cv_cuda_cuda_Event_Event, ".cuda")
CVPY_TYPE(TargetArchs, cuda_TargetArchs, Ptr<cv::cuda::TargetArchs>, Ptr, NoBase, 0, ".cuda")
CVPY_TYPE(DeviceInfo, cuda_DeviceInfo, Ptr<cv::cuda::DeviceInfo>, Ptr, NoBase, pyopencv_cv_cuda_cuda_DeviceInfo_DeviceInfo, ".cuda")
CVPY_TYPE(Device, ocl_Device, cv::ocl::Device, Device, NoBase, pyopencv_cv_ocl_ocl_Device_Device, ".ocl")
CVPY_TYPE(OpenCLExecutionContext, ocl_OpenCLExecutionContext, Ptr<cv::ocl::OpenCLExecutionContext>, Ptr, NoBase, 0, ".ocl")
CVPY_TYPE(FileStorage, FileStorage, Ptr<cv::FileStorage>, Ptr, NoBase, pyopencv_cv_FileStorage_FileStorage, "")
CVPY_TYPE(FileNode, FileNode, cv::FileNode, FileNode, NoBase, pyopencv_cv_FileNode_FileNode, "")
CVPY_TYPE(RotatedRect, RotatedRect, cv::RotatedRect, RotatedRect, NoBase, pyopencv_cv_RotatedRect_RotatedRect, "")
CVPY_TYPE(KeyPoint, KeyPoint, cv::KeyPoint, KeyPoint, NoBase, pyopencv_cv_KeyPoint_KeyPoint, "")
CVPY_TYPE(DMatch, DMatch, cv::DMatch, DMatch, NoBase, pyopencv_cv_DMatch_DMatch, "")
CVPY_TYPE(TickMeter, TickMeter, Ptr<cv::TickMeter>, Ptr, NoBase, pyopencv_cv_TickMeter_TickMeter, "")
CVPY_TYPE(UMat, UMat, Ptr<cv::UMat>, Ptr, NoBase, pyopencv_cv_UMat_UMat, "")
CVPY_TYPE(LookUpTable, cuda_LookUpTable, Ptr<cv::cuda::LookUpTable>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(DFT, cuda_DFT, Ptr<cv::cuda::DFT>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(Convolution, cuda_Convolution, Ptr<cv::cuda::Convolution>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(Index, flann_Index, Ptr<cv::flann::Index>, Ptr, NoBase, pyopencv_cv_flann_flann_Index_Index, ".flann")
CVPY_TYPE(GeneralizedHough, GeneralizedHough, Ptr<cv::GeneralizedHough>, Ptr, Algorithm, 0, "")
CVPY_TYPE(GeneralizedHoughBallard, GeneralizedHoughBallard, Ptr<cv::GeneralizedHoughBallard>, Ptr, GeneralizedHough, 0, "")
CVPY_TYPE(GeneralizedHoughGuil, GeneralizedHoughGuil, Ptr<cv::GeneralizedHoughGuil>, Ptr, GeneralizedHough, 0, "")
CVPY_TYPE(CLAHE, CLAHE, Ptr<cv::CLAHE>, Ptr, Algorithm, 0, "")
CVPY_TYPE(Subdiv2D, Subdiv2D, Ptr<cv::Subdiv2D>, Ptr, NoBase, pyopencv_cv_Subdiv2D_Subdiv2D, "")
CVPY_TYPE(LineSegmentDetector, LineSegmentDetector, Ptr<cv::LineSegmentDetector>, Ptr, Algorithm, 0, "")
CVPY_TYPE(IntelligentScissorsMB, segmentation_IntelligentScissorsMB, cv::segmentation::IntelligentScissorsMB, IntelligentScissorsMB, NoBase, pyopencv_cv_segmentation_segmentation_IntelligentScissorsMB_IntelligentScissorsMB, ".segmentation")
CVPY_TYPE(ParamGrid, ml_ParamGrid, Ptr<cv::ml::ParamGrid>, Ptr, NoBase, 0, ".ml")
CVPY_TYPE(TrainData, ml_TrainData, Ptr<cv::ml::TrainData>, Ptr, NoBase, 0, ".ml")
CVPY_TYPE(StatModel, ml_StatModel, Ptr<cv::ml::StatModel>, Ptr, Algorithm, 0, ".ml")
CVPY_TYPE(NormalBayesClassifier, ml_NormalBayesClassifier, Ptr<cv::ml::NormalBayesClassifier>, Ptr, ml_StatModel, 0, ".ml")
CVPY_TYPE(KNearest, ml_KNearest, Ptr<cv::ml::KNearest>, Ptr, ml_StatModel, 0, ".ml")
CVPY_TYPE(SVM, ml_SVM, Ptr<cv::ml::SVM>, Ptr, ml_StatModel, 0, ".ml")
CVPY_TYPE(EM, ml_EM, Ptr<cv::ml::EM>, Ptr, ml_StatModel, 0, ".ml")
CVPY_TYPE(DTrees, ml_DTrees, Ptr<cv::ml::DTrees>, Ptr, ml_StatModel, 0, ".ml")
CVPY_TYPE(RTrees, ml_RTrees, Ptr<cv::ml::RTrees>, Ptr, ml_DTrees, 0, ".ml")
CVPY_TYPE(Boost, ml_Boost, Ptr<cv::ml::Boost>, Ptr, ml_DTrees, 0, ".ml")
CVPY_TYPE(ANN_MLP, ml_ANN_MLP, Ptr<cv::ml::ANN_MLP>, Ptr, ml_StatModel, 0, ".ml")
CVPY_TYPE(LogisticRegression, ml_LogisticRegression, Ptr<cv::ml::LogisticRegression>, Ptr, ml_StatModel, 0, ".ml")
CVPY_TYPE(SVMSGD, ml_SVMSGD, Ptr<cv::ml::SVMSGD>, Ptr, ml_StatModel, 0, ".ml")
CVPY_TYPE(PhaseUnwrapping, phase_unwrapping_PhaseUnwrapping, Ptr<cv::phase_unwrapping::PhaseUnwrapping>, Ptr, Algorithm, 0, ".phase_unwrapping")
CVPY_TYPE(HistogramPhaseUnwrapping, phase_unwrapping_HistogramPhaseUnwrapping, Ptr<cv::phase_unwrapping::HistogramPhaseUnwrapping>, Ptr, phase_unwrapping_PhaseUnwrapping, 0, ".phase_unwrapping")
CVPY_TYPE(Params, phase_unwrapping_HistogramPhaseUnwrapping_Params, cv::phase_unwrapping::HistogramPhaseUnwrapping::Params, Params, NoBase, pyopencv_cv_phase_unwrapping_phase_unwrapping_HistogramPhaseUnwrapping_Params_HistogramPhaseUnwrapping_Params, ".phase_unwrapping.HistogramPhaseUnwrapping")
CVPY_TYPE(Plot2d, plot_Plot2d, Ptr<cv::plot::Plot2d>, Ptr, Algorithm, 0, ".plot")
CVPY_TYPE(QualityBase, quality_QualityBase, Ptr<cv::quality::QualityBase>, Ptr, Algorithm, 0, ".quality")
CVPY_TYPE(QualityBRISQUE, quality_QualityBRISQUE, Ptr<cv::quality::QualityBRISQUE>, Ptr, quality_QualityBase, 0, ".quality")
CVPY_TYPE(QualityGMSD, quality_QualityGMSD, Ptr<cv::quality::QualityGMSD>, Ptr, quality_QualityBase, 0, ".quality")
CVPY_TYPE(QualityMSE, quality_QualityMSE, Ptr<cv::quality::QualityMSE>, Ptr, quality_QualityBase, 0, ".quality")
CVPY_TYPE(QualityPSNR, quality_QualityPSNR, Ptr<cv::quality::QualityPSNR>, Ptr, quality_QualityBase, 0, ".quality")
CVPY_TYPE(QualitySSIM, quality_QualitySSIM, Ptr<cv::quality::QualitySSIM>, Ptr, quality_QualityBase, 0, ".quality")
CVPY_TYPE(Map, reg_Map, Ptr<cv::reg::Map>, Ptr, NoBase, 0, ".reg")
CVPY_TYPE(MapAffine, reg_MapAffine, Ptr<cv::reg::MapAffine>, Ptr, reg_Map, pyopencv_cv_reg_reg_MapAffine_MapAffine, ".reg")
CVPY_TYPE(Mapper, reg_Mapper, Ptr<cv::reg::Mapper>, Ptr, NoBase, 0, ".reg")
CVPY_TYPE(MapperGradAffine, reg_MapperGradAffine, Ptr<cv::reg::MapperGradAffine>, Ptr, reg_Mapper, pyopencv_cv_reg_reg_MapperGradAffine_MapperGradAffine, ".reg")
CVPY_TYPE(MapperGradEuclid, reg_MapperGradEuclid, Ptr<cv::reg::MapperGradEuclid>, Ptr, reg_Mapper, pyopencv_cv_reg_reg_MapperGradEuclid_MapperGradEuclid, ".reg")
CVPY_TYPE(MapperGradProj, reg_MapperGradProj, Ptr<cv::reg::MapperGradProj>, Ptr, reg_Mapper, pyopencv_cv_reg_reg_MapperGradProj_MapperGradProj, ".reg")
CVPY_TYPE(MapperGradShift, reg_MapperGradShift, Ptr<cv::reg::MapperGradShift>, Ptr, reg_Mapper, pyopencv_cv_reg_reg_MapperGradShift_MapperGradShift, ".reg")
CVPY_TYPE(MapperGradSimilar, reg_MapperGradSimilar, Ptr<cv::reg::MapperGradSimilar>, Ptr, reg_Mapper, pyopencv_cv_reg_reg_MapperGradSimilar_MapperGradSimilar, ".reg")
CVPY_TYPE(MapperPyramid, reg_MapperPyramid, Ptr<cv::reg::MapperPyramid>, Ptr, reg_Mapper, pyopencv_cv_reg_reg_MapperPyramid_MapperPyramid, ".reg")
CVPY_TYPE(MapTypeCaster, reg_MapTypeCaster, Ptr<cv::reg::MapTypeCaster>, Ptr, NoBase, 0, ".reg")
CVPY_TYPE(MapProjec, reg_MapProjec, Ptr<cv::reg::MapProjec>, Ptr, reg_Map, pyopencv_cv_reg_reg_MapProjec_MapProjec, ".reg")
CVPY_TYPE(MapShift, reg_MapShift, Ptr<cv::reg::MapShift>, Ptr, reg_Map, pyopencv_cv_reg_reg_MapShift_MapShift, ".reg")
CVPY_TYPE(ICP, ppf_match_3d_ICP, Ptr<cv::ppf_match_3d::ICP>, Ptr, NoBase, pyopencv_cv_ppf_match_3d_ppf_match_3d_ICP_ICP, ".ppf_match_3d")
CVPY_TYPE(Pose3D, ppf_match_3d_Pose3D, Ptr<cv::ppf_match_3d::Pose3D>, Ptr, NoBase, pyopencv_cv_ppf_match_3d_ppf_match_3d_Pose3D_Pose3D, ".ppf_match_3d")
CVPY_TYPE(PoseCluster3D, ppf_match_3d_PoseCluster3D, Ptr<cv::ppf_match_3d::PoseCluster3D>, Ptr, NoBase, 0, ".ppf_match_3d")
CVPY_TYPE(PPF3DDetector, ppf_match_3d_PPF3DDetector, Ptr<cv::ppf_match_3d::PPF3DDetector>, Ptr, NoBase, pyopencv_cv_ppf_match_3d_ppf_match_3d_PPF3DDetector_PPF3DDetector, ".ppf_match_3d")
CVPY_TYPE(Filter, cuda_Filter, Ptr<cv::cuda::Filter>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(CLAHE, cuda_CLAHE, Ptr<cv::cuda::CLAHE>, Ptr, CLAHE, 0, ".cuda")
CVPY_TYPE(CannyEdgeDetector, cuda_CannyEdgeDetector, Ptr<cv::cuda::CannyEdgeDetector>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(HoughLinesDetector, cuda_HoughLinesDetector, Ptr<cv::cuda::HoughLinesDetector>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(HoughSegmentDetector, cuda_HoughSegmentDetector, Ptr<cv::cuda::HoughSegmentDetector>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(HoughCirclesDetector, cuda_HoughCirclesDetector, Ptr<cv::cuda::HoughCirclesDetector>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(CornernessCriteria, cuda_CornernessCriteria, Ptr<cv::cuda::CornernessCriteria>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(CornersDetector, cuda_CornersDetector, Ptr<cv::cuda::CornersDetector>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(TemplateMatching, cuda_TemplateMatching, Ptr<cv::cuda::TemplateMatching>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(DictValue, dnn_DictValue, Ptr<cv::dnn::DictValue>, Ptr, NoBase, pyopencv_cv_dnn_dnn_DictValue_DictValue, ".dnn")
CVPY_TYPE(Layer, dnn_Layer, Ptr<cv::dnn::Layer>, Ptr, Algorithm, 0, ".dnn")
CVPY_TYPE(Net, dnn_Net, cv::dnn::Net, Net, NoBase, pyopencv_cv_dnn_dnn_Net_Net, ".dnn")
CVPY_TYPE(Image2BlobParams, dnn_Image2BlobParams, cv::dnn::Image2BlobParams, Image2BlobParams, NoBase, pyopencv_cv_dnn_dnn_Image2BlobParams_Image2BlobParams, ".dnn")
CVPY_TYPE(Model, dnn_Model, cv::dnn::Model, Model, NoBase, pyopencv_cv_dnn_dnn_Model_Model, ".dnn")
CVPY_TYPE(ClassificationModel, dnn_ClassificationModel, cv::dnn::ClassificationModel, ClassificationModel, dnn_Model, pyopencv_cv_dnn_dnn_ClassificationModel_ClassificationModel, ".dnn")
CVPY_TYPE(KeypointsModel, dnn_KeypointsModel, cv::dnn::KeypointsModel, KeypointsModel, dnn_Model, pyopencv_cv_dnn_dnn_KeypointsModel_KeypointsModel, ".dnn")
CVPY_TYPE(SegmentationModel, dnn_SegmentationModel, cv::dnn::SegmentationModel, SegmentationModel, dnn_Model, pyopencv_cv_dnn_dnn_SegmentationModel_SegmentationModel, ".dnn")
CVPY_TYPE(DetectionModel, dnn_DetectionModel, cv::dnn::DetectionModel, DetectionModel, dnn_Model, pyopencv_cv_dnn_dnn_DetectionModel_DetectionModel, ".dnn")
CVPY_TYPE(TextRecognitionModel, dnn_TextRecognitionModel, cv::dnn::TextRecognitionModel, TextRecognitionModel, dnn_Model, pyopencv_cv_dnn_dnn_TextRecognitionModel_TextRecognitionModel, ".dnn")
CVPY_TYPE(TextDetectionModel, dnn_TextDetectionModel, cv::dnn::TextDetectionModel, TextDetectionModel, dnn_Model, 0, ".dnn")
CVPY_TYPE(TextDetectionModel_EAST, dnn_TextDetectionModel_EAST, cv::dnn::TextDetectionModel_EAST, TextDetectionModel_EAST, dnn_TextDetectionModel, pyopencv_cv_dnn_dnn_TextDetectionModel_EAST_TextDetectionModel_EAST, ".dnn")
CVPY_TYPE(TextDetectionModel_DB, dnn_TextDetectionModel_DB, cv::dnn::TextDetectionModel_DB, TextDetectionModel_DB, dnn_TextDetectionModel, pyopencv_cv_dnn_dnn_TextDetectionModel_DB_TextDetectionModel_DB, ".dnn")
CVPY_TYPE(DnnSuperResImpl, dnn_superres_DnnSuperResImpl, Ptr<cv::dnn_superres::DnnSuperResImpl>, Ptr, NoBase, 0, ".dnn_superres")
CVPY_TYPE(Feature2D, Feature2D, Ptr<cv::Feature2D>, Ptr, NoBase, 0, "")
CVPY_TYPE(AffineFeature, AffineFeature, Ptr<cv::AffineFeature>, Ptr, Feature2D, 0, "")
CVPY_TYPE(SIFT, SIFT, Ptr<cv::SIFT>, Ptr, Feature2D, 0, "")
CVPY_TYPE(BRISK, BRISK, Ptr<cv::BRISK>, Ptr, Feature2D, 0, "")
CVPY_TYPE(ORB, ORB, Ptr<cv::ORB>, Ptr, Feature2D, 0, "")
CVPY_TYPE(MSER, MSER, Ptr<cv::MSER>, Ptr, Feature2D, 0, "")
CVPY_TYPE(FastFeatureDetector, FastFeatureDetector, Ptr<cv::FastFeatureDetector>, Ptr, Feature2D, 0, "")
CVPY_TYPE(AgastFeatureDetector, AgastFeatureDetector, Ptr<cv::AgastFeatureDetector>, Ptr, Feature2D, 0, "")
CVPY_TYPE(GFTTDetector, GFTTDetector, Ptr<cv::GFTTDetector>, Ptr, Feature2D, 0, "")
CVPY_TYPE(SimpleBlobDetector, SimpleBlobDetector, Ptr<cv::SimpleBlobDetector>, Ptr, Feature2D, 0, "")
CVPY_TYPE(Params, SimpleBlobDetector_Params, cv::SimpleBlobDetector::Params, Params, NoBase, pyopencv_cv_SimpleBlobDetector_Params_SimpleBlobDetector_Params, ".SimpleBlobDetector")
CVPY_TYPE(KAZE, KAZE, Ptr<cv::KAZE>, Ptr, Feature2D, 0, "")
CVPY_TYPE(AKAZE, AKAZE, Ptr<cv::AKAZE>, Ptr, Feature2D, 0, "")
CVPY_TYPE(DescriptorMatcher, DescriptorMatcher, Ptr<cv::DescriptorMatcher>, Ptr, Algorithm, 0, "")
CVPY_TYPE(BFMatcher, BFMatcher, Ptr<cv::BFMatcher>, Ptr, DescriptorMatcher, pyopencv_cv_BFMatcher_BFMatcher, "")
CVPY_TYPE(FlannBasedMatcher, FlannBasedMatcher, Ptr<cv::FlannBasedMatcher>, Ptr, DescriptorMatcher, pyopencv_cv_FlannBasedMatcher_FlannBasedMatcher, "")
CVPY_TYPE(BOWTrainer, BOWTrainer, Ptr<cv::BOWTrainer>, Ptr, NoBase, 0, "")
CVPY_TYPE(BOWKMeansTrainer, BOWKMeansTrainer, Ptr<cv::BOWKMeansTrainer>, Ptr, BOWTrainer, pyopencv_cv_BOWKMeansTrainer_BOWKMeansTrainer, "")
CVPY_TYPE(BOWImgDescriptorExtractor, BOWImgDescriptorExtractor, Ptr<cv::BOWImgDescriptorExtractor>, Ptr, NoBase, pyopencv_cv_BOWImgDescriptorExtractor_BOWImgDescriptorExtractor, "")
CVPY_TYPE(HfsSegment, hfs_HfsSegment, Ptr<cv::hfs::HfsSegment>, Ptr, Algorithm, 0, ".hfs")
CVPY_TYPE(KeyLine, line_descriptor_KeyLine, cv::line_descriptor::KeyLine, KeyLine, NoBase, pyopencv_cv_line_descriptor_line_descriptor_KeyLine_KeyLine, ".line_descriptor")
CVPY_TYPE(BinaryDescriptor, line_descriptor_BinaryDescriptor, Ptr<cv::line_descriptor::BinaryDescriptor>, Ptr, Algorithm, 0, ".line_descriptor")
CVPY_TYPE(LSDParam, line_descriptor_LSDParam, cv::line_descriptor::LSDParam, LSDParam, NoBase, pyopencv_cv_line_descriptor_line_descriptor_LSDParam_LSDParam, ".line_descriptor")
CVPY_TYPE(LSDDetector, line_descriptor_LSDDetector, Ptr<cv::line_descriptor::LSDDetector>, Ptr, Algorithm, pyopencv_cv_line_descriptor_line_descriptor_LSDDetector_LSDDetectorWithParams, ".line_descriptor")
CVPY_TYPE(BinaryDescriptorMatcher, line_descriptor_BinaryDescriptorMatcher, Ptr<cv::line_descriptor::BinaryDescriptorMatcher>, Ptr, Algorithm, pyopencv_cv_line_descriptor_line_descriptor_BinaryDescriptorMatcher_BinaryDescriptorMatcher, ".line_descriptor")
CVPY_TYPE(DrawLinesMatchesFlags, line_descriptor_DrawLinesMatchesFlags, cv::line_descriptor::DrawLinesMatchesFlags, DrawLinesMatchesFlags, NoBase, 0, ".line_descriptor")
CVPY_TYPE(Tonemap, Tonemap, Ptr<cv::Tonemap>, Ptr, Algorithm, 0, "")
CVPY_TYPE(TonemapDrago, TonemapDrago, Ptr<cv::TonemapDrago>, Ptr, Tonemap, 0, "")
CVPY_TYPE(TonemapReinhard, TonemapReinhard, Ptr<cv::TonemapReinhard>, Ptr, Tonemap, 0, "")
CVPY_TYPE(TonemapMantiuk, TonemapMantiuk, Ptr<cv::TonemapMantiuk>, Ptr, Tonemap, 0, "")
CVPY_TYPE(AlignExposures, AlignExposures, Ptr<cv::AlignExposures>, Ptr, Algorithm, 0, "")
CVPY_TYPE(AlignMTB, AlignMTB, Ptr<cv::AlignMTB>, Ptr, AlignExposures, 0, "")
CVPY_TYPE(CalibrateCRF, CalibrateCRF, Ptr<cv::CalibrateCRF>, Ptr, Algorithm, 0, "")
CVPY_TYPE(CalibrateDebevec, CalibrateDebevec, Ptr<cv::CalibrateDebevec>, Ptr, CalibrateCRF, 0, "")
CVPY_TYPE(CalibrateRobertson, CalibrateRobertson, Ptr<cv::CalibrateRobertson>, Ptr, CalibrateCRF, 0, "")
CVPY_TYPE(MergeExposures, MergeExposures, Ptr<cv::MergeExposures>, Ptr, Algorithm, 0, "")
CVPY_TYPE(MergeDebevec, MergeDebevec, Ptr<cv::MergeDebevec>, Ptr, MergeExposures, 0, "")
CVPY_TYPE(MergeMertens, MergeMertens, Ptr<cv::MergeMertens>, Ptr, MergeExposures, 0, "")
CVPY_TYPE(MergeRobertson, MergeRobertson, Ptr<cv::MergeRobertson>, Ptr, MergeExposures, 0, "")
CVPY_TYPE(Saliency, saliency_Saliency, Ptr<cv::saliency::Saliency>, Ptr, Algorithm, 0, ".saliency")
CVPY_TYPE(StaticSaliency, saliency_StaticSaliency, Ptr<cv::saliency::StaticSaliency>, Ptr, saliency_Saliency, 0, ".saliency")
CVPY_TYPE(MotionSaliency, saliency_MotionSaliency, Ptr<cv::saliency::MotionSaliency>, Ptr, saliency_Saliency, 0, ".saliency")
CVPY_TYPE(Objectness, saliency_Objectness, Ptr<cv::saliency::Objectness>, Ptr, saliency_Saliency, 0, ".saliency")
CVPY_TYPE(StaticSaliencySpectralResidual, saliency_StaticSaliencySpectralResidual, Ptr<cv::saliency::StaticSaliencySpectralResidual>, Ptr, saliency_StaticSaliency, 0, ".saliency")
CVPY_TYPE(StaticSaliencyFineGrained, saliency_StaticSaliencyFineGrained, Ptr<cv::saliency::StaticSaliencyFineGrained>, Ptr, saliency_StaticSaliency, 0, ".saliency")
CVPY_TYPE(MotionSaliencyBinWangApr2014, saliency_MotionSaliencyBinWangApr2014, Ptr<cv::saliency::MotionSaliencyBinWangApr2014>, Ptr, saliency_MotionSaliency, 0, ".saliency")
CVPY_TYPE(ObjectnessBING, saliency_ObjectnessBING, Ptr<cv::saliency::ObjectnessBING>, Ptr, saliency_Objectness, 0, ".saliency")
CVPY_TYPE(ERFilter, text_ERFilter, Ptr<cv::text::ERFilter>, Ptr, Algorithm, 0, ".text")
CVPY_TYPE(Callback, text_ERFilter_Callback, Ptr<cv::text::ERFilter::Callback>, Ptr, NoBase, 0, ".text.ERFilter")
CVPY_TYPE(BaseOCR, text_BaseOCR, Ptr<cv::text::BaseOCR>, Ptr, NoBase, 0, ".text")
CVPY_TYPE(OCRTesseract, text_OCRTesseract, Ptr<cv::text::OCRTesseract>, Ptr, text_BaseOCR, 0, ".text")
CVPY_TYPE(OCRHMMDecoder, text_OCRHMMDecoder, Ptr<cv::text::OCRHMMDecoder>, Ptr, text_BaseOCR, 0, ".text")
CVPY_TYPE(ClassifierCallback, text_OCRHMMDecoder_ClassifierCallback, Ptr<cv::text::OCRHMMDecoder::ClassifierCallback>, Ptr, NoBase, 0, ".text.OCRHMMDecoder")
CVPY_TYPE(OCRBeamSearchDecoder, text_OCRBeamSearchDecoder, Ptr<cv::text::OCRBeamSearchDecoder>, Ptr, text_BaseOCR, 0, ".text")
CVPY_TYPE(ClassifierCallback, text_OCRBeamSearchDecoder_ClassifierCallback, Ptr<cv::text::OCRBeamSearchDecoder::ClassifierCallback>, Ptr, NoBase, 0, ".text.OCRBeamSearchDecoder")
CVPY_TYPE(TextDetector, text_TextDetector, Ptr<cv::text::TextDetector>, Ptr, NoBase, 0, ".text")
CVPY_TYPE(TextDetectorCNN, text_TextDetectorCNN, Ptr<cv::text::TextDetectorCNN>, Ptr, text_TextDetector, 0, ".text")
CVPY_TYPE(VideoCapture, VideoCapture, Ptr<cv::VideoCapture>, Ptr, NoBase, pyopencv_cv_VideoCapture_VideoCapture, "")
CVPY_TYPE(VideoWriter, VideoWriter, Ptr<cv::VideoWriter>, Ptr, NoBase, pyopencv_cv_VideoWriter_VideoWriter, "")
CVPY_TYPE(TonemapDurand, xphoto_TonemapDurand, Ptr<cv::xphoto::TonemapDurand>, Ptr, Tonemap, 0, ".xphoto")
CVPY_TYPE(WhiteBalancer, xphoto_WhiteBalancer, Ptr<cv::xphoto::WhiteBalancer>, Ptr, Algorithm, 0, ".xphoto")
CVPY_TYPE(SimpleWB, xphoto_SimpleWB, Ptr<cv::xphoto::SimpleWB>, Ptr, xphoto_WhiteBalancer, 0, ".xphoto")
CVPY_TYPE(GrayworldWB, xphoto_GrayworldWB, Ptr<cv::xphoto::GrayworldWB>, Ptr, xphoto_WhiteBalancer, 0, ".xphoto")
CVPY_TYPE(LearningBasedWB, xphoto_LearningBasedWB, Ptr<cv::xphoto::LearningBasedWB>, Ptr, xphoto_WhiteBalancer, 0, ".xphoto")
CVPY_TYPE(UsacParams, UsacParams, cv::UsacParams, UsacParams, NoBase, pyopencv_cv_UsacParams_UsacParams, "")
CVPY_TYPE(CirclesGridFinderParameters, CirclesGridFinderParameters, cv::CirclesGridFinderParameters, CirclesGridFinderParameters, NoBase, pyopencv_cv_CirclesGridFinderParameters_CirclesGridFinderParameters, "")
CVPY_TYPE(StereoMatcher, StereoMatcher, Ptr<cv::StereoMatcher>, Ptr, Algorithm, 0, "")
CVPY_TYPE(StereoBM, StereoBM, Ptr<cv::StereoBM>, Ptr, StereoMatcher, 0, "")
CVPY_TYPE(StereoSGBM, StereoSGBM, Ptr<cv::StereoSGBM>, Ptr, StereoMatcher, 0, "")
CVPY_TYPE(EncodeQp, cudacodec_EncodeQp, cv::cudacodec::EncodeQp, EncodeQp, NoBase, 0, ".cudacodec")
CVPY_TYPE(EncoderParams, cudacodec_EncoderParams, cv::cudacodec::EncoderParams, EncoderParams, NoBase, pyopencv_cv_cudacodec_cudacodec_EncoderParams_EncoderParams, ".cudacodec")
CVPY_TYPE(EncoderCallback, cudacodec_EncoderCallback, Ptr<cv::cudacodec::EncoderCallback>, Ptr, NoBase, 0, ".cudacodec")
CVPY_TYPE(VideoWriter, cudacodec_VideoWriter, Ptr<cv::cudacodec::VideoWriter>, Ptr, NoBase, 0, ".cudacodec")
CVPY_TYPE(FormatInfo, cudacodec_FormatInfo, cv::cudacodec::FormatInfo, FormatInfo, NoBase, pyopencv_cv_cudacodec_cudacodec_FormatInfo_FormatInfo, ".cudacodec")
CVPY_TYPE(VideoReader, cudacodec_VideoReader, Ptr<cv::cudacodec::VideoReader>, Ptr, NoBase, 0, ".cudacodec")
CVPY_TYPE(RawVideoSource, cudacodec_RawVideoSource, Ptr<cv::cudacodec::RawVideoSource>, Ptr, NoBase, 0, ".cudacodec")
CVPY_TYPE(VideoReaderInitParams, cudacodec_VideoReaderInitParams, cv::cudacodec::VideoReaderInitParams, VideoReaderInitParams, NoBase, pyopencv_cv_cudacodec_cudacodec_VideoReaderInitParams_VideoReaderInitParams, ".cudacodec")
CVPY_TYPE(DescriptorMatcher, cuda_DescriptorMatcher, Ptr<cv::cuda::DescriptorMatcher>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(Feature2DAsync, cuda_Feature2DAsync, Ptr<cv::cuda::Feature2DAsync>, Ptr, Feature2D, 0, ".cuda")
CVPY_TYPE(FastFeatureDetector, cuda_FastFeatureDetector, Ptr<cv::cuda::FastFeatureDetector>, Ptr, cuda_Feature2DAsync, 0, ".cuda")
CVPY_TYPE(ORB, cuda_ORB, Ptr<cv::cuda::ORB>, Ptr, cuda_Feature2DAsync, 0, ".cuda")
CVPY_TYPE(StereoBM, cuda_StereoBM, Ptr<cv::cuda::StereoBM>, Ptr, StereoBM, 0, ".cuda")
CVPY_TYPE(StereoBeliefPropagation, cuda_StereoBeliefPropagation, Ptr<cv::cuda::StereoBeliefPropagation>, Ptr, StereoMatcher, 0, ".cuda")
CVPY_TYPE(StereoConstantSpaceBP, cuda_StereoConstantSpaceBP, Ptr<cv::cuda::StereoConstantSpaceBP>, Ptr, cuda_StereoBeliefPropagation, 0, ".cuda")
CVPY_TYPE(StereoSGM, cuda_StereoSGM, Ptr<cv::cuda::StereoSGM>, Ptr, StereoSGBM, 0, ".cuda")
CVPY_TYPE(DisparityBilateralFilter, cuda_DisparityBilateralFilter, Ptr<cv::cuda::DisparityBilateralFilter>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(ColorCorrectionModel, ccm_ColorCorrectionModel, Ptr<cv::ccm::ColorCorrectionModel>, Ptr, NoBase, pyopencv_cv_ccm_ccm_ColorCorrectionModel_ColorCorrectionModel, ".ccm")
CVPY_TYPE(DetectorParameters, mcc_DetectorParameters, Ptr<cv::mcc::DetectorParameters>, Ptr, NoBase, 0, ".mcc")
CVPY_TYPE(CCheckerDetector, mcc_CCheckerDetector, Ptr<cv::mcc::CCheckerDetector>, Ptr, Algorithm, 0, ".mcc")
CVPY_TYPE(CChecker, mcc_CChecker, Ptr<cv::mcc::CChecker>, Ptr, NoBase, 0, ".mcc")
CVPY_TYPE(CCheckerDraw, mcc_CCheckerDraw, Ptr<cv::mcc::CCheckerDraw>, Ptr, NoBase, 0, ".mcc")
CVPY_TYPE(BaseCascadeClassifier, BaseCascadeClassifier, Ptr<cv::BaseCascadeClassifier>, Ptr, Algorithm, 0, "")
CVPY_TYPE(CascadeClassifier, CascadeClassifier, Ptr<cv::CascadeClassifier>, Ptr, NoBase, pyopencv_cv_CascadeClassifier_CascadeClassifier, "")
CVPY_TYPE(HOGDescriptor, HOGDescriptor, Ptr<cv::HOGDescriptor>, Ptr, NoBase, pyopencv_cv_HOGDescriptor_HOGDescriptor, "")
CVPY_TYPE(QRCodeEncoder, QRCodeEncoder, Ptr<cv::QRCodeEncoder>, Ptr, NoBase, 0, "")
CVPY_TYPE(Params, QRCodeEncoder_Params, cv::QRCodeEncoder::Params, Params, NoBase, pyopencv_cv_QRCodeEncoder_Params_QRCodeEncoder_Params, ".QRCodeEncoder")
CVPY_TYPE(GraphicalCodeDetector, GraphicalCodeDetector, cv::GraphicalCodeDetector, GraphicalCodeDetector, NoBase, 0, "")
CVPY_TYPE(QRCodeDetector, QRCodeDetector, cv::QRCodeDetector, QRCodeDetector, GraphicalCodeDetector, pyopencv_cv_QRCodeDetector_QRCodeDetector, "")
CVPY_TYPE(QRCodeDetectorAruco, QRCodeDetectorAruco, cv::QRCodeDetectorAruco, QRCodeDetectorAruco, GraphicalCodeDetector, pyopencv_cv_QRCodeDetectorAruco_QRCodeDetectorAruco, "")
CVPY_TYPE(Params, QRCodeDetectorAruco_Params, cv::QRCodeDetectorAruco::Params, Params, NoBase, pyopencv_cv_QRCodeDetectorAruco_Params_QRCodeDetectorAruco_Params, ".QRCodeDetectorAruco")
CVPY_TYPE(Board, aruco_Board, cv::aruco::Board, Board, NoBase, pyopencv_cv_aruco_aruco_Board_Board, ".aruco")
CVPY_TYPE(GridBoard, aruco_GridBoard, cv::aruco::GridBoard, GridBoard, aruco_Board, pyopencv_cv_aruco_aruco_GridBoard_GridBoard, ".aruco")
CVPY_TYPE(CharucoBoard, aruco_CharucoBoard, cv::aruco::CharucoBoard, CharucoBoard, aruco_Board, pyopencv_cv_aruco_aruco_CharucoBoard_CharucoBoard, ".aruco")
CVPY_TYPE(DetectorParameters, aruco_DetectorParameters, cv::aruco::DetectorParameters, DetectorParameters, NoBase, pyopencv_cv_aruco_aruco_DetectorParameters_DetectorParameters, ".aruco")
CVPY_TYPE(RefineParameters, aruco_RefineParameters, cv::aruco::RefineParameters, RefineParameters, NoBase, pyopencv_cv_aruco_aruco_RefineParameters_RefineParameters, ".aruco")
CVPY_TYPE(ArucoDetector, aruco_ArucoDetector, Ptr<cv::aruco::ArucoDetector>, Ptr, Algorithm, pyopencv_cv_aruco_aruco_ArucoDetector_ArucoDetector, ".aruco")
CVPY_TYPE(Dictionary, aruco_Dictionary, cv::aruco::Dictionary, Dictionary, NoBase, pyopencv_cv_aruco_aruco_Dictionary_Dictionary, ".aruco")
CVPY_TYPE(BarcodeDetector, barcode_BarcodeDetector, cv::barcode::BarcodeDetector, BarcodeDetector, GraphicalCodeDetector, pyopencv_cv_barcode_barcode_BarcodeDetector_BarcodeDetector, ".barcode")
CVPY_TYPE(CharucoParameters, aruco_CharucoParameters, cv::aruco::CharucoParameters, CharucoParameters, NoBase, pyopencv_cv_aruco_aruco_CharucoParameters_CharucoParameters, ".aruco")
CVPY_TYPE(CharucoDetector, aruco_CharucoDetector, Ptr<cv::aruco::CharucoDetector>, Ptr, Algorithm, pyopencv_cv_aruco_aruco_CharucoDetector_CharucoDetector, ".aruco")
CVPY_TYPE(FaceDetectorYN, FaceDetectorYN, Ptr<cv::FaceDetectorYN>, Ptr, NoBase, 0, "")
CVPY_TYPE(FaceRecognizerSF, FaceRecognizerSF, Ptr<cv::FaceRecognizerSF>, Ptr, NoBase, 0, "")
CVPY_TYPE(Tracker, rapid_Tracker, Ptr<cv::rapid::Tracker>, Ptr, Algorithm, 0, ".rapid")
CVPY_TYPE(Rapid, rapid_Rapid, Ptr<cv::rapid::Rapid>, Ptr, rapid_Tracker, 0, ".rapid")
CVPY_TYPE(OLSTracker, rapid_OLSTracker, Ptr<cv::rapid::OLSTracker>, Ptr, rapid_Tracker, 0, ".rapid")
CVPY_TYPE(GOSTracker, rapid_GOSTracker, Ptr<cv::rapid::GOSTracker>, Ptr, rapid_Tracker, 0, ".rapid")
CVPY_TYPE(Params, colored_kinfu_Params, Ptr<cv::colored_kinfu::Params>, Ptr, NoBase, pyopencv_cv_colored_kinfu_colored_kinfu_Params_Params, ".colored_kinfu")
CVPY_TYPE(ColoredKinFu, colored_kinfu_ColoredKinFu, Ptr<cv::colored_kinfu::ColoredKinFu>, Ptr, NoBase, 0, ".colored_kinfu")
CVPY_TYPE(RgbdNormals, rgbd_RgbdNormals, Ptr<cv::rgbd::RgbdNormals>, Ptr, Algorithm, 0, ".rgbd")
CVPY_TYPE(DepthCleaner, rgbd_DepthCleaner, Ptr<cv::rgbd::DepthCleaner>, Ptr, Algorithm, 0, ".rgbd")
CVPY_TYPE(RgbdPlane, rgbd_RgbdPlane, Ptr<cv::rgbd::RgbdPlane>, Ptr, Algorithm, 0, ".rgbd")
CVPY_TYPE(RgbdFrame, rgbd_RgbdFrame, Ptr<cv::rgbd::RgbdFrame>, Ptr, NoBase, 0, ".rgbd")
CVPY_TYPE(OdometryFrame, rgbd_OdometryFrame, Ptr<cv::rgbd::OdometryFrame>, Ptr, rgbd_RgbdFrame, 0, ".rgbd")
CVPY_TYPE(Odometry, rgbd_Odometry, Ptr<cv::rgbd::Odometry>, Ptr, Algorithm, 0, ".rgbd")
CVPY_TYPE(RgbdOdometry, rgbd_RgbdOdometry, Ptr<cv::rgbd::RgbdOdometry>, Ptr, rgbd_Odometry, 0, ".rgbd")
CVPY_TYPE(ICPOdometry, rgbd_ICPOdometry, Ptr<cv::rgbd::ICPOdometry>, Ptr, rgbd_Odometry, 0, ".rgbd")
CVPY_TYPE(RgbdICPOdometry, rgbd_RgbdICPOdometry, Ptr<cv::rgbd::RgbdICPOdometry>, Ptr, rgbd_Odometry, 0, ".rgbd")
CVPY_TYPE(FastICPOdometry, rgbd_FastICPOdometry, Ptr<cv::rgbd::FastICPOdometry>, Ptr, rgbd_Odometry, 0, ".rgbd")
CVPY_TYPE(DynaFu, dynafu_DynaFu, Ptr<cv::dynafu::DynaFu>, Ptr, NoBase, 0, ".dynafu")
CVPY_TYPE(Params, kinfu_Params, Ptr<cv::kinfu::Params>, Ptr, NoBase, pyopencv_cv_kinfu_kinfu_Params_Params, ".kinfu")
CVPY_TYPE(KinFu, kinfu_KinFu, Ptr<cv::kinfu::KinFu>, Ptr, NoBase, 0, ".kinfu")
CVPY_TYPE(Params, large_kinfu_Params, Ptr<cv::large_kinfu::Params>, Ptr, NoBase, 0, ".large_kinfu")
CVPY_TYPE(LargeKinfu, large_kinfu_LargeKinfu, Ptr<cv::large_kinfu::LargeKinfu>, Ptr, NoBase, 0, ".large_kinfu")
CVPY_TYPE(Feature, linemod_Feature, cv::linemod::Feature, Feature, NoBase, pyopencv_cv_linemod_linemod_Feature_Feature, ".linemod")
CVPY_TYPE(Template, linemod_Template, cv::linemod::Template, Template, NoBase, 0, ".linemod")
CVPY_TYPE(QuantizedPyramid, linemod_QuantizedPyramid, Ptr<cv::linemod::QuantizedPyramid>, Ptr, NoBase, 0, ".linemod")
CVPY_TYPE(Modality, linemod_Modality, Ptr<cv::linemod::Modality>, Ptr, NoBase, 0, ".linemod")
CVPY_TYPE(ColorGradient, linemod_ColorGradient, Ptr<cv::linemod::ColorGradient>, Ptr, linemod_Modality, 0, ".linemod")
CVPY_TYPE(DepthNormal, linemod_DepthNormal, Ptr<cv::linemod::DepthNormal>, Ptr, linemod_Modality, 0, ".linemod")
CVPY_TYPE(Match, linemod_Match, cv::linemod::Match, Match, NoBase, pyopencv_cv_linemod_linemod_Match_Match, ".linemod")
CVPY_TYPE(Detector, linemod_Detector, Ptr<cv::linemod::Detector>, Ptr, NoBase, pyopencv_cv_linemod_linemod_Detector_Detector, ".linemod")
CVPY_TYPE(Volume, kinfu_Volume, Ptr<cv::kinfu::Volume>, Ptr, NoBase, 0, ".kinfu")
CVPY_TYPE(VolumeParams, kinfu_VolumeParams, Ptr<cv::kinfu::VolumeParams>, Ptr, NoBase, 0, ".kinfu")
CVPY_TYPE(PoseGraph, kinfu_detail_PoseGraph, Ptr<cv::kinfu::detail::PoseGraph>, Ptr, NoBase, 0, ".kinfu.detail")
CVPY_TYPE(HistogramCostExtractor, HistogramCostExtractor, Ptr<cv::HistogramCostExtractor>, Ptr, Algorithm, 0, "")
CVPY_TYPE(NormHistogramCostExtractor, NormHistogramCostExtractor, Ptr<cv::NormHistogramCostExtractor>, Ptr, HistogramCostExtractor, 0, "")
CVPY_TYPE(EMDHistogramCostExtractor, EMDHistogramCostExtractor, Ptr<cv::EMDHistogramCostExtractor>, Ptr, HistogramCostExtractor, 0, "")
CVPY_TYPE(ChiHistogramCostExtractor, ChiHistogramCostExtractor, Ptr<cv::ChiHistogramCostExtractor>, Ptr, HistogramCostExtractor, 0, "")
CVPY_TYPE(EMDL1HistogramCostExtractor, EMDL1HistogramCostExtractor, Ptr<cv::EMDL1HistogramCostExtractor>, Ptr, HistogramCostExtractor, 0, "")
CVPY_TYPE(ShapeDistanceExtractor, ShapeDistanceExtractor, Ptr<cv::ShapeDistanceExtractor>, Ptr, Algorithm, 0, "")
CVPY_TYPE(ShapeContextDistanceExtractor, ShapeContextDistanceExtractor, Ptr<cv::ShapeContextDistanceExtractor>, Ptr, ShapeDistanceExtractor, 0, "")
CVPY_TYPE(HausdorffDistanceExtractor, HausdorffDistanceExtractor, Ptr<cv::HausdorffDistanceExtractor>, Ptr, ShapeDistanceExtractor, 0, "")
CVPY_TYPE(ShapeTransformer, ShapeTransformer, Ptr<cv::ShapeTransformer>, Ptr, Algorithm, 0, "")
CVPY_TYPE(ThinPlateSplineShapeTransformer, ThinPlateSplineShapeTransformer, Ptr<cv::ThinPlateSplineShapeTransformer>, Ptr, ShapeTransformer, 0, "")
CVPY_TYPE(AffineTransformer, AffineTransformer, Ptr<cv::AffineTransformer>, Ptr, ShapeTransformer, 0, "")
CVPY_TYPE(StructuredLightPattern, structured_light_StructuredLightPattern, Ptr<cv::structured_light::StructuredLightPattern>, Ptr, Algorithm, 0, ".structured_light")
CVPY_TYPE(GrayCodePattern, structured_light_GrayCodePattern, Ptr<cv::structured_light::GrayCodePattern>, Ptr, structured_light_StructuredLightPattern, 0, ".structured_light")
CVPY_TYPE(SinusoidalPattern, structured_light_SinusoidalPattern, Ptr<cv::structured_light::SinusoidalPattern>, Ptr, structured_light_StructuredLightPattern, 0, ".structured_light")
CVPY_TYPE(Params, structured_light_SinusoidalPattern_Params, Ptr<cv::structured_light::SinusoidalPattern::Params>, Ptr, NoBase, pyopencv_cv_structured_light_structured_light_SinusoidalPattern_Params_SinusoidalPattern_Params, ".structured_light.SinusoidalPattern")
CVPY_TYPE(BackgroundSubtractor, BackgroundSubtractor, Ptr<cv::BackgroundSubtractor>, Ptr, Algorithm, 0, "")
CVPY_TYPE(BackgroundSubtractorMOG2, BackgroundSubtractorMOG2, Ptr<cv::BackgroundSubtractorMOG2>, Ptr, BackgroundSubtractor, 0, "")
CVPY_TYPE(BackgroundSubtractorKNN, BackgroundSubtractorKNN, Ptr<cv::BackgroundSubtractorKNN>, Ptr, BackgroundSubtractor, 0, "")
CVPY_TYPE(KalmanFilter, KalmanFilter, Ptr<cv::KalmanFilter>, Ptr, NoBase, pyopencv_cv_KalmanFilter_KalmanFilter, "")
CVPY_TYPE(DenseOpticalFlow, DenseOpticalFlow, Ptr<cv::DenseOpticalFlow>, Ptr, Algorithm, 0, "")
CVPY_TYPE(SparseOpticalFlow, SparseOpticalFlow, Ptr<cv::SparseOpticalFlow>, Ptr, Algorithm, 0, "")
CVPY_TYPE(FarnebackOpticalFlow, FarnebackOpticalFlow, Ptr<cv::FarnebackOpticalFlow>, Ptr, DenseOpticalFlow, 0, "")
CVPY_TYPE(VariationalRefinement, VariationalRefinement, Ptr<cv::VariationalRefinement>, Ptr, DenseOpticalFlow, 0, "")
CVPY_TYPE(DISOpticalFlow, DISOpticalFlow, Ptr<cv::DISOpticalFlow>, Ptr, DenseOpticalFlow, 0, "")
CVPY_TYPE(SparsePyrLKOpticalFlow, SparsePyrLKOpticalFlow, Ptr<cv::SparsePyrLKOpticalFlow>, Ptr, SparseOpticalFlow, 0, "")
CVPY_TYPE(Tracker, Tracker, Ptr<cv::Tracker>, Ptr, NoBase, 0, "")
CVPY_TYPE(TrackerMIL, TrackerMIL, Ptr<cv::TrackerMIL>, Ptr, Tracker, 0, "")
CVPY_TYPE(Params, TrackerMIL_Params, cv::TrackerMIL::Params, Params, NoBase, pyopencv_cv_TrackerMIL_Params_TrackerMIL_Params, ".TrackerMIL")
CVPY_TYPE(TrackerGOTURN, TrackerGOTURN, Ptr<cv::TrackerGOTURN>, Ptr, Tracker, 0, "")
CVPY_TYPE(Params, TrackerGOTURN_Params, cv::TrackerGOTURN::Params, Params, NoBase, pyopencv_cv_TrackerGOTURN_Params_TrackerGOTURN_Params, ".TrackerGOTURN")
CVPY_TYPE(TrackerDaSiamRPN, TrackerDaSiamRPN, Ptr<cv::TrackerDaSiamRPN>, Ptr, Tracker, 0, "")
CVPY_TYPE(Params, TrackerDaSiamRPN_Params, cv::TrackerDaSiamRPN::Params, Params, NoBase, pyopencv_cv_TrackerDaSiamRPN_Params_TrackerDaSiamRPN_Params, ".TrackerDaSiamRPN")
CVPY_TYPE(TrackerNano, TrackerNano, Ptr<cv::TrackerNano>, Ptr, Tracker, 0, "")
CVPY_TYPE(Params, TrackerNano_Params, cv::TrackerNano::Params, Params, NoBase, pyopencv_cv_TrackerNano_Params_TrackerNano_Params, ".TrackerNano")
CVPY_TYPE(TrackerVit, TrackerVit, Ptr<cv::TrackerVit>, Ptr, Tracker, 0, "")
CVPY_TYPE(Params, TrackerVit_Params, cv::TrackerVit::Params, Params, NoBase, pyopencv_cv_TrackerVit_Params_TrackerVit_Params, ".TrackerVit")
CVPY_TYPE(WeChatQRCode, wechat_qrcode_WeChatQRCode, Ptr<cv::wechat_qrcode::WeChatQRCode>, Ptr, NoBase, pyopencv_cv_wechat_qrcode_wechat_qrcode_WeChatQRCode_WeChatQRCode, ".wechat_qrcode")
CVPY_TYPE(FREAK, xfeatures2d_FREAK, Ptr<cv::xfeatures2d::FREAK>, Ptr, Feature2D, 0, ".xfeatures2d")
CVPY_TYPE(StarDetector, xfeatures2d_StarDetector, Ptr<cv::xfeatures2d::StarDetector>, Ptr, Feature2D, 0, ".xfeatures2d")
CVPY_TYPE(BriefDescriptorExtractor, xfeatures2d_BriefDescriptorExtractor, Ptr<cv::xfeatures2d::BriefDescriptorExtractor>, Ptr, Feature2D, 0, ".xfeatures2d")
CVPY_TYPE(LUCID, xfeatures2d_LUCID, Ptr<cv::xfeatures2d::LUCID>, Ptr, Feature2D, 0, ".xfeatures2d")
CVPY_TYPE(LATCH, xfeatures2d_LATCH, Ptr<cv::xfeatures2d::LATCH>, Ptr, Feature2D, 0, ".xfeatures2d")
CVPY_TYPE(BEBLID, xfeatures2d_BEBLID, Ptr<cv::xfeatures2d::BEBLID>, Ptr, Feature2D, 0, ".xfeatures2d")
CVPY_TYPE(TEBLID, xfeatures2d_TEBLID, Ptr<cv::xfeatures2d::TEBLID>, Ptr, Feature2D, 0, ".xfeatures2d")
CVPY_TYPE(DAISY, xfeatures2d_DAISY, Ptr<cv::xfeatures2d::DAISY>, Ptr, Feature2D, 0, ".xfeatures2d")
CVPY_TYPE(MSDDetector, xfeatures2d_MSDDetector, Ptr<cv::xfeatures2d::MSDDetector>, Ptr, Feature2D, 0, ".xfeatures2d")
CVPY_TYPE(VGG, xfeatures2d_VGG, Ptr<cv::xfeatures2d::VGG>, Ptr, Feature2D, 0, ".xfeatures2d")
CVPY_TYPE(BoostDesc, xfeatures2d_BoostDesc, Ptr<cv::xfeatures2d::BoostDesc>, Ptr, Feature2D, 0, ".xfeatures2d")
CVPY_TYPE(PCTSignatures, xfeatures2d_PCTSignatures, Ptr<cv::xfeatures2d::PCTSignatures>, Ptr, Algorithm, 0, ".xfeatures2d")
CVPY_TYPE(PCTSignaturesSQFD, xfeatures2d_PCTSignaturesSQFD, Ptr<cv::xfeatures2d::PCTSignaturesSQFD>, Ptr, Algorithm, 0, ".xfeatures2d")
CVPY_TYPE(HarrisLaplaceFeatureDetector, xfeatures2d_HarrisLaplaceFeatureDetector, Ptr<cv::xfeatures2d::HarrisLaplaceFeatureDetector>, Ptr, Feature2D, 0, ".xfeatures2d")
CVPY_TYPE(AffineFeature2D, xfeatures2d_AffineFeature2D, Ptr<cv::xfeatures2d::AffineFeature2D>, Ptr, Feature2D, 0, ".xfeatures2d")
CVPY_TYPE(TBMR, xfeatures2d_TBMR, Ptr<cv::xfeatures2d::TBMR>, Ptr, xfeatures2d_AffineFeature2D, 0, ".xfeatures2d")
CVPY_TYPE(SURF_CUDA, cuda_SURF_CUDA, Ptr<cv::cuda::SURF_CUDA>, Ptr, NoBase, 0, ".cuda")
CVPY_TYPE(SURF, xfeatures2d_SURF, Ptr<cv::xfeatures2d::SURF>, Ptr, Feature2D, 0, ".xfeatures2d")
CVPY_TYPE(DisparityFilter, ximgproc_DisparityFilter, Ptr<cv::ximgproc::DisparityFilter>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(DisparityWLSFilter, ximgproc_DisparityWLSFilter, Ptr<cv::ximgproc::DisparityWLSFilter>, Ptr, ximgproc_DisparityFilter, 0, ".ximgproc")
CVPY_TYPE(EdgeDrawing, ximgproc_EdgeDrawing, Ptr<cv::ximgproc::EdgeDrawing>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(Params, ximgproc_EdgeDrawing_Params, cv::ximgproc::EdgeDrawing::Params, Params, NoBase, pyopencv_cv_ximgproc_ximgproc_EdgeDrawing_Params_EdgeDrawing_Params, ".ximgproc.EdgeDrawing")
CVPY_TYPE(DTFilter, ximgproc_DTFilter, Ptr<cv::ximgproc::DTFilter>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(GuidedFilter, ximgproc_GuidedFilter, Ptr<cv::ximgproc::GuidedFilter>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(AdaptiveManifoldFilter, ximgproc_AdaptiveManifoldFilter, Ptr<cv::ximgproc::AdaptiveManifoldFilter>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(FastBilateralSolverFilter, ximgproc_FastBilateralSolverFilter, Ptr<cv::ximgproc::FastBilateralSolverFilter>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(FastGlobalSmootherFilter, ximgproc_FastGlobalSmootherFilter, Ptr<cv::ximgproc::FastGlobalSmootherFilter>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(EdgeBoxes, ximgproc_EdgeBoxes, Ptr<cv::ximgproc::EdgeBoxes>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(FastLineDetector, ximgproc_FastLineDetector, Ptr<cv::ximgproc::FastLineDetector>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(ContourFitting, ximgproc_ContourFitting, Ptr<cv::ximgproc::ContourFitting>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(SuperpixelLSC, ximgproc_SuperpixelLSC, Ptr<cv::ximgproc::SuperpixelLSC>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(RidgeDetectionFilter, ximgproc_RidgeDetectionFilter, Ptr<cv::ximgproc::RidgeDetectionFilter>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(ScanSegment, ximgproc_ScanSegment, Ptr<cv::ximgproc::ScanSegment>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(SuperpixelSEEDS, ximgproc_SuperpixelSEEDS, Ptr<cv::ximgproc::SuperpixelSEEDS>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(GraphSegmentation, ximgproc_segmentation_GraphSegmentation, Ptr<cv::ximgproc::segmentation::GraphSegmentation>, Ptr, Algorithm, 0, ".ximgproc.segmentation")
CVPY_TYPE(SelectiveSearchSegmentationStrategy, ximgproc_segmentation_SelectiveSearchSegmentationStrategy, Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy>, Ptr, Algorithm, 0, ".ximgproc.segmentation")
CVPY_TYPE(SelectiveSearchSegmentationStrategyColor, ximgproc_segmentation_SelectiveSearchSegmentationStrategyColor, Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyColor>, Ptr, ximgproc_segmentation_SelectiveSearchSegmentationStrategy, 0, ".ximgproc.segmentation")
CVPY_TYPE(SelectiveSearchSegmentationStrategySize, ximgproc_segmentation_SelectiveSearchSegmentationStrategySize, Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategySize>, Ptr, ximgproc_segmentation_SelectiveSearchSegmentationStrategy, 0, ".ximgproc.segmentation")
CVPY_TYPE(SelectiveSearchSegmentationStrategyTexture, ximgproc_segmentation_SelectiveSearchSegmentationStrategyTexture, Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyTexture>, Ptr, ximgproc_segmentation_SelectiveSearchSegmentationStrategy, 0, ".ximgproc.segmentation")
CVPY_TYPE(SelectiveSearchSegmentationStrategyFill, ximgproc_segmentation_SelectiveSearchSegmentationStrategyFill, Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyFill>, Ptr, ximgproc_segmentation_SelectiveSearchSegmentationStrategy, 0, ".ximgproc.segmentation")
CVPY_TYPE(SelectiveSearchSegmentationStrategyMultiple, ximgproc_segmentation_SelectiveSearchSegmentationStrategyMultiple, Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyMultiple>, Ptr, ximgproc_segmentation_SelectiveSearchSegmentationStrategy, 0, ".ximgproc.segmentation")
CVPY_TYPE(SelectiveSearchSegmentation, ximgproc_segmentation_SelectiveSearchSegmentation, Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentation>, Ptr, Algorithm, 0, ".ximgproc.segmentation")
CVPY_TYPE(SuperpixelSLIC, ximgproc_SuperpixelSLIC, Ptr<cv::ximgproc::SuperpixelSLIC>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(SparseMatchInterpolator, ximgproc_SparseMatchInterpolator, Ptr<cv::ximgproc::SparseMatchInterpolator>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(EdgeAwareInterpolator, ximgproc_EdgeAwareInterpolator, Ptr<cv::ximgproc::EdgeAwareInterpolator>, Ptr, ximgproc_SparseMatchInterpolator, 0, ".ximgproc")
CVPY_TYPE(RICInterpolator, ximgproc_RICInterpolator, Ptr<cv::ximgproc::RICInterpolator>, Ptr, ximgproc_SparseMatchInterpolator, 0, ".ximgproc")
CVPY_TYPE(RFFeatureGetter, ximgproc_RFFeatureGetter, Ptr<cv::ximgproc::RFFeatureGetter>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(StructuredEdgeDetection, ximgproc_StructuredEdgeDetection, Ptr<cv::ximgproc::StructuredEdgeDetection>, Ptr, Algorithm, 0, ".ximgproc")
CVPY_TYPE(EstimateParameters, aruco_EstimateParameters, cv::aruco::EstimateParameters, EstimateParameters, NoBase, pyopencv_cv_aruco_aruco_EstimateParameters_EstimateParameters, ".aruco")
CVPY_TYPE(BackgroundSubtractorMOG, bgsegm_BackgroundSubtractorMOG, Ptr<cv::bgsegm::BackgroundSubtractorMOG>, Ptr, BackgroundSubtractor, 0, ".bgsegm")
CVPY_TYPE(BackgroundSubtractorGMG, bgsegm_BackgroundSubtractorGMG, Ptr<cv::bgsegm::BackgroundSubtractorGMG>, Ptr, BackgroundSubtractor, 0, ".bgsegm")
CVPY_TYPE(BackgroundSubtractorCNT, bgsegm_BackgroundSubtractorCNT, Ptr<cv::bgsegm::BackgroundSubtractorCNT>, Ptr, BackgroundSubtractor, 0, ".bgsegm")
CVPY_TYPE(BackgroundSubtractorGSOC, bgsegm_BackgroundSubtractorGSOC, Ptr<cv::bgsegm::BackgroundSubtractorGSOC>, Ptr, BackgroundSubtractor, 0, ".bgsegm")
CVPY_TYPE(BackgroundSubtractorLSBP, bgsegm_BackgroundSubtractorLSBP, Ptr<cv::bgsegm::BackgroundSubtractorLSBP>, Ptr, BackgroundSubtractor, 0, ".bgsegm")
CVPY_TYPE(BackgroundSubtractorLSBPDesc, bgsegm_BackgroundSubtractorLSBPDesc, Ptr<cv::bgsegm::BackgroundSubtractorLSBPDesc>, Ptr, NoBase, 0, ".bgsegm")
CVPY_TYPE(SyntheticSequenceGenerator, bgsegm_SyntheticSequenceGenerator, Ptr<cv::bgsegm::SyntheticSequenceGenerator>, Ptr, Algorithm, pyopencv_cv_bgsegm_bgsegm_SyntheticSequenceGenerator_SyntheticSequenceGenerator, ".bgsegm")
CVPY_TYPE(Retina, bioinspired_Retina, Ptr<cv::bioinspired::Retina>, Ptr, Algorithm, 0, ".bioinspired")
CVPY_TYPE(RetinaFastToneMapping, bioinspired_RetinaFastToneMapping, Ptr<cv::bioinspired::RetinaFastToneMapping>, Ptr, Algorithm, 0, ".bioinspired")
CVPY_TYPE(TransientAreasSegmentationModule, bioinspired_TransientAreasSegmentationModule, Ptr<cv::bioinspired::TransientAreasSegmentationModule>, Ptr, Algorithm, 0, ".bioinspired")
CVPY_TYPE(BackgroundSubtractorMOG, cuda_BackgroundSubtractorMOG, Ptr<cv::cuda::BackgroundSubtractorMOG>, Ptr, BackgroundSubtractor, 0, ".cuda")
CVPY_TYPE(BackgroundSubtractorMOG2, cuda_BackgroundSubtractorMOG2, Ptr<cv::cuda::BackgroundSubtractorMOG2>, Ptr, BackgroundSubtractorMOG2, 0, ".cuda")
CVPY_TYPE(HOG, cuda_HOG, Ptr<cv::cuda::HOG>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(CascadeClassifier, cuda_CascadeClassifier, Ptr<cv::cuda::CascadeClassifier>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(DPMDetector, dpm_DPMDetector, Ptr<cv::dpm::DPMDetector>, Ptr, NoBase, 0, ".dpm")
CVPY_TYPE(ObjectDetection, dpm_DPMDetector_ObjectDetection, Ptr<cv::dpm::DPMDetector::ObjectDetection>, Ptr, NoBase, 0, ".dpm.DPMDetector")
CVPY_TYPE(FaceRecognizer, face_FaceRecognizer, Ptr<cv::face::FaceRecognizer>, Ptr, Algorithm, 0, ".face")
CVPY_TYPE(BIF, face_BIF, Ptr<cv::face::BIF>, Ptr, Algorithm, 0, ".face")
CVPY_TYPE(Facemark, face_Facemark, Ptr<cv::face::Facemark>, Ptr, Algorithm, 0, ".face")
CVPY_TYPE(FacemarkKazemi, face_FacemarkKazemi, Ptr<cv::face::FacemarkKazemi>, Ptr, face_Facemark, 0, ".face")
CVPY_TYPE(FacemarkTrain, face_FacemarkTrain, Ptr<cv::face::FacemarkTrain>, Ptr, face_Facemark, 0, ".face")
CVPY_TYPE(FacemarkAAM, face_FacemarkAAM, Ptr<cv::face::FacemarkAAM>, Ptr, face_FacemarkTrain, 0, ".face")
CVPY_TYPE(FacemarkLBF, face_FacemarkLBF, Ptr<cv::face::FacemarkLBF>, Ptr, face_FacemarkTrain, 0, ".face")
CVPY_TYPE(BasicFaceRecognizer, face_BasicFaceRecognizer, Ptr<cv::face::BasicFaceRecognizer>, Ptr, face_FaceRecognizer, 0, ".face")
CVPY_TYPE(EigenFaceRecognizer, face_EigenFaceRecognizer, Ptr<cv::face::EigenFaceRecognizer>, Ptr, face_BasicFaceRecognizer, 0, ".face")
CVPY_TYPE(FisherFaceRecognizer, face_FisherFaceRecognizer, Ptr<cv::face::FisherFaceRecognizer>, Ptr, face_BasicFaceRecognizer, 0, ".face")
CVPY_TYPE(LBPHFaceRecognizer, face_LBPHFaceRecognizer, Ptr<cv::face::LBPHFaceRecognizer>, Ptr, face_FaceRecognizer, 0, ".face")
CVPY_TYPE(MACE, face_MACE, Ptr<cv::face::MACE>, Ptr, Algorithm, 0, ".face")
CVPY_TYPE(PredictCollector, face_PredictCollector, Ptr<cv::face::PredictCollector>, Ptr, NoBase, 0, ".face")
CVPY_TYPE(StandardCollector, face_StandardCollector, Ptr<cv::face::StandardCollector>, Ptr, face_PredictCollector, 0, ".face")
CVPY_TYPE(GArrayDesc, GArrayDesc, cv::GArrayDesc, GArrayDesc, NoBase, 0, "")
CVPY_TYPE(GComputation, GComputation, Ptr<cv::GComputation>, Ptr, NoBase, pyopencv_cv_GComputation_GComputation, "")
CVPY_TYPE(GFrame, GFrame, cv::GFrame, GFrame, NoBase, pyopencv_cv_GFrame_GFrame, "")
CVPY_TYPE(GKernelPackage, GKernelPackage, cv::GKernelPackage, GKernelPackage, NoBase, 0, "")
CVPY_TYPE(GMat, GMat, cv::GMat, GMat, NoBase, pyopencv_cv_GMat_GMat, "")
CVPY_TYPE(GMatDesc, GMatDesc, cv::GMatDesc, GMatDesc, NoBase, pyopencv_cv_GMatDesc_GMatDesc, "")
CVPY_TYPE(GOpaqueDesc, GOpaqueDesc, cv::GOpaqueDesc, GOpaqueDesc, NoBase, 0, "")
CVPY_TYPE(GScalar, GScalar, cv::GScalar, GScalar, NoBase, pyopencv_cv_GScalar_GScalar, "")
CVPY_TYPE(GScalarDesc, GScalarDesc, cv::GScalarDesc, GScalarDesc, NoBase, 0, "")
CVPY_TYPE(GStreamingCompiled, GStreamingCompiled, cv::GStreamingCompiled, GStreamingCompiled, NoBase, pyopencv_cv_GStreamingCompiled_GStreamingCompiled, "")
CVPY_TYPE(queue_capacity, gapi_streaming_queue_capacity, cv::gapi::streaming::queue_capacity, queue_capacity, NoBase, pyopencv_cv_gapi_streaming_gapi_streaming_queue_capacity_queue_capacity, ".gapi.streaming")
CVPY_TYPE(GNetParam, gapi_GNetParam, cv::gapi::GNetParam, GNetParam, NoBase, 0, ".gapi")
CVPY_TYPE(GNetPackage, gapi_GNetPackage, cv::gapi::GNetPackage, GNetPackage, NoBase, pyopencv_cv_gapi_gapi_GNetPackage_GNetPackage, ".gapi")
CVPY_TYPE(PyParams, gapi_ie_PyParams, cv::gapi::ie::PyParams, PyParams, NoBase, pyopencv_cv_gapi_ie_gapi_ie_PyParams_PyParams, ".gapi.ie")
CVPY_TYPE(PyParams, gapi_onnx_PyParams, cv::gapi::onnx::PyParams, PyParams, NoBase, pyopencv_cv_gapi_onnx_gapi_onnx_PyParams_PyParams, ".gapi.onnx")
CVPY_TYPE(PyParams, gapi_ov_PyParams, cv::gapi::ov::PyParams, PyParams, NoBase, pyopencv_cv_gapi_ov_gapi_ov_PyParams_PyParams, ".gapi.ov")
CVPY_TYPE(CoreML, gapi_onnx_ep_CoreML, cv::gapi::onnx::ep::CoreML, CoreML, NoBase, pyopencv_cv_gapi_onnx_ep_gapi_onnx_ep_CoreML_CoreML, ".gapi.onnx.ep")
CVPY_TYPE(CUDA, gapi_onnx_ep_CUDA, cv::gapi::onnx::ep::CUDA, CUDA, NoBase, pyopencv_cv_gapi_onnx_ep_gapi_onnx_ep_CUDA_CUDA, ".gapi.onnx.ep")
CVPY_TYPE(TensorRT, gapi_onnx_ep_TensorRT, cv::gapi::onnx::ep::TensorRT, TensorRT, NoBase, pyopencv_cv_gapi_onnx_ep_gapi_onnx_ep_TensorRT_TensorRT, ".gapi.onnx.ep")
CVPY_TYPE(OpenVINO, gapi_onnx_ep_OpenVINO, cv::gapi::onnx::ep::OpenVINO, OpenVINO, NoBase, pyopencv_cv_gapi_onnx_ep_gapi_onnx_ep_OpenVINO_OpenVINO, ".gapi.onnx.ep")
CVPY_TYPE(DirectML, gapi_onnx_ep_DirectML, cv::gapi::onnx::ep::DirectML, DirectML, NoBase, pyopencv_cv_gapi_onnx_ep_gapi_onnx_ep_DirectML_DirectML, ".gapi.onnx.ep")
CVPY_TYPE(ObjectTrackerParams, gapi_ot_ObjectTrackerParams, cv::gapi::ot::ObjectTrackerParams, ObjectTrackerParams, NoBase, 0, ".gapi.ot")
CVPY_TYPE(Text, gapi_wip_draw_Text, cv::gapi::wip::draw::Text, Text, NoBase, pyopencv_cv_gapi_wip_draw_gapi_wip_draw_Text_Text, ".gapi.wip.draw")
CVPY_TYPE(Rect, gapi_wip_draw_Rect, cv::gapi::wip::draw::Rect, Rect, NoBase, pyopencv_cv_gapi_wip_draw_gapi_wip_draw_Rect_Rect, ".gapi.wip.draw")
CVPY_TYPE(Circle, gapi_wip_draw_Circle, cv::gapi::wip::draw::Circle, Circle, NoBase, pyopencv_cv_gapi_wip_draw_gapi_wip_draw_Circle_Circle, ".gapi.wip.draw")
CVPY_TYPE(Line, gapi_wip_draw_Line, cv::gapi::wip::draw::Line, Line, NoBase, pyopencv_cv_gapi_wip_draw_gapi_wip_draw_Line_Line, ".gapi.wip.draw")
CVPY_TYPE(Mosaic, gapi_wip_draw_Mosaic, cv::gapi::wip::draw::Mosaic, Mosaic, NoBase, pyopencv_cv_gapi_wip_draw_gapi_wip_draw_Mosaic_Mosaic, ".gapi.wip.draw")
CVPY_TYPE(Image, gapi_wip_draw_Image, cv::gapi::wip::draw::Image, Image, NoBase, pyopencv_cv_gapi_wip_draw_gapi_wip_draw_Image_Image, ".gapi.wip.draw")
CVPY_TYPE(Poly, gapi_wip_draw_Poly, cv::gapi::wip::draw::Poly, Poly, NoBase, pyopencv_cv_gapi_wip_draw_gapi_wip_draw_Poly_Poly, ".gapi.wip.draw")
CVPY_TYPE(GStreamerPipeline, gapi_wip_gst_GStreamerPipeline, Ptr<cv::gapi::wip::gst::GStreamerPipeline>, Ptr, NoBase, pyopencv_cv_gapi_wip_gst_gapi_wip_gst_GStreamerPipeline_GStreamerPipeline, ".gapi.wip.gst")
CVPY_TYPE(GOpaqueT, GOpaqueT, cv::GOpaqueT, GOpaqueT, NoBase, pyopencv_cv_GOpaqueT_GOpaqueT, "")
CVPY_TYPE(GArrayT, GArrayT, cv::GArrayT, GArrayT, NoBase, pyopencv_cv_GArrayT_GArrayT, "")
CVPY_TYPE(GOutputs, gapi_wip_GOutputs, cv::gapi::wip::GOutputs, GOutputs, NoBase, 0, ".gapi.wip")
CVPY_TYPE(GCompileArg, GCompileArg, cv::GCompileArg, GCompileArg, NoBase, pyopencv_cv_GCompileArg_GCompileArg, "")
CVPY_TYPE(GInferInputs, GInferInputs, cv::GInferInputs, GInferInputs, NoBase, pyopencv_cv_GInferInputs_GInferInputs, "")
CVPY_TYPE(GInferListInputs, GInferListInputs, cv::GInferListInputs, GInferListInputs, NoBase, pyopencv_cv_GInferListInputs_GInferListInputs, "")
CVPY_TYPE(GInferOutputs, GInferOutputs, cv::GInferOutputs, GInferOutputs, NoBase, pyopencv_cv_GInferOutputs_GInferOutputs, "")
CVPY_TYPE(GInferListOutputs, GInferListOutputs, cv::GInferListOutputs, GInferListOutputs, NoBase, pyopencv_cv_GInferListOutputs_GInferListOutputs, "")
CVPY_TYPE(IStreamSource, gapi_wip_IStreamSource, Ptr<cv::gapi::wip::IStreamSource>, Ptr, NoBase, 0, ".gapi.wip")
CVPY_TYPE(DualTVL1OpticalFlow, optflow_DualTVL1OpticalFlow, Ptr<cv::optflow::DualTVL1OpticalFlow>, Ptr, DenseOpticalFlow, 0, ".optflow")
CVPY_TYPE(PCAPrior, optflow_PCAPrior, Ptr<cv::optflow::PCAPrior>, Ptr, NoBase, 0, ".optflow")
CVPY_TYPE(OpticalFlowPCAFlow, optflow_OpticalFlowPCAFlow, Ptr<cv::optflow::OpticalFlowPCAFlow>, Ptr, DenseOpticalFlow, 0, ".optflow")
CVPY_TYPE(RLOFOpticalFlowParameter, optflow_RLOFOpticalFlowParameter, Ptr<cv::optflow::RLOFOpticalFlowParameter>, Ptr, NoBase, 0, ".optflow")
CVPY_TYPE(DenseRLOFOpticalFlow, optflow_DenseRLOFOpticalFlow, Ptr<cv::optflow::DenseRLOFOpticalFlow>, Ptr, DenseOpticalFlow, 0, ".optflow")
CVPY_TYPE(SparseRLOFOpticalFlow, optflow_SparseRLOFOpticalFlow, Ptr<cv::optflow::SparseRLOFOpticalFlow>, Ptr, SparseOpticalFlow, 0, ".optflow")
CVPY_TYPE(GPCPatchDescriptor, optflow_GPCPatchDescriptor, Ptr<cv::optflow::GPCPatchDescriptor>, Ptr, NoBase, 0, ".optflow")
CVPY_TYPE(GPCPatchSample, optflow_GPCPatchSample, Ptr<cv::optflow::GPCPatchSample>, Ptr, NoBase, 0, ".optflow")
CVPY_TYPE(GPCTrainingSamples, optflow_GPCTrainingSamples, Ptr<cv::optflow::GPCTrainingSamples>, Ptr, NoBase, 0, ".optflow")
CVPY_TYPE(GPCTree, optflow_GPCTree, Ptr<cv::optflow::GPCTree>, Ptr, Algorithm, 0, ".optflow")
CVPY_TYPE(GPCDetails, optflow_GPCDetails, Ptr<cv::optflow::GPCDetails>, Ptr, NoBase, 0, ".optflow")
CVPY_TYPE(Stitcher, Stitcher, Ptr<cv::Stitcher>, Ptr, NoBase, 0, "")
CVPY_TYPE(PyRotationWarper, PyRotationWarper, Ptr<cv::PyRotationWarper>, Ptr, NoBase, pyopencv_cv_PyRotationWarper_PyRotationWarper, "")
CVPY_TYPE(WarperCreator, WarperCreator, Ptr<cv::WarperCreator>, Ptr, NoBase, 0, "")
CVPY_TYPE(Blender, detail_Blender, Ptr<cv::detail::Blender>, Ptr, NoBase, 0, ".detail")
CVPY_TYPE(FeatherBlender, detail_FeatherBlender, Ptr<cv::detail::FeatherBlender>, Ptr, detail_Blender, pyopencv_cv_detail_detail_FeatherBlender_FeatherBlender, ".detail")
CVPY_TYPE(MultiBandBlender, detail_MultiBandBlender, Ptr<cv::detail::MultiBandBlender>, Ptr, detail_Blender, pyopencv_cv_detail_detail_MultiBandBlender_MultiBandBlender, ".detail")
CVPY_TYPE(CameraParams, detail_CameraParams, cv::detail::CameraParams, CameraParams, NoBase, 0, ".detail")
CVPY_TYPE(ExposureCompensator, detail_ExposureCompensator, Ptr<cv::detail::ExposureCompensator>, Ptr, NoBase, 0, ".detail")
CVPY_TYPE(NoExposureCompensator, detail_NoExposureCompensator, Ptr<cv::detail::NoExposureCompensator>, Ptr, detail_ExposureCompensator, 0, ".detail")
CVPY_TYPE(GainCompensator, detail_GainCompensator, Ptr<cv::detail::GainCompensator>, Ptr, detail_ExposureCompensator, pyopencv_cv_detail_detail_GainCompensator_GainCompensator, ".detail")
CVPY_TYPE(ChannelsCompensator, detail_ChannelsCompensator, Ptr<cv::detail::ChannelsCompensator>, Ptr, detail_ExposureCompensator, pyopencv_cv_detail_detail_ChannelsCompensator_ChannelsCompensator, ".detail")
CVPY_TYPE(BlocksCompensator, detail_BlocksCompensator, Ptr<cv::detail::BlocksCompensator>, Ptr, detail_ExposureCompensator, 0, ".detail")
CVPY_TYPE(BlocksGainCompensator, detail_BlocksGainCompensator, Ptr<cv::detail::BlocksGainCompensator>, Ptr, detail_BlocksCompensator, pyopencv_cv_detail_detail_BlocksGainCompensator_BlocksGainCompensator, ".detail")
CVPY_TYPE(BlocksChannelsCompensator, detail_BlocksChannelsCompensator, Ptr<cv::detail::BlocksChannelsCompensator>, Ptr, detail_BlocksCompensator, pyopencv_cv_detail_detail_BlocksChannelsCompensator_BlocksChannelsCompensator, ".detail")
CVPY_TYPE(ImageFeatures, detail_ImageFeatures, cv::detail::ImageFeatures, ImageFeatures, NoBase, 0, ".detail")
CVPY_TYPE(MatchesInfo, detail_MatchesInfo, cv::detail::MatchesInfo, MatchesInfo, NoBase, 0, ".detail")
CVPY_TYPE(FeaturesMatcher, detail_FeaturesMatcher, Ptr<cv::detail::FeaturesMatcher>, Ptr, NoBase, 0, ".detail")
CVPY_TYPE(BestOf2NearestMatcher, detail_BestOf2NearestMatcher, Ptr<cv::detail::BestOf2NearestMatcher>, Ptr, detail_FeaturesMatcher, pyopencv_cv_detail_detail_BestOf2NearestMatcher_BestOf2NearestMatcher, ".detail")
CVPY_TYPE(BestOf2NearestRangeMatcher, detail_BestOf2NearestRangeMatcher, Ptr<cv::detail::BestOf2NearestRangeMatcher>, Ptr, detail_BestOf2NearestMatcher, pyopencv_cv_detail_detail_BestOf2NearestRangeMatcher_BestOf2NearestRangeMatcher, ".detail")
CVPY_TYPE(AffineBestOf2NearestMatcher, detail_AffineBestOf2NearestMatcher, Ptr<cv::detail::AffineBestOf2NearestMatcher>, Ptr, detail_BestOf2NearestMatcher, pyopencv_cv_detail_detail_AffineBestOf2NearestMatcher_AffineBestOf2NearestMatcher, ".detail")
CVPY_TYPE(Estimator, detail_Estimator, Ptr<cv::detail::Estimator>, Ptr, NoBase, 0, ".detail")
CVPY_TYPE(HomographyBasedEstimator, detail_HomographyBasedEstimator, Ptr<cv::detail::HomographyBasedEstimator>, Ptr, detail_Estimator, pyopencv_cv_detail_detail_HomographyBasedEstimator_HomographyBasedEstimator, ".detail")
CVPY_TYPE(AffineBasedEstimator, detail_AffineBasedEstimator, Ptr<cv::detail::AffineBasedEstimator>, Ptr, detail_Estimator, pyopencv_cv_detail_detail_AffineBasedEstimator_AffineBasedEstimator, ".detail")
CVPY_TYPE(BundleAdjusterBase, detail_BundleAdjusterBase, Ptr<cv::detail::BundleAdjusterBase>, Ptr, detail_Estimator, 0, ".detail")
CVPY_TYPE(NoBundleAdjuster, detail_NoBundleAdjuster, Ptr<cv::detail::NoBundleAdjuster>, Ptr, detail_BundleAdjusterBase, pyopencv_cv_detail_detail_NoBundleAdjuster_NoBundleAdjuster, ".detail")
CVPY_TYPE(BundleAdjusterReproj, detail_BundleAdjusterReproj, Ptr<cv::detail::BundleAdjusterReproj>, Ptr, detail_BundleAdjusterBase, pyopencv_cv_detail_detail_BundleAdjusterReproj_BundleAdjusterReproj, ".detail")
CVPY_TYPE(BundleAdjusterRay, detail_BundleAdjusterRay, Ptr<cv::detail::BundleAdjusterRay>, Ptr, detail_BundleAdjusterBase, pyopencv_cv_detail_detail_BundleAdjusterRay_BundleAdjusterRay, ".detail")
CVPY_TYPE(BundleAdjusterAffine, detail_BundleAdjusterAffine, Ptr<cv::detail::BundleAdjusterAffine>, Ptr, detail_BundleAdjusterBase, pyopencv_cv_detail_detail_BundleAdjusterAffine_BundleAdjusterAffine, ".detail")
CVPY_TYPE(BundleAdjusterAffinePartial, detail_BundleAdjusterAffinePartial, Ptr<cv::detail::BundleAdjusterAffinePartial>, Ptr, detail_BundleAdjusterBase, pyopencv_cv_detail_detail_BundleAdjusterAffinePartial_BundleAdjusterAffinePartial, ".detail")
CVPY_TYPE(SeamFinder, detail_SeamFinder, Ptr<cv::detail::SeamFinder>, Ptr, NoBase, 0, ".detail")
CVPY_TYPE(NoSeamFinder, detail_NoSeamFinder, Ptr<cv::detail::NoSeamFinder>, Ptr, detail_SeamFinder, 0, ".detail")
CVPY_TYPE(PairwiseSeamFinder, detail_PairwiseSeamFinder, Ptr<cv::detail::PairwiseSeamFinder>, Ptr, detail_SeamFinder, 0, ".detail")
CVPY_TYPE(VoronoiSeamFinder, detail_VoronoiSeamFinder, Ptr<cv::detail::VoronoiSeamFinder>, Ptr, detail_PairwiseSeamFinder, 0, ".detail")
CVPY_TYPE(DpSeamFinder, detail_DpSeamFinder, Ptr<cv::detail::DpSeamFinder>, Ptr, detail_SeamFinder, pyopencv_cv_detail_detail_DpSeamFinder_DpSeamFinder, ".detail")
CVPY_TYPE(GraphCutSeamFinder, detail_GraphCutSeamFinder, Ptr<cv::detail::GraphCutSeamFinder>, Ptr, NoBase, pyopencv_cv_detail_detail_GraphCutSeamFinder_GraphCutSeamFinder, ".detail")
CVPY_TYPE(Timelapser, detail_Timelapser, Ptr<cv::detail::Timelapser>, Ptr, NoBase, 0, ".detail")
CVPY_TYPE(TimelapserCrop, detail_TimelapserCrop, Ptr<cv::detail::TimelapserCrop>, Ptr, detail_Timelapser, 0, ".detail")
CVPY_TYPE(ProjectorBase, detail_ProjectorBase, cv::detail::ProjectorBase, ProjectorBase, NoBase, 0, ".detail")
CVPY_TYPE(SphericalProjector, detail_SphericalProjector, cv::detail::SphericalProjector, SphericalProjector, detail_ProjectorBase, 0, ".detail")
CVPY_TYPE(TrackerCSRT, TrackerCSRT, Ptr<cv::TrackerCSRT>, Ptr, Tracker, 0, "")
CVPY_TYPE(Params, TrackerCSRT_Params, cv::TrackerCSRT::Params, Params, NoBase, pyopencv_cv_TrackerCSRT_Params_TrackerCSRT_Params, ".TrackerCSRT")
CVPY_TYPE(TrackerKCF, TrackerKCF, Ptr<cv::TrackerKCF>, Ptr, Tracker, 0, "")
CVPY_TYPE(Params, TrackerKCF_Params, cv::TrackerKCF::Params, Params, NoBase, pyopencv_cv_TrackerKCF_Params_TrackerKCF_Params, ".TrackerKCF")
CVPY_TYPE(Tracker, legacy_Tracker, Ptr<cv::legacy::Tracker>, Ptr, Algorithm, 0, ".legacy")
CVPY_TYPE(TrackerMIL, legacy_TrackerMIL, Ptr<cv::legacy::TrackerMIL>, Ptr, legacy_Tracker, 0, ".legacy")
CVPY_TYPE(TrackerBoosting, legacy_TrackerBoosting, Ptr<cv::legacy::TrackerBoosting>, Ptr, legacy_Tracker, 0, ".legacy")
CVPY_TYPE(TrackerMedianFlow, legacy_TrackerMedianFlow, Ptr<cv::legacy::TrackerMedianFlow>, Ptr, legacy_Tracker, 0, ".legacy")
CVPY_TYPE(TrackerTLD, legacy_TrackerTLD, Ptr<cv::legacy::TrackerTLD>, Ptr, legacy_Tracker, 0, ".legacy")
CVPY_TYPE(TrackerKCF, legacy_TrackerKCF, Ptr<cv::legacy::TrackerKCF>, Ptr, legacy_Tracker, 0, ".legacy")
CVPY_TYPE(TrackerMOSSE, legacy_TrackerMOSSE, Ptr<cv::legacy::TrackerMOSSE>, Ptr, legacy_Tracker, 0, ".legacy")
CVPY_TYPE(MultiTracker, legacy_MultiTracker, Ptr<cv::legacy::MultiTracker>, Ptr, Algorithm, pyopencv_cv_legacy_legacy_MultiTracker_MultiTracker, ".legacy")
CVPY_TYPE(TrackerCSRT, legacy_TrackerCSRT, Ptr<cv::legacy::TrackerCSRT>, Ptr, legacy_Tracker, 0, ".legacy")
CVPY_TYPE(DenseOpticalFlow, cuda_DenseOpticalFlow, Ptr<cv::cuda::DenseOpticalFlow>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(SparseOpticalFlow, cuda_SparseOpticalFlow, Ptr<cv::cuda::SparseOpticalFlow>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(NvidiaHWOpticalFlow, cuda_NvidiaHWOpticalFlow, Ptr<cv::cuda::NvidiaHWOpticalFlow>, Ptr, Algorithm, 0, ".cuda")
CVPY_TYPE(BroxOpticalFlow, cuda_BroxOpticalFlow, Ptr<cv::cuda::BroxOpticalFlow>, Ptr, cuda_DenseOpticalFlow, 0, ".cuda")
CVPY_TYPE(SparsePyrLKOpticalFlow, cuda_SparsePyrLKOpticalFlow, Ptr<cv::cuda::SparsePyrLKOpticalFlow>, Ptr, cuda_SparseOpticalFlow, 0, ".cuda")
CVPY_TYPE(DensePyrLKOpticalFlow, cuda_DensePyrLKOpticalFlow, Ptr<cv::cuda::DensePyrLKOpticalFlow>, Ptr, cuda_DenseOpticalFlow, 0, ".cuda")
CVPY_TYPE(FarnebackOpticalFlow, cuda_FarnebackOpticalFlow, Ptr<cv::cuda::FarnebackOpticalFlow>, Ptr, cuda_DenseOpticalFlow, 0, ".cuda")
CVPY_TYPE(OpticalFlowDual_TVL1, cuda_OpticalFlowDual_TVL1, Ptr<cv::cuda::OpticalFlowDual_TVL1>, Ptr, cuda_DenseOpticalFlow, 0, ".cuda")
CVPY_TYPE(NvidiaOpticalFlow_1_0, cuda_NvidiaOpticalFlow_1_0, Ptr<cv::cuda::NvidiaOpticalFlow_1_0>, Ptr, cuda_NvidiaHWOpticalFlow, 0, ".cuda")
CVPY_TYPE(NvidiaOpticalFlow_2_0, cuda_NvidiaOpticalFlow_2_0, Ptr<cv::cuda::NvidiaOpticalFlow_2_0>, Ptr, cuda_NvidiaHWOpticalFlow, 0, ".cuda")
CVPY_TYPE(MatchQuasiDense, stereo_MatchQuasiDense, cv::stereo::MatchQuasiDense, MatchQuasiDense, NoBase, pyopencv_cv_stereo_stereo_MatchQuasiDense_MatchQuasiDense, ".stereo")
CVPY_TYPE(PropagationParameters, stereo_PropagationParameters, cv::stereo::PropagationParameters, PropagationParameters, NoBase, 0, ".stereo")
CVPY_TYPE(QuasiDenseStereo, stereo_QuasiDenseStereo, Ptr<cv::stereo::QuasiDenseStereo>, Ptr, NoBase, 0, ".stereo")
CVPY_TYPE(ImgHashBase, img_hash_ImgHashBase, Ptr<cv::img_hash::ImgHashBase>, Ptr, Algorithm, 0, ".img_hash")
CVPY_TYPE(AverageHash, img_hash_AverageHash, Ptr<cv::img_hash::AverageHash>, Ptr, img_hash_ImgHashBase, 0, ".img_hash")
CVPY_TYPE(BlockMeanHash, img_hash_BlockMeanHash, Ptr<cv::img_hash::BlockMeanHash>, Ptr, img_hash_ImgHashBase, 0, ".img_hash")
CVPY_TYPE(ColorMomentHash, img_hash_ColorMomentHash, Ptr<cv::img_hash::ColorMomentHash>, Ptr, img_hash_ImgHashBase, 0, ".img_hash")
CVPY_TYPE(MarrHildrethHash, img_hash_MarrHildrethHash, Ptr<cv::img_hash::MarrHildrethHash>, Ptr, img_hash_ImgHashBase, 0, ".img_hash")
CVPY_TYPE(PHash, img_hash_PHash, Ptr<cv::img_hash::PHash>, Ptr, img_hash_ImgHashBase, 0, ".img_hash")
CVPY_TYPE(RadialVarianceHash, img_hash_RadialVarianceHash, Ptr<cv::img_hash::RadialVarianceHash>, Ptr, img_hash_ImgHashBase, 0, ".img_hash")
