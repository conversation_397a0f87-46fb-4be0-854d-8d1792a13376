// This file is auto-generated. Do not edit!

#include "opencv2/core/ocl.hpp"
#include "opencv2/core/ocl_genbase.hpp"
#include "opencv2/core/opencl/ocl_defs.hpp"

#ifdef HAVE_OPENCL

namespace cv
{
namespace ocl
{
namespace video
{

extern struct cv::ocl::internal::ProgramEntry bgfg_knn_oclsrc;
extern struct cv::ocl::internal::ProgramEntry bgfg_mog2_oclsrc;
extern struct cv::ocl::internal::ProgramEntry dis_flow_oclsrc;
extern struct cv::ocl::internal::ProgramEntry optical_flow_farneback_oclsrc;
extern struct cv::ocl::internal::ProgramEntry pyrlk_oclsrc;

}}}
#endif
