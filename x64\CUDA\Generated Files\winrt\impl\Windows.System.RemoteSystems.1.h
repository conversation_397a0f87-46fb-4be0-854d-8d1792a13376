// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_System_RemoteSystems_1_H
#define WINRT_Windows_System_RemoteSystems_1_H
#include "winrt/impl/Windows.System.RemoteSystems.0.h"
WINRT_EXPORT namespace winrt::Windows::System::RemoteSystems
{
    struct WINRT_IMPL_EMPTY_BASES IKnownRemoteSystemCapabilitiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownRemoteSystemCapabilitiesStatics>
    {
        IKnownRemoteSystemCapabilitiesStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownRemoteSystemCapabilitiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystem>
    {
        IRemoteSystem(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystem2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystem2>
    {
        IRemoteSystem2(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystem2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystem3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystem3>
    {
        IRemoteSystem3(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystem3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystem4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystem4>
    {
        IRemoteSystem4(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystem4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystem5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystem5>
    {
        IRemoteSystem5(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystem5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystem6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystem6>
    {
        IRemoteSystem6(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystem6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemAddedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemAddedEventArgs>
    {
        IRemoteSystemAddedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemAddedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemApp :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemApp>
    {
        IRemoteSystemApp(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemApp(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemApp2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemApp2>
    {
        IRemoteSystemApp2(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemApp2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemAppRegistration :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemAppRegistration>
    {
        IRemoteSystemAppRegistration(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemAppRegistration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemAppRegistrationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemAppRegistrationStatics>
    {
        IRemoteSystemAppRegistrationStatics(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemAppRegistrationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemAuthorizationKindFilter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemAuthorizationKindFilter>
    {
        IRemoteSystemAuthorizationKindFilter(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemAuthorizationKindFilter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemAuthorizationKindFilterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemAuthorizationKindFilterFactory>
    {
        IRemoteSystemAuthorizationKindFilterFactory(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemAuthorizationKindFilterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemConnectionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemConnectionInfo>
    {
        IRemoteSystemConnectionInfo(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemConnectionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemConnectionInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemConnectionInfoStatics>
    {
        IRemoteSystemConnectionInfoStatics(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemConnectionInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemConnectionRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemConnectionRequest>
    {
        IRemoteSystemConnectionRequest(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemConnectionRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemConnectionRequest2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemConnectionRequest2>
    {
        IRemoteSystemConnectionRequest2(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemConnectionRequest2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemConnectionRequest3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemConnectionRequest3>
    {
        IRemoteSystemConnectionRequest3(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemConnectionRequest3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemConnectionRequestFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemConnectionRequestFactory>
    {
        IRemoteSystemConnectionRequestFactory(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemConnectionRequestFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemConnectionRequestStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemConnectionRequestStatics>
    {
        IRemoteSystemConnectionRequestStatics(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemConnectionRequestStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemConnectionRequestStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemConnectionRequestStatics2>
    {
        IRemoteSystemConnectionRequestStatics2(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemConnectionRequestStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemDiscoveryTypeFilter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemDiscoveryTypeFilter>
    {
        IRemoteSystemDiscoveryTypeFilter(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemDiscoveryTypeFilter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemDiscoveryTypeFilterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemDiscoveryTypeFilterFactory>
    {
        IRemoteSystemDiscoveryTypeFilterFactory(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemDiscoveryTypeFilterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemEnumerationCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemEnumerationCompletedEventArgs>
    {
        IRemoteSystemEnumerationCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemEnumerationCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemFilter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemFilter>
    {
        IRemoteSystemFilter(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemFilter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemKindFilter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemKindFilter>
    {
        IRemoteSystemKindFilter(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemKindFilter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemKindFilterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemKindFilterFactory>
    {
        IRemoteSystemKindFilterFactory(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemKindFilterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemKindStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemKindStatics>
    {
        IRemoteSystemKindStatics(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemKindStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemKindStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemKindStatics2>
    {
        IRemoteSystemKindStatics2(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemKindStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemRemovedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemRemovedEventArgs>
    {
        IRemoteSystemRemovedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemRemovedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSession :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSession>
    {
        IRemoteSystemSession(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSession(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionAddedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionAddedEventArgs>
    {
        IRemoteSystemSessionAddedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionAddedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionController>
    {
        IRemoteSystemSessionController(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionControllerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionControllerFactory>
    {
        IRemoteSystemSessionControllerFactory(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionControllerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionCreationResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionCreationResult>
    {
        IRemoteSystemSessionCreationResult(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionCreationResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionDisconnectedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionDisconnectedEventArgs>
    {
        IRemoteSystemSessionDisconnectedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionDisconnectedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionInfo>
    {
        IRemoteSystemSessionInfo(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionInvitation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionInvitation>
    {
        IRemoteSystemSessionInvitation(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionInvitation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionInvitationListener :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionInvitationListener>
    {
        IRemoteSystemSessionInvitationListener(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionInvitationListener(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionInvitationReceivedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionInvitationReceivedEventArgs>
    {
        IRemoteSystemSessionInvitationReceivedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionInvitationReceivedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionJoinRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionJoinRequest>
    {
        IRemoteSystemSessionJoinRequest(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionJoinRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionJoinRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionJoinRequestedEventArgs>
    {
        IRemoteSystemSessionJoinRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionJoinRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionJoinResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionJoinResult>
    {
        IRemoteSystemSessionJoinResult(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionJoinResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionMessageChannel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionMessageChannel>
    {
        IRemoteSystemSessionMessageChannel(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionMessageChannel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionMessageChannelFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionMessageChannelFactory>
    {
        IRemoteSystemSessionMessageChannelFactory(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionMessageChannelFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionOptions>
    {
        IRemoteSystemSessionOptions(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionParticipant :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionParticipant>
    {
        IRemoteSystemSessionParticipant(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionParticipant(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionParticipantAddedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionParticipantAddedEventArgs>
    {
        IRemoteSystemSessionParticipantAddedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionParticipantAddedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionParticipantRemovedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionParticipantRemovedEventArgs>
    {
        IRemoteSystemSessionParticipantRemovedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionParticipantRemovedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionParticipantWatcher :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionParticipantWatcher>
    {
        IRemoteSystemSessionParticipantWatcher(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionParticipantWatcher(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionRemovedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionRemovedEventArgs>
    {
        IRemoteSystemSessionRemovedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionRemovedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionStatics>
    {
        IRemoteSystemSessionStatics(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionUpdatedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionUpdatedEventArgs>
    {
        IRemoteSystemSessionUpdatedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionUpdatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionValueSetReceivedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionValueSetReceivedEventArgs>
    {
        IRemoteSystemSessionValueSetReceivedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionValueSetReceivedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemSessionWatcher :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemSessionWatcher>
    {
        IRemoteSystemSessionWatcher(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemSessionWatcher(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemStatics>
    {
        IRemoteSystemStatics(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemStatics2>
    {
        IRemoteSystemStatics2(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemStatics3>
    {
        IRemoteSystemStatics3(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemStatusTypeFilter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemStatusTypeFilter>
    {
        IRemoteSystemStatusTypeFilter(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemStatusTypeFilter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemStatusTypeFilterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemStatusTypeFilterFactory>
    {
        IRemoteSystemStatusTypeFilterFactory(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemStatusTypeFilterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemUpdatedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemUpdatedEventArgs>
    {
        IRemoteSystemUpdatedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemUpdatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemWatcher :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemWatcher>
    {
        IRemoteSystemWatcher(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemWatcher(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemWatcher2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemWatcher2>
    {
        IRemoteSystemWatcher2(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemWatcher2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemWatcher3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemWatcher3>
    {
        IRemoteSystemWatcher3(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemWatcher3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemWatcherErrorOccurredEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemWatcherErrorOccurredEventArgs>
    {
        IRemoteSystemWatcherErrorOccurredEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemWatcherErrorOccurredEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemWebAccountFilter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemWebAccountFilter>
    {
        IRemoteSystemWebAccountFilter(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemWebAccountFilter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteSystemWebAccountFilterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteSystemWebAccountFilterFactory>
    {
        IRemoteSystemWebAccountFilterFactory(std::nullptr_t = nullptr) noexcept {}
        IRemoteSystemWebAccountFilterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
