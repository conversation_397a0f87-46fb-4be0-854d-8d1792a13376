// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Graphics_Capture_1_H
#define WINRT_Windows_Graphics_Capture_1_H
#include "winrt/impl/Windows.Graphics.Capture.0.h"
WINRT_EXPORT namespace winrt::Windows::Graphics::Capture
{
    struct WINRT_IMPL_EMPTY_BASES IDirect3D11CaptureFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDirect3D11CaptureFrame>
    {
        IDirect3D11CaptureFrame(std::nullptr_t = nullptr) noexcept {}
        IDirect3D11CaptureFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDirect3D11CaptureFrame2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDirect3D11CaptureFrame2>
    {
        IDirect3D11CaptureFrame2(std::nullptr_t = nullptr) noexcept {}
        IDirect3D11CaptureFrame2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDirect3D11CaptureFramePool :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDirect3D11CaptureFramePool>
    {
        IDirect3D11CaptureFramePool(std::nullptr_t = nullptr) noexcept {}
        IDirect3D11CaptureFramePool(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDirect3D11CaptureFramePoolStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDirect3D11CaptureFramePoolStatics>
    {
        IDirect3D11CaptureFramePoolStatics(std::nullptr_t = nullptr) noexcept {}
        IDirect3D11CaptureFramePoolStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDirect3D11CaptureFramePoolStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDirect3D11CaptureFramePoolStatics2>
    {
        IDirect3D11CaptureFramePoolStatics2(std::nullptr_t = nullptr) noexcept {}
        IDirect3D11CaptureFramePoolStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGraphicsCaptureAccessStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGraphicsCaptureAccessStatics>
    {
        IGraphicsCaptureAccessStatics(std::nullptr_t = nullptr) noexcept {}
        IGraphicsCaptureAccessStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGraphicsCaptureItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGraphicsCaptureItem>
    {
        IGraphicsCaptureItem(std::nullptr_t = nullptr) noexcept {}
        IGraphicsCaptureItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGraphicsCaptureItemStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGraphicsCaptureItemStatics>
    {
        IGraphicsCaptureItemStatics(std::nullptr_t = nullptr) noexcept {}
        IGraphicsCaptureItemStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGraphicsCaptureItemStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGraphicsCaptureItemStatics2>
    {
        IGraphicsCaptureItemStatics2(std::nullptr_t = nullptr) noexcept {}
        IGraphicsCaptureItemStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGraphicsCapturePicker :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGraphicsCapturePicker>
    {
        IGraphicsCapturePicker(std::nullptr_t = nullptr) noexcept {}
        IGraphicsCapturePicker(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGraphicsCaptureSession :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGraphicsCaptureSession>
    {
        IGraphicsCaptureSession(std::nullptr_t = nullptr) noexcept {}
        IGraphicsCaptureSession(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGraphicsCaptureSession2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGraphicsCaptureSession2>
    {
        IGraphicsCaptureSession2(std::nullptr_t = nullptr) noexcept {}
        IGraphicsCaptureSession2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGraphicsCaptureSession3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGraphicsCaptureSession3>
    {
        IGraphicsCaptureSession3(std::nullptr_t = nullptr) noexcept {}
        IGraphicsCaptureSession3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGraphicsCaptureSession4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGraphicsCaptureSession4>
    {
        IGraphicsCaptureSession4(std::nullptr_t = nullptr) noexcept {}
        IGraphicsCaptureSession4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGraphicsCaptureSession5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGraphicsCaptureSession5>
    {
        IGraphicsCaptureSession5(std::nullptr_t = nullptr) noexcept {}
        IGraphicsCaptureSession5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGraphicsCaptureSession6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGraphicsCaptureSession6>
    {
        IGraphicsCaptureSession6(std::nullptr_t = nullptr) noexcept {}
        IGraphicsCaptureSession6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGraphicsCaptureSessionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGraphicsCaptureSessionStatics>
    {
        IGraphicsCaptureSessionStatics(std::nullptr_t = nullptr) noexcept {}
        IGraphicsCaptureSessionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
