#define WIN32_LEAN_AND_MEAN
#define _WINSOCKAPI_
#include <winsock2.h>
#include <Windows.h>

#include <tchar.h>
#include <thread>
#include <mutex>
#include <atomic>
#include <d3d11.h>
#include <dxgi.h>
#include <filesystem>

#include <imgui.h>
#include <imgui_impl_dx11.h>
#include <imgui_impl_win32.h>
#include <imgui/imgui_internal.h>

#include "overlay.h"
#include "overlay/draw_settings.h"
#include "config.h"
#include "keycodes.h"
#include "sunone_aimbot_cpp.h"
#include "capture.h"
#include "keyboard_listener.h"
#include "other_tools.h"
#include "virtual_camera.h"
#ifdef USE_CUDA
#include "trt_detector.h"
#endif

ID3D11Device* g_pd3dDevice = NULL;
ID3D11DeviceContext* g_pd3dDeviceContext = NULL;
IDXGISwapChain* g_pSwapChain = NULL;
ID3D11RenderTargetView* g_mainRenderTargetView = NULL;
HWND g_hwnd = NULL;

extern Config config;
extern std::mutex configMutex;
extern std::atomic<bool> shouldExit;

bool CreateDeviceD3D(HWND hWnd);
void CleanupDeviceD3D();
void CreateRenderTarget();
void CleanupRenderTarget();

ID3D11BlendState* g_pBlendState = nullptr;
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);
IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

const int BASE_OVERLAY_WIDTH = 680;
const int BASE_OVERLAY_HEIGHT = 480;
int overlayWidth = 0;
int overlayHeight = 0;

std::vector<std::string> availableModels;
std::vector<std::string> key_names;
std::vector<const char*> key_names_cstrs;

ID3D11ShaderResourceView* body_texture = nullptr;
ID3D11ShaderResourceView* icon_texture = nullptr;

bool InitializeBlendState()
{
    D3D11_BLEND_DESC blendDesc;
    ZeroMemory(&blendDesc, sizeof(blendDesc));

    blendDesc.AlphaToCoverageEnable = FALSE;
    blendDesc.RenderTarget[0].BlendEnable = TRUE;
    blendDesc.RenderTarget[0].SrcBlend = D3D11_BLEND_SRC_ALPHA;
    blendDesc.RenderTarget[0].DestBlend = D3D11_BLEND_INV_SRC_ALPHA;
    blendDesc.RenderTarget[0].BlendOp = D3D11_BLEND_OP_ADD;
    blendDesc.RenderTarget[0].SrcBlendAlpha = D3D11_BLEND_ONE;
    blendDesc.RenderTarget[0].DestBlendAlpha = D3D11_BLEND_ZERO;
    blendDesc.RenderTarget[0].BlendOpAlpha = D3D11_BLEND_OP_ADD;
    blendDesc.RenderTarget[0].RenderTargetWriteMask = D3D11_COLOR_WRITE_ENABLE_ALL;

    HRESULT hr = g_pd3dDevice->CreateBlendState(&blendDesc, &g_pBlendState);
    if (FAILED(hr))
    {
        return false;
    }

    float blendFactor[4] = { 0.f, 0.f, 0.f, 0.f };
    g_pd3dDeviceContext->OMSetBlendState(g_pBlendState, blendFactor, 0xffffffff);

    return true;
}

bool CreateDeviceD3D(HWND hWnd)
{
    DXGI_SWAP_CHAIN_DESC sd;
    ZeroMemory(&sd, sizeof(sd));
    sd.BufferCount = 2;
    sd.BufferDesc.Width = overlayWidth;
    sd.BufferDesc.Height = overlayHeight;
    sd.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    sd.BufferDesc.RefreshRate.Numerator = 0;
    sd.BufferDesc.RefreshRate.Denominator = 0;
    sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    sd.OutputWindow = hWnd;
    sd.SampleDesc.Count = 1;
    sd.SampleDesc.Quality = 0;
    sd.Windowed = TRUE;
    sd.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;
    sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;

    UINT createDeviceFlags = 0;

    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] =
    {
        D3D_FEATURE_LEVEL_11_0,
        D3D_FEATURE_LEVEL_10_0,
    };

    HRESULT res = D3D11CreateDeviceAndSwapChain(NULL,
        D3D_DRIVER_TYPE_HARDWARE,
        NULL,
        createDeviceFlags,
        featureLevelArray,
        2,
        D3D11_SDK_VERSION,
        &sd,
        &g_pSwapChain,
        &g_pd3dDevice,
        &featureLevel,
        &g_pd3dDeviceContext);
    if (res != S_OK)
        return false;

    if (!InitializeBlendState())
        return false;

    CreateRenderTarget();
    return true;
}

void CreateRenderTarget()
{
    ID3D11Texture2D* pBackBuffer = NULL;
    g_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
    g_pd3dDevice->CreateRenderTargetView(pBackBuffer, NULL, &g_mainRenderTargetView);
    pBackBuffer->Release();
}

void CleanupRenderTarget()
{
    if (g_mainRenderTargetView) { g_mainRenderTargetView->Release(); g_mainRenderTargetView = NULL; }
}

void CleanupDeviceD3D()
{
    CleanupRenderTarget();
    if (g_pSwapChain) { g_pSwapChain->Release(); g_pSwapChain = NULL; }
    if (g_pd3dDeviceContext) { g_pd3dDeviceContext->Release(); g_pd3dDeviceContext = NULL; }
    if (g_pd3dDevice) { g_pd3dDevice->Release(); g_pd3dDevice = NULL; }
    if (g_pBlendState) { g_pBlendState->Release(); g_pBlendState = nullptr; }
}

LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam)
{
    if (ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam))
        return true;

    switch (msg)
    {
    case WM_SIZE:
        if (g_pd3dDevice != NULL && wParam != SIZE_MINIMIZED)
        {
            RECT rect;
            GetWindowRect(hWnd, &rect);
            UINT width = rect.right - rect.left;
            UINT height = rect.bottom - rect.top;

            CleanupRenderTarget();
            g_pSwapChain->ResizeBuffers(0, width, height, DXGI_FORMAT_UNKNOWN, 0);
            CreateRenderTarget();
        }
        return 0;
    case WM_DESTROY:
        shouldExit = true;
        ::PostQuitMessage(0);
        return 0;
    default:
        return ::DefWindowProc(hWnd, msg, wParam, lParam);
    }
}

#include "IconsFontAwesome6.h" // 从 IconFontCppHeaders 获取

bool load_icon_texture()
{
    int image_width = 0;
    int image_height = 0;

    bool ret = LoadTextureFromFile("overlay/icon.png", g_pd3dDevice, &icon_texture, &image_width, &image_height);
    if (!ret)
    {
        std::cerr << "[Overlay] Can't load icon.png!" << std::endl;
        return false;
    }
    return true;
}

void release_icon_texture()
{
    if (icon_texture)
    {
        icon_texture->Release();
        icon_texture = nullptr;
    }
}

void SetupUltraModernStyle()
{
    ImGuiStyle& style = ImGui::GetStyle();
    ImVec4* colors = style.Colors;  // 添加这行声明

    // 现代化圆角设计
    style.WindowRounding = 12.0f;
    style.ChildRounding = 10.0f;
    style.FrameRounding = 8.0f;
    style.PopupRounding = 8.0f;
    style.ScrollbarRounding = 6.0f;
    style.GrabRounding = 6.0f;
    style.TabRounding = 8.0f;

    // 优化间距和尺寸
    style.WindowPadding = ImVec2(16, 16);
    style.FramePadding = ImVec2(12, 8);
    style.ItemSpacing = ImVec2(12, 10);
    style.ItemInnerSpacing = ImVec2(8, 6);
    style.IndentSpacing = 24.0f;
    style.ScrollbarSize = 14.0f;
    style.GrabMinSize = 16.0f;

    // 边框设计
    style.WindowBorderSize = 0.0f;
    style.ChildBorderSize = 0.0f;
    style.PopupBorderSize = 1.0f;
    style.FrameBorderSize = 0.0f;

    // 深色渐变背景
    colors[ImGuiCol_WindowBg] = ImVec4(0.06f, 0.06f, 0.08f, 0.95f);
    colors[ImGuiCol_ChildBg] = ImVec4(0.10f, 0.10f, 0.13f, 0.8f);
    colors[ImGuiCol_PopupBg] = ImVec4(0.08f, 0.08f, 0.11f, 0.95f);

    // 边框和阴影
    colors[ImGuiCol_Border] = ImVec4(0.20f, 0.20f, 0.25f, 0.6f);
    colors[ImGuiCol_BorderShadow] = ImVec4(0.00f, 0.00f, 0.00f, 0.3f);

    // 文字颜色
    colors[ImGuiCol_Text] = ImVec4(0.98f, 0.98f, 0.98f, 1.00f);
    colors[ImGuiCol_TextDisabled] = ImVec4(0.55f, 0.55f, 0.60f, 1.00f);

    // 标题栏渐变
    colors[ImGuiCol_TitleBg] = ImVec4(0.12f, 0.12f, 0.16f, 1.00f);
    colors[ImGuiCol_TitleBgActive] = ImVec4(0.15f, 0.15f, 0.20f, 1.00f);
    colors[ImGuiCol_TitleBgCollapsed] = ImVec4(0.10f, 0.10f, 0.14f, 1.00f);

    // 菜单栏
    colors[ImGuiCol_MenuBarBg] = ImVec4(0.12f, 0.12f, 0.16f, 1.00f);

    // 现代滚动条
    colors[ImGuiCol_ScrollbarBg] = ImVec4(0.08f, 0.08f, 0.11f, 0.8f);
    colors[ImGuiCol_ScrollbarGrab] = ImVec4(0.40f, 0.40f, 0.50f, 0.8f);
    colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(0.50f, 0.50f, 0.60f, 0.9f);
    colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(0.60f, 0.60f, 0.70f, 1.0f);

    // 霓虹粉紫色主题
    colors[ImGuiCol_CheckMark] = ImVec4(0.90f, 0.40f, 0.90f, 1.00f);

    // 发光滑块效果
    colors[ImGuiCol_SliderGrab] = ImVec4(0.85f, 0.35f, 0.85f, 1.00f);
    colors[ImGuiCol_SliderGrabActive] = ImVec4(0.95f, 0.50f, 0.95f, 1.00f);

    // 现代按钮设计
    colors[ImGuiCol_Button] = ImVec4(0.18f, 0.18f, 0.24f, 1.00f);
    colors[ImGuiCol_ButtonHovered] = ImVec4(0.25f, 0.25f, 0.32f, 1.00f);
    colors[ImGuiCol_ButtonActive] = ImVec4(0.20f, 0.20f, 0.28f, 1.00f);

    // 卡片式标题
    colors[ImGuiCol_Header] = ImVec4(0.16f, 0.16f, 0.22f, 1.00f);
    colors[ImGuiCol_HeaderHovered] = ImVec4(0.22f, 0.22f, 0.30f, 1.00f);
    colors[ImGuiCol_HeaderActive] = ImVec4(0.18f, 0.18f, 0.26f, 1.00f);

    // 分隔线
    colors[ImGuiCol_Separator] = ImVec4(0.30f, 0.30f, 0.40f, 0.6f);
    colors[ImGuiCol_SeparatorHovered] = ImVec4(0.40f, 0.40f, 0.50f, 0.8f);
    colors[ImGuiCol_SeparatorActive] = ImVec4(0.50f, 0.50f, 0.60f, 1.0f);

    // 调整手柄
    colors[ImGuiCol_ResizeGrip] = ImVec4(0.30f, 0.30f, 0.40f, 0.6f);
    colors[ImGuiCol_ResizeGripHovered] = ImVec4(0.40f, 0.40f, 0.50f, 0.8f);
    colors[ImGuiCol_ResizeGripActive] = ImVec4(0.50f, 0.50f, 0.60f, 1.0f);

    // 现代标签页
    colors[ImGuiCol_Tab] = ImVec4(0.14f, 0.14f, 0.20f, 1.00f);
    colors[ImGuiCol_TabHovered] = ImVec4(0.22f, 0.22f, 0.30f, 1.00f);
    colors[ImGuiCol_TabActive] = ImVec4(0.18f, 0.18f, 0.26f, 1.00f);
    colors[ImGuiCol_TabUnfocused] = ImVec4(0.12f, 0.12f, 0.18f, 1.00f);
    colors[ImGuiCol_TabUnfocusedActive] = ImVec4(0.16f, 0.16f, 0.22f, 1.00f);

    // 现代输入框
    colors[ImGuiCol_FrameBg] = ImVec4(0.14f, 0.14f, 0.20f, 1.00f);
    colors[ImGuiCol_FrameBgHovered] = ImVec4(0.18f, 0.18f, 0.26f, 1.00f);
    colors[ImGuiCol_FrameBgActive] = ImVec4(0.22f, 0.22f, 0.30f, 1.00f);

    // 表格样式
    colors[ImGuiCol_TableHeaderBg] = ImVec4(0.16f, 0.16f, 0.22f, 1.00f);
    colors[ImGuiCol_TableBorderStrong] = ImVec4(0.25f, 0.25f, 0.35f, 1.00f);
    colors[ImGuiCol_TableBorderLight] = ImVec4(0.20f, 0.20f, 0.28f, 1.00f);
    colors[ImGuiCol_TableRowBg] = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);
    colors[ImGuiCol_TableRowBgAlt] = ImVec4(1.00f, 1.00f, 1.00f, 0.05f);
}

void SetupImGui()
{
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();

    ImGuiIO& io = ImGui::GetIO();
    io.FontGlobalScale = config.overlay_ui_scale;
    io.Fonts->Clear();

    ImFontConfig font_config;
    font_config.OversampleH = 2;
    font_config.OversampleV = 2;
    font_config.PixelSnapH = true;

    bool font_loaded = false;
    const char* font_paths[] = {
        "C:/Windows/Fonts/msyh.ttf",
        "C:/Windows/Fonts/simhei.ttf",
        "C:/Windows/Fonts/simsun.ttc",
        "C:/Windows/Fonts/msyh.ttc",
    };

    ImFont* main_font = nullptr;
    for (const char* font_path : font_paths) {
        FILE* file = fopen(font_path, "rb");
        if (file) {
            fclose(file);
            main_font = io.Fonts->AddFontFromFileTTF(font_path, 16.0f, &font_config, io.Fonts->GetGlyphRangesChineseFull());
            if (main_font) {
                font_loaded = true;
                break;
            }
        }
    }

    if (!font_loaded) {
        main_font = io.Fonts->AddFontDefault();
    }

    static const ImWchar icons_ranges[] = { ICON_MIN_FA, ICON_MAX_FA, 0 };
    ImFontConfig icons_config;
    icons_config.MergeMode = true;
    icons_config.GlyphOffset.y = 2.0f;
    icons_config.GlyphMinAdvanceX = 18.0f;

    io.Fonts->AddFontFromFileTTF("overlay/Font Awesome 7 Free-Solid-900.otf", 16.0f, &icons_config, icons_ranges);

    io.Fonts->Build();

    ImGui_ImplWin32_Init(g_hwnd);
    ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext);

    // 应用超现代样式
    SetupUltraModernStyle();

    load_body_texture();
    load_icon_texture();
}

bool CreateOverlayWindow()
{
    overlayWidth = static_cast<int>(BASE_OVERLAY_WIDTH * config.overlay_ui_scale);
    overlayHeight = static_cast<int>(BASE_OVERLAY_HEIGHT * config.overlay_ui_scale);

    WNDCLASSEX wc = { sizeof(WNDCLASSEX), CS_CLASSDC, WndProc, 0L, 0L,
                      GetModuleHandle(NULL), NULL, NULL, NULL, NULL,
                      _T("Edge"), NULL };
    ::RegisterClassEx(&wc);

    g_hwnd = ::CreateWindowEx(
        WS_EX_TOPMOST | WS_EX_LAYERED,
        wc.lpszClassName, _T("Chrome"),
        WS_POPUP, 0, 0, overlayWidth, overlayHeight,
        NULL, NULL, wc.hInstance, NULL);

    if (g_hwnd == NULL)
        return false;

    if (config.overlay_opacity <= 20)
    {
        config.overlay_opacity = 20;
        config.saveConfig("config.ini");
    }

    if (config.overlay_opacity >= 256)
    {
        config.overlay_opacity = 255;
        config.saveConfig("config.ini");
    }

    BYTE opacity = config.overlay_opacity;

    SetLayeredWindowAttributes(g_hwnd, 0, opacity, LWA_ALPHA);

    if (!CreateDeviceD3D(g_hwnd))
    {
        CleanupDeviceD3D();
        ::UnregisterClass(wc.lpszClassName, wc.hInstance);
        return false;
    }

    return true;
}

void RenderUltraModernLayout()
{
    static int selected_tab = 0;
    static float tab_animation[8] = { 0 };

    const char* tab_names[8] = {
        "屏幕捕捉", "目标设置", "鼠标设置", "AI设置",
        "按键设置", "界面设置", "统计信息", "调试信息"
    };
    const char* tab_icons[8] = {
        ICON_FA_DISPLAY, ICON_FA_BULLSEYE, ICON_FA_MOUSE, ICON_FA_MICROCHIP,
        ICON_FA_GAMEPAD, ICON_FA_PALETTE, ICON_FA_CHART_LINE, ICON_FA_BUG
    };

    const int tab_count = 8;
    const float sidebar_width = 140.0f;  // 进一步缩小侧边栏
    const float tab_height = 28.0f;      // 进一步缩小按钮高度

    // 左侧导航栏 - 玻璃质感
    ImGui::PushStyleColor(ImGuiCol_ChildBg, ImVec4(0.08f, 0.08f, 0.12f, 0.85f));
    ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 12.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0, 0));

    if (ImGui::BeginChild("Sidebar", ImVec2(sidebar_width, 0), true, ImGuiWindowFlags_NoScrollbar))
    {
        // Logo区域 - 发光效果
        ImGui::SetCursorPos(ImVec2(10, 15));

        if (icon_texture)
        {
            ImGui::Image((void*)icon_texture, ImVec2(45, 45));  // 进一步缩小图标

            ImGui::SetCursorPosX((sidebar_width - 60) / 2);
            ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 5);

            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.9f, 0.4f, 0.9f, 1.0f));
            ImGui::PushFont(nullptr);
            ImGui::Text("无上法器");
            ImGui::PopFont();

            ImGui::SetCursorPosX((sidebar_width - 50) / 2);
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.7f, 0.7f, 0.8f, 0.9f));
            ImGui::Text("祭道之境");
            ImGui::PopStyleColor(2);
        }

        ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 15);  // 缩小间距

        // 导航按钮
        for (int i = 0; i < tab_count; i++)
        {
            // 动画效果
            bool is_selected = (selected_tab == i);
            if (is_selected) {
                tab_animation[i] = ImMin(tab_animation[i] + 0.1f, 1.0f);
            }
            else {
                tab_animation[i] = ImMax(tab_animation[i] - 0.08f, 0.0f);
            }

            ImGui::SetCursorPosX(8);
            ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 2);  // 缩小按钮间距

            // 选中状态的发光背景
            if (is_selected)
            {
                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.3f + tab_animation[i] * 0.2f, 0.15f + tab_animation[i] * 0.25f, 0.6f + tab_animation[i] * 0.3f, 0.8f + tab_animation[i] * 0.2f));
                ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.35f + tab_animation[i] * 0.25f, 0.2f + tab_animation[i] * 0.3f, 0.65f + tab_animation[i] * 0.35f, 0.9f + tab_animation[i] * 0.1f));
                ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.25f + tab_animation[i] * 0.15f, 0.1f + tab_animation[i] * 0.2f, 0.55f + tab_animation[i] * 0.25f, 0.85f + tab_animation[i] * 0.15f));
                ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 1.0f, 1.0f, 1.0f));
            }
            else
            {
                float hover_alpha = 0.3f + tab_animation[i] * 0.4f;
                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.12f, 0.12f, 0.18f, hover_alpha));
                ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.18f, 0.18f, 0.26f, 0.8f));
                ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.15f, 0.15f, 0.22f, 0.9f));
                ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.8f + tab_animation[i] * 0.2f, 0.8f + tab_animation[i] * 0.2f, 0.85f + tab_animation[i] * 0.15f, 0.9f + tab_animation[i] * 0.1f));
            }

            ImGui::PushStyleVar(ImGuiStyleVar_ButtonTextAlign, ImVec2(0.12f, 0.5f));  // 调整文字对齐
            ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, 5.0f + tab_animation[i] * 2.0f);

            char button_label[256];
            snprintf(button_label, sizeof(button_label), "%s %s", tab_icons[i], tab_names[i]);  // 缩小图标和文字间距

            if (ImGui::Button(button_label, ImVec2(sidebar_width - 16, tab_height)))
            {
                selected_tab = i;
            }

            ImGui::PopStyleVar(2);
            ImGui::PopStyleColor(4);
        }
    }
    ImGui::EndChild();
    ImGui::PopStyleColor();
    ImGui::PopStyleVar(2);

    // 右侧内容区域保持不变
    ImGui::SameLine();

    ImGui::PushStyleColor(ImGuiCol_ChildBg, ImVec4(0.06f, 0.06f, 0.09f, 0.9f));
    ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 12.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0, 0));

    if (ImGui::BeginChild("MainContent", ImVec2(0, 0), true, ImGuiWindowFlags_NoScrollbar))
    {
        // 顶部标题栏 - 渐变背景
        ImGui::PushStyleColor(ImGuiCol_ChildBg, ImVec4(0.10f, 0.10f, 0.15f, 0.95f));
        ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 0.0f);

        if (ImGui::BeginChild("ContentHeader", ImVec2(0, 50), true, ImGuiWindowFlags_NoScrollbar))  // 进一步缩小标题栏
        {
            ImGui::SetCursorPos(ImVec2(20, 12));

            // 发光标题文字
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.4f, 0.9f, 0.5f, 1.0f));
            char header_text[128];
            sprintf_s(header_text, sizeof(header_text), "%s  %s", tab_icons[selected_tab], tab_names[selected_tab]);
            ImGui::Text(header_text);
            ImGui::PopStyleColor();

            // 装饰线
            ImGui::SetCursorPos(ImVec2(20, 35));
            ImGui::PushStyleColor(ImGuiCol_Separator, ImVec4(0.4f, 0.9f, 0.5f, 0.6f));
            ImGui::Separator();
            ImGui::PopStyleColor();
        }
        ImGui::EndChild();
        ImGui::PopStyleColor();
        ImGui::PopStyleVar();

        // 内容区域 - 卡片式布局
        ImGui::PushStyleColor(ImGuiCol_ChildBg, ImVec4(0.06f, 0.06f, 0.09f, 0.0f));
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));

        if (ImGui::BeginChild("ContentBody", ImVec2(0, 0), false, ImGuiWindowFlags_AlwaysVerticalScrollbar))
        {
            ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(10, 8));

            switch (selected_tab)
            {
            case 0: draw_capture_settings(); break;
            case 1: draw_target(); break;
            case 2: draw_mouse(); break;
            case 3: draw_ai(); break;
            case 4: draw_buttons(); break;
            case 5: draw_overlay(); break;
            case 6: draw_stats(); break;
            case 7: draw_debug(); break;
            }

            ImGui::PopStyleVar();
        }
        ImGui::EndChild();
        ImGui::PopStyleColor();
        ImGui::PopStyleVar();
    }
    ImGui::EndChild();
    ImGui::PopStyleColor();
    ImGui::PopStyleVar(2);
}

void OverlayThread()
{
    if (!CreateOverlayWindow())
    {
        std::cout << "[Overlay] Can't create overlay window!" << std::endl;
        return;
    }

    SetupImGui();

    bool show_overlay = false;
    int prev_opacity = config.overlay_opacity;

    for (const auto& pair : KeyCodes::key_code_map)
    {
        key_names.push_back(pair.first);
    }
    std::sort(key_names.begin(), key_names.end());

    key_names_cstrs.reserve(key_names.size());
    for (const auto& name : key_names)
    {
        key_names_cstrs.push_back(name.c_str());
    }

    int input_method_index = 0;
    if (config.input_method == "WIN32")
        input_method_index = 0;
    else if (config.input_method == "GHUB")
        input_method_index = 1;
    else if (config.input_method == "ARDUINO")
        input_method_index = 2;
    else
        input_method_index = 0;

    std::vector<std::string> availableModels = getAvailableModels();

    MSG msg;
    ZeroMemory(&msg, sizeof(msg));
    while (!shouldExit)
    {
        while (::PeekMessage(&msg, NULL, 0U, 0U, PM_REMOVE))
        {
            ::TranslateMessage(&msg);
            ::DispatchMessage(&msg);
            if (msg.message == WM_QUIT)
            {
                shouldExit = true;
                return;
            }
        }

        if (isAnyKeyPressed(config.button_open_overlay) & 0x1)
        {
            show_overlay = !show_overlay;

            if (show_overlay)
            {
                ShowWindow(g_hwnd, SW_SHOW);
                SetForegroundWindow(g_hwnd);
            }
            else
            {
                ShowWindow(g_hwnd, SW_HIDE);
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }

        if (show_overlay)
        {
            ImGui_ImplDX11_NewFrame();
            ImGui_ImplWin32_NewFrame();
            ImGui::NewFrame();

            ImGui::SetNextWindowPos(ImVec2(0, 0));
            ImGui::SetNextWindowSize(ImVec2((float)overlayWidth, (float)overlayHeight));

            ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 12.0f);
            ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 8.0f);
            ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, 6.0f);
            ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0, 0));

            ImGui::Begin("SunOne Aimbot", &show_overlay,
                ImGuiWindowFlags_NoResize |
                ImGuiWindowFlags_NoMove |
                ImGuiWindowFlags_NoTitleBar |
                ImGuiWindowFlags_NoScrollbar);
            {
                std::lock_guard<std::mutex> lock(configMutex);

                RenderUltraModernLayout();

                if (prev_opacity != config.overlay_opacity)
                {
                    BYTE opacity = config.overlay_opacity;
                    SetLayeredWindowAttributes(g_hwnd, 0, opacity, LWA_ALPHA);
                    prev_opacity = config.overlay_opacity;
                    config.saveConfig();
                }
            }
            ImGui::End();

            ImGui::PopStyleVar(4);

            ImGui::Render();
            const float clear_color_with_alpha[4] = { 0.0f, 0.0f, 0.0f, 0.0f };
            g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, NULL);
            g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clear_color_with_alpha);
            ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());

            g_pSwapChain->Present(1, 0);
        }
        else
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }
    }

    release_body_texture();
    release_icon_texture();

    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();

    CleanupDeviceD3D();
    ::DestroyWindow(g_hwnd);
    ::UnregisterClass(_T("Edge"), GetModuleHandle(NULL));
}

int APIENTRY _tWinMain(_In_ HINSTANCE hInstance,
    _In_opt_ HINSTANCE hPrevInstance,
    _In_ LPTSTR    lpCmdLine,
    _In_ int       nCmdShow)
{
    std::thread overlay(OverlayThread);
    overlay.join();
    return 0;
}





