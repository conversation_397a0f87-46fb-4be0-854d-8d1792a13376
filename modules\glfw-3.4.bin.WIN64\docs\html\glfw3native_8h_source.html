<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: glfw3native.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function() { init_codefold(0); });
/* @license-end */
</script>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_13577e2d8b9423099662de029791bd7d.html">glfw-3.4</a></li><li class="navelem"><a class="el" href="dir_b11153cd0f4fd04a7564cc166f482635.html">include</a></li><li class="navelem"><a class="el" href="dir_7f92719a7fe62e5b064f87d7a3c220b1.html">GLFW</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle"><div class="title">glfw3native.h</div></div>
</div><!--header-->
<div class="contents">
<a href="glfw3native_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">/*************************************************************************</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="comment"> * GLFW 3.4 - www.glfw.org</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="comment"> * A library for OpenGL, window and input</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="comment"> *------------------------------------------------------------------------</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment"> * Copyright (c) 2002-2006 Marcus Geelnard</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="comment"> * Copyright (c) 2006-2018 Camilla Löwy &lt;<EMAIL>&gt;</span></div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span><span class="comment"> *</span></div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="comment"> * This software is provided &#39;as-is&#39;, without any express or implied</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span><span class="comment"> * warranty. In no event will the authors be held liable for any damages</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span><span class="comment"> * arising from the use of this software.</span></div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="comment"> *</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span><span class="comment"> * Permission is granted to anyone to use this software for any purpose,</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="comment"> * including commercial applications, and to alter it and redistribute it</span></div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span><span class="comment"> * freely, subject to the following restrictions:</span></div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="comment"> *</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span><span class="comment"> * 1. The origin of this software must not be misrepresented; you must not</span></div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno">   17</span><span class="comment"> *    claim that you wrote the original software. If you use this software</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno">   18</span><span class="comment"> *    in a product, an acknowledgment in the product documentation would</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span><span class="comment"> *    be appreciated but is not required.</span></div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno">   20</span><span class="comment"> *</span></div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno">   21</span><span class="comment"> * 2. Altered source versions must be plainly marked as such, and must not</span></div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span><span class="comment"> *    be misrepresented as being the original software.</span></div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span><span class="comment"> *</span></div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno">   24</span><span class="comment"> * 3. This notice may not be removed or altered from any source</span></div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span><span class="comment"> *    distribution.</span></div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno">   26</span><span class="comment"> *</span></div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno">   27</span><span class="comment"> *************************************************************************/</span></div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno">   28</span> </div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno">   29</span><span class="preprocessor">#ifndef _glfw3_native_h_</span></div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span><span class="preprocessor">#define _glfw3_native_h_</span></div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno">   31</span> </div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno">   32</span><span class="preprocessor">#ifdef __cplusplus</span></div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span><span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> {</div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno">   35</span> </div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno">   36</span> </div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno">   37</span><span class="comment">/*************************************************************************</span></div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno">   38</span><span class="comment"> * Doxygen documentation</span></div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno">   39</span><span class="comment"> *************************************************************************/</span></div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span> </div>
<div class="line"><a id="l00090" name="l00090"></a><span class="lineno">   90</span><span class="comment">/*************************************************************************</span></div>
<div class="line"><a id="l00091" name="l00091"></a><span class="lineno">   91</span><span class="comment"> * System headers and types</span></div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno">   92</span><span class="comment"> *************************************************************************/</span></div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span> </div>
<div class="line"><a id="l00094" name="l00094"></a><span class="lineno">   94</span><span class="preprocessor">#if !defined(GLFW_NATIVE_INCLUDE_NONE)</span></div>
<div class="line"><a id="l00095" name="l00095"></a><span class="lineno">   95</span> </div>
<div class="line"><a id="l00096" name="l00096"></a><span class="lineno">   96</span><span class="preprocessor"> #if defined(GLFW_EXPOSE_NATIVE_WIN32) || defined(GLFW_EXPOSE_NATIVE_WGL)</span></div>
<div class="line"><a id="l00097" name="l00097"></a><span class="lineno">   97</span>  <span class="comment">/* This is a workaround for the fact that glfw3.h needs to export APIENTRY (for</span></div>
<div class="line"><a id="l00098" name="l00098"></a><span class="lineno">   98</span><span class="comment">   * example to allow applications to correctly declare a GL_KHR_debug callback)</span></div>
<div class="line"><a id="l00099" name="l00099"></a><span class="lineno">   99</span><span class="comment">   * but windows.h assumes no one will define APIENTRY before it does</span></div>
<div class="line"><a id="l00100" name="l00100"></a><span class="lineno">  100</span><span class="comment">   */</span></div>
<div class="line"><a id="l00101" name="l00101"></a><span class="lineno">  101</span><span class="preprocessor">  #if defined(GLFW_APIENTRY_DEFINED)</span></div>
<div class="line"><a id="l00102" name="l00102"></a><span class="lineno">  102</span><span class="preprocessor">   #undef APIENTRY</span></div>
<div class="line"><a id="l00103" name="l00103"></a><span class="lineno">  103</span><span class="preprocessor">   #undef GLFW_APIENTRY_DEFINED</span></div>
<div class="line"><a id="l00104" name="l00104"></a><span class="lineno">  104</span><span class="preprocessor">  #endif</span></div>
<div class="line"><a id="l00105" name="l00105"></a><span class="lineno">  105</span><span class="preprocessor">  #include &lt;windows.h&gt;</span></div>
<div class="line"><a id="l00106" name="l00106"></a><span class="lineno">  106</span><span class="preprocessor"> #endif</span></div>
<div class="line"><a id="l00107" name="l00107"></a><span class="lineno">  107</span> </div>
<div class="line"><a id="l00108" name="l00108"></a><span class="lineno">  108</span><span class="preprocessor"> #if defined(GLFW_EXPOSE_NATIVE_COCOA) || defined(GLFW_EXPOSE_NATIVE_NSGL)</span></div>
<div class="line"><a id="l00109" name="l00109"></a><span class="lineno">  109</span><span class="preprocessor">  #if defined(__OBJC__)</span></div>
<div class="line"><a id="l00110" name="l00110"></a><span class="lineno">  110</span><span class="preprocessor">   #import &lt;Cocoa/Cocoa.h&gt;</span></div>
<div class="line"><a id="l00111" name="l00111"></a><span class="lineno">  111</span><span class="preprocessor">  #else</span></div>
<div class="line"><a id="l00112" name="l00112"></a><span class="lineno">  112</span><span class="preprocessor">   #include &lt;ApplicationServices/ApplicationServices.h&gt;</span></div>
<div class="line"><a id="l00113" name="l00113"></a><span class="lineno">  113</span><span class="preprocessor">   #include &lt;objc/objc.h&gt;</span></div>
<div class="line"><a id="l00114" name="l00114"></a><span class="lineno">  114</span><span class="preprocessor">  #endif</span></div>
<div class="line"><a id="l00115" name="l00115"></a><span class="lineno">  115</span><span class="preprocessor"> #endif</span></div>
<div class="line"><a id="l00116" name="l00116"></a><span class="lineno">  116</span> </div>
<div class="line"><a id="l00117" name="l00117"></a><span class="lineno">  117</span><span class="preprocessor"> #if defined(GLFW_EXPOSE_NATIVE_X11) || defined(GLFW_EXPOSE_NATIVE_GLX)</span></div>
<div class="line"><a id="l00118" name="l00118"></a><span class="lineno">  118</span><span class="preprocessor">  #include &lt;X11/Xlib.h&gt;</span></div>
<div class="line"><a id="l00119" name="l00119"></a><span class="lineno">  119</span><span class="preprocessor">  #include &lt;X11/extensions/Xrandr.h&gt;</span></div>
<div class="line"><a id="l00120" name="l00120"></a><span class="lineno">  120</span><span class="preprocessor"> #endif</span></div>
<div class="line"><a id="l00121" name="l00121"></a><span class="lineno">  121</span> </div>
<div class="line"><a id="l00122" name="l00122"></a><span class="lineno">  122</span><span class="preprocessor"> #if defined(GLFW_EXPOSE_NATIVE_WAYLAND)</span></div>
<div class="line"><a id="l00123" name="l00123"></a><span class="lineno">  123</span><span class="preprocessor">  #include &lt;wayland-client.h&gt;</span></div>
<div class="line"><a id="l00124" name="l00124"></a><span class="lineno">  124</span><span class="preprocessor"> #endif</span></div>
<div class="line"><a id="l00125" name="l00125"></a><span class="lineno">  125</span> </div>
<div class="line"><a id="l00126" name="l00126"></a><span class="lineno">  126</span><span class="preprocessor"> #if defined(GLFW_EXPOSE_NATIVE_WGL)</span></div>
<div class="line"><a id="l00127" name="l00127"></a><span class="lineno">  127</span>  <span class="comment">/* WGL is declared by windows.h */</span></div>
<div class="line"><a id="l00128" name="l00128"></a><span class="lineno">  128</span><span class="preprocessor"> #endif</span></div>
<div class="line"><a id="l00129" name="l00129"></a><span class="lineno">  129</span><span class="preprocessor"> #if defined(GLFW_EXPOSE_NATIVE_NSGL)</span></div>
<div class="line"><a id="l00130" name="l00130"></a><span class="lineno">  130</span>  <span class="comment">/* NSGL is declared by Cocoa.h */</span></div>
<div class="line"><a id="l00131" name="l00131"></a><span class="lineno">  131</span><span class="preprocessor"> #endif</span></div>
<div class="line"><a id="l00132" name="l00132"></a><span class="lineno">  132</span><span class="preprocessor"> #if defined(GLFW_EXPOSE_NATIVE_GLX)</span></div>
<div class="line"><a id="l00133" name="l00133"></a><span class="lineno">  133</span>  <span class="comment">/* This is a workaround for the fact that glfw3.h defines GLAPIENTRY because by</span></div>
<div class="line"><a id="l00134" name="l00134"></a><span class="lineno">  134</span><span class="comment">   * default it also acts as an OpenGL header</span></div>
<div class="line"><a id="l00135" name="l00135"></a><span class="lineno">  135</span><span class="comment">   * However, glx.h will include gl.h, which will define it unconditionally</span></div>
<div class="line"><a id="l00136" name="l00136"></a><span class="lineno">  136</span><span class="comment">   */</span></div>
<div class="line"><a id="l00137" name="l00137"></a><span class="lineno">  137</span><span class="preprocessor">  #if defined(GLFW_GLAPIENTRY_DEFINED)</span></div>
<div class="line"><a id="l00138" name="l00138"></a><span class="lineno">  138</span><span class="preprocessor">   #undef GLAPIENTRY</span></div>
<div class="line"><a id="l00139" name="l00139"></a><span class="lineno">  139</span><span class="preprocessor">   #undef GLFW_GLAPIENTRY_DEFINED</span></div>
<div class="line"><a id="l00140" name="l00140"></a><span class="lineno">  140</span><span class="preprocessor">  #endif</span></div>
<div class="line"><a id="l00141" name="l00141"></a><span class="lineno">  141</span><span class="preprocessor">  #include &lt;GL/glx.h&gt;</span></div>
<div class="line"><a id="l00142" name="l00142"></a><span class="lineno">  142</span><span class="preprocessor"> #endif</span></div>
<div class="line"><a id="l00143" name="l00143"></a><span class="lineno">  143</span><span class="preprocessor"> #if defined(GLFW_EXPOSE_NATIVE_EGL)</span></div>
<div class="line"><a id="l00144" name="l00144"></a><span class="lineno">  144</span><span class="preprocessor">  #include &lt;EGL/egl.h&gt;</span></div>
<div class="line"><a id="l00145" name="l00145"></a><span class="lineno">  145</span><span class="preprocessor"> #endif</span></div>
<div class="line"><a id="l00146" name="l00146"></a><span class="lineno">  146</span><span class="preprocessor"> #if defined(GLFW_EXPOSE_NATIVE_OSMESA)</span></div>
<div class="line"><a id="l00147" name="l00147"></a><span class="lineno">  147</span>  <span class="comment">/* This is a workaround for the fact that glfw3.h defines GLAPIENTRY because by</span></div>
<div class="line"><a id="l00148" name="l00148"></a><span class="lineno">  148</span><span class="comment">   * default it also acts as an OpenGL header</span></div>
<div class="line"><a id="l00149" name="l00149"></a><span class="lineno">  149</span><span class="comment">   * However, osmesa.h will include gl.h, which will define it unconditionally</span></div>
<div class="line"><a id="l00150" name="l00150"></a><span class="lineno">  150</span><span class="comment">   */</span></div>
<div class="line"><a id="l00151" name="l00151"></a><span class="lineno">  151</span><span class="preprocessor">  #if defined(GLFW_GLAPIENTRY_DEFINED)</span></div>
<div class="line"><a id="l00152" name="l00152"></a><span class="lineno">  152</span><span class="preprocessor">   #undef GLAPIENTRY</span></div>
<div class="line"><a id="l00153" name="l00153"></a><span class="lineno">  153</span><span class="preprocessor">   #undef GLFW_GLAPIENTRY_DEFINED</span></div>
<div class="line"><a id="l00154" name="l00154"></a><span class="lineno">  154</span><span class="preprocessor">  #endif</span></div>
<div class="line"><a id="l00155" name="l00155"></a><span class="lineno">  155</span><span class="preprocessor">  #include &lt;GL/osmesa.h&gt;</span></div>
<div class="line"><a id="l00156" name="l00156"></a><span class="lineno">  156</span><span class="preprocessor"> #endif</span></div>
<div class="line"><a id="l00157" name="l00157"></a><span class="lineno">  157</span> </div>
<div class="line"><a id="l00158" name="l00158"></a><span class="lineno">  158</span><span class="preprocessor">#endif </span><span class="comment">/*GLFW_NATIVE_INCLUDE_NONE*/</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00159" name="l00159"></a><span class="lineno">  159</span> </div>
<div class="line"><a id="l00160" name="l00160"></a><span class="lineno">  160</span> </div>
<div class="line"><a id="l00161" name="l00161"></a><span class="lineno">  161</span><span class="comment">/*************************************************************************</span></div>
<div class="line"><a id="l00162" name="l00162"></a><span class="lineno">  162</span><span class="comment"> * Functions</span></div>
<div class="line"><a id="l00163" name="l00163"></a><span class="lineno">  163</span><span class="comment"> *************************************************************************/</span></div>
<div class="line"><a id="l00164" name="l00164"></a><span class="lineno">  164</span> </div>
<div class="line"><a id="l00165" name="l00165"></a><span class="lineno">  165</span><span class="preprocessor">#if defined(GLFW_EXPOSE_NATIVE_WIN32)</span></div>
<div class="line"><a id="l00182" name="l00182"></a><span class="lineno"><a class="line" href="group__native.html#gad4d3e9242536c0ba6be88a98f4c73a41">  182</a></span>GLFWAPI <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="group__native.html#gad4d3e9242536c0ba6be88a98f4c73a41">glfwGetWin32Adapter</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor);</div>
<div class="line"><a id="l00183" name="l00183"></a><span class="lineno">  183</span> </div>
<div class="line"><a id="l00200" name="l00200"></a><span class="lineno"><a class="line" href="group__native.html#gac845f7dbe4c1d7fdd682a3c6fdae6766">  200</a></span>GLFWAPI <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="group__native.html#gac845f7dbe4c1d7fdd682a3c6fdae6766">glfwGetWin32Monitor</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor);</div>
<div class="line"><a id="l00201" name="l00201"></a><span class="lineno">  201</span> </div>
<div class="line"><a id="l00225" name="l00225"></a><span class="lineno"><a class="line" href="group__native.html#gafe5079aa79038b0079fc09d5f0a8e667">  225</a></span>GLFWAPI HWND <a class="code hl_function" href="group__native.html#gafe5079aa79038b0079fc09d5f0a8e667">glfwGetWin32Window</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l00226" name="l00226"></a><span class="lineno">  226</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00227" name="l00227"></a><span class="lineno">  227</span> </div>
<div class="line"><a id="l00228" name="l00228"></a><span class="lineno">  228</span><span class="preprocessor">#if defined(GLFW_EXPOSE_NATIVE_WGL)</span></div>
<div class="line"><a id="l00252" name="l00252"></a><span class="lineno"><a class="line" href="group__native.html#gadc4010d91d9cc1134d040eeb1202a143">  252</a></span>GLFWAPI HGLRC <a class="code hl_function" href="group__native.html#gadc4010d91d9cc1134d040eeb1202a143">glfwGetWGLContext</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l00253" name="l00253"></a><span class="lineno">  253</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00254" name="l00254"></a><span class="lineno">  254</span> </div>
<div class="line"><a id="l00255" name="l00255"></a><span class="lineno">  255</span><span class="preprocessor">#if defined(GLFW_EXPOSE_NATIVE_COCOA)</span></div>
<div class="line"><a id="l00271" name="l00271"></a><span class="lineno"><a class="line" href="group__native.html#gaf22f429aec4b1aab316142d66d9be3e6">  271</a></span>GLFWAPI CGDirectDisplayID <a class="code hl_function" href="group__native.html#gaf22f429aec4b1aab316142d66d9be3e6">glfwGetCocoaMonitor</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor);</div>
<div class="line"><a id="l00272" name="l00272"></a><span class="lineno">  272</span> </div>
<div class="line"><a id="l00288" name="l00288"></a><span class="lineno"><a class="line" href="group__native.html#gac3ed9d495d0c2bb9652de5a50c648715">  288</a></span>GLFWAPI <span class="keywordtype">id</span> <a class="code hl_function" href="group__native.html#gac3ed9d495d0c2bb9652de5a50c648715">glfwGetCocoaWindow</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l00289" name="l00289"></a><span class="lineno">  289</span> </div>
<div class="line"><a id="l00305" name="l00305"></a><span class="lineno"><a class="line" href="group__native.html#ga7274fb6595894e880fc95dc63156e9b1">  305</a></span>GLFWAPI <span class="keywordtype">id</span> <a class="code hl_function" href="group__native.html#ga7274fb6595894e880fc95dc63156e9b1">glfwGetCocoaView</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l00306" name="l00306"></a><span class="lineno">  306</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00307" name="l00307"></a><span class="lineno">  307</span> </div>
<div class="line"><a id="l00308" name="l00308"></a><span class="lineno">  308</span><span class="preprocessor">#if defined(GLFW_EXPOSE_NATIVE_NSGL)</span></div>
<div class="line"><a id="l00324" name="l00324"></a><span class="lineno"><a class="line" href="group__native.html#ga559e002e3cd63c979881770cd4dc63bc">  324</a></span>GLFWAPI <span class="keywordtype">id</span> <a class="code hl_function" href="group__native.html#ga559e002e3cd63c979881770cd4dc63bc">glfwGetNSGLContext</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l00325" name="l00325"></a><span class="lineno">  325</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00326" name="l00326"></a><span class="lineno">  326</span> </div>
<div class="line"><a id="l00327" name="l00327"></a><span class="lineno">  327</span><span class="preprocessor">#if defined(GLFW_EXPOSE_NATIVE_X11)</span></div>
<div class="line"><a id="l00343" name="l00343"></a><span class="lineno"><a class="line" href="group__native.html#ga6e7822385cc8a1cc3b18f60352830189">  343</a></span>GLFWAPI Display* <a class="code hl_function" href="group__native.html#ga6e7822385cc8a1cc3b18f60352830189">glfwGetX11Display</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l00344" name="l00344"></a><span class="lineno">  344</span> </div>
<div class="line"><a id="l00360" name="l00360"></a><span class="lineno"><a class="line" href="group__native.html#ga088fbfa80f50569402b41be71ad66e40">  360</a></span>GLFWAPI RRCrtc <a class="code hl_function" href="group__native.html#ga088fbfa80f50569402b41be71ad66e40">glfwGetX11Adapter</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor);</div>
<div class="line"><a id="l00361" name="l00361"></a><span class="lineno">  361</span> </div>
<div class="line"><a id="l00377" name="l00377"></a><span class="lineno"><a class="line" href="group__native.html#gab2f8cc043905e9fa9b12bfdbbcfe874c">  377</a></span>GLFWAPI RROutput <a class="code hl_function" href="group__native.html#gab2f8cc043905e9fa9b12bfdbbcfe874c">glfwGetX11Monitor</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor);</div>
<div class="line"><a id="l00378" name="l00378"></a><span class="lineno">  378</span> </div>
<div class="line"><a id="l00394" name="l00394"></a><span class="lineno"><a class="line" href="group__native.html#ga90ca676322740842db446999a1b1f21d">  394</a></span>GLFWAPI Window <a class="code hl_function" href="group__native.html#ga90ca676322740842db446999a1b1f21d">glfwGetX11Window</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l00395" name="l00395"></a><span class="lineno">  395</span> </div>
<div class="line"><a id="l00416" name="l00416"></a><span class="lineno"><a class="line" href="group__native.html#ga55f879ab02d93367f966186b6f0133f7">  416</a></span>GLFWAPI <span class="keywordtype">void</span> <a class="code hl_function" href="group__native.html#ga55f879ab02d93367f966186b6f0133f7">glfwSetX11SelectionString</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* <span class="keywordtype">string</span>);</div>
<div class="line"><a id="l00417" name="l00417"></a><span class="lineno">  417</span> </div>
<div class="line"><a id="l00444" name="l00444"></a><span class="lineno"><a class="line" href="group__native.html#gae084ef64dc0db140b455b1427256d3f7">  444</a></span>GLFWAPI <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code hl_function" href="group__native.html#gae084ef64dc0db140b455b1427256d3f7">glfwGetX11SelectionString</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l00445" name="l00445"></a><span class="lineno">  445</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00446" name="l00446"></a><span class="lineno">  446</span> </div>
<div class="line"><a id="l00447" name="l00447"></a><span class="lineno">  447</span><span class="preprocessor">#if defined(GLFW_EXPOSE_NATIVE_GLX)</span></div>
<div class="line"><a id="l00463" name="l00463"></a><span class="lineno"><a class="line" href="group__native.html#ga62d884114b0abfcdc2930e89f20867e2">  463</a></span>GLFWAPI GLXContext <a class="code hl_function" href="group__native.html#ga62d884114b0abfcdc2930e89f20867e2">glfwGetGLXContext</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l00464" name="l00464"></a><span class="lineno">  464</span> </div>
<div class="line"><a id="l00480" name="l00480"></a><span class="lineno"><a class="line" href="group__native.html#ga1ed27b8766e859a21381e8f8ce18d049">  480</a></span>GLFWAPI GLXWindow <a class="code hl_function" href="group__native.html#ga1ed27b8766e859a21381e8f8ce18d049">glfwGetGLXWindow</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l00481" name="l00481"></a><span class="lineno">  481</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00482" name="l00482"></a><span class="lineno">  482</span> </div>
<div class="line"><a id="l00483" name="l00483"></a><span class="lineno">  483</span><span class="preprocessor">#if defined(GLFW_EXPOSE_NATIVE_WAYLAND)</span></div>
<div class="line"><a id="l00499" name="l00499"></a><span class="lineno"><a class="line" href="group__native.html#gacbe11f93ce20621de82989bbba94e62a">  499</a></span>GLFWAPI <span class="keyword">struct </span>wl_display* <a class="code hl_function" href="group__native.html#gacbe11f93ce20621de82989bbba94e62a">glfwGetWaylandDisplay</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l00500" name="l00500"></a><span class="lineno">  500</span> </div>
<div class="line"><a id="l00516" name="l00516"></a><span class="lineno"><a class="line" href="group__native.html#ga4f16066bd4c59e2f99418adfcb43dd16">  516</a></span>GLFWAPI <span class="keyword">struct </span>wl_output* <a class="code hl_function" href="group__native.html#ga4f16066bd4c59e2f99418adfcb43dd16">glfwGetWaylandMonitor</a>(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor);</div>
<div class="line"><a id="l00517" name="l00517"></a><span class="lineno">  517</span> </div>
<div class="line"><a id="l00533" name="l00533"></a><span class="lineno"><a class="line" href="group__native.html#ga5c597f2841229d9626f0811cca41ceb3">  533</a></span>GLFWAPI <span class="keyword">struct </span>wl_surface* <a class="code hl_function" href="group__native.html#ga5c597f2841229d9626f0811cca41ceb3">glfwGetWaylandWindow</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l00534" name="l00534"></a><span class="lineno">  534</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00535" name="l00535"></a><span class="lineno">  535</span> </div>
<div class="line"><a id="l00536" name="l00536"></a><span class="lineno">  536</span><span class="preprocessor">#if defined(GLFW_EXPOSE_NATIVE_EGL)</span></div>
<div class="line"><a id="l00554" name="l00554"></a><span class="lineno"><a class="line" href="group__native.html#ga1cd8d973f47aacb5532d368147cc3138">  554</a></span>GLFWAPI EGLDisplay <a class="code hl_function" href="group__native.html#ga1cd8d973f47aacb5532d368147cc3138">glfwGetEGLDisplay</a>(<span class="keywordtype">void</span>);</div>
<div class="line"><a id="l00555" name="l00555"></a><span class="lineno">  555</span> </div>
<div class="line"><a id="l00571" name="l00571"></a><span class="lineno"><a class="line" href="group__native.html#ga671c5072becd085f4ab5771a9c8efcf1">  571</a></span>GLFWAPI EGLContext <a class="code hl_function" href="group__native.html#ga671c5072becd085f4ab5771a9c8efcf1">glfwGetEGLContext</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l00572" name="l00572"></a><span class="lineno">  572</span> </div>
<div class="line"><a id="l00588" name="l00588"></a><span class="lineno"><a class="line" href="group__native.html#ga2199b36117a6a695fec8441d8052eee6">  588</a></span>GLFWAPI EGLSurface <a class="code hl_function" href="group__native.html#ga2199b36117a6a695fec8441d8052eee6">glfwGetEGLSurface</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l00589" name="l00589"></a><span class="lineno">  589</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00590" name="l00590"></a><span class="lineno">  590</span> </div>
<div class="line"><a id="l00591" name="l00591"></a><span class="lineno">  591</span><span class="preprocessor">#if defined(GLFW_EXPOSE_NATIVE_OSMESA)</span></div>
<div class="line"><a id="l00614" name="l00614"></a><span class="lineno"><a class="line" href="group__native.html#ga3b36e3e3dcf308b776427b6bd73cc132">  614</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__native.html#ga3b36e3e3dcf308b776427b6bd73cc132">glfwGetOSMesaColorBuffer</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span>* width, <span class="keywordtype">int</span>* height, <span class="keywordtype">int</span>* format, <span class="keywordtype">void</span>** buffer);</div>
<div class="line"><a id="l00615" name="l00615"></a><span class="lineno">  615</span> </div>
<div class="line"><a id="l00638" name="l00638"></a><span class="lineno"><a class="line" href="group__native.html#ga6b64039ffc88a7a2f57f0956c0c75d53">  638</a></span>GLFWAPI <span class="keywordtype">int</span> <a class="code hl_function" href="group__native.html#ga6b64039ffc88a7a2f57f0956c0c75d53">glfwGetOSMesaDepthBuffer</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span>* width, <span class="keywordtype">int</span>* height, <span class="keywordtype">int</span>* bytesPerValue, <span class="keywordtype">void</span>** buffer);</div>
<div class="line"><a id="l00639" name="l00639"></a><span class="lineno">  639</span> </div>
<div class="line"><a id="l00655" name="l00655"></a><span class="lineno"><a class="line" href="group__native.html#ga9e47700080094eb569cb053afaa88773">  655</a></span>GLFWAPI OSMesaContext <a class="code hl_function" href="group__native.html#ga9e47700080094eb569cb053afaa88773">glfwGetOSMesaContext</a>(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
<div class="line"><a id="l00656" name="l00656"></a><span class="lineno">  656</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00657" name="l00657"></a><span class="lineno">  657</span> </div>
<div class="line"><a id="l00658" name="l00658"></a><span class="lineno">  658</span><span class="preprocessor">#ifdef __cplusplus</span></div>
<div class="line"><a id="l00659" name="l00659"></a><span class="lineno">  659</span>}</div>
<div class="line"><a id="l00660" name="l00660"></a><span class="lineno">  660</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00661" name="l00661"></a><span class="lineno">  661</span> </div>
<div class="line"><a id="l00662" name="l00662"></a><span class="lineno">  662</span><span class="preprocessor">#endif </span><span class="comment">/* _glfw3_native_h_ */</span><span class="preprocessor"></span></div>
<div class="line"><a id="l00663" name="l00663"></a><span class="lineno">  663</span> </div>
<div class="ttc" id="agroup__monitor_html_ga8d9efd1cde9426692c73fe40437d0ae3"><div class="ttname"><a href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a></div><div class="ttdeci">struct GLFWmonitor GLFWmonitor</div><div class="ttdoc">Opaque monitor object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1391</div></div>
<div class="ttc" id="agroup__native_html_ga088fbfa80f50569402b41be71ad66e40"><div class="ttname"><a href="group__native.html#ga088fbfa80f50569402b41be71ad66e40">glfwGetX11Adapter</a></div><div class="ttdeci">RRCrtc glfwGetX11Adapter(GLFWmonitor *monitor)</div><div class="ttdoc">Returns the RRCrtc of the specified monitor.</div></div>
<div class="ttc" id="agroup__native_html_ga1cd8d973f47aacb5532d368147cc3138"><div class="ttname"><a href="group__native.html#ga1cd8d973f47aacb5532d368147cc3138">glfwGetEGLDisplay</a></div><div class="ttdeci">EGLDisplay glfwGetEGLDisplay(void)</div><div class="ttdoc">Returns the EGLDisplay used by GLFW.</div></div>
<div class="ttc" id="agroup__native_html_ga1ed27b8766e859a21381e8f8ce18d049"><div class="ttname"><a href="group__native.html#ga1ed27b8766e859a21381e8f8ce18d049">glfwGetGLXWindow</a></div><div class="ttdeci">GLXWindow glfwGetGLXWindow(GLFWwindow *window)</div><div class="ttdoc">Returns the GLXWindow of the specified window.</div></div>
<div class="ttc" id="agroup__native_html_ga2199b36117a6a695fec8441d8052eee6"><div class="ttname"><a href="group__native.html#ga2199b36117a6a695fec8441d8052eee6">glfwGetEGLSurface</a></div><div class="ttdeci">EGLSurface glfwGetEGLSurface(GLFWwindow *window)</div><div class="ttdoc">Returns the EGLSurface of the specified window.</div></div>
<div class="ttc" id="agroup__native_html_ga3b36e3e3dcf308b776427b6bd73cc132"><div class="ttname"><a href="group__native.html#ga3b36e3e3dcf308b776427b6bd73cc132">glfwGetOSMesaColorBuffer</a></div><div class="ttdeci">int glfwGetOSMesaColorBuffer(GLFWwindow *window, int *width, int *height, int *format, void **buffer)</div><div class="ttdoc">Retrieves the color buffer associated with the specified window.</div></div>
<div class="ttc" id="agroup__native_html_ga4f16066bd4c59e2f99418adfcb43dd16"><div class="ttname"><a href="group__native.html#ga4f16066bd4c59e2f99418adfcb43dd16">glfwGetWaylandMonitor</a></div><div class="ttdeci">struct wl_output * glfwGetWaylandMonitor(GLFWmonitor *monitor)</div><div class="ttdoc">Returns the struct wl_output* of the specified monitor.</div></div>
<div class="ttc" id="agroup__native_html_ga559e002e3cd63c979881770cd4dc63bc"><div class="ttname"><a href="group__native.html#ga559e002e3cd63c979881770cd4dc63bc">glfwGetNSGLContext</a></div><div class="ttdeci">id glfwGetNSGLContext(GLFWwindow *window)</div><div class="ttdoc">Returns the NSOpenGLContext of the specified window.</div></div>
<div class="ttc" id="agroup__native_html_ga55f879ab02d93367f966186b6f0133f7"><div class="ttname"><a href="group__native.html#ga55f879ab02d93367f966186b6f0133f7">glfwSetX11SelectionString</a></div><div class="ttdeci">void glfwSetX11SelectionString(const char *string)</div><div class="ttdoc">Sets the current primary selection to the specified string.</div></div>
<div class="ttc" id="agroup__native_html_ga5c597f2841229d9626f0811cca41ceb3"><div class="ttname"><a href="group__native.html#ga5c597f2841229d9626f0811cca41ceb3">glfwGetWaylandWindow</a></div><div class="ttdeci">struct wl_surface * glfwGetWaylandWindow(GLFWwindow *window)</div><div class="ttdoc">Returns the main struct wl_surface* of the specified window.</div></div>
<div class="ttc" id="agroup__native_html_ga62d884114b0abfcdc2930e89f20867e2"><div class="ttname"><a href="group__native.html#ga62d884114b0abfcdc2930e89f20867e2">glfwGetGLXContext</a></div><div class="ttdeci">GLXContext glfwGetGLXContext(GLFWwindow *window)</div><div class="ttdoc">Returns the GLXContext of the specified window.</div></div>
<div class="ttc" id="agroup__native_html_ga671c5072becd085f4ab5771a9c8efcf1"><div class="ttname"><a href="group__native.html#ga671c5072becd085f4ab5771a9c8efcf1">glfwGetEGLContext</a></div><div class="ttdeci">EGLContext glfwGetEGLContext(GLFWwindow *window)</div><div class="ttdoc">Returns the EGLContext of the specified window.</div></div>
<div class="ttc" id="agroup__native_html_ga6b64039ffc88a7a2f57f0956c0c75d53"><div class="ttname"><a href="group__native.html#ga6b64039ffc88a7a2f57f0956c0c75d53">glfwGetOSMesaDepthBuffer</a></div><div class="ttdeci">int glfwGetOSMesaDepthBuffer(GLFWwindow *window, int *width, int *height, int *bytesPerValue, void **buffer)</div><div class="ttdoc">Retrieves the depth buffer associated with the specified window.</div></div>
<div class="ttc" id="agroup__native_html_ga6e7822385cc8a1cc3b18f60352830189"><div class="ttname"><a href="group__native.html#ga6e7822385cc8a1cc3b18f60352830189">glfwGetX11Display</a></div><div class="ttdeci">Display * glfwGetX11Display(void)</div><div class="ttdoc">Returns the Display used by GLFW.</div></div>
<div class="ttc" id="agroup__native_html_ga7274fb6595894e880fc95dc63156e9b1"><div class="ttname"><a href="group__native.html#ga7274fb6595894e880fc95dc63156e9b1">glfwGetCocoaView</a></div><div class="ttdeci">id glfwGetCocoaView(GLFWwindow *window)</div><div class="ttdoc">Returns the NSView of the specified window.</div></div>
<div class="ttc" id="agroup__native_html_ga90ca676322740842db446999a1b1f21d"><div class="ttname"><a href="group__native.html#ga90ca676322740842db446999a1b1f21d">glfwGetX11Window</a></div><div class="ttdeci">Window glfwGetX11Window(GLFWwindow *window)</div><div class="ttdoc">Returns the Window of the specified window.</div></div>
<div class="ttc" id="agroup__native_html_ga9e47700080094eb569cb053afaa88773"><div class="ttname"><a href="group__native.html#ga9e47700080094eb569cb053afaa88773">glfwGetOSMesaContext</a></div><div class="ttdeci">OSMesaContext glfwGetOSMesaContext(GLFWwindow *window)</div><div class="ttdoc">Returns the OSMesaContext of the specified window.</div></div>
<div class="ttc" id="agroup__native_html_gab2f8cc043905e9fa9b12bfdbbcfe874c"><div class="ttname"><a href="group__native.html#gab2f8cc043905e9fa9b12bfdbbcfe874c">glfwGetX11Monitor</a></div><div class="ttdeci">RROutput glfwGetX11Monitor(GLFWmonitor *monitor)</div><div class="ttdoc">Returns the RROutput of the specified monitor.</div></div>
<div class="ttc" id="agroup__native_html_gac3ed9d495d0c2bb9652de5a50c648715"><div class="ttname"><a href="group__native.html#gac3ed9d495d0c2bb9652de5a50c648715">glfwGetCocoaWindow</a></div><div class="ttdeci">id glfwGetCocoaWindow(GLFWwindow *window)</div><div class="ttdoc">Returns the NSWindow of the specified window.</div></div>
<div class="ttc" id="agroup__native_html_gac845f7dbe4c1d7fdd682a3c6fdae6766"><div class="ttname"><a href="group__native.html#gac845f7dbe4c1d7fdd682a3c6fdae6766">glfwGetWin32Monitor</a></div><div class="ttdeci">const char * glfwGetWin32Monitor(GLFWmonitor *monitor)</div><div class="ttdoc">Returns the display device name of the specified monitor.</div></div>
<div class="ttc" id="agroup__native_html_gacbe11f93ce20621de82989bbba94e62a"><div class="ttname"><a href="group__native.html#gacbe11f93ce20621de82989bbba94e62a">glfwGetWaylandDisplay</a></div><div class="ttdeci">struct wl_display * glfwGetWaylandDisplay(void)</div><div class="ttdoc">Returns the struct wl_display* used by GLFW.</div></div>
<div class="ttc" id="agroup__native_html_gad4d3e9242536c0ba6be88a98f4c73a41"><div class="ttname"><a href="group__native.html#gad4d3e9242536c0ba6be88a98f4c73a41">glfwGetWin32Adapter</a></div><div class="ttdeci">const char * glfwGetWin32Adapter(GLFWmonitor *monitor)</div><div class="ttdoc">Returns the adapter device name of the specified monitor.</div></div>
<div class="ttc" id="agroup__native_html_gadc4010d91d9cc1134d040eeb1202a143"><div class="ttname"><a href="group__native.html#gadc4010d91d9cc1134d040eeb1202a143">glfwGetWGLContext</a></div><div class="ttdeci">HGLRC glfwGetWGLContext(GLFWwindow *window)</div><div class="ttdoc">Returns the HGLRC of the specified window.</div></div>
<div class="ttc" id="agroup__native_html_gae084ef64dc0db140b455b1427256d3f7"><div class="ttname"><a href="group__native.html#gae084ef64dc0db140b455b1427256d3f7">glfwGetX11SelectionString</a></div><div class="ttdeci">const char * glfwGetX11SelectionString(void)</div><div class="ttdoc">Returns the contents of the current primary selection as a string.</div></div>
<div class="ttc" id="agroup__native_html_gaf22f429aec4b1aab316142d66d9be3e6"><div class="ttname"><a href="group__native.html#gaf22f429aec4b1aab316142d66d9be3e6">glfwGetCocoaMonitor</a></div><div class="ttdeci">CGDirectDisplayID glfwGetCocoaMonitor(GLFWmonitor *monitor)</div><div class="ttdoc">Returns the CGDirectDisplayID of the specified monitor.</div></div>
<div class="ttc" id="agroup__native_html_gafe5079aa79038b0079fc09d5f0a8e667"><div class="ttname"><a href="group__native.html#gafe5079aa79038b0079fc09d5f0a8e667">glfwGetWin32Window</a></div><div class="ttdeci">HWND glfwGetWin32Window(GLFWwindow *window)</div><div class="ttdoc">Returns the HWND of the specified window.</div></div>
<div class="ttc" id="agroup__window_html_ga3c96d80d363e67d13a41b5d1821f3242"><div class="ttname"><a href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a></div><div class="ttdeci">struct GLFWwindow GLFWwindow</div><div class="ttdoc">Opaque window object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1403</div></div>
</div><!-- fragment --></div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
