<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Compiling GLFW</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div><div class="header">
  <div class="headertitle"><div class="title">Compiling GLFW</div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#compile_cmake">Using CMake</a><ul><li class="level2"><a href="#compile_deps">Installing dependencies</a><ul><li class="level3"><a href="#compile_deps_wayland">Dependencies for Wayland and X11</a></li>
</ul>
</li>
<li class="level2"><a href="#compile_generate">Generating build files with CMake</a><ul><li class="level3"><a href="#compile_generate_gui">Generating with the CMake GUI</a></li>
<li class="level3"><a href="#compile_generate_cli">Generating with command-line CMake</a></li>
</ul>
</li>
<li class="level2"><a href="#compile_compile">Compiling the library</a></li>
</ul>
</li>
<li class="level1"><a href="#compile_options">CMake options</a><ul><li class="level2"><a href="#compile_options_shared">Shared CMake options</a></li>
<li class="level2"><a href="#compile_options_win32">Win32 specific CMake options</a></li>
<li class="level2"><a href="#compile_options_macos">macOS specific CMake options</a></li>
<li class="level2"><a href="#compile_options_unix">Unix-like system specific CMake options</a></li>
</ul>
</li>
<li class="level1"><a href="#compile_mingw_cross">Cross-compilation with CMake and MinGW</a></li>
<li class="level1"><a href="#compile_manual">Compiling GLFW manually</a></li>
</ul>
</div>
<div class="textblock"><p>This is about compiling the GLFW library itself. For information on how to build applications that use GLFW, see <a class="el" href="build_guide.html">Building applications</a>.</p>
<p>GLFW uses some C99 features and does not support Visual Studio 2012 and earlier.</p>
<h1><a class="anchor" id="compile_cmake"></a>
Using CMake</h1>
<p>GLFW behaves like most other libraries that use CMake so this guide mostly describes the standard configure, generate and compile sequence. If you are already familiar with this from other projects, you may want to focus on the <a class="el" href="compile_guide.html#compile_deps">Installing dependencies</a> and <a class="el" href="compile_guide.html#compile_options">CMake options</a> sections for GLFW-specific information.</p>
<p>GLFW uses <a href="https://cmake.org/">CMake</a> to generate project files or makefiles for your chosen development environment. To compile GLFW, first generate these files with CMake and then use them to compile the GLFW library.</p>
<p>If you are on Windows and macOS you can <a href="https://cmake.org/download/">download CMake</a> from their site.</p>
<p>If you are on a Unix-like system such as Linux, FreeBSD or Cygwin or have a package system like Fink, MacPorts or Homebrew, you can install its CMake package.</p>
<p>CMake is a complex tool and this guide will only show a few of the possible ways to set up and compile GLFW. The CMake project has their own much more detailed <a href="https://cmake.org/cmake/help/latest/guide/user-interaction/">CMake user guide</a> that includes everything in this guide not specific to GLFW. It may be a useful companion to this one.</p>
<h2><a class="anchor" id="compile_deps"></a>
Installing dependencies</h2>
<p>The C/C++ development environments in Visual Studio, Xcode and MinGW come with all necessary dependencies for compiling GLFW, but on Unix-like systems like Linux and FreeBSD you will need a few extra packages.</p>
<h3><a class="anchor" id="compile_deps_wayland"></a>
Dependencies for Wayland and X11</h3>
<p>By default, both the Wayland and X11 backends are enabled on Linux and other Unix-like systems (except macOS). To disable one or both of these, set the <a class="el" href="compile_guide.html#GLFW_BUILD_WAYLAND">GLFW_BUILD_WAYLAND</a> or <a class="el" href="compile_guide.html#GLFW_BUILD_X11">GLFW_BUILD_X11</a> CMake options in the next step when generating build files.</p>
<p>To compile GLFW for both Wayland and X11, you need to have the X11, Wayland and xkbcommon development packages installed. On some systems a few other packages are also required. None of the development packages above are needed to build or run programs that use an already compiled GLFW library.</p>
<p>On Debian and derivatives like Ubuntu and Linux Mint you will need the <code>libwayland-dev</code> and <code>libxkbcommon-dev</code> packages to compile for Wayland and the <code>xorg-dev</code> meta-package to compile for X11. These will pull in all other dependencies.</p>
<div class="fragment"><div class="line">sudo apt install libwayland-dev libxkbcommon-dev xorg-dev</div>
</div><!-- fragment --><p>On Fedora and derivatives like Red Hat you will need the <code>wayland-devel</code> and <code>libxkbcommon-devel</code> packages to compile for Wayland and the <code>libXcursor-devel</code>, <code>libXi-devel</code>, <code>libXinerama-devel</code> and <code>libXrandr-devel</code> packages to compile for X11. These will pull in all other dependencies.</p>
<div class="fragment"><div class="line">sudo dnf install wayland-devel libxkbcommon-devel libXcursor-devel libXi-devel libXinerama-devel libXrandr-devel</div>
</div><!-- fragment --><p>On FreeBSD you will need the <code>wayland</code>, <code>libxkbcommon</code> and <code>evdev-proto</code> packages to compile for Wayland. The X11 headers are installed along the end-user X11 packages, so if you have an X server running you should have the headers as well. If not, install the <code>xorgproto</code> package to compile for X11.</p>
<div class="fragment"><div class="line">pkg install wayland libxkbcommon evdev-proto xorgproto</div>
</div><!-- fragment --><p>On Cygwin Wayland is not supported but you will need the <code>libXcursor-devel</code>, <code>libXi-devel</code>, <code>libXinerama-devel</code>, <code>libXrandr-devel</code> and <code>libXrender-devel</code> packages to compile for X11. These can be found in the Libs section of the GUI installer and will pull in all other dependencies.</p>
<p>Once you have the required dependencies, move on to <a class="el" href="compile_guide.html#compile_generate">Generating build files with CMake</a>.</p>
<h2><a class="anchor" id="compile_generate"></a>
Generating build files with CMake</h2>
<p>Once you have all necessary dependencies it is time to generate the project files or makefiles for your development environment. CMake needs two paths for this:</p>
<ul>
<li>the path to the root directory of the GLFW source tree (not its <code>src</code> subdirectory)</li>
<li>the path to the directory where the generated build files and compiled binaries will be placed</li>
</ul>
<p>If these are the same, it is called an in-tree build, otherwise it is called an out-of-tree build.</p>
<p>Out-of-tree builds are recommended as they avoid cluttering up the source tree. They also allow you to have several build directories for different configurations all using the same source tree.</p>
<p>A common pattern when building a single configuration is to have a build directory named <code>build</code> in the root of the source tree.</p>
<h3><a class="anchor" id="compile_generate_gui"></a>
Generating with the CMake GUI</h3>
<p>Start the CMake GUI and set the paths to the source and build directories described above. Then press <em>Configure</em> and <em>Generate</em>.</p>
<p>If you wish change any CMake variables in the list, press <em>Configure</em> and then <em>Generate</em> to have the new values take effect. The variable list will be populated after the first configure step.</p>
<p>By default, GLFW will use Wayland and X11 on Linux and other Unix-like systems other than macOS. To disable support for one or both of these, set the <a class="el" href="compile_guide.html#GLFW_BUILD_WAYLAND">GLFW_BUILD_WAYLAND</a> and/or <a class="el" href="compile_guide.html#GLFW_BUILD_X11">GLFW_BUILD_X11</a> option in the GLFW section of the variable list, then apply the new value as described above.</p>
<p>Once you have generated the project files or makefiles for your chosen development environment, move on to <a class="el" href="compile_guide.html#compile_compile">Compiling the library</a>.</p>
<h3><a class="anchor" id="compile_generate_cli"></a>
Generating with command-line CMake</h3>
<p>To make a build directory, pass the source and build directories to the <code>cmake</code> command. These can be relative or absolute paths. The build directory is created if it doesn't already exist.</p>
<div class="fragment"><div class="line">cmake -S path/to/glfw -B path/to/build</div>
</div><!-- fragment --><p>It is common to name the build directory <code>build</code> and place it in the root of the source tree when only planning to build a single configuration.</p>
<div class="fragment"><div class="line">cd path/to/glfw</div>
<div class="line">cmake -S . -B build</div>
</div><!-- fragment --><p>Without other flags these will generate Visual Studio project files on Windows and makefiles on other platforms. You can choose other targets using the <code>-G</code> flag.</p>
<div class="fragment"><div class="line">cmake -S path/to/glfw -B path/to/build -G Xcode</div>
</div><!-- fragment --><p>By default, GLFW will use Wayland and X11 on Linux and other Unix-like systems other than macOS. To disable support for one or both of these, set the <a class="el" href="compile_guide.html#GLFW_BUILD_WAYLAND">GLFW_BUILD_WAYLAND</a> and/or <a class="el" href="compile_guide.html#GLFW_BUILD_X11">GLFW_BUILD_X11</a> CMake option.</p>
<div class="fragment"><div class="line">cmake -S path/to/glfw -B path/to/build -D GLFW_BUILD_X11=0</div>
</div><!-- fragment --><p>Once you have generated the project files or makefiles for your chosen development environment, move on to <a class="el" href="compile_guide.html#compile_compile">Compiling the library</a>.</p>
<h2><a class="anchor" id="compile_compile"></a>
Compiling the library</h2>
<p>You should now have all required dependencies and the project files or makefiles necessary to compile GLFW. Go ahead and compile the actual GLFW library with these files as you would with any other project.</p>
<p>With Visual Studio open <code>GLFW.sln</code> and use the Build menu. With Xcode open <code>GLFW.xcodeproj</code> and use the Project menu.</p>
<p>With Linux, macOS and other forms of Unix, run <code>make</code>.</p>
<div class="fragment"><div class="line">cd path/to/build</div>
<div class="line">make</div>
</div><!-- fragment --><p>With MinGW, it is <code>mingw32-make</code>.</p>
<div class="fragment"><div class="line">cd path/to/build</div>
<div class="line">mingw32-make</div>
</div><!-- fragment --><p>Any CMake build directory can also be built with the <code>cmake</code> command and the <code>--build</code> flag.</p>
<div class="fragment"><div class="line">cmake --build path/to/build</div>
</div><!-- fragment --><p>This will run the platform specific build tool the directory was generated for.</p>
<p>Once the GLFW library is compiled you are ready to build your application, linking it to the GLFW library. See <a class="el" href="build_guide.html">Building applications</a> for more information.</p>
<h1><a class="anchor" id="compile_options"></a>
CMake options</h1>
<p>The CMake files for GLFW provide a number of options, although not all are available on all supported platforms. Some of these are de facto standards among projects using CMake and so have no <code>GLFW_</code> prefix.</p>
<p>If you are using the GUI version of CMake, these are listed and can be changed from there. If you are using the command-line version of CMake you can use the <code>ccmake</code> ncurses GUI to set options. Some package systems like Ubuntu and other distributions based on Debian GNU/Linux have this tool in a separate <code>cmake-curses-gui</code> package.</p>
<p>Finally, if you don't want to use any GUI, you can set options from the <code>cmake</code> command-line with the <code>-D</code> flag.</p>
<div class="fragment"><div class="line">cmake -S path/to/glfw -B path/to/build -D BUILD_SHARED_LIBS=ON</div>
</div><!-- fragment --><h2><a class="anchor" id="compile_options_shared"></a>
Shared CMake options</h2>
<p><a class="anchor" id="BUILD_SHARED_LIBS"></a><b>BUILD_SHARED_LIBS</b> determines whether GLFW is built as a static library or as a DLL / shared library / dynamic library. This is disabled by default, producing a static GLFW library. This variable has no <code>GLFW_</code> prefix because it is defined by CMake. If you want to change the library only for GLFW when it is part of a larger project, see <a class="el" href="compile_guide.html#GLFW_LIBRARY_TYPE">GLFW_LIBRARY_TYPE</a>.</p>
<p><a class="anchor" id="GLFW_LIBRARY_TYPE"></a><b>GLFW_LIBRARY_TYPE</b> allows you to override <a class="el" href="compile_guide.html#BUILD_SHARED_LIBS">BUILD_SHARED_LIBS</a> only for GLFW, without affecting other libraries in a larger project. When set, the value of this option must be a valid CMake library type. Set it to <code>STATIC</code> to build GLFW as a static library, <code>SHARED</code> to build it as a shared library / dynamic library / DLL, or <code>OBJECT</code> to make GLFW a CMake object library.</p>
<p><a class="anchor" id="GLFW_BUILD_EXAMPLES"></a><b>GLFW_BUILD_EXAMPLES</b> determines whether the GLFW examples are built along with the library. This is enabled by default unless GLFW is being built as a subproject of a larger CMake project.</p>
<p><a class="anchor" id="GLFW_BUILD_TESTS"></a><b>GLFW_BUILD_TESTS</b> determines whether the GLFW test programs are built along with the library. This is enabled by default unless GLFW is being built as a subproject of a larger CMake project.</p>
<p><a class="anchor" id="GLFW_BUILD_DOCS"></a><b>GLFW_BUILD_DOCS</b> determines whether the GLFW documentation is built along with the library. This is enabled by default if <a href="https://www.doxygen.nl/">Doxygen</a> is found by CMake during configuration.</p>
<h2><a class="anchor" id="compile_options_win32"></a>
Win32 specific CMake options</h2>
<p><a class="anchor" id="GLFW_BUILD_WIN32"></a><b>GLFW_BUILD_WIN32</b> determines whether to include support for Win32 when compiling the library. This option is only available when compiling for Windows. This is enabled by default.</p>
<p><a class="anchor" id="USE_MSVC_RUNTIME_LIBRARY_DLL"></a><b>USE_MSVC_RUNTIME_LIBRARY_DLL</b> determines whether to use the DLL version or the static library version of the Visual C++ runtime library. When enabled, the DLL version of the Visual C++ library is used. This is enabled by default.</p>
<p>On CMake 3.15 and later you can set the standard CMake <a href="https://cmake.org/cmake/help/latest/variable/CMAKE_MSVC_RUNTIME_LIBRARY.html">CMAKE_MSVC_RUNTIME_LIBRARY</a> variable instead of this GLFW-specific option.</p>
<p><a class="anchor" id="GLFW_USE_HYBRID_HPG"></a><b>GLFW_USE_HYBRID_HPG</b> determines whether to export the <code>NvOptimusEnablement</code> and <code>AmdPowerXpressRequestHighPerformance</code> symbols, which force the use of the high-performance GPU on Nvidia Optimus and AMD PowerXpress systems. These symbols need to be exported by the EXE to be detected by the driver, so the override will not work if GLFW is built as a DLL. This is disabled by default, letting the operating system and driver decide.</p>
<h2><a class="anchor" id="compile_options_macos"></a>
macOS specific CMake options</h2>
<p><a class="anchor" id="GLFW_BUILD_COCOA"></a><b>GLFW_BUILD_COCOA</b> determines whether to include support for Cocoa when compiling the library. This option is only available when compiling for macOS. This is enabled by default.</p>
<h2><a class="anchor" id="compile_options_unix"></a>
Unix-like system specific CMake options</h2>
<p><a class="anchor" id="GLFW_BUILD_WAYLAND"></a><b>GLFW_BUILD_WAYLAND</b> determines whether to include support for Wayland when compiling the library. This option is only available when compiling for Linux and other Unix-like systems other than macOS. This is enabled by default.</p>
<p><a class="anchor" id="GLFW_BUILD_X11"></a><b>GLFW_BUILD_X11</b> determines whether to include support for X11 when compiling the library. This option is only available when compiling for Linux and other Unix-like systems other than macOS. This is enabled by default.</p>
<h1><a class="anchor" id="compile_mingw_cross"></a>
Cross-compilation with CMake and MinGW</h1>
<p>Both Cygwin and many Linux distributions have MinGW or MinGW-w64 packages. For example, Cygwin has the <code>mingw64-i686-gcc</code> and <code>mingw64-x86_64-gcc</code> packages for 32- and 64-bit version of MinGW-w64, while Debian GNU/Linux and derivatives like Ubuntu have the <code>mingw-w64</code> package for both.</p>
<p>GLFW has CMake toolchain files in the <code>CMake</code> subdirectory that set up cross-compilation of Windows binaries. To use these files you set the <code>CMAKE_TOOLCHAIN_FILE</code> CMake variable with the <code>-D</code> flag add an option when configuring and generating the build files.</p>
<div class="fragment"><div class="line">cmake -S path/to/glfw -B path/to/build -D CMAKE_TOOLCHAIN_FILE=path/to/file</div>
</div><!-- fragment --><p>The exact toolchain file to use depends on the prefix used by the MinGW or MinGW-w64 binaries on your system. You can usually see this in the /usr directory. For example, both the Ubuntu and Cygwin MinGW-w64 packages have <code>/usr/x86_64-w64-mingw32</code> for the 64-bit compilers, so the correct invocation would be:</p>
<div class="fragment"><div class="line">cmake -S path/to/glfw -B path/to/build -D CMAKE_TOOLCHAIN_FILE=CMake/x86_64-w64-mingw32.cmake</div>
</div><!-- fragment --><p>The path to the toolchain file is relative to the path to the GLFW source tree passed to the <code>-S</code> flag, not to the current directory.</p>
<p>For more details see the <a href="https://cmake.org/cmake/help/latest/manual/cmake-toolchains.7.html">CMake toolchain guide</a>.</p>
<h1><a class="anchor" id="compile_manual"></a>
Compiling GLFW manually</h1>
<p>If you wish to compile GLFW without its CMake build environment then you will have to do at least some platform-detection yourself. There are preprocessor macros for enabling support for the platforms (window systems) available. There are also optional, platform-specific macros for various features.</p>
<p>When building, GLFW will expect the necessary configuration macros to be defined on the command-line. The GLFW CMake files set these as private compile definitions on the GLFW target but if you compile the GLFW sources manually you will need to define them yourself.</p>
<p>The window system is used to create windows, handle input, monitors, gamma ramps and clipboard. The options are:</p>
<ul>
<li><b>_GLFW_COCOA</b> to use the Cocoa frameworks</li>
<li><b>_GLFW_WIN32</b> to use the Win32 API</li>
<li><b>_GLFW_WAYLAND</b> to use the Wayland protocol</li>
<li><b>_GLFW_X11</b> to use the X Window System</li>
</ul>
<p>The <b>_GLFW_WAYLAND</b> and <b>_GLFW_X11</b> macros may be combined and produces a library that attempts to detect the appropriate platform at initialization.</p>
<p>If you are building GLFW as a shared library / dynamic library / DLL then you must also define <b>_GLFW_BUILD_DLL</b>. Otherwise, you must not define it.</p>
<p>If you are using a custom name for the Vulkan, EGL, GLX, OSMesa, OpenGL, GLESv1 or GLESv2 library, you can override the default names by defining those you need of <b>_GLFW_VULKAN_LIBRARY</b>, <b>_GLFW_EGL_LIBRARY</b>, <b>_GLFW_GLX_LIBRARY</b>, <b>_GLFW_OSMESA_LIBRARY</b>, <b>_GLFW_OPENGL_LIBRARY</b>, <b>_GLFW_GLESV1_LIBRARY</b> and <b>_GLFW_GLESV2_LIBRARY</b>. Otherwise, GLFW will use the built-in default names.</p>
<dl class="section note"><dt>Note</dt><dd>None of the <a class="el" href="build_guide.html#build_macros">GLFW header option macros</a> may be defined during the compilation of GLFW. If you define any of these in your build files, make sure they are not applied to the GLFW sources. </dd></dl>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
