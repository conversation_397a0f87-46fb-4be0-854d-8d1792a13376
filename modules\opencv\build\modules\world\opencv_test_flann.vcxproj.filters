﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\flann\test\test_lshtable_badarg.cpp">
      <Filter>opencv_flann\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\flann\test\test_main.cpp">
      <Filter>opencv_flann\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\flann\test\test_precomp.hpp">
      <Filter>opencv_flann\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_flann">
      <UniqueIdentifier>{9A657EF5-F815-371B-AC93-E269F622E2F6}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_flann\Include">
      <UniqueIdentifier>{1BB1A0CC-8756-37FE-A70C-8D4341224755}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_flann\Src">
      <UniqueIdentifier>{DA1C39FE-6D8D-3B31-933F-93004C1A12DA}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
