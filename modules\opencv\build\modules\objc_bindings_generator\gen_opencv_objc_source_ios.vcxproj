﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{12DC022F-B4BB-31F2-AAF8-19175BD62A36}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>gen_opencv_objc_source_ios</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\AI\opencv\cudabuild\CMakeFiles\b395ee7c0999e42287482932580994cb\gen_opencv_objc_source_ios.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generate files for Objective-C bindings (ios)</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd D:\AI\opencv\cudabuild\modules\objc_bindings_generator\ios
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe D:/AI/opencv/opencv-4.10.0/modules/objc/generator/../generator/gen_objc.py -p D:/AI/opencv/opencv-4.10.0/modules/objc/generator/../../python/src2/gen2.py -c D:/AI/opencv/cudabuild/modules/objc_bindings_generator/gen_objc.json -t ios -f opencv2
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch D:/AI/opencv/cudabuild/CMakeFiles/dephelper/gen_opencv_objc_source_ios
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\opencv-4.10.0\modules\objc\generator\gen_objc.py;D:\AI\opencv\opencv-4.10.0\modules\python\src2\gen2.py;D:\AI\opencv\opencv-4.10.0\modules\python\src2\hdr_parser.py;D:\AI\opencv\opencv-4.10.0\modules\objc\generator\templates\cmakelists.template;D:\AI\opencv\opencv-4.10.0\modules\objc\generator\templates\objc_class_body.template;D:\AI\opencv\opencv-4.10.0\modules\objc\generator\templates\objc_class_header.template;D:\AI\opencv\opencv-4.10.0\modules\objc\generator\templates\objc_module_body.template;D:\AI\opencv\opencv-4.10.0\modules\objc\generator\templates\objc_module_header.template;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\backend\parallel_for.openmp.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\backend\parallel_for.tbb.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\parallel_backend.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\ocl_defs.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\opencl_info.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\opencl_svm.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_clblas.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_clfft.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_core_wrappers.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_gl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_gl_wrappers.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_clblas.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_clfft.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_core_wrappers.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_gl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_gl_wrappers.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_svm_20.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_svm_definitions.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_svm_hsa_extension.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\block.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\border_interpolate.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\color.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\common.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\datamov_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\dynamic_smem.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\emulation.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\filters.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\funcattrib.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\functional.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\limits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\reduce.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\saturate_cast.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\scan.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\simd_functions.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\transform.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\type_traits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\utility.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\vec_distance.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\vec_math.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\vec_traits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\warp.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\warp_reduce.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\warp_shuffle.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\color_detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\reduce.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\reduce_key_val.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\transform_detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\type_traits_detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\vec_distance_detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\affine.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\async.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\base.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\bindings_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\bufferpool.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\check.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\core_c.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda_stream_accessor.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda_types.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cv_cpu_dispatch.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cv_cpu_helper.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvdef.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd_wrapper.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\directx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\dualquaternion.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\dualquaternion.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\eigen.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\fast_math.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\hal.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\interface.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_avx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_avx512.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_cpp.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_forward.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_lasx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_lsx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_msa.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_neon.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv071.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_010_compat_non-policy.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_010_compat_overloaded-non-policy.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_011_compat.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_compat_overloaded.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_scalable.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_sse.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_sse_em.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_vsx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_wasm.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\msa_macros.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\simd_utils.impl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\mat.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\mat.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\matx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\matx.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\neon_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\ocl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\ocl_genbase.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opengl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\operations.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\optim.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\ovx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\persistence.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\private.cuda.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\quaternion.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\quaternion.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\saturate.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\simd_intrinsics.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\softfloat.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\sse_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\traits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\types.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\types_c.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utility.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\allocator_stats.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\allocator_stats.impl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\buffer_area.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\configuration.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\filesystem.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\filesystem.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\fp_control.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\fp_control_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\instrumentation.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\lock.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logger.defines.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logger.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logtag.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\plugin_loader.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\tls.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\trace.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\trace.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\va_intel.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\version.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\vsx_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\async_promise.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\dispatch_helper.impl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\exception_ptr.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\ArrayUtil.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\ArrayUtil.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\ByteVector.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\ByteVector.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\ByteVectorExt.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\CVObjcUtil.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Converters.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Converters.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\CvType.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\CvType.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\CvTypeExt.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\DMatch.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\DMatch.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Double2.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Double2.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Double3.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Double3.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\DoubleVector.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\DoubleVector.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\DoubleVectorExt.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Float4.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Float4.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Float6.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Float6.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\FloatVector.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\FloatVector.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\FloatVectorExt.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Int4.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Int4.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\IntVector.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\IntVector.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\IntVectorExt.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\KeyPoint.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\KeyPoint.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Mat.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Mat.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatExt.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfByte.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfByte.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfDMatch.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfDMatch.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfDouble.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfDouble.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfFloat.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfFloat.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfFloat4.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfFloat4.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfFloat6.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfFloat6.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfInt.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfInt.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfInt4.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfInt4.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfKeyPoint.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfKeyPoint.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfPoint2f.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfPoint2f.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfPoint2i.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfPoint2i.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfPoint3.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfPoint3.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfPoint3f.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfPoint3f.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfRect2d.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfRect2d.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfRect2i.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfRect2i.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfRotatedRect.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfRotatedRect.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MinMaxLocResult.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MinMaxLocResult.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point2d.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point2d.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point2f.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point2f.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point2i.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point2i.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point3d.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point3d.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point3f.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point3f.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point3i.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point3i.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Range.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Range.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Rect2d.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Rect2d.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Rect2f.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Rect2f.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Rect2i.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Rect2i.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\RotatedRect.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\RotatedRect.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Scalar.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Scalar.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Size2d.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Size2d.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Size2f.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Size2f.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Size2i.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Size2i.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\TermCriteria.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\TermCriteria.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Typealiases.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\filelist;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\manual\core_manual.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\ConvertersTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\CoreTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\CvTypeTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\DMatchTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\KeyPointTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\MatTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\MatTestObjc.m;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\Point3Test.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\PointTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\RangeTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\RectTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\RotatedRectTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\ScalarTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\SizeTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\TermCriteriaTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\resources\chessboard.jpg;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\resources\lena.png;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\bindings.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\hal\hal.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\hal\interface.h;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\imgproc.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\imgproc_c.h;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\segmentation.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\types_c.h;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\detail\gcgraph.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\detail\legacy.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\objc\common\Moments.h;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\objc\common\Moments.mm;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\objc\test\ImgprocTest.swift;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\objc\test\MomentsTest.swift;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\objc\test\Subdiv2DTest.swift;D:\AI\opencv\opencv-4.10.0\modules\ml\include\opencv2\ml.hpp;D:\AI\opencv\opencv-4.10.0\modules\ml\include\opencv2\ml\ml.hpp;D:\AI\opencv\opencv-4.10.0\modules\ml\include\opencv2\ml\ml.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\ml\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include\opencv2\phase_unwrapping.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include\opencv2\phase_unwrapping\histogramphaseunwrapping.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include\opencv2\phase_unwrapping\phase_unwrapping.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\plot\include\opencv2\plot.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\plot\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\all_layers.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dict.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dnn.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dnn.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer.details.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer_reg.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\shape_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\utils\debug_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\utils\inference_engine.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\version.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\features2d\include\opencv2\features2d.hpp;D:\AI\opencv\opencv-4.10.0\modules\features2d\include\opencv2\features2d\features2d.hpp;D:\AI\opencv\opencv-4.10.0\modules\features2d\include\opencv2\features2d\hal\interface.h;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\imgcodecs.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\imgcodecs_c.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\ios.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\legacy\constants_c.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\macosx.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\ios\MatConverters.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\ios\MatConverters.mm;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\ios\MatQuickLook.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\ios\MatQuickLook.mm;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\macosx\MatConverters.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\macosx\MatConverters.mm;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\macosx\MatQuickLook.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\macosx\MatQuickLook.mm;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\test\ImgcodecsTest.swift;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo\cuda.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo\legacy\constants_c.h;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo\photo.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text\erfilter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text\ocr.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text\swt_text_detection.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text\textDetector.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\cap_ios.h;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\container_avi.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\legacy\constants_c.h;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\registry.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\utils.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\videoio.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\videoio_c.h;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\precomp.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_dshow.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_obsensor_capture.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_obsensor\obsensor_stream_channel_interface.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_obsensor\obsensor_uvc_stream_channel.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_obsensor\obsensor_stream_channel_msmf.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\videoio\misc\objc\ios\CvAbstractCamera2.mm;D:\AI\opencv\opencv-4.10.0\modules\videoio\misc\objc\ios\CvCamera2.h;D:\AI\opencv\opencv-4.10.0\modules\videoio\misc\objc\ios\CvPhotoCamera2.m;D:\AI\opencv\opencv-4.10.0\modules\videoio\misc\objc\ios\CvVideoCamera2.mm;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\bm3d_image_denoising.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\dct_image_denoising.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\inpainting.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\oilpainting.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\tonemap.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\white_balance.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include\opencv2\calib3d.hpp;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include\opencv2\calib3d\calib3d.hpp;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include\opencv2\calib3d\calib3d_c.h;D:\AI\opencv\opencv-4.10.0\modules\calib3d\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\calib3d\misc\objc\test\Calib3dTest.swift;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_board.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_dictionary.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\barcode.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\charuco_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\detection_based_tracker.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\face.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\graphical_code_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\objdetect.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include\opencv2\structured_light.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include\opencv2\structured_light\graycodepattern.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include\opencv2\structured_light\sinusoidalpattern.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include\opencv2\structured_light\structured_light.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\background_segm.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\legacy\constants_c.h;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\tracking.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\video.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\detail\tracking.detail.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\wechat_qrcode\include\opencv2\wechat_qrcode.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include\opencv2\xfeatures2d.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include\opencv2\xfeatures2d\cuda.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include\opencv2\xfeatures2d\nonfree.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\brightedges.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\color_match.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\deriche_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\disparity_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\edge_drawing.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\edge_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\edgeboxes.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\edgepreserving_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\estimated_covariance.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\fast_hough_transform.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\fast_line_detector.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\find_ellipses.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\fourier_descriptors.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\lsc.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\paillou_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\peilin.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\radon_transform.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\ridgefilter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\run_length_morphology.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\scansegment.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\seeds.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\segmentation.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\slic.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\sparse_match_interpolator.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\structured_edge_detection.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\weighted_median_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco\aruco_calib.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco\charuco.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\bgsegm\include\opencv2\bgsegm.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bgsegm\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired\bioinspired.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired\retina.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired\retinafasttonemapping.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired\transientareassegmentationmodule.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\bif.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\face_alignment.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facemark.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facemarkAAM.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facemarkLBF.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facemark_train.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facerec.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\mace.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\predict_collector.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\feature.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\kalman_filters.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\onlineBoosting.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tldDataset.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tracking.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tracking_by_matching.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tracking_internals.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tracking_legacy.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\twist.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\average_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\block_mean_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\color_moment_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\img_hash_base.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\marr_hildreth_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\phash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\radial_variance_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\misc\objc\gen_dict.json;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\cudabuild\CMakeFiles\dephelper\gen_opencv_objc_source_ios</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generate files for Objective-C bindings (ios)</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd D:\AI\opencv\cudabuild\modules\objc_bindings_generator\ios
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe D:/AI/opencv/opencv-4.10.0/modules/objc/generator/../generator/gen_objc.py -p D:/AI/opencv/opencv-4.10.0/modules/objc/generator/../../python/src2/gen2.py -c D:/AI/opencv/cudabuild/modules/objc_bindings_generator/gen_objc.json -t ios -f opencv2
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch D:/AI/opencv/cudabuild/CMakeFiles/dephelper/gen_opencv_objc_source_ios
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\opencv-4.10.0\modules\objc\generator\gen_objc.py;D:\AI\opencv\opencv-4.10.0\modules\python\src2\gen2.py;D:\AI\opencv\opencv-4.10.0\modules\python\src2\hdr_parser.py;D:\AI\opencv\opencv-4.10.0\modules\objc\generator\templates\cmakelists.template;D:\AI\opencv\opencv-4.10.0\modules\objc\generator\templates\objc_class_body.template;D:\AI\opencv\opencv-4.10.0\modules\objc\generator\templates\objc_class_header.template;D:\AI\opencv\opencv-4.10.0\modules\objc\generator\templates\objc_module_body.template;D:\AI\opencv\opencv-4.10.0\modules\objc\generator\templates\objc_module_header.template;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\backend\parallel_for.openmp.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\backend\parallel_for.tbb.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\parallel\parallel_backend.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\ocl_defs.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\opencl_info.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\opencl_svm.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_clblas.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_clfft.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_core_wrappers.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_gl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\autogenerated\opencl_gl_wrappers.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_clblas.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_clfft.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_core_wrappers.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_gl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_gl_wrappers.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_svm_20.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_svm_definitions.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opencl\runtime\opencl_svm_hsa_extension.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\block.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\border_interpolate.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\color.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\common.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\datamov_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\dynamic_smem.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\emulation.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\filters.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\funcattrib.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\functional.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\limits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\reduce.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\saturate_cast.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\scan.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\simd_functions.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\transform.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\type_traits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\utility.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\vec_distance.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\vec_math.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\vec_traits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\warp.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\warp_reduce.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\warp_shuffle.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\color_detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\reduce.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\reduce_key_val.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\transform_detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\type_traits_detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda\detail\vec_distance_detail.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\affine.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\async.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\base.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\bindings_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\bufferpool.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\check.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\core.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\core_c.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda_stream_accessor.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cuda_types.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cv_cpu_dispatch.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cv_cpu_helper.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvdef.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\cvstd_wrapper.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\directx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\dualquaternion.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\dualquaternion.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\eigen.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\fast_math.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\hal.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\interface.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_avx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_avx512.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_cpp.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_forward.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_lasx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_lsx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_msa.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_neon.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv071.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_010_compat_non-policy.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_010_compat_overloaded-non-policy.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_011_compat.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_compat_overloaded.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_rvv_scalable.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_sse.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_sse_em.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_vsx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\intrin_wasm.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\msa_macros.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\hal\simd_utils.impl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\mat.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\mat.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\matx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\matx.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\neon_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\ocl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\ocl_genbase.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\opengl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\operations.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\optim.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\ovx.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\persistence.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\private.cuda.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\quaternion.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\quaternion.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\saturate.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\simd_intrinsics.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\softfloat.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\sse_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\traits.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\types.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\types_c.h;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utility.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\allocator_stats.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\allocator_stats.impl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\buffer_area.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\configuration.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\filesystem.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\filesystem.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\fp_control.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\fp_control_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\instrumentation.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\lock.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logger.defines.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logger.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\logtag.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\plugin_loader.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\tls.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\trace.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\utils\trace.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\va_intel.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\version.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\vsx_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\async_promise.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\dispatch_helper.impl.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\include\opencv2\core\detail\exception_ptr.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\ArrayUtil.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\ArrayUtil.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\ByteVector.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\ByteVector.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\ByteVectorExt.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\CVObjcUtil.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Converters.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Converters.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\CvType.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\CvType.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\CvTypeExt.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\DMatch.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\DMatch.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Double2.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Double2.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Double3.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Double3.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\DoubleVector.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\DoubleVector.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\DoubleVectorExt.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Float4.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Float4.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Float6.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Float6.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\FloatVector.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\FloatVector.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\FloatVectorExt.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Int4.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Int4.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\IntVector.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\IntVector.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\IntVectorExt.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\KeyPoint.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\KeyPoint.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Mat.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Mat.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatExt.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfByte.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfByte.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfDMatch.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfDMatch.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfDouble.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfDouble.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfFloat.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfFloat.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfFloat4.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfFloat4.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfFloat6.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfFloat6.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfInt.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfInt.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfInt4.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfInt4.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfKeyPoint.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfKeyPoint.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfPoint2f.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfPoint2f.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfPoint2i.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfPoint2i.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfPoint3.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfPoint3.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfPoint3f.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfPoint3f.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfRect2d.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfRect2d.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfRect2i.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfRect2i.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfRotatedRect.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MatOfRotatedRect.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MinMaxLocResult.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\MinMaxLocResult.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point2d.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point2d.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point2f.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point2f.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point2i.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point2i.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point3d.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point3d.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point3f.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point3f.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point3i.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Point3i.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Range.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Range.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Rect2d.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Rect2d.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Rect2f.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Rect2f.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Rect2i.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Rect2i.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\RotatedRect.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\RotatedRect.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Scalar.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Scalar.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Size2d.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Size2d.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Size2f.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Size2f.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Size2i.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Size2i.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\TermCriteria.h;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\TermCriteria.mm;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\common\Typealiases.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\filelist;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\manual\core_manual.hpp;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\ConvertersTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\CoreTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\CvTypeTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\DMatchTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\KeyPointTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\MatTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\MatTestObjc.m;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\Point3Test.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\PointTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\RangeTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\RectTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\RotatedRectTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\ScalarTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\SizeTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\TermCriteriaTest.swift;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\resources\chessboard.jpg;D:\AI\opencv\opencv-4.10.0\modules\core\misc\objc\test\resources\lena.png;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\bindings.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\hal\hal.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\hal\interface.h;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\imgproc.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\imgproc_c.h;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\segmentation.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\types_c.h;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\detail\gcgraph.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include\opencv2\imgproc\detail\legacy.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\objc\common\Moments.h;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\objc\common\Moments.mm;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\objc\test\ImgprocTest.swift;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\objc\test\MomentsTest.swift;D:\AI\opencv\opencv-4.10.0\modules\imgproc\misc\objc\test\Subdiv2DTest.swift;D:\AI\opencv\opencv-4.10.0\modules\ml\include\opencv2\ml.hpp;D:\AI\opencv\opencv-4.10.0\modules\ml\include\opencv2\ml\ml.hpp;D:\AI\opencv\opencv-4.10.0\modules\ml\include\opencv2\ml\ml.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\ml\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include\opencv2\phase_unwrapping.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include\opencv2\phase_unwrapping\histogramphaseunwrapping.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include\opencv2\phase_unwrapping\phase_unwrapping.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\plot\include\opencv2\plot.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\plot\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\all_layers.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dict.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dnn.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\dnn.inl.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer.details.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\layer_reg.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\shape_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\utils\debug_utils.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\utils\inference_engine.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\include\opencv2\dnn\version.hpp;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\features2d\include\opencv2\features2d.hpp;D:\AI\opencv\opencv-4.10.0\modules\features2d\include\opencv2\features2d\features2d.hpp;D:\AI\opencv\opencv-4.10.0\modules\features2d\include\opencv2\features2d\hal\interface.h;D:\AI\opencv\opencv-4.10.0\modules\features2d\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\imgcodecs.hpp;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\imgcodecs_c.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\ios.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\legacy\constants_c.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include\opencv2\imgcodecs\macosx.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\ios\MatConverters.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\ios\MatConverters.mm;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\ios\MatQuickLook.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\ios\MatQuickLook.mm;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\macosx\MatConverters.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\macosx\MatConverters.mm;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\macosx\MatQuickLook.h;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\macosx\MatQuickLook.mm;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\misc\objc\test\ImgcodecsTest.swift;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo\cuda.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo\legacy\constants_c.h;D:\AI\opencv\opencv-4.10.0\modules\photo\include\opencv2\photo\photo.hpp;D:\AI\opencv\opencv-4.10.0\modules\photo\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text\erfilter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text\ocr.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text\swt_text_detection.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include\opencv2\text\textDetector.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\cap_ios.h;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\container_avi.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\legacy\constants_c.h;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\registry.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\utils.private.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\videoio.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\include\opencv2\videoio\videoio_c.h;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\precomp.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_dshow.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_obsensor_capture.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_obsensor\obsensor_stream_channel_interface.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_obsensor\obsensor_uvc_stream_channel.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\src\cap_obsensor\obsensor_stream_channel_msmf.hpp;D:\AI\opencv\opencv-4.10.0\modules\videoio\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\videoio\misc\objc\ios\CvAbstractCamera2.mm;D:\AI\opencv\opencv-4.10.0\modules\videoio\misc\objc\ios\CvCamera2.h;D:\AI\opencv\opencv-4.10.0\modules\videoio\misc\objc\ios\CvPhotoCamera2.m;D:\AI\opencv\opencv-4.10.0\modules\videoio\misc\objc\ios\CvVideoCamera2.mm;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\bm3d_image_denoising.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\dct_image_denoising.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\inpainting.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\oilpainting.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\tonemap.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include\opencv2\xphoto\white_balance.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include\opencv2\calib3d.hpp;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include\opencv2\calib3d\calib3d.hpp;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include\opencv2\calib3d\calib3d_c.h;D:\AI\opencv\opencv-4.10.0\modules\calib3d\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\calib3d\misc\objc\test\Calib3dTest.swift;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_board.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\aruco_dictionary.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\barcode.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\charuco_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\detection_based_tracker.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\face.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\graphical_code_detector.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include\opencv2\objdetect\objdetect.hpp;D:\AI\opencv\opencv-4.10.0\modules\objdetect\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include\opencv2\structured_light.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include\opencv2\structured_light\graycodepattern.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include\opencv2\structured_light\sinusoidalpattern.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include\opencv2\structured_light\structured_light.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\misc\objc\gen_dict.json;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\background_segm.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\legacy\constants_c.h;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\tracking.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\video.hpp;D:\AI\opencv\opencv-4.10.0\modules\video\include\opencv2\video\detail\tracking.detail.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\wechat_qrcode\include\opencv2\wechat_qrcode.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include\opencv2\xfeatures2d.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include\opencv2\xfeatures2d\cuda.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include\opencv2\xfeatures2d\nonfree.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\brightedges.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\color_match.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\deriche_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\disparity_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\edge_drawing.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\edge_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\edgeboxes.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\edgepreserving_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\estimated_covariance.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\fast_hough_transform.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\fast_line_detector.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\find_ellipses.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\fourier_descriptors.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\lsc.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\paillou_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\peilin.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\radon_transform.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\ridgefilter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\run_length_morphology.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\scansegment.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\seeds.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\segmentation.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\slic.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\sparse_match_interpolator.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\structured_edge_detection.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include\opencv2\ximgproc\weighted_median_filter.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco\aruco_calib.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include\opencv2\aruco\charuco.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\bgsegm\include\opencv2\bgsegm.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bgsegm\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired\bioinspired.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired\retina.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired\retinafasttonemapping.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include\opencv2\bioinspired\transientareassegmentationmodule.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\bif.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\face_alignment.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facemark.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facemarkAAM.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facemarkLBF.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facemark_train.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\facerec.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\mace.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include\opencv2\face\predict_collector.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\feature.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\kalman_filters.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\onlineBoosting.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tldDataset.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tracking.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tracking_by_matching.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tracking_internals.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\tracking_legacy.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include\opencv2\tracking\twist.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\misc\objc\gen_dict.json;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\average_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\block_mean_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\color_moment_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\img_hash_base.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\marr_hildreth_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\phash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\radial_variance_hash.hpp;D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\misc\objc\gen_dict.json;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\cudabuild\CMakeFiles\dephelper\gen_opencv_objc_source_ios</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\AI\opencv\cudabuild\CMakeFiles\b813899294a05925b323f4dcb8f385f1\gen_opencv_objc_source_ios.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\cudabuild\CMakeFiles\dephelper\gen_opencv_objc_source_ios;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\cudabuild\modules\objc_bindings_generator\CMakeFiles\gen_opencv_objc_source_ios</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\cudabuild\CMakeFiles\dephelper\gen_opencv_objc_source_ios;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\cudabuild\modules\objc_bindings_generator\CMakeFiles\gen_opencv_objc_source_ios</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\AI\opencv\opencv-4.10.0\modules\objc\generator\gen_objc.py">
    </None>
    <None Include="D:\AI\opencv\opencv-4.10.0\modules\objc\generator\templates\cmakelists.template">
    </None>
    <None Include="D:\AI\opencv\cudabuild\modules\objc_bindings_generator\gen_objc.json">
    </None>
    <None Include="D:\AI\opencv\cudabuild\modules\objc_bindings_generator\CMakeFiles\gen_opencv_objc_source_ios">
    </None>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>