<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Input reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#groups">Modules</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">Input reference</div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>This is the reference documentation for input related functions and types. For more task-oriented information, see the <a class="el" href="input_guide.html">Input guide</a>. </p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="groups" name="groups"></a>
Modules</h2></td></tr>
<tr class="memitem:group__gamepad__axes" id="r_group__gamepad__axes"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__axes.html">Gamepad axes</a></td></tr>
<tr class="memdesc:group__gamepad__axes"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gamepad axes. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group__gamepad__buttons" id="r_group__gamepad__buttons"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__buttons.html">Gamepad buttons</a></td></tr>
<tr class="memdesc:group__gamepad__buttons"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gamepad buttons. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group__hat__state" id="r_group__hat__state"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html">Joystick hat states</a></td></tr>
<tr class="memdesc:group__hat__state"><td class="mdescLeft">&#160;</td><td class="mdescRight">Joystick hat states. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group__joysticks" id="r_group__joysticks"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html">Joysticks</a></td></tr>
<tr class="memdesc:group__joysticks"><td class="mdescLeft">&#160;</td><td class="mdescRight">Joystick IDs. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group__keys" id="r_group__keys"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__keys.html">Keyboard key tokens</a></td></tr>
<tr class="memdesc:group__keys"><td class="mdescLeft">&#160;</td><td class="mdescRight">Keyboard key tokens. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group__mods" id="r_group__mods"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__mods.html">Modifier key flags</a></td></tr>
<tr class="memdesc:group__mods"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modifier key flags. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group__buttons" id="r_group__buttons"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html">Mouse buttons</a></td></tr>
<tr class="memdesc:group__buttons"><td class="mdescLeft">&#160;</td><td class="mdescRight">Mouse button IDs. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:group__shapes" id="r_group__shapes"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html">Standard cursor shapes</a></td></tr>
<tr class="memdesc:group__shapes"><td class="mdescLeft">&#160;</td><td class="mdescRight">Standard system cursor shapes. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:ga99aacc875b6b27a072552631e13775c7" id="r_ga99aacc875b6b27a072552631e13775c7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga99aacc875b6b27a072552631e13775c7">GLFW_KEY_UNKNOWN</a>&#160;&#160;&#160;-1</td></tr>
<tr class="separator:ga99aacc875b6b27a072552631e13775c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga89261ae18c75e863aaf2656ecdd238f4" id="r_ga89261ae18c75e863aaf2656ecdd238f4"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a></td></tr>
<tr class="memdesc:ga89261ae18c75e863aaf2656ecdd238f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Opaque cursor object.  <br /></td></tr>
<tr class="separator:ga89261ae18c75e863aaf2656ecdd238f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0184dcb59f6d85d735503dcaae809727" id="r_ga0184dcb59f6d85d735503dcaae809727"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga0184dcb59f6d85d735503dcaae809727">GLFWmousebuttonfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int button, int action, int mods)</td></tr>
<tr class="memdesc:ga0184dcb59f6d85d735503dcaae809727"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for mouse button callbacks.  <br /></td></tr>
<tr class="separator:ga0184dcb59f6d85d735503dcaae809727"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad6fae41b3ac2e4209aaa87b596c57f68" id="r_gad6fae41b3ac2e4209aaa87b596c57f68"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gad6fae41b3ac2e4209aaa87b596c57f68">GLFWcursorposfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, double xpos, double ypos)</td></tr>
<tr class="memdesc:gad6fae41b3ac2e4209aaa87b596c57f68"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for cursor position callbacks.  <br /></td></tr>
<tr class="separator:gad6fae41b3ac2e4209aaa87b596c57f68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa93dc4818ac9ab32532909d53a337cbe" id="r_gaa93dc4818ac9ab32532909d53a337cbe"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaa93dc4818ac9ab32532909d53a337cbe">GLFWcursorenterfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int entered)</td></tr>
<tr class="memdesc:gaa93dc4818ac9ab32532909d53a337cbe"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for cursor enter/leave callbacks.  <br /></td></tr>
<tr class="separator:gaa93dc4818ac9ab32532909d53a337cbe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf656112c33de3efdb227fa58f0134cf5" id="r_gaf656112c33de3efdb227fa58f0134cf5"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaf656112c33de3efdb227fa58f0134cf5">GLFWscrollfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, double xoffset, double yoffset)</td></tr>
<tr class="memdesc:gaf656112c33de3efdb227fa58f0134cf5"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for scroll callbacks.  <br /></td></tr>
<tr class="separator:gaf656112c33de3efdb227fa58f0134cf5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5bd751b27b90f865d2ea613533f0453c" id="r_ga5bd751b27b90f865d2ea613533f0453c"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga5bd751b27b90f865d2ea613533f0453c">GLFWkeyfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int key, int scancode, int action, int mods)</td></tr>
<tr class="memdesc:ga5bd751b27b90f865d2ea613533f0453c"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for keyboard key callbacks.  <br /></td></tr>
<tr class="separator:ga5bd751b27b90f865d2ea613533f0453c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1ab90a55cf3f58639b893c0f4118cb6e" id="r_ga1ab90a55cf3f58639b893c0f4118cb6e"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga1ab90a55cf3f58639b893c0f4118cb6e">GLFWcharfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, unsigned int codepoint)</td></tr>
<tr class="memdesc:ga1ab90a55cf3f58639b893c0f4118cb6e"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for Unicode character callbacks.  <br /></td></tr>
<tr class="separator:ga1ab90a55cf3f58639b893c0f4118cb6e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac3cf64f90b6219c05ac7b7822d5a4b8f" id="r_gac3cf64f90b6219c05ac7b7822d5a4b8f"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gac3cf64f90b6219c05ac7b7822d5a4b8f">GLFWcharmodsfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, unsigned int codepoint, int mods)</td></tr>
<tr class="memdesc:gac3cf64f90b6219c05ac7b7822d5a4b8f"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for Unicode character with modifiers callbacks.  <br /></td></tr>
<tr class="separator:gac3cf64f90b6219c05ac7b7822d5a4b8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaba73c3274062c18723b7f05862d94b2" id="r_gaaba73c3274062c18723b7f05862d94b2"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaaba73c3274062c18723b7f05862d94b2">GLFWdropfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int path_count, const char *paths[])</td></tr>
<tr class="memdesc:gaaba73c3274062c18723b7f05862d94b2"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for path drop callbacks.  <br /></td></tr>
<tr class="separator:gaaba73c3274062c18723b7f05862d94b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa21ad5986ae9a26077a40142efb56243" id="r_gaa21ad5986ae9a26077a40142efb56243"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaa21ad5986ae9a26077a40142efb56243">GLFWjoystickfun</a>) (int jid, int event)</td></tr>
<tr class="memdesc:gaa21ad5986ae9a26077a40142efb56243"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for joystick configuration callbacks.  <br /></td></tr>
<tr class="separator:gaa21ad5986ae9a26077a40142efb56243"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga61acfb1f28f751438dd221225c5e725d" id="r_ga61acfb1f28f751438dd221225c5e725d"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="struct_g_l_f_wgamepadstate.html">GLFWgamepadstate</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga61acfb1f28f751438dd221225c5e725d">GLFWgamepadstate</a></td></tr>
<tr class="memdesc:ga61acfb1f28f751438dd221225c5e725d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gamepad input state.  <br /></td></tr>
<tr class="separator:ga61acfb1f28f751438dd221225c5e725d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaf5b859dbe19bdf434e42695ea45cc5f4" id="r_gaf5b859dbe19bdf434e42695ea45cc5f4"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaf5b859dbe19bdf434e42695ea45cc5f4">glfwGetInputMode</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int mode)</td></tr>
<tr class="memdesc:gaf5b859dbe19bdf434e42695ea45cc5f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the value of an input option for the specified window.  <br /></td></tr>
<tr class="separator:gaf5b859dbe19bdf434e42695ea45cc5f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa92336e173da9c8834558b54ee80563b" id="r_gaa92336e173da9c8834558b54ee80563b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int mode, int value)</td></tr>
<tr class="memdesc:gaa92336e173da9c8834558b54ee80563b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets an input option for the specified window.  <br /></td></tr>
<tr class="separator:gaa92336e173da9c8834558b54ee80563b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae4ee0dbd0d256183e1ea4026d897e1c2" id="r_gae4ee0dbd0d256183e1ea4026d897e1c2"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gae4ee0dbd0d256183e1ea4026d897e1c2">glfwRawMouseMotionSupported</a> (void)</td></tr>
<tr class="memdesc:gae4ee0dbd0d256183e1ea4026d897e1c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether raw mouse motion is supported.  <br /></td></tr>
<tr class="separator:gae4ee0dbd0d256183e1ea4026d897e1c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeaed62e69c3bd62b7ff8f7b19913ce4f" id="r_gaeaed62e69c3bd62b7ff8f7b19913ce4f"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaeaed62e69c3bd62b7ff8f7b19913ce4f">glfwGetKeyName</a> (int key, int scancode)</td></tr>
<tr class="memdesc:gaeaed62e69c3bd62b7ff8f7b19913ce4f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the layout-specific name of the specified printable key.  <br /></td></tr>
<tr class="separator:gaeaed62e69c3bd62b7ff8f7b19913ce4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga67ddd1b7dcbbaff03e4a76c0ea67103a" id="r_ga67ddd1b7dcbbaff03e4a76c0ea67103a"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga67ddd1b7dcbbaff03e4a76c0ea67103a">glfwGetKeyScancode</a> (int key)</td></tr>
<tr class="memdesc:ga67ddd1b7dcbbaff03e4a76c0ea67103a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the platform-specific scancode of the specified key.  <br /></td></tr>
<tr class="separator:ga67ddd1b7dcbbaff03e4a76c0ea67103a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadd341da06bc8d418b4dc3a3518af9ad2" id="r_gadd341da06bc8d418b4dc3a3518af9ad2"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gadd341da06bc8d418b4dc3a3518af9ad2">glfwGetKey</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int key)</td></tr>
<tr class="memdesc:gadd341da06bc8d418b4dc3a3518af9ad2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the last reported state of a keyboard key for the specified window.  <br /></td></tr>
<tr class="separator:gadd341da06bc8d418b4dc3a3518af9ad2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac1473feacb5996c01a7a5a33b5066704" id="r_gac1473feacb5996c01a7a5a33b5066704"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gac1473feacb5996c01a7a5a33b5066704">glfwGetMouseButton</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int button)</td></tr>
<tr class="memdesc:gac1473feacb5996c01a7a5a33b5066704"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the last reported state of a mouse button for the specified window.  <br /></td></tr>
<tr class="separator:gac1473feacb5996c01a7a5a33b5066704"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga01d37b6c40133676b9cea60ca1d7c0cc" id="r_ga01d37b6c40133676b9cea60ca1d7c0cc"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga01d37b6c40133676b9cea60ca1d7c0cc">glfwGetCursorPos</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, double *xpos, double *ypos)</td></tr>
<tr class="memdesc:ga01d37b6c40133676b9cea60ca1d7c0cc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the position of the cursor relative to the content area of the window.  <br /></td></tr>
<tr class="separator:ga01d37b6c40133676b9cea60ca1d7c0cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga04b03af936d906ca123c8f4ee08b39e7" id="r_ga04b03af936d906ca123c8f4ee08b39e7"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga04b03af936d906ca123c8f4ee08b39e7">glfwSetCursorPos</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, double xpos, double ypos)</td></tr>
<tr class="memdesc:ga04b03af936d906ca123c8f4ee08b39e7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the position of the cursor, relative to the content area of the window.  <br /></td></tr>
<tr class="separator:ga04b03af936d906ca123c8f4ee08b39e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga556f604f73af156c0db0e97c081373c3" id="r_ga556f604f73af156c0db0e97c081373c3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga556f604f73af156c0db0e97c081373c3">glfwCreateCursor</a> (const <a class="el" href="struct_g_l_f_wimage.html">GLFWimage</a> *image, int xhot, int yhot)</td></tr>
<tr class="memdesc:ga556f604f73af156c0db0e97c081373c3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a custom cursor.  <br /></td></tr>
<tr class="separator:ga556f604f73af156c0db0e97c081373c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf2fb2eb2c9dd842d1cef8a34e3c6403e" id="r_gaf2fb2eb2c9dd842d1cef8a34e3c6403e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaf2fb2eb2c9dd842d1cef8a34e3c6403e">glfwCreateStandardCursor</a> (int shape)</td></tr>
<tr class="memdesc:gaf2fb2eb2c9dd842d1cef8a34e3c6403e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a cursor with a standard shape.  <br /></td></tr>
<tr class="separator:gaf2fb2eb2c9dd842d1cef8a34e3c6403e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga81b952cd1764274d0db7fb3c5a79ba6a" id="r_ga81b952cd1764274d0db7fb3c5a79ba6a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga81b952cd1764274d0db7fb3c5a79ba6a">glfwDestroyCursor</a> (<a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a> *cursor)</td></tr>
<tr class="memdesc:ga81b952cd1764274d0db7fb3c5a79ba6a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Destroys a cursor.  <br /></td></tr>
<tr class="separator:ga81b952cd1764274d0db7fb3c5a79ba6a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad3b4f38c8d5dae036bc8fa959e18343e" id="r_gad3b4f38c8d5dae036bc8fa959e18343e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gad3b4f38c8d5dae036bc8fa959e18343e">glfwSetCursor</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a> *cursor)</td></tr>
<tr class="memdesc:gad3b4f38c8d5dae036bc8fa959e18343e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the cursor for the window.  <br /></td></tr>
<tr class="separator:gad3b4f38c8d5dae036bc8fa959e18343e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1caf18159767e761185e49a3be019f8d" id="r_ga1caf18159767e761185e49a3be019f8d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#ga5bd751b27b90f865d2ea613533f0453c">GLFWkeyfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga1caf18159767e761185e49a3be019f8d">glfwSetKeyCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#ga5bd751b27b90f865d2ea613533f0453c">GLFWkeyfun</a> callback)</td></tr>
<tr class="memdesc:ga1caf18159767e761185e49a3be019f8d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the key callback.  <br /></td></tr>
<tr class="separator:ga1caf18159767e761185e49a3be019f8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab25c4a220fd8f5717718dbc487828996" id="r_gab25c4a220fd8f5717718dbc487828996"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#ga1ab90a55cf3f58639b893c0f4118cb6e">GLFWcharfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gab25c4a220fd8f5717718dbc487828996">glfwSetCharCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#ga1ab90a55cf3f58639b893c0f4118cb6e">GLFWcharfun</a> callback)</td></tr>
<tr class="memdesc:gab25c4a220fd8f5717718dbc487828996"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the Unicode character callback.  <br /></td></tr>
<tr class="separator:gab25c4a220fd8f5717718dbc487828996"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0b7f4ad13c2b17435ff13b6dcfb4e43c" id="r_ga0b7f4ad13c2b17435ff13b6dcfb4e43c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#gac3cf64f90b6219c05ac7b7822d5a4b8f">GLFWcharmodsfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga0b7f4ad13c2b17435ff13b6dcfb4e43c">glfwSetCharModsCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#gac3cf64f90b6219c05ac7b7822d5a4b8f">GLFWcharmodsfun</a> callback)</td></tr>
<tr class="memdesc:ga0b7f4ad13c2b17435ff13b6dcfb4e43c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the Unicode character with modifiers callback.  <br /></td></tr>
<tr class="separator:ga0b7f4ad13c2b17435ff13b6dcfb4e43c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6ab84420974d812bee700e45284a723c" id="r_ga6ab84420974d812bee700e45284a723c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#ga0184dcb59f6d85d735503dcaae809727">GLFWmousebuttonfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga6ab84420974d812bee700e45284a723c">glfwSetMouseButtonCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#ga0184dcb59f6d85d735503dcaae809727">GLFWmousebuttonfun</a> callback)</td></tr>
<tr class="memdesc:ga6ab84420974d812bee700e45284a723c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the mouse button callback.  <br /></td></tr>
<tr class="separator:ga6ab84420974d812bee700e45284a723c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac1f879ab7435d54d4d79bb469fe225d7" id="r_gac1f879ab7435d54d4d79bb469fe225d7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#gad6fae41b3ac2e4209aaa87b596c57f68">GLFWcursorposfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gac1f879ab7435d54d4d79bb469fe225d7">glfwSetCursorPosCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#gad6fae41b3ac2e4209aaa87b596c57f68">GLFWcursorposfun</a> callback)</td></tr>
<tr class="memdesc:gac1f879ab7435d54d4d79bb469fe225d7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the cursor position callback.  <br /></td></tr>
<tr class="separator:gac1f879ab7435d54d4d79bb469fe225d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad27f8ad0142c038a281466c0966817d8" id="r_gad27f8ad0142c038a281466c0966817d8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#gaa93dc4818ac9ab32532909d53a337cbe">GLFWcursorenterfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gad27f8ad0142c038a281466c0966817d8">glfwSetCursorEnterCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#gaa93dc4818ac9ab32532909d53a337cbe">GLFWcursorenterfun</a> callback)</td></tr>
<tr class="memdesc:gad27f8ad0142c038a281466c0966817d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the cursor enter/leave callback.  <br /></td></tr>
<tr class="separator:gad27f8ad0142c038a281466c0966817d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga571e45a030ae4061f746ed56cb76aede" id="r_ga571e45a030ae4061f746ed56cb76aede"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#gaf656112c33de3efdb227fa58f0134cf5">GLFWscrollfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga571e45a030ae4061f746ed56cb76aede">glfwSetScrollCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#gaf656112c33de3efdb227fa58f0134cf5">GLFWscrollfun</a> callback)</td></tr>
<tr class="memdesc:ga571e45a030ae4061f746ed56cb76aede"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the scroll callback.  <br /></td></tr>
<tr class="separator:ga571e45a030ae4061f746ed56cb76aede"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab773f0ee0a07cff77a210cea40bc1f6b" id="r_gab773f0ee0a07cff77a210cea40bc1f6b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#gaaba73c3274062c18723b7f05862d94b2">GLFWdropfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gab773f0ee0a07cff77a210cea40bc1f6b">glfwSetDropCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__input.html#gaaba73c3274062c18723b7f05862d94b2">GLFWdropfun</a> callback)</td></tr>
<tr class="memdesc:gab773f0ee0a07cff77a210cea40bc1f6b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the path drop callback.  <br /></td></tr>
<tr class="separator:gab773f0ee0a07cff77a210cea40bc1f6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaed0966cee139d815317f9ffcba64c9f1" id="r_gaed0966cee139d815317f9ffcba64c9f1"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a> (int jid)</td></tr>
<tr class="memdesc:gaed0966cee139d815317f9ffcba64c9f1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the specified joystick is present.  <br /></td></tr>
<tr class="separator:gaed0966cee139d815317f9ffcba64c9f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeb1c0191d3140a233a682987c61eb408" id="r_gaeb1c0191d3140a233a682987c61eb408"><td class="memItemLeft" align="right" valign="top">const float *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaeb1c0191d3140a233a682987c61eb408">glfwGetJoystickAxes</a> (int jid, int *count)</td></tr>
<tr class="memdesc:gaeb1c0191d3140a233a682987c61eb408"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the values of all axes of the specified joystick.  <br /></td></tr>
<tr class="separator:gaeb1c0191d3140a233a682987c61eb408"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5ffe34739d3dc97efe432ed2d81d9938" id="r_ga5ffe34739d3dc97efe432ed2d81d9938"><td class="memItemLeft" align="right" valign="top">const unsigned char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga5ffe34739d3dc97efe432ed2d81d9938">glfwGetJoystickButtons</a> (int jid, int *count)</td></tr>
<tr class="memdesc:ga5ffe34739d3dc97efe432ed2d81d9938"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the state of all buttons of the specified joystick.  <br /></td></tr>
<tr class="separator:ga5ffe34739d3dc97efe432ed2d81d9938"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga06e660841b3e79c54da4f54a932c5a2c" id="r_ga06e660841b3e79c54da4f54a932c5a2c"><td class="memItemLeft" align="right" valign="top">const unsigned char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga06e660841b3e79c54da4f54a932c5a2c">glfwGetJoystickHats</a> (int jid, int *count)</td></tr>
<tr class="memdesc:ga06e660841b3e79c54da4f54a932c5a2c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the state of all hats of the specified joystick.  <br /></td></tr>
<tr class="separator:ga06e660841b3e79c54da4f54a932c5a2c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac6a8e769e18e0bcfa9097793fc2c3978" id="r_gac6a8e769e18e0bcfa9097793fc2c3978"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gac6a8e769e18e0bcfa9097793fc2c3978">glfwGetJoystickName</a> (int jid)</td></tr>
<tr class="memdesc:gac6a8e769e18e0bcfa9097793fc2c3978"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the name of the specified joystick.  <br /></td></tr>
<tr class="separator:gac6a8e769e18e0bcfa9097793fc2c3978"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6659411aec3c7fcef27780e2cb2d9600" id="r_ga6659411aec3c7fcef27780e2cb2d9600"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga6659411aec3c7fcef27780e2cb2d9600">glfwGetJoystickGUID</a> (int jid)</td></tr>
<tr class="memdesc:ga6659411aec3c7fcef27780e2cb2d9600"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the SDL compatible GUID of the specified joystick.  <br /></td></tr>
<tr class="separator:ga6659411aec3c7fcef27780e2cb2d9600"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6b2f72d64d636b48a727b437cbb7489e" id="r_ga6b2f72d64d636b48a727b437cbb7489e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga6b2f72d64d636b48a727b437cbb7489e">glfwSetJoystickUserPointer</a> (int jid, void *pointer)</td></tr>
<tr class="memdesc:ga6b2f72d64d636b48a727b437cbb7489e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the user pointer of the specified joystick.  <br /></td></tr>
<tr class="separator:ga6b2f72d64d636b48a727b437cbb7489e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga18cefd7265d1fa04f3fd38a6746db5f3" id="r_ga18cefd7265d1fa04f3fd38a6746db5f3"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga18cefd7265d1fa04f3fd38a6746db5f3">glfwGetJoystickUserPointer</a> (int jid)</td></tr>
<tr class="memdesc:ga18cefd7265d1fa04f3fd38a6746db5f3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the user pointer of the specified joystick.  <br /></td></tr>
<tr class="separator:ga18cefd7265d1fa04f3fd38a6746db5f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad0f676860f329d80f7e47e9f06a96f00" id="r_gad0f676860f329d80f7e47e9f06a96f00"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gad0f676860f329d80f7e47e9f06a96f00">glfwJoystickIsGamepad</a> (int jid)</td></tr>
<tr class="memdesc:gad0f676860f329d80f7e47e9f06a96f00"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the specified joystick has a gamepad mapping.  <br /></td></tr>
<tr class="separator:gad0f676860f329d80f7e47e9f06a96f00"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c" id="r_ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__input.html#gaa21ad5986ae9a26077a40142efb56243">GLFWjoystickfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c">glfwSetJoystickCallback</a> (<a class="el" href="group__input.html#gaa21ad5986ae9a26077a40142efb56243">GLFWjoystickfun</a> callback)</td></tr>
<tr class="memdesc:ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the joystick configuration callback.  <br /></td></tr>
<tr class="separator:ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaed5104612f2fa8e66aa6e846652ad00f" id="r_gaed5104612f2fa8e66aa6e846652ad00f"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaed5104612f2fa8e66aa6e846652ad00f">glfwUpdateGamepadMappings</a> (const char *string)</td></tr>
<tr class="memdesc:gaed5104612f2fa8e66aa6e846652ad00f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds the specified SDL_GameControllerDB gamepad mappings.  <br /></td></tr>
<tr class="separator:gaed5104612f2fa8e66aa6e846652ad00f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8aea73a1a25cc6c0486a617019f56728" id="r_ga8aea73a1a25cc6c0486a617019f56728"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga8aea73a1a25cc6c0486a617019f56728">glfwGetGamepadName</a> (int jid)</td></tr>
<tr class="memdesc:ga8aea73a1a25cc6c0486a617019f56728"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the human-readable gamepad name for the specified joystick.  <br /></td></tr>
<tr class="separator:ga8aea73a1a25cc6c0486a617019f56728"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadccddea8bce6113fa459de379ddaf051" id="r_gadccddea8bce6113fa459de379ddaf051"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gadccddea8bce6113fa459de379ddaf051">glfwGetGamepadState</a> (int jid, <a class="el" href="struct_g_l_f_wgamepadstate.html">GLFWgamepadstate</a> *state)</td></tr>
<tr class="memdesc:gadccddea8bce6113fa459de379ddaf051"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the state of the specified joystick remapped as a gamepad.  <br /></td></tr>
<tr class="separator:gadccddea8bce6113fa459de379ddaf051"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaba1f022c5eb07dfac421df34cdcd31dd" id="r_gaba1f022c5eb07dfac421df34cdcd31dd"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaba1f022c5eb07dfac421df34cdcd31dd">glfwSetClipboardString</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, const char *string)</td></tr>
<tr class="memdesc:gaba1f022c5eb07dfac421df34cdcd31dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the clipboard to the specified string.  <br /></td></tr>
<tr class="separator:gaba1f022c5eb07dfac421df34cdcd31dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga71a5b20808ea92193d65c21b82580355" id="r_ga71a5b20808ea92193d65c21b82580355"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga71a5b20808ea92193d65c21b82580355">glfwGetClipboardString</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga71a5b20808ea92193d65c21b82580355"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the contents of the clipboard as a string.  <br /></td></tr>
<tr class="separator:ga71a5b20808ea92193d65c21b82580355"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa6cf4e7a77158a3b8fd00328b1720a4a" id="r_gaa6cf4e7a77158a3b8fd00328b1720a4a"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaa6cf4e7a77158a3b8fd00328b1720a4a">glfwGetTime</a> (void)</td></tr>
<tr class="memdesc:gaa6cf4e7a77158a3b8fd00328b1720a4a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the GLFW time.  <br /></td></tr>
<tr class="separator:gaa6cf4e7a77158a3b8fd00328b1720a4a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf59589ef6e8b8c8b5ad184b25afd4dc0" id="r_gaf59589ef6e8b8c8b5ad184b25afd4dc0"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#gaf59589ef6e8b8c8b5ad184b25afd4dc0">glfwSetTime</a> (double time)</td></tr>
<tr class="memdesc:gaf59589ef6e8b8c8b5ad184b25afd4dc0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the GLFW time.  <br /></td></tr>
<tr class="separator:gaf59589ef6e8b8c8b5ad184b25afd4dc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga09b2bd37d328e0b9456c7ec575cc26aa" id="r_ga09b2bd37d328e0b9456c7ec575cc26aa"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga09b2bd37d328e0b9456c7ec575cc26aa">glfwGetTimerValue</a> (void)</td></tr>
<tr class="memdesc:ga09b2bd37d328e0b9456c7ec575cc26aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the current value of the raw timer.  <br /></td></tr>
<tr class="separator:ga09b2bd37d328e0b9456c7ec575cc26aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3289ee876572f6e91f06df3a24824443" id="r_ga3289ee876572f6e91f06df3a24824443"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__input.html#ga3289ee876572f6e91f06df3a24824443">glfwGetTimerFrequency</a> (void)</td></tr>
<tr class="memdesc:ga3289ee876572f6e91f06df3a24824443"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the frequency, in Hz, of the raw timer.  <br /></td></tr>
<tr class="separator:ga3289ee876572f6e91f06df3a24824443"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="gada11d965c4da13090ad336e030e4d11f" name="gada11d965c4da13090ad336e030e4d11f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gada11d965c4da13090ad336e030e4d11f">&#9670;&#160;</a></span>GLFW_RELEASE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_RELEASE&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The key or mouse button was released. </p>

</div>
</div>
<a id="ga2485743d0b59df3791c45951c4195265" name="ga2485743d0b59df3791c45951c4195265"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2485743d0b59df3791c45951c4195265">&#9670;&#160;</a></span>GLFW_PRESS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_PRESS&#160;&#160;&#160;1</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The key or mouse button was pressed. </p>

</div>
</div>
<a id="gac96fd3b9fc66c6f0eebaf6532595338f" name="gac96fd3b9fc66c6f0eebaf6532595338f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac96fd3b9fc66c6f0eebaf6532595338f">&#9670;&#160;</a></span>GLFW_REPEAT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_REPEAT&#160;&#160;&#160;2</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The key was held down until it repeated. </p>

</div>
</div>
<a id="ga99aacc875b6b27a072552631e13775c7" name="ga99aacc875b6b27a072552631e13775c7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga99aacc875b6b27a072552631e13775c7">&#9670;&#160;</a></span>GLFW_KEY_UNKNOWN</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_KEY_UNKNOWN&#160;&#160;&#160;-1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Typedef Documentation</h2>
<a id="ga89261ae18c75e863aaf2656ecdd238f4" name="ga89261ae18c75e863aaf2656ecdd238f4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga89261ae18c75e863aaf2656ecdd238f4">&#9670;&#160;</a></span>GLFWcursor</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a> <a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Opaque cursor object.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#cursor_object">Cursor objects</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.1. </dd></dl>

</div>
</div>
<a id="ga0184dcb59f6d85d735503dcaae809727" name="ga0184dcb59f6d85d735503dcaae809727"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga0184dcb59f6d85d735503dcaae809727">&#9670;&#160;</a></span>GLFWmousebuttonfun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWmousebuttonfun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int button, int action, int mods)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for mouse button callback functions. A mouse button callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> button, <span class="keywordtype">int</span> action, <span class="keywordtype">int</span> mods)</div>
<div class="ttc" id="agroup__window_html_ga3c96d80d363e67d13a41b5d1821f3242"><div class="ttname"><a href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a></div><div class="ttdeci">struct GLFWwindow GLFWwindow</div><div class="ttdoc">Opaque window object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1403</div></div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window that received the event. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">button</td><td>The <a class="el" href="group__buttons.html">mouse button</a> that was pressed or released. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">action</td><td>One of <code>GLFW_PRESS</code> or <code>GLFW_RELEASE</code>. Future releases may add more actions. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">mods</td><td>Bit field describing which <a class="el" href="group__mods.html">modifier keys</a> were held down.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#input_mouse_button">Mouse button input</a> </dd>
<dd>
<a class="el" href="group__input.html#ga6ab84420974d812bee700e45284a723c">glfwSetMouseButtonCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. <b>GLFW 3:</b> Added window handle and modifier mask parameters. </dd></dl>

</div>
</div>
<a id="gad6fae41b3ac2e4209aaa87b596c57f68" name="gad6fae41b3ac2e4209aaa87b596c57f68"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad6fae41b3ac2e4209aaa87b596c57f68">&#9670;&#160;</a></span>GLFWcursorposfun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWcursorposfun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, double xpos, double ypos)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for cursor position callbacks. A cursor position callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">double</span> xpos, <span class="keywordtype">double</span> ypos);</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window that received the event. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">xpos</td><td>The new cursor x-coordinate, relative to the left edge of the content area. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ypos</td><td>The new cursor y-coordinate, relative to the top edge of the content area.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#cursor_pos">Cursor position</a> </dd>
<dd>
<a class="el" href="group__input.html#gac1f879ab7435d54d4d79bb469fe225d7">glfwSetCursorPosCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. Replaces <code>GLFWmouseposfun</code>. </dd></dl>

</div>
</div>
<a id="gaa93dc4818ac9ab32532909d53a337cbe" name="gaa93dc4818ac9ab32532909d53a337cbe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa93dc4818ac9ab32532909d53a337cbe">&#9670;&#160;</a></span>GLFWcursorenterfun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWcursorenterfun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int entered)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for cursor enter/leave callbacks. A cursor enter/leave callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> entered)</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window that received the event. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">entered</td><td><code>GLFW_TRUE</code> if the cursor entered the window's content area, or <code>GLFW_FALSE</code> if it left it.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#cursor_enter">Cursor enter/leave events</a> </dd>
<dd>
<a class="el" href="group__input.html#gad27f8ad0142c038a281466c0966817d8">glfwSetCursorEnterCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gaf656112c33de3efdb227fa58f0134cf5" name="gaf656112c33de3efdb227fa58f0134cf5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf656112c33de3efdb227fa58f0134cf5">&#9670;&#160;</a></span>GLFWscrollfun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWscrollfun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, double xoffset, double yoffset)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for scroll callbacks. A scroll callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">double</span> xoffset, <span class="keywordtype">double</span> yoffset)</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window that received the event. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">xoffset</td><td>The scroll offset along the x-axis. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">yoffset</td><td>The scroll offset along the y-axis.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#scrolling">Scroll input</a> </dd>
<dd>
<a class="el" href="group__input.html#ga571e45a030ae4061f746ed56cb76aede">glfwSetScrollCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. Replaces <code>GLFWmousewheelfun</code>. </dd></dl>

</div>
</div>
<a id="ga5bd751b27b90f865d2ea613533f0453c" name="ga5bd751b27b90f865d2ea613533f0453c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5bd751b27b90f865d2ea613533f0453c">&#9670;&#160;</a></span>GLFWkeyfun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWkeyfun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int key, int scancode, int action, int mods)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for keyboard key callbacks. A keyboard key callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> key, <span class="keywordtype">int</span> scancode, <span class="keywordtype">int</span> action, <span class="keywordtype">int</span> mods)</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window that received the event. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">key</td><td>The <a class="el" href="group__keys.html">keyboard key</a> that was pressed or released. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">scancode</td><td>The platform-specific scancode of the key. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">action</td><td><code>GLFW_PRESS</code>, <code>GLFW_RELEASE</code> or <code>GLFW_REPEAT</code>. Future releases may add more actions. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">mods</td><td>Bit field describing which <a class="el" href="group__mods.html">modifier keys</a> were held down.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#input_key">Key input</a> </dd>
<dd>
<a class="el" href="group__input.html#ga1caf18159767e761185e49a3be019f8d">glfwSetKeyCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. <b>GLFW 3:</b> Added window handle, scancode and modifier mask parameters. </dd></dl>

</div>
</div>
<a id="ga1ab90a55cf3f58639b893c0f4118cb6e" name="ga1ab90a55cf3f58639b893c0f4118cb6e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1ab90a55cf3f58639b893c0f4118cb6e">&#9670;&#160;</a></span>GLFWcharfun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWcharfun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, unsigned int codepoint)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for Unicode character callbacks. A Unicode character callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> codepoint)</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window that received the event. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">codepoint</td><td>The Unicode code point of the character.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#input_char">Text input</a> </dd>
<dd>
<a class="el" href="group__input.html#gab25c4a220fd8f5717718dbc487828996">glfwSetCharCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 2.4. <b>GLFW 3:</b> Added window handle parameter. </dd></dl>

</div>
</div>
<a id="gac3cf64f90b6219c05ac7b7822d5a4b8f" name="gac3cf64f90b6219c05ac7b7822d5a4b8f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac3cf64f90b6219c05ac7b7822d5a4b8f">&#9670;&#160;</a></span>GLFWcharmodsfun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWcharmodsfun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, unsigned int codepoint, int mods)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for Unicode character with modifiers callbacks. It is called for each input character, regardless of what modifier keys are held down. A Unicode character with modifiers callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> codepoint, <span class="keywordtype">int</span> mods)</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window that received the event. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">codepoint</td><td>The Unicode code point of the character. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">mods</td><td>Bit field describing which <a class="el" href="group__mods.html">modifier keys</a> were held down.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#input_char">Text input</a> </dd>
<dd>
<a class="el" href="group__input.html#ga0b7f4ad13c2b17435ff13b6dcfb4e43c">glfwSetCharModsCallback</a></dd></dl>
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000001">Deprecated:</a></b></dt><dd>Scheduled for removal in version 4.0.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.1. </dd></dl>

</div>
</div>
<a id="gaaba73c3274062c18723b7f05862d94b2" name="gaaba73c3274062c18723b7f05862d94b2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaaba73c3274062c18723b7f05862d94b2">&#9670;&#160;</a></span>GLFWdropfun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWdropfun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int path_count, const char *paths[])</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for path drop callbacks. A path drop callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> path_count, <span class="keyword">const</span> <span class="keywordtype">char</span>* paths[])</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window that received the event. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">path_count</td><td>The number of dropped paths. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">paths</td><td>The UTF-8 encoded file and/or directory path names.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The path array and its strings are valid until the callback function returns.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#path_drop">Path drop input</a> </dd>
<dd>
<a class="el" href="group__input.html#gab773f0ee0a07cff77a210cea40bc1f6b">glfwSetDropCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.1. </dd></dl>

</div>
</div>
<a id="gaa21ad5986ae9a26077a40142efb56243" name="gaa21ad5986ae9a26077a40142efb56243"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa21ad5986ae9a26077a40142efb56243">&#9670;&#160;</a></span>GLFWjoystickfun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWjoystickfun) (int jid, int event)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for joystick configuration callbacks. A joystick configuration callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<span class="keywordtype">int</span> jid, <span class="keywordtype">int</span> event)</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">jid</td><td>The joystick that was connected or disconnected. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">event</td><td>One of <code>GLFW_CONNECTED</code> or <code>GLFW_DISCONNECTED</code>. Future releases may add more events.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#joystick_event">Joystick configuration changes</a> </dd>
<dd>
<a class="el" href="group__input.html#ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c">glfwSetJoystickCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="ga61acfb1f28f751438dd221225c5e725d" name="ga61acfb1f28f751438dd221225c5e725d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga61acfb1f28f751438dd221225c5e725d">&#9670;&#160;</a></span>GLFWgamepadstate</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="struct_g_l_f_wgamepadstate.html">GLFWgamepadstate</a> <a class="el" href="struct_g_l_f_wgamepadstate.html">GLFWgamepadstate</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This describes the input state of a gamepad.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#gamepad">Gamepad input</a> </dd>
<dd>
<a class="el" href="group__input.html#gadccddea8bce6113fa459de379ddaf051">glfwGetGamepadState</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="gaf5b859dbe19bdf434e42695ea45cc5f4" name="gaf5b859dbe19bdf434e42695ea45cc5f4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf5b859dbe19bdf434e42695ea45cc5f4">&#9670;&#160;</a></span>glfwGetInputMode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwGetInputMode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>mode</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the value of an input option for the specified window. The mode must be one of <a class="el" href="input_guide.html#GLFW_CURSOR">GLFW_CURSOR</a>, <a class="el" href="input_guide.html#GLFW_STICKY_KEYS">GLFW_STICKY_KEYS</a>, <a class="el" href="input_guide.html#GLFW_STICKY_MOUSE_BUTTONS">GLFW_STICKY_MOUSE_BUTTONS</a>, <a class="el" href="input_guide.html#GLFW_LOCK_KEY_MODS">GLFW_LOCK_KEY_MODS</a> or <a class="el" href="input_guide.html#GLFW_RAW_MOUSE_MOTION">GLFW_RAW_MOUSE_MOTION</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to query. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">mode</td><td>One of <code>GLFW_CURSOR</code>, <code>GLFW_STICKY_KEYS</code>, <code>GLFW_STICKY_MOUSE_BUTTONS</code>, <code>GLFW_LOCK_KEY_MODS</code> or <code>GLFW_RAW_MOUSE_MOTION</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gaa92336e173da9c8834558b54ee80563b" name="gaa92336e173da9c8834558b54ee80563b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa92336e173da9c8834558b54ee80563b">&#9670;&#160;</a></span>glfwSetInputMode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetInputMode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>mode</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets an input mode option for the specified window. The mode must be one of <a class="el" href="input_guide.html#GLFW_CURSOR">GLFW_CURSOR</a>, <a class="el" href="input_guide.html#GLFW_STICKY_KEYS">GLFW_STICKY_KEYS</a>, <a class="el" href="input_guide.html#GLFW_STICKY_MOUSE_BUTTONS">GLFW_STICKY_MOUSE_BUTTONS</a>, <a class="el" href="input_guide.html#GLFW_LOCK_KEY_MODS">GLFW_LOCK_KEY_MODS</a> or <a class="el" href="input_guide.html#GLFW_RAW_MOUSE_MOTION">GLFW_RAW_MOUSE_MOTION</a>.</p>
<p>If the mode is <code>GLFW_CURSOR</code>, the value must be one of the following cursor modes:</p><ul>
<li><code>GLFW_CURSOR_NORMAL</code> makes the cursor visible and behaving normally.</li>
<li><code>GLFW_CURSOR_HIDDEN</code> makes the cursor invisible when it is over the content area of the window but does not restrict the cursor from leaving.</li>
<li><code>GLFW_CURSOR_DISABLED</code> hides and grabs the cursor, providing virtual and unlimited cursor movement. This is useful for implementing for example 3D camera controls.</li>
<li><code>GLFW_CURSOR_CAPTURED</code> makes the cursor visible and confines it to the content area of the window.</li>
</ul>
<p>If the mode is <code>GLFW_STICKY_KEYS</code>, the value must be either <code>GLFW_TRUE</code> to enable sticky keys, or <code>GLFW_FALSE</code> to disable it. If sticky keys are enabled, a key press will ensure that <a class="el" href="group__input.html#gadd341da06bc8d418b4dc3a3518af9ad2">glfwGetKey</a> returns <code>GLFW_PRESS</code> the next time it is called even if the key had been released before the call. This is useful when you are only interested in whether keys have been pressed but not when or in which order.</p>
<p>If the mode is <code>GLFW_STICKY_MOUSE_BUTTONS</code>, the value must be either <code>GLFW_TRUE</code> to enable sticky mouse buttons, or <code>GLFW_FALSE</code> to disable it. If sticky mouse buttons are enabled, a mouse button press will ensure that <a class="el" href="group__input.html#gac1473feacb5996c01a7a5a33b5066704">glfwGetMouseButton</a> returns <code>GLFW_PRESS</code> the next time it is called even if the mouse button had been released before the call. This is useful when you are only interested in whether mouse buttons have been pressed but not when or in which order.</p>
<p>If the mode is <code>GLFW_LOCK_KEY_MODS</code>, the value must be either <code>GLFW_TRUE</code> to enable lock key modifier bits, or <code>GLFW_FALSE</code> to disable them. If enabled, callbacks that receive modifier bits will also have the <a class="el" href="group__mods.html#gaefeef8fcf825a6e43e241b337897200f">GLFW_MOD_CAPS_LOCK</a> bit set when the event was generated with Caps Lock on, and the <a class="el" href="group__mods.html#ga64e020b8a42af8376e944baf61feecbe">GLFW_MOD_NUM_LOCK</a> bit when Num Lock was on.</p>
<p>If the mode is <code>GLFW_RAW_MOUSE_MOTION</code>, the value must be either <code>GLFW_TRUE</code> to enable raw (unscaled and unaccelerated) mouse motion when the cursor is disabled, or <code>GLFW_FALSE</code> to disable it. If raw motion is not supported, attempting to set this will emit <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a>. Call <a class="el" href="group__input.html#gae4ee0dbd0d256183e1ea4026d897e1c2">glfwRawMouseMotionSupported</a> to check for support.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose input mode to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">mode</td><td>One of <code>GLFW_CURSOR</code>, <code>GLFW_STICKY_KEYS</code>, <code>GLFW_STICKY_MOUSE_BUTTONS</code>, <code>GLFW_LOCK_KEY_MODS</code> or <code>GLFW_RAW_MOUSE_MOTION</code>. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>The new value of the specified input mode.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a>, <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a> and <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a> (see above).</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="group__input.html#gaf5b859dbe19bdf434e42695ea45cc5f4">glfwGetInputMode</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. Replaces <code>glfwEnable</code> and <code>glfwDisable</code>. </dd></dl>

</div>
</div>
<a id="gae4ee0dbd0d256183e1ea4026d897e1c2" name="gae4ee0dbd0d256183e1ea4026d897e1c2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae4ee0dbd0d256183e1ea4026d897e1c2">&#9670;&#160;</a></span>glfwRawMouseMotionSupported()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwRawMouseMotionSupported </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns whether raw mouse motion is supported on the current system. This status does not change after GLFW has been initialized so you only need to check this once. If you attempt to enable raw motion on a system that does not support it, <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a> will be emitted.</p>
<p>Raw mouse motion is closer to the actual motion of the mouse across a surface. It is not affected by the scaling and acceleration applied to the motion of the desktop cursor. That processing is suitable for a cursor while raw motion is better for controlling for example a 3D camera. Because of this, raw mouse motion is only provided when the cursor is disabled.</p>
<dl class="section return"><dt>Returns</dt><dd><code>GLFW_TRUE</code> if raw mouse motion is supported on the current machine, or <code>GLFW_FALSE</code> otherwise.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#raw_mouse_motion">Raw mouse motion</a> </dd>
<dd>
<a class="el" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="gaeaed62e69c3bd62b7ff8f7b19913ce4f" name="gaeaed62e69c3bd62b7ff8f7b19913ce4f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaeaed62e69c3bd62b7ff8f7b19913ce4f">&#9670;&#160;</a></span>glfwGetKeyName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char * glfwGetKeyName </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>key</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>scancode</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the name of the specified printable key, encoded as UTF-8. This is typically the character that key would produce without any modifier keys, intended for displaying key bindings to the user. For dead keys, it is typically the diacritic it would add to a character.</p>
<p><b>Do not use this function</b> for <a class="el" href="input_guide.html#input_char">text input</a>. You will break text input for many languages even if it happens to work for yours.</p>
<p>If the key is <code>GLFW_KEY_UNKNOWN</code>, the scancode is used to identify the key, otherwise the scancode is ignored. If you specify a non-printable key, or <code>GLFW_KEY_UNKNOWN</code> and a scancode that maps to a non-printable key, this function returns <code>NULL</code> but does not emit an error.</p>
<p>This behavior allows you to always pass in the arguments in the <a class="el" href="input_guide.html#input_key">key callback</a> without modification.</p>
<p>The printable keys are:</p><ul>
<li><code>GLFW_KEY_APOSTROPHE</code></li>
<li><code>GLFW_KEY_COMMA</code></li>
<li><code>GLFW_KEY_MINUS</code></li>
<li><code>GLFW_KEY_PERIOD</code></li>
<li><code>GLFW_KEY_SLASH</code></li>
<li><code>GLFW_KEY_SEMICOLON</code></li>
<li><code>GLFW_KEY_EQUAL</code></li>
<li><code>GLFW_KEY_LEFT_BRACKET</code></li>
<li><code>GLFW_KEY_RIGHT_BRACKET</code></li>
<li><code>GLFW_KEY_BACKSLASH</code></li>
<li><code>GLFW_KEY_WORLD_1</code></li>
<li><code>GLFW_KEY_WORLD_2</code></li>
<li><code>GLFW_KEY_0</code> to <code>GLFW_KEY_9</code></li>
<li><code>GLFW_KEY_A</code> to <code>GLFW_KEY_Z</code></li>
<li><code>GLFW_KEY_KP_0</code> to <code>GLFW_KEY_KP_9</code></li>
<li><code>GLFW_KEY_KP_DECIMAL</code></li>
<li><code>GLFW_KEY_KP_DIVIDE</code></li>
<li><code>GLFW_KEY_KP_MULTIPLY</code></li>
<li><code>GLFW_KEY_KP_SUBTRACT</code></li>
<li><code>GLFW_KEY_KP_ADD</code></li>
<li><code>GLFW_KEY_KP_EQUAL</code></li>
</ul>
<p>Names for printable keys depend on keyboard layout, while names for non-printable keys are the same across layouts but depend on the application language and should be localized along with other user interface text.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">key</td><td>The key to query, or <code>GLFW_KEY_UNKNOWN</code>. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">scancode</td><td>The scancode of the key to query. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The UTF-8 encoded, layout-specific name of the key, or <code>NULL</code>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a>, <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>The contents of the returned string may change when a keyboard layout change event is received.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned string is allocated and freed by GLFW. You should not free it yourself. It is valid until the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#input_key_name">Key names</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="ga67ddd1b7dcbbaff03e4a76c0ea67103a" name="ga67ddd1b7dcbbaff03e4a76c0ea67103a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga67ddd1b7dcbbaff03e4a76c0ea67103a">&#9670;&#160;</a></span>glfwGetKeyScancode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwGetKeyScancode </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>key</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the platform-specific scancode of the specified key.</p>
<p>If the specified <a class="el" href="group__keys.html">key token</a> corresponds to a physical key not supported on the current platform then this method will return <code>-1</code>. Calling this function with anything other than a key token will return <code>-1</code> and generate a <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a> error.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">key</td><td>Any <a class="el" href="group__keys.html">key token</a>. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The platform-specific scancode for the key, or <code>-1</code> if the key is not supported on the current platform or an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#input_key">Key input</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="gadd341da06bc8d418b4dc3a3518af9ad2" name="gadd341da06bc8d418b4dc3a3518af9ad2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadd341da06bc8d418b4dc3a3518af9ad2">&#9670;&#160;</a></span>glfwGetKey()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwGetKey </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>key</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the last state reported for the specified key to the specified window. The returned state is one of <code>GLFW_PRESS</code> or <code>GLFW_RELEASE</code>. The action <code>GLFW_REPEAT</code> is only reported to the key callback.</p>
<p>If the <a class="el" href="input_guide.html#GLFW_STICKY_KEYS">GLFW_STICKY_KEYS</a> input mode is enabled, this function returns <code>GLFW_PRESS</code> the first time you call it for a key that was pressed, even if that key has already been released.</p>
<p>The key functions deal with physical keys, with <a class="el" href="group__keys.html">key tokens</a> named after their use on the standard US keyboard layout. If you want to input text, use the Unicode character callback instead.</p>
<p>The <a class="el" href="group__mods.html">modifier key bit masks</a> are not key tokens and cannot be used with this function.</p>
<p><b>Do not use this function</b> to implement <a class="el" href="input_guide.html#input_char">text input</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The desired window. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">key</td><td>The desired <a class="el" href="group__keys.html">keyboard key</a>. <code>GLFW_KEY_UNKNOWN</code> is not a valid key for this function. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>One of <code>GLFW_PRESS</code> or <code>GLFW_RELEASE</code>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#input_key">Key input</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. <b>GLFW 3:</b> Added window handle parameter. </dd></dl>

</div>
</div>
<a id="gac1473feacb5996c01a7a5a33b5066704" name="gac1473feacb5996c01a7a5a33b5066704"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac1473feacb5996c01a7a5a33b5066704">&#9670;&#160;</a></span>glfwGetMouseButton()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwGetMouseButton </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>button</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the last state reported for the specified mouse button to the specified window. The returned state is one of <code>GLFW_PRESS</code> or <code>GLFW_RELEASE</code>.</p>
<p>If the <a class="el" href="input_guide.html#GLFW_STICKY_MOUSE_BUTTONS">GLFW_STICKY_MOUSE_BUTTONS</a> input mode is enabled, this function returns <code>GLFW_PRESS</code> the first time you call it for a mouse button that was pressed, even if that mouse button has already been released.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The desired window. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">button</td><td>The desired <a class="el" href="group__buttons.html">mouse button</a>. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>One of <code>GLFW_PRESS</code> or <code>GLFW_RELEASE</code>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#input_mouse_button">Mouse button input</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. <b>GLFW 3:</b> Added window handle parameter. </dd></dl>

</div>
</div>
<a id="ga01d37b6c40133676b9cea60ca1d7c0cc" name="ga01d37b6c40133676b9cea60ca1d7c0cc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga01d37b6c40133676b9cea60ca1d7c0cc">&#9670;&#160;</a></span>glfwGetCursorPos()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwGetCursorPos </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double *&#160;</td>
          <td class="paramname"><em>xpos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double *&#160;</td>
          <td class="paramname"><em>ypos</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the position of the cursor, in screen coordinates, relative to the upper-left corner of the content area of the specified window.</p>
<p>If the cursor is disabled (with <code>GLFW_CURSOR_DISABLED</code>) then the cursor position is unbounded and limited only by the minimum and maximum values of a <code>double</code>.</p>
<p>The coordinate can be converted to their integer equivalents with the <code>floor</code> function. Casting directly to an integer type works for positive coordinates, but fails for negative ones.</p>
<p>Any or all of the position arguments may be <code>NULL</code>. If an error occurs, all non-<code>NULL</code> position arguments will be set to zero.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The desired window. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">xpos</td><td>Where to store the cursor x-coordinate, relative to the left edge of the content area, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">ypos</td><td>Where to store the cursor y-coordinate, relative to the to top edge of the content area, or <code>NULL</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#cursor_pos">Cursor position</a> </dd>
<dd>
<a class="el" href="group__input.html#ga04b03af936d906ca123c8f4ee08b39e7">glfwSetCursorPos</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. Replaces <code>glfwGetMousePos</code>. </dd></dl>

</div>
</div>
<a id="ga04b03af936d906ca123c8f4ee08b39e7" name="ga04b03af936d906ca123c8f4ee08b39e7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga04b03af936d906ca123c8f4ee08b39e7">&#9670;&#160;</a></span>glfwSetCursorPos()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetCursorPos </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>xpos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>ypos</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the position, in screen coordinates, of the cursor relative to the upper-left corner of the content area of the specified window. The window must have input focus. If the window does not have input focus when this function is called, it fails silently.</p>
<p><b>Do not use this function</b> to implement things like camera controls. GLFW already provides the <code>GLFW_CURSOR_DISABLED</code> cursor mode that hides the cursor, transparently re-centers it and provides unconstrained cursor motion. See <a class="el" href="group__input.html#gaa92336e173da9c8834558b54ee80563b">glfwSetInputMode</a> for more information.</p>
<p>If the cursor mode is <code>GLFW_CURSOR_DISABLED</code> then the cursor position is unconstrained and limited only by the minimum and maximum values of a <code>double</code>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The desired window. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">xpos</td><td>The desired x-coordinate, relative to the left edge of the content area. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ypos</td><td>The desired y-coordinate, relative to the top edge of the content area.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a> and <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a> (see remarks).</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>Wayland:</b> This function will only work when the cursor mode is <code>GLFW_CURSOR_DISABLED</code>, otherwise it will emit <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#cursor_pos">Cursor position</a> </dd>
<dd>
<a class="el" href="group__input.html#ga01d37b6c40133676b9cea60ca1d7c0cc">glfwGetCursorPos</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. Replaces <code>glfwSetMousePos</code>. </dd></dl>

</div>
</div>
<a id="ga556f604f73af156c0db0e97c081373c3" name="ga556f604f73af156c0db0e97c081373c3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga556f604f73af156c0db0e97c081373c3">&#9670;&#160;</a></span>glfwCreateCursor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a> * glfwCreateCursor </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="struct_g_l_f_wimage.html">GLFWimage</a> *&#160;</td>
          <td class="paramname"><em>image</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>xhot</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>yhot</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Creates a new custom cursor image that can be set for a window with <a class="el" href="group__input.html#gad3b4f38c8d5dae036bc8fa959e18343e">glfwSetCursor</a>. The cursor can be destroyed with <a class="el" href="group__input.html#ga81b952cd1764274d0db7fb3c5a79ba6a">glfwDestroyCursor</a>. Any remaining cursors are destroyed by <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a>.</p>
<p>The pixels are 32-bit, little-endian, non-premultiplied RGBA, i.e. eight bits per channel with the red channel first. They are arranged canonically as packed sequential rows, starting from the top-left corner.</p>
<p>The cursor hotspot is specified in pixels, relative to the upper-left corner of the cursor image. Like all other coordinate systems in GLFW, the X-axis points to the right and the Y-axis points down.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">image</td><td>The desired cursor image. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">xhot</td><td>The desired x-coordinate, in pixels, of the cursor hotspot. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">yhot</td><td>The desired y-coordinate, in pixels, of the cursor hotspot. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The handle of the created cursor, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The specified image data is copied before this function returns.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#cursor_object">Cursor objects</a> </dd>
<dd>
<a class="el" href="group__input.html#ga81b952cd1764274d0db7fb3c5a79ba6a">glfwDestroyCursor</a> </dd>
<dd>
<a class="el" href="group__input.html#gaf2fb2eb2c9dd842d1cef8a34e3c6403e">glfwCreateStandardCursor</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.1. </dd></dl>

</div>
</div>
<a id="gaf2fb2eb2c9dd842d1cef8a34e3c6403e" name="gaf2fb2eb2c9dd842d1cef8a34e3c6403e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf2fb2eb2c9dd842d1cef8a34e3c6403e">&#9670;&#160;</a></span>glfwCreateStandardCursor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a> * glfwCreateStandardCursor </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>shape</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns a cursor with a standard shape, that can be set for a window with <a class="el" href="group__input.html#gad3b4f38c8d5dae036bc8fa959e18343e">glfwSetCursor</a>. The images for these cursors come from the system cursor theme and their exact appearance will vary between platforms.</p>
<p>Most of these shapes are guaranteed to exist on every supported platform but a few may not be present. See the table below for details.</p>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Cursor shape   </th><th class="markdownTableHeadNone">Windows   </th><th class="markdownTableHeadNone">macOS   </th><th class="markdownTableHeadNone">X11   </th><th class="markdownTableHeadNone">Wayland    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><a class="el" href="group__shapes.html#ga8ab0e717245b85506cb0eaefdea39d0a">GLFW_ARROW_CURSOR</a>   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><a class="el" href="group__shapes.html#ga36185f4375eaada1b04e431244774c86">GLFW_IBEAM_CURSOR</a>   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><a class="el" href="group__shapes.html#ga8af88c0ea05ab9e8f9ac1530e8873c22">GLFW_CROSSHAIR_CURSOR</a>   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><a class="el" href="group__shapes.html#gaad01a50929fb515bf27e4462c51f6ed0">GLFW_POINTING_HAND_CURSOR</a>   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><a class="el" href="group__shapes.html#ga2010a43dc1050a7c9154148a63cf01ad">GLFW_RESIZE_EW_CURSOR</a>   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><a class="el" href="group__shapes.html#gaa59214e8cdc8c8adf08fdf125ed68388">GLFW_RESIZE_NS_CURSOR</a>   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><a class="el" href="group__shapes.html#gadf2c0a495ec9cef4e1a364cc99aa78da">GLFW_RESIZE_NWSE_CURSOR</a>   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes<sup>1</sup>   </td><td class="markdownTableBodyNone">Maybe<sup>2</sup>   </td><td class="markdownTableBodyNone">Maybe<sup>2</sup>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><a class="el" href="group__shapes.html#gab06bba3b407f92807ba9b48de667a323">GLFW_RESIZE_NESW_CURSOR</a>   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes<sup>1</sup>   </td><td class="markdownTableBodyNone">Maybe<sup>2</sup>   </td><td class="markdownTableBodyNone">Maybe<sup>2</sup>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><a class="el" href="group__shapes.html#ga3a5f4811155f95ccafbbb4c9a899fc1d">GLFW_RESIZE_ALL_CURSOR</a>   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><a class="el" href="group__shapes.html#ga297c503095b034bc8891393b637844b1">GLFW_NOT_ALLOWED_CURSOR</a>   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Yes   </td><td class="markdownTableBodyNone">Maybe<sup>2</sup>   </td><td class="markdownTableBodyNone">Maybe<sup>2</sup>   </td></tr>
</table>
<p>1) This uses a private system API and may fail in the future.</p>
<p>2) This uses a newer standard that not all cursor themes support.</p>
<p>If the requested shape is not available, this function emits a <a class="el" href="group__errors.html#ga09d6943923a70ddef3a085f5baee786c">GLFW_CURSOR_UNAVAILABLE</a> error and returns <code>NULL</code>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">shape</td><td>One of the <a class="el" href="group__shapes.html">standard shapes</a>. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>A new cursor ready to use or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a>, <a class="el" href="group__errors.html#ga09d6943923a70ddef3a085f5baee786c">GLFW_CURSOR_UNAVAILABLE</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#cursor_standard">Standard cursor creation</a> </dd>
<dd>
<a class="el" href="group__input.html#ga556f604f73af156c0db0e97c081373c3">glfwCreateCursor</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.1. </dd></dl>

</div>
</div>
<a id="ga81b952cd1764274d0db7fb3c5a79ba6a" name="ga81b952cd1764274d0db7fb3c5a79ba6a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga81b952cd1764274d0db7fb3c5a79ba6a">&#9670;&#160;</a></span>glfwDestroyCursor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwDestroyCursor </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a> *&#160;</td>
          <td class="paramname"><em>cursor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function destroys a cursor previously created with <a class="el" href="group__input.html#ga556f604f73af156c0db0e97c081373c3">glfwCreateCursor</a>. Any remaining cursors will be destroyed by <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a>.</p>
<p>If the specified cursor is current for any window, that window will be reverted to the default cursor. This does not affect the cursor mode.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">cursor</td><td>The cursor object to destroy.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Reentrancy</dt><dd>This function must not be called from a callback.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#cursor_object">Cursor objects</a> </dd>
<dd>
<a class="el" href="group__input.html#ga556f604f73af156c0db0e97c081373c3">glfwCreateCursor</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.1. </dd></dl>

</div>
</div>
<a id="gad3b4f38c8d5dae036bc8fa959e18343e" name="gad3b4f38c8d5dae036bc8fa959e18343e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad3b4f38c8d5dae036bc8fa959e18343e">&#9670;&#160;</a></span>glfwSetCursor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetCursor </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__input.html#ga89261ae18c75e863aaf2656ecdd238f4">GLFWcursor</a> *&#160;</td>
          <td class="paramname"><em>cursor</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the cursor image to be used when the cursor is over the content area of the specified window. The set cursor will only be visible when the <a class="el" href="input_guide.html#cursor_mode">cursor mode</a> of the window is <code>GLFW_CURSOR_NORMAL</code>.</p>
<p>On some platforms, the set cursor may not be visible unless the window also has input focus.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to set the cursor for. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">cursor</td><td>The cursor to set, or <code>NULL</code> to switch back to the default arrow cursor.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#cursor_object">Cursor objects</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.1. </dd></dl>

</div>
</div>
<a id="ga1caf18159767e761185e49a3be019f8d" name="ga1caf18159767e761185e49a3be019f8d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1caf18159767e761185e49a3be019f8d">&#9670;&#160;</a></span>glfwSetKeyCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__input.html#ga5bd751b27b90f865d2ea613533f0453c">GLFWkeyfun</a> glfwSetKeyCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__input.html#ga5bd751b27b90f865d2ea613533f0453c">GLFWkeyfun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the key callback of the specified window, which is called when a key is pressed, repeated or released.</p>
<p>The key functions deal with physical keys, with layout independent <a class="el" href="group__keys.html">key tokens</a> named after their values in the standard US keyboard layout. If you want to input text, use the <a class="el" href="group__input.html#gab25c4a220fd8f5717718dbc487828996">character callback</a> instead.</p>
<p>When a window loses input focus, it will generate synthetic key release events for all pressed keys with associated key tokens. You can tell these events from user-generated events by the fact that the synthetic ones are generated after the focus loss event has been processed, i.e. after the <a class="el" href="group__window.html#gac2d83c4a10f071baf841f6730528e66c">window focus callback</a> has been called.</p>
<p>The scancode of a key is specific to that platform or sometimes even to that machine. Scancodes are intended to allow users to bind keys that don't have a GLFW key token. Such keys have <code>key</code> set to <code>GLFW_KEY_UNKNOWN</code>, their state is not saved and so it cannot be queried with <a class="el" href="group__input.html#gadd341da06bc8d418b4dc3a3518af9ad2">glfwGetKey</a>.</p>
<p>Sometimes GLFW needs to generate synthetic key events, in which case the scancode may be zero.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new key callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> key, <span class="keywordtype">int</span> scancode, <span class="keywordtype">int</span> action, <span class="keywordtype">int</span> mods)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__input.html#ga5bd751b27b90f865d2ea613533f0453c">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#input_key">Key input</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. <b>GLFW 3:</b> Added window handle parameter and return value. </dd></dl>

</div>
</div>
<a id="gab25c4a220fd8f5717718dbc487828996" name="gab25c4a220fd8f5717718dbc487828996"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab25c4a220fd8f5717718dbc487828996">&#9670;&#160;</a></span>glfwSetCharCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__input.html#ga1ab90a55cf3f58639b893c0f4118cb6e">GLFWcharfun</a> glfwSetCharCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__input.html#ga1ab90a55cf3f58639b893c0f4118cb6e">GLFWcharfun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the character callback of the specified window, which is called when a Unicode character is input.</p>
<p>The character callback is intended for Unicode text input. As it deals with characters, it is keyboard layout dependent, whereas the <a class="el" href="group__input.html#ga1caf18159767e761185e49a3be019f8d">key callback</a> is not. Characters do not map 1:1 to physical keys, as a key may produce zero, one or more characters. If you want to know whether a specific physical key was pressed or released, see the key callback instead.</p>
<p>The character callback behaves as system text input normally does and will not be called if modifier keys are held down that would prevent normal text input on that platform, for example a Super (Command) key on macOS or Alt key on Windows.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> codepoint)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__input.html#ga1ab90a55cf3f58639b893c0f4118cb6e">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#input_char">Text input</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 2.4. <b>GLFW 3:</b> Added window handle parameter and return value. </dd></dl>

</div>
</div>
<a id="ga0b7f4ad13c2b17435ff13b6dcfb4e43c" name="ga0b7f4ad13c2b17435ff13b6dcfb4e43c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga0b7f4ad13c2b17435ff13b6dcfb4e43c">&#9670;&#160;</a></span>glfwSetCharModsCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__input.html#gac3cf64f90b6219c05ac7b7822d5a4b8f">GLFWcharmodsfun</a> glfwSetCharModsCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__input.html#gac3cf64f90b6219c05ac7b7822d5a4b8f">GLFWcharmodsfun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the character with modifiers callback of the specified window, which is called when a Unicode character is input regardless of what modifier keys are used.</p>
<p>The character with modifiers callback is intended for implementing custom Unicode character input. For regular Unicode text input, see the <a class="el" href="group__input.html#gab25c4a220fd8f5717718dbc487828996">character callback</a>. Like the character callback, the character with modifiers callback deals with characters and is keyboard layout dependent. Characters do not map 1:1 to physical keys, as a key may produce zero, one or more characters. If you want to know whether a specific physical key was pressed or released, see the <a class="el" href="group__input.html#ga1caf18159767e761185e49a3be019f8d">key callback</a> instead.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> codepoint, <span class="keywordtype">int</span> mods)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__input.html#gac3cf64f90b6219c05ac7b7822d5a4b8f">function pointer type</a>.</dd></dl>
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000002">Deprecated:</a></b></dt><dd>Scheduled for removal in version 4.0.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#input_char">Text input</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.1. </dd></dl>

</div>
</div>
<a id="ga6ab84420974d812bee700e45284a723c" name="ga6ab84420974d812bee700e45284a723c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6ab84420974d812bee700e45284a723c">&#9670;&#160;</a></span>glfwSetMouseButtonCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__input.html#ga0184dcb59f6d85d735503dcaae809727">GLFWmousebuttonfun</a> glfwSetMouseButtonCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__input.html#ga0184dcb59f6d85d735503dcaae809727">GLFWmousebuttonfun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the mouse button callback of the specified window, which is called when a mouse button is pressed or released.</p>
<p>When a window loses input focus, it will generate synthetic mouse button release events for all pressed mouse buttons. You can tell these events from user-generated events by the fact that the synthetic ones are generated after the focus loss event has been processed, i.e. after the <a class="el" href="group__window.html#gac2d83c4a10f071baf841f6730528e66c">window focus callback</a> has been called.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> button, <span class="keywordtype">int</span> action, <span class="keywordtype">int</span> mods)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__input.html#ga0184dcb59f6d85d735503dcaae809727">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#input_mouse_button">Mouse button input</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. <b>GLFW 3:</b> Added window handle parameter and return value. </dd></dl>

</div>
</div>
<a id="gac1f879ab7435d54d4d79bb469fe225d7" name="gac1f879ab7435d54d4d79bb469fe225d7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac1f879ab7435d54d4d79bb469fe225d7">&#9670;&#160;</a></span>glfwSetCursorPosCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__input.html#gad6fae41b3ac2e4209aaa87b596c57f68">GLFWcursorposfun</a> glfwSetCursorPosCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__input.html#gad6fae41b3ac2e4209aaa87b596c57f68">GLFWcursorposfun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the cursor position callback of the specified window, which is called when the cursor is moved. The callback is provided with the position, in screen coordinates, relative to the upper-left corner of the content area of the window.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">double</span> xpos, <span class="keywordtype">double</span> ypos);</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__input.html#gad6fae41b3ac2e4209aaa87b596c57f68">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#cursor_pos">Cursor position</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. Replaces <code>glfwSetMousePosCallback</code>. </dd></dl>

</div>
</div>
<a id="gad27f8ad0142c038a281466c0966817d8" name="gad27f8ad0142c038a281466c0966817d8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad27f8ad0142c038a281466c0966817d8">&#9670;&#160;</a></span>glfwSetCursorEnterCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__input.html#gaa93dc4818ac9ab32532909d53a337cbe">GLFWcursorenterfun</a> glfwSetCursorEnterCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__input.html#gaa93dc4818ac9ab32532909d53a337cbe">GLFWcursorenterfun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the cursor boundary crossing callback of the specified window, which is called when the cursor enters or leaves the content area of the window.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> entered)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__input.html#gaa93dc4818ac9ab32532909d53a337cbe">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#cursor_enter">Cursor enter/leave events</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga571e45a030ae4061f746ed56cb76aede" name="ga571e45a030ae4061f746ed56cb76aede"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga571e45a030ae4061f746ed56cb76aede">&#9670;&#160;</a></span>glfwSetScrollCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__input.html#gaf656112c33de3efdb227fa58f0134cf5">GLFWscrollfun</a> glfwSetScrollCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__input.html#gaf656112c33de3efdb227fa58f0134cf5">GLFWscrollfun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the scroll callback of the specified window, which is called when a scrolling device is used, such as a mouse wheel or scrolling area of a touchpad.</p>
<p>The scroll callback receives all scrolling input, like that from a mouse wheel or a touchpad scrolling area.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new scroll callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">double</span> xoffset, <span class="keywordtype">double</span> yoffset)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__input.html#gaf656112c33de3efdb227fa58f0134cf5">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#scrolling">Scroll input</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. Replaces <code>glfwSetMouseWheelCallback</code>. </dd></dl>

</div>
</div>
<a id="gab773f0ee0a07cff77a210cea40bc1f6b" name="gab773f0ee0a07cff77a210cea40bc1f6b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab773f0ee0a07cff77a210cea40bc1f6b">&#9670;&#160;</a></span>glfwSetDropCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__input.html#gaaba73c3274062c18723b7f05862d94b2">GLFWdropfun</a> glfwSetDropCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__input.html#gaaba73c3274062c18723b7f05862d94b2">GLFWdropfun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the path drop callback of the specified window, which is called when one or more dragged paths are dropped on the window.</p>
<p>Because the path array and its strings may have been generated specifically for that event, they are not guaranteed to be valid after the callback has returned. If you wish to use them after the callback returns, you need to make a deep copy.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new file drop callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> path_count, <span class="keyword">const</span> <span class="keywordtype">char</span>* paths[])</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__input.html#gaaba73c3274062c18723b7f05862d94b2">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#path_drop">Path drop input</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.1. </dd></dl>

</div>
</div>
<a id="gaed0966cee139d815317f9ffcba64c9f1" name="gaed0966cee139d815317f9ffcba64c9f1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaed0966cee139d815317f9ffcba64c9f1">&#9670;&#160;</a></span>glfwJoystickPresent()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwJoystickPresent </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>jid</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns whether the specified joystick is present.</p>
<p>There is no need to call this function before other functions that accept a joystick ID, as they all check for presence before performing any other work.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">jid</td><td>The <a class="el" href="group__joysticks.html">joystick</a> to query. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><code>GLFW_TRUE</code> if the joystick is present, or <code>GLFW_FALSE</code> otherwise.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#joystick">Joystick input</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. Replaces <code>glfwGetJoystickParam</code>. </dd></dl>

</div>
</div>
<a id="gaeb1c0191d3140a233a682987c61eb408" name="gaeb1c0191d3140a233a682987c61eb408"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaeb1c0191d3140a233a682987c61eb408">&#9670;&#160;</a></span>glfwGetJoystickAxes()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const float * glfwGetJoystickAxes </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>jid</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>count</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the values of all axes of the specified joystick. Each element in the array is a value between -1.0 and 1.0.</p>
<p>If the specified joystick is not present this function will return <code>NULL</code> but will not generate an error. This can be used instead of first calling <a class="el" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">jid</td><td>The <a class="el" href="group__joysticks.html">joystick</a> to query. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">count</td><td>Where to store the number of axis values in the returned array. This is set to zero if the joystick is not present or an error occurred. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>An array of axis values, or <code>NULL</code> if the joystick is not present or an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned array is allocated and freed by GLFW. You should not free it yourself. It is valid until the specified joystick is disconnected or the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#joystick_axis">Joystick axis states</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. Replaces <code>glfwGetJoystickPos</code>. </dd></dl>

</div>
</div>
<a id="ga5ffe34739d3dc97efe432ed2d81d9938" name="ga5ffe34739d3dc97efe432ed2d81d9938"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5ffe34739d3dc97efe432ed2d81d9938">&#9670;&#160;</a></span>glfwGetJoystickButtons()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const unsigned char * glfwGetJoystickButtons </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>jid</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>count</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the state of all buttons of the specified joystick. Each element in the array is either <code>GLFW_PRESS</code> or <code>GLFW_RELEASE</code>.</p>
<p>For backward compatibility with earlier versions that did not have <a class="el" href="group__input.html#ga06e660841b3e79c54da4f54a932c5a2c">glfwGetJoystickHats</a>, the button array also includes all hats, each represented as four buttons. The hats are in the same order as returned by <b>glfwGetJoystickHats</b> and are in the order <em>up</em>, <em>right</em>, <em>down</em> and <em>left</em>. To disable these extra buttons, set the <a class="el" href="intro_guide.html#GLFW_JOYSTICK_HAT_BUTTONS">GLFW_JOYSTICK_HAT_BUTTONS</a> init hint before initialization.</p>
<p>If the specified joystick is not present this function will return <code>NULL</code> but will not generate an error. This can be used instead of first calling <a class="el" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">jid</td><td>The <a class="el" href="group__joysticks.html">joystick</a> to query. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">count</td><td>Where to store the number of button states in the returned array. This is set to zero if the joystick is not present or an error occurred. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>An array of button states, or <code>NULL</code> if the joystick is not present or an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned array is allocated and freed by GLFW. You should not free it yourself. It is valid until the specified joystick is disconnected or the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#joystick_button">Joystick button states</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 2.2. <b>GLFW 3:</b> Changed to return a dynamic array. </dd></dl>

</div>
</div>
<a id="ga06e660841b3e79c54da4f54a932c5a2c" name="ga06e660841b3e79c54da4f54a932c5a2c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga06e660841b3e79c54da4f54a932c5a2c">&#9670;&#160;</a></span>glfwGetJoystickHats()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const unsigned char * glfwGetJoystickHats </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>jid</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>count</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the state of all hats of the specified joystick. Each element in the array is one of the following values:</p>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Name   </th><th class="markdownTableHeadNone">Value    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_HAT_CENTERED</code>   </td><td class="markdownTableBodyNone">0    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_HAT_UP</code>   </td><td class="markdownTableBodyNone">1    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_HAT_RIGHT</code>   </td><td class="markdownTableBodyNone">2    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_HAT_DOWN</code>   </td><td class="markdownTableBodyNone">4    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_HAT_LEFT</code>   </td><td class="markdownTableBodyNone">8    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_HAT_RIGHT_UP</code>   </td><td class="markdownTableBodyNone"><code>GLFW_HAT_RIGHT</code> | <code>GLFW_HAT_UP</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_HAT_RIGHT_DOWN</code>   </td><td class="markdownTableBodyNone"><code>GLFW_HAT_RIGHT</code> | <code>GLFW_HAT_DOWN</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><code>GLFW_HAT_LEFT_UP</code>   </td><td class="markdownTableBodyNone"><code>GLFW_HAT_LEFT</code> | <code>GLFW_HAT_UP</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><code>GLFW_HAT_LEFT_DOWN</code>   </td><td class="markdownTableBodyNone"><code>GLFW_HAT_LEFT</code> | <code>GLFW_HAT_DOWN</code>   </td></tr>
</table>
<p>The diagonal directions are bitwise combinations of the primary (up, right, down and left) directions and you can test for these individually by ANDing it with the corresponding direction.</p>
<div class="fragment"><div class="line"><span class="keywordflow">if</span> (hats[2] &amp; <a class="code hl_define" href="group__hat__state.html#ga252586e3bbde75f4b0e07ad3124867f5">GLFW_HAT_RIGHT</a>)</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// State of hat 2 could be right-up, right or right-down</span></div>
<div class="line">}</div>
<div class="ttc" id="agroup__hat__state_html_ga252586e3bbde75f4b0e07ad3124867f5"><div class="ttname"><a href="group__hat__state.html#ga252586e3bbde75f4b0e07ad3124867f5">GLFW_HAT_RIGHT</a></div><div class="ttdeci">#define GLFW_HAT_RIGHT</div><div class="ttdef"><b>Definition</b> glfw3.h:357</div></div>
</div><!-- fragment --><p>If the specified joystick is not present this function will return <code>NULL</code> but will not generate an error. This can be used instead of first calling <a class="el" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">jid</td><td>The <a class="el" href="group__joysticks.html">joystick</a> to query. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">count</td><td>Where to store the number of hat states in the returned array. This is set to zero if the joystick is not present or an error occurred. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>An array of hat states, or <code>NULL</code> if the joystick is not present or an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned array is allocated and freed by GLFW. You should not free it yourself. It is valid until the specified joystick is disconnected, this function is called again for that joystick or the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#joystick_hat">Joystick hat states</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="gac6a8e769e18e0bcfa9097793fc2c3978" name="gac6a8e769e18e0bcfa9097793fc2c3978"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac6a8e769e18e0bcfa9097793fc2c3978">&#9670;&#160;</a></span>glfwGetJoystickName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char * glfwGetJoystickName </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>jid</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the name, encoded as UTF-8, of the specified joystick. The returned string is allocated and freed by GLFW. You should not free it yourself.</p>
<p>If the specified joystick is not present this function will return <code>NULL</code> but will not generate an error. This can be used instead of first calling <a class="el" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">jid</td><td>The <a class="el" href="group__joysticks.html">joystick</a> to query. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The UTF-8 encoded name of the joystick, or <code>NULL</code> if the joystick is not present or an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned string is allocated and freed by GLFW. You should not free it yourself. It is valid until the specified joystick is disconnected or the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#joystick_name">Joystick name</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga6659411aec3c7fcef27780e2cb2d9600" name="ga6659411aec3c7fcef27780e2cb2d9600"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6659411aec3c7fcef27780e2cb2d9600">&#9670;&#160;</a></span>glfwGetJoystickGUID()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char * glfwGetJoystickGUID </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>jid</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the SDL compatible GUID, as a UTF-8 encoded hexadecimal string, of the specified joystick. The returned string is allocated and freed by GLFW. You should not free it yourself.</p>
<p>The GUID is what connects a joystick to a gamepad mapping. A connected joystick will always have a GUID even if there is no gamepad mapping assigned to it.</p>
<p>If the specified joystick is not present this function will return <code>NULL</code> but will not generate an error. This can be used instead of first calling <a class="el" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a>.</p>
<p>The GUID uses the format introduced in SDL 2.0.5. This GUID tries to uniquely identify the make and model of a joystick but does not identify a specific unit, e.g. all wired Xbox 360 controllers will have the same GUID on that platform. The GUID for a unit may vary between platforms depending on what hardware information the platform specific APIs provide.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">jid</td><td>The <a class="el" href="group__joysticks.html">joystick</a> to query. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The UTF-8 encoded GUID of the joystick, or <code>NULL</code> if the joystick is not present or an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned string is allocated and freed by GLFW. You should not free it yourself. It is valid until the specified joystick is disconnected or the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#gamepad">Gamepad input</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga6b2f72d64d636b48a727b437cbb7489e" name="ga6b2f72d64d636b48a727b437cbb7489e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6b2f72d64d636b48a727b437cbb7489e">&#9670;&#160;</a></span>glfwSetJoystickUserPointer()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetJoystickUserPointer </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>jid</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>pointer</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the user-defined pointer of the specified joystick. The current value is retained until the joystick is disconnected. The initial value is <code>NULL</code>.</p>
<p>This function may be called from the joystick callback, even for a joystick that is being disconnected.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">jid</td><td>The joystick whose pointer to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">pointer</td><td>The new value.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#joystick_userptr">Joystick user pointer</a> </dd>
<dd>
<a class="el" href="group__input.html#ga18cefd7265d1fa04f3fd38a6746db5f3">glfwGetJoystickUserPointer</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga18cefd7265d1fa04f3fd38a6746db5f3" name="ga18cefd7265d1fa04f3fd38a6746db5f3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga18cefd7265d1fa04f3fd38a6746db5f3">&#9670;&#160;</a></span>glfwGetJoystickUserPointer()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void * glfwGetJoystickUserPointer </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>jid</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the current value of the user-defined pointer of the specified joystick. The initial value is <code>NULL</code>.</p>
<p>This function may be called from the joystick callback, even for a joystick that is being disconnected.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">jid</td><td>The joystick whose pointer to return.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#joystick_userptr">Joystick user pointer</a> </dd>
<dd>
<a class="el" href="group__input.html#ga6b2f72d64d636b48a727b437cbb7489e">glfwSetJoystickUserPointer</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="gad0f676860f329d80f7e47e9f06a96f00" name="gad0f676860f329d80f7e47e9f06a96f00"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad0f676860f329d80f7e47e9f06a96f00">&#9670;&#160;</a></span>glfwJoystickIsGamepad()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwJoystickIsGamepad </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>jid</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns whether the specified joystick is both present and has a gamepad mapping.</p>
<p>If the specified joystick is present but does not have a gamepad mapping this function will return <code>GLFW_FALSE</code> but will not generate an error. Call <a class="el" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a> to check if a joystick is present regardless of whether it has a mapping.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">jid</td><td>The <a class="el" href="group__joysticks.html">joystick</a> to query. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><code>GLFW_TRUE</code> if a joystick is both present and has a gamepad mapping, or <code>GLFW_FALSE</code> otherwise.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#gamepad">Gamepad input</a> </dd>
<dd>
<a class="el" href="group__input.html#gadccddea8bce6113fa459de379ddaf051">glfwGetGamepadState</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c" name="ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2f60a0e5b7bd8d1b7344dc0a7cb32b4c">&#9670;&#160;</a></span>glfwSetJoystickCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__input.html#gaa21ad5986ae9a26077a40142efb56243">GLFWjoystickfun</a> glfwSetJoystickCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__input.html#gaa21ad5986ae9a26077a40142efb56243">GLFWjoystickfun</a>&#160;</td>
          <td class="paramname"><em>callback</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the joystick configuration callback, or removes the currently set callback. This is called when a joystick is connected to or disconnected from the system.</p>
<p>For joystick connection and disconnection events to be delivered on all platforms, you need to call one of the <a class="el" href="input_guide.html#events">event processing</a> functions. Joystick disconnection may also be detected and the callback called by joystick functions. The function will then return whatever it returns if the joystick is not present.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<span class="keywordtype">int</span> jid, <span class="keywordtype">int</span> event)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__input.html#gaa21ad5986ae9a26077a40142efb56243">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#joystick_event">Joystick configuration changes</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="gaed5104612f2fa8e66aa6e846652ad00f" name="gaed5104612f2fa8e66aa6e846652ad00f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaed5104612f2fa8e66aa6e846652ad00f">&#9670;&#160;</a></span>glfwUpdateGamepadMappings()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwUpdateGamepadMappings </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>string</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function parses the specified ASCII encoded string and updates the internal list with any gamepad mappings it finds. This string may contain either a single gamepad mapping or many mappings separated by newlines. The parser supports the full format of the <code>gamecontrollerdb.txt</code> source file including empty lines and comments.</p>
<p>See <a class="el" href="input_guide.html#gamepad_mapping">Gamepad mappings</a> for a description of the format.</p>
<p>If there is already a gamepad mapping for a given GUID in the internal list, it will be replaced by the one passed to this function. If the library is terminated and re-initialized the internal list will revert to the built-in default.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">string</td><td>The string containing the gamepad mappings. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><code>GLFW_TRUE</code> if successful, or <code>GLFW_FALSE</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#gamepad">Gamepad input</a> </dd>
<dd>
<a class="el" href="group__input.html#gad0f676860f329d80f7e47e9f06a96f00">glfwJoystickIsGamepad</a> </dd>
<dd>
<a class="el" href="group__input.html#ga8aea73a1a25cc6c0486a617019f56728">glfwGetGamepadName</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga8aea73a1a25cc6c0486a617019f56728" name="ga8aea73a1a25cc6c0486a617019f56728"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8aea73a1a25cc6c0486a617019f56728">&#9670;&#160;</a></span>glfwGetGamepadName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char * glfwGetGamepadName </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>jid</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the human-readable name of the gamepad from the gamepad mapping assigned to the specified joystick.</p>
<p>If the specified joystick is not present or does not have a gamepad mapping this function will return <code>NULL</code> but will not generate an error. Call <a class="el" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a> to check whether it is present regardless of whether it has a mapping.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">jid</td><td>The <a class="el" href="group__joysticks.html">joystick</a> to query. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The UTF-8 encoded name of the gamepad, or <code>NULL</code> if the joystick is not present, does not have a mapping or an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned string is allocated and freed by GLFW. You should not free it yourself. It is valid until the specified joystick is disconnected, the gamepad mappings are updated or the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#gamepad">Gamepad input</a> </dd>
<dd>
<a class="el" href="group__input.html#gad0f676860f329d80f7e47e9f06a96f00">glfwJoystickIsGamepad</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="gadccddea8bce6113fa459de379ddaf051" name="gadccddea8bce6113fa459de379ddaf051"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadccddea8bce6113fa459de379ddaf051">&#9670;&#160;</a></span>glfwGetGamepadState()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwGetGamepadState </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>jid</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="struct_g_l_f_wgamepadstate.html">GLFWgamepadstate</a> *&#160;</td>
          <td class="paramname"><em>state</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function retrieves the state of the specified joystick remapped to an Xbox-like gamepad.</p>
<p>If the specified joystick is not present or does not have a gamepad mapping this function will return <code>GLFW_FALSE</code> but will not generate an error. Call <a class="el" href="group__input.html#gaed0966cee139d815317f9ffcba64c9f1">glfwJoystickPresent</a> to check whether it is present regardless of whether it has a mapping.</p>
<p>The Guide button may not be available for input as it is often hooked by the system or the Steam client.</p>
<p>Not all devices have all the buttons or axes provided by <a class="el" href="struct_g_l_f_wgamepadstate.html">GLFWgamepadstate</a>. Unavailable buttons and axes will always report <code>GLFW_RELEASE</code> and 0.0 respectively.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">jid</td><td>The <a class="el" href="group__joysticks.html">joystick</a> to query. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">state</td><td>The gamepad input state of the joystick. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><code>GLFW_TRUE</code> if successful, or <code>GLFW_FALSE</code> if no joystick is connected, it has no gamepad mapping or an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#gamepad">Gamepad input</a> </dd>
<dd>
<a class="el" href="group__input.html#gaed5104612f2fa8e66aa6e846652ad00f">glfwUpdateGamepadMappings</a> </dd>
<dd>
<a class="el" href="group__input.html#gad0f676860f329d80f7e47e9f06a96f00">glfwJoystickIsGamepad</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="gaba1f022c5eb07dfac421df34cdcd31dd" name="gaba1f022c5eb07dfac421df34cdcd31dd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaba1f022c5eb07dfac421df34cdcd31dd">&#9670;&#160;</a></span>glfwSetClipboardString()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetClipboardString </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>string</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the system clipboard to the specified, UTF-8 encoded string.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>Deprecated. Any valid window or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">string</td><td>A UTF-8 encoded string.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>Windows:</b> The clipboard on Windows has a single global lock for reading and writing. GLFW tries to acquire it a few times, which is almost always enough. If it cannot acquire the lock then this function emits <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a> and returns. It is safe to try this multiple times.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The specified string is copied before this function returns.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#clipboard">Clipboard input and output</a> </dd>
<dd>
<a class="el" href="group__input.html#ga71a5b20808ea92193d65c21b82580355">glfwGetClipboardString</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga71a5b20808ea92193d65c21b82580355" name="ga71a5b20808ea92193d65c21b82580355"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga71a5b20808ea92193d65c21b82580355">&#9670;&#160;</a></span>glfwGetClipboardString()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char * glfwGetClipboardString </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the contents of the system clipboard, if it contains or is convertible to a UTF-8 encoded string. If the clipboard is empty or if its contents cannot be converted, <code>NULL</code> is returned and a <a class="el" href="group__errors.html#ga196e125ef261d94184e2b55c05762f14">GLFW_FORMAT_UNAVAILABLE</a> error is generated.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>Deprecated. Any valid window or <code>NULL</code>. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The contents of the clipboard as a UTF-8 encoded string, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga196e125ef261d94184e2b55c05762f14">GLFW_FORMAT_UNAVAILABLE</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>Windows:</b> The clipboard on Windows has a single global lock for reading and writing. GLFW tries to acquire it a few times, which is almost always enough. If it cannot acquire the lock then this function emits <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a> and returns. It is safe to try this multiple times.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned string is allocated and freed by GLFW. You should not free it yourself. It is valid until the next call to <a class="el" href="group__input.html#ga71a5b20808ea92193d65c21b82580355">glfwGetClipboardString</a> or <a class="el" href="group__input.html#gaba1f022c5eb07dfac421df34cdcd31dd">glfwSetClipboardString</a>, or until the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#clipboard">Clipboard input and output</a> </dd>
<dd>
<a class="el" href="group__input.html#gaba1f022c5eb07dfac421df34cdcd31dd">glfwSetClipboardString</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gaa6cf4e7a77158a3b8fd00328b1720a4a" name="gaa6cf4e7a77158a3b8fd00328b1720a4a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa6cf4e7a77158a3b8fd00328b1720a4a">&#9670;&#160;</a></span>glfwGetTime()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">double glfwGetTime </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the current GLFW time, in seconds. Unless the time has been set using <a class="el" href="group__input.html#gaf59589ef6e8b8c8b5ad184b25afd4dc0">glfwSetTime</a> it measures time elapsed since GLFW was initialized.</p>
<p>This function and <a class="el" href="group__input.html#gaf59589ef6e8b8c8b5ad184b25afd4dc0">glfwSetTime</a> are helper functions on top of <a class="el" href="group__input.html#ga3289ee876572f6e91f06df3a24824443">glfwGetTimerFrequency</a> and <a class="el" href="group__input.html#ga09b2bd37d328e0b9456c7ec575cc26aa">glfwGetTimerValue</a>.</p>
<p>The resolution of the timer is system dependent, but is usually on the order of a few micro- or nanoseconds. It uses the highest-resolution monotonic time source on each operating system.</p>
<dl class="section return"><dt>Returns</dt><dd>The current time, in seconds, or zero if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Reading and writing of the internal base time is not atomic, so it needs to be externally synchronized with calls to <a class="el" href="group__input.html#gaf59589ef6e8b8c8b5ad184b25afd4dc0">glfwSetTime</a>.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#time">Time input</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. </dd></dl>

</div>
</div>
<a id="gaf59589ef6e8b8c8b5ad184b25afd4dc0" name="gaf59589ef6e8b8c8b5ad184b25afd4dc0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf59589ef6e8b8c8b5ad184b25afd4dc0">&#9670;&#160;</a></span>glfwSetTime()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetTime </td>
          <td>(</td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>time</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the current GLFW time, in seconds. The value must be a positive finite number less than or equal to 18446744073.0, which is approximately 584.5 years.</p>
<p>This function and <a class="el" href="group__input.html#gaa6cf4e7a77158a3b8fd00328b1720a4a">glfwGetTime</a> are helper functions on top of <a class="el" href="group__input.html#ga3289ee876572f6e91f06df3a24824443">glfwGetTimerFrequency</a> and <a class="el" href="group__input.html#ga09b2bd37d328e0b9456c7ec575cc26aa">glfwGetTimerValue</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">time</td><td>The new value, in seconds.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>The upper limit of GLFW time is calculated as floor((2<sup>64</sup> - 1) / 10<sup>9</sup>) and is due to implementations storing nanoseconds in 64 bits. The limit may be increased in the future.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Reading and writing of the internal base time is not atomic, so it needs to be externally synchronized with calls to <a class="el" href="group__input.html#gaa6cf4e7a77158a3b8fd00328b1720a4a">glfwGetTime</a>.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#time">Time input</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 2.2. </dd></dl>

</div>
</div>
<a id="ga09b2bd37d328e0b9456c7ec575cc26aa" name="ga09b2bd37d328e0b9456c7ec575cc26aa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga09b2bd37d328e0b9456c7ec575cc26aa">&#9670;&#160;</a></span>glfwGetTimerValue()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t glfwGetTimerValue </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the current value of the raw timer, measured in 1&#160;/&#160;frequency seconds. To get the frequency, call <a class="el" href="group__input.html#ga3289ee876572f6e91f06df3a24824443">glfwGetTimerFrequency</a>.</p>
<dl class="section return"><dt>Returns</dt><dd>The value of the timer, or zero if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#time">Time input</a> </dd>
<dd>
<a class="el" href="group__input.html#ga3289ee876572f6e91f06df3a24824443">glfwGetTimerFrequency</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="ga3289ee876572f6e91f06df3a24824443" name="ga3289ee876572f6e91f06df3a24824443"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3289ee876572f6e91f06df3a24824443">&#9670;&#160;</a></span>glfwGetTimerFrequency()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t glfwGetTimerFrequency </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the frequency, in Hz, of the raw timer.</p>
<dl class="section return"><dt>Returns</dt><dd>The frequency of the timer, in Hz, or zero if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#time">Time input</a> </dd>
<dd>
<a class="el" href="group__input.html#ga09b2bd37d328e0b9456c7ec575cc26aa">glfwGetTimerValue</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
