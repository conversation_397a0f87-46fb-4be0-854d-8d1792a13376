﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\test\ocl\test_tsdf.cpp">
      <Filter>opencv_rgbd\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\test\test_colored_kinfu.cpp">
      <Filter>opencv_rgbd\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\test\test_dynafu.cpp">
      <Filter>opencv_rgbd\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\test\test_kinfu.cpp">
      <Filter>opencv_rgbd\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\test\test_main.cpp">
      <Filter>opencv_rgbd\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\test\test_normal.cpp">
      <Filter>opencv_rgbd\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\test\test_odometry.cpp">
      <Filter>opencv_rgbd\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\test\test_pose_graph.cpp">
      <Filter>opencv_rgbd\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\test\test_registration.cpp">
      <Filter>opencv_rgbd\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\test\test_tsdf.cpp">
      <Filter>opencv_rgbd\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\test\test_utils.cpp">
      <Filter>opencv_rgbd\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\test\test_precomp.hpp">
      <Filter>opencv_rgbd\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_rgbd">
      <UniqueIdentifier>{8A6C70F2-EA96-3A1D-B0F5-CD4D62D272B8}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_rgbd\Include">
      <UniqueIdentifier>{1AF57F2B-DE36-318E-A064-88CA34A60DAE}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_rgbd\Src">
      <UniqueIdentifier>{2D1C3012-E7B4-3D9B-A4DC-02212743915D}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_rgbd\Src\ocl">
      <UniqueIdentifier>{011AB3D9-FDA5-332D-8393-58D99B43EF0B}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
