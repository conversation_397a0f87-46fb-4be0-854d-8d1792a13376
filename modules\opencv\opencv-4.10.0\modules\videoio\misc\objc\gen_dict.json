{"AdditionalImports": {"Videoio": ["\"videoio/registry.hpp\""]}, "ManualFuncs": {"VideoCapture": {"release": {"declaration": [""], "implementation": [""]}}, "VideoWriter": {"release": {"declaration": [""], "implementation": [""]}}}, "func_arg_fix": {"VideoCapture": {"(BOOL)open:(int)index apiPreference:(int)apiPreference": {"open": {"name": "openWithIndex"}}, "(BOOL)open:(int)index apiPreference:(int)apiPreference params:(IntVector*)params": {"open": {"name": "openWithIndexAndParameters"}}}}}