<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Introduction</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">Introduction </div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p><a class="anchor" id="mainpage"></a> GLFW is a free, Open Source, multi-platform library for OpenGL, OpenGL ES and Vulkan application development. It provides a simple, platform-independent API for creating windows, contexts and surfaces, reading input, handling events, etc.</p>
<p><a class="el" href="news.html">Release notes for version 3.4</a> list new features, caveats and deprecations.</p>
<p><a class="el" href="quick_guide.html">Getting started</a> is a guide for users new to GLFW. It takes you through how to write a small but complete program.</p>
<p>There are guides for each section of the API:</p>
<ul>
<li><a class="el" href="intro_guide.html">Introduction to the API</a> – initialization, error handling and high-level design</li>
<li><a class="el" href="window_guide.html">Window guide</a> – creating and working with windows and framebuffers</li>
<li><a class="el" href="context_guide.html">Context guide</a> – working with OpenGL and OpenGL ES contexts</li>
<li><a class="el" href="vulkan_guide.html">Vulkan guide</a> - working with Vulkan objects and extensions</li>
<li><a class="el" href="monitor_guide.html">Monitor guide</a> – enumerating and working with monitors and video modes</li>
<li><a class="el" href="input_guide.html">Input guide</a> – receiving events, polling and processing input</li>
</ul>
<p>Once you have written a program, see <a class="el" href="compile_guide.html">Compiling GLFW</a> and <a class="el" href="build_guide.html">Building applications</a>.</p>
<p>The <a href="modules.html">reference documentation</a> provides more detailed information about specific functions.</p>
<p><a class="el" href="moving_guide.html">Moving from GLFW 2 to 3</a> explains what has changed and how to update existing code to use the new API.</p>
<p>There is a section on <a class="el" href="intro_guide.html#guarantees_limitations">Guarantees and limitations</a> for pointer lifetimes, reentrancy, thread safety, event order and backward and forward compatibility.</p>
<p>Finally, <a class="el" href="compat_guide.html">Standards conformance</a> explains what APIs, standards and protocols GLFW uses and what happens when they are not present on a given machine.</p>
<p>This documentation was generated with Doxygen. The sources for it are available in both the <a href="https://www.glfw.org/download.html">source distribution</a> and <a href="https://github.com/glfw/glfw">GitHub repository</a>. </p>
</div></div><!-- PageDoc -->
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
