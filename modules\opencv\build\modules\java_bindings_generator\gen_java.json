{"rootdir": "D:/AI/opencv/opencv-4.10.0", "modules": [{"name": "core", "location": "modules/core"}, {"name": "imgproc", "location": "modules/imgproc"}, {"name": "ml", "location": "modules/ml"}, {"name": "phase_unwrapping", "location": "../opencv_contrib-4.10.0/modules/phase_unwrapping"}, {"name": "plot", "location": "../opencv_contrib-4.10.0/modules/plot"}, {"name": "dnn", "location": "modules/dnn"}, {"name": "dnn_superres", "location": "../opencv_contrib-4.10.0/modules/dnn_superres"}, {"name": "features2d", "location": "modules/features2d"}, {"name": "imgcodecs", "location": "modules/imgcodecs"}, {"name": "photo", "location": "modules/photo"}, {"name": "text", "location": "../opencv_contrib-4.10.0/modules/text"}, {"name": "videoio", "location": "modules/videoio"}, {"name": "xphoto", "location": "../opencv_contrib-4.10.0/modules/xphoto"}, {"name": "calib3d", "location": "modules/calib3d"}, {"name": "<PERSON><PERSON><PERSON>", "location": "modules/highgui"}, {"name": "objdetect", "location": "modules/objdetect"}, {"name": "structured_light", "location": "../opencv_contrib-4.10.0/modules/structured_light"}, {"name": "video", "location": "modules/video"}, {"name": "wechat_qrcode", "location": "../opencv_contrib-4.10.0/modules/wechat_qrcode"}, {"name": "xfeatures2d", "location": "../opencv_contrib-4.10.0/modules/xfeatures2d"}, {"name": "ximgproc", "location": "../opencv_contrib-4.10.0/modules/ximgproc"}, {"name": "aruco", "location": "../opencv_contrib-4.10.0/modules/aruco"}, {"name": "bgsegm", "location": "../opencv_contrib-4.10.0/modules/bgsegm"}, {"name": "bioinspired", "location": "../opencv_contrib-4.10.0/modules/bioinspired"}, {"name": "face", "location": "../opencv_contrib-4.10.0/modules/face"}, {"name": "tracking", "location": "../opencv_contrib-4.10.0/modules/tracking"}, {"name": "img_hash", "location": "../opencv_contrib-4.10.0/modules/img_hash"}], "files_remap": [{"src": "modules/java/generator/android/java/org/opencv/android/OpenCVLoader.java.in", "target": "D:/AI/opencv/cudabuild/configured/modules/java/generator/android/java/org/opencv/android/OpenCVLoader.java"}, {"src": "modules/java/generator/src/java/org/opencv/osgi/OpenCVNativeLoader.java.in", "target": "D:/AI/opencv/cudabuild/configured/modules/java/generator/src/java/org/opencv/osgi/OpenCVNativeLoader.java"}, {"src": "modules/core/misc/java/src/java/core+Core.jcode.in", "target": "D:/AI/opencv/cudabuild/configured/modules/core/misc/java/src/java/core+Core.jcode"}]}