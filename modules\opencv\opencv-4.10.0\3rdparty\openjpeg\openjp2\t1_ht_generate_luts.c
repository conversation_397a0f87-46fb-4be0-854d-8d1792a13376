//***************************************************************************/
// This software is released under the 2-Clause BSD license, included
// below.
//
// Copyright (c) 2021, A<PERSON><PERSON>
// Copyright (c) 2021, Kakadu Software Pty Ltd, Australia
// Copyright (c) 2021, The University of New South Wales, Australia
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
// 1. Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//
// 2. Redistributions in binary form must reproduce the above copyright
// notice, this list of conditions and the following disclaimer in the
// documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
// IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
// TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
// PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED
// TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
// PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
// LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
// NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//***************************************************************************/
// This file is part of the OpenJpeg software implementation.
// File: t1_ht_generate_luts.c
// Author: Aous Naman
// Date: 01 September 2021
//***************************************************************************/

#include <string.h>
#include <stdio.h>
#include <assert.h>
#include <stdint.h>

typedef int8_t   OPJ_INT8;
typedef uint8_t  OPJ_UINT8;
typedef int16_t  OPJ_INT16;
typedef uint16_t OPJ_UINT16;
typedef int32_t  OPJ_INT32;
typedef uint32_t OPJ_UINT32;
typedef int64_t  OPJ_INT64;
typedef uint64_t OPJ_UINT64;
typedef int OPJ_BOOL;
#define OPJ_TRUE 1
#define OPJ_FALSE 0

//************************************************************************/
/** @brief HT decoding tables, as given in the standard
  *
  *  Data in the table is arranged in this format:
  *   c_q is the context for a quad
  *   rho is the signficance pattern for a quad
  *   u_off indicate if u value is 0 (u_off is 0), or communicated
  *   e_k, e_1 EMB patterns
  *   cwd VLC codeword
  *   cwd VLC codeword length
  */
typedef struct vlc_src_table {
    int c_q, rho, u_off, e_k, e_1, cwd, cwd_len;
}
vlc_src_table_t;

// initial quad rows
static vlc_src_table_t tbl0[] = {
    {0, 0x1, 0x0, 0x0, 0x0, 0x06, 4},
    {0, 0x1, 0x1, 0x1, 0x1, 0x3F, 7},
    {0, 0x2, 0x0, 0x0, 0x0, 0x00, 3},
    {0, 0x2, 0x1, 0x2, 0x2, 0x7F, 7},
    {0, 0x3, 0x0, 0x0, 0x0, 0x11, 5},
    {0, 0x3, 0x1, 0x2, 0x2, 0x5F, 7},
    {0, 0x3, 0x1, 0x3, 0x1, 0x1F, 7},
    {0, 0x4, 0x0, 0x0, 0x0, 0x02, 3},
    {0, 0x4, 0x1, 0x4, 0x4, 0x13, 6},
    {0, 0x5, 0x0, 0x0, 0x0, 0x0E, 5},
    {0, 0x5, 0x1, 0x4, 0x4, 0x23, 6},
    {0, 0x5, 0x1, 0x5, 0x1, 0x0F, 7},
    {0, 0x6, 0x0, 0x0, 0x0, 0x03, 6},
    {0, 0x6, 0x1, 0x0, 0x0, 0x6F, 7},
    {0, 0x7, 0x0, 0x0, 0x0, 0x2F, 7},
    {0, 0x7, 0x1, 0x2, 0x2, 0x4F, 7},
    {0, 0x7, 0x1, 0x2, 0x0, 0x0D, 6},
    {0, 0x8, 0x0, 0x0, 0x0, 0x04, 3},
    {0, 0x8, 0x1, 0x8, 0x8, 0x3D, 6},
    {0, 0x9, 0x0, 0x0, 0x0, 0x1D, 6},
    {0, 0x9, 0x1, 0x0, 0x0, 0x2D, 6},
    {0, 0xA, 0x0, 0x0, 0x0, 0x01, 5},
    {0, 0xA, 0x1, 0x8, 0x8, 0x35, 6},
    {0, 0xA, 0x1, 0xA, 0x2, 0x77, 7},
    {0, 0xB, 0x0, 0x0, 0x0, 0x37, 7},
    {0, 0xB, 0x1, 0x1, 0x1, 0x57, 7},
    {0, 0xB, 0x1, 0x1, 0x0, 0x09, 6},
    {0, 0xC, 0x0, 0x0, 0x0, 0x1E, 5},
    {0, 0xC, 0x1, 0xC, 0xC, 0x17, 7},
    {0, 0xC, 0x1, 0xC, 0x4, 0x15, 6},
    {0, 0xC, 0x1, 0xC, 0x8, 0x25, 6},
    {0, 0xD, 0x0, 0x0, 0x0, 0x67, 7},
    {0, 0xD, 0x1, 0x1, 0x1, 0x27, 7},
    {0, 0xD, 0x1, 0x5, 0x4, 0x47, 7},
    {0, 0xD, 0x1, 0xD, 0x8, 0x07, 7},
    {0, 0xE, 0x0, 0x0, 0x0, 0x7B, 7},
    {0, 0xE, 0x1, 0x2, 0x2, 0x4B, 7},
    {0, 0xE, 0x1, 0xA, 0x8, 0x05, 6},
    {0, 0xE, 0x1, 0xE, 0x4, 0x3B, 7},
    {0, 0xF, 0x0, 0x0, 0x0, 0x5B, 7},
    {0, 0xF, 0x1, 0x9, 0x9, 0x1B, 7},
    {0, 0xF, 0x1, 0xB, 0xA, 0x6B, 7},
    {0, 0xF, 0x1, 0xF, 0xC, 0x2B, 7},
    {0, 0xF, 0x1, 0xF, 0x8, 0x39, 6},
    {0, 0xF, 0x1, 0xE, 0x6, 0x73, 7},
    {0, 0xF, 0x1, 0xE, 0x2, 0x19, 6},
    {0, 0xF, 0x1, 0xF, 0x5, 0x0B, 7},
    {0, 0xF, 0x1, 0xF, 0x4, 0x29, 6},
    {0, 0xF, 0x1, 0xF, 0x1, 0x33, 7},
    {1, 0x0, 0x0, 0x0, 0x0, 0x00, 2},
    {1, 0x1, 0x0, 0x0, 0x0, 0x0E, 4},
    {1, 0x1, 0x1, 0x1, 0x1, 0x1F, 7},
    {1, 0x2, 0x0, 0x0, 0x0, 0x06, 4},
    {1, 0x2, 0x1, 0x2, 0x2, 0x3B, 6},
    {1, 0x3, 0x0, 0x0, 0x0, 0x1B, 6},
    {1, 0x3, 0x1, 0x0, 0x0, 0x3D, 6},
    {1, 0x4, 0x0, 0x0, 0x0, 0x0A, 4},
    {1, 0x4, 0x1, 0x4, 0x4, 0x2B, 6},
    {1, 0x5, 0x0, 0x0, 0x0, 0x0B, 6},
    {1, 0x5, 0x1, 0x4, 0x4, 0x33, 6},
    {1, 0x5, 0x1, 0x5, 0x1, 0x7F, 7},
    {1, 0x6, 0x0, 0x0, 0x0, 0x13, 6},
    {1, 0x6, 0x1, 0x0, 0x0, 0x23, 6},
    {1, 0x7, 0x0, 0x0, 0x0, 0x3F, 7},
    {1, 0x7, 0x1, 0x2, 0x2, 0x5F, 7},
    {1, 0x7, 0x1, 0x2, 0x0, 0x03, 6},
    {1, 0x8, 0x0, 0x0, 0x0, 0x02, 4},
    {1, 0x8, 0x1, 0x8, 0x8, 0x1D, 6},
    {1, 0x9, 0x0, 0x0, 0x0, 0x2D, 6},
    {1, 0x9, 0x1, 0x0, 0x0, 0x0D, 6},
    {1, 0xA, 0x0, 0x0, 0x0, 0x35, 6},
    {1, 0xA, 0x1, 0x8, 0x8, 0x15, 6},
    {1, 0xA, 0x1, 0xA, 0x2, 0x6F, 7},
    {1, 0xB, 0x0, 0x0, 0x0, 0x2F, 7},
    {1, 0xB, 0x1, 0x1, 0x1, 0x4F, 7},
    {1, 0xB, 0x1, 0x1, 0x0, 0x11, 6},
    {1, 0xC, 0x0, 0x0, 0x0, 0x01, 5},
    {1, 0xC, 0x1, 0x8, 0x8, 0x25, 6},
    {1, 0xC, 0x1, 0xC, 0x4, 0x05, 6},
    {1, 0xD, 0x0, 0x0, 0x0, 0x0F, 7},
    {1, 0xD, 0x1, 0x1, 0x1, 0x17, 7},
    {1, 0xD, 0x1, 0x5, 0x4, 0x39, 6},
    {1, 0xD, 0x1, 0xD, 0x8, 0x77, 7},
    {1, 0xE, 0x0, 0x0, 0x0, 0x37, 7},
    {1, 0xE, 0x1, 0x2, 0x2, 0x57, 7},
    {1, 0xE, 0x1, 0xA, 0x8, 0x19, 6},
    {1, 0xE, 0x1, 0xE, 0x4, 0x67, 7},
    {1, 0xF, 0x0, 0x0, 0x0, 0x07, 7},
    {1, 0xF, 0x1, 0xB, 0x8, 0x29, 6},
    {1, 0xF, 0x1, 0x8, 0x8, 0x27, 7},
    {1, 0xF, 0x1, 0xA, 0x2, 0x09, 6},
    {1, 0xF, 0x1, 0xE, 0x4, 0x31, 6},
    {1, 0xF, 0x1, 0xF, 0x1, 0x47, 7},
    {2, 0x0, 0x0, 0x0, 0x0, 0x00, 2},
    {2, 0x1, 0x0, 0x0, 0x0, 0x0E, 4},
    {2, 0x1, 0x1, 0x1, 0x1, 0x1B, 6},
    {2, 0x2, 0x0, 0x0, 0x0, 0x06, 4},
    {2, 0x2, 0x1, 0x2, 0x2, 0x3F, 7},
    {2, 0x3, 0x0, 0x0, 0x0, 0x2B, 6},
    {2, 0x3, 0x1, 0x1, 0x1, 0x33, 6},
    {2, 0x3, 0x1, 0x3, 0x2, 0x7F, 7},
    {2, 0x4, 0x0, 0x0, 0x0, 0x0A, 4},
    {2, 0x4, 0x1, 0x4, 0x4, 0x0B, 6},
    {2, 0x5, 0x0, 0x0, 0x0, 0x01, 5},
    {2, 0x5, 0x1, 0x5, 0x5, 0x2F, 7},
    {2, 0x5, 0x1, 0x5, 0x1, 0x13, 6},
    {2, 0x5, 0x1, 0x5, 0x4, 0x23, 6},
    {2, 0x6, 0x0, 0x0, 0x0, 0x03, 6},
    {2, 0x6, 0x1, 0x0, 0x0, 0x5F, 7},
    {2, 0x7, 0x0, 0x0, 0x0, 0x1F, 7},
    {2, 0x7, 0x1, 0x2, 0x2, 0x6F, 7},
    {2, 0x7, 0x1, 0x3, 0x1, 0x11, 6},
    {2, 0x7, 0x1, 0x7, 0x4, 0x37, 7},
    {2, 0x8, 0x0, 0x0, 0x0, 0x02, 4},
    {2, 0x8, 0x1, 0x8, 0x8, 0x4F, 7},
    {2, 0x9, 0x0, 0x0, 0x0, 0x3D, 6},
    {2, 0x9, 0x1, 0x0, 0x0, 0x1D, 6},
    {2, 0xA, 0x0, 0x0, 0x0, 0x2D, 6},
    {2, 0xA, 0x1, 0x0, 0x0, 0x0D, 6},
    {2, 0xB, 0x0, 0x0, 0x0, 0x0F, 7},
    {2, 0xB, 0x1, 0x2, 0x2, 0x77, 7},
    {2, 0xB, 0x1, 0x2, 0x0, 0x35, 6},
    {2, 0xC, 0x0, 0x0, 0x0, 0x15, 6},
    {2, 0xC, 0x1, 0x4, 0x4, 0x25, 6},
    {2, 0xC, 0x1, 0xC, 0x8, 0x57, 7},
    {2, 0xD, 0x0, 0x0, 0x0, 0x17, 7},
    {2, 0xD, 0x1, 0x8, 0x8, 0x05, 6},
    {2, 0xD, 0x1, 0xC, 0x4, 0x39, 6},
    {2, 0xD, 0x1, 0xD, 0x1, 0x67, 7},
    {2, 0xE, 0x0, 0x0, 0x0, 0x27, 7},
    {2, 0xE, 0x1, 0x2, 0x2, 0x7B, 7},
    {2, 0xE, 0x1, 0x2, 0x0, 0x19, 6},
    {2, 0xF, 0x0, 0x0, 0x0, 0x47, 7},
    {2, 0xF, 0x1, 0xF, 0x1, 0x29, 6},
    {2, 0xF, 0x1, 0x1, 0x1, 0x09, 6},
    {2, 0xF, 0x1, 0x3, 0x2, 0x07, 7},
    {2, 0xF, 0x1, 0x7, 0x4, 0x31, 6},
    {2, 0xF, 0x1, 0xF, 0x8, 0x3B, 7},
    {3, 0x0, 0x0, 0x0, 0x0, 0x00, 3},
    {3, 0x1, 0x0, 0x0, 0x0, 0x04, 4},
    {3, 0x1, 0x1, 0x1, 0x1, 0x3D, 6},
    {3, 0x2, 0x0, 0x0, 0x0, 0x0C, 5},
    {3, 0x2, 0x1, 0x2, 0x2, 0x4F, 7},
    {3, 0x3, 0x0, 0x0, 0x0, 0x1D, 6},
    {3, 0x3, 0x1, 0x1, 0x1, 0x05, 6},
    {3, 0x3, 0x1, 0x3, 0x2, 0x7F, 7},
    {3, 0x4, 0x0, 0x0, 0x0, 0x16, 5},
    {3, 0x4, 0x1, 0x4, 0x4, 0x2D, 6},
    {3, 0x5, 0x0, 0x0, 0x0, 0x06, 5},
    {3, 0x5, 0x1, 0x5, 0x5, 0x1A, 5},
    {3, 0x5, 0x1, 0x5, 0x1, 0x0D, 6},
    {3, 0x5, 0x1, 0x5, 0x4, 0x35, 6},
    {3, 0x6, 0x0, 0x0, 0x0, 0x3F, 7},
    {3, 0x6, 0x1, 0x4, 0x4, 0x5F, 7},
    {3, 0x6, 0x1, 0x6, 0x2, 0x1F, 7},
    {3, 0x7, 0x0, 0x0, 0x0, 0x6F, 7},
    {3, 0x7, 0x1, 0x6, 0x6, 0x2F, 7},
    {3, 0x7, 0x1, 0x6, 0x4, 0x15, 6},
    {3, 0x7, 0x1, 0x7, 0x3, 0x77, 7},
    {3, 0x7, 0x1, 0x7, 0x1, 0x25, 6},
    {3, 0x7, 0x1, 0x7, 0x2, 0x0F, 7},
    {3, 0x8, 0x0, 0x0, 0x0, 0x0A, 5},
    {3, 0x8, 0x1, 0x8, 0x8, 0x07, 7},
    {3, 0x9, 0x0, 0x0, 0x0, 0x39, 6},
    {3, 0x9, 0x1, 0x1, 0x1, 0x37, 7},
    {3, 0x9, 0x1, 0x9, 0x8, 0x57, 7},
    {3, 0xA, 0x0, 0x0, 0x0, 0x19, 6},
    {3, 0xA, 0x1, 0x8, 0x8, 0x29, 6},
    {3, 0xA, 0x1, 0xA, 0x2, 0x17, 7},
    {3, 0xB, 0x0, 0x0, 0x0, 0x67, 7},
    {3, 0xB, 0x1, 0xB, 0x1, 0x27, 7},
    {3, 0xB, 0x1, 0x1, 0x1, 0x47, 7},
    {3, 0xB, 0x1, 0x3, 0x2, 0x09, 6},
    {3, 0xB, 0x1, 0xB, 0x8, 0x7B, 7},
    {3, 0xC, 0x0, 0x0, 0x0, 0x31, 6},
    {3, 0xC, 0x1, 0x4, 0x4, 0x11, 6},
    {3, 0xC, 0x1, 0xC, 0x8, 0x3B, 7},
    {3, 0xD, 0x0, 0x0, 0x0, 0x5B, 7},
    {3, 0xD, 0x1, 0x9, 0x9, 0x1B, 7},
    {3, 0xD, 0x1, 0xD, 0x5, 0x2B, 7},
    {3, 0xD, 0x1, 0xD, 0x1, 0x21, 6},
    {3, 0xD, 0x1, 0xD, 0xC, 0x6B, 7},
    {3, 0xD, 0x1, 0xD, 0x4, 0x01, 6},
    {3, 0xD, 0x1, 0xD, 0x8, 0x4B, 7},
    {3, 0xE, 0x0, 0x0, 0x0, 0x0B, 7},
    {3, 0xE, 0x1, 0xE, 0x4, 0x73, 7},
    {3, 0xE, 0x1, 0x4, 0x4, 0x13, 7},
    {3, 0xE, 0x1, 0xC, 0x8, 0x3E, 6},
    {3, 0xE, 0x1, 0xE, 0x2, 0x33, 7},
    {3, 0xF, 0x0, 0x0, 0x0, 0x53, 7},
    {3, 0xF, 0x1, 0xA, 0xA, 0x0E, 6},
    {3, 0xF, 0x1, 0xB, 0x9, 0x63, 7},
    {3, 0xF, 0x1, 0xF, 0xC, 0x03, 7},
    {3, 0xF, 0x1, 0xF, 0x8, 0x12, 5},
    {3, 0xF, 0x1, 0xE, 0x6, 0x23, 7},
    {3, 0xF, 0x1, 0xF, 0x5, 0x1E, 6},
    {3, 0xF, 0x1, 0xF, 0x4, 0x02, 5},
    {3, 0xF, 0x1, 0xF, 0x3, 0x43, 7},
    {3, 0xF, 0x1, 0xF, 0x1, 0x1C, 5},
    {3, 0xF, 0x1, 0xF, 0x2, 0x2E, 6},
    {4, 0x0, 0x0, 0x0, 0x0, 0x00, 2},
    {4, 0x1, 0x0, 0x0, 0x0, 0x0E, 4},
    {4, 0x1, 0x1, 0x1, 0x1, 0x3F, 7},
    {4, 0x2, 0x0, 0x0, 0x0, 0x06, 4},
    {4, 0x2, 0x1, 0x2, 0x2, 0x1B, 6},
    {4, 0x3, 0x0, 0x0, 0x0, 0x2B, 6},
    {4, 0x3, 0x1, 0x2, 0x2, 0x3D, 6},
    {4, 0x3, 0x1, 0x3, 0x1, 0x7F, 7},
    {4, 0x4, 0x0, 0x0, 0x0, 0x0A, 4},
    {4, 0x4, 0x1, 0x4, 0x4, 0x5F, 7},
    {4, 0x5, 0x0, 0x0, 0x0, 0x0B, 6},
    {4, 0x5, 0x1, 0x0, 0x0, 0x33, 6},
    {4, 0x6, 0x0, 0x0, 0x0, 0x13, 6},
    {4, 0x6, 0x1, 0x0, 0x0, 0x23, 6},
    {4, 0x7, 0x0, 0x0, 0x0, 0x1F, 7},
    {4, 0x7, 0x1, 0x4, 0x4, 0x6F, 7},
    {4, 0x7, 0x1, 0x4, 0x0, 0x03, 6},
    {4, 0x8, 0x0, 0x0, 0x0, 0x02, 4},
    {4, 0x8, 0x1, 0x8, 0x8, 0x1D, 6},
    {4, 0x9, 0x0, 0x0, 0x0, 0x11, 6},
    {4, 0x9, 0x1, 0x0, 0x0, 0x77, 7},
    {4, 0xA, 0x0, 0x0, 0x0, 0x01, 5},
    {4, 0xA, 0x1, 0xA, 0xA, 0x2F, 7},
    {4, 0xA, 0x1, 0xA, 0x2, 0x2D, 6},
    {4, 0xA, 0x1, 0xA, 0x8, 0x0D, 6},
    {4, 0xB, 0x0, 0x0, 0x0, 0x4F, 7},
    {4, 0xB, 0x1, 0xB, 0x2, 0x0F, 7},
    {4, 0xB, 0x1, 0x0, 0x0, 0x35, 6},
    {4, 0xC, 0x0, 0x0, 0x0, 0x15, 6},
    {4, 0xC, 0x1, 0x8, 0x8, 0x25, 6},
    {4, 0xC, 0x1, 0xC, 0x4, 0x37, 7},
    {4, 0xD, 0x0, 0x0, 0x0, 0x57, 7},
    {4, 0xD, 0x1, 0x1, 0x1, 0x07, 7},
    {4, 0xD, 0x1, 0x1, 0x0, 0x05, 6},
    {4, 0xE, 0x0, 0x0, 0x0, 0x17, 7},
    {4, 0xE, 0x1, 0x4, 0x4, 0x39, 6},
    {4, 0xE, 0x1, 0xC, 0x8, 0x19, 6},
    {4, 0xE, 0x1, 0xE, 0x2, 0x67, 7},
    {4, 0xF, 0x0, 0x0, 0x0, 0x27, 7},
    {4, 0xF, 0x1, 0x9, 0x9, 0x47, 7},
    {4, 0xF, 0x1, 0x9, 0x1, 0x29, 6},
    {4, 0xF, 0x1, 0x7, 0x6, 0x7B, 7},
    {4, 0xF, 0x1, 0x7, 0x2, 0x09, 6},
    {4, 0xF, 0x1, 0xB, 0x8, 0x31, 6},
    {4, 0xF, 0x1, 0xF, 0x4, 0x3B, 7},
    {5, 0x0, 0x0, 0x0, 0x0, 0x00, 3},
    {5, 0x1, 0x0, 0x0, 0x0, 0x1A, 5},
    {5, 0x1, 0x1, 0x1, 0x1, 0x7F, 7},
    {5, 0x2, 0x0, 0x0, 0x0, 0x0A, 5},
    {5, 0x2, 0x1, 0x2, 0x2, 0x1D, 6},
    {5, 0x3, 0x0, 0x0, 0x0, 0x2D, 6},
    {5, 0x3, 0x1, 0x3, 0x3, 0x5F, 7},
    {5, 0x3, 0x1, 0x3, 0x2, 0x39, 6},
    {5, 0x3, 0x1, 0x3, 0x1, 0x3F, 7},
    {5, 0x4, 0x0, 0x0, 0x0, 0x12, 5},
    {5, 0x4, 0x1, 0x4, 0x4, 0x1F, 7},
    {5, 0x5, 0x0, 0x0, 0x0, 0x0D, 6},
    {5, 0x5, 0x1, 0x4, 0x4, 0x35, 6},
    {5, 0x5, 0x1, 0x5, 0x1, 0x6F, 7},
    {5, 0x6, 0x0, 0x0, 0x0, 0x15, 6},
    {5, 0x6, 0x1, 0x2, 0x2, 0x25, 6},
    {5, 0x6, 0x1, 0x6, 0x4, 0x2F, 7},
    {5, 0x7, 0x0, 0x0, 0x0, 0x4F, 7},
    {5, 0x7, 0x1, 0x6, 0x6, 0x57, 7},
    {5, 0x7, 0x1, 0x6, 0x4, 0x05, 6},
    {5, 0x7, 0x1, 0x7, 0x3, 0x0F, 7},
    {5, 0x7, 0x1, 0x7, 0x2, 0x77, 7},
    {5, 0x7, 0x1, 0x7, 0x1, 0x37, 7},
    {5, 0x8, 0x0, 0x0, 0x0, 0x02, 5},
    {5, 0x8, 0x1, 0x8, 0x8, 0x19, 6},
    {5, 0x9, 0x0, 0x0, 0x0, 0x26, 6},
    {5, 0x9, 0x1, 0x8, 0x8, 0x17, 7},
    {5, 0x9, 0x1, 0x9, 0x1, 0x67, 7},
    {5, 0xA, 0x0, 0x0, 0x0, 0x1C, 5},
    {5, 0xA, 0x1, 0xA, 0xA, 0x29, 6},
    {5, 0xA, 0x1, 0xA, 0x2, 0x09, 6},
    {5, 0xA, 0x1, 0xA, 0x8, 0x31, 6},
    {5, 0xB, 0x0, 0x0, 0x0, 0x27, 7},
    {5, 0xB, 0x1, 0x9, 0x9, 0x07, 7},
    {5, 0xB, 0x1, 0x9, 0x8, 0x11, 6},
    {5, 0xB, 0x1, 0xB, 0x3, 0x47, 7},
    {5, 0xB, 0x1, 0xB, 0x2, 0x21, 6},
    {5, 0xB, 0x1, 0xB, 0x1, 0x7B, 7},
    {5, 0xC, 0x0, 0x0, 0x0, 0x01, 6},
    {5, 0xC, 0x1, 0x8, 0x8, 0x3E, 6},
    {5, 0xC, 0x1, 0xC, 0x4, 0x3B, 7},
    {5, 0xD, 0x0, 0x0, 0x0, 0x5B, 7},
    {5, 0xD, 0x1, 0x9, 0x9, 0x6B, 7},
    {5, 0xD, 0x1, 0x9, 0x8, 0x1E, 6},
    {5, 0xD, 0x1, 0xD, 0x5, 0x1B, 7},
    {5, 0xD, 0x1, 0xD, 0x4, 0x2E, 6},
    {5, 0xD, 0x1, 0xD, 0x1, 0x2B, 7},
    {5, 0xE, 0x0, 0x0, 0x0, 0x4B, 7},
    {5, 0xE, 0x1, 0x6, 0x6, 0x0B, 7},
    {5, 0xE, 0x1, 0xE, 0xA, 0x33, 7},
    {5, 0xE, 0x1, 0xE, 0x2, 0x0E, 6},
    {5, 0xE, 0x1, 0xE, 0xC, 0x73, 7},
    {5, 0xE, 0x1, 0xE, 0x8, 0x36, 6},
    {5, 0xE, 0x1, 0xE, 0x4, 0x53, 7},
    {5, 0xF, 0x0, 0x0, 0x0, 0x13, 7},
    {5, 0xF, 0x1, 0x7, 0x7, 0x43, 7},
    {5, 0xF, 0x1, 0x7, 0x6, 0x16, 6},
    {5, 0xF, 0x1, 0x7, 0x5, 0x63, 7},
    {5, 0xF, 0x1, 0xF, 0xC, 0x23, 7},
    {5, 0xF, 0x1, 0xF, 0x4, 0x0C, 5},
    {5, 0xF, 0x1, 0xD, 0x9, 0x03, 7},
    {5, 0xF, 0x1, 0xF, 0xA, 0x3D, 7},
    {5, 0xF, 0x1, 0xF, 0x8, 0x14, 5},
    {5, 0xF, 0x1, 0xF, 0x3, 0x7D, 7},
    {5, 0xF, 0x1, 0xF, 0x2, 0x04, 5},
    {5, 0xF, 0x1, 0xF, 0x1, 0x06, 6},
    {6, 0x0, 0x0, 0x0, 0x0, 0x00, 3},
    {6, 0x1, 0x0, 0x0, 0x0, 0x04, 4},
    {6, 0x1, 0x1, 0x1, 0x1, 0x03, 6},
    {6, 0x2, 0x0, 0x0, 0x0, 0x0C, 5},
    {6, 0x2, 0x1, 0x2, 0x2, 0x0D, 6},
    {6, 0x3, 0x0, 0x0, 0x0, 0x1A, 5},
    {6, 0x3, 0x1, 0x3, 0x3, 0x3D, 6},
    {6, 0x3, 0x1, 0x3, 0x1, 0x1D, 6},
    {6, 0x3, 0x1, 0x3, 0x2, 0x2D, 6},
    {6, 0x4, 0x0, 0x0, 0x0, 0x0A, 5},
    {6, 0x4, 0x1, 0x4, 0x4, 0x3F, 7},
    {6, 0x5, 0x0, 0x0, 0x0, 0x35, 6},
    {6, 0x5, 0x1, 0x1, 0x1, 0x15, 6},
    {6, 0x5, 0x1, 0x5, 0x4, 0x7F, 7},
    {6, 0x6, 0x0, 0x0, 0x0, 0x25, 6},
    {6, 0x6, 0x1, 0x2, 0x2, 0x5F, 7},
    {6, 0x6, 0x1, 0x6, 0x4, 0x1F, 7},
    {6, 0x7, 0x0, 0x0, 0x0, 0x6F, 7},
    {6, 0x7, 0x1, 0x6, 0x6, 0x4F, 7},
    {6, 0x7, 0x1, 0x6, 0x4, 0x05, 6},
    {6, 0x7, 0x1, 0x7, 0x3, 0x2F, 7},
    {6, 0x7, 0x1, 0x7, 0x1, 0x36, 6},
    {6, 0x7, 0x1, 0x7, 0x2, 0x77, 7},
    {6, 0x8, 0x0, 0x0, 0x0, 0x12, 5},
    {6, 0x8, 0x1, 0x8, 0x8, 0x0F, 7},
    {6, 0x9, 0x0, 0x0, 0x0, 0x39, 6},
    {6, 0x9, 0x1, 0x1, 0x1, 0x37, 7},
    {6, 0x9, 0x1, 0x9, 0x8, 0x57, 7},
    {6, 0xA, 0x0, 0x0, 0x0, 0x19, 6},
    {6, 0xA, 0x1, 0x2, 0x2, 0x29, 6},
    {6, 0xA, 0x1, 0xA, 0x8, 0x17, 7},
    {6, 0xB, 0x0, 0x0, 0x0, 0x67, 7},
    {6, 0xB, 0x1, 0x9, 0x9, 0x47, 7},
    {6, 0xB, 0x1, 0x9, 0x1, 0x09, 6},
    {6, 0xB, 0x1, 0xB, 0xA, 0x27, 7},
    {6, 0xB, 0x1, 0xB, 0x2, 0x31, 6},
    {6, 0xB, 0x1, 0xB, 0x8, 0x7B, 7},
    {6, 0xC, 0x0, 0x0, 0x0, 0x11, 6},
    {6, 0xC, 0x1, 0xC, 0xC, 0x07, 7},
    {6, 0xC, 0x1, 0xC, 0x8, 0x21, 6},
    {6, 0xC, 0x1, 0xC, 0x4, 0x3B, 7},
    {6, 0xD, 0x0, 0x0, 0x0, 0x5B, 7},
    {6, 0xD, 0x1, 0x5, 0x5, 0x33, 7},
    {6, 0xD, 0x1, 0x5, 0x4, 0x01, 6},
    {6, 0xD, 0x1, 0xC, 0x8, 0x1B, 7},
    {6, 0xD, 0x1, 0xD, 0x1, 0x6B, 7},
    {6, 0xE, 0x0, 0x0, 0x0, 0x2B, 7},
    {6, 0xE, 0x1, 0xE, 0x2, 0x4B, 7},
    {6, 0xE, 0x1, 0x2, 0x2, 0x0B, 7},
    {6, 0xE, 0x1, 0xE, 0xC, 0x73, 7},
    {6, 0xE, 0x1, 0xE, 0x8, 0x3E, 6},
    {6, 0xE, 0x1, 0xE, 0x4, 0x53, 7},
    {6, 0xF, 0x0, 0x0, 0x0, 0x13, 7},
    {6, 0xF, 0x1, 0x6, 0x6, 0x1E, 6},
    {6, 0xF, 0x1, 0xE, 0xA, 0x2E, 6},
    {6, 0xF, 0x1, 0xF, 0x3, 0x0E, 6},
    {6, 0xF, 0x1, 0xF, 0x2, 0x02, 5},
    {6, 0xF, 0x1, 0xB, 0x9, 0x63, 7},
    {6, 0xF, 0x1, 0xF, 0xC, 0x16, 6},
    {6, 0xF, 0x1, 0xF, 0x8, 0x06, 6},
    {6, 0xF, 0x1, 0xF, 0x5, 0x23, 7},
    {6, 0xF, 0x1, 0xF, 0x1, 0x1C, 5},
    {6, 0xF, 0x1, 0xF, 0x4, 0x26, 6},
    {7, 0x0, 0x0, 0x0, 0x0, 0x12, 5},
    {7, 0x1, 0x0, 0x0, 0x0, 0x05, 6},
    {7, 0x1, 0x1, 0x1, 0x1, 0x7F, 7},
    {7, 0x2, 0x0, 0x0, 0x0, 0x39, 6},
    {7, 0x2, 0x1, 0x2, 0x2, 0x3F, 7},
    {7, 0x3, 0x0, 0x0, 0x0, 0x5F, 7},
    {7, 0x3, 0x1, 0x3, 0x3, 0x1F, 7},
    {7, 0x3, 0x1, 0x3, 0x2, 0x6F, 7},
    {7, 0x3, 0x1, 0x3, 0x1, 0x2F, 7},
    {7, 0x4, 0x0, 0x0, 0x0, 0x4F, 7},
    {7, 0x4, 0x1, 0x4, 0x4, 0x0F, 7},
    {7, 0x5, 0x0, 0x0, 0x0, 0x57, 7},
    {7, 0x5, 0x1, 0x1, 0x1, 0x19, 6},
    {7, 0x5, 0x1, 0x5, 0x4, 0x77, 7},
    {7, 0x6, 0x0, 0x0, 0x0, 0x37, 7},
    {7, 0x6, 0x1, 0x0, 0x0, 0x29, 6},
    {7, 0x7, 0x0, 0x0, 0x0, 0x17, 7},
    {7, 0x7, 0x1, 0x6, 0x6, 0x67, 7},
    {7, 0x7, 0x1, 0x7, 0x3, 0x27, 7},
    {7, 0x7, 0x1, 0x7, 0x2, 0x47, 7},
    {7, 0x7, 0x1, 0x7, 0x5, 0x1B, 7},
    {7, 0x7, 0x1, 0x7, 0x1, 0x09, 6},
    {7, 0x7, 0x1, 0x7, 0x4, 0x07, 7},
    {7, 0x8, 0x0, 0x0, 0x0, 0x7B, 7},
    {7, 0x8, 0x1, 0x8, 0x8, 0x3B, 7},
    {7, 0x9, 0x0, 0x0, 0x0, 0x5B, 7},
    {7, 0x9, 0x1, 0x0, 0x0, 0x31, 6},
    {7, 0xA, 0x0, 0x0, 0x0, 0x53, 7},
    {7, 0xA, 0x1, 0x2, 0x2, 0x11, 6},
    {7, 0xA, 0x1, 0xA, 0x8, 0x6B, 7},
    {7, 0xB, 0x0, 0x0, 0x0, 0x2B, 7},
    {7, 0xB, 0x1, 0x9, 0x9, 0x4B, 7},
    {7, 0xB, 0x1, 0xB, 0x3, 0x0B, 7},
    {7, 0xB, 0x1, 0xB, 0x1, 0x73, 7},
    {7, 0xB, 0x1, 0xB, 0xA, 0x33, 7},
    {7, 0xB, 0x1, 0xB, 0x2, 0x21, 6},
    {7, 0xB, 0x1, 0xB, 0x8, 0x13, 7},
    {7, 0xC, 0x0, 0x0, 0x0, 0x63, 7},
    {7, 0xC, 0x1, 0x8, 0x8, 0x23, 7},
    {7, 0xC, 0x1, 0xC, 0x4, 0x43, 7},
    {7, 0xD, 0x0, 0x0, 0x0, 0x03, 7},
    {7, 0xD, 0x1, 0x9, 0x9, 0x7D, 7},
    {7, 0xD, 0x1, 0xD, 0x5, 0x5D, 7},
    {7, 0xD, 0x1, 0xD, 0x1, 0x01, 6},
    {7, 0xD, 0x1, 0xD, 0xC, 0x3D, 7},
    {7, 0xD, 0x1, 0xD, 0x4, 0x3E, 6},
    {7, 0xD, 0x1, 0xD, 0x8, 0x1D, 7},
    {7, 0xE, 0x0, 0x0, 0x0, 0x6D, 7},
    {7, 0xE, 0x1, 0x6, 0x6, 0x2D, 7},
    {7, 0xE, 0x1, 0xE, 0xA, 0x0D, 7},
    {7, 0xE, 0x1, 0xE, 0x2, 0x1E, 6},
    {7, 0xE, 0x1, 0xE, 0xC, 0x4D, 7},
    {7, 0xE, 0x1, 0xE, 0x8, 0x0E, 6},
    {7, 0xE, 0x1, 0xE, 0x4, 0x75, 7},
    {7, 0xF, 0x0, 0x0, 0x0, 0x15, 7},
    {7, 0xF, 0x1, 0xF, 0xF, 0x06, 5},
    {7, 0xF, 0x1, 0xF, 0xD, 0x35, 7},
    {7, 0xF, 0x1, 0xF, 0x7, 0x55, 7},
    {7, 0xF, 0x1, 0xF, 0x5, 0x1A, 5},
    {7, 0xF, 0x1, 0xF, 0xB, 0x25, 7},
    {7, 0xF, 0x1, 0xF, 0x3, 0x0A, 5},
    {7, 0xF, 0x1, 0xF, 0x9, 0x2E, 6},
    {7, 0xF, 0x1, 0xF, 0x1, 0x00, 4},
    {7, 0xF, 0x1, 0xF, 0xE, 0x65, 7},
    {7, 0xF, 0x1, 0xF, 0x6, 0x36, 6},
    {7, 0xF, 0x1, 0xF, 0xA, 0x02, 5},
    {7, 0xF, 0x1, 0xF, 0x2, 0x0C, 4},
    {7, 0xF, 0x1, 0xF, 0xC, 0x16, 6},
    {7, 0xF, 0x1, 0xF, 0x8, 0x04, 4},
    {7, 0xF, 0x1, 0xF, 0x4, 0x08, 4}
};

// nono-initial quad rows
static vlc_src_table_t tbl1[] = {
    {0, 0x1, 0x0, 0x0, 0x0, 0x00, 3},
    {0, 0x1, 0x1, 0x1, 0x1, 0x27, 6},
    {0, 0x2, 0x0, 0x0, 0x0, 0x06, 3},
    {0, 0x2, 0x1, 0x2, 0x2, 0x17, 6},
    {0, 0x3, 0x0, 0x0, 0x0, 0x0D, 5},
    {0, 0x3, 0x1, 0x0, 0x0, 0x3B, 6},
    {0, 0x4, 0x0, 0x0, 0x0, 0x02, 3},
    {0, 0x4, 0x1, 0x4, 0x4, 0x07, 6},
    {0, 0x5, 0x0, 0x0, 0x0, 0x15, 5},
    {0, 0x5, 0x1, 0x0, 0x0, 0x2B, 6},
    {0, 0x6, 0x0, 0x0, 0x0, 0x01, 5},
    {0, 0x6, 0x1, 0x0, 0x0, 0x7F, 7},
    {0, 0x7, 0x0, 0x0, 0x0, 0x1F, 7},
    {0, 0x7, 0x1, 0x0, 0x0, 0x1B, 6},
    {0, 0x8, 0x0, 0x0, 0x0, 0x04, 3},
    {0, 0x8, 0x1, 0x8, 0x8, 0x05, 5},
    {0, 0x9, 0x0, 0x0, 0x0, 0x19, 5},
    {0, 0x9, 0x1, 0x0, 0x0, 0x13, 6},
    {0, 0xA, 0x0, 0x0, 0x0, 0x09, 5},
    {0, 0xA, 0x1, 0x8, 0x8, 0x0B, 6},
    {0, 0xA, 0x1, 0xA, 0x2, 0x3F, 7},
    {0, 0xB, 0x0, 0x0, 0x0, 0x5F, 7},
    {0, 0xB, 0x1, 0x0, 0x0, 0x33, 6},
    {0, 0xC, 0x0, 0x0, 0x0, 0x11, 5},
    {0, 0xC, 0x1, 0x8, 0x8, 0x23, 6},
    {0, 0xC, 0x1, 0xC, 0x4, 0x6F, 7},
    {0, 0xD, 0x0, 0x0, 0x0, 0x0F, 7},
    {0, 0xD, 0x1, 0x0, 0x0, 0x03, 6},
    {0, 0xE, 0x0, 0x0, 0x0, 0x2F, 7},
    {0, 0xE, 0x1, 0x4, 0x4, 0x4F, 7},
    {0, 0xE, 0x1, 0x4, 0x0, 0x3D, 6},
    {0, 0xF, 0x0, 0x0, 0x0, 0x77, 7},
    {0, 0xF, 0x1, 0x1, 0x1, 0x37, 7},
    {0, 0xF, 0x1, 0x1, 0x0, 0x1D, 6},
    {1, 0x0, 0x0, 0x0, 0x0, 0x00, 1},
    {1, 0x1, 0x0, 0x0, 0x0, 0x05, 4},
    {1, 0x1, 0x1, 0x1, 0x1, 0x7F, 7},
    {1, 0x2, 0x0, 0x0, 0x0, 0x09, 4},
    {1, 0x2, 0x1, 0x2, 0x2, 0x1F, 7},
    {1, 0x3, 0x0, 0x0, 0x0, 0x1D, 5},
    {1, 0x3, 0x1, 0x1, 0x1, 0x3F, 7},
    {1, 0x3, 0x1, 0x3, 0x2, 0x5F, 7},
    {1, 0x4, 0x0, 0x0, 0x0, 0x0D, 5},
    {1, 0x4, 0x1, 0x4, 0x4, 0x37, 7},
    {1, 0x5, 0x0, 0x0, 0x0, 0x03, 6},
    {1, 0x5, 0x1, 0x0, 0x0, 0x6F, 7},
    {1, 0x6, 0x0, 0x0, 0x0, 0x2F, 7},
    {1, 0x6, 0x1, 0x0, 0x0, 0x4F, 7},
    {1, 0x7, 0x0, 0x0, 0x0, 0x0F, 7},
    {1, 0x7, 0x1, 0x0, 0x0, 0x77, 7},
    {1, 0x8, 0x0, 0x0, 0x0, 0x01, 4},
    {1, 0x8, 0x1, 0x8, 0x8, 0x17, 7},
    {1, 0x9, 0x0, 0x0, 0x0, 0x0B, 6},
    {1, 0x9, 0x1, 0x0, 0x0, 0x57, 7},
    {1, 0xA, 0x0, 0x0, 0x0, 0x33, 6},
    {1, 0xA, 0x1, 0x0, 0x0, 0x67, 7},
    {1, 0xB, 0x0, 0x0, 0x0, 0x27, 7},
    {1, 0xB, 0x1, 0x0, 0x0, 0x2B, 7},
    {1, 0xC, 0x0, 0x0, 0x0, 0x13, 6},
    {1, 0xC, 0x1, 0x0, 0x0, 0x47, 7},
    {1, 0xD, 0x0, 0x0, 0x0, 0x07, 7},
    {1, 0xD, 0x1, 0x0, 0x0, 0x7B, 7},
    {1, 0xE, 0x0, 0x0, 0x0, 0x3B, 7},
    {1, 0xE, 0x1, 0x0, 0x0, 0x5B, 7},
    {1, 0xF, 0x0, 0x0, 0x0, 0x1B, 7},
    {1, 0xF, 0x1, 0x4, 0x4, 0x6B, 7},
    {1, 0xF, 0x1, 0x4, 0x0, 0x23, 6},
    {2, 0x0, 0x0, 0x0, 0x0, 0x00, 1},
    {2, 0x1, 0x0, 0x0, 0x0, 0x09, 4},
    {2, 0x1, 0x1, 0x1, 0x1, 0x7F, 7},
    {2, 0x2, 0x0, 0x0, 0x0, 0x01, 4},
    {2, 0x2, 0x1, 0x2, 0x2, 0x23, 6},
    {2, 0x3, 0x0, 0x0, 0x0, 0x3D, 6},
    {2, 0x3, 0x1, 0x2, 0x2, 0x3F, 7},
    {2, 0x3, 0x1, 0x3, 0x1, 0x1F, 7},
    {2, 0x4, 0x0, 0x0, 0x0, 0x15, 5},
    {2, 0x4, 0x1, 0x4, 0x4, 0x5F, 7},
    {2, 0x5, 0x0, 0x0, 0x0, 0x03, 6},
    {2, 0x5, 0x1, 0x0, 0x0, 0x6F, 7},
    {2, 0x6, 0x0, 0x0, 0x0, 0x2F, 7},
    {2, 0x6, 0x1, 0x0, 0x0, 0x4F, 7},
    {2, 0x7, 0x0, 0x0, 0x0, 0x0F, 7},
    {2, 0x7, 0x1, 0x0, 0x0, 0x17, 7},
    {2, 0x8, 0x0, 0x0, 0x0, 0x05, 5},
    {2, 0x8, 0x1, 0x8, 0x8, 0x77, 7},
    {2, 0x9, 0x0, 0x0, 0x0, 0x37, 7},
    {2, 0x9, 0x1, 0x0, 0x0, 0x57, 7},
    {2, 0xA, 0x0, 0x0, 0x0, 0x1D, 6},
    {2, 0xA, 0x1, 0xA, 0xA, 0x7B, 7},
    {2, 0xA, 0x1, 0xA, 0x2, 0x2D, 6},
    {2, 0xA, 0x1, 0xA, 0x8, 0x67, 7},
    {2, 0xB, 0x0, 0x0, 0x0, 0x27, 7},
    {2, 0xB, 0x1, 0xB, 0x2, 0x47, 7},
    {2, 0xB, 0x1, 0x0, 0x0, 0x07, 7},
    {2, 0xC, 0x0, 0x0, 0x0, 0x0D, 6},
    {2, 0xC, 0x1, 0x0, 0x0, 0x3B, 7},
    {2, 0xD, 0x0, 0x0, 0x0, 0x5B, 7},
    {2, 0xD, 0x1, 0x0, 0x0, 0x1B, 7},
    {2, 0xE, 0x0, 0x0, 0x0, 0x6B, 7},
    {2, 0xE, 0x1, 0x4, 0x4, 0x2B, 7},
    {2, 0xE, 0x1, 0x4, 0x0, 0x4B, 7},
    {2, 0xF, 0x0, 0x0, 0x0, 0x0B, 7},
    {2, 0xF, 0x1, 0x4, 0x4, 0x73, 7},
    {2, 0xF, 0x1, 0x5, 0x1, 0x33, 7},
    {2, 0xF, 0x1, 0x7, 0x2, 0x53, 7},
    {2, 0xF, 0x1, 0xF, 0x8, 0x13, 7},
    {3, 0x0, 0x0, 0x0, 0x0, 0x00, 2},
    {3, 0x1, 0x0, 0x0, 0x0, 0x0A, 4},
    {3, 0x1, 0x1, 0x1, 0x1, 0x0B, 6},
    {3, 0x2, 0x0, 0x0, 0x0, 0x02, 4},
    {3, 0x2, 0x1, 0x2, 0x2, 0x23, 6},
    {3, 0x3, 0x0, 0x0, 0x0, 0x0E, 5},
    {3, 0x3, 0x1, 0x3, 0x3, 0x7F, 7},
    {3, 0x3, 0x1, 0x3, 0x2, 0x33, 6},
    {3, 0x3, 0x1, 0x3, 0x1, 0x13, 6},
    {3, 0x4, 0x0, 0x0, 0x0, 0x16, 5},
    {3, 0x4, 0x1, 0x4, 0x4, 0x3F, 7},
    {3, 0x5, 0x0, 0x0, 0x0, 0x03, 6},
    {3, 0x5, 0x1, 0x1, 0x1, 0x3D, 6},
    {3, 0x5, 0x1, 0x5, 0x4, 0x1F, 7},
    {3, 0x6, 0x0, 0x0, 0x0, 0x1D, 6},
    {3, 0x6, 0x1, 0x0, 0x0, 0x5F, 7},
    {3, 0x7, 0x0, 0x0, 0x0, 0x2D, 6},
    {3, 0x7, 0x1, 0x4, 0x4, 0x2F, 7},
    {3, 0x7, 0x1, 0x5, 0x1, 0x1E, 6},
    {3, 0x7, 0x1, 0x7, 0x2, 0x6F, 7},
    {3, 0x8, 0x0, 0x0, 0x0, 0x06, 5},
    {3, 0x8, 0x1, 0x8, 0x8, 0x4F, 7},
    {3, 0x9, 0x0, 0x0, 0x0, 0x0D, 6},
    {3, 0x9, 0x1, 0x0, 0x0, 0x35, 6},
    {3, 0xA, 0x0, 0x0, 0x0, 0x15, 6},
    {3, 0xA, 0x1, 0x2, 0x2, 0x25, 6},
    {3, 0xA, 0x1, 0xA, 0x8, 0x0F, 7},
    {3, 0xB, 0x0, 0x0, 0x0, 0x05, 6},
    {3, 0xB, 0x1, 0x8, 0x8, 0x39, 6},
    {3, 0xB, 0x1, 0xB, 0x3, 0x17, 7},
    {3, 0xB, 0x1, 0xB, 0x2, 0x19, 6},
    {3, 0xB, 0x1, 0xB, 0x1, 0x77, 7},
    {3, 0xC, 0x0, 0x0, 0x0, 0x29, 6},
    {3, 0xC, 0x1, 0x0, 0x0, 0x09, 6},
    {3, 0xD, 0x0, 0x0, 0x0, 0x37, 7},
    {3, 0xD, 0x1, 0x4, 0x4, 0x57, 7},
    {3, 0xD, 0x1, 0x4, 0x0, 0x31, 6},
    {3, 0xE, 0x0, 0x0, 0x0, 0x67, 7},
    {3, 0xE, 0x1, 0x4, 0x4, 0x27, 7},
    {3, 0xE, 0x1, 0xC, 0x8, 0x47, 7},
    {3, 0xE, 0x1, 0xE, 0x2, 0x6B, 7},
    {3, 0xF, 0x0, 0x0, 0x0, 0x11, 6},
    {3, 0xF, 0x1, 0x6, 0x6, 0x07, 7},
    {3, 0xF, 0x1, 0x7, 0x3, 0x7B, 7},
    {3, 0xF, 0x1, 0xF, 0xA, 0x3B, 7},
    {3, 0xF, 0x1, 0xF, 0x2, 0x21, 6},
    {3, 0xF, 0x1, 0xF, 0x8, 0x01, 6},
    {3, 0xF, 0x1, 0xA, 0x8, 0x5B, 7},
    {3, 0xF, 0x1, 0xF, 0x5, 0x1B, 7},
    {3, 0xF, 0x1, 0xF, 0x1, 0x3E, 6},
    {3, 0xF, 0x1, 0xF, 0x4, 0x2B, 7},
    {4, 0x0, 0x0, 0x0, 0x0, 0x00, 1},
    {4, 0x1, 0x0, 0x0, 0x0, 0x0D, 5},
    {4, 0x1, 0x1, 0x1, 0x1, 0x7F, 7},
    {4, 0x2, 0x0, 0x0, 0x0, 0x15, 5},
    {4, 0x2, 0x1, 0x2, 0x2, 0x3F, 7},
    {4, 0x3, 0x0, 0x0, 0x0, 0x5F, 7},
    {4, 0x3, 0x1, 0x0, 0x0, 0x6F, 7},
    {4, 0x4, 0x0, 0x0, 0x0, 0x09, 4},
    {4, 0x4, 0x1, 0x4, 0x4, 0x23, 6},
    {4, 0x5, 0x0, 0x0, 0x0, 0x33, 6},
    {4, 0x5, 0x1, 0x0, 0x0, 0x1F, 7},
    {4, 0x6, 0x0, 0x0, 0x0, 0x13, 6},
    {4, 0x6, 0x1, 0x0, 0x0, 0x2F, 7},
    {4, 0x7, 0x0, 0x0, 0x0, 0x4F, 7},
    {4, 0x7, 0x1, 0x0, 0x0, 0x57, 7},
    {4, 0x8, 0x0, 0x0, 0x0, 0x01, 4},
    {4, 0x8, 0x1, 0x8, 0x8, 0x0F, 7},
    {4, 0x9, 0x0, 0x0, 0x0, 0x77, 7},
    {4, 0x9, 0x1, 0x0, 0x0, 0x37, 7},
    {4, 0xA, 0x0, 0x0, 0x0, 0x1D, 6},
    {4, 0xA, 0x1, 0x0, 0x0, 0x17, 7},
    {4, 0xB, 0x0, 0x0, 0x0, 0x67, 7},
    {4, 0xB, 0x1, 0x0, 0x0, 0x6B, 7},
    {4, 0xC, 0x0, 0x0, 0x0, 0x05, 5},
    {4, 0xC, 0x1, 0xC, 0xC, 0x27, 7},
    {4, 0xC, 0x1, 0xC, 0x8, 0x47, 7},
    {4, 0xC, 0x1, 0xC, 0x4, 0x07, 7},
    {4, 0xD, 0x0, 0x0, 0x0, 0x7B, 7},
    {4, 0xD, 0x1, 0x0, 0x0, 0x3B, 7},
    {4, 0xE, 0x0, 0x0, 0x0, 0x5B, 7},
    {4, 0xE, 0x1, 0x2, 0x2, 0x1B, 7},
    {4, 0xE, 0x1, 0x2, 0x0, 0x03, 6},
    {4, 0xF, 0x0, 0x0, 0x0, 0x2B, 7},
    {4, 0xF, 0x1, 0x1, 0x1, 0x4B, 7},
    {4, 0xF, 0x1, 0x3, 0x2, 0x0B, 7},
    {4, 0xF, 0x1, 0x3, 0x0, 0x3D, 6},
    {5, 0x0, 0x0, 0x0, 0x0, 0x00, 2},
    {5, 0x1, 0x0, 0x0, 0x0, 0x1E, 5},
    {5, 0x1, 0x1, 0x1, 0x1, 0x3B, 6},
    {5, 0x2, 0x0, 0x0, 0x0, 0x0A, 5},
    {5, 0x2, 0x1, 0x2, 0x2, 0x3F, 7},
    {5, 0x3, 0x0, 0x0, 0x0, 0x1B, 6},
    {5, 0x3, 0x1, 0x0, 0x0, 0x0B, 6},
    {5, 0x4, 0x0, 0x0, 0x0, 0x02, 4},
    {5, 0x4, 0x1, 0x4, 0x4, 0x2B, 6},
    {5, 0x5, 0x0, 0x0, 0x0, 0x0E, 5},
    {5, 0x5, 0x1, 0x4, 0x4, 0x33, 6},
    {5, 0x5, 0x1, 0x5, 0x1, 0x7F, 7},
    {5, 0x6, 0x0, 0x0, 0x0, 0x13, 6},
    {5, 0x6, 0x1, 0x0, 0x0, 0x6F, 7},
    {5, 0x7, 0x0, 0x0, 0x0, 0x23, 6},
    {5, 0x7, 0x1, 0x2, 0x2, 0x5F, 7},
    {5, 0x7, 0x1, 0x2, 0x0, 0x15, 6},
    {5, 0x8, 0x0, 0x0, 0x0, 0x16, 5},
    {5, 0x8, 0x1, 0x8, 0x8, 0x03, 6},
    {5, 0x9, 0x0, 0x0, 0x0, 0x3D, 6},
    {5, 0x9, 0x1, 0x0, 0x0, 0x1F, 7},
    {5, 0xA, 0x0, 0x0, 0x0, 0x1D, 6},
    {5, 0xA, 0x1, 0x0, 0x0, 0x2D, 6},
    {5, 0xB, 0x0, 0x0, 0x0, 0x0D, 6},
    {5, 0xB, 0x1, 0x1, 0x1, 0x4F, 7},
    {5, 0xB, 0x1, 0x1, 0x0, 0x35, 6},
    {5, 0xC, 0x0, 0x0, 0x0, 0x06, 5},
    {5, 0xC, 0x1, 0x4, 0x4, 0x25, 6},
    {5, 0xC, 0x1, 0xC, 0x8, 0x2F, 7},
    {5, 0xD, 0x0, 0x0, 0x0, 0x05, 6},
    {5, 0xD, 0x1, 0x1, 0x1, 0x77, 7},
    {5, 0xD, 0x1, 0x5, 0x4, 0x39, 6},
    {5, 0xD, 0x1, 0xD, 0x8, 0x0F, 7},
    {5, 0xE, 0x0, 0x0, 0x0, 0x19, 6},
    {5, 0xE, 0x1, 0x2, 0x2, 0x57, 7},
    {5, 0xE, 0x1, 0xA, 0x8, 0x01, 6},
    {5, 0xE, 0x1, 0xE, 0x4, 0x37, 7},
    {5, 0xF, 0x0, 0x0, 0x0, 0x1A, 5},
    {5, 0xF, 0x1, 0x9, 0x9, 0x17, 7},
    {5, 0xF, 0x1, 0xD, 0x5, 0x67, 7},
    {5, 0xF, 0x1, 0xF, 0x3, 0x07, 7},
    {5, 0xF, 0x1, 0xF, 0x1, 0x29, 6},
    {5, 0xF, 0x1, 0x7, 0x6, 0x27, 7},
    {5, 0xF, 0x1, 0xF, 0xC, 0x09, 6},
    {5, 0xF, 0x1, 0xF, 0x4, 0x31, 6},
    {5, 0xF, 0x1, 0xF, 0xA, 0x47, 7},
    {5, 0xF, 0x1, 0xF, 0x8, 0x11, 6},
    {5, 0xF, 0x1, 0xF, 0x2, 0x21, 6},
    {6, 0x0, 0x0, 0x0, 0x0, 0x00, 3},
    {6, 0x1, 0x0, 0x0, 0x0, 0x02, 4},
    {6, 0x1, 0x1, 0x1, 0x1, 0x03, 6},
    {6, 0x2, 0x0, 0x0, 0x0, 0x0C, 4},
    {6, 0x2, 0x1, 0x2, 0x2, 0x3D, 6},
    {6, 0x3, 0x0, 0x0, 0x0, 0x1D, 6},
    {6, 0x3, 0x1, 0x2, 0x2, 0x0D, 6},
    {6, 0x3, 0x1, 0x3, 0x1, 0x7F, 7},
    {6, 0x4, 0x0, 0x0, 0x0, 0x04, 4},
    {6, 0x4, 0x1, 0x4, 0x4, 0x2D, 6},
    {6, 0x5, 0x0, 0x0, 0x0, 0x0A, 5},
    {6, 0x5, 0x1, 0x4, 0x4, 0x35, 6},
    {6, 0x5, 0x1, 0x5, 0x1, 0x2F, 7},
    {6, 0x6, 0x0, 0x0, 0x0, 0x15, 6},
    {6, 0x6, 0x1, 0x2, 0x2, 0x3F, 7},
    {6, 0x6, 0x1, 0x6, 0x4, 0x5F, 7},
    {6, 0x7, 0x0, 0x0, 0x0, 0x25, 6},
    {6, 0x7, 0x1, 0x2, 0x2, 0x29, 6},
    {6, 0x7, 0x1, 0x3, 0x1, 0x1F, 7},
    {6, 0x7, 0x1, 0x7, 0x4, 0x6F, 7},
    {6, 0x8, 0x0, 0x0, 0x0, 0x16, 5},
    {6, 0x8, 0x1, 0x8, 0x8, 0x05, 6},
    {6, 0x9, 0x0, 0x0, 0x0, 0x39, 6},
    {6, 0x9, 0x1, 0x0, 0x0, 0x19, 6},
    {6, 0xA, 0x0, 0x0, 0x0, 0x06, 5},
    {6, 0xA, 0x1, 0xA, 0xA, 0x0F, 7},
    {6, 0xA, 0x1, 0xA, 0x2, 0x09, 6},
    {6, 0xA, 0x1, 0xA, 0x8, 0x4F, 7},
    {6, 0xB, 0x0, 0x0, 0x0, 0x0E, 6},
    {6, 0xB, 0x1, 0xB, 0x2, 0x77, 7},
    {6, 0xB, 0x1, 0x2, 0x2, 0x37, 7},
    {6, 0xB, 0x1, 0xA, 0x8, 0x57, 7},
    {6, 0xB, 0x1, 0xB, 0x1, 0x47, 7},
    {6, 0xC, 0x0, 0x0, 0x0, 0x1A, 5},
    {6, 0xC, 0x1, 0xC, 0xC, 0x17, 7},
    {6, 0xC, 0x1, 0xC, 0x8, 0x67, 7},
    {6, 0xC, 0x1, 0xC, 0x4, 0x27, 7},
    {6, 0xD, 0x0, 0x0, 0x0, 0x31, 6},
    {6, 0xD, 0x1, 0xD, 0x4, 0x07, 7},
    {6, 0xD, 0x1, 0x4, 0x4, 0x7B, 7},
    {6, 0xD, 0x1, 0xC, 0x8, 0x3B, 7},
    {6, 0xD, 0x1, 0xD, 0x1, 0x2B, 7},
    {6, 0xE, 0x0, 0x0, 0x0, 0x11, 6},
    {6, 0xE, 0x1, 0xE, 0x4, 0x5B, 7},
    {6, 0xE, 0x1, 0x4, 0x4, 0x1B, 7},
    {6, 0xE, 0x1, 0xE, 0xA, 0x6B, 7},
    {6, 0xE, 0x1, 0xE, 0x8, 0x21, 6},
    {6, 0xE, 0x1, 0xE, 0x2, 0x33, 7},
    {6, 0xF, 0x0, 0x0, 0x0, 0x01, 6},
    {6, 0xF, 0x1, 0x3, 0x3, 0x4B, 7},
    {6, 0xF, 0x1, 0x7, 0x6, 0x0B, 7},
    {6, 0xF, 0x1, 0xF, 0xA, 0x73, 7},
    {6, 0xF, 0x1, 0xF, 0x2, 0x3E, 6},
    {6, 0xF, 0x1, 0xB, 0x9, 0x53, 7},
    {6, 0xF, 0x1, 0xF, 0xC, 0x63, 7},
    {6, 0xF, 0x1, 0xF, 0x8, 0x1E, 6},
    {6, 0xF, 0x1, 0xF, 0x5, 0x13, 7},
    {6, 0xF, 0x1, 0xF, 0x4, 0x2E, 6},
    {6, 0xF, 0x1, 0xF, 0x1, 0x23, 7},
    {7, 0x0, 0x0, 0x0, 0x0, 0x04, 4},
    {7, 0x1, 0x0, 0x0, 0x0, 0x33, 6},
    {7, 0x1, 0x1, 0x1, 0x1, 0x13, 6},
    {7, 0x2, 0x0, 0x0, 0x0, 0x23, 6},
    {7, 0x2, 0x1, 0x2, 0x2, 0x7F, 7},
    {7, 0x3, 0x0, 0x0, 0x0, 0x03, 6},
    {7, 0x3, 0x1, 0x1, 0x1, 0x3F, 7},
    {7, 0x3, 0x1, 0x3, 0x2, 0x6F, 7},
    {7, 0x4, 0x0, 0x0, 0x0, 0x2D, 6},
    {7, 0x4, 0x1, 0x4, 0x4, 0x5F, 7},
    {7, 0x5, 0x0, 0x0, 0x0, 0x16, 5},
    {7, 0x5, 0x1, 0x1, 0x1, 0x3D, 6},
    {7, 0x5, 0x1, 0x5, 0x4, 0x1F, 7},
    {7, 0x6, 0x0, 0x0, 0x0, 0x1D, 6},
    {7, 0x6, 0x1, 0x0, 0x0, 0x77, 7},
    {7, 0x7, 0x0, 0x0, 0x0, 0x06, 5},
    {7, 0x7, 0x1, 0x7, 0x4, 0x2F, 7},
    {7, 0x7, 0x1, 0x4, 0x4, 0x4F, 7},
    {7, 0x7, 0x1, 0x7, 0x3, 0x0F, 7},
    {7, 0x7, 0x1, 0x7, 0x1, 0x0D, 6},
    {7, 0x7, 0x1, 0x7, 0x2, 0x57, 7},
    {7, 0x8, 0x0, 0x0, 0x0, 0x35, 6},
    {7, 0x8, 0x1, 0x8, 0x8, 0x37, 7},
    {7, 0x9, 0x0, 0x0, 0x0, 0x15, 6},
    {7, 0x9, 0x1, 0x0, 0x0, 0x27, 7},
    {7, 0xA, 0x0, 0x0, 0x0, 0x25, 6},
    {7, 0xA, 0x1, 0x0, 0x0, 0x29, 6},
    {7, 0xB, 0x0, 0x0, 0x0, 0x1A, 5},
    {7, 0xB, 0x1, 0xB, 0x1, 0x17, 7},
    {7, 0xB, 0x1, 0x1, 0x1, 0x67, 7},
    {7, 0xB, 0x1, 0x3, 0x2, 0x05, 6},
    {7, 0xB, 0x1, 0xB, 0x8, 0x7B, 7},
    {7, 0xC, 0x0, 0x0, 0x0, 0x39, 6},
    {7, 0xC, 0x1, 0x0, 0x0, 0x19, 6},
    {7, 0xD, 0x0, 0x0, 0x0, 0x0C, 5},
    {7, 0xD, 0x1, 0xD, 0x1, 0x47, 7},
    {7, 0xD, 0x1, 0x1, 0x1, 0x07, 7},
    {7, 0xD, 0x1, 0x5, 0x4, 0x09, 6},
    {7, 0xD, 0x1, 0xD, 0x8, 0x1B, 7},
    {7, 0xE, 0x0, 0x0, 0x0, 0x31, 6},
    {7, 0xE, 0x1, 0xE, 0x2, 0x3B, 7},
    {7, 0xE, 0x1, 0x2, 0x2, 0x5B, 7},
    {7, 0xE, 0x1, 0xA, 0x8, 0x3E, 6},
    {7, 0xE, 0x1, 0xE, 0x4, 0x0B, 7},
    {7, 0xF, 0x0, 0x0, 0x0, 0x00, 3},
    {7, 0xF, 0x1, 0xF, 0xF, 0x6B, 7},
    {7, 0xF, 0x1, 0xF, 0x7, 0x2B, 7},
    {7, 0xF, 0x1, 0xF, 0xB, 0x4B, 7},
    {7, 0xF, 0x1, 0xF, 0x3, 0x11, 6},
    {7, 0xF, 0x1, 0x7, 0x6, 0x21, 6},
    {7, 0xF, 0x1, 0xF, 0xA, 0x01, 6},
    {7, 0xF, 0x1, 0xF, 0x2, 0x0A, 5},
    {7, 0xF, 0x1, 0xB, 0x9, 0x1E, 6},
    {7, 0xF, 0x1, 0xF, 0xC, 0x0E, 6},
    {7, 0xF, 0x1, 0xF, 0x8, 0x12, 5},
    {7, 0xF, 0x1, 0xF, 0x5, 0x2E, 6},
    {7, 0xF, 0x1, 0xF, 0x1, 0x02, 5},
    {7, 0xF, 0x1, 0xF, 0x4, 0x1C, 5}
};

//************************************************************************/
/** @defgroup vlc_decoding_tables_grp VLC decoding tables
  *  @{
  *  VLC tables to decode VLC codewords to these fields: (in order)       \n
  *  \li \c cwd_len : 3bits -> the codeword length of the VLC codeword;
  *                   the VLC cwd is in the LSB of bitstream              \n
  *  \li \c u_off   : 1bit  -> u_offset, which is 1 if u value is not 0   \n
  *  \li \c rho     : 4bits -> significant samples within a quad          \n
  *  \li \c e_1     : 4bits -> EMB e_1                                    \n
  *  \li \c e_k     : 4bits -> EMB e_k                                    \n
  *                                                                       \n
  *  The table index is 10 bits and composed of two parts:                \n
  *  The 7 LSBs contain a codeword which might be shorter than 7 bits;
  *  this word is the next decoable bits in the bitstream.                \n
  *  The 3 MSB is the context of for the codeword.                        \n
  */

/// @brief vlc_tbl0 contains decoding information for initial row of quads
int vlc_tbl0[1024] = { 0 };
/// @brief vlc_tbl1 contains decoding information for non-initial row of
///        quads
int vlc_tbl1[1024] = { 0 };
/// @}

//************************************************************************/
/** @ingroup vlc_decoding_tables_grp
  *  @brief Initializes vlc_tbl0 and vlc_tbl1 tables, from table0.h and
  *         table1.h
  */
OPJ_BOOL vlc_init_tables()
{
    const OPJ_BOOL debug = OPJ_FALSE; //useful for checking

    // number of entries in the table
    size_t tbl0_size = sizeof(tbl0) / sizeof(vlc_src_table_t);

    // number of entries in the table
    size_t tbl1_size = sizeof(tbl1) / sizeof(vlc_src_table_t);

    if (debug) {
        memset(vlc_tbl0, 0, sizeof(vlc_tbl0));    //unnecessary
    }

    // this is to convert table entries into values for decoder look up
    // There can be at most 1024 possibilities, not all of them are valid.
    //
    for (int i = 0; i < 1024; ++i) {
        int cwd = i & 0x7F; // from i extract codeword
        int c_q = i >> 7;   // from i extract context
        // See if this case exist in the table, if so then set the entry in
        // vlc_tbl0
        for (size_t j = 0; j < tbl0_size; ++j)
            if (tbl0[j].c_q == c_q) // this is an and operation
                if (tbl0[j].cwd == (cwd & ((1 << tbl0[j].cwd_len) - 1))) {
                    if (debug) {
                        assert(vlc_tbl0[i] == 0);
                    }
                    // Put this entry into the table
                    vlc_tbl0[i] = (tbl0[j].rho << 4) | (tbl0[j].u_off << 3)
                                  | (tbl0[j].e_k << 12) | (tbl0[j].e_1 << 8) | tbl0[j].cwd_len;
                }
    }

    if (debug) {
        memset(vlc_tbl1, 0, sizeof(vlc_tbl1));    //unnecessary
    }

    // this the same as above but for non-initial rows
    for (int i = 0; i < 1024; ++i) {
        int cwd = i & 0x7F; //7 bits
        int c_q = i >> 7;
        for (size_t j = 0; j < tbl1_size; ++j)
            if (tbl1[j].c_q == c_q) // this is an and operation
                if (tbl1[j].cwd == (cwd & ((1 << tbl1[j].cwd_len) - 1))) {
                    if (debug) {
                        assert(vlc_tbl1[i] == 0);
                    }
                    vlc_tbl1[i] = (tbl1[j].rho << 4) | (tbl1[j].u_off << 3)
                                  | (tbl1[j].e_k << 12) | (tbl1[j].e_1 << 8) | tbl1[j].cwd_len;
                }
    }

    return OPJ_TRUE;
}

//************************************************************************/
/** @ingroup vlc_decoding_tables_grp
  *  @brief Initializes VLC tables vlc_tbl0 and vlc_tbl1
  */
OPJ_BOOL vlc_tables_initialized = OPJ_FALSE;

