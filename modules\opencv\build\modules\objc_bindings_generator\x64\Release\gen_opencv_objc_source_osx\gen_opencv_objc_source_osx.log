﻿  Generate files for Objective-C bindings (osx)
  duplicated: CLASS cv::.Algorithm : 
  SKIP:void cv::Algorithm::write(FileStorage fs)	 due to ARG type FileStorage/I
  SKIP:void cv::Algorithm::write(FileStorage fs, String name)	 due to ARG type FileStorage/I
  SKIP:void cv::Algorithm::read(FileNode fn)	 due to ARG type FileNode/I
  SKIP:vector_Target cv::dnn::getAvailableTargets(dnn_Backend be)	 due to RET type vector_Target
  SKIP:AsyncArray cv::dnn::Net::forwardAsync(String outputName = String())	 due to RET type AsyncArray
  SKIP:void cv::DescriptorMatcher::read(FileNode arg1)	 due to ARG type FileNode/I
  SKIP:void cv::DescriptorMatcher::write(FileStorage fs, String name)	 due to ARG type FileStorage/I
  SKIP:void cv::Feature2D::read(FileNode arg1)	 due to ARG type FileNode/I
  SKIP:void cv::Feature2D::write(FileStorage fs, String name)	 due to ARG type FileStorage/I
  SKIP:void cv::cuda::nonLocalMeans(GpuMat src, GpuMat& dst, float h, int search_window = 21, int block_size = 7, int borderMode = BORDER_DEFAULT,  _hidden_  stream = Stream::Null())	 due to ARG type GpuMat/I
  SKIP:void cv::cuda::fastNlMeansDenoising(GpuMat src, GpuMat& dst, float h, int search_window = 21, int block_size = 7, Stream stream = Stream::Null())	 due to ARG type GpuMat/I
  SKIP:void cv::cuda::fastNlMeansDenoisingColored(GpuMat src, GpuMat& dst, float h_luminance, float photo_render, int search_window = 21, int block_size = 7, Stream stream = Stream::Null())	 due to ARG type GpuMat/I
  SKIP:vector_VideoCaptureAPIs cv::videoio_registry::getBackends()	 due to RET type vector_VideoCaptureAPIs
  SKIP:vector_VideoCaptureAPIs cv::videoio_registry::getCameraBackends()	 due to RET type vector_VideoCaptureAPIs
  SKIP:vector_VideoCaptureAPIs cv::videoio_registry::getStreamBackends()	 due to RET type vector_VideoCaptureAPIs
  SKIP:vector_VideoCaptureAPIs cv::videoio_registry::getWriterBackends()	 due to RET type vector_VideoCaptureAPIs
  SKIP:static bool cv::VideoCapture::waitAny(vector_VideoCapture streams, vector_int& readyIndex, int64 timeoutNs = 0)	 due to ARG type vector_VideoCapture/I
  SKIP:bool cv::CascadeClassifier::read(FileNode node)	 due to ARG type FileNode/I
  SKIP:aruco_DetectorParameters cv::QRCodeDetectorAruco::getArucoParameters()	 due to RET type aruco_DetectorParameters
  SKIP:void cv::QRCodeDetectorAruco::setArucoParameters(aruco_DetectorParameters params)	 due to ARG type aruco_DetectorParameters/I
  SKIP:void cv::aruco::ArucoDetector::write(FileStorage fs, String name)	 due to ARG type FileStorage/I
  SKIP:void cv::aruco::ArucoDetector::read(FileNode fn)	 due to ARG type FileNode/I
  SKIP:bool cv::aruco::DetectorParameters::readDetectorParameters(FileNode fn)	 due to ARG type FileNode/I
  SKIP:bool cv::aruco::DetectorParameters::writeDetectorParameters(FileStorage fs, String name = String())	 due to ARG type FileStorage/I
  SKIP:bool cv::aruco::Dictionary::readDictionary(FileNode fn)	 due to ARG type FileNode/I
  SKIP:void cv::aruco::Dictionary::writeDictionary(FileStorage fs, String name = String())	 due to ARG type FileStorage/I
  SKIP:bool cv::aruco::RefineParameters::readRefineParameters(FileNode fn)	 due to ARG type FileNode/I
  SKIP:bool cv::aruco::RefineParameters::writeRefineParameters(FileStorage fs, String name = String())	 due to ARG type FileStorage/I
  SKIP:vector_pair_int_and_double cv::face::StandardCollector::getResults(bool sorted = false)	 due to RET type vector_pair_int_and_double
  Objective-C: Processing OpenCV modules: 25
  Generated files: 674 (updated 674)
