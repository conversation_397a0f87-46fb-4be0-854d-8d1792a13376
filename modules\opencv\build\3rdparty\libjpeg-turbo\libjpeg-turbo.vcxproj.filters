﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcapistd.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jccolor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcdiffct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jclossls.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcmainct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcprepct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcsample.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdapistd.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdcolor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jddiffct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdlossls.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdmainct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdpostct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdsample.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jutils.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jccoefct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcdctmgr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdcoefct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jddctmgr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdmerge.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jfdctfst.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jfdctint.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jidctflt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jidctfst.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jidctint.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jidctred.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jquant1.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jquant2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcapimin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jchuff.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcicc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcinit.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jclhuff.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcmarker.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcmaster.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcomapi.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcparam.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcphuff.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jctrans.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdapimin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdatadst.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdatasrc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdhuff.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdicc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdinput.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdlhuff.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdmarker.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdmaster.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdphuff.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdtrans.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jerror.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jfdctflt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jmemmgr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jmemnobs.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jpeg_nbits.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jaricom.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcarith.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdarith.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jcapistd.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jccolor.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jcdiffct.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jclossls.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jcmainct.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jcprepct.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jcsample.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jdapistd.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jdcolor.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jddiffct.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jdlossls.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jdmainct.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jdpostct.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jdsample.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jutils.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jccoefct.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jcdctmgr.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jdcoefct.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jddctmgr.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jdmerge.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jfdctfst.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jfdctint.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jidctflt.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jidctfst.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jidctint.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jidctred.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jquant1.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.dir\$(Configuration)\jquant2.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg16-static.dir\$(Configuration)\jcapistd.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg16-static.dir\$(Configuration)\jccolor.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg16-static.dir\$(Configuration)\jcdiffct.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg16-static.dir\$(Configuration)\jclossls.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg16-static.dir\$(Configuration)\jcmainct.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg16-static.dir\$(Configuration)\jcprepct.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg16-static.dir\$(Configuration)\jcsample.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg16-static.dir\$(Configuration)\jdapistd.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg16-static.dir\$(Configuration)\jdcolor.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg16-static.dir\$(Configuration)\jddiffct.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg16-static.dir\$(Configuration)\jdlossls.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg16-static.dir\$(Configuration)\jdmainct.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg16-static.dir\$(Configuration)\jdpostct.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg16-static.dir\$(Configuration)\jdsample.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg16-static.dir\$(Configuration)\jutils.obj">
      <Filter>Object Libraries</Filter>
    </Object>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Object Libraries">
      <UniqueIdentifier>{EBD5911D-2884-32B2-ADB5-A849E0CBF498}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{2A318434-2169-3E81-A653-C1C0490B5893}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
