PORTED FUNCs LIST (58 of 59):

 Ptr_Facemark cv::face::createFacemarkAAM()
 Ptr_Facemark cv::face::createFacemarkLBF()
 Ptr_Facemark cv::face::createFacemarkKazemi()
 bool cv::face::getFacesHAAR(Mat image, Mat& faces, String face_cascade_name)
 bool cv::face::loadDatasetList(String imageList, String annotationList, vector_String images, vector_String annotations)
 bool cv::face::loadTrainingData(String filename, vector_String images, Mat& facePoints, char delim = ' ', float offset = 0.0f)
 bool cv::face::loadTrainingData(String imageList, String groundTruth, vector_String images, Mat& facePoints, float offset = 0.0f)
 bool cv::face::loadTrainingData(vector_String filename, vector_vector_Point2f trainlandmarks, vector_String trainimages)
 bool cv::face::loadFacePoints(String filename, Mat& points, float offset = 0.0f)
 void cv::face::drawFacemarks(Mat& image, Mat points, Scalar color = Scalar(255,0,0))
 int cv::face::BIF::getNumBands()
 int cv::face::BIF::getNumRotations()
 void cv::face::BIF::compute(Mat image, Mat& features)
static Ptr_BIF cv::face::BIF::create(int num_bands = 8, int num_rotations = 12)
 int cv::face::BasicFaceRecognizer::getNumComponents()
 void cv::face::BasicFaceRecognizer::setNumComponents(int val)
 double cv::face::BasicFaceRecognizer::getThreshold()
 void cv::face::BasicFaceRecognizer::setThreshold(double val)
 vector_Mat cv::face::BasicFaceRecognizer::getProjections()
 Mat cv::face::BasicFaceRecognizer::getLabels()
 Mat cv::face::BasicFaceRecognizer::getEigenValues()
 Mat cv::face::BasicFaceRecognizer::getEigenVectors()
 Mat cv::face::BasicFaceRecognizer::getMean()
static Ptr_EigenFaceRecognizer cv::face::EigenFaceRecognizer::create(int num_components = 0, double threshold = DBL_MAX)
 void cv::face::FaceRecognizer::train(vector_Mat src, Mat labels)
 void cv::face::FaceRecognizer::update(vector_Mat src, Mat labels)
 int cv::face::FaceRecognizer::predict(Mat src)
 void cv::face::FaceRecognizer::predict(Mat src, int& label, double& confidence)
 void cv::face::FaceRecognizer::predict(Mat src, Ptr_PredictCollector collector)
 void cv::face::FaceRecognizer::write(String filename)
 void cv::face::FaceRecognizer::read(String filename)
 void cv::face::FaceRecognizer::setLabelInfo(int label, String strInfo)
 String cv::face::FaceRecognizer::getLabelInfo(int label)
 vector_int cv::face::FaceRecognizer::getLabelsByString(String str)
 void cv::face::Facemark::loadModel(String model)
 bool cv::face::Facemark::fit(Mat image, Mat faces, vector_Mat& landmarks)
static Ptr_FisherFaceRecognizer cv::face::FisherFaceRecognizer::create(int num_components = 0, double threshold = DBL_MAX)
 int cv::face::LBPHFaceRecognizer::getGridX()
 void cv::face::LBPHFaceRecognizer::setGridX(int val)
 int cv::face::LBPHFaceRecognizer::getGridY()
 void cv::face::LBPHFaceRecognizer::setGridY(int val)
 int cv::face::LBPHFaceRecognizer::getRadius()
 void cv::face::LBPHFaceRecognizer::setRadius(int val)
 int cv::face::LBPHFaceRecognizer::getNeighbors()
 void cv::face::LBPHFaceRecognizer::setNeighbors(int val)
 double cv::face::LBPHFaceRecognizer::getThreshold()
 void cv::face::LBPHFaceRecognizer::setThreshold(double val)
 vector_Mat cv::face::LBPHFaceRecognizer::getHistograms()
 Mat cv::face::LBPHFaceRecognizer::getLabels()
static Ptr_LBPHFaceRecognizer cv::face::LBPHFaceRecognizer::create(int radius = 1, int neighbors = 8, int grid_x = 8, int grid_y = 8, double threshold = DBL_MAX)
 void cv::face::MACE::salt(String passphrase)
 void cv::face::MACE::train(vector_Mat images)
 bool cv::face::MACE::same(Mat query)
static Ptr_MACE cv::face::MACE::load(String filename, String objname = String())
static Ptr_MACE cv::face::MACE::create(int IMGSIZE = 64)
 int cv::face::StandardCollector::getMinLabel()
 double cv::face::StandardCollector::getMinDist()
static Ptr_StandardCollector cv::face::StandardCollector::create(double threshold = DBL_MAX)

SKIPPED FUNCs LIST (1 of 59):

 vector_pair_int_and_double cv::face::StandardCollector::getResults(bool sorted = false)
// Return type 'vector_pair_int_and_double' is not supported, skipping the function


0 def args - 47 funcs
1 def args - 7 funcs
2 def args - 4 funcs
5 def args - 1 funcs