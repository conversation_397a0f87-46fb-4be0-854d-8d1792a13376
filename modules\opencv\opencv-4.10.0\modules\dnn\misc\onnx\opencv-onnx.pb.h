// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: opencv-onnx.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_opencv_2donnx_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_opencv_2donnx_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_opencv_2donnx_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_opencv_2donnx_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[13]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_opencv_2donnx_2eproto;
namespace opencv_onnx {
class AttributeProto;
struct AttributeProtoDefaultTypeInternal;
extern AttributeProtoDefaultTypeInternal _AttributeProto_default_instance_;
class GraphProto;
struct GraphProtoDefaultTypeInternal;
extern GraphProtoDefaultTypeInternal _GraphProto_default_instance_;
class ModelProto;
struct ModelProtoDefaultTypeInternal;
extern ModelProtoDefaultTypeInternal _ModelProto_default_instance_;
class NodeProto;
struct NodeProtoDefaultTypeInternal;
extern NodeProtoDefaultTypeInternal _NodeProto_default_instance_;
class OperatorSetIdProto;
struct OperatorSetIdProtoDefaultTypeInternal;
extern OperatorSetIdProtoDefaultTypeInternal _OperatorSetIdProto_default_instance_;
class StringStringEntryProto;
struct StringStringEntryProtoDefaultTypeInternal;
extern StringStringEntryProtoDefaultTypeInternal _StringStringEntryProto_default_instance_;
class TensorProto;
struct TensorProtoDefaultTypeInternal;
extern TensorProtoDefaultTypeInternal _TensorProto_default_instance_;
class TensorProto_Segment;
struct TensorProto_SegmentDefaultTypeInternal;
extern TensorProto_SegmentDefaultTypeInternal _TensorProto_Segment_default_instance_;
class TensorShapeProto;
struct TensorShapeProtoDefaultTypeInternal;
extern TensorShapeProtoDefaultTypeInternal _TensorShapeProto_default_instance_;
class TensorShapeProto_Dimension;
struct TensorShapeProto_DimensionDefaultTypeInternal;
extern TensorShapeProto_DimensionDefaultTypeInternal _TensorShapeProto_Dimension_default_instance_;
class TypeProto;
struct TypeProtoDefaultTypeInternal;
extern TypeProtoDefaultTypeInternal _TypeProto_default_instance_;
class TypeProto_Tensor;
struct TypeProto_TensorDefaultTypeInternal;
extern TypeProto_TensorDefaultTypeInternal _TypeProto_Tensor_default_instance_;
class ValueInfoProto;
struct ValueInfoProtoDefaultTypeInternal;
extern ValueInfoProtoDefaultTypeInternal _ValueInfoProto_default_instance_;
}  // namespace opencv_onnx
PROTOBUF_NAMESPACE_OPEN
template<> ::opencv_onnx::AttributeProto* Arena::CreateMaybeMessage<::opencv_onnx::AttributeProto>(Arena*);
template<> ::opencv_onnx::GraphProto* Arena::CreateMaybeMessage<::opencv_onnx::GraphProto>(Arena*);
template<> ::opencv_onnx::ModelProto* Arena::CreateMaybeMessage<::opencv_onnx::ModelProto>(Arena*);
template<> ::opencv_onnx::NodeProto* Arena::CreateMaybeMessage<::opencv_onnx::NodeProto>(Arena*);
template<> ::opencv_onnx::OperatorSetIdProto* Arena::CreateMaybeMessage<::opencv_onnx::OperatorSetIdProto>(Arena*);
template<> ::opencv_onnx::StringStringEntryProto* Arena::CreateMaybeMessage<::opencv_onnx::StringStringEntryProto>(Arena*);
template<> ::opencv_onnx::TensorProto* Arena::CreateMaybeMessage<::opencv_onnx::TensorProto>(Arena*);
template<> ::opencv_onnx::TensorProto_Segment* Arena::CreateMaybeMessage<::opencv_onnx::TensorProto_Segment>(Arena*);
template<> ::opencv_onnx::TensorShapeProto* Arena::CreateMaybeMessage<::opencv_onnx::TensorShapeProto>(Arena*);
template<> ::opencv_onnx::TensorShapeProto_Dimension* Arena::CreateMaybeMessage<::opencv_onnx::TensorShapeProto_Dimension>(Arena*);
template<> ::opencv_onnx::TypeProto* Arena::CreateMaybeMessage<::opencv_onnx::TypeProto>(Arena*);
template<> ::opencv_onnx::TypeProto_Tensor* Arena::CreateMaybeMessage<::opencv_onnx::TypeProto_Tensor>(Arena*);
template<> ::opencv_onnx::ValueInfoProto* Arena::CreateMaybeMessage<::opencv_onnx::ValueInfoProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace opencv_onnx {

enum AttributeProto_AttributeType : int {
  AttributeProto_AttributeType_UNDEFINED = 0,
  AttributeProto_AttributeType_FLOAT = 1,
  AttributeProto_AttributeType_INT = 2,
  AttributeProto_AttributeType_STRING = 3,
  AttributeProto_AttributeType_TENSOR = 4,
  AttributeProto_AttributeType_GRAPH = 5,
  AttributeProto_AttributeType_FLOATS = 6,
  AttributeProto_AttributeType_INTS = 7,
  AttributeProto_AttributeType_STRINGS = 8,
  AttributeProto_AttributeType_TENSORS = 9,
  AttributeProto_AttributeType_GRAPHS = 10
};
bool AttributeProto_AttributeType_IsValid(int value);
constexpr AttributeProto_AttributeType AttributeProto_AttributeType_AttributeType_MIN = AttributeProto_AttributeType_UNDEFINED;
constexpr AttributeProto_AttributeType AttributeProto_AttributeType_AttributeType_MAX = AttributeProto_AttributeType_GRAPHS;
constexpr int AttributeProto_AttributeType_AttributeType_ARRAYSIZE = AttributeProto_AttributeType_AttributeType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AttributeProto_AttributeType_descriptor();
template<typename T>
inline const std::string& AttributeProto_AttributeType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, AttributeProto_AttributeType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function AttributeProto_AttributeType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    AttributeProto_AttributeType_descriptor(), enum_t_value);
}
inline bool AttributeProto_AttributeType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, AttributeProto_AttributeType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<AttributeProto_AttributeType>(
    AttributeProto_AttributeType_descriptor(), name, value);
}
enum TensorProto_DataType : int {
  TensorProto_DataType_UNDEFINED = 0,
  TensorProto_DataType_FLOAT = 1,
  TensorProto_DataType_UINT8 = 2,
  TensorProto_DataType_INT8 = 3,
  TensorProto_DataType_UINT16 = 4,
  TensorProto_DataType_INT16 = 5,
  TensorProto_DataType_INT32 = 6,
  TensorProto_DataType_INT64 = 7,
  TensorProto_DataType_STRING = 8,
  TensorProto_DataType_BOOL = 9,
  TensorProto_DataType_FLOAT16 = 10,
  TensorProto_DataType_DOUBLE = 11,
  TensorProto_DataType_UINT32 = 12,
  TensorProto_DataType_UINT64 = 13,
  TensorProto_DataType_COMPLEX64 = 14,
  TensorProto_DataType_COMPLEX128 = 15
};
bool TensorProto_DataType_IsValid(int value);
constexpr TensorProto_DataType TensorProto_DataType_DataType_MIN = TensorProto_DataType_UNDEFINED;
constexpr TensorProto_DataType TensorProto_DataType_DataType_MAX = TensorProto_DataType_COMPLEX128;
constexpr int TensorProto_DataType_DataType_ARRAYSIZE = TensorProto_DataType_DataType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TensorProto_DataType_descriptor();
template<typename T>
inline const std::string& TensorProto_DataType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TensorProto_DataType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TensorProto_DataType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TensorProto_DataType_descriptor(), enum_t_value);
}
inline bool TensorProto_DataType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, TensorProto_DataType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TensorProto_DataType>(
    TensorProto_DataType_descriptor(), name, value);
}
enum Version : int {
  _START_VERSION = 0,
  IR_VERSION_2017_10_10 = 1,
  IR_VERSION_2017_10_30 = 2,
  IR_VERSION = 3
};
bool Version_IsValid(int value);
constexpr Version Version_MIN = _START_VERSION;
constexpr Version Version_MAX = IR_VERSION;
constexpr int Version_ARRAYSIZE = Version_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Version_descriptor();
template<typename T>
inline const std::string& Version_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Version>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Version_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Version_descriptor(), enum_t_value);
}
inline bool Version_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Version* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Version>(
    Version_descriptor(), name, value);
}
// ===================================================================

class AttributeProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_onnx.AttributeProto) */ {
 public:
  inline AttributeProto() : AttributeProto(nullptr) {}
  ~AttributeProto() override;
  explicit constexpr AttributeProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AttributeProto(const AttributeProto& from);
  AttributeProto(AttributeProto&& from) noexcept
    : AttributeProto() {
    *this = ::std::move(from);
  }

  inline AttributeProto& operator=(const AttributeProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline AttributeProto& operator=(AttributeProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AttributeProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const AttributeProto* internal_default_instance() {
    return reinterpret_cast<const AttributeProto*>(
               &_AttributeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(AttributeProto& a, AttributeProto& b) {
    a.Swap(&b);
  }
  inline void Swap(AttributeProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AttributeProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AttributeProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AttributeProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AttributeProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AttributeProto& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AttributeProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_onnx.AttributeProto";
  }
  protected:
  explicit AttributeProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef AttributeProto_AttributeType AttributeType;
  static constexpr AttributeType UNDEFINED =
    AttributeProto_AttributeType_UNDEFINED;
  static constexpr AttributeType FLOAT =
    AttributeProto_AttributeType_FLOAT;
  static constexpr AttributeType INT =
    AttributeProto_AttributeType_INT;
  static constexpr AttributeType STRING =
    AttributeProto_AttributeType_STRING;
  static constexpr AttributeType TENSOR =
    AttributeProto_AttributeType_TENSOR;
  static constexpr AttributeType GRAPH =
    AttributeProto_AttributeType_GRAPH;
  static constexpr AttributeType FLOATS =
    AttributeProto_AttributeType_FLOATS;
  static constexpr AttributeType INTS =
    AttributeProto_AttributeType_INTS;
  static constexpr AttributeType STRINGS =
    AttributeProto_AttributeType_STRINGS;
  static constexpr AttributeType TENSORS =
    AttributeProto_AttributeType_TENSORS;
  static constexpr AttributeType GRAPHS =
    AttributeProto_AttributeType_GRAPHS;
  static inline bool AttributeType_IsValid(int value) {
    return AttributeProto_AttributeType_IsValid(value);
  }
  static constexpr AttributeType AttributeType_MIN =
    AttributeProto_AttributeType_AttributeType_MIN;
  static constexpr AttributeType AttributeType_MAX =
    AttributeProto_AttributeType_AttributeType_MAX;
  static constexpr int AttributeType_ARRAYSIZE =
    AttributeProto_AttributeType_AttributeType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  AttributeType_descriptor() {
    return AttributeProto_AttributeType_descriptor();
  }
  template<typename T>
  static inline const std::string& AttributeType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, AttributeType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function AttributeType_Name.");
    return AttributeProto_AttributeType_Name(enum_t_value);
  }
  static inline bool AttributeType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      AttributeType* value) {
    return AttributeProto_AttributeType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kFloatsFieldNumber = 7,
    kIntsFieldNumber = 8,
    kStringsFieldNumber = 9,
    kTensorsFieldNumber = 10,
    kGraphsFieldNumber = 11,
    kNameFieldNumber = 1,
    kSFieldNumber = 4,
    kDocStringFieldNumber = 13,
    kRefAttrNameFieldNumber = 21,
    kTFieldNumber = 5,
    kGFieldNumber = 6,
    kIFieldNumber = 3,
    kFFieldNumber = 2,
    kTypeFieldNumber = 20,
  };
  // repeated float floats = 7;
  int floats_size() const;
  private:
  int _internal_floats_size() const;
  public:
  void clear_floats();
  private:
  float _internal_floats(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_floats() const;
  void _internal_add_floats(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_floats();
  public:
  float floats(int index) const;
  void set_floats(int index, float value);
  void add_floats(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      floats() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_floats();

  // repeated int64 ints = 8;
  int ints_size() const;
  private:
  int _internal_ints_size() const;
  public:
  void clear_ints();
  private:
  int64_t _internal_ints(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_ints() const;
  void _internal_add_ints(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_ints();
  public:
  int64_t ints(int index) const;
  void set_ints(int index, int64_t value);
  void add_ints(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      ints() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_ints();

  // repeated bytes strings = 9;
  int strings_size() const;
  private:
  int _internal_strings_size() const;
  public:
  void clear_strings();
  const std::string& strings(int index) const;
  std::string* mutable_strings(int index);
  void set_strings(int index, const std::string& value);
  void set_strings(int index, std::string&& value);
  void set_strings(int index, const char* value);
  void set_strings(int index, const void* value, size_t size);
  std::string* add_strings();
  void add_strings(const std::string& value);
  void add_strings(std::string&& value);
  void add_strings(const char* value);
  void add_strings(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& strings() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_strings();
  private:
  const std::string& _internal_strings(int index) const;
  std::string* _internal_add_strings();
  public:

  // repeated .opencv_onnx.TensorProto tensors = 10;
  int tensors_size() const;
  private:
  int _internal_tensors_size() const;
  public:
  void clear_tensors();
  ::opencv_onnx::TensorProto* mutable_tensors(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::TensorProto >*
      mutable_tensors();
  private:
  const ::opencv_onnx::TensorProto& _internal_tensors(int index) const;
  ::opencv_onnx::TensorProto* _internal_add_tensors();
  public:
  const ::opencv_onnx::TensorProto& tensors(int index) const;
  ::opencv_onnx::TensorProto* add_tensors();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::TensorProto >&
      tensors() const;

  // repeated .opencv_onnx.GraphProto graphs = 11;
  int graphs_size() const;
  private:
  int _internal_graphs_size() const;
  public:
  void clear_graphs();
  ::opencv_onnx::GraphProto* mutable_graphs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::GraphProto >*
      mutable_graphs();
  private:
  const ::opencv_onnx::GraphProto& _internal_graphs(int index) const;
  ::opencv_onnx::GraphProto* _internal_add_graphs();
  public:
  const ::opencv_onnx::GraphProto& graphs(int index) const;
  ::opencv_onnx::GraphProto* add_graphs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::GraphProto >&
      graphs() const;

  // optional string name = 1;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional bytes s = 4;
  bool has_s() const;
  private:
  bool _internal_has_s() const;
  public:
  void clear_s();
  const std::string& s() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_s(ArgT0&& arg0, ArgT... args);
  std::string* mutable_s();
  PROTOBUF_NODISCARD std::string* release_s();
  void set_allocated_s(std::string* s);
  private:
  const std::string& _internal_s() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_s(const std::string& value);
  std::string* _internal_mutable_s();
  public:

  // optional string doc_string = 13;
  bool has_doc_string() const;
  private:
  bool _internal_has_doc_string() const;
  public:
  void clear_doc_string();
  const std::string& doc_string() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_doc_string(ArgT0&& arg0, ArgT... args);
  std::string* mutable_doc_string();
  PROTOBUF_NODISCARD std::string* release_doc_string();
  void set_allocated_doc_string(std::string* doc_string);
  private:
  const std::string& _internal_doc_string() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_doc_string(const std::string& value);
  std::string* _internal_mutable_doc_string();
  public:

  // optional string ref_attr_name = 21;
  bool has_ref_attr_name() const;
  private:
  bool _internal_has_ref_attr_name() const;
  public:
  void clear_ref_attr_name();
  const std::string& ref_attr_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_ref_attr_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_ref_attr_name();
  PROTOBUF_NODISCARD std::string* release_ref_attr_name();
  void set_allocated_ref_attr_name(std::string* ref_attr_name);
  private:
  const std::string& _internal_ref_attr_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_ref_attr_name(const std::string& value);
  std::string* _internal_mutable_ref_attr_name();
  public:

  // optional .opencv_onnx.TensorProto t = 5;
  bool has_t() const;
  private:
  bool _internal_has_t() const;
  public:
  void clear_t();
  const ::opencv_onnx::TensorProto& t() const;
  PROTOBUF_NODISCARD ::opencv_onnx::TensorProto* release_t();
  ::opencv_onnx::TensorProto* mutable_t();
  void set_allocated_t(::opencv_onnx::TensorProto* t);
  private:
  const ::opencv_onnx::TensorProto& _internal_t() const;
  ::opencv_onnx::TensorProto* _internal_mutable_t();
  public:
  void unsafe_arena_set_allocated_t(
      ::opencv_onnx::TensorProto* t);
  ::opencv_onnx::TensorProto* unsafe_arena_release_t();

  // optional .opencv_onnx.GraphProto g = 6;
  bool has_g() const;
  private:
  bool _internal_has_g() const;
  public:
  void clear_g();
  const ::opencv_onnx::GraphProto& g() const;
  PROTOBUF_NODISCARD ::opencv_onnx::GraphProto* release_g();
  ::opencv_onnx::GraphProto* mutable_g();
  void set_allocated_g(::opencv_onnx::GraphProto* g);
  private:
  const ::opencv_onnx::GraphProto& _internal_g() const;
  ::opencv_onnx::GraphProto* _internal_mutable_g();
  public:
  void unsafe_arena_set_allocated_g(
      ::opencv_onnx::GraphProto* g);
  ::opencv_onnx::GraphProto* unsafe_arena_release_g();

  // optional int64 i = 3;
  bool has_i() const;
  private:
  bool _internal_has_i() const;
  public:
  void clear_i();
  int64_t i() const;
  void set_i(int64_t value);
  private:
  int64_t _internal_i() const;
  void _internal_set_i(int64_t value);
  public:

  // optional float f = 2;
  bool has_f() const;
  private:
  bool _internal_has_f() const;
  public:
  void clear_f();
  float f() const;
  void set_f(float value);
  private:
  float _internal_f() const;
  void _internal_set_f(float value);
  public:

  // optional .opencv_onnx.AttributeProto.AttributeType type = 20;
  bool has_type() const;
  private:
  bool _internal_has_type() const;
  public:
  void clear_type();
  ::opencv_onnx::AttributeProto_AttributeType type() const;
  void set_type(::opencv_onnx::AttributeProto_AttributeType value);
  private:
  ::opencv_onnx::AttributeProto_AttributeType _internal_type() const;
  void _internal_set_type(::opencv_onnx::AttributeProto_AttributeType value);
  public:

  // @@protoc_insertion_point(class_scope:opencv_onnx.AttributeProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > floats_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > ints_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> strings_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::TensorProto > tensors_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::GraphProto > graphs_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr s_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr doc_string_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr ref_attr_name_;
  ::opencv_onnx::TensorProto* t_;
  ::opencv_onnx::GraphProto* g_;
  int64_t i_;
  float f_;
  int type_;
  friend struct ::TableStruct_opencv_2donnx_2eproto;
};
// -------------------------------------------------------------------

class ValueInfoProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_onnx.ValueInfoProto) */ {
 public:
  inline ValueInfoProto() : ValueInfoProto(nullptr) {}
  ~ValueInfoProto() override;
  explicit constexpr ValueInfoProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ValueInfoProto(const ValueInfoProto& from);
  ValueInfoProto(ValueInfoProto&& from) noexcept
    : ValueInfoProto() {
    *this = ::std::move(from);
  }

  inline ValueInfoProto& operator=(const ValueInfoProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ValueInfoProto& operator=(ValueInfoProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ValueInfoProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const ValueInfoProto* internal_default_instance() {
    return reinterpret_cast<const ValueInfoProto*>(
               &_ValueInfoProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ValueInfoProto& a, ValueInfoProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ValueInfoProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ValueInfoProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ValueInfoProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ValueInfoProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ValueInfoProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ValueInfoProto& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ValueInfoProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_onnx.ValueInfoProto";
  }
  protected:
  explicit ValueInfoProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kDocStringFieldNumber = 3,
    kTypeFieldNumber = 2,
  };
  // optional string name = 1;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional string doc_string = 3;
  bool has_doc_string() const;
  private:
  bool _internal_has_doc_string() const;
  public:
  void clear_doc_string();
  const std::string& doc_string() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_doc_string(ArgT0&& arg0, ArgT... args);
  std::string* mutable_doc_string();
  PROTOBUF_NODISCARD std::string* release_doc_string();
  void set_allocated_doc_string(std::string* doc_string);
  private:
  const std::string& _internal_doc_string() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_doc_string(const std::string& value);
  std::string* _internal_mutable_doc_string();
  public:

  // optional .opencv_onnx.TypeProto type = 2;
  bool has_type() const;
  private:
  bool _internal_has_type() const;
  public:
  void clear_type();
  const ::opencv_onnx::TypeProto& type() const;
  PROTOBUF_NODISCARD ::opencv_onnx::TypeProto* release_type();
  ::opencv_onnx::TypeProto* mutable_type();
  void set_allocated_type(::opencv_onnx::TypeProto* type);
  private:
  const ::opencv_onnx::TypeProto& _internal_type() const;
  ::opencv_onnx::TypeProto* _internal_mutable_type();
  public:
  void unsafe_arena_set_allocated_type(
      ::opencv_onnx::TypeProto* type);
  ::opencv_onnx::TypeProto* unsafe_arena_release_type();

  // @@protoc_insertion_point(class_scope:opencv_onnx.ValueInfoProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr doc_string_;
  ::opencv_onnx::TypeProto* type_;
  friend struct ::TableStruct_opencv_2donnx_2eproto;
};
// -------------------------------------------------------------------

class NodeProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_onnx.NodeProto) */ {
 public:
  inline NodeProto() : NodeProto(nullptr) {}
  ~NodeProto() override;
  explicit constexpr NodeProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NodeProto(const NodeProto& from);
  NodeProto(NodeProto&& from) noexcept
    : NodeProto() {
    *this = ::std::move(from);
  }

  inline NodeProto& operator=(const NodeProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeProto& operator=(NodeProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NodeProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const NodeProto* internal_default_instance() {
    return reinterpret_cast<const NodeProto*>(
               &_NodeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(NodeProto& a, NodeProto& b) {
    a.Swap(&b);
  }
  inline void Swap(NodeProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NodeProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NodeProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NodeProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const NodeProto& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NodeProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_onnx.NodeProto";
  }
  protected:
  explicit NodeProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputFieldNumber = 1,
    kOutputFieldNumber = 2,
    kAttributeFieldNumber = 5,
    kNameFieldNumber = 3,
    kOpTypeFieldNumber = 4,
    kDocStringFieldNumber = 6,
    kDomainFieldNumber = 7,
  };
  // repeated string input = 1;
  int input_size() const;
  private:
  int _internal_input_size() const;
  public:
  void clear_input();
  const std::string& input(int index) const;
  std::string* mutable_input(int index);
  void set_input(int index, const std::string& value);
  void set_input(int index, std::string&& value);
  void set_input(int index, const char* value);
  void set_input(int index, const char* value, size_t size);
  std::string* add_input();
  void add_input(const std::string& value);
  void add_input(std::string&& value);
  void add_input(const char* value);
  void add_input(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& input() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_input();
  private:
  const std::string& _internal_input(int index) const;
  std::string* _internal_add_input();
  public:

  // repeated string output = 2;
  int output_size() const;
  private:
  int _internal_output_size() const;
  public:
  void clear_output();
  const std::string& output(int index) const;
  std::string* mutable_output(int index);
  void set_output(int index, const std::string& value);
  void set_output(int index, std::string&& value);
  void set_output(int index, const char* value);
  void set_output(int index, const char* value, size_t size);
  std::string* add_output();
  void add_output(const std::string& value);
  void add_output(std::string&& value);
  void add_output(const char* value);
  void add_output(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& output() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_output();
  private:
  const std::string& _internal_output(int index) const;
  std::string* _internal_add_output();
  public:

  // repeated .opencv_onnx.AttributeProto attribute = 5;
  int attribute_size() const;
  private:
  int _internal_attribute_size() const;
  public:
  void clear_attribute();
  ::opencv_onnx::AttributeProto* mutable_attribute(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::AttributeProto >*
      mutable_attribute();
  private:
  const ::opencv_onnx::AttributeProto& _internal_attribute(int index) const;
  ::opencv_onnx::AttributeProto* _internal_add_attribute();
  public:
  const ::opencv_onnx::AttributeProto& attribute(int index) const;
  ::opencv_onnx::AttributeProto* add_attribute();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::AttributeProto >&
      attribute() const;

  // optional string name = 3;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional string op_type = 4;
  bool has_op_type() const;
  private:
  bool _internal_has_op_type() const;
  public:
  void clear_op_type();
  const std::string& op_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_op_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_op_type();
  PROTOBUF_NODISCARD std::string* release_op_type();
  void set_allocated_op_type(std::string* op_type);
  private:
  const std::string& _internal_op_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_op_type(const std::string& value);
  std::string* _internal_mutable_op_type();
  public:

  // optional string doc_string = 6;
  bool has_doc_string() const;
  private:
  bool _internal_has_doc_string() const;
  public:
  void clear_doc_string();
  const std::string& doc_string() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_doc_string(ArgT0&& arg0, ArgT... args);
  std::string* mutable_doc_string();
  PROTOBUF_NODISCARD std::string* release_doc_string();
  void set_allocated_doc_string(std::string* doc_string);
  private:
  const std::string& _internal_doc_string() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_doc_string(const std::string& value);
  std::string* _internal_mutable_doc_string();
  public:

  // optional string domain = 7;
  bool has_domain() const;
  private:
  bool _internal_has_domain() const;
  public:
  void clear_domain();
  const std::string& domain() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_domain(ArgT0&& arg0, ArgT... args);
  std::string* mutable_domain();
  PROTOBUF_NODISCARD std::string* release_domain();
  void set_allocated_domain(std::string* domain);
  private:
  const std::string& _internal_domain() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_domain(const std::string& value);
  std::string* _internal_mutable_domain();
  public:

  // @@protoc_insertion_point(class_scope:opencv_onnx.NodeProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> input_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> output_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::AttributeProto > attribute_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr doc_string_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr domain_;
  friend struct ::TableStruct_opencv_2donnx_2eproto;
};
// -------------------------------------------------------------------

class ModelProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_onnx.ModelProto) */ {
 public:
  inline ModelProto() : ModelProto(nullptr) {}
  ~ModelProto() override;
  explicit constexpr ModelProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelProto(const ModelProto& from);
  ModelProto(ModelProto&& from) noexcept
    : ModelProto() {
    *this = ::std::move(from);
  }

  inline ModelProto& operator=(const ModelProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelProto& operator=(ModelProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelProto* internal_default_instance() {
    return reinterpret_cast<const ModelProto*>(
               &_ModelProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ModelProto& a, ModelProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ModelProto& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_onnx.ModelProto";
  }
  protected:
  explicit ModelProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpsetImportFieldNumber = 8,
    kMetadataPropsFieldNumber = 14,
    kProducerNameFieldNumber = 2,
    kProducerVersionFieldNumber = 3,
    kDomainFieldNumber = 4,
    kDocStringFieldNumber = 6,
    kGraphFieldNumber = 7,
    kIrVersionFieldNumber = 1,
    kModelVersionFieldNumber = 5,
  };
  // repeated .opencv_onnx.OperatorSetIdProto opset_import = 8;
  int opset_import_size() const;
  private:
  int _internal_opset_import_size() const;
  public:
  void clear_opset_import();
  ::opencv_onnx::OperatorSetIdProto* mutable_opset_import(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::OperatorSetIdProto >*
      mutable_opset_import();
  private:
  const ::opencv_onnx::OperatorSetIdProto& _internal_opset_import(int index) const;
  ::opencv_onnx::OperatorSetIdProto* _internal_add_opset_import();
  public:
  const ::opencv_onnx::OperatorSetIdProto& opset_import(int index) const;
  ::opencv_onnx::OperatorSetIdProto* add_opset_import();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::OperatorSetIdProto >&
      opset_import() const;

  // repeated .opencv_onnx.StringStringEntryProto metadata_props = 14;
  int metadata_props_size() const;
  private:
  int _internal_metadata_props_size() const;
  public:
  void clear_metadata_props();
  ::opencv_onnx::StringStringEntryProto* mutable_metadata_props(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::StringStringEntryProto >*
      mutable_metadata_props();
  private:
  const ::opencv_onnx::StringStringEntryProto& _internal_metadata_props(int index) const;
  ::opencv_onnx::StringStringEntryProto* _internal_add_metadata_props();
  public:
  const ::opencv_onnx::StringStringEntryProto& metadata_props(int index) const;
  ::opencv_onnx::StringStringEntryProto* add_metadata_props();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::StringStringEntryProto >&
      metadata_props() const;

  // optional string producer_name = 2;
  bool has_producer_name() const;
  private:
  bool _internal_has_producer_name() const;
  public:
  void clear_producer_name();
  const std::string& producer_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_producer_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_producer_name();
  PROTOBUF_NODISCARD std::string* release_producer_name();
  void set_allocated_producer_name(std::string* producer_name);
  private:
  const std::string& _internal_producer_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_producer_name(const std::string& value);
  std::string* _internal_mutable_producer_name();
  public:

  // optional string producer_version = 3;
  bool has_producer_version() const;
  private:
  bool _internal_has_producer_version() const;
  public:
  void clear_producer_version();
  const std::string& producer_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_producer_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_producer_version();
  PROTOBUF_NODISCARD std::string* release_producer_version();
  void set_allocated_producer_version(std::string* producer_version);
  private:
  const std::string& _internal_producer_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_producer_version(const std::string& value);
  std::string* _internal_mutable_producer_version();
  public:

  // optional string domain = 4;
  bool has_domain() const;
  private:
  bool _internal_has_domain() const;
  public:
  void clear_domain();
  const std::string& domain() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_domain(ArgT0&& arg0, ArgT... args);
  std::string* mutable_domain();
  PROTOBUF_NODISCARD std::string* release_domain();
  void set_allocated_domain(std::string* domain);
  private:
  const std::string& _internal_domain() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_domain(const std::string& value);
  std::string* _internal_mutable_domain();
  public:

  // optional string doc_string = 6;
  bool has_doc_string() const;
  private:
  bool _internal_has_doc_string() const;
  public:
  void clear_doc_string();
  const std::string& doc_string() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_doc_string(ArgT0&& arg0, ArgT... args);
  std::string* mutable_doc_string();
  PROTOBUF_NODISCARD std::string* release_doc_string();
  void set_allocated_doc_string(std::string* doc_string);
  private:
  const std::string& _internal_doc_string() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_doc_string(const std::string& value);
  std::string* _internal_mutable_doc_string();
  public:

  // optional .opencv_onnx.GraphProto graph = 7;
  bool has_graph() const;
  private:
  bool _internal_has_graph() const;
  public:
  void clear_graph();
  const ::opencv_onnx::GraphProto& graph() const;
  PROTOBUF_NODISCARD ::opencv_onnx::GraphProto* release_graph();
  ::opencv_onnx::GraphProto* mutable_graph();
  void set_allocated_graph(::opencv_onnx::GraphProto* graph);
  private:
  const ::opencv_onnx::GraphProto& _internal_graph() const;
  ::opencv_onnx::GraphProto* _internal_mutable_graph();
  public:
  void unsafe_arena_set_allocated_graph(
      ::opencv_onnx::GraphProto* graph);
  ::opencv_onnx::GraphProto* unsafe_arena_release_graph();

  // optional int64 ir_version = 1;
  bool has_ir_version() const;
  private:
  bool _internal_has_ir_version() const;
  public:
  void clear_ir_version();
  int64_t ir_version() const;
  void set_ir_version(int64_t value);
  private:
  int64_t _internal_ir_version() const;
  void _internal_set_ir_version(int64_t value);
  public:

  // optional int64 model_version = 5;
  bool has_model_version() const;
  private:
  bool _internal_has_model_version() const;
  public:
  void clear_model_version();
  int64_t model_version() const;
  void set_model_version(int64_t value);
  private:
  int64_t _internal_model_version() const;
  void _internal_set_model_version(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:opencv_onnx.ModelProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::OperatorSetIdProto > opset_import_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::StringStringEntryProto > metadata_props_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr producer_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr producer_version_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr domain_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr doc_string_;
  ::opencv_onnx::GraphProto* graph_;
  int64_t ir_version_;
  int64_t model_version_;
  friend struct ::TableStruct_opencv_2donnx_2eproto;
};
// -------------------------------------------------------------------

class StringStringEntryProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_onnx.StringStringEntryProto) */ {
 public:
  inline StringStringEntryProto() : StringStringEntryProto(nullptr) {}
  ~StringStringEntryProto() override;
  explicit constexpr StringStringEntryProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StringStringEntryProto(const StringStringEntryProto& from);
  StringStringEntryProto(StringStringEntryProto&& from) noexcept
    : StringStringEntryProto() {
    *this = ::std::move(from);
  }

  inline StringStringEntryProto& operator=(const StringStringEntryProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline StringStringEntryProto& operator=(StringStringEntryProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StringStringEntryProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const StringStringEntryProto* internal_default_instance() {
    return reinterpret_cast<const StringStringEntryProto*>(
               &_StringStringEntryProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(StringStringEntryProto& a, StringStringEntryProto& b) {
    a.Swap(&b);
  }
  inline void Swap(StringStringEntryProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StringStringEntryProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StringStringEntryProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StringStringEntryProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StringStringEntryProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StringStringEntryProto& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StringStringEntryProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_onnx.StringStringEntryProto";
  }
  protected:
  explicit StringStringEntryProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
    kValueFieldNumber = 2,
  };
  // optional string key = 1;
  bool has_key() const;
  private:
  bool _internal_has_key() const;
  public:
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // optional string value = 2;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  const std::string& value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_value();
  PROTOBUF_NODISCARD std::string* release_value();
  void set_allocated_value(std::string* value);
  private:
  const std::string& _internal_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_value(const std::string& value);
  std::string* _internal_mutable_value();
  public:

  // @@protoc_insertion_point(class_scope:opencv_onnx.StringStringEntryProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr value_;
  friend struct ::TableStruct_opencv_2donnx_2eproto;
};
// -------------------------------------------------------------------

class GraphProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_onnx.GraphProto) */ {
 public:
  inline GraphProto() : GraphProto(nullptr) {}
  ~GraphProto() override;
  explicit constexpr GraphProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GraphProto(const GraphProto& from);
  GraphProto(GraphProto&& from) noexcept
    : GraphProto() {
    *this = ::std::move(from);
  }

  inline GraphProto& operator=(const GraphProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphProto& operator=(GraphProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GraphProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const GraphProto* internal_default_instance() {
    return reinterpret_cast<const GraphProto*>(
               &_GraphProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(GraphProto& a, GraphProto& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GraphProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GraphProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GraphProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GraphProto& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_onnx.GraphProto";
  }
  protected:
  explicit GraphProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeFieldNumber = 1,
    kInitializerFieldNumber = 5,
    kInputFieldNumber = 11,
    kOutputFieldNumber = 12,
    kValueInfoFieldNumber = 13,
    kNameFieldNumber = 2,
    kDocStringFieldNumber = 10,
  };
  // repeated .opencv_onnx.NodeProto node = 1;
  int node_size() const;
  private:
  int _internal_node_size() const;
  public:
  void clear_node();
  ::opencv_onnx::NodeProto* mutable_node(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::NodeProto >*
      mutable_node();
  private:
  const ::opencv_onnx::NodeProto& _internal_node(int index) const;
  ::opencv_onnx::NodeProto* _internal_add_node();
  public:
  const ::opencv_onnx::NodeProto& node(int index) const;
  ::opencv_onnx::NodeProto* add_node();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::NodeProto >&
      node() const;

  // repeated .opencv_onnx.TensorProto initializer = 5;
  int initializer_size() const;
  private:
  int _internal_initializer_size() const;
  public:
  void clear_initializer();
  ::opencv_onnx::TensorProto* mutable_initializer(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::TensorProto >*
      mutable_initializer();
  private:
  const ::opencv_onnx::TensorProto& _internal_initializer(int index) const;
  ::opencv_onnx::TensorProto* _internal_add_initializer();
  public:
  const ::opencv_onnx::TensorProto& initializer(int index) const;
  ::opencv_onnx::TensorProto* add_initializer();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::TensorProto >&
      initializer() const;

  // repeated .opencv_onnx.ValueInfoProto input = 11;
  int input_size() const;
  private:
  int _internal_input_size() const;
  public:
  void clear_input();
  ::opencv_onnx::ValueInfoProto* mutable_input(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::ValueInfoProto >*
      mutable_input();
  private:
  const ::opencv_onnx::ValueInfoProto& _internal_input(int index) const;
  ::opencv_onnx::ValueInfoProto* _internal_add_input();
  public:
  const ::opencv_onnx::ValueInfoProto& input(int index) const;
  ::opencv_onnx::ValueInfoProto* add_input();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::ValueInfoProto >&
      input() const;

  // repeated .opencv_onnx.ValueInfoProto output = 12;
  int output_size() const;
  private:
  int _internal_output_size() const;
  public:
  void clear_output();
  ::opencv_onnx::ValueInfoProto* mutable_output(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::ValueInfoProto >*
      mutable_output();
  private:
  const ::opencv_onnx::ValueInfoProto& _internal_output(int index) const;
  ::opencv_onnx::ValueInfoProto* _internal_add_output();
  public:
  const ::opencv_onnx::ValueInfoProto& output(int index) const;
  ::opencv_onnx::ValueInfoProto* add_output();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::ValueInfoProto >&
      output() const;

  // repeated .opencv_onnx.ValueInfoProto value_info = 13;
  int value_info_size() const;
  private:
  int _internal_value_info_size() const;
  public:
  void clear_value_info();
  ::opencv_onnx::ValueInfoProto* mutable_value_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::ValueInfoProto >*
      mutable_value_info();
  private:
  const ::opencv_onnx::ValueInfoProto& _internal_value_info(int index) const;
  ::opencv_onnx::ValueInfoProto* _internal_add_value_info();
  public:
  const ::opencv_onnx::ValueInfoProto& value_info(int index) const;
  ::opencv_onnx::ValueInfoProto* add_value_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::ValueInfoProto >&
      value_info() const;

  // optional string name = 2;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional string doc_string = 10;
  bool has_doc_string() const;
  private:
  bool _internal_has_doc_string() const;
  public:
  void clear_doc_string();
  const std::string& doc_string() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_doc_string(ArgT0&& arg0, ArgT... args);
  std::string* mutable_doc_string();
  PROTOBUF_NODISCARD std::string* release_doc_string();
  void set_allocated_doc_string(std::string* doc_string);
  private:
  const std::string& _internal_doc_string() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_doc_string(const std::string& value);
  std::string* _internal_mutable_doc_string();
  public:

  // @@protoc_insertion_point(class_scope:opencv_onnx.GraphProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::NodeProto > node_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::TensorProto > initializer_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::ValueInfoProto > input_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::ValueInfoProto > output_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::ValueInfoProto > value_info_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr doc_string_;
  friend struct ::TableStruct_opencv_2donnx_2eproto;
};
// -------------------------------------------------------------------

class TensorProto_Segment final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_onnx.TensorProto.Segment) */ {
 public:
  inline TensorProto_Segment() : TensorProto_Segment(nullptr) {}
  ~TensorProto_Segment() override;
  explicit constexpr TensorProto_Segment(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TensorProto_Segment(const TensorProto_Segment& from);
  TensorProto_Segment(TensorProto_Segment&& from) noexcept
    : TensorProto_Segment() {
    *this = ::std::move(from);
  }

  inline TensorProto_Segment& operator=(const TensorProto_Segment& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorProto_Segment& operator=(TensorProto_Segment&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TensorProto_Segment& default_instance() {
    return *internal_default_instance();
  }
  static inline const TensorProto_Segment* internal_default_instance() {
    return reinterpret_cast<const TensorProto_Segment*>(
               &_TensorProto_Segment_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(TensorProto_Segment& a, TensorProto_Segment& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorProto_Segment* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorProto_Segment* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TensorProto_Segment* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TensorProto_Segment>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TensorProto_Segment& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TensorProto_Segment& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorProto_Segment* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_onnx.TensorProto.Segment";
  }
  protected:
  explicit TensorProto_Segment(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBeginFieldNumber = 1,
    kEndFieldNumber = 2,
  };
  // optional int64 begin = 1;
  bool has_begin() const;
  private:
  bool _internal_has_begin() const;
  public:
  void clear_begin();
  int64_t begin() const;
  void set_begin(int64_t value);
  private:
  int64_t _internal_begin() const;
  void _internal_set_begin(int64_t value);
  public:

  // optional int64 end = 2;
  bool has_end() const;
  private:
  bool _internal_has_end() const;
  public:
  void clear_end();
  int64_t end() const;
  void set_end(int64_t value);
  private:
  int64_t _internal_end() const;
  void _internal_set_end(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:opencv_onnx.TensorProto.Segment)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int64_t begin_;
  int64_t end_;
  friend struct ::TableStruct_opencv_2donnx_2eproto;
};
// -------------------------------------------------------------------

class TensorProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_onnx.TensorProto) */ {
 public:
  inline TensorProto() : TensorProto(nullptr) {}
  ~TensorProto() override;
  explicit constexpr TensorProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TensorProto(const TensorProto& from);
  TensorProto(TensorProto&& from) noexcept
    : TensorProto() {
    *this = ::std::move(from);
  }

  inline TensorProto& operator=(const TensorProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorProto& operator=(TensorProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TensorProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const TensorProto* internal_default_instance() {
    return reinterpret_cast<const TensorProto*>(
               &_TensorProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(TensorProto& a, TensorProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TensorProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TensorProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TensorProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TensorProto& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_onnx.TensorProto";
  }
  protected:
  explicit TensorProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TensorProto_Segment Segment;

  typedef TensorProto_DataType DataType;
  static constexpr DataType UNDEFINED =
    TensorProto_DataType_UNDEFINED;
  static constexpr DataType FLOAT =
    TensorProto_DataType_FLOAT;
  static constexpr DataType UINT8 =
    TensorProto_DataType_UINT8;
  static constexpr DataType INT8 =
    TensorProto_DataType_INT8;
  static constexpr DataType UINT16 =
    TensorProto_DataType_UINT16;
  static constexpr DataType INT16 =
    TensorProto_DataType_INT16;
  static constexpr DataType INT32 =
    TensorProto_DataType_INT32;
  static constexpr DataType INT64 =
    TensorProto_DataType_INT64;
  static constexpr DataType STRING =
    TensorProto_DataType_STRING;
  static constexpr DataType BOOL =
    TensorProto_DataType_BOOL;
  static constexpr DataType FLOAT16 =
    TensorProto_DataType_FLOAT16;
  static constexpr DataType DOUBLE =
    TensorProto_DataType_DOUBLE;
  static constexpr DataType UINT32 =
    TensorProto_DataType_UINT32;
  static constexpr DataType UINT64 =
    TensorProto_DataType_UINT64;
  static constexpr DataType COMPLEX64 =
    TensorProto_DataType_COMPLEX64;
  static constexpr DataType COMPLEX128 =
    TensorProto_DataType_COMPLEX128;
  static inline bool DataType_IsValid(int value) {
    return TensorProto_DataType_IsValid(value);
  }
  static constexpr DataType DataType_MIN =
    TensorProto_DataType_DataType_MIN;
  static constexpr DataType DataType_MAX =
    TensorProto_DataType_DataType_MAX;
  static constexpr int DataType_ARRAYSIZE =
    TensorProto_DataType_DataType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  DataType_descriptor() {
    return TensorProto_DataType_descriptor();
  }
  template<typename T>
  static inline const std::string& DataType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, DataType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function DataType_Name.");
    return TensorProto_DataType_Name(enum_t_value);
  }
  static inline bool DataType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      DataType* value) {
    return TensorProto_DataType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kDimsFieldNumber = 1,
    kFloatDataFieldNumber = 4,
    kInt32DataFieldNumber = 5,
    kStringDataFieldNumber = 6,
    kInt64DataFieldNumber = 7,
    kDoubleDataFieldNumber = 10,
    kUint64DataFieldNumber = 11,
    kNameFieldNumber = 8,
    kRawDataFieldNumber = 9,
    kDocStringFieldNumber = 12,
    kSegmentFieldNumber = 3,
    kDataTypeFieldNumber = 2,
  };
  // repeated int64 dims = 1;
  int dims_size() const;
  private:
  int _internal_dims_size() const;
  public:
  void clear_dims();
  private:
  int64_t _internal_dims(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_dims() const;
  void _internal_add_dims(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_dims();
  public:
  int64_t dims(int index) const;
  void set_dims(int index, int64_t value);
  void add_dims(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      dims() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_dims();

  // repeated float float_data = 4 [packed = true];
  int float_data_size() const;
  private:
  int _internal_float_data_size() const;
  public:
  void clear_float_data();
  private:
  float _internal_float_data(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_float_data() const;
  void _internal_add_float_data(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_float_data();
  public:
  float float_data(int index) const;
  void set_float_data(int index, float value);
  void add_float_data(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      float_data() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_float_data();

  // repeated int32 int32_data = 5 [packed = true];
  int int32_data_size() const;
  private:
  int _internal_int32_data_size() const;
  public:
  void clear_int32_data();
  private:
  int32_t _internal_int32_data(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_int32_data() const;
  void _internal_add_int32_data(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_int32_data();
  public:
  int32_t int32_data(int index) const;
  void set_int32_data(int index, int32_t value);
  void add_int32_data(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      int32_data() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_int32_data();

  // repeated bytes string_data = 6;
  int string_data_size() const;
  private:
  int _internal_string_data_size() const;
  public:
  void clear_string_data();
  const std::string& string_data(int index) const;
  std::string* mutable_string_data(int index);
  void set_string_data(int index, const std::string& value);
  void set_string_data(int index, std::string&& value);
  void set_string_data(int index, const char* value);
  void set_string_data(int index, const void* value, size_t size);
  std::string* add_string_data();
  void add_string_data(const std::string& value);
  void add_string_data(std::string&& value);
  void add_string_data(const char* value);
  void add_string_data(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& string_data() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_string_data();
  private:
  const std::string& _internal_string_data(int index) const;
  std::string* _internal_add_string_data();
  public:

  // repeated int64 int64_data = 7 [packed = true];
  int int64_data_size() const;
  private:
  int _internal_int64_data_size() const;
  public:
  void clear_int64_data();
  private:
  int64_t _internal_int64_data(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_int64_data() const;
  void _internal_add_int64_data(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_int64_data();
  public:
  int64_t int64_data(int index) const;
  void set_int64_data(int index, int64_t value);
  void add_int64_data(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      int64_data() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_int64_data();

  // repeated double double_data = 10 [packed = true];
  int double_data_size() const;
  private:
  int _internal_double_data_size() const;
  public:
  void clear_double_data();
  private:
  double _internal_double_data(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      _internal_double_data() const;
  void _internal_add_double_data(double value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      _internal_mutable_double_data();
  public:
  double double_data(int index) const;
  void set_double_data(int index, double value);
  void add_double_data(double value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      double_data() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      mutable_double_data();

  // repeated uint64 uint64_data = 11 [packed = true];
  int uint64_data_size() const;
  private:
  int _internal_uint64_data_size() const;
  public:
  void clear_uint64_data();
  private:
  uint64_t _internal_uint64_data(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_uint64_data() const;
  void _internal_add_uint64_data(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_uint64_data();
  public:
  uint64_t uint64_data(int index) const;
  void set_uint64_data(int index, uint64_t value);
  void add_uint64_data(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      uint64_data() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_uint64_data();

  // optional string name = 8;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional bytes raw_data = 9;
  bool has_raw_data() const;
  private:
  bool _internal_has_raw_data() const;
  public:
  void clear_raw_data();
  const std::string& raw_data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_raw_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_raw_data();
  PROTOBUF_NODISCARD std::string* release_raw_data();
  void set_allocated_raw_data(std::string* raw_data);
  private:
  const std::string& _internal_raw_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_raw_data(const std::string& value);
  std::string* _internal_mutable_raw_data();
  public:

  // optional string doc_string = 12;
  bool has_doc_string() const;
  private:
  bool _internal_has_doc_string() const;
  public:
  void clear_doc_string();
  const std::string& doc_string() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_doc_string(ArgT0&& arg0, ArgT... args);
  std::string* mutable_doc_string();
  PROTOBUF_NODISCARD std::string* release_doc_string();
  void set_allocated_doc_string(std::string* doc_string);
  private:
  const std::string& _internal_doc_string() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_doc_string(const std::string& value);
  std::string* _internal_mutable_doc_string();
  public:

  // optional .opencv_onnx.TensorProto.Segment segment = 3;
  bool has_segment() const;
  private:
  bool _internal_has_segment() const;
  public:
  void clear_segment();
  const ::opencv_onnx::TensorProto_Segment& segment() const;
  PROTOBUF_NODISCARD ::opencv_onnx::TensorProto_Segment* release_segment();
  ::opencv_onnx::TensorProto_Segment* mutable_segment();
  void set_allocated_segment(::opencv_onnx::TensorProto_Segment* segment);
  private:
  const ::opencv_onnx::TensorProto_Segment& _internal_segment() const;
  ::opencv_onnx::TensorProto_Segment* _internal_mutable_segment();
  public:
  void unsafe_arena_set_allocated_segment(
      ::opencv_onnx::TensorProto_Segment* segment);
  ::opencv_onnx::TensorProto_Segment* unsafe_arena_release_segment();

  // optional .opencv_onnx.TensorProto.DataType data_type = 2;
  bool has_data_type() const;
  private:
  bool _internal_has_data_type() const;
  public:
  void clear_data_type();
  ::opencv_onnx::TensorProto_DataType data_type() const;
  void set_data_type(::opencv_onnx::TensorProto_DataType value);
  private:
  ::opencv_onnx::TensorProto_DataType _internal_data_type() const;
  void _internal_set_data_type(::opencv_onnx::TensorProto_DataType value);
  public:

  // @@protoc_insertion_point(class_scope:opencv_onnx.TensorProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > dims_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > float_data_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > int32_data_;
  mutable std::atomic<int> _int32_data_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> string_data_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > int64_data_;
  mutable std::atomic<int> _int64_data_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double > double_data_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > uint64_data_;
  mutable std::atomic<int> _uint64_data_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr raw_data_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr doc_string_;
  ::opencv_onnx::TensorProto_Segment* segment_;
  int data_type_;
  friend struct ::TableStruct_opencv_2donnx_2eproto;
};
// -------------------------------------------------------------------

class TensorShapeProto_Dimension final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_onnx.TensorShapeProto.Dimension) */ {
 public:
  inline TensorShapeProto_Dimension() : TensorShapeProto_Dimension(nullptr) {}
  ~TensorShapeProto_Dimension() override;
  explicit constexpr TensorShapeProto_Dimension(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TensorShapeProto_Dimension(const TensorShapeProto_Dimension& from);
  TensorShapeProto_Dimension(TensorShapeProto_Dimension&& from) noexcept
    : TensorShapeProto_Dimension() {
    *this = ::std::move(from);
  }

  inline TensorShapeProto_Dimension& operator=(const TensorShapeProto_Dimension& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorShapeProto_Dimension& operator=(TensorShapeProto_Dimension&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TensorShapeProto_Dimension& default_instance() {
    return *internal_default_instance();
  }
  enum ValueCase {
    kDimValue = 1,
    kDimParam = 2,
    VALUE_NOT_SET = 0,
  };

  static inline const TensorShapeProto_Dimension* internal_default_instance() {
    return reinterpret_cast<const TensorShapeProto_Dimension*>(
               &_TensorShapeProto_Dimension_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(TensorShapeProto_Dimension& a, TensorShapeProto_Dimension& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorShapeProto_Dimension* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorShapeProto_Dimension* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TensorShapeProto_Dimension* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TensorShapeProto_Dimension>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TensorShapeProto_Dimension& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TensorShapeProto_Dimension& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorShapeProto_Dimension* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_onnx.TensorShapeProto.Dimension";
  }
  protected:
  explicit TensorShapeProto_Dimension(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDenotationFieldNumber = 3,
    kDimValueFieldNumber = 1,
    kDimParamFieldNumber = 2,
  };
  // optional string denotation = 3;
  bool has_denotation() const;
  private:
  bool _internal_has_denotation() const;
  public:
  void clear_denotation();
  const std::string& denotation() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_denotation(ArgT0&& arg0, ArgT... args);
  std::string* mutable_denotation();
  PROTOBUF_NODISCARD std::string* release_denotation();
  void set_allocated_denotation(std::string* denotation);
  private:
  const std::string& _internal_denotation() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_denotation(const std::string& value);
  std::string* _internal_mutable_denotation();
  public:

  // int64 dim_value = 1;
  bool has_dim_value() const;
  private:
  bool _internal_has_dim_value() const;
  public:
  void clear_dim_value();
  int64_t dim_value() const;
  void set_dim_value(int64_t value);
  private:
  int64_t _internal_dim_value() const;
  void _internal_set_dim_value(int64_t value);
  public:

  // string dim_param = 2;
  bool has_dim_param() const;
  private:
  bool _internal_has_dim_param() const;
  public:
  void clear_dim_param();
  const std::string& dim_param() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_dim_param(ArgT0&& arg0, ArgT... args);
  std::string* mutable_dim_param();
  PROTOBUF_NODISCARD std::string* release_dim_param();
  void set_allocated_dim_param(std::string* dim_param);
  private:
  const std::string& _internal_dim_param() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_dim_param(const std::string& value);
  std::string* _internal_mutable_dim_param();
  public:

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:opencv_onnx.TensorShapeProto.Dimension)
 private:
  class _Internal;
  void set_has_dim_value();
  void set_has_dim_param();

  inline bool has_value() const;
  inline void clear_has_value();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr denotation_;
  union ValueUnion {
    constexpr ValueUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    int64_t dim_value_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dim_param_;
  } value_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_opencv_2donnx_2eproto;
};
// -------------------------------------------------------------------

class TensorShapeProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_onnx.TensorShapeProto) */ {
 public:
  inline TensorShapeProto() : TensorShapeProto(nullptr) {}
  ~TensorShapeProto() override;
  explicit constexpr TensorShapeProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TensorShapeProto(const TensorShapeProto& from);
  TensorShapeProto(TensorShapeProto&& from) noexcept
    : TensorShapeProto() {
    *this = ::std::move(from);
  }

  inline TensorShapeProto& operator=(const TensorShapeProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorShapeProto& operator=(TensorShapeProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TensorShapeProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const TensorShapeProto* internal_default_instance() {
    return reinterpret_cast<const TensorShapeProto*>(
               &_TensorShapeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(TensorShapeProto& a, TensorShapeProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorShapeProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorShapeProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TensorShapeProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TensorShapeProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TensorShapeProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TensorShapeProto& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorShapeProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_onnx.TensorShapeProto";
  }
  protected:
  explicit TensorShapeProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TensorShapeProto_Dimension Dimension;

  // accessors -------------------------------------------------------

  enum : int {
    kDimFieldNumber = 1,
  };
  // repeated .opencv_onnx.TensorShapeProto.Dimension dim = 1;
  int dim_size() const;
  private:
  int _internal_dim_size() const;
  public:
  void clear_dim();
  ::opencv_onnx::TensorShapeProto_Dimension* mutable_dim(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::TensorShapeProto_Dimension >*
      mutable_dim();
  private:
  const ::opencv_onnx::TensorShapeProto_Dimension& _internal_dim(int index) const;
  ::opencv_onnx::TensorShapeProto_Dimension* _internal_add_dim();
  public:
  const ::opencv_onnx::TensorShapeProto_Dimension& dim(int index) const;
  ::opencv_onnx::TensorShapeProto_Dimension* add_dim();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::TensorShapeProto_Dimension >&
      dim() const;

  // @@protoc_insertion_point(class_scope:opencv_onnx.TensorShapeProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::TensorShapeProto_Dimension > dim_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_opencv_2donnx_2eproto;
};
// -------------------------------------------------------------------

class TypeProto_Tensor final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_onnx.TypeProto.Tensor) */ {
 public:
  inline TypeProto_Tensor() : TypeProto_Tensor(nullptr) {}
  ~TypeProto_Tensor() override;
  explicit constexpr TypeProto_Tensor(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TypeProto_Tensor(const TypeProto_Tensor& from);
  TypeProto_Tensor(TypeProto_Tensor&& from) noexcept
    : TypeProto_Tensor() {
    *this = ::std::move(from);
  }

  inline TypeProto_Tensor& operator=(const TypeProto_Tensor& from) {
    CopyFrom(from);
    return *this;
  }
  inline TypeProto_Tensor& operator=(TypeProto_Tensor&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TypeProto_Tensor& default_instance() {
    return *internal_default_instance();
  }
  static inline const TypeProto_Tensor* internal_default_instance() {
    return reinterpret_cast<const TypeProto_Tensor*>(
               &_TypeProto_Tensor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(TypeProto_Tensor& a, TypeProto_Tensor& b) {
    a.Swap(&b);
  }
  inline void Swap(TypeProto_Tensor* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TypeProto_Tensor* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TypeProto_Tensor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TypeProto_Tensor>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TypeProto_Tensor& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TypeProto_Tensor& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TypeProto_Tensor* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_onnx.TypeProto.Tensor";
  }
  protected:
  explicit TypeProto_Tensor(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 2,
    kElemTypeFieldNumber = 1,
  };
  // optional .opencv_onnx.TensorShapeProto shape = 2;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::opencv_onnx::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::opencv_onnx::TensorShapeProto* release_shape();
  ::opencv_onnx::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::opencv_onnx::TensorShapeProto* shape);
  private:
  const ::opencv_onnx::TensorShapeProto& _internal_shape() const;
  ::opencv_onnx::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::opencv_onnx::TensorShapeProto* shape);
  ::opencv_onnx::TensorShapeProto* unsafe_arena_release_shape();

  // optional .opencv_onnx.TensorProto.DataType elem_type = 1;
  bool has_elem_type() const;
  private:
  bool _internal_has_elem_type() const;
  public:
  void clear_elem_type();
  ::opencv_onnx::TensorProto_DataType elem_type() const;
  void set_elem_type(::opencv_onnx::TensorProto_DataType value);
  private:
  ::opencv_onnx::TensorProto_DataType _internal_elem_type() const;
  void _internal_set_elem_type(::opencv_onnx::TensorProto_DataType value);
  public:

  // @@protoc_insertion_point(class_scope:opencv_onnx.TypeProto.Tensor)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::opencv_onnx::TensorShapeProto* shape_;
  int elem_type_;
  friend struct ::TableStruct_opencv_2donnx_2eproto;
};
// -------------------------------------------------------------------

class TypeProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_onnx.TypeProto) */ {
 public:
  inline TypeProto() : TypeProto(nullptr) {}
  ~TypeProto() override;
  explicit constexpr TypeProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TypeProto(const TypeProto& from);
  TypeProto(TypeProto&& from) noexcept
    : TypeProto() {
    *this = ::std::move(from);
  }

  inline TypeProto& operator=(const TypeProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TypeProto& operator=(TypeProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TypeProto& default_instance() {
    return *internal_default_instance();
  }
  enum ValueCase {
    kTensorType = 1,
    VALUE_NOT_SET = 0,
  };

  static inline const TypeProto* internal_default_instance() {
    return reinterpret_cast<const TypeProto*>(
               &_TypeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(TypeProto& a, TypeProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TypeProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TypeProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TypeProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TypeProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TypeProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TypeProto& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TypeProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_onnx.TypeProto";
  }
  protected:
  explicit TypeProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TypeProto_Tensor Tensor;

  // accessors -------------------------------------------------------

  enum : int {
    kDenotationFieldNumber = 6,
    kTensorTypeFieldNumber = 1,
  };
  // optional string denotation = 6;
  bool has_denotation() const;
  private:
  bool _internal_has_denotation() const;
  public:
  void clear_denotation();
  const std::string& denotation() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_denotation(ArgT0&& arg0, ArgT... args);
  std::string* mutable_denotation();
  PROTOBUF_NODISCARD std::string* release_denotation();
  void set_allocated_denotation(std::string* denotation);
  private:
  const std::string& _internal_denotation() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_denotation(const std::string& value);
  std::string* _internal_mutable_denotation();
  public:

  // .opencv_onnx.TypeProto.Tensor tensor_type = 1;
  bool has_tensor_type() const;
  private:
  bool _internal_has_tensor_type() const;
  public:
  void clear_tensor_type();
  const ::opencv_onnx::TypeProto_Tensor& tensor_type() const;
  PROTOBUF_NODISCARD ::opencv_onnx::TypeProto_Tensor* release_tensor_type();
  ::opencv_onnx::TypeProto_Tensor* mutable_tensor_type();
  void set_allocated_tensor_type(::opencv_onnx::TypeProto_Tensor* tensor_type);
  private:
  const ::opencv_onnx::TypeProto_Tensor& _internal_tensor_type() const;
  ::opencv_onnx::TypeProto_Tensor* _internal_mutable_tensor_type();
  public:
  void unsafe_arena_set_allocated_tensor_type(
      ::opencv_onnx::TypeProto_Tensor* tensor_type);
  ::opencv_onnx::TypeProto_Tensor* unsafe_arena_release_tensor_type();

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:opencv_onnx.TypeProto)
 private:
  class _Internal;
  void set_has_tensor_type();

  inline bool has_value() const;
  inline void clear_has_value();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr denotation_;
  union ValueUnion {
    constexpr ValueUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    ::opencv_onnx::TypeProto_Tensor* tensor_type_;
  } value_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_opencv_2donnx_2eproto;
};
// -------------------------------------------------------------------

class OperatorSetIdProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_onnx.OperatorSetIdProto) */ {
 public:
  inline OperatorSetIdProto() : OperatorSetIdProto(nullptr) {}
  ~OperatorSetIdProto() override;
  explicit constexpr OperatorSetIdProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OperatorSetIdProto(const OperatorSetIdProto& from);
  OperatorSetIdProto(OperatorSetIdProto&& from) noexcept
    : OperatorSetIdProto() {
    *this = ::std::move(from);
  }

  inline OperatorSetIdProto& operator=(const OperatorSetIdProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline OperatorSetIdProto& operator=(OperatorSetIdProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OperatorSetIdProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const OperatorSetIdProto* internal_default_instance() {
    return reinterpret_cast<const OperatorSetIdProto*>(
               &_OperatorSetIdProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(OperatorSetIdProto& a, OperatorSetIdProto& b) {
    a.Swap(&b);
  }
  inline void Swap(OperatorSetIdProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OperatorSetIdProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OperatorSetIdProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OperatorSetIdProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OperatorSetIdProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const OperatorSetIdProto& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OperatorSetIdProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_onnx.OperatorSetIdProto";
  }
  protected:
  explicit OperatorSetIdProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDomainFieldNumber = 1,
    kVersionFieldNumber = 2,
  };
  // optional string domain = 1;
  bool has_domain() const;
  private:
  bool _internal_has_domain() const;
  public:
  void clear_domain();
  const std::string& domain() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_domain(ArgT0&& arg0, ArgT... args);
  std::string* mutable_domain();
  PROTOBUF_NODISCARD std::string* release_domain();
  void set_allocated_domain(std::string* domain);
  private:
  const std::string& _internal_domain() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_domain(const std::string& value);
  std::string* _internal_mutable_domain();
  public:

  // optional int64 version = 2;
  bool has_version() const;
  private:
  bool _internal_has_version() const;
  public:
  void clear_version();
  int64_t version() const;
  void set_version(int64_t value);
  private:
  int64_t _internal_version() const;
  void _internal_set_version(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:opencv_onnx.OperatorSetIdProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr domain_;
  int64_t version_;
  friend struct ::TableStruct_opencv_2donnx_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AttributeProto

// optional string name = 1;
inline bool AttributeProto::_internal_has_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool AttributeProto::has_name() const {
  return _internal_has_name();
}
inline void AttributeProto::clear_name() {
  name_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& AttributeProto::name() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.AttributeProto.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AttributeProto::set_name(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.AttributeProto.name)
}
inline std::string* AttributeProto::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.AttributeProto.name)
  return _s;
}
inline const std::string& AttributeProto::_internal_name() const {
  return name_.Get();
}
inline void AttributeProto::_internal_set_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AttributeProto::_internal_mutable_name() {
  _has_bits_[0] |= 0x00000001u;
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AttributeProto::release_name() {
  // @@protoc_insertion_point(field_release:opencv_onnx.AttributeProto.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = name_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void AttributeProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.AttributeProto.name)
}

// optional string ref_attr_name = 21;
inline bool AttributeProto::_internal_has_ref_attr_name() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool AttributeProto::has_ref_attr_name() const {
  return _internal_has_ref_attr_name();
}
inline void AttributeProto::clear_ref_attr_name() {
  ref_attr_name_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000008u;
}
inline const std::string& AttributeProto::ref_attr_name() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.AttributeProto.ref_attr_name)
  return _internal_ref_attr_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AttributeProto::set_ref_attr_name(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000008u;
 ref_attr_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.AttributeProto.ref_attr_name)
}
inline std::string* AttributeProto::mutable_ref_attr_name() {
  std::string* _s = _internal_mutable_ref_attr_name();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.AttributeProto.ref_attr_name)
  return _s;
}
inline const std::string& AttributeProto::_internal_ref_attr_name() const {
  return ref_attr_name_.Get();
}
inline void AttributeProto::_internal_set_ref_attr_name(const std::string& value) {
  _has_bits_[0] |= 0x00000008u;
  ref_attr_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AttributeProto::_internal_mutable_ref_attr_name() {
  _has_bits_[0] |= 0x00000008u;
  return ref_attr_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AttributeProto::release_ref_attr_name() {
  // @@protoc_insertion_point(field_release:opencv_onnx.AttributeProto.ref_attr_name)
  if (!_internal_has_ref_attr_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000008u;
  auto* p = ref_attr_name_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (ref_attr_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    ref_attr_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void AttributeProto::set_allocated_ref_attr_name(std::string* ref_attr_name) {
  if (ref_attr_name != nullptr) {
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  ref_attr_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ref_attr_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (ref_attr_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    ref_attr_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.AttributeProto.ref_attr_name)
}

// optional string doc_string = 13;
inline bool AttributeProto::_internal_has_doc_string() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool AttributeProto::has_doc_string() const {
  return _internal_has_doc_string();
}
inline void AttributeProto::clear_doc_string() {
  doc_string_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000004u;
}
inline const std::string& AttributeProto::doc_string() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.AttributeProto.doc_string)
  return _internal_doc_string();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AttributeProto::set_doc_string(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000004u;
 doc_string_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.AttributeProto.doc_string)
}
inline std::string* AttributeProto::mutable_doc_string() {
  std::string* _s = _internal_mutable_doc_string();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.AttributeProto.doc_string)
  return _s;
}
inline const std::string& AttributeProto::_internal_doc_string() const {
  return doc_string_.Get();
}
inline void AttributeProto::_internal_set_doc_string(const std::string& value) {
  _has_bits_[0] |= 0x00000004u;
  doc_string_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AttributeProto::_internal_mutable_doc_string() {
  _has_bits_[0] |= 0x00000004u;
  return doc_string_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AttributeProto::release_doc_string() {
  // @@protoc_insertion_point(field_release:opencv_onnx.AttributeProto.doc_string)
  if (!_internal_has_doc_string()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000004u;
  auto* p = doc_string_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (doc_string_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    doc_string_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void AttributeProto::set_allocated_doc_string(std::string* doc_string) {
  if (doc_string != nullptr) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  doc_string_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), doc_string,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (doc_string_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    doc_string_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.AttributeProto.doc_string)
}

// optional .opencv_onnx.AttributeProto.AttributeType type = 20;
inline bool AttributeProto::_internal_has_type() const {
  bool value = (_has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool AttributeProto::has_type() const {
  return _internal_has_type();
}
inline void AttributeProto::clear_type() {
  type_ = 0;
  _has_bits_[0] &= ~0x00000100u;
}
inline ::opencv_onnx::AttributeProto_AttributeType AttributeProto::_internal_type() const {
  return static_cast< ::opencv_onnx::AttributeProto_AttributeType >(type_);
}
inline ::opencv_onnx::AttributeProto_AttributeType AttributeProto::type() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.AttributeProto.type)
  return _internal_type();
}
inline void AttributeProto::_internal_set_type(::opencv_onnx::AttributeProto_AttributeType value) {
  assert(::opencv_onnx::AttributeProto_AttributeType_IsValid(value));
  _has_bits_[0] |= 0x00000100u;
  type_ = value;
}
inline void AttributeProto::set_type(::opencv_onnx::AttributeProto_AttributeType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:opencv_onnx.AttributeProto.type)
}

// optional float f = 2;
inline bool AttributeProto::_internal_has_f() const {
  bool value = (_has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool AttributeProto::has_f() const {
  return _internal_has_f();
}
inline void AttributeProto::clear_f() {
  f_ = 0;
  _has_bits_[0] &= ~0x00000080u;
}
inline float AttributeProto::_internal_f() const {
  return f_;
}
inline float AttributeProto::f() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.AttributeProto.f)
  return _internal_f();
}
inline void AttributeProto::_internal_set_f(float value) {
  _has_bits_[0] |= 0x00000080u;
  f_ = value;
}
inline void AttributeProto::set_f(float value) {
  _internal_set_f(value);
  // @@protoc_insertion_point(field_set:opencv_onnx.AttributeProto.f)
}

// optional int64 i = 3;
inline bool AttributeProto::_internal_has_i() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool AttributeProto::has_i() const {
  return _internal_has_i();
}
inline void AttributeProto::clear_i() {
  i_ = int64_t{0};
  _has_bits_[0] &= ~0x00000040u;
}
inline int64_t AttributeProto::_internal_i() const {
  return i_;
}
inline int64_t AttributeProto::i() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.AttributeProto.i)
  return _internal_i();
}
inline void AttributeProto::_internal_set_i(int64_t value) {
  _has_bits_[0] |= 0x00000040u;
  i_ = value;
}
inline void AttributeProto::set_i(int64_t value) {
  _internal_set_i(value);
  // @@protoc_insertion_point(field_set:opencv_onnx.AttributeProto.i)
}

// optional bytes s = 4;
inline bool AttributeProto::_internal_has_s() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool AttributeProto::has_s() const {
  return _internal_has_s();
}
inline void AttributeProto::clear_s() {
  s_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& AttributeProto::s() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.AttributeProto.s)
  return _internal_s();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AttributeProto::set_s(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000002u;
 s_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.AttributeProto.s)
}
inline std::string* AttributeProto::mutable_s() {
  std::string* _s = _internal_mutable_s();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.AttributeProto.s)
  return _s;
}
inline const std::string& AttributeProto::_internal_s() const {
  return s_.Get();
}
inline void AttributeProto::_internal_set_s(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  s_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AttributeProto::_internal_mutable_s() {
  _has_bits_[0] |= 0x00000002u;
  return s_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AttributeProto::release_s() {
  // @@protoc_insertion_point(field_release:opencv_onnx.AttributeProto.s)
  if (!_internal_has_s()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  auto* p = s_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (s_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void AttributeProto::set_allocated_s(std::string* s) {
  if (s != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  s_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), s,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (s_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    s_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.AttributeProto.s)
}

// optional .opencv_onnx.TensorProto t = 5;
inline bool AttributeProto::_internal_has_t() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  PROTOBUF_ASSUME(!value || t_ != nullptr);
  return value;
}
inline bool AttributeProto::has_t() const {
  return _internal_has_t();
}
inline void AttributeProto::clear_t() {
  if (t_ != nullptr) t_->Clear();
  _has_bits_[0] &= ~0x00000010u;
}
inline const ::opencv_onnx::TensorProto& AttributeProto::_internal_t() const {
  const ::opencv_onnx::TensorProto* p = t_;
  return p != nullptr ? *p : reinterpret_cast<const ::opencv_onnx::TensorProto&>(
      ::opencv_onnx::_TensorProto_default_instance_);
}
inline const ::opencv_onnx::TensorProto& AttributeProto::t() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.AttributeProto.t)
  return _internal_t();
}
inline void AttributeProto::unsafe_arena_set_allocated_t(
    ::opencv_onnx::TensorProto* t) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(t_);
  }
  t_ = t;
  if (t) {
    _has_bits_[0] |= 0x00000010u;
  } else {
    _has_bits_[0] &= ~0x00000010u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:opencv_onnx.AttributeProto.t)
}
inline ::opencv_onnx::TensorProto* AttributeProto::release_t() {
  _has_bits_[0] &= ~0x00000010u;
  ::opencv_onnx::TensorProto* temp = t_;
  t_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::opencv_onnx::TensorProto* AttributeProto::unsafe_arena_release_t() {
  // @@protoc_insertion_point(field_release:opencv_onnx.AttributeProto.t)
  _has_bits_[0] &= ~0x00000010u;
  ::opencv_onnx::TensorProto* temp = t_;
  t_ = nullptr;
  return temp;
}
inline ::opencv_onnx::TensorProto* AttributeProto::_internal_mutable_t() {
  _has_bits_[0] |= 0x00000010u;
  if (t_ == nullptr) {
    auto* p = CreateMaybeMessage<::opencv_onnx::TensorProto>(GetArenaForAllocation());
    t_ = p;
  }
  return t_;
}
inline ::opencv_onnx::TensorProto* AttributeProto::mutable_t() {
  ::opencv_onnx::TensorProto* _msg = _internal_mutable_t();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.AttributeProto.t)
  return _msg;
}
inline void AttributeProto::set_allocated_t(::opencv_onnx::TensorProto* t) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete t_;
  }
  if (t) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::opencv_onnx::TensorProto>::GetOwningArena(t);
    if (message_arena != submessage_arena) {
      t = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, t, submessage_arena);
    }
    _has_bits_[0] |= 0x00000010u;
  } else {
    _has_bits_[0] &= ~0x00000010u;
  }
  t_ = t;
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.AttributeProto.t)
}

// optional .opencv_onnx.GraphProto g = 6;
inline bool AttributeProto::_internal_has_g() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  PROTOBUF_ASSUME(!value || g_ != nullptr);
  return value;
}
inline bool AttributeProto::has_g() const {
  return _internal_has_g();
}
inline void AttributeProto::clear_g() {
  if (g_ != nullptr) g_->Clear();
  _has_bits_[0] &= ~0x00000020u;
}
inline const ::opencv_onnx::GraphProto& AttributeProto::_internal_g() const {
  const ::opencv_onnx::GraphProto* p = g_;
  return p != nullptr ? *p : reinterpret_cast<const ::opencv_onnx::GraphProto&>(
      ::opencv_onnx::_GraphProto_default_instance_);
}
inline const ::opencv_onnx::GraphProto& AttributeProto::g() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.AttributeProto.g)
  return _internal_g();
}
inline void AttributeProto::unsafe_arena_set_allocated_g(
    ::opencv_onnx::GraphProto* g) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(g_);
  }
  g_ = g;
  if (g) {
    _has_bits_[0] |= 0x00000020u;
  } else {
    _has_bits_[0] &= ~0x00000020u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:opencv_onnx.AttributeProto.g)
}
inline ::opencv_onnx::GraphProto* AttributeProto::release_g() {
  _has_bits_[0] &= ~0x00000020u;
  ::opencv_onnx::GraphProto* temp = g_;
  g_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::opencv_onnx::GraphProto* AttributeProto::unsafe_arena_release_g() {
  // @@protoc_insertion_point(field_release:opencv_onnx.AttributeProto.g)
  _has_bits_[0] &= ~0x00000020u;
  ::opencv_onnx::GraphProto* temp = g_;
  g_ = nullptr;
  return temp;
}
inline ::opencv_onnx::GraphProto* AttributeProto::_internal_mutable_g() {
  _has_bits_[0] |= 0x00000020u;
  if (g_ == nullptr) {
    auto* p = CreateMaybeMessage<::opencv_onnx::GraphProto>(GetArenaForAllocation());
    g_ = p;
  }
  return g_;
}
inline ::opencv_onnx::GraphProto* AttributeProto::mutable_g() {
  ::opencv_onnx::GraphProto* _msg = _internal_mutable_g();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.AttributeProto.g)
  return _msg;
}
inline void AttributeProto::set_allocated_g(::opencv_onnx::GraphProto* g) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete g_;
  }
  if (g) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::opencv_onnx::GraphProto>::GetOwningArena(g);
    if (message_arena != submessage_arena) {
      g = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, g, submessage_arena);
    }
    _has_bits_[0] |= 0x00000020u;
  } else {
    _has_bits_[0] &= ~0x00000020u;
  }
  g_ = g;
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.AttributeProto.g)
}

// repeated float floats = 7;
inline int AttributeProto::_internal_floats_size() const {
  return floats_.size();
}
inline int AttributeProto::floats_size() const {
  return _internal_floats_size();
}
inline void AttributeProto::clear_floats() {
  floats_.Clear();
}
inline float AttributeProto::_internal_floats(int index) const {
  return floats_.Get(index);
}
inline float AttributeProto::floats(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.AttributeProto.floats)
  return _internal_floats(index);
}
inline void AttributeProto::set_floats(int index, float value) {
  floats_.Set(index, value);
  // @@protoc_insertion_point(field_set:opencv_onnx.AttributeProto.floats)
}
inline void AttributeProto::_internal_add_floats(float value) {
  floats_.Add(value);
}
inline void AttributeProto::add_floats(float value) {
  _internal_add_floats(value);
  // @@protoc_insertion_point(field_add:opencv_onnx.AttributeProto.floats)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
AttributeProto::_internal_floats() const {
  return floats_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
AttributeProto::floats() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.AttributeProto.floats)
  return _internal_floats();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
AttributeProto::_internal_mutable_floats() {
  return &floats_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
AttributeProto::mutable_floats() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.AttributeProto.floats)
  return _internal_mutable_floats();
}

// repeated int64 ints = 8;
inline int AttributeProto::_internal_ints_size() const {
  return ints_.size();
}
inline int AttributeProto::ints_size() const {
  return _internal_ints_size();
}
inline void AttributeProto::clear_ints() {
  ints_.Clear();
}
inline int64_t AttributeProto::_internal_ints(int index) const {
  return ints_.Get(index);
}
inline int64_t AttributeProto::ints(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.AttributeProto.ints)
  return _internal_ints(index);
}
inline void AttributeProto::set_ints(int index, int64_t value) {
  ints_.Set(index, value);
  // @@protoc_insertion_point(field_set:opencv_onnx.AttributeProto.ints)
}
inline void AttributeProto::_internal_add_ints(int64_t value) {
  ints_.Add(value);
}
inline void AttributeProto::add_ints(int64_t value) {
  _internal_add_ints(value);
  // @@protoc_insertion_point(field_add:opencv_onnx.AttributeProto.ints)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
AttributeProto::_internal_ints() const {
  return ints_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
AttributeProto::ints() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.AttributeProto.ints)
  return _internal_ints();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
AttributeProto::_internal_mutable_ints() {
  return &ints_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
AttributeProto::mutable_ints() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.AttributeProto.ints)
  return _internal_mutable_ints();
}

// repeated bytes strings = 9;
inline int AttributeProto::_internal_strings_size() const {
  return strings_.size();
}
inline int AttributeProto::strings_size() const {
  return _internal_strings_size();
}
inline void AttributeProto::clear_strings() {
  strings_.Clear();
}
inline std::string* AttributeProto::add_strings() {
  std::string* _s = _internal_add_strings();
  // @@protoc_insertion_point(field_add_mutable:opencv_onnx.AttributeProto.strings)
  return _s;
}
inline const std::string& AttributeProto::_internal_strings(int index) const {
  return strings_.Get(index);
}
inline const std::string& AttributeProto::strings(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.AttributeProto.strings)
  return _internal_strings(index);
}
inline std::string* AttributeProto::mutable_strings(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_onnx.AttributeProto.strings)
  return strings_.Mutable(index);
}
inline void AttributeProto::set_strings(int index, const std::string& value) {
  strings_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:opencv_onnx.AttributeProto.strings)
}
inline void AttributeProto::set_strings(int index, std::string&& value) {
  strings_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:opencv_onnx.AttributeProto.strings)
}
inline void AttributeProto::set_strings(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  strings_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:opencv_onnx.AttributeProto.strings)
}
inline void AttributeProto::set_strings(int index, const void* value, size_t size) {
  strings_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:opencv_onnx.AttributeProto.strings)
}
inline std::string* AttributeProto::_internal_add_strings() {
  return strings_.Add();
}
inline void AttributeProto::add_strings(const std::string& value) {
  strings_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:opencv_onnx.AttributeProto.strings)
}
inline void AttributeProto::add_strings(std::string&& value) {
  strings_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:opencv_onnx.AttributeProto.strings)
}
inline void AttributeProto::add_strings(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  strings_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:opencv_onnx.AttributeProto.strings)
}
inline void AttributeProto::add_strings(const void* value, size_t size) {
  strings_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:opencv_onnx.AttributeProto.strings)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
AttributeProto::strings() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.AttributeProto.strings)
  return strings_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
AttributeProto::mutable_strings() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.AttributeProto.strings)
  return &strings_;
}

// repeated .opencv_onnx.TensorProto tensors = 10;
inline int AttributeProto::_internal_tensors_size() const {
  return tensors_.size();
}
inline int AttributeProto::tensors_size() const {
  return _internal_tensors_size();
}
inline void AttributeProto::clear_tensors() {
  tensors_.Clear();
}
inline ::opencv_onnx::TensorProto* AttributeProto::mutable_tensors(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_onnx.AttributeProto.tensors)
  return tensors_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::TensorProto >*
AttributeProto::mutable_tensors() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.AttributeProto.tensors)
  return &tensors_;
}
inline const ::opencv_onnx::TensorProto& AttributeProto::_internal_tensors(int index) const {
  return tensors_.Get(index);
}
inline const ::opencv_onnx::TensorProto& AttributeProto::tensors(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.AttributeProto.tensors)
  return _internal_tensors(index);
}
inline ::opencv_onnx::TensorProto* AttributeProto::_internal_add_tensors() {
  return tensors_.Add();
}
inline ::opencv_onnx::TensorProto* AttributeProto::add_tensors() {
  ::opencv_onnx::TensorProto* _add = _internal_add_tensors();
  // @@protoc_insertion_point(field_add:opencv_onnx.AttributeProto.tensors)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::TensorProto >&
AttributeProto::tensors() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.AttributeProto.tensors)
  return tensors_;
}

// repeated .opencv_onnx.GraphProto graphs = 11;
inline int AttributeProto::_internal_graphs_size() const {
  return graphs_.size();
}
inline int AttributeProto::graphs_size() const {
  return _internal_graphs_size();
}
inline void AttributeProto::clear_graphs() {
  graphs_.Clear();
}
inline ::opencv_onnx::GraphProto* AttributeProto::mutable_graphs(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_onnx.AttributeProto.graphs)
  return graphs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::GraphProto >*
AttributeProto::mutable_graphs() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.AttributeProto.graphs)
  return &graphs_;
}
inline const ::opencv_onnx::GraphProto& AttributeProto::_internal_graphs(int index) const {
  return graphs_.Get(index);
}
inline const ::opencv_onnx::GraphProto& AttributeProto::graphs(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.AttributeProto.graphs)
  return _internal_graphs(index);
}
inline ::opencv_onnx::GraphProto* AttributeProto::_internal_add_graphs() {
  return graphs_.Add();
}
inline ::opencv_onnx::GraphProto* AttributeProto::add_graphs() {
  ::opencv_onnx::GraphProto* _add = _internal_add_graphs();
  // @@protoc_insertion_point(field_add:opencv_onnx.AttributeProto.graphs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::GraphProto >&
AttributeProto::graphs() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.AttributeProto.graphs)
  return graphs_;
}

// -------------------------------------------------------------------

// ValueInfoProto

// optional string name = 1;
inline bool ValueInfoProto::_internal_has_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool ValueInfoProto::has_name() const {
  return _internal_has_name();
}
inline void ValueInfoProto::clear_name() {
  name_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& ValueInfoProto::name() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.ValueInfoProto.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ValueInfoProto::set_name(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.ValueInfoProto.name)
}
inline std::string* ValueInfoProto::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.ValueInfoProto.name)
  return _s;
}
inline const std::string& ValueInfoProto::_internal_name() const {
  return name_.Get();
}
inline void ValueInfoProto::_internal_set_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ValueInfoProto::_internal_mutable_name() {
  _has_bits_[0] |= 0x00000001u;
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ValueInfoProto::release_name() {
  // @@protoc_insertion_point(field_release:opencv_onnx.ValueInfoProto.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = name_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void ValueInfoProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.ValueInfoProto.name)
}

// optional .opencv_onnx.TypeProto type = 2;
inline bool ValueInfoProto::_internal_has_type() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  PROTOBUF_ASSUME(!value || type_ != nullptr);
  return value;
}
inline bool ValueInfoProto::has_type() const {
  return _internal_has_type();
}
inline void ValueInfoProto::clear_type() {
  if (type_ != nullptr) type_->Clear();
  _has_bits_[0] &= ~0x00000004u;
}
inline const ::opencv_onnx::TypeProto& ValueInfoProto::_internal_type() const {
  const ::opencv_onnx::TypeProto* p = type_;
  return p != nullptr ? *p : reinterpret_cast<const ::opencv_onnx::TypeProto&>(
      ::opencv_onnx::_TypeProto_default_instance_);
}
inline const ::opencv_onnx::TypeProto& ValueInfoProto::type() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.ValueInfoProto.type)
  return _internal_type();
}
inline void ValueInfoProto::unsafe_arena_set_allocated_type(
    ::opencv_onnx::TypeProto* type) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(type_);
  }
  type_ = type;
  if (type) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:opencv_onnx.ValueInfoProto.type)
}
inline ::opencv_onnx::TypeProto* ValueInfoProto::release_type() {
  _has_bits_[0] &= ~0x00000004u;
  ::opencv_onnx::TypeProto* temp = type_;
  type_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::opencv_onnx::TypeProto* ValueInfoProto::unsafe_arena_release_type() {
  // @@protoc_insertion_point(field_release:opencv_onnx.ValueInfoProto.type)
  _has_bits_[0] &= ~0x00000004u;
  ::opencv_onnx::TypeProto* temp = type_;
  type_ = nullptr;
  return temp;
}
inline ::opencv_onnx::TypeProto* ValueInfoProto::_internal_mutable_type() {
  _has_bits_[0] |= 0x00000004u;
  if (type_ == nullptr) {
    auto* p = CreateMaybeMessage<::opencv_onnx::TypeProto>(GetArenaForAllocation());
    type_ = p;
  }
  return type_;
}
inline ::opencv_onnx::TypeProto* ValueInfoProto::mutable_type() {
  ::opencv_onnx::TypeProto* _msg = _internal_mutable_type();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.ValueInfoProto.type)
  return _msg;
}
inline void ValueInfoProto::set_allocated_type(::opencv_onnx::TypeProto* type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete type_;
  }
  if (type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::opencv_onnx::TypeProto>::GetOwningArena(type);
    if (message_arena != submessage_arena) {
      type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, type, submessage_arena);
    }
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  type_ = type;
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.ValueInfoProto.type)
}

// optional string doc_string = 3;
inline bool ValueInfoProto::_internal_has_doc_string() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool ValueInfoProto::has_doc_string() const {
  return _internal_has_doc_string();
}
inline void ValueInfoProto::clear_doc_string() {
  doc_string_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& ValueInfoProto::doc_string() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.ValueInfoProto.doc_string)
  return _internal_doc_string();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ValueInfoProto::set_doc_string(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000002u;
 doc_string_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.ValueInfoProto.doc_string)
}
inline std::string* ValueInfoProto::mutable_doc_string() {
  std::string* _s = _internal_mutable_doc_string();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.ValueInfoProto.doc_string)
  return _s;
}
inline const std::string& ValueInfoProto::_internal_doc_string() const {
  return doc_string_.Get();
}
inline void ValueInfoProto::_internal_set_doc_string(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  doc_string_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ValueInfoProto::_internal_mutable_doc_string() {
  _has_bits_[0] |= 0x00000002u;
  return doc_string_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ValueInfoProto::release_doc_string() {
  // @@protoc_insertion_point(field_release:opencv_onnx.ValueInfoProto.doc_string)
  if (!_internal_has_doc_string()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  auto* p = doc_string_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (doc_string_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    doc_string_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void ValueInfoProto::set_allocated_doc_string(std::string* doc_string) {
  if (doc_string != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  doc_string_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), doc_string,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (doc_string_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    doc_string_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.ValueInfoProto.doc_string)
}

// -------------------------------------------------------------------

// NodeProto

// repeated string input = 1;
inline int NodeProto::_internal_input_size() const {
  return input_.size();
}
inline int NodeProto::input_size() const {
  return _internal_input_size();
}
inline void NodeProto::clear_input() {
  input_.Clear();
}
inline std::string* NodeProto::add_input() {
  std::string* _s = _internal_add_input();
  // @@protoc_insertion_point(field_add_mutable:opencv_onnx.NodeProto.input)
  return _s;
}
inline const std::string& NodeProto::_internal_input(int index) const {
  return input_.Get(index);
}
inline const std::string& NodeProto::input(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.NodeProto.input)
  return _internal_input(index);
}
inline std::string* NodeProto::mutable_input(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_onnx.NodeProto.input)
  return input_.Mutable(index);
}
inline void NodeProto::set_input(int index, const std::string& value) {
  input_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:opencv_onnx.NodeProto.input)
}
inline void NodeProto::set_input(int index, std::string&& value) {
  input_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:opencv_onnx.NodeProto.input)
}
inline void NodeProto::set_input(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  input_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:opencv_onnx.NodeProto.input)
}
inline void NodeProto::set_input(int index, const char* value, size_t size) {
  input_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:opencv_onnx.NodeProto.input)
}
inline std::string* NodeProto::_internal_add_input() {
  return input_.Add();
}
inline void NodeProto::add_input(const std::string& value) {
  input_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:opencv_onnx.NodeProto.input)
}
inline void NodeProto::add_input(std::string&& value) {
  input_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:opencv_onnx.NodeProto.input)
}
inline void NodeProto::add_input(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  input_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:opencv_onnx.NodeProto.input)
}
inline void NodeProto::add_input(const char* value, size_t size) {
  input_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:opencv_onnx.NodeProto.input)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
NodeProto::input() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.NodeProto.input)
  return input_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
NodeProto::mutable_input() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.NodeProto.input)
  return &input_;
}

// repeated string output = 2;
inline int NodeProto::_internal_output_size() const {
  return output_.size();
}
inline int NodeProto::output_size() const {
  return _internal_output_size();
}
inline void NodeProto::clear_output() {
  output_.Clear();
}
inline std::string* NodeProto::add_output() {
  std::string* _s = _internal_add_output();
  // @@protoc_insertion_point(field_add_mutable:opencv_onnx.NodeProto.output)
  return _s;
}
inline const std::string& NodeProto::_internal_output(int index) const {
  return output_.Get(index);
}
inline const std::string& NodeProto::output(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.NodeProto.output)
  return _internal_output(index);
}
inline std::string* NodeProto::mutable_output(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_onnx.NodeProto.output)
  return output_.Mutable(index);
}
inline void NodeProto::set_output(int index, const std::string& value) {
  output_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:opencv_onnx.NodeProto.output)
}
inline void NodeProto::set_output(int index, std::string&& value) {
  output_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:opencv_onnx.NodeProto.output)
}
inline void NodeProto::set_output(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  output_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:opencv_onnx.NodeProto.output)
}
inline void NodeProto::set_output(int index, const char* value, size_t size) {
  output_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:opencv_onnx.NodeProto.output)
}
inline std::string* NodeProto::_internal_add_output() {
  return output_.Add();
}
inline void NodeProto::add_output(const std::string& value) {
  output_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:opencv_onnx.NodeProto.output)
}
inline void NodeProto::add_output(std::string&& value) {
  output_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:opencv_onnx.NodeProto.output)
}
inline void NodeProto::add_output(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  output_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:opencv_onnx.NodeProto.output)
}
inline void NodeProto::add_output(const char* value, size_t size) {
  output_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:opencv_onnx.NodeProto.output)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
NodeProto::output() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.NodeProto.output)
  return output_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
NodeProto::mutable_output() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.NodeProto.output)
  return &output_;
}

// optional string name = 3;
inline bool NodeProto::_internal_has_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool NodeProto::has_name() const {
  return _internal_has_name();
}
inline void NodeProto::clear_name() {
  name_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& NodeProto::name() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.NodeProto.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NodeProto::set_name(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.NodeProto.name)
}
inline std::string* NodeProto::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.NodeProto.name)
  return _s;
}
inline const std::string& NodeProto::_internal_name() const {
  return name_.Get();
}
inline void NodeProto::_internal_set_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* NodeProto::_internal_mutable_name() {
  _has_bits_[0] |= 0x00000001u;
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* NodeProto::release_name() {
  // @@protoc_insertion_point(field_release:opencv_onnx.NodeProto.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = name_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void NodeProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.NodeProto.name)
}

// optional string op_type = 4;
inline bool NodeProto::_internal_has_op_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool NodeProto::has_op_type() const {
  return _internal_has_op_type();
}
inline void NodeProto::clear_op_type() {
  op_type_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& NodeProto::op_type() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.NodeProto.op_type)
  return _internal_op_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NodeProto::set_op_type(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000002u;
 op_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.NodeProto.op_type)
}
inline std::string* NodeProto::mutable_op_type() {
  std::string* _s = _internal_mutable_op_type();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.NodeProto.op_type)
  return _s;
}
inline const std::string& NodeProto::_internal_op_type() const {
  return op_type_.Get();
}
inline void NodeProto::_internal_set_op_type(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  op_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* NodeProto::_internal_mutable_op_type() {
  _has_bits_[0] |= 0x00000002u;
  return op_type_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* NodeProto::release_op_type() {
  // @@protoc_insertion_point(field_release:opencv_onnx.NodeProto.op_type)
  if (!_internal_has_op_type()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  auto* p = op_type_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (op_type_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    op_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void NodeProto::set_allocated_op_type(std::string* op_type) {
  if (op_type != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  op_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), op_type,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (op_type_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    op_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.NodeProto.op_type)
}

// optional string domain = 7;
inline bool NodeProto::_internal_has_domain() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool NodeProto::has_domain() const {
  return _internal_has_domain();
}
inline void NodeProto::clear_domain() {
  domain_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000008u;
}
inline const std::string& NodeProto::domain() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.NodeProto.domain)
  return _internal_domain();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NodeProto::set_domain(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000008u;
 domain_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.NodeProto.domain)
}
inline std::string* NodeProto::mutable_domain() {
  std::string* _s = _internal_mutable_domain();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.NodeProto.domain)
  return _s;
}
inline const std::string& NodeProto::_internal_domain() const {
  return domain_.Get();
}
inline void NodeProto::_internal_set_domain(const std::string& value) {
  _has_bits_[0] |= 0x00000008u;
  domain_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* NodeProto::_internal_mutable_domain() {
  _has_bits_[0] |= 0x00000008u;
  return domain_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* NodeProto::release_domain() {
  // @@protoc_insertion_point(field_release:opencv_onnx.NodeProto.domain)
  if (!_internal_has_domain()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000008u;
  auto* p = domain_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (domain_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    domain_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void NodeProto::set_allocated_domain(std::string* domain) {
  if (domain != nullptr) {
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  domain_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), domain,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (domain_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    domain_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.NodeProto.domain)
}

// repeated .opencv_onnx.AttributeProto attribute = 5;
inline int NodeProto::_internal_attribute_size() const {
  return attribute_.size();
}
inline int NodeProto::attribute_size() const {
  return _internal_attribute_size();
}
inline void NodeProto::clear_attribute() {
  attribute_.Clear();
}
inline ::opencv_onnx::AttributeProto* NodeProto::mutable_attribute(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_onnx.NodeProto.attribute)
  return attribute_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::AttributeProto >*
NodeProto::mutable_attribute() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.NodeProto.attribute)
  return &attribute_;
}
inline const ::opencv_onnx::AttributeProto& NodeProto::_internal_attribute(int index) const {
  return attribute_.Get(index);
}
inline const ::opencv_onnx::AttributeProto& NodeProto::attribute(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.NodeProto.attribute)
  return _internal_attribute(index);
}
inline ::opencv_onnx::AttributeProto* NodeProto::_internal_add_attribute() {
  return attribute_.Add();
}
inline ::opencv_onnx::AttributeProto* NodeProto::add_attribute() {
  ::opencv_onnx::AttributeProto* _add = _internal_add_attribute();
  // @@protoc_insertion_point(field_add:opencv_onnx.NodeProto.attribute)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::AttributeProto >&
NodeProto::attribute() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.NodeProto.attribute)
  return attribute_;
}

// optional string doc_string = 6;
inline bool NodeProto::_internal_has_doc_string() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool NodeProto::has_doc_string() const {
  return _internal_has_doc_string();
}
inline void NodeProto::clear_doc_string() {
  doc_string_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000004u;
}
inline const std::string& NodeProto::doc_string() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.NodeProto.doc_string)
  return _internal_doc_string();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NodeProto::set_doc_string(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000004u;
 doc_string_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.NodeProto.doc_string)
}
inline std::string* NodeProto::mutable_doc_string() {
  std::string* _s = _internal_mutable_doc_string();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.NodeProto.doc_string)
  return _s;
}
inline const std::string& NodeProto::_internal_doc_string() const {
  return doc_string_.Get();
}
inline void NodeProto::_internal_set_doc_string(const std::string& value) {
  _has_bits_[0] |= 0x00000004u;
  doc_string_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* NodeProto::_internal_mutable_doc_string() {
  _has_bits_[0] |= 0x00000004u;
  return doc_string_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* NodeProto::release_doc_string() {
  // @@protoc_insertion_point(field_release:opencv_onnx.NodeProto.doc_string)
  if (!_internal_has_doc_string()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000004u;
  auto* p = doc_string_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (doc_string_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    doc_string_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void NodeProto::set_allocated_doc_string(std::string* doc_string) {
  if (doc_string != nullptr) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  doc_string_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), doc_string,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (doc_string_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    doc_string_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.NodeProto.doc_string)
}

// -------------------------------------------------------------------

// ModelProto

// optional int64 ir_version = 1;
inline bool ModelProto::_internal_has_ir_version() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool ModelProto::has_ir_version() const {
  return _internal_has_ir_version();
}
inline void ModelProto::clear_ir_version() {
  ir_version_ = int64_t{0};
  _has_bits_[0] &= ~0x00000020u;
}
inline int64_t ModelProto::_internal_ir_version() const {
  return ir_version_;
}
inline int64_t ModelProto::ir_version() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.ModelProto.ir_version)
  return _internal_ir_version();
}
inline void ModelProto::_internal_set_ir_version(int64_t value) {
  _has_bits_[0] |= 0x00000020u;
  ir_version_ = value;
}
inline void ModelProto::set_ir_version(int64_t value) {
  _internal_set_ir_version(value);
  // @@protoc_insertion_point(field_set:opencv_onnx.ModelProto.ir_version)
}

// repeated .opencv_onnx.OperatorSetIdProto opset_import = 8;
inline int ModelProto::_internal_opset_import_size() const {
  return opset_import_.size();
}
inline int ModelProto::opset_import_size() const {
  return _internal_opset_import_size();
}
inline void ModelProto::clear_opset_import() {
  opset_import_.Clear();
}
inline ::opencv_onnx::OperatorSetIdProto* ModelProto::mutable_opset_import(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_onnx.ModelProto.opset_import)
  return opset_import_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::OperatorSetIdProto >*
ModelProto::mutable_opset_import() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.ModelProto.opset_import)
  return &opset_import_;
}
inline const ::opencv_onnx::OperatorSetIdProto& ModelProto::_internal_opset_import(int index) const {
  return opset_import_.Get(index);
}
inline const ::opencv_onnx::OperatorSetIdProto& ModelProto::opset_import(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.ModelProto.opset_import)
  return _internal_opset_import(index);
}
inline ::opencv_onnx::OperatorSetIdProto* ModelProto::_internal_add_opset_import() {
  return opset_import_.Add();
}
inline ::opencv_onnx::OperatorSetIdProto* ModelProto::add_opset_import() {
  ::opencv_onnx::OperatorSetIdProto* _add = _internal_add_opset_import();
  // @@protoc_insertion_point(field_add:opencv_onnx.ModelProto.opset_import)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::OperatorSetIdProto >&
ModelProto::opset_import() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.ModelProto.opset_import)
  return opset_import_;
}

// optional string producer_name = 2;
inline bool ModelProto::_internal_has_producer_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool ModelProto::has_producer_name() const {
  return _internal_has_producer_name();
}
inline void ModelProto::clear_producer_name() {
  producer_name_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& ModelProto::producer_name() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.ModelProto.producer_name)
  return _internal_producer_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelProto::set_producer_name(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 producer_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.ModelProto.producer_name)
}
inline std::string* ModelProto::mutable_producer_name() {
  std::string* _s = _internal_mutable_producer_name();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.ModelProto.producer_name)
  return _s;
}
inline const std::string& ModelProto::_internal_producer_name() const {
  return producer_name_.Get();
}
inline void ModelProto::_internal_set_producer_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  producer_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelProto::_internal_mutable_producer_name() {
  _has_bits_[0] |= 0x00000001u;
  return producer_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelProto::release_producer_name() {
  // @@protoc_insertion_point(field_release:opencv_onnx.ModelProto.producer_name)
  if (!_internal_has_producer_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = producer_name_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (producer_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    producer_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void ModelProto::set_allocated_producer_name(std::string* producer_name) {
  if (producer_name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  producer_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), producer_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (producer_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    producer_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.ModelProto.producer_name)
}

// optional string producer_version = 3;
inline bool ModelProto::_internal_has_producer_version() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool ModelProto::has_producer_version() const {
  return _internal_has_producer_version();
}
inline void ModelProto::clear_producer_version() {
  producer_version_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& ModelProto::producer_version() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.ModelProto.producer_version)
  return _internal_producer_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelProto::set_producer_version(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000002u;
 producer_version_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.ModelProto.producer_version)
}
inline std::string* ModelProto::mutable_producer_version() {
  std::string* _s = _internal_mutable_producer_version();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.ModelProto.producer_version)
  return _s;
}
inline const std::string& ModelProto::_internal_producer_version() const {
  return producer_version_.Get();
}
inline void ModelProto::_internal_set_producer_version(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  producer_version_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelProto::_internal_mutable_producer_version() {
  _has_bits_[0] |= 0x00000002u;
  return producer_version_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelProto::release_producer_version() {
  // @@protoc_insertion_point(field_release:opencv_onnx.ModelProto.producer_version)
  if (!_internal_has_producer_version()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  auto* p = producer_version_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (producer_version_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    producer_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void ModelProto::set_allocated_producer_version(std::string* producer_version) {
  if (producer_version != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  producer_version_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), producer_version,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (producer_version_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    producer_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.ModelProto.producer_version)
}

// optional string domain = 4;
inline bool ModelProto::_internal_has_domain() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool ModelProto::has_domain() const {
  return _internal_has_domain();
}
inline void ModelProto::clear_domain() {
  domain_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000004u;
}
inline const std::string& ModelProto::domain() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.ModelProto.domain)
  return _internal_domain();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelProto::set_domain(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000004u;
 domain_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.ModelProto.domain)
}
inline std::string* ModelProto::mutable_domain() {
  std::string* _s = _internal_mutable_domain();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.ModelProto.domain)
  return _s;
}
inline const std::string& ModelProto::_internal_domain() const {
  return domain_.Get();
}
inline void ModelProto::_internal_set_domain(const std::string& value) {
  _has_bits_[0] |= 0x00000004u;
  domain_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelProto::_internal_mutable_domain() {
  _has_bits_[0] |= 0x00000004u;
  return domain_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelProto::release_domain() {
  // @@protoc_insertion_point(field_release:opencv_onnx.ModelProto.domain)
  if (!_internal_has_domain()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000004u;
  auto* p = domain_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (domain_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    domain_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void ModelProto::set_allocated_domain(std::string* domain) {
  if (domain != nullptr) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  domain_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), domain,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (domain_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    domain_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.ModelProto.domain)
}

// optional int64 model_version = 5;
inline bool ModelProto::_internal_has_model_version() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool ModelProto::has_model_version() const {
  return _internal_has_model_version();
}
inline void ModelProto::clear_model_version() {
  model_version_ = int64_t{0};
  _has_bits_[0] &= ~0x00000040u;
}
inline int64_t ModelProto::_internal_model_version() const {
  return model_version_;
}
inline int64_t ModelProto::model_version() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.ModelProto.model_version)
  return _internal_model_version();
}
inline void ModelProto::_internal_set_model_version(int64_t value) {
  _has_bits_[0] |= 0x00000040u;
  model_version_ = value;
}
inline void ModelProto::set_model_version(int64_t value) {
  _internal_set_model_version(value);
  // @@protoc_insertion_point(field_set:opencv_onnx.ModelProto.model_version)
}

// optional string doc_string = 6;
inline bool ModelProto::_internal_has_doc_string() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool ModelProto::has_doc_string() const {
  return _internal_has_doc_string();
}
inline void ModelProto::clear_doc_string() {
  doc_string_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000008u;
}
inline const std::string& ModelProto::doc_string() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.ModelProto.doc_string)
  return _internal_doc_string();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelProto::set_doc_string(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000008u;
 doc_string_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.ModelProto.doc_string)
}
inline std::string* ModelProto::mutable_doc_string() {
  std::string* _s = _internal_mutable_doc_string();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.ModelProto.doc_string)
  return _s;
}
inline const std::string& ModelProto::_internal_doc_string() const {
  return doc_string_.Get();
}
inline void ModelProto::_internal_set_doc_string(const std::string& value) {
  _has_bits_[0] |= 0x00000008u;
  doc_string_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelProto::_internal_mutable_doc_string() {
  _has_bits_[0] |= 0x00000008u;
  return doc_string_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelProto::release_doc_string() {
  // @@protoc_insertion_point(field_release:opencv_onnx.ModelProto.doc_string)
  if (!_internal_has_doc_string()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000008u;
  auto* p = doc_string_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (doc_string_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    doc_string_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void ModelProto::set_allocated_doc_string(std::string* doc_string) {
  if (doc_string != nullptr) {
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  doc_string_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), doc_string,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (doc_string_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    doc_string_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.ModelProto.doc_string)
}

// optional .opencv_onnx.GraphProto graph = 7;
inline bool ModelProto::_internal_has_graph() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  PROTOBUF_ASSUME(!value || graph_ != nullptr);
  return value;
}
inline bool ModelProto::has_graph() const {
  return _internal_has_graph();
}
inline void ModelProto::clear_graph() {
  if (graph_ != nullptr) graph_->Clear();
  _has_bits_[0] &= ~0x00000010u;
}
inline const ::opencv_onnx::GraphProto& ModelProto::_internal_graph() const {
  const ::opencv_onnx::GraphProto* p = graph_;
  return p != nullptr ? *p : reinterpret_cast<const ::opencv_onnx::GraphProto&>(
      ::opencv_onnx::_GraphProto_default_instance_);
}
inline const ::opencv_onnx::GraphProto& ModelProto::graph() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.ModelProto.graph)
  return _internal_graph();
}
inline void ModelProto::unsafe_arena_set_allocated_graph(
    ::opencv_onnx::GraphProto* graph) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_);
  }
  graph_ = graph;
  if (graph) {
    _has_bits_[0] |= 0x00000010u;
  } else {
    _has_bits_[0] &= ~0x00000010u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:opencv_onnx.ModelProto.graph)
}
inline ::opencv_onnx::GraphProto* ModelProto::release_graph() {
  _has_bits_[0] &= ~0x00000010u;
  ::opencv_onnx::GraphProto* temp = graph_;
  graph_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::opencv_onnx::GraphProto* ModelProto::unsafe_arena_release_graph() {
  // @@protoc_insertion_point(field_release:opencv_onnx.ModelProto.graph)
  _has_bits_[0] &= ~0x00000010u;
  ::opencv_onnx::GraphProto* temp = graph_;
  graph_ = nullptr;
  return temp;
}
inline ::opencv_onnx::GraphProto* ModelProto::_internal_mutable_graph() {
  _has_bits_[0] |= 0x00000010u;
  if (graph_ == nullptr) {
    auto* p = CreateMaybeMessage<::opencv_onnx::GraphProto>(GetArenaForAllocation());
    graph_ = p;
  }
  return graph_;
}
inline ::opencv_onnx::GraphProto* ModelProto::mutable_graph() {
  ::opencv_onnx::GraphProto* _msg = _internal_mutable_graph();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.ModelProto.graph)
  return _msg;
}
inline void ModelProto::set_allocated_graph(::opencv_onnx::GraphProto* graph) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete graph_;
  }
  if (graph) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::opencv_onnx::GraphProto>::GetOwningArena(graph);
    if (message_arena != submessage_arena) {
      graph = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, graph, submessage_arena);
    }
    _has_bits_[0] |= 0x00000010u;
  } else {
    _has_bits_[0] &= ~0x00000010u;
  }
  graph_ = graph;
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.ModelProto.graph)
}

// repeated .opencv_onnx.StringStringEntryProto metadata_props = 14;
inline int ModelProto::_internal_metadata_props_size() const {
  return metadata_props_.size();
}
inline int ModelProto::metadata_props_size() const {
  return _internal_metadata_props_size();
}
inline void ModelProto::clear_metadata_props() {
  metadata_props_.Clear();
}
inline ::opencv_onnx::StringStringEntryProto* ModelProto::mutable_metadata_props(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_onnx.ModelProto.metadata_props)
  return metadata_props_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::StringStringEntryProto >*
ModelProto::mutable_metadata_props() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.ModelProto.metadata_props)
  return &metadata_props_;
}
inline const ::opencv_onnx::StringStringEntryProto& ModelProto::_internal_metadata_props(int index) const {
  return metadata_props_.Get(index);
}
inline const ::opencv_onnx::StringStringEntryProto& ModelProto::metadata_props(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.ModelProto.metadata_props)
  return _internal_metadata_props(index);
}
inline ::opencv_onnx::StringStringEntryProto* ModelProto::_internal_add_metadata_props() {
  return metadata_props_.Add();
}
inline ::opencv_onnx::StringStringEntryProto* ModelProto::add_metadata_props() {
  ::opencv_onnx::StringStringEntryProto* _add = _internal_add_metadata_props();
  // @@protoc_insertion_point(field_add:opencv_onnx.ModelProto.metadata_props)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::StringStringEntryProto >&
ModelProto::metadata_props() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.ModelProto.metadata_props)
  return metadata_props_;
}

// -------------------------------------------------------------------

// StringStringEntryProto

// optional string key = 1;
inline bool StringStringEntryProto::_internal_has_key() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool StringStringEntryProto::has_key() const {
  return _internal_has_key();
}
inline void StringStringEntryProto::clear_key() {
  key_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& StringStringEntryProto::key() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.StringStringEntryProto.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StringStringEntryProto::set_key(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.StringStringEntryProto.key)
}
inline std::string* StringStringEntryProto::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.StringStringEntryProto.key)
  return _s;
}
inline const std::string& StringStringEntryProto::_internal_key() const {
  return key_.Get();
}
inline void StringStringEntryProto::_internal_set_key(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StringStringEntryProto::_internal_mutable_key() {
  _has_bits_[0] |= 0x00000001u;
  return key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StringStringEntryProto::release_key() {
  // @@protoc_insertion_point(field_release:opencv_onnx.StringStringEntryProto.key)
  if (!_internal_has_key()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = key_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (key_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void StringStringEntryProto::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), key,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (key_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.StringStringEntryProto.key)
}

// optional string value = 2;
inline bool StringStringEntryProto::_internal_has_value() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool StringStringEntryProto::has_value() const {
  return _internal_has_value();
}
inline void StringStringEntryProto::clear_value() {
  value_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& StringStringEntryProto::value() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.StringStringEntryProto.value)
  return _internal_value();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StringStringEntryProto::set_value(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000002u;
 value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.StringStringEntryProto.value)
}
inline std::string* StringStringEntryProto::mutable_value() {
  std::string* _s = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.StringStringEntryProto.value)
  return _s;
}
inline const std::string& StringStringEntryProto::_internal_value() const {
  return value_.Get();
}
inline void StringStringEntryProto::_internal_set_value(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StringStringEntryProto::_internal_mutable_value() {
  _has_bits_[0] |= 0x00000002u;
  return value_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StringStringEntryProto::release_value() {
  // @@protoc_insertion_point(field_release:opencv_onnx.StringStringEntryProto.value)
  if (!_internal_has_value()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  auto* p = value_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (value_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void StringStringEntryProto::set_allocated_value(std::string* value) {
  if (value != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  value_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (value_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.StringStringEntryProto.value)
}

// -------------------------------------------------------------------

// GraphProto

// repeated .opencv_onnx.NodeProto node = 1;
inline int GraphProto::_internal_node_size() const {
  return node_.size();
}
inline int GraphProto::node_size() const {
  return _internal_node_size();
}
inline void GraphProto::clear_node() {
  node_.Clear();
}
inline ::opencv_onnx::NodeProto* GraphProto::mutable_node(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_onnx.GraphProto.node)
  return node_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::NodeProto >*
GraphProto::mutable_node() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.GraphProto.node)
  return &node_;
}
inline const ::opencv_onnx::NodeProto& GraphProto::_internal_node(int index) const {
  return node_.Get(index);
}
inline const ::opencv_onnx::NodeProto& GraphProto::node(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.GraphProto.node)
  return _internal_node(index);
}
inline ::opencv_onnx::NodeProto* GraphProto::_internal_add_node() {
  return node_.Add();
}
inline ::opencv_onnx::NodeProto* GraphProto::add_node() {
  ::opencv_onnx::NodeProto* _add = _internal_add_node();
  // @@protoc_insertion_point(field_add:opencv_onnx.GraphProto.node)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::NodeProto >&
GraphProto::node() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.GraphProto.node)
  return node_;
}

// optional string name = 2;
inline bool GraphProto::_internal_has_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool GraphProto::has_name() const {
  return _internal_has_name();
}
inline void GraphProto::clear_name() {
  name_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& GraphProto::name() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.GraphProto.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphProto::set_name(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.GraphProto.name)
}
inline std::string* GraphProto::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.GraphProto.name)
  return _s;
}
inline const std::string& GraphProto::_internal_name() const {
  return name_.Get();
}
inline void GraphProto::_internal_set_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GraphProto::_internal_mutable_name() {
  _has_bits_[0] |= 0x00000001u;
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GraphProto::release_name() {
  // @@protoc_insertion_point(field_release:opencv_onnx.GraphProto.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = name_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void GraphProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.GraphProto.name)
}

// repeated .opencv_onnx.TensorProto initializer = 5;
inline int GraphProto::_internal_initializer_size() const {
  return initializer_.size();
}
inline int GraphProto::initializer_size() const {
  return _internal_initializer_size();
}
inline void GraphProto::clear_initializer() {
  initializer_.Clear();
}
inline ::opencv_onnx::TensorProto* GraphProto::mutable_initializer(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_onnx.GraphProto.initializer)
  return initializer_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::TensorProto >*
GraphProto::mutable_initializer() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.GraphProto.initializer)
  return &initializer_;
}
inline const ::opencv_onnx::TensorProto& GraphProto::_internal_initializer(int index) const {
  return initializer_.Get(index);
}
inline const ::opencv_onnx::TensorProto& GraphProto::initializer(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.GraphProto.initializer)
  return _internal_initializer(index);
}
inline ::opencv_onnx::TensorProto* GraphProto::_internal_add_initializer() {
  return initializer_.Add();
}
inline ::opencv_onnx::TensorProto* GraphProto::add_initializer() {
  ::opencv_onnx::TensorProto* _add = _internal_add_initializer();
  // @@protoc_insertion_point(field_add:opencv_onnx.GraphProto.initializer)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::TensorProto >&
GraphProto::initializer() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.GraphProto.initializer)
  return initializer_;
}

// optional string doc_string = 10;
inline bool GraphProto::_internal_has_doc_string() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool GraphProto::has_doc_string() const {
  return _internal_has_doc_string();
}
inline void GraphProto::clear_doc_string() {
  doc_string_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& GraphProto::doc_string() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.GraphProto.doc_string)
  return _internal_doc_string();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphProto::set_doc_string(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000002u;
 doc_string_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.GraphProto.doc_string)
}
inline std::string* GraphProto::mutable_doc_string() {
  std::string* _s = _internal_mutable_doc_string();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.GraphProto.doc_string)
  return _s;
}
inline const std::string& GraphProto::_internal_doc_string() const {
  return doc_string_.Get();
}
inline void GraphProto::_internal_set_doc_string(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  doc_string_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GraphProto::_internal_mutable_doc_string() {
  _has_bits_[0] |= 0x00000002u;
  return doc_string_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GraphProto::release_doc_string() {
  // @@protoc_insertion_point(field_release:opencv_onnx.GraphProto.doc_string)
  if (!_internal_has_doc_string()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  auto* p = doc_string_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (doc_string_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    doc_string_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void GraphProto::set_allocated_doc_string(std::string* doc_string) {
  if (doc_string != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  doc_string_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), doc_string,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (doc_string_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    doc_string_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.GraphProto.doc_string)
}

// repeated .opencv_onnx.ValueInfoProto input = 11;
inline int GraphProto::_internal_input_size() const {
  return input_.size();
}
inline int GraphProto::input_size() const {
  return _internal_input_size();
}
inline void GraphProto::clear_input() {
  input_.Clear();
}
inline ::opencv_onnx::ValueInfoProto* GraphProto::mutable_input(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_onnx.GraphProto.input)
  return input_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::ValueInfoProto >*
GraphProto::mutable_input() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.GraphProto.input)
  return &input_;
}
inline const ::opencv_onnx::ValueInfoProto& GraphProto::_internal_input(int index) const {
  return input_.Get(index);
}
inline const ::opencv_onnx::ValueInfoProto& GraphProto::input(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.GraphProto.input)
  return _internal_input(index);
}
inline ::opencv_onnx::ValueInfoProto* GraphProto::_internal_add_input() {
  return input_.Add();
}
inline ::opencv_onnx::ValueInfoProto* GraphProto::add_input() {
  ::opencv_onnx::ValueInfoProto* _add = _internal_add_input();
  // @@protoc_insertion_point(field_add:opencv_onnx.GraphProto.input)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::ValueInfoProto >&
GraphProto::input() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.GraphProto.input)
  return input_;
}

// repeated .opencv_onnx.ValueInfoProto output = 12;
inline int GraphProto::_internal_output_size() const {
  return output_.size();
}
inline int GraphProto::output_size() const {
  return _internal_output_size();
}
inline void GraphProto::clear_output() {
  output_.Clear();
}
inline ::opencv_onnx::ValueInfoProto* GraphProto::mutable_output(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_onnx.GraphProto.output)
  return output_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::ValueInfoProto >*
GraphProto::mutable_output() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.GraphProto.output)
  return &output_;
}
inline const ::opencv_onnx::ValueInfoProto& GraphProto::_internal_output(int index) const {
  return output_.Get(index);
}
inline const ::opencv_onnx::ValueInfoProto& GraphProto::output(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.GraphProto.output)
  return _internal_output(index);
}
inline ::opencv_onnx::ValueInfoProto* GraphProto::_internal_add_output() {
  return output_.Add();
}
inline ::opencv_onnx::ValueInfoProto* GraphProto::add_output() {
  ::opencv_onnx::ValueInfoProto* _add = _internal_add_output();
  // @@protoc_insertion_point(field_add:opencv_onnx.GraphProto.output)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::ValueInfoProto >&
GraphProto::output() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.GraphProto.output)
  return output_;
}

// repeated .opencv_onnx.ValueInfoProto value_info = 13;
inline int GraphProto::_internal_value_info_size() const {
  return value_info_.size();
}
inline int GraphProto::value_info_size() const {
  return _internal_value_info_size();
}
inline void GraphProto::clear_value_info() {
  value_info_.Clear();
}
inline ::opencv_onnx::ValueInfoProto* GraphProto::mutable_value_info(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_onnx.GraphProto.value_info)
  return value_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::ValueInfoProto >*
GraphProto::mutable_value_info() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.GraphProto.value_info)
  return &value_info_;
}
inline const ::opencv_onnx::ValueInfoProto& GraphProto::_internal_value_info(int index) const {
  return value_info_.Get(index);
}
inline const ::opencv_onnx::ValueInfoProto& GraphProto::value_info(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.GraphProto.value_info)
  return _internal_value_info(index);
}
inline ::opencv_onnx::ValueInfoProto* GraphProto::_internal_add_value_info() {
  return value_info_.Add();
}
inline ::opencv_onnx::ValueInfoProto* GraphProto::add_value_info() {
  ::opencv_onnx::ValueInfoProto* _add = _internal_add_value_info();
  // @@protoc_insertion_point(field_add:opencv_onnx.GraphProto.value_info)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::ValueInfoProto >&
GraphProto::value_info() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.GraphProto.value_info)
  return value_info_;
}

// -------------------------------------------------------------------

// TensorProto_Segment

// optional int64 begin = 1;
inline bool TensorProto_Segment::_internal_has_begin() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TensorProto_Segment::has_begin() const {
  return _internal_has_begin();
}
inline void TensorProto_Segment::clear_begin() {
  begin_ = int64_t{0};
  _has_bits_[0] &= ~0x00000001u;
}
inline int64_t TensorProto_Segment::_internal_begin() const {
  return begin_;
}
inline int64_t TensorProto_Segment::begin() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorProto.Segment.begin)
  return _internal_begin();
}
inline void TensorProto_Segment::_internal_set_begin(int64_t value) {
  _has_bits_[0] |= 0x00000001u;
  begin_ = value;
}
inline void TensorProto_Segment::set_begin(int64_t value) {
  _internal_set_begin(value);
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorProto.Segment.begin)
}

// optional int64 end = 2;
inline bool TensorProto_Segment::_internal_has_end() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TensorProto_Segment::has_end() const {
  return _internal_has_end();
}
inline void TensorProto_Segment::clear_end() {
  end_ = int64_t{0};
  _has_bits_[0] &= ~0x00000002u;
}
inline int64_t TensorProto_Segment::_internal_end() const {
  return end_;
}
inline int64_t TensorProto_Segment::end() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorProto.Segment.end)
  return _internal_end();
}
inline void TensorProto_Segment::_internal_set_end(int64_t value) {
  _has_bits_[0] |= 0x00000002u;
  end_ = value;
}
inline void TensorProto_Segment::set_end(int64_t value) {
  _internal_set_end(value);
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorProto.Segment.end)
}

// -------------------------------------------------------------------

// TensorProto

// repeated int64 dims = 1;
inline int TensorProto::_internal_dims_size() const {
  return dims_.size();
}
inline int TensorProto::dims_size() const {
  return _internal_dims_size();
}
inline void TensorProto::clear_dims() {
  dims_.Clear();
}
inline int64_t TensorProto::_internal_dims(int index) const {
  return dims_.Get(index);
}
inline int64_t TensorProto::dims(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorProto.dims)
  return _internal_dims(index);
}
inline void TensorProto::set_dims(int index, int64_t value) {
  dims_.Set(index, value);
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorProto.dims)
}
inline void TensorProto::_internal_add_dims(int64_t value) {
  dims_.Add(value);
}
inline void TensorProto::add_dims(int64_t value) {
  _internal_add_dims(value);
  // @@protoc_insertion_point(field_add:opencv_onnx.TensorProto.dims)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TensorProto::_internal_dims() const {
  return dims_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TensorProto::dims() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.TensorProto.dims)
  return _internal_dims();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TensorProto::_internal_mutable_dims() {
  return &dims_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TensorProto::mutable_dims() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.TensorProto.dims)
  return _internal_mutable_dims();
}

// optional .opencv_onnx.TensorProto.DataType data_type = 2;
inline bool TensorProto::_internal_has_data_type() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool TensorProto::has_data_type() const {
  return _internal_has_data_type();
}
inline void TensorProto::clear_data_type() {
  data_type_ = 0;
  _has_bits_[0] &= ~0x00000010u;
}
inline ::opencv_onnx::TensorProto_DataType TensorProto::_internal_data_type() const {
  return static_cast< ::opencv_onnx::TensorProto_DataType >(data_type_);
}
inline ::opencv_onnx::TensorProto_DataType TensorProto::data_type() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorProto.data_type)
  return _internal_data_type();
}
inline void TensorProto::_internal_set_data_type(::opencv_onnx::TensorProto_DataType value) {
  assert(::opencv_onnx::TensorProto_DataType_IsValid(value));
  _has_bits_[0] |= 0x00000010u;
  data_type_ = value;
}
inline void TensorProto::set_data_type(::opencv_onnx::TensorProto_DataType value) {
  _internal_set_data_type(value);
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorProto.data_type)
}

// optional .opencv_onnx.TensorProto.Segment segment = 3;
inline bool TensorProto::_internal_has_segment() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  PROTOBUF_ASSUME(!value || segment_ != nullptr);
  return value;
}
inline bool TensorProto::has_segment() const {
  return _internal_has_segment();
}
inline void TensorProto::clear_segment() {
  if (segment_ != nullptr) segment_->Clear();
  _has_bits_[0] &= ~0x00000008u;
}
inline const ::opencv_onnx::TensorProto_Segment& TensorProto::_internal_segment() const {
  const ::opencv_onnx::TensorProto_Segment* p = segment_;
  return p != nullptr ? *p : reinterpret_cast<const ::opencv_onnx::TensorProto_Segment&>(
      ::opencv_onnx::_TensorProto_Segment_default_instance_);
}
inline const ::opencv_onnx::TensorProto_Segment& TensorProto::segment() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorProto.segment)
  return _internal_segment();
}
inline void TensorProto::unsafe_arena_set_allocated_segment(
    ::opencv_onnx::TensorProto_Segment* segment) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(segment_);
  }
  segment_ = segment;
  if (segment) {
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:opencv_onnx.TensorProto.segment)
}
inline ::opencv_onnx::TensorProto_Segment* TensorProto::release_segment() {
  _has_bits_[0] &= ~0x00000008u;
  ::opencv_onnx::TensorProto_Segment* temp = segment_;
  segment_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::opencv_onnx::TensorProto_Segment* TensorProto::unsafe_arena_release_segment() {
  // @@protoc_insertion_point(field_release:opencv_onnx.TensorProto.segment)
  _has_bits_[0] &= ~0x00000008u;
  ::opencv_onnx::TensorProto_Segment* temp = segment_;
  segment_ = nullptr;
  return temp;
}
inline ::opencv_onnx::TensorProto_Segment* TensorProto::_internal_mutable_segment() {
  _has_bits_[0] |= 0x00000008u;
  if (segment_ == nullptr) {
    auto* p = CreateMaybeMessage<::opencv_onnx::TensorProto_Segment>(GetArenaForAllocation());
    segment_ = p;
  }
  return segment_;
}
inline ::opencv_onnx::TensorProto_Segment* TensorProto::mutable_segment() {
  ::opencv_onnx::TensorProto_Segment* _msg = _internal_mutable_segment();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.TensorProto.segment)
  return _msg;
}
inline void TensorProto::set_allocated_segment(::opencv_onnx::TensorProto_Segment* segment) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete segment_;
  }
  if (segment) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::opencv_onnx::TensorProto_Segment>::GetOwningArena(segment);
    if (message_arena != submessage_arena) {
      segment = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, segment, submessage_arena);
    }
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  segment_ = segment;
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.TensorProto.segment)
}

// repeated float float_data = 4 [packed = true];
inline int TensorProto::_internal_float_data_size() const {
  return float_data_.size();
}
inline int TensorProto::float_data_size() const {
  return _internal_float_data_size();
}
inline void TensorProto::clear_float_data() {
  float_data_.Clear();
}
inline float TensorProto::_internal_float_data(int index) const {
  return float_data_.Get(index);
}
inline float TensorProto::float_data(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorProto.float_data)
  return _internal_float_data(index);
}
inline void TensorProto::set_float_data(int index, float value) {
  float_data_.Set(index, value);
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorProto.float_data)
}
inline void TensorProto::_internal_add_float_data(float value) {
  float_data_.Add(value);
}
inline void TensorProto::add_float_data(float value) {
  _internal_add_float_data(value);
  // @@protoc_insertion_point(field_add:opencv_onnx.TensorProto.float_data)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
TensorProto::_internal_float_data() const {
  return float_data_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
TensorProto::float_data() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.TensorProto.float_data)
  return _internal_float_data();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
TensorProto::_internal_mutable_float_data() {
  return &float_data_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
TensorProto::mutable_float_data() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.TensorProto.float_data)
  return _internal_mutable_float_data();
}

// repeated int32 int32_data = 5 [packed = true];
inline int TensorProto::_internal_int32_data_size() const {
  return int32_data_.size();
}
inline int TensorProto::int32_data_size() const {
  return _internal_int32_data_size();
}
inline void TensorProto::clear_int32_data() {
  int32_data_.Clear();
}
inline int32_t TensorProto::_internal_int32_data(int index) const {
  return int32_data_.Get(index);
}
inline int32_t TensorProto::int32_data(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorProto.int32_data)
  return _internal_int32_data(index);
}
inline void TensorProto::set_int32_data(int index, int32_t value) {
  int32_data_.Set(index, value);
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorProto.int32_data)
}
inline void TensorProto::_internal_add_int32_data(int32_t value) {
  int32_data_.Add(value);
}
inline void TensorProto::add_int32_data(int32_t value) {
  _internal_add_int32_data(value);
  // @@protoc_insertion_point(field_add:opencv_onnx.TensorProto.int32_data)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
TensorProto::_internal_int32_data() const {
  return int32_data_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
TensorProto::int32_data() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.TensorProto.int32_data)
  return _internal_int32_data();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
TensorProto::_internal_mutable_int32_data() {
  return &int32_data_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
TensorProto::mutable_int32_data() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.TensorProto.int32_data)
  return _internal_mutable_int32_data();
}

// repeated bytes string_data = 6;
inline int TensorProto::_internal_string_data_size() const {
  return string_data_.size();
}
inline int TensorProto::string_data_size() const {
  return _internal_string_data_size();
}
inline void TensorProto::clear_string_data() {
  string_data_.Clear();
}
inline std::string* TensorProto::add_string_data() {
  std::string* _s = _internal_add_string_data();
  // @@protoc_insertion_point(field_add_mutable:opencv_onnx.TensorProto.string_data)
  return _s;
}
inline const std::string& TensorProto::_internal_string_data(int index) const {
  return string_data_.Get(index);
}
inline const std::string& TensorProto::string_data(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorProto.string_data)
  return _internal_string_data(index);
}
inline std::string* TensorProto::mutable_string_data(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_onnx.TensorProto.string_data)
  return string_data_.Mutable(index);
}
inline void TensorProto::set_string_data(int index, const std::string& value) {
  string_data_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorProto.string_data)
}
inline void TensorProto::set_string_data(int index, std::string&& value) {
  string_data_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorProto.string_data)
}
inline void TensorProto::set_string_data(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  string_data_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:opencv_onnx.TensorProto.string_data)
}
inline void TensorProto::set_string_data(int index, const void* value, size_t size) {
  string_data_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:opencv_onnx.TensorProto.string_data)
}
inline std::string* TensorProto::_internal_add_string_data() {
  return string_data_.Add();
}
inline void TensorProto::add_string_data(const std::string& value) {
  string_data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:opencv_onnx.TensorProto.string_data)
}
inline void TensorProto::add_string_data(std::string&& value) {
  string_data_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:opencv_onnx.TensorProto.string_data)
}
inline void TensorProto::add_string_data(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  string_data_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:opencv_onnx.TensorProto.string_data)
}
inline void TensorProto::add_string_data(const void* value, size_t size) {
  string_data_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:opencv_onnx.TensorProto.string_data)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
TensorProto::string_data() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.TensorProto.string_data)
  return string_data_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
TensorProto::mutable_string_data() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.TensorProto.string_data)
  return &string_data_;
}

// repeated int64 int64_data = 7 [packed = true];
inline int TensorProto::_internal_int64_data_size() const {
  return int64_data_.size();
}
inline int TensorProto::int64_data_size() const {
  return _internal_int64_data_size();
}
inline void TensorProto::clear_int64_data() {
  int64_data_.Clear();
}
inline int64_t TensorProto::_internal_int64_data(int index) const {
  return int64_data_.Get(index);
}
inline int64_t TensorProto::int64_data(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorProto.int64_data)
  return _internal_int64_data(index);
}
inline void TensorProto::set_int64_data(int index, int64_t value) {
  int64_data_.Set(index, value);
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorProto.int64_data)
}
inline void TensorProto::_internal_add_int64_data(int64_t value) {
  int64_data_.Add(value);
}
inline void TensorProto::add_int64_data(int64_t value) {
  _internal_add_int64_data(value);
  // @@protoc_insertion_point(field_add:opencv_onnx.TensorProto.int64_data)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TensorProto::_internal_int64_data() const {
  return int64_data_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TensorProto::int64_data() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.TensorProto.int64_data)
  return _internal_int64_data();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TensorProto::_internal_mutable_int64_data() {
  return &int64_data_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TensorProto::mutable_int64_data() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.TensorProto.int64_data)
  return _internal_mutable_int64_data();
}

// optional string name = 8;
inline bool TensorProto::_internal_has_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TensorProto::has_name() const {
  return _internal_has_name();
}
inline void TensorProto::clear_name() {
  name_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& TensorProto::name() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorProto.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TensorProto::set_name(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorProto.name)
}
inline std::string* TensorProto::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.TensorProto.name)
  return _s;
}
inline const std::string& TensorProto::_internal_name() const {
  return name_.Get();
}
inline void TensorProto::_internal_set_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TensorProto::_internal_mutable_name() {
  _has_bits_[0] |= 0x00000001u;
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TensorProto::release_name() {
  // @@protoc_insertion_point(field_release:opencv_onnx.TensorProto.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = name_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void TensorProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.TensorProto.name)
}

// optional string doc_string = 12;
inline bool TensorProto::_internal_has_doc_string() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool TensorProto::has_doc_string() const {
  return _internal_has_doc_string();
}
inline void TensorProto::clear_doc_string() {
  doc_string_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000004u;
}
inline const std::string& TensorProto::doc_string() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorProto.doc_string)
  return _internal_doc_string();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TensorProto::set_doc_string(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000004u;
 doc_string_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorProto.doc_string)
}
inline std::string* TensorProto::mutable_doc_string() {
  std::string* _s = _internal_mutable_doc_string();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.TensorProto.doc_string)
  return _s;
}
inline const std::string& TensorProto::_internal_doc_string() const {
  return doc_string_.Get();
}
inline void TensorProto::_internal_set_doc_string(const std::string& value) {
  _has_bits_[0] |= 0x00000004u;
  doc_string_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TensorProto::_internal_mutable_doc_string() {
  _has_bits_[0] |= 0x00000004u;
  return doc_string_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TensorProto::release_doc_string() {
  // @@protoc_insertion_point(field_release:opencv_onnx.TensorProto.doc_string)
  if (!_internal_has_doc_string()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000004u;
  auto* p = doc_string_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (doc_string_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    doc_string_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void TensorProto::set_allocated_doc_string(std::string* doc_string) {
  if (doc_string != nullptr) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  doc_string_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), doc_string,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (doc_string_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    doc_string_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.TensorProto.doc_string)
}

// optional bytes raw_data = 9;
inline bool TensorProto::_internal_has_raw_data() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TensorProto::has_raw_data() const {
  return _internal_has_raw_data();
}
inline void TensorProto::clear_raw_data() {
  raw_data_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& TensorProto::raw_data() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorProto.raw_data)
  return _internal_raw_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TensorProto::set_raw_data(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000002u;
 raw_data_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorProto.raw_data)
}
inline std::string* TensorProto::mutable_raw_data() {
  std::string* _s = _internal_mutable_raw_data();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.TensorProto.raw_data)
  return _s;
}
inline const std::string& TensorProto::_internal_raw_data() const {
  return raw_data_.Get();
}
inline void TensorProto::_internal_set_raw_data(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  raw_data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TensorProto::_internal_mutable_raw_data() {
  _has_bits_[0] |= 0x00000002u;
  return raw_data_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TensorProto::release_raw_data() {
  // @@protoc_insertion_point(field_release:opencv_onnx.TensorProto.raw_data)
  if (!_internal_has_raw_data()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  auto* p = raw_data_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (raw_data_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    raw_data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void TensorProto::set_allocated_raw_data(std::string* raw_data) {
  if (raw_data != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  raw_data_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), raw_data,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (raw_data_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    raw_data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.TensorProto.raw_data)
}

// repeated double double_data = 10 [packed = true];
inline int TensorProto::_internal_double_data_size() const {
  return double_data_.size();
}
inline int TensorProto::double_data_size() const {
  return _internal_double_data_size();
}
inline void TensorProto::clear_double_data() {
  double_data_.Clear();
}
inline double TensorProto::_internal_double_data(int index) const {
  return double_data_.Get(index);
}
inline double TensorProto::double_data(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorProto.double_data)
  return _internal_double_data(index);
}
inline void TensorProto::set_double_data(int index, double value) {
  double_data_.Set(index, value);
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorProto.double_data)
}
inline void TensorProto::_internal_add_double_data(double value) {
  double_data_.Add(value);
}
inline void TensorProto::add_double_data(double value) {
  _internal_add_double_data(value);
  // @@protoc_insertion_point(field_add:opencv_onnx.TensorProto.double_data)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
TensorProto::_internal_double_data() const {
  return double_data_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
TensorProto::double_data() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.TensorProto.double_data)
  return _internal_double_data();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
TensorProto::_internal_mutable_double_data() {
  return &double_data_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
TensorProto::mutable_double_data() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.TensorProto.double_data)
  return _internal_mutable_double_data();
}

// repeated uint64 uint64_data = 11 [packed = true];
inline int TensorProto::_internal_uint64_data_size() const {
  return uint64_data_.size();
}
inline int TensorProto::uint64_data_size() const {
  return _internal_uint64_data_size();
}
inline void TensorProto::clear_uint64_data() {
  uint64_data_.Clear();
}
inline uint64_t TensorProto::_internal_uint64_data(int index) const {
  return uint64_data_.Get(index);
}
inline uint64_t TensorProto::uint64_data(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorProto.uint64_data)
  return _internal_uint64_data(index);
}
inline void TensorProto::set_uint64_data(int index, uint64_t value) {
  uint64_data_.Set(index, value);
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorProto.uint64_data)
}
inline void TensorProto::_internal_add_uint64_data(uint64_t value) {
  uint64_data_.Add(value);
}
inline void TensorProto::add_uint64_data(uint64_t value) {
  _internal_add_uint64_data(value);
  // @@protoc_insertion_point(field_add:opencv_onnx.TensorProto.uint64_data)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
TensorProto::_internal_uint64_data() const {
  return uint64_data_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
TensorProto::uint64_data() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.TensorProto.uint64_data)
  return _internal_uint64_data();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
TensorProto::_internal_mutable_uint64_data() {
  return &uint64_data_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
TensorProto::mutable_uint64_data() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.TensorProto.uint64_data)
  return _internal_mutable_uint64_data();
}

// -------------------------------------------------------------------

// TensorShapeProto_Dimension

// int64 dim_value = 1;
inline bool TensorShapeProto_Dimension::_internal_has_dim_value() const {
  return value_case() == kDimValue;
}
inline bool TensorShapeProto_Dimension::has_dim_value() const {
  return _internal_has_dim_value();
}
inline void TensorShapeProto_Dimension::set_has_dim_value() {
  _oneof_case_[0] = kDimValue;
}
inline void TensorShapeProto_Dimension::clear_dim_value() {
  if (_internal_has_dim_value()) {
    value_.dim_value_ = int64_t{0};
    clear_has_value();
  }
}
inline int64_t TensorShapeProto_Dimension::_internal_dim_value() const {
  if (_internal_has_dim_value()) {
    return value_.dim_value_;
  }
  return int64_t{0};
}
inline void TensorShapeProto_Dimension::_internal_set_dim_value(int64_t value) {
  if (!_internal_has_dim_value()) {
    clear_value();
    set_has_dim_value();
  }
  value_.dim_value_ = value;
}
inline int64_t TensorShapeProto_Dimension::dim_value() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorShapeProto.Dimension.dim_value)
  return _internal_dim_value();
}
inline void TensorShapeProto_Dimension::set_dim_value(int64_t value) {
  _internal_set_dim_value(value);
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorShapeProto.Dimension.dim_value)
}

// string dim_param = 2;
inline bool TensorShapeProto_Dimension::_internal_has_dim_param() const {
  return value_case() == kDimParam;
}
inline bool TensorShapeProto_Dimension::has_dim_param() const {
  return _internal_has_dim_param();
}
inline void TensorShapeProto_Dimension::set_has_dim_param() {
  _oneof_case_[0] = kDimParam;
}
inline void TensorShapeProto_Dimension::clear_dim_param() {
  if (_internal_has_dim_param()) {
    value_.dim_param_.Destroy(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
    clear_has_value();
  }
}
inline const std::string& TensorShapeProto_Dimension::dim_param() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorShapeProto.Dimension.dim_param)
  return _internal_dim_param();
}
template <typename ArgT0, typename... ArgT>
inline void TensorShapeProto_Dimension::set_dim_param(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_dim_param()) {
    clear_value();
    set_has_dim_param();
    value_.dim_param_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.dim_param_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorShapeProto.Dimension.dim_param)
}
inline std::string* TensorShapeProto_Dimension::mutable_dim_param() {
  std::string* _s = _internal_mutable_dim_param();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.TensorShapeProto.Dimension.dim_param)
  return _s;
}
inline const std::string& TensorShapeProto_Dimension::_internal_dim_param() const {
  if (_internal_has_dim_param()) {
    return value_.dim_param_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void TensorShapeProto_Dimension::_internal_set_dim_param(const std::string& value) {
  if (!_internal_has_dim_param()) {
    clear_value();
    set_has_dim_param();
    value_.dim_param_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.dim_param_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TensorShapeProto_Dimension::_internal_mutable_dim_param() {
  if (!_internal_has_dim_param()) {
    clear_value();
    set_has_dim_param();
    value_.dim_param_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return value_.dim_param_.Mutable(
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TensorShapeProto_Dimension::release_dim_param() {
  // @@protoc_insertion_point(field_release:opencv_onnx.TensorShapeProto.Dimension.dim_param)
  if (_internal_has_dim_param()) {
    clear_has_value();
    return value_.dim_param_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
  } else {
    return nullptr;
  }
}
inline void TensorShapeProto_Dimension::set_allocated_dim_param(std::string* dim_param) {
  if (has_value()) {
    clear_value();
  }
  if (dim_param != nullptr) {
    set_has_dim_param();
    value_.dim_param_.UnsafeSetDefault(dim_param);
    ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaForAllocation();
    if (arena != nullptr) {
      arena->Own(dim_param);
    }
  }
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.TensorShapeProto.Dimension.dim_param)
}

// optional string denotation = 3;
inline bool TensorShapeProto_Dimension::_internal_has_denotation() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TensorShapeProto_Dimension::has_denotation() const {
  return _internal_has_denotation();
}
inline void TensorShapeProto_Dimension::clear_denotation() {
  denotation_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& TensorShapeProto_Dimension::denotation() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorShapeProto.Dimension.denotation)
  return _internal_denotation();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TensorShapeProto_Dimension::set_denotation(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 denotation_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.TensorShapeProto.Dimension.denotation)
}
inline std::string* TensorShapeProto_Dimension::mutable_denotation() {
  std::string* _s = _internal_mutable_denotation();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.TensorShapeProto.Dimension.denotation)
  return _s;
}
inline const std::string& TensorShapeProto_Dimension::_internal_denotation() const {
  return denotation_.Get();
}
inline void TensorShapeProto_Dimension::_internal_set_denotation(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  denotation_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TensorShapeProto_Dimension::_internal_mutable_denotation() {
  _has_bits_[0] |= 0x00000001u;
  return denotation_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TensorShapeProto_Dimension::release_denotation() {
  // @@protoc_insertion_point(field_release:opencv_onnx.TensorShapeProto.Dimension.denotation)
  if (!_internal_has_denotation()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = denotation_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (denotation_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    denotation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void TensorShapeProto_Dimension::set_allocated_denotation(std::string* denotation) {
  if (denotation != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  denotation_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), denotation,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (denotation_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    denotation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.TensorShapeProto.Dimension.denotation)
}

inline bool TensorShapeProto_Dimension::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void TensorShapeProto_Dimension::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline TensorShapeProto_Dimension::ValueCase TensorShapeProto_Dimension::value_case() const {
  return TensorShapeProto_Dimension::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// TensorShapeProto

// repeated .opencv_onnx.TensorShapeProto.Dimension dim = 1;
inline int TensorShapeProto::_internal_dim_size() const {
  return dim_.size();
}
inline int TensorShapeProto::dim_size() const {
  return _internal_dim_size();
}
inline void TensorShapeProto::clear_dim() {
  dim_.Clear();
}
inline ::opencv_onnx::TensorShapeProto_Dimension* TensorShapeProto::mutable_dim(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_onnx.TensorShapeProto.dim)
  return dim_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::TensorShapeProto_Dimension >*
TensorShapeProto::mutable_dim() {
  // @@protoc_insertion_point(field_mutable_list:opencv_onnx.TensorShapeProto.dim)
  return &dim_;
}
inline const ::opencv_onnx::TensorShapeProto_Dimension& TensorShapeProto::_internal_dim(int index) const {
  return dim_.Get(index);
}
inline const ::opencv_onnx::TensorShapeProto_Dimension& TensorShapeProto::dim(int index) const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TensorShapeProto.dim)
  return _internal_dim(index);
}
inline ::opencv_onnx::TensorShapeProto_Dimension* TensorShapeProto::_internal_add_dim() {
  return dim_.Add();
}
inline ::opencv_onnx::TensorShapeProto_Dimension* TensorShapeProto::add_dim() {
  ::opencv_onnx::TensorShapeProto_Dimension* _add = _internal_add_dim();
  // @@protoc_insertion_point(field_add:opencv_onnx.TensorShapeProto.dim)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_onnx::TensorShapeProto_Dimension >&
TensorShapeProto::dim() const {
  // @@protoc_insertion_point(field_list:opencv_onnx.TensorShapeProto.dim)
  return dim_;
}

// -------------------------------------------------------------------

// TypeProto_Tensor

// optional .opencv_onnx.TensorProto.DataType elem_type = 1;
inline bool TypeProto_Tensor::_internal_has_elem_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TypeProto_Tensor::has_elem_type() const {
  return _internal_has_elem_type();
}
inline void TypeProto_Tensor::clear_elem_type() {
  elem_type_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::opencv_onnx::TensorProto_DataType TypeProto_Tensor::_internal_elem_type() const {
  return static_cast< ::opencv_onnx::TensorProto_DataType >(elem_type_);
}
inline ::opencv_onnx::TensorProto_DataType TypeProto_Tensor::elem_type() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TypeProto.Tensor.elem_type)
  return _internal_elem_type();
}
inline void TypeProto_Tensor::_internal_set_elem_type(::opencv_onnx::TensorProto_DataType value) {
  assert(::opencv_onnx::TensorProto_DataType_IsValid(value));
  _has_bits_[0] |= 0x00000002u;
  elem_type_ = value;
}
inline void TypeProto_Tensor::set_elem_type(::opencv_onnx::TensorProto_DataType value) {
  _internal_set_elem_type(value);
  // @@protoc_insertion_point(field_set:opencv_onnx.TypeProto.Tensor.elem_type)
}

// optional .opencv_onnx.TensorShapeProto shape = 2;
inline bool TypeProto_Tensor::_internal_has_shape() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || shape_ != nullptr);
  return value;
}
inline bool TypeProto_Tensor::has_shape() const {
  return _internal_has_shape();
}
inline void TypeProto_Tensor::clear_shape() {
  if (shape_ != nullptr) shape_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::opencv_onnx::TensorShapeProto& TypeProto_Tensor::_internal_shape() const {
  const ::opencv_onnx::TensorShapeProto* p = shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::opencv_onnx::TensorShapeProto&>(
      ::opencv_onnx::_TensorShapeProto_default_instance_);
}
inline const ::opencv_onnx::TensorShapeProto& TypeProto_Tensor::shape() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TypeProto.Tensor.shape)
  return _internal_shape();
}
inline void TypeProto_Tensor::unsafe_arena_set_allocated_shape(
    ::opencv_onnx::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_);
  }
  shape_ = shape;
  if (shape) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:opencv_onnx.TypeProto.Tensor.shape)
}
inline ::opencv_onnx::TensorShapeProto* TypeProto_Tensor::release_shape() {
  _has_bits_[0] &= ~0x00000001u;
  ::opencv_onnx::TensorShapeProto* temp = shape_;
  shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::opencv_onnx::TensorShapeProto* TypeProto_Tensor::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:opencv_onnx.TypeProto.Tensor.shape)
  _has_bits_[0] &= ~0x00000001u;
  ::opencv_onnx::TensorShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::opencv_onnx::TensorShapeProto* TypeProto_Tensor::_internal_mutable_shape() {
  _has_bits_[0] |= 0x00000001u;
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::opencv_onnx::TensorShapeProto>(GetArenaForAllocation());
    shape_ = p;
  }
  return shape_;
}
inline ::opencv_onnx::TensorShapeProto* TypeProto_Tensor::mutable_shape() {
  ::opencv_onnx::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.TypeProto.Tensor.shape)
  return _msg;
}
inline void TypeProto_Tensor::set_allocated_shape(::opencv_onnx::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete shape_;
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::opencv_onnx::TensorShapeProto>::GetOwningArena(shape);
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.TypeProto.Tensor.shape)
}

// -------------------------------------------------------------------

// TypeProto

// .opencv_onnx.TypeProto.Tensor tensor_type = 1;
inline bool TypeProto::_internal_has_tensor_type() const {
  return value_case() == kTensorType;
}
inline bool TypeProto::has_tensor_type() const {
  return _internal_has_tensor_type();
}
inline void TypeProto::set_has_tensor_type() {
  _oneof_case_[0] = kTensorType;
}
inline void TypeProto::clear_tensor_type() {
  if (_internal_has_tensor_type()) {
    if (GetArenaForAllocation() == nullptr) {
      delete value_.tensor_type_;
    }
    clear_has_value();
  }
}
inline ::opencv_onnx::TypeProto_Tensor* TypeProto::release_tensor_type() {
  // @@protoc_insertion_point(field_release:opencv_onnx.TypeProto.tensor_type)
  if (_internal_has_tensor_type()) {
    clear_has_value();
      ::opencv_onnx::TypeProto_Tensor* temp = value_.tensor_type_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.tensor_type_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::opencv_onnx::TypeProto_Tensor& TypeProto::_internal_tensor_type() const {
  return _internal_has_tensor_type()
      ? *value_.tensor_type_
      : reinterpret_cast< ::opencv_onnx::TypeProto_Tensor&>(::opencv_onnx::_TypeProto_Tensor_default_instance_);
}
inline const ::opencv_onnx::TypeProto_Tensor& TypeProto::tensor_type() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TypeProto.tensor_type)
  return _internal_tensor_type();
}
inline ::opencv_onnx::TypeProto_Tensor* TypeProto::unsafe_arena_release_tensor_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:opencv_onnx.TypeProto.tensor_type)
  if (_internal_has_tensor_type()) {
    clear_has_value();
    ::opencv_onnx::TypeProto_Tensor* temp = value_.tensor_type_;
    value_.tensor_type_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void TypeProto::unsafe_arena_set_allocated_tensor_type(::opencv_onnx::TypeProto_Tensor* tensor_type) {
  clear_value();
  if (tensor_type) {
    set_has_tensor_type();
    value_.tensor_type_ = tensor_type;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:opencv_onnx.TypeProto.tensor_type)
}
inline ::opencv_onnx::TypeProto_Tensor* TypeProto::_internal_mutable_tensor_type() {
  if (!_internal_has_tensor_type()) {
    clear_value();
    set_has_tensor_type();
    value_.tensor_type_ = CreateMaybeMessage< ::opencv_onnx::TypeProto_Tensor >(GetArenaForAllocation());
  }
  return value_.tensor_type_;
}
inline ::opencv_onnx::TypeProto_Tensor* TypeProto::mutable_tensor_type() {
  ::opencv_onnx::TypeProto_Tensor* _msg = _internal_mutable_tensor_type();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.TypeProto.tensor_type)
  return _msg;
}

// optional string denotation = 6;
inline bool TypeProto::_internal_has_denotation() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TypeProto::has_denotation() const {
  return _internal_has_denotation();
}
inline void TypeProto::clear_denotation() {
  denotation_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& TypeProto::denotation() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.TypeProto.denotation)
  return _internal_denotation();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TypeProto::set_denotation(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 denotation_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.TypeProto.denotation)
}
inline std::string* TypeProto::mutable_denotation() {
  std::string* _s = _internal_mutable_denotation();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.TypeProto.denotation)
  return _s;
}
inline const std::string& TypeProto::_internal_denotation() const {
  return denotation_.Get();
}
inline void TypeProto::_internal_set_denotation(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  denotation_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TypeProto::_internal_mutable_denotation() {
  _has_bits_[0] |= 0x00000001u;
  return denotation_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TypeProto::release_denotation() {
  // @@protoc_insertion_point(field_release:opencv_onnx.TypeProto.denotation)
  if (!_internal_has_denotation()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = denotation_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (denotation_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    denotation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void TypeProto::set_allocated_denotation(std::string* denotation) {
  if (denotation != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  denotation_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), denotation,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (denotation_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    denotation_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.TypeProto.denotation)
}

inline bool TypeProto::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void TypeProto::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline TypeProto::ValueCase TypeProto::value_case() const {
  return TypeProto::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// OperatorSetIdProto

// optional string domain = 1;
inline bool OperatorSetIdProto::_internal_has_domain() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool OperatorSetIdProto::has_domain() const {
  return _internal_has_domain();
}
inline void OperatorSetIdProto::clear_domain() {
  domain_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& OperatorSetIdProto::domain() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.OperatorSetIdProto.domain)
  return _internal_domain();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OperatorSetIdProto::set_domain(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 domain_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_onnx.OperatorSetIdProto.domain)
}
inline std::string* OperatorSetIdProto::mutable_domain() {
  std::string* _s = _internal_mutable_domain();
  // @@protoc_insertion_point(field_mutable:opencv_onnx.OperatorSetIdProto.domain)
  return _s;
}
inline const std::string& OperatorSetIdProto::_internal_domain() const {
  return domain_.Get();
}
inline void OperatorSetIdProto::_internal_set_domain(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  domain_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* OperatorSetIdProto::_internal_mutable_domain() {
  _has_bits_[0] |= 0x00000001u;
  return domain_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* OperatorSetIdProto::release_domain() {
  // @@protoc_insertion_point(field_release:opencv_onnx.OperatorSetIdProto.domain)
  if (!_internal_has_domain()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = domain_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (domain_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    domain_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void OperatorSetIdProto::set_allocated_domain(std::string* domain) {
  if (domain != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  domain_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), domain,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (domain_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    domain_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_onnx.OperatorSetIdProto.domain)
}

// optional int64 version = 2;
inline bool OperatorSetIdProto::_internal_has_version() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool OperatorSetIdProto::has_version() const {
  return _internal_has_version();
}
inline void OperatorSetIdProto::clear_version() {
  version_ = int64_t{0};
  _has_bits_[0] &= ~0x00000002u;
}
inline int64_t OperatorSetIdProto::_internal_version() const {
  return version_;
}
inline int64_t OperatorSetIdProto::version() const {
  // @@protoc_insertion_point(field_get:opencv_onnx.OperatorSetIdProto.version)
  return _internal_version();
}
inline void OperatorSetIdProto::_internal_set_version(int64_t value) {
  _has_bits_[0] |= 0x00000002u;
  version_ = value;
}
inline void OperatorSetIdProto::set_version(int64_t value) {
  _internal_set_version(value);
  // @@protoc_insertion_point(field_set:opencv_onnx.OperatorSetIdProto.version)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace opencv_onnx

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::opencv_onnx::AttributeProto_AttributeType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::opencv_onnx::AttributeProto_AttributeType>() {
  return ::opencv_onnx::AttributeProto_AttributeType_descriptor();
}
template <> struct is_proto_enum< ::opencv_onnx::TensorProto_DataType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::opencv_onnx::TensorProto_DataType>() {
  return ::opencv_onnx::TensorProto_DataType_descriptor();
}
template <> struct is_proto_enum< ::opencv_onnx::Version> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::opencv_onnx::Version>() {
  return ::opencv_onnx::Version_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_opencv_2donnx_2eproto
