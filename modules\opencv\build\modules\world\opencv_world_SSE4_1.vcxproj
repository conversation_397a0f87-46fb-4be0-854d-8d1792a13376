﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{7B7A4E81-3E2A-36A7-BD7C-57CFC8064160}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>opencv_world_SSE4_1</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">opencv_world_SSE4_1.dir\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">opencv_world_SSE4_1.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">opencv_world_SSE4_1</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">opencv_world_SSE4_1.dir\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">opencv_world_SSE4_1.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">opencv_world_SSE4_1</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\modules\world;D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2;D:\AI\opencv\cudabuild\3rdparty\openjpeg\openjp2;D:\AI\opencv\cudabuild\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libpng;D:\AI\opencv\opencv-4.10.0\3rdparty\libwebp\src;D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudev\include;D:\AI\opencv\opencv-4.10.0\modules\core\include;D:\AI\opencv\opencv-4.10.0\3rdparty\include\opencl\1.2;D:\AI\opencv\opencv-4.10.0\3rdparty\ittnotify\include;D:\AI\opencv\opencv-4.10.0\modules\ts\include;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include;D:\AI\opencv\opencv-4.10.0\modules\videoio\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\include;D:\AI\opencv\opencv-4.10.0\modules\flann\include;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\intensity_transform\include;D:\AI\opencv\opencv-4.10.0\modules\ml\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\plot\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\quality\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\reg\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\signal\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\surface_matching\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudafilters\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudawarping\include;D:\AI\opencv\opencv-4.10.0\modules\dnn\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dnn_superres\include;D:\AI\opencv\opencv-4.10.0\modules\features2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\fuzzy\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\hfs\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\line_descriptor\include;D:\AI\opencv\opencv-4.10.0\modules\photo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\saliency\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudacodec\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudafeatures2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudastereo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\datasets\include;D:\AI\opencv\opencv-4.10.0\modules\highgui\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\mcc\include;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\rapid\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\shape\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include;D:\AI\opencv\opencv-4.10.0\modules\video\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\wechat_qrcode\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xobjdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\bgsegm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\ccalib\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudabgsegm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudalegacy\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaobjdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dnn_objdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dpm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include;D:\AI\opencv\opencv-4.10.0\modules\gapi\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\optflow\include;D:\AI\opencv\opencv-4.10.0\modules\stitching\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaoptflow\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\stereo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\superres\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\videostab\include;D:\AI\opencv\opencv-4.10.0\modules\world\include;D:\AI\opencv\cudabuild\modules\world\test;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\caffe;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\tensorflow;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\onnx;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\tflite;D:\AI\cudnn\include;D:\AI\opencv\cudabuild\downloads\xfeatures2d;D:\AI\opencv\opencv-4.10.0\modules;D:\AI\opencv\opencv-4.10.0\modules\gapi\src;D:\AI\opencv\opencv-4.10.0\modules\gapi\src\3rdparty\vasot\include;D:\AI\opencv\opencv-4.10.0\3rdparty\flatbuffers\include;D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src;D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/AI/opencv/cudabuild/3rdparty/NVIDIAOpticalFlowSDK_2_0_Headers/NVIDIAOpticalFlowSDK-edb50da3cf849840d680249aa6dbef248ebce2ca" /external:I "D:/AI/opencv/cudabuild" /external:I "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include" /external:I "D:/AI/opencv/opencv-4.10.0/3rdparty/openexr/Half" /external:I "D:/AI/opencv/opencv-4.10.0/3rdparty/openexr/Iex" /external:I "D:/AI/opencv/opencv-4.10.0/3rdparty/openexr/IlmThread" /external:I "D:/AI/opencv/opencv-4.10.0/3rdparty/openexr/Imath" /external:I "D:/AI/opencv/opencv-4.10.0/3rdparty/openexr/IlmImf" /external:I "D:/AI/opencv/cudabuild/3rdparty/openexr" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4127;4251;4324;4275;4512;4589;4819;4189;4505;4127;4324;4512;4127;4324;4512;4127;4100;4324;4512;4515;4127;4324;4512;4244;4267;4018;4355;4800;4251;4996;4146;4305;4127;4100;4512;4125;4389;4510;4610;4702;4456;4457;4065;4310;4661;4506;4125;4267;4127;4244;4512;4702;4456;4510;4610;4800;4701;4703;4505;4458;4127;4324;4512;4127;4100;4324;4512;4515;4127;4324;4512;4267;4127;4324;4512;4127;4130;4324;4512;4310;4127;4324;4512;4512;4458;4127;4324;4512</DisableSpecificWarnings>
      <ExceptionHandling>Async</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;OPENCV_WITH_ITT=1;OPENCV_ALLOCATOR_STATS_COUNTER_TYPE=long long;_CRT_SECURE_NO_WARNINGS=1;_HFS_CUDA_ON_;HAVE_WEBP;HAVE_IMGCODEC_HDR;HAVE_IMGCODEC_SUNRASTER;HAVE_IMGCODEC_PXM;HAVE_IMGCODEC_PFM;HAVE_NVIDIA_OPTFLOW=2;_USE_MATH_DEFINES;__STDC_CONSTANT_MACROS;__STDC_LIMIT_MACROS;__STDC_FORMAT_MACROS;__OPENCV_BUILD=1;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;OPENCV_WITH_ITT=1;OPENCV_ALLOCATOR_STATS_COUNTER_TYPE=long long;_CRT_SECURE_NO_WARNINGS=1;_HFS_CUDA_ON_;HAVE_WEBP;HAVE_IMGCODEC_HDR;HAVE_IMGCODEC_SUNRASTER;HAVE_IMGCODEC_PXM;HAVE_IMGCODEC_PFM;HAVE_NVIDIA_OPTFLOW=2;_USE_MATH_DEFINES;__STDC_CONSTANT_MACROS;__STDC_LIMIT_MACROS;__STDC_FORMAT_MACROS;__OPENCV_BUILD=1;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\modules\world;D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2;D:\AI\opencv\cudabuild\3rdparty\openjpeg\openjp2;D:\AI\opencv\cudabuild\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libpng;D:\AI\opencv\opencv-4.10.0\3rdparty\libwebp\src;D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudev\include;D:\AI\opencv\opencv-4.10.0\modules\core\include;D:\AI\opencv\opencv-4.10.0\3rdparty\include\opencl\1.2;D:\AI\opencv\opencv-4.10.0\3rdparty\ittnotify\include;D:\AI\opencv\opencv-4.10.0\modules\ts\include;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include;D:\AI\opencv\opencv-4.10.0\modules\videoio\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\include;D:\AI\opencv\opencv-4.10.0\modules\flann\include;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\intensity_transform\include;D:\AI\opencv\opencv-4.10.0\modules\ml\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\plot\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\quality\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\reg\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\signal\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\surface_matching\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudafilters\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudawarping\include;D:\AI\opencv\opencv-4.10.0\modules\dnn\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dnn_superres\include;D:\AI\opencv\opencv-4.10.0\modules\features2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\fuzzy\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\hfs\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\line_descriptor\include;D:\AI\opencv\opencv-4.10.0\modules\photo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\saliency\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudacodec\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudafeatures2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudastereo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\datasets\include;D:\AI\opencv\opencv-4.10.0\modules\highgui\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\mcc\include;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\rapid\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\shape\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include;D:\AI\opencv\opencv-4.10.0\modules\video\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\wechat_qrcode\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xobjdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\bgsegm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\ccalib\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudabgsegm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudalegacy\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaobjdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dnn_objdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dpm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include;D:\AI\opencv\opencv-4.10.0\modules\gapi\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\optflow\include;D:\AI\opencv\opencv-4.10.0\modules\stitching\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaoptflow\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\stereo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\superres\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\videostab\include;D:\AI\opencv\opencv-4.10.0\modules\world\include;D:\AI\opencv\cudabuild\modules\world\test;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\caffe;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\tensorflow;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\onnx;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\tflite;D:\AI\cudnn\include;D:\AI\opencv\cudabuild\downloads\xfeatures2d;D:\AI\opencv\opencv-4.10.0\modules;D:\AI\opencv\opencv-4.10.0\modules\gapi\src;D:\AI\opencv\opencv-4.10.0\modules\gapi\src\3rdparty\vasot\include;D:\AI\opencv\opencv-4.10.0\3rdparty\flatbuffers\include;D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src;D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include;D:\AI\opencv\cudabuild\3rdparty\NVIDIAOpticalFlowSDK_2_0_Headers\NVIDIAOpticalFlowSDK-edb50da3cf849840d680249aa6dbef248ebce2ca;D:\AI\opencv\cudabuild;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf;D:\AI\opencv\cudabuild\3rdparty\openexr;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\modules\world;D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2;D:\AI\opencv\cudabuild\3rdparty\openjpeg\openjp2;D:\AI\opencv\cudabuild\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libpng;D:\AI\opencv\opencv-4.10.0\3rdparty\libwebp\src;D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudev\include;D:\AI\opencv\opencv-4.10.0\modules\core\include;D:\AI\opencv\opencv-4.10.0\3rdparty\include\opencl\1.2;D:\AI\opencv\opencv-4.10.0\3rdparty\ittnotify\include;D:\AI\opencv\opencv-4.10.0\modules\ts\include;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include;D:\AI\opencv\opencv-4.10.0\modules\videoio\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\include;D:\AI\opencv\opencv-4.10.0\modules\flann\include;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\intensity_transform\include;D:\AI\opencv\opencv-4.10.0\modules\ml\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\plot\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\quality\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\reg\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\signal\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\surface_matching\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudafilters\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudawarping\include;D:\AI\opencv\opencv-4.10.0\modules\dnn\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dnn_superres\include;D:\AI\opencv\opencv-4.10.0\modules\features2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\fuzzy\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\hfs\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\line_descriptor\include;D:\AI\opencv\opencv-4.10.0\modules\photo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\saliency\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudacodec\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudafeatures2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudastereo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\datasets\include;D:\AI\opencv\opencv-4.10.0\modules\highgui\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\mcc\include;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\rapid\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\shape\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include;D:\AI\opencv\opencv-4.10.0\modules\video\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\wechat_qrcode\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xobjdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\bgsegm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\ccalib\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudabgsegm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudalegacy\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaobjdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dnn_objdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dpm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include;D:\AI\opencv\opencv-4.10.0\modules\gapi\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\optflow\include;D:\AI\opencv\opencv-4.10.0\modules\stitching\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaoptflow\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\stereo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\superres\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\videostab\include;D:\AI\opencv\opencv-4.10.0\modules\world\include;D:\AI\opencv\cudabuild\modules\world\test;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\caffe;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\tensorflow;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\onnx;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\tflite;D:\AI\cudnn\include;D:\AI\opencv\cudabuild\downloads\xfeatures2d;D:\AI\opencv\opencv-4.10.0\modules;D:\AI\opencv\opencv-4.10.0\modules\gapi\src;D:\AI\opencv\opencv-4.10.0\modules\gapi\src\3rdparty\vasot\include;D:\AI\opencv\opencv-4.10.0\3rdparty\flatbuffers\include;D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src;D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include;D:\AI\opencv\cudabuild\3rdparty\NVIDIAOpticalFlowSDK_2_0_Headers\NVIDIAOpticalFlowSDK-edb50da3cf849840d680249aa6dbef248ebce2ca;D:\AI\opencv\cudabuild;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf;D:\AI\opencv\cudabuild\3rdparty\openexr;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4221</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\modules\world;D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2;D:\AI\opencv\cudabuild\3rdparty\openjpeg\openjp2;D:\AI\opencv\cudabuild\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libpng;D:\AI\opencv\opencv-4.10.0\3rdparty\libwebp\src;D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudev\include;D:\AI\opencv\opencv-4.10.0\modules\core\include;D:\AI\opencv\opencv-4.10.0\3rdparty\include\opencl\1.2;D:\AI\opencv\opencv-4.10.0\3rdparty\ittnotify\include;D:\AI\opencv\opencv-4.10.0\modules\ts\include;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include;D:\AI\opencv\opencv-4.10.0\modules\videoio\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\include;D:\AI\opencv\opencv-4.10.0\modules\flann\include;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\intensity_transform\include;D:\AI\opencv\opencv-4.10.0\modules\ml\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\plot\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\quality\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\reg\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\signal\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\surface_matching\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudafilters\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudawarping\include;D:\AI\opencv\opencv-4.10.0\modules\dnn\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dnn_superres\include;D:\AI\opencv\opencv-4.10.0\modules\features2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\fuzzy\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\hfs\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\line_descriptor\include;D:\AI\opencv\opencv-4.10.0\modules\photo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\saliency\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudacodec\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudafeatures2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudastereo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\datasets\include;D:\AI\opencv\opencv-4.10.0\modules\highgui\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\mcc\include;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\rapid\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\shape\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include;D:\AI\opencv\opencv-4.10.0\modules\video\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\wechat_qrcode\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xobjdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\bgsegm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\ccalib\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudabgsegm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudalegacy\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaobjdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dnn_objdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dpm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include;D:\AI\opencv\opencv-4.10.0\modules\gapi\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\optflow\include;D:\AI\opencv\opencv-4.10.0\modules\stitching\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaoptflow\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\stereo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\superres\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\videostab\include;D:\AI\opencv\opencv-4.10.0\modules\world\include;D:\AI\opencv\cudabuild\modules\world\test;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\caffe;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\tensorflow;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\onnx;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\tflite;D:\AI\cudnn\include;D:\AI\opencv\cudabuild\downloads\xfeatures2d;D:\AI\opencv\opencv-4.10.0\modules;D:\AI\opencv\opencv-4.10.0\modules\gapi\src;D:\AI\opencv\opencv-4.10.0\modules\gapi\src\3rdparty\vasot\include;D:\AI\opencv\opencv-4.10.0\3rdparty\flatbuffers\include;D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src;D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/AI/opencv/cudabuild/3rdparty/NVIDIAOpticalFlowSDK_2_0_Headers/NVIDIAOpticalFlowSDK-edb50da3cf849840d680249aa6dbef248ebce2ca" /external:I "D:/AI/opencv/cudabuild" /external:I "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include" /external:I "D:/AI/opencv/opencv-4.10.0/3rdparty/openexr/Half" /external:I "D:/AI/opencv/opencv-4.10.0/3rdparty/openexr/Iex" /external:I "D:/AI/opencv/opencv-4.10.0/3rdparty/openexr/IlmThread" /external:I "D:/AI/opencv/opencv-4.10.0/3rdparty/openexr/Imath" /external:I "D:/AI/opencv/opencv-4.10.0/3rdparty/openexr/IlmImf" /external:I "D:/AI/opencv/cudabuild/3rdparty/openexr" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4127;4251;4324;4275;4512;4589;4819;4189;4505;4127;4324;4512;4127;4324;4512;4127;4100;4324;4512;4515;4127;4324;4512;4244;4267;4018;4355;4800;4251;4996;4146;4305;4127;4100;4512;4125;4389;4510;4610;4702;4456;4457;4065;4310;4661;4506;4125;4267;4127;4244;4512;4702;4456;4510;4610;4800;4701;4703;4505;4458;4127;4324;4512;4127;4100;4324;4512;4515;4127;4324;4512;4267;4127;4324;4512;4127;4130;4324;4512;4310;4127;4324;4512;4512;4458;4127;4324;4512</DisableSpecificWarnings>
      <ExceptionHandling>Async</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;NDEBUG;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;OPENCV_WITH_ITT=1;OPENCV_ALLOCATOR_STATS_COUNTER_TYPE=long long;_CRT_SECURE_NO_WARNINGS=1;_HFS_CUDA_ON_;HAVE_WEBP;HAVE_IMGCODEC_HDR;HAVE_IMGCODEC_SUNRASTER;HAVE_IMGCODEC_PXM;HAVE_IMGCODEC_PFM;HAVE_NVIDIA_OPTFLOW=2;_USE_MATH_DEFINES;__STDC_CONSTANT_MACROS;__STDC_LIMIT_MACROS;__STDC_FORMAT_MACROS;__OPENCV_BUILD=1;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;NDEBUG;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;OPENCV_WITH_ITT=1;OPENCV_ALLOCATOR_STATS_COUNTER_TYPE=long long;_CRT_SECURE_NO_WARNINGS=1;_HFS_CUDA_ON_;HAVE_WEBP;HAVE_IMGCODEC_HDR;HAVE_IMGCODEC_SUNRASTER;HAVE_IMGCODEC_PXM;HAVE_IMGCODEC_PFM;HAVE_NVIDIA_OPTFLOW=2;_USE_MATH_DEFINES;__STDC_CONSTANT_MACROS;__STDC_LIMIT_MACROS;__STDC_FORMAT_MACROS;__OPENCV_BUILD=1;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\modules\world;D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2;D:\AI\opencv\cudabuild\3rdparty\openjpeg\openjp2;D:\AI\opencv\cudabuild\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libpng;D:\AI\opencv\opencv-4.10.0\3rdparty\libwebp\src;D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudev\include;D:\AI\opencv\opencv-4.10.0\modules\core\include;D:\AI\opencv\opencv-4.10.0\3rdparty\include\opencl\1.2;D:\AI\opencv\opencv-4.10.0\3rdparty\ittnotify\include;D:\AI\opencv\opencv-4.10.0\modules\ts\include;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include;D:\AI\opencv\opencv-4.10.0\modules\videoio\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\include;D:\AI\opencv\opencv-4.10.0\modules\flann\include;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\intensity_transform\include;D:\AI\opencv\opencv-4.10.0\modules\ml\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\plot\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\quality\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\reg\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\signal\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\surface_matching\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudafilters\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudawarping\include;D:\AI\opencv\opencv-4.10.0\modules\dnn\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dnn_superres\include;D:\AI\opencv\opencv-4.10.0\modules\features2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\fuzzy\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\hfs\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\line_descriptor\include;D:\AI\opencv\opencv-4.10.0\modules\photo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\saliency\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudacodec\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudafeatures2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudastereo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\datasets\include;D:\AI\opencv\opencv-4.10.0\modules\highgui\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\mcc\include;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\rapid\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\shape\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include;D:\AI\opencv\opencv-4.10.0\modules\video\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\wechat_qrcode\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xobjdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\bgsegm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\ccalib\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudabgsegm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudalegacy\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaobjdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dnn_objdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dpm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include;D:\AI\opencv\opencv-4.10.0\modules\gapi\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\optflow\include;D:\AI\opencv\opencv-4.10.0\modules\stitching\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaoptflow\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\stereo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\superres\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\videostab\include;D:\AI\opencv\opencv-4.10.0\modules\world\include;D:\AI\opencv\cudabuild\modules\world\test;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\caffe;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\tensorflow;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\onnx;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\tflite;D:\AI\cudnn\include;D:\AI\opencv\cudabuild\downloads\xfeatures2d;D:\AI\opencv\opencv-4.10.0\modules;D:\AI\opencv\opencv-4.10.0\modules\gapi\src;D:\AI\opencv\opencv-4.10.0\modules\gapi\src\3rdparty\vasot\include;D:\AI\opencv\opencv-4.10.0\3rdparty\flatbuffers\include;D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src;D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include;D:\AI\opencv\cudabuild\3rdparty\NVIDIAOpticalFlowSDK_2_0_Headers\NVIDIAOpticalFlowSDK-edb50da3cf849840d680249aa6dbef248ebce2ca;D:\AI\opencv\cudabuild;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf;D:\AI\opencv\cudabuild\3rdparty\openexr;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\modules\world;D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\openjpeg\openjp2;D:\AI\opencv\cudabuild\3rdparty\openjpeg\openjp2;D:\AI\opencv\cudabuild\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libpng;D:\AI\opencv\opencv-4.10.0\3rdparty\libwebp\src;D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudev\include;D:\AI\opencv\opencv-4.10.0\modules\core\include;D:\AI\opencv\opencv-4.10.0\3rdparty\include\opencl\1.2;D:\AI\opencv\opencv-4.10.0\3rdparty\ittnotify\include;D:\AI\opencv\opencv-4.10.0\modules\ts\include;D:\AI\opencv\opencv-4.10.0\modules\imgcodecs\include;D:\AI\opencv\opencv-4.10.0\modules\videoio\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaarithm\include;D:\AI\opencv\opencv-4.10.0\modules\flann\include;D:\AI\opencv\opencv-4.10.0\modules\imgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\intensity_transform\include;D:\AI\opencv\opencv-4.10.0\modules\ml\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\phase_unwrapping\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\plot\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\quality\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\reg\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\signal\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\surface_matching\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudafilters\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudawarping\include;D:\AI\opencv\opencv-4.10.0\modules\dnn\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dnn_superres\include;D:\AI\opencv\opencv-4.10.0\modules\features2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\fuzzy\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\hfs\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\line_descriptor\include;D:\AI\opencv\opencv-4.10.0\modules\photo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\saliency\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\text\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\include;D:\AI\opencv\opencv-4.10.0\modules\calib3d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudacodec\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudafeatures2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudastereo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\datasets\include;D:\AI\opencv\opencv-4.10.0\modules\highgui\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\mcc\include;D:\AI\opencv\opencv-4.10.0\modules\objdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\rapid\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\rgbd\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\shape\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\include;D:\AI\opencv\opencv-4.10.0\modules\video\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\wechat_qrcode\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xfeatures2d\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\xobjdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\aruco\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\bgsegm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\bioinspired\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\ccalib\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudabgsegm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudalegacy\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaobjdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dnn_objdetect\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\dpm\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\face\include;D:\AI\opencv\opencv-4.10.0\modules\gapi\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\optflow\include;D:\AI\opencv\opencv-4.10.0\modules\stitching\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\tracking\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaoptflow\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\stereo\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\superres\include;D:\AI\opencv\opencv_contrib-4.10.0\modules\videostab\include;D:\AI\opencv\opencv-4.10.0\modules\world\include;D:\AI\opencv\cudabuild\modules\world\test;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\caffe;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\tensorflow;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\onnx;D:\AI\opencv\opencv-4.10.0\modules\dnn\misc\tflite;D:\AI\cudnn\include;D:\AI\opencv\cudabuild\downloads\xfeatures2d;D:\AI\opencv\opencv-4.10.0\modules;D:\AI\opencv\opencv-4.10.0\modules\gapi\src;D:\AI\opencv\opencv-4.10.0\modules\gapi\src\3rdparty\vasot\include;D:\AI\opencv\opencv-4.10.0\3rdparty\flatbuffers\include;D:\AI\opencv\opencv-4.10.0\3rdparty\protobuf\src;D:\AI\opencv\cudabuild\3rdparty\ade\ade-0.1.2d\sources\ade\include;D:\AI\opencv\cudabuild\3rdparty\NVIDIAOpticalFlowSDK_2_0_Headers\NVIDIAOpticalFlowSDK-edb50da3cf849840d680249aa6dbef248ebce2ca;D:\AI\opencv\cudabuild;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath;D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf;D:\AI\opencv\cudabuild\3rdparty\openexr;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4221</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\arithm.sse4_1.cpp">
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\matmul.sse4_1.cpp">
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\src\imgwarp.sse4_1.cpp">
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\src\resize.sse4_1.cpp">
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\accum.sse4_1.cpp">
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\box_filter.sse4_1.cpp">
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\filter.sse4_1.cpp">
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\color_hsv.sse4_1.cpp">
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\color_rgb.sse4_1.cpp">
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\color_yuv.sse4_1.cpp">
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\median_blur.sse4_1.cpp">
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\morph.sse4_1.cpp">
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\smooth.sse4_1.cpp">
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\sift.sse4_1.cpp">
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\backends\fluid\gfluidimgproc_func.sse4_1.cpp">
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\world\backends\fluid\gfluidcore_func.sse4_1.cpp">
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(PreprocessorDefinitions);CV_CPU_DISPATCH_MODE=SSE4_1</PreprocessorDefinitions>
    </ClCompile>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>