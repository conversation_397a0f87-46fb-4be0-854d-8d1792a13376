﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\ocl\test_accumulate.cpp">
      <Filter>opencv_imgproc\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\ocl\test_blend.cpp">
      <Filter>opencv_imgproc\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\ocl\test_boxfilter.cpp">
      <Filter>opencv_imgproc\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\ocl\test_canny.cpp">
      <Filter>opencv_imgproc\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\ocl\test_color.cpp">
      <Filter>opencv_imgproc\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\ocl\test_filter2d.cpp">
      <Filter>opencv_imgproc\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\ocl\test_filters.cpp">
      <Filter>opencv_imgproc\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\ocl\test_gftt.cpp">
      <Filter>opencv_imgproc\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\ocl\test_histogram.cpp">
      <Filter>opencv_imgproc\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\ocl\test_houghlines.cpp">
      <Filter>opencv_imgproc\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\ocl\test_imgproc.cpp">
      <Filter>opencv_imgproc\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\ocl\test_match_template.cpp">
      <Filter>opencv_imgproc\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\ocl\test_medianfilter.cpp">
      <Filter>opencv_imgproc\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\ocl\test_pyramids.cpp">
      <Filter>opencv_imgproc\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\ocl\test_sepfilter2d.cpp">
      <Filter>opencv_imgproc\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\ocl\test_warp.cpp">
      <Filter>opencv_imgproc\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_approxpoly.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_bilateral_filter.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_boundingrect.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_canny.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_color.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_connectedcomponents.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_contours.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_contours_new.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_convhull.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_cornersubpix.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_cvtyuv.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_distancetransform.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_drawing.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_emd.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_filter.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_fitellipse.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_fitellipse_ams.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_fitellipse_direct.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_floodfill.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_goodfeaturetotrack.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_grabcut.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_histograms.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_houghcircles.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_houghlines.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_imgproc_umat.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_imgwarp.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_imgwarp_strict.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_intelligent_scissors.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_intersectconvexconvex.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_intersection.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_lsd.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_main.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_moments.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_pc.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_pyramid.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_resize_bitexact.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_smooth_bitexact.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_stackblur.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_subdivision2d.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_templmatch.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_templmatchmask.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_thresh.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_watershed.cpp">
      <Filter>opencv_imgproc\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\imgproc\test\test_precomp.hpp">
      <Filter>opencv_imgproc\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_imgproc">
      <UniqueIdentifier>{FA23004A-22DB-30E6-B8FD-718F497FB629}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_imgproc\Include">
      <UniqueIdentifier>{AABF94F6-4F3F-3B4E-B7E7-DFBBB12077CC}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_imgproc\Src">
      <UniqueIdentifier>{7A7ACE0E-D197-310E-909D-212CE8311A24}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_imgproc\Src\ocl">
      <UniqueIdentifier>{D8BFB576-C5A3-357A-8AA3-B10426A8E87C}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
