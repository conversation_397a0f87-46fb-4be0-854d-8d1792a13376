@InProceedings{<PERSON><PERSON><PERSON>2010,
author="<PERSON><PERSON><PERSON>, <PERSON><PERSON>
and <PERSON>, <PERSON>
and <PERSON>, <PERSON>
and <PERSON>, <PERSON><PERSON><PERSON>",
editor="<PERSON>, <PERSON><PERSON><PERSON>
and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
and <PERSON><PERSON><PERSON>, <PERSON><PERSON>
and <PERSON><PERSON><PERSON><PERSON>, Max A<PERSON>",
title="Real-Time Stereo Reconstruction in Robotically Assisted Minimally Invasive Surgery",
booktitle="Medical Image Computing and Computer-Assisted Intervention (MICCAI 2010)",
year="2010",
publisher="Springer Berlin Heidelberg",
address="Berlin, Heidelberg",
pages="275--282",
abstract="The recovery of 3D tissue structure and morphology during robotic assisted surgery is an important step towards accurate deployment of surgical guidance and control techniques in minimally invasive therapies. In this article, we present a novel stereo reconstruction algorithm that propagates disparity information around a set of candidate feature matches. This has the advantage of avoiding problems with specular highlights, occlusions from instruments and view dependent illumination bias. Furthermore, the algorithm can be used with any feature matching strategy allowing the propagation of depth in very disparate views. Validation is provided for a phantom model with known geometry and this data is available online in order to establish a structured validation scheme in the field. The practical value of the proposed method is further demonstrated by reconstructions on various in vivo images of robotic assisted procedures, which are also available to the community.",
isbn="978-3-642-15705-9"
}

@article{Lhuillier2000,
abstract = {A new robust dense matching algorithm is introduced. The algorithm$\backslash$nstarts from matching the most textured points, then a match propagation$\backslash$nalgorithm is developed with the best first strategy to dense matching.$\backslash$nNext, the matching map is regularised by using the local geometric$\backslash$nconstraints encoded by planar affine applications and by using the$\backslash$nglobal geometric constraint encoded by the fundamental matrix. Two most$\backslash$ndistinctive features are a match propagation strategy developed by$\backslash$nanalogy to region growing and a successive regularisation by local and$\backslash$nglobal geometric constraints. The algorithm is efficient, robust and can$\backslash$ncope with wide disparity. The algorithm is demonstrated on many real$\backslash$nimage pairs, and applications on image interpolation and a creation of$\backslash$nnovel views are also presented},
author = {Lhuillier, Maxime and Quan, Long},
doi = {10.1109/ICPR.2000.905620},
file = {:home/dimitrisps/Desktop/ucl/papers/quasiDenseMatching.pdf:pdf},
isbn = {0-7695-0750-6},
issn = {10514651},
journal = {Proceedings-International Conference on Pattern Recognition},
number = {1},
pages = {968--972},
title = {{Robust dense matching using local and global geometric constraints}},
volume = {15},
year = {2000}
}
