<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Joysticks</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle"><div class="title">Joysticks<div class="ingroups"><a class="el" href="group__input.html">Input reference</a></div></div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>See <a class="el" href="input_guide.html#joystick">joystick input</a> for how these are used. </p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:ga34a0443d059e9f22272cd4669073f73d" id="r_ga34a0443d059e9f22272cd4669073f73d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga34a0443d059e9f22272cd4669073f73d">GLFW_JOYSTICK_1</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:ga34a0443d059e9f22272cd4669073f73d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6eab65ec88e65e0850ef8413504cb50c" id="r_ga6eab65ec88e65e0850ef8413504cb50c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga6eab65ec88e65e0850ef8413504cb50c">GLFW_JOYSTICK_2</a>&#160;&#160;&#160;1</td></tr>
<tr class="separator:ga6eab65ec88e65e0850ef8413504cb50c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae6f3eedfeb42424c2f5e3161efb0b654" id="r_gae6f3eedfeb42424c2f5e3161efb0b654"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#gae6f3eedfeb42424c2f5e3161efb0b654">GLFW_JOYSTICK_3</a>&#160;&#160;&#160;2</td></tr>
<tr class="separator:gae6f3eedfeb42424c2f5e3161efb0b654"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga97ddbcad02b7f48d74fad4ddb08fff59" id="r_ga97ddbcad02b7f48d74fad4ddb08fff59"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga97ddbcad02b7f48d74fad4ddb08fff59">GLFW_JOYSTICK_4</a>&#160;&#160;&#160;3</td></tr>
<tr class="separator:ga97ddbcad02b7f48d74fad4ddb08fff59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae43281bc66d3fa5089fb50c3e7a28695" id="r_gae43281bc66d3fa5089fb50c3e7a28695"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#gae43281bc66d3fa5089fb50c3e7a28695">GLFW_JOYSTICK_5</a>&#160;&#160;&#160;4</td></tr>
<tr class="separator:gae43281bc66d3fa5089fb50c3e7a28695"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga74771620aa53bd68a487186dea66fd77" id="r_ga74771620aa53bd68a487186dea66fd77"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga74771620aa53bd68a487186dea66fd77">GLFW_JOYSTICK_6</a>&#160;&#160;&#160;5</td></tr>
<tr class="separator:ga74771620aa53bd68a487186dea66fd77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga20a9f4f3aaefed9ea5e66072fc588b87" id="r_ga20a9f4f3aaefed9ea5e66072fc588b87"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga20a9f4f3aaefed9ea5e66072fc588b87">GLFW_JOYSTICK_7</a>&#160;&#160;&#160;6</td></tr>
<tr class="separator:ga20a9f4f3aaefed9ea5e66072fc588b87"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga21a934c940bcf25db0e4c8fe9b364bdb" id="r_ga21a934c940bcf25db0e4c8fe9b364bdb"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga21a934c940bcf25db0e4c8fe9b364bdb">GLFW_JOYSTICK_8</a>&#160;&#160;&#160;7</td></tr>
<tr class="separator:ga21a934c940bcf25db0e4c8fe9b364bdb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga87689d47df0ba6f9f5fcbbcaf7b3cecf" id="r_ga87689d47df0ba6f9f5fcbbcaf7b3cecf"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga87689d47df0ba6f9f5fcbbcaf7b3cecf">GLFW_JOYSTICK_9</a>&#160;&#160;&#160;8</td></tr>
<tr class="separator:ga87689d47df0ba6f9f5fcbbcaf7b3cecf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaef55389ee605d6dfc31aef6fe98c54ec" id="r_gaef55389ee605d6dfc31aef6fe98c54ec"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#gaef55389ee605d6dfc31aef6fe98c54ec">GLFW_JOYSTICK_10</a>&#160;&#160;&#160;9</td></tr>
<tr class="separator:gaef55389ee605d6dfc31aef6fe98c54ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae7d26e3df447c2c14a569fcc18516af4" id="r_gae7d26e3df447c2c14a569fcc18516af4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#gae7d26e3df447c2c14a569fcc18516af4">GLFW_JOYSTICK_11</a>&#160;&#160;&#160;10</td></tr>
<tr class="separator:gae7d26e3df447c2c14a569fcc18516af4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab91bbf5b7ca6be8d3ac5c4d89ff48ac7" id="r_gab91bbf5b7ca6be8d3ac5c4d89ff48ac7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#gab91bbf5b7ca6be8d3ac5c4d89ff48ac7">GLFW_JOYSTICK_12</a>&#160;&#160;&#160;11</td></tr>
<tr class="separator:gab91bbf5b7ca6be8d3ac5c4d89ff48ac7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5c84fb4e49bf661d7d7c78eb4018c508" id="r_ga5c84fb4e49bf661d7d7c78eb4018c508"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga5c84fb4e49bf661d7d7c78eb4018c508">GLFW_JOYSTICK_13</a>&#160;&#160;&#160;12</td></tr>
<tr class="separator:ga5c84fb4e49bf661d7d7c78eb4018c508"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga89540873278ae5a42b3e70d64164dc74" id="r_ga89540873278ae5a42b3e70d64164dc74"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga89540873278ae5a42b3e70d64164dc74">GLFW_JOYSTICK_14</a>&#160;&#160;&#160;13</td></tr>
<tr class="separator:ga89540873278ae5a42b3e70d64164dc74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7b02ab70daf7a78bcc942d5d4cc1dcf9" id="r_ga7b02ab70daf7a78bcc942d5d4cc1dcf9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga7b02ab70daf7a78bcc942d5d4cc1dcf9">GLFW_JOYSTICK_15</a>&#160;&#160;&#160;14</td></tr>
<tr class="separator:ga7b02ab70daf7a78bcc942d5d4cc1dcf9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga453edeeabf350827646b6857df4f80ce" id="r_ga453edeeabf350827646b6857df4f80ce"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga453edeeabf350827646b6857df4f80ce">GLFW_JOYSTICK_16</a>&#160;&#160;&#160;15</td></tr>
<tr class="separator:ga453edeeabf350827646b6857df4f80ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9ca13ebf24c331dd98df17d84a4b72c9" id="r_ga9ca13ebf24c331dd98df17d84a4b72c9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__joysticks.html#ga9ca13ebf24c331dd98df17d84a4b72c9">GLFW_JOYSTICK_LAST</a>&#160;&#160;&#160;<a class="el" href="group__joysticks.html#ga453edeeabf350827646b6857df4f80ce">GLFW_JOYSTICK_16</a></td></tr>
<tr class="separator:ga9ca13ebf24c331dd98df17d84a4b72c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="ga34a0443d059e9f22272cd4669073f73d" name="ga34a0443d059e9f22272cd4669073f73d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga34a0443d059e9f22272cd4669073f73d">&#9670;&#160;</a></span>GLFW_JOYSTICK_1</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_1&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga6eab65ec88e65e0850ef8413504cb50c" name="ga6eab65ec88e65e0850ef8413504cb50c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6eab65ec88e65e0850ef8413504cb50c">&#9670;&#160;</a></span>GLFW_JOYSTICK_2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_2&#160;&#160;&#160;1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gae6f3eedfeb42424c2f5e3161efb0b654" name="gae6f3eedfeb42424c2f5e3161efb0b654"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae6f3eedfeb42424c2f5e3161efb0b654">&#9670;&#160;</a></span>GLFW_JOYSTICK_3</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_3&#160;&#160;&#160;2</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga97ddbcad02b7f48d74fad4ddb08fff59" name="ga97ddbcad02b7f48d74fad4ddb08fff59"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga97ddbcad02b7f48d74fad4ddb08fff59">&#9670;&#160;</a></span>GLFW_JOYSTICK_4</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_4&#160;&#160;&#160;3</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gae43281bc66d3fa5089fb50c3e7a28695" name="gae43281bc66d3fa5089fb50c3e7a28695"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae43281bc66d3fa5089fb50c3e7a28695">&#9670;&#160;</a></span>GLFW_JOYSTICK_5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_5&#160;&#160;&#160;4</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga74771620aa53bd68a487186dea66fd77" name="ga74771620aa53bd68a487186dea66fd77"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga74771620aa53bd68a487186dea66fd77">&#9670;&#160;</a></span>GLFW_JOYSTICK_6</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_6&#160;&#160;&#160;5</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga20a9f4f3aaefed9ea5e66072fc588b87" name="ga20a9f4f3aaefed9ea5e66072fc588b87"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga20a9f4f3aaefed9ea5e66072fc588b87">&#9670;&#160;</a></span>GLFW_JOYSTICK_7</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_7&#160;&#160;&#160;6</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga21a934c940bcf25db0e4c8fe9b364bdb" name="ga21a934c940bcf25db0e4c8fe9b364bdb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga21a934c940bcf25db0e4c8fe9b364bdb">&#9670;&#160;</a></span>GLFW_JOYSTICK_8</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_8&#160;&#160;&#160;7</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga87689d47df0ba6f9f5fcbbcaf7b3cecf" name="ga87689d47df0ba6f9f5fcbbcaf7b3cecf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga87689d47df0ba6f9f5fcbbcaf7b3cecf">&#9670;&#160;</a></span>GLFW_JOYSTICK_9</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_9&#160;&#160;&#160;8</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaef55389ee605d6dfc31aef6fe98c54ec" name="gaef55389ee605d6dfc31aef6fe98c54ec"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaef55389ee605d6dfc31aef6fe98c54ec">&#9670;&#160;</a></span>GLFW_JOYSTICK_10</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_10&#160;&#160;&#160;9</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gae7d26e3df447c2c14a569fcc18516af4" name="gae7d26e3df447c2c14a569fcc18516af4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae7d26e3df447c2c14a569fcc18516af4">&#9670;&#160;</a></span>GLFW_JOYSTICK_11</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_11&#160;&#160;&#160;10</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gab91bbf5b7ca6be8d3ac5c4d89ff48ac7" name="gab91bbf5b7ca6be8d3ac5c4d89ff48ac7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab91bbf5b7ca6be8d3ac5c4d89ff48ac7">&#9670;&#160;</a></span>GLFW_JOYSTICK_12</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_12&#160;&#160;&#160;11</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga5c84fb4e49bf661d7d7c78eb4018c508" name="ga5c84fb4e49bf661d7d7c78eb4018c508"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5c84fb4e49bf661d7d7c78eb4018c508">&#9670;&#160;</a></span>GLFW_JOYSTICK_13</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_13&#160;&#160;&#160;12</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga89540873278ae5a42b3e70d64164dc74" name="ga89540873278ae5a42b3e70d64164dc74"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga89540873278ae5a42b3e70d64164dc74">&#9670;&#160;</a></span>GLFW_JOYSTICK_14</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_14&#160;&#160;&#160;13</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga7b02ab70daf7a78bcc942d5d4cc1dcf9" name="ga7b02ab70daf7a78bcc942d5d4cc1dcf9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7b02ab70daf7a78bcc942d5d4cc1dcf9">&#9670;&#160;</a></span>GLFW_JOYSTICK_15</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_15&#160;&#160;&#160;14</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga453edeeabf350827646b6857df4f80ce" name="ga453edeeabf350827646b6857df4f80ce"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga453edeeabf350827646b6857df4f80ce">&#9670;&#160;</a></span>GLFW_JOYSTICK_16</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_16&#160;&#160;&#160;15</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga9ca13ebf24c331dd98df17d84a4b72c9" name="ga9ca13ebf24c331dd98df17d84a4b72c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9ca13ebf24c331dd98df17d84a4b72c9">&#9670;&#160;</a></span>GLFW_JOYSTICK_LAST</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_JOYSTICK_LAST&#160;&#160;&#160;<a class="el" href="group__joysticks.html#ga453edeeabf350827646b6857df4f80ce">GLFW_JOYSTICK_16</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
