/* clang-format off */
/* clang-format disabled because CMake scripts are very sensitive to the
 * formatting of this file. configure_file variables of type "@VAR@" are
 * modified by clang-format and won't be substituted.
 */

/* libtiff/tif_config.h.cmake.in.  Not generated, but originated from autoheader.  */
/* This file must be kept up-to-date with needed substitutions from libtiff/tif_config.h.in. */

#include "tiffconf.h"

/* Support CCITT Group 3 & 4 algorithms */
#cmakedefine CCITT_SUPPORT 1

/* Pick up YCbCr subsampling info from the JPEG data stream to support files
   lacking the tag (default enabled). */
#cmakedefine CHECK_JPEG_YCBCR_SUBSAMPLING 1

/* enable partial strip reading for large strips (experimental) */
#cmakedefine CHUNKY_STRIP_READ_SUPPORT 1

/* Support C++ stream API (requires C++ compiler) */
#cmakedefine CXX_SUPPORT 1

/* enable deferred strip/tile offset/size loading (experimental) */
#cmakedefine DEFER_STRILE_LOAD 1

/* Define to 1 if you have the <assert.h> header file. */
#cmakedefine HAVE_ASSERT_H 1

/* Define to 1 if you have the declaration of `optarg', and to 0 if you don't. */
#cmakedefine HAVE_DECL_OPTARG 1

/* Define to 1 if you have the <fcntl.h> header file. */
#cmakedefine HAVE_FCNTL_H 1

/* Define to 1 if fseeko (and presumably ftello) exists and is declared. */
#cmakedefine HAVE_FSEEKO 1

/* Define to 1 if you have the `getopt' function. */
#cmakedefine HAVE_GETOPT 1

/* Define to 1 if you have the <io.h> header file. */
#cmakedefine HAVE_IO_H 1

/* Define to 1 if you have the `jbg_newlen' function. */
#cmakedefine HAVE_JBG_NEWLEN 1

/* Define to 1 if you have the `mmap' function. */
#cmakedefine HAVE_MMAP 1

/* Define to 1 if you have the `setmode' function. */
#cmakedefine HAVE_SETMODE 1

/* Define to 1 if you have the <strings.h> header file. */
#cmakedefine HAVE_STRINGS_H 1

/* Define to 1 if you have the <sys/types.h> header file. */
#cmakedefine HAVE_SYS_TYPES_H 1

/* Define to 1 if you have the <unistd.h> header file. */
#cmakedefine HAVE_UNISTD_H 1

/* 8/12 bit libjpeg dual mode enabled */
#cmakedefine JPEG_DUAL_MODE_8_12 1 1

/* Support LERC compression */
#cmakedefine LERC_SUPPORT 1

/* 12bit libjpeg primary include file with path */
#define LIBJPEG_12_PATH "@LIBJPEG_12_PATH@"

/* Support LZMA2 compression */
#cmakedefine LZMA_SUPPORT 1

/* Name of package */
#define PACKAGE "@PACKAGE_NAME@"

/* Define to the address where bug reports for this package should be sent. */
#define PACKAGE_BUGREPORT "@PACKAGE_BUGREPORT@"

/* Define to the full name of this package. */
#define PACKAGE_NAME "@PACKAGE_NAME@"

/* Define to the one symbol short name of this package. */
#define PACKAGE_TARNAME "@PACKAGE_TARNAME@"

/* Define to the home page for this package. */
#define PACKAGE_URL "@PACKAGE_URL@"

/* Size of size_t */
#define SIZEOF_SIZE_T @SIZEOF_SIZE_T@

/* Default size of the strip in bytes (when strip chopping enabled) */
#cmakedefine STRIP_SIZE_DEFAULT @STRIP_SIZE_DEFAULT@

/** Maximum number of TIFF IFDs that libtiff can iterate through in a file. */
#define TIFF_MAX_DIR_COUNT @TIFF_MAX_DIR_COUNT@

/* define to use win32 IO system */
#cmakedefine USE_WIN32_FILEIO 1

/* Support WEBP compression */
#cmakedefine WEBP_SUPPORT 1

/* Support ZSTD compression */
#cmakedefine ZSTD_SUPPORT 1


/* Define WORDS_BIGENDIAN to 1 if your processor stores words with the most
   significant byte first (like Motorola and SPARC, unlike Intel). */
#if defined AC_APPLE_UNIVERSAL_BUILD
# if defined __BIG_ENDIAN__
#  define WORDS_BIGENDIAN 1
# endif
#else
# ifndef WORDS_BIGENDIAN
#  undef WORDS_BIGENDIAN
# endif
#endif

#if !defined(__MINGW32__)
#  define TIFF_SIZE_FORMAT "zu"
#endif
#if SIZEOF_SIZE_T == 8
#  define TIFF_SSIZE_FORMAT PRId64
#  if defined(__MINGW32__)
#    define TIFF_SIZE_FORMAT PRIu64
#  endif
#elif SIZEOF_SIZE_T == 4
#  define TIFF_SSIZE_FORMAT PRId32
#  if defined(__MINGW32__)
#    define TIFF_SIZE_FORMAT PRIu32
#  endif
#else
#  error "Unsupported size_t size; please submit a bug report"
#endif

/* clang-format on */
