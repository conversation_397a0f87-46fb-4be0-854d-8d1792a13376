Structured Light tutorials {#tutorial_structured_light}
=============================================================

-   @subpage tutorial_capture_graycode_pattern

    _Compatibility:_ \> OpenCV 3.0.0

    _Author:_ <PERSON>

    You will learn how to acquire a dataset using *GrayCodePattern* class.

-   @subpage tutorial_decode_graycode_pattern

    _Compatibility:_ \> OpenCV 3.0.0

    _Author:_ <PERSON>

    You will learn how to decode a previously acquired Gray code pattern, generating a pointcloud.

-	@subpage tutorial_capture_sinusoidal_pattern

	_Compatibility:_ \> OpenCV 3.0.0

	_Author:_ <PERSON><PERSON><PERSON>

	You will learn how to compute phase maps using *SinusoidalPattern* class.
