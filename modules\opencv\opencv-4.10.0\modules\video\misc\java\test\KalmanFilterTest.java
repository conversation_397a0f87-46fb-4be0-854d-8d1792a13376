package org.opencv.test.video;

import org.opencv.test.OpenCVTestCase;
import org.opencv.video.KalmanFilter;

public class KalmanFilterTest extends OpenCVTestCase {

    public void testCorrect() {
        fail("Not yet implemented");
    }

    public void testKalmanFilter() {
        KalmanFilter kf = new KalmanFilter();

        assertNotNull(kf);
    }

    public void testKalmanFilterIntInt() {
        fail("Not yet implemented");
    }

    public void testKalmanFilterIntIntInt() {
        fail("Not yet implemented");
    }

    public void testKalmanFilterIntIntIntInt() {
        fail("Not yet implemented");
    }

    public void testPredict() {
        fail("Not yet implemented");
    }

    public void testPredictMat() {
        fail("Not yet implemented");
    }

}
