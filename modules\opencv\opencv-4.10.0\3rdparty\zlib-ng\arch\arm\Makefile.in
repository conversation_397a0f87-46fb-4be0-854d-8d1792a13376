# Makefile for zlib
# Copyright (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON>, <PERSON>
# For conditions of distribution and use, see copyright notice in zlib.h

CC=
CFLAGS=
SFLAGS=
INCLUDES=
SUFFIX=

ACLEFLAG=
NEONFLAG=
ARMV6FLAG=
NOLTOFLAG=

SRCDIR=.
SRCTOP=../..
TOPDIR=$(SRCTOP)

all: \
	adler32_neon.o adler32_neon.lo \
	arm_features.o arm_features.lo \
	chunkset_neon.o chunkset_neon.lo \
	compare256_neon.o compare256_neon.lo \
	crc32_acle.o crc32_acle.lo \
	slide_hash_neon.o slide_hash_neon.lo \
	slide_hash_armv6.o slide_hash_armv6.lo \
	insert_string_acle.o insert_string_acle.lo

adler32_neon.o:
	$(CC) $(CFLAGS) $(NEONFLAG) $(NOLTOFLAG) $(INCLUDES) -c -o $@ $(SRCDIR)/adler32_neon.c

adler32_neon.lo:
	$(CC) $(SFLAGS) $(NEONFLAG) $(NOLTOFLAG) $(INCLUDES) -c -o $@ $(SRCDIR)/adler32_neon.c

arm_features.o:
	$(CC) $(CFLAGS) $(INCLUDES) -c -o $@ $(SRCDIR)/arm_features.c

arm_features.lo:
	$(CC) $(SFLAGS) $(INCLUDES) -c -o $@ $(SRCDIR)/arm_features.c

chunkset_neon.o:
	$(CC) $(CFLAGS) $(NEONFLAG) $(NOLTOFLAG) $(INCLUDES) -c -o $@ $(SRCDIR)/chunkset_neon.c

chunkset_neon.lo:
	$(CC) $(SFLAGS) $(NEONFLAG) $(NOLTOFLAG) $(INCLUDES) -c -o $@ $(SRCDIR)/chunkset_neon.c

compare256_neon.o:
	$(CC) $(CFLAGS) $(NEONFLAG) $(NOLTOFLAG) $(INCLUDES) -c -o $@ $(SRCDIR)/compare256_neon.c

compare256_neon.lo:
	$(CC) $(SFLAGS) $(NEONFLAG) $(NOLTOFLAG) $(INCLUDES) -c -o $@ $(SRCDIR)/compare256_neon.c

crc32_acle.o:
	$(CC) $(CFLAGS) $(ACLEFLAG) $(NOLTOFLAG) $(INCLUDES) -c -o $@ $(SRCDIR)/crc32_acle.c

crc32_acle.lo:
	$(CC) $(SFLAGS) $(ACLEFLAG) $(NOLTOFLAG) $(INCLUDES) -c -o $@ $(SRCDIR)/crc32_acle.c

slide_hash_neon.o:
	$(CC) $(CFLAGS) $(NEONFLAG) $(NOLTOFLAG) $(INCLUDES) -c -o $@ $(SRCDIR)/slide_hash_neon.c

slide_hash_neon.lo:
	$(CC) $(SFLAGS) $(NEONFLAG) $(NOLTOFLAG) $(INCLUDES) -c -o $@ $(SRCDIR)/slide_hash_neon.c

slide_hash_armv6.o:
	$(CC) $(CFLAGS) $(ARMV6FLAG) $(NOLTOFLAG) $(INCLUDES) -c -o $@ $(SRCDIR)/slide_hash_armv6.c

slide_hash_armv6.lo:
	$(CC) $(SFLAGS) $(ARMV6FLAG) $(NOLTOFLAG) $(INCLUDES) -c -o $@ $(SRCDIR)/slide_hash_armv6.c

insert_string_acle.o:
	$(CC) $(CFLAGS) $(ACLEFLAG) $(NOLTOFLAG) $(INCLUDES) -c -o $@ $(SRCDIR)/insert_string_acle.c

insert_string_acle.lo:
	$(CC) $(SFLAGS) $(ACLEFLAG) $(NOLTOFLAG) $(INCLUDES) -c -o $@ $(SRCDIR)/insert_string_acle.c

mostlyclean: clean
clean:
	rm -f *.o *.lo *~
	rm -rf objs
	rm -f *.gcda *.gcno *.gcov

distclean: clean
	rm -f Makefile
