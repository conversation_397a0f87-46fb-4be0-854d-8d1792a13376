The Hierarchical Data Format (hdf) I/O {#tutorial_table_of_content_hdf}
=====================================

Here you will know how to read and write a HDF5 file using OpenCV.
Specifically, it shows you how to read/write groups, datasets and attributes.

@note The HDF5 library has to be installed in your system
to use this module.

-   @subpage tutorial_hdf_create_groups

    *Compatibility:* \> OpenCV 3.0

    *Author:* <PERSON><PERSON>

    You will learn how to create groups and subgroups.

-   @subpage tutorial_hdf_create_read_write_datasets

    *Compatibility:* \> OpenCV 3.0

    *Author:* <PERSON><PERSON>

    You will learn how to create, read and write datasets.

-   @subpage tutorial_hdf_read_write_attributes

    *Compatibility:* \> OpenCV 3.4

    *Author:* <PERSON><PERSON>

    You will learn how to read and write attributes.
