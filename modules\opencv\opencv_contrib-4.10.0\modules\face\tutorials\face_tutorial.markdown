Tutorials for face module {#tutorial_table_of_content_face}
=========================

-   @subpage tutorial_face_main

    Face Recognition using OpenCV

-   @subpage tutorial_face_landmark_detection_in_an_image

    *Compatibility:* \> OpenCV 3.3

    *Author:* Sukha<PERSON>

    *Mentor:* <PERSON>

    Face landmark detection in an image using ensemble of regression trees

-   @subpage tutorial_face_training_face_landmark_detector

    *Compatibility:* \> OpenCV 3.3

    *Author:* Sukhad <PERSON>

    *Mentor:* <PERSON>

    Training a face landmark detector using an ensemble of regression trees

-   @subpage tutorial_face_landmark_detection_in_video

    *Compatibility:* \> OpenCV 3.3

    *Author:* Sukhad <PERSON>

    *Mentor:* <PERSON>

    Face lanmark detection in a video running at real time

-   @subpage tutorial_face_swapping_face_landmark_detection

    *Author:* Sukhad <PERSON>

    *Mentor:* <PERSON>

    Basic application to swap faces using face landmark detection
