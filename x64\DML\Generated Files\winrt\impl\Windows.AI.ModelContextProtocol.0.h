// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_AI_ModelContextProtocol_0_H
#define WINRT_Windows_AI_ModelContextProtocol_0_H
WINRT_EXPORT namespace winrt::Windows::ApplicationModel
{
    struct Package;
}
WINRT_EXPORT namespace winrt::Windows::UI
{
    struct WindowId;
}
WINRT_EXPORT namespace winrt::Windows::AI::ModelContextProtocol
{
    struct IModelContextProtocolClientContext;
    struct IModelContextProtocolClientContextFactory;
    struct IModelContextProtocolServer;
    struct IModelContextProtocolServerCatalog;
    struct IModelContextProtocolServerCatalogFactory;
    struct IModelContextProtocolServerInfo;
    struct IModelContextProtocolServerInfoFactory;
    struct ModelContextProtocolClientContext;
    struct ModelContextProtocolServerCatalog;
    struct ModelContextProtocolServerInfo;
    struct ModelContextProtocolContract;
}
namespace winrt::impl
{
    template <> struct category<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContext>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContextFactory>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServer>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalog>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalogFactory>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfo>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfoFactory>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolClientContext>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolServerCatalog>{ using type = class_category; };
    template <> struct category<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolServerInfo>{ using type = class_category; };
    template <> inline constexpr auto& name_v<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolClientContext> = L"Windows.AI.ModelContextProtocol.ModelContextProtocolClientContext";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolServerCatalog> = L"Windows.AI.ModelContextProtocol.ModelContextProtocolServerCatalog";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolServerInfo> = L"Windows.AI.ModelContextProtocol.ModelContextProtocolServerInfo";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContext> = L"Windows.AI.ModelContextProtocol.IModelContextProtocolClientContext";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContextFactory> = L"Windows.AI.ModelContextProtocol.IModelContextProtocolClientContextFactory";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServer> = L"Windows.AI.ModelContextProtocol.IModelContextProtocolServer";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalog> = L"Windows.AI.ModelContextProtocol.IModelContextProtocolServerCatalog";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalogFactory> = L"Windows.AI.ModelContextProtocol.IModelContextProtocolServerCatalogFactory";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfo> = L"Windows.AI.ModelContextProtocol.IModelContextProtocolServerInfo";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfoFactory> = L"Windows.AI.ModelContextProtocol.IModelContextProtocolServerInfoFactory";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolContract> = L"Windows.AI.ModelContextProtocol.ModelContextProtocolContract";
    template <> inline constexpr guid guid_v<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContext>{ 0x5BD93F10,0xC0AA,0x4963,{ 0xB4,0xC5,0xAC,0x4A,0x69,0xBD,0xBB,0x33 } }; // 5BD93F10-C0AA-4963-B4C5-AC4A69BDBB33
    template <> inline constexpr guid guid_v<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContextFactory>{ 0xEFB2BA31,0x148C,0x5AD7,{ 0xAB,0x8F,0x4F,0x0E,0x61,0x54,0xFE,0xDB } }; // EFB2BA31-148C-5AD7-AB8F-4F0E6154FEDB
    template <> inline constexpr guid guid_v<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServer>{ 0x9D78431D,0x533F,0x55DD,{ 0x96,0x92,0xDC,0x14,0x62,0xF0,0xBB,0x39 } }; // 9D78431D-533F-55DD-9692-DC1462F0BB39
    template <> inline constexpr guid guid_v<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalog>{ 0x062B8A5E,0xB124,0x4490,{ 0xA1,0xBA,0x46,0x92,0x87,0x5D,0xF8,0x3E } }; // 062B8A5E-B124-4490-A1BA-4692875DF83E
    template <> inline constexpr guid guid_v<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalogFactory>{ 0x6E5D0E8F,0x77E1,0x5A9D,{ 0xB9,0x80,0x07,0x79,0xB3,0xB1,0x28,0xBA } }; // 6E5D0E8F-77E1-5A9D-B980-0779B3B128BA
    template <> inline constexpr guid guid_v<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfo>{ 0x503102BA,0x831B,0x47E5,{ 0xB9,0x7B,0xE7,0xB0,0x62,0x09,0xDD,0x8B } }; // 503102BA-831B-47E5-B97B-E7B06209DD8B
    template <> inline constexpr guid guid_v<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfoFactory>{ 0xC93EE14E,0xE477,0x5A65,{ 0xBD,0x80,0x2A,0x78,0x60,0xDE,0x7E,0xAD } }; // C93EE14E-E477-5A65-BD80-2A7860DE7EAD
    template <> struct default_interface<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolClientContext>{ using type = winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContext; };
    template <> struct default_interface<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolServerCatalog>{ using type = winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalog; };
    template <> struct default_interface<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolServerInfo>{ using type = winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfo; };
    template <> struct abi<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContext>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall put_OwnerWindowId(struct struct_Windows_UI_WindowId) noexcept = 0;
            virtual int32_t __stdcall get_OwnerWindowId(struct struct_Windows_UI_WindowId*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContextFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Command(void**) noexcept = 0;
            virtual int32_t __stdcall GetCommandArguments(uint32_t* __resultSize, void***) noexcept = 0;
            virtual int32_t __stdcall get_Info(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalog>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetServerInfos(uint32_t* __resultSize, void***) noexcept = 0;
            virtual int32_t __stdcall ActivateServer(winrt::guid, void*, void**) noexcept = 0;
            virtual int32_t __stdcall CreateClientContext(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalogFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfo>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Id(winrt::guid*) noexcept = 0;
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall get_Description(void**) noexcept = 0;
            virtual int32_t __stdcall GetPackage(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfoFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <typename D>
    struct consume_Windows_AI_ModelContextProtocol_IModelContextProtocolClientContext
    {
        auto OwnerWindowId(winrt::Windows::UI::WindowId const& value) const;
        [[nodiscard]] auto OwnerWindowId() const;
    };
    template <> struct consume<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContext>
    {
        template <typename D> using type = consume_Windows_AI_ModelContextProtocol_IModelContextProtocolClientContext<D>;
    };
    template <typename D>
    struct consume_Windows_AI_ModelContextProtocol_IModelContextProtocolClientContextFactory
    {
    };
    template <> struct consume<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContextFactory>
    {
        template <typename D> using type = consume_Windows_AI_ModelContextProtocol_IModelContextProtocolClientContextFactory<D>;
    };
    template <typename D>
    struct consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServer
    {
        [[nodiscard]] auto Command() const;
        auto GetCommandArguments() const;
        [[nodiscard]] auto Info() const;
    };
    template <> struct consume<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServer>
    {
        template <typename D> using type = consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServer<D>;
    };
    template <typename D>
    struct consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServerCatalog
    {
        auto GetServerInfos() const;
        auto ActivateServer(winrt::guid const& serverId, winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolClientContext const& client) const;
        auto CreateClientContext() const;
    };
    template <> struct consume<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalog>
    {
        template <typename D> using type = consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServerCatalog<D>;
    };
    template <typename D>
    struct consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServerCatalogFactory
    {
    };
    template <> struct consume<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalogFactory>
    {
        template <typename D> using type = consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServerCatalogFactory<D>;
    };
    template <typename D>
    struct consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServerInfo
    {
        [[nodiscard]] auto Id() const;
        [[nodiscard]] auto Name() const;
        [[nodiscard]] auto Description() const;
        auto GetPackage() const;
    };
    template <> struct consume<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfo>
    {
        template <typename D> using type = consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServerInfo<D>;
    };
    template <typename D>
    struct consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServerInfoFactory
    {
    };
    template <> struct consume<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfoFactory>
    {
        template <typename D> using type = consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServerInfoFactory<D>;
    };
}
#endif
