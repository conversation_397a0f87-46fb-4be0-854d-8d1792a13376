// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_UI_Input_1_H
#define WINRT_Windows_UI_Input_1_H
#include "winrt/impl/Windows.UI.Input.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Input
{
    struct WINRT_IMPL_EMPTY_BASES IAttachableInputObject :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAttachableInputObject>
    {
        IAttachableInputObject(std::nullptr_t = nullptr) noexcept {}
        IAttachableInputObject(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAttachableInputObjectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAttachableInputObjectFactory>
    {
        IAttachableInputObjectFactory(std::nullptr_t = nullptr) noexcept {}
        IAttachableInputObjectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICrossSlidingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICrossSlidingEventArgs>
    {
        ICrossSlidingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICrossSlidingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICrossSlidingEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICrossSlidingEventArgs2>
    {
        ICrossSlidingEventArgs2(std::nullptr_t = nullptr) noexcept {}
        ICrossSlidingEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDraggingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDraggingEventArgs>
    {
        IDraggingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDraggingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDraggingEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDraggingEventArgs2>
    {
        IDraggingEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IDraggingEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEdgeGesture :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEdgeGesture>
    {
        IEdgeGesture(std::nullptr_t = nullptr) noexcept {}
        IEdgeGesture(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEdgeGestureEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEdgeGestureEventArgs>
    {
        IEdgeGestureEventArgs(std::nullptr_t = nullptr) noexcept {}
        IEdgeGestureEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEdgeGestureStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEdgeGestureStatics>
    {
        IEdgeGestureStatics(std::nullptr_t = nullptr) noexcept {}
        IEdgeGestureStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGestureRecognizer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGestureRecognizer>
    {
        IGestureRecognizer(std::nullptr_t = nullptr) noexcept {}
        IGestureRecognizer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGestureRecognizer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGestureRecognizer2>
    {
        IGestureRecognizer2(std::nullptr_t = nullptr) noexcept {}
        IGestureRecognizer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHoldingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHoldingEventArgs>
    {
        IHoldingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IHoldingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHoldingEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHoldingEventArgs2>
    {
        IHoldingEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IHoldingEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInputActivationListener :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInputActivationListener>
    {
        IInputActivationListener(std::nullptr_t = nullptr) noexcept {}
        IInputActivationListener(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInputActivationListenerActivationChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInputActivationListenerActivationChangedEventArgs>
    {
        IInputActivationListenerActivationChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IInputActivationListenerActivationChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyboardDeliveryInterceptor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyboardDeliveryInterceptor>
    {
        IKeyboardDeliveryInterceptor(std::nullptr_t = nullptr) noexcept {}
        IKeyboardDeliveryInterceptor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyboardDeliveryInterceptorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyboardDeliveryInterceptorStatics>
    {
        IKeyboardDeliveryInterceptorStatics(std::nullptr_t = nullptr) noexcept {}
        IKeyboardDeliveryInterceptorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IManipulationCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManipulationCompletedEventArgs>
    {
        IManipulationCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IManipulationCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IManipulationCompletedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManipulationCompletedEventArgs2>
    {
        IManipulationCompletedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IManipulationCompletedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IManipulationInertiaStartingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManipulationInertiaStartingEventArgs>
    {
        IManipulationInertiaStartingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IManipulationInertiaStartingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IManipulationInertiaStartingEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManipulationInertiaStartingEventArgs2>
    {
        IManipulationInertiaStartingEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IManipulationInertiaStartingEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IManipulationStartedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManipulationStartedEventArgs>
    {
        IManipulationStartedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IManipulationStartedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IManipulationStartedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManipulationStartedEventArgs2>
    {
        IManipulationStartedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IManipulationStartedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IManipulationUpdatedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManipulationUpdatedEventArgs>
    {
        IManipulationUpdatedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IManipulationUpdatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IManipulationUpdatedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManipulationUpdatedEventArgs2>
    {
        IManipulationUpdatedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IManipulationUpdatedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMouseWheelParameters :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMouseWheelParameters>
    {
        IMouseWheelParameters(std::nullptr_t = nullptr) noexcept {}
        IMouseWheelParameters(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhysicalGestureRecognizer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhysicalGestureRecognizer>
    {
        IPhysicalGestureRecognizer(std::nullptr_t = nullptr) noexcept {}
        IPhysicalGestureRecognizer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerPoint :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerPoint>
    {
        IPointerPoint(std::nullptr_t = nullptr) noexcept {}
        IPointerPoint(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerPointPhysicalPosition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerPointPhysicalPosition>
    {
        IPointerPointPhysicalPosition(std::nullptr_t = nullptr) noexcept {}
        IPointerPointPhysicalPosition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerPointProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerPointProperties>
    {
        IPointerPointProperties(std::nullptr_t = nullptr) noexcept {}
        IPointerPointProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerPointProperties2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerPointProperties2>
    {
        IPointerPointProperties2(std::nullptr_t = nullptr) noexcept {}
        IPointerPointProperties2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerPointStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerPointStatics>
    {
        IPointerPointStatics(std::nullptr_t = nullptr) noexcept {}
        IPointerPointStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerPointTransform :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerPointTransform>
    {
        IPointerPointTransform(std::nullptr_t = nullptr) noexcept {}
        IPointerPointTransform(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerVisualizationSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerVisualizationSettings>
    {
        IPointerVisualizationSettings(std::nullptr_t = nullptr) noexcept {}
        IPointerVisualizationSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerVisualizationSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerVisualizationSettingsStatics>
    {
        IPointerVisualizationSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        IPointerVisualizationSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialController>
    {
        IRadialController(std::nullptr_t = nullptr) noexcept {}
        IRadialController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialController2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialController2>
    {
        IRadialController2(std::nullptr_t = nullptr) noexcept {}
        IRadialController2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerButtonClickedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerButtonClickedEventArgs>
    {
        IRadialControllerButtonClickedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerButtonClickedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerButtonClickedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerButtonClickedEventArgs2>
    {
        IRadialControllerButtonClickedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerButtonClickedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerButtonHoldingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerButtonHoldingEventArgs>
    {
        IRadialControllerButtonHoldingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerButtonHoldingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerButtonPressedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerButtonPressedEventArgs>
    {
        IRadialControllerButtonPressedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerButtonPressedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerButtonReleasedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerButtonReleasedEventArgs>
    {
        IRadialControllerButtonReleasedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerButtonReleasedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerConfiguration :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerConfiguration>
    {
        IRadialControllerConfiguration(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerConfiguration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerConfiguration2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerConfiguration2>
    {
        IRadialControllerConfiguration2(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerConfiguration2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerConfigurationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerConfigurationStatics>
    {
        IRadialControllerConfigurationStatics(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerConfigurationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerConfigurationStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerConfigurationStatics2>
    {
        IRadialControllerConfigurationStatics2(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerConfigurationStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerControlAcquiredEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerControlAcquiredEventArgs>
    {
        IRadialControllerControlAcquiredEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerControlAcquiredEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerControlAcquiredEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerControlAcquiredEventArgs2>
    {
        IRadialControllerControlAcquiredEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerControlAcquiredEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerMenu :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerMenu>
    {
        IRadialControllerMenu(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerMenu(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerMenuItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerMenuItem>
    {
        IRadialControllerMenuItem(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerMenuItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerMenuItemStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerMenuItemStatics>
    {
        IRadialControllerMenuItemStatics(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerMenuItemStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerMenuItemStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerMenuItemStatics2>
    {
        IRadialControllerMenuItemStatics2(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerMenuItemStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerRotationChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerRotationChangedEventArgs>
    {
        IRadialControllerRotationChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerRotationChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerRotationChangedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerRotationChangedEventArgs2>
    {
        IRadialControllerRotationChangedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerRotationChangedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerScreenContact :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerScreenContact>
    {
        IRadialControllerScreenContact(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerScreenContact(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerScreenContactContinuedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerScreenContactContinuedEventArgs>
    {
        IRadialControllerScreenContactContinuedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerScreenContactContinuedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerScreenContactContinuedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerScreenContactContinuedEventArgs2>
    {
        IRadialControllerScreenContactContinuedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerScreenContactContinuedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerScreenContactEndedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerScreenContactEndedEventArgs>
    {
        IRadialControllerScreenContactEndedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerScreenContactEndedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerScreenContactStartedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerScreenContactStartedEventArgs>
    {
        IRadialControllerScreenContactStartedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerScreenContactStartedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerScreenContactStartedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerScreenContactStartedEventArgs2>
    {
        IRadialControllerScreenContactStartedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerScreenContactStartedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialControllerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialControllerStatics>
    {
        IRadialControllerStatics(std::nullptr_t = nullptr) noexcept {}
        IRadialControllerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRightTappedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRightTappedEventArgs>
    {
        IRightTappedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRightTappedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRightTappedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRightTappedEventArgs2>
    {
        IRightTappedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IRightTappedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemButtonEventController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemButtonEventController>
    {
        ISystemButtonEventController(std::nullptr_t = nullptr) noexcept {}
        ISystemButtonEventController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemButtonEventControllerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemButtonEventControllerStatics>
    {
        ISystemButtonEventControllerStatics(std::nullptr_t = nullptr) noexcept {}
        ISystemButtonEventControllerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemFunctionButtonEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemFunctionButtonEventArgs>
    {
        ISystemFunctionButtonEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISystemFunctionButtonEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemFunctionLockChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemFunctionLockChangedEventArgs>
    {
        ISystemFunctionLockChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISystemFunctionLockChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemFunctionLockIndicatorChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemFunctionLockIndicatorChangedEventArgs>
    {
        ISystemFunctionLockIndicatorChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISystemFunctionLockIndicatorChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITappedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITappedEventArgs>
    {
        ITappedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITappedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITappedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITappedEventArgs2>
    {
        ITappedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        ITappedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITouchpadGesturesController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITouchpadGesturesController>
    {
        ITouchpadGesturesController(std::nullptr_t = nullptr) noexcept {}
        ITouchpadGesturesController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITouchpadGesturesControllerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITouchpadGesturesControllerStatics>
    {
        ITouchpadGesturesControllerStatics(std::nullptr_t = nullptr) noexcept {}
        ITouchpadGesturesControllerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITouchpadGlobalActionEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITouchpadGlobalActionEventArgs>
    {
        ITouchpadGlobalActionEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITouchpadGlobalActionEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
