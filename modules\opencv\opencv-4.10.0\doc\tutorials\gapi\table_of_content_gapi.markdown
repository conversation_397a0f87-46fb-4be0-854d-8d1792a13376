# Graph API (gapi module) {#tutorial_table_of_content_gapi}

In this section you will learn about graph-based image processing and
how G-API module can be used for that.

- @subpage tutorial_gapi_interactive_face_detection

    *Languages:* C++

    *Compatibility:* \> OpenCV 4.2

    *Author:* <PERSON>ev

    This tutorial illustrates how to build a hybrid video processing
    pipeline with G-API where Deep Learning and image processing are
    combined effectively to maximize the overall throughput. This
    sample requires Intel® distribution of OpenVINO™ Toolkit version
    2019R2 or later.

- @subpage tutorial_gapi_anisotropic_segmentation

    *Languages:* C++

    *Compatibility:* \> OpenCV 4.0

    *Author:* Dmitry Matveev

    This is an end-to-end tutorial where an existing sample algorithm
    is ported on G-API, covering the basic intuition behind this
    transition process, and examining benefits which a graph model
    brings there.

- @subpage tutorial_gapi_face_beautification

    *Languages:* C++

    *Compatibility:* \> OpenCV 4.2

    *Author:* Orest Chura

    In this tutorial we build a complex hybrid Computer Vision/Deep
    Learning video processing pipeline with G-API.


- @subpage tutorial_gapi_oak_devices

    *Languages:* C++

    *Compatibility:* \> OpenCV 4.6

    *Author:* <PERSON> (A.K.A. CABEL<PERSON>)

    In this tutorial we showed how to use the Luxonis DepthAI library with G-API.
