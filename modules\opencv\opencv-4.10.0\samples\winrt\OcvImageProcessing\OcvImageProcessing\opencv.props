﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Label="PropertySheets" />
  <PropertyGroup Label="UserMacros">
    <OpenCV_Bin>$(OPENCV_WINRT_INSTALL_DIR)\WS\8.1\$(PlatformTarget)\$(PlatformTarget)\vc12\bin\</OpenCV_Bin>
    <OpenCV_Lib>$(OPENCV_WINRT_INSTALL_DIR)\WS\8.1\$(PlatformTarget)\$(PlatformTarget)\vc12\lib\</OpenCV_Lib>
    <OpenCV_Include>$(OPENCV_WINRT_INSTALL_DIR)\WS\8.1\$(PlatformTarget)\include\</OpenCV_Include>
    <!--debug suffix for OpenCV dlls and libs -->
    <DebugSuffix Condition="'$(Configuration)'=='Debug'">d</DebugSuffix>
    <DebugSuffix Condition="'$(Configuration)'!='Debug'"></DebugSuffix>
  </PropertyGroup>
  <ItemGroup>
    <!--Add required OpenCV dlls here-->
    <None Include="$(OpenCV_Bin)opencv_core300$(DebugSuffix).dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
    <None Include="$(OpenCV_Bin)opencv_imgproc300$(DebugSuffix).dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
    <None Include="$(OpenCV_Bin)opencv_imgcodecs300$(DebugSuffix).dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
    <None Include="$(OpenCV_Bin)opencv_features2d300$(DebugSuffix).dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
    <None Include="$(OpenCV_Bin)opencv_flann300$(DebugSuffix).dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
    <None Include="$(OpenCV_Bin)opencv_ml300$(DebugSuffix).dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
  </ItemGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <AdditionalIncludeDirectories>$(OpenCV_Include);$(ProjectDir);$(GeneratedFilesDir);$(IntDir);%(AdditionalIncludeDirectories);</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <!--Add required OpenCV libs here-->
      <AdditionalDependencies>opencv_core300$(DebugSuffix).lib;opencv_imgproc300$(DebugSuffix).lib;opencv_features2d300$(DebugSuffix).lib;opencv_flann300$(DebugSuffix).lib;opencv_ml300$(DebugSuffix).lib;opencv_imgcodecs300$(DebugSuffix).lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(OpenCV_Lib);%(AdditionalLibraryDirectories);</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
</Project>