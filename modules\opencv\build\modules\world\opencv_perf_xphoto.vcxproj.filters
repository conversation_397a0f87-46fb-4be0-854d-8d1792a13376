﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\perf\perf_grayworld.cpp">
      <Filter>opencv_xphoto\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\perf\perf_learning_based_color_balance.cpp">
      <Filter>opencv_xphoto\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\perf\perf_main.cpp">
      <Filter>opencv_xphoto\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\xphoto\perf\perf_precomp.hpp">
      <Filter>opencv_xphoto\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_xphoto">
      <UniqueIdentifier>{69BDCA84-68EB-3C4E-B634-A5AF2F28645A}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_xphoto\Include">
      <UniqueIdentifier>{415D2144-469F-36C0-B6E0-EA5C3C74667B}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_xphoto\Src">
      <UniqueIdentifier>{A66E0B02-630F-33F4-AE3B-E26E9AF5EA47}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
