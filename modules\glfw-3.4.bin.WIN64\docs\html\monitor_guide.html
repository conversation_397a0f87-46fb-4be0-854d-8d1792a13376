<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Monitor guide</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div><div class="header">
  <div class="headertitle"><div class="title">Monitor guide</div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#monitor_object">Monitor objects</a><ul><li class="level2"><a href="#monitor_monitors">Retrieving monitors</a></li>
<li class="level2"><a href="#monitor_event">Monitor configuration changes</a></li>
</ul>
</li>
<li class="level1"><a href="#monitor_properties">Monitor properties</a><ul><li class="level2"><a href="#monitor_modes">Video modes</a></li>
<li class="level2"><a href="#monitor_size">Physical size</a></li>
<li class="level2"><a href="#monitor_scale">Content scale</a></li>
<li class="level2"><a href="#monitor_pos">Virtual position</a></li>
<li class="level2"><a href="#monitor_workarea">Work area</a></li>
<li class="level2"><a href="#monitor_name">Human-readable name</a></li>
<li class="level2"><a href="#monitor_userptr">User pointer</a></li>
<li class="level2"><a href="#monitor_gamma">Gamma ramp</a></li>
</ul>
</li>
</ul>
</div>
<div class="textblock"><p>This guide introduces the monitor related functions of GLFW. For details on a specific function in this category, see the <a class="el" href="group__monitor.html">Monitor reference</a>. There are also guides for the other areas of GLFW.</p>
<ul>
<li><a class="el" href="intro_guide.html">Introduction to the API</a></li>
<li><a class="el" href="window_guide.html">Window guide</a></li>
<li><a class="el" href="context_guide.html">Context guide</a></li>
<li><a class="el" href="vulkan_guide.html">Vulkan guide</a></li>
<li><a class="el" href="input_guide.html">Input guide</a></li>
</ul>
<h1><a class="anchor" id="monitor_object"></a>
Monitor objects</h1>
<p>A monitor object represents a currently connected monitor and is represented as a pointer to the <a href="https://en.wikipedia.org/wiki/Opaque_data_type">opaque</a> type <a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>. Monitor objects cannot be created or destroyed by the application and retain their addresses until the monitors they represent are disconnected or until the library is <a class="el" href="intro_guide.html#intro_init_terminate">terminated</a>.</p>
<p>Each monitor has a current video mode, a list of supported video modes, a virtual position, a human-readable name, an estimated physical size and a gamma ramp. One of the monitors is the primary monitor.</p>
<p>The virtual position of a monitor is in <a class="el" href="intro_guide.html#coordinate_systems">screen coordinates</a> and, together with the current video mode, describes the viewports that the connected monitors provide into the virtual desktop that spans them.</p>
<p>To see how GLFW views your monitor setup and its available video modes, run the <code>monitors</code> test program.</p>
<h2><a class="anchor" id="monitor_monitors"></a>
Retrieving monitors</h2>
<p>The primary monitor is returned by <a class="el" href="group__monitor.html#gac3adb24947eb709e1874028272e5dfc5">glfwGetPrimaryMonitor</a>. It is the user's preferred monitor and is usually the one with global UI elements like task bar or menu bar.</p>
<div class="fragment"><div class="line"><a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* primary = <a class="code hl_function" href="group__monitor.html#gac3adb24947eb709e1874028272e5dfc5">glfwGetPrimaryMonitor</a>();</div>
<div class="ttc" id="agroup__monitor_html_ga8d9efd1cde9426692c73fe40437d0ae3"><div class="ttname"><a href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a></div><div class="ttdeci">struct GLFWmonitor GLFWmonitor</div><div class="ttdoc">Opaque monitor object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1391</div></div>
<div class="ttc" id="agroup__monitor_html_gac3adb24947eb709e1874028272e5dfc5"><div class="ttname"><a href="group__monitor.html#gac3adb24947eb709e1874028272e5dfc5">glfwGetPrimaryMonitor</a></div><div class="ttdeci">GLFWmonitor * glfwGetPrimaryMonitor(void)</div><div class="ttdoc">Returns the primary monitor.</div></div>
</div><!-- fragment --><p>You can retrieve all currently connected monitors with <a class="el" href="group__monitor.html#ga70b1156d5d24e9928f145d6c864369d2">glfwGetMonitors</a>. See the reference documentation for the lifetime of the returned array.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> count;</div>
<div class="line"><a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>** monitors = <a class="code hl_function" href="group__monitor.html#ga70b1156d5d24e9928f145d6c864369d2">glfwGetMonitors</a>(&amp;count);</div>
<div class="ttc" id="agroup__monitor_html_ga70b1156d5d24e9928f145d6c864369d2"><div class="ttname"><a href="group__monitor.html#ga70b1156d5d24e9928f145d6c864369d2">glfwGetMonitors</a></div><div class="ttdeci">GLFWmonitor ** glfwGetMonitors(int *count)</div><div class="ttdoc">Returns the currently connected monitors.</div></div>
</div><!-- fragment --><p>The primary monitor is always the first monitor in the returned array, but other monitors may be moved to a different index when a monitor is connected or disconnected.</p>
<h2><a class="anchor" id="monitor_event"></a>
Monitor configuration changes</h2>
<p>If you wish to be notified when a monitor is connected or disconnected, set a monitor callback.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__monitor.html#gab39df645587c8518192aa746c2fb06c3">glfwSetMonitorCallback</a>(monitor_callback);</div>
<div class="ttc" id="agroup__monitor_html_gab39df645587c8518192aa746c2fb06c3"><div class="ttname"><a href="group__monitor.html#gab39df645587c8518192aa746c2fb06c3">glfwSetMonitorCallback</a></div><div class="ttdeci">GLFWmonitorfun glfwSetMonitorCallback(GLFWmonitorfun callback)</div><div class="ttdoc">Sets the monitor configuration callback.</div></div>
</div><!-- fragment --><p>The callback function receives the handle for the monitor that has been connected or disconnected and the event that occurred.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> monitor_callback(<a class="code hl_typedef" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a>* monitor, <span class="keywordtype">int</span> event)</div>
<div class="line">{</div>
<div class="line">    <span class="keywordflow">if</span> (event == <a class="code hl_define" href="glfw3_8h.html#abe11513fd1ffbee5bb9b173f06028b9e">GLFW_CONNECTED</a>)</div>
<div class="line">    {</div>
<div class="line">        <span class="comment">// The monitor was connected</span></div>
<div class="line">    }</div>
<div class="line">    <span class="keywordflow">else</span> <span class="keywordflow">if</span> (event == <a class="code hl_define" href="glfw3_8h.html#aab64b25921ef21d89252d6f0a71bfc32">GLFW_DISCONNECTED</a>)</div>
<div class="line">    {</div>
<div class="line">        <span class="comment">// The monitor was disconnected</span></div>
<div class="line">    }</div>
<div class="line">}</div>
<div class="ttc" id="aglfw3_8h_html_aab64b25921ef21d89252d6f0a71bfc32"><div class="ttname"><a href="glfw3_8h.html#aab64b25921ef21d89252d6f0a71bfc32">GLFW_DISCONNECTED</a></div><div class="ttdeci">#define GLFW_DISCONNECTED</div><div class="ttdef"><b>Definition</b> glfw3.h:1291</div></div>
<div class="ttc" id="aglfw3_8h_html_abe11513fd1ffbee5bb9b173f06028b9e"><div class="ttname"><a href="glfw3_8h.html#abe11513fd1ffbee5bb9b173f06028b9e">GLFW_CONNECTED</a></div><div class="ttdeci">#define GLFW_CONNECTED</div><div class="ttdef"><b>Definition</b> glfw3.h:1290</div></div>
</div><!-- fragment --><p>If a monitor is disconnected, all windows that are full screen on it will be switched to windowed mode before the callback is called. Only <a class="el" href="group__monitor.html#ga7af83e13489d90379588fb331b9e4b68">glfwGetMonitorName</a> and <a class="el" href="group__monitor.html#ga1adbfbfb8cd58b23cfee82e574fbbdc5">glfwGetMonitorUserPointer</a> will return useful values for a disconnected monitor and only before the monitor callback returns.</p>
<h1><a class="anchor" id="monitor_properties"></a>
Monitor properties</h1>
<p>Each monitor has a current video mode, a list of supported video modes, a virtual position, a content scale, a human-readable name, a user pointer, an estimated physical size and a gamma ramp.</p>
<h2><a class="anchor" id="monitor_modes"></a>
Video modes</h2>
<p>GLFW generally does a good job selecting a suitable video mode when you create a full screen window, change its video mode or make a windowed one full screen, but it is sometimes useful to know exactly which video modes are supported.</p>
<p>Video modes are represented as <a class="el" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a> structures. You can get an array of the video modes supported by a monitor with <a class="el" href="group__monitor.html#gad2e24d2843cb7d6c26202cddd530fc1b">glfwGetVideoModes</a>. See the reference documentation for the lifetime of the returned array.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> count;</div>
<div class="line"><a class="code hl_struct" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a>* modes = <a class="code hl_function" href="group__monitor.html#gad2e24d2843cb7d6c26202cddd530fc1b">glfwGetVideoModes</a>(monitor, &amp;count);</div>
<div class="ttc" id="agroup__monitor_html_gad2e24d2843cb7d6c26202cddd530fc1b"><div class="ttname"><a href="group__monitor.html#gad2e24d2843cb7d6c26202cddd530fc1b">glfwGetVideoModes</a></div><div class="ttdeci">const GLFWvidmode * glfwGetVideoModes(GLFWmonitor *monitor, int *count)</div><div class="ttdoc">Returns the available video modes for the specified monitor.</div></div>
<div class="ttc" id="astruct_g_l_f_wvidmode_html"><div class="ttname"><a href="struct_g_l_f_wvidmode.html">GLFWvidmode</a></div><div class="ttdoc">Video mode type.</div><div class="ttdef"><b>Definition</b> glfw3.h:2027</div></div>
</div><!-- fragment --><p>To get the current video mode of a monitor call <a class="el" href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a>. See the reference documentation for the lifetime of the returned pointer.</p>
<div class="fragment"><div class="line"><span class="keyword">const</span> <a class="code hl_struct" href="struct_g_l_f_wvidmode.html">GLFWvidmode</a>* mode = <a class="code hl_function" href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a>(monitor);</div>
<div class="ttc" id="agroup__monitor_html_gaba376fa7e76634b4788bddc505d6c9d5"><div class="ttname"><a href="group__monitor.html#gaba376fa7e76634b4788bddc505d6c9d5">glfwGetVideoMode</a></div><div class="ttdeci">const GLFWvidmode * glfwGetVideoMode(GLFWmonitor *monitor)</div><div class="ttdoc">Returns the current mode of the specified monitor.</div></div>
</div><!-- fragment --><p>The resolution of a video mode is specified in <a class="el" href="intro_guide.html#coordinate_systems">screen coordinates</a>, not pixels.</p>
<h2><a class="anchor" id="monitor_size"></a>
Physical size</h2>
<p>The physical size of a monitor in millimetres, or an estimation of it, can be retrieved with <a class="el" href="group__monitor.html#ga7d8bffc6c55539286a6bd20d32a8d7ea">glfwGetMonitorPhysicalSize</a>. This has no relation to its current <em>resolution</em>, i.e. the width and height of its current <a class="el" href="monitor_guide.html#monitor_modes">video mode</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> width_mm, height_mm;</div>
<div class="line"><a class="code hl_function" href="group__monitor.html#ga7d8bffc6c55539286a6bd20d32a8d7ea">glfwGetMonitorPhysicalSize</a>(monitor, &amp;width_mm, &amp;height_mm);</div>
<div class="ttc" id="agroup__monitor_html_ga7d8bffc6c55539286a6bd20d32a8d7ea"><div class="ttname"><a href="group__monitor.html#ga7d8bffc6c55539286a6bd20d32a8d7ea">glfwGetMonitorPhysicalSize</a></div><div class="ttdeci">void glfwGetMonitorPhysicalSize(GLFWmonitor *monitor, int *widthMM, int *heightMM)</div><div class="ttdoc">Returns the physical size of the monitor.</div></div>
</div><!-- fragment --><p>While this can be used to calculate the raw DPI of a monitor, this is often not useful. Instead, use the <a class="el" href="monitor_guide.html#monitor_scale">monitor content scale</a> and <a class="el" href="window_guide.html#window_scale">window content scale</a> to scale your content.</p>
<h2><a class="anchor" id="monitor_scale"></a>
Content scale</h2>
<p>The content scale for a monitor can be retrieved with <a class="el" href="group__monitor.html#gad3152e84465fa620b601265ebfcdb21b">glfwGetMonitorContentScale</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">float</span> xscale, yscale;</div>
<div class="line"><a class="code hl_function" href="group__monitor.html#gad3152e84465fa620b601265ebfcdb21b">glfwGetMonitorContentScale</a>(monitor, &amp;xscale, &amp;yscale);</div>
<div class="ttc" id="agroup__monitor_html_gad3152e84465fa620b601265ebfcdb21b"><div class="ttname"><a href="group__monitor.html#gad3152e84465fa620b601265ebfcdb21b">glfwGetMonitorContentScale</a></div><div class="ttdeci">void glfwGetMonitorContentScale(GLFWmonitor *monitor, float *xscale, float *yscale)</div><div class="ttdoc">Retrieves the content scale for the specified monitor.</div></div>
</div><!-- fragment --><p>For more information on what the content scale is and how to use it, see <a class="el" href="window_guide.html#window_scale">window content scale</a>.</p>
<h2><a class="anchor" id="monitor_pos"></a>
Virtual position</h2>
<p>The position of the monitor on the virtual desktop, in <a class="el" href="intro_guide.html#coordinate_systems">screen coordinates</a>, can be retrieved with <a class="el" href="group__monitor.html#ga102f54e7acc9149edbcf0997152df8c9">glfwGetMonitorPos</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> xpos, ypos;</div>
<div class="line"><a class="code hl_function" href="group__monitor.html#ga102f54e7acc9149edbcf0997152df8c9">glfwGetMonitorPos</a>(monitor, &amp;xpos, &amp;ypos);</div>
<div class="ttc" id="agroup__monitor_html_ga102f54e7acc9149edbcf0997152df8c9"><div class="ttname"><a href="group__monitor.html#ga102f54e7acc9149edbcf0997152df8c9">glfwGetMonitorPos</a></div><div class="ttdeci">void glfwGetMonitorPos(GLFWmonitor *monitor, int *xpos, int *ypos)</div><div class="ttdoc">Returns the position of the monitor's viewport on the virtual screen.</div></div>
</div><!-- fragment --><h2><a class="anchor" id="monitor_workarea"></a>
Work area</h2>
<p>The area of a monitor not occupied by global task bars or menu bars is the work area. This is specified in <a class="el" href="intro_guide.html#coordinate_systems">screen coordinates</a> and can be retrieved with <a class="el" href="group__monitor.html#ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0">glfwGetMonitorWorkarea</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> xpos, ypos, width, height;</div>
<div class="line"><a class="code hl_function" href="group__monitor.html#ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0">glfwGetMonitorWorkarea</a>(monitor, &amp;xpos, &amp;ypos, &amp;width, &amp;height);</div>
<div class="ttc" id="agroup__monitor_html_ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0"><div class="ttname"><a href="group__monitor.html#ga7387a3bdb64bfe8ebf2b9e54f5b6c9d0">glfwGetMonitorWorkarea</a></div><div class="ttdeci">void glfwGetMonitorWorkarea(GLFWmonitor *monitor, int *xpos, int *ypos, int *width, int *height)</div><div class="ttdoc">Retrieves the work area of the monitor.</div></div>
</div><!-- fragment --><h2><a class="anchor" id="monitor_name"></a>
Human-readable name</h2>
<p>The human-readable, UTF-8 encoded name of a monitor is returned by <a class="el" href="group__monitor.html#ga7af83e13489d90379588fb331b9e4b68">glfwGetMonitorName</a>. See the reference documentation for the lifetime of the returned string.</p>
<div class="fragment"><div class="line"><span class="keyword">const</span> <span class="keywordtype">char</span>* name = <a class="code hl_function" href="group__monitor.html#ga7af83e13489d90379588fb331b9e4b68">glfwGetMonitorName</a>(monitor);</div>
<div class="ttc" id="agroup__monitor_html_ga7af83e13489d90379588fb331b9e4b68"><div class="ttname"><a href="group__monitor.html#ga7af83e13489d90379588fb331b9e4b68">glfwGetMonitorName</a></div><div class="ttdeci">const char * glfwGetMonitorName(GLFWmonitor *monitor)</div><div class="ttdoc">Returns the name of the specified monitor.</div></div>
</div><!-- fragment --><p>Monitor names are not guaranteed to be unique. Two monitors of the same model and make may have the same name. Only the monitor handle is guaranteed to be unique, and only until that monitor is disconnected.</p>
<h2><a class="anchor" id="monitor_userptr"></a>
User pointer</h2>
<p>Each monitor has a user pointer that can be set with <a class="el" href="group__monitor.html#ga702750e24313a686d3637297b6e85fda">glfwSetMonitorUserPointer</a> and queried with <a class="el" href="group__monitor.html#ga1adbfbfb8cd58b23cfee82e574fbbdc5">glfwGetMonitorUserPointer</a>. This can be used for any purpose you need and will not be modified by GLFW. The value will be kept until the monitor is disconnected or until the library is terminated.</p>
<p>The initial value of the pointer is <code>NULL</code>.</p>
<h2><a class="anchor" id="monitor_gamma"></a>
Gamma ramp</h2>
<p>The gamma ramp of a monitor can be set with <a class="el" href="group__monitor.html#ga583f0ffd0d29613d8cd172b996bbf0dd">glfwSetGammaRamp</a>, which accepts a monitor handle and a pointer to a <a class="el" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a> structure.</p>
<div class="fragment"><div class="line"><a class="code hl_struct" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a> ramp;</div>
<div class="line"><span class="keywordtype">unsigned</span> <span class="keywordtype">short</span> red[256], green[256], blue[256];</div>
<div class="line"> </div>
<div class="line">ramp.<a class="code hl_variable" href="struct_g_l_f_wgammaramp.html#ad620e1cffbff9a32c51bca46301b59a5">size</a> = 256;</div>
<div class="line">ramp.<a class="code hl_variable" href="struct_g_l_f_wgammaramp.html#a2cce5d968734b685623eef913e635138">red</a> = red;</div>
<div class="line">ramp.<a class="code hl_variable" href="struct_g_l_f_wgammaramp.html#affccc6f5df47820b6562d709da3a5a3a">green</a> = green;</div>
<div class="line">ramp.<a class="code hl_variable" href="struct_g_l_f_wgammaramp.html#acf0c836d0efe29c392fe8d1a1042744b">blue</a> = blue;</div>
<div class="line"> </div>
<div class="line"><span class="keywordflow">for</span> (i = 0;  i &lt; ramp.<a class="code hl_variable" href="struct_g_l_f_wgammaramp.html#ad620e1cffbff9a32c51bca46301b59a5">size</a>;  i++)</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// Fill out gamma ramp arrays as desired</span></div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><a class="code hl_function" href="group__monitor.html#ga583f0ffd0d29613d8cd172b996bbf0dd">glfwSetGammaRamp</a>(monitor, &amp;ramp);</div>
<div class="ttc" id="agroup__monitor_html_ga583f0ffd0d29613d8cd172b996bbf0dd"><div class="ttname"><a href="group__monitor.html#ga583f0ffd0d29613d8cd172b996bbf0dd">glfwSetGammaRamp</a></div><div class="ttdeci">void glfwSetGammaRamp(GLFWmonitor *monitor, const GLFWgammaramp *ramp)</div><div class="ttdoc">Sets the current gamma ramp for the specified monitor.</div></div>
<div class="ttc" id="astruct_g_l_f_wgammaramp_html"><div class="ttname"><a href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a></div><div class="ttdoc">Gamma ramp.</div><div class="ttdef"><b>Definition</b> glfw3.h:2061</div></div>
<div class="ttc" id="astruct_g_l_f_wgammaramp_html_a2cce5d968734b685623eef913e635138"><div class="ttname"><a href="struct_g_l_f_wgammaramp.html#a2cce5d968734b685623eef913e635138">GLFWgammaramp::red</a></div><div class="ttdeci">unsigned short * red</div><div class="ttdef"><b>Definition</b> glfw3.h:2064</div></div>
<div class="ttc" id="astruct_g_l_f_wgammaramp_html_acf0c836d0efe29c392fe8d1a1042744b"><div class="ttname"><a href="struct_g_l_f_wgammaramp.html#acf0c836d0efe29c392fe8d1a1042744b">GLFWgammaramp::blue</a></div><div class="ttdeci">unsigned short * blue</div><div class="ttdef"><b>Definition</b> glfw3.h:2070</div></div>
<div class="ttc" id="astruct_g_l_f_wgammaramp_html_ad620e1cffbff9a32c51bca46301b59a5"><div class="ttname"><a href="struct_g_l_f_wgammaramp.html#ad620e1cffbff9a32c51bca46301b59a5">GLFWgammaramp::size</a></div><div class="ttdeci">unsigned int size</div><div class="ttdef"><b>Definition</b> glfw3.h:2073</div></div>
<div class="ttc" id="astruct_g_l_f_wgammaramp_html_affccc6f5df47820b6562d709da3a5a3a"><div class="ttname"><a href="struct_g_l_f_wgammaramp.html#affccc6f5df47820b6562d709da3a5a3a">GLFWgammaramp::green</a></div><div class="ttdeci">unsigned short * green</div><div class="ttdef"><b>Definition</b> glfw3.h:2067</div></div>
</div><!-- fragment --><p>The gamma ramp data is copied before the function returns, so there is no need to keep it around once the ramp has been set.</p>
<p>It is recommended that your gamma ramp have the same size as the current gamma ramp for that monitor.</p>
<p>The current gamma ramp for a monitor is returned by <a class="el" href="group__monitor.html#ga76ba90debcf0062b5c4b73052b24f96f">glfwGetGammaRamp</a>. See the reference documentation for the lifetime of the returned structure.</p>
<div class="fragment"><div class="line"><span class="keyword">const</span> <a class="code hl_struct" href="struct_g_l_f_wgammaramp.html">GLFWgammaramp</a>* ramp = <a class="code hl_function" href="group__monitor.html#ga76ba90debcf0062b5c4b73052b24f96f">glfwGetGammaRamp</a>(monitor);</div>
<div class="ttc" id="agroup__monitor_html_ga76ba90debcf0062b5c4b73052b24f96f"><div class="ttname"><a href="group__monitor.html#ga76ba90debcf0062b5c4b73052b24f96f">glfwGetGammaRamp</a></div><div class="ttdeci">const GLFWgammaramp * glfwGetGammaRamp(GLFWmonitor *monitor)</div><div class="ttdoc">Returns the current gamma ramp for the specified monitor.</div></div>
</div><!-- fragment --><p>If you wish to set a regular gamma ramp, you can have GLFW calculate it for you from the desired exponent with <a class="el" href="group__monitor.html#ga6ac582625c990220785ddd34efa3169a">glfwSetGamma</a>, which in turn calls <a class="el" href="group__monitor.html#ga583f0ffd0d29613d8cd172b996bbf0dd">glfwSetGammaRamp</a> with the resulting ramp.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__monitor.html#ga6ac582625c990220785ddd34efa3169a">glfwSetGamma</a>(monitor, 1.0);</div>
<div class="ttc" id="agroup__monitor_html_ga6ac582625c990220785ddd34efa3169a"><div class="ttname"><a href="group__monitor.html#ga6ac582625c990220785ddd34efa3169a">glfwSetGamma</a></div><div class="ttdeci">void glfwSetGamma(GLFWmonitor *monitor, float gamma)</div><div class="ttdoc">Generates a gamma ramp and sets it for the specified monitor.</div></div>
</div><!-- fragment --><p>To experiment with gamma correction via the <a class="el" href="group__monitor.html#ga6ac582625c990220785ddd34efa3169a">glfwSetGamma</a> function, run the <code>gamma</code> test program.</p>
<dl class="section note"><dt>Note</dt><dd>The software controlled gamma ramp is applied <em>in addition</em> to the hardware gamma correction, which today is typically an approximation of sRGB gamma. This means that setting a perfectly linear ramp, or gamma 1.0, will produce the default (usually sRGB-like) behavior. </dd></dl>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
