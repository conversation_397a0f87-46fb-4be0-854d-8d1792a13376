﻿  test_adaptive_manifold.cpp
  test_adaptive_manifold_ref_impl.cpp
  test_anisodiff.cpp
  test_bilateral_texture_filter.cpp
  test_deriche_filter.cpp
  test_disparity_wls_filter.cpp
  test_domain_transform.cpp
  test_edgeboxes.cpp
  test_edgepreserving_filter.cpp
  test_fast_hough_transform.cpp
  test_fbs_filter.cpp
  test_fgs_filter.cpp
  test_find_ellipses.cpp
  test_fld.cpp
  test_fourier_descriptors.cpp
  test_guided_filter.cpp
  test_joint_bilateral_filter.cpp
  test_l0_smooth.cpp
  test_main.cpp
  test_matchcolortemplate.cpp
  test_niblack_threshold.cpp
  test_radon_transform.cpp
  test_ridge_detection_filter.cpp
  test_rolling_guidance_filter.cpp
  test_run_length_morphology.cpp
  test_scansegment.cpp
  test_slic.cpp
  test_sparse_match_interpolator.cpp
  test_structured_edge_detection.cpp
  test_thinning.cpp
  test_weighted_median_filter.cpp
  opencv_test_ximgproc.vcxproj -> D:\AI\opencv\cudabuild\bin\Release\opencv_test_ximgproc.exe
