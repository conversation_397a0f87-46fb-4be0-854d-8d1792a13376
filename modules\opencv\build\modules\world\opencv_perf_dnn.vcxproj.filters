﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\perf\perf_caffe.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\perf\perf_common.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\perf\perf_convolution.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\perf\perf_convolution1d.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\perf\perf_convolution3d.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\perf\perf_einsum.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\perf\perf_gemm.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\perf\perf_layer.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\perf\perf_main.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\perf\perf_net.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\perf\perf_recurrent.cpp">
      <Filter>Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_common.hpp">
      <Filter>test_common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\test\test_common.impl.hpp">
      <Filter>test_common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\dnn\perf\perf_precomp.hpp">
      <Filter>Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Include">
      <UniqueIdentifier>{0D522D53-72BF-369F-A761-1665CACACBF7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Src">
      <UniqueIdentifier>{4C321FAF-208D-3921-91F6-A2582E33B23F}</UniqueIdentifier>
    </Filter>
    <Filter Include="test_common">
      <UniqueIdentifier>{C47A5B31-D42B-3AEF-A551-8A65CBA791F6}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
