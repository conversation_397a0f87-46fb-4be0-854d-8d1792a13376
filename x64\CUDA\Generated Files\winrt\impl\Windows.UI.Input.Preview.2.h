// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_UI_Input_Preview_2_H
#define WINRT_Windows_UI_Input_Preview_2_H
#include "winrt/impl/Windows.UI.WindowManagement.1.h"
#include "winrt/impl/Windows.UI.Input.Preview.1.h"
WINRT_EXPORT namespace winrt::Windows::UI::Input::Preview
{
    struct InputActivationListenerPreview
    {
        InputActivationListenerPreview() = delete;
        static auto CreateForApplicationWindow(winrt::Windows::UI::WindowManagement::AppWindow const& window);
    };
}
#endif
