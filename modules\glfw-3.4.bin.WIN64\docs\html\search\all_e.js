var searchData=
[
  ['h_20header_0',['The glext.h header',['../context_guide.html#context_glext_header',1,'']]],
  ['handle_20parameters_1',['Window handle parameters',['../moving_guide.html#moving_window_handles',1,'']]],
  ['handling_2',['Error handling',['../intro_guide.html#error_handling',1,'']]],
  ['hard_20and_20soft_20constraints_3',['Hard and soft constraints',['../window_guide.html#window_hints_hard',1,'']]],
  ['has_20been_20changed_4',['Version string format has been changed',['../news.html#version_string_caveat',1,'']]],
  ['has_20been_20removed_5',['has been removed',['../news.html#use_osmesa_removed',1,'GLFW_USE_OSMESA CMake option has been removed'],['../news.html#use_wayland_removed',1,'GLFW_USE_WAYLAND CMake option has been removed'],['../news.html#vulkan_static_removed',1,'GLFW_VULKAN_STATIC CMake option has been removed'],['../news.html#corevideo_caveat',1,'macOS CoreVideo dependency has been removed'],['../news.html#wl_shell_removed',1,'wl_shell protocol support has been removed']]],
  ['hat_20states_6',['hat states',['../group__hat__state.html',1,'Joystick hat states'],['../input_guide.html#joystick_hat',1,'Joystick hat states']]],
  ['header_7',['header',['../quick_guide.html#quick_include',1,'Including the GLFW header'],['../context_guide.html#context_glext_header',1,'The glext.h header']]],
  ['header_20file_8',['header file',['../build_guide.html#build_include',1,'Including the GLFW header file'],['../vulkan_guide.html#vulkan_include',1,'Including the Vulkan header file'],['../moving_guide.html#moving_renamed_files',1,'Renamed library and header file']]],
  ['header_20inclusion_9',['GLU header inclusion',['../moving_guide.html#moving_glu',1,'']]],
  ['header_20is_20no_20longer_20generated_10',['Configuration header is no longer generated',['../news.html#config_header_caveat',1,'']]],
  ['header_20option_20macros_11',['GLFW header option macros',['../build_guide.html#build_macros',1,'']]],
  ['heap_20memory_20allocator_12',['heap memory allocator',['../intro_guide.html#init_allocator',1,'Custom heap memory allocator'],['../news.html#custom_heap_allocator',1,'Support for custom heap memory allocator']]],
  ['height_13',['height',['../struct_g_l_f_wimage.html#a0b7d95368f0c80d5e5c9875057c7dbec',1,'GLFWimage::height'],['../struct_g_l_f_wvidmode.html#ac65942a5f6981695517437a9d571d03c',1,'GLFWvidmode::height']]],
  ['hint_14',['hint',['../news.html#angle_renderer_hint',1,'ANGLE rendering backend hint'],['../news.html#wayland_app_id_hint',1,'Wayland surface app_id hint'],['../news.html#win32_showdefault_hint',1,'Windows STARTUPINFO show command hint'],['../news.html#win32_keymenu_hint',1,'Windows window menu keyboard access hint'],['../news.html#x11_xcb_vulkan_surface',1,'X11 Vulkan window surface hint']]],
  ['hint_20for_20framebuffer_20scaling_15',['Window hint for framebuffer scaling',['../news.html#scale_framebuffer_hint',1,'']]],
  ['hints_16',['hints',['../context_guide.html#context_hints',1,'Context creation hints'],['../window_guide.html#window_hints_ctx',1,'Context related hints'],['../window_guide.html#window_hints_fb',1,'Framebuffer related hints'],['../intro_guide.html#init_hints',1,'Initialization hints'],['../window_guide.html#window_hints_osx',1,'macOS specific hints'],['../intro_guide.html#init_hints_osx',1,'macOS specific init hints'],['../window_guide.html#window_hints_mtr',1,'Monitor related hints'],['../moving_guide.html#moving_hints',1,'Persistent window hints'],['../intro_guide.html#init_hints_shared',1,'Shared init hints'],['../intro_guide.html#init_hints_wayland',1,'Wayland specific init hints'],['../window_guide.html#window_hints_wayland',1,'Wayland specific window hints'],['../window_guide.html#window_hints_win32',1,'Win32 specific hints'],['../window_guide.html#window_hints',1,'Window creation hints'],['../window_guide.html#window_hints_wnd',1,'Window related hints'],['../intro_guide.html#init_hints_x11',1,'X11 specific init hints'],['../window_guide.html#window_hints_x11',1,'X11 specific window hints']]],
  ['hints_20for_20initial_20window_20position_17',['Window hints for initial window position',['../news.html#window_position_hint',1,'']]],
  ['hotkeys_18',['Capture of system-wide hotkeys',['../moving_guide.html#moving_syskeys',1,'']]],
  ['human_20readable_20name_19',['Human-readable name',['../monitor_guide.html#monitor_name',1,'']]]
];
