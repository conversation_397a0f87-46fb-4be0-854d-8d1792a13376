// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_ApplicationModel_UserDataAccounts_2_H
#define WINRT_Windows_ApplicationModel_UserDataAccounts_2_H
#include "winrt/impl/Windows.System.1.h"
#include "winrt/impl/Windows.ApplicationModel.UserDataAccounts.1.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::UserDataAccounts
{
    struct WINRT_IMPL_EMPTY_BASES UserDataAccount : winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccount,
        impl::require<UserDataAccount, winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccount2, winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccount3, winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccount4>
    {
        UserDataAccount(std::nullptr_t) noexcept {}
        UserDataAccount(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccount(ptr, take_ownership_from_abi) {}
        using winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccount::Icon;
        using impl::consume_t<UserDataAccount, winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccount4>::Icon;
        using impl::consume_t<UserDataAccount, winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccount2>::IsProtectedUnderLock;
        using impl::consume_t<UserDataAccount, winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccount4>::IsProtectedUnderLock;
    };
    struct UserDataAccountManager
    {
        UserDataAccountManager() = delete;
        static auto RequestStoreAsync(winrt::Windows::ApplicationModel::UserDataAccounts::UserDataAccountStoreAccessType const& storeAccessType);
        static auto ShowAddAccountAsync(winrt::Windows::ApplicationModel::UserDataAccounts::UserDataAccountContentKinds const& contentKinds);
        static auto ShowAccountSettingsAsync(param::hstring const& id);
        static auto ShowAccountErrorResolverAsync(param::hstring const& id);
        static auto GetForUser(winrt::Windows::System::User const& user);
    };
    struct WINRT_IMPL_EMPTY_BASES UserDataAccountManagerForUser : winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccountManagerForUser
    {
        UserDataAccountManagerForUser(std::nullptr_t) noexcept {}
        UserDataAccountManagerForUser(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccountManagerForUser(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES UserDataAccountStore : winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccountStore,
        impl::require<UserDataAccountStore, winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccountStore2, winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccountStore3>
    {
        UserDataAccountStore(std::nullptr_t) noexcept {}
        UserDataAccountStore(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccountStore(ptr, take_ownership_from_abi) {}
        using winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccountStore::CreateAccountAsync;
        using impl::consume_t<UserDataAccountStore, winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccountStore2>::CreateAccountAsync;
        using impl::consume_t<UserDataAccountStore, winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccountStore3>::CreateAccountAsync;
    };
    struct WINRT_IMPL_EMPTY_BASES UserDataAccountStoreChangedEventArgs : winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccountStoreChangedEventArgs
    {
        UserDataAccountStoreChangedEventArgs(std::nullptr_t) noexcept {}
        UserDataAccountStoreChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::UserDataAccounts::IUserDataAccountStoreChangedEventArgs(ptr, take_ownership_from_abi) {}
    };
}
#endif
