var searchData=
[
  ['damage_20and_20refresh_0',['Window damage and refresh',['../window_guide.html#window_refresh',1,'']]],
  ['deallocate_1',['deallocate',['../struct_g_l_f_wallocator.html#ab74cf9a969e73e6eb65a6112a591a988',1,'GLFWallocator']]],
  ['decorations_2',['Wayland libdecor decorations',['../news.html#wayland_libdecor_decorations',1,'']]],
  ['default_20values_3',['default values',['../intro_guide.html#init_hints_values',1,'Supported and default values'],['../window_guide.html#window_hints_values',1,'Supported and default values']]],
  ['demand_4',['Joystick support is initialized on demand',['../news.html#joystick_init_caveat',1,'']]],
  ['dependencies_5',['Installing dependencies',['../compile_guide.html#compile_deps',1,'']]],
  ['dependencies_20for_20wayland_20and_20x11_6',['Dependencies for Wayland and X11',['../compile_guide.html#compile_deps_wayland',1,'']]],
  ['dependency_20has_20been_20removed_7',['macOS CoreVideo dependency has been removed',['../news.html#corevideo_caveat',1,'']]],
  ['deprecated_8',['deprecated',['../news.html#mingw_deprecated',1,'Original MinGW support is deprecated'],['../news.html#yosemite_deprecated',1,'OS X Yosemite support is deprecated'],['../news.html#winxp_deprecated',1,'Windows XP and Vista support is deprecated']]],
  ['deprecated_20list_9',['Deprecated List',['../deprecated.html',1,'']]],
  ['deprecations_10',['Deprecations',['../news.html#deprecations',1,'']]],
  ['destruction_11',['destruction',['../input_guide.html#cursor_destruction',1,'Cursor destruction'],['../window_guide.html#window_destruction',1,'Window destruction']]],
  ['disabled_20when_20built_20as_20a_20subproject_12',['Tests and examples are disabled when built as a subproject',['../news.html#standalone_caveat',1,'']]],
  ['documentation_20generation_20requires_20doxygen_201_209_208_20or_20later_13',['Documentation generation requires Doxygen 1.9.8 or later',['../news.html#docs_target_caveat',1,'']]],
  ['doxygen_201_209_208_20or_20later_14',['Documentation generation requires Doxygen 1.9.8 or later',['../news.html#docs_target_caveat',1,'']]],
  ['drop_20input_15',['Path drop input',['../input_guide.html#path_drop',1,'']]],
  ['dwm_20transparency_16',['Windows 7 framebuffer transparency requires DWM transparency',['../news.html#win7_framebuffer_caveat',1,'']]]
];
