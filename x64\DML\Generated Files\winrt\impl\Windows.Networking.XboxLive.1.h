// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Networking_XboxLive_1_H
#define WINRT_Windows_Networking_XboxLive_1_H
#include "winrt/impl/Windows.Networking.XboxLive.0.h"
WINRT_EXPORT namespace winrt::Windows::Networking::XboxLive
{
    struct WINRT_IMPL_EMPTY_BASES IXboxLiveDeviceAddress :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXboxLiveDeviceAddress>
    {
        IXboxLiveDeviceAddress(std::nullptr_t = nullptr) noexcept {}
        IXboxLiveDeviceAddress(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXboxLiveDeviceAddressStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXboxLiveDeviceAddressStatics>
    {
        IXboxLiveDeviceAddressStatics(std::nullptr_t = nullptr) noexcept {}
        IXboxLiveDeviceAddressStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXboxLiveEndpointPair :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXboxLiveEndpointPair>
    {
        IXboxLiveEndpointPair(std::nullptr_t = nullptr) noexcept {}
        IXboxLiveEndpointPair(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXboxLiveEndpointPairCreationResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXboxLiveEndpointPairCreationResult>
    {
        IXboxLiveEndpointPairCreationResult(std::nullptr_t = nullptr) noexcept {}
        IXboxLiveEndpointPairCreationResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXboxLiveEndpointPairStateChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXboxLiveEndpointPairStateChangedEventArgs>
    {
        IXboxLiveEndpointPairStateChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IXboxLiveEndpointPairStateChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXboxLiveEndpointPairStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXboxLiveEndpointPairStatics>
    {
        IXboxLiveEndpointPairStatics(std::nullptr_t = nullptr) noexcept {}
        IXboxLiveEndpointPairStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXboxLiveEndpointPairTemplate :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXboxLiveEndpointPairTemplate>
    {
        IXboxLiveEndpointPairTemplate(std::nullptr_t = nullptr) noexcept {}
        IXboxLiveEndpointPairTemplate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXboxLiveEndpointPairTemplateStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXboxLiveEndpointPairTemplateStatics>
    {
        IXboxLiveEndpointPairTemplateStatics(std::nullptr_t = nullptr) noexcept {}
        IXboxLiveEndpointPairTemplateStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXboxLiveInboundEndpointPairCreatedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXboxLiveInboundEndpointPairCreatedEventArgs>
    {
        IXboxLiveInboundEndpointPairCreatedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IXboxLiveInboundEndpointPairCreatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXboxLiveQualityOfServiceMeasurement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXboxLiveQualityOfServiceMeasurement>
    {
        IXboxLiveQualityOfServiceMeasurement(std::nullptr_t = nullptr) noexcept {}
        IXboxLiveQualityOfServiceMeasurement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXboxLiveQualityOfServiceMeasurementStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXboxLiveQualityOfServiceMeasurementStatics>
    {
        IXboxLiveQualityOfServiceMeasurementStatics(std::nullptr_t = nullptr) noexcept {}
        IXboxLiveQualityOfServiceMeasurementStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXboxLiveQualityOfServiceMetricResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXboxLiveQualityOfServiceMetricResult>
    {
        IXboxLiveQualityOfServiceMetricResult(std::nullptr_t = nullptr) noexcept {}
        IXboxLiveQualityOfServiceMetricResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXboxLiveQualityOfServicePrivatePayloadResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXboxLiveQualityOfServicePrivatePayloadResult>
    {
        IXboxLiveQualityOfServicePrivatePayloadResult(std::nullptr_t = nullptr) noexcept {}
        IXboxLiveQualityOfServicePrivatePayloadResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
