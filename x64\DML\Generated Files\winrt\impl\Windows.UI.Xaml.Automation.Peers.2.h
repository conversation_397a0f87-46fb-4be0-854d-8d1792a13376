// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Automation_Peers_2_H
#define WINRT_Windows_UI_Xaml_Automation_Peers_2_H
#include "winrt/impl/Windows.Foundation.1.h"
#include "winrt/impl/Windows.UI.Xaml.1.h"
#include "winrt/impl/Windows.UI.Xaml.Automation.1.h"
#include "winrt/impl/Windows.UI.Xaml.Automation.Provider.1.h"
#include "winrt/impl/Windows.UI.Xaml.Controls.1.h"
#include "winrt/impl/Windows.UI.Xaml.Controls.Primitives.1.h"
#include "winrt/impl/Windows.UI.Xaml.Automation.Peers.1.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Automation::Peers
{
    struct RawElementProviderRuntimeId
    {
        uint32_t Part1;
        uint32_t Part2;
    };
    inline bool operator==(RawElementProviderRuntimeId const& left, RawElementProviderRuntimeId const& right) noexcept
    {
        return left.Part1 == right.Part1 && left.Part2 == right.Part2;
    }
    inline bool operator!=(RawElementProviderRuntimeId const& left, RawElementProviderRuntimeId const& right) noexcept
    {
        return !(left == right);
    }
    struct WINRT_IMPL_EMPTY_BASES AppBarAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IAppBarAutomationPeer,
        impl::base<AppBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<AppBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IToggleProvider, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Provider::IWindowProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        AppBarAutomationPeer(std::nullptr_t) noexcept {}
        AppBarAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IAppBarAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit AppBarAutomationPeer(winrt::Windows::UI::Xaml::Controls::AppBar const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES AppBarButtonAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IAppBarButtonAutomationPeer,
        impl::base<AppBarButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<AppBarButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Peers::IButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Peers::IButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        AppBarButtonAutomationPeer(std::nullptr_t) noexcept {}
        AppBarButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IAppBarButtonAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit AppBarButtonAutomationPeer(winrt::Windows::UI::Xaml::Controls::AppBarButton const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES AppBarToggleButtonAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IAppBarToggleButtonAutomationPeer,
        impl::base<AppBarToggleButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ToggleButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<AppBarToggleButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IToggleButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IToggleProvider, winrt::Windows::UI::Xaml::Automation::Peers::IButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        AppBarToggleButtonAutomationPeer(std::nullptr_t) noexcept {}
        AppBarToggleButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IAppBarToggleButtonAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit AppBarToggleButtonAutomationPeer(winrt::Windows::UI::Xaml::Controls::AppBarToggleButton const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES AutoSuggestBoxAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IAutoSuggestBoxAutomationPeer,
        impl::base<AutoSuggestBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<AutoSuggestBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        AutoSuggestBoxAutomationPeer(std::nullptr_t) noexcept {}
        AutoSuggestBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IAutoSuggestBoxAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit AutoSuggestBoxAutomationPeer(winrt::Windows::UI::Xaml::Controls::AutoSuggestBox const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES AutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer,
        impl::base<AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<AutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        AutomationPeer(std::nullptr_t) noexcept {}
        AutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer(ptr, take_ownership_from_abi) {}
        static auto ListenerExists(winrt::Windows::UI::Xaml::Automation::Peers::AutomationEvents const& eventId);
        static auto GenerateRawElementProviderRuntimeId();
    };
    struct WINRT_IMPL_EMPTY_BASES AutomationPeerAnnotation : winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerAnnotation,
        impl::base<AutomationPeerAnnotation, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<AutomationPeerAnnotation, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        AutomationPeerAnnotation(std::nullptr_t) noexcept {}
        AutomationPeerAnnotation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerAnnotation(ptr, take_ownership_from_abi) {}
        AutomationPeerAnnotation();
        explicit AutomationPeerAnnotation(winrt::Windows::UI::Xaml::Automation::AnnotationType const& type);
        AutomationPeerAnnotation(winrt::Windows::UI::Xaml::Automation::AnnotationType const& type, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer const& peer);
        [[nodiscard]] static auto TypeProperty();
        [[nodiscard]] static auto PeerProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES ButtonAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IButtonAutomationPeer,
        impl::base<ButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Peers::IButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ButtonAutomationPeer(std::nullptr_t) noexcept {}
        ButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IButtonAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ButtonAutomationPeer(winrt::Windows::UI::Xaml::Controls::Button const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ButtonBaseAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IButtonBaseAutomationPeer,
        impl::base<ButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ButtonBaseAutomationPeer(std::nullptr_t) noexcept {}
        ButtonBaseAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IButtonBaseAutomationPeer(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES CalendarDatePickerAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ICalendarDatePickerAutomationPeer,
        impl::base<CalendarDatePickerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<CalendarDatePickerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Provider::IValueProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        CalendarDatePickerAutomationPeer(std::nullptr_t) noexcept {}
        CalendarDatePickerAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ICalendarDatePickerAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit CalendarDatePickerAutomationPeer(winrt::Windows::UI::Xaml::Controls::CalendarDatePicker const& owner);
        using impl::consume_t<CalendarDatePickerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IValueProvider>::SetValue;
        using impl::consume_t<CalendarDatePickerAutomationPeer, winrt::Windows::UI::Xaml::IDependencyObject>::SetValue;
    };
    struct WINRT_IMPL_EMPTY_BASES CaptureElementAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ICaptureElementAutomationPeer,
        impl::base<CaptureElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<CaptureElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        CaptureElementAutomationPeer(std::nullptr_t) noexcept {}
        CaptureElementAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ICaptureElementAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit CaptureElementAutomationPeer(winrt::Windows::UI::Xaml::Controls::CaptureElement const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES CheckBoxAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ICheckBoxAutomationPeer,
        impl::base<CheckBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ToggleButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<CheckBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IToggleButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IToggleProvider, winrt::Windows::UI::Xaml::Automation::Peers::IButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        CheckBoxAutomationPeer(std::nullptr_t) noexcept {}
        CheckBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ICheckBoxAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit CheckBoxAutomationPeer(winrt::Windows::UI::Xaml::Controls::CheckBox const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ColorPickerSliderAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeer,
        impl::base<ColorPickerSliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::RangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ColorPickerSliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ISliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IRangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ColorPickerSliderAutomationPeer(std::nullptr_t) noexcept {}
        ColorPickerSliderAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ColorPickerSliderAutomationPeer(winrt::Windows::UI::Xaml::Controls::Primitives::ColorPickerSlider const& owner);
        using impl::consume_t<ColorPickerSliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider>::SetValue;
        using impl::consume_t<ColorPickerSliderAutomationPeer, winrt::Windows::UI::Xaml::IDependencyObject>::SetValue;
    };
    struct WINRT_IMPL_EMPTY_BASES ColorSpectrumAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeer,
        impl::base<ColorSpectrumAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ColorSpectrumAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ColorSpectrumAutomationPeer(std::nullptr_t) noexcept {}
        ColorSpectrumAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ColorSpectrumAutomationPeer(winrt::Windows::UI::Xaml::Controls::Primitives::ColorSpectrum const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ComboBoxAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IComboBoxAutomationPeer,
        impl::base<ComboBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ComboBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IValueProvider, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Provider::IWindowProvider, winrt::Windows::UI::Xaml::Automation::Peers::ISelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Provider::IItemContainerProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ComboBoxAutomationPeer(std::nullptr_t) noexcept {}
        ComboBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IComboBoxAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ComboBoxAutomationPeer(winrt::Windows::UI::Xaml::Controls::ComboBox const& owner);
        using impl::consume_t<ComboBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IValueProvider>::SetValue;
        using impl::consume_t<ComboBoxAutomationPeer, winrt::Windows::UI::Xaml::IDependencyObject>::SetValue;
    };
    struct WINRT_IMPL_EMPTY_BASES ComboBoxItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IComboBoxItemAutomationPeer,
        impl::base<ComboBoxItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ComboBoxItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ComboBoxItemAutomationPeer(std::nullptr_t) noexcept {}
        ComboBoxItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IComboBoxItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ComboBoxItemAutomationPeer(winrt::Windows::UI::Xaml::Controls::ComboBoxItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ComboBoxItemDataAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IComboBoxItemDataAutomationPeer,
        impl::base<ComboBoxItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SelectorItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ComboBoxItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IScrollItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::ISelectorItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IVirtualizedItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ComboBoxItemDataAutomationPeer(std::nullptr_t) noexcept {}
        ComboBoxItemDataAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IComboBoxItemDataAutomationPeer(ptr, take_ownership_from_abi) {}
        ComboBoxItemDataAutomationPeer(winrt::Windows::Foundation::IInspectable const& item, winrt::Windows::UI::Xaml::Automation::Peers::ComboBoxAutomationPeer const& parent);
    };
    struct WINRT_IMPL_EMPTY_BASES DatePickerAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IDatePickerAutomationPeer,
        impl::base<DatePickerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<DatePickerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        DatePickerAutomationPeer(std::nullptr_t) noexcept {}
        DatePickerAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IDatePickerAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit DatePickerAutomationPeer(winrt::Windows::UI::Xaml::Controls::DatePicker const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES DatePickerFlyoutPresenterAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IDatePickerFlyoutPresenterAutomationPeer,
        impl::base<DatePickerFlyoutPresenterAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<DatePickerFlyoutPresenterAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        DatePickerFlyoutPresenterAutomationPeer(std::nullptr_t) noexcept {}
        DatePickerFlyoutPresenterAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IDatePickerFlyoutPresenterAutomationPeer(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES FlipViewAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IFlipViewAutomationPeer,
        impl::base<FlipViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<FlipViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ISelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Provider::IItemContainerProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        FlipViewAutomationPeer(std::nullptr_t) noexcept {}
        FlipViewAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IFlipViewAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit FlipViewAutomationPeer(winrt::Windows::UI::Xaml::Controls::FlipView const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES FlipViewItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IFlipViewItemAutomationPeer,
        impl::base<FlipViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<FlipViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        FlipViewItemAutomationPeer(std::nullptr_t) noexcept {}
        FlipViewItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IFlipViewItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit FlipViewItemAutomationPeer(winrt::Windows::UI::Xaml::Controls::FlipViewItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES FlipViewItemDataAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IFlipViewItemDataAutomationPeer,
        impl::base<FlipViewItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SelectorItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<FlipViewItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IScrollItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::ISelectorItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IVirtualizedItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        FlipViewItemDataAutomationPeer(std::nullptr_t) noexcept {}
        FlipViewItemDataAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IFlipViewItemDataAutomationPeer(ptr, take_ownership_from_abi) {}
        FlipViewItemDataAutomationPeer(winrt::Windows::Foundation::IInspectable const& item, winrt::Windows::UI::Xaml::Automation::Peers::FlipViewAutomationPeer const& parent);
    };
    struct WINRT_IMPL_EMPTY_BASES FlyoutPresenterAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IFlyoutPresenterAutomationPeer,
        impl::base<FlyoutPresenterAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<FlyoutPresenterAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        FlyoutPresenterAutomationPeer(std::nullptr_t) noexcept {}
        FlyoutPresenterAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IFlyoutPresenterAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit FlyoutPresenterAutomationPeer(winrt::Windows::UI::Xaml::Controls::FlyoutPresenter const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES FrameworkElementAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer,
        impl::base<FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        FrameworkElementAutomationPeer(std::nullptr_t) noexcept {}
        FrameworkElementAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit FrameworkElementAutomationPeer(winrt::Windows::UI::Xaml::FrameworkElement const& owner);
        static auto FromElement(winrt::Windows::UI::Xaml::UIElement const& element);
        static auto CreatePeerForElement(winrt::Windows::UI::Xaml::UIElement const& element);
    };
    struct WINRT_IMPL_EMPTY_BASES GridViewAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IGridViewAutomationPeer,
        impl::base<GridViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ListViewBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<GridViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IListViewBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IDropTargetProvider, winrt::Windows::UI::Xaml::Automation::Peers::ISelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Provider::IItemContainerProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        GridViewAutomationPeer(std::nullptr_t) noexcept {}
        GridViewAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IGridViewAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit GridViewAutomationPeer(winrt::Windows::UI::Xaml::Controls::GridView const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES GridViewHeaderItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IGridViewHeaderItemAutomationPeer,
        impl::base<GridViewHeaderItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ListViewBaseHeaderItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<GridViewHeaderItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IListViewBaseHeaderItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        GridViewHeaderItemAutomationPeer(std::nullptr_t) noexcept {}
        GridViewHeaderItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IGridViewHeaderItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit GridViewHeaderItemAutomationPeer(winrt::Windows::UI::Xaml::Controls::GridViewHeaderItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES GridViewItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IGridViewItemAutomationPeer,
        impl::base<GridViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<GridViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        GridViewItemAutomationPeer(std::nullptr_t) noexcept {}
        GridViewItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IGridViewItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit GridViewItemAutomationPeer(winrt::Windows::UI::Xaml::Controls::GridViewItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES GridViewItemDataAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IGridViewItemDataAutomationPeer,
        impl::base<GridViewItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SelectorItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<GridViewItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IScrollItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::ISelectorItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IVirtualizedItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        GridViewItemDataAutomationPeer(std::nullptr_t) noexcept {}
        GridViewItemDataAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IGridViewItemDataAutomationPeer(ptr, take_ownership_from_abi) {}
        GridViewItemDataAutomationPeer(winrt::Windows::Foundation::IInspectable const& item, winrt::Windows::UI::Xaml::Automation::Peers::GridViewAutomationPeer const& parent);
    };
    struct WINRT_IMPL_EMPTY_BASES GroupItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IGroupItemAutomationPeer,
        impl::base<GroupItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<GroupItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        GroupItemAutomationPeer(std::nullptr_t) noexcept {}
        GroupItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IGroupItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit GroupItemAutomationPeer(winrt::Windows::UI::Xaml::Controls::GroupItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES HubAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IHubAutomationPeer,
        impl::base<HubAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<HubAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        HubAutomationPeer(std::nullptr_t) noexcept {}
        HubAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IHubAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit HubAutomationPeer(winrt::Windows::UI::Xaml::Controls::Hub const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES HubSectionAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IHubSectionAutomationPeer,
        impl::base<HubSectionAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<HubSectionAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IScrollItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        HubSectionAutomationPeer(std::nullptr_t) noexcept {}
        HubSectionAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IHubSectionAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit HubSectionAutomationPeer(winrt::Windows::UI::Xaml::Controls::HubSection const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES HyperlinkButtonAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IHyperlinkButtonAutomationPeer,
        impl::base<HyperlinkButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<HyperlinkButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Peers::IButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        HyperlinkButtonAutomationPeer(std::nullptr_t) noexcept {}
        HyperlinkButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IHyperlinkButtonAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit HyperlinkButtonAutomationPeer(winrt::Windows::UI::Xaml::Controls::HyperlinkButton const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ImageAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IImageAutomationPeer,
        impl::base<ImageAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ImageAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ImageAutomationPeer(std::nullptr_t) noexcept {}
        ImageAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IImageAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ImageAutomationPeer(winrt::Windows::UI::Xaml::Controls::Image const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES InkToolbarAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IInkToolbarAutomationPeer,
        impl::base<InkToolbarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<InkToolbarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        InkToolbarAutomationPeer(std::nullptr_t) noexcept {}
        InkToolbarAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IInkToolbarAutomationPeer(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IItemAutomationPeer,
        impl::base<ItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IVirtualizedItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ItemAutomationPeer(std::nullptr_t) noexcept {}
        ItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IItemAutomationPeer(ptr, take_ownership_from_abi) {}
        ItemAutomationPeer(winrt::Windows::Foundation::IInspectable const& item, winrt::Windows::UI::Xaml::Automation::Peers::ItemsControlAutomationPeer const& parent);
    };
    struct WINRT_IMPL_EMPTY_BASES ItemsControlAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer,
        impl::base<ItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Provider::IItemContainerProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ItemsControlAutomationPeer(std::nullptr_t) noexcept {}
        ItemsControlAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ItemsControlAutomationPeer(winrt::Windows::UI::Xaml::Controls::ItemsControl const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ListBoxAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IListBoxAutomationPeer,
        impl::base<ListBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ListBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ISelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Provider::IItemContainerProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ListBoxAutomationPeer(std::nullptr_t) noexcept {}
        ListBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IListBoxAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ListBoxAutomationPeer(winrt::Windows::UI::Xaml::Controls::ListBox const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ListBoxItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IListBoxItemAutomationPeer,
        impl::base<ListBoxItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ListBoxItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ListBoxItemAutomationPeer(std::nullptr_t) noexcept {}
        ListBoxItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IListBoxItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ListBoxItemAutomationPeer(winrt::Windows::UI::Xaml::Controls::ListBoxItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ListBoxItemDataAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IListBoxItemDataAutomationPeer,
        impl::base<ListBoxItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SelectorItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ListBoxItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IScrollItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::ISelectorItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IVirtualizedItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ListBoxItemDataAutomationPeer(std::nullptr_t) noexcept {}
        ListBoxItemDataAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IListBoxItemDataAutomationPeer(ptr, take_ownership_from_abi) {}
        ListBoxItemDataAutomationPeer(winrt::Windows::Foundation::IInspectable const& item, winrt::Windows::UI::Xaml::Automation::Peers::ListBoxAutomationPeer const& parent);
    };
    struct WINRT_IMPL_EMPTY_BASES ListPickerFlyoutPresenterAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IListPickerFlyoutPresenterAutomationPeer,
        impl::base<ListPickerFlyoutPresenterAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ListPickerFlyoutPresenterAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ListPickerFlyoutPresenterAutomationPeer(std::nullptr_t) noexcept {}
        ListPickerFlyoutPresenterAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IListPickerFlyoutPresenterAutomationPeer(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ListViewAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IListViewAutomationPeer,
        impl::base<ListViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ListViewBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ListViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IListViewBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IDropTargetProvider, winrt::Windows::UI::Xaml::Automation::Peers::ISelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Provider::IItemContainerProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ListViewAutomationPeer(std::nullptr_t) noexcept {}
        ListViewAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IListViewAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ListViewAutomationPeer(winrt::Windows::UI::Xaml::Controls::ListView const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ListViewBaseAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IListViewBaseAutomationPeer,
        impl::base<ListViewBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ListViewBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IDropTargetProvider, winrt::Windows::UI::Xaml::Automation::Peers::ISelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Provider::IItemContainerProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ListViewBaseAutomationPeer(std::nullptr_t) noexcept {}
        ListViewBaseAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IListViewBaseAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ListViewBaseAutomationPeer(winrt::Windows::UI::Xaml::Controls::ListViewBase const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ListViewBaseHeaderItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IListViewBaseHeaderItemAutomationPeer,
        impl::base<ListViewBaseHeaderItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ListViewBaseHeaderItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ListViewBaseHeaderItemAutomationPeer(std::nullptr_t) noexcept {}
        ListViewBaseHeaderItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IListViewBaseHeaderItemAutomationPeer(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ListViewHeaderItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IListViewHeaderItemAutomationPeer,
        impl::base<ListViewHeaderItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ListViewBaseHeaderItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ListViewHeaderItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IListViewBaseHeaderItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ListViewHeaderItemAutomationPeer(std::nullptr_t) noexcept {}
        ListViewHeaderItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IListViewHeaderItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ListViewHeaderItemAutomationPeer(winrt::Windows::UI::Xaml::Controls::ListViewHeaderItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ListViewItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IListViewItemAutomationPeer,
        impl::base<ListViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ListViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ListViewItemAutomationPeer(std::nullptr_t) noexcept {}
        ListViewItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IListViewItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ListViewItemAutomationPeer(winrt::Windows::UI::Xaml::Controls::ListViewItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ListViewItemDataAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IListViewItemDataAutomationPeer,
        impl::base<ListViewItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SelectorItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ListViewItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IScrollItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::ISelectorItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IVirtualizedItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ListViewItemDataAutomationPeer(std::nullptr_t) noexcept {}
        ListViewItemDataAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IListViewItemDataAutomationPeer(ptr, take_ownership_from_abi) {}
        ListViewItemDataAutomationPeer(winrt::Windows::Foundation::IInspectable const& item, winrt::Windows::UI::Xaml::Automation::Peers::ListViewBaseAutomationPeer const& parent);
    };
    struct WINRT_IMPL_EMPTY_BASES LoopingSelectorAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ILoopingSelectorAutomationPeer,
        impl::base<LoopingSelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<LoopingSelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionProvider, winrt::Windows::UI::Xaml::Automation::Provider::IItemContainerProvider, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Provider::IScrollProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        LoopingSelectorAutomationPeer(std::nullptr_t) noexcept {}
        LoopingSelectorAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ILoopingSelectorAutomationPeer(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES LoopingSelectorItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ILoopingSelectorItemAutomationPeer,
        impl::base<LoopingSelectorItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<LoopingSelectorItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IScrollItemProvider, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        LoopingSelectorItemAutomationPeer(std::nullptr_t) noexcept {}
        LoopingSelectorItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ILoopingSelectorItemAutomationPeer(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES LoopingSelectorItemDataAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ILoopingSelectorItemDataAutomationPeer,
        impl::base<LoopingSelectorItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<LoopingSelectorItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IVirtualizedItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        LoopingSelectorItemDataAutomationPeer(std::nullptr_t) noexcept {}
        LoopingSelectorItemDataAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ILoopingSelectorItemDataAutomationPeer(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES MapControlAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IMapControlAutomationPeer,
        impl::base<MapControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<MapControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IScrollProvider, winrt::Windows::UI::Xaml::Automation::Provider::ITransformProvider, winrt::Windows::UI::Xaml::Automation::Provider::ITransformProvider2, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        MapControlAutomationPeer(std::nullptr_t) noexcept {}
        MapControlAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IMapControlAutomationPeer(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES MediaElementAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IMediaElementAutomationPeer,
        impl::base<MediaElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<MediaElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        MediaElementAutomationPeer(std::nullptr_t) noexcept {}
        MediaElementAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IMediaElementAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit MediaElementAutomationPeer(winrt::Windows::UI::Xaml::Controls::MediaElement const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES MediaPlayerElementAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IMediaPlayerElementAutomationPeer,
        impl::base<MediaPlayerElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<MediaPlayerElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        MediaPlayerElementAutomationPeer(std::nullptr_t) noexcept {}
        MediaPlayerElementAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IMediaPlayerElementAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit MediaPlayerElementAutomationPeer(winrt::Windows::UI::Xaml::Controls::MediaPlayerElement const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES MediaTransportControlsAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IMediaTransportControlsAutomationPeer,
        impl::base<MediaTransportControlsAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<MediaTransportControlsAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        MediaTransportControlsAutomationPeer(std::nullptr_t) noexcept {}
        MediaTransportControlsAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IMediaTransportControlsAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit MediaTransportControlsAutomationPeer(winrt::Windows::UI::Xaml::Controls::MediaTransportControls const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES MenuBarAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IMenuBarAutomationPeer,
        impl::base<MenuBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<MenuBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        MenuBarAutomationPeer(std::nullptr_t) noexcept {}
        MenuBarAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IMenuBarAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit MenuBarAutomationPeer(winrt::Windows::UI::Xaml::Controls::MenuBar const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES MenuBarItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeer,
        impl::base<MenuBarItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<MenuBarItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        MenuBarItemAutomationPeer(std::nullptr_t) noexcept {}
        MenuBarItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit MenuBarItemAutomationPeer(winrt::Windows::UI::Xaml::Controls::MenuBarItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES MenuFlyoutItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IMenuFlyoutItemAutomationPeer,
        impl::base<MenuFlyoutItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<MenuFlyoutItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        MenuFlyoutItemAutomationPeer(std::nullptr_t) noexcept {}
        MenuFlyoutItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IMenuFlyoutItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit MenuFlyoutItemAutomationPeer(winrt::Windows::UI::Xaml::Controls::MenuFlyoutItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES MenuFlyoutPresenterAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IMenuFlyoutPresenterAutomationPeer,
        impl::base<MenuFlyoutPresenterAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<MenuFlyoutPresenterAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Provider::IItemContainerProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        MenuFlyoutPresenterAutomationPeer(std::nullptr_t) noexcept {}
        MenuFlyoutPresenterAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IMenuFlyoutPresenterAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit MenuFlyoutPresenterAutomationPeer(winrt::Windows::UI::Xaml::Controls::MenuFlyoutPresenter const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeer,
        impl::base<NavigationViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ListViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<NavigationViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IListViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        NavigationViewItemAutomationPeer(std::nullptr_t) noexcept {}
        NavigationViewItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit NavigationViewItemAutomationPeer(winrt::Windows::UI::Xaml::Controls::NavigationViewItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES PasswordBoxAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IPasswordBoxAutomationPeer,
        impl::base<PasswordBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<PasswordBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        PasswordBoxAutomationPeer(std::nullptr_t) noexcept {}
        PasswordBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IPasswordBoxAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit PasswordBoxAutomationPeer(winrt::Windows::UI::Xaml::Controls::PasswordBox const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES PersonPictureAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeer,
        impl::base<PersonPictureAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<PersonPictureAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        PersonPictureAutomationPeer(std::nullptr_t) noexcept {}
        PersonPictureAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit PersonPictureAutomationPeer(winrt::Windows::UI::Xaml::Controls::PersonPicture const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES PickerFlyoutPresenterAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IPickerFlyoutPresenterAutomationPeer,
        impl::base<PickerFlyoutPresenterAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<PickerFlyoutPresenterAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        PickerFlyoutPresenterAutomationPeer(std::nullptr_t) noexcept {}
        PickerFlyoutPresenterAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IPickerFlyoutPresenterAutomationPeer(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PivotAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IPivotAutomationPeer,
        impl::base<PivotAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<PivotAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionProvider, winrt::Windows::UI::Xaml::Automation::Provider::IScrollProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Provider::IItemContainerProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        PivotAutomationPeer(std::nullptr_t) noexcept {}
        PivotAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IPivotAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit PivotAutomationPeer(winrt::Windows::UI::Xaml::Controls::Pivot const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES PivotItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IPivotItemAutomationPeer,
        impl::base<PivotItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<PivotItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        PivotItemAutomationPeer(std::nullptr_t) noexcept {}
        PivotItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IPivotItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit PivotItemAutomationPeer(winrt::Windows::UI::Xaml::Controls::PivotItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES PivotItemDataAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IPivotItemDataAutomationPeer,
        impl::base<PivotItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<PivotItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IScrollItemProvider, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionItemProvider, winrt::Windows::UI::Xaml::Automation::Provider::IVirtualizedItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        PivotItemDataAutomationPeer(std::nullptr_t) noexcept {}
        PivotItemDataAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IPivotItemDataAutomationPeer(ptr, take_ownership_from_abi) {}
        PivotItemDataAutomationPeer(winrt::Windows::Foundation::IInspectable const& item, winrt::Windows::UI::Xaml::Automation::Peers::PivotAutomationPeer const& parent);
    };
    struct WINRT_IMPL_EMPTY_BASES ProgressBarAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IProgressBarAutomationPeer,
        impl::base<ProgressBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::RangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ProgressBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IRangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ProgressBarAutomationPeer(std::nullptr_t) noexcept {}
        ProgressBarAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IProgressBarAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ProgressBarAutomationPeer(winrt::Windows::UI::Xaml::Controls::ProgressBar const& owner);
        using impl::consume_t<ProgressBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider>::SetValue;
        using impl::consume_t<ProgressBarAutomationPeer, winrt::Windows::UI::Xaml::IDependencyObject>::SetValue;
    };
    struct WINRT_IMPL_EMPTY_BASES ProgressRingAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IProgressRingAutomationPeer,
        impl::base<ProgressRingAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ProgressRingAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ProgressRingAutomationPeer(std::nullptr_t) noexcept {}
        ProgressRingAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IProgressRingAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ProgressRingAutomationPeer(winrt::Windows::UI::Xaml::Controls::ProgressRing const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES RadioButtonAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IRadioButtonAutomationPeer,
        impl::base<RadioButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ToggleButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RadioButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IToggleButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IToggleProvider, winrt::Windows::UI::Xaml::Automation::Peers::IButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RadioButtonAutomationPeer(std::nullptr_t) noexcept {}
        RadioButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IRadioButtonAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit RadioButtonAutomationPeer(winrt::Windows::UI::Xaml::Controls::RadioButton const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES RangeBaseAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IRangeBaseAutomationPeer,
        impl::base<RangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RangeBaseAutomationPeer(std::nullptr_t) noexcept {}
        RangeBaseAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IRangeBaseAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit RangeBaseAutomationPeer(winrt::Windows::UI::Xaml::Controls::Primitives::RangeBase const& owner);
        using impl::consume_t<RangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider>::SetValue;
        using impl::consume_t<RangeBaseAutomationPeer, winrt::Windows::UI::Xaml::IDependencyObject>::SetValue;
    };
    struct WINRT_IMPL_EMPTY_BASES RatingControlAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IRatingControlAutomationPeer,
        impl::base<RatingControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RatingControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RatingControlAutomationPeer(std::nullptr_t) noexcept {}
        RatingControlAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IRatingControlAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit RatingControlAutomationPeer(winrt::Windows::UI::Xaml::Controls::RatingControl const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES RepeatButtonAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IRepeatButtonAutomationPeer,
        impl::base<RepeatButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RepeatButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Peers::IButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RepeatButtonAutomationPeer(std::nullptr_t) noexcept {}
        RepeatButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IRepeatButtonAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit RepeatButtonAutomationPeer(winrt::Windows::UI::Xaml::Controls::Primitives::RepeatButton const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES RichEditBoxAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IRichEditBoxAutomationPeer,
        impl::base<RichEditBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RichEditBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RichEditBoxAutomationPeer(std::nullptr_t) noexcept {}
        RichEditBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IRichEditBoxAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit RichEditBoxAutomationPeer(winrt::Windows::UI::Xaml::Controls::RichEditBox const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES RichTextBlockAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IRichTextBlockAutomationPeer,
        impl::base<RichTextBlockAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RichTextBlockAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RichTextBlockAutomationPeer(std::nullptr_t) noexcept {}
        RichTextBlockAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IRichTextBlockAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit RichTextBlockAutomationPeer(winrt::Windows::UI::Xaml::Controls::RichTextBlock const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES RichTextBlockOverflowAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IRichTextBlockOverflowAutomationPeer,
        impl::base<RichTextBlockOverflowAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RichTextBlockOverflowAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RichTextBlockOverflowAutomationPeer(std::nullptr_t) noexcept {}
        RichTextBlockOverflowAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IRichTextBlockOverflowAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit RichTextBlockOverflowAutomationPeer(winrt::Windows::UI::Xaml::Controls::RichTextBlockOverflow const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ScrollBarAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IScrollBarAutomationPeer,
        impl::base<ScrollBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::RangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ScrollBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IRangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ScrollBarAutomationPeer(std::nullptr_t) noexcept {}
        ScrollBarAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IScrollBarAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ScrollBarAutomationPeer(winrt::Windows::UI::Xaml::Controls::Primitives::ScrollBar const& owner);
        using impl::consume_t<ScrollBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider>::SetValue;
        using impl::consume_t<ScrollBarAutomationPeer, winrt::Windows::UI::Xaml::IDependencyObject>::SetValue;
    };
    struct WINRT_IMPL_EMPTY_BASES ScrollViewerAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IScrollViewerAutomationPeer,
        impl::base<ScrollViewerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ScrollViewerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IScrollProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ScrollViewerAutomationPeer(std::nullptr_t) noexcept {}
        ScrollViewerAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IScrollViewerAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ScrollViewerAutomationPeer(winrt::Windows::UI::Xaml::Controls::ScrollViewer const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES SearchBoxAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ISearchBoxAutomationPeer,
        impl::base<SearchBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<SearchBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        SearchBoxAutomationPeer(std::nullptr_t) noexcept {}
        SearchBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ISearchBoxAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit SearchBoxAutomationPeer(winrt::Windows::UI::Xaml::Controls::SearchBox const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES SelectorAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ISelectorAutomationPeer,
        impl::base<SelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<SelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Provider::IItemContainerProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        SelectorAutomationPeer(std::nullptr_t) noexcept {}
        SelectorAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ISelectorAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit SelectorAutomationPeer(winrt::Windows::UI::Xaml::Controls::Primitives::Selector const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES SelectorItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ISelectorItemAutomationPeer,
        impl::base<SelectorItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<SelectorItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IVirtualizedItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        SelectorItemAutomationPeer(std::nullptr_t) noexcept {}
        SelectorItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ISelectorItemAutomationPeer(ptr, take_ownership_from_abi) {}
        SelectorItemAutomationPeer(winrt::Windows::Foundation::IInspectable const& item, winrt::Windows::UI::Xaml::Automation::Peers::SelectorAutomationPeer const& parent);
    };
    struct WINRT_IMPL_EMPTY_BASES SemanticZoomAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ISemanticZoomAutomationPeer,
        impl::base<SemanticZoomAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<SemanticZoomAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IToggleProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        SemanticZoomAutomationPeer(std::nullptr_t) noexcept {}
        SemanticZoomAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ISemanticZoomAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit SemanticZoomAutomationPeer(winrt::Windows::UI::Xaml::Controls::SemanticZoom const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES SettingsFlyoutAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ISettingsFlyoutAutomationPeer,
        impl::base<SettingsFlyoutAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<SettingsFlyoutAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        SettingsFlyoutAutomationPeer(std::nullptr_t) noexcept {}
        SettingsFlyoutAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ISettingsFlyoutAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit SettingsFlyoutAutomationPeer(winrt::Windows::UI::Xaml::Controls::SettingsFlyout const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES SliderAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ISliderAutomationPeer,
        impl::base<SliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::RangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<SliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IRangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        SliderAutomationPeer(std::nullptr_t) noexcept {}
        SliderAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ISliderAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit SliderAutomationPeer(winrt::Windows::UI::Xaml::Controls::Slider const& owner);
        using impl::consume_t<SliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider>::SetValue;
        using impl::consume_t<SliderAutomationPeer, winrt::Windows::UI::Xaml::IDependencyObject>::SetValue;
    };
    struct WINRT_IMPL_EMPTY_BASES TextBlockAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ITextBlockAutomationPeer,
        impl::base<TextBlockAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TextBlockAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TextBlockAutomationPeer(std::nullptr_t) noexcept {}
        TextBlockAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ITextBlockAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit TextBlockAutomationPeer(winrt::Windows::UI::Xaml::Controls::TextBlock const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES TextBoxAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ITextBoxAutomationPeer,
        impl::base<TextBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TextBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TextBoxAutomationPeer(std::nullptr_t) noexcept {}
        TextBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ITextBoxAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit TextBoxAutomationPeer(winrt::Windows::UI::Xaml::Controls::TextBox const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ThumbAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IThumbAutomationPeer,
        impl::base<ThumbAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ThumbAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ThumbAutomationPeer(std::nullptr_t) noexcept {}
        ThumbAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IThumbAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ThumbAutomationPeer(winrt::Windows::UI::Xaml::Controls::Primitives::Thumb const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES TimePickerAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ITimePickerAutomationPeer,
        impl::base<TimePickerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TimePickerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TimePickerAutomationPeer(std::nullptr_t) noexcept {}
        TimePickerAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ITimePickerAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit TimePickerAutomationPeer(winrt::Windows::UI::Xaml::Controls::TimePicker const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES TimePickerFlyoutPresenterAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ITimePickerFlyoutPresenterAutomationPeer,
        impl::base<TimePickerFlyoutPresenterAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TimePickerFlyoutPresenterAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TimePickerFlyoutPresenterAutomationPeer(std::nullptr_t) noexcept {}
        TimePickerFlyoutPresenterAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ITimePickerFlyoutPresenterAutomationPeer(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ToggleButtonAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IToggleButtonAutomationPeer,
        impl::base<ToggleButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ToggleButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IToggleProvider, winrt::Windows::UI::Xaml::Automation::Peers::IButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ToggleButtonAutomationPeer(std::nullptr_t) noexcept {}
        ToggleButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IToggleButtonAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ToggleButtonAutomationPeer(winrt::Windows::UI::Xaml::Controls::Primitives::ToggleButton const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ToggleMenuFlyoutItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IToggleMenuFlyoutItemAutomationPeer,
        impl::base<ToggleMenuFlyoutItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ToggleMenuFlyoutItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IToggleProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ToggleMenuFlyoutItemAutomationPeer(std::nullptr_t) noexcept {}
        ToggleMenuFlyoutItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IToggleMenuFlyoutItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ToggleMenuFlyoutItemAutomationPeer(winrt::Windows::UI::Xaml::Controls::ToggleMenuFlyoutItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ToggleSwitchAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::IToggleSwitchAutomationPeer,
        impl::base<ToggleSwitchAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ToggleSwitchAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IToggleProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ToggleSwitchAutomationPeer(std::nullptr_t) noexcept {}
        ToggleSwitchAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::IToggleSwitchAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ToggleSwitchAutomationPeer(winrt::Windows::UI::Xaml::Controls::ToggleSwitch const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES TreeViewItemAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeer,
        impl::base<TreeViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ListViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TreeViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Peers::IListViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TreeViewItemAutomationPeer(std::nullptr_t) noexcept {}
        TreeViewItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit TreeViewItemAutomationPeer(winrt::Windows::UI::Xaml::Controls::TreeViewItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES TreeViewListAutomationPeer : winrt::Windows::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeer,
        impl::base<TreeViewListAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TreeViewListAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ISelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Provider::IItemContainerProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TreeViewListAutomationPeer(std::nullptr_t) noexcept {}
        TreeViewListAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit TreeViewListAutomationPeer(winrt::Windows::UI::Xaml::Controls::TreeViewList const& owner);
    };
    template <typename D>
    class IAutomationPeerOverridesT
    {
        D& shim() noexcept { return *static_cast<D*>(this); }
        D const& shim() const noexcept { return *static_cast<const D*>(this); }
    public:
        using IAutomationPeerOverrides = winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides;
        auto GetPatternCore(winrt::Windows::UI::Xaml::Automation::Peers::PatternInterface const& patternInterface) const;
        auto GetAcceleratorKeyCore() const;
        auto GetAccessKeyCore() const;
        auto GetAutomationControlTypeCore() const;
        auto GetAutomationIdCore() const;
        auto GetBoundingRectangleCore() const;
        auto GetChildrenCore() const;
        auto GetClassNameCore() const;
        auto GetClickablePointCore() const;
        auto GetHelpTextCore() const;
        auto GetItemStatusCore() const;
        auto GetItemTypeCore() const;
        auto GetLabeledByCore() const;
        auto GetLocalizedControlTypeCore() const;
        auto GetNameCore() const;
        auto GetOrientationCore() const;
        auto HasKeyboardFocusCore() const;
        auto IsContentElementCore() const;
        auto IsControlElementCore() const;
        auto IsEnabledCore() const;
        auto IsKeyboardFocusableCore() const;
        auto IsOffscreenCore() const;
        auto IsPasswordCore() const;
        auto IsRequiredForFormCore() const;
        auto SetFocusCore() const;
        auto GetPeerFromPointCore(winrt::Windows::Foundation::Point const& point) const;
        auto GetLiveSettingCore() const;
    };
    template <typename D>
    class IAutomationPeerOverrides2T
    {
        D& shim() noexcept { return *static_cast<D*>(this); }
        D const& shim() const noexcept { return *static_cast<const D*>(this); }
    public:
        using IAutomationPeerOverrides2 = winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2;
        auto ShowContextMenuCore() const;
        auto GetControlledPeersCore() const;
    };
    template <typename D>
    class IAutomationPeerOverrides3T
    {
        D& shim() noexcept { return *static_cast<D*>(this); }
        D const& shim() const noexcept { return *static_cast<const D*>(this); }
    public:
        using IAutomationPeerOverrides3 = winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3;
        auto NavigateCore(winrt::Windows::UI::Xaml::Automation::Peers::AutomationNavigationDirection const& direction) const;
        auto GetElementFromPointCore(winrt::Windows::Foundation::Point const& pointInWindowCoordinates) const;
        auto GetFocusedElementCore() const;
        auto GetAnnotationsCore() const;
        auto GetPositionInSetCore() const;
        auto GetSizeOfSetCore() const;
        auto GetLevelCore() const;
    };
    template <typename D>
    class IAutomationPeerOverrides4T
    {
        D& shim() noexcept { return *static_cast<D*>(this); }
        D const& shim() const noexcept { return *static_cast<const D*>(this); }
    public:
        using IAutomationPeerOverrides4 = winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4;
        auto GetLandmarkTypeCore() const;
        auto GetLocalizedLandmarkTypeCore() const;
    };
    template <typename D>
    class IAutomationPeerOverrides5T
    {
        D& shim() noexcept { return *static_cast<D*>(this); }
        D const& shim() const noexcept { return *static_cast<const D*>(this); }
    public:
        using IAutomationPeerOverrides5 = winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5;
        auto IsPeripheralCore() const;
        auto IsDataValidForFormCore() const;
        auto GetFullDescriptionCore() const;
        auto GetDescribedByCore() const;
        auto GetFlowsToCore() const;
        auto GetFlowsFromCore() const;
    };
    template <typename D>
    class IAutomationPeerOverrides6T
    {
        D& shim() noexcept { return *static_cast<D*>(this); }
        D const& shim() const noexcept { return *static_cast<const D*>(this); }
    public:
        using IAutomationPeerOverrides6 = winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6;
        auto GetCultureCore() const;
    };
    template <typename D>
    class IAutomationPeerOverrides8T
    {
        D& shim() noexcept { return *static_cast<D*>(this); }
        D const& shim() const noexcept { return *static_cast<const D*>(this); }
    public:
        using IAutomationPeerOverrides8 = winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8;
        auto GetHeadingLevelCore() const;
    };
    template <typename D>
    class IAutomationPeerOverrides9T
    {
        D& shim() noexcept { return *static_cast<D*>(this); }
        D const& shim() const noexcept { return *static_cast<const D*>(this); }
    public:
        using IAutomationPeerOverrides9 = winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9;
        auto IsDialogCore() const;
    };
    template <typename D>
    class IItemsControlAutomationPeerOverrides2T
    {
        D& shim() noexcept { return *static_cast<D*>(this); }
        D const& shim() const noexcept { return *static_cast<const D*>(this); }
    public:
        using IItemsControlAutomationPeerOverrides2 = winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeerOverrides2;
        auto OnCreateItemAutomationPeer(winrt::Windows::Foundation::IInspectable const& item) const;
    };
}
#endif
