D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_adaptive_manifold.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_adaptive_manifold.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_adaptive_manifold_ref_impl.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_adaptive_manifold_ref_impl.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_anisodiff.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_anisodiff.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_bilateral_texture_filter.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_bilateral_texture_filter.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_deriche_filter.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_deriche_filter.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_disparity_wls_filter.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_disparity_wls_filter.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_domain_transform.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_domain_transform.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_edgeboxes.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_edgeboxes.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_edgepreserving_filter.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_edgepreserving_filter.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_fast_hough_transform.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_fast_hough_transform.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_fbs_filter.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_fbs_filter.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_fgs_filter.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_fgs_filter.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_find_ellipses.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_find_ellipses.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_fld.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_fld.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_fourier_descriptors.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_fourier_descriptors.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_guided_filter.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_guided_filter.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_joint_bilateral_filter.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_joint_bilateral_filter.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_l0_smooth.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_l0_smooth.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_main.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_main.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_matchcolortemplate.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_matchcolortemplate.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_niblack_threshold.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_niblack_threshold.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_radon_transform.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_radon_transform.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_ridge_detection_filter.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_ridge_detection_filter.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_rolling_guidance_filter.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_rolling_guidance_filter.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_run_length_morphology.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_run_length_morphology.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_scansegment.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_scansegment.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_slic.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_slic.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_sparse_match_interpolator.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_sparse_match_interpolator.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_structured_edge_detection.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_structured_edge_detection.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_thinning.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_thinning.obj
D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\test\test_weighted_median_filter.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.dir\Release\test_weighted_median_filter.obj
