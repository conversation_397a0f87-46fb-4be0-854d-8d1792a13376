﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\src\average_hash.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\src\block_mean_hash.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\src\color_moment_hash.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\src\img_hash_base.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\src\marr_hildreth_hash.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\src\phash.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\src\radial_variance_hash.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\cudabuild\modules\img_hash\opencv_img_hash_main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash.hpp">
      <Filter>Include\opencv2</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\average_hash.hpp">
      <Filter>Include\opencv2\img_hash</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\block_mean_hash.hpp">
      <Filter>Include\opencv2\img_hash</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\color_moment_hash.hpp">
      <Filter>Include\opencv2\img_hash</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\img_hash_base.hpp">
      <Filter>Include\opencv2\img_hash</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\marr_hildreth_hash.hpp">
      <Filter>Include\opencv2\img_hash</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\phash.hpp">
      <Filter>Include\opencv2\img_hash</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\include\opencv2\img_hash\radial_variance_hash.hpp">
      <Filter>Include\opencv2\img_hash</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\img_hash\src\precomp.hpp">
      <Filter>Src</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\cvconfig.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\opencv2\opencv_modules.hpp">
      <Filter>Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="D:\AI\opencv\cudabuild\modules\img_hash\vs_version.rc">
      <Filter>Src</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Include">
      <UniqueIdentifier>{0D522D53-72BF-369F-A761-1665CACACBF7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Include\opencv2">
      <UniqueIdentifier>{0C987C71-7C33-37AF-8FEA-10E4920FC58F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Include\opencv2\img_hash">
      <UniqueIdentifier>{19CB198B-E8FF-3981-820F-9311EC40311D}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{2A318434-2169-3E81-A653-C1C0490B5893}</UniqueIdentifier>
    </Filter>
    <Filter Include="Src">
      <UniqueIdentifier>{4C321FAF-208D-3921-91F6-A2582E33B23F}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
