// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Graphics_Printing_ProtectedPrint_0_H
#define WINRT_Windows_Graphics_Printing_ProtectedPrint_0_H
WINRT_EXPORT namespace winrt::Windows::Graphics::Printing::ProtectedPrint
{
    struct IWindowsProtectedPrintInfoStatics;
    struct WindowsProtectedPrintInfo;
}
namespace winrt::impl
{
    template <> struct category<winrt::Windows::Graphics::Printing::ProtectedPrint::IWindowsProtectedPrintInfoStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Graphics::Printing::ProtectedPrint::WindowsProtectedPrintInfo>{ using type = class_category; };
    template <> inline constexpr auto& name_v<winrt::Windows::Graphics::Printing::ProtectedPrint::WindowsProtectedPrintInfo> = L"Windows.Graphics.Printing.ProtectedPrint.WindowsProtectedPrintInfo";
    template <> inline constexpr auto& name_v<winrt::Windows::Graphics::Printing::ProtectedPrint::IWindowsProtectedPrintInfoStatics> = L"Windows.Graphics.Printing.ProtectedPrint.IWindowsProtectedPrintInfoStatics";
    template <> inline constexpr guid guid_v<winrt::Windows::Graphics::Printing::ProtectedPrint::IWindowsProtectedPrintInfoStatics>{ 0xA7D212F3,0x4168,0x5485,{ 0x98,0xAB,0xD8,0x9D,0x04,0x60,0x3B,0x40 } }; // A7D212F3-4168-5485-98AB-D89D04603B40
    template <> struct abi<winrt::Windows::Graphics::Printing::ProtectedPrint::IWindowsProtectedPrintInfoStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsProtectedPrintEnabled(bool*) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Windows_Graphics_Printing_ProtectedPrint_IWindowsProtectedPrintInfoStatics
    {
        [[nodiscard]] auto IsProtectedPrintEnabled() const;
    };
    template <> struct consume<winrt::Windows::Graphics::Printing::ProtectedPrint::IWindowsProtectedPrintInfoStatics>
    {
        template <typename D> using type = consume_Windows_Graphics_Printing_ProtectedPrint_IWindowsProtectedPrintInfoStatics<D>;
    };
}
#endif
