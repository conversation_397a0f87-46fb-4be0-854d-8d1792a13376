﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{DA191BC3-1259-3204-A2E1-B7E62F2CEC6B}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>opencv_perf_tests</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_bioinspired.vcxproj">
      <Project>{C1FEB36D-4FBB-371F-A03A-4993CF3F0A87}</Project>
      <Name>opencv_perf_bioinspired</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_calib3d.vcxproj">
      <Project>{D1D93572-C1E2-3BB3-B873-83349D483FE1}</Project>
      <Name>opencv_perf_calib3d</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.vcxproj">
      <Project>{36306458-1264-3737-8466-16B8BBEA87AB}</Project>
      <Name>opencv_perf_core</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudaarithm.vcxproj">
      <Project>{95BD6AFD-7651-322C-8C99-E8669BA52F80}</Project>
      <Name>opencv_perf_cudaarithm</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudabgsegm.vcxproj">
      <Project>{86B66096-C821-3DD6-9F1A-AC4C4101585E}</Project>
      <Name>opencv_perf_cudabgsegm</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudacodec.vcxproj">
      <Project>{B234651C-019E-39E9-B34A-137C60E711A7}</Project>
      <Name>opencv_perf_cudacodec</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudafeatures2d.vcxproj">
      <Project>{83B16A7A-2B33-3422-9C53-DAF36DB48C22}</Project>
      <Name>opencv_perf_cudafeatures2d</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudafilters.vcxproj">
      <Project>{CFCB55D5-CD4F-36E7-9398-107181B05336}</Project>
      <Name>opencv_perf_cudafilters</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudaimgproc.vcxproj">
      <Project>{F835E413-1BC8-33C6-BAC1-A9F19B718613}</Project>
      <Name>opencv_perf_cudaimgproc</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudalegacy.vcxproj">
      <Project>{F9CD9DC9-2315-3B2E-8549-700C0AA6FAE8}</Project>
      <Name>opencv_perf_cudalegacy</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudaobjdetect.vcxproj">
      <Project>{F9B177C0-A866-3586-BF1C-82EB50423F2D}</Project>
      <Name>opencv_perf_cudaobjdetect</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudaoptflow.vcxproj">
      <Project>{2D862446-A894-3760-A872-1FBFD2230942}</Project>
      <Name>opencv_perf_cudaoptflow</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudastereo.vcxproj">
      <Project>{14D47995-AB86-3820-9F1A-F2C724F982BE}</Project>
      <Name>opencv_perf_cudastereo</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudawarping.vcxproj">
      <Project>{399FC75A-4976-3F26-AD37-20E4372120B8}</Project>
      <Name>opencv_perf_cudawarping</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_dnn.vcxproj">
      <Project>{65484FAC-DBA1-395D-BB66-A28BF5F6DCC0}</Project>
      <Name>opencv_perf_dnn</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_dnn_superres.vcxproj">
      <Project>{ACFD9BDF-7968-3755-BBF4-A64AF925FFCC}</Project>
      <Name>opencv_perf_dnn_superres</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_features2d.vcxproj">
      <Project>{6A5C8157-3921-3A81-8AD8-2D9A54798119}</Project>
      <Name>opencv_perf_features2d</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_gapi.vcxproj">
      <Project>{F1DB0A13-FD34-3CC6-B7C1-CDF54AD0F871}</Project>
      <Name>opencv_perf_gapi</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_imgcodecs.vcxproj">
      <Project>{5A05B04F-5B35-31A9-BEA2-EEBE49FD1469}</Project>
      <Name>opencv_perf_imgcodecs</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_imgproc.vcxproj">
      <Project>{37AD4DB7-6D26-316C-BC4C-00A88BC39522}</Project>
      <Name>opencv_perf_imgproc</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_line_descriptor.vcxproj">
      <Project>{FF8BC5AE-369A-3EE7-984D-ACAFCE2902F1}</Project>
      <Name>opencv_perf_line_descriptor</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_mcc.vcxproj">
      <Project>{555100BA-6397-34EA-8367-0AD515D1167C}</Project>
      <Name>opencv_perf_mcc</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_objdetect.vcxproj">
      <Project>{BF1B7D49-1E44-3932-AF55-60878DE9858F}</Project>
      <Name>opencv_perf_objdetect</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_optflow.vcxproj">
      <Project>{42952552-9334-3AED-AECC-63C48D4AB5E7}</Project>
      <Name>opencv_perf_optflow</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_photo.vcxproj">
      <Project>{AFC8B534-DE25-3346-AF67-B0615A432FFB}</Project>
      <Name>opencv_perf_photo</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_reg.vcxproj">
      <Project>{9D6FB9F3-45AB-3C48-9491-5A6D12D69F81}</Project>
      <Name>opencv_perf_reg</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_rgbd.vcxproj">
      <Project>{F1871AA5-2651-3A13-97C5-92B6719E1E0E}</Project>
      <Name>opencv_perf_rgbd</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_signal.vcxproj">
      <Project>{5398681F-8D40-31A3-AF53-8B310ADBD646}</Project>
      <Name>opencv_perf_signal</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_stereo.vcxproj">
      <Project>{C04D9893-B65A-3CB5-BEA8-F7BC2B28125B}</Project>
      <Name>opencv_perf_stereo</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_stitching.vcxproj">
      <Project>{69796F08-E11B-37C4-A04A-437BABDD7AD0}</Project>
      <Name>opencv_perf_stitching</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_superres.vcxproj">
      <Project>{5CD2B0A5-0496-3D34-BA12-31E74C191782}</Project>
      <Name>opencv_perf_superres</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_tracking.vcxproj">
      <Project>{5AA0F741-7269-3AC0-B3A2-24B3585E56D5}</Project>
      <Name>opencv_perf_tracking</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_video.vcxproj">
      <Project>{3470A316-D0F1-3AFA-A9DA-EE4DEAA67804}</Project>
      <Name>opencv_perf_video</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_videoio.vcxproj">
      <Project>{B741E106-84DB-3AE1-91BA-E316CD342B09}</Project>
      <Name>opencv_perf_videoio</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_wechat_qrcode.vcxproj">
      <Project>{14857B7C-E07F-3009-B130-9F723F420AA5}</Project>
      <Name>opencv_perf_wechat_qrcode</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_xfeatures2d.vcxproj">
      <Project>{0ED4EA1B-1F60-38A9-A880-D094B9B25142}</Project>
      <Name>opencv_perf_xfeatures2d</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_ximgproc.vcxproj">
      <Project>{7800CC11-25D2-32F2-9E5C-18C37CCBBE19}</Project>
      <Name>opencv_perf_ximgproc</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_xphoto.vcxproj">
      <Project>{E137F199-4C9A-3207-811F-1AC426819C92}</Project>
      <Name>opencv_perf_xphoto</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>