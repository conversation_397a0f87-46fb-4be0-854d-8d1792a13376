<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Window reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">Window reference</div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>This is the reference documentation for window related functions and types, including creation, deletion and event polling. For more task-oriented information, see the <a class="el" href="window_guide.html">Window guide</a>. </p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:ga54ddb14825a1541a56e22afb5f832a9e" id="r_ga54ddb14825a1541a56e22afb5f832a9e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga54ddb14825a1541a56e22afb5f832a9e">GLFW_FOCUSED</a>&#160;&#160;&#160;0x00020001</td></tr>
<tr class="memdesc:ga54ddb14825a1541a56e22afb5f832a9e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Input focus window hint and attribute.  <br /></td></tr>
<tr class="separator:ga54ddb14825a1541a56e22afb5f832a9e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga39d44b7c056e55e581355a92d240b58a" id="r_ga39d44b7c056e55e581355a92d240b58a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga39d44b7c056e55e581355a92d240b58a">GLFW_ICONIFIED</a>&#160;&#160;&#160;0x00020002</td></tr>
<tr class="memdesc:ga39d44b7c056e55e581355a92d240b58a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window iconification window attribute.  <br /></td></tr>
<tr class="separator:ga39d44b7c056e55e581355a92d240b58a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadba13c7a1b3aa40831eb2beedbd5bd1d" id="r_gadba13c7a1b3aa40831eb2beedbd5bd1d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gadba13c7a1b3aa40831eb2beedbd5bd1d">GLFW_RESIZABLE</a>&#160;&#160;&#160;0x00020003</td></tr>
<tr class="memdesc:gadba13c7a1b3aa40831eb2beedbd5bd1d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window resize-ability window hint and attribute.  <br /></td></tr>
<tr class="separator:gadba13c7a1b3aa40831eb2beedbd5bd1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafb3cdc45297e06d8f1eb13adc69ca6c4" id="r_gafb3cdc45297e06d8f1eb13adc69ca6c4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gafb3cdc45297e06d8f1eb13adc69ca6c4">GLFW_VISIBLE</a>&#160;&#160;&#160;0x00020004</td></tr>
<tr class="memdesc:gafb3cdc45297e06d8f1eb13adc69ca6c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window visibility window hint and attribute.  <br /></td></tr>
<tr class="separator:gafb3cdc45297e06d8f1eb13adc69ca6c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga21b854d36314c94d65aed84405b2f25e" id="r_ga21b854d36314c94d65aed84405b2f25e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga21b854d36314c94d65aed84405b2f25e">GLFW_DECORATED</a>&#160;&#160;&#160;0x00020005</td></tr>
<tr class="memdesc:ga21b854d36314c94d65aed84405b2f25e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window decoration window hint and attribute.  <br /></td></tr>
<tr class="separator:ga21b854d36314c94d65aed84405b2f25e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9d9874fc928200136a6dcdad726aa252" id="r_ga9d9874fc928200136a6dcdad726aa252"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga9d9874fc928200136a6dcdad726aa252">GLFW_AUTO_ICONIFY</a>&#160;&#160;&#160;0x00020006</td></tr>
<tr class="memdesc:ga9d9874fc928200136a6dcdad726aa252"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window auto-iconification window hint and attribute.  <br /></td></tr>
<tr class="separator:ga9d9874fc928200136a6dcdad726aa252"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7fb0be51407783b41adbf5bec0b09d80" id="r_ga7fb0be51407783b41adbf5bec0b09d80"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga7fb0be51407783b41adbf5bec0b09d80">GLFW_FLOATING</a>&#160;&#160;&#160;0x00020007</td></tr>
<tr class="memdesc:ga7fb0be51407783b41adbf5bec0b09d80"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window decoration window hint and attribute.  <br /></td></tr>
<tr class="separator:ga7fb0be51407783b41adbf5bec0b09d80"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad8ccb396253ad0b72c6d4c917eb38a03" id="r_gad8ccb396253ad0b72c6d4c917eb38a03"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gad8ccb396253ad0b72c6d4c917eb38a03">GLFW_MAXIMIZED</a>&#160;&#160;&#160;0x00020008</td></tr>
<tr class="memdesc:gad8ccb396253ad0b72c6d4c917eb38a03"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window maximization window hint and attribute.  <br /></td></tr>
<tr class="separator:gad8ccb396253ad0b72c6d4c917eb38a03"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5ac0847c0aa0b3619f2855707b8a7a77" id="r_ga5ac0847c0aa0b3619f2855707b8a7a77"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga5ac0847c0aa0b3619f2855707b8a7a77">GLFW_CENTER_CURSOR</a>&#160;&#160;&#160;0x00020009</td></tr>
<tr class="memdesc:ga5ac0847c0aa0b3619f2855707b8a7a77"><td class="mdescLeft">&#160;</td><td class="mdescRight">Cursor centering window hint.  <br /></td></tr>
<tr class="separator:ga5ac0847c0aa0b3619f2855707b8a7a77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga60a0578c3b9449027d683a9c6abb9f14" id="r_ga60a0578c3b9449027d683a9c6abb9f14"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga60a0578c3b9449027d683a9c6abb9f14">GLFW_TRANSPARENT_FRAMEBUFFER</a>&#160;&#160;&#160;0x0002000A</td></tr>
<tr class="memdesc:ga60a0578c3b9449027d683a9c6abb9f14"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window framebuffer transparency hint and attribute.  <br /></td></tr>
<tr class="separator:ga60a0578c3b9449027d683a9c6abb9f14"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8665c71c6fa3d22425c6a0e8a3f89d8a" id="r_ga8665c71c6fa3d22425c6a0e8a3f89d8a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga8665c71c6fa3d22425c6a0e8a3f89d8a">GLFW_HOVERED</a>&#160;&#160;&#160;0x0002000B</td></tr>
<tr class="memdesc:ga8665c71c6fa3d22425c6a0e8a3f89d8a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Mouse cursor hover window attribute.  <br /></td></tr>
<tr class="separator:ga8665c71c6fa3d22425c6a0e8a3f89d8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafa94b1da34bfd6488c0d709761504dfc" id="r_gafa94b1da34bfd6488c0d709761504dfc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gafa94b1da34bfd6488c0d709761504dfc">GLFW_FOCUS_ON_SHOW</a>&#160;&#160;&#160;0x0002000C</td></tr>
<tr class="memdesc:gafa94b1da34bfd6488c0d709761504dfc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Input focus on calling show window hint and attribute.  <br /></td></tr>
<tr class="separator:gafa94b1da34bfd6488c0d709761504dfc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga88981797d29800808ec242274ab5c03a" id="r_ga88981797d29800808ec242274ab5c03a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga88981797d29800808ec242274ab5c03a">GLFW_MOUSE_PASSTHROUGH</a>&#160;&#160;&#160;0x0002000D</td></tr>
<tr class="memdesc:ga88981797d29800808ec242274ab5c03a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Mouse input transparency window hint and attribute.  <br /></td></tr>
<tr class="separator:ga88981797d29800808ec242274ab5c03a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaededa6b208b8e31343da56bb349c6fb2" id="r_gaededa6b208b8e31343da56bb349c6fb2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaededa6b208b8e31343da56bb349c6fb2">GLFW_POSITION_X</a>&#160;&#160;&#160;0x0002000E</td></tr>
<tr class="memdesc:gaededa6b208b8e31343da56bb349c6fb2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initial position x-coordinate window hint.  <br /></td></tr>
<tr class="separator:gaededa6b208b8e31343da56bb349c6fb2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6b3ccf63683c81f479e2a98f5027200e" id="r_ga6b3ccf63683c81f479e2a98f5027200e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga6b3ccf63683c81f479e2a98f5027200e">GLFW_POSITION_Y</a>&#160;&#160;&#160;0x0002000F</td></tr>
<tr class="memdesc:ga6b3ccf63683c81f479e2a98f5027200e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initial position y-coordinate window hint.  <br /></td></tr>
<tr class="separator:ga6b3ccf63683c81f479e2a98f5027200e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf78ed8e417dbcc1e354906cc2708c982" id="r_gaf78ed8e417dbcc1e354906cc2708c982"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaf78ed8e417dbcc1e354906cc2708c982">GLFW_RED_BITS</a>&#160;&#160;&#160;0x00021001</td></tr>
<tr class="memdesc:gaf78ed8e417dbcc1e354906cc2708c982"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:gaf78ed8e417dbcc1e354906cc2708c982"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafba3b72638c914e5fb8a237dd4c50d4d" id="r_gafba3b72638c914e5fb8a237dd4c50d4d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gafba3b72638c914e5fb8a237dd4c50d4d">GLFW_GREEN_BITS</a>&#160;&#160;&#160;0x00021002</td></tr>
<tr class="memdesc:gafba3b72638c914e5fb8a237dd4c50d4d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:gafba3b72638c914e5fb8a237dd4c50d4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab292ea403db6d514537b515311bf9ae3" id="r_gab292ea403db6d514537b515311bf9ae3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gab292ea403db6d514537b515311bf9ae3">GLFW_BLUE_BITS</a>&#160;&#160;&#160;0x00021003</td></tr>
<tr class="memdesc:gab292ea403db6d514537b515311bf9ae3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:gab292ea403db6d514537b515311bf9ae3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafed79a3f468997877da86c449bd43e8c" id="r_gafed79a3f468997877da86c449bd43e8c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gafed79a3f468997877da86c449bd43e8c">GLFW_ALPHA_BITS</a>&#160;&#160;&#160;0x00021004</td></tr>
<tr class="memdesc:gafed79a3f468997877da86c449bd43e8c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:gafed79a3f468997877da86c449bd43e8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga318a55eac1fee57dfe593b6d38149d07" id="r_ga318a55eac1fee57dfe593b6d38149d07"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga318a55eac1fee57dfe593b6d38149d07">GLFW_DEPTH_BITS</a>&#160;&#160;&#160;0x00021005</td></tr>
<tr class="memdesc:ga318a55eac1fee57dfe593b6d38149d07"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:ga318a55eac1fee57dfe593b6d38149d07"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5339890a45a1fb38e93cb9fcc5fd069d" id="r_ga5339890a45a1fb38e93cb9fcc5fd069d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga5339890a45a1fb38e93cb9fcc5fd069d">GLFW_STENCIL_BITS</a>&#160;&#160;&#160;0x00021006</td></tr>
<tr class="memdesc:ga5339890a45a1fb38e93cb9fcc5fd069d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:ga5339890a45a1fb38e93cb9fcc5fd069d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaead34a9a683b2bc20eecf30ba738bfc6" id="r_gaead34a9a683b2bc20eecf30ba738bfc6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaead34a9a683b2bc20eecf30ba738bfc6">GLFW_ACCUM_RED_BITS</a>&#160;&#160;&#160;0x00021007</td></tr>
<tr class="memdesc:gaead34a9a683b2bc20eecf30ba738bfc6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:gaead34a9a683b2bc20eecf30ba738bfc6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga65713cee1326f8e9d806fdf93187b471" id="r_ga65713cee1326f8e9d806fdf93187b471"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga65713cee1326f8e9d806fdf93187b471">GLFW_ACCUM_GREEN_BITS</a>&#160;&#160;&#160;0x00021008</td></tr>
<tr class="memdesc:ga65713cee1326f8e9d806fdf93187b471"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:ga65713cee1326f8e9d806fdf93187b471"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga22bbe9104a8ce1f8b88fb4f186aa36ce" id="r_ga22bbe9104a8ce1f8b88fb4f186aa36ce"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga22bbe9104a8ce1f8b88fb4f186aa36ce">GLFW_ACCUM_BLUE_BITS</a>&#160;&#160;&#160;0x00021009</td></tr>
<tr class="memdesc:ga22bbe9104a8ce1f8b88fb4f186aa36ce"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:ga22bbe9104a8ce1f8b88fb4f186aa36ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae829b55591c18169a40ab4067a041b1f" id="r_gae829b55591c18169a40ab4067a041b1f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gae829b55591c18169a40ab4067a041b1f">GLFW_ACCUM_ALPHA_BITS</a>&#160;&#160;&#160;0x0002100A</td></tr>
<tr class="memdesc:gae829b55591c18169a40ab4067a041b1f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer bit depth hint.  <br /></td></tr>
<tr class="separator:gae829b55591c18169a40ab4067a041b1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab05108c5029443b371112b031d1fa174" id="r_gab05108c5029443b371112b031d1fa174"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gab05108c5029443b371112b031d1fa174">GLFW_AUX_BUFFERS</a>&#160;&#160;&#160;0x0002100B</td></tr>
<tr class="memdesc:gab05108c5029443b371112b031d1fa174"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer auxiliary buffer hint.  <br /></td></tr>
<tr class="separator:gab05108c5029443b371112b031d1fa174"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga83d991efca02537e2d69969135b77b03" id="r_ga83d991efca02537e2d69969135b77b03"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga83d991efca02537e2d69969135b77b03">GLFW_STEREO</a>&#160;&#160;&#160;0x0002100C</td></tr>
<tr class="memdesc:ga83d991efca02537e2d69969135b77b03"><td class="mdescLeft">&#160;</td><td class="mdescRight">OpenGL stereoscopic rendering hint.  <br /></td></tr>
<tr class="separator:ga83d991efca02537e2d69969135b77b03"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2cdf86fdcb7722fb8829c4e201607535" id="r_ga2cdf86fdcb7722fb8829c4e201607535"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga2cdf86fdcb7722fb8829c4e201607535">GLFW_SAMPLES</a>&#160;&#160;&#160;0x0002100D</td></tr>
<tr class="memdesc:ga2cdf86fdcb7722fb8829c4e201607535"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer MSAA samples hint.  <br /></td></tr>
<tr class="separator:ga2cdf86fdcb7722fb8829c4e201607535"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga444a8f00414a63220591f9fdb7b5642b" id="r_ga444a8f00414a63220591f9fdb7b5642b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga444a8f00414a63220591f9fdb7b5642b">GLFW_SRGB_CAPABLE</a>&#160;&#160;&#160;0x0002100E</td></tr>
<tr class="memdesc:ga444a8f00414a63220591f9fdb7b5642b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer sRGB hint.  <br /></td></tr>
<tr class="separator:ga444a8f00414a63220591f9fdb7b5642b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0f20825e6e47ee8ba389024519682212" id="r_ga0f20825e6e47ee8ba389024519682212"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga0f20825e6e47ee8ba389024519682212">GLFW_REFRESH_RATE</a>&#160;&#160;&#160;0x0002100F</td></tr>
<tr class="memdesc:ga0f20825e6e47ee8ba389024519682212"><td class="mdescLeft">&#160;</td><td class="mdescRight">Monitor refresh rate hint.  <br /></td></tr>
<tr class="separator:ga0f20825e6e47ee8ba389024519682212"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga714a5d569e8a274ea58fdfa020955339" id="r_ga714a5d569e8a274ea58fdfa020955339"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga714a5d569e8a274ea58fdfa020955339">GLFW_DOUBLEBUFFER</a>&#160;&#160;&#160;0x00021010</td></tr>
<tr class="memdesc:ga714a5d569e8a274ea58fdfa020955339"><td class="mdescLeft">&#160;</td><td class="mdescRight">Framebuffer double buffering hint and attribute.  <br /></td></tr>
<tr class="separator:ga714a5d569e8a274ea58fdfa020955339"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga649309cf72a3d3de5b1348ca7936c95b" id="r_ga649309cf72a3d3de5b1348ca7936c95b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga649309cf72a3d3de5b1348ca7936c95b">GLFW_CLIENT_API</a>&#160;&#160;&#160;0x00022001</td></tr>
<tr class="memdesc:ga649309cf72a3d3de5b1348ca7936c95b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Context client API hint and attribute.  <br /></td></tr>
<tr class="separator:ga649309cf72a3d3de5b1348ca7936c95b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafe5e4922de1f9932d7e9849bb053b0c0" id="r_gafe5e4922de1f9932d7e9849bb053b0c0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gafe5e4922de1f9932d7e9849bb053b0c0">GLFW_CONTEXT_VERSION_MAJOR</a>&#160;&#160;&#160;0x00022002</td></tr>
<tr class="memdesc:gafe5e4922de1f9932d7e9849bb053b0c0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Context client API major version hint and attribute.  <br /></td></tr>
<tr class="separator:gafe5e4922de1f9932d7e9849bb053b0c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga31aca791e4b538c4e4a771eb95cc2d07" id="r_ga31aca791e4b538c4e4a771eb95cc2d07"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga31aca791e4b538c4e4a771eb95cc2d07">GLFW_CONTEXT_VERSION_MINOR</a>&#160;&#160;&#160;0x00022003</td></tr>
<tr class="memdesc:ga31aca791e4b538c4e4a771eb95cc2d07"><td class="mdescLeft">&#160;</td><td class="mdescRight">Context client API minor version hint and attribute.  <br /></td></tr>
<tr class="separator:ga31aca791e4b538c4e4a771eb95cc2d07"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafb9475071aa77c6fb05ca5a5c8678a08" id="r_gafb9475071aa77c6fb05ca5a5c8678a08"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gafb9475071aa77c6fb05ca5a5c8678a08">GLFW_CONTEXT_REVISION</a>&#160;&#160;&#160;0x00022004</td></tr>
<tr class="memdesc:gafb9475071aa77c6fb05ca5a5c8678a08"><td class="mdescLeft">&#160;</td><td class="mdescRight">Context client API revision number attribute.  <br /></td></tr>
<tr class="separator:gafb9475071aa77c6fb05ca5a5c8678a08"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gade3593916b4c507900aa2d6844810e00" id="r_gade3593916b4c507900aa2d6844810e00"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gade3593916b4c507900aa2d6844810e00">GLFW_CONTEXT_ROBUSTNESS</a>&#160;&#160;&#160;0x00022005</td></tr>
<tr class="memdesc:gade3593916b4c507900aa2d6844810e00"><td class="mdescLeft">&#160;</td><td class="mdescRight">Context robustness hint and attribute.  <br /></td></tr>
<tr class="separator:gade3593916b4c507900aa2d6844810e00"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga13d24b12465da8b28985f46c8557925b" id="r_ga13d24b12465da8b28985f46c8557925b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga13d24b12465da8b28985f46c8557925b">GLFW_OPENGL_FORWARD_COMPAT</a>&#160;&#160;&#160;0x00022006</td></tr>
<tr class="memdesc:ga13d24b12465da8b28985f46c8557925b"><td class="mdescLeft">&#160;</td><td class="mdescRight">OpenGL forward-compatibility hint and attribute.  <br /></td></tr>
<tr class="separator:ga13d24b12465da8b28985f46c8557925b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8d55e3afec73c7de0509c3b7ad1d9e3f" id="r_ga8d55e3afec73c7de0509c3b7ad1d9e3f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga8d55e3afec73c7de0509c3b7ad1d9e3f">GLFW_CONTEXT_DEBUG</a>&#160;&#160;&#160;0x00022007</td></tr>
<tr class="memdesc:ga8d55e3afec73c7de0509c3b7ad1d9e3f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Debug mode context hint and attribute.  <br /></td></tr>
<tr class="separator:ga8d55e3afec73c7de0509c3b7ad1d9e3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga87ec2df0b915201e950ca42d5d0831e1" id="r_ga87ec2df0b915201e950ca42d5d0831e1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga87ec2df0b915201e950ca42d5d0831e1">GLFW_OPENGL_DEBUG_CONTEXT</a>&#160;&#160;&#160;<a class="el" href="group__window.html#ga8d55e3afec73c7de0509c3b7ad1d9e3f">GLFW_CONTEXT_DEBUG</a></td></tr>
<tr class="memdesc:ga87ec2df0b915201e950ca42d5d0831e1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Legacy name for compatibility.  <br /></td></tr>
<tr class="separator:ga87ec2df0b915201e950ca42d5d0831e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga44f3a6b4261fbe351e0b950b0f372e12" id="r_ga44f3a6b4261fbe351e0b950b0f372e12"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga44f3a6b4261fbe351e0b950b0f372e12">GLFW_OPENGL_PROFILE</a>&#160;&#160;&#160;0x00022008</td></tr>
<tr class="memdesc:ga44f3a6b4261fbe351e0b950b0f372e12"><td class="mdescLeft">&#160;</td><td class="mdescRight">OpenGL profile hint and attribute.  <br /></td></tr>
<tr class="separator:ga44f3a6b4261fbe351e0b950b0f372e12"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga72b648a8378fe3310c7c7bbecc0f7be6" id="r_ga72b648a8378fe3310c7c7bbecc0f7be6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga72b648a8378fe3310c7c7bbecc0f7be6">GLFW_CONTEXT_RELEASE_BEHAVIOR</a>&#160;&#160;&#160;0x00022009</td></tr>
<tr class="memdesc:ga72b648a8378fe3310c7c7bbecc0f7be6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Context flush-on-release hint and attribute.  <br /></td></tr>
<tr class="separator:ga72b648a8378fe3310c7c7bbecc0f7be6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5a52fdfd46d8249c211f923675728082" id="r_ga5a52fdfd46d8249c211f923675728082"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga5a52fdfd46d8249c211f923675728082">GLFW_CONTEXT_NO_ERROR</a>&#160;&#160;&#160;0x0002200A</td></tr>
<tr class="memdesc:ga5a52fdfd46d8249c211f923675728082"><td class="mdescLeft">&#160;</td><td class="mdescRight">Context error suppression hint and attribute.  <br /></td></tr>
<tr class="separator:ga5a52fdfd46d8249c211f923675728082"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5154cebfcd831c1cc63a4d5ac9bb4486" id="r_ga5154cebfcd831c1cc63a4d5ac9bb4486"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga5154cebfcd831c1cc63a4d5ac9bb4486">GLFW_CONTEXT_CREATION_API</a>&#160;&#160;&#160;0x0002200B</td></tr>
<tr class="memdesc:ga5154cebfcd831c1cc63a4d5ac9bb4486"><td class="mdescLeft">&#160;</td><td class="mdescRight">Context creation API hint and attribute.  <br /></td></tr>
<tr class="separator:ga5154cebfcd831c1cc63a4d5ac9bb4486"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga620bc4280c7eab81ac9f02204500ed47" id="r_ga620bc4280c7eab81ac9f02204500ed47"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga620bc4280c7eab81ac9f02204500ed47">GLFW_SCALE_TO_MONITOR</a>&#160;&#160;&#160;0x0002200C</td></tr>
<tr class="memdesc:ga620bc4280c7eab81ac9f02204500ed47"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window content area scaling window <a class="el" href="window_guide.html#GLFW_SCALE_TO_MONITOR">window hint</a>.  <br /></td></tr>
<tr class="separator:ga620bc4280c7eab81ac9f02204500ed47"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa5a9c6b4722670fd33d6e8a88f2e21bc" id="r_gaa5a9c6b4722670fd33d6e8a88f2e21bc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaa5a9c6b4722670fd33d6e8a88f2e21bc">GLFW_SCALE_FRAMEBUFFER</a>&#160;&#160;&#160;0x0002200D</td></tr>
<tr class="memdesc:gaa5a9c6b4722670fd33d6e8a88f2e21bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Window framebuffer scaling <a class="el" href="window_guide.html#GLFW_SCALE_FRAMEBUFFER_hint">window hint</a>.  <br /></td></tr>
<tr class="separator:gaa5a9c6b4722670fd33d6e8a88f2e21bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab6ef2d02eb55800d249ccf1af253c35e" id="r_gab6ef2d02eb55800d249ccf1af253c35e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gab6ef2d02eb55800d249ccf1af253c35e">GLFW_COCOA_RETINA_FRAMEBUFFER</a>&#160;&#160;&#160;0x00023001</td></tr>
<tr class="memdesc:gab6ef2d02eb55800d249ccf1af253c35e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Legacy name for compatibility.  <br /></td></tr>
<tr class="separator:gab6ef2d02eb55800d249ccf1af253c35e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga70fa0fbc745de6aa824df79a580e84b5" id="r_ga70fa0fbc745de6aa824df79a580e84b5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga70fa0fbc745de6aa824df79a580e84b5">GLFW_COCOA_FRAME_NAME</a>&#160;&#160;&#160;0x00023002</td></tr>
<tr class="memdesc:ga70fa0fbc745de6aa824df79a580e84b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">macOS specific <a class="el" href="window_guide.html#GLFW_COCOA_FRAME_NAME_hint">window hint</a>.  <br /></td></tr>
<tr class="separator:ga70fa0fbc745de6aa824df79a580e84b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga53c84ed2ddd94e15bbd44b1f6f7feafc" id="r_ga53c84ed2ddd94e15bbd44b1f6f7feafc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga53c84ed2ddd94e15bbd44b1f6f7feafc">GLFW_COCOA_GRAPHICS_SWITCHING</a>&#160;&#160;&#160;0x00023003</td></tr>
<tr class="memdesc:ga53c84ed2ddd94e15bbd44b1f6f7feafc"><td class="mdescLeft">&#160;</td><td class="mdescRight">macOS specific <a class="el" href="window_guide.html#GLFW_COCOA_GRAPHICS_SWITCHING_hint">window hint</a>.  <br /></td></tr>
<tr class="separator:ga53c84ed2ddd94e15bbd44b1f6f7feafc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae5a9ea2fccccd92edbd343fc56461114" id="r_gae5a9ea2fccccd92edbd343fc56461114"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gae5a9ea2fccccd92edbd343fc56461114">GLFW_X11_CLASS_NAME</a>&#160;&#160;&#160;0x00024001</td></tr>
<tr class="memdesc:gae5a9ea2fccccd92edbd343fc56461114"><td class="mdescLeft">&#160;</td><td class="mdescRight">X11 specific <a class="el" href="window_guide.html#GLFW_X11_CLASS_NAME_hint">window hint</a>.  <br /></td></tr>
<tr class="separator:gae5a9ea2fccccd92edbd343fc56461114"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga494c3c0d911e4b860b946530a3e389e8" id="r_ga494c3c0d911e4b860b946530a3e389e8"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga494c3c0d911e4b860b946530a3e389e8">GLFW_X11_INSTANCE_NAME</a>&#160;&#160;&#160;0x00024002</td></tr>
<tr class="memdesc:ga494c3c0d911e4b860b946530a3e389e8"><td class="mdescLeft">&#160;</td><td class="mdescRight">X11 specific <a class="el" href="window_guide.html#GLFW_X11_CLASS_NAME_hint">window hint</a>.  <br /></td></tr>
<tr class="separator:ga494c3c0d911e4b860b946530a3e389e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf65ea8dafdc0edb07b821b9a336d5043" id="r_gaf65ea8dafdc0edb07b821b9a336d5043"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaf65ea8dafdc0edb07b821b9a336d5043">GLFW_WIN32_KEYBOARD_MENU</a>&#160;&#160;&#160;0x00025001</td></tr>
<tr class="separator:gaf65ea8dafdc0edb07b821b9a336d5043"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gace10f3846571de62243b46f75d978487" id="r_gace10f3846571de62243b46f75d978487"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gace10f3846571de62243b46f75d978487">GLFW_WIN32_SHOWDEFAULT</a>&#160;&#160;&#160;0x00025002</td></tr>
<tr class="memdesc:gace10f3846571de62243b46f75d978487"><td class="mdescLeft">&#160;</td><td class="mdescRight">Win32 specific <a class="el" href="window_guide.html#GLFW_WIN32_SHOWDEFAULT_hint">window hint</a>.  <br /></td></tr>
<tr class="separator:gace10f3846571de62243b46f75d978487"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafbf1ce7a4362c75e602a4df9e1bdecd3" id="r_gafbf1ce7a4362c75e602a4df9e1bdecd3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gafbf1ce7a4362c75e602a4df9e1bdecd3">GLFW_WAYLAND_APP_ID</a>&#160;&#160;&#160;0x00026001</td></tr>
<tr class="memdesc:gafbf1ce7a4362c75e602a4df9e1bdecd3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Wayland specific <a class="el" href="window_guide.html#GLFW_WAYLAND_APP_ID_hint">window hint</a>.  <br /></td></tr>
<tr class="separator:gafbf1ce7a4362c75e602a4df9e1bdecd3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga3c96d80d363e67d13a41b5d1821f3242" id="r_ga3c96d80d363e67d13a41b5d1821f3242"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a></td></tr>
<tr class="memdesc:ga3c96d80d363e67d13a41b5d1821f3242"><td class="mdescLeft">&#160;</td><td class="mdescRight">Opaque window object.  <br /></td></tr>
<tr class="separator:ga3c96d80d363e67d13a41b5d1821f3242"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabe287973a21a8f927cde4db06b8dcbe9" id="r_gabe287973a21a8f927cde4db06b8dcbe9"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gabe287973a21a8f927cde4db06b8dcbe9">GLFWwindowposfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int xpos, int ypos)</td></tr>
<tr class="memdesc:gabe287973a21a8f927cde4db06b8dcbe9"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for window position callbacks.  <br /></td></tr>
<tr class="separator:gabe287973a21a8f927cde4db06b8dcbe9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaec0282944bb810f6f3163ec02da90350" id="r_gaec0282944bb810f6f3163ec02da90350"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaec0282944bb810f6f3163ec02da90350">GLFWwindowsizefun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int width, int height)</td></tr>
<tr class="memdesc:gaec0282944bb810f6f3163ec02da90350"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for window size callbacks.  <br /></td></tr>
<tr class="separator:gaec0282944bb810f6f3163ec02da90350"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabf859b936d80961b7d39013a9694cc3e" id="r_gabf859b936d80961b7d39013a9694cc3e"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gabf859b936d80961b7d39013a9694cc3e">GLFWwindowclosefun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:gabf859b936d80961b7d39013a9694cc3e"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for window close callbacks.  <br /></td></tr>
<tr class="separator:gabf859b936d80961b7d39013a9694cc3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga431663a1427d2eb3a273bc398b6737b5" id="r_ga431663a1427d2eb3a273bc398b6737b5"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga431663a1427d2eb3a273bc398b6737b5">GLFWwindowrefreshfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga431663a1427d2eb3a273bc398b6737b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for window content refresh callbacks.  <br /></td></tr>
<tr class="separator:ga431663a1427d2eb3a273bc398b6737b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabc58c47e9d93f6eb1862d615c3680f46" id="r_gabc58c47e9d93f6eb1862d615c3680f46"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gabc58c47e9d93f6eb1862d615c3680f46">GLFWwindowfocusfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int focused)</td></tr>
<tr class="memdesc:gabc58c47e9d93f6eb1862d615c3680f46"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for window focus callbacks.  <br /></td></tr>
<tr class="separator:gabc58c47e9d93f6eb1862d615c3680f46"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga35c658cccba236f26e7adee0e25f6a4f" id="r_ga35c658cccba236f26e7adee0e25f6a4f"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga35c658cccba236f26e7adee0e25f6a4f">GLFWwindowiconifyfun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int iconified)</td></tr>
<tr class="memdesc:ga35c658cccba236f26e7adee0e25f6a4f"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for window iconify callbacks.  <br /></td></tr>
<tr class="separator:ga35c658cccba236f26e7adee0e25f6a4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3017196fdaec33ac3e095765176c2a90" id="r_ga3017196fdaec33ac3e095765176c2a90"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga3017196fdaec33ac3e095765176c2a90">GLFWwindowmaximizefun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int maximized)</td></tr>
<tr class="memdesc:ga3017196fdaec33ac3e095765176c2a90"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for window maximize callbacks.  <br /></td></tr>
<tr class="separator:ga3017196fdaec33ac3e095765176c2a90"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae18026e294dde685ed2e5f759533144d" id="r_gae18026e294dde685ed2e5f759533144d"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gae18026e294dde685ed2e5f759533144d">GLFWframebuffersizefun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int width, int height)</td></tr>
<tr class="memdesc:gae18026e294dde685ed2e5f759533144d"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for framebuffer size callbacks.  <br /></td></tr>
<tr class="separator:gae18026e294dde685ed2e5f759533144d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga77f288a2d04bb3c77c7d9615d08cf70e" id="r_ga77f288a2d04bb3c77c7d9615d08cf70e"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga77f288a2d04bb3c77c7d9615d08cf70e">GLFWwindowcontentscalefun</a>) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, float xscale, float yscale)</td></tr>
<tr class="memdesc:ga77f288a2d04bb3c77c7d9615d08cf70e"><td class="mdescLeft">&#160;</td><td class="mdescRight">The function pointer type for window content scale callbacks.  <br /></td></tr>
<tr class="separator:ga77f288a2d04bb3c77c7d9615d08cf70e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7cc0a09de172fa7250872046f8c4d2ca" id="r_ga7cc0a09de172fa7250872046f8c4d2ca"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="struct_g_l_f_wimage.html">GLFWimage</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga7cc0a09de172fa7250872046f8c4d2ca">GLFWimage</a></td></tr>
<tr class="memdesc:ga7cc0a09de172fa7250872046f8c4d2ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">Image data.  <br /></td></tr>
<tr class="separator:ga7cc0a09de172fa7250872046f8c4d2ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaa77c4898dfb83344a6b4f76aa16b9a4a" id="r_gaa77c4898dfb83344a6b4f76aa16b9a4a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaa77c4898dfb83344a6b4f76aa16b9a4a">glfwDefaultWindowHints</a> (void)</td></tr>
<tr class="memdesc:gaa77c4898dfb83344a6b4f76aa16b9a4a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Resets all window hints to their default values.  <br /></td></tr>
<tr class="separator:gaa77c4898dfb83344a6b4f76aa16b9a4a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7d9c8c62384b1e2821c4dc48952d2033" id="r_ga7d9c8c62384b1e2821c4dc48952d2033"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a> (int hint, int value)</td></tr>
<tr class="memdesc:ga7d9c8c62384b1e2821c4dc48952d2033"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the specified window hint to the desired value.  <br /></td></tr>
<tr class="separator:ga7d9c8c62384b1e2821c4dc48952d2033"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8cb2782861c9d997bcf2dea97f363e5f" id="r_ga8cb2782861c9d997bcf2dea97f363e5f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga8cb2782861c9d997bcf2dea97f363e5f">glfwWindowHintString</a> (int hint, const char *value)</td></tr>
<tr class="memdesc:ga8cb2782861c9d997bcf2dea97f363e5f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the specified window hint to the desired value.  <br /></td></tr>
<tr class="separator:ga8cb2782861c9d997bcf2dea97f363e5f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3555a418df92ad53f917597fe2f64aeb" id="r_ga3555a418df92ad53f917597fe2f64aeb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a> (int width, int height, const char *title, <a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, <a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *share)</td></tr>
<tr class="memdesc:ga3555a418df92ad53f917597fe2f64aeb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a window and its associated context.  <br /></td></tr>
<tr class="separator:ga3555a418df92ad53f917597fe2f64aeb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacdf43e51376051d2c091662e9fe3d7b2" id="r_gacdf43e51376051d2c091662e9fe3d7b2"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:gacdf43e51376051d2c091662e9fe3d7b2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Destroys the specified window and its context.  <br /></td></tr>
<tr class="separator:gacdf43e51376051d2c091662e9fe3d7b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga24e02fbfefbb81fc45320989f8140ab5" id="r_ga24e02fbfefbb81fc45320989f8140ab5"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga24e02fbfefbb81fc45320989f8140ab5">glfwWindowShouldClose</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga24e02fbfefbb81fc45320989f8140ab5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks the close flag of the specified window.  <br /></td></tr>
<tr class="separator:ga24e02fbfefbb81fc45320989f8140ab5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga49c449dde2a6f87d996f4daaa09d6708" id="r_ga49c449dde2a6f87d996f4daaa09d6708"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga49c449dde2a6f87d996f4daaa09d6708">glfwSetWindowShouldClose</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int value)</td></tr>
<tr class="memdesc:ga49c449dde2a6f87d996f4daaa09d6708"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the close flag of the specified window.  <br /></td></tr>
<tr class="separator:ga49c449dde2a6f87d996f4daaa09d6708"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac6151765c54b789c4fe66c6bc6215953" id="r_gac6151765c54b789c4fe66c6bc6215953"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gac6151765c54b789c4fe66c6bc6215953">glfwGetWindowTitle</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:gac6151765c54b789c4fe66c6bc6215953"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the title of the specified window.  <br /></td></tr>
<tr class="separator:gac6151765c54b789c4fe66c6bc6215953"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5d877f09e968cef7a360b513306f17ff" id="r_ga5d877f09e968cef7a360b513306f17ff"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga5d877f09e968cef7a360b513306f17ff">glfwSetWindowTitle</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, const char *title)</td></tr>
<tr class="memdesc:ga5d877f09e968cef7a360b513306f17ff"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the title of the specified window.  <br /></td></tr>
<tr class="separator:ga5d877f09e968cef7a360b513306f17ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadd7ccd39fe7a7d1f0904666ae5932dc5" id="r_gadd7ccd39fe7a7d1f0904666ae5932dc5"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gadd7ccd39fe7a7d1f0904666ae5932dc5">glfwSetWindowIcon</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int count, const <a class="el" href="struct_g_l_f_wimage.html">GLFWimage</a> *images)</td></tr>
<tr class="memdesc:gadd7ccd39fe7a7d1f0904666ae5932dc5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the icon for the specified window.  <br /></td></tr>
<tr class="separator:gadd7ccd39fe7a7d1f0904666ae5932dc5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga73cb526c000876fd8ddf571570fdb634" id="r_ga73cb526c000876fd8ddf571570fdb634"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga73cb526c000876fd8ddf571570fdb634">glfwGetWindowPos</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int *xpos, int *ypos)</td></tr>
<tr class="memdesc:ga73cb526c000876fd8ddf571570fdb634"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the position of the content area of the specified window.  <br /></td></tr>
<tr class="separator:ga73cb526c000876fd8ddf571570fdb634"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1abb6d690e8c88e0c8cd1751356dbca8" id="r_ga1abb6d690e8c88e0c8cd1751356dbca8"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga1abb6d690e8c88e0c8cd1751356dbca8">glfwSetWindowPos</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int xpos, int ypos)</td></tr>
<tr class="memdesc:ga1abb6d690e8c88e0c8cd1751356dbca8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the position of the content area of the specified window.  <br /></td></tr>
<tr class="separator:ga1abb6d690e8c88e0c8cd1751356dbca8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeea7cbc03373a41fb51cfbf9f2a5d4c6" id="r_gaeea7cbc03373a41fb51cfbf9f2a5d4c6"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaeea7cbc03373a41fb51cfbf9f2a5d4c6">glfwGetWindowSize</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int *width, int *height)</td></tr>
<tr class="memdesc:gaeea7cbc03373a41fb51cfbf9f2a5d4c6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the size of the content area of the specified window.  <br /></td></tr>
<tr class="separator:gaeea7cbc03373a41fb51cfbf9f2a5d4c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac314fa6cec7d2d307be9963e2709cc90" id="r_gac314fa6cec7d2d307be9963e2709cc90"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gac314fa6cec7d2d307be9963e2709cc90">glfwSetWindowSizeLimits</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int minwidth, int minheight, int maxwidth, int maxheight)</td></tr>
<tr class="memdesc:gac314fa6cec7d2d307be9963e2709cc90"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the size limits of the specified window.  <br /></td></tr>
<tr class="separator:gac314fa6cec7d2d307be9963e2709cc90"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga72ac8cb1ee2e312a878b55153d81b937" id="r_ga72ac8cb1ee2e312a878b55153d81b937"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga72ac8cb1ee2e312a878b55153d81b937">glfwSetWindowAspectRatio</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int numer, int denom)</td></tr>
<tr class="memdesc:ga72ac8cb1ee2e312a878b55153d81b937"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the aspect ratio of the specified window.  <br /></td></tr>
<tr class="separator:ga72ac8cb1ee2e312a878b55153d81b937"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga371911f12c74c504dd8d47d832d095cb" id="r_ga371911f12c74c504dd8d47d832d095cb"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga371911f12c74c504dd8d47d832d095cb">glfwSetWindowSize</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int width, int height)</td></tr>
<tr class="memdesc:ga371911f12c74c504dd8d47d832d095cb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the size of the content area of the specified window.  <br /></td></tr>
<tr class="separator:ga371911f12c74c504dd8d47d832d095cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0e2637a4161afb283f5300c7f94785c9" id="r_ga0e2637a4161afb283f5300c7f94785c9"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga0e2637a4161afb283f5300c7f94785c9">glfwGetFramebufferSize</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int *width, int *height)</td></tr>
<tr class="memdesc:ga0e2637a4161afb283f5300c7f94785c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the size of the framebuffer of the specified window.  <br /></td></tr>
<tr class="separator:ga0e2637a4161afb283f5300c7f94785c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1a9fd382058c53101b21cf211898f1f1" id="r_ga1a9fd382058c53101b21cf211898f1f1"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga1a9fd382058c53101b21cf211898f1f1">glfwGetWindowFrameSize</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int *left, int *top, int *right, int *bottom)</td></tr>
<tr class="memdesc:ga1a9fd382058c53101b21cf211898f1f1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the size of the frame of the window.  <br /></td></tr>
<tr class="separator:ga1a9fd382058c53101b21cf211898f1f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf5d31de9c19c4f994facea64d2b3106c" id="r_gaf5d31de9c19c4f994facea64d2b3106c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaf5d31de9c19c4f994facea64d2b3106c">glfwGetWindowContentScale</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, float *xscale, float *yscale)</td></tr>
<tr class="memdesc:gaf5d31de9c19c4f994facea64d2b3106c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the content scale for the specified window.  <br /></td></tr>
<tr class="separator:gaf5d31de9c19c4f994facea64d2b3106c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad09f0bd7a6307c4533b7061828480a84" id="r_gad09f0bd7a6307c4533b7061828480a84"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gad09f0bd7a6307c4533b7061828480a84">glfwGetWindowOpacity</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:gad09f0bd7a6307c4533b7061828480a84"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the opacity of the whole window.  <br /></td></tr>
<tr class="separator:gad09f0bd7a6307c4533b7061828480a84"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac31caeb3d1088831b13d2c8a156802e9" id="r_gac31caeb3d1088831b13d2c8a156802e9"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gac31caeb3d1088831b13d2c8a156802e9">glfwSetWindowOpacity</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, float opacity)</td></tr>
<tr class="memdesc:gac31caeb3d1088831b13d2c8a156802e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the opacity of the whole window.  <br /></td></tr>
<tr class="separator:gac31caeb3d1088831b13d2c8a156802e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1bb559c0ebaad63c5c05ad2a066779c4" id="r_ga1bb559c0ebaad63c5c05ad2a066779c4"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga1bb559c0ebaad63c5c05ad2a066779c4">glfwIconifyWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga1bb559c0ebaad63c5c05ad2a066779c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Iconifies the specified window.  <br /></td></tr>
<tr class="separator:ga1bb559c0ebaad63c5c05ad2a066779c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga52527a5904b47d802b6b4bb519cdebc7" id="r_ga52527a5904b47d802b6b4bb519cdebc7"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga52527a5904b47d802b6b4bb519cdebc7">glfwRestoreWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga52527a5904b47d802b6b4bb519cdebc7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Restores the specified window.  <br /></td></tr>
<tr class="separator:ga52527a5904b47d802b6b4bb519cdebc7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3f541387449d911274324ae7f17ec56b" id="r_ga3f541387449d911274324ae7f17ec56b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga3f541387449d911274324ae7f17ec56b">glfwMaximizeWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga3f541387449d911274324ae7f17ec56b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximizes the specified window.  <br /></td></tr>
<tr class="separator:ga3f541387449d911274324ae7f17ec56b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga61be47917b72536a148300f46494fc66" id="r_ga61be47917b72536a148300f46494fc66"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga61be47917b72536a148300f46494fc66">glfwShowWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga61be47917b72536a148300f46494fc66"><td class="mdescLeft">&#160;</td><td class="mdescRight">Makes the specified window visible.  <br /></td></tr>
<tr class="separator:ga61be47917b72536a148300f46494fc66"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga49401f82a1ba5f15db5590728314d47c" id="r_ga49401f82a1ba5f15db5590728314d47c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga49401f82a1ba5f15db5590728314d47c">glfwHideWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga49401f82a1ba5f15db5590728314d47c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Hides the specified window.  <br /></td></tr>
<tr class="separator:ga49401f82a1ba5f15db5590728314d47c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga873780357abd3f3a081d71a40aae45a1" id="r_ga873780357abd3f3a081d71a40aae45a1"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga873780357abd3f3a081d71a40aae45a1">glfwFocusWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga873780357abd3f3a081d71a40aae45a1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Brings the specified window to front and sets input focus.  <br /></td></tr>
<tr class="separator:ga873780357abd3f3a081d71a40aae45a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2f8d59323fc4692c1d54ba08c863a703" id="r_ga2f8d59323fc4692c1d54ba08c863a703"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga2f8d59323fc4692c1d54ba08c863a703">glfwRequestWindowAttention</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga2f8d59323fc4692c1d54ba08c863a703"><td class="mdescLeft">&#160;</td><td class="mdescRight">Requests user attention to the specified window.  <br /></td></tr>
<tr class="separator:ga2f8d59323fc4692c1d54ba08c863a703"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4d766499ac02c60f02221a9dfab87299" id="r_ga4d766499ac02c60f02221a9dfab87299"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga4d766499ac02c60f02221a9dfab87299">glfwGetWindowMonitor</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga4d766499ac02c60f02221a9dfab87299"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the monitor that the window uses for full screen mode.  <br /></td></tr>
<tr class="separator:ga4d766499ac02c60f02221a9dfab87299"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga81c76c418af80a1cce7055bccb0ae0a7" id="r_ga81c76c418af80a1cce7055bccb0ae0a7"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga81c76c418af80a1cce7055bccb0ae0a7">glfwSetWindowMonitor</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor, int xpos, int ypos, int width, int height, int refreshRate)</td></tr>
<tr class="memdesc:ga81c76c418af80a1cce7055bccb0ae0a7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the mode, monitor, video mode and placement of a window.  <br /></td></tr>
<tr class="separator:ga81c76c418af80a1cce7055bccb0ae0a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacccb29947ea4b16860ebef42c2cb9337" id="r_gacccb29947ea4b16860ebef42c2cb9337"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int attrib)</td></tr>
<tr class="memdesc:gacccb29947ea4b16860ebef42c2cb9337"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an attribute of the specified window.  <br /></td></tr>
<tr class="separator:gacccb29947ea4b16860ebef42c2cb9337"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gace2afda29b4116ec012e410a6819033e" id="r_gace2afda29b4116ec012e410a6819033e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gace2afda29b4116ec012e410a6819033e">glfwSetWindowAttrib</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int attrib, int value)</td></tr>
<tr class="memdesc:gace2afda29b4116ec012e410a6819033e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets an attribute of the specified window.  <br /></td></tr>
<tr class="separator:gace2afda29b4116ec012e410a6819033e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3d2fc6026e690ab31a13f78bc9fd3651" id="r_ga3d2fc6026e690ab31a13f78bc9fd3651"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga3d2fc6026e690ab31a13f78bc9fd3651">glfwSetWindowUserPointer</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, void *pointer)</td></tr>
<tr class="memdesc:ga3d2fc6026e690ab31a13f78bc9fd3651"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the user pointer of the specified window.  <br /></td></tr>
<tr class="separator:ga3d2fc6026e690ab31a13f78bc9fd3651"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae77a4add0d2023ca21ff1443ced01653" id="r_gae77a4add0d2023ca21ff1443ced01653"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gae77a4add0d2023ca21ff1443ced01653">glfwGetWindowUserPointer</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:gae77a4add0d2023ca21ff1443ced01653"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the user pointer of the specified window.  <br /></td></tr>
<tr class="separator:gae77a4add0d2023ca21ff1443ced01653"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga08bdfbba88934f9c4f92fd757979ac74" id="r_ga08bdfbba88934f9c4f92fd757979ac74"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#gabe287973a21a8f927cde4db06b8dcbe9">GLFWwindowposfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga08bdfbba88934f9c4f92fd757979ac74">glfwSetWindowPosCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#gabe287973a21a8f927cde4db06b8dcbe9">GLFWwindowposfun</a> callback)</td></tr>
<tr class="memdesc:ga08bdfbba88934f9c4f92fd757979ac74"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the position callback for the specified window.  <br /></td></tr>
<tr class="separator:ga08bdfbba88934f9c4f92fd757979ac74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad91b8b047a0c4c6033c38853864c34f8" id="r_gad91b8b047a0c4c6033c38853864c34f8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#gaec0282944bb810f6f3163ec02da90350">GLFWwindowsizefun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gad91b8b047a0c4c6033c38853864c34f8">glfwSetWindowSizeCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#gaec0282944bb810f6f3163ec02da90350">GLFWwindowsizefun</a> callback)</td></tr>
<tr class="memdesc:gad91b8b047a0c4c6033c38853864c34f8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the size callback for the specified window.  <br /></td></tr>
<tr class="separator:gad91b8b047a0c4c6033c38853864c34f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gada646d775a7776a95ac000cfc1885331" id="r_gada646d775a7776a95ac000cfc1885331"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#gabf859b936d80961b7d39013a9694cc3e">GLFWwindowclosefun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gada646d775a7776a95ac000cfc1885331">glfwSetWindowCloseCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#gabf859b936d80961b7d39013a9694cc3e">GLFWwindowclosefun</a> callback)</td></tr>
<tr class="memdesc:gada646d775a7776a95ac000cfc1885331"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the close callback for the specified window.  <br /></td></tr>
<tr class="separator:gada646d775a7776a95ac000cfc1885331"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1c5c7eb889c33c7f4d10dd35b327654e" id="r_ga1c5c7eb889c33c7f4d10dd35b327654e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#ga431663a1427d2eb3a273bc398b6737b5">GLFWwindowrefreshfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga1c5c7eb889c33c7f4d10dd35b327654e">glfwSetWindowRefreshCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#ga431663a1427d2eb3a273bc398b6737b5">GLFWwindowrefreshfun</a> callback)</td></tr>
<tr class="memdesc:ga1c5c7eb889c33c7f4d10dd35b327654e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the refresh callback for the specified window.  <br /></td></tr>
<tr class="separator:ga1c5c7eb889c33c7f4d10dd35b327654e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac2d83c4a10f071baf841f6730528e66c" id="r_gac2d83c4a10f071baf841f6730528e66c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#gabc58c47e9d93f6eb1862d615c3680f46">GLFWwindowfocusfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gac2d83c4a10f071baf841f6730528e66c">glfwSetWindowFocusCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#gabc58c47e9d93f6eb1862d615c3680f46">GLFWwindowfocusfun</a> callback)</td></tr>
<tr class="memdesc:gac2d83c4a10f071baf841f6730528e66c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the focus callback for the specified window.  <br /></td></tr>
<tr class="separator:gac2d83c4a10f071baf841f6730528e66c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac793e9efd255567b5fb8b445052cfd3e" id="r_gac793e9efd255567b5fb8b445052cfd3e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#ga35c658cccba236f26e7adee0e25f6a4f">GLFWwindowiconifyfun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gac793e9efd255567b5fb8b445052cfd3e">glfwSetWindowIconifyCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#ga35c658cccba236f26e7adee0e25f6a4f">GLFWwindowiconifyfun</a> callback)</td></tr>
<tr class="memdesc:gac793e9efd255567b5fb8b445052cfd3e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the iconify callback for the specified window.  <br /></td></tr>
<tr class="separator:gac793e9efd255567b5fb8b445052cfd3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacbe64c339fbd94885e62145563b6dc93" id="r_gacbe64c339fbd94885e62145563b6dc93"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#ga3017196fdaec33ac3e095765176c2a90">GLFWwindowmaximizefun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gacbe64c339fbd94885e62145563b6dc93">glfwSetWindowMaximizeCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#ga3017196fdaec33ac3e095765176c2a90">GLFWwindowmaximizefun</a> callback)</td></tr>
<tr class="memdesc:gacbe64c339fbd94885e62145563b6dc93"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the maximize callback for the specified window.  <br /></td></tr>
<tr class="separator:gacbe64c339fbd94885e62145563b6dc93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab3fb7c3366577daef18c0023e2a8591f" id="r_gab3fb7c3366577daef18c0023e2a8591f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#gae18026e294dde685ed2e5f759533144d">GLFWframebuffersizefun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gab3fb7c3366577daef18c0023e2a8591f">glfwSetFramebufferSizeCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#gae18026e294dde685ed2e5f759533144d">GLFWframebuffersizefun</a> callback)</td></tr>
<tr class="memdesc:gab3fb7c3366577daef18c0023e2a8591f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the framebuffer resize callback for the specified window.  <br /></td></tr>
<tr class="separator:gab3fb7c3366577daef18c0023e2a8591f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf2832ebb5aa6c252a2d261de002c92d6" id="r_gaf2832ebb5aa6c252a2d261de002c92d6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#ga77f288a2d04bb3c77c7d9615d08cf70e">GLFWwindowcontentscalefun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gaf2832ebb5aa6c252a2d261de002c92d6">glfwSetWindowContentScaleCallback</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, <a class="el" href="group__window.html#ga77f288a2d04bb3c77c7d9615d08cf70e">GLFWwindowcontentscalefun</a> callback)</td></tr>
<tr class="memdesc:gaf2832ebb5aa6c252a2d261de002c92d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the window content scale callback for the specified window.  <br /></td></tr>
<tr class="separator:gaf2832ebb5aa6c252a2d261de002c92d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga37bd57223967b4211d60ca1a0bf3c832" id="r_ga37bd57223967b4211d60ca1a0bf3c832"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a> (void)</td></tr>
<tr class="memdesc:ga37bd57223967b4211d60ca1a0bf3c832"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processes all pending events.  <br /></td></tr>
<tr class="separator:ga37bd57223967b4211d60ca1a0bf3c832"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga554e37d781f0a997656c26b2c56c835e" id="r_ga554e37d781f0a997656c26b2c56c835e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga554e37d781f0a997656c26b2c56c835e">glfwWaitEvents</a> (void)</td></tr>
<tr class="memdesc:ga554e37d781f0a997656c26b2c56c835e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Waits until events are queued and processes them.  <br /></td></tr>
<tr class="separator:ga554e37d781f0a997656c26b2c56c835e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga605a178db92f1a7f1a925563ef3ea2cf" id="r_ga605a178db92f1a7f1a925563ef3ea2cf"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga605a178db92f1a7f1a925563ef3ea2cf">glfwWaitEventsTimeout</a> (double timeout)</td></tr>
<tr class="memdesc:ga605a178db92f1a7f1a925563ef3ea2cf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Waits with timeout until events are queued and processes them.  <br /></td></tr>
<tr class="separator:ga605a178db92f1a7f1a925563ef3ea2cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab5997a25187e9fd5c6f2ecbbc8dfd7e9" id="r_gab5997a25187e9fd5c6f2ecbbc8dfd7e9"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#gab5997a25187e9fd5c6f2ecbbc8dfd7e9">glfwPostEmptyEvent</a> (void)</td></tr>
<tr class="memdesc:gab5997a25187e9fd5c6f2ecbbc8dfd7e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Posts an empty event to the event queue.  <br /></td></tr>
<tr class="separator:gab5997a25187e9fd5c6f2ecbbc8dfd7e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga15a5a1ee5b3c2ca6b15ca209a12efd14" id="r_ga15a5a1ee5b3c2ca6b15ca209a12efd14"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga15a5a1ee5b3c2ca6b15ca209a12efd14"><td class="mdescLeft">&#160;</td><td class="mdescRight">Swaps the front and back buffers of the specified window.  <br /></td></tr>
<tr class="separator:ga15a5a1ee5b3c2ca6b15ca209a12efd14"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="ga54ddb14825a1541a56e22afb5f832a9e" name="ga54ddb14825a1541a56e22afb5f832a9e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga54ddb14825a1541a56e22afb5f832a9e">&#9670;&#160;</a></span>GLFW_FOCUSED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_FOCUSED&#160;&#160;&#160;0x00020001</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Input focus <a class="el" href="window_guide.html#GLFW_FOCUSED_hint">window hint</a> or <a class="el" href="window_guide.html#GLFW_FOCUSED_attrib">window attribute</a>. </p>

</div>
</div>
<a id="ga39d44b7c056e55e581355a92d240b58a" name="ga39d44b7c056e55e581355a92d240b58a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga39d44b7c056e55e581355a92d240b58a">&#9670;&#160;</a></span>GLFW_ICONIFIED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ICONIFIED&#160;&#160;&#160;0x00020002</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Window iconification <a class="el" href="window_guide.html#GLFW_ICONIFIED_attrib">window attribute</a>. </p>

</div>
</div>
<a id="gadba13c7a1b3aa40831eb2beedbd5bd1d" name="gadba13c7a1b3aa40831eb2beedbd5bd1d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadba13c7a1b3aa40831eb2beedbd5bd1d">&#9670;&#160;</a></span>GLFW_RESIZABLE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_RESIZABLE&#160;&#160;&#160;0x00020003</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Window resize-ability <a class="el" href="window_guide.html#GLFW_RESIZABLE_hint">window hint</a> and <a class="el" href="window_guide.html#GLFW_RESIZABLE_attrib">window attribute</a>. </p>

</div>
</div>
<a id="gafb3cdc45297e06d8f1eb13adc69ca6c4" name="gafb3cdc45297e06d8f1eb13adc69ca6c4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafb3cdc45297e06d8f1eb13adc69ca6c4">&#9670;&#160;</a></span>GLFW_VISIBLE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_VISIBLE&#160;&#160;&#160;0x00020004</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Window visibility <a class="el" href="window_guide.html#GLFW_VISIBLE_hint">window hint</a> and <a class="el" href="window_guide.html#GLFW_VISIBLE_attrib">window attribute</a>. </p>

</div>
</div>
<a id="ga21b854d36314c94d65aed84405b2f25e" name="ga21b854d36314c94d65aed84405b2f25e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga21b854d36314c94d65aed84405b2f25e">&#9670;&#160;</a></span>GLFW_DECORATED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_DECORATED&#160;&#160;&#160;0x00020005</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Window decoration <a class="el" href="window_guide.html#GLFW_DECORATED_hint">window hint</a> and <a class="el" href="window_guide.html#GLFW_DECORATED_attrib">window attribute</a>. </p>

</div>
</div>
<a id="ga9d9874fc928200136a6dcdad726aa252" name="ga9d9874fc928200136a6dcdad726aa252"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9d9874fc928200136a6dcdad726aa252">&#9670;&#160;</a></span>GLFW_AUTO_ICONIFY</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_AUTO_ICONIFY&#160;&#160;&#160;0x00020006</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Window auto-iconification <a class="el" href="window_guide.html#GLFW_AUTO_ICONIFY_hint">window hint</a> and <a class="el" href="window_guide.html#GLFW_AUTO_ICONIFY_attrib">window attribute</a>. </p>

</div>
</div>
<a id="ga7fb0be51407783b41adbf5bec0b09d80" name="ga7fb0be51407783b41adbf5bec0b09d80"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7fb0be51407783b41adbf5bec0b09d80">&#9670;&#160;</a></span>GLFW_FLOATING</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_FLOATING&#160;&#160;&#160;0x00020007</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Window decoration <a class="el" href="window_guide.html#GLFW_FLOATING_hint">window hint</a> and <a class="el" href="window_guide.html#GLFW_FLOATING_attrib">window attribute</a>. </p>

</div>
</div>
<a id="gad8ccb396253ad0b72c6d4c917eb38a03" name="gad8ccb396253ad0b72c6d4c917eb38a03"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad8ccb396253ad0b72c6d4c917eb38a03">&#9670;&#160;</a></span>GLFW_MAXIMIZED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MAXIMIZED&#160;&#160;&#160;0x00020008</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Window maximization <a class="el" href="window_guide.html#GLFW_MAXIMIZED_hint">window hint</a> and <a class="el" href="window_guide.html#GLFW_MAXIMIZED_attrib">window attribute</a>. </p>

</div>
</div>
<a id="ga5ac0847c0aa0b3619f2855707b8a7a77" name="ga5ac0847c0aa0b3619f2855707b8a7a77"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5ac0847c0aa0b3619f2855707b8a7a77">&#9670;&#160;</a></span>GLFW_CENTER_CURSOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CENTER_CURSOR&#160;&#160;&#160;0x00020009</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Cursor centering <a class="el" href="window_guide.html#GLFW_CENTER_CURSOR_hint">window hint</a>. </p>

</div>
</div>
<a id="ga60a0578c3b9449027d683a9c6abb9f14" name="ga60a0578c3b9449027d683a9c6abb9f14"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga60a0578c3b9449027d683a9c6abb9f14">&#9670;&#160;</a></span>GLFW_TRANSPARENT_FRAMEBUFFER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_TRANSPARENT_FRAMEBUFFER&#160;&#160;&#160;0x0002000A</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Window framebuffer transparency <a class="el" href="window_guide.html#GLFW_TRANSPARENT_FRAMEBUFFER_hint">window hint</a> and <a class="el" href="window_guide.html#GLFW_TRANSPARENT_FRAMEBUFFER_attrib">window attribute</a>. </p>

</div>
</div>
<a id="ga8665c71c6fa3d22425c6a0e8a3f89d8a" name="ga8665c71c6fa3d22425c6a0e8a3f89d8a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8665c71c6fa3d22425c6a0e8a3f89d8a">&#9670;&#160;</a></span>GLFW_HOVERED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_HOVERED&#160;&#160;&#160;0x0002000B</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Mouse cursor hover <a class="el" href="window_guide.html#GLFW_HOVERED_attrib">window attribute</a>. </p>

</div>
</div>
<a id="gafa94b1da34bfd6488c0d709761504dfc" name="gafa94b1da34bfd6488c0d709761504dfc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafa94b1da34bfd6488c0d709761504dfc">&#9670;&#160;</a></span>GLFW_FOCUS_ON_SHOW</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_FOCUS_ON_SHOW&#160;&#160;&#160;0x0002000C</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Input focus <a class="el" href="window_guide.html#GLFW_FOCUS_ON_SHOW_hint">window hint</a> or <a class="el" href="window_guide.html#GLFW_FOCUS_ON_SHOW_attrib">window attribute</a>. </p>

</div>
</div>
<a id="ga88981797d29800808ec242274ab5c03a" name="ga88981797d29800808ec242274ab5c03a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga88981797d29800808ec242274ab5c03a">&#9670;&#160;</a></span>GLFW_MOUSE_PASSTHROUGH</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOUSE_PASSTHROUGH&#160;&#160;&#160;0x0002000D</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Mouse input transparency <a class="el" href="window_guide.html#GLFW_MOUSE_PASSTHROUGH_hint">window hint</a> or <a class="el" href="window_guide.html#GLFW_MOUSE_PASSTHROUGH_attrib">window attribute</a>. </p>

</div>
</div>
<a id="gaededa6b208b8e31343da56bb349c6fb2" name="gaededa6b208b8e31343da56bb349c6fb2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaededa6b208b8e31343da56bb349c6fb2">&#9670;&#160;</a></span>GLFW_POSITION_X</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_POSITION_X&#160;&#160;&#160;0x0002000E</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Initial position x-coordinate <a class="el" href="window_guide.html#GLFW_POSITION_X">window hint</a>. </p>

</div>
</div>
<a id="ga6b3ccf63683c81f479e2a98f5027200e" name="ga6b3ccf63683c81f479e2a98f5027200e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6b3ccf63683c81f479e2a98f5027200e">&#9670;&#160;</a></span>GLFW_POSITION_Y</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_POSITION_Y&#160;&#160;&#160;0x0002000F</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Initial position y-coordinate <a class="el" href="window_guide.html#GLFW_POSITION_Y">window hint</a>. </p>

</div>
</div>
<a id="gaf78ed8e417dbcc1e354906cc2708c982" name="gaf78ed8e417dbcc1e354906cc2708c982"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf78ed8e417dbcc1e354906cc2708c982">&#9670;&#160;</a></span>GLFW_RED_BITS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_RED_BITS&#160;&#160;&#160;0x00021001</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Framebuffer bit depth <a class="el" href="window_guide.html#GLFW_RED_BITS">hint</a>. </p>

</div>
</div>
<a id="gafba3b72638c914e5fb8a237dd4c50d4d" name="gafba3b72638c914e5fb8a237dd4c50d4d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafba3b72638c914e5fb8a237dd4c50d4d">&#9670;&#160;</a></span>GLFW_GREEN_BITS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GREEN_BITS&#160;&#160;&#160;0x00021002</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Framebuffer bit depth <a class="el" href="window_guide.html#GLFW_GREEN_BITS">hint</a>. </p>

</div>
</div>
<a id="gab292ea403db6d514537b515311bf9ae3" name="gab292ea403db6d514537b515311bf9ae3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab292ea403db6d514537b515311bf9ae3">&#9670;&#160;</a></span>GLFW_BLUE_BITS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_BLUE_BITS&#160;&#160;&#160;0x00021003</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Framebuffer bit depth <a class="el" href="window_guide.html#GLFW_BLUE_BITS">hint</a>. </p>

</div>
</div>
<a id="gafed79a3f468997877da86c449bd43e8c" name="gafed79a3f468997877da86c449bd43e8c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafed79a3f468997877da86c449bd43e8c">&#9670;&#160;</a></span>GLFW_ALPHA_BITS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ALPHA_BITS&#160;&#160;&#160;0x00021004</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Framebuffer bit depth <a class="el" href="window_guide.html#GLFW_ALPHA_BITS">hint</a>. </p>

</div>
</div>
<a id="ga318a55eac1fee57dfe593b6d38149d07" name="ga318a55eac1fee57dfe593b6d38149d07"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga318a55eac1fee57dfe593b6d38149d07">&#9670;&#160;</a></span>GLFW_DEPTH_BITS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_DEPTH_BITS&#160;&#160;&#160;0x00021005</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Framebuffer bit depth <a class="el" href="window_guide.html#GLFW_DEPTH_BITS">hint</a>. </p>

</div>
</div>
<a id="ga5339890a45a1fb38e93cb9fcc5fd069d" name="ga5339890a45a1fb38e93cb9fcc5fd069d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5339890a45a1fb38e93cb9fcc5fd069d">&#9670;&#160;</a></span>GLFW_STENCIL_BITS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_STENCIL_BITS&#160;&#160;&#160;0x00021006</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Framebuffer bit depth <a class="el" href="window_guide.html#GLFW_STENCIL_BITS">hint</a>. </p>

</div>
</div>
<a id="gaead34a9a683b2bc20eecf30ba738bfc6" name="gaead34a9a683b2bc20eecf30ba738bfc6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaead34a9a683b2bc20eecf30ba738bfc6">&#9670;&#160;</a></span>GLFW_ACCUM_RED_BITS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ACCUM_RED_BITS&#160;&#160;&#160;0x00021007</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Framebuffer bit depth <a class="el" href="window_guide.html#GLFW_ACCUM_RED_BITS">hint</a>. </p>

</div>
</div>
<a id="ga65713cee1326f8e9d806fdf93187b471" name="ga65713cee1326f8e9d806fdf93187b471"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga65713cee1326f8e9d806fdf93187b471">&#9670;&#160;</a></span>GLFW_ACCUM_GREEN_BITS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ACCUM_GREEN_BITS&#160;&#160;&#160;0x00021008</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Framebuffer bit depth <a class="el" href="window_guide.html#GLFW_ACCUM_GREEN_BITS">hint</a>. </p>

</div>
</div>
<a id="ga22bbe9104a8ce1f8b88fb4f186aa36ce" name="ga22bbe9104a8ce1f8b88fb4f186aa36ce"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga22bbe9104a8ce1f8b88fb4f186aa36ce">&#9670;&#160;</a></span>GLFW_ACCUM_BLUE_BITS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ACCUM_BLUE_BITS&#160;&#160;&#160;0x00021009</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Framebuffer bit depth <a class="el" href="window_guide.html#GLFW_ACCUM_BLUE_BITS">hint</a>. </p>

</div>
</div>
<a id="gae829b55591c18169a40ab4067a041b1f" name="gae829b55591c18169a40ab4067a041b1f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae829b55591c18169a40ab4067a041b1f">&#9670;&#160;</a></span>GLFW_ACCUM_ALPHA_BITS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ACCUM_ALPHA_BITS&#160;&#160;&#160;0x0002100A</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Framebuffer bit depth <a class="el" href="window_guide.html#GLFW_ACCUM_ALPHA_BITS">hint</a>. </p>

</div>
</div>
<a id="gab05108c5029443b371112b031d1fa174" name="gab05108c5029443b371112b031d1fa174"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab05108c5029443b371112b031d1fa174">&#9670;&#160;</a></span>GLFW_AUX_BUFFERS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_AUX_BUFFERS&#160;&#160;&#160;0x0002100B</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Framebuffer auxiliary buffer <a class="el" href="window_guide.html#GLFW_AUX_BUFFERS">hint</a>. </p>

</div>
</div>
<a id="ga83d991efca02537e2d69969135b77b03" name="ga83d991efca02537e2d69969135b77b03"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga83d991efca02537e2d69969135b77b03">&#9670;&#160;</a></span>GLFW_STEREO</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_STEREO&#160;&#160;&#160;0x0002100C</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>OpenGL stereoscopic rendering <a class="el" href="window_guide.html#GLFW_STEREO">hint</a>. </p>

</div>
</div>
<a id="ga2cdf86fdcb7722fb8829c4e201607535" name="ga2cdf86fdcb7722fb8829c4e201607535"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2cdf86fdcb7722fb8829c4e201607535">&#9670;&#160;</a></span>GLFW_SAMPLES</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_SAMPLES&#160;&#160;&#160;0x0002100D</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Framebuffer MSAA samples <a class="el" href="window_guide.html#GLFW_SAMPLES">hint</a>. </p>

</div>
</div>
<a id="ga444a8f00414a63220591f9fdb7b5642b" name="ga444a8f00414a63220591f9fdb7b5642b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga444a8f00414a63220591f9fdb7b5642b">&#9670;&#160;</a></span>GLFW_SRGB_CAPABLE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_SRGB_CAPABLE&#160;&#160;&#160;0x0002100E</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Framebuffer sRGB <a class="el" href="window_guide.html#GLFW_SRGB_CAPABLE">hint</a>. </p>

</div>
</div>
<a id="ga0f20825e6e47ee8ba389024519682212" name="ga0f20825e6e47ee8ba389024519682212"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga0f20825e6e47ee8ba389024519682212">&#9670;&#160;</a></span>GLFW_REFRESH_RATE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_REFRESH_RATE&#160;&#160;&#160;0x0002100F</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Monitor refresh rate <a class="el" href="window_guide.html#GLFW_REFRESH_RATE">hint</a>. </p>

</div>
</div>
<a id="ga714a5d569e8a274ea58fdfa020955339" name="ga714a5d569e8a274ea58fdfa020955339"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga714a5d569e8a274ea58fdfa020955339">&#9670;&#160;</a></span>GLFW_DOUBLEBUFFER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_DOUBLEBUFFER&#160;&#160;&#160;0x00021010</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Framebuffer double buffering <a class="el" href="window_guide.html#GLFW_DOUBLEBUFFER_hint">hint</a> and <a class="el" href="window_guide.html#GLFW_DOUBLEBUFFER_attrib">attribute</a>. </p>

</div>
</div>
<a id="ga649309cf72a3d3de5b1348ca7936c95b" name="ga649309cf72a3d3de5b1348ca7936c95b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga649309cf72a3d3de5b1348ca7936c95b">&#9670;&#160;</a></span>GLFW_CLIENT_API</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CLIENT_API&#160;&#160;&#160;0x00022001</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Context client API <a class="el" href="window_guide.html#GLFW_CLIENT_API_hint">hint</a> and <a class="el" href="window_guide.html#GLFW_CLIENT_API_attrib">attribute</a>. </p>

</div>
</div>
<a id="gafe5e4922de1f9932d7e9849bb053b0c0" name="gafe5e4922de1f9932d7e9849bb053b0c0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafe5e4922de1f9932d7e9849bb053b0c0">&#9670;&#160;</a></span>GLFW_CONTEXT_VERSION_MAJOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CONTEXT_VERSION_MAJOR&#160;&#160;&#160;0x00022002</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Context client API major version <a class="el" href="window_guide.html#GLFW_CONTEXT_VERSION_MAJOR_hint">hint</a> and <a class="el" href="window_guide.html#GLFW_CONTEXT_VERSION_MAJOR_attrib">attribute</a>. </p>

</div>
</div>
<a id="ga31aca791e4b538c4e4a771eb95cc2d07" name="ga31aca791e4b538c4e4a771eb95cc2d07"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga31aca791e4b538c4e4a771eb95cc2d07">&#9670;&#160;</a></span>GLFW_CONTEXT_VERSION_MINOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CONTEXT_VERSION_MINOR&#160;&#160;&#160;0x00022003</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Context client API minor version <a class="el" href="window_guide.html#GLFW_CONTEXT_VERSION_MINOR_hint">hint</a> and <a class="el" href="window_guide.html#GLFW_CONTEXT_VERSION_MINOR_attrib">attribute</a>. </p>

</div>
</div>
<a id="gafb9475071aa77c6fb05ca5a5c8678a08" name="gafb9475071aa77c6fb05ca5a5c8678a08"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafb9475071aa77c6fb05ca5a5c8678a08">&#9670;&#160;</a></span>GLFW_CONTEXT_REVISION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CONTEXT_REVISION&#160;&#160;&#160;0x00022004</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Context client API revision number <a class="el" href="window_guide.html#GLFW_CONTEXT_REVISION_attrib">attribute</a>. </p>

</div>
</div>
<a id="gade3593916b4c507900aa2d6844810e00" name="gade3593916b4c507900aa2d6844810e00"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gade3593916b4c507900aa2d6844810e00">&#9670;&#160;</a></span>GLFW_CONTEXT_ROBUSTNESS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CONTEXT_ROBUSTNESS&#160;&#160;&#160;0x00022005</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Context client API revision number <a class="el" href="window_guide.html#GLFW_CONTEXT_ROBUSTNESS_hint">hint</a> and <a class="el" href="window_guide.html#GLFW_CONTEXT_ROBUSTNESS_attrib">attribute</a>. </p>

</div>
</div>
<a id="ga13d24b12465da8b28985f46c8557925b" name="ga13d24b12465da8b28985f46c8557925b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga13d24b12465da8b28985f46c8557925b">&#9670;&#160;</a></span>GLFW_OPENGL_FORWARD_COMPAT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_OPENGL_FORWARD_COMPAT&#160;&#160;&#160;0x00022006</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>OpenGL forward-compatibility <a class="el" href="window_guide.html#GLFW_OPENGL_FORWARD_COMPAT_hint">hint</a> and <a class="el" href="window_guide.html#GLFW_OPENGL_FORWARD_COMPAT_attrib">attribute</a>. </p>

</div>
</div>
<a id="ga8d55e3afec73c7de0509c3b7ad1d9e3f" name="ga8d55e3afec73c7de0509c3b7ad1d9e3f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8d55e3afec73c7de0509c3b7ad1d9e3f">&#9670;&#160;</a></span>GLFW_CONTEXT_DEBUG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CONTEXT_DEBUG&#160;&#160;&#160;0x00022007</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Debug mode context <a class="el" href="window_guide.html#GLFW_CONTEXT_DEBUG_hint">hint</a> and <a class="el" href="window_guide.html#GLFW_CONTEXT_DEBUG_attrib">attribute</a>. </p>

</div>
</div>
<a id="ga87ec2df0b915201e950ca42d5d0831e1" name="ga87ec2df0b915201e950ca42d5d0831e1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga87ec2df0b915201e950ca42d5d0831e1">&#9670;&#160;</a></span>GLFW_OPENGL_DEBUG_CONTEXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_OPENGL_DEBUG_CONTEXT&#160;&#160;&#160;<a class="el" href="group__window.html#ga8d55e3afec73c7de0509c3b7ad1d9e3f">GLFW_CONTEXT_DEBUG</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is an alias for compatibility with earlier versions. </p>

</div>
</div>
<a id="ga44f3a6b4261fbe351e0b950b0f372e12" name="ga44f3a6b4261fbe351e0b950b0f372e12"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga44f3a6b4261fbe351e0b950b0f372e12">&#9670;&#160;</a></span>GLFW_OPENGL_PROFILE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_OPENGL_PROFILE&#160;&#160;&#160;0x00022008</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>OpenGL profile <a class="el" href="window_guide.html#GLFW_OPENGL_PROFILE_hint">hint</a> and <a class="el" href="window_guide.html#GLFW_OPENGL_PROFILE_attrib">attribute</a>. </p>

</div>
</div>
<a id="ga72b648a8378fe3310c7c7bbecc0f7be6" name="ga72b648a8378fe3310c7c7bbecc0f7be6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga72b648a8378fe3310c7c7bbecc0f7be6">&#9670;&#160;</a></span>GLFW_CONTEXT_RELEASE_BEHAVIOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CONTEXT_RELEASE_BEHAVIOR&#160;&#160;&#160;0x00022009</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Context flush-on-release <a class="el" href="window_guide.html#GLFW_CONTEXT_RELEASE_BEHAVIOR_hint">hint</a> and <a class="el" href="window_guide.html#GLFW_CONTEXT_RELEASE_BEHAVIOR_attrib">attribute</a>. </p>

</div>
</div>
<a id="ga5a52fdfd46d8249c211f923675728082" name="ga5a52fdfd46d8249c211f923675728082"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5a52fdfd46d8249c211f923675728082">&#9670;&#160;</a></span>GLFW_CONTEXT_NO_ERROR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CONTEXT_NO_ERROR&#160;&#160;&#160;0x0002200A</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Context error suppression <a class="el" href="window_guide.html#GLFW_CONTEXT_NO_ERROR_hint">hint</a> and <a class="el" href="window_guide.html#GLFW_CONTEXT_NO_ERROR_attrib">attribute</a>. </p>

</div>
</div>
<a id="ga5154cebfcd831c1cc63a4d5ac9bb4486" name="ga5154cebfcd831c1cc63a4d5ac9bb4486"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5154cebfcd831c1cc63a4d5ac9bb4486">&#9670;&#160;</a></span>GLFW_CONTEXT_CREATION_API</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CONTEXT_CREATION_API&#160;&#160;&#160;0x0002200B</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Context creation API <a class="el" href="window_guide.html#GLFW_CONTEXT_CREATION_API_hint">hint</a> and <a class="el" href="window_guide.html#GLFW_CONTEXT_CREATION_API_attrib">attribute</a>. </p>

</div>
</div>
<a id="ga620bc4280c7eab81ac9f02204500ed47" name="ga620bc4280c7eab81ac9f02204500ed47"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga620bc4280c7eab81ac9f02204500ed47">&#9670;&#160;</a></span>GLFW_SCALE_TO_MONITOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_SCALE_TO_MONITOR&#160;&#160;&#160;0x0002200C</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaa5a9c6b4722670fd33d6e8a88f2e21bc" name="gaa5a9c6b4722670fd33d6e8a88f2e21bc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa5a9c6b4722670fd33d6e8a88f2e21bc">&#9670;&#160;</a></span>GLFW_SCALE_FRAMEBUFFER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_SCALE_FRAMEBUFFER&#160;&#160;&#160;0x0002200D</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gab6ef2d02eb55800d249ccf1af253c35e" name="gab6ef2d02eb55800d249ccf1af253c35e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab6ef2d02eb55800d249ccf1af253c35e">&#9670;&#160;</a></span>GLFW_COCOA_RETINA_FRAMEBUFFER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_COCOA_RETINA_FRAMEBUFFER&#160;&#160;&#160;0x00023001</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is an alias for the <a class="el" href="window_guide.html#GLFW_SCALE_FRAMEBUFFER_hint">GLFW_SCALE_FRAMEBUFFER</a> window hint for compatibility with earlier versions. </p>

</div>
</div>
<a id="ga70fa0fbc745de6aa824df79a580e84b5" name="ga70fa0fbc745de6aa824df79a580e84b5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga70fa0fbc745de6aa824df79a580e84b5">&#9670;&#160;</a></span>GLFW_COCOA_FRAME_NAME</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_COCOA_FRAME_NAME&#160;&#160;&#160;0x00023002</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga53c84ed2ddd94e15bbd44b1f6f7feafc" name="ga53c84ed2ddd94e15bbd44b1f6f7feafc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga53c84ed2ddd94e15bbd44b1f6f7feafc">&#9670;&#160;</a></span>GLFW_COCOA_GRAPHICS_SWITCHING</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_COCOA_GRAPHICS_SWITCHING&#160;&#160;&#160;0x00023003</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gae5a9ea2fccccd92edbd343fc56461114" name="gae5a9ea2fccccd92edbd343fc56461114"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae5a9ea2fccccd92edbd343fc56461114">&#9670;&#160;</a></span>GLFW_X11_CLASS_NAME</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_X11_CLASS_NAME&#160;&#160;&#160;0x00024001</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga494c3c0d911e4b860b946530a3e389e8" name="ga494c3c0d911e4b860b946530a3e389e8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga494c3c0d911e4b860b946530a3e389e8">&#9670;&#160;</a></span>GLFW_X11_INSTANCE_NAME</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_X11_INSTANCE_NAME&#160;&#160;&#160;0x00024002</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaf65ea8dafdc0edb07b821b9a336d5043" name="gaf65ea8dafdc0edb07b821b9a336d5043"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf65ea8dafdc0edb07b821b9a336d5043">&#9670;&#160;</a></span>GLFW_WIN32_KEYBOARD_MENU</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_WIN32_KEYBOARD_MENU&#160;&#160;&#160;0x00025001</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gace10f3846571de62243b46f75d978487" name="gace10f3846571de62243b46f75d978487"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gace10f3846571de62243b46f75d978487">&#9670;&#160;</a></span>GLFW_WIN32_SHOWDEFAULT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_WIN32_SHOWDEFAULT&#160;&#160;&#160;0x00025002</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gafbf1ce7a4362c75e602a4df9e1bdecd3" name="gafbf1ce7a4362c75e602a4df9e1bdecd3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafbf1ce7a4362c75e602a4df9e1bdecd3">&#9670;&#160;</a></span>GLFW_WAYLAND_APP_ID</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_WAYLAND_APP_ID&#160;&#160;&#160;0x00026001</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Allows specification of the Wayland app_id. </p>

</div>
</div>
<h2 class="groupheader">Typedef Documentation</h2>
<a id="ga3c96d80d363e67d13a41b5d1821f3242" name="ga3c96d80d363e67d13a41b5d1821f3242"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3c96d80d363e67d13a41b5d1821f3242">&#9670;&#160;</a></span>GLFWwindow</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> <a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Opaque window object.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_object">Window objects</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gabe287973a21a8f927cde4db06b8dcbe9" name="gabe287973a21a8f927cde4db06b8dcbe9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gabe287973a21a8f927cde4db06b8dcbe9">&#9670;&#160;</a></span>GLFWwindowposfun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWwindowposfun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int xpos, int ypos)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for window position callbacks. A window position callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> callback_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> xpos, <span class="keywordtype">int</span> ypos)</div>
<div class="ttc" id="agroup__window_html_ga3c96d80d363e67d13a41b5d1821f3242"><div class="ttname"><a href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a></div><div class="ttdeci">struct GLFWwindow GLFWwindow</div><div class="ttdoc">Opaque window object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1403</div></div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window that was moved. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">xpos</td><td>The new x-coordinate, in screen coordinates, of the upper-left corner of the content area of the window. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ypos</td><td>The new y-coordinate, in screen coordinates, of the upper-left corner of the content area of the window.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_pos">Window position</a> </dd>
<dd>
<a class="el" href="group__window.html#ga08bdfbba88934f9c4f92fd757979ac74">glfwSetWindowPosCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gaec0282944bb810f6f3163ec02da90350" name="gaec0282944bb810f6f3163ec02da90350"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaec0282944bb810f6f3163ec02da90350">&#9670;&#160;</a></span>GLFWwindowsizefun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWwindowsizefun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int width, int height)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for window size callbacks. A window size callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> callback_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> width, <span class="keywordtype">int</span> height)</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window that was resized. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">width</td><td>The new width, in screen coordinates, of the window. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">height</td><td>The new height, in screen coordinates, of the window.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_size">Window size</a> </dd>
<dd>
<a class="el" href="group__window.html#gad91b8b047a0c4c6033c38853864c34f8">glfwSetWindowSizeCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. <b>GLFW 3:</b> Added window handle parameter. </dd></dl>

</div>
</div>
<a id="gabf859b936d80961b7d39013a9694cc3e" name="gabf859b936d80961b7d39013a9694cc3e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gabf859b936d80961b7d39013a9694cc3e">&#9670;&#160;</a></span>GLFWwindowclosefun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWwindowclosefun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for window close callbacks. A window close callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window)</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window that the user attempted to close.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_close">Window closing and close flag</a> </dd>
<dd>
<a class="el" href="group__window.html#gada646d775a7776a95ac000cfc1885331">glfwSetWindowCloseCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 2.5. <b>GLFW 3:</b> Added window handle parameter. </dd></dl>

</div>
</div>
<a id="ga431663a1427d2eb3a273bc398b6737b5" name="ga431663a1427d2eb3a273bc398b6737b5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga431663a1427d2eb3a273bc398b6737b5">&#9670;&#160;</a></span>GLFWwindowrefreshfun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWwindowrefreshfun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for window content refresh callbacks. A window content refresh callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose content needs to be refreshed.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_refresh">Window damage and refresh</a> </dd>
<dd>
<a class="el" href="group__window.html#ga1c5c7eb889c33c7f4d10dd35b327654e">glfwSetWindowRefreshCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 2.5. <b>GLFW 3:</b> Added window handle parameter. </dd></dl>

</div>
</div>
<a id="gabc58c47e9d93f6eb1862d615c3680f46" name="gabc58c47e9d93f6eb1862d615c3680f46"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gabc58c47e9d93f6eb1862d615c3680f46">&#9670;&#160;</a></span>GLFWwindowfocusfun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWwindowfocusfun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int focused)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for window focus callbacks. A window focus callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> focused)</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window that gained or lost input focus. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">focused</td><td><code>GLFW_TRUE</code> if the window was given input focus, or <code>GLFW_FALSE</code> if it lost it.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_focus">Window input focus</a> </dd>
<dd>
<a class="el" href="group__window.html#gac2d83c4a10f071baf841f6730528e66c">glfwSetWindowFocusCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga35c658cccba236f26e7adee0e25f6a4f" name="ga35c658cccba236f26e7adee0e25f6a4f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga35c658cccba236f26e7adee0e25f6a4f">&#9670;&#160;</a></span>GLFWwindowiconifyfun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWwindowiconifyfun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int iconified)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for window iconify callbacks. A window iconify callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> iconified)</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window that was iconified or restored. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">iconified</td><td><code>GLFW_TRUE</code> if the window was iconified, or <code>GLFW_FALSE</code> if it was restored.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_iconify">Window iconification</a> </dd>
<dd>
<a class="el" href="group__window.html#gac793e9efd255567b5fb8b445052cfd3e">glfwSetWindowIconifyCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga3017196fdaec33ac3e095765176c2a90" name="ga3017196fdaec33ac3e095765176c2a90"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3017196fdaec33ac3e095765176c2a90">&#9670;&#160;</a></span>GLFWwindowmaximizefun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWwindowmaximizefun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int maximized)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for window maximize callbacks. A window maximize callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> maximized)</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window that was maximized or restored. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">maximized</td><td><code>GLFW_TRUE</code> if the window was maximized, or <code>GLFW_FALSE</code> if it was restored.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_maximize">Window maximization</a> </dd>
<dd>
<a class="el" href="group__window.html#gacbe64c339fbd94885e62145563b6dc93" title="Sets the maximize callback for the specified window.">glfwSetWindowMaximizeCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="gae18026e294dde685ed2e5f759533144d" name="gae18026e294dde685ed2e5f759533144d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae18026e294dde685ed2e5f759533144d">&#9670;&#160;</a></span>GLFWframebuffersizefun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWframebuffersizefun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int width, int height)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for framebuffer size callbacks. A framebuffer size callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> width, <span class="keywordtype">int</span> height)</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose framebuffer was resized. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">width</td><td>The new width, in pixels, of the framebuffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">height</td><td>The new height, in pixels, of the framebuffer.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_fbsize">Framebuffer size</a> </dd>
<dd>
<a class="el" href="group__window.html#gab3fb7c3366577daef18c0023e2a8591f">glfwSetFramebufferSizeCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga77f288a2d04bb3c77c7d9615d08cf70e" name="ga77f288a2d04bb3c77c7d9615d08cf70e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga77f288a2d04bb3c77c7d9615d08cf70e">&#9670;&#160;</a></span>GLFWwindowcontentscalefun</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWwindowcontentscalefun) (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, float xscale, float yscale)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is the function pointer type for window content scale callbacks. A window content scale callback function has the following signature: </p><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">float</span> xscale, <span class="keywordtype">float</span> yscale)</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose content scale changed. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">xscale</td><td>The new x-axis content scale of the window. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">yscale</td><td>The new y-axis content scale of the window.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_scale">Window content scale</a> </dd>
<dd>
<a class="el" href="group__window.html#gaf2832ebb5aa6c252a2d261de002c92d6">glfwSetWindowContentScaleCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga7cc0a09de172fa7250872046f8c4d2ca" name="ga7cc0a09de172fa7250872046f8c4d2ca"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7cc0a09de172fa7250872046f8c4d2ca">&#9670;&#160;</a></span>GLFWimage</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="struct_g_l_f_wimage.html">GLFWimage</a> <a class="el" href="struct_g_l_f_wimage.html">GLFWimage</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This describes a single 2D image. See the documentation for each related function what the expected pixel format is.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#cursor_custom">Custom cursor creation</a> </dd>
<dd>
<a class="el" href="window_guide.html#window_icon">Window icon</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 2.1. <b>GLFW 3:</b> Removed format and bytes-per-pixel members. </dd></dl>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="gaa77c4898dfb83344a6b4f76aa16b9a4a" name="gaa77c4898dfb83344a6b4f76aa16b9a4a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa77c4898dfb83344a6b4f76aa16b9a4a">&#9670;&#160;</a></span>glfwDefaultWindowHints()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwDefaultWindowHints </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function resets all window hints to their <a class="el" href="window_guide.html#window_hints_values">default values</a>.</p>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_hints">Window creation hints</a> </dd>
<dd>
<a class="el" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a> </dd>
<dd>
<a class="el" href="group__window.html#ga8cb2782861c9d997bcf2dea97f363e5f">glfwWindowHintString</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga7d9c8c62384b1e2821c4dc48952d2033" name="ga7d9c8c62384b1e2821c4dc48952d2033"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7d9c8c62384b1e2821c4dc48952d2033">&#9670;&#160;</a></span>glfwWindowHint()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwWindowHint </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>hint</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets hints for the next call to <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>. The hints, once set, retain their values until changed by a call to this function or <a class="el" href="group__window.html#gaa77c4898dfb83344a6b4f76aa16b9a4a">glfwDefaultWindowHints</a>, or until the library is terminated.</p>
<p>Only integer value hints can be set with this function. String value hints are set with <a class="el" href="group__window.html#ga8cb2782861c9d997bcf2dea97f363e5f">glfwWindowHintString</a>.</p>
<p>This function does not check whether the specified hint values are valid. If you set hints to invalid values this will instead be reported by the next call to <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>.</p>
<p>Some hints are platform specific. These may be set on any platform but they will only affect their specific platform. Other platforms will ignore them. Setting these hints requires no platform specific headers or functions.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">hint</td><td>The <a class="el" href="window_guide.html#window_hints">window hint</a> to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>The new value of the window hint.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_hints">Window creation hints</a> </dd>
<dd>
<a class="el" href="group__window.html#ga8cb2782861c9d997bcf2dea97f363e5f">glfwWindowHintString</a> </dd>
<dd>
<a class="el" href="group__window.html#gaa77c4898dfb83344a6b4f76aa16b9a4a">glfwDefaultWindowHints</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. Replaces <code>glfwOpenWindowHint</code>. </dd></dl>

</div>
</div>
<a id="ga8cb2782861c9d997bcf2dea97f363e5f" name="ga8cb2782861c9d997bcf2dea97f363e5f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8cb2782861c9d997bcf2dea97f363e5f">&#9670;&#160;</a></span>glfwWindowHintString()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwWindowHintString </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>hint</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets hints for the next call to <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>. The hints, once set, retain their values until changed by a call to this function or <a class="el" href="group__window.html#gaa77c4898dfb83344a6b4f76aa16b9a4a">glfwDefaultWindowHints</a>, or until the library is terminated.</p>
<p>Only string type hints can be set with this function. Integer value hints are set with <a class="el" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>.</p>
<p>This function does not check whether the specified hint values are valid. If you set hints to invalid values this will instead be reported by the next call to <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>.</p>
<p>Some hints are platform specific. These may be set on any platform but they will only affect their specific platform. Other platforms will ignore them. Setting these hints requires no platform specific headers or functions.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">hint</td><td>The <a class="el" href="window_guide.html#window_hints">window hint</a> to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>The new value of the window hint.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The specified string is copied before this function returns.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_hints">Window creation hints</a> </dd>
<dd>
<a class="el" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a> </dd>
<dd>
<a class="el" href="group__window.html#gaa77c4898dfb83344a6b4f76aa16b9a4a">glfwDefaultWindowHints</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga3555a418df92ad53f917597fe2f64aeb" name="ga3555a418df92ad53f917597fe2f64aeb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3555a418df92ad53f917597fe2f64aeb">&#9670;&#160;</a></span>glfwCreateWindow()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> * glfwCreateWindow </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>height</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>title</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>share</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function creates a window and its associated OpenGL or OpenGL ES context. Most of the options controlling how the window and its context should be created are specified with <a class="el" href="window_guide.html#window_hints">window hints</a>.</p>
<p>Successful creation does not change which context is current. Before you can use the newly created context, you need to <a class="el" href="context_guide.html#context_current">make it current</a>. For information about the <code>share</code> parameter, see <a class="el" href="context_guide.html#context_sharing">Context object sharing</a>.</p>
<p>The created window, framebuffer and context may differ from what you requested, as not all parameters and hints are <a class="el" href="window_guide.html#window_hints_hard">hard constraints</a>. This includes the size of the window, especially for full screen windows. To query the actual attributes of the created window, framebuffer and context, see <a class="el" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a>, <a class="el" href="group__window.html#gaeea7cbc03373a41fb51cfbf9f2a5d4c6">glfwGetWindowSize</a> and <a class="el" href="group__window.html#ga0e2637a4161afb283f5300c7f94785c9">glfwGetFramebufferSize</a>.</p>
<p>To create a full screen window, you need to specify the monitor the window will cover. If no monitor is specified, the window will be windowed mode. Unless you have a way for the user to choose a specific monitor, it is recommended that you pick the primary monitor. For more information on how to query connected monitors, see <a class="el" href="monitor_guide.html#monitor_monitors">Retrieving monitors</a>.</p>
<p>For full screen windows, the specified size becomes the resolution of the window's <em>desired video mode</em>. As long as a full screen window is not iconified, the supported video mode most closely matching the desired video mode is set for the specified monitor. For more information about full screen windows, including the creation of so called <em>windowed full screen</em> or <em>borderless full screen</em> windows, see <a class="el" href="window_guide.html#window_windowed_full_screen">"Windowed full screen" windows</a>.</p>
<p>Once you have created the window, you can switch it between windowed and full screen mode with <a class="el" href="group__window.html#ga81c76c418af80a1cce7055bccb0ae0a7">glfwSetWindowMonitor</a>. This will not affect its OpenGL or OpenGL ES context.</p>
<p>By default, newly created windows use the placement recommended by the window system. To create the window at a specific position, set the <a class="el" href="window_guide.html#GLFW_POSITION_X">GLFW_POSITION_X</a> and <a class="el" href="window_guide.html#GLFW_POSITION_Y">GLFW_POSITION_Y</a> window hints before creation. To restore the default behavior, set either or both hints back to <code>GLFW_ANY_POSITION</code>.</p>
<p>As long as at least one full screen window is not iconified, the screensaver is prohibited from starting.</p>
<p>Window systems put limits on window sizes. Very large or very small window dimensions may be overridden by the window system on creation. Check the actual <a class="el" href="window_guide.html#window_size">size</a> after creation.</p>
<p>The <a class="el" href="window_guide.html#buffer_swap">swap interval</a> is not set during window creation and the initial value may vary depending on driver settings and defaults.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">width</td><td>The desired width, in screen coordinates, of the window. This must be greater than zero. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">height</td><td>The desired height, in screen coordinates, of the window. This must be greater than zero. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">title</td><td>The initial, UTF-8 encoded window title. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">monitor</td><td>The monitor to use for full screen mode, or <code>NULL</code> for windowed mode. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">share</td><td>The window whose context to share resources with, or <code>NULL</code> to not share resources. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The handle of the created window, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a>, <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a>, <a class="el" href="group__errors.html#ga56882b290db23261cc6c053c40c2d08e">GLFW_API_UNAVAILABLE</a>, <a class="el" href="group__errors.html#gad16c5565b4a69f9c2a9ac2c0dbc89462">GLFW_VERSION_UNAVAILABLE</a>, <a class="el" href="group__errors.html#ga196e125ef261d94184e2b55c05762f14">GLFW_FORMAT_UNAVAILABLE</a>, <a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>Windows:</b> Window creation will fail if the Microsoft GDI software OpenGL implementation is the only one available.</dd>
<dd>
<b>Windows:</b> If the executable has an icon resource named <code>GLFW_ICON,</code> it will be set as the initial icon for the window. If no such icon is present, the <code>IDI_APPLICATION</code> icon will be used instead. To set a different icon, see <a class="el" href="group__window.html#gadd7ccd39fe7a7d1f0904666ae5932dc5">glfwSetWindowIcon</a>.</dd>
<dd>
<b>Windows:</b> The context to share resources with must not be current on any other thread.</dd>
<dd>
<b>macOS:</b> The OS only supports core profile contexts for OpenGL versions 3.2 and later. Before creating an OpenGL context of version 3.2 or later you must set the <a class="el" href="window_guide.html#GLFW_OPENGL_PROFILE_hint">GLFW_OPENGL_PROFILE</a> hint accordingly. OpenGL 3.0 and 3.1 contexts are not supported at all on macOS.</dd>
<dd>
<b>macOS:</b> The GLFW window has no icon, as it is not a document window, but the dock icon will be the same as the application bundle's icon. For more information on bundles, see the <a href="https://developer.apple.com/library/mac/documentation/CoreFoundation/Conceptual/CFBundles/">Bundle Programming Guide</a> in the Mac Developer Library.</dd>
<dd>
<b>macOS:</b> On OS X 10.10 and later the window frame will not be rendered at full resolution on Retina displays unless the <a class="el" href="window_guide.html#GLFW_SCALE_FRAMEBUFFER_hint">GLFW_SCALE_FRAMEBUFFER</a> hint is <code>GLFW_TRUE</code> and the <code>NSHighResolutionCapable</code> key is enabled in the application bundle's <code>Info.plist</code>. For more information, see <a href="https://developer.apple.com/library/mac/documentation/GraphicsAnimation/Conceptual/HighResolutionOSX/Explained/Explained.html">High Resolution Guidelines for OS X</a> in the Mac Developer Library. The GLFW test and example programs use a custom <code>Info.plist</code> template for this, which can be found as <code>CMake/Info.plist.in</code> in the source tree.</dd>
<dd>
<b>macOS:</b> When activating frame autosaving with <a class="el" href="window_guide.html#GLFW_COCOA_FRAME_NAME_hint">GLFW_COCOA_FRAME_NAME</a>, the specified window size and position may be overridden by previously saved values.</dd>
<dd>
<b>Wayland:</b> GLFW uses <a href="https://gitlab.freedesktop.org/libdecor/libdecor">libdecor</a> where available to create its window decorations. This in turn uses server-side XDG decorations where available and provides high quality client-side decorations on compositors like GNOME. If both XDG decorations and libdecor are unavailable, GLFW falls back to a very simple set of window decorations that only support moving, resizing and the window manager's right-click menu.</dd>
<dd>
<b>X11:</b> Some window managers will not respect the placement of initially hidden windows.</dd>
<dd>
<b>X11:</b> Due to the asynchronous nature of X11, it may take a moment for a window to reach its requested state. This means you may not be able to query the final size, position or other attributes directly after window creation.</dd>
<dd>
<b>X11:</b> The class part of the <code>WM_CLASS</code> window property will by default be set to the window title passed to this function. The instance part will use the contents of the <code>RESOURCE_NAME</code> environment variable, if present and not empty, or fall back to the window title. Set the <a class="el" href="window_guide.html#GLFW_X11_CLASS_NAME_hint">GLFW_X11_CLASS_NAME</a> and <a class="el" href="window_guide.html#GLFW_X11_INSTANCE_NAME_hint">GLFW_X11_INSTANCE_NAME</a> window hints to override this.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_creation">Window creation</a> </dd>
<dd>
<a class="el" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. Replaces <code>glfwOpenWindow</code>. </dd></dl>

</div>
</div>
<a id="gacdf43e51376051d2c091662e9fe3d7b2" name="gacdf43e51376051d2c091662e9fe3d7b2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gacdf43e51376051d2c091662e9fe3d7b2">&#9670;&#160;</a></span>glfwDestroyWindow()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwDestroyWindow </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function destroys the specified window and its context. On calling this function, no further callbacks will be called for that window.</p>
<p>If the context of the specified window is current on the main thread, it is detached before being destroyed.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to destroy.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section note"><dt>Note</dt><dd>The context of the specified window must not be current on any other thread when this function is called.</dd></dl>
<dl class="section user"><dt>Reentrancy</dt><dd>This function must not be called from a callback.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_creation">Window creation</a> </dd>
<dd>
<a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. Replaces <code>glfwCloseWindow</code>. </dd></dl>

</div>
</div>
<a id="ga24e02fbfefbb81fc45320989f8140ab5" name="ga24e02fbfefbb81fc45320989f8140ab5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga24e02fbfefbb81fc45320989f8140ab5">&#9670;&#160;</a></span>glfwWindowShouldClose()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwWindowShouldClose </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the value of the close flag of the specified window.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to query. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The value of the close flag.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_close">Window closing and close flag</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga49c449dde2a6f87d996f4daaa09d6708" name="ga49c449dde2a6f87d996f4daaa09d6708"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga49c449dde2a6f87d996f4daaa09d6708">&#9670;&#160;</a></span>glfwSetWindowShouldClose()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetWindowShouldClose </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the value of the close flag of the specified window. This can be used to override the user's attempt to close the window, or to signal that it should be closed.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose flag to change. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>The new value.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_close">Window closing and close flag</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gac6151765c54b789c4fe66c6bc6215953" name="gac6151765c54b789c4fe66c6bc6215953"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac6151765c54b789c4fe66c6bc6215953">&#9670;&#160;</a></span>glfwGetWindowTitle()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char * glfwGetWindowTitle </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the window title, encoded as UTF-8, of the specified window. This is the title set previously by <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a> or <a class="el" href="group__window.html#ga5d877f09e968cef7a360b513306f17ff">glfwSetWindowTitle</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to query. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The UTF-8 encoded window title, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>The returned title is currently a copy of the title last set by <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a> or <a class="el" href="group__window.html#ga5d877f09e968cef7a360b513306f17ff">glfwSetWindowTitle</a>. It does not include any additional text which may be appended by the platform or another program.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned string is allocated and freed by GLFW. You should not free it yourself. It is valid until the next call to <a class="el" href="group__window.html#gac6151765c54b789c4fe66c6bc6215953">glfwGetWindowTitle</a> or <a class="el" href="group__window.html#ga5d877f09e968cef7a360b513306f17ff">glfwSetWindowTitle</a>, or until the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_title">Window title</a> </dd>
<dd>
<a class="el" href="group__window.html#ga5d877f09e968cef7a360b513306f17ff">glfwSetWindowTitle</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.4. </dd></dl>

</div>
</div>
<a id="ga5d877f09e968cef7a360b513306f17ff" name="ga5d877f09e968cef7a360b513306f17ff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5d877f09e968cef7a360b513306f17ff">&#9670;&#160;</a></span>glfwSetWindowTitle()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetWindowTitle </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>title</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the window title, encoded as UTF-8, of the specified window.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose title to change. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">title</td><td>The UTF-8 encoded window title.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>macOS:</b> The window title will not be updated until the next time you process events.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_title">Window title</a> </dd>
<dd>
<a class="el" href="group__window.html#gac6151765c54b789c4fe66c6bc6215953">glfwGetWindowTitle</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. <b>GLFW 3:</b> Added window handle parameter. </dd></dl>

</div>
</div>
<a id="gadd7ccd39fe7a7d1f0904666ae5932dc5" name="gadd7ccd39fe7a7d1f0904666ae5932dc5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadd7ccd39fe7a7d1f0904666ae5932dc5">&#9670;&#160;</a></span>glfwSetWindowIcon()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetWindowIcon </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>count</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="struct_g_l_f_wimage.html">GLFWimage</a> *&#160;</td>
          <td class="paramname"><em>images</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the icon of the specified window. If passed an array of candidate images, those of or closest to the sizes desired by the system are selected. If no images are specified, the window reverts to its default icon.</p>
<p>The pixels are 32-bit, little-endian, non-premultiplied RGBA, i.e. eight bits per channel with the red channel first. They are arranged canonically as packed sequential rows, starting from the top-left corner.</p>
<p>The desired image sizes varies depending on platform and system settings. The selected images will be rescaled as needed. Good sizes include 16x16, 32x32 and 48x48.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose icon to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">count</td><td>The number of images in the specified array, or zero to revert to the default window icon. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">images</td><td>The images to create the icon from. This is ignored if count is zero.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a>, <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a> and <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a> (see remarks).</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The specified image data is copied before this function returns.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>macOS:</b> Regular windows do not have icons on macOS. This function will emit <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a>. The dock icon will be the same as the application bundle's icon. For more information on bundles, see the <a href="https://developer.apple.com/library/mac/documentation/CoreFoundation/Conceptual/CFBundles/">Bundle Programming Guide</a> in the Mac Developer Library.</dd>
<dd>
<b>Wayland:</b> There is no existing protocol to change an icon, the window will thus inherit the one defined in the application's desktop file. This function will emit <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_icon">Window icon</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="ga73cb526c000876fd8ddf571570fdb634" name="ga73cb526c000876fd8ddf571570fdb634"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga73cb526c000876fd8ddf571570fdb634">&#9670;&#160;</a></span>glfwGetWindowPos()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwGetWindowPos </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>xpos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>ypos</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function retrieves the position, in screen coordinates, of the upper-left corner of the content area of the specified window.</p>
<p>Any or all of the position arguments may be <code>NULL</code>. If an error occurs, all non-<code>NULL</code> position arguments will be set to zero.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to query. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">xpos</td><td>Where to store the x-coordinate of the upper-left corner of the content area, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">ypos</td><td>Where to store the y-coordinate of the upper-left corner of the content area, or <code>NULL</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a> and <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a> (see remarks).</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>Wayland:</b> There is no way for an application to retrieve the global position of its windows. This function will emit <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_pos">Window position</a> </dd>
<dd>
<a class="el" href="group__window.html#ga1abb6d690e8c88e0c8cd1751356dbca8">glfwSetWindowPos</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga1abb6d690e8c88e0c8cd1751356dbca8" name="ga1abb6d690e8c88e0c8cd1751356dbca8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1abb6d690e8c88e0c8cd1751356dbca8">&#9670;&#160;</a></span>glfwSetWindowPos()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetWindowPos </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>xpos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>ypos</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the position, in screen coordinates, of the upper-left corner of the content area of the specified windowed mode window. If the window is a full screen window, this function does nothing.</p>
<p><b>Do not use this function</b> to move an already visible window unless you have very good reasons for doing so, as it will confuse and annoy the user.</p>
<p>The window manager may put limits on what positions are allowed. GLFW cannot and should not override these limits.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to query. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">xpos</td><td>The x-coordinate of the upper-left corner of the content area. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ypos</td><td>The y-coordinate of the upper-left corner of the content area.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a> and <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a> (see remarks).</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>Wayland:</b> There is no way for an application to set the global position of its windows. This function will emit <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_pos">Window position</a> </dd>
<dd>
<a class="el" href="group__window.html#ga73cb526c000876fd8ddf571570fdb634">glfwGetWindowPos</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. <b>GLFW 3:</b> Added window handle parameter. </dd></dl>

</div>
</div>
<a id="gaeea7cbc03373a41fb51cfbf9f2a5d4c6" name="gaeea7cbc03373a41fb51cfbf9f2a5d4c6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaeea7cbc03373a41fb51cfbf9f2a5d4c6">&#9670;&#160;</a></span>glfwGetWindowSize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwGetWindowSize </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>height</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function retrieves the size, in screen coordinates, of the content area of the specified window. If you wish to retrieve the size of the framebuffer of the window in pixels, see <a class="el" href="group__window.html#ga0e2637a4161afb283f5300c7f94785c9">glfwGetFramebufferSize</a>.</p>
<p>Any or all of the size arguments may be <code>NULL</code>. If an error occurs, all non-<code>NULL</code> size arguments will be set to zero.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose size to retrieve. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">width</td><td>Where to store the width, in screen coordinates, of the content area, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">height</td><td>Where to store the height, in screen coordinates, of the content area, or <code>NULL</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_size">Window size</a> </dd>
<dd>
<a class="el" href="group__window.html#ga371911f12c74c504dd8d47d832d095cb">glfwSetWindowSize</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. <b>GLFW 3:</b> Added window handle parameter. </dd></dl>

</div>
</div>
<a id="gac314fa6cec7d2d307be9963e2709cc90" name="gac314fa6cec7d2d307be9963e2709cc90"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac314fa6cec7d2d307be9963e2709cc90">&#9670;&#160;</a></span>glfwSetWindowSizeLimits()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetWindowSizeLimits </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>minwidth</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>minheight</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>maxwidth</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>maxheight</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the size limits of the content area of the specified window. If the window is full screen, the size limits only take effect once it is made windowed. If the window is not resizable, this function does nothing.</p>
<p>The size limits are applied immediately to a windowed mode window and may cause it to be resized.</p>
<p>The maximum dimensions must be greater than or equal to the minimum dimensions and all must be greater than or equal to zero.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to set limits for. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">minwidth</td><td>The minimum width, in screen coordinates, of the content area, or <code>GLFW_DONT_CARE</code>. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">minheight</td><td>The minimum height, in screen coordinates, of the content area, or <code>GLFW_DONT_CARE</code>. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">maxwidth</td><td>The maximum width, in screen coordinates, of the content area, or <code>GLFW_DONT_CARE</code>. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">maxheight</td><td>The maximum height, in screen coordinates, of the content area, or <code>GLFW_DONT_CARE</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>If you set size limits and an aspect ratio that conflict, the results are undefined.</dd>
<dd>
<b>Wayland:</b> The size limits will not be applied until the window is actually resized, either by the user or by the compositor.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_sizelimits">Window size limits</a> </dd>
<dd>
<a class="el" href="group__window.html#ga72ac8cb1ee2e312a878b55153d81b937">glfwSetWindowAspectRatio</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="ga72ac8cb1ee2e312a878b55153d81b937" name="ga72ac8cb1ee2e312a878b55153d81b937"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga72ac8cb1ee2e312a878b55153d81b937">&#9670;&#160;</a></span>glfwSetWindowAspectRatio()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetWindowAspectRatio </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>numer</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>denom</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the required aspect ratio of the content area of the specified window. If the window is full screen, the aspect ratio only takes effect once it is made windowed. If the window is not resizable, this function does nothing.</p>
<p>The aspect ratio is specified as a numerator and a denominator and both values must be greater than zero. For example, the common 16:9 aspect ratio is specified as 16 and 9, respectively.</p>
<p>If the numerator and denominator is set to <code>GLFW_DONT_CARE</code> then the aspect ratio limit is disabled.</p>
<p>The aspect ratio is applied immediately to a windowed mode window and may cause it to be resized.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to set limits for. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numer</td><td>The numerator of the desired aspect ratio, or <code>GLFW_DONT_CARE</code>. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">denom</td><td>The denominator of the desired aspect ratio, or <code>GLFW_DONT_CARE</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>If you set size limits and an aspect ratio that conflict, the results are undefined.</dd>
<dd>
<b>Wayland:</b> The aspect ratio will not be applied until the window is actually resized, either by the user or by the compositor.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_sizelimits">Window size limits</a> </dd>
<dd>
<a class="el" href="group__window.html#gac314fa6cec7d2d307be9963e2709cc90">glfwSetWindowSizeLimits</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="ga371911f12c74c504dd8d47d832d095cb" name="ga371911f12c74c504dd8d47d832d095cb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga371911f12c74c504dd8d47d832d095cb">&#9670;&#160;</a></span>glfwSetWindowSize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetWindowSize </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>height</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the size, in screen coordinates, of the content area of the specified window.</p>
<p>For full screen windows, this function updates the resolution of its desired video mode and switches to the video mode closest to it, without affecting the window's context. As the context is unaffected, the bit depths of the framebuffer remain unchanged.</p>
<p>If you wish to update the refresh rate of the desired video mode in addition to its resolution, see <a class="el" href="group__window.html#ga81c76c418af80a1cce7055bccb0ae0a7">glfwSetWindowMonitor</a>.</p>
<p>The window manager may put limits on what sizes are allowed. GLFW cannot and should not override these limits.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to resize. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">width</td><td>The desired width, in screen coordinates, of the window content area. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">height</td><td>The desired height, in screen coordinates, of the window content area.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_size">Window size</a> </dd>
<dd>
<a class="el" href="group__window.html#gaeea7cbc03373a41fb51cfbf9f2a5d4c6">glfwGetWindowSize</a> </dd>
<dd>
<a class="el" href="group__window.html#ga81c76c418af80a1cce7055bccb0ae0a7">glfwSetWindowMonitor</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. <b>GLFW 3:</b> Added window handle parameter. </dd></dl>

</div>
</div>
<a id="ga0e2637a4161afb283f5300c7f94785c9" name="ga0e2637a4161afb283f5300c7f94785c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga0e2637a4161afb283f5300c7f94785c9">&#9670;&#160;</a></span>glfwGetFramebufferSize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwGetFramebufferSize </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>height</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function retrieves the size, in pixels, of the framebuffer of the specified window. If you wish to retrieve the size of the window in screen coordinates, see <a class="el" href="group__window.html#gaeea7cbc03373a41fb51cfbf9f2a5d4c6">glfwGetWindowSize</a>.</p>
<p>Any or all of the size arguments may be <code>NULL</code>. If an error occurs, all non-<code>NULL</code> size arguments will be set to zero.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose framebuffer to query. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">width</td><td>Where to store the width, in pixels, of the framebuffer, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">height</td><td>Where to store the height, in pixels, of the framebuffer, or <code>NULL</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_fbsize">Framebuffer size</a> </dd>
<dd>
<a class="el" href="group__window.html#gab3fb7c3366577daef18c0023e2a8591f">glfwSetFramebufferSizeCallback</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga1a9fd382058c53101b21cf211898f1f1" name="ga1a9fd382058c53101b21cf211898f1f1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1a9fd382058c53101b21cf211898f1f1">&#9670;&#160;</a></span>glfwGetWindowFrameSize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwGetWindowFrameSize </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>bottom</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function retrieves the size, in screen coordinates, of each edge of the frame of the specified window. This size includes the title bar, if the window has one. The size of the frame may vary depending on the <a class="el" href="window_guide.html#window_hints_wnd">window-related hints</a> used to create it.</p>
<p>Because this function retrieves the size of each window frame edge and not the offset along a particular coordinate axis, the retrieved values will always be zero or positive.</p>
<p>Any or all of the size arguments may be <code>NULL</code>. If an error occurs, all non-<code>NULL</code> size arguments will be set to zero.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose frame size to query. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">left</td><td>Where to store the size, in screen coordinates, of the left edge of the window frame, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">top</td><td>Where to store the size, in screen coordinates, of the top edge of the window frame, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">right</td><td>Where to store the size, in screen coordinates, of the right edge of the window frame, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">bottom</td><td>Where to store the size, in screen coordinates, of the bottom edge of the window frame, or <code>NULL</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_size">Window size</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.1. </dd></dl>

</div>
</div>
<a id="gaf5d31de9c19c4f994facea64d2b3106c" name="gaf5d31de9c19c4f994facea64d2b3106c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf5d31de9c19c4f994facea64d2b3106c">&#9670;&#160;</a></span>glfwGetWindowContentScale()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwGetWindowContentScale </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float *&#160;</td>
          <td class="paramname"><em>xscale</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float *&#160;</td>
          <td class="paramname"><em>yscale</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function retrieves the content scale for the specified window. The content scale is the ratio between the current DPI and the platform's default DPI. This is especially important for text and any UI elements. If the pixel dimensions of your UI scaled by this look appropriate on your machine then it should appear at a reasonable size on other machines regardless of their DPI and scaling settings. This relies on the system DPI and scaling settings being somewhat correct.</p>
<p>On platforms where each monitors can have its own content scale, the window content scale will depend on which monitor the system considers the window to be on.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to query. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">xscale</td><td>Where to store the x-axis content scale, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">yscale</td><td>Where to store the y-axis content scale, or <code>NULL</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_scale">Window content scale</a> </dd>
<dd>
<a class="el" href="group__window.html#gaf2832ebb5aa6c252a2d261de002c92d6">glfwSetWindowContentScaleCallback</a> </dd>
<dd>
<a class="el" href="group__monitor.html#gad3152e84465fa620b601265ebfcdb21b">glfwGetMonitorContentScale</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="gad09f0bd7a6307c4533b7061828480a84" name="gad09f0bd7a6307c4533b7061828480a84"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad09f0bd7a6307c4533b7061828480a84">&#9670;&#160;</a></span>glfwGetWindowOpacity()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float glfwGetWindowOpacity </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the opacity of the window, including any decorations.</p>
<p>The opacity (or alpha) value is a positive finite number between zero and one, where zero is fully transparent and one is fully opaque. If the system does not support whole window transparency, this function always returns one.</p>
<p>The initial opacity value for newly created windows is one.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to query. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The opacity value of the specified window.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_transparency">Window transparency</a> </dd>
<dd>
<a class="el" href="group__window.html#gac31caeb3d1088831b13d2c8a156802e9">glfwSetWindowOpacity</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="gac31caeb3d1088831b13d2c8a156802e9" name="gac31caeb3d1088831b13d2c8a156802e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac31caeb3d1088831b13d2c8a156802e9">&#9670;&#160;</a></span>glfwSetWindowOpacity()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetWindowOpacity </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>opacity</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the opacity of the window, including any decorations.</p>
<p>The opacity (or alpha) value is a positive finite number between zero and one, where zero is fully transparent and one is fully opaque.</p>
<p>The initial opacity value for newly created windows is one.</p>
<p>A window created with framebuffer transparency may not use whole window transparency. The results of doing this are undefined.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to set the opacity for. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">opacity</td><td>The desired opacity of the specified window.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a> and <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a> (see remarks).</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>Wayland:</b> There is no way to set an opacity factor for a window. This function will emit <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_transparency">Window transparency</a> </dd>
<dd>
<a class="el" href="group__window.html#gad09f0bd7a6307c4533b7061828480a84">glfwGetWindowOpacity</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga1bb559c0ebaad63c5c05ad2a066779c4" name="ga1bb559c0ebaad63c5c05ad2a066779c4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1bb559c0ebaad63c5c05ad2a066779c4">&#9670;&#160;</a></span>glfwIconifyWindow()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwIconifyWindow </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function iconifies (minimizes) the specified window if it was previously restored. If the window is already iconified, this function does nothing.</p>
<p>If the specified window is a full screen window, GLFW restores the original video mode of the monitor. The window's desired video mode is set again when the window is restored.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to iconify.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>Wayland:</b> Once a window is iconified, <a class="el" href="group__window.html#ga52527a5904b47d802b6b4bb519cdebc7">glfwRestoreWindow</a> won’t be able to restore it. This is a design decision of the xdg-shell protocol.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_iconify">Window iconification</a> </dd>
<dd>
<a class="el" href="group__window.html#ga52527a5904b47d802b6b4bb519cdebc7">glfwRestoreWindow</a> </dd>
<dd>
<a class="el" href="group__window.html#ga3f541387449d911274324ae7f17ec56b">glfwMaximizeWindow</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 2.1. <b>GLFW 3:</b> Added window handle parameter. </dd></dl>

</div>
</div>
<a id="ga52527a5904b47d802b6b4bb519cdebc7" name="ga52527a5904b47d802b6b4bb519cdebc7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga52527a5904b47d802b6b4bb519cdebc7">&#9670;&#160;</a></span>glfwRestoreWindow()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwRestoreWindow </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function restores the specified window if it was previously iconified (minimized) or maximized. If the window is already restored, this function does nothing.</p>
<p>If the specified window is an iconified full screen window, its desired video mode is set again for its monitor when the window is restored.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to restore.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_iconify">Window iconification</a> </dd>
<dd>
<a class="el" href="group__window.html#ga1bb559c0ebaad63c5c05ad2a066779c4">glfwIconifyWindow</a> </dd>
<dd>
<a class="el" href="group__window.html#ga3f541387449d911274324ae7f17ec56b">glfwMaximizeWindow</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 2.1. <b>GLFW 3:</b> Added window handle parameter. </dd></dl>

</div>
</div>
<a id="ga3f541387449d911274324ae7f17ec56b" name="ga3f541387449d911274324ae7f17ec56b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3f541387449d911274324ae7f17ec56b">&#9670;&#160;</a></span>glfwMaximizeWindow()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwMaximizeWindow </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function maximizes the specified window if it was previously not maximized. If the window is already maximized, this function does nothing.</p>
<p>If the specified window is a full screen window, this function does nothing.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to maximize.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread Safety</dt><dd>This function may only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_iconify">Window iconification</a> </dd>
<dd>
<a class="el" href="group__window.html#ga1bb559c0ebaad63c5c05ad2a066779c4">glfwIconifyWindow</a> </dd>
<dd>
<a class="el" href="group__window.html#ga52527a5904b47d802b6b4bb519cdebc7">glfwRestoreWindow</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in GLFW 3.2. </dd></dl>

</div>
</div>
<a id="ga61be47917b72536a148300f46494fc66" name="ga61be47917b72536a148300f46494fc66"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga61be47917b72536a148300f46494fc66">&#9670;&#160;</a></span>glfwShowWindow()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwShowWindow </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function makes the specified window visible if it was previously hidden. If the window is already visible or is in full screen mode, this function does nothing.</p>
<p>By default, windowed mode windows are focused when shown Set the <a class="el" href="window_guide.html#GLFW_FOCUS_ON_SHOW_hint">GLFW_FOCUS_ON_SHOW</a> window hint to change this behavior for all newly created windows, or change the behavior for an existing window with <a class="el" href="group__window.html#gace2afda29b4116ec012e410a6819033e">glfwSetWindowAttrib</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to make visible.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>Wayland:</b> Because Wayland wants every frame of the desktop to be complete, this function does not immediately make the window visible. Instead it will become visible the next time the window framebuffer is updated after this call.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_hide">Window visibility</a> </dd>
<dd>
<a class="el" href="group__window.html#ga49401f82a1ba5f15db5590728314d47c">glfwHideWindow</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga49401f82a1ba5f15db5590728314d47c" name="ga49401f82a1ba5f15db5590728314d47c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga49401f82a1ba5f15db5590728314d47c">&#9670;&#160;</a></span>glfwHideWindow()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwHideWindow </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function hides the specified window if it was previously visible. If the window is already hidden or is in full screen mode, this function does nothing.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to hide.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_hide">Window visibility</a> </dd>
<dd>
<a class="el" href="group__window.html#ga61be47917b72536a148300f46494fc66">glfwShowWindow</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga873780357abd3f3a081d71a40aae45a1" name="ga873780357abd3f3a081d71a40aae45a1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga873780357abd3f3a081d71a40aae45a1">&#9670;&#160;</a></span>glfwFocusWindow()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwFocusWindow </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function brings the specified window to front and sets input focus. The window should already be visible and not iconified.</p>
<p>By default, both windowed and full screen mode windows are focused when initially created. Set the <a class="el" href="window_guide.html#GLFW_FOCUSED_hint">GLFW_FOCUSED</a> to disable this behavior.</p>
<p>Also by default, windowed mode windows are focused when shown with <a class="el" href="group__window.html#ga61be47917b72536a148300f46494fc66">glfwShowWindow</a>. Set the <a class="el" href="window_guide.html#GLFW_FOCUS_ON_SHOW_hint">GLFW_FOCUS_ON_SHOW</a> to disable this behavior.</p>
<p><b>Do not use this function</b> to steal focus from other applications unless you are certain that is what the user wants. Focus stealing can be extremely disruptive.</p>
<p>For a less disruptive way of getting the user's attention, see <a class="el" href="window_guide.html#window_attention">attention requests</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to give input focus.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>Wayland:</b> The compositor will likely ignore focus requests unless another window created by the same application already has input focus.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_focus">Window input focus</a> </dd>
<dd>
<a class="el" href="window_guide.html#window_attention">Window attention request</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="ga2f8d59323fc4692c1d54ba08c863a703" name="ga2f8d59323fc4692c1d54ba08c863a703"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2f8d59323fc4692c1d54ba08c863a703">&#9670;&#160;</a></span>glfwRequestWindowAttention()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwRequestWindowAttention </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function requests user attention to the specified window. On platforms where this is not supported, attention is requested to the application as a whole.</p>
<p>Once the user has given attention, usually by focusing the window or application, the system will end the request automatically.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to request attention to.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>macOS:</b> Attention is requested to the application as a whole, not the specific window.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_attention">Window attention request</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga4d766499ac02c60f02221a9dfab87299" name="ga4d766499ac02c60f02221a9dfab87299"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4d766499ac02c60f02221a9dfab87299">&#9670;&#160;</a></span>glfwGetWindowMonitor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> * glfwGetWindowMonitor </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the handle of the monitor that the specified window is in full screen on.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to query. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The monitor, or <code>NULL</code> if the window is in windowed mode or an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_monitor">Window monitor</a> </dd>
<dd>
<a class="el" href="group__window.html#ga81c76c418af80a1cce7055bccb0ae0a7">glfwSetWindowMonitor</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga81c76c418af80a1cce7055bccb0ae0a7" name="ga81c76c418af80a1cce7055bccb0ae0a7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga81c76c418af80a1cce7055bccb0ae0a7">&#9670;&#160;</a></span>glfwSetWindowMonitor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetWindowMonitor </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>xpos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>ypos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>height</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>refreshRate</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the monitor that the window uses for full screen mode or, if the monitor is <code>NULL</code>, makes it windowed mode.</p>
<p>When setting a monitor, this function updates the width, height and refresh rate of the desired video mode and switches to the video mode closest to it. The window position is ignored when setting a monitor.</p>
<p>When the monitor is <code>NULL</code>, the position, width and height are used to place the window content area. The refresh rate is ignored when no monitor is specified.</p>
<p>If you only wish to update the resolution of a full screen window or the size of a windowed mode window, see <a class="el" href="group__window.html#ga371911f12c74c504dd8d47d832d095cb">glfwSetWindowSize</a>.</p>
<p>When a window transitions from full screen to windowed mode, this function restores any previous window settings such as whether it is decorated, floating, resizable, has size or aspect ratio limits, etc.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose monitor, size or video mode to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">monitor</td><td>The desired monitor, or <code>NULL</code> to set windowed mode. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">xpos</td><td>The desired x-coordinate of the upper-left corner of the content area. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ypos</td><td>The desired y-coordinate of the upper-left corner of the content area. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">width</td><td>The desired with, in screen coordinates, of the content area or video mode. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">height</td><td>The desired height, in screen coordinates, of the content area or video mode. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">refreshRate</td><td>The desired refresh rate, in Hz, of the video mode, or <code>GLFW_DONT_CARE</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>The OpenGL or OpenGL ES context will not be destroyed or otherwise affected by any resizing or mode switching, although you may need to update your viewport if the framebuffer size has changed.</dd>
<dd>
<b>Wayland:</b> The desired window position is ignored, as there is no way for an application to set this property.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_monitor">Window monitor</a> </dd>
<dd>
<a class="el" href="window_guide.html#window_full_screen">Full screen windows</a> </dd>
<dd>
<a class="el" href="group__window.html#ga4d766499ac02c60f02221a9dfab87299">glfwGetWindowMonitor</a> </dd>
<dd>
<a class="el" href="group__window.html#ga371911f12c74c504dd8d47d832d095cb">glfwSetWindowSize</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="gacccb29947ea4b16860ebef42c2cb9337" name="gacccb29947ea4b16860ebef42c2cb9337"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gacccb29947ea4b16860ebef42c2cb9337">&#9670;&#160;</a></span>glfwGetWindowAttrib()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwGetWindowAttrib </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>attrib</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the value of an attribute of the specified window or its OpenGL or OpenGL ES context.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to query. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">attrib</td><td>The <a class="el" href="window_guide.html#window_attribs">window attribute</a> whose value to return. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The value of the attribute, or zero if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>Framebuffer related hints are not window attributes. See <a class="el" href="window_guide.html#window_attribs_fb">Framebuffer related attributes</a> for more information.</dd>
<dd>
Zero is a valid value for many window and context related attributes so you cannot use a return value of zero as an indication of errors. However, this function should not fail as long as it is passed valid arguments and the library has been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd>
<dd>
<b>Wayland:</b> The Wayland protocol provides no way to check whether a window is iconfied, so <a class="el" href="group__window.html#ga39d44b7c056e55e581355a92d240b58a">GLFW_ICONIFIED</a> always returns <code>GLFW_FALSE</code>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_attribs">Window attributes</a> </dd>
<dd>
<a class="el" href="group__window.html#gace2afda29b4116ec012e410a6819033e">glfwSetWindowAttrib</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. Replaces <code>glfwGetWindowParam</code> and <code>glfwGetGLVersion</code>. </dd></dl>

</div>
</div>
<a id="gace2afda29b4116ec012e410a6819033e" name="gace2afda29b4116ec012e410a6819033e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gace2afda29b4116ec012e410a6819033e">&#9670;&#160;</a></span>glfwSetWindowAttrib()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetWindowAttrib </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>attrib</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the value of an attribute of the specified window.</p>
<p>The supported attributes are <a class="el" href="window_guide.html#GLFW_DECORATED_attrib">GLFW_DECORATED</a>, <a class="el" href="window_guide.html#GLFW_RESIZABLE_attrib">GLFW_RESIZABLE</a>, <a class="el" href="window_guide.html#GLFW_FLOATING_attrib">GLFW_FLOATING</a>, <a class="el" href="window_guide.html#GLFW_AUTO_ICONIFY_attrib">GLFW_AUTO_ICONIFY</a> and <a class="el" href="window_guide.html#GLFW_FOCUS_ON_SHOW_attrib">GLFW_FOCUS_ON_SHOW</a>. <a class="el" href="window_guide.html#GLFW_MOUSE_PASSTHROUGH_attrib">GLFW_MOUSE_PASSTHROUGH</a></p>
<p>Some of these attributes are ignored for full screen windows. The new value will take effect if the window is later made windowed.</p>
<p>Some of these attributes are ignored for windowed mode windows. The new value will take effect if the window is later made full screen.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window to set the attribute for. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">attrib</td><td>A supported window attribute. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga76f6bb9c4eea73db675f096b404593ce">GLFW_INVALID_ENUM</a>, <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a>, <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a> and <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a> (see remarks).</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>Calling <a class="el" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a> will always return the latest value, even if that value is ignored by the current mode of the window.</dd>
<dd>
<b>Wayland:</b> The <a class="el" href="window_guide.html#GLFW_FLOATING_attrib">GLFW_FLOATING</a> window attribute is not supported. Setting this will emit <a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_attribs">Window attributes</a> </dd>
<dd>
<a class="el" href="group__window.html#gacccb29947ea4b16860ebef42c2cb9337">glfwGetWindowAttrib</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga3d2fc6026e690ab31a13f78bc9fd3651" name="ga3d2fc6026e690ab31a13f78bc9fd3651"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3d2fc6026e690ab31a13f78bc9fd3651">&#9670;&#160;</a></span>glfwSetWindowUserPointer()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetWindowUserPointer </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>pointer</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the user-defined pointer of the specified window. The current value is retained until the window is destroyed. The initial value is <code>NULL</code>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose pointer to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">pointer</td><td>The new value.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_userptr">User pointer</a> </dd>
<dd>
<a class="el" href="group__window.html#gae77a4add0d2023ca21ff1443ced01653">glfwGetWindowUserPointer</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gae77a4add0d2023ca21ff1443ced01653" name="gae77a4add0d2023ca21ff1443ced01653"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae77a4add0d2023ca21ff1443ced01653">&#9670;&#160;</a></span>glfwGetWindowUserPointer()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void * glfwGetWindowUserPointer </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the current value of the user-defined pointer of the specified window. The initial value is <code>NULL</code>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose pointer to return.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_userptr">User pointer</a> </dd>
<dd>
<a class="el" href="group__window.html#ga3d2fc6026e690ab31a13f78bc9fd3651">glfwSetWindowUserPointer</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga08bdfbba88934f9c4f92fd757979ac74" name="ga08bdfbba88934f9c4f92fd757979ac74"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga08bdfbba88934f9c4f92fd757979ac74">&#9670;&#160;</a></span>glfwSetWindowPosCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__window.html#gabe287973a21a8f927cde4db06b8dcbe9">GLFWwindowposfun</a> glfwSetWindowPosCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__window.html#gabe287973a21a8f927cde4db06b8dcbe9">GLFWwindowposfun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the position callback of the specified window, which is called when the window is moved. The callback is provided with the position, in screen coordinates, of the upper-left corner of the content area of the window.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> xpos, <span class="keywordtype">int</span> ypos)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__window.html#gabe287973a21a8f927cde4db06b8dcbe9">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>Wayland:</b> This callback will never be called, as there is no way for an application to know its global position.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_pos">Window position</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gad91b8b047a0c4c6033c38853864c34f8" name="gad91b8b047a0c4c6033c38853864c34f8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad91b8b047a0c4c6033c38853864c34f8">&#9670;&#160;</a></span>glfwSetWindowSizeCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__window.html#gaec0282944bb810f6f3163ec02da90350">GLFWwindowsizefun</a> glfwSetWindowSizeCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__window.html#gaec0282944bb810f6f3163ec02da90350">GLFWwindowsizefun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the size callback of the specified window, which is called when the window is resized. The callback is provided with the size, in screen coordinates, of the content area of the window.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> width, <span class="keywordtype">int</span> height)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__window.html#gaec0282944bb810f6f3163ec02da90350">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_size">Window size</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. <b>GLFW 3:</b> Added window handle parameter and return value. </dd></dl>

</div>
</div>
<a id="gada646d775a7776a95ac000cfc1885331" name="gada646d775a7776a95ac000cfc1885331"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gada646d775a7776a95ac000cfc1885331">&#9670;&#160;</a></span>glfwSetWindowCloseCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__window.html#gabf859b936d80961b7d39013a9694cc3e">GLFWwindowclosefun</a> glfwSetWindowCloseCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__window.html#gabf859b936d80961b7d39013a9694cc3e">GLFWwindowclosefun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the close callback of the specified window, which is called when the user attempts to close the window, for example by clicking the close widget in the title bar.</p>
<p>The close flag is set before this callback is called, but you can modify it at any time with <a class="el" href="group__window.html#ga49c449dde2a6f87d996f4daaa09d6708">glfwSetWindowShouldClose</a>.</p>
<p>The close callback is not triggered by <a class="el" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__window.html#gabf859b936d80961b7d39013a9694cc3e">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>macOS:</b> Selecting Quit from the application menu will trigger the close callback for all windows.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_close">Window closing and close flag</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 2.5. <b>GLFW 3:</b> Added window handle parameter and return value. </dd></dl>

</div>
</div>
<a id="ga1c5c7eb889c33c7f4d10dd35b327654e" name="ga1c5c7eb889c33c7f4d10dd35b327654e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1c5c7eb889c33c7f4d10dd35b327654e">&#9670;&#160;</a></span>glfwSetWindowRefreshCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__window.html#ga431663a1427d2eb3a273bc398b6737b5">GLFWwindowrefreshfun</a> glfwSetWindowRefreshCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__window.html#ga431663a1427d2eb3a273bc398b6737b5">GLFWwindowrefreshfun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the refresh callback of the specified window, which is called when the content area of the window needs to be redrawn, for example if the window has been exposed after having been covered by another window.</p>
<p>On compositing window systems such as Aero, Compiz, Aqua or Wayland, where the window contents are saved off-screen, this callback may be called only very infrequently or never at all.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window);</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__window.html#ga431663a1427d2eb3a273bc398b6737b5">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_refresh">Window damage and refresh</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 2.5. <b>GLFW 3:</b> Added window handle parameter and return value. </dd></dl>

</div>
</div>
<a id="gac2d83c4a10f071baf841f6730528e66c" name="gac2d83c4a10f071baf841f6730528e66c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac2d83c4a10f071baf841f6730528e66c">&#9670;&#160;</a></span>glfwSetWindowFocusCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__window.html#gabc58c47e9d93f6eb1862d615c3680f46">GLFWwindowfocusfun</a> glfwSetWindowFocusCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__window.html#gabc58c47e9d93f6eb1862d615c3680f46">GLFWwindowfocusfun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the focus callback of the specified window, which is called when the window gains or loses input focus.</p>
<p>After the focus callback is called for a window that lost input focus, synthetic key and mouse button release events will be generated for all such that had been pressed. For more information, see <a class="el" href="group__input.html#ga1caf18159767e761185e49a3be019f8d">glfwSetKeyCallback</a> and <a class="el" href="group__input.html#ga6ab84420974d812bee700e45284a723c">glfwSetMouseButtonCallback</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> focused)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__window.html#gabc58c47e9d93f6eb1862d615c3680f46">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_focus">Window input focus</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gac793e9efd255567b5fb8b445052cfd3e" name="gac793e9efd255567b5fb8b445052cfd3e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac793e9efd255567b5fb8b445052cfd3e">&#9670;&#160;</a></span>glfwSetWindowIconifyCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__window.html#ga35c658cccba236f26e7adee0e25f6a4f">GLFWwindowiconifyfun</a> glfwSetWindowIconifyCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__window.html#ga35c658cccba236f26e7adee0e25f6a4f">GLFWwindowiconifyfun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the iconification callback of the specified window, which is called when the window is iconified or restored.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> iconified)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__window.html#ga35c658cccba236f26e7adee0e25f6a4f">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_iconify">Window iconification</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gacbe64c339fbd94885e62145563b6dc93" name="gacbe64c339fbd94885e62145563b6dc93"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gacbe64c339fbd94885e62145563b6dc93">&#9670;&#160;</a></span>glfwSetWindowMaximizeCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__window.html#ga3017196fdaec33ac3e095765176c2a90">GLFWwindowmaximizefun</a> glfwSetWindowMaximizeCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3017196fdaec33ac3e095765176c2a90">GLFWwindowmaximizefun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the maximization callback of the specified window, which is called when the window is maximized or restored.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> maximized)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__window.html#ga3017196fdaec33ac3e095765176c2a90">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_maximize">Window maximization</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="gab3fb7c3366577daef18c0023e2a8591f" name="gab3fb7c3366577daef18c0023e2a8591f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab3fb7c3366577daef18c0023e2a8591f">&#9670;&#160;</a></span>glfwSetFramebufferSizeCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__window.html#gae18026e294dde685ed2e5f759533144d">GLFWframebuffersizefun</a> glfwSetFramebufferSizeCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__window.html#gae18026e294dde685ed2e5f759533144d">GLFWframebuffersizefun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the framebuffer resize callback of the specified window, which is called when the framebuffer of the specified window is resized.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> width, <span class="keywordtype">int</span> height)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__window.html#gae18026e294dde685ed2e5f759533144d">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_fbsize">Framebuffer size</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gaf2832ebb5aa6c252a2d261de002c92d6" name="gaf2832ebb5aa6c252a2d261de002c92d6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf2832ebb5aa6c252a2d261de002c92d6">&#9670;&#160;</a></span>glfwSetWindowContentScaleCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__window.html#ga77f288a2d04bb3c77c7d9615d08cf70e">GLFWwindowcontentscalefun</a> glfwSetWindowContentScaleCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__window.html#ga77f288a2d04bb3c77c7d9615d08cf70e">GLFWwindowcontentscalefun</a>&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the window content scale callback of the specified window, which is called when the content scale of the specified window changes.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose callback to set. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The new callback, or <code>NULL</code> to remove the currently set callback. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The previously set callback, or <code>NULL</code> if no callback was set or the library had not been <a class="el" href="intro_guide.html#intro_init">initialized</a>.</dd></dl>
<dl class="section user"><dt>Callback signature</dt><dd><div class="fragment"><div class="line"><span class="keywordtype">void</span> function_name(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">float</span> xscale, <span class="keywordtype">float</span> yscale)</div>
</div><!-- fragment --> For more information about the callback parameters, see the <a class="el" href="group__window.html#ga77f288a2d04bb3c77c7d9615d08cf70e">function pointer type</a>.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#window_scale">Window content scale</a> </dd>
<dd>
<a class="el" href="group__window.html#gaf5d31de9c19c4f994facea64d2b3106c">glfwGetWindowContentScale</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga37bd57223967b4211d60ca1a0bf3c832" name="ga37bd57223967b4211d60ca1a0bf3c832"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga37bd57223967b4211d60ca1a0bf3c832">&#9670;&#160;</a></span>glfwPollEvents()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwPollEvents </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function processes only those events that are already in the event queue and then returns immediately. Processing events will cause the window and input callbacks associated with those events to be called.</p>
<p>On some platforms, a window move, resize or menu operation will cause event processing to block. This is due to how event processing is designed on those platforms. You can use the <a class="el" href="window_guide.html#window_refresh">window refresh callback</a> to redraw the contents of your window when necessary during such operations.</p>
<p>Do not assume that callbacks you set will <em>only</em> be called in response to event processing functions like this one. While it is necessary to poll for events, window systems that require GLFW to register callbacks of its own can pass events to GLFW in response to many window system function calls. GLFW will pass those events on to the application callbacks before returning.</p>
<p>Event processing is not required for joystick input to work.</p>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Reentrancy</dt><dd>This function must not be called from a callback.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#events">Event processing</a> </dd>
<dd>
<a class="el" href="group__window.html#ga554e37d781f0a997656c26b2c56c835e">glfwWaitEvents</a> </dd>
<dd>
<a class="el" href="group__window.html#ga605a178db92f1a7f1a925563ef3ea2cf">glfwWaitEventsTimeout</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. </dd></dl>

</div>
</div>
<a id="ga554e37d781f0a997656c26b2c56c835e" name="ga554e37d781f0a997656c26b2c56c835e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga554e37d781f0a997656c26b2c56c835e">&#9670;&#160;</a></span>glfwWaitEvents()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwWaitEvents </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function puts the calling thread to sleep until at least one event is available in the event queue. Once one or more events are available, it behaves exactly like <a class="el" href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a>, i.e. the events in the queue are processed and the function then returns immediately. Processing events will cause the window and input callbacks associated with those events to be called.</p>
<p>Since not all events are associated with callbacks, this function may return without a callback having been called even if you are monitoring all callbacks.</p>
<p>On some platforms, a window move, resize or menu operation will cause event processing to block. This is due to how event processing is designed on those platforms. You can use the <a class="el" href="window_guide.html#window_refresh">window refresh callback</a> to redraw the contents of your window when necessary during such operations.</p>
<p>Do not assume that callbacks you set will <em>only</em> be called in response to event processing functions like this one. While it is necessary to poll for events, window systems that require GLFW to register callbacks of its own can pass events to GLFW in response to many window system function calls. GLFW will pass those events on to the application callbacks before returning.</p>
<p>Event processing is not required for joystick input to work.</p>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Reentrancy</dt><dd>This function must not be called from a callback.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#events">Event processing</a> </dd>
<dd>
<a class="el" href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a> </dd>
<dd>
<a class="el" href="group__window.html#ga605a178db92f1a7f1a925563ef3ea2cf">glfwWaitEventsTimeout</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 2.5. </dd></dl>

</div>
</div>
<a id="ga605a178db92f1a7f1a925563ef3ea2cf" name="ga605a178db92f1a7f1a925563ef3ea2cf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga605a178db92f1a7f1a925563ef3ea2cf">&#9670;&#160;</a></span>glfwWaitEventsTimeout()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwWaitEventsTimeout </td>
          <td>(</td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>timeout</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function puts the calling thread to sleep until at least one event is available in the event queue, or until the specified timeout is reached. If one or more events are available, it behaves exactly like <a class="el" href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a>, i.e. the events in the queue are processed and the function then returns immediately. Processing events will cause the window and input callbacks associated with those events to be called.</p>
<p>The timeout value must be a positive finite number.</p>
<p>Since not all events are associated with callbacks, this function may return without a callback having been called even if you are monitoring all callbacks.</p>
<p>On some platforms, a window move, resize or menu operation will cause event processing to block. This is due to how event processing is designed on those platforms. You can use the <a class="el" href="window_guide.html#window_refresh">window refresh callback</a> to redraw the contents of your window when necessary during such operations.</p>
<p>Do not assume that callbacks you set will <em>only</em> be called in response to event processing functions like this one. While it is necessary to poll for events, window systems that require GLFW to register callbacks of its own can pass events to GLFW in response to many window system function calls. GLFW will pass those events on to the application callbacks before returning.</p>
<p>Event processing is not required for joystick input to work.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">timeout</td><td>The maximum amount of time, in seconds, to wait.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Reentrancy</dt><dd>This function must not be called from a callback.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#events">Event processing</a> </dd>
<dd>
<a class="el" href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a> </dd>
<dd>
<a class="el" href="group__window.html#ga554e37d781f0a997656c26b2c56c835e">glfwWaitEvents</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="gab5997a25187e9fd5c6f2ecbbc8dfd7e9" name="gab5997a25187e9fd5c6f2ecbbc8dfd7e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab5997a25187e9fd5c6f2ecbbc8dfd7e9">&#9670;&#160;</a></span>glfwPostEmptyEvent()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwPostEmptyEvent </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function posts an empty event from the current thread to the event queue, causing <a class="el" href="group__window.html#ga554e37d781f0a997656c26b2c56c835e">glfwWaitEvents</a> or <a class="el" href="group__window.html#ga605a178db92f1a7f1a925563ef3ea2cf">glfwWaitEventsTimeout</a> to return.</p>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#events">Event processing</a> </dd>
<dd>
<a class="el" href="group__window.html#ga554e37d781f0a997656c26b2c56c835e">glfwWaitEvents</a> </dd>
<dd>
<a class="el" href="group__window.html#ga605a178db92f1a7f1a925563ef3ea2cf">glfwWaitEventsTimeout</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.1. </dd></dl>

</div>
</div>
<a id="ga15a5a1ee5b3c2ca6b15ca209a12efd14" name="ga15a5a1ee5b3c2ca6b15ca209a12efd14"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga15a5a1ee5b3c2ca6b15ca209a12efd14">&#9670;&#160;</a></span>glfwSwapBuffers()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSwapBuffers </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function swaps the front and back buffers of the specified window when rendering with OpenGL or OpenGL ES. If the swap interval is greater than zero, the GPU driver waits the specified number of screen updates before swapping the buffers.</p>
<p>The specified window must have an OpenGL or OpenGL ES context. Specifying a window without a context will generate a <a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a> error.</p>
<p>This function does not apply to Vulkan. If you are rendering with Vulkan, see <code>vkQueuePresentKHR</code> instead.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose buffers to swap.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><b>EGL:</b> The context of the specified window must be current on the calling thread.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#buffer_swap">Buffer swapping</a> </dd>
<dd>
<a class="el" href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed">glfwSwapInterval</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. <b>GLFW 3:</b> Added window handle parameter. </dd></dl>

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
