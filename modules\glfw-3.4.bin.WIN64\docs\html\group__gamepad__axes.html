<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Gamepad axes</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle"><div class="title">Gamepad axes<div class="ingroups"><a class="el" href="group__input.html">Input reference</a></div></div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>See <a class="el" href="input_guide.html#gamepad">Gamepad input</a> for how these are used. </p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:ga544e396d092036a7d80c1e5f233f7a38" id="r_ga544e396d092036a7d80c1e5f233f7a38"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__axes.html#ga544e396d092036a7d80c1e5f233f7a38">GLFW_GAMEPAD_AXIS_LEFT_X</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:ga544e396d092036a7d80c1e5f233f7a38"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga64dcf2c6e9be50b7c556ff7671996dd5" id="r_ga64dcf2c6e9be50b7c556ff7671996dd5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__axes.html#ga64dcf2c6e9be50b7c556ff7671996dd5">GLFW_GAMEPAD_AXIS_LEFT_Y</a>&#160;&#160;&#160;1</td></tr>
<tr class="separator:ga64dcf2c6e9be50b7c556ff7671996dd5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabd6785106cd3c5a044a6e49a395ee2fc" id="r_gabd6785106cd3c5a044a6e49a395ee2fc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__axes.html#gabd6785106cd3c5a044a6e49a395ee2fc">GLFW_GAMEPAD_AXIS_RIGHT_X</a>&#160;&#160;&#160;2</td></tr>
<tr class="separator:gabd6785106cd3c5a044a6e49a395ee2fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1cc20566d44d521b7183681a8e88e2e4" id="r_ga1cc20566d44d521b7183681a8e88e2e4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__axes.html#ga1cc20566d44d521b7183681a8e88e2e4">GLFW_GAMEPAD_AXIS_RIGHT_Y</a>&#160;&#160;&#160;3</td></tr>
<tr class="separator:ga1cc20566d44d521b7183681a8e88e2e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6d79561dd8907c37354426242901b86e" id="r_ga6d79561dd8907c37354426242901b86e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__axes.html#ga6d79561dd8907c37354426242901b86e">GLFW_GAMEPAD_AXIS_LEFT_TRIGGER</a>&#160;&#160;&#160;4</td></tr>
<tr class="separator:ga6d79561dd8907c37354426242901b86e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga121a7d5d20589a423cd1634dd6ee6eab" id="r_ga121a7d5d20589a423cd1634dd6ee6eab"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__axes.html#ga121a7d5d20589a423cd1634dd6ee6eab">GLFW_GAMEPAD_AXIS_RIGHT_TRIGGER</a>&#160;&#160;&#160;5</td></tr>
<tr class="separator:ga121a7d5d20589a423cd1634dd6ee6eab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0818fd9433e1359692b7443293e5ac86" id="r_ga0818fd9433e1359692b7443293e5ac86"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__gamepad__axes.html#ga0818fd9433e1359692b7443293e5ac86">GLFW_GAMEPAD_AXIS_LAST</a>&#160;&#160;&#160;<a class="el" href="group__gamepad__axes.html#ga121a7d5d20589a423cd1634dd6ee6eab">GLFW_GAMEPAD_AXIS_RIGHT_TRIGGER</a></td></tr>
<tr class="separator:ga0818fd9433e1359692b7443293e5ac86"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="ga544e396d092036a7d80c1e5f233f7a38" name="ga544e396d092036a7d80c1e5f233f7a38"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga544e396d092036a7d80c1e5f233f7a38">&#9670;&#160;</a></span>GLFW_GAMEPAD_AXIS_LEFT_X</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_AXIS_LEFT_X&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga64dcf2c6e9be50b7c556ff7671996dd5" name="ga64dcf2c6e9be50b7c556ff7671996dd5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga64dcf2c6e9be50b7c556ff7671996dd5">&#9670;&#160;</a></span>GLFW_GAMEPAD_AXIS_LEFT_Y</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_AXIS_LEFT_Y&#160;&#160;&#160;1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gabd6785106cd3c5a044a6e49a395ee2fc" name="gabd6785106cd3c5a044a6e49a395ee2fc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gabd6785106cd3c5a044a6e49a395ee2fc">&#9670;&#160;</a></span>GLFW_GAMEPAD_AXIS_RIGHT_X</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_AXIS_RIGHT_X&#160;&#160;&#160;2</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga1cc20566d44d521b7183681a8e88e2e4" name="ga1cc20566d44d521b7183681a8e88e2e4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1cc20566d44d521b7183681a8e88e2e4">&#9670;&#160;</a></span>GLFW_GAMEPAD_AXIS_RIGHT_Y</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_AXIS_RIGHT_Y&#160;&#160;&#160;3</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga6d79561dd8907c37354426242901b86e" name="ga6d79561dd8907c37354426242901b86e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6d79561dd8907c37354426242901b86e">&#9670;&#160;</a></span>GLFW_GAMEPAD_AXIS_LEFT_TRIGGER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_AXIS_LEFT_TRIGGER&#160;&#160;&#160;4</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga121a7d5d20589a423cd1634dd6ee6eab" name="ga121a7d5d20589a423cd1634dd6ee6eab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga121a7d5d20589a423cd1634dd6ee6eab">&#9670;&#160;</a></span>GLFW_GAMEPAD_AXIS_RIGHT_TRIGGER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_AXIS_RIGHT_TRIGGER&#160;&#160;&#160;5</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga0818fd9433e1359692b7443293e5ac86" name="ga0818fd9433e1359692b7443293e5ac86"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga0818fd9433e1359692b7443293e5ac86">&#9670;&#160;</a></span>GLFW_GAMEPAD_AXIS_LAST</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_GAMEPAD_AXIS_LAST&#160;&#160;&#160;<a class="el" href="group__gamepad__axes.html#ga121a7d5d20589a423cd1634dd6ee6eab">GLFW_GAMEPAD_AXIS_RIGHT_TRIGGER</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
