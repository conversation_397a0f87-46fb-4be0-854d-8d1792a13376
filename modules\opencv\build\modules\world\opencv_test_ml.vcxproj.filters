﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ml\test\test_ann.cpp">
      <Filter>opencv_ml\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ml\test\test_bayes.cpp">
      <Filter>opencv_ml\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ml\test\test_em.cpp">
      <Filter>opencv_ml\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ml\test\test_kmeans.cpp">
      <Filter>opencv_ml\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ml\test\test_knearest.cpp">
      <Filter>opencv_ml\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ml\test\test_lr.cpp">
      <Filter>opencv_ml\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ml\test\test_main.cpp">
      <Filter>opencv_ml\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ml\test\test_mltests.cpp">
      <Filter>opencv_ml\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ml\test\test_rtrees.cpp">
      <Filter>opencv_ml\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ml\test\test_save_load.cpp">
      <Filter>opencv_ml\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ml\test\test_svmsgd.cpp">
      <Filter>opencv_ml\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ml\test\test_svmtrainauto.cpp">
      <Filter>opencv_ml\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\ml\test\test_utils.cpp">
      <Filter>opencv_ml\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\ml\test\test_precomp.hpp">
      <Filter>opencv_ml\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_ml">
      <UniqueIdentifier>{DA418736-4493-384C-81B8-7538371CA901}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_ml\Include">
      <UniqueIdentifier>{EC3EBE4B-D193-32CB-A490-FC254D96E411}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_ml\Src">
      <UniqueIdentifier>{169233FB-0105-3967-917F-938183CC25CA}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
