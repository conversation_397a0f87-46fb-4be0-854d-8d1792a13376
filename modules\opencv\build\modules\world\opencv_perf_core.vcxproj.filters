﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\cuda\perf_gpumat.cpp">
      <Filter>opencv_core\Src\cuda</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\opencl\perf_arithm.cpp">
      <Filter>opencv_core\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\opencl\perf_bufferpool.cpp">
      <Filter>opencv_core\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\opencl\perf_channels.cpp">
      <Filter>opencv_core\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\opencl\perf_dxt.cpp">
      <Filter>opencv_core\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\opencl\perf_gemm.cpp">
      <Filter>opencv_core\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\opencl\perf_matop.cpp">
      <Filter>opencv_core\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\opencl\perf_usage_flags.cpp">
      <Filter>opencv_core\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_abs.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_addWeighted.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_allocation.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_arithm.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_bitwise.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_compare.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_convertTo.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_cvround.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_dft.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_dot.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_inRange.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_io_base64.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_lut.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_main.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_mat.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_math.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_merge.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_minmaxloc.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_norm.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_reduce.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_sort.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_split.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_stat.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_umat.cpp">
      <Filter>opencv_core\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\core\perf\perf_precomp.hpp">
      <Filter>opencv_core\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_core">
      <UniqueIdentifier>{D10889F9-FE1B-3344-A0CB-8FD035FF683E}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_core\Include">
      <UniqueIdentifier>{CB0FFE36-5A9C-3286-AFE8-956D499C76DF}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_core\Src">
      <UniqueIdentifier>{8C58C161-C746-3A49-912D-28B5DFBEEBF4}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_core\Src\cuda">
      <UniqueIdentifier>{CC5ECAEC-6404-3144-883D-C9A8CE475B33}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_core\Src\opencl">
      <UniqueIdentifier>{64F39A6D-DF75-3CB8-AB57-22AB9ADC39CB}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
