<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Native access</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">Native access</div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p><b>By using the native access functions you assert that you know what you're doing and how to fix problems caused by using them. If you don't, you shouldn't be using them.</b></p>
<p>Before the inclusion of <a class="el" href="glfw3native_8h.html">glfw3native.h</a>, you may define zero or more window system API macro and zero or more context creation API macros.</p>
<p>The chosen backends must match those the library was compiled for. Failure to do this will cause a link-time error.</p>
<p>The available window API macros are:</p><ul>
<li><code>GLFW_EXPOSE_NATIVE_WIN32</code></li>
<li><code>GLFW_EXPOSE_NATIVE_COCOA</code></li>
<li><code>GLFW_EXPOSE_NATIVE_X11</code></li>
<li><code>GLFW_EXPOSE_NATIVE_WAYLAND</code></li>
</ul>
<p>The available context API macros are:</p><ul>
<li><code>GLFW_EXPOSE_NATIVE_WGL</code></li>
<li><code>GLFW_EXPOSE_NATIVE_NSGL</code></li>
<li><code>GLFW_EXPOSE_NATIVE_GLX</code></li>
<li><code>GLFW_EXPOSE_NATIVE_EGL</code></li>
<li><code>GLFW_EXPOSE_NATIVE_OSMESA</code></li>
</ul>
<p>These macros select which of the native access functions that are declared and which platform-specific headers to include. It is then up your (by definition platform-specific) code to handle which of these should be defined.</p>
<p>If you do not want the platform-specific headers to be included, define <code>GLFW_NATIVE_INCLUDE_NONE</code> before including the <a class="el" href="glfw3native_8h.html">glfw3native.h</a> header.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#define GLFW_EXPOSE_NATIVE_WIN32</span></div>
<div class="line"><span class="preprocessor">#define GLFW_EXPOSE_NATIVE_WGL</span></div>
<div class="line"><span class="preprocessor">#define GLFW_NATIVE_INCLUDE_NONE</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3native_8h.html">GLFW/glfw3native.h</a>&gt;</span></div>
<div class="ttc" id="aglfw3native_8h_html"><div class="ttname"><a href="glfw3native_8h.html">glfw3native.h</a></div><div class="ttdoc">The header of the native access functions.</div></div>
</div><!-- fragment --> <table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gad4d3e9242536c0ba6be88a98f4c73a41" id="r_gad4d3e9242536c0ba6be88a98f4c73a41"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gad4d3e9242536c0ba6be88a98f4c73a41">glfwGetWin32Adapter</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:gad4d3e9242536c0ba6be88a98f4c73a41"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the adapter device name of the specified monitor.  <br /></td></tr>
<tr class="separator:gad4d3e9242536c0ba6be88a98f4c73a41"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac845f7dbe4c1d7fdd682a3c6fdae6766" id="r_gac845f7dbe4c1d7fdd682a3c6fdae6766"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gac845f7dbe4c1d7fdd682a3c6fdae6766">glfwGetWin32Monitor</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:gac845f7dbe4c1d7fdd682a3c6fdae6766"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the display device name of the specified monitor.  <br /></td></tr>
<tr class="separator:gac845f7dbe4c1d7fdd682a3c6fdae6766"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafe5079aa79038b0079fc09d5f0a8e667" id="r_gafe5079aa79038b0079fc09d5f0a8e667"><td class="memItemLeft" align="right" valign="top">HWND&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gafe5079aa79038b0079fc09d5f0a8e667">glfwGetWin32Window</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:gafe5079aa79038b0079fc09d5f0a8e667"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>HWND</code> of the specified window.  <br /></td></tr>
<tr class="separator:gafe5079aa79038b0079fc09d5f0a8e667"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadc4010d91d9cc1134d040eeb1202a143" id="r_gadc4010d91d9cc1134d040eeb1202a143"><td class="memItemLeft" align="right" valign="top">HGLRC&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gadc4010d91d9cc1134d040eeb1202a143">glfwGetWGLContext</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:gadc4010d91d9cc1134d040eeb1202a143"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>HGLRC</code> of the specified window.  <br /></td></tr>
<tr class="separator:gadc4010d91d9cc1134d040eeb1202a143"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf22f429aec4b1aab316142d66d9be3e6" id="r_gaf22f429aec4b1aab316142d66d9be3e6"><td class="memItemLeft" align="right" valign="top">CGDirectDisplayID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gaf22f429aec4b1aab316142d66d9be3e6">glfwGetCocoaMonitor</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:gaf22f429aec4b1aab316142d66d9be3e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>CGDirectDisplayID</code> of the specified monitor.  <br /></td></tr>
<tr class="separator:gaf22f429aec4b1aab316142d66d9be3e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac3ed9d495d0c2bb9652de5a50c648715" id="r_gac3ed9d495d0c2bb9652de5a50c648715"><td class="memItemLeft" align="right" valign="top">id&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gac3ed9d495d0c2bb9652de5a50c648715">glfwGetCocoaWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:gac3ed9d495d0c2bb9652de5a50c648715"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>NSWindow</code> of the specified window.  <br /></td></tr>
<tr class="separator:gac3ed9d495d0c2bb9652de5a50c648715"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7274fb6595894e880fc95dc63156e9b1" id="r_ga7274fb6595894e880fc95dc63156e9b1"><td class="memItemLeft" align="right" valign="top">id&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga7274fb6595894e880fc95dc63156e9b1">glfwGetCocoaView</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga7274fb6595894e880fc95dc63156e9b1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>NSView</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga7274fb6595894e880fc95dc63156e9b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga559e002e3cd63c979881770cd4dc63bc" id="r_ga559e002e3cd63c979881770cd4dc63bc"><td class="memItemLeft" align="right" valign="top">id&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga559e002e3cd63c979881770cd4dc63bc">glfwGetNSGLContext</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga559e002e3cd63c979881770cd4dc63bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>NSOpenGLContext</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga559e002e3cd63c979881770cd4dc63bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6e7822385cc8a1cc3b18f60352830189" id="r_ga6e7822385cc8a1cc3b18f60352830189"><td class="memItemLeft" align="right" valign="top">Display *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga6e7822385cc8a1cc3b18f60352830189">glfwGetX11Display</a> (void)</td></tr>
<tr class="memdesc:ga6e7822385cc8a1cc3b18f60352830189"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>Display</code> used by GLFW.  <br /></td></tr>
<tr class="separator:ga6e7822385cc8a1cc3b18f60352830189"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga088fbfa80f50569402b41be71ad66e40" id="r_ga088fbfa80f50569402b41be71ad66e40"><td class="memItemLeft" align="right" valign="top">RRCrtc&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga088fbfa80f50569402b41be71ad66e40">glfwGetX11Adapter</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:ga088fbfa80f50569402b41be71ad66e40"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>RRCrtc</code> of the specified monitor.  <br /></td></tr>
<tr class="separator:ga088fbfa80f50569402b41be71ad66e40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab2f8cc043905e9fa9b12bfdbbcfe874c" id="r_gab2f8cc043905e9fa9b12bfdbbcfe874c"><td class="memItemLeft" align="right" valign="top">RROutput&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gab2f8cc043905e9fa9b12bfdbbcfe874c">glfwGetX11Monitor</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:gab2f8cc043905e9fa9b12bfdbbcfe874c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>RROutput</code> of the specified monitor.  <br /></td></tr>
<tr class="separator:gab2f8cc043905e9fa9b12bfdbbcfe874c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga90ca676322740842db446999a1b1f21d" id="r_ga90ca676322740842db446999a1b1f21d"><td class="memItemLeft" align="right" valign="top">Window&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga90ca676322740842db446999a1b1f21d">glfwGetX11Window</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga90ca676322740842db446999a1b1f21d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>Window</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga90ca676322740842db446999a1b1f21d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga55f879ab02d93367f966186b6f0133f7" id="r_ga55f879ab02d93367f966186b6f0133f7"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga55f879ab02d93367f966186b6f0133f7">glfwSetX11SelectionString</a> (const char *string)</td></tr>
<tr class="memdesc:ga55f879ab02d93367f966186b6f0133f7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the current primary selection to the specified string.  <br /></td></tr>
<tr class="separator:ga55f879ab02d93367f966186b6f0133f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae084ef64dc0db140b455b1427256d3f7" id="r_gae084ef64dc0db140b455b1427256d3f7"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gae084ef64dc0db140b455b1427256d3f7">glfwGetX11SelectionString</a> (void)</td></tr>
<tr class="memdesc:gae084ef64dc0db140b455b1427256d3f7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the contents of the current primary selection as a string.  <br /></td></tr>
<tr class="separator:gae084ef64dc0db140b455b1427256d3f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga62d884114b0abfcdc2930e89f20867e2" id="r_ga62d884114b0abfcdc2930e89f20867e2"><td class="memItemLeft" align="right" valign="top">GLXContext&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga62d884114b0abfcdc2930e89f20867e2">glfwGetGLXContext</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga62d884114b0abfcdc2930e89f20867e2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>GLXContext</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga62d884114b0abfcdc2930e89f20867e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1ed27b8766e859a21381e8f8ce18d049" id="r_ga1ed27b8766e859a21381e8f8ce18d049"><td class="memItemLeft" align="right" valign="top">GLXWindow&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga1ed27b8766e859a21381e8f8ce18d049">glfwGetGLXWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga1ed27b8766e859a21381e8f8ce18d049"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>GLXWindow</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga1ed27b8766e859a21381e8f8ce18d049"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacbe11f93ce20621de82989bbba94e62a" id="r_gacbe11f93ce20621de82989bbba94e62a"><td class="memItemLeft" align="right" valign="top">struct wl_display *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#gacbe11f93ce20621de82989bbba94e62a">glfwGetWaylandDisplay</a> (void)</td></tr>
<tr class="memdesc:gacbe11f93ce20621de82989bbba94e62a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>struct wl_display*</code> used by GLFW.  <br /></td></tr>
<tr class="separator:gacbe11f93ce20621de82989bbba94e62a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4f16066bd4c59e2f99418adfcb43dd16" id="r_ga4f16066bd4c59e2f99418adfcb43dd16"><td class="memItemLeft" align="right" valign="top">struct wl_output *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga4f16066bd4c59e2f99418adfcb43dd16">glfwGetWaylandMonitor</a> (<a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *monitor)</td></tr>
<tr class="memdesc:ga4f16066bd4c59e2f99418adfcb43dd16"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>struct wl_output*</code> of the specified monitor.  <br /></td></tr>
<tr class="separator:ga4f16066bd4c59e2f99418adfcb43dd16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5c597f2841229d9626f0811cca41ceb3" id="r_ga5c597f2841229d9626f0811cca41ceb3"><td class="memItemLeft" align="right" valign="top">struct wl_surface *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga5c597f2841229d9626f0811cca41ceb3">glfwGetWaylandWindow</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga5c597f2841229d9626f0811cca41ceb3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the main <code>struct wl_surface*</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga5c597f2841229d9626f0811cca41ceb3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1cd8d973f47aacb5532d368147cc3138" id="r_ga1cd8d973f47aacb5532d368147cc3138"><td class="memItemLeft" align="right" valign="top">EGLDisplay&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga1cd8d973f47aacb5532d368147cc3138">glfwGetEGLDisplay</a> (void)</td></tr>
<tr class="memdesc:ga1cd8d973f47aacb5532d368147cc3138"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>EGLDisplay</code> used by GLFW.  <br /></td></tr>
<tr class="separator:ga1cd8d973f47aacb5532d368147cc3138"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga671c5072becd085f4ab5771a9c8efcf1" id="r_ga671c5072becd085f4ab5771a9c8efcf1"><td class="memItemLeft" align="right" valign="top">EGLContext&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga671c5072becd085f4ab5771a9c8efcf1">glfwGetEGLContext</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga671c5072becd085f4ab5771a9c8efcf1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>EGLContext</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga671c5072becd085f4ab5771a9c8efcf1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2199b36117a6a695fec8441d8052eee6" id="r_ga2199b36117a6a695fec8441d8052eee6"><td class="memItemLeft" align="right" valign="top">EGLSurface&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga2199b36117a6a695fec8441d8052eee6">glfwGetEGLSurface</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga2199b36117a6a695fec8441d8052eee6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>EGLSurface</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga2199b36117a6a695fec8441d8052eee6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3b36e3e3dcf308b776427b6bd73cc132" id="r_ga3b36e3e3dcf308b776427b6bd73cc132"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga3b36e3e3dcf308b776427b6bd73cc132">glfwGetOSMesaColorBuffer</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int *width, int *height, int *format, void **buffer)</td></tr>
<tr class="memdesc:ga3b36e3e3dcf308b776427b6bd73cc132"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the color buffer associated with the specified window.  <br /></td></tr>
<tr class="separator:ga3b36e3e3dcf308b776427b6bd73cc132"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6b64039ffc88a7a2f57f0956c0c75d53" id="r_ga6b64039ffc88a7a2f57f0956c0c75d53"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga6b64039ffc88a7a2f57f0956c0c75d53">glfwGetOSMesaDepthBuffer</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window, int *width, int *height, int *bytesPerValue, void **buffer)</td></tr>
<tr class="memdesc:ga6b64039ffc88a7a2f57f0956c0c75d53"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieves the depth buffer associated with the specified window.  <br /></td></tr>
<tr class="separator:ga6b64039ffc88a7a2f57f0956c0c75d53"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9e47700080094eb569cb053afaa88773" id="r_ga9e47700080094eb569cb053afaa88773"><td class="memItemLeft" align="right" valign="top">OSMesaContext&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__native.html#ga9e47700080094eb569cb053afaa88773">glfwGetOSMesaContext</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga9e47700080094eb569cb053afaa88773"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the <code>OSMesaContext</code> of the specified window.  <br /></td></tr>
<tr class="separator:ga9e47700080094eb569cb053afaa88773"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Function Documentation</h2>
<a id="gad4d3e9242536c0ba6be88a98f4c73a41" name="gad4d3e9242536c0ba6be88a98f4c73a41"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad4d3e9242536c0ba6be88a98f4c73a41">&#9670;&#160;</a></span>glfwGetWin32Adapter()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char * glfwGetWin32Adapter </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The UTF-8 encoded adapter device name (for example <code>\\.\DISPLAY1</code>) of the specified monitor, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.1. </dd></dl>

</div>
</div>
<a id="gac845f7dbe4c1d7fdd682a3c6fdae6766" name="gac845f7dbe4c1d7fdd682a3c6fdae6766"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac845f7dbe4c1d7fdd682a3c6fdae6766">&#9670;&#160;</a></span>glfwGetWin32Monitor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char * glfwGetWin32Monitor </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The UTF-8 encoded display device name (for example <code>\\.\DISPLAY1\Monitor0</code>) of the specified monitor, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.1. </dd></dl>

</div>
</div>
<a id="gafe5079aa79038b0079fc09d5f0a8e667" name="gafe5079aa79038b0079fc09d5f0a8e667"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gafe5079aa79038b0079fc09d5f0a8e667">&#9670;&#160;</a></span>glfwGetWin32Window()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">HWND glfwGetWin32Window </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>HWND</code> of the specified window, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>The <code>HDC</code> associated with the window can be queried with the <a href="https://docs.microsoft.com/en-us/windows/win32/api/winuser/nf-winuser-getdc">GetDC</a> function. <div class="fragment"><div class="line">HDC dc = GetDC(<a class="code hl_function" href="group__native.html#gafe5079aa79038b0079fc09d5f0a8e667">glfwGetWin32Window</a>(window));</div>
<div class="ttc" id="agroup__native_html_gafe5079aa79038b0079fc09d5f0a8e667"><div class="ttname"><a href="group__native.html#gafe5079aa79038b0079fc09d5f0a8e667">glfwGetWin32Window</a></div><div class="ttdeci">HWND glfwGetWin32Window(GLFWwindow *window)</div><div class="ttdoc">Returns the HWND of the specified window.</div></div>
</div><!-- fragment --> This DC is private and does not need to be released.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gadc4010d91d9cc1134d040eeb1202a143" name="gadc4010d91d9cc1134d040eeb1202a143"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadc4010d91d9cc1134d040eeb1202a143">&#9670;&#160;</a></span>glfwGetWGLContext()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">HGLRC glfwGetWGLContext </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>HGLRC</code> of the specified window, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a> and <a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>The <code>HDC</code> associated with the window can be queried with the <a href="https://docs.microsoft.com/en-us/windows/win32/api/winuser/nf-winuser-getdc">GetDC</a> function. <div class="fragment"><div class="line">HDC dc = GetDC(<a class="code hl_function" href="group__native.html#gafe5079aa79038b0079fc09d5f0a8e667">glfwGetWin32Window</a>(window));</div>
</div><!-- fragment --> This DC is private and does not need to be released.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gaf22f429aec4b1aab316142d66d9be3e6" name="gaf22f429aec4b1aab316142d66d9be3e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf22f429aec4b1aab316142d66d9be3e6">&#9670;&#160;</a></span>glfwGetCocoaMonitor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">CGDirectDisplayID glfwGetCocoaMonitor </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>CGDirectDisplayID</code> of the specified monitor, or <code>kCGNullDirectDisplay</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.1. </dd></dl>

</div>
</div>
<a id="gac3ed9d495d0c2bb9652de5a50c648715" name="gac3ed9d495d0c2bb9652de5a50c648715"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac3ed9d495d0c2bb9652de5a50c648715">&#9670;&#160;</a></span>glfwGetCocoaWindow()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">id glfwGetCocoaWindow </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>NSWindow</code> of the specified window, or <code>nil</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga7274fb6595894e880fc95dc63156e9b1" name="ga7274fb6595894e880fc95dc63156e9b1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga7274fb6595894e880fc95dc63156e9b1">&#9670;&#160;</a></span>glfwGetCocoaView()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">id glfwGetCocoaView </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>NSView</code> of the specified window, or <code>nil</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.4. </dd></dl>

</div>
</div>
<a id="ga559e002e3cd63c979881770cd4dc63bc" name="ga559e002e3cd63c979881770cd4dc63bc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga559e002e3cd63c979881770cd4dc63bc">&#9670;&#160;</a></span>glfwGetNSGLContext()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">id glfwGetNSGLContext </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>NSOpenGLContext</code> of the specified window, or <code>nil</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a> and <a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga6e7822385cc8a1cc3b18f60352830189" name="ga6e7822385cc8a1cc3b18f60352830189"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6e7822385cc8a1cc3b18f60352830189">&#9670;&#160;</a></span>glfwGetX11Display()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Display * glfwGetX11Display </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>Display</code> used by GLFW, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga088fbfa80f50569402b41be71ad66e40" name="ga088fbfa80f50569402b41be71ad66e40"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga088fbfa80f50569402b41be71ad66e40">&#9670;&#160;</a></span>glfwGetX11Adapter()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">RRCrtc glfwGetX11Adapter </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>RRCrtc</code> of the specified monitor, or <code>None</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.1. </dd></dl>

</div>
</div>
<a id="gab2f8cc043905e9fa9b12bfdbbcfe874c" name="gab2f8cc043905e9fa9b12bfdbbcfe874c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab2f8cc043905e9fa9b12bfdbbcfe874c">&#9670;&#160;</a></span>glfwGetX11Monitor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">RROutput glfwGetX11Monitor </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>RROutput</code> of the specified monitor, or <code>None</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.1. </dd></dl>

</div>
</div>
<a id="ga90ca676322740842db446999a1b1f21d" name="ga90ca676322740842db446999a1b1f21d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga90ca676322740842db446999a1b1f21d">&#9670;&#160;</a></span>glfwGetX11Window()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Window glfwGetX11Window </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>Window</code> of the specified window, or <code>None</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga55f879ab02d93367f966186b6f0133f7" name="ga55f879ab02d93367f966186b6f0133f7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga55f879ab02d93367f966186b6f0133f7">&#9670;&#160;</a></span>glfwSetX11SelectionString()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSetX11SelectionString </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>string</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">string</td><td>A UTF-8 encoded string.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The specified string is copied before this function returns.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#clipboard">Clipboard input and output</a> </dd>
<dd>
<a class="el" href="group__native.html#gae084ef64dc0db140b455b1427256d3f7" title="Returns the contents of the current primary selection as a string.">glfwGetX11SelectionString</a> </dd>
<dd>
<a class="el" href="group__input.html#gaba1f022c5eb07dfac421df34cdcd31dd" title="Sets the clipboard to the specified string.">glfwSetClipboardString</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="gae084ef64dc0db140b455b1427256d3f7" name="gae084ef64dc0db140b455b1427256d3f7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae084ef64dc0db140b455b1427256d3f7">&#9670;&#160;</a></span>glfwGetX11SelectionString()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char * glfwGetX11SelectionString </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If the selection is empty or if its contents cannot be converted, <code>NULL</code> is returned and a <a class="el" href="group__errors.html#ga196e125ef261d94184e2b55c05762f14">GLFW_FORMAT_UNAVAILABLE</a> error is generated.</p>
<dl class="section return"><dt>Returns</dt><dd>The contents of the selection as a UTF-8 encoded string, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned string is allocated and freed by GLFW. You should not free it yourself. It is valid until the next call to <a class="el" href="group__native.html#gae084ef64dc0db140b455b1427256d3f7">glfwGetX11SelectionString</a> or <a class="el" href="group__native.html#ga55f879ab02d93367f966186b6f0133f7">glfwSetX11SelectionString</a>, or until the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function must only be called from the main thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="input_guide.html#clipboard">Clipboard input and output</a> </dd>
<dd>
<a class="el" href="group__native.html#ga55f879ab02d93367f966186b6f0133f7" title="Sets the current primary selection to the specified string.">glfwSetX11SelectionString</a> </dd>
<dd>
<a class="el" href="group__input.html#ga71a5b20808ea92193d65c21b82580355" title="Returns the contents of the clipboard as a string.">glfwGetClipboardString</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga62d884114b0abfcdc2930e89f20867e2" name="ga62d884114b0abfcdc2930e89f20867e2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga62d884114b0abfcdc2930e89f20867e2">&#9670;&#160;</a></span>glfwGetGLXContext()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLXContext glfwGetGLXContext </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>GLXContext</code> of the specified window, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a> and <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga1ed27b8766e859a21381e8f8ce18d049" name="ga1ed27b8766e859a21381e8f8ce18d049"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1ed27b8766e859a21381e8f8ce18d049">&#9670;&#160;</a></span>glfwGetGLXWindow()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLXWindow glfwGetGLXWindow </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>GLXWindow</code> of the specified window, or <code>None</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a> and <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="gacbe11f93ce20621de82989bbba94e62a" name="gacbe11f93ce20621de82989bbba94e62a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gacbe11f93ce20621de82989bbba94e62a">&#9670;&#160;</a></span>glfwGetWaylandDisplay()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct wl_display * glfwGetWaylandDisplay </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>struct wl_display*</code> used by GLFW, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="ga4f16066bd4c59e2f99418adfcb43dd16" name="ga4f16066bd4c59e2f99418adfcb43dd16"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4f16066bd4c59e2f99418adfcb43dd16">&#9670;&#160;</a></span>glfwGetWaylandMonitor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct wl_output * glfwGetWaylandMonitor </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__monitor.html#ga8d9efd1cde9426692c73fe40437d0ae3">GLFWmonitor</a> *&#160;</td>
          <td class="paramname"><em>monitor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>struct wl_output*</code> of the specified monitor, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="ga5c597f2841229d9626f0811cca41ceb3" name="ga5c597f2841229d9626f0811cca41ceb3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5c597f2841229d9626f0811cca41ceb3">&#9670;&#160;</a></span>glfwGetWaylandWindow()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct wl_surface * glfwGetWaylandWindow </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The main <code>struct wl_surface*</code> of the specified window, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.2. </dd></dl>

</div>
</div>
<a id="ga1cd8d973f47aacb5532d368147cc3138" name="ga1cd8d973f47aacb5532d368147cc3138"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1cd8d973f47aacb5532d368147cc3138">&#9670;&#160;</a></span>glfwGetEGLDisplay()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">EGLDisplay glfwGetEGLDisplay </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>EGLDisplay</code> used by GLFW, or <code>EGL_NO_DISPLAY</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>Because EGL is initialized on demand, this function will return <code>EGL_NO_DISPLAY</code> until the first context has been created via EGL.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga671c5072becd085f4ab5771a9c8efcf1" name="ga671c5072becd085f4ab5771a9c8efcf1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga671c5072becd085f4ab5771a9c8efcf1">&#9670;&#160;</a></span>glfwGetEGLContext()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">EGLContext glfwGetEGLContext </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>EGLContext</code> of the specified window, or <code>EGL_NO_CONTEXT</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga2199b36117a6a695fec8441d8052eee6" name="ga2199b36117a6a695fec8441d8052eee6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2199b36117a6a695fec8441d8052eee6">&#9670;&#160;</a></span>glfwGetEGLSurface()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">EGLSurface glfwGetEGLSurface </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>EGLSurface</code> of the specified window, or <code>EGL_NO_SURFACE</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga3b36e3e3dcf308b776427b6bd73cc132" name="ga3b36e3e3dcf308b776427b6bd73cc132"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3b36e3e3dcf308b776427b6bd73cc132">&#9670;&#160;</a></span>glfwGetOSMesaColorBuffer()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwGetOSMesaColorBuffer </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>height</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>format</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void **&#160;</td>
          <td class="paramname"><em>buffer</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose color buffer to retrieve. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">width</td><td>Where to store the width of the color buffer, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">height</td><td>Where to store the height of the color buffer, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">format</td><td>Where to store the OSMesa pixel format of the color buffer, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">buffer</td><td>Where to store the address of the color buffer, or <code>NULL</code>. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><code>GLFW_TRUE</code> if successful, or <code>GLFW_FALSE</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga6b64039ffc88a7a2f57f0956c0c75d53" name="ga6b64039ffc88a7a2f57f0956c0c75d53"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6b64039ffc88a7a2f57f0956c0c75d53">&#9670;&#160;</a></span>glfwGetOSMesaDepthBuffer()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwGetOSMesaDepthBuffer </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>height</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>bytesPerValue</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void **&#160;</td>
          <td class="paramname"><em>buffer</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose depth buffer to retrieve. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">width</td><td>Where to store the width of the depth buffer, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">height</td><td>Where to store the height of the depth buffer, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">bytesPerValue</td><td>Where to store the number of bytes per depth buffer element, or <code>NULL</code>. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">buffer</td><td>Where to store the address of the depth buffer, or <code>NULL</code>. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><code>GLFW_TRUE</code> if successful, or <code>GLFW_FALSE</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
<a id="ga9e47700080094eb569cb053afaa88773" name="ga9e47700080094eb569cb053afaa88773"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9e47700080094eb569cb053afaa88773">&#9670;&#160;</a></span>glfwGetOSMesaContext()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">OSMesaContext glfwGetOSMesaContext </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The <code>OSMesaContext</code> of the specified window, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> and <a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread. Access is not synchronized.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.3. </dd></dl>

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
