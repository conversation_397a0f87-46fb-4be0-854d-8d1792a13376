// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: function.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_function_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_function_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "attr_value.pb.h"
#include "op_def.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_function_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_function_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[5]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_function_2eproto;
namespace opencv_tensorflow {
class FunctionDef;
struct FunctionDefDefaultTypeInternal;
extern FunctionDefDefaultTypeInternal _FunctionDef_default_instance_;
class FunctionDefLibrary;
struct FunctionDefLibraryDefaultTypeInternal;
extern FunctionDefLibraryDefaultTypeInternal _FunctionDefLibrary_default_instance_;
class FunctionDef_Node;
struct FunctionDef_NodeDefaultTypeInternal;
extern FunctionDef_NodeDefaultTypeInternal _FunctionDef_Node_default_instance_;
class FunctionDef_Node_AttrEntry_DoNotUse;
struct FunctionDef_Node_AttrEntry_DoNotUseDefaultTypeInternal;
extern FunctionDef_Node_AttrEntry_DoNotUseDefaultTypeInternal _FunctionDef_Node_AttrEntry_DoNotUse_default_instance_;
class GradientDef;
struct GradientDefDefaultTypeInternal;
extern GradientDefDefaultTypeInternal _GradientDef_default_instance_;
}  // namespace opencv_tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::opencv_tensorflow::FunctionDef* Arena::CreateMaybeMessage<::opencv_tensorflow::FunctionDef>(Arena*);
template<> ::opencv_tensorflow::FunctionDefLibrary* Arena::CreateMaybeMessage<::opencv_tensorflow::FunctionDefLibrary>(Arena*);
template<> ::opencv_tensorflow::FunctionDef_Node* Arena::CreateMaybeMessage<::opencv_tensorflow::FunctionDef_Node>(Arena*);
template<> ::opencv_tensorflow::FunctionDef_Node_AttrEntry_DoNotUse* Arena::CreateMaybeMessage<::opencv_tensorflow::FunctionDef_Node_AttrEntry_DoNotUse>(Arena*);
template<> ::opencv_tensorflow::GradientDef* Arena::CreateMaybeMessage<::opencv_tensorflow::GradientDef>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace opencv_tensorflow {

// ===================================================================

class FunctionDefLibrary final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_tensorflow.FunctionDefLibrary) */ {
 public:
  inline FunctionDefLibrary() : FunctionDefLibrary(nullptr) {}
  ~FunctionDefLibrary() override;
  explicit constexpr FunctionDefLibrary(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FunctionDefLibrary(const FunctionDefLibrary& from);
  FunctionDefLibrary(FunctionDefLibrary&& from) noexcept
    : FunctionDefLibrary() {
    *this = ::std::move(from);
  }

  inline FunctionDefLibrary& operator=(const FunctionDefLibrary& from) {
    CopyFrom(from);
    return *this;
  }
  inline FunctionDefLibrary& operator=(FunctionDefLibrary&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FunctionDefLibrary& default_instance() {
    return *internal_default_instance();
  }
  static inline const FunctionDefLibrary* internal_default_instance() {
    return reinterpret_cast<const FunctionDefLibrary*>(
               &_FunctionDefLibrary_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(FunctionDefLibrary& a, FunctionDefLibrary& b) {
    a.Swap(&b);
  }
  inline void Swap(FunctionDefLibrary* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FunctionDefLibrary* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FunctionDefLibrary* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FunctionDefLibrary>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FunctionDefLibrary& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FunctionDefLibrary& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunctionDefLibrary* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_tensorflow.FunctionDefLibrary";
  }
  protected:
  explicit FunctionDefLibrary(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFunctionFieldNumber = 1,
    kGradientFieldNumber = 2,
  };
  // repeated .opencv_tensorflow.FunctionDef function = 1;
  int function_size() const;
  private:
  int _internal_function_size() const;
  public:
  void clear_function();
  ::opencv_tensorflow::FunctionDef* mutable_function(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::FunctionDef >*
      mutable_function();
  private:
  const ::opencv_tensorflow::FunctionDef& _internal_function(int index) const;
  ::opencv_tensorflow::FunctionDef* _internal_add_function();
  public:
  const ::opencv_tensorflow::FunctionDef& function(int index) const;
  ::opencv_tensorflow::FunctionDef* add_function();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::FunctionDef >&
      function() const;

  // repeated .opencv_tensorflow.GradientDef gradient = 2;
  int gradient_size() const;
  private:
  int _internal_gradient_size() const;
  public:
  void clear_gradient();
  ::opencv_tensorflow::GradientDef* mutable_gradient(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::GradientDef >*
      mutable_gradient();
  private:
  const ::opencv_tensorflow::GradientDef& _internal_gradient(int index) const;
  ::opencv_tensorflow::GradientDef* _internal_add_gradient();
  public:
  const ::opencv_tensorflow::GradientDef& gradient(int index) const;
  ::opencv_tensorflow::GradientDef* add_gradient();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::GradientDef >&
      gradient() const;

  // @@protoc_insertion_point(class_scope:opencv_tensorflow.FunctionDefLibrary)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::FunctionDef > function_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::GradientDef > gradient_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_function_2eproto;
};
// -------------------------------------------------------------------

class FunctionDef_Node_AttrEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_Node_AttrEntry_DoNotUse,
    std::string, ::opencv_tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_Node_AttrEntry_DoNotUse,
    std::string, ::opencv_tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  FunctionDef_Node_AttrEntry_DoNotUse();
  explicit constexpr FunctionDef_Node_AttrEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit FunctionDef_Node_AttrEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FunctionDef_Node_AttrEntry_DoNotUse& other);
  static const FunctionDef_Node_AttrEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FunctionDef_Node_AttrEntry_DoNotUse*>(&_FunctionDef_Node_AttrEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "opencv_tensorflow.FunctionDef.Node.AttrEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class FunctionDef_Node final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_tensorflow.FunctionDef.Node) */ {
 public:
  inline FunctionDef_Node() : FunctionDef_Node(nullptr) {}
  ~FunctionDef_Node() override;
  explicit constexpr FunctionDef_Node(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FunctionDef_Node(const FunctionDef_Node& from);
  FunctionDef_Node(FunctionDef_Node&& from) noexcept
    : FunctionDef_Node() {
    *this = ::std::move(from);
  }

  inline FunctionDef_Node& operator=(const FunctionDef_Node& from) {
    CopyFrom(from);
    return *this;
  }
  inline FunctionDef_Node& operator=(FunctionDef_Node&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FunctionDef_Node& default_instance() {
    return *internal_default_instance();
  }
  static inline const FunctionDef_Node* internal_default_instance() {
    return reinterpret_cast<const FunctionDef_Node*>(
               &_FunctionDef_Node_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(FunctionDef_Node& a, FunctionDef_Node& b) {
    a.Swap(&b);
  }
  inline void Swap(FunctionDef_Node* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FunctionDef_Node* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FunctionDef_Node* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FunctionDef_Node>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FunctionDef_Node& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FunctionDef_Node& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunctionDef_Node* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_tensorflow.FunctionDef.Node";
  }
  protected:
  explicit FunctionDef_Node(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kRetFieldNumber = 1,
    kArgFieldNumber = 3,
    kDepFieldNumber = 4,
    kAttrFieldNumber = 5,
    kOpFieldNumber = 2,
  };
  // repeated string ret = 1;
  int ret_size() const;
  private:
  int _internal_ret_size() const;
  public:
  void clear_ret();
  const std::string& ret(int index) const;
  std::string* mutable_ret(int index);
  void set_ret(int index, const std::string& value);
  void set_ret(int index, std::string&& value);
  void set_ret(int index, const char* value);
  void set_ret(int index, const char* value, size_t size);
  std::string* add_ret();
  void add_ret(const std::string& value);
  void add_ret(std::string&& value);
  void add_ret(const char* value);
  void add_ret(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& ret() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_ret();
  private:
  const std::string& _internal_ret(int index) const;
  std::string* _internal_add_ret();
  public:

  // repeated string arg = 3;
  int arg_size() const;
  private:
  int _internal_arg_size() const;
  public:
  void clear_arg();
  const std::string& arg(int index) const;
  std::string* mutable_arg(int index);
  void set_arg(int index, const std::string& value);
  void set_arg(int index, std::string&& value);
  void set_arg(int index, const char* value);
  void set_arg(int index, const char* value, size_t size);
  std::string* add_arg();
  void add_arg(const std::string& value);
  void add_arg(std::string&& value);
  void add_arg(const char* value);
  void add_arg(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& arg() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_arg();
  private:
  const std::string& _internal_arg(int index) const;
  std::string* _internal_add_arg();
  public:

  // repeated string dep = 4;
  int dep_size() const;
  private:
  int _internal_dep_size() const;
  public:
  void clear_dep();
  const std::string& dep(int index) const;
  std::string* mutable_dep(int index);
  void set_dep(int index, const std::string& value);
  void set_dep(int index, std::string&& value);
  void set_dep(int index, const char* value);
  void set_dep(int index, const char* value, size_t size);
  std::string* add_dep();
  void add_dep(const std::string& value);
  void add_dep(std::string&& value);
  void add_dep(const char* value);
  void add_dep(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& dep() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_dep();
  private:
  const std::string& _internal_dep(int index) const;
  std::string* _internal_add_dep();
  public:

  // map<string, .opencv_tensorflow.AttrValue> attr = 5;
  int attr_size() const;
  private:
  int _internal_attr_size() const;
  public:
  void clear_attr();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::opencv_tensorflow::AttrValue >&
      _internal_attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::opencv_tensorflow::AttrValue >*
      _internal_mutable_attr();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::opencv_tensorflow::AttrValue >&
      attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::opencv_tensorflow::AttrValue >*
      mutable_attr();

  // string op = 2;
  void clear_op();
  const std::string& op() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_op(ArgT0&& arg0, ArgT... args);
  std::string* mutable_op();
  PROTOBUF_NODISCARD std::string* release_op();
  void set_allocated_op(std::string* op);
  private:
  const std::string& _internal_op() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_op(const std::string& value);
  std::string* _internal_mutable_op();
  public:

  // @@protoc_insertion_point(class_scope:opencv_tensorflow.FunctionDef.Node)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> ret_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> arg_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> dep_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      FunctionDef_Node_AttrEntry_DoNotUse,
      std::string, ::opencv_tensorflow::AttrValue,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> attr_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_function_2eproto;
};
// -------------------------------------------------------------------

class FunctionDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_tensorflow.FunctionDef) */ {
 public:
  inline FunctionDef() : FunctionDef(nullptr) {}
  ~FunctionDef() override;
  explicit constexpr FunctionDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FunctionDef(const FunctionDef& from);
  FunctionDef(FunctionDef&& from) noexcept
    : FunctionDef() {
    *this = ::std::move(from);
  }

  inline FunctionDef& operator=(const FunctionDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline FunctionDef& operator=(FunctionDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FunctionDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const FunctionDef* internal_default_instance() {
    return reinterpret_cast<const FunctionDef*>(
               &_FunctionDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(FunctionDef& a, FunctionDef& b) {
    a.Swap(&b);
  }
  inline void Swap(FunctionDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FunctionDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FunctionDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FunctionDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FunctionDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FunctionDef& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunctionDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_tensorflow.FunctionDef";
  }
  protected:
  explicit FunctionDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef FunctionDef_Node Node;

  // accessors -------------------------------------------------------

  enum : int {
    kNodeFieldNumber = 2,
    kSignatureFieldNumber = 1,
  };
  // repeated .opencv_tensorflow.FunctionDef.Node node = 2;
  int node_size() const;
  private:
  int _internal_node_size() const;
  public:
  void clear_node();
  ::opencv_tensorflow::FunctionDef_Node* mutable_node(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::FunctionDef_Node >*
      mutable_node();
  private:
  const ::opencv_tensorflow::FunctionDef_Node& _internal_node(int index) const;
  ::opencv_tensorflow::FunctionDef_Node* _internal_add_node();
  public:
  const ::opencv_tensorflow::FunctionDef_Node& node(int index) const;
  ::opencv_tensorflow::FunctionDef_Node* add_node();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::FunctionDef_Node >&
      node() const;

  // .opencv_tensorflow.OpDef signature = 1;
  bool has_signature() const;
  private:
  bool _internal_has_signature() const;
  public:
  void clear_signature();
  const ::opencv_tensorflow::OpDef& signature() const;
  PROTOBUF_NODISCARD ::opencv_tensorflow::OpDef* release_signature();
  ::opencv_tensorflow::OpDef* mutable_signature();
  void set_allocated_signature(::opencv_tensorflow::OpDef* signature);
  private:
  const ::opencv_tensorflow::OpDef& _internal_signature() const;
  ::opencv_tensorflow::OpDef* _internal_mutable_signature();
  public:
  void unsafe_arena_set_allocated_signature(
      ::opencv_tensorflow::OpDef* signature);
  ::opencv_tensorflow::OpDef* unsafe_arena_release_signature();

  // @@protoc_insertion_point(class_scope:opencv_tensorflow.FunctionDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::FunctionDef_Node > node_;
  ::opencv_tensorflow::OpDef* signature_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_function_2eproto;
};
// -------------------------------------------------------------------

class GradientDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:opencv_tensorflow.GradientDef) */ {
 public:
  inline GradientDef() : GradientDef(nullptr) {}
  ~GradientDef() override;
  explicit constexpr GradientDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GradientDef(const GradientDef& from);
  GradientDef(GradientDef&& from) noexcept
    : GradientDef() {
    *this = ::std::move(from);
  }

  inline GradientDef& operator=(const GradientDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline GradientDef& operator=(GradientDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GradientDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const GradientDef* internal_default_instance() {
    return reinterpret_cast<const GradientDef*>(
               &_GradientDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GradientDef& a, GradientDef& b) {
    a.Swap(&b);
  }
  inline void Swap(GradientDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GradientDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GradientDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GradientDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GradientDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GradientDef& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GradientDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "opencv_tensorflow.GradientDef";
  }
  protected:
  explicit GradientDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFunctionNameFieldNumber = 1,
    kGradientFuncFieldNumber = 2,
  };
  // string function_name = 1;
  void clear_function_name();
  const std::string& function_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_function_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_function_name();
  PROTOBUF_NODISCARD std::string* release_function_name();
  void set_allocated_function_name(std::string* function_name);
  private:
  const std::string& _internal_function_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_function_name(const std::string& value);
  std::string* _internal_mutable_function_name();
  public:

  // string gradient_func = 2;
  void clear_gradient_func();
  const std::string& gradient_func() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_gradient_func(ArgT0&& arg0, ArgT... args);
  std::string* mutable_gradient_func();
  PROTOBUF_NODISCARD std::string* release_gradient_func();
  void set_allocated_gradient_func(std::string* gradient_func);
  private:
  const std::string& _internal_gradient_func() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_gradient_func(const std::string& value);
  std::string* _internal_mutable_gradient_func();
  public:

  // @@protoc_insertion_point(class_scope:opencv_tensorflow.GradientDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr function_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr gradient_func_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_function_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// FunctionDefLibrary

// repeated .opencv_tensorflow.FunctionDef function = 1;
inline int FunctionDefLibrary::_internal_function_size() const {
  return function_.size();
}
inline int FunctionDefLibrary::function_size() const {
  return _internal_function_size();
}
inline void FunctionDefLibrary::clear_function() {
  function_.Clear();
}
inline ::opencv_tensorflow::FunctionDef* FunctionDefLibrary::mutable_function(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.FunctionDefLibrary.function)
  return function_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::FunctionDef >*
FunctionDefLibrary::mutable_function() {
  // @@protoc_insertion_point(field_mutable_list:opencv_tensorflow.FunctionDefLibrary.function)
  return &function_;
}
inline const ::opencv_tensorflow::FunctionDef& FunctionDefLibrary::_internal_function(int index) const {
  return function_.Get(index);
}
inline const ::opencv_tensorflow::FunctionDef& FunctionDefLibrary::function(int index) const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.FunctionDefLibrary.function)
  return _internal_function(index);
}
inline ::opencv_tensorflow::FunctionDef* FunctionDefLibrary::_internal_add_function() {
  return function_.Add();
}
inline ::opencv_tensorflow::FunctionDef* FunctionDefLibrary::add_function() {
  ::opencv_tensorflow::FunctionDef* _add = _internal_add_function();
  // @@protoc_insertion_point(field_add:opencv_tensorflow.FunctionDefLibrary.function)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::FunctionDef >&
FunctionDefLibrary::function() const {
  // @@protoc_insertion_point(field_list:opencv_tensorflow.FunctionDefLibrary.function)
  return function_;
}

// repeated .opencv_tensorflow.GradientDef gradient = 2;
inline int FunctionDefLibrary::_internal_gradient_size() const {
  return gradient_.size();
}
inline int FunctionDefLibrary::gradient_size() const {
  return _internal_gradient_size();
}
inline void FunctionDefLibrary::clear_gradient() {
  gradient_.Clear();
}
inline ::opencv_tensorflow::GradientDef* FunctionDefLibrary::mutable_gradient(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.FunctionDefLibrary.gradient)
  return gradient_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::GradientDef >*
FunctionDefLibrary::mutable_gradient() {
  // @@protoc_insertion_point(field_mutable_list:opencv_tensorflow.FunctionDefLibrary.gradient)
  return &gradient_;
}
inline const ::opencv_tensorflow::GradientDef& FunctionDefLibrary::_internal_gradient(int index) const {
  return gradient_.Get(index);
}
inline const ::opencv_tensorflow::GradientDef& FunctionDefLibrary::gradient(int index) const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.FunctionDefLibrary.gradient)
  return _internal_gradient(index);
}
inline ::opencv_tensorflow::GradientDef* FunctionDefLibrary::_internal_add_gradient() {
  return gradient_.Add();
}
inline ::opencv_tensorflow::GradientDef* FunctionDefLibrary::add_gradient() {
  ::opencv_tensorflow::GradientDef* _add = _internal_add_gradient();
  // @@protoc_insertion_point(field_add:opencv_tensorflow.FunctionDefLibrary.gradient)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::GradientDef >&
FunctionDefLibrary::gradient() const {
  // @@protoc_insertion_point(field_list:opencv_tensorflow.FunctionDefLibrary.gradient)
  return gradient_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// FunctionDef_Node

// repeated string ret = 1;
inline int FunctionDef_Node::_internal_ret_size() const {
  return ret_.size();
}
inline int FunctionDef_Node::ret_size() const {
  return _internal_ret_size();
}
inline void FunctionDef_Node::clear_ret() {
  ret_.Clear();
}
inline std::string* FunctionDef_Node::add_ret() {
  std::string* _s = _internal_add_ret();
  // @@protoc_insertion_point(field_add_mutable:opencv_tensorflow.FunctionDef.Node.ret)
  return _s;
}
inline const std::string& FunctionDef_Node::_internal_ret(int index) const {
  return ret_.Get(index);
}
inline const std::string& FunctionDef_Node::ret(int index) const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.FunctionDef.Node.ret)
  return _internal_ret(index);
}
inline std::string* FunctionDef_Node::mutable_ret(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.FunctionDef.Node.ret)
  return ret_.Mutable(index);
}
inline void FunctionDef_Node::set_ret(int index, const std::string& value) {
  ret_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:opencv_tensorflow.FunctionDef.Node.ret)
}
inline void FunctionDef_Node::set_ret(int index, std::string&& value) {
  ret_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:opencv_tensorflow.FunctionDef.Node.ret)
}
inline void FunctionDef_Node::set_ret(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  ret_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:opencv_tensorflow.FunctionDef.Node.ret)
}
inline void FunctionDef_Node::set_ret(int index, const char* value, size_t size) {
  ret_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:opencv_tensorflow.FunctionDef.Node.ret)
}
inline std::string* FunctionDef_Node::_internal_add_ret() {
  return ret_.Add();
}
inline void FunctionDef_Node::add_ret(const std::string& value) {
  ret_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:opencv_tensorflow.FunctionDef.Node.ret)
}
inline void FunctionDef_Node::add_ret(std::string&& value) {
  ret_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:opencv_tensorflow.FunctionDef.Node.ret)
}
inline void FunctionDef_Node::add_ret(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  ret_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:opencv_tensorflow.FunctionDef.Node.ret)
}
inline void FunctionDef_Node::add_ret(const char* value, size_t size) {
  ret_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:opencv_tensorflow.FunctionDef.Node.ret)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
FunctionDef_Node::ret() const {
  // @@protoc_insertion_point(field_list:opencv_tensorflow.FunctionDef.Node.ret)
  return ret_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
FunctionDef_Node::mutable_ret() {
  // @@protoc_insertion_point(field_mutable_list:opencv_tensorflow.FunctionDef.Node.ret)
  return &ret_;
}

// string op = 2;
inline void FunctionDef_Node::clear_op() {
  op_.ClearToEmpty();
}
inline const std::string& FunctionDef_Node::op() const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.FunctionDef.Node.op)
  return _internal_op();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FunctionDef_Node::set_op(ArgT0&& arg0, ArgT... args) {

 op_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_tensorflow.FunctionDef.Node.op)
}
inline std::string* FunctionDef_Node::mutable_op() {
  std::string* _s = _internal_mutable_op();
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.FunctionDef.Node.op)
  return _s;
}
inline const std::string& FunctionDef_Node::_internal_op() const {
  return op_.Get();
}
inline void FunctionDef_Node::_internal_set_op(const std::string& value) {

  op_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FunctionDef_Node::_internal_mutable_op() {

  return op_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FunctionDef_Node::release_op() {
  // @@protoc_insertion_point(field_release:opencv_tensorflow.FunctionDef.Node.op)
  return op_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FunctionDef_Node::set_allocated_op(std::string* op) {
  if (op != nullptr) {

  } else {

  }
  op_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), op,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (op_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    op_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_tensorflow.FunctionDef.Node.op)
}

// repeated string arg = 3;
inline int FunctionDef_Node::_internal_arg_size() const {
  return arg_.size();
}
inline int FunctionDef_Node::arg_size() const {
  return _internal_arg_size();
}
inline void FunctionDef_Node::clear_arg() {
  arg_.Clear();
}
inline std::string* FunctionDef_Node::add_arg() {
  std::string* _s = _internal_add_arg();
  // @@protoc_insertion_point(field_add_mutable:opencv_tensorflow.FunctionDef.Node.arg)
  return _s;
}
inline const std::string& FunctionDef_Node::_internal_arg(int index) const {
  return arg_.Get(index);
}
inline const std::string& FunctionDef_Node::arg(int index) const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.FunctionDef.Node.arg)
  return _internal_arg(index);
}
inline std::string* FunctionDef_Node::mutable_arg(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.FunctionDef.Node.arg)
  return arg_.Mutable(index);
}
inline void FunctionDef_Node::set_arg(int index, const std::string& value) {
  arg_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:opencv_tensorflow.FunctionDef.Node.arg)
}
inline void FunctionDef_Node::set_arg(int index, std::string&& value) {
  arg_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:opencv_tensorflow.FunctionDef.Node.arg)
}
inline void FunctionDef_Node::set_arg(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  arg_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:opencv_tensorflow.FunctionDef.Node.arg)
}
inline void FunctionDef_Node::set_arg(int index, const char* value, size_t size) {
  arg_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:opencv_tensorflow.FunctionDef.Node.arg)
}
inline std::string* FunctionDef_Node::_internal_add_arg() {
  return arg_.Add();
}
inline void FunctionDef_Node::add_arg(const std::string& value) {
  arg_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:opencv_tensorflow.FunctionDef.Node.arg)
}
inline void FunctionDef_Node::add_arg(std::string&& value) {
  arg_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:opencv_tensorflow.FunctionDef.Node.arg)
}
inline void FunctionDef_Node::add_arg(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  arg_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:opencv_tensorflow.FunctionDef.Node.arg)
}
inline void FunctionDef_Node::add_arg(const char* value, size_t size) {
  arg_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:opencv_tensorflow.FunctionDef.Node.arg)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
FunctionDef_Node::arg() const {
  // @@protoc_insertion_point(field_list:opencv_tensorflow.FunctionDef.Node.arg)
  return arg_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
FunctionDef_Node::mutable_arg() {
  // @@protoc_insertion_point(field_mutable_list:opencv_tensorflow.FunctionDef.Node.arg)
  return &arg_;
}

// repeated string dep = 4;
inline int FunctionDef_Node::_internal_dep_size() const {
  return dep_.size();
}
inline int FunctionDef_Node::dep_size() const {
  return _internal_dep_size();
}
inline void FunctionDef_Node::clear_dep() {
  dep_.Clear();
}
inline std::string* FunctionDef_Node::add_dep() {
  std::string* _s = _internal_add_dep();
  // @@protoc_insertion_point(field_add_mutable:opencv_tensorflow.FunctionDef.Node.dep)
  return _s;
}
inline const std::string& FunctionDef_Node::_internal_dep(int index) const {
  return dep_.Get(index);
}
inline const std::string& FunctionDef_Node::dep(int index) const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.FunctionDef.Node.dep)
  return _internal_dep(index);
}
inline std::string* FunctionDef_Node::mutable_dep(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.FunctionDef.Node.dep)
  return dep_.Mutable(index);
}
inline void FunctionDef_Node::set_dep(int index, const std::string& value) {
  dep_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:opencv_tensorflow.FunctionDef.Node.dep)
}
inline void FunctionDef_Node::set_dep(int index, std::string&& value) {
  dep_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:opencv_tensorflow.FunctionDef.Node.dep)
}
inline void FunctionDef_Node::set_dep(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  dep_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:opencv_tensorflow.FunctionDef.Node.dep)
}
inline void FunctionDef_Node::set_dep(int index, const char* value, size_t size) {
  dep_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:opencv_tensorflow.FunctionDef.Node.dep)
}
inline std::string* FunctionDef_Node::_internal_add_dep() {
  return dep_.Add();
}
inline void FunctionDef_Node::add_dep(const std::string& value) {
  dep_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:opencv_tensorflow.FunctionDef.Node.dep)
}
inline void FunctionDef_Node::add_dep(std::string&& value) {
  dep_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:opencv_tensorflow.FunctionDef.Node.dep)
}
inline void FunctionDef_Node::add_dep(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  dep_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:opencv_tensorflow.FunctionDef.Node.dep)
}
inline void FunctionDef_Node::add_dep(const char* value, size_t size) {
  dep_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:opencv_tensorflow.FunctionDef.Node.dep)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
FunctionDef_Node::dep() const {
  // @@protoc_insertion_point(field_list:opencv_tensorflow.FunctionDef.Node.dep)
  return dep_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
FunctionDef_Node::mutable_dep() {
  // @@protoc_insertion_point(field_mutable_list:opencv_tensorflow.FunctionDef.Node.dep)
  return &dep_;
}

// map<string, .opencv_tensorflow.AttrValue> attr = 5;
inline int FunctionDef_Node::_internal_attr_size() const {
  return attr_.size();
}
inline int FunctionDef_Node::attr_size() const {
  return _internal_attr_size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::opencv_tensorflow::AttrValue >&
FunctionDef_Node::_internal_attr() const {
  return attr_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::opencv_tensorflow::AttrValue >&
FunctionDef_Node::attr() const {
  // @@protoc_insertion_point(field_map:opencv_tensorflow.FunctionDef.Node.attr)
  return _internal_attr();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::opencv_tensorflow::AttrValue >*
FunctionDef_Node::_internal_mutable_attr() {
  return attr_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::opencv_tensorflow::AttrValue >*
FunctionDef_Node::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_map:opencv_tensorflow.FunctionDef.Node.attr)
  return _internal_mutable_attr();
}

// -------------------------------------------------------------------

// FunctionDef

// .opencv_tensorflow.OpDef signature = 1;
inline bool FunctionDef::_internal_has_signature() const {
  return this != internal_default_instance() && signature_ != nullptr;
}
inline bool FunctionDef::has_signature() const {
  return _internal_has_signature();
}
inline const ::opencv_tensorflow::OpDef& FunctionDef::_internal_signature() const {
  const ::opencv_tensorflow::OpDef* p = signature_;
  return p != nullptr ? *p : reinterpret_cast<const ::opencv_tensorflow::OpDef&>(
      ::opencv_tensorflow::_OpDef_default_instance_);
}
inline const ::opencv_tensorflow::OpDef& FunctionDef::signature() const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.FunctionDef.signature)
  return _internal_signature();
}
inline void FunctionDef::unsafe_arena_set_allocated_signature(
    ::opencv_tensorflow::OpDef* signature) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(signature_);
  }
  signature_ = signature;
  if (signature) {

  } else {

  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:opencv_tensorflow.FunctionDef.signature)
}
inline ::opencv_tensorflow::OpDef* FunctionDef::release_signature() {

  ::opencv_tensorflow::OpDef* temp = signature_;
  signature_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::opencv_tensorflow::OpDef* FunctionDef::unsafe_arena_release_signature() {
  // @@protoc_insertion_point(field_release:opencv_tensorflow.FunctionDef.signature)

  ::opencv_tensorflow::OpDef* temp = signature_;
  signature_ = nullptr;
  return temp;
}
inline ::opencv_tensorflow::OpDef* FunctionDef::_internal_mutable_signature() {

  if (signature_ == nullptr) {
    auto* p = CreateMaybeMessage<::opencv_tensorflow::OpDef>(GetArenaForAllocation());
    signature_ = p;
  }
  return signature_;
}
inline ::opencv_tensorflow::OpDef* FunctionDef::mutable_signature() {
  ::opencv_tensorflow::OpDef* _msg = _internal_mutable_signature();
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.FunctionDef.signature)
  return _msg;
}
inline void FunctionDef::set_allocated_signature(::opencv_tensorflow::OpDef* signature) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(signature_);
  }
  if (signature) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(signature));
    if (message_arena != submessage_arena) {
      signature = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, signature, submessage_arena);
    }

  } else {

  }
  signature_ = signature;
  // @@protoc_insertion_point(field_set_allocated:opencv_tensorflow.FunctionDef.signature)
}

// repeated .opencv_tensorflow.FunctionDef.Node node = 2;
inline int FunctionDef::_internal_node_size() const {
  return node_.size();
}
inline int FunctionDef::node_size() const {
  return _internal_node_size();
}
inline void FunctionDef::clear_node() {
  node_.Clear();
}
inline ::opencv_tensorflow::FunctionDef_Node* FunctionDef::mutable_node(int index) {
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.FunctionDef.node)
  return node_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::FunctionDef_Node >*
FunctionDef::mutable_node() {
  // @@protoc_insertion_point(field_mutable_list:opencv_tensorflow.FunctionDef.node)
  return &node_;
}
inline const ::opencv_tensorflow::FunctionDef_Node& FunctionDef::_internal_node(int index) const {
  return node_.Get(index);
}
inline const ::opencv_tensorflow::FunctionDef_Node& FunctionDef::node(int index) const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.FunctionDef.node)
  return _internal_node(index);
}
inline ::opencv_tensorflow::FunctionDef_Node* FunctionDef::_internal_add_node() {
  return node_.Add();
}
inline ::opencv_tensorflow::FunctionDef_Node* FunctionDef::add_node() {
  ::opencv_tensorflow::FunctionDef_Node* _add = _internal_add_node();
  // @@protoc_insertion_point(field_add:opencv_tensorflow.FunctionDef.node)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::opencv_tensorflow::FunctionDef_Node >&
FunctionDef::node() const {
  // @@protoc_insertion_point(field_list:opencv_tensorflow.FunctionDef.node)
  return node_;
}

// -------------------------------------------------------------------

// GradientDef

// string function_name = 1;
inline void GradientDef::clear_function_name() {
  function_name_.ClearToEmpty();
}
inline const std::string& GradientDef::function_name() const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.GradientDef.function_name)
  return _internal_function_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GradientDef::set_function_name(ArgT0&& arg0, ArgT... args) {

 function_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_tensorflow.GradientDef.function_name)
}
inline std::string* GradientDef::mutable_function_name() {
  std::string* _s = _internal_mutable_function_name();
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.GradientDef.function_name)
  return _s;
}
inline const std::string& GradientDef::_internal_function_name() const {
  return function_name_.Get();
}
inline void GradientDef::_internal_set_function_name(const std::string& value) {

  function_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GradientDef::_internal_mutable_function_name() {

  return function_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GradientDef::release_function_name() {
  // @@protoc_insertion_point(field_release:opencv_tensorflow.GradientDef.function_name)
  return function_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GradientDef::set_allocated_function_name(std::string* function_name) {
  if (function_name != nullptr) {

  } else {

  }
  function_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), function_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (function_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    function_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_tensorflow.GradientDef.function_name)
}

// string gradient_func = 2;
inline void GradientDef::clear_gradient_func() {
  gradient_func_.ClearToEmpty();
}
inline const std::string& GradientDef::gradient_func() const {
  // @@protoc_insertion_point(field_get:opencv_tensorflow.GradientDef.gradient_func)
  return _internal_gradient_func();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GradientDef::set_gradient_func(ArgT0&& arg0, ArgT... args) {

 gradient_func_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:opencv_tensorflow.GradientDef.gradient_func)
}
inline std::string* GradientDef::mutable_gradient_func() {
  std::string* _s = _internal_mutable_gradient_func();
  // @@protoc_insertion_point(field_mutable:opencv_tensorflow.GradientDef.gradient_func)
  return _s;
}
inline const std::string& GradientDef::_internal_gradient_func() const {
  return gradient_func_.Get();
}
inline void GradientDef::_internal_set_gradient_func(const std::string& value) {

  gradient_func_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GradientDef::_internal_mutable_gradient_func() {

  return gradient_func_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GradientDef::release_gradient_func() {
  // @@protoc_insertion_point(field_release:opencv_tensorflow.GradientDef.gradient_func)
  return gradient_func_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GradientDef::set_allocated_gradient_func(std::string* gradient_func) {
  if (gradient_func != nullptr) {

  } else {

  }
  gradient_func_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), gradient_func,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (gradient_func_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    gradient_func_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:opencv_tensorflow.GradientDef.gradient_func)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace opencv_tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_function_2eproto
