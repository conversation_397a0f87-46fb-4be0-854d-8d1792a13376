var searchData=
[
  ['name_0',['name',['../monitor_guide.html#monitor_name',1,'Human-readable name'],['../input_guide.html#joystick_name',1,'Joystick name']]],
  ['name_20change_20tables_1',['Name change tables',['../moving_guide.html#moving_tables',1,'']]],
  ['names_2',['Key names',['../input_guide.html#input_key_name',1,'']]],
  ['native_20access_3',['Native access',['../group__native.html',1,'']]],
  ['native_20access_20function_4',['Cocoa NSView native access function',['../news.html#cocoa_nsview_function',1,'']]],
  ['native_20access_20functions_5',['Multiple sets of native access functions',['../news.html#multiplatform_caveat',1,'']]],
  ['native_20interface_6',['Native interface',['../internals_guide.html#internals_native',1,'']]],
  ['new_20constants_7',['New constants',['../news.html#new_constants',1,'']]],
  ['new_20features_8',['New features',['../news.html#features',1,'']]],
  ['new_20functions_9',['New functions',['../news.html#new_functions',1,'']]],
  ['new_20symbols_10',['New symbols',['../news.html#new_symbols',1,'']]],
  ['new_20types_11',['New types',['../news.html#new_types',1,'']]],
  ['news_2emd_12',['news.md',['../news_8md.html',1,'']]],
  ['no_20longer_20generated_13',['Configuration header is no longer generated',['../news.html#config_header_caveat',1,'']]],
  ['no_20longer_20round_20trip_20to_20server_14',['X11 empty events no longer round-trip to server',['../news.html#x11_emptyevent_caveat',1,'']]],
  ['notes_20for_20earlier_20versions_15',['Release notes for earlier versions',['../news.html#news_archive',1,'']]],
  ['notes_20for_20version_203_204_16',['Release notes for version 3.4',['../news.html',1,'']]],
  ['now_20created_20at_20initialization_17',['macOS main menu now created at initialization',['../news.html#macos_menu_caveat',1,'']]],
  ['nsview_20native_20access_20function_18',['Cocoa NSView native access function',['../news.html#cocoa_nsview_function',1,'']]]
];
