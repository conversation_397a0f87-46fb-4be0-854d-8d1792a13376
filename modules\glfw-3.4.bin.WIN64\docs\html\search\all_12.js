var searchData=
[
  ['lack_20alpha_20channel_20on_20older_20systems_0',['Wayland framebuffer may lack alpha channel on older systems',['../news.html#wayland_alpha_caveat',1,'']]],
  ['later_1',['Documentation generation requires Doxygen 1.9.8 or later',['../news.html#docs_target_caveat',1,'']]],
  ['leave_20events_2',['Cursor enter/leave events',['../input_guide.html#cursor_enter',1,'']]],
  ['libdecor_20decorations_3',['Wayland libdecor decorations',['../news.html#wayland_libdecor_decorations',1,'']]],
  ['libraries_4',['Link with the right libraries',['../build_guide.html#build_link',1,'']]],
  ['library_5',['library',['../compile_guide.html#compile_compile',1,'Compiling the library'],['../context_guide.html#context_glext_auto',1,'Loading extension with a loader library']]],
  ['library_20and_20header_20file_6',['Renamed library and header file',['../moving_guide.html#moving_renamed_files',1,'']]],
  ['lifetimes_7',['Pointer lifetimes',['../intro_guide.html#lifetime',1,'']]],
  ['like_20system_20specific_20cmake_20options_8',['Unix-like system specific CMake options',['../compile_guide.html#compile_options_unix',1,'']]],
  ['limitations_9',['Guarantees and limitations',['../intro_guide.html#guarantees_limitations',1,'']]],
  ['limits_10',['Window size limits',['../window_guide.html#window_sizelimits',1,'']]],
  ['line_20cmake_11',['Generating with command-line CMake',['../compile_guide.html#compile_generate_cli',1,'']]],
  ['line_20or_20makefile_20on_20macos_12',['With command-line or makefile on macOS',['../build_guide.html#build_link_osx',1,'']]],
  ['link_20with_20the_20right_20libraries_13',['Link with the right libraries',['../build_guide.html#build_link',1,'']]],
  ['list_14',['Deprecated List',['../deprecated.html',1,'']]],
  ['loader_15',['Finding the Vulkan loader',['../vulkan_guide.html#vulkan_loader',1,'']]],
  ['loader_20and_20api_16',['Vulkan loader and API',['../compat_guide.html#compat_vulkan',1,'']]],
  ['loader_20library_17',['Loading extension with a loader library',['../context_guide.html#context_glext_auto',1,'']]],
  ['loading_18',['Removal of image and texture loading',['../moving_guide.html#moving_image',1,'']]],
  ['loading_20extension_20with_20a_20loader_20library_19',['Loading extension with a loader library',['../context_guide.html#context_glext_auto',1,'']]],
  ['loading_20extensions_20manually_20',['Loading extensions manually',['../context_guide.html#context_glext_manual',1,'']]],
  ['longer_20generated_21',['Configuration header is no longer generated',['../news.html#config_header_caveat',1,'']]],
  ['longer_20round_20trip_20to_20server_22',['X11 empty events no longer round-trip to server',['../news.html#x11_emptyevent_caveat',1,'']]]
];
