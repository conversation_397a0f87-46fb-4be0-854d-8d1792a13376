PORTED FUNCs LIST (23 of 23):

 void cv::img_hash::averageHash(Mat inputArr, Mat& outputArr)
 void cv::img_hash::blockMeanHash(Mat inputArr, Mat& outputArr, int mode = BLOCK_MEAN_HASH_MODE_0)
 void cv::img_hash::colorMomentHash(Mat inputArr, Mat& outputArr)
 void cv::img_hash::marrHildrethHash(Mat inputArr, Mat& outputArr, float alpha = 2.0f, float scale = 1.0f)
 void cv::img_hash::pHash(Mat inputArr, Mat& outputArr)
 void cv::img_hash::radialVarianceHash(Mat inputArr, Mat& outputArr, double sigma = 1, int numOfAngleLine = 180)
static Ptr_AverageHash cv::img_hash::AverageHash::create()
 void cv::img_hash::BlockMeanHash::setMode(BlockMeanHashMode mode)
 vector_double cv::img_hash::BlockMeanHash::getMean()
static Ptr_BlockMeanHash cv::img_hash::BlockMeanHash::create(BlockMeanHashMode mode = BLOCK_MEAN_HASH_MODE_0)
static Ptr_ColorMomentHash cv::img_hash::ColorMomentHash::create()
 void cv::img_hash::ImgHashBase::compute(Mat inputArr, Mat& outputArr)
 double cv::img_hash::ImgHashBase::compare(Mat hashOne, Mat hashTwo)
 float cv::img_hash::MarrHildrethHash::getAlpha()
 float cv::img_hash::MarrHildrethHash::getScale()
 void cv::img_hash::MarrHildrethHash::setKernelParam(float alpha, float scale)
static Ptr_MarrHildrethHash cv::img_hash::MarrHildrethHash::create(float alpha = 2.0f, float scale = 1.0f)
static Ptr_PHash cv::img_hash::PHash::create()
static Ptr_RadialVarianceHash cv::img_hash::RadialVarianceHash::create(double sigma = 1, int numOfAngleLine = 180)
 int cv::img_hash::RadialVarianceHash::getNumOfAngleLine()
 double cv::img_hash::RadialVarianceHash::getSigma()
 void cv::img_hash::RadialVarianceHash::setNumOfAngleLine(int value)
 void cv::img_hash::RadialVarianceHash::setSigma(double value)

SKIPPED FUNCs LIST (0 of 23):


0 def args - 17 funcs
1 def args - 2 funcs
2 def args - 4 funcs