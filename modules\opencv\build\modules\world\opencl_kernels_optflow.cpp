// This file is auto-generated. Do not edit!

#include "opencv2/core.hpp"
#include "cvconfig.h"
#include "opencl_kernels_optflow.hpp"

#ifdef HAVE_OPENCL

namespace cv
{
namespace ocl
{
namespace optflow
{

static const char* const moduleName = "optflow";

struct cv::ocl::internal::ProgramEntry optical_flow_tvl1_oclsrc={moduleName, "optical_flow_tvl1",
"__kernel void centeredGradientKernel(__global const float* src_ptr, int src_col, int src_row, int src_step,\n"
"__global float* dx, __global float* dy, int d_step)\n"
"{\n"
"int x = get_global_id(0);\n"
"int y = get_global_id(1);\n"
"if((x < src_col)&&(y < src_row))\n"
"{\n"
"int src_x1 = (x + 1) < (src_col -1)? (x + 1) : (src_col - 1);\n"
"int src_x2 = (x - 1) > 0 ? (x -1) : 0;\n"
"dx[y * d_step+ x] = 0.5f * (src_ptr[y * src_step + src_x1] - src_ptr[y * src_step+ src_x2]);\n"
"int src_y1 = (y+1) < (src_row - 1) ? (y + 1) : (src_row - 1);\n"
"int src_y2 = (y - 1) > 0 ? (y - 1) : 0;\n"
"dy[y * d_step+ x] = 0.5f * (src_ptr[src_y1 * src_step + x] - src_ptr[src_y2 * src_step+ x]);\n"
"}\n"
"}\n"
"inline float bicubicCoeff(float x_)\n"
"{\n"
"float x = fabs(x_);\n"
"if (x <= 1.0f)\n"
"return x * x * (1.5f * x - 2.5f) + 1.0f;\n"
"else if (x < 2.0f)\n"
"return x * (x * (-0.5f * x + 2.5f) - 4.0f) + 2.0f;\n"
"else\n"
"return 0.0f;\n"
"}\n"
"__kernel void warpBackwardKernel(__global const float* I0, int I0_step, int I0_col, int I0_row,\n"
"image2d_t tex_I1, image2d_t tex_I1x, image2d_t tex_I1y,\n"
"__global const float* u1, int u1_step,\n"
"__global const float* u2,\n"
"__global float* I1w,\n"
"__global float* I1wx, \n"
"__global float* I1wy, \n"
"__global float* grad, \n"
"__global float* rho,\n"
"int I1w_step,\n"
"int u2_step,\n"
"int u1_offset_x,\n"
"int u1_offset_y,\n"
"int u2_offset_x,\n"
"int u2_offset_y)\n"
"{\n"
"int x = get_global_id(0);\n"
"int y = get_global_id(1);\n"
"if(x < I0_col&&y < I0_row)\n"
"{\n"
"float u1Val = u1[(y + u1_offset_y) * u1_step + x + u1_offset_x];\n"
"float u2Val = u2[(y + u2_offset_y) * u2_step + x + u2_offset_x];\n"
"float wx = x + u1Val;\n"
"float wy = y + u2Val;\n"
"int xmin = ceil(wx - 2.0f);\n"
"int xmax = floor(wx + 2.0f);\n"
"int ymin = ceil(wy - 2.0f);\n"
"int ymax = floor(wy + 2.0f);\n"
"float sum  = 0.0f;\n"
"float sumx = 0.0f;\n"
"float sumy = 0.0f;\n"
"float wsum = 0.0f;\n"
"sampler_t sampleri = CLK_NORMALIZED_COORDS_FALSE | CLK_ADDRESS_CLAMP_TO_EDGE | CLK_FILTER_NEAREST;\n"
"for (int cy = ymin; cy <= ymax; ++cy)\n"
"{\n"
"for (int cx = xmin; cx <= xmax; ++cx)\n"
"{\n"
"float w = bicubicCoeff(wx - cx) * bicubicCoeff(wy - cy);\n"
"int2 cood = (int2)(cx, cy);\n"
"sum += w * read_imagef(tex_I1, sampleri, cood).x;\n"
"sumx += w * read_imagef(tex_I1x, sampleri, cood).x;\n"
"sumy += w * read_imagef(tex_I1y, sampleri, cood).x;\n"
"wsum += w;\n"
"}\n"
"}\n"
"float coeff = 1.0f / wsum;\n"
"float I1wVal  = sum  * coeff;\n"
"float I1wxVal = sumx * coeff;\n"
"float I1wyVal = sumy * coeff;\n"
"I1w[y * I1w_step + x]  = I1wVal;\n"
"I1wx[y * I1w_step + x] = I1wxVal;\n"
"I1wy[y * I1w_step + x] = I1wyVal;\n"
"float Ix2 = I1wxVal * I1wxVal;\n"
"float Iy2 = I1wyVal * I1wyVal;\n"
"grad[y * I1w_step + x] = Ix2 + Iy2;\n"
"float I0Val = I0[y * I0_step + x];\n"
"rho[y * I1w_step + x] = I1wVal - I1wxVal * u1Val - I1wyVal * u2Val - I0Val;\n"
"}\n"
"}\n"
"inline float readImage(__global const float *image,  int x,  int y,  int rows,  int cols, int elemCntPerRow)\n"
"{\n"
"int i0 = clamp(x, 0, cols - 1);\n"
"int j0 = clamp(y, 0, rows - 1);\n"
"return image[j0 * elemCntPerRow + i0];\n"
"}\n"
"__kernel void warpBackwardKernelNoImage2d(__global const float* I0, int I0_step, int I0_col, int I0_row,\n"
"__global const float* tex_I1, __global const float* tex_I1x, __global const float* tex_I1y,\n"
"__global const float* u1, int u1_step,\n"
"__global const float* u2,\n"
"__global float* I1w,\n"
"__global float* I1wx, \n"
"__global float* I1wy, \n"
"__global float* grad, \n"
"__global float* rho,\n"
"int I1w_step,\n"
"int u2_step,\n"
"int I1_step,\n"
"int I1x_step)\n"
"{\n"
"int x = get_global_id(0);\n"
"int y = get_global_id(1);\n"
"if(x < I0_col&&y < I0_row)\n"
"{\n"
"float u1Val = u1[y * u1_step + x];\n"
"float u2Val = u2[y * u2_step + x];\n"
"float wx = x + u1Val;\n"
"float wy = y + u2Val;\n"
"int xmin = ceil(wx - 2.0f);\n"
"int xmax = floor(wx + 2.0f);\n"
"int ymin = ceil(wy - 2.0f);\n"
"int ymax = floor(wy + 2.0f);\n"
"float sum  = 0.0f;\n"
"float sumx = 0.0f;\n"
"float sumy = 0.0f;\n"
"float wsum = 0.0f;\n"
"for (int cy = ymin; cy <= ymax; ++cy)\n"
"{\n"
"for (int cx = xmin; cx <= xmax; ++cx)\n"
"{\n"
"float w = bicubicCoeff(wx - cx) * bicubicCoeff(wy - cy);\n"
"int2 cood = (int2)(cx, cy);\n"
"sum += w * readImage(tex_I1, cood.x, cood.y, I0_col, I0_row, I1_step);\n"
"sumx += w * readImage(tex_I1x, cood.x, cood.y, I0_col, I0_row, I1x_step);\n"
"sumy += w * readImage(tex_I1y, cood.x, cood.y, I0_col, I0_row, I1x_step);\n"
"wsum += w;\n"
"}\n"
"}\n"
"float coeff = 1.0f / wsum;\n"
"float I1wVal  = sum  * coeff;\n"
"float I1wxVal = sumx * coeff;\n"
"float I1wyVal = sumy * coeff;\n"
"I1w[y * I1w_step + x]  = I1wVal;\n"
"I1wx[y * I1w_step + x] = I1wxVal;\n"
"I1wy[y * I1w_step + x] = I1wyVal;\n"
"float Ix2 = I1wxVal * I1wxVal;\n"
"float Iy2 = I1wyVal * I1wyVal;\n"
"grad[y * I1w_step + x] = Ix2 + Iy2;\n"
"float I0Val = I0[y * I0_step + x];\n"
"rho[y * I1w_step + x] = I1wVal - I1wxVal * u1Val - I1wyVal * u2Val - I0Val;\n"
"}\n"
"}\n"
"__kernel void estimateDualVariablesKernel(__global const float* u1, int u1_col, int u1_row, int u1_step,\n"
"__global const float* u2,\n"
"__global float* p11, int p11_step,\n"
"__global float* p12,\n"
"__global float* p21,\n"
"__global float* p22,\n"
"float taut,\n"
"int u2_step,\n"
"int u1_offset_x,\n"
"int u1_offset_y,\n"
"int u2_offset_x,\n"
"int u2_offset_y)\n"
"{\n"
"int x = get_global_id(0);\n"
"int y = get_global_id(1);\n"
"if(x < u1_col && y < u1_row)\n"
"{\n"
"int src_x1 = (x + 1) < (u1_col - 1) ? (x + 1) : (u1_col - 1);\n"
"float u1x = u1[(y + u1_offset_y) * u1_step + src_x1 + u1_offset_x] - u1[(y + u1_offset_y) * u1_step + x + u1_offset_x];\n"
"int src_y1 = (y + 1) < (u1_row - 1) ? (y + 1) : (u1_row - 1);\n"
"float u1y = u1[(src_y1 + u1_offset_y) * u1_step + x + u1_offset_x] - u1[(y + u1_offset_y) * u1_step + x + u1_offset_x];\n"
"int src_x2 = (x + 1) < (u1_col - 1) ? (x + 1) : (u1_col - 1);\n"
"float u2x = u2[(y + u2_offset_y) * u2_step + src_x2 + u2_offset_x] - u2[(y + u2_offset_y) * u2_step + x + u2_offset_x];\n"
"int src_y2 = (y + 1) <  (u1_row - 1) ? (y + 1) : (u1_row - 1);\n"
"float u2y = u2[(src_y2 + u2_offset_y) * u2_step + x + u2_offset_x] - u2[(y + u2_offset_y) * u2_step + x + u2_offset_x];\n"
"float g1 = hypot(u1x, u1y);\n"
"float g2 = hypot(u2x, u2y);\n"
"float ng1 = 1.0f + taut * g1;\n"
"float ng2 = 1.0f + taut * g2;\n"
"p11[y * p11_step + x] = (p11[y * p11_step + x] + taut * u1x) / ng1;\n"
"p12[y * p11_step + x] = (p12[y * p11_step + x] + taut * u1y) / ng1;\n"
"p21[y * p11_step + x] = (p21[y * p11_step + x] + taut * u2x) / ng2;\n"
"p22[y * p11_step + x] = (p22[y * p11_step + x] + taut * u2y) / ng2;\n"
"}\n"
"}\n"
"inline float divergence(__global const float* v1, __global const float* v2, int y, int x, int v1_step, int v2_step)\n"
"{\n"
"if (x > 0 && y > 0)\n"
"{\n"
"float v1x = v1[y * v1_step + x] - v1[y * v1_step + x - 1];\n"
"float v2y = v2[y * v2_step + x] - v2[(y - 1) * v2_step + x];\n"
"return v1x + v2y;\n"
"}\n"
"else\n"
"{\n"
"if (y > 0)\n"
"return v1[y * v1_step + 0] + v2[y * v2_step + 0] - v2[(y - 1) * v2_step + 0];\n"
"else\n"
"{\n"
"if (x > 0)\n"
"return v1[0 * v1_step + x] - v1[0 * v1_step + x - 1] + v2[0 * v2_step + x];\n"
"else\n"
"return v1[0 * v1_step + 0] + v2[0 * v2_step + 0];\n"
"}\n"
"}\n"
"}\n"
"__kernel void estimateUKernel(__global const float* I1wx, int I1wx_col, int I1wx_row, int I1wx_step,\n"
"__global const float* I1wy, \n"
"__global const float* grad, \n"
"__global const float* rho_c, \n"
"__global const float* p11, \n"
"__global const float* p12, \n"
"__global const float* p21, \n"
"__global const float* p22, \n"
"__global float* u1, int u1_step,\n"
"__global float* u2,\n"
"__global float* error, float l_t, float theta, int u2_step,\n"
"int u1_offset_x,\n"
"int u1_offset_y,\n"
"int u2_offset_x,\n"
"int u2_offset_y,\n"
"char calc_error)\n"
"{\n"
"int x = get_global_id(0);\n"
"int y = get_global_id(1);\n"
"if(x < I1wx_col && y < I1wx_row)\n"
"{\n"
"float I1wxVal = I1wx[y * I1wx_step + x];\n"
"float I1wyVal = I1wy[y * I1wx_step + x];\n"
"float gradVal = grad[y * I1wx_step + x];\n"
"float u1OldVal = u1[(y + u1_offset_y) * u1_step + x + u1_offset_x];\n"
"float u2OldVal = u2[(y + u2_offset_y) * u2_step + x + u2_offset_x];\n"
"float rho = rho_c[y * I1wx_step + x] + (I1wxVal * u1OldVal + I1wyVal * u2OldVal);\n"
"float d1 = 0.0f;\n"
"float d2 = 0.0f;\n"
"if (rho < -l_t * gradVal)\n"
"{\n"
"d1 = l_t * I1wxVal;\n"
"d2 = l_t * I1wyVal;\n"
"}\n"
"else if (rho > l_t * gradVal)\n"
"{\n"
"d1 = -l_t * I1wxVal;\n"
"d2 = -l_t * I1wyVal;\n"
"}\n"
"else if (gradVal > 1.192092896e-07f)\n"
"{\n"
"float fi = -rho / gradVal;\n"
"d1 = fi * I1wxVal;\n"
"d2 = fi * I1wyVal;\n"
"}\n"
"float v1 = u1OldVal + d1;\n"
"float v2 = u2OldVal + d2;\n"
"float div_p1 = divergence(p11, p12, y, x, I1wx_step, I1wx_step);\n"
"float div_p2 = divergence(p21, p22, y, x, I1wx_step, I1wx_step);\n"
"float u1NewVal = v1 + theta * div_p1;\n"
"float u2NewVal = v2 + theta * div_p2;\n"
"u1[(y + u1_offset_y) * u1_step + x + u1_offset_x] = u1NewVal;\n"
"u2[(y + u2_offset_y) * u2_step + x + u2_offset_x] = u2NewVal;\n"
"if(calc_error)\n"
"{\n"
"float n1 = (u1OldVal - u1NewVal) * (u1OldVal - u1NewVal);\n"
"float n2 = (u2OldVal - u2NewVal) * (u2OldVal - u2NewVal);\n"
"error[y * I1wx_step + x] = n1 + n2;\n"
"}\n"
"}\n"
"}\n"
, "9474ca90cbcc7660f6c47b8a45d6730d", NULL};
struct cv::ocl::internal::ProgramEntry sparse_matching_gpc_oclsrc={moduleName, "sparse_matching_gpc",
"__kernel void getPatchDescriptor(\n"
"__global const uchar* imgCh0, int ic0step, int ic0off,\n"
"__global const uchar* imgCh1, int ic1step, int ic1off,\n"
"__global const uchar* imgCh2, int ic2step, int ic2off,\n"
"__global uchar* out, int outstep, int outoff,\n"
"const int gh, const int gw, const int PR  )\n"
"{\n"
"const int i = get_global_id(0);\n"
"const int j = get_global_id(1);\n"
"if (i >= gh || j >= gw)\n"
"return;\n"
"__global double* desc = (__global double*)(out + (outstep * (i * gw + j) + outoff));\n"
"const int patchRadius = PR * 2;\n"
"float patch[PATCH_RADIUS_DOUBLED][PATCH_RADIUS_DOUBLED];\n"
"for (int i0 = 0; i0 < patchRadius; ++i0) {\n"
"__global const float* ch0Row = (__global const float*)(imgCh0 + (ic0step * (i + i0) + ic0off + j * sizeof(float)));\n"
"for (int j0 = 0; j0 < patchRadius; ++j0)\n"
"patch[i0][j0] = ch0Row[j0];\n"
"}\n"
"#pragma unroll\n"
"for (int n0 = 0; n0 < 4; ++n0) {\n"
"#pragma unroll\n"
"for (int n1 = 0; n1 < 4; ++n1) {\n"
"double sum = 0;\n"
"for (int i0 = 0; i0 < patchRadius; ++i0)\n"
"for (int j0 = 0; j0 < patchRadius; ++j0)\n"
"sum += patch[i0][j0] * cos(CV_PI * (i0 + 0.5) * n0 / patchRadius) * cos(CV_PI * (j0 + 0.5) * n1 / patchRadius);\n"
"desc[n0 * 4 + n1] = sum / PR;\n"
"}\n"
"}\n"
"for (int k = 0; k < 4; ++k) {\n"
"desc[k] *= SQRT2_INV;\n"
"desc[k * 4] *= SQRT2_INV;\n"
"}\n"
"double sum = 0;\n"
"for (int i0 = 0; i0 < patchRadius; ++i0) {\n"
"__global const float* ch1Row = (__global const float*)(imgCh1 + (ic1step * (i + i0) + ic1off + j * sizeof(float)));\n"
"for (int j0 = 0; j0 < patchRadius; ++j0)\n"
"sum += ch1Row[j0];\n"
"}\n"
"desc[16] = sum / patchRadius;\n"
"sum = 0;\n"
"for (int i0 = 0; i0 < patchRadius; ++i0) {\n"
"__global const float* ch2Row = (__global const float*)(imgCh2 + (ic2step * (i + i0) + ic2off + j * sizeof(float)));\n"
"for (int j0 = 0; j0 < patchRadius; ++j0)\n"
"sum += ch2Row[j0];\n"
"}\n"
"desc[17] = sum / patchRadius;\n"
"}\n"
, "4de6dbd7b34900887da8399ec2e431b0", NULL};
struct cv::ocl::internal::ProgramEntry updatemotionhistory_oclsrc={moduleName, "updatemotionhistory",
"__kernel void updateMotionHistory(__global const uchar * silh, int silh_step, int silh_offset,\n"
"__global uchar * mhiptr, int mhi_step, int mhi_offset, int mhi_rows, int mhi_cols,\n"
"float timestamp, float delbound)\n"
"{\n"
"int x = get_global_id(0);\n"
"int y = get_global_id(1);\n"
"if (x < mhi_cols && y < mhi_rows)\n"
"{\n"
"int silh_index = mad24(y, silh_step, silh_offset + x);\n"
"int mhi_index = mad24(y, mhi_step, mhi_offset + x * (int)sizeof(float));\n"
"silh += silh_index;\n"
"__global float * mhi = (__global float *)(mhiptr + mhi_index);\n"
"float val = mhi[0];\n"
"val = silh[0] ? timestamp : val < delbound ? 0 : val;\n"
"mhi[0] = val;\n"
"}\n"
"}\n"
, "b19beb01d0c6052524049341b55a2be5", NULL};

}}}
#endif
