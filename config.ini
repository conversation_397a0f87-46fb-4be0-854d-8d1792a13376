# Capture
capture_method = duplication_api
detection_resolution = 320
capture_fps = 60
monitor_idx = 0
circle_mask = true
capture_borders = true
capture_cursor = true
virtual_camera_name = None
virtual_camera_width = 1920
virtual_camera_heigth = 1080

# Target
disable_headshot = false
body_y_offset = 0.15
head_y_offset = 0.05
ignore_third_person = false
shooting_range_targets = false
auto_aim = false

# Mouse move
fovX = 106
fovY = 74
minSpeedMultiplier = 0.10
maxSpeedMultiplier = 0.10
predictionInterval = 0.01
prediction_futurePositions = 20
draw_futurePositions = true
snapRadius = 1.50
nearRadius = 25.00
speedCurveExponent = 3.00
snapBoostFactor = 1.15
easynorecoil = false
easynorecoilstrength = 0.0
# WIN32, GHUB, ARDUINO, KMBOX_B, KMBOX_NET
input_method = WIN32

# Wind mouse
wind_mouse_enabled = false
wind_G = 18.0
wind_W = 15.0
wind_M = 10.0
wind_D = 8.0

# Arduino
arduino_baudrate = 115200
arduino_port = COM0
arduino_16_bit_mouse = false
arduino_enable_keys = false

# Kmbox_B
kmbox_baudrate = 115200
kmbox_port = COM0

# Kmbox_net
kmbox_net_ip = ***********
kmbox_net_port = 1984
kmbox_net_uuid = DEADC0DE

# Mouse shooting
auto_shoot = false
bScope_multiplier = 1.0

# AI
backend = TRT
dml_device_id = 0
ai_model = sunxds_0.5.6.engine
confidence_threshold = 0.10
nms_threshold = 0.50
max_detections = 100
postprocess = yolo10
batch_size = 1
export_enable_fp8 = false
export_enable_fp16 = true
fixed_input_size = false

# CUDA
use_cuda_graph = false
use_pinned_memory = false

# Buttons
button_targeting = RightMouseButton
button_shoot = LeftMouseButton
button_zoom = RightMouseButton
button_exit = F2
button_pause = F3
button_reload_config = F4
button_open_overlay = Home
enable_arrows_settings = false

# Overlay
overlay_opacity = 225
overlay_snow_theme = true
overlay_ui_scale = 1.00

# Custom Classes
class_player = 0
class_bot = 1
class_weapon = 2
class_outline = 3
class_dead_body = 4
class_hideout_target_human = 5
class_hideout_target_balls = 6
class_head = 7
class_smoke = 8
class_fire = 9
class_third_person = 10

# Debug
show_window = true
show_fps = false
screenshot_button = None
screenshot_delay = 500
verbose = false

# Active game profile
active_game = 

[Games]
