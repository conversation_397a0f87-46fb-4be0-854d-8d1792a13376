﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{C61D51FE-FA1F-3698-BF46-7F6739255824}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>libtiff</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\AI\opencv\cudabuild\3rdparty\lib\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">libtiff.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">libtiffd</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\AI\opencv\cudabuild\3rdparty\lib\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">libtiff.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">libtiff</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\cudabuild\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff;D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/AI/opencv/cudabuild" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4127;4251;4324;4275;4512;4589;4819;4018;4100;4127;4311;4701;4706;4244;4267;4305;4306;4703;4456;4457;4312</DisableSpecificWarnings>
      <ExceptionHandling>Async</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ProgramDataBaseFileName>D:\AI\opencv\cudabuild\3rdparty\lib\Debug\libtiffd.pdb</ProgramDataBaseFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\cudabuild\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff;D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\cudabuild\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff;D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4221</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\cudabuild\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff;D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/AI/opencv/cudabuild" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4127;4251;4324;4275;4512;4589;4819;4018;4100;4127;4311;4701;4706;4244;4267;4305;4306;4703;4456;4457;4312</DisableSpecificWarnings>
      <ExceptionHandling>Async</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;NDEBUG;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ProgramDataBaseFileName>D:\AI\opencv\cudabuild\3rdparty\lib\Release\libtiff.pdb</ProgramDataBaseFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;NDEBUG;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\cudabuild\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff;D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\zlib;D:\AI\opencv\opencv-4.10.0\3rdparty\zlib;D:\AI\opencv\cudabuild\3rdparty\libtiff;D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff;D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4221</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_aux.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_close.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_codec.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_color.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_compress.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_dir.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_dirinfo.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_dirread.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_dirwrite.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_dumpmode.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_error.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_extension.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_fax3.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_fax3sm.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_flush.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_getimage.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_hash_set.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_jbig.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_jpeg_12.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_jpeg.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_luv.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_lzma.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_lzw.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_next.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_ojpeg.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_open.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_packbits.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_pixarlog.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_predict.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_print.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_read.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_strip.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_swab.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_thunder.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_tile.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_version.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_warning.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_webp.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_write.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_zip.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_zstd.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_stream.cxx" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\t4.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_dir.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_fax3.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_hash_set.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_predict.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tiff.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tiffio.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tiffiop.h" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\libtiff\tiffvers.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\uvcode.h" />
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tiffio.hxx" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\libtiff\tif_config.h" />
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\libtiff\tiffconf.h" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libtiff\tif_win32.c" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\AI\opencv\cudabuild\3rdparty\zlib\zlib.vcxproj">
      <Project>{5BA6087C-B49F-31D7-A8F0-C37E5E8F7F80}</Project>
      <Name>zlib</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>