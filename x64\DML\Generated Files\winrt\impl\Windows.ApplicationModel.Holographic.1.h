// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_ApplicationModel_Holographic_1_H
#define WINRT_Windows_ApplicationModel_Holographic_1_H
#include "winrt/impl/Windows.ApplicationModel.Holographic.0.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Holographic
{
    struct WINRT_IMPL_EMPTY_BASES IHolographicKeyboard :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHolographicKeyboard>
    {
        IHolographicKeyboard(std::nullptr_t = nullptr) noexcept {}
        IHolographicKeyboard(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHolographicKeyboardStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHolographicKeyboardStatics>
    {
        IHolographicKeyboardStatics(std::nullptr_t = nullptr) noexcept {}
        IHolographicKeyboardStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
