// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_ApplicationModel_AppService_1_H
#define WINRT_Windows_ApplicationModel_AppService_1_H
#include "winrt/impl/Windows.ApplicationModel.AppService.0.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::AppService
{
    struct WINRT_IMPL_EMPTY_BASES IAppServiceCatalogStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppServiceCatalogStatics>
    {
        IAppServiceCatalogStatics(std::nullptr_t = nullptr) noexcept {}
        IAppServiceCatalogStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppServiceClosedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppServiceClosedEventArgs>
    {
        IAppServiceClosedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAppServiceClosedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppServiceConnection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppServiceConnection>
    {
        IAppServiceConnection(std::nullptr_t = nullptr) noexcept {}
        IAppServiceConnection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppServiceConnection2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppServiceConnection2>
    {
        IAppServiceConnection2(std::nullptr_t = nullptr) noexcept {}
        IAppServiceConnection2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppServiceConnectionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppServiceConnectionStatics>
    {
        IAppServiceConnectionStatics(std::nullptr_t = nullptr) noexcept {}
        IAppServiceConnectionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppServiceDeferral :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppServiceDeferral>
    {
        IAppServiceDeferral(std::nullptr_t = nullptr) noexcept {}
        IAppServiceDeferral(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppServiceRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppServiceRequest>
    {
        IAppServiceRequest(std::nullptr_t = nullptr) noexcept {}
        IAppServiceRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppServiceRequestReceivedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppServiceRequestReceivedEventArgs>
    {
        IAppServiceRequestReceivedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAppServiceRequestReceivedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppServiceResponse :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppServiceResponse>
    {
        IAppServiceResponse(std::nullptr_t = nullptr) noexcept {}
        IAppServiceResponse(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppServiceTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppServiceTriggerDetails>
    {
        IAppServiceTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        IAppServiceTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppServiceTriggerDetails2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppServiceTriggerDetails2>
    {
        IAppServiceTriggerDetails2(std::nullptr_t = nullptr) noexcept {}
        IAppServiceTriggerDetails2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppServiceTriggerDetails3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppServiceTriggerDetails3>
    {
        IAppServiceTriggerDetails3(std::nullptr_t = nullptr) noexcept {}
        IAppServiceTriggerDetails3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppServiceTriggerDetails4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppServiceTriggerDetails4>
    {
        IAppServiceTriggerDetails4(std::nullptr_t = nullptr) noexcept {}
        IAppServiceTriggerDetails4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStatelessAppServiceResponse :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStatelessAppServiceResponse>
    {
        IStatelessAppServiceResponse(std::nullptr_t = nullptr) noexcept {}
        IStatelessAppServiceResponse(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
