﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\test\test_faps.cpp">
      <Filter>opencv_structured_light\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\test\test_getProjPixel.cpp">
      <Filter>opencv_structured_light\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\test\test_main.cpp">
      <Filter>opencv_structured_light\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\test\test_plane.cpp">
      <Filter>opencv_structured_light\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\structured_light\test\test_precomp.hpp">
      <Filter>opencv_structured_light\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_structured_light">
      <UniqueIdentifier>{A80F8953-D456-3FCD-9AD0-DBEC59557E33}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_structured_light\Include">
      <UniqueIdentifier>{7838F324-5488-35F8-A9E0-EF69658B672B}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_structured_light\Src">
      <UniqueIdentifier>{3E3D632C-6583-3E1F-AE57-6F041BE1682E}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
