# This is the CMakeCache file.
# For build in directory: d:/AI/opencv/cudabuild/3rdparty/libjpeg-turbo/src/simd/CMakeFiles/CheckASM_NASM
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_AR:FILEPATH=CMAKE_AR-NOTFOUND

//ASM_NASM compiler
CMAKE_ASM_NASM_COMPILER:FILEPATH=CMAKE_ASM_NASM_COMPILER-NOTFOUND

//Flags used by the ASM_NASM compiler during all build types.
CMAKE_ASM_NASM_FLAGS:STRING=

//Flags used by the ASM_NASM compiler during DEBUG builds.
CMAKE_ASM_NASM_FLAGS_DEBUG:STRING=

//Flags used by the ASM_NASM compiler during MINSIZEREL builds.
CMAKE_ASM_NASM_FLAGS_MINSIZEREL:STRING=

//Flags used by the ASM_NASM compiler during RELEASE builds.
CMAKE_ASM_NASM_FLAGS_RELEASE:STRING=

//Flags used by the ASM_NASM compiler during RELWITHDEBINFO builds.
CMAKE_ASM_NASM_FLAGS_RELWITHDEBINFO:STRING=

//Semicolon separated list of supported configuration types, only
// supports Debug, Release, MinSizeRel, and RelWithDebInfo, anything
// else will be ignored.
CMAKE_CONFIGURATION_TYPES:STRING=Debug;Release;MinSizeRel;RelWithDebInfo

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=D:/AI/opencv/cudabuild/3rdparty/libjpeg-turbo/src/simd/CMakeFiles/CheckASM_NASM/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files/CheckASM_NASM

//Path to a program.
CMAKE_LINKER:FILEPATH=CMAKE_LINKER-NOTFOUND

//Path to a program.
CMAKE_MT:FILEPATH=CMAKE_MT-NOTFOUND

//Value Computed by CMake
CMAKE_PROJECT_COMPAT_VERSION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=CheckASM_NASM

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Value Computed by CMake
CheckASM_NASM_BINARY_DIR:STATIC=D:/AI/opencv/cudabuild/3rdparty/libjpeg-turbo/src/simd/CMakeFiles/CheckASM_NASM

//Value Computed by CMake
CheckASM_NASM_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
CheckASM_NASM_SOURCE_DIR:STATIC=D:/AI/opencv/cudabuild/3rdparty/libjpeg-turbo/src/simd/CMakeFiles/CheckASM_NASM


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_NASM_COMPILER
CMAKE_ASM_NASM_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_NASM_FLAGS
CMAKE_ASM_NASM_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_NASM_FLAGS_DEBUG
CMAKE_ASM_NASM_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_NASM_FLAGS_MINSIZEREL
CMAKE_ASM_NASM_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_NASM_FLAGS_RELEASE
CMAKE_ASM_NASM_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_NASM_FLAGS_RELWITHDEBINFO
CMAKE_ASM_NASM_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=d:/AI/opencv/cudabuild/3rdparty/libjpeg-turbo/src/simd/CMakeFiles/CheckASM_NASM
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=1
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=0
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Visual Studio 17 2022
//No help, variable specified on the command line.
CMAKE_GENERATOR_INSTANCE:INTERNAL=C:/Program Files/Microsoft Visual Studio/2022/Community
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=x64
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=D:/AI/opencv/cudabuild/3rdparty/libjpeg-turbo/src/simd/CMakeFiles/CheckASM_NASM
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MT
CMAKE_MT-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//noop for ranlib
CMAKE_RANLIB:INTERNAL=:
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-4.1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1

