// This file is auto-generated. Do not edit!

#include "opencv2/core/ocl.hpp"
#include "opencv2/core/ocl_genbase.hpp"
#include "opencv2/core/opencl/ocl_defs.hpp"

#ifdef HAVE_OPENCL

namespace cv
{
namespace ocl
{
namespace optflow
{

extern struct cv::ocl::internal::ProgramEntry optical_flow_tvl1_oclsrc;
extern struct cv::ocl::internal::ProgramEntry sparse_matching_gpc_oclsrc;
extern struct cv::ocl::internal::ProgramEntry updatemotionhistory_oclsrc;

}}}
#endif
