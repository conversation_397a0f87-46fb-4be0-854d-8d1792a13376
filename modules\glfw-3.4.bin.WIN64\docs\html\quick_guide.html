<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Getting started</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div><div class="header">
  <div class="headertitle"><div class="title">Getting started</div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#quick_steps">Step by step</a><ul><li class="level2"><a href="#quick_include">Including the GLFW header</a></li>
<li class="level2"><a href="#quick_init_term">Initializing and terminating GLFW</a></li>
<li class="level2"><a href="#quick_capture_error">Setting an error callback</a></li>
<li class="level2"><a href="#quick_create_window">Creating a window and context</a></li>
<li class="level2"><a href="#quick_context_current">Making the OpenGL context current</a></li>
<li class="level2"><a href="#quick_window_close">Checking the window close flag</a></li>
<li class="level2"><a href="#quick_key_input">Receiving input events</a></li>
<li class="level2"><a href="#quick_render">Rendering with OpenGL</a></li>
<li class="level2"><a href="#quick_timer">Reading the timer</a></li>
<li class="level2"><a href="#quick_swap_buffers">Swapping buffers</a></li>
<li class="level2"><a href="#quick_process_events">Processing events</a></li>
</ul>
</li>
<li class="level1"><a href="#quick_example">Putting it together</a></li>
</ul>
</div>
<div class="textblock"><p>This guide takes you through writing a small application using GLFW 3. The application will create a window and OpenGL context, render a rotating triangle and exit when the user closes the window or presses <em>Escape</em>. This guide will introduce a few of the most commonly used functions, but there are many more.</p>
<p>This guide assumes no experience with earlier versions of GLFW. If you have used GLFW 2 in the past, read <a class="el" href="moving_guide.html">Moving from GLFW 2 to 3</a>, as some functions behave differently in GLFW 3.</p>
<h1><a class="anchor" id="quick_steps"></a>
Step by step</h1>
<h2><a class="anchor" id="quick_include"></a>
Including the GLFW header</h2>
<p>In the source files of your application where you use GLFW, you need to include its header file.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3_8h.html">GLFW/glfw3.h</a>&gt;</span></div>
<div class="ttc" id="aglfw3_8h_html"><div class="ttname"><a href="glfw3_8h.html">glfw3.h</a></div><div class="ttdoc">The header of the GLFW 3 API.</div></div>
</div><!-- fragment --><p>This header provides all the constants, types and function prototypes of the GLFW API.</p>
<p>By default it also includes the OpenGL header from your development environment. On some platforms this header only supports older versions of OpenGL. The most extreme case is Windows, where it typically only supports OpenGL 1.2.</p>
<p>Most programs will instead use an <a class="el" href="context_guide.html#context_glext_auto">extension loader library</a> and include its header. This example uses files generated by <a href="https://gen.glad.sh/">glad</a>. The GLFW header can detect most such headers if they are included first and will then not include the one from your development environment.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#include &lt;glad/gl.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3_8h.html">GLFW/glfw3.h</a>&gt;</span></div>
</div><!-- fragment --><p>To make sure there will be no header conflicts, you can define <a class="el" href="build_guide.html#GLFW_INCLUDE_NONE">GLFW_INCLUDE_NONE</a> before the GLFW header to explicitly disable inclusion of the development environment header. This also allows the two headers to be included in any order.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#define GLFW_INCLUDE_NONE</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3_8h.html">GLFW/glfw3.h</a>&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;glad/gl.h&gt;</span></div>
</div><!-- fragment --><h2><a class="anchor" id="quick_init_term"></a>
Initializing and terminating GLFW</h2>
<p>Before you can use most GLFW functions, the library must be initialized. On successful initialization, <code>GLFW_TRUE</code> is returned. If an error occurred, <code>GLFW_FALSE</code> is returned.</p>
<div class="fragment"><div class="line"><span class="keywordflow">if</span> (!<a class="code hl_function" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a>())</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// Initialization failed</span></div>
<div class="line">}</div>
<div class="ttc" id="agroup__init_html_ga317aac130a235ab08c6db0834907d85e"><div class="ttname"><a href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a></div><div class="ttdeci">int glfwInit(void)</div><div class="ttdoc">Initializes the GLFW library.</div></div>
</div><!-- fragment --><p>Note that <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code> are and will always be one and zero.</p>
<p>When you are done using GLFW, typically just before the application exits, you need to terminate GLFW.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a>();</div>
<div class="ttc" id="agroup__init_html_gaaae48c0a18607ea4a4ba951d939f0901"><div class="ttname"><a href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a></div><div class="ttdeci">void glfwTerminate(void)</div><div class="ttdoc">Terminates the GLFW library.</div></div>
</div><!-- fragment --><p>This destroys any remaining windows and releases any other resources allocated by GLFW. After this call, you must initialize GLFW again before using any GLFW functions that require it.</p>
<h2><a class="anchor" id="quick_capture_error"></a>
Setting an error callback</h2>
<p>Most events are reported through callbacks, whether it's a key being pressed, a GLFW window being moved, or an error occurring. Callbacks are C functions (or C++ static methods) that are called by GLFW with arguments describing the event.</p>
<p>In case a GLFW function fails, an error is reported to the GLFW error callback. You can receive these reports with an error callback. This function must have the signature below but may do anything permitted in other callbacks.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> error_callback(<span class="keywordtype">int</span> error, <span class="keyword">const</span> <span class="keywordtype">char</span>* description)</div>
<div class="line">{</div>
<div class="line">    fprintf(stderr, <span class="stringliteral">&quot;Error: %s\n&quot;</span>, description);</div>
<div class="line">}</div>
</div><!-- fragment --><p>Callback functions must be set, so GLFW knows to call them. The function to set the error callback is one of the few GLFW functions that may be called before initialization, which lets you be notified of errors both during and after initialization.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__init.html#gaff45816610d53f0b83656092a4034f40">glfwSetErrorCallback</a>(error_callback);</div>
<div class="ttc" id="agroup__init_html_gaff45816610d53f0b83656092a4034f40"><div class="ttname"><a href="group__init.html#gaff45816610d53f0b83656092a4034f40">glfwSetErrorCallback</a></div><div class="ttdeci">GLFWerrorfun glfwSetErrorCallback(GLFWerrorfun callback)</div><div class="ttdoc">Sets the error callback.</div></div>
</div><!-- fragment --><h2><a class="anchor" id="quick_create_window"></a>
Creating a window and context</h2>
<p>The window and its OpenGL context are created with a single call to <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>, which returns a handle to the created combined window and context object</p>
<div class="fragment"><div class="line"><a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window = <a class="code hl_function" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>(640, 480, <span class="stringliteral">&quot;My Title&quot;</span>, NULL, NULL);</div>
<div class="line"><span class="keywordflow">if</span> (!window)</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// Window or OpenGL context creation failed</span></div>
<div class="line">}</div>
<div class="ttc" id="agroup__window_html_ga3555a418df92ad53f917597fe2f64aeb"><div class="ttname"><a href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a></div><div class="ttdeci">GLFWwindow * glfwCreateWindow(int width, int height, const char *title, GLFWmonitor *monitor, GLFWwindow *share)</div><div class="ttdoc">Creates a window and its associated context.</div></div>
<div class="ttc" id="agroup__window_html_ga3c96d80d363e67d13a41b5d1821f3242"><div class="ttname"><a href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a></div><div class="ttdeci">struct GLFWwindow GLFWwindow</div><div class="ttdoc">Opaque window object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1403</div></div>
</div><!-- fragment --><p>This creates a 640 by 480 windowed mode window with an OpenGL context. If window or OpenGL context creation fails, <code>NULL</code> will be returned. You should always check the return value. While window creation rarely fails, context creation depends on properly installed drivers and may fail even on machines with the necessary hardware.</p>
<p>By default, the OpenGL context GLFW creates may have any version. You can require a minimum OpenGL version by setting the <code>GLFW_CONTEXT_VERSION_MAJOR</code> and <code>GLFW_CONTEXT_VERSION_MINOR</code> hints <em>before</em> creation. If the required minimum version is not supported on the machine, context (and window) creation fails.</p>
<p>You can select the OpenGL profile by setting the <code>GLFW_OPENGL_PROFILE</code> hint. This program uses the core profile as that is the only profile macOS supports for OpenGL 3.x and 4.x.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#gafe5e4922de1f9932d7e9849bb053b0c0">GLFW_CONTEXT_VERSION_MAJOR</a>, 3);</div>
<div class="line"><a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#ga31aca791e4b538c4e4a771eb95cc2d07">GLFW_CONTEXT_VERSION_MINOR</a>, 3);</div>
<div class="line"><a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#ga44f3a6b4261fbe351e0b950b0f372e12">GLFW_OPENGL_PROFILE</a>, <a class="code hl_define" href="glfw3_8h.html#af094bb16da76f66ebceb19ee213b3de8">GLFW_OPENGL_CORE_PROFILE</a>);</div>
<div class="line"><a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window = <a class="code hl_function" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>(640, 480, <span class="stringliteral">&quot;My Title&quot;</span>, NULL, NULL);</div>
<div class="line"><span class="keywordflow">if</span> (!window)</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// Window or context creation failed</span></div>
<div class="line">}</div>
<div class="ttc" id="aglfw3_8h_html_af094bb16da76f66ebceb19ee213b3de8"><div class="ttname"><a href="glfw3_8h.html#af094bb16da76f66ebceb19ee213b3de8">GLFW_OPENGL_CORE_PROFILE</a></div><div class="ttdeci">#define GLFW_OPENGL_CORE_PROFILE</div><div class="ttdef"><b>Definition</b> glfw3.h:1149</div></div>
<div class="ttc" id="agroup__window_html_ga31aca791e4b538c4e4a771eb95cc2d07"><div class="ttname"><a href="group__window.html#ga31aca791e4b538c4e4a771eb95cc2d07">GLFW_CONTEXT_VERSION_MINOR</a></div><div class="ttdeci">#define GLFW_CONTEXT_VERSION_MINOR</div><div class="ttdoc">Context client API minor version hint and attribute.</div><div class="ttdef"><b>Definition</b> glfw3.h:1043</div></div>
<div class="ttc" id="agroup__window_html_ga44f3a6b4261fbe351e0b950b0f372e12"><div class="ttname"><a href="group__window.html#ga44f3a6b4261fbe351e0b950b0f372e12">GLFW_OPENGL_PROFILE</a></div><div class="ttdeci">#define GLFW_OPENGL_PROFILE</div><div class="ttdoc">OpenGL profile hint and attribute.</div><div class="ttdef"><b>Definition</b> glfw3.h:1078</div></div>
<div class="ttc" id="agroup__window_html_ga7d9c8c62384b1e2821c4dc48952d2033"><div class="ttname"><a href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a></div><div class="ttdeci">void glfwWindowHint(int hint, int value)</div><div class="ttdoc">Sets the specified window hint to the desired value.</div></div>
<div class="ttc" id="agroup__window_html_gafe5e4922de1f9932d7e9849bb053b0c0"><div class="ttname"><a href="group__window.html#gafe5e4922de1f9932d7e9849bb053b0c0">GLFW_CONTEXT_VERSION_MAJOR</a></div><div class="ttdeci">#define GLFW_CONTEXT_VERSION_MAJOR</div><div class="ttdoc">Context client API major version hint and attribute.</div><div class="ttdef"><b>Definition</b> glfw3.h:1037</div></div>
</div><!-- fragment --><p>When a window and context is no longer needed, destroy it.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a>(window);</div>
<div class="ttc" id="agroup__window_html_gacdf43e51376051d2c091662e9fe3d7b2"><div class="ttname"><a href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a></div><div class="ttdeci">void glfwDestroyWindow(GLFWwindow *window)</div><div class="ttdoc">Destroys the specified window and its context.</div></div>
</div><!-- fragment --><p>Once this function is called, no more events will be delivered for that window and its handle becomes invalid.</p>
<h2><a class="anchor" id="quick_context_current"></a>
Making the OpenGL context current</h2>
<p>Before you can use the OpenGL API, you must have a current OpenGL context.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__context.html#ga1c04dc242268f827290fe40aa1c91157">glfwMakeContextCurrent</a>(window);</div>
<div class="ttc" id="agroup__context_html_ga1c04dc242268f827290fe40aa1c91157"><div class="ttname"><a href="group__context.html#ga1c04dc242268f827290fe40aa1c91157">glfwMakeContextCurrent</a></div><div class="ttdeci">void glfwMakeContextCurrent(GLFWwindow *window)</div><div class="ttdoc">Makes the context of the specified window current for the calling thread.</div></div>
</div><!-- fragment --><p>The context will remain current until you make another context current or until the window owning the current context is destroyed.</p>
<p>If you are using an <a class="el" href="context_guide.html#context_glext_auto">extension loader library</a> to access modern OpenGL then this is when to initialize it, as the loader needs a current context to load from. This example uses <a href="https://github.com/Dav1dde/glad">glad</a>, but the same rule applies to all such libraries.</p>
<div class="fragment"><div class="line">gladLoadGL(<a class="code hl_function" href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a>);</div>
<div class="ttc" id="agroup__context_html_ga35f1837e6f666781842483937612f163"><div class="ttname"><a href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a></div><div class="ttdeci">GLFWglproc glfwGetProcAddress(const char *procname)</div><div class="ttdoc">Returns the address of the specified function for the current context.</div></div>
</div><!-- fragment --><h2><a class="anchor" id="quick_window_close"></a>
Checking the window close flag</h2>
<p>Each window has a flag indicating whether the window should be closed.</p>
<p>When the user attempts to close the window, either by pressing the close widget in the title bar or using a key combination like Alt+F4, this flag is set to 1. Note that <b>the window isn't actually closed</b>, so you are expected to monitor this flag and either destroy the window or give some kind of feedback to the user.</p>
<div class="fragment"><div class="line"><span class="keywordflow">while</span> (!<a class="code hl_function" href="group__window.html#ga24e02fbfefbb81fc45320989f8140ab5">glfwWindowShouldClose</a>(window))</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// Keep running</span></div>
<div class="line">}</div>
<div class="ttc" id="agroup__window_html_ga24e02fbfefbb81fc45320989f8140ab5"><div class="ttname"><a href="group__window.html#ga24e02fbfefbb81fc45320989f8140ab5">glfwWindowShouldClose</a></div><div class="ttdeci">int glfwWindowShouldClose(GLFWwindow *window)</div><div class="ttdoc">Checks the close flag of the specified window.</div></div>
</div><!-- fragment --><p>You can be notified when the user is attempting to close the window by setting a close callback with <a class="el" href="group__window.html#gada646d775a7776a95ac000cfc1885331">glfwSetWindowCloseCallback</a>. The callback will be called immediately after the close flag has been set.</p>
<p>You can also set it yourself with <a class="el" href="group__window.html#ga49c449dde2a6f87d996f4daaa09d6708">glfwSetWindowShouldClose</a>. This can be useful if you want to interpret other kinds of input as closing the window, like for example pressing the <em>Escape</em> key.</p>
<h2><a class="anchor" id="quick_key_input"></a>
Receiving input events</h2>
<p>Each window has a large number of callbacks that can be set to receive all the various kinds of events. To receive key press and release events, create a key callback function.</p>
<div class="fragment"><div class="line"><span class="keyword">static</span> <span class="keywordtype">void</span> key_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> key, <span class="keywordtype">int</span> scancode, <span class="keywordtype">int</span> action, <span class="keywordtype">int</span> mods)</div>
<div class="line">{</div>
<div class="line">    <span class="keywordflow">if</span> (key == <a class="code hl_define" href="group__keys.html#gaac6596c350b635c245113b81c2123b93">GLFW_KEY_ESCAPE</a> &amp;&amp; action == <a class="code hl_define" href="group__input.html#ga2485743d0b59df3791c45951c4195265">GLFW_PRESS</a>)</div>
<div class="line">        <a class="code hl_function" href="group__window.html#ga49c449dde2a6f87d996f4daaa09d6708">glfwSetWindowShouldClose</a>(window, <a class="code hl_define" href="group__init.html#ga2744fbb29b5631bb28802dbe0cf36eba">GLFW_TRUE</a>);</div>
<div class="line">}</div>
<div class="ttc" id="agroup__init_html_ga2744fbb29b5631bb28802dbe0cf36eba"><div class="ttname"><a href="group__init.html#ga2744fbb29b5631bb28802dbe0cf36eba">GLFW_TRUE</a></div><div class="ttdeci">#define GLFW_TRUE</div><div class="ttdoc">One.</div><div class="ttdef"><b>Definition</b> glfw3.h:312</div></div>
<div class="ttc" id="agroup__input_html_ga2485743d0b59df3791c45951c4195265"><div class="ttname"><a href="group__input.html#ga2485743d0b59df3791c45951c4195265">GLFW_PRESS</a></div><div class="ttdeci">#define GLFW_PRESS</div><div class="ttdoc">The key or mouse button was pressed.</div><div class="ttdef"><b>Definition</b> glfw3.h:338</div></div>
<div class="ttc" id="agroup__keys_html_gaac6596c350b635c245113b81c2123b93"><div class="ttname"><a href="group__keys.html#gaac6596c350b635c245113b81c2123b93">GLFW_KEY_ESCAPE</a></div><div class="ttdeci">#define GLFW_KEY_ESCAPE</div><div class="ttdef"><b>Definition</b> glfw3.h:448</div></div>
<div class="ttc" id="agroup__window_html_ga49c449dde2a6f87d996f4daaa09d6708"><div class="ttname"><a href="group__window.html#ga49c449dde2a6f87d996f4daaa09d6708">glfwSetWindowShouldClose</a></div><div class="ttdeci">void glfwSetWindowShouldClose(GLFWwindow *window, int value)</div><div class="ttdoc">Sets the close flag of the specified window.</div></div>
</div><!-- fragment --><p>The key callback, like other window related callbacks, are set per-window.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__input.html#ga1caf18159767e761185e49a3be019f8d">glfwSetKeyCallback</a>(window, key_callback);</div>
<div class="ttc" id="agroup__input_html_ga1caf18159767e761185e49a3be019f8d"><div class="ttname"><a href="group__input.html#ga1caf18159767e761185e49a3be019f8d">glfwSetKeyCallback</a></div><div class="ttdeci">GLFWkeyfun glfwSetKeyCallback(GLFWwindow *window, GLFWkeyfun callback)</div><div class="ttdoc">Sets the key callback.</div></div>
</div><!-- fragment --><p>In order for event callbacks to be called when events occur, you need to process events as described below.</p>
<h2><a class="anchor" id="quick_render"></a>
Rendering with OpenGL</h2>
<p>Once you have a current OpenGL context, you can use OpenGL normally. In this tutorial, a multicolored rotating triangle will be rendered. The framebuffer size needs to be retrieved for <code>glViewport</code>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> width, height;</div>
<div class="line"><a class="code hl_function" href="group__window.html#ga0e2637a4161afb283f5300c7f94785c9">glfwGetFramebufferSize</a>(window, &amp;width, &amp;height);</div>
<div class="line">glViewport(0, 0, width, height);</div>
<div class="ttc" id="agroup__window_html_ga0e2637a4161afb283f5300c7f94785c9"><div class="ttname"><a href="group__window.html#ga0e2637a4161afb283f5300c7f94785c9">glfwGetFramebufferSize</a></div><div class="ttdeci">void glfwGetFramebufferSize(GLFWwindow *window, int *width, int *height)</div><div class="ttdoc">Retrieves the size of the framebuffer of the specified window.</div></div>
</div><!-- fragment --><p>You can also set a framebuffer size callback using <a class="el" href="group__window.html#gab3fb7c3366577daef18c0023e2a8591f">glfwSetFramebufferSizeCallback</a> and be notified when the size changes.</p>
<p>The details of how to render with OpenGL is outside the scope of this tutorial, but there are many excellent resources for learning modern OpenGL. Here are a few of them:</p>
<ul>
<li><a href="https://antongerdelan.net/opengl/">Anton's OpenGL 4 Tutorials</a></li>
<li><a href="https://learnopengl.com/">Learn OpenGL</a></li>
<li><a href="https://open.gl/">Open.GL</a></li>
</ul>
<p>These all happen to use GLFW, but OpenGL itself works the same whatever API you use to create the window and context.</p>
<h2><a class="anchor" id="quick_timer"></a>
Reading the timer</h2>
<p>To create smooth animation, a time source is needed. GLFW provides a timer that returns the number of seconds since initialization. The time source used is the most accurate on each platform and generally has micro- or nanosecond resolution.</p>
<div class="fragment"><div class="line"><span class="keywordtype">double</span> time = <a class="code hl_function" href="group__input.html#gaa6cf4e7a77158a3b8fd00328b1720a4a">glfwGetTime</a>();</div>
<div class="ttc" id="agroup__input_html_gaa6cf4e7a77158a3b8fd00328b1720a4a"><div class="ttname"><a href="group__input.html#gaa6cf4e7a77158a3b8fd00328b1720a4a">glfwGetTime</a></div><div class="ttdeci">double glfwGetTime(void)</div><div class="ttdoc">Returns the GLFW time.</div></div>
</div><!-- fragment --><h2><a class="anchor" id="quick_swap_buffers"></a>
Swapping buffers</h2>
<p>GLFW windows by default use double buffering. That means that each window has two rendering buffers; a front buffer and a back buffer. The front buffer is the one being displayed and the back buffer the one you render to.</p>
<p>When the entire frame has been rendered, the buffers need to be swapped with one another, so the back buffer becomes the front buffer and vice versa.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a>(window);</div>
<div class="ttc" id="agroup__window_html_ga15a5a1ee5b3c2ca6b15ca209a12efd14"><div class="ttname"><a href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a></div><div class="ttdeci">void glfwSwapBuffers(GLFWwindow *window)</div><div class="ttdoc">Swaps the front and back buffers of the specified window.</div></div>
</div><!-- fragment --><p>The swap interval indicates how many frames to wait until swapping the buffers, commonly known as <em>vsync</em>. By default, the swap interval is zero, meaning buffer swapping will occur immediately. On fast machines, many of those frames will never be seen, as the screen is still only updated typically 60-75 times per second, so this wastes a lot of CPU and GPU cycles.</p>
<p>Also, because the buffers will be swapped in the middle the screen update, leading to <a href="https://en.wikipedia.org/wiki/Screen_tearing">screen tearing</a>.</p>
<p>For these reasons, applications will typically want to set the swap interval to one. It can be set to higher values, but this is usually not recommended, because of the input latency it leads to.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed">glfwSwapInterval</a>(1);</div>
<div class="ttc" id="agroup__context_html_ga6d4e0cdf151b5e579bd67f13202994ed"><div class="ttname"><a href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed">glfwSwapInterval</a></div><div class="ttdeci">void glfwSwapInterval(int interval)</div><div class="ttdoc">Sets the swap interval for the current context.</div></div>
</div><!-- fragment --><p>This function acts on the current context and will fail unless a context is current.</p>
<h2><a class="anchor" id="quick_process_events"></a>
Processing events</h2>
<p>GLFW needs to communicate regularly with the window system both in order to receive events and to show that the application hasn't locked up. Event processing must be done regularly while you have visible windows and is normally done each frame after buffer swapping.</p>
<p>There are two methods for processing pending events; polling and waiting. This example will use event polling, which processes only those events that have already been received and then returns immediately.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a>();</div>
<div class="ttc" id="agroup__window_html_ga37bd57223967b4211d60ca1a0bf3c832"><div class="ttname"><a href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a></div><div class="ttdeci">void glfwPollEvents(void)</div><div class="ttdoc">Processes all pending events.</div></div>
</div><!-- fragment --><p>This is the best choice when rendering continually, like most games do. If instead you only need to update your rendering once you have received new input, <a class="el" href="group__window.html#ga554e37d781f0a997656c26b2c56c835e">glfwWaitEvents</a> is a better choice. It waits until at least one event has been received, putting the thread to sleep in the meantime, and then processes all received events. This saves a great deal of CPU cycles and is useful for, for example, many kinds of editing tools.</p>
<h1><a class="anchor" id="quick_example"></a>
Putting it together</h1>
<p>Now that you know how to initialize GLFW, create a window and poll for keyboard input, it's possible to create a small program.</p>
<p>This program creates a 640 by 480 windowed mode window and starts a loop that clears the screen, renders a triangle and processes events until the user either presses <em>Escape</em> or closes the window.</p>
<div class="fragment"><div class="line"> </div>
<div class="line"><span class="preprocessor">#define GLAD_GL_IMPLEMENTATION</span></div>
<div class="line"><span class="preprocessor">#include &lt;glad/gl.h&gt;</span></div>
<div class="line"><span class="preprocessor">#define GLFW_INCLUDE_NONE</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3_8h.html">GLFW/glfw3.h</a>&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &quot;linmath.h&quot;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;stdlib.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;stddef.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;stdio.h&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="keyword">typedef</span> <span class="keyword">struct </span>Vertex</div>
<div class="line">{</div>
<div class="line">    vec2 pos;</div>
<div class="line">    vec3 col;</div>
<div class="line">} Vertex;</div>
<div class="line"> </div>
<div class="line"><span class="keyword">static</span> <span class="keyword">const</span> Vertex vertices[3] =</div>
<div class="line">{</div>
<div class="line">    { { -0.6f, -0.4f }, { 1.f, 0.f, 0.f } },</div>
<div class="line">    { {  0.6f, -0.4f }, { 0.f, 1.f, 0.f } },</div>
<div class="line">    { {   0.f,  0.6f }, { 0.f, 0.f, 1.f } }</div>
<div class="line">};</div>
<div class="line"> </div>
<div class="line"><span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span>* vertex_shader_text =</div>
<div class="line"><span class="stringliteral">&quot;#version 330\n&quot;</span></div>
<div class="line"><span class="stringliteral">&quot;uniform mat4 MVP;\n&quot;</span></div>
<div class="line"><span class="stringliteral">&quot;in vec3 vCol;\n&quot;</span></div>
<div class="line"><span class="stringliteral">&quot;in vec2 vPos;\n&quot;</span></div>
<div class="line"><span class="stringliteral">&quot;out vec3 color;\n&quot;</span></div>
<div class="line"><span class="stringliteral">&quot;void main()\n&quot;</span></div>
<div class="line"><span class="stringliteral">&quot;{\n&quot;</span></div>
<div class="line"><span class="stringliteral">&quot;    gl_Position = MVP * vec4(vPos, 0.0, 1.0);\n&quot;</span></div>
<div class="line"><span class="stringliteral">&quot;    color = vCol;\n&quot;</span></div>
<div class="line"><span class="stringliteral">&quot;}\n&quot;</span>;</div>
<div class="line"> </div>
<div class="line"><span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span>* fragment_shader_text =</div>
<div class="line"><span class="stringliteral">&quot;#version 330\n&quot;</span></div>
<div class="line"><span class="stringliteral">&quot;in vec3 color;\n&quot;</span></div>
<div class="line"><span class="stringliteral">&quot;out vec4 fragment;\n&quot;</span></div>
<div class="line"><span class="stringliteral">&quot;void main()\n&quot;</span></div>
<div class="line"><span class="stringliteral">&quot;{\n&quot;</span></div>
<div class="line"><span class="stringliteral">&quot;    fragment = vec4(color, 1.0);\n&quot;</span></div>
<div class="line"><span class="stringliteral">&quot;}\n&quot;</span>;</div>
<div class="line"> </div>
<div class="line"><span class="keyword">static</span> <span class="keywordtype">void</span> error_callback(<span class="keywordtype">int</span> error, <span class="keyword">const</span> <span class="keywordtype">char</span>* description)</div>
<div class="line">{</div>
<div class="line">    fprintf(stderr, <span class="stringliteral">&quot;Error: %s\n&quot;</span>, description);</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="keyword">static</span> <span class="keywordtype">void</span> key_callback(<a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window, <span class="keywordtype">int</span> key, <span class="keywordtype">int</span> scancode, <span class="keywordtype">int</span> action, <span class="keywordtype">int</span> mods)</div>
<div class="line">{</div>
<div class="line">    <span class="keywordflow">if</span> (key == <a class="code hl_define" href="group__keys.html#gaac6596c350b635c245113b81c2123b93">GLFW_KEY_ESCAPE</a> &amp;&amp; action == <a class="code hl_define" href="group__input.html#ga2485743d0b59df3791c45951c4195265">GLFW_PRESS</a>)</div>
<div class="line">        <a class="code hl_function" href="group__window.html#ga49c449dde2a6f87d996f4daaa09d6708">glfwSetWindowShouldClose</a>(window, <a class="code hl_define" href="group__init.html#ga2744fbb29b5631bb28802dbe0cf36eba">GLFW_TRUE</a>);</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main(<span class="keywordtype">void</span>)</div>
<div class="line">{</div>
<div class="line">    <a class="code hl_function" href="group__init.html#gaff45816610d53f0b83656092a4034f40">glfwSetErrorCallback</a>(error_callback);</div>
<div class="line"> </div>
<div class="line">    <span class="keywordflow">if</span> (!<a class="code hl_function" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a>())</div>
<div class="line">        exit(EXIT_FAILURE);</div>
<div class="line"> </div>
<div class="line">    <a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#gafe5e4922de1f9932d7e9849bb053b0c0">GLFW_CONTEXT_VERSION_MAJOR</a>, 3);</div>
<div class="line">    <a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#ga31aca791e4b538c4e4a771eb95cc2d07">GLFW_CONTEXT_VERSION_MINOR</a>, 3);</div>
<div class="line">    <a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#ga44f3a6b4261fbe351e0b950b0f372e12">GLFW_OPENGL_PROFILE</a>, <a class="code hl_define" href="glfw3_8h.html#af094bb16da76f66ebceb19ee213b3de8">GLFW_OPENGL_CORE_PROFILE</a>);</div>
<div class="line"> </div>
<div class="line">    <a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window = <a class="code hl_function" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>(640, 480, <span class="stringliteral">&quot;OpenGL Triangle&quot;</span>, NULL, NULL);</div>
<div class="line">    <span class="keywordflow">if</span> (!window)</div>
<div class="line">    {</div>
<div class="line">        <a class="code hl_function" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a>();</div>
<div class="line">        exit(EXIT_FAILURE);</div>
<div class="line">    }</div>
<div class="line"> </div>
<div class="line">    <a class="code hl_function" href="group__input.html#ga1caf18159767e761185e49a3be019f8d">glfwSetKeyCallback</a>(window, key_callback);</div>
<div class="line"> </div>
<div class="line">    <a class="code hl_function" href="group__context.html#ga1c04dc242268f827290fe40aa1c91157">glfwMakeContextCurrent</a>(window);</div>
<div class="line">    gladLoadGL(<a class="code hl_function" href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a>);</div>
<div class="line">    <a class="code hl_function" href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed">glfwSwapInterval</a>(1);</div>
<div class="line"> </div>
<div class="line">    <span class="comment">// NOTE: OpenGL error checks have been omitted for brevity</span></div>
<div class="line"> </div>
<div class="line">    GLuint vertex_buffer;</div>
<div class="line">    glGenBuffers(1, &amp;vertex_buffer);</div>
<div class="line">    glBindBuffer(GL_ARRAY_BUFFER, vertex_buffer);</div>
<div class="line">    glBufferData(GL_ARRAY_BUFFER, <span class="keyword">sizeof</span>(vertices), vertices, GL_STATIC_DRAW);</div>
<div class="line"> </div>
<div class="line">    <span class="keyword">const</span> GLuint vertex_shader = glCreateShader(GL_VERTEX_SHADER);</div>
<div class="line">    glShaderSource(vertex_shader, 1, &amp;vertex_shader_text, NULL);</div>
<div class="line">    glCompileShader(vertex_shader);</div>
<div class="line"> </div>
<div class="line">    <span class="keyword">const</span> GLuint fragment_shader = glCreateShader(GL_FRAGMENT_SHADER);</div>
<div class="line">    glShaderSource(fragment_shader, 1, &amp;fragment_shader_text, NULL);</div>
<div class="line">    glCompileShader(fragment_shader);</div>
<div class="line"> </div>
<div class="line">    <span class="keyword">const</span> GLuint program = glCreateProgram();</div>
<div class="line">    glAttachShader(program, vertex_shader);</div>
<div class="line">    glAttachShader(program, fragment_shader);</div>
<div class="line">    glLinkProgram(program);</div>
<div class="line"> </div>
<div class="line">    <span class="keyword">const</span> GLint mvp_location = glGetUniformLocation(program, <span class="stringliteral">&quot;MVP&quot;</span>);</div>
<div class="line">    <span class="keyword">const</span> GLint vpos_location = glGetAttribLocation(program, <span class="stringliteral">&quot;vPos&quot;</span>);</div>
<div class="line">    <span class="keyword">const</span> GLint vcol_location = glGetAttribLocation(program, <span class="stringliteral">&quot;vCol&quot;</span>);</div>
<div class="line"> </div>
<div class="line">    GLuint vertex_array;</div>
<div class="line">    glGenVertexArrays(1, &amp;vertex_array);</div>
<div class="line">    glBindVertexArray(vertex_array);</div>
<div class="line">    glEnableVertexAttribArray(vpos_location);</div>
<div class="line">    glVertexAttribPointer(vpos_location, 2, GL_FLOAT, GL_FALSE,</div>
<div class="line">                          <span class="keyword">sizeof</span>(Vertex), (<span class="keywordtype">void</span>*) offsetof(Vertex, pos));</div>
<div class="line">    glEnableVertexAttribArray(vcol_location);</div>
<div class="line">    glVertexAttribPointer(vcol_location, 3, GL_FLOAT, GL_FALSE,</div>
<div class="line">                          <span class="keyword">sizeof</span>(Vertex), (<span class="keywordtype">void</span>*) offsetof(Vertex, col));</div>
<div class="line"> </div>
<div class="line">    <span class="keywordflow">while</span> (!<a class="code hl_function" href="group__window.html#ga24e02fbfefbb81fc45320989f8140ab5">glfwWindowShouldClose</a>(window))</div>
<div class="line">    {</div>
<div class="line">        <span class="keywordtype">int</span> width, height;</div>
<div class="line">        <a class="code hl_function" href="group__window.html#ga0e2637a4161afb283f5300c7f94785c9">glfwGetFramebufferSize</a>(window, &amp;width, &amp;height);</div>
<div class="line">        <span class="keyword">const</span> <span class="keywordtype">float</span> ratio = width / (float) height;</div>
<div class="line"> </div>
<div class="line">        glViewport(0, 0, width, height);</div>
<div class="line">        glClear(GL_COLOR_BUFFER_BIT);</div>
<div class="line"> </div>
<div class="line">        mat4x4 m, p, mvp;</div>
<div class="line">        mat4x4_identity(m);</div>
<div class="line">        mat4x4_rotate_Z(m, m, (<span class="keywordtype">float</span>) <a class="code hl_function" href="group__input.html#gaa6cf4e7a77158a3b8fd00328b1720a4a">glfwGetTime</a>());</div>
<div class="line">        mat4x4_ortho(p, -ratio, ratio, -1.f, 1.f, 1.f, -1.f);</div>
<div class="line">        mat4x4_mul(mvp, p, m);</div>
<div class="line"> </div>
<div class="line">        glUseProgram(program);</div>
<div class="line">        glUniformMatrix4fv(mvp_location, 1, GL_FALSE, (<span class="keyword">const</span> GLfloat*) &amp;mvp);</div>
<div class="line">        glBindVertexArray(vertex_array);</div>
<div class="line">        glDrawArrays(GL_TRIANGLES, 0, 3);</div>
<div class="line"> </div>
<div class="line">        <a class="code hl_function" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a>(window);</div>
<div class="line">        <a class="code hl_function" href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a>();</div>
<div class="line">    }</div>
<div class="line"> </div>
<div class="line">    <a class="code hl_function" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a>(window);</div>
<div class="line"> </div>
<div class="line">    <a class="code hl_function" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a>();</div>
<div class="line">    exit(EXIT_SUCCESS);</div>
<div class="line">}</div>
<div class="line"> </div>
</div><!-- fragment --><p>The program above can be found in the <a href="https://www.glfw.org/download.html">source package</a> as <code>examples/triangle-opengl.c</code> and is compiled along with all other examples when you build GLFW. If you built GLFW from the source package then you already have this as <code>triangle-opengl.exe</code> on Windows, <code>triangle-opengl</code> on Linux or <code>triangle-opengl.app</code> on macOS.</p>
<p>This tutorial used only a few of the many functions GLFW provides. There are guides for each of the areas covered by GLFW. Each guide will introduce all the functions for that category.</p>
<ul>
<li><a class="el" href="intro_guide.html">Introduction to the API</a></li>
<li><a class="el" href="window_guide.html">Window guide</a></li>
<li><a class="el" href="context_guide.html">Context guide</a></li>
<li><a class="el" href="monitor_guide.html">Monitor guide</a></li>
<li><a class="el" href="input_guide.html">Input guide</a></li>
</ul>
<p>You can access reference documentation for any GLFW function by clicking it and the reference for each function links to related functions and guide sections.</p>
<p>The tutorial ends here. Once you have written a program that uses GLFW, you will need to compile and link it. How to do that depends on the development environment you are using and is best explained by the documentation for that environment. To learn about the details that are specific to GLFW, see <a class="el" href="build_guide.html">Building applications</a>. </p>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
