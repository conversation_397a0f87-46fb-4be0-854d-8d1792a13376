{"sources": [{"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/include/opencv2/ts.hpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/include/opencv2/ts/cuda_perf.hpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/include/opencv2/ts/cuda_test.hpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/include/opencv2/ts/ocl_perf.hpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/include/opencv2/ts/ocl_test.hpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/include/opencv2/ts/ts_ext.hpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/include/opencv2/ts/ts_gtest.h", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/include/opencv2/ts/ts_perf.hpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/src/cuda_perf.cpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/src/cuda_test.cpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/src/ocl_perf.cpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/src/ocl_test.cpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/src/ts.cpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/src/ts_arrtest.cpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/src/ts_func.cpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/src/ts_gtest.cpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/src/ts_perf.cpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/src/ts_tags.cpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/src/precomp.hpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/ts/src/ts_tags.hpp", "labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/AI/opencv/cudabuild/cvconfig.h"}, {"file": "D:/AI/opencv/cudabuild/opencv2/opencv_modules.hpp"}], "target": {"labels": ["Main", "opencv_ts", "<PERSON><PERSON><PERSON>"], "name": "opencv_ts"}}