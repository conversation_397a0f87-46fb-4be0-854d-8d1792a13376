// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Input_1_H
#define WINRT_Windows_UI_Xaml_Input_1_H
#include "winrt/impl/Windows.UI.Xaml.Input.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Input
{
    struct WINRT_IMPL_EMPTY_BASES IAccessKeyDisplayDismissedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccessKeyDisplayDismissedEventArgs>
    {
        IAccessKeyDisplayDismissedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAccessKeyDisplayDismissedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccessKeyDisplayRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccessKeyDisplayRequestedEventArgs>
    {
        IAccessKeyDisplayRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAccessKeyDisplayRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccessKeyInvokedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccessKeyInvokedEventArgs>
    {
        IAccessKeyInvokedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAccessKeyInvokedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccessKeyManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccessKeyManager>
    {
        IAccessKeyManager(std::nullptr_t = nullptr) noexcept {}
        IAccessKeyManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccessKeyManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccessKeyManagerStatics>
    {
        IAccessKeyManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IAccessKeyManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAccessKeyManagerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAccessKeyManagerStatics2>
    {
        IAccessKeyManagerStatics2(std::nullptr_t = nullptr) noexcept {}
        IAccessKeyManagerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICanExecuteRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICanExecuteRequestedEventArgs>
    {
        ICanExecuteRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICanExecuteRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICharacterReceivedRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICharacterReceivedRoutedEventArgs>
    {
        ICharacterReceivedRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICharacterReceivedRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICommand :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommand>
    {
        ICommand(std::nullptr_t = nullptr) noexcept {}
        ICommand(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IContextRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContextRequestedEventArgs>
    {
        IContextRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IContextRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDoubleTappedRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDoubleTappedRoutedEventArgs>
    {
        IDoubleTappedRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDoubleTappedRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IExecuteRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExecuteRequestedEventArgs>
    {
        IExecuteRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IExecuteRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFindNextElementOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFindNextElementOptions>
    {
        IFindNextElementOptions(std::nullptr_t = nullptr) noexcept {}
        IFindNextElementOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFocusManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusManager>
    {
        IFocusManager(std::nullptr_t = nullptr) noexcept {}
        IFocusManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFocusManagerGotFocusEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusManagerGotFocusEventArgs>
    {
        IFocusManagerGotFocusEventArgs(std::nullptr_t = nullptr) noexcept {}
        IFocusManagerGotFocusEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFocusManagerLostFocusEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusManagerLostFocusEventArgs>
    {
        IFocusManagerLostFocusEventArgs(std::nullptr_t = nullptr) noexcept {}
        IFocusManagerLostFocusEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFocusManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusManagerStatics>
    {
        IFocusManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IFocusManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFocusManagerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusManagerStatics2>
    {
        IFocusManagerStatics2(std::nullptr_t = nullptr) noexcept {}
        IFocusManagerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFocusManagerStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusManagerStatics3>
    {
        IFocusManagerStatics3(std::nullptr_t = nullptr) noexcept {}
        IFocusManagerStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFocusManagerStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusManagerStatics4>
    {
        IFocusManagerStatics4(std::nullptr_t = nullptr) noexcept {}
        IFocusManagerStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFocusManagerStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusManagerStatics5>
    {
        IFocusManagerStatics5(std::nullptr_t = nullptr) noexcept {}
        IFocusManagerStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFocusManagerStatics6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusManagerStatics6>
    {
        IFocusManagerStatics6(std::nullptr_t = nullptr) noexcept {}
        IFocusManagerStatics6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFocusManagerStatics7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusManagerStatics7>
    {
        IFocusManagerStatics7(std::nullptr_t = nullptr) noexcept {}
        IFocusManagerStatics7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFocusMovementResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusMovementResult>
    {
        IFocusMovementResult(std::nullptr_t = nullptr) noexcept {}
        IFocusMovementResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGettingFocusEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGettingFocusEventArgs>
    {
        IGettingFocusEventArgs(std::nullptr_t = nullptr) noexcept {}
        IGettingFocusEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGettingFocusEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGettingFocusEventArgs2>
    {
        IGettingFocusEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IGettingFocusEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGettingFocusEventArgs3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGettingFocusEventArgs3>
    {
        IGettingFocusEventArgs3(std::nullptr_t = nullptr) noexcept {}
        IGettingFocusEventArgs3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHoldingRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHoldingRoutedEventArgs>
    {
        IHoldingRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IHoldingRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInertiaExpansionBehavior :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInertiaExpansionBehavior>
    {
        IInertiaExpansionBehavior(std::nullptr_t = nullptr) noexcept {}
        IInertiaExpansionBehavior(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInertiaRotationBehavior :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInertiaRotationBehavior>
    {
        IInertiaRotationBehavior(std::nullptr_t = nullptr) noexcept {}
        IInertiaRotationBehavior(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInertiaTranslationBehavior :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInertiaTranslationBehavior>
    {
        IInertiaTranslationBehavior(std::nullptr_t = nullptr) noexcept {}
        IInertiaTranslationBehavior(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInputScope :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInputScope>
    {
        IInputScope(std::nullptr_t = nullptr) noexcept {}
        IInputScope(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInputScopeName :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInputScopeName>
    {
        IInputScopeName(std::nullptr_t = nullptr) noexcept {}
        IInputScopeName(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInputScopeNameFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInputScopeNameFactory>
    {
        IInputScopeNameFactory(std::nullptr_t = nullptr) noexcept {}
        IInputScopeNameFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyRoutedEventArgs>
    {
        IKeyRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IKeyRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyRoutedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyRoutedEventArgs2>
    {
        IKeyRoutedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IKeyRoutedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyRoutedEventArgs3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyRoutedEventArgs3>
    {
        IKeyRoutedEventArgs3(std::nullptr_t = nullptr) noexcept {}
        IKeyRoutedEventArgs3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyboardAccelerator :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyboardAccelerator>
    {
        IKeyboardAccelerator(std::nullptr_t = nullptr) noexcept {}
        IKeyboardAccelerator(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyboardAcceleratorFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyboardAcceleratorFactory>
    {
        IKeyboardAcceleratorFactory(std::nullptr_t = nullptr) noexcept {}
        IKeyboardAcceleratorFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyboardAcceleratorInvokedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyboardAcceleratorInvokedEventArgs>
    {
        IKeyboardAcceleratorInvokedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IKeyboardAcceleratorInvokedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyboardAcceleratorInvokedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyboardAcceleratorInvokedEventArgs2>
    {
        IKeyboardAcceleratorInvokedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IKeyboardAcceleratorInvokedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyboardAcceleratorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyboardAcceleratorStatics>
    {
        IKeyboardAcceleratorStatics(std::nullptr_t = nullptr) noexcept {}
        IKeyboardAcceleratorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILosingFocusEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILosingFocusEventArgs>
    {
        ILosingFocusEventArgs(std::nullptr_t = nullptr) noexcept {}
        ILosingFocusEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILosingFocusEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILosingFocusEventArgs2>
    {
        ILosingFocusEventArgs2(std::nullptr_t = nullptr) noexcept {}
        ILosingFocusEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILosingFocusEventArgs3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILosingFocusEventArgs3>
    {
        ILosingFocusEventArgs3(std::nullptr_t = nullptr) noexcept {}
        ILosingFocusEventArgs3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IManipulationCompletedRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManipulationCompletedRoutedEventArgs>
    {
        IManipulationCompletedRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IManipulationCompletedRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IManipulationDeltaRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManipulationDeltaRoutedEventArgs>
    {
        IManipulationDeltaRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IManipulationDeltaRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IManipulationInertiaStartingRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManipulationInertiaStartingRoutedEventArgs>
    {
        IManipulationInertiaStartingRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IManipulationInertiaStartingRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IManipulationPivot :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManipulationPivot>
    {
        IManipulationPivot(std::nullptr_t = nullptr) noexcept {}
        IManipulationPivot(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IManipulationPivotFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManipulationPivotFactory>
    {
        IManipulationPivotFactory(std::nullptr_t = nullptr) noexcept {}
        IManipulationPivotFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IManipulationStartedRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManipulationStartedRoutedEventArgs>
    {
        IManipulationStartedRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IManipulationStartedRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IManipulationStartedRoutedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManipulationStartedRoutedEventArgsFactory>
    {
        IManipulationStartedRoutedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IManipulationStartedRoutedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IManipulationStartingRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManipulationStartingRoutedEventArgs>
    {
        IManipulationStartingRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IManipulationStartingRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INoFocusCandidateFoundEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INoFocusCandidateFoundEventArgs>
    {
        INoFocusCandidateFoundEventArgs(std::nullptr_t = nullptr) noexcept {}
        INoFocusCandidateFoundEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointer>
    {
        IPointer(std::nullptr_t = nullptr) noexcept {}
        IPointer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerRoutedEventArgs>
    {
        IPointerRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPointerRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerRoutedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerRoutedEventArgs2>
    {
        IPointerRoutedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IPointerRoutedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProcessKeyboardAcceleratorEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProcessKeyboardAcceleratorEventArgs>
    {
        IProcessKeyboardAcceleratorEventArgs(std::nullptr_t = nullptr) noexcept {}
        IProcessKeyboardAcceleratorEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRightTappedRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRightTappedRoutedEventArgs>
    {
        IRightTappedRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRightTappedRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStandardUICommand :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStandardUICommand>
    {
        IStandardUICommand(std::nullptr_t = nullptr) noexcept {}
        IStandardUICommand(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStandardUICommand2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStandardUICommand2>
    {
        IStandardUICommand2(std::nullptr_t = nullptr) noexcept {}
        IStandardUICommand2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStandardUICommandFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStandardUICommandFactory>
    {
        IStandardUICommandFactory(std::nullptr_t = nullptr) noexcept {}
        IStandardUICommandFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStandardUICommandStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStandardUICommandStatics>
    {
        IStandardUICommandStatics(std::nullptr_t = nullptr) noexcept {}
        IStandardUICommandStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITappedRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITappedRoutedEventArgs>
    {
        ITappedRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITappedRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlUICommand :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlUICommand>
    {
        IXamlUICommand(std::nullptr_t = nullptr) noexcept {}
        IXamlUICommand(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlUICommandFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlUICommandFactory>
    {
        IXamlUICommandFactory(std::nullptr_t = nullptr) noexcept {}
        IXamlUICommandFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlUICommandStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlUICommandStatics>
    {
        IXamlUICommandStatics(std::nullptr_t = nullptr) noexcept {}
        IXamlUICommandStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
