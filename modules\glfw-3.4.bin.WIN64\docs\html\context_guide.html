<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Context guide</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div><div class="header">
  <div class="headertitle"><div class="title">Context guide</div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#context_object">Context objects</a><ul><li class="level2"><a href="#context_hints">Context creation hints</a></li>
<li class="level2"><a href="#context_sharing">Context object sharing</a></li>
<li class="level2"><a href="#context_offscreen">Offscreen contexts</a></li>
<li class="level2"><a href="#context_less">Windows without contexts</a></li>
</ul>
</li>
<li class="level1"><a href="#context_current">Current context</a></li>
<li class="level1"><a href="#context_swap">Buffer swapping</a></li>
<li class="level1"><a href="#context_glext">OpenGL and OpenGL ES extensions</a><ul><li class="level2"><a href="#context_glext_auto">Loading extension with a loader library</a></li>
<li class="level2"><a href="#context_glext_manual">Loading extensions manually</a><ul><li class="level3"><a href="#context_glext_header">The glext.h header</a></li>
<li class="level3"><a href="#context_glext_string">Checking for extensions</a></li>
<li class="level3"><a href="#context_glext_proc">Fetching function pointers</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="textblock"><p>This guide introduces the OpenGL and OpenGL ES context related functions of GLFW. For details on a specific function in this category, see the <a class="el" href="group__context.html">Context reference</a>. There are also guides for the other areas of the GLFW API.</p>
<ul>
<li><a class="el" href="intro_guide.html">Introduction to the API</a></li>
<li><a class="el" href="window_guide.html">Window guide</a></li>
<li><a class="el" href="vulkan_guide.html">Vulkan guide</a></li>
<li><a class="el" href="monitor_guide.html">Monitor guide</a></li>
<li><a class="el" href="input_guide.html">Input guide</a></li>
</ul>
<h1><a class="anchor" id="context_object"></a>
Context objects</h1>
<p>A window object encapsulates both a top-level window and an OpenGL or OpenGL ES context. It is created with <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a> and destroyed with <a class="el" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a> or <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a>. See <a class="el" href="window_guide.html#window_creation">Window creation</a> for more information.</p>
<p>As the window and context are inseparably linked, the window object also serves as the context handle.</p>
<p>To test the creation of various kinds of contexts and see their properties, run the <code>glfwinfo</code> test program.</p>
<dl class="section note"><dt>Note</dt><dd>Vulkan does not have a context and the Vulkan instance is created via the Vulkan API itself. If you will be using Vulkan to render to a window, disable context creation by setting the <a class="el" href="window_guide.html#GLFW_CLIENT_API_hint">GLFW_CLIENT_API</a> hint to <code>GLFW_NO_API</code>. For more information, see the <a class="el" href="vulkan_guide.html">Vulkan guide</a>.</dd></dl>
<h2><a class="anchor" id="context_hints"></a>
Context creation hints</h2>
<p>There are a number of hints, specified using <a class="el" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>, related to what kind of context is created. See <a class="el" href="window_guide.html#window_hints_ctx">context related hints</a> in the window guide.</p>
<h2><a class="anchor" id="context_sharing"></a>
Context object sharing</h2>
<p>When creating a window and its OpenGL or OpenGL ES context with <a class="el" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>, you can specify another window whose context the new one should share its objects (textures, vertex and element buffers, etc.) with.</p>
<div class="fragment"><div class="line"><a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* second_window = <a class="code hl_function" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>(640, 480, <span class="stringliteral">&quot;Second Window&quot;</span>, NULL, first_window);</div>
<div class="ttc" id="agroup__window_html_ga3555a418df92ad53f917597fe2f64aeb"><div class="ttname"><a href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a></div><div class="ttdeci">GLFWwindow * glfwCreateWindow(int width, int height, const char *title, GLFWmonitor *monitor, GLFWwindow *share)</div><div class="ttdoc">Creates a window and its associated context.</div></div>
<div class="ttc" id="agroup__window_html_ga3c96d80d363e67d13a41b5d1821f3242"><div class="ttname"><a href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a></div><div class="ttdeci">struct GLFWwindow GLFWwindow</div><div class="ttdoc">Opaque window object.</div><div class="ttdef"><b>Definition</b> glfw3.h:1403</div></div>
</div><!-- fragment --><p>Object sharing is implemented by the operating system and graphics driver. On platforms where it is possible to choose which types of objects are shared, GLFW requests that all types are shared.</p>
<p>See the relevant chapter of the <a href="https://www.opengl.org/registry/">OpenGL</a> or <a href="https://www.khronos.org/opengles/">OpenGL ES</a> reference documents for more information. The name and number of this chapter unfortunately varies between versions and APIs, but has at times been named <em>Shared Objects and Multiple Contexts</em>.</p>
<p>GLFW comes with a bare-bones object sharing example program called <code>sharing</code>.</p>
<h2><a class="anchor" id="context_offscreen"></a>
Offscreen contexts</h2>
<p>GLFW doesn't support creating contexts without an associated window. However, contexts with hidden windows can be created with the <a class="el" href="window_guide.html#GLFW_VISIBLE_hint">GLFW_VISIBLE</a> window hint.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a>(<a class="code hl_define" href="group__window.html#gafb3cdc45297e06d8f1eb13adc69ca6c4">GLFW_VISIBLE</a>, <a class="code hl_define" href="group__init.html#gac877fe3b627d21ef3a0a23e0a73ba8c5">GLFW_FALSE</a>);</div>
<div class="line"> </div>
<div class="line"><a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* offscreen_context = <a class="code hl_function" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>(640, 480, <span class="stringliteral">&quot;&quot;</span>, NULL, NULL);</div>
<div class="ttc" id="agroup__init_html_gac877fe3b627d21ef3a0a23e0a73ba8c5"><div class="ttname"><a href="group__init.html#gac877fe3b627d21ef3a0a23e0a73ba8c5">GLFW_FALSE</a></div><div class="ttdeci">#define GLFW_FALSE</div><div class="ttdoc">Zero.</div><div class="ttdef"><b>Definition</b> glfw3.h:321</div></div>
<div class="ttc" id="agroup__window_html_ga7d9c8c62384b1e2821c4dc48952d2033"><div class="ttname"><a href="group__window.html#ga7d9c8c62384b1e2821c4dc48952d2033">glfwWindowHint</a></div><div class="ttdeci">void glfwWindowHint(int hint, int value)</div><div class="ttdoc">Sets the specified window hint to the desired value.</div></div>
<div class="ttc" id="agroup__window_html_gafb3cdc45297e06d8f1eb13adc69ca6c4"><div class="ttname"><a href="group__window.html#gafb3cdc45297e06d8f1eb13adc69ca6c4">GLFW_VISIBLE</a></div><div class="ttdeci">#define GLFW_VISIBLE</div><div class="ttdoc">Window visibility window hint and attribute.</div><div class="ttdef"><b>Definition</b> glfw3.h:876</div></div>
</div><!-- fragment --><p>The window never needs to be shown and its context can be used as a plain offscreen context. Depending on the window manager, the size of a hidden window's framebuffer may not be usable or modifiable, so framebuffer objects are recommended for rendering with such contexts.</p>
<p>You should still <a class="el" href="input_guide.html#events">process events</a> as long as you have at least one window, even if none of them are visible.</p>
<h2><a class="anchor" id="context_less"></a>
Windows without contexts</h2>
<p>You can disable context creation by setting the <a class="el" href="window_guide.html#GLFW_CLIENT_API_hint">GLFW_CLIENT_API</a> hint to <code>GLFW_NO_API</code>.</p>
<p>Windows without contexts should not be passed to <a class="el" href="group__context.html#ga1c04dc242268f827290fe40aa1c91157">glfwMakeContextCurrent</a> or <a class="el" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a>. Doing this generates a <a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a> error.</p>
<h1><a class="anchor" id="context_current"></a>
Current context</h1>
<p>Before you can make OpenGL or OpenGL ES calls, you need to have a current context of the correct type. A context can only be current for a single thread at a time, and a thread can only have a single context current at a time.</p>
<p>When moving a context between threads, you must make it non-current on the old thread before making it current on the new one.</p>
<p>The context of a window is made current with <a class="el" href="group__context.html#ga1c04dc242268f827290fe40aa1c91157">glfwMakeContextCurrent</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__context.html#ga1c04dc242268f827290fe40aa1c91157">glfwMakeContextCurrent</a>(window);</div>
<div class="ttc" id="agroup__context_html_ga1c04dc242268f827290fe40aa1c91157"><div class="ttname"><a href="group__context.html#ga1c04dc242268f827290fe40aa1c91157">glfwMakeContextCurrent</a></div><div class="ttdeci">void glfwMakeContextCurrent(GLFWwindow *window)</div><div class="ttdoc">Makes the context of the specified window current for the calling thread.</div></div>
</div><!-- fragment --><p>The window of the current context is returned by <a class="el" href="group__context.html#gad94e80185397a6cf5fe2ab30567af71c">glfwGetCurrentContext</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_typedef" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a>* window = <a class="code hl_function" href="group__context.html#gad94e80185397a6cf5fe2ab30567af71c">glfwGetCurrentContext</a>();</div>
<div class="ttc" id="agroup__context_html_gad94e80185397a6cf5fe2ab30567af71c"><div class="ttname"><a href="group__context.html#gad94e80185397a6cf5fe2ab30567af71c">glfwGetCurrentContext</a></div><div class="ttdeci">GLFWwindow * glfwGetCurrentContext(void)</div><div class="ttdoc">Returns the window whose context is current on the calling thread.</div></div>
</div><!-- fragment --><p>The following GLFW functions require a context to be current. Calling any these functions without a current context will generate a <a class="el" href="group__errors.html#gaa8290386e9528ccb9e42a3a4e16fc0d0">GLFW_NO_CURRENT_CONTEXT</a> error.</p>
<ul>
<li><a class="el" href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed">glfwSwapInterval</a></li>
<li><a class="el" href="group__context.html#ga87425065c011cef1ebd6aac75e059dfa">glfwExtensionSupported</a></li>
<li><a class="el" href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a></li>
</ul>
<h1><a class="anchor" id="context_swap"></a>
Buffer swapping</h1>
<p>See <a class="el" href="window_guide.html#buffer_swap">Buffer swapping</a> in the window guide.</p>
<h1><a class="anchor" id="context_glext"></a>
OpenGL and OpenGL ES extensions</h1>
<p>One of the benefits of OpenGL and OpenGL ES is their extensibility. Hardware vendors may include extensions in their implementations that extend the API before that functionality is included in a new version of the OpenGL or OpenGL ES specification, and some extensions are never included and remain as extensions until they become obsolete.</p>
<p>An extension is defined by:</p>
<ul>
<li>An extension name (e.g. <code>GL_ARB_gl_spirv</code>)</li>
<li>New OpenGL tokens (e.g. <code>GL_SPIR_V_BINARY_ARB</code>)</li>
<li>New OpenGL functions (e.g. <code>glSpecializeShaderARB</code>)</li>
</ul>
<p>Note the <code>ARB</code> affix, which stands for Architecture Review Board and is used for official extensions. The extension above was created by the ARB, but there are many different affixes, like <code>NV</code> for Nvidia and <code>AMD</code> for, well, AMD. Any group may also use the generic <code>EXT</code> affix. Lists of extensions, together with their specifications, can be found at the <a href="https://www.opengl.org/registry/">OpenGL Registry</a> and <a href="https://www.khronos.org/registry/gles/">OpenGL ES Registry</a>.</p>
<h2><a class="anchor" id="context_glext_auto"></a>
Loading extension with a loader library</h2>
<p>An extension loader library is the easiest and best way to access both OpenGL and OpenGL ES extensions and modern versions of the core OpenGL or OpenGL ES APIs. They will take care of all the details of declaring and loading everything you need. One such library is <a href="https://github.com/Dav1dde/glad">glad</a> and there are several others.</p>
<p>The following example will use glad but all extension loader libraries work similarly.</p>
<p>First you need to generate the source files using the glad Python script. This example generates a loader for any version of OpenGL, which is the default for both GLFW and glad, but loaders for OpenGL ES, as well as loaders for specific API versions and extension sets can be generated. The generated files are written to the <code>output</code> directory.</p>
<div class="fragment"><div class="line">python main.py --generator c --no-loader --out-path output</div>
</div><!-- fragment --><p>The <code>--no-loader</code> option is added because GLFW already provides a function for loading OpenGL and OpenGL ES function pointers, one that automatically uses the selected context creation API, and glad can call this instead of having to implement its own. There are several other command-line options as well. See the glad documentation for details.</p>
<p>Add the generated <code>output/src/glad.c</code>, <code>output/include/glad/glad.h</code> and <code>output/include/KHR/khrplatform.h</code> files to your build. Then you need to include the glad header file, which will replace the OpenGL header of your development environment. By including the glad header before the GLFW header, it suppresses the development environment's OpenGL or OpenGL ES header.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#include &lt;glad/glad.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3_8h.html">GLFW/glfw3.h</a>&gt;</span></div>
<div class="ttc" id="aglfw3_8h_html"><div class="ttname"><a href="glfw3_8h.html">glfw3.h</a></div><div class="ttdoc">The header of the GLFW 3 API.</div></div>
</div><!-- fragment --><p>Finally, you need to initialize glad once you have a suitable current context.</p>
<div class="fragment"><div class="line">window = <a class="code hl_function" href="group__window.html#ga3555a418df92ad53f917597fe2f64aeb">glfwCreateWindow</a>(640, 480, <span class="stringliteral">&quot;My Window&quot;</span>, NULL, NULL);</div>
<div class="line"><span class="keywordflow">if</span> (!window)</div>
<div class="line">{</div>
<div class="line">    ...</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><a class="code hl_function" href="group__context.html#ga1c04dc242268f827290fe40aa1c91157">glfwMakeContextCurrent</a>(window);</div>
<div class="line"> </div>
<div class="line">gladLoadGLLoader((GLADloadproc) <a class="code hl_function" href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a>);</div>
<div class="ttc" id="agroup__context_html_ga35f1837e6f666781842483937612f163"><div class="ttname"><a href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a></div><div class="ttdeci">GLFWglproc glfwGetProcAddress(const char *procname)</div><div class="ttdoc">Returns the address of the specified function for the current context.</div></div>
</div><!-- fragment --><p>Once glad has been loaded, you have access to all OpenGL core and extension functions supported by both the context you created and the glad loader you generated. After that, you are ready to start rendering.</p>
<p>You can specify a minimum required OpenGL or OpenGL ES version with <a class="el" href="window_guide.html#window_hints_ctx">context hints</a>. If your needs are more complex, you can check the actual OpenGL or OpenGL ES version with <a class="el" href="window_guide.html#window_attribs_ctx">context attributes</a>, or you can check whether a specific version is supported by the current context with the <code>GLAD_GL_VERSION_x_x</code> booleans.</p>
<div class="fragment"><div class="line"><span class="keywordflow">if</span> (GLAD_GL_VERSION_3_2)</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// Call OpenGL 3.2+ specific code</span></div>
<div class="line">}</div>
</div><!-- fragment --><p>To check whether a specific extension is supported, use the <code>GLAD_GL_xxx</code> booleans.</p>
<div class="fragment"><div class="line"><span class="keywordflow">if</span> (GLAD_GL_ARB_gl_spirv)</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// Use GL_ARB_gl_spirv</span></div>
<div class="line">}</div>
</div><!-- fragment --><h2><a class="anchor" id="context_glext_manual"></a>
Loading extensions manually</h2>
<p><b>Do not use this technique</b> unless it is absolutely necessary. An <a class="el" href="context_guide.html#context_glext_auto">extension loader library</a> will save you a ton of tedious, repetitive, error prone work.</p>
<p>To use a certain extension, you must first check whether the context supports that extension and then, if it introduces new functions, retrieve the pointers to those functions. GLFW provides <a class="el" href="group__context.html#ga87425065c011cef1ebd6aac75e059dfa">glfwExtensionSupported</a> and <a class="el" href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a> for manual loading of extensions and new API functions.</p>
<p>This section will demonstrate manual loading of OpenGL extensions. The loading of OpenGL ES extensions is identical except for the name of the extension header.</p>
<h3><a class="anchor" id="context_glext_header"></a>
The glext.h header</h3>
<p>The <code>glext.h</code> extension header is a continually updated file that defines the interfaces for all OpenGL extensions. The latest version of this can always be found at the <a href="https://www.opengl.org/registry/">OpenGL Registry</a>. There are also extension headers for the various versions of OpenGL ES at the <a href="https://www.khronos.org/registry/gles/">OpenGL ES Registry</a>. It it strongly recommended that you use your own copy of the extension header, as the one included in your development environment may be several years out of date and may not include the extensions you wish to use.</p>
<p>The header defines function pointer types for all functions of all extensions it supports. These have names like <code>PFNGLSPECIALIZESHADERARBPROC</code> (for <code>glSpecializeShaderARB</code>), i.e. the name is made uppercase and <code>PFN</code> (pointer to function) and <code>PROC</code> (procedure) are added to the ends.</p>
<p>To include the extension header, define <a class="el" href="build_guide.html#GLFW_INCLUDE_GLEXT">GLFW_INCLUDE_GLEXT</a> before including the GLFW header.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#define GLFW_INCLUDE_GLEXT</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3_8h.html">GLFW/glfw3.h</a>&gt;</span></div>
</div><!-- fragment --><h3><a class="anchor" id="context_glext_string"></a>
Checking for extensions</h3>
<p>A given machine may not actually support the extension (it may have older drivers or a graphics card that lacks the necessary hardware features), so it is necessary to check at run-time whether the context supports the extension. This is done with <a class="el" href="group__context.html#ga87425065c011cef1ebd6aac75e059dfa">glfwExtensionSupported</a>.</p>
<div class="fragment"><div class="line"><span class="keywordflow">if</span> (<a class="code hl_function" href="group__context.html#ga87425065c011cef1ebd6aac75e059dfa">glfwExtensionSupported</a>(<span class="stringliteral">&quot;GL_ARB_gl_spirv&quot;</span>))</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// The extension is supported by the current context</span></div>
<div class="line">}</div>
<div class="ttc" id="agroup__context_html_ga87425065c011cef1ebd6aac75e059dfa"><div class="ttname"><a href="group__context.html#ga87425065c011cef1ebd6aac75e059dfa">glfwExtensionSupported</a></div><div class="ttdeci">int glfwExtensionSupported(const char *extension)</div><div class="ttdoc">Returns whether the specified extension is available.</div></div>
</div><!-- fragment --><p>The argument is a null terminated ASCII string with the extension name. If the extension is supported, <a class="el" href="group__context.html#ga87425065c011cef1ebd6aac75e059dfa">glfwExtensionSupported</a> returns <code>GLFW_TRUE</code>, otherwise it returns <code>GLFW_FALSE</code>.</p>
<h3><a class="anchor" id="context_glext_proc"></a>
Fetching function pointers</h3>
<p>Many extensions, though not all, require the use of new OpenGL functions. These functions often do not have entry points in the client API libraries of your operating system, making it necessary to fetch them at run time. You can retrieve pointers to these functions with <a class="el" href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a>.</p>
<div class="fragment"><div class="line">PFNGLSPECIALIZESHADERARBPROC pfnSpecializeShaderARB = <a class="code hl_function" href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a>(<span class="stringliteral">&quot;glSpecializeShaderARB&quot;</span>);</div>
</div><!-- fragment --><p>In general, you should avoid giving the function pointer variables the (exact) same name as the function, as this may confuse your linker. Instead, you can use a different prefix, like above, or some other naming scheme.</p>
<p>Now that all the pieces have been introduced, here is what they might look like when used together.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#define GLFW_INCLUDE_GLEXT</span></div>
<div class="line"><span class="preprocessor">#include &lt;<a class="code" href="glfw3_8h.html">GLFW/glfw3.h</a>&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#define glSpecializeShaderARB pfnSpecializeShaderARB</span></div>
<div class="line">PFNGLSPECIALIZESHADERARBPROC pfnSpecializeShaderARB;</div>
<div class="line"> </div>
<div class="line"><span class="comment">// Flag indicating whether the extension is supported</span></div>
<div class="line"><span class="keywordtype">int</span> has_ARB_gl_spirv = 0;</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> load_extensions(<span class="keywordtype">void</span>)</div>
<div class="line">{</div>
<div class="line">    <span class="keywordflow">if</span> (<a class="code hl_function" href="group__context.html#ga87425065c011cef1ebd6aac75e059dfa">glfwExtensionSupported</a>(<span class="stringliteral">&quot;GL_ARB_gl_spirv&quot;</span>))</div>
<div class="line">    {</div>
<div class="line">        pfnSpecializeShaderARB = (PFNGLSPECIALIZESHADERARBPROC)</div>
<div class="line">            <a class="code hl_function" href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a>(<span class="stringliteral">&quot;glSpecializeShaderARB&quot;</span>);</div>
<div class="line">        has_ARB_gl_spirv = 1;</div>
<div class="line">    }</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">void</span> some_function(<span class="keywordtype">void</span>)</div>
<div class="line">{</div>
<div class="line">    <span class="keywordflow">if</span> (has_ARB_gl_spirv)</div>
<div class="line">    {</div>
<div class="line">        <span class="comment">// Now the extension function can be called as usual</span></div>
<div class="line">        glSpecializeShaderARB(...);</div>
<div class="line">    }</div>
<div class="line">}</div>
</div><!-- fragment --> </div></div><!-- contents -->
</div><!-- PageDoc -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
