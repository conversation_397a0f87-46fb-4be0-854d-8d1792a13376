{"sources": [{"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/ocl/test_accumulate.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/ocl/test_blend.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/ocl/test_boxfilter.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/ocl/test_canny.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/ocl/test_color.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/ocl/test_filter2d.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/ocl/test_filters.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/ocl/test_gftt.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/ocl/test_histogram.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/ocl/test_houghlines.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/ocl/test_imgproc.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/ocl/test_match_template.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/ocl/test_medianfilter.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/ocl/test_pyramids.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/ocl/test_sepfilter2d.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/ocl/test_warp.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_approxpoly.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_bilateral_filter.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_boundingrect.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_canny.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_color.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_connectedcomponents.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_contours.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_contours_new.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_convhull.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_cornersubpix.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_cvtyuv.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_distancetransform.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_drawing.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_emd.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_filter.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_fitellipse.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_fitellipse_ams.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_fitellipse_direct.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_floodfill.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_goodfeaturetotrack.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_grabcut.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_histograms.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_houghcircles.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_houghlines.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_imgproc_umat.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_imgwarp.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_imgwarp_strict.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_intelligent_scissors.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_intersectconvexconvex.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_intersection.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_lsd.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_main.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_moments.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_pc.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_pyramid.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_resize_bitexact.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_smooth_bitexact.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_stackblur.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_subdivision2d.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_templmatch.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_templmatchmask.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_thresh.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_watershed.cpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}, {"file": "D:/AI/opencv/opencv-4.10.0/modules/imgproc/test/test_precomp.hpp", "labels": ["Main", "opencv_imgproc", "AccuracyTest"]}], "target": {"labels": ["Main", "opencv_imgproc", "AccuracyTest"], "name": "opencv_test_imgproc"}}