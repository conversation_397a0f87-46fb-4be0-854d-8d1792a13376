﻿  test_accumulate.cpp
  test_blend.cpp
  test_boxfilter.cpp
  test_filter2d.cpp
  test_filters.cpp
  test_gftt.cpp
  test_histogram.cpp
  test_imgproc.cpp
  test_match_template.cpp
  test_medianfilter.cpp
  test_pyramids.cpp
  test_sepfilter2d.cpp
  test_warp.cpp
  test_approxpoly.cpp
  test_bilateral_filter.cpp
  test_boundingrect.cpp
  test_connectedcomponents.cpp
  test_contours.cpp
  test_contours_new.cpp
  test_convhull.cpp
  test_cornersubpix.cpp
  test_cvtyuv.cpp
  test_distancetransform.cpp
  test_drawing.cpp
  test_emd.cpp
  test_filter.cpp
  test_fitellipse.cpp
  test_fitellipse_ams.cpp
  test_fitellipse_direct.cpp
  test_floodfill.cpp
  test_goodfeaturetotrack.cpp
  test_grabcut.cpp
  test_histograms.cpp
  test_houghcircles.cpp
  test_imgproc_umat.cpp
  test_imgwarp.cpp
  test_imgwarp_strict.cpp
  test_intelligent_scissors.cpp
  test_intersectconvexconvex.cpp
  test_intersection.cpp
  test_lsd.cpp
  test_main.cpp
  test_moments.cpp
  test_pc.cpp
  test_pyramid.cpp
  test_resize_bitexact.cpp
  test_smooth_bitexact.cpp
  test_stackblur.cpp
  test_subdivision2d.cpp
  test_templmatch.cpp
  test_templmatchmask.cpp
  test_thresh.cpp
  test_watershed.cpp
  test_canny.cpp
  test_color.cpp
  test_houghlines.cpp
  test_canny.cpp
  test_color.cpp
  test_houghlines.cpp
  opencv_test_imgproc.vcxproj -> D:\AI\opencv\cudabuild\bin\Release\opencv_test_imgproc.exe
