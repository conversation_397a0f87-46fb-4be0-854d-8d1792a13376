var searchData=
[
  ['object_20sharing_0',['Context object sharing',['../context_guide.html#context_sharing',1,'']]],
  ['objects_1',['objects',['../context_guide.html#context_object',1,'Context objects'],['../input_guide.html#cursor_object',1,'Cursor objects'],['../monitor_guide.html#monitor_object',1,'Monitor objects'],['../window_guide.html#window_object',1,'Window objects']]],
  ['of_20automatic_20event_20polling_2',['Removal of automatic event polling',['../moving_guide.html#moving_autopoll',1,'']]],
  ['of_20character_20actions_3',['Removal of character actions',['../moving_guide.html#moving_char_up',1,'']]],
  ['of_20glfwcall_20macro_4',['Removal of GLFWCALL macro',['../moving_guide.html#moving_stdcall',1,'']]],
  ['of_20image_20and_20texture_20loading_5',['Removal of image and texture loading',['../moving_guide.html#moving_image',1,'']]],
  ['of_20native_20access_20functions_6',['Multiple sets of native access functions',['../news.html#multiplatform_caveat',1,'']]],
  ['of_20system_20wide_20hotkeys_7',['Capture of system-wide hotkeys',['../moving_guide.html#moving_syskeys',1,'']]],
  ['of_20threading_20functions_8',['Removal of threading functions',['../moving_guide.html#moving_threads',1,'']]],
  ['of_20window_20and_20framebuffer_20sizes_9',['Separation of window and framebuffer sizes',['../moving_guide.html#moving_hidpi',1,'']]],
  ['of_20windows_20older_20than_20xp_10',['Support for versions of Windows older than XP',['../moving_guide.html#moving_windows',1,'']]],
  ['offscreen_20contexts_11',['Offscreen contexts',['../context_guide.html#context_offscreen',1,'']]],
  ['offsets_12',['Wheel position replaced by scroll offsets',['../moving_guide.html#moving_wheel',1,'']]],
  ['older_20systems_13',['Wayland framebuffer may lack alpha channel on older systems',['../news.html#wayland_alpha_caveat',1,'']]],
  ['older_20than_20xp_14',['Support for versions of Windows older than XP',['../moving_guide.html#moving_windows',1,'']]],
  ['on_20demand_15',['Joystick support is initialized on demand',['../news.html#joystick_init_caveat',1,'']]],
  ['on_20macos_16',['on macos',['../compat_guide.html#compat_osx',1,'OpenGL on macOS'],['../build_guide.html#build_link_osx',1,'With command-line or makefile on macOS'],['../build_guide.html#build_link_xcode',1,'With Xcode on macOS']]],
  ['on_20older_20systems_17',['Wayland framebuffer may lack alpha channel on older systems',['../news.html#wayland_alpha_caveat',1,'']]],
  ['on_20unix_18',['With pkg-config and GLFW binaries on Unix',['../build_guide.html#build_link_pkgconfig',1,'']]],
  ['opengl_19',['Rendering with OpenGL',['../quick_guide.html#quick_render',1,'']]],
  ['opengl_20and_20opengl_20es_20extensions_20',['OpenGL and OpenGL ES extensions',['../context_guide.html#context_glext',1,'']]],
  ['opengl_20context_20current_21',['Making the OpenGL context current',['../quick_guide.html#quick_context_current',1,'']]],
  ['opengl_20on_20macos_22',['OpenGL on macOS',['../compat_guide.html#compat_osx',1,'']]],
  ['option_20has_20been_20removed_23',['option has been removed',['../news.html#use_osmesa_removed',1,'GLFW_USE_OSMESA CMake option has been removed'],['../news.html#use_wayland_removed',1,'GLFW_USE_WAYLAND CMake option has been removed'],['../news.html#vulkan_static_removed',1,'GLFW_VULKAN_STATIC CMake option has been removed']]],
  ['option_20macros_24',['GLFW header option macros',['../build_guide.html#build_macros',1,'']]],
  ['options_25',['options',['../compile_guide.html#compile_options',1,'CMake options'],['../compile_guide.html#compile_options_macos',1,'macOS specific CMake options'],['../compile_guide.html#compile_options_shared',1,'Shared CMake options'],['../compile_guide.html#compile_options_unix',1,'Unix-like system specific CMake options'],['../compile_guide.html#compile_options_win32',1,'Win32 specific CMake options']]],
  ['or_20later_26',['Documentation generation requires Doxygen 1.9.8 or later',['../news.html#docs_target_caveat',1,'']]],
  ['or_20makefile_20on_20macos_27',['With command-line or makefile on macOS',['../build_guide.html#build_link_osx',1,'']]],
  ['order_28',['Event order',['../intro_guide.html#event_order',1,'']]],
  ['original_20mingw_20support_20is_20deprecated_29',['Original MinGW support is deprecated',['../news.html#mingw_deprecated',1,'']]],
  ['os_20x_20yosemite_20support_20is_20deprecated_30',['OS X Yosemite support is deprecated',['../news.html#yosemite_deprecated',1,'']]],
  ['output_31',['Clipboard input and output',['../input_guide.html#clipboard',1,'']]]
];
