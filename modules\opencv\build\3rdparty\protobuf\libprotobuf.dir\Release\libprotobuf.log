﻿  any_lite.cc
  arena.cc
  arenastring.cc
  extension_set.cc
  generated_message_util.cc
  implicit_weak_message.cc
  coded_stream.cc
  io_win32.cc
  strtod.cc
  zero_copy_stream.cc
  zero_copy_stream_impl.cc
  zero_copy_stream_impl_lite.cc
  map.cc
  message_lite.cc
  parse_context.cc
  repeated_field.cc
  repeated_ptr_field.cc
  bytestream.cc
  common.cc
  int128.cc
  status.cc
  stringpiece.cc
  stringprintf.cc
  structurally_valid.cc
  strutil.cc
  wire_format_lite.cc
  any.cc
  descriptor.cc
  descriptor.pb.cc
  descriptor_database.cc
  dynamic_message.cc
  extension_set_heavy.cc
  generated_message_reflection.cc
  tokenizer.cc
  map_field.cc
  message.cc
  reflection_ops.cc
  substitute.cc
  text_format.cc
  unknown_field_set.cc
  wire_format.cc
  libprotobuf.vcxproj -> D:\AI\opencv\cudabuild\3rdparty\lib\Release\libprotobuf.lib
