// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Security_Isolation_1_H
#define WINRT_Windows_Security_Isolation_1_H
#include "winrt/impl/Windows.Security.Isolation.0.h"
WINRT_EXPORT namespace winrt::Windows::Security::Isolation
{
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironment>
    {
        IIsolatedWindowsEnvironment(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironment2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironment2>
    {
        IIsolatedWindowsEnvironment2(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironment2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironment3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironment3>
    {
        IIsolatedWindowsEnvironment3(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironment3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironment4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironment4>
    {
        IIsolatedWindowsEnvironment4(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironment4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentCreateResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentCreateResult>
    {
        IIsolatedWindowsEnvironmentCreateResult(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentCreateResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentCreateResult2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentCreateResult2>
    {
        IIsolatedWindowsEnvironmentCreateResult2(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentCreateResult2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentFactory>
    {
        IIsolatedWindowsEnvironmentFactory(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentFile :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentFile>
    {
        IIsolatedWindowsEnvironmentFile(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentFile(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentFile2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentFile2>
    {
        IIsolatedWindowsEnvironmentFile2(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentFile2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentHostStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentHostStatics>
    {
        IIsolatedWindowsEnvironmentHostStatics(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentHostStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentLaunchFileResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentLaunchFileResult>
    {
        IIsolatedWindowsEnvironmentLaunchFileResult(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentLaunchFileResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentOptions>
    {
        IIsolatedWindowsEnvironmentOptions(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentOptions2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentOptions2>
    {
        IIsolatedWindowsEnvironmentOptions2(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentOptions2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentOptions3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentOptions3>
    {
        IIsolatedWindowsEnvironmentOptions3(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentOptions3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentOwnerRegistrationData :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentOwnerRegistrationData>
    {
        IIsolatedWindowsEnvironmentOwnerRegistrationData(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentOwnerRegistrationData(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentOwnerRegistrationResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentOwnerRegistrationResult>
    {
        IIsolatedWindowsEnvironmentOwnerRegistrationResult(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentOwnerRegistrationResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentOwnerRegistrationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentOwnerRegistrationStatics>
    {
        IIsolatedWindowsEnvironmentOwnerRegistrationStatics(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentOwnerRegistrationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentPostMessageResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentPostMessageResult>
    {
        IIsolatedWindowsEnvironmentPostMessageResult(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentPostMessageResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentProcess :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentProcess>
    {
        IIsolatedWindowsEnvironmentProcess(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentProcess(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentShareFileRequestOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentShareFileRequestOptions>
    {
        IIsolatedWindowsEnvironmentShareFileRequestOptions(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentShareFileRequestOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentShareFileResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentShareFileResult>
    {
        IIsolatedWindowsEnvironmentShareFileResult(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentShareFileResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentShareFolderRequestOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentShareFolderRequestOptions>
    {
        IIsolatedWindowsEnvironmentShareFolderRequestOptions(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentShareFolderRequestOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentShareFolderResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentShareFolderResult>
    {
        IIsolatedWindowsEnvironmentShareFolderResult(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentShareFolderResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentStartProcessResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentStartProcessResult>
    {
        IIsolatedWindowsEnvironmentStartProcessResult(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentStartProcessResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentTelemetryParameters :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentTelemetryParameters>
    {
        IIsolatedWindowsEnvironmentTelemetryParameters(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentTelemetryParameters(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentUserInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentUserInfo>
    {
        IIsolatedWindowsEnvironmentUserInfo(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentUserInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsEnvironmentUserInfo2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsEnvironmentUserInfo2>
    {
        IIsolatedWindowsEnvironmentUserInfo2(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsEnvironmentUserInfo2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsHostMessengerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsHostMessengerStatics>
    {
        IIsolatedWindowsHostMessengerStatics(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsHostMessengerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IIsolatedWindowsHostMessengerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsolatedWindowsHostMessengerStatics2>
    {
        IIsolatedWindowsHostMessengerStatics2(std::nullptr_t = nullptr) noexcept {}
        IIsolatedWindowsHostMessengerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
