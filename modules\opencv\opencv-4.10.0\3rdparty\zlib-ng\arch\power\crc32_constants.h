/* Constants table used by crc32_power8.c
 * Copyright (C) 2021 IBM Corporation
 *
 * This file was automatically generated, DO NOT EDIT IT MANUALLY.
 *
 * For conditions of distribution and use, see copyright notice in zlib.h
 */

#include "zendian.h"
#include "zbuild.h"

/* Reduce 262144 kbits to 1024 bits */
static const __vector unsigned long long vcrc_const[255] ALIGNED_(16) = {
#if BYTE_ORDER == LITTLE_ENDIAN
    /* x^261120 mod p(x)` << 1, x^261184 mod p(x)` << 1 */
    { 0x0000000099ea94a8, 0x00000001651797d2 },
    /* x^260096 mod p(x)` << 1, x^260160 mod p(x)` << 1 */
    { 0x00000000945a8420, 0x0000000021e0d56c },
    /* x^259072 mod p(x)` << 1, x^259136 mod p(x)` << 1 */
    { 0x0000000030762706, 0x000000000f95ecaa },
    /* x^258048 mod p(x)` << 1, x^258112 mod p(x)` << 1 */
    { 0x00000001a52fc582, 0x00000001ebd224ac },
    /* x^257024 mod p(x)` << 1, x^257088 mod p(x)` << 1 */
    { 0x00000001a4a7167a, 0x000000000ccb97ca },
    /* x^256000 mod p(x)` << 1, x^256064 mod p(x)` << 1 */
    { 0x000000000c18249a, 0x00000001006ec8a8 },
    /* x^254976 mod p(x)` << 1, x^255040 mod p(x)` << 1 */
    { 0x00000000a924ae7c, 0x000000014f58f196 },
    /* x^253952 mod p(x)` << 1, x^254016 mod p(x)` << 1 */
    { 0x00000001e12ccc12, 0x00000001a7192ca6 },
    /* x^252928 mod p(x)` << 1, x^252992 mod p(x)` << 1 */
    { 0x00000000a0b9d4ac, 0x000000019a64bab2 },
    /* x^251904 mod p(x)` << 1, x^251968 mod p(x)` << 1 */
    { 0x0000000095e8ddfe, 0x0000000014f4ed2e },
    /* x^250880 mod p(x)` << 1, x^250944 mod p(x)` << 1 */
    { 0x00000000233fddc4, 0x000000011092b6a2 },
    /* x^249856 mod p(x)` << 1, x^249920 mod p(x)` << 1 */
    { 0x00000001b4529b62, 0x00000000c8a1629c },
    /* x^248832 mod p(x)` << 1, x^248896 mod p(x)` << 1 */
    { 0x00000001a7fa0e64, 0x000000017bf32e8e },
    /* x^247808 mod p(x)` << 1, x^247872 mod p(x)` << 1 */
    { 0x00000001b5334592, 0x00000001f8cc6582 },
    /* x^246784 mod p(x)` << 1, x^246848 mod p(x)` << 1 */
    { 0x000000011f8ee1b4, 0x000000008631ddf0 },
    /* x^245760 mod p(x)` << 1, x^245824 mod p(x)` << 1 */
    { 0x000000006252e632, 0x000000007e5a76d0 },
    /* x^244736 mod p(x)` << 1, x^244800 mod p(x)` << 1 */
    { 0x00000000ab973e84, 0x000000002b09b31c },
    /* x^243712 mod p(x)` << 1, x^243776 mod p(x)` << 1 */
    { 0x000000007734f5ec, 0x00000001b2df1f84 },
    /* x^242688 mod p(x)` << 1, x^242752 mod p(x)` << 1 */
    { 0x000000007c547798, 0x00000001d6f56afc },
    /* x^241664 mod p(x)` << 1, x^241728 mod p(x)` << 1 */
    { 0x000000007ec40210, 0x00000001b9b5e70c },
    /* x^240640 mod p(x)` << 1, x^240704 mod p(x)` << 1 */
    { 0x00000001ab1695a8, 0x0000000034b626d2 },
    /* x^239616 mod p(x)` << 1, x^239680 mod p(x)` << 1 */
    { 0x0000000090494bba, 0x000000014c53479a },
    /* x^238592 mod p(x)` << 1, x^238656 mod p(x)` << 1 */
    { 0x00000001123fb816, 0x00000001a6d179a4 },
    /* x^237568 mod p(x)` << 1, x^237632 mod p(x)` << 1 */
    { 0x00000001e188c74c, 0x000000015abd16b4 },
    /* x^236544 mod p(x)` << 1, x^236608 mod p(x)` << 1 */
    { 0x00000001c2d3451c, 0x00000000018f9852 },
    /* x^235520 mod p(x)` << 1, x^235584 mod p(x)` << 1 */
    { 0x00000000f55cf1ca, 0x000000001fb3084a },
    /* x^234496 mod p(x)` << 1, x^234560 mod p(x)` << 1 */
    { 0x00000001a0531540, 0x00000000c53dfb04 },
    /* x^233472 mod p(x)` << 1, x^233536 mod p(x)` << 1 */
    { 0x0000000132cd7ebc, 0x00000000e10c9ad6 },
    /* x^232448 mod p(x)` << 1, x^232512 mod p(x)` << 1 */
    { 0x0000000073ab7f36, 0x0000000025aa994a },
    /* x^231424 mod p(x)` << 1, x^231488 mod p(x)` << 1 */
    { 0x0000000041aed1c2, 0x00000000fa3a74c4 },
    /* x^230400 mod p(x)` << 1, x^230464 mod p(x)` << 1 */
    { 0x0000000136c53800, 0x0000000033eb3f40 },
    /* x^229376 mod p(x)` << 1, x^229440 mod p(x)` << 1 */
    { 0x0000000126835a30, 0x000000017193f296 },
    /* x^228352 mod p(x)` << 1, x^228416 mod p(x)` << 1 */
    { 0x000000006241b502, 0x0000000043f6c86a },
    /* x^227328 mod p(x)` << 1, x^227392 mod p(x)` << 1 */
    { 0x00000000d5196ad4, 0x000000016b513ec6 },
    /* x^226304 mod p(x)` << 1, x^226368 mod p(x)` << 1 */
    { 0x000000009cfa769a, 0x00000000c8f25b4e },
    /* x^225280 mod p(x)` << 1, x^225344 mod p(x)` << 1 */
    { 0x00000000920e5df4, 0x00000001a45048ec },
    /* x^224256 mod p(x)` << 1, x^224320 mod p(x)` << 1 */
    { 0x0000000169dc310e, 0x000000000c441004 },
    /* x^223232 mod p(x)` << 1, x^223296 mod p(x)` << 1 */
    { 0x0000000009fc331c, 0x000000000e17cad6 },
    /* x^222208 mod p(x)` << 1, x^222272 mod p(x)` << 1 */
    { 0x000000010d94a81e, 0x00000001253ae964 },
    /* x^221184 mod p(x)` << 1, x^221248 mod p(x)` << 1 */
    { 0x0000000027a20ab2, 0x00000001d7c88ebc },
    /* x^220160 mod p(x)` << 1, x^220224 mod p(x)` << 1 */
    { 0x0000000114f87504, 0x00000001e7ca913a },
    /* x^219136 mod p(x)` << 1, x^219200 mod p(x)` << 1 */
    { 0x000000004b076d96, 0x0000000033ed078a },
    /* x^218112 mod p(x)` << 1, x^218176 mod p(x)` << 1 */
    { 0x00000000da4d1e74, 0x00000000e1839c78 },
    /* x^217088 mod p(x)` << 1, x^217152 mod p(x)` << 1 */
    { 0x000000001b81f672, 0x00000001322b267e },
    /* x^216064 mod p(x)` << 1, x^216128 mod p(x)` << 1 */
    { 0x000000009367c988, 0x00000000638231b6 },
    /* x^215040 mod p(x)` << 1, x^215104 mod p(x)` << 1 */
    { 0x00000001717214ca, 0x00000001ee7f16f4 },
    /* x^214016 mod p(x)` << 1, x^214080 mod p(x)` << 1 */
    { 0x000000009f47d820, 0x0000000117d9924a },
    /* x^212992 mod p(x)` << 1, x^213056 mod p(x)` << 1 */
    { 0x000000010d9a47d2, 0x00000000e1a9e0c4 },
    /* x^211968 mod p(x)` << 1, x^212032 mod p(x)` << 1 */
    { 0x00000000a696c58c, 0x00000001403731dc },
    /* x^210944 mod p(x)` << 1, x^211008 mod p(x)` << 1 */
    { 0x000000002aa28ec6, 0x00000001a5ea9682 },
    /* x^209920 mod p(x)` << 1, x^209984 mod p(x)` << 1 */
    { 0x00000001fe18fd9a, 0x0000000101c5c578 },
    /* x^208896 mod p(x)` << 1, x^208960 mod p(x)` << 1 */
    { 0x000000019d4fc1ae, 0x00000000dddf6494 },
    /* x^207872 mod p(x)` << 1, x^207936 mod p(x)` << 1 */
    { 0x00000001ba0e3dea, 0x00000000f1c3db28 },
    /* x^206848 mod p(x)` << 1, x^206912 mod p(x)` << 1 */
    { 0x0000000074b59a5e, 0x000000013112fb9c },
    /* x^205824 mod p(x)` << 1, x^205888 mod p(x)` << 1 */
    { 0x00000000f2b5ea98, 0x00000000b680b906 },
    /* x^204800 mod p(x)` << 1, x^204864 mod p(x)` << 1 */
    { 0x0000000187132676, 0x000000001a282932 },
    /* x^203776 mod p(x)` << 1, x^203840 mod p(x)` << 1 */
    { 0x000000010a8c6ad4, 0x0000000089406e7e },
    /* x^202752 mod p(x)` << 1, x^202816 mod p(x)` << 1 */
    { 0x00000001e21dfe70, 0x00000001def6be8c },
    /* x^201728 mod p(x)` << 1, x^201792 mod p(x)` << 1 */
    { 0x00000001da0050e4, 0x0000000075258728 },
    /* x^200704 mod p(x)` << 1, x^200768 mod p(x)` << 1 */
    { 0x00000000772172ae, 0x000000019536090a },
    /* x^199680 mod p(x)` << 1, x^199744 mod p(x)` << 1 */
    { 0x00000000e47724aa, 0x00000000f2455bfc },
    /* x^198656 mod p(x)` << 1, x^198720 mod p(x)` << 1 */
    { 0x000000003cd63ac4, 0x000000018c40baf4 },
    /* x^197632 mod p(x)` << 1, x^197696 mod p(x)` << 1 */
    { 0x00000001bf47d352, 0x000000004cd390d4 },
    /* x^196608 mod p(x)` << 1, x^196672 mod p(x)` << 1 */
    { 0x000000018dc1d708, 0x00000001e4ece95a },
    /* x^195584 mod p(x)` << 1, x^195648 mod p(x)` << 1 */
    { 0x000000002d4620a4, 0x000000001a3ee918 },
    /* x^194560 mod p(x)` << 1, x^194624 mod p(x)` << 1 */
    { 0x0000000058fd1740, 0x000000007c652fb8 },
    /* x^193536 mod p(x)` << 1, x^193600 mod p(x)` << 1 */
    { 0x00000000dadd9bfc, 0x000000011c67842c },
    /* x^192512 mod p(x)` << 1, x^192576 mod p(x)` << 1 */
    { 0x00000001ea2140be, 0x00000000254f759c },
    /* x^191488 mod p(x)` << 1, x^191552 mod p(x)` << 1 */
    { 0x000000009de128ba, 0x000000007ece94ca },
    /* x^190464 mod p(x)` << 1, x^190528 mod p(x)` << 1 */
    { 0x000000013ac3aa8e, 0x0000000038f258c2 },
    /* x^189440 mod p(x)` << 1, x^189504 mod p(x)` << 1 */
    { 0x0000000099980562, 0x00000001cdf17b00 },
    /* x^188416 mod p(x)` << 1, x^188480 mod p(x)` << 1 */
    { 0x00000001c1579c86, 0x000000011f882c16 },
    /* x^187392 mod p(x)` << 1, x^187456 mod p(x)` << 1 */
    { 0x0000000068dbbf94, 0x0000000100093fc8 },
    /* x^186368 mod p(x)` << 1, x^186432 mod p(x)` << 1 */
    { 0x000000004509fb04, 0x00000001cd684f16 },
    /* x^185344 mod p(x)` << 1, x^185408 mod p(x)` << 1 */
    { 0x00000001202f6398, 0x000000004bc6a70a },
    /* x^184320 mod p(x)` << 1, x^184384 mod p(x)` << 1 */
    { 0x000000013aea243e, 0x000000004fc7e8e4 },
    /* x^183296 mod p(x)` << 1, x^183360 mod p(x)` << 1 */
    { 0x00000001b4052ae6, 0x0000000130103f1c },
    /* x^182272 mod p(x)` << 1, x^182336 mod p(x)` << 1 */
    { 0x00000001cd2a0ae8, 0x0000000111b0024c },
    /* x^181248 mod p(x)` << 1, x^181312 mod p(x)` << 1 */
    { 0x00000001fe4aa8b4, 0x000000010b3079da },
    /* x^180224 mod p(x)` << 1, x^180288 mod p(x)` << 1 */
    { 0x00000001d1559a42, 0x000000010192bcc2 },
    /* x^179200 mod p(x)` << 1, x^179264 mod p(x)` << 1 */
    { 0x00000001f3e05ecc, 0x0000000074838d50 },
    /* x^178176 mod p(x)` << 1, x^178240 mod p(x)` << 1 */
    { 0x0000000104ddd2cc, 0x000000001b20f520 },
    /* x^177152 mod p(x)` << 1, x^177216 mod p(x)` << 1 */
    { 0x000000015393153c, 0x0000000050c3590a },
    /* x^176128 mod p(x)` << 1, x^176192 mod p(x)` << 1 */
    { 0x0000000057e942c6, 0x00000000b41cac8e },
    /* x^175104 mod p(x)` << 1, x^175168 mod p(x)` << 1 */
    { 0x000000012c633850, 0x000000000c72cc78 },
    /* x^174080 mod p(x)` << 1, x^174144 mod p(x)` << 1 */
    { 0x00000000ebcaae4c, 0x0000000030cdb032 },
    /* x^173056 mod p(x)` << 1, x^173120 mod p(x)` << 1 */
    { 0x000000013ee532a6, 0x000000013e09fc32 },
    /* x^172032 mod p(x)` << 1, x^172096 mod p(x)` << 1 */
    { 0x00000001bf0cbc7e, 0x000000001ed624d2 },
    /* x^171008 mod p(x)` << 1, x^171072 mod p(x)` << 1 */
    { 0x00000000d50b7a5a, 0x00000000781aee1a },
    /* x^169984 mod p(x)` << 1, x^170048 mod p(x)` << 1 */
    { 0x0000000002fca6e8, 0x00000001c4d8348c },
    /* x^168960 mod p(x)` << 1, x^169024 mod p(x)` << 1 */
    { 0x000000007af40044, 0x0000000057a40336 },
    /* x^167936 mod p(x)` << 1, x^168000 mod p(x)` << 1 */
    { 0x0000000016178744, 0x0000000085544940 },
    /* x^166912 mod p(x)` << 1, x^166976 mod p(x)` << 1 */
    { 0x000000014c177458, 0x000000019cd21e80 },
    /* x^165888 mod p(x)` << 1, x^165952 mod p(x)` << 1 */
    { 0x000000011b6ddf04, 0x000000013eb95bc0 },
    /* x^164864 mod p(x)` << 1, x^164928 mod p(x)` << 1 */
    { 0x00000001f3e29ccc, 0x00000001dfc9fdfc },
    /* x^163840 mod p(x)` << 1, x^163904 mod p(x)` << 1 */
    { 0x0000000135ae7562, 0x00000000cd028bc2 },
    /* x^162816 mod p(x)` << 1, x^162880 mod p(x)` << 1 */
    { 0x0000000190ef812c, 0x0000000090db8c44 },
    /* x^161792 mod p(x)` << 1, x^161856 mod p(x)` << 1 */
    { 0x0000000067a2c786, 0x000000010010a4ce },
    /* x^160768 mod p(x)` << 1, x^160832 mod p(x)` << 1 */
    { 0x0000000048b9496c, 0x00000001c8f4c72c },
    /* x^159744 mod p(x)` << 1, x^159808 mod p(x)` << 1 */
    { 0x000000015a422de6, 0x000000001c26170c },
    /* x^158720 mod p(x)` << 1, x^158784 mod p(x)` << 1 */
    { 0x00000001ef0e3640, 0x00000000e3fccf68 },
    /* x^157696 mod p(x)` << 1, x^157760 mod p(x)` << 1 */
    { 0x00000001006d2d26, 0x00000000d513ed24 },
    /* x^156672 mod p(x)` << 1, x^156736 mod p(x)` << 1 */
    { 0x00000001170d56d6, 0x00000000141beada },
    /* x^155648 mod p(x)` << 1, x^155712 mod p(x)` << 1 */
    { 0x00000000a5fb613c, 0x000000011071aea0 },
    /* x^154624 mod p(x)` << 1, x^154688 mod p(x)` << 1 */
    { 0x0000000040bbf7fc, 0x000000012e19080a },
    /* x^153600 mod p(x)` << 1, x^153664 mod p(x)` << 1 */
    { 0x000000016ac3a5b2, 0x0000000100ecf826 },
    /* x^152576 mod p(x)` << 1, x^152640 mod p(x)` << 1 */
    { 0x00000000abf16230, 0x0000000069b09412 },
    /* x^151552 mod p(x)` << 1, x^151616 mod p(x)` << 1 */
    { 0x00000001ebe23fac, 0x0000000122297bac },
    /* x^150528 mod p(x)` << 1, x^150592 mod p(x)` << 1 */
    { 0x000000008b6a0894, 0x00000000e9e4b068 },
    /* x^149504 mod p(x)` << 1, x^149568 mod p(x)` << 1 */
    { 0x00000001288ea478, 0x000000004b38651a },
    /* x^148480 mod p(x)` << 1, x^148544 mod p(x)` << 1 */
    { 0x000000016619c442, 0x00000001468360e2 },
    /* x^147456 mod p(x)` << 1, x^147520 mod p(x)` << 1 */
    { 0x0000000086230038, 0x00000000121c2408 },
    /* x^146432 mod p(x)` << 1, x^146496 mod p(x)` << 1 */
    { 0x000000017746a756, 0x00000000da7e7d08 },
    /* x^145408 mod p(x)` << 1, x^145472 mod p(x)` << 1 */
    { 0x0000000191b8f8f8, 0x00000001058d7652 },
    /* x^144384 mod p(x)` << 1, x^144448 mod p(x)` << 1 */
    { 0x000000008e167708, 0x000000014a098a90 },
    /* x^143360 mod p(x)` << 1, x^143424 mod p(x)` << 1 */
    { 0x0000000148b22d54, 0x0000000020dbe72e },
    /* x^142336 mod p(x)` << 1, x^142400 mod p(x)` << 1 */
    { 0x0000000044ba2c3c, 0x000000011e7323e8 },
    /* x^141312 mod p(x)` << 1, x^141376 mod p(x)` << 1 */
    { 0x00000000b54d2b52, 0x00000000d5d4bf94 },
    /* x^140288 mod p(x)` << 1, x^140352 mod p(x)` << 1 */
    { 0x0000000005a4fd8a, 0x0000000199d8746c },
    /* x^139264 mod p(x)` << 1, x^139328 mod p(x)` << 1 */
    { 0x0000000139f9fc46, 0x00000000ce9ca8a0 },
    /* x^138240 mod p(x)` << 1, x^138304 mod p(x)` << 1 */
    { 0x000000015a1fa824, 0x00000000136edece },
    /* x^137216 mod p(x)` << 1, x^137280 mod p(x)` << 1 */
    { 0x000000000a61ae4c, 0x000000019b92a068 },
    /* x^136192 mod p(x)` << 1, x^136256 mod p(x)` << 1 */
    { 0x0000000145e9113e, 0x0000000071d62206 },
    /* x^135168 mod p(x)` << 1, x^135232 mod p(x)` << 1 */
    { 0x000000006a348448, 0x00000000dfc50158 },
    /* x^134144 mod p(x)` << 1, x^134208 mod p(x)` << 1 */
    { 0x000000004d80a08c, 0x00000001517626bc },
    /* x^133120 mod p(x)` << 1, x^133184 mod p(x)` << 1 */
    { 0x000000014b6837a0, 0x0000000148d1e4fa },
    /* x^132096 mod p(x)` << 1, x^132160 mod p(x)` << 1 */
    { 0x000000016896a7fc, 0x0000000094d8266e },
    /* x^131072 mod p(x)` << 1, x^131136 mod p(x)` << 1 */
    { 0x000000014f187140, 0x00000000606c5e34 },
    /* x^130048 mod p(x)` << 1, x^130112 mod p(x)` << 1 */
    { 0x000000019581b9da, 0x000000019766beaa },
    /* x^129024 mod p(x)` << 1, x^129088 mod p(x)` << 1 */
    { 0x00000001091bc984, 0x00000001d80c506c },
    /* x^128000 mod p(x)` << 1, x^128064 mod p(x)` << 1 */
    { 0x000000001067223c, 0x000000001e73837c },
    /* x^126976 mod p(x)` << 1, x^127040 mod p(x)` << 1 */
    { 0x00000001ab16ea02, 0x0000000064d587de },
    /* x^125952 mod p(x)` << 1, x^126016 mod p(x)` << 1 */
    { 0x000000013c4598a8, 0x00000000f4a507b0 },
    /* x^124928 mod p(x)` << 1, x^124992 mod p(x)` << 1 */
    { 0x00000000b3735430, 0x0000000040e342fc },
    /* x^123904 mod p(x)` << 1, x^123968 mod p(x)` << 1 */
    { 0x00000001bb3fc0c0, 0x00000001d5ad9c3a },
    /* x^122880 mod p(x)` << 1, x^122944 mod p(x)` << 1 */
    { 0x00000001570ae19c, 0x0000000094a691a4 },
    /* x^121856 mod p(x)` << 1, x^121920 mod p(x)` << 1 */
    { 0x00000001ea910712, 0x00000001271ecdfa },
    /* x^120832 mod p(x)` << 1, x^120896 mod p(x)` << 1 */
    { 0x0000000167127128, 0x000000009e54475a },
    /* x^119808 mod p(x)` << 1, x^119872 mod p(x)` << 1 */
    { 0x0000000019e790a2, 0x00000000c9c099ee },
    /* x^118784 mod p(x)` << 1, x^118848 mod p(x)` << 1 */
    { 0x000000003788f710, 0x000000009a2f736c },
    /* x^117760 mod p(x)` << 1, x^117824 mod p(x)` << 1 */
    { 0x00000001682a160e, 0x00000000bb9f4996 },
    /* x^116736 mod p(x)` << 1, x^116800 mod p(x)` << 1 */
    { 0x000000007f0ebd2e, 0x00000001db688050 },
    /* x^115712 mod p(x)` << 1, x^115776 mod p(x)` << 1 */
    { 0x000000002b032080, 0x00000000e9b10af4 },
    /* x^114688 mod p(x)` << 1, x^114752 mod p(x)` << 1 */
    { 0x00000000cfd1664a, 0x000000012d4545e4 },
    /* x^113664 mod p(x)` << 1, x^113728 mod p(x)` << 1 */
    { 0x00000000aa1181c2, 0x000000000361139c },
    /* x^112640 mod p(x)` << 1, x^112704 mod p(x)` << 1 */
    { 0x00000000ddd08002, 0x00000001a5a1a3a8 },
    /* x^111616 mod p(x)` << 1, x^111680 mod p(x)` << 1 */
    { 0x00000000e8dd0446, 0x000000006844e0b0 },
    /* x^110592 mod p(x)` << 1, x^110656 mod p(x)` << 1 */
    { 0x00000001bbd94a00, 0x00000000c3762f28 },
    /* x^109568 mod p(x)` << 1, x^109632 mod p(x)` << 1 */
    { 0x00000000ab6cd180, 0x00000001d26287a2 },
    /* x^108544 mod p(x)` << 1, x^108608 mod p(x)` << 1 */
    { 0x0000000031803ce2, 0x00000001f6f0bba8 },
    /* x^107520 mod p(x)` << 1, x^107584 mod p(x)` << 1 */
    { 0x0000000024f40b0c, 0x000000002ffabd62 },
    /* x^106496 mod p(x)` << 1, x^106560 mod p(x)` << 1 */
    { 0x00000001ba1d9834, 0x00000000fb4516b8 },
    /* x^105472 mod p(x)` << 1, x^105536 mod p(x)` << 1 */
    { 0x0000000104de61aa, 0x000000018cfa961c },
    /* x^104448 mod p(x)` << 1, x^104512 mod p(x)` << 1 */
    { 0x0000000113e40d46, 0x000000019e588d52 },
    /* x^103424 mod p(x)` << 1, x^103488 mod p(x)` << 1 */
    { 0x00000001415598a0, 0x00000001180f0bbc },
    /* x^102400 mod p(x)` << 1, x^102464 mod p(x)` << 1 */
    { 0x00000000bf6c8c90, 0x00000000e1d9177a },
    /* x^101376 mod p(x)` << 1, x^101440 mod p(x)` << 1 */
    { 0x00000001788b0504, 0x0000000105abc27c },
    /* x^100352 mod p(x)` << 1, x^100416 mod p(x)` << 1 */
    { 0x0000000038385d02, 0x00000000972e4a58 },
    /* x^99328 mod p(x)` << 1, x^99392 mod p(x)` << 1 */
    { 0x00000001b6c83844, 0x0000000183499a5e },
    /* x^98304 mod p(x)` << 1, x^98368 mod p(x)` << 1 */
    { 0x0000000051061a8a, 0x00000001c96a8cca },
    /* x^97280 mod p(x)` << 1, x^97344 mod p(x)` << 1 */
    { 0x000000017351388a, 0x00000001a1a5b60c },
    /* x^96256 mod p(x)` << 1, x^96320 mod p(x)` << 1 */
    { 0x0000000132928f92, 0x00000000e4b6ac9c },
    /* x^95232 mod p(x)` << 1, x^95296 mod p(x)` << 1 */
    { 0x00000000e6b4f48a, 0x00000001807e7f5a },
    /* x^94208 mod p(x)` << 1, x^94272 mod p(x)` << 1 */
    { 0x0000000039d15e90, 0x000000017a7e3bc8 },
    /* x^93184 mod p(x)` << 1, x^93248 mod p(x)` << 1 */
    { 0x00000000312d6074, 0x00000000d73975da },
    /* x^92160 mod p(x)` << 1, x^92224 mod p(x)` << 1 */
    { 0x000000017bbb2cc4, 0x000000017375d038 },
    /* x^91136 mod p(x)` << 1, x^91200 mod p(x)` << 1 */
    { 0x000000016ded3e18, 0x00000000193680bc },
    /* x^90112 mod p(x)` << 1, x^90176 mod p(x)` << 1 */
    { 0x00000000f1638b16, 0x00000000999b06f6 },
    /* x^89088 mod p(x)` << 1, x^89152 mod p(x)` << 1 */
    { 0x00000001d38b9ecc, 0x00000001f685d2b8 },
    /* x^88064 mod p(x)` << 1, x^88128 mod p(x)` << 1 */
    { 0x000000018b8d09dc, 0x00000001f4ecbed2 },
    /* x^87040 mod p(x)` << 1, x^87104 mod p(x)` << 1 */
    { 0x00000000e7bc27d2, 0x00000000ba16f1a0 },
    /* x^86016 mod p(x)` << 1, x^86080 mod p(x)` << 1 */
    { 0x00000000275e1e96, 0x0000000115aceac4 },
    /* x^84992 mod p(x)` << 1, x^85056 mod p(x)` << 1 */
    { 0x00000000e2e3031e, 0x00000001aeff6292 },
    /* x^83968 mod p(x)` << 1, x^84032 mod p(x)` << 1 */
    { 0x00000001041c84d8, 0x000000009640124c },
    /* x^82944 mod p(x)` << 1, x^83008 mod p(x)` << 1 */
    { 0x00000000706ce672, 0x0000000114f41f02 },
    /* x^81920 mod p(x)` << 1, x^81984 mod p(x)` << 1 */
    { 0x000000015d5070da, 0x000000009c5f3586 },
    /* x^80896 mod p(x)` << 1, x^80960 mod p(x)` << 1 */
    { 0x0000000038f9493a, 0x00000001878275fa },
    /* x^79872 mod p(x)` << 1, x^79936 mod p(x)` << 1 */
    { 0x00000000a3348a76, 0x00000000ddc42ce8 },
    /* x^78848 mod p(x)` << 1, x^78912 mod p(x)` << 1 */
    { 0x00000001ad0aab92, 0x0000000181d2c73a },
    /* x^77824 mod p(x)` << 1, x^77888 mod p(x)` << 1 */
    { 0x000000019e85f712, 0x0000000141c9320a },
    /* x^76800 mod p(x)` << 1, x^76864 mod p(x)` << 1 */
    { 0x000000005a871e76, 0x000000015235719a },
    /* x^75776 mod p(x)` << 1, x^75840 mod p(x)` << 1 */
    { 0x000000017249c662, 0x00000000be27d804 },
    /* x^74752 mod p(x)` << 1, x^74816 mod p(x)` << 1 */
    { 0x000000003a084712, 0x000000006242d45a },
    /* x^73728 mod p(x)` << 1, x^73792 mod p(x)` << 1 */
    { 0x00000000ed438478, 0x000000009a53638e },
    /* x^72704 mod p(x)` << 1, x^72768 mod p(x)` << 1 */
    { 0x00000000abac34cc, 0x00000001001ecfb6 },
    /* x^71680 mod p(x)` << 1, x^71744 mod p(x)` << 1 */
    { 0x000000005f35ef3e, 0x000000016d7c2d64 },
    /* x^70656 mod p(x)` << 1, x^70720 mod p(x)` << 1 */
    { 0x0000000047d6608c, 0x00000001d0ce46c0 },
    /* x^69632 mod p(x)` << 1, x^69696 mod p(x)` << 1 */
    { 0x000000002d01470e, 0x0000000124c907b4 },
    /* x^68608 mod p(x)` << 1, x^68672 mod p(x)` << 1 */
    { 0x0000000158bbc7b0, 0x0000000018a555ca },
    /* x^67584 mod p(x)` << 1, x^67648 mod p(x)` << 1 */
    { 0x00000000c0a23e8e, 0x000000006b0980bc },
    /* x^66560 mod p(x)` << 1, x^66624 mod p(x)` << 1 */
    { 0x00000001ebd85c88, 0x000000008bbba964 },
    /* x^65536 mod p(x)` << 1, x^65600 mod p(x)` << 1 */
    { 0x000000019ee20bb2, 0x00000001070a5a1e },
    /* x^64512 mod p(x)` << 1, x^64576 mod p(x)` << 1 */
    { 0x00000001acabf2d6, 0x000000002204322a },
    /* x^63488 mod p(x)` << 1, x^63552 mod p(x)` << 1 */
    { 0x00000001b7963d56, 0x00000000a27524d0 },
    /* x^62464 mod p(x)` << 1, x^62528 mod p(x)` << 1 */
    { 0x000000017bffa1fe, 0x0000000020b1e4ba },
    /* x^61440 mod p(x)` << 1, x^61504 mod p(x)` << 1 */
    { 0x000000001f15333e, 0x0000000032cc27fc },
    /* x^60416 mod p(x)` << 1, x^60480 mod p(x)` << 1 */
    { 0x000000018593129e, 0x0000000044dd22b8 },
    /* x^59392 mod p(x)` << 1, x^59456 mod p(x)` << 1 */
    { 0x000000019cb32602, 0x00000000dffc9e0a },
    /* x^58368 mod p(x)` << 1, x^58432 mod p(x)` << 1 */
    { 0x0000000142b05cc8, 0x00000001b7a0ed14 },
    /* x^57344 mod p(x)` << 1, x^57408 mod p(x)` << 1 */
    { 0x00000001be49e7a4, 0x00000000c7842488 },
    /* x^56320 mod p(x)` << 1, x^56384 mod p(x)` << 1 */
    { 0x0000000108f69d6c, 0x00000001c02a4fee },
    /* x^55296 mod p(x)` << 1, x^55360 mod p(x)` << 1 */
    { 0x000000006c0971f0, 0x000000003c273778 },
    /* x^54272 mod p(x)` << 1, x^54336 mod p(x)` << 1 */
    { 0x000000005b16467a, 0x00000001d63f8894 },
    /* x^53248 mod p(x)` << 1, x^53312 mod p(x)` << 1 */
    { 0x00000001551a628e, 0x000000006be557d6 },
    /* x^52224 mod p(x)` << 1, x^52288 mod p(x)` << 1 */
    { 0x000000019e42ea92, 0x000000006a7806ea },
    /* x^51200 mod p(x)` << 1, x^51264 mod p(x)` << 1 */
    { 0x000000012fa83ff2, 0x000000016155aa0c },
    /* x^50176 mod p(x)` << 1, x^50240 mod p(x)` << 1 */
    { 0x000000011ca9cde0, 0x00000000908650ac },
    /* x^49152 mod p(x)` << 1, x^49216 mod p(x)` << 1 */
    { 0x00000000c8e5cd74, 0x00000000aa5a8084 },
    /* x^48128 mod p(x)` << 1, x^48192 mod p(x)` << 1 */
    { 0x0000000096c27f0c, 0x0000000191bb500a },
    /* x^47104 mod p(x)` << 1, x^47168 mod p(x)` << 1 */
    { 0x000000002baed926, 0x0000000064e9bed0 },
    /* x^46080 mod p(x)` << 1, x^46144 mod p(x)` << 1 */
    { 0x000000017c8de8d2, 0x000000009444f302 },
    /* x^45056 mod p(x)` << 1, x^45120 mod p(x)` << 1 */
    { 0x00000000d43d6068, 0x000000019db07d3c },
    /* x^44032 mod p(x)` << 1, x^44096 mod p(x)` << 1 */
    { 0x00000000cb2c4b26, 0x00000001359e3e6e },
    /* x^43008 mod p(x)` << 1, x^43072 mod p(x)` << 1 */
    { 0x0000000145b8da26, 0x00000001e4f10dd2 },
    /* x^41984 mod p(x)` << 1, x^42048 mod p(x)` << 1 */
    { 0x000000018fff4b08, 0x0000000124f5735e },
    /* x^40960 mod p(x)` << 1, x^41024 mod p(x)` << 1 */
    { 0x0000000150b58ed0, 0x0000000124760a4c },
    /* x^39936 mod p(x)` << 1, x^40000 mod p(x)` << 1 */
    { 0x00000001549f39bc, 0x000000000f1fc186 },
    /* x^38912 mod p(x)` << 1, x^38976 mod p(x)` << 1 */
    { 0x00000000ef4d2f42, 0x00000000150e4cc4 },
    /* x^37888 mod p(x)` << 1, x^37952 mod p(x)` << 1 */
    { 0x00000001b1468572, 0x000000002a6204e8 },
    /* x^36864 mod p(x)` << 1, x^36928 mod p(x)` << 1 */
    { 0x000000013d7403b2, 0x00000000beb1d432 },
    /* x^35840 mod p(x)` << 1, x^35904 mod p(x)` << 1 */
    { 0x00000001a4681842, 0x0000000135f3f1f0 },
    /* x^34816 mod p(x)` << 1, x^34880 mod p(x)` << 1 */
    { 0x0000000167714492, 0x0000000074fe2232 },
    /* x^33792 mod p(x)` << 1, x^33856 mod p(x)` << 1 */
    { 0x00000001e599099a, 0x000000001ac6e2ba },
    /* x^32768 mod p(x)` << 1, x^32832 mod p(x)` << 1 */
    { 0x00000000fe128194, 0x0000000013fca91e },
    /* x^31744 mod p(x)` << 1, x^31808 mod p(x)` << 1 */
    { 0x0000000077e8b990, 0x0000000183f4931e },
    /* x^30720 mod p(x)` << 1, x^30784 mod p(x)` << 1 */
    { 0x00000001a267f63a, 0x00000000b6d9b4e4 },
    /* x^29696 mod p(x)` << 1, x^29760 mod p(x)` << 1 */
    { 0x00000001945c245a, 0x00000000b5188656 },
    /* x^28672 mod p(x)` << 1, x^28736 mod p(x)` << 1 */
    { 0x0000000149002e76, 0x0000000027a81a84 },
    /* x^27648 mod p(x)` << 1, x^27712 mod p(x)` << 1 */
    { 0x00000001bb8310a4, 0x0000000125699258 },
    /* x^26624 mod p(x)` << 1, x^26688 mod p(x)` << 1 */
    { 0x000000019ec60bcc, 0x00000001b23de796 },
    /* x^25600 mod p(x)` << 1, x^25664 mod p(x)` << 1 */
    { 0x000000012d8590ae, 0x00000000fe4365dc },
    /* x^24576 mod p(x)` << 1, x^24640 mod p(x)` << 1 */
    { 0x0000000065b00684, 0x00000000c68f497a },
    /* x^23552 mod p(x)` << 1, x^23616 mod p(x)` << 1 */
    { 0x000000015e5aeadc, 0x00000000fbf521ee },
    /* x^22528 mod p(x)` << 1, x^22592 mod p(x)` << 1 */
    { 0x00000000b77ff2b0, 0x000000015eac3378 },
    /* x^21504 mod p(x)` << 1, x^21568 mod p(x)` << 1 */
    { 0x0000000188da2ff6, 0x0000000134914b90 },
    /* x^20480 mod p(x)` << 1, x^20544 mod p(x)` << 1 */
    { 0x0000000063da929a, 0x0000000016335cfe },
    /* x^19456 mod p(x)` << 1, x^19520 mod p(x)` << 1 */
    { 0x00000001389caa80, 0x000000010372d10c },
    /* x^18432 mod p(x)` << 1, x^18496 mod p(x)` << 1 */
    { 0x000000013db599d2, 0x000000015097b908 },
    /* x^17408 mod p(x)` << 1, x^17472 mod p(x)` << 1 */
    { 0x0000000122505a86, 0x00000001227a7572 },
    /* x^16384 mod p(x)` << 1, x^16448 mod p(x)` << 1 */
    { 0x000000016bd72746, 0x000000009a8f75c0 },
    /* x^15360 mod p(x)` << 1, x^15424 mod p(x)` << 1 */
    { 0x00000001c3faf1d4, 0x00000000682c77a2 },
    /* x^14336 mod p(x)` << 1, x^14400 mod p(x)` << 1 */
    { 0x00000001111c826c, 0x00000000231f091c },
    /* x^13312 mod p(x)` << 1, x^13376 mod p(x)` << 1 */
    { 0x00000000153e9fb2, 0x000000007d4439f2 },
    /* x^12288 mod p(x)` << 1, x^12352 mod p(x)` << 1 */
    { 0x000000002b1f7b60, 0x000000017e221efc },
    /* x^11264 mod p(x)` << 1, x^11328 mod p(x)` << 1 */
    { 0x00000000b1dba570, 0x0000000167457c38 },
    /* x^10240 mod p(x)` << 1, x^10304 mod p(x)` << 1 */
    { 0x00000001f6397b76, 0x00000000bdf081c4 },
    /* x^9216 mod p(x)` << 1, x^9280 mod p(x)` << 1 */
    { 0x0000000156335214, 0x000000016286d6b0 },
    /* x^8192 mod p(x)` << 1, x^8256 mod p(x)` << 1 */
    { 0x00000001d70e3986, 0x00000000c84f001c },
    /* x^7168 mod p(x)` << 1, x^7232 mod p(x)` << 1 */
    { 0x000000003701a774, 0x0000000064efe7c0 },
    /* x^6144 mod p(x)` << 1, x^6208 mod p(x)` << 1 */
    { 0x00000000ac81ef72, 0x000000000ac2d904 },
    /* x^5120 mod p(x)` << 1, x^5184 mod p(x)` << 1 */
    { 0x0000000133212464, 0x00000000fd226d14 },
    /* x^4096 mod p(x)` << 1, x^4160 mod p(x)` << 1 */
    { 0x00000000e4e45610, 0x000000011cfd42e0 },
    /* x^3072 mod p(x)` << 1, x^3136 mod p(x)` << 1 */
    { 0x000000000c1bd370, 0x000000016e5a5678 },
    /* x^2048 mod p(x)` << 1, x^2112 mod p(x)` << 1 */
    { 0x00000001a7b9e7a6, 0x00000001d888fe22 },
    /* x^1024 mod p(x)` << 1, x^1088 mod p(x)` << 1 */
    { 0x000000007d657a10, 0x00000001af77fcd4 }
#else /* BYTE_ORDER == LITTLE_ENDIAN */
    /* x^261120 mod p(x)` << 1, x^261184 mod p(x)` << 1 */
    { 0x00000001651797d2, 0x0000000099ea94a8 },
    /* x^260096 mod p(x)` << 1, x^260160 mod p(x)` << 1 */
    { 0x0000000021e0d56c, 0x00000000945a8420 },
    /* x^259072 mod p(x)` << 1, x^259136 mod p(x)` << 1 */
    { 0x000000000f95ecaa, 0x0000000030762706 },
    /* x^258048 mod p(x)` << 1, x^258112 mod p(x)` << 1 */
    { 0x00000001ebd224ac, 0x00000001a52fc582 },
    /* x^257024 mod p(x)` << 1, x^257088 mod p(x)` << 1 */
    { 0x000000000ccb97ca, 0x00000001a4a7167a },
    /* x^256000 mod p(x)` << 1, x^256064 mod p(x)` << 1 */
    { 0x00000001006ec8a8, 0x000000000c18249a },
    /* x^254976 mod p(x)` << 1, x^255040 mod p(x)` << 1 */
    { 0x000000014f58f196, 0x00000000a924ae7c },
    /* x^253952 mod p(x)` << 1, x^254016 mod p(x)` << 1 */
    { 0x00000001a7192ca6, 0x00000001e12ccc12 },
    /* x^252928 mod p(x)` << 1, x^252992 mod p(x)` << 1 */
    { 0x000000019a64bab2, 0x00000000a0b9d4ac },
    /* x^251904 mod p(x)` << 1, x^251968 mod p(x)` << 1 */
    { 0x0000000014f4ed2e, 0x0000000095e8ddfe },
    /* x^250880 mod p(x)` << 1, x^250944 mod p(x)` << 1 */
    { 0x000000011092b6a2, 0x00000000233fddc4 },
    /* x^249856 mod p(x)` << 1, x^249920 mod p(x)` << 1 */
    { 0x00000000c8a1629c, 0x00000001b4529b62 },
    /* x^248832 mod p(x)` << 1, x^248896 mod p(x)` << 1 */
    { 0x000000017bf32e8e, 0x00000001a7fa0e64 },
    /* x^247808 mod p(x)` << 1, x^247872 mod p(x)` << 1 */
    { 0x00000001f8cc6582, 0x00000001b5334592 },
    /* x^246784 mod p(x)` << 1, x^246848 mod p(x)` << 1 */
    { 0x000000008631ddf0, 0x000000011f8ee1b4 },
    /* x^245760 mod p(x)` << 1, x^245824 mod p(x)` << 1 */
    { 0x000000007e5a76d0, 0x000000006252e632 },
    /* x^244736 mod p(x)` << 1, x^244800 mod p(x)` << 1 */
    { 0x000000002b09b31c, 0x00000000ab973e84 },
    /* x^243712 mod p(x)` << 1, x^243776 mod p(x)` << 1 */
    { 0x00000001b2df1f84, 0x000000007734f5ec },
    /* x^242688 mod p(x)` << 1, x^242752 mod p(x)` << 1 */
    { 0x00000001d6f56afc, 0x000000007c547798 },
    /* x^241664 mod p(x)` << 1, x^241728 mod p(x)` << 1 */
    { 0x00000001b9b5e70c, 0x000000007ec40210 },
    /* x^240640 mod p(x)` << 1, x^240704 mod p(x)` << 1 */
    { 0x0000000034b626d2, 0x00000001ab1695a8 },
    /* x^239616 mod p(x)` << 1, x^239680 mod p(x)` << 1 */
    { 0x000000014c53479a, 0x0000000090494bba },
    /* x^238592 mod p(x)` << 1, x^238656 mod p(x)` << 1 */
    { 0x00000001a6d179a4, 0x00000001123fb816 },
    /* x^237568 mod p(x)` << 1, x^237632 mod p(x)` << 1 */
    { 0x000000015abd16b4, 0x00000001e188c74c },
    /* x^236544 mod p(x)` << 1, x^236608 mod p(x)` << 1 */
    { 0x00000000018f9852, 0x00000001c2d3451c },
    /* x^235520 mod p(x)` << 1, x^235584 mod p(x)` << 1 */
    { 0x000000001fb3084a, 0x00000000f55cf1ca },
    /* x^234496 mod p(x)` << 1, x^234560 mod p(x)` << 1 */
    { 0x00000000c53dfb04, 0x00000001a0531540 },
    /* x^233472 mod p(x)` << 1, x^233536 mod p(x)` << 1 */
    { 0x00000000e10c9ad6, 0x0000000132cd7ebc },
    /* x^232448 mod p(x)` << 1, x^232512 mod p(x)` << 1 */
    { 0x0000000025aa994a, 0x0000000073ab7f36 },
    /* x^231424 mod p(x)` << 1, x^231488 mod p(x)` << 1 */
    { 0x00000000fa3a74c4, 0x0000000041aed1c2 },
    /* x^230400 mod p(x)` << 1, x^230464 mod p(x)` << 1 */
    { 0x0000000033eb3f40, 0x0000000136c53800 },
    /* x^229376 mod p(x)` << 1, x^229440 mod p(x)` << 1 */
    { 0x000000017193f296, 0x0000000126835a30 },
    /* x^228352 mod p(x)` << 1, x^228416 mod p(x)` << 1 */
    { 0x0000000043f6c86a, 0x000000006241b502 },
    /* x^227328 mod p(x)` << 1, x^227392 mod p(x)` << 1 */
    { 0x000000016b513ec6, 0x00000000d5196ad4 },
    /* x^226304 mod p(x)` << 1, x^226368 mod p(x)` << 1 */
    { 0x00000000c8f25b4e, 0x000000009cfa769a },
    /* x^225280 mod p(x)` << 1, x^225344 mod p(x)` << 1 */
    { 0x00000001a45048ec, 0x00000000920e5df4 },
    /* x^224256 mod p(x)` << 1, x^224320 mod p(x)` << 1 */
    { 0x000000000c441004, 0x0000000169dc310e },
    /* x^223232 mod p(x)` << 1, x^223296 mod p(x)` << 1 */
    { 0x000000000e17cad6, 0x0000000009fc331c },
    /* x^222208 mod p(x)` << 1, x^222272 mod p(x)` << 1 */
    { 0x00000001253ae964, 0x000000010d94a81e },
    /* x^221184 mod p(x)` << 1, x^221248 mod p(x)` << 1 */
    { 0x00000001d7c88ebc, 0x0000000027a20ab2 },
    /* x^220160 mod p(x)` << 1, x^220224 mod p(x)` << 1 */
    { 0x00000001e7ca913a, 0x0000000114f87504 },
    /* x^219136 mod p(x)` << 1, x^219200 mod p(x)` << 1 */
    { 0x0000000033ed078a, 0x000000004b076d96 },
    /* x^218112 mod p(x)` << 1, x^218176 mod p(x)` << 1 */
    { 0x00000000e1839c78, 0x00000000da4d1e74 },
    /* x^217088 mod p(x)` << 1, x^217152 mod p(x)` << 1 */
    { 0x00000001322b267e, 0x000000001b81f672 },
    /* x^216064 mod p(x)` << 1, x^216128 mod p(x)` << 1 */
    { 0x00000000638231b6, 0x000000009367c988 },
    /* x^215040 mod p(x)` << 1, x^215104 mod p(x)` << 1 */
    { 0x00000001ee7f16f4, 0x00000001717214ca },
    /* x^214016 mod p(x)` << 1, x^214080 mod p(x)` << 1 */
    { 0x0000000117d9924a, 0x000000009f47d820 },
    /* x^212992 mod p(x)` << 1, x^213056 mod p(x)` << 1 */
    { 0x00000000e1a9e0c4, 0x000000010d9a47d2 },
    /* x^211968 mod p(x)` << 1, x^212032 mod p(x)` << 1 */
    { 0x00000001403731dc, 0x00000000a696c58c },
    /* x^210944 mod p(x)` << 1, x^211008 mod p(x)` << 1 */
    { 0x00000001a5ea9682, 0x000000002aa28ec6 },
    /* x^209920 mod p(x)` << 1, x^209984 mod p(x)` << 1 */
    { 0x0000000101c5c578, 0x00000001fe18fd9a },
    /* x^208896 mod p(x)` << 1, x^208960 mod p(x)` << 1 */
    { 0x00000000dddf6494, 0x000000019d4fc1ae },
    /* x^207872 mod p(x)` << 1, x^207936 mod p(x)` << 1 */
    { 0x00000000f1c3db28, 0x00000001ba0e3dea },
    /* x^206848 mod p(x)` << 1, x^206912 mod p(x)` << 1 */
    { 0x000000013112fb9c, 0x0000000074b59a5e },
    /* x^205824 mod p(x)` << 1, x^205888 mod p(x)` << 1 */
    { 0x00000000b680b906, 0x00000000f2b5ea98 },
    /* x^204800 mod p(x)` << 1, x^204864 mod p(x)` << 1 */
    { 0x000000001a282932, 0x0000000187132676 },
    /* x^203776 mod p(x)` << 1, x^203840 mod p(x)` << 1 */
    { 0x0000000089406e7e, 0x000000010a8c6ad4 },
    /* x^202752 mod p(x)` << 1, x^202816 mod p(x)` << 1 */
    { 0x00000001def6be8c, 0x00000001e21dfe70 },
    /* x^201728 mod p(x)` << 1, x^201792 mod p(x)` << 1 */
    { 0x0000000075258728, 0x00000001da0050e4 },
    /* x^200704 mod p(x)` << 1, x^200768 mod p(x)` << 1 */
    { 0x000000019536090a, 0x00000000772172ae },
    /* x^199680 mod p(x)` << 1, x^199744 mod p(x)` << 1 */
    { 0x00000000f2455bfc, 0x00000000e47724aa },
    /* x^198656 mod p(x)` << 1, x^198720 mod p(x)` << 1 */
    { 0x000000018c40baf4, 0x000000003cd63ac4 },
    /* x^197632 mod p(x)` << 1, x^197696 mod p(x)` << 1 */
    { 0x000000004cd390d4, 0x00000001bf47d352 },
    /* x^196608 mod p(x)` << 1, x^196672 mod p(x)` << 1 */
    { 0x00000001e4ece95a, 0x000000018dc1d708 },
    /* x^195584 mod p(x)` << 1, x^195648 mod p(x)` << 1 */
    { 0x000000001a3ee918, 0x000000002d4620a4 },
    /* x^194560 mod p(x)` << 1, x^194624 mod p(x)` << 1 */
    { 0x000000007c652fb8, 0x0000000058fd1740 },
    /* x^193536 mod p(x)` << 1, x^193600 mod p(x)` << 1 */
    { 0x000000011c67842c, 0x00000000dadd9bfc },
    /* x^192512 mod p(x)` << 1, x^192576 mod p(x)` << 1 */
    { 0x00000000254f759c, 0x00000001ea2140be },
    /* x^191488 mod p(x)` << 1, x^191552 mod p(x)` << 1 */
    { 0x000000007ece94ca, 0x000000009de128ba },
    /* x^190464 mod p(x)` << 1, x^190528 mod p(x)` << 1 */
    { 0x0000000038f258c2, 0x000000013ac3aa8e },
    /* x^189440 mod p(x)` << 1, x^189504 mod p(x)` << 1 */
    { 0x00000001cdf17b00, 0x0000000099980562 },
    /* x^188416 mod p(x)` << 1, x^188480 mod p(x)` << 1 */
    { 0x000000011f882c16, 0x00000001c1579c86 },
    /* x^187392 mod p(x)` << 1, x^187456 mod p(x)` << 1 */
    { 0x0000000100093fc8, 0x0000000068dbbf94 },
    /* x^186368 mod p(x)` << 1, x^186432 mod p(x)` << 1 */
    { 0x00000001cd684f16, 0x000000004509fb04 },
    /* x^185344 mod p(x)` << 1, x^185408 mod p(x)` << 1 */
    { 0x000000004bc6a70a, 0x00000001202f6398 },
    /* x^184320 mod p(x)` << 1, x^184384 mod p(x)` << 1 */
    { 0x000000004fc7e8e4, 0x000000013aea243e },
    /* x^183296 mod p(x)` << 1, x^183360 mod p(x)` << 1 */
    { 0x0000000130103f1c, 0x00000001b4052ae6 },
    /* x^182272 mod p(x)` << 1, x^182336 mod p(x)` << 1 */
    { 0x0000000111b0024c, 0x00000001cd2a0ae8 },
    /* x^181248 mod p(x)` << 1, x^181312 mod p(x)` << 1 */
    { 0x000000010b3079da, 0x00000001fe4aa8b4 },
    /* x^180224 mod p(x)` << 1, x^180288 mod p(x)` << 1 */
    { 0x000000010192bcc2, 0x00000001d1559a42 },
    /* x^179200 mod p(x)` << 1, x^179264 mod p(x)` << 1 */
    { 0x0000000074838d50, 0x00000001f3e05ecc },
    /* x^178176 mod p(x)` << 1, x^178240 mod p(x)` << 1 */
    { 0x000000001b20f520, 0x0000000104ddd2cc },
    /* x^177152 mod p(x)` << 1, x^177216 mod p(x)` << 1 */
    { 0x0000000050c3590a, 0x000000015393153c },
    /* x^176128 mod p(x)` << 1, x^176192 mod p(x)` << 1 */
    { 0x00000000b41cac8e, 0x0000000057e942c6 },
    /* x^175104 mod p(x)` << 1, x^175168 mod p(x)` << 1 */
    { 0x000000000c72cc78, 0x000000012c633850 },
    /* x^174080 mod p(x)` << 1, x^174144 mod p(x)` << 1 */
    { 0x0000000030cdb032, 0x00000000ebcaae4c },
    /* x^173056 mod p(x)` << 1, x^173120 mod p(x)` << 1 */
    { 0x000000013e09fc32, 0x000000013ee532a6 },
    /* x^172032 mod p(x)` << 1, x^172096 mod p(x)` << 1 */
    { 0x000000001ed624d2, 0x00000001bf0cbc7e },
    /* x^171008 mod p(x)` << 1, x^171072 mod p(x)` << 1 */
    { 0x00000000781aee1a, 0x00000000d50b7a5a },
    /* x^169984 mod p(x)` << 1, x^170048 mod p(x)` << 1 */
    { 0x00000001c4d8348c, 0x0000000002fca6e8 },
    /* x^168960 mod p(x)` << 1, x^169024 mod p(x)` << 1 */
    { 0x0000000057a40336, 0x000000007af40044 },
    /* x^167936 mod p(x)` << 1, x^168000 mod p(x)` << 1 */
    { 0x0000000085544940, 0x0000000016178744 },
    /* x^166912 mod p(x)` << 1, x^166976 mod p(x)` << 1 */
    { 0x000000019cd21e80, 0x000000014c177458 },
    /* x^165888 mod p(x)` << 1, x^165952 mod p(x)` << 1 */
    { 0x000000013eb95bc0, 0x000000011b6ddf04 },
    /* x^164864 mod p(x)` << 1, x^164928 mod p(x)` << 1 */
    { 0x00000001dfc9fdfc, 0x00000001f3e29ccc },
    /* x^163840 mod p(x)` << 1, x^163904 mod p(x)` << 1 */
    { 0x00000000cd028bc2, 0x0000000135ae7562 },
    /* x^162816 mod p(x)` << 1, x^162880 mod p(x)` << 1 */
    { 0x0000000090db8c44, 0x0000000190ef812c },
    /* x^161792 mod p(x)` << 1, x^161856 mod p(x)` << 1 */
    { 0x000000010010a4ce, 0x0000000067a2c786 },
    /* x^160768 mod p(x)` << 1, x^160832 mod p(x)` << 1 */
    { 0x00000001c8f4c72c, 0x0000000048b9496c },
    /* x^159744 mod p(x)` << 1, x^159808 mod p(x)` << 1 */
    { 0x000000001c26170c, 0x000000015a422de6 },
    /* x^158720 mod p(x)` << 1, x^158784 mod p(x)` << 1 */
    { 0x00000000e3fccf68, 0x00000001ef0e3640 },
    /* x^157696 mod p(x)` << 1, x^157760 mod p(x)` << 1 */
    { 0x00000000d513ed24, 0x00000001006d2d26 },
    /* x^156672 mod p(x)` << 1, x^156736 mod p(x)` << 1 */
    { 0x00000000141beada, 0x00000001170d56d6 },
    /* x^155648 mod p(x)` << 1, x^155712 mod p(x)` << 1 */
    { 0x000000011071aea0, 0x00000000a5fb613c },
    /* x^154624 mod p(x)` << 1, x^154688 mod p(x)` << 1 */
    { 0x000000012e19080a, 0x0000000040bbf7fc },
    /* x^153600 mod p(x)` << 1, x^153664 mod p(x)` << 1 */
    { 0x0000000100ecf826, 0x000000016ac3a5b2 },
    /* x^152576 mod p(x)` << 1, x^152640 mod p(x)` << 1 */
    { 0x0000000069b09412, 0x00000000abf16230 },
    /* x^151552 mod p(x)` << 1, x^151616 mod p(x)` << 1 */
    { 0x0000000122297bac, 0x00000001ebe23fac },
    /* x^150528 mod p(x)` << 1, x^150592 mod p(x)` << 1 */
    { 0x00000000e9e4b068, 0x000000008b6a0894 },
    /* x^149504 mod p(x)` << 1, x^149568 mod p(x)` << 1 */
    { 0x000000004b38651a, 0x00000001288ea478 },
    /* x^148480 mod p(x)` << 1, x^148544 mod p(x)` << 1 */
    { 0x00000001468360e2, 0x000000016619c442 },
    /* x^147456 mod p(x)` << 1, x^147520 mod p(x)` << 1 */
    { 0x00000000121c2408, 0x0000000086230038 },
    /* x^146432 mod p(x)` << 1, x^146496 mod p(x)` << 1 */
    { 0x00000000da7e7d08, 0x000000017746a756 },
    /* x^145408 mod p(x)` << 1, x^145472 mod p(x)` << 1 */
    { 0x00000001058d7652, 0x0000000191b8f8f8 },
    /* x^144384 mod p(x)` << 1, x^144448 mod p(x)` << 1 */
    { 0x000000014a098a90, 0x000000008e167708 },
    /* x^143360 mod p(x)` << 1, x^143424 mod p(x)` << 1 */
    { 0x0000000020dbe72e, 0x0000000148b22d54 },
    /* x^142336 mod p(x)` << 1, x^142400 mod p(x)` << 1 */
    { 0x000000011e7323e8, 0x0000000044ba2c3c },
    /* x^141312 mod p(x)` << 1, x^141376 mod p(x)` << 1 */
    { 0x00000000d5d4bf94, 0x00000000b54d2b52 },
    /* x^140288 mod p(x)` << 1, x^140352 mod p(x)` << 1 */
    { 0x0000000199d8746c, 0x0000000005a4fd8a },
    /* x^139264 mod p(x)` << 1, x^139328 mod p(x)` << 1 */
    { 0x00000000ce9ca8a0, 0x0000000139f9fc46 },
    /* x^138240 mod p(x)` << 1, x^138304 mod p(x)` << 1 */
    { 0x00000000136edece, 0x000000015a1fa824 },
    /* x^137216 mod p(x)` << 1, x^137280 mod p(x)` << 1 */
    { 0x000000019b92a068, 0x000000000a61ae4c },
    /* x^136192 mod p(x)` << 1, x^136256 mod p(x)` << 1 */
    { 0x0000000071d62206, 0x0000000145e9113e },
    /* x^135168 mod p(x)` << 1, x^135232 mod p(x)` << 1 */
    { 0x00000000dfc50158, 0x000000006a348448 },
    /* x^134144 mod p(x)` << 1, x^134208 mod p(x)` << 1 */
    { 0x00000001517626bc, 0x000000004d80a08c },
    /* x^133120 mod p(x)` << 1, x^133184 mod p(x)` << 1 */
    { 0x0000000148d1e4fa, 0x000000014b6837a0 },
    /* x^132096 mod p(x)` << 1, x^132160 mod p(x)` << 1 */
    { 0x0000000094d8266e, 0x000000016896a7fc },
    /* x^131072 mod p(x)` << 1, x^131136 mod p(x)` << 1 */
    { 0x00000000606c5e34, 0x000000014f187140 },
    /* x^130048 mod p(x)` << 1, x^130112 mod p(x)` << 1 */
    { 0x000000019766beaa, 0x000000019581b9da },
    /* x^129024 mod p(x)` << 1, x^129088 mod p(x)` << 1 */
    { 0x00000001d80c506c, 0x00000001091bc984 },
    /* x^128000 mod p(x)` << 1, x^128064 mod p(x)` << 1 */
    { 0x000000001e73837c, 0x000000001067223c },
    /* x^126976 mod p(x)` << 1, x^127040 mod p(x)` << 1 */
    { 0x0000000064d587de, 0x00000001ab16ea02 },
    /* x^125952 mod p(x)` << 1, x^126016 mod p(x)` << 1 */
    { 0x00000000f4a507b0, 0x000000013c4598a8 },
    /* x^124928 mod p(x)` << 1, x^124992 mod p(x)` << 1 */
    { 0x0000000040e342fc, 0x00000000b3735430 },
    /* x^123904 mod p(x)` << 1, x^123968 mod p(x)` << 1 */
    { 0x00000001d5ad9c3a, 0x00000001bb3fc0c0 },
    /* x^122880 mod p(x)` << 1, x^122944 mod p(x)` << 1 */
    { 0x0000000094a691a4, 0x00000001570ae19c },
    /* x^121856 mod p(x)` << 1, x^121920 mod p(x)` << 1 */
    { 0x00000001271ecdfa, 0x00000001ea910712 },
    /* x^120832 mod p(x)` << 1, x^120896 mod p(x)` << 1 */
    { 0x000000009e54475a, 0x0000000167127128 },
    /* x^119808 mod p(x)` << 1, x^119872 mod p(x)` << 1 */
    { 0x00000000c9c099ee, 0x0000000019e790a2 },
    /* x^118784 mod p(x)` << 1, x^118848 mod p(x)` << 1 */
    { 0x000000009a2f736c, 0x000000003788f710 },
    /* x^117760 mod p(x)` << 1, x^117824 mod p(x)` << 1 */
    { 0x00000000bb9f4996, 0x00000001682a160e },
    /* x^116736 mod p(x)` << 1, x^116800 mod p(x)` << 1 */
    { 0x00000001db688050, 0x000000007f0ebd2e },
    /* x^115712 mod p(x)` << 1, x^115776 mod p(x)` << 1 */
    { 0x00000000e9b10af4, 0x000000002b032080 },
    /* x^114688 mod p(x)` << 1, x^114752 mod p(x)` << 1 */
    { 0x000000012d4545e4, 0x00000000cfd1664a },
    /* x^113664 mod p(x)` << 1, x^113728 mod p(x)` << 1 */
    { 0x000000000361139c, 0x00000000aa1181c2 },
    /* x^112640 mod p(x)` << 1, x^112704 mod p(x)` << 1 */
    { 0x00000001a5a1a3a8, 0x00000000ddd08002 },
    /* x^111616 mod p(x)` << 1, x^111680 mod p(x)` << 1 */
    { 0x000000006844e0b0, 0x00000000e8dd0446 },
    /* x^110592 mod p(x)` << 1, x^110656 mod p(x)` << 1 */
    { 0x00000000c3762f28, 0x00000001bbd94a00 },
    /* x^109568 mod p(x)` << 1, x^109632 mod p(x)` << 1 */
    { 0x00000001d26287a2, 0x00000000ab6cd180 },
    /* x^108544 mod p(x)` << 1, x^108608 mod p(x)` << 1 */
    { 0x00000001f6f0bba8, 0x0000000031803ce2 },
    /* x^107520 mod p(x)` << 1, x^107584 mod p(x)` << 1 */
    { 0x000000002ffabd62, 0x0000000024f40b0c },
    /* x^106496 mod p(x)` << 1, x^106560 mod p(x)` << 1 */
    { 0x00000000fb4516b8, 0x00000001ba1d9834 },
    /* x^105472 mod p(x)` << 1, x^105536 mod p(x)` << 1 */
    { 0x000000018cfa961c, 0x0000000104de61aa },
    /* x^104448 mod p(x)` << 1, x^104512 mod p(x)` << 1 */
    { 0x000000019e588d52, 0x0000000113e40d46 },
    /* x^103424 mod p(x)` << 1, x^103488 mod p(x)` << 1 */
    { 0x00000001180f0bbc, 0x00000001415598a0 },
    /* x^102400 mod p(x)` << 1, x^102464 mod p(x)` << 1 */
    { 0x00000000e1d9177a, 0x00000000bf6c8c90 },
    /* x^101376 mod p(x)` << 1, x^101440 mod p(x)` << 1 */
    { 0x0000000105abc27c, 0x00000001788b0504 },
    /* x^100352 mod p(x)` << 1, x^100416 mod p(x)` << 1 */
    { 0x00000000972e4a58, 0x0000000038385d02 },
    /* x^99328 mod p(x)` << 1, x^99392 mod p(x)` << 1 */
    { 0x0000000183499a5e, 0x00000001b6c83844 },
    /* x^98304 mod p(x)` << 1, x^98368 mod p(x)` << 1 */
    { 0x00000001c96a8cca, 0x0000000051061a8a },
    /* x^97280 mod p(x)` << 1, x^97344 mod p(x)` << 1 */
    { 0x00000001a1a5b60c, 0x000000017351388a },
    /* x^96256 mod p(x)` << 1, x^96320 mod p(x)` << 1 */
    { 0x00000000e4b6ac9c, 0x0000000132928f92 },
    /* x^95232 mod p(x)` << 1, x^95296 mod p(x)` << 1 */
    { 0x00000001807e7f5a, 0x00000000e6b4f48a },
    /* x^94208 mod p(x)` << 1, x^94272 mod p(x)` << 1 */
    { 0x000000017a7e3bc8, 0x0000000039d15e90 },
    /* x^93184 mod p(x)` << 1, x^93248 mod p(x)` << 1 */
    { 0x00000000d73975da, 0x00000000312d6074 },
    /* x^92160 mod p(x)` << 1, x^92224 mod p(x)` << 1 */
    { 0x000000017375d038, 0x000000017bbb2cc4 },
    /* x^91136 mod p(x)` << 1, x^91200 mod p(x)` << 1 */
    { 0x00000000193680bc, 0x000000016ded3e18 },
    /* x^90112 mod p(x)` << 1, x^90176 mod p(x)` << 1 */
    { 0x00000000999b06f6, 0x00000000f1638b16 },
    /* x^89088 mod p(x)` << 1, x^89152 mod p(x)` << 1 */
    { 0x00000001f685d2b8, 0x00000001d38b9ecc },
    /* x^88064 mod p(x)` << 1, x^88128 mod p(x)` << 1 */
    { 0x00000001f4ecbed2, 0x000000018b8d09dc },
    /* x^87040 mod p(x)` << 1, x^87104 mod p(x)` << 1 */
    { 0x00000000ba16f1a0, 0x00000000e7bc27d2 },
    /* x^86016 mod p(x)` << 1, x^86080 mod p(x)` << 1 */
    { 0x0000000115aceac4, 0x00000000275e1e96 },
    /* x^84992 mod p(x)` << 1, x^85056 mod p(x)` << 1 */
    { 0x00000001aeff6292, 0x00000000e2e3031e },
    /* x^83968 mod p(x)` << 1, x^84032 mod p(x)` << 1 */
    { 0x000000009640124c, 0x00000001041c84d8 },
    /* x^82944 mod p(x)` << 1, x^83008 mod p(x)` << 1 */
    { 0x0000000114f41f02, 0x00000000706ce672 },
    /* x^81920 mod p(x)` << 1, x^81984 mod p(x)` << 1 */
    { 0x000000009c5f3586, 0x000000015d5070da },
    /* x^80896 mod p(x)` << 1, x^80960 mod p(x)` << 1 */
    { 0x00000001878275fa, 0x0000000038f9493a },
    /* x^79872 mod p(x)` << 1, x^79936 mod p(x)` << 1 */
    { 0x00000000ddc42ce8, 0x00000000a3348a76 },
    /* x^78848 mod p(x)` << 1, x^78912 mod p(x)` << 1 */
    { 0x0000000181d2c73a, 0x00000001ad0aab92 },
    /* x^77824 mod p(x)` << 1, x^77888 mod p(x)` << 1 */
    { 0x0000000141c9320a, 0x000000019e85f712 },
    /* x^76800 mod p(x)` << 1, x^76864 mod p(x)` << 1 */
    { 0x000000015235719a, 0x000000005a871e76 },
    /* x^75776 mod p(x)` << 1, x^75840 mod p(x)` << 1 */
    { 0x00000000be27d804, 0x000000017249c662 },
    /* x^74752 mod p(x)` << 1, x^74816 mod p(x)` << 1 */
    { 0x000000006242d45a, 0x000000003a084712 },
    /* x^73728 mod p(x)` << 1, x^73792 mod p(x)` << 1 */
    { 0x000000009a53638e, 0x00000000ed438478 },
    /* x^72704 mod p(x)` << 1, x^72768 mod p(x)` << 1 */
    { 0x00000001001ecfb6, 0x00000000abac34cc },
    /* x^71680 mod p(x)` << 1, x^71744 mod p(x)` << 1 */
    { 0x000000016d7c2d64, 0x000000005f35ef3e },
    /* x^70656 mod p(x)` << 1, x^70720 mod p(x)` << 1 */
    { 0x00000001d0ce46c0, 0x0000000047d6608c },
    /* x^69632 mod p(x)` << 1, x^69696 mod p(x)` << 1 */
    { 0x0000000124c907b4, 0x000000002d01470e },
    /* x^68608 mod p(x)` << 1, x^68672 mod p(x)` << 1 */
    { 0x0000000018a555ca, 0x0000000158bbc7b0 },
    /* x^67584 mod p(x)` << 1, x^67648 mod p(x)` << 1 */
    { 0x000000006b0980bc, 0x00000000c0a23e8e },
    /* x^66560 mod p(x)` << 1, x^66624 mod p(x)` << 1 */
    { 0x000000008bbba964, 0x00000001ebd85c88 },
    /* x^65536 mod p(x)` << 1, x^65600 mod p(x)` << 1 */
    { 0x00000001070a5a1e, 0x000000019ee20bb2 },
    /* x^64512 mod p(x)` << 1, x^64576 mod p(x)` << 1 */
    { 0x000000002204322a, 0x00000001acabf2d6 },
    /* x^63488 mod p(x)` << 1, x^63552 mod p(x)` << 1 */
    { 0x00000000a27524d0, 0x00000001b7963d56 },
    /* x^62464 mod p(x)` << 1, x^62528 mod p(x)` << 1 */
    { 0x0000000020b1e4ba, 0x000000017bffa1fe },
    /* x^61440 mod p(x)` << 1, x^61504 mod p(x)` << 1 */
    { 0x0000000032cc27fc, 0x000000001f15333e },
    /* x^60416 mod p(x)` << 1, x^60480 mod p(x)` << 1 */
    { 0x0000000044dd22b8, 0x000000018593129e },
    /* x^59392 mod p(x)` << 1, x^59456 mod p(x)` << 1 */
    { 0x00000000dffc9e0a, 0x000000019cb32602 },
    /* x^58368 mod p(x)` << 1, x^58432 mod p(x)` << 1 */
    { 0x00000001b7a0ed14, 0x0000000142b05cc8 },
    /* x^57344 mod p(x)` << 1, x^57408 mod p(x)` << 1 */
    { 0x00000000c7842488, 0x00000001be49e7a4 },
    /* x^56320 mod p(x)` << 1, x^56384 mod p(x)` << 1 */
    { 0x00000001c02a4fee, 0x0000000108f69d6c },
    /* x^55296 mod p(x)` << 1, x^55360 mod p(x)` << 1 */
    { 0x000000003c273778, 0x000000006c0971f0 },
    /* x^54272 mod p(x)` << 1, x^54336 mod p(x)` << 1 */
    { 0x00000001d63f8894, 0x000000005b16467a },
    /* x^53248 mod p(x)` << 1, x^53312 mod p(x)` << 1 */
    { 0x000000006be557d6, 0x00000001551a628e },
    /* x^52224 mod p(x)` << 1, x^52288 mod p(x)` << 1 */
    { 0x000000006a7806ea, 0x000000019e42ea92 },
    /* x^51200 mod p(x)` << 1, x^51264 mod p(x)` << 1 */
    { 0x000000016155aa0c, 0x000000012fa83ff2 },
    /* x^50176 mod p(x)` << 1, x^50240 mod p(x)` << 1 */
    { 0x00000000908650ac, 0x000000011ca9cde0 },
    /* x^49152 mod p(x)` << 1, x^49216 mod p(x)` << 1 */
    { 0x00000000aa5a8084, 0x00000000c8e5cd74 },
    /* x^48128 mod p(x)` << 1, x^48192 mod p(x)` << 1 */
    { 0x0000000191bb500a, 0x0000000096c27f0c },
    /* x^47104 mod p(x)` << 1, x^47168 mod p(x)` << 1 */
    { 0x0000000064e9bed0, 0x000000002baed926 },
    /* x^46080 mod p(x)` << 1, x^46144 mod p(x)` << 1 */
    { 0x000000009444f302, 0x000000017c8de8d2 },
    /* x^45056 mod p(x)` << 1, x^45120 mod p(x)` << 1 */
    { 0x000000019db07d3c, 0x00000000d43d6068 },
    /* x^44032 mod p(x)` << 1, x^44096 mod p(x)` << 1 */
    { 0x00000001359e3e6e, 0x00000000cb2c4b26 },
    /* x^43008 mod p(x)` << 1, x^43072 mod p(x)` << 1 */
    { 0x00000001e4f10dd2, 0x0000000145b8da26 },
    /* x^41984 mod p(x)` << 1, x^42048 mod p(x)` << 1 */
    { 0x0000000124f5735e, 0x000000018fff4b08 },
    /* x^40960 mod p(x)` << 1, x^41024 mod p(x)` << 1 */
    { 0x0000000124760a4c, 0x0000000150b58ed0 },
    /* x^39936 mod p(x)` << 1, x^40000 mod p(x)` << 1 */
    { 0x000000000f1fc186, 0x00000001549f39bc },
    /* x^38912 mod p(x)` << 1, x^38976 mod p(x)` << 1 */
    { 0x00000000150e4cc4, 0x00000000ef4d2f42 },
    /* x^37888 mod p(x)` << 1, x^37952 mod p(x)` << 1 */
    { 0x000000002a6204e8, 0x00000001b1468572 },
    /* x^36864 mod p(x)` << 1, x^36928 mod p(x)` << 1 */
    { 0x00000000beb1d432, 0x000000013d7403b2 },
    /* x^35840 mod p(x)` << 1, x^35904 mod p(x)` << 1 */
    { 0x0000000135f3f1f0, 0x00000001a4681842 },
    /* x^34816 mod p(x)` << 1, x^34880 mod p(x)` << 1 */
    { 0x0000000074fe2232, 0x0000000167714492 },
    /* x^33792 mod p(x)` << 1, x^33856 mod p(x)` << 1 */
    { 0x000000001ac6e2ba, 0x00000001e599099a },
    /* x^32768 mod p(x)` << 1, x^32832 mod p(x)` << 1 */
    { 0x0000000013fca91e, 0x00000000fe128194 },
    /* x^31744 mod p(x)` << 1, x^31808 mod p(x)` << 1 */
    { 0x0000000183f4931e, 0x0000000077e8b990 },
    /* x^30720 mod p(x)` << 1, x^30784 mod p(x)` << 1 */
    { 0x00000000b6d9b4e4, 0x00000001a267f63a },
    /* x^29696 mod p(x)` << 1, x^29760 mod p(x)` << 1 */
    { 0x00000000b5188656, 0x00000001945c245a },
    /* x^28672 mod p(x)` << 1, x^28736 mod p(x)` << 1 */
    { 0x0000000027a81a84, 0x0000000149002e76 },
    /* x^27648 mod p(x)` << 1, x^27712 mod p(x)` << 1 */
    { 0x0000000125699258, 0x00000001bb8310a4 },
    /* x^26624 mod p(x)` << 1, x^26688 mod p(x)` << 1 */
    { 0x00000001b23de796, 0x000000019ec60bcc },
    /* x^25600 mod p(x)` << 1, x^25664 mod p(x)` << 1 */
    { 0x00000000fe4365dc, 0x000000012d8590ae },
    /* x^24576 mod p(x)` << 1, x^24640 mod p(x)` << 1 */
    { 0x00000000c68f497a, 0x0000000065b00684 },
    /* x^23552 mod p(x)` << 1, x^23616 mod p(x)` << 1 */
    { 0x00000000fbf521ee, 0x000000015e5aeadc },
    /* x^22528 mod p(x)` << 1, x^22592 mod p(x)` << 1 */
    { 0x000000015eac3378, 0x00000000b77ff2b0 },
    /* x^21504 mod p(x)` << 1, x^21568 mod p(x)` << 1 */
    { 0x0000000134914b90, 0x0000000188da2ff6 },
    /* x^20480 mod p(x)` << 1, x^20544 mod p(x)` << 1 */
    { 0x0000000016335cfe, 0x0000000063da929a },
    /* x^19456 mod p(x)` << 1, x^19520 mod p(x)` << 1 */
    { 0x000000010372d10c, 0x00000001389caa80 },
    /* x^18432 mod p(x)` << 1, x^18496 mod p(x)` << 1 */
    { 0x000000015097b908, 0x000000013db599d2 },
    /* x^17408 mod p(x)` << 1, x^17472 mod p(x)` << 1 */
    { 0x00000001227a7572, 0x0000000122505a86 },
    /* x^16384 mod p(x)` << 1, x^16448 mod p(x)` << 1 */
    { 0x000000009a8f75c0, 0x000000016bd72746 },
    /* x^15360 mod p(x)` << 1, x^15424 mod p(x)` << 1 */
    { 0x00000000682c77a2, 0x00000001c3faf1d4 },
    /* x^14336 mod p(x)` << 1, x^14400 mod p(x)` << 1 */
    { 0x00000000231f091c, 0x00000001111c826c },
    /* x^13312 mod p(x)` << 1, x^13376 mod p(x)` << 1 */
    { 0x000000007d4439f2, 0x00000000153e9fb2 },
    /* x^12288 mod p(x)` << 1, x^12352 mod p(x)` << 1 */
    { 0x000000017e221efc, 0x000000002b1f7b60 },
    /* x^11264 mod p(x)` << 1, x^11328 mod p(x)` << 1 */
    { 0x0000000167457c38, 0x00000000b1dba570 },
    /* x^10240 mod p(x)` << 1, x^10304 mod p(x)` << 1 */
    { 0x00000000bdf081c4, 0x00000001f6397b76 },
    /* x^9216 mod p(x)` << 1, x^9280 mod p(x)` << 1 */
    { 0x000000016286d6b0, 0x0000000156335214 },
    /* x^8192 mod p(x)` << 1, x^8256 mod p(x)` << 1 */
    { 0x00000000c84f001c, 0x00000001d70e3986 },
    /* x^7168 mod p(x)` << 1, x^7232 mod p(x)` << 1 */
    { 0x0000000064efe7c0, 0x000000003701a774 },
    /* x^6144 mod p(x)` << 1, x^6208 mod p(x)` << 1 */
    { 0x000000000ac2d904, 0x00000000ac81ef72 },
    /* x^5120 mod p(x)` << 1, x^5184 mod p(x)` << 1 */
    { 0x00000000fd226d14, 0x0000000133212464 },
    /* x^4096 mod p(x)` << 1, x^4160 mod p(x)` << 1 */
    { 0x000000011cfd42e0, 0x00000000e4e45610 },
    /* x^3072 mod p(x)` << 1, x^3136 mod p(x)` << 1 */
    { 0x000000016e5a5678, 0x000000000c1bd370 },
    /* x^2048 mod p(x)` << 1, x^2112 mod p(x)` << 1 */
    { 0x00000001d888fe22, 0x00000001a7b9e7a6 },
    /* x^1024 mod p(x)` << 1, x^1088 mod p(x)` << 1 */
    { 0x00000001af77fcd4, 0x000000007d657a10 }
#endif /* BYTE_ORDER == LITTLE_ENDIAN */
};

/* Reduce final 1024-2048 bits to 64 bits, shifting 32 bits to include the trailing 32 bits of zeros */

static const __vector unsigned long long vcrc_short_const[16] ALIGNED_(16) = {
#if BYTE_ORDER == LITTLE_ENDIAN
    /* x^1952 mod p(x) , x^1984 mod p(x) , x^2016 mod p(x) , x^2048 mod p(x)  */
    { 0x99168a18ec447f11, 0xed837b2613e8221e },
    /* x^1824 mod p(x) , x^1856 mod p(x) , x^1888 mod p(x) , x^1920 mod p(x)  */
    { 0xe23e954e8fd2cd3c, 0xc8acdd8147b9ce5a },
    /* x^1696 mod p(x) , x^1728 mod p(x) , x^1760 mod p(x) , x^1792 mod p(x)  */
    { 0x92f8befe6b1d2b53, 0xd9ad6d87d4277e25 },
    /* x^1568 mod p(x) , x^1600 mod p(x) , x^1632 mod p(x) , x^1664 mod p(x)  */
    { 0xf38a3556291ea462, 0xc10ec5e033fbca3b },
    /* x^1440 mod p(x) , x^1472 mod p(x) , x^1504 mod p(x) , x^1536 mod p(x)  */
    { 0x974ac56262b6ca4b, 0xc0b55b0e82e02e2f },
    /* x^1312 mod p(x) , x^1344 mod p(x) , x^1376 mod p(x) , x^1408 mod p(x)  */
    { 0x855712b3784d2a56, 0x71aa1df0e172334d },
    /* x^1184 mod p(x) , x^1216 mod p(x) , x^1248 mod p(x) , x^1280 mod p(x)  */
    { 0xa5abe9f80eaee722, 0xfee3053e3969324d },
    /* x^1056 mod p(x) , x^1088 mod p(x) , x^1120 mod p(x) , x^1152 mod p(x)  */
    { 0x1fa0943ddb54814c, 0xf44779b93eb2bd08 },
    /* x^928 mod p(x) , x^960 mod p(x) , x^992 mod p(x) , x^1024 mod p(x)  */
    { 0xa53ff440d7bbfe6a, 0xf5449b3f00cc3374 },
    /* x^800 mod p(x) , x^832 mod p(x) , x^864 mod p(x) , x^896 mod p(x)  */
    { 0xebe7e3566325605c, 0x6f8346e1d777606e },
    /* x^672 mod p(x) , x^704 mod p(x) , x^736 mod p(x) , x^768 mod p(x)  */
    { 0xc65a272ce5b592b8, 0xe3ab4f2ac0b95347 },
    /* x^544 mod p(x) , x^576 mod p(x) , x^608 mod p(x) , x^640 mod p(x)  */
    { 0x5705a9ca4721589f, 0xaa2215ea329ecc11 },
    /* x^416 mod p(x) , x^448 mod p(x) , x^480 mod p(x) , x^512 mod p(x)  */
    { 0xe3720acb88d14467, 0x1ed8f66ed95efd26 },
    /* x^288 mod p(x) , x^320 mod p(x) , x^352 mod p(x) , x^384 mod p(x)  */
    { 0xba1aca0315141c31, 0x78ed02d5a700e96a },
    /* x^160 mod p(x) , x^192 mod p(x) , x^224 mod p(x) , x^256 mod p(x)  */
    { 0xad2a31b3ed627dae, 0xba8ccbe832b39da3 },
    /* x^32 mod p(x) , x^64 mod p(x) , x^96 mod p(x) , x^128 mod p(x)  */
    { 0x6655004fa06a2517, 0xedb88320b1e6b092 }
#else /* BYTE_ORDER == LITTLE_ENDIAN */
    /* x^1952 mod p(x) , x^1984 mod p(x) , x^2016 mod p(x) , x^2048 mod p(x)  */
    { 0xed837b2613e8221e, 0x99168a18ec447f11 },
    /* x^1824 mod p(x) , x^1856 mod p(x) , x^1888 mod p(x) , x^1920 mod p(x)  */
    { 0xc8acdd8147b9ce5a, 0xe23e954e8fd2cd3c },
    /* x^1696 mod p(x) , x^1728 mod p(x) , x^1760 mod p(x) , x^1792 mod p(x)  */
    { 0xd9ad6d87d4277e25, 0x92f8befe6b1d2b53 },
    /* x^1568 mod p(x) , x^1600 mod p(x) , x^1632 mod p(x) , x^1664 mod p(x)  */
    { 0xc10ec5e033fbca3b, 0xf38a3556291ea462 },
    /* x^1440 mod p(x) , x^1472 mod p(x) , x^1504 mod p(x) , x^1536 mod p(x)  */
    { 0xc0b55b0e82e02e2f, 0x974ac56262b6ca4b },
    /* x^1312 mod p(x) , x^1344 mod p(x) , x^1376 mod p(x) , x^1408 mod p(x)  */
    { 0x71aa1df0e172334d, 0x855712b3784d2a56 },
    /* x^1184 mod p(x) , x^1216 mod p(x) , x^1248 mod p(x) , x^1280 mod p(x)  */
    { 0xfee3053e3969324d, 0xa5abe9f80eaee722 },
    /* x^1056 mod p(x) , x^1088 mod p(x) , x^1120 mod p(x) , x^1152 mod p(x)  */
    { 0xf44779b93eb2bd08, 0x1fa0943ddb54814c },
    /* x^928 mod p(x) , x^960 mod p(x) , x^992 mod p(x) , x^1024 mod p(x)  */
    { 0xf5449b3f00cc3374, 0xa53ff440d7bbfe6a },
    /* x^800 mod p(x) , x^832 mod p(x) , x^864 mod p(x) , x^896 mod p(x)  */
    { 0x6f8346e1d777606e, 0xebe7e3566325605c },
    /* x^672 mod p(x) , x^704 mod p(x) , x^736 mod p(x) , x^768 mod p(x)  */
    { 0xe3ab4f2ac0b95347, 0xc65a272ce5b592b8 },
    /* x^544 mod p(x) , x^576 mod p(x) , x^608 mod p(x) , x^640 mod p(x)  */
    { 0xaa2215ea329ecc11, 0x5705a9ca4721589f },
    /* x^416 mod p(x) , x^448 mod p(x) , x^480 mod p(x) , x^512 mod p(x)  */
    { 0x1ed8f66ed95efd26, 0xe3720acb88d14467 },
    /* x^288 mod p(x) , x^320 mod p(x) , x^352 mod p(x) , x^384 mod p(x)  */
    { 0x78ed02d5a700e96a, 0xba1aca0315141c31 },
    /* x^160 mod p(x) , x^192 mod p(x) , x^224 mod p(x) , x^256 mod p(x)  */
    { 0xba8ccbe832b39da3, 0xad2a31b3ed627dae },
    /* x^32 mod p(x) , x^64 mod p(x) , x^96 mod p(x) , x^128 mod p(x)  */
    { 0xedb88320b1e6b092, 0x6655004fa06a2517 }
#endif /* BYTE_ORDER == LITTLE_ENDIAN */
};

/* Barrett constants */
/* 33 bit reflected Barrett constant m - (4^32)/n */

static const __vector unsigned long long v_Barrett_const[2] ALIGNED_(16) = {
    /* x^64 div p(x)  */
#if BYTE_ORDER == LITTLE_ENDIAN
    { 0x00000001f7011641, 0x0000000000000000 },
    { 0x00000001db710641, 0x0000000000000000 }
#else /* BYTE_ORDER == LITTLE_ENDIAN */
    { 0x0000000000000000, 0x00000001f7011641 },
    { 0x0000000000000000, 0x00000001db710641 }
#endif /* BYTE_ORDER == LITTLE_ENDIAN */
};
