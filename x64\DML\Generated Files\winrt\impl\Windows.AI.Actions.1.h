// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_AI_Actions_1_H
#define WINRT_Windows_AI_Actions_1_H
#include "winrt/impl/Windows.AI.Actions.0.h"
WINRT_EXPORT namespace winrt::Windows::AI::Actions
{
    struct WINRT_IMPL_EMPTY_BASES IActionEntity :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActionEntity>
    {
        IActionEntity(std::nullptr_t = nullptr) noexcept {}
        IActionEntity(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActionEntity2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActionEntity2>
    {
        IActionEntity2(std::nullptr_t = nullptr) noexcept {}
        IActionEntity2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActionEntityDisplayInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActionEntityDisplayInfo>
    {
        IActionEntityDisplayInfo(std::nullptr_t = nullptr) noexcept {}
        IActionEntityDisplayInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActionEntityFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActionEntityFactory>
    {
        IActionEntityFactory(std::nullptr_t = nullptr) noexcept {}
        IActionEntityFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActionEntityFactory2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActionEntityFactory2>
    {
        IActionEntityFactory2(std::nullptr_t = nullptr) noexcept {}
        IActionEntityFactory2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActionEntityFactory3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActionEntityFactory3>
    {
        IActionEntityFactory3(std::nullptr_t = nullptr) noexcept {}
        IActionEntityFactory3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActionEntityFactory4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActionEntityFactory4>
    {
        IActionEntityFactory4(std::nullptr_t = nullptr) noexcept {}
        IActionEntityFactory4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActionEntityFactoryFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActionEntityFactoryFactory>
    {
        IActionEntityFactoryFactory(std::nullptr_t = nullptr) noexcept {}
        IActionEntityFactoryFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActionFeedback :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActionFeedback>
    {
        IActionFeedback(std::nullptr_t = nullptr) noexcept {}
        IActionFeedback(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActionInvocationContext :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActionInvocationContext>
    {
        IActionInvocationContext(std::nullptr_t = nullptr) noexcept {}
        IActionInvocationContext(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActionInvocationContext2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActionInvocationContext2>
    {
        IActionInvocationContext2(std::nullptr_t = nullptr) noexcept {}
        IActionInvocationContext2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActionInvocationHelpDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActionInvocationHelpDetails>
    {
        IActionInvocationHelpDetails(std::nullptr_t = nullptr) noexcept {}
        IActionInvocationHelpDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActionRuntime :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActionRuntime>
    {
        IActionRuntime(std::nullptr_t = nullptr) noexcept {}
        IActionRuntime(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActionRuntime2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActionRuntime2>
    {
        IActionRuntime2(std::nullptr_t = nullptr) noexcept {}
        IActionRuntime2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActionRuntime3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActionRuntime3>
    {
        IActionRuntime3(std::nullptr_t = nullptr) noexcept {}
        IActionRuntime3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActionRuntimeFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActionRuntimeFactory>
    {
        IActionRuntimeFactory(std::nullptr_t = nullptr) noexcept {}
        IActionRuntimeFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IContactActionEntity :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContactActionEntity>
    {
        IContactActionEntity(std::nullptr_t = nullptr) noexcept {}
        IContactActionEntity(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDocumentActionEntity :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDocumentActionEntity>
    {
        IDocumentActionEntity(std::nullptr_t = nullptr) noexcept {}
        IDocumentActionEntity(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFileActionEntity :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileActionEntity>
    {
        IFileActionEntity(std::nullptr_t = nullptr) noexcept {}
        IFileActionEntity(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INamedActionEntity :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INamedActionEntity>
    {
        INamedActionEntity(std::nullptr_t = nullptr) noexcept {}
        INamedActionEntity(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoActionEntity :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoActionEntity>
    {
        IPhotoActionEntity(std::nullptr_t = nullptr) noexcept {}
        IPhotoActionEntity(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRemoteFileActionEntity :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRemoteFileActionEntity>
    {
        IRemoteFileActionEntity(std::nullptr_t = nullptr) noexcept {}
        IRemoteFileActionEntity(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStreamingTextActionEntity :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStreamingTextActionEntity>
    {
        IStreamingTextActionEntity(std::nullptr_t = nullptr) noexcept {}
        IStreamingTextActionEntity(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStreamingTextActionEntityTextChangedArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStreamingTextActionEntityTextChangedArgs>
    {
        IStreamingTextActionEntityTextChangedArgs(std::nullptr_t = nullptr) noexcept {}
        IStreamingTextActionEntityTextChangedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStreamingTextActionEntityWriter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStreamingTextActionEntityWriter>
    {
        IStreamingTextActionEntityWriter(std::nullptr_t = nullptr) noexcept {}
        IStreamingTextActionEntityWriter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITableActionEntity :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITableActionEntity>
    {
        ITableActionEntity(std::nullptr_t = nullptr) noexcept {}
        ITableActionEntity(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITextActionEntity :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextActionEntity>
    {
        ITextActionEntity(std::nullptr_t = nullptr) noexcept {}
        ITextActionEntity(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITextActionEntity2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextActionEntity2>
    {
        ITextActionEntity2(std::nullptr_t = nullptr) noexcept {}
        ITextActionEntity2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
