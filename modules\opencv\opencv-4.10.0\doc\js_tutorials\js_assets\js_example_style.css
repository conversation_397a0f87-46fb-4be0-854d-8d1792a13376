body, div, p {
    font: 400 14px/22px Roboto,sans-serif;
}
canvas, img, video {
    border: 1px solid black;
}
td {
    padding: 10px 0px 0px 10px;
    text-align: center;
}
button {
    display: inline-block;
    color: #fff;
    background-color: #337ab7;
    border-color: #2e6da4;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px;
}
button[disabled] {
    cursor: not-allowed;
    filter: alpha(opacity=65);
    -webkit-box-shadow: none;
    box-shadow: none;
    opacity: .65;
}
.control {
    margin-bottom: 3px;
}
.err {
    color: red;
    font-weight: bold;
}
.caption {
    margin: 0;
    font-weight: bold;
}
.code {
    padding: 4px 6px;
    margin: 4px 8px 4px 2px;
    background-color: #FBFCFD;
    border: 1px solid #C4CFE5;
    font-family: monospace, fixed;
    font-size: 13px;
    min-height: 13px;
    line-height: 1.0;
    text-wrap: unrestricted;
    padding-bottom: 0px;
    margin: 0px;
}
.hidden {
    display: none;
}
.small {
    max-width: 300px;
}
