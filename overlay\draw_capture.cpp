#define WIN32_LEAN_AND_MEAN
#define _WINSOCKAPI_
#include <winsock2.h>
#include <Windows.h>

#include <string.h>
#include <algorithm>

#include <imgui/imgui.h>
#include "imgui/imgui_internal.h"

#include "config.h"
#include "sunone_aimbot_cpp.h"
#include "capture.h"
#include "other_tools.h"
#include "virtual_camera.h"
#include "draw_settings.h"
#include "overlay.h"

bool disable_winrt_futures = checkwin1903();
int monitors = get_active_monitors();

static std::vector<std::string> virtual_cameras;
static char virtual_camera_filter_buf[128] = "";

void ensureVirtualCamerasLoaded()
{
    if (virtual_cameras.empty())
    {
        virtual_cameras = VirtualCameraCapture::GetAvailableVirtualCameras();
    }
}

void draw_capture_settings()
{
    // Main title with tech styling
    ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.2f, 0.8f, 1.0f, 1.0f));
    ImGui::Text(u8"截屏");
    ImGui::PopStyleColor();
    ImGui::Separator();
    ImGui::Spacing();

    float content_width = ImGui::GetContentRegionAvail().x;
    float item_width = content_width * 0.3f;  // 从0.6f减少到0.4f
    float slider_width = content_width * 0.4f;

    // Detection Resolution Card
    ImGui::PushStyleColor(ImGuiCol_ChildBg, ImVec4(0.08f, 0.12f, 0.18f, 0.95f));
    ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.2f, 0.6f, 1.0f, 0.8f));
    ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 6.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 1.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(8, 6));
    
    if (ImGui::BeginChild("DetectionCard", ImVec2(0, 85), true))
    {
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.3f, 0.9f, 1.0f, 1.0f));
        ImGui::Text("SIZE");
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();

        static const int allowed_resolutions[] = { 160, 320, 640 };
        static int current_resolution_idx = 1;

        for (int i = 0; i < 3; ++i)
            if (config.detection_resolution == allowed_resolutions[i])
                current_resolution_idx = i;

        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.12f, 0.16f, 0.24f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.16f, 0.20f, 0.30f, 1.0f));

        ImGui::SetNextItemWidth(item_width);
        if (ImGui::Combo("IMGE", &current_resolution_idx, "160\0""320\0""640\0"))
        {
            config.detection_resolution = allowed_resolutions[current_resolution_idx];
            detection_resolution_changed.store(true);
            detector_model_changed.store(true);

            globalMouseThread->updateConfig(
                config.detection_resolution,
                config.fovX,
                config.fovY,
                config.minSpeedMultiplier,
                config.maxSpeedMultiplier,
                config.predictionInterval,
                config.auto_shoot,
                config.bScope_multiplier);
            config.saveConfig();
        }

        ImGui::SameLine();
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "AA");

        ImGui::PopStyleColor(2);
    }
    ImGui::EndChild();
    ImGui::PopStyleVar(3);
    ImGui::PopStyleColor(2);
    
    ImGui::Spacing();

    // Performance Settings Card
    ImGui::PushStyleColor(ImGuiCol_ChildBg, ImVec4(0.12f, 0.08f, 0.18f, 0.95f));
    ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.8f, 0.4f, 1.0f, 0.8f));
    ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 6.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 1.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(8, 6));
    
    if (ImGui::BeginChild("PerformanceCard", ImVec2(0, 100), true))
    {
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.9f, 0.5f, 1.0f, 1.0f));
        ImGui::Text("FPS");
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();

        ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(0.6f, 0.3f, 0.9f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(0.8f, 0.5f, 1.0f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.12f, 0.16f, 0.24f, 1.0f));

        ImGui::SetNextItemWidth(slider_width);
        if (ImGui::SliderInt("FPS", &config.capture_fps, 0, 240))
        {
            capture_fps_changed.store(true);
            config.saveConfig();
        }

        if (config.capture_fps == 0)
        {
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(1.0f, 0.3f, 0.3f, 1.0f), "DISABLED");
        }

        ImGui::Spacing();
        
        ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(0.6f, 0.3f, 0.9f, 1.0f));
        if (ImGui::Checkbox("Third-person", &config.circle_mask))
        {
            capture_method_changed.store(true);
            config.saveConfig();
        }
        ImGui::PopStyleColor();

        ImGui::PopStyleColor(3);
    }
    ImGui::EndChild();
    ImGui::PopStyleVar(3);
    ImGui::PopStyleColor(2);
    
    ImGui::Spacing();

    // Capture Method Card
    ImGui::PushStyleColor(ImGuiCol_ChildBg, ImVec4(0.08f, 0.18f, 0.12f, 0.95f));
    ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.2f, 1.0f, 0.6f, 0.8f));
    ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 6.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 1.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(8, 6));
    
    if (ImGui::BeginChild("CaptureMethodCard", ImVec2(0, 110), true))
    {
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.3f, 1.0f, 0.7f, 1.0f));
        ImGui::Text("CAPTURE");
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();

        std::vector<std::string> captureMethodOptions = { "duplication_api", "winrt", "virtual_camera" };
        std::vector<const char*> captureMethodItems;

        for (const auto& option : captureMethodOptions)
        {
            captureMethodItems.push_back(option.c_str());
        }

        int currentcaptureMethodIndex = 0;
        for (size_t i = 0; i < captureMethodOptions.size(); ++i)
        {
            if (captureMethodOptions[i] == config.capture_method)
            {
                currentcaptureMethodIndex = static_cast<int>(i);
                break;
            }
        }

        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.12f, 0.24f, 0.16f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.16f, 0.30f, 0.20f, 1.0f));

        ImGui::SetNextItemWidth(item_width);
        if (ImGui::Combo("Method", &currentcaptureMethodIndex, captureMethodItems.data(), static_cast<int>(captureMethodItems.size()))) {
            config.capture_method = captureMethodOptions[currentcaptureMethodIndex];
            config.saveConfig();
            capture_method_changed.store(true);
        }

        // Method descriptions
        ImGui::Spacing();
        if (config.capture_method == "duplication_api")
            ImGui::TextColored(ImVec4(0.7f, 0.9f, 0.7f, 1.0f), "Fast ");
        else if (config.capture_method == "winrt")
            ImGui::TextColored(ImVec4(0.7f, 0.9f, 0.7f, 1.0f), "Windows capture");
        else if (config.capture_method == "virtual_camera")
            ImGui::TextColored(ImVec4(0.7f, 0.9f, 0.7f, 1.0f), "Virtual camera ");

        ImGui::PopStyleColor(2);
    }
    ImGui::EndChild();
    ImGui::PopStyleVar(3);
    ImGui::PopStyleColor(2);
    
    ImGui::Spacing();

    // WinRT Advanced Options
    if (config.capture_method == "winrt")
    {
        ImGui::PushStyleColor(ImGuiCol_ChildBg, ImVec4(0.18f, 0.12f, 0.08f, 0.95f));
        ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(1.0f, 0.6f, 0.2f, 0.8f));
        ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 6.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 1.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(8, 6));
        
        if (ImGui::BeginChild("WinRTOptionsCard", ImVec2(0, 85), true))
        {
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.7f, 0.3f, 1.0f));
            ImGui::Text("WINRT OPTIONS");
            ImGui::PopStyleColor();
            ImGui::Separator();
            ImGui::Spacing();

            if (disable_winrt_futures)
            {
                ImGui::PushItemFlag(ImGuiItemFlags_Disabled, true);
                ImGui::PushStyleVar(ImGuiStyleVar_Alpha, ImGui::GetStyle().Alpha * 0.5f);
            }

            ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(1.0f, 0.6f, 0.2f, 1.0f));
            
            if (ImGui::Checkbox("Capture Borders", &config.capture_borders))
            {
                capture_borders_changed.store(true);
                config.saveConfig();
            }

            ImGui::SameLine();
            if (ImGui::Checkbox("Capture", &config.capture_cursor))
            {
                capture_cursor_changed.store(true);
                config.saveConfig();
            }

            ImGui::PopStyleColor();

            if (disable_winrt_futures)
            {
                ImGui::PopStyleVar();
                ImGui::PopItemFlag();
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Windows 10 1903+");
            }
        }
        ImGui::EndChild();
        ImGui::PopStyleVar(3);
        ImGui::PopStyleColor(2);
        ImGui::Spacing();
    }

    // Monitor Selection
    if (config.capture_method == "duplication_api" || config.capture_method == "winrt")
    {
        ImGui::PushStyleColor(ImGuiCol_ChildBg, ImVec4(0.15f, 0.08f, 0.15f, 0.95f));
        ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.9f, 0.3f, 0.9f, 0.8f));
        ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 6.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 1.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(8, 6));
        
        if (ImGui::BeginChild("MonitorCard", ImVec2(0, 75), true))
        {
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.9f, 0.5f, 0.9f, 1.0f));
            ImGui::Text("DISPLAY ");
            ImGui::PopStyleColor();
            ImGui::Separator();
            ImGui::Spacing();

            std::vector<std::string> monitorNames;
            if (monitors == -1)
            {
                monitorNames.push_back("Primary Display");
            }
            else
            {
                for (int i = -1; i < monitors; ++i)
                {
                    monitorNames.push_back("Display " + std::to_string(i + 1));
                }
            }

            std::vector<const char*> monitorItems;
            for (const auto& name : monitorNames)
            {
                monitorItems.push_back(name.c_str());
            }

            ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.20f, 0.12f, 0.20f, 1.0f));
            ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.25f, 0.16f, 0.25f, 1.0f));

            ImGui::SetNextItemWidth(item_width);
            if (ImGui::Combo("Target Display", &config.monitor_idx, monitorItems.data(), static_cast<int>(monitorItems.size())))
            {
                config.saveConfig();
                capture_method_changed.store(true);
            }

            ImGui::PopStyleColor(2);
        }
        ImGui::EndChild();
        ImGui::PopStyleVar(3);
        ImGui::PopStyleColor(2);
        ImGui::Spacing();
    }

    // Virtual Camera Settings
    if (config.capture_method == "virtual_camera")
    {
        ImGui::PushStyleColor(ImGuiCol_ChildBg, ImVec4(0.08f, 0.15f, 0.18f, 0.95f));
        ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.2f, 0.8f, 1.0f, 0.8f));
        ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 6.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 1.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(8, 6));
        
        if (ImGui::BeginChild("VirtualCameraCard", ImVec2(0, 200), true))
        {
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.3f, 0.8f, 1.0f, 1.0f));
            ImGui::Text("VIRTUAL CAMERA");
            ImGui::PopStyleColor();
            ImGui::Separator();
            ImGui::Spacing();

            ensureVirtualCamerasLoaded();

            // Filter
            ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.12f, 0.20f, 0.24f, 1.0f));
            ImGui::SetNextItemWidth(item_width);
            ImGui::InputText("Filter", virtual_camera_filter_buf, IM_ARRAYSIZE(virtual_camera_filter_buf));
            ImGui::PopStyleColor();

            std::string filter_lower = virtual_camera_filter_buf;
            std::transform(filter_lower.begin(), filter_lower.end(), filter_lower.begin(), ::tolower);

            // Filter list
            std::vector<int> filtered_indices;
            for (int i = 0; i < static_cast<int>(virtual_cameras.size()); ++i)
            {
                std::string name_lower = virtual_cameras[i];
                std::transform(name_lower.begin(), name_lower.end(), name_lower.begin(), ::tolower);
                if (filter_lower.empty() || name_lower.find(filter_lower) != std::string::npos)
                {
                    filtered_indices.push_back(i);
                }
            }

            if (!filtered_indices.empty())
            {
                int currentIndex = 0;
                for (int fi = 0; fi < static_cast<int>(filtered_indices.size()); ++fi)
                {
                    if (virtual_cameras[filtered_indices[fi]] == config.virtual_camera_name)
                    {
                        currentIndex = fi;
                        break;
                    }
                }

                std::vector<const char*> items;
                items.reserve(filtered_indices.size());
                for (int idx : filtered_indices)
                {
                    items.push_back(virtual_cameras[idx].c_str());
                }

                ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.12f, 0.20f, 0.24f, 1.0f));
                ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.16f, 0.25f, 0.30f, 1.0f));

                ImGui::SetNextItemWidth(item_width);
                if (ImGui::Combo("Camera Device", &currentIndex, items.data(), static_cast<int>(items.size())))
                {
                    config.virtual_camera_name = virtual_cameras[filtered_indices[currentIndex]];
                    config.saveConfig();
                    capture_method_changed.store(true);
                }

                ImGui::PopStyleColor(2);
            }
            else
            {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "No matching cameras found");
            }

            ImGui::SameLine();
            ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.6f, 0.8f, 1.0f));
            ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.3f, 0.7f, 0.9f, 1.0f));
            if (ImGui::Button("Refresh"))
            {
                VirtualCameraCapture::ClearCachedCameraList();
                virtual_cameras = VirtualCameraCapture::GetAvailableVirtualCameras(true);
                virtual_camera_filter_buf[0] = '\0';
            }
            ImGui::PopStyleColor(2);

            ImGui::Spacing();
            ImGui::Separator();
            ImGui::Spacing();

            // Resolution Settings
            ImGui::Text("Resolution:");
            ImGui::Spacing();

            ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(0.2f, 0.6f, 0.8f, 1.0f));
            ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(0.3f, 0.7f, 0.9f, 1.0f));
            ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.12f, 0.20f, 0.24f, 1.0f));

            ImGui::SetNextItemWidth(slider_width);
            if (ImGui::SliderInt("Width", &config.virtual_camera_width, 128, 3840))
            {
                config.saveConfig();
                capture_method_changed.store(true);
            }

            ImGui::SetNextItemWidth(slider_width);
            if (ImGui::SliderInt("Height", &config.virtual_camera_heigth, 128, 2160))
            {
                config.saveConfig();
                capture_method_changed.store(true);
            }

            ImGui::PopStyleColor(3);
        }
        ImGui::EndChild();
        ImGui::PopStyleVar(3);
        ImGui::PopStyleColor(2);
    }
}


