﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Assets">
      <UniqueIdentifier>8beffbbf-df41-4539-86bc-a84722831035</UniqueIdentifier>
      <Extensions>bmp;fbx;gif;jpg;jpeg;tga;tiff;tif;png</Extensions>
    </Filter>
    <Image Include="Assets\Logo.scale-240.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SmallLogo.scale-240.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SplashScreen.scale-240.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square71x71Logo.scale-240.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\StoreLogo.scale-240.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\WideLogo.scale-240.png">
      <Filter>Assets</Filter>
    </Image>
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="App.xaml.cpp" />
    <ClCompile Include="MainPage.xaml.cpp" />
    <ClCompile Include="pch.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="pch.h" />
    <ClInclude Include="App.xaml.h" />
    <ClInclude Include="MainPage.xaml.h" />
  </ItemGroup>
  <ItemGroup>
    <AppxManifest Include="Package.appxmanifest" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="MainPage.xaml" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="Lena.png">
      <Filter>Assets</Filter>
    </Image>
  </ItemGroup>
</Project>