﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{CADC7BEE-E51E-3A9E-8E05-C698B5F6BF26}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>jpeg12-static</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">jpeg12-static.dir\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">jpeg12-static.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">jpeg12-static</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">jpeg12-static.dir\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">jpeg12-static.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">jpeg12-static</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/AI/opencv/cudabuild" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4996;4018</DisableSpecificWarnings>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;BITS_IN_JSAMPLE=12;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;NO_GETENV;NO_PUTENV;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;BITS_IN_JSAMPLE=12;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;NO_GETENV;NO_PUTENV;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4221</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/AI/opencv/cudabuild" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4996;4018</DisableSpecificWarnings>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <FloatingPointModel>Fast</FloatingPointModel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;NDEBUG;BITS_IN_JSAMPLE=12;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;NO_GETENV;NO_PUTENV;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;NDEBUG;BITS_IN_JSAMPLE=12;_VARIADIC_MAX=10;_WIN32_WINNT=0x0601;NO_GETENV;NO_PUTENV;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo;D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4221</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcapistd.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jccolor.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcdiffct.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jclossls.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcmainct.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcprepct.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcsample.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdapistd.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdcolor.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jddiffct.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdlossls.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdmainct.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdpostct.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdsample.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jutils.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jccoefct.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jcdctmgr.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdcoefct.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jddctmgr.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jdmerge.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jfdctfst.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jfdctint.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jidctflt.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jidctfst.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jidctint.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jidctred.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jquant1.c" />
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\libjpeg-turbo\src\jquant2.c" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>