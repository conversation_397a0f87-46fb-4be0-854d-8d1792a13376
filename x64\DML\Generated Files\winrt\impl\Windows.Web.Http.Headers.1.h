// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Web_Http_Headers_1_H
#define WINRT_Windows_Web_Http_Headers_1_H
#include "winrt/impl/Windows.Web.Http.Headers.0.h"
WINRT_EXPORT namespace winrt::Windows::Web::Http::Headers
{
    struct WINRT_IMPL_EMPTY_BASES IHttpCacheDirectiveHeaderValueCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpCacheDirectiveHeaderValueCollection>
    {
        IHttpCacheDirectiveHeaderValueCollection(std::nullptr_t = nullptr) noexcept {}
        IHttpCacheDirectiveHeaderValueCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpChallengeHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpChallengeHeaderValue>
    {
        IHttpChallengeHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpChallengeHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpChallengeHeaderValueCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpChallengeHeaderValueCollection>
    {
        IHttpChallengeHeaderValueCollection(std::nullptr_t = nullptr) noexcept {}
        IHttpChallengeHeaderValueCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpChallengeHeaderValueFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpChallengeHeaderValueFactory>
    {
        IHttpChallengeHeaderValueFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpChallengeHeaderValueFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpChallengeHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpChallengeHeaderValueStatics>
    {
        IHttpChallengeHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpChallengeHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpConnectionOptionHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpConnectionOptionHeaderValue>
    {
        IHttpConnectionOptionHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpConnectionOptionHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpConnectionOptionHeaderValueCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpConnectionOptionHeaderValueCollection>
    {
        IHttpConnectionOptionHeaderValueCollection(std::nullptr_t = nullptr) noexcept {}
        IHttpConnectionOptionHeaderValueCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpConnectionOptionHeaderValueFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpConnectionOptionHeaderValueFactory>
    {
        IHttpConnectionOptionHeaderValueFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpConnectionOptionHeaderValueFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpConnectionOptionHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpConnectionOptionHeaderValueStatics>
    {
        IHttpConnectionOptionHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpConnectionOptionHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpContentCodingHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpContentCodingHeaderValue>
    {
        IHttpContentCodingHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpContentCodingHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpContentCodingHeaderValueCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpContentCodingHeaderValueCollection>
    {
        IHttpContentCodingHeaderValueCollection(std::nullptr_t = nullptr) noexcept {}
        IHttpContentCodingHeaderValueCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpContentCodingHeaderValueFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpContentCodingHeaderValueFactory>
    {
        IHttpContentCodingHeaderValueFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpContentCodingHeaderValueFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpContentCodingHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpContentCodingHeaderValueStatics>
    {
        IHttpContentCodingHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpContentCodingHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpContentCodingWithQualityHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpContentCodingWithQualityHeaderValue>
    {
        IHttpContentCodingWithQualityHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpContentCodingWithQualityHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpContentCodingWithQualityHeaderValueCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpContentCodingWithQualityHeaderValueCollection>
    {
        IHttpContentCodingWithQualityHeaderValueCollection(std::nullptr_t = nullptr) noexcept {}
        IHttpContentCodingWithQualityHeaderValueCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpContentCodingWithQualityHeaderValueFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpContentCodingWithQualityHeaderValueFactory>
    {
        IHttpContentCodingWithQualityHeaderValueFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpContentCodingWithQualityHeaderValueFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpContentCodingWithQualityHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpContentCodingWithQualityHeaderValueStatics>
    {
        IHttpContentCodingWithQualityHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpContentCodingWithQualityHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpContentDispositionHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpContentDispositionHeaderValue>
    {
        IHttpContentDispositionHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpContentDispositionHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpContentDispositionHeaderValueFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpContentDispositionHeaderValueFactory>
    {
        IHttpContentDispositionHeaderValueFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpContentDispositionHeaderValueFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpContentDispositionHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpContentDispositionHeaderValueStatics>
    {
        IHttpContentDispositionHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpContentDispositionHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpContentHeaderCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpContentHeaderCollection>
    {
        IHttpContentHeaderCollection(std::nullptr_t = nullptr) noexcept {}
        IHttpContentHeaderCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpContentRangeHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpContentRangeHeaderValue>
    {
        IHttpContentRangeHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpContentRangeHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpContentRangeHeaderValueFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpContentRangeHeaderValueFactory>
    {
        IHttpContentRangeHeaderValueFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpContentRangeHeaderValueFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpContentRangeHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpContentRangeHeaderValueStatics>
    {
        IHttpContentRangeHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpContentRangeHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpCookiePairHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpCookiePairHeaderValue>
    {
        IHttpCookiePairHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpCookiePairHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpCookiePairHeaderValueCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpCookiePairHeaderValueCollection>
    {
        IHttpCookiePairHeaderValueCollection(std::nullptr_t = nullptr) noexcept {}
        IHttpCookiePairHeaderValueCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpCookiePairHeaderValueFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpCookiePairHeaderValueFactory>
    {
        IHttpCookiePairHeaderValueFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpCookiePairHeaderValueFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpCookiePairHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpCookiePairHeaderValueStatics>
    {
        IHttpCookiePairHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpCookiePairHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpCredentialsHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpCredentialsHeaderValue>
    {
        IHttpCredentialsHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpCredentialsHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpCredentialsHeaderValueFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpCredentialsHeaderValueFactory>
    {
        IHttpCredentialsHeaderValueFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpCredentialsHeaderValueFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpCredentialsHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpCredentialsHeaderValueStatics>
    {
        IHttpCredentialsHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpCredentialsHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpDateOrDeltaHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpDateOrDeltaHeaderValue>
    {
        IHttpDateOrDeltaHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpDateOrDeltaHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpDateOrDeltaHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpDateOrDeltaHeaderValueStatics>
    {
        IHttpDateOrDeltaHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpDateOrDeltaHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpExpectationHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpExpectationHeaderValue>
    {
        IHttpExpectationHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpExpectationHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpExpectationHeaderValueCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpExpectationHeaderValueCollection>
    {
        IHttpExpectationHeaderValueCollection(std::nullptr_t = nullptr) noexcept {}
        IHttpExpectationHeaderValueCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpExpectationHeaderValueFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpExpectationHeaderValueFactory>
    {
        IHttpExpectationHeaderValueFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpExpectationHeaderValueFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpExpectationHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpExpectationHeaderValueStatics>
    {
        IHttpExpectationHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpExpectationHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpLanguageHeaderValueCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpLanguageHeaderValueCollection>
    {
        IHttpLanguageHeaderValueCollection(std::nullptr_t = nullptr) noexcept {}
        IHttpLanguageHeaderValueCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpLanguageRangeWithQualityHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpLanguageRangeWithQualityHeaderValue>
    {
        IHttpLanguageRangeWithQualityHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpLanguageRangeWithQualityHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpLanguageRangeWithQualityHeaderValueCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpLanguageRangeWithQualityHeaderValueCollection>
    {
        IHttpLanguageRangeWithQualityHeaderValueCollection(std::nullptr_t = nullptr) noexcept {}
        IHttpLanguageRangeWithQualityHeaderValueCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpLanguageRangeWithQualityHeaderValueFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpLanguageRangeWithQualityHeaderValueFactory>
    {
        IHttpLanguageRangeWithQualityHeaderValueFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpLanguageRangeWithQualityHeaderValueFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpLanguageRangeWithQualityHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpLanguageRangeWithQualityHeaderValueStatics>
    {
        IHttpLanguageRangeWithQualityHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpLanguageRangeWithQualityHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpMediaTypeHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMediaTypeHeaderValue>
    {
        IHttpMediaTypeHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpMediaTypeHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpMediaTypeHeaderValueFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMediaTypeHeaderValueFactory>
    {
        IHttpMediaTypeHeaderValueFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpMediaTypeHeaderValueFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpMediaTypeHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMediaTypeHeaderValueStatics>
    {
        IHttpMediaTypeHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpMediaTypeHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpMediaTypeWithQualityHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMediaTypeWithQualityHeaderValue>
    {
        IHttpMediaTypeWithQualityHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpMediaTypeWithQualityHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpMediaTypeWithQualityHeaderValueCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMediaTypeWithQualityHeaderValueCollection>
    {
        IHttpMediaTypeWithQualityHeaderValueCollection(std::nullptr_t = nullptr) noexcept {}
        IHttpMediaTypeWithQualityHeaderValueCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpMediaTypeWithQualityHeaderValueFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMediaTypeWithQualityHeaderValueFactory>
    {
        IHttpMediaTypeWithQualityHeaderValueFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpMediaTypeWithQualityHeaderValueFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpMediaTypeWithQualityHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMediaTypeWithQualityHeaderValueStatics>
    {
        IHttpMediaTypeWithQualityHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpMediaTypeWithQualityHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpMethodHeaderValueCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMethodHeaderValueCollection>
    {
        IHttpMethodHeaderValueCollection(std::nullptr_t = nullptr) noexcept {}
        IHttpMethodHeaderValueCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpNameValueHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpNameValueHeaderValue>
    {
        IHttpNameValueHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpNameValueHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpNameValueHeaderValueFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpNameValueHeaderValueFactory>
    {
        IHttpNameValueHeaderValueFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpNameValueHeaderValueFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpNameValueHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpNameValueHeaderValueStatics>
    {
        IHttpNameValueHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpNameValueHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpProductHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpProductHeaderValue>
    {
        IHttpProductHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpProductHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpProductHeaderValueFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpProductHeaderValueFactory>
    {
        IHttpProductHeaderValueFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpProductHeaderValueFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpProductHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpProductHeaderValueStatics>
    {
        IHttpProductHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpProductHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpProductInfoHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpProductInfoHeaderValue>
    {
        IHttpProductInfoHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpProductInfoHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpProductInfoHeaderValueCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpProductInfoHeaderValueCollection>
    {
        IHttpProductInfoHeaderValueCollection(std::nullptr_t = nullptr) noexcept {}
        IHttpProductInfoHeaderValueCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpProductInfoHeaderValueFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpProductInfoHeaderValueFactory>
    {
        IHttpProductInfoHeaderValueFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpProductInfoHeaderValueFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpProductInfoHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpProductInfoHeaderValueStatics>
    {
        IHttpProductInfoHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpProductInfoHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpRequestHeaderCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpRequestHeaderCollection>
    {
        IHttpRequestHeaderCollection(std::nullptr_t = nullptr) noexcept {}
        IHttpRequestHeaderCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpResponseHeaderCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpResponseHeaderCollection>
    {
        IHttpResponseHeaderCollection(std::nullptr_t = nullptr) noexcept {}
        IHttpResponseHeaderCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpTransferCodingHeaderValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpTransferCodingHeaderValue>
    {
        IHttpTransferCodingHeaderValue(std::nullptr_t = nullptr) noexcept {}
        IHttpTransferCodingHeaderValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpTransferCodingHeaderValueCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpTransferCodingHeaderValueCollection>
    {
        IHttpTransferCodingHeaderValueCollection(std::nullptr_t = nullptr) noexcept {}
        IHttpTransferCodingHeaderValueCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpTransferCodingHeaderValueFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpTransferCodingHeaderValueFactory>
    {
        IHttpTransferCodingHeaderValueFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpTransferCodingHeaderValueFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpTransferCodingHeaderValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpTransferCodingHeaderValueStatics>
    {
        IHttpTransferCodingHeaderValueStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpTransferCodingHeaderValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
