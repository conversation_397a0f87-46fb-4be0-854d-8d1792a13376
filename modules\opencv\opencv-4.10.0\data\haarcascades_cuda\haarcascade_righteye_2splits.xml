<?xml version="1.0"?>
<!--
    Tree-based 20x20 right eye detector.
    The detector is trained by 6665 positive samples from FERET, VALID and BioID face databases.
    Created by <PERSON><PERSON> (http://yushiqi.cn/research/eyedetection).

////////////////////////////////////////////////////////////////////////////////////////

  IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.

  By downloading, copying, installing or using the software you agree to this license.
  If you do not agree to this license, do not download, install,
  copy or use the software.


                        Intel License Agreement
                For Open Source Computer Vision Library

 Copyright (C) 2000, Intel Corporation, all rights reserved.
 Third party copyrights are property of their respective owners.

 Redistribution and use in source and binary forms, with or without modification,
 are permitted provided that the following conditions are met:

   * Redistribution's of source code must retain the above copyright notice,
     this list of conditions and the following disclaimer.

   * Redistribution's in binary form must reproduce the above copyright notice,
     this list of conditions and the following disclaimer in the documentation
     and/or other materials provided with the distribution.

   * The name of Intel Corporation may not be used to endorse or promote products
     derived from this software without specific prior written permission.

 This software is provided by the copyright holders and contributors "as is" and
 any express or implied warranties, including, but not limited to, the implied
 warranties of merchantability and fitness for a particular purpose are disclaimed.
 In no event shall the Intel Corporation or contributors be liable for any direct,
 indirect, incidental, special, exemplary, or consequential damages
 (including, but not limited to, procurement of substitute goods or services;
 loss of use, data, or profits; or business interruption) however caused
 and on any theory of liability, whether in contract, strict liability,
 or tort (including negligence or otherwise) arising in any way out of
 the use of this software, even if advised of the possibility of such damage.
-->
<opencv_storage>
<haarcascade_righteye type_id="opencv-haar-classifier">
  <size>
    20 20</size>
  <stages>
    <_>
      <!-- stage 0 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 7 3 12 -1.</_>
                <_>
                  8 11 3 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0482105500996113</threshold>
            <left_node>1</left_node>
            <right_val>-0.8614044785499573</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 7 8 3 -1.</_>
                <_>
                  10 9 4 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0415761992335320</threshold>
            <left_val>0.9176905751228333</left_val>
            <right_val>-0.2128400951623917</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 13 2 6 -1.</_>
                <_>
                  9 16 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.3528684228658676e-03</threshold>
            <left_val>-0.6978576779365540</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 2 12 8 -1.</_>
                <_>
                  11 2 6 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2144919785205275e-04</threshold>
            <left_val>0.7952337265014648</left_val>
            <right_val>-0.4894809126853943</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 0 6 6 -1.</_>
                <_>
                  14 3 6 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0218533501029015</threshold>
            <left_val>0.7057464122772217</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 1 5 12 -1.</_>
                <_>
                  8 4 5 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0996729284524918</threshold>
            <left_val>-0.7066624164581299</left_val>
            <right_val>0.7921097874641418</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 8 3 12 -1.</_>
                <_>
                  1 12 3 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0216648206114769</threshold>
            <left_node>1</left_node>
            <right_val>-0.6089860796928406</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 11 2 7 -1.</_>
                <_>
                  1 11 1 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.5680727604776621e-04</threshold>
            <left_val>0.7168570160865784</left_val>
            <right_val>-0.3046456873416901</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 12 9 7 -1.</_>
                <_>
                  9 12 3 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0133330496028066</threshold>
            <left_node>1</left_node>
            <right_val>-0.4684469103813171</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 4 6 9 -1.</_>
                <_>
                  15 4 2 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.2925298959016800e-03</threshold>
            <left_val>0.6423593163490295</left_val>
            <right_val>-0.5118042826652527</right_val></_></_></trees>
      <stage_threshold>-2.2325520515441895</stage_threshold>
      <parent>-1</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 1 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 7 12 12 -1.</_>
                <_>
                  8 11 4 4 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.3394871950149536</threshold>
            <left_val>0.7791326045989990</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 0 4 20 -1.</_>
                <_>
                  15 5 4 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1367247998714447</threshold>
            <left_val>0.2642127871513367</left_val>
            <right_val>-0.8791009187698364</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 5 8 -1.</_>
                <_>
                  0 16 5 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0313945002853870</threshold>
            <left_val>-0.6995670199394226</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 2 12 8 -1.</_>
                <_>
                  12 2 4 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0108281401917338</threshold>
            <left_val>0.7650449275970459</left_val>
            <right_val>-0.4371921122074127</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 0 1 8 -1.</_>
                <_>
                  19 4 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.2506768368184566e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5756158232688904</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 7 3 12 -1.</_>
                <_>
                  9 11 3 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0226754695177078</threshold>
            <left_val>0.7408059239387512</left_val>
            <right_val>-0.3667725026607513</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 2 8 8 -1.</_>
                <_>
                  1 6 8 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0391614809632301</threshold>
            <left_node>1</left_node>
            <right_val>0.6404516100883484</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 12 4 4 -1.</_>
                <_>
                  2 12 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.1934089493006468e-03</threshold>
            <left_val>0.1604758948087692</left_val>
            <right_val>-0.7101097702980042</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 7 6 8 -1.</_>
                <_>
                  9 7 3 4 2.</_>
                <_>
                  12 11 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0253219902515411</threshold>
            <left_node>1</left_node>
            <right_val>0.4957486093044281</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 18 7 2 -1.</_>
                <_>
                  13 19 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.7583367237821221e-04</threshold>
            <left_val>-0.7173789739608765</left_val>
            <right_val>-0.0185817703604698</right_val></_></_></trees>
      <stage_threshold>-2.1598019599914551</stage_threshold>
      <parent>0</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 2 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 7 12 12 -1.</_>
                <_>
                  8 11 4 4 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.2655405998229980</threshold>
            <left_node>1</left_node>
            <right_val>-0.8471245169639587</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 8 5 12 -1.</_>
                <_>
                  0 12 5 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0225327797234058</threshold>
            <left_val>0.8797718882560730</left_val>
            <right_val>-0.3339469134807587</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 4 8 -1.</_>
                <_>
                  18 0 2 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.5310067515820265e-04</threshold>
            <left_val>-0.8203244805335999</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 12 1 8 -1.</_>
                <_>
                  16 16 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.5820249973330647e-04</threshold>
            <left_val>-0.7517635822296143</left_val>
            <right_val>0.6776971220970154</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 1 9 9 -1.</_>
                <_>
                  12 1 3 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0837490117410198e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.8331400156021118</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 16 1 3 -1.</_>
                <_>
                  15 17 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.6810260023921728e-03</threshold>
            <left_val>0.5384474992752075</left_val>
            <right_val>-0.7653415799140930</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 14 2 4 -1.</_>
                <_>
                  2 16 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.5202371701598167e-04</threshold>
            <left_val>-0.7751489877700806</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 12 9 3 -1.</_>
                <_>
                  9 12 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0122417397797108</threshold>
            <left_val>0.6324015259742737</left_val>
            <right_val>-0.6339520812034607</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 18 5 2 -1.</_>
                <_>
                  0 19 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.2314196838997304e-05</threshold>
            <left_node>1</left_node>
            <right_val>0.4429041147232056</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 7 18 12 -1.</_>
                <_>
                  7 11 6 4 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.7191110849380493</threshold>
            <left_val>0.8013592958450317</left_val>
            <right_val>-0.5343109965324402</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 16 12 -1.</_>
                <_>
                  4 0 8 6 2.</_>
                <_>
                  12 6 8 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0242803394794464</threshold>
            <left_node>1</left_node>
            <right_val>-0.6779791712760925</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 3 2 5 -1.</_>
                <_>
                  9 3 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.4558640327304602e-03</threshold>
            <left_val>0.4903061091899872</left_val>
            <right_val>-0.8844798207283020</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 17 1 2 -1.</_>
                <_>
                  17 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.2993327446747571e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.5788341760635376</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 16 1 3 -1.</_>
                <_>
                  17 17 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.6443562023341656e-03</threshold>
            <left_val>-0.8587880730628967</left_val>
            <right_val>0.5245460271835327</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 9 2 6 -1.</_>
                <_>
                  1 9 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.0299328247783706e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.5271345973014832</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 3 3 4 -1.</_>
                <_>
                  4 3 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.7485519424080849e-03</threshold>
            <left_val>-0.8562619090080261</left_val>
            <right_val>0.4894461035728455</right_val></_></_></trees>
      <stage_threshold>-2.3451159000396729</stage_threshold>
      <parent>1</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 3 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 7 12 12 -1.</_>
                <_>
                  8 11 4 4 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.3837707936763763</threshold>
            <left_val>0.7171502113342285</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 0 7 8 -1.</_>
                <_>
                  10 4 7 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1383703052997589</threshold>
            <left_val>0.3439235985279083</left_val>
            <right_val>-0.7993127703666687</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 0 2 9 -1.</_>
                <_>
                  19 0 1 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.3107071067206562e-04</threshold>
            <left_val>-0.6835243105888367</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 13 1 4 -1.</_>
                <_>
                  4 13 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.1273438148200512e-03</threshold>
            <left_val>0.5825061798095703</left_val>
            <right_val>-0.4095500111579895</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 8 6 2 -1.</_>
                <_>
                  12 10 2 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0261006802320480</threshold>
            <left_node>1</left_node>
            <right_val>-0.4371330142021179</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 11 4 7 -1.</_>
                <_>
                  15 11 2 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0628979653120041e-03</threshold>
            <left_val>0.7068073749542236</left_val>
            <right_val>-0.2681793868541718</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 13 8 -1.</_>
                <_>
                  4 2 13 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0978548526763916</threshold>
            <left_val>0.7394003868103027</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 1 7 8 -1.</_>
                <_>
                  9 5 7 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1182982027530670</threshold>
            <left_val>0.6381418108940125</left_val>
            <right_val>-0.3872187137603760</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 12 9 -1.</_>
                <_>
                  10 0 6 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.5409049168229103e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4880301952362061</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 3 4 4 -1.</_>
                <_>
                  15 3 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.6851659640669823e-03</threshold>
            <left_val>0.3908346891403198</left_val>
            <right_val>-0.6556153893470764</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 4 4 -1.</_>
                <_>
                  0 18 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.6870240215212107e-03</threshold>
            <left_val>-0.4989174902439117</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 17 2 1 -1.</_>
                <_>
                  3 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-3.8136160001158714e-03</threshold>
            <left_val>-0.6640558838844299</left_val>
            <right_val>0.4065074920654297</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 16 1 3 -1.</_>
                <_>
                  16 17 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.0289309322834015e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6998921036720276</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 10 6 4 -1.</_>
                <_>
                  10 11 6 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.6308869756758213e-03</threshold>
            <left_val>0.4320684075355530</left_val>
            <right_val>-0.2966496944427490</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 0 1 4 -1.</_>
                <_>
                  19 2 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.3815231290645897e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.4680854082107544</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 0 3 3 -1.</_>
                <_>
                  18 1 1 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.5163291767239571e-03</threshold>
            <left_val>0.3652149140834808</left_val>
            <right_val>-0.7601454257965088</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 1 12 6 -1.</_>
                <_>
                  2 4 12 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0614795088768005</threshold>
            <left_node>1</left_node>
            <right_val>0.5699062943458557</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 2 1 16 -1.</_>
                <_>
                  15 6 1 8 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0462865792214870</threshold>
            <left_val>0.2262506037950516</left_val>
            <right_val>-0.4533078074455261</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 2 4 6 -1.</_>
                <_>
                  13 2 2 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.6903551556169987e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7728670835494995</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 3 3 3 -1.</_>
                <_>
                  12 3 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.8803169950842857e-03</threshold>
            <left_val>0.2734912037849426</left_val>
            <right_val>-0.6666783094406128</right_val></_></_></trees>
      <stage_threshold>-2.3431489467620850</stage_threshold>
      <parent>2</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 4 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 7 18 12 -1.</_>
                <_>
                  7 11 6 4 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.5542067289352417</threshold>
            <left_node>1</left_node>
            <right_val>-0.6062026023864746</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 1 12 9 -1.</_>
                <_>
                  12 1 4 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.9329799152910709e-03</threshold>
            <left_val>0.7854202985763550</left_val>
            <right_val>-0.3552212119102478</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 0 2 10 -1.</_>
                <_>
                  18 5 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0211699604988098</threshold>
            <left_val>0.5294768810272217</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 5 12 15 -1.</_>
                <_>
                  8 10 4 5 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.6742839813232422</threshold>
            <left_val>0.4606522023677826</left_val>
            <right_val>-0.7005820870399475</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 8 4 12 -1.</_>
                <_>
                  1 12 4 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0427250787615776</threshold>
            <left_node>1</left_node>
            <right_val>-0.5990480780601501</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 13 8 2 -1.</_>
                <_>
                  8 13 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0101093295961618</threshold>
            <left_val>0.6810922026634216</left_val>
            <right_val>-0.2073187977075577</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 4 15 -1.</_>
                <_>
                  18 0 2 15 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.5861130133271217e-03</threshold>
            <left_val>-0.5242084860801697</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 0 4 8 -1.</_>
                <_>
                  15 0 2 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.6380418613553047e-03</threshold>
            <left_val>-0.7016978263854980</left_val>
            <right_val>0.4410013854503632</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 8 9 -1.</_>
                <_>
                  5 3 8 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0976815819740295</threshold>
            <left_val>0.5770874023437500</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 0 6 6 -1.</_>
                <_>
                  10 0 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0101973600685596</threshold>
            <left_val>-0.0985185503959656</left_val>
            <right_val>-0.8811169862747192</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 17 3 3 -1.</_>
                <_>
                  11 17 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.5724549777805805e-03</threshold>
            <left_val>-0.8323333859443665</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 17 4 3 -1.</_>
                <_>
                  11 17 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.6594230439513922e-03</threshold>
            <left_val>0.3099535107612610</left_val>
            <right_val>-0.8160917758941650</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 12 4 4 -1.</_>
                <_>
                  15 12 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0042720241472125e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4355852007865906</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 18 4 2 -1.</_>
                <_>
                  9 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.6080000679939985e-03</threshold>
            <left_val>0.3356660008430481</left_val>
            <right_val>-0.8188933134078979</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 1 4 5 -1.</_>
                <_>
                  7 1 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.9724509008228779e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7704818248748779</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 0 6 5 -1.</_>
                <_>
                  4 0 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0122432401403785</threshold>
            <left_val>0.2253420054912567</left_val>
            <right_val>-0.6869555115699768</right_val></_></_></trees>
      <stage_threshold>-2.1268370151519775</stage_threshold>
      <parent>3</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 5 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 7 8 3 -1.</_>
                <_>
                  10 9 4 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0577849298715591</threshold>
            <left_node>1</left_node>
            <right_val>-0.7051600813865662</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 12 4 3 -1.</_>
                <_>
                  15 12 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7517809756100178e-03</threshold>
            <left_val>0.8565592169761658</left_val>
            <right_val>-0.0924034193158150</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 10 3 4 -1.</_>
                <_>
                  9 11 3 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0115223797038198</threshold>
            <left_node>1</left_node>
            <right_val>-0.4274964034557343</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 0 2 6 -1.</_>
                <_>
                  17 3 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.8323760963976383e-03</threshold>
            <left_val>0.7591353058815002</left_val>
            <right_val>-0.1089404970407486</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 9 6 9 -1.</_>
                <_>
                  3 12 2 3 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0809223875403404</threshold>
            <left_node>1</left_node>
            <right_val>-0.3136476874351501</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 11 8 4 -1.</_>
                <_>
                  9 11 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.2537011690437794e-03</threshold>
            <left_val>0.6999592185020447</left_val>
            <right_val>-0.1180569007992744</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 16 6 -1.</_>
                <_>
                  1 3 16 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1222786009311676</threshold>
            <left_val>0.5207250118255615</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 0 14 6 -1.</_>
                <_>
                  2 2 14 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0641681104898453</threshold>
            <left_val>0.3927274942398071</left_val>
            <right_val>-0.4219441115856171</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 11 2 9 -1.</_>
                <_>
                  1 11 1 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.3712888620793819e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.4952454864978790</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 11 1 8 -1.</_>
                <_>
                  18 11 1 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-2.8175620827823877e-03</threshold>
            <left_val>0.4135014116764069</left_val>
            <right_val>-0.3891927897930145</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 12 3 2 -1.</_>
                <_>
                  11 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.6368549335747957e-03</threshold>
            <left_val>0.6761502027511597</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 13 3 1 -1.</_>
                <_>
                  12 13 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3223909772932529e-03</threshold>
            <left_val>0.4342699944972992</left_val>
            <right_val>-0.3764213025569916</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 0 4 8 -1.</_>
                <_>
                  17 0 2 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.7143539520911872e-04</threshold>
            <left_val>-0.5563088059425354</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 17 4 3 -1.</_>
                <_>
                  14 17 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.0255712121725082e-03</threshold>
            <left_val>-0.5232859253883362</left_val>
            <right_val>0.3464682102203369</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 17 1 2 -1.</_>
                <_>
                  15 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-9.2711612523999065e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.4965266883373260</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 16 1 3 -1.</_>
                <_>
                  14 17 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.9847028888761997e-03</threshold>
            <left_val>0.3340164124965668</left_val>
            <right_val>-0.6244689226150513</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 0 14 8 -1.</_>
                <_>
                  3 2 14 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0472034402191639</threshold>
            <left_node>1</left_node>
            <right_val>0.5756261944770813</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 1 1 2 -1.</_>
                <_>
                  18 2 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.8562600063160062e-05</threshold>
            <left_val>0.0261726602911949</left_val>
            <right_val>-0.6084907054901123</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 8 3 -1.</_>
                <_>
                  8 0 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.5034219771623611e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6857675909996033</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 4 1 9 -1.</_>
                <_>
                  9 7 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.3834791071712971e-03</threshold>
            <left_val>-0.1731251031160355</left_val>
            <right_val>0.3856042921543121</right_val></_></_></trees>
      <stage_threshold>-2.0604379177093506</stage_threshold>
      <parent>4</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 6 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 13 9 2 -1.</_>
                <_>
                  9 13 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0155844502151012</threshold>
            <left_node>1</left_node>
            <right_val>-0.6664896011352539</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 13 5 6 -1.</_>
                <_>
                  0 16 5 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0145570198073983</threshold>
            <left_val>-0.4374513030052185</left_val>
            <right_val>0.7222781777381897</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 12 6 4 -1.</_>
                <_>
                  15 12 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.7889888994395733e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4318324029445648</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 6 12 2 -1.</_>
                <_>
                  8 10 4 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0819367691874504</threshold>
            <left_val>0.6846765279769897</left_val>
            <right_val>-0.2254672944545746</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 0 1 8 -1.</_>
                <_>
                  19 4 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.2995368130505085e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5240963101387024</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 2 12 8 -1.</_>
                <_>
                  11 2 6 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0137366401031613</threshold>
            <left_val>0.6162620782852173</left_val>
            <right_val>-0.3589316010475159</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 4 4 -1.</_>
                <_>
                  2 12 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.8069912008941174e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4238238930702209</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 8 13 9 -1.</_>
                <_>
                  7 11 13 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0771310999989510</threshold>
            <left_val>0.6059936285018921</left_val>
            <right_val>-0.3155533075332642</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 1 2 6 -1.</_>
                <_>
                  19 1 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.4640208943746984e-04</threshold>
            <left_val>-0.4920611083507538</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 4 5 8 -1.</_>
                <_>
                  7 6 5 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0348415784537792</threshold>
            <left_val>-0.0410178899765015</left_val>
            <right_val>0.6133087873458862</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 18 9 2 -1.</_>
                <_>
                  11 19 9 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.2969048526138067e-04</threshold>
            <left_val>-0.4547941982746124</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 7 2 3 -1.</_>
                <_>
                  11 7 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.8510129242204130e-05</threshold>
            <left_val>0.4000732898712158</left_val>
            <right_val>-0.2088876962661743</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 18 6 2 -1.</_>
                <_>
                  6 18 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.6054688282310963e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6793137788772583</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 13 6 7 -1.</_>
                <_>
                  8 13 2 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.1904482319951057e-03</threshold>
            <left_val>0.4706067144870758</left_val>
            <right_val>-0.1413861066102982</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 18 6 2 -1.</_>
                <_>
                  7 18 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.5724480189383030e-03</threshold>
            <left_val>-0.7052550911903381</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 5 2 2 -1.</_>
                <_>
                  18 6 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0458237314596772e-04</threshold>
            <left_val>0.3609785139560699</left_val>
            <right_val>-0.1836154013872147</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 2 9 4 -1.</_>
                <_>
                  6 4 9 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0185950603336096</threshold>
            <left_node>1</left_node>
            <right_val>0.4176576137542725</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 0 7 4 -1.</_>
                <_>
                  13 0 7 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0500725507736206</threshold>
            <left_val>-0.4186944961547852</left_val>
            <right_val>0.2818650901317596</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 9 3 6 -1.</_>
                <_>
                  11 11 3 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0203559193760157</threshold>
            <left_node>1</left_node>
            <right_val>-0.3649415075778961</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 8 4 6 -1.</_>
                <_>
                  16 11 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0286865197122097</threshold>
            <left_val>-0.5386778712272644</left_val>
            <right_val>0.3476788103580475</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 2 1 2 -1.</_>
                <_>
                  19 3 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.1101690991781652e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.4015679061412811</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 1 1 3 -1.</_>
                <_>
                  19 2 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.0686469506472349e-03</threshold>
            <left_val>0.3296366035938263</left_val>
            <right_val>-0.7095105051994324</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 12 2 4 -1.</_>
                <_>
                  13 12 1 2 2.</_>
                <_>
                  14 14 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1430920567363501e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.4417298138141632</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 9 3 5 -1.</_>
                <_>
                  15 10 1 5 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-8.8636036962270737e-03</threshold>
            <left_val>0.1842613071203232</left_val>
            <right_val>-0.4127517044544220</right_val></_></_></trees>
      <stage_threshold>-2.3187489509582520</stage_threshold>
      <parent>5</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 7 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 7 8 3 -1.</_>
                <_>
                  10 9 4 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0776376426219940</threshold>
            <left_node>1</left_node>
            <right_val>-0.4932152926921844</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 7 9 4 -1.</_>
                <_>
                  6 8 9 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-8.4830820560455322e-03</threshold>
            <left_val>0.7813854217529297</left_val>
            <right_val>-0.3606229126453400</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 11 2 6 -1.</_>
                <_>
                  1 11 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7180460272356868e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4769004881381989</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 13 5 6 -1.</_>
                <_>
                  0 16 5 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0247409492731094</threshold>
            <left_val>-0.3242008090019226</left_val>
            <right_val>0.5928000211715698</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 2 4 6 -1.</_>
                <_>
                  18 2 2 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.3028100151568651e-03</threshold>
            <left_val>-0.5399159789085388</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 5 6 7 -1.</_>
                <_>
                  15 7 2 7 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0346220396459103</threshold>
            <left_val>0.5207672715187073</left_val>
            <right_val>-0.3353079855442047</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 2 1 4 -1.</_>
                <_>
                  19 4 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.1505777304992080e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.4898169934749603</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 1 6 2 -1.</_>
                <_>
                  16 1 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.0145105496048927e-03</threshold>
            <left_val>-0.7796980142593384</left_val>
            <right_val>0.3658635914325714</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 12 4 5 -1.</_>
                <_>
                  15 12 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0250939521938562e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4697051048278809</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 15 2 3 -1.</_>
                <_>
                  17 16 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.5693178437650204e-03</threshold>
            <left_val>-0.6969562172889709</left_val>
            <right_val>0.3502543866634369</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 16 3 4 -1.</_>
                <_>
                  14 18 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.3235070509836078e-03</threshold>
            <left_val>-0.4470798075199127</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 16 1 2 -1.</_>
                <_>
                  16 16 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-3.3737940248101950e-03</threshold>
            <left_val>-0.5619515180587769</left_val>
            <right_val>0.3183380961418152</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 0 1 2 -1.</_>
                <_>
                  18 1 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.4095242123585194e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.3547363877296448</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 8 1 6 -1.</_>
                <_>
                  9 11 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.7294119354337454e-03</threshold>
            <left_val>0.4128524065017700</left_val>
            <right_val>-0.3141682147979736</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 5 2 1 -1.</_>
                <_>
                  19 5 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.3087652961257845e-05</threshold>
            <left_val>-0.3594656884670258</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 3 6 4 -1.</_>
                <_>
                  16 3 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0154360998421907</threshold>
            <left_val>-0.6132907867431641</left_val>
            <right_val>0.3430199921131134</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 18 4 2 -1.</_>
                <_>
                  9 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.1025019232183695e-03</threshold>
            <left_val>-0.7696225047111511</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 13 9 7 -1.</_>
                <_>
                  9 13 3 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0168495699763298</threshold>
            <left_val>0.3656980991363525</left_val>
            <right_val>-0.2121037989854813</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 16 2 2 -1.</_>
                <_>
                  1 17 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.6847798987291753e-05</threshold>
            <left_val>-0.4046655893325806</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 16 3 4 -1.</_>
                <_>
                  0 17 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.9984489344060421e-03</threshold>
            <left_val>0.2850377857685089</left_val>
            <right_val>-0.5875617861747742</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 1 4 5 -1.</_>
                <_>
                  9 1 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.1389962211251259e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.8718982934951782</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 1 6 9 -1.</_>
                <_>
                  12 1 2 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.8117469628341496e-04</threshold>
            <left_val>0.2518250942230225</left_val>
            <right_val>-0.3186821937561035</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 8 10 4 -1.</_>
                <_>
                  10 10 10 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.5429798774421215e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3672421872615814</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 8 5 4 -1.</_>
                <_>
                  15 10 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0321671105921268</threshold>
            <left_val>-0.7948120236396790</left_val>
            <right_val>0.2888720035552979</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 1 3 2 -1.</_>
                <_>
                  18 2 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>5.0912089645862579e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7147749066352844</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 11 3 5 -1.</_>
                <_>
                  14 11 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5173070132732391e-03</threshold>
            <left_val>0.4451462924480438</left_val>
            <right_val>-0.0952073410153389</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 7 4 3 -1.</_>
                <_>
                  10 7 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.0079508693888783e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.3602145016193390</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 0 8 1 -1.</_>
                <_>
                  5 0 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.4868541881442070e-03</threshold>
            <left_val>0.2827636003494263</left_val>
            <right_val>-0.7208412885665894</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 13 6 5 -1.</_>
                <_>
                  3 13 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.7957848981022835e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.2871744036674500</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 9 3 5 -1.</_>
                <_>
                  14 10 1 5 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-9.1829998418688774e-03</threshold>
            <left_val>0.5047904253005981</left_val>
            <right_val>-0.0707810372114182</right_val></_></_></trees>
      <stage_threshold>-2.2203750610351562</stage_threshold>
      <parent>6</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 8 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 8 4 6 -1.</_>
                <_>
                  9 10 4 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0557602494955063</threshold>
            <left_node>1</left_node>
            <right_val>-0.5585464835166931</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 7 6 6 -1.</_>
                <_>
                  13 9 2 6 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0594366900622845</threshold>
            <left_val>0.6894369721412659</left_val>
            <right_val>-0.3719508051872253</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 7 6 -1.</_>
                <_>
                  7 3 7 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0546371787786484</threshold>
            <left_val>0.5304033160209656</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 1 10 12 -1.</_>
                <_>
                  3 5 10 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2360835969448090</threshold>
            <left_val>-0.4735530912876129</left_val>
            <right_val>0.4632248878479004</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 12 6 4 -1.</_>
                <_>
                  15 12 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.4560505822300911e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3254477977752686</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 9 6 9 -1.</_>
                <_>
                  2 12 2 3 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0531827099621296</threshold>
            <left_val>0.6346856951713562</left_val>
            <right_val>-0.2826836109161377</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 0 12 11 -1.</_>
                <_>
                  12 0 4 11 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0106381997466087</threshold>
            <left_node>1</left_node>
            <right_val>-0.5577635169029236</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 11 1 8 -1.</_>
                <_>
                  13 11 1 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0212070196866989</threshold>
            <left_val>0.3904919028282166</left_val>
            <right_val>-0.4211193025112152</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 4 1 2 -1.</_>
                <_>
                  19 5 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.6731878430582583e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.4180330932140350</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 15 1 2 -1.</_>
                <_>
                  2 15 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.4976451317779720e-04</threshold>
            <left_val>0.3735578954219818</left_val>
            <right_val>-0.3919964134693146</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 16 2 2 -1.</_>
                <_>
                  17 16 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.7574670966714621e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7910463213920593</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 16 1 3 -1.</_>
                <_>
                  15 17 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.5649419985711575e-03</threshold>
            <left_val>0.1925818026065826</left_val>
            <right_val>-0.7534446120262146</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 11 3 2 -1.</_>
                <_>
                  6 12 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-9.4359368085861206e-03</threshold>
            <left_val>0.4483475089073181</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 11 2 2 -1.</_>
                <_>
                  4 11 1 1 2.</_>
                <_>
                  5 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.4136210083961487e-03</threshold>
            <left_val>-0.3387843072414398</left_val>
            <right_val>0.4429191946983337</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 7 3 2 -1.</_>
                <_>
                  18 8 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.9976350963115692e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6663758158683777</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 9 3 8 -1.</_>
                <_>
                  16 11 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5278969658538699e-03</threshold>
            <left_val>0.3129239976406097</left_val>
            <right_val>-0.2802799046039581</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 0 1 4 -1.</_>
                <_>
                  19 2 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.2376639865105972e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.4667209088802338</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 0 1 3 -1.</_>
                <_>
                  19 1 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.6323389718309045e-03</threshold>
            <left_val>0.2799555957317352</left_val>
            <right_val>-0.6132150888442993</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 0 10 3 -1.</_>
                <_>
                  14 0 5 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.7096219174563885e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.2035254985094070</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 3 15 17 -1.</_>
                <_>
                  8 3 5 17 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0785993188619614</threshold>
            <left_val>0.0727269127964973</left_val>
            <right_val>-0.6867709755897522</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 0 4 4 -1.</_>
                <_>
                  9 0 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.6581400781869888e-03</threshold>
            <left_val>-0.6807945966720581</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 11 8 1 -1.</_>
                <_>
                  1 11 4 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0426121987402439</threshold>
            <left_val>-0.8455178141593933</left_val>
            <right_val>0.1599057018756866</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 10 2 4 -1.</_>
                <_>
                  3 11 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.8822778626345098e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.4794569909572601</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 17 4 3 -1.</_>
                <_>
                  5 17 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.6951142139732838e-03</threshold>
            <left_val>-0.8223428130149841</left_val>
            <right_val>0.2043157964944839</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 7 2 1 -1.</_>
                <_>
                  19 7 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.1706348787993193e-05</threshold>
            <left_val>-0.3174282014369965</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 7 18 3 -1.</_>
                <_>
                  11 7 9 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0138099100440741</threshold>
            <left_val>0.3076930046081543</left_val>
            <right_val>-0.4354496896266937</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 11 4 2 -1.</_>
                <_>
                  4 11 2 1 2.</_>
                <_>
                  6 12 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.2187729850411415e-03</threshold>
            <left_val>0.6249998211860657</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 9 2 4 -1.</_>
                <_>
                  4 11 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.9540808647871017e-03</threshold>
            <left_val>0.1322520971298218</left_val>
            <right_val>-0.3974510133266449</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 1 3 1 -1.</_>
                <_>
                  17 2 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.2203531116247177e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6004533171653748</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 18 1 2 -1.</_>
                <_>
                  4 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.2806582718621939e-05</threshold>
            <left_val>-0.2242998033761978</left_val>
            <right_val>0.2976852059364319</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 18 4 2 -1.</_>
                <_>
                  10 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.3292789701372385e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7598208189010620</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 11 5 4 -1.</_>
                <_>
                  11 12 5 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.3711822256445885e-03</threshold>
            <left_val>0.2648491859436035</left_val>
            <right_val>-0.2600553929805756</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 2 2 1 -1.</_>
                <_>
                  19 2 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.4782587287481874e-05</threshold>
            <left_val>-0.3211930096149445</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 0 6 2 -1.</_>
                <_>
                  9 0 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.6606678776443005e-03</threshold>
            <left_val>0.2417640984058380</left_val>
            <right_val>-0.8382272720336914</right_val></_></_></trees>
      <stage_threshold>-2.1757249832153320</stage_threshold>
      <parent>7</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 9 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 13 8 2 -1.</_>
                <_>
                  8 13 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0148482797667384</threshold>
            <left_node>1</left_node>
            <right_val>-0.5339112877845764</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 12 4 4 -1.</_>
                <_>
                  15 12 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6066679963842034e-03</threshold>
            <left_val>0.7600271105766296</left_val>
            <right_val>-0.2109173983335495</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 8 17 9 -1.</_>
                <_>
                  3 11 17 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1565192043781281</threshold>
            <left_node>1</left_node>
            <right_val>-0.4281854927539825</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 12 4 3 -1.</_>
                <_>
                  2 12 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.5439779534935951e-03</threshold>
            <left_val>0.6562075018882751</left_val>
            <right_val>-0.2294984012842178</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 3 12 6 -1.</_>
                <_>
                  12 3 4 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0194483399391174</threshold>
            <left_node>1</left_node>
            <right_val>-0.4421252012252808</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 14 3 6 -1.</_>
                <_>
                  0 17 3 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.6653067953884602e-03</threshold>
            <left_val>-0.3395059108734131</left_val>
            <right_val>0.4658721983432770</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 0 13 9 -1.</_>
                <_>
                  3 3 13 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.2114201039075851</threshold>
            <left_val>0.5500797033309937</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 2 8 6 -1.</_>
                <_>
                  8 5 8 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1062842980027199</threshold>
            <left_val>0.6828094720840454</left_val>
            <right_val>-0.3098773956298828</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 11 18 3 -1.</_>
                <_>
                  7 11 6 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0526535995304585</threshold>
            <left_node>1</left_node>
            <right_val>-0.3481881916522980</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 17 1 2 -1.</_>
                <_>
                  16 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.3522300731856376e-05</threshold>
            <left_val>0.5056676268577576</left_val>
            <right_val>-0.2522951960563660</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 12 6 4 -1.</_>
                <_>
                  16 12 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.7972650974988937e-03</threshold>
            <left_val>0.3023801147937775</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 11 4 5 -1.</_>
                <_>
                  14 11 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.7428899668157101e-03</threshold>
            <left_val>0.2287323027849197</left_val>
            <right_val>-0.4836657941341400</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 3 1 2 -1.</_>
                <_>
                  19 4 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.2694038458866999e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.3798896074295044</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 0 1 3 -1.</_>
                <_>
                  19 1 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.1983739677816629e-03</threshold>
            <left_val>-0.6744245290756226</left_val>
            <right_val>0.2861126065254211</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 2 8 4 -1.</_>
                <_>
                  7 4 8 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0225447993725538</threshold>
            <left_node>1</left_node>
            <right_val>0.4756571948528290</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 12 3 2 -1.</_>
                <_>
                  10 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.1783939339220524e-03</threshold>
            <left_val>-0.2889334857463837</left_val>
            <right_val>0.5550963878631592</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 8 3 2 -1.</_>
                <_>
                  16 9 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.4742769785225391e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5982655286788940</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 15 3 2 -1.</_>
                <_>
                  16 15 3 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-8.1408787518739700e-03</threshold>
            <left_val>-0.5593379139900208</left_val>
            <right_val>0.2234921008348465</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 12 3 3 -1.</_>
                <_>
                  7 12 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0238809995353222e-03</threshold>
            <left_val>0.4591797888278961</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 12 3 1 -1.</_>
                <_>
                  14 13 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.9159598313271999e-03</threshold>
            <left_val>0.6223490238189697</left_val>
            <right_val>-0.2446815073490143</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 1 3 -1.</_>
                <_>
                  3 1 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.3184430319815874e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6047807931900024</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 2 6 4 -1.</_>
                <_>
                  10 2 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.7198208309710026e-03</threshold>
            <left_val>0.2100450992584229</left_val>
            <right_val>-0.6433128118515015</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 15 2 3 -1.</_>
                <_>
                  14 16 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.5973320268094540e-03</threshold>
            <left_val>-0.7162581086158752</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 18 8 2 -1.</_>
                <_>
                  12 19 8 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.0320380281191319e-04</threshold>
            <left_val>-0.3801802992820740</left_val>
            <right_val>0.2133689969778061</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 12 6 7 -1.</_>
                <_>
                  9 12 2 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.8205389864742756e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3595725893974304</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 18 6 2 -1.</_>
                <_>
                  6 18 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.8883338458836079e-03</threshold>
            <left_val>0.2647193074226379</left_val>
            <right_val>-0.5899668931961060</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 12 3 3 -1.</_>
                <_>
                  12 12 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3334590476006269e-03</threshold>
            <left_val>0.3225848972797394</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 12 2 2 -1.</_>
                <_>
                  13 12 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5447080368176103e-03</threshold>
            <left_val>0.3697105050086975</left_val>
            <right_val>-0.3130857050418854</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 5 2 1 -1.</_>
                <_>
                  19 5 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.5150746852159500e-05</threshold>
            <left_val>-0.3467453122138977</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 19 4 1 -1.</_>
                <_>
                  6 19 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.1108840117231011e-03</threshold>
            <left_val>-0.5747753977775574</left_val>
            <right_val>0.2920114099979401</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 11 5 2 -1.</_>
                <_>
                  0 12 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6881119518075138e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.3604178130626678</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 0 2 2 -1.</_>
                <_>
                  18 1 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2814450019504875e-04</threshold>
            <left_val>0.3504320979118347</left_val>
            <right_val>-0.2201405018568039</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 12 6 -1.</_>
                <_>
                  1 2 12 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0195469707250595</threshold>
            <left_node>1</left_node>
            <right_val>0.4129591882228851</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 1 6 1 -1.</_>
                <_>
                  3 3 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0110611803829670</threshold>
            <left_val>0.2596271932125092</left_val>
            <right_val>-0.3487595021724701</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 9 3 1 -1.</_>
                <_>
                  17 10 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.8147419905290008e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5201988816261292</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 10 1 6 -1.</_>
                <_>
                  12 12 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.1724010631442070e-03</threshold>
            <left_val>0.2745266854763031</left_val>
            <right_val>-0.2682884931564331</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 1 1 3 -1.</_>
                <_>
                  2 2 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.2158189676702023e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5734090805053711</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 0 4 3 -1.</_>
                <_>
                  2 1 4 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-9.6856858581304550e-03</threshold>
            <left_val>-0.5802857279777527</left_val>
            <right_val>0.1856441050767899</right_val></_></_></trees>
      <stage_threshold>-2.2618789672851562</stage_threshold>
      <parent>8</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 10 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 14 8 1 -1.</_>
                <_>
                  8 14 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0120652196928859</threshold>
            <left_val>0.6167957186698914</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 8 18 9 -1.</_>
                <_>
                  7 11 6 3 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.4906777143478394</threshold>
            <left_val>0.1406393945217133</left_val>
            <right_val>-0.5535774230957031</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 0 1 18 -1.</_>
                <_>
                  19 6 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.6585717722773552e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5133228898048401</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 13 3 6 -1.</_>
                <_>
                  1 16 3 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0158275607973337</threshold>
            <left_val>-0.3630152046680450</left_val>
            <right_val>0.4334334135055542</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 10 7 3 -1.</_>
                <_>
                  6 11 7 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0140811800956726</threshold>
            <left_val>0.5422372221946716</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 9 7 3 -1.</_>
                <_>
                  6 10 7 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0121394498273730</threshold>
            <left_val>0.4428128898143768</left_val>
            <right_val>-0.3417111933231354</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 1 6 8 -1.</_>
                <_>
                  17 1 3 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.8055798076093197e-03</threshold>
            <left_val>-0.4865975975990295</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 6 2 4 -1.</_>
                <_>
                  10 6 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0759910158813000e-05</threshold>
            <left_val>0.3481867909431458</left_val>
            <right_val>-0.3280673921108246</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 11 7 2 -1.</_>
                <_>
                  6 12 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0181996300816536</threshold>
            <left_val>0.5659415125846863</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 11 3 6 -1.</_>
                <_>
                  18 12 1 6 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-2.5289389304816723e-03</threshold>
            <left_val>0.1131006032228470</left_val>
            <right_val>-0.4077238142490387</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 17 1 2 -1.</_>
                <_>
                  19 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.0156990028917789e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5984297990798950</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 9 4 2 -1.</_>
                <_>
                  17 10 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.9432660085149109e-04</threshold>
            <left_val>0.2843945026397705</left_val>
            <right_val>-0.3219023048877716</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 18 4 2 -1.</_>
                <_>
                  7 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.0865290425717831e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7828571200370789</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 12 4 4 -1.</_>
                <_>
                  3 12 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7371569992974401e-03</threshold>
            <left_val>0.3358530104160309</left_val>
            <right_val>-0.2058237046003342</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 2 1 2 -1.</_>
                <_>
                  19 3 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0026202592998743e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.3910934925079346</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 2 1 3 -1.</_>
                <_>
                  19 3 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4891549944877625e-03</threshold>
            <left_val>-0.4695341885089874</left_val>
            <right_val>0.2760924100875854</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 12 12 3 -1.</_>
                <_>
                  7 12 6 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0117884296923876</threshold>
            <left_node>1</left_node>
            <right_val>-0.4011414945125580</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 18 4 1 -1.</_>
                <_>
                  7 18 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5155089786276221e-03</threshold>
            <left_val>-0.7429047822952271</left_val>
            <right_val>0.2769562900066376</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 2 12 6 -1.</_>
                <_>
                  5 5 12 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0683967173099518</threshold>
            <left_node>1</left_node>
            <right_val>0.4523564875125885</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 1 6 6 -1.</_>
                <_>
                  9 4 6 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0764414072036743</threshold>
            <left_val>0.4284816980361938</left_val>
            <right_val>-0.3163630962371826</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 11 9 -1.</_>
                <_>
                  7 3 11 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0683102011680603</threshold>
            <left_node>1</left_node>
            <right_val>0.5140427947044373</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 0 8 9 -1.</_>
                <_>
                  2 3 8 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0645080134272575</threshold>
            <left_val>0.1808187067508698</left_val>
            <right_val>-0.3421795070171356</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 3 4 3 -1.</_>
                <_>
                  6 3 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.8335719835013151e-03</threshold>
            <left_val>-0.6950976848602295</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 18 3 2 -1.</_>
                <_>
                  0 19 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.9732237868010998e-04</threshold>
            <left_val>-0.4372459053993225</left_val>
            <right_val>0.2022608071565628</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 10 19 -1.</_>
                <_>
                  6 0 5 19 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.2286991029977798</threshold>
            <left_val>0.6466220021247864</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 8 2 3 -1.</_>
                <_>
                  2 9 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.9855249449610710e-03</threshold>
            <left_val>8.1149758771061897e-03</left_val>
            <right_val>-0.6021029949188232</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 17 4 3 -1.</_>
                <_>
                  11 17 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9535989742726088e-03</threshold>
            <left_val>-0.7201312780380249</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 13 3 2 -1.</_>
                <_>
                  12 13 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.1225619129836559e-03</threshold>
            <left_val>0.5087562203407288</left_val>
            <right_val>-0.0593666099011898</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 12 3 2 -1.</_>
                <_>
                  11 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9382819775491953e-03</threshold>
            <left_val>0.3928753137588501</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 11 3 3 -1.</_>
                <_>
                  10 11 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.8961478061974049e-03</threshold>
            <left_val>0.4186604022979736</left_val>
            <right_val>-0.2540551126003265</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 2 3 1 -1.</_>
                <_>
                  18 3 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.5730929337441921e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5870727896690369</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 0 6 13 -1.</_>
                <_>
                  14 0 2 13 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0166477393358946</threshold>
            <left_val>0.1920848041772842</left_val>
            <right_val>-0.6038894057273865</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 3 1 -1.</_>
                <_>
                  17 1 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.4041840806603432e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5719233751296997</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 11 1 2 -1.</_>
                <_>
                  5 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.0452830772846937e-04</threshold>
            <left_val>0.3486076891422272</left_val>
            <right_val>-0.1304924041032791</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 11 4 2 -1.</_>
                <_>
                  2 11 2 1 2.</_>
                <_>
                  4 12 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.0814210660755634e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.5177801847457886</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 15 2 3 -1.</_>
                <_>
                  15 16 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.3811479806900024e-03</threshold>
            <left_val>-6.3828541897237301e-03</left_val>
            <right_val>-0.6144781708717346</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 17 4 2 -1.</_>
                <_>
                  9 17 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.7499340940266848e-03</threshold>
            <left_val>-0.6540778875350952</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 16 4 3 -1.</_>
                <_>
                  0 17 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.8207710497081280e-03</threshold>
            <left_val>-0.6002961993217468</left_val>
            <right_val>0.1437458992004395</right_val></_></_></trees>
      <stage_threshold>-2.0994780063629150</stage_threshold>
      <parent>9</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 11 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 13 6 2 -1.</_>
                <_>
                  12 13 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.9710120335221291e-03</threshold>
            <left_val>-0.6199223995208740</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 14 1 2 -1.</_>
                <_>
                  2 14 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-9.7160867881029844e-04</threshold>
            <left_val>0.5487716197967529</left_val>
            <right_val>-0.4060696065425873</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 10 8 3 -1.</_>
                <_>
                  5 11 8 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0109458696097136</threshold>
            <left_val>0.4693686962127686</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 0 3 8 -1.</_>
                <_>
                  13 2 3 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0611748211085796</threshold>
            <left_val>0.3057084977626801</left_val>
            <right_val>-0.4445989131927490</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 11 4 7 -1.</_>
                <_>
                  15 11 2 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.3100150283426046e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3781644105911255</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 11 15 4 -1.</_>
                <_>
                  8 11 5 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0475850515067577</threshold>
            <left_val>0.4886583983898163</left_val>
            <right_val>-0.2972886860370636</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 1 9 9 -1.</_>
                <_>
                  12 1 3 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.5944279041141272e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5440536737442017</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 11 4 7 -1.</_>
                <_>
                  2 11 2 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.9469371549785137e-03</threshold>
            <left_val>0.3638249039649963</left_val>
            <right_val>-0.3046984970569611</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 1 4 -1.</_>
                <_>
                  0 18 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.1871569808572531e-04</threshold>
            <left_val>-0.4682297110557556</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 0 1 6 -1.</_>
                <_>
                  19 3 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6655721012502909e-03</threshold>
            <left_val>0.3313196897506714</left_val>
            <right_val>-0.2991823852062225</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 8 9 9 -1.</_>
                <_>
                  11 11 9 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0395346507430077</threshold>
            <left_node>1</left_node>
            <right_val>-0.3531683087348938</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 17 8 3 -1.</_>
                <_>
                  11 17 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.4085611635819077e-04</threshold>
            <left_val>0.4444710016250610</left_val>
            <right_val>-0.1108866035938263</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 4 2 2 -1.</_>
                <_>
                  19 4 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.9526307925116271e-05</threshold>
            <left_val>-0.3940326869487762</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 11 3 3 -1.</_>
                <_>
                  9 12 1 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.6976682543754578e-03</threshold>
            <left_val>0.5718188881874084</left_val>
            <right_val>-0.0163709502667189</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 2 3 4 -1.</_>
                <_>
                  13 2 3 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0394690409302711</threshold>
            <left_node>1</left_node>
            <right_val>0.6915212273597717</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 6 16 3 -1.</_>
                <_>
                  12 6 8 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.2811042666435242e-03</threshold>
            <left_val>0.1334999054670334</left_val>
            <right_val>-0.4706448018550873</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 12 1 3 -1.</_>
                <_>
                  9 13 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.3219728395342827e-03</threshold>
            <left_val>0.3823925852775574</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 12 3 3 -1.</_>
                <_>
                  9 13 1 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.5436040274798870e-03</threshold>
            <left_val>0.1564587950706482</left_val>
            <right_val>-0.4108820855617523</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 17 1 2 -1.</_>
                <_>
                  17 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.9953341406071559e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.3922179937362671</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 16 2 2 -1.</_>
                <_>
                  16 16 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.9089371934533119e-03</threshold>
            <left_val>-0.5908386707305908</left_val>
            <right_val>0.2792448103427887</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 9 6 -1.</_>
                <_>
                  6 2 9 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0447213910520077</threshold>
            <left_val>0.4145449101924896</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 0 10 8 -1.</_>
                <_>
                  5 2 10 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0412670187652111</threshold>
            <left_val>-0.3224200904369354</left_val>
            <right_val>0.3784987926483154</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 5 2 1 -1.</_>
                <_>
                  18 5 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.6728709751041606e-05</threshold>
            <left_val>-0.3222804069519043</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 0 9 9 -1.</_>
                <_>
                  14 0 3 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0624278709292412</threshold>
            <left_val>-0.5966644883155823</left_val>
            <right_val>0.2891578078269958</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 9 7 3 -1.</_>
                <_>
                  6 10 7 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.6994128972291946e-03</threshold>
            <left_val>0.3749934136867523</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 12 6 2 -1.</_>
                <_>
                  3 12 3 1 2.</_>
                <_>
                  6 13 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.5202910229563713e-03</threshold>
            <left_val>-0.2813245952129364</left_val>
            <right_val>0.5098885893821716</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 10 1 2 -1.</_>
                <_>
                  2 10 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-3.3640549518167973e-03</threshold>
            <left_val>-0.6397820711135864</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 15 2 3 -1.</_>
                <_>
                  12 16 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.8076648749411106e-03</threshold>
            <left_val>-0.7310581803321838</left_val>
            <right_val>0.1447525024414062</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 2 6 5 -1.</_>
                <_>
                  9 2 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0126334596425295</threshold>
            <left_node>1</left_node>
            <right_val>-0.7772529721260071</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 13 6 3 -1.</_>
                <_>
                  15 13 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9199919663369656e-03</threshold>
            <left_val>0.2325859963893890</left_val>
            <right_val>-0.2049060016870499</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 9 3 8 -1.</_>
                <_>
                  17 11 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0305822491645813</threshold>
            <left_val>-0.6573882102966309</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 3 4 3 -1.</_>
                <_>
                  9 3 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.7796169742941856e-03</threshold>
            <left_val>-0.5488834977149963</left_val>
            <right_val>0.1383789032697678</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 6 2 12 -1.</_>
                <_>
                  15 6 1 12 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.6163080520927906e-03</threshold>
            <left_val>-0.3591234982013702</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 14 4 2 -1.</_>
                <_>
                  11 14 4 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.8409560434520245e-03</threshold>
            <left_val>0.2240446954965591</left_val>
            <right_val>-0.3788186013698578</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 2 5 4 -1.</_>
                <_>
                  9 4 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0392002612352371</threshold>
            <left_val>0.5009055137634277</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 12 3 3 -1.</_>
                <_>
                  14 12 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2543789818882942e-03</threshold>
            <left_val>0.3136400878429413</left_val>
            <right_val>-0.2213186025619507</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 1 2 3 -1.</_>
                <_>
                  18 2 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.3894659243524075e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5869951248168945</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 13 4 1 -1.</_>
                <_>
                  6 13 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0725490283221006e-03</threshold>
            <left_val>0.4714120924472809</left_val>
            <right_val>-0.0325704887509346</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 10 2 2 -1.</_>
                <_>
                  5 10 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>8.9095337898470461e-05</threshold>
            <left_val>-0.3044430911540985</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 11 1 2 -1.</_>
                <_>
                  2 11 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.6920049674808979e-03</threshold>
            <left_val>0.3028089106082916</left_val>
            <right_val>-0.3890272974967957</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 3 2 6 -1.</_>
                <_>
                  18 5 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0117840003222227</threshold>
            <left_node>1</left_node>
            <right_val>-0.6899343729019165</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 4 6 2 -1.</_>
                <_>
                  10 5 6 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.9335917681455612e-03</threshold>
            <left_val>-0.0677639394998550</left_val>
            <right_val>0.4649978876113892</right_val></_></_></trees>
      <stage_threshold>-2.1254189014434814</stage_threshold>
      <parent>10</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 12 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 13 6 2 -1.</_>
                <_>
                  13 13 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0114308400079608</threshold>
            <left_val>-0.3927457034587860</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 11 3 4 -1.</_>
                <_>
                  9 11 3 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0322429202497005</threshold>
            <left_val>0.6556879878044128</left_val>
            <right_val>-0.3106881082057953</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 11 2 5 -1.</_>
                <_>
                  1 11 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.8382760463282466e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4082506895065308</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 8 20 9 -1.</_>
                <_>
                  0 11 20 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1076439991593361</threshold>
            <left_val>0.4328007996082306</left_val>
            <right_val>-0.4226345121860504</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 0 1 6 -1.</_>
                <_>
                  18 3 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.3866090923547745e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4643520116806030</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 1 6 7 -1.</_>
                <_>
                  17 1 3 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.6586214601993561e-03</threshold>
            <left_val>-0.4067307114601135</left_val>
            <right_val>0.4126786887645721</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 13 2 4 -1.</_>
                <_>
                  4 13 1 2 2.</_>
                <_>
                  5 15 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6437229933217168e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.2134404927492142</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 9 18 6 -1.</_>
                <_>
                  7 9 6 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0985111370682716</threshold>
            <left_val>0.6843231916427612</left_val>
            <right_val>-0.0970350131392479</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 5 4 -1.</_>
                <_>
                  0 18 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.4292360544204712e-03</threshold>
            <left_val>-0.3949891030788422</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 14 3 4 -1.</_>
                <_>
                  8 15 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.6966210938990116e-03</threshold>
            <left_val>-0.1134598031640053</left_val>
            <right_val>0.4968199133872986</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 7 8 3 -1.</_>
                <_>
                  11 7 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.8480701670050621e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3129310011863708</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 3 4 7 -1.</_>
                <_>
                  13 3 2 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.7258379422128201e-03</threshold>
            <left_val>-0.6163579225540161</left_val>
            <right_val>0.3176476955413818</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 12 2 8 -1.</_>
                <_>
                  13 12 1 4 2.</_>
                <_>
                  14 16 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.0052040927112103e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.3172427117824554</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 10 3 5 -1.</_>
                <_>
                  14 11 1 5 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0134073402732611</threshold>
            <left_val>0.1973506063222885</left_val>
            <right_val>-0.3719918131828308</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 5 4 5 -1.</_>
                <_>
                  11 5 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.4199679978191853e-03</threshold>
            <left_val>-0.5716447830200195</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 11 18 2 -1.</_>
                <_>
                  8 11 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0328009389340878</threshold>
            <left_val>0.3059993088245392</left_val>
            <right_val>-0.1739796996116638</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 1 2 -1.</_>
                <_>
                  2 0 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.9407979531679302e-05</threshold>
            <left_val>-0.2827053070068359</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 0 1 2 -1.</_>
                <_>
                  2 0 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.1550169698894024e-03</threshold>
            <left_val>0.2968680858612061</left_val>
            <right_val>-0.4849430918693542</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 17 1 2 -1.</_>
                <_>
                  15 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.5589967309497297e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.3853113949298859</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 16 1 3 -1.</_>
                <_>
                  16 17 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-3.2147730235010386e-03</threshold>
            <left_val>-0.6330680847167969</left_val>
            <right_val>0.2343475073575974</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 0 2 10 -1.</_>
                <_>
                  19 0 1 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.6021779738366604e-03</threshold>
            <left_val>-0.2957904934883118</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 2 6 7 -1.</_>
                <_>
                  16 2 2 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0194780193269253</threshold>
            <left_val>-0.4962520897388458</left_val>
            <right_val>0.2609257996082306</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 0 4 4 -1.</_>
                <_>
                  12 0 4 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0251937508583069</threshold>
            <left_val>0.3938488066196442</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 3 15 6 -1.</_>
                <_>
                  0 5 15 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0464877299964428</threshold>
            <left_val>0.2216883003711700</left_val>
            <right_val>-0.2969174087047577</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 1 4 4 -1.</_>
                <_>
                  6 1 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.3414267711341381e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6766117811203003</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 13 6 7 -1.</_>
                <_>
                  9 13 2 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4886759929358959e-03</threshold>
            <left_val>0.2050992995500565</left_val>
            <right_val>-0.2977114021778107</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 18 6 2 -1.</_>
                <_>
                  8 18 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.8827269822359085e-03</threshold>
            <left_val>-0.6130179762840271</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 15 5 2 -1.</_>
                <_>
                  0 16 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.0498890494927764e-04</threshold>
            <left_val>-0.3402321934700012</left_val>
            <right_val>0.1816820949316025</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 1 12 6 -1.</_>
                <_>
                  4 3 12 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0983389019966125</threshold>
            <left_val>0.4772956967353821</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 0 13 8 -1.</_>
                <_>
                  5 2 13 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0561418086290359</threshold>
            <left_val>-0.2290443927049637</left_val>
            <right_val>0.3441008925437927</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 10 6 6 -1.</_>
                <_>
                  15 12 2 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.5787130258977413e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3591017127037048</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 9 3 1 -1.</_>
                <_>
                  16 10 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.5108759980648756e-03</threshold>
            <left_val>0.2490043044090271</left_val>
            <right_val>-0.4379807114601135</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 11 3 3 -1.</_>
                <_>
                  6 12 1 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.0129738412797451e-03</threshold>
            <left_val>0.3116418123245239</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 11 2 2 -1.</_>
                <_>
                  6 11 1 1 2.</_>
                <_>
                  7 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.9341192031279206e-04</threshold>
            <left_val>0.2675966024398804</left_val>
            <right_val>-0.3680290877819061</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 3 3 2 -1.</_>
                <_>
                  18 4 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>6.1855330131947994e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7215331792831421</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 3 3 3 -1.</_>
                <_>
                  17 4 1 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.3785060085356236e-03</threshold>
            <left_val>-0.5371438264846802</left_val>
            <right_val>0.1382489055395126</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 13 3 1 -1.</_>
                <_>
                  13 13 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.7488732747733593e-04</threshold>
            <left_val>0.3740605115890503</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 12 3 2 -1.</_>
                <_>
                  12 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3102099765092134e-03</threshold>
            <left_val>0.1900379061698914</left_val>
            <right_val>-0.3163227140903473</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 0 1 2 -1.</_>
                <_>
                  10 0 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.9453211249783635e-04</threshold>
            <left_val>-0.2328317016363144</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 13 1 6 -1.</_>
                <_>
                  17 13 1 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.2824690202251077e-03</threshold>
            <left_val>0.3046380877494812</left_val>
            <right_val>-0.4809210896492004</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 14 2 4 -1.</_>
                <_>
                  16 14 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0226248204708099</threshold>
            <left_val>-0.6878347992897034</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 0 4 3 -1.</_>
                <_>
                  4 0 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.3685249984264374e-03</threshold>
            <left_val>0.1240309029817581</left_val>
            <right_val>-0.7922073006629944</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 14 1 -1.</_>
                <_>
                  13 0 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.6756488047540188e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.1761142015457153</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 15 18 5 -1.</_>
                <_>
                  8 15 6 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0817692130804062</threshold>
            <left_val>0.3894216120243073</left_val>
            <right_val>-0.4509401023387909</right_val></_></_></trees>
      <stage_threshold>-2.0614759922027588</stage_threshold>
      <parent>11</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 13 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 11 8 5 -1.</_>
                <_>
                  8 11 4 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0200035497546196</threshold>
            <left_node>1</left_node>
            <right_val>-0.5665075182914734</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 8 5 12 -1.</_>
                <_>
                  0 11 5 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0326212085783482</threshold>
            <left_val>0.5080708265304565</left_val>
            <right_val>-0.4534570872783661</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 0 6 2 -1.</_>
                <_>
                  14 0 6 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0106681399047375</threshold>
            <left_val>-0.3231683969497681</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 8 4 5 -1.</_>
                <_>
                  14 9 2 5 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0162766892462969</threshold>
            <left_val>0.6018949747085571</left_val>
            <right_val>-0.2405951023101807</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 11 4 9 -1.</_>
                <_>
                  2 11 2 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.8211208991706371e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4718115031719208</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 9 2 6 -1.</_>
                <_>
                  6 11 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0142911802977324</threshold>
            <left_val>0.5128008723258972</left_val>
            <right_val>-0.1074400022625923</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 18 4 2 -1.</_>
                <_>
                  12 19 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0120410006493330e-03</threshold>
            <left_val>-0.3884469866752625</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 13 6 2 -1.</_>
                <_>
                  16 13 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.9822672046720982e-03</threshold>
            <left_val>0.4692885875701904</left_val>
            <right_val>-0.0913559198379517</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 9 1 10 -1.</_>
                <_>
                  19 9 1 5 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-2.4705699179321527e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4596441090106964</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 5 4 4 -1.</_>
                <_>
                  12 5 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.4079859722405672e-03</threshold>
            <left_val>0.2183067053556442</left_val>
            <right_val>-0.5937340259552002</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 12 3 5 -1.</_>
                <_>
                  15 12 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4312269631773233e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.2473167032003403</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 0 2 6 -1.</_>
                <_>
                  18 0 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.9141810955479741e-04</threshold>
            <left_val>-0.2597224116325378</left_val>
            <right_val>0.3820636868476868</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 16 3 3 -1.</_>
                <_>
                  14 16 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.2818811014294624e-03</threshold>
            <left_val>-0.7718012928962708</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 0 1 4 -1.</_>
                <_>
                  19 2 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0365940397605300e-03</threshold>
            <left_val>0.2356985956430435</left_val>
            <right_val>-0.2206770032644272</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 13 4 2 -1.</_>
                <_>
                  7 13 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2078400943428278e-03</threshold>
            <left_val>0.3088611960411072</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 11 3 3 -1.</_>
                <_>
                  10 11 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.5239339340478182e-03</threshold>
            <left_val>-0.2849600017070770</left_val>
            <right_val>0.4754430055618286</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 15 2 3 -1.</_>
                <_>
                  13 16 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.1774807982146740e-03</threshold>
            <left_val>-0.7031838297843933</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 7 3 4 -1.</_>
                <_>
                  12 7 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.2023619860410690e-03</threshold>
            <left_val>-0.5136131048202515</left_val>
            <right_val>0.1565625965595245</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 12 1 3 -1.</_>
                <_>
                  4 13 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-8.7003601947799325e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.2992512881755829</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 11 6 2 -1.</_>
                <_>
                  1 11 3 1 2.</_>
                <_>
                  4 12 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.8079950027167797e-03</threshold>
            <left_val>0.5521563887596130</left_val>
            <right_val>-8.0608041025698185e-04</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 7 2 3 -1.</_>
                <_>
                  4 8 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.9994210712611675e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4354174137115479</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 12 2 2 -1.</_>
                <_>
                  5 12 1 1 2.</_>
                <_>
                  6 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0323170572519302e-03</threshold>
            <left_val>0.5499215126037598</left_val>
            <right_val>-5.0770761445164680e-03</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 8 4 3 -1.</_>
                <_>
                  8 9 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.9215619005262852e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.3390001058578491</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 8 5 3 -1.</_>
                <_>
                  7 9 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.1578325480222702e-03</threshold>
            <left_val>0.3435488939285278</left_val>
            <right_val>-0.2448388934135437</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 19 4 1 -1.</_>
                <_>
                  7 19 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6159559600055218e-03</threshold>
            <left_val>-0.7465370297431946</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 0 4 4 -1.</_>
                <_>
                  6 0 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.7165839932858944e-03</threshold>
            <left_val>0.1185505986213684</left_val>
            <right_val>-0.7180386781692505</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 16 8 -1.</_>
                <_>
                  8 0 8 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0160931199789047</threshold>
            <left_node>1</left_node>
            <right_val>-0.3298721015453339</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 11 3 4 -1.</_>
                <_>
                  11 12 3 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.9861610643565655e-03</threshold>
            <left_val>0.3126398026943207</left_val>
            <right_val>-0.2319402992725372</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 4 20 6 -1.</_>
                <_>
                  5 4 10 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0641226172447205</threshold>
            <left_node>1</left_node>
            <right_val>0.4623914957046509</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 2 2 4 -1.</_>
                <_>
                  13 2 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0215181596577168</threshold>
            <left_val>-0.2427732050418854</left_val>
            <right_val>0.4096390902996063</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 5 14 15 -1.</_>
                <_>
                  7 5 7 15 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.2854138016700745</threshold>
            <left_val>0.4452179968357086</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 18 3 2 -1.</_>
                <_>
                  1 19 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.7372559998184443e-04</threshold>
            <left_val>-0.4730761051177979</left_val>
            <right_val>0.0767397210001945</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 6 3 3 -1.</_>
                <_>
                  2 7 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.4039281569421291e-03</threshold>
            <left_val>-0.5616778731346130</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 1 6 8 -1.</_>
                <_>
                  0 1 3 4 2.</_>
                <_>
                  3 5 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0142796700820327</threshold>
            <left_val>-0.0673118904232979</left_val>
            <right_val>0.4380675852298737</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 6 6 -1.</_>
                <_>
                  7 0 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0131798600777984</threshold>
            <left_val>-0.6767266988754272</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 1 15 8 -1.</_>
                <_>
                  1 3 15 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0668280720710754</threshold>
            <left_val>-0.0321829095482826</left_val>
            <right_val>0.5130872130393982</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 16 1 -1.</_>
                <_>
                  8 0 8 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.3021448440849781e-03</threshold>
            <left_val>-0.2008266001939774</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 0 1 2 -1.</_>
                <_>
                  3 0 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.6806010389700532e-03</threshold>
            <left_val>-0.5176724195480347</left_val>
            <right_val>0.3857651054859161</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 13 4 1 -1.</_>
                <_>
                  4 13 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5057720011100173e-03</threshold>
            <left_val>0.3935809135437012</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 11 2 2 -1.</_>
                <_>
                  4 11 1 1 2.</_>
                <_>
                  5 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1699240421876311e-03</threshold>
            <left_val>-0.2557956874370575</left_val>
            <right_val>0.3192729949951172</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 2 3 3 -1.</_>
                <_>
                  18 3 1 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.2735180146992207e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7166724205017090</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 3 2 1 -1.</_>
                <_>
                  17 3 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.8693883551750332e-05</threshold>
            <left_val>-0.1890882998704910</left_val>
            <right_val>0.2384908050298691</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 11 3 2 -1.</_>
                <_>
                  0 12 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.9624589476734400e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5158377289772034</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 11 4 2 -1.</_>
                <_>
                  4 11 2 1 2.</_>
                <_>
                  6 12 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.1472831033170223e-03</threshold>
            <left_val>0.4803304970264435</left_val>
            <right_val>-0.0362379103899002</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 0 4 11 -1.</_>
                <_>
                  11 0 2 11 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0133569166064262e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5272933840751648</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 15 2 3 -1.</_>
                <_>
                  17 16 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.5994369797408581e-03</threshold>
            <left_val>-0.6940053105354309</left_val>
            <right_val>0.1227589026093483</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 11 8 1 -1.</_>
                <_>
                  2 11 4 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0427003614604473</threshold>
            <left_val>-0.6821854710578918</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 13 1 6 -1.</_>
                <_>
                  17 13 1 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-3.5096149076707661e-05</threshold>
            <left_val>0.1216031014919281</left_val>
            <right_val>-0.4214228987693787</right_val></_></_></trees>
      <stage_threshold>-1.9795049428939819</stage_threshold>
      <parent>12</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 14 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 13 6 2 -1.</_>
                <_>
                  13 13 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.7128365412354469e-03</threshold>
            <left_val>-0.4404883980751038</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 0 1 10 -1.</_>
                <_>
                  19 5 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.0675927884876728e-03</threshold>
            <left_val>0.6003010272979736</left_val>
            <right_val>-0.2604264914989471</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 8 7 9 -1.</_>
                <_>
                  2 11 7 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0839333981275558</threshold>
            <left_node>1</left_node>
            <right_val>-0.3794398903846741</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 11 20 2 -1.</_>
                <_>
                  5 11 10 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0226261802017689</threshold>
            <left_val>0.5252948999404907</left_val>
            <right_val>-0.3273332118988037</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 14 6 1 -1.</_>
                <_>
                  8 14 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.5725389607250690e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.2603093981742859</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 3 8 7 -1.</_>
                <_>
                  12 3 4 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6297569964081049e-03</threshold>
            <left_val>0.4843423068523407</left_val>
            <right_val>-0.3836326897144318</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 5 9 -1.</_>
                <_>
                  7 3 5 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0800115764141083</threshold>
            <left_val>0.3957956135272980</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 16 6 -1.</_>
                <_>
                  0 2 16 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0960614532232285</threshold>
            <left_val>0.4287418127059937</left_val>
            <right_val>-0.2909663915634155</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 10 2 6 -1.</_>
                <_>
                  4 12 2 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-9.3183852732181549e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3932549953460693</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 0 4 14 -1.</_>
                <_>
                  18 0 2 14 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.2205153778195381e-03</threshold>
            <left_val>-0.2985737919807434</left_val>
            <right_val>0.3173330128192902</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 9 6 -1.</_>
                <_>
                  6 2 9 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0232087504118681</threshold>
            <left_node>1</left_node>
            <right_val>0.3929522931575775</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 18 12 2 -1.</_>
                <_>
                  8 19 12 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.6389730153605342e-03</threshold>
            <left_val>-0.5403599739074707</left_val>
            <right_val>-0.0218368805944920</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 17 4 3 -1.</_>
                <_>
                  11 17 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.8872499242424965e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7817273736000061</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 0 1 4 -1.</_>
                <_>
                  4 1 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.7465260140597820e-03</threshold>
            <left_val>0.1447418928146362</left_val>
            <right_val>-0.6423770189285278</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 6 2 2 -1.</_>
                <_>
                  18 6 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.7432148605585098e-03</threshold>
            <left_val>-0.6555628776550293</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 10 3 4 -1.</_>
                <_>
                  11 11 3 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-8.5324952378869057e-03</threshold>
            <left_val>0.2209030985832214</left_val>
            <right_val>-0.2579030096530914</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 9 4 3 -1.</_>
                <_>
                  9 10 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.8752172887325287e-03</threshold>
            <left_val>0.4659686088562012</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 10 4 3 -1.</_>
                <_>
                  9 11 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.7129527926445007e-03</threshold>
            <left_val>0.2527978122234344</left_val>
            <right_val>-0.2617045044898987</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 4 3 4 -1.</_>
                <_>
                  18 5 1 4 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>7.6909800991415977e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5935081839561462</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 0 2 3 -1.</_>
                <_>
                  18 1 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.6657560374587774e-03</threshold>
            <left_val>0.1696972995996475</left_val>
            <right_val>-0.5412395000457764</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 1 2 2 -1.</_>
                <_>
                  18 2 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.4685939792543650e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.3038387000560760</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 1 1 3 -1.</_>
                <_>
                  19 2 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5998890157788992e-03</threshold>
            <left_val>-0.5481774806976318</left_val>
            <right_val>0.2497155964374542</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 18 4 2 -1.</_>
                <_>
                  9 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.9368670182302594e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6320034861564636</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 13 4 2 -1.</_>
                <_>
                  2 13 2 1 2.</_>
                <_>
                  4 14 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4878541007637978e-03</threshold>
            <left_val>0.4705137908458710</left_val>
            <right_val>-0.0451872199773788</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 11 4 2 -1.</_>
                <_>
                  3 11 2 1 2.</_>
                <_>
                  5 12 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.8134910389780998e-03</threshold>
            <left_val>0.3927085101604462</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 10 4 2 -1.</_>
                <_>
                  2 10 2 1 2.</_>
                <_>
                  4 11 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4107710449025035e-03</threshold>
            <left_val>0.1801708042621613</left_val>
            <right_val>-0.2571457922458649</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 9 2 3 -1.</_>
                <_>
                  4 10 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.9013070315122604e-03</threshold>
            <left_val>-0.5338624119758606</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 10 4 6 -1.</_>
                <_>
                  3 10 2 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.1458620429039001e-03</threshold>
            <left_val>0.2817435860633850</left_val>
            <right_val>-0.1608024984598160</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 0 6 8 -1.</_>
                <_>
                  16 0 3 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.2800445854663849e-03</threshold>
            <left_val>-0.3002896010875702</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 0 8 9 -1.</_>
                <_>
                  12 0 4 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0412813015282154</threshold>
            <left_val>-0.6240906715393066</left_val>
            <right_val>0.2054990977048874</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 11 8 1 -1.</_>
                <_>
                  1 11 4 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0356253609061241</threshold>
            <left_val>-0.5252934098243713</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 0 1 3 -1.</_>
                <_>
                  2 1 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.1647539474070072e-03</threshold>
            <left_val>-0.6353800892829895</left_val>
            <right_val>0.1284665018320084</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 13 2 2 -1.</_>
                <_>
                  14 13 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.5598259940743446e-04</threshold>
            <left_val>0.2650550901889801</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 12 3 4 -1.</_>
                <_>
                  5 12 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.9347851462662220e-04</threshold>
            <left_val>0.1826681047677994</left_val>
            <right_val>-0.3753179013729095</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 17 4 3 -1.</_>
                <_>
                  7 17 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5431478861719370e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6105722188949585</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 1 2 6 -1.</_>
                <_>
                  14 1 2 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0158538892865181</threshold>
            <left_val>0.3075476884841919</left_val>
            <right_val>-0.0981439203023911</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 4 8 4 -1.</_>
                <_>
                  8 6 8 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0413157604634762</threshold>
            <left_val>0.4924758970737457</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 3 4 5 -1.</_>
                <_>
                  10 3 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.8226549774408340e-04</threshold>
            <left_val>0.0629759430885315</left_val>
            <right_val>-0.4263429939746857</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 12 2 2 -1.</_>
                <_>
                  13 12 1 1 2.</_>
                <_>
                  14 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.3098431564867496e-04</threshold>
            <left_node>1</left_node>
            <right_val>0.3139733970165253</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 12 3 3 -1.</_>
                <_>
                  7 12 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.8946860693395138e-03</threshold>
            <left_val>0.2859097123146057</left_val>
            <right_val>-0.2562322914600372</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 7 3 3 -1.</_>
                <_>
                  4 8 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0102441404014826</threshold>
            <left_val>-0.6973748207092285</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 10 5 4 -1.</_>
                <_>
                  15 11 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0169798508286476</threshold>
            <left_val>-0.7312573194503784</left_val>
            <right_val>0.1038917973637581</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 8 4 9 -1.</_>
                <_>
                  14 11 4 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0198569446802139e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3507063984870911</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 9 4 3 -1.</_>
                <_>
                  16 10 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.0688778758049011e-03</threshold>
            <left_val>-0.5339580774307251</left_val>
            <right_val>0.1733485013246536</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 7 2 13 -1.</_>
                <_>
                  19 7 1 13 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.6911415457725525e-03</threshold>
            <left_val>0.5639979839324951</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 16 1 -1.</_>
                <_>
                  8 0 8 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.5460003465414047e-03</threshold>
            <left_val>-0.2471649050712585</left_val>
            <right_val>0.1821652054786682</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 11 5 4 -1.</_>
                <_>
                  11 12 5 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.9479231238365173e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.2833398878574371</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 13 2 4 -1.</_>
                <_>
                  18 13 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.9269150216132402e-03</threshold>
            <left_val>-0.0681960731744766</left_val>
            <right_val>0.3778719902038574</right_val></_></_></trees>
      <stage_threshold>-1.9048260450363159</stage_threshold>
      <parent>13</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 15 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 13 9 2 -1.</_>
                <_>
                  9 13 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0286398194730282</threshold>
            <left_node>1</left_node>
            <right_val>-0.3771826028823853</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 8 6 8 -1.</_>
                <_>
                  3 10 6 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0421766601502895</threshold>
            <left_val>0.7229869961738586</left_val>
            <right_val>-0.0761411637067795</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 12 4 3 -1.</_>
                <_>
                  15 12 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2537210024893284e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3272745907306671</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 6 6 4 -1.</_>
                <_>
                  14 8 2 4 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0306833293288946</threshold>
            <left_val>0.5150523781776428</left_val>
            <right_val>-0.2223519980907440</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 12 6 -1.</_>
                <_>
                  4 3 12 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1234126985073090</threshold>
            <left_val>0.4469901025295258</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 17 2 -1.</_>
                <_>
                  0 1 17 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0236741509288549</threshold>
            <left_val>0.3470853865146637</left_val>
            <right_val>-0.3177390098571777</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 14 1 6 -1.</_>
                <_>
                  2 17 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.1951239798218012e-03</threshold>
            <left_val>-0.4977504909038544</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 10 3 3 -1.</_>
                <_>
                  2 11 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-1.4915530337020755e-03</threshold>
            <left_val>0.2638441920280457</left_val>
            <right_val>-0.3891254961490631</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 2 2 9 -1.</_>
                <_>
                  19 2 1 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.8097527623176575e-04</threshold>
            <left_val>-0.4093979001045227</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 9 13 8 -1.</_>
                <_>
                  7 11 13 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0583557710051537</threshold>
            <left_val>0.3228761851787567</left_val>
            <right_val>-0.2304559946060181</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 6 3 4 -1.</_>
                <_>
                  18 7 1 4 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>5.1132370717823505e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5135368108749390</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 13 2 2 -1.</_>
                <_>
                  7 13 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.5418320223689079e-03</threshold>
            <left_val>0.5301175713539124</left_val>
            <right_val>-0.0306493304669857</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 16 1 3 -1.</_>
                <_>
                  14 17 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.6811339883133769e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5316147208213806</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 16 6 4 -1.</_>
                <_>
                  11 16 3 2 2.</_>
                <_>
                  14 18 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.8129699639976025e-03</threshold>
            <left_val>-0.0675240531563759</left_val>
            <right_val>0.3854224979877472</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 0 1 4 -1.</_>
                <_>
                  19 1 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.1835418883711100e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6429883241653442</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 0 1 2 -1.</_>
                <_>
                  19 1 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4335379712283611e-03</threshold>
            <left_val>-0.6631330847740173</left_val>
            <right_val>0.1388237029314041</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 3 3 6 -1.</_>
                <_>
                  13 3 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.0736608896404505e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6343315839767456</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 10 4 3 -1.</_>
                <_>
                  8 11 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.6425544470548630e-03</threshold>
            <left_val>0.3869616091251373</left_val>
            <right_val>-0.0687377974390984</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 0 1 8 -1.</_>
                <_>
                  19 4 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.2082108817994595e-03</threshold>
            <left_val>0.1612125039100647</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 0 6 6 -1.</_>
                <_>
                  14 0 3 3 2.</_>
                <_>
                  17 3 3 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.0191977322101593e-03</threshold>
            <left_val>0.3801113069057465</left_val>
            <right_val>-0.4139797985553741</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 11 3 3 -1.</_>
                <_>
                  9 12 1 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.2479159571230412e-03</threshold>
            <left_val>0.2435187995433807</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 6 10 12 -1.</_>
                <_>
                  6 6 5 12 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.2263164073228836</threshold>
            <left_val>0.6066794991493225</left_val>
            <right_val>-0.2252188026905060</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 6 2 1 -1.</_>
                <_>
                  11 6 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0091613451950252e-05</threshold>
            <left_val>0.1711532026529312</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 1 7 10 -1.</_>
                <_>
                  8 6 7 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1816139966249466</threshold>
            <left_val>0.5272598266601562</left_val>
            <right_val>-0.3524754047393799</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 11 3 3 -1.</_>
                <_>
                  14 12 1 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-9.4038434326648712e-03</threshold>
            <left_val>0.3497051894664764</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 13 4 4 -1.</_>
                <_>
                  10 13 2 2 2.</_>
                <_>
                  12 15 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.1289030555635691e-03</threshold>
            <left_val>0.0558786988258362</left_val>
            <right_val>-0.4981659054756165</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 15 2 3 -1.</_>
                <_>
                  14 16 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.1798550412058830e-03</threshold>
            <left_val>-0.6309564113616943</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 13 3 1 -1.</_>
                <_>
                  14 13 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.5030192490667105e-04</threshold>
            <left_val>0.3585645854473114</left_val>
            <right_val>-0.0782810524106026</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 4 6 3 -1.</_>
                <_>
                  12 4 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0105559304356575</threshold>
            <left_val>-0.5550283193588257</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 7 6 4 -1.</_>
                <_>
                  1 7 3 2 2.</_>
                <_>
                  4 9 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.1852981559932232e-03</threshold>
            <left_val>0.3554868102073669</left_val>
            <right_val>-0.0688922926783562</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 7 4 2 -1.</_>
                <_>
                  16 8 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.8725479543209076e-03</threshold>
            <left_val>-0.4859617948532104</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 4 9 6 -1.</_>
                <_>
                  13 4 3 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.5342970192432404e-03</threshold>
            <left_val>0.2117895931005478</left_val>
            <right_val>-0.2317408025264740</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 2 6 2 -1.</_>
                <_>
                  14 2 6 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0139099201187491</threshold>
            <left_val>0.5993698239326477</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 18 4 2 -1.</_>
                <_>
                  6 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.5418450348079205e-03</threshold>
            <left_val>-9.5086917281150818e-03</left_val>
            <right_val>-0.6479613184928894</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 2 8 -1.</_>
                <_>
                  1 12 1 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.1549900518730283e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.2750172019004822</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 19 18 1 -1.</_>
                <_>
                  10 19 9 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0326870307326317</threshold>
            <left_val>-0.6733620762825012</left_val>
            <right_val>0.1952040046453476</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 12 20 -1.</_>
                <_>
                  8 0 6 20 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.2642259001731873</threshold>
            <left_val>0.3698686957359314</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 0 14 1 -1.</_>
                <_>
                  9 0 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.9438670761883259e-03</threshold>
            <left_val>-0.3002974092960358</left_val>
            <right_val>0.1499896943569183</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 9 8 3 -1.</_>
                <_>
                  7 10 8 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0120779201388359</threshold>
            <left_val>0.4164412915706635</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 11 2 2 -1.</_>
                <_>
                  3 11 1 1 2.</_>
                <_>
                  4 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3986700214445591e-03</threshold>
            <left_val>0.4124872982501984</left_val>
            <right_val>-0.1953365951776505</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 0 9 2 -1.</_>
                <_>
                  14 0 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0131383398547769</threshold>
            <left_node>1</left_node>
            <right_val>-0.6420493125915527</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 0 9 1 -1.</_>
                <_>
                  9 0 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.2417110204696655e-03</threshold>
            <left_val>0.1135936006903648</left_val>
            <right_val>-0.7383887171745300</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 8 1 4 -1.</_>
                <_>
                  3 9 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.4837901629507542e-03</threshold>
            <left_val>-0.6924629807472229</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 9 3 3 -1.</_>
                <_>
                  0 10 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.8022231571376324e-03</threshold>
            <left_val>0.0928734391927719</left_val>
            <right_val>-0.6004747152328491</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 4 15 12 -1.</_>
                <_>
                  8 8 5 4 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.4532290995121002</threshold>
            <left_node>1</left_node>
            <right_val>0.5626053214073181</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 13 6 6 -1.</_>
                <_>
                  9 13 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.5721630342304707e-03</threshold>
            <left_val>0.0778201594948769</left_val>
            <right_val>-0.3399060070514679</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 1 12 6 -1.</_>
                <_>
                  2 3 12 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0315839610993862</threshold>
            <left_node>1</left_node>
            <right_val>0.3229267001152039</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 1 6 1 -1.</_>
                <_>
                  3 3 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.7926177978515625e-03</threshold>
            <left_val>0.1553445011377335</left_val>
            <right_val>-0.3571783900260925</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 4 5 3 -1.</_>
                <_>
                  2 5 5 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.6025379821658134e-03</threshold>
            <left_val>-0.5185949802398682</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 12 2 2 -1.</_>
                <_>
                  2 12 1 1 2.</_>
                <_>
                  3 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.5151038840413094e-04</threshold>
            <left_val>-0.0295706707984209</left_val>
            <right_val>0.4602751135826111</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 11 3 3 -1.</_>
                <_>
                  9 11 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.9723300356417894e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.3692665100097656</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 11 3 4 -1.</_>
                <_>
                  10 11 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.3158260155469179e-03</threshold>
            <left_val>-0.2129974067211151</left_val>
            <right_val>0.2694854140281677</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 2 3 1 -1.</_>
                <_>
                  18 3 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.1179600153118372e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4836950004100800</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 11 6 3 -1.</_>
                <_>
                  8 11 3 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6946600992232561e-03</threshold>
            <left_val>0.1854566037654877</left_val>
            <right_val>-0.2941196858882904</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 12 12 8 -1.</_>
                <_>
                  2 12 6 4 2.</_>
                <_>
                  8 16 6 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0588654093444347</threshold>
            <left_node>1</left_node>
            <right_val>-0.4677037894725800</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 15 2 3 -1.</_>
                <_>
                  12 16 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.8408921360969543e-03</threshold>
            <left_val>-0.6637132167816162</left_val>
            <right_val>0.1272134929895401</right_val></_></_></trees>
      <stage_threshold>-1.9407349824905396</stage_threshold>
      <parent>14</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 16 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 14 9 1 -1.</_>
                <_>
                  8 14 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0127664897590876</threshold>
            <left_node>1</left_node>
            <right_val>-0.3796809911727905</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 13 4 6 -1.</_>
                <_>
                  13 13 2 3 2.</_>
                <_>
                  15 16 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.7821640726178885e-03</threshold>
            <left_val>-0.1600182950496674</left_val>
            <right_val>0.6195328831672668</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 7 9 1 -1.</_>
                <_>
                  11 10 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0330498814582825</threshold>
            <left_node>1</left_node>
            <right_val>-0.3682548105716705</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 0 4 4 -1.</_>
                <_>
                  16 0 4 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0450502410531044</threshold>
            <left_val>9.3770343810319901e-03</left_val>
            <right_val>0.7157058119773865</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 13 2 2 -1.</_>
                <_>
                  2 13 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-3.5275409463793039e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3733660876750946</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 12 2 2 -1.</_>
                <_>
                  5 13 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.2250709589570761e-03</threshold>
            <left_val>-0.0667124912142754</left_val>
            <right_val>0.4990611970424652</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 2 4 -1.</_>
                <_>
                  0 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.3609490124508739e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.1716292947530746</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 8 14 11 -1.</_>
                <_>
                  7 8 7 11 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.2908785939216614</threshold>
            <left_val>0.3615890145301819</left_val>
            <right_val>-0.5087137222290039</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 17 4 3 -1.</_>
                <_>
                  5 17 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.3148950897157192e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7178813815116882</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 12 3 5 -1.</_>
                <_>
                  4 12 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.8641437469050288e-04</threshold>
            <left_val>0.2571361958980560</left_val>
            <right_val>-0.1797894984483719</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 11 1 3 -1.</_>
                <_>
                  5 12 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1313590221107006e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.3538742065429688</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 10 4 2 -1.</_>
                <_>
                  4 10 2 1 2.</_>
                <_>
                  6 11 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0621800106018782e-03</threshold>
            <left_val>0.3079080879688263</left_val>
            <right_val>-0.3121724128723145</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 9 3 1 -1.</_>
                <_>
                  16 10 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.5443620979785919e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5678855180740356</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 0 16 7 -1.</_>
                <_>
                  7 0 8 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.7088878713548183e-03</threshold>
            <left_val>0.2122289985418320</left_val>
            <right_val>-0.2682110965251923</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 2 17 6 -1.</_>
                <_>
                  2 5 17 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1644680947065353</threshold>
            <left_val>0.4901696145534515</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 4 14 6 -1.</_>
                <_>
                  2 6 14 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0408281087875366</threshold>
            <left_val>-0.3121747076511383</left_val>
            <right_val>0.2474814951419830</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 9 6 2 -1.</_>
                <_>
                  2 9 3 1 2.</_>
                <_>
                  5 10 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.6051510833203793e-03</threshold>
            <left_val>0.3435586094856262</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 11 4 2 -1.</_>
                <_>
                  3 11 2 1 2.</_>
                <_>
                  5 12 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.3608640767633915e-03</threshold>
            <left_val>0.2656646072864532</left_val>
            <right_val>-0.2864471971988678</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 13 4 2 -1.</_>
                <_>
                  18 13 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.2965350179001689e-03</threshold>
            <left_val>-0.2931776046752930</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 7 3 2 -1.</_>
                <_>
                  16 8 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>6.0111000202596188e-03</threshold>
            <left_val>0.2194170057773590</left_val>
            <right_val>-0.6001421809196472</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 11 4 2 -1.</_>
                <_>
                  0 12 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.1628420371562243e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.3129233121871948</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 9 2 3 -1.</_>
                <_>
                  3 10 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.0573718938976526e-03</threshold>
            <left_val>0.2876316905021667</left_val>
            <right_val>-0.3732070922851562</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 18 6 2 -1.</_>
                <_>
                  5 18 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.7166007831692696e-03</threshold>
            <left_val>-0.7168325185775757</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 12 3 2 -1.</_>
                <_>
                  12 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.8222459368407726e-03</threshold>
            <left_val>0.4250183105468750</left_val>
            <right_val>-0.0532948896288872</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 0 1 2 -1.</_>
                <_>
                  19 1 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.3861207056324929e-05</threshold>
            <left_val>0.1490345001220703</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 14 1 -1.</_>
                <_>
                  7 0 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.8680498041212559e-03</threshold>
            <left_val>-0.5843665003776550</left_val>
            <right_val>0.1072475984692574</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 10 3 4 -1.</_>
                <_>
                  10 11 3 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.9013723880052567e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3431994915008545</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 16 1 3 -1.</_>
                <_>
                  13 17 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.7825690340250731e-03</threshold>
            <left_val>0.1765536069869995</left_val>
            <right_val>-0.6147375702857971</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 1 2 4 -1.</_>
                <_>
                  19 1 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.2751538674347103e-04</threshold>
            <left_val>-0.3383756875991821</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 13 5 6 -1.</_>
                <_>
                  15 15 5 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0307008996605873</threshold>
            <left_val>0.1856613010168076</left_val>
            <right_val>-0.5345026850700378</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 4 3 3 -1.</_>
                <_>
                  17 5 1 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>5.6932470761239529e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5175045132637024</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 6 16 14 -1.</_>
                <_>
                  12 6 8 14 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2137514054775238</threshold>
            <left_val>0.1233239993453026</left_val>
            <right_val>-0.6428813934326172</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 12 3 1 -1.</_>
                <_>
                  11 12 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.4024959206581116e-03</threshold>
            <left_val>0.5853567719459534</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 12 2 2 -1.</_>
                <_>
                  5 12 1 1 2.</_>
                <_>
                  6 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.5719969784840941e-04</threshold>
            <left_val>0.2336882054805756</left_val>
            <right_val>-0.1903900951147079</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 3 4 5 -1.</_>
                <_>
                  10 3 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.2587839998304844e-03</threshold>
            <left_val>-0.5119084715843201</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 1 2 3 -1.</_>
                <_>
                  18 2 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.3462621029466391e-03</threshold>
            <left_val>-0.4716477096080780</left_val>
            <right_val>0.1478340029716492</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 17 1 2 -1.</_>
                <_>
                  19 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.5065571106970310e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.2988634109497070</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 16 2 2 -1.</_>
                <_>
                  17 16 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.5082160979509354e-03</threshold>
            <left_val>-0.4850896000862122</left_val>
            <right_val>0.2001491039991379</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 2 7 6 -1.</_>
                <_>
                  10 4 7 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0189427901059389</threshold>
            <left_node>1</left_node>
            <right_val>0.3102895021438599</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 0 13 4 -1.</_>
                <_>
                  2 1 13 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.9123771972954273e-03</threshold>
            <left_val>-0.2870123982429504</left_val>
            <right_val>0.2053406983613968</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 2 2 -1.</_>
                <_>
                  2 0 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>8.1696882843971252e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.4581083059310913</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 3 6 8 -1.</_>
                <_>
                  3 3 3 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0100697698071599</threshold>
            <left_val>-0.2417591959238052</left_val>
            <right_val>0.1759382039308548</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 0 1 3 -1.</_>
                <_>
                  2 1 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.1663580555468798e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4987790882587433</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 0 6 9 -1.</_>
                <_>
                  10 0 2 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0105057302862406</threshold>
            <left_val>0.1623128056526184</left_val>
            <right_val>-0.4298886954784393</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 9 3 2 -1.</_>
                <_>
                  18 10 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>5.7576788822188973e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.3101257085800171</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 8 4 6 -1.</_>
                <_>
                  16 10 4 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0306088998913765</threshold>
            <left_val>-0.7406430244445801</left_val>
            <right_val>0.1621717959642410</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 9 7 3 -1.</_>
                <_>
                  6 10 7 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0134306596592069</threshold>
            <left_val>0.4550563991069794</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 10 3 4 -1.</_>
                <_>
                  2 11 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1859040241688490e-03</threshold>
            <left_val>-0.2722725868225098</left_val>
            <right_val>0.2247501015663147</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 8 1 6 -1.</_>
                <_>
                  15 8 1 3 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.9311347538605332e-04</threshold>
            <left_val>-0.3959831893444061</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 3 1 12 -1.</_>
                <_>
                  19 7 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4509918875992298e-03</threshold>
            <left_val>0.2500421106815338</left_val>
            <right_val>-0.1614051014184952</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 5 2 -1.</_>
                <_>
                  2 0 5 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0136419497430325</threshold>
            <left_node>1</left_node>
            <right_val>-0.6452549099922180</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 3 11 6 -1.</_>
                <_>
                  1 5 11 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0367333292961121</threshold>
            <left_val>0.3419705927371979</left_val>
            <right_val>-0.0659683272242546</right_val></_></_></trees>
      <stage_threshold>-1.8931059837341309</stage_threshold>
      <parent>15</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 17 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 13 2 4 -1.</_>
                <_>
                  14 13 1 2 2.</_>
                <_>
                  15 15 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.3613830087706447e-03</threshold>
            <left_val>-0.3438392877578735</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 11 10 3 -1.</_>
                <_>
                  13 11 5 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0122110601514578</threshold>
            <left_val>-0.4035860002040863</left_val>
            <right_val>0.5787363052368164</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 11 1 4 -1.</_>
                <_>
                  6 13 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.2929528970271349e-03</threshold>
            <left_val>-0.2216434925794601</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 9 3 9 -1.</_>
                <_>
                  3 12 1 3 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0248319804668427</threshold>
            <left_val>0.5425691008567810</left_val>
            <right_val>-0.4758560061454773</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 15 9 -1.</_>
                <_>
                  9 3 5 3 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.3408153057098389</threshold>
            <left_val>0.5343874096870422</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 0 6 4 -1.</_>
                <_>
                  12 0 6 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0609296411275864</threshold>
            <left_val>-0.2601535916328430</left_val>
            <right_val>0.3762655854225159</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 5 4 5 -1.</_>
                <_>
                  12 5 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4399300562217832e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4163514971733093</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 7 18 12 -1.</_>
                <_>
                  7 11 6 4 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.7571117877960205</threshold>
            <left_val>0.4776453971862793</left_val>
            <right_val>-0.1237422972917557</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 12 6 4 -1.</_>
                <_>
                  16 12 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.9891431592404842e-03</threshold>
            <left_val>0.2184862047433853</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 12 3 3 -1.</_>
                <_>
                  14 12 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.9398561976850033e-04</threshold>
            <left_val>0.1772602945566177</left_val>
            <right_val>-0.5481501817703247</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 9 4 1 -1.</_>
                <_>
                  15 10 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.9013510793447495e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5670918226242065</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 7 3 2 -1.</_>
                <_>
                  18 8 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.4361278414726257e-03</threshold>
            <left_val>0.1418378055095673</left_val>
            <right_val>-0.5878441929817200</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 3 1 2 -1.</_>
                <_>
                  19 4 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.3319290600484237e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.3482188880443573</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 1 1 4 -1.</_>
                <_>
                  19 2 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5481029879301786e-03</threshold>
            <left_val>0.1974532008171082</left_val>
            <right_val>-0.5597922205924988</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 2 12 8 -1.</_>
                <_>
                  3 4 12 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0748829394578934</threshold>
            <left_node>1</left_node>
            <right_val>0.4664795100688934</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 0 16 6 -1.</_>
                <_>
                  1 2 16 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0488163083791733</threshold>
            <left_val>-0.2257521003484726</left_val>
            <right_val>0.3232581913471222</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 8 3 1 -1.</_>
                <_>
                  17 9 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-3.9128339849412441e-03</threshold>
            <left_val>-0.5977287292480469</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 13 6 3 -1.</_>
                <_>
                  9 14 2 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0138206295669079</threshold>
            <left_val>0.2603121101856232</left_val>
            <right_val>-0.2021141052246094</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 18 6 2 -1.</_>
                <_>
                  11 19 6 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.4047200400382280e-04</threshold>
            <left_val>-0.3400524854660034</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 17 5 3 -1.</_>
                <_>
                  15 18 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.6419431455433369e-03</threshold>
            <left_val>-0.4518780112266541</left_val>
            <right_val>0.2105485945940018</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 1 18 4 -1.</_>
                <_>
                  8 1 6 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0319609418511391</threshold>
            <left_node>1</left_node>
            <right_val>-0.2082601934671402</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 0 1 2 -1.</_>
                <_>
                  5 1 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2651160068344325e-04</threshold>
            <left_val>0.3855319023132324</left_val>
            <right_val>-0.2311642020940781</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 11 6 6 -1.</_>
                <_>
                  3 13 2 2 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0504137091338634</threshold>
            <left_val>0.2284615933895111</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 12 4 2 -1.</_>
                <_>
                  3 12 2 1 2.</_>
                <_>
                  5 13 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.0950778853148222e-03</threshold>
            <left_val>0.3263955116271973</left_val>
            <right_val>-0.3438543081283569</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 0 3 3 -1.</_>
                <_>
                  2 1 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0110178804025054</threshold>
            <left_val>-0.7738878130912781</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 10 3 3 -1.</_>
                <_>
                  9 11 1 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.7415763884782791e-03</threshold>
            <left_val>0.3673199117183685</left_val>
            <right_val>-0.0657460018992424</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 2 2 -1.</_>
                <_>
                  0 17 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.3386680519906804e-05</threshold>
            <left_val>-0.3557175099849701</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 16 4 3 -1.</_>
                <_>
                  0 17 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.9820311143994331e-03</threshold>
            <left_val>0.1765311956405640</left_val>
            <right_val>-0.4611007869243622</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 13 12 1 -1.</_>
                <_>
                  6 13 6 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.9558269996196032e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3617269098758698</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 2 6 9 -1.</_>
                <_>
                  15 2 2 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.6739699579775333e-03</threshold>
            <left_val>0.1803857982158661</left_val>
            <right_val>-0.4045203030109406</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 11 3 3 -1.</_>
                <_>
                  9 11 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.2935381643474102e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.5208635926246643</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 11 3 4 -1.</_>
                <_>
                  10 11 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.4181300066411495e-03</threshold>
            <left_val>-0.2208580970764160</left_val>
            <right_val>0.2735756039619446</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 0 6 10 -1.</_>
                <_>
                  15 0 2 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0282630994915962</threshold>
            <left_val>-0.6383373141288757</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 10 1 4 -1.</_>
                <_>
                  3 11 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>6.3434068579226732e-04</threshold>
            <left_val>0.1563638001680374</left_val>
            <right_val>-0.3214890062808990</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 11 3 3 -1.</_>
                <_>
                  10 12 1 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.2387307882308960e-03</threshold>
            <left_val>0.2312625944614410</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 12 3 3 -1.</_>
                <_>
                  5 13 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-9.9928081035614014e-03</threshold>
            <left_val>0.3039731979370117</left_val>
            <right_val>-0.2447843998670578</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 6 2 1 -1.</_>
                <_>
                  18 6 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.4995248976629227e-05</threshold>
            <left_node>1</left_node>
            <right_val>0.1513298004865646</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 2 1 4 -1.</_>
                <_>
                  16 2 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.3049270063638687e-03</threshold>
            <left_val>0.2041787058115005</left_val>
            <right_val>-0.4626043140888214</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 5 13 4 -1.</_>
                <_>
                  2 6 13 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0166130997240543</threshold>
            <left_val>0.3339976966381073</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 4 6 2 -1.</_>
                <_>
                  14 4 6 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0116302901878953</threshold>
            <left_val>0.3705343008041382</left_val>
            <right_val>-0.1936154961585999</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 8 1 3 -1.</_>
                <_>
                  2 9 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.9068180117756128e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3810505867004395</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 7 8 3 -1.</_>
                <_>
                  7 8 8 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.6926468387246132e-03</threshold>
            <left_val>0.5064520835876465</left_val>
            <right_val>6.5170922316610813e-03</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 8 4 3 -1.</_>
                <_>
                  10 8 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2453670680988580e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.3152601122856140</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 11 3 8 -1.</_>
                <_>
                  10 15 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.5565039664506912e-03</threshold>
            <left_val>-0.5303559899330139</left_val>
            <right_val>0.2053276002407074</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 15 2 3 -1.</_>
                <_>
                  12 16 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.1540619675070047e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4592832922935486</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 12 20 -1.</_>
                <_>
                  6 0 6 20 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.3068132996559143</threshold>
            <left_val>0.5071771740913391</left_val>
            <right_val>-0.0144392503425479</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 10 1 -1.</_>
                <_>
                  5 0 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.8239809907972813e-03</threshold>
            <left_val>-0.1543793976306915</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 6 3 -1.</_>
                <_>
                  0 1 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.3063529990613461e-03</threshold>
            <left_val>-0.4357138872146606</left_val>
            <right_val>0.3934271931648254</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 13 2 2 -1.</_>
                <_>
                  14 13 1 1 2.</_>
                <_>
                  15 14 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.7848789361305535e-04</threshold>
            <left_node>1</left_node>
            <right_val>0.2521260082721710</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 10 4 2 -1.</_>
                <_>
                  12 10 2 1 2.</_>
                <_>
                  14 11 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0488630291074514e-03</threshold>
            <left_val>0.4666233956813812</left_val>
            <right_val>-0.2279223054647446</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 6 4 -1.</_>
                <_>
                  9 0 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0147243803367019</threshold>
            <left_val>-0.7860211133956909</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 10 10 -1.</_>
                <_>
                  0 0 5 5 2.</_>
                <_>
                  5 5 5 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0360623002052307</threshold>
            <left_val>-0.0685713216662407</left_val>
            <right_val>0.3669883906841278</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 3 4 2 -1.</_>
                <_>
                  7 3 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2327410988509655e-03</threshold>
            <left_val>-0.5974019765853882</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 5 4 11 -1.</_>
                <_>
                  2 5 2 11 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.8541820403188467e-04</threshold>
            <left_val>0.2027346938848495</left_val>
            <right_val>-0.1722168028354645</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 8 3 1 -1.</_>
                <_>
                  13 8 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.8553898492828012e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.4340744912624359</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 2 6 2 -1.</_>
                <_>
                  2 2 6 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0100781098008156</threshold>
            <left_val>0.1246414035558701</left_val>
            <right_val>-0.4839141964912415</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 5 7 3 -1.</_>
                <_>
                  12 6 7 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0209287907928228</threshold>
            <left_node>1</left_node>
            <right_val>0.5686420798301697</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 7 3 4 -1.</_>
                <_>
                  14 7 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.3340089935809374e-03</threshold>
            <left_val>0.0145246395841241</left_val>
            <right_val>-0.4600321054458618</right_val></_></_></trees>
      <stage_threshold>-1.9677840471267700</stage_threshold>
      <parent>16</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 18 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 12 3 2 -1.</_>
                <_>
                  8 12 3 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0153139596804976</threshold>
            <left_node>1</left_node>
            <right_val>-0.3434768915176392</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 10 4 8 -1.</_>
                <_>
                  0 12 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0142658604308963</threshold>
            <left_val>0.5820953249931335</left_val>
            <right_val>-0.3552739918231964</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 13 2 6 -1.</_>
                <_>
                  14 13 1 3 2.</_>
                <_>
                  15 16 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.2652979930862784e-03</threshold>
            <left_val>-0.3149831891059875</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  16 17 1 2 -1.</_>
                <_>
                  16 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-7.3807648732326925e-05</threshold>
            <left_val>0.4724959135055542</left_val>
            <right_val>-0.2638080120086670</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 0 3 6 -1.</_>
                <_>
                  10 2 3 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0385270304977894</threshold>
            <left_val>0.4155685007572174</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 10 14 3 -1.</_>
                <_>
                  4 11 14 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0147587703540921</threshold>
            <left_val>0.1567724943161011</left_val>
            <right_val>-0.3765023946762085</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 4 1 12 -1.</_>
                <_>
                  19 8 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5448270132765174e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3593201935291290</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 2 1 6 -1.</_>
                <_>
                  19 4 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.4564580097794533e-03</threshold>
            <left_val>0.2127663940191269</left_val>
            <right_val>-0.7228717803955078</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 12 12 3 -1.</_>
                <_>
                  14 12 6 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0102673498913646</threshold>
            <left_val>-0.4604580998420715</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 13 2 3 -1.</_>
                <_>
                  1 13 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.6422899039462209e-04</threshold>
            <left_val>0.2492025941610336</left_val>
            <right_val>-0.2672136127948761</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 4 9 -1.</_>
                <_>
                  18 0 2 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.2311889808624983e-03</threshold>
            <left_val>-0.4093919992446899</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 2 6 4 -1.</_>
                <_>
                  9 4 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0136765297502279</threshold>
            <left_val>-0.0273916907608509</left_val>
            <right_val>0.4525907039642334</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 2 3 1 -1.</_>
                <_>
                  17 3 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.2787120435386896e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.7002565264701843</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 12 3 6 -1.</_>
                <_>
                  16 12 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4256529975682497e-03</threshold>
            <left_val>0.2578780055046082</left_val>
            <right_val>-0.1509343981742859</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 12 3 3 -1.</_>
                <_>
                  14 12 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2095029707998037e-03</threshold>
            <left_val>0.3514811098575592</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 3 15 4 -1.</_>
                <_>
                  3 5 15 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0877013728022575</threshold>
            <left_val>0.4197874069213867</left_val>
            <right_val>-0.2360018044710159</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 11 3 4 -1.</_>
                <_>
                  12 11 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.8805620968341827e-03</threshold>
            <left_val>0.3047986924648285</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 11 3 3 -1.</_>
                <_>
                  11 11 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.5028509553521872e-03</threshold>
            <left_val>0.1331669986248016</left_val>
            <right_val>-0.3169130086898804</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 0 1 4 -1.</_>
                <_>
                  19 2 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.1710562547668815e-04</threshold>
            <left_node>1</left_node>
            <right_val>-0.3519909083843231</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 0 3 3 -1.</_>
                <_>
                  15 1 1 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>6.7088729701936245e-03</threshold>
            <left_val>0.2016315013170242</left_val>
            <right_val>-0.6094800829887390</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 10 8 2 -1.</_>
                <_>
                  2 10 4 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0760587528347969</threshold>
            <left_val>-0.6369420886039734</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 18 4 2 -1.</_>
                <_>
                  10 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0889140907675028e-03</threshold>
            <left_val>-0.7902534008026123</left_val>
            <right_val>0.1036607995629311</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 0 4 9 -1.</_>
                <_>
                  11 0 2 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5740528944879770e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4542419910430908</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 10 5 6 -1.</_>
                <_>
                  15 12 5 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.4877097718417645e-03</threshold>
            <left_val>0.2148129940032959</left_val>
            <right_val>-0.1932951062917709</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 13 4 2 -1.</_>
                <_>
                  3 13 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2507289648056030e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.2165144979953766</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 15 4 1 -1.</_>
                <_>
                  3 16 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.3231048621237278e-03</threshold>
            <left_val>-0.6279907822608948</left_val>
            <right_val>0.2427074015140533</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 8 3 2 -1.</_>
                <_>
                  16 9 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.3724630959331989e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5188937783241272</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 6 4 2 -1.</_>
                <_>
                  2 6 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.4632692849263549e-04</threshold>
            <left_val>-0.1137868016958237</left_val>
            <right_val>0.2822437882423401</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 17 6 1 -1.</_>
                <_>
                  12 17 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3375070411711931e-03</threshold>
            <left_val>0.2458911985158920</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 19 6 1 -1.</_>
                <_>
                  17 19 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9367550741881132e-03</threshold>
            <left_val>0.2433581948280334</left_val>
            <right_val>-0.2911281883716583</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 18 1 2 -1.</_>
                <_>
                  17 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.3193867390509695e-05</threshold>
            <left_val>-0.2580659091472626</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 16 2 2 -1.</_>
                <_>
                  17 16 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.1338938064873219e-03</threshold>
            <left_val>-0.4611040949821472</left_val>
            <right_val>0.2433398067951202</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 3 1 9 -1.</_>
                <_>
                  19 6 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.9400608986616135e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3963299095630646</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 10 3 3 -1.</_>
                <_>
                  9 11 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.6112580932676792e-03</threshold>
            <left_val>0.2450238019227982</left_val>
            <right_val>-0.1563901007175446</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 0 3 3 -1.</_>
                <_>
                  2 1 3 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.2950599454343319e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4767167866230011</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 16 2 2 -1.</_>
                <_>
                  17 16 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.5142881572246552e-03</threshold>
            <left_val>0.1069843024015427</left_val>
            <right_val>-0.9047132134437561</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 11 3 3 -1.</_>
                <_>
                  6 12 1 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>7.5297639705240726e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.4123980998992920</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 11 2 2 -1.</_>
                <_>
                  3 11 1 1 2.</_>
                <_>
                  4 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2225280515849590e-03</threshold>
            <left_val>0.2848817110061646</left_val>
            <right_val>-0.1981569975614548</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 9 2 2 -1.</_>
                <_>
                  16 9 1 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-3.4703810233622789e-03</threshold>
            <left_val>-0.4496796131134033</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 9 2 2 -1.</_>
                <_>
                  4 9 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>8.3724651485681534e-03</threshold>
            <left_val>0.1532424986362457</left_val>
            <right_val>-0.3866685032844543</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 10 2 3 -1.</_>
                <_>
                  2 11 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-3.3934618841158226e-05</threshold>
            <left_node>1</left_node>
            <right_val>-0.3142907023429871</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 0 20 20 -1.</_>
                <_>
                  0 0 10 10 2.</_>
                <_>
                  10 10 10 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.2724170982837677</threshold>
            <left_val>-0.5584210157394409</left_val>
            <right_val>0.1662781983613968</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 16 5 3 -1.</_>
                <_>
                  7 17 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.7582740876823664e-03</threshold>
            <left_val>0.2718957066535950</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 1 3 6 -1.</_>
                <_>
                  12 3 3 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0255304891616106</threshold>
            <left_val>-0.1917200982570648</left_val>
            <right_val>0.4378049969673157</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 4 7 -1.</_>
                <_>
                  7 0 2 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.2080380953848362e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4468413889408112</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  9 5 9 6 -1.</_>
                <_>
                  12 5 3 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.2151442766189575e-03</threshold>
            <left_val>0.2278670966625214</left_val>
            <right_val>-0.1744178980588913</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 18 4 2 -1.</_>
                <_>
                  6 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9405429959297180e-03</threshold>
            <left_val>-0.7264354825019836</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 7 6 8 -1.</_>
                <_>
                  9 7 2 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.4840265810489655e-03</threshold>
            <left_val>0.2079429030418396</left_val>
            <right_val>-0.1523991972208023</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 16 2 4 -1.</_>
                <_>
                  18 16 1 2 2.</_>
                <_>
                  19 18 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.2596450075507164e-03</threshold>
            <left_node>1</left_node>
            <right_val>0.6177268028259277</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 18 2 2 -1.</_>
                <_>
                  12 18 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7117479583248496e-03</threshold>
            <left_val>-0.7110661268234253</left_val>
            <right_val>-6.1875251121819019e-03</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 2 5 2 -1.</_>
                <_>
                  3 3 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3266160385683179e-03</threshold>
            <left_val>0.1718126982450485</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 1 6 4 -1.</_>
                <_>
                  7 3 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.1314306482672691e-03</threshold>
            <left_val>-0.4113875925540924</left_val>
            <right_val>0.1812427937984467</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 2 2 -1.</_>
                <_>
                  2 0 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>6.8382360041141510e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5760108232498169</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 1 16 1 -1.</_>
                <_>
                  8 1 8 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.5181988067924976e-03</threshold>
            <left_val>-0.1081907972693443</left_val>
            <right_val>0.2956142127513885</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 1 3 10 -1.</_>
                <_>
                  12 1 1 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.2788819670677185e-03</threshold>
            <left_val>-0.5811352133750916</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 0 4 4 -1.</_>
                <_>
                  5 1 2 4 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0180394705384970</threshold>
            <left_val>0.4518306851387024</left_val>
            <right_val>-0.0270830895751715</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 13 3 2 -1.</_>
                <_>
                  5 13 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0126599809154868e-03</threshold>
            <left_val>0.2434411942958832</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 11 4 3 -1.</_>
                <_>
                  7 12 4 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.7263199016451836e-03</threshold>
            <left_val>0.1687044054269791</left_val>
            <right_val>-0.2700772881507874</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 17 4 3 -1.</_>
                <_>
                  8 17 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.2334970310330391e-03</threshold>
            <left_val>-0.6004822254180908</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  5 19 2 1 -1.</_>
                <_>
                  6 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.7852200774941593e-05</threshold>
            <left_val>0.2424176931381226</left_val>
            <right_val>-0.1241324990987778</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 9 2 2 -1.</_>
                <_>
                  0 9 1 1 2.</_>
                <_>
                  1 10 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.7774722992908210e-05</threshold>
            <left_val>0.1572915017604828</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 9 2 2 -1.</_>
                <_>
                  0 9 1 1 2.</_>
                <_>
                  1 10 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.1789676439948380e-05</threshold>
            <left_val>-0.5289350748062134</left_val>
            <right_val>-0.0316655710339546</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 9 2 2 -1.</_>
                <_>
                  6 9 2 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0100242998450994</threshold>
            <left_node>1</left_node>
            <right_val>-0.4864695966243744</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 10 5 3 -1.</_>
                <_>
                  0 11 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.4298496842384338e-03</threshold>
            <left_val>0.1124086976051331</left_val>
            <right_val>-0.4257048964500427</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 10 2 2 -1.</_>
                <_>
                  3 10 1 1 2.</_>
                <_>
                  4 11 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.4433721601963043e-04</threshold>
            <left_val>0.2754076123237610</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 10 18 1 -1.</_>
                <_>
                  6 10 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0116605600342155</threshold>
            <left_val>-0.2311726063489914</left_val>
            <right_val>0.2244233042001724</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 4 3 1 -1.</_>
                <_>
                  18 5 1 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>3.9079408161342144e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6351963877677917</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 1 2 7 -1.</_>
                <_>
                  17 1 1 7 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0165501497685909</threshold>
            <left_val>0.1061910018324852</left_val>
            <right_val>-0.4765498936176300</right_val></_></_></trees>
      <stage_threshold>-1.9657919406890869</stage_threshold>
      <parent>17</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 19 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 13 9 2 -1.</_>
                <_>
                  9 13 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0184390302747488</threshold>
            <left_node>1</left_node>
            <right_val>-0.4874570965766907</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  4 9 16 6 -1.</_>
                <_>
                  4 11 16 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0533645190298557</threshold>
            <left_val>0.5103781223297119</left_val>
            <right_val>-0.2267013043165207</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 1 16 4 -1.</_>
                <_>
                  1 3 16 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0757063180208206</threshold>
            <left_val>0.4148775041103363</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 12 3 3 -1.</_>
                <_>
                  15 12 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5329009620472789e-03</threshold>
            <left_val>0.0857649371027946</left_val>
            <right_val>-0.4347091019153595</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 9 6 2 -1.</_>
                <_>
                  4 11 2 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0244948901236057</threshold>
            <left_node>1</left_node>
            <right_val>-0.2753269970417023</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 0 8 10 -1.</_>
                <_>
                  12 0 4 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.8144161226227880e-04</threshold>
            <left_val>0.3804396986961365</left_val>
            <right_val>-0.4396784901618958</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 12 16 4 -1.</_>
                <_>
                  5 12 8 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.8816778734326363e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.4325881898403168</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 8 6 9 -1.</_>
                <_>
                  15 11 2 3 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0396251305937767</threshold>
            <left_val>0.2448122054338455</left_val>
            <right_val>-0.2619363963603973</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 0 1 8 -1.</_>
                <_>
                  19 4 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.5907390993088484e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3619948029518127</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  8 2 10 6 -1.</_>
                <_>
                  8 5 10 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0370088703930378</threshold>
            <left_val>0.0226374603807926</left_val>
            <right_val>0.5577843785285950</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 7 2 1 -1.</_>
                <_>
                  19 7 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.8503930126316845e-05</threshold>
            <left_val>-0.3386113047599792</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  19 4 1 12 -1.</_>
                <_>
                  19 7 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.7969701699912548e-03</threshold>
            <left_val>0.3185609877109528</left_val>
            <right_val>-0.1660024970769882</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 11 3 3 -1.</_>
                <_>
                  9 12 1 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0112980101257563</threshold>
            <left_val>0.3730547130107880</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  7 12 3 3 -1.</_>
                <_>
                  8 12 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.4886539690196514e-03</threshold>
            <left_val>0.2969295978546143</left_val>
            <right_val>-0.2523576021194458</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 13 3 2 -1.</_>
                <_>
                  7 13 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2497780155390501e-03</threshold>
            <left_val>0.3426302969455719</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 15 3 2 -1.</_>
                <_>
                  17 15 3 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.9247230850160122e-03</threshold>
            <left_val>-0.0565932393074036</left_val>
            <right_val>-0.7062603235244751</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 6 3 3 -1.</_>
                <_>
                  12 6 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.7976630479097366e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5418022871017456</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 15 2 4 -1.</_>
                <_>
                  0 17 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.9808609504252672e-03</threshold>
            <left_val>-0.2564300894737244</left_val>
            <right_val>0.1844687014818192</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 9 7 2 -1.</_>
                <_>
                  12 9 7 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-4.7688339836895466e-03</threshold>
            <left_val>-0.2969822883605957</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 5 8 7 -1.</_>
                <_>
                  10 5 4 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0157556105405092</threshold>
            <left_val>0.2895937860012054</left_val>
            <right_val>-0.1648074984550476</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 17 8 3 -1.</_>
                <_>
                  8 17 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0119196400046349</threshold>
            <left_val>-0.5856721997261047</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 17 4 3 -1.</_>
                <_>
                  0 18 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.2308131232857704e-03</threshold>
            <left_val>0.1360127031803131</left_val>
            <right_val>-0.4816245138645172</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 1 10 6 -1.</_>
                <_>
                  5 3 10 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0205485504120588</threshold>
            <left_node>1</left_node>
            <right_val>0.3014349937438965</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 2 18 2 -1.</_>
                <_>
                  6 2 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.3943338356912136e-03</threshold>
            <left_val>0.0463677607476711</left_val>
            <right_val>-0.4237951934337616</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 8 6 3 -1.</_>
                <_>
                  7 9 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.2137800268828869e-03</threshold>
            <left_val>0.4572427868843079</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  10 8 1 3 -1.</_>
                <_>
                  10 9 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.4182809973135591e-03</threshold>
            <left_val>-0.3014363944530487</left_val>
            <right_val>0.1820451021194458</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 1 3 2 -1.</_>
                <_>
                  17 2 1 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.1609420441091061e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.5265483856201172</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 10 1 2 -1.</_>
                <_>
                  2 10 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-3.7915320135653019e-03</threshold>
            <left_val>-0.5867707133293152</left_val>
            <right_val>0.1170366033911705</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 9 1 2 -1.</_>
                <_>
                  2 9 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>2.0879150833934546e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.3530772924423218</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 9 2 3 -1.</_>
                <_>
                  2 10 2 1 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>1.5018540434539318e-03</threshold>
            <left_val>0.1862480044364929</left_val>
            <right_val>-0.3272973001003265</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 14 12 6 -1.</_>
                <_>
                  2 14 6 3 2.</_>
                <_>
                  8 17 6 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0212488099932671</threshold>
            <left_node>1</left_node>
            <right_val>-0.3197925984859467</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 17 1 2 -1.</_>
                <_>
                  15 17 1 1 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-5.5249751312658191e-04</threshold>
            <left_val>0.2337023019790649</left_val>
            <right_val>-0.1738619953393936</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 11 3 3 -1.</_>
                <_>
                  18 12 1 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-3.0085169710218906e-03</threshold>
            <left_val>0.1759604960680008</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  13 12 3 2 -1.</_>
                <_>
                  14 12 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.1611919617280364e-03</threshold>
            <left_val>0.1603343039751053</left_val>
            <right_val>-0.3968097865581512</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 18 4 2 -1.</_>
                <_>
                  18 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.9655580185353756e-03</threshold>
            <left_val>0.3669176995754242</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 14 2 4 -1.</_>
                <_>
                  17 15 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-6.5836100839078426e-03</threshold>
            <left_val>-0.6296635866165161</left_val>
            <right_val>-0.0249264501035213</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 13 3 1 -1.</_>
                <_>
                  13 13 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.0950471349060535e-04</threshold>
            <left_val>0.3957498073577881</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  11 12 3 3 -1.</_>
                <_>
                  12 13 1 1 9.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.7984529994428158e-03</threshold>
            <left_val>0.1749224066734314</left_val>
            <right_val>-0.2683740854263306</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 16 20 -1.</_>
                <_>
                  8 0 8 20 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.5775880217552185</threshold>
            <left_val>0.5961139202117920</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  3 0 8 5 -1.</_>
                <_>
                  5 0 4 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0151613103225827</threshold>
            <left_val>-0.6613163948059082</left_val>
            <right_val>3.3608361263759434e-04</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 2 1 -1.</_>
                <_>
                  1 0 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.6604672358371317e-05</threshold>
            <left_node>1</left_node>
            <right_val>0.2040158957242966</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 2 19 4 -1.</_>
                <_>
                  1 4 19 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0277699790894985</threshold>
            <left_val>-0.3209733068943024</left_val>
            <right_val>0.2231740057468414</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 7 3 4 -1.</_>
                <_>
                  13 7 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6336179580539465e-03</threshold>
            <left_val>-0.3965649902820587</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  15 6 3 3 -1.</_>
                <_>
                  16 7 1 3 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>8.3722146227955818e-03</threshold>
            <left_val>0.1388397067785263</left_val>
            <right_val>-0.5800622105598450</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 13 2 2 -1.</_>
                <_>
                  3 13 1 1 2.</_>
                <_>
                  4 14 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0203031646087766e-04</threshold>
            <left_val>0.2777728140354156</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  2 12 2 2 -1.</_>
                <_>
                  2 12 1 1 2.</_>
                <_>
                  3 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.8448870074935257e-04</threshold>
            <left_val>0.2162851989269257</left_val>
            <right_val>-0.2969225049018860</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 3 19 4 -1.</_>
                <_>
                  0 4 19 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0336381718516350</threshold>
            <left_val>0.3579196929931641</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  17 7 3 4 -1.</_>
                <_>
                  18 8 1 4 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>4.4241230934858322e-03</threshold>
            <left_val>-8.6632027523592114e-04</left_val>
            <right_val>-0.5587272047996521</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 8 3 4 -1.</_>
                <_>
                  5 9 1 4 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0115452604368329</threshold>
            <left_node>1</left_node>
            <right_val>0.3383761942386627</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 11 4 6 -1.</_>
                <_>
                  15 11 2 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5816639643162489e-03</threshold>
            <left_val>0.0286606997251511</left_val>
            <right_val>-0.3504197001457214</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 3 2 6 -1.</_>
                <_>
                  18 5 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0138381402939558</threshold>
            <left_node>1</left_node>
            <right_val>-0.7788680791854858</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 3 2 4 -1.</_>
                <_>
                  14 3 2 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>0.0283274091780186</threshold>
            <left_val>-0.0186049100011587</left_val>
            <right_val>0.6214786767959595</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 9 5 4 -1.</_>
                <_>
                  7 10 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.8482163846492767e-03</threshold>
            <left_val>0.2636981904506683</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  12 11 8 2 -1.</_>
                <_>
                  12 12 8 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.1661020107567310e-03</threshold>
            <left_val>0.1030258014798164</left_val>
            <right_val>-0.3268001079559326</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 13 3 4 -1.</_>
                <_>
                  16 13 3 2 2.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0322522111237049</threshold>
            <left_val>-0.5004624128341675</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  14 7 5 9 -1.</_>
                <_>
                  14 10 5 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0949211195111275</threshold>
            <left_val>-0.7276101112365723</left_val>
            <right_val>0.1033010035753250</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 1 3 -1.</_>
                <_>
                  0 13 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5177269708365202e-03</threshold>
            <left_node>1</left_node>
            <right_val>-0.6393802762031555</right_val></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  6 6 3 6 -1.</_>
                <_>
                  4 8 3 2 3.</_></rects>
              <tilted>1</tilted></feature>
            <threshold>-0.0408921688795090</threshold>
            <left_val>-0.5734522938728333</left_val>
            <right_val>0.0815025269985199</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 9 9 1 -1.</_>
                <_>
                  3 9 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.9293189980089664e-03</threshold>
            <left_val>0.2417722940444946</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  0 9 6 2 -1.</_>
                <_>
                  0 9 3 1 2.</_>
                <_>
                  3 10 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4116390375420451e-03</threshold>
            <left_val>0.0803638175129890</left_val>
            <right_val>-0.3614653944969177</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 2 4 4 -1.</_>
                <_>
                  4 2 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.8812779821455479e-03</threshold>
            <left_val>-0.5763878226280212</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  18 3 2 3 -1.</_>
                <_>
                  18 4 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.4630360789597034e-03</threshold>
            <left_val>0.0918357893824577</left_val>
            <right_val>-0.6803910136222839</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 16 3 3 -1.</_>
                <_>
                  6 17 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.9870839789509773e-03</threshold>
            <left_val>-0.1023664027452469</left_val>
            <right_node>1</right_node></_>
          <_>
            <!-- node 1 -->
            <feature>
              <rects>
                <_>
                  1 16 6 3 -1.</_>
                <_>
                  1 17 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.4975335523486137e-03</threshold>
            <left_val>0.4915060997009277</left_val>
            <right_val>-0.3801138997077942</right_val></_></_></trees>
      <stage_threshold>-1.7649420499801636</stage_threshold>
      <parent>18</parent>
      <next>-1</next></_></stages></haarcascade_righteye>
</opencv_storage>
