//
//  IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
//
//  By downloading, copying, installing or using the software you agree to this license.
//  If you do not agree to this license, do not download, install,
//  copy or use the software.
//
//
//                          License Agreement
//                For Open Source Computer Vision Library
//
// Copyright (C) 2014, OpenCV Foundation, all rights reserved.
// Third party copyrights are property of their respective owners.
//
// Redistribution and use in source and binary forms, with or without modification,
// are permitted provided that the following conditions are met:
//
//   * Redistribution's of source code must retain the above copyright notice,
//     this list of conditions and the following disclaimer.
//
//   * Redistribution's in binary form must reproduce the above copyright notice,
//     this list of conditions and the following disclaimer in the documentation
//     and/or other materials provided with the distribution.
//
//   * The name of the copyright holders may not be used to endorse or promote products
//     derived from this software without specific prior written permission.
//
// This software is provided by the copyright holders and contributors "as is" and
// any express or implied warranties, including, but not limited to, the implied
// warranties of merchantability and fitness for a particular purpose are disclaimed.
// In no event shall the Intel Corporation or contributors be liable for any direct,
// indirect, incidental, special, exemplary, or consequential damages
// (including, but not limited to, procurement of substitute goods or services;
// loss of use, data, or profits; or business interruption) however caused
// and on any theory of liability, whether in contract, strict liability,
// or tort (including negligence or otherwise) arising in any way out of
// the use of this software, even if advised of the possibility of such damage.
//
// Author: Tolga Birdal <tbirdal AT gmail.com>

#ifndef __OPENCV_SURFACE_MATCHING_HASH_MURMUR_HPP_
#define __OPENCV_SURFACE_MATCHING_HASH_MURMUR_HPP_

namespace cv
{
namespace ppf_match_3d
{


#if defined(_MSC_VER)

#define FORCE_INLINE    inline static

#include <stdlib.h>

#define ROTL32(x,y)     _rotl(x,y)
#define ROTL64(x,y)     _rotl64(x,y)

#else
//#define FORCE_INLINE __attribute__((always_inline))
#define FORCE_INLINE inline static

/* gcc recognises this code and generates a rotate instruction for CPUs with one */
#define ROTL32(x,r)  (((uint32_t)x << r) | ((uint32_t)x >> (32 - r)))

inline static long long ROTL64 ( long long x, int8_t r )
{
    return (x << r) | (x >> (64 - r));
}

#endif // !defined(_MSC_VER)

#if (defined __x86_64__ || defined _M_X64)
#include "hash_murmur64.hpp"
#define murmurHash hashMurmurx64
#else
#include "hash_murmur86.hpp"
#define murmurHash hashMurmurx86
#endif
}
}


#endif
