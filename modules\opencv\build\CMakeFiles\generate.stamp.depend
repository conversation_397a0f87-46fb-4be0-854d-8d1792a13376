# CMake generation dependency list for this directory.
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCInformation.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXInformation.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCommonLanguageInclude.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindJavaCommon.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeGenericSystem.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeInitializeConfigs.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeLanguageInformation.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeParseArguments.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeParseImplicitLinkInfo.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakePushCheckState.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeRCInformation.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeSystemSpecificInformation.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeSystemSpecificInitialize.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CPack.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CPackComponent.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceCompiles.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckFunctionExists.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckIncludeFile.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckIncludeFileCXX.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckLibraryExists.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckSourceCompiles.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckSymbolExists.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckTypeSize.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/CMakeCommonCompilerMacros.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/MSVC-C.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/MSVC-CXX.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/MSVC.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindBLAS.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindCUDA.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindCUDA/select_compute_arch.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindGit.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindJNI.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindJava.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindLAPACK.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindPackageHandleStandardArgs.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindPackageMessage.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindThreads.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeCLinkerInformation.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeCXXLinkerInformation.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeCommonLinkerInformation.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Linker/MSVC-C.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Linker/MSVC-CXX.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Linker/MSVC.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/Windows-MSVC-C.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/Windows-MSVC-CXX.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/Windows-MSVC.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-Initialize.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC-C.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC-CXX.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/WindowsPaths.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/TestBigEndian.cmake
C:/Program Files/CMake/share/cmake-4.1/Templates/CPackConfig.cmake.in
D:/AI/opencv/cudabuild/3rdparty/ffmpeg/ffmpeg_version.cmake
D:/AI/opencv/cudabuild/CMakeFiles/4.1.0-rc3/CMakeCCompiler.cmake
D:/AI/opencv/cudabuild/CMakeFiles/4.1.0-rc3/CMakeCXXCompiler.cmake
D:/AI/opencv/cudabuild/CMakeFiles/4.1.0-rc3/CMakeRCCompiler.cmake
D:/AI/opencv/cudabuild/CMakeFiles/4.1.0-rc3/CMakeSystem.cmake
D:/AI/opencv/opencv-4.10.0/3rdparty/ffmpeg/ffmpeg.cmake
D:/AI/opencv/opencv-4.10.0/3rdparty/ippicv/ippicv.cmake
D:/AI/opencv/opencv-4.10.0/CMakeLists.txt
D:/AI/opencv/opencv-4.10.0/cmake/FindCUDNN.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVCRTLinkage.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVCompilerOptimizations.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVCompilerOptions.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVDetectApacheAnt.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVDetectCUDA.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVDetectCUDAUtils.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVDetectCXXCompiler.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVDetectDirectML.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVDetectDirectX.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVDetectFlatbuffers.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVDetectOpenCL.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVDetectPython.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVDetectTrace.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVDetectVTK.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVDownload.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVExtraTargets.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVFindFrameworks.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVFindIPP.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVFindIPPIW.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVFindLAPACK.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVFindLibsGUI.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVFindLibsGrfmt.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVFindLibsPerf.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVFindLibsVideo.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVFindMKL.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVFindOpenBLAS.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVFindOpenEXR.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVFindProtobuf.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVGenABI.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVGenAndroidMK.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVGenConfig.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVGenHeaders.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVGenSetupVars.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVInstallLayout.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVMinDepVersions.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVModule.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVPCHSupport.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVPackaging.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVUtils.cmake
D:/AI/opencv/opencv-4.10.0/cmake/OpenCVVersion.cmake
D:/AI/opencv/opencv-4.10.0/cmake/checks/directml.cpp
D:/AI/opencv/opencv-4.10.0/cmake/checks/directx.cpp
D:/AI/opencv/opencv-4.10.0/cmake/checks/opencl.cpp
D:/AI/opencv/opencv-4.10.0/cmake/checks/win32uitest.cpp
D:/AI/opencv/opencv-4.10.0/cmake/platforms/OpenCV-Windows.cmake
D:/AI/opencv/opencv-4.10.0/cmake/templates/OpenCVConfig-version.cmake.in
D:/AI/opencv/opencv-4.10.0/cmake/templates/OpenCVConfig.cmake.in
D:/AI/opencv/opencv-4.10.0/cmake/templates/OpenCVConfig.root-WIN32.cmake.in
D:/AI/opencv/opencv-4.10.0/cmake/templates/cmake_uninstall.cmake.in
D:/AI/opencv/opencv-4.10.0/cmake/templates/custom_hal.hpp.in
D:/AI/opencv/opencv-4.10.0/cmake/templates/cv_cpu_config.h.in
D:/AI/opencv/opencv-4.10.0/cmake/templates/cvconfig.h.in
D:/AI/opencv/opencv-4.10.0/cmake/templates/opencv_modules.hpp.in
D:/AI/opencv/opencv-4.10.0/cmake/templates/setup_vars_win32.cmd.in
D:/AI/opencv/opencv-4.10.0/cmake/vars/EnableModeVars.cmake
D:/AI/opencv/opencv-4.10.0/modules/core/include/opencv2/core/version.hpp
D:/AI/opencv/opencv-4.10.0/modules/dnn/cmake/init.cmake
D:/AI/opencv/opencv-4.10.0/modules/gapi/cmake/DownloadADE.cmake
D:/AI/opencv/opencv-4.10.0/modules/gapi/cmake/init.cmake
D:/AI/opencv/opencv-4.10.0/modules/highgui/cmake/detect_win32ui.cmake
D:/AI/opencv/opencv-4.10.0/modules/highgui/cmake/init.cmake
D:/AI/opencv/opencv-4.10.0/modules/videoio/cmake/detect_dc1394.cmake
D:/AI/opencv/opencv-4.10.0/modules/videoio/cmake/detect_dshow.cmake
D:/AI/opencv/opencv-4.10.0/modules/videoio/cmake/detect_ffmpeg.cmake
D:/AI/opencv/opencv-4.10.0/modules/videoio/cmake/detect_gstreamer.cmake
D:/AI/opencv/opencv-4.10.0/modules/videoio/cmake/detect_msmf.cmake
D:/AI/opencv/opencv-4.10.0/modules/videoio/cmake/detect_obsensor.cmake
D:/AI/opencv/opencv-4.10.0/modules/videoio/cmake/init.cmake
D:/AI/opencv/opencv_contrib-4.10.0/modules/julia/cmake/init.cmake
D:/AI/opencv/opencv_contrib-4.10.0/modules/matlab/cmake/init.cmake
D:/AI/opencv/opencv_contrib-4.10.0/modules/text/cmake/FindTesseract.cmake
D:/AI/opencv/opencv_contrib-4.10.0/modules/text/cmake/init.cmake
