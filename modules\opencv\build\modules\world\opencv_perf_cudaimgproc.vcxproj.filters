﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\perf\perf_bilateral_filter.cpp">
      <Filter>opencv_cudaimgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\perf\perf_blend.cpp">
      <Filter>opencv_cudaimgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\perf\perf_canny.cpp">
      <Filter>opencv_cudaimgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\perf\perf_color.cpp">
      <Filter>opencv_cudaimgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\perf\perf_corners.cpp">
      <Filter>opencv_cudaimgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\perf\perf_gftt.cpp">
      <Filter>opencv_cudaimgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\perf\perf_histogram.cpp">
      <Filter>opencv_cudaimgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\perf\perf_hough.cpp">
      <Filter>opencv_cudaimgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\perf\perf_main.cpp">
      <Filter>opencv_cudaimgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\perf\perf_match_template.cpp">
      <Filter>opencv_cudaimgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\perf\perf_mean_shift.cpp">
      <Filter>opencv_cudaimgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\perf\perf_moments.cpp">
      <Filter>opencv_cudaimgproc\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\cudaimgproc\perf\perf_precomp.hpp">
      <Filter>opencv_cudaimgproc\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_cudaimgproc">
      <UniqueIdentifier>{396D462E-A9AD-3A4C-AC81-CCE54C34CBCA}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_cudaimgproc\Include">
      <UniqueIdentifier>{75F717A3-066A-395B-BCA3-E00F1F8D3B1F}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_cudaimgproc\Src">
      <UniqueIdentifier>{35407FE8-D1D8-3996-B6B4-7C5D444141BE}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
