@incollection{<PERSON>20<PERSON>,
  title={Statistics of patch offsets for image completion},
  author={<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>},
  booktitle={Computer Vision--ECCV 2012},
  pages={16--29},
  year={2012},
  publisher={<PERSON>}
}
@inproceedings{Cheng2015,
  title={Effective learning-based illuminant estimation using simple features},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
  booktitle={Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition},
  pages={1000--1008},
  year={2015}
}
@book{Holzmann1988,
  title={Beyond Photography: The Digital Darkroom},
  author={GerPublished by ard <PERSON><PERSON>},
  publisher={<PERSON> in 1988}
}
@inproceedings{DD02,
  author = {<PERSON><PERSON>, <PERSON>{\'e}do and <PERSON>, <PERSON>},
  title = {Fast bilateral filtering for the display of high-dynamic-range images},
  booktitle = {ACM Transactions on Graphics (TOG)},
  year = {2002},
  pages = {257--266},
  volume = {21},
  number = {3},
  publisher = {ACM},
  url = {https://www.researchgate.net/profile/<PERSON>_<PERSON>/publication/220184746_Fast_Bilateral_Filtering_for_the_Display_of_High_-_dynamic_-_range_Images/links/54566b000cf26d5090a95f96/Fast-Bilateral-Filtering-for-the-Display-of-High-dynamic-range-Images.pdf}
}

@INPROCEEDINGS{GenserPCS2018,
   author={N. {Genser} and J. {Seiler} and F. {Schilling} and A. {Kaup}},
   booktitle={Proc. Picture Coding Symposium (PCS)},
   title={Signal and Loss Geometry Aware Frequency Selective Extrapolation for Error Concealment},
   year={2018},
   pages={159-163},
   keywords={extrapolation;image reconstruction;video coding;loss geometry aware frequency selective extrapolation;error concealment;complex models;moderate computational complexity;Full HD image;error pattern;adjacent samples;undistorted samples;reconstruction parameters;processing order;High Efficiency Video Coding;content based partitioning;signal characteristics;block based frequency selective extrapolation;Image reconstruction;Extrapolation;Geometry;Partitioning algorithms;Task analysis;Computational modeling;Standards},
   doi={10.1109/PCS.2018.8456259},
   month={June},
}

@ARTICLE{SeilerTIP2015,
   author={J. {Seiler} and M. {Jonscher} and M. {Schöberl} and A. {Kaup}},
   journal={IEEE Transactions on Image Processing},
   title={Resampling Images to a Regular Grid From a Non-Regular Subset of Pixel Positions Using Frequency Selective Reconstruction},
   year={2015},
   volume={24},
   number={11},
   pages={4540-4555},
   keywords={Fourier transforms;image reconstruction;resampling images;regular grid;nonregular subset;pixel positions;frequency selective reconstruction;displaying image signals;image signal reconstruction algorithm;Fourier domain;optical transfer function;visual quality;peak signal-to-noise ratio;Image reconstruction;Signal processing algorithms;Reconstruction algorithms;Signal processing;Spatial resolution;;Image reconstruction;non-regular sampling;interpolation},
   doi={10.1109/TIP.2015.2463084},
   month={Nov},
}

@INPROCEEDINGS{GroscheICIP2018,
   author={S. {Grosche} and J. {Seiler} and A. {Kaup}},
   booktitle={Proc. 25th IEEE International Conference on Image Processing (ICIP)},
   title={Iterative Optimization of Quarter Sampling Masks for Non-Regular Sampling Sensors},
   year={2018},
   pages={26-30},
   keywords={extrapolation;image enhancement;image reconstruction;image resolution;image sampling;image sensors;interpolation;iterative methods;optimisation;regression analysis;iterative optimization;nonregular sampling sensors;iterative algorithm;arbitrary quarter sampling mask;reconstruction algorithms;random quarter sampling mask;optimized mask;frequency selective extrapolation;steering kernel regression;nearest neighbor interpolation;linear interpolation;regular imaging sensor;reconstruction quality;noise figure 0.31 dB to 0.68 dB;Image resolution;Image reconstruction;Sensors;Optimization;Energy resolution;Reconstruction algorithms;Image sensors;Non-Regular Sampling;Image reconstruction},
   doi={10.1109/ICIP.2018.8451658},
   month={Oct},
}

@INPROCEEDINGS{GroscheIST2018,
   author={S. {Grosche} and J. {Seiler} and A. {Kaup}},
   booktitle={Proc. IEEE International Conference on Imaging Systems and Techniques (IST)},
   title={Design Techniques for Incremental Non-Regular Image Sampling Patterns},
   year={2018},
   pages={1-6},
   keywords={image reconstruction;image resolution;image sampling;design techniques;incremental nonregular image sampling patterns;image signals;regular two dimensional grid;nonregular sampling patterns;sampling positions;random patterns;regular patterns;arbitrary sampling densities;incremental sampling patterns;sampling density;Image reconstruction;Scanning electron microscopy;Probability distribution;Atomic force microscopy;Reconstruction algorithms;Measurement by laser beam;Image Reconstruction;non-Regular Sampling},
   doi={10.1109/IST.2018.8577090},
   month={Oct},
}
