// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Devices_Scanners_2_H
#define WINRT_Windows_Devices_Scanners_2_H
#include "winrt/impl/Windows.Devices.Scanners.1.h"
WINRT_EXPORT namespace winrt::Windows::Devices::Scanners
{
    struct ImageScannerResolution
    {
        float DpiX;
        float DpiY;
    };
    inline bool operator==(ImageScannerResolution const& left, ImageScannerResolution const& right) noexcept
    {
        return left.DpiX == right.DpiX && left.DpiY == right.DpiY;
    }
    inline bool operator!=(ImageScannerResolution const& left, ImageScannerResolution const& right) noexcept
    {
        return !(left == right);
    }
    struct WINRT_IMPL_EMPTY_BASES ImageScanner : winrt::Windows::Devices::Scanners::IImageScanner
    {
        ImageScanner(std::nullptr_t) noexcept {}
        ImageScanner(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Devices::Scanners::IImageScanner(ptr, take_ownership_from_abi) {}
        static auto FromIdAsync(param::hstring const& deviceId);
        static auto GetDeviceSelector();
    };
    struct WINRT_IMPL_EMPTY_BASES ImageScannerAutoConfiguration : winrt::Windows::Devices::Scanners::IImageScannerFormatConfiguration
    {
        ImageScannerAutoConfiguration(std::nullptr_t) noexcept {}
        ImageScannerAutoConfiguration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Devices::Scanners::IImageScannerFormatConfiguration(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ImageScannerFeederConfiguration : winrt::Windows::Devices::Scanners::IImageScannerFormatConfiguration,
        impl::require<ImageScannerFeederConfiguration, winrt::Windows::Devices::Scanners::IImageScannerSourceConfiguration, winrt::Windows::Devices::Scanners::IImageScannerFeederConfiguration>
    {
        ImageScannerFeederConfiguration(std::nullptr_t) noexcept {}
        ImageScannerFeederConfiguration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Devices::Scanners::IImageScannerFormatConfiguration(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ImageScannerFlatbedConfiguration : winrt::Windows::Devices::Scanners::IImageScannerFormatConfiguration,
        impl::require<ImageScannerFlatbedConfiguration, winrt::Windows::Devices::Scanners::IImageScannerSourceConfiguration>
    {
        ImageScannerFlatbedConfiguration(std::nullptr_t) noexcept {}
        ImageScannerFlatbedConfiguration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Devices::Scanners::IImageScannerFormatConfiguration(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ImageScannerPreviewResult : winrt::Windows::Devices::Scanners::IImageScannerPreviewResult
    {
        ImageScannerPreviewResult(std::nullptr_t) noexcept {}
        ImageScannerPreviewResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Devices::Scanners::IImageScannerPreviewResult(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ImageScannerScanResult : winrt::Windows::Devices::Scanners::IImageScannerScanResult
    {
        ImageScannerScanResult(std::nullptr_t) noexcept {}
        ImageScannerScanResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Devices::Scanners::IImageScannerScanResult(ptr, take_ownership_from_abi) {}
    };
}
#endif
