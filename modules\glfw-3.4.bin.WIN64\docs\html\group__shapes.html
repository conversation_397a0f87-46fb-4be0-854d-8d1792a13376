<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Standard cursor shapes</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle"><div class="title">Standard cursor shapes<div class="ingroups"><a class="el" href="group__input.html">Input reference</a></div></div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>These are the <a class="el" href="input_guide.html#cursor_standard">standard cursor shapes</a> that can be requested from the platform (window system). </p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:ga8ab0e717245b85506cb0eaefdea39d0a" id="r_ga8ab0e717245b85506cb0eaefdea39d0a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#ga8ab0e717245b85506cb0eaefdea39d0a">GLFW_ARROW_CURSOR</a>&#160;&#160;&#160;0x00036001</td></tr>
<tr class="memdesc:ga8ab0e717245b85506cb0eaefdea39d0a"><td class="mdescLeft">&#160;</td><td class="mdescRight">The regular arrow cursor shape.  <br /></td></tr>
<tr class="separator:ga8ab0e717245b85506cb0eaefdea39d0a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga36185f4375eaada1b04e431244774c86" id="r_ga36185f4375eaada1b04e431244774c86"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#ga36185f4375eaada1b04e431244774c86">GLFW_IBEAM_CURSOR</a>&#160;&#160;&#160;0x00036002</td></tr>
<tr class="memdesc:ga36185f4375eaada1b04e431244774c86"><td class="mdescLeft">&#160;</td><td class="mdescRight">The text input I-beam cursor shape.  <br /></td></tr>
<tr class="separator:ga36185f4375eaada1b04e431244774c86"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8af88c0ea05ab9e8f9ac1530e8873c22" id="r_ga8af88c0ea05ab9e8f9ac1530e8873c22"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#ga8af88c0ea05ab9e8f9ac1530e8873c22">GLFW_CROSSHAIR_CURSOR</a>&#160;&#160;&#160;0x00036003</td></tr>
<tr class="memdesc:ga8af88c0ea05ab9e8f9ac1530e8873c22"><td class="mdescLeft">&#160;</td><td class="mdescRight">The crosshair cursor shape.  <br /></td></tr>
<tr class="separator:ga8af88c0ea05ab9e8f9ac1530e8873c22"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaad01a50929fb515bf27e4462c51f6ed0" id="r_gaad01a50929fb515bf27e4462c51f6ed0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#gaad01a50929fb515bf27e4462c51f6ed0">GLFW_POINTING_HAND_CURSOR</a>&#160;&#160;&#160;0x00036004</td></tr>
<tr class="memdesc:gaad01a50929fb515bf27e4462c51f6ed0"><td class="mdescLeft">&#160;</td><td class="mdescRight">The pointing hand cursor shape.  <br /></td></tr>
<tr class="separator:gaad01a50929fb515bf27e4462c51f6ed0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2010a43dc1050a7c9154148a63cf01ad" id="r_ga2010a43dc1050a7c9154148a63cf01ad"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#ga2010a43dc1050a7c9154148a63cf01ad">GLFW_RESIZE_EW_CURSOR</a>&#160;&#160;&#160;0x00036005</td></tr>
<tr class="memdesc:ga2010a43dc1050a7c9154148a63cf01ad"><td class="mdescLeft">&#160;</td><td class="mdescRight">The horizontal resize/move arrow shape.  <br /></td></tr>
<tr class="separator:ga2010a43dc1050a7c9154148a63cf01ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa59214e8cdc8c8adf08fdf125ed68388" id="r_gaa59214e8cdc8c8adf08fdf125ed68388"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#gaa59214e8cdc8c8adf08fdf125ed68388">GLFW_RESIZE_NS_CURSOR</a>&#160;&#160;&#160;0x00036006</td></tr>
<tr class="memdesc:gaa59214e8cdc8c8adf08fdf125ed68388"><td class="mdescLeft">&#160;</td><td class="mdescRight">The vertical resize/move arrow shape.  <br /></td></tr>
<tr class="separator:gaa59214e8cdc8c8adf08fdf125ed68388"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadf2c0a495ec9cef4e1a364cc99aa78da" id="r_gadf2c0a495ec9cef4e1a364cc99aa78da"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#gadf2c0a495ec9cef4e1a364cc99aa78da">GLFW_RESIZE_NWSE_CURSOR</a>&#160;&#160;&#160;0x00036007</td></tr>
<tr class="memdesc:gadf2c0a495ec9cef4e1a364cc99aa78da"><td class="mdescLeft">&#160;</td><td class="mdescRight">The top-left to bottom-right diagonal resize/move arrow shape.  <br /></td></tr>
<tr class="separator:gadf2c0a495ec9cef4e1a364cc99aa78da"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab06bba3b407f92807ba9b48de667a323" id="r_gab06bba3b407f92807ba9b48de667a323"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#gab06bba3b407f92807ba9b48de667a323">GLFW_RESIZE_NESW_CURSOR</a>&#160;&#160;&#160;0x00036008</td></tr>
<tr class="memdesc:gab06bba3b407f92807ba9b48de667a323"><td class="mdescLeft">&#160;</td><td class="mdescRight">The top-right to bottom-left diagonal resize/move arrow shape.  <br /></td></tr>
<tr class="separator:gab06bba3b407f92807ba9b48de667a323"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3a5f4811155f95ccafbbb4c9a899fc1d" id="r_ga3a5f4811155f95ccafbbb4c9a899fc1d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#ga3a5f4811155f95ccafbbb4c9a899fc1d">GLFW_RESIZE_ALL_CURSOR</a>&#160;&#160;&#160;0x00036009</td></tr>
<tr class="memdesc:ga3a5f4811155f95ccafbbb4c9a899fc1d"><td class="mdescLeft">&#160;</td><td class="mdescRight">The omni-directional resize/move cursor shape.  <br /></td></tr>
<tr class="separator:ga3a5f4811155f95ccafbbb4c9a899fc1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga297c503095b034bc8891393b637844b1" id="r_ga297c503095b034bc8891393b637844b1"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#ga297c503095b034bc8891393b637844b1">GLFW_NOT_ALLOWED_CURSOR</a>&#160;&#160;&#160;0x0003600A</td></tr>
<tr class="memdesc:ga297c503095b034bc8891393b637844b1"><td class="mdescLeft">&#160;</td><td class="mdescRight">The operation-not-allowed shape.  <br /></td></tr>
<tr class="separator:ga297c503095b034bc8891393b637844b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabb3eb0109f11bb808fc34659177ca962" id="r_gabb3eb0109f11bb808fc34659177ca962"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#gabb3eb0109f11bb808fc34659177ca962">GLFW_HRESIZE_CURSOR</a>&#160;&#160;&#160;<a class="el" href="group__shapes.html#ga2010a43dc1050a7c9154148a63cf01ad">GLFW_RESIZE_EW_CURSOR</a></td></tr>
<tr class="memdesc:gabb3eb0109f11bb808fc34659177ca962"><td class="mdescLeft">&#160;</td><td class="mdescRight">Legacy name for compatibility.  <br /></td></tr>
<tr class="separator:gabb3eb0109f11bb808fc34659177ca962"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf024f0e1ff8366fb2b5c260509a1fce5" id="r_gaf024f0e1ff8366fb2b5c260509a1fce5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#gaf024f0e1ff8366fb2b5c260509a1fce5">GLFW_VRESIZE_CURSOR</a>&#160;&#160;&#160;<a class="el" href="group__shapes.html#gaa59214e8cdc8c8adf08fdf125ed68388">GLFW_RESIZE_NS_CURSOR</a></td></tr>
<tr class="memdesc:gaf024f0e1ff8366fb2b5c260509a1fce5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Legacy name for compatibility.  <br /></td></tr>
<tr class="separator:gaf024f0e1ff8366fb2b5c260509a1fce5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1db35e20849e0837c82e3dc1fd797263" id="r_ga1db35e20849e0837c82e3dc1fd797263"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__shapes.html#ga1db35e20849e0837c82e3dc1fd797263">GLFW_HAND_CURSOR</a>&#160;&#160;&#160;<a class="el" href="group__shapes.html#gaad01a50929fb515bf27e4462c51f6ed0">GLFW_POINTING_HAND_CURSOR</a></td></tr>
<tr class="memdesc:ga1db35e20849e0837c82e3dc1fd797263"><td class="mdescLeft">&#160;</td><td class="mdescRight">Legacy name for compatibility.  <br /></td></tr>
<tr class="separator:ga1db35e20849e0837c82e3dc1fd797263"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="ga8ab0e717245b85506cb0eaefdea39d0a" name="ga8ab0e717245b85506cb0eaefdea39d0a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8ab0e717245b85506cb0eaefdea39d0a">&#9670;&#160;</a></span>GLFW_ARROW_CURSOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_ARROW_CURSOR&#160;&#160;&#160;0x00036001</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The regular arrow cursor shape. </p>

</div>
</div>
<a id="ga36185f4375eaada1b04e431244774c86" name="ga36185f4375eaada1b04e431244774c86"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga36185f4375eaada1b04e431244774c86">&#9670;&#160;</a></span>GLFW_IBEAM_CURSOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_IBEAM_CURSOR&#160;&#160;&#160;0x00036002</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The text input I-beam cursor shape. </p>

</div>
</div>
<a id="ga8af88c0ea05ab9e8f9ac1530e8873c22" name="ga8af88c0ea05ab9e8f9ac1530e8873c22"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8af88c0ea05ab9e8f9ac1530e8873c22">&#9670;&#160;</a></span>GLFW_CROSSHAIR_CURSOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_CROSSHAIR_CURSOR&#160;&#160;&#160;0x00036003</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The crosshair cursor shape. </p>

</div>
</div>
<a id="gaad01a50929fb515bf27e4462c51f6ed0" name="gaad01a50929fb515bf27e4462c51f6ed0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaad01a50929fb515bf27e4462c51f6ed0">&#9670;&#160;</a></span>GLFW_POINTING_HAND_CURSOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_POINTING_HAND_CURSOR&#160;&#160;&#160;0x00036004</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The pointing hand cursor shape. </p>

</div>
</div>
<a id="ga2010a43dc1050a7c9154148a63cf01ad" name="ga2010a43dc1050a7c9154148a63cf01ad"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2010a43dc1050a7c9154148a63cf01ad">&#9670;&#160;</a></span>GLFW_RESIZE_EW_CURSOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_RESIZE_EW_CURSOR&#160;&#160;&#160;0x00036005</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The horizontal resize/move arrow shape. This is usually a horizontal double-headed arrow. </p>

</div>
</div>
<a id="gaa59214e8cdc8c8adf08fdf125ed68388" name="gaa59214e8cdc8c8adf08fdf125ed68388"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa59214e8cdc8c8adf08fdf125ed68388">&#9670;&#160;</a></span>GLFW_RESIZE_NS_CURSOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_RESIZE_NS_CURSOR&#160;&#160;&#160;0x00036006</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The vertical resize/move shape. This is usually a vertical double-headed arrow. </p>

</div>
</div>
<a id="gadf2c0a495ec9cef4e1a364cc99aa78da" name="gadf2c0a495ec9cef4e1a364cc99aa78da"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadf2c0a495ec9cef4e1a364cc99aa78da">&#9670;&#160;</a></span>GLFW_RESIZE_NWSE_CURSOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_RESIZE_NWSE_CURSOR&#160;&#160;&#160;0x00036007</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The top-left to bottom-right diagonal resize/move shape. This is usually a diagonal double-headed arrow.</p>
<dl class="section note"><dt>Note</dt><dd><b>macOS:</b> This shape is provided by a private system API and may fail with <a class="el" href="group__errors.html#ga09d6943923a70ddef3a085f5baee786c">GLFW_CURSOR_UNAVAILABLE</a> in the future.</dd>
<dd>
<b>Wayland:</b> This shape is provided by a newer standard not supported by all cursor themes.</dd>
<dd>
<b>X11:</b> This shape is provided by a newer standard not supported by all cursor themes. </dd></dl>

</div>
</div>
<a id="gab06bba3b407f92807ba9b48de667a323" name="gab06bba3b407f92807ba9b48de667a323"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab06bba3b407f92807ba9b48de667a323">&#9670;&#160;</a></span>GLFW_RESIZE_NESW_CURSOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_RESIZE_NESW_CURSOR&#160;&#160;&#160;0x00036008</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The top-right to bottom-left diagonal resize/move shape. This is usually a diagonal double-headed arrow.</p>
<dl class="section note"><dt>Note</dt><dd><b>macOS:</b> This shape is provided by a private system API and may fail with <a class="el" href="group__errors.html#ga09d6943923a70ddef3a085f5baee786c">GLFW_CURSOR_UNAVAILABLE</a> in the future.</dd>
<dd>
<b>Wayland:</b> This shape is provided by a newer standard not supported by all cursor themes.</dd>
<dd>
<b>X11:</b> This shape is provided by a newer standard not supported by all cursor themes. </dd></dl>

</div>
</div>
<a id="ga3a5f4811155f95ccafbbb4c9a899fc1d" name="ga3a5f4811155f95ccafbbb4c9a899fc1d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3a5f4811155f95ccafbbb4c9a899fc1d">&#9670;&#160;</a></span>GLFW_RESIZE_ALL_CURSOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_RESIZE_ALL_CURSOR&#160;&#160;&#160;0x00036009</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The omni-directional resize cursor/move shape. This is usually either a combined horizontal and vertical double-headed arrow or a grabbing hand. </p>

</div>
</div>
<a id="ga297c503095b034bc8891393b637844b1" name="ga297c503095b034bc8891393b637844b1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga297c503095b034bc8891393b637844b1">&#9670;&#160;</a></span>GLFW_NOT_ALLOWED_CURSOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_NOT_ALLOWED_CURSOR&#160;&#160;&#160;0x0003600A</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The operation-not-allowed shape. This is usually a circle with a diagonal line through it.</p>
<dl class="section note"><dt>Note</dt><dd><b>Wayland:</b> This shape is provided by a newer standard not supported by all cursor themes.</dd>
<dd>
<b>X11:</b> This shape is provided by a newer standard not supported by all cursor themes. </dd></dl>

</div>
</div>
<a id="gabb3eb0109f11bb808fc34659177ca962" name="gabb3eb0109f11bb808fc34659177ca962"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gabb3eb0109f11bb808fc34659177ca962">&#9670;&#160;</a></span>GLFW_HRESIZE_CURSOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_HRESIZE_CURSOR&#160;&#160;&#160;<a class="el" href="group__shapes.html#ga2010a43dc1050a7c9154148a63cf01ad">GLFW_RESIZE_EW_CURSOR</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is an alias for compatibility with earlier versions. </p>

</div>
</div>
<a id="gaf024f0e1ff8366fb2b5c260509a1fce5" name="gaf024f0e1ff8366fb2b5c260509a1fce5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf024f0e1ff8366fb2b5c260509a1fce5">&#9670;&#160;</a></span>GLFW_VRESIZE_CURSOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_VRESIZE_CURSOR&#160;&#160;&#160;<a class="el" href="group__shapes.html#gaa59214e8cdc8c8adf08fdf125ed68388">GLFW_RESIZE_NS_CURSOR</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is an alias for compatibility with earlier versions. </p>

</div>
</div>
<a id="ga1db35e20849e0837c82e3dc1fd797263" name="ga1db35e20849e0837c82e3dc1fd797263"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1db35e20849e0837c82e3dc1fd797263">&#9670;&#160;</a></span>GLFW_HAND_CURSOR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_HAND_CURSOR&#160;&#160;&#160;<a class="el" href="group__shapes.html#gaad01a50929fb515bf27e4462c51f6ed0">GLFW_POINTING_HAND_CURSOR</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This is an alias for compatibility with earlier versions. </p>

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
