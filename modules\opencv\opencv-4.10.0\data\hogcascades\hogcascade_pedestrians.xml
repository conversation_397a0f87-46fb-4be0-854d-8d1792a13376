<?xml version="1.0"?>
<opencv_storage>
<cascade>
  <stageType>BOOST</stageType>
  <featureType>HOG</featureType>
  <height>96</height>
  <width>48</width>
  <stageParams>
    <boostType>GAB</boostType>
    <minHitRate>9.9500000476837158e-01</minHitRate>
    <maxFalseAlarm>5.0000000000000000e-01</maxFalseAlarm>
    <weightTrimRate>9.4999999999999996e-01</weightTrimRate>
    <maxDepth>1</maxDepth>
    <maxWeakCount>100</maxWeakCount></stageParams>
  <featureParams>
    <maxCatCount>0</maxCatCount>
    <featSize>36</featSize></featureParams>
  <stageNum>15</stageNum>
  <stages>
    <!-- stage 0 -->
    <_>
      <maxWeakCount>5</maxWeakCount>
      <stageThreshold>-7.4773830175399780e-01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 352 1.5149119310081005e-02</internalNodes>
          <leafValues>
            -7.2157478332519531e-01 7.3052006959915161e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 339 3.7586629390716553e-02</internalNodes>
          <leafValues>
            -4.8785275220870972e-01 6.6180247068405151e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 141 3.4038446843624115e-02</internalNodes>
          <leafValues>
            -6.0242265462875366e-01 5.5909192562103271e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 235 3.2107092440128326e-02</internalNodes>
          <leafValues>
            -4.6153610944747925e-01 5.7118606567382812e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 158 1.5392595902085304e-02</internalNodes>
          <leafValues>
            3.7599274516105652e-01 -5.6218206882476807e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 1 -->
    <_>
      <maxWeakCount>8</maxWeakCount>
      <stageThreshold>-1.1153992414474487e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 291 2.6056792587041855e-02</internalNodes>
          <leafValues>
            -3.5386690497398376e-01 7.4421298503875732e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 281 2.7038652449846268e-02</internalNodes>
          <leafValues>
            -4.4826781749725342e-01 5.7312715053558350e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 87 2.0648468285799026e-02</internalNodes>
          <leafValues>
            5.1690024137496948e-01 -4.6962317824363708e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 222 3.4239932894706726e-02</internalNodes>
          <leafValues>
            -4.0093562006950378e-01 5.1903754472732544e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 191 1.5181843191385269e-02</internalNodes>
          <leafValues>
            3.6930915713310242e-01 -6.3566577434539795e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 215 2.5590915232896805e-02</internalNodes>
          <leafValues>
            -4.6027383208274841e-01 4.2719814181327820e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 319 1.3583375141024590e-02</internalNodes>
          <leafValues>
            -7.2019070386886597e-01 2.6437741518020630e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 154 3.6607541143894196e-02</internalNodes>
          <leafValues>
            -5.2001053094863892e-01 3.3129671216011047e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 2 -->
    <_>
      <maxWeakCount>9</maxWeakCount>
      <stageThreshold>-1.1758594512939453e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 343 3.8235597312450409e-02</internalNodes>
          <leafValues>
            6.6281318664550781e-01 -4.3764784932136536e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 246 3.4951344132423401e-02</internalNodes>
          <leafValues>
            -2.7101355791091919e-01 6.3058525323867798e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 142 4.9028784036636353e-02</internalNodes>
          <leafValues>
            -5.0216394662857056e-01 4.6368971467018127e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 222 3.9998225867748260e-02</internalNodes>
          <leafValues>
            -3.4643593430519104e-01 5.4380100965499878e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 215 3.7188939750194550e-02</internalNodes>
          <leafValues>
            -3.0966973304748535e-01 5.5211979150772095e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 174 1.5790924429893494e-02</internalNodes>
          <leafValues>
            3.0823510885238647e-01 -5.6646531820297241e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 235 4.0689803659915924e-02</internalNodes>
          <leafValues>
            -3.4229642152786255e-01 5.3844648599624634e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 389 1.7860446125268936e-02</internalNodes>
          <leafValues>
            -5.5334877967834473e-01 2.5605210661888123e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 333 1.3136577792465687e-02</internalNodes>
          <leafValues>
            -7.3952680826187134e-01 1.7197811603546143e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 3 -->
    <_>
      <maxWeakCount>16</maxWeakCount>
      <stageThreshold>-1.1947091817855835e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 267 2.9602605849504471e-02</internalNodes>
          <leafValues>
            -1.5583258867263794e-01 6.8996644020080566e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 144 1.8591046333312988e-02</internalNodes>
          <leafValues>
            3.5206872224807739e-01 -5.9619015455245972e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 137 3.1888354569673538e-02</internalNodes>
          <leafValues>
            -4.6452161669731140e-01 3.6019989848136902e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 276 1.9890336319804192e-02</internalNodes>
          <leafValues>
            -5.8890694379806519e-01 2.7484476566314697e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 272 3.5785537213087082e-02</internalNodes>
          <leafValues>
            -3.6444014310836792e-01 4.2384833097457886e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 171 3.3389784395694733e-02</internalNodes>
          <leafValues>
            -4.9679115414619446e-01 3.0011394619941711e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 109 1.7280768603086472e-02</internalNodes>
          <leafValues>
            3.6484482884407043e-01 -3.6291497945785522e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 256 3.8766704499721527e-02</internalNodes>
          <leafValues>
            -2.5569698214530945e-01 4.5890298485755920e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 91 2.7794418856501579e-02</internalNodes>
          <leafValues>
            -3.1639650464057922e-01 3.5169041156768799e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 356 2.2687412798404694e-02</internalNodes>
          <leafValues>
            -4.7676536440849304e-01 2.1583864092826843e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 84 4.3383773416280746e-02</internalNodes>
          <leafValues>
            -1.9801677763462067e-01 5.5397444963455200e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 185 3.7482082843780518e-02</internalNodes>
          <leafValues>
            -3.9640232920646667e-01 2.9216948151588440e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 10 6.9366261363029480e-02</internalNodes>
          <leafValues>
            -1.6931630671024323e-01 5.9330856800079346e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 128 1.9471727311611176e-02</internalNodes>
          <leafValues>
            -4.1056692600250244e-01 2.4197265505790710e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 285 1.7909351736307144e-02</internalNodes>
          <leafValues>
            2.6627296209335327e-01 -3.2458198070526123e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 201 4.6794384717941284e-02</internalNodes>
          <leafValues>
            -1.6228257119655609e-01 5.1329153776168823e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 4 -->
    <_>
      <maxWeakCount>17</maxWeakCount>
      <stageThreshold>-9.0973609685897827e-01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 135 2.3856874555349350e-02</internalNodes>
          <leafValues>
            7.0977918803691864e-02 6.9912153482437134e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 156 1.7264183610677719e-02</internalNodes>
          <leafValues>
            3.3970844745635986e-01 -5.3391200304031372e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 234 1.9154762849211693e-02</internalNodes>
          <leafValues>
            3.1814944744110107e-01 -4.2153197526931763e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 338 4.5807309448719025e-02</internalNodes>
          <leafValues>
            -2.2852534055709839e-01 5.2349030971527100e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 326 1.6635738313198090e-02</internalNodes>
          <leafValues>
            -6.2276625633239746e-01 1.9962249696254730e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 97 4.7933962196111679e-02</internalNodes>
          <leafValues>
            -2.4796873331069946e-01 4.5297086238861084e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 204 2.7708321809768677e-02</internalNodes>
          <leafValues>
            -5.3516471385955811e-01 2.0813040435314178e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 345 3.3218063414096832e-02</internalNodes>
          <leafValues>
            1.8686634302139282e-01 -5.7147103548049927e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 94 2.5231953710317612e-02</internalNodes>
          <leafValues>
            -3.5735231637954712e-01 2.6227018237113953e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 252 6.5917596220970154e-02</internalNodes>
          <leafValues>
            -1.7371983826160431e-01 5.5358475446701050e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 397 1.9202515482902527e-02</internalNodes>
          <leafValues>
            -4.5851269364356995e-01 2.2202965617179871e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 319 1.5277124941349030e-02</internalNodes>
          <leafValues>
            -5.7695335149765015e-01 1.6361239552497864e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 258 1.5292022377252579e-02</internalNodes>
          <leafValues>
            2.1122620999813080e-01 -4.5840665698051453e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 408 3.2990656793117523e-02</internalNodes>
          <leafValues>
            -4.8911759257316589e-01 2.0797151327133179e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 106 3.8935579359531403e-02</internalNodes>
          <leafValues>
            -2.1567581593990326e-01 4.5372936129570007e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 375 2.3524215444922447e-02</internalNodes>
          <leafValues>
            1.9085225462913513e-01 -4.2954295873641968e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 150 3.5664834082126617e-02</internalNodes>
          <leafValues>
            -2.2717741131782532e-01 3.6430290341377258e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 5 -->
    <_>
      <maxWeakCount>23</maxWeakCount>
      <stageThreshold>-1.1312623023986816e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 360 2.7237605303525925e-02</internalNodes>
          <leafValues>
            -1.3005600869655609e-01 6.2063622474670410e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 35 1.5457786619663239e-02</internalNodes>
          <leafValues>
            3.9330831170082092e-01 -3.0358260869979858e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 222 4.9070447683334351e-02</internalNodes>
          <leafValues>
            -2.3332066833972931e-01 4.7401455044746399e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 304 1.8720947206020355e-02</internalNodes>
          <leafValues>
            2.2771716117858887e-01 -5.6522828340530396e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 202 3.8210235536098480e-02</internalNodes>
          <leafValues>
            -2.7802225947380066e-01 4.3542638421058655e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 92 2.6717394590377808e-02</internalNodes>
          <leafValues>
            -3.6837801337242126e-01 2.9116657376289368e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 151 2.7231091633439064e-02</internalNodes>
          <leafValues>
            -4.2188262939453125e-01 2.2320161759853363e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 215 4.3489985167980194e-02</internalNodes>
          <leafValues>
            -2.3963752388954163e-01 4.7450444102287292e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 374 5.8235637843608856e-02</internalNodes>
          <leafValues>
            -1.8371292948722839e-01 4.0496450662612915e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 76 2.9798872768878937e-02</internalNodes>
          <leafValues>
            -2.4578784406185150e-01 3.2878914475440979e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 111 4.7885790467262268e-02</internalNodes>
          <leafValues>
            -1.2591503560543060e-01 6.2480401992797852e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 188 3.4020651131868362e-02</internalNodes>
          <leafValues>
            -4.4559559226036072e-01 1.6698820888996124e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 164 1.3041117228567600e-02</internalNodes>
          <leafValues>
            -5.3376603126525879e-01 1.4401918649673462e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 272 2.4463854730129242e-02</internalNodes>
          <leafValues>
            -4.0139153599739075e-01 1.7714020609855652e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 284 2.2825594991445541e-02</internalNodes>
          <leafValues>
            1.7576573789119720e-01 -4.0140661597251892e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 319 1.1311025358736515e-02</internalNodes>
          <leafValues>
            -7.0247244834899902e-01 9.1130860149860382e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 84 3.7818603217601776e-02</internalNodes>
          <leafValues>
            -2.0045366883277893e-01 3.6091402173042297e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 123 1.0495018213987350e-02</internalNodes>
          <leafValues>
            3.0858317017555237e-01 -2.4268315732479095e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 389 1.2330400757491589e-02</internalNodes>
          <leafValues>
            -7.1054571866989136e-01 8.9192166924476624e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 115 4.7990530729293823e-02</internalNodes>
          <leafValues>
            -1.7858967185020447e-01 3.6234906315803528e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 305 2.1023772656917572e-02</internalNodes>
          <leafValues>
            1.5675763785839081e-01 -4.3989735841751099e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 254 3.8850005716085434e-02</internalNodes>
          <leafValues>
            -1.8820044398307800e-01 3.4962168335914612e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 347 2.8050413355231285e-02</internalNodes>
          <leafValues>
            -3.0509811639785767e-01 1.9749833643436432e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 6 -->
    <_>
      <maxWeakCount>27</maxWeakCount>
      <stageThreshold>-1.0298318862915039e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 237 2.0085353404283524e-02</internalNodes>
          <leafValues>
            6.8383973836898804e-01 1.0699717700481415e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 45 4.8421140760183334e-02</internalNodes>
          <leafValues>
            -1.3240151107311249e-01 5.1609915494918823e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 82 3.8163051009178162e-02</internalNodes>
          <leafValues>
            -3.4365755319595337e-01 2.8352063894271851e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 158 1.2671290896832943e-02</internalNodes>
          <leafValues>
            2.1707102656364441e-01 -4.6296969056129456e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 219 4.9968473613262177e-02</internalNodes>
          <leafValues>
            -2.0395633578300476e-01 4.8772707581520081e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 29 3.6861550062894821e-02</internalNodes>
          <leafValues>
            -2.4139428138732910e-01 3.5726866126060486e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 256 4.2565550655126572e-02</internalNodes>
          <leafValues>
            -2.1208253502845764e-01 3.8207599520683289e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 188 5.0948105752468109e-02</internalNodes>
          <leafValues>
            -3.5843509435653687e-01 2.3258960247039795e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 281 2.1425981074571609e-02</internalNodes>
          <leafValues>
            -5.2376288175582886e-01 1.5590295195579529e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 332 1.3647775165736675e-02</internalNodes>
          <leafValues>
            -6.1688733100891113e-01 1.0927138477563858e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 260 1.8924936652183533e-02</internalNodes>
          <leafValues>
            2.1575494110584259e-01 -3.6787825822830200e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 380 5.5247403681278229e-02</internalNodes>
          <leafValues>
            -1.9455035030841827e-01 3.4366169571876526e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 335 2.0190875977277756e-02</internalNodes>
          <leafValues>
            -3.6711192131042480e-01 1.8107001483440399e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 346 3.7939853966236115e-02</internalNodes>
          <leafValues>
            1.3768656551837921e-01 -4.6894323825836182e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 69 9.1459471732378006e-03</internalNodes>
          <leafValues>
            3.5288289189338684e-01 -2.1019902825355530e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 399 9.7939893603324890e-03</internalNodes>
          <leafValues>
            -8.4028327465057373e-01 7.8037321567535400e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 166 1.6496714204549789e-02</internalNodes>
          <leafValues>
            -5.3407979011535645e-01 1.1492820084095001e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 24 3.6862336099147797e-02</internalNodes>
          <leafValues>
            -2.0891386270523071e-01 2.9305332899093628e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 374 3.9162468165159225e-02</internalNodes>
          <leafValues>
            -2.3828795552253723e-01 2.3572438955307007e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 106 5.3227715194225311e-02</internalNodes>
          <leafValues>
            -1.1534099280834198e-01 5.0998371839523315e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 292 3.6753699183464050e-02</internalNodes>
          <leafValues>
            -2.2679540514945984e-01 2.5717708468437195e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 377 8.3144977688789368e-03</internalNodes>
          <leafValues>
            -6.3354510068893433e-01 8.7934792041778564e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 33 7.2001896798610687e-02</internalNodes>
          <leafValues>
            -1.1218450218439102e-01 4.8822814226150513e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 230 1.4469072222709656e-02</internalNodes>
          <leafValues>
            2.1301060914993286e-01 -2.1902599930763245e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 403 9.8585635423660278e-03</internalNodes>
          <leafValues>
            -5.5979317426681519e-01 8.5896357893943787e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 220 2.3483691737055779e-02</internalNodes>
          <leafValues>
            1.1298619210720062e-01 -3.8618877530097961e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 178 5.9933196753263474e-03</internalNodes>
          <leafValues>
            -5.3726577758789062e-01 9.0948425233364105e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 7 -->
    <_>
      <maxWeakCount>34</maxWeakCount>
      <stageThreshold>-1.0942479372024536e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 235 3.6536104977130890e-02</internalNodes>
          <leafValues>
            1.9647644460201263e-01 6.9540560245513916e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 371 3.2121933996677399e-02</internalNodes>
          <leafValues>
            -1.6034141182899475e-01 4.1880574822425842e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 142 4.3577149510383606e-02</internalNodes>
          <leafValues>
            -4.5058783888816833e-01 2.1058270335197449e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 222 2.8153307735919952e-02</internalNodes>
          <leafValues>
            -4.0950176119804382e-01 2.0908586680889130e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 316 4.3106514960527420e-02</internalNodes>
          <leafValues>
            -1.9041596353054047e-01 4.3768414855003357e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 122 7.4041862972080708e-03</internalNodes>
          <leafValues>
            4.1894209384918213e-01 -1.9011391699314117e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 157 1.6510343179106712e-02</internalNodes>
          <leafValues>
            1.4369004964828491e-01 -4.7289690375328064e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 162 1.2839104980230331e-02</internalNodes>
          <leafValues>
            -4.6771577000617981e-01 1.4251045882701874e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 92 1.6060426831245422e-02</internalNodes>
          <leafValues>
            -5.1332229375839233e-01 1.2367062270641327e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 329 2.3882389068603516e-02</internalNodes>
          <leafValues>
            1.4602147042751312e-01 -4.4274711608886719e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 103 3.1255017966032028e-02</internalNodes>
          <leafValues>
            -3.5572269558906555e-01 1.7880403995513916e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 6 1.6088826581835747e-02</internalNodes>
          <leafValues>
            2.3779444396495819e-01 -2.4586609005928040e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 343 4.1388049721717834e-02</internalNodes>
          <leafValues>
            8.8665485382080078e-02 -6.4651757478713989e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 394 2.7607444673776627e-02</internalNodes>
          <leafValues>
            1.1070463806390762e-01 -4.6220114827156067e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 361 1.9766032695770264e-02</internalNodes>
          <leafValues>
            -4.7855651378631592e-01 1.0003557056188583e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 30 4.1242823004722595e-02</internalNodes>
          <leafValues>
            -1.4716184139251709e-01 3.5464936494827271e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 154 4.5376919209957123e-02</internalNodes>
          <leafValues>
            -2.6911497116088867e-01 1.9488053023815155e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 287 1.5769151970744133e-02</internalNodes>
          <leafValues>
            2.2650752961635590e-01 -2.4712686240673065e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 255 2.6672195643186569e-02</internalNodes>
          <leafValues>
            -1.9842943549156189e-01 2.5844731926918030e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 112 3.2237760722637177e-02</internalNodes>
          <leafValues>
            -1.9061625003814697e-01 2.6821178197860718e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 74 1.9332654774188995e-02</internalNodes>
          <leafValues>
            1.6823272407054901e-01 -3.0788516998291016e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 243 7.3547840118408203e-02</internalNodes>
          <leafValues>
            9.1615833342075348e-02 -5.0179886817932129e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 21 1.2525960803031921e-02</internalNodes>
          <leafValues>
            2.3779673874378204e-01 -2.0396140217781067e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 324 4.9661640077829361e-03</internalNodes>
          <leafValues>
            -7.6614838838577271e-01 6.1269875615835190e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 175 2.2441711276769638e-02</internalNodes>
          <leafValues>
            1.3196066021919250e-01 -3.5895580053329468e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 372 1.0201791301369667e-02</internalNodes>
          <leafValues>
            -6.6678810119628906e-01 6.5755382180213928e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 46 1.6904029995203018e-02</internalNodes>
          <leafValues>
            1.8288742005825043e-01 -2.4392281472682953e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 326 2.4186503142118454e-02</internalNodes>
          <leafValues>
            -2.7055114507675171e-01 1.6063591837882996e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 266 3.5995054990053177e-02</internalNodes>
          <leafValues>
            -1.9926220178604126e-01 2.1623481810092926e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 386 3.7790406495332718e-02</internalNodes>
          <leafValues>
            -1.5248920023441315e-01 2.7407574653625488e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 185 1.8160825595259666e-02</internalNodes>
          <leafValues>
            -5.6231391429901123e-01 8.5356920957565308e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 389 2.3590657860040665e-02</internalNodes>
          <leafValues>
            -2.7612343430519104e-01 1.6488939523696899e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 41 3.9401575922966003e-02</internalNodes>
          <leafValues>
            -1.5084822475910187e-01 3.0135068297386169e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 196 7.5395773164927959e-03</internalNodes>
          <leafValues>
            -7.2811645269393921e-01 5.9477921575307846e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 8 -->
    <_>
      <maxWeakCount>40</maxWeakCount>
      <stageThreshold>-1.0667979717254639e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 135 3.3803708851337433e-02</internalNodes>
          <leafValues>
            2.0294502377510071e-01 6.8919557332992554e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 235 4.4259797781705856e-02</internalNodes>
          <leafValues>
            -1.8319618701934814e-01 4.0606230497360229e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 305 1.7499379813671112e-02</internalNodes>
          <leafValues>
            1.8021285533905029e-01 -5.0267386436462402e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 225 1.7421444877982140e-02</internalNodes>
          <leafValues>
            2.0643070340156555e-01 -3.6459740996360779e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 272 4.1447594761848450e-02</internalNodes>
          <leafValues>
            -2.1964523196220398e-01 3.1299889087677002e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 3 1.0844048112630844e-02</internalNodes>
          <leafValues>
            2.5269708037376404e-01 -2.4235825240612030e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 136 4.3476268649101257e-02</internalNodes>
          <leafValues>
            -1.7678391933441162e-01 3.5271537303924561e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 165 3.1896732747554779e-02</internalNodes>
          <leafValues>
            -3.8901832699775696e-01 1.5926574170589447e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 216 3.3719871193170547e-02</internalNodes>
          <leafValues>
            -1.6785654425621033e-01 3.4403425455093384e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 94 1.6506414860486984e-02</internalNodes>
          <leafValues>
            -4.5513299107551575e-01 1.3159599900245667e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 309 5.6165158748626709e-02</internalNodes>
          <leafValues>
            -1.8263947963714600e-01 3.1392019987106323e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 150 2.9371824115514755e-02</internalNodes>
          <leafValues>
            -2.4799329042434692e-01 2.2645363211631775e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 277 2.0845592021942139e-02</internalNodes>
          <leafValues>
            -5.6128650903701782e-01 1.0008560866117477e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 125 1.3332892209291458e-02</internalNodes>
          <leafValues>
            1.8940114974975586e-01 -2.5059500336647034e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 111 5.9525385499000549e-02</internalNodes>
          <leafValues>
            -8.4326848387718201e-02 5.9320241212844849e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 91 3.2087143510580063e-02</internalNodes>
          <leafValues>
            -2.1008726954460144e-01 2.2367969155311584e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 171 2.8348915278911591e-02</internalNodes>
          <leafValues>
            -3.8646319508552551e-01 1.2766058743000031e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 280 3.3310860395431519e-02</internalNodes>
          <leafValues>
            -1.7685657739639282e-01 2.8453165292739868e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 27 3.9370644837617874e-02</internalNodes>
          <leafValues>
            -1.6512715816497803e-01 2.8704455494880676e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 147 8.3449587225914001e-02</internalNodes>
          <leafValues>
            -1.1813610792160034e-01 4.2421570420265198e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 352 1.7090287059545517e-02</internalNodes>
          <leafValues>
            -5.5372512340545654e-01 8.9624352753162384e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 384 8.3843227475881577e-03</internalNodes>
          <leafValues>
            -5.9863013029098511e-01 6.8536214530467987e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 271 1.4468373730778694e-02</internalNodes>
          <leafValues>
            -4.8452723026275635e-01 7.8063711524009705e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 330 2.4037588387727737e-02</internalNodes>
          <leafValues>
            1.3170041143894196e-01 -3.4924519062042236e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 84 5.7554122060537338e-02</internalNodes>
          <leafValues>
            -9.5674909651279449e-02 4.4177547097206116e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 257 4.9626484513282776e-02</internalNodes>
          <leafValues>
            -1.9171085953712463e-01 2.3156909644603729e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 142 6.7105665802955627e-02</internalNodes>
          <leafValues>
            -1.9868017733097076e-01 2.1714982390403748e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 373 2.4514596909284592e-02</internalNodes>
          <leafValues>
            -2.2806458175182343e-01 1.7530490458011627e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 288 4.5362018048763275e-02</internalNodes>
          <leafValues>
            6.0322988778352737e-02 -5.9761273860931396e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 241 1.9888341426849365e-02</internalNodes>
          <leafValues>
            1.3828387856483459e-01 -2.7181312441825867e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 348 7.2217527776956558e-03</internalNodes>
          <leafValues>
            -6.4959281682968140e-01 5.4709441959857941e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 205 3.6005795001983643e-02</internalNodes>
          <leafValues>
            -1.1102730780839920e-01 3.4471026062965393e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 320 1.3334888033568859e-02</internalNodes>
          <leafValues>
            -4.8756721615791321e-01 7.6772011816501617e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 259 2.2652450948953629e-02</internalNodes>
          <leafValues>
            1.2517645955085754e-01 -3.1397601962089539e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 78 3.0479673296213150e-02</internalNodes>
          <leafValues>
            -1.6392000019550323e-01 2.2823038697242737e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 294 3.2174132764339447e-02</internalNodes>
          <leafValues>
            7.2459667921066284e-02 -5.0438696146011353e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 250 2.0307323429733515e-03</internalNodes>
          <leafValues>
            -6.0100507736206055e-01 6.2978878617286682e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 193 2.4996384978294373e-02</internalNodes>
          <leafValues>
            9.4787009060382843e-02 -3.3236780762672424e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 184 9.4616198912262917e-03</internalNodes>
          <leafValues>
            -6.1198323965072632e-01 5.4307788610458374e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 108 6.1800219118595123e-02</internalNodes>
          <leafValues>
            -8.1825248897075653e-02 4.7097754478454590e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 9 -->
    <_>
      <maxWeakCount>49</maxWeakCount>
      <stageThreshold>-1.1365391016006470e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 105 2.9898788779973984e-02</internalNodes>
          <leafValues>
            6.4444082975387573e-01 1.5926423668861389e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 135 4.3845266103744507e-02</internalNodes>
          <leafValues>
            -1.3981340825557709e-01 4.3804591894149780e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 236 4.9079112708568573e-02</internalNodes>
          <leafValues>
            -1.8019071221351624e-01 3.8800683617591858e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 191 1.3924567960202694e-02</internalNodes>
          <leafValues>
            1.5545988082885742e-01 -4.4299390912055969e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 349 2.7049161493778229e-02</internalNodes>
          <leafValues>
            -5.1695418357849121e-01 1.1857955157756805e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 213 1.3575891032814980e-02</internalNodes>
          <leafValues>
            2.0789936184883118e-01 -2.8043094277381897e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 345 2.7712848037481308e-02</internalNodes>
          <leafValues>
            1.5637956559658051e-01 -3.6645907163619995e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 66 1.3193745166063309e-02</internalNodes>
          <leafValues>
            2.3904225230216980e-01 -2.2799262404441833e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 29 5.1669768989086151e-02</internalNodes>
          <leafValues>
            -1.5179835259914398e-01 3.3588418364524841e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 389 2.0016321912407875e-02</internalNodes>
          <leafValues>
            -3.7837767601013184e-01 1.4262075722217560e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 276 1.7536439001560211e-02</internalNodes>
          <leafValues>
            -4.7328859567642212e-01 1.0360060632228851e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 198 1.9453909248113632e-02</internalNodes>
          <leafValues>
            1.6739392280578613e-01 -3.2834252715110779e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 204 4.7895789146423340e-02</internalNodes>
          <leafValues>
            -2.8093844652175903e-01 1.8732315301895142e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 386 3.6252494901418686e-02</internalNodes>
          <leafValues>
            -1.9594472646713257e-01 2.9072594642639160e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 87 1.1202009394764900e-02</internalNodes>
          <leafValues>
            2.5409469008445740e-01 -1.6037191450595856e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 279 1.8624890595674515e-02</internalNodes>
          <leafValues>
            -3.8854196667671204e-01 1.1531944572925568e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 381 2.0409123972058296e-02</internalNodes>
          <leafValues>
            1.5490253269672394e-01 -2.9211193323135376e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 396 1.4438996091485023e-02</internalNodes>
          <leafValues>
            -5.8407187461853027e-01 7.3514230549335480e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 44 4.6211585402488708e-02</internalNodes>
          <leafValues>
            -1.2724950909614563e-01 3.5867068171501160e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 282 2.1834429353475571e-02</internalNodes>
          <leafValues>
            -4.8774343729019165e-01 8.8088802993297577e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 302 1.4306938275694847e-02</internalNodes>
          <leafValues>
            -3.9986306428909302e-01 9.5921404659748077e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 7 1.5015116892755032e-02</internalNodes>
          <leafValues>
            2.0611824095249176e-01 -2.1622006595134735e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 26 3.4759372472763062e-02</internalNodes>
          <leafValues>
            -1.5017031133174896e-01 2.4653677642345428e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 209 1.6483131796121597e-02</internalNodes>
          <leafValues>
            1.1389654874801636e-01 -3.4744158387184143e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 155 4.5423090457916260e-02</internalNodes>
          <leafValues>
            1.1709145456552505e-01 -3.6127036809921265e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 297 9.8602399230003357e-03</internalNodes>
          <leafValues>
            -5.9805208444595337e-01 6.4656950533390045e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 328 4.4437684118747711e-02</internalNodes>
          <leafValues>
            -1.7433795332908630e-01 2.1107853949069977e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 272 2.4257807061076164e-02</internalNodes>
          <leafValues>
            -3.0487525463104248e-01 1.1572900414466858e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 341 1.5823580324649811e-02</internalNodes>
          <leafValues>
            -4.1735991835594177e-01 8.0870188772678375e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 101 5.5421203374862671e-02</internalNodes>
          <leafValues>
            -9.2224739491939545e-02 4.2088964581489563e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 238 9.7968988120555878e-03</internalNodes>
          <leafValues>
            3.0837103724479675e-01 -1.1570020020008087e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 242 3.4521829336881638e-02</internalNodes>
          <leafValues>
            -1.5905003249645233e-01 2.4456644058227539e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 166 1.8127705901861191e-02</internalNodes>
          <leafValues>
            -4.1122585535049438e-01 9.3564964830875397e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 325 9.5051340758800507e-03</internalNodes>
          <leafValues>
            -6.7785978317260742e-01 4.6322401612997055e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 226 5.9901960194110870e-02</internalNodes>
          <leafValues>
            -6.5578587353229523e-02 5.8266454935073853e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 100 5.4452434182167053e-02</internalNodes>
          <leafValues>
            -7.6557263731956482e-02 4.4653451442718506e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 255 3.6866582930088043e-02</internalNodes>
          <leafValues>
            -1.0719012469053268e-01 2.9660055041313171e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 374 4.2660210281610489e-02</internalNodes>
          <leafValues>
            -1.7993980646133423e-01 1.8417468667030334e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 378 8.9137665927410126e-03</internalNodes>
          <leafValues>
            -6.3697338104248047e-01 5.4773576557636261e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 114 1.1400089599192142e-02</internalNodes>
          <leafValues>
            2.5295448303222656e-01 -1.3493345677852631e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 148 3.3225949853658676e-02</internalNodes>
          <leafValues>
            -1.5319000184535980e-01 2.2326107323169708e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 185 4.4984422624111176e-02</internalNodes>
          <leafValues>
            -2.1095339953899384e-01 1.5410959720611572e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 214 5.1712311804294586e-02</internalNodes>
          <leafValues>
            -9.3758344650268555e-02 3.6428454518318176e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 153 3.3612012863159180e-02</internalNodes>
          <leafValues>
            -9.6304103732109070e-02 3.7952485680580139e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 286 4.1407853364944458e-02</internalNodes>
          <leafValues>
            6.4589627087116241e-02 -4.9156913161277771e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 313 8.7404683232307434e-02</internalNodes>
          <leafValues>
            5.2760947495698929e-02 -5.3573220968246460e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 70 1.8000207841396332e-02</internalNodes>
          <leafValues>
            1.3890655338764191e-01 -2.2383554279804230e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 160 2.9085248708724976e-02</internalNodes>
          <leafValues>
            8.1772908568382263e-02 -3.8127329945564270e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 169 1.9239231944084167e-02</internalNodes>
          <leafValues>
            -1.9019705057144165e-01 1.7916351556777954e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 10 -->
    <_>
      <maxWeakCount>47</maxWeakCount>
      <stageThreshold>-1.0116653442382812e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 144 2.7801316231489182e-02</internalNodes>
          <leafValues>
            5.3492093086242676e-01 -2.4647414684295654e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 203 4.5032106339931488e-02</internalNodes>
          <leafValues>
            -2.5259229540824890e-01 3.1522995233535767e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 47 5.3729001432657242e-02</internalNodes>
          <leafValues>
            -1.9453412294387817e-01 3.6114758253097534e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 18 7.1603775024414062e-02</internalNodes>
          <leafValues>
            -1.2540340423583984e-01 4.5672309398651123e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 124 9.9766831845045090e-03</internalNodes>
          <leafValues>
            2.6568776369094849e-01 -2.0229923725128174e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 215 3.3997669816017151e-02</internalNodes>
          <leafValues>
            -2.2654175758361816e-01 2.2218938171863556e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 64 5.9508979320526123e-02</internalNodes>
          <leafValues>
            -1.4012111723423004e-01 3.3521434664726257e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 335 2.0788393914699554e-02</internalNodes>
          <leafValues>
            -3.1516796350479126e-01 1.4921028912067413e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 293 2.6780268177390099e-02</internalNodes>
          <leafValues>
            1.1207921802997589e-01 -4.6000838279724121e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 81 3.4766957163810730e-02</internalNodes>
          <leafValues>
            -1.7912036180496216e-01 2.5626036524772644e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 156 1.1539075523614883e-02</internalNodes>
          <leafValues>
            1.6142164170742035e-01 -2.9306703805923462e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 227 2.6543047279119492e-02</internalNodes>
          <leafValues>
            -2.6969066262245178e-01 1.6138702630996704e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 244 1.6161387786269188e-02</internalNodes>
          <leafValues>
            2.0254437625408173e-01 -2.3378078639507294e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 228 4.5159406960010529e-02</internalNodes>
          <leafValues>
            -1.4788989722728729e-01 3.2111972570419312e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 408 3.6860406398773193e-02</internalNodes>
          <leafValues>
            -3.4088444709777832e-01 1.5263999998569489e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 168 1.5109863132238388e-02</internalNodes>
          <leafValues>
            -5.4718774557113647e-01 8.2045741379261017e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 370 2.8016595169901848e-02</internalNodes>
          <leafValues>
            -2.3323598504066467e-01 1.9090305268764496e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 88 6.3618853688240051e-02</internalNodes>
          <leafValues>
            -7.8254461288452148e-02 5.5258846282958984e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 199 6.8580904044210911e-03</internalNodes>
          <leafValues>
            -6.3956487178802490e-01 6.9815970957279205e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 232 2.0595733076334000e-02</internalNodes>
          <leafValues>
            1.2898202240467072e-01 -3.2508504390716553e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 269 2.8314685449004173e-02</internalNodes>
          <leafValues>
            -2.2404149174690247e-01 1.6563916206359863e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 4 7.9108789563179016e-02</internalNodes>
          <leafValues>
            -6.2124527990818024e-02 6.0598611831665039e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 281 1.6589259728789330e-02</internalNodes>
          <leafValues>
            -5.2956002950668335e-01 8.4093615412712097e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 51 5.5094532668590546e-02</internalNodes>
          <leafValues>
            -1.0222712159156799e-01 3.9321076869964600e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 390 1.0503903031349182e-02</internalNodes>
          <leafValues>
            -5.9208440780639648e-01 6.7801252007484436e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 210 2.3749233223497868e-03</internalNodes>
          <leafValues>
            -5.3886908292770386e-01 5.9363964945077896e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 131 1.8814763054251671e-02</internalNodes>
          <leafValues>
            -3.1261232495307922e-01 1.1180796474218369e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 159 2.0527314394712448e-02</internalNodes>
          <leafValues>
            1.2992724776268005e-01 -2.8644782304763794e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 73 6.1145043000578880e-03</internalNodes>
          <leafValues>
            3.3627521991729736e-01 -1.0495150089263916e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 89 3.0952483415603638e-02</internalNodes>
          <leafValues>
            -1.6427862644195557e-01 2.0351713895797729e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 171 1.5921674668788910e-02</internalNodes>
          <leafValues>
            -5.5327165126800537e-01 5.7676360011100769e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 368 4.0456190705299377e-02</internalNodes>
          <leafValues>
            6.0299206525087357e-02 -5.2531564235687256e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 59 1.3443487696349621e-02</internalNodes>
          <leafValues>
            1.4375637471675873e-01 -2.1119056642055511e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 385 8.6331767961382866e-03</internalNodes>
          <leafValues>
            -4.4204819202423096e-01 7.8406274318695068e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 48 5.8937132358551025e-02</internalNodes>
          <leafValues>
            -8.8852100074291229e-02 3.5532650351524353e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 292 2.8679285198450089e-02</internalNodes>
          <leafValues>
            -2.2101837396621704e-01 1.4601917564868927e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 398 1.2925801798701286e-02</internalNodes>
          <leafValues>
            -5.9588652849197388e-01 5.0016134977340698e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 52 5.3128455765545368e-03</internalNodes>
          <leafValues>
            2.6390919089317322e-01 -1.3713072240352631e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 142 5.8427557349205017e-02</internalNodes>
          <leafValues>
            -2.0995871722698212e-01 1.5187016129493713e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 312 1.2560402974486351e-02</internalNodes>
          <leafValues>
            -5.2102887630462646e-01 6.0699488967657089e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1 2.1175123751163483e-02</internalNodes>
          <leafValues>
            1.2936566770076752e-01 -2.5762283802032471e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 355 1.7020858824253082e-02</internalNodes>
          <leafValues>
            -3.3331403136253357e-01 9.9338315427303314e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 200 7.2212636470794678e-02</internalNodes>
          <leafValues>
            6.2653824687004089e-02 -4.8250997066497803e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 194 7.3252543807029724e-02</internalNodes>
          <leafValues>
            -1.1153536289930344e-01 3.1560242176055908e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 130 2.4769719690084457e-02</internalNodes>
          <leafValues>
            -2.2570444643497467e-01 1.6274058818817139e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 31 1.4226272702217102e-02</internalNodes>
          <leafValues>
            1.4799727499485016e-01 -2.0190501213073730e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 404 1.0724326595664024e-02</internalNodes>
          <leafValues>
            -6.2658333778381348e-01 4.9919847398996353e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 11 -->
    <_>
      <maxWeakCount>54</maxWeakCount>
      <stageThreshold>-9.9449658393859863e-01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 95 1.8665460869669914e-02</internalNodes>
          <leafValues>
            6.7694538831710815e-01 2.6328191161155701e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 245 6.2435247004032135e-02</internalNodes>
          <leafValues>
            -6.6552631556987762e-02 5.5264985561370850e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 71 1.1597499251365662e-02</internalNodes>
          <leafValues>
            2.3295506834983826e-01 -2.4157637357711792e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 80 5.1069814711809158e-02</internalNodes>
          <leafValues>
            -1.7527097463607788e-01 3.2310706377029419e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 382 6.4036197960376740e-02</internalNodes>
          <leafValues>
            -1.5130068361759186e-01 3.3947771787643433e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 256 4.8696346580982208e-02</internalNodes>
          <leafValues>
            -1.5965068340301514e-01 3.2663738727569580e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 61 5.3191006183624268e-02</internalNodes>
          <leafValues>
            -1.5307182073593140e-01 3.4863162040710449e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 174 1.6555204987525940e-02</internalNodes>
          <leafValues>
            1.2112359702587128e-01 -4.6862232685089111e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 86 2.0337283611297607e-02</internalNodes>
          <leafValues>
            -4.5971450209617615e-01 1.0953056812286377e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 365 1.1595044285058975e-02</internalNodes>
          <leafValues>
            -4.7979262471199036e-01 7.6207697391510010e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 283 2.2066965699195862e-02</internalNodes>
          <leafValues>
            -4.8033183813095093e-01 7.8574642539024353e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 117 1.0200021788477898e-02</internalNodes>
          <leafValues>
            2.2034211456775665e-01 -1.8268603086471558e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 315 4.7522459179162979e-02</internalNodes>
          <leafValues>
            -1.0461943596601486e-01 3.5927659273147583e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 308 2.8317932039499283e-02</internalNodes>
          <leafValues>
            8.9004360139369965e-02 -4.3938761949539185e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 334 2.4015534669160843e-02</internalNodes>
          <leafValues>
            -3.4306117892265320e-01 1.1998588591814041e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 31 2.1171212196350098e-02</internalNodes>
          <leafValues>
            1.1449780315160751e-01 -3.3492365479469299e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 383 8.7842330336570740e-02</internalNodes>
          <leafValues>
            6.6495500504970551e-02 -5.6651270389556885e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 9 1.9201990216970444e-02</internalNodes>
          <leafValues>
            -2.5308519601821899e-01 1.5692935883998871e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 370 3.3523544669151306e-02</internalNodes>
          <leafValues>
            -1.5270863473415375e-01 2.3095367848873138e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 171 4.4334933161735535e-02</internalNodes>
          <leafValues>
            -2.3225110769271851e-01 1.5393845736980438e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 326 1.7828078940510750e-02</internalNodes>
          <leafValues>
            -4.3410968780517578e-01 8.7237603962421417e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 366 2.7970856055617332e-02</internalNodes>
          <leafValues>
            9.8533637821674347e-02 -3.8462772965431213e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 340 8.2091763615608215e-02</internalNodes>
          <leafValues>
            -1.1092124134302139e-01 3.3764466643333435e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 289 4.6008333563804626e-02</internalNodes>
          <leafValues>
            -1.1516174674034119e-01 2.9743531346321106e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 341 1.3903729617595673e-02</internalNodes>
          <leafValues>
            -5.0074380636215210e-01 7.9755537211894989e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 182 2.5103049352765083e-02</internalNodes>
          <leafValues>
            -1.6730682551860809e-01 2.2408586740493774e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 67 2.9331450350582600e-03</internalNodes>
          <leafValues>
            -4.8622006177902222e-01 7.0578098297119141e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 190 1.3889933004975319e-02</internalNodes>
          <leafValues>
            1.0139058530330658e-01 -3.1855314970016479e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 83 6.3016422092914581e-02</internalNodes>
          <leafValues>
            -7.6343230903148651e-02 4.4974201917648315e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 310 2.3791689425706863e-02</internalNodes>
          <leafValues>
            -2.1932505071163177e-01 1.4710763096809387e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 307 2.1487560123205185e-02</internalNodes>
          <leafValues>
            1.2838844954967499e-01 -2.5909209251403809e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 189 3.4274224191904068e-02</internalNodes>
          <leafValues>
            -9.5347620546817780e-02 3.7022551894187927e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 303 7.1915900334715843e-03</internalNodes>
          <leafValues>
            -7.3691523075103760e-01 4.6294290572404861e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 172 2.5272460654377937e-02</internalNodes>
          <leafValues>
            9.5943376421928406e-02 -3.3830648660659790e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 409 2.0854394882917404e-02</internalNodes>
          <leafValues>
            1.3279491662979126e-01 -2.6329138875007629e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 344 1.4316380023956299e-02</internalNodes>
          <leafValues>
            -4.5482146739959717e-01 7.0914179086685181e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 231 2.8929179534316063e-02</internalNodes>
          <leafValues>
            6.6486880183219910e-02 -4.5508390665054321e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 20 1.0630194097757339e-02</internalNodes>
          <leafValues>
            1.8809454143047333e-01 -1.6614237427711487e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 350 9.7995914518833160e-02</internalNodes>
          <leafValues>
            5.7244099676609039e-02 -5.8636969327926636e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 298 5.1458822563290596e-03</internalNodes>
          <leafValues>
            -7.1203124523162842e-01 3.7936817854642868e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 392 2.3478031158447266e-02</internalNodes>
          <leafValues>
            -3.0215761065483093e-01 8.8247083127498627e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 17 3.6021061241626740e-02</internalNodes>
          <leafValues>
            -1.1509682238101959e-01 2.6338168978691101e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 278 3.9570771157741547e-02</internalNodes>
          <leafValues>
            -1.2997727096080780e-01 2.3227843642234802e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 185 4.6252459287643433e-02</internalNodes>
          <leafValues>
            -2.0763714611530304e-01 1.5233637392520905e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 163 1.0493120178580284e-02</internalNodes>
          <leafValues>
            -3.7226682901382446e-01 7.4590153992176056e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 262 3.8044206798076630e-02</internalNodes>
          <leafValues>
            -1.0903593897819519e-01 2.6904660463333130e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 322 1.0875662788748741e-02</internalNodes>
          <leafValues>
            -6.7161810398101807e-01 4.4944703578948975e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 60 2.4129729717969894e-02</internalNodes>
          <leafValues>
            9.5554195344448090e-02 -2.9388493299484253e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 327 2.7652573771774769e-03</internalNodes>
          <leafValues>
            -6.4378261566162109e-01 4.3076869100332260e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 90 4.8550616949796677e-02</internalNodes>
          <leafValues>
            -8.8114514946937561e-02 3.4373363852500916e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 194 6.7535176873207092e-02</internalNodes>
          <leafValues>
            -1.0665769875049591e-01 2.4410410225391388e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 153 4.6423368155956268e-02</internalNodes>
          <leafValues>
            -5.9944093227386475e-02 4.7295922040939331e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 237 1.0667273774743080e-02</internalNodes>
          <leafValues>
            2.1510368585586548e-01 -1.1941892653703690e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 396 1.6616852954030037e-02</internalNodes>
          <leafValues>
            -4.1614437103271484e-01 7.0346660912036896e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 12 -->
    <_>
      <maxWeakCount>64</maxWeakCount>
      <stageThreshold>-1.0546809434890747e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 102 3.2909393310546875e-02</internalNodes>
          <leafValues>
            1.3556618988513947e-01 5.9496569633483887e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 18 5.7673007249832153e-02</internalNodes>
          <leafValues>
            -1.1456339061260223e-01 4.2552378773689270e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 15 1.1992406100034714e-02</internalNodes>
          <leafValues>
            2.4031037092208862e-01 -2.4737285077571869e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 56 6.1344116926193237e-02</internalNodes>
          <leafValues>
            -1.4613483846187592e-01 3.6417004466056824e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 23 3.5104401409626007e-02</internalNodes>
          <leafValues>
            -3.8308736681938171e-01 1.2162487953901291e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 338 5.5985964834690094e-02</internalNodes>
          <leafValues>
            -1.3175597786903381e-01 3.9519834518432617e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 265 4.5360349118709564e-02</internalNodes>
          <leafValues>
            -1.1925575137138367e-01 3.5105460882186890e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 150 2.2281080484390259e-02</internalNodes>
          <leafValues>
            -3.0465376377105713e-01 1.4432303607463837e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 224 3.2830841839313507e-02</internalNodes>
          <leafValues>
            -2.9900938272476196e-01 1.3699726760387421e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 248 1.4167425222694874e-02</internalNodes>
          <leafValues>
            1.2152181565761566e-01 -3.4019640088081360e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 240 3.3475767821073532e-02</internalNodes>
          <leafValues>
            -1.1398931592702866e-01 3.3892172574996948e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 253 5.6810773909091949e-02</internalNodes>
          <leafValues>
            -1.3839827477931976e-01 2.7739012241363525e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 78 2.0613271743059158e-02</internalNodes>
          <leafValues>
            -2.8242266178131104e-01 1.4313375949859619e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 308 1.6712650656700134e-02</internalNodes>
          <leafValues>
            1.6265860199928284e-01 -2.4141319096088409e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 201 2.9832722619175911e-02</internalNodes>
          <leafValues>
            -2.1411854028701782e-01 1.7730319499969482e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 121 4.7100419178605080e-03</internalNodes>
          <leafValues>
            3.5185843706130981e-01 -1.0267037153244019e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 127 2.0623035728931427e-02</internalNodes>
          <leafValues>
            1.2791988253593445e-01 -2.8007981181144714e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 58 6.1587020754814148e-02</internalNodes>
          <leafValues>
            -7.1944102644920349e-02 4.9679103493690491e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 275 1.8270082771778107e-02</internalNodes>
          <leafValues>
            -4.0494844317436218e-01 9.3867540359497070e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 34 5.1193911582231522e-02</internalNodes>
          <leafValues>
            -9.8075203597545624e-02 3.5078194737434387e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 134 1.2031028047204018e-02</internalNodes>
          <leafValues>
            1.9643558561801910e-01 -1.6022813320159912e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 195 5.9083811938762665e-03</internalNodes>
          <leafValues>
            -4.9917405843734741e-01 6.4527131617069244e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 150 4.9596227705478668e-02</internalNodes>
          <leafValues>
            -1.1082161962985992e-01 2.9465663433074951e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 343 4.8769481480121613e-02</internalNodes>
          <leafValues>
            4.2230345308780670e-02 -7.6498138904571533e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 186 2.4277199059724808e-02</internalNodes>
          <leafValues>
            7.5935915112495422e-02 -4.0026673674583435e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 206 2.5390535593032837e-02</internalNodes>
          <leafValues>
            -1.8416796624660492e-01 1.9858093559741974e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 177 1.9438743591308594e-02</internalNodes>
          <leafValues>
            1.2287277728319168e-01 -2.5645399093627930e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 391 8.8542364537715912e-03</internalNodes>
          <leafValues>
            -6.3241440057754517e-01 4.8879984766244888e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 357 3.0551470816135406e-02</internalNodes>
          <leafValues>
            -1.8552851676940918e-01 1.6086483001708984e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 50 1.8117606639862061e-02</internalNodes>
          <leafValues>
            2.3619586229324341e-01 -1.5224708616733551e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 393 2.2853232920169830e-02</internalNodes>
          <leafValues>
            -3.2127961516380310e-01 9.5281556248664856e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 88 7.7133789658546448e-02</internalNodes>
          <leafValues>
            -5.6924272328615189e-02 5.5168312788009644e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 364 1.6538575291633606e-02</internalNodes>
          <leafValues>
            -6.1036610603332520e-01 5.3948421031236649e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 166 3.3028811216354370e-02</internalNodes>
          <leafValues>
            -2.1796108782291412e-01 1.3436897099018097e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 155 7.4932515621185303e-02</internalNodes>
          <leafValues>
            6.3060440123081207e-02 -5.0771480798721313e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 30 3.7362482398748398e-02</internalNodes>
          <leafValues>
            -1.2669509649276733e-01 2.3785883188247681e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 299 7.3003470897674561e-03</internalNodes>
          <leafValues>
            -5.1063781976699829e-01 6.0329724103212357e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 263 3.3306218683719635e-02</internalNodes>
          <leafValues>
            -1.6422729194164276e-01 1.8774913251399994e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 112 4.0949136018753052e-02</internalNodes>
          <leafValues>
            -1.0705300420522690e-01 2.5951048731803894e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 53 6.0007654130458832e-02</internalNodes>
          <leafValues>
            -5.7804863899946213e-02 4.8536598682403564e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 270 8.3733513951301575e-02</internalNodes>
          <leafValues>
            -8.8066965341567993e-02 3.2750615477561951e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 149 4.8994873650372028e-03</internalNodes>
          <leafValues>
            -6.3597649335861206e-01 4.7279179096221924e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 239 1.2618229724466801e-02</internalNodes>
          <leafValues>
            1.5810568630695343e-01 -1.8534006178379059e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 16 4.9128290265798569e-03</internalNodes>
          <leafValues>
            -6.7431080341339111e-01 4.2064949870109558e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 192 6.1659682542085648e-02</internalNodes>
          <leafValues>
            7.7905796468257904e-02 -3.1210681796073914e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 191 1.0398313403129578e-02</internalNodes>
          <leafValues>
            1.3589595258235931e-01 -2.4312557280063629e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 113 2.2464931011199951e-02</internalNodes>
          <leafValues>
            9.4380743801593781e-02 -2.6920509338378906e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 229 9.9546080455183983e-03</internalNodes>
          <leafValues>
            -6.1364966630935669e-01 3.8600925356149673e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 98 5.9078827500343323e-02</internalNodes>
          <leafValues>
            -6.9042220711708069e-02 4.0226206183433533e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 358 2.0355718210339546e-02</internalNodes>
          <leafValues>
            -3.2917183637619019e-01 8.5820250213146210e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 72 4.8340834677219391e-02</internalNodes>
          <leafValues>
            -8.2636915147304535e-02 3.7525442242622375e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 68 3.7296619266271591e-03</internalNodes>
          <leafValues>
            -4.4785124063491821e-01 6.0102187097072601e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 234 1.5672549605369568e-02</internalNodes>
          <leafValues>
            1.5237241983413696e-01 -1.6926215589046478e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 221 8.1734046339988708e-02</internalNodes>
          <leafValues>
            5.4154094308614731e-02 -5.2352732419967651e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 120 1.1431444436311722e-02</internalNodes>
          <leafValues>
            1.7892888188362122e-01 -1.4085929095745087e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 351 5.8017624542117119e-03</internalNodes>
          <leafValues>
            -7.2548639774322510e-01 3.6602437496185303e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 19 6.7446351051330566e-02</internalNodes>
          <leafValues>
            -6.3306964933872223e-02 4.2737153172492981e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 318 7.9820938408374786e-03</internalNodes>
          <leafValues>
            -7.4366998672485352e-01 3.6713104695081711e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 11 6.9536261260509491e-02</internalNodes>
          <leafValues>
            -8.0821603536605835e-02 3.3922439813613892e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 145 3.7549443542957306e-02</internalNodes>
          <leafValues>
            6.9156736135482788e-02 -3.5819274187088013e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 152 1.0656273923814297e-02</internalNodes>
          <leafValues>
            -7.2513943910598755e-01 3.1234202906489372e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 183 2.5280531495809555e-02</internalNodes>
          <leafValues>
            -1.9225510954856873e-01 1.3537347316741943e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 363 4.4457316398620605e-02</internalNodes>
          <leafValues>
            3.7981312721967697e-02 -6.7479836940765381e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 249 1.2521247845143080e-04</internalNodes>
          <leafValues>
            -6.2205755710601807e-01 3.2420136034488678e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 13 -->
    <_>
      <maxWeakCount>73</maxWeakCount>
      <stageThreshold>-1.0017215013504028e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 105 2.4735962972044945e-02</internalNodes>
          <leafValues>
            6.4785188436508179e-01 2.2758445143699646e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 44 6.3518315553665161e-02</internalNodes>
          <leafValues>
            -4.1365418583154678e-02 5.4869818687438965e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 36 9.4195883721113205e-03</internalNodes>
          <leafValues>
            2.6038098335266113e-01 -1.7342963814735413e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 272 3.5093430429697037e-02</internalNodes>
          <leafValues>
            -2.2665421664714813e-01 1.9666101038455963e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 63 1.1061885394155979e-02</internalNodes>
          <leafValues>
            2.0353546738624573e-01 -2.0876882970333099e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 47 5.9598404914140701e-02</internalNodes>
          <leafValues>
            -1.3412886857986450e-01 2.8034633398056030e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 173 1.5450142323970795e-02</internalNodes>
          <leafValues>
            1.0424488037824631e-01 -3.9602130651473999e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 397 1.8680159002542496e-02</internalNodes>
          <leafValues>
            -3.8349124789237976e-01 1.0002009570598602e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 402 3.3807259052991867e-02</internalNodes>
          <leafValues>
            -3.3930355310440063e-01 1.2061641365289688e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 12 7.4018035084009171e-03</internalNodes>
          <leafValues>
            2.6364955306053162e-01 -1.4020060002803802e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 336 1.7433203756809235e-02</internalNodes>
          <leafValues>
            -3.8107573986053467e-01 9.7438894212245941e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 89 4.0178414434194565e-02</internalNodes>
          <leafValues>
            -1.2612041831016541e-01 2.7031618356704712e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 0 6.0638077557086945e-02</internalNodes>
          <leafValues>
            -1.1307135969400406e-01 2.9262986779212952e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 389 2.4623382836580276e-02</internalNodes>
          <leafValues>
            -2.2446420788764954e-01 1.4744958281517029e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 290 3.9106391370296478e-02</internalNodes>
          <leafValues>
            -1.7826075851917267e-01 2.0597907900810242e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 38 2.7834912762045860e-02</internalNodes>
          <leafValues>
            -3.8111215829849243e-01 8.9661866426467896e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 369 3.4408185631036758e-02</internalNodes>
          <leafValues>
            1.0636640340089798e-01 -3.6162582039833069e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 133 1.0959336161613464e-01</internalNodes>
          <leafValues>
            4.1240800172090530e-02 -7.1632438898086548e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 55 3.2452266663312912e-02</internalNodes>
          <leafValues>
            6.5087780356407166e-02 -4.1960519552230835e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 217 5.4920144379138947e-02</internalNodes>
          <leafValues>
            9.6366412937641144e-02 -3.1108140945434570e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 382 7.4698656797409058e-02</internalNodes>
          <leafValues>
            -1.0990877449512482e-01 2.9453867673873901e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 332 1.2666454538702965e-02</internalNodes>
          <leafValues>
            -5.5246281623840332e-01 6.3385792076587677e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 42 3.7707298994064331e-02</internalNodes>
          <leafValues>
            -1.5134002268314362e-01 1.9798284769058228e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 197 3.6982268095016479e-02</internalNodes>
          <leafValues>
            -1.8963862955570221e-01 1.6592139005661011e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 276 2.6210237294435501e-02</internalNodes>
          <leafValues>
            -2.3561659455299377e-01 1.4326202869415283e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 273 1.8246090039610863e-02</internalNodes>
          <leafValues>
            1.2638229131698608e-01 -2.4407584965229034e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 167 2.6667824015021324e-02</internalNodes>
          <leafValues>
            6.9522418081760406e-02 -4.7825774550437927e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 101 4.1815117001533508e-02</internalNodes>
          <leafValues>
            -1.1861740797758102e-01 2.5843381881713867e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 216 2.4072701111435890e-02</internalNodes>
          <leafValues>
            -1.7671245336532593e-01 1.6403034329414368e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 321 1.2120285071432590e-02</internalNodes>
          <leafValues>
            -5.5707901716232300e-01 4.8198804259300232e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 57 1.3616212643682957e-02</internalNodes>
          <leafValues>
            1.4195388555526733e-01 -1.8388766050338745e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 353 9.9695213139057159e-03</internalNodes>
          <leafValues>
            -5.6080347299575806e-01 5.1336046308279037e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 176 9.9237188696861267e-02</internalNodes>
          <leafValues>
            -8.4744565188884735e-02 3.2638099789619446e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 25 3.7445567548274994e-02</internalNodes>
          <leafValues>
            -1.1257749050855637e-01 2.4573321640491486e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 179 3.8241371512413025e-02</internalNodes>
          <leafValues>
            -1.2643416225910187e-01 1.9334000349044800e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 405 1.3448245823383331e-02</internalNodes>
          <leafValues>
            -5.4429870843887329e-01 4.9834873527288437e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 165 4.4142831116914749e-02</internalNodes>
          <leafValues>
            -1.8847377598285675e-01 1.2911646068096161e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 254 6.4688831567764282e-02</internalNodes>
          <leafValues>
            -6.9217003881931305e-02 4.0028157830238342e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 53 5.8575954288244247e-02</internalNodes>
          <leafValues>
            -6.0475941747426987e-02 4.2772307991981506e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 77 1.1222053319215775e-02</internalNodes>
          <leafValues>
            2.0112392306327820e-01 -1.3156783580780029e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 335 9.4986613839864731e-03</internalNodes>
          <leafValues>
            -6.8230766057968140e-01 3.7198062986135483e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 161 8.9841037988662720e-03</internalNodes>
          <leafValues>
            -5.8327585458755493e-01 3.5930003970861435e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 13 6.7835962399840355e-03</internalNodes>
          <leafValues>
            2.2695730626583099e-01 -1.1493884772062302e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 187 5.1899375393986702e-03</internalNodes>
          <leafValues>
            -7.1629756689071655e-01 3.4858833998441696e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 5 6.3280604779720306e-02</internalNodes>
          <leafValues>
            -6.6279791295528412e-02 3.9587023854255676e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 136 4.4401831924915314e-02</internalNodes>
          <leafValues>
            -1.0898534208536148e-01 2.1678060293197632e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 347 2.7715172618627548e-02</internalNodes>
          <leafValues>
            -2.0937801897525787e-01 1.2542441487312317e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 317 2.1837871521711349e-02</internalNodes>
          <leafValues>
            8.9339092373847961e-02 -2.6766279339790344e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 306 4.9154721200466156e-03</internalNodes>
          <leafValues>
            -4.9983385205268860e-01 5.2077054977416992e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 311 2.6585884392261505e-02</internalNodes>
          <leafValues>
            -1.6169707477092743e-01 1.4095610380172729e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 286 5.0314925611019135e-02</internalNodes>
          <leafValues>
            3.5808511078357697e-02 -6.2886255979537964e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 251 2.3879460990428925e-02</internalNodes>
          <leafValues>
            8.6062356829643250e-02 -2.5804695487022400e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 140 4.9669235944747925e-02</internalNodes>
          <leafValues>
            -4.4713549315929413e-02 5.1127582788467407e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 129 7.4203774333000183e-02</internalNodes>
          <leafValues>
            -8.5694156587123871e-02 2.5295278429985046e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 138 4.6431384980678558e-03</internalNodes>
          <leafValues>
            -6.1233133077621460e-01 3.7366155534982681e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 40 5.2247926592826843e-02</internalNodes>
          <leafValues>
            -8.4081567823886871e-02 2.7305892109870911e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 141 5.1341928541660309e-02</internalNodes>
          <leafValues>
            -1.6881528496742249e-01 1.3544729351997375e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 164 2.2766537964344025e-02</internalNodes>
          <leafValues>
            -2.3369863629341125e-01 1.0711924731731415e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 233 1.2284438125789165e-02</internalNodes>
          <leafValues>
            -4.6011406183242798e-01 5.2534077316522598e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 43 2.4523537606000900e-02</internalNodes>
          <leafValues>
            9.1649621725082397e-02 -2.6965776085853577e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 116 6.6589027643203735e-02</internalNodes>
          <leafValues>
            -6.8442545831203461e-02 3.4471645951271057e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 14 8.0686174333095551e-02</internalNodes>
          <leafValues>
            -3.9102286100387573e-02 5.3736394643783569e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 337 1.3866674154996872e-02</internalNodes>
          <leafValues>
            -3.9964887499809265e-01 5.9886042028665543e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 62 1.6880001872777939e-02</internalNodes>
          <leafValues>
            1.2184502929449081e-01 -2.0639364421367645e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 323 5.7127773761749268e-02</internalNodes>
          <leafValues>
            8.2929193973541260e-02 -2.7692446112632751e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 342 3.9122045040130615e-02</internalNodes>
          <leafValues>
            3.7299778312444687e-02 -6.1657840013504028e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 223 1.7894832417368889e-02</internalNodes>
          <leafValues>
            1.4571423828601837e-01 -1.5307255089282990e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 406 1.4890542253851891e-02</internalNodes>
          <leafValues>
            -2.9539492726325989e-01 7.5577899813652039e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 119 1.5185782685875893e-02</internalNodes>
          <leafValues>
            1.6211624443531036e-01 -1.7077782750129700e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 401 7.5862407684326172e-03</internalNodes>
          <leafValues>
            -8.3635044097900391e-01 2.6230650022625923e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 107 7.8573390841484070e-02</internalNodes>
          <leafValues>
            -4.8550911247730255e-02 5.1848882436752319e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 184 7.1184257976710796e-03</internalNodes>
          <leafValues>
            -6.9117778539657593e-01 3.5236366093158722e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 208 1.8060296773910522e-02</internalNodes>
          <leafValues>
            7.1712046861648560e-02 -2.8598770499229431e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 14 -->
    <_>
      <maxWeakCount>66</maxWeakCount>
      <stageThreshold>-9.3593156337738037e-01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 135 3.6235164850950241e-02</internalNodes>
          <leafValues>
            2.5959107279777527e-01 6.5475034713745117e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 104 1.6041299328207970e-02</internalNodes>
          <leafValues>
            3.4271126985549927e-01 -1.3454577326774597e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 126 1.1771091260015965e-02</internalNodes>
          <leafValues>
            2.1354986727237701e-01 -2.1427741646766663e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 272 4.8521537333726883e-02</internalNodes>
          <leafValues>
            -1.5072266757488251e-01 3.1238463521003723e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 2 4.9052890390157700e-03</internalNodes>
          <leafValues>
            3.8656437397003174e-01 -1.0882703214883804e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 39 3.1850583851337433e-02</internalNodes>
          <leafValues>
            -1.9052696228027344e-01 2.3098929226398468e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 172 3.6208137869834900e-02</internalNodes>
          <leafValues>
            7.4774622917175293e-02 -4.9908718466758728e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 354 1.7295781522989273e-02</internalNodes>
          <leafValues>
            -5.6907713413238525e-01 6.2078382819890976e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 256 3.0972531065344810e-02</internalNodes>
          <leafValues>
            -2.2531478106975555e-01 1.6073043644428253e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 93 4.7689776867628098e-02</internalNodes>
          <leafValues>
            -1.2031003832817078e-01 3.2669574022293091e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 158 1.4887932687997818e-02</internalNodes>
          <leafValues>
            1.0208155214786530e-01 -3.7894788384437561e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 99 3.5810727626085281e-02</internalNodes>
          <leafValues>
            -2.6830124855041504e-01 1.4859032630920410e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 146 2.7338899672031403e-02</internalNodes>
          <leafValues>
            9.7476467490196228e-02 -3.9892432093620300e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 110 5.7814918458461761e-02</internalNodes>
          <leafValues>
            -6.8911291658878326e-02 5.6632876396179199e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 30 5.6642763316631317e-02</internalNodes>
          <leafValues>
            -8.6775369942188263e-02 3.8195505738258362e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 212 3.4008264541625977e-02</internalNodes>
          <leafValues>
            -1.9706143438816071e-01 1.7401424050331116e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 322 2.3729218170046806e-02</internalNodes>
          <leafValues>
            -2.8421404957771301e-01 1.1589094996452332e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 132 1.2428058311343193e-02</internalNodes>
          <leafValues>
            -3.2965391874313354e-01 9.2809893190860748e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 379 2.3588120937347412e-02</internalNodes>
          <leafValues>
            9.6562243998050690e-02 -3.2788053154945374e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 359 1.2904807925224304e-02</internalNodes>
          <leafValues>
            -5.0684076547622681e-01 5.9990908950567245e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 37 1.9343098625540733e-02</internalNodes>
          <leafValues>
            -4.2304861545562744e-01 7.1619503200054169e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 54 5.8683887124061584e-02</internalNodes>
          <leafValues>
            -1.1897763609886169e-01 2.8910380601882935e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 314 7.7387332916259766e-02</internalNodes>
          <leafValues>
            6.4371474087238312e-02 -4.8401272296905518e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 32 1.5952927991747856e-02</internalNodes>
          <leafValues>
            1.3915392756462097e-01 -1.9765122234821320e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 218 1.4991804957389832e-02</internalNodes>
          <leafValues>
            -3.0661338567733765e-01 9.0627431869506836e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 301 5.8532017283141613e-03</internalNodes>
          <leafValues>
            -5.8305358886718750e-01 4.6034123748540878e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 65 9.3251187354326248e-03</internalNodes>
          <leafValues>
            1.7550553381443024e-01 -1.6501483321189880e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 75 2.2475536912679672e-02</internalNodes>
          <leafValues>
            -2.5033253431320190e-01 1.1100177466869354e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 175 3.2452531158924103e-02</internalNodes>
          <leafValues>
            6.9783955812454224e-02 -4.2088204622268677e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 106 9.1266795992851257e-02</internalNodes>
          <leafValues>
            -4.3792236596345901e-02 6.9610071182250977e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 387 8.0512259155511856e-03</internalNodes>
          <leafValues>
            -4.1337686777114868e-01 7.2663143277168274e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 118 5.7529125362634659e-02</internalNodes>
          <leafValues>
            -7.9097419977188110e-02 3.6628520488739014e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 396 1.0331628844141960e-02</internalNodes>
          <leafValues>
            -7.1585410833358765e-01 4.1666161268949509e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 388 2.6609631255269051e-02</internalNodes>
          <leafValues>
            5.9718802571296692e-02 -4.4557204842567444e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 170 1.9581317901611328e-02</internalNodes>
          <leafValues>
            -2.3506633937358856e-01 1.2753143906593323e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 140 4.5224994421005249e-02</internalNodes>
          <leafValues>
            -5.9275396168231964e-02 4.9855732917785645e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 28 1.1585703119635582e-02</internalNodes>
          <leafValues>
            2.8731283545494080e-01 -1.0442002862691879e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 139 4.8658858984708786e-02</internalNodes>
          <leafValues>
            -1.9899384677410126e-01 1.4117087423801422e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 247 2.6171270757913589e-02</internalNodes>
          <leafValues>
            9.5432475209236145e-02 -3.2297986745834351e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 22 1.0336546227335930e-02</internalNodes>
          <leafValues>
            2.0490223169326782e-01 -1.4106391370296478e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 400 2.2370018064975739e-02</internalNodes>
          <leafValues>
            -3.2311582565307617e-01 8.0236829817295074e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 376 4.8868972808122635e-02</internalNodes>
          <leafValues>
            -7.5187265872955322e-02 3.6600896716117859e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 79 2.8816960752010345e-02</internalNodes>
          <leafValues>
            -1.8595387041568756e-01 1.5604765713214874e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 331 5.6826010346412659e-02</internalNodes>
          <leafValues>
            -1.1946620792150497e-01 2.0578666031360626e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 362 1.1959284543991089e-02</internalNodes>
          <leafValues>
            -6.9594573974609375e-01 3.9588540792465210e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 295 4.2123295366764069e-02</internalNodes>
          <leafValues>
            5.2904155105352402e-02 -4.8233994841575623e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 274 3.8260199129581451e-02</internalNodes>
          <leafValues>
            -1.2325955182313919e-01 2.3103201389312744e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 407 1.2891152873635292e-02</internalNodes>
          <leafValues>
            -3.7091109156608582e-01 7.7288657426834106e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 395 3.6757849156856537e-02</internalNodes>
          <leafValues>
            -1.0695543885231018e-01 2.7097389101982117e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 319 1.9748253747820854e-02</internalNodes>
          <leafValues>
            -2.6692709326744080e-01 1.1164034157991409e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 8 6.6089570522308350e-02</internalNodes>
          <leafValues>
            -1.6268984973430634e-01 1.7516276240348816e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 181 1.1842563748359680e-02</internalNodes>
          <leafValues>
            1.8089738488197327e-01 -1.4368705451488495e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 211 9.6298125572502613e-04</internalNodes>
          <leafValues>
            -6.6003662347793579e-01 3.7299372255802155e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 264 5.5496811866760254e-02</internalNodes>
          <leafValues>
            -7.9757250845432281e-02 3.5867783427238464e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 85 4.5877113938331604e-02</internalNodes>
          <leafValues>
            -6.5585024654865265e-02 3.6003270745277405e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 261 3.3135972917079926e-02</internalNodes>
          <leafValues>
            6.1119224876165390e-02 -4.1669166088104248e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 96 3.2037578523159027e-02</internalNodes>
          <leafValues>
            1.2124529480934143e-01 -2.1470184624195099e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 296 1.4727091416716576e-02</internalNodes>
          <leafValues>
            -5.7288587093353271e-01 4.6685628592967987e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 207 4.7442387789487839e-02</internalNodes>
          <leafValues>
            -5.2196249365806580e-02 5.1182353496551514e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 367 1.1715631932020187e-02</internalNodes>
          <leafValues>
            -5.6910729408264160e-01 4.6120714396238327e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 268 4.1607704013586044e-02</internalNodes>
          <leafValues>
            -1.2063561379909515e-01 2.0680625736713409e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 300 6.9544389843940735e-03</internalNodes>
          <leafValues>
            -2.9259884357452393e-01 8.6709313094615936e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 372 2.0231716334819794e-02</internalNodes>
          <leafValues>
            -2.3428122699260712e-01 9.7812965512275696e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 180 1.3869043439626694e-02</internalNodes>
          <leafValues>
            1.5906314551830292e-01 -1.6273392736911774e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 143 2.4136468768119812e-02</internalNodes>
          <leafValues>
            -1.7815589904785156e-01 1.4657269418239594e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 49 5.2772037684917450e-02</internalNodes>
          <leafValues>
            -8.5652016103267670e-02 2.8691658377647400e-01</leafValues></_></weakClassifiers></_></stages>
  <features>
    <_>
      <rect>
        0 12 8 8 26</rect></_>
    <_>
      <rect>
        0 20 8 8 15</rect></_>
    <_>
      <rect>
        0 44 8 8 26</rect></_>
    <_>
      <rect>
        0 48 8 8 19</rect></_>
    <_>
      <rect>
        0 52 8 8 27</rect></_>
    <_>
      <rect>
        0 56 8 8 27</rect></_>
    <_>
      <rect>
        0 60 8 8 26</rect></_>
    <_>
      <rect>
        0 68 8 8 8</rect></_>
    <_>
      <rect>
        0 80 8 8 13</rect></_>
    <_>
      <rect>
        0 80 8 8 14</rect></_>
    <_>
      <rect>
        4 4 8 8 8</rect></_>
    <_>
      <rect>
        4 20 8 8 10</rect></_>
    <_>
      <rect>
        4 48 8 8 25</rect></_>
    <_>
      <rect>
        4 56 8 8 1</rect></_>
    <_>
      <rect>
        4 60 8 8 34</rect></_>
    <_>
      <rect>
        4 68 8 8 7</rect></_>
    <_>
      <rect>
        4 80 8 8 12</rect></_>
    <_>
      <rect>
        8 4 8 8 12</rect></_>
    <_>
      <rect>
        8 28 8 8 0</rect></_>
    <_>
      <rect>
        8 48 8 8 18</rect></_>
    <_>
      <rect>
        8 48 8 8 29</rect></_>
    <_>
      <rect>
        8 56 8 8 31</rect></_>
    <_>
      <rect>
        8 60 8 8 31</rect></_>
    <_>
      <rect>
        8 80 8 8 13</rect></_>
    <_>
      <rect>
        12 0 8 8 30</rect></_>
    <_>
      <rect>
        12 4 8 8 12</rect></_>
    <_>
      <rect>
        12 8 8 8 12</rect></_>
    <_>
      <rect>
        12 8 8 8 13</rect></_>
    <_>
      <rect>
        12 16 8 8 35</rect></_>
    <_>
      <rect>
        12 20 8 8 1</rect></_>
    <_>
      <rect>
        12 20 8 8 2</rect></_>
    <_>
      <rect>
        12 20 8 8 30</rect></_>
    <_>
      <rect>
        12 20 8 8 31</rect></_>
    <_>
      <rect>
        12 28 8 8 0</rect></_>
    <_>
      <rect>
        12 40 8 8 25</rect></_>
    <_>
      <rect>
        12 64 8 8 13</rect></_>
    <_>
      <rect>
        12 68 8 8 13</rect></_>
    <_>
      <rect>
        12 80 8 8 12</rect></_>
    <_>
      <rect>
        12 80 8 8 13</rect></_>
    <_>
      <rect>
        16 20 8 8 14</rect></_>
    <_>
      <rect>
        16 56 8 8 18</rect></_>
    <_>
      <rect>
        20 4 8 8 4</rect></_>
    <_>
      <rect>
        20 8 8 8 4</rect></_>
    <_>
      <rect>
        20 12 8 8 11</rect></_>
    <_>
      <rect>
        20 20 8 8 14</rect></_>
    <_>
      <rect>
        20 20 8 8 15</rect></_>
    <_>
      <rect>
        20 20 8 8 22</rect></_>
    <_>
      <rect>
        20 24 8 8 16</rect></_>
    <_>
      <rect>
        20 28 8 8 34</rect></_>
    <_>
      <rect>
        20 32 8 8 34</rect></_>
    <_>
      <rect>
        20 44 8 8 8</rect></_>
    <_>
      <rect>
        20 44 8 8 27</rect></_>
    <_>
      <rect>
        20 56 8 8 3</rect></_>
    <_>
      <rect>
        20 60 8 8 33</rect></_>
    <_>
      <rect>
        20 68 8 8 26</rect></_>
    <_>
      <rect>
        20 80 8 8 32</rect></_>
    <_>
      <rect>
        24 28 8 8 16</rect></_>
    <_>
      <rect>
        24 52 8 8 5</rect></_>
    <_>
      <rect>
        24 52 8 8 34</rect></_>
    <_>
      <rect>
        24 68 8 8 2</rect></_>
    <_>
      <rect>
        28 12 8 8 15</rect></_>
    <_>
      <rect>
        28 12 8 8 24</rect></_>
    <_>
      <rect>
        28 52 8 8 32</rect></_>
    <_>
      <rect>
        28 68 8 8 9</rect></_>
    <_>
      <rect>
        32 8 8 8 35</rect></_>
    <_>
      <rect>
        32 48 8 8 33</rect></_>
    <_>
      <rect>
        32 68 8 8 17</rect></_>
    <_>
      <rect>
        0 0 8 16 0</rect></_>
    <_>
      <rect>
        0 4 8 16 7</rect></_>
    <_>
      <rect>
        0 40 8 16 25</rect></_>
    <_>
      <rect>
        0 44 8 16 1</rect></_>
    <_>
      <rect>
        0 44 8 16 24</rect></_>
    <_>
      <rect>
        4 8 8 16 22</rect></_>
    <_>
      <rect>
        4 36 8 16 19</rect></_>
    <_>
      <rect>
        8 8 8 16 5</rect></_>
    <_>
      <rect>
        8 8 8 16 10</rect></_>
    <_>
      <rect>
        8 8 8 16 11</rect></_>
    <_>
      <rect>
        8 28 8 16 12</rect></_>
    <_>
      <rect>
        12 8 8 16 11</rect></_>
    <_>
      <rect>
        12 8 8 16 12</rect></_>
    <_>
      <rect>
        12 24 8 16 0</rect></_>
    <_>
      <rect>
        12 24 8 16 18</rect></_>
    <_>
      <rect>
        12 28 8 16 26</rect></_>
    <_>
      <rect>
        12 36 8 16 18</rect></_>
    <_>
      <rect>
        12 40 8 16 18</rect></_>
    <_>
      <rect>
        12 44 8 16 19</rect></_>
    <_>
      <rect>
        12 48 8 16 8</rect></_>
    <_>
      <rect>
        12 56 8 16 13</rect></_>
    <_>
      <rect>
        12 64 8 16 1</rect></_>
    <_>
      <rect>
        16 8 8 16 2</rect></_>
    <_>
      <rect>
        16 8 8 16 14</rect></_>
    <_>
      <rect>
        16 12 8 16 1</rect></_>
    <_>
      <rect>
        16 12 8 16 15</rect></_>
    <_>
      <rect>
        16 40 8 16 18</rect></_>
    <_>
      <rect>
        20 8 8 16 3</rect></_>
    <_>
      <rect>
        20 8 8 16 26</rect></_>
    <_>
      <rect>
        20 20 8 16 26</rect></_>
    <_>
      <rect>
        20 24 8 16 16</rect></_>
    <_>
      <rect>
        20 24 8 16 34</rect></_>
    <_>
      <rect>
        20 28 8 16 35</rect></_>
    <_>
      <rect>
        20 32 8 16 27</rect></_>
    <_>
      <rect>
        20 32 8 16 34</rect></_>
    <_>
      <rect>
        20 32 8 16 35</rect></_>
    <_>
      <rect>
        20 36 8 16 35</rect></_>
    <_>
      <rect>
        20 40 8 16 0</rect></_>
    <_>
      <rect>
        20 40 8 16 8</rect></_>
    <_>
      <rect>
        20 40 8 16 34</rect></_>
    <_>
      <rect>
        20 44 8 16 34</rect></_>
    <_>
      <rect>
        20 48 8 16 34</rect></_>
    <_>
      <rect>
        20 56 8 16 4</rect></_>
    <_>
      <rect>
        20 60 8 16 15</rect></_>
    <_>
      <rect>
        20 64 8 16 15</rect></_>
    <_>
      <rect>
        24 8 8 16 5</rect></_>
    <_>
      <rect>
        24 8 8 16 11</rect></_>
    <_>
      <rect>
        24 12 8 16 22</rect></_>
    <_>
      <rect>
        24 24 8 16 16</rect></_>
    <_>
      <rect>
        24 28 8 16 16</rect></_>
    <_>
      <rect>
        28 56 8 16 10</rect></_>
    <_>
      <rect>
        28 64 8 16 0</rect></_>
    <_>
      <rect>
        32 24 8 16 29</rect></_>
    <_>
      <rect>
        32 28 8 16 33</rect></_>
    <_>
      <rect>
        32 36 8 16 34</rect></_>
    <_>
      <rect>
        32 40 8 16 27</rect></_>
    <_>
      <rect>
        32 40 8 16 28</rect></_>
    <_>
      <rect>
        32 44 8 16 27</rect></_>
    <_>
      <rect>
        32 44 8 16 33</rect></_>
    <_>
      <rect>
        32 56 8 16 35</rect></_>
    <_>
      <rect>
        0 0 16 8 10</rect></_>
    <_>
      <rect>
        0 0 16 8 30</rect></_>
    <_>
      <rect>
        0 4 16 8 8</rect></_>
    <_>
      <rect>
        0 4 16 8 13</rect></_>
    <_>
      <rect>
        0 4 16 8 27</rect></_>
    <_>
      <rect>
        0 8 16 8 11</rect></_>
    <_>
      <rect>
        0 36 16 8 35</rect></_>
    <_>
      <rect>
        0 68 16 8 11</rect></_>
    <_>
      <rect>
        0 76 16 8 9</rect></_>
    <_>
      <rect>
        0 76 16 8 16</rect></_>
    <_>
      <rect>
        0 76 16 8 17</rect></_>
    <_>
      <rect>
        0 76 16 8 21</rect></_>
    <_>
      <rect>
        0 76 16 8 31</rect></_>
    <_>
      <rect>
        0 80 16 8 6</rect></_>
    <_>
      <rect>
        0 80 16 8 12</rect></_>
    <_>
      <rect>
        0 80 16 8 13</rect></_>
    <_>
      <rect>
        0 80 16 8 14</rect></_>
    <_>
      <rect>
        0 80 16 8 35</rect></_>
    <_>
      <rect>
        4 0 16 8 0</rect></_>
    <_>
      <rect>
        4 0 16 8 15</rect></_>
    <_>
      <rect>
        4 0 16 8 17</rect></_>
    <_>
      <rect>
        4 4 16 8 13</rect></_>
    <_>
      <rect>
        4 8 16 8 15</rect></_>
    <_>
      <rect>
        4 20 16 8 15</rect></_>
    <_>
      <rect>
        4 76 16 8 30</rect></_>
    <_>
      <rect>
        4 76 16 8 31</rect></_>
    <_>
      <rect>
        4 80 16 8 6</rect></_>
    <_>
      <rect>
        4 80 16 8 12</rect></_>
    <_>
      <rect>
        4 80 16 8 21</rect></_>
    <_>
      <rect>
        4 80 16 8 26</rect></_>
    <_>
      <rect>
        4 80 16 8 34</rect></_>
    <_>
      <rect>
        4 80 16 8 35</rect></_>
    <_>
      <rect>
        8 0 16 8 1</rect></_>
    <_>
      <rect>
        8 0 16 8 15</rect></_>
    <_>
      <rect>
        8 52 16 8 35</rect></_>
    <_>
      <rect>
        8 76 16 8 0</rect></_>
    <_>
      <rect>
        8 76 16 8 16</rect></_>
    <_>
      <rect>
        8 76 16 8 17</rect></_>
    <_>
      <rect>
        8 76 16 8 22</rect></_>
    <_>
      <rect>
        8 76 16 8 30</rect></_>
    <_>
      <rect>
        8 76 16 8 35</rect></_>
    <_>
      <rect>
        8 80 16 8 3</rect></_>
    <_>
      <rect>
        8 80 16 8 10</rect></_>
    <_>
      <rect>
        8 80 16 8 11</rect></_>
    <_>
      <rect>
        8 80 16 8 12</rect></_>
    <_>
      <rect>
        8 80 16 8 20</rect></_>
    <_>
      <rect>
        8 80 16 8 26</rect></_>
    <_>
      <rect>
        8 80 16 8 35</rect></_>
    <_>
      <rect>
        12 0 16 8 1</rect></_>
    <_>
      <rect>
        12 0 16 8 8</rect></_>
    <_>
      <rect>
        12 0 16 8 15</rect></_>
    <_>
      <rect>
        12 4 16 8 3</rect></_>
    <_>
      <rect>
        12 20 16 8 1</rect></_>
    <_>
      <rect>
        12 48 16 8 29</rect></_>
    <_>
      <rect>
        12 56 16 8 2</rect></_>
    <_>
      <rect>
        12 76 16 8 1</rect></_>
    <_>
      <rect>
        12 76 16 8 7</rect></_>
    <_>
      <rect>
        12 76 16 8 21</rect></_>
    <_>
      <rect>
        12 76 16 8 22</rect></_>
    <_>
      <rect>
        12 76 16 8 26</rect></_>
    <_>
      <rect>
        12 76 16 8 30</rect></_>
    <_>
      <rect>
        12 80 16 8 4</rect></_>
    <_>
      <rect>
        12 80 16 8 10</rect></_>
    <_>
      <rect>
        12 80 16 8 18</rect></_>
    <_>
      <rect>
        12 80 16 8 26</rect></_>
    <_>
      <rect>
        12 80 16 8 31</rect></_>
    <_>
      <rect>
        16 0 16 8 6</rect></_>
    <_>
      <rect>
        16 0 16 8 8</rect></_>
    <_>
      <rect>
        16 4 16 8 3</rect></_>
    <_>
      <rect>
        16 4 16 8 25</rect></_>
    <_>
      <rect>
        16 4 16 8 26</rect></_>
    <_>
      <rect>
        16 4 16 8 32</rect></_>
    <_>
      <rect>
        16 8 16 8 3</rect></_>
    <_>
      <rect>
        16 20 16 8 8</rect></_>
    <_>
      <rect>
        16 76 16 8 0</rect></_>
    <_>
      <rect>
        16 76 16 8 7</rect></_>
    <_>
      <rect>
        16 76 16 8 8</rect></_>
    <_>
      <rect>
        16 80 16 8 3</rect></_>
    <_>
      <rect>
        16 80 16 8 6</rect></_>
    <_>
      <rect>
        16 80 16 8 7</rect></_>
    <_>
      <rect>
        16 80 16 8 10</rect></_>
    <_>
      <rect>
        16 80 16 8 28</rect></_>
    <_>
      <rect>
        16 80 16 8 35</rect></_>
    <_>
      <rect>
        0 0 12 12 7</rect></_>
    <_>
      <rect>
        0 8 12 12 7</rect></_>
    <_>
      <rect>
        0 12 12 12 26</rect></_>
    <_>
      <rect>
        0 60 12 12 6</rect></_>
    <_>
      <rect>
        0 68 12 12 16</rect></_>
    <_>
      <rect>
        0 72 12 12 9</rect></_>
    <_>
      <rect>
        0 72 12 12 10</rect></_>
    <_>
      <rect>
        0 72 12 12 21</rect></_>
    <_>
      <rect>
        4 72 12 12 3</rect></_>
    <_>
      <rect>
        4 72 12 12 9</rect></_>
    <_>
      <rect>
        8 0 12 12 6</rect></_>
    <_>
      <rect>
        8 20 12 12 17</rect></_>
    <_>
      <rect>
        8 24 12 12 0</rect></_>
    <_>
      <rect>
        8 28 12 12 13</rect></_>
    <_>
      <rect>
        8 36 12 12 8</rect></_>
    <_>
      <rect>
        8 60 12 12 12</rect></_>
    <_>
      <rect>
        8 68 12 12 1</rect></_>
    <_>
      <rect>
        12 20 12 12 1</rect></_>
    <_>
      <rect>
        12 20 12 12 15</rect></_>
    <_>
      <rect>
        12 40 12 12 26</rect></_>
    <_>
      <rect>
        12 64 12 12 11</rect></_>
    <_>
      <rect>
        16 0 12 12 10</rect></_>
    <_>
      <rect>
        16 4 12 12 10</rect></_>
    <_>
      <rect>
        16 12 12 12 1</rect></_>
    <_>
      <rect>
        16 16 12 12 22</rect></_>
    <_>
      <rect>
        16 24 12 12 16</rect></_>
    <_>
      <rect>
        16 28 12 12 16</rect></_>
    <_>
      <rect>
        16 60 12 12 4</rect></_>
    <_>
      <rect>
        16 64 12 12 4</rect></_>
    <_>
      <rect>
        16 64 12 12 5</rect></_>
    <_>
      <rect>
        16 72 12 12 15</rect></_>
    <_>
      <rect>
        20 8 12 12 28</rect></_>
    <_>
      <rect>
        20 12 12 12 6</rect></_>
    <_>
      <rect>
        20 32 12 12 26</rect></_>
    <_>
      <rect>
        20 60 12 12 4</rect></_>
    <_>
      <rect>
        20 72 12 12 0</rect></_>
    <_>
      <rect>
        20 72 12 12 7</rect></_>
    <_>
      <rect>
        20 72 12 12 32</rect></_>
    <_>
      <rect>
        20 72 12 12 35</rect></_>
    <_>
      <rect>
        24 0 12 12 12</rect></_>
    <_>
      <rect>
        24 0 12 12 16</rect></_>
    <_>
      <rect>
        24 0 12 12 33</rect></_>
    <_>
      <rect>
        24 4 12 12 17</rect></_>
    <_>
      <rect>
        24 16 12 12 17</rect></_>
    <_>
      <rect>
        24 72 12 12 0</rect></_>
    <_>
      <rect>
        24 72 12 12 6</rect></_>
    <_>
      <rect>
        24 72 12 12 7</rect></_>
    <_>
      <rect>
        24 72 12 12 8</rect></_>
    <_>
      <rect>
        24 72 12 12 35</rect></_>
    <_>
      <rect>
        0 0 12 24 1</rect></_>
    <_>
      <rect>
        0 0 12 24 2</rect></_>
    <_>
      <rect>
        0 8 12 24 2</rect></_>
    <_>
      <rect>
        0 16 12 24 21</rect></_>
    <_>
      <rect>
        0 16 12 24 22</rect></_>
    <_>
      <rect>
        0 20 12 24 9</rect></_>
    <_>
      <rect>
        0 36 12 24 27</rect></_>
    <_>
      <rect>
        0 44 12 24 4</rect></_>
    <_>
      <rect>
        0 48 12 24 17</rect></_>
    <_>
      <rect>
        4 44 12 24 4</rect></_>
    <_>
      <rect>
        4 48 12 24 4</rect></_>
    <_>
      <rect>
        8 0 12 24 8</rect></_>
    <_>
      <rect>
        8 8 12 24 14</rect></_>
    <_>
      <rect>
        8 24 12 24 0</rect></_>
    <_>
      <rect>
        8 32 12 24 29</rect></_>
    <_>
      <rect>
        8 36 12 24 34</rect></_>
    <_>
      <rect>
        12 8 12 24 1</rect></_>
    <_>
      <rect>
        12 8 12 24 15</rect></_>
    <_>
      <rect>
        12 48 12 24 8</rect></_>
    <_>
      <rect>
        16 0 12 24 34</rect></_>
    <_>
      <rect>
        16 8 12 24 1</rect></_>
    <_>
      <rect>
        16 16 12 24 34</rect></_>
    <_>
      <rect>
        16 24 12 24 16</rect></_>
    <_>
      <rect>
        16 28 12 24 17</rect></_>
    <_>
      <rect>
        16 36 12 24 17</rect></_>
    <_>
      <rect>
        20 0 12 24 10</rect></_>
    <_>
      <rect>
        20 4 12 24 11</rect></_>
    <_>
      <rect>
        20 20 12 24 22</rect></_>
    <_>
      <rect>
        20 32 12 24 20</rect></_>
    <_>
      <rect>
        20 48 12 24 3</rect></_>
    <_>
      <rect>
        24 24 12 24 7</rect></_>
    <_>
      <rect>
        24 44 12 24 13</rect></_>
    <_>
      <rect>
        24 48 12 24 8</rect></_>
    <_>
      <rect>
        24 48 12 24 13</rect></_>
    <_>
      <rect>
        0 0 24 12 6</rect></_>
    <_>
      <rect>
        0 0 24 12 10</rect></_>
    <_>
      <rect>
        0 0 24 12 16</rect></_>
    <_>
      <rect>
        0 16 24 12 35</rect></_>
    <_>
      <rect>
        0 60 24 12 26</rect></_>
    <_>
      <rect>
        0 64 24 12 26</rect></_>
    <_>
      <rect>
        0 64 24 12 35</rect></_>
    <_>
      <rect>
        0 68 24 12 19</rect></_>
    <_>
      <rect>
        0 68 24 12 32</rect></_>
    <_>
      <rect>
        0 72 24 12 16</rect></_>
    <_>
      <rect>
        0 72 24 12 21</rect></_>
    <_>
      <rect>
        0 72 24 12 26</rect></_>
    <_>
      <rect>
        0 72 24 12 35</rect></_>
    <_>
      <rect>
        0 0 16 16 7</rect></_>
    <_>
      <rect>
        0 4 16 16 1</rect></_>
    <_>
      <rect>
        0 4 16 16 2</rect></_>
    <_>
      <rect>
        0 4 16 16 8</rect></_>
    <_>
      <rect>
        0 8 16 16 11</rect></_>
    <_>
      <rect>
        0 8 16 16 14</rect></_>
    <_>
      <rect>
        0 8 16 16 15</rect></_>
    <_>
      <rect>
        0 20 16 16 35</rect></_>
    <_>
      <rect>
        0 24 16 16 35</rect></_>
    <_>
      <rect>
        0 52 16 16 27</rect></_>
    <_>
      <rect>
        0 52 16 16 34</rect></_>
    <_>
      <rect>
        4 0 16 16 6</rect></_>
    <_>
      <rect>
        4 0 16 16 33</rect></_>
    <_>
      <rect>
        4 8 16 16 14</rect></_>
    <_>
      <rect>
        4 8 16 16 15</rect></_>
    <_>
      <rect>
        4 8 16 16 18</rect></_>
    <_>
      <rect>
        4 12 16 16 15</rect></_>
    <_>
      <rect>
        4 20 16 16 17</rect></_>
    <_>
      <rect>
        8 0 16 16 0</rect></_>
    <_>
      <rect>
        12 8 16 16 2</rect></_>
    <_>
      <rect>
        12 12 16 16 1</rect></_>
    <_>
      <rect>
        16 0 16 16 9</rect></_>
    <_>
      <rect>
        16 0 16 16 17</rect></_>
    <_>
      <rect>
        16 4 16 16 14</rect></_>
    <_>
      <rect>
        16 4 16 16 15</rect></_>
    <_>
      <rect>
        16 4 16 16 17</rect></_>
    <_>
      <rect>
        16 8 16 16 2</rect></_>
    <_>
      <rect>
        16 8 16 16 3</rect></_>
    <_>
      <rect>
        16 8 16 16 4</rect></_>
    <_>
      <rect>
        16 8 16 16 5</rect></_>
    <_>
      <rect>
        16 8 16 16 6</rect></_>
    <_>
      <rect>
        16 52 16 16 31</rect></_>
    <_>
      <rect>
        16 64 16 16 7</rect></_>
    <_>
      <rect>
        16 64 16 16 8</rect></_>
    <_>
      <rect>
        0 0 16 32 8</rect></_>
    <_>
      <rect>
        0 4 16 32 22</rect></_>
    <_>
      <rect>
        0 8 16 32 30</rect></_>
    <_>
      <rect>
        0 12 16 32 31</rect></_>
    <_>
      <rect>
        0 12 16 32 34</rect></_>
    <_>
      <rect>
        0 20 16 32 30</rect></_>
    <_>
      <rect>
        0 20 16 32 31</rect></_>
    <_>
      <rect>
        0 32 16 32 4</rect></_>
    <_>
      <rect>
        4 8 16 32 21</rect></_>
    <_>
      <rect>
        4 28 16 32 8</rect></_>
    <_>
      <rect>
        4 32 16 32 17</rect></_>
    <_>
      <rect>
        8 4 16 32 28</rect></_>
    <_>
      <rect>
        8 8 16 32 0</rect></_>
    <_>
      <rect>
        8 8 16 32 22</rect></_>
    <_>
      <rect>
        8 12 16 32 16</rect></_>
    <_>
      <rect>
        8 20 16 32 18</rect></_>
    <_>
      <rect>
        8 28 16 32 16</rect></_>
    <_>
      <rect>
        8 32 16 32 0</rect></_>
    <_>
      <rect>
        12 4 16 32 0</rect></_>
    <_>
      <rect>
        12 8 16 32 18</rect></_>
    <_>
      <rect>
        12 32 16 32 17</rect></_>
    <_>
      <rect>
        16 0 16 32 17</rect></_>
    <_>
      <rect>
        16 4 16 32 7</rect></_>
    <_>
      <rect>
        16 8 16 32 14</rect></_>
    <_>
      <rect>
        16 8 16 32 26</rect></_>
    <_>
      <rect>
        16 8 16 32 30</rect></_>
    <_>
      <rect>
        16 12 16 32 21</rect></_>
    <_>
      <rect>
        16 16 16 32 18</rect></_>
    <_>
      <rect>
        16 20 16 32 21</rect></_>
    <_>
      <rect>
        16 20 16 32 22</rect></_>
    <_>
      <rect>
        16 20 16 32 25</rect></_>
    <_>
      <rect>
        16 24 16 32 18</rect></_>
    <_>
      <rect>
        16 24 16 32 30</rect></_>
    <_>
      <rect>
        16 32 16 32 12</rect></_>
    <_>
      <rect>
        0 0 20 20 8</rect></_>
    <_>
      <rect>
        0 4 20 20 6</rect></_>
    <_>
      <rect>
        0 4 20 20 34</rect></_>
    <_>
      <rect>
        0 52 20 20 34</rect></_>
    <_>
      <rect>
        4 0 20 20 33</rect></_>
    <_>
      <rect>
        8 0 20 20 10</rect></_>
    <_>
      <rect>
        8 0 20 20 17</rect></_>
    <_>
      <rect>
        8 4 20 20 11</rect></_>
    <_>
      <rect>
        8 4 20 20 17</rect></_>
    <_>
      <rect>
        8 16 20 20 26</rect></_>
    <_>
      <rect>
        8 52 20 20 18</rect></_>
    <_>
      <rect>
        8 52 20 20 19</rect></_>
    <_>
      <rect>
        8 56 20 20 0</rect></_>
    <_>
      <rect>
        8 56 20 20 18</rect></_>
    <_>
      <rect>
        0 0 20 40 29</rect></_>
    <_>
      <rect>
        0 4 20 40 34</rect></_>
    <_>
      <rect>
        0 16 20 40 34</rect></_>
    <_>
      <rect>
        4 0 20 40 30</rect></_>
    <_>
      <rect>
        4 12 20 40 0</rect></_>
    <_>
      <rect>
        4 12 20 40 16</rect></_>
    <_>
      <rect>
        8 0 20 40 10</rect></_>
    <_>
      <rect>
        8 0 20 40 18</rect></_>
    <_>
      <rect>
        8 0 20 40 31</rect></_>
    <_>
      <rect>
        8 4 20 40 18</rect></_>
    <_>
      <rect>
        8 8 20 40 1</rect></_>
    <_>
      <rect>
        8 8 20 40 18</rect></_>
    <_>
      <rect>
        8 12 20 40 0</rect></_>
    <_>
      <rect>
        8 12 20 40 18</rect></_>
    <_>
      <rect>
        8 16 20 40 17</rect></_>
    <_>
      <rect>
        0 12 24 24 30</rect></_>
    <_>
      <rect>
        0 28 24 24 7</rect></_>
    <_>
      <rect>
        0 28 24 24 16</rect></_>
    <_>
      <rect>
        0 44 24 24 12</rect></_>
    <_>
      <rect>
        0 48 24 24 3</rect></_>
    <_>
      <rect>
        0 0 24 48 8</rect></_>
    <_>
      <rect>
        0 0 24 48 23</rect></_></features></cascade>
</opencv_storage>
