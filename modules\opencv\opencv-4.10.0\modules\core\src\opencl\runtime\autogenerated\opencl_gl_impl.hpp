//
// AUTOGENERATED, DO NOT EDIT
//
// generated by parser_cl.py
enum OPENCL_GL_FN_ID {
    OPENCL_GL_FN_clCreateFromGLBuffer = 0,
    OPENCL_GL_FN_clCreateFromGLRenderbuffer = 1,
    OPENCL_GL_FN_clCreateFromGLTexture = 2,
    OPENCL_GL_FN_clCreateFromGLTexture2D = 3,
    OPENCL_GL_FN_clCreateFromGLTexture3D = 4,
    OPENCL_GL_FN_clEnqueueAcquireGLObjects = 5,
    OPENCL_GL_FN_clEnqueueReleaseGLObjects = 6,
    OPENCL_GL_FN_clGetGLContextInfoKHR = 7,
    OPENCL_GL_FN_clGetGLObjectInfo = 8,
    OPENCL_GL_FN_clGetGLTextureInfo = 9,
};

namespace {
// generated by parser_cl.py
#define opencl_gl_fn0(ID, _R, decl_args) \
    typedef _R (CL_API_CALL*ID##FN)decl_args; \
    static _R CL_API_CALL ID##_switch_fn decl_args \
    { return ((ID##FN)opencl_gl_check_fn(ID))(); } \

#define opencl_gl_fn1(ID, _R, decl_args) \
    typedef _R (CL_API_CALL*ID##FN)decl_args; \
    static _R CL_API_CALL ID##_switch_fn decl_args \
    { return ((ID##FN)opencl_gl_check_fn(ID))(p1); } \

#define opencl_gl_fn2(ID, _R, decl_args) \
    typedef _R (CL_API_CALL*ID##FN)decl_args; \
    static _R CL_API_CALL ID##_switch_fn decl_args \
    { return ((ID##FN)opencl_gl_check_fn(ID))(p1, p2); } \

#define opencl_gl_fn3(ID, _R, decl_args) \
    typedef _R (CL_API_CALL*ID##FN)decl_args; \
    static _R CL_API_CALL ID##_switch_fn decl_args \
    { return ((ID##FN)opencl_gl_check_fn(ID))(p1, p2, p3); } \

#define opencl_gl_fn4(ID, _R, decl_args) \
    typedef _R (CL_API_CALL*ID##FN)decl_args; \
    static _R CL_API_CALL ID##_switch_fn decl_args \
    { return ((ID##FN)opencl_gl_check_fn(ID))(p1, p2, p3, p4); } \

#define opencl_gl_fn5(ID, _R, decl_args) \
    typedef _R (CL_API_CALL*ID##FN)decl_args; \
    static _R CL_API_CALL ID##_switch_fn decl_args \
    { return ((ID##FN)opencl_gl_check_fn(ID))(p1, p2, p3, p4, p5); } \

#define opencl_gl_fn6(ID, _R, decl_args) \
    typedef _R (CL_API_CALL*ID##FN)decl_args; \
    static _R CL_API_CALL ID##_switch_fn decl_args \
    { return ((ID##FN)opencl_gl_check_fn(ID))(p1, p2, p3, p4, p5, p6); } \

#define opencl_gl_fn7(ID, _R, decl_args) \
    typedef _R (CL_API_CALL*ID##FN)decl_args; \
    static _R CL_API_CALL ID##_switch_fn decl_args \
    { return ((ID##FN)opencl_gl_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7); } \

#define opencl_gl_fn8(ID, _R, decl_args) \
    typedef _R (CL_API_CALL*ID##FN)decl_args; \
    static _R CL_API_CALL ID##_switch_fn decl_args \
    { return ((ID##FN)opencl_gl_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8); } \

#define opencl_gl_fn9(ID, _R, decl_args) \
    typedef _R (CL_API_CALL*ID##FN)decl_args; \
    static _R CL_API_CALL ID##_switch_fn decl_args \
    { return ((ID##FN)opencl_gl_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9); } \

#define opencl_gl_fn10(ID, _R, decl_args) \
    typedef _R (CL_API_CALL*ID##FN)decl_args; \
    static _R CL_API_CALL ID##_switch_fn decl_args \
    { return ((ID##FN)opencl_gl_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10); } \

#define opencl_gl_fn11(ID, _R, decl_args) \
    typedef _R (CL_API_CALL*ID##FN)decl_args; \
    static _R CL_API_CALL ID##_switch_fn decl_args \
    { return ((ID##FN)opencl_gl_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11); } \

#define opencl_gl_fn12(ID, _R, decl_args) \
    typedef _R (CL_API_CALL*ID##FN)decl_args; \
    static _R CL_API_CALL ID##_switch_fn decl_args \
    { return ((ID##FN)opencl_gl_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12); } \

#define opencl_gl_fn13(ID, _R, decl_args) \
    typedef _R (CL_API_CALL*ID##FN)decl_args; \
    static _R CL_API_CALL ID##_switch_fn decl_args \
    { return ((ID##FN)opencl_gl_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12, p13); } \

#define opencl_gl_fn14(ID, _R, decl_args) \
    typedef _R (CL_API_CALL*ID##FN)decl_args; \
    static _R CL_API_CALL ID##_switch_fn decl_args \
    { return ((ID##FN)opencl_gl_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12, p13, p14); } \

} // anonymous namespace

#ifdef cl_khr_gl_sharing

// generated by parser_cl.py
opencl_gl_fn4(OPENCL_GL_FN_clCreateFromGLBuffer, cl_mem, (cl_context p1, cl_mem_flags p2, cl_GLuint p3, int* p4))
cl_mem (CL_API_CALL*clCreateFromGLBuffer)(cl_context, cl_mem_flags, cl_GLuint, int*) =
        OPENCL_GL_FN_clCreateFromGLBuffer_switch_fn;
static const struct DynamicFnEntry clCreateFromGLBuffer_definition = { "clCreateFromGLBuffer", (void**)&clCreateFromGLBuffer};

opencl_gl_fn4(OPENCL_GL_FN_clCreateFromGLRenderbuffer, cl_mem, (cl_context p1, cl_mem_flags p2, cl_GLuint p3, cl_int* p4))
cl_mem (CL_API_CALL*clCreateFromGLRenderbuffer)(cl_context, cl_mem_flags, cl_GLuint, cl_int*) =
        OPENCL_GL_FN_clCreateFromGLRenderbuffer_switch_fn;
static const struct DynamicFnEntry clCreateFromGLRenderbuffer_definition = { "clCreateFromGLRenderbuffer", (void**)&clCreateFromGLRenderbuffer};

opencl_gl_fn6(OPENCL_GL_FN_clCreateFromGLTexture, cl_mem, (cl_context p1, cl_mem_flags p2, cl_GLenum p3, cl_GLint p4, cl_GLuint p5, cl_int* p6))
cl_mem (CL_API_CALL*clCreateFromGLTexture)(cl_context, cl_mem_flags, cl_GLenum, cl_GLint, cl_GLuint, cl_int*) =
        OPENCL_GL_FN_clCreateFromGLTexture_switch_fn;
static const struct DynamicFnEntry clCreateFromGLTexture_definition = { "clCreateFromGLTexture", (void**)&clCreateFromGLTexture};

opencl_gl_fn6(OPENCL_GL_FN_clCreateFromGLTexture2D, cl_mem, (cl_context p1, cl_mem_flags p2, cl_GLenum p3, cl_GLint p4, cl_GLuint p5, cl_int* p6))
cl_mem (CL_API_CALL*clCreateFromGLTexture2D)(cl_context, cl_mem_flags, cl_GLenum, cl_GLint, cl_GLuint, cl_int*) =
        OPENCL_GL_FN_clCreateFromGLTexture2D_switch_fn;
static const struct DynamicFnEntry clCreateFromGLTexture2D_definition = { "clCreateFromGLTexture2D", (void**)&clCreateFromGLTexture2D};

opencl_gl_fn6(OPENCL_GL_FN_clCreateFromGLTexture3D, cl_mem, (cl_context p1, cl_mem_flags p2, cl_GLenum p3, cl_GLint p4, cl_GLuint p5, cl_int* p6))
cl_mem (CL_API_CALL*clCreateFromGLTexture3D)(cl_context, cl_mem_flags, cl_GLenum, cl_GLint, cl_GLuint, cl_int*) =
        OPENCL_GL_FN_clCreateFromGLTexture3D_switch_fn;
static const struct DynamicFnEntry clCreateFromGLTexture3D_definition = { "clCreateFromGLTexture3D", (void**)&clCreateFromGLTexture3D};

opencl_gl_fn6(OPENCL_GL_FN_clEnqueueAcquireGLObjects, cl_int, (cl_command_queue p1, cl_uint p2, const cl_mem* p3, cl_uint p4, const cl_event* p5, cl_event* p6))
cl_int (CL_API_CALL*clEnqueueAcquireGLObjects)(cl_command_queue, cl_uint, const cl_mem*, cl_uint, const cl_event*, cl_event*) =
        OPENCL_GL_FN_clEnqueueAcquireGLObjects_switch_fn;
static const struct DynamicFnEntry clEnqueueAcquireGLObjects_definition = { "clEnqueueAcquireGLObjects", (void**)&clEnqueueAcquireGLObjects};

opencl_gl_fn6(OPENCL_GL_FN_clEnqueueReleaseGLObjects, cl_int, (cl_command_queue p1, cl_uint p2, const cl_mem* p3, cl_uint p4, const cl_event* p5, cl_event* p6))
cl_int (CL_API_CALL*clEnqueueReleaseGLObjects)(cl_command_queue, cl_uint, const cl_mem*, cl_uint, const cl_event*, cl_event*) =
        OPENCL_GL_FN_clEnqueueReleaseGLObjects_switch_fn;
static const struct DynamicFnEntry clEnqueueReleaseGLObjects_definition = { "clEnqueueReleaseGLObjects", (void**)&clEnqueueReleaseGLObjects};

opencl_gl_fn5(OPENCL_GL_FN_clGetGLContextInfoKHR, cl_int, (const cl_context_properties* p1, cl_gl_context_info p2, size_t p3, void* p4, size_t* p5))
cl_int (CL_API_CALL*clGetGLContextInfoKHR)(const cl_context_properties*, cl_gl_context_info, size_t, void*, size_t*) =
        OPENCL_GL_FN_clGetGLContextInfoKHR_switch_fn;
static const struct DynamicFnEntry clGetGLContextInfoKHR_definition = { "clGetGLContextInfoKHR", (void**)&clGetGLContextInfoKHR};

opencl_gl_fn3(OPENCL_GL_FN_clGetGLObjectInfo, cl_int, (cl_mem p1, cl_gl_object_type* p2, cl_GLuint* p3))
cl_int (CL_API_CALL*clGetGLObjectInfo)(cl_mem, cl_gl_object_type*, cl_GLuint*) =
        OPENCL_GL_FN_clGetGLObjectInfo_switch_fn;
static const struct DynamicFnEntry clGetGLObjectInfo_definition = { "clGetGLObjectInfo", (void**)&clGetGLObjectInfo};

opencl_gl_fn5(OPENCL_GL_FN_clGetGLTextureInfo, cl_int, (cl_mem p1, cl_gl_texture_info p2, size_t p3, void* p4, size_t* p5))
cl_int (CL_API_CALL*clGetGLTextureInfo)(cl_mem, cl_gl_texture_info, size_t, void*, size_t*) =
        OPENCL_GL_FN_clGetGLTextureInfo_switch_fn;
static const struct DynamicFnEntry clGetGLTextureInfo_definition = { "clGetGLTextureInfo", (void**)&clGetGLTextureInfo};


// generated by parser_cl.py
static const struct DynamicFnEntry* opencl_gl_fn_list[] = {
    &clCreateFromGLBuffer_definition,
    &clCreateFromGLRenderbuffer_definition,
    &clCreateFromGLTexture_definition,
    &clCreateFromGLTexture2D_definition,
    &clCreateFromGLTexture3D_definition,
    &clEnqueueAcquireGLObjects_definition,
    &clEnqueueReleaseGLObjects_definition,
    &clGetGLContextInfoKHR_definition,
    &clGetGLObjectInfo_definition,
    &clGetGLTextureInfo_definition,
};

// number of enabled functions: 10

#endif // cl_khr_gl_sharing
