// This file is auto-generated. Do not edit!

#include "opencv2/core/ocl.hpp"
#include "opencv2/core/ocl_genbase.hpp"
#include "opencv2/core/opencl/ocl_defs.hpp"

#ifdef HAVE_OPENCL

namespace cv
{
namespace ocl
{
namespace features2d
{

extern struct cv::ocl::internal::ProgramEntry akaze_oclsrc;
extern struct cv::ocl::internal::ProgramEntry brute_force_match_oclsrc;
extern struct cv::ocl::internal::ProgramEntry fast_oclsrc;
extern struct cv::ocl::internal::ProgramEntry orb_oclsrc;

}}}
#endif
