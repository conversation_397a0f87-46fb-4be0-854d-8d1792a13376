<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Context reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">Context reference</div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>This is the reference documentation for OpenGL and OpenGL ES context related functions. For more task-oriented information, see the <a class="el" href="context_guide.html">Context guide</a>. </p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga3d47c2d2fbe0be9c505d0e04e91a133c" id="r_ga3d47c2d2fbe0be9c505d0e04e91a133c"><td class="memItemLeft" align="right" valign="top">typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__context.html#ga3d47c2d2fbe0be9c505d0e04e91a133c">GLFWglproc</a>) (void)</td></tr>
<tr class="memdesc:ga3d47c2d2fbe0be9c505d0e04e91a133c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Client API function pointer type.  <br /></td></tr>
<tr class="separator:ga3d47c2d2fbe0be9c505d0e04e91a133c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga1c04dc242268f827290fe40aa1c91157" id="r_ga1c04dc242268f827290fe40aa1c91157"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__context.html#ga1c04dc242268f827290fe40aa1c91157">glfwMakeContextCurrent</a> (<a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *window)</td></tr>
<tr class="memdesc:ga1c04dc242268f827290fe40aa1c91157"><td class="mdescLeft">&#160;</td><td class="mdescRight">Makes the context of the specified window current for the calling thread.  <br /></td></tr>
<tr class="separator:ga1c04dc242268f827290fe40aa1c91157"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad94e80185397a6cf5fe2ab30567af71c" id="r_gad94e80185397a6cf5fe2ab30567af71c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__context.html#gad94e80185397a6cf5fe2ab30567af71c">glfwGetCurrentContext</a> (void)</td></tr>
<tr class="memdesc:gad94e80185397a6cf5fe2ab30567af71c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the window whose context is current on the calling thread.  <br /></td></tr>
<tr class="separator:gad94e80185397a6cf5fe2ab30567af71c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6d4e0cdf151b5e579bd67f13202994ed" id="r_ga6d4e0cdf151b5e579bd67f13202994ed"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed">glfwSwapInterval</a> (int interval)</td></tr>
<tr class="memdesc:ga6d4e0cdf151b5e579bd67f13202994ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the swap interval for the current context.  <br /></td></tr>
<tr class="separator:ga6d4e0cdf151b5e579bd67f13202994ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga87425065c011cef1ebd6aac75e059dfa" id="r_ga87425065c011cef1ebd6aac75e059dfa"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__context.html#ga87425065c011cef1ebd6aac75e059dfa">glfwExtensionSupported</a> (const char *extension)</td></tr>
<tr class="memdesc:ga87425065c011cef1ebd6aac75e059dfa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the specified extension is available.  <br /></td></tr>
<tr class="separator:ga87425065c011cef1ebd6aac75e059dfa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga35f1837e6f666781842483937612f163" id="r_ga35f1837e6f666781842483937612f163"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__context.html#ga3d47c2d2fbe0be9c505d0e04e91a133c">GLFWglproc</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a> (const char *procname)</td></tr>
<tr class="memdesc:ga35f1837e6f666781842483937612f163"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the address of the specified function for the current context.  <br /></td></tr>
<tr class="separator:ga35f1837e6f666781842483937612f163"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Typedef Documentation</h2>
<a id="ga3d47c2d2fbe0be9c505d0e04e91a133c" name="ga3d47c2d2fbe0be9c505d0e04e91a133c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3d47c2d2fbe0be9c505d0e04e91a133c">&#9670;&#160;</a></span>GLFWglproc</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef void(* GLFWglproc) (void)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Generic function pointer used for returning client API function pointers without forcing a cast from a regular pointer.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="context_guide.html#context_glext">OpenGL and OpenGL ES extensions</a> </dd>
<dd>
<a class="el" href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="ga1c04dc242268f827290fe40aa1c91157" name="ga1c04dc242268f827290fe40aa1c91157"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1c04dc242268f827290fe40aa1c91157">&#9670;&#160;</a></span>glfwMakeContextCurrent()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwMakeContextCurrent </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> *&#160;</td>
          <td class="paramname"><em>window</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function makes the OpenGL or OpenGL ES context of the specified window current on the calling thread. It can also detach the current context from the calling thread without making a new one current by passing in <code>NULL</code>.</p>
<p>A context must only be made current on a single thread at a time and each thread can have only a single current context at a time. Making a context current detaches any previously current context on the calling thread.</p>
<p>When moving a context between threads, you must detach it (make it non-current) on the old thread before making it current on the new one.</p>
<p>By default, making a context non-current implicitly forces a pipeline flush. On machines that support <code>GL_KHR_context_flush_control</code>, you can control whether a context performs this flush by setting the <a class="el" href="window_guide.html#GLFW_CONTEXT_RELEASE_BEHAVIOR_hint">GLFW_CONTEXT_RELEASE_BEHAVIOR</a> hint.</p>
<p>The specified window must have an OpenGL or OpenGL ES context. Specifying a window without a context will generate a <a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a> error.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">window</td><td>The window whose context to make current, or <code>NULL</code> to detach the current context.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section remark"><dt>Remarks</dt><dd>If the previously current context was created via a different context creation API than the one passed to this function, GLFW will still detach the previous one from its API before making the new one current.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gacff24d2757da752ae4c80bf452356487">GLFW_NO_WINDOW_CONTEXT</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="context_guide.html#context_current">Current context</a> </dd>
<dd>
<a class="el" href="group__context.html#gad94e80185397a6cf5fe2ab30567af71c">glfwGetCurrentContext</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="gad94e80185397a6cf5fe2ab30567af71c" name="gad94e80185397a6cf5fe2ab30567af71c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad94e80185397a6cf5fe2ab30567af71c">&#9670;&#160;</a></span>glfwGetCurrentContext()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__window.html#ga3c96d80d363e67d13a41b5d1821f3242">GLFWwindow</a> * glfwGetCurrentContext </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the window whose OpenGL or OpenGL ES context is current on the calling thread.</p>
<dl class="section return"><dt>Returns</dt><dd>The window whose context is current, or <code>NULL</code> if no window's context is current.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="context_guide.html#context_current">Current context</a> </dd>
<dd>
<a class="el" href="group__context.html#ga1c04dc242268f827290fe40aa1c91157">glfwMakeContextCurrent</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>

</div>
</div>
<a id="ga6d4e0cdf151b5e579bd67f13202994ed" name="ga6d4e0cdf151b5e579bd67f13202994ed"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6d4e0cdf151b5e579bd67f13202994ed">&#9670;&#160;</a></span>glfwSwapInterval()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void glfwSwapInterval </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>interval</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the swap interval for the current OpenGL or OpenGL ES context, i.e. the number of screen updates to wait from the time <a class="el" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a> was called before swapping the buffers and returning. This is sometimes called <em>vertical synchronization</em>, <em>vertical retrace synchronization</em> or just <em>vsync</em>.</p>
<p>A context that supports either of the <code>WGL_EXT_swap_control_tear</code> and <code>GLX_EXT_swap_control_tear</code> extensions also accepts <em>negative</em> swap intervals, which allows the driver to swap immediately even if a frame arrives a little bit late. You can check for these extensions with <a class="el" href="group__context.html#ga87425065c011cef1ebd6aac75e059dfa">glfwExtensionSupported</a>.</p>
<p>A context must be current on the calling thread. Calling this function without a current context will cause a <a class="el" href="group__errors.html#gaa8290386e9528ccb9e42a3a4e16fc0d0">GLFW_NO_CURRENT_CONTEXT</a> error.</p>
<p>This function does not apply to Vulkan. If you are rendering with Vulkan, see the present mode of your swapchain instead.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">interval</td><td>The minimum number of screen updates to wait for until the buffers are swapped by <a class="el" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gaa8290386e9528ccb9e42a3a4e16fc0d0">GLFW_NO_CURRENT_CONTEXT</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>This function is not called during context creation, leaving the swap interval set to whatever is the default for that API. This is done because some swap interval extensions used by GLFW do not allow the swap interval to be reset to zero once it has been set to a non-zero value.</dd>
<dd>
Some GPU drivers do not honor the requested swap interval, either because of a user setting that overrides the application's request or due to bugs in the driver.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="window_guide.html#buffer_swap">Buffer swapping</a> </dd>
<dd>
<a class="el" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. </dd></dl>

</div>
</div>
<a id="ga87425065c011cef1ebd6aac75e059dfa" name="ga87425065c011cef1ebd6aac75e059dfa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga87425065c011cef1ebd6aac75e059dfa">&#9670;&#160;</a></span>glfwExtensionSupported()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int glfwExtensionSupported </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>extension</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns whether the specified <a class="el" href="context_guide.html#context_glext">API extension</a> is supported by the current OpenGL or OpenGL ES context. It searches both for client API extension and context creation API extensions.</p>
<p>A context must be current on the calling thread. Calling this function without a current context will cause a <a class="el" href="group__errors.html#gaa8290386e9528ccb9e42a3a4e16fc0d0">GLFW_NO_CURRENT_CONTEXT</a> error.</p>
<p>As this functions retrieves and searches one or more extension strings each call, it is recommended that you cache its results if it is going to be used frequently. The extension strings will not change during the lifetime of a context, so there is no danger in doing this.</p>
<p>This function does not apply to Vulkan. If you are using Vulkan, see <a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a>, <code>vkEnumerateInstanceExtensionProperties</code> and <code>vkEnumerateDeviceExtensionProperties</code> instead.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">extension</td><td>The ASCII encoded name of the extension. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><code>GLFW_TRUE</code> if the extension is available, or <code>GLFW_FALSE</code> otherwise.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gaa8290386e9528ccb9e42a3a4e16fc0d0">GLFW_NO_CURRENT_CONTEXT</a>, <a class="el" href="group__errors.html#gaaf2ef9aa8202c2b82ac2d921e554c687">GLFW_INVALID_VALUE</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="context_guide.html#context_glext">OpenGL and OpenGL ES extensions</a> </dd>
<dd>
<a class="el" href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. </dd></dl>

</div>
</div>
<a id="ga35f1837e6f666781842483937612f163" name="ga35f1837e6f666781842483937612f163"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga35f1837e6f666781842483937612f163">&#9670;&#160;</a></span>glfwGetProcAddress()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__context.html#ga3d47c2d2fbe0be9c505d0e04e91a133c">GLFWglproc</a> glfwGetProcAddress </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>procname</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the address of the specified OpenGL or OpenGL ES <a class="el" href="context_guide.html#context_glext">core or extension function</a>, if it is supported by the current context.</p>
<p>A context must be current on the calling thread. Calling this function without a current context will cause a <a class="el" href="group__errors.html#gaa8290386e9528ccb9e42a3a4e16fc0d0">GLFW_NO_CURRENT_CONTEXT</a> error.</p>
<p>This function does not apply to Vulkan. If you are rendering with Vulkan, see <a class="el" href="group__vulkan.html#gadf228fac94c5fd8f12423ec9af9ff1e9">glfwGetInstanceProcAddress</a>, <code>vkGetInstanceProcAddr</code> and <code>vkGetDeviceProcAddr</code> instead.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">procname</td><td>The ASCII encoded name of the function. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The address of the function, or <code>NULL</code> if an <a class="el" href="intro_guide.html#error_handling">error</a> occurred.</dd></dl>
<dl class="section user"><dt>Errors</dt><dd>Possible errors include <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a>, <a class="el" href="group__errors.html#gaa8290386e9528ccb9e42a3a4e16fc0d0">GLFW_NO_CURRENT_CONTEXT</a> and <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a>.</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd>The address of a given function is not guaranteed to be the same between contexts.</dd>
<dd>
This function may return a non-<code>NULL</code> address despite the associated version or extension not being available. Always check the context version or extension string first.</dd></dl>
<dl class="section user"><dt>Pointer lifetime</dt><dd>The returned function pointer is valid until the context is destroyed or the library is terminated.</dd></dl>
<dl class="section user"><dt>Thread safety</dt><dd>This function may be called from any thread.</dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="context_guide.html#context_glext">OpenGL and OpenGL ES extensions</a> </dd>
<dd>
<a class="el" href="group__context.html#ga87425065c011cef1ebd6aac75e059dfa">glfwExtensionSupported</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 1.0. </dd></dl>

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
