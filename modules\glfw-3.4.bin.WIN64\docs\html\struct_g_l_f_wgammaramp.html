<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: GLFWgammaramp Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">GLFWgammaramp Struct Reference<div class="ingroups"><a class="el" href="group__monitor.html">Monitor reference</a></div></div></div>
</div><!--header-->
<div class="contents">

<p>Gamma ramp.  
 <a href="struct_g_l_f_wgammaramp.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a2cce5d968734b685623eef913e635138" id="r_a2cce5d968734b685623eef913e635138"><td class="memItemLeft" align="right" valign="top">unsigned short *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wgammaramp.html#a2cce5d968734b685623eef913e635138">red</a></td></tr>
<tr class="separator:a2cce5d968734b685623eef913e635138"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:affccc6f5df47820b6562d709da3a5a3a" id="r_affccc6f5df47820b6562d709da3a5a3a"><td class="memItemLeft" align="right" valign="top">unsigned short *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wgammaramp.html#affccc6f5df47820b6562d709da3a5a3a">green</a></td></tr>
<tr class="separator:affccc6f5df47820b6562d709da3a5a3a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acf0c836d0efe29c392fe8d1a1042744b" id="r_acf0c836d0efe29c392fe8d1a1042744b"><td class="memItemLeft" align="right" valign="top">unsigned short *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wgammaramp.html#acf0c836d0efe29c392fe8d1a1042744b">blue</a></td></tr>
<tr class="separator:acf0c836d0efe29c392fe8d1a1042744b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad620e1cffbff9a32c51bca46301b59a5" id="r_ad620e1cffbff9a32c51bca46301b59a5"><td class="memItemLeft" align="right" valign="top">unsigned int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_g_l_f_wgammaramp.html#ad620e1cffbff9a32c51bca46301b59a5">size</a></td></tr>
<tr class="separator:ad620e1cffbff9a32c51bca46301b59a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>This describes the gamma ramp for a monitor.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="monitor_guide.html#monitor_gamma">Gamma ramp</a> </dd>
<dd>
<a class="el" href="group__monitor.html#ga76ba90debcf0062b5c4b73052b24f96f">glfwGetGammaRamp</a> </dd>
<dd>
<a class="el" href="group__monitor.html#ga583f0ffd0d29613d8cd172b996bbf0dd">glfwSetGammaRamp</a></dd></dl>
<dl class="section since"><dt>Since</dt><dd>Added in version 3.0. </dd></dl>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="a2cce5d968734b685623eef913e635138" name="a2cce5d968734b685623eef913e635138"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2cce5d968734b685623eef913e635138">&#9670;&#160;</a></span>red</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">unsigned short* GLFWgammaramp::red</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>An array of value describing the response of the red channel. </p>

</div>
</div>
<a id="affccc6f5df47820b6562d709da3a5a3a" name="affccc6f5df47820b6562d709da3a5a3a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#affccc6f5df47820b6562d709da3a5a3a">&#9670;&#160;</a></span>green</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">unsigned short* GLFWgammaramp::green</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>An array of value describing the response of the green channel. </p>

</div>
</div>
<a id="acf0c836d0efe29c392fe8d1a1042744b" name="acf0c836d0efe29c392fe8d1a1042744b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acf0c836d0efe29c392fe8d1a1042744b">&#9670;&#160;</a></span>blue</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">unsigned short* GLFWgammaramp::blue</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>An array of value describing the response of the blue channel. </p>

</div>
</div>
<a id="ad620e1cffbff9a32c51bca46301b59a5" name="ad620e1cffbff9a32c51bca46301b59a5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad620e1cffbff9a32c51bca46301b59a5">&#9670;&#160;</a></span>size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">unsigned int GLFWgammaramp::size</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The number of elements in each array. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="glfw3_8h_source.html">glfw3.h</a></li>
</ul>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
