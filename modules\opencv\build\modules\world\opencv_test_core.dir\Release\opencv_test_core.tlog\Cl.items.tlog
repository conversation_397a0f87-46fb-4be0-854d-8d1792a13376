D:\AI\opencv\opencv-4.10.0\modules\core\test\ocl\test_arithm.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\__\core\test\ocl\test_arithm.cpp.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\ocl\test_channels.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_channels.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\ocl\test_dft.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_dft.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\ocl\test_gemm.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_gemm.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\ocl\test_image2d.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_image2d.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\ocl\test_matrix_expr.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_matrix_expr.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\ocl\test_matrix_operation.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_matrix_operation.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\ocl\test_opencl.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\__\core\test\ocl\test_opencl.cpp.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_arithm.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\__\core\test\test_arithm.cpp.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_async.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_async.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_concatenation.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_concatenation.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_conjugate_gradient.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_conjugate_gradient.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_countnonzero.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_countnonzero.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_cuda.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_cuda.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_downhill_simplex.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_downhill_simplex.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_ds.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_ds.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_dxt.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_dxt.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_eigen.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_eigen.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_hal_core.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_hal_core.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_hasnonzero.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_hasnonzero.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_intrin.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_intrin.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_intrin_emulator.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_intrin_emulator.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_io.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_io.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_logtagconfigparser.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_logtagconfigparser.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_logtagmanager.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_logtagmanager.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_lpsolver.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_lpsolver.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_main.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_main.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_mat.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_mat.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_math.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_math.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_misc.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_misc.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_opencl.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\__\core\test\test_opencl.cpp.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_operations.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_operations.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_ptr.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_ptr.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_quaternion.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_quaternion.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_rand.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_rand.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_rotatedrect.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_rotatedrect.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_umat.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_umat.obj
D:\AI\opencv\opencv-4.10.0\modules\core\test\test_utils.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_test_core.dir\Release\test_utils.obj
