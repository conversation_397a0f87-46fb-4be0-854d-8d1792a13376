//
// This file is auto-generated. Please don't modify it!
//

#import "SelectiveSearchSegmentationStrategyFill.h"
#import "CVObjcUtil.h"



@implementation SelectiveSearchSegmentationStrategyFill


- (instancetype)initWithNativePtr:(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyFill>)nativePtr {
    self = [super initWithNativePtr:nativePtr];
    if (self) {
        _nativePtrSelectiveSearchSegmentationStrategyFill = nativePtr;
    }
    return self;
}

+ (instancetype)fromNative:(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyFill>)nativePtr {
    return [[SelectiveSearchSegmentationStrategyFill alloc] initWithNativePtr:nativePtr];
}




@end


