//
// This file is auto-generated. Please don't modify it!
//

#import "SelectiveSearchSegmentationStrategyColor.h"
#import "CVObjcUtil.h"



@implementation SelectiveSearchSegmentationStrategyColor


- (instancetype)initWithNativePtr:(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyColor>)nativePtr {
    self = [super initWithNativePtr:nativePtr];
    if (self) {
        _nativePtrSelectiveSearchSegmentationStrategyColor = nativePtr;
    }
    return self;
}

+ (instancetype)fromNative:(cv::Ptr<cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyColor>)nativePtr {
    return [[SelectiveSearchSegmentationStrategyColor alloc] initWithNativePtr:nativePtr];
}




@end


