// This file is part of OpenCV project.
// It is subject to the license terms in the LICENSE file found in the top-level directory
// of this distribution and at http://opencv.org/license.html.
//
// <PERSON><PERSON> is pleased to support the open source community by making WeChat QRCode available.
// Copyright (C) 2020 THL A29 Limited, a Tencent company. All rights reserved.

#ifndef __OPENCV_WECHAT_QRCODE_PRECOMP_HPP__
#define __OPENCV_WECHAT_QRCODE_PRECOMP_HPP__
#ifdef _MSC_VER
#pragma warning(disable: 4244)
#pragma warning(disable: 4267)
#endif
#include <stdint.h>
#include <stdio.h>
#include <algorithm>
#include <cmath>
#include <cstdlib>
#include <memory>
#include <string>
#include <tuple>
#include <utility>
#include <vector>
#include <map>
#include "imgsource.hpp"
using std::ostringstream;
using std::string;
using std::vector;
#endif  // __OPENCV_WECHAT_QRCODE_PRECOMP_HPP__
