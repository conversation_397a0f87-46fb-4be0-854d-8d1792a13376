D:\AI\opencv\cudabuild\modules\world\mathfuncs_core.avx.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_world_AVX.dir\Release\mathfuncs_core.avx.obj
D:\AI\opencv\opencv-4.10.0\modules\imgproc\src\corner.avx.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_world_AVX.dir\Release\corner.avx.obj
D:\AI\opencv\cudabuild\modules\world\accum.avx.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_world_AVX.dir\Release\accum.avx.obj
D:\AI\opencv\cudabuild\modules\world\layers\layers_common.avx.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_world_AVX.dir\Release\layers_common.avx.obj
D:\AI\opencv\cudabuild\modules\world\layers\cpu_kernels\conv_block.avx.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_world_AVX.dir\Release\conv_block.avx.obj
D:\AI\opencv\cudabuild\modules\world\layers\cpu_kernels\conv_depthwise.avx.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_world_AVX.dir\Release\conv_depthwise.avx.obj
D:\AI\opencv\cudabuild\modules\world\layers\cpu_kernels\conv_winograd_f63.avx.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_world_AVX.dir\Release\conv_winograd_f63.avx.obj
D:\AI\opencv\cudabuild\modules\world\layers\cpu_kernels\fast_gemm_kernels.avx.cpp;D:\AI\opencv\cudabuild\modules\world\opencv_world_AVX.dir\Release\fast_gemm_kernels.avx.obj
