//
// This file is auto-generated. Please don't modify it!
//
#pragma once

#ifdef __cplusplus
//#import "opencv.hpp"
$additionalImports
#else
#define CV_EXPORTS
#endif

#import <Foundation/Foundation.h>
$importBaseClass

$forwardDeclarations

$enumDeclarations

NS_ASSUME_NONNULL_BEGIN

$docs
CV_EXPORTS @interface $objcName : $base

$nativePointerHandling

$methodDeclarations

@end

NS_ASSUME_NONNULL_END
