// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_ApplicationModel_Calls_2_H
#define WINRT_Windows_ApplicationModel_Calls_2_H
#include "winrt/impl/Windows.Foundation.1.h"
#include "winrt/impl/Windows.Foundation.Collections.1.h"
#include "winrt/impl/Windows.System.1.h"
#include "winrt/impl/Windows.ApplicationModel.Calls.1.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Calls
{
    struct WINRT_IMPL_EMPTY_BASES AcceptedVoipPhoneCallOptions : winrt::Windows::ApplicationModel::Calls::IAcceptedVoipPhoneCallOptions
    {
        AcceptedVoipPhoneCallOptions(std::nullptr_t) noexcept {}
        AcceptedVoipPhoneCallOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IAcceptedVoipPhoneCallOptions(ptr, take_ownership_from_abi) {}
        AcceptedVoipPhoneCallOptions();
        explicit AcceptedVoipPhoneCallOptions(param::iterable<hstring> const& associatedDeviceIds);
    };
    struct WINRT_IMPL_EMPTY_BASES AppInitiatedVoipPhoneCallOptions : winrt::Windows::ApplicationModel::Calls::IAppInitiatedVoipPhoneCallOptions
    {
        AppInitiatedVoipPhoneCallOptions(std::nullptr_t) noexcept {}
        AppInitiatedVoipPhoneCallOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IAppInitiatedVoipPhoneCallOptions(ptr, take_ownership_from_abi) {}
        AppInitiatedVoipPhoneCallOptions();
        explicit AppInitiatedVoipPhoneCallOptions(param::iterable<hstring> const& associatedDeviceIds);
    };
    struct WINRT_IMPL_EMPTY_BASES CallAnswerEventArgs : winrt::Windows::ApplicationModel::Calls::ICallAnswerEventArgs,
        impl::require<CallAnswerEventArgs, winrt::Windows::ApplicationModel::Calls::ICallAnswerEventArgs2>
    {
        CallAnswerEventArgs(std::nullptr_t) noexcept {}
        CallAnswerEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::ICallAnswerEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES CallRejectEventArgs : winrt::Windows::ApplicationModel::Calls::ICallRejectEventArgs
    {
        CallRejectEventArgs(std::nullptr_t) noexcept {}
        CallRejectEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::ICallRejectEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES CallStateChangeEventArgs : winrt::Windows::ApplicationModel::Calls::ICallStateChangeEventArgs
    {
        CallStateChangeEventArgs(std::nullptr_t) noexcept {}
        CallStateChangeEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::ICallStateChangeEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IncomingVoipPhoneCallOptions : winrt::Windows::ApplicationModel::Calls::IIncomingVoipPhoneCallOptions
    {
        IncomingVoipPhoneCallOptions(std::nullptr_t) noexcept {}
        IncomingVoipPhoneCallOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IIncomingVoipPhoneCallOptions(ptr, take_ownership_from_abi) {}
        IncomingVoipPhoneCallOptions();
        explicit IncomingVoipPhoneCallOptions(param::iterable<hstring> const& associatedDeviceIds);
    };
    struct WINRT_IMPL_EMPTY_BASES LockScreenCallEndCallDeferral : winrt::Windows::ApplicationModel::Calls::ILockScreenCallEndCallDeferral
    {
        LockScreenCallEndCallDeferral(std::nullptr_t) noexcept {}
        LockScreenCallEndCallDeferral(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::ILockScreenCallEndCallDeferral(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES LockScreenCallEndRequestedEventArgs : winrt::Windows::ApplicationModel::Calls::ILockScreenCallEndRequestedEventArgs
    {
        LockScreenCallEndRequestedEventArgs(std::nullptr_t) noexcept {}
        LockScreenCallEndRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::ILockScreenCallEndRequestedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES LockScreenCallUI : winrt::Windows::ApplicationModel::Calls::ILockScreenCallUI
    {
        LockScreenCallUI(std::nullptr_t) noexcept {}
        LockScreenCallUI(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::ILockScreenCallUI(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES MuteChangeEventArgs : winrt::Windows::ApplicationModel::Calls::IMuteChangeEventArgs
    {
        MuteChangeEventArgs(std::nullptr_t) noexcept {}
        MuteChangeEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IMuteChangeEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES OutgoingVoipPhoneCallOptions : winrt::Windows::ApplicationModel::Calls::IOutgoingVoipPhoneCallOptions
    {
        OutgoingVoipPhoneCallOptions(std::nullptr_t) noexcept {}
        OutgoingVoipPhoneCallOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IOutgoingVoipPhoneCallOptions(ptr, take_ownership_from_abi) {}
        OutgoingVoipPhoneCallOptions();
        explicit OutgoingVoipPhoneCallOptions(param::iterable<hstring> const& associatedDeviceIds);
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneCall : winrt::Windows::ApplicationModel::Calls::IPhoneCall
    {
        PhoneCall(std::nullptr_t) noexcept {}
        PhoneCall(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneCall(ptr, take_ownership_from_abi) {}
        static auto GetFromId(param::hstring const& callId);
    };
    struct PhoneCallBlocking
    {
        PhoneCallBlocking() = delete;
        [[nodiscard]] static auto BlockUnknownNumbers();
        static auto BlockUnknownNumbers(bool value);
        [[nodiscard]] static auto BlockPrivateNumbers();
        static auto BlockPrivateNumbers(bool value);
        static auto SetCallBlockingListAsync(param::async_iterable<hstring> const& phoneNumberList);
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneCallHistoryEntry : winrt::Windows::ApplicationModel::Calls::IPhoneCallHistoryEntry
    {
        PhoneCallHistoryEntry(std::nullptr_t) noexcept {}
        PhoneCallHistoryEntry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneCallHistoryEntry(ptr, take_ownership_from_abi) {}
        PhoneCallHistoryEntry();
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneCallHistoryEntryAddress : winrt::Windows::ApplicationModel::Calls::IPhoneCallHistoryEntryAddress
    {
        PhoneCallHistoryEntryAddress(std::nullptr_t) noexcept {}
        PhoneCallHistoryEntryAddress(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneCallHistoryEntryAddress(ptr, take_ownership_from_abi) {}
        PhoneCallHistoryEntryAddress();
        PhoneCallHistoryEntryAddress(param::hstring const& rawAddress, winrt::Windows::ApplicationModel::Calls::PhoneCallHistoryEntryRawAddressKind const& rawAddressKind);
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneCallHistoryEntryQueryOptions : winrt::Windows::ApplicationModel::Calls::IPhoneCallHistoryEntryQueryOptions
    {
        PhoneCallHistoryEntryQueryOptions(std::nullptr_t) noexcept {}
        PhoneCallHistoryEntryQueryOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneCallHistoryEntryQueryOptions(ptr, take_ownership_from_abi) {}
        PhoneCallHistoryEntryQueryOptions();
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneCallHistoryEntryReader : winrt::Windows::ApplicationModel::Calls::IPhoneCallHistoryEntryReader
    {
        PhoneCallHistoryEntryReader(std::nullptr_t) noexcept {}
        PhoneCallHistoryEntryReader(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneCallHistoryEntryReader(ptr, take_ownership_from_abi) {}
    };
    struct PhoneCallHistoryManager
    {
        PhoneCallHistoryManager() = delete;
        static auto RequestStoreAsync(winrt::Windows::ApplicationModel::Calls::PhoneCallHistoryStoreAccessType const& accessType);
        static auto GetForUser(winrt::Windows::System::User const& user);
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneCallHistoryManagerForUser : winrt::Windows::ApplicationModel::Calls::IPhoneCallHistoryManagerForUser
    {
        PhoneCallHistoryManagerForUser(std::nullptr_t) noexcept {}
        PhoneCallHistoryManagerForUser(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneCallHistoryManagerForUser(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneCallHistoryStore : winrt::Windows::ApplicationModel::Calls::IPhoneCallHistoryStore
    {
        PhoneCallHistoryStore(std::nullptr_t) noexcept {}
        PhoneCallHistoryStore(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneCallHistoryStore(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneCallInfo : winrt::Windows::ApplicationModel::Calls::IPhoneCallInfo
    {
        PhoneCallInfo(std::nullptr_t) noexcept {}
        PhoneCallInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneCallInfo(ptr, take_ownership_from_abi) {}
    };
    struct PhoneCallManager
    {
        PhoneCallManager() = delete;
        static auto ShowPhoneCallUI(param::hstring const& phoneNumber, param::hstring const& displayName);
        static auto CallStateChanged(winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler);
        using CallStateChanged_revoker = impl::factory_event_revoker<winrt::Windows::ApplicationModel::Calls::IPhoneCallManagerStatics2, &impl::abi_t<winrt::Windows::ApplicationModel::Calls::IPhoneCallManagerStatics2>::remove_CallStateChanged>;
        [[nodiscard]] static auto CallStateChanged(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler);
        static auto CallStateChanged(winrt::event_token const& token);
        [[nodiscard]] static auto IsCallActive();
        [[nodiscard]] static auto IsCallIncoming();
        static auto ShowPhoneCallSettingsUI();
        static auto RequestStoreAsync();
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneCallStore : winrt::Windows::ApplicationModel::Calls::IPhoneCallStore
    {
        PhoneCallStore(std::nullptr_t) noexcept {}
        PhoneCallStore(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneCallStore(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneCallVideoCapabilities : winrt::Windows::ApplicationModel::Calls::IPhoneCallVideoCapabilities
    {
        PhoneCallVideoCapabilities(std::nullptr_t) noexcept {}
        PhoneCallVideoCapabilities(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneCallVideoCapabilities(ptr, take_ownership_from_abi) {}
    };
    struct PhoneCallVideoCapabilitiesManager
    {
        PhoneCallVideoCapabilitiesManager() = delete;
        static auto GetCapabilitiesAsync(param::hstring const& phoneNumber);
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneCallsResult : winrt::Windows::ApplicationModel::Calls::IPhoneCallsResult
    {
        PhoneCallsResult(std::nullptr_t) noexcept {}
        PhoneCallsResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneCallsResult(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneDialOptions : winrt::Windows::ApplicationModel::Calls::IPhoneDialOptions
    {
        PhoneDialOptions(std::nullptr_t) noexcept {}
        PhoneDialOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneDialOptions(ptr, take_ownership_from_abi) {}
        PhoneDialOptions();
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneLine : winrt::Windows::ApplicationModel::Calls::IPhoneLine,
        impl::require<PhoneLine, winrt::Windows::ApplicationModel::Calls::IPhoneLine2, winrt::Windows::ApplicationModel::Calls::IPhoneLine3>
    {
        PhoneLine(std::nullptr_t) noexcept {}
        PhoneLine(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneLine(ptr, take_ownership_from_abi) {}
        static auto FromIdAsync(winrt::guid const& lineId);
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneLineCellularDetails : winrt::Windows::ApplicationModel::Calls::IPhoneLineCellularDetails
    {
        PhoneLineCellularDetails(std::nullptr_t) noexcept {}
        PhoneLineCellularDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneLineCellularDetails(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneLineConfiguration : winrt::Windows::ApplicationModel::Calls::IPhoneLineConfiguration
    {
        PhoneLineConfiguration(std::nullptr_t) noexcept {}
        PhoneLineConfiguration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneLineConfiguration(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneLineDialResult : winrt::Windows::ApplicationModel::Calls::IPhoneLineDialResult
    {
        PhoneLineDialResult(std::nullptr_t) noexcept {}
        PhoneLineDialResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneLineDialResult(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneLineTransportDevice : winrt::Windows::ApplicationModel::Calls::IPhoneLineTransportDevice,
        impl::require<PhoneLineTransportDevice, winrt::Windows::ApplicationModel::Calls::IPhoneLineTransportDevice2>
    {
        PhoneLineTransportDevice(std::nullptr_t) noexcept {}
        PhoneLineTransportDevice(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneLineTransportDevice(ptr, take_ownership_from_abi) {}
        static auto FromId(param::hstring const& id);
        static auto GetDeviceSelector();
        static auto GetDeviceSelector(winrt::Windows::ApplicationModel::Calls::PhoneLineTransport const& transport);
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneLineWatcher : winrt::Windows::ApplicationModel::Calls::IPhoneLineWatcher
    {
        PhoneLineWatcher(std::nullptr_t) noexcept {}
        PhoneLineWatcher(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneLineWatcher(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneLineWatcherEventArgs : winrt::Windows::ApplicationModel::Calls::IPhoneLineWatcherEventArgs
    {
        PhoneLineWatcherEventArgs(std::nullptr_t) noexcept {}
        PhoneLineWatcherEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneLineWatcherEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PhoneVoicemail : winrt::Windows::ApplicationModel::Calls::IPhoneVoicemail
    {
        PhoneVoicemail(std::nullptr_t) noexcept {}
        PhoneVoicemail(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IPhoneVoicemail(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES VoipCallCoordinator : winrt::Windows::ApplicationModel::Calls::IVoipCallCoordinator,
        impl::require<VoipCallCoordinator, winrt::Windows::ApplicationModel::Calls::IVoipCallCoordinator2, winrt::Windows::ApplicationModel::Calls::IVoipCallCoordinator3, winrt::Windows::ApplicationModel::Calls::IVoipCallCoordinator4, winrt::Windows::ApplicationModel::Calls::IVoipCallCoordinator5>
    {
        VoipCallCoordinator(std::nullptr_t) noexcept {}
        VoipCallCoordinator(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IVoipCallCoordinator(ptr, take_ownership_from_abi) {}
        using winrt::Windows::ApplicationModel::Calls::IVoipCallCoordinator::RequestNewIncomingCall;
        using impl::consume_t<VoipCallCoordinator, winrt::Windows::ApplicationModel::Calls::IVoipCallCoordinator3>::RequestNewIncomingCall;
        using winrt::Windows::ApplicationModel::Calls::IVoipCallCoordinator::ReserveCallResourcesAsync;
        using impl::consume_t<VoipCallCoordinator, winrt::Windows::ApplicationModel::Calls::IVoipCallCoordinator4>::ReserveCallResourcesAsync;
        static auto GetDefault();
        static auto IsCallControlDeviceKindSupportedForAssociation(winrt::Windows::ApplicationModel::Calls::VoipCallControlDeviceKind const& kind);
        static auto GetDeviceSelectorForCallControl();
    };
    struct WINRT_IMPL_EMPTY_BASES VoipPhoneCall : winrt::Windows::ApplicationModel::Calls::IVoipPhoneCall,
        impl::require<VoipPhoneCall, winrt::Windows::ApplicationModel::Calls::IVoipPhoneCall2, winrt::Windows::ApplicationModel::Calls::IVoipPhoneCall3, winrt::Windows::ApplicationModel::Calls::IVoipPhoneCall4>
    {
        VoipPhoneCall(std::nullptr_t) noexcept {}
        VoipPhoneCall(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Calls::IVoipPhoneCall(ptr, take_ownership_from_abi) {}
        using winrt::Windows::ApplicationModel::Calls::IVoipPhoneCall::NotifyCallActive;
        using impl::consume_t<VoipPhoneCall, winrt::Windows::ApplicationModel::Calls::IVoipPhoneCall4>::NotifyCallActive;
    };
}
#endif
