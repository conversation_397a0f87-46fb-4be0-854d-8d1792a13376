﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\common\gapi_core_perf_tests.cpp">
      <Filter>opencv_gapi\Src\common</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\common\gapi_imgproc_perf_tests.cpp">
      <Filter>opencv_gapi\Src\common</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\common\gapi_render_perf_tests.cpp">
      <Filter>opencv_gapi\Src\common</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\common\gapi_video_perf_tests.cpp">
      <Filter>opencv_gapi\Src\common</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\cpu\gapi_core_perf_tests_cpu.cpp">
      <Filter>opencv_gapi\Src\cpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\cpu\gapi_core_perf_tests_fluid.cpp">
      <Filter>opencv_gapi\Src\cpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\cpu\gapi_imgproc_perf_tests_cpu.cpp">
      <Filter>opencv_gapi\Src\cpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\cpu\gapi_imgproc_perf_tests_fluid.cpp">
      <Filter>opencv_gapi\Src\cpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\cpu\gapi_video_perf_tests_cpu.cpp">
      <Filter>opencv_gapi\Src\cpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\gpu\gapi_core_perf_tests_gpu.cpp">
      <Filter>opencv_gapi\Src\gpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\gpu\gapi_imgproc_perf_tests_gpu.cpp">
      <Filter>opencv_gapi\Src\gpu</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\internal\gapi_compiler_perf_tests.cpp">
      <Filter>opencv_gapi\Src\internal</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\perf_bench.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\perf_main.cpp">
      <Filter>opencv_gapi\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\render\gapi_render_perf_tests_ocv.cpp">
      <Filter>opencv_gapi\Src\render</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\streaming\gapi_streaming_source_perf_tests.cpp">
      <Filter>opencv_gapi\Src\streaming</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\common\gapi_core_perf_tests.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\common\gapi_core_perf_tests_inl.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\common\gapi_imgproc_perf_tests.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\common\gapi_imgproc_perf_tests_inl.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\common\gapi_render_perf_tests.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\common\gapi_render_perf_tests_inl.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\common\gapi_video_perf_tests.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\common\gapi_video_perf_tests_inl.hpp">
      <Filter>opencv_gapi\Include\common</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\gapi\perf\perf_precomp.hpp">
      <Filter>opencv_gapi\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_gapi">
      <UniqueIdentifier>{635964F1-90D1-30BB-83E2-A7321E84DF5B}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Include">
      <UniqueIdentifier>{3AACB4B7-2E16-3D87-A4AE-891F4290CB7F}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Include\common">
      <UniqueIdentifier>{FEB4466B-38A3-3EA9-AF52-7E635A89F8CD}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src">
      <UniqueIdentifier>{237426CF-E287-3A3E-9F1C-A63D7BCD5856}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\common">
      <UniqueIdentifier>{233E4BEC-C49B-3457-8F4D-904A1E8416A2}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\cpu">
      <UniqueIdentifier>{54F2A6C1-BA7E-3E2B-8C18-386E2D9F28A0}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\gpu">
      <UniqueIdentifier>{9937F1DC-D06A-3C6E-9C44-004954ED0604}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\internal">
      <UniqueIdentifier>{0FC6E801-193D-3AEA-92A0-CED01F0A6059}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\render">
      <UniqueIdentifier>{5227B584-CDF5-34A2-8A5B-6B0E25B58A18}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_gapi\Src\streaming">
      <UniqueIdentifier>{9B419151-4635-3CC9-B0F8-EEBD6C724C77}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
