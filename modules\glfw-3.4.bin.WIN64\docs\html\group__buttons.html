<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Mouse buttons</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle"><div class="title">Mouse buttons<div class="ingroups"><a class="el" href="group__input.html">Input reference</a></div></div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>See <a class="el" href="input_guide.html#input_mouse_button">mouse button input</a> for how these are used. </p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:ga181a6e875251fd8671654eff00f9112e" id="r_ga181a6e875251fd8671654eff00f9112e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#ga181a6e875251fd8671654eff00f9112e">GLFW_MOUSE_BUTTON_1</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:ga181a6e875251fd8671654eff00f9112e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga604b39b92c88ce9bd332e97fc3f4156c" id="r_ga604b39b92c88ce9bd332e97fc3f4156c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#ga604b39b92c88ce9bd332e97fc3f4156c">GLFW_MOUSE_BUTTON_2</a>&#160;&#160;&#160;1</td></tr>
<tr class="separator:ga604b39b92c88ce9bd332e97fc3f4156c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0130d505563d0236a6f85545f19e1721" id="r_ga0130d505563d0236a6f85545f19e1721"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#ga0130d505563d0236a6f85545f19e1721">GLFW_MOUSE_BUTTON_3</a>&#160;&#160;&#160;2</td></tr>
<tr class="separator:ga0130d505563d0236a6f85545f19e1721"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga53f4097bb01d5521c7d9513418c91ca9" id="r_ga53f4097bb01d5521c7d9513418c91ca9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#ga53f4097bb01d5521c7d9513418c91ca9">GLFW_MOUSE_BUTTON_4</a>&#160;&#160;&#160;3</td></tr>
<tr class="separator:ga53f4097bb01d5521c7d9513418c91ca9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf08c4ddecb051d3d9667db1d5e417c9c" id="r_gaf08c4ddecb051d3d9667db1d5e417c9c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#gaf08c4ddecb051d3d9667db1d5e417c9c">GLFW_MOUSE_BUTTON_5</a>&#160;&#160;&#160;4</td></tr>
<tr class="separator:gaf08c4ddecb051d3d9667db1d5e417c9c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae8513e06aab8aa393b595f22c6d8257a" id="r_gae8513e06aab8aa393b595f22c6d8257a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#gae8513e06aab8aa393b595f22c6d8257a">GLFW_MOUSE_BUTTON_6</a>&#160;&#160;&#160;5</td></tr>
<tr class="separator:gae8513e06aab8aa393b595f22c6d8257a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8b02a1ab55dde45b3a3883d54ffd7dc7" id="r_ga8b02a1ab55dde45b3a3883d54ffd7dc7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#ga8b02a1ab55dde45b3a3883d54ffd7dc7">GLFW_MOUSE_BUTTON_7</a>&#160;&#160;&#160;6</td></tr>
<tr class="separator:ga8b02a1ab55dde45b3a3883d54ffd7dc7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga35d5c4263e0dc0d0a4731ca6c562f32c" id="r_ga35d5c4263e0dc0d0a4731ca6c562f32c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#ga35d5c4263e0dc0d0a4731ca6c562f32c">GLFW_MOUSE_BUTTON_8</a>&#160;&#160;&#160;7</td></tr>
<tr class="separator:ga35d5c4263e0dc0d0a4731ca6c562f32c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab1fd86a4518a9141ec7bcde2e15a2fdf" id="r_gab1fd86a4518a9141ec7bcde2e15a2fdf"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#gab1fd86a4518a9141ec7bcde2e15a2fdf">GLFW_MOUSE_BUTTON_LAST</a>&#160;&#160;&#160;<a class="el" href="group__buttons.html#ga35d5c4263e0dc0d0a4731ca6c562f32c">GLFW_MOUSE_BUTTON_8</a></td></tr>
<tr class="separator:gab1fd86a4518a9141ec7bcde2e15a2fdf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf37100431dcd5082d48f95ee8bc8cd56" id="r_gaf37100431dcd5082d48f95ee8bc8cd56"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#gaf37100431dcd5082d48f95ee8bc8cd56">GLFW_MOUSE_BUTTON_LEFT</a>&#160;&#160;&#160;<a class="el" href="group__buttons.html#ga181a6e875251fd8671654eff00f9112e">GLFW_MOUSE_BUTTON_1</a></td></tr>
<tr class="separator:gaf37100431dcd5082d48f95ee8bc8cd56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3e2f2cf3c4942df73cc094247d275e74" id="r_ga3e2f2cf3c4942df73cc094247d275e74"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#ga3e2f2cf3c4942df73cc094247d275e74">GLFW_MOUSE_BUTTON_RIGHT</a>&#160;&#160;&#160;<a class="el" href="group__buttons.html#ga604b39b92c88ce9bd332e97fc3f4156c">GLFW_MOUSE_BUTTON_2</a></td></tr>
<tr class="separator:ga3e2f2cf3c4942df73cc094247d275e74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga34a4d2a701434f763fd93a2ff842b95a" id="r_ga34a4d2a701434f763fd93a2ff842b95a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__buttons.html#ga34a4d2a701434f763fd93a2ff842b95a">GLFW_MOUSE_BUTTON_MIDDLE</a>&#160;&#160;&#160;<a class="el" href="group__buttons.html#ga0130d505563d0236a6f85545f19e1721">GLFW_MOUSE_BUTTON_3</a></td></tr>
<tr class="separator:ga34a4d2a701434f763fd93a2ff842b95a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="ga181a6e875251fd8671654eff00f9112e" name="ga181a6e875251fd8671654eff00f9112e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga181a6e875251fd8671654eff00f9112e">&#9670;&#160;</a></span>GLFW_MOUSE_BUTTON_1</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOUSE_BUTTON_1&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga604b39b92c88ce9bd332e97fc3f4156c" name="ga604b39b92c88ce9bd332e97fc3f4156c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga604b39b92c88ce9bd332e97fc3f4156c">&#9670;&#160;</a></span>GLFW_MOUSE_BUTTON_2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOUSE_BUTTON_2&#160;&#160;&#160;1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga0130d505563d0236a6f85545f19e1721" name="ga0130d505563d0236a6f85545f19e1721"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga0130d505563d0236a6f85545f19e1721">&#9670;&#160;</a></span>GLFW_MOUSE_BUTTON_3</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOUSE_BUTTON_3&#160;&#160;&#160;2</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga53f4097bb01d5521c7d9513418c91ca9" name="ga53f4097bb01d5521c7d9513418c91ca9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga53f4097bb01d5521c7d9513418c91ca9">&#9670;&#160;</a></span>GLFW_MOUSE_BUTTON_4</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOUSE_BUTTON_4&#160;&#160;&#160;3</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaf08c4ddecb051d3d9667db1d5e417c9c" name="gaf08c4ddecb051d3d9667db1d5e417c9c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf08c4ddecb051d3d9667db1d5e417c9c">&#9670;&#160;</a></span>GLFW_MOUSE_BUTTON_5</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOUSE_BUTTON_5&#160;&#160;&#160;4</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gae8513e06aab8aa393b595f22c6d8257a" name="gae8513e06aab8aa393b595f22c6d8257a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae8513e06aab8aa393b595f22c6d8257a">&#9670;&#160;</a></span>GLFW_MOUSE_BUTTON_6</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOUSE_BUTTON_6&#160;&#160;&#160;5</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga8b02a1ab55dde45b3a3883d54ffd7dc7" name="ga8b02a1ab55dde45b3a3883d54ffd7dc7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8b02a1ab55dde45b3a3883d54ffd7dc7">&#9670;&#160;</a></span>GLFW_MOUSE_BUTTON_7</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOUSE_BUTTON_7&#160;&#160;&#160;6</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga35d5c4263e0dc0d0a4731ca6c562f32c" name="ga35d5c4263e0dc0d0a4731ca6c562f32c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga35d5c4263e0dc0d0a4731ca6c562f32c">&#9670;&#160;</a></span>GLFW_MOUSE_BUTTON_8</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOUSE_BUTTON_8&#160;&#160;&#160;7</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gab1fd86a4518a9141ec7bcde2e15a2fdf" name="gab1fd86a4518a9141ec7bcde2e15a2fdf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab1fd86a4518a9141ec7bcde2e15a2fdf">&#9670;&#160;</a></span>GLFW_MOUSE_BUTTON_LAST</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOUSE_BUTTON_LAST&#160;&#160;&#160;<a class="el" href="group__buttons.html#ga35d5c4263e0dc0d0a4731ca6c562f32c">GLFW_MOUSE_BUTTON_8</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gaf37100431dcd5082d48f95ee8bc8cd56" name="gaf37100431dcd5082d48f95ee8bc8cd56"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf37100431dcd5082d48f95ee8bc8cd56">&#9670;&#160;</a></span>GLFW_MOUSE_BUTTON_LEFT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOUSE_BUTTON_LEFT&#160;&#160;&#160;<a class="el" href="group__buttons.html#ga181a6e875251fd8671654eff00f9112e">GLFW_MOUSE_BUTTON_1</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga3e2f2cf3c4942df73cc094247d275e74" name="ga3e2f2cf3c4942df73cc094247d275e74"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3e2f2cf3c4942df73cc094247d275e74">&#9670;&#160;</a></span>GLFW_MOUSE_BUTTON_RIGHT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOUSE_BUTTON_RIGHT&#160;&#160;&#160;<a class="el" href="group__buttons.html#ga604b39b92c88ce9bd332e97fc3f4156c">GLFW_MOUSE_BUTTON_2</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga34a4d2a701434f763fd93a2ff842b95a" name="ga34a4d2a701434f763fd93a2ff842b95a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga34a4d2a701434f763fd93a2ff842b95a">&#9670;&#160;</a></span>GLFW_MOUSE_BUTTON_MIDDLE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_MOUSE_BUTTON_MIDDLE&#160;&#160;&#160;<a class="el" href="group__buttons.html#ga0130d505563d0236a6f85545f19e1721">GLFW_MOUSE_BUTTON_3</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
