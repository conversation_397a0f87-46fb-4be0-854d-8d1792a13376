Tutorial on Facial Landmark Detector API {#tutorial_table_of_content_facemark}
==========================================================

The facial landmark detector API is useful to detect facial landmarks from an input image.

-   @subpage tutorial_facemark_add_algorithm

    *Compatibility:* \> OpenCV 3.0

    *Author:* <PERSON><PERSON><PERSON>

    Adding a new algorithm in to the API.


-   @subpage tutorial_facemark_usage

    *Compatibility:* \> OpenCV 3.0

    *Author:* <PERSON><PERSON><PERSON> Kurnianggoro

    Tutorial on how to use the API.

-   @subpage tutorial_facemark_aam

    *Compatibility:* \> OpenCV 3.0

    *Author:* <PERSON><PERSON><PERSON> on how to use the FacemarkAAM algorithm.
