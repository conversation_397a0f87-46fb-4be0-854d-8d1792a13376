//
// AUTOGENERATED, DO NOT EDIT
//
// generated by parser_clblas.py
enum OPENCLAMDBLAS_FN_ID {
//    OPENCLAMDBLAS_FN_clblasCaxpy = 0,
//    OPENCLAMDBLAS_FN_clblasCcopy = 1,
//    OPENCLAMDBLAS_FN_clblasCdotc = 2,
//    OPENCLAMDBLAS_FN_clblasCdotu = 3,
//    OPENCLAMDBLAS_FN_clblasCgbmv = 4,
    OPENCLAMDBLAS_FN_clblasCgemm = 5,
//    OPENCLAMDBLAS_FN_clblasCgemv = 6,
//    OPENCLAMDBLAS_FN_clblasCgerc = 7,
//    OPENCLAMDBLAS_FN_clblasCgeru = 8,
//    OPENCLAMDBLAS_FN_clblasChbmv = 9,
//    OPENCLAMDBLAS_FN_clblasChemm = 10,
//    OPENCLAMDBLAS_FN_clblasChemv = 11,
//    OPENCLAMDBLAS_FN_clblasCher = 12,
//    OPENCLAMDBLAS_FN_clblasCher2 = 13,
//    OPENCLAMDBLAS_FN_clblasCher2k = 14,
//    OPENCLAMDBLAS_FN_clblasCherk = 15,
//    OPENCLAMDBLAS_FN_clblasChpmv = 16,
//    OPENCLAMDBLAS_FN_clblasChpr = 17,
//    OPENCLAMDBLAS_FN_clblasChpr2 = 18,
//    OPENCLAMDBLAS_FN_clblasCrotg = 19,
//    OPENCLAMDBLAS_FN_clblasCscal = 20,
//    OPENCLAMDBLAS_FN_clblasCsrot = 21,
//    OPENCLAMDBLAS_FN_clblasCsscal = 22,
//    OPENCLAMDBLAS_FN_clblasCswap = 23,
//    OPENCLAMDBLAS_FN_clblasCsymm = 24,
//    OPENCLAMDBLAS_FN_clblasCsyr2k = 25,
//    OPENCLAMDBLAS_FN_clblasCsyrk = 26,
//    OPENCLAMDBLAS_FN_clblasCtbmv = 27,
//    OPENCLAMDBLAS_FN_clblasCtbsv = 28,
//    OPENCLAMDBLAS_FN_clblasCtpmv = 29,
//    OPENCLAMDBLAS_FN_clblasCtpsv = 30,
//    OPENCLAMDBLAS_FN_clblasCtrmm = 31,
//    OPENCLAMDBLAS_FN_clblasCtrmv = 32,
//    OPENCLAMDBLAS_FN_clblasCtrsm = 33,
//    OPENCLAMDBLAS_FN_clblasCtrsv = 34,
//    OPENCLAMDBLAS_FN_clblasDasum = 35,
//    OPENCLAMDBLAS_FN_clblasDaxpy = 36,
//    OPENCLAMDBLAS_FN_clblasDcopy = 37,
//    OPENCLAMDBLAS_FN_clblasDdot = 38,
//    OPENCLAMDBLAS_FN_clblasDgbmv = 39,
    OPENCLAMDBLAS_FN_clblasDgemm = 40,
//    OPENCLAMDBLAS_FN_clblasDgemv = 41,
//    OPENCLAMDBLAS_FN_clblasDger = 42,
//    OPENCLAMDBLAS_FN_clblasDnrm2 = 43,
//    OPENCLAMDBLAS_FN_clblasDrot = 44,
//    OPENCLAMDBLAS_FN_clblasDrotg = 45,
//    OPENCLAMDBLAS_FN_clblasDrotm = 46,
//    OPENCLAMDBLAS_FN_clblasDrotmg = 47,
//    OPENCLAMDBLAS_FN_clblasDsbmv = 48,
//    OPENCLAMDBLAS_FN_clblasDscal = 49,
//    OPENCLAMDBLAS_FN_clblasDspmv = 50,
//    OPENCLAMDBLAS_FN_clblasDspr = 51,
//    OPENCLAMDBLAS_FN_clblasDspr2 = 52,
//    OPENCLAMDBLAS_FN_clblasDswap = 53,
//    OPENCLAMDBLAS_FN_clblasDsymm = 54,
//    OPENCLAMDBLAS_FN_clblasDsymv = 55,
//    OPENCLAMDBLAS_FN_clblasDsyr = 56,
//    OPENCLAMDBLAS_FN_clblasDsyr2 = 57,
//    OPENCLAMDBLAS_FN_clblasDsyr2k = 58,
//    OPENCLAMDBLAS_FN_clblasDsyrk = 59,
//    OPENCLAMDBLAS_FN_clblasDtbmv = 60,
//    OPENCLAMDBLAS_FN_clblasDtbsv = 61,
//    OPENCLAMDBLAS_FN_clblasDtpmv = 62,
//    OPENCLAMDBLAS_FN_clblasDtpsv = 63,
//    OPENCLAMDBLAS_FN_clblasDtrmm = 64,
//    OPENCLAMDBLAS_FN_clblasDtrmv = 65,
//    OPENCLAMDBLAS_FN_clblasDtrsm = 66,
//    OPENCLAMDBLAS_FN_clblasDtrsv = 67,
//    OPENCLAMDBLAS_FN_clblasDzasum = 68,
//    OPENCLAMDBLAS_FN_clblasDznrm2 = 69,
//    OPENCLAMDBLAS_FN_clblasGetVersion = 70,
//    OPENCLAMDBLAS_FN_clblasSasum = 71,
//    OPENCLAMDBLAS_FN_clblasSaxpy = 72,
//    OPENCLAMDBLAS_FN_clblasScasum = 73,
//    OPENCLAMDBLAS_FN_clblasScnrm2 = 74,
//    OPENCLAMDBLAS_FN_clblasScopy = 75,
//    OPENCLAMDBLAS_FN_clblasSdot = 76,
    OPENCLAMDBLAS_FN_clblasSetup = 77,
//    OPENCLAMDBLAS_FN_clblasSgbmv = 78,
    OPENCLAMDBLAS_FN_clblasSgemm = 79,
//    OPENCLAMDBLAS_FN_clblasSgemv = 80,
//    OPENCLAMDBLAS_FN_clblasSger = 81,
//    OPENCLAMDBLAS_FN_clblasSnrm2 = 82,
//    OPENCLAMDBLAS_FN_clblasSrot = 83,
//    OPENCLAMDBLAS_FN_clblasSrotg = 84,
//    OPENCLAMDBLAS_FN_clblasSrotm = 85,
//    OPENCLAMDBLAS_FN_clblasSrotmg = 86,
//    OPENCLAMDBLAS_FN_clblasSsbmv = 87,
//    OPENCLAMDBLAS_FN_clblasSscal = 88,
//    OPENCLAMDBLAS_FN_clblasSspmv = 89,
//    OPENCLAMDBLAS_FN_clblasSspr = 90,
//    OPENCLAMDBLAS_FN_clblasSspr2 = 91,
//    OPENCLAMDBLAS_FN_clblasSswap = 92,
//    OPENCLAMDBLAS_FN_clblasSsymm = 93,
//    OPENCLAMDBLAS_FN_clblasSsymv = 94,
//    OPENCLAMDBLAS_FN_clblasSsyr = 95,
//    OPENCLAMDBLAS_FN_clblasSsyr2 = 96,
//    OPENCLAMDBLAS_FN_clblasSsyr2k = 97,
//    OPENCLAMDBLAS_FN_clblasSsyrk = 98,
//    OPENCLAMDBLAS_FN_clblasStbmv = 99,
//    OPENCLAMDBLAS_FN_clblasStbsv = 100,
//    OPENCLAMDBLAS_FN_clblasStpmv = 101,
//    OPENCLAMDBLAS_FN_clblasStpsv = 102,
//    OPENCLAMDBLAS_FN_clblasStrmm = 103,
//    OPENCLAMDBLAS_FN_clblasStrmv = 104,
//    OPENCLAMDBLAS_FN_clblasStrsm = 105,
//    OPENCLAMDBLAS_FN_clblasStrsv = 106,
    OPENCLAMDBLAS_FN_clblasTeardown = 107,
//    OPENCLAMDBLAS_FN_clblasZaxpy = 108,
//    OPENCLAMDBLAS_FN_clblasZcopy = 109,
//    OPENCLAMDBLAS_FN_clblasZdotc = 110,
//    OPENCLAMDBLAS_FN_clblasZdotu = 111,
//    OPENCLAMDBLAS_FN_clblasZdrot = 112,
//    OPENCLAMDBLAS_FN_clblasZdscal = 113,
//    OPENCLAMDBLAS_FN_clblasZgbmv = 114,
    OPENCLAMDBLAS_FN_clblasZgemm = 115,
//    OPENCLAMDBLAS_FN_clblasZgemv = 116,
//    OPENCLAMDBLAS_FN_clblasZgerc = 117,
//    OPENCLAMDBLAS_FN_clblasZgeru = 118,
//    OPENCLAMDBLAS_FN_clblasZhbmv = 119,
//    OPENCLAMDBLAS_FN_clblasZhemm = 120,
//    OPENCLAMDBLAS_FN_clblasZhemv = 121,
//    OPENCLAMDBLAS_FN_clblasZher = 122,
//    OPENCLAMDBLAS_FN_clblasZher2 = 123,
//    OPENCLAMDBLAS_FN_clblasZher2k = 124,
//    OPENCLAMDBLAS_FN_clblasZherk = 125,
//    OPENCLAMDBLAS_FN_clblasZhpmv = 126,
//    OPENCLAMDBLAS_FN_clblasZhpr = 127,
//    OPENCLAMDBLAS_FN_clblasZhpr2 = 128,
//    OPENCLAMDBLAS_FN_clblasZrotg = 129,
//    OPENCLAMDBLAS_FN_clblasZscal = 130,
//    OPENCLAMDBLAS_FN_clblasZswap = 131,
//    OPENCLAMDBLAS_FN_clblasZsymm = 132,
//    OPENCLAMDBLAS_FN_clblasZsyr2k = 133,
//    OPENCLAMDBLAS_FN_clblasZsyrk = 134,
//    OPENCLAMDBLAS_FN_clblasZtbmv = 135,
//    OPENCLAMDBLAS_FN_clblasZtbsv = 136,
//    OPENCLAMDBLAS_FN_clblasZtpmv = 137,
//    OPENCLAMDBLAS_FN_clblasZtpsv = 138,
//    OPENCLAMDBLAS_FN_clblasZtrmm = 139,
//    OPENCLAMDBLAS_FN_clblasZtrmv = 140,
//    OPENCLAMDBLAS_FN_clblasZtrsm = 141,
//    OPENCLAMDBLAS_FN_clblasZtrsv = 142,
//    OPENCLAMDBLAS_FN_clblasiCamax = 143,
//    OPENCLAMDBLAS_FN_clblasiDamax = 144,
//    OPENCLAMDBLAS_FN_clblasiSamax = 145,
//    OPENCLAMDBLAS_FN_clblasiZamax = 146,
};

namespace {
// generated by parser_clblas.py
#define openclamdblas_fn0(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(); } \

#define openclamdblas_fn1(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1); } \

#define openclamdblas_fn2(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2); } \

#define openclamdblas_fn3(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3); } \

#define openclamdblas_fn4(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4); } \

#define openclamdblas_fn5(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5); } \

#define openclamdblas_fn6(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6); } \

#define openclamdblas_fn7(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7); } \

#define openclamdblas_fn8(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8); } \

#define openclamdblas_fn9(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9); } \

#define openclamdblas_fn10(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10); } \

#define openclamdblas_fn11(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11); } \

#define openclamdblas_fn12(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12); } \

#define openclamdblas_fn13(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12, p13); } \

#define openclamdblas_fn14(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12, p13, p14); } \

#define openclamdblas_fn15(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12, p13, p14, p15); } \

#define openclamdblas_fn16(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12, p13, p14, p15, p16); } \

#define openclamdblas_fn17(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12, p13, p14, p15, p16, p17); } \

#define openclamdblas_fn18(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12, p13, p14, p15, p16, p17, p18); } \

#define openclamdblas_fn19(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12, p13, p14, p15, p16, p17, p18, p19); } \

#define openclamdblas_fn20(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12, p13, p14, p15, p16, p17, p18, p19, p20); } \

#define openclamdblas_fn21(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12, p13, p14, p15, p16, p17, p18, p19, p20, p21); } \

#define openclamdblas_fn22(ID, _R, decl_args) \
    typedef _R (*ID##FN)decl_args; \
    static _R ID##_switch_fn decl_args \
    { return ((ID##FN)openclamdblas_check_fn(ID))(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12, p13, p14, p15, p16, p17, p18, p19, p20, p21, p22); } \

}

// generated by parser_clblas.py
//openclamdblas_fn13(OPENCLAMDBLAS_FN_clblasCaxpy, clblasStatus, (size_t p1, cl_float2 p2, const cl_mem p3, size_t p4, int p5, cl_mem p6, size_t p7, int p8, cl_uint p9, cl_command_queue* p10, cl_uint p11, const cl_event* p12, cl_event* p13))
//clblasStatus (*clblasCaxpy)(size_t, cl_float2, const cl_mem, size_t, int, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCaxpy_switch_fn;
//static const struct DynamicFnEntry clblasCaxpy_definition = { "clblasCaxpy", (void**)&clblasCaxpy};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasCcopy, clblasStatus, (size_t p1, const cl_mem p2, size_t p3, int p4, cl_mem p5, size_t p6, int p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasCcopy)(size_t, const cl_mem, size_t, int, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCcopy_switch_fn;
//static const struct DynamicFnEntry clblasCcopy_definition = { "clblasCcopy", (void**)&clblasCcopy};

//openclamdblas_fn15(OPENCLAMDBLAS_FN_clblasCdotc, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, const cl_mem p7, size_t p8, int p9, cl_mem p10, cl_uint p11, cl_command_queue* p12, cl_uint p13, const cl_event* p14, cl_event* p15))
//clblasStatus (*clblasCdotc)(size_t, cl_mem, size_t, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCdotc_switch_fn;
//static const struct DynamicFnEntry clblasCdotc_definition = { "clblasCdotc", (void**)&clblasCdotc};

//openclamdblas_fn15(OPENCLAMDBLAS_FN_clblasCdotu, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, const cl_mem p7, size_t p8, int p9, cl_mem p10, cl_uint p11, cl_command_queue* p12, cl_uint p13, const cl_event* p14, cl_event* p15))
//clblasStatus (*clblasCdotu)(size_t, cl_mem, size_t, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCdotu_switch_fn;
//static const struct DynamicFnEntry clblasCdotu_definition = { "clblasCdotu", (void**)&clblasCdotu};

//openclamdblas_fn22(OPENCLAMDBLAS_FN_clblasCgbmv, clblasStatus, (clblasOrder p1, clblasTranspose p2, size_t p3, size_t p4, size_t p5, size_t p6, cl_float2 p7, const cl_mem p8, size_t p9, size_t p10, const cl_mem p11, size_t p12, int p13, cl_float2 p14, cl_mem p15, size_t p16, int p17, cl_uint p18, cl_command_queue* p19, cl_uint p20, const cl_event* p21, cl_event* p22))
//clblasStatus (*clblasCgbmv)(clblasOrder, clblasTranspose, size_t, size_t, size_t, size_t, cl_float2, const cl_mem, size_t, size_t, const cl_mem, size_t, int, cl_float2, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCgbmv_switch_fn;
//static const struct DynamicFnEntry clblasCgbmv_definition = { "clblasCgbmv", (void**)&clblasCgbmv};

openclamdblas_fn22(OPENCLAMDBLAS_FN_clblasCgemm, clblasStatus, (clblasOrder p1, clblasTranspose p2, clblasTranspose p3, size_t p4, size_t p5, size_t p6, FloatComplex p7, const cl_mem p8, size_t p9, size_t p10, const cl_mem p11, size_t p12, size_t p13, FloatComplex p14, cl_mem p15, size_t p16, size_t p17, cl_uint p18, cl_command_queue* p19, cl_uint p20, const cl_event* p21, cl_event* p22))
clblasStatus (*clblasCgemm)(clblasOrder, clblasTranspose, clblasTranspose, size_t, size_t, size_t, FloatComplex, const cl_mem, size_t, size_t, const cl_mem, size_t, size_t, FloatComplex, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
        OPENCLAMDBLAS_FN_clblasCgemm_switch_fn;
static const struct DynamicFnEntry clblasCgemm_definition = { "clblasCgemm", (void**)&clblasCgemm};

//openclamdblas_fn20(OPENCLAMDBLAS_FN_clblasCgemv, clblasStatus, (clblasOrder p1, clblasTranspose p2, size_t p3, size_t p4, FloatComplex p5, const cl_mem p6, size_t p7, size_t p8, const cl_mem p9, size_t p10, int p11, FloatComplex p12, cl_mem p13, size_t p14, int p15, cl_uint p16, cl_command_queue* p17, cl_uint p18, const cl_event* p19, cl_event* p20))
//clblasStatus (*clblasCgemv)(clblasOrder, clblasTranspose, size_t, size_t, FloatComplex, const cl_mem, size_t, size_t, const cl_mem, size_t, int, FloatComplex, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCgemv_switch_fn;
//static const struct DynamicFnEntry clblasCgemv_definition = { "clblasCgemv", (void**)&clblasCgemv};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasCgerc, clblasStatus, (clblasOrder p1, size_t p2, size_t p3, cl_float2 p4, const cl_mem p5, size_t p6, int p7, const cl_mem p8, size_t p9, int p10, cl_mem p11, size_t p12, size_t p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasCgerc)(clblasOrder, size_t, size_t, cl_float2, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCgerc_switch_fn;
//static const struct DynamicFnEntry clblasCgerc_definition = { "clblasCgerc", (void**)&clblasCgerc};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasCgeru, clblasStatus, (clblasOrder p1, size_t p2, size_t p3, cl_float2 p4, const cl_mem p5, size_t p6, int p7, const cl_mem p8, size_t p9, int p10, cl_mem p11, size_t p12, size_t p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasCgeru)(clblasOrder, size_t, size_t, cl_float2, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCgeru_switch_fn;
//static const struct DynamicFnEntry clblasCgeru_definition = { "clblasCgeru", (void**)&clblasCgeru};

//openclamdblas_fn20(OPENCLAMDBLAS_FN_clblasChbmv, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, size_t p4, cl_float2 p5, const cl_mem p6, size_t p7, size_t p8, const cl_mem p9, size_t p10, int p11, cl_float2 p12, cl_mem p13, size_t p14, int p15, cl_uint p16, cl_command_queue* p17, cl_uint p18, const cl_event* p19, cl_event* p20))
//clblasStatus (*clblasChbmv)(clblasOrder, clblasUplo, size_t, size_t, cl_float2, const cl_mem, size_t, size_t, const cl_mem, size_t, int, cl_float2, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasChbmv_switch_fn;
//static const struct DynamicFnEntry clblasChbmv_definition = { "clblasChbmv", (void**)&clblasChbmv};

//openclamdblas_fn21(OPENCLAMDBLAS_FN_clblasChemm, clblasStatus, (clblasOrder p1, clblasSide p2, clblasUplo p3, size_t p4, size_t p5, cl_float2 p6, const cl_mem p7, size_t p8, size_t p9, const cl_mem p10, size_t p11, size_t p12, cl_float2 p13, cl_mem p14, size_t p15, size_t p16, cl_uint p17, cl_command_queue* p18, cl_uint p19, const cl_event* p20, cl_event* p21))
//clblasStatus (*clblasChemm)(clblasOrder, clblasSide, clblasUplo, size_t, size_t, cl_float2, const cl_mem, size_t, size_t, const cl_mem, size_t, size_t, cl_float2, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasChemm_switch_fn;
//static const struct DynamicFnEntry clblasChemm_definition = { "clblasChemm", (void**)&clblasChemm};

//openclamdblas_fn19(OPENCLAMDBLAS_FN_clblasChemv, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, FloatComplex p4, const cl_mem p5, size_t p6, size_t p7, const cl_mem p8, size_t p9, int p10, FloatComplex p11, cl_mem p12, size_t p13, int p14, cl_uint p15, cl_command_queue* p16, cl_uint p17, const cl_event* p18, cl_event* p19))
//clblasStatus (*clblasChemv)(clblasOrder, clblasUplo, size_t, FloatComplex, const cl_mem, size_t, size_t, const cl_mem, size_t, int, FloatComplex, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasChemv_switch_fn;
//static const struct DynamicFnEntry clblasChemv_definition = { "clblasChemv", (void**)&clblasChemv};

//openclamdblas_fn15(OPENCLAMDBLAS_FN_clblasCher, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_float p4, const cl_mem p5, size_t p6, int p7, cl_mem p8, size_t p9, size_t p10, cl_uint p11, cl_command_queue* p12, cl_uint p13, const cl_event* p14, cl_event* p15))
//clblasStatus (*clblasCher)(clblasOrder, clblasUplo, size_t, cl_float, const cl_mem, size_t, int, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCher_switch_fn;
//static const struct DynamicFnEntry clblasCher_definition = { "clblasCher", (void**)&clblasCher};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasCher2, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_float2 p4, const cl_mem p5, size_t p6, int p7, const cl_mem p8, size_t p9, int p10, cl_mem p11, size_t p12, size_t p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasCher2)(clblasOrder, clblasUplo, size_t, cl_float2, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCher2_switch_fn;
//static const struct DynamicFnEntry clblasCher2_definition = { "clblasCher2", (void**)&clblasCher2};

//openclamdblas_fn21(OPENCLAMDBLAS_FN_clblasCher2k, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, size_t p4, size_t p5, FloatComplex p6, const cl_mem p7, size_t p8, size_t p9, const cl_mem p10, size_t p11, size_t p12, cl_float p13, cl_mem p14, size_t p15, size_t p16, cl_uint p17, cl_command_queue* p18, cl_uint p19, const cl_event* p20, cl_event* p21))
//clblasStatus (*clblasCher2k)(clblasOrder, clblasUplo, clblasTranspose, size_t, size_t, FloatComplex, const cl_mem, size_t, size_t, const cl_mem, size_t, size_t, cl_float, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCher2k_switch_fn;
//static const struct DynamicFnEntry clblasCher2k_definition = { "clblasCher2k", (void**)&clblasCher2k};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasCherk, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, size_t p4, size_t p5, float p6, const cl_mem p7, size_t p8, size_t p9, float p10, cl_mem p11, size_t p12, size_t p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasCherk)(clblasOrder, clblasUplo, clblasTranspose, size_t, size_t, float, const cl_mem, size_t, size_t, float, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCherk_switch_fn;
//static const struct DynamicFnEntry clblasCherk_definition = { "clblasCherk", (void**)&clblasCherk};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasChpmv, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_float2 p4, const cl_mem p5, size_t p6, const cl_mem p7, size_t p8, int p9, cl_float2 p10, cl_mem p11, size_t p12, int p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasChpmv)(clblasOrder, clblasUplo, size_t, cl_float2, const cl_mem, size_t, const cl_mem, size_t, int, cl_float2, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasChpmv_switch_fn;
//static const struct DynamicFnEntry clblasChpmv_definition = { "clblasChpmv", (void**)&clblasChpmv};

//openclamdblas_fn14(OPENCLAMDBLAS_FN_clblasChpr, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_float p4, const cl_mem p5, size_t p6, int p7, cl_mem p8, size_t p9, cl_uint p10, cl_command_queue* p11, cl_uint p12, const cl_event* p13, cl_event* p14))
//clblasStatus (*clblasChpr)(clblasOrder, clblasUplo, size_t, cl_float, const cl_mem, size_t, int, cl_mem, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasChpr_switch_fn;
//static const struct DynamicFnEntry clblasChpr_definition = { "clblasChpr", (void**)&clblasChpr};

//openclamdblas_fn17(OPENCLAMDBLAS_FN_clblasChpr2, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_float2 p4, const cl_mem p5, size_t p6, int p7, const cl_mem p8, size_t p9, int p10, cl_mem p11, size_t p12, cl_uint p13, cl_command_queue* p14, cl_uint p15, const cl_event* p16, cl_event* p17))
//clblasStatus (*clblasChpr2)(clblasOrder, clblasUplo, size_t, cl_float2, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasChpr2_switch_fn;
//static const struct DynamicFnEntry clblasChpr2_definition = { "clblasChpr2", (void**)&clblasChpr2};

//openclamdblas_fn13(OPENCLAMDBLAS_FN_clblasCrotg, clblasStatus, (cl_mem p1, size_t p2, cl_mem p3, size_t p4, cl_mem p5, size_t p6, cl_mem p7, size_t p8, cl_uint p9, cl_command_queue* p10, cl_uint p11, const cl_event* p12, cl_event* p13))
//clblasStatus (*clblasCrotg)(cl_mem, size_t, cl_mem, size_t, cl_mem, size_t, cl_mem, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCrotg_switch_fn;
//static const struct DynamicFnEntry clblasCrotg_definition = { "clblasCrotg", (void**)&clblasCrotg};

//openclamdblas_fn10(OPENCLAMDBLAS_FN_clblasCscal, clblasStatus, (size_t p1, cl_float2 p2, cl_mem p3, size_t p4, int p5, cl_uint p6, cl_command_queue* p7, cl_uint p8, const cl_event* p9, cl_event* p10))
//clblasStatus (*clblasCscal)(size_t, cl_float2, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCscal_switch_fn;
//static const struct DynamicFnEntry clblasCscal_definition = { "clblasCscal", (void**)&clblasCscal};

//openclamdblas_fn14(OPENCLAMDBLAS_FN_clblasCsrot, clblasStatus, (size_t p1, cl_mem p2, size_t p3, int p4, cl_mem p5, size_t p6, int p7, cl_float p8, cl_float p9, cl_uint p10, cl_command_queue* p11, cl_uint p12, const cl_event* p13, cl_event* p14))
//clblasStatus (*clblasCsrot)(size_t, cl_mem, size_t, int, cl_mem, size_t, int, cl_float, cl_float, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCsrot_switch_fn;
//static const struct DynamicFnEntry clblasCsrot_definition = { "clblasCsrot", (void**)&clblasCsrot};

//openclamdblas_fn10(OPENCLAMDBLAS_FN_clblasCsscal, clblasStatus, (size_t p1, cl_float p2, cl_mem p3, size_t p4, int p5, cl_uint p6, cl_command_queue* p7, cl_uint p8, const cl_event* p9, cl_event* p10))
//clblasStatus (*clblasCsscal)(size_t, cl_float, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCsscal_switch_fn;
//static const struct DynamicFnEntry clblasCsscal_definition = { "clblasCsscal", (void**)&clblasCsscal};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasCswap, clblasStatus, (size_t p1, cl_mem p2, size_t p3, int p4, cl_mem p5, size_t p6, int p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasCswap)(size_t, cl_mem, size_t, int, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCswap_switch_fn;
//static const struct DynamicFnEntry clblasCswap_definition = { "clblasCswap", (void**)&clblasCswap};

//openclamdblas_fn21(OPENCLAMDBLAS_FN_clblasCsymm, clblasStatus, (clblasOrder p1, clblasSide p2, clblasUplo p3, size_t p4, size_t p5, cl_float2 p6, const cl_mem p7, size_t p8, size_t p9, const cl_mem p10, size_t p11, size_t p12, cl_float2 p13, cl_mem p14, size_t p15, size_t p16, cl_uint p17, cl_command_queue* p18, cl_uint p19, const cl_event* p20, cl_event* p21))
//clblasStatus (*clblasCsymm)(clblasOrder, clblasSide, clblasUplo, size_t, size_t, cl_float2, const cl_mem, size_t, size_t, const cl_mem, size_t, size_t, cl_float2, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCsymm_switch_fn;
//static const struct DynamicFnEntry clblasCsymm_definition = { "clblasCsymm", (void**)&clblasCsymm};

//openclamdblas_fn21(OPENCLAMDBLAS_FN_clblasCsyr2k, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, size_t p4, size_t p5, FloatComplex p6, const cl_mem p7, size_t p8, size_t p9, const cl_mem p10, size_t p11, size_t p12, FloatComplex p13, cl_mem p14, size_t p15, size_t p16, cl_uint p17, cl_command_queue* p18, cl_uint p19, const cl_event* p20, cl_event* p21))
//clblasStatus (*clblasCsyr2k)(clblasOrder, clblasUplo, clblasTranspose, size_t, size_t, FloatComplex, const cl_mem, size_t, size_t, const cl_mem, size_t, size_t, FloatComplex, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCsyr2k_switch_fn;
//static const struct DynamicFnEntry clblasCsyr2k_definition = { "clblasCsyr2k", (void**)&clblasCsyr2k};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasCsyrk, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, size_t p4, size_t p5, FloatComplex p6, const cl_mem p7, size_t p8, size_t p9, FloatComplex p10, cl_mem p11, size_t p12, size_t p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasCsyrk)(clblasOrder, clblasUplo, clblasTranspose, size_t, size_t, FloatComplex, const cl_mem, size_t, size_t, FloatComplex, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCsyrk_switch_fn;
//static const struct DynamicFnEntry clblasCsyrk_definition = { "clblasCsyrk", (void**)&clblasCsyrk};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasCtbmv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, size_t p6, const cl_mem p7, size_t p8, size_t p9, cl_mem p10, size_t p11, int p12, cl_mem p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasCtbmv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, size_t, const cl_mem, size_t, size_t, cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCtbmv_switch_fn;
//static const struct DynamicFnEntry clblasCtbmv_definition = { "clblasCtbmv", (void**)&clblasCtbmv};

//openclamdblas_fn17(OPENCLAMDBLAS_FN_clblasCtbsv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, size_t p6, const cl_mem p7, size_t p8, size_t p9, cl_mem p10, size_t p11, int p12, cl_uint p13, cl_command_queue* p14, cl_uint p15, const cl_event* p16, cl_event* p17))
//clblasStatus (*clblasCtbsv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, size_t, const cl_mem, size_t, size_t, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCtbsv_switch_fn;
//static const struct DynamicFnEntry clblasCtbsv_definition = { "clblasCtbsv", (void**)&clblasCtbsv};

//openclamdblas_fn16(OPENCLAMDBLAS_FN_clblasCtpmv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, const cl_mem p6, size_t p7, cl_mem p8, size_t p9, int p10, cl_mem p11, cl_uint p12, cl_command_queue* p13, cl_uint p14, const cl_event* p15, cl_event* p16))
//clblasStatus (*clblasCtpmv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, const cl_mem, size_t, cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCtpmv_switch_fn;
//static const struct DynamicFnEntry clblasCtpmv_definition = { "clblasCtpmv", (void**)&clblasCtpmv};

//openclamdblas_fn15(OPENCLAMDBLAS_FN_clblasCtpsv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, const cl_mem p6, size_t p7, cl_mem p8, size_t p9, int p10, cl_uint p11, cl_command_queue* p12, cl_uint p13, const cl_event* p14, cl_event* p15))
//clblasStatus (*clblasCtpsv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, const cl_mem, size_t, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCtpsv_switch_fn;
//static const struct DynamicFnEntry clblasCtpsv_definition = { "clblasCtpsv", (void**)&clblasCtpsv};

//openclamdblas_fn19(OPENCLAMDBLAS_FN_clblasCtrmm, clblasStatus, (clblasOrder p1, clblasSide p2, clblasUplo p3, clblasTranspose p4, clblasDiag p5, size_t p6, size_t p7, FloatComplex p8, const cl_mem p9, size_t p10, size_t p11, cl_mem p12, size_t p13, size_t p14, cl_uint p15, cl_command_queue* p16, cl_uint p17, const cl_event* p18, cl_event* p19))
//clblasStatus (*clblasCtrmm)(clblasOrder, clblasSide, clblasUplo, clblasTranspose, clblasDiag, size_t, size_t, FloatComplex, const cl_mem, size_t, size_t, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCtrmm_switch_fn;
//static const struct DynamicFnEntry clblasCtrmm_definition = { "clblasCtrmm", (void**)&clblasCtrmm};

//openclamdblas_fn17(OPENCLAMDBLAS_FN_clblasCtrmv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, const cl_mem p6, size_t p7, size_t p8, cl_mem p9, size_t p10, int p11, cl_mem p12, cl_uint p13, cl_command_queue* p14, cl_uint p15, const cl_event* p16, cl_event* p17))
//clblasStatus (*clblasCtrmv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, const cl_mem, size_t, size_t, cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCtrmv_switch_fn;
//static const struct DynamicFnEntry clblasCtrmv_definition = { "clblasCtrmv", (void**)&clblasCtrmv};

//openclamdblas_fn19(OPENCLAMDBLAS_FN_clblasCtrsm, clblasStatus, (clblasOrder p1, clblasSide p2, clblasUplo p3, clblasTranspose p4, clblasDiag p5, size_t p6, size_t p7, FloatComplex p8, const cl_mem p9, size_t p10, size_t p11, cl_mem p12, size_t p13, size_t p14, cl_uint p15, cl_command_queue* p16, cl_uint p17, const cl_event* p18, cl_event* p19))
//clblasStatus (*clblasCtrsm)(clblasOrder, clblasSide, clblasUplo, clblasTranspose, clblasDiag, size_t, size_t, FloatComplex, const cl_mem, size_t, size_t, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCtrsm_switch_fn;
//static const struct DynamicFnEntry clblasCtrsm_definition = { "clblasCtrsm", (void**)&clblasCtrsm};

//openclamdblas_fn16(OPENCLAMDBLAS_FN_clblasCtrsv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, const cl_mem p6, size_t p7, size_t p8, cl_mem p9, size_t p10, int p11, cl_uint p12, cl_command_queue* p13, cl_uint p14, const cl_event* p15, cl_event* p16))
//clblasStatus (*clblasCtrsv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, const cl_mem, size_t, size_t, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasCtrsv_switch_fn;
//static const struct DynamicFnEntry clblasCtrsv_definition = { "clblasCtrsv", (void**)&clblasCtrsv};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasDasum, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, cl_mem p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasDasum)(size_t, cl_mem, size_t, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDasum_switch_fn;
//static const struct DynamicFnEntry clblasDasum_definition = { "clblasDasum", (void**)&clblasDasum};

//openclamdblas_fn13(OPENCLAMDBLAS_FN_clblasDaxpy, clblasStatus, (size_t p1, cl_double p2, const cl_mem p3, size_t p4, int p5, cl_mem p6, size_t p7, int p8, cl_uint p9, cl_command_queue* p10, cl_uint p11, const cl_event* p12, cl_event* p13))
//clblasStatus (*clblasDaxpy)(size_t, cl_double, const cl_mem, size_t, int, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDaxpy_switch_fn;
//static const struct DynamicFnEntry clblasDaxpy_definition = { "clblasDaxpy", (void**)&clblasDaxpy};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasDcopy, clblasStatus, (size_t p1, const cl_mem p2, size_t p3, int p4, cl_mem p5, size_t p6, int p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasDcopy)(size_t, const cl_mem, size_t, int, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDcopy_switch_fn;
//static const struct DynamicFnEntry clblasDcopy_definition = { "clblasDcopy", (void**)&clblasDcopy};

//openclamdblas_fn15(OPENCLAMDBLAS_FN_clblasDdot, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, const cl_mem p7, size_t p8, int p9, cl_mem p10, cl_uint p11, cl_command_queue* p12, cl_uint p13, const cl_event* p14, cl_event* p15))
//clblasStatus (*clblasDdot)(size_t, cl_mem, size_t, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDdot_switch_fn;
//static const struct DynamicFnEntry clblasDdot_definition = { "clblasDdot", (void**)&clblasDdot};

//openclamdblas_fn22(OPENCLAMDBLAS_FN_clblasDgbmv, clblasStatus, (clblasOrder p1, clblasTranspose p2, size_t p3, size_t p4, size_t p5, size_t p6, cl_double p7, const cl_mem p8, size_t p9, size_t p10, const cl_mem p11, size_t p12, int p13, cl_double p14, cl_mem p15, size_t p16, int p17, cl_uint p18, cl_command_queue* p19, cl_uint p20, const cl_event* p21, cl_event* p22))
//clblasStatus (*clblasDgbmv)(clblasOrder, clblasTranspose, size_t, size_t, size_t, size_t, cl_double, const cl_mem, size_t, size_t, const cl_mem, size_t, int, cl_double, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDgbmv_switch_fn;
//static const struct DynamicFnEntry clblasDgbmv_definition = { "clblasDgbmv", (void**)&clblasDgbmv};

openclamdblas_fn22(OPENCLAMDBLAS_FN_clblasDgemm, clblasStatus, (clblasOrder p1, clblasTranspose p2, clblasTranspose p3, size_t p4, size_t p5, size_t p6, cl_double p7, const cl_mem p8, size_t p9, size_t p10, const cl_mem p11, size_t p12, size_t p13, cl_double p14, cl_mem p15, size_t p16, size_t p17, cl_uint p18, cl_command_queue* p19, cl_uint p20, const cl_event* p21, cl_event* p22))
clblasStatus (*clblasDgemm)(clblasOrder, clblasTranspose, clblasTranspose, size_t, size_t, size_t, cl_double, const cl_mem, size_t, size_t, const cl_mem, size_t, size_t, cl_double, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
        OPENCLAMDBLAS_FN_clblasDgemm_switch_fn;
static const struct DynamicFnEntry clblasDgemm_definition = { "clblasDgemm", (void**)&clblasDgemm};

//openclamdblas_fn20(OPENCLAMDBLAS_FN_clblasDgemv, clblasStatus, (clblasOrder p1, clblasTranspose p2, size_t p3, size_t p4, cl_double p5, const cl_mem p6, size_t p7, size_t p8, const cl_mem p9, size_t p10, int p11, cl_double p12, cl_mem p13, size_t p14, int p15, cl_uint p16, cl_command_queue* p17, cl_uint p18, const cl_event* p19, cl_event* p20))
//clblasStatus (*clblasDgemv)(clblasOrder, clblasTranspose, size_t, size_t, cl_double, const cl_mem, size_t, size_t, const cl_mem, size_t, int, cl_double, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDgemv_switch_fn;
//static const struct DynamicFnEntry clblasDgemv_definition = { "clblasDgemv", (void**)&clblasDgemv};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasDger, clblasStatus, (clblasOrder p1, size_t p2, size_t p3, cl_double p4, const cl_mem p5, size_t p6, int p7, const cl_mem p8, size_t p9, int p10, cl_mem p11, size_t p12, size_t p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasDger)(clblasOrder, size_t, size_t, cl_double, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDger_switch_fn;
//static const struct DynamicFnEntry clblasDger_definition = { "clblasDger", (void**)&clblasDger};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasDnrm2, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, cl_mem p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasDnrm2)(size_t, cl_mem, size_t, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDnrm2_switch_fn;
//static const struct DynamicFnEntry clblasDnrm2_definition = { "clblasDnrm2", (void**)&clblasDnrm2};

//openclamdblas_fn14(OPENCLAMDBLAS_FN_clblasDrot, clblasStatus, (size_t p1, cl_mem p2, size_t p3, int p4, cl_mem p5, size_t p6, int p7, cl_double p8, cl_double p9, cl_uint p10, cl_command_queue* p11, cl_uint p12, const cl_event* p13, cl_event* p14))
//clblasStatus (*clblasDrot)(size_t, cl_mem, size_t, int, cl_mem, size_t, int, cl_double, cl_double, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDrot_switch_fn;
//static const struct DynamicFnEntry clblasDrot_definition = { "clblasDrot", (void**)&clblasDrot};

//openclamdblas_fn13(OPENCLAMDBLAS_FN_clblasDrotg, clblasStatus, (cl_mem p1, size_t p2, cl_mem p3, size_t p4, cl_mem p5, size_t p6, cl_mem p7, size_t p8, cl_uint p9, cl_command_queue* p10, cl_uint p11, const cl_event* p12, cl_event* p13))
//clblasStatus (*clblasDrotg)(cl_mem, size_t, cl_mem, size_t, cl_mem, size_t, cl_mem, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDrotg_switch_fn;
//static const struct DynamicFnEntry clblasDrotg_definition = { "clblasDrotg", (void**)&clblasDrotg};

//openclamdblas_fn14(OPENCLAMDBLAS_FN_clblasDrotm, clblasStatus, (size_t p1, cl_mem p2, size_t p3, int p4, cl_mem p5, size_t p6, int p7, const cl_mem p8, size_t p9, cl_uint p10, cl_command_queue* p11, cl_uint p12, const cl_event* p13, cl_event* p14))
//clblasStatus (*clblasDrotm)(size_t, cl_mem, size_t, int, cl_mem, size_t, int, const cl_mem, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDrotm_switch_fn;
//static const struct DynamicFnEntry clblasDrotm_definition = { "clblasDrotm", (void**)&clblasDrotm};

//openclamdblas_fn15(OPENCLAMDBLAS_FN_clblasDrotmg, clblasStatus, (cl_mem p1, size_t p2, cl_mem p3, size_t p4, cl_mem p5, size_t p6, const cl_mem p7, size_t p8, cl_mem p9, size_t p10, cl_uint p11, cl_command_queue* p12, cl_uint p13, const cl_event* p14, cl_event* p15))
//clblasStatus (*clblasDrotmg)(cl_mem, size_t, cl_mem, size_t, cl_mem, size_t, const cl_mem, size_t, cl_mem, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDrotmg_switch_fn;
//static const struct DynamicFnEntry clblasDrotmg_definition = { "clblasDrotmg", (void**)&clblasDrotmg};

//openclamdblas_fn20(OPENCLAMDBLAS_FN_clblasDsbmv, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, size_t p4, cl_double p5, const cl_mem p6, size_t p7, size_t p8, const cl_mem p9, size_t p10, int p11, cl_double p12, cl_mem p13, size_t p14, int p15, cl_uint p16, cl_command_queue* p17, cl_uint p18, const cl_event* p19, cl_event* p20))
//clblasStatus (*clblasDsbmv)(clblasOrder, clblasUplo, size_t, size_t, cl_double, const cl_mem, size_t, size_t, const cl_mem, size_t, int, cl_double, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDsbmv_switch_fn;
//static const struct DynamicFnEntry clblasDsbmv_definition = { "clblasDsbmv", (void**)&clblasDsbmv};

//openclamdblas_fn10(OPENCLAMDBLAS_FN_clblasDscal, clblasStatus, (size_t p1, cl_double p2, cl_mem p3, size_t p4, int p5, cl_uint p6, cl_command_queue* p7, cl_uint p8, const cl_event* p9, cl_event* p10))
//clblasStatus (*clblasDscal)(size_t, cl_double, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDscal_switch_fn;
//static const struct DynamicFnEntry clblasDscal_definition = { "clblasDscal", (void**)&clblasDscal};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasDspmv, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_double p4, const cl_mem p5, size_t p6, const cl_mem p7, size_t p8, int p9, cl_double p10, cl_mem p11, size_t p12, int p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasDspmv)(clblasOrder, clblasUplo, size_t, cl_double, const cl_mem, size_t, const cl_mem, size_t, int, cl_double, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDspmv_switch_fn;
//static const struct DynamicFnEntry clblasDspmv_definition = { "clblasDspmv", (void**)&clblasDspmv};

//openclamdblas_fn14(OPENCLAMDBLAS_FN_clblasDspr, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_double p4, const cl_mem p5, size_t p6, int p7, cl_mem p8, size_t p9, cl_uint p10, cl_command_queue* p11, cl_uint p12, const cl_event* p13, cl_event* p14))
//clblasStatus (*clblasDspr)(clblasOrder, clblasUplo, size_t, cl_double, const cl_mem, size_t, int, cl_mem, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDspr_switch_fn;
//static const struct DynamicFnEntry clblasDspr_definition = { "clblasDspr", (void**)&clblasDspr};

//openclamdblas_fn17(OPENCLAMDBLAS_FN_clblasDspr2, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_double p4, const cl_mem p5, size_t p6, int p7, const cl_mem p8, size_t p9, int p10, cl_mem p11, size_t p12, cl_uint p13, cl_command_queue* p14, cl_uint p15, const cl_event* p16, cl_event* p17))
//clblasStatus (*clblasDspr2)(clblasOrder, clblasUplo, size_t, cl_double, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDspr2_switch_fn;
//static const struct DynamicFnEntry clblasDspr2_definition = { "clblasDspr2", (void**)&clblasDspr2};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasDswap, clblasStatus, (size_t p1, cl_mem p2, size_t p3, int p4, cl_mem p5, size_t p6, int p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasDswap)(size_t, cl_mem, size_t, int, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDswap_switch_fn;
//static const struct DynamicFnEntry clblasDswap_definition = { "clblasDswap", (void**)&clblasDswap};

//openclamdblas_fn21(OPENCLAMDBLAS_FN_clblasDsymm, clblasStatus, (clblasOrder p1, clblasSide p2, clblasUplo p3, size_t p4, size_t p5, cl_double p6, const cl_mem p7, size_t p8, size_t p9, const cl_mem p10, size_t p11, size_t p12, cl_double p13, cl_mem p14, size_t p15, size_t p16, cl_uint p17, cl_command_queue* p18, cl_uint p19, const cl_event* p20, cl_event* p21))
//clblasStatus (*clblasDsymm)(clblasOrder, clblasSide, clblasUplo, size_t, size_t, cl_double, const cl_mem, size_t, size_t, const cl_mem, size_t, size_t, cl_double, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDsymm_switch_fn;
//static const struct DynamicFnEntry clblasDsymm_definition = { "clblasDsymm", (void**)&clblasDsymm};

//openclamdblas_fn19(OPENCLAMDBLAS_FN_clblasDsymv, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_double p4, const cl_mem p5, size_t p6, size_t p7, const cl_mem p8, size_t p9, int p10, cl_double p11, cl_mem p12, size_t p13, int p14, cl_uint p15, cl_command_queue* p16, cl_uint p17, const cl_event* p18, cl_event* p19))
//clblasStatus (*clblasDsymv)(clblasOrder, clblasUplo, size_t, cl_double, const cl_mem, size_t, size_t, const cl_mem, size_t, int, cl_double, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDsymv_switch_fn;
//static const struct DynamicFnEntry clblasDsymv_definition = { "clblasDsymv", (void**)&clblasDsymv};

//openclamdblas_fn15(OPENCLAMDBLAS_FN_clblasDsyr, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_double p4, const cl_mem p5, size_t p6, int p7, cl_mem p8, size_t p9, size_t p10, cl_uint p11, cl_command_queue* p12, cl_uint p13, const cl_event* p14, cl_event* p15))
//clblasStatus (*clblasDsyr)(clblasOrder, clblasUplo, size_t, cl_double, const cl_mem, size_t, int, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDsyr_switch_fn;
//static const struct DynamicFnEntry clblasDsyr_definition = { "clblasDsyr", (void**)&clblasDsyr};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasDsyr2, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_double p4, const cl_mem p5, size_t p6, int p7, const cl_mem p8, size_t p9, int p10, cl_mem p11, size_t p12, size_t p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasDsyr2)(clblasOrder, clblasUplo, size_t, cl_double, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDsyr2_switch_fn;
//static const struct DynamicFnEntry clblasDsyr2_definition = { "clblasDsyr2", (void**)&clblasDsyr2};

//openclamdblas_fn21(OPENCLAMDBLAS_FN_clblasDsyr2k, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, size_t p4, size_t p5, cl_double p6, const cl_mem p7, size_t p8, size_t p9, const cl_mem p10, size_t p11, size_t p12, cl_double p13, cl_mem p14, size_t p15, size_t p16, cl_uint p17, cl_command_queue* p18, cl_uint p19, const cl_event* p20, cl_event* p21))
//clblasStatus (*clblasDsyr2k)(clblasOrder, clblasUplo, clblasTranspose, size_t, size_t, cl_double, const cl_mem, size_t, size_t, const cl_mem, size_t, size_t, cl_double, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDsyr2k_switch_fn;
//static const struct DynamicFnEntry clblasDsyr2k_definition = { "clblasDsyr2k", (void**)&clblasDsyr2k};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasDsyrk, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, size_t p4, size_t p5, cl_double p6, const cl_mem p7, size_t p8, size_t p9, cl_double p10, cl_mem p11, size_t p12, size_t p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasDsyrk)(clblasOrder, clblasUplo, clblasTranspose, size_t, size_t, cl_double, const cl_mem, size_t, size_t, cl_double, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDsyrk_switch_fn;
//static const struct DynamicFnEntry clblasDsyrk_definition = { "clblasDsyrk", (void**)&clblasDsyrk};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasDtbmv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, size_t p6, const cl_mem p7, size_t p8, size_t p9, cl_mem p10, size_t p11, int p12, cl_mem p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasDtbmv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, size_t, const cl_mem, size_t, size_t, cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDtbmv_switch_fn;
//static const struct DynamicFnEntry clblasDtbmv_definition = { "clblasDtbmv", (void**)&clblasDtbmv};

//openclamdblas_fn17(OPENCLAMDBLAS_FN_clblasDtbsv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, size_t p6, const cl_mem p7, size_t p8, size_t p9, cl_mem p10, size_t p11, int p12, cl_uint p13, cl_command_queue* p14, cl_uint p15, const cl_event* p16, cl_event* p17))
//clblasStatus (*clblasDtbsv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, size_t, const cl_mem, size_t, size_t, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDtbsv_switch_fn;
//static const struct DynamicFnEntry clblasDtbsv_definition = { "clblasDtbsv", (void**)&clblasDtbsv};

//openclamdblas_fn16(OPENCLAMDBLAS_FN_clblasDtpmv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, const cl_mem p6, size_t p7, cl_mem p8, size_t p9, int p10, cl_mem p11, cl_uint p12, cl_command_queue* p13, cl_uint p14, const cl_event* p15, cl_event* p16))
//clblasStatus (*clblasDtpmv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, const cl_mem, size_t, cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDtpmv_switch_fn;
//static const struct DynamicFnEntry clblasDtpmv_definition = { "clblasDtpmv", (void**)&clblasDtpmv};

//openclamdblas_fn15(OPENCLAMDBLAS_FN_clblasDtpsv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, const cl_mem p6, size_t p7, cl_mem p8, size_t p9, int p10, cl_uint p11, cl_command_queue* p12, cl_uint p13, const cl_event* p14, cl_event* p15))
//clblasStatus (*clblasDtpsv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, const cl_mem, size_t, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDtpsv_switch_fn;
//static const struct DynamicFnEntry clblasDtpsv_definition = { "clblasDtpsv", (void**)&clblasDtpsv};

//openclamdblas_fn19(OPENCLAMDBLAS_FN_clblasDtrmm, clblasStatus, (clblasOrder p1, clblasSide p2, clblasUplo p3, clblasTranspose p4, clblasDiag p5, size_t p6, size_t p7, cl_double p8, const cl_mem p9, size_t p10, size_t p11, cl_mem p12, size_t p13, size_t p14, cl_uint p15, cl_command_queue* p16, cl_uint p17, const cl_event* p18, cl_event* p19))
//clblasStatus (*clblasDtrmm)(clblasOrder, clblasSide, clblasUplo, clblasTranspose, clblasDiag, size_t, size_t, cl_double, const cl_mem, size_t, size_t, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDtrmm_switch_fn;
//static const struct DynamicFnEntry clblasDtrmm_definition = { "clblasDtrmm", (void**)&clblasDtrmm};

//openclamdblas_fn17(OPENCLAMDBLAS_FN_clblasDtrmv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, const cl_mem p6, size_t p7, size_t p8, cl_mem p9, size_t p10, int p11, cl_mem p12, cl_uint p13, cl_command_queue* p14, cl_uint p15, const cl_event* p16, cl_event* p17))
//clblasStatus (*clblasDtrmv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, const cl_mem, size_t, size_t, cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDtrmv_switch_fn;
//static const struct DynamicFnEntry clblasDtrmv_definition = { "clblasDtrmv", (void**)&clblasDtrmv};

//openclamdblas_fn19(OPENCLAMDBLAS_FN_clblasDtrsm, clblasStatus, (clblasOrder p1, clblasSide p2, clblasUplo p3, clblasTranspose p4, clblasDiag p5, size_t p6, size_t p7, cl_double p8, const cl_mem p9, size_t p10, size_t p11, cl_mem p12, size_t p13, size_t p14, cl_uint p15, cl_command_queue* p16, cl_uint p17, const cl_event* p18, cl_event* p19))
//clblasStatus (*clblasDtrsm)(clblasOrder, clblasSide, clblasUplo, clblasTranspose, clblasDiag, size_t, size_t, cl_double, const cl_mem, size_t, size_t, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDtrsm_switch_fn;
//static const struct DynamicFnEntry clblasDtrsm_definition = { "clblasDtrsm", (void**)&clblasDtrsm};

//openclamdblas_fn16(OPENCLAMDBLAS_FN_clblasDtrsv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, const cl_mem p6, size_t p7, size_t p8, cl_mem p9, size_t p10, int p11, cl_uint p12, cl_command_queue* p13, cl_uint p14, const cl_event* p15, cl_event* p16))
//clblasStatus (*clblasDtrsv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, const cl_mem, size_t, size_t, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDtrsv_switch_fn;
//static const struct DynamicFnEntry clblasDtrsv_definition = { "clblasDtrsv", (void**)&clblasDtrsv};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasDzasum, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, cl_mem p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasDzasum)(size_t, cl_mem, size_t, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDzasum_switch_fn;
//static const struct DynamicFnEntry clblasDzasum_definition = { "clblasDzasum", (void**)&clblasDzasum};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasDznrm2, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, cl_mem p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasDznrm2)(size_t, cl_mem, size_t, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasDznrm2_switch_fn;
//static const struct DynamicFnEntry clblasDznrm2_definition = { "clblasDznrm2", (void**)&clblasDznrm2};

//openclamdblas_fn3(OPENCLAMDBLAS_FN_clblasGetVersion, clblasStatus, (cl_uint* p1, cl_uint* p2, cl_uint* p3))
//clblasStatus (*clblasGetVersion)(cl_uint*, cl_uint*, cl_uint*) =
//        OPENCLAMDBLAS_FN_clblasGetVersion_switch_fn;
//static const struct DynamicFnEntry clblasGetVersion_definition = { "clblasGetVersion", (void**)&clblasGetVersion};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasSasum, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, cl_mem p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasSasum)(size_t, cl_mem, size_t, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSasum_switch_fn;
//static const struct DynamicFnEntry clblasSasum_definition = { "clblasSasum", (void**)&clblasSasum};

//openclamdblas_fn13(OPENCLAMDBLAS_FN_clblasSaxpy, clblasStatus, (size_t p1, cl_float p2, const cl_mem p3, size_t p4, int p5, cl_mem p6, size_t p7, int p8, cl_uint p9, cl_command_queue* p10, cl_uint p11, const cl_event* p12, cl_event* p13))
//clblasStatus (*clblasSaxpy)(size_t, cl_float, const cl_mem, size_t, int, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSaxpy_switch_fn;
//static const struct DynamicFnEntry clblasSaxpy_definition = { "clblasSaxpy", (void**)&clblasSaxpy};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasScasum, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, cl_mem p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasScasum)(size_t, cl_mem, size_t, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasScasum_switch_fn;
//static const struct DynamicFnEntry clblasScasum_definition = { "clblasScasum", (void**)&clblasScasum};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasScnrm2, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, cl_mem p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasScnrm2)(size_t, cl_mem, size_t, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasScnrm2_switch_fn;
//static const struct DynamicFnEntry clblasScnrm2_definition = { "clblasScnrm2", (void**)&clblasScnrm2};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasScopy, clblasStatus, (size_t p1, const cl_mem p2, size_t p3, int p4, cl_mem p5, size_t p6, int p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasScopy)(size_t, const cl_mem, size_t, int, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasScopy_switch_fn;
//static const struct DynamicFnEntry clblasScopy_definition = { "clblasScopy", (void**)&clblasScopy};

//openclamdblas_fn15(OPENCLAMDBLAS_FN_clblasSdot, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, const cl_mem p7, size_t p8, int p9, cl_mem p10, cl_uint p11, cl_command_queue* p12, cl_uint p13, const cl_event* p14, cl_event* p15))
//clblasStatus (*clblasSdot)(size_t, cl_mem, size_t, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSdot_switch_fn;
//static const struct DynamicFnEntry clblasSdot_definition = { "clblasSdot", (void**)&clblasSdot};

openclamdblas_fn0(OPENCLAMDBLAS_FN_clblasSetup, clblasStatus, ())
clblasStatus (*clblasSetup)() =
        OPENCLAMDBLAS_FN_clblasSetup_switch_fn;
static const struct DynamicFnEntry clblasSetup_definition = { "clblasSetup", (void**)&clblasSetup};

//openclamdblas_fn22(OPENCLAMDBLAS_FN_clblasSgbmv, clblasStatus, (clblasOrder p1, clblasTranspose p2, size_t p3, size_t p4, size_t p5, size_t p6, cl_float p7, const cl_mem p8, size_t p9, size_t p10, const cl_mem p11, size_t p12, int p13, cl_float p14, cl_mem p15, size_t p16, int p17, cl_uint p18, cl_command_queue* p19, cl_uint p20, const cl_event* p21, cl_event* p22))
//clblasStatus (*clblasSgbmv)(clblasOrder, clblasTranspose, size_t, size_t, size_t, size_t, cl_float, const cl_mem, size_t, size_t, const cl_mem, size_t, int, cl_float, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSgbmv_switch_fn;
//static const struct DynamicFnEntry clblasSgbmv_definition = { "clblasSgbmv", (void**)&clblasSgbmv};

openclamdblas_fn22(OPENCLAMDBLAS_FN_clblasSgemm, clblasStatus, (clblasOrder p1, clblasTranspose p2, clblasTranspose p3, size_t p4, size_t p5, size_t p6, cl_float p7, const cl_mem p8, size_t p9, size_t p10, const cl_mem p11, size_t p12, size_t p13, cl_float p14, cl_mem p15, size_t p16, size_t p17, cl_uint p18, cl_command_queue* p19, cl_uint p20, const cl_event* p21, cl_event* p22))
clblasStatus (*clblasSgemm)(clblasOrder, clblasTranspose, clblasTranspose, size_t, size_t, size_t, cl_float, const cl_mem, size_t, size_t, const cl_mem, size_t, size_t, cl_float, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
        OPENCLAMDBLAS_FN_clblasSgemm_switch_fn;
static const struct DynamicFnEntry clblasSgemm_definition = { "clblasSgemm", (void**)&clblasSgemm};

//openclamdblas_fn20(OPENCLAMDBLAS_FN_clblasSgemv, clblasStatus, (clblasOrder p1, clblasTranspose p2, size_t p3, size_t p4, cl_float p5, const cl_mem p6, size_t p7, size_t p8, const cl_mem p9, size_t p10, int p11, cl_float p12, cl_mem p13, size_t p14, int p15, cl_uint p16, cl_command_queue* p17, cl_uint p18, const cl_event* p19, cl_event* p20))
//clblasStatus (*clblasSgemv)(clblasOrder, clblasTranspose, size_t, size_t, cl_float, const cl_mem, size_t, size_t, const cl_mem, size_t, int, cl_float, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSgemv_switch_fn;
//static const struct DynamicFnEntry clblasSgemv_definition = { "clblasSgemv", (void**)&clblasSgemv};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasSger, clblasStatus, (clblasOrder p1, size_t p2, size_t p3, cl_float p4, const cl_mem p5, size_t p6, int p7, const cl_mem p8, size_t p9, int p10, cl_mem p11, size_t p12, size_t p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasSger)(clblasOrder, size_t, size_t, cl_float, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSger_switch_fn;
//static const struct DynamicFnEntry clblasSger_definition = { "clblasSger", (void**)&clblasSger};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasSnrm2, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, cl_mem p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasSnrm2)(size_t, cl_mem, size_t, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSnrm2_switch_fn;
//static const struct DynamicFnEntry clblasSnrm2_definition = { "clblasSnrm2", (void**)&clblasSnrm2};

//openclamdblas_fn14(OPENCLAMDBLAS_FN_clblasSrot, clblasStatus, (size_t p1, cl_mem p2, size_t p3, int p4, cl_mem p5, size_t p6, int p7, cl_float p8, cl_float p9, cl_uint p10, cl_command_queue* p11, cl_uint p12, const cl_event* p13, cl_event* p14))
//clblasStatus (*clblasSrot)(size_t, cl_mem, size_t, int, cl_mem, size_t, int, cl_float, cl_float, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSrot_switch_fn;
//static const struct DynamicFnEntry clblasSrot_definition = { "clblasSrot", (void**)&clblasSrot};

//openclamdblas_fn13(OPENCLAMDBLAS_FN_clblasSrotg, clblasStatus, (cl_mem p1, size_t p2, cl_mem p3, size_t p4, cl_mem p5, size_t p6, cl_mem p7, size_t p8, cl_uint p9, cl_command_queue* p10, cl_uint p11, const cl_event* p12, cl_event* p13))
//clblasStatus (*clblasSrotg)(cl_mem, size_t, cl_mem, size_t, cl_mem, size_t, cl_mem, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSrotg_switch_fn;
//static const struct DynamicFnEntry clblasSrotg_definition = { "clblasSrotg", (void**)&clblasSrotg};

//openclamdblas_fn14(OPENCLAMDBLAS_FN_clblasSrotm, clblasStatus, (size_t p1, cl_mem p2, size_t p3, int p4, cl_mem p5, size_t p6, int p7, const cl_mem p8, size_t p9, cl_uint p10, cl_command_queue* p11, cl_uint p12, const cl_event* p13, cl_event* p14))
//clblasStatus (*clblasSrotm)(size_t, cl_mem, size_t, int, cl_mem, size_t, int, const cl_mem, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSrotm_switch_fn;
//static const struct DynamicFnEntry clblasSrotm_definition = { "clblasSrotm", (void**)&clblasSrotm};

//openclamdblas_fn15(OPENCLAMDBLAS_FN_clblasSrotmg, clblasStatus, (cl_mem p1, size_t p2, cl_mem p3, size_t p4, cl_mem p5, size_t p6, const cl_mem p7, size_t p8, cl_mem p9, size_t p10, cl_uint p11, cl_command_queue* p12, cl_uint p13, const cl_event* p14, cl_event* p15))
//clblasStatus (*clblasSrotmg)(cl_mem, size_t, cl_mem, size_t, cl_mem, size_t, const cl_mem, size_t, cl_mem, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSrotmg_switch_fn;
//static const struct DynamicFnEntry clblasSrotmg_definition = { "clblasSrotmg", (void**)&clblasSrotmg};

//openclamdblas_fn20(OPENCLAMDBLAS_FN_clblasSsbmv, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, size_t p4, cl_float p5, const cl_mem p6, size_t p7, size_t p8, const cl_mem p9, size_t p10, int p11, cl_float p12, cl_mem p13, size_t p14, int p15, cl_uint p16, cl_command_queue* p17, cl_uint p18, const cl_event* p19, cl_event* p20))
//clblasStatus (*clblasSsbmv)(clblasOrder, clblasUplo, size_t, size_t, cl_float, const cl_mem, size_t, size_t, const cl_mem, size_t, int, cl_float, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSsbmv_switch_fn;
//static const struct DynamicFnEntry clblasSsbmv_definition = { "clblasSsbmv", (void**)&clblasSsbmv};

//openclamdblas_fn10(OPENCLAMDBLAS_FN_clblasSscal, clblasStatus, (size_t p1, cl_float p2, cl_mem p3, size_t p4, int p5, cl_uint p6, cl_command_queue* p7, cl_uint p8, const cl_event* p9, cl_event* p10))
//clblasStatus (*clblasSscal)(size_t, cl_float, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSscal_switch_fn;
//static const struct DynamicFnEntry clblasSscal_definition = { "clblasSscal", (void**)&clblasSscal};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasSspmv, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_float p4, const cl_mem p5, size_t p6, const cl_mem p7, size_t p8, int p9, cl_float p10, cl_mem p11, size_t p12, int p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasSspmv)(clblasOrder, clblasUplo, size_t, cl_float, const cl_mem, size_t, const cl_mem, size_t, int, cl_float, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSspmv_switch_fn;
//static const struct DynamicFnEntry clblasSspmv_definition = { "clblasSspmv", (void**)&clblasSspmv};

//openclamdblas_fn14(OPENCLAMDBLAS_FN_clblasSspr, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_float p4, const cl_mem p5, size_t p6, int p7, cl_mem p8, size_t p9, cl_uint p10, cl_command_queue* p11, cl_uint p12, const cl_event* p13, cl_event* p14))
//clblasStatus (*clblasSspr)(clblasOrder, clblasUplo, size_t, cl_float, const cl_mem, size_t, int, cl_mem, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSspr_switch_fn;
//static const struct DynamicFnEntry clblasSspr_definition = { "clblasSspr", (void**)&clblasSspr};

//openclamdblas_fn17(OPENCLAMDBLAS_FN_clblasSspr2, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_float p4, const cl_mem p5, size_t p6, int p7, const cl_mem p8, size_t p9, int p10, cl_mem p11, size_t p12, cl_uint p13, cl_command_queue* p14, cl_uint p15, const cl_event* p16, cl_event* p17))
//clblasStatus (*clblasSspr2)(clblasOrder, clblasUplo, size_t, cl_float, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSspr2_switch_fn;
//static const struct DynamicFnEntry clblasSspr2_definition = { "clblasSspr2", (void**)&clblasSspr2};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasSswap, clblasStatus, (size_t p1, cl_mem p2, size_t p3, int p4, cl_mem p5, size_t p6, int p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasSswap)(size_t, cl_mem, size_t, int, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSswap_switch_fn;
//static const struct DynamicFnEntry clblasSswap_definition = { "clblasSswap", (void**)&clblasSswap};

//openclamdblas_fn21(OPENCLAMDBLAS_FN_clblasSsymm, clblasStatus, (clblasOrder p1, clblasSide p2, clblasUplo p3, size_t p4, size_t p5, cl_float p6, const cl_mem p7, size_t p8, size_t p9, const cl_mem p10, size_t p11, size_t p12, cl_float p13, cl_mem p14, size_t p15, size_t p16, cl_uint p17, cl_command_queue* p18, cl_uint p19, const cl_event* p20, cl_event* p21))
//clblasStatus (*clblasSsymm)(clblasOrder, clblasSide, clblasUplo, size_t, size_t, cl_float, const cl_mem, size_t, size_t, const cl_mem, size_t, size_t, cl_float, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSsymm_switch_fn;
//static const struct DynamicFnEntry clblasSsymm_definition = { "clblasSsymm", (void**)&clblasSsymm};

//openclamdblas_fn19(OPENCLAMDBLAS_FN_clblasSsymv, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_float p4, const cl_mem p5, size_t p6, size_t p7, const cl_mem p8, size_t p9, int p10, cl_float p11, cl_mem p12, size_t p13, int p14, cl_uint p15, cl_command_queue* p16, cl_uint p17, const cl_event* p18, cl_event* p19))
//clblasStatus (*clblasSsymv)(clblasOrder, clblasUplo, size_t, cl_float, const cl_mem, size_t, size_t, const cl_mem, size_t, int, cl_float, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSsymv_switch_fn;
//static const struct DynamicFnEntry clblasSsymv_definition = { "clblasSsymv", (void**)&clblasSsymv};

//openclamdblas_fn15(OPENCLAMDBLAS_FN_clblasSsyr, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_float p4, const cl_mem p5, size_t p6, int p7, cl_mem p8, size_t p9, size_t p10, cl_uint p11, cl_command_queue* p12, cl_uint p13, const cl_event* p14, cl_event* p15))
//clblasStatus (*clblasSsyr)(clblasOrder, clblasUplo, size_t, cl_float, const cl_mem, size_t, int, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSsyr_switch_fn;
//static const struct DynamicFnEntry clblasSsyr_definition = { "clblasSsyr", (void**)&clblasSsyr};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasSsyr2, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_float p4, const cl_mem p5, size_t p6, int p7, const cl_mem p8, size_t p9, int p10, cl_mem p11, size_t p12, size_t p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasSsyr2)(clblasOrder, clblasUplo, size_t, cl_float, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSsyr2_switch_fn;
//static const struct DynamicFnEntry clblasSsyr2_definition = { "clblasSsyr2", (void**)&clblasSsyr2};

//openclamdblas_fn21(OPENCLAMDBLAS_FN_clblasSsyr2k, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, size_t p4, size_t p5, cl_float p6, const cl_mem p7, size_t p8, size_t p9, const cl_mem p10, size_t p11, size_t p12, cl_float p13, cl_mem p14, size_t p15, size_t p16, cl_uint p17, cl_command_queue* p18, cl_uint p19, const cl_event* p20, cl_event* p21))
//clblasStatus (*clblasSsyr2k)(clblasOrder, clblasUplo, clblasTranspose, size_t, size_t, cl_float, const cl_mem, size_t, size_t, const cl_mem, size_t, size_t, cl_float, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSsyr2k_switch_fn;
//static const struct DynamicFnEntry clblasSsyr2k_definition = { "clblasSsyr2k", (void**)&clblasSsyr2k};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasSsyrk, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, size_t p4, size_t p5, cl_float p6, const cl_mem p7, size_t p8, size_t p9, cl_float p10, cl_mem p11, size_t p12, size_t p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasSsyrk)(clblasOrder, clblasUplo, clblasTranspose, size_t, size_t, cl_float, const cl_mem, size_t, size_t, cl_float, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasSsyrk_switch_fn;
//static const struct DynamicFnEntry clblasSsyrk_definition = { "clblasSsyrk", (void**)&clblasSsyrk};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasStbmv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, size_t p6, const cl_mem p7, size_t p8, size_t p9, cl_mem p10, size_t p11, int p12, cl_mem p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasStbmv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, size_t, const cl_mem, size_t, size_t, cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasStbmv_switch_fn;
//static const struct DynamicFnEntry clblasStbmv_definition = { "clblasStbmv", (void**)&clblasStbmv};

//openclamdblas_fn17(OPENCLAMDBLAS_FN_clblasStbsv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, size_t p6, const cl_mem p7, size_t p8, size_t p9, cl_mem p10, size_t p11, int p12, cl_uint p13, cl_command_queue* p14, cl_uint p15, const cl_event* p16, cl_event* p17))
//clblasStatus (*clblasStbsv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, size_t, const cl_mem, size_t, size_t, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasStbsv_switch_fn;
//static const struct DynamicFnEntry clblasStbsv_definition = { "clblasStbsv", (void**)&clblasStbsv};

//openclamdblas_fn16(OPENCLAMDBLAS_FN_clblasStpmv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, const cl_mem p6, size_t p7, cl_mem p8, size_t p9, int p10, cl_mem p11, cl_uint p12, cl_command_queue* p13, cl_uint p14, const cl_event* p15, cl_event* p16))
//clblasStatus (*clblasStpmv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, const cl_mem, size_t, cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasStpmv_switch_fn;
//static const struct DynamicFnEntry clblasStpmv_definition = { "clblasStpmv", (void**)&clblasStpmv};

//openclamdblas_fn15(OPENCLAMDBLAS_FN_clblasStpsv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, const cl_mem p6, size_t p7, cl_mem p8, size_t p9, int p10, cl_uint p11, cl_command_queue* p12, cl_uint p13, const cl_event* p14, cl_event* p15))
//clblasStatus (*clblasStpsv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, const cl_mem, size_t, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasStpsv_switch_fn;
//static const struct DynamicFnEntry clblasStpsv_definition = { "clblasStpsv", (void**)&clblasStpsv};

//openclamdblas_fn19(OPENCLAMDBLAS_FN_clblasStrmm, clblasStatus, (clblasOrder p1, clblasSide p2, clblasUplo p3, clblasTranspose p4, clblasDiag p5, size_t p6, size_t p7, cl_float p8, const cl_mem p9, size_t p10, size_t p11, cl_mem p12, size_t p13, size_t p14, cl_uint p15, cl_command_queue* p16, cl_uint p17, const cl_event* p18, cl_event* p19))
//clblasStatus (*clblasStrmm)(clblasOrder, clblasSide, clblasUplo, clblasTranspose, clblasDiag, size_t, size_t, cl_float, const cl_mem, size_t, size_t, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasStrmm_switch_fn;
//static const struct DynamicFnEntry clblasStrmm_definition = { "clblasStrmm", (void**)&clblasStrmm};

//openclamdblas_fn17(OPENCLAMDBLAS_FN_clblasStrmv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, const cl_mem p6, size_t p7, size_t p8, cl_mem p9, size_t p10, int p11, cl_mem p12, cl_uint p13, cl_command_queue* p14, cl_uint p15, const cl_event* p16, cl_event* p17))
//clblasStatus (*clblasStrmv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, const cl_mem, size_t, size_t, cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasStrmv_switch_fn;
//static const struct DynamicFnEntry clblasStrmv_definition = { "clblasStrmv", (void**)&clblasStrmv};

//openclamdblas_fn19(OPENCLAMDBLAS_FN_clblasStrsm, clblasStatus, (clblasOrder p1, clblasSide p2, clblasUplo p3, clblasTranspose p4, clblasDiag p5, size_t p6, size_t p7, cl_float p8, const cl_mem p9, size_t p10, size_t p11, cl_mem p12, size_t p13, size_t p14, cl_uint p15, cl_command_queue* p16, cl_uint p17, const cl_event* p18, cl_event* p19))
//clblasStatus (*clblasStrsm)(clblasOrder, clblasSide, clblasUplo, clblasTranspose, clblasDiag, size_t, size_t, cl_float, const cl_mem, size_t, size_t, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasStrsm_switch_fn;
//static const struct DynamicFnEntry clblasStrsm_definition = { "clblasStrsm", (void**)&clblasStrsm};

//openclamdblas_fn16(OPENCLAMDBLAS_FN_clblasStrsv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, const cl_mem p6, size_t p7, size_t p8, cl_mem p9, size_t p10, int p11, cl_uint p12, cl_command_queue* p13, cl_uint p14, const cl_event* p15, cl_event* p16))
//clblasStatus (*clblasStrsv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, const cl_mem, size_t, size_t, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasStrsv_switch_fn;
//static const struct DynamicFnEntry clblasStrsv_definition = { "clblasStrsv", (void**)&clblasStrsv};

openclamdblas_fn0(OPENCLAMDBLAS_FN_clblasTeardown, void, ())
void (*clblasTeardown)() =
        OPENCLAMDBLAS_FN_clblasTeardown_switch_fn;
static const struct DynamicFnEntry clblasTeardown_definition = { "clblasTeardown", (void**)&clblasTeardown};

//openclamdblas_fn13(OPENCLAMDBLAS_FN_clblasZaxpy, clblasStatus, (size_t p1, cl_double2 p2, const cl_mem p3, size_t p4, int p5, cl_mem p6, size_t p7, int p8, cl_uint p9, cl_command_queue* p10, cl_uint p11, const cl_event* p12, cl_event* p13))
//clblasStatus (*clblasZaxpy)(size_t, cl_double2, const cl_mem, size_t, int, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZaxpy_switch_fn;
//static const struct DynamicFnEntry clblasZaxpy_definition = { "clblasZaxpy", (void**)&clblasZaxpy};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasZcopy, clblasStatus, (size_t p1, const cl_mem p2, size_t p3, int p4, cl_mem p5, size_t p6, int p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasZcopy)(size_t, const cl_mem, size_t, int, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZcopy_switch_fn;
//static const struct DynamicFnEntry clblasZcopy_definition = { "clblasZcopy", (void**)&clblasZcopy};

//openclamdblas_fn15(OPENCLAMDBLAS_FN_clblasZdotc, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, const cl_mem p7, size_t p8, int p9, cl_mem p10, cl_uint p11, cl_command_queue* p12, cl_uint p13, const cl_event* p14, cl_event* p15))
//clblasStatus (*clblasZdotc)(size_t, cl_mem, size_t, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZdotc_switch_fn;
//static const struct DynamicFnEntry clblasZdotc_definition = { "clblasZdotc", (void**)&clblasZdotc};

//openclamdblas_fn15(OPENCLAMDBLAS_FN_clblasZdotu, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, const cl_mem p7, size_t p8, int p9, cl_mem p10, cl_uint p11, cl_command_queue* p12, cl_uint p13, const cl_event* p14, cl_event* p15))
//clblasStatus (*clblasZdotu)(size_t, cl_mem, size_t, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZdotu_switch_fn;
//static const struct DynamicFnEntry clblasZdotu_definition = { "clblasZdotu", (void**)&clblasZdotu};

//openclamdblas_fn14(OPENCLAMDBLAS_FN_clblasZdrot, clblasStatus, (size_t p1, cl_mem p2, size_t p3, int p4, cl_mem p5, size_t p6, int p7, cl_double p8, cl_double p9, cl_uint p10, cl_command_queue* p11, cl_uint p12, const cl_event* p13, cl_event* p14))
//clblasStatus (*clblasZdrot)(size_t, cl_mem, size_t, int, cl_mem, size_t, int, cl_double, cl_double, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZdrot_switch_fn;
//static const struct DynamicFnEntry clblasZdrot_definition = { "clblasZdrot", (void**)&clblasZdrot};

//openclamdblas_fn10(OPENCLAMDBLAS_FN_clblasZdscal, clblasStatus, (size_t p1, cl_double p2, cl_mem p3, size_t p4, int p5, cl_uint p6, cl_command_queue* p7, cl_uint p8, const cl_event* p9, cl_event* p10))
//clblasStatus (*clblasZdscal)(size_t, cl_double, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZdscal_switch_fn;
//static const struct DynamicFnEntry clblasZdscal_definition = { "clblasZdscal", (void**)&clblasZdscal};

//openclamdblas_fn22(OPENCLAMDBLAS_FN_clblasZgbmv, clblasStatus, (clblasOrder p1, clblasTranspose p2, size_t p3, size_t p4, size_t p5, size_t p6, cl_double2 p7, const cl_mem p8, size_t p9, size_t p10, const cl_mem p11, size_t p12, int p13, cl_double2 p14, cl_mem p15, size_t p16, int p17, cl_uint p18, cl_command_queue* p19, cl_uint p20, const cl_event* p21, cl_event* p22))
//clblasStatus (*clblasZgbmv)(clblasOrder, clblasTranspose, size_t, size_t, size_t, size_t, cl_double2, const cl_mem, size_t, size_t, const cl_mem, size_t, int, cl_double2, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZgbmv_switch_fn;
//static const struct DynamicFnEntry clblasZgbmv_definition = { "clblasZgbmv", (void**)&clblasZgbmv};

openclamdblas_fn22(OPENCLAMDBLAS_FN_clblasZgemm, clblasStatus, (clblasOrder p1, clblasTranspose p2, clblasTranspose p3, size_t p4, size_t p5, size_t p6, DoubleComplex p7, const cl_mem p8, size_t p9, size_t p10, const cl_mem p11, size_t p12, size_t p13, DoubleComplex p14, cl_mem p15, size_t p16, size_t p17, cl_uint p18, cl_command_queue* p19, cl_uint p20, const cl_event* p21, cl_event* p22))
clblasStatus (*clblasZgemm)(clblasOrder, clblasTranspose, clblasTranspose, size_t, size_t, size_t, DoubleComplex, const cl_mem, size_t, size_t, const cl_mem, size_t, size_t, DoubleComplex, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
        OPENCLAMDBLAS_FN_clblasZgemm_switch_fn;
static const struct DynamicFnEntry clblasZgemm_definition = { "clblasZgemm", (void**)&clblasZgemm};

//openclamdblas_fn20(OPENCLAMDBLAS_FN_clblasZgemv, clblasStatus, (clblasOrder p1, clblasTranspose p2, size_t p3, size_t p4, DoubleComplex p5, const cl_mem p6, size_t p7, size_t p8, const cl_mem p9, size_t p10, int p11, DoubleComplex p12, cl_mem p13, size_t p14, int p15, cl_uint p16, cl_command_queue* p17, cl_uint p18, const cl_event* p19, cl_event* p20))
//clblasStatus (*clblasZgemv)(clblasOrder, clblasTranspose, size_t, size_t, DoubleComplex, const cl_mem, size_t, size_t, const cl_mem, size_t, int, DoubleComplex, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZgemv_switch_fn;
//static const struct DynamicFnEntry clblasZgemv_definition = { "clblasZgemv", (void**)&clblasZgemv};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasZgerc, clblasStatus, (clblasOrder p1, size_t p2, size_t p3, cl_double2 p4, const cl_mem p5, size_t p6, int p7, const cl_mem p8, size_t p9, int p10, cl_mem p11, size_t p12, size_t p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasZgerc)(clblasOrder, size_t, size_t, cl_double2, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZgerc_switch_fn;
//static const struct DynamicFnEntry clblasZgerc_definition = { "clblasZgerc", (void**)&clblasZgerc};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasZgeru, clblasStatus, (clblasOrder p1, size_t p2, size_t p3, cl_double2 p4, const cl_mem p5, size_t p6, int p7, const cl_mem p8, size_t p9, int p10, cl_mem p11, size_t p12, size_t p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasZgeru)(clblasOrder, size_t, size_t, cl_double2, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZgeru_switch_fn;
//static const struct DynamicFnEntry clblasZgeru_definition = { "clblasZgeru", (void**)&clblasZgeru};

//openclamdblas_fn20(OPENCLAMDBLAS_FN_clblasZhbmv, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, size_t p4, cl_double2 p5, const cl_mem p6, size_t p7, size_t p8, const cl_mem p9, size_t p10, int p11, cl_double2 p12, cl_mem p13, size_t p14, int p15, cl_uint p16, cl_command_queue* p17, cl_uint p18, const cl_event* p19, cl_event* p20))
//clblasStatus (*clblasZhbmv)(clblasOrder, clblasUplo, size_t, size_t, cl_double2, const cl_mem, size_t, size_t, const cl_mem, size_t, int, cl_double2, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZhbmv_switch_fn;
//static const struct DynamicFnEntry clblasZhbmv_definition = { "clblasZhbmv", (void**)&clblasZhbmv};

//openclamdblas_fn21(OPENCLAMDBLAS_FN_clblasZhemm, clblasStatus, (clblasOrder p1, clblasSide p2, clblasUplo p3, size_t p4, size_t p5, cl_double2 p6, const cl_mem p7, size_t p8, size_t p9, const cl_mem p10, size_t p11, size_t p12, cl_double2 p13, cl_mem p14, size_t p15, size_t p16, cl_uint p17, cl_command_queue* p18, cl_uint p19, const cl_event* p20, cl_event* p21))
//clblasStatus (*clblasZhemm)(clblasOrder, clblasSide, clblasUplo, size_t, size_t, cl_double2, const cl_mem, size_t, size_t, const cl_mem, size_t, size_t, cl_double2, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZhemm_switch_fn;
//static const struct DynamicFnEntry clblasZhemm_definition = { "clblasZhemm", (void**)&clblasZhemm};

//openclamdblas_fn19(OPENCLAMDBLAS_FN_clblasZhemv, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, DoubleComplex p4, const cl_mem p5, size_t p6, size_t p7, const cl_mem p8, size_t p9, int p10, DoubleComplex p11, cl_mem p12, size_t p13, int p14, cl_uint p15, cl_command_queue* p16, cl_uint p17, const cl_event* p18, cl_event* p19))
//clblasStatus (*clblasZhemv)(clblasOrder, clblasUplo, size_t, DoubleComplex, const cl_mem, size_t, size_t, const cl_mem, size_t, int, DoubleComplex, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZhemv_switch_fn;
//static const struct DynamicFnEntry clblasZhemv_definition = { "clblasZhemv", (void**)&clblasZhemv};

//openclamdblas_fn15(OPENCLAMDBLAS_FN_clblasZher, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_double p4, const cl_mem p5, size_t p6, int p7, cl_mem p8, size_t p9, size_t p10, cl_uint p11, cl_command_queue* p12, cl_uint p13, const cl_event* p14, cl_event* p15))
//clblasStatus (*clblasZher)(clblasOrder, clblasUplo, size_t, cl_double, const cl_mem, size_t, int, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZher_switch_fn;
//static const struct DynamicFnEntry clblasZher_definition = { "clblasZher", (void**)&clblasZher};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasZher2, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_double2 p4, const cl_mem p5, size_t p6, int p7, const cl_mem p8, size_t p9, int p10, cl_mem p11, size_t p12, size_t p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasZher2)(clblasOrder, clblasUplo, size_t, cl_double2, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZher2_switch_fn;
//static const struct DynamicFnEntry clblasZher2_definition = { "clblasZher2", (void**)&clblasZher2};

//openclamdblas_fn21(OPENCLAMDBLAS_FN_clblasZher2k, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, size_t p4, size_t p5, DoubleComplex p6, const cl_mem p7, size_t p8, size_t p9, const cl_mem p10, size_t p11, size_t p12, cl_double p13, cl_mem p14, size_t p15, size_t p16, cl_uint p17, cl_command_queue* p18, cl_uint p19, const cl_event* p20, cl_event* p21))
//clblasStatus (*clblasZher2k)(clblasOrder, clblasUplo, clblasTranspose, size_t, size_t, DoubleComplex, const cl_mem, size_t, size_t, const cl_mem, size_t, size_t, cl_double, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZher2k_switch_fn;
//static const struct DynamicFnEntry clblasZher2k_definition = { "clblasZher2k", (void**)&clblasZher2k};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasZherk, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, size_t p4, size_t p5, double p6, const cl_mem p7, size_t p8, size_t p9, double p10, cl_mem p11, size_t p12, size_t p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasZherk)(clblasOrder, clblasUplo, clblasTranspose, size_t, size_t, double, const cl_mem, size_t, size_t, double, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZherk_switch_fn;
//static const struct DynamicFnEntry clblasZherk_definition = { "clblasZherk", (void**)&clblasZherk};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasZhpmv, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_double2 p4, const cl_mem p5, size_t p6, const cl_mem p7, size_t p8, int p9, cl_double2 p10, cl_mem p11, size_t p12, int p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasZhpmv)(clblasOrder, clblasUplo, size_t, cl_double2, const cl_mem, size_t, const cl_mem, size_t, int, cl_double2, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZhpmv_switch_fn;
//static const struct DynamicFnEntry clblasZhpmv_definition = { "clblasZhpmv", (void**)&clblasZhpmv};

//openclamdblas_fn14(OPENCLAMDBLAS_FN_clblasZhpr, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_double p4, const cl_mem p5, size_t p6, int p7, cl_mem p8, size_t p9, cl_uint p10, cl_command_queue* p11, cl_uint p12, const cl_event* p13, cl_event* p14))
//clblasStatus (*clblasZhpr)(clblasOrder, clblasUplo, size_t, cl_double, const cl_mem, size_t, int, cl_mem, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZhpr_switch_fn;
//static const struct DynamicFnEntry clblasZhpr_definition = { "clblasZhpr", (void**)&clblasZhpr};

//openclamdblas_fn17(OPENCLAMDBLAS_FN_clblasZhpr2, clblasStatus, (clblasOrder p1, clblasUplo p2, size_t p3, cl_double2 p4, const cl_mem p5, size_t p6, int p7, const cl_mem p8, size_t p9, int p10, cl_mem p11, size_t p12, cl_uint p13, cl_command_queue* p14, cl_uint p15, const cl_event* p16, cl_event* p17))
//clblasStatus (*clblasZhpr2)(clblasOrder, clblasUplo, size_t, cl_double2, const cl_mem, size_t, int, const cl_mem, size_t, int, cl_mem, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZhpr2_switch_fn;
//static const struct DynamicFnEntry clblasZhpr2_definition = { "clblasZhpr2", (void**)&clblasZhpr2};

//openclamdblas_fn13(OPENCLAMDBLAS_FN_clblasZrotg, clblasStatus, (cl_mem p1, size_t p2, cl_mem p3, size_t p4, cl_mem p5, size_t p6, cl_mem p7, size_t p8, cl_uint p9, cl_command_queue* p10, cl_uint p11, const cl_event* p12, cl_event* p13))
//clblasStatus (*clblasZrotg)(cl_mem, size_t, cl_mem, size_t, cl_mem, size_t, cl_mem, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZrotg_switch_fn;
//static const struct DynamicFnEntry clblasZrotg_definition = { "clblasZrotg", (void**)&clblasZrotg};

//openclamdblas_fn10(OPENCLAMDBLAS_FN_clblasZscal, clblasStatus, (size_t p1, cl_double2 p2, cl_mem p3, size_t p4, int p5, cl_uint p6, cl_command_queue* p7, cl_uint p8, const cl_event* p9, cl_event* p10))
//clblasStatus (*clblasZscal)(size_t, cl_double2, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZscal_switch_fn;
//static const struct DynamicFnEntry clblasZscal_definition = { "clblasZscal", (void**)&clblasZscal};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasZswap, clblasStatus, (size_t p1, cl_mem p2, size_t p3, int p4, cl_mem p5, size_t p6, int p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasZswap)(size_t, cl_mem, size_t, int, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZswap_switch_fn;
//static const struct DynamicFnEntry clblasZswap_definition = { "clblasZswap", (void**)&clblasZswap};

//openclamdblas_fn21(OPENCLAMDBLAS_FN_clblasZsymm, clblasStatus, (clblasOrder p1, clblasSide p2, clblasUplo p3, size_t p4, size_t p5, cl_double2 p6, const cl_mem p7, size_t p8, size_t p9, const cl_mem p10, size_t p11, size_t p12, cl_double2 p13, cl_mem p14, size_t p15, size_t p16, cl_uint p17, cl_command_queue* p18, cl_uint p19, const cl_event* p20, cl_event* p21))
//clblasStatus (*clblasZsymm)(clblasOrder, clblasSide, clblasUplo, size_t, size_t, cl_double2, const cl_mem, size_t, size_t, const cl_mem, size_t, size_t, cl_double2, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZsymm_switch_fn;
//static const struct DynamicFnEntry clblasZsymm_definition = { "clblasZsymm", (void**)&clblasZsymm};

//openclamdblas_fn21(OPENCLAMDBLAS_FN_clblasZsyr2k, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, size_t p4, size_t p5, DoubleComplex p6, const cl_mem p7, size_t p8, size_t p9, const cl_mem p10, size_t p11, size_t p12, DoubleComplex p13, cl_mem p14, size_t p15, size_t p16, cl_uint p17, cl_command_queue* p18, cl_uint p19, const cl_event* p20, cl_event* p21))
//clblasStatus (*clblasZsyr2k)(clblasOrder, clblasUplo, clblasTranspose, size_t, size_t, DoubleComplex, const cl_mem, size_t, size_t, const cl_mem, size_t, size_t, DoubleComplex, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZsyr2k_switch_fn;
//static const struct DynamicFnEntry clblasZsyr2k_definition = { "clblasZsyr2k", (void**)&clblasZsyr2k};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasZsyrk, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, size_t p4, size_t p5, DoubleComplex p6, const cl_mem p7, size_t p8, size_t p9, DoubleComplex p10, cl_mem p11, size_t p12, size_t p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasZsyrk)(clblasOrder, clblasUplo, clblasTranspose, size_t, size_t, DoubleComplex, const cl_mem, size_t, size_t, DoubleComplex, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZsyrk_switch_fn;
//static const struct DynamicFnEntry clblasZsyrk_definition = { "clblasZsyrk", (void**)&clblasZsyrk};

//openclamdblas_fn18(OPENCLAMDBLAS_FN_clblasZtbmv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, size_t p6, const cl_mem p7, size_t p8, size_t p9, cl_mem p10, size_t p11, int p12, cl_mem p13, cl_uint p14, cl_command_queue* p15, cl_uint p16, const cl_event* p17, cl_event* p18))
//clblasStatus (*clblasZtbmv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, size_t, const cl_mem, size_t, size_t, cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZtbmv_switch_fn;
//static const struct DynamicFnEntry clblasZtbmv_definition = { "clblasZtbmv", (void**)&clblasZtbmv};

//openclamdblas_fn17(OPENCLAMDBLAS_FN_clblasZtbsv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, size_t p6, const cl_mem p7, size_t p8, size_t p9, cl_mem p10, size_t p11, int p12, cl_uint p13, cl_command_queue* p14, cl_uint p15, const cl_event* p16, cl_event* p17))
//clblasStatus (*clblasZtbsv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, size_t, const cl_mem, size_t, size_t, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZtbsv_switch_fn;
//static const struct DynamicFnEntry clblasZtbsv_definition = { "clblasZtbsv", (void**)&clblasZtbsv};

//openclamdblas_fn16(OPENCLAMDBLAS_FN_clblasZtpmv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, const cl_mem p6, size_t p7, cl_mem p8, size_t p9, int p10, cl_mem p11, cl_uint p12, cl_command_queue* p13, cl_uint p14, const cl_event* p15, cl_event* p16))
//clblasStatus (*clblasZtpmv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, const cl_mem, size_t, cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZtpmv_switch_fn;
//static const struct DynamicFnEntry clblasZtpmv_definition = { "clblasZtpmv", (void**)&clblasZtpmv};

//openclamdblas_fn15(OPENCLAMDBLAS_FN_clblasZtpsv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, const cl_mem p6, size_t p7, cl_mem p8, size_t p9, int p10, cl_uint p11, cl_command_queue* p12, cl_uint p13, const cl_event* p14, cl_event* p15))
//clblasStatus (*clblasZtpsv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, const cl_mem, size_t, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZtpsv_switch_fn;
//static const struct DynamicFnEntry clblasZtpsv_definition = { "clblasZtpsv", (void**)&clblasZtpsv};

//openclamdblas_fn19(OPENCLAMDBLAS_FN_clblasZtrmm, clblasStatus, (clblasOrder p1, clblasSide p2, clblasUplo p3, clblasTranspose p4, clblasDiag p5, size_t p6, size_t p7, DoubleComplex p8, const cl_mem p9, size_t p10, size_t p11, cl_mem p12, size_t p13, size_t p14, cl_uint p15, cl_command_queue* p16, cl_uint p17, const cl_event* p18, cl_event* p19))
//clblasStatus (*clblasZtrmm)(clblasOrder, clblasSide, clblasUplo, clblasTranspose, clblasDiag, size_t, size_t, DoubleComplex, const cl_mem, size_t, size_t, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZtrmm_switch_fn;
//static const struct DynamicFnEntry clblasZtrmm_definition = { "clblasZtrmm", (void**)&clblasZtrmm};

//openclamdblas_fn17(OPENCLAMDBLAS_FN_clblasZtrmv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, const cl_mem p6, size_t p7, size_t p8, cl_mem p9, size_t p10, int p11, cl_mem p12, cl_uint p13, cl_command_queue* p14, cl_uint p15, const cl_event* p16, cl_event* p17))
//clblasStatus (*clblasZtrmv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, const cl_mem, size_t, size_t, cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZtrmv_switch_fn;
//static const struct DynamicFnEntry clblasZtrmv_definition = { "clblasZtrmv", (void**)&clblasZtrmv};

//openclamdblas_fn19(OPENCLAMDBLAS_FN_clblasZtrsm, clblasStatus, (clblasOrder p1, clblasSide p2, clblasUplo p3, clblasTranspose p4, clblasDiag p5, size_t p6, size_t p7, DoubleComplex p8, const cl_mem p9, size_t p10, size_t p11, cl_mem p12, size_t p13, size_t p14, cl_uint p15, cl_command_queue* p16, cl_uint p17, const cl_event* p18, cl_event* p19))
//clblasStatus (*clblasZtrsm)(clblasOrder, clblasSide, clblasUplo, clblasTranspose, clblasDiag, size_t, size_t, DoubleComplex, const cl_mem, size_t, size_t, cl_mem, size_t, size_t, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZtrsm_switch_fn;
//static const struct DynamicFnEntry clblasZtrsm_definition = { "clblasZtrsm", (void**)&clblasZtrsm};

//openclamdblas_fn16(OPENCLAMDBLAS_FN_clblasZtrsv, clblasStatus, (clblasOrder p1, clblasUplo p2, clblasTranspose p3, clblasDiag p4, size_t p5, const cl_mem p6, size_t p7, size_t p8, cl_mem p9, size_t p10, int p11, cl_uint p12, cl_command_queue* p13, cl_uint p14, const cl_event* p15, cl_event* p16))
//clblasStatus (*clblasZtrsv)(clblasOrder, clblasUplo, clblasTranspose, clblasDiag, size_t, const cl_mem, size_t, size_t, cl_mem, size_t, int, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasZtrsv_switch_fn;
//static const struct DynamicFnEntry clblasZtrsv_definition = { "clblasZtrsv", (void**)&clblasZtrsv};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasiCamax, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, cl_mem p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasiCamax)(size_t, cl_mem, size_t, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasiCamax_switch_fn;
//static const struct DynamicFnEntry clblasiCamax_definition = { "clblasiCamax", (void**)&clblasiCamax};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasiDamax, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, cl_mem p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasiDamax)(size_t, cl_mem, size_t, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasiDamax_switch_fn;
//static const struct DynamicFnEntry clblasiDamax_definition = { "clblasiDamax", (void**)&clblasiDamax};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasiSamax, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, cl_mem p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasiSamax)(size_t, cl_mem, size_t, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasiSamax_switch_fn;
//static const struct DynamicFnEntry clblasiSamax_definition = { "clblasiSamax", (void**)&clblasiSamax};

//openclamdblas_fn12(OPENCLAMDBLAS_FN_clblasiZamax, clblasStatus, (size_t p1, cl_mem p2, size_t p3, const cl_mem p4, size_t p5, int p6, cl_mem p7, cl_uint p8, cl_command_queue* p9, cl_uint p10, const cl_event* p11, cl_event* p12))
//clblasStatus (*clblasiZamax)(size_t, cl_mem, size_t, const cl_mem, size_t, int, cl_mem, cl_uint, cl_command_queue*, cl_uint, const cl_event*, cl_event*) =
//        OPENCLAMDBLAS_FN_clblasiZamax_switch_fn;
//static const struct DynamicFnEntry clblasiZamax_definition = { "clblasiZamax", (void**)&clblasiZamax};


// generated by parser_clblas.py
static const struct DynamicFnEntry* openclamdblas_fn[] = {
    NULL/*&clblasCaxpy_definition*/,
    NULL/*&clblasCcopy_definition*/,
    NULL/*&clblasCdotc_definition*/,
    NULL/*&clblasCdotu_definition*/,
    NULL/*&clblasCgbmv_definition*/,
    &clblasCgemm_definition,
    NULL/*&clblasCgemv_definition*/,
    NULL/*&clblasCgerc_definition*/,
    NULL/*&clblasCgeru_definition*/,
    NULL/*&clblasChbmv_definition*/,
    NULL/*&clblasChemm_definition*/,
    NULL/*&clblasChemv_definition*/,
    NULL/*&clblasCher_definition*/,
    NULL/*&clblasCher2_definition*/,
    NULL/*&clblasCher2k_definition*/,
    NULL/*&clblasCherk_definition*/,
    NULL/*&clblasChpmv_definition*/,
    NULL/*&clblasChpr_definition*/,
    NULL/*&clblasChpr2_definition*/,
    NULL/*&clblasCrotg_definition*/,
    NULL/*&clblasCscal_definition*/,
    NULL/*&clblasCsrot_definition*/,
    NULL/*&clblasCsscal_definition*/,
    NULL/*&clblasCswap_definition*/,
    NULL/*&clblasCsymm_definition*/,
    NULL/*&clblasCsyr2k_definition*/,
    NULL/*&clblasCsyrk_definition*/,
    NULL/*&clblasCtbmv_definition*/,
    NULL/*&clblasCtbsv_definition*/,
    NULL/*&clblasCtpmv_definition*/,
    NULL/*&clblasCtpsv_definition*/,
    NULL/*&clblasCtrmm_definition*/,
    NULL/*&clblasCtrmv_definition*/,
    NULL/*&clblasCtrsm_definition*/,
    NULL/*&clblasCtrsv_definition*/,
    NULL/*&clblasDasum_definition*/,
    NULL/*&clblasDaxpy_definition*/,
    NULL/*&clblasDcopy_definition*/,
    NULL/*&clblasDdot_definition*/,
    NULL/*&clblasDgbmv_definition*/,
    &clblasDgemm_definition,
    NULL/*&clblasDgemv_definition*/,
    NULL/*&clblasDger_definition*/,
    NULL/*&clblasDnrm2_definition*/,
    NULL/*&clblasDrot_definition*/,
    NULL/*&clblasDrotg_definition*/,
    NULL/*&clblasDrotm_definition*/,
    NULL/*&clblasDrotmg_definition*/,
    NULL/*&clblasDsbmv_definition*/,
    NULL/*&clblasDscal_definition*/,
    NULL/*&clblasDspmv_definition*/,
    NULL/*&clblasDspr_definition*/,
    NULL/*&clblasDspr2_definition*/,
    NULL/*&clblasDswap_definition*/,
    NULL/*&clblasDsymm_definition*/,
    NULL/*&clblasDsymv_definition*/,
    NULL/*&clblasDsyr_definition*/,
    NULL/*&clblasDsyr2_definition*/,
    NULL/*&clblasDsyr2k_definition*/,
    NULL/*&clblasDsyrk_definition*/,
    NULL/*&clblasDtbmv_definition*/,
    NULL/*&clblasDtbsv_definition*/,
    NULL/*&clblasDtpmv_definition*/,
    NULL/*&clblasDtpsv_definition*/,
    NULL/*&clblasDtrmm_definition*/,
    NULL/*&clblasDtrmv_definition*/,
    NULL/*&clblasDtrsm_definition*/,
    NULL/*&clblasDtrsv_definition*/,
    NULL/*&clblasDzasum_definition*/,
    NULL/*&clblasDznrm2_definition*/,
    NULL/*&clblasGetVersion_definition*/,
    NULL/*&clblasSasum_definition*/,
    NULL/*&clblasSaxpy_definition*/,
    NULL/*&clblasScasum_definition*/,
    NULL/*&clblasScnrm2_definition*/,
    NULL/*&clblasScopy_definition*/,
    NULL/*&clblasSdot_definition*/,
    &clblasSetup_definition,
    NULL/*&clblasSgbmv_definition*/,
    &clblasSgemm_definition,
    NULL/*&clblasSgemv_definition*/,
    NULL/*&clblasSger_definition*/,
    NULL/*&clblasSnrm2_definition*/,
    NULL/*&clblasSrot_definition*/,
    NULL/*&clblasSrotg_definition*/,
    NULL/*&clblasSrotm_definition*/,
    NULL/*&clblasSrotmg_definition*/,
    NULL/*&clblasSsbmv_definition*/,
    NULL/*&clblasSscal_definition*/,
    NULL/*&clblasSspmv_definition*/,
    NULL/*&clblasSspr_definition*/,
    NULL/*&clblasSspr2_definition*/,
    NULL/*&clblasSswap_definition*/,
    NULL/*&clblasSsymm_definition*/,
    NULL/*&clblasSsymv_definition*/,
    NULL/*&clblasSsyr_definition*/,
    NULL/*&clblasSsyr2_definition*/,
    NULL/*&clblasSsyr2k_definition*/,
    NULL/*&clblasSsyrk_definition*/,
    NULL/*&clblasStbmv_definition*/,
    NULL/*&clblasStbsv_definition*/,
    NULL/*&clblasStpmv_definition*/,
    NULL/*&clblasStpsv_definition*/,
    NULL/*&clblasStrmm_definition*/,
    NULL/*&clblasStrmv_definition*/,
    NULL/*&clblasStrsm_definition*/,
    NULL/*&clblasStrsv_definition*/,
    &clblasTeardown_definition,
    NULL/*&clblasZaxpy_definition*/,
    NULL/*&clblasZcopy_definition*/,
    NULL/*&clblasZdotc_definition*/,
    NULL/*&clblasZdotu_definition*/,
    NULL/*&clblasZdrot_definition*/,
    NULL/*&clblasZdscal_definition*/,
    NULL/*&clblasZgbmv_definition*/,
    &clblasZgemm_definition,
    NULL/*&clblasZgemv_definition*/,
    NULL/*&clblasZgerc_definition*/,
    NULL/*&clblasZgeru_definition*/,
    NULL/*&clblasZhbmv_definition*/,
    NULL/*&clblasZhemm_definition*/,
    NULL/*&clblasZhemv_definition*/,
    NULL/*&clblasZher_definition*/,
    NULL/*&clblasZher2_definition*/,
    NULL/*&clblasZher2k_definition*/,
    NULL/*&clblasZherk_definition*/,
    NULL/*&clblasZhpmv_definition*/,
    NULL/*&clblasZhpr_definition*/,
    NULL/*&clblasZhpr2_definition*/,
    NULL/*&clblasZrotg_definition*/,
    NULL/*&clblasZscal_definition*/,
    NULL/*&clblasZswap_definition*/,
    NULL/*&clblasZsymm_definition*/,
    NULL/*&clblasZsyr2k_definition*/,
    NULL/*&clblasZsyrk_definition*/,
    NULL/*&clblasZtbmv_definition*/,
    NULL/*&clblasZtbsv_definition*/,
    NULL/*&clblasZtpmv_definition*/,
    NULL/*&clblasZtpsv_definition*/,
    NULL/*&clblasZtrmm_definition*/,
    NULL/*&clblasZtrmv_definition*/,
    NULL/*&clblasZtrsm_definition*/,
    NULL/*&clblasZtrsv_definition*/,
    NULL/*&clblasiCamax_definition*/,
    NULL/*&clblasiDamax_definition*/,
    NULL/*&clblasiSamax_definition*/,
    NULL/*&clblasiZamax_definition*/,
};

// number of enabled functions: 6
