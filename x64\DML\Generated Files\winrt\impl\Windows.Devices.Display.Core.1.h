// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Devices_Display_Core_1_H
#define WINRT_Windows_Devices_Display_Core_1_H
#include "winrt/impl/Windows.Devices.Display.Core.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices::Display::Core
{
    struct WINRT_IMPL_EMPTY_BASES IDisplayAdapter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayAdapter>
    {
        IDisplayAdapter(std::nullptr_t = nullptr) noexcept {}
        IDisplayAdapter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayAdapter2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayAdapter2>
    {
        IDisplayAdapter2(std::nullptr_t = nullptr) noexcept {}
        IDisplayAdapter2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayAdapterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayAdapterStatics>
    {
        IDisplayAdapterStatics(std::nullptr_t = nullptr) noexcept {}
        IDisplayAdapterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayDevice :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayDevice>
    {
        IDisplayDevice(std::nullptr_t = nullptr) noexcept {}
        IDisplayDevice(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayDevice2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayDevice2>
    {
        IDisplayDevice2(std::nullptr_t = nullptr) noexcept {}
        IDisplayDevice2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayDeviceRenderAdapter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayDeviceRenderAdapter>
    {
        IDisplayDeviceRenderAdapter(std::nullptr_t = nullptr) noexcept {}
        IDisplayDeviceRenderAdapter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayFence :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayFence>
    {
        IDisplayFence(std::nullptr_t = nullptr) noexcept {}
        IDisplayFence(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayManager>
    {
        IDisplayManager(std::nullptr_t = nullptr) noexcept {}
        IDisplayManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayManager2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayManager2>
    {
        IDisplayManager2(std::nullptr_t = nullptr) noexcept {}
        IDisplayManager2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayManager3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayManager3>
    {
        IDisplayManager3(std::nullptr_t = nullptr) noexcept {}
        IDisplayManager3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayManagerChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayManagerChangedEventArgs>
    {
        IDisplayManagerChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDisplayManagerChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayManagerDisabledEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayManagerDisabledEventArgs>
    {
        IDisplayManagerDisabledEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDisplayManagerDisabledEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayManagerEnabledEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayManagerEnabledEventArgs>
    {
        IDisplayManagerEnabledEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDisplayManagerEnabledEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayManagerPathsFailedOrInvalidatedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayManagerPathsFailedOrInvalidatedEventArgs>
    {
        IDisplayManagerPathsFailedOrInvalidatedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDisplayManagerPathsFailedOrInvalidatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayManagerResultWithState :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayManagerResultWithState>
    {
        IDisplayManagerResultWithState(std::nullptr_t = nullptr) noexcept {}
        IDisplayManagerResultWithState(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayManagerStatics>
    {
        IDisplayManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IDisplayManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayModeInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayModeInfo>
    {
        IDisplayModeInfo(std::nullptr_t = nullptr) noexcept {}
        IDisplayModeInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayModeInfo2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayModeInfo2>
    {
        IDisplayModeInfo2(std::nullptr_t = nullptr) noexcept {}
        IDisplayModeInfo2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayMuxDevice :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayMuxDevice>
    {
        IDisplayMuxDevice(std::nullptr_t = nullptr) noexcept {}
        IDisplayMuxDevice(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayMuxDeviceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayMuxDeviceStatics>
    {
        IDisplayMuxDeviceStatics(std::nullptr_t = nullptr) noexcept {}
        IDisplayMuxDeviceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayPath :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayPath>
    {
        IDisplayPath(std::nullptr_t = nullptr) noexcept {}
        IDisplayPath(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayPath2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayPath2>
    {
        IDisplayPath2(std::nullptr_t = nullptr) noexcept {}
        IDisplayPath2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayPrimaryDescription :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayPrimaryDescription>
    {
        IDisplayPrimaryDescription(std::nullptr_t = nullptr) noexcept {}
        IDisplayPrimaryDescription(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayPrimaryDescriptionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayPrimaryDescriptionFactory>
    {
        IDisplayPrimaryDescriptionFactory(std::nullptr_t = nullptr) noexcept {}
        IDisplayPrimaryDescriptionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayPrimaryDescriptionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayPrimaryDescriptionStatics>
    {
        IDisplayPrimaryDescriptionStatics(std::nullptr_t = nullptr) noexcept {}
        IDisplayPrimaryDescriptionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayScanout :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayScanout>
    {
        IDisplayScanout(std::nullptr_t = nullptr) noexcept {}
        IDisplayScanout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplaySource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplaySource>
    {
        IDisplaySource(std::nullptr_t = nullptr) noexcept {}
        IDisplaySource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplaySource2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplaySource2>
    {
        IDisplaySource2(std::nullptr_t = nullptr) noexcept {}
        IDisplaySource2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayState :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayState>
    {
        IDisplayState(std::nullptr_t = nullptr) noexcept {}
        IDisplayState(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayStateOperationResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayStateOperationResult>
    {
        IDisplayStateOperationResult(std::nullptr_t = nullptr) noexcept {}
        IDisplayStateOperationResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplaySurface :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplaySurface>
    {
        IDisplaySurface(std::nullptr_t = nullptr) noexcept {}
        IDisplaySurface(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayTarget :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayTarget>
    {
        IDisplayTarget(std::nullptr_t = nullptr) noexcept {}
        IDisplayTarget(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayTask :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayTask>
    {
        IDisplayTask(std::nullptr_t = nullptr) noexcept {}
        IDisplayTask(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayTask2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayTask2>
    {
        IDisplayTask2(std::nullptr_t = nullptr) noexcept {}
        IDisplayTask2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayTaskPool :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayTaskPool>
    {
        IDisplayTaskPool(std::nullptr_t = nullptr) noexcept {}
        IDisplayTaskPool(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayTaskPool2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayTaskPool2>
    {
        IDisplayTaskPool2(std::nullptr_t = nullptr) noexcept {}
        IDisplayTaskPool2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayTaskResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayTaskResult>
    {
        IDisplayTaskResult(std::nullptr_t = nullptr) noexcept {}
        IDisplayTaskResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayView>
    {
        IDisplayView(std::nullptr_t = nullptr) noexcept {}
        IDisplayView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayWireFormat :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayWireFormat>
    {
        IDisplayWireFormat(std::nullptr_t = nullptr) noexcept {}
        IDisplayWireFormat(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayWireFormatFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayWireFormatFactory>
    {
        IDisplayWireFormatFactory(std::nullptr_t = nullptr) noexcept {}
        IDisplayWireFormatFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDisplayWireFormatStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDisplayWireFormatStatics>
    {
        IDisplayWireFormatStatics(std::nullptr_t = nullptr) noexcept {}
        IDisplayWireFormatStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
