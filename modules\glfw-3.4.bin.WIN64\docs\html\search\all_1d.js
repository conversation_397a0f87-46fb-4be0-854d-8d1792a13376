var searchData=
[
  ['w64_20and_20glfw_20binaries_0',['With MinGW-w64 and GLFW binaries',['../build_guide.html#build_link_mingw',1,'']]],
  ['wayland_20and_20x11_1',['Dependencies for Wayland and X11',['../compile_guide.html#compile_deps_wayland',1,'']]],
  ['wayland_20framebuffer_20may_20lack_20alpha_20channel_20on_20older_20systems_2',['Wayland framebuffer may lack alpha channel on older systems',['../news.html#wayland_alpha_caveat',1,'']]],
  ['wayland_20libdecor_20decorations_3',['Wayland libdecor decorations',['../news.html#wayland_libdecor_decorations',1,'']]],
  ['wayland_20protocols_20and_20ipc_20standards_4',['Wayland protocols and IPC standards',['../compat_guide.html#compat_wayland',1,'']]],
  ['wayland_20specific_20init_20hints_5',['Wayland specific init hints',['../intro_guide.html#init_hints_wayland',1,'']]],
  ['wayland_20specific_20window_20hints_6',['Wayland specific window hints',['../window_guide.html#window_hints_wayland',1,'']]],
  ['wayland_20surface_20app_5fid_20hint_7',['Wayland surface app_id hint',['../news.html#wayland_app_id_hint',1,'']]],
  ['wgl_20extensions_8',['WGL extensions',['../compat_guide.html#compat_wgl',1,'']]],
  ['wheel_20position_20replaced_20by_20scroll_20offsets_9',['Wheel position replaced by scroll offsets',['../moving_guide.html#moving_wheel',1,'']]],
  ['when_20built_20as_20a_20subproject_10',['Tests and examples are disabled when built as a subproject',['../news.html#standalone_caveat',1,'']]],
  ['wide_20hotkeys_11',['Capture of system-wide hotkeys',['../moving_guide.html#moving_syskeys',1,'']]],
  ['width_12',['width',['../struct_g_l_f_wvidmode.html#a698dcb200562051a7249cb6ae154c71d',1,'GLFWvidmode::width'],['../struct_g_l_f_wimage.html#af6a71cc999fe6d3aea31dd7e9687d835',1,'GLFWimage::width']]],
  ['win32_20mbcs_20support_13',['Win32 MBCS support',['../moving_guide.html#moving_mbcs',1,'']]],
  ['win32_20specific_20cmake_20options_14',['Win32 specific CMake options',['../compile_guide.html#compile_options_win32',1,'']]],
  ['win32_20specific_20hints_15',['Win32 specific hints',['../window_guide.html#window_hints_win32',1,'']]],
  ['window_16',['Creating the window',['../vulkan_guide.html#vulkan_window',1,'']]],
  ['window_20and_20context_17',['Creating a window and context',['../quick_guide.html#quick_create_window',1,'']]],
  ['window_20and_20framebuffer_20sizes_18',['Separation of window and framebuffer sizes',['../moving_guide.html#moving_hidpi',1,'']]],
  ['window_20attention_20request_19',['Window attention request',['../window_guide.html#window_attention',1,'']]],
  ['window_20attributes_20',['Window attributes',['../window_guide.html#window_attribs',1,'']]],
  ['window_20close_20flag_21',['Checking the window close flag',['../quick_guide.html#quick_window_close',1,'']]],
  ['window_20closing_20and_20close_20flag_22',['Window closing and close flag',['../window_guide.html#window_close',1,'']]],
  ['window_20closing_20changes_23',['Window closing changes',['../moving_guide.html#moving_window_close',1,'']]],
  ['window_20content_20scale_24',['Window content scale',['../window_guide.html#window_scale',1,'']]],
  ['window_20creation_25',['Window creation',['../window_guide.html#window_creation',1,'']]],
  ['window_20creation_20hints_26',['Window creation hints',['../window_guide.html#window_hints',1,'']]],
  ['window_20damage_20and_20refresh_27',['Window damage and refresh',['../window_guide.html#window_refresh',1,'']]],
  ['window_20destruction_28',['Window destruction',['../window_guide.html#window_destruction',1,'']]],
  ['window_20event_20processing_29',['Window event processing',['../window_guide.html#window_events',1,'']]],
  ['window_20guide_30',['Window guide',['../window_guide.html',1,'']]],
  ['window_20handle_20parameters_31',['Window handle parameters',['../moving_guide.html#moving_window_handles',1,'']]],
  ['window_20hint_20for_20framebuffer_20scaling_32',['Window hint for framebuffer scaling',['../news.html#scale_framebuffer_hint',1,'']]],
  ['window_20hints_33',['window hints',['../moving_guide.html#moving_hints',1,'Persistent window hints'],['../window_guide.html#window_hints_wayland',1,'Wayland specific window hints'],['../window_guide.html#window_hints_x11',1,'X11 specific window hints']]],
  ['window_20hints_20for_20initial_20window_20position_34',['Window hints for initial window position',['../news.html#window_position_hint',1,'']]],
  ['window_20icon_35',['Window icon',['../window_guide.html#window_icon',1,'']]],
  ['window_20iconification_36',['Window iconification',['../window_guide.html#window_iconify',1,'']]],
  ['window_20input_20focus_37',['Window input focus',['../window_guide.html#window_focus',1,'']]],
  ['window_20maximization_38',['Window maximization',['../window_guide.html#window_maximize',1,'']]],
  ['window_20menu_20keyboard_20access_20hint_39',['Windows window menu keyboard access hint',['../news.html#win32_keymenu_hint',1,'']]],
  ['window_20monitor_40',['Window monitor',['../window_guide.html#window_monitor',1,'']]],
  ['window_20objects_41',['Window objects',['../window_guide.html#window_object',1,'']]],
  ['window_20position_42',['window position',['../news.html#window_position_hint',1,'Window hints for initial window position'],['../window_guide.html#window_pos',1,'Window position']]],
  ['window_20properties_20and_20events_43',['Window properties and events',['../window_guide.html#window_properties',1,'']]],
  ['window_20reference_44',['Window reference',['../group__window.html',1,'']]],
  ['window_20related_20attributes_45',['Window related attributes',['../window_guide.html#window_attribs_wnd',1,'']]],
  ['window_20related_20hints_46',['Window related hints',['../window_guide.html#window_hints_wnd',1,'']]],
  ['window_20size_47',['Window size',['../window_guide.html#window_size',1,'']]],
  ['window_20size_20limits_48',['Window size limits',['../window_guide.html#window_sizelimits',1,'']]],
  ['window_20surface_49',['Creating a Vulkan window surface',['../vulkan_guide.html#vulkan_surface',1,'']]],
  ['window_20surface_20hint_50',['X11 Vulkan window surface hint',['../news.html#x11_xcb_vulkan_surface',1,'']]],
  ['window_20title_51',['window title',['../news.html#window_title_function',1,'Ability to get window title'],['../window_guide.html#window_title',1,'Window title']]],
  ['window_20transparency_52',['Window transparency',['../window_guide.html#window_transparency',1,'']]],
  ['window_20visibility_53',['Window visibility',['../window_guide.html#window_hide',1,'']]],
  ['window_2emd_54',['window.md',['../window_8md.html',1,'']]],
  ['windowed_20full_20screen_20windows_55',['&quot;Windowed full screen&quot; windows',['../window_guide.html#window_windowed_full_screen',1,'']]],
  ['windows_56',['windows',['../window_guide.html#window_windowed_full_screen',1,'&quot;Windowed full screen&quot; windows'],['../window_guide.html#window_full_screen',1,'Full screen windows']]],
  ['windows_207_20framebuffer_20transparency_20requires_20dwm_20transparency_57',['Windows 7 framebuffer transparency requires DWM transparency',['../news.html#win7_framebuffer_caveat',1,'']]],
  ['windows_20older_20than_20xp_58',['Support for versions of Windows older than XP',['../moving_guide.html#moving_windows',1,'']]],
  ['windows_20startupinfo_20show_20command_20hint_59',['Windows STARTUPINFO show command hint',['../news.html#win32_showdefault_hint',1,'']]],
  ['windows_20window_20menu_20keyboard_20access_20hint_60',['Windows window menu keyboard access hint',['../news.html#win32_keymenu_hint',1,'']]],
  ['windows_20without_20contexts_61',['Windows without contexts',['../context_guide.html#context_less',1,'']]],
  ['windows_20xp_20and_20vista_20support_20is_20deprecated_62',['Windows XP and Vista support is deprecated',['../news.html#winxp_deprecated',1,'']]],
  ['with_20a_20loader_20library_63',['Loading extension with a loader library',['../context_guide.html#context_glext_auto',1,'']]],
  ['with_20cmake_64',['Generating build files with CMake',['../compile_guide.html#compile_generate',1,'']]],
  ['with_20cmake_20and_20glfw_20source_65',['With CMake and GLFW source',['../build_guide.html#build_link_cmake_source',1,'']]],
  ['with_20cmake_20and_20installed_20glfw_20binaries_66',['With CMake and installed GLFW binaries',['../build_guide.html#build_link_cmake_package',1,'']]],
  ['with_20cmake_20and_20mingw_67',['Cross-compilation with CMake and MinGW',['../compile_guide.html#compile_mingw_cross',1,'']]],
  ['with_20command_20line_20cmake_68',['Generating with command-line CMake',['../compile_guide.html#compile_generate_cli',1,'']]],
  ['with_20command_20line_20or_20makefile_20on_20macos_69',['With command-line or makefile on macOS',['../build_guide.html#build_link_osx',1,'']]],
  ['with_20mingw_20w64_20and_20glfw_20binaries_70',['With MinGW-w64 and GLFW binaries',['../build_guide.html#build_link_mingw',1,'']]],
  ['with_20opengl_71',['Rendering with OpenGL',['../quick_guide.html#quick_render',1,'']]],
  ['with_20pkg_20config_20and_20glfw_20binaries_20on_20unix_72',['With pkg-config and GLFW binaries on Unix',['../build_guide.html#build_link_pkgconfig',1,'']]],
  ['with_20the_20cmake_20gui_73',['Generating with the CMake GUI',['../compile_guide.html#compile_generate_gui',1,'']]],
  ['with_20the_20right_20libraries_74',['Link with the right libraries',['../build_guide.html#build_link',1,'']]],
  ['with_20visual_20c_20and_20glfw_20binaries_75',['With Visual C++ and GLFW binaries',['../build_guide.html#build_link_win32',1,'']]],
  ['with_20xcode_20on_20macos_76',['With Xcode on macOS',['../build_guide.html#build_link_xcode',1,'']]],
  ['without_20contexts_77',['Windows without contexts',['../context_guide.html#context_less',1,'']]],
  ['wl_5fshell_20protocol_20support_20has_20been_20removed_78',['wl_shell protocol support has been removed',['../news.html#wl_shell_removed',1,'']]],
  ['work_20area_79',['Work area',['../monitor_guide.html#monitor_workarea',1,'']]],
  ['wsi_20extensions_80',['Vulkan WSI extensions',['../compat_guide.html#compat_wsi',1,'']]]
];
