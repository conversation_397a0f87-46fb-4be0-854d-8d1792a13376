{"name": "opencv_js_tests", "description": "Tests for opencv js bindings", "version": "1.0.1", "dependencies": {"ansi-colors": "^4.1.1", "cli-table": "0.3.6", "minimist": "^1.2.0", "node-qunit": "latest"}, "devDependencies": {"eslint": "latest", "eslint-config-google": "latest"}, "scripts": {"test": "node tests.js"}, "repository": {"type": "git", "url": "https://github.com/opencv/opencv.git"}, "keywords": [], "author": "", "license": "Apache 2.0 License", "bugs": {"url": "https://github.com/opencv/opencv/issues"}, "homepage": "https://github.com/opencv/opencv"}