﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\photo\test\ocl\test_denoising.cpp">
      <Filter>opencv_photo\Src\ocl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\photo\test\test_cloning.cpp">
      <Filter>opencv_photo\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\photo\test\test_decolor.cpp">
      <Filter>opencv_photo\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\photo\test\test_denoise_tvl1.cpp">
      <Filter>opencv_photo\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\photo\test\test_denoising.cpp">
      <Filter>opencv_photo\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\photo\test\test_denoising.cuda.cpp">
      <Filter>opencv_photo\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\photo\test\test_hdr.cpp">
      <Filter>opencv_photo\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\photo\test\test_inpaint.cpp">
      <Filter>opencv_photo\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\photo\test\test_main.cpp">
      <Filter>opencv_photo\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\photo\test\test_npr.cpp">
      <Filter>opencv_photo\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\photo\test\test_precomp.hpp">
      <Filter>opencv_photo\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_photo">
      <UniqueIdentifier>{2C732EE1-21A3-36ED-A724-4D4727D6341E}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_photo\Include">
      <UniqueIdentifier>{A84FC73B-9E11-3AEB-9546-19CB1FB571CB}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_photo\Src">
      <UniqueIdentifier>{9BD3676C-F5B2-3833-A49B-A2050BA96172}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_photo\Src\ocl">
      <UniqueIdentifier>{38993F31-4957-3A3D-9E28-C48E56E89E14}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
