PORTED FUNCs LIST (213 of 213):

 void cv::ximgproc::niBlackThreshold(Mat _src, Mat& _dst, double maxValue, int type, int blockSize, double k, LocalBinarizationMethods binarizationMethod = BINARIZATION_NIBLACK, double r = 128)
 void cv::ximgproc::thinning(Mat src, Mat& dst, ThinningTypes thinningType = THINNING_ZHANGSUEN)
 void cv::ximgproc::anisotropicDiffusion(Mat src, Mat& dst, float alpha, float K, int niters)
 void cv::ximgproc::createQuaternionImage(Mat img, Mat& qimg)
 void cv::ximgproc::qconj(Mat qimg, Mat& qcimg)
 void cv::ximgproc::qunitary(Mat qimg, Mat& qnimg)
 void cv::ximgproc::qmultiply(Mat src1, <PERSON> src2, Mat& dst)
 void cv::ximgproc::qdft(Mat img, Mat& qimg, int flags, bool sideLeft)
 void cv::ximgproc::colorMatchTemplate(Mat img, <PERSON> templ, <PERSON>& result)
 void cv::ximgproc::GradientDericheY(Mat op, Mat& dst, double alpha, double omega)
 void cv::ximgproc::GradientDericheX(Mat op, Mat& dst, double alpha, double omega)
 Ptr_DisparityWLSFilter cv::ximgproc::createDisparityWLSFilter(Ptr_StereoMatcher matcher_left)
 Ptr_StereoMatcher cv::ximgproc::createRightMatcher(Ptr_StereoMatcher matcher_left)
 Ptr_DisparityWLSFilter cv::ximgproc::createDisparityWLSFilterGeneric(bool use_confidence)
 int cv::ximgproc::readGT(String src_path, Mat& dst)
 double cv::ximgproc::computeMSE(Mat GT, Mat src, Rect ROI)
 double cv::ximgproc::computeBadPixelPercent(Mat GT, Mat src, Rect ROI, int thresh = 24)
 void cv::ximgproc::getDisparityVis(Mat src, Mat& dst, double scale = 1.0)
 Ptr_EdgeBoxes cv::ximgproc::createEdgeBoxes(float alpha = 0.65f, float beta = 0.75f, float eta = 1, float minScore = 0.01f, int maxBoxes = 10000, float edgeMinMag = 0.1f, float edgeMergeThr = 0.5f, float clusterMinMag = 0.5f, float maxAspectRatio = 3, float minBoxArea = 1000, float gamma = 2, float kappa = 1.5f)
 void cv::ximgproc::edgePreservingFilter(Mat src, Mat& dst, int d, double threshold)
 Ptr_EdgeDrawing cv::ximgproc::createEdgeDrawing()
 Ptr_DTFilter cv::ximgproc::createDTFilter(Mat guide, double sigmaSpatial, double sigmaColor, EdgeAwareFiltersList mode = DTF_NC, int numIters = 3)
 void cv::ximgproc::dtFilter(Mat guide, Mat src, Mat& dst, double sigmaSpatial, double sigmaColor, EdgeAwareFiltersList mode = DTF_NC, int numIters = 3)
 Ptr_GuidedFilter cv::ximgproc::createGuidedFilter(Mat guide, int radius, double eps, double scale = 1.0)
 void cv::ximgproc::guidedFilter(Mat guide, Mat src, Mat& dst, int radius, double eps, int dDepth = -1, double scale = 1.0)
 Ptr_AdaptiveManifoldFilter cv::ximgproc::createAMFilter(double sigma_s, double sigma_r, bool adjust_outliers = false)
 void cv::ximgproc::amFilter(Mat joint, Mat src, Mat& dst, double sigma_s, double sigma_r, bool adjust_outliers = false)
 void cv::ximgproc::jointBilateralFilter(Mat joint, Mat src, Mat& dst, int d, double sigmaColor, double sigmaSpace, int borderType = BORDER_DEFAULT)
 void cv::ximgproc::bilateralTextureFilter(Mat src, Mat& dst, int fr = 3, int numIter = 1, double sigmaAlpha = -1., double sigmaAvg = -1.)
 void cv::ximgproc::rollingGuidanceFilter(Mat src, Mat& dst, int d = -1, double sigmaColor = 25, double sigmaSpace = 3, int numOfIter = 4, int borderType = BORDER_DEFAULT)
 Ptr_FastBilateralSolverFilter cv::ximgproc::createFastBilateralSolverFilter(Mat guide, double sigma_spatial, double sigma_luma, double sigma_chroma, double lambda = 128.0, int num_iter = 25, double max_tol = 1e-5)
 void cv::ximgproc::fastBilateralSolverFilter(Mat guide, Mat src, Mat confidence, Mat& dst, double sigma_spatial = 8, double sigma_luma = 8, double sigma_chroma = 8, double lambda = 128.0, int num_iter = 25, double max_tol = 1e-5)
 Ptr_FastGlobalSmootherFilter cv::ximgproc::createFastGlobalSmootherFilter(Mat guide, double lambda, double sigma_color, double lambda_attenuation = 0.25, int num_iter = 3)
 void cv::ximgproc::fastGlobalSmootherFilter(Mat guide, Mat src, Mat& dst, double lambda, double sigma_color, double lambda_attenuation = 0.25, int num_iter = 3)
 void cv::ximgproc::l0Smooth(Mat src, Mat& dst, double lambda = 0.02, double kappa = 2.0)
 void cv::ximgproc::covarianceEstimation(Mat src, Mat& dst, int windowRows, int windowCols)
 void cv::ximgproc::FastHoughTransform(Mat src, Mat& dst, int dstMatDepth, AngleRangeOption angleRange = ARO_315_135, HoughOp op = FHT_ADD, HoughDeskewOption makeSkew = HDO_DESKEW)
 Vec4i cv::ximgproc::HoughPoint2Line(Point houghPoint, Mat srcImgInfo, AngleRangeOption angleRange = ARO_315_135, HoughDeskewOption makeSkew = HDO_DESKEW, int rules = RO_IGNORE_BORDERS)
 Ptr_FastLineDetector cv::ximgproc::createFastLineDetector(int length_threshold = 10, float distance_threshold = 1.414213562f, double canny_th1 = 50.0, double canny_th2 = 50.0, int canny_aperture_size = 3, bool do_merge = false)
 void cv::ximgproc::findEllipses(Mat image, Mat& ellipses, float scoreThreshold = 0.7f, float reliabilityThreshold = 0.5f, float centerDistanceThreshold = 0.05f)
 void cv::ximgproc::fourierDescriptor(Mat src, Mat& dst, int nbElt = -1, int nbFD = -1)
 void cv::ximgproc::transformFD(Mat src, Mat t, Mat& dst, bool fdContour = true)
 void cv::ximgproc::contourSampling(Mat src, Mat& out, int nbElt)
 Ptr_ContourFitting cv::ximgproc::createContourFitting(int ctr = 1024, int fd = 16)
 Ptr_SuperpixelLSC cv::ximgproc::createSuperpixelLSC(Mat image, int region_size = 10, float ratio = 0.075f)
 void cv::ximgproc::PeiLinNormalization(Mat I, Mat& T)
 void cv::ximgproc::RadonTransform(Mat src, Mat& dst, double theta = 1, double start_angle = 0, double end_angle = 180, bool crop = false, bool norm = false)
 Ptr_ScanSegment cv::ximgproc::createScanSegment(int image_width, int image_height, int num_superpixels, int slices = 8, bool merge_small = true)
 Ptr_SuperpixelSEEDS cv::ximgproc::createSuperpixelSEEDS(int image_width, int image_height, int image_channels, int num_superpixels, int num_levels, int prior = 2, int histogram_bins = 5, bool double_step = false)
 Ptr_GraphSegmentation cv::ximgproc::segmentation::createGraphSegmentation(double sigma = 0.5, float k = 300, int min_size = 100)
 Ptr_SelectiveSearchSegmentationStrategyColor cv::ximgproc::segmentation::createSelectiveSearchSegmentationStrategyColor()
 Ptr_SelectiveSearchSegmentationStrategySize cv::ximgproc::segmentation::createSelectiveSearchSegmentationStrategySize()
 Ptr_SelectiveSearchSegmentationStrategyTexture cv::ximgproc::segmentation::createSelectiveSearchSegmentationStrategyTexture()
 Ptr_SelectiveSearchSegmentationStrategyFill cv::ximgproc::segmentation::createSelectiveSearchSegmentationStrategyFill()
 Ptr_SelectiveSearchSegmentationStrategyMultiple cv::ximgproc::segmentation::createSelectiveSearchSegmentationStrategyMultiple()
 Ptr_SelectiveSearchSegmentationStrategyMultiple cv::ximgproc::segmentation::createSelectiveSearchSegmentationStrategyMultiple(Ptr_SelectiveSearchSegmentationStrategy s1)
 Ptr_SelectiveSearchSegmentationStrategyMultiple cv::ximgproc::segmentation::createSelectiveSearchSegmentationStrategyMultiple(Ptr_SelectiveSearchSegmentationStrategy s1, Ptr_SelectiveSearchSegmentationStrategy s2)
 Ptr_SelectiveSearchSegmentationStrategyMultiple cv::ximgproc::segmentation::createSelectiveSearchSegmentationStrategyMultiple(Ptr_SelectiveSearchSegmentationStrategy s1, Ptr_SelectiveSearchSegmentationStrategy s2, Ptr_SelectiveSearchSegmentationStrategy s3)
 Ptr_SelectiveSearchSegmentationStrategyMultiple cv::ximgproc::segmentation::createSelectiveSearchSegmentationStrategyMultiple(Ptr_SelectiveSearchSegmentationStrategy s1, Ptr_SelectiveSearchSegmentationStrategy s2, Ptr_SelectiveSearchSegmentationStrategy s3, Ptr_SelectiveSearchSegmentationStrategy s4)
 Ptr_SelectiveSearchSegmentation cv::ximgproc::segmentation::createSelectiveSearchSegmentation()
 Ptr_SuperpixelSLIC cv::ximgproc::createSuperpixelSLIC(Mat image, SLICType algorithm = SLICO, int region_size = 10, float ruler = 10.0f)
 Ptr_EdgeAwareInterpolator cv::ximgproc::createEdgeAwareInterpolator()
 Ptr_RICInterpolator cv::ximgproc::createRICInterpolator()
 Ptr_RFFeatureGetter cv::ximgproc::createRFFeatureGetter()
 Ptr_StructuredEdgeDetection cv::ximgproc::createStructuredEdgeDetection(String model, Ptr_RFFeatureGetter howToGetFeatures = Ptr<RFFeatureGetter>())
 void cv::ximgproc::weightedMedianFilter(Mat joint, Mat src, Mat& dst, int r, double sigma = 25.5, WMFWeightType weightType = WMF_EXP, Mat mask = Mat())
 void cv::ximgproc::AdaptiveManifoldFilter::filter(Mat src, Mat& dst, Mat joint = Mat())
 void cv::ximgproc::AdaptiveManifoldFilter::collectGarbage()
static Ptr_AdaptiveManifoldFilter cv::ximgproc::AdaptiveManifoldFilter::create()
 void cv::ximgproc::ContourFitting::estimateTransformation(Mat src, Mat dst, Mat& alphaPhiST, double& dist, bool fdContour = false)
 void cv::ximgproc::ContourFitting::setCtrSize(int n)
 void cv::ximgproc::ContourFitting::setFDSize(int n)
 int cv::ximgproc::ContourFitting::getCtrSize()
 int cv::ximgproc::ContourFitting::getFDSize()
 void cv::ximgproc::DTFilter::filter(Mat src, Mat& dst, int dDepth = -1)
 void cv::ximgproc::DisparityFilter::filter(Mat disparity_map_left, Mat left_view, Mat& filtered_disparity_map, Mat disparity_map_right = Mat(), Rect ROI = Rect(), Mat right_view = Mat())
 double cv::ximgproc::DisparityWLSFilter::getLambda()
 void cv::ximgproc::DisparityWLSFilter::setLambda(double _lambda)
 double cv::ximgproc::DisparityWLSFilter::getSigmaColor()
 void cv::ximgproc::DisparityWLSFilter::setSigmaColor(double _sigma_color)
 int cv::ximgproc::DisparityWLSFilter::getLRCthresh()
 void cv::ximgproc::DisparityWLSFilter::setLRCthresh(int _LRC_thresh)
 int cv::ximgproc::DisparityWLSFilter::getDepthDiscontinuityRadius()
 void cv::ximgproc::DisparityWLSFilter::setDepthDiscontinuityRadius(int _disc_radius)
 Mat cv::ximgproc::DisparityWLSFilter::getConfidenceMap()
 Rect cv::ximgproc::DisparityWLSFilter::getROI()
 void cv::ximgproc::EdgeAwareInterpolator::setCostMap(Mat _costMap)
 void cv::ximgproc::EdgeAwareInterpolator::setK(int _k)
 int cv::ximgproc::EdgeAwareInterpolator::getK()
 void cv::ximgproc::EdgeAwareInterpolator::setSigma(float _sigma)
 float cv::ximgproc::EdgeAwareInterpolator::getSigma()
 void cv::ximgproc::EdgeAwareInterpolator::setLambda(float _lambda)
 float cv::ximgproc::EdgeAwareInterpolator::getLambda()
 void cv::ximgproc::EdgeAwareInterpolator::setUsePostProcessing(bool _use_post_proc)
 bool cv::ximgproc::EdgeAwareInterpolator::getUsePostProcessing()
 void cv::ximgproc::EdgeAwareInterpolator::setFGSLambda(float _lambda)
 float cv::ximgproc::EdgeAwareInterpolator::getFGSLambda()
 void cv::ximgproc::EdgeAwareInterpolator::setFGSSigma(float _sigma)
 float cv::ximgproc::EdgeAwareInterpolator::getFGSSigma()
 void cv::ximgproc::EdgeBoxes::getBoundingBoxes(Mat edge_map, Mat orientation_map, vector_Rect& boxes, Mat& scores = Mat())
 float cv::ximgproc::EdgeBoxes::getAlpha()
 void cv::ximgproc::EdgeBoxes::setAlpha(float value)
 float cv::ximgproc::EdgeBoxes::getBeta()
 void cv::ximgproc::EdgeBoxes::setBeta(float value)
 float cv::ximgproc::EdgeBoxes::getEta()
 void cv::ximgproc::EdgeBoxes::setEta(float value)
 float cv::ximgproc::EdgeBoxes::getMinScore()
 void cv::ximgproc::EdgeBoxes::setMinScore(float value)
 int cv::ximgproc::EdgeBoxes::getMaxBoxes()
 void cv::ximgproc::EdgeBoxes::setMaxBoxes(int value)
 float cv::ximgproc::EdgeBoxes::getEdgeMinMag()
 void cv::ximgproc::EdgeBoxes::setEdgeMinMag(float value)
 float cv::ximgproc::EdgeBoxes::getEdgeMergeThr()
 void cv::ximgproc::EdgeBoxes::setEdgeMergeThr(float value)
 float cv::ximgproc::EdgeBoxes::getClusterMinMag()
 void cv::ximgproc::EdgeBoxes::setClusterMinMag(float value)
 float cv::ximgproc::EdgeBoxes::getMaxAspectRatio()
 void cv::ximgproc::EdgeBoxes::setMaxAspectRatio(float value)
 float cv::ximgproc::EdgeBoxes::getMinBoxArea()
 void cv::ximgproc::EdgeBoxes::setMinBoxArea(float value)
 float cv::ximgproc::EdgeBoxes::getGamma()
 void cv::ximgproc::EdgeBoxes::setGamma(float value)
 float cv::ximgproc::EdgeBoxes::getKappa()
 void cv::ximgproc::EdgeBoxes::setKappa(float value)
 void cv::ximgproc::EdgeDrawing::detectEdges(Mat src)
 void cv::ximgproc::EdgeDrawing::getEdgeImage(Mat& dst)
 void cv::ximgproc::EdgeDrawing::getGradientImage(Mat& dst)
 vector_vector_Point cv::ximgproc::EdgeDrawing::getSegments()
 vector_int cv::ximgproc::EdgeDrawing::getSegmentIndicesOfLines()
 void cv::ximgproc::EdgeDrawing::detectLines(Mat& lines)
 void cv::ximgproc::EdgeDrawing::detectEllipses(Mat& ellipses)
 void cv::ximgproc::EdgeDrawing::setParams(EdgeDrawing_Params parameters)
  cv::ximgproc::EdgeDrawing::Params::Params()
 void cv::ximgproc::FastBilateralSolverFilter::filter(Mat src, Mat confidence, Mat& dst)
 void cv::ximgproc::FastGlobalSmootherFilter::filter(Mat src, Mat& dst)
 void cv::ximgproc::FastLineDetector::detect(Mat image, Mat& lines)
 void cv::ximgproc::FastLineDetector::drawSegments(Mat& image, Mat lines, bool draw_arrow = false, Scalar linecolor = Scalar(0, 0, 255), int linethickness = 1)
 void cv::ximgproc::GuidedFilter::filter(Mat src, Mat& dst, int dDepth = -1)
 void cv::ximgproc::RFFeatureGetter::getFeatures(Mat src, Mat features, int gnrmRad, int gsmthRad, int shrink, int outNum, int gradNum)
 void cv::ximgproc::RICInterpolator::setK(int k = 32)
 int cv::ximgproc::RICInterpolator::getK()
 void cv::ximgproc::RICInterpolator::setCostMap(Mat costMap)
 void cv::ximgproc::RICInterpolator::setSuperpixelSize(int spSize = 15)
 int cv::ximgproc::RICInterpolator::getSuperpixelSize()
 void cv::ximgproc::RICInterpolator::setSuperpixelNNCnt(int spNN = 150)
 int cv::ximgproc::RICInterpolator::getSuperpixelNNCnt()
 void cv::ximgproc::RICInterpolator::setSuperpixelRuler(float ruler = 15.f)
 float cv::ximgproc::RICInterpolator::getSuperpixelRuler()
 void cv::ximgproc::RICInterpolator::setSuperpixelMode(int mode = 100)
 int cv::ximgproc::RICInterpolator::getSuperpixelMode()
 void cv::ximgproc::RICInterpolator::setAlpha(float alpha = 0.7f)
 float cv::ximgproc::RICInterpolator::getAlpha()
 void cv::ximgproc::RICInterpolator::setModelIter(int modelIter = 4)
 int cv::ximgproc::RICInterpolator::getModelIter()
 void cv::ximgproc::RICInterpolator::setRefineModels(bool refineModles = true)
 bool cv::ximgproc::RICInterpolator::getRefineModels()
 void cv::ximgproc::RICInterpolator::setMaxFlow(float maxFlow = 250.f)
 float cv::ximgproc::RICInterpolator::getMaxFlow()
 void cv::ximgproc::RICInterpolator::setUseVariationalRefinement(bool use_variational_refinement = false)
 bool cv::ximgproc::RICInterpolator::getUseVariationalRefinement()
 void cv::ximgproc::RICInterpolator::setUseGlobalSmootherFilter(bool use_FGS = true)
 bool cv::ximgproc::RICInterpolator::getUseGlobalSmootherFilter()
 void cv::ximgproc::RICInterpolator::setFGSLambda(float lambda = 500.f)
 float cv::ximgproc::RICInterpolator::getFGSLambda()
 void cv::ximgproc::RICInterpolator::setFGSSigma(float sigma = 1.5f)
 float cv::ximgproc::RICInterpolator::getFGSSigma()
static Ptr_RidgeDetectionFilter cv::ximgproc::RidgeDetectionFilter::create(int ddepth = CV_32FC1, int dx = 1, int dy = 1, int ksize = 3, int out_dtype = CV_8UC1, double scale = 1, double delta = 0, int borderType = BORDER_DEFAULT)
 void cv::ximgproc::RidgeDetectionFilter::getRidgeFilteredImage(Mat _img, Mat& out)
 int cv::ximgproc::ScanSegment::getNumberOfSuperpixels()
 void cv::ximgproc::ScanSegment::iterate(Mat img)
 void cv::ximgproc::ScanSegment::getLabels(Mat& labels_out)
 void cv::ximgproc::ScanSegment::getLabelContourMask(Mat& image, bool thick_line = false)
 void cv::ximgproc::SparseMatchInterpolator::interpolate(Mat from_image, Mat from_points, Mat to_image, Mat to_points, Mat& dense_flow)
 void cv::ximgproc::StructuredEdgeDetection::detectEdges(Mat src, Mat& dst)
 void cv::ximgproc::StructuredEdgeDetection::computeOrientation(Mat src, Mat& dst)
 void cv::ximgproc::StructuredEdgeDetection::edgesNms(Mat edge_image, Mat orientation_image, Mat& dst, int r = 2, int s = 0, float m = 1, bool isParallel = true)
 int cv::ximgproc::SuperpixelLSC::getNumberOfSuperpixels()
 void cv::ximgproc::SuperpixelLSC::iterate(int num_iterations = 10)
 void cv::ximgproc::SuperpixelLSC::getLabels(Mat& labels_out)
 void cv::ximgproc::SuperpixelLSC::getLabelContourMask(Mat& image, bool thick_line = true)
 void cv::ximgproc::SuperpixelLSC::enforceLabelConnectivity(int min_element_size = 25)
 int cv::ximgproc::SuperpixelSEEDS::getNumberOfSuperpixels()
 void cv::ximgproc::SuperpixelSEEDS::iterate(Mat img, int num_iterations = 4)
 void cv::ximgproc::SuperpixelSEEDS::getLabels(Mat& labels_out)
 void cv::ximgproc::SuperpixelSEEDS::getLabelContourMask(Mat& image, bool thick_line = false)
 int cv::ximgproc::SuperpixelSLIC::getNumberOfSuperpixels()
 void cv::ximgproc::SuperpixelSLIC::iterate(int num_iterations = 10)
 void cv::ximgproc::SuperpixelSLIC::getLabels(Mat& labels_out)
 void cv::ximgproc::SuperpixelSLIC::getLabelContourMask(Mat& image, bool thick_line = true)
 void cv::ximgproc::SuperpixelSLIC::enforceLabelConnectivity(int min_element_size = 25)
 void cv::ximgproc::segmentation::GraphSegmentation::processImage(Mat src, Mat& dst)
 void cv::ximgproc::segmentation::GraphSegmentation::setSigma(double sigma)
 double cv::ximgproc::segmentation::GraphSegmentation::getSigma()
 void cv::ximgproc::segmentation::GraphSegmentation::setK(float k)
 float cv::ximgproc::segmentation::GraphSegmentation::getK()
 void cv::ximgproc::segmentation::GraphSegmentation::setMinSize(int min_size)
 int cv::ximgproc::segmentation::GraphSegmentation::getMinSize()
 void cv::ximgproc::segmentation::SelectiveSearchSegmentation::setBaseImage(Mat img)
 void cv::ximgproc::segmentation::SelectiveSearchSegmentation::switchToSingleStrategy(int k = 200, float sigma = 0.8f)
 void cv::ximgproc::segmentation::SelectiveSearchSegmentation::switchToSelectiveSearchFast(int base_k = 150, int inc_k = 150, float sigma = 0.8f)
 void cv::ximgproc::segmentation::SelectiveSearchSegmentation::switchToSelectiveSearchQuality(int base_k = 150, int inc_k = 150, float sigma = 0.8f)
 void cv::ximgproc::segmentation::SelectiveSearchSegmentation::addImage(Mat img)
 void cv::ximgproc::segmentation::SelectiveSearchSegmentation::clearImages()
 void cv::ximgproc::segmentation::SelectiveSearchSegmentation::addGraphSegmentation(Ptr_GraphSegmentation g)
 void cv::ximgproc::segmentation::SelectiveSearchSegmentation::clearGraphSegmentations()
 void cv::ximgproc::segmentation::SelectiveSearchSegmentation::addStrategy(Ptr_SelectiveSearchSegmentationStrategy s)
 void cv::ximgproc::segmentation::SelectiveSearchSegmentation::clearStrategies()
 void cv::ximgproc::segmentation::SelectiveSearchSegmentation::process(vector_Rect& rects)
 void cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy::setImage(Mat img, Mat regions, Mat sizes, int image_id = -1)
 float cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy::get(int r1, int r2)
 void cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategy::merge(int r1, int r2)
 void cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyMultiple::addStrategy(Ptr_SelectiveSearchSegmentationStrategy g, float weight)
 void cv::ximgproc::segmentation::SelectiveSearchSegmentationStrategyMultiple::clearStrategies()

SKIPPED FUNCs LIST (0 of 213):


0 def args - 144 funcs
1 def args - 37 funcs
2 def args - 12 funcs
3 def args - 12 funcs
4 def args - 2 funcs
5 def args - 2 funcs
6 def args - 2 funcs
8 def args - 1 funcs
12 def args - 1 funcs