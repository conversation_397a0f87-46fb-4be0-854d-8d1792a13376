// This file is part of OpenCV project.
// It is subject to the license terms in the LICENSE file found in the top-level directory
// of this distribution and at http://opencv.org/license.html.

#ifndef __OPENCV_PRECOMP_H__
#define __OPENCV_PRECOMP_H__

#include "opencv2/cann.hpp"
#include "opencv2/stream_accessor.hpp"
#include "opencv2/cann_call.hpp"
#include "opencv2/cann_interface.hpp"
#include "opencv2/cann_private.hpp"
#include "opencv2/dvpp_call.hpp"
#include "opencv2/ascendc_kernels.hpp"
#define ALIGN_UP(num, align) (((num) + (align) - 1) & ~((align) - 1))

#endif /* __OPENCV_PRECOMP_H__ */
