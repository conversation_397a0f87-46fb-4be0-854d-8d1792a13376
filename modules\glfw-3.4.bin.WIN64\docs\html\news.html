<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Release notes for version 3.4</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div><div class="header">
  <div class="headertitle"><div class="title">Release notes for version 3.4</div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#features">New features</a><ul><li class="level2"><a href="#runtime_platform_selection">Runtime platform selection</a></li>
<li class="level2"><a href="#more_cursor_shapes">More standard cursor shapes</a></li>
<li class="level2"><a href="#mouse_input_passthrough">Mouse event passthrough</a></li>
<li class="level2"><a href="#window_title_function">Ability to get window title</a></li>
<li class="level2"><a href="#captured_cursor_mode">Captured cursor mode</a></li>
<li class="level2"><a href="#custom_heap_allocator">Support for custom heap memory allocator</a></li>
<li class="level2"><a href="#scale_framebuffer_hint">Window hint for framebuffer scaling</a></li>
<li class="level2"><a href="#window_position_hint">Window hints for initial window position</a></li>
<li class="level2"><a href="#angle_renderer_hint">ANGLE rendering backend hint</a></li>
<li class="level2"><a href="#win32_keymenu_hint">Windows window menu keyboard access hint</a></li>
<li class="level2"><a href="#win32_showdefault_hint">Windows STARTUPINFO show command hint</a></li>
<li class="level2"><a href="#cocoa_nsview_function">Cocoa NSView native access function</a></li>
<li class="level2"><a href="#wayland_libdecor_decorations">Wayland libdecor decorations</a></li>
<li class="level2"><a href="#wayland_app_id_hint">Wayland surface app_id hint</a></li>
<li class="level2"><a href="#x11_xcb_vulkan_surface">X11 Vulkan window surface hint</a></li>
</ul>
</li>
<li class="level1"><a href="#caveats">Caveats</a><ul><li class="level2"><a href="#multiplatform_caveat">Multiple sets of native access functions</a></li>
<li class="level2"><a href="#version_string_caveat">Version string format has been changed</a></li>
<li class="level2"><a href="#joystick_init_caveat">Joystick support is initialized on demand</a></li>
<li class="level2"><a href="#standalone_caveat">Tests and examples are disabled when built as a subproject</a></li>
<li class="level2"><a href="#config_header_caveat">Configuration header is no longer generated</a></li>
<li class="level2"><a href="#docs_target_caveat">Documentation generation requires Doxygen 1.9.8 or later</a></li>
<li class="level2"><a href="#win7_framebuffer_caveat">Windows 7 framebuffer transparency requires DWM transparency</a></li>
<li class="level2"><a href="#macos_menu_caveat">macOS main menu now created at initialization</a></li>
<li class="level2"><a href="#corevideo_caveat">macOS CoreVideo dependency has been removed</a></li>
<li class="level2"><a href="#wayland_alpha_caveat">Wayland framebuffer may lack alpha channel on older systems</a></li>
<li class="level2"><a href="#x11_emptyevent_caveat">X11 empty events no longer round-trip to server</a></li>
</ul>
</li>
<li class="level1"><a href="#deprecations">Deprecations</a><ul><li class="level2"><a href="#winxp_deprecated">Windows XP and Vista support is deprecated</a></li>
<li class="level2"><a href="#mingw_deprecated">Original MinGW support is deprecated</a></li>
<li class="level2"><a href="#yosemite_deprecated">OS X Yosemite support is deprecated</a></li>
</ul>
</li>
<li class="level1"><a href="#removals">Removals</a><ul><li class="level2"><a href="#vulkan_static_removed">GLFW_VULKAN_STATIC CMake option has been removed</a></li>
<li class="level2"><a href="#use_wayland_removed">GLFW_USE_WAYLAND CMake option has been removed</a></li>
<li class="level2"><a href="#use_osmesa_removed">GLFW_USE_OSMESA CMake option has been removed</a></li>
<li class="level2"><a href="#wl_shell_removed">wl_shell protocol support has been removed</a></li>
</ul>
</li>
<li class="level1"><a href="#new_symbols">New symbols</a><ul><li class="level2"><a href="#new_functions">New functions</a></li>
<li class="level2"><a href="#new_types">New types</a></li>
<li class="level2"><a href="#new_constants">New constants</a></li>
</ul>
</li>
<li class="level1"><a href="#news_archive">Release notes for earlier versions</a></li>
</ul>
</div>
<div class="textblock"><h1><a class="anchor" id="features"></a>
New features</h1>
<h2><a class="anchor" id="runtime_platform_selection"></a>
Runtime platform selection</h2>
<p>GLFW now supports being compiled for multiple backends and selecting between them at runtime with the <a class="el" href="intro_guide.html#GLFW_PLATFORM">GLFW_PLATFORM</a> init hint. After initialization the selected platform can be queried with <a class="el" href="group__init.html#ga6d6a983d38bd4e8fd786d7a9061d399e">glfwGetPlatform</a>. You can check if support for a given platform is compiled in with <a class="el" href="group__init.html#ga8785d2b6b36632368d803e78079d38ed">glfwPlatformSupported</a>.</p>
<p>For more information see <a class="el" href="intro_guide.html#platform">Runtime platform selection</a>.</p>
<h2><a class="anchor" id="more_cursor_shapes"></a>
More standard cursor shapes</h2>
<p>GLFW now provides the standard cursor shapes <a class="el" href="group__shapes.html#gadf2c0a495ec9cef4e1a364cc99aa78da">GLFW_RESIZE_NWSE_CURSOR</a> and <a class="el" href="group__shapes.html#gab06bba3b407f92807ba9b48de667a323">GLFW_RESIZE_NESW_CURSOR</a> for diagonal resizing, <a class="el" href="group__shapes.html#ga3a5f4811155f95ccafbbb4c9a899fc1d">GLFW_RESIZE_ALL_CURSOR</a> for omnidirectional resizing and <a class="el" href="group__shapes.html#ga297c503095b034bc8891393b637844b1">GLFW_NOT_ALLOWED_CURSOR</a> for showing an action is not allowed.</p>
<p>Unlike the original set, these shapes may not be available everywhere and creation will then fail with the new <a class="el" href="group__errors.html#ga09d6943923a70ddef3a085f5baee786c">GLFW_CURSOR_UNAVAILABLE</a> error.</p>
<p>The cursors for horizontal and vertical resizing are now referred to as <a class="el" href="group__shapes.html#ga2010a43dc1050a7c9154148a63cf01ad">GLFW_RESIZE_EW_CURSOR</a> and <a class="el" href="group__shapes.html#gaa59214e8cdc8c8adf08fdf125ed68388">GLFW_RESIZE_NS_CURSOR</a>, and the pointing hand cursor is now referred to as <a class="el" href="group__shapes.html#gaad01a50929fb515bf27e4462c51f6ed0">GLFW_POINTING_HAND_CURSOR</a>. The older names are still available.</p>
<p>For more information see <a class="el" href="input_guide.html#cursor_standard">Standard cursor creation</a>.</p>
<h2><a class="anchor" id="mouse_input_passthrough"></a>
Mouse event passthrough</h2>
<p>GLFW now provides the <a class="el" href="window_guide.html#GLFW_MOUSE_PASSTHROUGH_hint">GLFW_MOUSE_PASSTHROUGH</a> window hint for making a window transparent to mouse input, lettings events pass to whatever window is behind it. This can also be changed after window creation with the matching <a class="el" href="window_guide.html#GLFW_MOUSE_PASSTHROUGH_attrib">window attribute</a>.</p>
<h2><a class="anchor" id="window_title_function"></a>
Ability to get window title</h2>
<p>GLFW now supports querying the title of a window with the <a class="el" href="group__window.html#gac6151765c54b789c4fe66c6bc6215953">glfwGetWindowTitle</a> function.</p>
<p>For more information see <a class="el" href="window_guide.html#window_title">Window title</a>.</p>
<h2><a class="anchor" id="captured_cursor_mode"></a>
Captured cursor mode</h2>
<p>GLFW now supports confining the cursor to the window content area with the <a class="el" href="glfw3_8h.html#ac1dbfa0cb4641a0edc93412ade0895dc">GLFW_CURSOR_CAPTURED</a> cursor mode.</p>
<p>For more information see <a class="el" href="input_guide.html#cursor_mode">Cursor mode</a>.</p>
<h2><a class="anchor" id="custom_heap_allocator"></a>
Support for custom heap memory allocator</h2>
<p>GLFW now supports plugging a custom heap memory allocator at initialization with <a class="el" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a>. The allocator is a struct of type <a class="el" href="struct_g_l_f_wallocator.html">GLFWallocator</a> with function pointers corresponding to the standard library functions <code>malloc</code>, <code>realloc</code> and <code>free</code>.</p>
<p>For more information see <a class="el" href="intro_guide.html#init_allocator">Custom heap memory allocator</a>.</p>
<h2><a class="anchor" id="scale_framebuffer_hint"></a>
Window hint for framebuffer scaling</h2>
<p>GLFW now allows provides the <a class="el" href="window_guide.html#GLFW_SCALE_FRAMEBUFFER_hint">GLFW_SCALE_FRAMEBUFFER</a> window hint for controlling framebuffer scaling on platforms that handle scaling by keeping the window size the same while resizing the framebuffer. The default value is to allow framebuffer scaling.</p>
<p>This was already possible on macOS via the <a class="el" href="window_guide.html#GLFW_COCOA_RETINA_FRAMEBUFFER_hint">GLFW_COCOA_RETINA_FRAMEBUFFER</a> window hint. This is now another name for the same hint value.</p>
<p>For more information see <a class="el" href="window_guide.html#window_scale">Window content scale</a>.</p>
<h2><a class="anchor" id="window_position_hint"></a>
Window hints for initial window position</h2>
<p>GLFW now provides the <a class="el" href="window_guide.html#GLFW_POSITION_X">GLFW_POSITION_X</a> and <a class="el" href="window_guide.html#GLFW_POSITION_Y">GLFW_POSITION_Y</a> window hints for specifying the initial position of the window. This removes the need to create a hidden window, move it and then show it. The default value of these hints is <code>GLFW_ANY_POSITION</code>, which selects the previous behavior.</p>
<p>For more information see <a class="el" href="window_guide.html#window_pos">Window position</a>.</p>
<h2><a class="anchor" id="angle_renderer_hint"></a>
ANGLE rendering backend hint</h2>
<p>GLFW now provides the <a class="el" href="intro_guide.html#GLFW_ANGLE_PLATFORM_TYPE_hint">GLFW_ANGLE_PLATFORM_TYPE</a> init hint for requesting a specific rendering backend when using <a href="https://chromium.googlesource.com/angle/angle/">ANGLE</a> to create OpenGL ES contexts.</p>
<h2><a class="anchor" id="win32_keymenu_hint"></a>
Windows window menu keyboard access hint</h2>
<p>GLFW now provides the <a class="el" href="window_guide.html#GLFW_WIN32_KEYBOARD_MENU_hint">GLFW_WIN32_KEYBOARD_MENU</a> window hint for enabling keyboard access to the window menu via the Alt+Space and Alt-and-then-Space shortcuts. This may be useful for more GUI-oriented applications.</p>
<h2><a class="anchor" id="win32_showdefault_hint"></a>
Windows STARTUPINFO show command hint</h2>
<p>GLFW now provides the <a class="el" href="window_guide.html#GLFW_WIN32_SHOWDEFAULT_hint">GLFW_WIN32_SHOWDEFAULT</a> window hint for applying the show command in the program's <code>STARTUPINFO</code> when showing the window for the first time. This may be useful for the main window of a windowed-mode tool.</p>
<h2><a class="anchor" id="cocoa_nsview_function"></a>
Cocoa NSView native access function</h2>
<p>GLFW now provides the <a class="el" href="group__native.html#ga7274fb6595894e880fc95dc63156e9b1">glfwGetCocoaView</a> native access function for returning the Cocoa NSView.</p>
<h2><a class="anchor" id="wayland_libdecor_decorations"></a>
Wayland libdecor decorations</h2>
<p>GLFW now supports improved client-side window decorations via <a href="https://gitlab.freedesktop.org/libdecor/libdecor">libdecor</a>. This provides fully featured window decorations on desktop environments like GNOME.</p>
<p>Support for libdecor can be toggled before GLFW is initialized with the <a class="el" href="intro_guide.html#GLFW_WAYLAND_LIBDECOR_hint">GLFW_WAYLAND_LIBDECOR</a> init hint. It is enabled by default.</p>
<p>This feature has also been available in GLFW 3.3 since 3.3.9.</p>
<h2><a class="anchor" id="wayland_app_id_hint"></a>
Wayland surface app_id hint</h2>
<p>GLFW now supports specifying the app_id for a Wayland window using the <a class="el" href="window_guide.html#GLFW_WAYLAND_APP_ID_hint">GLFW_WAYLAND_APP_ID</a> window hint string.</p>
<h2><a class="anchor" id="x11_xcb_vulkan_surface"></a>
X11 Vulkan window surface hint</h2>
<p>GLFW now supports disabling the use of <code>VK_KHR_xcb_surface</code> over <code>VK_KHR_xlib_surface</code> where available, with the <a class="el" href="intro_guide.html#GLFW_X11_XCB_VULKAN_SURFACE_hint">GLFW_X11_XCB_VULKAN_SURFACE</a> init hint. This affects <a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a> and <a class="el" href="group__vulkan.html#ga1a24536bec3f80b08ead18e28e6ae965">glfwCreateWindowSurface</a>.</p>
<h1><a class="anchor" id="caveats"></a>
Caveats</h1>
<h2><a class="anchor" id="multiplatform_caveat"></a>
Multiple sets of native access functions</h2>
<p>Because GLFW now supports runtime selection of platform (window system), a library binary may export native access functions for multiple platforms. Starting with version 3.4 you must not assume that GLFW is running on a platform just because it exports native access functions for it. After initialization, you can query the selected platform with <a class="el" href="group__init.html#ga6d6a983d38bd4e8fd786d7a9061d399e">glfwGetPlatform</a>.</p>
<h2><a class="anchor" id="version_string_caveat"></a>
Version string format has been changed</h2>
<p>Because GLFW now supports runtime selection of platform (window system), the version string returned by <a class="el" href="group__init.html#ga026abd003c8e6501981ab1662062f1c0">glfwGetVersionString</a> has been expanded. It now contains the names of all APIs for all the platforms that the library binary supports.</p>
<p>The version string is intended for bug reporting and should not be parsed. See <a class="el" href="group__init.html#ga9f8ffaacf3c269cc48eafbf8b9b71197">glfwGetVersion</a> and <a class="el" href="group__init.html#ga8785d2b6b36632368d803e78079d38ed">glfwPlatformSupported</a> instead.</p>
<h2><a class="anchor" id="joystick_init_caveat"></a>
Joystick support is initialized on demand</h2>
<p>The joystick part of GLFW is now initialized when first used, primarily to work around faulty Windows drivers that cause DirectInput to take up to several seconds to enumerate devices.</p>
<p>This change is mostly not observable. However, if your application waits for events without having first called any joystick function or created any visible windows, the wait may never unblock as GLFW may not yet have subscribed to joystick related OS events.</p>
<p>To work around this, call any joystick function before waiting for events, for example by setting a <a class="el" href="input_guide.html#joystick_event">joystick callback</a>.</p>
<h2><a class="anchor" id="standalone_caveat"></a>
Tests and examples are disabled when built as a subproject</h2>
<p>GLFW now by default does not build the tests or examples when it is added as a subdirectory of another CMake project. If you were setting <a class="el" href="compile_guide.html#GLFW_BUILD_TESTS">GLFW_BUILD_TESTS</a> or <a class="el" href="compile_guide.html#GLFW_BUILD_EXAMPLES">GLFW_BUILD_EXAMPLES</a> to false in your CMake files, you can now remove this.</p>
<p>If you do want these to be built, set <a class="el" href="compile_guide.html#GLFW_BUILD_TESTS">GLFW_BUILD_TESTS</a> and <a class="el" href="compile_guide.html#GLFW_BUILD_EXAMPLES">GLFW_BUILD_EXAMPLES</a> in your CMake files before adding the GLFW subdirectory.</p>
<div class="fragment"><div class="line">set(GLFW_BUILD_EXAMPLES ON CACHE BOOL &quot;&quot; FORCE)</div>
<div class="line">set(GLFW_BUILD_TESTS ON CACHE BOOL &quot;&quot; FORCE)</div>
<div class="line">add_subdirectory(path/to/glfw)</div>
</div><!-- fragment --><h2><a class="anchor" id="config_header_caveat"></a>
Configuration header is no longer generated</h2>
<p>The <code>glfw_config.h</code> configuration header is no longer generated by CMake and the platform selection macros are now part of the GLFW CMake target. The <code>_GLFW_USE_CONFIG_H</code> macro is still supported in case you are generating a configuration header in a custom build setup.</p>
<h2><a class="anchor" id="docs_target_caveat"></a>
Documentation generation requires Doxygen 1.9.8 or later</h2>
<p>Doxygen 1.9.8 or later is now required for the <code>docs</code> CMake target to be generated. This is because the documentation now uses more of the Markdown support in Doxygen and this support has until recently been relatively unstable.</p>
<h2><a class="anchor" id="win7_framebuffer_caveat"></a>
Windows 7 framebuffer transparency requires DWM transparency</h2>
<p>GLFW no longer supports per-pixel framebuffer transparency via <a class="el" href="group__window.html#ga60a0578c3b9449027d683a9c6abb9f14">GLFW_TRANSPARENT_FRAMEBUFFER</a> on Windows 7 if DWM transparency is off (the Transparency setting under Personalization &gt; Window Color).</p>
<h2><a class="anchor" id="macos_menu_caveat"></a>
macOS main menu now created at initialization</h2>
<p>GLFW now creates the main menu and completes the initialization of NSApplication during initialization. Programs that do not want a main menu can disable it with the <a class="el" href="intro_guide.html#GLFW_COCOA_MENUBAR_hint">GLFW_COCOA_MENUBAR</a> init hint.</p>
<h2><a class="anchor" id="corevideo_caveat"></a>
macOS CoreVideo dependency has been removed</h2>
<p>GLFW no longer depends on the CoreVideo framework on macOS and it no longer needs to be specified during compilation or linking.</p>
<h2><a class="anchor" id="wayland_alpha_caveat"></a>
Wayland framebuffer may lack alpha channel on older systems</h2>
<p>On Wayland, when creating an EGL context on a machine lacking the new <code>EGL_EXT_present_opaque</code> extension, the <a class="el" href="window_guide.html#GLFW_ALPHA_BITS">GLFW_ALPHA_BITS</a> window hint will be ignored and the framebuffer will not have an alpha channel. This is because some Wayland compositors treat any buffer with an alpha channel as per-pixel transparent.</p>
<p>If you want a per-pixel transparent window, see the <a class="el" href="window_guide.html#GLFW_TRANSPARENT_FRAMEBUFFER_hint">GLFW_TRANSPARENT_FRAMEBUFFER</a> window hint.</p>
<h2><a class="anchor" id="x11_emptyevent_caveat"></a>
X11 empty events no longer round-trip to server</h2>
<p>Events posted with <a class="el" href="group__window.html#gab5997a25187e9fd5c6f2ecbbc8dfd7e9">glfwPostEmptyEvent</a> now use a separate unnamed pipe instead of sending an X11 client event to the helper window.</p>
<h1><a class="anchor" id="deprecations"></a>
Deprecations</h1>
<h2><a class="anchor" id="winxp_deprecated"></a>
Windows XP and Vista support is deprecated</h2>
<p>Support for Windows XP and Vista has been deprecated and will be removed in a future release. Windows XP has been out of extended support since 2014.</p>
<h2><a class="anchor" id="mingw_deprecated"></a>
Original MinGW support is deprecated</h2>
<p>Support for the now unmaintained original MinGW distribution has been deprecated and will be removed in a future release.</p>
<p>This does not apply to the much more capable MinGW-w64, which remains fully supported, actively maintained and available on many platforms.</p>
<h2><a class="anchor" id="yosemite_deprecated"></a>
OS X Yosemite support is deprecated</h2>
<p>Support for OS X 10.10 Yosemite and earlier has been deprecated and will be removed in a future release. OS X 10.10 has been out of support since 2017.</p>
<h1><a class="anchor" id="removals"></a>
Removals</h1>
<h2><a class="anchor" id="vulkan_static_removed"></a>
GLFW_VULKAN_STATIC CMake option has been removed</h2>
<p>This option was used to compile GLFW directly linked with the Vulkan loader, instead of using dynamic loading to get hold of <code>vkGetInstanceProcAddr</code> at initialization. This is now done by calling the <a class="el" href="group__init.html#ga76af552d0307bb5f7791f245417d4752">glfwInitVulkanLoader</a> function before initialization.</p>
<p>If you need backward compatibility, this macro can still be defined for GLFW 3.4 and will have no effect. The call to <a class="el" href="group__init.html#ga76af552d0307bb5f7791f245417d4752">glfwInitVulkanLoader</a> can be conditionally enabled in your code by checking the <a class="el" href="group__init.html#ga6337d9ea43b22fc529b2bba066b4a576">GLFW_VERSION_MAJOR</a> and <a class="el" href="group__init.html#gaf80d40f0aea7088ff337606e9c48f7a3">GLFW_VERSION_MINOR</a> macros.</p>
<h2><a class="anchor" id="use_wayland_removed"></a>
GLFW_USE_WAYLAND CMake option has been removed</h2>
<p>This option was used to compile GLFW for Wayland instead of X11. GLFW now supports selecting the platform at run-time. By default GLFW is compiled for both Wayland and X11 on Linux and other Unix-like systems.</p>
<p>To disable Wayland or X11 or both, set the <a class="el" href="compile_guide.html#GLFW_BUILD_WAYLAND">GLFW_BUILD_WAYLAND</a> and <a class="el" href="compile_guide.html#GLFW_BUILD_X11">GLFW_BUILD_X11</a> CMake options.</p>
<p>The <code>GLFW_USE_WAYLAND</code> CMake variable must not be present in the CMake cache at all, or GLFW will fail to configure. If you are getting this error, delete the CMake cache for GLFW and configure again.</p>
<h2><a class="anchor" id="use_osmesa_removed"></a>
GLFW_USE_OSMESA CMake option has been removed</h2>
<p>This option was used to compile GLFW for the Null platform. The Null platform is now always available. To produce a library binary that only supports this platform, the way this CMake option used to do, you will instead need to disable the default platforms for the target OS. This means setting the <a class="el" href="compile_guide.html#GLFW_BUILD_WIN32">GLFW_BUILD_WIN32</a>, <a class="el" href="compile_guide.html#GLFW_BUILD_COCOA">GLFW_BUILD_COCOA</a> or <a class="el" href="compile_guide.html#GLFW_BUILD_WAYLAND">GLFW_BUILD_WAYLAND</a> and <a class="el" href="compile_guide.html#GLFW_BUILD_X11">GLFW_BUILD_X11</a> CMake options to false.</p>
<p>You can set all of them to false and the ones that don't apply for the target OS will be ignored.</p>
<h2><a class="anchor" id="wl_shell_removed"></a>
wl_shell protocol support has been removed</h2>
<p>Support for the deprecated wl_shell protocol has been removed and GLFW now only supports the XDG-Shell protocol. If your Wayland compositor does not support XDG-Shell then GLFW will fail to initialize.</p>
<h1><a class="anchor" id="new_symbols"></a>
New symbols</h1>
<h2><a class="anchor" id="new_functions"></a>
New functions</h2>
<ul>
<li><a class="el" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a></li>
<li><a class="el" href="group__init.html#ga6d6a983d38bd4e8fd786d7a9061d399e">glfwGetPlatform</a></li>
<li><a class="el" href="group__init.html#ga8785d2b6b36632368d803e78079d38ed">glfwPlatformSupported</a></li>
<li><a class="el" href="group__init.html#ga76af552d0307bb5f7791f245417d4752">glfwInitVulkanLoader</a></li>
<li><a class="el" href="group__window.html#gac6151765c54b789c4fe66c6bc6215953">glfwGetWindowTitle</a></li>
<li><a class="el" href="group__native.html#ga7274fb6595894e880fc95dc63156e9b1">glfwGetCocoaView</a></li>
</ul>
<h2><a class="anchor" id="new_types"></a>
New types</h2>
<ul>
<li><a class="el" href="struct_g_l_f_wallocator.html">GLFWallocator</a></li>
<li><a class="el" href="group__init.html#ga4306a564e9f60f4de8cc8f31731a3120">GLFWallocatefun</a></li>
<li><a class="el" href="group__init.html#ga3e88a829615d8efe8bec1746f7309c63">GLFWreallocatefun</a></li>
<li><a class="el" href="group__init.html#ga7181615eda94c4b07bd72bdcee39fa28">GLFWdeallocatefun</a></li>
</ul>
<h2><a class="anchor" id="new_constants"></a>
New constants</h2>
<ul>
<li><a class="el" href="intro_guide.html#GLFW_PLATFORM">GLFW_PLATFORM</a></li>
<li><a class="el" href="group__init.html#ga18b2d37374d0dea28cd69194fa85b859">GLFW_ANY_PLATFORM</a></li>
<li><a class="el" href="group__init.html#ga8d3d17df2ab57492cef665da52c603a1">GLFW_PLATFORM_WIN32</a></li>
<li><a class="el" href="group__init.html#ga83b18714254f75bc2f0cdbafa0f10b6b">GLFW_PLATFORM_COCOA</a></li>
<li><a class="el" href="group__init.html#gac4b08906a3cbf26c518a4a543eedd740">GLFW_PLATFORM_WAYLAND</a></li>
<li><a class="el" href="group__init.html#gaf5333f3933e9c248a00cfda6523f386b">GLFW_PLATFORM_X11</a></li>
<li><a class="el" href="group__init.html#gac06fad5a4866ae7a1d7b2675fac72d7f">GLFW_PLATFORM_NULL</a></li>
<li><a class="el" href="group__errors.html#ga3608c6c29ab7a72f3bf019f4c3a2563d">GLFW_PLATFORM_UNAVAILABLE</a></li>
<li><a class="el" href="group__shapes.html#gaad01a50929fb515bf27e4462c51f6ed0">GLFW_POINTING_HAND_CURSOR</a></li>
<li><a class="el" href="group__shapes.html#ga2010a43dc1050a7c9154148a63cf01ad">GLFW_RESIZE_EW_CURSOR</a></li>
<li><a class="el" href="group__shapes.html#gaa59214e8cdc8c8adf08fdf125ed68388">GLFW_RESIZE_NS_CURSOR</a></li>
<li><a class="el" href="group__shapes.html#gadf2c0a495ec9cef4e1a364cc99aa78da">GLFW_RESIZE_NWSE_CURSOR</a></li>
<li><a class="el" href="group__shapes.html#gab06bba3b407f92807ba9b48de667a323">GLFW_RESIZE_NESW_CURSOR</a></li>
<li><a class="el" href="group__shapes.html#ga3a5f4811155f95ccafbbb4c9a899fc1d">GLFW_RESIZE_ALL_CURSOR</a></li>
<li><a class="el" href="group__window.html#ga88981797d29800808ec242274ab5c03a">GLFW_MOUSE_PASSTHROUGH</a></li>
<li><a class="el" href="group__shapes.html#ga297c503095b034bc8891393b637844b1">GLFW_NOT_ALLOWED_CURSOR</a></li>
<li><a class="el" href="group__errors.html#ga09d6943923a70ddef3a085f5baee786c">GLFW_CURSOR_UNAVAILABLE</a></li>
<li><a class="el" href="group__window.html#gaf65ea8dafdc0edb07b821b9a336d5043">GLFW_WIN32_KEYBOARD_MENU</a></li>
<li><a class="el" href="group__window.html#gace10f3846571de62243b46f75d978487">GLFW_WIN32_SHOWDEFAULT</a></li>
<li><a class="el" href="group__window.html#ga8d55e3afec73c7de0509c3b7ad1d9e3f">GLFW_CONTEXT_DEBUG</a></li>
<li><a class="el" href="group__errors.html#ga526fba20a01504a8086c763b6ca53ce5">GLFW_FEATURE_UNAVAILABLE</a></li>
<li><a class="el" href="group__errors.html#ga5dda77e023e83151e8bd55a6758f946a">GLFW_FEATURE_UNIMPLEMENTED</a></li>
<li><a class="el" href="group__init.html#gaec269b24cf549ab46292c0125d8bbdce">GLFW_ANGLE_PLATFORM_TYPE</a></li>
<li><a class="el" href="glfw3_8h.html#ae78e673449c2a2b8c560ca1b1e283228">GLFW_ANGLE_PLATFORM_TYPE_NONE</a></li>
<li><a class="el" href="glfw3_8h.html#ad8d9e97ed7790811470366b338833623">GLFW_ANGLE_PLATFORM_TYPE_OPENGL</a></li>
<li><a class="el" href="glfw3_8h.html#a0003c089da020cbf957218e70245bb65">GLFW_ANGLE_PLATFORM_TYPE_OPENGLES</a></li>
<li><a class="el" href="glfw3_8h.html#a6e8fdc83113d247ad792bb5c4e82c894">GLFW_ANGLE_PLATFORM_TYPE_D3D9</a></li>
<li><a class="el" href="glfw3_8h.html#ad6eae659811a52a5cdc43c362aedfa33">GLFW_ANGLE_PLATFORM_TYPE_D3D11</a></li>
<li><a class="el" href="glfw3_8h.html#a579ac83506c7546709dad91960cc7ca1">GLFW_ANGLE_PLATFORM_TYPE_VULKAN</a></li>
<li><a class="el" href="glfw3_8h.html#ab56d91b26cf223dc67590a93a2f8507d">GLFW_ANGLE_PLATFORM_TYPE_METAL</a></li>
<li><a class="el" href="group__init.html#gaa341e303ebeb8e4199b8ab8be84351f6">GLFW_X11_XCB_VULKAN_SURFACE</a></li>
<li><a class="el" href="glfw3_8h.html#ac1dbfa0cb4641a0edc93412ade0895dc">GLFW_CURSOR_CAPTURED</a></li>
<li><a class="el" href="window_guide.html#GLFW_POSITION_X">GLFW_POSITION_X</a></li>
<li><a class="el" href="window_guide.html#GLFW_POSITION_Y">GLFW_POSITION_Y</a></li>
<li><a class="el" href="glfw3_8h.html#aa0e681bf859ef1bb8355692a70b0ee92">GLFW_ANY_POSITION</a></li>
<li><a class="el" href="group__window.html#gafbf1ce7a4362c75e602a4df9e1bdecd3">GLFW_WAYLAND_APP_ID</a></li>
<li><a class="el" href="group__init.html#ga2a3f2fd7695902c498b050215b3db452">GLFW_WAYLAND_LIBDECOR</a></li>
<li><a class="el" href="glfw3_8h.html#a92b0d7e0eaeeefaccc0ccc2ccb130e99">GLFW_WAYLAND_PREFER_LIBDECOR</a></li>
<li><a class="el" href="glfw3_8h.html#aadcea7c6afbf86b848404457c4253fd7">GLFW_WAYLAND_DISABLE_LIBDECOR</a></li>
<li><a class="el" href="group__window.html#gaa5a9c6b4722670fd33d6e8a88f2e21bc">GLFW_SCALE_FRAMEBUFFER</a></li>
</ul>
<h1><a class="anchor" id="news_archive"></a>
Release notes for earlier versions</h1>
<ul>
<li><a href="https://www.glfw.org/docs/3.3/news.html">Release notes for 3.3</a></li>
<li><a href="https://www.glfw.org/docs/3.2/news.html">Release notes for 3.2</a></li>
<li><a href="https://www.glfw.org/docs/3.1/news.html">Release notes for 3.1</a></li>
<li><a href="https://www.glfw.org/docs/3.0/news.html">Release notes for 3.0</a> </li>
</ul>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
