#define __TBB_VERSION_STRINGS(N) \
#N": BUILD_PACKAGE	OpenCV @OPENCV_VERSION@" ENDL \
#N": BUILD_HOST 	@CMAKE_HOST_SYSTEM_NAME@ @CMAKE_HOST_SYSTEM_VERSION@ @CMAKE_HOST_SYSTEM_PROCESSOR@" ENDL \
#N": BUILD_TARGET	@CMAKE_SYSTEM_NAME@ @CMAKE_SYSTEM_VERSION@ @CMAKE_SYSTEM_PROCESSOR@" ENDL \
#N": BUILD_COMPILER	@CMAKE_CXX_COMPILER@ (ver @CMAKE_CXX_COMPILER_VERSION@)" ENDL \
#N": BUILD_COMMAND	use cv::getBuildInformation() for details" ENDL
