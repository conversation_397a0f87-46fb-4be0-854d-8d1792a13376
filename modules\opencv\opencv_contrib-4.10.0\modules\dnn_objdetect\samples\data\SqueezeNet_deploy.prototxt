# SqueezeNet architecture for image classification on ImageNet dataset

name: "SqueezeNet"

input: "data"
input_dim: 1
input_dim: 3
input_dim: 416
input_dim: 416

layer {
   name: "conv1"
   type: "Convolution"
   bottom: "data"
   top: "conv1"
   convolution_param {
      num_output: 96
      kernel_size: 7
      stride: 2
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_conv1"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "conv1"
   top: "conv1"
}
layer {
   name: "pool1"
   type: "Pooling"
   bottom: "conv1"
   top: "pool1"
   pooling_param {
      pool: MAX
      kernel_size: 3
      stride: 2
   }
}

layer {
   name: "fire2_squeeze"
   type: "Convolution"
   bottom: "pool1"
   top: "fire2_squeeze"
   convolution_param {
      num_output: 16
      kernel_size: 1
      stride: 1
      pad: 0
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire2_squeeze"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire2_squeeze"
   top: "fire2_squeeze"
}
layer {
   name: "fire2_expand_1x1"
   type: "Convolution"
   bottom: "fire2_squeeze"
   top: "fire2_expand_1x1"
   convolution_param {
      num_output: 64
      kernel_size: 1
      stride: 1
      pad: 0
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire2_expand_1x1"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire2_expand_1x1"
   top: "fire2_expand_1x1"
}
layer {
   name: "fire2_expand_3x3"
   type: "Convolution"
   bottom: "fire2_squeeze"
   top: "fire2_expand_3x3"
   convolution_param {
      num_output: 64
      kernel_size: 3
      stride: 1
      pad: 1
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire2_expand_3x3"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire2_expand_3x3"
   top: "fire2_expand_3x3"
}
layer {
   name: "fire2"
   type: "Concat"
   bottom: "fire2_expand_1x1"
   bottom: "fire2_expand_3x3"
   top: "fire2"
   concat_param {
      axis: 1
   }
}

layer {
   name: "fire3_squeeze"
   type: "Convolution"
   bottom: "fire2"
   top: "fire3_squeeze"
   convolution_param {
      num_output: 16
      kernel_size: 1
      stride: 1
      pad: 0
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire3_squeeze"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire3_squeeze"
   top: "fire3_squeeze"
}
layer {
   name: "fire3_expand_1x1"
   type: "Convolution"
   bottom: "fire3_squeeze"
   top: "fire3_expand_1x1"
   convolution_param {
      num_output: 64
      kernel_size: 1
      stride: 1
      pad: 0
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire3_expand_1x1"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire3_expand_1x1"
   top: "fire3_expand_1x1"
}
layer {
   name: "fire3_expand_3x3"
   type: "Convolution"
   bottom: "fire3_squeeze"
   top: "fire3_expand_3x3"
   convolution_param {
      num_output: 64
      kernel_size: 3
      stride: 1
      pad: 1
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire3_expand_3x3"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire3_expand_3x3"
   top: "fire3_expand_3x3"
}
layer {
   name: "fire3"
   type: "Concat"
   bottom: "fire3_expand_1x1"
   bottom: "fire3_expand_3x3"
   top: "fire3"
   concat_param {
      axis: 1
   }
}

layer {
   name: "fire4_squeeze"
   type: "Convolution"
   bottom: "fire3"
   top: "fire4_squeeze"
   convolution_param {
      num_output: 32
      kernel_size: 1
      stride: 1
      pad: 0
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire4_squeeze"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire4_squeeze"
   top: "fire4_squeeze"
}
layer {
   name: "fire4_expand_1x1"
   type: "Convolution"
   bottom: "fire4_squeeze"
   top: "fire4_expand_1x1"
   convolution_param {
      num_output: 128
      kernel_size: 1
      stride: 1
      pad: 0
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire4_expand_1x1"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire4_expand_1x1"
   top: "fire4_expand_1x1"
}
layer {
   name: "fire4_expand_3x3"
   type: "Convolution"
   bottom: "fire4_squeeze"
   top: "fire4_expand_3x3"
   convolution_param {
      num_output: 128
      kernel_size: 3
      stride: 1
      pad: 1
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire4_expand_3x3"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire4_expand_3x3"
   top: "fire4_expand_3x3"
}
layer {
   name: "fire4"
   type: "Concat"
   bottom: "fire4_expand_1x1"
   bottom: "fire4_expand_3x3"
   top: "fire4"
   concat_param {
      axis: 1
   }
}
layer {
   name: "pool4"
   type: "Pooling"
   bottom: "fire4"
   top: "pool4"
   pooling_param {
      pool: MAX
      kernel_size: 3
      stride: 2
   }
}
layer {
   name: "fire5_squeeze"
   type: "Convolution"
   bottom: "pool4"
   top: "fire5_squeeze"
   convolution_param {
      num_output: 32
      kernel_size: 1
      stride: 1
      pad: 0
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire5_squeeze"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire5_squeeze"
   top: "fire5_squeeze"
}
layer {
   name: "fire5_expand_1x1"
   type: "Convolution"
   bottom: "fire5_squeeze"
   top: "fire5_expand_1x1"
   convolution_param {
      num_output: 128
      kernel_size: 1
      stride: 1
      pad: 0
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire5_expand_1x1"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire5_expand_1x1"
   top: "fire5_expand_1x1"
}
layer {
   name: "fire5_expand_3x3"
   type: "Convolution"
   bottom: "fire5_squeeze"
   top: "fire5_expand_3x3"
   convolution_param {
      num_output: 128
      kernel_size: 3
      stride: 1
      pad: 1
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire5_expand_3x3"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire5_expand_3x3"
   top: "fire5_expand_3x3"
}
layer {
   name: "fire5"
   type: "Concat"
   bottom: "fire5_expand_1x1"
   bottom: "fire5_expand_3x3"
   top: "fire5"
   concat_param {
      axis: 1
   }
}
layer {
   name: "fire6_squeeze"
   type: "Convolution"
   bottom: "fire5"
   top: "fire6_squeeze"
   convolution_param {
      num_output: 48
      kernel_size: 1
      stride: 1
      pad: 0
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire6_squeeze"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire6_squeeze"
   top: "fire6_squeeze"
}
layer {
   name: "fire6_expand_1x1"
   type: "Convolution"
   bottom: "fire6_squeeze"
   top: "fire6_expand_1x1"
   convolution_param {
      num_output: 192
      kernel_size: 1
      stride: 1
      pad: 0
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire6_expand_1x1"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire6_expand_1x1"
   top: "fire6_expand_1x1"
}
layer {
   name: "fire6_expand_3x3"
   type: "Convolution"
   bottom: "fire6_squeeze"
   top: "fire6_expand_3x3"
   convolution_param {
      num_output: 192
      kernel_size: 3
      stride: 1
      pad: 1
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire6_expand_3x3"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire6_expand_3x3"
   top: "fire6_expand_3x3"
}
layer {
   name: "fire6"
   type: "Concat"
   bottom: "fire6_expand_1x1"
   bottom: "fire6_expand_3x3"
   top: "fire6"
   concat_param {
      axis: 1
   }
}
layer {
   name: "fire7_squeeze"
   type: "Convolution"
   bottom: "fire6"
   top: "fire7_squeeze"
   convolution_param {
      num_output: 48
      kernel_size: 1
      stride: 1
      pad: 0
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire7_squeeze"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire7_squeeze"
   top: "fire7_squeeze"
}
layer {
   name: "fire7_expand_1x1"
   type: "Convolution"
   bottom: "fire7_squeeze"
   top: "fire7_expand_1x1"
   convolution_param {
      num_output: 192
      kernel_size: 1
      stride: 1
      pad: 0
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire7_expand_1x1"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire7_expand_1x1"
   top: "fire7_expand_1x1"
}
layer {
   name: "fire7_expand_3x3"
   type: "Convolution"
   bottom: "fire7_squeeze"
   top: "fire7_expand_3x3"
   convolution_param {
      num_output: 192
      kernel_size: 3
      stride: 1
      pad: 1
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire7_expand_3x3"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire7_expand_3x3"
   top: "fire7_expand_3x3"
}
layer {
   name: "fire7"
   type: "Concat"
   bottom: "fire7_expand_1x1"
   bottom: "fire7_expand_3x3"
   top: "fire7"
   concat_param {
      axis: 1
   }
}
layer {
   name: "fire8_squeeze"
   type: "Convolution"
   bottom: "fire7"
   top: "fire8_squeeze"
   convolution_param {
      num_output: 64
      kernel_size: 1
      stride: 1
      pad: 0
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire8_squeeze"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire8_squeeze"
   top: "fire8_squeeze"
}
layer {
   name: "fire8_expand_1x1"
   type: "Convolution"
   bottom: "fire8_squeeze"
   top: "fire8_expand_1x1"
   convolution_param {
      num_output: 256
      kernel_size: 1
      stride: 1
      pad: 0
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire8_expand_1x1"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire8_expand_1x1"
   top: "fire8_expand_1x1"
}
layer {
   name: "fire8_expand_3x3"
   type: "Convolution"
   bottom: "fire8_squeeze"
   top: "fire8_expand_3x3"
   convolution_param {
      num_output: 256
      kernel_size: 3
      stride: 1
      pad: 1
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire8_expand_3x3"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire8_expand_3x3"
   top: "fire8_expand_3x3"
}
layer {
   name: "fire8"
   type: "Concat"
   bottom: "fire8_expand_1x1"
   bottom: "fire8_expand_3x3"
   top: "fire8"
   concat_param {
      axis: 1
   }
}
layer {
   name: "pool8"
   type: "Pooling"
   bottom: "fire8"
   top: "pool8"
   pooling_param {
      pool: MAX
      kernel_size: 3
      stride: 2
   }
}
layer {
   name: "fire9_squeeze"
   type: "Convolution"
   bottom: "pool8"
   top: "fire9_squeeze"
   convolution_param {
      num_output: 64
      kernel_size: 1
      stride: 1
      pad: 0
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire9_squeeze"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire9_squeeze"
   top: "fire9_squeeze"
}
layer {
   name: "fire9_expand_1x1"
   type: "Convolution"
   bottom: "fire9_squeeze"
   top: "fire9_expand_1x1"
   convolution_param {
      num_output: 256
      kernel_size: 1
      stride: 1
      pad: 0
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire9_expand_1x1"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire9_expand_1x1"
   top: "fire9_expand_1x1"
}
layer {
   name: "fire9_expand_3x3"
   type: "Convolution"
   bottom: "fire9_squeeze"
   top: "fire9_expand_3x3"
   convolution_param {
      num_output: 256
      kernel_size: 3
      stride: 1
      pad: 1
      weight_filler {
         type: "xavier"
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_fire9_expand_3x3"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "fire9_expand_3x3"
   top: "fire9_expand_3x3"
}
layer {
   name: "fire9"
   type: "Concat"
   bottom: "fire9_expand_1x1"
   bottom: "fire9_expand_3x3"
   top: "fire9"
   concat_param {
      axis: 1
   }
}
layer {
   name: "conv10"
   type: "Convolution"
   bottom: "fire9"
   top: "conv10"
   convolution_param {
      num_output: 1000
      kernel_size: 1
      stride: 1
      weight_filler {
         type: "gaussian"
         mean: 0.0
         std: 0.01
      }
      bias_filler {
         type: "constant"
         value: 0.01
      }
   }
}
layer {
   name: "rect_conv10"
   type: "ReLU"
   relu_param {
      negative_slope: 0.01
   }
   bottom: "conv10"
   top: "conv10"
}
layer {
   name: "pool10"
   type: "Pooling"
   bottom: "conv10"
   top: "pool10"
   pooling_param {
      pool: AVE
      global_pooling: true
   }
}
layer {
   name: "predictions"
   type: "Softmax"
   bottom: "pool10"
   top: "predictions"
   softmax_param {
      axis: 1
   }
}
