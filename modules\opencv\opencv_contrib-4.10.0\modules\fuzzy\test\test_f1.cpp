/*M///////////////////////////////////////////////////////////////////////////////////////
//
//  IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
//
//  By downloading, copying, installing or using the software you agree to this license.
//  If you do not agree to this license, do not download, install,
//  copy or use the software.
//
//
//                           License Agreement
//                For Open Source Computer Vision Library
//
// Copyright (C) 2015, University of Ostrava, Institute for Research and Applications of Fuzzy Modeling,
// <PERSON>, all rights reserved. Third party copyrights are property of their respective owners.
//
// Redistribution and use in source and binary forms, with or without modification,
// are permitted provided that the following conditions are met:
//
//   * Redistribution's of source code must retain the above copyright notice,
//     this list of conditions and the following disclaimer.
//
//   * Redistribution's in binary form must reproduce the above copyright notice,
//     this list of conditions and the following disclaimer in the documentation
//     and/or other materials provided with the distribution.
//
//   * The name of the copyright holders may not be used to endorse or promote products
//     derived from this software without specific prior written permission.
//
// This software is provided by the copyright holders and contributors "as is" and
// any express or implied warranties, including, but not limited to, the implied
// warranties of merchantability and fitness for a particular purpose are disclaimed.
// In no event shall the Intel Corporation or contributors be liable for any direct,
// indirect, incidental, special, exemplary, or consequential damages
// (including, but not limited to, procurement of substitute goods or services;
// loss of use, data, or profits; or business interruption) however caused
// and on any theory of liability, whether in contract, strict liability,
// or tort (including negligence or otherwise) arising in any way out of
// the use of this software, even if advised of the possibility of such damage.
//
//M*/

#include "test_precomp.hpp"

namespace opencv_test { namespace {

TEST(fuzzy_f1, elements)
{
    float arI[16][16] =
    {
        {0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255},
        {0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255},
        {0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255},
        {0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255},
        {0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255},
        {0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255},
        {0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255},
        {0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255},
        {0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255},
        {0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255},
        {0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255},
        {0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255},
        {0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255},
        {0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255},
        {0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255},
        {0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255}
    };
    Mat I = Mat(16, 16, CV_32F, arI);

    float arDemandedC00[9][9] =
    {
        {0, 2.5, 33.75, 80.25, 127, 173.75, 220.75, 252.25, 255},
        {0, 2.5, 33.75, 80.25, 127, 173.75, 220.75, 252.25, 255},
        {0, 2.5, 33.75, 80.25, 127, 173.75, 220.75, 252.25, 255},
        {0, 2.5, 33.75, 80.25, 127, 173.75, 220.75, 252.25, 255},
        {0, 2.5, 33.75, 80.25, 127, 173.75, 220.75, 252.25, 255},
        {0, 2.5, 33.75, 80.25, 127, 173.75, 220.75, 252.25, 255},
        {0, 2.5, 33.75, 80.25, 127, 173.75, 220.75, 252.25, 255},
        {0, 2.5, 33.75, 80.25, 127, 173.75, 220.75, 252.25, 255},
        {0, 2.5, 33.75, 80.25, 127, 173.75, 220.75, 252.25, 255}
    };
    Mat demandedC00 = Mat(9, 9, CV_32F, arDemandedC00);

    float arDemandedC10[9][9] =
    {
        {0, 5, 23.5, 23.5, 23, 23.5, 23.5, 5.5, -255},
        {0, 5, 23.5, 23.5, 23, 23.5, 23.5, 5.5, -255},
        {0, 5, 23.5, 23.5, 23, 23.5, 23.5, 5.5, -255},
        {0, 5, 23.5, 23.5, 23, 23.5, 23.5, 5.5, -255},
        {0, 5, 23.5, 23.5, 23, 23.5, 23.5, 5.5, -255},
        {0, 5, 23.5, 23.5, 23, 23.5, 23.5, 5.5, -255},
        {0, 5, 23.5, 23.5, 23, 23.5, 23.5, 5.5, -255},
        {0, 5, 23.5, 23.5, 23, 23.5, 23.5, 5.5, -255},
        {0, 5, 23.5, 23.5, 23, 23.5, 23.5, 5.5, -255}
    };
    Mat demandedC10 = Mat(9, 9, CV_32F, arDemandedC10);

    float arDemandedC01[9][9] =
    {
        {0, 2.5, 33.75, 80.25, 127, 173.75, 220.75, 252.25, 255},
        {0, 0, 0, 0, 0, 0, 0, 0, 0},
        {0, 0, 0, 0, 0, 0, 0, 0, 0},
        {0, 0, 0, 0, 0, 0, 0, 0, 0},
        {0, 0, 0, 0, 0, 0, 0, 0, 0},
        {0, 0, 0, 0, 0, 0, 0, 0, 0},
        {0, 0, 0, 0, 0, 0, 0, 0, 0},
        {0, 0, 0, 0, 0, 0, 0, 0, 0},
        {0, -2.5, -33.75, -80.25, -127, -173.75, -220.75, -252.25, -255}
    };
    Mat demandedC01 = Mat(9, 9, CV_32F, arDemandedC01);

    float arDemandedComp[45][45] =
    {
        {0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255},
        {0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -5, 0, 5, 10, 15, 20.5, 44, 67.5, 91, 114.5, 113.5, 137, 160.5, 184, 207.5, 208, 231, 254, 277, 300, 300.5, 324, 347.5, 371, 394.5, 394.5, 418, 441.5, 465, 488.5, 493.5, 499, 504.5, 510, 252.25, 1020, 765, 255, 255, 255},
        {0, 0, 0, 0, 0, -2.5, 2.5, 7.5, 12.5, 17.5, 54.25, 77.75, 101.25, 124.75, 148.25, 193.75, 217.25, 240.75, 264.25, 287.75, 335, 358, 381, 404, 427, 474.25, 497.75, 521.25, 544.75, 568.25, 615.25, 638.75, 662.25, 685.75, 709.25, 745.75, 751.25, 756.75, 762.25, 252.25, 1275, 1020, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255},
        {0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255},
        {0, 0, 0, 0, 0, -2.5, 2.5, 7.5, 12.5, 17.5, 54.25, 77.75, 101.25, 124.75, 148.25, 193.75, 217.25, 240.75, 264.25, 287.75, 335, 358, 381, 404, 427, 474.25, 497.75, 521.25, 544.75, 568.25, 615.25, 638.75, 662.25, 685.75, 709.25, 745.75, 751.25, 756.75, 762.25, 252.25, 1275, 1020, 255, 255, 255},
        {0, 0, 0, 0, 0, -5, 0, 5, 10, 15, 20.5, 44, 67.5, 91, 114.5, 113.5, 137, 160.5, 184, 207.5, 208, 231, 254, 277, 300, 300.5, 324, 347.5, 371, 394.5, 394.5, 418, 441.5, 465, 488.5, 493.5, 499, 504.5, 510, 252.25, 1020, 765, 255, 255, 255},
        {0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255},
        {0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255},
        {0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255}
    };
    Mat demandedComp = Mat(45, 45, CV_32F, arDemandedComp);

    Mat kernel;
    ft::createKernel(ft::LINEAR, 2, kernel, 1);

    Mat c00, c10, c01, f1comp;
    ft::FT12D_polynomial(I, kernel, c00, c10, c01, f1comp);

    double n1 = cvtest::norm(demandedC00, c00, NORM_INF);
    double n2 = cvtest::norm(demandedC10, c10, NORM_INF);
    double n3 = cvtest::norm(demandedC01, c01, NORM_INF);
    double n4 = cvtest::norm(demandedComp, f1comp, NORM_INF);

    EXPECT_DOUBLE_EQ(n1 + n2 + n3 + n4, 0);
}

TEST(fuzzy_f1, components)
{
    float arI[16][16] =
    {
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 }
    };
    Mat I = Mat(16, 16, CV_32F, arI);

    float arDemandedComp[45][45] =
    {
        { 0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255 },
        { 0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -5, 0, 5, 10, 15, 20.5, 44, 67.5, 91, 114.5, 113.5, 137, 160.5, 184, 207.5, 208, 231, 254, 277, 300, 300.5, 324, 347.5, 371, 394.5, 394.5, 418, 441.5, 465, 488.5, 493.5, 499, 504.5, 510, 252.25, 1020, 765, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -2.5, 2.5, 7.5, 12.5, 17.5, 54.25, 77.75, 101.25, 124.75, 148.25, 193.75, 217.25, 240.75, 264.25, 287.75, 335, 358, 381, 404, 427, 474.25, 497.75, 521.25, 544.75, 568.25, 615.25, 638.75, 662.25, 685.75, 709.25, 745.75, 751.25, 756.75, 762.25, 252.25, 1275, 1020, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -2.5, 2.5, 7.5, 12.5, 17.5, 54.25, 77.75, 101.25, 124.75, 148.25, 193.75, 217.25, 240.75, 264.25, 287.75, 335, 358, 381, 404, 427, 474.25, 497.75, 521.25, 544.75, 568.25, 615.25, 638.75, 662.25, 685.75, 709.25, 745.75, 751.25, 756.75, 762.25, 252.25, 1275, 1020, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -5, 0, 5, 10, 15, 20.5, 44, 67.5, 91, 114.5, 113.5, 137, 160.5, 184, 207.5, 208, 231, 254, 277, 300, 300.5, 324, 347.5, 371, 394.5, 394.5, 418, 441.5, 465, 488.5, 493.5, 499, 504.5, 510, 252.25, 1020, 765, 255, 255, 255 },
        { 0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255 },
        { 0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255 },
        { 0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255 }
    };
    Mat demandedComp = Mat(45, 45, CV_32F, arDemandedComp);

    Mat kernel;
    ft::createKernel(ft::LINEAR, 2, kernel, 1);

    Mat f1comp;
    ft::FT12D_components(I, kernel, f1comp);

    double n1 = cvtest::norm(demandedComp, f1comp, NORM_INF);

    EXPECT_DOUBLE_EQ(n1, 0);
}

TEST(fuzzy_f1, process)
{
    float arI[16][16] =
    {
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 },
        { 0, 0, 0, 10, 34, 57, 80, 104, 127, 150, 174, 197, 221, 244, 255, 255 }
    };
    Mat I = Mat(16, 16, CV_32F, arI);

    float arDemandedO[16][16] =
    {
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -0.625, 3.75, 17.9375, 50.625, 85.5, 120.375, 155.6875, 190.5, 225.3125, 260.625, 295.875, 331.125, 363.75, 378.375, 510.6875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -0.625, 3.75, 17.9375, 50.625, 85.5, 120.375, 155.6875, 190.5, 225.3125, 260.625, 295.875, 331.125, 363.75, 378.375, 510.6875 }
    };
    Mat demandedO = Mat(16, 16, CV_32F, arDemandedO);

    Mat kernel;
    ft::createKernel(ft::LINEAR, 2, kernel, 1);

    Mat O;
    ft::FT12D_process(I, kernel, O);

    double n1 = cvtest::norm(demandedO, O, NORM_INF);

    EXPECT_DOUBLE_EQ(n1, 0);
}

TEST(fuzzy_f1, inversion)
{
    float arDemandedO[16][16] =
    {
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -0.625, 3.75, 17.9375, 50.625, 85.5, 120.375, 155.6875, 190.5, 225.3125, 260.625, 295.875, 331.125, 363.75, 378.375, 510.6875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -1.25, 2.5, 8.875, 33.75, 57, 80.25, 103.875, 127, 150.125, 173.75, 197.25, 220.75, 245.5, 252.25, 383.875 },
        { 0, -0.625, 3.75, 17.9375, 50.625, 85.5, 120.375, 155.6875, 190.5, 225.3125, 260.625, 295.875, 331.125, 363.75, 378.375, 510.6875 }
    };
    Mat demandedO = Mat(16, 16, CV_32F, arDemandedO);

    float arComp[45][45] =
    {
        { 0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255 },
        { 0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -5, 0, 5, 10, 15, 20.5, 44, 67.5, 91, 114.5, 113.5, 137, 160.5, 184, 207.5, 208, 231, 254, 277, 300, 300.5, 324, 347.5, 371, 394.5, 394.5, 418, 441.5, 465, 488.5, 493.5, 499, 504.5, 510, 252.25, 1020, 765, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -2.5, 2.5, 7.5, 12.5, 17.5, 54.25, 77.75, 101.25, 124.75, 148.25, 193.75, 217.25, 240.75, 264.25, 287.75, 335, 358, 381, 404, 427, 474.25, 497.75, 521.25, 544.75, 568.25, 615.25, 638.75, 662.25, 685.75, 709.25, 745.75, 751.25, 756.75, 762.25, 252.25, 1275, 1020, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -7.5, -2.5, 2.5, 7.5, 12.5, -13.25, 10.25, 33.75, 57.25, 80.75, 33.25, 56.75, 80.25, 103.75, 127.25, 81, 104, 127, 150, 173, 126.75, 150.25, 173.75, 197.25, 220.75, 173.75, 197.25, 220.75, 244.25, 267.75, 241.25, 246.75, 252.25, 257.75, 252.25, 765, 510, 255, 255, 255 },
        { 0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -2.5, 2.5, 7.5, 12.5, 17.5, 54.25, 77.75, 101.25, 124.75, 148.25, 193.75, 217.25, 240.75, 264.25, 287.75, 335, 358, 381, 404, 427, 474.25, 497.75, 521.25, 544.75, 568.25, 615.25, 638.75, 662.25, 685.75, 709.25, 745.75, 751.25, 756.75, 762.25, 252.25, 1275, 1020, 255, 255, 255 },
        { 0, 0, 0, 0, 0, -5, 0, 5, 10, 15, 20.5, 44, 67.5, 91, 114.5, 113.5, 137, 160.5, 184, 207.5, 208, 231, 254, 277, 300, 300.5, 324, 347.5, 371, 394.5, 394.5, 418, 441.5, 465, 488.5, 493.5, 499, 504.5, 510, 252.25, 1020, 765, 255, 255, 255 },
        { 0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255 },
        { 0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255 },
        { 0, 0, 0, 0, 0, 2.5, 2.5, 2.5, 2.5, 2.5, 33.75, 33.75, 33.75, 33.75, 33.75, 80.25, 80.25, 80.25, 80.25, 80.25, 127, 127, 127, 127, 127, 173.75, 173.75, 173.75, 173.75, 173.75, 220.75, 220.75, 220.75, 220.75, 220.75, 252.25, 252.25, 252.25, 252.25, 252.25, 255, 255, 255, 255, 255 }
    };
    Mat comp = Mat(45, 45, CV_32F, arComp);

    Mat kernel;
    ft::createKernel(ft::LINEAR, 2, kernel, 1);

    Mat O;
    ft::FT12D_inverseFT(comp, kernel, O, 16, 16);

    double n1 = cvtest::norm(demandedO, O, NORM_INF);

    EXPECT_DOUBLE_EQ(n1, 0);
}

}} // namespace
