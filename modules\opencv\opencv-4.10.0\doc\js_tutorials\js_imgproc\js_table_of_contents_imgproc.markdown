Image Processing {#tutorial_js_table_of_contents_imgproc}
==========================

-   @subpage tutorial_js_colorspaces

    Learn how to change images between different color spaces.

-   @subpage tutorial_js_geometric_transformations

    Learn how to apply different geometric transformations to images like rotation, translation etc.

-   @subpage tutorial_js_thresholding

    Learn
    how to convert images to binary images using global thresholding, Adaptive thresholding, <PERSON><PERSON>'s
    binarization etc.

-   @subpage tutorial_js_filtering

    Learn
    how to blur the images, filter the images with custom kernels etc.

-   @subpage tutorial_js_morphological_ops

    Learn about morphological transformations like Erosion, Dilation, Opening, Closing etc.

-   @subpage tutorial_js_gradients

    Learn
    how to find image gradients, edges etc.

-   @subpage tutorial_js_canny

    Learn
    how to find edges with Canny Edge Detection.

-   @subpage tutorial_js_pyramids

    Learn about image pyramids and how to use them for image blending.

-   @subpage tutorial_js_table_of_contents_contours

    Learn
    about Contours in OpenCV.js.

-   @subpage tutorial_js_table_of_contents_histograms

    Learn
    about histograms in OpenCV.js.

-   @subpage tutorial_js_table_of_contents_transforms

    Learn
    different Image Transforms in OpenCV.js like Fourier Transform, Cosine Transform etc.

-   @subpage tutorial_js_template_matching

    Learn
    how to search for an object in an image using Template Matching.

-   @subpage tutorial_js_houghlines

    Learn how to detect lines in an image.

-   @subpage tutorial_js_houghcircles

    Learn how to detect circles in an image.

-   @subpage tutorial_js_watershed

    Learn how to segment images with watershed segmentation.

-   @subpage tutorial_js_grabcut

    Learn how to extract foreground with GrabCut algorithm.

-   @subpage tutorial_js_imgproc_camera

    Learn image processing for video capture.

-   @subpage tutorial_js_intelligent_scissors

    Learn how to use IntelligentScissors tool for image segmentation task.
