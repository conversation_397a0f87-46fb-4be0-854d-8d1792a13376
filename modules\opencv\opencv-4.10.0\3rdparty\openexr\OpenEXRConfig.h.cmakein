//
// Define and set to 1 if the target system supports a proc filesystem
// compatible with the Linux kernel's proc filesystem.  Note that this
// is only used by a program in the IlmImfTest test suite, it's not
// used by any OpenEXR library or application code.
//

#cmakedefine OPENEXR_IMF_HAVE_LINUX_PROCFS

//
// Define and set to 1 if the target system is a Darwin-based system
// (e.g., OS X).
//

#cmakedefine OPENEXR_IMF_HAVE_DARWIN

//
// Define and set to 1 if the target system has a complete <iomanip>
// implementation, specifically if it supports the std::right
// formatter.
//

#cmakedefine OPENEXR_IMF_HAVE_COMPLETE_IOMANIP

//
// Define and set to 1 if the target system has support for large
// stack sizes.
//

#cmakedefine OPENEXR_IMF_HAVE_LARGE_STACK

//
// Define if we can support GCC style inline asm with AVX instructions
//

#cmakedefine OPENEXR_IMF_HAVE_GCC_INLINE_ASM_AVX

//
// Define if we can use sysconf(_SC_NPROCESSORS_ONLN) to get CPU count
//

#cmakedefine OPENEXR_IMF_HAVE_SYSCONF_NPROCESSORS_ONLN

//
// Current internal library namepace name
//
#define OPENEXR_IMF_INTERNAL_NAMESPACE_CUSTOM @OPENEXR_IMF_INTERNAL_NAMESPACE_CUSTOM@
#define OPENEXR_IMF_INTERNAL_NAMESPACE @OPENEXR_IMF_INTERNAL_NAMESPACE@

//
// Current public user namepace name
//

#define OPENEXR_IMF_NAMESPACE_CUSTOM @OPENEXR_IMF_NAMESPACE_CUSTOM@
#define OPENEXR_IMF_NAMESPACE @OPENEXR_IMF_NAMESPACE@

//
// Version string for runtime access
//

#define OPENEXR_VERSION_STRING @OPENEXR_VERSION_STRING@
#define OPENEXR_PACKAGE_STRING @OPENEXR_PACKAGE_STRING@

#define OPENEXR_VERSION_MAJOR @OPENEXR_VERSION_MAJOR@
#define OPENEXR_VERSION_MINOR @OPENEXR_VERSION_MINOR@
#define OPENEXR_VERSION_PATCH @OPENEXR_VERSION_PATCH@

// Version as a single hex number, e.g. 0x01000300 == 1.0.3
#define OPENEXR_VERSION_HEX ((OPENEXR_VERSION_MAJOR << 24) | \
                             (OPENEXR_VERSION_MINOR << 16) | \
                             (OPENEXR_VERSION_PATCH <<  8))

