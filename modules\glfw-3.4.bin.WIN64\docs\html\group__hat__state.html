<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Joystick hat states</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle"><div class="title">Joystick hat states<div class="ingroups"><a class="el" href="group__input.html">Input reference</a></div></div></div>
</div><!--header-->
<div class="contents">
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>See <a class="el" href="input_guide.html#joystick_hat">joystick hat input</a> for how these are used. </p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:gae2c0bcb7aec609e4736437554f6638fd" id="r_gae2c0bcb7aec609e4736437554f6638fd"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#gae2c0bcb7aec609e4736437554f6638fd">GLFW_HAT_CENTERED</a>&#160;&#160;&#160;0</td></tr>
<tr class="separator:gae2c0bcb7aec609e4736437554f6638fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8c9720c76cd1b912738159ed74c85b36" id="r_ga8c9720c76cd1b912738159ed74c85b36"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#ga8c9720c76cd1b912738159ed74c85b36">GLFW_HAT_UP</a>&#160;&#160;&#160;1</td></tr>
<tr class="separator:ga8c9720c76cd1b912738159ed74c85b36"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga252586e3bbde75f4b0e07ad3124867f5" id="r_ga252586e3bbde75f4b0e07ad3124867f5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#ga252586e3bbde75f4b0e07ad3124867f5">GLFW_HAT_RIGHT</a>&#160;&#160;&#160;2</td></tr>
<tr class="separator:ga252586e3bbde75f4b0e07ad3124867f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad60d1fd0dc85c18f2642cbae96d3deff" id="r_gad60d1fd0dc85c18f2642cbae96d3deff"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#gad60d1fd0dc85c18f2642cbae96d3deff">GLFW_HAT_DOWN</a>&#160;&#160;&#160;4</td></tr>
<tr class="separator:gad60d1fd0dc85c18f2642cbae96d3deff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac775f4b3154fdf5db93eb432ba546dff" id="r_gac775f4b3154fdf5db93eb432ba546dff"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#gac775f4b3154fdf5db93eb432ba546dff">GLFW_HAT_LEFT</a>&#160;&#160;&#160;8</td></tr>
<tr class="separator:gac775f4b3154fdf5db93eb432ba546dff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga94aea0ae241a8b902883536c592ee693" id="r_ga94aea0ae241a8b902883536c592ee693"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#ga94aea0ae241a8b902883536c592ee693">GLFW_HAT_RIGHT_UP</a>&#160;&#160;&#160;(<a class="el" href="group__hat__state.html#ga252586e3bbde75f4b0e07ad3124867f5">GLFW_HAT_RIGHT</a> | <a class="el" href="group__hat__state.html#ga8c9720c76cd1b912738159ed74c85b36">GLFW_HAT_UP</a>)</td></tr>
<tr class="separator:ga94aea0ae241a8b902883536c592ee693"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad7f0e4f52fd68d734863aaeadab3a3f5" id="r_gad7f0e4f52fd68d734863aaeadab3a3f5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#gad7f0e4f52fd68d734863aaeadab3a3f5">GLFW_HAT_RIGHT_DOWN</a>&#160;&#160;&#160;(<a class="el" href="group__hat__state.html#ga252586e3bbde75f4b0e07ad3124867f5">GLFW_HAT_RIGHT</a> | <a class="el" href="group__hat__state.html#gad60d1fd0dc85c18f2642cbae96d3deff">GLFW_HAT_DOWN</a>)</td></tr>
<tr class="separator:gad7f0e4f52fd68d734863aaeadab3a3f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga638f0e20dc5de90de21a33564e8ce129" id="r_ga638f0e20dc5de90de21a33564e8ce129"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#ga638f0e20dc5de90de21a33564e8ce129">GLFW_HAT_LEFT_UP</a>&#160;&#160;&#160;(<a class="el" href="group__hat__state.html#gac775f4b3154fdf5db93eb432ba546dff">GLFW_HAT_LEFT</a>  | <a class="el" href="group__hat__state.html#ga8c9720c76cd1b912738159ed74c85b36">GLFW_HAT_UP</a>)</td></tr>
<tr class="separator:ga638f0e20dc5de90de21a33564e8ce129"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga76c02baf1ea345fcbe3e8ff176a73e19" id="r_ga76c02baf1ea345fcbe3e8ff176a73e19"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__hat__state.html#ga76c02baf1ea345fcbe3e8ff176a73e19">GLFW_HAT_LEFT_DOWN</a>&#160;&#160;&#160;(<a class="el" href="group__hat__state.html#gac775f4b3154fdf5db93eb432ba546dff">GLFW_HAT_LEFT</a>  | <a class="el" href="group__hat__state.html#gad60d1fd0dc85c18f2642cbae96d3deff">GLFW_HAT_DOWN</a>)</td></tr>
<tr class="separator:ga76c02baf1ea345fcbe3e8ff176a73e19"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="gae2c0bcb7aec609e4736437554f6638fd" name="gae2c0bcb7aec609e4736437554f6638fd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gae2c0bcb7aec609e4736437554f6638fd">&#9670;&#160;</a></span>GLFW_HAT_CENTERED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_HAT_CENTERED&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga8c9720c76cd1b912738159ed74c85b36" name="ga8c9720c76cd1b912738159ed74c85b36"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8c9720c76cd1b912738159ed74c85b36">&#9670;&#160;</a></span>GLFW_HAT_UP</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_HAT_UP&#160;&#160;&#160;1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga252586e3bbde75f4b0e07ad3124867f5" name="ga252586e3bbde75f4b0e07ad3124867f5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga252586e3bbde75f4b0e07ad3124867f5">&#9670;&#160;</a></span>GLFW_HAT_RIGHT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_HAT_RIGHT&#160;&#160;&#160;2</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gad60d1fd0dc85c18f2642cbae96d3deff" name="gad60d1fd0dc85c18f2642cbae96d3deff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad60d1fd0dc85c18f2642cbae96d3deff">&#9670;&#160;</a></span>GLFW_HAT_DOWN</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_HAT_DOWN&#160;&#160;&#160;4</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gac775f4b3154fdf5db93eb432ba546dff" name="gac775f4b3154fdf5db93eb432ba546dff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac775f4b3154fdf5db93eb432ba546dff">&#9670;&#160;</a></span>GLFW_HAT_LEFT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_HAT_LEFT&#160;&#160;&#160;8</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga94aea0ae241a8b902883536c592ee693" name="ga94aea0ae241a8b902883536c592ee693"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga94aea0ae241a8b902883536c592ee693">&#9670;&#160;</a></span>GLFW_HAT_RIGHT_UP</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_HAT_RIGHT_UP&#160;&#160;&#160;(<a class="el" href="group__hat__state.html#ga252586e3bbde75f4b0e07ad3124867f5">GLFW_HAT_RIGHT</a> | <a class="el" href="group__hat__state.html#ga8c9720c76cd1b912738159ed74c85b36">GLFW_HAT_UP</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="gad7f0e4f52fd68d734863aaeadab3a3f5" name="gad7f0e4f52fd68d734863aaeadab3a3f5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad7f0e4f52fd68d734863aaeadab3a3f5">&#9670;&#160;</a></span>GLFW_HAT_RIGHT_DOWN</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_HAT_RIGHT_DOWN&#160;&#160;&#160;(<a class="el" href="group__hat__state.html#ga252586e3bbde75f4b0e07ad3124867f5">GLFW_HAT_RIGHT</a> | <a class="el" href="group__hat__state.html#gad60d1fd0dc85c18f2642cbae96d3deff">GLFW_HAT_DOWN</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga638f0e20dc5de90de21a33564e8ce129" name="ga638f0e20dc5de90de21a33564e8ce129"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga638f0e20dc5de90de21a33564e8ce129">&#9670;&#160;</a></span>GLFW_HAT_LEFT_UP</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_HAT_LEFT_UP&#160;&#160;&#160;(<a class="el" href="group__hat__state.html#gac775f4b3154fdf5db93eb432ba546dff">GLFW_HAT_LEFT</a>  | <a class="el" href="group__hat__state.html#ga8c9720c76cd1b912738159ed74c85b36">GLFW_HAT_UP</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ga76c02baf1ea345fcbe3e8ff176a73e19" name="ga76c02baf1ea345fcbe3e8ff176a73e19"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga76c02baf1ea345fcbe3e8ff176a73e19">&#9670;&#160;</a></span>GLFW_HAT_LEFT_DOWN</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GLFW_HAT_LEFT_DOWN&#160;&#160;&#160;(<a class="el" href="group__hat__state.html#gac775f4b3154fdf5db93eb432ba546dff">GLFW_HAT_LEFT</a>  | <a class="el" href="group__hat__state.html#gad60d1fd0dc85c18f2642cbae96d3deff">GLFW_HAT_DOWN</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
