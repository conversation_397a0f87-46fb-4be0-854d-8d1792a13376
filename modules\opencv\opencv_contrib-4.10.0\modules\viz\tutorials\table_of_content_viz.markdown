OpenCV Viz {#tutorial_table_of_content_viz}
==========

-   @subpage tutorial_launching_viz

    *Languages:* C++

    *Compatibility:* \> OpenCV 3.0.0

    *Author:* <PERSON><PERSON>

    You will learn how to launch a viz window.

-   @subpage tutorial_widget_pose

    *Languages:* C++

    *Compatibility:* \> OpenCV 3.0.0

    *Author:* <PERSON><PERSON>

    You will learn how to change pose of a widget.

-   @subpage tutorial_transformations

    *Languages:* C++

    *Compatibility:* \> OpenCV 3.0.0

    *Author:* <PERSON>an <PERSON>

    You will learn how to transform between global and camera frames.

-   @subpage tutorial_creating_widgets

    *Languages:* C++

    *Compatibility:* \> OpenCV 3.0.0

    *Author:* <PERSON><PERSON>

    You will learn how to create your own widgets.

-   @subpage tutorial_histo3D

    *Languages:* C++

    *Compatibility:* \> OpenCV 3.0.0

    *Author:* <PERSON> will learn how to plot a 3D histogram.
