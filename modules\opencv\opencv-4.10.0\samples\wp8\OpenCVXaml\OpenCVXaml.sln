﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 2013
VisualStudioVersion = 12.0.31101.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OpenCVXaml", "OpenCVXaml\OpenCVXaml.csproj", "{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "OpenCVComponent", "OpenCVComponent\OpenCVComponent.vcxproj", "{EADFF7B8-E6C3-4F34-9B33-014B3035C595}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|ARM = Debug|ARM
		Debug|Mixed Platforms = Debug|Mixed Platforms
		Debug|Win32 = Debug|Win32
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|ARM = Release|ARM
		Release|Mixed Platforms = Release|Mixed Platforms
		Release|Win32 = Release|Win32
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Debug|ARM.ActiveCfg = Debug|ARM
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Debug|ARM.Build.0 = Debug|ARM
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Debug|ARM.Deploy.0 = Debug|ARM
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Debug|Mixed Platforms.ActiveCfg = Debug|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Debug|Mixed Platforms.Build.0 = Debug|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Debug|Mixed Platforms.Deploy.0 = Debug|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Debug|Win32.ActiveCfg = Debug|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Debug|Win32.Build.0 = Debug|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Debug|Win32.Deploy.0 = Debug|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Debug|x86.ActiveCfg = Debug|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Debug|x86.Build.0 = Debug|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Debug|x86.Deploy.0 = Debug|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Release|Any CPU.Build.0 = Release|Any CPU
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Release|ARM.ActiveCfg = Release|ARM
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Release|ARM.Build.0 = Release|ARM
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Release|ARM.Deploy.0 = Release|ARM
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Release|Mixed Platforms.ActiveCfg = Release|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Release|Mixed Platforms.Build.0 = Release|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Release|Mixed Platforms.Deploy.0 = Release|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Release|Win32.ActiveCfg = Release|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Release|Win32.Build.0 = Release|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Release|Win32.Deploy.0 = Release|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Release|x86.ActiveCfg = Release|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Release|x86.Build.0 = Release|x86
		{AF139C56-D9C7-4AFE-8972-E5B30AABA1BC}.Release|x86.Deploy.0 = Release|x86
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Debug|Any CPU.ActiveCfg = Debug|Win32
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Debug|ARM.ActiveCfg = Debug|ARM
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Debug|ARM.Build.0 = Debug|ARM
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Debug|Mixed Platforms.ActiveCfg = Debug|Win32
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Debug|Mixed Platforms.Build.0 = Debug|Win32
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Debug|Win32.ActiveCfg = Debug|Win32
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Debug|Win32.Build.0 = Debug|Win32
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Debug|x86.ActiveCfg = Debug|Win32
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Debug|x86.Build.0 = Debug|Win32
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Release|Any CPU.ActiveCfg = Release|Win32
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Release|ARM.ActiveCfg = Release|ARM
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Release|ARM.Build.0 = Release|ARM
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Release|Mixed Platforms.ActiveCfg = Release|Win32
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Release|Mixed Platforms.Build.0 = Release|Win32
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Release|Win32.ActiveCfg = Release|Win32
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Release|Win32.Build.0 = Release|Win32
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Release|x86.ActiveCfg = Release|Win32
		{EADFF7B8-E6C3-4F34-9B33-014B3035C595}.Release|x86.Build.0 = Release|Win32
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
