cuda-python==12.2.0; python_version <= "3.10"
cuda-python==12.6.0; python_version >= "3.11"
cupy-cuda12x
numba
triton; platform_system != "Windows"
torch
--extra-index-url https://pypi.ngc.nvidia.com
polygraphy
colored
numpy==1.23.5; (platform_system != "Windows" and python_version <= "3.10")
numpy==1.26.4; (platform_system != "Windows" and python_version >= "3.11")
onnx==1.16.0; platform_system == "Windows"
--extra-index-url https://pypi.ngc.nvidia.com
onnx-graphsurgeon
pywin32; platform_system == "Windows"
pyyaml==6.0.1
requests==2.32.2
tqdm==4.66.4
