﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_adaptive_manifold.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_bilateral_texture_filter.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_disparity_wls_filter.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_domain_transform.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_edgepreserving_filter.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_fast_hough_transform.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_fgs_filter.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_find_ellipses.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_guided_filter.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_joint_bilateral_filter.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_l0_smooth.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_main.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_radon_transform.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_ridge_detection_filter.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_rolling_guidance_filter.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_run_length_morphology.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_thining.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_weighted_median_filter.cpp">
      <Filter>opencv_ximgproc\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\ximgproc\perf\perf_precomp.hpp">
      <Filter>opencv_ximgproc\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_ximgproc">
      <UniqueIdentifier>{E7C9BCAF-38FF-31F5-9C51-A9A7A2D0CFA5}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_ximgproc\Include">
      <UniqueIdentifier>{33797123-3805-3277-A1C9-D6D079BD724A}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_ximgproc\Src">
      <UniqueIdentifier>{764D163A-2D09-32AB-AF1D-9696E9C8392F}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
