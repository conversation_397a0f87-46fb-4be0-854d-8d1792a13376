﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 2013
VisualStudioVersion = 12.0.31101.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "OcvTransform", "..\ImageManipulations\MediaExtensions\OcvTransform\OcvTransform.vcxproj", "{BA69218F-DA5C-4D14-A78D-21A9E4DEC669}"
EndProject
Project("{262852C6-CD72-467D-83FE-5EEB1973A190}") = "MediaCaptureJavaScript", "MediaCaptureJavaScript.jsproj", "{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|ARM = Debug|ARM
		Debug|Win32 = Debug|Win32
		Debug|x64 = Debug|x64
		Release|Any CPU = Release|Any CPU
		Release|ARM = Release|ARM
		Release|Win32 = Release|Win32
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Debug|Any CPU.ActiveCfg = Debug|Win32
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Debug|ARM.ActiveCfg = Debug|ARM
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Debug|ARM.Build.0 = Debug|ARM
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Debug|ARM.Deploy.0 = Debug|ARM
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Debug|Win32.ActiveCfg = Debug|Win32
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Debug|Win32.Build.0 = Debug|Win32
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Debug|Win32.Deploy.0 = Debug|Win32
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Debug|x64.ActiveCfg = Debug|x64
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Debug|x64.Build.0 = Debug|x64
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Debug|x64.Deploy.0 = Debug|x64
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Release|Any CPU.ActiveCfg = Release|Win32
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Release|ARM.ActiveCfg = Release|ARM
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Release|ARM.Build.0 = Release|ARM
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Release|ARM.Deploy.0 = Release|ARM
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Release|Win32.ActiveCfg = Release|Win32
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Release|Win32.Build.0 = Release|Win32
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Release|Win32.Deploy.0 = Release|Win32
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Release|x64.ActiveCfg = Release|x64
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Release|x64.Build.0 = Release|x64
		{C5B886A7-8300-46FF-B533-9613DE2AF637}.Release|x64.Deploy.0 = Release|x64
		{BA69218F-DA5C-4D14-A78D-21A9E4DEC669}.Debug|Any CPU.ActiveCfg = Debug|Win32
		{BA69218F-DA5C-4D14-A78D-21A9E4DEC669}.Debug|ARM.ActiveCfg = Debug|ARM
		{BA69218F-DA5C-4D14-A78D-21A9E4DEC669}.Debug|ARM.Build.0 = Debug|ARM
		{BA69218F-DA5C-4D14-A78D-21A9E4DEC669}.Debug|Win32.ActiveCfg = Debug|Win32
		{BA69218F-DA5C-4D14-A78D-21A9E4DEC669}.Debug|Win32.Build.0 = Debug|Win32
		{BA69218F-DA5C-4D14-A78D-21A9E4DEC669}.Debug|x64.ActiveCfg = Debug|x64
		{BA69218F-DA5C-4D14-A78D-21A9E4DEC669}.Debug|x64.Build.0 = Debug|x64
		{BA69218F-DA5C-4D14-A78D-21A9E4DEC669}.Release|Any CPU.ActiveCfg = Release|Win32
		{BA69218F-DA5C-4D14-A78D-21A9E4DEC669}.Release|ARM.ActiveCfg = Release|ARM
		{BA69218F-DA5C-4D14-A78D-21A9E4DEC669}.Release|ARM.Build.0 = Release|ARM
		{BA69218F-DA5C-4D14-A78D-21A9E4DEC669}.Release|Win32.ActiveCfg = Release|Win32
		{BA69218F-DA5C-4D14-A78D-21A9E4DEC669}.Release|Win32.Build.0 = Release|Win32
		{BA69218F-DA5C-4D14-A78D-21A9E4DEC669}.Release|x64.ActiveCfg = Release|x64
		{BA69218F-DA5C-4D14-A78D-21A9E4DEC669}.Release|x64.Build.0 = Release|x64
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Debug|ARM.ActiveCfg = Debug|ARM
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Debug|ARM.Build.0 = Debug|ARM
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Debug|ARM.Deploy.0 = Debug|ARM
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Debug|Win32.ActiveCfg = Debug|x86
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Debug|Win32.Build.0 = Debug|x86
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Debug|Win32.Deploy.0 = Debug|x86
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Debug|x64.ActiveCfg = Debug|x64
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Debug|x64.Build.0 = Debug|x64
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Debug|x64.Deploy.0 = Debug|x64
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Release|Any CPU.Build.0 = Release|Any CPU
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Release|ARM.ActiveCfg = Release|ARM
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Release|ARM.Build.0 = Release|ARM
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Release|ARM.Deploy.0 = Release|ARM
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Release|Win32.ActiveCfg = Release|x86
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Release|Win32.Build.0 = Release|x86
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Release|Win32.Deploy.0 = Release|x86
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Release|x64.ActiveCfg = Release|x64
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Release|x64.Build.0 = Release|x64
		{D70A3790-48CE-4E58-AF60-EBEFC22E9C7A}.Release|x64.Deploy.0 = Release|x64
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
