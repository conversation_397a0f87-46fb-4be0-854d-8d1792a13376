^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\B395EE7C0999E42287482932580994CB\GEN_OPENCV_OBJC_SOURCE_IOS.RULE
setlocal
cd D:\AI\opencv\cudabuild\modules\objc_bindings_generator\ios
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe D:/AI/opencv/opencv-4.10.0/modules/objc/generator/../generator/gen_objc.py -p D:/AI/opencv/opencv-4.10.0/modules/objc/generator/../../python/src2/gen2.py -c D:/AI/opencv/cudabuild/modules/objc_bindings_generator/gen_objc.json -t ios -f opencv2
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch D:/AI/opencv/cudabuild/CMakeFiles/dephelper/gen_opencv_objc_source_ios
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AI\OPENCV\CUDABUILD\CMAKEFILES\B813899294A05925B323F4DCB8F385F1\GEN_OPENCV_OBJC_SOURCE_IOS.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
