﻿  gapi_sample_pipelines.cpp
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(86,22): error C2653: “high_resolution_clock”: 不是类或命名空间名称
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(86,45): error C3861: “now”: 找不到标识符
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(88,22): error C2653: “high_resolution_clock”: 不是类或命名空间名称
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(88,45): error C3861: “now”: 找不到标识符
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(91,38): error C2672: “std::chrono::duration_cast”: 未找到匹配的重载函数
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_chrono.hpp(87,19):
      可能是“_To std::chrono::duration_cast(const std::chrono::duration<_Rep,_Period> &) noexcept(<expr>)”
  
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(90,24): error C2737: “elapsed_in_ms”: 必须初始化 const 对象
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(93,41): error C3536: “elapsed_in_ms”: 初始化之前无法使用
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(96,17): error C2653: “high_resolution_clock”: 不是类或命名空间名称
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(96,40): error C3861: “now”: 找不到标识符
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(98,20): error C2672: “std::chrono::duration_cast”: 未找到匹配的重载函数
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_chrono.hpp(87,19):
      可能是“_To std::chrono::duration_cast(const std::chrono::duration<_Rep,_Period> &) noexcept(<expr>)”
  
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(99,21): error C2653: “high_resolution_clock”: 不是类或命名空间名称
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(99,44): error C3861: “now”: 找不到标识符
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(565,14): error C2653: “high_resolution_clock”: 不是类或命名空间名称
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(565,37): error C3861: “now”: 找不到标识符
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(569,14): error C2653: “high_resolution_clock”: 不是类或命名空间名称
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(569,37): error C3861: “now”: 找不到标识符
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(570,32): error C2672: “std::chrono::duration_cast”: 未找到匹配的重载函数
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_chrono.hpp(87,19):
      可能是“_To std::chrono::duration_cast(const std::chrono::duration<_Rep,_Period> &) noexcept(<expr>)”
  
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(570,16): error C2737: “elapsed_in_ms”: 必须初始化 const 对象
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(572,5): error C3536: “elapsed_in_ms”: 初始化之前无法使用
D:\AI\opencv\opencv-4.10.0\modules\gapi\test\gapi_sample_pipelines.cpp(572,5): error C2737: “gtest_ar”: 必须初始化 const 对象
