TIFF Software Distribution
--------------------------
This file is just a placeholder; the entire documentation is now located
as reStructuredText in the doc directory. To view the documentation
as HTML, visit https://libtiff.gitlab.io/libtiff/ or
http://www.simplesystems.org/libtiff/ or within the release package
in the doc/html-prebuilt directory. The manual pages are
located at doc/man-prebuilt.

The release package can be downloaded at

http://download.osgeo.org/libtiff/

If you can't hack either of these options then basically what you
want to do is:

    % ./configure
    % make
    % su
    # make install

More information, email contacts, and mailing list information can be 
found online at http://www.simplesystems.org/libtiff/

Source code repository
----------------------

[GitLab](https://gitlab.com/libtiff/libtiff)

Bug database
------------

[GitLab issues](https://gitlab.com/libtiff/libtiff/issues)

Previously, the project used
[Bugzilla](http://bugzilla.maptools.org/buglist.cgi?product=libtiff). This
is no longer in use, and all remaining issues have been migrated to GitLab.

Use and Copyright
-----------------
Silicon Graphics has seen fit to allow us to give this work away.  It
is free.  There is no support or guarantee of any sort as to its
operations, correctness, or whatever.  If you do anything useful with
all or parts of it you need to honor the copyright notices.   I would
also be interested in knowing about it and, hopefully, be acknowledged.

The legal way of saying that is:

Copyright (c) 1988-1997 Sam Leffler
Copyright (c) 1991-1997 Silicon Graphics, Inc.

Permission to use, copy, modify, distribute, and sell this software and 
its documentation for any purpose is hereby granted without fee, provided
that (i) the above copyright notices and this permission notice appear in
all copies of the software and related documentation, and (ii) the names of
Sam Leffler and Silicon Graphics may not be used in any advertising or
publicity relating to the software without the specific, prior written
permission of Sam Leffler and Silicon Graphics.

THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  

IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
OF THIS SOFTWARE.
