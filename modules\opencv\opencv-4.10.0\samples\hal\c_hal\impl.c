#include "impl.h"

int wrong_add8u(const uchar* src1, size_t sz1, const uchar* src2, size_t sz2, uchar* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_add8s(const schar* src1, size_t sz1, const schar* src2, size_t sz2, schar* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_add16u(const ushort* src1, size_t sz1, const ushort* src2, size_t sz2, ushort* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_add16s(const short* src1, size_t sz1, const short* src2, size_t sz2, short* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_add32s(const int* src1, size_t sz1, const int* src2, size_t sz2, int* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_add32f(const float* src1, size_t sz1, const float* src2, size_t sz2, float* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_add64f(const double* src1, size_t sz1, const double* src2, size_t sz2, double* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_sub8u(const uchar* src1, size_t sz1, const uchar* src2, size_t sz2, uchar* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_sub8s(const schar* src1, size_t sz1, const schar* src2, size_t sz2, schar* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_sub16u(const ushort* src1, size_t sz1, const ushort* src2, size_t sz2, ushort* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_sub16s(const short* src1, size_t sz1, const short* src2, size_t sz2, short* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_sub32s(const int* src1, size_t sz1, const int* src2, size_t sz2, int* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_sub32f(const float* src1, size_t sz1, const float* src2, size_t sz2, float* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_sub64f(const double* src1, size_t sz1, const double* src2, size_t sz2, double* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_max8u(const uchar* src1, size_t sz1, const uchar* src2, size_t sz2, uchar* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_max8s(const schar* src1, size_t sz1, const schar* src2, size_t sz2, schar* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_max16u(const ushort* src1, size_t sz1, const ushort* src2, size_t sz2, ushort* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_max16s(const short* src1, size_t sz1, const short* src2, size_t sz2, short* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_max32s(const int* src1, size_t sz1, const int* src2, size_t sz2, int* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_max32f(const float* src1, size_t sz1, const float* src2, size_t sz2, float* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_max64f(const double* src1, size_t sz1, const double* src2, size_t sz2, double* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_min8u(const uchar* src1, size_t sz1, const uchar* src2, size_t sz2, uchar* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_min8s(const schar* src1, size_t sz1, const schar* src2, size_t sz2, schar* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_min16u(const ushort* src1, size_t sz1, const ushort* src2, size_t sz2, ushort* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_min16s(const short* src1, size_t sz1, const short* src2, size_t sz2, short* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_min32s(const int* src1, size_t sz1, const int* src2, size_t sz2, int* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_min32f(const float* src1, size_t sz1, const float* src2, size_t sz2, float* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_min64f(const double* src1, size_t sz1, const double* src2, size_t sz2, double* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_absdiff8u(const uchar* src1, size_t sz1, const uchar* src2, size_t sz2, uchar* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_absdiff8s(const schar* src1, size_t sz1, const schar* src2, size_t sz2, schar* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_absdiff16u(const ushort* src1, size_t sz1, const ushort* src2, size_t sz2, ushort* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_absdiff16s(const short* src1, size_t sz1, const short* src2, size_t sz2, short* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_absdiff32s(const int* src1, size_t sz1, const int* src2, size_t sz2, int* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_absdiff32f(const float* src1, size_t sz1, const float* src2, size_t sz2, float* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_absdiff64f(const double* src1, size_t sz1, const double* src2, size_t sz2, double* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_and8u(const uchar* src1, size_t sz1, const uchar* src2, size_t sz2, uchar* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_or8u(const uchar* src1, size_t sz1, const uchar* src2, size_t sz2, uchar* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_xor8u(const uchar* src1, size_t sz1, const uchar* src2, size_t sz2, uchar* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_not8u(const uchar* src1, size_t sz1, uchar* dst, size_t sz, int w, int h)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_cmp8u(const uchar* src1, size_t sz1, const uchar* src2, size_t sz2, uchar* dst, size_t sz, int w, int h, int op)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_cmp8s(const schar* src1, size_t sz1, const schar* src2, size_t sz2, uchar* dst, size_t sz, int w, int h, int op)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_cmp16u(const ushort* src1, size_t sz1, const ushort* src2, size_t sz2, uchar* dst, size_t sz, int w, int h, int op)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_cmp16s(const short* src1, size_t sz1, const short* src2, size_t sz2, uchar* dst, size_t sz, int w, int h, int op)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_cmp32s(const int* src1, size_t sz1, const int* src2, size_t sz2, uchar* dst, size_t sz, int w, int h, int op)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_cmp32f(const float* src1, size_t sz1, const float* src2, size_t sz2, uchar* dst, size_t sz, int w, int h, int op)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_cmp64f(const double* src1, size_t sz1, const double* src2, size_t sz2, uchar* dst, size_t sz, int w, int h, int op)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_mul8u(const uchar* src1, size_t sz1, const uchar* src2, size_t sz2, uchar* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_mul8s(const schar* src1, size_t sz1, const schar* src2, size_t sz2, schar* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_mul16u(const ushort* src1, size_t sz1, const ushort* src2, size_t sz2, ushort* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_mul16s(const short* src1, size_t sz1, const short* src2, size_t sz2, short* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_mul32s(const int* src1, size_t sz1, const int* src2, size_t sz2, int* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_mul32f(const float* src1, size_t sz1, const float* src2, size_t sz2, float* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_mul64f(const double* src1, size_t sz1, const double* src2, size_t sz2, double* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_div8u(const uchar* src1, size_t sz1, const uchar* src2, size_t sz2, uchar* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_div8s(const schar* src1, size_t sz1, const schar* src2, size_t sz2, schar* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_div16u(const ushort* src1, size_t sz1, const ushort* src2, size_t sz2, ushort* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_div16s(const short* src1, size_t sz1, const short* src2, size_t sz2, short* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_div32s(const int* src1, size_t sz1, const int* src2, size_t sz2, int* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_div32f(const float* src1, size_t sz1, const float* src2, size_t sz2, float* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_div64f(const double* src1, size_t sz1, const double* src2, size_t sz2, double* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_recip8u(const uchar* src2, size_t sz2, uchar* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_recip8s(const schar* src2, size_t sz2, schar* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_recip16u(const ushort* src2, size_t sz2, ushort* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_recip16s(const short* src2, size_t sz2, short* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_recip32s(const int* src2, size_t sz2, int* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_recip32f(const float* src2, size_t sz2, float* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_recip64f(const double* src2, size_t sz2, double* dst, size_t sz, int w, int h, double scale)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_addWeighted8u(const uchar* src1, size_t sz1, const uchar* src2, size_t sz2, uchar* dst, size_t sz, int w, int h, const double* scales)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_addWeighted8s(const schar* src1, size_t sz1, const schar* src2, size_t sz2, schar* dst, size_t sz, int w, int h, const double* scales)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_addWeighted16u(const ushort* src1, size_t sz1, const ushort* src2, size_t sz2, ushort* dst, size_t sz, int w, int h, const double* scales)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_addWeighted16s(const short* src1, size_t sz1, const short* src2, size_t sz2, short* dst, size_t sz, int w, int h, const double* scales)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_addWeighted32s(const int* src1, size_t sz1, const int* src2, size_t sz2, int* dst, size_t sz, int w, int h, const double* scales)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_addWeighted32f(const float* src1, size_t sz1, const float* src2, size_t sz2, float* dst, size_t sz, int w, int h, const double* scales)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}

int wrong_addWeighted64f(const double* src1, size_t sz1, const double* src2, size_t sz2, double* dst, size_t sz, int w, int h, const double* scales)
{
    return CV_HAL_ERROR_UNKNOWN; // to test how OpenCV handles errors from external HAL
}
