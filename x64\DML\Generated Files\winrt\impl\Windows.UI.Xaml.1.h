// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_UI_Xaml_1_H
#define WINRT_Windows_UI_Xaml_1_H
#include "winrt/impl/Windows.UI.Xaml.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml
{
    struct WINRT_IMPL_EMPTY_BASES IAdaptiveTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdaptiveTrigger>
    {
        IAdaptiveTrigger(std::nullptr_t = nullptr) noexcept {}
        IAdaptiveTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAdaptiveTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdaptiveTriggerFactory>
    {
        IAdaptiveTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        IAdaptiveTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAdaptiveTriggerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdaptiveTriggerStatics>
    {
        IAdaptiveTriggerStatics(std::nullptr_t = nullptr) noexcept {}
        IAdaptiveTriggerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplication :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplication>
    {
        IApplication(std::nullptr_t = nullptr) noexcept {}
        IApplication(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplication2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplication2>
    {
        IApplication2(std::nullptr_t = nullptr) noexcept {}
        IApplication2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplication3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplication3>
    {
        IApplication3(std::nullptr_t = nullptr) noexcept {}
        IApplication3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationFactory>
    {
        IApplicationFactory(std::nullptr_t = nullptr) noexcept {}
        IApplicationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationInitializationCallbackParams :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationInitializationCallbackParams>
    {
        IApplicationInitializationCallbackParams(std::nullptr_t = nullptr) noexcept {}
        IApplicationInitializationCallbackParams(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationOverrides>
    {
        IApplicationOverrides(std::nullptr_t = nullptr) noexcept {}
        IApplicationOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationOverrides2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationOverrides2>
    {
        IApplicationOverrides2(std::nullptr_t = nullptr) noexcept {}
        IApplicationOverrides2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationStatics>
    {
        IApplicationStatics(std::nullptr_t = nullptr) noexcept {}
        IApplicationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBindingFailedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBindingFailedEventArgs>
    {
        IBindingFailedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBindingFailedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBringIntoViewOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBringIntoViewOptions>
    {
        IBringIntoViewOptions(std::nullptr_t = nullptr) noexcept {}
        IBringIntoViewOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBringIntoViewOptions2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBringIntoViewOptions2>
    {
        IBringIntoViewOptions2(std::nullptr_t = nullptr) noexcept {}
        IBringIntoViewOptions2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBringIntoViewRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBringIntoViewRequestedEventArgs>
    {
        IBringIntoViewRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBringIntoViewRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBrushTransition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBrushTransition>
    {
        IBrushTransition(std::nullptr_t = nullptr) noexcept {}
        IBrushTransition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBrushTransitionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBrushTransitionFactory>
    {
        IBrushTransitionFactory(std::nullptr_t = nullptr) noexcept {}
        IBrushTransitionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorPaletteResources :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorPaletteResources>
    {
        IColorPaletteResources(std::nullptr_t = nullptr) noexcept {}
        IColorPaletteResources(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorPaletteResourcesFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorPaletteResourcesFactory>
    {
        IColorPaletteResourcesFactory(std::nullptr_t = nullptr) noexcept {}
        IColorPaletteResourcesFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICornerRadiusHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICornerRadiusHelper>
    {
        ICornerRadiusHelper(std::nullptr_t = nullptr) noexcept {}
        ICornerRadiusHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICornerRadiusHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICornerRadiusHelperStatics>
    {
        ICornerRadiusHelperStatics(std::nullptr_t = nullptr) noexcept {}
        ICornerRadiusHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDataContextChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataContextChangedEventArgs>
    {
        IDataContextChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDataContextChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDataTemplate :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataTemplate>
    {
        IDataTemplate(std::nullptr_t = nullptr) noexcept {}
        IDataTemplate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDataTemplateExtension :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataTemplateExtension>
    {
        IDataTemplateExtension(std::nullptr_t = nullptr) noexcept {}
        IDataTemplateExtension(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDataTemplateFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataTemplateFactory>
    {
        IDataTemplateFactory(std::nullptr_t = nullptr) noexcept {}
        IDataTemplateFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDataTemplateKey :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataTemplateKey>
    {
        IDataTemplateKey(std::nullptr_t = nullptr) noexcept {}
        IDataTemplateKey(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDataTemplateKeyFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataTemplateKeyFactory>
    {
        IDataTemplateKeyFactory(std::nullptr_t = nullptr) noexcept {}
        IDataTemplateKeyFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDataTemplateStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataTemplateStatics2>
    {
        IDataTemplateStatics2(std::nullptr_t = nullptr) noexcept {}
        IDataTemplateStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDebugSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDebugSettings>
    {
        IDebugSettings(std::nullptr_t = nullptr) noexcept {}
        IDebugSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDebugSettings2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDebugSettings2>
    {
        IDebugSettings2(std::nullptr_t = nullptr) noexcept {}
        IDebugSettings2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDebugSettings3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDebugSettings3>
    {
        IDebugSettings3(std::nullptr_t = nullptr) noexcept {}
        IDebugSettings3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDebugSettings4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDebugSettings4>
    {
        IDebugSettings4(std::nullptr_t = nullptr) noexcept {}
        IDebugSettings4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDependencyObject :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDependencyObject>
    {
        IDependencyObject(std::nullptr_t = nullptr) noexcept {}
        IDependencyObject(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDependencyObject2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDependencyObject2>
    {
        IDependencyObject2(std::nullptr_t = nullptr) noexcept {}
        IDependencyObject2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDependencyObjectCollectionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDependencyObjectCollectionFactory>
    {
        IDependencyObjectCollectionFactory(std::nullptr_t = nullptr) noexcept {}
        IDependencyObjectCollectionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDependencyObjectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDependencyObjectFactory>
    {
        IDependencyObjectFactory(std::nullptr_t = nullptr) noexcept {}
        IDependencyObjectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDependencyProperty :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDependencyProperty>
    {
        IDependencyProperty(std::nullptr_t = nullptr) noexcept {}
        IDependencyProperty(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDependencyPropertyChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDependencyPropertyChangedEventArgs>
    {
        IDependencyPropertyChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDependencyPropertyChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDependencyPropertyStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDependencyPropertyStatics>
    {
        IDependencyPropertyStatics(std::nullptr_t = nullptr) noexcept {}
        IDependencyPropertyStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDispatcherTimer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDispatcherTimer>
    {
        IDispatcherTimer(std::nullptr_t = nullptr) noexcept {}
        IDispatcherTimer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDispatcherTimerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDispatcherTimerFactory>
    {
        IDispatcherTimerFactory(std::nullptr_t = nullptr) noexcept {}
        IDispatcherTimerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDragEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragEventArgs>
    {
        IDragEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDragEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDragEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragEventArgs2>
    {
        IDragEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IDragEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDragEventArgs3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragEventArgs3>
    {
        IDragEventArgs3(std::nullptr_t = nullptr) noexcept {}
        IDragEventArgs3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDragOperationDeferral :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragOperationDeferral>
    {
        IDragOperationDeferral(std::nullptr_t = nullptr) noexcept {}
        IDragOperationDeferral(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDragStartingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragStartingEventArgs>
    {
        IDragStartingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDragStartingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDragStartingEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragStartingEventArgs2>
    {
        IDragStartingEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IDragStartingEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDragUI :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragUI>
    {
        IDragUI(std::nullptr_t = nullptr) noexcept {}
        IDragUI(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDragUIOverride :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragUIOverride>
    {
        IDragUIOverride(std::nullptr_t = nullptr) noexcept {}
        IDragUIOverride(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDropCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropCompletedEventArgs>
    {
        IDropCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDropCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDurationHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDurationHelper>
    {
        IDurationHelper(std::nullptr_t = nullptr) noexcept {}
        IDurationHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDurationHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDurationHelperStatics>
    {
        IDurationHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IDurationHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEffectiveViewportChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEffectiveViewportChangedEventArgs>
    {
        IEffectiveViewportChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IEffectiveViewportChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IElementFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElementFactory>
    {
        IElementFactory(std::nullptr_t = nullptr) noexcept {}
        IElementFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IElementFactoryGetArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElementFactoryGetArgs>
    {
        IElementFactoryGetArgs(std::nullptr_t = nullptr) noexcept {}
        IElementFactoryGetArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IElementFactoryGetArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElementFactoryGetArgsFactory>
    {
        IElementFactoryGetArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IElementFactoryGetArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IElementFactoryRecycleArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElementFactoryRecycleArgs>
    {
        IElementFactoryRecycleArgs(std::nullptr_t = nullptr) noexcept {}
        IElementFactoryRecycleArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IElementFactoryRecycleArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElementFactoryRecycleArgsFactory>
    {
        IElementFactoryRecycleArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IElementFactoryRecycleArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IElementSoundPlayer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElementSoundPlayer>
    {
        IElementSoundPlayer(std::nullptr_t = nullptr) noexcept {}
        IElementSoundPlayer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IElementSoundPlayerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElementSoundPlayerStatics>
    {
        IElementSoundPlayerStatics(std::nullptr_t = nullptr) noexcept {}
        IElementSoundPlayerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IElementSoundPlayerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElementSoundPlayerStatics2>
    {
        IElementSoundPlayerStatics2(std::nullptr_t = nullptr) noexcept {}
        IElementSoundPlayerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEventTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEventTrigger>
    {
        IEventTrigger(std::nullptr_t = nullptr) noexcept {}
        IEventTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IExceptionRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExceptionRoutedEventArgs>
    {
        IExceptionRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IExceptionRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IExceptionRoutedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExceptionRoutedEventArgsFactory>
    {
        IExceptionRoutedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IExceptionRoutedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkElement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElement>
    {
        IFrameworkElement(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkElement2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElement2>
    {
        IFrameworkElement2(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElement2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkElement3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElement3>
    {
        IFrameworkElement3(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElement3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkElement4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElement4>
    {
        IFrameworkElement4(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElement4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkElement6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElement6>
    {
        IFrameworkElement6(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElement6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkElement7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElement7>
    {
        IFrameworkElement7(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElement7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkElementFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElementFactory>
    {
        IFrameworkElementFactory(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElementFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkElementOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElementOverrides>
    {
        IFrameworkElementOverrides(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElementOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkElementOverrides2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElementOverrides2>
    {
        IFrameworkElementOverrides2(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElementOverrides2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkElementProtected7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElementProtected7>
    {
        IFrameworkElementProtected7(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElementProtected7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkElementStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElementStatics>
    {
        IFrameworkElementStatics(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElementStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkElementStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElementStatics2>
    {
        IFrameworkElementStatics2(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElementStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkElementStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElementStatics4>
    {
        IFrameworkElementStatics4(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElementStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkElementStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElementStatics5>
    {
        IFrameworkElementStatics5(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElementStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkElementStatics6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElementStatics6>
    {
        IFrameworkElementStatics6(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElementStatics6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkTemplate :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkTemplate>
    {
        IFrameworkTemplate(std::nullptr_t = nullptr) noexcept {}
        IFrameworkTemplate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkTemplateFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkTemplateFactory>
    {
        IFrameworkTemplateFactory(std::nullptr_t = nullptr) noexcept {}
        IFrameworkTemplateFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkView>
    {
        IFrameworkView(std::nullptr_t = nullptr) noexcept {}
        IFrameworkView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFrameworkViewSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkViewSource>
    {
        IFrameworkViewSource(std::nullptr_t = nullptr) noexcept {}
        IFrameworkViewSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGridLengthHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridLengthHelper>
    {
        IGridLengthHelper(std::nullptr_t = nullptr) noexcept {}
        IGridLengthHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGridLengthHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridLengthHelperStatics>
    {
        IGridLengthHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IGridLengthHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMediaFailedRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaFailedRoutedEventArgs>
    {
        IMediaFailedRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMediaFailedRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointHelper>
    {
        IPointHelper(std::nullptr_t = nullptr) noexcept {}
        IPointHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointHelperStatics>
    {
        IPointHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IPointHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPropertyMetadata :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPropertyMetadata>
    {
        IPropertyMetadata(std::nullptr_t = nullptr) noexcept {}
        IPropertyMetadata(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPropertyMetadataFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPropertyMetadataFactory>
    {
        IPropertyMetadataFactory(std::nullptr_t = nullptr) noexcept {}
        IPropertyMetadataFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPropertyMetadataStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPropertyMetadataStatics>
    {
        IPropertyMetadataStatics(std::nullptr_t = nullptr) noexcept {}
        IPropertyMetadataStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPropertyPath :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPropertyPath>
    {
        IPropertyPath(std::nullptr_t = nullptr) noexcept {}
        IPropertyPath(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPropertyPathFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPropertyPathFactory>
    {
        IPropertyPathFactory(std::nullptr_t = nullptr) noexcept {}
        IPropertyPathFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRectHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRectHelper>
    {
        IRectHelper(std::nullptr_t = nullptr) noexcept {}
        IRectHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRectHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRectHelperStatics>
    {
        IRectHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IRectHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IResourceDictionary :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IResourceDictionary>
    {
        IResourceDictionary(std::nullptr_t = nullptr) noexcept {}
        IResourceDictionary(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IResourceDictionaryFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IResourceDictionaryFactory>
    {
        IResourceDictionaryFactory(std::nullptr_t = nullptr) noexcept {}
        IResourceDictionaryFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRoutedEvent :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRoutedEvent>
    {
        IRoutedEvent(std::nullptr_t = nullptr) noexcept {}
        IRoutedEvent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRoutedEventArgs>
    {
        IRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRoutedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRoutedEventArgsFactory>
    {
        IRoutedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IRoutedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IScalarTransition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScalarTransition>
    {
        IScalarTransition(std::nullptr_t = nullptr) noexcept {}
        IScalarTransition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IScalarTransitionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScalarTransitionFactory>
    {
        IScalarTransitionFactory(std::nullptr_t = nullptr) noexcept {}
        IScalarTransitionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISetter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISetter>
    {
        ISetter(std::nullptr_t = nullptr) noexcept {}
        ISetter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISetter2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISetter2>
    {
        ISetter2(std::nullptr_t = nullptr) noexcept {}
        ISetter2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISetterBase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISetterBase>
    {
        ISetterBase(std::nullptr_t = nullptr) noexcept {}
        ISetterBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISetterBaseCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISetterBaseCollection>
    {
        ISetterBaseCollection(std::nullptr_t = nullptr) noexcept {}
        ISetterBaseCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISetterBaseFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISetterBaseFactory>
    {
        ISetterBaseFactory(std::nullptr_t = nullptr) noexcept {}
        ISetterBaseFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISetterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISetterFactory>
    {
        ISetterFactory(std::nullptr_t = nullptr) noexcept {}
        ISetterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISizeChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISizeChangedEventArgs>
    {
        ISizeChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISizeChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISizeHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISizeHelper>
    {
        ISizeHelper(std::nullptr_t = nullptr) noexcept {}
        ISizeHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISizeHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISizeHelperStatics>
    {
        ISizeHelperStatics(std::nullptr_t = nullptr) noexcept {}
        ISizeHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStateTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStateTrigger>
    {
        IStateTrigger(std::nullptr_t = nullptr) noexcept {}
        IStateTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStateTriggerBase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStateTriggerBase>
    {
        IStateTriggerBase(std::nullptr_t = nullptr) noexcept {}
        IStateTriggerBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStateTriggerBaseFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStateTriggerBaseFactory>
    {
        IStateTriggerBaseFactory(std::nullptr_t = nullptr) noexcept {}
        IStateTriggerBaseFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStateTriggerBaseProtected :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStateTriggerBaseProtected>
    {
        IStateTriggerBaseProtected(std::nullptr_t = nullptr) noexcept {}
        IStateTriggerBaseProtected(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStateTriggerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStateTriggerStatics>
    {
        IStateTriggerStatics(std::nullptr_t = nullptr) noexcept {}
        IStateTriggerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStyle :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStyle>
    {
        IStyle(std::nullptr_t = nullptr) noexcept {}
        IStyle(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStyleFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStyleFactory>
    {
        IStyleFactory(std::nullptr_t = nullptr) noexcept {}
        IStyleFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITargetPropertyPath :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITargetPropertyPath>
    {
        ITargetPropertyPath(std::nullptr_t = nullptr) noexcept {}
        ITargetPropertyPath(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITargetPropertyPathFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITargetPropertyPathFactory>
    {
        ITargetPropertyPathFactory(std::nullptr_t = nullptr) noexcept {}
        ITargetPropertyPathFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IThicknessHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IThicknessHelper>
    {
        IThicknessHelper(std::nullptr_t = nullptr) noexcept {}
        IThicknessHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IThicknessHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IThicknessHelperStatics>
    {
        IThicknessHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IThicknessHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITriggerAction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITriggerAction>
    {
        ITriggerAction(std::nullptr_t = nullptr) noexcept {}
        ITriggerAction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITriggerActionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITriggerActionFactory>
    {
        ITriggerActionFactory(std::nullptr_t = nullptr) noexcept {}
        ITriggerActionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITriggerBase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITriggerBase>
    {
        ITriggerBase(std::nullptr_t = nullptr) noexcept {}
        ITriggerBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITriggerBaseFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITriggerBaseFactory>
    {
        ITriggerBaseFactory(std::nullptr_t = nullptr) noexcept {}
        ITriggerBaseFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElement>
    {
        IUIElement(std::nullptr_t = nullptr) noexcept {}
        IUIElement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElement10 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElement10>
    {
        IUIElement10(std::nullptr_t = nullptr) noexcept {}
        IUIElement10(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElement2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElement2>
    {
        IUIElement2(std::nullptr_t = nullptr) noexcept {}
        IUIElement2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElement3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElement3>
    {
        IUIElement3(std::nullptr_t = nullptr) noexcept {}
        IUIElement3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElement4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElement4>
    {
        IUIElement4(std::nullptr_t = nullptr) noexcept {}
        IUIElement4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElement5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElement5>
    {
        IUIElement5(std::nullptr_t = nullptr) noexcept {}
        IUIElement5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElement7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElement7>
    {
        IUIElement7(std::nullptr_t = nullptr) noexcept {}
        IUIElement7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElement8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElement8>
    {
        IUIElement8(std::nullptr_t = nullptr) noexcept {}
        IUIElement8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElement9 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElement9>
    {
        IUIElement9(std::nullptr_t = nullptr) noexcept {}
        IUIElement9(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementFactory>
    {
        IUIElementFactory(std::nullptr_t = nullptr) noexcept {}
        IUIElementFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementOverrides>
    {
        IUIElementOverrides(std::nullptr_t = nullptr) noexcept {}
        IUIElementOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementOverrides7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementOverrides7>
    {
        IUIElementOverrides7(std::nullptr_t = nullptr) noexcept {}
        IUIElementOverrides7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementOverrides8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementOverrides8>
    {
        IUIElementOverrides8(std::nullptr_t = nullptr) noexcept {}
        IUIElementOverrides8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementOverrides9 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementOverrides9>
    {
        IUIElementOverrides9(std::nullptr_t = nullptr) noexcept {}
        IUIElementOverrides9(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementStatics>
    {
        IUIElementStatics(std::nullptr_t = nullptr) noexcept {}
        IUIElementStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementStatics10 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementStatics10>
    {
        IUIElementStatics10(std::nullptr_t = nullptr) noexcept {}
        IUIElementStatics10(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementStatics2>
    {
        IUIElementStatics2(std::nullptr_t = nullptr) noexcept {}
        IUIElementStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementStatics3>
    {
        IUIElementStatics3(std::nullptr_t = nullptr) noexcept {}
        IUIElementStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementStatics4>
    {
        IUIElementStatics4(std::nullptr_t = nullptr) noexcept {}
        IUIElementStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementStatics5>
    {
        IUIElementStatics5(std::nullptr_t = nullptr) noexcept {}
        IUIElementStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementStatics6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementStatics6>
    {
        IUIElementStatics6(std::nullptr_t = nullptr) noexcept {}
        IUIElementStatics6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementStatics7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementStatics7>
    {
        IUIElementStatics7(std::nullptr_t = nullptr) noexcept {}
        IUIElementStatics7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementStatics8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementStatics8>
    {
        IUIElementStatics8(std::nullptr_t = nullptr) noexcept {}
        IUIElementStatics8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementStatics9 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementStatics9>
    {
        IUIElementStatics9(std::nullptr_t = nullptr) noexcept {}
        IUIElementStatics9(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementWeakCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementWeakCollection>
    {
        IUIElementWeakCollection(std::nullptr_t = nullptr) noexcept {}
        IUIElementWeakCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUIElementWeakCollectionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUIElementWeakCollectionFactory>
    {
        IUIElementWeakCollectionFactory(std::nullptr_t = nullptr) noexcept {}
        IUIElementWeakCollectionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUnhandledExceptionEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUnhandledExceptionEventArgs>
    {
        IUnhandledExceptionEventArgs(std::nullptr_t = nullptr) noexcept {}
        IUnhandledExceptionEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVector3Transition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVector3Transition>
    {
        IVector3Transition(std::nullptr_t = nullptr) noexcept {}
        IVector3Transition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVector3TransitionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVector3TransitionFactory>
    {
        IVector3TransitionFactory(std::nullptr_t = nullptr) noexcept {}
        IVector3TransitionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualState :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualState>
    {
        IVisualState(std::nullptr_t = nullptr) noexcept {}
        IVisualState(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualState2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualState2>
    {
        IVisualState2(std::nullptr_t = nullptr) noexcept {}
        IVisualState2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualStateChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualStateChangedEventArgs>
    {
        IVisualStateChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IVisualStateChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualStateGroup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualStateGroup>
    {
        IVisualStateGroup(std::nullptr_t = nullptr) noexcept {}
        IVisualStateGroup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualStateManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualStateManager>
    {
        IVisualStateManager(std::nullptr_t = nullptr) noexcept {}
        IVisualStateManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualStateManagerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualStateManagerFactory>
    {
        IVisualStateManagerFactory(std::nullptr_t = nullptr) noexcept {}
        IVisualStateManagerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualStateManagerOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualStateManagerOverrides>
    {
        IVisualStateManagerOverrides(std::nullptr_t = nullptr) noexcept {}
        IVisualStateManagerOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualStateManagerProtected :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualStateManagerProtected>
    {
        IVisualStateManagerProtected(std::nullptr_t = nullptr) noexcept {}
        IVisualStateManagerProtected(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualStateManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualStateManagerStatics>
    {
        IVisualStateManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IVisualStateManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualTransition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualTransition>
    {
        IVisualTransition(std::nullptr_t = nullptr) noexcept {}
        IVisualTransition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualTransitionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualTransitionFactory>
    {
        IVisualTransitionFactory(std::nullptr_t = nullptr) noexcept {}
        IVisualTransitionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindow :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindow>
    {
        IWindow(std::nullptr_t = nullptr) noexcept {}
        IWindow(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindow2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindow2>
    {
        IWindow2(std::nullptr_t = nullptr) noexcept {}
        IWindow2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindow3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindow3>
    {
        IWindow3(std::nullptr_t = nullptr) noexcept {}
        IWindow3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindow4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindow4>
    {
        IWindow4(std::nullptr_t = nullptr) noexcept {}
        IWindow4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowCreatedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowCreatedEventArgs>
    {
        IWindowCreatedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWindowCreatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowStatics>
    {
        IWindowStatics(std::nullptr_t = nullptr) noexcept {}
        IWindowStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlRoot :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlRoot>
    {
        IXamlRoot(std::nullptr_t = nullptr) noexcept {}
        IXamlRoot(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlRootChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlRootChangedEventArgs>
    {
        IXamlRootChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IXamlRootChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
