// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_AI_Actions_H
#define WINRT_Windows_AI_Actions_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.240405.15"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.240405.15"
#include "winrt/impl/Windows.AI.Actions.Hosting.2.h"
#include "winrt/impl/Windows.ApplicationModel.Contacts.2.h"
#include "winrt/impl/Windows.Foundation.2.h"
#include "winrt/impl/Windows.UI.2.h"
#include "winrt/impl/Windows.AI.Actions.2.h"
namespace winrt::impl
{
    template <typename D> auto consume_Windows_AI_Actions_IActionEntity<D>::Kind() const
    {
        winrt::Windows::AI::Actions::ActionEntityKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionEntity)->get_Kind(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionEntity<D>::DisplayInfo() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionEntity)->get_DisplayInfo(&value));
        return winrt::Windows::AI::Actions::ActionEntityDisplayInfo{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionEntity2<D>::Id() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionEntity2)->get_Id(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionEntityDisplayInfo<D>::Title() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionEntityDisplayInfo)->get_Title(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionEntityFactory2<D>::CreateFileEntity(param::hstring const& path) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionEntityFactory2)->CreateFileEntity(*(void**)(&path), &result));
        return winrt::Windows::AI::Actions::FileActionEntity{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionEntityFactory2<D>::CreateDocumentEntity(param::hstring const& path) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionEntityFactory2)->CreateDocumentEntity(*(void**)(&path), &result));
        return winrt::Windows::AI::Actions::DocumentActionEntity{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionEntityFactory2<D>::CreatePhotoEntity(param::hstring const& path) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionEntityFactory2)->CreatePhotoEntity(*(void**)(&path), &result));
        return winrt::Windows::AI::Actions::PhotoActionEntity{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionEntityFactory2<D>::CreateTextEntity(param::hstring const& text) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionEntityFactory2)->CreateTextEntity(*(void**)(&text), &result));
        return winrt::Windows::AI::Actions::TextActionEntity{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionEntityFactory3<D>::CreateRemoteFileEntity(param::hstring const& sourceId, winrt::Windows::AI::Actions::RemoteFileKind const& fileKind, winrt::Windows::Foundation::Uri const& sourceUri, param::hstring const& fileId, param::hstring const& contentType, param::hstring const& driveId, param::hstring const& accountId, param::hstring const& extension) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionEntityFactory3)->CreateRemoteFileEntity(*(void**)(&sourceId), static_cast<int32_t>(fileKind), *(void**)(&sourceUri), *(void**)(&fileId), *(void**)(&contentType), *(void**)(&driveId), *(void**)(&accountId), *(void**)(&extension), &result));
        return winrt::Windows::AI::Actions::RemoteFileActionEntity{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionEntityFactory3<D>::CreateTextEntity(param::hstring const& text, winrt::Windows::AI::Actions::ActionEntityTextFormat const& textFormat) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionEntityFactory3)->CreateTextEntityWithTextFormat(*(void**)(&text), static_cast<int32_t>(textFormat), &result));
        return winrt::Windows::AI::Actions::TextActionEntity{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionEntityFactory3<D>::CreateStreamingTextActionEntityWriter(winrt::Windows::AI::Actions::ActionEntityTextFormat const& textFormat) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionEntityFactory3)->CreateStreamingTextActionEntityWriter(static_cast<int32_t>(textFormat), &result));
        return winrt::Windows::AI::Actions::StreamingTextActionEntityWriter{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionEntityFactory4<D>::CreateTableEntity(array_view<hstring const> data, uint32_t columnCount) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionEntityFactory4)->CreateTableEntity(data.size(), get_abi(data), columnCount, &result));
        return winrt::Windows::AI::Actions::TableActionEntity{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionEntityFactory4<D>::CreateContactEntity(winrt::Windows::ApplicationModel::Contacts::Contact const& contact) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionEntityFactory4)->CreateContactEntity(*(void**)(&contact), &result));
        return winrt::Windows::AI::Actions::ContactActionEntity{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionFeedback<D>::FeedbackKind() const
    {
        winrt::Windows::AI::Actions::ActionFeedbackKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionFeedback)->get_FeedbackKind(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationContext<D>::EntityFactory() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationContext)->get_EntityFactory(&value));
        return winrt::Windows::AI::Actions::ActionEntityFactory{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationContext<D>::SetInputEntity(param::hstring const& inputName, winrt::Windows::AI::Actions::ActionEntity const& inputValue) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationContext)->SetInputEntity(*(void**)(&inputName), *(void**)(&inputValue)));
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationContext<D>::GetInputEntities() const
    {
        uint32_t result_impl_size{};
        void** result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationContext)->GetInputEntities(&result_impl_size, &result));
        return com_array<winrt::Windows::AI::Actions::NamedActionEntity>{ result, result_impl_size, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationContext<D>::SetOutputEntity(param::hstring const& outputName, winrt::Windows::AI::Actions::ActionEntity const& outputValue) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationContext)->SetOutputEntity(*(void**)(&outputName), *(void**)(&outputValue)));
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationContext<D>::GetOutputEntities() const
    {
        uint32_t result_impl_size{};
        void** result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationContext)->GetOutputEntities(&result_impl_size, &result));
        return com_array<winrt::Windows::AI::Actions::NamedActionEntity>{ result, result_impl_size, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationContext<D>::Result() const
    {
        winrt::Windows::AI::Actions::ActionInvocationResult value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationContext)->get_Result(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationContext<D>::Result(winrt::Windows::AI::Actions::ActionInvocationResult const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationContext)->put_Result(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationContext<D>::ExtendedError() const
    {
        winrt::hresult value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationContext)->get_ExtendedError(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationContext<D>::ExtendedError(winrt::hresult const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationContext)->put_ExtendedError(impl::bind_in(value)));
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationContext2<D>::InvokerWindowId() const
    {
        winrt::Windows::UI::WindowId value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationContext2)->get_InvokerWindowId(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationContext2<D>::HelpDetails() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationContext2)->get_HelpDetails(&value));
        return winrt::Windows::AI::Actions::ActionInvocationHelpDetails{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationContext2<D>::ActionId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationContext2)->get_ActionId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationContext2<D>::InvokerAppUserModelId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationContext2)->get_InvokerAppUserModelId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationHelpDetails<D>::Kind() const
    {
        winrt::Windows::AI::Actions::ActionInvocationHelpKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationHelpDetails)->get_Kind(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationHelpDetails<D>::Kind(winrt::Windows::AI::Actions::ActionInvocationHelpKind const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationHelpDetails)->put_Kind(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationHelpDetails<D>::Title() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationHelpDetails)->get_Title(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationHelpDetails<D>::Title(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationHelpDetails)->put_Title(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationHelpDetails<D>::Description() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationHelpDetails)->get_Description(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationHelpDetails<D>::Description(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationHelpDetails)->put_Description(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationHelpDetails<D>::HelpUri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationHelpDetails)->get_HelpUri(&value));
        return winrt::Windows::Foundation::Uri{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationHelpDetails<D>::HelpUri(winrt::Windows::Foundation::Uri const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationHelpDetails)->put_HelpUri(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationHelpDetails<D>::HelpUriDescription() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationHelpDetails)->get_HelpUriDescription(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionInvocationHelpDetails<D>::HelpUriDescription(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionInvocationHelpDetails)->put_HelpUriDescription(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionRuntime<D>::ActionCatalog() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionRuntime)->get_ActionCatalog(&value));
        return winrt::Windows::AI::Actions::Hosting::ActionCatalog{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionRuntime<D>::EntityFactory() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionRuntime)->get_EntityFactory(&value));
        return winrt::Windows::AI::Actions::ActionEntityFactory{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionRuntime<D>::CreateInvocationContext(param::hstring const& actionId) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionRuntime)->CreateInvocationContext(*(void**)(&actionId), &result));
        return winrt::Windows::AI::Actions::ActionInvocationContext{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionRuntime2<D>::CreateActionFeedback(winrt::Windows::AI::Actions::ActionFeedbackKind const& feedbackKind) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionRuntime2)->CreateActionFeedback(static_cast<int32_t>(feedbackKind), &result));
        return winrt::Windows::AI::Actions::ActionFeedback{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionRuntime2<D>::SetActionAvailability(param::hstring const& actionId, bool isAvailable) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionRuntime2)->SetActionAvailability(*(void**)(&actionId), isAvailable));
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionRuntime2<D>::GetActionAvailability(param::hstring const& actionId) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionRuntime2)->GetActionAvailability(*(void**)(&actionId), &result));
        return result;
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionRuntime3<D>::CreateInvocationContextWithWindowId(param::hstring const& actionId, winrt::Windows::UI::WindowId const& invokerWindowId) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionRuntime3)->CreateInvocationContextWithWindowId(*(void**)(&actionId), impl::bind_in(invokerWindowId), &result));
        return winrt::Windows::AI::Actions::ActionInvocationContext{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionRuntime3<D>::GetActionEntityById(param::hstring const& entityId) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionRuntime3)->GetActionEntityById(*(void**)(&entityId), &result));
        return winrt::Windows::AI::Actions::ActionEntity{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IActionRuntime3<D>::LatestSupportedSchemaVersion() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IActionRuntime3)->get_LatestSupportedSchemaVersion(&value));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_IContactActionEntity<D>::Contact() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IContactActionEntity)->get_Contact(&value));
        return winrt::Windows::ApplicationModel::Contacts::Contact{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IDocumentActionEntity<D>::FullPath() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IDocumentActionEntity)->get_FullPath(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IFileActionEntity<D>::FullPath() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IFileActionEntity)->get_FullPath(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_INamedActionEntity<D>::Name() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::INamedActionEntity)->get_Name(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_INamedActionEntity<D>::Name(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::INamedActionEntity)->put_Name(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_AI_Actions_INamedActionEntity<D>::Entity() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::INamedActionEntity)->get_Entity(&value));
        return winrt::Windows::AI::Actions::ActionEntity{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_INamedActionEntity<D>::Entity(winrt::Windows::AI::Actions::ActionEntity const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::INamedActionEntity)->put_Entity(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_AI_Actions_IPhotoActionEntity<D>::FullPath() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IPhotoActionEntity)->get_FullPath(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IRemoteFileActionEntity<D>::SourceId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IRemoteFileActionEntity)->get_SourceId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IRemoteFileActionEntity<D>::FileKind() const
    {
        winrt::Windows::AI::Actions::RemoteFileKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IRemoteFileActionEntity)->get_FileKind(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_IRemoteFileActionEntity<D>::SourceUri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IRemoteFileActionEntity)->get_SourceUri(&value));
        return winrt::Windows::Foundation::Uri{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IRemoteFileActionEntity<D>::FileId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IRemoteFileActionEntity)->get_FileId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IRemoteFileActionEntity<D>::ContentType() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IRemoteFileActionEntity)->get_ContentType(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IRemoteFileActionEntity<D>::DriveId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IRemoteFileActionEntity)->get_DriveId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IRemoteFileActionEntity<D>::AccountId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IRemoteFileActionEntity)->get_AccountId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IRemoteFileActionEntity<D>::Extension() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IRemoteFileActionEntity)->get_Extension(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IStreamingTextActionEntity<D>::IsComplete() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IStreamingTextActionEntity)->get_IsComplete(&value));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_IStreamingTextActionEntity<D>::GetText() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IStreamingTextActionEntity)->GetText(&result));
        return hstring{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IStreamingTextActionEntity<D>::TextFormat() const
    {
        winrt::Windows::AI::Actions::ActionEntityTextFormat value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IStreamingTextActionEntity)->get_TextFormat(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_IStreamingTextActionEntity<D>::TextChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::AI::Actions::StreamingTextActionEntity, winrt::Windows::AI::Actions::StreamingTextActionEntityTextChangedArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IStreamingTextActionEntity)->add_TextChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Windows_AI_Actions_IStreamingTextActionEntity<D>::TextChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::AI::Actions::StreamingTextActionEntity, winrt::Windows::AI::Actions::StreamingTextActionEntityTextChangedArgs> const& handler) const
    {
        return impl::make_event_revoker<D, TextChanged_revoker>(this, TextChanged(handler));
    }
    template <typename D> auto consume_Windows_AI_Actions_IStreamingTextActionEntity<D>::TextChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IStreamingTextActionEntity)->remove_TextChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Windows_AI_Actions_IStreamingTextActionEntityTextChangedArgs<D>::Text() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IStreamingTextActionEntityTextChangedArgs)->get_Text(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IStreamingTextActionEntityTextChangedArgs<D>::IsComplete() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IStreamingTextActionEntityTextChangedArgs)->get_IsComplete(&value));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_IStreamingTextActionEntityWriter<D>::ReaderEntity() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IStreamingTextActionEntityWriter)->get_ReaderEntity(&value));
        return winrt::Windows::AI::Actions::StreamingTextActionEntity{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_IStreamingTextActionEntityWriter<D>::TextFormat() const
    {
        winrt::Windows::AI::Actions::ActionEntityTextFormat value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IStreamingTextActionEntityWriter)->get_TextFormat(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_IStreamingTextActionEntityWriter<D>::SetText(param::hstring const& text) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::IStreamingTextActionEntityWriter)->SetText(*(void**)(&text)));
    }
    template <typename D> auto consume_Windows_AI_Actions_ITableActionEntity<D>::GetTextContent() const
    {
        uint32_t result_impl_size{};
        void** result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::ITableActionEntity)->GetTextContent(&result_impl_size, &result));
        return com_array<hstring>{ result, result_impl_size, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_ITableActionEntity<D>::RowCount() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::ITableActionEntity)->get_RowCount(&value));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_ITableActionEntity<D>::ColumnCount() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::ITableActionEntity)->get_ColumnCount(&value));
        return value;
    }
    template <typename D> auto consume_Windows_AI_Actions_ITextActionEntity<D>::Text() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::ITextActionEntity)->get_Text(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_ITextActionEntity2<D>::TextFormat() const
    {
        winrt::Windows::AI::Actions::ActionEntityTextFormat value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::ITextActionEntity2)->get_TextFormat(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IActionEntity> : produce_base<D, winrt::Windows::AI::Actions::IActionEntity>
    {
        int32_t __stdcall get_Kind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::ActionEntityKind>(this->shim().Kind());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DisplayInfo(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::ActionEntityDisplayInfo>(this->shim().DisplayInfo());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IActionEntity2> : produce_base<D, winrt::Windows::AI::Actions::IActionEntity2>
    {
        int32_t __stdcall get_Id(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Id());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IActionEntityDisplayInfo> : produce_base<D, winrt::Windows::AI::Actions::IActionEntityDisplayInfo>
    {
        int32_t __stdcall get_Title(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Title());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IActionEntityFactory> : produce_base<D, winrt::Windows::AI::Actions::IActionEntityFactory>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IActionEntityFactory2> : produce_base<D, winrt::Windows::AI::Actions::IActionEntityFactory2>
    {
        int32_t __stdcall CreateFileEntity(void* path, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::AI::Actions::FileActionEntity>(this->shim().CreateFileEntity(*reinterpret_cast<hstring const*>(&path)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateDocumentEntity(void* path, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::AI::Actions::DocumentActionEntity>(this->shim().CreateDocumentEntity(*reinterpret_cast<hstring const*>(&path)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreatePhotoEntity(void* path, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::AI::Actions::PhotoActionEntity>(this->shim().CreatePhotoEntity(*reinterpret_cast<hstring const*>(&path)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateTextEntity(void* text, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::AI::Actions::TextActionEntity>(this->shim().CreateTextEntity(*reinterpret_cast<hstring const*>(&text)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IActionEntityFactory3> : produce_base<D, winrt::Windows::AI::Actions::IActionEntityFactory3>
    {
        int32_t __stdcall CreateRemoteFileEntity(void* sourceId, int32_t fileKind, void* sourceUri, void* fileId, void* contentType, void* driveId, void* accountId, void* extension, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::AI::Actions::RemoteFileActionEntity>(this->shim().CreateRemoteFileEntity(*reinterpret_cast<hstring const*>(&sourceId), *reinterpret_cast<winrt::Windows::AI::Actions::RemoteFileKind const*>(&fileKind), *reinterpret_cast<winrt::Windows::Foundation::Uri const*>(&sourceUri), *reinterpret_cast<hstring const*>(&fileId), *reinterpret_cast<hstring const*>(&contentType), *reinterpret_cast<hstring const*>(&driveId), *reinterpret_cast<hstring const*>(&accountId), *reinterpret_cast<hstring const*>(&extension)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateTextEntityWithTextFormat(void* text, int32_t textFormat, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::AI::Actions::TextActionEntity>(this->shim().CreateTextEntity(*reinterpret_cast<hstring const*>(&text), *reinterpret_cast<winrt::Windows::AI::Actions::ActionEntityTextFormat const*>(&textFormat)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateStreamingTextActionEntityWriter(int32_t textFormat, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::AI::Actions::StreamingTextActionEntityWriter>(this->shim().CreateStreamingTextActionEntityWriter(*reinterpret_cast<winrt::Windows::AI::Actions::ActionEntityTextFormat const*>(&textFormat)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IActionEntityFactory4> : produce_base<D, winrt::Windows::AI::Actions::IActionEntityFactory4>
    {
        int32_t __stdcall CreateTableEntity(uint32_t __dataSize, void** data, uint32_t columnCount, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::AI::Actions::TableActionEntity>(this->shim().CreateTableEntity(array_view<hstring const>(reinterpret_cast<hstring const *>(data), reinterpret_cast<hstring const *>(data) + __dataSize), columnCount));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateContactEntity(void* contact, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::AI::Actions::ContactActionEntity>(this->shim().CreateContactEntity(*reinterpret_cast<winrt::Windows::ApplicationModel::Contacts::Contact const*>(&contact)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IActionEntityFactoryFactory> : produce_base<D, winrt::Windows::AI::Actions::IActionEntityFactoryFactory>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IActionFeedback> : produce_base<D, winrt::Windows::AI::Actions::IActionFeedback>
    {
        int32_t __stdcall get_FeedbackKind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::ActionFeedbackKind>(this->shim().FeedbackKind());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IActionInvocationContext> : produce_base<D, winrt::Windows::AI::Actions::IActionInvocationContext>
    {
        int32_t __stdcall get_EntityFactory(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::ActionEntityFactory>(this->shim().EntityFactory());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetInputEntity(void* inputName, void* inputValue) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetInputEntity(*reinterpret_cast<hstring const*>(&inputName), *reinterpret_cast<winrt::Windows::AI::Actions::ActionEntity const*>(&inputValue));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetInputEntities(uint32_t* __resultSize, void*** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            std::tie(*__resultSize, *result) = detach_abi(this->shim().GetInputEntities());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetOutputEntity(void* outputName, void* outputValue) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetOutputEntity(*reinterpret_cast<hstring const*>(&outputName), *reinterpret_cast<winrt::Windows::AI::Actions::ActionEntity const*>(&outputValue));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetOutputEntities(uint32_t* __resultSize, void*** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            std::tie(*__resultSize, *result) = detach_abi(this->shim().GetOutputEntities());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Result(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::ActionInvocationResult>(this->shim().Result());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Result(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Result(*reinterpret_cast<winrt::Windows::AI::Actions::ActionInvocationResult const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ExtendedError(winrt::hresult* value) noexcept final try
        {
            zero_abi<winrt::hresult>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::hresult>(this->shim().ExtendedError());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ExtendedError(winrt::hresult value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ExtendedError(*reinterpret_cast<winrt::hresult const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IActionInvocationContext2> : produce_base<D, winrt::Windows::AI::Actions::IActionInvocationContext2>
    {
        int32_t __stdcall get_InvokerWindowId(struct struct_Windows_UI_WindowId* value) noexcept final try
        {
            zero_abi<winrt::Windows::UI::WindowId>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::WindowId>(this->shim().InvokerWindowId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_HelpDetails(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::ActionInvocationHelpDetails>(this->shim().HelpDetails());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ActionId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ActionId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_InvokerAppUserModelId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().InvokerAppUserModelId());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IActionInvocationHelpDetails> : produce_base<D, winrt::Windows::AI::Actions::IActionInvocationHelpDetails>
    {
        int32_t __stdcall get_Kind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::ActionInvocationHelpKind>(this->shim().Kind());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Kind(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Kind(*reinterpret_cast<winrt::Windows::AI::Actions::ActionInvocationHelpKind const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Title(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Title());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Title(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Title(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Description(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Description());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Description(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Description(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_HelpUri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Uri>(this->shim().HelpUri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_HelpUri(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().HelpUri(*reinterpret_cast<winrt::Windows::Foundation::Uri const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_HelpUriDescription(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().HelpUriDescription());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_HelpUriDescription(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().HelpUriDescription(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IActionRuntime> : produce_base<D, winrt::Windows::AI::Actions::IActionRuntime>
    {
        int32_t __stdcall get_ActionCatalog(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::Hosting::ActionCatalog>(this->shim().ActionCatalog());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_EntityFactory(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::ActionEntityFactory>(this->shim().EntityFactory());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateInvocationContext(void* actionId, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::AI::Actions::ActionInvocationContext>(this->shim().CreateInvocationContext(*reinterpret_cast<hstring const*>(&actionId)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IActionRuntime2> : produce_base<D, winrt::Windows::AI::Actions::IActionRuntime2>
    {
        int32_t __stdcall CreateActionFeedback(int32_t feedbackKind, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::AI::Actions::ActionFeedback>(this->shim().CreateActionFeedback(*reinterpret_cast<winrt::Windows::AI::Actions::ActionFeedbackKind const*>(&feedbackKind)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetActionAvailability(void* actionId, bool isAvailable) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetActionAvailability(*reinterpret_cast<hstring const*>(&actionId), isAvailable);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetActionAvailability(void* actionId, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().GetActionAvailability(*reinterpret_cast<hstring const*>(&actionId)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IActionRuntime3> : produce_base<D, winrt::Windows::AI::Actions::IActionRuntime3>
    {
        int32_t __stdcall CreateInvocationContextWithWindowId(void* actionId, struct struct_Windows_UI_WindowId invokerWindowId, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::AI::Actions::ActionInvocationContext>(this->shim().CreateInvocationContextWithWindowId(*reinterpret_cast<hstring const*>(&actionId), *reinterpret_cast<winrt::Windows::UI::WindowId const*>(&invokerWindowId)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetActionEntityById(void* entityId, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::AI::Actions::ActionEntity>(this->shim().GetActionEntityById(*reinterpret_cast<hstring const*>(&entityId)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_LatestSupportedSchemaVersion(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().LatestSupportedSchemaVersion());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IActionRuntimeFactory> : produce_base<D, winrt::Windows::AI::Actions::IActionRuntimeFactory>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IContactActionEntity> : produce_base<D, winrt::Windows::AI::Actions::IContactActionEntity>
    {
        int32_t __stdcall get_Contact(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::ApplicationModel::Contacts::Contact>(this->shim().Contact());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IDocumentActionEntity> : produce_base<D, winrt::Windows::AI::Actions::IDocumentActionEntity>
    {
        int32_t __stdcall get_FullPath(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().FullPath());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IFileActionEntity> : produce_base<D, winrt::Windows::AI::Actions::IFileActionEntity>
    {
        int32_t __stdcall get_FullPath(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().FullPath());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::INamedActionEntity> : produce_base<D, winrt::Windows::AI::Actions::INamedActionEntity>
    {
        int32_t __stdcall get_Name(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Name());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Name(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Name(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Entity(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::ActionEntity>(this->shim().Entity());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Entity(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Entity(*reinterpret_cast<winrt::Windows::AI::Actions::ActionEntity const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IPhotoActionEntity> : produce_base<D, winrt::Windows::AI::Actions::IPhotoActionEntity>
    {
        int32_t __stdcall get_FullPath(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().FullPath());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IRemoteFileActionEntity> : produce_base<D, winrt::Windows::AI::Actions::IRemoteFileActionEntity>
    {
        int32_t __stdcall get_SourceId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().SourceId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_FileKind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::RemoteFileKind>(this->shim().FileKind());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SourceUri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Uri>(this->shim().SourceUri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_FileId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().FileId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ContentType(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ContentType());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DriveId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().DriveId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AccountId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().AccountId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Extension(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Extension());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IStreamingTextActionEntity> : produce_base<D, winrt::Windows::AI::Actions::IStreamingTextActionEntity>
    {
        int32_t __stdcall get_IsComplete(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsComplete());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetText(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<hstring>(this->shim().GetText());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TextFormat(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::ActionEntityTextFormat>(this->shim().TextFormat());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_TextChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().TextChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::AI::Actions::StreamingTextActionEntity, winrt::Windows::AI::Actions::StreamingTextActionEntityTextChangedArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_TextChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().TextChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IStreamingTextActionEntityTextChangedArgs> : produce_base<D, winrt::Windows::AI::Actions::IStreamingTextActionEntityTextChangedArgs>
    {
        int32_t __stdcall get_Text(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Text());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsComplete(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsComplete());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::IStreamingTextActionEntityWriter> : produce_base<D, winrt::Windows::AI::Actions::IStreamingTextActionEntityWriter>
    {
        int32_t __stdcall get_ReaderEntity(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::StreamingTextActionEntity>(this->shim().ReaderEntity());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TextFormat(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::ActionEntityTextFormat>(this->shim().TextFormat());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetText(void* text) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetText(*reinterpret_cast<hstring const*>(&text));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::ITableActionEntity> : produce_base<D, winrt::Windows::AI::Actions::ITableActionEntity>
    {
        int32_t __stdcall GetTextContent(uint32_t* __resultSize, void*** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            std::tie(*__resultSize, *result) = detach_abi(this->shim().GetTextContent());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RowCount(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().RowCount());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ColumnCount(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().ColumnCount());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::ITextActionEntity> : produce_base<D, winrt::Windows::AI::Actions::ITextActionEntity>
    {
        int32_t __stdcall get_Text(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Text());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::ITextActionEntity2> : produce_base<D, winrt::Windows::AI::Actions::ITextActionEntity2>
    {
        int32_t __stdcall get_TextFormat(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::Actions::ActionEntityTextFormat>(this->shim().TextFormat());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
}
WINRT_EXPORT namespace winrt::Windows::AI::Actions
{
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
    template<> struct hash<winrt::Windows::AI::Actions::IActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IActionEntity2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IActionEntityDisplayInfo> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IActionEntityFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IActionEntityFactory2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IActionEntityFactory3> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IActionEntityFactory4> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IActionEntityFactoryFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IActionFeedback> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IActionInvocationContext> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IActionInvocationContext2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IActionInvocationHelpDetails> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IActionRuntime> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IActionRuntime2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IActionRuntime3> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IActionRuntimeFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IContactActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IDocumentActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IFileActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::INamedActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IPhotoActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IRemoteFileActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IStreamingTextActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IStreamingTextActionEntityTextChangedArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::IStreamingTextActionEntityWriter> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::ITableActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::ITextActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::ITextActionEntity2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::ActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::ActionEntityDisplayInfo> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::ActionEntityFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::ActionFeedback> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::ActionInvocationContext> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::ActionInvocationHelpDetails> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::ActionRuntime> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::ContactActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::DocumentActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::FileActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::NamedActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::PhotoActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::RemoteFileActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::StreamingTextActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::StreamingTextActionEntityTextChangedArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::StreamingTextActionEntityWriter> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::TableActionEntity> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::TextActionEntity> : winrt::impl::hash_base {};
#endif
#ifdef __cpp_lib_format
#endif
}
#endif
