// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_ApplicationModel_Store_Preview_InstallControl_1_H
#define WINRT_Windows_ApplicationModel_Store_Preview_InstallControl_1_H
#include "winrt/impl/Windows.ApplicationModel.Store.Preview.InstallControl.0.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Store::Preview::InstallControl
{
    struct WINRT_IMPL_EMPTY_BASES IAppInstallItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallItem>
    {
        IAppInstallItem(std::nullptr_t = nullptr) noexcept {}
        IAppInstallItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallItem2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallItem2>
    {
        IAppInstallItem2(std::nullptr_t = nullptr) noexcept {}
        IAppInstallItem2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallItem3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallItem3>
    {
        IAppInstallItem3(std::nullptr_t = nullptr) noexcept {}
        IAppInstallItem3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallItem4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallItem4>
    {
        IAppInstallItem4(std::nullptr_t = nullptr) noexcept {}
        IAppInstallItem4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallItem5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallItem5>
    {
        IAppInstallItem5(std::nullptr_t = nullptr) noexcept {}
        IAppInstallItem5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallManager>
    {
        IAppInstallManager(std::nullptr_t = nullptr) noexcept {}
        IAppInstallManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallManager2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallManager2>
    {
        IAppInstallManager2(std::nullptr_t = nullptr) noexcept {}
        IAppInstallManager2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallManager3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallManager3>
    {
        IAppInstallManager3(std::nullptr_t = nullptr) noexcept {}
        IAppInstallManager3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallManager4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallManager4>
    {
        IAppInstallManager4(std::nullptr_t = nullptr) noexcept {}
        IAppInstallManager4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallManager5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallManager5>
    {
        IAppInstallManager5(std::nullptr_t = nullptr) noexcept {}
        IAppInstallManager5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallManager6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallManager6>
    {
        IAppInstallManager6(std::nullptr_t = nullptr) noexcept {}
        IAppInstallManager6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallManager7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallManager7>
    {
        IAppInstallManager7(std::nullptr_t = nullptr) noexcept {}
        IAppInstallManager7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallManagerItemEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallManagerItemEventArgs>
    {
        IAppInstallManagerItemEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAppInstallManagerItemEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallOptions>
    {
        IAppInstallOptions(std::nullptr_t = nullptr) noexcept {}
        IAppInstallOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallOptions2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallOptions2>
    {
        IAppInstallOptions2(std::nullptr_t = nullptr) noexcept {}
        IAppInstallOptions2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallStatus :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallStatus>
    {
        IAppInstallStatus(std::nullptr_t = nullptr) noexcept {}
        IAppInstallStatus(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallStatus2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallStatus2>
    {
        IAppInstallStatus2(std::nullptr_t = nullptr) noexcept {}
        IAppInstallStatus2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppInstallStatus3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppInstallStatus3>
    {
        IAppInstallStatus3(std::nullptr_t = nullptr) noexcept {}
        IAppInstallStatus3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppUpdateOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppUpdateOptions>
    {
        IAppUpdateOptions(std::nullptr_t = nullptr) noexcept {}
        IAppUpdateOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppUpdateOptions2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppUpdateOptions2>
    {
        IAppUpdateOptions2(std::nullptr_t = nullptr) noexcept {}
        IAppUpdateOptions2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGetEntitlementResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGetEntitlementResult>
    {
        IGetEntitlementResult(std::nullptr_t = nullptr) noexcept {}
        IGetEntitlementResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGetEntitlementResult2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGetEntitlementResult2>
    {
        IGetEntitlementResult2(std::nullptr_t = nullptr) noexcept {}
        IGetEntitlementResult2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
