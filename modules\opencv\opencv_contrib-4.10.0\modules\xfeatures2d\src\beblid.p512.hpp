// This file is part of OpenCV project.
// It is subject to the license terms in the LICENSE file found in the top-level directory
// of this distribution and at http://opencv.org/license.html.
// Author: <PERSON><PERSON> <<EMAIL>>

// Implementation of the article:
//     <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>.
//     BEBLID: Boosted Efficient Binary Local Image Descriptor.
//     Pattern Recognition Letters, 133:366–372, 2020.

// ABWLParams: x1, y1, x2, y2, boxRadius, th

// Pre-trained parameters of BEBLID-512 trained in Liberty data set with
// a million of patch pairs, 20% positives and 80% negatives
static const ABWLParams beblid_wl_params_512_[] = {
    {24, 18, 15, 17, 6, 13}, {19, 14, 13, 17, 2, 18}, {23, 19, 12, 15, 6, 19},
    {24, 14, 16, 16, 6, 11}, {16, 15, 12, 16, 1, 12}, {16, 15, 7, 10, 4, 10},
    {17, 12, 8, 17, 3, 16}, {24, 12, 11, 17, 7, 19}, {19, 17, 14, 11, 3, 13},
    {16, 15, 13, 15, 1, 10}, {16, 14, 6, 18, 5, 10}, {25, 5, 14, 15, 5, 15},
    {17, 18, 14, 16, 2, 10}, {17, 14, 14, 13, 2, 9}, {15, 14, 6, 22, 5, 7}, {14, 16, 5, 17, 5, 5},
    {16, 13, 15, 16, 1, 4}, {18, 17, 15, 15, 1, 9}, {26, 26, 15, 14, 5, 12}, {18, 18, 16, 16, 1, 4},
    {15, 14, 14, 27, 4, 0}, {17, 13, 15, 16, 1, 6}, {15, 15, 13, 14, 1, 6}, {18, 17, 16, 16, 1, 4},
    {14, 13, 6, 7, 5, 4}, {27, 12, 17, 15, 4, 8}, {12, 13, 7, 24, 7, 2}, {17, 18, 15, 15, 1, 6},
    {16, 16, 12, 17, 1, 12}, {27, 20, 16, 16, 4, 11}, {12, 14, 7, 5, 5, 0}, {12, 16, 7, 26, 5, 0},
    {15, 15, 15, 7, 4, -1}, {16, 17, 14, 17, 2, 6}, {16, 13, 10, 6, 4, 7}, {15, 26, 15, 19, 4, 1},
    {26, 5, 17, 13, 5, 7}, {15, 23, 5, 12, 5, 8}, {17, 14, 10, 11, 3, 14}, {21, 27, 17, 16, 4, 5},
    {15, 16, 14, 16, 1, 3}, {14, 11, 12, 26, 5, 1}, {12, 14, 12, 5, 4, -3}, {16, 16, 14, 12, 1, 7},
    {13, 20, 7, 13, 3, 4}, {19, 6, 17, 16, 6, 3}, {11, 9, 10, 19, 4, 2}, {14, 15, 13, 9, 3, 1},
    {16, 16, 14, 25, 3, 3}, {8, 26, 8, 13, 4, 3}, {16, 14, 15, 19, 2, 3}, {18, 15, 15, 16, 1, 9},
    {26, 23, 19, 16, 5, 4}, {11, 21, 4, 13, 4, 1}, {20, 16, 20, 5, 4, 2}, {15, 16, 15, 13, 1, 0},
    {16, 20, 16, 15, 2, 0}, {22, 13, 17, 14, 2, 8}, {18, 17, 14, 15, 1, 13}, {21, 12, 20, 26, 4, 3},
    {10, 7, 8, 18, 5, 3}, {11, 26, 11, 20, 5, 2}, {13, 21, 13, 17, 3, 1}, {10, 23, 6, 7, 6, 1},
    {10, 14, 5, 14, 5, 0}, {23, 25, 16, 6, 6, 8}, {18, 16, 18, 5, 4, 1}, {16, 16, 16, 14, 1, 0},
    {11, 15, 4, 23, 4, -2}, {17, 14, 16, 16, 1, 2}, {26, 4, 20, 24, 4, 2}, {20, 19, 18, 14, 2, 3},
    {14, 17, 10, 15, 2, 6}, {17, 13, 17, 9, 3, 0}, {26, 21, 5, 24, 5, 20}, {20, 15, 19, 25, 5, 3},
    {27, 15, 19, 5, 4, 5}, {10, 14, 10, 6, 6, -2}, {12, 22, 11, 10, 3, 2}, {17, 16, 16, 20, 2, 3},
    {15, 15, 12, 19, 1, 7}, {15, 11, 14, 17, 2, 4}, {14, 20, 10, 15, 2, 7}, {10, 14, 3, 7, 3, -5},
    {12, 16, 9, 11, 3, 1}, {19, 17, 17, 11, 2, 5}, {26, 7, 19, 26, 5, 4}, {20, 10, 19, 18, 3, 1},
    {17, 13, 16, 16, 1, 2}, {17, 11, 16, 4, 4, 2}, {15, 19, 14, 12, 2, 3}, {17, 18, 16, 13, 1, 3},
    {11, 9, 4, 27, 4, -1}, {21, 23, 18, 17, 3, 3}, {7, 21, 6, 7, 5, -1}, {25, 27, 21, 18, 4, -1},
    {14, 17, 14, 14, 2, 0}, {12, 11, 8, 19, 3, 3}, {14, 15, 13, 22, 2, 0}, {8, 23, 5, 17, 5, 1},
    {15, 16, 14, 8, 2, 1}, {16, 24, 15, 18, 3, 3}, {19, 25, 19, 18, 5, -1}, {11, 23, 10, 13, 2, 3},
    {19, 14, 18, 22, 2, 3}, {26, 15, 22, 6, 4, 2}, {24, 17, 19, 8, 3, 5}, {21, 15, 16, 15, 1, 10},
    {15, 14, 14, 20, 1, 2}, {16, 27, 13, 5, 4, 5}, {10, 4, 5, 13, 4, 3}, {12, 14, 10, 10, 2, 0},
    {14, 18, 14, 11, 1, -1}, {23, 6, 22, 20, 5, 0}, {14, 12, 10, 19, 2, 6}, {17, 18, 17, 15, 2, 0},
    {16, 15, 15, 18, 1, 4}, {11, 13, 3, 4, 3, -4}, {15, 14, 15, 8, 2, -1}, {11, 23, 5, 26, 5, 0},
    {20, 20, 19, 17, 2, 1}, {22, 19, 19, 20, 2, 3}, {16, 5, 15, 24, 4, 2}, {18, 15, 16, 12, 1, 5},
    {28, 27, 23, 15, 3, -2}, {7, 25, 6, 18, 6, 2}, {12, 19, 12, 13, 3, 0}, {9, 7, 4, 17, 4, 1},
    {14, 18, 13, 12, 1, 2}, {13, 16, 10, 23, 2, 1}, {24, 25, 23, 13, 6, -1}, {8, 13, 7, 4, 4, -3},
    {17, 15, 17, 11, 2, 0}, {20, 13, 18, 15, 1, 3}, {28, 3, 23, 15, 3, -2}, {13, 17, 12, 11, 1, 0},
    {16, 18, 16, 11, 1, 0}, {26, 16, 24, 26, 5, 2}, {14, 14, 11, 15, 1, 6}, {15, 9, 15, 3, 3, -1},
    {12, 28, 10, 19, 3, 6}, {18, 17, 18, 14, 2, 0}, {16, 14, 14, 15, 1, 7}, {20, 18, 19, 10, 2, 2},
    {27, 28, 18, 24, 3, 4}, {15, 11, 14, 25, 2, 1}, {16, 18, 15, 16, 1, 3}, {5, 27, 4, 6, 4, 0},
    {17, 20, 17, 14, 1, 0}, {13, 15, 9, 14, 1, 3}, {9, 23, 3, 23, 3, -1}, {9, 10, 3, 9, 3, -2},
    {16, 27, 16, 9, 3, 0}, {13, 17, 11, 15, 1, 3}, {14, 18, 14, 15, 1, 0}, {28, 12, 20, 21, 3, 2},
    {23, 7, 4, 27, 4, 16}, {16, 18, 16, 16, 1, -1}, {13, 16, 12, 19, 1, 1}, {20, 11, 19, 18, 2, 1},
    {23, 14, 19, 13, 1, 2}, {23, 10, 19, 3, 3, 5}, {15, 18, 13, 15, 1, 6}, {8, 14, 3, 19, 3, -3},
    {7, 18, 3, 17, 3, -2}, {22, 4, 21, 7, 4, 0}, {3, 28, 3, 18, 3, 2}, {19, 20, 17, 14, 1, 4},
    {16, 22, 15, 6, 2, 2}, {22, 20, 19, 29, 2, 5}, {11, 21, 9, 14, 2, 2}, {7, 9, 6, 4, 4, -2},
    {26, 19, 23, 9, 4, 1}, {16, 17, 16, 12, 2, 0}, {15, 5, 3, 4, 3, 4}, {18, 14, 17, 17, 1, 2},
    {19, 11, 17, 13, 1, 4}, {11, 17, 10, 10, 2, -1}, {15, 23, 12, 29, 2, 3},
    {28, 20, 24, 17, 3, -1}, {13, 10, 11, 2, 2, -1}, {28, 11, 23, 15, 3, -1},
    {16, 21, 16, 20, 2, 0}, {8, 8, 7, 17, 2, 2}, {15, 19, 14, 16, 1, 4}, {17, 11, 17, 10, 2, 0},
    {22, 21, 19, 16, 1, 1}, {13, 17, 13, 14, 1, 0}, {19, 13, 18, 16, 1, 2}, {6, 25, 5, 27, 4, -1},
    {16, 29, 16, 22, 2, 0}, {23, 27, 23, 22, 4, -1}, {29, 2, 22, 10, 2, -1}, {22, 10, 22, 5, 5, 1},
    {20, 16, 19, 15, 1, 1}, {20, 9, 19, 14, 1, 0}, {29, 29, 23, 22, 2, -1}, {12, 11, 10, 18, 1, 3},
    {4, 16, 4, 2, 2, -2}, {14, 8, 13, 2, 2, 0}, {16, 3, 15, 6, 3, 2}, {23, 8, 15, 2, 2, 10},
    {18, 19, 18, 16, 1, 0}, {12, 21, 6, 18, 1, 2}, {18, 15, 16, 19, 1, 5}, {16, 21, 16, 8, 2, 0},
    {18, 26, 17, 23, 2, 1}, {7, 8, 3, 3, 3, -3}, {6, 24, 3, 28, 3, -2}, {10, 19, 9, 26, 2, -3},
    {17, 9, 16, 13, 1, 2}, {13, 15, 13, 10, 1, -2}, {18, 16, 18, 12, 1, 0}, {17, 13, 17, 11, 1, 0},
    {6, 16, 3, 12, 3, -2}, {15, 21, 15, 20, 1, 0}, {23, 17, 20, 15, 2, 1}, {28, 22, 25, 8, 3, 0},
    {5, 16, 3, 25, 3, -3}, {14, 13, 13, 20, 1, 2}, {28, 28, 20, 27, 3, 2}, {15, 29, 8, 25, 2, 7},
    {10, 28, 5, 24, 3, 2}, {19, 14, 18, 13, 1, 2}, {19, 26, 14, 28, 3, 7}, {18, 21, 17, 18, 1, 2},
    {13, 17, 9, 20, 1, 2}, {15, 13, 13, 11, 1, 4}, {27, 7, 25, 15, 4, -1}, {12, 15, 11, 17, 1, 1},
    {13, 20, 12, 15, 1, 3}, {15, 20, 14, 22, 1, 2}, {19, 29, 17, 27, 2, 2}, {19, 3, 18, 5, 3, 1},
    {9, 21, 9, 17, 2, 1}, {19, 18, 17, 18, 1, 4}, {25, 13, 24, 18, 3, 0}, {11, 15, 10, 13, 1, 0},
    {9, 9, 8, 3, 2, -2}, {6, 8, 3, 8, 3, -1}, {28, 19, 23, 28, 3, 2}, {10, 30, 9, 23, 1, 3},
    {5, 5, 3, 18, 3, 1}, {14, 17, 12, 20, 1, 3}, {29, 16, 23, 15, 2, -1}, {23, 15, 21, 22, 2, 2},
    {28, 3, 25, 5, 3, 0}, {12, 20, 11, 17, 1, 2}, {20, 22, 18, 20, 1, 2}, {5, 9, 2, 2, 2, -3},
    {7, 27, 3, 19, 3, 1}, {13, 2, 7, 6, 2, 4}, {18, 29, 17, 25, 2, 1}, {15, 21, 14, 17, 1, 4},
    {13, 29, 12, 26, 2, 2}, {5, 22, 4, 12, 2, 0}, {16, 21, 16, 11, 1, 0}, {16, 23, 16, 10, 1, 0},
    {11, 5, 10, 11, 2, 3}, {15, 10, 14, 21, 1, 3}, {10, 18, 9, 18, 1, 0}, {17, 9, 16, 5, 2, 2},
    {19, 19, 19, 12, 1, 0}, {25, 12, 22, 4, 2, 2}, {6, 18, 1, 20, 1, -3}, {10, 13, 10, 10, 2, -1},
    {25, 16, 22, 16, 1, 0}, {18, 13, 18, 12, 1, 0}, {14, 13, 12, 11, 1, 3}, {10, 27, 1, 29, 1, -1},
    {13, 8, 11, 6, 1, 1}, {24, 24, 21, 28, 3, 2}, {22, 17, 20, 17, 1, 1}, {12, 13, 11, 18, 1, 1},
    {23, 3, 21, 7, 3, 0}, {18, 12, 17, 13, 1, 2}, {7, 28, 7, 25, 3, 1}, {28, 28, 28, 15, 3, -1},
    {17, 7, 17, 2, 2, 0}, {19, 9, 17, 11, 1, 3}, {14, 23, 14, 9, 1, 0}, {7, 22, 7, 19, 2, 1},
    {29, 24, 29, 2, 2, 0}, {28, 15, 25, 11, 3, 0}, {5, 11, 1, 10, 1, -2}, {2, 22, 2, 2, 2, -1},
    {22, 30, 16, 27, 1, 5}, {20, 15, 19, 13, 1, 1}, {23, 19, 22, 14, 2, 0}, {5, 7, 5, 3, 3, -1},
    {19, 20, 18, 18, 1, 1}, {29, 9, 25, 13, 2, -1}, {29, 23, 26, 23, 2, 0}, {9, 13, 8, 8, 1, -2},
    {21, 22, 21, 18, 2, -1}, {29, 12, 28, 20, 2, 0}, {18, 5, 1, 4, 1, 9}, {17, 4, 17, 2, 2, 0},
    {28, 29, 24, 25, 2, 0}, {14, 23, 13, 29, 1, 0}, {13, 5, 13, 1, 1, -1}, {20, 25, 20, 21, 1, -1},
    {6, 5, 2, 11, 2, 0}, {10, 14, 9, 21, 1, -1}, {13, 16, 13, 14, 1, 0}, {19, 17, 18, 14, 1, 2},
    {14, 21, 14, 17, 1, 1}, {20, 10, 18, 12, 1, 2}, {20, 4, 19, 3, 3, 1}, {3, 15, 1, 30, 1, -3},
    {13, 4, 8, 1, 1, 2}, {10, 18, 9, 14, 1, 0}, {6, 15, 1, 12, 1, -3}, {10, 25, 10, 20, 1, 2},
    {14, 11, 14, 7, 1, -1}, {22, 9, 20, 4, 1, 2}, {15, 27, 8, 30, 1, 4}, {10, 5, 10, 2, 2, -1},
    {17, 16, 16, 12, 1, 3}, {15, 18, 15, 10, 1, -1}, {20, 30, 20, 23, 1, -1}, {14, 9, 13, 22, 1, 2},
    {14, 22, 12, 25, 1, 2}, {5, 23, 2, 23, 2, -1}, {10, 16, 9, 16, 1, 0}, {26, 2, 19, 4, 1, 2},
    {3, 23, 2, 13, 2, 0}, {3, 17, 3, 7, 2, -1}, {15, 26, 15, 23, 1, 0}, {22, 14, 22, 8, 1, 1},
    {28, 9, 27, 6, 3, 0}, {26, 22, 25, 28, 3, 1}, {17, 10, 17, 5, 1, 1}, {11, 21, 10, 17, 1, 2},
    {20, 18, 20, 16, 1, 0}, {7, 20, 5, 20, 1, -1}, {17, 24, 17, 8, 1, 0}, {24, 9, 20, 9, 1, 1},
    {4, 13, 1, 16, 1, -1}, {30, 1, 28, 16, 1, -1}, {17, 21, 17, 17, 1, 0}, {19, 4, 11, 2, 1, 9},
    {30, 5, 24, 6, 1, 0}, {22, 19, 22, 12, 1, 0}, {9, 16, 9, 12, 1, -1}, {12, 16, 12, 12, 1, -1},
    {12, 24, 11, 29, 1, -1}, {3, 6, 1, 4, 1, -1}, {23, 29, 20, 27, 2, 1}, {23, 17, 22, 16, 1, 0},
    {30, 20, 26, 22, 1, 0}, {9, 2, 6, 5, 2, 1}, {20, 17, 19, 16, 1, 1}, {18, 26, 17, 30, 1, 1},
    {29, 14, 28, 14, 2, 0}, {20, 13, 19, 14, 1, 1}, {15, 23, 15, 21, 1, 0}, {8, 26, 2, 30, 1, -2},
    {4, 5, 3, 2, 2, -1}, {7, 16, 6, 12, 1, -1}, {29, 9, 23, 2, 2, 1}, {13, 2, 12, 5, 2, 2},
    {20, 18, 19, 21, 1, 2}, {7, 29, 2, 25, 2, 0}, {20, 3, 18, 8, 1, 1}, {14, 14, 14, 11, 1, -1},
    {12, 12, 12, 10, 1, -1}, {17, 27, 15, 30, 1, 2}, {22, 27, 20, 29, 2, 1}, {7, 12, 5, 9, 1, -2},
    {30, 30, 24, 24, 1, 0}, {19, 3, 19, 2, 2, 0}, {13, 19, 12, 18, 1, 2}, {3, 30, 2, 24, 1, 1},
    {9, 14, 7, 19, 1, -1}, {17, 22, 17, 18, 1, 0}, {18, 24, 17, 22, 1, 1}, {2, 18, 1, 23, 1, -1},
    {30, 23, 24, 19, 1, -1}, {11, 10, 11, 5, 1, -2}, {9, 30, 9, 27, 1, 1}, {21, 13, 20, 8, 1, 2},
    {6, 3, 2, 2, 2, -1}, {23, 22, 22, 26, 1, 1}, {12, 26, 11, 25, 1, 1}, {22, 1, 19, 5, 1, 1},
    {4, 24, 1, 25, 1, -1}, {5, 13, 5, 7, 1, -1}, {26, 22, 24, 16, 1, -1}, {27, 8, 27, 3, 2, 0},
    {13, 18, 13, 16, 1, 0}, {19, 15, 18, 17, 1, 2}, {30, 29, 26, 28, 1, 0}, {20, 15, 20, 14, 1, 0},
    {3, 18, 1, 15, 1, -1}, {18, 11, 17, 10, 1, 2}, {4, 18, 4, 16, 1, 0}, {8, 27, 5, 30, 1, -1},
    {30, 15, 28, 22, 1, 0}, {9, 19, 8, 22, 1, -1}, {30, 4, 29, 4, 1, 0}, {17, 10, 17, 8, 1, 0},
    {22, 6, 22, 1, 1, 1}, {2, 11, 1, 15, 1, 0}, {3, 16, 1, 17, 1, -1}, {9, 3, 8, 2, 2, 0},
    {3, 11, 1, 10, 1, -1}, {16, 29, 15, 28, 1, 1}, {15, 20, 15, 19, 1, 0}, {20, 17, 19, 17, 1, 1},
    {10, 3, 9, 8, 1, 2}, {10, 22, 7, 26, 1, -1}, {8, 16, 6, 16, 1, -1}, {16, 28, 16, 25, 1, 0},
    {12, 25, 10, 21, 1, 3}, {8, 9, 7, 7, 1, -1}, {3, 1, 1, 6, 1, 0}, {16, 7, 15, 9, 1, 2},
    {30, 23, 29, 23, 1, 0}, {22, 24, 21, 29, 1, 1}, {15, 1, 14, 3, 1, 1}, {18, 6, 17, 9, 1, 1},
    {26, 25, 25, 19, 1, -1}, {25, 13, 22, 18, 1, 0}, {11, 1, 10, 3, 1, 1}, {29, 28, 28, 30, 1, 0},
    {16, 17, 16, 13, 5, 0}, {28, 18, 28, 12, 2, 0}, {3, 22, 1, 23, 1, -1}, {10, 11, 10, 9, 1, -1},
    {7, 13, 6, 20, 1, -1}, {1, 15, 1, 6, 1, -1}, {16, 12, 16, 11, 1, 0}, {3, 26, 2, 30, 1, -1},
    {28, 30, 26, 23, 1, -1}, {17, 22, 16, 25, 1, 2}, {30, 13, 26, 7, 1, 0}, {10, 8, 7, 10, 1, 1},
    {2, 27, 1, 22, 1, 0}, {30, 7, 27, 8, 1, 0}, {22, 19, 21, 22, 1, 1}, {5, 19, 4, 21, 1, -1},
    {24, 6, 23, 11, 1, -1}, {24, 17, 23, 14, 1, 0}, {30, 7, 28, 1, 1, 0}, {11, 16, 11, 15, 1, 0},
    {29, 2, 26, 4, 1, 0}, {20, 4, 18, 1, 1, 2}, {18, 2, 17, 3, 1, 1}, {20, 30, 18, 29, 1, 1},
    {29, 15, 29, 9, 2, 0}, {14, 8, 14, 5, 1, -1}, {17, 15, 16, 18, 1, 3}, {12, 4, 11, 2, 2, 0},
    {23, 8, 21, 11, 1, 0}, {8, 30, 7, 24, 1, 2}, {2, 20, 1, 16, 1, 0}, {15, 26, 14, 29, 1, 1},
    {4, 30, 3, 29, 1, 0}, {19, 17, 19, 16, 1, 0}, {13, 17, 13, 15, 1, 0}, {2, 9, 1, 1, 1, -1},
    {30, 28, 27, 27, 1, 0}, {27, 4, 26, 1, 1, 0}, {19, 23, 19, 20, 1, -1}, {15, 24, 15, 23, 1, 0},
    {2, 29, 1, 28, 1, 0}, {2, 5, 1, 6, 1, 0}, {24, 29, 23, 26, 1, 0}, {13, 12, 12, 11, 1, 1},
    {12, 17, 12, 15, 1, 0}, {24, 26, 24, 22, 1, -1}, {11, 3, 10, 5, 1, 1}, {30, 2, 30, 1, 1, 0},
    {18, 30, 18, 29, 1, 0}, {30, 25, 29, 29, 1, 0}, {12, 30, 10, 28, 1, 1}, {24, 12, 22, 14, 1, 0},
    {6, 13, 4, 15, 1, -1}, {2, 26, 2, 23, 1, 0}, {8, 9, 7, 13, 1, 1}, {30, 1, 27, 1, 1, 0},
    {26, 29, 24, 30, 1, 0}, {18, 11, 18, 10, 1, 0}, {30, 19, 29, 17, 1, 0}, {20, 27, 19, 24, 1, 0},
    {28, 20, 26, 24, 1, 0}, {25, 9, 24, 9, 1, 0}, {27, 4, 24, 6, 1, 0}, {23, 21, 22, 19, 1, 0},
    {7, 13, 7, 10, 1, -1}, {12, 11, 11, 11, 1, 1}, {28, 26, 26, 26, 1, 0}, {8, 4, 6, 4, 1, 0},
    {15, 30, 15, 28, 1, 0}, {30, 14, 28, 14, 1, 0}, {17, 7, 17, 5, 1, 0}, {29, 10, 28, 6, 1, 0},
    {12, 17, 11, 17, 1, 1}, {16, 3, 16, 1, 1, 0}, {21, 3, 19, 3, 1, 1}, {12, 30, 11, 28, 1, 1},
    {18, 16, 18, 15, 1, 0}, {8, 18, 7, 20, 1, -1}, {5, 4, 1, 1, 1, -1}, {3, 27, 1, 30, 1, -1},
    {26, 4, 26, 1, 1, 0}, {5, 21, 2, 20, 1, -1}, {14, 1, 13, 3, 1, 1}, {30, 9, 28, 8, 1, 0},
    {13, 15, 12, 12, 1, 1}, {7, 23, 6, 25, 1, -1}
};
static const std::vector<ABWLParams> beblid_wl_params_512(std::begin(beblid_wl_params_512_),
                                                          std::end(beblid_wl_params_512_));
