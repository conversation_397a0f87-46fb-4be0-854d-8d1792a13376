{
	"word_wrap": "on",
    "wrap_width": 80,
	"folders":
	[
		{
			"path": "./"
		}
	],
	"settings":
	{
		"sublimeclang_options":
		[
			"-I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include/c++/v1",
			"-I/usr/local/include",
			"-I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/9.0.0/include",
			"-I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include",
			"-I/usr/include",
			"-I${folder:${project_path:serial.sublime-project}}/include",
		]
	}
}
