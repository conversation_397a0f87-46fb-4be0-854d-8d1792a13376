﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_world4100.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_annotation.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\x64\Release\opencv_dnn_plugins</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\x64\Release\opencv_highgui_plugins</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_img_hash4100.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_interactive-calibration.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_model_diagnostics.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\x64\Release\opencv_videoio_plugins</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_bioinspired.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_calib3d.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_core.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_cudaarithm.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_cudabgsegm.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_cudacodec.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_cudafeatures2d.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_cudafilters.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_cudaimgproc.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_cudalegacy.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_cudaobjdetect.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_cudaoptflow.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_cudastereo.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_cudawarping.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_dnn.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_dnn_superres.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_features2d.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_gapi.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_imgcodecs.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_imgproc.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_line_descriptor.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_mcc.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_objdetect.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_optflow.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_photo.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_reg.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_rgbd.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_signal.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_stereo.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_stitching.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_superres.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_tracking.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_video.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_videoio.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_wechat_qrcode.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_xfeatures2d.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_ximgproc.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_perf_xphoto.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\x64\Release\gen_opencv_python_source</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\lib\python3\Release\cv2.cp311-win_amd64.pyd</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_bgsegm.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_bioinspired.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_calib3d.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_core.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_cudaarithm.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_cudabgsegm.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_cudacodec.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_cudafeatures2d.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_cudafilters.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_cudaimgproc.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_cudalegacy.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_cudaobjdetect.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_cudaoptflow.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_cudastereo.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_cudawarping.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_dnn.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_dnn_superres.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_face.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_features2d.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_flann.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_fuzzy.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_highgui.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_img_hash.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_imgcodecs.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_imgproc.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_intensity_transform.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_line_descriptor.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_mcc.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_ml.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_objdetect.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_optflow.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_phase_unwrapping.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_photo.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_quality.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_rapid.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_reg.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_rgbd.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_saliency.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_shape.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_signal.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_stereo.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_stitching.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_structured_light.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_superres.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_text.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_tracking.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_video.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_videoio.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_videostab.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_wechat_qrcode.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_xfeatures2d.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_ximgproc.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_test_xphoto.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_version.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_version_win32.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_visualisation.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_waldboost_detector.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\x64\Release\ALL_BUILD</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>