"\n"
"General configuration for OpenCV 4.10.0 =====================================\n"
"  Version control:               unknown\n"
"\n"
"  Extra modules:\n"
"    Location (extra):            D:/AI/opencv/opencv_contrib-4.10.0/modules\n"
"    Version control (extra):     unknown\n"
"\n"
"  Platform:\n"
"    Timestamp:                   2025-07-30T03:48:31Z\n"
"    Host:                        Windows 10.0.19045 AMD64\n"
"    CMake:                       4.1.0-rc3\n"
"    CMake generator:             Visual Studio 17 2022\n"
"    CMake build tool:            C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe\n"
"    MSVC:                        1944\n"
"    Configuration:               Debug Release\n"
"\n"
"  CPU/HW features:\n"
"    Baseline:                    SSE SSE2 SSE3\n"
"      requested:                 SSE3\n"
"    Dispatched code generation:  SSE4_1 SSE4_2 FP16 AVX AVX2 AVX512_SKX\n"
"      requested:                 SSE4_1 SSE4_2 AVX FP16 AVX2 AVX512_SKX\n"
"      SSE4_1 (18 files):         + SSSE3 SSE4_1\n"
"      SSE4_2 (2 files):          + SSSE3 SSE4_1 POPCNT SSE4_2\n"
"      FP16 (1 files):            + SSSE3 SSE4_1 POPCNT SSE4_2 FP16 AVX\n"
"      AVX (9 files):             + SSSE3 SSE4_1 POPCNT SSE4_2 AVX\n"
"      AVX2 (38 files):           + SSSE3 SSE4_1 POPCNT SSE4_2 FP16 FMA3 AVX AVX2\n"
"      AVX512_SKX (8 files):      + SSSE3 SSE4_1 POPCNT SSE4_2 FP16 FMA3 AVX AVX2 AVX_512F AVX512_COMMON AVX512_SKX\n"
"\n"
"  C/C++:\n"
"    Built as dynamic libs?:      YES\n"
"    C++ standard:                11\n"
"    C++ Compiler:                C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe  (ver 19.44.35213.0)\n"
"    C++ flags (Release):         /DWIN32 /D_WINDOWS /W4 /GR  /D _CRT_SECURE_NO_DEPRECATE /D _CRT_NONSTDC_NO_DEPRECATE /D _SCL_SECURE_NO_WARNINGS /Gy /bigobj /Oi  /fp:fast     /EHa /wd4127 /wd4251 /wd4324 /wd4275 /wd4512 /wd4589 /wd4819 /MP  /O2 /Ob2 /DNDEBUG \n"
"    C++ flags (Debug):           /DWIN32 /D_WINDOWS /W4 /GR  /D _CRT_SECURE_NO_DEPRECATE /D _CRT_NONSTDC_NO_DEPRECATE /D _SCL_SECURE_NO_WARNINGS /Gy /bigobj /Oi  /fp:fast     /EHa /wd4127 /wd4251 /wd4324 /wd4275 /wd4512 /wd4589 /wd4819 /MP  /Zi /Ob0 /Od /RTC1 \n"
"    C Compiler:                  C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe\n"
"    C flags (Release):           /DWIN32 /D_WINDOWS /W3  /D _CRT_SECURE_NO_DEPRECATE /D _CRT_NONSTDC_NO_DEPRECATE /D _SCL_SECURE_NO_WARNINGS /Gy /bigobj /Oi  /fp:fast     /MP   /O2 /Ob2 /DNDEBUG \n"
"    C flags (Debug):             /DWIN32 /D_WINDOWS /W3  /D _CRT_SECURE_NO_DEPRECATE /D _CRT_NONSTDC_NO_DEPRECATE /D _SCL_SECURE_NO_WARNINGS /Gy /bigobj /Oi  /fp:fast     /MP /Zi /Ob0 /Od /RTC1 \n"
"    Linker flags (Release):      /machine:x64  /INCREMENTAL:NO \n"
"    Linker flags (Debug):        /machine:x64  /debug /INCREMENTAL \n"
"    ccache:                      NO\n"
"    Precompiled headers:         NO\n"
"    Extra dependencies:          cudart_static.lib nppc.lib nppial.lib nppicc.lib nppidei.lib nppif.lib nppig.lib nppim.lib nppist.lib nppisu.lib nppitc.lib npps.lib cublas.lib cudnn.lib cufft.lib -LIBPATH:C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64 -LIBPATH:D:/AI/cudnn/lib/x64\n"
"    3rdparty dependencies:\n"
"\n"
"  OpenCV modules:\n"
"    To be built:                 aruco bgsegm bioinspired calib3d ccalib core cudaarithm cudabgsegm cudacodec cudafeatures2d cudafilters cudaimgproc cudalegacy cudaobjdetect cudaoptflow cudastereo cudawarping cudev datasets dnn dnn_objdetect dnn_superres dpm face features2d flann fuzzy gapi hfs highgui img_hash imgcodecs imgproc intensity_transform line_descriptor mcc ml objdetect optflow phase_unwrapping photo plot python3 quality rapid reg rgbd saliency shape signal stereo stitching structured_light superres surface_matching text tracking ts video videoio videostab wechat_qrcode world xfeatures2d ximgproc xobjdetect xphoto\n"
"    Disabled:                    -\n"
"    Disabled by dependency:      -\n"
"    Unavailable:                 alphamat cannops cvv freetype hdf java julia matlab ovis python2 python2 sfm viz\n"
"    Applications:                tests perf_tests apps\n"
"    Documentation:               NO\n"
"    Non-free algorithms:         NO\n"
"\n"
"  Windows RT support:            NO\n"
"\n"
"  GUI: \n"
"    Win32 UI:                    YES\n"
"    VTK support:                 NO\n"
"\n"
"  Media I/O: \n"
"    ZLib:                        build (ver 1.3.1)\n"
"    JPEG:                        build-libjpeg-turbo (ver 3.0.3-70)\n"
"      SIMD Support Request:      YES\n"
"      SIMD Support:              NO\n"
"    WEBP:                        build (ver encoder: 0x020f)\n"
"    PNG:                         build (ver 1.6.43)\n"
"      SIMD Support Request:      YES\n"
"      SIMD Support:              YES (Intel SSE)\n"
"    TIFF:                        build (ver 42 - 4.6.0)\n"
"    JPEG 2000:                   build (ver 2.5.0)\n"
"    OpenEXR:                     build (ver 2.3.0)\n"
"    HDR:                         YES\n"
"    SUNRASTER:                   YES\n"
"    PXM:                         YES\n"
"    PFM:                         YES\n"
"\n"
"  Video I/O:\n"
"    DC1394:                      NO\n"
"    FFMPEG:                      YES (prebuilt binaries)\n"
"      avcodec:                   YES (58.134.100)\n"
"      avformat:                  YES (58.76.100)\n"
"      avutil:                    YES (56.70.100)\n"
"      swscale:                   YES (5.9.100)\n"
"      avresample:                YES (4.0.0)\n"
"    GStreamer:                   NO\n"
"    DirectShow:                  YES\n"
"    Media Foundation:            YES\n"
"      DXVA:                      YES\n"
"\n"
"  Parallel framework:            Concurrency\n"
"\n"
"  Trace:                         YES (with Intel ITT)\n"
"\n"
"  Other third-party libraries:\n"
"    Intel IPP:                   2021.11.0 [2021.11.0]\n"
"           at:                   D:/AI/opencv/cudabuild/3rdparty/ippicv/ippicv_win/icv\n"
"    Intel IPP IW:                sources (2021.11.0)\n"
"              at:                D:/AI/opencv/cudabuild/3rdparty/ippicv/ippicv_win/iw\n"
"    Lapack:                      NO\n"
"    Eigen:                       NO\n"
"    Custom HAL:                  NO\n"
"    Protobuf:                    build (3.19.1)\n"
"    Flatbuffers:                 builtin/3rdparty (23.5.9)\n"
"\n"
"  NVIDIA CUDA:                   YES (ver 12.8, CUFFT CUBLAS FAST_MATH)\n"
"    NVIDIA GPU arch:             50 52 60 61 70 75 80 86 89 90\n"
"    NVIDIA PTX archs:            90\n"
"\n"
"  cuDNN:                         YES (ver 9.11.0)\n"
"\n"
"  OpenCL:                        YES (NVD3D11)\n"
"    Include path:                D:/AI/opencv/opencv-4.10.0/3rdparty/include/opencl/1.2\n"
"    Link libraries:              Dynamic load\n"
"\n"
"  Python 3:\n"
"    Interpreter:                 C:/Users/<USER>/AppData/Local/Programs/Python/Python311/python.exe (ver 3.11.6)\n"
"    Libraries:                   C:/Users/<USER>/AppData/Local/Programs/Python/Python311/libs/python311.lib (ver 3.11.6)\n"
"    Limited API:                 NO\n"
"    numpy:                       C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/numpy/_core/include (ver 2.3.1)\n"
"    install path:                C:/Users/<USER>/AppData/Local/Programs/Python/Python311/Lib/site-packages/cv2/python-3.11\n"
"\n"
"  Python (for build):            C:/Users/<USER>/AppData/Local/Programs/Python/Python311/python.exe\n"
"\n"
"  Java:                          \n"
"    ant:                         NO\n"
"    Java:                        NO\n"
"    JNI:                         NO\n"
"    Java wrappers:               NO\n"
"    Java tests:                  NO\n"
"\n"
"  Install to:                    D:/AI/opencv/cudabuild/install\n"
"-----------------------------------------------------------------\n"
"\n"
