<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Internal structure</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div><div class="header">
  <div class="headertitle"><div class="title">Internal structure</div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#internals_public">Public interface</a></li>
<li class="level1"><a href="#internals_native">Native interface</a></li>
<li class="level1"><a href="#internals_internal">Internal interface</a></li>
<li class="level1"><a href="#internals_platform">Platform interface</a></li>
<li class="level1"><a href="#internals_event">Event interface</a></li>
<li class="level1"><a href="#internals_static">Static functions</a></li>
<li class="level1"><a href="#internals_config">Configuration macros</a></li>
</ul>
</div>
<div class="textblock"><p>There are several interfaces inside GLFW. Each interface has its own area of responsibility and its own naming conventions.</p>
<h1><a class="anchor" id="internals_public"></a>
Public interface</h1>
<p>The most well-known is the public interface, described in the <a class="el" href="glfw3_8h.html" title="The header of the GLFW 3 API.">glfw3.h</a> header file. This is implemented in source files shared by all platforms and these files contain no platform-specific code. This code usually ends up calling the platform and internal interfaces to do the actual work.</p>
<p>The public interface uses the OpenGL naming conventions except with GLFW and glfw instead of GL and gl. For struct members, where OpenGL sets no precedent, it use headless camel case.</p>
<p>Examples: <code>glfwCreateWindow</code>, <code>GLFWwindow</code>, <code>GLFW_RED_BITS</code></p>
<h1><a class="anchor" id="internals_native"></a>
Native interface</h1>
<p>The <a class="el" href="group__native.html">native interface</a> is a small set of publicly available but platform-specific functions, described in the <a class="el" href="glfw3native_8h.html" title="The header of the native access functions.">glfw3native.h</a> header file and used to gain access to the underlying window, context and (on some platforms) display handles used by the platform interface.</p>
<p>The function names of the native interface are similar to those of the public interface, but embeds the name of the interface that the returned handle is from.</p>
<p>Examples: <code>glfwGetX11Window</code>, <code>glfwGetWGLContext</code></p>
<h1><a class="anchor" id="internals_internal"></a>
Internal interface</h1>
<p>The internal interface consists of utility functions used by all other interfaces. It is shared code implemented in the same shared source files as the public and event interfaces. The internal interface is described in the internal.h header file.</p>
<p>The internal interface is in charge of GLFW's global data, which it stores in a <code>_GLFWlibrary</code> struct named <code>_glfw</code>.</p>
<p>The internal interface uses the same style as the public interface, except all global names have a leading underscore.</p>
<p>Examples: <code>_glfwIsValidContextConfig</code>, <code>_GLFWwindow</code>, <code>_glfw.monitorCount</code></p>
<h1><a class="anchor" id="internals_platform"></a>
Platform interface</h1>
<p>The platform interface implements all platform-specific operations as a service to the public interface. This includes event processing. The platform interface is never directly called by application code and never directly calls application-provided callbacks. It is also prohibited from modifying the platform-independent part of the internal structs. Instead, it calls the event interface when events interesting to GLFW are received.</p>
<p>The platform interface mostly mirrors those parts of the public interface that needs to perform platform-specific operations on some or all platforms.</p>
<p>The window system bits of the platform API is called through the <code>_GLFWplatform</code> struct of function pointers, to allow runtime selection of platform. This includes the window and context creation, input and event processing, monitor and Vulkan surface creation parts of GLFW. This is located in the global <code>_glfw</code> struct.</p>
<p>Examples: <code>_glfw.platform.createWindow</code></p>
<p>The timer, threading and module loading bits of the platform API are plain functions with a <code>_glfwPlatform</code> prefix, as these things are independent of what window system is being used.</p>
<p>Examples: <code>_glfwPlatformGetTimerValue</code></p>
<p>The platform interface also defines structs that contain platform-specific global and per-object state. Their names mirror those of the internal interface, except that an interface-specific suffix is added.</p>
<p>Examples: <code>_GLFWwindowX11</code>, <code>_GLFWcontextWGL</code></p>
<p>These structs are incorporated as members into the internal interface structs using special macros that name them after the specific interface used. This prevents shared code from accidentally using these members.</p>
<p>Examples: <code>window-&gt;win32.handle</code>, <code>_glfw.x11.display</code></p>
<h1><a class="anchor" id="internals_event"></a>
Event interface</h1>
<p>The event interface is implemented in the same shared source files as the public interface and is responsible for delivering the events it receives to the application, either via callbacks, via window state changes or both.</p>
<p>The function names of the event interface use a <code>_glfwInput</code> prefix and the ObjectEvent pattern.</p>
<p>Examples: <code>_glfwInputWindowFocus</code>, <code>_glfwInputCursorPos</code></p>
<h1><a class="anchor" id="internals_static"></a>
Static functions</h1>
<p>Static functions may be used by any interface and have no prefixes or suffixes. These use headless camel case.</p>
<p>Examples: <code>isValidElementForJoystick</code></p>
<h1><a class="anchor" id="internals_config"></a>
Configuration macros</h1>
<p>GLFW uses a number of configuration macros to select at compile time which interfaces and code paths to use. They are defined in the GLFW CMake target.</p>
<p>Configuration macros the same style as tokens in the public interface, except with a leading underscore.</p>
<p>Examples: <code>_GLFW_WIN32</code>, <code>_GLFW_BUILD_DLL</code> </p>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
