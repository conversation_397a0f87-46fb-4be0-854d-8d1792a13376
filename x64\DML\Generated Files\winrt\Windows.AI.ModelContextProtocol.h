// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_AI_ModelContextProtocol_H
#define WINRT_Windows_AI_ModelContextProtocol_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.240405.15"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.240405.15"
#include "winrt/impl/Windows.ApplicationModel.2.h"
#include "winrt/impl/Windows.UI.2.h"
#include "winrt/impl/Windows.AI.ModelContextProtocol.2.h"
namespace winrt::impl
{
    template <typename D> auto consume_Windows_AI_ModelContextProtocol_IModelContextProtocolClientContext<D>::OwnerWindowId(winrt::Windows::UI::WindowId const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContext)->put_OwnerWindowId(impl::bind_in(value)));
    }
    template <typename D> auto consume_Windows_AI_ModelContextProtocol_IModelContextProtocolClientContext<D>::OwnerWindowId() const
    {
        winrt::Windows::UI::WindowId value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContext)->get_OwnerWindowId(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServer<D>::Command() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServer)->get_Command(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServer<D>::GetCommandArguments() const
    {
        uint32_t result_impl_size{};
        void** result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServer)->GetCommandArguments(&result_impl_size, &result));
        return com_array<hstring>{ result, result_impl_size, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServer<D>::Info() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServer)->get_Info(&value));
        return winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolServerInfo{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServerCatalog<D>::GetServerInfos() const
    {
        uint32_t result_impl_size{};
        void** result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalog)->GetServerInfos(&result_impl_size, &result));
        return com_array<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolServerInfo>{ result, result_impl_size, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServerCatalog<D>::ActivateServer(winrt::guid const& serverId, winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolClientContext const& client) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalog)->ActivateServer(impl::bind_in(serverId), *(void**)(&client), &result));
        return winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServer{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServerCatalog<D>::CreateClientContext() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalog)->CreateClientContext(&result));
        return winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolClientContext{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServerInfo<D>::Id() const
    {
        winrt::guid value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfo)->get_Id(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServerInfo<D>::Name() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfo)->get_Name(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServerInfo<D>::Description() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfo)->get_Description(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_ModelContextProtocol_IModelContextProtocolServerInfo<D>::GetPackage() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfo)->GetPackage(&result));
        return winrt::Windows::ApplicationModel::Package{ result, take_ownership_from_abi };
    }
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContext> : produce_base<D, winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContext>
    {
        int32_t __stdcall put_OwnerWindowId(struct struct_Windows_UI_WindowId value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().OwnerWindowId(*reinterpret_cast<winrt::Windows::UI::WindowId const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_OwnerWindowId(struct struct_Windows_UI_WindowId* value) noexcept final try
        {
            zero_abi<winrt::Windows::UI::WindowId>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::WindowId>(this->shim().OwnerWindowId());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContextFactory> : produce_base<D, winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContextFactory>
    {
    };
#endif
    template <typename D>
    struct produce<D, winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServer> : produce_base<D, winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServer>
    {
        int32_t __stdcall get_Command(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Command());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetCommandArguments(uint32_t* __resultSize, void*** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            std::tie(*__resultSize, *result) = detach_abi(this->shim().GetCommandArguments());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Info(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolServerInfo>(this->shim().Info());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalog> : produce_base<D, winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalog>
    {
        int32_t __stdcall GetServerInfos(uint32_t* __resultSize, void*** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            std::tie(*__resultSize, *result) = detach_abi(this->shim().GetServerInfos());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ActivateServer(winrt::guid serverId, void* client, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServer>(this->shim().ActivateServer(*reinterpret_cast<winrt::guid const*>(&serverId), *reinterpret_cast<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolClientContext const*>(&client)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateClientContext(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolClientContext>(this->shim().CreateClientContext());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalogFactory> : produce_base<D, winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalogFactory>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfo> : produce_base<D, winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfo>
    {
        int32_t __stdcall get_Id(winrt::guid* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::guid>(this->shim().Id());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Name(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Name());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Description(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Description());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetPackage(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::ApplicationModel::Package>(this->shim().GetPackage());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfoFactory> : produce_base<D, winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfoFactory>
    {
    };
#endif
}
WINRT_EXPORT namespace winrt::Windows::AI::ModelContextProtocol
{
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
    template<> struct hash<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContext> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolClientContextFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalog> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerCatalogFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfo> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::ModelContextProtocol::IModelContextProtocolServerInfoFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolClientContext> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolServerCatalog> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::ModelContextProtocol::ModelContextProtocolServerInfo> : winrt::impl::hash_base {};
#endif
#ifdef __cpp_lib_format
#endif
}
#endif
