Binary Descriptors for Line Segments
====================================

This module shows how to extract line segments from an image by 2 different methods: First segmenting lines with Line Segment Detector LSDDetector and then (or just) using the Binary Descriptor to get the lines and give them a descriptor -- BinaryDescriptor. Finally, we can then match line segments using the BinaryDescriptorMatcher class.

## Two views of a builing
![Two views of a building](https://github.com/opencv/opencv_contrib/assets/810997/e5d438f9-5745-447c-b189-111a16fcdc76)

## Line segments detected and matched
![LSD segments detected and matched](https://github.com/opencv/opencv_contrib/assets/810997/22d89e93-24ad-4939-b48c-9223c76889bd)

* [Image examples from CSDN](https://blog.csdn.net/Small_Munich/article/details/87990946)
