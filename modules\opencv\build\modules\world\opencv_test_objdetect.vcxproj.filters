﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\opencl\test_hogdetector.cpp">
      <Filter>opencv_objdetect\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_aruco_tutorial.cpp">
      <Filter>opencv_objdetect\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_aruco_utils.cpp">
      <Filter>opencv_objdetect\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_arucodetection.cpp">
      <Filter>opencv_objdetect\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_barcode.cpp">
      <Filter>opencv_objdetect\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_boarddetection.cpp">
      <Filter>opencv_objdetect\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_cascadeandhog.cpp">
      <Filter>opencv_objdetect\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_charucodetection.cpp">
      <Filter>opencv_objdetect\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_face.cpp">
      <Filter>opencv_objdetect\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_main.cpp">
      <Filter>opencv_objdetect\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_qrcode.cpp">
      <Filter>opencv_objdetect\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_qrcode_encode.cpp">
      <Filter>opencv_objdetect\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_aruco_utils.hpp">
      <Filter>opencv_objdetect\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_precomp.hpp">
      <Filter>opencv_objdetect\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\objdetect\test\test_qr_utils.hpp">
      <Filter>opencv_objdetect\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_objdetect">
      <UniqueIdentifier>{7FD73A8B-3B8D-3FCF-937B-BBBBB2E3FA96}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_objdetect\Include">
      <UniqueIdentifier>{984649B3-D98C-3994-BB0E-6126EA961932}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_objdetect\Src">
      <UniqueIdentifier>{F88DFDBB-D3BD-3155-BF9C-30A2326711F4}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_objdetect\Src\opencl">
      <UniqueIdentifier>{8594C5CF-75DC-3455-B942-17A3F6BF655F}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
