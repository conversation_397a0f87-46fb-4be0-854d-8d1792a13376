/* This file was automatically generated by learn_color_balance.py script
 * using the following parameters:
 --num_trees 20 --hist_bin_num 64 --max_tree_depth 4 --num_augmented 2 -r 0,0
 */
const int num_features = 4;
const int _num_trees = 20;
const int _num_tree_nodes = 16;
unsigned char _feature_idx[_num_trees * num_features * 2 * (_num_tree_nodes - 1)] = {
  0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 1,
  0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1,
  0, 0, 1, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0,
  0, 0, 1, 0, 0, 0, 1, 1, 0, 1, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0,
  0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0,
  1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 0, 0, 0, 1, 1, 0,
  1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0,
  1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1,
  0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1,
  0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1,
  0, 1, 1, 0, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0,
  1, 0, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0,
  0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1,
  0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 1, 0, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1,
  0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0,
  1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1,
  1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1,
  0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1,
  0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1,
  1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0,
  0, 0, 1, 0, 0, 0, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 1,
  1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0,
  0, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0,
  1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0,
  0, 1, 1, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1,
  1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0,
  0, 0, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0,
  0, 0, 1, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1,
  1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
float _thresh_vals[_num_trees * num_features * 2 * (_num_tree_nodes - 1)] = {
  .193f, .098f, .455f, .040f, .145f, .316f, .571f, .016f, .058f, .137f, .174f, .276f, .356f, .515f, .730f, .606f, .324f,
  .794f, .230f, .440f, .683f, .878f, .134f, .282f, .406f, .532f, .036f, .747f, .830f, .931f, .196f, .145f, .363f, .047f,
  .351f, .279f, .519f, .013f, .887f, .191f, .193f, .361f, .316f, .576f, .445f, .524f, .368f, .752f, .271f, .477f, .636f,
  .798f, .146f, .249f, .423f, .521f, .446f, .023f, .795f, .908f, .259f, .026f, .557f, .125f, .121f, .432f, .774f, .500f,
  .500f, .984f, .202f, .307f, .509f, .038f, .042f, .667f, .500f, .000f, .014f, .560f, .984f, .000f, .125f, .333f, .553f,
  .333f, .860f, .000f, .500f, .000f, .193f, .114f, .432f, .032f, .157f, .310f, .567f, .013f, .048f, .127f, .428f, .271f,
  .370f, .511f, .762f, .615f, .325f, .833f, .193f, .440f, .728f, .887f, .086f, .230f, .411f, .546f, .671f, .009f, .863f,
  .944f, .283f, .174f, .515f, .087f, .209f, .356f, .693f, .059f, .145f, .344f, .254f, .316f, .455f, .571f, .811f, .537f,
  .343f, .751f, .243f, .435f, .630f, .813f, .134f, .310f, .391f, .487f, .597f, .683f, .755f, .878f, .307f, .145f, .446f,
  .063f, .349f, .457f, .576f, .021f, .792f, .268f, .250f, .421f, .463f, .574f, .610f, .478f, .354f, .636f, .271f, .423f,
  .529f, .795f, .146f, .257f, .124f, .458f, .043f, .022f, .752f, .836f, .307f, .026f, .557f, .125f, .135f, .435f, .774f,
  .500f, .500f, .984f, .238f, .500f, .509f, .038f, .042f, .600f, .400f, .000f, .014f, .500f, .667f, .000f, .125f, .250f,
  .455f, .097f, .333f, .984f, .000f, .000f, .271f, .155f, .511f, .078f, .198f, .372f, .696f, .059f, .123f, .183f, .237f,
  .310f, .432f, .564f, .875f, .546f, .324f, .728f, .195f, .423f, .616f, .823f, .131f, .281f, .355f, .490f, .581f, .677f,
  .775f, .885f, .300f, .192f, .515f, .109f, .248f, .374f, .693f, .065f, .157f, .580f, .276f, .341f, .455f, .571f, .811f,
  .532f, .324f, .679f, .196f, .421f, .630f, .804f, .111f, .252f, .348f, .447f, .565f, .247f, .755f, .866f, .335f, .188f,
  .503f, .104f, .270f, .521f, .607f, .021f, .134f, .364f, .332f, .421f, .571f, .576f, .836f, .458f, .294f, .523f, .171f,
  .413f, .501f, .651f, .065f, .273f, .354f, .062f, .052f, .489f, .523f, .784f, .307f, .026f, .557f, .125f, .156f, .432f,
  .774f, .500f, .500f, .984f, .200f, .399f, .509f, .038f, .042f, .560f, .429f, .000f, .014f, .138f, .667f, .000f, .125f,
  .222f, .500f, .522f, .286f, .984f, .500f, .000f, .295f, .186f, .564f, .104f, .293f, .432f, .762f, .059f, .127f, .271f,
  .593f, .353f, .511f, .630f, .875f, .538f, .324f, .685f, .195f, .423f, .616f, .791f, .099f, .281f, .389f, .452f, .581f,
  .293f, .728f, .842f, .320f, .211f, .521f, .098f, .251f, .403f, .693f, .058f, .158f, .580f, .469f, .188f, .455f, .571f,
  .811f, .532f, .324f, .717f, .193f, .415f, .587f, .791f, .122f, .265f, .391f, .457f, .559f, .665f, .753f, .835f, .405f,
  .305f, .503f, .135f, .457f, .483f, .607f, .044f, .182f, .394f, .569f, .156f, .484f, .576f, .836f, .455f, .294f, .576f,
  .248f, .376f, .482f, .651f, .089f, .273f, .181f, .430f, .472f, .500f, .265f, .784f, .414f, .228f, .557f, .026f, .620f,
  .476f, .774f, .500f, .156f, .595f, .304f, .322f, .509f, .038f, .042f, .533f, .391f, .683f, .106f, .175f, .286f, .010f,
  .026f, .226f, .500f, .429f, .197f, .655f, .000f, .800f, .386f, .218f, .564f, .082f, .295f, .479f, .762f, .043f, .155f,
  .293f, .440f, .432f, .511f, .630f, .875f, .544f, .306f, .705f, .185f, .412f, .606f, .791f, .109f, .263f, .372f, .502f,
  .315f, .685f, .741f, .855f, .365f, .221f, .544f, .110f, .307f, .455f, .730f, .059f, .179f, .276f, .468f, .403f, .515f,
  .678f, .860f, .520f, .340f, .683f, .196f, .440f, .591f, .754f, .122f, .274f, .394f, .464f, .556f, .622f, .723f, .835f,
  .375f, .147f, .503f, .055f, .319f, .436f, .607f, .018f, .773f, .624f, .298f, .467f, .450f, .576f, .836f, .454f, .287f,
  .571f, .163f, .376f, .308f, .648f, .086f, .236f, .373f, .427f, .477f, .496f, .333f, .795f, .500f, .307f, .774f, .026f,
  .462f, .557f, .042f, .125f, .191f, .406f, .500f, .509f, .038f, .000f, .138f, .600f, .400f, .000f, .073f, .500f, .010f,
  .000f, .026f, .275f, .226f, .588f, .984f, .688f, .000f, .000f, .386f, .245f, .564f, .097f, .343f, .479f, .762f, .048f,
  .180f, .424f, .466f, .432f, .511f, .630f, .875f, .541f, .335f, .702f, .193f, .413f, .602f, .791f, .116f, .296f, .376f,
  .481f, .210f, .641f, .741f, .863f, .341f, .145f, .531f, .087f, .283f, .455f, .729f, .047f, .109f, .189f, .469f, .375f,
  .515f, .571f, .811f, .553f, .324f, .683f, .196f, .425f, .607f, .761f, .122f, .252f, .368f, .499f, .248f, .648f, .723f,
  .848f, .365f, .135f, .503f, .082f, .279f, .503f, .607f, .044f, .086f, .182f, .319f, .395f, .448f, .576f, .836f, .454f,
  .287f, .571f, .163f, .389f, .308f, .752f, .086f, .250f, .373f, .436f, .498f, .496f, .636f, .834f, .500f, .307f, .774f,
  .026f, .474f, .557f, .042f, .125f, .000f, .414f, .561f, .509f, .038f, .000f, .138f, .560f, .626f, .000f, .400f, .174f,
  .984f, .000f, .073f, .500f, .852f, .226f, .667f, .500f, .000f, .000f, .353f, .141f, .564f, .073f, .265f, .432f, .762f,
  .032f, .106f, .193f, .478f, .470f, .511f, .630f, .875f, .605f, .325f, .728f, .193f, .437f, .652f, .855f, .116f, .264f,
  .412f, .502f, .202f, .685f, .776f, .912f, .341f, .161f, .531f, .087f, .298f, .455f, .729f, .047f, .109f, .185f, .467f,
  .375f, .515f, .571f, .811f, .580f, .324f, .685f, .196f, .425f, .607f, .824f, .122f, .252f, .368f, .512f, .231f, .648f,
  .749f, .885f, .365f, .135f, .503f, .082f, .319f, .499f, .607f, .044f, .086f, .702f, .384f, .439f, .465f, .576f, .836f,
  .478f, .287f, .594f, .163f, .454f, .502f, .784f, .086f, .250f, .364f, .345f, .500f, .370f, .651f, .836f, .476f, .295f,
  .557f, .026f, .474f, .509f, .774f, .125f, .000f, .333f, .364f, .495f, .405f, .038f, .042f, .560f, .626f, .000f, .400f,
  .174f, .010f, .000f, .059f, .500f, .852f, .226f, .667f, .667f, .000f, .000f, .419f, .157f, .564f, .078f, .295f, .479f,
  .762f, .043f, .124f, .256f, .353f, .432f, .511f, .630f, .875f, .605f, .325f, .713f, .193f, .423f, .677f, .842f, .116f,
  .264f, .385f, .502f, .621f, .177f, .756f, .912f, .388f, .174f, .564f, .095f, .310f, .456f, .730f, .047f, .143f, .276f,
  .468f, .431f, .515f, .682f, .860f, .352f, .215f, .591f, .124f, .290f, .536f, .723f, .064f, .172f, .244f, .318f, .428f,
  .548f, .666f, .825f, .394f, .146f, .503f, .055f, .349f, .499f, .607f, .018f, .773f, .271f, .327f, .439f, .465f, .576f,
  .836f, .464f, .287f, .588f, .163f, .377f, .502f, .752f, .086f, .250f, .237f, .454f, .464f, .370f, .636f, .834f, .476f,
  .000f, .557f, .282f, .000f, .509f, .774f, .026f, .481f, .000f, .000f, .495f, .405f, .038f, .042f, .556f, .626f, .000f,
  .343f, .174f, .010f, .000f, .023f, .559f, .852f, .226f, .667f, .667f, .000f, .000f, .468f, .157f, .567f, .078f, .305f,
  .511f, .762f, .043f, .124f, .271f, .464f, .511f, .193f, .630f, .875f, .351f, .208f, .602f, .116f, .302f, .489f, .720f,
  .059f, .164f, .281f, .325f, .414f, .282f, .654f, .842f, .388f, .174f, .564f, .095f, .313f, .456f, .730f, .047f, .143f,
  .279f, .468f, .431f, .515f, .682f, .860f, .356f, .215f, .591f, .124f, .290f, .539f, .753f, .064f, .174f, .274f, .318f,
  .435f, .556f, .647f, .848f, .411f, .146f, .411f, .055f, .349f, .490f, .499f, .018f, .773f, .271f, .327f, .181f, .610f,
  .467f, .448f, .287f, .163f, .454f, .086f, .262f, .359f, .636f, .041f, .476f, .200f, .633f, .257f, .428f, .522f, .795f,
  .435f, .000f, .557f, .049f, .000f, .509f, .774f, .330f, .282f, .500f, .000f, .462f, .405f, .038f, .042f, .393f, .029f,
  .000f, .026f, .226f, .602f, .000f, .500f, .183f, .163f, .337f, .500f, .984f, .500f, .000f, .468f, .157f, .567f, .078f,
  .314f, .511f, .762f, .043f, .124f, .271f, .456f, .511f, .193f, .630f, .875f, .355f, .208f, .602f, .116f, .296f, .414f,
  .761f, .059f, .179f, .263f, .324f, .381f, .502f, .681f, .855f, .356f, .174f, .537f, .095f, .289f, .455f, .729f, .047f,
  .143f, .186f, .471f, .403f, .515f, .588f, .811f, .352f, .215f, .568f, .147f, .282f, .435f, .723f, .077f, .191f, .265f,
  .318f, .395f, .543f, .630f, .824f, .439f, .141f, .443f, .051f, .363f, .490f, .479f, .018f, .773f, .279f, .446f, .223f,
  .607f, .504f, .491f, .298f, .179f, .454f, .086f, .264f, .350f, .571f, .041f, .122f, .390f, .287f, .324f, .431f, .502f,
  .752f, .500f, .307f, .774f, .026f, .500f, .557f, .042f, .125f, .000f, .434f, .524f, .509f, .038f, .000f, .138f, .447f,
  .225f, .000f, .026f, .226f, .600f, .000f, .500f, .337f, .667f, .333f, .269f, .984f, .500f, .000f, .425f, .157f, .564f,
  .078f, .314f, .511f, .762f, .043f, .124f, .269f, .467f, .479f, .561f, .630f, .875f, .372f, .208f, .602f, .148f, .296f,
  .441f, .775f, .072f, .179f, .230f, .324f, .408f, .516f, .652f, .863f, .403f, .179f, .571f, .095f, .337f, .456f, .730f,
  .047f, .145f, .267f, .458f, .056f, .521f, .682f, .860f, .352f, .193f, .568f, .095f, .282f, .435f, .696f, .056f, .147f,
  .230f, .318f, .395f, .534f, .630f, .808f, .484f, .141f, .454f, .051f, .372f, .605f, .487f, .018f, .773f, .316f, .483f,
  .490f, .610f, .486f, .490f, .298f, .200f, .472f, .122f, .275f, .397f, .571f, .061f, .174f, .370f, .364f, .349f, .435f,
  .499f, .752f, .500f, .307f, .667f, .026f, .500f, .661f, .774f, .125f, .000f, .398f, .524f, .557f, .167f, .760f, .042f,
  .500f, .667f, .000f, .429f, .138f, .984f, .000f, .029f, .364f, .906f, .226f, .667f, .500f, .000f, .000f, .488f, .157f,
  .567f, .078f, .366f, .511f, .762f, .043f, .124f, .309f, .487f, .511f, .193f, .630f, .875f, .376f, .208f, .575f, .148f,
  .302f, .452f, .687f, .072f, .182f, .230f, .325f, .413f, .516f, .621f, .833f, .452f, .179f, .571f, .095f, .349f, .515f,
  .730f, .047f, .145f, .244f, .444f, .456f, .206f, .682f, .860f, .368f, .215f, .568f, .124f, .290f, .444f, .689f, .063f,
  .188f, .252f, .343f, .406f, .516f, .630f, .804f, .162f, .085f, .490f, .044f, .086f, .372f, .454f, .015f, .424f, .463f,
  .134f, .262f, .478f, .607f, .490f, .462f, .297f, .583f, .163f, .349f, .522f, .702f, .065f, .237f, .315f, .428f, .499f,
  .397f, .636f, .795f, .500f, .310f, .667f, .026f, .432f, .661f, .000f, .125f, .146f, .500f, .332f, .557f, .167f, .774f,
  .000f, .537f, .667f, .000f, .400f, .138f, .984f, .000f, .029f, .500f, .906f, .226f, .667f, .500f, .000f, .000f, .511f,
  .157f, .696f, .078f, .361f, .564f, .875f, .043f, .124f, .223f, .404f, .561f, .630f, .762f, .896f, .376f, .209f, .578f,
  .116f, .296f, .452f, .687f, .058f, .179f, .264f, .332f, .413f, .516f, .621f, .833f, .455f, .189f, .571f, .098f, .274f,
  .515f, .730f, .056f, .148f, .244f, .354f, .233f, .206f, .682f, .860f, .381f, .230f, .565f, .124f, .315f, .496f, .673f,
  .064f, .177f, .282f, .351f, .441f, .528f, .622f, .804f, .146f, .082f, .503f, .044f, .086f, .349f, .607f, .015f, .424f,
  .085f, .134f, .262f, .298f, .576f, .836f, .349f, .271f, .466f, .146f, .297f, .413f, .608f, .065f, .200f, .364f, .318f,
  .441f, .291f, .531f, .752f, .535f, .000f, .226f, .286f, .000f, .000f, .348f, .026f, .432f, .000f, .000f, .667f, .000f,
  .333f, .423f, .500f, .391f, .000f, .073f, .500f, .600f, .000f, .026f, .226f, .455f, .551f, .379f, .984f, .000f, .000f,
  .564f, .184f, .762f, .089f, .404f, .630f, .875f, .044f, .141f, .271f, .332f, .614f, .752f, .864f, .896f, .385f, .230f,
  .590f, .116f, .332f, .505f, .687f, .059f, .185f, .283f, .506f, .452f, .546f, .626f, .833f, .254f, .161f, .456f, .087f,
  .189f, .300f, .571f, .047f, .109f, .179f, .209f, .275f, .375f, .521f, .730f, .395f, .230f, .565f, .112f, .348f, .505f,
  .751f, .064f, .177f, .290f, .387f, .441f, .282f, .623f, .848f, .146f, .055f, .503f, .018f, .773f, .316f, .607f, .011f,
  .428f, .134f, .887f, .236f, .358f, .576f, .836f, .349f, .271f, .472f, .146f, .364f, .413f, .636f, .065f, .200f, .288f,
  .366f, .441f, .398f, .516f, .795f, .592f, .000f, .774f, .282f, .000f, .735f, .042f, .026f, .476f, .000f, .000f, .038f,
  .127f, .000f, .138f, .500f, .391f, .000f, .029f, .500f, .522f, .000f, .026f, .226f, .455f, .535f, .260f, .984f, .000f,
  .000f, .250f, .155f, .564f, .078f, .198f, .404f, .762f, .043f, .124f, .183f, .223f, .295f, .432f, .630f, .875f, .385f,
  .230f, .574f, .116f, .330f, .505f, .728f, .065f, .173f, .283f, .554f, .453f, .546f, .626f, .842f, .285f, .161f, .515f,
  .087f, .209f, .441f, .693f, .047f, .109f, .184f, .264f, .334f, .456f, .571f, .811f, .352f, .198f, .555f, .124f, .274f,
  .449f, .723f, .076f, .172f, .236f, .318f, .402f, .519f, .622f, .824f, .236f, .146f, .503f, .055f, .147f, .366f, .607f,
  .018f, .773f, .146f, .188f, .280f, .499f, .576f, .836f, .329f, .262f, .449f, .163f, .364f, .350f, .588f, .086f, .200f,
  .296f, .366f, .331f, .412f, .482f, .784f, .571f, .000f, .774f, .307f, .000f, .234f, .042f, .026f, .406f, .000f, .000f,
  .647f, .620f, .000f, .138f, .500f, .400f, .000f, .109f, .068f, .537f, .000f, .026f, .226f, .500f, .447f, .522f, .984f,
  .000f, .000f, .268f, .157f, .511f, .078f, .198f, .346f, .696f, .043f, .124f, .183f, .237f, .313f, .458f, .564f, .875f,
  .351f, .208f, .571f, .116f, .265f, .460f, .728f, .065f, .164f, .230f, .308f, .412f, .516f, .632f, .842f, .300f, .161f,
  .521f, .087f, .209f, .360f, .693f, .047f, .109f, .184f, .276f, .313f, .452f, .571f, .811f, .351f, .196f, .558f, .122f,
  .274f, .449f, .723f, .072f, .166f, .236f, .318f, .421f, .520f, .617f, .824f, .190f, .134f, .503f, .051f, .402f, .280f,
  .607f, .018f, .773f, .156f, .616f, .389f, .345f, .576f, .836f, .326f, .237f, .466f, .179f, .294f, .350f, .636f, .086f,
  .407f, .277f, .338f, .295f, .415f, .562f, .795f, .307f, .026f, .774f, .125f, .000f, .543f, .042f, .500f, .500f, .205f,
  .000f, .420f, .321f, .000f, .138f, .447f, .371f, .560f, .026f, .226f, .500f, .000f, .125f, .297f, .187f, .304f, .500f,
  .522f, .984f, .000f, .256f, .155f, .564f, .078f, .193f, .314f, .762f, .043f, .124f, .428f, .223f, .258f, .470f, .630f,
  .875f, .338f, .193f, .582f, .116f, .264f, .465f, .728f, .065f, .164f, .230f, .296f, .414f, .531f, .652f, .842f, .316f,
  .174f, .571f, .095f, .276f, .433f, .730f, .047f, .143f, .209f, .300f, .343f, .521f, .682f, .860f, .343f, .193f, .587f,
  .105f, .274f, .438f, .751f, .066f, .166f, .230f, .318f, .381f, .519f, .648f, .848f, .271f, .146f, .503f, .055f, .403f,
  .366f, .607f, .018f, .773f, .365f, .445f, .444f, .517f, .576f, .836f, .324f, .237f, .478f, .115f, .287f, .350f, .636f,
  .074f, .405f, .387f, .337f, .295f, .431f, .522f, .795f, .307f, .026f, .667f, .125f, .984f, .426f, .000f, .500f, .500f,
  .156f, .000f, .500f, .557f, .972f, .000f, .429f, .434f, .000f, .023f, .226f, .560f, .000f, .125f, .222f, .760f, .500f,
  .500f, .667f, .000f, .000f, .310f, .157f, .564f, .078f, .256f, .425f, .762f, .043f, .124f, .193f, .258f, .350f, .511f,
  .630f, .875f, .337f, .182f, .597f, .101f, .264f, .443f, .728f, .063f, .161f, .216f, .296f, .395f, .522f, .652f, .842f,
  .341f, .174f, .516f, .095f, .276f, .431f, .678f, .047f, .143f, .209f, .300f, .391f, .460f, .571f, .811f, .324f, .193f,
  .543f, .122f, .254f, .434f, .689f, .066f, .145f, .230f, .431f, .391f, .508f, .597f, .808f, .271f, .146f, .366f, .055f,
  .365f, .357f, .503f, .018f, .773f, .249f, .417f, .316f, .444f, .517f, .607f, .308f, .200f, .472f, .115f, .264f, .357f,
  .571f, .058f, .138f, .260f, .364f, .338f, .431f, .499f, .752f, .414f, .049f, .557f, .125f, .307f, .438f, .000f, .500f,
  .330f, .984f, .333f, .446f, .122f, .014f, .000f, .391f, .333f, .518f, .026f, .174f, .500f, .000f, .125f, .279f, .760f,
  .299f, .500f, .408f, .667f, .000f, .310f, .157f, .479f, .078f, .255f, .370f, .564f, .043f, .124f, .193f, .271f, .483f,
  .418f, .512f, .762f, .335f, .182f, .597f, .086f, .265f, .423f, .728f, .041f, .129f, .230f, .302f, .375f, .522f, .652f,
  .842f, .374f, .184f, .544f, .095f, .300f, .455f, .811f, .047f, .145f, .254f, .341f, .404f, .516f, .569f, .860f, .326f,
  .196f, .584f, .118f, .252f, .434f, .751f, .064f, .141f, .236f, .290f, .391f, .508f, .648f, .848f, .316f, .146f, .421f,
  .055f, .279f, .454f, .503f, .018f, .773f, .183f, .280f, .347f, .571f, .499f, .605f, .294f, .161f, .499f, .086f, .225f,
  .364f, .636f, .058f, .476f, .200f, .264f, .347f, .454f, .571f, .795f, .500f, .520f, .307f, .462f, .571f, .000f, .450f,
  .051f, .220f, .557f, .833f, .984f, .000f, .655f, .532f, .355f, .073f, .556f, .026f, .138f, .500f, .000f, .500f, .183f,
  .906f, .299f, .410f, .333f, .984f, .000f, .370f, .157f, .559f, .078f, .271f, .483f, .762f, .043f, .124f, .198f, .310f,
  .418f, .512f, .752f, .022f, .308f, .161f, .582f, .086f, .230f, .414f, .728f, .042f, .116f, .193f, .281f, .346f, .489f,
  .632f, .842f, .455f, .184f, .729f, .095f, .316f, .579f, .811f, .047f, .145f, .276f, .356f, .544f, .666f, .806f, .860f,
  .326f, .174f, .584f, .088f, .247f, .435f, .751f, .042f, .141f, .215f, .290f, .368f, .528f, .648f, .848f, .347f, .306f,
  .553f, .146f, .364f, .433f, .610f, .055f, .198f, .338f, .451f, .445f, .432f, .096f, .836f, .294f, .161f, .499f, .108f,
  .204f, .381f, .636f, .041f, .474f, .198f, .262f, .324f, .362f, .571f, .795f, .500f, .569f, .307f, .101f, .774f, .000f,
  .423f, .043f, .465f, .121f, .000f, .984f, .000f, .500f, .524f, .333f, .675f, .560f, .292f, .138f, .429f, .000f, .073f,
  .550f, .000f, .195f, .377f, .500f, .984f, .000f, .479f, .183f, .704f, .082f, .310f, .567f, .875f, .043f, .141f, .271f,
  .372f, .511f, .630f, .762f, .896f, .325f, .164f, .602f, .086f, .230f, .414f, .761f, .040f, .131f, .197f, .283f, .352f,
  .516f, .685f, .855f};
float _leaf_vals[_num_trees * num_features * 2 * _num_tree_nodes] = {
  .011f, .029f, .047f, .064f, .075f, .102f, .141f, .172f, .212f, .259f, .308f, .364f, .443f, .497f, .592f, .767f, .069f,
  .165f, .241f, .278f, .357f, .412f, .463f, .540f, .562f, .623f, .676f, .734f, .797f, .838f, .894f, .944f, .014f, .040f,
  .061f, .033f, .040f, .160f, .181f, .101f, .123f, .047f, .195f, .282f, .374f, .775f, .248f, .068f, .064f, .155f, .177f,
  .351f, .409f, .479f, .576f, .451f, .677f, .784f, .817f, .764f, .823f, .860f, .898f, .941f, .154f, .154f, .248f, .248f,
  .050f, .081f, .177f, .227f, .252f, .309f, .385f, .428f, .441f, .525f, .616f, .689f, .435f, .137f, .208f, .406f, .457f,
  .483f, .518f, .576f, .669f, .844f, .593f, .706f, .853f, .853f, .895f, .925f, .012f, .029f, .047f, .067f, .111f, .134f,
  .148f, .178f, .214f, .261f, .311f, .357f, .420f, .476f, .592f, .773f, .057f, .143f, .194f, .262f, .358f, .415f, .465f,
  .541f, .602f, .649f, .655f, .739f, .808f, .849f, .894f, .944f, .050f, .068f, .089f, .118f, .146f, .187f, .211f, .230f,
  .263f, .308f, .364f, .443f, .497f, .581f, .690f, .832f, .079f, .171f, .263f, .306f, .356f, .401f, .452f, .486f, .538f,
  .577f, .629f, .687f, .722f, .766f, .834f, .900f, .046f, .066f, .083f, .064f, .090f, .113f, .143f, .235f, .289f, .416f,
  .094f, .204f, .454f, .074f, .697f, .836f, .067f, .156f, .200f, .332f, .266f, .411f, .473f, .514f, .627f, .575f, .758f,
  .676f, .775f, .826f, .864f, .900f, .162f, .162f, .248f, .248f, .079f, .102f, .165f, .241f, .281f, .337f, .385f, .428f,
  .441f, .525f, .616f, .689f, .397f, .137f, .166f, .307f, .421f, .443f, .525f, .486f, .527f, .585f, .687f, .611f, .767f,
  .821f, .942f, .916f, .055f, .073f, .090f, .110f, .165f, .188f, .207f, .225f, .261f, .312f, .358f, .420f, .475f, .579f,
  .693f, .875f, .079f, .164f, .238f, .277f, .325f, .378f, .448f, .487f, .527f, .557f, .610f, .648f, .716f, .769f, .830f,
  .896f, .038f, .090f, .112f, .131f, .206f, .160f, .224f, .249f, .286f, .334f, .370f, .443f, .497f, .581f, .690f, .832f,
  .056f, .153f, .221f, .278f, .311f, .365f, .420f, .463f, .524f, .562f, .625f, .699f, .696f, .762f, .829f, .889f, .024f,
  .093f, .104f, .119f, .104f, .154f, .153f, .216f, .273f, .376f, .202f, .138f, .609f, .690f, .814f, .930f, .027f, .098f,
  .158f, .252f, .304f, .393f, .706f, .462f, .630f, .554f, .845f, .643f, .852f, .694f, .781f, .858f, .169f, .169f, .248f,
  .248f, .105f, .124f, .110f, .197f, .308f, .242f, .385f, .428f, .441f, .525f, .616f, .689f, .375f, .137f, .146f, .314f,
  .412f, .437f, .454f, .520f, .510f, .615f, .692f, .576f, .701f, .701f, .780f, .846f, .039f, .091f, .109f, .125f, .209f,
  .256f, .251f, .126f, .295f, .350f, .420f, .475f, .568f, .625f, .738f, .875f, .055f, .153f, .236f, .281f, .338f, .390f,
  .425f, .462f, .522f, .563f, .609f, .687f, .674f, .721f, .776f, .846f, .034f, .078f, .123f, .148f, .201f, .153f, .215f,
  .253f, .239f, .335f, .382f, .446f, .502f, .581f, .690f, .832f, .063f, .145f, .220f, .284f, .340f, .386f, .424f, .467f,
  .520f, .550f, .611f, .671f, .718f, .758f, .792f, .854f, .065f, .117f, .138f, .163f, .225f, .371f, .188f, .145f, .457f,
  .345f, .102f, .276f, .609f, .690f, .814f, .930f, .032f, .133f, .188f, .247f, .268f, .350f, .427f, .495f, .538f, .578f,
  .641f, .835f, .700f, .759f, .780f, .868f, .187f, .187f, .135f, .170f, .218f, .144f, .261f, .340f, .416f, .335f, .388f,
  .428f, .441f, .525f, .616f, .689f, .367f, .273f, .143f, .308f, .382f, .439f, .410f, .470f, .524f, .461f, .626f, .528f,
  .583f, .702f, .673f, .773f, .031f, .068f, .124f, .154f, .217f, .154f, .255f, .302f, .358f, .405f, .435f, .475f, .568f,
  .625f, .738f, .875f, .061f, .144f, .221f, .261f, .325f, .366f, .448f, .495f, .538f, .590f, .618f, .659f, .686f, .739f,
  .791f, .858f, .034f, .079f, .149f, .175f, .198f, .231f, .249f, .327f, .353f, .382f, .443f, .489f, .570f, .649f, .740f,
  .882f, .076f, .148f, .218f, .296f, .357f, .400f, .444f, .472f, .516f, .554f, .597f, .630f, .678f, .722f, .781f, .864f,
  .021f, .055f, .135f, .053f, .180f, .150f, .370f, .214f, .331f, .530f, .219f, .326f, .609f, .690f, .814f, .930f, .049f,
  .095f, .149f, .216f, .370f, .294f, .443f, .489f, .526f, .594f, .621f, .747f, .656f, .762f, .780f, .884f, .216f, .248f,
  .160f, .190f, .197f, .356f, .296f, .341f, .391f, .428f, .441f, .525f, .593f, .668f, .760f, .637f, .388f, .250f, .155f,
  .334f, .419f, .456f, .497f, .448f, .591f, .542f, .552f, .719f, .656f, .709f, .849f, .897f, .034f, .078f, .151f, .184f,
  .211f, .253f, .262f, .351f, .358f, .405f, .435f, .475f, .568f, .625f, .738f, .875f, .076f, .148f, .229f, .303f, .341f,
  .376f, .444f, .480f, .548f, .510f, .594f, .638f, .685f, .742f, .800f, .882f, .028f, .062f, .089f, .114f, .174f, .196f,
  .241f, .294f, .335f, .371f, .443f, .482f, .511f, .590f, .714f, .832f, .075f, .157f, .223f, .281f, .342f, .386f, .450f,
  .489f, .542f, .590f, .611f, .653f, .682f, .728f, .783f, .893f, .041f, .076f, .186f, .109f, .175f, .195f, .209f, .227f,
  .274f, .355f, .196f, .314f, .609f, .690f, .814f, .930f, .049f, .097f, .161f, .221f, .415f, .304f, .454f, .492f, .527f,
  .581f, .629f, .747f, .685f, .758f, .836f, .914f, .225f, .248f, .187f, .074f, .228f, .365f, .295f, .337f, .391f, .428f,
  .441f, .525f, .593f, .668f, .760f, .637f, .413f, .277f, .431f, .456f, .115f, .162f, .254f, .334f, .503f, .661f, .515f,
  .515f, .696f, .751f, .836f, .897f, .023f, .057f, .090f, .116f, .180f, .197f, .239f, .283f, .338f, .365f, .420f, .475f,
  .568f, .625f, .738f, .875f, .074f, .157f, .227f, .282f, .365f, .410f, .451f, .504f, .577f, .610f, .646f, .679f, .728f,
  .782f, .855f, .923f, .028f, .062f, .089f, .120f, .165f, .204f, .243f, .304f, .335f, .371f, .443f, .482f, .511f, .590f,
  .714f, .832f, .073f, .157f, .220f, .287f, .343f, .393f, .451f, .489f, .567f, .596f, .616f, .650f, .711f, .760f, .840f,
  .917f, .041f, .076f, .186f, .092f, .203f, .116f, .222f, .261f, .330f, .438f, .214f, .316f, .609f, .690f, .814f, .930f,
  .049f, .104f, .163f, .221f, .414f, .448f, .513f, .561f, .566f, .744f, .614f, .683f, .721f, .761f, .854f, .915f, .228f,
  .248f, .196f, .096f, .300f, .225f, .295f, .344f, .466f, .385f, .403f, .468f, .441f, .525f, .616f, .689f, .414f, .307f,
  .445f, .460f, .115f, .162f, .254f, .334f, .459f, .495f, .501f, .705f, .680f, .751f, .836f, .897f, .031f, .065f, .100f,
  .132f, .201f, .221f, .280f, .333f, .374f, .405f, .435f, .475f, .568f, .625f, .738f, .875f, .073f, .157f, .226f, .288f,
  .349f, .401f, .450f, .489f, .589f, .621f, .649f, .680f, .718f, .759f, .843f, .923f, .029f, .067f, .107f, .140f, .207f,
  .227f, .279f, .339f, .369f, .393f, .444f, .494f, .575f, .651f, .740f, .882f, .042f, .093f, .147f, .184f, .220f, .256f,
  .290f, .323f, .402f, .455f, .495f, .540f, .619f, .687f, .748f, .876f, .021f, .055f, .098f, .053f, .206f, .221f, .389f,
  .239f, .343f, .438f, .228f, .316f, .609f, .690f, .814f, .930f, .049f, .104f, .160f, .221f, .235f, .426f, .455f, .529f,
  .623f, .551f, .600f, .677f, .697f, .760f, .836f, .914f, .232f, .201f, .231f, .309f, .117f, .096f, .070f, .044f, .466f,
  .385f, .403f, .468f, .441f, .525f, .616f, .689f, .418f, .251f, .450f, .394f, .115f, .162f, .254f, .334f, .460f, .488f,
  .494f, .703f, .680f, .751f, .836f, .897f, .031f, .065f, .100f, .132f, .207f, .229f, .289f, .342f, .435f, .346f, .461f,
  .482f, .568f, .625f, .738f, .875f, .043f, .093f, .146f, .180f, .241f, .278f, .307f, .330f, .391f, .451f, .472f, .524f,
  .610f, .651f, .741f, .874f, .029f, .067f, .107f, .140f, .212f, .233f, .269f, .343f, .369f, .393f, .444f, .494f, .575f,
  .651f, .740f, .882f, .042f, .093f, .151f, .188f, .238f, .271f, .293f, .321f, .408f, .459f, .513f, .553f, .609f, .672f,
  .777f, .893f, .021f, .055f, .098f, .053f, .210f, .226f, .355f, .247f, .439f, .514f, .637f, .836f, .333f, .420f, .227f,
  .313f, .019f, .060f, .098f, .133f, .147f, .179f, .237f, .125f, .196f, .407f, .451f, .477f, .572f, .654f, .774f, .903f,
  .239f, .375f, .204f, .250f, .150f, .150f, .096f, .057f, .426f, .383f, .403f, .468f, .441f, .525f, .616f, .689f, .407f,
  .407f, .126f, .244f, .134f, .203f, .294f, .406f, .449f, .469f, .573f, .482f, .751f, .751f, .836f, .897f, .031f, .065f,
  .100f, .132f, .212f, .232f, .281f, .348f, .435f, .346f, .461f, .482f, .568f, .625f, .738f, .875f, .043f, .093f, .152f,
  .190f, .235f, .262f, .295f, .330f, .354f, .417f, .455f, .492f, .620f, .685f, .768f, .888f, .029f, .067f, .107f, .140f,
  .167f, .219f, .238f, .298f, .352f, .382f, .443f, .485f, .532f, .596f, .714f, .832f, .056f, .105f, .161f, .195f, .230f,
  .267f, .289f, .322f, .367f, .414f, .462f, .529f, .579f, .667f, .742f, .875f, .021f, .053f, .094f, .052f, .214f, .235f,
  .288f, .235f, .451f, .530f, .632f, .826f, .316f, .233f, .466f, .356f, .019f, .060f, .084f, .110f, .192f, .162f, .235f,
  .287f, .418f, .363f, .447f, .482f, .573f, .631f, .724f, .880f, .243f, .248f, .210f, .074f, .237f, .308f, .378f, .334f,
  .391f, .428f, .441f, .525f, .593f, .668f, .760f, .637f, .398f, .398f, .235f, .418f, .105f, .166f, .287f, .405f, .458f,
  .482f, .589f, .488f, .630f, .630f, .751f, .866f, .031f, .065f, .100f, .132f, .218f, .235f, .269f, .344f, .400f, .435f,
  .478f, .396f, .568f, .625f, .738f, .875f, .056f, .106f, .160f, .190f, .215f, .248f, .292f, .331f, .383f, .415f, .459f,
  .503f, .594f, .678f, .783f, .898f, .029f, .067f, .108f, .144f, .226f, .241f, .293f, .353f, .275f, .384f, .446f, .502f,
  .579f, .651f, .740f, .882f, .038f, .077f, .112f, .161f, .202f, .241f, .289f, .323f, .362f, .410f, .462f, .515f, .582f,
  .658f, .727f, .868f, .021f, .053f, .094f, .052f, .227f, .249f, .316f, .237f, .483f, .630f, .726f, .836f, .583f, .493f,
  .274f, .426f, .034f, .080f, .109f, .146f, .210f, .181f, .285f, .223f, .385f, .436f, .469f, .544f, .576f, .619f, .714f,
  .880f, .250f, .248f, .218f, .074f, .241f, .293f, .378f, .334f, .408f, .522f, .409f, .317f, .547f, .397f, .616f, .689f,
  .410f, .308f, .440f, .469f, .111f, .160f, .250f, .328f, .516f, .674f, .506f, .506f, .685f, .751f, .836f, .897f, .031f,
  .065f, .100f, .132f, .229f, .267f, .359f, .244f, .442f, .346f, .461f, .482f, .568f, .625f, .738f, .875f, .056f, .106f,
  .160f, .190f, .219f, .255f, .302f, .340f, .392f, .421f, .463f, .496f, .578f, .642f, .717f, .869f, .029f, .067f, .108f,
  .144f, .223f, .250f, .318f, .362f, .400f, .444f, .476f, .508f, .579f, .651f, .740f, .882f, .032f, .096f, .155f, .192f,
  .227f, .255f, .306f, .349f, .381f, .418f, .464f, .519f, .589f, .653f, .721f, .867f, .018f, .049f, .037f, .080f, .201f,
  .248f, .091f, .152f, .229f, .253f, .323f, .259f, .632f, .826f, .274f, .428f, .028f, .096f, .165f, .230f, .434f, .361f,
  .449f, .500f, .554f, .596f, .610f, .679f, .678f, .743f, .801f, .903f, .260f, .248f, .208f, .243f, .259f, .302f, .414f,
  .315f, .408f, .522f, .409f, .317f, .535f, .620f, .357f, .692f, .405f, .266f, .432f, .463f, .111f, .170f, .250f, .328f,
  .535f, .656f, .525f, .525f, .693f, .751f, .836f, .897f, .031f, .065f, .100f, .132f, .211f, .249f, .320f, .372f, .478f,
  .396f, .568f, .608f, .647f, .738f, .849f, .902f, .032f, .095f, .153f, .190f, .237f, .269f, .305f, .344f, .390f, .423f,
  .465f, .514f, .581f, .637f, .718f, .869f, .033f, .072f, .110f, .152f, .214f, .250f, .273f, .316f, .419f, .449f, .476f,
  .508f, .579f, .651f, .740f, .882f, .039f, .095f, .144f, .185f, .250f, .296f, .323f, .362f, .416f, .467f, .502f, .531f,
  .589f, .643f, .714f, .867f, .018f, .049f, .036f, .079f, .095f, .246f, .091f, .131f, .233f, .268f, .342f, .294f, .609f,
  .690f, .814f, .930f, .037f, .093f, .146f, .175f, .270f, .226f, .408f, .339f, .448f, .303f, .472f, .506f, .580f, .640f,
  .726f, .880f, .273f, .235f, .283f, .319f, .117f, .096f, .070f, .044f, .475f, .609f, .357f, .692f, .414f, .278f, .536f,
  .462f, .374f, .229f, .139f, .344f, .414f, .441f, .505f, .402f, .496f, .572f, .606f, .526f, .680f, .751f, .836f, .897f,
  .031f, .072f, .110f, .157f, .239f, .277f, .437f, .352f, .565f, .578f, .631f, .514f, .748f, .578f, .849f, .902f, .039f,
  .095f, .152f, .202f, .252f, .305f, .345f, .432f, .423f, .467f, .509f, .544f, .592f, .640f, .713f, .869f, .028f, .062f,
  .089f, .120f, .152f, .173f, .191f, .211f, .252f, .277f, .302f, .324f, .446f, .502f, .592f, .767f, .043f, .090f, .136f,
  .191f, .256f, .311f, .359f, .390f, .424f, .470f, .492f, .534f, .593f, .655f, .776f, .893f, .012f, .032f, .021f, .058f,
  .093f, .135f, .059f, .026f, .228f, .270f, .292f, .324f, .609f, .690f, .814f, .930f, .042f, .097f, .141f, .176f, .218f,
  .342f, .143f, .270f, .446f, .303f, .480f, .516f, .580f, .627f, .774f, .903f, .292f, .238f, .299f, .331f, .117f, .096f,
  .070f, .044f, .430f, .536f, .612f, .347f, .593f, .668f, .760f, .637f, .386f, .214f, .133f, .342f, .405f, .444f, .507f,
  .442f, .464f, .479f, .565f, .517f, .680f, .751f, .836f, .897f, .031f, .065f, .100f, .131f, .165f, .188f, .204f, .222f,
  .275f, .303f, .336f, .383f, .568f, .625f, .738f, .875f, .046f, .101f, .141f, .193f, .256f, .302f, .345f, .451f, .425f,
  .468f, .509f, .535f, .586f, .649f, .744f, .874f, .028f, .062f, .089f, .120f, .155f, .189f, .214f, .247f, .310f, .338f,
  .392f, .444f, .497f, .581f, .690f, .832f, .049f, .101f, .142f, .181f, .211f, .247f, .287f, .325f, .377f, .426f, .473f,
  .530f, .587f, .645f, .745f, .875f, .021f, .055f, .098f, .053f, .280f, .306f, .168f, .226f, .257f, .314f, .351f, .309f,
  .609f, .690f, .814f, .930f, .048f, .102f, .140f, .185f, .274f, .321f, .143f, .250f, .443f, .359f, .443f, .483f, .542f,
  .609f, .746f, .894f, .317f, .252f, .324f, .348f, .117f, .096f, .070f, .044f, .594f, .404f, .499f, .531f, .593f, .668f,
  .760f, .637f, .402f, .260f, .124f, .345f, .382f, .444f, .423f, .448f, .473f, .511f, .563f, .516f, .680f, .751f, .836f,
  .897f, .031f, .065f, .100f, .132f, .166f, .188f, .207f, .225f, .301f, .322f, .341f, .404f, .475f, .579f, .693f, .875f,
  .048f, .100f, .142f, .187f, .216f, .244f, .283f, .318f, .378f, .430f, .473f, .525f, .586f, .642f, .744f, .874f, .028f,
  .062f, .089f, .120f, .155f, .189f, .215f, .249f, .274f, .345f, .363f, .409f, .502f, .581f, .690f, .832f, .053f, .095f,
  .132f, .175f, .211f, .243f, .288f, .326f, .399f, .434f, .474f, .527f, .579f, .637f, .745f, .875f, .021f, .053f, .092f,
  .052f, .054f, .108f, .180f, .116f, .204f, .271f, .321f, .362f, .609f, .690f, .814f, .930f, .054f, .105f, .177f, .148f,
  .216f, .260f, .394f, .301f, .226f, .381f, .443f, .484f, .588f, .680f, .774f, .903f, .339f, .339f, .248f, .248f, .222f,
  .288f, .111f, .057f, .352f, .366f, .422f, .505f, .593f, .668f, .760f, .637f, .387f, .016f, .160f, .380f, .113f, .221f,
  .346f, .410f, .442f, .482f, .474f, .496f, .563f, .508f, .700f, .866f, .031f, .065f, .100f, .131f, .148f, .177f, .202f,
  .218f, .338f, .280f, .357f, .420f, .568f, .625f, .738f, .875f, .053f, .098f, .135f, .175f, .212f, .242f, .277f, .307f,
  .384f, .434f, .476f, .536f, .594f, .644f, .744f, .874f, .029f, .067f, .107f, .140f, .184f, .215f, .249f, .274f, .335f,
  .376f, .406f, .479f, .579f, .651f, .740f, .882f, .056f, .085f, .129f, .177f, .209f, .238f, .289f, .323f, .361f, .413f,
  .471f, .539f, .601f, .677f, .776f, .893f, .021f, .055f, .098f, .053f, .138f, .211f, .281f, .183f, .345f, .232f, .387f,
  .290f, .609f, .690f, .814f, .930f, .057f, .083f, .167f, .124f, .232f, .182f, .401f, .293f, .226f, .358f, .445f, .497f,
  .581f, .654f, .774f, .903f, .353f, .353f, .248f, .248f, .112f, .240f, .290f, .096f, .354f, .374f, .393f, .433f, .595f,
  .468f, .648f, .692f, .378f, .016f, .106f, .339f, .119f, .191f, .327f, .397f, .446f, .477f, .512f, .549f, .680f, .751f,
  .836f, .897f, .031f, .065f, .100f, .132f, .172f, .211f, .338f, .261f, .349f, .375f, .402f, .437f, .568f, .625f, .738f,
  .875f, .057f, .086f, .129f, .173f, .199f, .232f, .280f, .306f, .366f, .419f, .471f, .531f, .592f, .652f, .744f, .874f,
  .029f, .067f, .107f, .140f, .184f, .215f, .249f, .286f, .371f, .396f, .420f, .443f, .477f, .533f, .682f, .832f, .047f,
  .094f, .134f, .165f, .205f, .237f, .277f, .306f, .366f, .411f, .466f, .519f, .558f, .619f, .727f, .868f, .021f, .055f,
  .098f, .053f, .115f, .175f, .238f, .195f, .305f, .390f, .332f, .232f, .424f, .243f, .626f, .826f, .030f, .071f, .108f,
  .138f, .195f, .114f, .295f, .240f, .320f, .362f, .424f, .489f, .542f, .613f, .730f, .880f, .385f, .385f, .248f, .375f,
  .172f, .262f, .393f, .347f, .364f, .416f, .412f, .433f, .448f, .486f, .648f, .533f, .354f, .016f, .183f, .308f, .111f,
  .177f, .269f, .346f, .414f, .440f, .454f, .520f, .507f, .544f, .700f, .866f, .031f, .065f, .100f, .132f, .172f, .210f,
  .232f, .261f, .323f, .361f, .390f, .418f, .444f, .468f, .548f, .773f, .030f, .072f, .110f, .153f, .205f, .246f, .283f,
  .312f, .355f, .399f, .461f, .532f, .594f, .652f, .744f, .874f, .029f, .067f, .108f, .147f, .206f, .237f, .286f, .334f,
  .358f, .424f, .461f, .494f, .522f, .565f, .791f, .882f, .039f, .091f, .132f, .168f, .214f, .244f, .278f, .311f, .373f,
  .413f, .465f, .521f, .596f, .677f, .776f, .893f, .021f, .055f, .098f, .053f, .166f, .219f, .411f, .273f, .379f, .461f,
  .256f, .206f, .501f, .264f, .587f, .816f, .031f, .066f, .104f, .139f, .154f, .187f, .203f, .241f, .318f, .360f, .414f,
  .493f, .621f, .692f, .775f, .903f, .429f, .223f, .430f, .462f, .480f, .504f, .532f, .555f, .170f, .266f, .111f, .057f,
  .327f, .407f, .441f, .475f, .307f, .307f, .126f, .257f, .091f, .171f, .253f, .332f, .390f, .420f, .457f, .488f, .573f,
  .503f, .700f, .866f, .031f, .065f, .100f, .132f, .176f, .215f, .261f, .311f, .386f, .439f, .466f, .489f, .562f, .514f,
  .612f, .773f, .033f, .073f, .102f, .146f, .177f, .216f, .255f, .295f, .323f, .388f, .450f, .502f, .580f, .644f, .744f,
  .874f, .029f, .067f, .108f, .147f, .210f, .259f, .308f, .364f, .503f, .543f, .584f, .646f, .723f, .578f, .791f, .882f,
  .028f, .068f, .122f, .163f, .194f, .231f, .273f, .310f, .357f, .403f, .464f, .533f, .596f, .677f, .776f, .893f, .048f,
  .094f, .177f, .218f, .307f, .432f, .273f, .229f, .500f, .253f, .603f, .513f, .754f, .673f, .825f, .930f, .018f, .062f,
  .102f, .129f, .142f, .173f, .188f, .226f, .391f, .313f, .471f, .402f, .621f, .692f, .775f, .903f, .489f, .443f, .207f,
  .494f, .541f, .577f, .648f, .720f, .175f, .237f, .111f, .057f, .287f, .335f, .409f, .374f, .264f, .187f, .285f, .318f,
  .156f, .106f, .341f, .251f, .380f, .391f, .432f, .475f, .584f, .513f, .700f, .866f, .031f, .068f, .108f, .157f, .212f,
  .261f, .312f, .371f, .483f, .517f, .571f, .611f, .665f, .738f, .849f, .902f, .028f, .068f, .120f, .154f, .188f, .219f,
  .259f, .305f, .338f, .387f, .454f, .520f, .609f, .691f, .768f, .888f};
