TURBOJPEG_1.0
{
  global:
    TJBUFSIZE;
    tjCompress;
    tjDecompress;
    tjDecompressHeader;
    tj<PERSON><PERSON>roy;
    tjGetErrorStr;
    tjInitCompress;
    tjInitDecompress;
  local:
    *;
};

TURBOJPEG_1.1
{
  global:
    TJBUFSIZEYUV;
    tjDecompressHeader2;
    tjDecompressToYUV;
    tjEncodeYUV;
} TURBOJPEG_1.0;

TURBOJPEG_1.2
{
  global:
    tjAlloc;
    tjBufSize;
    tjBufSizeYUV;
    tjCompress2;
    tjDecompress2;
    tjEncodeYUV2;
    tjFree;
    tjGetScalingFactors;
    tjInitTransform;
    tjTransform;
    Java_org_libjpegturbo_turbojpeg_TJ_bufSize;
    Java_org_libjpegturbo_turbojpeg_TJ_getScalingFactors;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_init;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_destroy;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_init;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decompressHeader;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_destroy;
    Java_org_libjpegturbo_turbojpeg_TJTransformer_init;
    Java_org_libjpegturbo_turbojpeg_TJTransformer_transform;
} TURBOJPEG_1.1;

TURBOJPEG_1.4
{
  global:
    tjBufSizeYUV2;
    tjCompressFromYUV;
    tjCompressFromYUVPlanes;
    tjDecodeYUV;
    tjDecodeYUVPlanes;
    tjDecompressHeader3;
    tjDecompressToYUV2;
    tjDecompressToYUVPlanes;
    tjEncodeYUV3;
    tjEncodeYUVPlanes;
    tjPlaneHeight;
    tjPlaneSizeYUV;
    tjPlaneWidth;
    Java_org_libjpegturbo_turbojpeg_TJ_bufSizeYUV__IIII;
    Java_org_libjpegturbo_turbojpeg_TJ_planeHeight__III;
    Java_org_libjpegturbo_turbojpeg_TJ_planeSizeYUV__IIIII;
    Java_org_libjpegturbo_turbojpeg_TJ_planeWidth__III;
} TURBOJPEG_1.2;

TURBOJPEG_2.0
{
  global:
    tjGetErrorCode;
    tjGetErrorStr2;
    tjLoadImage;
    tjSaveImage;
} TURBOJPEG_1.4;

TURBOJPEG_3
{
  global:
    tj3Alloc;
    tj3Compress8;
    tj3Compress12;
    tj3Compress16;
    tj3CompressFromYUV8;
    tj3CompressFromYUVPlanes8;
    tj3DecodeYUV8;
    tj3DecodeYUVPlanes8;
    tj3Decompress8;
    tj3Decompress12;
    tj3Decompress16;
    tj3DecompressHeader;
    tj3DecompressToYUV8;
    tj3DecompressToYUVPlanes8;
    tj3Destroy;
    tj3EncodeYUV8;
    tj3EncodeYUVPlanes8;
    tj3Free;
    tj3Get;
    tj3GetErrorCode;
    tj3GetErrorStr;
    tj3GetScalingFactors;
    tj3Init;
    tj3JPEGBufSize;
    tj3LoadImage8;
    tj3LoadImage12;
    tj3LoadImage16;
    tj3SaveImage8;
    tj3SaveImage12;
    tj3SaveImage16;
    tj3Set;
    tj3SetCroppingRegion;
    tj3SetScalingFactor;
    tj3Transform;
    tj3YUVBufSize;
    tj3YUVPlaneHeight;
    tj3YUVPlaneSize;
    tj3YUVPlaneWidth;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_compress8___3BIIIIII_3B;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_compress8___3IIIIIII_3B;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_compress12;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_compress16;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_compressFromYUV8;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_encodeYUV8___3BIIIIII_3_3B_3I_3I;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_encodeYUV8___3IIIIIII_3_3B_3I_3I;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_get;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_loadImage;
    Java_org_libjpegturbo_turbojpeg_TJCompressor_set;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decodeYUV8___3_3B_3I_3I_3BIIIIII;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decodeYUV8___3_3B_3I_3I_3IIIIIII;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decompress8___3BI_3BIIII;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decompress8___3BI_3IIIII;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decompress12;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decompress16;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_decompressToYUV8;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_get;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_saveImage;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_set;
    Java_org_libjpegturbo_turbojpeg_TJDecompressor_setCroppingRegion;
} TURBOJPEG_2.0;
