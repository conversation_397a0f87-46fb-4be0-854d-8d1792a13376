// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Management_Update_1_H
#define WINRT_Windows_Management_Update_1_H
#include "winrt/impl/Windows.Management.Update.0.h"
WINRT_EXPORT namespace winrt::Windows::Management::Update
{
    struct WINRT_IMPL_EMPTY_BASES IPreviewBuildsManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPreviewBuildsManager>
    {
        IPreviewBuildsManager(std::nullptr_t = nullptr) noexcept {}
        IPreviewBuildsManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPreviewBuildsManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPreviewBuildsManagerStatics>
    {
        IPreviewBuildsManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IPreviewBuildsManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPreviewBuildsState :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPreviewBuildsState>
    {
        IPreviewBuildsState(std::nullptr_t = nullptr) noexcept {}
        IPreviewBuildsState(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdate :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdate>
    {
        IWindowsUpdate(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdateActionCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdateActionCompletedEventArgs>
    {
        IWindowsUpdateActionCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdateActionCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdateActionProgress :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdateActionProgress>
    {
        IWindowsUpdateActionProgress(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdateActionProgress(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdateActionResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdateActionResult>
    {
        IWindowsUpdateActionResult(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdateActionResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdateAdministrator :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdateAdministrator>
    {
        IWindowsUpdateAdministrator(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdateAdministrator(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdateAdministratorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdateAdministratorStatics>
    {
        IWindowsUpdateAdministratorStatics(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdateAdministratorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdateApprovalData :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdateApprovalData>
    {
        IWindowsUpdateApprovalData(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdateApprovalData(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdateAttentionRequiredInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdateAttentionRequiredInfo>
    {
        IWindowsUpdateAttentionRequiredInfo(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdateAttentionRequiredInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdateAttentionRequiredReasonChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdateAttentionRequiredReasonChangedEventArgs>
    {
        IWindowsUpdateAttentionRequiredReasonChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdateAttentionRequiredReasonChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdateGetAdministratorResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdateGetAdministratorResult>
    {
        IWindowsUpdateGetAdministratorResult(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdateGetAdministratorResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdateItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdateItem>
    {
        IWindowsUpdateItem(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdateItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdateManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdateManager>
    {
        IWindowsUpdateManager(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdateManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdateManagerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdateManagerFactory>
    {
        IWindowsUpdateManagerFactory(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdateManagerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdateProgressChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdateProgressChangedEventArgs>
    {
        IWindowsUpdateProgressChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdateProgressChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdateRestartRequestOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdateRestartRequestOptions>
    {
        IWindowsUpdateRestartRequestOptions(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdateRestartRequestOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdateRestartRequestOptionsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdateRestartRequestOptionsFactory>
    {
        IWindowsUpdateRestartRequestOptionsFactory(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdateRestartRequestOptionsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowsUpdateScanCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowsUpdateScanCompletedEventArgs>
    {
        IWindowsUpdateScanCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IWindowsUpdateScanCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
