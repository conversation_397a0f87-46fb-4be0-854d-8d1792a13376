// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_ApplicationModel_DataTransfer_DragDrop_Core_1_H
#define WINRT_Windows_ApplicationModel_DataTransfer_DragDrop_Core_1_H
#include "winrt/impl/Windows.ApplicationModel.DataTransfer.DragDrop.Core.0.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::DataTransfer::DragDrop::Core
{
    struct WINRT_IMPL_EMPTY_BASES ICoreDragDropManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreDragDropManager>
    {
        ICoreDragDropManager(std::nullptr_t = nullptr) noexcept {}
        ICoreDragDropManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreDragDropManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreDragDropManagerStatics>
    {
        ICoreDragDropManagerStatics(std::nullptr_t = nullptr) noexcept {}
        ICoreDragDropManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreDragInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreDragInfo>
    {
        ICoreDragInfo(std::nullptr_t = nullptr) noexcept {}
        ICoreDragInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreDragInfo2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreDragInfo2>,
        impl::require<winrt::Windows::ApplicationModel::DataTransfer::DragDrop::Core::ICoreDragInfo2, winrt::Windows::ApplicationModel::DataTransfer::DragDrop::Core::ICoreDragInfo>
    {
        ICoreDragInfo2(std::nullptr_t = nullptr) noexcept {}
        ICoreDragInfo2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreDragOperation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreDragOperation>
    {
        ICoreDragOperation(std::nullptr_t = nullptr) noexcept {}
        ICoreDragOperation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreDragOperation2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreDragOperation2>,
        impl::require<winrt::Windows::ApplicationModel::DataTransfer::DragDrop::Core::ICoreDragOperation2, winrt::Windows::ApplicationModel::DataTransfer::DragDrop::Core::ICoreDragOperation>
    {
        ICoreDragOperation2(std::nullptr_t = nullptr) noexcept {}
        ICoreDragOperation2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreDragUIOverride :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreDragUIOverride>
    {
        ICoreDragUIOverride(std::nullptr_t = nullptr) noexcept {}
        ICoreDragUIOverride(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreDropOperationTarget :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreDropOperationTarget>
    {
        ICoreDropOperationTarget(std::nullptr_t = nullptr) noexcept {}
        ICoreDropOperationTarget(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreDropOperationTargetRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreDropOperationTargetRequestedEventArgs>
    {
        ICoreDropOperationTargetRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreDropOperationTargetRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
