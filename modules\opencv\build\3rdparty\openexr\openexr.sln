﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "3rdparty", "3rdparty", "{AA6DB7B9-0EDA-3A2D-8842-DF94D596B079}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakeTargets", "CMakeTargets", "{55D86C65-3472-3F5E-A0F2-E8AC916C6FFA}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{F2526E4E-3C28-36B3-A47A-CE6139EFF28A}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{87B4C361-A8BF-3768-9B92-12C6F0E219AC}"
	ProjectSection(ProjectDependencies) = postProject
		{F2526E4E-3C28-36B3-A47A-CE6139EFF28A} = {F2526E4E-3C28-36B3-A47A-CE6139EFF28A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "IlmImf", "IlmImf.vcxproj", "{6003A4BA-E7FA-3CE7-AAC1-0583BE9220D1}"
	ProjectSection(ProjectDependencies) = postProject
		{5BA6087C-B49F-31D7-A8F0-C37E5E8F7F80} = {5BA6087C-B49F-31D7-A8F0-C37E5E8F7F80}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PACKAGE", "PACKAGE.vcxproj", "{61E6F357-649E-3DD3-A24D-4DD08E8C3B48}"
	ProjectSection(ProjectDependencies) = postProject
		{F2526E4E-3C28-36B3-A47A-CE6139EFF28A} = {F2526E4E-3C28-36B3-A47A-CE6139EFF28A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RUN_TESTS", "RUN_TESTS.vcxproj", "{95292468-78DB-3129-9015-93DDCDE4641F}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "zlib", "..\zlib\zlib.vcxproj", "{5BA6087C-B49F-31D7-A8F0-C37E5E8F7F80}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F2526E4E-3C28-36B3-A47A-CE6139EFF28A}.Debug|x64.ActiveCfg = Debug|x64
		{F2526E4E-3C28-36B3-A47A-CE6139EFF28A}.Debug|x64.Build.0 = Debug|x64
		{F2526E4E-3C28-36B3-A47A-CE6139EFF28A}.Release|x64.ActiveCfg = Release|x64
		{F2526E4E-3C28-36B3-A47A-CE6139EFF28A}.Release|x64.Build.0 = Release|x64
		{87B4C361-A8BF-3768-9B92-12C6F0E219AC}.Debug|x64.ActiveCfg = Debug|x64
		{87B4C361-A8BF-3768-9B92-12C6F0E219AC}.Release|x64.ActiveCfg = Release|x64
		{6003A4BA-E7FA-3CE7-AAC1-0583BE9220D1}.Debug|x64.ActiveCfg = Debug|x64
		{6003A4BA-E7FA-3CE7-AAC1-0583BE9220D1}.Debug|x64.Build.0 = Debug|x64
		{6003A4BA-E7FA-3CE7-AAC1-0583BE9220D1}.Release|x64.ActiveCfg = Release|x64
		{6003A4BA-E7FA-3CE7-AAC1-0583BE9220D1}.Release|x64.Build.0 = Release|x64
		{61E6F357-649E-3DD3-A24D-4DD08E8C3B48}.Debug|x64.ActiveCfg = Debug|x64
		{61E6F357-649E-3DD3-A24D-4DD08E8C3B48}.Release|x64.ActiveCfg = Release|x64
		{95292468-78DB-3129-9015-93DDCDE4641F}.Debug|x64.ActiveCfg = Debug|x64
		{95292468-78DB-3129-9015-93DDCDE4641F}.Release|x64.ActiveCfg = Release|x64
		{5BA6087C-B49F-31D7-A8F0-C37E5E8F7F80}.Debug|x64.ActiveCfg = Debug|x64
		{5BA6087C-B49F-31D7-A8F0-C37E5E8F7F80}.Debug|x64.Build.0 = Debug|x64
		{5BA6087C-B49F-31D7-A8F0-C37E5E8F7F80}.Release|x64.ActiveCfg = Release|x64
		{5BA6087C-B49F-31D7-A8F0-C37E5E8F7F80}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{6003A4BA-E7FA-3CE7-AAC1-0583BE9220D1} = {AA6DB7B9-0EDA-3A2D-8842-DF94D596B079}
		{5BA6087C-B49F-31D7-A8F0-C37E5E8F7F80} = {AA6DB7B9-0EDA-3A2D-8842-DF94D596B079}
		{F2526E4E-3C28-36B3-A47A-CE6139EFF28A} = {55D86C65-3472-3F5E-A0F2-E8AC916C6FFA}
		{87B4C361-A8BF-3768-9B92-12C6F0E219AC} = {55D86C65-3472-3F5E-A0F2-E8AC916C6FFA}
		{61E6F357-649E-3DD3-A24D-4DD08E8C3B48} = {55D86C65-3472-3F5E-A0F2-E8AC916C6FFA}
		{95292468-78DB-3129-9015-93DDCDE4641F} = {55D86C65-3472-3F5E-A0F2-E8AC916C6FFA}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {03423234-90FB-3229-9348-88D24F6C1DB6}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
