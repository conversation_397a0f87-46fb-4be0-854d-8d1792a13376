﻿<?xml version="1.0" encoding="utf-8"?>
<Package xmlns="http://schemas.microsoft.com/appx/2010/manifest" xmlns:m2="http://schemas.microsoft.com/appx/2013/manifest">

  <Identity Name="f8308285-aea6-41b1-a76e-9954cfd46c7e"
            Publisher="CN=ericmitt"
            Version="1.0.0.0" />

  <Properties>
    <DisplayName>FaceDetection</DisplayName>
    <PublisherDisplayName>ericmitt</PublisherDisplayName>
    <Logo>Assets\StoreLogo.png</Logo>
  </Properties>

  <Prerequisites>
    <OSMinVersion>6.3.0</OSMinVersion>
    <OSMaxVersionTested>6.3.0</OSMaxVersionTested>
  </Prerequisites>

  <Resources>
    <Resource Language="x-generate"/>
  </Resources>

  <Applications>
    <Application Id="App"
        Executable="$targetnametoken$.exe"
        EntryPoint="FaceDetection.App">
        <m2:VisualElements
            DisplayName="FaceDetection"
            Square150x150Logo="Assets\Logo.png"
            Square30x30Logo="Assets\SmallLogo.png"
            Description="FaceDetection"
            ForegroundText="light"
            BackgroundColor="#464646">
            <m2:SplashScreen Image="Assets\SplashScreen.png" />
        </m2:VisualElements>
    </Application>
  </Applications>
  <Capabilities>
    <Capability Name="internetClient" />
  </Capabilities>
</Package>