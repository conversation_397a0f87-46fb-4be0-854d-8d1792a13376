﻿  perf_3vs4.cpp
  perf_blend.cpp
  perf_color.cpp
  perf_filters.cpp
  perf_gftt.cpp
  perf_imgproc.cpp
  perf_imgwarp.cpp
  perf_pyramid.cpp
  perf_bilateral.cpp
  perf_blur.cpp
  perf_canny.cpp
  perf_contours.cpp
  perf_corners.cpp
  perf_cvt_color.cpp
  perf_distanceTransform.cpp
  perf_filter2d.cpp
  perf_floodfill.cpp
  perf_goodFeaturesToTrack.cpp
  perf_histogram.cpp
  perf_houghcircles.cpp
  perf_integral.cpp
  perf_intelligent_scissors.cpp
  perf_main.cpp
  perf_morph.cpp
  perf_phasecorr.cpp
  perf_pyramids.cpp
  perf_remap.cpp
  perf_resize.cpp
  perf_sepfilters.cpp
  perf_spatialgradient.cpp
  perf_threshold.cpp
  perf_warp.cpp
  perf_accumulate.cpp
  perf_houghlines.cpp
  perf_matchTemplate.cpp
  perf_moments.cpp
  perf_accumulate.cpp
  perf_houghlines.cpp
  perf_matchTemplate.cpp
  perf_moments.cpp
  opencv_perf_imgproc.vcxproj -> D:\AI\opencv\cudabuild\bin\Release\opencv_perf_imgproc.exe
