// This file is part of OpenCV project.
// It is subject to the license terms in the LICENSE file found in the top-level directory
// of this distribution and at http://opencv.org/license.html.

#include "perf_precomp.hpp"
#include <opencv2/dnn/shape_utils.hpp>
#include <opencv2/core/utils/configuration.private.hpp>

namespace opencv_test {

// Flops_Kernel_Input_OutCN_Group_Stride_Pad_Dilation_PadAdjust_PadMode_Bias
struct TestSize_ {
    int width, height;
    operator Size() const { return Size(width, height); }
};
struct ConvParam_t {
    struct TestSize_ kernel;
    struct BlobShape { int dims[4]; } shapeIn;
    int outCN;
    int groups;
    struct TestSize_ stride;
    struct TestSize_ dilation;
    struct TestSize_ pad;
    struct TestSize_ padAdjust;
    const char* padMode;
    bool hasBias;
    double declared_flops;
};
// Details: #12142
// Last update: 2023-11
// Extended and classified: #24547
static const ConvParam_t testConvolution_Configs[] = {
    /* GFLOPS 3.398 x 20 = 67.956 */ {{7, 7}, {{1, 128, 46, 46}}, 128, 1, {1, 1}, {1, 1}, {3, 3}, {0, 0}, "", true, **********.},
    /* GFLOPS 16.987 x 3 = 50.962 */ {{5, 5}, {{1, 1152, 16, 16}}, 1152, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 16987226112.},
    /* GFLOPS 23.122 x 2 = 46.244 */ {{5, 5}, {{1, 672, 32, 32}}, 672, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 23121788928.},
    /* GFLOPS 4.566 x 5 = 22.828 */ {{7, 7}, {{1, 172, 46, 46}}, 128, 1, {1, 1}, {1, 1}, {3, 3}, {0, 0}, "", true, 4565684736.},
    /* GFLOPS 11.797 x 1 = 11.797 */ {{5, 5}, {{1, 240, 64, 64}}, 240, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 11797463040.},
    /* GFLOPS 11.797 x 1 = 11.797 */ {{5, 5}, {{1, 480, 32, 32}}, 480, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 11796971520.},
    /* GFLOPS 5.780 x 1 = 5.780 */ {{5, 5}, {{1, 672, 32, 32}}, 672, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 5780447232.},
    /* GFLOPS 4.247 x 1 = 4.247 */ {{5, 5}, {{1, 144, 128, 128}}, 144, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 4247322624.},
    /* GFLOPS 3.407 x 1 = 3.407 */ {{3, 3}, {{1, 512, 19, 19}}, 1024, 1, {1, 1}, {6, 6}, {6, 6}, {0, 0}, "", true, 3407193088.},
    /* GFLOPS 1.598 x 2 = 3.195 */ {{3, 3}, {{1, 32, 416, 416}}, 64, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", false, 1597652992.},
    /* GFLOPS 1.596 x 2 = 3.193 */ {{3, 3}, {{1, 64, 208, 208}}, 128, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", false, 1596268544.},
    /* GFLOPS 1.596 x 2 = 3.191 */ {{3, 3}, {{1, 128, 104, 104}}, 256, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", false, 1595576320.},
    /* GFLOPS 1.595 x 2 = 3.190 */ {{3, 3}, {{1, 512, 26, 26}}, 1024, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", false, 1595057152.},
    /* GFLOPS 2.719 x 1 = 2.719 */ {{3, 3}, {{1, 96, 256, 256}}, 96, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 2719481856.},
    /* GFLOPS 1.995 x 1 = 1.995 */ {{9, 9}, {{1, 3, 320, 400}}, 32, 1, {1, 1}, {1, 1}, {4, 4}, {0, 0}, "", true, 1994752000.},
    /* GFLOPS 0.945 x 2 = 1.891 */ {{3, 3}, {{1, 32, 320, 320}}, 64, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 945356800.},
    /* GFLOPS 0.945 x 2 = 1.889 */ {{3, 3}, {{1, 64, 160, 160}}, 128, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 944537600.},
    /* GFLOPS 0.944 x 2 = 1.888 */ {{3, 3}, {{1, 128, 80, 80}}, 256, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 944128000.},
    /* GFLOPS 0.944 x 2 = 1.888 */ {{3, 3}, {{1, 256, 40, 40}}, 512, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 943923200.},
    /* GFLOPS 1.195 x 1 = 1.195 */ {{9, 9}, {{1, 32, 240, 320}}, 3, 1, {1, 1}, {1, 1}, {4, 4}, {0, 0}, "", true, 1194624000.},
    /* GFLOPS 1.182 x 1 = 1.182 */ {{3, 3}, {{1, 32, 320, 400}}, 64, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 1181696000.},
    /* GFLOPS 1.181 x 1 = 1.181 */ {{3, 3}, {{1, 64, 160, 200}}, 128, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 1180672000.},
    /* GFLOPS 1.062 x 1 = 1.062 */ {{3, 3}, {{1, 240, 64, 64}}, 240, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 1061928960.},
    /* GFLOPS 0.237 x 4 = 0.947 */ {{3, 3}, {{1, 16, 320, 320}}, 32, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 236748800.},
    /* GFLOPS 0.236 x 4 = 0.945 */ {{3, 3}, {{1, 32, 160, 160}}, 64, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 236339200.},
    /* GFLOPS 0.236 x 4 = 0.945 */ {{3, 3}, {{1, 64, 80, 80}}, 128, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 236134400.},
    /* GFLOPS 0.896 x 1 = 0.896 */ {{5, 5}, {{1, 96, 27, 27}}, 256, 2, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 895981824.},
    /* GFLOPS 0.850 x 1 = 0.850 */ {{7, 7}, {{1, 3, 600, 800}}, 24, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 849600000.},
    /* GFLOPS 0.356 x 2 = 0.711 */ {{6, 6}, {{1, 3, 640, 640}}, 16, 1, {2, 2}, {1, 1}, {2, 2}, {0, 0}, "", true, 355532800.},
    /* GFLOPS 0.701 x 1 = 0.701 */ {{3, 3}, {{1, 128, 75, 100}}, 160, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 700720000.},
    /* GFLOPS 0.483 x 1 = 0.483 */ {{7, 7}, {{1, 3, 320, 320}}, 64, 1, {2, 2}, {1, 1}, {3, 3}, {0, 0}, "", false, 483328000.},
    /* GFLOPS 0.472 x 1 = 0.472 */ {{3, 3}, {{1, 512, 19, 19}}, 512, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 471910400.},
    /* GFLOPS 0.426 x 1 = 0.426 */ {{3, 3}, {{1, 128, 75, 75}}, 128, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 426037760.},
    /* GFLOPS 0.426 x 1 = 0.426 */ {{3, 3}, {{1, 256, 38, 38}}, 256, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 425945344.},
    /* GFLOPS 0.415 x 1 = 0.415 */ {{3, 3}, {{1, 64, 150, 150}}, 64, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 415080000.},
    /* GFLOPS 0.399 x 1 = 0.399 */ {{3, 3}, {{1, 32, 208, 208}}, 64, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", false, 399413248.},
    /* GFLOPS 0.090 x 4 = 0.360 */ {{3, 3}, {{1, 3, 640, 640}}, 16, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 90112000.},
    /* GFLOPS 0.170 x 2 = 0.340 */ {{3, 3}, {{1, 64, 96, 96}}, 64, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 170016768.},
    /* GFLOPS 0.315 x 1 = 0.315 */ {{3, 3}, {{1, 96, 75, 100}}, 96, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 315369600.},
    /* GFLOPS 0.240 x 1 = 0.240 */ {{3, 3}, {{1, 192, 38, 38}}, 192, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 239611584.},
    /* GFLOPS 0.237 x 1 = 0.237 */ {{7, 7}, {{1, 3, 224, 224}}, 64, 1, {2, 2}, {1, 1}, {3, 3}, {0, 0}, "", false, 236830720.},
    /* GFLOPS 0.213 x 1 = 0.213 */ {{3, 3}, {{1, 128, 38, 38}}, 256, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", false, 213018880.},
    /* GFLOPS 0.213 x 1 = 0.213 */ {{3, 3}, {{1, 128, 19, 19}}, 256, 1, {1, 1}, {2, 2}, {2, 2}, {0, 0}, "", false, 213018880.},
    /* GFLOPS 0.212 x 1 = 0.212 */ {{7, 7}, {{1, 3, 300, 300}}, 32, 1, {2, 2}, {1, 1}, {3, 3}, {0, 0}, "", true, 212400000.},
    /* GFLOPS 0.211 x 1 = 0.211 */ {{11, 11}, {{1, 3, 227, 227}}, 96, 1, {4, 4}, {1, 1}, {0, 0}, {0, 0}, "", true, 211120800.},
    /* GFLOPS 0.159 x 1 = 0.159 */ {{7, 7}, {{1, 3, 300, 300}}, 24, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 159300000.},
    /* GFLOPS 0.133 x 1 = 0.133 */ {{3, 3}, {{1, 128, 38, 38}}, 160, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 133136800.},
    /* GFLOPS 0.120 x 1 = 0.120 */ {{5, 5}, {{1, 32, 28, 28}}, 96, 1, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 120497664.},
    /* GFLOPS 0.060 x 2 = 0.119 */ {{3, 3}, {{1, 3, 736, 736}}, 8, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 59586560.},
    /* GFLOPS 0.118 x 1 = 0.118 */ {{3, 3}, {{1, 64, 80, 80}}, 64, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", false, 118067200.},
    /* GFLOPS 0.118 x 1 = 0.118 */ {{3, 3}, {{1, 128, 40, 40}}, 128, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", false, 118016000.},
    /* GFLOPS 0.115 x 1 = 0.115 */ {{3, 3}, {{1, 3, 512, 512}}, 32, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 115343360.},
    /* GFLOPS 0.107 x 1 = 0.107 */ {{3, 3}, {{1, 32, 75, 75}}, 128, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", false, 106648064.},
    /* GFLOPS 0.050 x 2 = 0.101 */ {{2, 2}, {{1, 512, 2, 25}}, 512, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 50343936.},
    /* GFLOPS 0.044 x 2 = 0.087 */ {{5, 5}, {{1, 3, 192, 192}}, 32, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 43608800.},
    /* GFLOPS 0.042 x 2 = 0.085 */ {{3, 3}, {{1, 128, 48, 48}}, 32, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 42485760.},
    /* GFLOPS 0.021 x 4 = 0.084 */ {{5, 1}, {{1, 32, 32, 64}}, 32, 1, {1, 1}, {1, 1}, {2, 0}, {0, 0}, "", false, 21037056.},
    /* GFLOPS 0.021 x 4 = 0.084 */ {{1, 5}, {{1, 32, 32, 64}}, 32, 1, {1, 1}, {1, 1}, {0, 2}, {0, 0}, "", true, 21037056.},
    /* GFLOPS 0.076 x 1 = 0.076 */ {{3, 3}, {{1, 3, 416, 416}}, 32, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", false, 76144640.},
    /* GFLOPS 0.038 x 2 = 0.076 */ {{3, 3}, {{1, 32, 32, 64}}, 32, 1, {1, 1}, {8, 8}, {8, 8}, {0, 0}, "", true, 37814272.},
    /* GFLOPS 0.038 x 2 = 0.076 */ {{3, 3}, {{1, 32, 32, 64}}, 32, 1, {1, 1}, {4, 4}, {4, 4}, {0, 0}, "", true, 37814272.},
    /* GFLOPS 0.038 x 2 = 0.076 */ {{3, 3}, {{1, 32, 32, 64}}, 32, 1, {1, 1}, {2, 2}, {2, 2}, {0, 0}, "", true, 37814272.},
    /* GFLOPS 0.038 x 2 = 0.076 */ {{3, 3}, {{1, 32, 32, 64}}, 32, 1, {1, 1}, {16, 16}, {16, 16}, {0, 0}, "", true, 37814272.},
    /* GFLOPS 0.032 x 2 = 0.065 */ {{3, 3}, {{1, 3, 192, 192}}, 64, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 32440320.},
    /* GFLOPS 0.060 x 1 = 0.060 */ {{3, 3}, {{1, 96, 38, 38}}, 96, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 59920224.},
    /* GFLOPS 0.059 x 1 = 0.059 */ {{3, 3}, {{1, 256, 10, 10}}, 512, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 58995200.},
    /* GFLOPS 0.045 x 1 = 0.045 */ {{3, 3}, {{1, 3, 227, 227}}, 64, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", true, 44946880.},
    /* GFLOPS 0.044 x 1 = 0.044 */ {{3, 3}, {{1, 128, 19, 19}}, 192, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 44256000.},
    /* GFLOPS 0.043 x 1 = 0.043 */ {{7, 7}, {{1, 3, 96, 96}}, 64, 1, {2, 2}, {1, 1}, {3, 3}, {0, 0}, "", true, 43499520.},
    /* GFLOPS 0.022 x 2 = 0.043 */ {{3, 3}, {{1, 3, 224, 224}}, 32, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", true, 21684960.},
    /* GFLOPS 0.022 x 2 = 0.043 */ {{3, 3}, {{1, 3, 258, 258}}, 24, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", true, 21626880.},
    /* GFLOPS 0.040 x 1 = 0.040 */ {{3, 3}, {{1, 3, 300, 300}}, 32, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 39600000.},
    /* GFLOPS 0.034 x 1 = 0.034 */ {{2, 2}, {{1, 64, 64, 128}}, 32, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", false, 33619968.},
    /* GFLOPS 0.016 x 2 = 0.033 */ {{3, 3}, {{1, 3, 224, 224}}, 24, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", true, 16263720.},
    /* GFLOPS 0.005 x 6 = 0.032 */ {{3, 3}, {{1, 16, 48, 48}}, 32, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 5326848.},
    /* GFLOPS 0.005 x 6 = 0.032 */ {{3, 3}, {{1, 32, 24, 24}}, 64, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 5317632.},
    /* GFLOPS 0.015 x 2 = 0.030 */ {{5, 5}, {{1, 24, 14, 14}}, 64, 1, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 15065344.},
    /* GFLOPS 0.029 x 1 = 0.029 */ {{3, 3}, {{1, 256, 10, 10}}, 256, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 29497600.},
    /* GFLOPS 0.023 x 1 = 0.023 */ {{3, 3}, {{1, 3, 256, 512}}, 13, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 23429120.},
    /* GFLOPS 0.017 x 1 = 0.017 */ {{2, 2}, {{1, 16, 128, 256}}, 16, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", false, 16908288.},
    /* GFLOPS 0.003 x 6 = 0.016 */ {{3, 3}, {{1, 16, 48, 48}}, 16, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 2663424.},
    /* GFLOPS 0.015 x 1 = 0.015 */ {{5, 5}, {{1, 48, 7, 7}}, 128, 1, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 15059072.},
    /* GFLOPS 0.005 x 2 = 0.011 */ {{3, 3}, {{1, 3, 256, 256}}, 6, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 5406720.},
    /* GFLOPS 0.005 x 2 = 0.011 */ {{3, 3}, {{1, 6, 128, 128}}, 12, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 5357568.},
    /* GFLOPS 0.005 x 2 = 0.011 */ {{3, 3}, {{1, 12, 64, 64}}, 24, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 5332992.},
    /* GFLOPS 0.005 x 2 = 0.011 */ {{3, 3}, {{1, 24, 32, 32}}, 48, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 5320704.},
    /* GFLOPS 0.003 x 4 = 0.011 */ {{3, 3}, {{1, 16, 24, 24}}, 64, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 2663424.},
    /* GFLOPS 0.010 x 1 = 0.010 */ {{5, 5}, {{1, 32, 7, 7}}, 128, 1, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 10041472.},
    /* GFLOPS 0.008 x 1 = 0.008 */ {{5, 5}, {{1, 16, 14, 14}}, 48, 1, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 7535808.},
    /* GFLOPS 0.007 x 1 = 0.007 */ {{3, 3}, {{1, 160, 6, 6}}, 256, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 6637824.},
    /* GFLOPS 0.003 x 2 = 0.005 */ {{3, 3}, {{1, 32, 24, 24}}, 32, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 2658816.},
    /* GFLOPS 0.003 x 2 = 0.005 */ {{3, 3}, {{1, 32, 12, 12}}, 128, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 2658816.},
    /* GFLOPS 0.004 x 1 = 0.004 */ {{5, 5}, {{1, 16, 12, 12}}, 32, 1, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 3691008.},
    /* GFLOPS 0.004 x 1 = 0.004 */ {{5, 5}, {{1, 32, 6, 6}}, 64, 1, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 3688704.},
    /* GFLOPS 0.004 x 1 = 0.004 */ {{5, 5}, {{1, 32, 12, 12}}, 64, 1, {2, 2}, {1, 1}, {2, 2}, {0, 0}, "", true, 3688704.},
    /* GFLOPS 0.004 x 1 = 0.004 */ {{5, 5}, {{1, 64, 6, 6}}, 128, 1, {2, 2}, {1, 1}, {2, 2}, {0, 0}, "", true, 3687552.},
    /* GFLOPS 0.001 x 2 = 0.003 */ {{3, 3}, {{1, 3, 128, 128}}, 6, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 1351680.},
    /* GFLOPS 0.001 x 2 = 0.003 */ {{3, 3}, {{1, 6, 64, 64}}, 12, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 1339392.},
    /* GFLOPS 0.001 x 2 = 0.003 */ {{3, 3}, {{1, 12, 32, 32}}, 24, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 1333248.},
    /* GFLOPS 0.001 x 2 = 0.003 */ {{3, 3}, {{1, 16, 12, 12}}, 128, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 1331712.},
    /* GFLOPS 0.001 x 2 = 0.003 */ {{3, 3}, {{1, 24, 16, 16}}, 48, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 1330176.},
    /* GFLOPS 0.002 x 1 = 0.002 */ {{3, 3}, {{1, 128, 3, 3}}, 256, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 2360320.},
    /* GFLOPS 0.001 x 1 = 0.001 */ {{3, 3}, {{1, 128, 3, 3}}, 128, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 1180160.},
    /* GFLOPS 0.001 x 2 = 0.001 */ {{3, 3}, {{1, 16, 24, 24}}, 16, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 665856.},
    /* GFLOPS 0.000 x 2 = 0.001 */ {{2, 2}, {{1, 192, 2, 2}}, 195, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 299715.},
    /* GFLOPS 0.000 x 2 = 0.000 */ {{2, 2}, {{1, 192, 2, 2}}, 117, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 179829.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{3, 3}, {{1, 64, 2, 2}}, 128, 1, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 147584.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{3, 3}, {{1, 64, 2, 2}}, 64, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 73792.},
    /* GFLOPS 0.000 x 2 = 0.000 */ {{2, 2}, {{1, 192, 2, 2}}, 1, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 1537.},
};

static const ConvParam_t testConvolution_1x1_Configs[] = {
    /* GFLOPS 0.280 x 5 = 1.402 */ {{1, 1}, {{1, 576, 38, 50}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 280409600.},
    /* GFLOPS 0.210 x 6 = 1.262 */ {{1, 1}, {{1, 576, 38, 50}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 210307200.},
    /* GFLOPS 0.357 x 3 = 1.072 */ {{1, 1}, {{1, 64, 208, 208}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 357187584.},
    /* GFLOPS 0.246 x 4 = 0.985 */ {{1, 1}, {{1, 256, 75, 100}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 246240000.},
    /* GFLOPS 0.053 x 18 = 0.947 */ {{1, 1}, {{1, 128, 40, 40}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 52633600.},
    /* GFLOPS 0.712 x 1 = 0.712 */ {{1, 1}, {{1, 128, 208, 208}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 711606272.},
    /* GFLOPS 0.178 x 4 = 0.712 */ {{1, 1}, {{1, 128, 104, 104}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 177901568.},
    /* GFLOPS 0.354 x 2 = 0.707 */ {{1, 1}, {{1, 256, 52, 52}}, 255, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 353723760.},
    /* GFLOPS 0.351 x 2 = 0.701 */ {{1, 1}, {{1, 576, 38, 50}}, 160, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 350512000.},
    /* GFLOPS 0.211 x 3 = 0.634 */ {{1, 1}, {{1, 64, 80, 80}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 211353600.},
    /* GFLOPS 0.211 x 3 = 0.632 */ {{1, 1}, {{1, 128, 40, 40}}, 512, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 210534400.},
    /* GFLOPS 0.105 x 6 = 0.632 */ {{1, 1}, {{1, 128, 80, 80}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 105267200.},
    /* GFLOPS 0.210 x 3 = 0.630 */ {{1, 1}, {{1, 512, 40, 40}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 209920000.},
    /* GFLOPS 0.615 x 1 = 0.615 */ {{1, 1}, {{1, 320, 75, 100}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 615360000.},
    /* GFLOPS 0.044 x 14 = 0.609 */ {{1, 1}, {{1, 1632, 7, 7}}, 272, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 43515920.},
    /* GFLOPS 0.185 x 3 = 0.554 */ {{1, 1}, {{1, 192, 75, 100}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 184800000.},
    /* GFLOPS 0.266 x 2 = 0.532 */ {{1, 1}, {{1, 240, 48, 48}}, 240, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 265973760.},
    /* GFLOPS 0.491 x 1 = 0.491 */ {{1, 1}, {{1, 576, 38, 50}}, 224, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 490716800.},
    /* GFLOPS 0.079 x 6 = 0.473 */ {{1, 1}, {{1, 192, 40, 40}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 78848000.},
    /* GFLOPS 0.079 x 6 = 0.472 */ {{1, 1}, {{1, 384, 20, 20}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 78745600.},
    /* GFLOPS 0.155 x 3 = 0.464 */ {{1, 1}, {{1, 112, 32, 32}}, 672, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 154828800.},
    /* GFLOPS 0.114 x 4 = 0.454 */ {{1, 1}, {{1, 192, 16, 16}}, 1152, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 113541120.},
    /* GFLOPS 0.089 x 5 = 0.443 */ {{1, 1}, {{1, 512, 13, 13}}, 512, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 88691200.},
    /* GFLOPS 0.428 x 1 = 0.428 */ {{1, 1}, {{1, 64, 64, 64}}, 810, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "VALID", true, 427991040.},
    /* GFLOPS 0.053 x 8 = 0.426 */ {{1, 1}, {{1, 32, 160, 160}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 53248000.},
    /* GFLOPS 0.211 x 2 = 0.423 */ {{1, 1}, {{1, 64, 160, 160}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 211353600.},
    /* GFLOPS 0.106 x 4 = 0.423 */ {{1, 1}, {{1, 64, 160, 160}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 105676800.},
    /* GFLOPS 0.421 x 1 = 0.421 */ {{1, 1}, {{1, 576, 38, 50}}, 192, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 420614400.},
    /* GFLOPS 0.211 x 2 = 0.421 */ {{1, 1}, {{1, 64, 80, 80}}, 255, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 210528000.},
    /* GFLOPS 0.420 x 1 = 0.420 */ {{1, 1}, {{1, 256, 40, 40}}, 512, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 420249600.},
    /* GFLOPS 0.420 x 1 = 0.420 */ {{1, 1}, {{1, 1024, 10, 10}}, 2048, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 419635200.},
    /* GFLOPS 0.210 x 2 = 0.420 */ {{1, 1}, {{1, 256, 80, 80}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 210124800.},
    /* GFLOPS 0.376 x 1 = 0.376 */ {{1, 1}, {{1, 24, 300, 400}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "VALID", true, 376320000.},
    /* GFLOPS 0.179 x 2 = 0.357 */ {{1, 1}, {{1, 64, 208, 208}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 178593792.},
    /* GFLOPS 0.089 x 4 = 0.357 */ {{1, 1}, {{1, 64, 104, 104}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 89296896.},
    /* GFLOPS 0.356 x 1 = 0.356 */ {{1, 1}, {{1, 128, 104, 104}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 355803136.},
    /* GFLOPS 0.113 x 3 = 0.340 */ {{1, 1}, {{1, 1152, 16, 16}}, 192, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 113295360.},
    /* GFLOPS 0.080 x 4 = 0.321 */ {{1, 1}, {{1, 56, 46, 46}}, 336, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 80340288.},
    /* GFLOPS 0.158 x 2 = 0.315 */ {{1, 1}, {{1, 192, 80, 80}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 157696000.},
    /* GFLOPS 0.157 x 2 = 0.315 */ {{1, 1}, {{1, 384, 40, 40}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 157491200.},
    /* GFLOPS 0.154 x 2 = 0.309 */ {{1, 1}, {{1, 672, 32, 32}}, 112, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 154255360.},
    /* GFLOPS 0.103 x 3 = 0.309 */ {{1, 1}, {{1, 512, 7, 7}}, 2048, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 102860800.},
    /* GFLOPS 0.308 x 1 = 0.308 */ {{1, 1}, {{1, 320, 75, 100}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 307680000.},
    /* GFLOPS 0.034 x 9 = 0.304 */ {{1, 1}, {{1, 64, 64, 64}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "VALID", true, 33816576.},
    /* GFLOPS 0.017 x 17 = 0.290 */ {{1, 1}, {{1, 32, 32, 64}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 17039360.},
    /* GFLOPS 0.017 x 16 = 0.269 */ {{1, 1}, {{1, 128, 32, 64}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 16842752.},
    /* GFLOPS 0.266 x 1 = 0.266 */ {{1, 1}, {{1, 768, 26, 26}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 265987072.},
    /* GFLOPS 0.132 x 2 = 0.263 */ {{1, 1}, {{1, 128, 80, 80}}, 80, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 131584000.},
    /* GFLOPS 0.026 x 10 = 0.263 */ {{1, 1}, {{1, 128, 40, 40}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 26316800.},
    /* GFLOPS 0.262 x 1 = 0.262 */ {{1, 1}, {{1, 2560, 20, 20}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 262195200.},
    /* GFLOPS 0.248 x 1 = 0.248 */ {{1, 1}, {{1, 64, 150, 200}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 247680000.},
    /* GFLOPS 0.041 x 6 = 0.245 */ {{1, 1}, {{1, 80, 23, 23}}, 480, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 40881120.},
    /* GFLOPS 0.079 x 3 = 0.237 */ {{1, 1}, {{1, 80, 32, 32}}, 480, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 79134720.},
    /* GFLOPS 0.116 x 2 = 0.231 */ {{1, 1}, {{1, 24, 128, 128}}, 144, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 115605504.},
    /* GFLOPS 0.107 x 2 = 0.215 */ {{1, 1}, {{1, 16, 184, 184}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 107255808.},
    /* GFLOPS 0.106 x 2 = 0.213 */ {{1, 1}, {{1, 32, 160, 160}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 106496000.},
    /* GFLOPS 0.105 x 2 = 0.210 */ {{1, 1}, {{1, 128, 40, 40}}, 255, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 104856000.},
    /* GFLOPS 0.208 x 1 = 0.208 */ {{1, 1}, {{1, 16, 256, 256}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 207618048.},
    /* GFLOPS 0.206 x 1 = 0.206 */ {{1, 1}, {{1, 256, 56, 56}}, 512, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", false, 205922304.},
    /* GFLOPS 0.206 x 1 = 0.206 */ {{1, 1}, {{1, 512, 28, 28}}, 1024, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", false, 205721600.},
    /* GFLOPS 0.206 x 1 = 0.206 */ {{1, 1}, {{1, 1024, 14, 14}}, 2048, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", false, 205621248.},
    /* GFLOPS 0.103 x 2 = 0.206 */ {{1, 1}, {{1, 1024, 7, 7}}, 1024, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 102810624.},
    /* GFLOPS 0.103 x 2 = 0.206 */ {{1, 1}, {{1, 2048, 7, 7}}, 512, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 102785536.},
    /* GFLOPS 0.201 x 1 = 0.201 */ {{1, 1}, {{1, 512, 14, 14}}, 1000, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 200900000.},
    /* GFLOPS 0.190 x 1 = 0.190 */ {{1, 1}, {{1, 256, 38, 38}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 189637632.},
    /* GFLOPS 0.047 x 4 = 0.190 */ {{1, 1}, {{1, 256, 38, 38}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 47409408.},
    /* GFLOPS 0.189 x 1 = 0.189 */ {{1, 1}, {{1, 1152, 16, 16}}, 320, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 188825600.},
    /* GFLOPS 0.185 x 1 = 0.185 */ {{1, 1}, {{1, 128, 75, 75}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 185040000.},
    /* GFLOPS 0.180 x 1 = 0.180 */ {{1, 1}, {{1, 224, 56, 56}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 180232192.},
    /* GFLOPS 0.045 x 4 = 0.179 */ {{1, 1}, {{1, 16, 184, 184}}, 40, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 44689920.},
    /* GFLOPS 0.089 x 2 = 0.177 */ {{1, 1}, {{1, 24, 112, 112}}, 144, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 88510464.},
    /* GFLOPS 0.088 x 2 = 0.177 */ {{1, 1}, {{1, 1024, 13, 13}}, 255, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 88301655.},
    /* GFLOPS 0.041 x 4 = 0.163 */ {{1, 1}, {{1, 480, 23, 23}}, 80, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 40669520.},
    /* GFLOPS 0.080 x 2 = 0.159 */ {{1, 1}, {{1, 336, 46, 46}}, 56, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 79747808.},
    /* GFLOPS 0.080 x 2 = 0.159 */ {{1, 1}, {{1, 40, 64, 64}}, 240, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 79626240.},
    /* GFLOPS 0.079 x 2 = 0.159 */ {{1, 1}, {{1, 48, 160, 160}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 79462400.},
    /* GFLOPS 0.079 x 2 = 0.158 */ {{1, 1}, {{1, 96, 80, 80}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 79052800.},
    /* GFLOPS 0.079 x 2 = 0.157 */ {{1, 1}, {{1, 480, 32, 32}}, 80, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 78725120.},
    /* GFLOPS 0.074 x 2 = 0.147 */ {{1, 1}, {{1, 8, 368, 368}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 73670656.},
    /* GFLOPS 0.072 x 2 = 0.144 */ {{1, 1}, {{1, 1024, 10, 10}}, 352, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 72124800.},
    /* GFLOPS 0.072 x 2 = 0.143 */ {{1, 1}, {{1, 1632, 7, 7}}, 448, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 71673280.},
    /* GFLOPS 0.140 x 1 = 0.140 */ {{1, 1}, {{1, 576, 38, 50}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 140204800.},
    /* GFLOPS 0.017 x 8 = 0.138 */ {{1, 1}, {{1, 16, 64, 128}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 17301504.},
    /* GFLOPS 0.044 x 3 = 0.133 */ {{1, 1}, {{1, 512, 13, 13}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 44345600.},
    /* GFLOPS 0.129 x 1 = 0.129 */ {{1, 1}, {{1, 160, 56, 56}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 128851968.},
    /* GFLOPS 0.118 x 1 = 0.118 */ {{1, 1}, {{1, 320, 38, 38}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 118477312.},
    /* GFLOPS 0.039 x 3 = 0.118 */ {{1, 1}, {{1, 1024, 10, 10}}, 192, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 39340800.},
    /* GFLOPS 0.017 x 7 = 0.118 */ {{1, 1}, {{1, 64, 64, 128}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 16908288.},
    /* GFLOPS 0.019 x 6 = 0.115 */ {{1, 1}, {{1, 32, 96, 96}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 19169280.},
    /* GFLOPS 0.114 x 1 = 0.114 */ {{1, 1}, {{1, 144, 128, 128}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 113639424.},
    /* GFLOPS 0.057 x 2 = 0.114 */ {{1, 1}, {{1, 240, 46, 46}}, 56, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 56996576.},
    /* GFLOPS 0.056 x 2 = 0.113 */ {{1, 1}, {{1, 448, 7, 7}}, 1280, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 56259840.},
    /* GFLOPS 0.112 x 1 = 0.112 */ {{1, 1}, {{1, 1024, 10, 10}}, 546, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 111875400.},
    /* GFLOPS 0.110 x 1 = 0.110 */ {{1, 1}, {{1, 480, 32, 32}}, 112, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 110215168.},
    /* GFLOPS 0.054 x 2 = 0.108 */ {{1, 1}, {{1, 16, 320, 320}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 54067200.},
    /* GFLOPS 0.107 x 1 = 0.107 */ {{1, 1}, {{1, 64, 32, 32}}, 810, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "VALID", true, 106997760.},
    /* GFLOPS 0.036 x 3 = 0.107 */ {{1, 1}, {{1, 192, 38, 38}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 35580160.},
    /* GFLOPS 0.027 x 4 = 0.106 */ {{1, 1}, {{1, 32, 160, 160}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 26624000.},
    /* GFLOPS 0.027 x 4 = 0.106 */ {{1, 1}, {{1, 24, 92, 92}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 26543104.},
    /* GFLOPS 0.026 x 4 = 0.106 */ {{1, 1}, {{1, 64, 80, 80}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 26419200.},
    /* GFLOPS 0.105 x 1 = 0.105 */ {{1, 1}, {{1, 256, 40, 40}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 105062400.},
    /* GFLOPS 0.105 x 1 = 0.105 */ {{1, 1}, {{1, 1024, 10, 10}}, 512, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 104908800.},
    /* GFLOPS 0.052 x 2 = 0.105 */ {{1, 1}, {{1, 256, 20, 20}}, 255, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 52326000.},
    /* GFLOPS 0.026 x 4 = 0.105 */ {{1, 1}, {{1, 64, 92, 92}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 26204544.},
    /* GFLOPS 0.052 x 2 = 0.104 */ {{1, 1}, {{1, 32, 112, 112}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 52183040.},
    /* GFLOPS 0.051 x 2 = 0.103 */ {{1, 1}, {{1, 512, 7, 7}}, 1024, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 51430400.},
    /* GFLOPS 0.101 x 1 = 0.101 */ {{1, 1}, {{1, 512, 19, 19}}, 273, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 101016825.},
    /* GFLOPS 0.008 x 12 = 0.101 */ {{1, 1}, {{1, 64, 32, 32}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "VALID", true, 8454144.},
    /* GFLOPS 0.050 x 2 = 0.100 */ {{1, 1}, {{1, 24, 92, 92}}, 120, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 49768320.},
    /* GFLOPS 0.095 x 1 = 0.095 */ {{1, 1}, {{1, 128, 38, 38}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 95003648.},
    /* GFLOPS 0.094 x 1 = 0.094 */ {{1, 1}, {{1, 32, 150, 150}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 93600000.},
    /* GFLOPS 0.093 x 1 = 0.093 */ {{1, 1}, {{1, 512, 38, 50}}, 48, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 93480000.},
    /* GFLOPS 0.093 x 1 = 0.093 */ {{1, 1}, {{1, 576, 19, 19}}, 224, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 93236192.},
    /* GFLOPS 0.093 x 1 = 0.093 */ {{1, 1}, {{1, 64, 75, 75}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 92880000.},
    /* GFLOPS 0.092 x 1 = 0.092 */ {{1, 1}, {{1, 192, 75, 100}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 92400000.},
    /* GFLOPS 0.031 x 3 = 0.092 */ {{1, 1}, {{1, 160, 10, 10}}, 960, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 30816000.},
    /* GFLOPS 0.044 x 2 = 0.088 */ {{1, 1}, {{1, 40, 184, 184}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 43877376.},
    /* GFLOPS 0.044 x 2 = 0.087 */ {{1, 1}, {{1, 272, 7, 7}}, 1632, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 43582560.},
    /* GFLOPS 0.042 x 2 = 0.084 */ {{1, 1}, {{1, 672, 14, 14}}, 160, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 42179200.},
    /* GFLOPS 0.082 x 1 = 0.082 */ {{1, 1}, {{1, 320, 10, 10}}, 1280, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 82048000.},
    /* GFLOPS 0.041 x 2 = 0.082 */ {{1, 1}, {{1, 40, 46, 46}}, 240, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 41135040.},
    /* GFLOPS 0.040 x 2 = 0.080 */ {{1, 1}, {{1, 24, 92, 92}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 39814656.},
    /* GFLOPS 0.013 x 6 = 0.080 */ {{1, 1}, {{1, 32, 80, 80}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 13312000.},
    /* GFLOPS 0.079 x 1 = 0.079 */ {{1, 1}, {{1, 240, 64, 64}}, 40, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 78807040.},
    /* GFLOPS 0.079 x 1 = 0.079 */ {{1, 1}, {{1, 384, 40, 40}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 78745600.},
    /* GFLOPS 0.040 x 2 = 0.079 */ {{1, 1}, {{1, 24, 75, 75}}, 144, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 39690000.},
    /* GFLOPS 0.077 x 1 = 0.077 */ {{1, 1}, {{1, 96, 56, 56}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 77471744.},
    /* GFLOPS 0.076 x 1 = 0.076 */ {{1, 1}, {{1, 96, 128, 128}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 75890688.},
    /* GFLOPS 0.038 x 2 = 0.076 */ {{1, 1}, {{1, 64, 48, 48}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 38043648.},
    /* GFLOPS 0.018 x 4 = 0.074 */ {{1, 1}, {{1, 8, 368, 368}}, 8, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 18417664.},
    /* GFLOPS 0.071 x 1 = 0.071 */ {{1, 1}, {{1, 16, 150, 150}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 71280000.},
    /* GFLOPS 0.071 x 1 = 0.071 */ {{1, 1}, {{1, 24, 150, 150}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "VALID", true, 70560000.},
    /* GFLOPS 0.068 x 1 = 0.068 */ {{1, 1}, {{1, 32, 256, 256}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 68157440.},
    /* GFLOPS 0.066 x 1 = 0.066 */ {{1, 1}, {{1, 672, 16, 16}}, 192, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 66109440.},
    /* GFLOPS 0.066 x 1 = 0.066 */ {{1, 1}, {{1, 1280, 10, 10}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 65561600.},
    /* GFLOPS 0.033 x 2 = 0.066 */ {{1, 1}, {{1, 128, 40, 40}}, 80, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 32896000.},
    /* GFLOPS 0.016 x 4 = 0.066 */ {{1, 1}, {{1, 40, 46, 46}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 16454016.},
    /* GFLOPS 0.016 x 4 = 0.065 */ {{1, 1}, {{1, 96, 46, 46}}, 40, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 16335520.},
    /* GFLOPS 0.061 x 1 = 0.061 */ {{1, 1}, {{1, 960, 10, 10}}, 320, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 61472000.},
    /* GFLOPS 0.061 x 1 = 0.061 */ {{1, 1}, {{1, 512, 46, 46}}, 28, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 60729200.},
    /* GFLOPS 0.031 x 2 = 0.061 */ {{1, 1}, {{1, 960, 10, 10}}, 160, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 30736000.},
    /* GFLOPS 0.059 x 1 = 0.059 */ {{1, 1}, {{1, 320, 38, 38}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 59238656.},
    /* GFLOPS 0.007 x 8 = 0.059 */ {{1, 1}, {{1, 112, 7, 7}}, 672, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 7408800.},
    /* GFLOPS 0.010 x 6 = 0.058 */ {{1, 1}, {{1, 56, 16, 16}}, 336, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 9719808.},
    /* GFLOPS 0.010 x 6 = 0.058 */ {{1, 1}, {{1, 64, 14, 14}}, 384, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 9709056.},
    /* GFLOPS 0.028 x 2 = 0.057 */ {{1, 1}, {{1, 336, 23, 23}}, 80, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 28481360.},
    /* GFLOPS 0.007 x 8 = 0.057 */ {{1, 1}, {{1, 96, 8, 8}}, 576, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 7114752.},
    /* GFLOPS 0.027 x 2 = 0.054 */ {{1, 1}, {{1, 16, 160, 160}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 27033600.},
    /* GFLOPS 0.018 x 3 = 0.054 */ {{1, 1}, {{1, 32, 38, 38}}, 192, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 18021120.},
    /* GFLOPS 0.014 x 4 = 0.054 */ {{1, 1}, {{1, 16, 160, 160}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 13516800.},
    /* GFLOPS 0.053 x 1 = 0.053 */ {{1, 1}, {{1, 528, 14, 14}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 53036032.},
    /* GFLOPS 0.053 x 1 = 0.053 */ {{1, 1}, {{1, 64, 40, 40}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 52838400.},
    /* GFLOPS 0.053 x 1 = 0.053 */ {{1, 1}, {{1, 128, 80, 80}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 52633600.},
    /* GFLOPS 0.053 x 1 = 0.053 */ {{1, 1}, {{1, 128, 20, 20}}, 512, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 52633600.},
    /* GFLOPS 0.053 x 1 = 0.053 */ {{1, 1}, {{1, 256, 10, 10}}, 1024, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 52531200.},
    /* GFLOPS 0.026 x 2 = 0.053 */ {{1, 1}, {{1, 16, 112, 112}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 26492928.},
    /* GFLOPS 0.013 x 4 = 0.053 */ {{1, 1}, {{1, 128, 20, 20}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 13158400.},
    /* GFLOPS 0.026 x 2 = 0.052 */ {{1, 1}, {{1, 1024, 10, 10}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 26227200.},
    /* GFLOPS 0.013 x 4 = 0.052 */ {{1, 1}, {{1, 16, 64, 64}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 12976128.},
    /* GFLOPS 0.051 x 1 = 0.051 */ {{1, 1}, {{1, 256, 56, 56}}, 128, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", false, 51480576.},
    /* GFLOPS 0.051 x 1 = 0.051 */ {{1, 1}, {{1, 512, 28, 28}}, 256, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", false, 51430400.},
    /* GFLOPS 0.051 x 1 = 0.051 */ {{1, 1}, {{1, 1024, 14, 14}}, 512, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", false, 51405312.},
    /* GFLOPS 0.026 x 2 = 0.051 */ {{1, 1}, {{1, 960, 7, 7}}, 272, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 25603088.},
    /* GFLOPS 0.047 x 1 = 0.047 */ {{1, 1}, {{1, 144, 64, 64}}, 40, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 47349760.},
    /* GFLOPS 0.047 x 1 = 0.047 */ {{1, 1}, {{1, 512, 38, 50}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 46740000.},
    /* GFLOPS 0.023 x 2 = 0.046 */ {{1, 1}, {{1, 56, 46, 46}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 22954368.},
    /* GFLOPS 0.045 x 1 = 0.045 */ {{1, 1}, {{1, 224, 28, 28}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 45058048.},
    /* GFLOPS 0.044 x 1 = 0.044 */ {{1, 1}, {{1, 512, 13, 13}}, 255, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 44172375.},
    /* GFLOPS 0.007 x 6 = 0.044 */ {{1, 1}, {{1, 672, 7, 7}}, 112, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 7381360.},
    /* GFLOPS 0.007 x 6 = 0.043 */ {{1, 1}, {{1, 576, 8, 8}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 7084032.},
    /* GFLOPS 0.020 x 2 = 0.041 */ {{1, 1}, {{1, 120, 46, 46}}, 40, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 20398240.},
    /* GFLOPS 0.010 x 4 = 0.040 */ {{1, 1}, {{1, 16, 56, 56}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 9934848.},
    /* GFLOPS 0.039 x 1 = 0.039 */ {{1, 1}, {{1, 240, 32, 32}}, 80, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 39403520.},
    /* GFLOPS 0.039 x 1 = 0.039 */ {{1, 1}, {{1, 144, 75, 75}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 39015000.},
    /* GFLOPS 0.039 x 1 = 0.039 */ {{1, 1}, {{1, 192, 28, 28}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 38635520.},
    /* GFLOPS 0.020 x 2 = 0.039 */ {{1, 1}, {{1, 32, 112, 112}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 19568640.},
    /* GFLOPS 0.010 x 4 = 0.039 */ {{1, 1}, {{1, 336, 16, 16}}, 56, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 9648128.},
    /* GFLOPS 0.019 x 2 = 0.038 */ {{1, 1}, {{1, 32, 48, 48}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 19169280.},
    /* GFLOPS 0.005 x 8 = 0.038 */ {{1, 1}, {{1, 256, 6, 6}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 4727808.},
    /* GFLOPS 0.036 x 1 = 0.036 */ {{1, 1}, {{1, 480, 14, 14}}, 192, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 36164352.},
    /* GFLOPS 0.018 x 2 = 0.036 */ {{1, 1}, {{1, 40, 46, 46}}, 104, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 17825184.},
    /* GFLOPS 0.009 x 4 = 0.036 */ {{1, 1}, {{1, 8, 256, 256}}, 8, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 8912896.},
    /* GFLOPS 0.035 x 1 = 0.035 */ {{1, 1}, {{1, 512, 46, 46}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 34702400.},
    /* GFLOPS 0.018 x 2 = 0.035 */ {{1, 1}, {{1, 104, 46, 46}}, 40, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 17689760.},
    /* GFLOPS 0.034 x 1 = 0.034 */ {{1, 1}, {{1, 128, 32, 64}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 33685504.},
    /* GFLOPS 0.017 x 2 = 0.034 */ {{1, 1}, {{1, 192, 28, 28}}, 56, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 16903040.},
    /* GFLOPS 0.033 x 1 = 0.033 */ {{1, 1}, {{1, 528, 14, 14}}, 160, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 33147520.},
    /* GFLOPS 0.033 x 1 = 0.033 */ {{1, 1}, {{1, 1024, 10, 10}}, 160, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 32784000.},
    /* GFLOPS 0.016 x 2 = 0.033 */ {{1, 1}, {{1, 40, 92, 92}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 16454016.},
    /* GFLOPS 0.005 x 6 = 0.033 */ {{1, 1}, {{1, 48, 14, 14}}, 288, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 5475456.},
    /* GFLOPS 0.032 x 1 = 0.032 */ {{1, 1}, {{1, 160, 28, 28}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 32212992.},
    /* GFLOPS 0.032 x 1 = 0.032 */ {{1, 1}, {{1, 512, 14, 14}}, 160, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 32144000.},
    /* GFLOPS 0.032 x 1 = 0.032 */ {{1, 1}, {{1, 508, 14, 14}}, 160, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 31893120.},
    /* GFLOPS 0.011 x 3 = 0.032 */ {{1, 1}, {{1, 320, 16, 16}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 10502144.},
    /* GFLOPS 0.031 x 1 = 0.031 */ {{1, 1}, {{1, 832, 7, 7}}, 384, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 31328640.},
    /* GFLOPS 0.015 x 2 = 0.030 */ {{1, 1}, {{1, 128, 46, 46}}, 28, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 15226736.},
    /* GFLOPS 0.015 x 2 = 0.030 */ {{1, 1}, {{1, 336, 14, 14}}, 112, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 14773696.},
    /* GFLOPS 0.005 x 6 = 0.030 */ {{1, 1}, {{1, 40, 16, 16}}, 240, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 4976640.},
    /* GFLOPS 0.029 x 1 = 0.029 */ {{1, 1}, {{1, 512, 14, 14}}, 144, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 28929600.},
    /* GFLOPS 0.015 x 2 = 0.029 */ {{1, 1}, {{1, 112, 32, 32}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 14745600.},
    /* GFLOPS 0.007 x 4 = 0.029 */ {{1, 1}, {{1, 24, 32, 32}}, 144, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 7225344.},
    /* GFLOPS 0.014 x 2 = 0.028 */ {{1, 1}, {{1, 576, 8, 8}}, 192, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 14168064.},
    /* GFLOPS 0.027 x 1 = 0.027 */ {{1, 1}, {{1, 384, 19, 19}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 26650464.},
    /* GFLOPS 0.027 x 1 = 0.027 */ {{1, 1}, {{1, 576, 19, 19}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 26638912.},
    /* GFLOPS 0.026 x 1 = 0.026 */ {{1, 1}, {{1, 96, 75, 75}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 26055000.},
    /* GFLOPS 0.026 x 1 = 0.026 */ {{1, 1}, {{1, 1024, 10, 10}}, 126, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 25817400.},
    /* GFLOPS 0.013 x 2 = 0.026 */ {{1, 1}, {{1, 512, 14, 14}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 12857600.},
    /* GFLOPS 0.009 x 3 = 0.026 */ {{1, 1}, {{1, 128, 46, 46}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 8700992.},
    /* GFLOPS 0.013 x 2 = 0.025 */ {{1, 1}, {{1, 96, 64, 64}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 12648448.},
    /* GFLOPS 0.024 x 1 = 0.024 */ {{1, 1}, {{1, 480, 14, 14}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 24109568.},
    /* GFLOPS 0.024 x 1 = 0.024 */ {{1, 1}, {{1, 128, 38, 38}}, 256, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", false, 23750912.},
    /* GFLOPS 0.023 x 1 = 0.023 */ {{1, 1}, {{1, 32, 150, 150}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 23400000.},
    /* GFLOPS 0.023 x 1 = 0.023 */ {{1, 1}, {{1, 512, 19, 19}}, 63, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 23311575.},
    /* GFLOPS 0.023 x 1 = 0.023 */ {{1, 1}, {{1, 448, 14, 14}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 22503936.},
    /* GFLOPS 0.023 x 1 = 0.023 */ {{1, 1}, {{1, 512, 14, 14}}, 112, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 22500800.},
    /* GFLOPS 0.022 x 1 = 0.022 */ {{1, 1}, {{1, 508, 14, 14}}, 112, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 22325184.},
    /* GFLOPS 0.006 x 4 = 0.022 */ {{1, 1}, {{1, 24, 28, 28}}, 144, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 5531904.},
    /* GFLOPS 0.005 x 4 = 0.022 */ {{1, 1}, {{1, 288, 14, 14}}, 48, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 5428416.},
    /* GFLOPS 0.021 x 1 = 0.021 */ {{1, 1}, {{1, 40, 64, 64}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 21233664.},
    /* GFLOPS 0.021 x 1 = 0.021 */ {{1, 1}, {{1, 416, 14, 14}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 20898304.},
    /* GFLOPS 0.021 x 1 = 0.021 */ {{1, 1}, {{1, 832, 7, 7}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 20885760.},
    /* GFLOPS 0.010 x 2 = 0.021 */ {{1, 1}, {{1, 32, 64, 64}}, 39, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 10383360.},
    /* GFLOPS 0.010 x 2 = 0.020 */ {{1, 1}, {{1, 24, 112, 112}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 9834496.},
    /* GFLOPS 0.005 x 4 = 0.020 */ {{1, 1}, {{1, 240, 16, 16}}, 40, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 4925440.},
    /* GFLOPS 0.019 x 1 = 0.019 */ {{1, 1}, {{1, 384, 14, 14}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 19292672.},
    /* GFLOPS 0.019 x 1 = 0.019 */ {{1, 1}, {{1, 64, 64, 64}}, 36, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "VALID", true, 19021824.},
    /* GFLOPS 0.010 x 2 = 0.019 */ {{1, 1}, {{1, 96, 56, 56}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 9683968.},
    /* GFLOPS 0.010 x 2 = 0.019 */ {{1, 1}, {{1, 32, 48, 48}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 9584640.},
    /* GFLOPS 0.010 x 2 = 0.019 */ {{1, 1}, {{1, 64, 48, 48}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 9510912.},
    /* GFLOPS 0.018 x 1 = 0.018 */ {{1, 1}, {{1, 576, 10, 10}}, 160, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 18448000.},
    /* GFLOPS 0.018 x 1 = 0.018 */ {{1, 1}, {{1, 480, 14, 14}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 18082176.},
    /* GFLOPS 0.018 x 1 = 0.018 */ {{1, 1}, {{1, 192, 38, 38}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 17790080.},
    /* GFLOPS 0.018 x 1 = 0.018 */ {{1, 1}, {{1, 352, 14, 14}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 17687040.},
    /* GFLOPS 0.009 x 2 = 0.018 */ {{1, 1}, {{1, 8, 128, 128}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 8912896.},
    /* GFLOPS 0.008 x 2 = 0.017 */ {{1, 1}, {{1, 64, 80, 80}}, 10, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 8256000.},
    /* GFLOPS 0.016 x 1 = 0.016 */ {{1, 1}, {{1, 832, 7, 7}}, 192, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 15664320.},
    /* GFLOPS 0.008 x 2 = 0.016 */ {{1, 1}, {{1, 128, 20, 20}}, 80, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 8224000.},
    /* GFLOPS 0.008 x 2 = 0.016 */ {{1, 1}, {{1, 256, 12, 12}}, 108, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 7978176.},
    /* GFLOPS 0.014 x 1 = 0.014 */ {{1, 1}, {{1, 288, 14, 14}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 14475776.},
    /* GFLOPS 0.014 x 1 = 0.014 */ {{1, 1}, {{1, 512, 5, 5}}, 546, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 13991250.},
    /* GFLOPS 0.007 x 2 = 0.014 */ {{1, 1}, {{1, 288, 14, 14}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 7237888.},
    /* GFLOPS 0.007 x 2 = 0.014 */ {{1, 1}, {{1, 144, 32, 32}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 7102464.},
    /* GFLOPS 0.007 x 2 = 0.014 */ {{1, 1}, {{1, 240, 16, 16}}, 56, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 6895616.},
    /* GFLOPS 0.013 x 1 = 0.013 */ {{1, 1}, {{1, 144, 38, 38}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 13354112.},
    /* GFLOPS 0.013 x 1 = 0.013 */ {{1, 1}, {{1, 832, 7, 7}}, 160, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 13053600.},
    /* GFLOPS 0.013 x 1 = 0.013 */ {{1, 1}, {{1, 508, 14, 14}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 12757248.},
    /* GFLOPS 0.007 x 2 = 0.013 */ {{1, 1}, {{1, 16, 56, 56}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 6623232.},
    /* GFLOPS 0.007 x 2 = 0.013 */ {{1, 1}, {{1, 128, 80, 80}}, 4, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 6579200.},
    /* GFLOPS 0.007 x 2 = 0.013 */ {{1, 1}, {{1, 32, 28, 28}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 6522880.},
    /* GFLOPS 0.006 x 2 = 0.013 */ {{1, 1}, {{1, 64, 14, 14}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 6472704.},
    /* GFLOPS 0.006 x 2 = 0.013 */ {{1, 1}, {{1, 24, 128, 128}}, 8, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 6422528.},
    /* GFLOPS 0.002 x 6 = 0.013 */ {{1, 1}, {{1, 8, 128, 128}}, 8, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 2228224.},
    /* GFLOPS 0.012 x 1 = 0.012 */ {{1, 1}, {{1, 992, 7, 7}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 12449920.},
    /* GFLOPS 0.012 x 1 = 0.012 */ {{1, 1}, {{1, 480, 14, 14}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 12054784.},
    /* GFLOPS 0.012 x 1 = 0.012 */ {{1, 1}, {{1, 960, 7, 7}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 12048512.},
    /* GFLOPS 0.012 x 1 = 0.012 */ {{1, 1}, {{1, 32, 75, 75}}, 128, 1, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", false, 12014080.},
    /* GFLOPS 0.012 x 1 = 0.012 */ {{1, 1}, {{1, 320, 12, 12}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 11814912.},
    /* GFLOPS 0.012 x 1 = 0.012 */ {{1, 1}, {{1, 640, 6, 6}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 11805696.},
    /* GFLOPS 0.012 x 1 = 0.012 */ {{1, 1}, {{1, 928, 7, 7}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 11647104.},
    /* GFLOPS 0.011 x 1 = 0.011 */ {{1, 1}, {{1, 896, 7, 7}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 11245696.},
    /* GFLOPS 0.011 x 1 = 0.011 */ {{1, 1}, {{1, 864, 7, 7}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 10844288.},
    /* GFLOPS 0.005 x 2 = 0.011 */ {{1, 1}, {{1, 144, 28, 28}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 5437824.},
    /* GFLOPS 0.005 x 2 = 0.011 */ {{1, 1}, {{1, 128, 24, 24}}, 36, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 5329152.},
    /* GFLOPS 0.010 x 1 = 0.010 */ {{1, 1}, {{1, 832, 7, 7}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 10442880.},
    /* GFLOPS 0.010 x 1 = 0.010 */ {{1, 1}, {{1, 800, 7, 7}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 10041472.},
    /* GFLOPS 0.010 x 1 = 0.010 */ {{1, 1}, {{1, 384, 14, 14}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 9646336.},
    /* GFLOPS 0.010 x 1 = 0.010 */ {{1, 1}, {{1, 768, 7, 7}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 9640064.},
    /* GFLOPS 0.009 x 1 = 0.009 */ {{1, 1}, {{1, 736, 7, 7}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 9238656.},
    /* GFLOPS 0.009 x 1 = 0.009 */ {{1, 1}, {{1, 704, 7, 7}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 8837248.},
    /* GFLOPS 0.005 x 2 = 0.009 */ {{1, 1}, {{1, 96, 32, 32}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 4743168.},
    /* GFLOPS 0.005 x 2 = 0.009 */ {{1, 1}, {{1, 4, 128, 256}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 4718592.},
    /* GFLOPS 0.004 x 2 = 0.009 */ {{1, 1}, {{1, 16, 64, 64}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 4325376.},
    /* GFLOPS 0.004 x 2 = 0.009 */ {{1, 1}, {{1, 32, 64, 64}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 4259840.},
    /* GFLOPS 0.008 x 1 = 0.008 */ {{1, 1}, {{1, 672, 7, 7}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 8435840.},
    /* GFLOPS 0.008 x 1 = 0.008 */ {{1, 1}, {{1, 128, 32, 64}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 8421376.},
    /* GFLOPS 0.008 x 1 = 0.008 */ {{1, 1}, {{1, 608, 7, 7}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 7633024.},
    /* GFLOPS 0.004 x 2 = 0.008 */ {{1, 1}, {{1, 384, 7, 7}}, 112, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 4220272.},
    /* GFLOPS 0.004 x 2 = 0.008 */ {{1, 1}, {{1, 336, 8, 8}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 4134912.},
    /* GFLOPS 0.007 x 1 = 0.007 */ {{1, 1}, {{1, 640, 6, 6}}, 160, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 7378560.},
    /* GFLOPS 0.007 x 1 = 0.007 */ {{1, 1}, {{1, 384, 14, 14}}, 48, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 7234752.},
    /* GFLOPS 0.007 x 1 = 0.007 */ {{1, 1}, {{1, 576, 7, 7}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 7231616.},
    /* GFLOPS 0.007 x 1 = 0.007 */ {{1, 1}, {{1, 256, 12, 12}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 7091712.},
    /* GFLOPS 0.007 x 1 = 0.007 */ {{1, 1}, {{1, 544, 7, 7}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 6830208.},
    /* GFLOPS 0.007 x 1 = 0.007 */ {{1, 1}, {{1, 528, 14, 14}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 6629504.},
    /* GFLOPS 0.007 x 1 = 0.007 */ {{1, 1}, {{1, 256, 5, 5}}, 512, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 6566400.},
    /* GFLOPS 0.004 x 2 = 0.007 */ {{1, 1}, {{1, 48, 14, 14}}, 192, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 3650304.},
    /* GFLOPS 0.003 x 2 = 0.007 */ {{1, 1}, {{1, 64, 80, 80}}, 4, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 3302400.},
    /* GFLOPS 0.006 x 1 = 0.006 */ {{1, 1}, {{1, 64, 56, 56}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 6472704.},
    /* GFLOPS 0.006 x 1 = 0.006 */ {{1, 1}, {{1, 512, 14, 14}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 6428800.},
    /* GFLOPS 0.003 x 2 = 0.006 */ {{1, 1}, {{1, 144, 16, 16}}, 40, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 2959360.},
    /* GFLOPS 0.005 x 1 = 0.005 */ {{1, 1}, {{1, 192, 12, 12}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 5322240.},
    /* GFLOPS 0.005 x 1 = 0.005 */ {{1, 1}, {{1, 1024, 10, 10}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 4917600.},
    /* GFLOPS 0.005 x 1 = 0.005 */ {{1, 1}, {{1, 256, 14, 14}}, 48, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 4826304.},
    /* GFLOPS 0.005 x 1 = 0.005 */ {{1, 1}, {{1, 508, 14, 14}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 4783968.},
    /* GFLOPS 0.005 x 1 = 0.005 */ {{1, 1}, {{1, 64, 32, 32}}, 36, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "VALID", true, 4755456.},
    /* GFLOPS 0.005 x 1 = 0.005 */ {{1, 1}, {{1, 1024, 3, 3}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 4720896.},
    /* GFLOPS 0.003 x 2 = 0.005 */ {{1, 1}, {{1, 144, 14, 14}}, 48, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 2718912.},
    /* GFLOPS 0.002 x 2 = 0.005 */ {{1, 1}, {{1, 576, 8, 8}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 2361344.},
    /* GFLOPS 0.004 x 1 = 0.004 */ {{1, 1}, {{1, 512, 19, 19}}, 12, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 4440300.},
    /* GFLOPS 0.004 x 1 = 0.004 */ {{1, 1}, {{1, 640, 6, 6}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 4427136.},
    /* GFLOPS 0.004 x 1 = 0.004 */ {{1, 1}, {{1, 16, 128, 256}}, 4, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 4325376.},
    /* GFLOPS 0.004 x 1 = 0.004 */ {{1, 1}, {{1, 64, 64, 128}}, 4, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 4227072.},
    /* GFLOPS 0.004 x 1 = 0.004 */ {{1, 1}, {{1, 832, 7, 7}}, 48, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 3916080.},
    /* GFLOPS 0.004 x 1 = 0.004 */ {{1, 1}, {{1, 192, 12, 12}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 3548160.},
    /* GFLOPS 0.002 x 2 = 0.004 */ {{1, 1}, {{1, 240, 48, 48}}, 2, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 2216448.},
    /* GFLOPS 0.002 x 2 = 0.004 */ {{1, 1}, {{1, 32, 64, 64}}, 8, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 2129920.},
    /* GFLOPS 0.002 x 2 = 0.004 */ {{1, 1}, {{1, 64, 40, 40}}, 10, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 2064000.},
    /* GFLOPS 0.001 x 6 = 0.004 */ {{1, 1}, {{1, 32, 24, 24}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 599040.},
    /* GFLOPS 0.003 x 1 = 0.003 */ {{1, 1}, {{1, 736, 3, 3}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 3393792.},
    /* GFLOPS 0.003 x 1 = 0.003 */ {{1, 1}, {{1, 512, 5, 5}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 3280000.},
    /* GFLOPS 0.003 x 1 = 0.003 */ {{1, 1}, {{1, 512, 5, 5}}, 126, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 3228750.},
    /* GFLOPS 0.003 x 1 = 0.003 */ {{1, 1}, {{1, 480, 14, 14}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 3013696.},
    /* GFLOPS 0.003 x 1 = 0.003 */ {{1, 1}, {{1, 320, 12, 12}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 2953728.},
    /* GFLOPS 0.003 x 1 = 0.003 */ {{1, 1}, {{1, 640, 6, 6}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 2951424.},
    /* GFLOPS 0.003 x 1 = 0.003 */ {{1, 1}, {{1, 832, 7, 7}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 2610720.},
    /* GFLOPS 0.002 x 2 = 0.003 */ {{1, 1}, {{1, 128, 80, 80}}, 1, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 1644800.},
    /* GFLOPS 0.002 x 2 = 0.003 */ {{1, 1}, {{1, 128, 40, 40}}, 4, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 1644800.},
    /* GFLOPS 0.002 x 2 = 0.003 */ {{1, 1}, {{1, 24, 32, 32}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 1605632.},
    /* GFLOPS 0.001 x 4 = 0.003 */ {{1, 1}, {{1, 64, 80, 80}}, 1, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 825600.},
    /* GFLOPS 0.002 x 1 = 0.002 */ {{1, 1}, {{1, 256, 12, 12}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 2363904.},
    /* GFLOPS 0.002 x 1 = 0.002 */ {{1, 1}, {{1, 528, 4, 4}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 2164736.},
    /* GFLOPS 0.002 x 1 = 0.002 */ {{1, 1}, {{1, 508, 4, 4}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 2082816.},
    /* GFLOPS 0.002 x 1 = 0.002 */ {{1, 1}, {{1, 1024, 1, 1}}, 1000, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 2049000.},
    /* GFLOPS 0.002 x 1 = 0.002 */ {{1, 1}, {{1, 64, 4, 4}}, 810, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "VALID", true, 1671840.},
    /* GFLOPS 0.002 x 1 = 0.002 */ {{1, 1}, {{1, 32, 80, 80}}, 4, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 1664000.},
    /* GFLOPS 0.001 x 2 = 0.002 */ {{1, 1}, {{1, 16, 4, 8400}}, 1, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", false, 1108800.},
    /* GFLOPS 0.001 x 2 = 0.002 */ {{1, 1}, {{1, 56, 16, 16}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 925696.},
    /* GFLOPS 0.001 x 2 = 0.002 */ {{1, 1}, {{1, 64, 40, 40}}, 4, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 825600.},
    /* GFLOPS 0.001 x 4 = 0.002 */ {{1, 1}, {{1, 64, 12, 12}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 594432.},
    /* GFLOPS 0.000 x 8 = 0.002 */ {{1, 1}, {{1, 192, 2, 2}}, 192, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 295680.},
    /* GFLOPS 0.001 x 1 = 0.001 */ {{1, 1}, {{1, 640, 6, 6}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 1475712.},
    /* GFLOPS 0.001 x 1 = 0.001 */ {{1, 1}, {{1, 256, 2, 2}}, 546, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 1120392.},
    /* GFLOPS 0.001 x 1 = 0.001 */ {{1, 1}, {{1, 192, 12, 12}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 887040.},
    /* GFLOPS 0.001 x 1 = 0.001 */ {{1, 1}, {{1, 640, 2, 2}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 655872.},
    /* GFLOPS 0.001 x 1 = 0.001 */ {{1, 1}, {{1, 512, 5, 5}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 615000.},
    /* GFLOPS 0.001 x 2 = 0.001 */ {{1, 1}, {{1, 256, 3, 3}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 590976.},
    /* GFLOPS 0.001 x 2 = 0.001 */ {{1, 1}, {{1, 64, 20, 20}}, 10, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 516000.},
    /* GFLOPS 0.000 x 2 = 0.001 */ {{1, 1}, {{1, 256, 12, 12}}, 6, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 443232.},
    /* GFLOPS 0.000 x 2 = 0.001 */ {{1, 1}, {{1, 32, 80, 80}}, 1, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 416000.},
    /* GFLOPS 0.000 x 2 = 0.001 */ {{1, 1}, {{1, 128, 40, 40}}, 1, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 411200.},
    /* GFLOPS 0.000 x 2 = 0.001 */ {{1, 1}, {{1, 128, 20, 20}}, 4, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 411200.},
    /* GFLOPS 0.000 x 4 = 0.001 */ {{1, 1}, {{1, 64, 12, 12}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 297216.},
    /* GFLOPS 0.000 x 2 = 0.001 */ {{1, 1}, {{1, 128, 6, 6}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 296064.},
    /* GFLOPS 0.000 x 2 = 0.001 */ {{1, 1}, {{1, 128, 24, 24}}, 2, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 296064.},
    /* GFLOPS 0.000 x 4 = 0.001 */ {{1, 1}, {{1, 64, 40, 40}}, 1, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 206400.},
    /* GFLOPS 0.000 x 9 = 0.001 */ {{1, 1}, {{1, 64, 4, 4}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "VALID", true, 132096.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{1, 1}, {{1, 192, 5, 5}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 308000.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{1, 1}, {{1, 128, 2, 2}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 263168.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{1, 1}, {{1, 256, 2, 2}}, 126, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 258552.},
    /* GFLOPS 0.000 x 2 = 0.000 */ {{1, 1}, {{1, 64, 20, 20}}, 4, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 206400.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{1, 1}, {{1, 1024, 1, 1}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 196704.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{1, 1}, {{1, 128, 3, 3}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 148032.},
    /* GFLOPS 0.000 x 2 = 0.000 */ {{1, 1}, {{1, 128, 6, 6}}, 16, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 148032.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{1, 1}, {{1, 736, 1, 1}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 141408.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{1, 1}, {{1, 128, 1, 1}}, 546, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 140322.},
    /* GFLOPS 0.000 x 2 = 0.000 */ {{1, 1}, {{1, 256, 2, 2}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 131328.},
    /* GFLOPS 0.000 x 4 = 0.000 */ {{1, 1}, {{1, 48, 1, 1}}, 1152, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 111744.},
    /* GFLOPS 0.000 x 4 = 0.000 */ {{1, 1}, {{1, 1152, 1, 1}}, 48, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 110640.},
    /* GFLOPS 0.000 x 2 = 0.000 */ {{1, 1}, {{1, 128, 20, 20}}, 1, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 102800.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{1, 1}, {{1, 64, 4, 4}}, 36, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "VALID", true, 74304.},
    /* GFLOPS 0.000 x 4 = 0.000 */ {{1, 1}, {{1, 64, 20, 20}}, 1, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 51600.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{1, 1}, {{1, 256, 2, 2}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 49248.},
    /* GFLOPS 0.000 x 3 = 0.000 */ {{1, 1}, {{1, 28, 1, 1}}, 672, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 38304.},
    /* GFLOPS 0.000 x 3 = 0.000 */ {{1, 1}, {{1, 672, 1, 1}}, 28, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 37660.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{1, 1}, {{1, 128, 1, 1}}, 126, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 32382.},
    /* GFLOPS 0.000 x 3 = 0.000 */ {{1, 1}, {{1, 20, 1, 1}}, 480, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 19680.},
    /* GFLOPS 0.000 x 3 = 0.000 */ {{1, 1}, {{1, 480, 1, 1}}, 20, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 19220.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{1, 1}, {{1, 64, 1, 1}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 16512.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{1, 1}, {{1, 128, 1, 1}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 6168.},
    /* GFLOPS 0.000 x 2 = 0.000 */ {{1, 1}, {{1, 10, 1, 1}}, 240, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 5040.},
    /* GFLOPS 0.000 x 2 = 0.000 */ {{1, 1}, {{1, 240, 1, 1}}, 10, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 4810.},
    /* GFLOPS 0.000 x 8 = 0.000 */ {{1, 1}, {{1, 24, 1, 1}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 4704.},
    /* GFLOPS 0.000 x 8 = 0.000 */ {{1, 1}, {{1, 96, 1, 1}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 4632.},
    /* GFLOPS 0.000 x 4 = 0.000 */ {{1, 1}, {{1, 4, 16, 16}}, 2, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 4608.},
    /* GFLOPS 0.000 x 2 = 0.000 */ {{1, 1}, {{1, 4, 16, 16}}, 1, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 2304.},
    /* GFLOPS 0.000 x 2 = 0.000 */ {{1, 1}, {{1, 6, 1, 1}}, 144, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 1872.},
    /* GFLOPS 0.000 x 2 = 0.000 */ {{1, 1}, {{1, 144, 1, 1}}, 6, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 1734.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{1, 1}, {{1, 4, 1, 1}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 864.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{1, 1}, {{1, 96, 1, 1}}, 4, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 772.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{1, 1}, {{1, 8, 1, 1}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 544.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{1, 1}, {{1, 32, 1, 1}}, 8, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 520.},
    /* GFLOPS 0.000 x 8 = 0.000 */ {{1, 1}, {{1, 6, 1, 1}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 312.},
    /* GFLOPS 0.000 x 8 = 0.000 */ {{1, 1}, {{1, 24, 1, 1}}, 6, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 294.},
};

static const ConvParam_t testConvolution_3x3S1D1_Configs[] = {
    /* GFLOPS 1.596 x 14 = 22.338 */ {{3, 3}, {{1, 128, 52, 52}}, 256, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 1595576320.},
    /* GFLOPS 1.595 x 12 = 19.141 */ {{3, 3}, {{1, 512, 13, 13}}, 1024, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 1595057152.},
    /* GFLOPS 6.814 x 2 = 13.629 */ {{3, 3}, {{1, 512, 38, 38}}, 512, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 6814386176.},
    /* GFLOPS 6.637 x 2 = 13.274 */ {{3, 3}, {{1, 256, 75, 75}}, 256, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 6636960000.},
    /* GFLOPS 10.701 x 1 = 10.701 */ {{3, 3}, {{1, 512, 38, 38}}, 804, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 10700715792.},
    /* GFLOPS 10.087 x 1 = 10.087 */ {{3, 3}, {{1, 576, 38, 50}}, 512, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 10086963200.},
    /* GFLOPS 9.993 x 1 = 9.993 */ {{3, 3}, {{1, 64, 368, 368}}, 64, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 9993207808.},
    /* GFLOPS 9.989 x 1 = 9.989 */ {{3, 3}, {{1, 128, 184, 184}}, 128, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 9988874240.},
    /* GFLOPS 4.247 x 2 = 8.494 */ {{3, 3}, {{1, 480, 32, 32}}, 480, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 4247224320.},
    /* GFLOPS 8.025 x 1 = 8.025 */ {{3, 3}, {{1, 1024, 19, 19}}, 1206, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 8025101478.},
    /* GFLOPS 6.641 x 1 = 6.641 */ {{3, 3}, {{1, 64, 300, 300}}, 64, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 6641280000.},
    /* GFLOPS 6.641 x 1 = 6.641 */ {{3, 3}, {{1, 64, 150, 200}}, 192, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 6641280000.},
    /* GFLOPS 6.638 x 1 = 6.638 */ {{3, 3}, {{1, 128, 150, 150}}, 128, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 6638400000.},
    /* GFLOPS 6.118 x 1 = 6.118 */ {{3, 3}, {{1, 144, 128, 128}}, 144, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 6117654528.},
    /* GFLOPS 6.116 x 1 = 6.116 */ {{3, 3}, {{1, 1152, 16, 16}}, 1152, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 6115590144.},
    /* GFLOPS 4.997 x 1 = 4.997 */ {{3, 3}, {{1, 64, 184, 184}}, 128, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 4996603904.},
    /* GFLOPS 4.993 x 1 = 4.993 */ {{3, 3}, {{1, 512, 46, 46}}, 256, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 4992812032.},
    /* GFLOPS 3.408 x 1 = 3.408 */ {{3, 3}, {{1, 256, 38, 38}}, 512, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 3407562752.},
    /* GFLOPS 0.302 x 11 = 3.325 */ {{3, 3}, {{1, 64, 64, 64}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 302252032.},
    /* GFLOPS 3.321 x 1 = 3.321 */ {{3, 3}, {{1, 64, 150, 150}}, 128, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 3320640000.},
    /* GFLOPS 0.830 x 4 = 3.321 */ {{3, 3}, {{1, 64, 75, 100}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 830160000.},
    /* GFLOPS 3.319 x 1 = 3.319 */ {{3, 3}, {{1, 128, 75, 75}}, 256, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 3319200000.},
    /* GFLOPS 1.598 x 2 = 3.195 */ {{3, 3}, {{1, 32, 208, 208}}, 64, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 1597652992.},
    /* GFLOPS 1.596 x 2 = 3.193 */ {{3, 3}, {{1, 64, 104, 104}}, 128, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 1596268544.},
    /* GFLOPS 1.405 x 2 = 2.810 */ {{3, 3}, {{1, 96, 184, 184}}, 24, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 1404888576.},
    /* GFLOPS 0.798 x 3 = 2.394 */ {{3, 3}, {{1, 64, 104, 104}}, 64, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 798134272.},
    /* GFLOPS 2.255 x 1 = 2.255 */ {{3, 3}, {{1, 128, 80, 100}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 2255285760.},
    /* GFLOPS 2.153 x 1 = 2.153 */ {{3, 3}, {{1, 128, 78, 98}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 2152611840.},
    /* GFLOPS 2.052 x 1 = 2.052 */ {{3, 3}, {{1, 128, 76, 96}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 2052298240.},
    /* GFLOPS 1.022 x 2 = 2.044 */ {{3, 3}, {{1, 576, 19, 19}}, 273, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 1021896057.},
    /* GFLOPS 1.954 x 1 = 1.954 */ {{3, 3}, {{1, 128, 74, 94}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 1954344960.},
    /* GFLOPS 1.888 x 1 = 1.888 */ {{3, 3}, {{1, 1024, 10, 10}}, 1024, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 1887539200.},
    /* GFLOPS 1.859 x 1 = 1.859 */ {{3, 3}, {{1, 128, 72, 92}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 1858752000.},
    /* GFLOPS 1.766 x 1 = 1.766 */ {{3, 3}, {{1, 128, 70, 90}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 1765519360.},
    /* GFLOPS 1.704 x 1 = 1.704 */ {{3, 3}, {{1, 256, 38, 38}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 1703781376.},
    /* GFLOPS 1.675 x 1 = 1.675 */ {{3, 3}, {{1, 128, 68, 88}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 1674647040.},
    /* GFLOPS 1.660 x 1 = 1.660 */ {{3, 3}, {{1, 128, 75, 75}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 1659600000.},
    /* GFLOPS 1.586 x 1 = 1.586 */ {{3, 3}, {{1, 128, 66, 86}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 1586135040.},
    /* GFLOPS 1.500 x 1 = 1.500 */ {{3, 3}, {{1, 128, 64, 84}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 1499983360.},
    /* GFLOPS 0.711 x 2 = 1.422 */ {{3, 3}, {{1, 12, 320, 320}}, 32, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 711065600.},
    /* GFLOPS 1.416 x 1 = 1.416 */ {{3, 3}, {{1, 128, 62, 82}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 1416192000.},
    /* GFLOPS 0.701 x 2 = 1.401 */ {{3, 3}, {{1, 128, 38, 50}}, 160, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 700720000.},
    /* GFLOPS 0.231 x 6 = 1.388 */ {{3, 3}, {{1, 128, 56, 56}}, 32, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 231311360.},
    /* GFLOPS 0.231 x 6 = 1.388 */ {{3, 3}, {{1, 256, 14, 14}}, 256, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 231261184.},
    /* GFLOPS 0.420 x 3 = 1.261 */ {{3, 3}, {{1, 96, 38, 50}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 420492800.},
    /* GFLOPS 1.258 x 1 = 1.258 */ {{3, 3}, {{1, 1280, 10, 10}}, 546, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 1258038600.},
    /* GFLOPS 1.248 x 1 = 1.248 */ {{3, 3}, {{1, 256, 46, 46}}, 128, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 1248338432.},
    /* GFLOPS 1.245 x 1 = 1.245 */ {{3, 3}, {{1, 64, 75, 75}}, 192, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 1245240000.},
    /* GFLOPS 1.210 x 1 = 1.210 */ {{3, 3}, {{1, 32, 256, 256}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 1210056704.},
    /* GFLOPS 1.196 x 1 = 1.196 */ {{3, 3}, {{1, 384, 26, 26}}, 256, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 1196336128.},
    /* GFLOPS 0.590 x 2 = 1.181 */ {{3, 3}, {{1, 64, 80, 80}}, 80, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 590336000.},
    /* GFLOPS 0.561 x 2 = 1.121 */ {{3, 3}, {{1, 128, 38, 50}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 560576000.},
    /* GFLOPS 1.112 x 1 = 1.112 */ {{3, 3}, {{1, 512, 10, 10}}, 1206, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 1111570200.},
    /* GFLOPS 0.076 x 14 = 1.058 */ {{3, 3}, {{1, 64, 32, 32}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 75563008.},
    /* GFLOPS 1.051 x 1 = 1.051 */ {{3, 3}, {{1, 160, 38, 50}}, 192, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 1050988800.},
    /* GFLOPS 1.006 x 1 = 1.006 */ {{3, 3}, {{1, 1024, 10, 10}}, 546, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 1006441800.},
    /* GFLOPS 0.473 x 2 = 0.945 */ {{3, 3}, {{1, 32, 160, 160}}, 32, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 472678400.},
    /* GFLOPS 0.472 x 2 = 0.944 */ {{3, 3}, {{1, 512, 4, 25}}, 512, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 471910400.},
    /* GFLOPS 0.841 x 1 = 0.841 */ {{3, 3}, {{1, 128, 38, 50}}, 192, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 840864000.},
    /* GFLOPS 0.415 x 2 = 0.831 */ {{3, 3}, {{1, 32, 150, 150}}, 32, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 415440000.},
    /* GFLOPS 0.118 x 6 = 0.710 */ {{3, 3}, {{1, 16, 160, 160}}, 16, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 118374400.},
    /* GFLOPS 0.351 x 2 = 0.702 */ {{3, 3}, {{1, 96, 92, 92}}, 24, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 351222144.},
    /* GFLOPS 0.694 x 1 = 0.694 */ {{3, 3}, {{1, 64, 56, 56}}, 192, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 694235136.},
    /* GFLOPS 0.231 x 3 = 0.694 */ {{3, 3}, {{1, 512, 7, 7}}, 512, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 231236096.},
    /* GFLOPS 0.160 x 4 = 0.639 */ {{3, 3}, {{1, 64, 38, 38}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 159833472.},
    /* GFLOPS 0.305 x 2 = 0.609 */ {{3, 3}, {{1, 3, 416, 416}}, 32, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 304578560.},
    /* GFLOPS 0.295 x 2 = 0.590 */ {{3, 3}, {{1, 128, 40, 40}}, 80, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 295040000.},
    /* GFLOPS 0.553 x 1 = 0.553 */ {{3, 3}, {{1, 64, 75, 100}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 553440000.},
    /* GFLOPS 0.477 x 1 = 0.477 */ {{3, 3}, {{1, 3, 368, 368}}, 64, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 476692480.},
    /* GFLOPS 0.236 x 2 = 0.472 */ {{3, 3}, {{1, 128, 40, 40}}, 64, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 236032000.},
    /* GFLOPS 0.236 x 2 = 0.472 */ {{3, 3}, {{1, 256, 8, 25}}, 256, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 235980800.},
    /* GFLOPS 0.236 x 2 = 0.472 */ {{3, 3}, {{1, 256, 4, 25}}, 512, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 235980800.},
    /* GFLOPS 0.449 x 1 = 0.449 */ {{3, 3}, {{1, 384, 13, 13}}, 384, 2, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 448626048.},
    /* GFLOPS 0.426 x 1 = 0.426 */ {{3, 3}, {{1, 128, 38, 38}}, 128, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 426037760.},
    /* GFLOPS 0.399 x 1 = 0.399 */ {{3, 3}, {{1, 256, 13, 13}}, 512, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 398807552.},
    /* GFLOPS 0.200 x 2 = 0.399 */ {{3, 3}, {{1, 32, 104, 104}}, 32, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 199706624.},
    /* GFLOPS 0.319 x 1 = 0.319 */ {{3, 3}, {{1, 192, 19, 19}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 319482112.},
    /* GFLOPS 0.317 x 1 = 0.317 */ {{3, 3}, {{1, 3, 300, 300}}, 64, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 316800000.},
    /* GFLOPS 0.299 x 1 = 0.299 */ {{3, 3}, {{1, 256, 13, 13}}, 384, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 299105664.},
    /* GFLOPS 0.299 x 1 = 0.299 */ {{3, 3}, {{1, 384, 13, 13}}, 256, 2, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 299084032.},
    /* GFLOPS 0.147 x 2 = 0.295 */ {{3, 3}, {{1, 256, 20, 20}}, 80, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 147488000.},
    /* GFLOPS 0.133 x 2 = 0.266 */ {{3, 3}, {{1, 128, 19, 19}}, 160, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 133136800.},
    /* GFLOPS 0.038 x 7 = 0.265 */ {{3, 3}, {{1, 16, 64, 128}}, 16, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 37879808.},
    /* GFLOPS 0.011 x 24 = 0.256 */ {{3, 3}, {{1, 16, 48, 48}}, 16, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "SAME", true, 10653696.},
    /* GFLOPS 0.011 x 24 = 0.255 */ {{3, 3}, {{1, 32, 24, 24}}, 32, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "SAME", true, 10635264.},
    /* GFLOPS 0.126 x 2 = 0.252 */ {{3, 3}, {{1, 512, 5, 5}}, 546, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 125812050.},
    /* GFLOPS 0.118 x 2 = 0.236 */ {{3, 3}, {{1, 64, 16, 50}}, 128, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 118067200.},
    /* GFLOPS 0.118 x 2 = 0.236 */ {{3, 3}, {{1, 128, 8, 25}}, 256, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 118016000.},
    /* GFLOPS 0.118 x 2 = 0.236 */ {{3, 3}, {{1, 256, 20, 20}}, 64, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 117990400.},
    /* GFLOPS 0.111 x 2 = 0.221 */ {{3, 3}, {{1, 192, 10, 10}}, 320, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 110624000.},
    /* GFLOPS 0.213 x 1 = 0.213 */ {{3, 3}, {{1, 256, 19, 19}}, 128, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 212972672.},
    /* GFLOPS 0.213 x 1 = 0.213 */ {{3, 3}, {{1, 512, 38, 38}}, 16, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 212949568.},
    /* GFLOPS 0.210 x 1 = 0.210 */ {{3, 3}, {{1, 64, 38, 50}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 210307200.},
    /* GFLOPS 0.104 x 2 = 0.208 */ {{3, 3}, {{1, 32, 75, 75}}, 32, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 103860000.},
    /* GFLOPS 0.200 x 1 = 0.200 */ {{3, 3}, {{1, 160, 19, 19}}, 192, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 199687872.},
    /* GFLOPS 0.038 x 5 = 0.189 */ {{3, 3}, {{1, 32, 32, 64}}, 32, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 37814272.},
    /* GFLOPS 0.090 x 2 = 0.181 */ {{3, 3}, {{1, 224, 10, 10}}, 224, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 90339200.},
    /* GFLOPS 0.088 x 2 = 0.176 */ {{3, 3}, {{1, 96, 46, 46}}, 24, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 87805536.},
    /* GFLOPS 0.160 x 1 = 0.160 */ {{3, 3}, {{1, 128, 19, 19}}, 192, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 159764160.},
    /* GFLOPS 0.146 x 1 = 0.146 */ {{3, 3}, {{1, 144, 14, 14}}, 288, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 146369664.},
    /* GFLOPS 0.139 x 1 = 0.139 */ {{3, 3}, {{1, 256, 5, 5}}, 1206, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 138961350.},
    /* GFLOPS 0.128 x 1 = 0.128 */ {{3, 3}, {{1, 64, 24, 24}}, 192, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 127512576.},
    /* GFLOPS 0.058 x 2 = 0.116 */ {{3, 3}, {{1, 16, 56, 56}}, 64, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 58003456.},
    /* GFLOPS 0.058 x 2 = 0.116 */ {{3, 3}, {{1, 32, 28, 28}}, 128, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 57903104.},
    /* GFLOPS 0.058 x 2 = 0.116 */ {{3, 3}, {{1, 64, 14, 14}}, 256, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 57852928.},
    /* GFLOPS 0.045 x 2 = 0.090 */ {{3, 3}, {{1, 576, 19, 19}}, 12, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 44918508.},
    /* GFLOPS 0.089 x 1 = 0.089 */ {{3, 3}, {{1, 112, 14, 14}}, 224, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 88554368.},
    /* GFLOPS 0.043 x 2 = 0.085 */ {{3, 3}, {{1, 32, 48, 48}}, 32, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "SAME", true, 42541056.},
    /* GFLOPS 0.011 x 8 = 0.085 */ {{3, 3}, {{1, 128, 6, 6}}, 128, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "SAME", true, 10621440.},
    /* GFLOPS 0.077 x 1 = 0.077 */ {{3, 3}, {{1, 192, 10, 10}}, 224, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 77436800.},
    /* GFLOPS 0.070 x 1 = 0.070 */ {{3, 3}, {{1, 96, 14, 14}}, 208, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 70487872.},
    /* GFLOPS 0.069 x 1 = 0.069 */ {{3, 3}, {{1, 96, 14, 14}}, 204, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 69132336.},
    /* GFLOPS 0.065 x 1 = 0.065 */ {{3, 3}, {{1, 192, 7, 7}}, 384, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 65046912.},
    /* GFLOPS 0.065 x 1 = 0.065 */ {{3, 3}, {{1, 160, 10, 10}}, 224, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 64534400.},
    /* GFLOPS 0.033 x 2 = 0.065 */ {{3, 3}, {{1, 48, 14, 14}}, 192, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 32551680.},
    /* GFLOPS 0.032 x 2 = 0.064 */ {{3, 3}, {{1, 96, 12, 12}}, 128, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 31868928.},
    /* GFLOPS 0.004 x 16 = 0.058 */ {{3, 3}, {{1, 128, 7, 7}}, 32, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 3614240.},
    /* GFLOPS 0.055 x 1 = 0.055 */ {{3, 3}, {{1, 1280, 10, 10}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 55298400.},
    /* GFLOPS 0.053 x 1 = 0.053 */ {{3, 3}, {{1, 128, 38, 38}}, 16, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 53254720.},
    /* GFLOPS 0.045 x 1 = 0.045 */ {{3, 3}, {{1, 160, 7, 7}}, 320, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 45174080.},
    /* GFLOPS 0.044 x 1 = 0.044 */ {{3, 3}, {{1, 1024, 10, 10}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 44239200.},
    /* GFLOPS 0.022 x 2 = 0.044 */ {{3, 3}, {{1, 3, 112, 112}}, 32, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 22077440.},
    /* GFLOPS 0.022 x 2 = 0.044 */ {{3, 3}, {{1, 96, 23, 23}}, 24, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 21951384.},
    /* GFLOPS 0.007 x 6 = 0.043 */ {{3, 3}, {{1, 48, 16, 16}}, 32, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 7086080.},
    /* GFLOPS 0.040 x 1 = 0.040 */ {{3, 3}, {{1, 64, 19, 19}}, 96, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 39958368.},
    /* GFLOPS 0.027 x 1 = 0.027 */ {{3, 3}, {{1, 128, 38, 38}}, 8, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 26627360.},
    /* GFLOPS 0.010 x 2 = 0.020 */ {{3, 3}, {{1, 256, 2, 2}}, 546, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 10066056.},
    /* GFLOPS 0.010 x 2 = 0.019 */ {{3, 3}, {{1, 8, 256, 256}}, 1, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 9502720.},
    /* GFLOPS 0.002 x 6 = 0.014 */ {{3, 3}, {{1, 32, 16, 16}}, 16, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 2363392.},
    /* GFLOPS 0.001 x 11 = 0.013 */ {{3, 3}, {{1, 64, 4, 4}}, 64, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", false, 1180672.},
    /* GFLOPS 0.012 x 1 = 0.012 */ {{3, 3}, {{1, 96, 6, 6}}, 192, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 11950848.},
    /* GFLOPS 0.006 x 2 = 0.012 */ {{3, 3}, {{1, 96, 3, 3}}, 384, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 5975424.},
    /* GFLOPS 0.006 x 2 = 0.011 */ {{3, 3}, {{1, 512, 5, 5}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 5530200.},
    /* GFLOPS 0.010 x 1 = 0.010 */ {{3, 3}, {{1, 4, 128, 256}}, 4, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 9568256.},
    /* GFLOPS 0.006 x 1 = 0.006 */ {{3, 3}, {{1, 256, 10, 10}}, 12, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 5530800.},
    /* GFLOPS 0.004 x 1 = 0.004 */ {{3, 3}, {{1, 256, 1, 1}}, 804, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 3705636.},
    /* GFLOPS 0.001 x 6 = 0.004 */ {{3, 3}, {{1, 16, 16, 16}}, 8, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 591872.},
    /* GFLOPS 0.001 x 2 = 0.003 */ {{3, 3}, {{1, 128, 1, 1}}, 546, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 1258530.},
    /* GFLOPS 0.001 x 1 = 0.001 */ {{3, 3}, {{1, 128, 5, 5}}, 12, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 691500.},
    /* GFLOPS 0.001 x 1 = 0.001 */ {{3, 3}, {{1, 128, 3, 3}}, 256, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 590080.},
    /* GFLOPS 0.000 x 2 = 0.001 */ {{3, 3}, {{1, 256, 2, 2}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 442464.},
    /* GFLOPS 0.000 x 6 = 0.001 */ {{3, 3}, {{1, 8, 16, 16}}, 4, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 148480.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{3, 3}, {{1, 64, 3, 3}}, 128, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "", true, 147584.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{3, 3}, {{1, 256, 1, 1}}, 16, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 73744.},
    /* GFLOPS 0.000 x 2 = 0.000 */ {{3, 3}, {{1, 128, 1, 1}}, 24, 1, {1, 1}, {1, 1}, {0, 0}, {0, 0}, "SAME", true, 55320.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{3, 3}, {{1, 128, 1, 1}}, 16, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 36880.},
    /* GFLOPS 0.000 x 1 = 0.000 */ {{3, 3}, {{1, 128, 1, 1}}, 8, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 18440.},
};

static const ConvParam_t testConvolution_Depthwise_Configs[] = {
    /* GFLOPS 6.525 x 14 = 91.357 */ {{5, 5}, {{1, 1632, 7, 7}}, 1632, 1632, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 6525468768.},
    /* GFLOPS 6.094 x 4 = 24.377 */ {{5, 5}, {{1, 480, 23, 23}}, 480, 480, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 6094333920.},
    /* GFLOPS 0.925 x 10 = 9.249 */ {{3, 3}, {{1, 512, 14, 14}}, 512, 512, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 924944384.},
    /* GFLOPS 4.301 x 2 = 8.601 */ {{3, 3}, {{1, 336, 46, 46}}, 336, 336, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 4300693824.},
    /* GFLOPS 1.734 x 4 = 6.936 */ {{5, 5}, {{1, 64, 92, 92}}, 64, 64, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 1733968896.},
    /* GFLOPS 1.106 x 6 = 6.638 */ {{5, 5}, {{1, 672, 7, 7}}, 672, 672, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 1106413728.},
    /* GFLOPS 1.062 x 6 = 6.370 */ {{5, 5}, {{1, 576, 8, 8}}, 576, 576, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 1061720064.},
    /* GFLOPS 2.986 x 2 = 5.973 */ {{5, 5}, {{1, 336, 46, 46}}, 336, 336, {2, 2}, {1, 1}, {2, 2}, {0, 0}, "", true, 2986276944.},
    /* GFLOPS 1.445 x 4 = 5.781 */ {{5, 5}, {{1, 336, 16, 16}}, 336, 336, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 1445154816.},
    /* GFLOPS 0.472 x 10 = 4.719 */ {{5, 5}, {{1, 128, 24, 24}}, 128, 128, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 471932928.},
    /* GFLOPS 2.194 x 2 = 4.389 */ {{3, 3}, {{1, 240, 46, 46}}, 240, 240, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 2194376640.},
    /* GFLOPS 1.889 x 2 = 3.778 */ {{3, 3}, {{1, 64, 160, 160}}, 64, 64, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 1889075200.},
    /* GFLOPS 1.659 x 2 = 3.318 */ {{5, 5}, {{1, 960, 14, 14}}, 960, 960, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 1658914560.},
    /* GFLOPS 0.472 x 6 = 2.834 */ {{3, 3}, {{1, 64, 80, 80}}, 64, 64, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 472268800.},
    /* GFLOPS 0.472 x 6 = 2.832 */ {{5, 5}, {{1, 64, 48, 48}}, 64, 64, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 472006656.},
    /* GFLOPS 1.344 x 2 = 2.688 */ {{5, 5}, {{1, 192, 56, 56}}, 192, 192, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 1343832768.},
    /* GFLOPS 0.382 x 6 = 2.293 */ {{3, 3}, {{1, 576, 8, 8}}, 576, 576, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 382242816.},
    /* GFLOPS 1.130 x 2 = 2.259 */ {{3, 3}, {{1, 144, 112, 112}}, 144, 144, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", true, 1129510800.},
    /* GFLOPS 1.062 x 2 = 2.124 */ {{5, 5}, {{1, 144, 32, 32}}, 144, 144, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 1061830656.},
    /* GFLOPS 0.976 x 2 = 1.953 */ {{3, 3}, {{1, 40, 184, 184}}, 40, 40, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 976407040.},
    /* GFLOPS 0.473 x 4 = 1.891 */ {{3, 3}, {{1, 32, 160, 160}}, 32, 32, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 472678400.},
    /* GFLOPS 0.925 x 2 = 1.850 */ {{3, 3}, {{1, 128, 56, 56}}, 128, 128, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 925245440.},
    /* GFLOPS 0.925 x 2 = 1.850 */ {{3, 3}, {{1, 256, 28, 28}}, 256, 256, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 925044736.},
    /* GFLOPS 0.925 x 2 = 1.850 */ {{3, 3}, {{1, 1024, 7, 7}}, 1024, 1024, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", false, 924894208.},
    /* GFLOPS 1.704 x 1 = 1.704 */ {{3, 3}, {{1, 256, 38, 38}}, 256, 256, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 1703781376.},
    /* GFLOPS 1.660 x 1 = 1.660 */ {{3, 3}, {{1, 128, 75, 75}}, 128, 128, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 1659600000.},
    /* GFLOPS 0.813 x 2 = 1.626 */ {{5, 5}, {{1, 144, 28, 28}}, 144, 144, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 812964096.},
    /* GFLOPS 0.813 x 2 = 1.626 */ {{5, 5}, {{1, 288, 14, 14}}, 288, 288, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 812907648.},
    /* GFLOPS 0.737 x 2 = 1.475 */ {{5, 5}, {{1, 240, 16, 16}}, 240, 240, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 737341440.},
    /* GFLOPS 0.351 x 4 = 1.405 */ {{3, 3}, {{1, 96, 46, 46}}, 96, 96, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 351222144.},
    /* GFLOPS 0.680 x 2 = 1.360 */ {{3, 3}, {{1, 96, 64, 64}}, 96, 96, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 679870464.},
    /* GFLOPS 0.677 x 2 = 1.355 */ {{5, 5}, {{1, 40, 184, 184}}, 40, 40, {2, 2}, {1, 1}, {2, 2}, {0, 0}, "", true, 677458560.},
    /* GFLOPS 0.625 x 2 = 1.250 */ {{3, 3}, {{1, 32, 368, 368}}, 32, 32, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 625117184.},
    /* GFLOPS 0.293 x 4 = 1.171 */ {{3, 3}, {{1, 288, 14, 14}}, 288, 288, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 292682880.},
    /* GFLOPS 0.549 x 2 = 1.097 */ {{3, 3}, {{1, 120, 92, 92}}, 120, 120, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 548721120.},
    /* GFLOPS 0.265 x 4 = 1.062 */ {{3, 3}, {{1, 240, 16, 16}}, 240, 240, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 265482240.},
    /* GFLOPS 0.473 x 2 = 0.947 */ {{3, 3}, {{1, 16, 320, 320}}, 16, 16, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 473497600.},
    /* GFLOPS 0.472 x 2 = 0.944 */ {{5, 5}, {{1, 96, 64, 64}}, 96, 96, {2, 2}, {1, 1}, {2, 2}, {0, 0}, "", true, 471957504.},
    /* GFLOPS 0.398 x 2 = 0.797 */ {{3, 3}, {{1, 672, 7, 7}}, 672, 672, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 398330016.},
    /* GFLOPS 0.361 x 2 = 0.723 */ {{5, 5}, {{1, 336, 16, 16}}, 336, 336, {2, 2}, {1, 1}, {2, 2}, {0, 0}, "", true, 361288704.},
    /* GFLOPS 0.118 x 6 = 0.708 */ {{3, 3}, {{1, 64, 40, 40}}, 64, 64, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 118067200.},
    /* GFLOPS 0.118 x 6 = 0.708 */ {{5, 5}, {{1, 256, 6, 6}}, 256, 256, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 117974016.},
    /* GFLOPS 0.336 x 2 = 0.672 */ {{5, 5}, {{1, 96, 56, 56}}, 96, 96, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 335993184.},
    /* GFLOPS 0.265 x 2 = 0.531 */ {{5, 5}, {{1, 384, 14, 14}}, 384, 384, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 265434624.},
    /* GFLOPS 0.472 x 1 = 0.472 */ {{5, 5}, {{1, 32, 96, 96}}, 32, 32, {1, 1}, {1, 1}, {2, 2}, {0, 0}, "", true, 472154112.},
    /* GFLOPS 0.232 x 2 = 0.463 */ {{3, 3}, {{1, 32, 112, 112}}, 32, 32, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 231612416.},
    /* GFLOPS 0.231 x 2 = 0.463 */ {{3, 3}, {{1, 64, 112, 112}}, 64, 64, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", false, 231411712.},
    /* GFLOPS 0.231 x 2 = 0.463 */ {{3, 3}, {{1, 128, 56, 56}}, 128, 128, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", false, 231311360.},
    /* GFLOPS 0.231 x 2 = 0.463 */ {{3, 3}, {{1, 256, 28, 28}}, 256, 256, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", false, 231261184.},
    /* GFLOPS 0.231 x 2 = 0.462 */ {{3, 3}, {{1, 512, 14, 14}}, 512, 512, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", false, 231236096.},
    /* GFLOPS 0.426 x 1 = 0.426 */ {{3, 3}, {{1, 128, 75, 75}}, 128, 128, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 426037760.},
    /* GFLOPS 0.426 x 1 = 0.426 */ {{3, 3}, {{1, 256, 38, 38}}, 256, 256, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 425945344.},
    /* GFLOPS 0.415 x 1 = 0.415 */ {{3, 3}, {{1, 32, 150, 150}}, 32, 32, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 415440000.},
    /* GFLOPS 0.415 x 1 = 0.415 */ {{3, 3}, {{1, 64, 150, 150}}, 64, 64, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 415080000.},
    /* GFLOPS 0.170 x 2 = 0.341 */ {{3, 3}, {{1, 24, 128, 128}}, 24, 24, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 170262528.},
    /* GFLOPS 0.157 x 2 = 0.314 */ {{3, 3}, {{1, 8, 368, 368}}, 8, 8, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 157091840.},
    /* GFLOPS 0.076 x 4 = 0.304 */ {{3, 3}, {{1, 8, 256, 256}}, 8, 8, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 76021760.},
    /* GFLOPS 0.130 x 2 = 0.261 */ {{3, 3}, {{1, 24, 112, 112}}, 24, 24, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 130357248.},
    /* GFLOPS 0.118 x 2 = 0.237 */ {{3, 3}, {{1, 16, 160, 160}}, 16, 16, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 118374400.},
    /* GFLOPS 0.113 x 2 = 0.226 */ {{5, 5}, {{1, 32, 96, 96}}, 32, 32, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 113171488.},
    /* GFLOPS 0.108 x 2 = 0.217 */ {{5, 5}, {{1, 64, 48, 48}}, 64, 64, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 108373056.},
    /* GFLOPS 0.099 x 2 = 0.198 */ {{5, 5}, {{1, 128, 24, 24}}, 128, 128, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 99138688.},
    /* GFLOPS 0.096 x 2 = 0.191 */ {{3, 3}, {{1, 144, 32, 32}}, 144, 144, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 95588352.},
    /* GFLOPS 0.030 x 6 = 0.177 */ {{3, 3}, {{1, 64, 20, 20}}, 64, 64, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 29516800.},
    /* GFLOPS 0.082 x 2 = 0.164 */ {{5, 5}, {{1, 256, 12, 12}}, 256, 256, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 81926400.},
    /* GFLOPS 0.076 x 2 = 0.151 */ {{3, 3}, {{1, 32, 64, 64}}, 32, 32, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 75628544.},
    /* GFLOPS 0.076 x 2 = 0.151 */ {{3, 3}, {{1, 32, 128, 128}}, 32, 32, {2, 2}, {1, 1}, {1, 1}, {0, 0}, "", true, 75628544.},
    /* GFLOPS 0.063 x 2 = 0.126 */ {{3, 3}, {{1, 144, 28, 28}}, 144, 144, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", true, 63103248.},
    /* GFLOPS 0.019 x 6 = 0.114 */ {{3, 3}, {{1, 8, 128, 128}}, 8, 8, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 19005440.},
    /* GFLOPS 0.019 x 2 = 0.038 */ {{3, 3}, {{1, 16, 64, 64}}, 16, 16, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 18939904.},
    /* GFLOPS 0.014 x 2 = 0.029 */ {{3, 3}, {{1, 56, 16, 16}}, 56, 56, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 14465024.},
    /* GFLOPS 0.012 x 2 = 0.023 */ {{3, 3}, {{1, 10, 80, 80}}, 10, 10, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 11584000.},
    /* GFLOPS 0.011 x 2 = 0.021 */ {{3, 3}, {{1, 24, 32, 32}}, 24, 24, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 10641408.},
    /* GFLOPS 0.003 x 6 = 0.016 */ {{3, 3}, {{1, 192, 2, 2}}, 192, 192, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 2654976.},
    /* GFLOPS 0.004 x 2 = 0.008 */ {{3, 3}, {{1, 1, 32, 100}}, 64, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 3891200.},
    /* GFLOPS 0.003 x 2 = 0.006 */ {{3, 3}, {{1, 10, 40, 40}}, 10, 10, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 2896000.},
    /* GFLOPS 0.002 x 2 = 0.004 */ {{3, 3}, {{1, 4, 80, 80}}, 4, 4, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 1868800.},
    /* GFLOPS 0.001 x 2 = 0.001 */ {{3, 3}, {{1, 10, 20, 20}}, 10, 10, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 724000.},
    /* GFLOPS 0.000 x 2 = 0.001 */ {{3, 3}, {{1, 192, 4, 4}}, 192, 192, {2, 2}, {1, 1}, {0, 0}, {0, 0}, "", true, 663744.},
    /* GFLOPS 0.000 x 2 = 0.001 */ {{3, 3}, {{1, 4, 40, 40}}, 4, 4, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 467200.},
    /* GFLOPS 0.000 x 4 = 0.000 */ {{3, 3}, {{1, 1, 80, 80}}, 1, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 121600.},
    /* GFLOPS 0.000 x 2 = 0.000 */ {{3, 3}, {{1, 4, 20, 20}}, 4, 4, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 116800.},
    /* GFLOPS 0.000 x 4 = 0.000 */ {{3, 3}, {{1, 1, 40, 40}}, 1, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 30400.},
    /* GFLOPS 0.000 x 4 = 0.000 */ {{3, 3}, {{1, 1, 20, 20}}, 1, 1, {1, 1}, {1, 1}, {1, 1}, {0, 0}, "", true, 7600.},
};

struct ConvParamGenerator
{
    ConvParamGenerator(const ConvParam_t* testConfigs, const int size): testConfigs(testConfigs), size(size)
    {}

    const ConvParam_t* testConfigs;
    const int size;

    ::testing::internal::ParamGenerator<ConvParam_t> all() const
    {
        int NUM = size;
        static size_t DNN_LIMIT_CONV = utils::getConfigurationParameterSizeT("OPENCV_TEST_DNN_LIMIT_CONV", 0);
        if (DNN_LIMIT_CONV > 0)
            NUM = std::min(NUM, (int)DNN_LIMIT_CONV);

        std::vector<ConvParam_t> v_(NUM);
        for (int i = 0; i < NUM; ++i) { v_[i] = testConfigs[i]; } // reduce generated code size
        return ::testing::ValuesIn(v_);
    }
};
static inline void PrintTo(const ConvParam_t& p, std::ostream* os)
{
    *os << "GFLOPS=" << cv::format("%.3f", p.declared_flops * 1e-9)
        << ", K=" << (Size)p.kernel
        << ", IN={" << p.shapeIn.dims[0] << ", " << p.shapeIn.dims[1] << ", " << p.shapeIn.dims[2] << ", " << p.shapeIn.dims[3] << "}"
        << ", OCN=" << p.outCN;
    if (p.groups > 1)
       *os << ", G=" << p.groups;
    if (((Size)p.stride).area() != 1)
        *os << ", S=" << ((Size)p.stride);
    if (((Size)p.dilation).area() != 1)
        *os << ", D=" << ((Size)p.dilation);
    if (!((Size)p.pad).empty())
        *os << ", P=" << ((Size)p.pad);
    if (!((Size)p.padAdjust).empty())
        *os << ", PAdj=" << ((Size)p.padAdjust);
    if (!((std::string)p.padMode).empty())
        *os << ", PM=" << ((std::string)p.padMode);
    if (p.hasBias)
        *os << ", BIAS";
}

static
Net build_net(
    const ConvParam_t& params, Backend backendId, Target targetId,
    const std::function<void(Net&)>& configure_network_cb = std::function<void(Net&)>(),
    double flops_limit_debug_long = 2e9, double flops_limit_debug_verylong = 6e9
)
{
    double declared_flops = params.declared_flops;

    if (flops_limit_debug_verylong > 0 && declared_flops >= flops_limit_debug_verylong)
        applyTestTag(CV_TEST_TAG_DEBUG_VERYLONG);
    if (flops_limit_debug_long > 0 && declared_flops >= flops_limit_debug_long)
        applyTestTag(CV_TEST_TAG_DEBUG_LONG);

    Size kernel = params.kernel;
    MatShape inputShape = MatShape(params.shapeIn.dims, params.shapeIn.dims + 4);
    int outChannels = params.outCN;
    int groups = params.groups;
    Size stride = params.stride;
    Size dilation = params.dilation;
    Size pad = params.pad;
    Size padAdjust = params.padAdjust;
    std::string padMode(params.padMode);
    bool hasBias = params.hasBias;

    int inChannels = inputShape[1];
    Size inSize(inputShape[3], inputShape[2]);

    int sz[] = {outChannels, inChannels / groups, kernel.height, kernel.width};
    Mat weights(4, &sz[0], CV_32F);
    randu(weights, -1.0f, 1.0f);

    LayerParams lp;
    lp.set("kernel_w", kernel.width);
    lp.set("kernel_h", kernel.height);
    lp.set("pad_w", pad.width);
    lp.set("pad_h", pad.height);
    if (padAdjust.width > 0 || padAdjust.height > 0)
    {
        lp.set("adj_w", padAdjust.width);
        lp.set("adj_h", padAdjust.height);
    }
    if (!padMode.empty())
        lp.set("pad_mode", padMode);
    lp.set("stride_w", stride.width);
    lp.set("stride_h", stride.height);
    lp.set("dilation_w", dilation.width);
    lp.set("dilation_h", dilation.height);
    lp.set("num_output", outChannels);
    lp.set("group", groups);
    lp.set("bias_term", hasBias);
    lp.type = "Convolution";
    lp.name = "testLayer";
    lp.blobs.push_back(weights);
    if (hasBias)
    {
        Mat bias(1, outChannels, CV_32F);
        randu(bias, -1.0f, 1.0f);
        lp.blobs.push_back(bias);
    }
    int inpSz[] = {1, inChannels, inSize.height, inSize.width};
    Mat input(4, &inpSz[0], CV_32F);
    randu(input, -1.0f, 1.0f);

    Net net;
    net.addLayerToPrev(lp.name, lp.type, lp);

    net.setPreferableBackend(backendId);
    net.setPreferableTarget(targetId);
    if (configure_network_cb)
    {
        configure_network_cb(net);
    }

    net.setInput(input);

    // warmup
    Mat output = net.forward();

    MatShape netInputShape = shape(input);
    size_t weightsMemory = 0, blobsMemory = 0;
    net.getMemoryConsumption(netInputShape, weightsMemory, blobsMemory);
    int64 flops = net.getFLOPS(netInputShape);
    CV_Assert(flops > 0);

    std::cout
        << "IN=" << divUp(input.total() * input.elemSize(), 1u<<10) << " Kb " << netInputShape
        << "    OUT=" << divUp(output.total() * output.elemSize(), 1u<<10) << " Kb " << shape(output)
        << "    Weights(parameters): " << divUp(weightsMemory, 1u<<10) << " Kb"
        << "    MFLOPS=" << flops * 1e-6 << std::endl;

    EXPECT_NEAR(flops, declared_flops, declared_flops * 1e-6);

    return net;
}

typedef tuple<ConvParam_t, tuple<Backend, Target> > ConvTestParam_t;
typedef tuple<ConvParam_t, tuple<Backend, Target>, bool> Conv3x3S1D1TestParam_t;
typedef TestBaseWithParam<ConvTestParam_t> Conv;
typedef TestBaseWithParam<ConvTestParam_t> Conv_1x1;
typedef TestBaseWithParam<Conv3x3S1D1TestParam_t> Conv_3x3S1D1;
typedef TestBaseWithParam<ConvTestParam_t> Conv_Depthwise;

PERF_TEST_P_(Conv, conv)
{
    const ConvParam_t& params = get<0>(GetParam());
    Backend backendId = get<0>(get<1>(GetParam()));
    Target targetId = get<1>(get<1>(GetParam()));
    Net net = build_net(params, backendId, targetId);

    TEST_CYCLE()
    {
        Mat res = net.forward();
    }
    SANITY_CHECK_NOTHING();
}

PERF_TEST_P_(Conv_1x1, conv)
{
    const ConvParam_t& params = get<0>(GetParam());
    Backend backendId = get<0>(get<1>(GetParam()));
    Target targetId = get<1>(get<1>(GetParam()));
    Net net = build_net(params, backendId, targetId);

    TEST_CYCLE()
    {
        Mat res = net.forward();
    }
    SANITY_CHECK_NOTHING();
}

PERF_TEST_P_(Conv_3x3S1D1, conv)
{
    const ConvParam_t& params = get<0>(GetParam());
    Backend backendId = get<0>(get<1>(GetParam()));
    Target targetId = get<1>(get<1>(GetParam()));
    bool winograd = get<2>(GetParam());
    Net net = build_net(params, backendId, targetId,
        [=](Net& net)
        {
            net.enableWinograd(winograd);
        }
    );

    TEST_CYCLE()
    {
        Mat res = net.forward();
    }
    SANITY_CHECK_NOTHING();
}

PERF_TEST_P_(Conv_Depthwise, conv)
{
    const ConvParam_t& params = get<0>(GetParam());
    Backend backendId = get<0>(get<1>(GetParam()));
    Target targetId = get<1>(get<1>(GetParam()));
    Net net = build_net(params, backendId, targetId, std::function<void(Net&)>(),
        0/*flops_limit_debug_long*/, 0/*flops_limit_debug_verylong*/);

    TEST_CYCLE()
    {
        Mat res = net.forward();
    }
    SANITY_CHECK_NOTHING();
}

ConvParamGenerator conv_params(testConvolution_Configs, sizeof(testConvolution_Configs) / sizeof(testConvolution_Configs[0]));
INSTANTIATE_TEST_CASE_P(/**/, Conv, Combine(
    conv_params.all(),
    dnnBackendsAndTargets(false, false)  // defined in ../test/test_common.hpp
));

ConvParamGenerator conv_1x1_params(testConvolution_1x1_Configs, sizeof(testConvolution_1x1_Configs) / sizeof(testConvolution_1x1_Configs[0]));
INSTANTIATE_TEST_CASE_P(/**/, Conv_1x1, Combine(
    conv_1x1_params.all(),
    dnnBackendsAndTargets(false, false)  // defined in ../test/test_common.hpp
));

ConvParamGenerator conv_3x3S1D1_params(testConvolution_3x3S1D1_Configs, sizeof(testConvolution_3x3S1D1_Configs) / sizeof(testConvolution_3x3S1D1_Configs[0]));
INSTANTIATE_TEST_CASE_P(/**/, Conv_3x3S1D1, Combine(
    conv_3x3S1D1_params.all(),
    dnnBackendsAndTargets(false, false),  // defined in ../test/test_common.hpp
    testing::Values(true, false)  // enable Winograd or not
));

ConvParamGenerator conv_depthwise_params(testConvolution_Depthwise_Configs, sizeof(testConvolution_Depthwise_Configs) / sizeof(testConvolution_Depthwise_Configs[0]));
INSTANTIATE_TEST_CASE_P(/**/, Conv_Depthwise, Combine(
    conv_depthwise_params.all(),
    dnnBackendsAndTargets(false, false)  // defined in ../test/test_common.hpp
));

} // namespace
