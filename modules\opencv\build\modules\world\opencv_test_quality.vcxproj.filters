﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\quality\test\test_brisque.cpp">
      <Filter>opencv_quality\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\quality\test\test_gmsd.cpp">
      <Filter>opencv_quality\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\quality\test\test_main.cpp">
      <Filter>opencv_quality\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\quality\test\test_mse.cpp">
      <Filter>opencv_quality\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\quality\test\test_psnr.cpp">
      <Filter>opencv_quality\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\quality\test\test_ssim.cpp">
      <Filter>opencv_quality\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv_contrib-4.10.0\modules\quality\test\test_precomp.hpp">
      <Filter>opencv_quality\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_quality">
      <UniqueIdentifier>{A87F41EF-9823-3D9C-B8DF-C3427803CE94}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_quality\Include">
      <UniqueIdentifier>{3545A6C8-7EB2-3CA4-99BD-9EC075C5D94E}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_quality\Src">
      <UniqueIdentifier>{C3E12B0F-ACC8-399E-82E7-569F5072F5B4}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
