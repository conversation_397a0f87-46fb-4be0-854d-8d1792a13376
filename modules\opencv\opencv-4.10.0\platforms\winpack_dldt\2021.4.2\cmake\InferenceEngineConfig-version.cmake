# Copyright (C) 2018-2021 Intel Corporation
# SPDX-License-Identifier: Apache-2.0
#

set(PACKAGE_VERSION_MAJOR 2021)
set(PACKAGE_VERSION_MINOR 4)
set(PACKAGE_VERSION_PATCH 2)
set(PACKAGE_VERSION "${PACKAGE_VERSION_MAJOR}.${PACKAGE_VERSION_MINOR}.${PACKAGE_VERSION_PATCH}")

set(PACKAGE_VERSION_EXACT False)
set(PACKAGE_VERSION_COMPATIBLE False)

# Compatibility with old versioning for 2.x
if(PACKAGE_FIND_VERSION_MAJOR VERSION_EQUAL 2)
    set(PACKAGE_VERSION_COMPATIBLE True)
    if(${CMAKE_FIND_PACKAGE_NAME}_FIND_REQUIRED)
        message(WARNING "Inference Engine versioning has changed. Use ${PACKAGE_VERSION} instead of ${PACKAGE_FIND_VERSION}")
    endif()
endif()

if(PACKAGE_FIND_VERSION VERSION_EQUAL PACKAGE_VERSION)
    set(PACKAGE_VERSION_EXACT True)
    set(PACKAGE_VERSION_COMPATIBLE True)
endif()

if(PACKAGE_FIND_VERSION_MAJOR EQUAL PACKAGE_VERSION_MAJOR AND
   PACKAGE_FIND_VERSION VERSION_LESS PACKAGE_VERSION)
    set(PACKAGE_VERSION_COMPATIBLE True)
endif()
