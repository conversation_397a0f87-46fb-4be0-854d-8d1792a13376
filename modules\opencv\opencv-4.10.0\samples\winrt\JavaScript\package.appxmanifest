﻿<?xml version="1.0" encoding="utf-8"?>
<Package xmlns="http://schemas.microsoft.com/appx/2010/manifest" xmlns:m2="http://schemas.microsoft.com/appx/2013/manifest">
  <Identity Name="d70a3790-48ce-4e58-af60-ebefc22e9c7a" Version="*******" Publisher="CN=Sergei" />
  <Properties>
    <DisplayName>MediaCaptureJavaScript</DisplayName>
    <PublisherDisplayName>Sergei</PublisherDisplayName>
    <Logo>images\storelogo.png</Logo>
  </Properties>
  <Prerequisites>
    <OSMinVersion>6.3.0</OSMinVersion>
    <OSMaxVersionTested>6.3.0</OSMaxVersionTested>
  </Prerequisites>
  <Resources>
    <Resource Language="x-generate" />
  </Resources>
  <Applications>
    <Application Id="App" StartPage="default.html">
      <m2:VisualElements DisplayName="MediaCaptureJavaScript" Description="MediaCaptureJavaScript" ForegroundText="light" BackgroundColor="#464646" Square150x150Logo="images\Logo.png" Square30x30Logo="images\SmallLogo.png">
        <m2:SplashScreen Image="images\splashscreen.png" />
      </m2:VisualElements>
    </Application>
  </Applications>
  <Capabilities>
    <Capability Name="internetClient" />
    <DeviceCapability Name="webcam" />
  </Capabilities>
  <Extensions>
    <Extension Category="windows.activatableClass.inProcessServer">
      <InProcessServer>
        <Path>OcvTransform.dll</Path>
        <ActivatableClass ActivatableClassId="OcvTransform.OcvImageManipulations" ThreadingModel="both" />
      </InProcessServer>
    </Extension>
  </Extensions>
</Package>