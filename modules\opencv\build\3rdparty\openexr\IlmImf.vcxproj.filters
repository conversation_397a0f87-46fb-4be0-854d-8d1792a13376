﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half\half.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexBaseExc.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexThrowErrnoExc.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfAcesFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfB44Compressor.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfBoxAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCRgbaFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfChannelList.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfChannelListAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfChromaticities.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfChromaticitiesAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCompositeDeepScanLine.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCompressionAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCompressor.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfConvert.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepCompositing.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepFrameBuffer.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepImageStateAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepScanLineInputFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepScanLineInputPart.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepScanLineOutputFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepScanLineOutputPart.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepTiledInputFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepTiledInputPart.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepTiledOutputFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepTiledOutputPart.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDoubleAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDwaCompressor.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfEnvmap.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfEnvmapAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFastHuf.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFloatAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFloatVectorAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFrameBuffer.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFramesPerSecond.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfGenericInputFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfGenericOutputFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfHeader.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfHuf.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfIO.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfInputFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfInputPart.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfInputPartData.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfIntAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfKeyCode.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfKeyCodeAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfLineOrderAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfLut.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMatrixAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMisc.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMultiPartInputFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMultiPartOutputFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMultiView.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOpaqueAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOutputFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOutputPart.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOutputPartData.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPartType.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPizCompressor.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPreviewImage.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPreviewImageAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPxr24Compressor.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRational.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRationalAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRgbaFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRgbaYca.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRle.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRleCompressor.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfScanLineInputFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfStandardAttributes.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfStdIO.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfStringAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfStringVectorAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfSystemSpecific.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTestFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfThreading.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTileDescriptionAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTileOffsets.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledInputFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledInputPart.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledMisc.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledOutputFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledOutputPart.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledRgbaFile.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTimeCode.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTimeCodeAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfVecAttribute.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfVersion.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfWav.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfZip.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfZipCompressor.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\dwaLookups.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThread.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadMutex.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadMutexWin32.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadPool.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadSemaphore.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadSemaphoreWin32.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadWin32.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathBox.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathColorAlgo.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathFun.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathMatrixAlgo.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathRandom.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathShear.cpp">
      <Filter>Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathVec.cpp">
      <Filter>Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half\eLut.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half\half.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half\halfExport.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half\halfFunction.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half\halfLimits.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Half\toFloat.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\Iex.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexBaseExc.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexErrnoExc.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexExport.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexForward.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexMacros.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexMathExc.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexNamespace.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Iex\IexThrowErrnoExc.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfAcesFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfArray.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfAutoArray.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfB44Compressor.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfBoxAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCRgbaFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfChannelList.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfChannelListAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCheckedArithmetic.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfChromaticities.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfChromaticitiesAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCompositeDeepScanLine.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCompression.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCompressionAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfCompressor.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfConvert.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepCompositing.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepFrameBuffer.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepImageState.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepImageStateAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepScanLineInputFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepScanLineInputPart.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepScanLineOutputFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepScanLineOutputPart.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepTiledInputFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepTiledInputPart.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepTiledOutputFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDeepTiledOutputPart.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDoubleAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDwaCompressor.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfDwaCompressorSimd.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfEnvmap.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfEnvmapAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfExport.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFastHuf.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFloatAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFloatVectorAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfForward.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFrameBuffer.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfFramesPerSecond.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfGenericInputFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfGenericOutputFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfHeader.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfHuf.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfIO.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfInputFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfInputPart.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfInputPartData.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfInputStreamMutex.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfInt64.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfIntAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfKeyCode.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfKeyCodeAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfLineOrder.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfLineOrderAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfLut.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMatrixAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMisc.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMultiPartInputFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMultiPartOutputFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfMultiView.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfName.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfNamespace.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOpaqueAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOptimizedPixelReading.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOutputFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOutputPart.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOutputPartData.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfOutputStreamMutex.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPartHelper.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPartType.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPixelType.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPizCompressor.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPreviewImage.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPreviewImageAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfPxr24Compressor.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRational.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRationalAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRgba.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRgbaFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRgbaYca.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRle.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfRleCompressor.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfScanLineInputFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfSimd.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfStandardAttributes.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfStdIO.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfStringAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfStringVectorAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfSystemSpecific.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTestFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfThreading.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTileDescription.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTileDescriptionAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTileOffsets.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledInputFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledInputPart.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledMisc.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledOutputFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledOutputPart.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTiledRgbaFile.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTimeCode.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfTimeCodeAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfVecAttribute.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfVersion.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfWav.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfXdr.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfZip.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\ImfZipCompressor.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\b44ExpLogTable.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmImf\dwaLookups.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThread.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadExport.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadForward.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadMutex.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadNamespace.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadPool.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\IlmThread\IlmThreadSemaphore.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathBox.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathBoxAlgo.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathColor.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathColorAlgo.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathEuler.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathExc.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathExport.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathForward.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathFrame.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathFrustum.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathFrustumTest.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathFun.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathHalfLimits.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathInt64.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathInterval.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathLimits.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathLine.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathLineAlgo.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathMath.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathMatrix.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathMatrixAlgo.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathNamespace.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathPlane.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathPlatform.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathQuat.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathRandom.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathRoots.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathShear.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathSphere.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathVec.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\3rdparty\openexr\Imath\ImathVecAlgo.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\openexr\IlmBaseConfig.h">
      <Filter>Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\cudabuild\3rdparty\openexr\OpenEXRConfig.h">
      <Filter>Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Include">
      <UniqueIdentifier>{0D522D53-72BF-369F-A761-1665CACACBF7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Src">
      <UniqueIdentifier>{4C321FAF-208D-3921-91F6-A2582E33B23F}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
