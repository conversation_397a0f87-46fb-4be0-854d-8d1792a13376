{"AdditionalImports": {"Imgproc": ["\"imgproc/bindings.hpp\""]}, "enum_ignore_list": ["MorphShapes_c", "SmoothMethod_c"], "module_imports": ["Size2i"], "const_ignore_list": ["CV_TM_.+", "CV_COLORCVT_MAX", "CV_.*<PERSON><PERSON>.*", "CV_YUV420(i|sp|p)2.+", "CV_L?(BGRA?|RGBA?|GRAY|XYZ|YCrCb|Luv|Lab|HLS|YUV|HSV)\\d*2L?(BGRA?|RGBA?|GRAY|XYZ|YCrCb|Luv|Lab|HLS|YUV|HSV).*", "CV_FLOODFILL_.+", "CV_ADAPTIVE_THRESH_.+", "CV_DIST_.+", "CV_HOUGH_.+", "CV_CONTOURS_MATCH_.+", "CV_COMP_.+"], "const_private_list": ["CV_MOP_.+", "CV_INTER_.+", "CV_THRESH_.+", "CV_INPAINT_.+", "CV_RETR_.+", "CV_CHAIN_APPROX_.+"], "missing_consts": {"Imgproc": {"private": [["IPL_BORDER_CONSTANT", 0], ["IPL_BORDER_REPLICATE", 1], ["IPL_BORDER_REFLECT", 2], ["IPL_BORDER_WRAP", 3], ["IPL_BORDER_REFLECT_101", 4], ["IPL_BORDER_TRANSPARENT", 5]]}}, "func_arg_fix": {"Imgproc": {"goodFeaturesToTrack": {"corners": {"ctype": "vector_Point"}}, "minEnclosingCircle": {"points": {"ctype": "vector_Point2f"}}, "fitEllipse": {"points": {"ctype": "vector_Point2f"}}, "fillPoly": {"pts": {"ctype": "vector_vector_Point"}, "lineType": {"ctype": "LineTypes"}}, "polylines": {"pts": {"ctype": "vector_vector_Point"}, "lineType": {"ctype": "LineTypes"}}, "fillConvexPoly": {"points": {"ctype": "vector_Point"}, "lineType": {"ctype": "LineTypes"}}, "approxPolyDP": {"curve": {"ctype": "vector_Point2f"}, "approxCurve": {"ctype": "vector_Point2f"}}, "arcLength": {"curve": {"ctype": "vector_Point2f"}}, "pointPolygonTest": {"contour": {"ctype": "vector_Point2f"}}, "minAreaRect": {"points": {"ctype": "vector_Point2f"}}, "getAffineTransform": {"src": {"ctype": "vector_Point2f"}, "dst": {"ctype": "vector_Point2f"}}, "drawContours": {"contours": {"ctype": "vector_vector_Point"}, "lineType": {"ctype": "LineTypes"}}, "findContours": {"contours": {"ctype": "vector_vector_Point"}, "mode": {"ctype": "RetrievalModes"}, "method": {"ctype": "ContourApproximationModes"}}, "convexityDefects": {"contour": {"ctype": "vector_Point"}, "convexhull": {"ctype": "vector_int"}, "convexityDefects": {"ctype": "vector_Vec4i"}}, "isContourConvex": {"contour": {"ctype": "vector_Point"}}, "convexHull": {"points": {"ctype": "vector_Point"}, "hull": {"ctype": "vector_int"}, "returnPoints": {"ctype": ""}}, "getStructuringElement": {"shape": {"ctype": "MorphShapes"}}, "EMD": {"lowerBound": {"defval": "cv::Ptr<float>()"}, "distType": {"ctype": "DistanceTypes"}}, "createLineSegmentDetector": {"_refine": {"ctype": "LineSegmentDetectorModes"}}, "compareHist": {"method": {"ctype": "HistCompMethods"}}, "matchShapes": {"method": {"ctype": "ShapeMatchModes"}}, "threshold": {"type": {"ctype": "ThresholdTypes"}}, "connectedComponentsWithStatsWithAlgorithm": {"ccltype": {"ctype": "ConnectedComponentsAlgorithmsTypes"}}, "GaussianBlur": {"borderType": {"ctype": "BorderTypes"}}, "HoughCircles": {"method": {"ctype": "HoughModes"}}, "Laplacian": {"borderType": {"ctype": "BorderTypes"}}, "Scharr": {"borderType": {"ctype": "BorderTypes"}}, "Sobel": {"borderType": {"ctype": "BorderTypes"}}, "adaptiveThreshold": {"adaptiveMethod": {"ctype": "AdaptiveThresholdTypes"}, "thresholdType": {"ctype": "ThresholdTypes"}}, "applyColorMap": {"colormap": {"ctype": "ColormapTypes"}}, "arrowedLine": {"line_type": {"ctype": "LineTypes"}}, "bilateralFilter": {"borderType": {"ctype": "BorderTypes"}}, "blur": {"borderType": {"ctype": "BorderTypes"}}, "boxFilter": {"borderType": {"ctype": "BorderTypes"}}, "circle": {"lineType": {"ctype": "LineTypes"}}, "cornerEigenValsAndVecs": {"borderType": {"ctype": "BorderTypes"}}, "cornerHarris": {"borderType": {"ctype": "BorderTypes"}}, "cornerMinEigenVal": {"borderType": {"ctype": "BorderTypes"}}, "cvtColor": {"code": {"ctype": "ColorConversionCodes"}}, "dilate": {"borderType": {"ctype": "BorderTypes"}}, "distanceTransformWithLabels": {"labelType": {"ctype": "DistanceTransformLabelTypes"}, "distanceType": {"ctype": "DistanceTypes"}, "maskSize": {"ctype": "DistanceTransformMasks"}}, "distanceTransform": {"distanceType": {"ctype": "DistanceTypes"}, "maskSize": {"ctype": "DistanceTransformMasks"}}, "drawMarker": {"markerType": {"ctype": "MarkerTypes"}, "line_type": {"ctype": "LineTypes"}}, "ellipse": {"lineType": {"ctype": "LineTypes"}}, "erode": {"borderType": {"ctype": "BorderTypes"}}, "filter2D": {"borderType": {"ctype": "BorderTypes"}}, "fitLine": {"distType": {"ctype": "DistanceTypes"}}, "line": {"lineType": {"ctype": "LineTypes"}}, "matchTemplate": {"method": {"ctype": "TemplateMatchModes"}}, "morphologyEx": {"op": {"ctype": "MorphTypes"}, "borderType": {"ctype": "BorderTypes"}}, "preCornerDetect": {"borderType": {"ctype": "BorderTypes"}}, "putText": {"fontFace": {"ctype": "HersheyFonts"}, "lineType": {"ctype": "LineTypes"}}, "pyrDown": {"borderType": {"ctype": "BorderTypes"}}, "pyrUp": {"borderType": {"ctype": "BorderTypes"}}, "rectangle": {"lineType": {"ctype": "LineTypes"}}, "remap": {"borderMode": {"ctype": "BorderTypes"}}, "sepFilter2D": {"borderType": {"ctype": "BorderTypes"}}, "spatialGradient": {"borderType": {"ctype": "BorderTypes"}}, "sqrBoxFilter": {"borderType": {"ctype": "BorderTypes"}}, "warpAffine": {"borderMode": {"ctype": "BorderTypes"}}, "warpPerspective": {"borderMode": {"ctype": "BorderTypes"}}, "getTextSize": {"fontFace": {"ctype": "HersheyFonts"}}}, "Subdiv2D": {"(void)insert:(NSArray<Point2f*>*)ptvec": {"insert": {"name": "insertVector"}}}}}