var searchData=
[
  ['a_20loader_20library_0',['Loading extension with a loader library',['../context_guide.html#context_glext_auto',1,'']]],
  ['a_20subproject_1',['Tests and examples are disabled when built as a subproject',['../news.html#standalone_caveat',1,'']]],
  ['a_20vulkan_20window_20surface_2',['Creating a Vulkan window surface',['../vulkan_guide.html#vulkan_surface',1,'']]],
  ['a_20window_20and_20context_3',['Creating a window and context',['../quick_guide.html#quick_create_window',1,'']]],
  ['ability_20to_20get_20window_20title_4',['Ability to get window title',['../news.html#window_title_function',1,'']]],
  ['access_5',['Native access',['../group__native.html',1,'']]],
  ['access_20function_6',['Cocoa NSView native access function',['../news.html#cocoa_nsview_function',1,'']]],
  ['access_20functions_7',['Multiple sets of native access functions',['../news.html#multiplatform_caveat',1,'']]],
  ['access_20hint_8',['Windows window menu keyboard access hint',['../news.html#win32_keymenu_hint',1,'']]],
  ['action_9',['Key repeat action',['../moving_guide.html#moving_repeat',1,'']]],
  ['actions_10',['Removal of character actions',['../moving_guide.html#moving_char_up',1,'']]],
  ['allocate_11',['allocate',['../struct_g_l_f_wallocator.html#a18a798136f17a9cb105be18312193bf7',1,'GLFWallocator']]],
  ['allocator_12',['allocator',['../intro_guide.html#init_allocator',1,'Custom heap memory allocator'],['../news.html#custom_heap_allocator',1,'Support for custom heap memory allocator']]],
  ['alpha_20channel_20on_20older_20systems_13',['Wayland framebuffer may lack alpha channel on older systems',['../news.html#wayland_alpha_caveat',1,'']]],
  ['an_20error_20callback_14',['Setting an error callback',['../quick_guide.html#quick_capture_error',1,'']]],
  ['and_20api_15',['Vulkan loader and API',['../compat_guide.html#compat_vulkan',1,'']]],
  ['and_20close_20flag_16',['Window closing and close flag',['../window_guide.html#window_close',1,'']]],
  ['and_20context_17',['Creating a window and context',['../quick_guide.html#quick_create_window',1,'']]],
  ['and_20default_20values_18',['and default values',['../window_guide.html#window_hints_values',1,'Supported and default values'],['../intro_guide.html#init_hints_values',1,'Supported and default values']]],
  ['and_20error_20reference_19',['Initialization, version and error reference',['../group__init.html',1,'']]],
  ['and_20events_20',['Window properties and events',['../window_guide.html#window_properties',1,'']]],
  ['and_20examples_20are_20disabled_20when_20built_20as_20a_20subproject_21',['Tests and examples are disabled when built as a subproject',['../news.html#standalone_caveat',1,'']]],
  ['and_20framebuffer_20sizes_22',['Separation of window and framebuffer sizes',['../moving_guide.html#moving_hidpi',1,'']]],
  ['and_20glfw_20binaries_23',['and glfw binaries',['../build_guide.html#build_link_mingw',1,'With MinGW-w64 and GLFW binaries'],['../build_guide.html#build_link_win32',1,'With Visual C++ and GLFW binaries']]],
  ['and_20glfw_20binaries_20on_20unix_24',['With pkg-config and GLFW binaries on Unix',['../build_guide.html#build_link_pkgconfig',1,'']]],
  ['and_20glfw_20source_25',['With CMake and GLFW source',['../build_guide.html#build_link_cmake_source',1,'']]],
  ['and_20header_20file_26',['Renamed library and header file',['../moving_guide.html#moving_renamed_files',1,'']]],
  ['and_20installed_20glfw_20binaries_27',['With CMake and installed GLFW binaries',['../build_guide.html#build_link_cmake_package',1,'']]],
  ['and_20ipc_20standards_28',['and ipc standards',['../compat_guide.html#compat_wayland',1,'Wayland protocols and IPC standards'],['../compat_guide.html#compat_x11',1,'X11 extensions, protocols and IPC standards']]],
  ['and_20limitations_29',['Guarantees and limitations',['../intro_guide.html#guarantees_limitations',1,'']]],
  ['and_20mingw_30',['Cross-compilation with CMake and MinGW',['../compile_guide.html#compile_mingw_cross',1,'']]],
  ['and_20opengl_20es_20extensions_31',['OpenGL and OpenGL ES extensions',['../context_guide.html#context_glext',1,'']]],
  ['and_20output_32',['Clipboard input and output',['../input_guide.html#clipboard',1,'']]],
  ['and_20refresh_33',['Window damage and refresh',['../window_guide.html#window_refresh',1,'']]],
  ['and_20removed_20features_34',['Changed and removed features',['../moving_guide.html#moving_removed',1,'']]],
  ['and_20soft_20constraints_35',['Hard and soft constraints',['../window_guide.html#window_hints_hard',1,'']]],
  ['and_20terminating_20glfw_36',['Initializing and terminating GLFW',['../quick_guide.html#quick_init_term',1,'']]],
  ['and_20termination_37',['Initialization and termination',['../intro_guide.html#intro_init',1,'']]],
  ['and_20texture_20loading_38',['Removal of image and texture loading',['../moving_guide.html#moving_image',1,'']]],
  ['and_20vista_20support_20is_20deprecated_39',['Windows XP and Vista support is deprecated',['../news.html#winxp_deprecated',1,'']]],
  ['and_20x11_40',['Dependencies for Wayland and X11',['../compile_guide.html#compile_deps_wayland',1,'']]],
  ['angle_20rendering_20backend_20hint_41',['ANGLE rendering backend hint',['../news.html#angle_renderer_hint',1,'']]],
  ['api_42',['api',['../intro_guide.html',1,'Introduction to the API'],['../compat_guide.html#compat_vulkan',1,'Vulkan loader and API']]],
  ['app_5fid_20hint_43',['Wayland surface app_id hint',['../news.html#wayland_app_id_hint',1,'']]],
  ['applications_44',['Building applications',['../build_guide.html',1,'']]],
  ['are_20disabled_20when_20built_20as_20a_20subproject_45',['Tests and examples are disabled when built as a subproject',['../news.html#standalone_caveat',1,'']]],
  ['area_46',['Work area',['../monitor_guide.html#monitor_workarea',1,'']]],
  ['as_20a_20subproject_47',['Tests and examples are disabled when built as a subproject',['../news.html#standalone_caveat',1,'']]],
  ['at_20initialization_48',['macOS main menu now created at initialization',['../news.html#macos_menu_caveat',1,'']]],
  ['attention_20request_49',['Window attention request',['../window_guide.html#window_attention',1,'']]],
  ['attributes_50',['attributes',['../window_guide.html#window_attribs_ctx',1,'Context related attributes'],['../window_guide.html#window_attribs_fb',1,'Framebuffer related attributes'],['../window_guide.html#window_attribs',1,'Window attributes'],['../window_guide.html#window_attribs_wnd',1,'Window related attributes']]],
  ['automatic_20event_20polling_51',['Removal of automatic event polling',['../moving_guide.html#moving_autopoll',1,'']]],
  ['automatic_20termination_52',['Automatic termination',['../moving_guide.html#moving_terminate',1,'']]],
  ['axes_53',['axes',['../struct_g_l_f_wgamepadstate.html#a8b2c8939b1d31458de5359998375c189',1,'GLFWgamepadstate::axes'],['../group__gamepad__axes.html',1,'Gamepad axes']]],
  ['axis_20states_54',['Joystick axis states',['../input_guide.html#joystick_axis',1,'']]]
];
