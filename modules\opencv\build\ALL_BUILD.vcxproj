﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{F2526E4E-3C28-36B3-A47A-CE6139EFF28A}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\icv\include;D:\AI\opencv\cudabuild\3rdparty\ippicv\ippicv_win\iw\include;D:\AI\opencv\cudabuild;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg12-static.vcxproj">
      <Project>{CADC7BEE-E51E-3A9E-8E05-C698B5F6BF26}</Project>
      <Name>jpeg12-static</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\jpeg16-static.vcxproj">
      <Project>{7852B34B-313F-33D0-AA1E-68C6E3F266E8}</Project>
      <Name>jpeg16-static</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\3rdparty\libjpeg-turbo\libjpeg-turbo.vcxproj">
      <Project>{1F40845B-51B2-3472-A2DF-B8EF314C3FCC}</Project>
      <Name>libjpeg-turbo</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\3rdparty\openjpeg\openjp2\libopenjp2.vcxproj">
      <Project>{4F8E09EE-C41E-34EE-81A6-59277C2ADFA0}</Project>
      <Name>libopenjp2</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\apps\annotation\opencv_annotation.vcxproj">
      <Project>{5DA7FF23-BE3F-3D0F-A23A-7F561AA1C83D}</Project>
      <Name>opencv_annotation</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\opencv_dnn_plugins.vcxproj">
      <Project>{686998EB-5B30-34A5-B6EA-4BF71339BC3E}</Project>
      <Name>opencv_dnn_plugins</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\opencv_highgui_plugins.vcxproj">
      <Project>{D6B65C5B-C7AE-36D8-8C74-DC2486A2C52E}</Project>
      <Name>opencv_highgui_plugins</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\img_hash\opencv_img_hash.vcxproj">
      <Project>{A08E5D4A-534F-33E3-A6E7-77CB100FF737}</Project>
      <Name>opencv_img_hash</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\apps\interactive-calibration\opencv_interactive-calibration.vcxproj">
      <Project>{B4FF3EEB-EDAC-36BB-95C8-C7C515540C20}</Project>
      <Name>opencv_interactive-calibration</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\apps\model-diagnostics\opencv_model_diagnostics.vcxproj">
      <Project>{5E9C809B-12D2-3488-B123-D91142B3C4F5}</Project>
      <Name>opencv_model_diagnostics</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_bioinspired.vcxproj">
      <Project>{C1FEB36D-4FBB-371F-A03A-4993CF3F0A87}</Project>
      <Name>opencv_perf_bioinspired</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_calib3d.vcxproj">
      <Project>{D1D93572-C1E2-3BB3-B873-83349D483FE1}</Project>
      <Name>opencv_perf_calib3d</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_core.vcxproj">
      <Project>{36306458-1264-3737-8466-16B8BBEA87AB}</Project>
      <Name>opencv_perf_core</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudaarithm.vcxproj">
      <Project>{95BD6AFD-7651-322C-8C99-E8669BA52F80}</Project>
      <Name>opencv_perf_cudaarithm</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudabgsegm.vcxproj">
      <Project>{86B66096-C821-3DD6-9F1A-AC4C4101585E}</Project>
      <Name>opencv_perf_cudabgsegm</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudacodec.vcxproj">
      <Project>{B234651C-019E-39E9-B34A-137C60E711A7}</Project>
      <Name>opencv_perf_cudacodec</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudafeatures2d.vcxproj">
      <Project>{83B16A7A-2B33-3422-9C53-DAF36DB48C22}</Project>
      <Name>opencv_perf_cudafeatures2d</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudafilters.vcxproj">
      <Project>{CFCB55D5-CD4F-36E7-9398-107181B05336}</Project>
      <Name>opencv_perf_cudafilters</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudaimgproc.vcxproj">
      <Project>{F835E413-1BC8-33C6-BAC1-A9F19B718613}</Project>
      <Name>opencv_perf_cudaimgproc</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudalegacy.vcxproj">
      <Project>{F9CD9DC9-2315-3B2E-8549-700C0AA6FAE8}</Project>
      <Name>opencv_perf_cudalegacy</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudaobjdetect.vcxproj">
      <Project>{F9B177C0-A866-3586-BF1C-82EB50423F2D}</Project>
      <Name>opencv_perf_cudaobjdetect</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudaoptflow.vcxproj">
      <Project>{2D862446-A894-3760-A872-1FBFD2230942}</Project>
      <Name>opencv_perf_cudaoptflow</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudastereo.vcxproj">
      <Project>{14D47995-AB86-3820-9F1A-F2C724F982BE}</Project>
      <Name>opencv_perf_cudastereo</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_cudawarping.vcxproj">
      <Project>{399FC75A-4976-3F26-AD37-20E4372120B8}</Project>
      <Name>opencv_perf_cudawarping</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_dnn.vcxproj">
      <Project>{65484FAC-DBA1-395D-BB66-A28BF5F6DCC0}</Project>
      <Name>opencv_perf_dnn</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_dnn_superres.vcxproj">
      <Project>{ACFD9BDF-7968-3755-BBF4-A64AF925FFCC}</Project>
      <Name>opencv_perf_dnn_superres</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_features2d.vcxproj">
      <Project>{6A5C8157-3921-3A81-8AD8-2D9A54798119}</Project>
      <Name>opencv_perf_features2d</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_gapi.vcxproj">
      <Project>{F1DB0A13-FD34-3CC6-B7C1-CDF54AD0F871}</Project>
      <Name>opencv_perf_gapi</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_imgcodecs.vcxproj">
      <Project>{5A05B04F-5B35-31A9-BEA2-EEBE49FD1469}</Project>
      <Name>opencv_perf_imgcodecs</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_imgproc.vcxproj">
      <Project>{37AD4DB7-6D26-316C-BC4C-00A88BC39522}</Project>
      <Name>opencv_perf_imgproc</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_line_descriptor.vcxproj">
      <Project>{FF8BC5AE-369A-3EE7-984D-ACAFCE2902F1}</Project>
      <Name>opencv_perf_line_descriptor</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_mcc.vcxproj">
      <Project>{555100BA-6397-34EA-8367-0AD515D1167C}</Project>
      <Name>opencv_perf_mcc</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_objdetect.vcxproj">
      <Project>{BF1B7D49-1E44-3932-AF55-60878DE9858F}</Project>
      <Name>opencv_perf_objdetect</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_optflow.vcxproj">
      <Project>{42952552-9334-3AED-AECC-63C48D4AB5E7}</Project>
      <Name>opencv_perf_optflow</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_photo.vcxproj">
      <Project>{AFC8B534-DE25-3346-AF67-B0615A432FFB}</Project>
      <Name>opencv_perf_photo</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_reg.vcxproj">
      <Project>{9D6FB9F3-45AB-3C48-9491-5A6D12D69F81}</Project>
      <Name>opencv_perf_reg</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_rgbd.vcxproj">
      <Project>{F1871AA5-2651-3A13-97C5-92B6719E1E0E}</Project>
      <Name>opencv_perf_rgbd</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_signal.vcxproj">
      <Project>{5398681F-8D40-31A3-AF53-8B310ADBD646}</Project>
      <Name>opencv_perf_signal</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_stereo.vcxproj">
      <Project>{C04D9893-B65A-3CB5-BEA8-F7BC2B28125B}</Project>
      <Name>opencv_perf_stereo</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_stitching.vcxproj">
      <Project>{69796F08-E11B-37C4-A04A-437BABDD7AD0}</Project>
      <Name>opencv_perf_stitching</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_superres.vcxproj">
      <Project>{5CD2B0A5-0496-3D34-BA12-31E74C191782}</Project>
      <Name>opencv_perf_superres</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_tracking.vcxproj">
      <Project>{5AA0F741-7269-3AC0-B3A2-24B3585E56D5}</Project>
      <Name>opencv_perf_tracking</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_video.vcxproj">
      <Project>{3470A316-D0F1-3AFA-A9DA-EE4DEAA67804}</Project>
      <Name>opencv_perf_video</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_videoio.vcxproj">
      <Project>{B741E106-84DB-3AE1-91BA-E316CD342B09}</Project>
      <Name>opencv_perf_videoio</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_wechat_qrcode.vcxproj">
      <Project>{14857B7C-E07F-3009-B130-9F723F420AA5}</Project>
      <Name>opencv_perf_wechat_qrcode</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_xfeatures2d.vcxproj">
      <Project>{0ED4EA1B-1F60-38A9-A880-D094B9B25142}</Project>
      <Name>opencv_perf_xfeatures2d</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_ximgproc.vcxproj">
      <Project>{7800CC11-25D2-32F2-9E5C-18C37CCBBE19}</Project>
      <Name>opencv_perf_ximgproc</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_perf_xphoto.vcxproj">
      <Project>{E137F199-4C9A-3207-811F-1AC426819C92}</Project>
      <Name>opencv_perf_xphoto</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\python3\opencv_python3.vcxproj">
      <Project>{EDB41763-5239-357D-B14E-025520FE6A94}</Project>
      <Name>opencv_python3</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_bgsegm.vcxproj">
      <Project>{AB274CE3-4130-34A2-B5EC-A7D68E30C8E4}</Project>
      <Name>opencv_test_bgsegm</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_bioinspired.vcxproj">
      <Project>{2A6FBB46-2663-3AB0-85F0-18D836292306}</Project>
      <Name>opencv_test_bioinspired</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_calib3d.vcxproj">
      <Project>{A9CE6310-C337-3620-855F-17BCA886D2D8}</Project>
      <Name>opencv_test_calib3d</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core.vcxproj">
      <Project>{20BCDCC2-EF6B-38D3-93C7-4F95DD5145FD}</Project>
      <Name>opencv_test_core</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_AVX.vcxproj">
      <Project>{0603C62E-A127-3A3C-9E19-721912520EBA}</Project>
      <Name>opencv_test_core_AVX</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_AVX2.vcxproj">
      <Project>{D8006AE6-6090-3419-B471-BF43F0508EBB}</Project>
      <Name>opencv_test_core_AVX2</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_AVX512_SKX.vcxproj">
      <Project>{3A49CE3B-8FD9-37C2-AFAD-3664091325BF}</Project>
      <Name>opencv_test_core_AVX512_SKX</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_FP16.vcxproj">
      <Project>{0EC249A6-AABA-32D5-8DAE-DB7A1EF0C944}</Project>
      <Name>opencv_test_core_FP16</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_SSE2.vcxproj">
      <Project>{094C0376-067E-35A3-A95A-023D429E531D}</Project>
      <Name>opencv_test_core_SSE2</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_SSE3.vcxproj">
      <Project>{CE4B68AA-BF8B-3956-979E-65B4769E22F7}</Project>
      <Name>opencv_test_core_SSE3</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_SSE4_1.vcxproj">
      <Project>{F59847BE-9F31-349E-9509-E2192BA8815B}</Project>
      <Name>opencv_test_core_SSE4_1</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_core_SSE4_2.vcxproj">
      <Project>{3A3861A2-4C53-34D4-90CE-EBB7058F0617}</Project>
      <Name>opencv_test_core_SSE4_2</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudaarithm.vcxproj">
      <Project>{17AA757A-594D-3DEE-8D59-3ADEFED4AEBB}</Project>
      <Name>opencv_test_cudaarithm</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudabgsegm.vcxproj">
      <Project>{1DDD7B63-9F19-3C5C-89DA-304A093ACCDB}</Project>
      <Name>opencv_test_cudabgsegm</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudacodec.vcxproj">
      <Project>{F961C93E-6792-367A-9B0D-E628A1355FA3}</Project>
      <Name>opencv_test_cudacodec</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudafeatures2d.vcxproj">
      <Project>{6F77DF9F-18C7-3EDD-B1AC-F1A6012F1C07}</Project>
      <Name>opencv_test_cudafeatures2d</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudafilters.vcxproj">
      <Project>{1CC02697-45BB-3566-8530-18674ED91696}</Project>
      <Name>opencv_test_cudafilters</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudaimgproc.vcxproj">
      <Project>{43A1D6CD-7084-3CC5-AEE9-F66B0934BF16}</Project>
      <Name>opencv_test_cudaimgproc</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudalegacy.vcxproj">
      <Project>{5BC869F2-B849-372A-97C7-C0E483D5BF5F}</Project>
      <Name>opencv_test_cudalegacy</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudaobjdetect.vcxproj">
      <Project>{7F2F54A4-E8D8-32F3-AC99-785755C68408}</Project>
      <Name>opencv_test_cudaobjdetect</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudaoptflow.vcxproj">
      <Project>{F1D25643-E3A6-3502-A988-2E37D154D30B}</Project>
      <Name>opencv_test_cudaoptflow</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudastereo.vcxproj">
      <Project>{80710832-FE32-32D2-B677-D260AEB166B2}</Project>
      <Name>opencv_test_cudastereo</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_cudawarping.vcxproj">
      <Project>{2D8309F8-A147-32E4-A1DF-88FB846D9277}</Project>
      <Name>opencv_test_cudawarping</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_dnn.vcxproj">
      <Project>{CA46D433-1B5A-3B6B-A526-1DF97D2588E0}</Project>
      <Name>opencv_test_dnn</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_dnn_superres.vcxproj">
      <Project>{AFA7C989-F448-365B-9DC9-5B5F107D04AD}</Project>
      <Name>opencv_test_dnn_superres</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_face.vcxproj">
      <Project>{FA3383DA-1CB9-3C20-AD36-2942D3D1D154}</Project>
      <Name>opencv_test_face</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_features2d.vcxproj">
      <Project>{2E4C3488-B183-3E50-898C-DC184AB27AED}</Project>
      <Name>opencv_test_features2d</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_flann.vcxproj">
      <Project>{BF4D207B-3FA9-3AA7-9FE2-E74D00C9DC4F}</Project>
      <Name>opencv_test_flann</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_fuzzy.vcxproj">
      <Project>{80FB8534-759F-37E7-BF4E-87B22CD3411F}</Project>
      <Name>opencv_test_fuzzy</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_gapi.vcxproj">
      <Project>{7E3F3299-9AE9-3535-A393-D06BECB3AEFD}</Project>
      <Name>opencv_test_gapi</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_highgui.vcxproj">
      <Project>{E72852E4-19D5-3DDE-A8CD-BB6F3DBEF2BE}</Project>
      <Name>opencv_test_highgui</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\img_hash\opencv_test_img_hash.vcxproj">
      <Project>{9610D2F5-A759-3F64-BB1B-AD434162186D}</Project>
      <Name>opencv_test_img_hash</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_imgcodecs.vcxproj">
      <Project>{A763175A-C8AA-3B7B-996D-4380075FC3D7}</Project>
      <Name>opencv_test_imgcodecs</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_imgproc.vcxproj">
      <Project>{1BB61935-747B-3444-8C64-D3908443C5DB}</Project>
      <Name>opencv_test_imgproc</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_intensity_transform.vcxproj">
      <Project>{694084B4-D6E7-352E-BD15-EE36FE1D1D50}</Project>
      <Name>opencv_test_intensity_transform</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_line_descriptor.vcxproj">
      <Project>{F713356D-4283-3E70-846A-88939E5A015A}</Project>
      <Name>opencv_test_line_descriptor</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_mcc.vcxproj">
      <Project>{0D9F8B5C-08F2-3D9B-A40F-8053FF826DBA}</Project>
      <Name>opencv_test_mcc</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_ml.vcxproj">
      <Project>{ADE70FE5-1BCF-3439-80B7-7984F3347868}</Project>
      <Name>opencv_test_ml</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_objdetect.vcxproj">
      <Project>{FDE3F5E1-BEF3-31CC-9681-4A6EAE8E8EC0}</Project>
      <Name>opencv_test_objdetect</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_optflow.vcxproj">
      <Project>{FB291324-E518-3CE8-AB43-683A43ECFDE5}</Project>
      <Name>opencv_test_optflow</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_phase_unwrapping.vcxproj">
      <Project>{59D15469-9DFC-3E6A-A662-3AFE4DAE934B}</Project>
      <Name>opencv_test_phase_unwrapping</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_photo.vcxproj">
      <Project>{4A381F53-2CFD-3FE2-82FC-FE74977E11A4}</Project>
      <Name>opencv_test_photo</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_quality.vcxproj">
      <Project>{D51DD788-733B-3F2F-9645-E7DC0001A609}</Project>
      <Name>opencv_test_quality</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_rapid.vcxproj">
      <Project>{2A097E88-2AF4-3824-A7BD-62220CA48022}</Project>
      <Name>opencv_test_rapid</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_reg.vcxproj">
      <Project>{285FEFA0-FC04-328F-B708-ECDE7E8F742D}</Project>
      <Name>opencv_test_reg</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_rgbd.vcxproj">
      <Project>{4A4F57BF-D9B4-3775-BBE9-54784FDC7D16}</Project>
      <Name>opencv_test_rgbd</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_saliency.vcxproj">
      <Project>{D2B920E9-7EE2-3C6F-AF90-0EBC49A44D89}</Project>
      <Name>opencv_test_saliency</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_shape.vcxproj">
      <Project>{B514C5B2-8EED-3100-9065-17A4DFAB0584}</Project>
      <Name>opencv_test_shape</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_signal.vcxproj">
      <Project>{2480BE67-8FA8-358D-95CD-1D680651AEE4}</Project>
      <Name>opencv_test_signal</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_stereo.vcxproj">
      <Project>{C3CB63F4-AD2B-3566-9E81-A9EE3872264E}</Project>
      <Name>opencv_test_stereo</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_stitching.vcxproj">
      <Project>{17A02BA3-F75D-3258-9349-956F14111B3F}</Project>
      <Name>opencv_test_stitching</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_structured_light.vcxproj">
      <Project>{03C548BA-2900-3ACA-B8AF-7ED57B66E862}</Project>
      <Name>opencv_test_structured_light</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_superres.vcxproj">
      <Project>{32717AB4-1792-3A36-8CA1-3F75318B1007}</Project>
      <Name>opencv_test_superres</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_text.vcxproj">
      <Project>{D28E9863-5E52-3895-9EFC-61EA4141D5ED}</Project>
      <Name>opencv_test_text</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_tracking.vcxproj">
      <Project>{EC8691B2-4B87-39ED-AEE9-0AEC7DE34B53}</Project>
      <Name>opencv_test_tracking</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_video.vcxproj">
      <Project>{C96EB2D4-BCEE-3536-BBBE-E39F2945167D}</Project>
      <Name>opencv_test_video</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_videoio.vcxproj">
      <Project>{FEE9F549-931A-35BA-A257-5157B9F6338C}</Project>
      <Name>opencv_test_videoio</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_videostab.vcxproj">
      <Project>{1E3C01D7-9346-30C2-A5EE-AE039CAC8886}</Project>
      <Name>opencv_test_videostab</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_wechat_qrcode.vcxproj">
      <Project>{4DFA4B86-3BFC-35C5-81B4-216805D2163A}</Project>
      <Name>opencv_test_wechat_qrcode</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_xfeatures2d.vcxproj">
      <Project>{60F8BCD5-A50D-3EBB-BAE8-1AA720C90510}</Project>
      <Name>opencv_test_xfeatures2d</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_ximgproc.vcxproj">
      <Project>{2199F101-C616-3A57-ACF3-1D944B533DA3}</Project>
      <Name>opencv_test_ximgproc</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_test_xphoto.vcxproj">
      <Project>{2E23BA6E-575F-3796-B694-D4114A0E37E8}</Project>
      <Name>opencv_test_xphoto</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\ts\opencv_ts.vcxproj">
      <Project>{2BD2C24E-DBF9-3A53-A8DD-A740494404A9}</Project>
      <Name>opencv_ts</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\apps\version\opencv_version.vcxproj">
      <Project>{888B3324-82B7-345A-8353-EBD8C9E14A63}</Project>
      <Name>opencv_version</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\apps\version\opencv_version_win32.vcxproj">
      <Project>{9DFC93C1-6329-3BD2-86AB-0373060A8C64}</Project>
      <Name>opencv_version_win32</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\.firstpass\videoio\opencv_videoio_plugins.vcxproj">
      <Project>{1EC2325E-EB72-3BD4-9EFE-34C36DE815C1}</Project>
      <Name>opencv_videoio_plugins</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\apps\visualisation\opencv_visualisation.vcxproj">
      <Project>{771B8076-B2AF-3CCF-A1A8-28AE52263B3D}</Project>
      <Name>opencv_visualisation</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\tools\waldboost_detector\opencv_waldboost_detector.vcxproj">
      <Project>{E0EAF782-AD40-3CE7-874F-2E5C16244394}</Project>
      <Name>opencv_waldboost_detector</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_world.vcxproj">
      <Project>{3FB93847-4239-3F8E-9388-4D0E6344BFB7}</Project>
      <Name>opencv_world</Name>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_world_AVX.vcxproj">
      <Project>{2DFB40C2-66E5-3DF8-A870-5534B7405BE8}</Project>
      <Name>opencv_world_AVX</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_world_AVX2.vcxproj">
      <Project>{2351632C-1939-3FA6-B0E9-01031BA67218}</Project>
      <Name>opencv_world_AVX2</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_world_AVX512_SKX.vcxproj">
      <Project>{002FA6DE-E9DD-3CB1-A1A4-DB7632471D54}</Project>
      <Name>opencv_world_AVX512_SKX</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_world_SSE4_1.vcxproj">
      <Project>{7B7A4E81-3E2A-36A7-BD7C-57CFC8064160}</Project>
      <Name>opencv_world_SSE4_1</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\AI\opencv\cudabuild\modules\world\opencv_world_SSE4_2.vcxproj">
      <Project>{C19F6956-ED5B-3ED2-80E4-673A0791E120}</Project>
      <Name>opencv_world_SSE4_2</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>