// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Graphics_Printing_ProtectedPrint_H
#define WINRT_Windows_Graphics_Printing_ProtectedPrint_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.240405.15"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.240405.15"
#include "winrt/Windows.Graphics.Printing.h"
#include "winrt/impl/Windows.Graphics.Printing.ProtectedPrint.2.h"
namespace winrt::impl
{
    template <typename D> auto consume_Windows_Graphics_Printing_ProtectedPrint_IWindowsProtectedPrintInfoStatics<D>::IsProtectedPrintEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Graphics::Printing::ProtectedPrint::IWindowsProtectedPrintInfoStatics)->get_IsProtectedPrintEnabled(&value));
        return value;
    }
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Graphics::Printing::ProtectedPrint::IWindowsProtectedPrintInfoStatics> : produce_base<D, winrt::Windows::Graphics::Printing::ProtectedPrint::IWindowsProtectedPrintInfoStatics>
    {
        int32_t __stdcall get_IsProtectedPrintEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsProtectedPrintEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
}
WINRT_EXPORT namespace winrt::Windows::Graphics::Printing::ProtectedPrint
{
    inline auto WindowsProtectedPrintInfo::IsProtectedPrintEnabled()
    {
        return impl::call_factory_cast<bool(*)(IWindowsProtectedPrintInfoStatics const&), WindowsProtectedPrintInfo, IWindowsProtectedPrintInfoStatics>([](IWindowsProtectedPrintInfoStatics const& f) { return f.IsProtectedPrintEnabled(); });
    }
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
    template<> struct hash<winrt::Windows::Graphics::Printing::ProtectedPrint::IWindowsProtectedPrintInfoStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Graphics::Printing::ProtectedPrint::WindowsProtectedPrintInfo> : winrt::impl::hash_base {};
#endif
#ifdef __cpp_lib_format
#endif
}
#endif
