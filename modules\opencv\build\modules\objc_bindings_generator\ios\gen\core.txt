PORTED FUNCs LIST (146 of 149):

 float cv::cubeRoot(float val)
 float cv::fastAtan2(float y, float x)
 bool cv::ipp::useIPP()
 void cv::ipp::setUseIPP(bool flag)
 String cv::ipp::getIppVersion()
 bool cv::ipp::useIPP_NotExact()
 void cv::ipp::setUseIPP_NotExact(bool flag)
 int cv::borderInterpolate(int p, int len, BorderTypes borderType)
 void cv::copyMakeBorder(Mat src, Mat& dst, int top, int bottom, int left, int right, BorderTypes borderType, Scalar value = Scalar())
 void cv::add(Mat src1, Mat src2, Mat& dst, Mat mask = Mat(), int dtype = -1)
 void cv::subtract(Mat src1, Mat src2, Mat& dst, Mat mask = Mat(), int dtype = -1)
 void cv::multiply(Mat src1, <PERSON> src2, Mat& dst, double scale = 1, int dtype = -1)
 void cv::divide(Mat src1, Mat src2, Mat& dst, double scale = 1, int dtype = -1)
 void cv::divide(double scale, Mat src, Mat& dst, int dtype = -1)
 void cv::scaleAdd(Mat src1, double alpha, Mat src2, Mat& dst)
 void cv::addWeighted(Mat src1, double alpha, Mat src2, double beta, double gamma, Mat& dst, int dtype = -1)
 void cv::convertScaleAbs(Mat src, Mat& dst, double alpha = 1, double beta = 0)
 void cv::convertFp16(Mat src, Mat& dst)
 void cv::LUT(Mat src, Mat lut, Mat& dst)
 Scalar cv::sum(Mat src)
 bool cv::hasNonZero(Mat src)
 int cv::countNonZero(Mat src)
 void cv::findNonZero(Mat src, Mat& idx)
 Scalar cv::mean(Mat src, Mat mask = Mat())
 void cv::meanStdDev(Mat src, vector_double& mean, vector_double& stddev, Mat mask = Mat())
 double cv::norm(Mat src1, NormTypes normType = NORM_L2, Mat mask = Mat())
 double cv::norm(Mat src1, Mat src2, NormTypes normType = NORM_L2, Mat mask = Mat())
 double cv::PSNR(Mat src1, Mat src2, double R = 255.)
 void cv::batchDistance(Mat src1, Mat src2, Mat& dist, int dtype, Mat& nidx, NormTypes normType = NORM_L2, int K = 0, Mat mask = Mat(), int update = 0, bool crosscheck = false)
 void cv::normalize(Mat src, Mat& dst, double alpha = 1, double beta = 0, NormTypes norm_type = NORM_L2, int dtype = -1, Mat mask = Mat())
 void cv::reduceArgMin(Mat src, Mat& dst, int axis, bool lastIndex = false)
 void cv::reduceArgMax(Mat src, Mat& dst, int axis, bool lastIndex = false)
 void cv::reduce(Mat src, Mat& dst, int dim, int rtype, int dtype = -1)
 void cv::merge(vector_Mat mv, Mat& dst)
 void cv::split(Mat m, vector_Mat& mv)
 void cv::mixChannels(vector_Mat src, vector_Mat dst, vector_int fromTo)
 void cv::extractChannel(Mat src, Mat& dst, int coi)
 void cv::insertChannel(Mat src, Mat& dst, int coi)
 void cv::flip(Mat src, Mat& dst, int flipCode)
 void cv::flipND(Mat src, Mat& dst, int axis)
 void cv::broadcast(Mat src, Mat shape, Mat& dst)
 void cv::rotate(Mat src, Mat& dst, RotateFlags rotateCode)
 void cv::repeat(Mat src, int ny, int nx, Mat& dst)
 void cv::hconcat(vector_Mat src, Mat& dst)
 void cv::vconcat(vector_Mat src, Mat& dst)
 void cv::bitwise_and(Mat src1, Mat src2, Mat& dst, Mat mask = Mat())
 void cv::bitwise_or(Mat src1, Mat src2, Mat& dst, Mat mask = Mat())
 void cv::bitwise_xor(Mat src1, Mat src2, Mat& dst, Mat mask = Mat())
 void cv::bitwise_not(Mat src, Mat& dst, Mat mask = Mat())
 void cv::absdiff(Mat src1, Mat src2, Mat& dst)
 void cv::copyTo(Mat src, Mat& dst, Mat mask)
 void cv::inRange(Mat src, Scalar lowerb, Scalar upperb, Mat& dst)
 void cv::compare(Mat src1, Mat src2, Mat& dst, CmpTypes cmpop)
 void cv::min(Mat src1, Mat src2, Mat& dst)
 void cv::max(Mat src1, Mat src2, Mat& dst)
 void cv::sqrt(Mat src, Mat& dst)
 void cv::pow(Mat src, double power, Mat& dst)
 void cv::exp(Mat src, Mat& dst)
 void cv::log(Mat src, Mat& dst)
 void cv::polarToCart(Mat magnitude, Mat angle, Mat& x, Mat& y, bool angleInDegrees = false)
 void cv::cartToPolar(Mat x, Mat y, Mat& magnitude, Mat& angle, bool angleInDegrees = false)
 void cv::phase(Mat x, Mat y, Mat& angle, bool angleInDegrees = false)
 void cv::magnitude(Mat x, Mat y, Mat& magnitude)
 bool cv::checkRange(Mat a, bool quiet = true,  _hidden_ * pos = 0, double minVal = -DBL_MAX, double maxVal = DBL_MAX)
 void cv::patchNaNs(Mat& a, double val = 0)
 void cv::gemm(Mat src1, Mat src2, double alpha, Mat src3, double beta, Mat& dst, int flags = 0)
 void cv::mulTransposed(Mat src, Mat& dst, bool aTa, Mat delta = Mat(), double scale = 1, int dtype = -1)
 void cv::transpose(Mat src, Mat& dst)
 void cv::transposeND(Mat src, vector_int order, Mat& dst)
 void cv::transform(Mat src, Mat& dst, Mat m)
 void cv::perspectiveTransform(Mat src, Mat& dst, Mat m)
 void cv::completeSymm(Mat& m, bool lowerToUpper = false)
 void cv::setIdentity(Mat& mtx, Scalar s = Scalar(1))
 double cv::determinant(Mat mtx)
 Scalar cv::trace(Mat mtx)
 double cv::invert(Mat src, Mat& dst, int flags = DECOMP_LU)
 bool cv::solve(Mat src1, Mat src2, Mat& dst, int flags = DECOMP_LU)
 void cv::sort(Mat src, Mat& dst, int flags)
 void cv::sortIdx(Mat src, Mat& dst, int flags)
 int cv::solveCubic(Mat coeffs, Mat& roots)
 double cv::solvePoly(Mat coeffs, Mat& roots, int maxIters = 300)
 bool cv::eigen(Mat src, Mat& eigenvalues, Mat& eigenvectors = Mat())
 void cv::eigenNonSymmetric(Mat src, Mat& eigenvalues, Mat& eigenvectors)
 void cv::calcCovarMatrix(Mat samples, Mat& covar, Mat& mean, int flags, int ctype = CV_64F)
 void cv::PCACompute(Mat data, Mat& mean, Mat& eigenvectors, int maxComponents = 0)
 void cv::PCACompute(Mat data, Mat& mean, Mat& eigenvectors, Mat& eigenvalues, int maxComponents = 0)
 void cv::PCACompute(Mat data, Mat& mean, Mat& eigenvectors, double retainedVariance)
 void cv::PCACompute(Mat data, Mat& mean, Mat& eigenvectors, Mat& eigenvalues, double retainedVariance)
 void cv::PCAProject(Mat data, Mat mean, Mat eigenvectors, Mat& result)
 void cv::PCABackProject(Mat data, Mat mean, Mat eigenvectors, Mat& result)
 void cv::SVDecomp(Mat src, Mat& w, Mat& u, Mat& vt, int flags = 0)
 void cv::SVBackSubst(Mat w, Mat u, Mat vt, Mat rhs, Mat& dst)
 double cv::Mahalanobis(Mat v1, Mat v2, Mat icovar)
 void cv::dft(Mat src, Mat& dst, int flags = 0, int nonzeroRows = 0)
 void cv::idft(Mat src, Mat& dst, int flags = 0, int nonzeroRows = 0)
 void cv::dct(Mat src, Mat& dst, int flags = 0)
 void cv::idct(Mat src, Mat& dst, int flags = 0)
 void cv::mulSpectrums(Mat a, Mat b, Mat& c, int flags, bool conjB = false)
 int cv::getOptimalDFTSize(int vecsize)
 void cv::setRNGSeed(int seed)
 void cv::randu(Mat& dst, double low, double high)
 void cv::randn(Mat& dst, double mean, double stddev)
 void cv::randShuffle(Mat& dst, double iterFactor = 1.,  _hidden_ * rng = 0)
 double cv::kmeans(Mat data, int K, Mat& bestLabels, TermCriteria criteria, int attempts, int flags, Mat& centers = Mat())
 void cv::setNumThreads(int nthreads)
 int cv::getNumThreads()
 int cv::getThreadNum()
 String cv::getBuildInformation()
 String cv::getVersionString()
 int cv::getVersionMajor()
 int cv::getVersionMinor()
 int cv::getVersionRevision()
 int64 cv::getTickCount()
 double cv::getTickFrequency()
 int64 cv::getCPUTickCount()
 String cv::getHardwareFeatureName(int feature)
 string cv::getCPUFeaturesLine()
 int cv::getNumberOfCPUs()
 String cv::samples::findFile(String relative_path, bool required = true, bool silentMode = false)
 String cv::samples::findFileOrKeep(String relative_path, bool silentMode = false)
 void cv::samples::addSamplesDataSearchPath(String path)
 void cv::samples::addSamplesDataSearchSubDirectory(String subdir)
 void cv::add(Mat src1, Scalar srcScalar, Mat& dst, Mat mask = Mat(), int dtype = -1)
 void cv::subtract(Mat src1, Scalar srcScalar, Mat& dst, Mat mask = Mat(), int dtype = -1)
 void cv::multiply(Mat src1, Scalar srcScalar, Mat& dst, double scale = 1, int dtype = -1)
 void cv::divide(Mat src1, Scalar srcScalar, Mat& dst, double scale = 1, int dtype = -1)
 void cv::absdiff(Mat src1, Scalar srcScalar, Mat& dst)
 void cv::compare(Mat src1, Scalar srcScalar, Mat& dst, CmpTypes cmpop)
 void cv::min(Mat src1, Scalar srcScalar, Mat& dst)
 void cv::max(Mat src1, Scalar srcScalar, Mat& dst)
 void cv::Algorithm::clear()
 bool cv::Algorithm::empty()
 void cv::Algorithm::save(String filename)
 String cv::Algorithm::getDefaultName()
  cv::TickMeter::TickMeter()
 void cv::TickMeter::start()
 void cv::TickMeter::stop()
 int64 cv::TickMeter::getTimeTicks()
 double cv::TickMeter::getTimeMicro()
 double cv::TickMeter::getTimeMilli()
 double cv::TickMeter::getTimeSec()
 int64 cv::TickMeter::getCounter()
 double cv::TickMeter::getFPS()
 double cv::TickMeter::getAvgTimeSec()
 double cv::TickMeter::getAvgTimeMilli()
 void cv::TickMeter::reset()

SKIPPED FUNCs LIST (3 of 149):

 void cv::Algorithm::write(FileStorage fs)
// Unknown type 'FileStorage' (I), skipping the function

 void cv::Algorithm::write(FileStorage fs, String name)
// Unknown type 'FileStorage' (I), skipping the function

 void cv::Algorithm::read(FileNode fn)
// Unknown type 'FileNode' (I), skipping the function


0 def args - 97 funcs
1 def args - 33 funcs
2 def args - 15 funcs
3 def args - 1 funcs
4 def args - 1 funcs
5 def args - 2 funcs