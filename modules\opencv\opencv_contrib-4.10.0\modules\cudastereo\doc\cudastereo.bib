@InProceedings{<PERSON><PERSON><PERSON>2013,
  author = {<PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON>, Ra{\'u}l},
  title = {Weighted Semi-Global Matching and Center-Symmetric Census Transform for Robust Driver Assistance},
  booktitle = {Computer Analysis of Images and Patterns},
  year = {2013},
  pages = {34--41},
  publisher = {Springer Berlin Heidelberg},
  abstract = {Automotive applications based on stereo vision require robust and fast matching algorithms, which makes semi-global matching (SGM) a popular method in this field. Typically the Census transform is used as a cost function, since it is advantageous for outdoor scenes. We propose an extension based on center-symmetric local binary patterns, which allows better efficiency and higher matching quality. Our second contribution exploits knowledge about the three-dimensional structure of the scene to selectively enforce the smoothness constraints of SGM. It is shown that information about surface normals can be easily integrated by weighing the paths according to the gradient of the disparity. The different approaches are evaluated on the KITTI benchmark, which provides real imagery with LIDAR ground truth. The results indicate improved performance compared to state-of-the-art SGM based algorithms.},
  url = {https://www.mi.fu-berlin.de/inf/groups/ag-ki/publications/Semi-Global_Matching/caip2013rsp_fu.pdf}
}
