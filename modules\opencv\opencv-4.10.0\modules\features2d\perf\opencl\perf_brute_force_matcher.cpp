/*M///////////////////////////////////////////////////////////////////////////////////////
//
//  IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
//
//  By downloading, copying, installing or using the software you agree to this license.
//  If you do not agree to this license, do not download, install,
//  copy or use the software.
//
//
//                           License Agreement
//                For Open Source Computer Vision Library
//
// Copyright (C) 2010-2012, Multicoreware, Inc., all rights reserved.
// Copyright (C) 2010-2012, Advanced Micro Devices, Inc., all rights reserved.
// Third party copyrights are property of their respective owners.
//
// @Authors
//    Fangfang Bai, <EMAIL>
//    <PERSON>,       <EMAIL>
//
// Redistribution and use in source and binary forms, with or without modification,
// are permitted provided that the following conditions are met:
//
//   * Redistribution's of source code must retain the above copyright notice,
//     this list of conditions and the following disclaimer.
//
//   * Redistribution's in binary form must reproduce the above copyright notice,
//     this list of conditions and the following disclaimer in the documentation
//     and/or other materials provided with the distribution.
//
//   * The name of the copyright holders may not be used to endorse or promote products
//     derived from this software without specific prior written permission.
//
// This software is provided by the copyright holders and contributors as is and
// any express or implied warranties, including, but not limited to, the implied
// warranties of merchantability and fitness for a particular purpose are disclaimed.
// In no event shall the Intel Corporation or contributors be liable for any direct,
// indirect, incidental, special, exemplary, or consequential damages
// (including, but not limited to, procurement of substitute goods or services;
// loss of use, data, or profits; or business interruption) however caused
// and on any theory of liability, whether in contract, strict liability,
// or tort (including negligence or otherwise) arising in any way out of
// the use of this software, even if advised of the possibility of such damage.
//
//M*/
#include "../perf_precomp.hpp"
#include "opencv2/ts/ocl_perf.hpp"

#ifdef HAVE_OPENCL

namespace opencv_test {
namespace ocl {

//////////////////// BruteForceMatch /////////////////

typedef Size_MatType BruteForceMatcherFixture;

OCL_PERF_TEST_P(BruteForceMatcherFixture, Match, ::testing::Combine(OCL_PERF_ENUM(OCL_SIZE_1, OCL_SIZE_2, OCL_SIZE_3), OCL_PERF_ENUM((MatType)CV_32FC1) ) )
{
    const Size_MatType_t params = GetParam();
    const Size srcSize = get<0>(params);
    const int type = get<1>(params);

    checkDeviceMaxMemoryAllocSize(srcSize, type);

    vector<DMatch> matches;
    UMat uquery(srcSize, type), utrain(srcSize, type);

    declare.in(uquery, utrain, WARMUP_RNG);

    BFMatcher matcher(NORM_L2);

    OCL_TEST_CYCLE()
        matcher.match(uquery, utrain, matches);

    SANITY_CHECK_MATCHES(matches, 1e-3);
}

OCL_PERF_TEST_P(BruteForceMatcherFixture, KnnMatch, ::testing::Combine(OCL_PERF_ENUM(OCL_SIZE_1, OCL_SIZE_2, OCL_SIZE_3), OCL_PERF_ENUM((MatType)CV_32FC1) ) )
{
    const Size_MatType_t params = GetParam();
    const Size srcSize = get<0>(params);
    const int type = get<1>(params);

    checkDeviceMaxMemoryAllocSize(srcSize, type);

    vector< vector<DMatch> > matches;
    UMat uquery(srcSize, type), utrain(srcSize, type);

    declare.in(uquery, utrain, WARMUP_RNG);

    BFMatcher matcher(NORM_L2);

    OCL_TEST_CYCLE()
        matcher.knnMatch(uquery, utrain, matches, 2);

    vector<DMatch> & matches0 = matches[0], & matches1 = matches[1];
    SANITY_CHECK_MATCHES(matches0, 1e-3);
    SANITY_CHECK_MATCHES(matches1, 1e-3);

}

OCL_PERF_TEST_P(BruteForceMatcherFixture, RadiusMatch, ::testing::Combine(OCL_PERF_ENUM(OCL_SIZE_1, OCL_SIZE_2, OCL_SIZE_3), OCL_PERF_ENUM((MatType)CV_32FC1) ) )
{
    const Size_MatType_t params = GetParam();
    const Size srcSize = get<0>(params);
    const int type = get<1>(params);

    checkDeviceMaxMemoryAllocSize(srcSize, type);

    vector< vector<DMatch> > matches;
    UMat uquery(srcSize, type), utrain(srcSize, type);

    declare.in(uquery, utrain, WARMUP_RNG);

    BFMatcher matcher(NORM_L2);

    OCL_TEST_CYCLE()
        matcher.radiusMatch(uquery, utrain, matches, 2.0f);

    vector<DMatch> & matches0 = matches[0], & matches1 = matches[1];
    SANITY_CHECK_MATCHES(matches0, 1e-3);
    SANITY_CHECK_MATCHES(matches1, 1e-3);
}

} // ocl
} // cvtest

#endif // HAVE_OPENCL
