// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Gaming_Preview_GamesEnumeration_2_H
#define WINRT_Windows_Gaming_Preview_GamesEnumeration_2_H
#include "winrt/impl/Windows.Foundation.1.h"
#include "winrt/impl/Windows.Gaming.Preview.GamesEnumeration.1.h"
WINRT_EXPORT namespace winrt::Windows::Gaming::Preview::GamesEnumeration
{
    struct GameListChangedEventHandler : winrt::Windows::Foundation::IUnknown
    {
        GameListChangedEventHandler(std::nullptr_t = nullptr) noexcept {}
        GameListChangedEventHandler(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> GameListChangedEventHandler(L lambda);
        template <typename F> GameListChangedEventHandler(F* function);
        template <typename O, typename M> GameListChangedEventHandler(O* object, M method);
        template <typename O, typename M> GameListChangedEventHandler(com_ptr<O>&& object, M method);
        template <typename O, typename LM> GameListChangedEventHandler(weak_ref<O>&& object, LM&& lambda_or_method);
        template <typename O, typename M> GameListChangedEventHandler(std::shared_ptr<O>&& object, M method);
        template <typename O, typename LM> GameListChangedEventHandler(std::weak_ptr<O>&& object, LM&& lambda_or_method);
        auto operator()(winrt::Windows::Gaming::Preview::GamesEnumeration::GameListEntry const& game) const;
    };
    struct GameListRemovedEventHandler : winrt::Windows::Foundation::IUnknown
    {
        GameListRemovedEventHandler(std::nullptr_t = nullptr) noexcept {}
        GameListRemovedEventHandler(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> GameListRemovedEventHandler(L lambda);
        template <typename F> GameListRemovedEventHandler(F* function);
        template <typename O, typename M> GameListRemovedEventHandler(O* object, M method);
        template <typename O, typename M> GameListRemovedEventHandler(com_ptr<O>&& object, M method);
        template <typename O, typename LM> GameListRemovedEventHandler(weak_ref<O>&& object, LM&& lambda_or_method);
        template <typename O, typename M> GameListRemovedEventHandler(std::shared_ptr<O>&& object, M method);
        template <typename O, typename LM> GameListRemovedEventHandler(std::weak_ptr<O>&& object, LM&& lambda_or_method);
        auto operator()(param::hstring const& identifier) const;
    };
    struct GameList
    {
        GameList() = delete;
        static auto FindAllAsync();
        static auto FindAllAsync(param::hstring const& packageFamilyName);
        static auto GameAdded(winrt::Windows::Gaming::Preview::GamesEnumeration::GameListChangedEventHandler const& handler);
        using GameAdded_revoker = impl::factory_event_revoker<winrt::Windows::Gaming::Preview::GamesEnumeration::IGameListStatics, &impl::abi_t<winrt::Windows::Gaming::Preview::GamesEnumeration::IGameListStatics>::remove_GameAdded>;
        [[nodiscard]] static auto GameAdded(auto_revoke_t, winrt::Windows::Gaming::Preview::GamesEnumeration::GameListChangedEventHandler const& handler);
        static auto GameAdded(winrt::event_token const& token);
        static auto GameRemoved(winrt::Windows::Gaming::Preview::GamesEnumeration::GameListRemovedEventHandler const& handler);
        using GameRemoved_revoker = impl::factory_event_revoker<winrt::Windows::Gaming::Preview::GamesEnumeration::IGameListStatics, &impl::abi_t<winrt::Windows::Gaming::Preview::GamesEnumeration::IGameListStatics>::remove_GameRemoved>;
        [[nodiscard]] static auto GameRemoved(auto_revoke_t, winrt::Windows::Gaming::Preview::GamesEnumeration::GameListRemovedEventHandler const& handler);
        static auto GameRemoved(winrt::event_token const& token);
        static auto GameUpdated(winrt::Windows::Gaming::Preview::GamesEnumeration::GameListChangedEventHandler const& handler);
        using GameUpdated_revoker = impl::factory_event_revoker<winrt::Windows::Gaming::Preview::GamesEnumeration::IGameListStatics, &impl::abi_t<winrt::Windows::Gaming::Preview::GamesEnumeration::IGameListStatics>::remove_GameUpdated>;
        [[nodiscard]] static auto GameUpdated(auto_revoke_t, winrt::Windows::Gaming::Preview::GamesEnumeration::GameListChangedEventHandler const& handler);
        static auto GameUpdated(winrt::event_token const& token);
        static auto MergeEntriesAsync(winrt::Windows::Gaming::Preview::GamesEnumeration::GameListEntry const& left, winrt::Windows::Gaming::Preview::GamesEnumeration::GameListEntry const& right);
        static auto UnmergeEntryAsync(winrt::Windows::Gaming::Preview::GamesEnumeration::GameListEntry const& mergedEntry);
    };
    struct WINRT_IMPL_EMPTY_BASES GameListEntry : winrt::Windows::Gaming::Preview::GamesEnumeration::IGameListEntry,
        impl::require<GameListEntry, winrt::Windows::Gaming::Preview::GamesEnumeration::IGameListEntry2>
    {
        GameListEntry(std::nullptr_t) noexcept {}
        GameListEntry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Gaming::Preview::GamesEnumeration::IGameListEntry(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES GameModeConfiguration : winrt::Windows::Gaming::Preview::GamesEnumeration::IGameModeConfiguration
    {
        GameModeConfiguration(std::nullptr_t) noexcept {}
        GameModeConfiguration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Gaming::Preview::GamesEnumeration::IGameModeConfiguration(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES GameModeUserConfiguration : winrt::Windows::Gaming::Preview::GamesEnumeration::IGameModeUserConfiguration
    {
        GameModeUserConfiguration(std::nullptr_t) noexcept {}
        GameModeUserConfiguration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Gaming::Preview::GamesEnumeration::IGameModeUserConfiguration(ptr, take_ownership_from_abi) {}
        static auto GetDefault();
    };
}
#endif
