set(MODU<PERSON>_NAME "js_bindings_generator")
set(OPENCV_MODULE_IS_PART_OF_WORLD FALSE)
ocv_add_module(${MODULE_NAME} INTERNAL)

set(OPENCV_JS_BINDINGS_DIR "${CMAKE_CURRENT_BINARY_DIR}" CACHE INTERNAL "")
file(REMOVE_RECURSE "${OPENCV_JS_BINDINGS_DIR}/gen")
file(MAKE_DIRECTORY "${OPENCV_JS_BINDINGS_DIR}/gen")
file(REMOVE "${OPENCV_DEPHELPER}/gen_opencv_js_source")  # force re-run after CMake

# This file is included from a subdirectory
set(JS_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/..")
include(${JS_SOURCE_DIR}/common.cmake)  # fill OPENCV_JS_MODULES

set(opencv_hdrs "")
foreach(m ${OPENCV_JS_MODULES})
  list(APPEND opencv_hdrs ${OPENCV_MODULE_${m}_HEADERS})
endforeach(m)

# header blacklist
ocv_list_filterout(opencv_hdrs "modules/.*.h$")
ocv_list_filterout(opencv_hdrs "modules/core/.*/cuda")
ocv_list_filterout(opencv_hdrs "modules/core/.*/opencl")
ocv_list_filterout(opencv_hdrs "modules/core/include/opencv2/core/opengl.hpp")
ocv_list_filterout(opencv_hdrs "modules/core/include/opencv2/core/ocl.hpp")
ocv_list_filterout(opencv_hdrs "modules/cuda.*")
ocv_list_filterout(opencv_hdrs "modules/cudev")
ocv_list_filterout(opencv_hdrs "modules/core/.*/hal/")
ocv_list_filterout(opencv_hdrs "modules/.*/detection_based_tracker.hpp") # Conditional compilation
ocv_list_filterout(opencv_hdrs "modules/core/include/opencv2/core/utils/*.private.*")
ocv_list_filterout(opencv_hdrs "modules/core/include/opencv2/core/utils/instrumentation.hpp")
ocv_list_filterout(opencv_hdrs "modules/core/include/opencv2/core/utils/trace*")

ocv_update_file("${CMAKE_CURRENT_BINARY_DIR}/headers.txt" "${opencv_hdrs}")

set(bindings_cpp "${OPENCV_JS_BINDINGS_DIR}/gen/bindings.cpp")

set(scripts_hdr_parser "${JS_SOURCE_DIR}/../python/src2/hdr_parser.py")

if(DEFINED ENV{OPENCV_JS_WHITELIST})
  set(OPENCV_JS_WHITELIST_FILE "$ENV{OPENCV_JS_WHITELIST}")
else()
  set(OPENCV_JS_WHITELIST_FILE "${OpenCV_SOURCE_DIR}/platforms/js/opencv_js.config.py")
endif()

add_custom_command(
  OUTPUT ${bindings_cpp} "${OPENCV_DEPHELPER}/gen_opencv_js_source"
  COMMAND
      ${PYTHON_DEFAULT_EXECUTABLE}
      "${CMAKE_CURRENT_SOURCE_DIR}/embindgen.py"
      "${scripts_hdr_parser}"
      "${bindings_cpp}"
      "${CMAKE_CURRENT_BINARY_DIR}/headers.txt"
      "${JS_SOURCE_DIR}/src/core_bindings.cpp"
      "${OPENCV_JS_WHITELIST_FILE}"
  COMMAND
      ${CMAKE_COMMAND} -E touch "${OPENCV_DEPHELPER}/gen_opencv_js_source"
  WORKING_DIRECTORY
      "${CMAKE_CURRENT_BINARY_DIR}/gen"
  DEPENDS
      ${JS_SOURCE_DIR}/src/core_bindings.cpp
      ${CMAKE_CURRENT_SOURCE_DIR}/embindgen.py
      ${CMAKE_CURRENT_SOURCE_DIR}/templates.py
      "${OPENCV_JS_WHITELIST_FILE}"
      ${scripts_hdr_parser}
      #(not needed - generated by CMake) ${CMAKE_CURRENT_BINARY_DIR}/headers.txt
      ${opencv_hdrs}
  COMMENT "Generate source files for JavaScript bindings"
)

add_custom_target(gen_opencv_js_source
  # excluded from all: ALL
  DEPENDS ${bindings_cpp} "${OPENCV_DEPHELPER}/gen_opencv_js_source"
  SOURCES
      ${JS_SOURCE_DIR}/src/core_bindings.cpp
      ${CMAKE_CURRENT_SOURCE_DIR}/embindgen.py
      ${CMAKE_CURRENT_SOURCE_DIR}/templates.py
)
