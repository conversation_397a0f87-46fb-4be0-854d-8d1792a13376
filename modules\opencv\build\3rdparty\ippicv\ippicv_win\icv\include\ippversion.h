/* 
// Copyright 2001 Intel Corporation All Rights Reserved.
// 
// 
// This software and the related documents are Intel copyrighted materials, and your use of them is governed by
// the express license under which they were provided to you ('License'). Unless the License provides otherwise,
// you may not use, modify, copy, publish, distribute, disclose or transmit this software or the related
// documents without Intel's prior written permission.
// This software and the related documents are provided as is, with no express or implied warranties, other than
// those that are expressly stated in the License.
// 
*/

/* 
//              Intel(R) Integrated Performance Primitives (Intel(R) IPP)
//              Purpose: Describes the Intel(R) IPP version
// 
// 
*/


#if !defined( IPPVERSION_H__ )
#define IPPVERSION_H__

#define IPP_VERSION_MAJOR  2021
#define IPP_VERSION_MINOR  11
#define IPP_VERSION_UPDATE 0

#define IPP_VERSION_STR "2021.11.0"

// Major and minor interface version
#define IPP_INTERFACE_VERSION_MAJOR 10
#define IPP_INTERFACE_VERSION_MINOR 10

#endif /* IPPVERSION_H__ */
