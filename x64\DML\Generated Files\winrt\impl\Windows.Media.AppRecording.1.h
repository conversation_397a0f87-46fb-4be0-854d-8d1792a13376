// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Media_AppRecording_1_H
#define WINRT_Windows_Media_AppRecording_1_H
#include "winrt/impl/Windows.Media.AppRecording.0.h"
WINRT_EXPORT namespace winrt::Windows::Media::AppRecording
{
    struct WINRT_IMPL_EMPTY_BASES IAppRecordingManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppRecordingManager>
    {
        IAppRecordingManager(std::nullptr_t = nullptr) noexcept {}
        IAppRecordingManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppRecordingManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppRecordingManagerStatics>
    {
        IAppRecordingManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IAppRecordingManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppRecordingResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppRecordingResult>
    {
        IAppRecordingResult(std::nullptr_t = nullptr) noexcept {}
        IAppRecordingResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppRecordingSaveScreenshotResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppRecordingSaveScreenshotResult>
    {
        IAppRecordingSaveScreenshotResult(std::nullptr_t = nullptr) noexcept {}
        IAppRecordingSaveScreenshotResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppRecordingSavedScreenshotInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppRecordingSavedScreenshotInfo>
    {
        IAppRecordingSavedScreenshotInfo(std::nullptr_t = nullptr) noexcept {}
        IAppRecordingSavedScreenshotInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppRecordingStatus :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppRecordingStatus>
    {
        IAppRecordingStatus(std::nullptr_t = nullptr) noexcept {}
        IAppRecordingStatus(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppRecordingStatusDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppRecordingStatusDetails>
    {
        IAppRecordingStatusDetails(std::nullptr_t = nullptr) noexcept {}
        IAppRecordingStatusDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
