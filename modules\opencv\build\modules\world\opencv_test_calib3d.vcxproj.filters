﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\opencl\test_stereobm.cpp">
      <Filter>opencv_calib3d\Src\opencl</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_affine2d_estimator.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_affine3.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_affine3d_estimator.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_affine_partial2d_estimator.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_calibration_hand_eye.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_cameracalibration.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_cameracalibration_artificial.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_cameracalibration_badarg.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_cameracalibration_tilt.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_chessboardgenerator.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_chesscorners.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_chesscorners_badarg.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_chesscorners_timing.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_compose_rt.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_cornerssubpix.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_decompose_projection.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_filter_homography_decomp.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_fisheye.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_fundam.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_homography.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_homography_decomp.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_main.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_modelest.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_posit.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_reproject_image_to_3d.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_solvepnp_ransac.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_stereomatching.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_translation3d_estimator.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_undistort.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_undistort_badarg.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_undistort_points.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
    <ClCompile Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_usac.cpp">
      <Filter>opencv_calib3d\Src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_chessboardgenerator.hpp">
      <Filter>opencv_calib3d\Include</Filter>
    </ClInclude>
    <ClInclude Include="D:\AI\opencv\opencv-4.10.0\modules\calib3d\test\test_precomp.hpp">
      <Filter>opencv_calib3d\Include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="opencv_calib3d">
      <UniqueIdentifier>{5793AB40-0443-396B-B27D-D8B999CC6F98}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_calib3d\Include">
      <UniqueIdentifier>{93EA1108-B0BB-3337-AD6C-DF6B8AA65C55}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_calib3d\Src">
      <UniqueIdentifier>{E6EEE02A-D60F-3044-B61D-DEAC04AF6489}</UniqueIdentifier>
    </Filter>
    <Filter Include="opencv_calib3d\Src\opencl">
      <UniqueIdentifier>{54AD7CCA-4A11-3F74-A288-A57565481BFC}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
