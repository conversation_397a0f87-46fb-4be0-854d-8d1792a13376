// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_AI_Actions_Provider_H
#define WINRT_Windows_AI_Actions_Provider_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.240405.15"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.240405.15"
#include "winrt/Windows.AI.Actions.h"
#include "winrt/impl/Windows.AI.Actions.2.h"
#include "winrt/impl/Windows.Foundation.2.h"
#include "winrt/impl/Windows.AI.Actions.Provider.2.h"
namespace winrt::impl
{
    template <typename D> auto consume_Windows_AI_Actions_Provider_IActionFeedbackHandler<D>::ProcessFeedbackAsync(winrt::Windows::AI::Actions::ActionInvocationContext const& context, winrt::Windows::AI::Actions::ActionFeedback const& feedback) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Provider::IActionFeedbackHandler)->ProcessFeedbackAsync(*(void**)(&context), *(void**)(&feedback), &operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_AI_Actions_Provider_IActionProvider<D>::InvokeAsync(winrt::Windows::AI::Actions::ActionInvocationContext const& context) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::AI::Actions::Provider::IActionProvider)->InvokeAsync(*(void**)(&context), &operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::Provider::IActionFeedbackHandler> : produce_base<D, winrt::Windows::AI::Actions::Provider::IActionFeedbackHandler>
    {
        int32_t __stdcall ProcessFeedbackAsync(void* context, void* feedback, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().ProcessFeedbackAsync(*reinterpret_cast<winrt::Windows::AI::Actions::ActionInvocationContext const*>(&context), *reinterpret_cast<winrt::Windows::AI::Actions::ActionFeedback const*>(&feedback)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Windows::AI::Actions::Provider::IActionProvider> : produce_base<D, winrt::Windows::AI::Actions::Provider::IActionProvider>
    {
        int32_t __stdcall InvokeAsync(void* context, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().InvokeAsync(*reinterpret_cast<winrt::Windows::AI::Actions::ActionInvocationContext const*>(&context)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
}
WINRT_EXPORT namespace winrt::Windows::AI::Actions::Provider
{
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
    template<> struct hash<winrt::Windows::AI::Actions::Provider::IActionFeedbackHandler> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::AI::Actions::Provider::IActionProvider> : winrt::impl::hash_base {};
#endif
#ifdef __cpp_lib_format
#endif
}
#endif
