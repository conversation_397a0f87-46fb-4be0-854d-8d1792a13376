//
// This file is auto-generated. Please don't modify it!
//
#pragma once

#ifdef __cplusplus
//#import "opencv.hpp"
#import "opencv2/structured_light.hpp"
#else
#define CV_EXPORTS
#endif

#import <Foundation/Foundation.h>





NS_ASSUME_NONNULL_BEGIN

// C++: class Structured_light
/**
 * The Structured_light module
 *
 * Member classes: `GrayCodePattern`, `SinusoidalPattern`, `SinusoidalPatternParams`, `StructuredLightPattern`
 *
 */
CV_EXPORTS @interface Structured_light : NSObject

#pragma mark - Class Constants


@property (class, readonly) int FTP NS_SWIFT_NAME(FTP);
@property (class, readonly) int PSP NS_SWIFT_NAME(PSP);
@property (class, readonly) int FAPS NS_SWIFT_NAME(FAPS);
@property (class, readonly) int DECODE_3D_UNDERWORLD NS_SWIFT_NAME(DECODE_3D_UNDERWORLD);

#pragma mark - Methods



@end

NS_ASSUME_NONNULL_END


