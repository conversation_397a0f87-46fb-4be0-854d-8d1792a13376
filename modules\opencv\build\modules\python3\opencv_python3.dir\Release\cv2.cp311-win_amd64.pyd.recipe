﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\x64\Release\gen_opencv_python_source</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_world4100.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\bin\Release\opencv_img_hash4100.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\AI\opencv\cudabuild\lib\python3\Release\cv2.cp311-win_amd64.pyd</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>