OpenCV Tutorials {#tutorial_root}
================

- @subpage tutorial_table_of_content_introduction - build and install OpenCV on your computer
- @subpage tutorial_table_of_content_core - basic building blocks of the library
- @subpage tutorial_table_of_content_imgproc - image processing functions
- @subpage tutorial_table_of_content_app - application utils (GUI, image/video input/output)
- @subpage tutorial_table_of_content_calib3d - extract 3D world information from 2D images
- @subpage tutorial_table_of_content_objdetect - INSERT OBJDETECT MODULE INFO
- @subpage tutorial_table_of_content_features2d - feature detectors, descriptors and matching framework
- @subpage tutorial_table_of_content_dnn - infer neural networks using built-in _dnn_ module
- @subpage tutorial_table_of_content_gapi - graph-based approach to computer vision algorithms building
- @subpage tutorial_table_of_content_other - other modules (ml, objdetect, stitching, video, photo)
- @subpage tutorial_table_of_content_ios - running OpenCV on an iDevice
@cond CUDA_MODULES
- @subpage tutorial_table_of_content_gpu - utilizing power of video card to run CV algorithms
@endcond
