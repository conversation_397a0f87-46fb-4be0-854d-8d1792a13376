//
// This file is auto-generated. Please don't modify it!
//
#pragma once

#ifdef __cplusplus
//#import "opencv.hpp"
#import "opencv2/plot.hpp"
#else
#define CV_EXPORTS
#endif

#import <Foundation/Foundation.h>





NS_ASSUME_NONNULL_BEGIN

// C++: class Plot
/**
 * The Plot module
 *
 * Member classes: `Plot2d`
 *
 */
CV_EXPORTS @interface Plot : NSObject

#pragma mark - Methods



@end

NS_ASSUME_NONNULL_END


