OpenCV.js Tutorials {#tutorial_js_root}
=======================
-   @subpage tutorial_js_table_of_contents_setup

    Learn how to use OpenCV.js inside your web pages!

-   @subpage tutorial_js_table_of_contents_gui

    Here you will learn how to read and display images and videos, and create trackbar.

-   @subpage tutorial_js_table_of_contents_core

    In this section you will learn some basic operations on image, some mathematical tools and some data structures etc.

-   @subpage tutorial_js_table_of_contents_imgproc

    In this section
    you will learn different image processing functions inside OpenCV.

-   @subpage tutorial_js_table_of_contents_video

    In this section you
    will learn different techniques to work with videos like object tracking etc.

-   @subpage tutorial_js_table_of_contents_objdetect

    In this section you
    will object detection techniques like face detection etc.

-   @subpage tutorial_js_table_of_contents_dnn

    These tutorials show how to use dnn module in JavaScript
