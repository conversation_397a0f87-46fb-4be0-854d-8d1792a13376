// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_Management_Deployment_0_H
#define WINRT_Windows_Management_Deployment_0_H
WINRT_EXPORT namespace winrt::Windows::ApplicationModel
{
    struct AppInstallerInfo;
    struct Package;
    enum class PackageContentGroupState : int32_t;
    struct PackageVersion;
}
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    struct HResult;
    struct IAsyncAction;
    struct Uri;
}
WINRT_EXPORT namespace winrt::Windows::Foundation::Collections
{
    template <typename T> struct WINRT_IMPL_EMPTY_BASES IIterable;
}
WINRT_EXPORT namespace winrt::Windows::Management::Deployment
{
    enum class AddPackageByAppInstallerOptions : uint32_t
    {
        None = 0,
        InstallAllResources = 0x20,
        ForceTargetAppShutdown = 0x40,
        RequiredContentGroupOnly = 0x100,
        LimitToExistingPackages = 0x200,
    };
    enum class DeploymentOptions : uint32_t
    {
        None = 0,
        ForceApplicationShutdown = 0x1,
        DevelopmentMode = 0x2,
        InstallAllResources = 0x20,
        ForceTargetApplicationShutdown = 0x40,
        RequiredContentGroupOnly = 0x100,
        ForceUpdateFromAnyVersion = 0x40000,
        RetainFilesOnFailure = 0x200000,
        StageInPlace = 0x400000,
    };
    enum class DeploymentProgressState : int32_t
    {
        Queued = 0,
        Processing = 1,
    };
    enum class PackageInstallState : int32_t
    {
        NotInstalled = 0,
        Staged = 1,
        Installed = 2,
        Paused = 6,
    };
    enum class PackageState : int32_t
    {
        Normal = 0,
        LicenseInvalid = 1,
        Modified = 2,
        Tampered = 3,
    };
    enum class PackageStatus : uint32_t
    {
        OK = 0,
        LicenseIssue = 0x1,
        Modified = 0x2,
        Tampered = 0x4,
        Disabled = 0x8,
    };
    enum class PackageStubPreference : int32_t
    {
        Full = 0,
        Stub = 1,
    };
    enum class PackageTypes : uint32_t
    {
        None = 0,
        Main = 0x1,
        Framework = 0x2,
        Resource = 0x4,
        Bundle = 0x8,
        Xap = 0x10,
        Optional = 0x20,
        All = 0xffffffff,
    };
    enum class RemovalOptions : uint32_t
    {
        None = 0,
        PreserveApplicationData = 0x1000,
        PreserveRoamableApplicationData = 0x80,
        DeferRemovalWhenPackagesAreInUse = 0x2000,
        RemoveForAllUsers = 0x80000,
    };
    enum class SharedPackageContainerCreationCollisionOptions : int32_t
    {
        FailIfExists = 0,
        MergeWithExisting = 1,
        ReplaceExisting = 2,
    };
    enum class SharedPackageContainerOperationStatus : int32_t
    {
        Success = 0,
        BlockedByPolicy = 1,
        AlreadyExists = 2,
        PackageFamilyExistsInAnotherContainer = 3,
        NotFound = 4,
        UnknownFailure = 5,
    };
    enum class StubPackageOption : int32_t
    {
        Default = 0,
        InstallFull = 1,
        InstallStub = 2,
        UsePreference = 3,
    };
    struct IAddPackageOptions;
    struct IAddPackageOptions2;
    struct IAppInstallerManager;
    struct IAppInstallerManagerStatics;
    struct IAutoUpdateSettingsOptions;
    struct IAutoUpdateSettingsOptionsStatics;
    struct ICreateSharedPackageContainerOptions;
    struct ICreateSharedPackageContainerResult;
    struct IDeleteSharedPackageContainerOptions;
    struct IDeleteSharedPackageContainerResult;
    struct IDeploymentResult;
    struct IDeploymentResult2;
    struct IFindSharedPackageContainerOptions;
    struct IPackageAllUserProvisioningOptions;
    struct IPackageAllUserProvisioningOptions2;
    struct IPackageManager;
    struct IPackageManager10;
    struct IPackageManager11;
    struct IPackageManager12;
    struct IPackageManager2;
    struct IPackageManager3;
    struct IPackageManager4;
    struct IPackageManager5;
    struct IPackageManager6;
    struct IPackageManager7;
    struct IPackageManager8;
    struct IPackageManager9;
    struct IPackageManagerDebugSettings;
    struct IPackageUserInformation;
    struct IPackageVolume;
    struct IPackageVolume2;
    struct IRegisterPackageOptions;
    struct IRegisterPackageOptions2;
    struct IRemovePackageOptions;
    struct IRemovePackageOptions2;
    struct ISharedPackageContainer;
    struct ISharedPackageContainerManager;
    struct ISharedPackageContainerManagerStatics;
    struct ISharedPackageContainerMember;
    struct ISharedPackageContainerMemberFactory;
    struct IStagePackageOptions;
    struct IStagePackageOptions2;
    struct IUpdateSharedPackageContainerOptions;
    struct IUpdateSharedPackageContainerResult;
    struct AddPackageOptions;
    struct AppInstallerManager;
    struct AutoUpdateSettingsOptions;
    struct CreateSharedPackageContainerOptions;
    struct CreateSharedPackageContainerResult;
    struct DeleteSharedPackageContainerOptions;
    struct DeleteSharedPackageContainerResult;
    struct DeploymentResult;
    struct FindSharedPackageContainerOptions;
    struct PackageAllUserProvisioningOptions;
    struct PackageManager;
    struct PackageManagerDebugSettings;
    struct PackageUserInformation;
    struct PackageVolume;
    struct RegisterPackageOptions;
    struct RemovePackageOptions;
    struct SharedPackageContainer;
    struct SharedPackageContainerManager;
    struct SharedPackageContainerMember;
    struct StagePackageOptions;
    struct UpdateSharedPackageContainerOptions;
    struct UpdateSharedPackageContainerResult;
    struct DeploymentProgress;
    struct SharedPackageContainerContract;
}
namespace winrt::impl
{
    template <> struct category<winrt::Windows::Management::Deployment::IAddPackageOptions>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IAddPackageOptions2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IAppInstallerManager>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IAppInstallerManagerStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IAutoUpdateSettingsOptions>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IAutoUpdateSettingsOptionsStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::ICreateSharedPackageContainerOptions>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::ICreateSharedPackageContainerResult>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IDeleteSharedPackageContainerOptions>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IDeleteSharedPackageContainerResult>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IDeploymentResult>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IDeploymentResult2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IFindSharedPackageContainerOptions>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageAllUserProvisioningOptions>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageAllUserProvisioningOptions2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageManager>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageManager10>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageManager11>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageManager12>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageManager2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageManager3>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageManager4>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageManager5>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageManager6>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageManager7>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageManager8>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageManager9>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageManagerDebugSettings>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageUserInformation>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageVolume>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IPackageVolume2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IRegisterPackageOptions>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IRegisterPackageOptions2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IRemovePackageOptions>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IRemovePackageOptions2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::ISharedPackageContainer>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::ISharedPackageContainerManager>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::ISharedPackageContainerManagerStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::ISharedPackageContainerMember>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::ISharedPackageContainerMemberFactory>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IStagePackageOptions>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IStagePackageOptions2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IUpdateSharedPackageContainerOptions>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::IUpdateSharedPackageContainerResult>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Management::Deployment::AddPackageOptions>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::AppInstallerManager>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::AutoUpdateSettingsOptions>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::CreateSharedPackageContainerOptions>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::CreateSharedPackageContainerResult>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::DeleteSharedPackageContainerOptions>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::DeleteSharedPackageContainerResult>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::DeploymentResult>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::FindSharedPackageContainerOptions>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::PackageAllUserProvisioningOptions>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::PackageManager>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::PackageManagerDebugSettings>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::PackageUserInformation>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::PackageVolume>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::RegisterPackageOptions>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::RemovePackageOptions>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::SharedPackageContainer>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::SharedPackageContainerManager>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::SharedPackageContainerMember>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::StagePackageOptions>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::UpdateSharedPackageContainerOptions>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::UpdateSharedPackageContainerResult>{ using type = class_category; };
    template <> struct category<winrt::Windows::Management::Deployment::AddPackageByAppInstallerOptions>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Management::Deployment::DeploymentOptions>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Management::Deployment::DeploymentProgressState>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Management::Deployment::PackageInstallState>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Management::Deployment::PackageState>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Management::Deployment::PackageStatus>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Management::Deployment::PackageStubPreference>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Management::Deployment::PackageTypes>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Management::Deployment::RemovalOptions>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Management::Deployment::SharedPackageContainerCreationCollisionOptions>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Management::Deployment::SharedPackageContainerOperationStatus>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Management::Deployment::StubPackageOption>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Management::Deployment::DeploymentProgress>{ using type = struct_category<winrt::Windows::Management::Deployment::DeploymentProgressState, uint32_t>; };
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::AddPackageOptions> = L"Windows.Management.Deployment.AddPackageOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::AppInstallerManager> = L"Windows.Management.Deployment.AppInstallerManager";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::AutoUpdateSettingsOptions> = L"Windows.Management.Deployment.AutoUpdateSettingsOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::CreateSharedPackageContainerOptions> = L"Windows.Management.Deployment.CreateSharedPackageContainerOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::CreateSharedPackageContainerResult> = L"Windows.Management.Deployment.CreateSharedPackageContainerResult";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::DeleteSharedPackageContainerOptions> = L"Windows.Management.Deployment.DeleteSharedPackageContainerOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::DeleteSharedPackageContainerResult> = L"Windows.Management.Deployment.DeleteSharedPackageContainerResult";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::DeploymentResult> = L"Windows.Management.Deployment.DeploymentResult";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::FindSharedPackageContainerOptions> = L"Windows.Management.Deployment.FindSharedPackageContainerOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::PackageAllUserProvisioningOptions> = L"Windows.Management.Deployment.PackageAllUserProvisioningOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::PackageManager> = L"Windows.Management.Deployment.PackageManager";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::PackageManagerDebugSettings> = L"Windows.Management.Deployment.PackageManagerDebugSettings";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::PackageUserInformation> = L"Windows.Management.Deployment.PackageUserInformation";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::PackageVolume> = L"Windows.Management.Deployment.PackageVolume";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::RegisterPackageOptions> = L"Windows.Management.Deployment.RegisterPackageOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::RemovePackageOptions> = L"Windows.Management.Deployment.RemovePackageOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::SharedPackageContainer> = L"Windows.Management.Deployment.SharedPackageContainer";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::SharedPackageContainerManager> = L"Windows.Management.Deployment.SharedPackageContainerManager";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::SharedPackageContainerMember> = L"Windows.Management.Deployment.SharedPackageContainerMember";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::StagePackageOptions> = L"Windows.Management.Deployment.StagePackageOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::UpdateSharedPackageContainerOptions> = L"Windows.Management.Deployment.UpdateSharedPackageContainerOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::UpdateSharedPackageContainerResult> = L"Windows.Management.Deployment.UpdateSharedPackageContainerResult";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::AddPackageByAppInstallerOptions> = L"Windows.Management.Deployment.AddPackageByAppInstallerOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::DeploymentOptions> = L"Windows.Management.Deployment.DeploymentOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::DeploymentProgressState> = L"Windows.Management.Deployment.DeploymentProgressState";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::PackageInstallState> = L"Windows.Management.Deployment.PackageInstallState";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::PackageState> = L"Windows.Management.Deployment.PackageState";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::PackageStatus> = L"Windows.Management.Deployment.PackageStatus";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::PackageStubPreference> = L"Windows.Management.Deployment.PackageStubPreference";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::PackageTypes> = L"Windows.Management.Deployment.PackageTypes";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::RemovalOptions> = L"Windows.Management.Deployment.RemovalOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::SharedPackageContainerCreationCollisionOptions> = L"Windows.Management.Deployment.SharedPackageContainerCreationCollisionOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::SharedPackageContainerOperationStatus> = L"Windows.Management.Deployment.SharedPackageContainerOperationStatus";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::StubPackageOption> = L"Windows.Management.Deployment.StubPackageOption";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::DeploymentProgress> = L"Windows.Management.Deployment.DeploymentProgress";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IAddPackageOptions> = L"Windows.Management.Deployment.IAddPackageOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IAddPackageOptions2> = L"Windows.Management.Deployment.IAddPackageOptions2";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IAppInstallerManager> = L"Windows.Management.Deployment.IAppInstallerManager";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IAppInstallerManagerStatics> = L"Windows.Management.Deployment.IAppInstallerManagerStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IAutoUpdateSettingsOptions> = L"Windows.Management.Deployment.IAutoUpdateSettingsOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IAutoUpdateSettingsOptionsStatics> = L"Windows.Management.Deployment.IAutoUpdateSettingsOptionsStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::ICreateSharedPackageContainerOptions> = L"Windows.Management.Deployment.ICreateSharedPackageContainerOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::ICreateSharedPackageContainerResult> = L"Windows.Management.Deployment.ICreateSharedPackageContainerResult";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IDeleteSharedPackageContainerOptions> = L"Windows.Management.Deployment.IDeleteSharedPackageContainerOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IDeleteSharedPackageContainerResult> = L"Windows.Management.Deployment.IDeleteSharedPackageContainerResult";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IDeploymentResult> = L"Windows.Management.Deployment.IDeploymentResult";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IDeploymentResult2> = L"Windows.Management.Deployment.IDeploymentResult2";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IFindSharedPackageContainerOptions> = L"Windows.Management.Deployment.IFindSharedPackageContainerOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageAllUserProvisioningOptions> = L"Windows.Management.Deployment.IPackageAllUserProvisioningOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageAllUserProvisioningOptions2> = L"Windows.Management.Deployment.IPackageAllUserProvisioningOptions2";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageManager> = L"Windows.Management.Deployment.IPackageManager";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageManager10> = L"Windows.Management.Deployment.IPackageManager10";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageManager11> = L"Windows.Management.Deployment.IPackageManager11";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageManager12> = L"Windows.Management.Deployment.IPackageManager12";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageManager2> = L"Windows.Management.Deployment.IPackageManager2";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageManager3> = L"Windows.Management.Deployment.IPackageManager3";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageManager4> = L"Windows.Management.Deployment.IPackageManager4";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageManager5> = L"Windows.Management.Deployment.IPackageManager5";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageManager6> = L"Windows.Management.Deployment.IPackageManager6";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageManager7> = L"Windows.Management.Deployment.IPackageManager7";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageManager8> = L"Windows.Management.Deployment.IPackageManager8";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageManager9> = L"Windows.Management.Deployment.IPackageManager9";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageManagerDebugSettings> = L"Windows.Management.Deployment.IPackageManagerDebugSettings";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageUserInformation> = L"Windows.Management.Deployment.IPackageUserInformation";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageVolume> = L"Windows.Management.Deployment.IPackageVolume";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IPackageVolume2> = L"Windows.Management.Deployment.IPackageVolume2";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IRegisterPackageOptions> = L"Windows.Management.Deployment.IRegisterPackageOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IRegisterPackageOptions2> = L"Windows.Management.Deployment.IRegisterPackageOptions2";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IRemovePackageOptions> = L"Windows.Management.Deployment.IRemovePackageOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IRemovePackageOptions2> = L"Windows.Management.Deployment.IRemovePackageOptions2";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::ISharedPackageContainer> = L"Windows.Management.Deployment.ISharedPackageContainer";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::ISharedPackageContainerManager> = L"Windows.Management.Deployment.ISharedPackageContainerManager";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::ISharedPackageContainerManagerStatics> = L"Windows.Management.Deployment.ISharedPackageContainerManagerStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::ISharedPackageContainerMember> = L"Windows.Management.Deployment.ISharedPackageContainerMember";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::ISharedPackageContainerMemberFactory> = L"Windows.Management.Deployment.ISharedPackageContainerMemberFactory";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IStagePackageOptions> = L"Windows.Management.Deployment.IStagePackageOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IStagePackageOptions2> = L"Windows.Management.Deployment.IStagePackageOptions2";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IUpdateSharedPackageContainerOptions> = L"Windows.Management.Deployment.IUpdateSharedPackageContainerOptions";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::IUpdateSharedPackageContainerResult> = L"Windows.Management.Deployment.IUpdateSharedPackageContainerResult";
    template <> inline constexpr auto& name_v<winrt::Windows::Management::Deployment::SharedPackageContainerContract> = L"Windows.Management.Deployment.SharedPackageContainerContract";
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IAddPackageOptions>{ 0x05CEE018,0xF68F,0x422B,{ 0x95,0xA4,0x66,0x67,0x9E,0xC7,0x7F,0xC0 } }; // 05CEE018-F68F-422B-95A4-66679EC77FC0
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IAddPackageOptions2>{ 0xEE515828,0xBF33,0x40F7,{ 0x84,0xAF,0x1B,0x6F,0xAD,0x29,0x19,0xD7 } }; // EE515828-BF33-40F7-84AF-1B6FAD2919D7
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IAppInstallerManager>{ 0xE7EE21C3,0x2103,0x53EE,{ 0x9B,0x18,0x68,0xAF,0xEA,0xB0,0x03,0x3D } }; // E7EE21C3-2103-53EE-9B18-68AFEAB0033D
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IAppInstallerManagerStatics>{ 0xC95A6ED5,0xFC59,0x5336,{ 0x9B,0x2E,0x2B,0x07,0xC5,0xE6,0x14,0x34 } }; // C95A6ED5-FC59-5336-9B2E-2B07C5E61434
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IAutoUpdateSettingsOptions>{ 0x67491D87,0x35E1,0x512A,{ 0x89,0x68,0x1A,0xE8,0x8D,0x1B,0xE6,0xD3 } }; // 67491D87-35E1-512A-8968-1AE88D1BE6D3
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IAutoUpdateSettingsOptionsStatics>{ 0x887B337D,0x0C05,0x54D0,{ 0xBD,0x49,0x3B,0xB7,0xA2,0xC0,0x84,0xCB } }; // 887B337D-0C05-54D0-BD49-3BB7A2C084CB
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::ICreateSharedPackageContainerOptions>{ 0xC2AB6ECE,0xF664,0x5C8E,{ 0xA4,0xB3,0x2A,0x33,0x27,0x6D,0x3D,0xDE } }; // C2AB6ECE-F664-5C8E-A4B3-2A33276D3DDE
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::ICreateSharedPackageContainerResult>{ 0xCE8810BF,0x151C,0x5707,{ 0xB9,0x36,0x49,0x7E,0x56,0x4A,0xFC,0x7A } }; // CE8810BF-151C-5707-B936-497E564AFC7A
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IDeleteSharedPackageContainerOptions>{ 0x9D81865F,0x986E,0x5138,{ 0x8B,0x5D,0x38,0x4D,0x8E,0x66,0xED,0x6C } }; // 9D81865F-986E-5138-8B5D-384D8E66ED6C
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IDeleteSharedPackageContainerResult>{ 0x35398884,0x5736,0x517B,{ 0x85,0xBC,0xE5,0x98,0xC8,0x1A,0xB2,0x84 } }; // *************-517B-85BC-E598C81AB284
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IDeploymentResult>{ 0x2563B9AE,0xB77D,0x4C1F,{ 0x8A,0x7B,0x20,0xE6,0xAD,0x51,0x5E,0xF3 } }; // 2563B9AE-B77D-4C1F-8A7B-20E6AD515EF3
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IDeploymentResult2>{ 0xFC0E715C,0x5A01,0x4BD7,{ 0xBC,0xF1,0x38,0x1C,0x8C,0x82,0xE0,0x4A } }; // FC0E715C-5A01-4BD7-BCF1-381C8C82E04A
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IFindSharedPackageContainerOptions>{ 0xB40FC8FE,0x8384,0x54CC,{ 0x81,0x7D,0xAE,0x09,0xD3,0xB6,0xA6,0x06 } }; // B40FC8FE-8384-54CC-817D-AE09D3B6A606
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageAllUserProvisioningOptions>{ 0xDA35AA22,0x1DE0,0x5D3E,{ 0x99,0xFF,0xD2,0x4F,0x31,0x18,0xBF,0x5E } }; // DA35AA22-1DE0-5D3E-99FF-D24F3118BF5E
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageAllUserProvisioningOptions2>{ 0xB9E3CAB5,0x2D97,0x579F,{ 0x93,0x68,0xD1,0x0B,0xB4,0xD4,0x54,0x2B } }; // B9E3CAB5-2D97-579F-9368-D10BB4D4542B
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageManager>{ 0x9A7D4B65,0x5E8F,0x4FC7,{ 0xA2,0xE5,0x7F,0x69,0x25,0xCB,0x8B,0x53 } }; // 9A7D4B65-5E8F-4FC7-A2E5-7F6925CB8B53
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageManager10>{ 0xA7D7D07E,0x2E66,0x4093,{ 0xAE,0xD5,0xE0,0x93,0xED,0x87,0xB3,0xBB } }; // A7D7D07E-2E66-4093-AED5-E093ED87B3BB
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageManager11>{ 0x12950B24,0xC77E,0x4EA7,{ 0x88,0x59,0x32,0x53,0x18,0x07,0x4E,0x15 } }; // 12950B24-C77E-4EA7-8859-325318074E15
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageManager12>{ 0x5D233ADF,0xF9E3,0x4D96,{ 0xB4,0x0D,0x96,0x78,0x8E,0x39,0x53,0x9F } }; // 5D233ADF-F9E3-4D96-B40D-96788E39539F
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageManager2>{ 0xF7AAD08D,0x0840,0x46F2,{ 0xB5,0xD8,0xCA,0xD4,0x76,0x93,0xA0,0x95 } }; // F7AAD08D-0840-46F2-B5D8-CAD47693A095
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageManager3>{ 0xDAAD9948,0x36F1,0x41A7,{ 0x91,0x88,0xBC,0x26,0x3E,0x0D,0xCB,0x72 } }; // DAAD9948-36F1-41A7-9188-BC263E0DCB72
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageManager4>{ 0x3C719963,0xBAB6,0x46BF,{ 0x8F,0xF7,0xDA,0x47,0x19,0x23,0x0A,0xE6 } }; // 3C719963-BAB6-46BF-8FF7-DA4719230AE6
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageManager5>{ 0x711F3117,0x1AFD,0x4313,{ 0x97,0x8C,0x9B,0xB6,0xE1,0xB8,0x64,0xA7 } }; // 711F3117-1AFD-4313-978C-9BB6E1B864A7
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageManager6>{ 0x0847E909,0x53CD,0x4E4F,{ 0x83,0x2E,0x57,0xD1,0x80,0xF6,0xE4,0x47 } }; // 0847E909-53CD-4E4F-832E-57D180F6E447
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageManager7>{ 0xF28654F4,0x2BA7,0x4B80,{ 0x88,0xD6,0xBE,0x15,0xF9,0xA2,0x3F,0xBA } }; // F28654F4-2BA7-4B80-88D6-BE15F9A23FBA
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageManager8>{ 0xB8575330,0x1298,0x4EE2,{ 0x80,0xEE,0x7F,0x65,0x9C,0x5D,0x27,0x82 } }; // B8575330-1298-4EE2-80EE-7F659C5D2782
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageManager9>{ 0x1AA79035,0xCC71,0x4B2E,{ 0x80,0xA6,0xC7,0x04,0x1D,0x85,0x79,0xA7 } }; // 1AA79035-CC71-4B2E-80A6-C7041D8579A7
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageManagerDebugSettings>{ 0x1A611683,0xA988,0x4FCF,{ 0x8F,0x0F,0xCE,0x17,0x58,0x98,0xE8,0xEB } }; // 1A611683-A988-4FCF-8F0F-CE175898E8EB
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageUserInformation>{ 0xF6383423,0xFA09,0x4CBC,{ 0x90,0x55,0x15,0xCA,0x27,0x5E,0x2E,0x7E } }; // F6383423-FA09-4CBC-9055-15CA275E2E7E
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageVolume>{ 0xCF2672C3,0x1A40,0x4450,{ 0x97,0x39,0x2A,0xCE,0x2E,0x89,0x88,0x53 } }; // CF2672C3-1A40-4450-9739-2ACE2E898853
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IPackageVolume2>{ 0x46ABCF2E,0x9DD4,0x47A2,{ 0xAB,0x8C,0xC6,0x40,0x83,0x49,0xBC,0xD8 } }; // 46ABCF2E-9DD4-47A2-AB8C-C6408349BCD8
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IRegisterPackageOptions>{ 0x677112A7,0x50D4,0x496C,{ 0x84,0x15,0x06,0x02,0xB4,0xC6,0xD3,0xBF } }; // 677112A7-50D4-496C-8415-0602B4C6D3BF
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IRegisterPackageOptions2>{ 0x3DFA9743,0x86FF,0x4A11,{ 0xBC,0x93,0x43,0x4E,0xB6,0xBE,0x3A,0x0B } }; // 3DFA9743-86FF-4A11-BC93-434EB6BE3A0B
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IRemovePackageOptions>{ 0x13CF01F3,0xC450,0x4F7C,{ 0xA5,0xA3,0x5E,0x3C,0x63,0x1B,0x74,0x62 } }; // 13CF01F3-C450-4F7C-A5A3-5E3C631B7462
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IRemovePackageOptions2>{ 0x3FCC61E5,0x22C5,0x423B,{ 0xB4,0xB4,0xCF,0x10,0xBB,0x50,0x83,0x0C } }; // 3FCC61E5-22C5-423B-B4B4-CF10BB50830C
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::ISharedPackageContainer>{ 0x177F1AA9,0x151E,0x5EF7,{ 0xB1,0xD9,0x2F,0xBA,0x0B,0x4B,0x0D,0x17 } }; // 177F1AA9-151E-5EF7-B1D9-2FBA0B4B0D17
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::ISharedPackageContainerManager>{ 0xBE353068,0x1EF7,0x5AC8,{ 0xAB,0x3F,0x0B,0x9F,0x61,0x2F,0x02,0x74 } }; // BE353068-1EF7-5AC8-AB3F-0B9F612F0274
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::ISharedPackageContainerManagerStatics>{ 0x2EF56348,0x838A,0x5F55,{ 0xA8,0x9E,0x11,0x98,0xA2,0xC6,0x27,0xE6 } }; // 2EF56348-838A-5F55-A89E-1198A2C627E6
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::ISharedPackageContainerMember>{ 0xFE0D0438,0x43C9,0x5426,{ 0xB8,0x9C,0xF7,0x9B,0xF8,0x5D,0xDF,0xF4 } }; // FE0D0438-43C9-5426-B89C-F79BF85DDFF4
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::ISharedPackageContainerMemberFactory>{ 0x49B0CEEB,0x498F,0x5A62,{ 0xB7,0x38,0xB3,0xCA,0x0D,0x43,0x67,0x04 } }; // 49B0CEEB-498F-5A62-B738-B3CA0D436704
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IStagePackageOptions>{ 0x0B110C9C,0xB95D,0x4C56,{ 0xBD,0x36,0x6D,0x65,0x68,0x00,0xD0,0x6B } }; // 0B110C9C-B95D-4C56-BD36-6D656800D06B
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IStagePackageOptions2>{ 0x990C4CCC,0x6226,0x4192,{ 0xBA,0x92,0x79,0x87,0x5F,0xCE,0x0D,0x9C } }; // 990C4CCC-6226-4192-BA92-79875FCE0D9C
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IUpdateSharedPackageContainerOptions>{ 0x80672E83,0x7194,0x59F9,{ 0xB5,0xB9,0xDA,0xA5,0x37,0x5F,0x13,0x0A } }; // 80672E83-7194-59F9-B5B9-DAA5375F130A
    template <> inline constexpr guid guid_v<winrt::Windows::Management::Deployment::IUpdateSharedPackageContainerResult>{ 0xAA407DF7,0xC72D,0x5458,{ 0xAE,0xA3,0x46,0x45,0xB6,0xA8,0xEE,0x99 } }; // AA407DF7-C72D-5458-AEA3-4645B6A8EE99
    template <> struct default_interface<winrt::Windows::Management::Deployment::AddPackageOptions>{ using type = winrt::Windows::Management::Deployment::IAddPackageOptions; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::AppInstallerManager>{ using type = winrt::Windows::Management::Deployment::IAppInstallerManager; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::AutoUpdateSettingsOptions>{ using type = winrt::Windows::Management::Deployment::IAutoUpdateSettingsOptions; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::CreateSharedPackageContainerOptions>{ using type = winrt::Windows::Management::Deployment::ICreateSharedPackageContainerOptions; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::CreateSharedPackageContainerResult>{ using type = winrt::Windows::Management::Deployment::ICreateSharedPackageContainerResult; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::DeleteSharedPackageContainerOptions>{ using type = winrt::Windows::Management::Deployment::IDeleteSharedPackageContainerOptions; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::DeleteSharedPackageContainerResult>{ using type = winrt::Windows::Management::Deployment::IDeleteSharedPackageContainerResult; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::DeploymentResult>{ using type = winrt::Windows::Management::Deployment::IDeploymentResult; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::FindSharedPackageContainerOptions>{ using type = winrt::Windows::Management::Deployment::IFindSharedPackageContainerOptions; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::PackageAllUserProvisioningOptions>{ using type = winrt::Windows::Management::Deployment::IPackageAllUserProvisioningOptions; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::PackageManager>{ using type = winrt::Windows::Management::Deployment::IPackageManager; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::PackageManagerDebugSettings>{ using type = winrt::Windows::Management::Deployment::IPackageManagerDebugSettings; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::PackageUserInformation>{ using type = winrt::Windows::Management::Deployment::IPackageUserInformation; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::PackageVolume>{ using type = winrt::Windows::Management::Deployment::IPackageVolume; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::RegisterPackageOptions>{ using type = winrt::Windows::Management::Deployment::IRegisterPackageOptions; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::RemovePackageOptions>{ using type = winrt::Windows::Management::Deployment::IRemovePackageOptions; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::SharedPackageContainer>{ using type = winrt::Windows::Management::Deployment::ISharedPackageContainer; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::SharedPackageContainerManager>{ using type = winrt::Windows::Management::Deployment::ISharedPackageContainerManager; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::SharedPackageContainerMember>{ using type = winrt::Windows::Management::Deployment::ISharedPackageContainerMember; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::StagePackageOptions>{ using type = winrt::Windows::Management::Deployment::IStagePackageOptions; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::UpdateSharedPackageContainerOptions>{ using type = winrt::Windows::Management::Deployment::IUpdateSharedPackageContainerOptions; };
    template <> struct default_interface<winrt::Windows::Management::Deployment::UpdateSharedPackageContainerResult>{ using type = winrt::Windows::Management::Deployment::IUpdateSharedPackageContainerResult; };
    template <> struct abi<winrt::Windows::Management::Deployment::IAddPackageOptions>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_DependencyPackageUris(void**) noexcept = 0;
            virtual int32_t __stdcall get_TargetVolume(void**) noexcept = 0;
            virtual int32_t __stdcall put_TargetVolume(void*) noexcept = 0;
            virtual int32_t __stdcall get_OptionalPackageFamilyNames(void**) noexcept = 0;
            virtual int32_t __stdcall get_OptionalPackageUris(void**) noexcept = 0;
            virtual int32_t __stdcall get_RelatedPackageUris(void**) noexcept = 0;
            virtual int32_t __stdcall get_ExternalLocationUri(void**) noexcept = 0;
            virtual int32_t __stdcall put_ExternalLocationUri(void*) noexcept = 0;
            virtual int32_t __stdcall get_StubPackageOption(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_StubPackageOption(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_DeveloperMode(bool*) noexcept = 0;
            virtual int32_t __stdcall put_DeveloperMode(bool) noexcept = 0;
            virtual int32_t __stdcall get_ForceAppShutdown(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ForceAppShutdown(bool) noexcept = 0;
            virtual int32_t __stdcall get_ForceTargetAppShutdown(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ForceTargetAppShutdown(bool) noexcept = 0;
            virtual int32_t __stdcall get_ForceUpdateFromAnyVersion(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ForceUpdateFromAnyVersion(bool) noexcept = 0;
            virtual int32_t __stdcall get_InstallAllResources(bool*) noexcept = 0;
            virtual int32_t __stdcall put_InstallAllResources(bool) noexcept = 0;
            virtual int32_t __stdcall get_RequiredContentGroupOnly(bool*) noexcept = 0;
            virtual int32_t __stdcall put_RequiredContentGroupOnly(bool) noexcept = 0;
            virtual int32_t __stdcall get_RetainFilesOnFailure(bool*) noexcept = 0;
            virtual int32_t __stdcall put_RetainFilesOnFailure(bool) noexcept = 0;
            virtual int32_t __stdcall get_StageInPlace(bool*) noexcept = 0;
            virtual int32_t __stdcall put_StageInPlace(bool) noexcept = 0;
            virtual int32_t __stdcall get_AllowUnsigned(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AllowUnsigned(bool) noexcept = 0;
            virtual int32_t __stdcall get_DeferRegistrationWhenPackagesAreInUse(bool*) noexcept = 0;
            virtual int32_t __stdcall put_DeferRegistrationWhenPackagesAreInUse(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IAddPackageOptions2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ExpectedDigests(void**) noexcept = 0;
            virtual int32_t __stdcall get_LimitToExistingPackages(bool*) noexcept = 0;
            virtual int32_t __stdcall put_LimitToExistingPackages(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IAppInstallerManager>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall SetAutoUpdateSettings(void*, void*) noexcept = 0;
            virtual int32_t __stdcall ClearAutoUpdateSettings(void*) noexcept = 0;
            virtual int32_t __stdcall PauseAutoUpdatesUntil(void*, int64_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IAppInstallerManagerStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetDefault(void**) noexcept = 0;
            virtual int32_t __stdcall GetForSystem(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IAutoUpdateSettingsOptions>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Version(struct struct_Windows_ApplicationModel_PackageVersion*) noexcept = 0;
            virtual int32_t __stdcall put_Version(struct struct_Windows_ApplicationModel_PackageVersion) noexcept = 0;
            virtual int32_t __stdcall get_AppInstallerUri(void**) noexcept = 0;
            virtual int32_t __stdcall put_AppInstallerUri(void*) noexcept = 0;
            virtual int32_t __stdcall get_OnLaunch(bool*) noexcept = 0;
            virtual int32_t __stdcall put_OnLaunch(bool) noexcept = 0;
            virtual int32_t __stdcall get_HoursBetweenUpdateChecks(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_HoursBetweenUpdateChecks(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_ShowPrompt(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ShowPrompt(bool) noexcept = 0;
            virtual int32_t __stdcall get_UpdateBlocksActivation(bool*) noexcept = 0;
            virtual int32_t __stdcall put_UpdateBlocksActivation(bool) noexcept = 0;
            virtual int32_t __stdcall get_AutomaticBackgroundTask(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AutomaticBackgroundTask(bool) noexcept = 0;
            virtual int32_t __stdcall get_ForceUpdateFromAnyVersion(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ForceUpdateFromAnyVersion(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsAutoRepairEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsAutoRepairEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_UpdateUris(void**) noexcept = 0;
            virtual int32_t __stdcall get_RepairUris(void**) noexcept = 0;
            virtual int32_t __stdcall get_DependencyPackageUris(void**) noexcept = 0;
            virtual int32_t __stdcall get_OptionalPackageUris(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IAutoUpdateSettingsOptionsStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateFromAppInstallerInfo(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::ICreateSharedPackageContainerOptions>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Members(void**) noexcept = 0;
            virtual int32_t __stdcall get_ForceAppShutdown(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ForceAppShutdown(bool) noexcept = 0;
            virtual int32_t __stdcall get_CreateCollisionOption(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_CreateCollisionOption(int32_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::ICreateSharedPackageContainerResult>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Container(void**) noexcept = 0;
            virtual int32_t __stdcall get_Status(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_ExtendedError(winrt::hresult*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IDeleteSharedPackageContainerOptions>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ForceAppShutdown(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ForceAppShutdown(bool) noexcept = 0;
            virtual int32_t __stdcall get_AllUsers(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AllUsers(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IDeleteSharedPackageContainerResult>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Status(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_ExtendedError(winrt::hresult*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IDeploymentResult>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ErrorText(void**) noexcept = 0;
            virtual int32_t __stdcall get_ActivityId(winrt::guid*) noexcept = 0;
            virtual int32_t __stdcall get_ExtendedErrorCode(winrt::hresult*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IDeploymentResult2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsRegistered(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IFindSharedPackageContainerOptions>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall put_Name(void*) noexcept = 0;
            virtual int32_t __stdcall get_PackageFamilyName(void**) noexcept = 0;
            virtual int32_t __stdcall put_PackageFamilyName(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageAllUserProvisioningOptions>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_OptionalPackageFamilyNames(void**) noexcept = 0;
            virtual int32_t __stdcall get_ProjectionOrderPackageFamilyNames(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageAllUserProvisioningOptions2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_DeferAutomaticRegistration(bool*) noexcept = 0;
            virtual int32_t __stdcall put_DeferAutomaticRegistration(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageManager>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall AddPackageAsync(void*, void*, uint32_t, void**) noexcept = 0;
            virtual int32_t __stdcall UpdatePackageAsync(void*, void*, uint32_t, void**) noexcept = 0;
            virtual int32_t __stdcall RemovePackageAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall StagePackageAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall RegisterPackageAsync(void*, void*, uint32_t, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackages(void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByUserSecurityId(void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByNamePublisher(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByUserSecurityIdNamePublisher(void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindUsers(void*, void**) noexcept = 0;
            virtual int32_t __stdcall SetPackageState(void*, int32_t) noexcept = 0;
            virtual int32_t __stdcall FindPackageByPackageFullName(void*, void**) noexcept = 0;
            virtual int32_t __stdcall CleanupPackageForUserAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByPackageFamilyName(void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByUserSecurityIdPackageFamilyName(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackageByUserSecurityIdPackageFullName(void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageManager10>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall ProvisionPackageForAllUsersWithOptionsAsync(void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageManager11>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall RemovePackageByUriAsync(void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageManager12>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall IsPackageRemovalPending(void*, bool*) noexcept = 0;
            virtual int32_t __stdcall IsPackageRemovalPendingForUser(void*, void*, bool*) noexcept = 0;
            virtual int32_t __stdcall IsPackageRemovalPendingByUri(void*, bool*) noexcept = 0;
            virtual int32_t __stdcall IsPackageRemovalPendingByUriForUser(void*, void*, bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageManager2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall RemovePackageWithOptionsAsync(void*, uint32_t, void**) noexcept = 0;
            virtual int32_t __stdcall StagePackageWithOptionsAsync(void*, void*, uint32_t, void**) noexcept = 0;
            virtual int32_t __stdcall RegisterPackageByFullNameAsync(void*, void*, uint32_t, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesWithPackageTypes(uint32_t, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByUserSecurityIdWithPackageTypes(void*, uint32_t, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByNamePublisherWithPackageTypes(void*, void*, uint32_t, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByUserSecurityIdNamePublisherWithPackageTypes(void*, void*, void*, uint32_t, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByPackageFamilyNameWithPackageTypes(void*, uint32_t, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByUserSecurityIdPackageFamilyNameWithPackageTypes(void*, void*, uint32_t, void**) noexcept = 0;
            virtual int32_t __stdcall StageUserDataAsync(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageManager3>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall AddPackageVolumeAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall AddPackageToVolumeAsync(void*, void*, uint32_t, void*, void**) noexcept = 0;
            virtual int32_t __stdcall ClearPackageStatus(void*, uint32_t) noexcept = 0;
            virtual int32_t __stdcall RegisterPackageWithAppDataVolumeAsync(void*, void*, uint32_t, void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackageVolumeByName(void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackageVolumes(void**) noexcept = 0;
            virtual int32_t __stdcall GetDefaultPackageVolume(void**) noexcept = 0;
            virtual int32_t __stdcall MovePackageToVolumeAsync(void*, uint32_t, void*, void**) noexcept = 0;
            virtual int32_t __stdcall RemovePackageVolumeAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall SetDefaultPackageVolume(void*) noexcept = 0;
            virtual int32_t __stdcall SetPackageStatus(void*, uint32_t) noexcept = 0;
            virtual int32_t __stdcall SetPackageVolumeOfflineAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall SetPackageVolumeOnlineAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall StagePackageToVolumeAsync(void*, void*, uint32_t, void*, void**) noexcept = 0;
            virtual int32_t __stdcall StageUserDataWithOptionsAsync(void*, uint32_t, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageManager4>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetPackageVolumesAsync(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageManager5>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall AddPackageToVolumeAndOptionalPackagesAsync(void*, void*, uint32_t, void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall StagePackageToVolumeAndOptionalPackagesAsync(void*, void*, uint32_t, void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall RegisterPackageByFamilyNameAndOptionalPackagesAsync(void*, void*, uint32_t, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall get_DebugSettings(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageManager6>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall ProvisionPackageForAllUsersAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall AddPackageByAppInstallerFileAsync(void*, uint32_t, void*, void**) noexcept = 0;
            virtual int32_t __stdcall RequestAddPackageByAppInstallerFileAsync(void*, uint32_t, void*, void**) noexcept = 0;
            virtual int32_t __stdcall AddPackageToVolumeAndRelatedSetAsync(void*, void*, uint32_t, void*, void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall StagePackageToVolumeAndRelatedSetAsync(void*, void*, uint32_t, void*, void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall RequestAddPackageAsync(void*, void*, uint32_t, void*, void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageManager7>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall RequestAddPackageAndRelatedSetAsync(void*, void*, uint32_t, void*, void*, void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageManager8>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall DeprovisionPackageForAllUsersAsync(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageManager9>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall FindProvisionedPackages(void**) noexcept = 0;
            virtual int32_t __stdcall AddPackageByUriAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall StagePackageByUriAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall RegisterPackageByUriAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall RegisterPackagesByFullNameAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall SetPackageStubPreference(void*, int32_t) noexcept = 0;
            virtual int32_t __stdcall GetPackageStubPreference(void*, int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageManagerDebugSettings>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall SetContentGroupStateAsync(void*, void*, int32_t, void**) noexcept = 0;
            virtual int32_t __stdcall SetContentGroupStateWithPercentageAsync(void*, void*, int32_t, double, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageUserInformation>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_UserSecurityId(void**) noexcept = 0;
            virtual int32_t __stdcall get_InstallState(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageVolume>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsOffline(bool*) noexcept = 0;
            virtual int32_t __stdcall get_IsSystemVolume(bool*) noexcept = 0;
            virtual int32_t __stdcall get_MountPoint(void**) noexcept = 0;
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall get_PackageStorePath(void**) noexcept = 0;
            virtual int32_t __stdcall get_SupportsHardLinks(bool*) noexcept = 0;
            virtual int32_t __stdcall FindPackages(void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByNamePublisher(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByPackageFamilyName(void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesWithPackageTypes(uint32_t, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByNamePublisherWithPackagesTypes(uint32_t, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByPackageFamilyNameWithPackageTypes(uint32_t, void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackageByPackageFullName(void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByUserSecurityId(void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByUserSecurityIdNamePublisher(void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByUserSecurityIdPackageFamilyName(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByUserSecurityIdWithPackageTypes(void*, uint32_t, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByUserSecurityIdNamePublisherWithPackageTypes(void*, uint32_t, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackagesByUserSecurityIdPackageFamilyNameWithPackagesTypes(void*, uint32_t, void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindPackageByUserSecurityIdPackageFullName(void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IPackageVolume2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsFullTrustPackageSupported(bool*) noexcept = 0;
            virtual int32_t __stdcall get_IsAppxInstallSupported(bool*) noexcept = 0;
            virtual int32_t __stdcall GetAvailableSpaceAsync(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IRegisterPackageOptions>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_DependencyPackageUris(void**) noexcept = 0;
            virtual int32_t __stdcall get_AppDataVolume(void**) noexcept = 0;
            virtual int32_t __stdcall put_AppDataVolume(void*) noexcept = 0;
            virtual int32_t __stdcall get_OptionalPackageFamilyNames(void**) noexcept = 0;
            virtual int32_t __stdcall get_ExternalLocationUri(void**) noexcept = 0;
            virtual int32_t __stdcall put_ExternalLocationUri(void*) noexcept = 0;
            virtual int32_t __stdcall get_DeveloperMode(bool*) noexcept = 0;
            virtual int32_t __stdcall put_DeveloperMode(bool) noexcept = 0;
            virtual int32_t __stdcall get_ForceAppShutdown(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ForceAppShutdown(bool) noexcept = 0;
            virtual int32_t __stdcall get_ForceTargetAppShutdown(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ForceTargetAppShutdown(bool) noexcept = 0;
            virtual int32_t __stdcall get_ForceUpdateFromAnyVersion(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ForceUpdateFromAnyVersion(bool) noexcept = 0;
            virtual int32_t __stdcall get_InstallAllResources(bool*) noexcept = 0;
            virtual int32_t __stdcall put_InstallAllResources(bool) noexcept = 0;
            virtual int32_t __stdcall get_StageInPlace(bool*) noexcept = 0;
            virtual int32_t __stdcall put_StageInPlace(bool) noexcept = 0;
            virtual int32_t __stdcall get_AllowUnsigned(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AllowUnsigned(bool) noexcept = 0;
            virtual int32_t __stdcall get_DeferRegistrationWhenPackagesAreInUse(bool*) noexcept = 0;
            virtual int32_t __stdcall put_DeferRegistrationWhenPackagesAreInUse(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IRegisterPackageOptions2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ExpectedDigests(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IRemovePackageOptions>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_PreserveApplicationData(bool*) noexcept = 0;
            virtual int32_t __stdcall put_PreserveApplicationData(bool) noexcept = 0;
            virtual int32_t __stdcall get_PreserveRoamableApplicationData(bool*) noexcept = 0;
            virtual int32_t __stdcall put_PreserveRoamableApplicationData(bool) noexcept = 0;
            virtual int32_t __stdcall get_RemoveForAllUsers(bool*) noexcept = 0;
            virtual int32_t __stdcall put_RemoveForAllUsers(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IRemovePackageOptions2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_DeferRemovalWhenPackagesAreInUse(bool*) noexcept = 0;
            virtual int32_t __stdcall put_DeferRemovalWhenPackagesAreInUse(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::ISharedPackageContainer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall get_Id(void**) noexcept = 0;
            virtual int32_t __stdcall GetMembers(void**) noexcept = 0;
            virtual int32_t __stdcall RemovePackageFamily(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall ResetData(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::ISharedPackageContainerManager>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateContainer(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall DeleteContainer(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetContainer(void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindContainers(void**) noexcept = 0;
            virtual int32_t __stdcall FindContainersWithOptions(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::ISharedPackageContainerManagerStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetDefault(void**) noexcept = 0;
            virtual int32_t __stdcall GetForUser(void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetForProvisioning(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::ISharedPackageContainerMember>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_PackageFamilyName(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::ISharedPackageContainerMemberFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IStagePackageOptions>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_DependencyPackageUris(void**) noexcept = 0;
            virtual int32_t __stdcall get_TargetVolume(void**) noexcept = 0;
            virtual int32_t __stdcall put_TargetVolume(void*) noexcept = 0;
            virtual int32_t __stdcall get_OptionalPackageFamilyNames(void**) noexcept = 0;
            virtual int32_t __stdcall get_OptionalPackageUris(void**) noexcept = 0;
            virtual int32_t __stdcall get_RelatedPackageUris(void**) noexcept = 0;
            virtual int32_t __stdcall get_ExternalLocationUri(void**) noexcept = 0;
            virtual int32_t __stdcall put_ExternalLocationUri(void*) noexcept = 0;
            virtual int32_t __stdcall get_StubPackageOption(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_StubPackageOption(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_DeveloperMode(bool*) noexcept = 0;
            virtual int32_t __stdcall put_DeveloperMode(bool) noexcept = 0;
            virtual int32_t __stdcall get_ForceUpdateFromAnyVersion(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ForceUpdateFromAnyVersion(bool) noexcept = 0;
            virtual int32_t __stdcall get_InstallAllResources(bool*) noexcept = 0;
            virtual int32_t __stdcall put_InstallAllResources(bool) noexcept = 0;
            virtual int32_t __stdcall get_RequiredContentGroupOnly(bool*) noexcept = 0;
            virtual int32_t __stdcall put_RequiredContentGroupOnly(bool) noexcept = 0;
            virtual int32_t __stdcall get_StageInPlace(bool*) noexcept = 0;
            virtual int32_t __stdcall put_StageInPlace(bool) noexcept = 0;
            virtual int32_t __stdcall get_AllowUnsigned(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AllowUnsigned(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IStagePackageOptions2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ExpectedDigests(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IUpdateSharedPackageContainerOptions>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ForceAppShutdown(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ForceAppShutdown(bool) noexcept = 0;
            virtual int32_t __stdcall get_RequirePackagesPresent(bool*) noexcept = 0;
            virtual int32_t __stdcall put_RequirePackagesPresent(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Management::Deployment::IUpdateSharedPackageContainerResult>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Status(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_ExtendedError(winrt::hresult*) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IAddPackageOptions
    {
        [[nodiscard]] auto DependencyPackageUris() const;
        [[nodiscard]] auto TargetVolume() const;
        auto TargetVolume(winrt::Windows::Management::Deployment::PackageVolume const& value) const;
        [[nodiscard]] auto OptionalPackageFamilyNames() const;
        [[nodiscard]] auto OptionalPackageUris() const;
        [[nodiscard]] auto RelatedPackageUris() const;
        [[nodiscard]] auto ExternalLocationUri() const;
        auto ExternalLocationUri(winrt::Windows::Foundation::Uri const& value) const;
        [[nodiscard]] auto StubPackageOption() const;
        auto StubPackageOption(winrt::Windows::Management::Deployment::StubPackageOption const& value) const;
        [[nodiscard]] auto DeveloperMode() const;
        auto DeveloperMode(bool value) const;
        [[nodiscard]] auto ForceAppShutdown() const;
        auto ForceAppShutdown(bool value) const;
        [[nodiscard]] auto ForceTargetAppShutdown() const;
        auto ForceTargetAppShutdown(bool value) const;
        [[nodiscard]] auto ForceUpdateFromAnyVersion() const;
        auto ForceUpdateFromAnyVersion(bool value) const;
        [[nodiscard]] auto InstallAllResources() const;
        auto InstallAllResources(bool value) const;
        [[nodiscard]] auto RequiredContentGroupOnly() const;
        auto RequiredContentGroupOnly(bool value) const;
        [[nodiscard]] auto RetainFilesOnFailure() const;
        auto RetainFilesOnFailure(bool value) const;
        [[nodiscard]] auto StageInPlace() const;
        auto StageInPlace(bool value) const;
        [[nodiscard]] auto AllowUnsigned() const;
        auto AllowUnsigned(bool value) const;
        [[nodiscard]] auto DeferRegistrationWhenPackagesAreInUse() const;
        auto DeferRegistrationWhenPackagesAreInUse(bool value) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IAddPackageOptions>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IAddPackageOptions<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IAddPackageOptions2
    {
        [[nodiscard]] auto ExpectedDigests() const;
        [[nodiscard]] auto LimitToExistingPackages() const;
        auto LimitToExistingPackages(bool value) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IAddPackageOptions2>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IAddPackageOptions2<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IAppInstallerManager
    {
        auto SetAutoUpdateSettings(param::hstring const& packageFamilyName, winrt::Windows::Management::Deployment::AutoUpdateSettingsOptions const& appInstallerInfo) const;
        auto ClearAutoUpdateSettings(param::hstring const& packageFamilyName) const;
        auto PauseAutoUpdatesUntil(param::hstring const& packageFamilyName, winrt::Windows::Foundation::DateTime const& dateTime) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IAppInstallerManager>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IAppInstallerManager<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IAppInstallerManagerStatics
    {
        auto GetDefault() const;
        auto GetForSystem() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IAppInstallerManagerStatics>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IAppInstallerManagerStatics<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IAutoUpdateSettingsOptions
    {
        [[nodiscard]] auto Version() const;
        auto Version(winrt::Windows::ApplicationModel::PackageVersion const& value) const;
        [[nodiscard]] auto AppInstallerUri() const;
        auto AppInstallerUri(winrt::Windows::Foundation::Uri const& value) const;
        [[nodiscard]] auto OnLaunch() const;
        auto OnLaunch(bool value) const;
        [[nodiscard]] auto HoursBetweenUpdateChecks() const;
        auto HoursBetweenUpdateChecks(uint32_t value) const;
        [[nodiscard]] auto ShowPrompt() const;
        auto ShowPrompt(bool value) const;
        [[nodiscard]] auto UpdateBlocksActivation() const;
        auto UpdateBlocksActivation(bool value) const;
        [[nodiscard]] auto AutomaticBackgroundTask() const;
        auto AutomaticBackgroundTask(bool value) const;
        [[nodiscard]] auto ForceUpdateFromAnyVersion() const;
        auto ForceUpdateFromAnyVersion(bool value) const;
        [[nodiscard]] auto IsAutoRepairEnabled() const;
        auto IsAutoRepairEnabled(bool value) const;
        [[nodiscard]] auto UpdateUris() const;
        [[nodiscard]] auto RepairUris() const;
        [[nodiscard]] auto DependencyPackageUris() const;
        [[nodiscard]] auto OptionalPackageUris() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IAutoUpdateSettingsOptions>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IAutoUpdateSettingsOptions<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IAutoUpdateSettingsOptionsStatics
    {
        auto CreateFromAppInstallerInfo(winrt::Windows::ApplicationModel::AppInstallerInfo const& appInstallerInfo) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IAutoUpdateSettingsOptionsStatics>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IAutoUpdateSettingsOptionsStatics<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_ICreateSharedPackageContainerOptions
    {
        [[nodiscard]] auto Members() const;
        [[nodiscard]] auto ForceAppShutdown() const;
        auto ForceAppShutdown(bool value) const;
        [[nodiscard]] auto CreateCollisionOption() const;
        auto CreateCollisionOption(winrt::Windows::Management::Deployment::SharedPackageContainerCreationCollisionOptions const& value) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::ICreateSharedPackageContainerOptions>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_ICreateSharedPackageContainerOptions<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_ICreateSharedPackageContainerResult
    {
        [[nodiscard]] auto Container() const;
        [[nodiscard]] auto Status() const;
        [[nodiscard]] auto ExtendedError() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::ICreateSharedPackageContainerResult>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_ICreateSharedPackageContainerResult<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IDeleteSharedPackageContainerOptions
    {
        [[nodiscard]] auto ForceAppShutdown() const;
        auto ForceAppShutdown(bool value) const;
        [[nodiscard]] auto AllUsers() const;
        auto AllUsers(bool value) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IDeleteSharedPackageContainerOptions>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IDeleteSharedPackageContainerOptions<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IDeleteSharedPackageContainerResult
    {
        [[nodiscard]] auto Status() const;
        [[nodiscard]] auto ExtendedError() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IDeleteSharedPackageContainerResult>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IDeleteSharedPackageContainerResult<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IDeploymentResult
    {
        [[nodiscard]] auto ErrorText() const;
        [[nodiscard]] auto ActivityId() const;
        [[nodiscard]] auto ExtendedErrorCode() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IDeploymentResult>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IDeploymentResult<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IDeploymentResult2
    {
        [[nodiscard]] auto IsRegistered() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IDeploymentResult2>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IDeploymentResult2<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IFindSharedPackageContainerOptions
    {
        [[nodiscard]] auto Name() const;
        auto Name(param::hstring const& value) const;
        [[nodiscard]] auto PackageFamilyName() const;
        auto PackageFamilyName(param::hstring const& value) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IFindSharedPackageContainerOptions>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IFindSharedPackageContainerOptions<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageAllUserProvisioningOptions
    {
        [[nodiscard]] auto OptionalPackageFamilyNames() const;
        [[nodiscard]] auto ProjectionOrderPackageFamilyNames() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageAllUserProvisioningOptions>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageAllUserProvisioningOptions<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageAllUserProvisioningOptions2
    {
        [[nodiscard]] auto DeferAutomaticRegistration() const;
        auto DeferAutomaticRegistration(bool value) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageAllUserProvisioningOptions2>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageAllUserProvisioningOptions2<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageManager
    {
        auto AddPackageAsync(winrt::Windows::Foundation::Uri const& packageUri, param::async_iterable<winrt::Windows::Foundation::Uri> const& dependencyPackageUris, winrt::Windows::Management::Deployment::DeploymentOptions const& deploymentOptions) const;
        auto UpdatePackageAsync(winrt::Windows::Foundation::Uri const& packageUri, param::async_iterable<winrt::Windows::Foundation::Uri> const& dependencyPackageUris, winrt::Windows::Management::Deployment::DeploymentOptions const& deploymentOptions) const;
        auto RemovePackageAsync(param::hstring const& packageFullName) const;
        auto StagePackageAsync(winrt::Windows::Foundation::Uri const& packageUri, param::async_iterable<winrt::Windows::Foundation::Uri> const& dependencyPackageUris) const;
        auto RegisterPackageAsync(winrt::Windows::Foundation::Uri const& manifestUri, param::async_iterable<winrt::Windows::Foundation::Uri> const& dependencyPackageUris, winrt::Windows::Management::Deployment::DeploymentOptions const& deploymentOptions) const;
        auto FindPackages() const;
        auto FindPackagesForUser(param::hstring const& userSecurityId) const;
        auto FindPackages(param::hstring const& packageName, param::hstring const& packagePublisher) const;
        auto FindPackagesForUser(param::hstring const& userSecurityId, param::hstring const& packageName, param::hstring const& packagePublisher) const;
        auto FindUsers(param::hstring const& packageFullName) const;
        auto SetPackageState(param::hstring const& packageFullName, winrt::Windows::Management::Deployment::PackageState const& packageState) const;
        auto FindPackage(param::hstring const& packageFullName) const;
        auto CleanupPackageForUserAsync(param::hstring const& packageName, param::hstring const& userSecurityId) const;
        auto FindPackages(param::hstring const& packageFamilyName) const;
        auto FindPackagesForUser(param::hstring const& userSecurityId, param::hstring const& packageFamilyName) const;
        auto FindPackageForUser(param::hstring const& userSecurityId, param::hstring const& packageFullName) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageManager>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageManager<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageManager10
    {
        auto ProvisionPackageForAllUsersAsync(param::hstring const& mainPackageFamilyName, winrt::Windows::Management::Deployment::PackageAllUserProvisioningOptions const& options) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageManager10>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageManager10<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageManager11
    {
        auto RemovePackageByUriAsync(winrt::Windows::Foundation::Uri const& packageUri, winrt::Windows::Management::Deployment::RemovePackageOptions const& options) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageManager11>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageManager11<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageManager12
    {
        auto IsPackageRemovalPending(param::hstring const& packageFullName) const;
        auto IsPackageRemovalPendingForUser(param::hstring const& packageFullName, param::hstring const& userSecurityId) const;
        auto IsPackageRemovalPendingByUri(winrt::Windows::Foundation::Uri const& packageUri) const;
        auto IsPackageRemovalPendingByUriForUser(winrt::Windows::Foundation::Uri const& packageUri, param::hstring const& userSecurityId) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageManager12>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageManager12<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageManager2
    {
        auto RemovePackageAsync(param::hstring const& packageFullName, winrt::Windows::Management::Deployment::RemovalOptions const& removalOptions) const;
        auto StagePackageAsync(winrt::Windows::Foundation::Uri const& packageUri, param::async_iterable<winrt::Windows::Foundation::Uri> const& dependencyPackageUris, winrt::Windows::Management::Deployment::DeploymentOptions const& deploymentOptions) const;
        auto RegisterPackageByFullNameAsync(param::hstring const& mainPackageFullName, param::async_iterable<hstring> const& dependencyPackageFullNames, winrt::Windows::Management::Deployment::DeploymentOptions const& deploymentOptions) const;
        auto FindPackagesWithPackageTypes(winrt::Windows::Management::Deployment::PackageTypes const& packageTypes) const;
        auto FindPackagesForUserWithPackageTypes(param::hstring const& userSecurityId, winrt::Windows::Management::Deployment::PackageTypes const& packageTypes) const;
        auto FindPackagesWithPackageTypes(param::hstring const& packageName, param::hstring const& packagePublisher, winrt::Windows::Management::Deployment::PackageTypes const& packageTypes) const;
        auto FindPackagesForUserWithPackageTypes(param::hstring const& userSecurityId, param::hstring const& packageName, param::hstring const& packagePublisher, winrt::Windows::Management::Deployment::PackageTypes const& packageTypes) const;
        auto FindPackagesWithPackageTypes(param::hstring const& packageFamilyName, winrt::Windows::Management::Deployment::PackageTypes const& packageTypes) const;
        auto FindPackagesForUserWithPackageTypes(param::hstring const& userSecurityId, param::hstring const& packageFamilyName, winrt::Windows::Management::Deployment::PackageTypes const& packageTypes) const;
        auto StageUserDataAsync(param::hstring const& packageFullName) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageManager2>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageManager2<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageManager3
    {
        auto AddPackageVolumeAsync(param::hstring const& packageStorePath) const;
        auto AddPackageAsync(winrt::Windows::Foundation::Uri const& packageUri, param::async_iterable<winrt::Windows::Foundation::Uri> const& dependencyPackageUris, winrt::Windows::Management::Deployment::DeploymentOptions const& deploymentOptions, winrt::Windows::Management::Deployment::PackageVolume const& targetVolume) const;
        auto ClearPackageStatus(param::hstring const& packageFullName, winrt::Windows::Management::Deployment::PackageStatus const& status) const;
        auto RegisterPackageAsync(winrt::Windows::Foundation::Uri const& manifestUri, param::async_iterable<winrt::Windows::Foundation::Uri> const& dependencyPackageUris, winrt::Windows::Management::Deployment::DeploymentOptions const& deploymentOptions, winrt::Windows::Management::Deployment::PackageVolume const& appDataVolume) const;
        auto FindPackageVolume(param::hstring const& volumeName) const;
        auto FindPackageVolumes() const;
        auto GetDefaultPackageVolume() const;
        auto MovePackageToVolumeAsync(param::hstring const& packageFullName, winrt::Windows::Management::Deployment::DeploymentOptions const& deploymentOptions, winrt::Windows::Management::Deployment::PackageVolume const& targetVolume) const;
        auto RemovePackageVolumeAsync(winrt::Windows::Management::Deployment::PackageVolume const& volume) const;
        auto SetDefaultPackageVolume(winrt::Windows::Management::Deployment::PackageVolume const& volume) const;
        auto SetPackageStatus(param::hstring const& packageFullName, winrt::Windows::Management::Deployment::PackageStatus const& status) const;
        auto SetPackageVolumeOfflineAsync(winrt::Windows::Management::Deployment::PackageVolume const& packageVolume) const;
        auto SetPackageVolumeOnlineAsync(winrt::Windows::Management::Deployment::PackageVolume const& packageVolume) const;
        auto StagePackageAsync(winrt::Windows::Foundation::Uri const& packageUri, param::async_iterable<winrt::Windows::Foundation::Uri> const& dependencyPackageUris, winrt::Windows::Management::Deployment::DeploymentOptions const& deploymentOptions, winrt::Windows::Management::Deployment::PackageVolume const& targetVolume) const;
        auto StageUserDataAsync(param::hstring const& packageFullName, winrt::Windows::Management::Deployment::DeploymentOptions const& deploymentOptions) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageManager3>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageManager3<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageManager4
    {
        auto GetPackageVolumesAsync() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageManager4>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageManager4<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageManager5
    {
        auto AddPackageAsync(winrt::Windows::Foundation::Uri const& packageUri, param::async_iterable<winrt::Windows::Foundation::Uri> const& dependencyPackageUris, winrt::Windows::Management::Deployment::DeploymentOptions const& deploymentOptions, winrt::Windows::Management::Deployment::PackageVolume const& targetVolume, param::async_iterable<hstring> const& optionalPackageFamilyNames, param::async_iterable<winrt::Windows::Foundation::Uri> const& externalPackageUris) const;
        auto StagePackageAsync(winrt::Windows::Foundation::Uri const& packageUri, param::async_iterable<winrt::Windows::Foundation::Uri> const& dependencyPackageUris, winrt::Windows::Management::Deployment::DeploymentOptions const& deploymentOptions, winrt::Windows::Management::Deployment::PackageVolume const& targetVolume, param::async_iterable<hstring> const& optionalPackageFamilyNames, param::async_iterable<winrt::Windows::Foundation::Uri> const& externalPackageUris) const;
        auto RegisterPackageByFamilyNameAsync(param::hstring const& mainPackageFamilyName, param::async_iterable<hstring> const& dependencyPackageFamilyNames, winrt::Windows::Management::Deployment::DeploymentOptions const& deploymentOptions, winrt::Windows::Management::Deployment::PackageVolume const& appDataVolume, param::async_iterable<hstring> const& optionalPackageFamilyNames) const;
        [[nodiscard]] auto DebugSettings() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageManager5>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageManager5<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageManager6
    {
        auto ProvisionPackageForAllUsersAsync(param::hstring const& packageFamilyName) const;
        auto AddPackageByAppInstallerFileAsync(winrt::Windows::Foundation::Uri const& appInstallerFileUri, winrt::Windows::Management::Deployment::AddPackageByAppInstallerOptions const& options, winrt::Windows::Management::Deployment::PackageVolume const& targetVolume) const;
        auto RequestAddPackageByAppInstallerFileAsync(winrt::Windows::Foundation::Uri const& appInstallerFileUri, winrt::Windows::Management::Deployment::AddPackageByAppInstallerOptions const& options, winrt::Windows::Management::Deployment::PackageVolume const& targetVolume) const;
        auto AddPackageAsync(winrt::Windows::Foundation::Uri const& packageUri, param::async_iterable<winrt::Windows::Foundation::Uri> const& dependencyPackageUris, winrt::Windows::Management::Deployment::DeploymentOptions const& options, winrt::Windows::Management::Deployment::PackageVolume const& targetVolume, param::async_iterable<hstring> const& optionalPackageFamilyNames, param::async_iterable<winrt::Windows::Foundation::Uri> const& packageUrisToInstall, param::async_iterable<winrt::Windows::Foundation::Uri> const& relatedPackageUris) const;
        auto StagePackageAsync(winrt::Windows::Foundation::Uri const& packageUri, param::async_iterable<winrt::Windows::Foundation::Uri> const& dependencyPackageUris, winrt::Windows::Management::Deployment::DeploymentOptions const& options, winrt::Windows::Management::Deployment::PackageVolume const& targetVolume, param::async_iterable<hstring> const& optionalPackageFamilyNames, param::async_iterable<winrt::Windows::Foundation::Uri> const& packageUrisToInstall, param::async_iterable<winrt::Windows::Foundation::Uri> const& relatedPackageUris) const;
        auto RequestAddPackageAsync(winrt::Windows::Foundation::Uri const& packageUri, param::async_iterable<winrt::Windows::Foundation::Uri> const& dependencyPackageUris, winrt::Windows::Management::Deployment::DeploymentOptions const& deploymentOptions, winrt::Windows::Management::Deployment::PackageVolume const& targetVolume, param::async_iterable<hstring> const& optionalPackageFamilyNames, param::async_iterable<winrt::Windows::Foundation::Uri> const& relatedPackageUris) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageManager6>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageManager6<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageManager7
    {
        auto RequestAddPackageAsync(winrt::Windows::Foundation::Uri const& packageUri, param::async_iterable<winrt::Windows::Foundation::Uri> const& dependencyPackageUris, winrt::Windows::Management::Deployment::DeploymentOptions const& deploymentOptions, winrt::Windows::Management::Deployment::PackageVolume const& targetVolume, param::async_iterable<hstring> const& optionalPackageFamilyNames, param::async_iterable<winrt::Windows::Foundation::Uri> const& relatedPackageUris, param::async_iterable<winrt::Windows::Foundation::Uri> const& packageUrisToInstall) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageManager7>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageManager7<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageManager8
    {
        auto DeprovisionPackageForAllUsersAsync(param::hstring const& packageFamilyName) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageManager8>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageManager8<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageManager9
    {
        auto FindProvisionedPackages() const;
        auto AddPackageByUriAsync(winrt::Windows::Foundation::Uri const& packageUri, winrt::Windows::Management::Deployment::AddPackageOptions const& options) const;
        auto StagePackageByUriAsync(winrt::Windows::Foundation::Uri const& packageUri, winrt::Windows::Management::Deployment::StagePackageOptions const& options) const;
        auto RegisterPackageByUriAsync(winrt::Windows::Foundation::Uri const& manifestUri, winrt::Windows::Management::Deployment::RegisterPackageOptions const& options) const;
        auto RegisterPackagesByFullNameAsync(param::async_iterable<hstring> const& packageFullNames, winrt::Windows::Management::Deployment::RegisterPackageOptions const& options) const;
        auto SetPackageStubPreference(param::hstring const& packageFamilyName, winrt::Windows::Management::Deployment::PackageStubPreference const& useStub) const;
        auto GetPackageStubPreference(param::hstring const& packageFamilyName) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageManager9>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageManager9<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageManagerDebugSettings
    {
        auto SetContentGroupStateAsync(winrt::Windows::ApplicationModel::Package const& package, param::hstring const& contentGroupName, winrt::Windows::ApplicationModel::PackageContentGroupState const& state) const;
        auto SetContentGroupStateAsync(winrt::Windows::ApplicationModel::Package const& package, param::hstring const& contentGroupName, winrt::Windows::ApplicationModel::PackageContentGroupState const& state, double completionPercentage) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageManagerDebugSettings>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageManagerDebugSettings<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageUserInformation
    {
        [[nodiscard]] auto UserSecurityId() const;
        [[nodiscard]] auto InstallState() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageUserInformation>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageUserInformation<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageVolume
    {
        [[nodiscard]] auto IsOffline() const;
        [[nodiscard]] auto IsSystemVolume() const;
        [[nodiscard]] auto MountPoint() const;
        [[nodiscard]] auto Name() const;
        [[nodiscard]] auto PackageStorePath() const;
        [[nodiscard]] auto SupportsHardLinks() const;
        auto FindPackages() const;
        auto FindPackages(param::hstring const& packageName, param::hstring const& packagePublisher) const;
        auto FindPackages(param::hstring const& packageFamilyName) const;
        auto FindPackagesWithPackageTypes(winrt::Windows::Management::Deployment::PackageTypes const& packageTypes) const;
        auto FindPackagesWithPackageTypes(winrt::Windows::Management::Deployment::PackageTypes const& packageTypes, param::hstring const& packageName, param::hstring const& packagePublisher) const;
        auto FindPackagesWithPackageTypes(winrt::Windows::Management::Deployment::PackageTypes const& packageTypes, param::hstring const& packageFamilyName) const;
        auto FindPackage(param::hstring const& packageFullName) const;
        auto FindPackagesForUser(param::hstring const& userSecurityId) const;
        auto FindPackagesForUser(param::hstring const& userSecurityId, param::hstring const& packageName, param::hstring const& packagePublisher) const;
        auto FindPackagesForUser(param::hstring const& userSecurityId, param::hstring const& packageFamilyName) const;
        auto FindPackagesForUserWithPackageTypes(param::hstring const& userSecurityId, winrt::Windows::Management::Deployment::PackageTypes const& packageTypes) const;
        auto FindPackagesForUserWithPackageTypes(param::hstring const& userSecurityId, winrt::Windows::Management::Deployment::PackageTypes const& packageTypes, param::hstring const& packageName, param::hstring const& packagePublisher) const;
        auto FindPackagesForUserWithPackageTypes(param::hstring const& userSecurityId, winrt::Windows::Management::Deployment::PackageTypes const& packageTypes, param::hstring const& packageFamilyName) const;
        auto FindPackageForUser(param::hstring const& userSecurityId, param::hstring const& packageFullName) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageVolume>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageVolume<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IPackageVolume2
    {
        [[nodiscard]] auto IsFullTrustPackageSupported() const;
        [[nodiscard]] auto IsAppxInstallSupported() const;
        auto GetAvailableSpaceAsync() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IPackageVolume2>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IPackageVolume2<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IRegisterPackageOptions
    {
        [[nodiscard]] auto DependencyPackageUris() const;
        [[nodiscard]] auto AppDataVolume() const;
        auto AppDataVolume(winrt::Windows::Management::Deployment::PackageVolume const& value) const;
        [[nodiscard]] auto OptionalPackageFamilyNames() const;
        [[nodiscard]] auto ExternalLocationUri() const;
        auto ExternalLocationUri(winrt::Windows::Foundation::Uri const& value) const;
        [[nodiscard]] auto DeveloperMode() const;
        auto DeveloperMode(bool value) const;
        [[nodiscard]] auto ForceAppShutdown() const;
        auto ForceAppShutdown(bool value) const;
        [[nodiscard]] auto ForceTargetAppShutdown() const;
        auto ForceTargetAppShutdown(bool value) const;
        [[nodiscard]] auto ForceUpdateFromAnyVersion() const;
        auto ForceUpdateFromAnyVersion(bool value) const;
        [[nodiscard]] auto InstallAllResources() const;
        auto InstallAllResources(bool value) const;
        [[nodiscard]] auto StageInPlace() const;
        auto StageInPlace(bool value) const;
        [[nodiscard]] auto AllowUnsigned() const;
        auto AllowUnsigned(bool value) const;
        [[nodiscard]] auto DeferRegistrationWhenPackagesAreInUse() const;
        auto DeferRegistrationWhenPackagesAreInUse(bool value) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IRegisterPackageOptions>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IRegisterPackageOptions<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IRegisterPackageOptions2
    {
        [[nodiscard]] auto ExpectedDigests() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IRegisterPackageOptions2>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IRegisterPackageOptions2<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IRemovePackageOptions
    {
        [[nodiscard]] auto PreserveApplicationData() const;
        auto PreserveApplicationData(bool value) const;
        [[nodiscard]] auto PreserveRoamableApplicationData() const;
        auto PreserveRoamableApplicationData(bool value) const;
        [[nodiscard]] auto RemoveForAllUsers() const;
        auto RemoveForAllUsers(bool value) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IRemovePackageOptions>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IRemovePackageOptions<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IRemovePackageOptions2
    {
        [[nodiscard]] auto DeferRemovalWhenPackagesAreInUse() const;
        auto DeferRemovalWhenPackagesAreInUse(bool value) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IRemovePackageOptions2>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IRemovePackageOptions2<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_ISharedPackageContainer
    {
        [[nodiscard]] auto Name() const;
        [[nodiscard]] auto Id() const;
        auto GetMembers() const;
        auto RemovePackageFamily(param::hstring const& packageFamilyName, winrt::Windows::Management::Deployment::UpdateSharedPackageContainerOptions const& options) const;
        auto ResetData() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::ISharedPackageContainer>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_ISharedPackageContainer<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_ISharedPackageContainerManager
    {
        auto CreateContainer(param::hstring const& name, winrt::Windows::Management::Deployment::CreateSharedPackageContainerOptions const& options) const;
        auto DeleteContainer(param::hstring const& id, winrt::Windows::Management::Deployment::DeleteSharedPackageContainerOptions const& options) const;
        auto GetContainer(param::hstring const& id) const;
        auto FindContainers() const;
        auto FindContainers(winrt::Windows::Management::Deployment::FindSharedPackageContainerOptions const& options) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::ISharedPackageContainerManager>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_ISharedPackageContainerManager<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_ISharedPackageContainerManagerStatics
    {
        auto GetDefault() const;
        auto GetForUser(param::hstring const& userSid) const;
        auto GetForProvisioning() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::ISharedPackageContainerManagerStatics>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_ISharedPackageContainerManagerStatics<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_ISharedPackageContainerMember
    {
        [[nodiscard]] auto PackageFamilyName() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::ISharedPackageContainerMember>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_ISharedPackageContainerMember<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_ISharedPackageContainerMemberFactory
    {
        auto CreateInstance(param::hstring const& packageFamilyName) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::ISharedPackageContainerMemberFactory>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_ISharedPackageContainerMemberFactory<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IStagePackageOptions
    {
        [[nodiscard]] auto DependencyPackageUris() const;
        [[nodiscard]] auto TargetVolume() const;
        auto TargetVolume(winrt::Windows::Management::Deployment::PackageVolume const& value) const;
        [[nodiscard]] auto OptionalPackageFamilyNames() const;
        [[nodiscard]] auto OptionalPackageUris() const;
        [[nodiscard]] auto RelatedPackageUris() const;
        [[nodiscard]] auto ExternalLocationUri() const;
        auto ExternalLocationUri(winrt::Windows::Foundation::Uri const& value) const;
        [[nodiscard]] auto StubPackageOption() const;
        auto StubPackageOption(winrt::Windows::Management::Deployment::StubPackageOption const& value) const;
        [[nodiscard]] auto DeveloperMode() const;
        auto DeveloperMode(bool value) const;
        [[nodiscard]] auto ForceUpdateFromAnyVersion() const;
        auto ForceUpdateFromAnyVersion(bool value) const;
        [[nodiscard]] auto InstallAllResources() const;
        auto InstallAllResources(bool value) const;
        [[nodiscard]] auto RequiredContentGroupOnly() const;
        auto RequiredContentGroupOnly(bool value) const;
        [[nodiscard]] auto StageInPlace() const;
        auto StageInPlace(bool value) const;
        [[nodiscard]] auto AllowUnsigned() const;
        auto AllowUnsigned(bool value) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IStagePackageOptions>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IStagePackageOptions<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IStagePackageOptions2
    {
        [[nodiscard]] auto ExpectedDigests() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IStagePackageOptions2>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IStagePackageOptions2<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IUpdateSharedPackageContainerOptions
    {
        [[nodiscard]] auto ForceAppShutdown() const;
        auto ForceAppShutdown(bool value) const;
        [[nodiscard]] auto RequirePackagesPresent() const;
        auto RequirePackagesPresent(bool value) const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IUpdateSharedPackageContainerOptions>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IUpdateSharedPackageContainerOptions<D>;
    };
    template <typename D>
    struct consume_Windows_Management_Deployment_IUpdateSharedPackageContainerResult
    {
        [[nodiscard]] auto Status() const;
        [[nodiscard]] auto ExtendedError() const;
    };
    template <> struct consume<winrt::Windows::Management::Deployment::IUpdateSharedPackageContainerResult>
    {
        template <typename D> using type = consume_Windows_Management_Deployment_IUpdateSharedPackageContainerResult<D>;
    };
    struct struct_Windows_Management_Deployment_DeploymentProgress
    {
        int32_t state;
        uint32_t percentage;
    };
    template <> struct abi<Windows::Management::Deployment::DeploymentProgress>
    {
        using type = struct_Windows_Management_Deployment_DeploymentProgress;
    };
}
#endif
