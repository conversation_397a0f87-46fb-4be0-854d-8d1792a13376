@incollection{<PERSON><PERSON>wal08,
  title = {Censure: Center surround extremas for realtime feature detection and matching},
  author = {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>},
  booktitle = {Computer Vision--ECCV 2008},
  pages = {102--115},
  year = {2008},
  publisher = {<PERSON>}
}

@inproceedings{AOV12,
  title = {Freak: Fast retina keypoint},
  author = {<PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
  booktitle = {Computer Vision and Pattern Recognition (CVPR), 2012 IEEE Conference on},
  pages = {510--517},
  year = {2012},
  organization = {IEEE}
}

@article{Bay06,
  title = {Surf: Speeded up robust features},
  author = {<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, Luc},
  journal = {Computer Vision--ECCV 2006},
  pages = {404--417},
  year = {2006},
  publisher = {Springer Berlin Heidelberg}
}

@inproceedings{BeecksUS10,
  author = {<PERSON> and <PERSON><PERSON><PERSON> and <PERSON>},
  title = {Signature Quadratic Form Distance},
  booktitle = {CIVR},
  pages = {438--445},
  publisher = {ACM},
  year = {2010}
}

@inproceedings{Bian2017gms,
  title = {GMS: Grid-based Motion Statistics for Fast, Ultra-robust Feature Correspondence},
  author = {JiaWang Bian and Wen-Yan Lin and Yasuyuki Matsushita and Sai-Kit Yeung and Tan Dat Nguyen and Ming-Ming Cheng},
  booktitle = {IEEE Conference on Computer Vision and Pattern Recognition},
  year = {2017}
}

@incollection{calon2010,
  title = {Brief: Binary robust independent elementary features},
  author = {Calonder, Michael and Lepetit, Vincent and Strecha, Christoph and Fua, Pascal},
  booktitle = {Computer Vision--ECCV 2010},
  pages = {778--792},
  year = {2010},
  publisher = {Springer}
}

@article{KrulisLS16,
  author = {Martin Krulis and Jakub Lokoc and Tomas Skopal},
  title = {Efficient extraction of clustering-based feature signatures using {GPU} architectures},
  journal = {Multimedia Tools Appl.},
  volume = {75},
  number = {13},
  pages = {8071--8103},
  year = {2016}
}

@article{Lowry2018LOGOSLG,
  title = {LOGOS: Local Geometric Support for High-Outlier Spatial Verification},
  author = {Stephanie Lowry and Henrik Andreasson},
  journal = {2018 IEEE International Conference on Robotics and Automation (ICRA)},
  year = {2018},
  pages = {7262-7269},
  doi = {10.1109/ICRA.2018.8460988},
}

@article{Mikolajczyk2004,
  title = {Scale \& affine invariant interest point detectors},
  author = {Mikolajczyk, Krystian and Schmid, Cordelia},
  journal = {International journal of computer vision},
  volume = {60},
  number = {1},
  pages = {63--86},
  year = {2004},
  publisher = {Springer}
}

@ARTICLE{Najman2014,
  author={Y. {Xu} and P. {Monasse} and T. {Géraud} and L. {Najman}},
  journal={IEEE Transactions on Image Processing},
  title={Tree-Based Morse Regions: A Topological Approach to Local Feature Detection},
  year={2014},
  volume={23},
  number={12},
  pages={5612-5625},
  abstract={This paper introduces a topological approach to local invariant feature detection motivated by Morse theory. We use the critical points of the graph of the intensity image, revealing directly the topology information as initial interest points. Critical points are selected from what we call a tree-based shape-space. In particular, they are selected from both the connected components of the upper level sets of the image (the Max-tree) and those of the lower level sets (the Min-tree). They correspond to specific nodes on those two trees: 1) to the leaves (extrema) and 2) to the nodes having bifurcation (saddle points). We then associate to each critical point the largest region that contains it and is topologically equivalent in its tree. We call such largest regions the tree-based Morse regions (TBMRs). The TBMR can be seen as a variant of maximally stable extremal region (MSER), which are contrasted regions. Contrarily to MSER, TBMR relies only on topological information and thus fully inherit the invariance properties of the space of shapes (e.g., invariance to affine contrast changes and covariance to continuous transformations). In particular, TBMR extracts the regions independently of the contrast, which makes it truly contrast invariant. Furthermore, it is quasi-parameter free. TBMR extraction is fast, having the same complexity as MSER. Experimentally, TBMR achieves a repeatability on par with state-of-the-art methods, but obtains a significantly higher number of features. Both the accuracy and robustness of TBMR are demonstrated by applications to image registration and 3D reconstruction.},
  keywords={feature extraction;image reconstruction;image registration;trees (mathematics);tree-based Morse regions;topological approach;local invariant feature detection;Morse theory;intensity image;initial interest points;critical points;tree-based shape-space;upper level image sets;Max-tree;lower level sets;Min-tree;saddle points;bifurcation;maximally stable extremal region variant;MSER;topological information;TBMR extraction;3D reconstruction;image registration;Feature extraction;Detectors;Shape;Time complexity;Level set;Three-dimensional displays;Image registration;Min/Max tree;local features;affine region detectors;image registration;3D reconstruction;Min/Max tree;local features;affine region detectors;image registration;3D reconstruction},
  doi={10.1109/TIP.2014.2364127},
  ISSN={1941-0042},
  month={Dec},}

@article{Simonyan14,
  author = {Simonyan, K. and Vedaldi, A. and Zisserman, A.},
  title = {Learning Local Feature Descriptors Using Convex Optimisation},
  journal = {IEEE Transactions on Pattern Analysis and Machine Intelligence},
  year = {2014}
}

@article{Tola10,
  author = {E. Tola and V. Lepetit and P. Fua},
  title = {DAISY: An Efficient Dense Descriptor Applied to Wide Baseline Stereo},
  journal = {IEEE Transactions on Pattern Analysis and Machine Intelligence},
  year = {2010},
  month = {May},
  pages = {815--830},
  volume = {32},
  number = {5}
}

@inproceedings{Tombari14,
  title = {Interest Points via Maximal Self-Dissimilarities},
  author = {Tombari, Federico and Di Stefano, Luigi},
  booktitle = {Asian Conference on Computer Vision -- ACCV 2014},
  year = {2014}
}

@inproceedings{Trzcinski13a,
  author = {T. Trzcinski, M. Christoudias, V. Lepetit and P. Fua},
  title = {Boosting Binary Keypoint Descriptors},
  booktitle = {Computer Vision and Pattern Recognition},
  year = {2013}
}

@article{Trzcinski13b,
  author = {T. Trzcinski, M. Christoudias and V. Lepetit},
  title = {Learning Image Descriptors with Boosting},
  journal = {IEEE Transactions on Pattern Analysis and Machine Intelligence (PAMI)},
  year = {2013}
}

@incollection{LUCID,
  title = {Locally uniform comparison image descriptor},
  author = {Ziegler, Andrew, Eric Christiansen, David Kriegman, and Serge J. Belongie}
  booktitle = {Advances in Neural Information Processing Systems}
  pages = {1--9}
  year = {2012}
  publisher = {NIPS}
}

@article{Suarez2020BEBLID,
  title = {{BEBLID: Boosted Efficient Binary Local Image Descriptor}},
  journal = {Pattern Recognition Letters},
  volume = {133},
  pages = {366--372},
  year = {2020},
  issn = {0167-8655},
  doi = {https://doi.org/10.1016/j.patrec.2020.04.005},
  url = {https://raw.githubusercontent.com/iago-suarez/BEBLID/master/BEBLID_Boosted_Efficient_Binary_Local_Image_Descriptor.pdf},
  author = {Iago Su\'arez and Ghesn Sfeir and Jos\'e M. Buenaposada and Luis Baumela},
}

@article{Suarez2021TEBLID,
  title = {Revisiting Binary Local Image Description for Resource Limited Devices},
  journal = {IEEE Robotics and Automation Letters},
  volume = {6},
  pages = {8317--8324},
  year = {2021},
  number = {4},
  doi = {https://doi.org/10.1109/LRA.2021.3107024},
  url = {https://arxiv.org/pdf/2108.08380.pdf},
  author = {Iago Su\'arez and Jos\'e M. Buenaposada and Luis Baumela},
}

@inproceedings{winder2007learning,
  title= {Learning Local Image Descriptors},
  author= {Winder, Simon AJ and Brown, Matthew},
  booktitle= {Computer Vision and Pattern Recognition},
  pages={1--8},
  year={2007},
}