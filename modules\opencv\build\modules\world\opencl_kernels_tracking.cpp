// This file is auto-generated. Do not edit!

#include "opencv2/core.hpp"
#include "cvconfig.h"
#include "opencl_kernels_tracking.hpp"

#ifdef HAVE_OPENCL

namespace cv
{
namespace ocl
{
namespace tracking
{

static const char* const moduleName = "tracking";

struct cv::ocl::internal::ProgramEntry tldDetector_oclsrc={moduleName, "tldDetector",
"__kernel void NCC(__global const uchar *patch,\n"
"__global const uchar *positiveSamples,\n"
"__global const uchar *negativeSamples,\n"
"__global float *ncc,\n"
"int posNum,\n"
"int negNum)\n"
"{\n"
"int id = get_global_id(0);\n"
"if (id >= 1000) return;\n"
"bool posFlg;\n"
"if (id < 500)\n"
"posFlg = true;\n"
"if (id >= 500)\n"
"{\n"
"id = id - 500;\n"
"posFlg = false;\n"
"}\n"
"int s1 = 0, s2 = 0, n1 = 0, n2 = 0, prod = 0;\n"
"float sq1 = 0, sq2 = 0, ares = 0;\n"
"int N = 225;\n"
"if (posFlg && id < posNum)\n"
"{\n"
"for (int i = 0; i < N; i++)\n"
"{\n"
"s1 += positiveSamples[id * N + i];\n"
"s2 += patch[i];\n"
"n1 += positiveSamples[id * N + i] * positiveSamples[id * N + i];\n"
"n2 += patch[i] * patch[i];\n"
"prod += positiveSamples[id * N + i] * patch[i];\n"
"}\n"
"sq1 = sqrt(max(0.0, n1 - 1.0 * s1 * s1 / N));\n"
"sq2 = sqrt(max(0.0, n2 - 1.0 * s2 * s2 / N));\n"
"ares = (sq2 == 0) ? sq1 / fabs(sq1) : (prod - s1 * s2 / N) / sq1 / sq2;\n"
"ncc[id] = ares;\n"
"}\n"
"if (!posFlg && id < negNum)\n"
"{\n"
"for (int i = 0; i < N; i++)\n"
"{\n"
"s1 += negativeSamples[id * N + i];\n"
"s2 += patch[i];\n"
"n1 += negativeSamples[id * N + i] * negativeSamples[id * N + i];\n"
"n2 += patch[i] * patch[i];\n"
"prod += negativeSamples[id * N + i] * patch[i];\n"
"}\n"
"sq1 = sqrt(max(0.0, n1 - 1.0 * s1 * s1 / N));\n"
"sq2 = sqrt(max(0.0, n2 - 1.0 * s2 * s2 / N));\n"
"ares = (sq2 == 0) ? sq1 / fabs(sq1) : (prod - s1 * s2 / N) / sq1 / sq2;\n"
"ncc[id+500] = ares;\n"
"}\n"
"}\n"
"__kernel void batchNCC(__global const uchar *patches,\n"
"__global const uchar *positiveSamples,\n"
"__global const uchar *negativeSamples,\n"
"__global float *posNcc,\n"
"__global float *negNcc,\n"
"int posNum,\n"
"int negNum,\n"
"int patchNum)\n"
"{\n"
"int id = get_global_id(0);\n"
"bool posFlg;\n"
"if (id < 500*patchNum)\n"
"posFlg = true;\n"
"if (id >= 500*patchNum)\n"
"{\n"
"id = id - 500*patchNum;\n"
"posFlg = false;\n"
"}\n"
"int modelSampleID = id % 500;\n"
"int patchID = id / 500;\n"
"int s1 = 0, s2 = 0, n1 = 0, n2 = 0, prod = 0;\n"
"float sq1 = 0, sq2 = 0, ares = 0;\n"
"int N = 225;\n"
"if (posFlg && modelSampleID < posNum)\n"
"{\n"
"for (int i = 0; i < N; i++)\n"
"{\n"
"s1 += positiveSamples[modelSampleID * N + i];\n"
"s2 += patches[patchID*N + i];\n"
"n1 += positiveSamples[modelSampleID * N + i] * positiveSamples[modelSampleID * N + i];\n"
"n2 += patches[patchID*N + i] * patches[patchID*N + i];\n"
"prod += positiveSamples[modelSampleID * N + i] * patches[patchID*N + i];\n"
"}\n"
"sq1 = sqrt(max(0.0, n1 - 1.0 * s1 * s1 / N));\n"
"sq2 = sqrt(max(0.0, n2 - 1.0 * s2 * s2 / N));\n"
"ares = (sq2 == 0) ? sq1 / fabs(sq1) : (prod - s1 * s2 / N) / sq1 / sq2;\n"
"posNcc[id] = ares;\n"
"}\n"
"if (!posFlg && modelSampleID < negNum)\n"
"{\n"
"for (int i = 0; i < N; i++)\n"
"{\n"
"s1 += negativeSamples[modelSampleID * N + i];\n"
"s2 += patches[patchID*N + i];\n"
"n1 += negativeSamples[modelSampleID * N + i] * negativeSamples[modelSampleID * N + i];\n"
"n2 += patches[patchID*N + i] * patches[patchID*N + i];\n"
"prod += negativeSamples[modelSampleID * N + i] * patches[patchID*N + i];\n"
"}\n"
"sq1 = sqrt(max(0.0, n1 - 1.0 * s1 * s1 / N));\n"
"sq2 = sqrt(max(0.0, n2 - 1.0 * s2 * s2 / N));\n"
"ares = (sq2 == 0) ? sq1 / fabs(sq1) : (prod - s1 * s2 / N) / sq1 / sq2;\n"
"negNcc[id] = ares;\n"
"}\n"
"}\n"
, "f15ba33f44bb0051df83a2b52cf9d67f", NULL};
struct cv::ocl::internal::ProgramEntry tmm_oclsrc={moduleName, "tmm",
"#define LOCAL_SIZE_X 64\n"
"#define BLOCK_SIZE_X  3\n"
"__kernel void tmm(__global float *A, int m, int n, float alpha, __global float *D)\n"
"{\n"
"int lidX = get_local_id(0);\n"
"uint lsizeX = get_local_size(0);\n"
"uint matI = get_group_id(1);\n"
"uint matJ = get_group_id(0);\n"
"if (matI < matJ)\n"
"return;\n"
"__local float4 a[LOCAL_SIZE_X], b[LOCAL_SIZE_X];\n"
"float4 result;\n"
"__local uint cnt;\n"
"result = 0;\n"
"cnt = 0;\n"
"barrier(CLK_LOCAL_MEM_FENCE);\n"
"do {\n"
"int global_block_base = (lidX + cnt * lsizeX) * BLOCK_SIZE_X;\n"
"float4 pa[BLOCK_SIZE_X], pb[BLOCK_SIZE_X];\n"
"#pragma unroll\n"
"for(uint j = 0; j < BLOCK_SIZE_X && (cnt * lsizeX + lidX) * BLOCK_SIZE_X < n / 4; j++) {\n"
"pa[j] = *(__global float4*)&A[matI * n + (global_block_base + j) * 4];\n"
"if (matI != matJ)\n"
"pb[j] = *(__global float4*)&A[matJ * n + (global_block_base + j) * 4];\n"
"else\n"
"pb[j] = pa[j];\n"
"}\n"
"if (global_block_base + BLOCK_SIZE_X - 1 >= n/4) {\n"
"#pragma unroll\n"
"for(int i = 0; i < BLOCK_SIZE_X; i++) {\n"
"if (global_block_base + i >= n/4)\n"
"pb[i] = 0;\n"
"}\n"
"}\n"
"pb[0] *= pa[0];\n"
"for(int j = 1; j < BLOCK_SIZE_X; j++)\n"
"pb[0] =  fma(pb[j], pa[j], pb[0]);\n"
"b[lidX] = pb[0];\n"
"barrier(CLK_LOCAL_MEM_FENCE);\n"
"for(int offset = LOCAL_SIZE_X / 2; offset > 0; offset >>= 1) {\n"
"if (lidX < offset)\n"
"b[lidX] += b[(lidX + offset)];\n"
"barrier(CLK_LOCAL_MEM_FENCE);\n"
"}\n"
"if (lidX == 0) {\n"
"result += b[0];\n"
"cnt++;\n"
"}\n"
"barrier(CLK_LOCAL_MEM_FENCE);\n"
"} while(cnt * BLOCK_SIZE_X * lsizeX < n / 4);\n"
"if (lidX == 0) {\n"
"float ret = (result.s0 + result.s1 + result.s2 + result.s3) * alpha;\n"
"D[matI * m + matJ] = ret;\n"
"if (matI != matJ)\n"
"D[matJ * m + matI] = ret;\n"
"}\n"
"}\n"
, "1968d33b7c3068a6469896acdba60dee", NULL};

}}}
#endif
