// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_AI_Actions_Provider_0_H
#define WINRT_Windows_AI_Actions_Provider_0_H
WINRT_EXPORT namespace winrt::Windows::AI::Actions
{
    struct ActionFeedback;
    struct ActionInvocationContext;
}
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    struct IAsyncAction;
}
WINRT_EXPORT namespace winrt::Windows::AI::Actions::Provider
{
    struct IActionFeedbackHandler;
    struct IActionProvider;
}
namespace winrt::impl
{
    template <> struct category<winrt::Windows::AI::Actions::Provider::IActionFeedbackHandler>{ using type = interface_category; };
    template <> struct category<winrt::Windows::AI::Actions::Provider::IActionProvider>{ using type = interface_category; };
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::Provider::IActionFeedbackHandler> = L"Windows.AI.Actions.Provider.IActionFeedbackHandler";
    template <> inline constexpr auto& name_v<winrt::Windows::AI::Actions::Provider::IActionProvider> = L"Windows.AI.Actions.Provider.IActionProvider";
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::Provider::IActionFeedbackHandler>{ 0xA3FC3C51,0xA8C6,0x52C8,{ 0xAD,0x77,0x37,0xBF,0x3E,0x2B,0x56,0x5C } }; // A3FC3C51-A8C6-52C8-AD77-37BF3E2B565C
    template <> inline constexpr guid guid_v<winrt::Windows::AI::Actions::Provider::IActionProvider>{ 0x62906C47,0x3D07,0x55F1,{ 0xAE,0xFA,0x15,0x22,0x50,0x5A,0xFB,0xBE } }; // 62906C47-3D07-55F1-AEFA-1522505AFBBE
    template <> struct abi<winrt::Windows::AI::Actions::Provider::IActionFeedbackHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall ProcessFeedbackAsync(void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::AI::Actions::Provider::IActionProvider>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall InvokeAsync(void*, void**) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Windows_AI_Actions_Provider_IActionFeedbackHandler
    {
        auto ProcessFeedbackAsync(winrt::Windows::AI::Actions::ActionInvocationContext const& context, winrt::Windows::AI::Actions::ActionFeedback const& feedback) const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::Provider::IActionFeedbackHandler>
    {
        template <typename D> using type = consume_Windows_AI_Actions_Provider_IActionFeedbackHandler<D>;
    };
    template <typename D>
    struct consume_Windows_AI_Actions_Provider_IActionProvider
    {
        auto InvokeAsync(winrt::Windows::AI::Actions::ActionInvocationContext const& context) const;
    };
    template <> struct consume<winrt::Windows::AI::Actions::Provider::IActionProvider>
    {
        template <typename D> using type = consume_Windows_AI_Actions_Provider_IActionProvider<D>;
    };
}
#endif
