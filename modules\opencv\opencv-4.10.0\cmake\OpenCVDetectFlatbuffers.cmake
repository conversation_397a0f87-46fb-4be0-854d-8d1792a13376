if(WITH_FLATBUFFERS)
  set(HAVE_FLATBUFFERS 1)
  set(flatbuffers_VERSION "23.5.9")
  ocv_install_3rdparty_licenses(flatbuffers "${OpenCV_SOURCE_DIR}/3rdparty/flatbuffers/LICENSE.txt")
  ocv_add_external_target(flatbuffers "${OpenCV_SOURCE_DIR}/3rdparty/flatbuffers/include" "" "HAVE_FLATBUFFERS=1")
  set(CUSTOM_STATUS_flatbuffers "    Flatbuffers:" "builtin/3rdparty (${flatbuffers_VERSION})")
endif()

if(WITH_FLATBUFFERS OR HAVE_FLATBUFFERS)
  list(APPEND CUSTOM_STATUS flatbuffers)

  if(HAVE_FLATBUFFERS)
    if(NOT CUSTOM_STATUS_flatbuffers)
      list(APPEND CUSTOM_STATUS_flatbuffers "    Flatbuffers:" "${flatbuffers_VERSION}")
    endif()
  else()
    list(APPEND CUSTOM_STATUS_flatbuffers "    Flatbuffers:" "NO")
  endif()
endif()
