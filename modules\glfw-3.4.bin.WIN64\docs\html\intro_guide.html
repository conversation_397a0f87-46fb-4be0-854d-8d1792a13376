<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.8"/>
<title>GLFW: Introduction to the API</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<link href="extra.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
	<div class="glfwheader">
		<a href="https://www.glfw.org/" id="glfwhome">GLFW</a>
		<ul class="glfwnavbar">
			<li><a href="https://www.glfw.org/documentation.html">Documentation</a></li>
			<li><a href="https://www.glfw.org/download.html">Download</a></li>
			<li><a href="https://www.glfw.org/community.html">Community</a></li>
		</ul>
	</div>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.8 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div><div class="header">
  <div class="headertitle"><div class="title">Introduction to the API</div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#intro_init">Initialization and termination</a><ul><li class="level2"><a href="#intro_init_init">Initializing GLFW</a></li>
<li class="level2"><a href="#init_hints">Initialization hints</a><ul><li class="level3"><a href="#init_hints_shared">Shared init hints</a></li>
<li class="level3"><a href="#init_hints_osx">macOS specific init hints</a></li>
<li class="level3"><a href="#init_hints_wayland">Wayland specific init hints</a></li>
<li class="level3"><a href="#init_hints_x11">X11 specific init hints</a></li>
<li class="level3"><a href="#init_hints_values">Supported and default values</a></li>
</ul>
</li>
<li class="level2"><a href="#platform">Runtime platform selection</a></li>
<li class="level2"><a href="#init_allocator">Custom heap memory allocator</a></li>
<li class="level2"><a href="#intro_init_terminate">Terminating GLFW</a></li>
</ul>
</li>
<li class="level1"><a href="#error_handling">Error handling</a></li>
<li class="level1"><a href="#coordinate_systems">Coordinate systems</a></li>
<li class="level1"><a href="#guarantees_limitations">Guarantees and limitations</a><ul><li class="level2"><a href="#lifetime">Pointer lifetimes</a></li>
<li class="level2"><a href="#reentrancy">Reentrancy</a></li>
<li class="level2"><a href="#thread_safety">Thread safety</a></li>
<li class="level2"><a href="#compatibility">Version compatibility</a></li>
<li class="level2"><a href="#event_order">Event order</a></li>
</ul>
</li>
<li class="level1"><a href="#intro_version">Version management</a><ul><li class="level2"><a href="#intro_version_compile">Compile-time version</a></li>
<li class="level2"><a href="#intro_version_runtime">Run-time version</a></li>
<li class="level2"><a href="#intro_version_string">Version string</a></li>
</ul>
</li>
</ul>
</div>
<div class="textblock"><p>This guide introduces the basic concepts of GLFW and describes initialization, error handling and API guarantees and limitations. For a broad but shallow tutorial, see <a class="el" href="quick_guide.html">Getting started</a> instead. For details on a specific function in this category, see the <a class="el" href="group__init.html">Initialization, version and error reference</a>.</p>
<p>There are also guides for the other areas of GLFW.</p>
<ul>
<li><a class="el" href="window_guide.html">Window guide</a></li>
<li><a class="el" href="context_guide.html">Context guide</a></li>
<li><a class="el" href="vulkan_guide.html">Vulkan guide</a></li>
<li><a class="el" href="monitor_guide.html">Monitor guide</a></li>
<li><a class="el" href="input_guide.html">Input guide</a></li>
</ul>
<h1><a class="anchor" id="intro_init"></a>
Initialization and termination</h1>
<p>Before most GLFW functions may be called, the library must be initialized. This initialization checks what features are available on the machine, enumerates monitors, initializes the timer and performs any required platform-specific initialization.</p>
<p>Only the following functions may be called before the library has been successfully initialized, and only from the main thread.</p>
<ul>
<li><a class="el" href="group__init.html#ga9f8ffaacf3c269cc48eafbf8b9b71197">glfwGetVersion</a></li>
<li><a class="el" href="group__init.html#ga026abd003c8e6501981ab1662062f1c0">glfwGetVersionString</a></li>
<li><a class="el" href="group__init.html#ga8785d2b6b36632368d803e78079d38ed">glfwPlatformSupported</a></li>
<li><a class="el" href="group__init.html#ga944986b4ec0b928d488141f92982aa18">glfwGetError</a></li>
<li><a class="el" href="group__init.html#gaff45816610d53f0b83656092a4034f40">glfwSetErrorCallback</a></li>
<li><a class="el" href="group__init.html#ga110fd1d3f0412822b4f1908c026f724a">glfwInitHint</a></li>
<li><a class="el" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a></li>
<li><a class="el" href="group__init.html#ga76af552d0307bb5f7791f245417d4752">glfwInitVulkanLoader</a></li>
<li><a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a></li>
<li><a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a></li>
</ul>
<p>Calling any other function before successful initialization will cause a <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> error.</p>
<h2><a class="anchor" id="intro_init_init"></a>
Initializing GLFW</h2>
<p>The library is initialized with <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a>, which returns <code>GLFW_FALSE</code> if an error occurred.</p>
<div class="fragment"><div class="line"><span class="keywordflow">if</span> (!<a class="code hl_function" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a>())</div>
<div class="line">{</div>
<div class="line">    <span class="comment">// Handle initialization failure</span></div>
<div class="line">}</div>
<div class="ttc" id="agroup__init_html_ga317aac130a235ab08c6db0834907d85e"><div class="ttname"><a href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a></div><div class="ttdeci">int glfwInit(void)</div><div class="ttdoc">Initializes the GLFW library.</div></div>
</div><!-- fragment --><p>If any part of initialization fails, any parts that succeeded are terminated as if <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a> had been called. The library only needs to be initialized once and additional calls to an already initialized library will return <code>GLFW_TRUE</code> immediately.</p>
<p>Once the library has been successfully initialized, it should be terminated before the application exits. Modern systems are very good at freeing resources allocated by programs that exit, but GLFW sometimes has to change global system settings and these might not be restored without termination.</p>
<p><b>macOS:</b> When the library is initialized the main menu and dock icon are created. These are not desirable for a command-line only program. The creation of the main menu and dock icon can be disabled with the <a class="el" href="group__init.html#ga71e0b4ce2f2696a84a9b8c5e12dc70cf">GLFW_COCOA_MENUBAR</a> init hint.</p>
<h2><a class="anchor" id="init_hints"></a>
Initialization hints</h2>
<p>Initialization hints are set before <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a> and affect how the library behaves until termination. Hints are set with <a class="el" href="group__init.html#ga110fd1d3f0412822b4f1908c026f724a">glfwInitHint</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__init.html#ga110fd1d3f0412822b4f1908c026f724a">glfwInitHint</a>(<a class="code hl_define" href="group__init.html#gab9c0534709fda03ec8959201da3a9a18">GLFW_JOYSTICK_HAT_BUTTONS</a>, <a class="code hl_define" href="group__init.html#gac877fe3b627d21ef3a0a23e0a73ba8c5">GLFW_FALSE</a>);</div>
<div class="ttc" id="agroup__init_html_ga110fd1d3f0412822b4f1908c026f724a"><div class="ttname"><a href="group__init.html#ga110fd1d3f0412822b4f1908c026f724a">glfwInitHint</a></div><div class="ttdeci">void glfwInitHint(int hint, int value)</div><div class="ttdoc">Sets the specified init hint to the desired value.</div></div>
<div class="ttc" id="agroup__init_html_gab9c0534709fda03ec8959201da3a9a18"><div class="ttname"><a href="group__init.html#gab9c0534709fda03ec8959201da3a9a18">GLFW_JOYSTICK_HAT_BUTTONS</a></div><div class="ttdeci">#define GLFW_JOYSTICK_HAT_BUTTONS</div><div class="ttdoc">Joystick hat buttons init hint.</div><div class="ttdef"><b>Definition</b> glfw3.h:1299</div></div>
<div class="ttc" id="agroup__init_html_gac877fe3b627d21ef3a0a23e0a73ba8c5"><div class="ttname"><a href="group__init.html#gac877fe3b627d21ef3a0a23e0a73ba8c5">GLFW_FALSE</a></div><div class="ttdeci">#define GLFW_FALSE</div><div class="ttdoc">Zero.</div><div class="ttdef"><b>Definition</b> glfw3.h:321</div></div>
</div><!-- fragment --><p>The values you set hints to are never reset by GLFW, but they only take effect during initialization. Once GLFW has been initialized, any values you set will be ignored until the library is terminated and initialized again.</p>
<p>Some hints are platform specific. These may be set on any platform but they will only affect their specific platform. Other platforms will ignore them. Setting these hints requires no platform specific headers or functions.</p>
<h3><a class="anchor" id="init_hints_shared"></a>
Shared init hints</h3>
<p><a class="anchor" id="GLFW_PLATFORM"></a><b>GLFW_PLATFORM</b> specifies the platform to use for windowing and input. Possible values are <code>GLFW_ANY_PLATFORM</code>, <code>GLFW_PLATFORM_WIN32</code>, <code>GLFW_PLATFORM_COCOA</code>, <code>GLFW_PLATFORM_WAYLAND</code>, <code>GLFW_PLATFORM_X11</code> and <code>GLFW_PLATFORM_NULL</code>. The default value is <code>GLFW_ANY_PLATFORM</code>, which will choose any platform the library includes support for except for the Null backend.</p>
<p><a class="anchor" id="GLFW_JOYSTICK_HAT_BUTTONS"></a><b>GLFW_JOYSTICK_HAT_BUTTONS</b> specifies whether to also expose joystick hats as buttons, for compatibility with earlier versions of GLFW that did not have <a class="el" href="group__input.html#ga06e660841b3e79c54da4f54a932c5a2c">glfwGetJoystickHats</a>. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>.</p>
<p><a class="anchor" id="GLFW_ANGLE_PLATFORM_TYPE_hint"></a><b>GLFW_ANGLE_PLATFORM_TYPE</b> specifies the platform type (rendering backend) to request when using OpenGL ES and EGL via <a href="https://chromium.googlesource.com/angle/angle/">ANGLE</a>. If the requested platform type is unavailable, ANGLE will use its default. Possible values are one of <code>GLFW_ANGLE_PLATFORM_TYPE_NONE</code>, <code>GLFW_ANGLE_PLATFORM_TYPE_OPENGL</code>, <code>GLFW_ANGLE_PLATFORM_TYPE_OPENGLES</code>, <code>GLFW_ANGLE_PLATFORM_TYPE_D3D9</code>, <code>GLFW_ANGLE_PLATFORM_TYPE_D3D11</code>, <code>GLFW_ANGLE_PLATFORM_TYPE_VULKAN</code> and <code>GLFW_ANGLE_PLATFORM_TYPE_METAL</code>.</p>
<p>The ANGLE platform type is specified via the <code>EGL_ANGLE_platform_angle</code> extension. This extension is not used if this hint is <code>GLFW_ANGLE_PLATFORM_TYPE_NONE</code>, which is the default value.</p>
<h3><a class="anchor" id="init_hints_osx"></a>
macOS specific init hints</h3>
<p><a class="anchor" id="GLFW_COCOA_CHDIR_RESOURCES_hint"></a><b>GLFW_COCOA_CHDIR_RESOURCES</b> specifies whether to set the current directory to the application to the <code>Contents/Resources</code> subdirectory of the application's bundle, if present. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>. This is ignored on other platforms.</p>
<p><a class="anchor" id="GLFW_COCOA_MENUBAR_hint"></a><b>GLFW_COCOA_MENUBAR</b> specifies whether to create the menu bar and dock icon when GLFW is initialized. This applies whether the menu bar is created from a nib or manually by GLFW. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>. This is ignored on other platforms.</p>
<h3><a class="anchor" id="init_hints_wayland"></a>
Wayland specific init hints</h3>
<p><a class="anchor" id="GLFW_WAYLAND_LIBDECOR_hint"></a><b>GLFW_WAYLAND_LIBDECOR</b> specifies whether to use <a href="https://gitlab.freedesktop.org/libdecor/libdecor">libdecor</a> for window decorations where available. Possible values are <code>GLFW_WAYLAND_PREFER_LIBDECOR</code> and <code>GLFW_WAYLAND_DISABLE_LIBDECOR</code>. This is ignored on other platforms.</p>
<h3><a class="anchor" id="init_hints_x11"></a>
X11 specific init hints</h3>
<p><a class="anchor" id="GLFW_X11_XCB_VULKAN_SURFACE_hint"></a><b>GLFW_X11_XCB_VULKAN_SURFACE</b> specifies whether to prefer the <code>VK_KHR_xcb_surface</code> extension for creating Vulkan surfaces, or whether to use the <code>VK_KHR_xlib_surface</code> extension. Possible values are <code>GLFW_TRUE</code> and <code>GLFW_FALSE</code>. This is ignored on other platforms.</p>
<h3><a class="anchor" id="init_hints_values"></a>
Supported and default values</h3>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Initialization hint   </th><th class="markdownTableHeadNone">Default value   </th><th class="markdownTableHeadNone">Supported values    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><a class="el" href="intro_guide.html#GLFW_PLATFORM">GLFW_PLATFORM</a>   </td><td class="markdownTableBodyNone"><code>GLFW_ANY_PLATFORM</code>   </td><td class="markdownTableBodyNone"><code>GLFW_ANY_PLATFORM</code>, <code>GLFW_PLATFORM_WIN32</code>, <code>GLFW_PLATFORM_COCOA</code>, <code>GLFW_PLATFORM_WAYLAND</code>, <code>GLFW_PLATFORM_X11</code> or <code>GLFW_PLATFORM_NULL</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><a class="el" href="intro_guide.html#GLFW_JOYSTICK_HAT_BUTTONS">GLFW_JOYSTICK_HAT_BUTTONS</a>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><a class="el" href="group__init.html#gaec269b24cf549ab46292c0125d8bbdce">GLFW_ANGLE_PLATFORM_TYPE</a>   </td><td class="markdownTableBodyNone"><code>GLFW_ANGLE_PLATFORM_TYPE_NONE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_ANGLE_PLATFORM_TYPE_NONE</code>, <code>GLFW_ANGLE_PLATFORM_TYPE_OPENGL</code>, <code>GLFW_ANGLE_PLATFORM_TYPE_OPENGLES</code>, <code>GLFW_ANGLE_PLATFORM_TYPE_D3D9</code>, <code>GLFW_ANGLE_PLATFORM_TYPE_D3D11</code>, <code>GLFW_ANGLE_PLATFORM_TYPE_VULKAN</code> or <code>GLFW_ANGLE_PLATFORM_TYPE_METAL</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><a class="el" href="group__init.html#gab937983147a3158d45f88fad7129d9f2">GLFW_COCOA_CHDIR_RESOURCES</a>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><a class="el" href="group__init.html#ga71e0b4ce2f2696a84a9b8c5e12dc70cf">GLFW_COCOA_MENUBAR</a>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone"><a class="el" href="group__init.html#ga2a3f2fd7695902c498b050215b3db452">GLFW_WAYLAND_LIBDECOR</a>   </td><td class="markdownTableBodyNone"><code>GLFW_WAYLAND_PREFER_LIBDECOR</code>   </td><td class="markdownTableBodyNone"><code>GLFW_WAYLAND_PREFER_LIBDECOR</code> or <code>GLFW_WAYLAND_DISABLE_LIBDECOR</code>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone"><a class="el" href="group__init.html#gaa341e303ebeb8e4199b8ab8be84351f6">GLFW_X11_XCB_VULKAN_SURFACE</a>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code>   </td><td class="markdownTableBodyNone"><code>GLFW_TRUE</code> or <code>GLFW_FALSE</code>   </td></tr>
</table>
<h2><a class="anchor" id="platform"></a>
Runtime platform selection</h2>
<p>GLFW can be compiled for more than one platform (window system) at once. This lets a single library binary support both Wayland and X11 on Linux and other Unix-like systems.</p>
<p>You can control platform selection via the <a class="el" href="intro_guide.html#GLFW_PLATFORM">GLFW_PLATFORM</a> initialization hint. By default, this is set to <a class="el" href="group__init.html#ga18b2d37374d0dea28cd69194fa85b859">GLFW_ANY_PLATFORM</a>, which will look for supported window systems in order of priority and select the first one it finds. It can also be set to any specific platform to have GLFW only look for that one.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__init.html#ga110fd1d3f0412822b4f1908c026f724a">glfwInitHint</a>(<a class="code hl_define" href="group__init.html#ga9d38bf1fdf4f91d6565401734a7cd967">GLFW_PLATFORM</a>, <a class="code hl_define" href="group__init.html#gaf5333f3933e9c248a00cfda6523f386b">GLFW_PLATFORM_X11</a>);</div>
<div class="ttc" id="agroup__init_html_ga9d38bf1fdf4f91d6565401734a7cd967"><div class="ttname"><a href="group__init.html#ga9d38bf1fdf4f91d6565401734a7cd967">GLFW_PLATFORM</a></div><div class="ttdeci">#define GLFW_PLATFORM</div><div class="ttdoc">Platform selection init hint.</div><div class="ttdef"><b>Definition</b> glfw3.h:1309</div></div>
<div class="ttc" id="agroup__init_html_gaf5333f3933e9c248a00cfda6523f386b"><div class="ttname"><a href="group__init.html#gaf5333f3933e9c248a00cfda6523f386b">GLFW_PLATFORM_X11</a></div><div class="ttdeci">#define GLFW_PLATFORM_X11</div><div class="ttdef"><b>Definition</b> glfw3.h:1342</div></div>
</div><!-- fragment --><p>This mechanism also provides the Null platform, which is always supported but needs to be explicitly requested. This platform is effectively a stub, emulating a window system on a single 1080p monitor, but will not interact with any actual window system.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__init.html#ga110fd1d3f0412822b4f1908c026f724a">glfwInitHint</a>(<a class="code hl_define" href="group__init.html#ga9d38bf1fdf4f91d6565401734a7cd967">GLFW_PLATFORM</a>, <a class="code hl_define" href="group__init.html#gac06fad5a4866ae7a1d7b2675fac72d7f">GLFW_PLATFORM_NULL</a>);</div>
<div class="ttc" id="agroup__init_html_gac06fad5a4866ae7a1d7b2675fac72d7f"><div class="ttname"><a href="group__init.html#gac06fad5a4866ae7a1d7b2675fac72d7f">GLFW_PLATFORM_NULL</a></div><div class="ttdeci">#define GLFW_PLATFORM_NULL</div><div class="ttdef"><b>Definition</b> glfw3.h:1343</div></div>
</div><!-- fragment --><p>You can test whether a library binary was compiled with support for a specific platform with <a class="el" href="group__init.html#ga8785d2b6b36632368d803e78079d38ed">glfwPlatformSupported</a>.</p>
<div class="fragment"><div class="line"><span class="keywordflow">if</span> (<a class="code hl_function" href="group__init.html#ga8785d2b6b36632368d803e78079d38ed">glfwPlatformSupported</a>(<a class="code hl_define" href="group__init.html#gac4b08906a3cbf26c518a4a543eedd740">GLFW_PLATFORM_WAYLAND</a>))</div>
<div class="line">    <a class="code hl_function" href="group__init.html#ga110fd1d3f0412822b4f1908c026f724a">glfwInitHint</a>(<a class="code hl_define" href="group__init.html#ga9d38bf1fdf4f91d6565401734a7cd967">GLFW_PLATFORM</a>, <a class="code hl_define" href="group__init.html#gac4b08906a3cbf26c518a4a543eedd740">GLFW_PLATFORM_WAYLAND</a>);</div>
<div class="ttc" id="agroup__init_html_ga8785d2b6b36632368d803e78079d38ed"><div class="ttname"><a href="group__init.html#ga8785d2b6b36632368d803e78079d38ed">glfwPlatformSupported</a></div><div class="ttdeci">int glfwPlatformSupported(int platform)</div><div class="ttdoc">Returns whether the library includes support for the specified platform.</div></div>
<div class="ttc" id="agroup__init_html_gac4b08906a3cbf26c518a4a543eedd740"><div class="ttname"><a href="group__init.html#gac4b08906a3cbf26c518a4a543eedd740">GLFW_PLATFORM_WAYLAND</a></div><div class="ttdeci">#define GLFW_PLATFORM_WAYLAND</div><div class="ttdef"><b>Definition</b> glfw3.h:1341</div></div>
</div><!-- fragment --><p>Once GLFW has been initialized, you can query which platform was selected with <a class="el" href="group__init.html#ga6d6a983d38bd4e8fd786d7a9061d399e">glfwGetPlatform</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> platform = <a class="code hl_function" href="group__init.html#ga6d6a983d38bd4e8fd786d7a9061d399e">glfwGetPlatform</a>();</div>
<div class="ttc" id="agroup__init_html_ga6d6a983d38bd4e8fd786d7a9061d399e"><div class="ttname"><a href="group__init.html#ga6d6a983d38bd4e8fd786d7a9061d399e">glfwGetPlatform</a></div><div class="ttdeci">int glfwGetPlatform(void)</div><div class="ttdoc">Returns the currently selected platform.</div></div>
</div><!-- fragment --><p>If you are using any <a class="el" href="group__native.html">native access functions</a>, especially on Linux and other Unix-like systems, then you may need to check that you are calling the ones matching the selected platform.</p>
<h2><a class="anchor" id="init_allocator"></a>
Custom heap memory allocator</h2>
<p>The heap memory allocator can be customized before initialization with <a class="el" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_struct" href="struct_g_l_f_wallocator.html">GLFWallocator</a> allocator;</div>
<div class="line">allocator.<a class="code hl_variable" href="struct_g_l_f_wallocator.html#a18a798136f17a9cb105be18312193bf7">allocate</a> = my_malloc;</div>
<div class="line">allocator.<a class="code hl_variable" href="struct_g_l_f_wallocator.html#af5a674af9e170095b968f467233437be">reallocate</a> = my_realloc;</div>
<div class="line">allocator.<a class="code hl_variable" href="struct_g_l_f_wallocator.html#ab74cf9a969e73e6eb65a6112a591a988">deallocate</a> = my_free;</div>
<div class="line">allocator.<a class="code hl_variable" href="struct_g_l_f_wallocator.html#af6153be74dbaf7f0a7e8bd3bfc039910">user</a> = NULL;</div>
<div class="line"> </div>
<div class="line"><a class="code hl_function" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a>(&amp;allocator);</div>
<div class="ttc" id="agroup__init_html_ga9dde93e9891fa7dd17e4194c9f3ae7c6"><div class="ttname"><a href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a></div><div class="ttdeci">void glfwInitAllocator(const GLFWallocator *allocator)</div><div class="ttdoc">Sets the init allocator to the desired value.</div></div>
<div class="ttc" id="astruct_g_l_f_wallocator_html"><div class="ttname"><a href="struct_g_l_f_wallocator.html">GLFWallocator</a></div><div class="ttdoc">Custom heap memory allocator.</div><div class="ttdef"><b>Definition</b> glfw3.h:2138</div></div>
<div class="ttc" id="astruct_g_l_f_wallocator_html_a18a798136f17a9cb105be18312193bf7"><div class="ttname"><a href="struct_g_l_f_wallocator.html#a18a798136f17a9cb105be18312193bf7">GLFWallocator::allocate</a></div><div class="ttdeci">GLFWallocatefun allocate</div><div class="ttdef"><b>Definition</b> glfw3.h:2142</div></div>
<div class="ttc" id="astruct_g_l_f_wallocator_html_ab74cf9a969e73e6eb65a6112a591a988"><div class="ttname"><a href="struct_g_l_f_wallocator.html#ab74cf9a969e73e6eb65a6112a591a988">GLFWallocator::deallocate</a></div><div class="ttdeci">GLFWdeallocatefun deallocate</div><div class="ttdef"><b>Definition</b> glfw3.h:2150</div></div>
<div class="ttc" id="astruct_g_l_f_wallocator_html_af5a674af9e170095b968f467233437be"><div class="ttname"><a href="struct_g_l_f_wallocator.html#af5a674af9e170095b968f467233437be">GLFWallocator::reallocate</a></div><div class="ttdeci">GLFWreallocatefun reallocate</div><div class="ttdef"><b>Definition</b> glfw3.h:2146</div></div>
<div class="ttc" id="astruct_g_l_f_wallocator_html_af6153be74dbaf7f0a7e8bd3bfc039910"><div class="ttname"><a href="struct_g_l_f_wallocator.html#af6153be74dbaf7f0a7e8bd3bfc039910">GLFWallocator::user</a></div><div class="ttdeci">void * user</div><div class="ttdef"><b>Definition</b> glfw3.h:2154</div></div>
</div><!-- fragment --><p>The allocator will be made active at the beginning of initialization and will be used by GLFW until the library has been fully terminated. Any allocator set after initialization will be picked up only at the next initialization.</p>
<p>The allocator will only be used for allocations that would have been made with the C standard library. Memory allocations that must be made with platform specific APIs will still use those.</p>
<p>The allocation function must have a signature matching <a class="el" href="group__init.html#ga4306a564e9f60f4de8cc8f31731a3120">GLFWallocatefun</a>. It receives the desired size, in bytes, and the user pointer passed to <a class="el" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a> and returns the address to the allocated memory block.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span>* my_malloc(<span class="keywordtype">size_t</span> size, <span class="keywordtype">void</span>* user)</div>
<div class="line">{</div>
<div class="line">    ...</div>
<div class="line">}</div>
</div><!-- fragment --><p>The documentation for <a class="el" href="group__init.html#ga4306a564e9f60f4de8cc8f31731a3120">GLFWallocatefun</a> also lists the requirements and limitations for an allocation function. If the active one does not meet all of these, GLFW may fail.</p>
<p>The reallocation function must have a function signature matching <a class="el" href="group__init.html#ga3e88a829615d8efe8bec1746f7309c63">GLFWreallocatefun</a>. It receives the memory block to be reallocated, the new desired size, in bytes, and the user pointer passed to <a class="el" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a> and returns the address to the resized memory block.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span>* my_realloc(<span class="keywordtype">void</span>* block, <span class="keywordtype">size_t</span> size, <span class="keywordtype">void</span>* user)</div>
<div class="line">{</div>
<div class="line">    ...</div>
<div class="line">}</div>
</div><!-- fragment --><p>The documentation for <a class="el" href="group__init.html#ga3e88a829615d8efe8bec1746f7309c63">GLFWreallocatefun</a> also lists the requirements and limitations for a reallocation function. If the active one does not meet all of these, GLFW may fail.</p>
<p>The deallocation function must have a function signature matching <a class="el" href="group__init.html#ga7181615eda94c4b07bd72bdcee39fa28">GLFWdeallocatefun</a>. It receives the memory block to be deallocated and the user pointer passed to <a class="el" href="group__init.html#ga9dde93e9891fa7dd17e4194c9f3ae7c6">glfwInitAllocator</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> my_free(<span class="keywordtype">void</span>* block, <span class="keywordtype">void</span>* user)</div>
<div class="line">{</div>
<div class="line">    ...</div>
<div class="line">}</div>
</div><!-- fragment --><p>The documentation for <a class="el" href="group__init.html#ga7181615eda94c4b07bd72bdcee39fa28">GLFWdeallocatefun</a> also lists the requirements and limitations for a deallocation function. If the active one does not meet all of these, GLFW may fail.</p>
<h2><a class="anchor" id="intro_init_terminate"></a>
Terminating GLFW</h2>
<p>Before your application exits, you should terminate the GLFW library if it has been initialized. This is done with <a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a>();</div>
<div class="ttc" id="agroup__init_html_gaaae48c0a18607ea4a4ba951d939f0901"><div class="ttname"><a href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a></div><div class="ttdeci">void glfwTerminate(void)</div><div class="ttdoc">Terminates the GLFW library.</div></div>
</div><!-- fragment --><p>This will destroy any remaining window, monitor and cursor objects, restore any modified gamma ramps, re-enable the screensaver if it had been disabled and free any other resources allocated by GLFW.</p>
<p>Once the library is terminated, it is as if it had never been initialized, therefore you will need to initialize it again before being able to use GLFW. If the library was not initialized or had already been terminated, it returns immediately.</p>
<h1><a class="anchor" id="error_handling"></a>
Error handling</h1>
<p>Some GLFW functions have return values that indicate an error, but this is often not very helpful when trying to figure out what happened or why it occurred. Other functions have no return value reserved for errors, so error notification needs a separate channel. Finally, far from all GLFW functions have return values.</p>
<p>The last <a class="el" href="group__errors.html">error code</a> for the calling thread can be queried at any time with <a class="el" href="group__init.html#ga944986b4ec0b928d488141f92982aa18">glfwGetError</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> code = <a class="code hl_function" href="group__init.html#ga944986b4ec0b928d488141f92982aa18">glfwGetError</a>(NULL);</div>
<div class="line"> </div>
<div class="line"><span class="keywordflow">if</span> (code != <a class="code hl_define" href="group__errors.html#gafa30deee5db4d69c4c93d116ed87dbf4">GLFW_NO_ERROR</a>)</div>
<div class="line">    handle_error(code);</div>
<div class="ttc" id="agroup__errors_html_gafa30deee5db4d69c4c93d116ed87dbf4"><div class="ttname"><a href="group__errors.html#gafa30deee5db4d69c4c93d116ed87dbf4">GLFW_NO_ERROR</a></div><div class="ttdeci">#define GLFW_NO_ERROR</div><div class="ttdoc">No error has occurred.</div><div class="ttdef"><b>Definition</b> glfw3.h:672</div></div>
<div class="ttc" id="agroup__init_html_ga944986b4ec0b928d488141f92982aa18"><div class="ttname"><a href="group__init.html#ga944986b4ec0b928d488141f92982aa18">glfwGetError</a></div><div class="ttdeci">int glfwGetError(const char **description)</div><div class="ttdoc">Returns and clears the last error for the calling thread.</div></div>
</div><!-- fragment --><p>If no error has occurred since the last call, <a class="el" href="group__errors.html#gafa30deee5db4d69c4c93d116ed87dbf4">GLFW_NO_ERROR</a> (zero) is returned. The error is cleared before the function returns.</p>
<p>The error code indicates the general category of the error. Some error codes, such as <a class="el" href="group__errors.html#ga2374ee02c177f12e1fa76ff3ed15e14a">GLFW_NOT_INITIALIZED</a> has only a single meaning, whereas others like <a class="el" href="group__errors.html#gad44162d78100ea5e87cdd38426b8c7a1">GLFW_PLATFORM_ERROR</a> are used for many different errors.</p>
<p>GLFW often has more information about an error than its general category. You can retrieve a UTF-8 encoded human-readable description along with the error code. If no error has occurred since the last call, the description is set to <code>NULL</code>.</p>
<div class="fragment"><div class="line"><span class="keyword">const</span> <span class="keywordtype">char</span>* description;</div>
<div class="line"><span class="keywordtype">int</span> code = <a class="code hl_function" href="group__init.html#ga944986b4ec0b928d488141f92982aa18">glfwGetError</a>(&amp;description);</div>
<div class="line"> </div>
<div class="line"><span class="keywordflow">if</span> (description)</div>
<div class="line">    display_error_message(code, description);</div>
</div><!-- fragment --><p>The retrieved description string is only valid until the next error occurs. This means you must make a copy of it if you want to keep it.</p>
<p>You can also set an error callback, which will be called each time an error occurs. It is set with <a class="el" href="group__init.html#gaff45816610d53f0b83656092a4034f40">glfwSetErrorCallback</a>.</p>
<div class="fragment"><div class="line"><a class="code hl_function" href="group__init.html#gaff45816610d53f0b83656092a4034f40">glfwSetErrorCallback</a>(error_callback);</div>
<div class="ttc" id="agroup__init_html_gaff45816610d53f0b83656092a4034f40"><div class="ttname"><a href="group__init.html#gaff45816610d53f0b83656092a4034f40">glfwSetErrorCallback</a></div><div class="ttdeci">GLFWerrorfun glfwSetErrorCallback(GLFWerrorfun callback)</div><div class="ttdoc">Sets the error callback.</div></div>
</div><!-- fragment --><p>The error callback receives the same error code and human-readable description returned by <a class="el" href="group__init.html#ga944986b4ec0b928d488141f92982aa18">glfwGetError</a>.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> error_callback(<span class="keywordtype">int</span> code, <span class="keyword">const</span> <span class="keywordtype">char</span>* description)</div>
<div class="line">{</div>
<div class="line">    display_error_message(code, description);</div>
<div class="line">}</div>
</div><!-- fragment --><p>The error callback is called after the error is stored, so calling <a class="el" href="group__init.html#ga944986b4ec0b928d488141f92982aa18">glfwGetError</a> from within the error callback returns the same values as the callback argument.</p>
<p>The description string passed to the callback is only valid until the error callback returns. This means you must make a copy of it if you want to keep it.</p>
<p><b>Reported errors are never fatal.</b> As long as GLFW was successfully initialized, it will remain initialized and in a safe state until terminated regardless of how many errors occur. If an error occurs during initialization that causes <a class="el" href="group__init.html#ga317aac130a235ab08c6db0834907d85e">glfwInit</a> to fail, any part of the library that was initialized will be safely terminated.</p>
<p>Do not rely on a currently invalid call to generate a specific error, as in the future that same call may generate a different error or become valid.</p>
<h1><a class="anchor" id="coordinate_systems"></a>
Coordinate systems</h1>
<p>GLFW has two primary coordinate systems: the <em>virtual screen</em> and the window <em>content area</em> or <em>content area</em>. Both use the same unit: <em>virtual screen coordinates</em>, or just <em>screen coordinates</em>, which don't necessarily correspond to pixels.</p>
<p><img src="spaces.svg" alt="" style="pointer-events: none;" width="90%" class="inline"/></p>
<p>Both the virtual screen and the content area coordinate systems have the X-axis pointing to the right and the Y-axis pointing down.</p>
<p>Window and monitor positions are specified as the position of the upper-left corners of their content areas relative to the virtual screen, while cursor positions are specified relative to a window's content area.</p>
<p>Because the origin of the window's content area coordinate system is also the point from which the window position is specified, you can translate content area coordinates to the virtual screen by adding the window position. The window frame, when present, extends out from the content area but does not affect the window position.</p>
<p>Almost all positions and sizes in GLFW are measured in screen coordinates relative to one of the two origins above. This includes cursor positions, window positions and sizes, window frame sizes, monitor positions and video mode resolutions.</p>
<p>Two exceptions are the <a class="el" href="monitor_guide.html#monitor_size">monitor physical size</a>, which is measured in millimetres, and <a class="el" href="window_guide.html#window_fbsize">framebuffer size</a>, which is measured in pixels.</p>
<p>Pixels and screen coordinates may map 1:1 on your machine, but they won't on every other machine, for example on a Mac with a Retina display. The ratio between screen coordinates and pixels may also change at run-time depending on which monitor the window is currently considered to be on.</p>
<h1><a class="anchor" id="guarantees_limitations"></a>
Guarantees and limitations</h1>
<p>This section describes the conditions under which GLFW can be expected to function, barring bugs in the operating system or drivers. Use of GLFW outside these limits may work on some platforms, or on some machines, or some of the time, or on some versions of GLFW, but it may break at any time and this will not be considered a bug.</p>
<h2><a class="anchor" id="lifetime"></a>
Pointer lifetimes</h2>
<p>GLFW will never free any pointer you provide to it, and you must never free any pointer it provides to you.</p>
<p>Many GLFW functions return pointers to dynamically allocated structures, strings or arrays, and some callbacks are provided with strings or arrays. These are always managed by GLFW and should never be freed by the application. The lifetime of these pointers is documented for each GLFW function and callback. If you need to keep this data, you must copy it before its lifetime expires.</p>
<p>Many GLFW functions accept pointers to structures or strings allocated by the application. These are never freed by GLFW and are always the responsibility of the application. If GLFW needs to keep the data in these structures or strings, it is copied before the function returns.</p>
<p>Pointer lifetimes are guaranteed not to be shortened in future minor or patch releases.</p>
<h2><a class="anchor" id="reentrancy"></a>
Reentrancy</h2>
<p>GLFW event processing and object destruction are not reentrant. This means that the following functions must not be called from any callback function:</p>
<ul>
<li><a class="el" href="group__window.html#gacdf43e51376051d2c091662e9fe3d7b2">glfwDestroyWindow</a></li>
<li><a class="el" href="group__input.html#ga81b952cd1764274d0db7fb3c5a79ba6a">glfwDestroyCursor</a></li>
<li><a class="el" href="group__window.html#ga37bd57223967b4211d60ca1a0bf3c832">glfwPollEvents</a></li>
<li><a class="el" href="group__window.html#ga554e37d781f0a997656c26b2c56c835e">glfwWaitEvents</a></li>
<li><a class="el" href="group__window.html#ga605a178db92f1a7f1a925563ef3ea2cf">glfwWaitEventsTimeout</a></li>
<li><a class="el" href="group__init.html#gaaae48c0a18607ea4a4ba951d939f0901">glfwTerminate</a></li>
</ul>
<p>These functions may be made reentrant in future minor or patch releases, but functions not on this list will not be made non-reentrant.</p>
<h2><a class="anchor" id="thread_safety"></a>
Thread safety</h2>
<p>Most GLFW functions must only be called from the main thread (the thread that calls main), but some may be called from any thread once the library has been initialized. Before initialization the whole library is thread-unsafe.</p>
<p>The reference documentation for every GLFW function states whether it is limited to the main thread.</p>
<p>Initialization, termination, event processing and the creation and destruction of windows, cursors and OpenGL and OpenGL ES contexts are all restricted to the main thread due to limitations of one or several platforms.</p>
<p>Because event processing must be performed on the main thread, all callbacks except for the error callback will only be called on that thread. The error callback may be called on any thread, as any GLFW function may generate errors.</p>
<p>The error code and description may be queried from any thread.</p>
<ul>
<li><a class="el" href="group__init.html#ga944986b4ec0b928d488141f92982aa18">glfwGetError</a></li>
</ul>
<p>Empty events may be posted from any thread.</p>
<ul>
<li><a class="el" href="group__window.html#gab5997a25187e9fd5c6f2ecbbc8dfd7e9">glfwPostEmptyEvent</a></li>
</ul>
<p>The window user pointer and close flag may be read and written from any thread, but this is not synchronized by GLFW.</p>
<ul>
<li><a class="el" href="group__window.html#gae77a4add0d2023ca21ff1443ced01653">glfwGetWindowUserPointer</a></li>
<li><a class="el" href="group__window.html#ga3d2fc6026e690ab31a13f78bc9fd3651">glfwSetWindowUserPointer</a></li>
<li><a class="el" href="group__window.html#ga24e02fbfefbb81fc45320989f8140ab5">glfwWindowShouldClose</a></li>
<li><a class="el" href="group__window.html#ga49c449dde2a6f87d996f4daaa09d6708">glfwSetWindowShouldClose</a></li>
</ul>
<p>These functions for working with OpenGL and OpenGL ES contexts may be called from any thread, but the window object is not synchronized by GLFW.</p>
<ul>
<li><a class="el" href="group__context.html#ga1c04dc242268f827290fe40aa1c91157">glfwMakeContextCurrent</a></li>
<li><a class="el" href="group__context.html#gad94e80185397a6cf5fe2ab30567af71c">glfwGetCurrentContext</a></li>
<li><a class="el" href="group__window.html#ga15a5a1ee5b3c2ca6b15ca209a12efd14">glfwSwapBuffers</a></li>
<li><a class="el" href="group__context.html#ga6d4e0cdf151b5e579bd67f13202994ed">glfwSwapInterval</a></li>
<li><a class="el" href="group__context.html#ga87425065c011cef1ebd6aac75e059dfa">glfwExtensionSupported</a></li>
<li><a class="el" href="group__context.html#ga35f1837e6f666781842483937612f163">glfwGetProcAddress</a></li>
</ul>
<p>The raw timer functions may be called from any thread.</p>
<ul>
<li><a class="el" href="group__input.html#ga3289ee876572f6e91f06df3a24824443">glfwGetTimerFrequency</a></li>
<li><a class="el" href="group__input.html#ga09b2bd37d328e0b9456c7ec575cc26aa">glfwGetTimerValue</a></li>
</ul>
<p>The regular timer may be used from any thread, but reading and writing the timer offset is not synchronized by GLFW.</p>
<ul>
<li><a class="el" href="group__input.html#gaa6cf4e7a77158a3b8fd00328b1720a4a">glfwGetTime</a></li>
<li><a class="el" href="group__input.html#gaf59589ef6e8b8c8b5ad184b25afd4dc0">glfwSetTime</a></li>
</ul>
<p>Library version information may be queried from any thread.</p>
<ul>
<li><a class="el" href="group__init.html#ga9f8ffaacf3c269cc48eafbf8b9b71197">glfwGetVersion</a></li>
<li><a class="el" href="group__init.html#ga026abd003c8e6501981ab1662062f1c0">glfwGetVersionString</a></li>
</ul>
<p>Platform information may be queried from any thread.</p>
<ul>
<li><a class="el" href="group__init.html#ga8785d2b6b36632368d803e78079d38ed">glfwPlatformSupported</a></li>
<li><a class="el" href="group__init.html#ga6d6a983d38bd4e8fd786d7a9061d399e">glfwGetPlatform</a></li>
</ul>
<p>All Vulkan related functions may be called from any thread.</p>
<ul>
<li><a class="el" href="group__vulkan.html#ga2e7f30931e02464b5bc8d0d4b6f9fe2b">glfwVulkanSupported</a></li>
<li><a class="el" href="group__vulkan.html#ga99ad342d82f4a3421e2864978cb6d1d6">glfwGetRequiredInstanceExtensions</a></li>
<li><a class="el" href="group__vulkan.html#gadf228fac94c5fd8f12423ec9af9ff1e9">glfwGetInstanceProcAddress</a></li>
<li><a class="el" href="group__vulkan.html#gaff3823355cdd7e2f3f9f4d9ea9518d92">glfwGetPhysicalDevicePresentationSupport</a></li>
<li><a class="el" href="group__vulkan.html#ga1a24536bec3f80b08ead18e28e6ae965">glfwCreateWindowSurface</a></li>
</ul>
<p>GLFW uses synchronization objects internally only to manage the per-thread context and error states. Additional synchronization is left to the application.</p>
<p>Functions that may currently be called from any thread will always remain so, but functions that are currently limited to the main thread may be updated to allow calls from any thread in future releases.</p>
<h2><a class="anchor" id="compatibility"></a>
Version compatibility</h2>
<p>GLFW uses <a href="https://semver.org/">Semantic Versioning</a>. This guarantees source and binary backward compatibility with earlier minor versions of the API. This means that you can drop in a newer version of the library and existing programs will continue to compile and existing binaries will continue to run.</p>
<p>Once a function or constant has been added, the signature of that function or value of that constant will remain unchanged until the next major version of GLFW. No compatibility of any kind is guaranteed between major versions.</p>
<p>Undocumented behavior, i.e. behavior that is not described in the documentation, may change at any time until it is documented.</p>
<p>If the reference documentation and the implementation differ, the reference documentation will almost always take precedence and the implementation will be fixed in the next release. The reference documentation will also take precedence over anything stated in a guide.</p>
<h2><a class="anchor" id="event_order"></a>
Event order</h2>
<p>The order of arrival of related events is not guaranteed to be consistent across platforms. The exception is synthetic key and mouse button release events, which are always delivered after the window defocus event.</p>
<h1><a class="anchor" id="intro_version"></a>
Version management</h1>
<p>GLFW provides mechanisms for identifying what version of GLFW your application was compiled against as well as what version it is currently running against. If you are loading GLFW dynamically (not just linking dynamically), you can use this to verify that the library binary is compatible with your application.</p>
<h2><a class="anchor" id="intro_version_compile"></a>
Compile-time version</h2>
<p>The compile-time version of GLFW is provided by the GLFW header with the <code>GLFW_VERSION_MAJOR</code>, <code>GLFW_VERSION_MINOR</code> and <code>GLFW_VERSION_REVISION</code> macros.</p>
<div class="fragment"><div class="line">printf(<span class="stringliteral">&quot;Compiled against GLFW %i.%i.%i\n&quot;</span>,</div>
<div class="line">       <a class="code hl_define" href="group__init.html#ga6337d9ea43b22fc529b2bba066b4a576">GLFW_VERSION_MAJOR</a>,</div>
<div class="line">       <a class="code hl_define" href="group__init.html#gaf80d40f0aea7088ff337606e9c48f7a3">GLFW_VERSION_MINOR</a>,</div>
<div class="line">       <a class="code hl_define" href="group__init.html#gab72ae2e2035d9ea461abc3495eac0502">GLFW_VERSION_REVISION</a>);</div>
<div class="ttc" id="agroup__init_html_ga6337d9ea43b22fc529b2bba066b4a576"><div class="ttname"><a href="group__init.html#ga6337d9ea43b22fc529b2bba066b4a576">GLFW_VERSION_MAJOR</a></div><div class="ttdeci">#define GLFW_VERSION_MAJOR</div><div class="ttdoc">The major version number of the GLFW header.</div><div class="ttdef"><b>Definition</b> glfw3.h:287</div></div>
<div class="ttc" id="agroup__init_html_gab72ae2e2035d9ea461abc3495eac0502"><div class="ttname"><a href="group__init.html#gab72ae2e2035d9ea461abc3495eac0502">GLFW_VERSION_REVISION</a></div><div class="ttdeci">#define GLFW_VERSION_REVISION</div><div class="ttdoc">The revision number of the GLFW header.</div><div class="ttdef"><b>Definition</b> glfw3.h:301</div></div>
<div class="ttc" id="agroup__init_html_gaf80d40f0aea7088ff337606e9c48f7a3"><div class="ttname"><a href="group__init.html#gaf80d40f0aea7088ff337606e9c48f7a3">GLFW_VERSION_MINOR</a></div><div class="ttdeci">#define GLFW_VERSION_MINOR</div><div class="ttdoc">The minor version number of the GLFW header.</div><div class="ttdef"><b>Definition</b> glfw3.h:294</div></div>
</div><!-- fragment --><h2><a class="anchor" id="intro_version_runtime"></a>
Run-time version</h2>
<p>The run-time version can be retrieved with <a class="el" href="group__init.html#ga9f8ffaacf3c269cc48eafbf8b9b71197">glfwGetVersion</a>, a function that may be called regardless of whether GLFW is initialized.</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> major, minor, revision;</div>
<div class="line"><a class="code hl_function" href="group__init.html#ga9f8ffaacf3c269cc48eafbf8b9b71197">glfwGetVersion</a>(&amp;major, &amp;minor, &amp;revision);</div>
<div class="line"> </div>
<div class="line">printf(<span class="stringliteral">&quot;Running against GLFW %i.%i.%i\n&quot;</span>, major, minor, revision);</div>
<div class="ttc" id="agroup__init_html_ga9f8ffaacf3c269cc48eafbf8b9b71197"><div class="ttname"><a href="group__init.html#ga9f8ffaacf3c269cc48eafbf8b9b71197">glfwGetVersion</a></div><div class="ttdeci">void glfwGetVersion(int *major, int *minor, int *rev)</div><div class="ttdoc">Retrieves the version of the GLFW library.</div></div>
</div><!-- fragment --><h2><a class="anchor" id="intro_version_string"></a>
Version string</h2>
<p>GLFW 3 also provides a compile-time generated version string that describes the version, platform, compiler and any platform-specific compile-time options. This is primarily intended for submitting bug reports, to allow developers to see which code paths are enabled in a binary.</p>
<p>The version string is returned by <a class="el" href="group__init.html#ga026abd003c8e6501981ab1662062f1c0">glfwGetVersionString</a>, a function that may be called regardless of whether GLFW is initialized.</p>
<p><b>Do not use the version string</b> to parse the GLFW library version. The <a class="el" href="group__init.html#ga9f8ffaacf3c269cc48eafbf8b9b71197">glfwGetVersion</a> function already provides the version of the running library binary.</p>
<p><b>Do not use the version string</b> to parse what platforms are supported. The <a class="el" href="group__init.html#ga8785d2b6b36632368d803e78079d38ed">glfwPlatformSupported</a> function lets you query platform support.</p>
<p><b>GLFW 3.4:</b> The format of this string was changed to support the addition of <a class="el" href="intro_guide.html#platform">runtime platform selection</a>.</p>
<p>The format of the string is as follows:</p><ul>
<li>The version of GLFW</li>
<li>For each supported platform:<ul>
<li>The name of the window system API</li>
<li>The name of the window system specific context creation API, if applicable</li>
</ul>
</li>
<li>The names of the always supported context creation APIs EGL and OSMesa</li>
<li>Any additional compile-time options, APIs and (on Windows) what compiler was used</li>
</ul>
<p>For example, compiling GLFW 3.4 with MinGW as a DLL for Windows, may result in a version string like this:</p>
<div class="fragment"><div class="line">3.4.0 Win32 WGL Null EGL OSMesa MinGW DLL</div>
</div><!-- fragment --><p>Compiling GLFW as a static library for Linux, with both Wayland and X11 enabled, may result in a version string like this:</p>
<div class="fragment"><div class="line">3.4.0 Wayland X11 GLX Null EGL OSMesa monotonic</div>
</div><!-- fragment --> </div></div><!-- contents -->
</div><!-- PageDoc -->
<address class="footer">
<p>
Last update on Fri Feb 23 2024 for GLFW 3.4.0
</p>
</address>
</body>
</html>
