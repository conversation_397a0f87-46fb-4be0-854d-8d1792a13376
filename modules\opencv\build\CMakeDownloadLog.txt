#use_cache "D:/AI/opencv/opencv-4.10.0/.cache"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_IPPICV_HASH_3rdparty_ippicv_ippicv_2021_11_0_win_intel64_20240201_general_zip"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_ADE_HASH_3rdparty_ade_v0_1_2d_zip"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_FFMPEG_HASH_3rdparty_ffmpeg_opencv_videoio_ffmpeg_dll"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_FFMPEG_HASH_3rdparty_ffmpeg_opencv_videoio_ffmpeg_64_dll"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_FFMPEG_HASH_3rdparty_ffmpeg_ffmpeg_version_cmake"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_WECHAT_QRCODE_HASH_downloads_wechat_qrcode_detect_caffemodel"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_WECHAT_QRCODE_HASH_downloads_wechat_qrcode_detect_prototxt"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_WECHAT_QRCODE_HASH_downloads_wechat_qrcode_sr_caffemodel"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_WECHAT_QRCODE_HASH_downloads_wechat_qrcode_sr_prototxt"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_XFEATURES2D_BOOSTDESC_HASH_downloads_xfeatures2d_boostdesc_bgm_i"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_XFEATURES2D_BOOSTDESC_HASH_downloads_xfeatures2d_boostdesc_bgm_bi_i"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_XFEATURES2D_BOOSTDESC_HASH_downloads_xfeatures2d_boostdesc_bgm_hd_i"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_XFEATURES2D_BOOSTDESC_HASH_downloads_xfeatures2d_boostdesc_binboost_064_i"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_XFEATURES2D_BOOSTDESC_HASH_downloads_xfeatures2d_boostdesc_binboost_128_i"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_XFEATURES2D_BOOSTDESC_HASH_downloads_xfeatures2d_boostdesc_binboost_256_i"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_XFEATURES2D_BOOSTDESC_HASH_downloads_xfeatures2d_boostdesc_lbgm_i"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_XFEATURES2D_VGG_HASH_downloads_xfeatures2d_vgg_generated_48_i"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_XFEATURES2D_VGG_HASH_downloads_xfeatures2d_vgg_generated_64_i"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_XFEATURES2D_VGG_HASH_downloads_xfeatures2d_vgg_generated_80_i"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_XFEATURES2D_VGG_HASH_downloads_xfeatures2d_vgg_generated_120_i"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_DATA_HASH_testdata_cv_face_face_landmark_model_dat"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_NVIDIA_OPTICAL_FLOW_HASH_3rdparty_NVIDIAOpticalFlowSDK_2_0_Headers_edb50da3cf849840d680249aa6dbef248ebce2ca_zip"
