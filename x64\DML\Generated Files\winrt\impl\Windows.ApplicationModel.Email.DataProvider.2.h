// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Windows_ApplicationModel_Email_DataProvider_2_H
#define WINRT_Windows_ApplicationModel_Email_DataProvider_2_H
#include "winrt/impl/Windows.ApplicationModel.Email.DataProvider.1.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Email::DataProvider
{
    struct WINRT_IMPL_EMPTY_BASES EmailDataProviderConnection : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailDataProviderConnection
    {
        EmailDataProviderConnection(std::nullptr_t) noexcept {}
        EmailDataProviderConnection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailDataProviderConnection(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailDataProviderTriggerDetails : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailDataProviderTriggerDetails
    {
        EmailDataProviderTriggerDetails(std::nullptr_t) noexcept {}
        EmailDataProviderTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailDataProviderTriggerDetails(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxCreateFolderRequest : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxCreateFolderRequest
    {
        EmailMailboxCreateFolderRequest(std::nullptr_t) noexcept {}
        EmailMailboxCreateFolderRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxCreateFolderRequest(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxCreateFolderRequestEventArgs : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxCreateFolderRequestEventArgs
    {
        EmailMailboxCreateFolderRequestEventArgs(std::nullptr_t) noexcept {}
        EmailMailboxCreateFolderRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxCreateFolderRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxDeleteFolderRequest : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxDeleteFolderRequest
    {
        EmailMailboxDeleteFolderRequest(std::nullptr_t) noexcept {}
        EmailMailboxDeleteFolderRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxDeleteFolderRequest(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxDeleteFolderRequestEventArgs : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxDeleteFolderRequestEventArgs
    {
        EmailMailboxDeleteFolderRequestEventArgs(std::nullptr_t) noexcept {}
        EmailMailboxDeleteFolderRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxDeleteFolderRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxDownloadAttachmentRequest : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxDownloadAttachmentRequest
    {
        EmailMailboxDownloadAttachmentRequest(std::nullptr_t) noexcept {}
        EmailMailboxDownloadAttachmentRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxDownloadAttachmentRequest(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxDownloadAttachmentRequestEventArgs : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxDownloadAttachmentRequestEventArgs
    {
        EmailMailboxDownloadAttachmentRequestEventArgs(std::nullptr_t) noexcept {}
        EmailMailboxDownloadAttachmentRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxDownloadAttachmentRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxDownloadMessageRequest : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxDownloadMessageRequest
    {
        EmailMailboxDownloadMessageRequest(std::nullptr_t) noexcept {}
        EmailMailboxDownloadMessageRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxDownloadMessageRequest(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxDownloadMessageRequestEventArgs : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxDownloadMessageRequestEventArgs
    {
        EmailMailboxDownloadMessageRequestEventArgs(std::nullptr_t) noexcept {}
        EmailMailboxDownloadMessageRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxDownloadMessageRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxEmptyFolderRequest : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxEmptyFolderRequest
    {
        EmailMailboxEmptyFolderRequest(std::nullptr_t) noexcept {}
        EmailMailboxEmptyFolderRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxEmptyFolderRequest(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxEmptyFolderRequestEventArgs : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxEmptyFolderRequestEventArgs
    {
        EmailMailboxEmptyFolderRequestEventArgs(std::nullptr_t) noexcept {}
        EmailMailboxEmptyFolderRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxEmptyFolderRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxForwardMeetingRequest : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxForwardMeetingRequest
    {
        EmailMailboxForwardMeetingRequest(std::nullptr_t) noexcept {}
        EmailMailboxForwardMeetingRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxForwardMeetingRequest(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxForwardMeetingRequestEventArgs : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxForwardMeetingRequestEventArgs
    {
        EmailMailboxForwardMeetingRequestEventArgs(std::nullptr_t) noexcept {}
        EmailMailboxForwardMeetingRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxForwardMeetingRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxGetAutoReplySettingsRequest : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxGetAutoReplySettingsRequest
    {
        EmailMailboxGetAutoReplySettingsRequest(std::nullptr_t) noexcept {}
        EmailMailboxGetAutoReplySettingsRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxGetAutoReplySettingsRequest(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxGetAutoReplySettingsRequestEventArgs : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxGetAutoReplySettingsRequestEventArgs
    {
        EmailMailboxGetAutoReplySettingsRequestEventArgs(std::nullptr_t) noexcept {}
        EmailMailboxGetAutoReplySettingsRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxGetAutoReplySettingsRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxMoveFolderRequest : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxMoveFolderRequest
    {
        EmailMailboxMoveFolderRequest(std::nullptr_t) noexcept {}
        EmailMailboxMoveFolderRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxMoveFolderRequest(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxMoveFolderRequestEventArgs : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxMoveFolderRequestEventArgs
    {
        EmailMailboxMoveFolderRequestEventArgs(std::nullptr_t) noexcept {}
        EmailMailboxMoveFolderRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxMoveFolderRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxProposeNewTimeForMeetingRequest : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxProposeNewTimeForMeetingRequest
    {
        EmailMailboxProposeNewTimeForMeetingRequest(std::nullptr_t) noexcept {}
        EmailMailboxProposeNewTimeForMeetingRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxProposeNewTimeForMeetingRequest(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxProposeNewTimeForMeetingRequestEventArgs : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxProposeNewTimeForMeetingRequestEventArgs
    {
        EmailMailboxProposeNewTimeForMeetingRequestEventArgs(std::nullptr_t) noexcept {}
        EmailMailboxProposeNewTimeForMeetingRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxProposeNewTimeForMeetingRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxResolveRecipientsRequest : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxResolveRecipientsRequest
    {
        EmailMailboxResolveRecipientsRequest(std::nullptr_t) noexcept {}
        EmailMailboxResolveRecipientsRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxResolveRecipientsRequest(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxResolveRecipientsRequestEventArgs : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxResolveRecipientsRequestEventArgs
    {
        EmailMailboxResolveRecipientsRequestEventArgs(std::nullptr_t) noexcept {}
        EmailMailboxResolveRecipientsRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxResolveRecipientsRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxServerSearchReadBatchRequest : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxServerSearchReadBatchRequest
    {
        EmailMailboxServerSearchReadBatchRequest(std::nullptr_t) noexcept {}
        EmailMailboxServerSearchReadBatchRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxServerSearchReadBatchRequest(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxServerSearchReadBatchRequestEventArgs : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxServerSearchReadBatchRequestEventArgs
    {
        EmailMailboxServerSearchReadBatchRequestEventArgs(std::nullptr_t) noexcept {}
        EmailMailboxServerSearchReadBatchRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxServerSearchReadBatchRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxSetAutoReplySettingsRequest : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxSetAutoReplySettingsRequest
    {
        EmailMailboxSetAutoReplySettingsRequest(std::nullptr_t) noexcept {}
        EmailMailboxSetAutoReplySettingsRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxSetAutoReplySettingsRequest(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxSetAutoReplySettingsRequestEventArgs : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxSetAutoReplySettingsRequestEventArgs
    {
        EmailMailboxSetAutoReplySettingsRequestEventArgs(std::nullptr_t) noexcept {}
        EmailMailboxSetAutoReplySettingsRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxSetAutoReplySettingsRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxSyncManagerSyncRequest : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxSyncManagerSyncRequest
    {
        EmailMailboxSyncManagerSyncRequest(std::nullptr_t) noexcept {}
        EmailMailboxSyncManagerSyncRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxSyncManagerSyncRequest(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxSyncManagerSyncRequestEventArgs : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxSyncManagerSyncRequestEventArgs
    {
        EmailMailboxSyncManagerSyncRequestEventArgs(std::nullptr_t) noexcept {}
        EmailMailboxSyncManagerSyncRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxSyncManagerSyncRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxUpdateMeetingResponseRequest : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxUpdateMeetingResponseRequest
    {
        EmailMailboxUpdateMeetingResponseRequest(std::nullptr_t) noexcept {}
        EmailMailboxUpdateMeetingResponseRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxUpdateMeetingResponseRequest(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxUpdateMeetingResponseRequestEventArgs : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxUpdateMeetingResponseRequestEventArgs
    {
        EmailMailboxUpdateMeetingResponseRequestEventArgs(std::nullptr_t) noexcept {}
        EmailMailboxUpdateMeetingResponseRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxUpdateMeetingResponseRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxValidateCertificatesRequest : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxValidateCertificatesRequest
    {
        EmailMailboxValidateCertificatesRequest(std::nullptr_t) noexcept {}
        EmailMailboxValidateCertificatesRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxValidateCertificatesRequest(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES EmailMailboxValidateCertificatesRequestEventArgs : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxValidateCertificatesRequestEventArgs
    {
        EmailMailboxValidateCertificatesRequestEventArgs(std::nullptr_t) noexcept {}
        EmailMailboxValidateCertificatesRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Email::DataProvider::IEmailMailboxValidateCertificatesRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
}
#endif
